CHECK_BOM_RFQ_QANTITIES_GEN_CATEGORY = """
    -- USAGE
    -- Call the validation function for project_id
    SELECT * FROM validate_bom_rfq_totals(project_id)
    
    ORDER BY 
        metric_type,
        ABS(difference) DESC;

        DROP FUNCTION validate_bom_rfq_totals(integer);

    CREATE OR REPLACE FUNCTION validate_bom_rfq_totals(p_project_id INTEGER)
    RETURNS TABLE (
        metric_type TEXT,
        category TEXT,
        bom_value NUMERIC,
        rfq_value NUMERIC,
        difference NUMERIC,
        difference_percentage NUMERIC
    ) AS $$
    BEGIN
        -- Validate quantities
        RETURN QUERY
        SELECT 
            'Quantity'::TEXT AS metric_type,
            COALESCE(b.general_category, r.general_category)::TEXT AS category,
            COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
            COALESCE(r.rfq_sum, 0)::NUMERIC AS rfq_value,
            (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
            CASE 
                WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
                ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
            END AS difference_percentage
        FROM
            (SELECT 
                general_category, 
                SUM(quantity)::NUMERIC AS bom_sum
            FROM 
                public.bom
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) b
        FULL OUTER JOIN
            (SELECT 
                general_category, 
                SUM(quantity)::NUMERIC AS rfq_sum
            FROM 
                public.atem_rfq
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) r
        ON b.general_category = r.general_category;

        -- Validate equivalent lengths
        RETURN QUERY
        SELECT 
            'Equivalent Length'::TEXT AS metric_type,
            COALESCE(b.general_category, r.general_category)::TEXT AS category,
            COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
            COALESCE(r.rfq_sum, 0)::NUMERIC AS rfq_value,
            (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
            CASE 
                WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
                ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
            END AS difference_percentage
        FROM
            (SELECT 
                general_category, 
                SUM(calculated_eq_length)::NUMERIC AS bom_sum
            FROM 
                public.bom
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) b
        FULL OUTER JOIN
            (SELECT 
                general_category, 
                SUM(calculated_eq_length)::NUMERIC AS rfq_sum
            FROM 
                public.atem_rfq
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) r
        ON b.general_category = r.general_category;

        -- Validate areas
        RETURN QUERY
        SELECT 
            'Area'::TEXT AS metric_type,
            COALESCE(b.general_category, r.general_category)::TEXT AS category,
            COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
            COALESCE(r.rfq_sum, 0)::NUMERIC AS rfq_value,
            (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
            CASE 
                WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
                ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
            END AS difference_percentage
        FROM
            (SELECT 
                general_category, 
                SUM(calculated_area)::NUMERIC AS bom_sum
            FROM 
                public.bom
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) b
        FULL OUTER JOIN
            (SELECT 
                general_category, 
                SUM(calculated_area)::NUMERIC AS rfq_sum
            FROM 
                public.atem_rfq
            WHERE 
                project_id = p_project_id
            GROUP BY 
                general_category) r
        ON b.general_category = r.general_category;
    END;
    $$ LANGUAGE plpgsql;
"""

CHECK_GENERAL_SUM = """
    SELECT
        SUM(length) AS total_length,
        SUM(elbows_90) AS total_elbows_90,
        SUM(elbows_45) AS total_elbows_45,
        SUM(bevels) AS total_bevels,
        SUM(tees) AS total_tees,
        SUM(reducers) AS total_reducers,
        SUM(caps) AS total_caps,
        SUM(flanges) AS total_flanges,
        SUM(valves_flanged) AS total_valves_flanged,
        SUM(valves_welded) AS total_valves_welded,
        SUM(cut_outs) AS total_cut_outs,
        SUM(supports) AS total_supports,
        SUM(bends) AS total_bends,
        SUM(union_couplings) AS total_union_couplings,
        SUM(expansion_joints) AS total_expansion_joints,
        SUM(field_welds) AS total_field_welds,
        SUM(calculated_eq_length) AS total_calculated_eq_length,
        SUM(calculated_area) AS total_calculated_area
    FROM manage_bom_to_general_aggregation(7, FALSE);
"""
