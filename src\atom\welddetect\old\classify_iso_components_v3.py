


'''
Determine Line Segments
Tasks:
- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can ensure isometric items are detected prior to running extraction
A. Classify objects
    1. Identify Isometric Sketch area
        - Filter vectors to identify possible vector lines
        Criteria: Isometrics are drawn at the angles: Horizontal [30, 210], [330, 150], Vertical: [90, 270] 
        Considerations: Handle odd angle segments that do not follow the usual angeles. Need a sample to identify the cosine area
            - Link similar conditions, i.e line width     
    
    2. Identify BOM label boxes:
        -Criteria: Small Square containing a number, series of numbers, or letter & number where a number corresponds back to a BOM Item
        - Vertical[90, 270], Actual Left Right [0, 180]
        
    3. Identify pointer lines
        - Criteria: Lines that touch a known BOM label item and intersect or almost intersect a portion of the isometric. 
        
    4. Link isometric segments, pointer lines, and BOM label
    
    5. Identify point of size change:
        - Look for a measurement using regex expressions to manually separate a size change point
        - Look for reducer symbols and pipe tees for hints

        
    Current implementation logic:
    
    
    Colors:
    Green: (0, 1, 0)
    Red: (1, 0, 0)
    Blue: (0, 0, 1)
    Yellow: (1, 1, 0)
    Cyan: (0, 1, 1)
    Magenta: (1, 0, 1)
    Orange: (1, 0.5, 0)
    Purple: (0.5, 0, 0.5)
    Pink: (1, 0.75, 0.8)
    Lime: (0.5, 1, 0)
    Teal: (0, 0.5, 0.5)
    Brown: (0.6, 0.3, 0)
    Navy: (0, 0, 0.5)
    Olive: (0.5, 0.5, 0)
    Maroon: (0.5, 0, 0)
        
'''

'''
Workflow logic:
Outstanding:
- Number all classified items internally for linking

- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can 
  ensure isometric items are detected prior to running extraction
  
- Get continuation piping lines not detected by the initital isometric detection

- Draw anything in the isometric gaps/path and label as "Pipe Component". 

- Detect Squares (Circles for some drawings)

- Get arrows and vertices

- Need pointer origin and destination stored in data for grouping

- Draw "Other" with numbers for debugging

- Use BOM item #'s for hints

- Identify measurements, size callouts, and reduced size callouts (1"x1 1/2"). Can be done with regex. Dual sizes used to create iso size breaks

- Identify measurement breaks (Perpendicular Lines signifying a specific measurment of an item, like a valve)

- Identify insulation dashes

- Identify revision triangles and clouds. Need to research this.

- Identify Weep holes (Regex pattern or key word pickout. Research alternate terms to look for)

- Handle case where the break in isometric is because the line is running behind another pipe segment and is not really a break (Page 4 of "Weld Recognition Test.pdf)


* Purpose: Isolate piping components like pipe line segments, measurement lines, pointers, etc., to assist in relating items to their actual location on the isometric drawing.
* Input: PDF file containing isometric piping drawings.
* Output: 
  1. A new PDF file with color-coded line segments.
  2. An Excel file with detailed information about each line segment.

Steps:
1. Open the PDF and extract vector graphics (drawings) from each page. (Function: process_drawings)
2. For each line in the drawings: (Function: process_drawings)
   a. Calculate its angle and length. (Functions: calculate_angle, LineString.length)
   b. Determine if it's an isometric angle. (Function: is_isometric_angle)
   c. Store its properties (coordinates, width, etc.).
3. Determine the two most significant line widths on each page. (Function: determine_significant_widths)
   - Purpose: Identify potential pipe and measurement line widths.
4. Classify lines based on width and isometric property: (Function: process_drawings)
   a. Thicker isometric lines are classified as 'Pipe'.
   b. Other lines are initially classified as 'Other'.
5. Reclassify 'Other' lines: (Function: process_drawings)
   a. Check if they're parallel to pipes. (Function: is_parallel_to_pipe)
      If yes, classify as 'Measurement'.
   b. Check if they're pointing to pipes. (Function: is_pointer_line)
      If yes, classify as 'Pointer'.
6. Generate output PDF with color-coded lines and Excel file with line data. (Functions: output_isometric_lines_to_pdf, DataFrame.to_excel)

Key Functions:
- calculate_angle: Computes the angle of a line.
- is_isometric_angle: Checks if an angle is close to standard isometric angles.
- determine_significant_widths: Finds the two most common line widths for pipes and measurements.
- is_parallel_to_pipe: Checks if a line runs parallel to a pipe.
- is_pointer_line: Identifies lines pointing to pipes (likely annotations or labels).
- process_drawings: Main function that orchestrates the entire workflow.
- output_isometric_lines_to_pdf: Generates the color-coded PDF output.
- main: Calls process_drawings and handles the overall execution flow.

Adjustable Parameters:
- angle_tolerance (in is_isometric_angle): Tolerance for considering an angle as isometric. 
  Increasing it will classify more lines as isometric, potentially including non-standard angles.
- distance_threshold (in is_pointer_line): Maximum distance for a line to be considered close to a pipe. 
  Increasing it will detect pointers that are further from pipes, but may increase false positives.
- min_length (in process_drawings): Minimum length for a line to be considered. 
  Decreasing it will include shorter lines, potentially increasing noise in the detection.
- tolerance (in determine_significant_widths): Tolerance for grouping similar line widths. 
  Increasing it will group more varied line widths together, potentially misclassifying some lines.

Color Coding in Output PDF:
- Red: Pipe lines
- Brown: Measurement lines
- Blue: Pointer lines
- Orange: Other unclassified lines



                    Colors:
                        Green: (0, 1, 0)
                        Red: (1, 0, 0)
                        Blue: (0, 0, 1)
                        Yellow: (1, 1, 0)
                        Cyan: (0, 1, 1)
                        Magenta: (1, 0, 1)
                        Orange: (1, 0.5, 0)
                        Purple: (0.5, 0, 0.5)
                        Pink: (1, 0.75, 0.8)
                        Lime: (0.5, 1, 0)
                        Teal: (0, 0.5, 0.5)
                        Brown: (0.6, 0.3, 0)
                        Navy: (0, 0, 0.5)
                        Olive: (0.5, 0.5, 0)
                        Maroon: (0.5, 0, 0)
         


Note: Adjusting these parameters may require fine-tuning based on the specific characteristics of the input drawings.
'''

import fitz, os, math, re, time, random
from fitz.utils import getColor
import pandas as pd
import numpy as np
from shapely import wkt
from shapely.geometry import Point, LineString, box, Polygon, base
from shapely.ops import nearest_points
from src.utils.logger import logger
from collections import defaultdict
from scipy.spatial import distance, cKDTree
from collections import OrderedDict
import random
import itertools as it
import math

# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)

##### NEEDS WORK 
def is_arrow_tip(vector_group):
    items = vector_group
    logger.debug(f"Checking vector group with {len(items)} items")
    
    if len(items) < 2 or any(item['item_type'] != 'l' for item in items):
        logger.debug("Not an arrow tip: doesn't have at least 2 line items")
        return False

    lines = [item['geometry'] for item in items]
    
    # Collect all endpoints
    endpoints = set()
    for line in lines:
        endpoints.update(line.coords)
    
    # Check if lines form a connected structure
    connected = False
    for i, line1 in enumerate(lines):
        for line2 in lines[i+1:]:
            if set(line1.coords) & set(line2.coords):
                connected = True
                break
        if connected:
            break
    
    if not connected:
        logger.debug("Not an arrow tip: lines are not connected")
        return False
    
    # Check if the structure forms a point (one end should have multiple lines connecting)
    endpoint_counts = defaultdict(int)
    for endpoint in endpoints:
        endpoint_counts[endpoint] = sum(endpoint in line.coords for line in lines)
    
    max_connections = max(endpoint_counts.values())
    
    logger.debug(f"Max connections at an endpoint: {max_connections}")
    
    # We consider it an arrow tip if at least one point connects multiple lines
    return max_connections >= 2

def detect_arrow_tips(page_items):
    arrow_tips = []
    vector_groups = defaultdict(list)
    measurement_pointer_lines = [item for item in page_items if item['det_cat_1'] in ['Measurement', 'Pointer']]
    logger.debug(f"Found {len(measurement_pointer_lines)} Measurement/Pointer lines")
    
    # Group items by vector_id
    for item in page_items:
        vector_groups[item['vector_id']].append(item)
    
    for vector_id, group in vector_groups.items():
        if all(item['det_cat_1'] == 'Other' for item in group):
            logger.debug(f"Checking vector group {vector_id}: {[item['geometry'] for item in group]}")
            if is_arrow_tip(group):
                logger.debug(f"Vector group {vector_id} is a potential arrow tip")
                # Check if this arrow tip is at the end of a Measurement or Pointer line
                arrow_points = set([coord for item in group for coord in item['geometry'].coords])
                for line in measurement_pointer_lines:
                    line_end = Point(line['geometry'].coords[-1])
                    for arrow_point in arrow_points:
                        distance = Point(arrow_point).distance(line_end)
                        #logger.debug(f"Distance to line end: {distance}")
                        if distance < 1:  # Adjust this threshold as needed
                            logger.debug(f"Arrow tip found in vector group {vector_id}")
                            arrow_tips.extend([i for i, item in enumerate(page_items) if item['vector_id'] == vector_id])
                            break
                    if any(page_items[i]['vector_id'] == vector_id for i in arrow_tips):
                        break
    
    logger.debug(f"Total arrow tips found: {len(arrow_tips)}")
    return arrow_tips

##### NEEDS WORK ^

def distance_to(p1, p2):
    # Gives the absolute distance between two points
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])


class RelationshipManager:
    def __init__(self):
        self.items = {}  # Store all items
        self.relationships = {}  # Store relationships between items

    def add_item(self, item_id, item_data):
        self.items[item_id] = item_data
        if item_id not in self.relationships:
            self.relationships[item_id] = set()

    def add_relationship(self, item1_id, item2_id):
        # Ensure both items exist in the relationships dictionary
        for item_id in [item1_id, item2_id]:
            if item_id not in self.relationships:
                #print(f"Warning: Item {item_id} not found. Adding it to relationships.")
                self.relationships[item_id] = set()
        
        self.relationships[item1_id].add(item2_id)
        self.relationships[item2_id].add(item1_id)

    def get_item(self, item_id):
        return self.items.get(item_id)

    def get_related_items(self, item_id):
        related_ids = self.relationships.get(item_id, set())
        return {related_id: self.items.get(related_id) for related_id in related_ids if related_id in self.items}

    def get_related_items_by_type(self, item_id, item_type):
        related_items = self.get_related_items(item_id)
        return {id: item for id, item in related_items.items() if item and item.get('det_cat_1') == item_type}
    
    def __repr__(self):
        return f"RelationshipManager(items={self.items})"


def get_finish_params(path, color=None):
    if color is None:
        c = path.get("color")
    elif isinstance(color, tuple):
        c = color  # If color is already a tuple, use it directly
    else:
        c = getColor(color)  # This will handle string color names
    
    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),
        "fill": path.get("fill", None),
        "color": c,
        "dashes": path["dashes"] if path.get("dashes") is not None else None,
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
        "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,
        "width": path["width"] if path.get("width") is not None else None
    }

def draw_path(shape, path):
    for item in path["items"]:
        if item[0] == "l":  # line
            shape.draw_line(item[1], item[2])
        elif item[0] == "re":  # rectangle
            shape.draw_rect(item[1])
        elif item[0] == "qu":  # quad
            shape.draw_quad(item[1])
        elif item[0] == "c":  # curve
            shape.draw_bezier(item[1], item[2], item[3], item[4])
        else:
            print(f"Unhandled path type: {item[0]}")

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.
    """
    x1, y1 = p1
    x2, y2 = p2
    angle = math.atan2(y2 - y1, x2 - x1)
    return angle * 180 / math.pi

def draw_pipes_from_dataframe(group, outpage):
    
    pipes_drawn = 0
    pipelines = OrderedDict()

    def calculate_offset(angle, is_start):
        offset_distance = 5  # Adjust this value to increase/decrease the offset
        if is_start:
            offset_angle = angle - 90 if angle > 0 else angle + 90
        else:
            offset_angle = angle + 90 if angle > 0 else angle - 90
        return (offset_distance * math.cos(math.radians(offset_angle)),
                offset_distance * math.sin(math.radians(offset_angle)))


    for _, item in group.iterrows():
        if item['det_cat_1'] == 'Pipe':
            shape = outpage.new_shape()
            
            start = item['start']
            end = item['end']
            # angle = int(item['angle'])
            angle = angle_between_points(start, end)
            item_number = item['item_number']
            group_number = item['group']

            # Update pipelines
            for pipe_start, pipe_points in pipelines.items():
                if start == pipe_points[-1]:
                    pipe_points.append(end)
                    break
            else:  # No connection found
                pipelines[start] = [end]

            color = (0, 0, 0)  # Black for text
            rect = fitz.Rect(start[0], start[1], end[0], end[1])
            rect2 = fitz.Rect(end[0], end[1], end[0], end[1])

            pipes_drawn += 1

            # Calculate offset positions for start and end labels
            start_offset = calculate_offset(angle, True)
            end_offset = calculate_offset(angle, False)

            # Draw start label
            start_label_pos = (start[0] + start_offset[0], start[1] + start_offset[1])
            shape.insert_text(start_label_pos, f"({item_number}, G{group_number}) {angle:.1f} deg.", 
                              fontsize=5, color=color)

            # Draw end label
            end_label_pos = (end[0] + end_offset[0], end[1] + end_offset[1])
            shape.insert_text(end_label_pos, f"({item_number}, G{group_number})", 
                              fontsize=3, color=color)

            # # Draw text labels
            # shape.insert_text(rect.tl, f"({item_number}, G{group_number}) {angle} deg.", fontsize=5, color=color)
            # shape.insert_text(rect2.tl, f"({item_number}, G{group_number})", fontsize=3, color=color)

            # Draw the pipe line
            shape.draw_line(start, end)
            shape.finish(width=item['line_width'], color=(1, 0, 0))  # Red for pipe lines

            shape.commit()

    return pipes_drawn, pipelines

def output_isometric_lines_to_pdf(df, input_filepath, output_filepath, bom_labels_df: pd.DataFrame):
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")

        # Convert page_num to integer and subtract 1
        input_page_num = int(page_num) - 1
        
        # Create a new page with the same dimensions as the input PDF
        input_page = doc[input_page_num] #doc[page_num - 1]  # Subtract 1 because page numbers in df start from 1
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
        
        pipes_drawn = 0
        bom_labels_drawn = 0

        # By start: pipe
        pipelines = OrderedDict()
       

        # Draw pipe from dataframe
        pipes_drawn, pipelines = draw_pipes_from_dataframe(group, outpage)
        
        print("===Pipes Drawn")

        # Need to this to match quad 'qu' drawings with its associated bom_label rect
        # to get the value. The qu is used for detecting those labels which are close and parallel to 
        # the pipe which have no arrow going to the pipe
        
        bom_label_rects = []
        quad_items = []
        entry_point = None # The first point of the pipe
        
        # Draw all items for this page
        items_drawn = 0
        for _, item in group.iterrows():
            try:
                shape = outpage.new_shape()
                geometry = item['geometry']
                
                
                if item['det_cat_1'] == 'Pipe':
                    pass
                    #pipes_drawn, pipelines = draw_pipes_from_dataframe(group, outpage)
                elif item['det_cat_1'] == 'Measurement':
                    color = (0.6, 0.3, 0) # Brown
                elif item['det_cat_1'] == 'Pointer':
                    color = (0, 0, 1)  # Blue
                elif item['det_cat_1'] == 'Text Callout':
                    color = (0.5, 0, 0.5)  # Purple
                elif item['det_cat_1'] == 'Weld Map':
                    color = (1, 0, 1) # Magenta
                elif item['det_cat_1'] == 'BOM Label':
                    color = (0, 1, 1) # Cyan
                elif item['det_cat_1'] == 'Size Label':
                    color = (0.5, 1, 0) # Lime
                elif item['det_cat_1'] == 'Size Break Label':
                    color = (1, 0, 0) # Red
                elif item['det_cat_1'] == 'Length Label':
                    color = (0.6, 0.3, 0)
                    
                elif item['det_cat_1'] == 'Other':
                    color = (1, 0.5, 0) # Orange
                else:
                    # continue
                    color = (1, 0.75, 0.8) # Pink
                
                
                # # New code for debugging pointers
                if item['det_cat_1'] == 'Pointer':
                    pass

                # if item['det_cat_1'] in ['Weld Map', 'BOM Label', 'Size Label', 'Size Break Label', 'Length Label']:
                if item['det_cat_1'] in ['BOM Label']:
                    # Draw bounding box for Weld Map and BOM Label
                    x0, y0, x1, y1 = item['coordinates']
                    rect = fitz.Rect(x0, y0, x1, y1)
                    shape.draw_rect(rect)
                    shape.finish(color=color, width=2)

                    bom_labels_drawn += 1
                    # if bom_labels_drawn > 1:
                    #     break
                    
                    # Add label text for BOM Label
                    if item['det_cat_1'] == 'BOM Label' and 'value' in item:
                        value = str(item['value'])
                        print("BOM label value", value)
                        shape.insert_text(rect.tl, value, fontsize=8, color=color)
                        v = (item['coordinates'], value) # store this for later
                        bom_label_rects.append(v)
                else:

                    if item['det_cat_1'] == 'Other' and item["item_type"] != "qu":
                        continue
                    # rect = fitz.Rect(start[0], start[1], end[0], end[1])
                    # orange_drawn += 1
                    # shape.insert_text(rect.tl, f"({orange_drawn})", fontsize=6, color=color)

                    if item['item_type'] == 'l':
                        if len(geometry.coords) < 2:
                            print(f"Warning: Line with less than 2 coordinates: {geometry}")
                            continue
                        start = fitz.Point(geometry.coords[0][0], geometry.coords[0][1])
                        end = fitz.Point(geometry.coords[-1][0], geometry.coords[-1][1])
                        shape.draw_line(start, end)
                    elif item['item_type'] == 're':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Rectangle with less than 4 coordinates: {coords}")
                            continue
                        rect = fitz.Rect(coords[0][0], coords[0][1], coords[2][0], coords[2][1])
                        shape.draw_rect(rect)
                    elif item['item_type'] == 'qu':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Quad with less than 4 coordinates: {coords}")
                            continue
                        # elif len(coords) > 4:
                        #     continue # big arrow candidate
                        quad_points = [fitz.Point(coords[i][0], coords[i][1]) for i in range(4)]
                        print(coords)
                        print(quad_points)

                        start = fitz.Point(quad_points[1][0], quad_points[1][1])
                        end = fitz.Point(quad_points[3][0], quad_points[3][1])
                        shape.draw_line(start, end)

                        color = (0.3, 0.8, 0.2)

                        bottom_line_angle = int(angle_between_points(end, start))
                        rect = fitz.Rect(start[0], start[1], end[0], end[1])
                        shape.insert_text(rect.tl, f"{bottom_line_angle} deg., id={len(quad_items)}", fontsize=3, color=color)

                        v = (quad_points, bottom_line_angle) # store this for matching with bom_label_rects
                        quad_items.append(v)

                        shape.draw_quad(fitz.Quad(quad_points))
                        
                    elif item['item_type'] == 'c':
                        points = [fitz.Point(x, y) for x, y in geometry.coords]
                        if len(points) < 2:
                            print(f"Warning: Curve with less than 2 points: {points}")
                            continue
                        elif len(points) == 2:
                            # If only 2 points, draw a line
                            shape.draw_line(points[0], points[1])
                        elif len(points) == 3:
                            # If 3 points, use the middle point as both control points
                            shape.draw_bezier(points[0], points[1], points[1], points[2])
                        else:
                            # If 4 or more points, use the first and last as endpoints and the middle two as control points
                            shape.draw_bezier(points[0], points[1], points[-2], points[-1])
                    else:
                        print(f"Unhandled item type: {item['item_type']}")
                        continue

                    finish_params = get_finish_params(item['drawing'])
                    finish_params['color'] = color
                    shape.finish(**finish_params)

                # Draw item number and rectangle if available
                if 'item_number' in item and pd.notna(item['item_number']):
                    if 'item_coordinates' in item and pd.notna(item['item_coordinates']):
                        item_coords = eval(item['item_coordinates'])
                        rect = fitz.Rect(item_coords[0], item_coords[1], item_coords[2], item_coords[3])
                        shape.draw_rect(rect)
                        shape.insert_text(rect.tl, str(item['item_number']), fontsize=8, color=(0, 0, 0))
                
                shape.commit()
                    
                items_drawn += 1
            except Exception as e:
                print(f"Error processing item: {item}")
                print(f"Error message: {str(e)}")

        # Draw the initial pipe groupings, giving each pipe a random color
        print("")
        print("Drawing pipeline lines with random color...")
        for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
            pipe_coords = [pipe_start] + pipeline
            shape = outpage.new_shape()
            for start, end in zip(pipe_coords, pipe_coords[1:]):
                print(f"pipe_{n} drawing line shape from", start, "to", end)
                shape.draw_line(start, end)

            rect = fitz.Rect(pipe_start[0], pipe_start[1], pipe_coords[0][0], pipe_coords[0][1])
            shape.insert_text(rect.tl, f"pipe_{n}", fontsize=8, color=(0, 0, 0))

            color = (random.uniform(0, 1), random.uniform(0, 1), random.uniform(0, 1))
            finish_params = {'even_odd': True, 
                    'stroke_opacity': 1.0, 'fill_opacity': 0, 'fill': None, 'color': color, 'dashes': '[] 0', 
                    'closePath': False, 'lineJoin': 1.0, 'lineCap': 0, 'width': 1}

            shape.finish(**finish_params)
            shape.commit()


        pipe_angles = {}
        # Calculate starting and ending angles of pipe
        # Starting angle is the two first coords of pipe
        # Ending angle is the last two coords of pipe
        for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
            pipe_coords = [pipe_start] + pipeline
            pipe_end = pipe_coords[-1]
            pipe_start_angle = int(angle_between_points(pipe_start, pipe_coords[1]))
            pipe_end_angle = int(angle_between_points(pipe_coords[-2], pipe_end))
            pipe_angles[n] = {"start_angle": pipe_start_angle, "end_angle": pipe_end_angle}

        print(pipe_angles)

        connected_angle_tolerance = 2
        connected_distance_threshold = 40 # 15-25 were typical distance values that are close   

        pipe_connections = OrderedDict() # e.g. {n: { before: n1, after: n2 }}
        print()
        print()
        print("Finding connection between pipes")
        print()
        for n1, (pipe_start1, pipeline1) in enumerate(pipelines.items()):
            pipe_connections.setdefault(n1, {})  # Insert entry early as this keeps dict ordered
            pipe1_coords = [pipe_start1] + pipeline1
            pipe_end1 = pipe1_coords[-1]
            pipe_start1_angle = pipe_angles[n1]["start_angle"]
            pipe_end1_angle = pipe_angles[n1]["end_angle"]
            print(f"pipe_{n1} -", "start angle:", pipe_start1_angle, ", end angle:", pipe_end1_angle)

            # before_pipe = None
            # after_pipe = None
            for n2, (pipe_start2, pipeline2) in enumerate(pipelines.items()):
                if n1 == n2:
                    continue # A pipe cannot connect to itself

                pipe2_coords = [pipe_start2] + pipeline2
                pipe_end2 = pipe2_coords[-1]
                # pipe_start2_angle = pipe_angles[n2]["start_angle"]
                pipe_end2_angle = pipe_angles[n2]["end_angle"]

                # Check if pipe starting point is connected to another pipe starting point
                # If same angle and within distance, we could consider them connect end-to-end
                if abs(pipe_start1_angle - pipe_end2_angle) < connected_angle_tolerance: # Allow some small angle tolerance i.e. 2
                    dist = distance_to(pipe_start1, pipe_end2)
                    if dist < connected_distance_threshold:
                        print(f"pipe_{n1} start to pipe_{n2} end", ", angle:", pipe_start1_angle, ", distance between:", dist)
                        pipe_connections[n1]["before"] = n2
                        pipe_connections.setdefault(n2, {})
                        pipe_connections[n2]["after"] = n1

            # TODO if not behind pipe, check junctions, non face-to-face connections
            pass

    print()
    print("Printing Pipe connection results")
    print()
    
    for n, v in pipe_connections.items():
        print(f"pipe_{n}", v)


    parallel_angle_tolerance = 2
    parallel_distance_tolerance = 6


    quad_id_to_pipe_no = OrderedDict()

    candidate_bom_labels = []
    # Now look at the BOM Labels. 
    # Filter out the ones we need. Consider them candidates
    # First see the close ones with no arrows
    for qu_no, qu in enumerate(quad_items):
        quad_points, bottom_line_angle = qu
        quad_bottom_end = fitz.Point(quad_points[3][0], quad_points[3][1])
        # Unless bom_labels  are weirdly positioned (which I haven't noticed yet), the bottom line
        # angle is usually in the middle of a pipe segment, so should match a start or end angle
        for pipe_no, angles in pipe_angles.items():
            start_angle = angles["start_angle"]
            end_angle = angles["end_angle"]
            if (not abs(bottom_line_angle - start_angle) < parallel_angle_tolerance
                    and not abs(bottom_line_angle - end_angle) < parallel_angle_tolerance):
                continue # Neither pipe start or end angle is similar/parallel to bottom of quad

            # Check pipe n to see if any coords are very close to bottom line
            pipe_start = list(pipelines.keys())[pipe_no] # the first coord of pipe is also a dict key
            pipe_coords = [pipe_start] + pipelines[pipe_start]

            # A bit ugly this, iterate over every single coord of pipe
            # as soon as one is close exit
            found = False
            for start, end in zip(pipe_coords, pipe_coords[1:]):
                start = (int(start[0]), int(start[1]))
                end = (int(end[0]), int(end[1]))
                x1 = min(start[0], end[0])
                x2 = max(start[0], end[0])
                y1 = min(start[1], end[1])
                y2 = max(start[1], end[1])
                if x1 == x2: # Ensure range works
                    x2 += 1
                if y1 == y2: # Ensure range works
                    y2 += 1
                if qu_no == 2 and x1 == 1963 and y2 == 528:
                    pass
                for x in range(x1, x2):
                    if found: break
                    for y in range(y1, y2):
                        pipe_point = (x, y)
                        dist = distance_to(pipe_point, quad_bottom_end)
                        if dist < parallel_distance_tolerance:
                            print(f"quad_id={qu_no}", f"linked to pipe_{pipe_no}", f"distance_to_pipe: {int(dist)}")
                            found = True
                            quad_id_to_pipe_no[qu_no] = pipe_no
                            break
                
                if found: 
                    break
                # print(f"pipe_{n}", start, end)

        # for bom_label_rect in bom_label_rects:

    print("quad id to pipe no results: ", quad_id_to_pipe_no)

    
    ## Charlie TODO 1

    # Looking at the bom_labels, it looks like those which have arrow have a bottom_line_angle=80
    # We can filter this out and look for arrow points which point to the pipeline,
    # A possible solution is to look for the start and end point of an arrow

    # if the start of arrow is close to bom_label, and end arrow is close to pipeline, we could say
    # it is a valid bom_label

    # May need distance thresholds for arrows

    # Iterate again over quads but look for 
    for qu_no, qu in enumerate(quad_items):
        quad_points, bottom_line_angle = qu
        if bottom_line_angle != 80:
            continue
        # TODO - add logic here to find valid arrows which are close to qu and which points to the pipeline 


    ## Charlie TODO 2

    # Match quad id and its quad points with the bom_label_items
    # Maybe consider the central point of quad with central point of bom_label_item rect
    # and use a distance threshold.
    # 
    # If within distance, we can say this quad belongs to bom_label_item, then we can assign 
    # it the bom_label_value. As we should have assigned quads to pipe, we can start assigning
    # pipes with the size
    #   

    ## Charlie TODO 3

    # A harder part, is then to figure out how assign neighboring pipes by looking at the before
    # and after in `pipe_connections`. 





    print(pipe_connections)

    print("Pipes:", pipes_drawn)
    for n, p in enumerate(pipelines):
        print(f"pipe_{n} coords. First coord =", p, "Rest of Pipe coords =", pipelines[p])
        print()


    print()
    print()
    print(f"Saving PDF to {output_filepath}")
    outpdf.save(output_filepath)
    print("PDF saved. Closing files...")
    outpdf.close()
    doc.close()
    print(f"PDF output process completed. Total items drawn: {items_drawn}")


def is_within_area(bounds, area):
    """Check if an item's bounding box is within the specified area."""
    x_min, y_min, x_max, y_max = bounds
    return (area['x0'] <= x_min <= area['x1'] and
            area['y0'] <= y_min <= area['y1'] and
            area['x0'] <= x_max <= area['x1'] and
            area['y0'] <= y_max <= area['y1'])

def calculate_angle(line):
    start, end = line.coords
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]

    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)

def is_pointer_line(item, items_to_check, angle_tolerance=20, distance_threshold=10, debug_func=False):
    '''
    Identifies "pointers", which point from descriptors/labels to items on the pipe or measurement lines.
    '''
    if item['item_type'] != 'l':
        return False

    line_geom = item['geometry']
    line_angle = item['angle']
    start_point = Point(line_geom.coords[0])
    end_point = Point(line_geom.coords[-1])
    
    for target_item in items_to_check:
        if target_item['item_type'] != 'l':
            continue

        if 'det_cat_1' in target_item and target_item['det_cat_1'] not in ['Pipe', 'Potential_Pipe', 'Measurement']:
            continue

        target_geom = target_item['geometry']
        target_angle = target_item['angle']
        target_start = Point(target_geom.coords[0])
        target_end = Point(target_geom.coords[-1])
        
        # Check if one end of the line is close to the target item
        start_to_target_start = start_point.distance(target_start)
        start_to_target_end = start_point.distance(target_end)
        end_to_target_start = end_point.distance(target_start)
        end_to_target_end = end_point.distance(target_end)
        
        if (start_to_target_start <= distance_threshold or
            start_to_target_end <= distance_threshold or
            end_to_target_start <= distance_threshold or
            end_to_target_end <= distance_threshold):
            
            if debug_func:
                print(f"Line close to {target_item.get('det_cat_1', 'pipe')}. Distances: {start_to_target_start}, {start_to_target_end}, {end_to_target_start}, {end_to_target_end}")
            
            # Check if the line angle is significantly different from the target angle
            angle_diff = min(abs(line_angle - target_angle), 360 - abs(line_angle - target_angle))
            if debug_func:
                print(f"Line angle: {line_angle}, Target angle: {target_angle}, Angle difference: {angle_diff}")
            
            if angle_diff > angle_tolerance:
                if debug_func:
                    print(f"Pointer line detected pointing to {target_item.get('det_cat_1', 'pipe')}!")
                return True
            else:
                if debug_func:
                    print(f"Line close to {target_item.get('det_cat_1', 'pipe')} but angle difference too small.")
    
    return False

def is_parallel_to_pipe(item, pipe_items, angle_tolerance=5, distance_threshold=100, min_parallel_ratio=0.5, min_length=1e-6, debug_func=False):
    if item['item_type'] != 'l':
        return False, []

    line_geom = item['geometry']
    line_angle = item.get('angle')
    
    # Check for very short lines
    if line_geom.length < min_length:
        if debug_func:
            print(f"Line is too short: length = {line_geom.length}")
        return False, []

    if line_angle is None:
        if debug_func:
            print("Line angle is None")
        return False, []

    parallel_pipes = []

    for pipe_item in pipe_items:
        if pipe_item['item_type'] != 'l':
            continue

        pipe_geom = pipe_item['geometry']
        pipe_angle = pipe_item.get('angle')

        if pipe_angle is None:
            continue

        try:
            # Check if angles are parallel (considering the isometric nature)
            angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
            if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
                # Check if the line runs alongside the pipe
                pipe_buffer = pipe_geom.buffer(distance_threshold)
                parallel_part = line_geom.intersection(pipe_buffer)
                
                if line_geom.length > 0:
                    parallel_ratio = parallel_part.length / line_geom.length
                    if debug_func:
                        print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Parallel ratio: {parallel_ratio}")
                    if parallel_ratio >= min_parallel_ratio:
                        pipe_id = create_id(pipe_item['page'], pipe_item['vector_id'], pipe_item['item_position'])
                        parallel_pipes.append(pipe_id)
                else:
                    if debug_func:
                        print(f"Line has zero length: {line_geom}")
        except Exception as e:
            if debug_func:
                print(f"Error in parallel check: {str(e)}")
    
    return len(parallel_pipes) > 0, parallel_pipes

def determine_significant_widths(df, tolerance=0.01, min_length=10):
    '''
    Considers only lines ('l' type) determined to be isometric angles to find the line_widths of the largest lengths. 
    Narrows down to the top 2 sizes. The larger size is assumed as the isometric pipe.
    '''
    valid_widths = df[(df['line_width'].notna()) & 
                      (df['line_width'] > 0) & 
                      (df['is_isometric'] == True) & 
                      (df['item_type'] == 'l') &
                      (df['length'] >= min_length)]
    
    if valid_widths.empty:
        print("Warning: No valid widths found for determining significant widths.")
        return None, None

    width_groups = valid_widths.groupby('line_width')['length'].sum().sort_values(ascending=False)
    
    significant_widths = []
    for width, total_length in width_groups.items():
        if not significant_widths or abs(width - significant_widths[0]) > tolerance:
            significant_widths.append(width)
            if len(significant_widths) == 2:
                break
    
    if len(significant_widths) < 2:
        print(f"Warning: Only found {len(significant_widths)} significant width(s).")
        return tuple(significant_widths + [None] * (2 - len(significant_widths)))
    
    return tuple(significant_widths)


def identify_pipe_continuations(page_lines, pipe_width, tolerance=0.01):
    pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
    potential_continuations = [line for line in page_lines if line['det_cat_1'] == 'Other' and abs(line['line_width'] - pipe_width) < tolerance]
    
    def lines_are_collinear(line1, line2):
        # Check if two lines are collinear (on the same path)
        angle1 = line1['angle']
        angle2 = line2['angle']
        return abs(angle1 - angle2) < 5 or abs(abs(angle1 - angle2) - 180) < 5

    def is_potential_elbow(line, pipe1, pipe2):
        # Check if a line could be an elbow connecting two pipe segments
        return (
            (line['start_x'] == pipe1['end_x'] and line['start_y'] == pipe1['end_y'] and
             line['end_x'] == pipe2['start_x'] and line['end_y'] == pipe2['start_y']) or
            (line['start_x'] == pipe2['end_x'] and line['start_y'] == pipe2['end_y'] and
             line['end_x'] == pipe1['start_x'] and line['end_y'] == pipe1['start_y'])
        )

    for potential in potential_continuations:
        # Check if the potential continuation is collinear with any existing pipe segment
        if any(lines_are_collinear(potential, pipe) for pipe in pipe_lines):
            potential['det_cat_1'] = 'Pipe'
            potential['det_cat_2'] = 'Continuation'
            continue

        # Check if the potential continuation could be an elbow
        for i, pipe1 in enumerate(pipe_lines):
            for pipe2 in pipe_lines[i+1:]:
                if is_potential_elbow(potential, pipe1, pipe2):
                    potential['det_cat_1'] = 'Pipe'
                    potential['det_cat_2'] = 'Elbow'
                    break
            if potential['det_cat_1'] == 'Pipe':
                break

    return page_lines

def is_connected(item1, item2, tolerance=5):
    """Check if two items are connected within a small tolerance."""
    geom1 = item1['geometry']
    geom2 = item2['geometry']
    
    # For curves, check if any point is close to the other geometry
    if item1['item_type'] == 'c':
        return any(Point(p).distance(geom2) <= tolerance for p in geom1.coords)
    elif item2['item_type'] == 'c':
        return any(Point(p).distance(geom1) <= tolerance for p in geom2.coords)
    
    return geom1.distance(geom2) <= tolerance

# def classify_pipes_og(page_items, pipe_width, measurement_width, tolerance=0.01, item_numbers=None):
#     if item_numbers is None:
#         item_numbers = {'Pipe': 0, 'Potential_Pipe': 0, 'Other': 0}
        
#     pipe_items = []
#     pipelines = OrderedDict()
    
#     # First pass: Classify pipes without grouping
#     for item in page_items:
#         # Check if line_width is None or if pipe_width or measurement_width are None
#         if item['line_width'] is None or pipe_width is None or measurement_width is None:
#             item['det_cat_1'] = 'Other'
#             item['item_number'] = item_numbers['Other']
#             item_numbers['Other'] += 1
#             continue

#         try:
#             if abs(item['line_width'] - max(pipe_width, measurement_width)) < tolerance:
#                 if item['item_type'] == 'l' and item['is_isometric']:
#                     item['det_cat_1'] = 'Pipe'
#                     item['item_number'] = item_numbers['Pipe']
#                     item_numbers['Pipe'] += 1
                    
#                     # Add coordinate information
#                     coords = list(item['geometry'].coords)
#                     item['start'] = coords[0]
#                     item['end'] = coords[-1]
#                     item['x0'], item['y0'] = coords[0]
#                     item['x1'], item['y1'] = coords[-1]
                    
#                     #pipe_items.append(item)
                    
#                 else:
#                     item['det_cat_1'] = 'Potential_Pipe'
#                     item['item_number'] = item_numbers['Potential_Pipe']
#                     item_numbers['Potential_Pipe'] += 1

#                 pipe_items.append(item)
#             else:
#                 item['det_cat_1'] = 'Other'
#                 item['item_number'] = item_numbers['Other']
#                 item_numbers['Other'] += 1

#         except TypeError as e:
#             print(f"Error processing item: {item}")
#             print(f"Error message: {str(e)}")
#             item['det_cat_1'] = 'Other'
#             item['item_number'] = item_numbers['Other']
#             item_numbers['Other'] += 1

#     # Second pass: Connect potential pipe segments
#     for item in page_items:
#         if item['det_cat_1'] == 'Potential_Pipe':
#             connected_pipes = [pipe for pipe in pipe_items if pipe['det_cat_1'] == 'Pipe' and is_connected(item, pipe)]
#             if connected_pipes:
#                 item['det_cat_1'] = 'Pipe'
#                 item['item_number'] = item_numbers['Pipe']
#                 item_numbers['Pipe'] += 1
                
#                 # Add coordinate information for upgraded pipes
#                 coords = list(item['geometry'].coords)
#                 item['start'] = coords[0]
#                 item['end'] = coords[-1]
#                 item['x0'], item['y0'] = coords[0]
#                 item['x1'], item['y1'] = coords[-1]
                
#                 # item['group'] = connected_pipes[0]['group']
#                 pipe_items.append(item)

#     # Third pass: Group pipes into pipelines
#     for item in pipe_items:
#         start, end = item['start'], item['end']
#         added = False
#         for pipe_start, pipeline in pipelines.items():
#             if start == pipeline[-1]:
#                 pipeline.append(end)
#                 added = True
#                 break
#             elif end == pipe_start:
#                 pipelines[start] = [start] + pipeline
#                 del pipelines[pipe_start]
#                 added = True
#                 break
#             elif start == pipe_start:
#                 pipelines[end] = [end] + pipeline[::-1]
#                 del pipelines[pipe_start]
#                 added = True
#                 break
#             elif end == pipeline[-1]:
#                 pipeline.extend([start, end])
#                 added = True
#                 break
#         if not added:
#             pipelines[start] = [start, end]

#     # Merge any overlapping pipelines
#     merged = True
#     while merged:
#         merged = False
#         keys = list(pipelines.keys())
#         for i, key1 in enumerate(keys):
#             if key1 not in pipelines:
#                 continue
#             for key2 in keys[i+1:]:
#                 if key2 not in pipelines:
#                     continue
#                 if pipelines[key1][-1] == pipelines[key2][0]:
#                     pipelines[key1].extend(pipelines[key2][1:])
#                     del pipelines[key2]
#                     merged = True
#                     break
#                 elif pipelines[key1][0] == pipelines[key2][-1]:
#                     pipelines[key2].extend(pipelines[key1][1:])
#                     del pipelines[key1]
#                     merged = True
#                     break
#             if merged:
#                 break

#     # Fourth pass: Calculate angles for pipelines
#     pipe_angles = {}
#     for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
#         pipe_coords = [pipe_start] + pipeline
#         pipe_end = pipe_coords[-1]
#         pipe_start_angle = int(angle_between_points(pipe_start, pipe_coords[1]))
#         pipe_end_angle = int(angle_between_points(pipe_coords[-2], pipe_end))
#         pipe_angles[n] = {"start_angle": pipe_start_angle, "end_angle": pipe_end_angle}

#     # Fifth pass: Find connections between pipes
#     connected_angle_tolerance = 2
#     connected_distance_threshold = 40
#     pipe_connections = OrderedDict()

#     for n1, (pipe_start1, pipeline1) in enumerate(pipelines.items()):
#         pipe_connections.setdefault(n1, {})
#         pipe1_coords = [pipe_start1] + pipeline1
#         pipe_end1 = pipe1_coords[-1]
#         pipe_start1_angle = pipe_angles[n1]["start_angle"]
#         pipe_end1_angle = pipe_angles[n1]["end_angle"]

#         for n2, (pipe_start2, pipeline2) in enumerate(pipelines.items()):
#             if n1 == n2:
#                 continue  # A pipe cannot connect to itself

#             pipe2_coords = [pipe_start2] + pipeline2
#             pipe_end2 = pipe2_coords[-1]
#             pipe_end2_angle = pipe_angles[n2]["end_angle"]

#             if abs(pipe_start1_angle - pipe_end2_angle) < connected_angle_tolerance:
#                 dist = distance_to(pipe_start1, pipe_end2)
#                 if dist < connected_distance_threshold:
#                     pipe_connections[n1]["before"] = n2
#                     pipe_connections.setdefault(n2, {})
#                     pipe_connections[n2]["after"] = n1

#     # Assign group numbers to pipe_items based on pipelines
#     for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
#         pipe_coords = [pipe_start] + pipeline
#         for item in pipe_items:
#             if item['start'] in pipe_coords and item['end'] in pipe_coords:
#                 item['group'] = n

#     return pipe_items, item_numbers, len(pipelines), pipelines, pipe_angles, pipe_connections
            

def classify_pipes(page_items, pipe_width, measurement_width, tolerance=0.01, item_numbers=None):
    if item_numbers is None:
        item_numbers = {'Pipe': 0, 'Potential_Pipe': 0, 'Other': 0}
    
    pipe_items = []
    pipelines = OrderedDict()
    
    # First pass: Classify pipes without grouping
    for item in page_items:
        if item['line_width'] is None or pipe_width is None or measurement_width is None:
            item['det_cat_1'] = 'Other'
            item['item_number'] = item_numbers['Other']
            item_numbers['Other'] += 1
            continue

        try:
            if abs(item['line_width'] - max(pipe_width, measurement_width)) < tolerance:
                if item['item_type'] == 'l' and item['is_isometric']:
                    item['det_cat_1'] = 'Pipe'
                    item['item_number'] = item_numbers['Pipe']
                    item_numbers['Pipe'] += 1
                    
                    # Add coordinate information
                    coords = list(item['geometry'].coords)
                    item['start'] = coords[0]
                    item['end'] = coords[-1]
                    item['x0'], item['y0'] = coords[0]
                    item['x1'], item['y1'] = coords[-1]
                    
                    pipe_items.append(item)
                else:
                    item['det_cat_1'] = 'Potential_Pipe'
                    item['item_number'] = item_numbers['Potential_Pipe']
                    item_numbers['Potential_Pipe'] += 1
            else:
                item['det_cat_1'] = 'Other'
                item['item_number'] = item_numbers['Other']
                item_numbers['Other'] += 1

        except TypeError as e:
            print(f"Error processing item: {item}")
            print(f"Error message: {str(e)}")
            item['det_cat_1'] = 'Other'
            item['item_number'] = item_numbers['Other']
            item_numbers['Other'] += 1
            
    # Second pass: Connect potential pipe segments
    for item in page_items:
        if item['det_cat_1'] == 'Potential_Pipe':
            connected_pipes = [pipe for pipe in pipe_items if pipe['det_cat_1'] == 'Pipe' and is_connected(item, pipe)]
            if connected_pipes:
                item['det_cat_1'] = 'Pipe'
                item['item_number'] = item_numbers['Pipe']
                item_numbers['Pipe'] += 1
                
                # Add coordinate information for upgraded pipes
                coords = list(item['geometry'].coords)
                item['start'] = coords[0]
                item['end'] = coords[-1]
                item['x0'], item['y0'] = coords[0]
                item['x1'], item['y1'] = coords[-1]
                
                # item['group'] = connected_pipes[0]['group']
                pipe_items.append(item)

    # Second pass: Group pipes into pipelines (replicating the drawing code logic)
    for item in pipe_items:
        start = item['start']
        end = item['end']
        
        for pipe_start, pipe_points in pipelines.items():
            if start == pipe_points[-1]:
                pipe_points.append(end)
                break
        else:  # No connection found
            pipelines[start] = [end]

    # Calculate angles for pipelines
    pipe_angles = {}
    for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
        pipe_coords = [pipe_start] + pipeline
        pipe_end = pipe_coords[-1]
        pipe_start_angle = int(angle_between_points(pipe_start, pipe_coords[1]))
        pipe_end_angle = int(angle_between_points(pipe_coords[-2], pipe_end))
        pipe_angles[n] = {"start_angle": pipe_start_angle, "end_angle": pipe_end_angle}

    # Find connections between pipes
    connected_angle_tolerance = 2
    connected_distance_threshold = 40
    pipe_connections = OrderedDict()

    for n1, (pipe_start1, pipeline1) in enumerate(pipelines.items()):
        pipe_connections.setdefault(n1, {})
        pipe1_coords = [pipe_start1] + pipeline1
        pipe_end1 = pipe1_coords[-1]
        pipe_start1_angle = pipe_angles[n1]["start_angle"]
        pipe_end1_angle = pipe_angles[n1]["end_angle"]

        for n2, (pipe_start2, pipeline2) in enumerate(pipelines.items()):
            if n1 == n2:
                continue  # A pipe cannot connect to itself

            pipe2_coords = [pipe_start2] + pipeline2
            pipe_end2 = pipe2_coords[-1]
            pipe_end2_angle = pipe_angles[n2]["end_angle"]

            if abs(pipe_start1_angle - pipe_end2_angle) < connected_angle_tolerance:
                dist = distance_to(pipe_start1, pipe_end2)
                if dist < connected_distance_threshold:
                    pipe_connections[n1]["before"] = n2
                    pipe_connections.setdefault(n2, {})
                    pipe_connections[n2]["after"] = n1

    # Assign group numbers to pipe_items based on pipelines
    for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
        pipe_coords = [pipe_start] + pipeline
        for item in pipe_items:
            if item['start'] in pipe_coords and item['end'] in pipe_coords:
                item['group'] = n

    return pipe_items, item_numbers, len(pipelines), pipelines, pipe_angles, pipe_connections
 



# ----> Classsify rectangles
def detect_rectangles(page_items, tolerance=5, rectangularity_threshold=0.98):
    def are_connected(line1, line2):
        return any(distance.euclidean(p1, p2) < tolerance 
                   for p1 in [line1.coords[0], line1.coords[-1]] 
                   for p2 in [line2.coords[0], line2.coords[-1]])

    def is_rectangle(poly):
        if poly.is_valid and poly.area > 0:
            rectangularity = poly.area / poly.minimum_rotated_rectangle.area
            return rectangularity > rectangularity_threshold
        return False

    potential_items = [item for item in page_items if item['det_cat_1'] != 'Pipe' and item['item_type'] == 'l']
    
    rectangles = []
    processed = set()

    for i, item in enumerate(potential_items):
        if i in processed:
            continue
        
        connected_lines = [item['geometry']]
        connected_items = [item]
        processed.add(i)

        # Find connected lines
        for j, other_item in enumerate(potential_items):
            if j in processed:
                continue
            if are_connected(item['geometry'], other_item['geometry']):
                connected_lines.append(other_item['geometry'])
                connected_items.append(other_item)
                processed.add(j)

        # Try to form a polygon from the connected lines
        if len(connected_lines) >= 3:
            points = []
            for line in connected_lines:
                points.extend(line.coords)
            unique_points = list(set(points))
            
            if len(unique_points) >= 4:
                try:
                    poly = Polygon(unique_points)
                    if is_rectangle(poly):
                        rectangles.append(connected_items)
                except ValueError:
                    # If we can't form a valid polygon, skip this set of lines
                    pass

    return rectangles

# ----> Classsify rectangles


def is_pointer_line_vectorized(items, angle_tolerance=0, distance_threshold=40):
    coords, angles, categories, geometries = preprocess_items(items)
    
    if len(coords) == 0:
        return []  # Return empty list if no valid items

    # Create KD-Tree for efficient nearest neighbor search
    tree = cKDTree(coords)
    
    pointers = []
    for i, item in enumerate(items):
        if item['item_type'] != 'l':
            continue
        if 'det_cat_1' in item and item['det_cat_1'] not in [None, 'Other']:
            continue
        
        line_geom = item['geometry']
        line_angle = item['angle']
        start_point = Point(line_geom.coords[0])
        end_point = Point(line_geom.coords[-1])
        
        # Find nearby points for both start and end of the line
        start_indices = tree.query_ball_point([start_point.x, start_point.y], distance_threshold)
        end_indices = tree.query_ball_point([end_point.x, end_point.y], distance_threshold)
        nearby_indices = list(set(start_indices + end_indices))
        
        # Filter out invalid indices and non-target categories
        valid_indices = [idx for idx in nearby_indices if idx < len(categories) and 
                         categories[idx] in ['Pipe', 'Potential_Pipe', 'Measurement']]
        
        if not valid_indices:
            continue
        
        nearby_categories = categories[valid_indices]
        nearby_angles = angles[valid_indices]
        nearby_geometries = [geometries[idx] for idx in valid_indices]
        
        for idx, (target_cat, target_angle, target_geom) in enumerate(zip(nearby_categories, nearby_angles, nearby_geometries)):
            # Check if one end of the line is close to the target item
            target_start, target_end = target_geom.coords[0], target_geom.coords[-1]
            start_to_target_start = start_point.distance(Point(target_start))
            start_to_target_end = start_point.distance(Point(target_end))
            end_to_target_start = end_point.distance(Point(target_start))
            end_to_target_end = end_point.distance(Point(target_end))
            
            if (start_to_target_start <= distance_threshold or
                start_to_target_end <= distance_threshold or
                end_to_target_start <= distance_threshold or
                end_to_target_end <= distance_threshold):
                
                # Check if the line angle is significantly different from the target angle
                angle_diff = min(abs(line_angle - target_angle), 360 - abs(line_angle - target_angle))
                
                if angle_diff > angle_tolerance:
                    # Determine which end of the pointer is closest to the target
                    distances = [start_to_target_start, start_to_target_end, end_to_target_start, end_to_target_end]
                    min_distance_index = distances.index(min(distances))
                    
                    if min_distance_index in [0, 1]:
                        pointer_end = start_point
                    else:
                        pointer_end = end_point
                    
                    if min_distance_index in [0, 2]:
                        target_point = Point(target_start)
                    else:
                        target_point = Point(target_end)
                    
                    pointer_info = {
                        'pointer_index': i,
                        'target_index': valid_indices[idx],
                        'points_to': target_cat,
                        'pointer_start': start_point,
                        'pointer_end': end_point,
                        'target_point': target_point
                    }
                    pointers.append(pointer_info)
                    
                    print(f"Detected pointer: {create_id(items[i]['page'], items[i]['vector_id'], items[i]['item_position'])}")
                    print(f"  Points to: {target_cat}")
                    print(f"  Target ID: {create_id(items[valid_indices[idx]]['page'], items[valid_indices[idx]]['vector_id'], items[valid_indices[idx]]['item_position'])}")
                    print(f"  Target det_cat_1: {items[valid_indices[idx]]['det_cat_1']}")
                    
                    break  # We found a valid pointer, no need to check other nearby items
    
    return pointers

def preprocess_items(items):
    coords = []
    angles = []
    categories = []
    geometries = []
    for item in items:
        if item['item_type'] == 'l' and item['det_cat_1'] in ['Pipe', 'Potential_Pipe', 'Measurement', 'Other']:
            geom = item['geometry']
            coords.append(geom.coords[0])  # Only use start point for KD-Tree
            angles.append(item['angle'])
            categories.append(item.get('det_cat_1', None))  # Use None if 'det_cat_1' doesn't exist
            geometries.append(geom)
    return np.array(coords), np.array(angles), np.array(categories), geometries

def process_drawings(filepath, rm, pages_to_process=None, isometric_area_coords=None):
    doc = fitz.open(filepath)
    
    if pages_to_process is None:
        pages_to_process = range(len(doc))
    else:
        pages_to_process = [p - 1 for p in pages_to_process if 0 < p <= len(doc)]

    all_items = []
    total_items = 0

    classified_items = {'Pipe': 0, 'Potential_Pipe': 0, 'Measurement': 0, 'Pointer': 0, 'Other': 0, 'Arrow Tip': 0}

    # Dictionary to keep track of item numbers for each category
    item_numbers = {category: 0 for category in classified_items.keys()} # Sequentially number objects (starts at 0 for each category)
    total_groups = 0 # Keep track of grouped Pipe Segments

    all_pipelines = OrderedDict()
    all_pipe_angles = {}
    all_pipe_connections = OrderedDict()

    for page_num in pages_to_process:
        page = doc[page_num]
        drawings = page.get_drawings()
        print(f"Page {page_num + 1}: Found {len(drawings)} drawings")

        page_items = []
        for vector_id, drawing in enumerate(drawings):
            for item_position, item in enumerate(drawing['items']):
                total_items += 1
                
                item_type = item[0]
                if item_type == 'l':  # line
                    start, end = item[1], item[2]
                    geometry = LineString([(start.x, start.y), (end.x, end.y)])
                    angle = calculate_angle(geometry)
                    length = geometry.length
                    is_iso = is_isometric_angle(angle)
                elif item_type == 're':  # rectangle
                    x0, y0, x1, y1 = item[1]
                    geometry = Polygon([(x0, y0), (x1, y0), (x1, y1), (x0, y1)])
                elif item_type == 'qu':  # quad
                    points = item[1]
                    geometry = Polygon([(p.x, p.y) for p in points])
                elif item_type == 'c':  # curve
                    # For curves, we'll use a LineString approximation
                    points = item[1:]  # The control points of the curve
                    geometry = LineString([(p.x, p.y) for p in points])
                else:
                    print(f"Unhandled item type: {item_type}")
                    continue

                # Check if the item is within the isometric drawing area
                if isometric_area_coords and not is_within_area(geometry.bounds, isometric_area_coords):
                    continue

                line_width = drawing.get('width', 0)
                
                page_items.append({
                    'page': page_num + 1,
                    'vector_id': vector_id,
                    'item_position': item_position,
                    'item_type': item_type,
                    'geometry': geometry,
                    'angle': angle,
                    'length': length,
                    'is_isometric': is_iso,
                    'drawing': drawing,
                    'line_width': line_width,
                    'det_cat_1': 'Other',  # Initialize as 'Other'
                    'det_cat_2': None,
                    'det_cat_3': None,
                    'size': None,
                    'det_weight': None
                })

        # Determine significant widths for this page
        page_df = pd.DataFrame(page_items)
        pipe_width, measurement_width = determine_significant_widths(page_df)

        pipe_items = []
        

        # Identify and group pipe
        # pipe_items, item_numbers, pipe_groups  = classify_pipes(page_items, pipe_width, measurement_width, item_numbers=item_numbers)
        
        pipe_items, item_numbers, pipe_groups, pipelines, pipe_angles, pipe_connections = classify_pipes(
            page_items, pipe_width, measurement_width, item_numbers=item_numbers
        )
        total_groups += pipe_groups
        
        # Update the global dictionaries
        all_pipelines.update(pipelines)
        all_pipe_angles.update(pipe_angles)
        all_pipe_connections.update(pipe_connections)

        
        # Classify measurement lines and pointer lines
        for item in page_items:
            if item['item_type'] == 'l' and item['det_cat_1'] == 'Other':
                if item['line_width'] is None or pipe_width is None or measurement_width is None:
                    print(f"Skipping classification for item due to missing width information: {item}")
                    continue

                try:
                    is_parallel, parallel_pipes = is_parallel_to_pipe(item, pipe_items, debug_func=False)
                    if abs(item['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel:
                        item['det_cat_1'] = 'Measurement'
                        item['item_number'] = item_numbers['Measurement']
                        item_numbers['Measurement'] += 1
                        item_id = create_id(item['page'], item['vector_id'], item['item_position'])
        
                        # Add relationships to RelationshipManager
                        for pipe_id in parallel_pipes:
                            rm.add_relationship(item_id, pipe_id)
        
                        # Store parallel pipe IDs in the item for reference
                        item['parallel_pipes'] = parallel_pipes

                        # Add coordinate information for Measurement
                        coords = list(item['geometry'].coords)
                        item['start'] = coords[0]
                        item['end'] = coords[-1]
                        item['x0'], item['y0'] = coords[0]
                        item['x1'], item['y1'] = coords[-1]
                        
                    else:
                        # Filter items to check for pointers
                        items_to_check = [i for i in page_items if i['det_cat_1'] in ['Pipe', 'Potential_Pipe', 'Measurement']]
                        if is_pointer_line(item, items_to_check):
                            item['det_cat_1'] = 'Pointer'
                            item['item_number'] = item_numbers['Pointer']
                            item_numbers['Pointer'] += 1
                except Exception as e:
                    print(f"Error processing item: {item}")
                    print(f"Error message: {str(e)}")
                    
        pointer_info = is_pointer_line_vectorized(page_items)
        for info in pointer_info:
            item = page_items[info['pointer_index']]
            target_item = page_items[info['target_index']]
            item['det_cat_1'] = 'Pointer'
            item['points_to'] = target_item['det_cat_1']  # Use the actual category of the target item
            item['target_id'] = create_id(target_item['page'], target_item['vector_id'], target_item['item_position'])
            item['pointer_start'] = info['pointer_start']
            item['pointer_end'] = info['pointer_end']
            item['target_point'] = info['target_point']

            # Add coordinate information for Pointer
            item['start'] = (info['pointer_start'].x, info['pointer_start'].y)
            item['end'] = (info['pointer_end'].x, info['pointer_end'].y)
            item['x0'], item['y0'] = item['start']
            item['x1'], item['y1'] = item['end']
            
            #print(f"Pointer {create_id(item['page'], item['vector_id'], item['item_position'])} points to {item['points_to']} (ID: {item['target_id']})")
        
        # Verify classifications after pointer detection
        print("Classifications after pointer detection:")
        for item in page_items:
            print(f"{create_id(item['page'], item['vector_id'], item['item_position'])}: {item['det_cat_1']}")
                    
        # # After classifying pointers,  ensure all items have a classification
        # for item in page_items:
        #     if 'det_cat_1' not in item or item['det_cat_1'] is None:
        #         item['det_cat_1'] = 'Other'
                
        # # After classifying pipes, measurements, and pointers:
        # rectangles = detect_rectangles(page_items)
        # print(f"Detected {len(rectangles)} potential text callout rectangles.")

        # # Mark these items in your DataFrame:
        # for rect in rectangles:
        #     for item in rect:
        #         item['det_cat_1'] = 'Text Callout'

        # Update your classification counts
        classified_items['Text Callout'] = sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout')
        classified_items['Other'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Other') == 'Other')
        classified_items['Measurement'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Measurement') == 'Measurement')
       
        # Identify Arrow Tips needs work
        # # After classifying pipes, measurements, and pointers:
        # arrow_tip_indices = detect_arrow_tips(page_items)
        
        # logger.debug(f"Arrow tips detected: {arrow_tip_indices}")



        # # Classify arrow tips
        # for index in arrow_tip_indices:
        #     old_category = page_items[index]['det_cat_1']
        #     page_items[index]['det_cat_1'] = 'Arrow Tip'
        #     classified_items['Arrow Tip'] += 1
        #     classified_items[old_category] -= 1
        
        # Identify Arrow Tips needs work

        # Count classifications
        for item in page_items:
            classified_items[item['det_cat_1']] += 1

        all_items.extend(page_items)

        print(f"Page {page_num + 1}: Found {len(pipe_items)} pipe items")
        print(f"Page {page_num + 1}: Found {sum(1 for item in page_items if item['det_cat_1'] == 'Measurement')} measurement items")


    doc.close()
    print(f"\nTotal items processed: {total_items}")
    print(f"Classified items: {classified_items}")
    print(f"Total items found: {len(all_items)}")
    return pd.DataFrame(all_items)

def load_bom_data(filepath):
    return pd.read_excel(filepath)


def find_labels(raw_data_df, bom_data_df, isometric_area, page):
    labels = []
    pos_items = bom_data_df[bom_data_df['pdf_page'] == page][['pos', 'size']].to_dict('records')
    
    for _, row in raw_data_df[raw_data_df['pdf_page'] == page].iterrows():
        coords = eval(row['coordinates2'])
        if is_within_area(coords, isometric_area):
            value = str(row['value']).strip()
            matched_items = match_bom_item(value, pos_items)
            for matched_item in matched_items:
                labels.append({
                    'page': row['pdf_page'],
                    'coordinates': coords,
                    'det_cat_1': 'BOM Label',
                    'value': matched_item['pos'],
                    'size': matched_item['size']
                })
            
            # Check for Size Label, Size Break Label, and Length Label
            label_type = classify_label(value)
            if label_type:
                labels.append({
                    'page': row['pdf_page'],
                    'coordinates': coords,
                    'det_cat_1': label_type,
                    'value': value
                })
    
    return labels

def match_bom_item(value, pos_items):
    matched_items = []
    
    # Split the value by spaces to handle "F# G# B#" format
    value_parts = value.split()
    
    for part in value_parts:
        # Check for exact match
        exact_matches = [item for item in pos_items if str(item['pos']) == part]
        matched_items.extend(exact_matches)
        
        # Check for "[A-Z]#" or "[A-Z]##" format
        if re.match(r'^[A-Za-z]\d{1,2}$', part):
            # Extract the number part
            number_part = re.search(r'\d+', part).group()
            prefix_matches = [item for item in pos_items if str(item['pos']) == number_part]
            matched_items.extend(prefix_matches)
    
    return matched_items

def classify_label(value):
    # Remove spaces before NB
    value = re.sub(r'\s+NB', 'NB', value)
    
    # Check for Size Break Label
    if '"' in value and 'x' in value.lower() and 'nb' in value.lower():
        return 'Size Break Label'
    
    # Check for Size Label
    if '"' in value and 'nb' in value.lower():
        return 'Size Label'
    
    # Check for Length Label
    if ('"' in value or "'" in value) and 'nb' not in value.lower():
        return 'Length Label'
    
    return None

def exact_match(value, pos_items):
    return value in [str(item) for item in pos_items]

def prefix_match(value, pos_items):
    # Split the value into parts
    parts = re.findall(r'[A-Za-z]+|\d+', value)
    
    for item in pos_items:
        item_parts = re.findall(r'[A-Za-z]+|\d+', str(item))
        
        # Check if all parts of the item are in the value parts
        if all(part in parts for part in item_parts):
            return True
    
    return False

def load_detected_welds(filepath):
    df = pd.read_excel(filepath)
    weld_maps = []
    for _, row in df.iterrows():
        coords = eval(row['coordinates2'])
        weld_maps.append({
            'page': row['pdf_page'],
            'coordinates': coords,
            'det_cat_1': 'Weld Map'
        })
    return weld_maps

def create_id(page, vector_id, item_position):
    try:
        page = int(float(page))
        # Check if vector_id is NaN
        if isinstance(vector_id, float) and math.isnan(vector_id):
            vector_id = 0  # or some other default value
        else:
            vector_id = int(float(vector_id))
        item_position = int(float(item_position))
        return f"{page}-{vector_id}-{item_position}"
    except ValueError as e:
        print(f"Error creating ID: page={page}, vector_id={vector_id}, item_position={item_position}")
        print(f"Error message: {str(e)}")
        return None  # or some default value


# In your main function, after creating the DataFrame:
def clean_dataframe(df):
    # Convert 'page', 'vector_id', and 'item_position' to integers
    for col in ['page', 'vector_id', 'item_position']:
        df[col] = df[col].fillna(0).astype(int)
    return df

def draw_pdf_from_dataframe(df, input_filepath, output_filepath):
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")

        # Create a new page with the same dimensions as the input PDF
        input_page = doc[int(page_num) - 1]
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)

        # Draw pipes
        draw_pipes(outpage, group[group['det_cat_1'] == 'Pipe'])

        # # Draw measurements
        # draw_measurements(outpage, group[group['det_cat_1'] == 'Measurement'])

        # # Draw pointers
        # draw_pointers(outpage, group[group['det_cat_1'] == 'Pointer'])

        # # Draw labels (BOM Labels, Length Labels, etc.)
        # draw_labels(outpage, group[group['det_cat_1'].isin(['BOM Label', 'Length Label', 'Size Label', 'Size Break Label'])])

    outpdf.save(output_filepath)
    outpdf.close()
    doc.close()

def draw_pdf_from_dataframe(df, input_filepath, output_filepath):
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")

        # Convert page_num to integer and subtract 1
        input_page_num = int(page_num) - 1
        
        # Create a new page with the same dimensions as the input PDF
        input_page = doc[input_page_num]
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
        
        # Draw pipes
        draw_pipes(outpage, group[group['det_cat_1'] == 'Pipe'])

        # Draw other elements
        draw_other_elements(outpage, group[group['det_cat_1'] != 'Pipe'])

    print(f"Saving PDF to {output_filepath}")
    outpdf.save(output_filepath)
    print("PDF saved. Closing files...")
    outpdf.close()
    doc.close()
    print("PDF output process completed.")

def draw_pipes(outpage, pipes):
    pipelines = OrderedDict()

    for _, item in pipes.iterrows():
        shape = outpage.new_shape()
        
        start = item['start']
        end = item['end']
        angle = math.degrees(math.atan2(end[1] - start[1], end[0] - start[0]))
        item_number = item['item_number']
        group_number = item['group']

        # Update pipelines
        if group_number not in pipelines:
            pipelines[group_number] = []
        pipelines[group_number].append((start, end))

        # # Draw start label
        # shape.insert_text(fitz.Point(start[0], start[1]), 
        #                   f"({item_number}, G{group_number}) {angle:.1f} deg.", 
        #                   fontsize=5, color=(0, 0, 0))

        # # Draw end label
        # shape.insert_text(fitz.Point(end[0], end[1]), 
        #                   f"({item_number}, G{group_number})", 
        #                   fontsize=3, color=(0, 0, 0))

        # Draw the pipe line
        shape.draw_line(fitz.Point(start[0], start[1]), fitz.Point(end[0], end[1]))
        shape.finish(width=item['line_width'], color=(1, 0, 0))  # Red for pipe lines

        shape.commit()

    # Draw the pipe groupings with random colors
    for group_number, pipeline in pipelines.items():
        print(f"\n\nDrawing Pipe Group #: {group_number}")
        shape = outpage.new_shape()
        color = (random.uniform(0, 1), random.uniform(0, 1), random.uniform(0, 1))
        
        for start, end in pipeline:
            shape.draw_line(fitz.Point(start[0], start[1]), fitz.Point(end[0], end[1]))

        shape.insert_text(fitz.Point(pipeline[0][0][0], pipeline[0][0][1]), 
                          f"Pipe: {group_number}", fontsize=8, color=(0, 0, 0))
        

        
                           

        shape.finish(width=1, color=color)
        shape.commit()

def draw_other_elements(outpage, elements):
    for _, item in elements.iterrows():
        shape = outpage.new_shape()
        
        if item['det_cat_1'] == 'Measurement':
            color = (0.6, 0.3, 0)  # Brown
        elif item['det_cat_1'] == 'Pointer':
            color = (0, 0, 1)  # Blue
        elif item['det_cat_1'] == 'Text Callout':
            color = (0.5, 0, 0.5)  # Purple
        elif item['det_cat_1'] == 'Weld Map':
            color = (1, 0, 1)  # Magenta
        elif item['det_cat_1'] == 'BOM Label':
            color = (0, 1, 1)  # Cyan
        elif item['det_cat_1'] == 'Size Label':
            color = (0.5, 1, 0)  # Lime
        elif item['det_cat_1'] == 'Size Break Label':
            color = (1, 0, 0)  # Red
        elif item['det_cat_1'] == 'Length Label':
            color = (0.6, 0.3, 0)
        elif item['det_cat_1'] == 'Other':
            color = (1, 0.5, 0)  # Orange
        else:
            color = (1, 0.75, 0.8)  # Pink

        if item['det_cat_1'] in ['BOM Label', 'Weld Map', 'Size Label', 'Size Break Label', 'Length Label']:
            # Draw bounding box for labels
            x0, y0, x1, y1 = item['coordinates']
            rect = fitz.Rect(x0, y0, x1, y1)
            shape.draw_rect(rect)
            shape.finish(color=color, width=2)

            # Add label text
            if 'value' in item:
                shape.insert_text(rect.tl, str(item['value']), fontsize=8, color=color)
        else:
            # Draw lines for other elements
            start = fitz.Point(item['x0'], item['y0'])
            end = fitz.Point(item['x1'], item['y1'])
            shape.draw_line(start, end)
            shape.finish(width=item['line_width'], color=color)

        shape.commit()

        # Draw item number if available
        # if 'item_number' in item and pd.notna(item['item_number']):
        #     rect = fitz.Rect(item['x0'], item['y0'], item['x1'], item['y1'])
        #     outpage.insert_text(rect.tl, str(item['item_number']), fontsize=8, color=(0, 0, 0))

def main(input_filepath, output_filepath, raw_data_filepath, detected_welds_filepath, bom_data_filepath, pages_to_process=None):

    rm = RelationshipManager()
    
    isometric_area = {
        'x0': 804.5,
        'x1': 2404.5,
        'y0': 40.0,
        'y1': 1234.0
    }

    # Process vector drawings and create DataFrame
    print("\n\nIsolating Isometric Properties...")
    df = process_drawings(input_filepath, rm, pages_to_process, isometric_area_coords=isometric_area)

    # Use it in your main function:
    df = clean_dataframe(df)

    # Add items to RelationshipManager
    for _, item in df.iterrows():
        item_id = create_id(item['page'], item['vector_id'], item['item_position'])
        rm.add_item(item_id, item.to_dict())


    # Load detected welds
    print("\n\nLoading Weld, BOM, Raw Data...")
    weld_maps = load_detected_welds(detected_welds_filepath)

    # Add weld maps to RelationshipManager
    for weld_map in weld_maps:
        weld_id = create_id(weld_map['page'], 0, len(rm.items))  # Assuming unique position for each weld map
        rm.add_item(weld_id, weld_map)
        
    

    # Load BOM data
    bom_data = load_bom_data(bom_data_filepath)

    # Load raw data
    raw_data = pd.read_excel(raw_data_filepath)

    bom_labels = []
    
    print("\n\nEvaluating BOM, Size, Length, and Size Break Label Locations...")
    # for page in pages_to_process:
    #     bom_labels.extend(find_labels(raw_data, bom_data, isometric_area, page))
        
    #     # Add labels to RelationshipManager
    #     for label in page_labels:
    #         page_labels = find_labels(raw_data, bom_data, isometric_area, page)
    #         bom_labels.extend(page_labels)
    #         # label_id = create_id(label['page'], 0, len(rm.items))  # Assuming unique position for each label
    #         # rm.add_item(label_id, label)
    
    for page in pages_to_process:
        page_labels = find_labels(raw_data, bom_data, isometric_area, page)
        bom_labels.extend(page_labels)
        
        # Add labels to RelationshipManager
        for label in page_labels:
            label_id = create_id(label['page'], 0, len(rm.items))  # Assuming unique position for each label
            rm.add_item(label_id, label)
    
    # Filter weld maps for specified pages
    print("\n\nEvaluating Weld Map Locations...")    
    if pages_to_process is not None:
        weld_maps = [wm for wm in weld_maps if wm['page'] in pages_to_process]
    
    pd.DataFrame(bom_labels).to_excel("bom_labels.xlsx")
    print("\n\nConsolidating Data...")
    # Concatenate weld maps and BOM labels to the DataFrame
    df = pd.concat([df, pd.DataFrame(weld_maps), pd.DataFrame(bom_labels)], ignore_index=True)
    
    print("\n\nRelating pointers to items...")
    #df = relate_pointers_to_items(df, rm)
    # In your main function, after creating the DataFrame:
    #df = match_pointers_to_labels(df)
    #validate_pointer_connections(df)


    print(df.describe())
    print(f"Isometric lines after item identification: {len(df)}")
    
    # Print summary of the DataFrame
    print(f"\nDataFrame summary:")
    print(df.describe())
    #print(f"\nIsometric lines: {len(df)}")
    print(f"\n\nTotal items: {len(df)}")
    print(f"Weld Maps: {len(df[df['det_cat_1'] == 'Weld Map'])}")
    print(f"BOM Labels: {len(df[df['det_cat_1'] == 'BOM Label'])}")
    
    # print(f"\n\nPointers originating from labels: {len(df[df['originates_from'].str.endswith('Label', na=False)])}")
    # print(f"Unique origin types: {df['originates_from'].nunique()}")
    # print(f"Unique target types: {df['points_to'].nunique()}")
    
    # Output isometric lines to PDF
    output_isometric_lines_to_pdf(df, input_filepath, output_filepath, pd.DataFrame(bom_labels))
    
    draw_pdf_from_dataframe(df, input_filepath, os.path.join(output_dir, "output_test.pdf"))
    

    # Print the RelationshipManager object
    #print("\n\n--> Debug -- RelationshipMangager\n")
    #print(rm)
    
    return df, rm


if __name__ == "__main__":
    # Usage remains the same
    
    base_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Weld Recognition"
    # base_path = "/home/<USER>/Desktop/Weld client/"
    output_dir = os.path.join(base_path, "Testing")
    data_dir = os.path.join(output_dir, "Data")

    input_pdf_path = os.path.join(base_path, "Weld Recognition Test.pdf")
    output_filepath = os.path.join(output_dir, "output_iso.pdf")
    raw_data_path = os.path.join(data_dir, "raw_data.xlsx")
    detected_welds_path = os.path.join(data_dir, "detected_welds.xlsx")
    bom_data_path = os.path.join(data_dir, "bom_data.xlsx")

    start_time = time.time()

    # Process only pages 1, 2, 3, 4, and 5
    df, relationship_manager = main(input_pdf_path, output_filepath, raw_data_path, detected_welds_path, bom_data_path, pages_to_process=[1])

    end_time = time.time()

    total_time = end_time - start_time


    print(f"\n\nIdentified in {total_time} seconds...")

    #related_items = relationship_manager.get_related_items(item_id)
    #related_pipes = relationship_manager.get_related_items_by_type(item_id, 'Pipe')

    #print(f"Related items: {related_items}")
    #print(f"Related pipes: {related_pipes}")

    df.to_excel(os.path.join(output_dir, "vector_data.xlsx"))
    #df.to_excel(os.path.join(output_dir, "vector_data_with_weld_maps.xlsx"))
    #regions.to_excel(os.path.join(output_dir, "iso_regions_data.xlsx"))

###############################
###############################
###############################
###############################

# def get_connected_lines(start_line, df):
#     connected_lines = [start_line]
#     line_queue = [start_line]
    
#     while line_queue:
#         current_line = line_queue.pop(0)
#         current_geom = current_line['geometry']
        
#         for _, other_line in df[df['det_cat_1'].isin(['Pointer', 'Other'])].iterrows():
#             if other_line.name in [line.name for line in connected_lines]:
#                 continue
            
#             other_geom = other_line['geometry']
#             if current_geom.touches(other_geom):
#                 connected_lines.append(other_line)
#                 line_queue.append(other_line)
    
#     return connected_lines

# def trace_pointer(pointer_chain, label_items, pipe_items, distance_threshold):
#     def ensure_geometry(geom):
#         if isinstance(geom, str) and geom.startswith('geometry '):
#             # Remove 'geometry ' prefix and parse WKT
#             return wkt.loads(geom[9:])
#         elif isinstance(geom, base.BaseGeometry):
#             return geom
#         else:
#             return None

#     pointer_coords = []
#     for line in pointer_chain:
#         line_geom = ensure_geometry(line['geometry'])
#         if line_geom:
#             pointer_coords.extend(line_geom.coords)
    
#     if not pointer_coords:
#         return None, None
    
#     pointer_line = LineString(pointer_coords)
#     start_point = Point(pointer_line.coords[0])
#     end_point = Point(pointer_line.coords[-1])

#     # Find nearest label (origin)
#     origin = None
#     min_distance = float('inf')
#     for _, label in label_items.iterrows():
#         label_geom = ensure_geometry(label['geometry'])
#         if label_geom is None:
#             continue
#         distance = label_geom.distance(start_point)
#         if distance < min_distance and distance <= distance_threshold:
#             origin = label
#             min_distance = distance

#     # Find nearest pipe (target)
#     target = None
#     min_distance = float('inf')
#     for _, pipe in pipe_items.iterrows():
#         pipe_geom = ensure_geometry(pipe['geometry'])
#         if pipe_geom is None:
#             continue
#         distance = pipe_geom.distance(end_point)
#         if distance < min_distance and distance <= distance_threshold:
#             target = pipe
#             min_distance = distance

#     return origin, target



# def relate_pointers_to_items(df, rm, distance_threshold=1):
#     label_items = df[df['det_cat_1'].str.endswith('Label', na=False)]
#     pointer_items = df[df['det_cat_1'] == 'Pointer']
#     pipe_items = df[df['det_cat_1'].isin(['Pipe', 'Potential_Pipe', 'Measurement'])]

#     for _, pointer in pointer_items.iterrows():
#         pointer_chain = get_connected_lines(pointer, df)
#         origin, target = trace_pointer(pointer_chain, label_items, pipe_items, distance_threshold)

#         if origin is not None and target is not None:
#             origin_id = create_id(origin['page'], origin['vector_id'], origin['item_position'])
#             target_id = create_id(target['page'], target['vector_id'], target['item_position'])

#             for p in pointer_chain:
#                 pointer_id = create_id(p['page'], p['vector_id'], p['item_position'])
#                 df.loc[p.name, 'points_to'] = target['det_cat_1']
#                 df.loc[p.name, 'target_id'] = target_id
#                 df.loc[p.name, 'originates_from'] = origin['det_cat_1']
#                 df.loc[p.name, 'origin_id'] = origin_id
#                 rm.add_relationship(pointer_id, target_id)
#                 rm.add_relationship(pointer_id, origin_id)

#             rm.add_relationship(origin_id, target_id)

#     return df



# def relate_pointers_to_items(df, rm):
#     pointer_items = df[df['det_cat_1'] == 'Pointer']
#     target_items = df[df['det_cat_1'].isin(['Pipe', 'Potential_Pipe', 'Measurement', 'BOM Label', 'Size Label', 'Size Break Label', 'Length Label'])]
    
#     for _, pointer in pointer_items.iterrows():
#         try:
#             pointer_id = create_id(pointer['page'], pointer['vector_id'], pointer['item_position'])
#             pointer_geom = pointer['geometry']
#             pointer_start = Point(pointer_geom.coords[0])
#             pointer_end = Point(pointer_geom.coords[-1])
            
#             closest_target = None
#             min_distance = float('inf')
            
#             for _, target in target_items.iterrows():
#                 try:
#                     target_id = create_id(target['page'], target['vector_id'], target['item_position'])
#                     target_geom = target['geometry']
                    
#                     if isinstance(target_geom, LineString):
#                         target_points = [Point(target_geom.coords[0]), Point(target_geom.coords[-1])]
#                     elif isinstance(target_geom, Polygon):
#                         target_points = [Point(p) for p in target_geom.exterior.coords]
#                     else:
#                         continue  # Skip if geometry type is not handled
                    
#                     for target_point in target_points:
#                         dist_start = pointer_start.distance(target_point)
#                         dist_end = pointer_end.distance(target_point)
                        
#                         if min(dist_start, dist_end) < min_distance:
#                             min_distance = min(dist_start, dist_end)
#                             closest_target = target
#                             closest_target_id = target_id
                
#                 except (ValueError, TypeError) as e:
#                     #print(f"Error processing target item: {e}")
#                     continue
            
#             if closest_target is not None:
#                 # Update the pointer item with information about its target
#                 df.loc[pointer.name, 'points_to'] = closest_target['det_cat_1']
#                 df.loc[pointer.name, 'target_id'] = closest_target_id
                
#                 # Add relationship to RelationshipManager
#                 rm.add_relationship(pointer_id, closest_target_id)
        
#         except (ValueError, TypeError) as e:
#             print(f"Error processing pointer item: {e}")
#             continue
    
#     return df

