import pandas as pd
import json
import re
import os
import traceback


def extract_column_ranges(image_width, bom_roi: dict) -> dict:
    """Converts BOM ROI payload to column ranges"""
    column_ranges = {}
    start_x = 0
    column_ratios = [start_x] + bom_roi["columnRatios"]

    table_columns = [next(iter(k)) for k in bom_roi["tableColumns"]]

    for n, (x, y) in enumerate(zip(column_ratios, column_ratios[1:])):
        column_ranges[table_columns[n]] = (int(x * image_width), int(y * image_width))

    return column_ranges

def extract_standard_table(ocr_results,
                        roi_payload: dict,
                        table_name: str,
                        y_tolerance=15,
                        pages=None):
    """
    Extract structured standard table from OCR results dictionary.

    Args:
        ocr_results: Dictionary containing OCR data with bounding boxes and text
        roi_payload: Dictionary containing ROI information
        table_name: Name of the table to extract
        y_tolerance: Tolerance for grouping words in the same row
        pages: Optional list of specific pages to process

    Returns:
        pandas.DataFrame: Structured table with data from the specified table
    """
    # Initialize data structure
    table_data = []

    print(f"Keys in OCR results: {list(ocr_results.keys())}")

    # Process each page in OCR results
    for page_key, page_data in ocr_results.items():
        # Skip if pages is specified and this page is not in the list
        if pages and page_key not in pages:
            continue

        image_width, image_height = image_size = page_data['image_size']
        # Extract page number from key
        page_match = re.search(r'page_(\d+)', page_key)
        if page_match:
            pdf_page = int(page_match.group(1))
        else:
            # Alternative: try to extract number from any position in the key
            numbers = re.findall(r'(\d+)', page_key)
            if numbers:
                pdf_page = int(numbers[0])
            else:
                print(f"Warning: Could not extract page number from {page_key}, using 0")
                pdf_page = 0

        print(f"Processing page {pdf_page} from key {page_key}")

        # Verify that expected data structure exists
        if 'lines' not in page_data:
            print(f"Warning: 'lines' not found in page data for {page_key}")
            continue

        # Calculate column ranges for each page
        try:
            pageToGroup = roi_payload["pageToGroup"][pdf_page]
            table_roi = None
            rois = roi_payload["groupRois"][pageToGroup]
            for roi in rois:
                if roi["columnName"].lower() == table_name.lower():
                    table_roi = roi
                    break

            if not table_roi:
                print(f"Warning: No {table_name} ROI found for page {pdf_page}")
                continue

            column_ranges = extract_column_ranges(image_width, table_roi)
            column_names = [k for k in column_ranges.keys()]

            print(f"Found columns for {table_name}: {column_names}")
            print(f"Column ranges: {column_ranges}")
        except Exception as e:
            print(f"Error extracting column ranges: {e}")
            continue

        # First, extract all words with their positions
        all_words = []
        for line in page_data['lines']:
            if 'merged_words' not in line or not line['merged_words']:
                continue

            for word in line['merged_words']:
                try:
                    # Check if the word is within the table ROI
                    word_x = word['bounding_rect']['x']
                    word_y = word['bounding_rect']['y']

                    # Only include words that are within the table ROI
                    # if (table_roi['x'] <= word_x <= table_roi['x'] + table_roi['width'] and
                    #     table_roi['y'] <= word_y <= table_roi['y'] + table_roi['height']):
                    all_words.append({
                        'text': word['text'],
                        'x': word_x,
                        'y': word_y,
                        'width': word['bounding_rect']['width'],
                        'height': word['bounding_rect']['height']
                    })
                except Exception as e:
                    print(f"Error processing word: {e}, word: {word}")

        # Group words by rows based on y-coordinates
        rows = []
        current_row = []

        # Sort all words by y-coordinate first
        all_words.sort(key=lambda w: w['y'])

        for i, word in enumerate(all_words):
            if not current_row:
                current_row = [word]
            elif abs(word['y'] - current_row[0]['y']) <= y_tolerance:
                current_row.append(word)
            else:
                # Sort words in the row by x-coordinate (left to right)
                current_row.sort(key=lambda w: w['x'])
                rows.append(current_row)
                current_row = [word]

        # Add the last row if not empty
        if current_row:
            current_row.sort(key=lambda w: w['x'])
            rows.append(current_row)

        # Process each row and map words to columns
        for row_idx, row in enumerate(rows):
            # Skip empty rows
            if not row:
                continue

            # Initialize row data with page number
            row_data = {'pdf_page': pdf_page}

            # Initialize all columns with empty strings
            for col_name in column_names:
                row_data[col_name] = ''

            # Collect words for each column
            column_words = {col_name: [] for col_name in column_names}

            # Assign words to columns based on x-coordinate
            for word in row:
                x_pos = word['x']

                for key, column_range in column_ranges.items():
                    if column_range[0] <= x_pos <= column_range[1]:
                        # if key == "material_description":
                        #     row_desc_words.append(word)
                        # else:
                        row_data[key] = word['text']
                        break

                # # Find which column this word belongs to
                # for col_name, (start_x, end_x) in column_ranges.items():
                #     if start_x <= x_pos <= end_x:
                #         column_words[col_name].append(word)
                #         break

            # Combine words for each column
            for col_name, words in column_words.items():
                if words:
                    # Sort words by x-coordinate to maintain reading order
                    words.sort(key=lambda w: w['x'])
                    row_data[col_name] = ' '.join(word['text'] for word in words)

            # Only add rows that have at least one non-empty column
            if any(value.strip() for value in row_data.values() if isinstance(value, str)):
                table_data.append(row_data)
                print(f"Added row: {row_data}")

    # Convert to DataFrame
    if not table_data:
        print(f"Warning: No data extracted for table {table_name}")
        return pd.DataFrame()

    df = pd.DataFrame(table_data)
    return df


def extract_bom_table_from_ocr(ocr_results,
                               roi_payload: dict,
                               y_tolerance=15,
                               pages=None):
    """
    Extract structured BOM table from OCR results dictionary.

    Args:
        ocr_results: Dictionary containing OCR data with bounding boxes and text
        y_tolerance: Tolerance for grouping words in the same row
        pages: Optional list of specific pages to process

    Returns:
        pandas.DataFrame: Structured BOM table
    """
    # Initialize data structure
    bom_data = []

    print(f"Keys in OCR results: {list(ocr_results.keys())}")

    # Process each page in OCR results
    for page_key, page_data in ocr_results.items():

        image_width, image_height = image_size = page_data['image_size']
        # Extract page number from key
        page_match = re.search(r'page_(\d+)', page_key)
        if page_match:
            pdf_page = int(page_match.group(1))
        else:
            # Alternative: try to extract number from any position in the key
            numbers = re.findall(r'(\d+)', page_key)
            if numbers:
                pdf_page = int(numbers[0])
            else:
                print(f"Warning: Could not extract page number from {page_key}, using 0")
                pdf_page = 0

        print(f"Processing page {pdf_page} from key {page_key}")

        # Verify that expected data structure exists
        if 'lines' not in page_data:
            print(f"Warning: 'lines' not found in page data for {page_key}")
            continue

        # Calculate column ranges for each page. No caching
        pageToGroup = roi_payload["pageToGroup"][pdf_page]
        bom_roi = None
        rois = roi_payload["groupRois"][pageToGroup]
        for roi in rois:
            if roi["columnName"] == "BOM":
                bom_roi = roi
                break
        else:
            print(f"Warning: No BOM ROI found for page {pdf_page}")
            continue

        column_ranges = extract_column_ranges(image_width, bom_roi)
        column_names = [k for k in column_ranges.keys()]
        leftmost_column_x = column_ranges[column_names[0]][1]

        # First, extract all words with their positions
        all_words = []
        for line in page_data['lines']:
            if 'merged_words' not in line or not line['merged_words']:
                continue

            for word in line['merged_words']:
                try:
                    all_words.append({
                        'text': word['text'],
                        'x': word['bounding_rect']['x'],
                        'y': word['bounding_rect']['y'],
                        'width': word['bounding_rect']['width'],
                        'height': word['bounding_rect']['height']
                    })
                except Exception as e:
                    print(f"Error processing word: {e}, word: {word}")

        # Group words by rows based on y-coordinates
        rows = []
        current_row = []

        # Sort all words by y-coordinate first
        all_words.sort(key=lambda w: w['y'])

        for i, word in enumerate(all_words):
            if not current_row:
                current_row = [word]
            elif abs(word['y'] - current_row[0]['y']) <= y_tolerance:
                current_row.append(word)
            else:
                # Sort words in the row by x-coordinate (left to right)
                current_row.sort(key=lambda w: w['x'])
                rows.append(current_row)
                current_row = [word]

        # Add the last row if not empty
        if current_row:
            current_row.sort(key=lambda w: w['x'])
            rows.append(current_row)

        # Step 1: Identify sections and part rows
        sections = []
        parts = []
        current_section = None

        for i, row in enumerate(rows):
            # Leftmost word in the row
            leftmost = row[0]

            # Check if this is a section header
            if leftmost['x'] < leftmost_column_x and len(leftmost['text']) > 1 and not re.match(r'^\d+$', leftmost['text']):
                section_text = leftmost['text']
                # if section_text == "VALVESUIN:LNEATEMS":
                #     section_text = "VALVES"

                sections.append({
                    'index': i,
                    'y': leftmost['y'],
                    'text': section_text
                })
                current_section = section_text
                print(f"Found section: {current_section} at row {i}, y={leftmost['y']}")

            # Check if this is a part row (starts with a number)
            elif leftmost['x'] < leftmost_column_x and re.match(r'^\d+$', leftmost['text']):
                parts.append({
                    'index': i,
                    'y': leftmost['y'],
                    'text': leftmost['text'],
                    'section': current_section
                })
                print(f"Found part: {leftmost['text']} in section {current_section} at row {i}, y={leftmost['y']}")

        # Step 2: Determine row ranges for each part
        part_ranges = []
        for i, part in enumerate(parts):
            # Start index is the part's row index
            start_index = part['index']

            # End index is the next part's row index or the end of rows
            end_index = len(rows)
            if i < len(parts) - 1:
                end_index = parts[i+1]['index']

            # Check if a section header appears before the next part
            for section in sections:
                if section['index'] > start_index and section['index'] < end_index:
                    end_index = section['index']

            part_ranges.append({
                'part': part,
                'start_index': start_index,
                'end_index': end_index
            })

        # Step 3: Process each part and its rows
        for part_range in part_ranges:
            part = part_range['part']
            start_index = part_range['start_index']
            end_index = part_range['end_index']

            # Initialize row data
            row_data = {
                'pdf_page': pdf_page,
                'section': part['section'],
                column_names[0]: part['text'],
            }

            # Initialize remaining columns
            for i in range(1, len(column_names)):
                row_data[column_names[i]] = ''

            # Process rows for this part
            description_by_row = []

            for i in range(start_index, end_index):
                row_desc_words = []

                for word in rows[i]:
                    x_pos = word['x']

                    for key, column_range in column_ranges.items():
                        if column_range[0] <= x_pos <= column_range[1]:
                            if key == "material_description":
                                row_desc_words.append(word)
                            else:
                                row_data[key] = word['text']
                                break

                # If there are description words in this row, add them to the list
                if row_desc_words:
                    # Sort by x position to preserve reading order within row
                    row_desc_words.sort(key=lambda w: w['x'])
                    row_text = ' '.join(word['text'] for word in row_desc_words)
                    description_by_row.append(row_text)

            # Combine all description rows with spaces
            row_data['material_description'] = ' '.join(description_by_row)

            # Add row to dataset
            bom_data.append(row_data)
            print(f"Added row: {row_data}")

    # Convert to DataFrame
    if not bom_data:
        print("Warning: No BOM data extracted")
        return pd.DataFrame()

    df = pd.DataFrame(bom_data)
    return df


if __name__ == "__main__":
    # File path
    json_path = r"debug/ocr/ocr_results.json"
    roi_payload = r"C:\Drawings\31-3-25 tr-002\tr002 layout.json"

    from src.utils.convert_roi_payload import convert_roi_payload

    roi_payload = convert_roi_payload(roi_payload, force_extract=True)

    # Load OCR results from JSON
    try:
        with open(json_path, 'r') as f:
            ocr_results = json.load(f)

        print(f"Successfully loaded OCR data from {json_path}")

        # Process the OCR results
        bom_df = extract_bom_table_from_ocr(ocr_results, roi_payload)

        # Print summary of results
        if not bom_df.empty:
            print(f"\nExtracted {len(bom_df)} BOM entries across {bom_df['pdf_page'].nunique()} pages")
            print("\nSample of extracted data:")
            print(bom_df.head(20))

            # Save results to CSV
            output_path = os.path.join(os.path.dirname(json_path), "structured_bom_table.csv")
            bom_df.to_csv(output_path, index=False)
            print(f"\nSaved structured BOM table to {output_path}")
        else:
            print("No BOM data was extracted")

    except Exception as e:
        print(f"Error processing OCR results:")
        print(traceback.format_exc())  # Print full traceback