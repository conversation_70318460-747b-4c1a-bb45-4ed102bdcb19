from typing import Iterable
import fitz  # PyMuPDF
import pandas as pd
import os
import shutil
import tempfile
import multiprocessing
from multiprocessing import freeze_support
import time
from itertools import groupby
from operator import itemgetter

from PySide6.QtGui import *
from PySide6.QtCore import *
from PySide6.QtWidgets import *

from fitz import Rect
from PIL import Image
import io

MULTIPROCESS: bool = False
PYPDF_SAVE: bool = True


def is_within_tolerance(val1, val2, tolerance=2):
    return abs(val1 - val2) <= tolerance

def group_pages_by_layout(doc_path):
    groups = {}
    group_counter = 0
    results = []

    doc = fitz.open(doc_path)

    for page_num in range(len(doc)):
        #print(f"Processing Page {page_num + 1}")
        page = doc[page_num]
        width, height = round(page.rect.width, 2), round(page.rect.height, 2)
        
        crop_box = tuple(round(v, 2) for v in page.cropbox)
        media_box = tuple(round(v, 2) for v in page.mediabox)
        rotation = page.rotation
        rotation_matrix = page.rotation_matrix

        c_drawings = page.get_cdrawings(extended=False)
        
        # Initialize variables to track the outermost drawing
        left_margin = width
        right_margin = 0
        top_margin = height
        bottom_margin = 0

        # Extract text blocks and annotations to exclude them from the calculations
        text_blocks = page.get_text("blocks")
        annotations = page.annots()

        excluded_areas = []

        for block in text_blocks:
            bbox = block[:4]
            adjusted_bbox = adjust_bbox(bbox, rotation_matrix)
            excluded_areas.append(adjusted_bbox)

        if annotations:
            for annot in annotations:
                bbox = annot.rect
                adjusted_bbox = adjust_bbox((bbox.x0, bbox.y0, bbox.x1, bbox.y1), rotation_matrix)
                excluded_areas.append(adjusted_bbox)

        def is_in_excluded_area(bbox):
            for area in excluded_areas:
                if (bbox[0] >= area[0] and bbox[2] <= area[2] and
                    bbox[1] >= area[1] and bbox[3] <= area[3]):
                    return True
            return False

        # Iterate through all drawings to find the outermost ones
        for drawing in c_drawings:
            if 'rect' in drawing:
                rect = drawing['rect']
                rect = adjust_bbox(rect, rotation_matrix)
                if is_in_excluded_area(rect):
                    continue
                left_margin = min(left_margin, rect[0])
                right_margin = max(right_margin, rect[2])
                top_margin = min(top_margin, rect[1])
                bottom_margin = max(bottom_margin, rect[3])
            elif 'items' in drawing:
                for item in drawing['items']:
                    if item[0] == 're':  # rectangle
                        rect = item[1]
                        rect = adjust_bbox(rect, rotation_matrix)
                        if is_in_excluded_area(rect):
                            continue
                        left_margin = min(left_margin, rect[0])
                        right_margin = max(right_margin, rect[2])
                        top_margin = min(top_margin, rect[1])
                        bottom_margin = max(bottom_margin, rect[3])
                    elif item[0] in ['l', 'c', 'qu']:  # line, curve, or quad
                        points = item[1:]
                        for point in points:
                            point = fitz.Point(point[0], point[1]) * rotation_matrix
                            if is_in_excluded_area((point.x, point.y, point.x, point.y)):
                                continue
                            left_margin = min(left_margin, point.x)
                            right_margin = max(right_margin, point.x)
                            top_margin = min(top_margin, point.y)
                            bottom_margin = max(bottom_margin, point.y)

        # Calculate margins
        left_margin = round(left_margin, 2)
        right_margin = round(width - right_margin, 2)
        top_margin = round(top_margin, 2)
        bottom_margin = round(height - bottom_margin, 2)

        # Debug statements to print out the values
        print(f"Page {page_num + 1} width: {width}, height: {height}, rotation: {rotation}")
        # print(f"left_margin: {left_margin}, right_margin: {right_margin}")
        # print(f"top_margin: {top_margin}, bottom_margin: {bottom_margin}")

        # Create a signature that includes the page size, margins, and rotation
        page_signature = (width, height, left_margin, right_margin, top_margin, bottom_margin, rotation)

        # Apply tolerance to the margins
        found_group = False
        for sig in groups:
            if all(is_within_tolerance(sig[i], page_signature[i]) for i in range(len(sig))):
                current_group = groups[sig]
                found_group = True
                break
        
        if not found_group:
            group_counter += 1
            groups[page_signature] = group_counter
            current_group = group_counter

        # Add page info to results
        results.append({
            'document_path': doc_path,
            'page_number': page_num + 1,
            'group_number': current_group,
            'width': width,
            'height': height,
            'crop_box': crop_box,
            'media_box': media_box,
            'left_margin': left_margin,
            'right_margin': right_margin,
            'top_margin': top_margin,
            'bottom_margin': bottom_margin,
            'rotation': rotation
        })

    # Close the document
    doc.close()

    # Create a DataFrame from the results
    df = pd.DataFrame(results)

    return df

def save_pdf(original_doc_path: str, output_path: str, group_number: int, pages_to_keep: Iterable):
    """Keep this as outer function so multiprocessing can pickle it"""
    original_filename = os.path.splitext(os.path.basename(original_doc_path))[0]
    output_filename = f"{original_filename}_group_{group_number}.pdf"
    output_file_path = os.path.join(output_path, output_filename)  

    if PYPDF_SAVE:
        from pypdf import PdfReader, PdfWriter
        reader = PdfReader(original_doc_path)
        writer = PdfWriter()

        for page_num, page in enumerate(reader.pages, 1):
            if page_num in pages_to_keep: # +1 not needed here as enum starts at 1
                writer.add_page(page)

        with open(output_file_path, 'wb') as out:
            writer.write(out)

    else:
        # PyMuPDF method

        # Create a copy of the original PDF
        shutil.copy2(original_doc_path, output_file_path)
    
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_path = temp_file.name

        # Groups pages into consecutive page ranges
        # page_ranges = []
        # for k, g in groupby(enumerate(pages_to_keep), lambda x:x[0]-x[1]):
        #     group = list(map(itemgetter(1), g))
        #     page_ranges.append((group[0], group[-1]))

        # src = fitz.open(original_doc_path)
        # dest = fitz.open()  # output PDF for 1 page
        # for start_page, end_page in page_ranges:
        #     # copy over current page
        #     print(start_page, end_page)
        #     dest.insert_pdf(src, from_page=start_page, to_page=end_page)

        # dest.save(output_file_path, deflate=True)
        # dest.close()
        
        # Open the copy and save to temp file
        with fitz.open(output_file_path) as doc:
            # Create a mapping of new page numbers to original page numbers
            new_to_original_mapping = {}
            new_page_number = 1
            # Iterate through pages in reverse order
            for page in reversed(doc):
                if page.number + 1 not in pages_to_keep:  # +1 because page numbers are 0-indexed
                    doc.delete_page(page.number)

            # Save the modified document to the temporary file
            doc.save(temp_path, deflate=True) #, clean=True) <- Not sure what Clean does

        # Replace the original file with the temporary file
        shutil.move(temp_path, output_file_path)
    
    print(f"Created: {output_filename}")

def create_grouped_pdfs(df, original_doc_path, output_path):
    print()
    print("Saving grouped pdfs...")
    grouped = df.groupby('group_number')
    if MULTIPROCESS:
        n = multiprocessing.cpu_count()
        with multiprocessing.Pool(n) as pool:
            # Can't seem to pass group_df directly to a process, so pass the page number list
            pool.starmap(save_pdf, [(original_doc_path, output_path, group_number, set(group_df['page_number'].tolist())) for group_number, group_df in grouped])
    else:
        for group_number, group_df in grouped:
            pages_to_keep = set(group_df['page_number'].tolist())
            save_pdf(original_doc_path, output_path, group_number, pages_to_keep)

    # Update the dataframe with the new page numbers
    for group_number, group_df in grouped:
        group_df['new_page_number'] = range(1, len(group_df) + 1)
        df.loc[group_df.index, 'new_page_number'] = group_df['new_page_number']


class MainWindow(QWidget):

    def __init__(self):
        super().__init__(None)
        self.setLayout(QVBoxLayout())

        self.outerBox = None

        doc_path = r"c:\Users\<USER>\Desktop\Drawings\Binder1_group_3.pdf" #r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Excel USA\Excel 008\ISOs.A.pdf"

        self.filenameLabel = QLabel("Filename: None")
        self.filenameLabel.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.layout().addWidget(self.filenameLabel)

        file_browser_btn = QPushButton('Open PDF')
        file_browser_btn.clicked.connect(self.open_file_dialog)
        self.layout().addWidget(file_browser_btn)

        self.pageList = QComboBox()
        self.pageList.currentIndexChanged.connect(self.onPageChanged)
        self.layout().addWidget(self.pageList)

        self.chkRemovePageRotation = QCheckBox(text="Remove Page Rotation (Refreshes Rect List)")
        self.chkRemovePageRotation.stateChanged.connect(self.removePageRotationChecked)
        self.layout().addWidget(self.chkRemovePageRotation)

        label = QLabel("Rect List (All rect c_drawings)")
        label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.layout().addWidget(label)
        self.rectList = QComboBox()
        self.rectList.currentIndexChanged.connect(self.onRectListChanged)
        self.layout().addWidget(self.rectList)

        label = QLabel("Rect List (Filtered, Candidate Rects for Outer Box)")
        label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.layout().addWidget(label)
        self.rectListCandidates = QComboBox()
        self.rectListCandidates.currentIndexChanged.connect(self.onRectListCandidateChanged)
        self.rectListCandidates.activated.connect(self.onRectListCandidateChanged)
        self.layout().addWidget(self.rectListCandidates)

        pbDetectOuterbox = QPushButton('Draw Detected Outer Box (Blue Outline)')
        pbDetectOuterbox.clicked.connect(self.detectOuterbox)
        self.layout().addWidget(pbDetectOuterbox)

        # self.chkRect = QCheckBox(text="Rect Bounding Box (R)")
        # self.chkLine = QCheckBox(text="Line (L)")
        # self.chkQuad = QCheckBox(text="Quad (Q)")
        # self.chkRe = QCheckBox(text="Rect (RE)")
        # self.chkBezier = QCheckBox(text="Bezier (c)")

        # hbox = QWidget()
        # hbox.setLayout(QHBoxLayout())
        # hbox.setFixedHeight(48)
        # self.layout().addWidget(hbox)
        # for chk in [self.chkRect, self.chkLine, self.chkQuad, self.chkRe, self.chkBezier]:
        #     hbox.layout().addWidget(chk)
        #     chk.setChecked(chk == self.chkRect)
        #     chk.stateChanged.connect(self.showAllDrawings)

        # draw_all = QPushButton('Show All Checkedbox Drawings')
        # draw_all.clicked.connect(self.showAllDrawings)
        # self.layout().addWidget(draw_all)
        
        self.label = QLabel()
        self.layout().addWidget(self.label)

        self.show()
        self.setMinimumSize(QSize(1280, 768))

        self.openPdf(doc_path)
        self.c_drawings = None

        self.setWindowTitle("PyMuPDF Drawings")

    def open_file_dialog(self):
        dialog = QFileDialog(self)
        dialog.setDirectory(r'C:\images')
        dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        dialog.setNameFilter("PDF (*.pdf)")
        dialog.setViewMode(QFileDialog.ViewMode.List)
        if dialog.exec():
            filenames = dialog.selectedFiles()
            if filenames:
                file = filenames[0]
                self.openPdf(file)

    def openPdf(self, file):
        # self.doc = fitz.open(file, fitz.Document(file, filetype="pdf").convert_to_pdf()) # Slow but rect are drawn correctly? Why
        self.doc = fitz.open(file)
        self.filenameLabel.setText(f"Filename: {file}")
        self.updatePageList()
        self.setPage(0)

    def setPage(self, page_num: int):
        self.outerBox = None
        self.page_num = page_num

        # Duplicate the page here so that rotation can be unset
        doc = fitz.open()
        doc.insert_pdf(self.doc, self.page_num)
        self.page = page = doc[0]
        # self.page = page = self.doc[page_num]
        if self.chkRemovePageRotation.isChecked():
            self.page.remove_rotation()
            print(self.page.rotation)
        self.c_drawings = page.get_cdrawings(extended=False)
        self.count = 0
        self.updateRectList()
        self.detectOuterbox()

    def updateRectList(self):
        self.rectList.clear()
        self.rectListCandidates.clear()
        rotation_matrix = self.page.rotation_matrix

        largestRect = None
        largestRectArea: float = None
        lines = []
        lineWidths = []
        lineHeights = []
        for n, drawing in enumerate(self.c_drawings):
            if 'rect' in drawing:
                rect = drawing['rect']
                r = Rect(rect)
                self.rectList.addItem(f"Rect drawing_index={n} - {r}, Width - {r.width}, Height - {r.height}, Area - {r.get_area()}", userData=drawing)
                # Ignore small rects which are not considered lines
                if r.width < 100 and r.height < 100:
                    continue

                # Continue checking lines if we already detected a viable rect
                if r.get_area() == 0: 
                    if r.width < 100 and r.height > self.page.rect.height // 1.5:
                        # Vertical line
                        lineHeights.append((n, drawing))
                    elif r.height < 100 and r.width > self.page.rect.width // 1.5:
                        # Horizontal line
                        lineWidths.append((n, drawing))
                    continue

                # Check box is full size rect and within bounds of page
                if not all((r.width < self.page.rect.width, r.height < self.page.rect.height)):
                    continue 
                if r.get_area() > self.page.rect.get_area() * 0.7:
                    if not largestRectArea or r.get_area() > largestRectArea:
                        largestRect = (n, drawing)
                        largestRectArea = r.get_area()

        if largestRect:
            n, drawing = largestRect
            r = Rect(drawing["rect"])
            self.rectListCandidates.addItem(f"Largest Rect drawing_index={n} - {r}, Width - {r.width}, Height - {r.height}, Area - {r.get_area()}", userData=drawing)

        left_rect, left_margin = None, None
        top_rect, top_margin = None, None
        right_rect, right_margin = None, None
        bottom_rect, bottom_margin = None, None

        for n, drawing in lineWidths:
            r = Rect(drawing["rect"])
            if not top_margin or r.y0 < top_margin.y0:
                top_margin = r
                top_rect = (n, drawing)
            if not bottom_margin or r.y1 > bottom_margin.y1:
                bottom_margin = r
                bottom_rect = (n, drawing)

        for n, drawing in lineHeights:
            r = Rect(drawing["rect"])
            if not left_margin or r.x0 < left_margin.x0:
                left_margin = r
                left_rect = (n, drawing)
            if not right_margin or r.x1 > right_margin.x1:
                right_margin = r
                right_rect = (n, drawing)

        def rectFromMargins(left, top, right, bottom) -> Rect:
            return Rect([left[0], top[1], right[2], bottom[3]])
        
        # Fallback, if no largest rect nor line margins, use page rect until better solution found
        self.outerBox = None
        if all([left_margin, top_margin, right_margin, bottom_margin]):
            self.outerBox = rectFromMargins(left_rect[1]["rect"], top_rect[1]["rect"], right_rect[1]["rect"], bottom_rect[1]["rect"])
        
        if largestRect and self.outerBox:
            r = Rect(largestRect[1]["rect"])
            if r.get_area() > self.outerBox.get_area():
                self.outerBox = r
        elif largestRect and not self.outerBox:
             r = Rect(largestRect[1]["rect"])
             self.outerBox = r
        elif self.outerBox:
            pass
        else:
            self.outerBox = Rect(self.page.rect)

        if all([left_margin, top_margin, right_margin, bottom_margin]):
            # Add line items to combobox
            lines = [left_rect, top_rect, right_rect, bottom_rect]
            labels = ["Left", "Top", "Right", "Bottom"]
            for n, line in enumerate(lines):
                index, drawing = line
                r = Rect(drawing["rect"])
                w = r.width
                h = r.height
                self.rectListCandidates.addItem(f"{labels[n]} Line Rect drawing_index={index} - {r}, Width - {w}, Height - {h}, Area - {r.get_area()}", userData=drawing)
        
    def updatePageList(self):
        self.pageList.clear()
        for n in range(self.doc.page_count):
            page = self.doc[n]
            mediabox = page.mediabox_size
            cropbox = page.cropbox
            rotation = page.rotation
            area = page.rect.get_area()
            self.pageList.addItem(f"Page {n+1}, Cropbox {cropbox}, Mediabox {mediabox}, Rotation {rotation}, Ares {area}", userData=n)

    def onPageChanged(self, event=None):
        page_num = self.pageList.currentData(Qt.ItemDataRole.UserRole)
        if page_num is None:
            return
        self.setPage(page_num)

    def onRectListChanged(self, event=None):
        drawing = self.rectList.currentData(Qt.ItemDataRole.UserRole)
        if not drawing:
            return
        rect = drawing["rect"]
        doc = fitz.open()
        doc.insert_pdf(self.doc, self.page_num)
        self.page = doc[0]
        if self.chkRemovePageRotation.isChecked():
            self.page.remove_rotation()
        self.page.draw_rect(rect, color = (0, 1, 0), width = 2)
        rgb = self.page.get_pixmap()
        pil_image = Image.open(io.BytesIO(rgb.tobytes()))
        self.pixmap = pil_image.toqpixmap()
        self.label.setPixmap(pil_image.toqpixmap().scaled(self.label.width(), 
                                                          self.label.height(), 
                                                          Qt.AspectRatioMode.KeepAspectRatio))

    def onRectListCandidateChanged(self, event=None):
        drawing = self.rectListCandidates.currentData(Qt.ItemDataRole.UserRole)
        if not drawing:
            return
        rect = drawing["rect"]
        doc = fitz.open()
        doc.insert_pdf(self.doc, self.page_num)
        self.page = doc[0]
        if self.chkRemovePageRotation.isChecked():
            self.page.remove_rotation()
        self.page.draw_rect(rect, color = (0, 1, 0), width = 2)
        rgb = self.page.get_pixmap()
        pil_image = Image.open(io.BytesIO(rgb.tobytes()))
        self.pixmap = pil_image.toqpixmap()
        self.label.setPixmap(pil_image.toqpixmap().scaled(self.label.width(), 
                                                          self.label.height(), 
                                                          Qt.AspectRatioMode.KeepAspectRatio))
    
    def resizeEvent(self, event: QResizeEvent) -> None:
        r = super().resizeEvent(event)
        try:
            self.label.setPixmap(self.pixmap.scaled(self.label.width(), self.label.height(), Qt.AspectRatioMode.KeepAspectRatio))
        except:
            pass
        return r

    def get_finish_params(self, path, color=None, fill_opacity=None):
        from fitz.utils import getColor
        if color:
            c = getColor(color)
        else:
            c = path.get("color")
        
        return {
            "even_odd": True if path.get("even_odd") is None else path["even_odd"],
            "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
            # "fill_opacity": fill_opacity if path.get("fill_opacity") is None else path["fill_opacity"],
            # "fill": path["fill"] if path.get("fill") is not None else 0, # None
            "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),  # Set fill_opacity to 0 if no fill
            "fill": path.get("fill", None),  # Set fill to None if no fill
            "color": c if path.get("color") is not None else None,
            "dashes": path["dashes"] if path.get("dashes") is not None else None,
            "closePath": False if path.get("closePath") is None else path["closePath"],
            "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
            "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,  # Default to 0 if None or empty
            "width": path["width"] if path.get("width") is not None else None
        }

    def showAllDrawings(self):
        doc = fitz.open()
        doc.insert_pdf(self.doc, self.page_num)
        self.page = page = doc[0]
        self.c_drawings = page.get_cdrawings(extended=False)

        print(self.page.transformation_matrix)

        for n, drawing in enumerate(self.c_drawings):
            shape = self.page.new_shape()
            for item in drawing["items"]:
                if self.chkLine.isChecked() and item[0] == "l":  # line
                    shape.draw_line(item[1], item[2])
                elif self.chkRe.isChecked() and item[0] == "re":  # rectangle
                    shape.draw_rect(item[1])
                elif self.chkQuad.isChecked() and  item[0] == "qu":  # quad
                    shape.draw_quad(item[1])
                elif self.chkBezier.isChecked() and  item[0] == "c":  # curve
                    shape.draw_bezier(item[1], item[2], item[3], item[4])
                else:
                    continue
                # Call shape.finish() with only the parameters that are valid
                shape.finish(**self.get_finish_params(drawing, color="green"))
                shape.commit()

        if self.chkRect.isChecked():
            for index in range(self.rectList.count()):
                drawing = self.rectList.itemData(index, Qt.ItemDataRole.UserRole)
                rect = drawing["rect"]
                self.page.draw_rect(rect, color = (0, 0, 1), width = 2)

        rgb = self.page.get_pixmap()
        pil_image = Image.open(io.BytesIO(rgb.tobytes()))
        self.pixmap = pil_image.toqpixmap()
        self.label.setPixmap(self.pixmap.scaled(self.label.width(), self.label.height(), Qt.AspectRatioMode.KeepAspectRatio))
    
    def detectOuterbox(self):
        """Largest Rect"""
        if not self.outerBox:
            return

        doc = fitz.open()
        doc.insert_pdf(self.doc, self.page_num)
        self.page = page = doc[0]
        if self.chkRemovePageRotation.isChecked():
            self.page.remove_rotation()
        
        self.page.draw_rect(self.outerBox, color = (0, 0, 1), width = 2)

        rgb = self.page.get_pixmap()
        pil_image = Image.open(io.BytesIO(rgb.tobytes()))
        self.pixmap = pil_image.toqpixmap()
        self.label.setPixmap(self.pixmap.scaled(self.label.width(), self.label.height(), Qt.AspectRatioMode.KeepAspectRatio))

    def removePageRotationChecked(self, checked):
        self.setPage(self.page_num)


if __name__ == "__main__":
    app = QApplication()
    w = MainWindow()
    app.exec()

