from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSizePolicy,
                               QLabel, QPushButton, QTextEdit, QLineEdit)
from PySide6.QtCore import QSize
from src.views.supportticket import mailto
from __version__ import version, build_date

SUPPORT_ADDR = "<EMAIL>"


class ReportIssueDialog(QWidget):

    def __init__(self, parent) -> None:
        super().__init__()

        self.setObjectName("popup")

        self.setLayout(QVBoxLayout())

        # self.layout().setSpacing(0)
        # self.layout().setContentsMargins(0, 0, 0, 0)
        label = QLabel("Report an issue", self)
        label.setObjectName("titleLabel")
        label.setContentsMargins(4, 4, 4, 4)
        self.layout().addWidget(label)

        label = QLabel("Title *", self)
        label.setObjectName("headerLabel")
        label.setContentsMargins(4, 6, 4, 6)
        self.layout().addWidget(label)

        self.title = QLineEdit(self)
        self.title.setPlaceholderText("Subject title for the issue")
        self.title.textChanged.connect(self.onTextChanged)
        self.layout().addWidget(self.title)

        label = QLabel("Description *", self)
        label.setContentsMargins(4, 6, 4, 6)
        label.setObjectName("headerLabel")
        self.layout().addWidget(label)

        self.description = QTextEdit(self)
        helpHint = [
            "What were you working on?",
            "Steps to reproduce the issue?",
            "What was the expected result?",
            "What was the actual result?",
        ]
        helpHint = "Please provide a detailed description of the issue you are experiencing..."
        # self.description.setPlaceholderText("\n".join(helpHint))
        self.description.setPlaceholderText(helpHint)
        self.description.textChanged.connect(self.onTextChanged)
        self.description.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addWidget(self.description)

        # label = QLabel("Please provide a title and a description of the issue you are experiencing")
        # label.setContentsMargins(4, 6, 4, 6)
        # self.layout().addWidget(label)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())

        label = QWidget()
        label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        hbox.layout().addWidget(label)

        self.pbCancel = QPushButton("Cancel")
        self.pbCancel.setFixedWidth(128)
        self.pbCancel.setMinimumHeight(34)
        self.pbCancel.setContentsMargins(4, 4, 4, 4)

        self.pbSubmit = QPushButton("Submit")
        self.pbSubmit.setFixedWidth(128)
        self.pbSubmit.setMinimumHeight(34)
        self.pbSubmit.setContentsMargins(4, 4, 4, 4)
        self.pbSubmit.setEnabled(False)

        hbox.layout().addWidget(self.pbCancel)
        hbox.layout().addWidget(self.pbSubmit)

        self.layout().addWidget(hbox)
        hbox.setFixedHeight(44)

        self.pbCancel.clicked.connect(self.close)
        self.pbSubmit.clicked.connect(self.onSubmit)

        self.show()
        self.setMinimumSize(QSize(600, 700))
        self.setWindowTitle("Report Issue")
        self.setWindowIcon(self.topLevelWidget().windowIcon())

    def onTextChanged(self, event=None):
        valid = bool(self.description.toPlainText()) and bool(self.title.text())
        self.pbSubmit.setEnabled(valid)

    def onSubmit(self, event):
        subject = f"Issue: {self.title.text()}"
        body = "Description\n\n"
        body += self.description.toPlainText()
        body += "\n\n\n"
        body += f"ATEM Version: {version}"
        mailto(address=SUPPORT_ADDR,
               subject=subject,
               body=body)