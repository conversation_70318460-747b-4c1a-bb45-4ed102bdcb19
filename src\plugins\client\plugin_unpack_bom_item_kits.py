"""

Context:

Enerfab Test Drawings

S:\Shared Folders\ATEM\Client Work Space\Enerfab\Enerfab 0000

Example, the BOM combines multiple items into one Pos row

pdf_page pos quantity size sch_class ident material_description
2       7       1       8       BU-1Z-FOL-00064 8 - .75"x5 Std Blt, B18.31.2, A193 B7 1 - Insulating Gskt Set, B16.21 , CL150 , RF , Per Mech Const Specification , 0.125" Thk 16 - Hvy Hex Nut, B18.2.2, A194 Gr 2H

The above shows three items packed into 1 quantity on pos 7. The BU-.... is extracted as the ident name


These need to be separated and info extracted for each:

pdf_page pos quantity size sch_class ident material_description
2       7       8       8       BU-1Z-FOL-00064 8 - .75"x5 Std Blt, B18.31.2, A193 B7 1 - Insulating Gskt Set, B16.21 , CL150 , RF , Per Mech Const Specification , 0.125" Thk 16 - Hvy Hex Nut, B18.2.2, A194 Gr 2H
2       7       1       8       BU-1Z-FOL-00064 8 - .75"x5 Std Blt, B18.31.2, A193 B7 1 - Insulating Gskt Set, B16.21 , CL150 , RF , Per Mech Const Specification , 0.125" Thk 16 - Hvy Hex Nut, B18.2.2, A194 Gr 2H
2       7       16      8       BU-1Z-FOL-00064 8 - .75"x5 Std Blt, B18.31.2, A193 B7 1 - Insulating Gskt Set, B16.21 , CL150 , RF , Per Mech Const Specification , 0.125" Thk 16 - Hvy Hex Nut, B18.2.2, A194 Gr 2H

Actions -

"""
import re
from tkinter import N
import pandas as pd


def plugin_unpack_bom_item_kits(input_file: str, save_file: str = "debug/bom_unpacked_kits.xlsx", keep_original_rows_debug: bool = False):
    f"""Unpacks BOM item kits from a given input file and saves the result to a specified save file.

    * See {__file__} header for more context

    Args:
        input_file (str): Input file path containing the BOM data.
        save_file (str, optional): Save file path for the unpacked BOM data. Defaults to None.

    Returns:
        str: Result message indicating the success or failure of the operation.
"""
    df = pd.read_excel(input_file)

    res = {}

    pattern = r"^BU-1Z-FOL-\d+" # Finds kits

    new_df = []
    for index, row in df.iterrows():
        material_description = row["material_description"]
        print(index, material_description)
        print(df.iloc[index]["material_description"], "test")

        pdf_page = row["pdf_page"]
        pos = row["pos"]

        row_dict = row.to_dict()

        material_description = material_description
        match = re.match(pattern, material_description)
        ident = ""
        if not match:
            # None found leave as normal
            new_df.append(row_dict)
            continue

        # Save original for debug
        if keep_original_rows_debug:
            row_dict["kit"] = True
            new_df.append(row_dict)

        ident = match.group().strip()
        print("Match found:", match.group())

        # Match: number + dash + space, followed by as much non-greedy content as possible
        # Pattern to split into items
        pattern_split = r'(\d+\s*-\s*.*?)(?=\b\d+\s*-\s*|$)'

        material_description = material_description[len(ident):].strip()
        matches = re.findall(pattern_split, material_description)

        # Print results
        for i, item in enumerate(matches, 1):
            # Remove leading/trailing dash and strip whitespace
            cleaned = item.strip().strip('-').strip()
            new_quantity, new_material_description = cleaned.split("-")
            new_row = row_dict.copy()
            new_row["quantity"] = new_quantity.strip()
            new_row["ident"] = ident
            new_row["material_description"] = new_material_description.strip()

            if "Std Blt" in new_material_description:
                new_size = new_material_description.split("Std Blt")[0].strip()
                new_row["size"] = new_size.strip()
                new_row["extracted_size"] = True

            new_row["kit"] = True
            new_row["unpacked_item"] = True
            new_df.append(new_row)

    new_df = pd.DataFrame(new_df)
    new_df.to_excel(save_file, index=False)

    res["save_file"] = str(save_file)
    return res
