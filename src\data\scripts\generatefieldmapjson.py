"""
This script generates a template field map and placeholde for specifying tags
"""
from src.atom.dbManager import DatabaseManager
import json
from os import path, makedirs
import re


data: dict = {
    "fields": {},
    "tags": {
        "size": ["nps", "npd"]
    },
}

def generate_display_name(field: str):
    try:
        """ Best attempt to convert to a friendly display name """
        if field.isupper(): # e.g. SPEC, BOM, etc.
            return field
        res = re.sub("([A-Z]+)", r"_\1", field).title()
        res = res.replace("_", " ") 
        res.find
        res = re.sub(" +", " ", res)  # Remove multiple spaces
        res = res.lstrip()  # Remove leading spaces
        return res
    except Exception as e:
        print(f"Error generating fields: {e}")
        return "error"

db = DatabaseManager()
try:
    for field in db.get_sorted_fields():
        data["fields"][field] = {}
        data["fields"][field]["default"] = field
        data["fields"][field]["display"] = generate_display_name(field)
except Exception as e:
        print(f"Error generating fields: {e}")
try:
    def generate(file):
        with open(file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print("Generated field map json template", file)
except Exception as e:
        print(f"Error generating fields: {e}")



if __name__ == "__main__":
    generated = path.join(path.dirname(__file__), "generated")
    makedirs(generated, exist_ok=True)
    file = path.join(generated, "fieldmaptemplate.json")
    generate(file)
