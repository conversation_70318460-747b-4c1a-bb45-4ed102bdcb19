"""
Test Script for LangGraph BOM Classification System

This script tests the foundational system components to ensure everything
is working correctly before full integration.
"""

import asyncio
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported successfully"""
    
    print("Testing Module Imports")
    print("=" * 40)
    
    try:
        import state_models
        print("✓ state_models imported successfully")
    except Exception as e:
        print(f"❌ state_models import failed: {e}")
        return False
    
    try:
        import category_intelligence
        print("✓ category_intelligence imported successfully")
    except Exception as e:
        print(f"❌ category_intelligence import failed: {e}")
        return False
    
    try:
        import langgraph_prompts
        print("✓ langgraph_prompts imported successfully")
    except Exception as e:
        print(f"❌ langgraph_prompts import failed: {e}")
        return False
    
    try:
        import classification_nodes
        print("✓ classification_nodes imported successfully")
    except Exception as e:
        print(f"❌ classification_nodes import failed: {e}")
        return False
    
    try:
        import integration_layer
        print("✓ integration_layer imported successfully")
    except Exception as e:
        print(f"❌ integration_layer import failed: {e}")
        return False
    
    try:
        import langgraph_main
        print("✓ langgraph_main imported successfully")
    except Exception as e:
        print(f"❌ langgraph_main import failed: {e}")
        return False
    
    return True


def test_state_models():
    """Test state model creation and manipulation"""
    
    print("\nTesting State Models")
    print("=" * 40)
    
    try:
        from state_models import create_initial_state, update_state_metadata, WorkflowResult
        
        # Test initial state creation
        state = create_initial_state(
            item_id="test_001",
            material_description="Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            original_classification={"rfq_scope": "Pipe"},
            debug_mode=True
        )
        
        print("✓ Initial state created")
        print(f"  Item ID: {state['item_id']}")
        print(f"  Description: {state['material_description']}")
        print(f"  Debug mode: {state['debug_mode']}")
        
        # Test state update
        updated_state = update_state_metadata(
            state,
            stage_name="test_stage",
            processing_time=1.5,
            model_calls=1,
            tokens_used=150
        )
        
        print("✓ State metadata updated")
        print(f"  Processing time: {updated_state['processing_time']}s")
        print(f"  Workflow path: {updated_state['workflow_path']}")
        
        # Test WorkflowResult
        result = WorkflowResult(
            item_id="test_001",
            status="ok",
            processing_time=2.1,
            model_calls=2
        )
        
        print("✓ WorkflowResult created")
        print(f"  Status: {result.status}")
        print(f"  Processing time: {result.processing_time}s")
        
        return True
        
    except Exception as e:
        print(f"❌ State models test failed: {e}")
        return False


def test_category_intelligence():
    """Test category intelligence functions"""
    
    print("\nTesting Category Intelligence")
    print("=" * 40)
    
    try:
        from category_intelligence import lookup_component, is_vendor_code, resolve_abbreviations
        
        # Test component lookup
        test_descriptions = [
            "Pipe Nipple 2\" SCH 40",
            "90 LR Elbow 4\"",
            "Ball Valve 3\"",
            "VENDOR ABC123"
        ]
        
        for desc in test_descriptions:
            component_info = lookup_component(desc)
            vendor_check = is_vendor_code(desc)
            
            if component_info:
                print(f"✓ '{desc}' → {component_info['category']}: {component_info['full_name']}")
            elif vendor_check:
                print(f"✓ '{desc}' → Vendor Code detected")
            else:
                print(f"✓ '{desc}' → No match (expected for some cases)")
        
        # Test abbreviation resolution
        resolved = resolve_abbreviations("WN Flange RF", "flanges")
        print(f"✓ Abbreviation resolution: 'WN Flange RF' → '{resolved}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Category intelligence test failed: {e}")
        return False


def test_prompts():
    """Test prompt generation"""
    
    print("\nTesting Prompt Generation")
    print("=" * 40)
    
    try:
        from langgraph_prompts import (
            build_material_analysis_prompt,
            build_fitting_classification_prompt
        )
        
        # Test material analysis prompt
        analysis_prompt = build_material_analysis_prompt("Pipe Nipple 2\" SCH 40 ASTM A106 BE")
        print(f"✓ Material analysis prompt generated ({len(analysis_prompt)} chars)")
        
        # Test fitting classification prompt
        fitting_prompt = build_fitting_classification_prompt(
            "Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            {"astm": "A106", "size": "2", "schedule": "40"},
            {}
        )
        print(f"✓ Fitting classification prompt generated ({len(fitting_prompt)} chars)")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        return False


async def test_classification_nodes():
    """Test individual classification nodes"""
    
    print("\nTesting Classification Nodes")
    print("=" * 40)
    
    try:
        from classification_nodes import material_analysis_node, fitting_classification_node
        from state_models import create_initial_state
        
        # Create test state
        state = create_initial_state(
            item_id="test_001",
            material_description="Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            original_classification={},
            debug_mode=False  # Reduce output for testing
        )
        
        # Test material analysis node
        state = await material_analysis_node(state)
        print(f"✓ Material analysis completed: {state['processing_path']}")
        
        # Test fitting classification if routed to fitting
        if state['processing_path'] == 'fitting':
            state = await fitting_classification_node(state)
            print(f"✓ Fitting classification completed")
            print(f"  Classifications: {len(state.get('field_classifications', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Classification nodes test failed: {e}")
        return False


async def test_workflow():
    """Test complete workflow"""
    
    print("\nTesting Complete Workflow")
    print("=" * 40)
    
    try:
        from langgraph_main import create_classification_workflow
        from state_models import create_initial_state
        
        # Create workflow
        workflow = create_classification_workflow()
        print("✓ Workflow created")
        
        # Create test state
        state = create_initial_state(
            item_id="test_001",
            material_description="Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            original_classification={},
            debug_mode=False  # Reduce output for testing
        )
        
        # Run workflow
        final_state = await workflow.ainvoke(state)
        print("✓ Workflow executed successfully")
        print(f"  Final stage: {final_state.get('current_stage', 'unknown')}")
        print(f"  Workflow path: {' → '.join(final_state.get('workflow_path', []))}")
        print(f"  Processing time: {final_state.get('processing_time', 0):.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False


async def main():
    """Run all tests"""
    
    print("LangGraph BOM Classification System - Foundation Test")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("State Models", test_state_models),
        ("Category Intelligence", test_category_intelligence),
        ("Prompt Generation", test_prompts),
        ("Classification Nodes", test_classification_nodes),
        ("Complete Workflow", test_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    """Run the test suite"""
    success = asyncio.run(main())

    # Note: sys.exit() commented out for IDE compatibility
    # Uncomment the line below if running from command line and need exit codes
    # sys.exit(0 if success else 1)

    print(f"\nScript completed. Success: {success}")
