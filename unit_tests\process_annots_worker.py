import pandas as pd
import re

def format_value_with_colors(value, stroke_color, font_color, font_name, font_style, font_size):
    formatted_value = value if value else ''
    attributes = []
    if stroke_color:
        attributes.append(f"stroke:{format_color(stroke_color)}")
    if font_color:
        attributes.append(f"font:{format_color(font_color)}")
    if font_name:
        attributes.append(f"font-name:{font_name}")
    if font_style:
        attributes.append(f"font-style:{font_style}")
    if font_size:
        attributes.append(f"font-size:{font_size}")
    if attributes:
        formatted_value += f"-[{','.join(attributes)}]"
    return formatted_value

def format_color(color):
    if isinstance(color, list) and len(color) == 3:
        rgb = [int(c * 255) for c in color]
        return f"rgb({rgb[0]},{rgb[1]},{rgb[2]})"
    else:
        return str(color)

def is_inside(inner_coords, outer_coords):
    ix0, iy0, ix1, iy1 = inner_coords
    ox0, oy0, ox1, oy1 = outer_coords
    return ox0 <= ix0 and ix1 <= ox1 and oy0 <= iy0 and iy1 <= oy1

def safe_literal_eval(coord_str):
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

def process_extracted_annot_data(df, debug_mode=False):
    '''
    Builds 'annotMarkups' dictionary
    '''

    if debug_mode:
        df.to_excel("annot_element_data.xlsx")

    # Filter out both Text and OCR types
    df = df[~df['type'].isin(['Text', 'OCR'])] 

    
    # Separate Text Box annotations
    text_boxes = df[df['type'] == 'Text Box'].copy()
    other_annots = df[df['type'] != 'Text Box'].copy()

    # Safely convert coordinates2 to tuples of floats
    text_boxes['coordinates2'] = text_boxes['coordinates2'].apply(safe_literal_eval)
    other_annots['coordinates2'] = other_annots['coordinates2'].apply(safe_literal_eval)

    # Remove rows with invalid coordinates
    text_boxes = text_boxes.dropna(subset=['coordinates2'])
    other_annots = other_annots.dropna(subset=['coordinates2'])

    # Find parent annotations for each Text Box
    for idx, text_box in text_boxes.iterrows():
        for other_idx, other_annot in other_annots.iterrows():
            if (text_box['sys_path'] == other_annot['sys_path'] and
                text_box['pdf_page'] == other_annot['pdf_page'] and
                is_inside(text_box['coordinates2'], other_annot['coordinates2'])):
                # Assign the text box value to the parent annotation
                other_annots.at[other_idx, 'value'] = text_box['value']
                # Merge properties
                other_annots.at[other_idx, 'value'] = text_box['value']
                other_annots.at[other_idx, 'font_color'] = text_box['font_color']
                other_annots.at[other_idx, 'Font'] = text_box['Font']
                other_annots.at[other_idx, 'FontStyle'] = text_box['FontStyle']
                other_annots.at[other_idx, 'FontSize'] = text_box['FontSize']

    # Process the annotations
    annotation_data = {}

    for annotation_type in other_annots['type'].unique():

        if pd.isna(annotation_type):  # Skip NaN annotation types
            continue

        temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color', 'Font', 'FontStyle', 'FontSize']].copy()
        
        # Only process if we have data
        if not temp_df.empty:
            formatted_values = []
            grouped = temp_df.groupby(['sys_path', 'pdf_page'])
        
            for name, group in grouped:
                sys_path, pdf_page = name
                values = [format_value_with_colors(v, sc, fc, fn, fs, fsz) 
                         for v, sc, fc, fn, fs, fsz in zip(group['value'], 
                                                          group['stroke_color'], 
                                                          group['font_color'],
                                                          group['Font'],
                                                          group['FontStyle'],
                                                          group['FontSize'])]
                formatted_values.append({
                    'sys_path': sys_path,
                    'pdf_page': pdf_page,
                    'value': values
                })
            
            if formatted_values:  # Only create DataFrame if we have values
                annotation_data[annotation_type] = pd.DataFrame(formatted_values)
    
    # explicitly set the dtype of annotMarkups to string when creating annot_types_df to avoid deprecation
    annot_types_df = other_annots[['sys_path', 'pdf_page', 'annotMarkups']].drop_duplicates().reset_index(drop=True)
    annot_types_df['annotMarkups'] = annot_types_df['annotMarkups'].astype('str')  # Add this line

    for index, row in annot_types_df.iterrows():
        annotations_for_row = {}
        for annotation_type, annotations_df in annotation_data.items():
            if not annotations_df.empty:  # Check if DataFrame has data
                matching_annotations = annotations_df[
                    (annotations_df['sys_path'] == row['sys_path']) & 
                    (annotations_df['pdf_page'] == row['pdf_page'])
                ]['value'].tolist()

                if matching_annotations:
                    if matching_annotations and isinstance(matching_annotations[0], list):
                        matching_annotations = [item for sublist in matching_annotations for item in sublist]
            
                    annotations_for_row[annotation_type] = matching_annotations

        annot_types_df.at[index, 'annotMarkups'] = str(annotations_for_row)
        
    return annot_types_df

if __name__ == "__main__":
    df = pd.read_excel('annot_element_data.xlsx')

    annot_df = process_extracted_annot_data(df)

    print(f"\n\n DF Length: {len(annot_df)}")

    fpath = "_unit_test_annot_df.xlsx"
    annot_df.to_excel(fpath)

    print(f"Exported to {fpath}")



def process_extracted_annot_data_backup(df): # 12.4.24 - Corrects error in groupby
    '''
    Builds 'annotMarkups' dictionary
    '''

    # if debug_mode:
    df.to_excel("annot_element_data.xlsx")

    # Filter for Annotation types
    df = df[df['type'] != 'Text']    
    
    # Separate Text Box annotations
    text_boxes = df[df['type'] == 'Text Box'].copy()
    other_annots = df[df['type'] != 'Text Box'].copy()

    # Safely convert coordinates2 to tuples of floats
    text_boxes['coordinates2'] = text_boxes['coordinates2'].apply(safe_literal_eval)
    other_annots['coordinates2'] = other_annots['coordinates2'].apply(safe_literal_eval)

    # Remove rows with invalid coordinates
    text_boxes = text_boxes.dropna(subset=['coordinates2'])
    other_annots = other_annots.dropna(subset=['coordinates2'])

    # Find parent annotations for each Text Box
    for idx, text_box in text_boxes.iterrows():
        for other_idx, other_annot in other_annots.iterrows():
            if (text_box['sys_path'] == other_annot['sys_path'] and
                text_box['pdf_page'] == other_annot['pdf_page'] and
                is_inside(text_box['coordinates2'], other_annot['coordinates2'])):
                # Assign the text box value to the parent annotation
                other_annots.at[other_idx, 'value'] = text_box['value']
                # Merge properties
                other_annots.at[other_idx, 'value'] = text_box['value']
                other_annots.at[other_idx, 'font_color'] = text_box['font_color']
                other_annots.at[other_idx, 'Font'] = text_box['Font']
                other_annots.at[other_idx, 'FontStyle'] = text_box['FontStyle']
                other_annots.at[other_idx, 'FontSize'] = text_box['FontSize']

    # # Process the annotations
    # annotation_data = {}
    # for annotation_type in other_annots['type'].unique():
    #     temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color']].copy()
        
    #     annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page']).apply(
    #         lambda x: [format_value_with_colors(v, sc, fc) for v, sc, fc in zip(x['value'], x['stroke_color'], x['font_color'])]
    #     ).reset_index(name='value')

    # annot_types_df = other_annots[['sys_path', 'pdf_page', 'annotMarkups']].drop_duplicates().reset_index(drop=True)

    # Process the annotations
    print(f"\n--------------------\nANNOT TYPES: {other_annots}\n--------------------\n")
    annotation_data = {}
    for annotation_type in other_annots['type'].unique():
        temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color', 'Font', 'FontStyle', 'FontSize']].copy()
        

        annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page']).apply(
            lambda x: [format_value_with_colors(v, sc, fc, fn, fs, fsz) 
                       for v, sc, fc, fn, fs, fsz in zip(x['value'], x['stroke_color'], x['font_color'], x['Font'], x['FontStyle'], x['FontSize'])]
        ).reset_index(drop=True)  # Drop the old index completely

        # annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page']).apply(
        #     lambda x: [format_value_with_colors(v, sc, fc, fn, fs, fsz) 
        #                for v, sc, fc, fn, fs, fsz in zip(x['value'], x['stroke_color'], x['font_color'], x['Font'], x['FontStyle'], x['FontSize'])]
        # ).to_frame('value').reset_index()

        # annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page']).apply(
        #     lambda x: [format_value_with_colors(v, sc, fc, fn, fs, fsz) 
        #                for v, sc, fc, fn, fs, fsz in zip(x['value'], x['stroke_color'], x['font_color'], x['Font'], x['FontStyle'], x['FontSize'])]
        # ).reset_index(name='value')

    annot_types_df = other_annots[['sys_path', 'pdf_page', 'annotMarkups']].drop_duplicates().reset_index(drop=True)

    for index, row in annot_types_df.iterrows():
        annotations_for_row = {}
        for annotation_type, annotations_df in annotation_data.items():
            matching_annotations = annotations_df[
                (annotations_df['sys_path'] == row['sys_path']) & 
                (annotations_df['pdf_page'] == row['pdf_page'])
            ]['value'].tolist()

            if matching_annotations:
                if matching_annotations and isinstance(matching_annotations[0], list):
                    matching_annotations = [item for sublist in matching_annotations for item in sublist]
        
                annotations_for_row[annotation_type] = matching_annotations

        annot_types_df.at[index, 'annotMarkups'] = str(annotations_for_row)
        
    return annot_types_df


