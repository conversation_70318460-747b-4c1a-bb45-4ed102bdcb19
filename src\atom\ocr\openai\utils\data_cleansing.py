
import pandas as pd

def normalize_text(text):
    # Ensure the input is a string
    text = str(text)

    replacements = {
        '\u201C': '"',  # Left double quotation mark to ASCII double quote
        '\u201D': '"',  # Right double quotation mark to ASCII double quote
        '\u2018': "'",  # Left single quotation mark to ASCII single quote
        '\u2019': "'",  # Right single quotation mark to ASCII single quote
        "''": '"',      # Double apostrophe to ASCII double quote
        '\u2033': '"',  # Prime (double quote in some contexts) to ASCII double quote
        '\u2032': "'",  # Prime (single quote in some contexts) to ASCII single quote
        '\u2013': '-',  # En dash to ASCII hyphen
        '\u2014': '-',  # Em dash to ASCII hyphen
        '\u00A0': ' ',  # Non-breaking space
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot to hyphen
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }

    # Replace all specified characters
    for original, replacement in replacements.items():
        try:
            text = text.replace(original, replacement)
        except Exception as e:
            print(f"Error replacing unicode value '{original}' with '{replacement}'. Error: {e}")
    return text

def transform_dataframe(df):
    # Clean up the DataFrame
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df = df.replace('', pd.NA).dropna(how='all')

    # Apply normalization to text fields
    text_columns = df.select_dtypes(include=['object']).columns
    for col in text_columns:
        df[col] = df[col].apply(normalize_text)

    # Ensure all expected columns are present
    expected_columns = ["ITEM", "QUANTITY", "SIZE", "RATIG", "DESCRIPTION", "page_number"]
    for col in expected_columns:
        if col not in df.columns:
            df[col] = ""

    # Sort by page number and row number
    df['NO.'] = pd.to_numeric(df['NO.'], errors='coerce')
    df = df.sort_values(['page_number', 'NO.']).reset_index(drop=True)

    return df


