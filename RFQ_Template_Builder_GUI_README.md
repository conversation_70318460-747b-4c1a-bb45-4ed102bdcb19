# RFQ Template Builder - GUI Interface

## Overview

The RFQ Template Builder now includes a modern PySide6 GUI interface that makes it easy to configure and run your workflow with file pickers, progress tracking, and configurable parameters.

## Quick Start

### Option 1: Direct GUI Launch (Recommended)
```bash
python rfq_template_builder_gui.py
```

### Option 2: VS Code Launch Profiles
1. Open VS Code in the project directory
2. Press `F5` or go to Run and Debug
3. Select "RFQ Template Builder - GUI" from the dropdown
4. Click the green play button

### Option 3: Launcher Script
```bash
# Launch GUI (default)
python launch_rfq_builder.py

# Or explicitly
python launch_rfq_builder.py --gui

# CLI mode
python launch_rfq_builder.py --cli
```

## GUI Features

### 📁 **Configuration Tab**
- **File Pickers**: Browse for input files and output directories
- **Step Selection**: Enable/disable each workflow step with checkboxes
- **Step 1 Options**: Choose what to combine (General, BOM, Spec, RFQ)
- **Step 4 Options**: Configure RFQ template settings

### 📂 **Files to Combine Tab**
- **Multiple File Support**: Add multiple Excel files for combining
- **File Management**: Add, remove, or clear files from the combine list
- **Smart Handling**: 
  - Multiple files → Full combining workflow
  - Single file → Column renaming and preprocessing

### 📊 **Progress Tab**
- **Real-time Progress**: See what's happening at each step
- **Timestamped Logs**: Track when each operation occurs
- **Step Status**: Visual indicators for success/failure
- **Progress Bar**: Overall workflow progress

### 💾 **Configuration Management**
- **Save Config**: Save your settings to a JSON file
- **Load Config**: Load previously saved configurations
- **Default Settings**: Sensible defaults for common workflows

## Workflow Steps

### Step 1: Create Combined Workbook ✅
- **Single File Mode**: Renames `ansme_ansi` → `technical_standard`
- **Multiple File Mode**: Combines files using `misc_help.py` logic
- **Sheet Selection**: Choose which sheet types to process
- **Output**: `Stage1_Combined_YYYYMMDD_HHMMSS.xlsx` or `Stage1_Preprocessed_YYYYMMDD_HHMMSS.xlsx`

### Step 2: Material Database Check 🔍
- **Database Validation**: Checks materials against verified database
- **Graceful Fallback**: Skips if database unavailable
- **Output**: `Stage2_Material_Check.xlsx`

### Step 3: Normalize Descriptions 🔧
- **Property Extraction**: ASTM, schedule, rating, ends, etc.
- **Metadata Generation**: Creates structured tags
- **Valve Type Normalization**: Classifies valve types based on end types
- **Output**: `Stage3_Normalized.xlsx`

### Step 4: Create RFQ Template 📋
- **47-Column Template**: Complete RFQ structure
- **Validation Dropdowns**: Classification assistance
- **Deduplication**: Removes duplicate material descriptions
- **Output**: `RFQ_Template_Final_YYYYMMDD_HHMMSS.xlsx`

## Key Improvements

### 🔧 **Column Renaming**
- Automatically renames `ansme_ansi` to `technical_standard` before processing
- Handles both single files and multiple file combinations
- Preserves data integrity throughout the workflow

### 📁 **File Management**
- **File Pickers**: No more manual path editing
- **Multiple File Support**: Easy selection of files to combine
- **Path Validation**: Checks file existence before processing

### 🎛️ **Configurable Parameters**
- **Step Flags**: Enable/disable any step
- **Combine Options**: Choose what sheet types to process
- **Template Options**: Validation, deduplication settings
- **Error Handling**: Continue on error option

### 🔄 **Background Processing**
- **Non-blocking UI**: Interface remains responsive during processing
- **Progress Updates**: Real-time status information
- **Error Handling**: Graceful error reporting and recovery

## Configuration Options

### Input/Output
- **Input Excel File**: Primary data source
- **Output Directory**: Where all results are saved
- **Files to Combine**: Additional files for Step 1 combining

### Workflow Steps
- **Step 1**: Create Combined Workbook (handles column renaming)
- **Step 2**: Material Database Check (optional)
- **Step 3**: Normalize Descriptions (extracts metadata)
- **Step 4**: Create RFQ Template (final output)

### Step 1 Options
- **Combine General**: Include General sheet data
- **Combine BOM**: Include BOM sheet data ✅ (default)
- **Combine Spec**: Include Spec sheet data
- **Combine RFQ**: Include RFQ sheet data ✅ (default)

### Step 4 Options
- **Enable Validation**: Add dropdown validation to template ✅ (default)
- **Deduplicate Materials**: Remove duplicate descriptions ✅ (default)
- **Continue on Error**: Keep processing if a step fails ✅ (default)

## Usage Scenarios

### 🔄 **Full Workflow** (Recommended)
1. Select input file or multiple files to combine
2. Enable all steps (1-4)
3. Configure combine options (BOM + RFQ typically)
4. Run workflow
5. Get final RFQ template with validation

### 📊 **Data Processing Only**
1. Select input file
2. Enable Steps 2-3 (skip combining)
3. Run workflow
4. Get normalized data without template

### 📋 **Template Creation Only**
1. Select normalized data file
2. Enable only Step 4
3. Configure template options
4. Run workflow
5. Get RFQ template from existing data

### 🔗 **File Combining Only**
1. Add multiple files to combine
2. Enable only Step 1
3. Configure combine options
4. Run workflow
5. Get combined workbook

## Error Handling

### 🛡️ **Robust Error Management**
- **Import Fallbacks**: Multiple import paths for modules
- **Database Graceful Failure**: Continues without database if unavailable
- **File Validation**: Checks file existence and permissions
- **Continue on Error**: Option to proceed despite step failures

### 📝 **Detailed Logging**
- **Timestamped Messages**: Track when operations occur
- **Success/Failure Indicators**: Clear visual feedback
- **Error Details**: Specific error messages for troubleshooting

## Installation & Dependencies

### Required Dependencies
All dependencies are already in `requirements.txt`:
- `PySide6` - GUI framework
- `pandas` - Data processing
- `openpyxl` - Excel file reading
- `xlsxwriter` - Excel file writing

### Installation
```bash
# Install all dependencies
pip install -r requirements.txt

# Or install GUI dependencies specifically
pip install PySide6 pandas openpyxl xlsxwriter
```

## Troubleshooting

### GUI Won't Start
- Check PySide6 installation: `pip install PySide6`
- Verify Python version (3.7+)
- Check console for error messages

### Import Errors
- Ensure all referenced modules are in correct locations
- Check that `misc_help.py`, `normalize_description.py`, etc. are available
- Verify Python path includes necessary directories

### File Processing Errors
- Verify input files exist and are readable
- Check that input files contain `material_description` column
- Ensure output directory is writable

### Database Connection Issues
- Step 2 will automatically skip if database unavailable
- Check database configuration if needed
- Use "Continue on Error" option to proceed without database

## Advanced Usage

### Configuration Files
Save and load configurations for repeated workflows:
```bash
# Create sample config
python launch_rfq_builder.py --sample-config

# Run with config file
python launch_rfq_builder.py --config my_config.json
```

### Command Line Interface
For automated/scripted workflows:
```bash
# Run CLI version
python launch_rfq_builder.py --cli

# Or directly
python rfq_template_builder.py
```

### Batch Processing
Use the GUI to configure settings, save config, then use CLI for batch processing multiple projects.

## Support

The GUI interface provides the same functionality as the original workflow but with:
- ✅ Better user experience
- ✅ File picker integration
- ✅ Real-time progress tracking
- ✅ Configuration management
- ✅ Error handling and recovery
- ✅ Automatic column renaming
- ✅ Multiple file support
