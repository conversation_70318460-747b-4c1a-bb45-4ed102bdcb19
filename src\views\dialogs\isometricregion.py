import fitz
import math
import cv2
import time
import ast

from pathlib import Path
import pandas as pd
from PIL import Image
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *

from src.atom.sourcepreprocess import page_to_opencv
from src.views.documentviewonly import DocumentViewOnly
from src.widgets.pandastable import PandasTable
from src.atom.vision import isometric_analysis
from src.utils.convert_roi_payload import convert_roi_payload

DATA_COLUMNS = ["filename", "page", "DetectedText", "Type", "Id", "ParentId", "Confidence", "x0", "y0", "x1", "y1", "polygon"]

ANALYSIS_COLUMNS = []

LINE_NUMBER_COLUMNS = ["page", "lineNumber", "drawingNumber"]

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.

    Note: same function as calculate_angle but supply two co-ords
    instead of line
    """
    x1, y1 = p1
    x2, y2 = p2
    dx = x2 - x1
    dy = y2 - y1
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range


def toQUrl(string) -> str:
    return QUrl.fromLocalFile(string).toString()

def parse_range(astr):
    result=set()
    for part in astr.split(','):
        x=part.split('-')
        result.update(range(int(x[0]),int(x[-1])+1))
    return sorted(result)

def vconcat_resize(img_list, interpolation = cv2.INTER_LINEAR):
    # take maximium width
    w_max = max(img.shape[1] for img in img_list)

    # resizing images
    im_list_resize = [cv2.resize(img,
                      (w_max, int(img.shape[0] * w_max / img.shape[1])),
                                 interpolation = interpolation)
                      for img in img_list]
    # return final image
    return cv2.vconcat(im_list_resize)

def parse_coords_list(coord_str):
    return ast.literal_eval(coord_str)

def process_raw_response(page_num: int,
                         cvImage: cv2.Mat,
                         response: dict,
                         isometricImage: cv2.Mat,
                         isometricRoi: list[float]) -> pd.DataFrame:
    """Adjust y0, y1 for cropped images"""

    height, width, _ = cvImage.shape

    # Cut off point where lineNumber cropped data starts
    isometricHeight, isometricWidth, _ = isometricImage.shape

    records = []
    textDetections = response['TextDetections']
    for text in textDetections:
        data = {
            "page": page_num,
        }
        data.update(dict(text))

        geom = text["Geometry"]
        bbox = geom["BoundingBox"]

        bbox_tl = (bbox["Left"], bbox["Top"])
        bbox_br = (bbox["Left"] + bbox["Width"], bbox["Top"] + bbox["Height"])

        # adjust relative coords to isometric image before mapping back to full
        bbox_tl = (bbox_tl[0] * width, bbox_tl[1] * height)
        bbox_tl = (bbox_tl[0] / isometricWidth, bbox_tl[1] / isometricHeight)

        # adjust relative coords to isometric image before mapping back to full
        bbox_br = (bbox_br[0] * width, bbox_br[1] * height)
        bbox_br = (bbox_br[0] / isometricWidth, bbox_br[1] / isometricHeight)

        bx0, by0 = isometric_analysis.map_point_to_full_size(bbox_tl, isometricRoi)
        bx1, by1 =  isometric_analysis.map_point_to_full_size(bbox_br, isometricRoi)

        data["x0"] = bx0
        data["y0"] = by0
        data["x1"] = bx1
        data["y1"] = by1

        polygon = geom["Polygon"]

        points = []
        for pt in polygon:
            x = pt["X"]
            y = pt["Y"]
            point = (x, y)
            point = (point[0] * width, point[1] * height)
            point = (point[0] / isometricWidth, point[1] / isometricHeight)
            mapped =  isometric_analysis.map_point_to_full_size(point, isometricRoi)
            points.append(mapped)

        data["polygon"] = points
        del data["Geometry"]

        records.append(data)

    df = pd.DataFrame(records)
    df.to_excel("debug.temp.xlsx")
    try:
        lineNumber = df[df["y1"] > 1]["DetectedText"].iloc[0]
    except Exception as e:
        lineNumber = None

    df = df[df["y1"] <= 1] # exclude lineNumber data

    return df, lineNumber

def get_ocr_image(page: fitz.Document, isometricRoi: list[float], lineNumberRoi: list[float]):
    """Return a vertically concatenated image used for OCR

    Returns the cropped images as a list
    """
    zoom = 2
    page_width = page.rect.width
    page_height = page.rect.height
    cvImage = page_to_opencv(page, zoom=zoom)

    images = []

    region = isometricRoi
    x0 = int(region[0] * page_width * zoom)
    y0 = int(region[1] * page_height * zoom)
    x1 = int(region[2] * page_width * zoom)
    y1 = int(region[3] * page_height * zoom)

    isoCrop = cvImage[y0:y1, x0:x1]
    images.append(isoCrop)

    region = lineNumberRoi
    x0 = int(region[0] * page_width * zoom)
    y0 = int(region[1] * page_height * zoom)
    x1 = int(region[2] * page_width * zoom)
    y1 = int(region[3] * page_height * zoom)

    lineNumberCrop = cvImage[y0:y1, x0:x1]
    images.append(lineNumberCrop)

    concatImage = vconcat_resize(images)
    return concatImage, images

class DetectWorker(QObject):

    finished = Signal()
    ocrReceived = Signal(int, object, str)
    error = Signal(object)
    message = Signal(str)
    def __init__(self,
                 doc: fitz.Document,
                 pages: list[int],
                 roiPayload: dict):
        super().__init__()
        self.doc = doc
        self.pages = pages
        self.roiPayload = roiPayload

    def run(self):
        print("OCR pages", self.pages)
        self.message.emit(f"Running OCR. {len(self.pages)} remaining..")
        while self.pages:
            pageNum = self.pages.pop(0)
            try:
                page: fitz.Document = self.doc[pageNum-1]
                group = self.roiPayload["pageToGroup"][pageNum]
                rois = self.roiPayload["groupRois"][group]

                isometricRoi = None
                lineNumberRoi = None
                for roi in rois:
                    if roi["columnName"] == "Isometric Drawing Area":
                        isometricRoi = [
                            roi["relativeX0"],
                            roi["relativeY0"],
                            roi["relativeX1"],
                            roi["relativeY1"],
                        ]
                        continue
                    if roi["columnName"] == "lineNumber":
                        lineNumberRoi = [
                            roi["relativeX0"],
                            roi["relativeY0"],
                            roi["relativeX1"],
                            roi["relativeY1"],
                        ]
                        continue

                cvImage, images = get_ocr_image(page, isometricRoi, lineNumberRoi)

                isometricImage = images[0]

                response = isometric_analysis.run_text_detection_cv(cvImage)
                processed_response, lineNumber = process_raw_response(pageNum, cvImage, response, isometricImage, isometricRoi)

                self.ocrReceived.emit(pageNum, processed_response, lineNumber)
                self.message.emit(f"Page {pageNum} result received. {len(self.pages)} remaining..")

            except Exception as e:
                self.error.emit(f"Prediction error on page {pageNum} - {str(e)}")
                self.message.emit(f"Page {pageNum} result errored. {len(self.pages)} remaining..")

        self.message.emit(f"Finished...")
        time.sleep(2)
        self.finished.emit()

class IsometricRegionPopup(QWidget):

    def __init__(self):
        super().__init__(parent=None)
        self.setObjectName("popup")
        self.setLayout(QVBoxLayout())
        self.setMinimumSize(800, 600)
        self.setWindowTitle("Isometric Region")

        self.doc: fitz.Document = None
        self.page: int = None
        self.pages: int = None
        self.file: str = None
        self.roiPayload: dict = {}

        self.x0: float = None
        self.y0: float = None
        self.x1: float = None
        self.y1: float = None

        self.thread: QThread = None
        self.worker: DetectWorker = None

        self.lineNumberRegion: list[float] = None

        self.df: pd.DataFrame = None

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.leFile = QLineEdit()
        self.leFile.setReadOnly(True)
        hbox.layout().addWidget(self.leFile)

        self.leText = QLineEdit()
        self.leText.setReadOnly(True)
        self.leText.setPlaceholderText("Displays info...")
        hbox.layout().addWidget(self.leText)

        self.pbPrevPage = QPushButton("<")
        self.pbPrevPage.setMinimumHeight(24)
        self.pbPrevPage.setFixedWidth(36)
        hbox.layout().addWidget(self.pbPrevPage)
        self.pbPrevPage.clicked.connect(self.previousPage)

        self.lePage = QLineEdit(" ")
        self.lePage.setObjectName("pager")
        hbox.layout().addWidget(self.lePage)
        self.lePage.setFixedWidth(48)
        self.lePage.returnPressed.connect(self.onChangePage)

        self.lblPages = QLabel("/")
        hbox.layout().addWidget(self.lblPages)

        self.pbNextPage = QPushButton(">")
        self.pbNextPage.setMinimumHeight(24)
        self.pbNextPage.setFixedWidth(36)
        hbox.layout().addWidget(self.pbNextPage)
        self.pbNextPage.clicked.connect(self.nextPage)

        self.lblLineNumber = QLabel()
        hbox.layout().addWidget(self.lblLineNumber)

        hbox = QWidget()
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.leOcrFile = QLineEdit()
        self.leOcrFile.setPlaceholderText("Required - select an OCR save path or load an existing data file...")
        self.leOcrFile.setReadOnly(True)
        hbox.layout().addWidget(self.leOcrFile)

        self.pbLoadOcrData = QPushButton("Load OCR File")
        self.pbLoadOcrData.setToolTip("Loads previously saved OCR data")
        self.pbLoadOcrData.clicked.connect(self.loadOcrData)
        hbox.layout().addWidget(self.pbLoadOcrData)

        hbox = QWidget()
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.lePageRange = QLineEdit()
        self.lePageRange.setPlaceholderText("Choose a page range e.g. '1-10, 3, 4, 23-47'")
        self.lePageRange.setMinimumHeight(36)
        hbox.layout().addWidget(self.lePageRange)

        pbDetectPageRange = QPushButton("Detect Missing Pages Range")
        pbDetectPageRange.setMinimumHeight(36)
        hbox.layout().addWidget(pbDetectPageRange)
        pbDetectPageRange.clicked.connect(self.detectPageRange)

        self.pbRunOcr = QPushButton("Run OCR")
        self.pbRunOcr.setFixedHeight(36)
        self.pbRunOcr.clicked.connect(self.onRunOcr)
        hbox.layout().addWidget(self.pbRunOcr)

        self.tabs = QTabWidget()
        self.layout().addWidget(self.tabs)

        self.splitter = QSplitter()
        self.splitter.setOrientation(Qt.Orientation.Horizontal)

        self.viewer = DocumentViewOnly(self)
        self.viewer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.viewer.hboxTop.hide()

        self.splitter.addWidget(self.viewer)

        vbox = QWidget()
        vbox.setLayout(QVBoxLayout())
        self.splitter.addWidget(vbox)

        self.textInfo = QTextEdit()
        self.textInfo.setPlaceholderText("Actions needed: Run OCR and analysis on page. Set 'Display Analysis Data' ON")
        vbox.layout().addWidget(self.textInfo)

        self.splitter.setSizes([600, 200])

        self.tabs.addTab(self.splitter, "Viewer")

        self.tableOcr: PandasTable = PandasTable()
        self.tableOcr.setDataFrame(pd.DataFrame(columns=DATA_COLUMNS))
        self.tabs.addTab(self.tableOcr, "OCR Data")

        self.tableLineNumbers: PandasTable = PandasTable()
        self.tableLineNumbers.setDataFrame(pd.DataFrame(columns=LINE_NUMBER_COLUMNS))
        self.tabs.addTab(self.tableLineNumbers, "Line Numbers")

        self.tableAnalysis: PandasTable = PandasTable()
        self.tableAnalysis.setDataFrame(pd.DataFrame(columns=DATA_COLUMNS))
        self.tabs.addTab(self.tableAnalysis, "Analysis")

        self.textLog = QTextEdit()
        self.textLog.setPlaceholderText("Log data")
        self.tabs.addTab(self.textLog, "Log / Errors")

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.pbSave = QPushButton("Save/merge OCR Data")
        self.pbSave.clicked.connect(self.onSave)
        hbox.layout().addWidget(self.pbSave)

        # self.pbAnalyze = QPushButton("Run Analysis on Data")
        # self.pbAnalyze.clicked.connect(self.onAnalyze)
        # hbox.layout().addWidget(self.pbAnalyze)

        self.pbDisplayAnalysis = QPushButton("Toggle Analysis Data and Regions")
        self.pbDisplayAnalysis.setCheckable(True)
        self.pbDisplayAnalysis.clicked.connect(self.onDisplayAnalysis)
        hbox.layout().addWidget(self.pbDisplayAnalysis)

        self.viewer.setEnabled(True)

    def setData(self, file: str, rois: dict):

        self.file = file
        self.doc = fitz.open(self.file)
        self.page = 1
        self.pages = self.doc.page_count
        self.leFile.setText(f"{self.file} - Page {self.page}")

        self.lePage.setText(f"{self.page}")
        self.lblPages.setText(f"/{self.pages}")

        self.roiPayload = convert_roi_payload(rois, ignore_bom=True)

        self.setPage(self.page)

    def closeEvent(self, event):
        if QMessageBox.question(self, "Close", "Confirm close?") != QMessageBox.Yes:
            return
        return super().closeEvent(event)

    def setImage(self, cv_image):
        image = Image.fromarray(cv_image)
        data = {
            "id": -1,
            "ok": True,
            "pdf": {
                "originalFilename": self.file,
                "documentName": self.file,
                "document": image.toqpixmap(),
                "page_number": self.page
            }
        }
        self.viewer.onPageReceived(data)

    def getImagesForOcr(self, pages):
        """Return a vertically concatenated image used for OCR"""

        uniqueGroups = set()

        skippedGroups = set()

        # check invalid rois
        for n in pages:
            group = self.roiPayload["pageToGroup"]
            uniqueGroups.add(uniqueGroups)

        for group in uniqueGroups:
            pass

    def checkPageRoisValid(self):
        """Return valid and invalid pages"""
        valid = []
        invalid = []
        invalidGroups = set()

    def setDefaultImage(self):
        doc: fitz.Document = self.doc
        page: fitz.Document = doc[self.page - 1]

        # self.getImageForOcr(self.page)
        zoom = 2
        page_width = page.rect.width
        page_height = page.rect.height
        cvImage = page_to_opencv(page, zoom=zoom)

        warnings = []

        group = self.roiPayload["pageToGroup"][self.page]
        rois = self.roiPayload["groupRois"][group]

        self.isometricRoi = None
        self.lineNumberRoi = None
        for roi in rois:
            if roi["columnName"] == "Isometric Drawing Area":
                self.isometricRoi = [
                    roi["relativeX0"],
                    roi["relativeY0"],
                    roi["relativeX1"],
                    roi["relativeY1"],
                ]
                continue
            if roi["columnName"] == "lineNumber":
                self.lineNumberRoi = [
                    roi["relativeX0"],
                    roi["relativeY0"],
                    roi["relativeX1"],
                    roi["relativeY1"],
                ]
                continue

        images = []
        if not self.isometricRoi:
            warnings.append("Missing isometric ROI")
        else:
            region = self.isometricRoi
            x0 = int(region[0] * page_width * zoom)
            y0 = int(region[1] * page_height * zoom)
            x1 = int(region[2] * page_width * zoom)
            y1 = int(region[3] * page_height * zoom)

            isoCrop = cvImage[y0:y1, x0:x1]
            images.append(isoCrop)

        if not self.lineNumberRoi:
            warnings.append("Missing lineNumber ROI")
        else:
            region = self.lineNumberRoi
            x0 = int(region[0] * page_width * zoom)
            y0 = int(region[1] * page_height * zoom)
            x1 = int(region[2] * page_width * zoom)
            y1 = int(region[3] * page_height * zoom)

            lineNumberCrop = cvImage[y0:y1, x0:x1]
            images.append(lineNumberCrop)

        warning = ""
        if images:
            concatImage = vconcat_resize(images)
            # concatImage = images[0]
            self.setImage(concatImage)
        else:
            warning = "Missing ROIs (OCR will skip this page) - "
            warning = ", ".join(warnings)

        if not warning:
            self.leText.setText("Isometric region and lineNumber region set... OK")
        else:
            warning = "Missing ROIs (OCR will skip this page) - "
            warning = ", ".join(warnings)
            self.leText.setText(warning)

    def loadOcrData(self):
        """Restores previously saved OCR data"""

        path, _ = QFileDialog.getSaveFileName(
            self,
            "Select existing save path or open Document",
            filter="All Supported Files (*.xlsx)",
        )
        if path:
            self.leOcrFile.setText(path)
        else:
            return

        path = self.leOcrFile.text()
        file = Path(path)
        suffix = file.suffix
        if suffix != ".xlsx":
            QMessageBox.information(self, "File must be xlsx", "Cannot read file must be xlsx")
            return

        if self.thread and not self.thread.isFinished():
             QMessageBox.information(self, "Prediction in progress", "Please cancel running prediction before reading data")
             return

        if file.exists():
            res = QMessageBox.question(self, "Read data from selected file?", "Reading from file will replace current OCR Data+lineNumber table. Proceed?")
            if res != QMessageBox.Yes:
                return

            # sheet ocr
            df2 = pd.read_excel(path, sheet_name="ocr")
            df = pd.DataFrame(columns=DATA_COLUMNS)
            df = pd.concat([df, df2], ignore_index=True)[DATA_COLUMNS]
            df["polygon"] = df["polygon"].apply(parse_coords_list)
            self.tableOcr.setDataFrame(df)

            # sheet line numbers
            df2 = pd.read_excel(path, sheet_name="lineNumber")
            df = pd.DataFrame(columns=LINE_NUMBER_COLUMNS)
            df = pd.concat([df, df2], ignore_index=True)[LINE_NUMBER_COLUMNS]
            self.tableLineNumbers.setDataFrame(df)
            QMessageBox.information(self, "Updated", "Table results updated")
        else:
            QMessageBox.information(self, "New output file selected", "A new file is selected as output")
            self.tableOcr.setDataFrame(pd.DataFrame(DATA_COLUMNS))
            self.tableLineNumbers.setDataFrame(pd.DataFrame(LINE_NUMBER_COLUMNS))


    def onRunOcr(self):

        if not self.leOcrFile.text():
            QMessageBox.information(self, "Select/Load OCR first", "'Load OCR file' first")
            return

        pages = []
        pageRange = self.lePageRange.text()
        if not pageRange:
            pages = [n+1 for n in range(len(self.doc))]
        else:
            try:
                pages = parse_range(pageRange)
            except Exception as e:
                QMessageBox.warning(self, "Invalid Page Range", "Invalid Page Range")
                return

        invalid = [p for p in pages if p < 1 or p > len(self.doc)]
        if invalid:
            QMessageBox.warning(self, "Invalid Page Range", f"Page(s) out of range:\n{invalid}")
            return

        res = QMessageBox.question(self,
                                   "Confirm",
                                   f"Proceed run ocr?\n\nPage count - {len(pages)}")
        if res != QMessageBox.Yes:
            return

        self.progress = QProgressDialog("Running OCR", "Cancel", 0, 0, self)
        self.progress.setEnabled(True)
        self.progress.setWindowTitle("OCR")
        self.progress.setModal(False)
        self.progress.show()

        def finished():
            try:
                self.worker.finished.disconnect()
                self.worker.predictionReceived.disconnect()
                self.worker.predictionError.disconnect()
                self.worker.message.disconnect()
                self.worker.stop()
                self.thread.exit()
                self.worker = None
                self.thread.wait()
                self.progress.close()
            except Exception as e:
                self.progress.close()
                self.worker = None

            self.progress = None
            self.worker = None
            self.thread = None

        self.progress.canceled.connect(finished)

        self.thread = QThread(self)
        self.worker = DetectWorker(self.doc, pages, self.roiPayload)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(finished)
        self.worker.ocrReceived.connect(self.onOcrReceived)
        self.worker.error.connect(self.onOcrError)
        self.worker.message.connect(self.onWorkerMessage)
        self.thread.start()

        return

    def onSave(self):
        try:
            # create a excel writer object
            outfile = self.leOcrFile.text()
            if QMessageBox.question(self, "Save Changes", f"This will save to {outfile}") != QMessageBox.Yes:
                return

            with pd.ExcelWriter(outfile) as writer:
                ocrDf = self.tableOcr.dataframe()
                ocrDf.to_excel(writer, sheet_name="ocr", index=False)
                ocrLineNumbers = self.tableLineNumbers.dataframe()
                ocrLineNumbers.to_excel(writer, sheet_name="lineNumber", index=False)

            QMessageBox.information(self, "Saved", f"Saved to {outfile}")
        except Exception as e:
            QMessageBox.warning(self, "Save", f"{e}")

    def onAnalyze(self):
        # try:
        #     self.df.to_excel("debug/iso_analysis.xlsx")
        #     QMessageBox.information(self, "Saved", "Saved to debug/iso_analysis2.xlsx")
        # except Exception as e:
        #     QMessageBox.warning(self, "Save", f"{e}")

        df = self.tableOcr.dataframe()

        zoom = 2
        page_width = page.rect.width
        page_height = page.rect.height

        region = [self.x0, self.y0, self.x1, self.y1]
        response = isometric_analysis.run_text_detection(self.file, self.page, region, zoom=2)

        df = isometric_analysis.process_response(response)

        # df.to_excel("debug/iso_analysis.xlsx", index=False)

        result = isometric_analysis.analyze(df, self.page)
        result = result[result["ParentId"].isna()] # shows outer regions only
        # result = result[result["ParentId"].notna()] # inner regions only
        self.df = result.copy()

        image = isometric_analysis.draw_analysis(self.file, self.page, result, region)

        self.setImage(image)

        regions = []
        for row in result.itertuples():
            # adjust for crop
            x0, y0, x1, y1 = row.x0 - region[0], row.y0 - region[1], row.x1 - region[0], row.y1 - region[1]
            x0 = int(x0 * page_width * zoom)
            y0 = int(y0 * page_height * zoom)
            x1 = int(x1 * page_width * zoom)
            y1 = int(y1 * page_height * zoom)
            start = QPoint(x0, y0)
            end = QPoint(x1, y1)

            text = f"{row.value}\n"
            text += f"Angle: {row.angle}\n"
            text += f"Facing: {row.angle_facing}\n"
            text += f"Cat. 1: {row.category1}\n"
            text += f"Cat. 2: {row.category2}\n"
            text += f"Cat. 3: {row.category3}\n"
            text += f"Other Value: {row.other_value}\n"
            regions.append((start, end, text))

        self.viewer.pdfViewer.drawRegions(regions, "lightgreen")

        QMessageBox.information(self, "Finished", "Image updated")

    def onChangePage(self):
        try:
            page = int(self.lePage.text())
            self.setPage(page)
        except:
            pass

    def previousPage(self):
        self.setPage(self.page - 1)

    def nextPage(self):
        self.setPage(self.page + 1)

    def setPage(self, page: int):
        print("setting page", page)
        if not (page >= 1 and page <= self.pages):
            QMessageBox.information(self,
                                    "Invalid Page\t\t\t",
                                    "Page invalid or out of range")
            return

        self.page = page
        # self.setDefaultImage()
        self.onDisplayAnalysis()
        self.viewer.setEnabled(True)
        self.viewer.pdfViewer.fitPage()
        self.lePage.setText(f"{self.page}")

    def detectPageRange(self):
        if not self.doc:
            QMessageBox.information(self, "Detect Page Range", "Cannot detect missing pages without doc")
            return

        if not self.leOcrFile.text():
            QMessageBox.information(self, "Detect Page Range", "Cannot detect missing pages without setting OCR file")
            return

        res = QMessageBox.question(self, "Confirm", f"This will detect which pages are missing results and update page range text. Proceed?")
        if res != QMessageBox.Yes:
            return
        df = self.tableOcr.dataframe()
        existing = df["page"].unique().tolist()
        missing = []
        for n in range(self.pages):
            if n + 1 not in existing:
                missing.append(str(n+1))

        self.lePageRange.setText(", ".join(missing))
        QMessageBox.information(self, "Updated Page Range", f"Detected {len(missing)} missing pages")

    def onDisplayAnalysis(self):
        self.lblLineNumber.clear()

        zoom = 2
        page_width = self.doc[self.page - 1].rect.width
        page_height = self.doc[self.page - 1].rect.height

        page = self.doc[self.page]

        ocrDf = self.tableOcr.dataframe()
        ocrDf = ocrDf[ocrDf["page"] == self.page]

        group = self.roiPayload["pageToGroup"][self.page]
        rois = self.roiPayload["groupRois"][group]

        isometricRoi = None
        lineNumberRoi = None
        for roi in rois:
            if roi["columnName"] == "Isometric Drawing Area":
                isometricRoi = [
                    roi["relativeX0"],
                    roi["relativeY0"],
                    roi["relativeX1"],
                    roi["relativeY1"],
                ]
                continue
            if roi["columnName"] == "lineNumber":
                lineNumberRoi = [
                    roi["relativeX0"],
                    roi["relativeY0"],
                    roi["relativeX1"],
                    roi["relativeY1"],
                ]
                continue

        try:
            region = lineNumberRoi
            x0 = int(region[0] * page_width * zoom)
            y0 = int(region[1] * page_height * zoom)
            x1 = int(region[2] * page_width * zoom)
            y1 = int(region[3] * page_height * zoom)
            lineNumberCrop = page_to_opencv(page, 2)[y0:y1, x0:x1]

            resized = cv2.resize(lineNumberCrop, (0,0), fx=0.5, fy=0.5)
            pixmap = Image.fromarray(resized).toqpixmap()
            self.lblLineNumber.setPixmap(pixmap)
        except:
            print("Warning setting line number")
            pass

        if not self.pbDisplayAnalysis.isChecked():
            self.setDefaultImage()
            return

        lineNumberDf = self.tableLineNumbers.dataframe()

        result: pd.DataFrame = isometric_analysis.analyze(ocrDf, self.page, lineNumberDf)
        image = isometric_analysis.draw_analysis(self.file, self.page, result, isometricRoi)

        self.setImage(image)

        region = isometricRoi
        regions = []
        result_filtered = result[result["ParentId"].isna()]
        for row in result_filtered.itertuples():
            # adjust for crop
            x0, y0, x1, y1 = row.x0 - region[0], row.y0 - region[1], row.x1 - region[0], row.y1 - region[1]
            x0 = int(x0 * page_width * zoom)
            y0 = int(y0 * page_height * zoom)
            x1 = int(x1 * page_width * zoom)
            y1 = int(y1 * page_height * zoom)
            start = QPoint(x0, y0)
            end = QPoint(x1, y1)

            text = f"{row.value}\n"
            text += f"Angle: {row.angle}\n"
            text += f"Facing: {row.angle_facing}\n"
            text += f"Cat. 1: {row.category1}\n"
            text += f"Cat. 2: {row.category2}\n"
            text += f"Cat. 3: {row.category3}\n"
            text += f"Other Value: {row.other_value}\n"

            ref = f"Page {row.reference}" if row.reference else ""
            text += f"Reference: {ref}\n"
            regions.append((start, end, text))

        self.viewer.pdfViewer.drawRegions(regions, "lightgreen")

        text = [
            f"Page {self.page}",
            "------------------\n"
        ]
        try:
            lineNumberDf = self.tableLineNumbers.dataframe()
            lineNumber = lineNumberDf[lineNumberDf["page"] == self.page]["lineNumber"].iloc[0]
            text.append(f"Line Number: {lineNumber}")
        except:
            text.append(f"Line Number: Not found for this page")

        measurements = []
        total_measurement = None

        # text.append()

        text = "\n".join(text)

        self.textInfo.setText(text)

    def onOcrReceived(self, pageNum, data: pd.DataFrame, lineNumber: str):
        data["filename"] = self.file
        data = data[DATA_COLUMNS]
        self.tableOcr.model().removeRowsByQuery(f"page == {pageNum}")
        self.tableOcr.model().appendRows(data)

        print(f"Page {pageNum} Line number:", lineNumber)
        self.tableLineNumbers.model().removeRowsByQuery(f"page == {pageNum}")
        lineNumberDf = pd.DataFrame([{"page": pageNum, "lineNumber": lineNumber}])
        self.tableLineNumbers.model().appendRows(lineNumberDf)
        print(f"Received result for page {pageNum}")

        if pageNum == self.page:
            self.onDisplayAnalysis()

    def onOcrError(self, data):
        self.textLog.append(f"\nError - {data}")

    def onWorkerMessage(self, message):
        self.progress.setLabelText(f"{message}")
