
import os
import fitz  # PyMuPDF
import pandas as pd

def check_text_in_page(page, text_string):
    return text_string in page.get_text()

# def combine_pdf_in_folder(folder_path, output_filename, text_string, discard_folder):
#     # Create a new PDF document for the combined file
#     pdf_writer = fitz.open()

#     # Ensure discard folder exists
#     if not os.path.exists(discard_folder):
#         os.makedirs(discard_folder)

#     # Loop through all the PDF files in the folder
#     for file in sorted(os.listdir(folder_path)):
#         if file.endswith('.pdf'):
#             file_path = os.path.join(folder_path, file)
#             print(f"Processing: {file_path}")

#             try:
#                 with fitz.open(file_path) as pdf_document:
#                     # Create a discard document only if needed
#                     discard_pdf = None

#                     for page_num in range(pdf_document.page_count):
#                         page = pdf_document.load_page(page_num)
#                         if text_string in page.get_text():
#                             # Import the page to the combined document
#                             pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)
#                         else:
#                             if discard_pdf is None:
#                                 discard_pdf = fitz.open()  # Initialize only if needed
#                             # Import the page to the discard document
#                             discard_pdf.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

#                     if discard_pdf and discard_pdf.page_count > 0:
#                         discard_pdf_path = os.path.join(discard_folder, f"discard_{file}")
#                         discard_pdf.save(discard_pdf_path)
#                         discard_pdf.close()

#             except Exception as e:
#                 print(f"Error processing file '{file}': {e}")

#     # Save and close the combined PDF
#     pdf_writer.save(output_filename)
#     pdf_writer.close()

#     print(f'Combined PDF saved as {output_filename}')

def combine_pdf_in_folder(folder_path, output_filename, text_string, discard_folder):
    # Create a new PDF document for the combined file
    pdf_writer = fitz.open()

    # Ensure discard folder exists
    if not os.path.exists(discard_folder):
        os.makedirs(discard_folder)

    # Create a list to store mapping data
    mapping_data = []

    # Walk through all subfolders and files in the folder
    for root, dirs, files in os.walk(folder_path):
        for file in sorted(files):
            if file.endswith('.pdf'):
                file_path = os.path.join(root, file)
                print(f"Processing: {file_path}")

                try:
                    with fitz.open(file_path) as pdf_document:
                        # Check if text_string is None or empty, and set a flag accordingly
                        check_text_string = text_string is not None and text_string != ""
                        # Create a discard document only if needed and text_string check is enabled
                        discard_pdf = None

                        for page_num in range(pdf_document.page_count):
                            page = pdf_document.load_page(page_num)
                            # Modify condition to check if text_string check is disabled or if text_string is in page
                            if not check_text_string or text_string in page.get_text():
                                # Import the page to the combined document
                                pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)
                                
                            elif check_text_string:
                                # Initialize discard document if needed and text_string check is enabled
                                if discard_pdf is None:
                                    discard_pdf = fitz.open()
                                # Import the page to the discard document
                                discard_pdf.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

                        # Save discard document if it has pages and was created
                        if discard_pdf and discard_pdf.page_count > 0:
                            discard_pdf_path = os.path.join(discard_folder, f"discard_{file}")
                            discard_pdf.save(discard_pdf_path)
                            discard_pdf.close()

                except Exception as e:
                    print(f"Error processing file '{file}': {e}")

    # Save and close the combined PDF if it contains any pages
    if pdf_writer.page_count > 0:
        pdf_writer.save(output_filename)
        pdf_writer.close()
        print(f'Combined PDF saved as {output_filename}')
    else:
        print("No pages were added to the combined PDF. No file was saved.")

def extract_and_combine_pages(excel_path, pdf_path, output_filename):
    # Read page numbers from the Excel file
    page_numbers = pd.read_excel(excel_path, header=None, dtype=int).iloc[:, 0]

    # Create a new PDF document for the combined file
    pdf_writer = fitz.open()

    # Open the source PDF document
    with fitz.open(pdf_path) as pdf_document:
        for page_num in page_numbers:
            # Page numbers in Excel are likely 1-based, PDF pages are 0-based
            page = pdf_document.load_page(page_num - 1)
            # Import the page to the combined document
            pdf_writer.insert_pdf(pdf_document, from_page=page_num - 1, to_page=page_num - 1)

    # Save and close the combined PDF
    pdf_writer.save(output_filename)
    pdf_writer.close()

    print(f'Combined PDF saved as {output_filename}')
    
def combine_pdf_in_directory(source_directory, output_filename):
    # Create a new PDF document for the combined file
    combined_pdf_writer = fitz.open()

    # Iterate over all files in the directory
    for filename in os.listdir(source_directory):
        # Construct the full file path
        file_path = os.path.join(source_directory, filename)
        # Check if the current file is a PDF
        if os.path.isfile(file_path) and filename.lower().endswith('.pdf'):
            # Open the current PDF
            with fitz.open(file_path) as pdf_document:
                # Iterate through each page of the current PDF
                for page_num in range(len(pdf_document)):
                    # Get the page object
                    page = pdf_document.load_page(page_num)
                    # Import the page to the combined document
                    combined_pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

    # Save and close the combined PDF
    combined_pdf_writer.save(output_filename)
    combined_pdf_writer.close()

    print(f'Combined PDF saved as {output_filename}')
    
# Excel workbook path
excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\pages_missing.xlsx"  # Update this to the path of your Excel file

# PDF file to extract pages from
pdf_path = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Received\COMBINED - SHOP SCANNED PIECEMARKS.pdf"


# Directory path
folder_path = r"S:\Shared With Me\08 Contractors\80457 - XTO - Cowboy - Mech\DRAWINGS\ISOs"#'D:\\Architekt Integrated Systems\\Excel 81179_81216 Data Dump\\Individual ISOs\\Linde Validated'
# Output file
output_filename = r"Combined 80457 XTO Cowboy Mech.pdf"
discard_folder = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Validation Drawings\Discard_Pages"
text_to_search = None

#extract_and_combine_pages(excel_path, pdf_path, output_filename)
combine_pdf_in_folder(folder_path, output_filename, text_to_search, discard_folder)

#combine_pdf_in_directory(folder_path, output_filename)
