"""
Download and setup build environment required for app signing 

"""
import os
import json
import zipfile
import tempfile
from urllib.request import urlretrieve
from math import ceil
from subprocess import check_output, <PERSON><PERSON>, PIPE
from shutil import rmtree

# urlretrieve(url, basename(url), report);  # Don't need return
BUILD_DIR = "build"

# WINDOWS_SDK_VER = "10.0.26100.1742"
WINDOWS_SDK_URL = "https://www.nuget.org/api/v2/package/Microsoft.Windows.SDK.BuildTools/"
WINDOWS_SDK_ZIP = os.path.join(BUILD_DIR, "Windows.SDK.BuildTools.zip")
WINDOWS_SDK_PATH = os.path.join(BUILD_DIR, "Microsoft.Windows.SDK.BuildTools")

MS_TRUSTED_SIGNING_CLIENT_URL = "https://www.nuget.org/api/v2/package/Microsoft.Trusted.Signing.Client/1.0.60"
MS_TRUSTED_SIGNING_CLIENT_ZIP = os.path.join(BUILD_DIR, "Microsoft.Trusted.Signing.Client.zip")
MS_TRUSTED_SIGNING_CLIENT_PATH = os.path.join(BUILD_DIR, "Microsoft.Trusted.Signing.Client")

TRUSTED_SIGNING_CLIENT_DLIB = r"C:\Users\<USER>\Downloads\microsoft.trusted.signing.client.1.0.60\bin\x64\Azure.CodeSigning.Dlib.dll"

MICROSOFT_TIMESTAMP = "http://timestamp.acs.microsoft.com"

MIN_DOTNET_VER = 6

METADATA_JSON = {
	"Endpoint": "https://eus.codesigning.azure.net/", 
	"CodeSigningAccountName": "AIS-ATEM-Signing", 
	"CertificateProfileName": "ais-atem-cert",
	"ExcludeCredentials": [
		"InteractiveBrowserCredential"
	],
}
METADATA_JSON_PATH = os.path.abspath(os.path.join(BUILD_DIR, "metadata.json"))

AZURE_ENV_VARS = ("AZURE_CLIENT_ID", "AZURE_CLIENT_SECRET", "AZURE_TENANT_ID")
AZURE_ENV_PATH = os.path.join(BUILD_DIR, "azure_env.json")

def download_file(source, dest):

    def report(count, block, total):
        N = ceil(total/block)
        print(f'\r{source} - {count:,}/{N:,} of {total:,}', end='')

    urlretrieve(source, dest, report)
    print("Download finished.")


def get_signing_cmd(signtool_exe: str, code_signing_dlib: str, target_exe: str) -> list:
    """
    signtool.exe - Location of signtool.exe
    target_exe - Target executable to be signed 
    """
    metadata_json_file = METADATA_JSON_PATH
    with open(METADATA_JSON_PATH, 'w') as f:
        json.dump(METADATA_JSON, f)
    cmd = [
        "&",
        f'"{signtool_exe}"',
        "sign",
        "/v",
        "/debug",
        "/fd",
        "SHA256",
        "/tr",
        f'"{MICROSOFT_TIMESTAMP}"',
        "/td",
        "SHA256",
        "/dlib",
        f'"{code_signing_dlib}"',
        "/dmdf",
        f'"{metadata_json_file}"',
        f"{target_exe}"
    ]

    return cmd

def get_signtool_exe() -> str:
    """Return signtool path from within WINDOWS_SDK_PATH.

    Return False if cannot be found
    """
    bin_dir = os.path.join(WINDOWS_SDK_PATH, "bin")
    try:
        x64_dir = [x[0] for x in os.walk(bin_dir) if os.path.basename(os.path.normpath(x[0])) == "x64"][0]
    except Exception as e:
        print("Could not locate Windows SDK x64 dir")
        return False
    signtool_exe = os.path.abspath(os.path.join(x64_dir, "signtool.exe"))
    if os.path.exists(signtool_exe):
        print(f"Found signtool exe - {signtool_exe}")
        return signtool_exe

    return False

def get_code_signing_dlib() -> str:
    """Return trusted signing client dlib path from within MS_TRUSTED_SIGNING_CLIENT_PATH.

    Return False if cannot be found
    """
    bin_dir = os.path.join(MS_TRUSTED_SIGNING_CLIENT_PATH, "bin")
    try:
        x64_dir = [x[0] for x in os.walk(bin_dir) if os.path.basename(os.path.normpath(x[0])) == "x64"][0]
    except Exception as e:
        print("Could not locate x64 Code Signing Dlib")
        return False
    code_signing_dlib = os.path.abspath(os.path.join(x64_dir, "Azure.CodeSigning.Dlib.dll"))
    if os.path.exists(code_signing_dlib):
        print(f"Found code signing dlib - {code_signing_dlib}")
        return code_signing_dlib

    return False

def setup_sdk_buildtools():
    """This will remove, re-download, and extract SDK"""
    try:
        rmtree(WINDOWS_SDK_PATH)
    except:
        pass
    try:
        os.remove(WINDOWS_SDK_ZIP)
    except:
        pass
    if os.path.exists(WINDOWS_SDK_PATH):
        print("Windows SDK already extracted to build directory")
        return
    download_file(WINDOWS_SDK_URL, WINDOWS_SDK_ZIP)

    # Extract the downloaded zip
    with zipfile.ZipFile(WINDOWS_SDK_ZIP, 'r') as zip_ref:
        os.makedirs(WINDOWS_SDK_PATH)
        zip_ref.extractall(WINDOWS_SDK_PATH)

    # Cleanup
    os.remove(WINDOWS_SDK_ZIP)

def setup_trusted_signing_dlib():
    """This will remove, re-download, and extract dlib"""
    try:
        rmtree(MS_TRUSTED_SIGNING_CLIENT_PATH)
    except:
        pass
    try:
        os.remove(MS_TRUSTED_SIGNING_CLIENT_ZIP)
    except:
        pass
    if os.path.exists(MS_TRUSTED_SIGNING_CLIENT_PATH):
        print("Microsoft Trusted Signing Client already extracted to build directory")
        return
    download_file(MS_TRUSTED_SIGNING_CLIENT_URL, MS_TRUSTED_SIGNING_CLIENT_ZIP)

    # Extract the downloaded zip
    with zipfile.ZipFile(MS_TRUSTED_SIGNING_CLIENT_ZIP, 'r') as zip_ref:
        os.makedirs(MS_TRUSTED_SIGNING_CLIENT_PATH)
        zip_ref.extractall(MS_TRUSTED_SIGNING_CLIENT_PATH)

    # Cleanup
    os.remove(MS_TRUSTED_SIGNING_CLIENT_ZIP)

def dotnet_runtime_ok() -> bool:
    output = check_output(["dotnet", "--list-runtimes"], universal_newlines=True)
    print(output.split())
    valid_dotnet: bool = False
    dotnet_version = None
    for line in output.split():
        ver = line.split(".")
        # Check for version string
        try:
            ver = [int(v) for v in ver]
            major = ver[0]
            # Found dotnet major version
            print("Installed Dotnet version:", ver, "Minimum required:", MIN_DOTNET_VER)
            valid_dotnet = major >= MIN_DOTNET_VER
            dotnet_version = ver
            break
        except:
            pass

    if not valid_dotnet:
        print()
        print("Could not detect dotnet x64 (ver 6 or above) installed. Install and re-run script")
        print("Download link - https://dotnet.microsoft.com/en-us/download")
        print()

    return valid_dotnet

def setup_environment() -> bool:
    """Handles build dependencies and environment vars for app signing"""
    os.makedirs("build", exist_ok=True)

    signtool_exe = get_signtool_exe()
    if not signtool_exe:
        setup_sdk_buildtools()
        signtool_exe = get_signtool_exe()
        if not signtool_exe:
            print("Error downloading signtool. Exiting...")
            exit()

    code_signing_dlib = get_code_signing_dlib()
    if not code_signing_dlib:
        setup_trusted_signing_dlib()
        code_signing_dlib = get_code_signing_dlib()
        if not code_signing_dlib:
            print("Error downloading dlib. Exiting...")
            exit()

    print("signtool_exe", signtool_exe)
    print("code_signing_dlib", code_signing_dlib)

    return True

def check_env_vars():
    data = {}
    try:
        with open(AZURE_ENV_PATH, "r") as f:
            data = json.load(f)
    except:
        pass

    for key in AZURE_ENV_VARS:
        value = data.get(key)
        if not value:
            break
    else:
        print()
        y = input(f"Found existing env vars from {AZURE_ENV_PATH}. Use these credentials? (y/n)").lower() == "y"
        if y:
            return data
        print()
    
    print(f"Enter AZURE credentials....These will be saved at {AZURE_ENV_PATH}")
    for key in AZURE_ENV_VARS:
        value = input(f"Enter value for {key}=")
        data[key] = value
    
    with open(AZURE_ENV_PATH, 'w') as f:
        json.dump(data, f)

    return data


def sign_exe(target_exe: str):

    signtool_exe = get_signtool_exe()
    if not signtool_exe:
        print("Error detecting signtool. Exiting...")
        exit()

    code_signing_dlib = get_code_signing_dlib()
    if not code_signing_dlib:
        print("Error detecting dlib. Exiting...")
        exit()

    cmd = get_signing_cmd(signtool_exe, target_exe=target_exe, code_signing_dlib=code_signing_dlib)

    print()
    print()
    print(cmd)
    print()
    print()
    print()

    # vars: dict = check_env_vars()
    # for key in AZURE_ENV_VARS:
    #     try:
    #         os.environ(key)
    #     except Exception as e:
    #         print("Failed code signing. Not all AZURE env vars are set", key)
    #         exit()

    print()
    print(" ".join(cmd))
    print()

    process = Popen(['powershell.exe'] + cmd, stdout=PIPE)
    result = process.communicate()[0]

    # Print the output
    print(result.decode())

    print("done")