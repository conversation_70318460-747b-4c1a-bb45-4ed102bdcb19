# Row Detection Fix Implementation Plan

## Problem Statement

The current row detection logic in `detect_bom_rows()` fails when leftmost column position numbers are vertically centered within multi-line wrapped content. This causes wrapped text to be incorrectly assigned to the wrong rows.

### Current Failure Pattern:
```
Row 1: "1.     This is not a wrapped row"

Row 2: Position "2." is vertically centered in:
       "This is a wrapped row
        That gets merged into the 
        wrong row"

Row 3: "3. This is another row"
```

**Problem:** Row boundaries are calculated using `y0` coordinates of position numbers. When "2." is centered, its `y0` is in the middle of the wrapped content, causing:
- Row 1 range extends too far down (captures Row 2's wrapped content)
- Row 2 range starts too late (misses its own wrapped content)

## Root Cause Analysis

**Location:** Lines 1919-1945 in `detect_bom_rows()` function

**Current Logic:**
```python
start = bom_items_sorted.iloc[i]['y0']        # Top of position number
end = bom_items_sorted.iloc[i+1]['y0']        # Top of NEXT position number
```

**Issue:** When position numbers are vertically centered, `y0` doesn't represent the logical start of the row content.

## Proposed Solution: Minimal Targeted Fix

### Strategy: Content-Aware Boundary Detection

1. **Detection Phase:** Identify when position numbers appear vertically centered
2. **Adjustment Phase:** Calculate more accurate row boundaries for centered cases
3. **Fallback:** Preserve original logic for standard cases

### Implementation Approach

#### Phase 1: Add Detection Logic
```python
def detect_centered_position_numbers(bom_items_sorted, df):
    """
    Detect when position numbers are vertically centered in wrapped content
    Returns list of indices where centering is detected
    """
    centered_indices = []
    
    for i in range(len(bom_items_sorted)):
        pos_y0 = bom_items_sorted.iloc[i]['y0']
        pos_y1 = bom_items_sorted.iloc[i]['coordinates2'][3]
        
        # Find content that should logically belong to this row
        # Look for text that starts significantly above the position number
        nearby_content = df[
            (df['x0'] > leftmost_column['x1']) &  # Not in leftmost column
            (df['y0'] < pos_y0) &                 # Starts above position
            (df['y1'] > pos_y0 - 20)              # But not too far above
        ]
        
        if len(nearby_content) > 0:
            # Check if position number appears centered in the content
            content_top = nearby_content['y0'].min()
            content_bottom = nearby_content['y1'].max()
            
            # If position is roughly in the middle of content, it's likely centered
            pos_center = (pos_y0 + pos_y1) / 2
            content_center = (content_top + content_bottom) / 2
            
            if abs(pos_center - content_center) < 5:  # Small tolerance
                centered_indices.append(i)
    
    return centered_indices
```

#### Phase 2: Adjust Row Boundaries
```python
def calculate_adjusted_row_ranges(bom_items_sorted, df, leftmost_column, centered_indices):
    """
    Calculate row ranges with adjustments for centered position numbers
    """
    row_ranges = []
    
    for i in range(len(bom_items_sorted)):
        if i in centered_indices:
            # For centered positions, find the actual content boundaries
            pos_y0 = bom_items_sorted.iloc[i]['y0']
            
            # Find content that belongs to this row
            row_content = df[
                (df['x0'] > leftmost_column['x1']) &  # Not in leftmost column
                (df['y0'] >= pos_y0 - 15) &           # Near position number
                (df['y0'] <= pos_y0 + 15)             # Within reasonable range
            ]
            
            if len(row_content) > 0:
                start = min(row_content['y0'].min(), pos_y0)
            else:
                start = pos_y0  # Fallback to original
                
        else:
            # Use original logic for non-centered positions
            start = bom_items_sorted.iloc[i]['y0']
        
        # Calculate end boundary
        if i < len(bom_items_sorted) - 1:
            next_pos_y0 = bom_items_sorted.iloc[i+1]['y0']
            
            if (i+1) in centered_indices:
                # Next position is centered, find where current row should end
                next_row_content = df[
                    (df['x0'] > leftmost_column['x1']) &
                    (df['y0'] >= next_pos_y0 - 15) &
                    (df['y0'] <= next_pos_y0 + 15)
                ]
                
                if len(next_row_content) > 0:
                    end = next_row_content['y0'].min()
                else:
                    end = next_pos_y0  # Fallback
            else:
                end = next_pos_y0  # Original logic
        else:
            # Last row logic (unchanged)
            end = calculate_last_row_end(...)
            
        row_ranges.append((start, end))
    
    return row_ranges
```

#### Phase 3: Integration with Feature Flag
```python
# Add new debug flag
debug_centered_fix = True  # Feature flag for the new logic

# In detect_bom_rows function:
if debug_centered_fix:
    centered_indices = detect_centered_position_numbers(bom_items_sorted, df)
    if centered_indices:
        print(f"Detected centered position numbers at indices: {centered_indices}")
        row_ranges = calculate_adjusted_row_ranges(bom_items_sorted, df, leftmost_column, centered_indices)
    else:
        # Use original logic
        row_ranges = calculate_original_row_ranges(...)
else:
    # Original logic (unchanged)
    row_ranges = calculate_original_row_ranges(...)
```

## Implementation Constraints

### Minimal Changes Required:
1. Add new functions without modifying existing ones
2. Use feature flag to enable/disable new logic
3. Preserve all existing debug output and function signatures
4. Ensure original logic remains as fallback

### Testing Strategy:
1. Test with known problematic documents
2. Verify no regression on working documents
3. Compare before/after row assignments
4. Validate debug output consistency

## Success Metrics

1. **Functionality:** Wrapped content correctly assigned to proper rows
2. **Compatibility:** No changes to existing working documents
3. **Reversibility:** Can be disabled via feature flag
4. **Maintainability:** Clear separation between old and new logic

## File Locations

- **Main Implementation:** `unit_tests/get_tables_test.py`
- **Test Data:** 
  - Raw data: `C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 042 - InDemand Berryville\Data\data.feather`
  - ROI: `C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 042 - InDemand Berryville\roi.json`
- **Test Harness:** Use existing `if __name__ == '__main__'` block

## Next Steps

1. Set up test harness with new data sources
2. Implement detection logic with feature flag
3. Test on problematic cases
4. Refine boundary calculation algorithm
5. Validate against existing working documents
