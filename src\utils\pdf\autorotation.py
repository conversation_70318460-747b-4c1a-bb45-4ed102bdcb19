import os
import time
import fitz  # PyMuPDF
from PIL import Image
import logging
import pandas as pd
import concurrent.futures
from tqdm import tqdm

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create a custom temp directory in the project folder
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "temp")
os.makedirs(TEMP_DIR, exist_ok=True)
logger.info(f"Using custom temp directory: {TEMP_DIR}")

# Check if running on Windows and WinRT is available
USE_WINSDK = False
try:
    import winsdk.windows.media.ocr as ocr
    import winsdk.windows.graphics.imaging as imaging
    import winsdk.windows.storage as storage
    import asyncio

    USE_WINSDK = True
    logger.info("WinSDK OCR capabilities are available")
except ImportError:
    logger.info("WinSDK not available, falling back to PyMuPDF only")
    print("WinSDK required. ")
    print("Please install it with 'pip install winsdk' and run again.")
    exit()


def detect_orientation(pdf_path, pdf_page=1, use_winsdk=USE_WINSDK):
    """
    Detect the orientation of a PDF page using OCR.

    Args:
        pdf_path (str): Path to the PDF file
        pdf_page (int): Page number to analyze (1-based)
        use_winsdk (bool): Whether to use WinSDK OCR capabilities (Windows only)

    Returns:
        int: Detected rotation angle (0, 90, 180, or 270)
    """
    # First try with WinSDK if available
    if use_winsdk:
        rotation = detect_orientation_winsdk(pdf_path, pdf_page)
        if rotation is not None:
            return rotation

    # Fall back to PyMuPDF if WinSDK is not available or failed
    rotation = detect_orientation_pymupdf(pdf_path, pdf_page)

    # If still not determined, default to 0 (no rotation)
    if rotation is None:
        rotation = 0

    return rotation


def detect_orientation_pymupdf(pdf_path, pdf_page=1):
    """
    Detect the orientation of a PDF page using PyMuPDF text extraction and baseline analysis.

    Args:
        pdf_path (str): Path to the PDF file
        pdf_page (int): Page number to analyze (1-based)

    Returns:
        int or None: Detected rotation angle (0, 90, 180, or 270) or None if undetermined
    """
    try:
        doc = fitz.open(pdf_path)
        # Convert from 1-based to 0-based page numbering
        page_idx = pdf_page - 1

        if page_idx >= len(doc) or page_idx < 0:
            logger.warning(f"Page {pdf_page} does not exist in document with {len(doc)} pages")
            return None

        page = doc[page_idx]

        # Try to extract text blocks
        text_dict = page.get_text("dict")
        if not text_dict or "blocks" not in text_dict or not text_dict["blocks"]:
            logger.info(f"No text blocks found in page {pdf_page}, cannot determine orientation")
            return None

        # Analyze text baselines
        result = analyze_baselines_pymupdf(text_dict)
        doc.close()
        return result

    except Exception as e:
        logger.error(f"Error in detect_orientation_pymupdf: {str(e)}")
        return None


def analyze_baselines_pymupdf(text_dict):
    """
    Analyze text baselines to determine orientation.

    In correctly oriented text, most characters sit on a common baseline near the bottom
    of their bounding boxes. When upside down, characters appear to "hang" from the top.

    Args:
        text_dict (dict): Text dictionary from PyMuPDF

    Returns:
        int: Detected rotation angle (0 or 180) or None if undetermined
    """
    # Collect baseline metrics
    baseline_metrics = []

    # Process each text block
    for block in text_dict["blocks"]:
        if block["type"] != 0:  # Skip non-text blocks
            continue

        for line in block["lines"]:
            line_height = line["bbox"][3] - line["bbox"][1]

            for span in line["spans"]:
                # Skip very short spans (likely punctuation)
                if len(span["text"].strip()) < 3:
                    continue

                span_height = span["bbox"][3] - span["bbox"][1]

                # Calculate baseline position relative to bounding box
                # In normal text, most characters sit on the baseline near the bottom
                # The ascender zone (above x-height) is typically larger than the descender zone

                # For each span, calculate the ratio of space above vs. below the center
                center_y = (span["bbox"][1] + span["bbox"][3]) / 2

                # Calculate distances from center to top and bottom
                dist_to_top = center_y - span["bbox"][1]
                dist_to_bottom = span["bbox"][3] - center_y

                # Calculate ratio
                if dist_to_bottom > 0:
                    ratio = dist_to_top / dist_to_bottom
                    baseline_metrics.append(ratio)

    # If we don't have enough data, return None
    if len(baseline_metrics) < 3:
        logger.info("Not enough text spans for reliable baseline analysis")
        return None

    # Calculate average ratio
    avg_ratio = sum(baseline_metrics) / len(baseline_metrics)

    logger.info(f"Average baseline ratio: {avg_ratio:.2f}")

    if avg_ratio > 1.1:
        return 0  # Correctly oriented
    elif avg_ratio < 0.9:
        return 180  # Upside down
    else:
        # Ratio is close to 1, can't determine reliably
        logger.info("Baseline ratio inconclusive")
        return None


async def _run_winsdk_ocr(image_path):
    """
    Run Windows OCR on an image using WinSDK.

    Args:
        image_path (str): Path to the image file

    Returns:
        List of OCR results with bounding boxes
    """
    # Get the file
    file = await storage.StorageFile.get_file_from_path_async(image_path)

    # Open a stream for the file
    stream = await file.open_async(storage.FileAccessMode.READ)

    # Create a decoder
    decoder = await imaging.BitmapDecoder.create_async(stream)

    # Get the bitmap
    bitmap = await decoder.get_software_bitmap_async()

    # Create an OCR engine for the user's language
    engine = ocr.OcrEngine.try_create_from_user_profile_languages()

    # Recognize the text
    result = await engine.recognize_async(bitmap)

    return result


def detect_orientation_winsdk(pdf_path, pdf_page=1):
    """
    Detect the orientation of a PDF page using Windows OCR via WinSDK.
    Tests all four possible orientations and selects the one with the most text.

    Args:
        pdf_path (str): Path to the PDF file
        pdf_page (int): Page number to analyze (1-based)

    Returns:
        int or None: Detected rotation angle (0, 90, 180, or 270) or None if undetermined
    """
    if not USE_WINSDK:
        logger.warning("WinSDK is not available")
        return None

    try:
        # Open the PDF
        doc = fitz.open(pdf_path)
        # Convert from 1-based to 0-based page numbering
        page_idx = pdf_page - 1

        if page_idx >= len(doc) or page_idx < 0:
            logger.warning(f"Page {pdf_page} does not exist in document with {len(doc)} pages")
            return None

        page = doc[page_idx]

        # Test all four rotations
        results = {}
        temp_files = []

        for rotation in [0, 90, 180, 270]:
            logger.info(f"Testing rotation {rotation}°")

            # Create a rotated matrix
            matrix = fitz.Matrix(2, 2)  # 2x zoom for better OCR
            if rotation != 0:
                matrix.prerotate(rotation)

            # Render page to image with rotation
            pix = page.get_pixmap(matrix=matrix)

            # Save to temporary file
            temp_path = os.path.join(TEMP_DIR, f"atom_pdf_rotation_{os.getpid()}_{time.time()}_{rotation}.png")
            pix.save(temp_path)
            temp_files.append(temp_path)

            try:
                # Run OCR using WinSDK
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                ocr_result = loop.run_until_complete(_run_winsdk_ocr(temp_path))
                loop.close()

                # Count the number of detected lines and words
                line_count = 0
                word_count = 0
                text_length = 0

                if ocr_result and hasattr(ocr_result, 'lines') and ocr_result.lines:
                    try:
                        line_count = ocr_result.lines.size

                        # Count words and total text length
                        for line in ocr_result.lines:
                            if hasattr(line, 'text'):
                                text_length += len(line.text.strip())

                            if hasattr(line, 'words') and line.words:
                                try:
                                    word_count += line.words.size
                                except Exception:
                                    # If we can't get word count, estimate from text
                                    if hasattr(line, 'text'):
                                        word_count += len(line.text.split())
                    except Exception as e:
                        logger.warning(f"Error counting text elements for rotation {rotation}°: {str(e)}")

                # Store results
                results[rotation] = {
                    'line_count': line_count,
                    'word_count': word_count,
                    'text_length': text_length,
                    'ocr_result': ocr_result
                }

                logger.info(f"Rotation {rotation}°: detected {line_count} lines, {word_count} words, {text_length} chars")

            except Exception as e:
                logger.error(f"Error in OCR for rotation {rotation}°: {str(e)}")
                results[rotation] = {
                    'line_count': 0,
                    'word_count': 0,
                    'text_length': 0,
                    'ocr_result': None
                }

        # Close the document
        doc.close()

        # Clean up temp files (best effort)
        for temp_path in temp_files:
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except Exception:
                pass  # Ignore errors, files will be cleaned up later

        # Determine the best rotation
        if not results:
            logger.warning("No OCR results available for any rotation")
            return None

        # First try to find the rotation with the most lines
        max_lines = 0
        best_rotation = 0  # Default to 0 degrees

        for rotation, data in results.items():
            if data['line_count'] > max_lines:
                max_lines = data['line_count']
                best_rotation = rotation

        # If no clear winner based on line count, try word count
        if max_lines == 0:
            max_words = 0
            for rotation, data in results.items():
                if data['word_count'] > max_words:
                    max_words = data['word_count']
                    best_rotation = rotation

            # If still no clear winner, try text length
            if max_words == 0:
                max_text = 0
                for rotation, data in results.items():
                    if data['text_length'] > max_text:
                        max_text = data['text_length']
                        best_rotation = rotation

                if max_text == 0:
                    logger.warning("No text detected in any rotation")
                    return None
                else:
                    logger.info(f"Selected rotation {best_rotation}° based on text length ({max_text} chars)")
            else:
                logger.info(f"Selected rotation {best_rotation}° based on word count ({max_words} words)")
        else:
            logger.info(f"Selected rotation {best_rotation}° based on line count ({max_lines} lines)")

        # If there's a significant difference between the best and second best rotation,
        # we can be confident in our choice
        second_best_lines = 0
        for rotation, data in results.items():
            if rotation != best_rotation and data['line_count'] > second_best_lines:
                second_best_lines = data['line_count']

        if max_lines > 0 and (second_best_lines == 0 or max_lines >= second_best_lines * 1.5):
            logger.info(f"Confident in rotation {best_rotation}° (significantly more text than other rotations)")
            return best_rotation

        # If the difference isn't significant, try baseline analysis on the best rotation
        best_data = results[best_rotation]
        if best_data['ocr_result'] and best_data['line_count'] >= 5:
            baseline_result = analyze_baselines_winsdk(best_data['ocr_result'])
            if baseline_result is not None:
                # If baseline analysis suggests a 180° flip, apply it to our best rotation
                final_rotation = (best_rotation + baseline_result) % 360
                logger.info(f"Adjusted rotation from {best_rotation}° to {final_rotation}° based on baseline analysis")
                return final_rotation

        # If baseline analysis didn't help, just return the rotation with the most text
        return best_rotation

    except Exception as e:
        logger.error(f"Error in detect_orientation_winsdk: {str(e)}")
        return None


def analyze_baselines_winsdk(ocr_result):
    """
    Analyze text baselines from WinSDK OCR results to determine orientation.

    Args:
        ocr_result: OCR result from WinSDK

    Returns:
        int: Detected rotation angle (0 or 180) or None if undetermined
    """
    baseline_metrics = []

    # Process each line
    for line in ocr_result.lines:
        try:
            # Skip very short lines
            if len(line.text.strip()) < 3:
                continue

            # Get bounding box - try different attribute names
            if hasattr(line, 'bounding_rect'):
                rect = line.bounding_rect
            elif hasattr(line, 'boundingRect'):
                rect = line.boundingRect
            elif hasattr(line, 'bounding_rectangle'):
                rect = line.bounding_rectangle
            else:
                # Log available attributes and skip this line
                logger.warning(f"Could not find bounding rectangle attribute for OCR line. Available attributes: {dir(line)}")
                continue

            # Calculate center position
            center_y = rect.y + (rect.height / 2)

            # Calculate distances from center to top and bottom
            dist_to_top = center_y - rect.y
            dist_to_bottom = (rect.y + rect.height) - center_y

            # Calculate ratio
            if dist_to_bottom > 0:
                ratio = dist_to_top / dist_to_bottom
                baseline_metrics.append(ratio)
        except Exception as e:
            logger.warning(f"Error processing OCR line for baseline analysis: {str(e)}")
            continue

    # If we don't have enough data, return None
    if len(baseline_metrics) < 5:
        logger.info("Not enough text lines for reliable baseline analysis")
        return None

    # Calculate average ratio
    avg_ratio = sum(baseline_metrics) / len(baseline_metrics)

    logger.info(f"Average baseline ratio (WinSDK): {avg_ratio:.2f}")

    if avg_ratio > 1.1:
        return 0  # Correctly oriented
    elif avg_ratio < 0.9:
        return 180  # Upside down
    else:
        # Ratio is close to 1, can't determine reliably
        logger.info("Baseline ratio inconclusive (WinSDK)")
        return None


def rotate_pdf(pdf_path, output_path=None, rotations=None, auto_detect=True):
    """
    Rotate pages in a PDF file based on detected orientation or specified rotations.

    Args:
        pdf_path (str): Path to the PDF file
        output_path (str): Path to save the rotated PDF (if None, will use pdf_path + "_rotated.pdf")
        rotations (dict or int): Dictionary mapping page numbers (1-based) to rotation angles,
                                or a single int to apply to all pages
        auto_detect (bool): Whether to automatically detect orientation for pages
                           not specified in rotations

    Returns:
        str: Path to the rotated PDF file
    """
    if output_path is None:
        base, ext = os.path.splitext(pdf_path)
        output_path = f"{base}_rotated{ext}"

    # Ensure rotations is a dictionary
    if rotations is None:
        rotations = {}
    elif isinstance(rotations, int):
        rotations = {0: rotations}  # 0 means apply to all pages

    try:
        doc = fitz.open(pdf_path)

        # Process each page
        for page_idx in range(len(doc)):
            # Convert to 1-based page number for external API
            pdf_page = page_idx + 1

            # Determine rotation angle
            if pdf_page in rotations:
                angle = rotations[pdf_page]
            elif 0 in rotations:  # 0 means apply to all pages
                angle = rotations[0]
            elif auto_detect:
                angle = detect_orientation(pdf_path, pdf_page)
            else:
                angle = 0

            # Apply rotation if needed
            if angle != 0:
                logger.info(f"Rotating page {pdf_page} by {angle} degrees")
                page = doc[page_idx]
                page.set_rotation(angle)

        # Save the rotated document
        doc.save(output_path)
        doc.close()

        logger.info(f"Rotated PDF saved to {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error in rotate_pdf: {str(e)}")
        return None


def visualize_text_spans(pdf_path, pdf_page=1, output_path=None, show_baselines=True, show_ratios=True, use_winsdk=USE_WINSDK):
    """
    Visualize text spans in a PDF page by drawing bounding boxes and baseline information.

    Args:
        pdf_path (str): Path to the PDF file
        pdf_page (int): Page number to visualize (1-based)
        output_path (str): Path to save the visualization image (if None, will use pdf_path + "_spans.png")
        show_baselines (bool): Whether to show baseline positions
        show_ratios (bool): Whether to show baseline ratio values
        use_winsdk (bool): Whether to also show WinSDK OCR results

    Returns:
        str: Path to the saved visualization image
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        from matplotlib.colors import LinearSegmentedColormap
        import numpy as np

        # Open the PDF
        doc = fitz.open(pdf_path)
        # Convert from 1-based to 0-based page numbering
        page_idx = pdf_page - 1

        if page_idx >= len(doc) or page_idx < 0:
            logger.warning(f"Page {pdf_page} does not exist in document with {len(doc)} pages")
            return None

        page = doc[page_idx]

        # Render page to image
        zoom = 2  # higher zoom for better quality
        mat = fitz.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=mat)

        # Convert to numpy array for matplotlib
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)

        # Determine if we need one or two subplots
        if use_winsdk:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 16))
            ax1.set_title(f"PyMuPDF Text Spans - Page {pdf_page}")
            ax2.set_title(f"WinSDK OCR Results - Page {pdf_page}")

            # Display the page on both axes
            ax1.imshow(img)
            ax2.imshow(img)
        else:
            fig, ax1 = plt.subplots(figsize=(12, 16))
            ax1.set_title(f"Text Spans Visualization - Page {pdf_page}")

            # Display the page
            ax1.imshow(img)
            ax2 = None

        # Extract text blocks with PyMuPDF
        text_dict = page.get_text("dict")
        pymupdf_has_text = bool(text_dict and "blocks" in text_dict and text_dict["blocks"])

        if not pymupdf_has_text:
            logger.info(f"No text blocks found in page {pdf_page} using PyMuPDF")
            ax1.text(10, 30, "No text blocks found with PyMuPDF",
                    color='red', fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.8))
        else:
            # Create a colormap for baseline ratios
            cmap = LinearSegmentedColormap.from_list("baseline_cmap", ["blue", "green", "red"])

            # Process each text block
            baseline_metrics = []

            # First pass to collect all baseline metrics for normalization
            for block in text_dict["blocks"]:
                if block["type"] != 0:  # Skip non-text blocks
                    continue

                for line in block["lines"]:
                    for span in line["spans"]:
                        # Skip very short spans (likely punctuation)
                        if len(span["text"].strip()) < 3:
                            continue

                        # Calculate baseline position relative to bounding box
                        center_y = (span["bbox"][1] + span["bbox"][3]) / 2

                        # Calculate distances from center to top and bottom
                        dist_to_top = center_y - span["bbox"][1]
                        dist_to_bottom = span["bbox"][3] - center_y

                        # Calculate ratio
                        if dist_to_bottom > 0:
                            ratio = dist_to_top / dist_to_bottom
                            baseline_metrics.append(ratio)

            # Determine min and max ratios for color mapping
            if baseline_metrics:
                min_ratio = min(baseline_metrics)
                max_ratio = max(baseline_metrics)
                # Ensure range is at least 0.5 to 1.5 for better color distribution
                min_ratio = min(min_ratio, 0.5)
                max_ratio = max(max_ratio, 1.5)
            else:
                min_ratio, max_ratio = 0.5, 1.5

            # Second pass to draw bounding boxes and baselines
            for block_idx, block in enumerate(text_dict["blocks"]):
                if block["type"] != 0:  # Skip non-text blocks
                    continue

                # Draw block bounding box with light color
                block_bbox = [coord * zoom for coord in block["bbox"]]
                rect = patches.Rectangle(
                    (block_bbox[0], block_bbox[1]),
                    block_bbox[2] - block_bbox[0],
                    block_bbox[3] - block_bbox[1],
                    linewidth=1, edgecolor='lightgray', facecolor='none', linestyle='--'
                )
                ax1.add_patch(rect)

                for line_idx, line in enumerate(block["lines"]):
                    # Draw line bounding box
                    line_bbox = [coord * zoom for coord in line["bbox"]]
                    rect = patches.Rectangle(
                        (line_bbox[0], line_bbox[1]),
                        line_bbox[2] - line_bbox[0],
                        line_bbox[3] - line_bbox[1],
                        linewidth=1, edgecolor='gray', facecolor='none', linestyle=':'
                    )
                    ax1.add_patch(rect)

                    for span_idx, span in enumerate(line["spans"]):
                        # Skip very short spans
                        if len(span["text"].strip()) < 3:
                            continue

                        # Calculate baseline position and ratio
                        span_bbox = [coord * zoom for coord in span["bbox"]]
                        center_y = (span_bbox[1] + span_bbox[3]) / 2
                        dist_to_top = center_y - span_bbox[1]
                        dist_to_bottom = span_bbox[3] - center_y

                        if dist_to_bottom > 0:
                            ratio = dist_to_top / dist_to_bottom
                        else:
                            ratio = 1.0

                        # Normalize ratio for color mapping
                        norm_ratio = (ratio - min_ratio) / (max_ratio - min_ratio)
                        color = cmap(norm_ratio)

                        # Draw span bounding box
                        rect = patches.Rectangle(
                            (span_bbox[0], span_bbox[1]),
                            span_bbox[2] - span_bbox[0],
                            span_bbox[3] - span_bbox[1],
                            linewidth=1.5, edgecolor=color, facecolor='none'
                        )
                        ax1.add_patch(rect)

                        # Draw baseline
                        if show_baselines:
                            baseline_y = span_bbox[1] + dist_to_top
                            ax1.plot(
                                [span_bbox[0], span_bbox[2]],
                                [baseline_y, baseline_y],
                                color=color, linewidth=1.5
                            )

                        # Show ratio value
                        if show_ratios:
                            ax1.text(
                                span_bbox[0], span_bbox[1] - 5,
                                f"{ratio:.2f}",
                                color=color, fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1)
                            )

                        # Show text content
                        ax1.text(
                            span_bbox[0], span_bbox[3] + 5,
                            span["text"][:10] + ("..." if len(span["text"]) > 10 else ""),
                            color='black', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1)
                        )

            # Add a colorbar to show ratio scale for PyMuPDF
            sm = plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(min_ratio, max_ratio))
            sm.set_array([])
            if use_winsdk:
                cbar = plt.colorbar(sm, ax=ax1, orientation='vertical', pad=0.01, fraction=0.05)
            else:
                cbar = plt.colorbar(sm, ax=ax1, orientation='vertical', pad=0.01, fraction=0.05)
            cbar.set_label('Baseline Ratio (dist_top/dist_bottom)')

            # Add legend for PyMuPDF
            legend_elements = [
                patches.Patch(facecolor='none', edgecolor='lightgray', linestyle='--', label='Text Block'),
                patches.Patch(facecolor='none', edgecolor='gray', linestyle=':', label='Text Line'),
                patches.Patch(facecolor='none', edgecolor='green', label='Text Span'),
            ]
            ax1.legend(handles=legend_elements, loc='upper right')

        # Process WinSDK OCR results if requested
        if use_winsdk and ax2 is not None:
            # Create a unique filename in our custom temp directory
            temp_path = os.path.join(TEMP_DIR, f"atom_pdf_rotation_{os.getpid()}_{time.time()}.png")

            try:
                # Save the image directly to our custom temp directory
                pix.save(temp_path)

                # Make sure the file exists before proceeding
                if not os.path.exists(temp_path):
                    raise FileNotFoundError(f"Temporary file {temp_path} not created properly")

                winsdk_has_text = False

                # Run OCR using WinSDK
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                ocr_result = loop.run_until_complete(_run_winsdk_ocr(temp_path))
                loop.close()

                # Check if OCR found any text
                if ocr_result and ocr_result.lines and ocr_result.lines.size > 0:
                    winsdk_has_text = True

                    # Draw OCR results
                    for line_idx, line in enumerate(ocr_result.lines):
                        # Get line bounding box - use the correct attribute name
                        # Different versions of WinSDK might have different attribute names
                        try:
                            # Try different possible attribute names
                            if hasattr(line, 'bounding_rect'):
                                rect = line.bounding_rect
                            elif hasattr(line, 'boundingRect'):
                                rect = line.boundingRect
                            elif hasattr(line, 'bounding_rectangle'):
                                rect = line.bounding_rectangle
                            else:
                                # Print all available attributes for debugging
                                logger.info(f"Available attributes for OCR line: {dir(line)}, {line.text}")
                                # Use a default rectangle as fallback
                                continue

                            x, y, w, h = rect.x * zoom, rect.y * zoom, rect.width * zoom, rect.height * zoom

                            # Draw line bounding box
                            rect_patch = patches.Rectangle(
                                (x, y), w, h,
                                linewidth=1, edgecolor='blue', facecolor='none', linestyle='-'
                            )
                            ax2.add_patch(rect_patch)

                            # Show line text
                            ax2.text(
                                x, y - 5,
                                line.text[:20] + ("..." if len(line.text) > 20 else ""),
                                color='blue', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1)
                            )
                        except Exception as e:
                            logger.warning(f"Error processing OCR line {line_idx}: {str(e)}")
                            continue

                        # Draw word bounding boxes
                        for word_idx, word in enumerate(line.words):
                            try:
                                # Try different possible attribute names for word bounding rectangle
                                if hasattr(word, 'bounding_rect'):
                                    rect = word.bounding_rect
                                elif hasattr(word, 'boundingRect'):
                                    rect = word.boundingRect
                                elif hasattr(word, 'bounding_rectangle'):
                                    rect = word.bounding_rectangle
                                else:
                                    # Print all available attributes for debugging
                                    logger.info(f"Available attributes for OCR word: {dir(word)}, {word.text}")
                                    # Skip this word if we can't find the bounding rectangle
                                    continue

                                x, y, w, h = rect.x * zoom, rect.y * zoom, rect.width * zoom, rect.height * zoom

                                # Draw word bounding box
                                rect_patch = patches.Rectangle(
                                    (x, y), w, h,
                                    linewidth=1, edgecolor='green', facecolor='none', linestyle='-'
                                )
                                ax2.add_patch(rect_patch)

                                # Show word text
                                ax2.text(
                                    x, y + h + 5,
                                    word.text,
                                    color='green', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1)
                                )
                            except Exception as e:
                                logger.warning(f"Error processing OCR word {word_idx}: {str(e)}")
                                continue
                if not winsdk_has_text:
                    ax2.text(10, 30, "No text detected with WinSDK OCR",
                            color='red', fontsize=12,
                            bbox=dict(facecolor='white', alpha=0.8))
            except Exception as e:
                logger.error(f"Error in WinSDK OCR visualization: {str(e)}")
                ax2.text(10, 30, f"Error in WinSDK OCR: {str(e)}",
                        color='red', fontsize=12,
                        bbox=dict(facecolor='white', alpha=0.8))

            finally:
                # Don't try to delete the file immediately, just leave it in our custom temp directory
                # We'll clean up the whole directory later
                pass

        # Remove axes ticks
        ax1.set_xticks([])
        ax1.set_yticks([])
        if ax2 is not None:
            ax2.set_xticks([])
            ax2.set_yticks([])

        # Save the figure
        if output_path is None:
            base, ext = os.path.splitext(pdf_path)
            output_path = f"{base}_spans_p{pdf_page}.png"

        plt.tight_layout()
        plt.savefig(output_path, dpi=150)
        plt.close(fig)

        doc.close()
        logger.info(f"Text spans visualization saved to {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error in visualize_text_spans: {str(e)}")
        return None


def detect_orientation_parallel(pdf_path, page_range=None, max_workers=None):
    """
    Detect orientation for multiple PDF pages in parallel and return results as a DataFrame.

    Args:
        pdf_path (str): Path to the PDF file
        page_range (tuple, optional): Range of pages to process (start, end) in 1-based numbering.
                                     If None, processes all pages.
        max_workers (int, optional): Maximum number of worker processes.
                                    If None, uses the default based on CPU count.

    Returns:
        pd.DataFrame: DataFrame with columns 'pdf_page', 'apply_rotation', and 'no_rotation_detected'
    """
    # Open the PDF to get page count
    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        doc.close()

        if total_pages == 0:
            logger.warning(f"PDF {pdf_path} has no pages")
            return pd.DataFrame(columns=['pdf_page', 'apply_rotation', 'no_rotation_detected'])

        # Determine page range
        if page_range is None:
            pages_to_process = range(1, total_pages + 1)
        else:
            start, end = page_range
            # Ensure start and end are within valid range
            start = max(1, min(start, total_pages))
            end = max(start, min(end, total_pages))
            pages_to_process = range(start, end + 1)

        logger.info(f"Processing {len(pages_to_process)} pages from PDF {pdf_path}")

        # Process pages in parallel
        results = []

        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Create a list of future objects
            future_to_page = {
                executor.submit(detect_orientation_winsdk, pdf_path, page): page
                for page in pages_to_process
            }

            # Process results as they complete with a progress bar
            for future in tqdm(concurrent.futures.as_completed(future_to_page),
                              total=len(future_to_page),
                              desc="Detecting orientations"):
                page = future_to_page[future]
                try:
                    rotation = future.result()
                    if rotation is None:
                        # Rotation detection failed but didn't raise an exception
                        results.append({
                            'pdf_page': page,
                            'apply_rotation': 0,  # Default to no rotation when detection fails
                            'no_rotation_detected': True  # Mark as failed detection
                        })
                    else:
                        results.append({
                            'pdf_page': page,
                            'apply_rotation': rotation,
                            'no_rotation_detected': None  # Successful detection
                        })
                except Exception as e:
                    logger.error(f"Error processing page {page}: {str(e)}")
                    results.append({
                        'pdf_page': page,
                        'apply_rotation': 0,  # Default to no rotation on error
                        'no_rotation_detected': True  # Mark as failed detection
                    })

        # Convert results to DataFrame and sort by page number
        df = pd.DataFrame(results)
        df = df.sort_values('pdf_page').reset_index(drop=True)

        return df

    except Exception as e:
        logger.error(f"Error in detect_orientation_parallel: {str(e)}")
        return pd.DataFrame(columns=['pdf_page', 'apply_rotation', 'no_rotation_detected'])


# Add a cleanup function for the temp directory
def cleanup_temp_files(max_age_hours=24):
    """
    Clean up old temporary files in the custom temp directory.

    Args:
        max_age_hours (int): Maximum age of files to keep in hours
    """
    try:
        if not os.path.exists(TEMP_DIR):
            return

        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        for filename in os.listdir(TEMP_DIR):
            file_path = os.path.join(TEMP_DIR, filename)

            # Skip directories
            if os.path.isdir(file_path):
                continue

            # Check file age
            file_age = current_time - os.path.getmtime(file_path)

            if file_age > max_age_seconds:
                try:
                    os.remove(file_path)
                    logger.info(f"Cleaned up old temp file: {file_path}")
                except Exception as e:
                    logger.warning(f"Could not delete temp file {file_path}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning up temp files: {str(e)}")

# Register cleanup on exit
import atexit
atexit.register(cleanup_temp_files)


def create_preview_grid(pdf_path, orientation_df=None, grid_size=(4, 5), dpi=32, page_range=None, output_path=None):
    """
    Create a preview PDF with pages arranged in a grid layout for quick visual validation.
    Pages that need rotation are highlighted with colored borders.

    Args:
        pdf_path (str): Path to the PDF file
        orientation_df (pd.DataFrame, optional): DataFrame with orientation results (columns: 'pdf_page', 'apply_rotation')
        grid_size (tuple, optional): Grid dimensions as (columns, rows)
        dpi (int, optional): Resolution for rendering pages (lower = smaller file size)
        page_range (tuple, optional): Range of pages to include (start, end) in 1-based numbering
        output_path (str, optional): Path to save the output PDF. If None, saves in the same directory
                                    with "_preview_grid.pdf" suffix

    Returns:
        str: Path to the created preview PDF or None if failed
    """
    try:
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.patches as patches
        import math

        # Open the PDF
        doc = fitz.open(pdf_path)
        total_pages = len(doc)

        if total_pages == 0:
            logger.warning(f"PDF {pdf_path} has no pages")
            return None

        # Determine page range
        if page_range is None:
            pages_to_process = range(1, total_pages + 1)
        else:
            start, end = page_range
            # Ensure start and end are within valid range
            start = max(1, min(start, total_pages))
            end = max(start, min(end, total_pages))
            pages_to_process = range(start, end + 1)

        # Determine output path
        if output_path is None:
            output_path = os.path.join(
                os.path.dirname(pdf_path),
                f"{os.path.splitext(os.path.basename(pdf_path))[0]}_preview_grid.pdf"
            )

        # Calculate grid parameters
        cols, rows = grid_size
        pages_per_sheet = cols * rows
        num_sheets = math.ceil(len(pages_to_process) / pages_per_sheet)

        logger.info(f"Creating preview grid with {num_sheets} sheets, {pages_per_sheet} pages per sheet")

        # Define colors for different rotations
        rotation_colors = {
            0: 'black',     # No rotation
            90: '#FF5733',  # Red-orange
            180: '#C70039', # Dark red
            270: '#900C3F'  # Purple
        }

        # Create PDF with matplotlib
        with PdfPages(output_path) as pdf:
            for sheet in range(num_sheets):
                # Create a new figure for each sheet
                fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3))
                fig.suptitle(f"Page Preview Grid (Sheet {sheet+1}/{num_sheets})", fontsize=14)

                # Flatten axes array for easier indexing
                if rows > 1 and cols > 1:
                    axes = axes.flatten()
                elif rows == 1:
                    axes = [axes[i] for i in range(cols)]
                elif cols == 1:
                    axes = [axes[i] for i in range(rows)]

                # Fill the grid with pages
                for i in range(pages_per_sheet):
                    page_idx = sheet * pages_per_sheet + i

                    if page_idx < len(pages_to_process):
                        # Get the page number (1-based)
                        page_num = pages_to_process[page_idx]
                        # Convert to 0-based for PyMuPDF
                        pymupdf_page_idx = page_num - 1

                        # Get rotation angle if available
                        rotation = 0
                        if orientation_df is not None:
                            page_data = orientation_df[orientation_df['pdf_page'] == page_num]
                            if not page_data.empty and page_data['apply_rotation'].values[0] is not None:
                                rotation = page_data['apply_rotation'].values[0]

                        # Render the page with rotation
                        page = doc[pymupdf_page_idx]

                        # Create a matrix with rotation
                        matrix = fitz.Matrix(1, 1)  # Low resolution
                        if rotation != 0:
                            matrix.prerotate(rotation)

                        # Render to pixmap
                        pix = page.get_pixmap(matrix=matrix)
                        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                        # Display in grid
                        axes[i].imshow(img)

                        # Determine color based on rotation
                        color = rotation_colors.get(rotation, 'red')  # Default to red if rotation is not in our map

                        # Add colored border for pages with rotation
                        if rotation != 0:
                            # Add a rectangle border with the rotation color
                            rect = patches.Rectangle(
                                (0, 0), 1, 1,
                                linewidth=4,
                                edgecolor=color,
                                facecolor='none',
                                transform=axes[i].transAxes
                            )
                            axes[i].add_patch(rect)

                            # Add background to title for better visibility
                            title_text = f"Page {page_num} (Rot: {rotation}°)"
                            axes[i].set_title(
                                title_text,
                                color='white',
                                fontweight='bold',
                                backgroundcolor=color,
                                pad=8
                            )
                        else:
                            # Regular title for non-rotated pages
                            axes[i].set_title(f"Page {page_num}", color=color)

                        axes[i].axis('off')
                    else:
                        # Empty cell
                        axes[i].axis('off')

                # Add a legend for rotation colors
                legend_elements = [
                    patches.Patch(facecolor='white', edgecolor='black', label='No rotation'),
                    patches.Patch(facecolor=rotation_colors[90], label='90° rotation'),
                    patches.Patch(facecolor=rotation_colors[180], label='180° rotation'),
                    patches.Patch(facecolor=rotation_colors[270], label='270° rotation')
                ]

                # Place legend at the bottom of the figure
                fig.legend(
                    handles=legend_elements,
                    loc='lower center',
                    ncol=4,
                    frameon=True,
                    fancybox=True,
                    shadow=True,
                    fontsize='small'
                )

                # Adjust layout and save to PDF
                plt.tight_layout(rect=[0, 0.05, 1, 0.97])  # Leave space for title and legend
                pdf.savefig(fig)
                plt.close(fig)

        doc.close()
        logger.info(f"Preview grid saved to: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error creating preview grid: {str(e)}")
        return None


def apply_rotations_to_pdf_pypdf(pdf_path, orientation_df, output_path=None):
    """
    Apply detected rotations to a PDF using PyPDF2, which may better preserve embedded data.

    Args:
        pdf_path (str): Path to the PDF file
        orientation_df (pd.DataFrame): DataFrame with orientation results (columns: 'pdf_page', 'apply_rotation')
        output_path (str, optional): Path to save the rotated PDF. If None, saves in the same directory
                                    with "_rotated_pypdf.pdf" suffix

    Returns:
        str: Path to the rotated PDF or None if failed
    """
    try:
        # Import PyPDF2
        try:
            # Try to import from PyPDF first (newer version)
            from pypdf import PdfReader, PdfWriter
            logger.info("Using PyPDF for PDF rotation")
        except ImportError:
            # Fall back to PyPDF2
            from PyPDF2 import PdfFileReader as PdfReader, PdfFileWriter as PdfWriter
            logger.info("Using PyPDF2 for PDF rotation")

        # Determine output path
        if output_path is None:
            output_path = os.path.join(
                os.path.dirname(pdf_path),
                f"{os.path.splitext(os.path.basename(pdf_path))[0]}_rotated_pypdf.pdf"
            )

        # Create a dictionary mapping page numbers to rotation angles
        rotation_dict = {}
        for _, row in orientation_df.iterrows():
            page_num = row['pdf_page']
            rotation = row['apply_rotation']
            if rotation is not None and rotation != 0:
                # Convert from 1-based to 0-based page numbering
                pymupdf_page_idx = page_num - 1
                rotation_dict[pymupdf_page_idx] = rotation

        # Open the PDF
        reader = PdfReader(pdf_path)
        writer = PdfWriter()

        # Process each page
        for i in range(len(reader.pages)):
            page = reader.pages[i]

            # Apply rotation if needed
            if i in rotation_dict:
                rotation_angle = rotation_dict[i]

                # Simply apply the rotation directly
                # PyPDF's rotate() method rotates clockwise, which matches our UI
                page.rotate(rotation_angle)
                logger.info(f"Applied {rotation_angle}° rotation to page {i+1} using PyPDF")

            # Add the page to the writer
            writer.add_page(page)

        # Save the rotated PDF
        with open(output_path, 'wb') as f:
            writer.write(f)

        logger.info(f"Rotated PDF saved to: {output_path} using PyPDF")
        return output_path

    except Exception as e:
        logger.error(f"Error applying rotations with PyPDF: {str(e)}")
        return None


def apply_rotations_to_pdf(pdf_path, orientation_df, output_path=None):
    """
    Apply detected rotations to a PDF and save the result.

    Args:
        pdf_path (str): Path to the PDF file
        orientation_df (pd.DataFrame): DataFrame with orientation results (columns: 'pdf_page', 'apply_rotation')
        output_path (str, optional): Path to save the rotated PDF. If None, saves in the same directory
                                    with "_rotated.pdf" suffix

    Returns:
        str: Path to the rotated PDF or None if failed
    """
    try:
        # Determine output path
        if output_path is None:
            output_path = os.path.join(
                os.path.dirname(pdf_path),
                f"{os.path.splitext(os.path.basename(pdf_path))[0]}_rotated.pdf"
            )

        # Open the PDF
        doc = fitz.open(pdf_path)

        # Apply rotations
        for _, row in orientation_df.iterrows():
            page_num = row['pdf_page']
            rotation = row['apply_rotation']

            if rotation is not None and rotation != 0:
                # Convert to 0-based for PyMuPDF
                pymupdf_page_idx = page_num - 1

                if 0 <= pymupdf_page_idx < len(doc):
                    # Apply rotation
                    page = doc[pymupdf_page_idx]
                    current_rotation = page.rotation
                    # Set the new rotation (PyMuPDF uses multiples of 90 degrees)
                    page.set_rotation((current_rotation + rotation) % 360)
                    logger.info(f"Applied {rotation}° rotation to page {page_num}")

        # Save the rotated PDF
        doc.save(output_path)
        doc.close()

        logger.info(f"Rotated PDF saved to: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error applying rotations: {str(e)}")
        return None


# Add to the main function
if __name__ == "__main__":
    # Clean up old temp files first
    cleanup_temp_files()

    # Example usage
    import sys
    import time

    pdf_path = r"C:\Drawings\Unclean Docs\Combined ISO Yates - Rotation Needed.pdf"
    output_path = os.path.join(os.path.dirname(pdf_path), os.path.basename(pdf_path) + "_rotated.pdf")

    # Detect orientation of a specific page
    pdf_page = 329  # Changed to 1-based page numbering
    try:
        angle = detect_orientation(pdf_path, pdf_page)
        print(f"Detected orientation of page {pdf_page}: {angle} degrees")
    except Exception as e:
        print(f"Error detecting orientation: {str(e)}")

    # Visualize text spans
    try:
        # Set use_winsdk=True to see WinSDK OCR results side by side with PyMuPDF
        vis_path = visualize_text_spans(pdf_path, pdf_page, use_winsdk=True)
        if vis_path:
            print(f"Text spans visualization saved to: {vis_path}")
        else:
            print("Failed to generate visualization")
    except Exception as e:
        print(f"Error visualizing text spans: {str(e)}")

    # Example of parallel processing for multiple pages
    try:
        # Process all pages in the document
        results_df = detect_orientation_parallel(pdf_path)
        print("\nParallel processing results:")
        print(results_df)

        # Save results to CSV
        csv_path = os.path.join(os.path.dirname(pdf_path), "orientation_results.csv")
        results_df.to_csv(csv_path, index=False)
        print(f"Results saved to: {csv_path}")

        # Create preview grid
        preview_path = create_preview_grid(pdf_path, results_df, grid_size=(5, 7), dpi=16)
        if preview_path:
            print(f"Preview grid saved to: {preview_path}")

        # Apply rotations to PDF using both methods for comparison
        rotated_path_pymupdf = apply_rotations_to_pdf(pdf_path, results_df)
        if rotated_path_pymupdf:
            print(f"Rotated PDF (PyMuPDF) saved to: {rotated_path_pymupdf}")

        rotated_path_pypdf = apply_rotations_to_pdf_pypdf(pdf_path, results_df)
        if rotated_path_pypdf:
            print(f"Rotated PDF (PyPDF) saved to: {rotated_path_pypdf}")
    except Exception as e:
        print(f"Error in processing: {str(e)}")