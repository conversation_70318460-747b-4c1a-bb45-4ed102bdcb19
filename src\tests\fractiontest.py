
from fractions import Fraction
import math

v = "3.2"

def getFraction(v):
    dec, whole = math.modf(v)
    print(dec, whole)
    r = Fraction(dec)
    whole = int(whole)
    if dec == 0 and whole == 0:
        r = "0"
    elif whole == 0:
        r = f"{r.numerator}/{r.denominator} {v}"
    else:
        r = f"{int(whole)} {r.numerator}/{r.denominator} {v}"
    return r

getFraction(2)   
getFraction(2.5)
getFraction(1.125)
getFraction(0.2)
getFraction(0.4)
getFraction(0.5)
getFraction(0.3)
getFraction(0.1)
getFraction(0.6)
getFraction(0)


keys = {
    "0": "0",
    "0.1": "1/10",
    "0.2": "1/5",
    "0.3": "3/10",
    "0.4": "1/10",
}