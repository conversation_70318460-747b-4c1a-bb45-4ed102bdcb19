import polars as pl
import numpy as np
from sklearn.cluster import KMeans

from PIL import Image, ImageDraw

def visualize_table_dynamic(df: pl.DataFrame, buffer=10, save_path=None):
    """
    Create a blank image sized dynamically based on bounding boxes.
    Coordinates normalized to start at (0,0) with optional buffer.

    df: Polars DataFrame with x0,x1,y0,y1
    buffer: pixels to pad around the bounding boxes
    save_path: if provided, saves the image
    """
    # Compute dynamic canvas size
    x_min = df["x0"].min() - buffer
    x_max = df["x1"].max() + buffer
    y_min = df["y0"].min() - buffer
    y_max = df["y1"].max() + buffer

    width = int(x_max - x_min)
    height = int(y_max - y_min)

    # Create blank image
    img = Image.new("RGB", (width, height), color="white")
    draw = ImageDraw.Draw(img)

    # Draw rectangles, normalized to start at (0,0)
    for r in df.iter_rows():
        x0, x1 = r[df.columns.index("x0")] - x_min, r[df.columns.index("x1")] - x_min
        y0, y1 = r[df.columns.index("y0")] - y_min, r[df.columns.index("y1")] - y_min
        rect = [x0, y0, x1, y1]
        draw.rectangle(rect, outline="red", width=2)

    if save_path:
        img.save(save_path)

    return img

def infer_table_structure(df: pl.DataFrame, tol_x=5, tol_y=5):
    """
    Infer table structure: assigns each box to a row and column.
    Robust to noise in the far left.

    df: Polars DataFrame with columns x0,x1,y0,y1
    tol_x: horizontal tolerance for column alignment
    tol_y: vertical tolerance for row alignment
    """
    # Compute centers
    df = df.with_columns([
        ((pl.col("x0") + pl.col("x1")) / 2).alias("x_center"),
        ((pl.col("y0") + pl.col("y1")) / 2).alias("y_center")
    ])

    # Sort by x_center
    df_sorted = df.sort("x_center")
    x_centers = df_sorted["x_center"].to_numpy()

    # Step 1: 1D clustering of x_center to find first column
    clusters = []
    current_cluster = [0]  # store indices
    for i in range(1, len(x_centers)):
        if x_centers[i] - x_centers[i-1] <= tol_x:
            current_cluster.append(i)
        else:
            clusters.append(current_cluster)
            current_cluster = [i]
    clusters.append(current_cluster)

    # First column = leftmost cluster with most boxes
    first_col_cluster = max(clusters, key=lambda c: len(c))
    first_col_indices = [df_sorted[i, 0] for i in first_col_cluster]  # original indices

    first_col = df.filter(pl.arange(0, df.height).is_in(first_col_indices))
    first_col = first_col.with_columns([pl.lit(0).alias("column")])

    # Step 2: assign rows based on y_center of first column
    first_col_sorted = first_col.sort("y_center")
    rows = []
    row_id = 0
    current_y = None
    for r in first_col_sorted.iter_rows():
        y_center = r[df.columns.index("y_center")]
        if current_y is None or abs(y_center - current_y) > tol_y:
            row_id += 1
            current_y = y_center
        rows.append((r[0], row_id))

    row_map = {idx: rid for idx, rid in rows}

    # Step 3: assign columns to other boxes
    other_boxes = df.filter(~pl.arange(0, df.height).is_in(first_col_indices))

    # Simple 1D binning for other columns
    all_columns = [first_col]
    col_id = 1
    for cluster in clusters:
        # skip first column
        if set(cluster).intersection(first_col_indices):
            continue
        cluster_indices = [df_sorted[i, 0] for i in cluster]
        cluster_boxes = df.filter(pl.arange(0, df.height).is_in(cluster_indices))
        cluster_boxes = cluster_boxes.with_columns([pl.lit(col_id).alias("column")])
        all_columns.append(cluster_boxes)
        col_id += 1

    df_out = pl.concat(all_columns)

    # Step 4: assign row ids to all boxes based on closest first column row
    first_col_rows = df_out.filter(pl.col("column") == 0)
    row_assignments = []
    for r in df_out.iter_rows():
        y_center = r[df.columns.index("y_center")]
        # find closest first column row
        closest_row = min(first_col_rows.iter_rows(), key=lambda x: abs(x[df.columns.index("y_center")] - y_center))
        row_id = row_map[closest_row[0]]
        row_assignments.append(row_id)

    df_out = df_out.with_columns([pl.Series("row", row_assignments)])

    # Sort nicely
    df_out = df_out.sort(["row", "column"])

    return df_out

from collections import defaultdict
from collections import Counter

def infer_possible_columns(df: pl.DataFrame, tol_x=10):
    """
    Group boxes into candidate columns based on x_center proximity.
    Return the groups and the most common item count.

    df: Polars DataFrame with x0,x1
    tol_x: horizontal tolerance for column grouping
    """
    # Compute x_center
    df = df.with_columns(((pl.col("x0")+pl.col("x1"))/2).alias("x_center"))
    df = df.with_row_index("row")

    # Sort by x_center
    df_sorted = df.sort("x_center")
    x_centers = df_sorted["x_center"].to_numpy()

    # 1D clustering on x_center
    clusters = []
    current_cluster = [0]  # store row indices
    for i in range(1, len(x_centers)):
        if x_centers[i] - x_centers[i-1] <= tol_x:
            current_cluster.append(i)
        else:
            clusters.append(current_cluster)
            current_cluster = [i]
    clusters.append(current_cluster)

    # Map clusters to actual DataFrame rows
    column_groups = []
    for cluster in clusters:
        # cluster contains indices into df_sorted, not original row indices
        # We need to get the actual rows from df_sorted using these indices
        column_groups.append(cluster)  # Keep the df_sorted indices

    # Count items per column
    item_counts = [len(g) for g in column_groups]
    most_common_count = Counter(item_counts).most_common(1)[0][0] if item_counts else 0

    print("most_common_count", most_common_count)

    print("column_groups", [c for c in column_groups if len(c) == most_common_count])
    print("column_groups_count", len([c for c in column_groups if len(c) == most_common_count]))

    structure = []
    print(222)
    column_no = 0
    for c in column_groups:
        # if len(c) != most_common_count:
        #     continue
        # c contains indices into df_sorted, so we can use them directly
        indices = c
        # Get the rows from df_sorted using the cluster indices
        column_data = df_sorted[indices].sort("y0")
        values = column_data["value"].to_list()
        # get the values of the column
        # print(values)
        print(f"Column no. {column_no}", values, "cluster count", len(c))
        structure.append({"column": column_no, "values": values})
        column_no += 1
        # break

    # merge into row
    new_df = pl.DataFrame(structure)
    new_df.write_excel("debug/extraction_results/inferred_table_structure.xlsx")

    return column_groups, most_common_count

def infer_possible_rows(df: pl.DataFrame, tol_y=10):
    """
    Group boxes into candidate rows based on y_center proximity.
    Return the groups and the most common item count.

    df: Polars DataFrame with y0,y1
    tol_y: vertical tolerance for row grouping
    """
    # Compute y_center
    df = df.with_columns(((pl.col("y0")+pl.col("y1"))/2).alias("y_center"))
    df = df.with_row_index("row")

    # Sort by y_center
    df_sorted = df.sort("y_center")
    y_centers = df_sorted["y_center"].to_numpy()

    # 1D clustering on y_center
    clusters = []
    current_cluster = [0]  # store row indices
    for i in range(1, len(y_centers)):
        if y_centers[i] - y_centers[i-1] <= tol_y:
            current_cluster.append(i)
        else:
            clusters.append(current_cluster)
            current_cluster = [i]
    clusters.append(current_cluster)

    # Map clusters to actual DataFrame rows
    row_groups = []
    for cluster in clusters:
        # cluster contains indices into df_sorted, not original row indices
        # We need to get the actual rows from df_sorted using these indices
        row_groups.append(cluster)  # Keep the df_sorted indices

    # Count items per row
    item_counts = [len(g) for g in row_groups]
    most_common_count = Counter(item_counts).most_common(1)[0][0] if item_counts else 0

    print("most_common_count", most_common_count)

    print("row_groups", [c for c in row_groups if len(c) == most_common_count])
    print("row_groups_count", len([c for c in row_groups if len(c) == most_common_count]))

    structure = []
    print(333)
    row_no = 0
    for c in row_groups:
        # if len(c) != most_common_count:
        #     continue
        # c contains indices into df_sorted, so we can use them directly
        indices = c
        # Get the rows from df_sorted using the cluster indices
        row_data = df_sorted[indices].sort("x0")
        values = row_data["value"].to_list()
        # get the values of the row
        # print(values)
        if len(c) != 5:
            continue
        print(f"Row no. {row_no}", values, "cluster count", len(c))
        structure.append({"row": row_no, "values": values})
        row_no += 1
        # break

    # merge into row
    new_df = pl.DataFrame(structure)
    new_df.write_excel("debug/extraction_results/inferred_row_structure.xlsx")

    return row_groups, most_common_count

if __name__ == "__main__":
    import time

    df = pl.read_excel("debug/extraction_results/raw_table_df.xlsx")
    # res_df = infer_table_structure(df)
    # res_df.write_excel("debug/extraction_results/inferred_table_structure.xlsx")
    # visualize_table_dynamic(df, save_path="debug/extraction_results/inferred_table_structure.png")

    start = time.time()
    infer_possible_columns(df)
    infer_possible_rows(df, tol_y=1)
    end = time.time()
    print("Time taken:", end - start)
    # print(infer_possible_columns(df))