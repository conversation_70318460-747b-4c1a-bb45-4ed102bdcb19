import sys
import fitz  # PyMuPDF
from PySide6.QtWidgets import (
    QApplication, QWidget, QLabel, QVBoxLayout, QPushButton, QHBoxLayout
)
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt


class PDFViewer(QWidget):
    def __init__(self, pdf_path):
        super().__init__()
        self.setWindowTitle("PySide6 PDF Viewer")

        # Load PDF
        self.doc = fitz.open(pdf_path)
        self.page_index = 0

        # UI
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)

        self.prev_btn = QPushButton("Previous")
        self.next_btn = QPushButton("Next")

        self.prev_btn.clicked.connect(self.prev_page)
        self.next_btn.clicked.connect(self.next_page)

        nav_layout = QHBoxLayout()
        nav_layout.addWidget(self.prev_btn)
        nav_layout.addWidget(self.next_btn)

        layout = QVBoxLayout()
        layout.addWidget(self.image_label)
        layout.addLayout(nav_layout)
        self.setLayout(layout)

        self.render_page()

    def render_page(self, dpi=150):
        page = self.doc[self.page_index]
        zoom = dpi / 72
        mat = fitz.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=mat)

        # Convert Pixmap to QImage
        mode = QImage.Format_RGB888 if pix.alpha == 0 else QImage.Format_RGBA8888
        img = QImage(pix.samples, pix.width, pix.height, pix.stride, mode)
        pixmap = QPixmap.fromImage(img)
        self.image_label.setPixmap(pixmap)
        self.setWindowTitle(f"PySide6 PDF Viewer - Page {self.page_index + 1}/{len(self.doc)}")
        pix = None

    def next_page(self):
        if self.page_index < len(self.doc) - 1:
            self.page_index += 1
            self.render_page()

    def prev_page(self):
        if self.page_index > 0:
            self.page_index -= 1
            self.render_page()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    viewer = PDFViewer("C:/Drawings/Clients/axisindustries/Axis 2025-08-14 - ENTERPRISE/received/Binder1 (4).pdf")  # replace with your PDF path
    viewer.resize(800, 1000)
    viewer.show()
    sys.exit(app.exec())
