"""
Bare minimum pdf viewer

"""
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QWidget, QSplitter, QVBoxLayout, QGraphicsRectItem, QGraphicsScene, QGraphicsView)
from PySide6.QtGui import QPixmap, QImage
import fitz
from src.utils.logger import logger

# This logger will inherit configurations from the root logger configured in main.py
# logger = logging.getLogger(__name__)

# logger.debug("Logger Format?")


class PdfViewer(QGraphicsView):
    """ PDF Viewer """

    sgnLoadPdf = Signal(str)
    sgnPdfLoaded = Signal(bool) # Success (True) / Fail (False)
    sgnRoiAdded = Signal(object)
    sgnRoiDrawChanged = Signal(bool) # Enabled/Disabled (True/False)
    sgnSetContextMenu = Signal(object)
    sgnPdfChanged = Signal()
    sgnUpdateStatus = Signal(str, object)
    sgnZoomUpdated = Signal(float)
    sgnShowEditRoiDialog = Signal(object, bool)

    roiMotionListener = None

    # dpi = 1500
    dpi = 144

    maxScale = 4
    minScale = 0.1

    def __init__(self, parent):
        super().__init__()
        self.setScene(QGraphicsScene())
        self.parent = parent
        self.pages: int = None
        self.page = None
        self.pageWidth = None
        self.pageHeight = None
        self._roiDrawEnabled: bool = False
        self.roiStart, self.roiEnd = None, None
        self.previewRoi: QGraphicsRectItem = None
        self._scaleDelta: float = 1
        self.setDragMode(self.DragMode.NoDrag)
        self.sgnLoadPdf.connect(self.loadPdf)
        self.show()

            
    def closePdf(self):
        try:
            self._pdf = None
            self.roiMotionListener = None
        except Exception as e:
            logger.error(f"Error closing PDF: {e}", exc_info=True)

    def setPage(self, page: int):
        try:
            assert page >= 0
            self.page = page
            zoom = self.dpi / 72  # Default DPI in PyMuPDF is 72
            mat = fitz.Matrix(zoom, zoom)
            page = self._pdf.load_page(0)  # Load the first page
            pix = page.get_pixmap(matrix=mat) # dpi=1200
            qt_img = QImage(pix.samples, pix.width, pix.height, pix.stride, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qt_img)
            self.pageWidth, self.pageHeight = pix.width, pix.height
            self.scene().clear()
            self.setScene(QGraphicsScene())
            self.pageItem = self.scene().addPixmap(pixmap)
            self.pageItem.setZValue(0)
            # TODO: self.sgnPdfPageChanged.emit
        except Exception as e:
            logger.error(f"Error setting page to {page}: {e}", exc_info=True)


    def loadPdf(self, filename, page=1):
        try:
            self.closePdf()
            self._pdf = fitz.open(filename)
            self.pages = self._pdf.page_count if self._pdf.page_count else None
            self.setPage(0)
            
            #Store The successfully loaded PDF Path #Added
            self.currentPdfPath = filename  
    
            # Update UI to allow PDF editing
            self.sgnPdfLoaded.emit(True)

            # Cleanup - Potential crashes caused by keeping instance of opened PyMuPDF
            self._pdf = None
        except Exception as e:
            logger.error(f"Failed to load PDF: {filename}. Error: {e}", exc_info=True)
            self.sgnPdfLoaded.emit(False)

            

class BasicPdfViewer(QWidget):

    def __init__(self, parent):
        super().__init__(parent=parent)
        self.setLayout(QVBoxLayout())

        # Siderbar and PDF Viewer
        self.splitter = QSplitter(self)
        self.splitter.setHandleWidth(0)
        self.pdfViewer = PdfViewer(self)
        self.layout().addWidget(self.pdfViewer)

        # autoload pdf
        self.loadPdf("/home/<USER>/Desktop/test pages.pdf")
          
    def loadPdf(self, filename):
        try:
            self.pdfViewer.sgnLoadPdf.emit(filename)
        except Exception as e:
            logger.error(f"Error emitting loadpdf signal: {e}", exc_info=True)


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    app.setApplicationName("ATEM")
    window = BasicPdfViewer(None)
    window.show()
    app.exec()