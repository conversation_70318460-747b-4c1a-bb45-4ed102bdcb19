"""
Data auditing utilities for validating data before PostgreSQL uploads.

This module provides functions to audit and validate data quality,
check for required fields, and identify potential issues before uploading.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import re


class DataAuditor:
    """
    Audits data quality and validates data before PostgreSQL uploads.
    """
    
    def __init__(self, table_name: str):
        """
        Initialize the DataAuditor for a specific table.
        
        Args:
            table_name: Name of the table ('general', 'bom', or 'rfq')
        """
        self.table_name = table_name.lower()
        self.audit_results = {}
        
        # Define required fields for each table
        self.required_fields = {
            'general': ['project_id', 'pdf_id'],
            'bom': ['project_id', 'pdf_id', 'material_description'],
            'rfq': ['project_id', 'material_description']
        }
        
        # Define data type expectations
        self.expected_types = {
            'general': {
                'id': 'int',
                'pdf_id': 'int', 
                'project_id': 'int',
                'length': 'float',
                'calculated_area': 'float',
                'calculated_eq_length': 'float',
                'line_number': 'str',
                'iso_number': 'str',
                'size': 'str',
                'elevation': 'float',
                'elbows_90': 'int',
                'elbows_45': 'int',
                'tees': 'int',
                'reducers': 'int',
                'flanges': 'int',
                'valves_flanged': 'int',
                'valves_welded': 'int'
            },
            'bom': {
                'id': 'int',
                'pdf_id': 'int',
                'project_id': 'int',
                'quantity': 'float',
                'calculated_eq_length': 'float',
                'calculated_area': 'float',
                'material_description': 'str',
                'size': 'str',
                'component_category': 'str'
            },
            'rfq': {
                'id': 'int',
                'project_id': 'int',
                'quantity': 'float',
                'material_description': 'str',
                'size': 'str',
                'unit_of_measure': 'str'
            }
        }
    
    def audit_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform comprehensive audit of a DataFrame.
        
        Args:
            df: DataFrame to audit
            
        Returns:
            Dictionary containing audit results
        """
        results = {
            'timestamp': datetime.now().isoformat(),
            'table_name': self.table_name,
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'issues': [],
            'warnings': [],
            'summary': {}
        }
        
        # Check for empty DataFrame
        if df.empty:
            results['issues'].append("DataFrame is empty")
            return results
        
        # Check required fields
        missing_required = self._check_required_fields(df)
        if missing_required:
            results['issues'].extend([f"Missing required field: {field}" for field in missing_required])
        
        # Check for duplicate rows
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            results['warnings'].append(f"Found {duplicate_count} duplicate rows")
        
        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            results['warnings'].append(f"Found {empty_rows} completely empty rows")
        
        # Check data types
        type_issues = self._check_data_types(df)
        if type_issues:
            results['warnings'].extend(type_issues)
        
        # Check for null values in important fields
        null_issues = self._check_null_values(df)
        if null_issues:
            results['warnings'].extend(null_issues)
        
        # Check for suspicious values
        suspicious_issues = self._check_suspicious_values(df)
        if suspicious_issues:
            results['warnings'].extend(suspicious_issues)
        
        # Generate summary statistics
        results['summary'] = self._generate_summary(df)
        
        self.audit_results = results
        return results
    
    def _check_required_fields(self, df: pd.DataFrame) -> List[str]:
        """Check if all required fields are present."""
        required = self.required_fields.get(self.table_name, [])
        missing = [field for field in required if field not in df.columns]
        return missing
    
    def _check_data_types(self, df: pd.DataFrame) -> List[str]:
        """Check if data types match expectations."""
        issues = []
        expected = self.expected_types.get(self.table_name, {})
        
        for column, expected_type in expected.items():
            if column in df.columns:
                actual_dtype = str(df[column].dtype)
                
                # Check type compatibility
                if expected_type == 'int' and not pd.api.types.is_integer_dtype(df[column]):
                    # Allow float if all values are whole numbers or NaN
                    if pd.api.types.is_float_dtype(df[column]):
                        non_null_values = df[column].dropna()
                        if not non_null_values.empty and not all(val.is_integer() for val in non_null_values):
                            issues.append(f"Column '{column}' expected int but contains non-integer floats")
                    else:
                        issues.append(f"Column '{column}' expected int but is {actual_dtype}")
                
                elif expected_type == 'float' and not pd.api.types.is_numeric_dtype(df[column]):
                    issues.append(f"Column '{column}' expected float but is {actual_dtype}")
                
                elif expected_type == 'str' and not pd.api.types.is_object_dtype(df[column]):
                    issues.append(f"Column '{column}' expected string but is {actual_dtype}")
        
        return issues
    
    def _check_null_values(self, df: pd.DataFrame) -> List[str]:
        """Check for null values in important fields."""
        issues = []
        required = self.required_fields.get(self.table_name, [])
        
        for field in required:
            if field in df.columns:
                null_count = df[field].isnull().sum()
                if null_count > 0:
                    issues.append(f"Required field '{field}' has {null_count} null values")
        
        return issues
    
    def _check_suspicious_values(self, df: pd.DataFrame) -> List[str]:
        """Check for suspicious or potentially problematic values."""
        issues = []
        
        # Check for negative quantities
        quantity_cols = ['quantity', 'length', 'calculated_area', 'calculated_eq_length']
        for col in quantity_cols:
            if col in df.columns and pd.api.types.is_numeric_dtype(df[col]):
                negative_count = (df[col] < 0).sum()
                if negative_count > 0:
                    issues.append(f"Column '{col}' has {negative_count} negative values")
        
        # Check for extremely large values (potential data entry errors)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col in df.columns:
                q99 = df[col].quantile(0.99)
                q01 = df[col].quantile(0.01)
                outliers = ((df[col] > q99 * 100) | (df[col] < q01 / 100)).sum()
                if outliers > 0:
                    issues.append(f"Column '{col}' has {outliers} potential outliers")
        
        # Check for suspicious text patterns
        text_cols = df.select_dtypes(include=['object']).columns
        for col in text_cols:
            if col in df.columns:
                # Check for very long strings (potential data corruption)
                long_strings = (df[col].astype(str).str.len() > 500).sum()
                if long_strings > 0:
                    issues.append(f"Column '{col}' has {long_strings} unusually long text values")
                
                # Check for special characters that might cause issues
                special_chars = df[col].astype(str).str.contains(r'[^\w\s\-\.\,\(\)]', na=False).sum()
                if special_chars > 0:
                    issues.append(f"Column '{col}' has {special_chars} values with special characters")
        
        return issues
    
    def _generate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate summary statistics for the DataFrame."""
        summary = {
            'row_count': len(df),
            'column_count': len(df.columns),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
            'null_percentage': (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
            'duplicate_rows': df.duplicated().sum(),
            'columns_with_nulls': df.isnull().any().sum(),
            'numeric_columns': len(df.select_dtypes(include=[np.number]).columns),
            'text_columns': len(df.select_dtypes(include=['object']).columns)
        }
        
        return summary
    
    def print_audit_report(self) -> None:
        """Print a formatted audit report."""
        if not self.audit_results:
            print("No audit results available. Run audit_dataframe() first.")
            return
        
        results = self.audit_results
        
        print("=" * 60)
        print(f"DATA AUDIT REPORT - {results['table_name'].upper()} TABLE")
        print("=" * 60)
        print(f"Audit Time: {results['timestamp']}")
        print(f"Total Rows: {results['total_rows']:,}")
        print(f"Total Columns: {results['total_columns']}")
        print()
        
        # Summary
        summary = results['summary']
        print("SUMMARY:")
        print(f"  Memory Usage: {summary['memory_usage_mb']:.2f} MB")
        print(f"  Null Percentage: {summary['null_percentage']:.2f}%")
        print(f"  Duplicate Rows: {summary['duplicate_rows']}")
        print(f"  Columns with Nulls: {summary['columns_with_nulls']}")
        print(f"  Numeric Columns: {summary['numeric_columns']}")
        print(f"  Text Columns: {summary['text_columns']}")
        print()
        
        # Issues
        if results['issues']:
            print("CRITICAL ISSUES:")
            for issue in results['issues']:
                print(f"  ❌ {issue}")
            print()
        
        # Warnings
        if results['warnings']:
            print("WARNINGS:")
            for warning in results['warnings']:
                print(f"  ⚠️  {warning}")
            print()
        
        # Overall assessment
        if not results['issues'] and not results['warnings']:
            print("✅ DATA QUALITY: EXCELLENT - No issues found")
        elif not results['issues']:
            print("⚠️  DATA QUALITY: GOOD - Minor warnings only")
        else:
            print("❌ DATA QUALITY: POOR - Critical issues found")
        
        print("=" * 60)
    
    def get_clean_dataframe(self, df: pd.DataFrame, 
                           remove_duplicates: bool = True,
                           remove_empty_rows: bool = True) -> pd.DataFrame:
        """
        Return a cleaned version of the DataFrame.
        
        Args:
            df: Original DataFrame
            remove_duplicates: Whether to remove duplicate rows
            remove_empty_rows: Whether to remove completely empty rows
            
        Returns:
            Cleaned DataFrame
        """
        df_clean = df.copy()
        
        if remove_empty_rows:
            df_clean = df_clean.dropna(how='all')
        
        if remove_duplicates:
            df_clean = df_clean.drop_duplicates()
        
        return df_clean


def quick_audit(df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
    """
    Convenience function for quick data auditing.
    
    Args:
        df: DataFrame to audit
        table_name: Name of the table ('general', 'bom', or 'rfq')
        
    Returns:
        Audit results dictionary
    """
    auditor = DataAuditor(table_name)
    return auditor.audit_dataframe(df)


if __name__ == "__main__":
    # Example usage
    print("Data Auditor Example")
    print("=" * 30)
    
    # Create sample data for testing
    sample_data = pd.DataFrame({
        'project_id': [1, 2, 3, None, 5],
        'pdf_id': [101, 102, 103, 104, 105],
        'material_description': ['Pipe', 'Valve', 'Fitting', 'Pipe', 'Elbow'],
        'quantity': [10.5, 2.0, 1.0, -5.0, 3.0],  # Note: negative value
        'size': ['2"', '4"', '1"', '2"', '3"']
    })
    
    # Audit the sample data
    auditor = DataAuditor('bom')
    results = auditor.audit_dataframe(sample_data)
    auditor.print_audit_report()
