"""
LangGraph Main Workflow Orchestration

This module implements the core LangGraph workflow for BOM classification,
orchestrating the multi-stage classification process with state management.
"""

import asyncio
import time
from typing import Dict, Any, Optional

# LangGraph imports
try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    print("Warning: LangGraph not available - using mock implementation")

# Handle imports for both direct execution and module import
try:
    from .state_models import ClassificationState, WorkflowResult
    from .classification_nodes import (
        material_analysis_node,
        fitting_classification_node,
        pipe_classification_node,
        qa_decision_node,
        self_audit_node
    )
    from .integration_layer import get_model_handler
except ImportError:
    # Direct execution - use absolute imports
    from state_models import ClassificationState, WorkflowResult
    from classification_nodes import (
        material_analysis_node,
        fitting_classification_node,
        pipe_classification_node,
        qa_decision_node,
        self_audit_node
    )
    from integration_layer import get_model_handler


# Development flags
ENABLE_WORKFLOW_CHECKPOINTS = True
ENABLE_PARALLEL_PROCESSING = True
DEBUG_WORKFLOW = True


def route_to_category_node(state: ClassificationState) -> str:
    """
    Route to appropriate category-specific classification node
    
    This function determines which category-specific node should process
    the item based on the material analysis results.
    """
    
    processing_path = state.get("processing_path", "miscellaneous")
    
    # Map processing paths to node names
    category_routing = {
        "pipe": "pipe_classification",
        "fitting": "fitting_classification",
        "valve": "valve_classification",  # TODO: Implement valve node
        "flange": "flange_classification",  # TODO: Implement flange node
        "gasket": "gasket_classification",  # TODO: Implement gasket node
        "bolt": "bolt_classification",  # TODO: Implement bolt node
        "support": "support_classification",  # TODO: Implement support node
        "miscellaneous": "miscellaneous_classification"  # TODO: Implement misc node
    }
    
    node_name = category_routing.get(processing_path, "miscellaneous_classification")
    
    if DEBUG_WORKFLOW:
        print(f"   🔀 Routing {processing_path} → {node_name}")
    
    return node_name


def should_continue_to_audit(state: ClassificationState) -> str:
    """
    Determine if self-audit stage is needed
    
    This function checks confidence scores and other factors to decide
    whether the self-audit stage should be executed.
    """
    
    confidence_scores = state.get("confidence_scores", {})
    
    # Check if any field has low confidence
    low_confidence_threshold = 0.8
    has_low_confidence = any(
        confidence is not None and confidence < low_confidence_threshold
        for confidence in confidence_scores.values()
    )
    
    # Check if there are existing issues
    has_issues = bool(state.get("identified_issues", []))
    
    # Always run audit if low confidence or existing issues
    if has_low_confidence or has_issues:
        if DEBUG_WORKFLOW:
            print(f"   🔍 Continuing to self-audit (low confidence: {has_low_confidence}, issues: {has_issues})")
        return "continue"
    
    if DEBUG_WORKFLOW:
        print(f"   ✅ Skipping self-audit (all confidence scores > {low_confidence_threshold})")
    return "end"


# Placeholder nodes for categories not yet implemented
async def valve_classification_node(state: ClassificationState) -> ClassificationState:
    """Placeholder for valve classification - TODO: Implement"""
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Valves", "valve_type": "Unknown"},
        "confidence_scores": {"rfq_scope": 0.80, "valve_type": 0.60},
        "workflow_path": state["workflow_path"] + ["valve_classification_placeholder"]
    }


async def flange_classification_node(state: ClassificationState) -> ClassificationState:
    """Placeholder for flange classification - TODO: Implement"""
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Flanges", "fitting_category": "Unknown Flange"},
        "confidence_scores": {"rfq_scope": 0.80, "fitting_category": 0.60},
        "workflow_path": state["workflow_path"] + ["flange_classification_placeholder"]
    }


async def gasket_classification_node(state: ClassificationState) -> ClassificationState:
    """Placeholder for gasket classification - TODO: Implement"""
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Gaskets", "gasket_category": "Unknown"},
        "confidence_scores": {"rfq_scope": 0.80, "gasket_category": 0.60},
        "workflow_path": state["workflow_path"] + ["gasket_classification_placeholder"]
    }


async def bolt_classification_node(state: ClassificationState) -> ClassificationState:
    """Placeholder for bolt classification - TODO: Implement"""
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Bolts", "bolt_category": "Unknown"},
        "confidence_scores": {"rfq_scope": 0.80, "bolt_category": 0.60},
        "workflow_path": state["workflow_path"] + ["bolt_classification_placeholder"]
    }


async def support_classification_node(state: ClassificationState) -> ClassificationState:
    """Placeholder for support classification - TODO: Implement"""
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Supports", "support_category": "Unknown"},
        "confidence_scores": {"rfq_scope": 0.80, "support_category": 0.60},
        "workflow_path": state["workflow_path"] + ["support_classification_placeholder"]
    }


async def miscellaneous_classification_node(state: ClassificationState) -> ClassificationState:
    """Handle miscellaneous items (vendor codes, unidentifiable items)"""
    
    if DEBUG_WORKFLOW:
        print(f"\n🔧 STAGE 2: Miscellaneous Classification")
        print(f"   Description: {state['material_description']}")
    
    # Simple classification for miscellaneous items
    return {
        **state,
        "current_stage": "qa_decisions",
        "field_classifications": {"rfq_scope": "Miscellaneous"},
        "confidence_scores": {"rfq_scope": 0.95},
        "workflow_path": state["workflow_path"] + ["miscellaneous_classification"]
    }


def create_classification_workflow(config: Optional[Any] = None):
    """
    Create the LangGraph classification workflow
    
    This function builds the complete workflow graph with all nodes,
    edges, and routing logic for the multi-stage classification process.
    """
    
    if not LANGGRAPH_AVAILABLE:
        # Return mock workflow for testing
        return MockWorkflow()
    
    # Initialize workflow with state management
    workflow = StateGraph(ClassificationState)

    # Add checkpoint saver for state persistence (if supported)
    if ENABLE_WORKFLOW_CHECKPOINTS:
        try:
            memory = MemorySaver()
            # Try to add checkpointer if the API supports it
            if hasattr(workflow, 'with_config'):
                workflow = workflow.with_config({"checkpointer": memory})
            elif hasattr(workflow, 'add_checkpointer'):
                workflow.add_checkpointer(memory)
            else:
                print("Warning: Checkpointing not supported in this LangGraph version")
        except Exception as e:
            print(f"Warning: Could not enable checkpointing: {e}")
    
    # Add all nodes
    workflow.add_node("material_analysis", material_analysis_node)
    workflow.add_node("pipe_classification", pipe_classification_node)
    workflow.add_node("fitting_classification", fitting_classification_node)
    workflow.add_node("valve_classification", valve_classification_node)
    workflow.add_node("flange_classification", flange_classification_node)
    workflow.add_node("gasket_classification", gasket_classification_node)
    workflow.add_node("bolt_classification", bolt_classification_node)
    workflow.add_node("support_classification", support_classification_node)
    workflow.add_node("miscellaneous_classification", miscellaneous_classification_node)
    workflow.add_node("qa_decisions", qa_decision_node)
    workflow.add_node("self_audit", self_audit_node)
    
    # Set entry point
    workflow.set_entry_point("material_analysis")
    
    # Add conditional edges for category routing
    workflow.add_conditional_edges(
        "material_analysis",
        route_to_category_node,
        {
            "pipe_classification": "pipe_classification",
            "fitting_classification": "fitting_classification",
            "valve_classification": "valve_classification",
            "flange_classification": "flange_classification",
            "gasket_classification": "gasket_classification",
            "bolt_classification": "bolt_classification",
            "support_classification": "support_classification",
            "miscellaneous_classification": "miscellaneous_classification"
        }
    )
    
    # Add edges from category nodes to Q&A decisions
    category_nodes = [
        "pipe_classification",
        "fitting_classification", 
        "valve_classification",
        "flange_classification",
        "gasket_classification",
        "bolt_classification",
        "support_classification",
        "miscellaneous_classification"
    ]
    
    for node in category_nodes:
        workflow.add_edge(node, "qa_decisions")
    
    # Add conditional edge for Q&A decisions to self-audit
    workflow.add_conditional_edges(
        "qa_decisions",
        should_continue_to_audit,
        {
            "continue": "self_audit",
            "end": END
        }
    )
    
    # Add final edge from self-audit to end
    workflow.add_edge("self_audit", END)
    
    # Compile workflow
    compiled_workflow = workflow.compile()
    
    if DEBUG_WORKFLOW:
        print("✓ LangGraph workflow compiled successfully")
        print(f"  Nodes: {len(workflow.nodes)}")
        print(f"  Checkpoints enabled: {ENABLE_WORKFLOW_CHECKPOINTS}")
    
    return compiled_workflow


class MockWorkflow:
    """Mock workflow for testing when LangGraph is not available"""
    
    async def ainvoke(self, state: ClassificationState) -> ClassificationState:
        """Mock workflow execution"""
        
        print("🔄 Running mock LangGraph workflow...")
        
        # Simulate workflow execution
        state = await material_analysis_node(state)
        
        processing_path = state["processing_path"]
        if processing_path == "fitting":
            state = await fitting_classification_node(state)
        elif processing_path == "pipe":
            state = await pipe_classification_node(state)
        else:
            state = await miscellaneous_classification_node(state)
        
        state = await qa_decision_node(state)
        state = await self_audit_node(state)
        
        return state


if __name__ == "__main__":
    """Test LangGraph workflow"""
    
    print("Testing LangGraph BOM Classification Workflow")
    print("=" * 60)
    
    try:
        from .state_models import create_initial_state
    except ImportError:
        from state_models import create_initial_state
    
    # Test data
    test_cases = [
        {
            "description": "Pipe Nipple 2\" SCH 40 ASTM A106 BE",
            "expected_path": "fitting"
        },
        {
            "description": "90 LR Elbow 4\" ASTM A234 WPB",
            "expected_path": "fitting"
        },
        {
            "description": "Pipe SMLS 6\" SCH 40 ASTM A106",
            "expected_path": "pipe"
        },
        {
            "description": "VENDOR ASYM18456",
            "expected_path": "miscellaneous"
        }
    ]
    
    async def test_complete_workflow():
        """Test the complete workflow with various inputs"""
        
        # Create workflow
        workflow = create_classification_workflow()
        
        for i, test_case in enumerate(test_cases):
            print(f"\n📝 Test Case {i+1}: {test_case['description']}")
            print("-" * 50)
            
            # Create initial state
            initial_state = create_initial_state(
                item_id=f"test_{i+1:03d}",
                material_description=test_case["description"],
                original_classification={},
                debug_mode=True
            )
            
            start_time = time.time()
            
            # Run workflow
            final_state = await workflow.ainvoke(initial_state)
            
            execution_time = time.time() - start_time
            
            # Analyze results
            print(f"\n   ✅ Workflow Results:")
            print(f"      Expected path: {test_case['expected_path']}")
            print(f"      Actual path: {final_state.get('processing_path', 'unknown')}")
            print(f"      Workflow stages: {' → '.join(final_state.get('workflow_path', []))}")
            print(f"      Execution time: {execution_time:.2f}s")
            print(f"      Total processing time: {final_state.get('processing_time', 0):.2f}s")
            print(f"      Model calls: {final_state.get('model_calls', 0)}")
            print(f"      Tokens used: {final_state.get('tokens_used', 0)}")
            print(f"      Final stage: {final_state.get('current_stage', 'unknown')}")
            
            # Check classifications
            classifications = final_state.get('field_classifications', {})
            if classifications:
                print(f"      Classifications: {len(classifications)} fields")
                for field, value in list(classifications.items())[:3]:  # Show first 3
                    print(f"        {field}: {value}")
            
            # Check issues
            issues = final_state.get('identified_issues', [])
            print(f"      Issues identified: {len(issues)}")
            
            # Verify expected path
            actual_path = final_state.get('processing_path', 'unknown')
            if actual_path == test_case['expected_path']:
                print(f"      ✅ Path verification: PASSED")
            else:
                print(f"      ❌ Path verification: FAILED (expected {test_case['expected_path']}, got {actual_path})")
    
    # Run async test
    asyncio.run(test_complete_workflow())
    
    print("\n" + "=" * 60)
    print("LangGraph workflow testing complete!")
    print(f"LangGraph available: {LANGGRAPH_AVAILABLE}")
    print("Ready for production integration.")
