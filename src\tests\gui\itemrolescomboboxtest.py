"""
P:1. Map values for dropdown to show human readable names (We will use a configuration json file with 
properties "id", "default", "display" 

Store user entered name if they want to change), 
tags(other terms that appear in the search (ex. "size" tags {"nps", "npd"}

You can use a QItemRole for this

https://doc.qt.io/qt-6/qt.html#ItemDataRole-enum

"""
from PySide6.QtWidgets import QComboBox, QWidget, QVBoxLayout, QCompleter, QLineEdit
from PySide6.QtCore import Qt, QEvent


field_map = {
    "heatTrace": {
        "default": None,
        "display": "Heat Trace",
    },
    "insulationSpec": {
        "default": None,
        "display": "Insulation Spec",
    },
    "material_description": {
        "default": None,
        "display": "Material Description",
    },
}


tags = {
    "size": ["nps", "npd"],
}


class CustomComboBox(QComboBox):

    def __init__(self, parent):
        super().__init__(parent)
        self.readyToEdit = self.hasFocus()
        self.setLineEdit(QLineEdit())
        self.lineEdit().installEventFilter(self)

    def focusNextChild(self) -> bool:
        return False

    def focusOutEvent(self, e):
        super().focusOutEvent(e)
        self.lineEdit().deselect()

    def eventFilter(self, source, event):
        """ Select all if in not text is currently selected """
        if event.type() == QEvent.MouseButtonPress:
            if event.button() == Qt.LeftButton:
                if self.lineEdit().hasSelectedText():
                    return super().eventFilter(source, event)
                self.lineEdit().selectAll()
                return True

        return super().eventFilter(source, event)


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    widget = QWidget()
    widget.setLayout(QVBoxLayout())
    
    cbox = CustomComboBox(widget)
    widget.layout().addWidget(cbox)

    completer = QCompleter(field_map.keys(), None)
    cbox.setEditable(True)
    cbox.setCompleter(completer)
    cbox.completer().setCaseSensitivity(Qt.CaseInsensitive)
    cbox.setInsertPolicy(QComboBox.NoInsert)
    cbox.completer().setCompletionMode(QCompleter.CompletionMode.PopupCompletion)

    for n, key in enumerate(sorted(field_map.keys())):
        data = field_map[key]
        cbox.addItem(data.get("display"), key)
        cbox.setItemData(n, data, Qt.ItemDataRole.UserRole)
        print(data)

    for name in range(cbox.count()):
        print(cbox.itemData(n, Qt.ItemDataRole.DisplayRole))
        print(cbox.itemData(n, Qt.ItemDataRole.UserRole))

    widget.setFixedSize(500,100)
    item = cbox.itemData(cbox.currentIndex())
    widget.show()
    app.exec()