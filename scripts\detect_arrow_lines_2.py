"""
This script detect lines from a PDF

Noise should be removed.

This works on drawings with embedded data. Currently the text is excluded before being passed to cv2.

For OCR, this would require getting the polygons and bboxes and scrubbing them from the image.

"""

from ast import Import
import os
from turtle import distance
import cv2
import numpy as np
import math
import fitz
from src.utils.pdf.page_to_opencv import page_to_opencv
import pandas as pd

page_width, page_height = [2448, 1584]
relative_isometric_area = [0.04811764705882353, 0.027676767676767676, 0.6834150326797386, 0.7986111111111112]
absolute_isometric_area = [relative_isometric_area[0] * page_width,
                           relative_isometric_area[1] * page_height,
                           relative_isometric_area[2] * page_width,
                           relative_isometric_area[3] * page_height]

def angle_between(p1, p2):
    """Returns the angle between two points in degrees."""
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    angle_rad = np.arctan2(dy, dx)
    angle_deg = np.degrees(angle_rad)
    # return angle_deg % 180  # ignore direction, keep in [0, 180)
    return (angle_deg + 360) % 360  # ignore direction, keep in [0, 360)

def compute_angle(line):
    """Compute angle of the line in degrees."""
    x1, y1, x2, y2 = line
    return np.degrees(np.arctan2(y2 - y1, x2 - x1))

def compute_distance(p1, p2):
    """Compute Euclidean distance between two points."""
    return np.linalg.norm(np.array(p1) - np.array(p2))

def fit_line(points):
    """Fit a line through points using least squares."""
    points = np.array(points)

    # Fit line: y = mx + b
    x = points[:, 0]
    y = points[:, 1]

    A = np.vstack([x, np.ones(len(x))]).T
    m, b = np.linalg.lstsq(A, y, rcond=None)[0]

    return m, b

def line_from_fit(m, b, x_min, x_max):
    """Return line endpoints from line equation."""
    y_min = m * x_min + b
    y_max = m * x_max + b
    return [x_min, y_min, x_max, y_max]

def lines_are_mergeable(line1, line2, angle_thresh=5, distance_thresh=20):
    """Check if lines are similar in angle and close enough to merge."""
    angle1 = compute_angle(line1)
    angle2 = compute_angle(line2)

    # Check angle similarity
    if abs(angle1 - angle2) > angle_thresh:
        return False

    # Check if endpoints are close
    points1 = [(line1[0], line1[1]), (line1[2], line1[3])]
    points2 = [(line2[0], line2[1]), (line2[2], line2[3])]

    for p1 in points1:
        for p2 in points2:
            if compute_distance(p1, p2) < distance_thresh:
                return True

    return False


def merge_and_fit_lines(lines_to_merge):
    """Merge multiple lines and fit a straight line."""
    points = []
    for line in lines_to_merge:
        points.append((line[0], line[1]))
        points.append((line[2], line[3]))

    m, b = fit_line(points)

    xs = [p[0] for p in points]
    x_min = min(xs)
    x_max = max(xs)

    return line_from_fit(m, b, x_min, x_max)

import numpy as np

def compute_angle(line):
    x1, y1, x2, y2 = line
    angle = np.arctan2(y2 - y1, x2 - x1)
    return np.degrees(angle) % 180  # Normalize to [0, 180)

def line_length(line):
    x1, y1, x2, y2 = line
    return np.hypot(x2 - x1, y2 - y1)

def endpoints_distance(line1, line2):
    # distance between any endpoint of line1 and any endpoint of line2
    points1 = [(line1[0], line1[1]), (line1[2], line1[3])]
    points2 = [(line2[0], line2[1]), (line2[2], line2[3])]
    dists = [np.hypot(p1[0]-p2[0], p1[1]-p2[1]) for p1 in points1 for p2 in points2]
    return min(dists)

def merge_two_lines(line1, line2):
    points = [
        (line1[0], line1[1]), (line1[2], line1[3]),
        (line2[0], line2[1]), (line2[2], line2[3])
    ]
    xs, ys = zip(*points)
    return [min(xs), min(ys), max(xs), max(ys)]

def merge_lsd_lines(lines, angle_threshold=0.3, distance_threshold=4):
    """Merge lines based on angle and distance."""
    merged = []
    used = [False] * len(lines)

    for i, line1 in enumerate(lines):
        if used[i]:
            continue

        x1, y1, x2, y2 = line1
        angle1 = compute_angle(line1)

        new_line = line1.copy()

        for j, line2 in enumerate(lines):
            if i == j or used[j]:
                continue

            angle2 = compute_angle(line2)
            if abs(angle1 - angle2) < angle_threshold:
                if endpoints_distance(new_line, line2) < distance_threshold:
                    # Merge them
                    new_line = merge_two_lines(new_line, line2)
                    used[j] = True

        merged.append(new_line)
        used[i] = True

    return merged

def smart_merge_lsd_lines(lines, angle_threshold=5, distance_threshold=20):
    """Performs least squares fit after merging points, to get straight line results"""
    merged = []
    used = [False] * len(lines)

    for i, line1 in enumerate(lines):
        if used[i]:
            continue

        group = [line1]
        used[i] = True
        angle1 = compute_angle(line1)

        for j, line2 in enumerate(lines):
            if i == j or used[j]:
                continue

            angle2 = compute_angle(line2)

            if abs(angle1 - angle2) < angle_threshold:
                if endpoints_distance(line1, line2) < distance_threshold:
                    group.append(line2)
                    used[j] = True

        merged_line = merge_and_fit_lines(group)
        merged.append(merged_line)

    return merged

def draw_fitz_page(page: fitz.Page,
                   zoom: int = 3,
                   lines: bool = True,
                   text: bool = False) -> np.ndarray:
    """Redraw specific page elements and return the image.

    Args:
        text: Whether to include text in the image. - Not implemented
        lines: Whether to include lines in the image.
        zoom: Zoom factor for the image.
    """
    # Get the page's drawing operations
    # img = page_to_opencv(page, zoom=zoom)
    drawings = page.get_drawings()

    # Create a blank image with white background
    img = np.ones((int(page.rect.height * zoom),
                   int(page.rect.width * zoom),
                  3), dtype=np.uint8) * 255

    # Scale the isometric area according to zoom factor
    iso_area = [
        int(absolute_isometric_area[0] * zoom),
        int(absolute_isometric_area[1] * zoom),
        int(absolute_isometric_area[2] * zoom),
        int(absolute_isometric_area[3] * zoom)
    ]

    # Draw only the vector graphics within isometric area
    for drawing in drawings:
        items = drawing["items"]  # List of drawing commands
        width = drawing.get("width", 1)
        if not width:
            width = 1
        width = int(math.ceil(width) * zoom)
        for item in items:
            if item[0] == "l":  # Line drawing command
                # Extract line coordinates and scale them
                point1 = item[1]
                point2 = item[2]
                x1, y1 = int(point1[0] * zoom), int(point1[1] * zoom)
                x2, y2 = int(point2[0] * zoom), int(point2[1] * zoom)

                # Check if line is within isometric area
                if (iso_area[0] <= x1 <= iso_area[2] and
                    iso_area[1] <= y1 <= iso_area[3] and
                    iso_area[0] <= x2 <= iso_area[2] and
                    iso_area[1] <= y2 <= iso_area[3]):
                    # Draw the line on our image
                    cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), width)

    return img


def parse_page_range(page_range_str, total_pages):
    """Parse page range string into list of page numbers (1-based)"""
    if type(page_range_str) == int:
        return [page_range_str]
    elif type(page_range_str) == list:
        return page_range_str
    elif page_range_str is None:
        return list(range(1, total_pages + 1))
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return list(range(1, total_pages + 1))

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            end = min(total_pages, end)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            if 1 <= page <= total_pages:
                pages.append(page)

    return sorted(set(pages))

def distance_to(p1, p2):
    """Returns the distance between two points."""
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])


from shapely.geometry import LineString, Point
import rtree
def to_linestring(row):
    return LineString([(row['x1'], row['y1']), (row['x2'], row['y2'])])

def angle_between_lines(line1, line2, shared_pt):

    def other_point(line, pt):
        coords = list(line.coords)
        return coords[0] if coords[1] == pt else coords[1]

    p1 = other_point(line1, shared_pt)
    p2 = shared_pt
    p3 = other_point(line2, shared_pt)

    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
    angle_rad = np.arccos(np.clip(cos_angle, -1, 1))
    angle_deg = np.degrees(angle_rad)
    return angle_deg

def check_arrow_shape(line1, line2, near_threshold=3, far_min_threshold=4, far_max_threshold=8):
    """
    The start of line1 is near the end of line2 i.e. the arrow tip.

    The other ends are far enough apart to form an open "V".
    """
    pts1 = [Point(p) for p in line1.coords]
    pts2 = [Point(p) for p in line2.coords]

    # Case 1: line1 start near line2 end
    near_dist = pts1[0].distance(pts2[1])
    far_dist = pts1[1].distance(pts2[0])
    if near_dist <= near_threshold and far_min_threshold <= far_dist <= far_max_threshold:
        return pts1[0]

    # Case 2: line1 end near line2 start
    near_dist = pts1[1].distance(pts2[0])
    far_dist = pts1[0].distance(pts2[1])
    if near_dist <= near_threshold and far_min_threshold <= far_dist <= far_max_threshold:
        return pts1[1]

    return None

from shapely.geometry import LineString
from shapely.ops import nearest_points

def line_as_ray(line, length=1000, reverse=False):
    """Extend a line to a ray.

    Args:
        line: LineString
        length: Length of the ray
        reverse: Reverse the direction of the ray
    """
    coords = list(line.coords)
    if reverse:
        coords.reverse()
    p1, p2 = coords
    vec = np.array(p2) - np.array(p1)
    vec = vec / np.linalg.norm(vec) * length
    new_p2 = np.array(p1) + vec
    return LineString([p1, new_p2])

def get_arrow_tip_precise(line1, line2):
    ray1 = line_as_ray(line1)
    ray2 = line_as_ray(line2)
    intersection = ray1.intersection(ray2)
    if intersection.is_empty:
        return None
    return intersection  # This is the true arrow tip

def get_arrowhead_direction(line1, line2, shared_pt):
    def other_point(line, pt):
        coords = list(line.coords)
        return coords[0] if coords[1] == pt else coords[1]

    # Get vectors pointing away from the shared point
    p1 = np.array(other_point(line1, shared_pt))
    p2 = np.array(shared_pt)
    v1 = p1 - p2

    p3 = np.array(other_point(line2, shared_pt))
    v2 = p3 - p2

    # Compute average (bisector)
    v_avg = (v1 / np.linalg.norm(v1) + v2 / np.linalg.norm(v2)) / 2

    # Arrowhead direction angle in degrees [0°, 360°]
    angle_rad = np.arctan2(-v_avg[1], v_avg[0])  # Y flipped for image coords
    angle_deg = np.degrees(angle_rad) % 360
    return angle_deg

def main(pdf_path: str, page_range: str = "1-50", debug: bool = True):

    from scripts.cv2_detection_utils import detect_rectangles, drawing

    zoom = 2

    doc = fitz.open(pdf_path)
    page_count = len(doc)
    pages_to_process = parse_page_range(page_range, page_count)

    save_image_count = 1
    def save_debug_file(data: any,
                        name: str,
                        description: str = None,
                        pdf_page: int = ""):
        nonlocal save_image_count

        if not debug:
            return

        if pdf_page:
            pdf_page = f"page_{pdf_page}_"

        os.makedirs("debug/isometric_detection", exist_ok=True)
        try:
            # Dataframes
            if data.empty:
                pass
            fn = f"debug/isometric_detection/{pdf_page}stage_{save_image_count}_{name}.xlsx"
            data.to_excel(fn, index=False)
            print(f"saved to {fn}")
            return fn
        except Exception as e:
            pass

        try:
            # Save image types
            img = data.copy()
            height, width, channel = img.shape
            if description:
                description = f"Stage {save_image_count} - {description}. Image size = ({width}x{height})"
                cv2.putText(img, description, (10, 220), cv2.FONT_HERSHEY_SIMPLEX, height//2000, (0, 0, 255), 5)
            fn = f"debug/isometric_detection/{pdf_page}stage_{save_image_count}_{name}.png"
            cv2.imwrite(fn, img)
            print(f"saved to {fn}")
            save_image_count += 1
            return fn
        except Exception as e:
            pass

        print("Unsupported debug save file type!")

    all_line_data = []

    try:
        num_pages = len(pages_to_process)
        # Determine number of pages to process
        print(f"Processing {num_pages} pages...")

        # Create a new PDF document
        output_doc = fitz.open()

        for pdf_page in pages_to_process:
            print(f"Processing page {pdf_page}...")
            save_image_count = 1

            zoom = 2

            # cv_img = page_to_opencv(doc.load_page(page_num), zoom=zoom)

            page = doc.load_page(pdf_page - 1)
            img = draw_fitz_page(page, zoom=zoom)

            def insert_original_page():
                output_doc.insert_pdf(doc, from_page=pdf_page-1, to_page=pdf_page-1)
                output_page = output_doc[output_doc.page_count-1]
                output_page.insert_text((2, 24), f"Page: {pdf_page}", fontsize = 32)
                return output_page

            outpage = insert_original_page()

            cv_img = img

            # Make everything black
            masked = np.zeros_like(cv_img)
            x1, y1, x2, y2 = absolute_isometric_area
            x1 = int(x1) * zoom
            y1 = int(y1) * zoom
            x2 = int(x2) * zoom
            y2 = int(y2) * zoom
            masked[y1:y2, x1:x2] = cv_img[y1:y2, x1:x2]
            cv_img = masked

            gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

            # scale = 0.5
            # gray = cv2.resize(gray, (0, 0), fx=scale, fy=scale)  # 50% size

            # Create LSD detector
            lsd = cv2.createLineSegmentDetector(refine=cv2.LSD_REFINE_NONE)

            # Detect lines
            lines = lsd.detect(gray)[0]  # Position 0 has the lines

            # cv = lsd.drawSegments(cv_img, lines)
            # save_debug_file(cv, "lsd_detected_lines", "Detected lines", page_num)

            lines = [line[0] for line in lines]
            # merged_lines = smart_merge_lsd_lines(lines)
            # merged_lines = merge_lsd_lines(lines)
            # merged_lines = merge_and_fit_lines(lines)

            import random
            def random_color(min_val=100, max_val=255):
                return (random.randint(min_val, max_val),
                        random.randint(min_val, max_val),
                        random.randint(min_val, max_val))

            def random_color_fitz(min_val=50, max_val=80):
                return (random.randint(min_val, max_val) / 100,
                        random.randint(min_val, max_val) / 100,
                        random.randint(min_val, max_val) / 100)

            image_width = gray.shape[1]
            image_height = gray.shape[0]

            def is_close_angle(angle, angles, tol=2, scale=1):
                for angle2 in angles:
                    if abs(angle - angle2) < tol:
                        return True
                return False

            line_data = []
            max_arrow_head_length = 16

            # Draw detected lines
            for line in lines:
                color = random_color_fitz()
                length = line_length(line)

                potential_arrow_head = False

                x1, y1, x2, y2 = line
                px1, py1, px2, py2 = x1 / image_width, y1 / image_height, x2 / image_width, y2 / image_height

                ax1, ay1, ax2, ay2 = px1 * outpage.rect.width, py1 * outpage.rect.height, px2 * outpage.rect.width, py2 * outpage.rect.height
                angle = angle_between((x1, y1), (x2, y2))
                if length < max_arrow_head_length:
                    outpage.insert_text((ax1, ay1), f"{int(angle)}", fontsize = 3, color=color)
                    cv2.line(cv_img, (int(line[0]), int(line[1])), (int(line[2]), int(line[3])), color, zoom)
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=color, width=1, stroke_opacity=1)
                    potential_arrow_head = True
                elif is_close_angle(angle, [0, 180, 360]):
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 1, 0), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [30, 210]): # blue measurement line
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(1, 0, 1), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [330, 150]): # blue measurement line
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0, 1), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [90, 270]):
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0.5, 0.5), width=0.6, stroke_opacity=1)
                # elif is_close_angle(angle, [0, 150]):
                #     outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0.5, 0, 0.5), width=0.6, stroke_opacity=1)
                else:
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(1, 0.2, 0.2), width=0.6, stroke_opacity=1)
                    outpage.insert_text((ax1, ay1), f"{int(angle)}", fontsize = 3, color=(1, 0.2, 0.2))

                # print((x1, y1), (x2, y2))
                d = {
                    'pdf_page': pdf_page,
                    'x1': x1 / image_width,
                    'y1': y1 / image_height,
                    'x2': x2 / image_width,
                    'y2': y2 / image_height,
                    'angle': angle,
                    'length': length,
                    'category': None,
                    'potential_arrow_head': potential_arrow_head,
                    'arrow_direction': None
                }
                d['start_point'] = (d['x1'], d['y1'])
                d['end_point'] = (d['x2'], d['y2'])

                line_data.append(d)

            all_line_data.extend(line_data)
            # save_debug_file(cv_img, "detected_lines", "Detected lines", pdf_page)

            outpage = insert_original_page()

            line_data_df = pd.DataFrame(line_data)
            arrow_df = line_data_df[line_data_df['potential_arrow_head'] == True]
            arrow_df["angle_diff"] = None
            arrow_id = 1
            pairs_checked = set()
            ignore = set()

            arrow_df["x1"] = arrow_df["x1"] * outpage.rect.width
            arrow_df["y1"] = arrow_df["y1"] * outpage.rect.height
            arrow_df["x2"] = arrow_df["x2"] * outpage.rect.width
            arrow_df["y2"] = arrow_df["y2"] * outpage.rect.height

            arrow_df["geom"] = arrow_df.apply(to_linestring, axis=1)

            # Build Spatial Index to Reduce Comparisons
            spatial_idx = rtree.index.Index()
            # spatial_indexes = set()
            for i, line in enumerate(arrow_df['geom']):
                spatial_idx.insert(i, line.bounds)
                # spatial_indexes.append(i)

            def padded_bounds(line, padding=10):
                minx, miny, maxx, maxy = line.bounds
                return (minx - padding, miny - padding, maxx + padding, maxy + padding)

            arrow_candidates = []

            unfound = set([i for i in range(len(arrow_df))])
            for i, line1 in enumerate(arrow_df['geom']):
                for j in spatial_idx.intersection(padded_bounds(line1, padding=12)):
                    if i >= j:
                        continue
                    line2 = arrow_df['geom'].iloc[j]

                    shared_pt = check_arrow_shape(line1, line2)
                    if shared_pt:
                        angle = angle_between_lines(line1, line2, (shared_pt.x, shared_pt.y))
                        if 5 < angle < 330:  # tweak as needed
                            ray1 = line_as_ray(line1, 1, reverse=True) # Reverse so they expand
                            ray2 = line_as_ray(line2, 1, reverse=False)
                            precise_tip = get_arrow_tip_precise(ray1, ray2)
                            arrow_direction = get_arrowhead_direction(line1, line2, (shared_pt.x, shared_pt.y))
                            arrow_candidates.append((i, j, precise_tip, arrow_direction, angle))
                            unfound.discard(i)
                            unfound.discard(j)

            for arrow in arrow_candidates:
                i, j, precise_tip, arrow_direction, angle = arrow
                a = arrow_df.iloc[i]
                b = arrow_df.iloc[j]
                x1, y1, x2, y2 = a.x1, a.y1, a.x2, a.y2
                x3, y3, x4, y4 = b.x1, b.y1, b.x2, b.y2
                color = random_color_fitz()
                outpage.draw_line((x1, y1), (x2, y2), color=color, width=1, stroke_opacity=1)
                outpage.draw_line((x3, y3), (x4, y4), color=color, width=1, stroke_opacity=1)
                # outpage.insert_text((x1, y1), f"{int(angle)}", fontsize = 2, color=color)
                # outpage.insert_text((x3, y3), f"{int(angle)}", fontsize = 2, color=color)

                line1 = arrow_df['geom'].iloc[i]
                line2 = arrow_df['geom'].iloc[j]

                outpage.insert_text((x1, y1), f"{int(i)}", fontsize = 1, color=color)
                outpage.insert_text((x3, y3), f"{int(j)}", fontsize = 1, color=color)

                ray1 = line_as_ray(line1, 16, reverse=False)
                ray2 = line_as_ray(line2, 16, reverse=True)
                outpage.draw_line((ray1.coords[0][0], ray1.coords[0][1]), (ray1.coords[1][0], ray1.coords[1][1]), color=(color[0], color[1], color[2], 0.5), width=0.4, stroke_opacity=1)
                outpage.draw_line((ray2.coords[0][0], ray2.coords[0][1]), (ray2.coords[1][0], ray2.coords[1][1]), color=(color[0], color[1], color[2], 0.5), width=0.4, stroke_opacity=1)

                if precise_tip is None:
                    continue
                px1, px2 = precise_tip.x, precise_tip.y
                outpage.insert_text((px1, px2), f"{int(arrow_direction)}", fontsize = 5, color=color)
                outpage.draw_circle((px1, px2), 0.4, color=(0,1,0), fill_opacity=1, stroke_opacity=1)


            for i in unfound:
                line = arrow_df.iloc[i]
                x1, y1, x2, y2 = line.x1, line.y1, line.x2, line.y2
                color = random_color_fitz()
                outpage.draw_line((x1, y1), (x2, y2), color=(1,0,0), width=1, stroke_opacity=1)


            for line in arrow_df.itertuples():
                break
                index = line.Index
                angle = line.angle
                color = random_color_fitz()
                x1, y1, x2, y2 = line.x1, line.y1, line.x2, line.y2

                if index in ignore:
                    continue
                ignore.add(index)

                arrow_df["angle_diff"] = abs(arrow_df["angle"] - angle)

                # Detected arrow head line match on opposite ends
                for line2 in arrow_df.itertuples():
                    index2 = line2.Index
                    if index == index2:
                        continue
                    # if index2 in ignore:
                    #     continue
                    if (min(index, index2), max(index, index2)) in pairs_checked:
                        continue
                    pairs_checked.add((min(index, index2), max(index, index2))) # add both directions to pairs_checked

                    j1, k1, j2, k2 = line2.x1, line2.y1, line2.x2, line2.y2
                    abs_j1, abs_k1, abs_j2, abs_k2 = line2.abs_x1, line2.abs_y1, line2.abs_x2, line2.abs_y2
                    angle2 = line2.angle

                    print(distance_to((ax1, ay1), (abs_j2, abs_k2)))
                    if distance_to((abs_x1, abs_y1), (abs_j2, abs_k2)) < 10 or distance_to((abs_x2, abs_y2), (abs_j1, abs_k1)) < 10: # and distance_to((x2, y2), (j2, k2)) < 10:

                        angle_diff = line2.angle_diff
                        angle_diff = min(angle_diff, 360 - angle_diff)
                        print(angle_diff)

                        if angle_diff < 200:
                            print("Matched arrow head line")
                            # line_data[index]['category'] = f"arrow_{arrow_id}"
                            # line_data[index2]['category'] = f"arrow_{arrow_id}"
                            outpage.draw_line((abs_x1, abs_y1), (abs_x2, abs_y2), color=(1,0,0), width=1, stroke_opacity=1)
                            outpage.draw_line((abs_j1, abs_k1), (abs_j2, abs_k2), color=(1,0,0), width=1, stroke_opacity=1)
                            arrow_id += 1
                            ignore.add(index2)
                            break

                    else:
                        # Not assigned
                        outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0, 1), width=1, stroke_opacity=1)



            continue

            # save_debug_file(img, "fitz_page_to_cv2", "Draws the page with cv2 using PyMuPDF embedded get_drawings()", page_num + 1)

            print("Detecting rectangles...")
            # rectangles = detect_rectangles.detect_rectangles(img)

            # print("Removing rectangles from image...")
            # # Scrubs rectangles from image
            # img = drawing.draw_rectangles(
            #         img,
            #         rectangles=rectangles,
            #         fill_color=(255, 0, 255),     # White in BGR
            #         border_color=(255, 255, 255),   # White border
            #         fill_opacity=0,
            #         border_thickness=32
            # )

            # save_debug_file(img, "detected_rectangles", "Scrubs the detected rectangles", page_num + 1)

            classified_lines = detector.detect_all_lines(img)

            # Create visualization image
            img = np.ones((int(page.rect.height * detector.zoom),
                          int(page.rect.width * detector.zoom),
                          3), dtype=np.uint8) * 255

            # Draw lines on image
            detected_img_no_labels = detector.visualize_lines(img, classified_lines, show_labels=False)
            # save_debug_file(detected_img_no_labels, "detected_lines_no_labels", "All detected lines no labels", page_num + 1)

            # Skeletonize the image
            # skeleton_lines: list = skimage_detect_lines.detect_lines(detected_img_no_labels)
            # skeleton_lines = {"other": skeleton_lines}
            # skeleton_img = detector.visualize_lines(img, skeleton_lines, show_labels=False)
            # save_debug_file(skeleton_img, "skeletonized_image", "Skeletonized image", page_num + 1)

            detected_img = detector.visualize_lines(cv_img, classified_lines, show_labels=True)
            png_path = save_debug_file(detected_img, "detected_lines_with_labels", "All detected lines with labels", pdf_page + 1)

            # Create a new page in output PDF
            new_page = output_pdf.new_page(
                width=int(page.rect.width * detector.zoom),
                height=int(page.rect.height * detector.zoom)
            )

            if png_path:
                # Insert the saved PNG into the PDF
                new_page.insert_image(
                    new_page.rect,
                    filename=png_path
                )

            # Export line data to CSV
            line_data = []
            for line_type, lines in classified_lines.items():
                for line in lines:
                    x1, y1, x2, y2 = line
                    angle = detector.calculate_angle(line)
                    length = math.hypot(x2 - x1, y2 - y1)
                    line_data.append({
                        'page': pdf_page + 1,
                        'start_point': (x1, y1),
                        'end_point': (x2, y2),
                        'angle': angle,
                        'length': length,
                        'type': line_type
                    })

            # Append to CSV
            df = pd.DataFrame(line_data)
            # save_debug_file(df, f"detected_lines_page_{page_num + 1}", "Detected lines", page_num + 1)

        # Save the output PDF
        try:
            output_doc.save("debug/detected_lines.pdf", garbage=4, deflate=True)
            print(f"Results saved to debug/detected_lines.pdf and debug/detected_lines.csv")
            print()
        except:
            pass

    finally:
        doc.close()
        if 'output_pdf' in locals():
            output_doc.close()


    all_line_data_df = pd.DataFrame(all_line_data)
    all_line_data_df.to_csv("debug/detected_lines.csv", index=False)

if __name__ == "__main__":
    pdf_path = r"S:\Shared Folders\Client Uploads\Brock Services\S1601 Insulation Only (1).pdf"
    pdf_path = r"C:/Drawings/Clients/brockservices/BRS_0031 - BSLE34891 - Valero St Charles/received/BSLE34891 - Valero St Charles - Combined.pdf"
    main(pdf_path, page_range="1-5")  # Process first 10 pages

