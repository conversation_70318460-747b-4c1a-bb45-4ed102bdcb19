"""
Used claude to generate base tables from SQLite database. Focus on <PERSON><PERSON> and General creation for now. Can remove the reference to PDF Storage table and add slowly add later.
"""


-- PROJECTS TABLE
CREATE TABLE IF NOT EXISTS public.projects (
    id SERIAL PRIMARY KEY,
    project_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    project_number VA<PERSON><PERSON><PERSON>(100),
    user_company_name <PERSON><PERSON><PERSON><PERSON>(200),
    client_name VA<PERSON>HA<PERSON>(200),
    project_location VARCHAR(255),
    project_scope TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- PDF STORAGE TABLE
CREATE TABLE IF NOT EXISTS public.pdf_storage (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES public.projects(id) ON DELETE CASCADE,
    original_filename VARCHAR(255),
    pdf_page INTEGER,
    document BYTEA,
    document_vendor VARCHAR(100),
    document_type VARCHAR(100),
    document_name <PERSON><PERSON><PERSON><PERSON>(255),
    doc_size VARCHAR(50),
    skipped BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_pdf_storage_project_id ON public.pdf_storage(project_id);

-- GENERAL TABLE
CREATE TABLE IF NOT EXISTS public.general (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    annot_markups TEXT,
    area TEXT,
    avg_elevation VARCHAR(50),
    block_coordinates TEXT,
    client_document_id VARCHAR(100),
    coordinates TEXT,
    design_code VARCHAR(100),
    document_description TEXT,
    document_id VARCHAR(100),
    document_title VARCHAR(255),
    drawing VARCHAR(100),
    elevation VARCHAR(255),
    flange_id VARCHAR(100),
    heat_trace VARCHAR(50),
    insulation_spec VARCHAR(100),
    insulation_thickness VARCHAR(50),
    iso_number VARCHAR(100),
    iso_type VARCHAR(100),
    line_number VARCHAR(100),
    max_elevation VARCHAR(50),
    medium_code VARCHAR(100),
    min_elevation VARCHAR(50),
    mod_date VARCHAR(50),
    paint_spec VARCHAR(100),
    pid VARCHAR(100),
    pipe_spec VARCHAR(100),
    pipe_standard VARCHAR(100),
    process_line_list VARCHAR(100),
    process_unit VARCHAR(100),
    project_no VARCHAR(100),
    project_name VARCHAR(255),
    pwht VARCHAR(50),
    revision VARCHAR(50),
    sequence VARCHAR(50),
    service VARCHAR(100),
    sheet VARCHAR(50),
    size DECIMAL(12,3),
    sys_build VARCHAR(100),
    sys_layout_valid VARCHAR(50),
    sys_document VARCHAR(255),
    sys_document_name VARCHAR(255),
    sys_filename VARCHAR(255),
    sys_path VARCHAR(255),
    system VARCHAR(100),
    total_sheets VARCHAR(50),
    unit VARCHAR(50),
    vendor_document_id VARCHAR(100),
    weld_id VARCHAR(100),
    weld_class VARCHAR(100),
    x_coord VARCHAR(50),
    xray VARCHAR(50),
    y_coord VARCHAR(50),
    lf DECIMAL,
    sf DECIMAL,
    ef DECIMAL,
    elbows_90 DECIMAL,
    elbows_45 DECIMAL,
    bevels DECIMAL,
    tees DECIMAL,
    reducers DECIMAL,
    caps DECIMAL,
    flanges DECIMAL,
    valves_flanged DECIMAL,
    valves_welded DECIMAL,
    cut_outs DECIMAL,
    supports DECIMAL,
    bends DECIMAL,
    union_couplings DECIMAL,
    expansion_joints DECIMAL,
    field_welds DECIMAL,
    paint_color VARCHAR(100),
    cwp VARCHAR(100),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_general_pdf_id ON public.general(pdf_id);
CREATE INDEX idx_general_size ON public.general(size);

-- BOM TABLE
CREATE TABLE IF NOT EXISTS public.bom (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    calculated_eq_length DECIMAL,
    calculated_eq_area DECIMAL,
    pos VARCHAR(50),
    material_description TEXT,
    size DECIMAL(12,3),
    size1 DECIMAL(12,3),
    size2 DECIMAL(12,3),
    ident VARCHAR(100),
    item VARCHAR(100),
    tag VARCHAR(100),
    quantity DECIMAL,
    status VARCHAR(50),
    nb VARCHAR(50),
    fluid VARCHAR(100),
    clean_spec VARCHAR(100),
    line_number VARCHAR(100),
    component_category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_bom_pdf_id ON public.bom(pdf_id);
CREATE INDEX idx_bom_material_description ON public.bom(material_description);
CREATE INDEX idx_bom_size ON public.bom(size);

-- PROJECT SOURCES TABLE
CREATE TABLE IF NOT EXISTS public.project_sources (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES public.projects(id) ON DELETE CASCADE,
    filename VARCHAR(255),
    document_vendor VARCHAR(100),
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sort_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_project_sources_project_id ON public.project_sources(project_id);

-- USERS TABLE
CREATE TABLE IF NOT EXISTS public.users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(200),
    role VARCHAR(50),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_users_email ON public.users(email);

-- SPEC TABLE
CREATE TABLE IF NOT EXISTS public.spec (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    dp1 VARCHAR(50),
    dp2 VARCHAR(50),
    dt1 VARCHAR(50),
    dt2 VARCHAR(50),
    op1 VARCHAR(50),
    op2 VARCHAR(50),
    opt1 VARCHAR(50),
    opt2 VARCHAR(50),
    pipe_spec VARCHAR(100),
    size DECIMAL(12,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_spec_pdf_id ON public.spec(pdf_id);
CREATE INDEX idx_spec_pipe_spec ON public.spec(pipe_spec);

-- SPOOL TABLE
CREATE TABLE IF NOT EXISTS public.spool (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    cut_piece VARCHAR(100),
    length DECIMAL,
    spool VARCHAR(100),
    spec VARCHAR(100),
    size DECIMAL(12,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_spool_pdf_id ON public.spool(pdf_id);

-- RAW DATA TABLE
CREATE TABLE IF NOT EXISTS public.raw_data (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    pdf_page INTEGER,
    sys_build VARCHAR(100),
    sys_path VARCHAR(255),
    sys_filename VARCHAR(255),
    sys_layout_valid BOOLEAN DEFAULT FALSE,
    type VARCHAR(100),
    category VARCHAR(100),
    value TEXT,
    elevation VARCHAR(50),
    x_position VARCHAR(50),
    y_position VARCHAR(50),
    coordinates TEXT,
    coordinates2 TEXT,
    words TEXT,
    name VARCHAR(255),
    title VARCHAR(255),
    created_date VARCHAR(50),
    mod_date VARCHAR(50),
    id_annot_info VARCHAR(100),
    annot_page_rotation INTEGER,
    color VARCHAR(50),
    annot_type TEXT,
    annot_subtype VARCHAR(100),
    vertices TEXT,
    endpoint TEXT,
    stroke_color VARCHAR(50),
    font VARCHAR(100),
    font_style VARCHAR(50),
    font_size REAL,
    flags INTEGER,
    page_rotation INTEGER,
    font_color VARCHAR(50),
    annot_markups TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_raw_data_pdf_id ON public.raw_data(pdf_id);
CREATE INDEX idx_raw_data_type ON public.raw_data(type);
CREATE INDEX idx_raw_data_category ON public.raw_data(category);

-- DOCUMENT REVISION TRACKING TABLE (New)
CREATE TABLE IF NOT EXISTS public.document_revisions (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES public.pdf_storage(id) ON DELETE CASCADE,
    revision VARCHAR(50) NOT NULL,
    revision_date TIMESTAMP,
    revision_description TEXT,
    revised_by VARCHAR(200),
    approved_by VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_document_revisions_pdf_id ON public.document_revisions(pdf_id);

-- ANNOTATION TYPES TABLE (New)
CREATE TABLE IF NOT EXISTS public.annotation_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- PROJECT PERMISSIONS TABLE (New)
CREATE TABLE IF NOT EXISTS public.project_permissions (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES public.users(id) ON DELETE CASCADE,
    permission_level VARCHAR(20) NOT NULL, -- 'read', 'write', 'admin'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, user_id)
);
CREATE INDEX idx_project_permissions_project_id ON public.project_permissions(project_id);
CREATE INDEX idx_project_permissions_user_id ON public.project_permissions(user_id);

-- FUNCTIONS TO AUTO-UPDATE TIMESTAMPS
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply timestamp triggers to all tables
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE format('
            CREATE TRIGGER update_timestamp
            BEFORE UPDATE ON public.%I
            FOR EACH ROW EXECUTE FUNCTION update_modified_column();
        ', t);
    END LOOP;
END;
$$;