
guides = [
    (65.75994873046875, 90.41998291015625),
    (90.41998291015625, 115.0799560546875),
    (115.0799560546875, 137.63995361328125),
    (137.63995361328125, 160.01995849609375),
    (160.01995849609375, 185.3399658203125),
    (185.3399658203125, 213.47998046875),
    (213.47998046875, 241.49993896484375),
    (241.49993896484375, 269.5799560546875),
    (269.5799560546875, 297.************),
    (297.************, 325.8599853515625),
    (325.8599853515625, 353.9399719238281),
    (353.9399719238281, 381.9599609375),
    (381.9599609375, 421.4849624633789),

]

guides_2 = [
    (179, 179),
    (190, 190),
]

import fitz

file = r"C:\Drawings\Clients\axisindustries\05-28-25 <PERSON>ville Piping\Received\InDemand Berryville Piping Isos.pdf"
doc = fitz.open(file)


page_num = 24
page = doc.load_page(page_num)

new_doc = fitz.open()
new_doc.insert_pdf(doc, page_num, page_num)

new_page = new_doc.load_page(0)
new_page.remove_rotation()

for guide in guides:
    y0 = guide[0]
    y1 = guide[1]
    start = fitz.Point(0, y0)
    end = fitz.Point(new_page.rect.width, y0)
    new_page.draw_line(start, end, color=(1, 0, 0))
    start = fitz.Point(0, y1)
    end = fitz.Point(new_page.rect.width, y1)
    new_page.draw_line(start, end, color=(1, 0, 0))

for guide in guides_2:
    y0 = guide[0]
    y1 = guide[1]
    start = fitz.Point(0, y0)
    end = fitz.Point(new_page.rect.width, y0)
    new_page.draw_line(start, end, color=(0, 1, 0))
    start = fitz.Point(0, y1)
    end = fitz.Point(new_page.rect.width, y1)
    new_page.draw_line(start, end, color=(0, 1, 0))

new_doc.save("debug/extraction_results/guides.pdf")