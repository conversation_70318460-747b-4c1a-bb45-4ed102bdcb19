# Concat a sequence of equally sized images

import cv2
import os

paths = []
cv_images = []
base_path = "src/resources/animated/loader"

frames = 30
for n in range(frames):
    file = f"{base_path}/{n+1}.png"
    paths.append(file)
    cv_image = cv2.imread(file)
    cv_images.append(cv_image)

full_image = cv2.hconcat(cv_images)
cv2.imshow("test", full_image)
cv2.waitKey()

size = 22
outfile = f"src/resources/loader_frames_{frames}_px_{size}.png"
print(outfile)
cv2.imwrite(outfile, full_image)