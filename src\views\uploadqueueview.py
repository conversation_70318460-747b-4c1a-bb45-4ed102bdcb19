from src.utils.logger import logger
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from pubsub import pub
from src.utils.logger import logger
from src.widgets.jobqueuecard import JobQueueCardExtended

# logger = logging.getLogger()


class JobQueueGrid(QWidget):

    sgnClear = Signal()
    sgnUpdateJobStatus = Signal(str, str) # (uuid, message)
    sgnUpdateQueue = Signal(object)
    def __init__(self, parent):
        super().__init__(parent)
        self.jobs = {}  # {uuid: widget}
        self.setLayout(QVBoxLayout())
        self.layout().setDirection(QVBoxLayout.Direction.BottomToTop)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.layout().setSpacing(16)
        self.sgnUpdateQueue.connect(self.onUpdateQueue)
        self.sgnUpdateJobStatus.connect(self.onUpdateJobStatus)
        self.sgnClear.connect(self.clear)

    def onUpdateQueue(self, data: dict):
        """Display thumbnails and links in grid view"""
        for jobData in data["jobs"]:
            jobId = jobData["uuid"]
            widget = self.jobs.get(jobId)
            if not widget:
                widget = JobQueueCardExtended(self, data=jobData)
                widget.setFixedSize(512, 144)
                self.layout().addWidget(widget)
                self.jobs[jobId] = widget
            else:
                widget.updateData(jobData)

    def onUpdateJobStatus(self, jobId: str, msg: str):
        widget: JobQueueCardExtended = self.jobs.get(jobId)
        if widget:
            widget.updateStatus(msg)

    def clear(self):
        for ch in reversed(self.children()):
            if isinstance(ch, JobQueueCardExtended):
                ch.setParent(None)
                ch.deleteLater()
        self.jobs = {}
           

class UploadQueueViewContainer(QScrollArea):

    def __init__(self, parent):
        super().__init__(parent)
        self.jobQueueGrid = JobQueueGrid(self)
        self.jobQueueGrid.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Maximum)

        self.setWidget(self.jobQueueGrid)
        self.setWidgetResizable(True)
        self.showScrollbars()
        self.setAlignment(Qt.AlignmentFlag.AlignTop)

        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")
        pub.subscribe(self.setStatusbarMessageRealtime, "set-statusbar-realtime")
        pub.subscribe(self.logoutCleanup, "logout")

    def logoutCleanup(self):
        self.jobQueueGrid.sgnClear.emit()

    def showScrollbars(self):
        """ Only show vertical scrollbars """
        try:
            self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        except Exception as e:
            logger.error(f"Error showing scrollbars: {e}", exc_info=True)
    
    def onJobQueueUpdated(self, data: dict):
        self.jobQueueGrid.sgnUpdateQueue.emit(data)
    
    def setStatusbarMessageRealtime(self, message, jobId=None):
        self.jobQueueGrid.sgnUpdateJobStatus.emit(jobId, message)


class UploadQueueView(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        self._parent = parent
        self.setLayout(QVBoxLayout())
        self.toolbar = QWidget(self)
        self.toolbar.setLayout(QHBoxLayout())
        self.toolbar.setFixedHeight(48)
        self.toolbar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Maximum)

        self.label = QLabel("Jobs")
        self.label.setObjectName("header")
        self.toolbar.layout().addWidget(self.label)
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.toolbar.layout().addItem(spacer)

        self.jobQueueContainer = UploadQueueViewContainer(self)
        self.jobQueueContainer.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.layout().addWidget(self.toolbar)
        self.layout().addWidget(self.jobQueueContainer)


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    sys.path[0] = ""  # For relative resource pathss
    app = QApplication()
    from src.theme import stylesheet
    app.setStyleSheet(stylesheet)
    window = UploadQueueView(None)
    window.setMinimumSize(800, 600)
    window.show()

    try:
        from src.theme import stylesheet
        app.setStyleSheet(stylesheet)
    except:
        pass

    data = {
        "jobs": [
            {"name": "Worker Name", "uuid": 1, "progress": 20, "finished": False},
            {"name": "Worker 1", "uuid": 2, "progress": 80, "finished": False},
            {"name": "Worker 2", "uuid": 3, "progress": 80, "finished": False},
            {"name": "Worker 3", "uuid": 4, "progress": 80, "finished": False},
            {"name": "Worker 4", "uuid": 5, "progress": 80, "finished": False},
        ]
    }
    pub.sendMessage("job-queue-updated", data=data)
    # pub.sendMessage("job-queue-updated", data=data)
    app.exec()