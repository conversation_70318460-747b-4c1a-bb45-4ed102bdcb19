import sys
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:  # This block is only for type checking tools like mypy.
    from PySide6.QtWidgets import QWidget

from PySide6.QtGui import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from functools import partial

from PySide6.QtWidgets import QWidget
from src.pyside_util import get_resource_qicon, get_resource_pixmap
from src.app_paths import getSavedFieldMapJson
from src.data.tables.alwaysvisibletablefields import always_visible_table_fields
from src.data.tables.defaultcolumnorder import default_column_order


class ColumnsList(QListWidget):

    if sys.version_info >= (3, 10):
        def __init__(self, parent: 'QWidget | None' = None) -> None:
            super().__init__(parent)
            # self.setAlternatingRowColors(True)
    else:
        def __init__(self, parent: Optional['QWidget'] = None) -> None:
            super().__init__(parent)
            # self.setAlternatingRowColors(True)


class ColumnOrganizer(QWidget):

    showCursorTooltip = Signal(str)
    columnsSaved = Signal(list)
    def __init__(self,
                 parent=None,
                 tableType:str=None,
                 fields:list=[]):
        super().__init__(parent)

        self.setObjectName("popup")
        self.tableType: str = tableType
        self.fields: list = fields
        self.fieldMap = self.getSimpleFieldMap()
        self.default_column_order = default_column_order.get(self.tableType, [])

        self.setLayout(QVBoxLayout())

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        vboxA = QWidget()
        vboxA.setLayout(QVBoxLayout())

        vboxA.layout().addWidget(QLabel("Available"))
        # self.filterAvailable = QLineEdit()
        # vboxA.layout().addWidget(self.filterAvailable)
        self.listAvailable = ColumnsList()
        self.listAvailable.setObjectName("ColumnOrganizer")
        self.listAvailable.itemDoubleClicked.connect(self.onAdd)
        # self.listAvailable.setDragDropMode(QAbstractItemView.DragDropMode.DragDrop)
        self.listAvailable.setDefaultDropAction(Qt.DropAction.MoveAction)
        vboxA.layout().addWidget(self.listAvailable)
        hbox.layout().addWidget(vboxA)

        vboxB = QWidget()
        vboxB.setLayout(QVBoxLayout())

        pbAdd = QPushButton(">>")
        pbAdd.setFixedSize(QSize(96, 32))
        pbAdd.clicked.connect(self.onAdd)
        vboxB.layout().addWidget(pbAdd)
        pbRemove = QPushButton("<<")
        pbRemove.setFixedSize(QSize(96, 32))
        pbRemove.clicked.connect(self.onRemove)
        vboxB.layout().addWidget(pbRemove)

        hbox.layout().addWidget(vboxB)

        vboxC = QWidget()
        vboxC.setLayout(QVBoxLayout())

        hboxC = QWidget()
        hboxC.setMinimumHeight(32)
        hboxC.setLayout(QHBoxLayout())
        hboxC.layout().addWidget(QLabel("Column Order"))

        pbDefaults = QPushButton("Restore Default Order")
        hboxC.layout().addWidget(pbDefaults)
        pbDefaults.clicked.connect(self.restoreColumnDefaults)

        vboxC.layout().addWidget(hboxC)
        # self.filterAvailable = QLineEdit()
        # vboxB.layout().addWidget(self.filterAvailable)
        self.listColumns = ColumnsList()
        self.listColumns.setObjectName("ColumnOrganizer")
        self.listColumns.itemDoubleClicked.connect(self.onRemove)
        self.listColumns.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.listColumns.setDefaultDropAction(Qt.DropAction.MoveAction)
        vboxC.layout().addWidget(self.listColumns)
        hbox.layout().addWidget(vboxC)

        # Reordering column buttons
        vboxD = QWidget()
        vboxD.setLayout(QVBoxLayout())
        for action in ["Top", "Up", "Down", "Bottom"]:
            pb = QPushButton()
            pb.setMinimumSize(QSize(32, 32))
            pb.setIcon(get_resource_qicon(f"arrow-{action.lower()}.svg"))
            vboxD.layout().addWidget(pb)
            pb.pressed.connect(partial(self.onColumnButton, action))
        hbox.layout().addWidget(vboxD)

        controls = QWidget()
        controls.setLayout(QHBoxLayout())
        controls.layout().addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        pbCancel = QPushButton("Cancel")
        pbCancel.setMinimumSize(QSize(160, 48))
        controls.layout().addWidget(pbCancel)
        pbSave = QPushButton("Save")
        pbSave.setMinimumSize(QSize(160, 48))
        controls.layout().addWidget(pbSave)
        self.layout().addWidget(controls)

        self.setMinimumSize(QSize(1200,768))
        self.setWindowIcon(QIcon(get_resource_pixmap("FFF_Architekt Integrated Systems_LOout.png")))
        self.setWindowTitle(f"Sort Column Order - {self.tableType} Table")
        self.setFields()
        pbCancel.pressed.connect(self.onCancel)
        pbSave.pressed.connect(self.onSave)
        self.showCursorTooltip.connect(self.onShowCursorTooltip)
        self.show()

        self.installEventFilter(self)

    def eventFilter(self, target, event):
        if event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key.Key_Escape:
                self.close()
                return True

        return super().eventFilter(target, event)

    def onAdd(self):
        """Move column from available to column order"""
        item = self.listAvailable.takeItem(self.listAvailable.currentIndex().row())
        self.listColumns.addItem(item)

    def onRemove(self):
        """Remove column from column order"""
        row = self.listColumns.currentIndex().row()
        if not self.listColumns.item(row):
            return
        field = self.listColumns.item(row).data(Qt.ItemDataRole.UserRole)
        if field in self.alwaysVisible:
            self.showCursorTooltip.emit("Selected field cannot be hidden")
            return
        item = self.listColumns.takeItem(self.listColumns.currentIndex().row())
        self.listAvailable.addItem(item)
        self.listAvailable.sortItems(Qt.SortOrder.AscendingOrder)

    def onColumnButton(self, action):
        currentRow = self.listColumns.currentIndex().row()

        def insertItem(row):
            item = self.listColumns.takeItem(currentRow)
            self.listColumns.insertItem(row, item)
            index = self.listColumns.indexFromItem(self.listColumns.item(row))
            self.listColumns.setCurrentIndex(index)

        if action == "Top":
            insertItem(0)
        elif action == "Bottom":
            insertItem(self.listColumns.count() - 1)
        elif action == "Up":
            previousRow = currentRow - 1
            if previousRow >= 0:
                insertItem(previousRow)
        elif action == "Down":
            nextRow = currentRow + 1
            if nextRow < self.listColumns.count():
                insertItem(nextRow)

    def getSimpleFieldMap(self):
        fieldMap = getSavedFieldMapJson()
        simpleFieldMap = {}
        for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
            subData = fieldMap.get(key, {})
            # Discard unuseful data
            for _, v in subData.items():
                try:
                    del v["options"]
                except Exception as e:
                    pass
            simpleFieldMap.update(subData)
        return simpleFieldMap

    def setFields(self):
        """Need to merge fields into the saved config order"""
        self.alwaysVisible = []
        if self.tableType:
            self.alwaysVisible = always_visible_table_fields[self.tableType]

        self.listColumns.alwaysVisible = self.alwaysVisible
        for a in self.alwaysVisible:
            if a not in self.fields:
                self.fields.append(a)

        self.listAvailable.clear()
        for field in self.default_column_order:
            if field in self.fields:
                continue
            display = self.fieldMap.get(field, {}).get("display", field)
            item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listAvailable.addItem(item)

        for field in self.fields:
            display = self.fieldMap.get(field, {}).get("display", field)
            if field in self.alwaysVisible:
                 item = QListWidgetItem(f"{display} *")
            else:
                item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listColumns.addItem(item)

    def closeEvent(self, event: QCloseEvent) -> None:
        return super().closeEvent(event)

    def onSave(self):
        """Update config with new column order"""
        columnOrder = []
        for row in range(self.listColumns.count()):
            item = self.listColumns.item(row)
            columnOrder.append(item.data(Qt.ItemDataRole.UserRole))

        self.columnsSaved.emit(columnOrder)
        self.onShowCursorTooltip("Saved")
        self.close()

    def onCancel(self):
        self.close()

    def onShowCursorTooltip(self, message: str):
        """Shows a tooltip at the mouse cursor position"""
        QTimer.singleShot(100, lambda: QToolTip().showText(QCursor().pos() + QPoint(2,2),
                                message,
                                w=self,
                                msecShowTime=1500))

    def restoreColumnDefaults(self):
        self.listAvailable.clear() # Assume all columns in the default order
        self.listColumns.clear()
        self.fields = self.default_column_order
        for field in self.fields:
            display = self.fieldMap.get(field, {}).get("display", field)
            if field in self.alwaysVisible:
                item = QListWidgetItem(f"{display} *")
            else:
                item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listColumns.addItem(item)


if __name__ == "__main__":
    import sys
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    c = ColumnOrganizer(tableType="BOM")
    app.exec()