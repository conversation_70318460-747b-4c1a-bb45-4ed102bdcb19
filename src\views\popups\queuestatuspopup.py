from src.utils.logger import logger
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QLabel, QGridLayout, QPushButton, QSizePolicy, QVBoxLayout, QLineEdit
from src.pyside_util import applyDropShadowEffect
from pubsub import pub

# logger = logging.getLogger(__file__)

from src.utils.logger import logger
from PySide6.QtWidgets import (QVBoxLayout, QScrollArea, QGridLayout,
                               QPushButton, QWidget, QLabel, QSizePolicy)
from PySide6.QtCore import Qt, Signal
from pubsub import pub
from src.utils.logger import logger
from src.widgets.jobqueuecard import JobQueueCard, JobQueueCardExtended

# logger = logging.getLogger()


class JobQueueGrid(QWidget):

    sgnUpdateQueue = Signal(object)
    sgnUpdateJobStatus = Signal(str, str) # (uuid, message)
    sgnClear = Signal()
    def __init__(self, parent):
        super().__init__(parent)
        self.jobs = {}  # {uuid: widget}
        self.setLayout(QVBoxLayout())
        self.layout().setDirection(QVBoxLayout.Direction.BottomToTop)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.layout().setSpacing(4)
        self.sgnUpdateQueue.connect(self.onUpdateQueue)
        self.sgnUpdateJobStatus.connect(self.onUpdateJobStatus)

    def onUpdateQueue(self, data: dict):
        """Display thumbnails and links in grid view"""
        for jobData in data["jobs"]:
            jobId = jobData["uuid"]
            widget = self.jobs.get(jobId)
            if not widget:
                widget = JobQueueCardExtended(self, data=jobData)
                widget.setContentsMargins(0, 0, 0, 0)
                widget.setFixedSize(450, 120)
                self.layout().addWidget(widget)
                self.jobs[jobId] = widget
            else:
                widget.updateData(jobData)
    
    def clear(self):
        for ch in reversed(self.children()):
            if isinstance(ch, JobQueueCard):
                ch.setParent(None)
                ch.deleteLater()
        self.jobs = {}
    
    def onUpdateJobStatus(self, jobId: str, msg: str):
        widget: JobQueueCardExtended = self.jobs.get(jobId)
        if widget:
            widget.updateStatus(msg)


class UploadQueueView(QScrollArea):

    def __init__(self, parent):
        super().__init__(parent)
        self.jobQueueGrid = JobQueueGrid(self)
        self.jobQueueGrid.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Maximum)

        self.setWidget(self.jobQueueGrid)
        self.setWidgetResizable(True)
        self.showScrollbars()
        self.setAlignment(Qt.AlignmentFlag.AlignTop)

        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")
        pub.subscribe(self.logoutCleanup, "logout")
        pub.subscribe(self.setStatusbarMessageRealtime, "set-statusbar-realtime")

    def logoutCleanup(self):
        self.jobQueueGrid.sgnClear.emit()

    def showScrollbars(self):
        """ Only show vertical scrollbars """
        try:
            self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
            self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        except Exception as e:
            logger.error(f"Error showing scrollbars: {e}", exc_info=True)
    
    def onJobQueueUpdated(self, data: dict):
        self.jobQueueGrid.sgnUpdateQueue.emit(data)
    
    def setStatusbarMessageRealtime(self, message, jobId=None):
        self.jobQueueGrid.sgnUpdateJobStatus.emit(jobId, message)


class QueueStatusPopup(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        
        self.data = {}  # Cache the latest details
        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(520)
        self.setMinimumHeight(480)

        widget = QWidget()
        widget.setObjectName("popup")
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())
        grid.setObjectName("popup")

        self.layout().addWidget(widget)

        lblName = QLabel("Job Count:", self)
        lblName.setObjectName("popup")
        grid.layout().addWidget(lblName, 1, 0, 1, 1)
        self.nameValue = QLineEdit("", self)
        grid.layout().addWidget(self.nameValue, 1, 1, 1, 1)
        lblName.hide()
        self.nameValue.hide()

        self.uploadQueue = UploadQueueView(self)
        self.uploadQueue.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.uploadQueue.setObjectName("popup")
        grid.layout().addWidget(self.uploadQueue, 2, 0, 2, 2)
        grid.layout().setContentsMargins(0, 0, 0, 0)
        grid.layout().setSpacing(0)

        for v in [self.nameValue]:
            v.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)

        widget.layout().addWidget(grid)

        self.lblError = QLabel("", self)
        self.lblError.setObjectName("formErrorSidebar")
        widget.layout().addWidget(self.lblError)
        self.lblError.setFixedHeight(32)
        self.lblError.hide()

        self.pbViewAll = QPushButton("View All Jobs")
        self.pbViewAll.setFixedHeight(32)
        self.pbViewAll.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        widget.layout().addWidget(self.pbViewAll)
        self.pbViewAll.clicked.connect(self.onViewAll)

        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")

        applyDropShadowEffect(self)

    def onJobQueueUpdated(self, data):
        jobs = data.get("jobs")
        jobCount = len(jobs)
        self.nameValue.setText(f"{jobCount}")

    def setVisible(self, visible: bool) -> None:
        if visible:
            pub.sendMessage("user-details-get")
        return super().setVisible(visible)

    def onUserDetailsGetResponse(self, data):
        try:
            self.nameValue.setText(data.get("username"))
            self.emailValue.setText(data.get("email"))
            self.tokenValue.setText(str(data.get("tokens")))
            self.data = data
            self.setErrorMessage("")
        except Exception as e:
            logger.info(f"Failed to sync user details {e}")

    def onViewAll(self):
        pub.sendMessage("goto-workspace-view", name="Upload Queue")
        self.hide()

    def onUserDetailsUpdateResponse(self, data: dict):
        self.setErrorMessage(data.get("error", ""))
    
    def setErrorMessage(self, text):
        self.lblError.setText(text)


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    window = QueueStatusPopup(None)
    window.show()
    app.exec()