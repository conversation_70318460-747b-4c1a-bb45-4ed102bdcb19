import pandas as pd
import ast
import re

def extract_properties(annotation_string):
    properties = {}
    value_match = re.match(r'^(.*?)-\[', annotation_string)
    if value_match:
        properties['value'] = value_match.group(1)
    
    # Modified regex to capture full RGB values
    property_matches = re.findall(r'(\w+(?:-\w+)?):(\w+(?:\([^)]+\))?)', annotation_string)
    for key, value in property_matches:
        properties[key] = value
    
    return properties

def process_annot_types_data(file_path, annotation_type='Polygon', flag_word='NO WELDS'):
    """
    Get specific annotations
    Helpful for extracting specific items when you know the annot type
    Useful for extracting weld maps, etc
    """

    # Read the Excel file
    df = pd.read_excel(file_path)
    
    # Initialize lists to store the data
    pdf_ids = []
    pdf_pages = []
    annotation_data = []
    annotation_keys = []
    flag_word_present = []
    
    # Iterate through each row in the DataFrame
    for index, row in df.iterrows():
        pdf_id = row['PDF ID']
        pdf_page = row['PDF Page']
        annot_types_data = ast.literal_eval(row['Annot Types Data'])
        
        # Check if flag word is present in any annotation on this page
        flag_present = any(flag_word.lower() in str(annot).lower() 
                           for annots in annot_types_data.values() 
                           for annot in annots)
        
        # If the flag is present or if the specified annotation type exists, process the annotations
        if flag_present or annotation_type in annot_types_data:
            annotations_to_process = annot_types_data.get(annotation_type, [])
            for annotation in annotations_to_process:
                properties = extract_properties(annotation)
                pdf_ids.append(pdf_id)
                pdf_pages.append(pdf_page)
                annotation_data.append(properties)
                annotation_keys.append(annotation_type)
                flag_word_present.append(flag_present)
    
    # Create a new DataFrame from the extracted data
    result_df = pd.DataFrame(annotation_data)
    result_df['PDF ID'] = pdf_ids
    result_df['PDF Page'] = pdf_pages
    result_df['Annotation Type'] = annotation_keys
    result_df['Flag Word Present'] = flag_word_present
    
    # Reorder columns to have PDF ID, PDF Page, Annotation Type, and Flag Word Present first
    cols = ['PDF ID', 'PDF Page', 'Annotation Type', 'Flag Word Present'] + [col for col in result_df.columns if col not in ['PDF ID', 'PDF Page', 'Annotation Type', 'Flag Word Present']]
    result_df = result_df[cols]
    
    return result_df

# Process the data
file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\Test\exported_general_data.xlsx"
result_df = process_annot_types_data(file_path, annotation_type='Polygon', flag_word='NO WELDS')

# Display the result
print(result_df)

# Optionally, save the result to a new Excel file
result_df.to_excel(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\Test\weld_mapped_data.xlsx", index=False)