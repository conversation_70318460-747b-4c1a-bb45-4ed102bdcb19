"""
Test to debug JSON response issues with Langchain
"""

import os
import asyncio
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set GOOGLE_API_KEY from GEMINI_API_KEY if needed
gemini_key = os.getenv('GEMINI_API_KEY')
if gemini_key and not os.getenv('GOOGLE_API_KEY'):
    os.environ['GOOGLE_API_KEY'] = gemini_key

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import SystemMessage, HumanMessage
    print("✓ Langchain imports successful")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    exit(1)

async def test_json_response():
    """Test JSON response without structured output"""
    print("\n" + "=" * 50)
    print("TESTING JSON RESPONSE")
    print("=" * 50)
    
    try:
        # Create model without structured output
        model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.0,
            max_tokens=500
        )
        
        system_prompt = """You are a BOM classification auditor. 

You must respond with valid JSON in exactly this structure:

{
    "status": "ok" | "issues",
    "issues": {
        "field_name": {
            "current_value": "the_current_value",
            "confidence": 0.95,
            "explanation": "Brief reason",
            "suggested": "corrected_value"
        }
    }
}

If status is "ok", the issues object should be empty: {}
"""
        
        user_prompt = """
REVIEW THE FOLLOWING BOM CLASSIFICATION:

Material Description: "45 ELBOW, 304/304L SS A182-F304/304L, SW, CL3000, B16.11"

Current Classification:
{
  "rfq_scope": "Fittings",
  "material": "Steel, Stainless",
  "astm": "A182",
  "rating": "",
  "fitting_category": "45 Elbow"
}

Analyze each field and return JSON response.
"""
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        print("Making API call...")
        response = await model.ainvoke(messages)
        
        print(f"Raw response: {response.content}")
        print(f"Response type: {type(response.content)}")
        
        # Try to parse JSON (with markdown cleanup)
        try:
            content = response.content.strip()

            # Remove markdown code blocks if present
            if content.startswith('```json'):
                content = content[7:]  # Remove ```json
            if content.startswith('```'):
                content = content[3:]   # Remove ```
            if content.endswith('```'):
                content = content[:-3]  # Remove trailing ```

            content = content.strip()
            print(f"Cleaned content: {content}")

            parsed = json.loads(content)
            print(f"✓ JSON parsing successful:")
            print(f"  Status: {parsed.get('status')}")
            print(f"  Issues: {parsed.get('issues')}")
            print(f"  Issues type: {type(parsed.get('issues'))}")

            return True

        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ API call failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_structured_output_debug():
    """Test structured output with debugging"""
    print("\n" + "=" * 50)
    print("TESTING STRUCTURED OUTPUT DEBUG")
    print("=" * 50)

    try:
        from pydantic import BaseModel, Field, field_validator
        from typing import Dict, Any

        class AuditResponse(BaseModel):
            """Simplified audit response"""
            status: str = Field(..., description="Either 'ok' or 'issues'")
            issues: Dict[str, Any] = Field(default_factory=dict, description="Dictionary of field issues")

            @field_validator('issues', mode='before')
            @classmethod
            def parse_issues_json(cls, v):
                """Parse issues field if it comes as a JSON string"""
                if isinstance(v, str):
                    try:
                        import json
                        return json.loads(v)
                    except json.JSONDecodeError:
                        return {}
                return v

        # Create model with structured output
        base_model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.0,
            max_tokens=500
        )

        structured_model = base_model.with_structured_output(AuditResponse)

        system_prompt = """You are a BOM classification auditor.

You must respond with the exact structure requested. Each issue should have:
- current_value: the current value from the input
- confidence: a number between 0.0 and 1.0
- explanation: brief reason why it's wrong
- suggested: the corrected value
"""

        # Test with different rating values to see what's happening
        test_cases = [
            {
                "name": "Empty Rating",
                "rating": "",
                "expected_issue": True
            },
            {
                "name": "Correct Rating",
                "rating": "3000",
                "expected_issue": False
            },
            {
                "name": "Wrong Rating",
                "rating": "150",
                "expected_issue": True
            }
        ]

        for test_case in test_cases:
            print(f"\n--- Testing: {test_case['name']} ---")
            print(f"Rating value: '{test_case['rating']}' (type: {type(test_case['rating'])})")

            user_prompt = f"""
REVIEW THE FOLLOWING BOM CLASSIFICATION:

Material Description: "45 ELBOW, 304/304L SS A182-F304/304L, SW, CL3000, B16.11"

Current Classification:
- rfq_scope: "Fittings"
- material: "Steel, Stainless"
- astm: "A182"
- rating: "{test_case['rating']}"
- fitting_category: "45 Elbow"

Analyze if the rating field is correct based on "CL3000" in the description.
"""

            print(f"Sending prompt with rating: '{test_case['rating']}'")

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            print("Making structured API call...")
            response = await structured_model.ainvoke(messages)

            print(f"Response status: {response.status}")
            if response.issues:
                for field, issue in response.issues.items():
                    print(f"  Issue with {field}:")
                    print(f"    Current value detected: '{issue['current_value']}'")
                    print(f"    Suggested: '{issue['suggested']}'")
                    print(f"    Explanation: {issue['explanation']}")
            else:
                print("  No issues found")

            # Check if result matches expectation
            has_issues = len(response.issues) > 0
            if has_issues == test_case['expected_issue']:
                print(f"  ✓ Result matches expectation")
            else:
                print(f"  ❌ Expected issues: {test_case['expected_issue']}, Got issues: {has_issues}")

        return True

    except Exception as e:
        print(f"❌ Structured API call failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("JSON Response Debug Test")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY not found")
        return
    
    print(f"✓ API key found: {api_key[:10]}...")
    
    # Test JSON response
    json_success = await test_json_response()
    
    if json_success:
        # Test structured output
        structured_success = await test_structured_output_debug()
        
        if structured_success:
            print("\n✅ Both tests passed!")
        else:
            print("\n❌ Structured output test failed")
    else:
        print("\n❌ JSON response test failed")

if __name__ == "__main__":
    asyncio.run(main())
