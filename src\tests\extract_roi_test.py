"""
Page 3 of Weird Y Alignment VGL document
"""
from src import atom
from src.app_paths import resource_path
import pandas as pd
from os import makedirs
print("Test")

jsonData = [
    {'columnName': 'BOM', 
     'tableCoordinates': {'relativeX0': 0.016339869281045753, 
                          'relativeY0': 0.054608585858585856, 
                          'relativeX1': 0.318218954248366, 
                          'relativeY1': 0.9539141414141414}, 
                          'tableColumns': [
                                {
                                    'pos': {
                                        'relativeX0': 0.016339869281045753, 
                                        'relativeY0': 0.054608585858585856, 
                                        'relativeX1': 0.03125, 
                                        'relativeY1': 0.9539141414141414}}, 
                                {
                                    'material_description': {
                                        'relativeX0': 0.03125, 
                                        'relativeY0': 0.054608585858585856, 
                                        'relativeX1': 0.14297385620915032, 
                                        'relativeY1': 0.9539141414141414}}, 
                                {
                                    'size': {'relativeX0': 0.14297385620915032, 
                                        'relativeY0': 0.054608585858585856, 
                                        'relativeX1': 0.19138071895424835, 
                                        'relativeY1': 0.9539141414141414}}, 
                                {
                                    'ident': {
                                    'relativeX0': 0.19138071895424835, 
                                    'relativeY0': 0.054608585858585856, 
                                    'relativeX1': 0.2849264705882353, 
                                    'relativeY1': 0.9539141414141414}}, 
                                {
                                    'quantity': {
                                    'relativeX0': 0.2849264705882353, 
                                    'relativeY0': 0.054608585858585856, 
                                    'relativeX1': 0.318218954248366, 
                                    'relativeY1': 0.9539141414141414}}], 
                                
                                'headersSelected': True}]

pdfPath = "/home/<USER>/Desktop/wetransfer_combined-oci-drawings-81148-pdf_2024-03-20_1815/flagged_documents/LM2500-ISO-page3.pdf"
jobId = 1
roiPayload = jsonData
projectId = "001"
payloadOptions = "src/atom/payload_options.json"
try:
    finalDf: pd.DataFrame
    sorted_columns_list: list
    finalDf, sorted_columns_list = atom.run_main(
        job_id=jobId,
        pdf_path=pdfPath,
        roi_payload=roiPayload,
        payload_options=payloadOptions,
        project_id=projectId,
        parent_dir="Linde 81213",
        sub_dir="81213-2",          
        debug_page_limit=None,
        analyze_bom=False,
        rename_files=False,
    )
    print("\n\nFinal Json returned. Checking parent table contents\n")
    # print(finalDf)
    makedirs("debug", exist_ok=True)

    df = finalDf["bom_data"]
    df = df.drop(columns=['sys_build', 'sys_path', 'sys_filename'])

    outfile = "debug/LM2500_page3_bom.xlsx"
    # df.to_excel(outfile)
    # re-open adjust columns
    writer = pd.ExcelWriter(outfile, engine='xlsxwriter') 
    df.to_excel(writer, sheet_name='Sheet1', index=False, na_rep='NaN')
    for column in df:
        column_length = max(df[column].astype(str).map(len).max(), len(column))
        col_idx = df.columns.get_loc(column)
        writer.sheets['Sheet1'].set_column(col_idx, col_idx, column_length)
    writer.close()
except Exception as e:
    print(e)