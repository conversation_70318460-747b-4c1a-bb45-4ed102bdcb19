from src.atom.dbManager import DatabaseManager

def test_get_project_source_pdf_storage():
    db = DatabaseManager()
    axis_pid = 7
    filename, project_id = ("C:/Drawings/Clients/axisindustries/Axis 2025-08-05 - Heartwell OSBL/received/Heartwell OSBL Combined.pdf", axis_pid)
    # result = db.get_project_source_pdf_storage(project_id, filename, output_format="pandas")
    result = db.get_project_source_pdf_storage(project_id, filename, output_format="polars")
    assert result is not None
    return result

if __name__ == "__main__":
    df = test_get_project_source_pdf_storage()
    print(df)