import dotenv
import sys
import fitz
import cv2
import io
import time

import pandas as pd
from PIL import Image
import numpy as np

from PySide6.QtGui import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from pathlib import Path

from src.atom.fast_storage import load_df_fast
from src.widgets.pandastable import PandasTable
from src.atom.welddetect.roboflow_utils import RoboFlowHelper

from src.utils.visualize_welds import generate_pdf

DOTENV_PATH = ".env2"

DATA_COLUMNS = ["page",
                "model",
                "version",
                "no_detections",
                "page_width",
                "page_height",
                "x",
                "y",
                "rel_x",
                "rel_y",
                "width",
                "height",
                "confidence",
                "class",
                "class_id",
                # "detection_id",
                "error"]
SUMMARY_COLUMNS = ["page"]


def toQUrl(string) -> str:
    return QUrl.fromLocalFile(string).toString()

def parse_range(astr):
    result=set()
    for part in astr.split(','):
        x=part.split('-')
        result.update(range(int(x[0]),int(x[-1])+1))
    return sorted(result)


def read_file(file: str) -> pd.DataFrame:
    """Reads either an xlsx or feather file"""
    if not file:
        raise ValueError("Need a value for file")
    if file.endswith(".xlsx"):
        return pd.read_excel(file)
    elif file.endswith(".feather"):
        return load_df_fast(file)

def page_to_opencv(page: fitz.Page):
    """Return open CV image from PyMuPDF page"""
    rgb = page.get_pixmap()
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image

def get_page_bytes(page: fitz.Page):
    rgb = page.get_pixmap()
    # Convert RGB to BGR
    return io.BytesIO(rgb.tobytes())


class DialogWithCheckBox(QMessageBox):

    def __init__(self, parent= None):
        super(DialogWithCheckBox, self).__init__()

        self.checkbox = QCheckBox()
        #Access the Layout of the MessageBox to add the Checkbox
        layout = self.layout()
        layout.addWidget(self.checkbox, 1, 2)
        self.setMinimumSize(600, 400)

    def exec_(self, *args, **kwargs):
        """
        Override the exec_ method so you can return the value of the checkbox
        """
        return QMessageBox.exec_(self, *args, **kwargs), self.checkbox.isChecked()


class GeneratePdfDialog(DialogWithCheckBox):

    def __init__(self, parent=None):
        super(GeneratePdfDialog, self).__init__()
        self.setWindowTitle("Generate PDF                     ")
        self.setText("Save weld information to PDF")
        self.checkbox.setText("Include pages which have no detected objects?")
        self.setStandardButtons(QMessageBox.Cancel | QMessageBox.Ok)
        self.setDefaultButton(QMessageBox.Cancel)
        self.setIcon(QMessageBox.Warning)
        self.setMinimumSize(600, 400)


class DetectWorker(QObject):

    finished = Signal()
    predictionReceived = Signal(int, object)
    predictionError = Signal(object)
    message = Signal(str)
    def __init__(self,
                 rf: RoboFlowHelper,
                 doc: fitz.Document,
                 projectId: str,
                 version: int,
                 pages: list[int]):
        super().__init__()
        self.roboflow = rf
        self.projectId = projectId
        self.version = version
        self.doc = doc
        self.pages = pages

    def run(self):
        print("Predicting pages", self.pages)
        print("project", self.projectId, "version", self.version)
        self.message.emit(f"Predicting. {len(self.pages)} remaining..")
        while self.pages:
            pageNum = self.pages.pop(0)
            try:
                page: fitz.Page = self.doc[pageNum - 1]
                page.get_pixmap()
                buffer: io.BytesIO = get_page_bytes(page)
                result = self.roboflow.run_prediction(buffer, self.projectId, self.version)
                # Catch probable error message
                if result.get("message"):
                    self.predictionError.emit(f"Page {pageNum} result warning/error {self.projectId}, {self.version}: {result.get('message')}\n")
                width = result["image"]["width"]
                height = result["image"]["height"]
                records = []
                for prediction in result.get("predictions", []):
                    res = {
                        "page": pageNum,
                        "model": self.projectId,
                        "no_detections": "",
                        "version": self.version,
                        "page_width": width,
                        "page_height": height,
                        "width": prediction["width"],
                        "height": prediction["height"],
                        "class": prediction["class"],
                        "class_id": prediction["class_id"],
                        "confidence": prediction["confidence"],
                        "x": prediction["x"],
                        "y": prediction["y"],
                        "rel_x": round(prediction["x"] / width, 5),
                        "rel_y": round(prediction["y"] / height, 5),
                        "error": "",
                    }
                    records.append(res)
                if not records:
                    records = [{
                        "page": pageNum,
                        "model": self.projectId,
                        "version": self.version,
                        "page_width": width,
                        "page_height": height,
                        "no_detections": "True",
                    }]

                df = pd.DataFrame(records)
                self.predictionReceived.emit(pageNum, df)
                self.message.emit(f"Page {pageNum} result received. {len(self.pages)} remaining..")

            except Exception as e:
                self.predictionError.emit(f"Prediction error on page {pageNum} - {str(e)}")
                self.message.emit(f"Page {pageNum} result errored. {len(self.pages)} remaining..")

        self.message.emit(f"Finished...")
        time.sleep(3)
        self.finished.emit()


class WeldDetect(QWidget):

    activated = Signal()
    def __init__(self,
                 file: str,
                 parent=None):
        super().__init__(parent)
        self.setObjectName("popup")

        self.file = file
        self.doc = None
        self.cvImage = None
        self.roboflow = RoboFlowHelper()

        self.thread: QThread = None
        self.worker: DetectWorker = None
        self.progress: QProgressDialog = None

        self.setLayout(QVBoxLayout())

        self.lineEdits: dict[str, QLineEdit] = {}
        self.dfs: dict[str, pd.DataFrame] = {}

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.layout().addWidget(hbox)

        hbox.layout().addWidget(QLabel("API Key:"))
        self.leApiKey = QLineEdit()
        # self.leApiKey.setEchoMode(QLineEdit.EchoMode.PasswordEchoOnEdit)
        hbox.layout().addWidget(self.leApiKey)
        pbStore = QPushButton()
        pbStore.setMinimumHeight(32)
        pbStore.setText(f"Store Key")
        pbStore.clicked.connect(self.storeApiKey)
        hbox.layout().addWidget(pbStore)
        pbUnset = QPushButton()
        pbUnset.setMinimumHeight(32)
        pbUnset.setText(f"Clear")
        pbUnset.clicked.connect(self.clearApiKey)
        hbox.layout().addWidget(pbUnset)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.layout().addWidget(hbox)
        hbox.layout().addWidget(QLabel("PDF:"))
        self.lePdf = QLineEdit()
        hbox.layout().addWidget(self.lePdf)
        self.lePdf.setText(file)
        pbSelect = QPushButton()
        pbSelect.setMinimumHeight(32)
        pbSelect.setText(f"Select Image/PDF")
        pbSelect.clicked.connect(self.openDocument)
        hbox.layout().addWidget(pbSelect)

        self.lblStatus = QLabel("Status:")
        self.layout().addWidget(self.lblStatus)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setMinimumHeight(48)
        self.layout().addWidget(hbox)
        self.leOutput = QLineEdit()
        self.leOutput.setPlaceholderText("Select saved data file or specify path")
        self.leOutput.setMinimumHeight(48)
        hbox.layout().addWidget(self.leOutput)
        pbSelectFile = QPushButton("Select Save File")
        pbSelectFile.setMinimumHeight(48)
        hbox.layout().addWidget(pbSelectFile)
        pbSelectFile.clicked.connect(self.openDataFile)

        pbReadData = QPushButton("Load Save File Data")
        pbReadData.setMinimumHeight(48)
        pbReadData.clicked.connect(self.onReadData)
        hbox.layout().addWidget(pbReadData)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setMinimumHeight(48)
        self.layout().addWidget(hbox)
        self.cbox = QComboBox()
        self.cbox.setPlaceholderText("Click refresh and choose a project version...")
        self.cbox.setMinimumHeight(48)
        hbox.layout().addWidget(self.cbox)
        pbRefresh = QPushButton("Refresh Projects/Models")
        pbRefresh.setMinimumHeight(48)
        hbox.layout().addWidget(pbRefresh)
        pbRefresh.clicked.connect(self.refreshInfo)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setMinimumHeight(48)
        self.layout().addWidget(hbox)
        self.lePageRange = QLineEdit()
        self.lePageRange.setPlaceholderText("Choose a page range e.g. '1-10, 3, 4, 23-47'")
        self.lePageRange.setMinimumHeight(48)
        hbox.layout().addWidget(self.lePageRange)
        pbDetectPageRange = QPushButton("Detect Missing Pages Range")
        pbDetectPageRange.setMinimumHeight(48)
        hbox.layout().addWidget(pbDetectPageRange)
        pbDetectPageRange.clicked.connect(self.detectPageRange)

        self.tabs = QTabWidget()
        self.layout().addWidget(self.tabs)

        self.table: PandasTable = PandasTable()
        self.table.setDataFrame(pd.DataFrame(columns=DATA_COLUMNS))
        self.tabs.addTab(self.table, "Prediction Data")

        self.tableSummary = PandasTable()
        self.tableSummary.setDataFrame(pd.DataFrame(columns=SUMMARY_COLUMNS))
        self.tabs.addTab(self.tableSummary, "Summary Data")

        self.info = QTextEdit()
        self.info.setReadOnly(True)
        self.tabs.addTab(self.info, "Log")

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.pbRun = QPushButton("Run Detection")
        self.pbRun.clicked.connect(self.onRun)
        self.pbRun.setMinimumHeight(48)
        hbox.layout().addWidget(self.pbRun)

        self.pbSave = QPushButton("Save Results")
        self.pbSave.clicked.connect(self.onSaveResults)
        self.pbSave.setMinimumHeight(48)
        hbox.layout().addWidget(self.pbSave)

        self.pbGeneratePdf = QPushButton("Generate PDF")
        self.pbGeneratePdf.clicked.connect(self.onGeneratePdf)
        self.pbGeneratePdf.setMinimumHeight(48)
        hbox.layout().addWidget(self.pbGeneratePdf)

        self.pbGenerateSummary = QPushButton("Generate Summary")
        self.pbGenerateSummary.clicked.connect(self.onGenerateSummary)
        self.pbGenerateSummary.setMinimumHeight(48)
        hbox.layout().addWidget(self.pbGenerateSummary)

        self.setMinimumWidth(800)

        self.setWindowTitle("Weld Detection")

        self.restoreApiKey()

        self.loadFile(prompt=False)

    @property
    def presetNames(self):
        return self._presetNames

    def openDocument(self):
        path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Document",
            filter="All Supported Files (*.pdf *.jpg *.png)",
        )
        if path:
            self.file = path
            self.lePdf.setText(path)
            self.loadFile()

    def onReadData(self):
        path = self.leOutput.text()
        file = Path(path)
        suffix = file.suffix
        if suffix != ".xlsx":
            QMessageBox.information(self, "File must be xlsx", "Cannot read file must be xlsx")
            return

        if self.thread and not self.thread.isFinished():
             QMessageBox.information(self, "Prediction in progress", "Please cancel running prediction before reading data")
             return

        if file.exists():
            if not self.table.dataframe().empty:
                res = QMessageBox.question(self, "Read data from selected file?", "This will replace the data in the Prediction Data. Proceed?")
                if res != QMessageBox.Yes:
                    return
            df2 = pd.read_excel(path)
            df = pd.DataFrame(columns=DATA_COLUMNS)
            df = pd.concat([df, df2], ignore_index=True)[DATA_COLUMNS]
            self.table.setDataFrame(df)
            QMessageBox.information(self, "Updated", "Table results updated")
        else:
            QMessageBox.information(self, "New output file selected", "A new file is selected as output")

    def openDataFile(self):
        path, _ = QFileDialog.getSaveFileName(
            self,
            "Select a save path or open Document",
            filter="All Supported Files (*.xlsx)",
        )
        if path:
            self.leOutput.setText(path)

    def loadFile(self, prompt=True):

        try:
            self.cvImage = cv2.imread(self.file)
            self.lblStatus.setText(f"Status: Image selected. Shape - {self.cvImage.shape}")
            if prompt:
                QMessageBox().information(self, "Image Loaded", "opened")
            return
        except Exception as e:
            self.cvImage = None

        try:
            self.doc = fitz.open(self.file)
            self.lblStatus.setText(f"Status: pdf selected. {len(self.doc)} Pages")
            if prompt:
                QMessageBox().information(self, "PDF Loaded", "opened")
            return
        except Exception as e:
            self.doc = None

    @presetNames.setter
    def presetNames(self, value: list):
        try:
            self._presetNames = value
        except Exception as e:
             self._presetNames = []

    def eventFilter(self, target, event):
        if event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key.Key_Escape:
                self.close()
                return True

        return super().eventFilter(target, event)

    def closeEvent(self, event: QCloseEvent) -> None:
        return super().closeEvent(event)

    def onSelectData(self, table: str):
        """Open file picker to select data"""
        dialog = QFileDialog(self)
        dialog.setFileMode(QFileDialog.FileMode.AnyFile)
        dialog.setNameFilter("Data Files (*.xlsx)")
        dialog.setViewMode(QFileDialog.ViewMode.List)
        if dialog.exec():
            filenames = dialog.selectedFiles()
            if filenames:
                file = filenames[0]
                self.lineEdits[table].setText(file)
                self.refreshInfo()

    def refreshInfo(self):
        print("Refresh info")

        text = ""
        self.roboflow.api_key = self.leApiKey.text()

        self.roboflow.fetch_info()
        self.cbox.clear()

        for project in self.roboflow.projects:
            name = project["name"]
            versions = project["versions"]
            project_id = project["id"]
            for ver in range(versions, 0, -1):
                self.cbox.addItem(f"{name} - v{ver}", {"project": name, "version": ver, "project_id": project_id})

        self.info.setText(text)

    def onRun(self):

        if self.progress:
            QMessageBox.information(self, "Already in progress", "In progress. Cancel to run again")
            self.progress.show()
            return

        pages = []
        pageRange = self.lePageRange.text()
        if not pageRange:
            pages = [n+1 for n in range(len(self.doc))]
        else:
            try:
                pages = parse_range(pageRange)
            except Exception as e:
                QMessageBox.warning(self, "Invalid Page Range", "Invalid Page Range")
                return

        invalid = [p for p in pages if p < 1 or p > len(self.doc)]
        if invalid:
            QMessageBox.warning(self, "Invalid Page Range", f"Page(s) out of range:\n{invalid}")
            return

        df = self.table.dataframe()
        existing = df["page"].unique().tolist()
        confirm = [p + 1 for p in pages if p + 1 in existing]
        if confirm:
            res = QMessageBox.question(self, "Confirm", f"Detected that page results are already in results? Proceed\nExisting: {confirm}")
            if res != QMessageBox.Yes:
                return

        project = None
        version = None
        project_id = None
        try:
            data = self.cbox.currentData()
            project = data["project"]
            version = data["version"]
            projectId = data["project_id"]
            projectId = Path(projectId).name
        except:
            QMessageBox.warning(self, "Select a project version", f"No project selected")
            return

        res = QMessageBox.question(self,
                                   "Confirm",
                                   f"Proceed run detection?\n\nPage count - {len(pages)}\nProject: {project} ({projectId})\nVersion: {version}")
        if res != QMessageBox.Yes:
            return

        self.progress = QProgressDialog("Running detection", "Cancel", 0, 0, self)
        self.progress.setEnabled(True)
        self.progress.setWindowTitle("Detection")
        self.progress.setModal(False)
        self.progress.show()

        def finished():
            try:
                self.worker.finished.disconnect()
                self.worker.predictionReceived.disconnect()
                self.worker.predictionError.disconnect()
                self.worker.message.disconnect()
                self.worker.stop()
                self.thread.exit()
                self.worker = None
                self.thread.wait()
                self.progress.close()
            except Exception as e:
                self.progress.close()
                self.worker = None

            self.progress = None
            self.worker = None
            self.thread = None

        self.progress.canceled.connect(finished)

        self.thread = QThread(self)
        self.worker = DetectWorker(self.roboflow, self.doc, projectId, version, pages)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(finished)
        self.worker.predictionReceived.connect(self.onPredictionReceived)
        self.worker.predictionError.connect(self.onPredictionError)
        self.worker.message.connect(self.onWorkerMessage)
        self.thread.start()

    def onPredictionReceived(self, pageNum, data: pd.DataFrame):
        self.table.model().removeRowsByQuery(f"page == {pageNum}")
        self.table.model().appendRows(data)
        print(f"Received result for page {pageNum}")

    def onPredictionError(self, data):
        self.info.append(f"\nError - {data}")

    def onWorkerMessage(self, message):
        self.progress.setLabelText(f"{message}")

    def onClose(self):
        self.close()

    def onRecheck(self):
        self.refreshInfo()
        QMessageBox.information(self, "Rechecked data", "rechecked")

    def onRemoveData(self, table):
        self.lineEdits[table].setText("")
        try:
            del self.dfs[table]
        except:
            pass
        self.refreshInfo()

    def clearApiKey(self):
        self.leApiKey.clear()
        dotenv.unset_key(DOTENV_PATH, "robo")
        QMessageBox.information(self, "Key removed from env", "Cleared")

    def restoreApiKey(self):
        self.leApiKey.setText(dotenv.get_key(DOTENV_PATH, "robo"))

    def storeApiKey(self):
        text = self.leApiKey.text()
        if not text:
            self.clearApiKey()
            return
        dotenv.set_key(DOTENV_PATH, "robo", self.leApiKey.text())
        QMessageBox.information(self, "Key stored to env", "Saved")

    def detectPageRange(self):
        if not self.doc:
            QMessageBox.information(self, "Detect Page Range", "Cannot detect missing pages without doc")
            return

        res = QMessageBox.question(self, "Confirm", f"This will detect which pages are missing results and update page range text. Proceed?")
        if res != QMessageBox.Yes:
            return
        pages = len(self.doc)
        df = self.table.dataframe()
        existing = df["page"].unique().tolist()
        missing = []
        for n in range(pages):
            if n + 1 not in existing:
                missing.append(str(n+1))

        self.lePageRange.setText(", ".join(missing))
        QMessageBox.information(self, "Updated Page Range", f"Detected {len(missing)} missing pages")

    def onSaveResults(self):
        file = self.leOutput.text()
        outfile = Path(file)
        suffix = outfile.suffix
        if not file:
            QMessageBox.warning(self, "No save file chosen", "Click select/load to open save file")
            return
        if suffix != ".xlsx":
            QMessageBox.warning(self, "Invalid save file", "Currently supported file are xlsx")
            return

        existingDf = pd.DataFrame(columns=DATA_COLUMNS)
        if outfile.exists():
            res = QMessageBox.question(self, "Proceed save", "The save file xlsx exists. Click yes to merge the results into this file")
            if res != QMessageBox.YES:
                return
            existingDf = pd.concat([existingDf, pd.read_excel(file)], ignore_index=True)

        df = self.table.dataframe().copy()
        newPages = df["page"].unique().tolist()
        existingDf = existingDf[~existingDf["page"].isin(newPages)]

        newDf = pd.concat([existingDf, df], ignore_index=True)
        newDf.to_excel(file, index=False)
        QMessageBox.information(self, "Saved", f"Data saved to {file}")

    def closeEvent(self, event):
        res = QMessageBox.question(self, "Confirm Close", "Confirm close?")
        if res != QMessageBox.YES:
            event.ignore()
            return
        event.accept()

    def onGeneratePdf(self):

        df: pd.DataFrame = self.table.dataframe()
        if df.empty:
            QMessageBox.information(self, "Nothing to save", f"Nothing to save")
            return

        pages = [n + 1 for n in range(self.doc.page_count)]

        # dlg = GeneratePdfDialog(None)
        # res = dlg.exec_()

        # if res[0] != int(QMessageBox.Ok):
        #     return

        pages = [n + 1 for n in range(self.doc.page_count)]
        # if res[1] == True:
        #     print("Including all pages")

        # else:
        #     df = df[df["no_detections"] != "True"]
        #     pages = df["page"].unique().tolist()
        #     print ("handle checkbox not activated")

        df = df[df["no_detections"] != "True"]
        pages = df["page"].unique().tolist()
        if not pages:
            QMessageBox.information(self, "No pages with detected results", f"No data to save")
            return

        dialog = QFileDialog(self)
        dialog.setFileMode(QFileDialog.FileMode.AnyFile)
        dialog.setNameFilter("PDF (*.pdf)")
        dialog.setViewMode(QFileDialog.ViewMode.List)
        if dialog.exec():
            filenames = dialog.selectedFiles()
            if filenames:
                file = filenames[0]
            else:
                return

        df = df[df["no_detections"] != "True"]
        generate_pdf(self.doc.name, df, file)

        msg = f"Saved to: <a href='{toQUrl(file)}'>{file}</a>"
        msgBox = QMessageBox()
        msgBox.setTextFormat(Qt.TextFormat.RichText)
        msgBox.setWindowTitle("PDF saved              ")  # Enlarges message box
        msgBox.setIcon(QMessageBox.Icon.Information)
        msgBox.setText(msg)
        msgBox.exec()
        print("Saved PDF")

    def onGenerateSummary(self):
        df: pd.DataFrame = self.table.dataframe().copy()
        df = df[df["no_detections"] != "True"]
        if df.empty:
            QMessageBox.information(self, "No data to save", f"No data to save")
            return

        dialog = QFileDialog(self)
        dialog.setFileMode(QFileDialog.FileMode.AnyFile)
        dialog.setNameFilter("Data Files (*.xlsx)")
        dialog.setViewMode(QFileDialog.ViewMode.List)
        if dialog.exec():
            filenames = dialog.selectedFiles()
            if filenames:
                file = filenames[0]
            else:
                return

        pages = [n + 1 for n in range(self.doc.page_count)]

        summaryDf = pd.DataFrame()
        summaryDf["filename"] = self.doc.name

        summary = []
        for p in pages:
            group = df[df["page"] == p]
            if group.empty:
                summary.append({
                    "page": p,
                    "total_welds": 0,
                })
            else:
                classes = group["class"].unique().tolist()
                data = {
                    "page": p,
                    "total_welds": len(group),
                }
                for c in classes:
                    data[c] = len(group[group["class"]==c])

                summary.append(data)

        summaryDf = pd.concat([summaryDf, pd.DataFrame(summary)], ignore_index=True)
        summaryDf = summaryDf.fillna(0)
        summaryDf.to_excel(file, index=False)

        msg = f"Saved to: <a href='{toQUrl(file)}'>{file}</a>"
        msgBox = QMessageBox()
        msgBox.setTextFormat(Qt.TextFormat.RichText)
        msgBox.setWindowTitle("Summary saved              ")  # Enlarges message box
        msgBox.setIcon(QMessageBox.Icon.Information)
        msgBox.setText(msg)
        msgBox.exec()
        print("Saved summary")


if __name__ == "__main__":
    import sys
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    try:
        from src.theme import stylesheet
        app.setStyleSheet(stylesheet)
    except:
        pass
    file = r"c:\Users\<USER>\Documents\Drawings\Binder - All ISOs (Except Archives).pdf"
    c = WeldDetect(file)
    c.show()
    app.exec()