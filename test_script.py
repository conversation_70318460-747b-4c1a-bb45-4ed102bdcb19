import os
# os.environ["POLARS_MAX_THREADS"] = "1"

import polars as pl

page_df = pl.DataFrame({
    "a": [1, 2, 3],
    "b": [4, 5, 6]
})
import json
def safe_json_parse(s):
    try:
        return json.loads(s)
    except json.JSONDecodeError:
        return {}
    except Exception as e:
        print(f"Unexpected error: {e}")
        return {}

# 🔴 Try breakpoint here
page_df = page_df.with_columns(
    pl.col("a").map_elements(safe_json_parse, return_dtype=pl.Object).alias("words")
)

# 🔴 Try breakpoint here
print("After with_columns")  # ✅ Should be hit