import matplotlib.pyplot as plt
import json

'''
This was code produced my GPTo that plots the vector with matplotlib and was pretty accurate, except the output was upside down. Definitely usable though. 
https://chatgpt.com/share/634fb34e-47f7-4749-a978-7077dd13657d
'''
def plot_vectors_1(file_paths, colors):
    plt.figure(figsize=(10, 10))
    for file_path, color in zip(file_paths, colors):
        coordinates = extract_coordinates(file_path)
        x_coords, y_coords = zip(*coordinates)
        # Inverting y-coordinates to correct upside-down issue
        y_coords = [1000 - y for y in y_coords]  # Adjust based on the range of coordinates
        plt.plot(x_coords, y_coords, color=color, linewidth=0.96)
        plt.scatter(x_coords, y_coords, color=color)
    plt.title('Vector Drawings from pymupdf/fitz')
    plt.xlabel('X Coordinates')
    plt.ylabel('Y Coordinates')
    plt.grid(True)
    plt.show()
    

def extract_coordinates(file_path):
    with open(file_path, 'r') as file:
        data = json.load(file)
    coordinates = []
    for item in data["items"]:
        if item[0] == "l":
            coordinates.append(item[1])
            coordinates.append(item[2])
    return coordinates



def plot_vectors_2(file_paths, colors, title='Vector Drawings from pymupdf/fitz'): # Alternate colors of each line command for debugging
    plt.figure(figsize=(10, 10))
    color_index = 0  # Initialize color index
    for file_path in file_paths:
        coordinates = extract_coordinates(file_path)
        for i in range(0, len(coordinates), 2):
            x_coords, y_coords = zip(*coordinates[i:i+2])
            # Inverting y-coordinates to correct upside-down issue
            y_coords = [1000 - y for y in y_coords]  # Adjust based on the range of coordinates
            plt.plot(x_coords, y_coords, color=colors[color_index], linewidth=0.96)
            plt.scatter(x_coords, y_coords, color=colors[color_index])
            color_index = (color_index + 1) % len(colors)  # Swap to the next color
    plt.title(title)
    plt.xlabel('X Coordinates')
    plt.ylabel('Y Coordinates')
    plt.grid(True)
    plt.show()


def plot_vectors_from_dict(data: dict, 
                           colors=['blue', 'black', 'purple', 'yellow' ,'red', 'green'], 
                           title='Vector Drawings from pymupdf/fitz'): # Alternate colors of each line command for debugging
    
    def extract_coordinates(data):
        coordinates = []
        for item in data["items"]:
            if item[0] == "l":
                coordinates.append(item[1])
                coordinates.append(item[2])
        return coordinates
    
    plt.figure(figsize=(10, 10))
    color_index = 0  # Initialize color indexx
    coordinates = extract_coordinates(data)
    for i in range(0, len(coordinates), 2):
        x_coords, y_coords = zip(*coordinates[i:i+2])
        # Inverting y-coordinates to correct upside-down issue
        y_coords = [1000 - y for y in y_coords]  # Adjust based on the range of coordinates
        plt.plot(x_coords, y_coords, color=colors[color_index], linewidth=0.96)
        plt.scatter(x_coords, y_coords, s=2, color=colors[color_index])
        color_index = (color_index + 1) % len(colors)  # Swap to the next color
    plt.title(title)
    plt.xlabel('X Coordinates')
    plt.ylabel('Y Coordinates')
    plt.grid(True)
    plt.show()



if __name__ == "__main__":
    # Given original coordinate (Detected Weld) and overlap coordinate (outputted from vector_classified_color_code_charlie.save_overlapping_vectors)    
    file_paths = [r"C:\Users\<USER>\source\repos\ATEM Extract Symbols\detect_graphics\debug\page_0_original_9\overlap-5.txt",r"C:\Users\<USER>\source\repos\ATEM Extract Symbols\detect_graphics\debug\page_0_original_9\original_vector_10.txt"]
    colors = ['blue', 'black', 'purple', 'yellow' ,'red', 'green']
    plot_vectors_2(file_paths, colors)



def gpt_output1(): # --> Was very close, but was upside down
    # Extracted coordinates from the provided vector drawing commands
    coordinates = [
        [845.52001953125, 972.239990234375],
        [845.52001953125, 974.6400146484375],
        [845.280029296875, 974.8800048828125],
        [844.7999877929688, 977.0399780273438],
        [844.7999877929688, 977.0399780273438],
        [843.5999755859375, 979.6799926757812],
        [843.5999755859375, 979.6799926757812],
        [842.1599731445312, 982.3200073242188],
        [842.1599731445312, 982.3200073242188],
        [840.47998046875, 984.47998046875],
        [840.47998046875, 984.47998046875],
        [838.5599975585938, 986.6400146484375],
        [838.5599975585938, 986.6400146484375],
        [836.4000244140625, 988.5599975585938],
        [836.4000244140625, 988.5599975585938],
        [834.47998046875, 989.760009765625],
        [834.47998046875, 989.760009765625],
        [832.3200073242188, 990.9600219726562],
        [832.3200073242188, 990.9600219726562],
        [829.9199829101562, 991.4400024414062],
        [829.9199829101562, 991.4400024414062],
        [828.239990234375, 991.4400024414062],
        [828.239990234375, 991.4400024414062],
        [826.5599975585938, 991.2000122070312],
        [826.5599975585938, 991.2000122070312],
        [825.1199951171875, 990.239990234375],
        [825.1199951171875, 990.239990234375],
        [823.9199829101562, 989.0399780273438],
        [823.9199829101562, 989.0399780273438],
        [823.4400024414062, 987.3599853515625],
        [823.4400024414062, 987.3599853515625],
        [823.4400024414062, 985.4400024414062],
        [845.52001953125, 972.239990234375],
        [823.4400024414062, 985.4400024414062],
        [834.47998046875, 989.52001953125],
        [838.5599975585938, 987.3599853515625],
        [838.5599975585938, 995.280029296875],
        [838.5599975585938, 995.280029296875],
        [830.6400146484375, 1000.0800170898438],
        [830.6400146484375, 1000.0800170898438],
        [830.6400146484375, 991.9199829101562],
        [830.6400146484375, 991.9199829101562],
        [834.47998046875, 989.52001953125]
    ]

    # Separate the coordinates into x and y lists
    x_coords, y_coords = zip(*coordinates)

    # Create the plot
    plt.figure(figsize=(10, 10))
    plt.plot(x_coords, y_coords, color='blue', linewidth=0.96)
    plt.scatter(x_coords, y_coords, color='red')  # Optional: to highlight points
    plt.title('Vector Drawing from pymupdf/fitz')
    plt.xlabel('X Coordinates')
    plt.ylabel('Y Coordinates')
    plt.grid(True)
    plt.show()

