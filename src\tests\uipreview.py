# main.py
import sys
from src.utils.logger import logger

from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from PySide6.QtSvg import QSvgRenderer
from logging.handlers import RotatingFileHandler

from src.splashscreen import Splash<PERSON>creen

from src.config.workspace import Workspace

from src.pyside_util import get_resource_qicon

# def setup_logging():
#     # Create a custom logger
#     global logger
#     logger = logging.getLogger()
#     logger.setLevel(logging.DEBUG)  # Logger is set to capture all messages from DEBUG and above.
#
#     # Prevent adding multiple handlers to the logger
#     if not logger.handlers:
#         # Create console handler and set level to debug
#         console_handler = logging.StreamHandler()
#         #console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         console_handler.setLevel(logging.DEBUG)  # Console handler will only emit ERROR and CRITICAL messages.
#         formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
#         console_handler.setFormatter(formatter)
#
#         # Optionally, add a file handler for persistent logging
#         file_handler = RotatingFileHandler('app_log.log', maxBytes=1024*1024*5, backupCount=5)
#         file_handler.setLevel(logging.DEBUG)
#         file_handler.setFormatter(formatter)
#
#
#         # Add the handlers to the logger
#         logger.addHandler(console_handler)
#         logger.addHandler(file_handler)
#         print("<----------RotatingFileHandler enabled for logging------------->")


def svg_to_pixmap(svg_filename: str) -> QPixmap:
    renderer = QSvgRenderer(svg_filename)
    pixmap = QPixmap(renderer.defaultSize())
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter(pixmap)
    renderer.render(painter)
    painter.end()
    return pixmap


class Tab1(QWidget):

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.setLayout(QVBoxLayout())

        self.layout().addWidget(QLabel("Label 1 - Standard text body"))
        self.textEdit = QTextEdit("Text Input Edit and showing scrollbars")
        self.textEdit.setWordWrapMode(QTextOption.WrapMode.NoWrap)
        for n in range(300):
            self.textEdit.append(f"Line {n} " + "aaa" * 100)
        self.layout().addWidget(self.textEdit)

        for n in range(2):
            pb = QPushButton(f"Icon Button {n}")
            import os.path
            from pathlib import PurePath

            icon = get_resource_qicon("blueprint.png")

            from PySide6.QtSvg import QSvgRenderer


            pix = svg_to_pixmap("src/resources/chevron-right.svg")

            icon = QIcon(pix)
            print(os.path.exists("src/resources/chevron-right.svg"), icon)
            pb.setIcon(icon)
            pb.setCheckable(True)
            self.layout().addWidget(pb)

        for n in range(2):
            pb = QRadioButton(f"Radio {n}")
            pb.setCheckable(True)
            self.layout().addWidget(pb)

        for n in range(2):
            pb = QCheckBox(f"Checkbox {n}")
            self.layout().addWidget(pb)

class Tab2(QWidget):

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.setLayout(QVBoxLayout())

        self.layout().addWidget(QLabel("Label 1 - Standard text body"))
        self.tableWidget = QTableWidget(self)
        self.layout().addWidget(self.tableWidget)
        self.initTable()
    
    def initTable(self):
        self.tableWidget.setRowCount(5)
        self.tableWidget.setColumnCount(3)
 
        self.tableWidget.setItem(0,0, QTableWidgetItem("Name"))
        self.tableWidget.setItem(0,1, QTableWidgetItem("Age"))
        self.tableWidget.setItem(0, 2 , QTableWidgetItem("Gender"))
        self.tableWidget.setColumnWidth(0, 200)
  
        self.tableWidget.setItem(1,0, QTableWidgetItem("John"))
        self.tableWidget.setItem(1,1, QTableWidgetItem("24"))
        self.tableWidget.setItem(1,2, QTableWidgetItem("Male"))
  
        self.tableWidget.setItem(2, 0, QTableWidgetItem("Lucy"))
        self.tableWidget.setItem(2, 1, QTableWidgetItem("19"))
        self.tableWidget.setItem(2, 2, QTableWidgetItem("Female"))
  
        self.tableWidget.setItem(3, 0, QTableWidgetItem("Subaru"))
        self.tableWidget.setItem(3, 1, QTableWidgetItem("18"))
        self.tableWidget.setItem(3, 2, QTableWidgetItem("Male"))
  
        self.tableWidget.setItem(4, 0, QTableWidgetItem("William"))
        self.tableWidget.setItem(4, 1, QTableWidgetItem("60"))
        self.tableWidget.setItem(4, 2, QTableWidgetItem("Male"))


class Tabs(QWidget):

    def __init__(self):
        super().__init__(None)
        self.setLayout(QVBoxLayout(self))
        self.setContentsMargins(0, 0, 0, 0)

        self.tabs = QTabWidget(self)
        self.tabs.addTab(Tab1(None), "Buttons and Text")
        self.tabs.addTab(Tab2(None), "Tables")
        self.tabs.setTabPosition(self.tabs.TabPosition.South)

        # self.setLayout(layout)
        self.layout().addWidget(self.tabs)

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        self.tabs.setDocumentMode(False)
        self.tabs.setDocumentMode(True)

        self.show()


class UiPreview(QMainWindow):

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.setWindowTitle("UI Preview")

        self.tabs = Tabs()
        self.setCentralWidget(self.tabs)


        self.setStatusBar(QStatusBar())
        self.statusBar().setSizeGripEnabled(False)


if __name__ == "__main__":
    sys.path[0] = ''
    from src.theme import stylesheet
    setup_logging()
    try:
        app = QApplication(sys.argv)
        app.setFont("Calibri")
        app.setStyleSheet(stylesheet)
        app.setAttribute(Qt.AA_EnableHighDpiScaling)
        # tabs = Tabs()
        # tabs.show()
        window = UiPreview(None)
        window.setMinimumSize(1024, 768)
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        logger.exception(f"Unhandled exception caused the application to crash: {e}", exc_info=True)   