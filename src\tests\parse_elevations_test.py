import pandas as pd
from src.atom.roiextraction import parse_elevation


if __name__ == "__main__":
    input_file = r"c:\Drawings\Clients\axisindustries\05-21-25\Data\exported_general_data_nofieldmap.xlsx"
    input_file = r"S:\Shared Folders\ATEM\Client Work Space\Brock Services\2025-06-06 Binder\data\brock - exported_general_data_nofieldmap.xlsx"
    df = pd.read_excel(input_file)


    new = []
    unique_elevations = set()
    for index, row in df.iterrows():
        elevation = row["elevation"]
        if not elevation or pd.isna(elevation):
            continue

        elevations = elevation.split(";")
        elevations = [e.strip() for e in elevations]# if "." in e]
        unique_elevations.update(elevations)


    for elevation in sorted(list(unique_elevations)):
        value = parse_elevation(elevation, 1)
        print(elevation, "old")
        print(value, "new")
        new.append({"elevation": elevation, "value": value})


    # output_file = r"c:\Users\<USER>\Documents\GitHub\ATEM_OCR\output\surya_data\surya fulL_ocr_output - manually cleaned 4.xlsx"

    df = pd.DataFrame(new)
    df.to_excel("debug/parse_elevations_test.xlsx", index=False)

