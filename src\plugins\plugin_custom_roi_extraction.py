import os
import time

from pubsub import pub
import pandas as pd
from collections import defaultdict

from src.app_paths import getSourceRawDataPath, getSourceExtractionOptionsPath
from src.utils.convert_roi_payload import convert_roi_payload
from src.atom.roiextraction import RoiExtraction


def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip())  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(1, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part)  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return pages

def plugin_custom_roi_extraction(project_source: tuple,
                          page_range: str,
                          extract_group_list: str = None,
                          roi_payload: str = None,
                          extract_bom: bool = True,
                          extract_general: bool = True,
                          extract_spool: bool = True,
                          extract_spec: bool = True,
                          extract_ifc: bool = True,
                          extract_generic_1: bool = True,
                          extract_generic_2: bool = True,
                          general_fields: str = "",
                          no_atem_job: bool = False,
                          no_atem_job_use_multiprocess: bool = False,
                          no_atem_job_output_dir: str = "debug/extraction_results"):

    """
    Note - this plugin currently only supports with ATEM opened

    ROI extraction with quick configuration

    Args:
        project_source: Tuple of (project_id, filename)
        page_range: Page range to extract e.g. 1-5,7,9-12
        extract_group_list: List of groups to extract e.g. 1,2,3
        roi_payload: ROI payload to use
        extract_bom: Set False to ignore BOM
        extract_general: Set False to ignore general
        extract_spool: Set False to ignore spool
        extract_spec: Set False to ignore spec
        extract_ifc: Set False to ignore IFC
        extract_generic_1: Set False to ignore generic 1
        extract_generic_2: Set False to ignore generic 2
        general_fields: Comma-separated list of general fields to extract. e.g. area, drawing. If specified, only these fields will be extracted from general
        no_atem_job: No multiprocessing or ATEM job. This data is saved externally. Intended for more controlled debugging
        no_atem_job_use_multiprocess: If no_atem_job is True, this sets whether multiprocessing is used
        no_atem_job_output_dir: If no_atem_job is True, this sets the output directory
    """

    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, fileExists)

    group_list = []
    if extract_group_list:
        try:
            group_list = [int(g) for g in extract_group_list.split(",")]
        except Exception:
            pass

        if not group_list:
            try:
                group_list = [int(extract_group_list)]
            except Exception:
                return "Invalid group list format."

    # Group list takes precedence over page range

    if not roi_payload:
        print("Loading saved ROI payload")
        roi_payload = getSourceExtractionOptionsPath(projectId, filename)
        if not os.path.exists(roi_payload):
            error = "A saved ROI payload not found for this project source."
            print(error)
            return error

    roi_payload = convert_roi_payload(roi_payload, force_extract=True, ignore_bom=True)

    print(roi_payload)

    cleaned_roi_payload = {}
    cleaned_roi_payload["ocrPages"] = roi_payload["ocrPages"]

    general_fields = [g.strip().lower() for g in general_fields.split(",")] if general_fields else []

    pages = parse_page_range(str(page_range), 9999)
    final_pages = set()
    unique_groups = set()
    cleaned_roi_payload['pageToGroup'] = {}
    group_to_page = defaultdict(list)
    for page, group in roi_payload['pageToGroup'].items():
        if extract_group_list and group not in group_list:
            continue
        if page in pages:
            cleaned_roi_payload['pageToGroup'][page] = group
            final_pages.add(page)
            unique_groups.add(group)
            group_to_page[group].append(page)

    table_names = [
        "bom",
        "general",
        "spool",
        "spec",
        "ifc",
        "generic_1",
        "generic_2"
    ]

    final_groups = set()
    cleaned_roi_payload['groupRois'] = {}
    for group, rois in roi_payload['groupRois'].items():
        if group not in unique_groups:
            continue
        if extract_group_list and group not in group_list:
            continue

        cleaned = []
        for roi in rois:
            column_name = roi["columnName"].lower()
            if not extract_bom and column_name == "bom":
                continue
            if not extract_general and column_name == "general":
                continue
            if not extract_spool and column_name == "spool":
                continue
            if not extract_spec and column_name == "spec":
                continue
            if not extract_ifc and column_name == "ifc":
                continue
            if not extract_generic_1 and column_name == "generic_1":
                continue
            if not extract_generic_2 and column_name == "generic_2":
                continue

            if column_name not in table_names:
                if not extract_general:
                    continue
                if general_fields and column_name not in general_fields:
                    continue
            cleaned.append(roi)

        if cleaned:
            cleaned_roi_payload['groupRois'][group] = cleaned
            final_groups.add(group)
        else:
            print("No ROI for this group. Removing pages belonging to this group")

    if not final_pages or not final_groups:
        return "No pages are set for extraction. Ensure that the ROI layout is valid for selections or that the page range or group list is valid."

    removed_groups = []
    removed_pages = []
    for group in unique_groups:
        if group not in final_groups:
            print(f"Removing group {group} from pageToGroup")
            for page in group_to_page[group]:
                final_pages.discard(page)
                removed_pages.append(page)
            removed_groups.append(group)

    print(cleaned_roi_payload)

    print()
    print("Removed pages", removed_pages)
    print("Removed groups", removed_groups)

    if not no_atem_job:
        pub.sendMessage("request-roi-extraction-job",
                        projectId=projectId,
                        pdfPath=filename,
                        data=cleaned_roi_payload,
                        legacy=True)

        return f"Extraction set for {len(final_pages)} pages in groups {final_groups}."

    debug = True
    start = time.perf_counter()
    try:
        # Create processor with 1-based page numbers
        processor = RoiExtraction(
            project_id=projectId,
            pdf_path=filename,
            roi_payload=cleaned_roi_payload,
            pages=list(final_pages),  # RoiExtraction expects 1-based page numbers
            # raw_df=raw_data_path,
            missing_regions=pd.DataFrame(),
            multiprocess=no_atem_job_use_multiprocess,
            debug=debug
        )

        # Run the extraction
        processor.run()

        # Get and print results
        results = processor.get_results()
        print("\n=== RESULTS SUMMARY ===")
        for key, df in results.items():
            print(f"{key}: {len(df)} rows")

        # Create an output directory
        output_dir = no_atem_job_output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Method 2: Save all dataframes to different sheets in one file
        # This approach creates a single file with multiple sheets
        save_file = os.path.join(output_dir, "all_results.xlsx")
        with pd.ExcelWriter(save_file) as writer:
            for key, df in results.items():
                sheet_name = key[:31]  # Excel sheet names limited to 31 chars
                if not df.empty:
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # Create an empty sheet with a message
                    pd.DataFrame({"Message": ["No data found"]}).to_excel(
                        writer, sheet_name=sheet_name, index=False
                    )
            print(f"Saved all results to {os.path.join(output_dir, 'all_results.xlsx')}")

    except Exception as e:
        import traceback
        print(f"Error running extraction: {e}")
        traceback.print_exc()

    end = time.perf_counter()
    duration = end - start
    print(f"\n\nTotal time taken: {duration:.2f} seconds")

    return {
        "message": f"Extraction complete for {len(final_pages)} pages in groups {final_groups}.",
        "output_dir": os.path.abspath(output_dir),
        "save_file": os.path.abspath(save_file),
        "duration": f"\n\nTotal time taken: {duration:.2f} seconds"
    }



