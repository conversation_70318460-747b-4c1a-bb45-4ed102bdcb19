import pandas as pd

def clean_material_description(input_file, output_file, sheet_name=None):
    """
    Clean the material_description column by removing the first line when the text contains a newline character.
    
    Parameters:
    input_file (str): Path to the input Excel file
    output_file (str): Path to save the cleaned Excel file
    sheet_name (str, optional): Name of the worksheet to process. If None, the first sheet is used.
    
    Returns:
    None: The function saves the cleaned data to the output file
    """
    # Read the Excel file
    if sheet_name:
        df = pd.read_excel(input_file, sheet_name=sheet_name)
    else:
        df = pd.read_excel(input_file)
    
    # Check if the material_description column exists
    if 'material_description' not in df.columns:
        raise ValueError("The 'material_description' column was not found in the workbook")
    
    # Clean the material_description column
    def clean_description(desc):
        if isinstance(desc, str) and '\n' in desc:
            # Split by newline and take everything after the first line
            return '\n'.join(desc.split('\n')[1:])
        return desc
    
    # Apply the cleaning function to the material_description column
    df['material_description'] = df['material_description'].apply(clean_description)
    
    # Save the cleaned data to a new Excel file
    df.to_excel(output_file, index=False)
    
    print(f"Cleaned data saved to {output_file}")

if __name__ == '__main__':
        # Example usage
    clean_material_description(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 028\Data\Workspace\OCR BOM Data - Shady Hills HRSG Pipe ISOs 10.xlsx", 
                               r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 028\Data\Workspace\Cleaned OCR BOM Data - Shady Hills HRSG Pipe ISOs 10.xlsx")

    # If your data is in a specific sheet
    # clean_material_description('your_input_file.xlsx', 'cleaned_output_file.xlsx', sheet_name='Sheet1')