import pandas as pd
import re
import os
import ast
import sys
import logging
from time import time

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))

try:
    from src.utils.logger import logger
    from src.atom.fast_storage import load_df_fast
except ImportError:
    # Set up a basic logger if the import fails
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s:: Architekt :: %(levelname)s :: %(message)s [%(filename)s:%(lineno)d]'
    )
    logger = logging.getLogger(__name__)

    # Define a simple load function as fallback
    def load_df_fast(path):
        print(f"Using fallback pandas read_feather for {path}")
        return pd.read_feather(path)

# Import TCM formatting function
try:
    from src.atom.welddetect.detect_from_text.format_tcm_data import format_tcm_data, save_tcm_format
except ImportError:
    # Fallback if the module is not available
    print("TCM formatting module not available. Using local import.")
    try:
        from format_tcm_data import format_tcm_data, save_tcm_format
    except ImportError:
        print("WARNING: TCM formatting functions not available.")

def parse_coordinates(coord_input):
    """
    Parse coordinates from various input formats.

    Args:
        coord_input: Coordinates as string, list, or other format

    Returns:
        List of coordinates [x0, y0, x1, y1] or [None, None, None, None] if parsing fails
    """
    # If it's already a list or numpy array, just convert to list
    if isinstance(coord_input, (list, tuple)) and len(coord_input) == 4:
        return list(coord_input)

    # If it's a numpy array, convert to list
    if hasattr(coord_input, 'tolist'):
        try:
            coords = coord_input.tolist()
            if isinstance(coords, list) and len(coords) == 4:
                return coords
        except:
            pass

    # If it's a string, try to parse it
    if isinstance(coord_input, str):
        # Clean up the string - remove newlines and extra spaces
        clean_str = coord_input.replace('\n', ' ').strip()

        # Try to parse as a list
        try:
            coords = ast.literal_eval(clean_str)
            if isinstance(coords, (list, tuple)) and len(coords) == 4:
                return list(coords)
        except (ValueError, SyntaxError):
            # If that fails, try to extract the numbers directly
            try:
                # Extract all numbers from the string
                import re
                numbers = re.findall(r'[-+]?\d*\.\d+|\d+', clean_str)
                if len(numbers) >= 4:
                    # Convert to float and take the first 4
                    return [float(numbers[0]), float(numbers[1]), float(numbers[2]), float(numbers[3])]
            except:
                pass

    # Print debug info for failed parsing
    print(f"Failed to parse coordinates: {coord_input} (type: {type(coord_input)})")
    return [None, None, None, None]

def safe_string_conversion(value):
    """
    Safely convert various types to string.

    Args:
        value: Value to convert to string

    Returns:
        String representation of the value
    """
    # Handle the case when value is a pandas Series or numpy array
    if hasattr(value, 'size') and value.size > 1:
        return str(value)

    try:
        if pd.isna(value):
            return ""
        elif isinstance(value, (int, float)):
            return str(value)
        elif isinstance(value, str):
            return value
        else:
            return str(value)
    except (ValueError, TypeError):
        # Fallback for any other case
        return str(value)

def detect_bom_callouts(text):
    """
    Detect BOM component callouts in text (F#, G#, B#, etc.).

    Args:
        text: Text to search for BOM callouts

    Returns:
        Dictionary with detected callout types and their values
    """
    # Define patterns for different component types - use word boundaries to avoid matching coordinates
    # Only include F, G, and B patterns as valid flange/bolt-up callouts
    patterns = {
        'F': r'\bF\s*(\d+)\b',  # Flange
        'G': r'\bG\s*(\d+)\b',  # Gasket
        'B': r'\bB\s*(\d+)\b',  # Bolts
    }

    # Exclude coordinate patterns like "N 847'2.3/4"" or "E 166'0""
    coordinate_pattern = r'[ENS]\s+\d+\'.*\"'

    # Remove coordinate patterns from the text to avoid false positives
    cleaned_text = text
    coord_matches = re.findall(coordinate_pattern, text)
    for match in coord_matches:
        cleaned_text = cleaned_text.replace(match, "")

    # Debug print to see what we're processing
    print(f"  Processing text for BOM callouts: '{cleaned_text}'")

    results = {}

    # Check if the text contains any individual component patterns (F#, G#, B#)
    # This is a more relaxed check that doesn't require all three components
    for component_type, pattern in patterns.items():
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        if matches:
            for match in matches:
                if match.isdigit() and int(match) > 0:
                    results[component_type] = match
                    print(f"  Found individual component: {component_type}{match}")

    # If we found at least one component, return the results
    if results:
        return results

    # If no individual components found, try more complex patterns

    # Check for exact "F# G# B#" pattern (in any order)
    exact_pattern = r'^(?:[FGB]\d+\s+){1,2}[FGB]\d+$'
    if re.match(exact_pattern, cleaned_text.strip(), re.IGNORECASE):
        print(f"  Found exact BOM callout pattern: '{cleaned_text.strip()}'")
        # Extract individual components
        for component_type, pattern in patterns.items():
            matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
            if matches and matches[0].isdigit() and int(matches[0]) > 0:
                results[component_type] = matches[0]
        return results

    # Try to find a specific pattern with more flexible spacing and order
    # This pattern allows for any combination of F#, G#, B# in any order
    fg_pattern = r'\b([FGB])\s*(\d+)(?:\s+([FGB])\s*(\d+))?(?:\s+([FGB])\s*(\d+))?\b'
    fg_matches = re.findall(fg_pattern, cleaned_text, re.IGNORECASE)

    if fg_matches:
        # Process the specific match
        match = fg_matches[0]
        print(f"  Found specific BOM callout pattern: {match}")

        # Extract components from the match
        for i in range(0, len(match), 2):
            if i+1 < len(match) and match[i] and match[i+1]:
                component_type = match[i].upper()
                component_value = match[i+1]
                # Only include if the number is greater than 0
                if component_value.isdigit() and int(component_value) > 0:
                    results[component_type] = component_value
    else:
        # Try to find a combined pattern with more flexible spacing and order
        combined_pattern = r'\b[FGB]\s*\d+(?:\s+[FGB]\s*\d+)*\b'
        combined_matches = re.findall(combined_pattern, cleaned_text, re.IGNORECASE)

        if combined_matches:
            # Process the combined match
            combined_text = combined_matches[0]
            print(f"  Found combined BOM callout: '{combined_text}'")

            # Extract individual callouts from the combined text
            for component_type, pattern in patterns.items():
                matches = re.findall(pattern, combined_text, re.IGNORECASE)
                if matches and matches[0].isdigit() and int(matches[0]) > 0:
                    results[component_type] = matches[0]

    # Final check to ensure we only have F, G, B callouts with numbers > 0
    filtered_results = {}
    for key, value in results.items():
        if key in ['F', 'G', 'B'] and value.isdigit() and int(value) > 0:
            filtered_results[key] = value

    return filtered_results

def detect_connections(df, x_tolerance=10):
    """
    Detect connection information in text data by grouping related text items by coordinates.

    Args:
        df: DataFrame containing the text data with columns 'value', 'coordinates', etc.
        x_tolerance: Maximum allowed difference in x0 coordinates to consider text aligned (default: 10)

    Returns:
        DataFrame containing detected connection items with their components
    """
    print(f"Starting connection detection with {len(df)} rows of data")
    print(f"Using x_tolerance of {x_tolerance} pixels for alignment")

    # Create a result DataFrame
    result_rows = []

    # Check if required columns exist
    required_columns = ['value', 'coordinates', 'coordinates2']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"WARNING: Missing required columns: {missing_columns}")
        return pd.DataFrame()

    # Add uppercase value column for case-insensitive search
    df['value_upper'] = df['value'].astype(str).str.upper()

    # Find all rows with "CONN TO" pattern
    conn_to_rows = df[df['value_upper'].str.contains(r'CONN\s*TO|CONNECTED\s*TO', regex=True)]
    print(f"Found {len(conn_to_rows)} rows with 'CONN TO' pattern")

    # Process each "CONN TO" row
    for _, conn_row in conn_to_rows.iterrows():
        pdf_id = conn_row.get('pdf_id', '')
        pdf_page = conn_row.get('pdf_page', '')

        # Get the coordinates of the "CONN TO" text
        conn_coords = parse_coordinates(conn_row.get('coordinates', ''))
        conn_coords2 = parse_coordinates(conn_row.get('coordinates2', ''))

        # Skip if we couldn't parse the coordinates
        if conn_coords == [None, None, None, None] or conn_coords2 == [None, None, None, None]:
            print(f"Skipping row with invalid coordinates: {conn_row.get('value', '')}")
            continue

        # Get the x0 coordinate of the "CONN TO" text (from coordinates2 which is more precise)
        conn_x0 = conn_coords2[0]
        conn_y0 = conn_coords2[1]

        # Find all text items on the same page
        page_rows = df[
            (df['pdf_id'] == pdf_id) &
            (df['pdf_page'] == pdf_page)
        ].copy()  # Create a copy to avoid SettingWithCopyWarning

        # Extract coordinates2 for all rows on the page
        page_rows['coords2'] = page_rows['coordinates2'].apply(parse_coordinates)

        # Filter out rows with invalid coordinates
        page_rows = page_rows[page_rows['coords2'].apply(lambda x: x != [None, None, None, None])]

        # Calculate the x-alignment score and y-distance for each row
        # Lower x-alignment score means better horizontal alignment
        # Higher y-distance means the text is further down from the "CONN TO" text
        page_rows['x_alignment'] = page_rows['coords2'].apply(lambda x: abs(x[0] - conn_x0))
        page_rows['y_distance'] = page_rows['coords2'].apply(lambda x: max(0, x[1] - conn_y0))

        # Find the "CONN TO" row itself
        conn_to_text = conn_row.get('value', '').strip()

        # Find potential connected components (rows below the "CONN TO" text)
        # Use a stricter x-alignment filter based on the x_tolerance parameter
        potential_components = page_rows[
            (page_rows['y_distance'] > 0) &                # Below the "CONN TO" text
            (page_rows['x_alignment'] < x_tolerance)       # Strictly aligned horizontally (within x_tolerance pixels)
        ].sort_values(by=['y_distance', 'x_alignment'])

        # Print what we found for debugging
        print(f"\nFound potential components for '{conn_to_text}' on page {pdf_page}:")
        print(f"  CONN TO at x0={conn_x0:.2f}, y0={conn_y0:.2f}")

        # Extract the connection components
        connection_string = conn_to_text
        connected_component = ""
        connection_details = ""
        location_info = []

        # Get the first 5 potential components for analysis
        top_components = potential_components.head(5)

        # Print the potential components for debugging
        for i, (_, row) in enumerate(top_components.iterrows()):
            x0 = row['coords2'][0]
            y0 = row['coords2'][1]
            value = row['value']
            x_align = row['x_alignment']
            y_dist = row['y_distance']
            print(f"  Potential {i+1}: '{value}' at x0={x0:.2f}, y0={y0:.2f}, x_align={x_align:.2f}, y_dist={y_dist:.2f}")

        # Get the connected component (first row below "CONN TO" with good x-alignment)
        if not top_components.empty:
            # Get the best aligned row as the connected component
            best_aligned = top_components.iloc[0]
            connected_component = best_aligned['value']

            # Check if the x-alignment is within tolerance
            if best_aligned['x_alignment'] > x_tolerance / 2:
                print(f"  WARNING: Best aligned component has x_alignment={best_aligned['x_alignment']:.2f}, which is high")
                print(f"  This may indicate the component is not directly under the CONN TO text")

            # Get the next row as the connection details
            if len(top_components) >= 2:
                # Check if the second row is a line number pattern or size pattern
                second_row = top_components.iloc[1]
                second_value = second_row['value']

                # Check if the second row's x-alignment is within tolerance
                if second_row['x_alignment'] > x_tolerance / 2:
                    print(f"  WARNING: Second component has x_alignment={second_row['x_alignment']:.2f}, which is high")
                    print(f"  This may indicate the component is not directly under the CONN TO text")

                # Check for common patterns
                line_number_pattern = r'\d+-\d+(?:-[A-Z0-9]+)?(?:/[A-Z0-9]+-\d+)'
                size_pattern = r'\d+(?:/\d+)?/in/[A-Z]+/\d+'
                gasket_pattern = r'(?:SPIRAL\s+WOUND\s+GASKET|GASKET|RING\s+GASKET)'
                flange_pattern = r'(?:FLANGE|BLIND\s+FLANGE|WELD\s+NECK|SLIP\s+ON)'

                # Check if values match specific patterns
                first_is_line_number = re.search(line_number_pattern, connected_component) is not None
                first_is_size = re.search(size_pattern, connected_component) is not None
                first_is_gasket = re.search(gasket_pattern, connected_component, re.IGNORECASE) is not None
                first_is_flange = re.search(flange_pattern, connected_component, re.IGNORECASE) is not None

                second_is_line_number = re.search(line_number_pattern, second_value) is not None
                second_is_size = re.search(size_pattern, second_value) is not None
                second_is_gasket = re.search(gasket_pattern, second_value, re.IGNORECASE) is not None
                second_is_flange = re.search(flange_pattern, second_value, re.IGNORECASE) is not None

                # Check for F# G# B# pattern (field, grid, bay references)
                field_grid_pattern = r'F\d+\s+G\d+\s+B\d+'
                first_is_field_grid = re.search(field_grid_pattern, connected_component) is not None
                second_is_field_grid = re.search(field_grid_pattern, second_value) is not None

                # Determine the correct order based on patterns
                if second_is_line_number and not first_is_line_number and not first_is_size:
                    # If second row is a line number and first isn't a line number or size, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif first_is_field_grid and second_is_line_number:
                    # If first is field/grid reference and second is line number, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif first_is_field_grid and second_is_size:
                    # If first is field/grid reference and second is size, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif first_is_gasket and second_is_line_number:
                    # If first is gasket and second is line number, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif first_is_flange and second_is_line_number:
                    # If first is flange and second is line number, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif second_is_field_grid and not first_is_field_grid:
                    # If second is field/grid reference and first isn't, keep as is
                    connection_details = second_value
                elif second_is_gasket and not first_is_gasket:
                    # If second is gasket and first isn't, keep as is
                    connection_details = second_value
                elif second_is_flange and not first_is_flange:
                    # If second is flange and first isn't, keep as is
                    connection_details = second_value
                elif len(connected_component) <= 3 and second_is_size:
                    # If first is very short (like a number) and second is size, swap them
                    connection_details = connected_component
                    connected_component = second_value
                elif re.match(r'^\d+$', connected_component) and len(second_value) > 3:
                    # If first is just a number and second is longer, swap them
                    connection_details = connected_component
                    connected_component = second_value
                else:
                    # Default: use second row as connection details
                    connection_details = second_value

            # Get location info from remaining rows
            if len(top_components) > 2:
                for _, row in top_components.iloc[2:].iterrows():
                    val = row['value']
                    # Check if the x-alignment is within tolerance
                    if row['x_alignment'] > x_tolerance:
                        print(f"  WARNING: Skipping potential location info '{val}' with x_alignment={row['x_alignment']:.2f}")
                        continue
                    if (val.startswith('E ') or val.startswith('N ') or val.startswith('EL ')):
                        location_info.append(val)

        # Combine all text to search for BOM callouts
        all_text = ' '.join([
            connection_string,
            connected_component,
            connection_details,
            '; '.join(location_info) if location_info else ""
        ])

        # Search for BOM callouts in all text
        bom_callouts = detect_bom_callouts(all_text)

        # If no BOM callouts found in the directly aligned text, search in nearby text
        if not bom_callouts:
            # Look for text items on the entire page - we'll find the nearest one later
            # No distance filtering at this stage - we want to find ALL possible BOM callouts

            # Create a copy of page_rows to avoid modifying the original
            all_page_rows = page_rows.copy()

            # Calculate distance from the connection point (Euclidean distance)
            all_page_rows['distance'] = all_page_rows['coords2'].apply(
                lambda x: ((x[0] - conn_x0)**2 + (x[1] - conn_y0)**2)**0.5 if x != [None, None, None, None] else float('inf')
            )

            # Sort by distance (nearest first)
            all_page_rows = all_page_rows.sort_values(by='distance')

            print(f"  Looking for BOM callouts in ALL {len(all_page_rows)} page rows...")

            # Store all found BOM callouts with their distances
            all_bom_callouts = []

            # Check each row individually for BOM callout patterns
            for _, row in all_page_rows.iterrows():
                row_text = row['value']
                row_distance = row['distance']

                # Skip very long text (likely not a BOM callout)
                if len(row_text) > 50:
                    continue

                # Skip if the text is too far away (more than 1000 pixels)
                # This is just to limit processing time, not to filter results
                if row_distance > 1000:
                    continue

                print(f"  Checking row text: '{row_text}' (distance: {row_distance:.2f})")
                row_bom_callouts = detect_bom_callouts(row_text)

                if row_bom_callouts:
                    print(f"  Found BOM callouts in row at distance {row_distance:.2f}!")
                    # Store the callouts with their distance
                    all_bom_callouts.append((row_bom_callouts, row_distance, row_text))

            # If we found any BOM callouts, use the nearest one
            if all_bom_callouts:
                # Sort by distance (nearest first)
                all_bom_callouts.sort(key=lambda x: x[1])

                # Use the nearest one
                nearest_callout, nearest_distance, nearest_text = all_bom_callouts[0]
                print(f"  Using nearest BOM callout: '{nearest_text}' at distance {nearest_distance:.2f}")
                bom_callouts = nearest_callout

                # Also print other found callouts for debugging
                if len(all_bom_callouts) > 1:
                    print(f"  Also found {len(all_bom_callouts)-1} other BOM callouts:")
                    for i, (callout, distance, text) in enumerate(all_bom_callouts[1:5]):  # Show up to 5 more
                        callout_str = ' '.join([f"{k}{v}" for k, v in callout.items()])
                        print(f"    {i+1}. '{text}' ({callout_str}) at distance {distance:.2f}")

            # If still not found, try combining nearby text as a last resort
            if not bom_callouts:
                # Use a wider area for the combined text approach
                wider_x_tolerance = x_tolerance * 10
                max_y_distance = 300

                nearby_rows = page_rows[
                    (page_rows['y_distance'] > 0) &                        # Below the "CONN TO" text
                    (page_rows['y_distance'] < max_y_distance) &           # Not too far below
                    (page_rows['x_alignment'] < wider_x_tolerance)         # Within wider horizontal alignment
                ].sort_values(by=['y_distance', 'x_alignment'])

                # Combine all nearby text
                nearby_text = ' '.join(nearby_rows['value'].astype(str).tolist())

                # Search for BOM callouts in nearby text
                if nearby_text:
                    print(f"  Searching for BOM callouts in combined nearby text...")
                    nearby_bom_callouts = detect_bom_callouts(nearby_text)

                    # If found, use these callouts
                    if nearby_bom_callouts:
                        print(f"  Found BOM callouts in combined nearby text!")
                        bom_callouts = nearby_bom_callouts

        # Format BOM callouts for display
        bom_callout_str = ""
        if bom_callouts:
            bom_callout_parts = []
            for component_type, value in bom_callouts.items():
                bom_callout_parts.append(f"{component_type}{value}")
            bom_callout_str = ' '.join(bom_callout_parts)

        # Print the extracted components for debugging
        print(f"  Extracted components:")
        print(f"    Connection String: '{connection_string}'")
        print(f"    Connected Component: '{connected_component}'")
        print(f"    Connection Details: '{connection_details}'")
        if location_info:
            print(f"    Location Info: '{'; '.join(location_info)}'")
        if bom_callout_str:
            print(f"    BOM Callouts: '{bom_callout_str}'")

        # Add to results
        result_rows.append({
            'pdf_id': pdf_id,
            'pdf_page': pdf_page,
            'connection_string': connection_string,
            'connected_component': connected_component,
            'connection_details': connection_details,
            'location_info': '; '.join(location_info) if location_info else "",
            'bom_callouts': bom_callout_str
        })

    # Create a DataFrame from the results
    result_df = pd.DataFrame(result_rows)
    print(f"Found {len(result_df)} connection items")

    return result_df

def main():
    """
    Main function to detect connection information in raw data.
    """
    # Path to the raw data file
    raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-003\data.feather"

    # Output folder and file
    output_folder = r"C:\Users\<USER>\source\repos\Architekt ATOM\output"  # Changed to local output folder
    filename_prefix = "TR-003"

    # Configuration parameters
    x_tolerance = 10  # Default x-tolerance in pixels for alignment

    # Test mode flag - set to True to enable additional testing
    TEST_MODE = True

    # TCM format flag - set to True to generate TCM formatted output
    TCM_FORMAT = True

    # Create output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)

    start_time = time()

    # Load the data using the fast_storage utility
    print(f"Loading data from {raw_data_path}...")
    try:
        df = load_df_fast(raw_data_path)
    except Exception as e:
        print(f"Error loading with load_df_fast: {e}")
        print("Falling back to direct pandas read_feather...")
        df = pd.read_feather(raw_data_path)

    # Print DataFrame info for debugging
    print("\nDataFrame info:")
    print(f"Shape: {df.shape}")
    print("Columns:")
    for col in df.columns:
        print(f"  - {col}: {df[col].dtype}")

    # If in test mode, directly test BOM callout detection on specific rows
    if TEST_MODE:
        print("\n=== TESTING BOM CALLOUT DETECTION ===")

        # Test specific examples mentioned by the user
        specific_tests = [
            "F5 G14 B15",  # Example from page 1673
            "F3 G4 B5",     # Example from page 3
            "F1 G2 B3",     # Another common pattern
            "G2 B3",        # Partial pattern (no F)
            "F4 B5",        # Partial pattern (no G)
            "F3",           # Single component
        ]

        print("\nTesting specific examples:")
        for test_text in specific_tests:
            print(f"\nTesting specific example: '{test_text}'")
            bom_callouts = detect_bom_callouts(test_text)

            # Print the results
            if bom_callouts:
                bom_callout_parts = []
                for component_type, component_value in bom_callouts.items():
                    bom_callout_parts.append(f"{component_type}{component_value}")
                bom_callout_str = ' '.join(bom_callout_parts)
                print(f"  SUCCESS: Found BOM callouts: '{bom_callout_str}'")
            else:
                print(f"  FAILED: No BOM callouts found")

        # Find rows that might contain BOM callouts
        print("\nTesting rows from the dataset:")

        # More flexible pattern to catch more variations
        test_pattern = r'[FGB]\s*\d+'
        test_rows = df[df['value'].astype(str).str.contains(test_pattern, regex=True, case=False)]

        print(f"Found {len(test_rows)} rows with potential BOM callout patterns for testing")

        # Check for specific examples on specific pages
        print("\nChecking for specific examples on specific pages:")

        # Example 1: "F5 G14 B15" on page 1673
        page_1673_rows = df[df['pdf_page'] == 1673]
        print(f"Found {len(page_1673_rows)} rows on page 1673")

        for i, row in page_1673_rows.iterrows():
            value = row.get('value', '')
            if "F5" in value or "G14" in value or "B15" in value:
                print(f"Row {i} on page 1673: '{value}'")
                bom_callouts = detect_bom_callouts(value)
                if bom_callouts:
                    bom_callout_parts = []
                    for component_type, component_value in bom_callouts.items():
                        bom_callout_parts.append(f"{component_type}{component_value}")
                    bom_callout_str = ' '.join(bom_callout_parts)
                    print(f"  SUCCESS: Found BOM callouts: '{bom_callout_str}'")
                else:
                    print(f"  FAILED: No BOM callouts found")

        # Example 2: "F3 G4 B5" on page 3
        page_3_rows = df[df['pdf_page'] == 3]
        print(f"Found {len(page_3_rows)} rows on page 3")

        for i, row in page_3_rows.iterrows():
            value = row.get('value', '')
            if "F3" in value or "G4" in value or "B5" in value:
                print(f"Row {i} on page 3: '{value}'")
                bom_callouts = detect_bom_callouts(value)
                if bom_callouts:
                    bom_callout_parts = []
                    for component_type, component_value in bom_callouts.items():
                        bom_callout_parts.append(f"{component_type}{component_value}")
                    bom_callout_str = ' '.join(bom_callout_parts)
                    print(f"  SUCCESS: Found BOM callouts: '{bom_callout_str}'")
                else:
                    print(f"  FAILED: No BOM callouts found")

        # Test a sample of rows from the dataset
        sample_size = min(100, len(test_rows))
        sampled_rows = test_rows.sample(sample_size, random_state=42)

        print(f"\nTesting {sample_size} random rows with potential BOM callout patterns:")
        for i, row in sampled_rows.iterrows():
            value = row.get('value', '')
            pdf_page = row.get('pdf_page', '')
            print(f"\nTesting row {i} on page {pdf_page} with value: '{value}'")

            # Test the BOM callout detection
            bom_callouts = detect_bom_callouts(value)

            # Print the results
            if bom_callouts:
                bom_callout_parts = []
                for component_type, component_value in bom_callouts.items():
                    bom_callout_parts.append(f"{component_type}{component_value}")
                bom_callout_str = ' '.join(bom_callout_parts)
                print(f"  SUCCESS: Found BOM callouts: '{bom_callout_str}'")
            else:
                print(f"  FAILED: No BOM callouts found")

    # Detect connection items with the specified x_tolerance
    result_df = detect_connections(df, x_tolerance=x_tolerance)

    # Output the results
    if not result_df.empty:
        # Save the connections data
        connections_output_path = os.path.join(output_folder, f"{filename_prefix}_connections_x{x_tolerance}.xlsx")
        try:
            result_df.to_excel(connections_output_path, index=False)
            print(f"Connections results saved to {connections_output_path}")
        except PermissionError:
            # Try with a timestamp to avoid permission issues
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            connections_output_path = os.path.join(output_folder, f"{filename_prefix}_connections_x{x_tolerance}_{timestamp}.xlsx")
            result_df.to_excel(connections_output_path, index=False)
            print(f"Connections results saved to {connections_output_path}")

        # Generate TCM formatted output if requested
        if TCM_FORMAT:
            try:
                # Check if TCM formatting functions are available
                if 'format_tcm_data' in globals() and 'save_tcm_format' in globals():
                    # Generate TCM formatted output
                    tcm_output_path = os.path.join(output_folder, f"{filename_prefix}_tcm_format.xlsx")
                    tcm_df = format_tcm_data(result_df)
                    tcm_df.to_excel(tcm_output_path, index=False)
                    print(f"TCM formatted data saved to {tcm_output_path}")

                    # Print a sample of the TCM formatted data
                    print("\nSample of TCM formatted data:")
                    print(tcm_df.head())
                else:
                    print("\nTCM formatting functions not available. Skipping TCM format generation.")
            except Exception as e:
                print(f"Error generating TCM formatted output: {e}")

        # Print a sample of the results
        print("\nSample of detected connections:")
        print(result_df.head())

        # Print a summary of what was found
        print("\nConnection Summary:")
        for i, row in result_df.head(5).iterrows():
            print(f"Connection {i+1}:")
            print(f"  Connection String: {row['connection_string']}")
            print(f"  Connected Component: {row['connected_component']}")
            print(f"  Connection Details: {row['connection_details']}")
            if row['location_info']:
                print(f"  Location Info: {row['location_info']}")
            if row['bom_callouts']:
                print(f"  BOM Callouts: {row['bom_callouts']}")
            print()

        # Check if specific examples are in the results
        print("\nChecking if specific examples are in the results:")

        # Example 1: Page 1673
        page_1673_results = result_df[result_df['pdf_page'] == 1673]
        print(f"Found {len(page_1673_results)} connections on page 1673")
        for i, row in page_1673_results.iterrows():
            print(f"Connection on page 1673:")
            print(f"  Connection String: {row['connection_string']}")
            print(f"  Connected Component: {row['connected_component']}")
            print(f"  BOM Callouts: {row['bom_callouts']}")

        # Example 2: Page 3
        page_3_results = result_df[result_df['pdf_page'] == 3]
        print(f"Found {len(page_3_results)} connections on page 3")
        for i, row in page_3_results.iterrows():
            print(f"Connection on page 3:")
            print(f"  Connection String: {row['connection_string']}")
            print(f"  Connected Component: {row['connected_component']}")
            print(f"  BOM Callouts: {row['bom_callouts']}")
    else:
        print("\nNo connections found.")

    end_time = time()
    print(f"\nFinished...\nTotal elapsed time: {end_time-start_time:.2f} seconds")

if __name__ == "__main__":
    main()
