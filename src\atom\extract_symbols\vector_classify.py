'''
This is an attempt to isolate, identify, group, and count particular symbols

Only works with version older than < 1.21.1 ??
Potentially solved: https://github.com/pymupdf/PyMuPDF/discussions/2518

Issues:
Vector for field weld is also the same vector for a shop weld with the addition of the cross. So shop weld would be counted twice.
See 557/558 in 'vectors_each_page_557.558 overlap issue.pdf'.
Need to check if the same vector overlaps and remove one from the count
'''
import fitz
import os, json, time
from shapely.geometry import Polygon, LineString
import math
from fitz.utils import getColor
try:
    from .detect_sw_types import process_vectors
except:
    from detect_sw_types import process_vectors
import pandas as pd
import multiprocessing
from pubsub import pub

debug_mode = False

def get_rect_center(rect):
            x1, y1, x2, y2 = rect
            center = ((x1+x2)/2, (y1+y2)/2)
            return center

def get_rgb_color(color_name):
    # Dictionary to map color names to RGB tuples
    color_map = {
        "red": (1.0, 0.0, 0.0),
        "green": (0.0, 1.0, 0.0),
        "blue": (0.0, 0.0, 1.0),
        "purple": (0.5, 0.0, 0.5),
        "black": (0.0, 0.0, 0.0),
        "white": (1.0, 1.0, 1.0)
        # Add more colors as needed
    }
    return color_map.get(color_name, (0.0, 0.0, 0.0))  # Default to black if color not found


# Define the function to draw a fixed-size bounding box centered on the given rect
def draw_bounding_box_centered(page, rect, color=(1, 0, 0), box_size=30):
    center_x = (rect.x0 + rect.x1) / 2
    center_y = (rect.y0 + rect.y1) / 2
    half_size = box_size / 2
    bbox_shape = page.new_shape()
    bbox_shape.draw_rect(fitz.Rect(center_x - half_size, center_y - half_size, center_x + half_size, center_y + half_size))
    bbox_shape.finish(stroke_opacity=1.0, color=get_rgb_color(color), width=1.5)  # Red bounding box with width 1.5
    bbox_shape.commit()


def draw_vector(page, original_data, intersecting_data, connected_data, color):
    shape = page.new_shape()  # Create a new shape object

    def draw_items(items):
        for item in items:
            if item[0] == "l":  # line
                shape.draw_line(item[1], item[2])
            elif item[0] == "re":  # rectangle
                shape.draw_rect(item[1])
            elif item[0] == "qu":  # quad
                shape.draw_quad(item[1])
            elif item[0] == "c":  # curve
                shape.draw_bezier(item[1], item[2], item[3], item[4])

    # Draw original vector elements in their order
    draw_items(original_data["items"])

    # Draw intersecting vector elements
    if intersecting_data:
        draw_items(intersecting_data["items"])

    # Draw connected vector elements
    if connected_data:
        draw_items(connected_data["items"])

    shape.finish(**get_finish_params(original_data, color=color))
    shape.commit()

    # Draw fixed-size bounding box centered on the original data
    def draw_bounding_box_centered(rect, box_size, color):
        center_x = (rect.x0 + rect.x1) / 2
        center_y = (rect.y0 + rect.y1) / 2
        half_size = box_size / 2
        bbox_shape = page.new_shape()
        bbox_shape.draw_rect(fitz.Rect(center_x - half_size, center_y - half_size, center_x + half_size, center_y + half_size))
        bbox_shape.finish(stroke_opacity=1.0, color=get_rgb_color(color), width=1.5)  # Red bounding box with width 1.5 color=(1, 0, 0)
        bbox_shape.commit()

    fixed_box_size = 30  # Set the size of the bounding box here

    draw_bounding_box_centered(original_data["rect"], fixed_box_size, color)

def detect_rects(page, graphics=None):
    """Detect and join rectangles of neighboring vector graphics."""
    delta = 0  # Changed to 0 for strict overlapping check

    def are_neighbors(r1, r2):
        """Detect whether r1, r2 are "neighbors".

        Neighbors are defined as:
        The minimum distance between points of r1 and points of r2 is not
        larger than delta.

        This check supports empty rect-likes and thus also lines.
        """
        return (r1.x0 <= r2.x1 and r1.x1 >= r2.x0 and r1.y0 <= r2.y1 and r1.y1 >= r2.y0)

    # we exclude graphics not contained in reasonable page margins
    parea = page.rect + (-36, -36, 36, 36)

    if graphics is None:
        graphics = page.get_drawings()

    # Exclude graphics not contained inside margins and large graphics
    doc_width = page.rect.width
    doc_height = page.rect.height

    paths = [
        p for p in page.get_drawings()
        if parea.x0 <= p["rect"].x0 <= p["rect"].x1 <= parea.x1
        and parea.y0 <= p["rect"].y0 <= p["rect"].y1 <= parea.y1
        and p["rect"].width < 0.04 * doc_width  # Ignore elements larger than 4% of doc width
        and p["rect"].height < 0.04 * doc_height  # Ignore elements larger than 4% of doc height
    ]

    # list of all vector graphic rectangles
    prects = sorted([p["rect"] for p in paths], key=lambda r: (r.y1, r.x0))

    new_rects = []  # the final list of the joined rectangles

    # -------------------------------------------------------------------------
    # The strategy is to identify and join all rects that are neighbors
    # -------------------------------------------------------------------------
    while prects:  # the algorithm will empty this list
        r = prects[0]  # first rectangle
        repeat = True
        while repeat:
            repeat = False
            for i in range(len(prects) - 1, 0, -1):  # back to front
                if are_neighbors(prects[i], r):
                    r |= prects[i].tl  # join in to first rect
                    r |= prects[i].br
                    del prects[i]  # delete this rect
                    repeat = True

        prects[0] = +r
        # move first item over to result list
        new_rects.append(prects.pop(0))
        prects = sorted(list(set(prects)), key=lambda r: (r.y1, r.x0))

    new_rects = sorted(list(set(new_rects)), key=lambda r: (r.y1, r.x0))
    return [r for r in new_rects if r.width > 5 and r.height > 5]

def point_to_tuple(point):
    """Convert a fitz.Point to a tuple."""
    return (point.x, point.y)

def is_visible_color(fill_color, fill_opacity):
    # Debugging: Print the types and values received
    if debug_mode:
        print(f"Received fill_color: {fill_color} (type: {type(fill_color)})")
        print(f"Received fill_opacity: {fill_opacity} (type: {type(fill_opacity)})")

    # Ensure fill_color is a tuple for consistent comparison
    try:
        fill_color = tuple(fill_color)
    except Exception as e:
        if debug_mode:
            print(f"Fill color is not a tuple: {e}")

    # Check if fill color is visible
    if fill_color == (0.0, 0.0, 0.0) and fill_opacity == 1.0:
        if debug_mode:
            print("Color is visible.")
        return True
    else:
        if debug_mode:
            print("Color is not visible.")
    return False

def calculate_circularity(polygon):
    if not polygon.is_valid or polygon.area == 0:
        if debug_mode:
            print("Invalid polygon for circularity calculation.")
        return 0
    return (4 * math.pi * polygon.area) / (polygon.length ** 2)

def is_symmetrical(polygon):
    minx, miny, maxx, maxy = polygon.bounds
    centerline = LineString([((minx + maxx) / 2, miny), ((minx + maxx) / 2, maxy)])
    left_half = polygon.intersection(centerline.buffer((maxx - minx) / 2))
    right_half = polygon.intersection(centerline.buffer((maxx - minx) / 2))
    return left_half.equals(right_half)

def check_cross_in_circle(polygon, items):
    center = polygon.centroid
    if debug_mode:
        print(f"Centroid of the polygon: {center}")

    # Assuming the last two lines in the list are the cross lines based on the vector data structure provided
    # Assuming the last two lines in the list are the cross lines based on the vector data structure provided
    try:
        cross_lines = [LineString([item[1], item[2]]) for item in items[-2:]]
    except Exception:
        if debug_mode:
            print("Could not detect cross lines. Returning False")
        return False

    # Check if both cross lines intersect each other within the bounds of the circle
    if len(cross_lines) == 2:
        print(cross_lines[0].length, cross_lines[1].length)
        if cross_lines[0].length < 6 or cross_lines[1].length < 6:
            return False
        # print(cross_lines[0].length, cross_lines[1].length)
        if cross_lines[0].intersects(cross_lines[1]):
            intersection_point = cross_lines[0].intersection(cross_lines[1])
            if debug_mode:
                print(cross_lines)
            if intersection_point.within(polygon):
                if debug_mode:
                    print("Intersection detected within the circle at:", intersection_point)
                return True
            else:
                if debug_mode:
                    print("Intersection point is not within the circle.")
        else:
            if debug_mode:
                print("Cross lines do not intersect.")
    else:
        if debug_mode:
            print("Not enough lines to form a cross.")

    return False

def is_potential_field_weld(path, index, seqno):
    print(f"\n\n----> Checking path with index: {index+1} | seqno: {seqno}")

    # try:
    #     if not is_visible_color(path['fill'], path['fill_opacity']):
    #         #print("Rejected: Fill color is not visible or opacity is incorrect.")
    #         return False
    # except Exception as e:
    #     print(f"'fill' or 'fill opacity' does not exist. Error: {e}")

    # try:
    #     if path['type'] != "fs":
    #     # if path['type'] not in ["fs", "f"]:
    #         print(f"Type is not 'fs'. Type: {path['type']}")
    #         return False
    # except Exception as e:
    #     print(f"'type' does not exist. Error: {e}")

    # Create the polygon from the circle segments (assuming all but the last two lines form the circle)
    try:
        circle_points = [item[1] for item in path['items'][:-2]] + [path['items'][0][1]]
    except:
        return False

    if len(set(circle_points)) < 4:
        if debug_mode:
            print(f"Not enough unique coordinates for a polygon: {circle_points}")
        return False

    try:
        polygon = Polygon(circle_points)
    except Exception as e:
        if debug_mode:
            print(f"Failed to create a polygon: {e}")
        return False

    #polygon = Polygon(circle_points)

    if not polygon.is_valid or polygon.is_empty:
        if debug_mode:
            print("Polygon is not valid or not closed.")
        return False

    # Comment this out if valid field welds are being skipped
    if calculate_circularity(polygon) < 0.75 or not is_symmetrical(polygon):
        #print(f"Shape is not circular, rule out field weld")
        return False
    if debug_mode:
        print("Polygon is valid and closed. Polygon area:", polygon.area)

    # Invoke the function to check for a cross within the circle
    if check_cross_in_circle(polygon, path['items']):
        if debug_mode:
            print("Shape is potentially a field weld with a proper cross.")
        return True
    else:
        if debug_mode:
            print("No proper cross detected or shape does not meet the criteria.")

    return False

def is_potential_shop_bw(path, index, seqno):
    #print(f"\n\n----> Checking path with index: {index+1} | seqno: {seqno}")



    try:
        if not is_visible_color(path['fill'], path['fill_opacity']):
            #print("Rejected: Fill color is not visible or opacity is incorrect.")
            return False
    except Exception as e:
        if debug_mode:
            print(f"'fill' or 'fill opacity' does not exist. Error: {e}")

    try:
        if path['type'] != "fs":
        # if path['type'] not in ["fs", "f"]:
            if debug_mode:
                print(f"Type is not 'fs'. Type: {path['type']}")
            return False
    except Exception as e:
        if debug_mode:
            print(f"'type' does not exist. Error: {e}")

    # Assuming each item in path['items'] is a tuple of form (command, (x1, y1), (x2, y2))
    points = [item[1] for item in path['items']] + [path['items'][0][1]]  # Ensure the polygon is closed

    if len(set(points)) < 4:
        if debug_mode:
            print(f"Not enough unique coordinates for a polygon: {points}")
        return False

    polygon = Polygon(points)
    if not polygon.is_valid or polygon.is_empty:
        #print("Polygon is not valid or not closed.")
        return False
    #print(f"Polygon is valid and closed. Index: {index+1} Seqno: {seqno}")

    # Check circularity and symmetry
    if calculate_circularity(polygon) > 0.75 and is_symmetrical(polygon):
        #print(f"Shape is potentially a shop BW. Index: {index+1} Seqno: {seqno}")
        return True

    #print("Shape does not meet the criteria for being a shop BW.")
    return False

def stroke_match_fill(stroke, fill):
    """Checks the stroke path points match the fill path points

    TODO - if still missing, consider adding tolerance
    """
    if len(fill["items"]) < len(stroke["items"]):
        pointsA = fill["items"]
        pointsB = stroke["items"]
    else:
        pointsA = stroke["items"]
        pointsB = fill["items"]

    for n, item in enumerate(pointsA):
        if item[1] == pointsB[n][1]: # Potentially add tolerance
            continue
        return False

    return True

def get_paths_of_interest(paths):
    res = []
    last_path = None

    def check_potential_weld(path, index, seqno) -> bool:
        """If potential weld detected, add it to results and return True"""
        if is_potential_shop_bw(path, index, seqno):
            # Potential Shop BW
            res.append((index, "shop_bw", path))
        elif is_potential_field_weld(path, index, seqno):
            # Potential FW
            res.append((index, "field_bw", path))
        else:
            return False

        return True

    def detect_circle_termination_index(items) -> int:
        for n, item in enumerate(items):
            if n == 0:
                continue
            # print(n, items[0][1], item[2])
            if items[0][1] == item[2]: # Check if returned to starting point
                return n
        return None


    for index, path in enumerate(paths):

        # IMPORTANT - Temporary workaround to avoid
        # detecting symbols outside of actual drawing.
        # Here, we assume these are drawn early in PDF extraction
        if index < 50:
            continue

        seqno = path.get('seqno', 0)

        if check_potential_weld(path, index, seqno):
            pass
        elif last_path and path["type"] == "s" and last_path["type"] == "f":
            # Some welds may have been drawn unconventionally/hidden
            # Do this only if we do not initially detect a weld in order to avoid
            # duplicate detections
            if stroke_match_fill(path, last_path):
                # Matching f and s paths. We use the fill path
                # as this seems to represent the circular drawing if applicable
                path = last_path
                path["type"] = "fs"
                last_path = path

                # The next index seems to have the actual data we need
                # In particular, the field weld cross
                # Sometimes the drawings contains multiple separate graphics
                # So try to find circle, and +2 for potential cross symbols
                next_path = paths[index]
                end = detect_circle_termination_index(next_path["items"])
                next_path["type"] = "fs"
                next_path["fill_opacity"] = last_path["fill_opacity"]
                next_path["fill"] = last_path["fill"]
                next_path["rect"] = path["rect"]
                if end is not None:
                    # A little inefficient but reduce the amount of points to expected circle
                    next_path["items"] = next_path["items"][:end+2]
                    if check_potential_weld(next_path, index, seqno):
                        pass
                    else:
                        # Final check
                        next_path["items"] = next_path["items"][:-1]
                        check_potential_weld(next_path, index, seqno)

                next_path["type"] = "ignore" # So we can ignore it next

        last_path = path
    if debug_mode:
        print(f"Found {len(res)} paths of interest")
    return res

#paths_of_interest = get_paths_of_interest(paths)

def get_finish_params(path, color=None, fill_opacity=None):
    if color:
        c = getColor(color)
    else:
        c = path.get("color")

    # if fill_opacity is None:
    #     fill_opacity = 1

    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        # "fill_opacity": fill_opacity if path.get("fill_opacity") is None else path["fill_opacity"],
        # "fill": path["fill"] if path.get("fill") is not None else 0, # None
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),  # Set fill_opacity to 0 if no fill
        "fill": path.get("fill", None),  # Set fill to None if no fill
        "color": c if path.get("color") is not None else None,
        "dashes": path["dashes"] if path.get("dashes") is not None else None,
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
        "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,  # Default to 0 if None or empty
        "width": path["width"] if path.get("width") is not None else None
    }

def bbox_ratio(rect_a, rect_b):
    area_a = max(1, rect_a.width * rect_a.height)
    area_b = max(1, rect_b.width * rect_b.height)
    # print()
    ratio = area_a / area_b
    if debug_mode:
        print("ratio", ratio)
    return ratio

def is_overlapping(rect_a, rect_b) -> bool:
    a_left, a_top = rect_a[0], rect_a[1]
    a_right, a_bottom = rect_a[2], rect_a[3]
    b_left, b_top = rect_b[0], rect_b[1]
    b_right, b_bottom = rect_b[2], rect_b[3]
    return (a_left < b_right
        and a_right > b_left
        and a_top < b_bottom
        and a_bottom > b_top)

def save_overlapping_vectors(page, path_dict, output_dir, original_index):
    # Create a new folder for this original vector
    folder_name = os.path.join(output_dir, f"page_{page.number+1}_original_{original_index}")
    os.makedirs(folder_name, exist_ok=True)

    # Create a new PDF document for this vector and its overlaps
    doc = fitz.open()

    # Save the original vector for reference
    target_rect = path_dict["rect"]
    original_page = doc.new_page(width=page.rect.width, height=page.rect.height)
    shape = original_page.new_shape()
    for item in path_dict["items"]:
        if item[0] == "l":  # line
            shape.draw_line(item[1], item[2])
        elif item[0] == "re":  # rectangle
            shape.draw_rect(item[1])
        elif item[0] == "qu":  # quad
            shape.draw_quad(item[1])
        elif item[0] == "c":  # curve
            shape.draw_bezier(item[1], item[2], item[3], item[4])
    shape.finish(**get_finish_params(path_dict))
    shape.commit()

    # Save the original vector details in a text file
    with open(os.path.join(folder_name, f'original_vector_{original_index+1}.txt'), 'w', encoding='utf-8') as f:
        items_serializable = []
        for item in path_dict['items']:
            if item[0] == "l":
                items_serializable.append((item[0], point_to_tuple(item[1]), point_to_tuple(item[2])))
            elif item[0] == "re":
                items_serializable.append((item[0], (item[1].x0, item[1].y0, item[1].x1, item[1].y1)))
            elif item[0] == "qu":
                items_serializable.append((item[0], (item[1].ul.x, item[1].ul.y, item[1].ur.x, item[1].ur.y, item[1].lr.x, item[1].lr.y, item[1].ll.x, item[1].ll.y)))
            elif item[0] == "c":
                items_serializable.append((item[0], (item[1].x, item[1].y), (item[2].x, item[2].y), (item[3].x, item[3].y), (item[4].x, item[4].y)))
        path_dict_serializable = {
            'items': items_serializable,
            'fill': path_dict.get('fill', None),
            'color': path_dict.get('color', None),
            'dashes': path_dict.get('dashes', []),
            'even_odd': path_dict.get('even_odd', False),
            'closePath': path_dict.get('closePath', None),
            'lineJoin': path_dict.get('lineJoin', 0),
            'lineCap': path_dict.get('lineCap', 0),
            'width': path_dict.get('width', 1),
            'stroke_opacity': path_dict.get('stroke_opacity', 0),
            "fill_opacity": path_dict.get("fill_opacity", 0) if path_dict.get("fill") else None,
            "level": path_dict.get('level', 0),
            "seqno": path_dict.get('seqno', 0),
            "type": path_dict.get('type', '')
        }
        f.write(json.dumps(path_dict_serializable, indent=4))

    paths = page.get_drawings()
    overlapping_paths = [p for p in paths if is_overlapping(target_rect, p["rect"])]

    for i, path in enumerate(overlapping_paths):
        overlap_page = doc.new_page(width=page.rect.width, height=page.rect.height)
        shape = overlap_page.new_shape()

        # Draw the original vector
        for item in path_dict["items"]:
            if item[0] == "l":  # line
                shape.draw_line(item[1], item[2])
            elif item[0] == "re":  # rectangle
                shape.draw_rect(item[1])
            elif item[0] == "qu":  # quad
                shape.draw_quad(item[1])
            elif item[0] == "c":  # curve
                shape.draw_bezier(item[1], item[2], item[3], item[4])

        # Draw the overlapping vector
        for item in path["items"]:
            if item[0] == "l":  # line
                shape.draw_line(item[1], item[2])
            elif item[0] == "re":  # rectangle
                shape.draw_rect(item[1])
            elif item[0] == "qu":  # quad
                shape.draw_quad(item[1])
            elif item[0] == "c":  # curve
                shape.draw_bezier(item[1], item[2], item[3], item[4])

        shape.finish(**get_finish_params(path))
        shape.commit()

        # Save the overlapping vector details in a text file
        with open(os.path.join(folder_name, f'overlap-{i+2}.txt'), 'w', encoding='utf-8') as f:
            items_serializable = []
            for item in path['items']:
                if item[0] == "l":
                    items_serializable.append((item[0], point_to_tuple(item[1]), point_to_tuple(item[2])))
                elif item[0] == "re":
                    items_serializable.append((item[0], (item[1].x0, item[1].y0, item[1].x1, item[1].y1)))
                elif item[0] == "qu":
                    items_serializable.append((item[0], (item[1].ul.x, item[1].ul.y, item[1].ur.x, item[1].ur.y, item[1].lr.x, item[1].lr.y, item[1].ll.x, item[1].ll.y)))
                elif item[0] == "c":
                    items_serializable.append((item[0], (item[1].x, item[1].y), (item[2].x, item[2].y), (item[3].x, item[3].y), (item[4].x, item[4].y)))
            path_dict_serializable = {
                'items': items_serializable,
                'fill': path.get('fill', None),
                'color': path.get('color', None),
                'dashes': path.get('dashes', []),
                'even_odd': path.get('even_odd', False),
                'closePath': path.get('closePath', None),
                'lineJoin': path.get('lineJoin', 0),
                'lineCap': path.get('lineCap', 0),
                'width': path.get('width', 1),
                'stroke_opacity': path.get('stroke_opacity', 0),
                "fill_opacity": path.get("fill_opacity", 0) if path.get("fill") else None,
                "level": path.get('level', 0),
                "seqno": path.get('seqno', 0),
                "type": path.get('type', '')
            }
            f.write(json.dumps(path_dict_serializable, indent=4))

    save_path = os.path.join(folder_name, "overlapping_graphics.pdf")
    doc.save(save_path)
    doc.close()

    return folder_name

def detect_sw_attributes(page, original_vector_data, vector_id):
    is_socket_weld = False
    detected_vectors = []
    paths = page.get_drawings()
    target_rect = original_vector_data["rect"]
    overlapping_paths = [p for p in paths if is_overlapping(target_rect, p["rect"])]

    for overlap_vector_data in overlapping_paths:
        original, intersecting, connected, is_socket_weld = process_vectors(original_vector_data, overlap_vector_data, page, vector_id)
        if is_socket_weld:  # Exit the loop if a socket weld is detected
            detected_vectors.append((original, intersecting, connected))
            break

    if not overlapping_paths:
        return detected_vectors, is_socket_weld
        #print("No overlapping paths detected.")

    return detected_vectors, is_socket_weld

def process_page(doc, page_num, outpdf, draw_pages, debug_vectors):
    page = doc[page_num]
    paths = page.get_drawings(extended=True)  # extract existing drawings
    # outpage = outpdf.new_page(width=page.rect.width, height=page.rect.height)
    outpdf.insert_pdf(doc, from_page=page_num, to_page=page_num)
    outpage = outpdf[0]

    detected_welds = []
    # Initialize counters for weld types
    weld_counts = {
        "page_num": page_num + 1,
        "shop_bw": 0,
        "field_bw": 0,
        "shop_sw": 0,
        "field_sw": 0,
        "screw_conn": 0,
        "error": None,
    }

    vector_id = 0
    all_detected_vectors = []  # List to collect all detected vectors
    drawn_vectors = set()  # Set to keep track of drawn vectors

    paths_of_interest = get_paths_of_interest(paths)

    for n, (index, base_weld_type, weld_path) in enumerate(paths_of_interest):
        bbox = weld_path["rect"]
        weld_shapes = []
        weld_color = None  # Initialize weld_color to None

        # Draw the items of interest
        for item in weld_path["items"]:
            shape = outpage.new_shape()
            valid = True
            if item[0] == "l":  # line
                shape.draw_line(item[1], item[2])
            elif item[0] == "re":  # rectangle
                shape.draw_rect(item[1])
            elif item[0] == "qu":  # quad
                shape.draw_quad(item[1])
            elif item[0] == "c":  # curve
                shape.draw_bezier(item[1], item[2], item[3], item[4])
            else:
                print("\n\nPath not a handled type..")
                valid = False
            if valid:
                weld_shapes.append(shape)  # Not important, but save so we can choose a color later


        folder_name = None
        if base_weld_type == "shop_bw" and vector_id == 9:
            pass # For debugging

        if base_weld_type == "shop_bw":
            weld_color = "red"
            # Check overlapping_vectors
            if n == 3:
                pass
            detected_vectors, is_socket_weld = detect_sw_attributes(page, weld_path, vector_id)

            # if debug_vectors: # --> Debug vectors and outputs
            #     folder_name = save_overlapping_vectors(page, weld_path, debug_output_path, vector_id)

            if is_socket_weld:
                base_weld_type = "shop_sw"
                weld_color = "purple"  # Shop BW
                all_detected_vectors.extend([(original, intersecting, connected, weld_color) for original, intersecting, connected in detected_vectors])

        elif base_weld_type == "field_bw":
            weld_color = "green"

        # Draw items from paths with intersecting bounding box
        # Look for socket
        for index2, path2 in enumerate(paths):
            if "items" not in path2:
                continue
            # Classify sub-shapes
            if len(path2["items"]) != 3:  # 3 lines proxy for U shaped
                continue
            if "rect" not in path2:
                continue
            bbox2 = path2["rect"]
            bbox_area = bbox.width * bbox.height
            bbox2_area = bbox2.width * bbox2.height
            if not bbox_area or not bbox2_area:
                continue
            ratio = float(bbox2_area) / float(bbox_area)
            if ratio > 6 or ratio < 0:  # adjust these thresholds
                continue
            if bbox == bbox2:
                continue
            intersecting = is_overlapping(bbox, bbox2)
            if not intersecting:
                continue

            for item in path2["items"]:
                shape = outpage.new_shape()
                if item[0] == "l":  # line
                    shape.draw_line(item[1], item[2])
                elif item[0] == "re":  # rectangle
                    shape.draw_rect(item[1])
                elif item[0] == "qu":  # quad
                    shape.draw_quad(item[1])
                elif item[0] == "c":  # curve
                    shape.draw_bezier(item[1], item[2], item[3], item[4])
                # Call shape.finish() with only the parameters that are valid
                shape.finish(**get_finish_params(path2, color=weld_color))
                shape.commit()

            # Track drawn vectors
            drawn_vectors.add(path2["rect"])

        weld_center = get_rect_center(weld_path["rect"])

        if draw_pages:
            for weld_shape in weld_shapes:
                weld_shape.finish(**get_finish_params(weld_path, color=weld_color))
                weld_shape.commit()

            if weld_color == "green":
                print(vector_id, weld_path)

            # Draw the bounding box centered on the weld path's rect
            draw_bounding_box_centered(outpage, weld_path["rect"], weld_color, box_size=30)
            if vector_id == 11:
                pass
            outpage.insert_text((weld_path["rect"][0], weld_path["rect"][1]), f"{vector_id}")

            if folder_name:
                outpage.insert_text((18,18), f"Overlap folder: {folder_name}", fontsize=30, color=(0, 0, 1))

            outpage.draw_circle(weld_center, 3)

        # Track drawn vectors
        drawn_vectors.add(weld_path["rect"])

        # Increment the counter for the base_weld_type
        weld_counts[base_weld_type] += 1

        detected_welds.append({
            "page_num": page_num + 1,
            "id": vector_id,
            "weld_type": base_weld_type,
            "x": weld_center[0],
            "y": weld_center[1],
            "size": None,
        })

        # Increment the vector_id for the next iteration
        vector_id += 1

    # Draw detected vectors on the same page
    if draw_pages:
        for original, intersecting, connected, color in all_detected_vectors:
            draw_vector(outpage, original, intersecting, connected, color)
            drawn_vectors.update([original["rect"], intersecting["rect"], connected["rect"]])

    # Print weld counts at the end
    print("Weld counts:", weld_counts)

    # Draw the first page for comparison, filtering out already drawn vectors
    if draw_pages:
        for index, path in enumerate(paths):
            if "items" not in path:
                continue
            if "rect" not in path or path["rect"] in drawn_vectors:
                continue
            for item in path["items"]:
                shape = outpage.new_shape()
                if item[0] == "l":  # line
                    shape.draw_line(item[1], item[2])
                elif item[0] == "re":  # rectangle
                    shape.draw_rect(item[1])
                elif item[0] == "qu":  # quad
                    shape.draw_quad(item[1])
                elif item[0] == "c":  # curve
                    shape.draw_bezier(item[1], item[2], item[3], item[4])
                shape.finish(**get_finish_params(path, fill_opacity=0))
                shape.commit()

    return weld_counts, detected_welds

def draw_all_vectors(doc, collected_data):
    outpdf = fitz.open()
    for page_num, weld_counts, detected_vectors in collected_data:
        page = doc[page_num]
        outpage = outpdf.new_page(width=page.rect.width, height=page.rect.height)
        for original, intersecting, connected, color in detected_vectors:
            draw_vector(outpage, original, intersecting, connected, color)
    outpdf.save("vectors_classified_experimental.pdf")
    outpdf.close()

def process_page_multiprocess(filename, page_num, draw_pages, debug_vectors):
    """A pickle friendly function for multiprocessing"""
    outpdf = fitz.open()
    doc = fitz.open(filename) # Potentially pass the page as bytes instead of reading whole doc?
    try:
        weld_counts, detected_welds = process_page(doc, page_num, outpdf, draw_pages, debug_vectors)
        return weld_counts, detected_welds, outpdf.tobytes()
    except Exception as e:
        return {
        "page_num": page_num + 1,
        "shop_bw": 0,
        "field_bw": 0,
        "shop_sw": 0,
        "field_sw": 0,
        "screw_conn": 0,
        "error": str(e)
    }, [], None

def process_document(job_id, filename, pages=None, debug_pages=False, debug_vector=False, multiprocess=True, save_interval=5):
    """
    pages arg examples -
            pages=None - All pages (Default)
            pages=[4,5] - Pages 3 and 4
            pages=[4] - Page 3 only
            pages=20 - First 20 pages
    """
    # Initialize list to collect weld counts for each page
    weld_counts_list = []
    detected_welds_list = []

    doc = fitz.open(filename)
    outpdf: fitz.Document = fitz.open()

    # Determine the number of pages to process
    total_pages = len(doc)

    if isinstance(pages, list):
        pass
    elif pages is None:
        pages = range(0, total_pages)
    elif pages is not None:
        pages = range(0, min(total_pages, pages))

    if multiprocess:
        # assert False, "TODO Multiprocessing not implemented"
        with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
            results = pool.starmap(process_page_multiprocess, [(filename, page_num, debug_pages, debug_vector) for page_num in pages])
            results.sort(key=lambda x: x[0]["page_num"])  # Ensure the results are in order of page numbers

        # Insert bytes page into pdf
        for weld_counts, detected_welds, pdf_bytes in results:
            weld_counts_list.append(weld_counts)
            detected_welds_list.extend(detected_welds)
            try:
                if pdf_bytes is None:
                    raise Exception("No result, so insert empty page")
                d = fitz.open(stream=pdf_bytes, filetype="pdf")
                outpdf.insert_pdf(d)
            except Exception:
                page = doc[weld_counts['page_num']]
                outpdf.new_page(width=page.rect.width, height=page.rect.height)

    else:
        # Keep older version for debugging
        for i, page_num in enumerate(pages):
            weld_counts, detected_welds = process_page(doc, page_num, outpdf, debug_pages, debug_vector)
            weld_counts_list.append(weld_counts)
            detected_welds_list.extend(weld_counts)
            # Save the PDF incrementally every save_interval pages
            # if (i + 1) % save_interval == 0:
            #     temp_filename = f"temp_vectors_classified_experimental_up_to_page_{page_num + 1}.pdf"
            #     outpdf.save(temp_filename)
            #     print(f"Up to page {page_num + 1} saved as {temp_filename}")

    if debug_pages:
        print("Outputting PDF for Debugging")
        final_filename = "Objects_detected_final.pdf"
        outpdf.save(final_filename)
        outpdf.close()
        # return outpdf # If we need visual
    doc.close()

    pub.sendMessage("set-statusbar-realtime", message="Weld Count - Complete", jobId=job_id)

    # print(f"PDF Document outputted as {final_filename}")

    # Convert weld_counts_list to a pandas DataFrame
    df_weld_counts = pd.DataFrame(weld_counts_list)
    df_detected_welds = pd.DataFrame(detected_welds_list)
    return df_weld_counts, df_detected_welds


def run(job_id,
        filename: str,
        pages=None,
        debug_pages:bool=False,
        debug_vector:bool=False,
        multiprocess:bool=True,
        save_interval=5):

    pub.sendMessage("set-statusbar-realtime", message="Weld Count - Starting", jobId=job_id)
    start_time = time.time()
    df_weld_count = process_document(job_id,
                                    filename,
                                    pages=pages,
                                    debug_pages=debug_pages,
                                    debug_vector=debug_vector,
                                    multiprocess=multiprocess,
                                    save_interval=save_interval)
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Time taken to process the document: {elapsed_time:.2f} seconds")  # Calculate the elapsed time

    return df_weld_count


if __name__ == "__main__":


    print(fitz.__doc__)  # This should print some documentation about PyMuPDF
    filename = r"/home/<USER>/Public/Drawings/Weld Client 7 - Tecnimont/Combined OCI-Tecnimont-Rotated.pdf" #"0 Binder.pdf"
    filename = r"C:\Drawings\Isometrics\joey_williams_combined_isometrics.pdf"

    job_id = 1

    multiprocess = True
    # Run the processing function for the document
    # process_document(filename)
    pages = [3,4,5,12,13,14,15,17,18,19] # Note - user target (page number - 1) # detected problem pages
    pages = [1,2,3,4,5,6,7,8,9,10] # Note - user target (page number - 1) # detected problem pages
    # pages = [5]
    # pages = [12, 15, 17, 18] # Missing ones
    # pages = [10]
    pages = 1
    pages = None
    pages = [5,6,7,8,9,10]
    pages = [1]
    pages = None
    debug_pages = False # Output complete file of all drawn vectors for visual validation
    debug_vector = False # Output each vector and vector drawing to a file for debugging

    df_weld_counts, df_detected_welds = run(job_id, filename, pages, debug_pages, debug_vector, multiprocess)
    df_weld_counts.to_excel("weld_counts.xlsx", index=False)

    df_detected_welds.to_excel("detected_welds.xlsx", index=False)
