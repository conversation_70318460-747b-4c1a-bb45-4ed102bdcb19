
import boto3
import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

OS_DOWNLOAD_PATH = str(Path.home() / "Downloads" / "AIS-UPLOAD")

SPACE_NAME = "ais-upload"
ENDPOINT_URL: str = "https://nyc3.digitaloceanspaces.com"

client = boto3.client('s3',
            region_name='nyc3',
            endpoint_url=ENDPOINT_URL,
            aws_access_key_id=os.getenv("DO_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("DO_SECRET_KEY"))


def get_folder_names(client) -> tuple[str]:
    """Download files from a space folder"""
    paginator = client.get_paginator('list_objects_v2')
    page_iterator = paginator.paginate(Bucket=SPACE_NAME, Prefix='', Delimiter='/')
    folder_names = []
    for page in page_iterator:
        if 'CommonPrefixes' in page:
            for cp in page['CommonPrefixes']:
                folder_names.append(cp['Prefix'].rstrip('/'))
    return tuple(folder_names)


def download_files_from_space(client, space_folder, save_dir):
    """Download files from a space folder"""
    paginator = client.get_paginator('list_objects_v2')
    page_iterator = paginator.paginate(Bucket=SPACE_NAME, Prefix=space_folder)
    for page in page_iterator:
        if 'Contents' in page:
            objects = page['Contents']
            # Process each object
            for obj in objects:
                key = obj['Key']
                if key.endswith('/'):
                    # It's a folder, create local folder and recursively download its content
                    folder_name = key.rstrip('/')
                    next_space_folder = os.path.join(space_folder, folder_name)
                    download_files_from_space(client, next_space_folder, save_dir)
                else:
                    # It's a file, download it
                    local_file_path = os.path.join(save_dir, key)
                    os.makedirs(os.path.dirname(local_file_path), exist_ok=True) # Create parent directory if not exists

                print(f"Downloading {key}...")
                client.download_file(SPACE_NAME, key, local_file_path)
                print(f"Downloaded {key} to {local_file_path}")

def plugin_download_drawings(folder_to_download: tuple[str]=get_folder_names(client), save_dir: str = OS_DOWNLOAD_PATH):
    """
    Go to digial ocean to generate access keys

    Set DO_ACCESS_KEY_ID and DO_SECRET_KEY in dotenv
    """
    # Initialize boto3 client
    s3 = boto3.client('s3',
                    region_name='nyc3',
                    endpoint_url=ENDPOINT_URL,
                    aws_access_key_id=DO_ACCESS_KEY_ID,
                    aws_secret_access_key=DO_SECRET_KEY)
    download_files_from_space(s3, folder_to_download, save_dir)

    return f"Finished downloading files from {folder_to_download}. Check {save_dir}"
