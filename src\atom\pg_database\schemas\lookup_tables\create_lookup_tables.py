"""
Creation of PostGreSQL (referenced as 'PG') tables and schema definitions.
"""

SCHEMA_STANDARD_FITTINGS = """
    /* SCHEMA_STANDARD_FITTINGS */
    /* Stores standardized fittings and their related calculation factors, lengths, and areas in multiple units (ft, mm). */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.standard_fittings;

    /* Create table: standard_fittings */
    CREATE TABLE standard_fittings (
        id SERIAL PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client VARCHAR(100) NOT NULL,
        profile VARCHAR(100) NOT NULL,
        lookup_category VARCHAR(100) NOT NULL,
        size1 DECIMAL(8, 3) NOT NULL,
        length_ft DECIMAL(20,12),
        length_in DECIMAL(20, 12),
        length_mm DECIMAL(20, 12),
        area_ft DECIMAL(20,12),
        area_in DECIMAL(20,12),
        area_mm DECIMAL(20, 12),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Indexes for performance optimization */
    CREATE INDEX idx_standard_fittings_category ON standard_fittings (lookup_category);
    CREATE INDEX idx_standard_fittings_client ON standard_fittings (client_id, profile);
    CREATE INDEX idx_standard_fittings_size ON standard_fittings (size1);

    /* Comments for table */
    COMMENT ON TABLE standard_fittings IS 'Stores standardized fittings and their related calculation factors, lengths, and areas in multiple units (ft, mm).';

    /* Comments for columns */
    COMMENT ON COLUMN standard_fittings.id IS 'Unique identifier for each standard fitting entry.';
    COMMENT ON COLUMN standard_fittings.client_id IS 'Identifier for the client associated with the fitting entry.';
    COMMENT ON COLUMN standard_fittings.client IS 'Name of the client.';
    COMMENT ON COLUMN standard_fittings.profile IS 'Profile identifier linking fittings to specific configuration or usage context.';
    COMMENT ON COLUMN standard_fittings.lookup_category IS 'Category type of fitting, e.g., "45", "90 Long Radius", "Tee", etc.';
    COMMENT ON COLUMN standard_fittings.size1 IS 'Nominal size of the fitting, typically represented in inches (NPS).';
    COMMENT ON COLUMN standard_fittings.length_ft IS 'Length of the fitting in feet (LF) - formerly known as Equivalent Factor (EF).';
    COMMENT ON COLUMN standard_fittings.length_in IS 'Length of the fitting in inches.';
    COMMENT ON COLUMN standard_fittings.length_mm IS 'Length of the fitting in millimeters.';
    COMMENT ON COLUMN standard_fittings.area_ft IS 'Surface area of the fitting in square feet.';
    COMMENT ON COLUMN standard_fittings.area_mm IS 'Surface area of the fitting in square millimeters.';
    COMMENT ON COLUMN standard_fittings.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN standard_fittings.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_TWO_SIZE_FITTINGS = """
    /* SCHEMA_TWO_SIZE_FITTINGS */
    DROP TABLE IF EXISTS reducers;
    CREATE TABLE reducers (
        id SERIAL PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client VARCHAR(100) NOT NULL,
        profile VARCHAR(100) NOT NULL,
        nps VARCHAR(50), /* Informational, not used for calculation */
        size1 DECIMAL(8, 3) NOT NULL,
        size2 DECIMAL(8, 3) NOT NULL,
        large_end_mm DECIMAL(10, 3),
        large_end_in DECIMAL(10, 3),
        large_end_ft DECIMAL(10, 3),
        small_end_mm DECIMAL(10, 3),
        small_end_in DECIMAL(10, 3),
        small_end_ft DECIMAL(10, 3),
        length_mm DECIMAL(20,12),
        length_in DECIMAL(20,12),
        length_ft DECIMAL(20,12),
        area_mm DECIMAL(20,12),
        area_in DECIMAL(20,12),
        area_ft DECIMAL(20,12),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* reducers table indexes */
    CREATE INDEX idx_reducers_client_profile ON reducers (client_id, profile);
    CREATE INDEX idx_reducers_sizes ON reducers (size1, size2);

    /* Comments for table and columns */
    COMMENT ON TABLE reducers IS 'Stores reducer fittings details with measurements for large and small ends, and calculated lengths and area values.';
    COMMENT ON COLUMN reducers.id IS 'Unique identifier for each reducer fitting entry.';
    COMMENT ON COLUMN reducers.client_id IS 'Identifier for the client associated with the fitting entry.';
    COMMENT ON COLUMN reducers.client IS 'Name of the client.';
    COMMENT ON COLUMN reducers.profile IS 'Profile identifier linking fittings to specific configuration or usage context.';
    COMMENT ON COLUMN reducers.nps IS 'Nominal Pipe Size description (informational only).';
    COMMENT ON COLUMN reducers.size1 IS 'Larger nominal size (NPS) for the reducer fitting.';
    COMMENT ON COLUMN reducers.size2 IS 'Smaller nominal size (NPS) for the reducer fitting.';
    COMMENT ON COLUMN reducers.large_end_mm IS 'Dimension of the larger end in millimeters.';
    COMMENT ON COLUMN reducers.large_end_in IS 'Dimension of the larger end in inches.';
    COMMENT ON COLUMN reducers.large_end_ft IS 'Dimension of the larger end in feet.';
    COMMENT ON COLUMN reducers.small_end_mm IS 'Dimension of the smaller end in millimeters.';
    COMMENT ON COLUMN reducers.small_end_in IS 'Dimension of the smaller end in inches.';
    COMMENT ON COLUMN reducers.small_end_ft IS 'Dimension of the smaller end in feet.';
    COMMENT ON COLUMN reducers.length_mm IS 'Length in millimeters for calculations (formerly ef_mm).';
    COMMENT ON COLUMN reducers.length_in IS 'Length in inches for calculations (formerly ef_in).';
    COMMENT ON COLUMN reducers.length_ft IS 'Length in feet for calculations (formerly ef_ft).';
    COMMENT ON COLUMN reducers.area_mm IS 'Surface area in square millimeters.';
    COMMENT ON COLUMN reducers.area_in IS 'Surface area in square inches.';
    COMMENT ON COLUMN reducers.area_ft IS 'Surface area in square feet.';
    COMMENT ON COLUMN reducers.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN reducers.updated_at IS 'Timestamp when the record was last updated.';

    DROP TABLE IF EXISTS elbows_reducing;
    CREATE TABLE elbows_reducing (
        id SERIAL PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client VARCHAR(100) NOT NULL,
        profile VARCHAR(100) NOT NULL,
        nps VARCHAR(50), /* Informational, not used for calculation */
        size1 DECIMAL(8, 3) NOT NULL,
        size2 DECIMAL(8, 3) NOT NULL,
        large_end_mm DECIMAL(10, 3),
        large_end_in DECIMAL(10, 3),
        large_end_ft DECIMAL(10, 3),
        small_end_mm DECIMAL(10, 3),
        small_end_in DECIMAL(10, 3),
        small_end_ft DECIMAL(10, 3),
        length_in DECIMAL(20,12),
        length_ft DECIMAL(20,12),
        length_mm DECIMAL(20,12),
        area_mm DECIMAL(20,12),
        area_in DECIMAL(20,12),
        area_ft DECIMAL(20,12),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* elbows_reducing table indexes */
    CREATE INDEX idx_elbows_reducing_client_profile ON elbows_reducing (client_id, profile);
    CREATE INDEX idx_elbows_reducing_sizes ON elbows_reducing (size1, size2);

    /* Comments for table and columns */
    COMMENT ON TABLE elbows_reducing IS 'Stores reducing elbow fittings details with measurements for large and small ends, and calculated lengths and area values.';
    COMMENT ON COLUMN elbows_reducing.id IS 'Unique identifier for each reducing elbow fitting entry.';
    COMMENT ON COLUMN elbows_reducing.client_id IS 'Identifier for the client associated with the fitting entry.';
    COMMENT ON COLUMN elbows_reducing.client IS 'Name of the client.';
    COMMENT ON COLUMN elbows_reducing.profile IS 'Profile identifier linking fittings to specific configuration or usage context.';
    COMMENT ON COLUMN elbows_reducing.nps IS 'Nominal Pipe Size description (informational only).';
    COMMENT ON COLUMN elbows_reducing.size1 IS 'Larger nominal size (NPS) for the reducing elbow fitting.';
    COMMENT ON COLUMN elbows_reducing.size2 IS 'Smaller nominal size (NPS) for the reducing elbow fitting.';
    COMMENT ON COLUMN elbows_reducing.large_end_mm IS 'Dimension of the larger end in millimeters.';
    COMMENT ON COLUMN elbows_reducing.large_end_in IS 'Dimension of the larger end in inches.';
    COMMENT ON COLUMN elbows_reducing.large_end_ft IS 'Dimension of the larger end in feet.';
    COMMENT ON COLUMN elbows_reducing.small_end_mm IS 'Dimension of the smaller end in millimeters.';
    COMMENT ON COLUMN elbows_reducing.small_end_in IS 'Dimension of the smaller end in inches.';
    COMMENT ON COLUMN elbows_reducing.small_end_ft IS 'Dimension of the smaller end in feet.';
    COMMENT ON COLUMN elbows_reducing.length_mm IS 'Length in millimeters for calculations (formerly ef_mm).';
    COMMENT ON COLUMN elbows_reducing.length_in IS 'Length in inches for calculations (formerly ef_in).';
    COMMENT ON COLUMN elbows_reducing.length_ft IS 'Length in feet for calculations (formerly ef_ft).';
    COMMENT ON COLUMN elbows_reducing.area_mm IS 'Surface area in square millimeters.';
    COMMENT ON COLUMN elbows_reducing.area_in IS 'Surface area in square inches.';
    COMMENT ON COLUMN elbows_reducing.area_ft IS 'Surface area in square feet.';
    COMMENT ON COLUMN elbows_reducing.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN elbows_reducing.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_THREE_SIZE_FITTINGS = """
    /* SCHEMA_THREE_SIZE_FITTINGS */
    DROP TABLE IF EXISTS tee_reducing;
    CREATE TABLE tee_reducing (
        id SERIAL PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client VARCHAR(100) NOT NULL,
        profile VARCHAR(100) NOT NULL,
        nps VARCHAR(50), /* Informational only */
        size1 DECIMAL(8, 3) NOT NULL,
        size2 DECIMAL(8, 3) NOT NULL,
        size3 DECIMAL(8, 3) NOT NULL,
        run_mm DECIMAL(10, 3),
        run_in DECIMAL(10, 3), 
        outlet_mm DECIMAL(10, 3),
        outlet_in DECIMAL(10, 3),
        run_c_mm DECIMAL(10, 3),
        run_c_in DECIMAL(10, 3),
        outlet_m_mm DECIMAL(10, 3),
        outlet_m_in DECIMAL(10, 3),
        length_in DECIMAL(20,12),
        length_ft DECIMAL(20,12),
        length_mm DECIMAL(20,12),
        area_mm DECIMAL(20,12),
        area_in DECIMAL(20,12),
        area_ft DECIMAL(20,12),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* tee_reducing table indexes */
    CREATE INDEX idx_tee_reducing_client_profile ON tee_reducing (client_id, profile);
    CREATE INDEX idx_tee_reducing_sizes ON tee_reducing (size1, size2, size3);

    /* Comments for table and columns */
    COMMENT ON TABLE tee_reducing IS 'Stores tee-reducing fittings with detailed measurements for run and outlet dimensions, including calculated lengths and area values.';
    COMMENT ON COLUMN tee_reducing.id IS 'Unique identifier for each tee-reducing fitting entry.';
    COMMENT ON COLUMN tee_reducing.client_id IS 'Identifier for the client associated with the fitting entry.';
    COMMENT ON COLUMN tee_reducing.client IS 'Name of the client.';
    COMMENT ON COLUMN tee_reducing.profile IS 'Profile identifier linking fittings to specific configuration or usage context.';
    COMMENT ON COLUMN tee_reducing.nps IS 'Nominal Pipe Size description (informational only).';
    COMMENT ON COLUMN tee_reducing.size1 IS 'First nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN tee_reducing.size2 IS 'Second nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN tee_reducing.size3 IS 'Third nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN tee_reducing.run_mm IS 'Main run dimension in millimeters.';
    COMMENT ON COLUMN tee_reducing.run_in IS 'Main run dimension in inches.';
    COMMENT ON COLUMN tee_reducing.outlet_mm IS 'Outlet dimension in millimeters.';
    COMMENT ON COLUMN tee_reducing.outlet_in IS 'Outlet dimension in inches.';
    COMMENT ON COLUMN tee_reducing.run_c_mm IS 'Secondary run dimension in millimeters.';
    COMMENT ON COLUMN tee_reducing.run_c_in IS 'Secondary run dimension in inches.';
    COMMENT ON COLUMN tee_reducing.outlet_m_mm IS 'Modified outlet dimension in millimeters.';
    COMMENT ON COLUMN tee_reducing.outlet_m_in IS 'Modified outlet dimension in inches.';
    COMMENT ON COLUMN tee_reducing.length_mm IS 'Length in millimeters for calculations (formerly ef_mm).';
    COMMENT ON COLUMN tee_reducing.length_in IS 'Length in inches for calculations (formerly ef_in).';
    COMMENT ON COLUMN tee_reducing.length_ft IS 'Length in feet for calculations (formerly ef_ft).';
    COMMENT ON COLUMN tee_reducing.area_mm IS 'Surface area in square millimeters.';
    COMMENT ON COLUMN tee_reducing.area_in IS 'Surface area in square inches.';
    COMMENT ON COLUMN tee_reducing.area_ft IS 'Surface area in square feet.';
    COMMENT ON COLUMN tee_reducing.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN tee_reducing.updated_at IS 'Timestamp when the record was last updated.';

    DROP TABLE IF EXISTS cross_reducing;
    CREATE TABLE cross_reducing (
        id SERIAL PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client VARCHAR(100) NOT NULL,
        profile VARCHAR(100) NOT NULL,
        nps VARCHAR(50), /* Informational only */
        size1 DECIMAL(8, 3) NOT NULL,
        size2 DECIMAL(8, 3) NOT NULL,
        size3 DECIMAL(8, 3) NOT NULL,
        run_mm DECIMAL(10, 3),
        run_in DECIMAL(10, 3),
        outlet_mm DECIMAL(10, 3),
        outlet_in DECIMAL(10, 3),
        run_c_mm DECIMAL(10, 3),
        run_c_in DECIMAL(10, 3),
        outlet_m_mm DECIMAL(10, 3),
        outlet_m_in DECIMAL(10, 3),
        length_in DECIMAL(20,12),
        length_ft DECIMAL(20,12),
        length_mm DECIMAL(20,12),
        area_mm DECIMAL(20,12),
        area_in DECIMAL(20,12),
        area_ft DECIMAL(20,12),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Comments for table and columns */
    COMMENT ON TABLE cross_reducing IS 'Stores cross-reducing fittings with detailed measurements for run and outlet dimensions, including calculated lengths and area values.';
    COMMENT ON COLUMN cross_reducing.id IS 'Unique identifier for each cross-reducing fitting entry.';
    COMMENT ON COLUMN cross_reducing.client_id IS 'Identifier for the client associated with the fitting entry.';
    COMMENT ON COLUMN cross_reducing.client IS 'Name of the client.';
    COMMENT ON COLUMN cross_reducing.profile IS 'Profile identifier linking fittings to specific configuration or usage context.';
    COMMENT ON COLUMN cross_reducing.nps IS 'Nominal Pipe Size description (informational only).';
    COMMENT ON COLUMN cross_reducing.size1 IS 'First nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN cross_reducing.size2 IS 'Second nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN cross_reducing.size3 IS 'Third nominal size (NPS) of the fitting.';
    COMMENT ON COLUMN cross_reducing.run_mm IS 'Main run dimension in millimeters.';
    COMMENT ON COLUMN cross_reducing.run_in IS 'Main run dimension in inches.';
    COMMENT ON COLUMN cross_reducing.outlet_mm IS 'Outlet dimension in millimeters.';
    COMMENT ON COLUMN cross_reducing.outlet_in IS 'Outlet dimension in inches.';
    COMMENT ON COLUMN cross_reducing.run_c_mm IS 'Secondary run dimension in millimeters.';
    COMMENT ON COLUMN cross_reducing.run_c_in IS 'Secondary run dimension in inches.';
    COMMENT ON COLUMN cross_reducing.outlet_m_mm IS 'Modified outlet dimension in millimeters.';
    COMMENT ON COLUMN cross_reducing.outlet_m_in IS 'Modified outlet dimension in inches.';
    COMMENT ON COLUMN cross_reducing.length_mm IS 'Length in millimeters for calculations (formerly ef_mm).';
    COMMENT ON COLUMN cross_reducing.length_in IS 'Length in inches for calculations (formerly ef_in).';
    COMMENT ON COLUMN cross_reducing.length_ft IS 'Length in feet for calculations (formerly ef_ft).';
    COMMENT ON COLUMN cross_reducing.area_mm IS 'Surface area in square millimeters.';
    COMMENT ON COLUMN cross_reducing.area_in IS 'Surface area in square inches.';
    COMMENT ON COLUMN cross_reducing.area_ft IS 'Surface area in square feet.';
    COMMENT ON COLUMN cross_reducing.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN cross_reducing.updated_at IS 'Timestamp when the record was last updated.';

    /* cross_reducing table indexes */
    CREATE INDEX idx_cross_reducing_client_profile ON cross_reducing (client_id, profile);
    CREATE INDEX idx_cross_reducing_sizes ON cross_reducing (size1, size2, size3);

"""

SCHEMA_EQ_LENGTH_FACTORS = """
    /* SCHEMA_EQ_LENGTH_FACTORS */
    /* Stores equivalent length factors for fittings. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_equiv_length_factors;

    /* Create table: public.atem_equiv_length_factors */
    CREATE TABLE public.atem_equiv_length_factors (
        id SERIAL PRIMARY KEY,
        profile_id INTEGER REFERENCES public.atem_client_profiles(id),
        component_type VARCHAR(50) NOT NULL, -- e.g., Pipe, Elbow, Tee
        eq_length_factor DECIMAL(10, 3) NOT NULL, -- Length Multiplier factor
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(profile_id, component_type)
    );

    /* Indexes for performance optimization */
    CREATE INDEX idx_equiv_length_factors_profile ON atem_equiv_length_factors(profile_id);
    CREATE INDEX idx_equiv_length_factors_component ON atem_equiv_length_factors(component_type);

    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_equiv_length_factors IS 'Stores equivalent length factors for fittings.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.id IS 'Unique identifier for each equivalent length factor.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.profile_id IS 'Identifier for the client profile associated with the equivalent length factor.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.component_type IS 'Type of component (e.g., Pipe, Elbow, Tee).';
    COMMENT ON COLUMN public.atem_equiv_length_factors.eq_length_factor IS 'Equivalent length factor for the component type.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.notes IS 'Additional notes or comments.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_equiv_length_factors.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_FLANGE_DATA = """
    -- NOT JOINED IN LOOKUP VIEW
    CREATE TABLE IF NOT EXISTS public.flange_data (
        id SERIAL PRIMARY KEY,
        profile_id INTEGER REFERENCES public.atem_client_profiles(id),
        client VARCHAR(100), 
        size_in DECIMAL(8, 3) NOT NULL,
        size_mm DECIMAL(8, 3),
        lbs INTEGER NOT NULL,
        o_in DECIMAL(10, 3),
        c_in DECIMAL(10, 4),
        o_mm DECIMAL(10, 3),
        c_mm DECIMAL(10, 4),
        bolt_circle_in DECIMAL(10, 4),
        bolt_circle_mm DECIMAL(10, 4),
        no_and_hole_size_desc_in VARCHAR(50),
        no_holes INTEGER,
        hole_size_in DECIMAL(10, 4),
        hole_size_mm DECIMAL(10, 4),
        
        /* Measurement values by flange type */
        weld_neck_length_in DECIMAL(20, 12),
        weld_neck_length_ft DECIMAL(20, 12),
        weld_neck_length_mm DECIMAL(20, 12),
        slip_on_thrd_length_in DECIMAL(20, 12),
        slip_on_thrd_length_ft DECIMAL(20, 12),
        slip_on_thrd_length_mm DECIMAL(20, 12),
        lap_joint_length_in DECIMAL(20, 12),
        lap_joint_length_ft DECIMAL(20, 12),
        lap_joint_length_mm DECIMAL(20, 12),
        
        /* Area values by flange type */
        weld_neck_area_in DECIMAL(20, 12),
        weld_neck_area_ft DECIMAL(20, 12),
        weld_neck_area_mm DECIMAL(20, 12),
        slip_on_thrd_area_in DECIMAL(20, 12),
        slip_on_thrd_area_ft DECIMAL(20, 12),
        slip_on_thrd_area_mm DECIMAL(20, 12),
        lap_joint_area_in DECIMAL(20, 12),
        lap_joint_area_ft DECIMAL(20, 12),
        lap_joint_area_mm DECIMAL(20, 12),
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Indexes for performance optimization */
    CREATE INDEX idx_flange_profile ON public.flange_data(profile_id);
    CREATE INDEX idx_flange_size_rating ON public.flange_data(size_in, lbs);

    /* Comments for table */
    COMMENT ON TABLE public.flange_data IS 'Stores flange standards with measurements, lengths, and areas for different flange types (weld neck, slip-on/threaded, lap joint) by size and pressure rating.';

"""

SCHEMA_VIEW_LOOKUP_TABLE = """
   CREATE OR REPLACE VIEW vw_fittings_lookup AS

    /* Standard fittings - single size */
    SELECT 
        id,
        client_id,
        client,
        profile,
        lookup_category::VARCHAR,
        size1,
        NULL::DECIMAL(8,3) AS size2,
        NULL::DECIMAL(8,3) AS size3,
        length_ft::DECIMAL(20,12),
        length_in::DECIMAL(20,12),
        length_mm::DECIMAL(20,12),
        area_ft::DECIMAL(20,12),
        area_in::DECIMAL(20,12), 
        area_mm::DECIMAL(20,12),
        NULL::DECIMAL(12,4) AS weight,
        created_at,
        updated_at
    FROM standard_fittings

    UNION ALL

    /* Reducers - two sizes */
    SELECT 
        id,
        client_id,
        client,
        profile,
        'Reducer'::VARCHAR AS lookup_category,
        size1,
        size2,
        NULL::DECIMAL(8,3) AS size3,
        length_ft::DECIMAL(20,12),
        length_in::DECIMAL(20,12),
        length_mm::DECIMAL(20,12),
        area_ft::DECIMAL(20,12),
        area_in::DECIMAL(20,12),
        area_mm::DECIMAL(20,12),
        NULL::DECIMAL(12,4) AS weight,
        created_at,
        updated_at
    FROM reducers

    UNION ALL

    /* Reducing elbows - two sizes */
    SELECT 
        id,
        client_id,
        client,
        profile,
        'Red. 90'::VARCHAR AS lookup_category,
        size1,
        size2,
        NULL::DECIMAL(8,3) AS size3,
        length_ft::DECIMAL(20,12),
        length_in::DECIMAL(20,12),
        length_mm::DECIMAL(20,12),
        area_ft::DECIMAL(20,12),
        area_in::DECIMAL(20,12),
        area_mm::DECIMAL(20,12),
        NULL::DECIMAL(12,4) AS weight,
        created_at,
        updated_at
    FROM elbows_reducing

    UNION ALL

    /* Reducing tees - three sizes */
    SELECT 
        id,
        client_id,
        client,
        profile,
        'Red. Tee'::VARCHAR AS lookup_category,
        size1,
        size2,
        size3,
        length_ft::DECIMAL(20,12),
        length_in::DECIMAL(20,12),
        length_mm::DECIMAL(20,12),
        area_ft::DECIMAL(20,12),
        area_in::DECIMAL(20,12),
        area_mm::DECIMAL(20,12),
        NULL::DECIMAL(12,4) AS weight,
        created_at,
        updated_at
    FROM tee_reducing

    UNION ALL

    /* Reducing crosses - three sizes */
    SELECT 
        id,
        client_id,
        client,
        profile,
        'Red. Cross'::VARCHAR AS lookup_category,
        size1,
        size2,
        size3,
        length_ft::DECIMAL(20,12),
        length_in::DECIMAL(20,12),
        length_mm::DECIMAL(20,12),
        area_ft::DECIMAL(20,12),
        area_in::DECIMAL(20,12),
        area_mm::DECIMAL(20,12),
        NULL::DECIMAL(12,4) AS weight,
        created_at,
        updated_at
    FROM cross_reducing;

    /* Add a comment to the view */
    COMMENT ON VIEW vw_fittings_lookup IS 'Unified view of all fitting types with standardized lookup categories and length measurements (replacing former EF values) to simplify application integration';

"""

