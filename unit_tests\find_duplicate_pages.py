import fitz
import hashlib
from typing import Dict, List
from collections import defaultdict
import os


def get_page_checksum(page) -> str:
    """
    Generates a checksum for a page based on its visual content.
    
    Args:
        page: fitz.Page object
        
    Returns:
        str: Checksum of the page content
    """
    # Get the page's pixmap (visual content)
    pix = page.get_pixmap()
    
    # Get the raw image data
    img_data = pix.samples
    
    # Generate checksum from the image data
    return hashlib.sha256(img_data).hexdigest()

def find_identical_pages(pdf_path: str) -> Dict[str, List[int]]:
    """
    Identifies groups of identical pages based on visual content.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        Dict[str, List[int]]: Dictionary mapping checksums to lists of page numbers
    """
    try:
        # Open the PDF document
        doc = fitz.open(pdf_path)
        
        # Dictionary to store checksum -> page numbers mapping
        checksum_pages: Dict[str, List[int]] = defaultdict(list)
        
        # Process each page
        print("Analyzing pages...")
        for page_num in range(len(doc)):
            # Get the page
            page = doc[page_num]
            
            # Generate checksum for the page
            checksum = get_page_checksum(page)
            
            # Store page number with its checksum
            checksum_pages[checksum].append(page_num)
        
        # Filter out unique pages (those without duplicates)
        duplicate_pages = {
            checksum: pages 
            for checksum, pages in checksum_pages.items() 
            if len(pages) > 1
        }
        
        return duplicate_pages
        
    except Exception as e:
        print(f"Error processing PDF: {str(e)}")
        return {}
    finally:
        if 'doc' in locals():
            doc.close()

def print_duplicate_pages(pdf_path: str):
    """
    Prints a human-readable report of duplicate pages in the PDF.
    
    Args:
        pdf_path (str): Path to the PDF file
    """
    duplicates = find_identical_pages(pdf_path)
    
    if not duplicates:
        print("No duplicate pages found.")
        return
    
    print("\nDuplicate page groups found:")
    for i, (checksum, pages) in enumerate(duplicates.items(), 1):
        pages_str = ', '.join(str(p + 1) for p in sorted(pages))  # Convert to 1-based
        print(f"Group {i}: Pages {pages_str} are identical")

def get_first_pages_list(pdf_path: str) -> List[int]:
    """
    Creates a list of the first page from each group of identical pages.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        List[int]: List of first page numbers from each group (0-based indexing)
    """
    duplicates = find_identical_pages(pdf_path)
    first_pages = [sorted(pages)[0] for pages in duplicates.values()]
    first_pages.sort()  # Sort the list for consistency
    return first_pages

def save_group_pdfs(pdf_path: str, output_dir: str = "page_groups"):
    """
    Saves each group of identical pages as a separate PDF file.
    
    Args:
        pdf_path (str): Path to the PDF file
        output_dir (str): Directory to save the group PDFs
    """
    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Get duplicate page groups
        duplicates = find_identical_pages(pdf_path)
        
        # Open source PDF
        src_doc = fitz.open(pdf_path)
        
        # Process each group
        for i, (checksum, pages) in enumerate(duplicates.items(), 1):
            # Create new PDF for this group
            output_path = os.path.join(output_dir, f"group_{i}.pdf")
            new_doc = fitz.open()
            
            # Add all pages from this group
            for page_num in sorted(pages):
                new_doc.insert_pdf(src_doc, from_page=page_num, to_page=page_num)
            
            # Save the group PDF
            new_doc.save(output_path)
            new_doc.close()
            
            print(f"Saved group {i} to {output_path}")
            
    except Exception as e:
        print(f"Error saving group PDFs: {str(e)}")
    finally:
        if 'src_doc' in locals():
            src_doc.close()


# Example usage
if __name__ == "__main__": 
    save_groups = True # Output each duplicated group to its own  file

    

    pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 027\Binder - All ISOs (Except Archives).pdf"

    # Print duplicate page groups
    print_duplicate_pages(pdf_path)
    

    # Get and print list of first pages
    first_pages = get_first_pages_list(pdf_path)
    print("\nList of first pages from each group (0-based indexing):")
    print(first_pages)
    print("\nList of first pages from each group (1-based indexing for human reading):")
    print([page + 1 for page in first_pages])
    

    # Save group PDFs
    if save_groups:
        save_group_pdfs(pdf_path)