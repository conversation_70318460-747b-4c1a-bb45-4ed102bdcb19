-- Fixed organization_members table
CREATE TABLE organization_members (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES atem_clients(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    client_domain VARCHAR(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    phone VA<PERSON>HAR(50),
    title VA<PERSON><PERSON><PERSON>(100),
    member_role VARCHAR(50) DEFAULT 'member',
    is_active BOOLEAN DEFAULT true,
    firebase_uid VARCHAR(128) UNIQUE,
    firebase_provider VARCHAR(50),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255)
);

-- Indexes for organization_members
CREATE INDEX idx_org_members_email ON organization_members(email);
CREATE INDEX idx_org_members_client_id ON organization_members(client_id);
CREATE INDEX idx_org_members_domain ON organization_members(client_domain);
CREATE INDEX idx_org_members_firebase_uid ON organization_members(firebase_uid);

-- Add table comments
COMMENT ON TABLE organization_members IS 'Stores individual users within client organizations, linked to Firebase authentication for secure access control and company auto-detection via email domains';

-- Add column comments for organization_members
COMMENT ON COLUMN organization_members.client_id IS 'Links user to their company in atem_clients table';
COMMENT ON COLUMN organization_members.email IS 'User email address, used for Firebase auth and domain-based company matching';
COMMENT ON COLUMN organization_members.client_domain IS 'Email domain extracted for automatic company assignment (e.g., company.com)';
COMMENT ON COLUMN organization_members.first_name IS 'User first name from registration or Firebase profile';
COMMENT ON COLUMN organization_members.last_name IS 'User last name from registration or Firebase profile';
COMMENT ON COLUMN organization_members.phone IS 'User contact phone number';
COMMENT ON COLUMN organization_members.title IS 'Job title or position within the organization';
COMMENT ON COLUMN organization_members.member_role IS 'Access level within organization: admin, super_admin, member, viewer';
COMMENT ON COLUMN organization_members.is_active IS 'Flag to enable/disable user access without deletion';
COMMENT ON COLUMN organization_members.firebase_uid IS 'Unique Firebase user identifier for authentication linking';
COMMENT ON COLUMN organization_members.firebase_provider IS 'Authentication method used: google, email, microsoft, etc.';
COMMENT ON COLUMN organization_members.last_login IS 'Timestamp of user last successful login for activity tracking';


-- Fixed client_submissions table
CREATE TABLE client_submissions (
    id SERIAL PRIMARY KEY,
    client_project_name VARCHAR(255) NOT NULL,
    client_project_id VARCHAR(255),
    project_description TEXT,
    client_id INTEGER REFERENCES atem_clients(id),
    submitted_by INTEGER REFERENCES organization_members(id),
    project_scope VARCHAR(255),
    engineering_company VARCHAR(255),
    drawings_count INTEGER DEFAULT 0,
    project_deadline TIMESTAMP,
    submission_status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);


-- Indexes for client_submissions
CREATE INDEX idx_client_submissions_client_id ON client_submissions(client_id);
CREATE INDEX idx_client_submissions_submitted_by ON client_submissions(submitted_by);
CREATE INDEX idx_client_submissions_status ON client_submissions(submission_status);


-- Add table comment
COMMENT ON TABLE client_submissions IS 'Tracks client project submissions before they become official ATEM projects, managing the intake workflow from initial submission to project acceptance';

-- Add column comments for client_submissions
COMMENT ON COLUMN client_submissions.client_project_name IS 'Project name as provided by the client during submission';
COMMENT ON COLUMN client_submissions.client_project_id IS 'Client internal project reference number or identifier';
COMMENT ON COLUMN client_submissions.project_description IS 'Detailed description of project scope and requirements from client';
COMMENT ON COLUMN client_submissions.client_id IS 'Links submission to the client company in atem_clients';
COMMENT ON COLUMN client_submissions.submitted_by IS 'Organization member who submitted this project';
COMMENT ON COLUMN client_submissions.project_scope IS 'Comma-separated list of work types: Pipe, Insulation, Coatings/Paint';
COMMENT ON COLUMN client_submissions.engineering_company IS 'Third-party engineering firm involved in the project';
COMMENT ON COLUMN client_submissions.drawings_count IS 'Number of drawings/documents uploaded with submission';
COMMENT ON COLUMN client_submissions.project_deadline IS 'Client requested completion deadline';
COMMENT ON COLUMN client_submissions.submission_status IS 'Workflow status: pending, received, accepted, in_progress, consolidating, complete';
COMMENT ON COLUMN client_submissions.priority IS 'Project priority level: low, medium, high, urgent';


-- Fixed submission_comments table
CREATE TABLE submission_comments (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    comment_type VARCHAR(50) DEFAULT 'general',
    submission_id INTEGER REFERENCES client_submissions(id),
    project_id INTEGER REFERENCES atem_projects(id),
    author_id INTEGER REFERENCES organization_members(id),
    author_name VARCHAR(255),
    author_type VARCHAR(50) DEFAULT 'client',
    is_internal BOOLEAN DEFAULT false,
    parent_comment_id INTEGER REFERENCES submission_comments(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);

-- Add table comment
COMMENT ON TABLE submission_comments IS 'Stores comments and notes on project submissions, supporting both client communication and internal AIS team discussions with threading capability';

-- Add column comments for submission_comments
COMMENT ON COLUMN submission_comments.content IS 'The actual comment text or message content';
COMMENT ON COLUMN submission_comments.comment_type IS 'Category of comment: general, feedback, status_update, question, etc.';
COMMENT ON COLUMN submission_comments.submission_id IS 'Links comment to a client submission (pre-project stage)';
COMMENT ON COLUMN submission_comments.project_id IS 'Links comment to an official ATEM project (post-acceptance)';
COMMENT ON COLUMN submission_comments.author_id IS 'Organization member who wrote the comment (null for AIS internal staff)';
COMMENT ON COLUMN submission_comments.author_name IS 'Display name for comment author (AIS staff names or client names)';
COMMENT ON COLUMN submission_comments.author_type IS 'Type of author: client, ais_internal, anonymous';
COMMENT ON COLUMN submission_comments.is_internal IS 'True for AIS-only comments not visible to clients';
COMMENT ON COLUMN submission_comments.parent_comment_id IS 'References parent comment for threaded/nested discussions';

-- Indexes for submission_comments
CREATE INDEX idx_submission_comments_submission_id ON submission_comments(submission_id);
CREATE INDEX idx_submission_comments_project_id ON submission_comments(project_id);
CREATE INDEX idx_submission_comments_author_id ON submission_comments(author_id);

-- Fixed file_uploads table
CREATE TABLE file_uploads (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    storage_path VARCHAR(512) UNIQUE NOT NULL,
    content_type VARCHAR(128),
    size_bytes BIGINT,
    file_category VARCHAR(100),
    submission_id INTEGER REFERENCES client_submissions(id),
    project_id INTEGER DEFAULT 0,
    uploaded_by INTEGER REFERENCES organization_members(id),
    is_processed BOOLEAN DEFAULT false,
    processing_status VARCHAR(50) DEFAULT 'pending',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Add table comment
COMMENT ON TABLE file_uploads IS 'Manages document uploads stored in Digital Ocean Spaces, tracking files from initial client submission through processing workflow';

-- Add column comments for file_uploads
COMMENT ON COLUMN file_uploads.filename IS 'Original filename as uploaded by the client';
COMMENT ON COLUMN file_uploads.storage_path IS 'Full path/URL to file in Digital Ocean Spaces storage';
COMMENT ON COLUMN file_uploads.content_type IS 'MIME type of the uploaded file (e.g., application/pdf, image/png)';
COMMENT ON COLUMN file_uploads.size_bytes IS 'File size in bytes for storage tracking and validation';
COMMENT ON COLUMN file_uploads.file_category IS 'Type of document: drawing, specification, bom, report, other';
COMMENT ON COLUMN file_uploads.submission_id IS 'Links file to the client submission it belongs to';
COMMENT ON COLUMN file_uploads.project_id IS 'Set to 0 initially, updated to atem_projects.id when submission becomes official project';
COMMENT ON COLUMN file_uploads.uploaded_by IS 'Organization member who uploaded the file';
COMMENT ON COLUMN file_uploads.is_processed IS 'Flag indicating if file has been processed by AIS team';
COMMENT ON COLUMN file_uploads.processing_status IS 'Current processing state: pending, processing, completed, failed';
COMMENT ON COLUMN file_uploads.processed_at IS 'Timestamp when file processing was completed';

-- Indexes for file_uploads
CREATE INDEX idx_file_uploads_submission_id ON file_uploads(submission_id);
CREATE INDEX idx_file_uploads_uploaded_by ON file_uploads(uploaded_by);
CREATE INDEX idx_file_uploads_processing_status ON file_uploads(processing_status);