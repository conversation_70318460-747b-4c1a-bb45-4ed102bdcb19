import re

def parse_elevation(elevation_str, convert_mm_to_feet=False, convert_m_to_feet=True) -> float:
    # Check if the string contains feet (') or inches (") symbols
    if "'" in elevation_str or '"' in elevation_str or '-' in elevation_str:  # Added check for hyphen
        # Handle standard format elevations
        # Check if the string does not start with "E" or "EL" (case-insensitive)
        if not re.match(r'^[EeZzFf][Ll]\.?\s*', elevation_str):  # Added \.?\s* to match "EL." with optional space
            return float('nan')

        try:
            # Remove 'E', 'EL', or 'EL.' prefix, any trailing double quotes
            elevation_str = re.sub(r'[EeZzFf][Ll]\.?\s*|\s*\"', '', elevation_str).strip()
            sign = -1 if elevation_str.startswith('-') else 1
            elevation_str = elevation_str.lstrip('+-')

            # Handle formats with and without foot symbol
            if "'" in elevation_str:
                parts = elevation_str.split("'")
                feet = int(parts[0]) if parts[0] else 0
                inch_part = parts[1].strip(' -"') if len(parts) > 1 else '0'
            else:
                # Handle format like "8-7 1/2" (8 feet 7.5 inches)
                parts = elevation_str.split('-')
                feet = int(parts[0]) if parts[0] else 0
                inch_part = parts[1].strip(' -"') if len(parts) > 1 else '0'

            inches = 0
            fraction = 0

            # Parse inches and fractions if present
            if ' ' in inch_part:  # Format: "7 1/2"
                whole_inches, fraction_str = inch_part.split()
                whole_inches = whole_inches.replace(".", "")
                inches = int(whole_inches)
                if '/' in fraction_str:
                    num, denom = map(int, fraction_str.split('/'))
                    fraction = num / denom
            else:  # Format: "7" or "1/2"
                if '/' in inch_part:  # Just a fraction
                    inch_part_split = inch_part.split('/')
                    num, denom = inch_part_split
                    if "." in num: # Format: "4.3/16"
                        inches, num = map(int, num.split("."))
                    else:
                        num = int(num)
                    denom = int(denom)
                    fraction = num / denom
                else:  # Just whole inches
                    if inch_part:  # Check if inch_part is not empty
                        inches = int(inch_part)
                    # If inch_part is empty, inches remains 0

            # Calculate total inches and convert to decimal feet
            total_inches = feet * 12 + inches + fraction
            decimal_feet = sign * total_inches / 12
            return decimal_feet

        except Exception as e:
            return float('nan')
    else:
        try:
            # Remove 'E', 'EL', or 'Z' prefix and any trailing whitespace
            elevation_str = re.sub(r'[EeZz][Ll]?', '', elevation_str).strip()

            # Add check for 'nan'
            if elevation_str.lower() == 'nan':
                return float('nan')

            # Convert the elevation to millimeters
            # millimeters = int(elevation_str)

            # Convert the elevation to millimeters
            value  = float(elevation_str)  # <-- Changed to float()

            print(f"Parsed numeric value: {value}")

            if convert_mm_to_feet:
                # Convert millimeters to feet
                decimal_feet = value / 304.8
                return decimal_feet

            elif convert_m_to_feet:
                # Convert millimeters to feet
                decimal_feet = value * 0.328084
                # print(f"Converting from m to feet: {value} m = {decimal_feet} feet")
                return decimal_feet
            else:
                # print(f"No conversion performed, returning original value: {value}")
                # Return the value in millimeters
                return value

        except Exception as e:
            return float('nan')  # Return NaN if there's an error