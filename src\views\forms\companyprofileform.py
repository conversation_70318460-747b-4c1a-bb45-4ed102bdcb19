from PySide6.QtWidgets import QLabel, QPushButton, QLineEdit
from PySide6.QtWidgets import QSizePolicy
from PySide6.QtCore import Qt
from .baseform import BaseForm
from src.pyside_util import get_resource_qicon

EMAIL_DEFAULT_MESSAGE = "Email *"
PASSWORD_DEFAULT_MESSAGE = "Password *"

EMAIL_LOGIN_REQUIRED = "Error: login required"
PASSWORD_REQUIRED = "Error: password required"


class CompanyProfileForm(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)

    def initUi(self):
        self.formSize.setHeight(360)

        self.title.setText("Company Profile")
        self.subtitle.setTextFormat(Qt.TextFormat.RichText)
        self.subtitle.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)
        self.subtitle.setText("Enter your profile details below")

        self.addVSpace()

        self.layout().addRow(QLabel("Company name"))
        self.companyName = QLineEdit()
        self.companyName.addAction(get_resource_qicon("users.svg"), QLineEdit.LeadingPosition)
        self.companyName.setEchoMode(QLineEdit.EchoMode.Normal)
        self.layout().addRow(self.companyName)

        self.addVSpace()

        self.layout().addRow(QLabel("Company address"))
        self.companyAddress = QLineEdit()
        self.companyAddress.setEchoMode(QLineEdit.EchoMode.Normal)
        self.companyAddress.addAction(get_resource_qicon("map-pin.svg"), QLineEdit.LeadingPosition)
        self.layout().addRow(self.companyAddress)

        self.addStretchSpacer()
        pbNext = QPushButton("Next")
        pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        pbNext.setMinimumHeight(48)
        pbNext.clicked.connect(self.onNext)
        self.layout().addRow(pbNext)

        pbNext.setContentsMargins(0, 132, 0, 32)

        self.companyName.returnPressed.connect(self.companyAddress.setFocus)
        self.companyAddress.returnPressed.connect(pbNext.setFocus)

        self.setButtonActivateOnEnter(pbNext, callback=self.onNext)

        self.setFloatingButtonBack()

    def initDefaults(self):
        pass

    def onNext(self, event=None):
        ok = True
        if ok:
            self.sgnSwitchTo.emit("UserAgreementForm")

    def getData(self):
        return {
            "companyName": self.companyName.text(),
            "companyAddress": self.companyAddress.text(),
        }

    def showEvent(self, event) -> None:
        self.companyName.setFocus()
        return super().showEvent(event)

    def onFloatingButton(self):
        self.sgnSwitchTo.emit("CreateAccountForm")