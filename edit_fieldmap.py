import json, re
import openpyxl

'''
This file is simply to assist in modifying the fieldMap.json file
'''

 # Define the order of columns
columns_order = [
    "rfq_scope",
    "general_category",
    "unit_of_measure",
    "material",
    "abbreviated_material",
    "ansme_ansi",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    "forging",
    "ends",   
    "item_tag",
    "tie_point",
    "pipe_category",
    "valve_type",
    "fitting_category",
    "weld_category",
    "bolt_category",
    "gasket_category",
    "ef",
    "sf"
    ]


def rfq_to_excel():
    # Load the JSON file
    with open("C:\\Users\\<USER>\\source\\repos\\Architekt ATOM\\src\\data\\fieldmap.json", "r") as file:
        data = json.load(file)

    # Get the "rfq_fields" items
    rfq_fields = data["rfq_fields"]

    # Create a new workbook and select the active sheet
    workbook = openpyxl.Workbook()
    sheet = workbook.active

    # Iterate over each key in "rfq_fields"
    for col, (key, item) in enumerate(rfq_fields.items(), start=1):
        # Write the key name in row 1
        sheet.cell(row=1, column=col, value=key)
    
        # Write the display name in row 2
        sheet.cell(row=2, column=col, value=item["display"])
    
        # Write the options starting from row 3
        if "options" in item:
            for row, option in enumerate(item["options"], start=3):
                sheet.cell(row=row, column=col, value=option)

    # Save the workbook
    workbook.save("rfq_fields.xlsx")
    
def natural_sort_key(s):
    """Create a list of integer and text chunks extracted from the input string, ensuring all inputs are strings."""
    s = str(s)  # Ensure the input is treated as a string
    s = s.replace("\u00a0", "")
    # This regex pattern will split the string into numbers and non-number chunks
    return [int(text) if text.isdigit() else text.lower() for text in re.split('([0-9]+)', s)]

def write_edited_xlsx_to_fieldMap():

    # Load the Excel workbook
    workbook = openpyxl.load_workbook(r"C:\Users\<USER>\Downloads\Telegram Desktop\rfq_fields-TylerEDIT.xlsx")
    # Select the "Modified" sheet
    sheet = workbook["Modified"]

    # Load the original JSON file
    with open("C:\\Users\\<USER>\\source\\repos\\Architekt ATOM\\src\\data\\fieldmap.json", "r") as file:
        data = json.load(file)

    # Get the "rfq_fields" items
    rfq_fields = data["rfq_fields"]
    
    # Create a new ordered dictionary
    ordered_rfq_fields = {}

    # Iterate over each column in the sheet, assuming the structure is the same as when created
    for col in range(1, sheet.max_column + 1):
        key = sheet.cell(row=1, column=col).value
        if key in rfq_fields:  # Check if key exists in the original JSON to prevent overwriting unrelated data
            rfq_fields[key]["display"] = sheet.cell(row=2, column=col).value
            rfq_fields[key]["default"] = sheet.cell(row=2, column=col).value
        
            # Collect options starting from row 3, sorting them naturally
            options = []
            for row in range(3, sheet.max_row + 1):
                option = sheet.cell(row=row, column=col).value
                if option is not None:  # Ensure no None values are added
                    clean_option = str(option).strip().replace("\u00a0", "")
                    options.append(clean_option)
            options.sort(key=natural_sort_key)  # Sort the options naturally
            rfq_fields[key]["options"] = options
            
     # Insert items into the new dictionary in the specified order
    for column in columns_order:
        if column in rfq_fields:
            ordered_rfq_fields[column] = rfq_fields[column]

    # Replace the old rfq_fields with the ordered version
    data["rfq_fields"] = ordered_rfq_fields

    # Save the updated JSON file
    with open("C:\\Users\\<USER>\\source\\repos\\Architekt ATOM\\src\\data\\fieldmap_updated.json", "w") as file:
        json.dump(data, file, indent=4)

def map_existing_fitttings():

    # New fitting categories
    new_fitting_categories = [
        "45 Elbow", "45 SR Elbow", "90 LR Elbow", "90 LR Reducing Elbow", "90 SR Elbow", "180 Red Return LR", 
        "180 Return LR", "180 Return SR", "Basket Strainer", "Bellmouth", "Bellow", "Bellow Reducing", 
        "Blind Flange", "Blind Flange - Drill And Tap", "Blind Flange - Drill Only", "Bucket Strainer", 
        "Bulkhead Fitting", "Bushing", "Bushing Reducing", "Cap", "Compression Fitting", "Conical Strainer", 
        "Coupling", "Coupling (Half)", "Coupling (Half) Reducing", "Coupling Reducing", "Cross", "Cross Reducing", 
        "Elbolet BW", "Elbolet PE", "Elbolet THRD", "Equal Tee", "Ferrule", "Figure 8", "Flange Other", "Flangeolet", 
        "Flatolet", "Hammer Blind", "Hex Plug", "Hose Coupling", "Lateral", "Lateral Reducing", "Latrolet BW", 
        "Latrolet PE", "Latrolet THRD", "LJ Flange FF", "LJ Flange Long FF", "LJ Flange Long RF", "LJ Flange Orifice FF", 
        "LJ Flange Orifice RF", "LJ Flange Reducing FF", "LJ Flange Reducing RF", "LJ Flange RF", "LJ Flange Ring Joint FF", 
        "LJ Flange Ring Joint RF", "Nipolet BW", "Nipolet PE", "Nipolet THRD", "Nipple", "Orifice Plate", "Other Fitting", 
        "Other Fitting Reducing", "Other Strainer", "Paddle Spacer", "Pipe Bend", "Plate Flange", "Puddle Flange", 
        "Reducer Bushing", "Reducer Concentric", "Reducer Eccentric", "Reducer Insert", "Reducing Tee", "Round Plug", 
        "SO Flange FF", "SO Flange Long FF", "SO Flange Long RF", "SO Flange Orifice FF", "SO Flange Orifice RF", 
        "SO Flange Reducing FF", "SO Flange Reducing RF", "SO Flange RF", "SO Flange Ring Joint FF", "SO Flange Ring Joint RF", 
        "Sockolet", "Spacer", "Spade Blind", "Spectacle Blind", "Square Plug", "Stub End BW", "Stub End Flgd", "Stub End PE", 
        "Stub End SW", "Stub End Te", "SW Flange FF", "SW Flange Long FF", "SW Flange Long RF", "SW Flange Orifice FF", 
        "SW Flange Orifice RF", "SW Flange Reducing FF", "SW Flange Reducing RF", "SW Flange RF", "SW Flange Ring Joint FF", 
        "SW Flange Ring Joint RF", "Swage Con. Reducer", "Swage Ecc. Reducer", "Swage Nipple", "Sweepolet BW", "Sweepolet PE", 
        "Sweepolet THRD", "Swivel Joint", "T-Strainer", "T-Strainer Reducing", "Tee", "Tee Reducing", "THRD Flange FF", 
        "THRD Flange Long FF", "THRD Flange Long RF", "THRD Flange Orifice FF", "THRD Flange Orifice RF", "THRD Flange Reducing FF", 
        "THRD Flange Reducing RF", "THRD Flange RF", "THRD Flange Ring Joint FF", "THRD Flange Ring Joint RF", "Threadolet", 
        "Union", "Weldolet", "WN Flange FF", "WN Flange Long FF", "WN Flange Long RF", "WN Flange Orifice FF", "WN Flange Orifice RF", 
        "WN Flange Reducing FF", "WN Flange Reducing RF", "WN Flange RF", "WN Flange Ring Joint FF", "WN Flange Ring Joint RF", 
        "Wye (Compact)", "Wye (Compact) Reducing", "Wye (Standard)", "Wye (Standard) Reducing", "Y-Strainer", "Y-Strainer Reducing"
    ]

    # Existing mapping dictionary (abbreviated for clarity)
    fitting_to_category = {
        '180 RETURN LR': 'Bend',
        '180 RETURN SR': 'Bend',
        '45 ELBOW': '45',
        '45 SR ELBOW': '45',
        '90 LR ELBOW': '90 Long Radius',
        '90 LR RED ELBOW': '90 Long Radius',
        '90 SR ELBOW': '90 Short Radius',
        # Add the rest of your existing mappings here
    }

    # Standardizing and mapping
    mapped_categories = {}
    unmapped_items = []

    for item in new_fitting_categories:
        standardized_item = item.upper()
        if standardized_item in fitting_to_category:
            mapped_categories[item] = fitting_to_category[standardized_item]
        else:
            unmapped_items.append(item)

    print("Mapped Categories:")
    for k, v in mapped_categories.items():
        print(f"{k}: {v}")

    print("\nUnmapped Items:")
    for item in unmapped_items:
        print(item)
    
# Load rfq_fields to excel workbook for editing
# print("Exporting RFQ Fields")    
# rfq_to_excel()
# print("Exported.")    

# Write modified fields to json
# write_edited_xlsx_to_fieldMap()
# print("New Json created")

map_existing_fitttings()        

