# Architekt-ATOM Plugin System

This document explains how to create and use plugins in the Architekt-ATOM application.

## Plugin Overview

The Architekt-ATOM plugin system allows you to extend the application's functionality by creating simple Python functions that can be discovered and executed through the Plugin Manager dialog. Plugins provide a way to add custom features without modifying the core application code.

## Creating Plugins

### File Naming Convention

1. Create a new Python file in the `src/plugins` directory
2. The filename must start with `plugin_` (e.g., `plugin_myfunction.py`)

### Function Naming Convention

1. Within your plugin file, define one or more functions that start with `plugin_`
2. Example: `def plugin_hello():`

### Function Documentation

For best results, include comprehensive documentation in your plugin functions:

1. Add a detailed docstring that explains what the function does
2. Document parameters using the Args section in the docstring
3. Document return values using the Returns section
4. Include type annotations for parameters and return values

Example:

```python
def plugin_hello(name: str = "World", show_exclamation: bool = True) -> str:
    """
    A simple plugin that prints and returns a hello message.

    This plugin demonstrates how docstrings and parameters are displayed
    in the enhanced plugin dialog.

    Args:
        name: The name to greet (default: "World")
        show_exclamation: Whether to add an exclamation mark (default: True)

    Returns:
        A greeting message as a string
    """
    greeting = f"Hello, {name}"
    if show_exclamation:
        greeting += "!"

    print(f"Executing greeting plugin...")
    print(f"Generated message: {greeting}")

    return greeting
```

## Using the Plugin Manager

1. Open the Plugin Manager dialog from the application menu
2. The left pane shows all available plugins under the "Plugin Location" header
3. Select a plugin to view its details in the right pane
4. Enter custom parameter values in the parameter input section if available
5. Click the "Run" button in the top-right to execute the selected plugin
6. Use the "Refresh Plugins" button to reload plugins after making changes

### Plugin Details

When you select a plugin, the right pane displays:

1. Function name and signature
2. Relative path to the plugin file
3. Detailed description from the docstring
4. Parameter information including types and default values

### Custom Parameters

The Plugin Dialog provides a user interface for entering parameter values:

1. A "Custom Parameters" section appears when a plugin has non-reserved parameters
2. Different input types are provided based on parameter types:
   - Checkboxes for boolean parameters
   - Spin boxes for integer parameters
   - Double spin boxes for float parameters
   - Text fields for string and other types
3. Default values are pre-filled when available
4. Values are automatically passed to the plugin when executed

### Reserved Arguments

The Plugin Dialog displays a list of reserved arguments at the top. These special parameters are automatically populated with application data:

- `bom_data`: BOM table data
- `general_data`: General table data
- `spool_data`: Spool table data
- `spec_data`: Spec table data
- `outlier_data`: Outlier table data

To use these data sources, simply include them as parameters in your plugin function:

```python
def plugin_analyze_bom(bom_data=None):
    """Analyze BOM data"""
    if bom_data is not None:
        # Process the BOM data
        return f"BOM has {len(bom_data)} rows"
    return "No BOM data available"
```

### Plugin Output

When you run a plugin:

1. The execution start time is displayed
2. Any print statements from the plugin appear in the output area
3. The return value is displayed in the result section
4. The execution end time and total duration are shown
5. Error messages are shown if the plugin encounters problems

## Tips for Plugin Development

1. **Dynamic Refreshing**: You can modify plugin files while the Plugin Manager is open and click "Refresh Plugins" to see your changes without restarting the application
2. **Clear Output**: Use the "Clear" button in the right pane to clear the output area
3. **Error Handling**: Proper error handling in your plugins will make debugging easier
4. **Print Statements**: Use print statements for progress updates or debugging information
5. **Return Values**: Always return a meaningful value from your plugin functions
6. **Parameter Types**: Use type annotations to get appropriate input widgets in the UI
7. **Reserved Arguments**: Use reserved arguments to access application data tables

## Example Plugins

See the included example plugins:

- `plugin_printer.py`: Contains simple demonstration plugins
  - `plugin_hello`: A basic greeting function with custom parameters
  - `plugin_calculate`: A function that performs calculations with different parameter types

- `plugin_tables_print.py`: Demonstrates using reserved arguments
  - `plugin_print_tables`: Shows information about available table data
  - `plugin_count_rows`: Counts rows across all available tables

You can use these as templates for creating your own plugins.

## Advanced Plugin Features

### Parameter Types

The Plugin Manager will display parameter types and default values when you select a plugin. Supported types include:

- Basic types: `str`, `int`, `float`, `bool`
- Complex types: `list`, `dict`, `tuple`
- Custom types: Any Python class

The dialog provides appropriate input widgets based on type annotations:

| Type | Input Widget | Notes |
|------|-------------|-------|
| `bool` | Checkbox | Pre-checked if default is `True` |
| `int` | Spin box | Range from -1,000,000 to 1,000,000 |
| `float` | Double spin box | 4 decimal places of precision |
| Other types | Text field | Attempts to convert strings to appropriate types |

### Execution Timing

The Plugin Dialog provides detailed timing information:

1. Start time: When the plugin execution began
2. Completion time: When the plugin finished executing
3. Execution duration: Total time in minutes and seconds

This helps identify slow-running plugins and track performance.

### Plugin Execution Context

Plugins run in the context of the main application and have access to:

- Standard Python libraries
- All modules imported in your plugin file
- The plugin's own namespace
- Application data tables via reserved arguments

### Error Handling

If a plugin raises an exception:

1. The error message is displayed in the Plugin Manager
2. The execution time and duration are still shown
3. The application continues running normally

## Troubleshooting

### Common Issues

1. **Plugin Not Appearing**: Ensure your file and function names start with `plugin_`
2. **Import Errors**: Make sure all required modules are installed
3. **Runtime Errors**: Check your plugin's error handling and input validation
4. **Parameter Type Issues**: Use explicit type annotations for best results with the parameter input UI

### Debugging Tips

1. Add print statements to your plugin to track execution
2. Use try/except blocks to catch and display specific errors
3. Use the Refresh button to reload your plugin after making changes
4. Check the execution timing to identify performance bottlenecks