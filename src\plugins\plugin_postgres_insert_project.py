import pandas as pd
from psycopg2.extras import execute_values
from datetime import datetime

# from src.atom.pg_database.schemas.base.insert_tables import INSERT_BOM, INSERT_GENERAL
# from src.atom.pg_database.schemas.base.sqlite_column_mapping import BOM_TABLE_MAPPING, GENERAL_TABLE_MAPPING
from src.atom.pg_database import pg_connection



def plugin_postgres_insert_project(project_id: int = 0,
                         client_id: int = 0,
                         project_name: str = "",
                         location: str = "",
                         jobsite_location: str = "",
                         bid_revision: datetime = None,
                         bid_due_date: datetime = None,
                         received_from_client: datetime = None,
                         engineering_drafter: str = "",
                         ais_project_status: str = "",
                         notes: str = "",
                         profile_id: int = 0,
                         ):
    """
    Inserts a new project into the PostgreSQL database.

    Args:
        project_id (int): Project ID (must be >= 1)
        client_id (int): Client ID (must be >= 1)
        project_name (str): Name of the project
        location (str): Project location
        jobsite_location (str): Jobsite location
        bid_revision (datetime): Bid revision date
        bid_due_date (datetime): Bid due date
        received_from_client (datetime): Date received from client
        engineering_drafter (str): Engineering drafter name
        ais_project_status (str): AIS project status
        notes (str): Additional notes
        profile_id (int): Profile ID (must be >= 1)

    Returns:
        bool: True if successful, False otherwise
    """
    print("Inserting new project into PostgreSQL database.")

    created_at = datetime.now()
    updated_at = datetime.now()

    # Validate IDs
    try:
        project_id = int(project_id)
        client_id = int(client_id)
        profile_id = int(profile_id)
    except (ValueError, TypeError):
        print("Error: project_id, client_id, and profile_id must be integers.")
        return False

    if project_id < 1:
        print("Error: project_id must be >= 1.")
        return False

    if client_id < 1:
        print("Error: client_id must be >= 1.")
        return False

    if profile_id < 1:
        print("Error: profile_id must be >= 1.")
        return False

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")

            # Create cursor
            with conn.cursor() as cursor:
                # Check if project_id already exists
                cursor.execute("SELECT id FROM public.atem_projects WHERE id = %s", (project_id,))
                if cursor.fetchone():
                    raise Exception(f"Project with ID {project_id} already exists. Updating not supported.")

                    # Update existing project
                    update_query = """
                    UPDATE public.atem_projects
                    SET
                        client_id = %s,
                        project_name = %s,
                        location = %s,
                        jobsite_location = %s,
                        bid_revision = %s,
                        bid_due_date = %s,
                        received_from_client = %s,
                        engineering_drafter = %s,
                        ais_project_status = %s,
                        notes = %s,
                        profile_id = %s,
                        updated_at = %s
                    WHERE id = %s
                    RETURNING id
                    """

                    cursor.execute(update_query, (
                        client_id,
                        project_name,
                        location,
                        jobsite_location,
                        bid_revision,
                        bid_due_date,
                        received_from_client,
                        engineering_drafter,
                        ais_project_status,
                        notes,
                        profile_id,
                        updated_at,
                        project_id
                    ))

                    result = cursor.fetchone()

                    # Add explicit commit to ensure data is saved
                    conn.commit()

                    print(f"Project updated successfully. Project ID: {result[0]}")

                else:
                    # Insert new project
                    insert_query = """
                    INSERT INTO public.atem_projects (
                        id, client_id, project_name, location, jobsite_location,
                        bid_revision, bid_due_date, received_from_client,
                        engineering_drafter, ais_project_status, notes,
                        profile_id, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    RETURNING id
                    """

                    cursor.execute(insert_query, (
                        project_id,
                        client_id,
                        project_name,
                        location,
                        jobsite_location,
                        bid_revision,
                        bid_due_date,
                        received_from_client,
                        engineering_drafter,
                        ais_project_status,
                        notes,
                        profile_id,
                        created_at,
                        updated_at
                    ))

                    result = cursor.fetchone()

                    # Add explicit commit to ensure data is saved
                    conn.commit()

                    print(f"Project inserted successfully. Project ID: {result[0]}")

                return True

    except Exception as e:
        print(f"Failed to insert project data: {e}")
        import traceback
        traceback.print_exc()
        return False