'''
New Main Entry Point for Uploads
'''

from typing import Dict, Optional, List
import pandas as pd
import re
from ..pg_connection import DatabaseConfig
from .import_base_tables import (
    import_atem_rfq_df,
    import_atem_rfq_input_df,
    #import_bom_df,
    import_bom_direct,
    import_general_df,
    import_general_direct,
    import_verified_material_classifications_df,
    import_verified_material_classifications_direct
)
from .import_profile_tables import (
    import_flange_data,
    import_tee_reducing,
    import_standard_fittings,
    import_bom_component_mapping
)

from ..pg_connection import DatabaseConfig, get_db_connection
from ..schemas.base.column_mapping import ColumnMapper


# Utility function to convert a string to snake_case
def to_snake_case(s: str) -> str:
    """
    Convert a string to snake_case.

    Args:
        s: Input string (can be camelCase, TitleCase, etc.)

    Returns:
        String converted to snake_case
    """
    # Replace any non-alphanumeric character with underscore
    s = re.sub(r'[^a-zA-Z0-9]', '_', s)

    # Insert underscore before uppercase letters
    s = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', s)

    # Convert to lowercase
    return s.lower()


def get_pg_column_names(display_names, table_name, db_config=None):
    """
    Convert a list of display column names to their PostgreSQL equivalents and return them in the same order.

    Args:
        display_names (list): List of display column names to convert
        table_name (str): Name of the table (e.g., 'general', 'bom', 'atem_rfq')
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        list: List of PostgreSQL column names in the same order as the input display names
    """
    # Extract table name without schema
    table_name_only = table_name.split('.')[-1] if '.' in table_name else table_name

    # Initialize ColumnMapper
    mapper = ColumnMapper()

    # Convert each display name to its PostgreSQL equivalent
    pg_columns = []
    unmapped = []

    for display_name in display_names:
        pg_col = mapper.display_to_pg(table_name_only, display_name)
        if pg_col:
            pg_columns.append(pg_col)
        else:
            # If no mapping found, flag as unmapped
            unmapped.append(display_name)

    if unmapped:
        print(f"Warning: Could not map display names: {unmapped}")

    return pg_columns


def get_pg_columns_for_query(display_names, table_name, db_config=None):
    """
    Convert a list of display column names to their PostgreSQL equivalents and return them as a comma-separated string
    for use in SQL queries.

    Args:
        display_names (list): List of display column names to convert
        table_name (str): Name of the table (e.g., 'general', 'bom', 'atem_rfq')
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        str: Comma-separated string of PostgreSQL column names for use in SQL queries
    """
    pg_columns = get_pg_column_names(display_names, table_name, db_config)
    return ", ".join(pg_columns)


def get_excel_columns_as_pg(excel_path, sheet_name=None, table_name="general", db_config=None):
    """
    Read column names from an Excel file, convert them to PostgreSQL format, and return them
    as a comma-separated string without quotes.

    Args:
        excel_path (str): Path to the Excel file
        sheet_name (str, optional): Name of the sheet to read. If None, uses the first sheet.
        table_name (str): Name of the table (e.g., 'general', 'bom', 'atem_rfq')
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        str: Comma-separated string of PostgreSQL column names without quotes
    """
    try:
        # Read just the header row from the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(excel_path, sheet_name=0, nrows=0)
        else:
            df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=0)

        # Get the column names from the DataFrame
        display_names = df.columns.tolist()
        print(f"Excel columns: {display_names}")

        # Extract table name without schema
        table_name_only = table_name.split('.')[-1] if '.' in table_name else table_name

        # Initialize ColumnMapper
        mapper = ColumnMapper()

        # Convert each column name to its PostgreSQL equivalent
        pg_columns = []
        unmapped_columns = []
        for col in display_names:
            # Check if the column is already in PostgreSQL format
            if col in ["project_id", "pdf_id", "pdf_page", "sys_filename", "medium_code",
                      "min_elevation", "max_elevation", "avg_elevation", "pid", "length",
                      "calculated_eq_length", "client_id", "profile_id"]:
                pg_columns.append(col)
            else:
                # Try to convert using the mapper
                pg_col = mapper.display_to_pg(table_name_only, col)
                if pg_col:
                    pg_columns.append(pg_col)
                else:
                    # If no mapping found, flag as unmapped
                    unmapped_columns.append(col)
                    print(f"Warning: Could not map column '{col}'")

        # Join the columns with commas (no quotes)
        result = ", ".join(pg_columns)
        print(f"PostgreSQL columns: {result}")

        return result

    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")
        return ""

def import_with_ids(workbook_path, table_name, client_id=None, profile_id=None, project_id=None, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into a PostgreSQL table, adding required IDs if they don't already exist.

    Args:
        workbook_path (str): Path to the Excel workbook
        table_name (str): Name of the target table (e.g., "public.atem_rfq")
        client_id (int, optional): Client ID to add to the dataframe if needed
        profile_id (int, optional): Profile ID to add to the dataframe if needed
        project_id (int, optional): Project ID to add to the dataframe if needed
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet
        db_config (dict, optional): Database configuration. If None, uses default

    Returns:
        dict: Result of the import operation
    """
    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Add required IDs if they don't already exist in the DataFrame
        if client_id is not None and 'client_id' not in df.columns:
            df['client_id'] = client_id
            print(f"Added client_id = {client_id}")

        if profile_id is not None and 'profile_id' not in df.columns:
            df['profile_id'] = profile_id
            print(f"Added profile_id = {profile_id}")

        if project_id is not None and 'project_id' not in df.columns:
            df['project_id'] = project_id
            print(f"Added project_id = {project_id}")

        # Display confirmation message
        print(f"\nTable: {table_name}")
        print(f"Rows to be imported: {len(df)}")
        print(f"Client ID: {df['client_id'].iloc[0] if 'client_id' in df.columns else 'Not specified'}")
        print(f"Profile ID: {df['profile_id'].iloc[0] if 'profile_id' in df.columns else 'Not specified'}")
        print(f"Project ID: {df['project_id'].iloc[0] if 'project_id' in df.columns else 'Not specified'}")

        confirm = input("\nDo you want to proceed with this import? (y/n): ")
        if confirm.lower() not in ['y', 'yes']:
            return {
                "status": "cancelled",
                "message": "Import cancelled by user",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": []
            }

        # Call the appropriate import function based on the table name
        if table_name == "public.atem_rfq":
            # Check if we should also import to atem_rfq_input
            import_to_input = input("\nDo you want to also import to atem_rfq_input table first? (y/n): ")
            import_to_input_first = import_to_input.lower() in ['y', 'yes']
            return import_atem_rfq_df(df, db_config, import_to_input_first=import_to_input_first)

        elif table_name == "public.atem_rfq_input":
            return import_atem_rfq_input_df(df, db_config)

        elif table_name == "public.bom":
            # Use the direct import method for BOM table to avoid timeouts
            return import_bom_direct(db_config=db_config, dataframe=df)
            # Comment out the original method in case we need to revert
            # return import_bom_df(df, db_config)

        elif table_name == "public.general":
            # Ask if user wants to use the direct import method with progress updates
            use_direct = input("\nDo you want to use the direct import method with progress updates? (y/n): ")
            if use_direct.lower() in ['y', 'yes']:
                return import_general_direct(db_config=db_config, dataframe=df)
            else:
                return import_general_df(df, db_config)

        elif table_name == "public.verified_material_classifications":
            # Ask if user wants to use the direct import method with progress updates
            use_direct = input("\nDo you want to use the direct import method with progress updates? (y/n): ")
            if use_direct.lower() in ['y', 'yes']:
                return import_verified_material_classifications_direct(db_config=db_config, dataframe=df)
            else:
                return import_verified_material_classifications_df(df, db_config)

        # Profile tables - these functions handle reading the Excel file themselves
        elif table_name == "public.atem_bom_component_mapping":
            # For profile tables, we call the import function directly with the workbook path
            # rather than passing the DataFrame
            print("Importing to BOM component mapping table using profile table importer...")
            return import_bom_component_mapping(workbook_path, sheet_name, db_config, profile_id)

        elif table_name == "public.flange_data":
            return import_flange_data(workbook_path, sheet_name, db_config)

        elif table_name == "public.tee_reducing":
            return import_tee_reducing(workbook_path, sheet_name, db_config)

        elif table_name == "public.standard_fittings":
            return import_standard_fittings(workbook_path, sheet_name, db_config)

        else:
            return {"status": "error", "message": f"Import to {table_name} not implemented"}

    except Exception as e:
        error_msg = str(e)
        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": [{"row_index": "N/A", "error": error_msg}]
        }


if __name__ == "__main__":
    # Example of using the column name conversion functions
    if False:  # Set to True to test column name conversion
        # Get columns directly from Excel file
        excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 037\general.xlsx"
        pg_columns = get_excel_columns_as_pg(excel_path, table_name="general")
        print("\nColumns for SQL query (from Excel):\n")
        print(pg_columns)

        # Exit after testing
        exit(0)

    # Configuration
    # Add required IDs if they don't already exist in the dataframe
    client_id = 2  # Specified client_id
    project_id = 24  # Specified project_id
    profile_id = 3  # Specified profile_id

    # Example paths for different table types
    # general_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-003\In\general.xlsx"
    #workbook_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0000_Dow\Data\COMBINED ISO'S S1601.pdf\exported_bom_data-s1601.xlsx"
    workbook_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 041 - Novelis\Data\exported_general_data_nofieldmap_elevation_test_results.xlsx"
    # Table to import into
    # Options: "public.general", "public.atem_rfq", "public.atem_rfq_input", "public.bom",
    #         "public.verified_material_classifications", "public.atem_bom_component_mapping"
    # table_name = "public.verified_material_classifications"  # Change this to match your workbook_path selection
    #table_name = "public.bom"  # Change this to match your workbook_path selection
    table_name = "public.general"

    # For testing the verified_material_classifications table
    # test_verified_materials = False  # Set to True to test importing to verified_material_classifications
    # if test_verified_materials:
    #     table_name = "public.verified_material_classifications"
    #     workbook_path = r"C:\path\to\your\verified_materials.xlsx"

    # For testing the atem_bom_component_mapping table
    # test_component_mapping = False  # Set to True to test importing to atem_bom_component_mapping
    # if test_component_mapping:
    #     table_name = "public.atem_bom_component_mapping"
    #     workbook_path = r"C:\path\to\your\component_mapping.xlsx"

    # For testing the atem_rfq_input table directly
    # test_input_table = False  # Set to True to test importing directly to atem_rfq_input
    # if test_input_table:
    #     table_name = "public.atem_rfq_input"

    # Optional sheet name (None uses first sheet)
    sheet_name = None

    # Create database configuration
    db_config = DatabaseConfig()

    # First test database connection
    try:
        with get_db_connection(db_config) as conn:
            print(f"Database connection successful.")
            print(f"PostgreSQL server version: {conn.server_version}")
    except Exception as e:
        print(f"Error connecting to database: {e}")
        exit(1)

    # For verified_material_classifications or general table, offer direct import option
    if table_name == "public.verified_material_classifications" or table_name == "public.general":
        use_direct = input("\nDo you want to use direct import with progress updates? (y/n): ")
        if use_direct.lower() in ['y', 'yes']:
            # Read the Excel file
            if sheet_name is None:
                # Use the first sheet by default
                df = pd.read_excel(workbook_path, sheet_name=0)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)

            # Add required IDs if they don't already exist in the DataFrame
            if client_id is not None and 'client_id' not in df.columns:
                df['client_id'] = client_id
            if profile_id is not None and 'profile_id' not in df.columns:
                df['profile_id'] = profile_id
            if project_id is not None and 'project_id' not in df.columns:
                df['project_id'] = project_id

            print(f"\nTable: {table_name}")
            print(f"Rows to be imported: {len(df)}")

            confirm = input("\nDo you want to proceed with this direct import? (y/n): ")
            if confirm.lower() in ['y', 'yes']:
                # Call the appropriate direct import function based on the table
                if table_name == "public.verified_material_classifications":
                    result = import_verified_material_classifications_direct(
                        dataframe=df,
                        db_config=db_config
                    )
                elif table_name == "public.general":
                    result = import_general_direct(
                        dataframe=df,
                        db_config=db_config
                    )
            else:
                result = {
                    "status": "cancelled",
                    "message": "Import cancelled by user",
                    "total_rows": len(df),
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": 0,
                    "errors": []
                }
        else:
            # Use the standard import_with_ids function
            result = import_with_ids(
                workbook_path=workbook_path,
                table_name=table_name,
                client_id=client_id,
                profile_id=profile_id,
                project_id=project_id,
                sheet_name=sheet_name,
                db_config=db_config
            )
    else:
        # Use the new import_with_ids function that adds IDs and confirms before importing
        result = import_with_ids(
            workbook_path=workbook_path,
            table_name=table_name,
            client_id=client_id,
            profile_id=profile_id,
            project_id=project_id,
            sheet_name=sheet_name,
            db_config=db_config
        )

    # Print results
    print("\nImport results:")
    print(f"Total rows in workbook: {result['total_rows']}")
    print(f"Valid rows: {result['valid_rows']}")
    print(f"Inserted: {result['inserted']}")
    print(f"Updated: {result['updated']}")
    print(f"Errors: {result['error_rows']}")

    # Print error details if any
    if result['error_rows'] > 0:
        print("\nError details:")
        for i, error in enumerate(result['errors'][:5]):  # Show first 5 errors
            print(f"Error {i+1}: Row {error['row_index']}: {error['error']}")

        if len(result['errors']) > 5:
            print(f"...and {len(result['errors']) - 5} more errors")
