"""
Test script for BOM Classification Audit System with Real Data - 2 Rows Only

This script loads real data from the Excel file and runs the actual audit
on just 2 rows to test the output format and ensure it works properly.
"""

import sys
import os
import pandas as pd
import json
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the path to import audit_main
sys.path.append(os.path.dirname(__file__))

from audit_main import (
    audit_bom_dataframe,
    AuditConfig,
    ModelType,
    normalize_rating_column,
    apply_audit_corrections
)


def load_real_data_full_file(file_path):
    """Load real data from Excel file and return all rows"""
    print("=" * 80)
    print("LOADING REAL DATA FROM EXCEL FILE (FULL FILE)")
    print("=" * 80)
    
    try:
        # Load the Excel file
        print(f"Loading data from: {file_path}")
        df = pd.read_excel(file_path)
        
        print(f"Original data shape: {df.shape}")
        print(f"Original columns: {list(df.columns)}")
        
        # Use all rows (remove .head(2) to process full file)
        df_subset = df.copy()

        # Normalize rating values to remove decimal points for whole numbers
        df_subset = normalize_rating_column(df_subset)
        if 'rating' in df_subset.columns:
            print("✓ Normalized rating values (removed decimal points for whole numbers)")

        print(f"Using all rows from file")
        print(f"Data shape: {df_subset.shape}")
        
        # Check for required columns and map them
        required_cols = ['id', 'material_description']
        missing_cols = [col for col in required_cols if col not in df_subset.columns]
        
        if missing_cols:
            print(f"⚠️  Missing required columns: {missing_cols}")
            
            # Try to map common column names
            column_mapping = {}
            
            # Look for ID-like columns
            id_candidates = [col for col in df_subset.columns if 'id' in col.lower() or 'item' in col.lower() or 'line' in col.lower()]
            if id_candidates and 'id' not in df_subset.columns:
                column_mapping['id'] = id_candidates[0]
                print(f"Mapping '{id_candidates[0]}' to 'id'")
            
            # Look for description-like columns
            desc_candidates = [col for col in df_subset.columns if 'description' in col.lower() or 'material' in col.lower()]
            if desc_candidates and 'material_description' not in df_subset.columns:
                column_mapping['material_description'] = desc_candidates[0]
                print(f"Mapping '{desc_candidates[0]}' to 'material_description'")
            
            # Apply mapping
            if column_mapping:
                df_subset = df_subset.rename(columns=column_mapping)
                print(f"Applied column mapping: {column_mapping}")
            
            # Create dummy columns if still missing
            if 'id' not in df_subset.columns:
                df_subset['id'] = [f'item_{i+1}' for i in range(len(df_subset))]
                print("Created dummy 'id' column")

            # Ensure ID column has no NaN values
            if df_subset['id'].isna().any():
                df_subset['id'] = df_subset['id'].fillna('').astype(str)
                df_subset.loc[df_subset['id'] == '', 'id'] = [f'item_{i+1}' for i in range(len(df_subset))]
                print("Fixed NaN values in 'id' column")
            
            if 'material_description' not in df_subset.columns:
                # Try to find the best description column
                text_cols = df_subset.select_dtypes(include=['object']).columns
                if len(text_cols) > 0:
                    df_subset['material_description'] = df_subset[text_cols[0]].fillna('No description')
                    print(f"Using '{text_cols[0]}' as material_description")
                else:
                    df_subset['material_description'] = 'No description available'
                    print("Created dummy 'material_description' column")
        
        print(f"Final columns: {list(df_subset.columns)}")
        print("\nSample data:")
        print(df_subset[['id', 'material_description']].to_string(index=False))
        
        return df_subset

    except Exception as e:
        print(f"❌ Error loading Excel data: {e}")
        print("SOLUTION: Close the Excel file and try again.")
        raise e


async def run_audit_test(df):
    """Run the actual audit on the dataframe"""
    print("\n" + "=" * 80)
    print("RUNNING ACTUAL AUDIT WITH LANGCHAIN GOOGLE GENAI")
    print("=" * 80)

    # Check for API key (Langchain uses GOOGLE_API_KEY)
    api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY or GEMINI_API_KEY not found in environment variables")
        return None

    print(f"✓ API key found: {api_key[:10]}...")

    # Check if langchain-google-genai is available
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        print("✓ Langchain Google GenAI package available")
    except ImportError:
        print("❌ Langchain Google GenAI package not installed")
        print("   Please install with: pip install langchain-google-genai")
        return None

    print(f"Processing {len(df)} rows...")

    # Configure audit with optimized settings for production
    config = AuditConfig(
        primary_model=ModelType.GEMINI_20_FLASH,
        fallback_model=ModelType.GEMINI_25_FLASH,
        max_retries=2,
        temperature=0.0,
        max_output_tokens=1500
    )

    try:
        # Run the audit with higher concurrency for speed
        print("Starting audit...")
        print(f"Processing {len(df)} rows with max_concurrent=20...")

        # Estimate processing time
        estimated_time_minutes = (len(df) * 1.5) / 20 / 60  # ~1.5 seconds per item, divided by concurrency, converted to minutes
        print(f"Estimated processing time: {estimated_time_minutes:.1f} minutes")
        print("Processing... (this may take a while for large files)")
        print("✓ Enhanced retry logic: 5 retries per item with exponential backoff")
        print("✓ Rate limit detection: Automatic handling of API rate limits")

        results = await audit_bom_dataframe(df, config=config, max_concurrent=20)
        
        print(f"✓ Audit completed! Processed {len(results)} items")
        return results
        
    except Exception as e:
        print(f"❌ Audit failed: {e}")
        import traceback
        traceback.print_exc()
        return None



def analyze_results(results):
    """Analyze and display the audit results in detail"""
    print("\n" + "=" * 80)
    print("DETAILED RESULTS ANALYSIS")
    print("=" * 80)
    
    if not results:
        print("No results to analyze")
        return
    
    # Summary statistics
    total_items = len(results)
    items_with_issues = sum(1 for r in results if r.status == "issues")
    items_ok = sum(1 for r in results if r.status == "ok")
    items_error = sum(1 for r in results if r.status == "error")
    
    print(f"Summary:")
    print(f"  Total items processed: {total_items}")
    print(f"  Items with issues: {items_with_issues}")
    print(f"  Items OK: {items_ok}")
    print(f"  Items with errors: {items_error}")
    
    # Detailed analysis of each result
    for i, result in enumerate(results, 1):
        print(f"\n" + "-" * 60)
        print(f"ITEM {i}: {result.id}")
        print(f"Status: {result.status}")
        print(f"Model used: {result.model_used}")
        print(f"Processing time: {result.processing_time:.2f}s")
        
        if result.status == "error":
            print(f"Error: {result.error_message}")
        
        elif result.status == "issues":
            print(f"Issues found: {len(result.issues)}")
            for field, issue in result.issues.items():
                print(f"  Field: {field}")
                print(f"    Current: {issue['current_value']}")
                print(f"    Suggested: {issue['suggested']}")
                print(f"    Confidence: {issue['confidence']}")
                print(f"    Explanation: {issue['explanation']}")
        
        elif result.status == "ok":
            print("No issues found - all classifications appear correct")


def save_results_to_dataframe(results, original_df):
    """Convert results to a structured DataFrame for further analysis"""
    print("\n" + "=" * 80)
    print("CONVERTING RESULTS TO STRUCTURED DATAFRAME")
    print("=" * 80)

    if not results:
        print("No results to convert")
        return None

    # Create a mapping from id to material_description
    id_to_description = {}
    if original_df is not None and 'id' in original_df.columns and 'material_description' in original_df.columns:
        id_to_description = dict(zip(original_df['id'], original_df['material_description']))

    # Create programmatic format: one row per issue
    programmatic_data = []

    for result in results:
        material_description = id_to_description.get(result.id, "")

        if result.status == "issues" and result.issues:
            # Create one row for each issue
            for field_name, issue in result.issues.items():
                programmatic_data.append({
                    'id': result.id,
                    'status': result.status,
                    'material_description': material_description,
                    'column_name': field_name,
                    'current_value': issue['current_value'],
                    'suggested_value': issue['suggested'],
                    'confidence': issue['confidence'],
                    'explanation': issue['explanation'],
                    'accept_merge': None  # Boolean column for user to edit
                })
        else:
            # For items with no issues or errors, create a single row
            programmatic_data.append({
                'id': result.id,
                'status': result.status,
                'material_description': material_description,
                'column_name': None,
                'current_value': None,
                'suggested_value': None,
                'confidence': None,
                'explanation': result.error_message if result.status == "error" else None,
                'accept_merge': None  # Boolean column for user to edit
            })

    programmatic_df = pd.DataFrame(programmatic_data)

    print(f"Programmatic DataFrame shape: {programmatic_df.shape}")
    print(f"Programmatic DataFrame columns: {list(programmatic_df.columns)}")

    # Save to Excel for inspection in the same output folder
    output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output"
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, "audit_results_programmatic.xlsx")
    try:
        programmatic_df.to_excel(output_file, index=False)
        print(f"✓ Programmatic results saved to: {output_file}")
    except Exception as e:
        print(f"❌ Error saving programmatic results: {e}")

    # Programmatic DataFrame preview (COMMENTED OUT FOR PRODUCTION)
    # print("\nProgrammatic DataFrame preview:")
    # print(programmatic_df.to_string(index=False))

    return programmatic_df


async def main():
    """Main test function"""
    print("BOM Classification Audit System - Full File Processing")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Configuration
    excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\rfq_template.xlsx"
    
    try:
        # Load real data (full file)
        df = load_real_data_full_file(excel_file_path)
        
        # Run the actual audit
        results = await run_audit_test(df)
        
        if results:
            # Analyze results (COMMENTED OUT FOR PRODUCTION - STREAMLINED OUTPUT)
            # analyze_results(results)

            # Convert to structured DataFrame
            results_df = save_results_to_dataframe(results, df)

            # Quick summary of issues found
            items_with_issues = sum(1 for r in results if r.status == "issues")
            items_ok = sum(1 for r in results if r.status == "ok")
            items_error = sum(1 for r in results if r.status == "error")

            print("\n" + "=" * 80)
            print("AUDIT COMPLETED SUCCESSFULLY")
            print("=" * 80)
            print(f"✓ Processed {len(df)} rows from real data")
            print(f"✓ Items OK: {items_ok}")
            print(f"✓ Items with issues: {items_with_issues}")
            print(f"✓ Items with errors: {items_error}")
            print(f"✓ Results saved to Excel file")

            print("\n" + "=" * 80)
            print("ALL FILES SAVED TO OUTPUT FOLDER")
            print("=" * 80)
            output_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output"
            print(f"📁 Folder: {output_folder}")
            print(f"📄 Files created:")
            print(f"   - audit_results_programmatic.xlsx (Programmatic format for corrections)")
            print(f"   (Debug files disabled for production run)")
            print()
            print("📋 NEXT STEPS:")
            print("1. Open audit_results_programmatic.xlsx")
            print("2. Set 'accept_merge' = True for corrections you want to apply")
            print("3. Run: python apply_corrections.py")
            print("4. This will create a new corrected workbook with your changes")
        
        else:
            print("\n❌ Test failed - no results generated")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
