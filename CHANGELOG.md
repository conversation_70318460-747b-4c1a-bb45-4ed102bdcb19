# Changelog

## [Unreleased]

### Added

- Group PDFs

### Changed

- 

### Removed

-

### Changed


### Fixed

- Fix typos in recent README changes.
- Update outdated unreleased diff link.


## [0.15.2] - 2024-10-24

### Added

- Dynamic token price display
- Added `Last updated` field. Displays the timestamp of RFQ record last saved

### Changed

- Table export
- Weld detection classification and updates
- Multi-row item selection

### Fixed

- Merge into RFQ fixes
- Table cut and paste not pasting correctly
- Frozen table font scaling
- Frozen item (non-combobox) delegate not closing

## [0.15.1] - 2024-09-18

### Added

- Table exports and presets management

### Changed

- Grouped PDFs
- Drawing classification

### Fixed

- Build script version check fix


## [0.15] - 2024-12-12


## [0.14] - 2024-08-30

### Added

- New table export dialog

### Changed

- Records without general_category or RFQ scope are considered unclassified for MTO
- Message prompt includes classification count
- ROI overlap intersection logic

### Fixed

- MTO updates re-merge columns after classification
- Inc<PERSON> originally requested fields on ROI extraction as they did not appear if not
picked up on extraction

## [0.13] - 2024-07-17

### Added

- Better explanation of the difference between the file ("CHANGELOG")
  and its function "the change log".

### Changed

- ROI overlap intersection logic

### Fixed

- Include originally requested fields on ROI extraction as they did not appear if not
picked up on extraction

## [0.12] - 2024-06-27

### Added

- 

## [0.11] - 2024-06-21

### Added

- Explanation of the recommended reverse chronological release ordering.

## [0.1] - 2024-06-21

### Added



