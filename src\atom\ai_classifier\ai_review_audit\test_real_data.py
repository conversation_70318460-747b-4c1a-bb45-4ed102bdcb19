"""
Test script for BOM Classification Audit System with Real Data

This script loads real data from the Excel file and generates request text
for inspection without making API calls. It processes only the first 5 rows
and excludes specified fields from the prompts.
"""

import sys
import os
import pandas as pd
import json
from datetime import datetime

# Add the parent directory to the path to import audit_main
sys.path.append(os.path.dirname(__file__))

from audit_main import (
    SystemPromptBuilder,
    RequestBuilder,
    AuditConfig
)


def load_real_data(file_path, max_rows=5):
    """Load real data from Excel file"""
    print("=" * 80)
    print("LOADING REAL DATA FROM EXCEL FILE")
    print("=" * 80)
    
    try:
        # Load the Excel file
        print(f"Loading data from: {file_path}")
        df = pd.read_excel(file_path)
        
        print(f"Original data shape: {df.shape}")
        print(f"Original columns: {list(df.columns)}")
        
        # Take only first max_rows
        df_subset = df.head(max_rows).copy()
        
        print(f"Using first {max_rows} rows")
        print(f"Subset shape: {df_subset.shape}")
        
        # Check for required columns
        required_cols = ['id', 'material_description']
        missing_cols = [col for col in required_cols if col not in df_subset.columns]
        
        if missing_cols:
            print(f"⚠️  Missing required columns: {missing_cols}")
            
            # Try to map common column names
            column_mapping = {}
            
            # Look for ID-like columns
            id_candidates = [col for col in df_subset.columns if 'id' in col.lower() or 'item' in col.lower()]
            if id_candidates and 'id' not in df_subset.columns:
                column_mapping['id'] = id_candidates[0]
                print(f"Mapping '{id_candidates[0]}' to 'id'")
            
            # Look for description-like columns
            desc_candidates = [col for col in df_subset.columns if 'description' in col.lower() or 'material' in col.lower()]
            if desc_candidates and 'material_description' not in df_subset.columns:
                column_mapping['material_description'] = desc_candidates[0]
                print(f"Mapping '{desc_candidates[0]}' to 'material_description'")
            
            # Apply mapping
            if column_mapping:
                df_subset = df_subset.rename(columns=column_mapping)
                print(f"Applied column mapping: {column_mapping}")
            
            # Create dummy columns if still missing
            if 'id' not in df_subset.columns:
                df_subset['id'] = [f'item_{i+1}' for i in range(len(df_subset))]
                print("Created dummy 'id' column")
            
            if 'material_description' not in df_subset.columns:
                # Try to find the best description column
                text_cols = df_subset.select_dtypes(include=['object']).columns
                if len(text_cols) > 0:
                    df_subset['material_description'] = df_subset[text_cols[0]].fillna('No description')
                    print(f"Using '{text_cols[0]}' as material_description")
                else:
                    df_subset['material_description'] = 'No description available'
                    print("Created dummy 'material_description' column")
        
        print(f"Final columns: {list(df_subset.columns)}")
        print("\nSample data:")
        print(df_subset.head().to_string(index=False))
        
        return df_subset
        
    except Exception as e:
        print(f"Error loading data: {e}")
        print("Creating fallback sample data...")
        
        # Create fallback data
        fallback_data = {
            'id': [f'fallback_{i+1}' for i in range(max_rows)],
            'material_description': [
                'Pipe SMLS, ASTM A106, SCH 40, 6", BE',
                'Elbow 90 LR, ASTM A234, WPB, 4", SCH 40, BE',
                'Valve Gate, ASTM A216, WCB, 3", #600, RF',
                'Flange WN, ASTM A182, F316L, 2", #150, RF',
                'VENDOR PART XYZ123'
            ]
        }
        
        return pd.DataFrame(fallback_data)


def test_system_prompt_with_exclusions():
    """Test system prompt building with field exclusions"""
    print("\n" + "=" * 80)
    print("TESTING SYSTEM PROMPT WITH FIELD EXCLUSIONS")
    print("=" * 80)

    # Create system prompt builder with exclusions
    excluded_fields = ['unit_of_measure', 'size', 'size1', 'size2', 'quantity',
                      'componentCategory', 'component_category', 'ef', 'sf']
    builder = SystemPromptBuilder(excluded_fields=excluded_fields)
    system_prompt = builder.build_system_prompt()
    
    print(f"System prompt length: {len(system_prompt)} characters")
    print(f"Number of fields included: {len(builder.categorization_fields)}")
    
    # Check which fields are included
    excluded_fields = ['unit_of_measure', 'size', 'size1', 'size2', 'quantity', 
                      'componentCategory', 'component_category', 'ef', 'sf']
    
    included_fields = [field for field in builder.categorization_fields.keys() 
                      if field not in excluded_fields]
    
    print(f"Excluded fields: {excluded_fields}")
    print(f"Included fields: {included_fields}")
    print(f"Number of included fields: {len(included_fields)}")
    
    return system_prompt


def generate_request_texts(df, output_file):
    """Generate request texts for each row and save to file"""
    print("\n" + "=" * 80)
    print("GENERATING REQUEST TEXTS")
    print("=" * 80)
    
    # Create request builder with exclusions
    excluded_fields = ['unit_of_measure', 'size', 'size1', 'size2', 'quantity', 
                      'componentCategory', 'component_category', 'ef', 'sf']
    
    request_builder = RequestBuilder(excluded_fields=excluded_fields)
    system_prompt_builder = SystemPromptBuilder(excluded_fields=excluded_fields)

    # Get system prompt
    system_prompt = system_prompt_builder.build_system_prompt()
    
    print(f"Processing {len(df)} rows...")
    print(f"Excluded fields: {excluded_fields}")
    
    # Prepare output content
    output_content = []
    output_content.append("BOM CLASSIFICATION AUDIT SYSTEM - REQUEST TEXT INSPECTION")
    output_content.append("=" * 80)
    output_content.append(f"Generated at: {datetime.now()}")
    output_content.append(f"Number of rows processed: {len(df)}")
    output_content.append(f"Excluded fields: {excluded_fields}")
    output_content.append("")
    
    # Add system prompt
    output_content.append("SYSTEM PROMPT:")
    output_content.append("-" * 40)
    output_content.append(system_prompt)
    output_content.append("")
    output_content.append("=" * 80)
    output_content.append("INDIVIDUAL REQUEST TEXTS")
    output_content.append("=" * 80)
    
    # Process each row
    for idx, row in df.iterrows():
        row_data = row.to_dict()
        request = request_builder.build_request(row_data)
        
        print(f"Processing row {idx + 1}: {request['id']}")
        
        # Add to output
        output_content.append(f"\nROW {idx + 1} - ID: {request['id']}")
        output_content.append("-" * 60)
        output_content.append(f"Material Description: {request['material_description']}")
        output_content.append("")
        output_content.append("Current Classification:")
        
        for field, value in request['current_classification'].items():
            output_content.append(f"  {field}: {value}")
        
        output_content.append("")
        output_content.append("COMPLETE USER PROMPT:")
        output_content.append("-" * 30)
        output_content.append(request['user_prompt'])
        output_content.append("")
        output_content.append("COMPLETE REQUEST (System + User):")
        output_content.append("-" * 30)
        output_content.append("SYSTEM PROMPT:")
        output_content.append(system_prompt)
        output_content.append("")
        output_content.append("USER PROMPT:")
        output_content.append(request['user_prompt'])
        output_content.append("")
        output_content.append("=" * 80)
    
    # Write to file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(output_content))
        
        print(f"\n✓ Request texts saved to: {output_file}")
        print(f"  File size: {os.path.getsize(output_file)} bytes")
        
        # Show summary
        total_chars = sum(len(line) for line in output_content)
        print(f"  Total characters: {total_chars:,}")
        print(f"  System prompt length: {len(system_prompt):,}")
        
        avg_user_prompt_length = sum(len(request_builder.build_request(row.to_dict())['user_prompt']) 
                                   for _, row in df.iterrows()) / len(df)
        print(f"  Average user prompt length: {avg_user_prompt_length:.0f}")
        
    except Exception as e:
        print(f"✗ Error saving file: {e}")


def analyze_data_coverage(df):
    """Analyze how well the real data covers the classification fields"""
    print("\n" + "=" * 80)
    print("ANALYZING DATA COVERAGE")
    print("=" * 80)
    
    from prompts import categorization_table
    
    # Get all possible fields
    all_fields = [item["field"] for item in categorization_table]
    excluded_fields = ['unit_of_measure', 'size', 'size1', 'size2', 'quantity', 
                      'componentCategory', 'component_category', 'ef', 'sf']
    relevant_fields = [field for field in all_fields if field not in excluded_fields]
    
    print(f"Total classification fields: {len(all_fields)}")
    print(f"Excluded fields: {len(excluded_fields)}")
    print(f"Relevant fields for audit: {len(relevant_fields)}")
    
    # Check coverage
    df_columns = set(df.columns)
    covered_fields = [field for field in relevant_fields if field in df_columns]
    missing_fields = [field for field in relevant_fields if field not in df_columns]
    
    print(f"\nData Coverage:")
    print(f"  Fields present in data: {len(covered_fields)}")
    print(f"  Fields missing from data: {len(missing_fields)}")
    print(f"  Coverage percentage: {len(covered_fields)/len(relevant_fields)*100:.1f}%")
    
    print(f"\nCovered fields: {covered_fields}")
    print(f"Missing fields: {missing_fields}")
    
    # Analyze data quality for covered fields
    print(f"\nData Quality Analysis:")
    for field in covered_fields:
        if field in df.columns:
            non_null_count = df[field].notna().sum()
            non_empty_count = (df[field].astype(str).str.strip() != '').sum()
            print(f"  {field}: {non_null_count}/{len(df)} non-null, {non_empty_count}/{len(df)} non-empty")


def main():
    """Main test function"""
    print("BOM Classification Audit System - Real Data Test")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Configuration
    excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\rfq_template.xlsx"
    output_file_path = os.path.join(os.path.dirname(__file__), "request_text_inspection.txt")
    max_rows = 5
    
    try:
        # Load real data
        df = load_real_data(excel_file_path, max_rows)
        
        # Test system prompt
        system_prompt = test_system_prompt_with_exclusions()
        
        # Analyze data coverage
        analyze_data_coverage(df)
        
        # Generate request texts
        generate_request_texts(df, output_file_path)
        
        print("\n" + "=" * 80)
        print("REAL DATA TEST COMPLETED SUCCESSFULLY")
        print("=" * 80)
        print(f"✓ Processed {len(df)} rows from real data")
        print(f"✓ Generated request texts saved to: {output_file_path}")
        print(f"✓ System ready for API testing")
        
        print(f"\nNext steps:")
        print(f"1. Review the generated request text in: {output_file_path}")
        print(f"2. Verify the prompts look correct")
        print(f"3. Set GEMINI_API_KEY to test with real API calls")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
