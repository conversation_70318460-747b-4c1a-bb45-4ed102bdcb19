"""
Sample plugin that demonstrates how to use reserved arguments.
"""

def plugin_print_tables(bom_data=None, general_data=None):
    """
    Prints information about the available table data.

    This plugin demonstrates how to access the table data that is
    automatically passed from the main application.

    Args:
        bom_data: The BOM table data (automatically provided)
        general_data: The General table data (automatically provided)

    Returns:
        A summary of the table data
    """
    result = []

    # Check if BOM data is available
    if bom_data is not None:
        result.append(f"BOM Data: {len(bom_data)} rows, {len(bom_data.columns)} columns")
        result.append(f"Columns: {', '.join(bom_data.columns)}")
        result.append("")
    else:
        result.append("BOM Data: Not available")
        result.append("")

    # Check if General data is available
    if general_data is not None:
        result.append(f"General Data: {len(general_data)} rows, {len(general_data.columns)} columns")
        result.append(f"Columns: {', '.join(general_data.columns)}")
        result.append("")
    else:
        result.append("General Data: Not available")
        result.append("")

    # Print the results
    for line in result:
        print(line)

    # Return a summary
    return f"Found {sum(1 for x in [bom_data, general_data] if x is not None)} tables with data"


def plugin_count_rows(bom_data=None, general_data=None, spool_data=None, spec_data=None, outlier_data=None):
    """
    Counts the total number of rows across all available tables.

    This plugin demonstrates how to use all the reserved arguments.

    Args:
        bom_data: BOM table data
        general_data: General table data
        spool_data: Spool table data
        spec_data: Spec table data
        outlier_data: Outlier table data

    Returns:
        Total row count across all tables
    """
    tables = {
        "BOM": bom_data,
        "General": general_data,
        "Spool": spool_data,
        "Spec": spec_data,
        "Outlier": outlier_data
    }

    total_rows = 0
    available_tables = 0

    for table_name, data in tables.items():
        if data is not None:
            rows = len(data)
            total_rows += rows
            available_tables += 1
            print(f"{table_name} table: {rows} rows")
        else:
            print(f"{table_name} table: Not available")

    print(f"\nTotal available tables: {available_tables}")
    return f"Total rows across all tables: {total_rows}"