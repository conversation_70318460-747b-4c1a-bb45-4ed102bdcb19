from PySide6.QtWidgets import QPushButton, QMenu, QLabel
from PySide6.QtGui import QMovie, QIcon
from PySide6.QtCore import Signal
from pubsub import pub
from src.pyside_util import get_resource_qicon

class TableStatusWidget(QPushButton):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        self.menuActions = {}
        self.jobItems = {}
        self.setText("")
        pub.subscribe(self.onStatus, "set-statusbar-table")
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.setIcon(get_resource_qicon("database.svg"))
        self.setToolTip("Table Data")

    def onStatus(self, data):
        self.sgnUpdateStatus.emit(data)

    def onUpdateStatus(self, data):
        tableType = data["type"]
        params = data["params"]
        msg = params.get(tableType, "") + " - " + params.get("msg")
        msg = params.get("msg")
        self.setText(" " + msg)
        if not msg:
            self.hide()
        else:
            self.show()
