from src.utils.logger import logger
from PySide6.QtCore import QItemSelection
from PySide6.QtGui import *
from PySide6.QtGui import QMouseEvent
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from functools import partial
from pubsub import pub
from os import path
from src.pyside_util import get_resource_qicon, applyDropShadowEffect
from src.app_paths import getExportPath
from importlib.util import find_spec
from __features__ import GET_WELD_COUNT_ENABLED

# logger = logging.getLogger(__name__)

if find_spec("src.views.placeholderview"):
    items = [
        "Create New",
        None,
        "Projects",
        "Notepad",
        "Construction Calculator",
        "Dashboard",
        "Upload Queue",
        None,
        "Document Library",
        "Manage Account",
        "Logout",
    ]
else:
    items = [
        "Create New",
        None,
        "Projects",
        "Upload Queue",
        None,
        "Document Library",
        "Logout",
    ]


class UpdateProjectSourcePopup(QWidget):

    def __init__(self, parent, sourceData):
        super().__init__(parent)
        self._projectData = {}
        self.sourceData = sourceData

        self.setObjectName("popup")

        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(600)
        self.setMinimumHeight(216)

        widget = QWidget()
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())

        self.layout().addWidget(widget)

        p = path.basename(self.sourceData["filename"])
        lblName = QLabel(f"{p}", self)
        lblName.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        lblName.setToolTip(self.sourceData["filename"])
        grid.layout().addWidget(lblName, 1, 0, 1, 1)

        lblName = QLabel("Engineer drafter:", self)
        grid.layout().addWidget(lblName, 2, 0, 1, 1)
        self.engineerDrafterValue = QLineEdit("", self)
        self.engineerDrafterValue.setObjectName("engineerDrafterValue")
        documentVendor = self.sourceData.get("documentVendor")
        if documentVendor:
            self.engineerDrafterValue.setText(documentVendor)
        self.engineerDrafterValue.textChanged.connect(self.onTextChanged)
        grid.layout().addWidget(self.engineerDrafterValue, 3, 0, 1, 1)

        widget.layout().addWidget(grid)

        self.pbUpdate = QPushButton("Update")
        self.pbUpdate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbUpdate.setEnabled(False)
        widget.layout().addWidget(self.pbUpdate)

        self.pbCancel = QPushButton("Cancel")
        self.pbCancel.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbCancel.clicked.connect(self.close)
        widget.layout().addWidget(self.pbCancel)

        self.pbUpdate.clicked.connect(self.onUpdateDetails)

        self.topLevelWidget().windowResized.connect(self.resizeEvent)
        applyDropShadowEffect(self)

    def setSourceData(self, data: dict):
        self.engineerDrafterValue.setText(data.get("documentVendor"))
        if not data.get("documentVendor"):
            self.show()
        self.sourceData = data

    def setErrorMessage(self, text):
        self.lblError.setText(text)

    def onUpdateDetails(self):
        engineerDrafter = self.engineerDrafterValue.text()
        if not engineerDrafter:
            self.pbUpdate.setEnabled(False)
            return
        self.sourceData["documentVendor"] = engineerDrafter

        def callback(res):
            if res["status"] == "ok":
                message = "Project source updated"
                self.sourceData.update(res["data"])
            else:
                message = "Update project source failed"
            QMessageBox.information(self, "Update Project Source", message)
            self.close()

        pub.sendMessage("project-source-update", data=self.sourceData, callback=callback)

    def onTextChanged(self, text):
        self.pbUpdate.setEnabled(True if text else False)

    def focus(self):
        self.engineerDrafterValue.setFocus()

    def resizeEvent(self, event: QResizeEvent) -> None:
        x = (self.topLevelWidget().width() // 2) - (self.width() // 2)
        y = 30
        self.move(QPoint(x, y))
        return super().resizeEvent(event)


class ProjectSubMenu(QListWidget):

    indexSelected = Signal(object)
    def __init__(self, parent, projectId: int):
        super().__init__(parent)
        self.projectId = projectId
        self.setMouseTracking(True)
        applyDropShadowEffect(self)
        self.setSelectionMode(self.SelectionMode.SingleSelection)
        self.setIconSize(QSize(16, 16))

        self.setObjectName("subMenu")

    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        index = self.indexAt(event.position().toPoint())
        self.selectionModel().select(index, self.selectionModel().SelectionFlag.ClearAndSelect)
        self.indexSelected.emit(index)
        return super().mouseMoveEvent(event)


class NavigationSidebar(QScrollArea):

    # Animation vars
    maxProjectHeight = 300

    sgnProjectSourceAdd = Signal(dict)
    sgnSetProjects = Signal(dict)
    def __init__(self):
        super().__init__()
        self._currentProjectId = None

        self.setObjectName("navSidebar")
        self.widgetItems = QWidget(self)
        self.widgetItems.setLayout(QVBoxLayout())
        self.widgetItems.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        # Popups
        self._updateSourcePopup: UpdateProjectSourcePopup = None
        # Cascading Menus
        self.projectTree = ProjectSubMenu(self, None)
        # self.projectTree.setObjectName("ProjectTree")
        self.projectSubmenuA: ProjectSubMenu = None
        self.projectSubmenuB: ProjectSubMenu = None
        self.projectSubmenuC: ProjectSubMenu = None

        self.projectTree.itemActivated.connect(self.onProjectTreeItemActivated)
        self.projectTree.indexSelected.connect(self.onProjectTreeItemSelected)
        self.projectTree.clear()
        self.projectTree.setMaximumHeight(self.maxProjectHeight)
        self.projectTree.setFixedHeight(self.maxProjectHeight)

        self.setLayout(QVBoxLayout())
        self.widgetItems.setObjectName("navSidebar")
        self.btnGroup = [] # QButtonGroup(self)
        for item in items:
            if not item:
                hline = QFrame(self.widgetItems)
                hline.setObjectName("navSidebarSeparater")
                hline.setFrameShape(QFrame.Shape.HLine)
                hline.setMaximumHeight(1)
                self.widgetItems.layout().addWidget(hline)
                continue
            pb = QPushButton(item)
            if item == "Create New":
                pb.setIcon(get_resource_qicon("file-plus.svg"))
            pb.setText(f" {item} ")
            pb.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
            pb.setFixedHeight(32)
            pb.setObjectName("navSidebarButton")
            pb.setCheckable(True)
            pb.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            pb.clicked.connect(partial(self.onNavigationButton, pb, item))
            self.widgetItems.layout().addWidget(pb)
            if item == "Projects":
                self.pbProjects = pb
                self.widgetItems.layout().addWidget(self.projectTree)
            self.btnGroup.append(pb)

        stretchSpacer = QWidget(self.widgetItems)
        stretchSpacer.setObjectName("navSidebar")
        stretchSpacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.widgetItems.layout().addWidget(stretchSpacer)
        self.setWidget(self.widgetItems)
        self.updateProjectsIcon()
        self.initAnimation()

        # Hide projects
        self.hideProjects()

        pub.subscribe(self.onSetWorkspaceData, "set-workspace-data")
        self.sgnSetProjects.connect(self.onSetProjects)

        self.verticalScrollBar().actionTriggered.connect(self.hideProjectSubmenus)

        pub.subscribe(self.onProjectSourceAddResponse, "project-source-add-response")
        pub.subscribe(self.logoutCleanup, "logout")
        pub.subscribe(self.onCloseProject, "project-close")
        pub.subscribe(self.onSyncProjectData, "sync-project-data")

        self.pbProjects.setChecked(True) # Opens up the tree by default
        self.onProjectsChecked()

        self.sgnProjectSourceAdd.connect(self.onProjectSourceAdd)

    @property
    def currentProjectId(self):
        return self._currentProjectId

    @currentProjectId.setter
    def currentProjectId(self, value):
        self._currentProjectId = value
        item: QListWidgetItem = None
        for row in range(self.projectTree.count()):
            item = self.projectTree.item(row)
            projectData = item.data(Qt.ItemDataRole.UserRole)
            font = item.font()
            font.setBold(projectData["id"] == value)
            item.setFont(font)
            item.setForeground(QColor("#F7941D") if projectData["id"] == value else QColor("#FFFFFF"))

    def initAnimation(self):
        self.animationDuration = 250  # Duration in milliseconds
        self.animation = QVariantAnimation(self.projectTree)
        self.animation.setDuration(self.animationDuration)
        self.animation.setStartValue(0)
        self.animation.setEndValue(self.maxProjectHeight)
        self.animation.valueChanged.connect(self.projectTree.setFixedHeight)

    def hideProjects(self):
        self.projectTree.setFixedHeight(0)

    def onNavigationButton(self, btn: QPushButton, name: str):
        logger.info(f"Navigation activated: {name}")
        self.hideProjectSubmenus()
        for b in self.btnGroup:
            b.setChecked(btn == b)
        if name == "Logout":
            res = QMessageBox.question(self,
                    "Confirm logout?",
                    "Any unsaved changes will be lost. Logout?                 ",
                    buttons=QMessageBox.Yes | QMessageBox.No)
            if res == QMessageBox.No:
                return
            pub.sendMessage("request-logout")
        elif name == "Projects":
            self.onProjectsChecked()
        elif name == "Create New":
            pub.sendMessage("goto-form", name="NewProjectExplorerForm")
        else:
            pub.sendMessage("goto-workspace-view", name=name)

    def onProjectsChecked(self):
        """Animate enlarge the project tree when selected and vice versa"""
        if self.projectTree.height() > 0: # A proxy for visibility
            self.pbProjects.setChecked(False)
        if self.pbProjects.isChecked():
            self.animation.start()
        else:
            self.hideProjects()

        self.updateProjectsIcon()

    def updateProjectsIcon(self):
        if self.pbProjects.isChecked():
            icon = "chevron-down"
        else:
            icon = "chevron-right"
        self.pbProjects.setIcon(get_resource_qicon(f"{icon}.svg"))
        self.pbProjects.updateGeometry()

    def onSetWorkspaceData(self, data):
        """Sets the projects tree with users' projects"""
        self.sgnSetProjects.emit(data)

    def onSetProjects(self, data):
        projects = data["projects"]
        projectId = self.currentProjectId
        self.projectTree.clear()
        for projectData in projects:
            item = QListWidgetItem(projectData["projectName"])
            item.setIcon(get_resource_qicon("folder.svg"))
            item.setData(Qt.ItemDataRole.UserRole, projectData)
            self.projectTree.addItem(item)
            font = item.font()
            font.setBold(projectId == projectData["id"])
            item.setFont(font)
            item.setForeground(QColor("#F7941D") if projectId == projectData["id"] else QColor("#FFFFFF"))
            if projectId == projectData["id"]:
                title = f"{projectData['projectName']} - id: {projectData['id']}"
                self.topLevelWidget().setWindowTitle(title)

    def onProjectTreeItemSelected(self, index: QModelIndex):
        projectData = self.getSelectedProjectData()
        if not projectData:
            self.hideProjectSubmenus()
            return
        menu = QMenu(None)
        sub = QMenu(menu)
        sub.setTitle("Test")
        menu.addMenu(sub)

        item: QListWidgetItem = self.projectTree.item(index.row())

        if self.projectSubmenuA:
            if self.projectSubmenuA.projectId == projectData["id"]:
                return

        self.hideProjectSubmenus()
        self.projectSubmenuA = ProjectSubMenu(self.topLevelWidget(), projectData["id"])
        self.projectSubmenuA.indexSelected.connect(self.onProjectSubMenuASelected)
        self.projectSubmenuA.clear()
        self.projectSubmenuA.show()
        self.projectSubmenuA.setFixedWidth(220)

        itemRect = self.projectTree.visualItemRect(item)
        menuHeight = 0
        y = self.projectTree.mapTo(self.topLevelWidget(), QPoint(0, 0)).y() + itemRect.y()
        self.projectSubmenuA.move(QPoint(self.projectTree.x()+self.projectTree.width(), y))
        self.projectSubmenuA.raise_()
        self.projectSubmenuA.setIconSize(QSize(16, 16))
        for label, icon in [
            ("Manage", "settings.svg"),
            ("Documents", "file-text.svg"),
            ("MTO", "file.svg"),
            ("Source", "file.svg")]:
            item = QListWidgetItem(label)
            item.setData(Qt.ItemDataRole.UserRole, label)
            item.setIcon(get_resource_qicon(icon))
            self.projectSubmenuA.addItem(item)
            # menuHeight += self.projectSubmenuA.visualItemRect(item).height()
            menuHeight = self.projectSubmenuA.visualItemRect(item).y() + self.projectSubmenuA.visualItemRect(item).height()

        self.projectSubmenuA.setMinimumHeight(menuHeight + 12)

    def onProjectTreeItemActivated(self, item: QListWidgetItem):
        projectData = self.getSelectedProjectData()
        if not projectData:
            return
        self.hideProjectSubmenus()
        if self.confirmOpenProject(projectId=projectData["id"]):
            pub.sendMessage("open-project", data=projectData)

    def hideProjectSubmenus(self, hideFrom: int = None):
        """Hide all sub menus or optionally hide menus on and after hideFrom menu"""
        submenu: QListWidget = None
        menus = [self.projectSubmenuA, self.projectSubmenuB, self.projectSubmenuC]
        for n, submenu in enumerate(menus):
            if hideFrom is not None and n < hideFrom:
                continue
            try:
                submenu.blockSignals(True)
                submenu.hide()
            except Exception as e:
                pass
            self.projectSubmenuA = None if self.projectSubmenuA == submenu else self.projectSubmenuA
            self.projectSubmenuB = None if self.projectSubmenuB == submenu else self.projectSubmenuB
            self.projectSubmenuC = None if self.projectSubmenuC == submenu else self.projectSubmenuC

    def onProjectSubMenuASelected(self, index: QModelIndex):
        item = self.projectSubmenuA.item(index.row())
        data = index.data(Qt.ItemDataRole.UserRole)
        projectData = self.getSelectedProjectData()
        self.hideProjectSubmenus(hideFrom=1)
        if data == "Source":
            self.projectSubmenuB = ProjectSubMenu(self.topLevelWidget(), self.projectSubmenuA.projectId)
            self.projectSubmenuB.setFixedWidth(320)
            self.projectSubmenuB.setMinimumHeight(80)
            itemRect = self.projectSubmenuA.visualItemRect(item)
            y = self.projectSubmenuA.y() + itemRect.y()
            self.projectSubmenuB.raise_()

            sources = projectData["documents"]
            menuHeight = 0
            if not sources:
                self.projectSubmenuB.setFixedWidth(190)
                item = QListWidgetItem("  <<No Sources Added>>  ")
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsSelectable)
                self.projectSubmenuB.addItem(item)
            else:
                sources = sorted(sources, key=lambda d: path.basename(d['filename']).lower())
                self.projectSubmenuB.indexSelected.connect(self.onProjectSubMenuBSelected)
                self.projectSubmenuB.itemClicked.connect(self.onProjectSourceClicked)
                for document in sources:
                    document["id"] = projectData["id"]
                    filename = document["filename"]
                    display = path.basename(filename)
                    item = QListWidgetItem(display)
                    item.setIcon(get_resource_qicon("file.svg"))
                    item.setData(Qt.ItemDataRole.UserRole, document)
                    self.projectSubmenuB.addItem(item)
                    # menuHeight += self.projectSubmenuB.visualItemRect(item).y()
                    menuHeight = self.projectSubmenuB.visualItemRect(item).y() + self.projectSubmenuB.visualItemRect(item).height()

            self.projectSubmenuB.setFixedWidth(1.1*self.projectSubmenuB.sizeHintForColumn(0))
            menuHeight = self.projectSubmenuB.sizeHintForRow(0) * self.projectSubmenuB.count() + 2 * self.projectSubmenuB.frameWidth()
            self.projectSubmenuB.setFixedHeight(menuHeight)

        elif data == "Manage":
            self.projectSubmenuB = ProjectSubMenu(self.topLevelWidget(), self.projectSubmenuA.projectId)
            self.projectSubmenuB.setFixedWidth(190)
            itemRect = self.projectSubmenuA.visualItemRect(item)
            y = self.projectSubmenuA.y() + itemRect.y()
            self.projectSubmenuB.raise_()
            self.projectSubmenuB.itemClicked.connect(self.onProjectManageMenuClicked)

            menuHeight = 0
            for action, icon in [
                    ("Open Project", "folder.svg"),
                    ("Edit Project", "edit.svg"),
                    ("Add Source", "plus-white.svg"),
                    ("Delete Project", "tableview-minus.svg"),
                ]:
                display = action
                item = QListWidgetItem(display)
                item.setIcon(get_resource_qicon(icon))
                item.setData(Qt.ItemDataRole.UserRole, action)
                self.projectSubmenuB.addItem(item)
                menuHeight += self.projectSubmenuB.visualItemRect(item).height()
                menuHeight = self.projectSubmenuB.visualItemRect(item).y() + self.projectSubmenuB.visualItemRect(item).height()
            self.projectSubmenuB.setFixedHeight(menuHeight)
            # self.projectSubmenuB.setFixedHeight(96-16)

        if self.projectSubmenuB:
            self.projectSubmenuB.setFixedWidth(1.1*self.projectSubmenuB.sizeHintForColumn(0))
            menuHeight = self.projectSubmenuB.sizeHintForRow(0) * self.projectSubmenuB.count() + 2 * self.projectSubmenuB.frameWidth()
            self.projectSubmenuB.setFixedHeight(menuHeight)
            self.projectSubmenuB.show()
            self.projectSubmenuB.move(QPoint(self.projectSubmenuA.x()+self.projectSubmenuA.width(), y))

    def getSelectedProjectData(self) -> dict:
        """Return the currently selected project data"""
        try:
            index = self.projectTree.selectedIndexes()[0]
            if not index.isValid():
                return None
        except IndexError:
            return None
        return self.projectTree.item(index.row()).data(Qt.ItemDataRole.UserRole)

    def getSelectedSourceData(self) -> dict:
        """Return the currently selected project source data"""
        try:
            index = self.projectSubmenuB.selectedIndexes()[0]
            if not index.isValid():
                return None
        except IndexError:
            return None
        return self.projectSubmenuB.item(index.row()).data(Qt.ItemDataRole.UserRole)

    def onProjectSubMenuBSelected(self, index: QModelIndex):
        projectData = self.getSelectedProjectData()
        if not projectData:
            return
        self.hideProjectSubmenus(hideFrom=2)
        if not index.isValid():
            return
        self.projectSubmenuC = ProjectSubMenu(self.topLevelWidget(), projectData["id"])
        self.projectSubmenuC.show()
        self.projectSubmenuC.setFixedWidth(400)
        self.projectSubmenuC.itemClicked.connect(self.onProjectAction)
        self.projectSubmenuC.clear()

        menuHeight = 0
        for action, icon in [
                ("Open Source", "folder.svg"),
                ("Update Source", "edit.svg"),
                ("Remove Source", "tableview-minus.svg"),
                ("Get Weld Count", "settings.svg"),
                ("Get Weld Count (10 Pages)", "settings.svg")
            ]:
            if not GET_WELD_COUNT_ENABLED and action.startswith("Get Weld Count"):
                continue
            item = QListWidgetItem(action)
            item.setIcon(get_resource_qicon(icon))
            self.projectSubmenuC.addItem(item)
            menuHeight += self.projectSubmenuC.visualItemRect(item).height()

        self.projectSubmenuC.setFixedHeight(menuHeight)
        itemRect = self.projectSubmenuB.visualItemRect(self.projectSubmenuB.item(index.row()))
        y = self.projectSubmenuB.y() + itemRect.y()

        self.projectSubmenuC.setFixedWidth(1.1*self.projectSubmenuC.sizeHintForColumn(0))
        menuHeight = self.projectSubmenuC.sizeHintForRow(0) * self.projectSubmenuC.count() + 2 * self.projectSubmenuC.frameWidth()
        self.projectSubmenuC.setFixedHeight(menuHeight)
        self.projectSubmenuC.move(QPoint(self.projectSubmenuB.x() + self.projectSubmenuB.width(), y))
        self.projectSubmenuC.raise_()

    def confirmOpenProject(self, projectId: int) -> bool:
        """Ask for confirmation if currently opened project is different to to be opened"""
        if self.currentProjectId is None: # No project opened, we permit open
            self.currentProjectId = projectId
            return True

        if self.currentProjectId is None:
            return False # Safety check

        if self.currentProjectId != projectId:
            res = QMessageBox.question(self,
                                "Confirm open project?",
                                f"Any unsaved changes will be lost. Change project?                 ",
                                buttons=QMessageBox.Yes | QMessageBox.Cancel)
            if res != QMessageBox.Yes:
                return False
            self.currentProjectId = projectId
            return True
        else:
            # Same id, already open
            return True

    def onProjectSourceClicked(self, item: QListWidgetItem):
        """Default action is to open source when clicked"""
        sourceData = item.data(Qt.ItemDataRole.UserRole)
        projectData = self.getSelectedProjectData()
        if self.confirmOpenProject(projectId=projectData["id"]):
            pub.sendMessage("open-project", data=projectData)
            pub.sendMessage("blueprintreader-load-pdf", data=sourceData)
            self.openProjectSource(sourceData)

    def onProjectAction(self, item: QListWidgetItem):
        action = item.data(Qt.ItemDataRole.DisplayRole)
        sourceData = self.getSelectedSourceData()
        if action == "Open Source":
            self.openProjectSource(sourceData)
        elif action == "Update Source":
            self.updateProjectSource(sourceData)
        elif action == "Remove Source":
            self.removeProjectSource(sourceData)
        elif action == "Get Weld Count":
            self.getSourceWeldCount(sourceData)
        elif action == "Get Weld Count (10 Pages)":
            self.getSourceWeldCount(sourceData, pages=10)

    def openProjectSource(self, sourceData):
        projectData = self.getSelectedProjectData()
        self.hideProjectSubmenus()
        if self.confirmOpenProject(projectId=projectData["id"]):
            pub.sendMessage("open-project", data=projectData)
        else:
            return

        pub.sendMessage("blueprintreader-load-pdf", data=sourceData)

    def updateProjectSource(self, sourceData):

        def close(event):
            self._updateSourcePopup = None

        if self._updateSourcePopup:
            self._updateSourcePopup.close()

        self.hideProjectSubmenus()
        self._updateSourcePopup = UpdateProjectSourcePopup(self.topLevelWidget(), sourceData=sourceData)
        self._updateSourcePopup.closeEvent = close
        self._updateSourcePopup.show()

    def removeProjectSource(self, sourceData):
        projectId = sourceData["id"]
        filename = sourceData["filename"]
        ret = QMessageBox.question(self,
                                   'Remove source file',
                                   f"Confirm remove source: {path.basename(filename)}?",
                                   QMessageBox.Yes | QMessageBox.No)

        def callback(res: dict):
            return
            if res["status"] == "ok":
                item.parent().removeChild(item)
                QMessageBox.information(self,
                                        "Project Source Deleted",
                                        "Project Source successfully deleted")
            else:
                QMessageBox.information(self,
                                        "Delete Project Source Failed",
                                        "Project Source could not be deleted")

        if ret == QMessageBox.Yes:
            pub.sendMessage("project-source-delete",
                            projectId=projectId,
                            filename=filename,
                            callback=callback)
            self.hideProjectSubmenus(hideFrom=1)

    def getSourceWeldCount(self, sourceData, pages=None):
        filename = sourceData["filename"]
        ret = QMessageBox.question(self,
                                   'Get Weld Count',
                                   f"Get source weld count: {path.basename(filename)}?",
                                   QMessageBox.Yes | QMessageBox.No)

        def callback(res: dict):
            if res["status"] == "ok":
                baseDir = getExportPath()
                fn = f'weld_counts.xlsx'
                fn2 = f'weld_counts2.xlsx'
                exportPath = path.join(baseDir, fn)
                exportPath2 = path.join(baseDir, fn2)
                df_weld_counts = res["data"]

                import pandas as pd
                print(type(df_weld_counts))
                try:
                    df_weld_counts[0].to_excel(exportPath, index=False)
                    df_weld_counts[1].to_excel(exportPath2, index=False)
                except:
                    pass

                try:
                    pd.DataFrame(df_weld_counts).to_excel(exportPath, index=False)
                except:
                    pass


                baseDirUrl = QUrl.fromLocalFile(baseDir).toString()
                exportPathUrl = QUrl.fromLocalFile(exportPath).toString()
                msg = f"Exported Weld count data to <a href='{baseDirUrl}'>{baseDir}</a>"
                msg += f"<br><br><a href='{exportPathUrl}'>{fn}</a>"
                pub.sendMessage("show-app-messagebox", msg=msg, title=f"Saved Weld count data for {res['filename']}")
            else:
                pub.sendMessage("show-app-messagebox", msg=res["error"], title=f"Weld count failed {res['filename']}")
                print("Failed weld count request", res["error"])

        if ret == QMessageBox.Yes:
            self.hideProjectSubmenus()
            pub.sendMessage("get-source-weld-count",
                            filename=filename,
                            callback=callback,
                            pages=pages)

    def hideEvent(self, event: QHideEvent) -> None:
        self.hideProjectSubmenus()
        return super().hideEvent(event)

    def onProjectManageMenuClicked(self, item: QListWidgetItem):
        action = item.data(Qt.ItemDataRole.DisplayRole)
        projectData = self.getSelectedProjectData()
        self.hideProjectSubmenus()
        if action == "Open Project":
            if self.confirmOpenProject(projectId=projectData["id"]):
                pub.sendMessage("open-project", data=projectData)
        elif action == "Edit Project":
            pub.sendMessage("goto-form", name="UpdateProjectForm", params={"data": projectData})
        elif action == "Delete Project":
            self.deleteProject(projectData)
        elif action == "Add Source":
            self.addProjectSource(projectData)

    def deleteProject(self, projectData):
        self.hideProjectSubmenus()
        projectId = projectData["id"]
        projectName = projectData["projectName"]
        ret = QMessageBox.question(self,'Delete project',
                                   f"Confirm delete project: {projectName}?",
                                   QMessageBox.Yes | QMessageBox.No)

        def callback(res: dict):
            logger.info(f"Delete project response: {projectData}")
            if res["status"] == "ok":
                pub.sendMessage("project-close", projectId=projectId)
                self.currentProjectId = None
                QMessageBox.information(self, 'Project Deleted', 'The project has been successfully deleted.')
            else:
                QMessageBox.information(self, 'Delete Project Failed', 'The project could not be deleted.')

        if ret == QMessageBox.Yes:
            pub.sendMessage("project-delete", projectId=projectId, callback=callback)

    def logoutCleanup(self):
        self.projectTree.clear()
        self.currentProjectId = None
        if self._updateSourcePopup:
            self._updateSourcePopup.close()

    def onProjectSourceAddResponse(self, data):
        self.sgnProjectSourceAdd.emit(data)

    def onCloseProject(self, projectId):
        if projectId == self.currentProjectId:
            self.currentProjectId = None

    def onProjectSourceAdd(self, data):
        if data["ok"] is False:
            return
        projectId = data["projectId"]
        for row in range(self.projectTree.count()):
            item = self.projectTree.item(row)
            projectData = item.data(Qt.ItemDataRole.UserRole)
            if projectData["id"] == projectId:
                projectData["documents"] = data["documents"]
                item.setData(Qt.ItemDataRole.UserRole, projectData)
                break

    def addProjectSource(self, projectData: dict):
        """ User chooses a PDF """
        projectId = projectData.get("id")
        if not projectId:
            return
        filename, _ = QFileDialog.getOpenFileName(self,
                                                  "Open PDF File",
                                                  "",
                                                  "PDF Files (*.pdf);;All Files (*)")
        if not filename:
            return
        for document in projectData.get("documents", []):
            if filename in document.get("filename", ""):
                msgBox = QMessageBox()
                msgBox.setWindowTitle("Already Added")
                msgBox.setIcon(QMessageBox.Critical)
                msgBox.setText("Document has already been added")
                msgBox.exec()
                return
        pub.sendMessage("project-source-add", data = {"projectId": projectId, "filename": filename})

    def onSyncProjectData(self, projectId: int, key: str, value: any):
        if key == "documents":
            assert type(value) == list
        for row in range(self.projectTree.count()):
            item = self.projectTree.item(row)
            projectData = item.data(Qt.ItemDataRole.UserRole)
            if projectData["id"] == projectId:
                projectData[key] = value
                item.setData(Qt.ItemDataRole.UserRole, projectData)
                break