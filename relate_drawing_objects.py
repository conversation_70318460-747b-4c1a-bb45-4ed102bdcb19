

'''
Determine Line Segments
Tasks:
- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can ensure isometric items are detected prior to running extraction
A. Classify objects
    1. Identify Isometric Sketch area
        - Filter vectors to identify possible vector lines
        Criteria: Isometrics are drawn at the angles: Horizontal [30, 210], [330, 150], Vertical: [90, 270] 
        Considerations: Handle odd angle segments that do not follow the usual angeles. Need a sample to identify the cosine area
            - Link similar conditions, i.e line width     
    
    2. Identify BOM label boxes:
        -Criteria: Small Square containing a number, series of numbers, or letter & number where a number corresponds back to a BOM Item
        - Vertical[90, 270], Actual Left Right [0, 180]
        
    3. Identify pointer lines
        - Criteria: Lines that touch a known BOM label item and intersect or almost intersect a portion of the isometric. 
        
    4. Link isometric segments, pointer lines, and BOM label
    
    5. Identify point of size change:
        - Look for a measurement using regex expressions to manually separate a size change point
        - Look for reducer symbols and pipe tees for hints

        
    Current implementation logic:
    
    
    Colors:
    Green: (0, 1, 0)
    Red: (1, 0, 0)
    Blue: (0, 0, 1)
    Yellow: (1, 1, 0)
    Cyan: (0, 1, 1)
    Magenta: (1, 0, 1)
    Orange: (1, 0.5, 0)
    Purple: (0.5, 0, 0.5)
    Pink: (1, 0.75, 0.8)
    Lime: (0.5, 1, 0)
    Teal: (0, 0.5, 0.5)
    Brown: (0.6, 0.3, 0)
    Navy: (0, 0, 0.5)
    Olive: (0.5, 0.5, 0)
    Maroon: (0.5, 0, 0)
        
'''

'''
Workflow logic:
Outstanding:
- Number all classified items internally for linking

- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can 
  ensure isometric items are detected prior to running extraction
  
- Get continuation piping lines not detected by the initital isometric detection

- Draw anything in the isometric gaps/path and label as "Pipe Component". 

- Detect Squares (Circles for some drawings)

- Get arrows and vertices

- Need pointer origin and destination stored in data for grouping

- Draw "Other" with numbers for debugging

- Use BOM item #'s for hints

- Identify measurements, size callouts, and reduced size callouts (1"x1 1/2"). Can be done with regex. Dual sizes used to create iso size breaks

- Identify measurement breaks (Perpendicular Lines signifying a specific measurment of an item, like a valve)

- Identify insulation dashes

- Identify revision triangles and clouds. Need to research this.

- Identify Weep holes (Regex pattern or key word pickout. Research alternate terms to look for)

- Handle case where the break in isometric is because the line is running behind another pipe segment and is not really a break (Page 4 of "Weld Recognition Test.pdf)


* Purpose: Isolate piping components like pipe line segments, measurement lines, pointers, etc., to assist in relating items to their actual location on the isometric drawing.
* Input: PDF file containing isometric piping drawings.
* Output: 
  1. A new PDF file with color-coded line segments.
  2. An Excel file with detailed information about each line segment.

Steps:
1. Open the PDF and extract vector graphics (drawings) from each page. (Function: process_drawings)
2. For each line in the drawings: (Function: process_drawings)
   a. Calculate its angle and length. (Functions: calculate_angle, LineString.length)
   b. Determine if it's an isometric angle. (Function: is_isometric_angle)
   c. Store its properties (coordinates, width, etc.).
3. Determine the two most significant line widths on each page. (Function: determine_significant_widths)
   - Purpose: Identify potential pipe and measurement line widths.
4. Classify lines based on width and isometric property: (Function: process_drawings)
   a. Thicker isometric lines are classified as 'Pipe'.
   b. Other lines are initially classified as 'Other'.
5. Reclassify 'Other' lines: (Function: process_drawings)
   a. Check if they're parallel to pipes. (Function: is_parallel_to_pipe)
      If yes, classify as 'Measurement'.
   b. Check if they're pointing to pipes. (Function: is_pointer_line)
      If yes, classify as 'Pointer'.
6. Generate output PDF with color-coded lines and Excel file with line data. (Functions: output_isometric_lines_to_pdf, DataFrame.to_excel)

Key Functions:
- calculate_angle: Computes the angle of a line.
- is_isometric_angle: Checks if an angle is close to standard isometric angles.
- determine_significant_widths: Finds the two most common line widths for pipes and measurements.
- is_parallel_to_pipe: Checks if a line runs parallel to a pipe.
- is_pointer_line: Identifies lines pointing to pipes (likely annotations or labels).
- process_drawings: Main function that orchestrates the entire workflow.
- output_isometric_lines_to_pdf: Generates the color-coded PDF output.
- main: Calls process_drawings and handles the overall execution flow.

Adjustable Parameters:
- angle_tolerance (in is_isometric_angle): Tolerance for considering an angle as isometric. 
  Increasing it will classify more lines as isometric, potentially including non-standard angles.
- distance_threshold (in is_pointer_line): Maximum distance for a line to be considered close to a pipe. 
  Increasing it will detect pointers that are further from pipes, but may increase false positives.
- min_length (in process_drawings): Minimum length for a line to be considered. 
  Decreasing it will include shorter lines, potentially increasing noise in the detection.
- tolerance (in determine_significant_widths): Tolerance for grouping similar line widths. 
  Increasing it will group more varied line widths together, potentially misclassifying some lines.

Color Coding in Output PDF:
- Red: Pipe lines
- Brown: Measurement lines
- Blue: Pointer lines
- Orange: Other unclassified lines

Note: Adjusting these parameters may require fine-tuning based on the specific characteristics of the input drawings.
'''

import fitz, os, math
from fitz.utils import getColor
import pandas as pd
import numpy as np
from shapely.geometry import LineString
from shapely.geometry import Point



def get_finish_params(path, color=None):
    if color is None:
        c = path.get("color")
    elif isinstance(color, tuple):
        c = color  # If color is already a tuple, use it directly
    else:
        c = getColor(color)  # This will handle string color names
    
    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),
        "fill": path.get("fill", None),
        "color": c,
        "dashes": path["dashes"] if path.get("dashes") is not None else None,
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
        "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,
        "width": path["width"] if path.get("width") is not None else None
    }

def draw_path(shape, path):
    for item in path["items"]:
        if item[0] == "l":  # line
            shape.draw_line(item[1], item[2])
        elif item[0] == "re":  # rectangle
            shape.draw_rect(item[1])
        elif item[0] == "qu":  # quad
            shape.draw_quad(item[1])
        elif item[0] == "c":  # curve
            shape.draw_bezier(item[1], item[2], item[3], item[4])
        else:
            print(f"Unhandled path type: {item[0]}")
            
def output_isometric_lines_to_pdf(df, input_filepath, output_filepath):
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")
        
        # Create a new page with the same dimensions as the input PDF
        input_page = doc[page_num - 1]  # Subtract 1 because page numbers in df start from 1
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
        shape = outpage.new_shape()

        # Draw all lines for this page
        for _, line in group.iterrows():
            start = fitz.Point(line['start_x'], line['start_y'])
            end = fitz.Point(line['end_x'], line['end_y'])
            
            # Set color based on classification
            if line['det_cat_1'] == 'Pipe':
                color = (1, 0, 0) # Red
                
                #color = line['drawing'].get('color')  # Original color
                
            elif line['det_cat_1'] == 'Measurement':
                color = (0.6, 0.3, 0) # Brown
                
            elif line['det_cat_1'] == 'Pointer':
                color = (0, 0, 1)  # Blue
            else:
                color = (1, 0.5, 0) # Orange
            
            shape.draw_line(start, end)
            finish_params = get_finish_params(line['drawing'])
            finish_params['color'] = color
            shape.finish(**finish_params)

        shape.commit()

    print(f"Saving PDF to {output_filepath}")
    outpdf.save(output_filepath)
    print("PDF saved. Closing files...")
    outpdf.close()
    doc.close()
    print("PDF output process completed.")

def is_within_area(start, end, area):
    """Check if a line is within the specified area."""
    return (area['x0'] <= start.x <= area['x1'] and
            area['y0'] <= start.y <= area['y1'] and
            area['x0'] <= end.x <= area['x1'] and
            area['y0'] <= end.y <= area['y1'])

def calculate_angle(line):
    start, end = line.coords
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]

    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)

def is_pointer_line(line, pipe_lines, angle_tolerance=20, distance_threshold=10, debug_func=False):
    '''
    Identifies "pointers", which points from descriptors/labels to items on the pipe.
    '''
    line_angle = line['angle']
    start_point = Point(line['start_x'], line['start_y'])
    end_point = Point(line['end_x'], line['end_y'])
    
    for pipe_line in pipe_lines:
        pipe_angle = pipe_line['angle']
        pipe_start = Point(pipe_line['start_x'], pipe_line['start_y'])
        pipe_end = Point(pipe_line['end_x'], pipe_line['end_y'])
        
        # Check if one end of the line is close to the pipe
        start_to_pipe_start = start_point.distance(pipe_start)
        start_to_pipe_end = start_point.distance(pipe_end)
        end_to_pipe_start = end_point.distance(pipe_start)
        end_to_pipe_end = end_point.distance(pipe_end)
        
        if (start_to_pipe_start <= distance_threshold or
            start_to_pipe_end <= distance_threshold or
            end_to_pipe_start <= distance_threshold or
            end_to_pipe_end <= distance_threshold):
            
            if debug_func:
                print(f"Line close to pipe. Distances: {start_to_pipe_start}, {start_to_pipe_end}, {end_to_pipe_start}, {end_to_pipe_end}")
            
            # Check if the line angle is significantly different from the pipe angle
            angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
            if debug_func:
                print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Angle difference: {angle_diff}")
            
            if angle_diff > angle_tolerance:
                if debug_func:
                    print("Pointer line detected!")
                return True
            else:
                if debug_func:
                    print("Line close to pipe but angle difference too small.")
    
    return False

def is_parallel_to_pipe(line, pipe_lines, angle_tolerance=5, distance_threshold=100, min_parallel_ratio=0.5, debug_func=False):
    line_angle = line['angle']
    line_geom = line['geometry']
    
    for pipe in pipe_lines:
        pipe_angle = pipe['angle']
        pipe_geom = pipe['geometry']
        
        # Check if angles are parallel (considering the isometric nature)
        angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
        if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
            # Check if the line runs alongside the pipe
            pipe_buffer = pipe_geom.buffer(distance_threshold)
            parallel_part = line_geom.intersection(pipe_buffer)
            parallel_ratio = parallel_part.length / line_geom.length
            if debug_func:
                print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Parallel ratio: {parallel_ratio}")
            if parallel_ratio >= min_parallel_ratio:
                return True
    
    return False

def determine_significant_widths(df, tolerance=0.01):
    '''
    Considers only lines determined to be isometric angles to find the line_widths of the largest lengths. Narrows down to the top 2 sizes. The larger size is assumed as the isometric pipe
    '''
    # Filter out lines with width 0 or None, and only consider isometric lines
    valid_widths = df[(df['line_width'].notna()) & (df['line_width'] > 0) & (df['is_isometric'] == True)]
    
    # Group by line width and sum the lengths
    width_groups = valid_widths.groupby('line_width')['length'].sum().sort_values(ascending=False)
    
    # Find the two most significant widths
    significant_widths = []
    for width, _ in width_groups.items():
        if not significant_widths or abs(width - significant_widths[0]) > tolerance:
            significant_widths.append(width)
            if len(significant_widths) == 2:
                break
    
    return tuple(significant_widths) if len(significant_widths) == 2 else (None, None)

def find_connected_isometric_lines(df):
    isometric_lines = df[df['is_isometric']]
    connected_groups = []
    processed = set()

    def find_connections(line_index):
        group = [line_index]
        line = isometric_lines.loc[line_index]
        start_point = Point(line['start_x'], line['start_y'])
        end_point = Point(line['end_x'], line['end_y'])
        
        for idx, next_line in isometric_lines.iterrows():
            if idx != line_index and idx not in processed:
                next_start = Point(next_line['start_x'], next_line['start_y'])
                next_end = Point(next_line['end_x'], next_line['end_y'])
                
                if (start_point.distance(next_start) < 1 or 
                    start_point.distance(next_end) < 1 or 
                    end_point.distance(next_start) < 1 or 
                    end_point.distance(next_end) < 1):
                    processed.add(idx)
                    group.extend(find_connections(idx))
        
        return group

    for idx in isometric_lines.index:
        if idx not in processed:
            processed.add(idx)
            connected_groups.append(find_connections(idx))

    return connected_groups

def identify_pipe_continuations(page_lines, pipe_width, tolerance=0.01):
    pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
    potential_continuations = [line for line in page_lines if line['det_cat_1'] == 'Other' and abs(line['line_width'] - pipe_width) < tolerance]
    
    def lines_are_collinear(line1, line2):
        # Check if two lines are collinear (on the same path)
        angle1 = line1['angle']
        angle2 = line2['angle']
        return abs(angle1 - angle2) < 5 or abs(abs(angle1 - angle2) - 180) < 5

    def is_potential_elbow(line, pipe1, pipe2):
        # Check if a line could be an elbow connecting two pipe segments
        return (
            (line['start_x'] == pipe1['end_x'] and line['start_y'] == pipe1['end_y'] and
             line['end_x'] == pipe2['start_x'] and line['end_y'] == pipe2['start_y']) or
            (line['start_x'] == pipe2['end_x'] and line['start_y'] == pipe2['end_y'] and
             line['end_x'] == pipe1['start_x'] and line['end_y'] == pipe1['start_y'])
        )

    for potential in potential_continuations:
        # Check if the potential continuation is collinear with any existing pipe segment
        if any(lines_are_collinear(potential, pipe) for pipe in pipe_lines):
            potential['det_cat_1'] = 'Pipe'
            potential['det_cat_2'] = 'Continuation'
            continue

        # Check if the potential continuation could be an elbow
        for i, pipe1 in enumerate(pipe_lines):
            for pipe2 in pipe_lines[i+1:]:
                if is_potential_elbow(potential, pipe1, pipe2):
                    potential['det_cat_1'] = 'Pipe'
                    potential['det_cat_2'] = 'Elbow'
                    break
            if potential['det_cat_1'] == 'Pipe':
                break

    return page_lines

def is_connected(line1, line2, tolerance=5):
    """Check if two lines are connected within a small tolerance."""
    line1_points = [Point(line1['start_x'], line1['start_y']), Point(line1['end_x'], line1['end_y'])]
    line2_points = [Point(line2['start_x'], line2['start_y']), Point(line2['end_x'], line2['end_y'])]
    
    for p1 in line1_points:
        for p2 in line2_points:
            if p1.distance(p2) <= tolerance:
                return True
    return False

def process_drawings(filepath, pages_to_process=None, min_length=.03, isometric_area_coords=None):
    doc = fitz.open(filepath)
    
    if pages_to_process is None:
        pages_to_process = range(len(doc))
    else:
        pages_to_process = [p - 1 for p in pages_to_process if 0 < p <= len(doc)]

    all_lines = []
    pointer_count = 0

    for page_num in pages_to_process:
        page = doc[page_num]
        drawings = page.get_drawings()
        print(f"Page {page_num + 1}: Found {len(drawings)} drawings")

        page_lines = []
        for drawing in drawings:
            for item in drawing['items']:
                if item[0] == 'l':  # line
                    start = item[1]
                    end = item[2]
                    
                    # Check if the line is within the isometric drawing area
                    if isometric_area_coords and not is_within_area(start, end, isometric_area_coords):
                        continue

                    line = LineString([(start.x, start.y), (end.x, end.y)])
                    angle = calculate_angle(line)
                    length = line.length
                    is_iso = is_isometric_angle(angle)
                    line_width = drawing.get('width', 0)
                    
                    # Include all lines, not just isometric ones
                    if length >= min_length and line_width is not None and line_width > 0:
                    #if line_width is not None:
                        page_lines.append({
                            'page': page_num + 1,
                            'geometry': line,
                            'angle': angle,
                            'length': length,
                            'is_isometric': is_iso,
                            'start_x': start.x,
                            'start_y': start.y,
                            'end_x': end.x,
                            'end_y': end.y,
                            'drawing': drawing,
                            'line_width': line_width,
                            'det_cat_1': None,
                            'det_cat_2': None,
                            'det_cat_3': None,
                            'size': None,
                            'det_weight': None
                        })

        # Determine significant widths for this page
        page_df = pd.DataFrame(page_lines)
        pipe_width, measurement_width = determine_significant_widths(page_df)


        ### WORKING DO NOT DELETE
        # Classify lines based on their width
        # First pass: classify lines based on their width
        # Classify lines based on their width and isometric property
        # pipe_lines = []
        # for line in page_lines:
        #     if pipe_width is not None and measurement_width is not None and line['is_isometric']:
        #         if abs(line['line_width'] - max(pipe_width, measurement_width)) < 0.01:
        #             line['det_cat_1'] = 'Pipe'
        #             pipe_lines.append(line)
        #         # elif abs(line['line_width'] - min(pipe_width, measurement_width)) < 0.01:
        #         #     line['det_cat_1'] = 'Measurement'
        #         else:
        #             line['det_cat_1'] = 'Other'
        #     else:
        #         line['det_cat_1'] = 'Other'
                
        ### WORKING - DO NOT DELETE
                
        pipe_lines = []
        for line in page_lines:
            if pipe_width is not None and measurement_width is not None:
                if abs(line['line_width'] - max(pipe_width, measurement_width)) < 0.01:
                    if line['is_isometric']:
                        line['det_cat_1'] = 'Pipe'
                        pipe_lines.append(line)
                    else:
                        line['det_cat_1'] = 'Potential_Pipe'
                        pipe_lines.append(line)
                else:
                    line['det_cat_1'] = 'Other'
            else:
                line['det_cat_1'] = 'Other'

        # Additional pass to connect potential pipe segments
        for line in page_lines:
            if line['det_cat_1'] == 'Potential_Pipe':
                if any(is_connected(line, pipe) for pipe in pipe_lines if pipe['det_cat_1'] == 'Pipe'):
                    line['det_cat_1'] = 'Pipe'
                else:
                    line['det_cat_1'] = 'Other'

        # After the initial classification
        for line in page_lines:
            if line['det_cat_1'] == 'Potential_Pipe':
                if any(is_connected(line, pipe) for pipe in pipe_lines):
                    line['det_cat_1'] = 'Pipe'
                    pipe_lines.append(line)  # Add this line to pipe_lines immediately

        # Final update of pipe_lines
        pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']

        print(f"Final pipe count: {len(pipe_lines)}")
        print(f"Remaining potential pipes: {sum(1 for line in page_lines if line['det_cat_1'] == 'Potential_Pipe')}")

        # Update pipe_lines to include only confirmed 'Pipe' segments
        #pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
                
        # Second pass: classify measurement lines and pointer lines
        for line in page_lines:
            if line['det_cat_1'] == 'Other':
                if abs(line['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel_to_pipe(line, pipe_lines):
                    line['det_cat_1'] = 'Measurement'
                elif is_pointer_line(line, pipe_lines):
                    line['det_cat_1'] = 'Pointer'
                    pointer_count += 1
                    
        # Third pass: identify pipe continuations and elbows
        # page_lines = identify_pipe_continuations(page_lines, pipe_width)

        # # Update pipe_lines after continuations have been identified
        # pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
                    
        print(f"Page {page_num + 1}: Found {len(pipe_lines)} pipe lines")
        print(f"Page {page_num + 1}: Found {sum(1 for line in page_lines if line['det_cat_1'] == 'Measurement')} measurement lines")

        # print(f"Page {page_num + 1}: Found {len(pipe_lines)} pipe lines")
        # # Second pass: classify measurement lines and pointer lines
        # for line in page_lines:
        #     if line['det_cat_1'] == 'Other':
        #         if abs(line['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel_to_pipe(line, pipe_lines):
        #             line['det_cat_1'] = 'Measurement'
        #         elif is_pointer_line(line, pipe_lines):
        #             line['det_cat_1'] = 'Pointer'
        #             pointer_count += 1
                    
        # # Second pass: check for pointer lines
        # for line in page_lines:
        #     if line['det_cat_1'] in ['Measurement', 'Other']:
        #         if is_pointer_line(line, pipe_lines):
        #             line['det_cat_1'] = 'Pointer'
        #             pointer_count += 1

        all_lines.extend(page_lines)

    doc.close()
    print(f"Total lines found: {len(all_lines)}")
    print(f"Total pointer lines detected: {pointer_count}")
    return pd.DataFrame(all_lines)

def main(input_filepath, output_filepath, pages_to_process=None):
    
    isometric_area = {
        'x0': 804.5,
        'x1': 2404.5,
        'y0': 40.0,
        'y1': 1234.0
    }

    # Process drawings and create DataFrame
    df = process_drawings(input_filepath, pages_to_process, isometric_area_coords=isometric_area)
    
    # Print summary of the DataFrame
    print(f"\nDataFrame summary:")
    print(df.describe())
    print(f"\nIsometric lines: {len(df)}")
    
    # Output isometric lines to PDF
    output_isometric_lines_to_pdf(df, input_filepath, output_filepath)
    
    return df

# Usage
input_pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Weld Recognition\Weld Recognition Test.pdf"
output_pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Weld Recognition\Testing\output_clusters_1.pdf"

output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Weld Recognition\Testing"

# Process only pages 1, 2, and 3
df = main(input_pdf_path, output_pdf_path, pages_to_process=[1,2,3,4,5]) # Pass Specific pages as a list like: [1,2,3]

df.to_excel(os.path.join(output_dir, "vector_data.xlsx"))
#regions.to_excel(os.path.join(output_dir, "iso_regions_data.xlsx"))

###############################
###############################
###############################
###############################

def output_isometric_lines_to_pdf_1(df, input_filepath, output_filepath): # Output as original color
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")
        
        # Create a new page with the same dimensions as the input PDF
        input_page = doc[page_num - 1]  # Subtract 1 because page numbers in df start from 1
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
        shape = outpage.new_shape()

        # Draw all isometric lines for this page
        for _, line in group.iterrows():
            start = fitz.Point(line['start_x'], line['start_y'])
            end = fitz.Point(line['end_x'], line['end_y'])
            shape.draw_line(start, end)
            shape.finish(**get_finish_params(line['drawing']))  # Use original color and properties

        shape.commit()

    print(f"Saving PDF to {output_filepath}")
    outpdf.save(output_filepath)
    print("PDF saved. Closing files...")
    outpdf.close()
    doc.close()
    print("PDF output process completed.")
    

# Works correctly but trying new suggested function for better fine tuning
# def is_parallel_to_pipe(line, pipe_lines, angle_tolerance=5, distance_threshold=80, min_parallel_ratio=0.5):
    
#     '''
#     Check if line runs parallel to identified pipe. Isolates measurement lines
    
#     '''

#     line_angle = line['angle']
#     line_geom = line['geometry']
    
#     for pipe in pipe_lines:
#         pipe_angle = pipe['angle']
#         pipe_geom = pipe['geometry']
        
#         # Check if angles are parallel (considering the isometric nature)
#         angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
#         if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
#             # Check if the line runs alongside the pipe
#             pipe_buffer = pipe_geom.buffer(distance_threshold)
#             parallel_part = line_geom.intersection(pipe_buffer)
#             if parallel_part.length / line_geom.length >= min_parallel_ratio:
#                 return True
    
#     return False

# Works but need to filter this region down
# def process_drawings(filepath, pages_to_process=None):
#     doc = fitz.open(filepath)
    
#     if pages_to_process is None:
#         pages_to_process = range(len(doc))
#     else:
#         pages_to_process = [p - 1 for p in pages_to_process if 0 < p <= len(doc)]

#     all_lines = []

#     for page_num in pages_to_process:
#         page = doc[page_num]
#         drawings = page.get_drawings()
#         print(f"Page {page_num + 1}: Found {len(drawings)} drawings")

#         for drawing in drawings:
#             for item in drawing['items']:
#                 if item[0] == 'l':  # line
#                     start = item[1]
#                     end = item[2]
#                     line = LineString([(start.x, start.y), (end.x, end.y)])
#                     angle = calculate_angle(line)
#                     length = line.length
#                     is_iso = is_isometric_angle(angle)
#                     print(f"Line: start={start}, end={end}, angle={angle:.2f}, is_isometric={is_iso}")
#                     all_lines.append({
#                         'page': page_num + 1,
#                         'geometry': line,
#                         'angle': angle,
#                         'length': length,
#                         'is_isometric': is_iso,
#                         'start_x': start.x,
#                         'start_y': start.y,
#                         'end_x': end.x,
#                         'end_y': end.y,
#                         'drawing': drawing  # Store the original drawing data
#                     })

#     doc.close()
#     print(f"Total lines found: {len(all_lines)}")
#     return pd.DataFrame(all_lines)



#df, regions = main(input_pdf_path, output_pdf_path, pages_to_process=[1])

# def identify_isometric_regions(df, min_lines=5):
#     connected_groups = find_connected_isometric_lines(df)
    
#     regions = []
#     for group in connected_groups:
#         if len(group) >= min_lines:
#             group_df = df.iloc[group]
#             regions.append({
#                 'total_lines': len(group),
#                 'isometric_lines': len(group),
#                 'isometric_ratio': 1.0,
#                 'is_isometric_region': True,
#                 'min_x': group_df['start_x'].min(),
#                 'min_y': group_df['start_y'].min(),
#                 'max_x': group_df['end_x'].max(),
#                 'max_y': group_df['end_y'].max(),
#                 'lines': group
#             })
    
#     regions_df = pd.DataFrame(regions)
    
#     print(f"Total isometric regions: {len(regions_df)}")
#     print(f"Total isometric lines in regions: {regions_df['isometric_lines'].sum()}")
    
#     return regions_df

# def output_isometric_regions_to_pdf(df, regions, input_filepath, output_filepath):
#     print(f"Starting PDF output process...")
#     print(f"Number of regions: {len(regions)}")
    
#     doc = fitz.open(input_filepath)
#     outpdf = fitz.open()

#     for idx, region in regions.iterrows():
#         print(f"Processing region {idx+1}")
#         try:
#             page_num = df.loc[region['lines'][0]]['page'] - 1
#             print(f"Attempting to access page {page_num}")
#             if page_num < 0 or page_num >= len(doc):
#                 print(f"Invalid page number: {page_num}. Skipping this region.")
#                 continue
#             page = doc[page_num]
#         except Exception as e:
#             print(f"Error accessing page for region {idx+1}: {str(e)}")
#             continue

#         outpage = outpdf.new_page(width=page.rect.width, height=page.rect.height)
#         shape = outpage.new_shape()

#         # Draw all lines in this region
#         for line_idx in region['lines']:
#             try:
#                 line = df.loc[line_idx]
#                 drawing = line['drawing']
#                 draw_path(shape, drawing)
#                 shape.finish(**get_finish_params(drawing))
#             except Exception as e:
#                 print(f"Error drawing line {line_idx}: {str(e)}")

#         shape.commit()

#         # Draw the region rectangle
#         rect = fitz.Rect(region['min_x'], region['min_y'], region['max_x'], region['max_y'])
#         shape = outpage.new_shape()
#         shape.draw_rect(rect)
#         shape.finish(color=(1, 0, 0), width=1)  # Red outline
#         shape.commit()

#         # Add region information
#         outpage.insert_text((20, 20), f"Isometric Region {idx+1}")
#         outpage.insert_text((20, 40), f"Total Lines: {region['total_lines']}")

#     print(f"Saving PDF to {output_filepath}")
#     outpdf.save(output_filepath)
#     print(f"PDF saved. Closing files...")
#     outpdf.close()
#     doc.close()
#     print(f"PDF output process completed.")





# To process all pages, you can either omit the pages_to_process argument:
# process_clusters(input_filepath, output_filepath)
# Or explicitly set it to None:
# process_clusters(input_filepath, output_filepath, pages_to_process=None)

# pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Weld Recognition\Weld Recognition Test.pdf"
# output = pymupdf4llm.to_markdown(pdf_path, page_chunks=True)