import os
from src.atom.dbManager import DatabaseManager
from src.utils.logger import logger
import datetime
import __features__

from PySide6.QtCore import *
from PySide6.QtGui import QHideEvent
from PySide6.QtWidgets import *
from PySide6.QtGui import *

from functools import partial
import pandas as pd
from pubsub import pub
from src.widgets.groupedtableview import GroupedTableView, GroupedTableFlags
from src.views.documentviewonly import DocumentViewOnly
from src.pyside_util import get_resource_qicon, applyDropShadowEffect
from src.app_paths import getColumnsConfigJson, saveColumnsConfigJson, getSavedFieldMapJson, getTableExportPath
from src.data.tables.hiddentablefields import hidden_table_fields
from src.data.tables.alwaysvisibletablefields import always_visible_table_fields
from src.views.popups import HelpPopup
from src.views.dialogs.columnorganizer import ColumnOrganizer
from src.utils.export import export_to_excel


# logger = logging.getLogger(__name__)
# ch = logging.StreamHandler()
# ch.setLevel(logging.DEBUG)
# logger.addHandler(ch)

TABLE_CHECKBOX_STYLE = GroupedTableFlags.HEADER_CHECKBOX_FIRST if __features__.TABLE_CHECKBOX_ENABLED else GroupedTableFlags.HEADER_CHECKBOX_OFF


HELP_HTML = """
<h3>Keyboard shortcuts</h3>
<p>

<b>Go to cell (1,1)</b>
<i>Ctrl + Home</i>
</p>
<p>

<b>Go to cell (n,n)</b>
<i>Ctrl + End</i>
</p>
<p>

<b>Go to next non-blank value in row or column</b>
<i>Ctrl + Shift + Arrow Key</i>
</p>
<p>

<h3>Build RFQ</h3>
<p>Generate a RFQ. This this will create a new RFQ tab or replace the existing one</p>

<h3>MTO Assistant</h3>
<p>Request AI assistance to automatically fill the RFQ table.
Saved records will be restored from the database</p>

<h3>Apply</h3>
<p>Map the RFQ into the data tables</p>
<br>
"""


class TableResultsViewBase(QWidget):
    """Shows basic results in table"""

    # Emit a <requesting table name, list[TableNames]> e.g. emit(RFQ, ["BOM", "General"])
    sgnRequestTableData = Signal(str, list) # Connected externally from ProjectsView
    # Send other table data in {"bom": df, "general": df}
    sgnTableDataReceived = Signal(dict)
    # This will set the data of another table
    updateOtherTableData = Signal(object) # (The Table name to update, Dataframe)
    #
    sgnUpdateDf = Signal(object) # Object is Pandas Dataframe
    sgnShowQMessageBox = Signal(str, str) # QMessageBox msg, title

    sgnSaveAll = Signal()

    tableExportDialog = Signal(str) # Active table

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self._parent = parent
        self._cacheColumnOrder = []
        self._toolbarBtns = {}
        self.popupHelp = None
        self._columnOrganizer = None

        self._dockedPreview = True
        self._documentViewerGeom: QRect = None
        self.field_mapping = self.loadFieldMap()

        self.setLayout(QVBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.lblTimestamp = QLabel("Last updated: Never")
        self.initToolbar()
        self.splitter: QSplitter = QSplitter(Qt.Orientation.Horizontal)
        self.splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.layout().addWidget(self.splitter)
        self.initTable()
        self.restoreColumnOrder()
        self.setObjectName("dataTable")

        self.documentViewer: DocumentViewOnly = None

        self.sgnUpdateDf.connect(self.setTableData)
        self.sgnShowQMessageBox.connect(self._showQMessageBox)
        self.sgnTableDataReceived.connect(self.onTableDataReceived)
        # Update the statusbar whenever we change cell
        self.table.sgnSelectionChanged.connect(self.onSelectionChanged)

        pub.subscribe(self.fieldMapUpdated, "field-map-updated")
        pub.subscribe(self.deleteTableDataResponse, "delete-table-data-response")
        pub.subscribe(self.setTableZoom, "set-table-zoom")

        self.table._alwaysVisibleColumns = always_visible_table_fields.get(str(self), [])

        actSaveAll = QAction("test", self)
        actSaveAll.triggered.connect(lambda: self.sgnSaveAll.emit())
        actSaveAll.setShortcut(QKeySequence("Ctrl+S"))
        self.addAction(actSaveAll)

    @property
    def forcedHiddenFields(self):
        """Force hide fields - User should not be able see these internal fields"""
        hidden = hidden_table_fields.get("ALL", []).copy()
        hidden.extend(hidden_table_fields.get(str(self), []))
        hidden = list(set(hidden))
        return hidden

    def cleanup(self):
        logger.info("Table results cleanup")
        try:
            self._columnOrganizer.close()
        except Exception as e:
            # TODO
            logger.info("Could not close Column Organizer. May not have been open")
        pub.unsubscribe(self.fieldMapUpdated, "field-map-updated")
        pub.unsubscribe(self.deleteTableDataResponse, "delete-table-data-response")
        pub.unsubscribe(self.setTableZoom, "set-table-zoom")

    def loadFieldMap(self):
        try:
            field_mapping = getSavedFieldMapJson()
            # print(field_mapping)
            for k, v in field_mapping.items():
                for field, v2 in field_mapping.get(k, {}).items():
                    v2["id"] = field
            return field_mapping
        except Exception as e:
            print(f"Error loading field mapping: {e}")

    def getGenMap(self):
        # Ensure the field mapping is loaded
        if not hasattr(self, 'field_mapping'):
            self.field_mapping = self.loadFieldMap()
        # Navigate through the nested structure to get 'gen_map'
        try:
            gen_map = self.field_mapping['rfq_fields']['general_category']['gen_map']
            return gen_map
        except KeyError as e:
            print(f"Key error accessing gen_map: {e}")
            return None

    def fieldMapUpdated(self):
        self.field_mapping = self.loadFieldMap()
        self.table.fieldMap = self.getSimpleFieldMap()

    def initTable(self):
        self.table: GroupedTableView = GroupedTableView(self.splitter,
                                                        checkboxStyle=TABLE_CHECKBOX_STYLE)
        self.table.horizontalHeader().setSectionsMovable(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignRight)
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.onTableRightClick)
        self.table.rowContextMenuRequested.connect(self.onRowContextMenuRequested)
        self.table.frozenTable.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.frozenTable.customContextMenuRequested.connect(self.onTableRightClick)
        self.table.contextHeaderMenuClicked.connect(self.onContextHeaderMenuClicked)
        self.table.zoomChanged.connect(self.zoomChanged)
        self.splitter.addWidget(self.table)

    def initToolbar(self):
        self.toolbar = QToolBar("Table Toolbar", self)
        self.layout().addWidget(self.toolbar)
        self.toolbarWidget = QWidget(self.toolbar)
        self.toolbar.setObjectName("popup")
        self.toolbarWidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.toolbarWidget.setLayout(QHBoxLayout())
        self.toolbar.addWidget(self.toolbarWidget)

        self.toolbarWidget.layout().addWidget(self.lblTimestamp)
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.toolbarWidget.layout().addItem(spacer)

        buttons = [
            ("freeze pane", "freeze-panes.svg"),
            ("help", "help-circle.svg"),
            ("edit columns", "tableview-columns.svg"),
            ("remove", "tableview-minus.svg"),
            ("save", "tableview-save.svg"),
            ("download", "tableview-download.svg")
        ]
        for name, icon_path in buttons:
            self.addToolbarButton(name, icon_path)

    @property
    def projectData(self):
        try:
            return self._parent.projectData
        except Exception:
            logger.info("Could not retrieve projectData from tableResultsView parent", exc_info=True)
            return {}

    def addToolbarButton(self, name: str, icon: str) -> QPushButton:
        btn = QPushButton(get_resource_qicon(icon), "", self)
        applyDropShadowEffect(btn, offset=(0, 2))
        # btn.setObjectName(f"{name}Btn")
        btn.setIconSize(QSize(18, 18))
        btn.setMinimumSize(42, 42)
        btn.setToolTip(name.replace("-", " ").title())
        self._toolbarBtns[name] = btn
        self.toolbarWidget.layout().addWidget(btn)
        self.toolbarWidget.layout().setSpacing(12)
        # Connect button click to onToolbarBtn method
        btn.clicked.connect(partial(self.onToolbarBtn, name))
        if name == "help":
            self.pbHelp = btn
            btn.setCheckable(True)
            btn.leaveEvent = self.onHelpLeave
            btn.enterEvent = self.onHelpEnter
        elif name == "download":
            menu = QMenu(btn)
            for a in ["Export...", "Quick Export"]:
                action = menu.addAction(a)
                action.setData(a)

            if not __file__.endswith(".pyc"):
                action = menu.addAction("Quick Export (All columns, No fieldmap)")
                action.setData("Quick Export (All columns, No fieldmap)")

            menu.triggered.connect(self.onTableDownload)
            btn.setMenu(menu)
            btn.setMinimumWidth(49)
            btn.clicked.disconnect()
        return btn

    def getSimpleFieldMap(self):
        CELL_BACKGROUND = "#2f353f"
        simpleFieldMap = {}
        for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
            subData = self.field_mapping.get(key, {})
            if key in ["rfq_fields", "ancillary_fields"]:
                for k, v in subData.items():
                    v["background"] = CELL_BACKGROUND
            simpleFieldMap.update(subData)
        return simpleFieldMap

    def setTableData(self, data: pd.DataFrame, autosize=True):
        """TODO: For now, we recreate the model every time data is set"""
        self.table.unFreezePane()
        if type(data) == list:
            logger.info("Data is likely an empty [] when there is no data.", exc_info=True)
            data = pd.DataFrame() # Convert to empty dataframe
        elif not data.empty and 'quantity' in data.columns:
            # Temp - data cleaning. This removes any rows where the cell
            # value of quantity is equal to 'QTY'
            data = data[data.quantity != "QTY"]

        data["__checked__"] = False
        # Cache column order to restore it. But don't cache if the current
        # data are extraction result
        tableName = str(self)
        tempdf = self.table.getDataFrame(False)
        if tempdf is not None and not tempdf.empty:
            if tableName in ["BOM", "General", "SPEC", "SPOOL"]:
                if "id" in tempdf.columns:
                    print("Caching columns before data refresh")
                    self.saveColumnOrder()
            elif tableName == "RFQ":
                print("Caching columns before data refresh")

        # Supply a flattened field map to the table.
        # We flatten the data so that {"fields": {}, "..": {}}
        # becomes single dict { field: alias }. This makes is simpler under
        # the hood i.e. for the model. But may revisit if it prevents some other functionality
        self.table.fieldMap = self.getSimpleFieldMap()

        now = datetime.datetime.now()
        self.lblTimestamp.setText(f"Last updated: {now}")

        self.table.setTableData(data)
        self.restoreColumnOrder()
        self.table.model().sourceModel().sgnRowsLoadedChanged.connect(self.updateStatusBar)

        # Connect the sectionClicked signal to print the column's key
        self.table.horizontalHeader().sectionClicked.connect(self.onHeaderClicked)
        if autosize:
            self.autoSizeColumns()

    def autoSizeColumns(self):
        # Auto-size columns based on their content, with adjustments
        self.table.autoSizeColumns()

    def _addRFQFields(self):
        """Append RFQ fields to table """
        try:
            if self.table.model():
                rfq_fields = self.field_mapping.get("rfq_fields", {})
                rfq_columns = [field for field in rfq_fields.keys()]
                self.table.addColumns(rfq_columns)
                for field, value in rfq_fields.items():
                    options = value.get("options")
                    if options:
                        showFractions = False
                        if field in ["size", "size1", "size2"]:
                            showFractions = True
                        self.table.setComboboxDelegateForColumn(field, options, showFractions)
                    else:
                        # default delegate
                        self.table.setLineEditDelegateForColumn(field)
            self.autoSizeColumns()
        except Exception as e:
            print(f"\n\nCould not add RFQ Fields {rfq_columns}: {e}")

    def _addAncillaryFields(self):
        """ Append Ancillary fields to table """
        try:
            if self.table.model():
                ancillary_fields = self.field_mapping.get("ancillary_fields", {})
                ancillary_columns = [field for field in ancillary_fields.keys()]
                self.table.addColumns(ancillary_columns)
                print("Added ANCILLARY COLUMNS")
                for field, value in ancillary_fields.items():
                    options = value.get("options")
                    if options:
                        try:
                            self.table.setComboboxDelegateForColumn(field, options)
                        except Exception as e:
                            logger.error(f"\n\nError setting ComboBox delegate: {e}", exc_info=True)
                    else:
                        try:
                            self.table.setLineEditDelegateForColumn(field)
                        except Exception as e:
                            logger.error(f"\n\nError setting Line Edit delegate: {e}", exc_info=True)
            self.autoSizeColumns()
        except Exception as e:
            logger.error(f"\n\nCould not add Ancillary Fields: {e}", exc_info=True)

    def onHeaderClicked(self, index):
        # print("\n\n -- HEADER CLICKED\n\n")
        # return
        model = self.table.model()
        # Retrieve the original key from the header item's data
        key = model.horizontalHeaderItem(index).data(Qt.UserRole)
        print(f"Column clicked: {key}")

    def getTableData(self, drop_uid: bool = False) -> pd.DataFrame:
        """Returns dataframe"""
        return self.table.getDataFrame(drop_uid=drop_uid)

    def getActivePdfId(self) -> int:

        try:
            index = self.table.selectedIndexes()[0]
            row = index.row()

            # Need to get uid from filtered and match with full data
            # as PDF ID maybe hidden in filtered
            if self.table.isFiltered():
                df = self.table.getFilteredDataFrame()
            else:
                df = self.getTableData(drop_uid=False)

            pdfId = df["pdf_id"].iloc[row]

            return pdfId
        except Exception as e:
            return -1

    def onTableRightClick(self, pos):
        parent = self.sender()
        offset = parent.mapToGlobal(QPoint(64, 20))
        try:
            pdfId = self.getActivePdfId()
            if pdfId == -1:
                return
            pdfId = int(pdfId)
            logger.debug(f"Right click table menu. Row pdfId = {pdfId}")
            menu = QMenu(self)
            action = menu.addAction("View Document")
            action.triggered.connect(lambda: self.onViewDocument(pdfId))
            # action = menu.addAction("Delete All Rows")
            # action.triggered.connect(self.onDeleteAllRows)
            menu.move(pos + offset)
            menu.show()
        except Exception as e:
            # Note: no index not selected.
            logger.debug(e, exc_info=True)

    def onViewDocument(self, pdfId):

        def dockToggle(docked: bool):
            self._dockedPreview = docked
            if self.documentViewer:
                if self.documentViewer.parent():
                    self.documentViewer.setParent(None)
                    self.documentViewer.show()
                    if self._documentViewerGeom:
                        self.documentViewer.setGeometry(self._documentViewerGeom)
                    self.documentViewer.ensureInViewport()
                    self.documentViewer.setWindowTitle(f"Document Viewer - {self}")
                else:
                    self._documentViewerGeom = self.documentViewer.geometry()
                    self.splitter.addWidget(self.documentViewer)
                    self.splitter.setStretchFactor(1, 0.8)

        def closeDocumentViewer():
            if self.documentViewer:
                if self.documentViewer.parent() is None:
                    self._documentViewerGeom = self.documentViewer.geometry()
                self.documentViewer.setParent(None)
                self.documentViewer.setEnabled(False)
                try:
                    self.documentViewer.sgnClose.disconnect()
                    self.documentViewer.sgnSetDockedMode.disconnect()
                except Exception as e:
                    logger.info("DocumentViewer cleanup error - disconnecting signals")
                # self.documentViewer.cleanup()
                self.documentViewer.destroy()
                self.documentViewer = None

        if not self.documentViewer:
            if self._dockedPreview:
                self.documentViewer = DocumentViewOnly(self.splitter)
                self.splitter.addWidget(self.documentViewer)
                self.splitter.setStretchFactor(1, 0.8)
            else:
                self.documentViewer = DocumentViewOnly(None)
                self.documentViewer.setWindowTitle(f"Document Viewer - {self}")
                if self._documentViewerGeom:
                    self.documentViewer.setGeometry(self._documentViewerGeom)
                self.documentViewer.ensureInViewport()
                self.documentViewer.show()
            self.documentViewer.sgnClose.connect(closeDocumentViewer)
            self.documentViewer.sgnSetDockedMode.connect(dockToggle)

        self.documentViewer.raise_()
        self.documentViewer.loadPdfById(pdfId)

    def updateStatusBar(self, *args):
        index = self.table.currentIndex()
        msg = ""
        try:
            model = self.table.model()
            x, y = None, None
            if index and index.column() != -1:
                self.table.getDefaultColumnNameOrder()
                c = self.table._cacheIndexToColumn[index.column()]
                swapped = self.table.getSwappedColumnNameOrder(excludeHidden=True, cache=False)
                c = swapped.index(c)
                x = c + 1
                y = index.row() + 1
            if x is None or x == -1:
                xy = ""
            else:
                xy = f" | [{x}, {y}]"
            rows = self.getTableData().shape[0]
            msg = f"{model.sourceModel().rowsLoaded} of {rows}{xy}"
        except Exception as e:
            msg = ""
        data = {"type": super().__repr__(), "params": {"msg": msg} }
        pub.sendMessage("set-statusbar-table", data=data)

    def showEvent(self, event) -> None:
        self.updateStatusBar()
        self.updateStatusBarZoom()
        return super().showEvent(event)

    def onSelectionChanged(self) -> None:
        self.updateStatusBar()
        if self.documentViewer and self.documentViewer.isFollowing():

            try:
                pdfId = self.getActivePdfId()
                if pdfId != -1 and self.documentViewer.pdfId != pdfId:
                    self.documentViewer.loadPdfById(pdfId)
            except Exception as e:
                pass


    def onToolbarBtn(self, name):
        """Default handling of toolbar buttons"""
        if name == "edit columns":
            self.showColumnOrganizer()
        elif name == "remove":
            try:
                self.onRemoveRows()
            except Exception as e:
                self.sgnShowQMessageBox.emit(f"Error: {e}", f"Failed to remove rows")
                self.table.setEnabled(True)
        elif name == "download":
            return
            baseDir = getTableExportPath()
            fn = f'exported_{str(self).lower()}_data.xlsx'
            exportPath = os.path.join(baseDir, fn)
            res = export_table_data_to_excel(self.table, exportPath) # Export to Excel

            baseDirUrl = QUrl.fromLocalFile(baseDir).toString()
            exportPathUrl = QUrl.fromLocalFile(exportPath).toString()
            msg = f"Exported table data to <a href='{baseDirUrl}'>{baseDir}</a>"
            msg += f"<br><br><a href='{exportPathUrl}'>{fn}</a>"
            if res is True:
                pass
            elif res is False:
                msg = "No data to export"
            else:
                msg = f"Failed to export data to {fn}: {res}"
            self.sgnShowQMessageBox.emit(msg, f"Export {fn} data")
        elif name == "freeze pane":
            self.table.toggleFreezePane()

    def onRemoveRows(self):
        """Prompt to remove checked rows"""
        logger.info("Delete Rows")
        tableKeys = {
            "BOM": "bom_data",
            "SPOOL": "spool_data",
            "General": "general_data",
            "SPEC": "spec_data",
            "Outlier": "outlier_data"
        }
        tableName = tableKeys.get(str(self))
        if not tableName:
            return
        if self.table.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            df: pd.DataFrame = self.table.getCheckedSelections()

        else:
            df: pd.DataFrame = self.table.getSelectedRowData()

        if df is None or len(df) == 0:
            return
        # Check if table data is not currently committed e.g. extraction results
        if "__uid__" not in df.columns or "id" not in df.columns:
            msg = f'Remove {len(df)} selected rows?'
            result = QMessageBox.question(self, "Remove?", msg)
            if result == QMessageBox.Yes:
                if self.table.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
                    self.table.removeCheckedRows()
                else:
                    self.table.removeSelectedRows()
            else:
                return
        else:
            single = len(df) == 1
            result = QMessageBox.question(self, "Confirm delete?", f"Delete {len(df)} selected row{'' if single else 's'} from {str(self)}?")
            if result == QMessageBox.Yes:
                pub.sendMessage("delete-table-data-request",
                                projectData=self.projectData,
                                tableName=str(self),
                                data=df)
                return
            else:
                return

        newDf = self.table.getDataFrame()
        if newDf is None or newDf.empty:
            # As we have cleared extraction results, refresh from the db
            t = f"No remaining extraction results. Refreshing {str(self)} data from the database"
            QMessageBox.information(self, "Results Cleared", t)
            pub.sendMessage("request-project-table-data", data=self.projectData, types=[tableName])

    def onColumnPresetChanged(self, index):
        """Not implemented"""
        data = self.cboxColumnPresets.itemData(index)
        if data == -1:
            self.table.onColumnPresetChanged()

    def restoreColumnOrder(self, fallback=None):
        tableName = str(self)
        data = getColumnsConfigJson()
        if tableName == "Outlier":
            data = {}

        try:
            # Get the order given the project ID
            projectId = str(self.projectData["id"])
            try:
                if tableName in data[projectId]:
                    columnOrder = data[projectId][tableName]["order"]
                    self.table.setColumnOrder(columnOrder)
                elif fallback:
                    self.table.setColumnOrder(fallback)
            except Exception as e:
                if fallback:
                    self.table.setColumnOrder(fallback)
                logger.info("No Project ID={e} column order to restore")
            self.table._forceHiddenColumns = list(set(self.forcedHiddenFields))
            # Ensure hidden in case columns config includes a should
            # be hidden field
            self.table.setHiddenColumns(self.forcedHiddenFields)
            logger.info(f"Table column order loaded for {tableName}")
            print(f"Table column order loaded for {tableName}")
        except Exception as e:
            # No column order previously saved
            logger.info("No Project ID={e} column order to restore")

        # self.table.refreshCheckboxColumn()

    def saveColumnOrder(self):
        """This is based on UI"""
        tableName = str(self)
        data = getColumnsConfigJson()
        fields = self.getAdaptedColumnOrderForConfig()
        projectId = str(self.projectData["id"])
        data.setdefault(projectId, {})
        data[projectId][tableName] = {"order": fields}
        saveColumnsConfigJson(data)

    def _showQMessageBox(self, message, title):
        """This should not be called directly. Use sgnShowQMessageBox signal"""
        msgBox = QMessageBox()
        msgBox.setTextFormat(Qt.TextFormat.RichText)
        msgBox.setWindowTitle(title + "              ")  # Enlarges message box
        msgBox.setIcon(QMessageBox.Icon.Information)
        msgBox.setText(message)
        msgBox.exec()

    def deleteTableDataResponse(self, data):
        projectId = data["projectId"]
        failed = len(data["failed"])
        tableName = data["tableName"]
        error = data["error"]
        success = len(data["deleted"])
        if str(self) != tableName:
            return
        if error:
            fail = f" Failed to delete rows"
            msg = f"{tableName} - {error}"
            self.sgnShowQMessageBox.emit(msg, "Delete Table Rows Fail")
            return

        fail = ""
        if failed:
            fail = f" Failed to delete {failed} rows"
        msg = f"Deleted {success} rows from Table {tableName}.{fail}"
        self.sgnShowQMessageBox.emit(msg, "Delete Table Rows")

    def onTableDataReceived(self, data: dict):
        """Handler for sgnTableDataReceived"""
        logger.info(f"Table {self} recieved data from {data.keys()} Tables")

    def onDeleteAllRows(self):
        """Override this"""
        assert False, "Deprecrated, this should never be called"
        logger.info("Delete Rows")
        df = self.getTableData(drop_uid=False)
        if df is None:
            return
        if str(self) == "RFQ":
            logger.info("TODO - clear RFQ data")
            return
        if "__uid__" not in df.columns or "id" not in df.columns:
            msg = 'Discard extraction results?'
            result = QMessageBox.question(self, "Clear?", msg)
            key = {"BOM": "bom_data", "SPOOL": "spool_data", "General": "general_data",
                       "SPEC": "spec_data", "Outlier": "outlier_data"}
            k = key.get(str(self))
            if not k:
                return
            if result == QMessageBox.Yes:
                self.table.setEnabled(False)
                pub.sendMessage("request-project-table-data", data=self.projectData, types=[k])
            return
        rows = self.table.getSelectedRowData()
        if rows is None or rows.empty:
            return
        # Replace this with checkbox rows
        msg = 'Delete all rows?'
        result = QMessageBox.question(self, f"Delete {len(rows)} from table?", msg)
        if result == QMessageBox.Yes:
            pub.sendMessage("delete-all-table-data-request", projectData=self.projectData, tableName=str(self))

    def isUnsaved(self) -> bool:
        rep = str(self)
        df = self.getTableData()
        return "__uid__" not in df.columns or "id" not in df.columns

    def saveToDatabase(self, tableRequests=None):
        from src.atom.dbManager import DatabaseManager
        df = self.getTableData()
        print(f"\n\{str(self)} DF ROWS FOR DB COMMIT: \n", len(df))
        if str(self) == "Outlier":
            logger.info("Outlier data saving not supported")
            return
        db_manager = DatabaseManager()
        db_manager.insert_dataframe(df, str(self))
        # TODO - Need to refresh
        if tableRequests:
            pass
            # pub.sendMessage("request-project-table-data", data=self.projectData, types=["bom_data"])

    def enableModelDataUpdates(self, enabled: bool):
        # print(self.table.model().sourceModel())
        self.table.model().sourceModel()._dataReadUpdates = enabled

    def onHelpEnter(self, event: QEnterEvent):
        if self.popupHelp:
            return
        self.popupHelp = HelpPopup(self)
        self.popupHelp.setText(HELP_HTML)
        self.popupHelp.show()

    def onHelpLeave(self, event):
        if self._toolbarBtns["help"].isChecked():
            return
        self.popupHelp.close()
        self.popupHelp = None

    def onRowContextMenuRequested(self, position):
        row: int = self.table.verticalHeader().logicalIndexAt(position)
        if row == -1:
            return
        selected = self.table.selectionModel().selectedIndexes()
        single = len(selected) == 1
        a, b = selected[0].row(), selected[-1].row()
        low, high = min(a, b), max(a, b)
        if not selected or not (low <= row <= high):
            self.table.selectRow(row)
            single = True

        menu = QMenu()
        removeRows = menu.addAction("Remove Row" if single else "Remove Rows")
        ac = menu.exec(self.table.mapToGlobal(position))
        if ac == removeRows:
            self.onRemoveRows()

    def onContextHeaderMenuClicked(self, action: QAction):
        """Right click on Table column header"""
        if action.text() == "Edit Columns":
            self.showColumnOrganizer()
        elif action.text() == "Hide Column":
            try:
                section: int = action.data()["section"]
                field: int = action.data()["field"]
                self.table.hideColumn(section)
                self.removeFieldFromColumnConfig(field)
            except Exception as e:
                logger.info(f"Could not hide column {e}", exc_info=True)
        elif action.text() == "Clear All Filters":
            self.table.clearAllFilters()
        elif action.text() == "Unhide All Columns":
            self.table.unhideAllColumns()

    def showColumnOrganizer(self):
        """Open new window or raise existing organizer"""

        def close(event: QCloseEvent):
            try:
                super(ColumnOrganizer, self._columnOrganizer).closeEvent(event)
                self._columnOrganizer = None
            except Exception as e:
                pass

        # Reconnect signals as dialog can be transferred in a table view refresh
        try:
            self._columnOrganizer.columnsSaved.disconnect()
            self._columnOrganizer.closeEvent = None
        except:
            pass

        if not self._columnOrganizer:
            fields = self.getAdaptedColumnOrderForConfig()
            self._columnOrganizer = ColumnOrganizer(tableType=str(self), fields=fields)
            self._columnOrganizer.show()
            self._columnOrganizer.raise_()
        self._columnOrganizer.columnsSaved.connect(self.onColumnOrderSaved)
        self._columnOrganizer.closeEvent = close
        self._columnOrganizer.show()

    def onColumnOrderSaved(self, columns: list):
        frozen = self.table.frozenTable.isVisible()
        if frozen:
            self.table.unFreezePane()
        tableName = str(self)
        data = getColumnsConfigJson()
        projectId = str(self.projectData["id"])
        data.setdefault(projectId, {})
        data[projectId][tableName] = {"order": columns}
        saveColumnsConfigJson(data)
        self.restoreColumnOrder()
        if frozen:
            self.table.freezePane()

    def removeFieldFromColumnConfig(self, field: str):
        """Get columns from currently saved and remove field"""
        tableName = str(self)
        data = getColumnsConfigJson()
        projectId = str(self.projectData["id"])
        data.setdefault(projectId, {})
        order: list = data[projectId][tableName].get("order", [])
        order = [c for c in order if c != field]
        data[projectId][tableName]["order"] = order
        saveColumnsConfigJson(data)

    def getAdaptedColumnOrderForConfig(self) -> list:
        """Adapts the current displayed column order to the persisted order"""
        projectId = str(self.projectData["id"])
        if self.table.cacheSwappedOrder: # Frozen table, we need original order
            displayedOrder = self.table.cacheSwappedOrder
        else:
            displayedOrder = self.table.getSwappedColumnNameOrder(excludeHidden=True, cache=False)
        # Adapt the current order with the currently persisted order before
        # passing it to the organizer
        data = getColumnsConfigJson()
        tableType = str(self)
        # allFields = default_column_order[tableType]
        try:
            data.setdefault(projectId, {})
            savedFieldOrder = data[projectId][tableType]["order"]
            return savedFieldOrder
            # Filter out any invalid fields from the config
            fields = [f for f in savedFieldOrder if f in allFields]
            # Remove duplicates
            tmp = []
            for f in fields:
                if f in tmp:
                    continue
                tmp.append(f)

            fields = tmp

            # Should not be necessary. Append any fields not
            # available in allFields
            for n, f in enumerate(displayedOrder):
                if f in fields:
                    continue
                if f in self.forcedHiddenFields:
                    continue
                fields.append(f)

            tmp = None
            for n, f in enumerate(displayedOrder):
                if n == len(displayedOrder) - 1:
                    break
                # Swap persisted field order if current displayed order
                # has been manually swapped
                # indexedOrder = []
                # for f2 in displayedOrder:
                #     indexedOrder.append(fields.index(f2))

                fIndex = fields.index(f)
                tmpField = fields[fIndex]
                fields[n] = f
                fields[fIndex] = tmpField

            # Re-insert fields not shown in current displayed order but
            # for n, f in enumerate(savedFieldOrder):
            #     if f not in fields:
            #         fields.insert(n, f)
            #         # print(n)

        except Exception as e:
            logger.debug("Failed to get saved field order", exc_info=True)
            fields = displayedOrder

        return fields

    def updateStatusBarZoom(self):
        data = {
            "value": self.table.zoom,
        }
        pub.sendMessage("update-statusbar-table-zoom", data=data)

    def getScaleValue(self) -> int:
        self.table.scaleContents

    def setTableZoom(self, value):
        if not self.isVisible():
            return
        self.setZoom(value)

    def setZoom(self, value):
        self.table.setZoom(value)

    def hideEvent(self, event: QHideEvent) -> None:
        pub.sendMessage("update-statusbar-table-zoom", data=None)
        return super().hideEvent(event)

    def zoomChanged(self, value):
        pub.sendMessage("update-statusbar-table-zoom", data={"value": value})

    def onTableDownload(self, action: QAction):
        data = action.data()
        if data == "Export...":
            self.tableExportDialog.emit(str(self))
        elif data == "Quick Export":
            self.quickExportTable()
        elif data == "Quick Export (All columns, No fieldmap)":
            self.quickExportTableInternal()

    def quickExportTable(self):
        import numpy as np

        baseDir = getTableExportPath()
        filename = f'exported_{str(self).lower()}_data.xlsx'
        exportPath = os.path.join(baseDir, filename)

        baseDirUrl = QUrl.fromLocalFile(baseDir).toString()
        exportPathUrl = QUrl.fromLocalFile(exportPath).toString()
        msg = f"Exported table data to <a href='{baseDirUrl}'>{baseDir}</a>"
        msg += f"<br><br><a href='{exportPathUrl}'>{filename}</a>"

        df = self.table.getDataFrameAsDisplayed(excludehidden=False)
        # Replace NaN with empty string
        df = df.replace(np.nan, '', regex=True)
        res = False
        try:
            export_to_excel(df, exportPath, sheet_names=[str(self)])
            res = True
        except Exception as e:
            msg = f"Failed to export data to {filename}: {res}"
            msg += f"\n{e}"

        self.sgnShowQMessageBox.emit(msg, f"Export {filename} data")

    def quickExportTableInternal(self):
        """Exports full dataframe without fieldmap"""
        import numpy as np

        baseDir = getTableExportPath()
        filename = f'exported_{str(self).lower()}_data_nofieldmap.xlsx'
        exportPath = os.path.join(baseDir, filename)

        baseDirUrl = QUrl.fromLocalFile(baseDir).toString()
        exportPathUrl = QUrl.fromLocalFile(exportPath).toString()
        msg = f"Exported table data to <a href='{baseDirUrl}'>{baseDir}</a>"
        msg += f"<br><br><a href='{exportPathUrl}'>{filename}</a>"

        df = self.table.getDataFrame()
        # Replace NaN with empty string
        df = df.replace(np.nan, '', regex=True)
        res = False

        # Ensure sys path is in data
        if "sys_path" not in df.columns:
            # Fetch sys_paths
            pass

        # df.insert(0, "originalFilename", "Test Doc")

        try:
            export_to_excel(df, exportPath, sheet_names=[str(self)])
            res = True
        except Exception as e:
            msg = f"Failed to export data to {filename}: {res}"
            msg += f"\n{e}"

        self.sgnShowQMessageBox.emit(msg, f"Export {filename} data")