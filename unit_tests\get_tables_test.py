# extract_tables.py
import pprint
import ast
import json
import sys
import os
import re
import fitz  # PyMuPDF fitz uses points coordinate system.
import unicodedata
import statistics

import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import numpy as np
from difflib import SequenceMatcher
from pprint import pprint as pp

from src.app_paths import getDataTempPath
from src.utils.logger import logger
from src.utils.spatial_utils import find_overlapping_rectangles_with_areas

logger_debug_mode=False # Ovveride App Logger settings
debug_mode= False # Export pages as xlsx files
debug_row_content = False # Debug row information at the end of detect_bom_rows
debug_discarded = False # Print intentionally skipped and unassigned items
debug_outliers = False  # Flag to enable debug output for outlier detection in def create_logical_structure_text
debug_row_assignment = False # Debug row assignment in detect_bom_rows
debug_row_merging = False # Debug row merging in merge_wrapped_rows_text
debug_centered_fix = False # Feature flag for the new centered position number fix

# ===== CONFIGURATION FLAGS =====
# Set this to True to skip number validation in leftmost column (for nozzle lists like N1, N2, etc.)
SKIP_NUMBER_CHECK = False  # Change to False for normal BOM processing


TABLE_NAMES = ['bom', 'spec', 'spool', 'ifc', 'generic_1', 'generic_2']
# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)

multi_roi = True

if multi_roi:
    # NEW FORMAT: Page groups are now embedded in the roi.json file
    # No longer need separate page group summary or multiple ROI files

    # The roi.json file will be loaded in the main execution block
    # and will contain both the ROI data and page group information
    page_group_dict = None  # Will be populated from roi.json when loaded

    # Import multi-ROI functions for the new format
    try:
        from src.atom.multi_roi_funcs import load_and_combine_jsons, multi_convert_relative_coords_to_points
        print("✓ Multi-ROI functions imported successfully")
    except ImportError as e:
        print(f"⚠ Warning: Could not import multi-ROI functions: {e}")
        print("  Falling back to single ROI mode")
        multi_roi = False
        page_group_dict = None

else:
    page_group_dict = None


####
# Set the log level
# if logger_debug_mode:
#     logger.setLevel(logging.DEBUG)
#
#     # Create a console handler
#     console_handler = logging.StreamHandler()
#
#     # Set the log level for the handler
#     console_handler.setLevel(logging.DEBUG)
#
#     # Create formatters and add it to the handler
#     log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#     console_handler.setFormatter(log_format)
#
#     # Add the handler to the logger
#     logger.addHandler(console_handler)
###

def preprocess_compound_entries(df, leftmost_column):
    """
    Preprocesses the dataframe to identify and split compound entries that contain
    both a position number and description text in a single value.

    Args:
        df: The dataframe containing table data
        leftmost_column: Dictionary with coordinates of the leftmost column

    Returns:
        DataFrame with compound entries split into separate rows
    """
    new_rows = []
    indices_to_drop = []

    df = df.reset_index(drop=True)

    for index, row in df.iterrows():
        text = str(row.value)
        # Match pattern: number (optional letter) followed by space and description
        match = re.match(r'^(\d{1,3}[a-zA-Z]?)\s+(.*)$', text)

        # Check if this is in the leftmost column region and matches our pattern
        if match and row.x0 >= leftmost_column['x0'] and row.x0 <= leftmost_column['x1']:
            print(index, text)
            indices_to_drop.append(index)

            # Create position entry (keep in leftmost column)
            pos_row = row.copy()
            pos_row.value = match.group(1)
            # Adjust coordinates to just contain the position number
            pos_width = len(match.group(1)) / len(text) * (row.coordinates2[2] - row.coordinates2[0])
            pos_row.coordinates2 = (
                row.coordinates2[0],
                row.coordinates2[1],
                row.coordinates2[0] + pos_width,
                row.coordinates2[3]
            )
            pos_row.x1 = pos_row.x0 + pos_width
            pos_row["words"] = [pos_row["words"][0]]
            new_rows.append(pos_row)

            # Create description entry (move to next column)
            desc_row = row.copy()
            desc_row.value = match.group(2)
            desc_row["words"] = desc_row["words"][1:]
            # Adjust coordinates to start after position number
            desc_row.coordinates2 = (
                row.coordinates2[0] + pos_width,
                row.coordinates2[1],
                row.coordinates2[2],
                row.coordinates2[3]
            )
            desc_row.x0 = row.coordinates2[0] + pos_width
            new_rows.append(desc_row)

    # Only perform the drop and concat if we found compound entries
    if indices_to_drop:
        # Create a copy to avoid modifying the original
        result_df = df.drop(indices_to_drop).copy().reset_index(drop=True)

        # Add the new rows
        if new_rows:
            new_df = pd.DataFrame(new_rows).reset_index(drop=True)
            result_df = pd.concat([result_df, new_df], ignore_index=True).reset_index(drop=True)
        # Sort by y0 and x0 again to maintain correct order
        result_df = result_df.sort_values(by=['y0', 'x0'])
        return result_df

    # If no compound entries found, return the original dataframe
    # Add this debugging code inside preprocess_compound_entries
    if len(indices_to_drop) > 0:
        print(f"Preprocessed {len(indices_to_drop)} compound entries on page {page_num + 1}")
        # You can add more detailed logging here if needed

    return df

def check_bbox_overlap(bbox1, bbox2):
    """Check if two bounding boxes overlap"""
    x0_1, y0_1, x1_1, y1_1 = bbox1
    x0_2, y0_2, x1_2, y1_2 = bbox2

    # Check if one rectangle is to the left of the other
    if x1_1 < x0_2 or x1_2 < x0_1:
        return False

    # Check if one rectangle is above the other
    if y1_1 < y0_2 or y1_2 < y0_1:
        return False

    return True

def filter_data_within_table(page_num, raw_data, rect, table_type):
    def is_within_rect(coords, rect):
        # # Convert string representation of coordinates to a tuple of floats
        # coords = eval(coords_str)
        # # Create a Rect object from the coordinates
        # item_rect = fitz.Rect(coords)
        # # Check if the item's rectangle intersects with the table rectangle
        # return rect.intersects(item_rect)

        # print(f"filter table range: \n\n{raw_data.columns}")

        try:
            # If coords is already a tuple, use it directly
            if isinstance(coords, tuple):
                item_rect = fitz.Rect(coords)
            else:
                # If it's a string, try to evaluate it
                coords = ast.literal_eval(coords)
                item_rect = fitz.Rect(coords)

            # Check if the item's rectangle intersects with the table rectangle
            return rect.intersects(item_rect)

        except (SyntaxError, ValueError, TypeError) as e:
            print()
            logger.warning(f"Warning: Unable to parse coordinates on page {page_num + 1}: Coordinates: {coords}, Rect: {rect}. Error: {e}", exc_info=True)
            return False

    # Apply the filter to the DataFrame
    filtered_data = raw_data[raw_data['coordinates2'].apply(lambda x: is_within_rect(x, rect))]

    #debug_raw_with_error = pd.DataFrame(raw_data)

    # # Rename the columns
    # column_mapping = {
    #     "sys_path": "PdfPath",
    #     "pdf_page": "PdfPage",
    #     "value": "Text",
    #     #"coordinates2": "Coordinates",
    #     "flags": "Flags",
    #     "created_date": "CreationDate",
    #     "mod_date": "ModDate"
    # }

    # filtered_data = filtered_data.rename(columns=column_mapping)

    # Ensure 'Coordinates' column contains tuples, not strings
    # filtered_data['coordinates2'] = filtered_data['coordinates2'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
    filtered_data.loc[:, 'coordinates2'] = filtered_data['coordinates2'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x) # Avoid pandas SettingWithCopyWarning
    # try:
    #     # Convert 'Fontsize' to numeric
    #     filtered_data['font_size'] = pd.to_numeric(filtered_data['font_size'], errors='coerce')
    # except Exception as e:
    #     logger.error(f"Error setting 'font_size' to numeric. {e}", exc_info=True)

    # Add empty columns
    new_columns = ["Type", "Subject", "Contents", "outlierScope"]
    # for col in new_columns:
    #     # filtered_data[col] = ""
    #     filtered_data.loc[:, col] = "" # Avoid pandas SettingWithCopyWarning

    filtered_data = filtered_data.assign(**{col: "" for col in new_columns})

    # Assign table_type to outlierScope column
    filtered_data["outlierScope"] = table_type

    return filtered_data


def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def has_non_standard_characters(text):
    return any(ord(char) > 127 for char in text)

def apply_replacement_function(df):
    return df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)


# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        # if non_standard in text:
        #     print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text

def adjust_bbox_90_swapped(bbox, rotation_matrix):
    """
    Adjust a bounding box for a 90-degree rotation by applying the rotation matrix
    and then swapping the x and y coordinates.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1), with x and y coordinates swapped.
    """
    #logger.debug("--> adjust_bbox_90_swapped accessed")
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Swap the coordinates directly, assigning transformed y to x and x to y
    # This corrects for the coordinate swap that occurs with a 90-degree rotation
    x0, y0 = p1_transformed.y, p1_transformed.x
    x1, y1 = p2_transformed.y, p2_transformed.x

    # It's important to ensure x0 < x1 and y0 < y1 for consistency
    adjusted_bbox = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_bbox

def adjust_clip_for_90_degree_rotation(rect, page):
    """
    Adjust a clipping rectangle for a 90-degree clockwise rotation.

    Parameters:
    - rect: The original clipping rectangle (x0, y0, x1, y1).
    - page: The page object for accessing dimensions.

    Returns:
    - The adjusted clipping rectangle as a tuple (x0, y0, x1, y1), correctly positioned for the rotated page.
    """

    logger.debug("--> adjust_clip_for_90_degree_rotation accessed")
    page_height = page.rect.height
    x0, y0 = rect[1], page_height - rect[2]
    x1, y1 = rect[3], page_height - rect[0]
    adjusted_clip = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_clip

def analyze_and_filter_outliers(page_num, raw_table_df, table_type, font_size_tolerance=0.5): # Changed
    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Initialize DataFrames
    red_text_df = pd.DataFrame()
    non_red_text_df = pd.DataFrame()
    text_outliers_df = pd.DataFrame()

    # Ensure 'color' column is a tuple of three values for the entire DataFrame
    raw_table_df['color'] = raw_table_df['color'].apply(lambda x: tuple(x) if isinstance(x, (tuple, list, np.ndarray)) and len(x) == 3 else (0, 0, 0))

    # Separate annotations and text data
    annotations_df = raw_table_df[raw_table_df['type'] != 'Text'].copy()
    text_df = raw_table_df[raw_table_df['type'] == 'Text']

    # Add annotations to outliers immediately
    # annotations_df['drop_reason'] = 'Annotation'

    # Separate red text and non-red text
    red_text_df = text_df[text_df['color'] == red_color]
    non_red_text_df = text_df[text_df['color'] != red_color]

    # Add this line to filter out 'Times-Roman' font - DEBUG TEMPORARY! Lines contains hidden characters!!
    non_red_text_df = non_red_text_df[non_red_text_df['font'] != 'Times-Roman']

    # non_red_text_df['font_size'] = pd.to_numeric(non_red_text_df['font_size'], errors='coerce')

    # Find common attributes from non-red text
    if len(non_red_text_df) > 0:
        common_font = non_red_text_df['font'].mode()[0]
        common_font_size = non_red_text_df['font_size'].mode()[0]
        common_font_color = non_red_text_df['color'].mode()[0]
    elif len(text_df) > 0:
        # If all text is red, use the most common attributes from all text
        common_font = text_df['font'].mode()[0]
        common_font_size = text_df['font_size'].mode()[0]
        common_font_color = text_df['color'].mode()[0]
    else:
        # Both are blank
        common_font, common_font_size, common_font_color = None, None, None

    # print(f"\n\nPAGE {page_num + 1}:\n COMMON FONT: {common_font} \n SIZE: {common_font_size} \n COLOR: {common_font_color}")

    # Create separate conditions for each outlier reason
    if common_font:
        font_condition = non_red_text_df['font'] != common_font
        size_condition = abs(non_red_text_df['font_size'] - common_font_size) > font_size_tolerance

        # Combine conditions
        outlier_condition = font_condition | size_condition

        # # Create a DataFrame for outliers with reasons
        # outliers_df = non_red_text_df[outlier_condition].copy()
        # outliers_df['drop_reason'] = ''
        # outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
        # outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
        # outliers_df['drop_reason'] = outliers_df['drop_reason'].str.rstrip('; ')

        # Create a DataFrame for outliers with reasons
        text_outliers_df = non_red_text_df[outlier_condition].copy()
        text_outliers_df['outlier_reason'] = ''
        text_outliers_df.loc[font_condition, 'outlier_reason'] += 'Uncommon Font; '
        text_outliers_df.loc[size_condition, 'outlier_reason'] += 'Font Size Outlier; '
        text_outliers_df['outlier_reason'] = text_outliers_df['outlier_reason'].str.rstrip('; ')

    # Combine text outliers with annotations
    outliers_df = pd.concat([text_outliers_df, annotations_df])


    # Regular data is non-red text that's not an outlier, plus all red text
    if common_font is not None:
        regular_data_df = pd.concat([non_red_text_df[~outlier_condition], red_text_df])
    else:
        regular_data_df = text_df  # If no common font, all text data is considered regular

    # regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")
    # outliers_df.to_excel(f"Outliers DF Pg {page_num + 1}.xlsx")

    # print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df

def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):

    print_converted_coords = False

    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ROI payload: {e}")
        return []

    # Handle case where roi_payload might not be a list
    if not isinstance(roi_payload, list):
        logger.error(f"ROI payload is not a list, got {type(roi_payload)}. Attempting to extract list from payload.")
        # Try to find a list within the payload
        if isinstance(roi_payload, dict):
            # Look for common keys that might contain the ROI data
            for key in ['rois', 'roi_data', 'tables', 'coordinates', 'payload']:
                if key in roi_payload and isinstance(roi_payload[key], list):
                    roi_payload = roi_payload[key]
                    print(f"Found ROI data in key '{key}'")
                    break
            else:
                logger.error("Could not find ROI list in payload structure")
                return []
        else:
            logger.error(f"Unexpected ROI payload type: {type(roi_payload)}")
            return []

    converted_payload = []

    for i, item in enumerate(roi_payload):
        try:
            # Handle case where item might be a string instead of dict
            if isinstance(item, str):
                logger.warning(f"Item {i} is a string, skipping: {item[:50]}...")
                continue
            elif not isinstance(item, dict):
                logger.warning(f"Item {i} is not a dict, got {type(item)}, skipping")
                continue

            converted_item = {"columnName": item.get("columnName", f"Unknown_{i}")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            if "headersSelected" in item:
                converted_item["headersSelected"] = item["headersSelected"]

            converted_payload.append(converted_item)
        except Exception as e:
            item_name = item.get("columnName", f"Item_{i}") if isinstance(item, dict) else f"Item_{i}"
            logger.error(f"Error processing item {item_name}: {e}")
            if debug_centered_fix:
                print(f"Debug: Problem item {i}: {type(item)} = {str(item)[:100]}...")

    if print_converted_coords:
        print(f"WIDTH: {width}, Height: {height}")
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))

def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }

# Convert the JSON string back to a list of dictionaries, but only if it's a string
def safe_json_loads(item):
    if isinstance(item, str):
        try:
            return json.loads(item)
        except json.JSONDecodeError:
            return item  # Return the original string if it's not valid JSON
    return item  # Return the item as is if it's not a string


# ^^^^^ General Functions ^^^^^
# Debugging: Check for non-standard characters before and after replacement
def debug_non_standard_characters(df, step):
    for col in df.columns:
        non_standard = df[df[col].apply(lambda x: has_non_standard_characters(str(x)))]
        #if not non_standard.empty:
            #print(f"Non-standard characters found in column '{col}' at step '{step}':")
            #print(non_standard)

def sort_by_y0(df):
    if 'coordinates2' in df.columns:
        # Create a new column 'y0' to hold the y0 values from the 'Coordinates' tuples
        df['y0'] = df['coordinates2'].apply(lambda coord: coord[1])
        # Sort the DataFrame by the 'y0' column
        df = df.sort_values(by='y0')
        # Drop the temporary 'y0' column
        df = df.drop(columns=['y0'])
        return df
    else:
        logger.warning("Column 'coordinates2' does not exist in the DataFrame.")
        return df

def get_table_coordinates(converted_roi_payload):
    identified_tables = {}  # Dictionary to hold coordinates and column details for each table type

    try:
        for item in converted_roi_payload:
            table_type = item.get('columnName', '').lower()
            # Check if the current item is one of the identified table types and has table coordinates
            if table_type in TABLE_NAMES and 'tableCoordinates' in item:
                # logger.debug(f"Found '{table_type}' item with table coordinates")

                # Extract table coordinates
                table_coords = item['tableCoordinates']

                # Initialize list to hold column coordinates for the current table
                column_coords = []
                # Extract column coordinates
                if 'tableColumns' in item:
                    for column_dict in item['tableColumns']:
                        for column_name, coords in column_dict.items():
                            coords['columnName'] = column_name  # Add columnName for compatibility
                            column_coords.append(coords)

                # Get the value of 'headersSelected'
                headers_selected = item.get('headersSelected', False)

                # print("\n\nHEADERS SELECTED", headers_selected)

                # Correctly structure the coordinates and column details for compatibility
                identified_tables[table_type] = (table_coords, column_coords, headers_selected)

    except Exception as e:
        logger.error(f"Error getting table coordinates: {e}", exc_info=True)


    return identified_tables # <-- IN USE

def combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5):

    def are_y_coords_close(coord1, coord2, tolerance):
        # Require one y coord closer and the other less strictly close

        # return abs(coord1[1] - coord2[1]) <= tolerance and abs(coord1[3] - coord2[3]) <= tolerance
        yd1 = abs(coord1[1] - coord2[1])
        yd2 = abs(coord1[3] - coord2[3])
        return any([
            (yd1 <= tolerance * 2 and yd2 <= tolerance),
            (yd1 <= tolerance and yd2 <= tolerance * 2)
        ])

    def should_combine(word1, word2, tolerance=x_tolerance):
        return word2['coordinates2'][0] - word1['coordinates2'][2] <= tolerance

    # Sort the dataframe by y0 and then x0 coordinates
    sorted_df = raw_df.sort_values(by=['coordinates2'], key=lambda x: [coord[1] for coord in x])

    combined_texts = {}
    current_line = []

    def process_line(line):
        line.sort(key=lambda x: x[1]['coordinates2'][0])  # Sort by x0

        row_text = " ".join([l[1]["value"] for l in line])
        # combined_text = line[0][1]['Text']
        combined_text = line[0][1]['value']
        start_index = 0

        # Experimental - if horizontal line components are smaller, more likely to be category,
        # so increase tolerance to increase chance of grouping
        x_tol = x_tolerance
        if len(row_text) < 20 and len(line) < 6:
            x_tol = x_tolerance * 3
        else:
            x_tol = x_tolerance

        for j in range(1, len(line)):
            if should_combine(line[j-1][1], line[j][1], x_tol):
                combined_text += ' ' + line[j][1]['value']
            else:
                for x in line[start_index:j]:
                    combined_texts[x[0]] = combined_text
                start_index = j
                # combined_text = line[j][1]['Text']                #
                combined_text = line[j][1]['value']
        for x in line[start_index:]:
            combined_texts[x[0]] = combined_text

    for i, row in sorted_df.iterrows():
        if not current_line or are_y_coords_close(current_line[-1][1]['coordinates2'], row['coordinates2'], y_tolerance):
            current_line.append((i, row))
            continue
        process_line(current_line)
        current_line = [(i, row)]  # New line

    # Process the last line
    if current_line:
        process_line(current_line)

    # Create a new dataframe with the combined texts
    combined_df = pd.DataFrame.from_dict(combined_texts, orient='index', columns=['Combined_Text'])

    # Update or add the 'Combined_Text' column efficiently
    if 'Combined_Text' in sorted_df.columns:
        sorted_df.update(combined_df)
    else:
        sorted_df = sorted_df.join(combined_df)

    return sorted_df

def is_keyword(text, prefixes, keywords, max_words=3):
    '''
    Checks keywords like Material Labels/Descriptors, Install Type (Shop,Field etc)
    to determine whether or not to drop/ignore them when creating the table structure
    '''

    # Convert text to uppercase for case-insensitive matching
    text = text.strip().upper()

    # Split the text into words
    words = text.split()

    # Check if the text starts with any of the prefixes and has <= max_words
    if len(words) <= max_words and any(text.startswith(prefix) for prefix in prefixes):
        #print(f"\n\nDEBUG: '{text}' is a keyword (prefix match and short)")
        return True

    # Check if the entire text matches any of the keywords
    if text in keywords:
        #print(f"DEBUG: '{text}' is a keyword (exact match)")
        return True

    return False

def word_count(text):
    return len(text.split())

def starts_with_prefix_and_short(text, prefixes, max_words=2):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False

    words = text.split()
    result = any(text.startswith(prefix) for prefix in prefixes) and len(words) <= max_words
    # if result:
    #     print(f"DEBUG: '{text}' starts with prefix {[p for p in prefixes if text.startswith(p)]} and has {len(words)} words")
    return result

def keyword_in_first_words(text, keywords, first_n_words=5, min_total_words=4):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False

    words = text.split()

    # Check for exact match first
    if text in keywords:
        return True

    if len(words) < min_total_words:
        return False

    # Only check the first 'n' words
    first_words = ' '.join(words[:first_n_words])

    # Check if any of the keywords match the first 'n' words
    result = any(keyword in first_words for keyword in keywords)
    #print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")

    # if result:
    #     print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    # else:
    #     print(f"DEBUG: '{text}' does not contain a keyword in first {first_n_words} words")
    return result
    # return any(keyword in first_words for keyword in keywords)

def preprocess_text(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = ''.join(c for c in unicodedata.normalize('NFD', text) if unicodedata.category(c) != 'Mn')
    text = re.sub(r'[^\w\s]', '', text)
    text = ' '.join(text.split())
    return text

def find_exact_keyword_match(text, keywords):
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if preprocess_text(keyword) == processed_text:
            return keyword

    return None

def find_similar_keyword_match(text, keywords, threshold: float = 0.8):
    """Returns keyword if meets similarity threshold"""
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if SequenceMatcher(None, preprocess_text(keyword), processed_text).ratio() >= threshold:
            return keyword

    return None


def get_table_data(pdf_path, page, page_num, converted_roi_payload, raw_data_df, missing_pos=False, remove_outliers=True, skip_number_check=None): # <-- IN USE
    #get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data, missing_pos=False, remove_outliers=True): # <-- IN USE
    logger.info("Getting table data")

    # Use global flag if not explicitly passed
    if skip_number_check is None:
        skip_number_check = SKIP_NUMBER_CHECK

    # raw_data_debug = pd.DataFrame(raw_data)

    '''
    -> Add more lists for Material Descriptors/Labels

    -> change outlier data to not filter the values out, but to flag/ignore the values instead. Need additional column
    -> Outlier data to keep word with outlier and without for easy merging later (Let the user decide)
    -> Add reason for outlier
    -> Check font color
    -> check type (text, annot)
    -> Add dropped keywords somewhere for review
    -> check for header values using similar logic to 'combine_nearby_words'

    '''

    # Dictionaries to store DataFrames for each table type
    structured_tables = {}
    annotations_tables = {}
    outliers_tables = []
    combined_outliers_df = pd.DataFrame()

    # logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()

    # Set file info
    filename = os.path.basename(pdf_path)
    directory_path = os.path.dirname(pdf_path)
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)

    # --> Find the items (e.g., 'BOM', 'SPEC', 'Spool') and their table coordinates
    identified_tables = get_table_coordinates(converted_roi_payload)  # Adjust this function to return a list of table types with their coordinates

    #print("IDENTIFIED TABLES: ", identified_tables)

    #print("\n\n --> TABLES: ", identified_tables)

    if not identified_tables:
        logger.error("No table coordinates found for items")
        # Return empty DataFrames for each expected table type if needed
        return structured_tables, annotations_tables, combined_outliers_df

    # List of header values and ignore terms
    header_values = ["PT", "PT.", "ID", "NO", "DESCRIPTION", "NPD", "(IN)", "CMDTY CODE", "QTY", "IDENT", "QTY", "POS",
            "HOLD", "COMPONENT DESCRIPTION", "N.B. (INS)", "ITEM CODE", "N.B.", "(INS)", "N.P.S.", "(IN)",
            "N.S. (INS)", "N.S.", "N.P.S.", "MATERIALS", "GMN", "MK", "MK.", "MK NO", "MK NO.", "MK NO NPD", "MK NO NPD (IN)",
            "NO NPD", "NPD","SPEC", "PART NO", "PART NO.", "PART", "MK NPD","NO.", "ND", "ND.", "BILL OF MATERIALS", "SIZE", "NO (IN)", "DN",
            "SCH/CLASS", "MATERIAL CODE"]

    # ignore_terms = ["PIPE SUPPORTS", "CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
    #                 "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS",

    #                 "FLANGES", "GASKETS", "BOLTS", "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS",
    #                 "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    # ignore_terms = ["CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
    #                 "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS",
    #                 "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    ignore_terms = ["CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
                    "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)",
                    "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S", "HOLD", "HARDWARES"
                    ] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    bottom_limit_stop_flag = ["PIECE MARKS", "PIECE MARK", "[1]", "[2]", "[3]", "[4]", "[5]", "[6]", "[7]" ,"PIPE SPOOLS", "PIPE SPOOL",
                              "NOTE:", "1.GM"]

    field_keywords = ["ERECTION ITEMS","ERECTION MATERIALS", "ERECTION", "OTHER THAN SHOP MATERIALS", "FIELD INSTALL"]
    shop_keywords = ["FABRICATION ITEMS", "FABRICATION MATERIALS", "FABRICATION","SHOP MATERIALS", "FABRICATION MATERIALS COMPONENT DESCRIPTION"] # "FABRICATION",
    misc_keywords = ["MISC.", "MISCELLANEOUS COMPONENTS", "OFFSHORE", "OFFSHORE MATERIALS", ]
    instr_keywords = ["INSTRUMENTS", "MISCELLANEOUS COMPONENTS"]

    component_keywords = ["BOLTS", "PIPE", "FITTINGS", "FLANGES", "GASKETS", "VALVES", "INSTRUMENTS", "INSTR.",
                          "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS", "MISCELLANEOUS COMPONENTS",
                          "MISC.","PIPE SUPPORTS", "OLETS", "FASTENERS", "GASKETS / BOLTS", "WELDS"]

    # Define the prefixes for partial matches (Checks if it begins to classify the Material Scope)
    field_prefixes = ("FIELD", "ERECTION", "OTHER THAN SHOP")
    shop_prefixes = ("SHOP", "FABRICAT", "OTHER THAN FIELD")
    misc_prefixes = ("MISCELLANEOUS",)  # Note the comma to make it a tuple with one element
    instr_prefixes = ("INSTR/", "ISTR", "INSTRUMENTS")

    # Define list of Exceptions
    exceptions = ["I-ROD", "SUPPORT - UBOLT", "SUPPORT - GUIDE", "SUPPORT - ANCHOR", "SHIM PLATE", "INSTRUMENT COMPONENT", "INSTRUMENT COMPONENTS"]

    # Combine all keywords and prefixes into a single list
    all_keywords = field_keywords + list(field_prefixes) + shop_keywords + list(shop_prefixes) #+ misc_keywords + list(misc_prefixes)# + instr_keywords + list(instr_prefixes)
    #all_keywords = field_keywords + field_prefixes + shop_keywords + shop_prefixes

    # Ensure all prefixes are tuples
    field_prefixes = tuple(field_prefixes)
    shop_prefixes = tuple(shop_prefixes)
    misc_prefixes = tuple(misc_prefixes)
    instr_prefixes = tuple(instr_prefixes)

    # Ensure all keywords are lists
    field_keywords = list(field_keywords)
    shop_keywords = list(shop_keywords)
    misc_keywords = list(misc_keywords)
    instr_keywords = list(instr_keywords)

    # Prepare Raw data
    if isinstance(raw_data_df, pd.DataFrame):
        if debug_mode:
            logger.debug("Using Raw DataFrame directly")
    else:
        try:
            raw_data_df = pd.DataFrame(raw_data_df)
        except Exception as e:
            logger.error(f"Error converting to DataFrame: {e}")
            raw_data_df = pd.DataFrame()  # Create an empty DataFrame if conversion fails

    if not raw_data_df.empty:

        # print(f"\n\n\n--> TESTING: Exporting raw_data_df - get_tables_test\n   Rows: {len(raw_data_df)}\n\n-------------------------")
        # raw_data_df.to_excel("_DEBUG raw_data_df get_tables_test.xlsx")
        try:
            raw_data_df['font_size'] = pd.to_numeric(raw_data_df['font_size'], errors='coerce')
        except Exception as e:
            logger.error(f"Error converting 'font_size' to numeric: {e}", exc_info=True)

        # Option 2
        # raw_data_df['Fontsize'] = raw_data_df['Fontsize'].astype(float)

        # Convert the JSON string back to a list of dictionaries
        #raw_data_df['words'] = raw_data_df['words'].apply(json.loads)
        try:
            raw_data_df['words'] = raw_data_df['words'].apply(safe_json_loads)
        except Exception as e:
            logger.error("No `words` in raw_data_df", exc_info=True)

        # Filter raw_data_df where 'pdf_page' is equal to page_num + 1
        raw_data_df = raw_data_df[raw_data_df['pdf_page'] == page_num + 1]

        # Filter annotations_df
        # print(f"ANNOT COLUMNS: {annotations_df.columns}")
        annotations_df = raw_data_df[raw_data_df['type'] != 'Text']

    # Find all overlaps with each table first using spatial indexing
    table_regions = {}
    for table_type, (table_coords, column_coords, headers_selected) in identified_tables.items():
        rect = (table_coords['x0'], table_coords['y0'], table_coords['x1'], table_coords['y1'])
        table_regions[table_type] = rect

    text_bboxes = raw_data_df[['x0', 'y0', 'x1', 'y1']].values.tolist()
    overlaps = find_overlapping_rectangles_with_areas(table_regions.values(), text_bboxes)
    overlaps = {list(table_regions.keys())[i]: values for i, values in overlaps.items()} # Gives table_type to overlapping indices mapping

    for table_type, roi_overlaps in overlaps.items():

        outliers_df = pd.DataFrame()
        annot_outliers_df = pd.DataFrame()
        collected_outliers = [] # Other collected outliers

        column_coords = identified_tables[table_type][1]

        # Could perform some valid overlap logic
        valid_overlaps = [index for index, _ in roi_overlaps]

        if valid_overlaps:
            raw_table_df = raw_data_df.iloc[valid_overlaps].copy()
        else:
            print()
            logger.error(f"Error in filter_data_within_table: {e}", exc_info=True)
            raw_table_df = pd.DataFrame()

        if debug_mode:
            annotations_df.to_excel("Annot - get_table_data.xlsx")
            print(f"\n--------------Page: {page_num + 1}\n--------------")

        if raw_table_df.empty and annotations_df.empty:
            logger.warning(f"No data found for table type '{table_type}' on page {page_num}. Returning empty DataFrames.")
            continue  # Skip to the next table type

        current_material_scope = None
        current_component_category = None
        component_match = None

        if not raw_table_df.empty:

            # Add debugging print statements
            if debug_mode:
                print(f"\n\nColumns in raw_df: {raw_table_df.columns}")
                print(f"Shape of raw_df: {raw_table_df.shape}")


            # Apply non ASCII replacement function and debug
            raw_table_df = apply_replacement_function(raw_table_df)

            # Group phrases that the replacement functions can analyze
            raw_table_df = combine_nearby_words(raw_table_df, y_tolerance=2, x_tolerance=8)

            raw_table_df = sort_by_y0(raw_table_df)

            # Create a copy for filtering
            filtered_df = raw_table_df.copy()

            # Find the y0 value from the row where the text matches any value in bottom_limit_stop_flag
            bottom_limit_y0 = None

            for term in bottom_limit_stop_flag:
                if term in filtered_df['Combined_Text'].values:
                    bottom_limit_y0 = filtered_df.loc[filtered_df['Combined_Text'] == term, 'coordinates2'].iloc[0][1]
                    break

            # Drop rows where y0 is greater than or equal to bottom_limit_y0
            if bottom_limit_y0 is not None:
                filtered_df = filtered_df[filtered_df['coordinates2'].apply(lambda coord: coord[1] < bottom_limit_y0)]

            # Filter out rows where the "Combined_Text" column matches any value in filter_terms
            filter_terms = header_values + ignore_terms

            filtered_df = filtered_df[~filtered_df['Combined_Text'].isin(filter_terms)]

            # # DEBUG!
            # debug_drop_df_1 = filtered_df[filtered_df['Combined_Text'].isin(filter_terms)]

            # debug_drop_df_1.to_excel(f"_Debug-1 Page {page_num} Drop Terms.xlsx")

            #filtered_df.to_excel(f"Filtered DF {page_num}.xlsx")

            filtered_df.fillna({"Combined_Text": ""}, inplace=True)
            filtered_df["Combined_Text"] = filtered_df["Combined_Text"].str.strip().str.upper()

            filtered_df['is_component_keyword'] = None
            # Distinguish Shop/Fabrication Items
            for row in filtered_df.itertuples():
                index = row.Index
                text = row.Combined_Text

                # print(f"{page_num + 1} Text: {text}")

                # Check for prefixes first
                if starts_with_prefix_and_short(text, field_prefixes):
                    current_material_scope = "Field"
                elif starts_with_prefix_and_short(text, shop_prefixes):
                    current_material_scope = "Shop"
                elif starts_with_prefix_and_short(text, misc_prefixes):
                    current_material_scope = "Misc."
                # elif starts_with_prefix_and_short(text, instr_prefixes):
                #     current_material_scope = "Instr."

                # If no prefix match, check for keywords
                elif keyword_in_first_words(text, field_keywords):
                    current_material_scope = "Field"
                elif keyword_in_first_words(text, shop_keywords):
                    current_material_scope = "Shop"
                elif keyword_in_first_words(text, misc_keywords):
                    current_material_scope = "Misc."
                # elif keyword_in_first_words(text, instr_keywords):
                #     current_material_scope = "Instr."
                # else:
                #     current_material_scope = None

                # elif find_exact_keyword_match(text, component_keywords):
                #     current_component_category = result

                # Check for exact keyword match (for component category)
                # component_match = find_exact_keyword_match(text, component_keywords)

                component_match = find_similar_keyword_match(text, component_keywords)
                if component_match:
                    current_component_category = component_match
                    filtered_df.at[index, 'is_component_keyword'] = True

                # Assign the current material scope to the row
                if current_material_scope:
                    filtered_df.at[index, 'Material Scope'] = current_material_scope

                # Assign the current component category to the row
                if current_component_category:
                    # print(f"COMPONENT MATCH: {current_component_category}")
                    filtered_df.at[index, 'componentCategory'] = current_component_category
                    # print(f"Current Material Scope Pg. {page_num}: {current_material_scope}")

            # Filter rows based on the new criteria
            all_prefixes = field_prefixes + shop_prefixes + misc_prefixes #+ instr_prefixes
            all_keywords = field_keywords + shop_keywords + misc_keywords #+ instr_keywords

            # Instead of dropping rows, let's mark them
            filtered_df['is_keyword'] = filtered_df['Combined_Text'].apply(lambda x:
                (starts_with_prefix_and_short(x, all_prefixes) or
                keyword_in_first_words(x, all_keywords)) and
                x.strip().upper() not in exceptions  # Add this condition
            )

            # Mark rows for component category keywords
            # filtered_df['is_component_keyword'] = filtered_df['Combined_Text'].apply(lambda x:
            #     # find_exact_keyword_match(x, component_keywords) is not None
            #     find_similar_keyword_match(x, component_keywords) is not None
            # )

            # Add keyword-matched rows to outliers_df
            keyword_matches = filtered_df[filtered_df['is_keyword'] | filtered_df['is_component_keyword']].copy()
            if not keyword_matches.empty:
                keyword_matches['outlier_reason'] = 'Keyword Match'
                outliers_df = pd.concat([outliers_df, keyword_matches], ignore_index=True)

            # Now, actually filter out the rows marked as keywords
            # Now, filter out the rows marked as keywords or component keywords
            filtered_df = filtered_df[~(filtered_df['is_keyword'] | filtered_df['is_component_keyword'])]

            # filtered_df = filtered_df[~filtered_df['is_keyword']]

            # debug_drop_df_2 = filtered_df[filtered_df['is_keyword']]

            # # DEBUG!
            # debug_drop_df_2.to_excel(f"_Debug-2 Page {page_num} Drop Terms.xlsx")

            # Drop the 'is_keyword' column as it's no longer needed
            #filtered_df = filtered_df.drop('is_keyword', axis=1)

            #print(f"-->Final shape of filtered_df: {filtered_df.shape}")
            raw_table_df = filtered_df.copy()

            # Continue with the rest of your processing using raw_table_df
            structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df,
                                                                                                    annot_outliers_df, column_coords, headers_selected,
                                                                                                    filename, pdf_path, page_num, parent_folders_str,
                                                                                                    table_type, True, remove_outliers=True, skip_number_check=skip_number_check)
            # print(f"Exporting Structured Table Page {page_num}....")
            #structured_table_df.to_excel(f"_Page {page_num} Structured Table.xlsx")

        #
        # --> Process and structure the raw table data and annotations (Outlier, table creation)
        else:
            if not annotations_df.empty:

                print(f"\n\n\n--> TESTING: Exporting annotations_df - get_tables_test\n   Rows: {len(annotations_df)}\n\n-------------------------")
                # annotations_df.to_excel("_DEBUG annotations_data get_tables_test.xlsx")

                structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df,
                                                                                                        annot_outliers_df, column_coords, headers_selected,
                                                                                                        filename, pdf_path, page_num, parent_folders_str, table_type, skip_number_check=skip_number_check)


        # List column names from column_coords based on 'columnName' field
        try:
            column_names = [col['columnName'] for col in column_coords]
        except Exception as e:
            logger.error(f"Could not get ROI table column names: {e}", exc_info = True)

        # Initialize a list to hold indices of outlier rows
        outlier_indices = []
        insufficient_values = []
        # Add outlier detection and logging here:
        try:
            for index, row in structured_table_df.iterrows():
                # Count non-empty and non-NaN values in the specified columns
                valid_values = row[column_names].replace('', np.nan).dropna().shape[0]
                if valid_values <= 1:  # Checking for rows with only one or no valid entries
                    #print(f"Outlier detected in {table_type} table at row {index}: {row[column_names].to_dict()}")
                    outlier_indices.append(index)  # Append index of outlier to list
                    row_dict = row[column_names].to_dict()
                    outlier = {
                        "pdf_page": page_num + 1,
                        "value": row_dict,
                        "outlier_reason": "Insufficient row values",
                        "outlier_action": "dropped",
                    }
                    insufficient_values.append(outlier)
        except Exception as e:
            logger.error(f"Error looping ROI column names to detect outlier rows in {table_type}: {e}", exc_info=True)

        # Remove outliers from structured_table_df
        if remove_outliers and outlier_indices:
            structured_table_df = structured_table_df.drop(outlier_indices)
            # logger.info(f"Removed {len(outlier_indices)} outliers from {table_type} table.")
        else:
            insufficient_values = []

        outliers_df["outlier_scope"] = table_type
        outliers_df["outlier_scope_2"] = "table_text"
        annot_outliers_df["outlier_scope"] = table_type
        annot_outliers_df["outlier_scope_2"] = "table_annots"

        structured_tables[table_type] = structured_table_df
        annotations_tables[table_type] = annotations_df

        # Outliers are collected and combined outside the loop
        outliers_tables.append(outliers_df)
        outliers_tables.append(annot_outliers_df)

        collected_outliers.extend(insufficient_values)
        if collected_outliers:
            collected_outliers = pd.DataFrame(collected_outliers)
            collected_outliers["outlier_scope"] = table_type
            collected_outliers["outlier_scope_2"] = "table_other"
            # outliers_df.append(collected_outliers)

    outliers_tables = [t for t in outliers_tables if not t.empty]
    combined_outliers_df = pd.concat(outliers_tables, ignore_index=True) if outliers_tables else pd.DataFrame()

    #print("\n\nCombined Outliers:", combined_outliers_df.head())  # For debugging

    return structured_tables, annotations_tables, combined_outliers_df


def process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords, headers_selected, filename,
                       pdf_path, page_num, parent_folders_str, table_type, include_annot_rows=False, remove_outliers=True, skip_number_check=False):

    if table_type == "bom":
        numbered_rows = True

    structured_table_df = pd.DataFrame()

    #Identify outliers in the text data and logically structure it
    if len(raw_table_df) > 0:
        # export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )

        from __features__ import LEGACY_EXTRACTION_FONT_SIZE_TOLERANCE, LEGACY_EXTRACTION_REMOVE_OUTLIERS
        font_size_tolerance = LEGACY_EXTRACTION_FONT_SIZE_TOLERANCE
        remove_outliers = LEGACY_EXTRACTION_REMOVE_OUTLIERS

        # Regular data is the data where the majority of the data is the the same font, font size (Red text is added to regular data to preserve revisions)
        potential_outliers_df, regular_data_df = analyze_and_filter_outliers(page_num, raw_table_df, table_type, font_size_tolerance)

        # Filter out outliers from the raw data - REMOVE OUTLIERS
        # structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
        if remove_outliers and len(regular_data_df) > 10: # Temporary workaround
            #print("REMOVE OUTLIERS = TRUE")
            structured_data = regular_data_df #raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
            outliers_data = potential_outliers_df
            outliers_data["outlier_action"] = "dropped"
        else:
            #print("REMOVE OUTLIERS = FALSE")
            structured_data = raw_table_df
            outliers_data = pd.DataFrame()  # Empty DataFrame if not removing outliers

        structured_table_df = create_logical_structure_text(page_num, structured_data, column_coords, headers_selected, table_type,
                                                            numbered_rows=False, include_annot_rows=False, annot_table=False, y_tolerance=5, skip_number_check=skip_number_check)

        # Insert columns at the beginning of the dataframe
        structured_table_df.insert(0, 'pdf_page', page_num + 1)
        structured_table_df.insert(0, 'sys_filename', filename)
        structured_table_df.insert(0, 'sys_path', pdf_path)
        structured_table_df.insert(0, 'sys_build', parent_folders_str)

        # Process outliers_df
        outliers_df = pd.concat([outliers_df, outliers_data], ignore_index=True)

    if len(annotations_df):

        if debug_mode:
            annotations_df.to_excel("Annot Before create_logical_structure.xlsx")

        #annotations_df = create_logical_structure_2(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)

        # print("\n\nANNOT - Calling create_logical_structure_annot")
        if include_annot_rows and not structured_table_df.empty:
            pass # Annotations will be included with text dataframe
        else:
            annotations_df = create_logical_structure_annot(annotations_df, column_coords, annot_table=True)

        try:
            # Insert columns at the beginning of the dataframe
            annotations_df.insert(0, 'pdf_page', page_num + 1)
            annotations_df.insert(0, 'sys_filename', filename)
            annotations_df.insert(0, 'sys_path', pdf_path)
            annotations_df.insert(0, 'sys_build', parent_folders_str)
            # annotations_df.to_excel("Annot After create_logical_structure.xlsx")
        except Exception as e:
            pass

        if debug_mode:
            annotations_df.to_excel("debug/DEBUG MODE- Annot After create_logical_structure.xlsx")

        #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
    else:
        pass
        # logger.debug("\n\nAnnotations table is empty.")

    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df

def find_spanning_columns(bbox, column_coords):
    """Determine which columns the given bbox spans based on 'x0' and 'x1' coordinates in the column definitions."""
    x1, y1, x2, y2 = bbox
    spanning_columns = []

    # Logging the column coordinates to ensure correct data structure is received
    #logger.debug(f"Column Coords: {column_coords}")

    for idx, col_data in enumerate(column_coords):
        try:
            col_start = col_data['x0']
            col_end = col_data['x1']

            if (x1 < col_end and x2 > col_start):
                spanning_columns.append(idx)

        except KeyError as e:
            logger.error(f"Key error processing column boundaries for index {idx} with data {col_data}: {e}")

    return spanning_columns

def split_text_by_columns(text, original_bbox, column_coords):
    try:
        words = text.split()
        spans = []
        estimated_x_start = original_bbox[0]
        average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Ensure len(text) is not zero to avoid division by zero error
        if len(text) == 0:
            average_char_width = 0
        else:
            average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Calculate bounding boxes for each word
        for word in words:
            word_width = len(word) * average_char_width
            word_bbox = (estimated_x_start, original_bbox[1], estimated_x_start + word_width, original_bbox[3])
            spans.append((word, word_bbox))
            estimated_x_start += word_width + average_char_width  # Account for the space between words

        # Initialize text data for each column
        column_text_data = [('', None)] * len(column_coords)

        # Assign words to columns based on bounding boxes
        for word, bbox in spans:
            assigned = False
            for idx, col_data in enumerate(column_coords):
                col_start = col_data['x0']
                col_end = col_data['x1']

                # Check if the word fits within the column boundaries
                # Adjusting condition to consider word's start point and its midpoint
                if bbox[0] >= col_start and (bbox[0] + bbox[2]) / 2 <= col_end:
                    existing_text, existing_bbox = column_text_data[idx]
                    combined_text = (existing_text + word + ' ') #.strip()
                    if existing_bbox:
                        # Update the bounding box to encompass the new word
                        new_bbox = (existing_bbox[0], existing_bbox[1], bbox[2], existing_bbox[3])
                    else:
                        new_bbox = bbox
                    column_text_data[idx] = (combined_text, new_bbox)
                    assigned = True
                    break
            if not assigned:
                #logger.warning(f"Word '{word}' at bbox {bbox} was not assigned to any column. Assigning to the first column.")
                existing_text, existing_bbox = column_text_data[0]
                combined_text = (existing_text + word + ' ').strip()
                if existing_bbox:
                    new_bbox = (min(existing_bbox[0], bbox[0]), existing_bbox[1], max(existing_bbox[2], bbox[2]), existing_bbox[3])
                else:
                    new_bbox = bbox
                column_text_data[0] = (combined_text, new_bbox)

                #print("\n\nCOLUMN TEXT DATA: \n", column_text_data)

        # Clean up results to remove empty entries and prepare for output
        cleaned_data = [(text.rstrip(), bbox) for text, bbox in column_text_data if text.strip()]
        #print("\n\nCLEANED DATA: \n", cleaned_data)
        return cleaned_data

    except ZeroDivisionError:
        # Return the original data in case of an error
        return [(text, original_bbox)]

def merge_wrapped_rows_text(structured_data, annot_table=False):
    #Convert Data to a Dataframe
    if annot_table: #Return the structured dataframe
        return pd.DataFrame(structured_data)

    structured_df = pd.DataFrame(structured_data)

    if debug_row_merging:
        print("\n=== DEBUG: ROW MERGING STARTED ===")
        print(f"Initial DataFrame has {len(structured_df)} rows")
        print("Initial DataFrame structure:")
        for i, row in structured_df.iterrows():
            print(f"Row {i}:")
            for col in structured_df.columns:
                value = row[col]
                if isinstance(value, str) and len(value) > 50:
                    value = value[:50] + "..."
                print(f"  {col}: {value}")

    # Check if DataFrame is empty
    if len(structured_df) == 0:
        if debug_row_merging:
            print("DataFrame is empty, returning empty DataFrame")
        return structured_df

    # The first column is considered the reference for merging
    try:
        leftmost_column = structured_df.columns[0]
        if debug_row_merging:
            print(f"\nUsing '{leftmost_column}' as the reference column for merging")
    except Exception as e:
        logger.error(f"Could not get left most columns. {e}")
        if debug_row_merging:
            print(f"ERROR: Could not get leftmost column: {e}")
        #structured_df.to_excel(f"Structured_df_merge_wrapped.xlsx")
        return structured_df

    # Iterate backwards through the DataFrame to merge rows
    rows_merged = 0
    for i in range(len(structured_df) - 1, 0, -1):
        # If the leftmost column is empty, merge this row with the one above
        if pd.isna(structured_df.at[i, leftmost_column]) or structured_df.at[i, leftmost_column].strip() == '':
            if debug_row_merging:
                print(f"\n--- Merging row {i} into row {i-1} ---")
                print(f"Row {i} has empty leftmost column value: '{structured_df.at[i, leftmost_column]}'")

            for col in structured_df.columns:
                # Skip the 'material_scope' column entirely
                if col not in ['material_scope', 'has_revision']:
                    # Only merge if the current row's cell is not empty
                    if not pd.isna(structured_df.at[i, col]) and structured_df.at[i, col].strip() != '':
                        if debug_row_merging:
                            print(f"  Merging column '{col}':")
                            print(f"    Before: '{structured_df.at[i-1, col]}'")
                            print(f"    Adding: '{structured_df.at[i, col]}'")

                        structured_df.at[i - 1, col] = structured_df.at[i - 1, col].strip() + ' ' + structured_df.at[i, col].strip()

                        if debug_row_merging:
                            print(f"    After: '{structured_df.at[i-1, col]}'")

            # After merging, drop the current row
            structured_df = structured_df.drop(index=i)
            rows_merged += 1

    # Reset index after dropping rows
    structured_df = structured_df.reset_index(drop=True)

    if debug_row_merging:
        print(f"\n=== ROW MERGING COMPLETED ===")
        print(f"Merged {rows_merged} rows")
        print(f"Final DataFrame has {len(structured_df)} rows")
        if rows_merged > 0:
            print("Final DataFrame structure:")
            for i, row in structured_df.iterrows():
                print(f"Row {i}:")
                for col in structured_df.columns:
                    value = row[col]
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {col}: {value}")

    #export_large_data_to_excel(structured_df,"merged_wrapped_df_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
    return structured_df

def create_logical_structure_text(page_num, raw_table_df, column_coords, headers_selected, table_type,
                                  numbered_rows=True, include_annot_rows=False,annot_table=False, y_tolerance=5, skip_number_check=False):

    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    # --> Debug
    # --> Debug
    # raw_table_df.to_excel(f"debug/Raw Data - create_logical - Pg {page_num + 1}.xlsx")

    table_type = table_type.lower().strip()

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    structured_data = []

    raw_table_df = raw_table_df.copy()

    # DEBUG: Check DataFrame structure before adding coordinate columns
    raw_table_df.loc[:, 'x0'] = raw_table_df['coordinates2'].apply(lambda coords: coords[0])
    raw_table_df.loc[:, 'x1'] = raw_table_df['coordinates2'].apply(lambda coords: coords[2])
    raw_table_df.loc[:, 'y0'] = raw_table_df['coordinates2'].apply(lambda coords: coords[1])

    # Add y1 for our midpoint calculation
    raw_table_df.loc[:, 'y1'] = raw_table_df['coordinates2'].apply(lambda coords: coords[3])

    # file = pd.DataFrame(raw_table_df)
    # file.to_excel('debug/raw_table_df.xlsx')
    raw_table_df_sorted = raw_table_df.sort_values(by=['y0', 'x0'])

    if len(raw_table_df) > 0:
        if table_type == "bom":
            leftmost_column = column_coords[0]

            # Add this line to preprocess and handle compound entries (pos (row numbers) merged with
            # neighboring columns causes rows not to be wrapped. Ex. "10 Some Material Description.")
            raw_table_df_sorted = preprocess_compound_entries(raw_table_df_sorted, leftmost_column)


            try:
                bom_rows, bom_texts, filtered_contents, unassigned_items, skipped_items = detect_bom_rows(page_num, raw_table_df_sorted, leftmost_column, skip_number_check=skip_number_check)

                if debug_row_content:
                    print(f"\n-----------------\nRETURNED BOM ROWS FROM 'detect_bom_rows' \n {bom_rows}\n-----------------\n")
            except Exception as e:
                print()
                logger.error(f"Error in detect_bom_rows on page {page_num + 1}: {e}", exc_info=True)
                print()
                # return empty lists or handle this error in some way
                bom_rows, bom_texts, filtered_contents, unassigned_items = [], [], [], []

            # if unassigned_items:
            if debug_row_content:
                if unassigned_items is not None and len(unassigned_items) > 0:
                    print()
                    print()
                    print(f"-----> ERROR! PG {page_num + 1} - UNASSIGNED TABLE ITEMS!:")
                    for index, text, coords, reason in unassigned_items:
                        #unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
                        logger.info(f" Index: {index}, \n Text: '{text}', \n coordinates2: {coords}, \n Reason: {reason}")

                # if skipped_items
                if skipped_items is not None and len(skipped_items) > 0:
                    print(f"\n\n -----> ERROR! PG {page_num + 1} - INTENTIONALLY SKIPPED TABLE ITEMS!:")
                    for index, text, coords, reason in skipped_items:
                        #unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
                        print(f" Index: {index}, \n Text: '{text}', \n coordinates2: {coords}, \n Reason: {reason}")


            index = 0
            for content in filtered_contents:
                row_texts = []
                for _, item in content.iterrows():
                    # text = item['Contents'] if annot_table else item['Text']
                    text = item['value']
                    # color = item['color']
                    coords = item['coordinates2']
                    words = item.get('words', None)
                    bbox = fitz.Rect(coords)

                    try:
                        color = item['color']
                    except Exception as e:
                        try:
                            color = item.get('Color', None)
                            if color is None:
                                logger.warning(f"Unable to get color value for row at item {row.name}: {e}")
                        except Exception as e2:
                            logger.error(f"Critical error getting color for row at item {row.name}: {e2}")
                            color = None

                        logger.info(f"Color Item Value: {item}")

                    # print(f"\n\nCONTENT: {content}")

                    row_data = {
                        'text': text,
                        'bbox': bbox,
                        'color': color,
                        'words': words,
                        'material_scope': item.get('Material Scope', ''),
                        'componentCategory': item.get('componentCategory', ''),
                        'type': item.get('type', '')
                    }
                    row_texts.append(row_data)

                # file = pd.DataFrame(row_texts)
                # file.to_excel(f'debug/raw_table_df{index}.xlsx')

                # print(f"\n\n----------DEBUG ROW TEXTS (create_logical_structure_text): \n{row_texts}----------------\n\n")
                structured_data.append(assign_texts_to_columns(row_texts, column_coords, column_names, table_type))

                # print(f"\n\nStructured data after assign texts to columns (filtered_contents):\n\n")
                # structured_df_debug = pd.DataFrame(structured_data)
                # pp(structured_data)
                index = index+1

        # For tables that are not 'BOM' type
        else:
            # NEW APPROACH FOR NON-BOM TABLES: Group by y-coordinate first
            # Group items with similar y-coordinates into the same logical row

            # Function to determine if two y-coordinates are close enough to be in the same row
            def are_y_coords_close(y1, y2, tolerance=y_tolerance):
                return abs(y1 - y2) <= tolerance

            # Group items by similar y-coordinates
            row_groups = []
            current_group = []
            last_y = None

            for _, row in raw_table_df_sorted.iterrows():
                # Get y1 from coordinates2 if y1 column doesn't exist
                if 'y1' in row:
                    current_y = (row['y0'] + row['y1']) / 2  # Use midpoint of the text
                else:
                    # Fall back to using coordinates2 directly
                    current_y = (row['coordinates2'][1] + row['coordinates2'][3]) / 2

                if last_y is None or are_y_coords_close(current_y, last_y):
                    # Same row
                    current_group.append(row)
                else:
                    # New row
                    if current_group:
                        row_groups.append(current_group)
                    current_group = [row]

                last_y = current_y

            # Add the last group if it exists
            if current_group:
                row_groups.append(current_group)

            # Process each group as a single row
            for group in row_groups:
                # Collect all words from all items in the group with their coordinates
                all_words = []


                for row in group:
                    text = row['value']
                    coords = row['coordinates2']
                    word_list = row.get('words', [])

                    # Explicitly access font information directly from DataFrame row
                    try:
                        # These are the exact column names from your raw data
                        row_font = row.get('font', None)
                        row_font_size = row.get('font_size', None)
                        row_font_style = row.get('font_style', None)

                        # Print font information to debug
                        if debug_outliers:
                            print(f"\n*** FONT INFO: '{text}': font={row_font}, size={row_font_size}, style={row_font_style}")
                    except Exception as e:
                        print(f"*** ERROR accessing font information: {e}")
                        row_font = None
                        row_font_size = None
                        row_font_style = None

                    # If we have a words list with detailed word positions, use it
                    if word_list and isinstance(word_list, list) and len(word_list) > 0:
                        for word_info in word_list:
                            if isinstance(word_info, dict):
                                # Handle different word dictionary structures
                                if 'text' in word_info and 'bbox' in word_info:
                                    word_text = word_info['text']
                                    word_bbox = word_info['bbox']
                                elif 'word' in word_info and 'bbox' in word_info:
                                    word_text = word_info['word']
                                    word_bbox = word_info['bbox']
                                else:
                                    # Skip invalid word entries
                                    continue

                                try:
                                    if isinstance(word_bbox, str):
                                        word_bbox = eval(word_bbox)
                                except:
                                    print(f"*** WARNING: Could not parse word bbox: {word_bbox}")
                                    continue

                                all_words.append({
                                    'text': word_text,
                                    'bbox': word_bbox,
                                    'color': row.get('color', None),
                                    'type': row.get('type', ''),
                                    'font': row_font,
                                    'font_size': row_font_size,
                                    'font_style': row_font_style
                                })
                    else:
                        # If no detailed word list, handle as a block
                        if isinstance(text, str) and ' ' in text:
                            split_words = text.split()
                            total_length = len(text)

                            if total_length == 0:
                                continue

                            start_pos = 0
                            for word in split_words:
                                word_len = len(word)
                                proportion = word_len / total_length

                                word_x0 = coords[0] + (coords[2] - coords[0]) * (start_pos / total_length)
                                word_x1 = word_x0 + (coords[2] - coords[0]) * proportion

                                all_words.append({
                                    'text': word,
                                    'bbox': (word_x0, coords[1], word_x1, coords[3]),
                                    'color': row.get('color', None),
                                    'type': row.get('type', ''),
                                    'font': row_font,
                                    'font_size': row_font_size,
                                    'font_style': row_font_style
                                })

                                start_pos += word_len + 1
                        else:
                            if text and isinstance(text, str):
                                all_words.append({
                                    'text': text,
                                    'bbox': coords,
                                    'color': row.get('color', None),
                                    'type': row.get('type', ''),
                                    'font': row_font,
                                    'font_size': row_font_size,
                                    'font_style': row_font_style
                                })

                # Sort words by x-coordinate
                all_words.sort(key=lambda w: w['bbox'][0])

                # Assign words to columns
                column_words = {col: [] for col in column_names}

                for word in all_words:
                    word_center_x = (word['bbox'][0] + word['bbox'][2]) / 2

                    assigned_column = None
                    for col_name, col_info in zip(column_names, column_coords):
                        if col_info['x0'] <= word_center_x <= col_info['x1']:
                            assigned_column = col_name
                            break

                    if not assigned_column:
                        distances = []
                        for col_name, col_info in zip(column_names, column_coords):
                            col_center = (col_info['x0'] + col_info['x1']) / 2
                            distance = abs(word_center_x - col_center)
                            distances.append((col_name, distance))

                        assigned_column = min(distances, key=lambda x: x[1])[0]

                    column_words[assigned_column].append(word)

                # Process the columns and detect outliers
                row_data = {}
                row_outliers = {}

                print("\n\n==== PROCESSING ROW WITH WORDS ====")
                for col_name in column_names:
                    words_in_col = column_words[col_name]
                    if not words_in_col:
                        row_data[col_name] = ''
                        continue

                    # Print column info
                    print(f"\n*** COLUMN: {col_name} has {len(words_in_col)} words ***")

                    words_in_col.sort(key=lambda w: w['bbox'][0])

                    if len(words_in_col) > 1:
                        # Group by formatting
                        format_groups = {}

                        for word in words_in_col:
                            # Get font info with fallbacks
                            word_font = str(word.get('font', 'unknown'))

                            # Try to get font size as numeric value
                            try:
                                if word.get('font_size') is not None and not pd.isna(word.get('font_size')):
                                    font_size_val = float(word.get('font_size', 0))
                                else:
                                    font_size_val = 0
                            except (ValueError, TypeError):
                                font_size_val = 0

                            # Print each word's font info
                            print(f"  Word: '{word['text']}' - Font: {word_font}, Size: {font_size_val}, Style: {word.get('font_style', 'unknown')}")

                            format_key = (
                                str(word.get('color', '')),
                                word_font,
                                # Round font size to nearest 0.5 to allow for minor variations
                                str(round(font_size_val * 2) / 2),
                                str(word.get('font_style', ''))
                            )

                            print(f"    Format key: {format_key}")

                            if format_key not in format_groups:
                                format_groups[format_key] = []
                            format_groups[format_key].append(word)

                        # Find the dominant formatting
                        if format_groups:
                            print(f"  Format groups found: {len(format_groups)}")
                            for fmt, words in format_groups.items():
                                print(f"    Format {fmt}: {len(words)} words")

                            dominant_format = max(format_groups.items(), key=lambda x: len(x[1]))[0]
                            print(f"  DOMINANT FORMAT: {dominant_format}")

                            # Identify outliers
                            outliers = []
                            normal_words = []

                            for word in words_in_col:
                                try:
                                    if word.get('font_size') is not None and not pd.isna(word.get('font_size')):
                                        font_size_val = float(word.get('font_size', 0))
                                    else:
                                        font_size_val = 0
                                except (ValueError, TypeError):
                                    font_size_val = 0

                                format_key = (
                                    str(word.get('color', '')),
                                    str(word.get('font', 'unknown')),
                                    str(round(font_size_val * 2) / 2),
                                    str(word.get('font_style', ''))
                                )

                                if format_key != dominant_format:
                                    outliers.append(word)
                                    print(f"  !!! OUTLIER DETECTED: '{word['text']}' with format {format_key}")
                                else:
                                    normal_words.append(word)

                            # Store outliers and create text from normal words
                            if outliers:
                                row_outliers[col_name] = [w['text'] for w in outliers]
                                print(f"  >>> Outliers in {col_name}: {row_outliers[col_name]}")

                            col_text = ' '.join([w['text'] for w in normal_words]) if normal_words else ''
                        else:
                            # Fallback if format_groups is empty
                            col_text = ' '.join([w['text'] for w in words_in_col])
                    else:
                        # Only one word, no need for outlier detection
                        col_text = words_in_col[0]['text']
                        print(f"  Single word: '{col_text}' - no outlier detection needed")

                    row_data[col_name] = col_text

                # Add outlier information to row data
                if row_outliers:
                    row_data['_outliers'] = row_outliers
                    print(f"\n=== FOUND OUTLIERS IN ROW: {row_outliers} ===\n")

                # Add completed row to structured data
                structured_data.append(row_data)

        structured_df = pd.DataFrame(structured_data)

    else:
        structured_df = pd.DataFrame()
        logger.warning("Structured DF is empty - Text")

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))

    # print(f"\n\nDEBUG: Structured DF Rows Before Return: {len(structured_df)}")
    if debug_row_content:
        print(f"\n\nLENTH OF STRUCTURE DF!: \n{len(structured_df)}\n\n")

    return structured_df

def detect_bom_rows(page_num, df, leftmost_column, skip_words = ["PIPE SPOOL", "PIPE SPOOLS"],
                    bottom_limit_flags = [["PIECE MARKS", "PIECE MARK NO", "PIECE MARK", "CUT PIECE LIST", "TOTAL FABRICATION WEIGHT"]], y0_deviation=1.5, skip_number_check=False):

    if debug_row_content:
        # Export the dataframe for analysis
        os.makedirs("debug", exist_ok=True)
        df.to_excel(f"debug/detect_bom_row PG {page_num + 1}.xlsx")
        print(f"\n\nBOM_ROWS: {len(df)}")

    # Debug the leftmost column definition
    print(f"\n=== DEBUG: LEFTMOST COLUMN DEFINITION ===")
    print(f"Leftmost column x0: {leftmost_column['x0']}, x1: {leftmost_column['x1']}")
    print(f"Leftmost column y0: {leftmost_column['y0']}, y1: {leftmost_column['y1']}")
    print(f"Y0 deviation parameter: {y0_deviation}")

    # Add bottom limit check here, right after the debug output but before any processing
    bottom_limit_y0 = None
    for flag_list in bottom_limit_flags:
        for flag in flag_list:
            # Convert values to string to handle any non-string types
            mask = df['value'].astype(str).str.contains(flag, case=False, na=False)
            matching_rows = df[mask]
            if not matching_rows.empty:
                bottom_limit_y0 = matching_rows.iloc[0]['coordinates2'][1]
                if debug_mode:
                    print(f"Found bottom limit flag '{flag}' at y0: {bottom_limit_y0}")
                break
        if bottom_limit_y0 is not None:
            break

    # Filter out rows below the bottom limit
    if bottom_limit_y0 is not None:
        df = df[df['coordinates2'].apply(lambda coord: coord[1] < bottom_limit_y0)]
        if debug_mode:
            print(f"Filtered out rows below y0: {bottom_limit_y0}")

    def is_valid_bom_item(row):
        text = row.value
        item_type = row.type

        # If skip_number_check is True, accept any non-empty text in the leftmost column
        if skip_number_check:
            try:
                result = bool(text and str(text).strip())
                if debug_row_content:
                    print(f"SKIP_NUMBER_CHECK: '{text}' -> {result}")
                return result
            except (AttributeError, TypeError):
                return False

        # Original number validation logic
        try:
            first_part = text.split()[0]
            pattern = r'^\d{1,3}[a-zA-Z]?$'

            if item_type == 'Text':
                return bool(re.match(pattern, first_part))

            else:
                # Handle non-text annotations here
                # You might want to add specific handling for different annotation types
                return bool(re.match(pattern, first_part))  # Or use different validation logic

        except (AttributeError, IndexError):
            return False

        # return bool(re.match(pattern, first_part))

    def is_in_leftmost_column(x, y, leftmost_column):
        return (leftmost_column['x0'] <= x <= leftmost_column['x1'] and
                leftmost_column['y0'] <= y <= leftmost_column['y1'])

    def is_red_text(color): # For Revisions
        return color == (255, 0, 0)

    # Filter items in the leftmost column
    left_column_df = df[df["x0"].between(leftmost_column["x0"], leftmost_column["x1"]) & df["y0"].between(leftmost_column["y0"], leftmost_column["y1"])]

    if debug_row_content:
        print(f"\n=== DEBUG: LEFTMOST COLUMN FILTERING ===")
        print(f"Found {len(left_column_df)} items in the leftmost column")
        if len(left_column_df) > 0:
            print("Sample items in leftmost column:")
            for i, (idx, row) in enumerate(left_column_df.head(5).iterrows()):
                print(f"  Item {i+1}: '{row['value']}' at y0={row['y0']:.2f}, y1={row['coordinates2'][3]:.2f}")

    # Filter for valid BOM items (usually position numbers)
    bom_items = left_column_df[left_column_df.apply(is_valid_bom_item, axis=1)]

    if debug_row_content:
        print(f"\n=== DEBUG: VALID BOM ITEMS ===")
        print(f"Found {len(bom_items)} valid BOM items (position numbers)")
        if len(bom_items) > 0:
            print("Valid BOM items:")
            for i, (idx, row) in enumerate(bom_items.iterrows()):
                print(f"  Item {i+1}: '{row['value']}' at y0={row['y0']:.2f}, y1={row['coordinates2'][3]:.2f}")

    # Sort by y0 coordinate
    bom_items_sorted = bom_items.sort_values('y0')

    # Calculate row ranges and store corresponding text
    row_ranges = []
    row_texts = []
    row_heights = []

    if debug_row_content:
        print(f"\n=== DEBUG: CALCULATING ROW RANGES ===")

    for i in range(len(bom_items_sorted)):
        start = bom_items_sorted.iloc[i]['y0']
        text = bom_items_sorted.iloc[i]['value']

        # Collect vertical (y1 - y0) heights of BOM rows
        if i < len(bom_items_sorted) - 1:
            end = bom_items_sorted.iloc[i+1]['y0']
        else:
            # For the last row, use a more reasonable height instead of extending to the bottom
            # Calculate based on the average height of previous rows, or use a fixed multiplier
            if len(row_heights) > 0:
                # Use the average of previous row heights
                avg_height = sum(row_heights) / len(row_heights)
                # Add a small buffer (1.5x the average height)
                end = start + (avg_height * 1.5)
            else:
                # Fallback if no previous rows
                # Use the height of the text itself plus a margin
                item_height = bom_items_sorted.iloc[i]['coordinates2'][3] - start
                end = start + (item_height * 3)  # 3x the height of the text

            # Make sure we don't go beyond the bottom of the leftmost column
            end = min(end, leftmost_column['y1'])

        row_ranges.append((start, end))
        row_texts.append(text)
        row_heights.append(end - start)

        if debug_row_content:
            print(f"Row {i} (Position {text}): Range={start:.2f}-{end:.2f}, Height={end-start:.2f}")

            # Find all items that would fall within this row range
            row_items = df[(df['y0'] >= start) & (df['y0'] < end)]
            print(f"  Items in this row range: {len(row_items)}")
            if len(row_items) > 0:
                print("  Sample items:")
                for j, (idx, item) in enumerate(row_items.head(3).iterrows()):
                    print(f"    Item {j+1}: '{item['value']}' at y0={item['y0']:.2f}, y1={item['coordinates2'][3]:.2f}")
                if len(row_items) > 3:
                    print(f"    ... and {len(row_items)-3} more items")


    # Calculate average row height and standard deviation
    if len(row_heights) > 2:
        # If we have more than 2 rows, we can use statistics on all but the last row
        row_heights_for_calc = row_heights[:-1]
        avg_row_height = statistics.mean(row_heights_for_calc)
        std_row_height = statistics.stdev(row_heights_for_calc)
        outlier_threshold = avg_row_height + 2 * std_row_height
    elif len(row_heights) == 2:
        # If we have exactly 2 rows, use the first row's height
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = avg_row_height * 2  # Set a reasonable threshold
    elif len(row_heights) == 1:
        # If we have only 1 row, use its height directly
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = float('inf')  # No outliers possible with one row
    else:
        # Handle the case where there are no rows
        logger.warning(f"Table has no rows on page {page_num + 1}!")
        avg_row_height = 0
        std_row_height = 0
        outlier_threshold = float('inf')

    # Function to assign row based on coordinates
    def assign_row(y0, y1, color, text):
        # Perform checks for: Both y0, y1 in row, y1
        if debug_row_assignment:
            print(f"\n--- DEBUG ASSIGN ROW ---")
            print(f"Text: '{text}', Y0: {y0}, Y1: {y1}, Color: {color}")

        # CRITICAL FIX: For text in the leftmost column (position numbers),
        # we need to be extremely strict about row assignment
        # This is the key to fixing the row wrapping issue
        is_position_number = False
        try:
            # Check if this is a position number (1-999)
            if re.match(r'^\d{1,3}$', text.strip()):
                is_position_number = True
        except:
            pass

        # For position numbers, use exact matching only
        if is_position_number:
            for i, (start, end) in enumerate(row_ranges):
                # If this is a position number, it should match exactly with the row range start
                if abs(y0 - start) < 0.5:  # Very small tolerance
                    if debug_row_assignment:
                        print(f"  ASSIGNED TO ROW {i} as position number")
                    return i

        # For all other text, use a more strict approach based on row boundaries
        # First, check if this text is at the start of a row (within 5% of the row start)
        for i, (start, end) in enumerate(row_ranges):
            row_height = end - start
            # If text starts very close to the start of a row, it likely belongs to that row
            if abs(y0 - start) < (row_height * 0.05):
                if debug_row_assignment:
                    print(f"  ASSIGNED TO ROW {i} by row start proximity")
                return i

        # Check vertical center line to assign row - but be more strict
        y_center = y0 + ((y1 - y0) / 2)

        # Calculate the percentage of overlap with each row
        row_overlaps = []
        for i, (start, end) in enumerate(row_ranges):
            # Calculate overlap
            overlap_start = max(y0, start)
            overlap_end = min(y1, end)

            if overlap_end > overlap_start:  # There is overlap
                text_height = y1 - y0
                overlap_height = overlap_end - overlap_start
                overlap_percentage = overlap_height / text_height

                # Calculate how far the text is from the start of the row
                distance_from_start = y0 - start

                # Prefer rows where the text starts near the beginning
                row_overlaps.append((i, overlap_percentage, distance_from_start))

        # Sort by overlap percentage (highest first)
        row_overlaps.sort(key=lambda x: (x[1], -x[2]), reverse=True)

        # If we have a significant overlap (>80%), use that row - increased threshold
        if row_overlaps and row_overlaps[0][1] >= 0.8:
            best_row = row_overlaps[0][0]
            if debug_row_assignment:
                print(f"  ASSIGNED TO ROW {best_row} by overlap percentage ({row_overlaps[0][1]:.2f})")
            return best_row

        # If no significant overlap, use center line with stricter bounds
        for i, (start, end) in enumerate(row_ranges):
            # Use the first 75% of the row height for center line check
            adjusted_end = start + ((end - start) * 0.75)
            if start <= y_center <= adjusted_end:
                if debug_row_assignment:
                    print(f"  ASSIGNED TO ROW {i} by strict center line check")
                return i

        # Alternative - Assign if both y0 and y1 are within row height range
        for i, (start, end) in enumerate(row_ranges):
            if start <= y0 <= end and start <= y1 <= end:
                if debug_row_assignment:
                    print(f"  ASSIGNED TO ROW {i} by full containment check")
                return i

        # If all other checks fail, check for red text (Could be Revisions, which can be off center)
        if is_red_text(color):
            distances = []
            for i, (start, end) in enumerate(row_ranges):
                mid = (start + end) / 2
                distance_y0 = abs(y0 - mid)
                distance_y1 = abs(y1 - mid)
                distances.append(min(distance_y0, distance_y1))
            best_row = np.argmin(distances)
            if debug_row_assignment:
                print(f"  ASSIGNED TO ROW {best_row} by red text check")
            return best_row

        # If nothing worked, return None
        if debug_row_assignment:
            print(f"  COULD NOT ASSIGN TO ANY ROW")
        return None

    # Assign rows to all items
    row_assignments = [-1] * len(df)  # Initialize all assignments to -1
    unassigned_items = [] # Items that could not be assigned but likely should have been
    skipped_items = [] # Intentionally skipped
    skip_processing = False
    last_valid_row = -1
    last_row_items = []
    buffer_items = [] # Holds items for use in loop. Accounts for minor deviations in y0 to ensure we do not permanently discard valid items in 'skip_processing'

    for i, (index, row) in enumerate(df.iterrows()):

        y0, y1 = row['coordinates2'][1], row['coordinates2'][3]
        color = row['color']
        # text = row['Text']
        text = str(row['value'])
        text_type = str(row['type'])

        # Check if we need to start skipping
        if any(skip_word in text for skip_word in skip_words):
            skip_processing = True
            skipped_items.append((i, text, row['coordinates2'], "Contains skip word"))
            continue

        # Check if we need to stop skipping
        if skip_processing and is_valid_bom_item(text, text_type) and is_in_leftmost_column(row['x0'], row['y0'], leftmost_column):
            skip_processing = False

            # Process buffered items
            for buffered_item in buffer_items:
                if abs(buffered_item[1] - y0) <= y0_deviation:
                    assigned_row = assign_row(buffered_item[1], buffered_item[2], buffered_item[3], buffered_item[4])
                    if assigned_row is not None:
                        row_assignments[buffered_item[0]] = assigned_row
                    else:
                        unassigned_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Could not be assigned to a row after buffer processing"))
            buffer_items.clear()

        if skip_processing:
            buffer_items.append((i, y0, y1, color, text))
            # skipped_items.append((i, text, df.iloc[i]['Coordinates'], "In skip processing mode"))
            continue

        assigned_row = assign_row(y0, y1, color, text)

        if assigned_row is not None:
            # row_assignments.append(assigned_row)
            row_assignments[i] = assigned_row
            last_valid_row = assigned_row

            # Store items for the last row
            if assigned_row == len(row_ranges) - 1:
                last_row_items.append((i, y0, y1, text))

        else:
            unassigned_items.append((index,text, row['coordinates2'], "Could not be assigned to a row"))
            row_assignments[i] = last_valid_row  # Assign to the last valid row instead of -1

    # Process any remaining buffered items
    for buffered_item in buffer_items:
        skipped_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Remained in buffer after processing"))

    # Process the last row items for outliers
    if last_row_items:
        last_row_start, last_row_end = row_ranges[-1]
        last_row_height = last_row_end - last_row_start

        if last_row_height > outlier_threshold:
            #print(f"\nLast row is an outlier. Removing trash data:")
            valid_last_row_items = []
            for i, y0, y1, text in last_row_items:
                if y0 - last_row_start > outlier_threshold:
                    #print(f"Removing: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")
                    #unassigned_items.append((i, text, df.iloc[i]['Coordinates']))
                    skipped_items.append((i, text, df.iloc[i]['coordinates2'], "Greater than outlier threshold"))
                    row_assignments[i] = -1
                else:
                    valid_last_row_items.append((i, y0, y1, text))
                    #print(f"Keeping: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")

            # Update the last row range with the valid items
            if valid_last_row_items:
                new_last_row_end = max(item[2] for item in valid_last_row_items)
                row_ranges[-1] = (last_row_start, new_last_row_end)
                #print(f"\nUpdated last row range: {row_ranges[-1]}")
            else:
                print("\n--> Warning: All items in the last row were removed as trash.")
        # else:
        #     print("\nLast row is not an outlier. Keeping all items.")


    # df['assigned_row'] = row_assignments

    df = df.copy()
    df.loc[:, 'assigned_row'] = row_assignments

    # Create filtered contents based on row assignments
    filtered_contents = [df[df['assigned_row'] == i] for i in range(len(row_ranges))]

    # Debug information
    if debug_row_content:
        print("\nFinal Row Ranges: - Line 1593")
        for i, (start, end) in enumerate(row_ranges):
            print(f"Row {i}: {start} - {end} (height: {end - start})")

        print(f"Average row height: {avg_row_height}")
        print(f"Row height standard deviation: {std_row_height}")
        print(f"Outlier threshold: {outlier_threshold}")

    if debug_discarded:
        print("\nIntentionally Skipped Items:")
        if skipped_items:  # Only print if there are skipped items
            for item in skipped_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

        if unassigned_items:  # Only print if there are unassigned items
            print("\nUnassigned Items:")
            for item in unassigned_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")
    if debug_mode:
        try:
            print(f"\n\n-------\nReturning {i} Rows for page {page_num}\n")
        except Exception as e:
            logger.info(f"Returning {i} Rows for page ERROR GETTING PAGE NUM: {e}")
    return row_ranges, row_texts, filtered_contents, unassigned_items, skipped_items



def assign_texts_to_columns(row_texts: list[dict], column_coords: list[dict], column_names: list[str], table_type: str, previous_row=None):
    '''
    Handle text and annotation data separately then join after.
    Need to identify row types (Text, Annot, Text w/Rev Markup)
    '''

    if debug_row_content:
        print(f"\n=== Starting new row assignment in assign_texts_to_columns===")
        print(f"Number of texts to process: {len(row_texts)}")
        print(f"Initial row_texts: {row_texts}")

    row_structure = {col: [] for col in column_names}  # Use lists instead of strings
    has_revision = None
    row_colors = {col: [] for col in column_names}  # To keep track of font colors
    row_material_scope = None
    row_component_category = None
    non_text_items = [] # New list to store non-text items (Annots)
    leftmost_column = column_names[0]  # Assuming the first column is the leftmost

    replace_mat_desc = False # Will replace values in material_description if 'True'

    # Debug column info
    print("\n=== DEBUG: Column Coordinates ===")
    for i, (col_name, col_info) in enumerate(zip(column_names, column_coords)):
        print(f"Column {i}: {col_name} - x0: {col_info['x0']}, x1: {col_info['x1']}")

    # Sort text items by y-coordinate (top to bottom) and then x-coordinate (left to right)
    sorted_texts = sorted(row_texts, key=lambda item: (item['bbox'][1], item['bbox'][0]))


    # STEP 1: Extract and validate all words from all text items
    all_words = []
    text_already_processed = set()  # Track which text has been processed

    print("\n=== STEP 1: Extract Words ===")

    for text_item in sorted_texts:
        # Get the text item's center coordinates
        text_center_x = (text_item['bbox'][0] + text_item['bbox'][2]) / 2
        text_center_y = (text_item['bbox'][1] + text_item['bbox'][3]) / 2

        # Create unique ID to avoid duplicates
        text_id = f"{text_center_x:.1f}_{text_center_y:.1f}"

        if text_id in text_already_processed:
            print(f"Skipping duplicate text at position {text_id}")
            continue

        text_already_processed.add(text_id)

        # Check the text type
        val_type = text_item.get('type', '')
        if not (pd.isna(val_type) or val_type == '' or val_type in ['Text', 'OCR']):
            non_text_items.append(text_item)
            continue

        text = str(text_item['text']).strip()
        color = text_item['color']

        region = [
            text_item['bbox'][0],
            text_item['bbox'][1],
            text_item['bbox'][2]-text_item['bbox'][0],
            text_item['bbox'][3]-text_item['bbox'][1]
        ]
        print(text, region)
        print(region)
        print(text_center_x, text_center_y)

        # Get material scope and category
        if table_type == 'bom':
            material_scope = text_item.get('material_scope', '')
            if material_scope and not row_material_scope:
                row_material_scope = material_scope

            component_category = text_item.get('componentCategory', '')
            if component_category and not row_component_category:
                row_component_category = component_category

        # Get and parse 'words' from the text item
        try:
            words_raw = text_item.get('words', [])
            parsed_words = []

            # Parse string representation if needed
            if isinstance(words_raw, str) and words_raw.startswith('[') and words_raw.endswith(']'):
                import ast
                try:
                    parsed_words = ast.literal_eval(words_raw)
                except Exception as e:
                    print(f"Error parsing words string: {e}")
                    parsed_words = []
            # Handle nested string representation
            elif isinstance(words_raw, list) and len(words_raw) == 1 and isinstance(words_raw[0], dict) and 'text' in words_raw[0]:
                text_content = words_raw[0]['text']
                if isinstance(text_content, str) and text_content.startswith('[') and text_content.endswith(']'):
                    import ast
                    try:
                        nested_words = ast.literal_eval(text_content)
                        if isinstance(nested_words, list):
                            parsed_words = nested_words
                    except Exception as e:
                        print(f"Error parsing nested words: {e}")
                        parsed_words = words_raw
                else:
                    parsed_words = words_raw
            else:
                parsed_words = words_raw


            # For text entries with no words, create a single word entry
            if not parsed_words:
                word_entry = {
                    'text': text,
                    'bbox': text_item['bbox'],
                    'x_center': text_center_x,
                    'y_center': text_center_y,
                    'color': color,
                    'original_text': text  # Keep track of the original text this came from
                }
                all_words.append(word_entry)
            else:
                # Process each word entry
                for word_info in parsed_words:
                    if not isinstance(word_info, dict):
                        continue

                    # Extract word text and bbox
                    word_text = word_info.get('word', word_info.get('text', ''))
                    word_bbox = word_info.get('bbox', text_item['bbox'])

                    if not word_text or not isinstance(word_bbox, (list, tuple)) or len(word_bbox) != 4:
                        continue

                    # Add word to our collection
                    word_entry = {
                        'text': word_text,
                        'bbox': word_bbox,
                        'x_center': (word_bbox[0] + word_bbox[2]) / 2,
                        'y_center': (word_bbox[1] + word_bbox[3]) / 2,
                        'color': color,
                        'original_text': text  # Keep track of the original text this came from
                    }
                    all_words.append(word_entry)
        except Exception as e:
            print(f"Error processing words: {e}")
            # Fallback to using the whole text
            word_entry = {
                'text': text,
                'bbox': text_item['bbox'],
                'x_center': text_center_x,
                'y_center': text_center_y,
                'color': color,
                'original_text': text
            }
            all_words.append(word_entry)

    # STEP 2: Sort all words by Y position (rows) and then X position (reading order)
    # First, group words with similar y_center values
    y_tolerance = 5.0  # Adjust this value based on your document characteristics

    # Group words by similar y_center values
    y_groups = {}
    for word in all_words:
        y_center = word['y_center']
        assigned_to_group = False

        # Try to find an existing group this word belongs to
        for group_y, group_words in y_groups.items():
            if abs(y_center - group_y) <= y_tolerance:
                group_words.append(word)
                assigned_to_group = True
                break

        # If not assigned to any existing group, create a new group
        if not assigned_to_group:
            y_groups[y_center] = [word]

    # Update y_center values within each group to be the same
    # Use the maximum y0 (top) value for better alignment with actual text
    aligned_words = []
    for group_y, group_words in y_groups.items():
        # Calculate the average y-center for the group
        avg_y = sum(word['y_center'] for word in group_words) / len(group_words)

        # Find any red text in the group (if it exists)
        red_words = [w for w in group_words if w.get('color') == (255, 0, 0) or w.get('color') == [255, 0, 0]]

        # If red text exists, prioritize its y_center
        if red_words:
            red_avg_y = sum(word['y_center'] for word in red_words) / len(red_words)
            # Use red text y_center if it's significantly different
            if abs(red_avg_y - avg_y) > 2.0:
                avg_y = red_avg_y

        # Update all words in the group with the same y_center
        for word in group_words:
            word_copy = word.copy()
            word_copy['original_y_center'] = word['y_center']  # Keep original for reference
            word_copy['y_center'] = avg_y
            aligned_words.append(word_copy)

    # Sort the aligned words by y_center and then x_center
    aligned_words.sort(key=lambda w: (w['y_center'], w['x_center']))

    # Replace the original all_words with the aligned version
    all_words = aligned_words

    print(f"\n=== STEP 2: Sorted {len(all_words)} words by position after y-alignment ===")

    # STEP 3: Assign each word to a column based on its center X position
    print("\n=== STEP 3: Assign words to columns ===")

    # First, identify if there are already valid entries in any column
    # to avoid replacing them with split-up text
    existing_entries = {col: len(row_structure[col]) > 0 for col in column_names}

    for word in all_words:
        x_center = word['x_center']
        word_text = word['text']
        color = word['color']

        # Find which column this word belongs to
        assigned_col = None
        for col_name, col_info in zip(column_names, column_coords):
            if col_info['x0'] <= x_center <= col_info['x1']:
                assigned_col = col_name
                break

        # If not found, find the closest column
        if not assigned_col:
            closest_col = min(column_coords, key=lambda c: min(abs(x_center - c['x0']), abs(x_center - c['x1'])))
            assigned_col = closest_col.get('columnName', column_names[0])  # Fallback to first column if columnName is missing

        # Check if this word's original text is already in this column
        # This helps prevent duplicate position numbers, etc.
        original_text = word['original_text']
        is_duplicate = False

        for existing_text in row_structure[assigned_col]:
            if existing_text == word_text or existing_text == original_text:
                # is_duplicate = True
                break

        if not is_duplicate:
            # Special handling for "pos" column to prevent duplicates like "6 6"
            if assigned_col == 'pos' and row_structure[assigned_col] and word_text in row_structure[assigned_col][0]:
                continue

            # Special handling for red text
            if color == (255, 0, 0):# and word_text not in ["1", "2", "3"]:
                has_revision = True
                if assigned_col == "material_description" or row_structure[assigned_col]:
                    # For material_description or if the column already has content
                    if row_structure[assigned_col] and row_colors[assigned_col] and row_colors[assigned_col][-1] == (255, 0, 0):
                        # If last text was red, append
                        row_structure[assigned_col][-1] += f" {word_text}"
                    else:
                        # If last text wasn't red or column is empty, replace
                        row_structure[assigned_col] = [word_text]
                        row_colors[assigned_col] = [color]
                else:
                    # For other columns or if it's the first entry, replace
                    row_structure[assigned_col] = [word_text]
                    row_colors[assigned_col] = [color]
            else:
                # For black text, append only if the last entry wasn't red
                if not row_structure[assigned_col] or row_colors[assigned_col][-1] != (255, 0, 0):
                    row_structure[assigned_col].append(word_text)
                    row_colors[assigned_col].append(color)

            print(f"Assigned '{word_text}' to column '{assigned_col}'")

        else:
            pass

    # STEP 4: Join text in each column
    print("\n=== STEP 4: Join text in each column ===")

    for col in row_structure:
        row_structure[col] = ' '.join(row_structure[col])
        print(f"Final column {col}: {row_structure[col][:50]}{'...' if len(row_structure[col]) > 50 else ''}")

    # Add material scope and component category
    if table_type == 'bom':
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''
        row_structure['componentCategory'] = row_component_category if row_component_category else ''

    # Check for duplicates with previous row
    if previous_row is not None:
        if all(row_structure[col].strip() == previous_row[col].strip() for col in row_structure if col in previous_row):
            print(f"Duplicate row detected: {row_structure}")
            return None  # Return None for duplicate rows

    # Final output for debugging
    print("\n=== Final row structure ===")
    for col, value in row_structure.items():
        try:
            print(f"{col}: {value[:50]}{'...' if len(value) > 50 else ''}")
        except Exception as e:
            print(f"Error printing column {col}, value: {value}, error: {e}")
    print()

    row_structure["has_revision"] = has_revision
    return row_structure

def extract_annotations(pdf_path, page, page_num, rect, table_type, raw_df, annotations_data=None, multi_roi=False):
    # Now, process annotations
    if annotations_data is None:
        annotations_data = []

    annotations = page.annots()
    if annotations:
        for annot in annotations:

            annot_rect = annot.rect

            if not multi_roi: # Use the annotation directly since the transformation matrix should already be applied
                # Adjust rect for rotation matrix if needed
                annot_rect = adjust_bbox(annot_rect, page.rotation_matrix)

            #adjust_bbox(annot_rect, page.rotation_matrix)

            if rect.intersects(annot_rect):  # Check if the annotation intersects with the table
                annot_info = annot.info
                annotations_data.append({
                    "PdfPath": pdf_path,
                    "PdfPage": page_num + 1,
                    "outlierScope": table_type,
                    "Type": annot_info.get("type", ""),
                    "Text": '',
                    "Subject": annot_info.get("subject", ""),  # Subject might be a better descriptor
                    "Contents": annot_info.get("content", "").strip(),
                    "coordinates": annot_rect, #(annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                    "coordinates2": annot_rect, #(annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                    "font": '',
                    "font_size": '',
                    "color":'',
                    "Flags": '',
                    "CreationDate": annot_info.get("creationDate", ""),
                    "ModDate": annot_info.get("mod_date", "")
                })
    if debug_mode:
        print(f"Page Rotation: {page.rotation}")
        print("\n\nAnnotations Data Rect:\n", annot_rect)
        print("\n\nAnnotations Data:\n", annotations_data)
    return annotations_data

# Temporarily used for Annotation type tables until a more robust solution is found
def create_logical_structure_annot(raw_table_df, column_coords, overlap_threshold=0, numbered_rows=True, annot_table=True):

    # print(f"ANNOT DF LENGTH: {len(raw_table_df)}\n Columns{raw_table_df.columns}")


    if debug_mode:
        print("\n\n--> ANNOT CREATE LOGICAL STRUCTURE ACCESSED...")
    #logger.debug("Starting create_logical_structure_2")
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    # Data structure to hold the text for each column
    structured_data = {col: [] for col in column_names}
    unassigned_values = []  # New list to store unassigned values

    # Track the last processed y-coordinate for the first column
    last_y = None

    # print("Table Colmns: ", raw_table_df.columns)

    # Process each text item in raw_table_df
    for _, row in raw_table_df.iterrows():
        if annot_table:
            # text = row['Contents'] # CHANGED
            # text = row['Text']
            text = row['Contents'] if 'Contents' in row else row.get('value', row.get('Text', ''))
        else:
            # text = row['Text']
            text = row.get('value', row.get('Text', ''))
        #
        #print("\n\nTEXT: ", text)
        coords = row['coordinates2']

        # print(f"\nCoords type: {type(coords)}, Value: {coords}")


        try:
            if isinstance(coords, str):
                # If coords is a string, try to evaluate it as a tuple
                coords = eval(coords)

            if isinstance(coords, (list, tuple)) and len(coords) == 4:
                bbox = fitz.Rect(coords[0], coords[1], coords[2], coords[3])
            elif isinstance(coords, (list, tuple)) and len(coords) == 2:
                # Assuming coords are (x, y) of top-left corner, create a small 1x1 rectangle
                bbox = fitz.Rect(coords[0], coords[1], coords[0] + 1, coords[1] + 1)
            else:
                raise ValueError(f"Unexpected coords format: {coords}")

        except Exception as e:
            print(f"\n\nError assigning bbox: Text: {text}, Coords: {coords} \n Error: {e}")
            # If all else fails, create a default rectangle at (0, 0)
            print(f"Warning: Using default bbox for text: {text}")
            bbox = fitz.Rect(0, 0, 1, 1)

        # Calculate the centerpoint of the text's x-coordinate
        text_center_x = (bbox.x0 + bbox.x1) / 2

        # # Determine which column the text belongs to
        # assigned_column = None
        # for i, col_info in enumerate(column_coords):
        #     col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])

        # Determine which column the text belongs to
        assigned_column = None
        for i, col_info in enumerate(column_coords):
            if col_info['x0'] <= text_center_x <= col_info['x1']:
                assigned_column = col_info['columnName']
                break

        if assigned_column is None:
            unassigned_values.append({'text': text, 'bbox': bbox})
            continue

    # if col_rect.intersects(bbox):
    #     column_name = col_info['columnName']

        # Check if we need to start a new row
        #print(f"\nChecking new row criteria for text: '{text}', Y: {coords[1]}, Last Y: {last_y}, Overlap Threshold: {overlap_threshold}")
        if last_y is None or coords[1] > last_y + overlap_threshold:
            # Append empty strings to columns that have no data yet
            for col_name in structured_data:
                structured_data[col_name].append('')
            last_y = coords[3]

        if i == 0 and numbered_rows:  # Leftmost column and numbered rows
            match = re.match(r'(\d+)(?:\.\s+)?(?:\s+(.*))?', text)
            if match:
                structured_data[assigned_column][-1] = match.group(1)
                if match.group(2):
                    next_column_name = column_names[i+1]
                    structured_data[next_column_name][-1] = match.group(2)
            else:
                structured_data[assigned_column][-1] = text
        else:
            structured_data[assigned_column][-1] = text
        #break

    structured_df_bf = pd.DataFrame(structured_data) # TYPO???

    # Call the merge_wrapped_rows function on the structured DataFrame
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    #print(f"\n\nANNOT CREATE LOGICAL END ROWS {len(structured_df)}")

    # Print unassigned values
    # if unassigned_values:
    if unassigned_values is not None and len(unassigned_values) > 0:
        print("\nUnassigned Values:")
        for item in unassigned_values:
            print(f"Text: {item['text']}, Bounding Box: {item['bbox']}")

    return structured_df

def get_group_for_page(page_number):
    if page_group_dict:
        return page_group_dict.get(page_number, None)  # Returns None if page_number is not found

def call_get_table_data(pdf_path, page_num, roi_json_path, raw_data_df, multi_roi=False, page_group_dict=None, x_offset=0, y_offset=0):
    # Load the PDF
    pdf_document = fitz.open(pdf_path)
    page = pdf_document[page_num]

    # Not used and replaced by raw_data_df (Function still requires argument as fallback method)
    page_text_blocks = []

    ref_rect = pdf_document.load_page(0).rect
    ref_width, ref_height = ref_rect.width, ref_rect.height

    # NEW SIMPLIFIED APPROACH: Always load from the single roi.json file
    # The new format embeds page group information within the roi.json

    # Load the ROI payload
    with open(roi_json_path, 'r') as f:
        roi_payload = json.load(f)

    # Convert Relative to Absolute Coordinates
    converted_roi_payload = convert_relative_coords_to_points(roi_payload, ref_width, ref_height, x_offset, y_offset)

    if multi_roi:
        print(f"Using new multi-ROI format for page {page_num + 1}")
        # TODO: Add page-specific ROI selection logic here if needed
        # For now, use the entire converted payload

    # # Call the get_table_data function



    structured_tables, annotations_tables, combined_outliers_df, annotations_df = get_table_data(
        pdf_path,
        page,
        page_num,
        converted_roi_payload,
        page_text_blocks,
        raw_data_df,
        missing_pos=False,
        remove_outliers=True
    )

    # get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data_df, missing_pos=False, remove_outliers=True)

    # Close the PDF
    pdf_document.close()

    return structured_tables, annotations_tables, combined_outliers_df, annotations_df

# Example usage
if __name__ == "__main__":
    # ===== ROW DETECTION FIX TEST HARNESS =====
    # Testing with new data sources for row detection fix

    # New data sources for testing the row detection fix
    raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 042 - InDemand Berryville\Data\data.feather"
    roi_json_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 042 - InDemand Berryville\roi.json"

    # Try to find the corresponding PDF file for the Axis project
    pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 042 - InDemand Berryville\Received\InDemand Berryville Piping Isos.pdf"



    start_page = 0
    end_page = 1  # Test with just one page initially

    print("="*60)
    print("ROW DETECTION FIX TEST HARNESS")
    print("="*60)
    print(f"Raw data source: {raw_data_path}")
    print(f"ROI source: {roi_json_path}")
    print(f"Testing pages: {start_page} to {end_page-1}")
    print("="*60)

    # Load raw_dataframe from feather file
    try:
        raw_data_df = pd.read_feather(raw_data_path)
        print(f"✓ Loaded raw data: {len(raw_data_df)} rows")
    except Exception as e:
        print(f"✗ Error loading feather file: {e}")
        # Fallback to old data for testing
        print("Falling back to old test data...")
        pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\Excel 012\Modified\BUNGE Combined.pdf"
        roi_json_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\temp\oci_coordinates.json"
        raw_data_path = r"C:\Users\<USER>\source\repos\Architekt ATOM\_debug_raw_data_full.xlsx"
        raw_data_df = pd.read_excel(raw_data_path)
        end_page = 5

    print(f"Total raw data rows: {len(raw_data_df)}")

    # Load ROI data
    try:
        with open(roi_json_path, 'r') as f:
            roi_data = json.load(f)
        print(f"✓ Loaded ROI data from: {roi_json_path}")

        # Debug: Show ROI structure to understand the format
        if debug_centered_fix:
            print(f"ROI data type: {type(roi_data)}")
            if isinstance(roi_data, dict):
                print(f"ROI keys: {list(roi_data.keys())}")
                # Show first few characters of each key's value
                for key, value in list(roi_data.items())[:5]:
                    print(f"  {key}: {type(value)} - {str(value)[:100]}...")
            elif isinstance(roi_data, list):
                print(f"ROI list length: {len(roi_data)}")
                if len(roi_data) > 0:
                    print(f"First item type: {type(roi_data[0])}")
                    print(f"First item: {str(roi_data[0])[:100]}...")
    except Exception as e:
        print(f"✗ Error loading ROI file: {e}")
        exit(1)

    # For feather data, we don't need to open a PDF to get page count
    # We'll determine pages from the raw data
    if 'pdf_page' in raw_data_df.columns:
        available_pages = sorted(raw_data_df['pdf_page'].unique())
        print(f"Available pages in data: {available_pages}")
        # Adjust end_page based on available data
        if end_page > len(available_pages):
            end_page = min(len(available_pages), 3)  # Test with first 3 pages max
    else:
        print("Warning: No 'pdf_page' column found in data")
        available_pages = [1]  # Default to page 1

    all_structured_tables = {}
    all_annotations_tables = {}
    all_combined_outliers = []
    all_annotations = []

    print(f"\nProcessing pages {start_page} to {end_page-1}...")

    for page_num in range(start_page, end_page):
        actual_page = available_pages[page_num] if page_num < len(available_pages) else available_pages[0]
        print(f"\n{'='*40}")
        print(f"Processing page {page_num + 1} (actual page {actual_page})")
        print(f"{'='*40}")

        # Filter raw data for this page
        page_data = raw_data_df[raw_data_df['pdf_page'] == actual_page] if 'pdf_page' in raw_data_df.columns else raw_data_df
        print(f"Page data rows: {len(page_data)}")

        # For testing with feather data, we need to handle the case where PDF might not exist
        try:
            # Check if we have a real PDF file or if we're using dummy data
            if pdf_path == "dummy_path_for_testing.pdf":
                # Create a minimal mock PDF processing for feather data testing
                print(f"  Using feather data mode (no actual PDF processing)")
                # We'll need to call get_table_data directly with mock page data
                # For now, skip PDF-dependent processing
                print(f"  Skipping page {page_num + 1} - PDF processing not available for feather-only mode")
                continue
            else:
                # Normal PDF processing
                results = call_get_table_data(pdf_path, page_num, roi_json_path, raw_data_df, multi_roi, page_group_dict if multi_roi else None)
        except Exception as e:
            print(f"Error processing page {page_num + 1}: {e}")
            import traceback
            traceback.print_exc()
            continue

        structured_tables, annotations_tables, combined_outliers_df, annotations_df = results

        # Debug output for row detection fix
        print(f"\n--- PAGE {page_num + 1} RESULTS ---")
        for table_type, df in structured_tables.items():
            print(f"{table_type.upper()} table: {len(df)} rows")
            if len(df) > 0 and debug_centered_fix:
                print(f"Sample {table_type} data:")
                print(df.head(3).to_string())

        # Combine results for this page
        for table_type, df in structured_tables.items():
            if table_type not in all_structured_tables:
                all_structured_tables[table_type] = df
            else:
                all_structured_tables[table_type] = pd.concat([all_structured_tables[table_type], df], ignore_index=True)

        for table_type, df in annotations_tables.items():
            if table_type not in all_annotations_tables:
                all_annotations_tables[table_type] = []
            all_annotations_tables[table_type].append(df)

        all_combined_outliers.append(combined_outliers_df)
        all_annotations.append(annotations_df)


    # Combine results from all pages

    # No need to concatenate structured_tables again
    final_structured_tables = all_structured_tables

    #final_structured_tables = {table_type: pd.concat(dfs, ignore_index=True) for table_type, dfs in all_structured_tables.items()}
    final_annotations_tables = {table_type: pd.concat(dfs, ignore_index=True) for table_type, dfs in all_annotations_tables.items()}
    final_combined_outliers = pd.concat(all_combined_outliers, ignore_index=True)
    final_annotations = pd.concat(all_annotations, ignore_index=True)

    # print("\n\nFinal Results:")
    # for table_type, df in final_structured_tables.items():
    #     print(f"\nStructured table for {table_type}:")
    #     print(df)

    # print("\nCombined Outliers:")
    # print(final_combined_outliers)

    # print("\nAnnotations:")
    # print(final_annotations)

    # # If you want to save the combined dataframes to CSV files
    # for table_type, df in final_structured_tables.items():
    #     df.to_csv(f"{table_type}_structured_table.csv", index=False)
    # final_combined_outliers.to_csv("combined_outliers.csv", index=False)
    #final_annotations.to_excel("annotations.xlsx", index=False)

    # ===== FINAL RESULTS SUMMARY =====
    print("\n" + "="*60)
    print("FINAL RESULTS SUMMARY")
    print("="*60)

    for table_type, df in final_structured_tables.items():
        print(f"\n{table_type.upper()} TABLE:")
        print(f"  Total rows: {len(df)}")
        if len(df) > 0:
            print(f"  Columns: {list(df.columns)}")
            # Export for analysis
            output_file = f"ROW_FIX_TEST_{table_type.upper()}_TABLE.xlsx"
            df.to_excel(output_file, index=False)
            print(f"  ✓ Exported to: {output_file}")

            # Show sample data for BOM table
            if table_type == "bom" and debug_centered_fix:
                print(f"  Sample data:")
                print(df.head(5).to_string())

    print(f"\nOutliers: {len(final_combined_outliers)} items")
    print(f"Annotations: {len(final_annotations)} items")

    print("\n" + "="*60)
    print("ROW DETECTION FIX TEST COMPLETED")
    print("="*60)


if __name__ == "__main__":
    print("Running table extraction test with existing data...")

    # ===== CONFIGURATION FLAGS =====
    # Set this to True to skip number validation in leftmost column (for nozzle lists like H1, H2, N1, N2, etc.)
    SKIP_NUMBER_CHECK = True  # Change to False for normal BOM processing

    # Define paths to test data
    feather_file = r"C:\Users\<USER>\Documents\EXC_0015\AQT\data.feather"
    roi_file = r"C:\Users\<USER>\Documents\EXC_0015\AQT\roi_AQT.json"
    page_num = 1  # 1-based index for display

    print(f"Feather file: {feather_file}")
    print(f"ROI file: {roi_file}")
    print(f"Page number: {page_num}")
    print(f"Skip number check: {SKIP_NUMBER_CHECK}")

    # Load data from feather file
    try:
        print(f"Loading data from {feather_file}")
        raw_data_df = pd.read_feather(feather_file)
        print(f"Loaded {len(raw_data_df)} rows from feather file")

        # Filter for the specific page
        page_data = raw_data_df[raw_data_df['pdf_page'] == page_num]
        print(f"Found {len(page_data)} rows for page {page_num}")

    except Exception as e:
        print(f"Error loading feather file: {e}")
        exit(1)

    # Load ROI data
    try:
        print(f"Loading ROI data from {roi_file}")
        with open(roi_file, 'r') as f:
            roi_data = json.load(f)

        # Check if this is the new 'groups' format
        if 'groups' in roi_data:
            print("ROI data is in 'groups' format")
            # Extract ROI data from the groups structure
            # For now, we'll use the first group's first page
            first_group_key = list(roi_data['groups'].keys())[0]
            first_group = roi_data['groups'][first_group_key]

            if 'pages' in first_group and len(first_group['pages']) > 0:
                # Find the page that matches our page number
                target_page_data = None
                for page_info in first_group['pages']:
                    if page_info.get('page') == page_num:
                        target_page_data = page_info
                        break

                if target_page_data and 'roi' in target_page_data:
                    roi_payload = target_page_data['roi']
                else:
                    print(f"No ROI data found for page {page_num}")

            else:
                print("No pages found in first group")

        else:
            # Old format - direct ROI list
            print("ROI data is in legacy format")
            roi_payload = roi_data

    except Exception as e:
        print(f"Error loading ROI file: {e}")

    # Convert relative coordinates to absolute
    # Note: In the original implementation, page dimensions would come from the actual PDF
    # For this test with raw data, we need to determine dimensions from the data itself
    # or use the original PDF dimensions that were used when the data was extracted

    # Try to determine page dimensions from the coordinate data
    if len(page_data) > 0:
        # Get the maximum coordinates from the data to estimate page dimensions
        max_x = page_data['coordinates2'].apply(lambda coord: coord[2]).max()
        max_y = page_data['coordinates2'].apply(lambda coord: coord[3]).max()

        # Use these as approximate page dimensions
        page_width = max_x * 1.1  # Add some margin
        page_height = max_y * 1.1  # Add some margin

        print(f"Estimated page dimensions from data: width={page_width:.1f}, height={page_height:.1f}")
        converted_roi_payload = convert_relative_coords_to_points(roi_payload, page_width, page_height)
    else:
        print("Warning: No data available to determine page dimensions")
        print("Skipping coordinate conversion - using ROI data as-is")
        converted_roi_payload = roi_payload

    # Get table coordinates
    identified_tables = get_table_coordinates(converted_roi_payload)

    print("\nROI Information:")
    for table_type, (table_coords, column_coords, headers_selected) in identified_tables.items():
        print(f"  Found table type: {table_type}")
        print(f"  Columns for {table_type}:")
        for col in column_coords:
            print(f"    {col.get('columnName', 'Unknown')}")

    print("\nExtracting tables...")

    # Extract tables using the main function
    try:
        structured_tables, annotations_tables, outliers_df, annot_outliers_df = get_table_data(
            pdf_path="dummy_path.pdf",  # Dummy path since we're using raw data
            page=None,  # Not needed for raw data
            page_num=page_num-1,  # Convert to 0-based index
            converted_roi_payload=converted_roi_payload,
            page_text_blocks=None,  # Not needed for raw data
            raw_data_df=page_data,
            missing_pos=False,
            remove_outliers=True,
            skip_number_check=SKIP_NUMBER_CHECK
        )

        print("\nExtracted tables:")

        # Process and display each table
        for table_type, df in structured_tables.items():
            print(f"\nTable '{table_type}':")
            print(f"  Rows: {len(df)}")
            print(f"  Columns: {', '.join(df.columns)}")

            print("\nSample data:")
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', 1000)
            print(df.head(6).to_string())  # Show up to 6 rows

            # Save to CSV for inspection
            csv_file = f"table_{table_type}_page_{page_num}.csv"
            df.to_csv(csv_file, index=False)
            print(f"  Saved to {csv_file}")

            # For BOM tables, also export to Excel
            if table_type == 'bom':
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                excel_file = f"bom_export_{timestamp}.xlsx"
                df.to_excel(excel_file, index=False)
                print(f"  Exported BOM to {excel_file}")

        print("\nTest completed.")

    except Exception as e:
        print(f"Error during table extraction: {e}")
        import traceback
        traceback.print_exc()
