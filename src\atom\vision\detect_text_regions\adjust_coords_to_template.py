'''
Purpose:

Goal:
    Adjust coordinates from ROI JSON to match and 'snap to' detected grid regions in an image.

Concept:
    1. Use extract_roi_data_from_json to load json file
    2. Read label and the coordinates for each group from the item(page) in the group
    3. Associate label and coordinates to its text box
    4. Filter the detected bounding box and attempt to 'snap to' the nearest detected bounding box.
    5. On subsequent pages, the regions are slightly different. Using the layout from the first page,
       autoadjust the region on the subsequent pages.
    6. Export the adjusted regions to a new json file with new groups if different

Refinements:
7. Each cell almost always contains a label within the same detected grid square.
8. Now we want to refine the selection to its value by adjusting the container to make
sure the adjusted region does not contain the label. We will supply a manual built file telling where the header label is in relation to the value.
    For example:
        documentTitle:above
        service:above
        pipeSpec:above
        dp1:above
        op1:above
        dt1:above
        opt1:above
        vendorDocumentId:above
        pid:above
        area:above
        pwht:above
        test_type:above
        insulationSpec:above
        clean_spec:above
        xray:above
        test_psi:above
        insulationThickness:above
        lineNumber:above
        unit:above
        mediumCode:above
        projectNo:above
        sheet:above
        totalSheets:above
        revision:above

This tells us the header label is above the value and we can adjust the region to exclude the header label.
We can use identified text coordinates as shown in detect_grid_regions_cv2.py or detect_text_regions_cv2.py



'''
import os
import cv2
import json
import numpy as np
from src.atom.vision.detect_text_regions.read_roi import extract_roi_data_from_json
from src.atom.vision.detect_text_regions.detect_grid_regions_cv2 import detect_grid_lines
from src.atom.vision.detect_text_regions.detect_text_regions_cv2 import detect_text_regions


def load_label_positions(label_positions_file=None):
    """
    Load label position mapping from a file or use default mapping.

    Args:
        label_positions_file: Path to the file containing label position mappings

    Returns:
        Dictionary mapping field names to their label positions
    """
    default_positions = {
        "documentTitle": "above",
        "service": "above",
        "pipeSpec": "above",
        "dp1": "above",
        "op1": "above",
        "dt1": "above",
        "opt1": "above",
        "vendorDocumentId": "above",
        "pid": "above",
        "area": "above",
        "pwht": "above",
        "test_type": "above",
        "insulationSpec": "above",
        "clean_spec": "above",
        "xray": "above",
        "test_psi": "above",
        "insulationThickness": "above",
        "lineNumber": "above",
        "unit": "above",
        "mediumCode": "above",
        "projectNo": "above",
        "sheet": "above",
        "totalSheets": "above",
        "revision": "above"
    }

    if label_positions_file and os.path.exists(label_positions_file):
        try:
            with open(label_positions_file, 'r') as f:
                positions = {}
                for line in f:
                    line = line.strip()
                    if line and ':' in line:
                        field, position = line.split(':', 1)
                        positions[field.strip()] = position.strip()
                return positions
        except Exception as e:
            print(f"Error loading label positions file: {e}")
            return default_positions

    return default_positions

def refine_roi_with_label_position(region, label_positions, text_regions, img_width, img_height):
    """
    Refine ROI coordinates to exclude the header label based on its position.

    Args:
        region: Dictionary containing region information
        label_positions: Dictionary mapping field names to their label positions
        text_regions: List of detected text regions in the image
        img_width: Width of the image
        img_height: Height of the image

    Returns:
        Dictionary with refined coordinates and detected text regions
    """
    roi_name = region['name']
    adjusted_coords = region['adjusted_coords']

    # Check if we have a position mapping for this field
    if roi_name not in label_positions:
        return region

    label_position = label_positions[roi_name]

    # Convert relative coordinates to absolute
    abs_x0 = int(adjusted_coords['x0'] * img_width)
    abs_y0 = int(adjusted_coords['y0'] * img_height)
    abs_x1 = int(adjusted_coords['x1'] * img_width)
    abs_y1 = int(adjusted_coords['y1'] * img_height)

    # Create a copy of the region to modify
    refined_region = region.copy()
    refined_coords = adjusted_coords.copy()

    # Find text regions within this ROI
    contained_text_regions = []
    for tx, ty, tw, th in text_regions:
        # Check if the text region is within the ROI
        if (tx >= abs_x0 and tx + tw <= abs_x1 and
            ty >= abs_y0 and ty + th <= abs_y1):
            contained_text_regions.append((tx, ty, tw, th))

    # If no text regions found, return the original region
    if not contained_text_regions:
        return region

    # Store all contained text regions for visualization
    refined_region['contained_text_regions'] = contained_text_regions

    # Identify label and value regions based on position
    label_region = None
    value_regions = []

    # Special handling for known problematic fields
    if roi_name.lower() in ['mediumcode', 'medium_code']:
        # For medium code, we need to ensure we detect the header
        # Sort by y-coordinate (top to bottom)
        contained_text_regions.sort(key=lambda r: r[1])

        # For medium code, assume the top 1/3 of the cell contains the header
        top_third_boundary = abs_y0 + (abs_y1 - abs_y0) / 3

        # Find text regions in the top third
        top_regions = [r for r in contained_text_regions if r[1] < top_third_boundary]

        if top_regions:
            # Use the top region as the header
            label_region = top_regions[0]
            # Everything else is a value
            value_regions = [r for r in contained_text_regions if r != label_region]

            # Set new y0 to just below the label
            new_y0 = (label_region[1] + label_region[3]) / img_height
            refined_coords['y0'] = new_y0
        else:
            # If no header detected in top third, use standard approach
            label_region = contained_text_regions[0]
            value_regions = contained_text_regions[1:] if len(contained_text_regions) > 1 else []

    elif roi_name.lower() == 'revision':
        # For revision, we need to be careful about vertical lines
        # Sort by y-coordinate (top to bottom)
        contained_text_regions.sort(key=lambda r: r[1])

        # For revision, filter out very tall, narrow regions (likely vertical lines)
        filtered_regions = [r for r in contained_text_regions if r[3] < (abs_y1 - abs_y0) * 0.5 or r[2] > r[3] * 0.5]

        if filtered_regions:
            # Use standard approach on filtered regions
            label_region = filtered_regions[0]
            value_regions = filtered_regions[1:] if len(filtered_regions) > 1 else []

            # Set new y0 to just below the label
            new_y0 = (label_region[1] + label_region[3]) / img_height
            refined_coords['y0'] = new_y0
        else:
            # If all regions were filtered out, use original regions but be conservative
            label_region = contained_text_regions[0]
            value_regions = contained_text_regions[1:] if len(contained_text_regions) > 1 else []

    elif "test" in roi_name.lower() and "pressure" in roi_name.lower() or roi_name.lower() in ['test_psi', 'testpsi']:
        # For test pressure fields, handle multi-line headers
        # Sort by y-coordinate (top to bottom)
        contained_text_regions.sort(key=lambda r: r[1])

        if len(contained_text_regions) >= 2:
            # Check if the first two regions are close together vertically (likely a multi-line header)
            first_region = contained_text_regions[0]
            second_region = contained_text_regions[1]

            # If the second region starts close to where the first ends, treat both as header
            if (second_region[1] - (first_region[1] + first_region[3])) < first_region[3] * 0.5:
                # Combine the two regions into one header region
                min_x = min(first_region[0], second_region[0])
                min_y = min(first_region[1], second_region[1])
                max_x = max(first_region[0] + first_region[2], second_region[0] + second_region[2])
                max_y = max(first_region[1] + first_region[3], second_region[1] + second_region[3])

                label_region = (min_x, min_y, max_x - min_x, max_y - min_y)
                value_regions = contained_text_regions[2:] if len(contained_text_regions) > 2 else []

                # Set new y0 to just below the combined header
                new_y0 = max_y / img_height
                refined_coords['y0'] = new_y0
            else:
                # Standard approach
                label_region = first_region
                value_regions = contained_text_regions[1:]

                # Set new y0 to just below the label
                new_y0 = (label_region[1] + label_region[3]) / img_height
                refined_coords['y0'] = new_y0
        else:
            # Not enough regions for multi-line detection, use standard approach
            label_region = contained_text_regions[0]
            value_regions = []

    else:
        # Standard approach for other fields
        # Sort text regions based on position
        if label_position == "above":
            # Sort by y-coordinate (top to bottom)
            contained_text_regions.sort(key=lambda r: r[1])

            # The first region is likely the label, adjust y0 to exclude it
            if len(contained_text_regions) > 1:
                label_region = contained_text_regions[0]
                value_regions = contained_text_regions[1:]

                # Set new y0 to just below the label
                new_y0 = (label_region[1] + label_region[3]) / img_height
                refined_coords['y0'] = new_y0
            elif len(contained_text_regions) == 1:
                # Only one text region found, assume it's the label
                label_region = contained_text_regions[0]

                # Set new y0 to just below the label, but leave some space for a value
                new_y0 = (label_region[1] + label_region[3]) / img_height
                refined_coords['y0'] = new_y0

        elif label_position == "below":
            # Sort by y-coordinate (bottom to top)
            contained_text_regions.sort(key=lambda r: r[1] + r[3], reverse=True)

            # The first region is likely the label, adjust y1 to exclude it
            if len(contained_text_regions) > 1:
                label_region = contained_text_regions[0]
                value_regions = contained_text_regions[1:]

                # Set new y1 to just above the label
                new_y1 = label_region[1] / img_height
                refined_coords['y1'] = new_y1

        elif label_position == "left":
            # Sort by x-coordinate (left to right)
            contained_text_regions.sort(key=lambda r: r[0])

            # The first region is likely the label, adjust x0 to exclude it
            if len(contained_text_regions) > 1:
                label_region = contained_text_regions[0]
                value_regions = contained_text_regions[1:]

                # Set new x0 to just right of the label
                new_x0 = (label_region[0] + label_region[2]) / img_width
                refined_coords['x0'] = new_x0

        elif label_position == "right":
            # Sort by x-coordinate (right to left)
            contained_text_regions.sort(key=lambda r: r[0] + r[2], reverse=True)

            # The first region is likely the label, adjust x1 to exclude it
            if len(contained_text_regions) > 1:
                label_region = contained_text_regions[0]
                value_regions = contained_text_regions[1:]

                # Set new x1 to just left of the label
                new_x1 = label_region[0] / img_width
                refined_coords['x1'] = new_x1

    # Ensure minimum height for the refined region (prevent collapse)
    if label_position == "above" and 'y0' in refined_coords and 'y1' in refined_coords:
        min_height = 0.02  # Minimum 2% of image height
        if refined_coords['y1'] - refined_coords['y0'] < min_height:
            # If region would be too small, ensure minimum height while preserving y1
            refined_coords['y0'] = refined_coords['y1'] - min_height
            refined_region['height_adjusted'] = True

    # Store identified regions for visualization
    if label_region:
        refined_region['label_region'] = label_region
    if value_regions:
        refined_region['value_regions'] = value_regions

    # Update the region with refined coordinates
    refined_region['refined_coords'] = refined_coords

    return refined_region

def adjust_coords_to_template(roi_json_path, image_path, output_dir=None, debug=False, label_positions_file=None):
    """
    Adjust coordinates from ROI JSON to match detected grid regions in an image.

    Args:
        roi_json_path: Path to the ROI JSON file
        image_path: Path to the image to process
        output_dir: Directory to save output files (default: same as image_path)
        debug: Whether to enable debug mode
        label_positions_file: Path to the file containing label position mappings

    Returns:
        Dictionary containing adjusted regions and visualization paths
    """
    if output_dir is None:
        output_dir = os.path.dirname(image_path)

    os.makedirs(output_dir, exist_ok=True)

    # Load ROI data from JSON
    try:
        with open(roi_json_path, 'r') as f:
            roi_data = json.load(f)

        # Extract ROI details
        roi_details = extract_roi_data_from_json(roi_data)
        if not roi_details:
            print("No valid ROI data was extracted.")
            return None

    except FileNotFoundError:
        print(f"Error: The file '{roi_json_path}' was not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from the file '{roi_json_path}'. Check file format.")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while loading ROI data: {e}")
        return None

    # Detect grid lines and rectangles in the image
    try:
        grid_results = detect_grid_lines(
            image_path,
            debug=debug,
            debug_dir=output_dir if debug else None
        )

        if not grid_results or 'rectangles' not in grid_results or not grid_results['rectangles']:
            print("No grid regions detected in the image.")
            return None

    except Exception as e:
        print(f"Error detecting grid regions: {e}")
        return None

    # Get image dimensions for coordinate conversion
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read the image at '{image_path}'.")
        return None

    img_height, img_width = img.shape[:2]
    print(f"Image dimensions: {img_width}x{img_height}")

    # Create a copy of the image for visualization
    # Use a deep copy to ensure we don't modify the original image
    vis_img = img.copy()

    # Load label positions
    label_positions = load_label_positions(label_positions_file)
    print(f"Loaded {len(label_positions)} label position mappings")

    # Detect text regions for label refinement
    print("Detecting text regions for label refinement...")
    text_regions = detect_text_regions(img, debug=debug, debug_dir=output_dir if debug else None)
    print(f"Detected {len(text_regions)} text regions")

    # Process each ROI group and match with detected rectangles
    matched_regions = {}

    # Get relative rectangles from grid_results
    relative_rectangles = grid_results.get('relative_rectangles', [])
    if not relative_rectangles and 'rectangles' in grid_results and img_width and img_height:
        # Convert absolute rectangles to relative if needed
        relative_rectangles = []
        for rect_x, rect_y, rect_w, rect_h, rect_id in grid_results['rectangles']:
            rel_x = rect_x / img_width
            rel_y = rect_y / img_height
            rel_w = rect_w / img_width
            rel_h = rect_h / img_height
            relative_rectangles.append((rel_x, rel_y, rel_w, rel_h, rect_id))

    for group_id, rois in roi_details.items():
        group_regions = []

        for roi in rois:
            roi_name = roi['name']
            coords = roi['coordinates']

            # Extract coordinates
            roi_rel_x0 = coords['x0']
            roi_rel_y0 = coords['y0']
            roi_rel_x1 = coords['x1']
            roi_rel_y1 = coords['y1']

            # Check if this is a table or isometric drawing area
            is_table = roi.get('isTable', False)
            is_isometric_area = roi_name.lower() == 'isometric drawing area'

            # Skip auto-adjustment for tables and isometric drawing area
            if is_table or is_isometric_area:
                # Use original coordinates without adjustment
                best_match = None
                rect_id = f"original_{len(group_regions)}"
            else:
                # For regular regions, find the closest matching rectangle
                roi_rel_width = roi_rel_x1 - roi_rel_x0
                roi_rel_height = roi_rel_y1 - roi_rel_y0
                roi_rel_center_x = roi_rel_x0 + roi_rel_width / 2
                roi_rel_center_y = roi_rel_y0 + roi_rel_height / 2

                # Find the closest matching rectangle using relative coordinates
                best_match = None
                min_distance = float('inf')

                for rel_rect in relative_rectangles:
                    rel_x, rel_y, rel_w, rel_h, rect_id = rel_rect

                    # Calculate distance between centers using relative coordinates
                    rect_rel_center_x = rel_x + rel_w / 2
                    rect_rel_center_y = rel_y + rel_h / 2

                    # Calculate Euclidean distance in relative space
                    distance = ((roi_rel_center_x - rect_rel_center_x) ** 2 +
                               (roi_rel_center_y - rect_rel_center_y) ** 2) ** 0.5

                    if distance < min_distance:
                        min_distance = distance
                        best_match = rel_rect

            # For tables and isometric drawing area, use original coordinates
            # For other regions, use the matched rectangle coordinates if available
            is_table = roi.get('isTable', False)
            is_isometric_area = roi_name.lower() == 'isometric drawing area'

            if is_table or is_isometric_area:
                # Use original coordinates without adjustment
                matched_region = {
                    'name': roi_name,
                    'original_coords': {
                        'x0': roi_rel_x0,
                        'y0': roi_rel_y0,
                        'x1': roi_rel_x1,
                        'y1': roi_rel_y1
                    },
                    'adjusted_coords': {
                        'x0': roi_rel_x0,  # Keep original coordinates
                        'y0': roi_rel_y0,
                        'x1': roi_rel_x1,
                        'y1': roi_rel_y1
                    },
                    'rect_id': rect_id,
                    'is_table': is_table,
                    'column_names': roi.get('columnNames', []),
                    'column_ratios': roi.get('columnRatios', [])
                }

                group_regions.append(matched_region)

                # Convert to absolute coordinates for visualization
                abs_x = int(roi_rel_x0 * img_width)
                abs_y = int(roi_rel_y0 * img_height)
                abs_w = int((roi_rel_x1 - roi_rel_x0) * img_width)
                abs_h = int((roi_rel_y1 - roi_rel_y0) * img_height)

                # Use different color for tables and isometric areas (blue)
                color = (255, 0, 0) if is_table else (255, 165, 0)  # Blue for tables, orange for isometric area

                # Draw the rectangle and label
                cv2.rectangle(vis_img, (abs_x, abs_y), (abs_x + abs_w, abs_y + abs_h), color, 2)

                # Add text label
                label = f"{roi_name} (Original)"
                cv2.putText(vis_img, label, (abs_x, abs_y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            elif best_match:  # For regular regions with a match
                rel_x, rel_y, rel_w, rel_h, rect_id = best_match

                # Store the matched region with relative coordinates
                matched_region = {
                    'name': roi_name,
                    'original_coords': {
                        'x0': roi_rel_x0,
                        'y0': roi_rel_y0,
                        'x1': roi_rel_x1,
                        'y1': roi_rel_y1
                    },
                    'adjusted_coords': {
                        'x0': rel_x,
                        'y0': rel_y,
                        'x1': rel_x + rel_w,
                        'y1': rel_y + rel_h
                    },
                    'rect_id': rect_id,
                    'is_table': False,
                    'column_names': [],
                    'column_ratios': []
                }

                # Refine the region to exclude header label if applicable
                refined_region = refine_roi_with_label_position(
                    matched_region,
                    label_positions,
                    text_regions,
                    img_width,
                    img_height
                )

                group_regions.append(refined_region)

                # Convert to absolute coordinates for visualization
                abs_x = int(rel_x * img_width)
                abs_y = int(rel_y * img_height)
                abs_w = int(rel_w * img_width)
                abs_h = int(rel_h * img_height)

                # Draw the rectangle and label on the visualization image (green for adjusted)
                cv2.rectangle(vis_img, (abs_x, abs_y), (abs_x + abs_w, abs_y + abs_h), (0, 255, 0), 2)

                # Add text label
                label = f"{roi_name} (ID: {rect_id})"
                cv2.putText(vis_img, label, (abs_x, abs_y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                # If the region was refined, draw the refined region in a different color
                if 'refined_coords' in refined_region:
                    refined_coords = refined_region['refined_coords']
                    ref_x0 = int(refined_coords['x0'] * img_width)
                    ref_y0 = int(refined_coords['y0'] * img_height)
                    ref_x1 = int(refined_coords['x1'] * img_width)
                    ref_y1 = int(refined_coords['y1'] * img_height)

                    # Determine color based on whether height was adjusted
                    if refined_region.get('height_adjusted', False):
                        # Use orange for height-adjusted regions
                        rect_color = (0, 165, 255)  # Orange
                        refined_label = f"{roi_name} (Height Adjusted)"
                    else:
                        # Use purple for normally refined regions
                        rect_color = (255, 0, 255)  # Purple
                        refined_label = f"{roi_name} (Refined)"

                    # Draw the refined rectangle
                    cv2.rectangle(vis_img, (ref_x0, ref_y0), (ref_x1, ref_y1), rect_color, 2)

                    # Add refined label
                    cv2.putText(vis_img, refined_label, (ref_x0, ref_y0 - 25),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, rect_color, 1)

                    # Draw detected header label in cyan
                    if 'label_region' in refined_region:
                        lx, ly, lw, lh = refined_region['label_region']
                        # Fill the label region with semi-transparent cyan
                        overlay = vis_img.copy()
                        cv2.rectangle(overlay, (lx, ly), (lx + lw, ly + lh), (255, 255, 0), -1)  # Cyan fill
                        # Apply the overlay with transparency
                        alpha = 0.4  # Transparency factor
                        cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)
                        # Draw border
                        cv2.rectangle(vis_img, (lx, ly), (lx + lw, ly + lh), (255, 255, 0), 2)  # Cyan border

                    # Draw detected value regions in magenta
                    if 'value_regions' in refined_region:
                        for vx, vy, vw, vh in refined_region['value_regions']:
                            # Fill the value region with semi-transparent magenta
                            overlay = vis_img.copy()
                            cv2.rectangle(overlay, (vx, vy), (vx + vw, vy + vh), (255, 0, 255), -1)  # Magenta fill
                            # Apply the overlay with transparency
                            alpha = 0.4  # Transparency factor
                            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)
                            # Draw border
                            cv2.rectangle(vis_img, (vx, vy), (vx + vw, vy + vh), (255, 0, 255), 2)  # Magenta border

        if group_regions:
            matched_regions[group_id] = group_regions

    # Add a legend to the visualization
    legend_height = 140  # Increased height for additional legend items
    legend = np.ones((legend_height, vis_img.shape[1], 3), dtype=np.uint8) * 255  # White background

    # Draw legend items
    cv2.putText(legend, "Legend:", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Regular ROI example
    cv2.rectangle(legend, (120, 10), (150, 30), (0, 255, 0), 2)
    cv2.putText(legend, "Regular ROI", (160, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Table ROI example
    cv2.rectangle(legend, (300, 10), (330, 30), (255, 0, 0), 2)
    cv2.putText(legend, "Table ROI", (340, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Isometric area example
    cv2.rectangle(legend, (450, 10), (480, 30), (255, 165, 0), 2)
    cv2.putText(legend, "Isometric Area", (490, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Refined region example
    cv2.rectangle(legend, (120, 40), (150, 60), (255, 0, 255), 2)
    cv2.putText(legend, "Refined Region", (160, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Height-adjusted region example
    cv2.rectangle(legend, (450, 40), (480, 60), (0, 165, 255), 2)
    cv2.putText(legend, "Height-Adjusted Region", (490, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Header label region example
    overlay = legend.copy()
    cv2.rectangle(overlay, (300, 40), (330, 60), (255, 255, 0), -1)  # Cyan fill
    alpha = 0.4  # Transparency factor
    cv2.addWeighted(overlay, alpha, legend, 1 - alpha, 0, legend)
    cv2.rectangle(legend, (300, 40), (330, 60), (255, 255, 0), 2)  # Cyan border
    cv2.putText(legend, "Detected Header Label", (340, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Value region example
    overlay = legend.copy()
    cv2.rectangle(overlay, (450, 40), (480, 60), (255, 0, 255), -1)  # Magenta fill
    cv2.addWeighted(overlay, alpha, legend, 1 - alpha, 0, legend)
    cv2.rectangle(legend, (450, 40), (480, 60), (255, 0, 255), 2)  # Magenta border
    cv2.putText(legend, "Detected Value", (490, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Add note about label positions
    cv2.putText(legend, "Header labels are shown in yellow, values in magenta", (120, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(legend, "Special handling applied for: mediumCode, revision, test_pressure", (120, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(legend, "Orange regions had minimum height enforced to prevent collapse", (120, 125), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Combine the visualization with the legend
    vis_img_with_legend = np.vstack((vis_img, legend))

    # Save the visualization image
    vis_path = os.path.join(output_dir, 'adjusted_regions.png')

    # Ensure the output directory exists
    os.makedirs(os.path.dirname(vis_path), exist_ok=True)

    # Check if the image is valid before saving
    if vis_img_with_legend is not None and vis_img_with_legend.size > 0:
        success = cv2.imwrite(vis_path, vis_img_with_legend)
        if not success:
            print(f"Warning: Failed to save visualization image to {vis_path}")
            # Try an alternative format
            alt_path = os.path.join(output_dir, 'adjusted_regions.jpg')
            success = cv2.imwrite(alt_path, vis_img_with_legend)
            if success:
                vis_path = alt_path
                print(f"Saved visualization image in JPEG format instead: {vis_path}")
    else:
        print("Warning: Visualization image is invalid and cannot be saved.")

    # Save the adjusted regions to JSON
    output_json = {
        'original_roi_path': roi_json_path,
        'image_path': image_path,
        'groups': {}
    }

    for group_id, regions in matched_regions.items():
        output_json['groups'][group_id] = {
            'rois': []
        }

        for region in regions:
            # Use refined coordinates if available, otherwise use adjusted coordinates
            coords_to_use = region.get('refined_coords', region['adjusted_coords'])

            roi_data = {
                'name': region['name'],
                'relativeX0': coords_to_use['x0'],
                'relativeY0': coords_to_use['y0'],
                'relativeX1': coords_to_use['x1'],
                'relativeY1': coords_to_use['y1'],
                'isTable': region['is_table']
            }

            # Include both adjusted and refined coordinates for reference
            roi_data['adjusted_coords'] = {
                'x0': region['adjusted_coords']['x0'],
                'y0': region['adjusted_coords']['y0'],
                'x1': region['adjusted_coords']['x1'],
                'y1': region['adjusted_coords']['y1']
            }

            if 'refined_coords' in region:
                roi_data['refined_coords'] = {
                    'x0': region['refined_coords']['x0'],
                    'y0': region['refined_coords']['y0'],
                    'x1': region['refined_coords']['x1'],
                    'y1': region['refined_coords']['y1']
                }
                roi_data['has_label_refinement'] = True
            else:
                roi_data['has_label_refinement'] = False

            if region['is_table']:
                roi_data['columnNames'] = region['column_names']
                roi_data['columnRatios'] = region['column_ratios']

            output_json['groups'][group_id]['rois'].append(roi_data)

    output_json_path = os.path.join(output_dir, 'adjusted_template.json')
    with open(output_json_path, 'w') as f:
        json.dump(output_json, f, indent=4)

    # Count how many regions were refined
    refined_count = 0
    for group_id, regions in matched_regions.items():
        for region in regions:
            if 'refined_coords' in region:
                refined_count += 1

    print(f"\nRefined {refined_count} regions to exclude header labels")

    return {
        'matched_regions': matched_regions,
        'visualization_path': vis_path,
        'output_json_path': output_json_path,
        'refined_count': refined_count
    }

def visualize_roi_regions(image_path, roi_json_path, output_dir=None):
    """
    Visualize ROI regions from JSON on the image without adjusting coordinates.

    Args:
        image_path: Path to the image
        roi_json_path: Path to the ROI JSON file
        output_dir: Directory to save output files (default: same as image_path)

    Returns:
        Path to the visualization image
    """
    if output_dir is None:
        output_dir = os.path.dirname(image_path)

    os.makedirs(output_dir, exist_ok=True)

    # Load ROI data
    try:
        with open(roi_json_path, 'r') as f:
            roi_data = json.load(f)

        roi_details = extract_roi_data_from_json(roi_data)
        if not roi_details:
            print("No valid ROI data was extracted.")
            return None

    except Exception as e:
        print(f"Error loading ROI data: {e}")
        return None

    # Load image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read the image at '{image_path}'.")
        return None

    img_height, img_width = img.shape[:2]
    print(f"Original image dimensions: {img_width}x{img_height}")

    # Create a deep copy for visualization
    vis_img = img.copy()

    # Draw ROI regions
    for _, rois in roi_details.items():
        for roi in rois:
            roi_name = roi['name']
            coords = roi['coordinates']

            # Convert relative coordinates to absolute pixel values
            x0 = int(coords['x0'] * img_width)
            y0 = int(coords['y0'] * img_height)
            x1 = int(coords['x1'] * img_width)
            y1 = int(coords['y1'] * img_height)

            # Draw rectangle
            cv2.rectangle(vis_img, (x0, y0), (x1, y1), (0, 0, 255), 2)

            # Add text label
            cv2.putText(vis_img, roi_name, (x0, y0 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    # Save visualization
    vis_path = os.path.join(output_dir, 'roi_regions.png')

    # Ensure the output directory exists
    os.makedirs(os.path.dirname(vis_path), exist_ok=True)

    # Check if the image is valid before saving
    if vis_img is not None and vis_img.size > 0:
        success = cv2.imwrite(vis_path, vis_img)
        if not success:
            print(f"Warning: Failed to save ROI visualization image to {vis_path}")
            # Try an alternative format
            alt_path = os.path.join(output_dir, 'roi_regions.jpg')
            success = cv2.imwrite(alt_path, vis_img)
            if success:
                vis_path = alt_path
                print(f"Saved ROI visualization image in JPEG format instead: {vis_path}")
    else:
        print("Warning: ROI visualization image is invalid and cannot be saved.")

    return vis_path

def create_sample_label_positions_file(output_path):
    """
    Create a sample label positions file with default mappings.

    Args:
        output_path: Path to save the sample file

    Returns:
        Path to the created file
    """
    default_positions = {
        "documentTitle": "above",
        "service": "above",
        "pipeSpec": "above",
        "dp1": "above",
        "op1": "above",
        "dt1": "above",
        "opt1": "above",
        "vendorDocumentId": "above",
        "pid": "above",
        "area": "above",
        "pwht": "above",
        "test_type": "above",
        "insulationSpec": "above",
        "clean_spec": "above",
        "xray": "above",
        "test_psi": "above",
        "insulationThickness": "above",
        "lineNumber": "above",
        "unit": "above",
        "mediumCode": "above",
        "projectNo": "above",
        "sheet": "above",
        "totalSheets": "above",
        "revision": "above"
    }

    try:
        with open(output_path, 'w') as f:
            for field, position in default_positions.items():
                f.write(f"{field}:{position}\n")
        print(f"Sample label positions file created at: {output_path}")
        return output_path
    except Exception as e:
        print(f"Error creating sample label positions file: {e}")
        return None

if __name__ == '__main__':
    # Example usage
    roi_json_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\test_adjust.json"
    image_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\images\page_1.png"
    output_dir = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output"

    # Create a sample label positions file
    label_positions_file = os.path.join(output_dir, 'label_positions.txt')
    create_sample_label_positions_file(label_positions_file)

    try:
        # First, visualize the original ROI regions
        print("Visualizing original ROI regions...")
        vis_path = visualize_roi_regions(image_path, roi_json_path, output_dir)
        if vis_path:
            print(f"Original ROI visualization saved to: {vis_path}")

        # Then adjust coordinates to match detected grid regions
        print("\nAdjusting coordinates to template...")
        result = adjust_coords_to_template(roi_json_path, image_path, output_dir, debug=True, label_positions_file=label_positions_file)

        if result:
            print(f"\nAdjusted regions visualization saved to: {result['visualization_path']}")
            print(f"Adjusted template JSON saved to: {result['output_json_path']}\n")

            # Print summary of matched regions
            print("Matched Regions Summary:")
            for group_id, regions in result['matched_regions'].items():
                print(f"\n--- Group ID: {group_id} ---")
                for region in regions:
                    print(f"  Name: {region['name']} (Rectangle ID: {region['rect_id']})")
                    print(f"  Original coordinates: {region['original_coords']}")
                    print(f"  Adjusted coordinates: {region['adjusted_coords']}")
                    if 'refined_coords' in region:
                        print(f"  Refined coordinates: {region['refined_coords']} (Label excluded)")
                    if region['is_table']:
                        print(f"  Is Table: {region['is_table']}")
                        print(f"  Column Names: {region['column_names']}")
                        print(f"  Column Ratios: {region['column_ratios']}")
        else:
            print("Failed to adjust coordinates to template.")

    except FileNotFoundError:
        print(f"Error: One of the input files was not found.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
