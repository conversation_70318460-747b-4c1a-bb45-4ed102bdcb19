# Developer Context: LLM Tools for BOM Classification Audit System

## 📜 Introduction

This document provides context on key technologies and frameworks like **LangChain** and **Function Calling/Structured Output (JSON Mode)**, which are relevant to the development of the BOM Classification Audit System. Understanding these concepts will help in building, maintaining, and extending the system effectively, particularly in how we interact with Large Language Models (LLMs) like Gemini.

The primary goals are to manage complex AI workflows, ensure reliable data exchange with LLMs, and build a modular, extensible system as outlined in the "BOM Classification Audit System - Development Guide."

---

## 🧠 LangChain Explained

### What is LangChain?

**LangChain** is an open-source framework designed to simplify the development of applications powered by Large Language Models (LLMs). It provides a standard interface for chains, integrations with various LLMs and tools, and end-to-end chains for common applications.

Think of it as a **toolkit or an orchestrator** that helps you:
* **Manage Prompts:** Create, optimize, and serialize prompts.
* **Chain LLM Calls:** Combine multiple LLM calls or calls to other utilities in a sequence (or graph).
* **Integrate Data Sources:** Connect LLMs to external data sources (e.g., document loaders for RAG).
* **Build Agents:** Allow LLMs to make decisions, take actions, observe outcomes, and iterate.
* **Memory:** Enable chains and agents to remember previous interactions.
* **Callbacks:** Log and stream intermediate steps of any chain.

### Core Benefits of LangChain:

* **Modularity:** Components are designed to be used independently or composed together.
* **Abstraction:** Provides a common interface for different LLMs, making it easier to switch models (e.g., `Multi-Model Support` feature).
* **Pre-built Components:** Offers many ready-to-use chains, agents, and tools for common tasks.
* **Improved Readability & Maintainability:** Complex LLM workflows can be structured more clearly.

### Relevance to Your BOM Audit System:

LangChain can be particularly beneficial for orchestrating your `BOMAuditPipeline`:

1.  **Pipeline Orchestration:** The sequence of steps (`PreProcessingStep`, `RuleValidationStep`, `ConditionalLLMStep`, `PostProcessingStep`, `MetricsCollectionStep`) can be implemented as a LangChain "Chain." This can simplify the management of context (`context` dictionary) passing between steps and the overall flow control.
2.  **Conditional Logic:** LangChain supports conditional execution of steps, directly aligning with your `ConditionalLLMStep` (e.g., `RouterChain` or custom logic within a chain).
3.  **Prompt Management:** While your `LLMHandler` already has prompt building logic, LangChain's `PromptTemplate` objects can make constructing and managing the dynamic parts of your prompts (like including conditional column descriptions or field-specific rules) more structured and less error-prone.
4.  **Multi-Model Support & Fallbacks:** LangChain can abstract the model switching logic. You could define your primary and fallback LLMs within LangChain and let it handle the retry/fallback mechanism.
5.  **Future Enhancements:** For features like "A/B Testing Framework" or "Continuous Learning," LangChain's structure can make it easier to swap out components or add new chains.

---

## 📞 Function Calling & Structured Output (JSON Mode)

### What is Function Calling?

In the context of LLMs, **function calling** allows the model to intelligently decide to invoke external functions or tools you define. You describe your functions to the LLM, and the LLM can then output a JSON object containing the name of a function it wants to call and the arguments to use. Your code then executes this function and can return the result to the LLM to continue the process.

**Example Flow:**
1.  User asks: "What's the weather in London?"
2.  You send this to the LLM along with a description of a function `get_weather(location)`.
3.  LLM responds with: `{ "function_call": { "name": "get_weather", "arguments": { "location": "London" } } }`
4.  Your code parses this, calls your actual `get_weather("London")` function.
5.  Your function returns, e.g., "15°C and sunny."
6.  You send this result back to the LLM.
7.  LLM generates a natural language response: "The weather in London is 15°C and sunny."

### What is Structured Output (e.g., Gemini's JSON Mode)?

This is closely related and often a prerequisite or a part of function calling. **Structured output** means configuring the LLM to return its response in a specific, predictable format, most commonly JSON, adhering to a schema you implicitly or explicitly provide.

Gemini's **JSON Mode** (as used in your `LLMHandler` via `response_mime_type: "application/json"`) is a powerful form of structured output. You guide the LLM (often via the system prompt) on the expected JSON structure, and the model aims to generate output that conforms to it.

### Benefits:

* **Reliable Data Extraction:** Instead of parsing natural language, you get a machine-readable format directly.
* **Reduced Errors:** Minimizes mistakes from trying to regex or string-match information from free-form text.
* **Programmatic Interaction:** Enables seamless integration of LLM outputs into your application logic.
* **Enables Complex Workflows:** Allows LLMs to interact with external systems and data.

### Relevance to Your BOM Audit System:

Your `LLMHandler` is a prime example of leveraging **structured output (JSON Mode)**:

* The system prompt for `LLMHandler` (`_get_system_prompt`) explicitly defines the expected JSON output structure:
    ```json
    {
        "status": "ok" | "issues",
        "issues": {
            "field_name": {
                "value": "current_value",
                "confidence": 0.0-1.0,
                "explanation": "reason for issue",
                "suggested": "corrected_value"
            }
        }
    }
    ```
* This is crucial for the `PostProcessingStep` and `_format_result` in `BOMAuditPipeline` to reliably parse the LLM's findings and integrate them with rule-based issues.
* While you're not currently having the LLM *decide* to call an external Python function during its generation (classic function calling), you are using its ability to structure its *response* as a function's output would be structured. This is a key capability.

---

## 🧩 How These Tools Fit into Your System

1.  **Overall Workflow (`BOMAuditPipeline`):**
    * **LangChain** can serve as the backbone for the `BOMAuditPipeline`, defining it as a sequence of connected components (steps). It can manage the flow, conditional logic (e.g., `ConditionalLLMStep`), and context propagation.
    * Each `PipelineStep` could be a LangChain `Runnable` or part of a custom LangChain `Chain`.

2.  **LLM Interaction (`LLMHandler`):**
    * This component is already correctly designed to leverage **Gemini's JSON Mode** for structured output.
    * If using LangChain, the `LLMHandler`'s logic would be wrapped or called by a LangChain component (e.g., an `LLMChain` or a custom `Runnable` that uses the `google-generativeai` SDK). LangChain would help construct the detailed prompt (`_build_prompt`) and process the JSON response.

3.  **Rule Management (`RuleBank`, `RuleValidationStep`):**
    * These components primarily involve Python logic. LangChain's role here would be to integrate this step smoothly into the overall pipeline.

---

## 💡 Pseudo-Code Logic for LLM Audit (Recap)

The core LLM interaction involves sending the material description, current classification, relevant rules/context, and asking for an audit in a specific JSON format.

**Refined Pseudo-Prompt for `LLMHandler._build_prompt`:**

The system prompt (already well-defined in `LLMHandler._get_system_prompt`) sets the stage for the expected JSON output. The user prompt then provides the specific instance details.

You are a precise BOM classification auditor.
Your task is to review classifications against the material description and rules.
CRITICAL RULES:

Only classify based on explicit information in the description.
Vendor codes (e.g., VENDOR XXXXX) should be classified with 'Category' as 'uncategorized'.
Adhere strictly to the provided Field Rules and Valid Options for each field.
If a field's value is correct according to the description and rules, do not list it as an issue.
Provide confidence scores between 0.0 and 1.0 for any suggested corrections. A score of 1.0 means absolute certainty. A lower score indicates less certainty.
If you identify an issue but cannot confidently suggest a correction, set "suggested" to null or an empty string, and use a low confidence score.
If no issues are found for any field, the "issues" object should be empty.
Response format MUST BE JSON: { "status": "ok" | "issues", // "issues" if one or more fields have problems, "ok" otherwise "issues": { // This object should be empty if status is "ok" "field_name_1": { // e.g., "Material" "value": "current_value_as_provided", "confidence": 0.0-1.0, // Your confidence in the suggested correction "explanation": "Brief reason why the current value is incorrect or why the suggestion is better, based on description and rules.", "suggested": "corrected_value_or_null" // The corrected value, or null/empty if no suggestion }, "field_name_2": { ... } // Potentially other fields with issues } }
NOW, REVIEW THE FOLLOWING BOM CLASSIFICATION:

Material Description:
"${description}" // e.g., "Pipe SMLS, ASTM A106, SCH 60, #3000 , PBE"

Current Classification:
${json.dumps(classification, indent=2)}
// e.g.,
// {
//   "Category": "miscellaneous",
//   "Astm": "a106",
//   "Material": "stainless",
//   ...
// }

Contextual Information & Rules (Pay close attention to these):
${json.dumps(field_rules_and_column_descriptions, indent=2)}
// This will include:
// - General description for each column/field (e.g., "Astm: This covers the ASTM standards according to...")
// - For each classified field: its description, valid_options, and specific cross-field/pattern rules from RuleBank.
// e.g.,
// {
//   "Material": {
//     "description": "The base material type...",
//     "valid_options": ["carbon", "stainless", ...],
//     "rules": [
//       { "type": "cross_field", "condition": {"field": "Astm", "value": "a106"}, "requirement": {"field": "Material", "value": "carbon"}, ... }
//     ]
//   },
//   "Astm": { // Dynamic column description provided
//      "description": "This covers the ASTM standards according to blah blah blah."
//   }
//   ...
// }

Previously Identified Rule-Based Issues (Consider these, but perform your own full analysis):
${json.dumps(context.get("rule_issues", {}), indent=2)}

Analyze each field in the "Current Classification" against the "Material Description" and the provided "Field Rules and Valid Options".
If a field is incorrect, provide the original value, your suggested correction, an explanation, and your confidence score.
If you cannot suggest a correction for an incorrect field, state that in the explanation and leave "suggested" as null/empty with a low confidence.
If all fields are correct, return "status": "ok" and an empty "issues" object.


**Key considerations for the `LLMHandler._build_prompt` method:**
* Ensure `field_rules_and_column_descriptions` is dynamically constructed based on the `classification.keys()` and any globally provided column descriptions.
* The LLM needs clear instructions on how to format the output, especially the `confidence` score and what to do if no correction can be suggested.

---

## ⚖️ Alternative Approaches & Recommendations

### 1. Direct SDK Usage (Your Current Primary Approach for `LLMHandler`)
* **Pros:**
    * Full, fine-grained control over API calls.
    * Fewer dependencies if the overall workflow is simple.
    * Directly leverages specific features of an LLM provider (like Gemini's tuned JSON mode).
* **Cons:**
    * Can lead to more boilerplate code for managing prompts, chaining calls, error handling across multiple steps, and conditional logic.
    * Switching LLM providers or models with different APIs can require significant refactoring.

### 2. LangChain
* **Pros:**
    * Speeds up development for complex, multi-step LLM workflows.
    * Good for abstraction, making it easier to manage prompts, chains, and switch models.
    * Encourages modular design.
    * Built-in support for retries, fallbacks, and other common patterns.
* **Cons:**
    * Can have a learning curve.
    * Might introduce some overhead if only very simple LLM calls are needed.
    * Sometimes, fine-tuning specific model parameters might be slightly less direct than using the native SDK (though usually still possible).

### Recommendations for BOM Audit System:

* **Overall Pipeline Orchestration (`BOMAuditPipeline`):**
    * **Strongly consider using LangChain.** The multi-step nature, conditional LLM calls, and potential for future enhancements (A/B testing, more complex agentic behavior) make LangChain a good fit for structuring and managing the `BOMAuditPipeline`. It will improve readability, maintainability, and ease of extension.

* **Direct LLM Interaction within `LLMHandler` (or its LangChain equivalent):**
    * Continue to leverage **Gemini's JSON mode** (via `response_mime_type: "application/json"`) for reliable structured output. This is a powerful feature of the native SDK.
    * If using LangChain, you would wrap the Gemini SDK calls. LangChain's `LLM` or `ChatModel` integrations for Gemini would still allow you to specify generation parameters like `response_mime_type`.

* **It's Not Necessarily Either/Or:**
    * You can use LangChain to orchestrate the pipeline and still use the `google-generativeai` SDK directly within a custom LangChain `Runnable` or tool if you need very specific control that LangChain's abstractions might obscure (though this is increasingly rare). However, LangChain's native Gemini integrations are quite comprehensive.

---

## 🔑 Key Takeaways for Developers

* **LangChain for Workflow:** Familiarize yourselves with LangChain concepts like `Chains`, `Runnables`, `PromptTemplates`, and `LLM/ChatModel` integrations. This will be valuable for understanding and contributing to the `BOMAuditPipeline` structure.
* **Master Structured Output:** The use of Gemini's JSON mode is critical. Understand how to effectively prompt for desired JSON schemas and how to handle responses. This is central to the `LLMHandler`.
* **Modular Design:** The current plan already promotes this. LangChain reinforces it, making it easier to test, modify, or replace individual steps (e.g., `RuleValidationStep`, `LLMHandler`) without impacting the entire system.
* **Configuration is Key:** The `AuditConfig` and `CategoryConfig` are excellent. LangChain can also benefit from such configuration-driven design.

---

## 🚀 Next Steps for Potential Integration

1.  **Evaluate `BOMAuditPipeline` for LangChain Refactoring:**
    * Identify how `PreProcessingStep`, `RuleValidationStep`, `ConditionalLLMStep`, etc., could be mapped to LangChain `Runnables`.
    * Explore LangChain's mechanisms for context passing (which mirrors your current `context` dictionary).
2.  **LangChain `PromptTemplate` for `LLMHandler`:**
    * Consider rewriting `_build_prompt` using LangChain's `PromptTemplate` for better organization, especially with dynamic inclusion of rules and column descriptions.
3.  **Model Abstraction with LangChain:**
    * Investigate using LangChain's `ChatGoogleGenerativeAI` (or similar) wrapper to handle `primary_model` and `fallback_model` logic more abstractly.
4.  **Error Handling and Retries:**
    * Leverage LangChain's built-in retry mechanisms for LLM calls if applicable, or ensure your existing retry logic in `LLMHandler` integrates well if it's part of a LangChain chain.

This approach allows you to benefit from LangChain's orchestration capabilities while still us