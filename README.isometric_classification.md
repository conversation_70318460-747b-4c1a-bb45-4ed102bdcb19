# Isometric Classification

## Objective

1. Process PDF files containing isometric diagrams to detect and classify various components such as pipes, measurements, pointers, and general items like gussets.

2. Assign labels to components

3. Adopt a process that generalizes well and does not rely on embedded data in the PDF

## Current process

#### Main script - `scripts\s16061_construct_bom.py and scripts folder`

### Ideal prequisites

1. Accurate text data
2. Accurate regions and tighter bounding boxes
3. Oriented bounding boxes if text is rotated
4. Roboflow classifications, bounding boxes and anything useful
5. Optional - use of data to generate a cleaned image
6. Embedded data could be useful in modifying the image for better detection
7. Correctly oriented image. Any rotation in a scanned image should be corrected.

### Detecting the isometric lines, measurement lines and arrows

See `scripts\cv2_detection_utils\feature_detection.py`. Run directly to experiment with configuration using GUI. Or call detect_image_features
with selected method and params.

#### Feature (point) Detection

See GUI script - `scripts\cv2_detection_utils\feature_detection.py`

![alt text](docs/images/readme_isometric_classification_akaze.png "Feature detection GUI")

Detect `x,y` points on the isometric

Given an input image, this will return points on the image that are likely to be part of an isometric line and pick up other points on other components

Defaulting to `AKAZE` detection provides decent results and pretty fast. Though fine-tuning or alternative methods can be explored for different drawings or better results.

#### Forming lines using detected points

Lines are formed using a collinear approach to group detection points based on given desired relative angles.

These angles are the angles of the isometric lines e.g. vertical and diagonals.

The relative angles are calculated as the angle from a reference points to all other points. All detection points are checked.

Duplicate lines are removed such that there cannot be two lines which have the same slope cannot have a point in common.

For example, if [(a,b), (c,d)] and [(e, f), (a,b)] are detected as 30 degree lines, the second line is merged as they are extensions of the same line i.e. the merged line will be [(a,b), (c,d), (e, f)].

Cleaning up lines. Some line points are connected despite not being actually/visually connected.
e.g. points which were detected to be on same oriented slope even though whitespace between them

See `split_visible_segments` function. This splits the line into segments which are visually connected. This uses whitespace percentage to determine if a segment is connected.

![alt text](docs/images/readme_isometric_classification_akaze_points_connected.png "Connected lines")

In image above, display detected feature points as circles. Red circle is completely (incorrectly) unconnected point.

Pink circles show connected points. The green lines show valid connected lines. The thin red lines show lines which are not visually connected i.e. discarded from `split_visible_segments` function.

As shown in the result image, there are improvements to be made for more accurate deconstruction of the isometric.

#### Assigning labels to lines

TODO


## NOTES

1. Finetine adaptive angle tolerance. Longer distance between points need stricter tolerance. Shorter distance between points need looser tolerance.
2. Potentially tighten the bounding boxes. Desired, if OCR or raw data bboxes are large.
3. Improve line detection. Multiple passes might be needed to pick up more accurate or different lines
4. A complete accurate breakdown of lines (positions and lengths) may not be possible with current approach.
   Look for alternative methods to make best educated guesses to assign values. Brute force and process of elimination could be used.

## Other - methods tried which were not used or successful

1. `Hough line detection` - produces too many small and noisy lines. So feature points are currently preferred. Hough may still be used for
   other aspects of detection
2. `Skimage line detection via skeletonization` - skeletonize contours to produce cleaner, single lines. Slow, additional libraries required and lines not ideal