-- Simple function to directly call and compare BOM/General values
-- No fancy stuff, just straight database queries

CREATE OR REPLACE FUNCTION public.simple_component_check(
    p_project_id INTEGER
)
RETURNS TEXT
AS $$
DECLARE
    bom_result RECORD;
    general_result RECORD;
    report TEXT := '';
BEGIN
    -- Get BOM totals
    SELECT 
        COALESCE(SUM(length), 0) AS length,
        COALESCE(SUM(elbows_90), 0) AS elbows_90,
        COALESCE(SUM(elbows_45), 0) AS elbows_45, 
        COALESCE(SUM(bevels), 0) AS bevels,
        COALESCE(SUM(tees), 0) AS tees,
        COALESCE(SUM(reducers), 0) AS reducers
    INTO bom_result
    FROM public.bom
    WHERE project_id = p_project_id;
    
    -- Get General totals
    SELECT 
        COALESCE(SUM(length), 0) AS length,
        COALESCE(SUM(elbows_90), 0) AS elbows_90,
        COALESCE(SUM(elbows_45), 0) AS elbows_45,
        COALESCE(SUM(bevels), 0) AS bevels,
        COALESCE(SUM(tees), 0) AS tees,
        COALESCE(SUM(reducers), 0) AS reducers
    INTO general_result
    FROM public.general
    WHERE project_id = p_project_id;
    
    -- Build report
    report := report || 'BOM VS GENERAL COMPARISON FOR PROJECT ' || p_project_id || E'\n';
    report := report || '----------------------------------------' || E'\n';
    report := report || 'Column       | BOM Value      | General Value  | Difference' || E'\n';
    report := report || '-------------|----------------|----------------|------------' || E'\n';
    report := report || format('length       | %14s | %14s | %s', 
                bom_result.length, general_result.length, 
                general_result.length - bom_result.length) || E'\n';
    report := report || format('elbows_90    | %14s | %14s | %s', 
                bom_result.elbows_90, general_result.elbows_90, 
                general_result.elbows_90 - bom_result.elbows_90) || E'\n';
    report := report || format('elbows_45    | %14s | %14s | %s', 
                bom_result.elbows_45, general_result.elbows_45, 
                general_result.elbows_45 - bom_result.elbows_45) || E'\n';
    report := report || format('bevels       | %14s | %14s | %s', 
                bom_result.bevels, general_result.bevels, 
                general_result.bevels - bom_result.bevels) || E'\n';
    report := report || format('tees         | %14s | %14s | %s', 
                bom_result.tees, general_result.tees, 
                general_result.tees - bom_result.tees) || E'\n';
    report := report || format('reducers     | %14s | %14s | %s', 
                bom_result.reducers, general_result.reducers, 
                general_result.reducers - bom_result.reducers) || E'\n';
    
    RETURN report;
END;
$$ LANGUAGE plpgsql;
