SCHEMA_CLIENTS = """
    /* SCHEMA_CLIENTS */
    /* Stores client information including contact details. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_clients;

    /* Create table: atem_clients */
    CREATE TABLE public.atem_clients (
        id SERIAL PRIMARY KEY,
        client_name VARCHAR(255) NOT NULL UNIQUE,
        contact_name VARCHAR(255),
        contact_email VARCHAR(255),
        contact_phone VARCHAR(50),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_clients IS 'Stores client information including contact details.';
    COMMENT ON COLUMN public.atem_clients.id IS 'Unique identifier for each client.';
    COMMENT ON COLUMN public.atem_clients.client_name IS 'Name of the client.';
    COMMENT ON COLUMN public.atem_clients.contact_name IS 'Name of the contact person.';
    COMMENT ON COLUMN public.atem_clients.contact_email IS 'Email of the contact person.';
    COMMENT ON COLUMN public.atem_clients.contact_phone IS 'Phone number of the contact person.';
    COMMENT ON COLUMN public.atem_clients.address IS 'Address of the client.';
    COMMENT ON COLUMN public.atem_clients.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_clients.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_PROJECTS = """
    /* SCHEMA_PROJECTS */
    /* Stores project information. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_projects;

    /* Create table: atem_projects */
    CREATE TABLE public.atem_projects (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES public.atem_clients(id),
        project_name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        jobsite_location VARCHAR(255),
        bid_revision VARCHAR(50),
        bid_due_date TIMESTAMP,
        received_from_client TIMESTAMP,
        engineering_drafter VARCHAR(100),
        ais_project_status VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Index */
    CREATE INDEX idx_projects_client_id ON public.atem_projects(client_id);

    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_projects IS 'Stores project information.';
    COMMENT ON COLUMN public.atem_projects.id IS 'Unique identifier for each project.';
    COMMENT ON COLUMN public.atem_projects.project_name IS 'Name of the project.';
    COMMENT ON COLUMN public.atem_projects.client_id IS 'Identifier for the client associated with the project.';
    COMMENT ON COLUMN public.atem_projects.location IS 'General location of the project.';
    COMMENT ON COLUMN public.atem_projects.jobsite_location IS 'Specific jobsite location of the project.';
    COMMENT ON COLUMN public.atem_projects.bid_revision IS 'Bid revision version.';
    COMMENT ON COLUMN public.atem_projects.bid_due_date IS 'Date when bid is due.';
    COMMENT ON COLUMN public.atem_projects.received_from_client IS 'Date when the project was received from client.';
    COMMENT ON COLUMN public.atem_projects.engineering_drafter IS 'Engineering drafter assigned to the project.';
    COMMENT ON COLUMN public.atem_projects.ais_project_status IS 'Status of the project at Archiekt Integrated Systems.';
    COMMENT ON COLUMN public.atem_projects.notes IS 'Additional notes related to the project.';
    COMMENT ON COLUMN public.atem_projects.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_projects.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_COMPONENT_MAPPING = """
    /* SCHEMA_COMPONENT_MAPPING */
    /* Stores component mapping information for RFQs. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_bom_component_mapping;

    /* Create table: atem_bom_component_mapping */
    CREATE TABLE public.atem_bom_component_mapping (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES public.atem_projects(id),
        profile_id INTEGER REFERENCES public.atem_client_profiles(id),
        class_description VARCHAR(255),
        component_name VARCHAR(255),
        takeoff_category VARCHAR(100),
        general_category VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Indexes for performance optimization */
    /* Create additional useful indexes based on likely query patterns */
    CREATE INDEX IF NOT EXISTS idx_bom_component_mapping_component ON public.atem_bom_component_mapping(component_name);
    CREATE INDEX IF NOT EXISTS idx_bom_component_mapping_takeoff ON public.atem_bom_component_mapping(takeoff_category);
    CREATE INDEX IF NOT EXISTS idx_bom_component_mapping_general ON public.atem_bom_component_mapping(general_category);

    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_bom_component_mapping IS 'Stores component mapping information for RFQs.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.id IS 'Unique identifier for each component mapping entry.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.profile_id IS 'Identifier for the client profile associated with the component mapping.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.class_description IS 'Description of the class.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.component_name IS 'Name of the component.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.takeoff_category IS 'Takeoff category for the component.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.general_category IS 'General category for the component.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_bom_component_mapping.updated_at IS 'Timestamp when the record was last updated.';
"""

SCHEMA_RFQ_INPUT = """
    /* SCHEMA_RFQ_INPUT */
    /* Stores standardized and deduplicated RFQ input data for materials and 
    components used across projects. This table serves as the master reference for normalized 
    material descriptions, specifications, categorization, and additional attributes. It ensures 
    consistent classification, accurate aggregation, and efficient cross-referencing in related 
    tables, particularly when generating detailed RFQs and managing bill of materials data. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_rfq_input;

    /* Create table: atem_rfq_input */
    CREATE TABLE public.atem_rfq_input (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES public.atem_projects(id),
        material_description VARCHAR(255) NOT NULL UNIQUE,
        normalized_description VARCHAR(255),
        rfq_scope VARCHAR(100),
        general_category VARCHAR(100),
        unit_of_measure VARCHAR(50),
        material VARCHAR(100),
        abbreviated_material VARCHAR(50),
        technical_standard VARCHAR(50),
        astm VARCHAR(50),
        grade VARCHAR(50),
        rating VARCHAR(50),
        schedule VARCHAR(50),
        coating VARCHAR(50),
        forging VARCHAR(50),
        ends VARCHAR(50),
        item_tag VARCHAR(100),
        tie_point VARCHAR(100),
        pipe_category VARCHAR(50),
        valve_type VARCHAR(50),
        fitting_category VARCHAR(50),
        weld_category VARCHAR(50),
        bolt_category VARCHAR(50),
        gasket_category VARCHAR(50),
        notes TEXT,
        deleted BOOLEAN DEFAULT FALSE,
        ignore_item BOOLEAN DEFAULT FALSE,
        validated_date TIMESTAMP,
        validated_by VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by VARCHAR(100)
        mapping_not_found BOOLEAN DEFAULT FALSE
    );

    CREATE INDEX idx_input_data_project_id ON public.atem_rfq_input(project_id);
    CREATE INDEX idx_input_data_material_description ON public.atem_rfq_input(material_description);
    
    -- Add a composite unique constraint
    ALTER TABLE public.atem_rfq_input ADD CONSTRAINT atem_rfq_input_project_material_key 
    UNIQUE (project_id, material_description);

    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_rfq_input IS 'Stores standardized and deduplicated RFQ input data for materials and components used across projects. This table serves as the master reference for normalized material descriptions, specifications, categorization, and additional attributes. It ensures consistent classification, accurate aggregation, and efficient cross-referencing in related tables, particularly when generating detailed RFQs and managing bill of materials data.';
      
    /* Comments for columns */
    COMMENT ON COLUMN public.atem_rfq_input.id IS 'Unique identifier for each RFQ input entry.';
    COMMENT ON COLUMN public.atem_rfq_input.project_id IS 'Identifier for the project associated with the RFQ input.';      
    COMMENT ON COLUMN public.atem_rfq_input.material_description IS 'Description of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.normalized_description IS 'Normalized description of the material. Intended for future use in improving AI classification accuracy. and normalization';
    COMMENT ON COLUMN public.atem_rfq_input.rfq_scope IS 'RFQ scope for the material. This a blanket category';
    COMMENT ON COLUMN public.atem_rfq_input.general_category IS 'General category for the material. Values here are aggregated into General. The values in this column are mapping to the columns in public.atem_general';
    COMMENT ON COLUMN public.atem_rfq_input.unit_of_measure IS 'Unit of measure for the material.';
    COMMENT ON COLUMN public.atem_rfq_input.material IS 'Material type.';
    COMMENT ON COLUMN public.atem_rfq_input.abbreviated_material IS 'Abbreviated material type.';
    COMMENT ON COLUMN public.atem_rfq_input.technical_standard IS 'Technical standard. Renamed from SQLite column ansme_ansi (typo)';
    COMMENT ON COLUMN public.atem_rfq_input.astm IS 'ASTM code.';
    COMMENT ON COLUMN public.atem_rfq_input.grade IS 'Grade of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.rating IS 'Rating of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.schedule IS 'Schedule of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.coating IS 'Coating of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.forging IS 'Forging of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.ends IS 'Ends of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.item_tag IS 'Item tag of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.tie_point IS ' Tie point of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.pipe_category IS 'Pipe category of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.valve_type IS 'Valve type of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.fitting_category IS 'Fitting category of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.weld_category IS 'Weld category of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.bolt_category IS 'Bolt category of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.gasket_category IS 'Gasket category of the material.';
    COMMENT ON COLUMN public.atem_rfq_input.notes IS 'Notes for the material.';
    COMMENT ON COLUMN public.atem_rfq_input.deleted IS 'Flag to indicate if the material is deleted. This will remove it from the public.atem_rfq';
    COMMENT ON COLUMN public.atem_rfq_input.ignore_item IS 'Flag to indicate if the material should be ignored when aggregating to public.atem_general.';
    COMMENT ON COLUMN public.atem_rfq_input.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_rfq_input.created_by IS 'User who created the record.';
    COMMENT ON COLUMN public.atem_rfq_input.updated_at IS 'Timestamp when the record was last updated.';
    COMMENT ON COLUMN public.atem_rfq_input.updated_by IS 'User who last updated the record.';    
"""

SCHEMA_RFQ = """
    /* SCHEMA_RFQ */
    /* Stores RFQ (Request for Quote) data and its related calculations, including size, quantity, 
    and material details. Uses public.atem_rfq_input as a batch update source for material details.*/

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_rfq;

    /* Create table: public.atem_rfq */
    CREATE TABLE public.atem_rfq (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES public.atem_projects(id),
        profile_id INTEGER REFERENCES public.atem_client_profiles(id),
        rfq_input_id INTEGER REFERENCES public.atem_rfq_input(id),
        material_description TEXT NOT NULL,
        normalized_description TEXT(255),
        component_category VARCHAR(100), -- Component category
        mtrl_category VARCHAR(100), -- Material category
        size VARCHAR(100), -- Original extracted value as string
        size1 DECIMAL(12,3), -- Standardized decimal format for size1
        size2 DECIMAL(12,3), -- Standardized decimal format for size2
        quantity DECIMAL, -- Quantity of the item 

        /* Classification Category Fields */
        rfq_scope VARCHAR(100), -- RFQ scope category
        general_category VARCHAR(100), -- General categorization for aggregation
        unit_of_measure VARCHAR(50), -- Unit of measure (e.g., inches, feet)
        material VARCHAR(100), -- Material type
        abbreviated_material VARCHAR(50), -- Short form of material type
        technical_standard VARCHAR(50), -- Technical standard (previously ansme_ansi)
        astm VARCHAR(50), -- ASTM standard
        grade VARCHAR(50), -- Grade specification
        rating VARCHAR(50), -- Rating specification
        schedule VARCHAR(50), -- Schedule specification
        coating VARCHAR(50), -- Coating applied to material
        forging VARCHAR(50), -- Forging type
        ends VARCHAR(50), -- Ends specification
        item_tag VARCHAR(100), -- Item tag for reference
        tie_point VARCHAR(100), -- Tie point designation
        pipe_category VARCHAR(50), -- Pipe category
        valve_type VARCHAR(50), -- Valve type category
        fitting_category VARCHAR(50), -- Fitting category
        weld_category VARCHAR(50), -- Weld category
        bolt_category VARCHAR(50), -- Bolt category
        gasket_category VARCHAR(50), -- Gasket category
        calculated_eq_length DECIMAL, -- Calculated equivalent length of the material.
        calculated_area DECIMAL, -- Calculated area of the material.
        notes TEXT, -- Additional notes
        deleted BOOLEAN DEFAULT FALSE, -- Flag to indicate deletion status
        ignore_item BOOLEAN DEFAULT FALSE, -- Flag to ignore during aggregations
        validated_date TIMESTAMP, -- Date of validation
        validated_by VARCHAR(100), -- Validator identity
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
        created_by VARCHAR(100), -- User who created the record
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Record update timestamp
        updated_by VARCHAR(100) -- User who updated the record

        -- Flag for failed lookup
        mapping_not_found BOOLEAN DEFAULT FALSE
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_rfq_project_id ON public.atem_rfq(project_id);
    CREATE INDEX idx_rfq_sizes ON public.atem_rfq(size1, size2);
    CREATE INDEX idx_rfq_material_desc ON public.atem_rfq(material_description);
    CREATE INDEX idx_rfq_profile_id ON public.atem_rfq(profile_id);
    COMMENT ON TABLE public.atem_rfq IS 'Stores RFQ data and its related calculations, including size, quantity, and material details.';

    /* Comments for columns */
    COMMENT ON COLUMN public.atem_rfq.id IS 'Unique identifier for each RFQ input entry.';
    COMMENT ON COLUMN public.atem_rfq.project_id IS 'Identifier for the project associated with the RFQ input.';     
    COMMENT ON COLUMN public.atem_rfq.profile_id IS 'Identifier for the client profile used for calculations.';
    COMMENT ON COLUMN public.atem_rfq.rfq_input_id IS 'Identifier for the RFQ input ID.';
    COMMENT ON COLUMN public.atem_rfq.material_description IS 'Description of the material.';
    COMMENT ON COLUMN public.atem_rfq.normalized_description IS 'Normalized description of the material. Intended for future use in improving AI classification accuracy. and normalization';
    COMMENT ON COLUMN public.atem_rfq.size1 IS 'First size of the material.';
    COMMENT ON COLUMN public.atem_rfq.size2 IS 'Second size of the material.';
    COMMENT ON COLUMN public.atem_rfq.quantity IS 'Quantity of the material.';
    COMMENT ON COLUMN public.atem_rfq.rfq_scope IS 'RFQ scope for the material. This a blanket category';
    COMMENT ON COLUMN public.atem_rfq.general_category IS 'General category for the material. Values here are aggregated into General. The values in this column are mapping to the columns in public.atem_general';
    COMMENT ON COLUMN public.atem_rfq.unit_of_measure IS 'Unit of measure for the material.';
    COMMENT ON COLUMN public.atem_rfq.material IS 'Material type.';
    COMMENT ON COLUMN public.atem_rfq.abbreviated_material IS 'Abbreviated material type.';
    COMMENT ON COLUMN public.atem_rfq.technical_standard IS 'Technical standard. Renamed from SQLite column ansme_ansi (typo)';
    COMMENT ON COLUMN public.atem_rfq.astm IS 'ASTM code.';
    COMMENT ON COLUMN public.atem_rfq.grade IS 'Grade of the material.';
    COMMENT ON COLUMN public.atem_rfq.rating IS 'Rating of the material.';
    COMMENT ON COLUMN public.atem_rfq.schedule IS 'Schedule of the material.';
    COMMENT ON COLUMN public.atem_rfq.coating IS 'Coating of the material.';
    COMMENT ON COLUMN public.atem_rfq.forging IS 'Forging of the material.';
    COMMENT ON COLUMN public.atem_rfq.ends IS 'Ends of the material.';
    COMMENT ON COLUMN public.atem_rfq.item_tag IS 'Item tag of the material.';
    COMMENT ON COLUMN public.atem_rfq.tie_point IS ' Tie point of the material.';
    COMMENT ON COLUMN public.atem_rfq.pipe_category IS 'Pipe category of the material.';
    COMMENT ON COLUMN public.atem_rfq.valve_type IS 'Valve type of the material.';
    COMMENT ON COLUMN public.atem_rfq.fitting_category IS 'Fitting category of the material.';
    COMMENT ON COLUMN public.atem_rfq.weld_category IS 'Weld category of the material.';
    COMMENT ON COLUMN public.atem_rfq.bolt_category IS 'Bolt category of the material.';
    COMMENT ON COLUMN public.atem_rfq.gasket_category IS 'Gasket category of the material.';
    COMMENT ON COLUMN public.atem_rfq.notes IS 'Notes for the material.';
    COMMENT ON COLUMN public.atem_rfq.deleted IS 'Flag to indicate if the material is deleted. This will remove it from the public.atem_rfq';
    COMMENT ON COLUMN public.atem_rfq.ignore_item IS 'Flag to indicate if the material should be ignored when aggregating to public.atem_general.';
    COMMENT ON COLUMN public.atem_rfq.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_rfq.created_by IS 'User who created the record.';
    COMMENT ON COLUMN public.atem_rfq.updated_at IS 'Timestamp when the record was last updated.';
    COMMENT ON COLUMN public.atem_rfq.updated_by IS 'User who last updated the record.';
"""