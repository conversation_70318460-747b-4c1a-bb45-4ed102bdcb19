from PySide6.QtWidgets import QPushButton, QMenu, QLabel
from PySide6.QtGui import QMovie, QIcon
from PySide6.QtCore import Signal
from pubsub import pub
from src.app_paths import resource_path
from src.pyside_util import get_resource_qicon

class JobStatusWidget(QPushButton):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        self.menuActions = {}
        self.jobItems = {}
        self.setText("Job Queue [0/0]")
        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        self.movie.frameChanged.connect(self.updateAnimation)
        # self.lblIcon = QLabel()
        self.setIcon(get_resource_qicon("tool.svg"))
        self.setToolTip("Job Queue Status")

    def updateIcon(self):
        self.setIcon(get_resource_qicon("tool.svg"))

    def onJobQueueUpdated(self, data):
        self.sgnUpdateStatus.emit(data)
    
    def updateAnimation(self):
        self.setIcon(QIcon(self.movie.currentPixmap()))

    def onUpdateStatus(self, data):
        jobs = data.get("jobs")
        jobCount = len(jobs)
        done = 0
        inProgress = []
        for n, j in enumerate(jobs):
            if j["finished"] is True:
                done += 1
        s = " ".join(inProgress)
        msg = f" Job Queue: [{done}/{jobCount}]"
        data = {
            "type": "Jobs",
            "msg": msg
        }
        if done == jobCount:
            self.movie.stop()
            self.updateIcon()
        else:
            self.movie.start()
        self.setText(msg)
