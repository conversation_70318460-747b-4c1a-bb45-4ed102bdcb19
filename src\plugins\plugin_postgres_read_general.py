import pandas as pd
from datetime import datetime

from src.atom.pg_database import pg_connection
from src.data.tablemap import get_payload_name

QUERY = """

-- BUILD general:
SELECT
    -- Basic identifiers
    id,
    project_id,
    pdf_id,
    pdf_page,

    -- System info
    sys_filename,

    -- Elevations
    min_elevation,
    max_elevation,
    avg_elevation,

    -- Line info
    line_number,
    size,

    -- Document info
    document_description,
    drawing,
    iso_number,
    sheet,
    total_sheets,
    updated_at AS last_updated,

    -- Pressure and temperature data (add the correct column names)
    --pressure_class AS "Design Press. 1 (P)",
    -- design_temp_1,
    -- design_temp_2,
    -- op_press_1,
    -- test_psi,
    -- test_temp,
    -- test_type,

    -- Calculated columns
    length,
    calculated_eq_length,
    calculated_area,

    -- Aggregated fitting counts
    elbows_90,
    elbows_45,
    bevels,
    tees,
    reducers,
    caps,
    flanges,
    valves_flanged,
    valves_welded,
    cut_outs,
    supports,
    bends,
    union_couplings,
    expansion_joints,
    field_welds

FROM report_general_with_length(%s)
ORDER BY pdf_page, size;

"""

def plugin_postgres_read_general(project_id: int,
                                limit: int = 0,
                                as_dataframe: bool = True,
                                save_file: str = None,
                                signals: dict = None):
    """
    Reads general data from the PostgreSQL database for a specific project.

    Args:
        project_id (int): Project ID to filter by (required)
        limit (int): Maximum number of records to return (0 means no limit)
        as_dataframe (bool): Return results as pandas DataFrame if True, else as list of dicts
        save_file (str): Path to save the results to a file (optional)
        signals (dict): Dictionary of signals to send progress updates (optional)

    Returns:
        pd.DataFrame or list: General data for the specified project
    """
    print("Reading general data from PostgreSQL database.")

    # Validate inputs
    try:
        project_id = int(project_id)
        if project_id <= 0:
            print("Error: project_id must be a positive integer.")
            return None
    except (ValueError, TypeError):
        print("Error: project_id must be an integer.")
        return None

    try:
        limit = int(limit)
        if limit < 0:
            limit = 0
            print(f"Invalid limit value. Using no limit.")
    except (ValueError, TypeError):
        limit = 0
        print(f"Invalid limit value. Using no limit.")

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")

            # Create a local copy of the query
            query_with_limit = QUERY

            params = [project_id]

            # Add limit only if specified
            if limit > 0:
                query_with_limit += f" LIMIT %s;"
                params.append(limit)

            # Execute query
            with conn.cursor() as cursor:
                cursor.execute(query_with_limit, params)
                columns = [desc[0] for desc in cursor.description]
                results = cursor.fetchall()

                if not results:
                    print(f"No general data found for project_id: {project_id}")
                    if as_dataframe:
                        return pd.DataFrame(columns=columns)
                    else:
                        return []

                # Save results to file if specified
                if save_file:
                    # Save results to file
                    df = pd.DataFrame(results, columns=columns)
                    df.to_csv(save_file, index=False)
                    print(f"Results saved to {save_file}")

                # Format results
                if as_dataframe:
                    df = pd.DataFrame(results, columns=columns)
                    print(f"Found {len(df)} general record(s) for project_id: {project_id}")

                    # Print the DataFrame
                    print("\nGeneral Data Summary:")
                    print(f"Columns: {', '.join(df.columns)}")
                    print(f"Row count: {len(df)}")

                    # Print a preview of the data (first few rows)
                    if not df.empty:
                        print("\nData Preview:")
                        preview_df = df.head(5)
                        print(preview_df)

                        if len(df) > 5:
                            print(f"... and {len(df) - 5} more rows")

                    payload_name = get_payload_name("General")
                    signals["stageData"].emit({payload_name: df})

                    return df
                else:
                    # Convert to list of dictionaries
                    general_list = []
                    for row in results:
                        general_dict = dict(zip(columns, row))
                        general_list.append(general_dict)

                    print(f"Found {len(general_list)} general record(s) for project_id: {project_id}")

                    # Print summary of the data
                    print("\nGeneral Data Summary:")
                    print(f"Fields: {', '.join(columns)}")
                    print(f"Record count: {len(general_list)}")

                    # Print a preview of the first few records
                    if general_list:
                        print("\nData Preview (first 3 records):")
                        for i, record in enumerate(general_list[:3]):
                            print(f"Record {i+1}:")
                            # Print key fields (limit to a few important ones)
                            important_fields = ['id', 'project_id', 'line_number', 'iso_number', 'size', 'pipe_spec']
                            for field in important_fields:
                                if field in record:
                                    print(f"  {field}: {record[field]}")
                            print("-" * 50)

                        if len(general_list) > 3:
                            print(f"... and {len(general_list) - 3} more records")

                    return general_list

    except Exception as e:
        print(f"Failed to read general data: {e}")
        import traceback
        traceback.print_exc()
        return None