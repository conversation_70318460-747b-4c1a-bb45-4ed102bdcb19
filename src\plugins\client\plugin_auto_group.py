"""
adhoc grouping purely based on some text match from source preprocessing text
"""
import fitz
import os
import json
import pandas as pd
from src.app_paths import getSourceRawDataPath

def plugin_auto_group(input_file,
                    project_source: tuple,
                    bom_reference: str = "",
                    bom_column_names: str = "pos,quantity,size,material_description",
                    use_pdf_file: bool = False,
                    pdf_file: str = "",
                    pdf_page_key: str = "pdf_page",
                    use_page_count: bool = False,
                    custom_page_count: int = -1):
    """
    Detect missing pages in a file against a the page count from project source or pdf, or a custom page count.

    See output to grab missing pages list

    Args:
        input_file (str): Input file path
        project_source (tuple): Project source tuple.
        use_pdf_file (bool, optional): Use PDF file. Defaults to False.
        pdf_file (str, optional): PDF file path. Defaults to "". If use_pdf_file is checked, this is required. This overrides project_source
        pdf_page_key (str, optional): PDF page key. The name of the page key in input_file
        use_page_count (bool, optional): Use page count. Defaults to False.
        custom_page_count (int, optional): Custom page count. Defaults to -1. If use_page_count is checked, this is required. This overrides use_pdf_file
    """

    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, fileExists)

    doc = fitz.open(filename)

    rawFeather = getSourceRawDataPath(project_source[0], project_source[1])
    df = pd.read_feather(rawFeather)

    print(df.head())

    print(df[df["value"] == "MARK"])

    df2 = df[df["value"] == "DRAWING NO."]

    pages = df2["pdf_page"].unique().tolist()

    # df2 = df[df["pdf_page"].isin(pages)]

    df2["coordinates3"] = df2["coordinates2"].astype(str)
    df2['group_number'] = df2.groupby('coordinates3').ngroup()

    roiLayout = {
        "groups": {}
    }

    bom_column_names = [b.strip() for b in bom_column_names.split(",")]
    bom_column_ratios = [(1/len(bom_column_names)) * (n+1) for n, m in enumerate(bom_column_names)]
    bom_column_ratios[-1] = 1

    group_number = 0
    for group_number, group in df2.groupby("group_number"):
        group_pages = group["pdf_page"].unique().tolist()
        print(f"Group {group_number} has pages {len(group_pages)}")

        columnNames = group["coordinates3"].tolist()
        print(f"Group {group_number} has columns {len(columnNames)}")

        page = doc[group_pages[0] - 1]
        page_width = page.rect.width
        page_height = page.rect.height

        page_group = df[df["pdf_page"] == group_pages[0]]

        start = page_group[page_group["value"] == "MARK"].iloc[0]["coordinates2"]
        end = page_group[page_group["value"] == "DESCRIPTION"].iloc[0]["coordinates2"]

        relativeX0 = start[0] / page_width
        relativeY0 = start[1] / page_height
        relativeX1 = end[2] / page_width
        relativeY1 = end[3] / page_height

        roiLayout["groups"][group_number + 1] = {
            "pages": [{"page": page} for page in group_pages],
            "rois": [
                {
                    "name": "BOM",
                    "isTable": True,
                    "columnNames": bom_column_names,
                    "columnRatios": bom_column_ratios,
                    "relativeX0": relativeX0,
                    "relativeY0": relativeY0,
                    "relativeX1": relativeX1,
                    "relativeY1": min(relativeY1 + 0.1, 1),
                }
            ]
        }
    else:
        group_number += 1

    missing = [i for i in range(1, len(doc) + 1) if i not in pages]

    roiLayout["groups"][group_number] = {
        "pages": [{"page": page} for page in missing],
        "rois": []
    }

    with open("debug/layout.json", "w") as f:
        json.dump(roiLayout, f, indent=2)

    return f"Missing pages: {missing}. Saved to debug/layout.json"
