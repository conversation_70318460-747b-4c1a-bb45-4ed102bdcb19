﻿import re
import pandas as pd
import logging
from typing import Optional, Union, List, Dict, Any
from pydantic import BaseModel, validator, Field, ValidationError
from enum import Enum, auto

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("quantity_validator")

class SeverityLevel(Enum):
    LOW = 1      # Minor issue, probably correct
    MEDIUM = 2   # Needs review, could be correct
    HIGH = 3     # Likely incorrect
    CRITICAL = 4 # Almost certainly incorrect
    INVALID = 5  # Invalid format/value

class IssueType(Enum):
    DOUBLE_FEET = auto()         # 1' 1'
    DOUBLE_INCHES = auto()       # 1" 1"
    WRONG_ORDER = auto()         # 1" 1'
    MISSING_UNITS = auto()       # 1 1
    SMALL_FEET_VALUE = auto()    # Small values like 1' that should likely be inches
    SMALL_INCH_VALUE = auto()    # Small values like 1" that are valid but might need review
    LARGE_INCH_VALUE = auto()    # Values like 13" that should likely be feet
    DECIMAL_INCH = auto()        # 1.1" which is uncommon
    MIXED_FORMAT = auto()        # When the format is inconsistent
    INVALID_FORMAT = auto()      # When the format doesn't match expected patterns

class ValidationIssue(BaseModel):
    issue_type: IssueType
    severity: SeverityLevel
    message: str
    original_value: str

class QuantityValidationResult(BaseModel):
    original_value: str
    is_valid: bool
    is_pipe_item: bool
    issues: List[ValidationIssue] = []
    normalized_value: Optional[float] = None

class LinearMeasurement(BaseModel):
    """Model for validating linear measurements (feet and inches)"""
    original: str
    feet: Optional[float] = 0
    inches: Optional[float] = 0
    
    @validator('original')
    def validate_format(cls, v):
        if not isinstance(v, str):
            try:
                # Try to convert to string if it's a number
                return str(v)
            except:
                raise ValueError(f"Cannot convert {v} to string")
        return v
    
    def total_inches(self) -> float:
        """Convert to total inches"""
        return (self.feet * 12) + self.inches
    
    def total_feet(self) -> float:
        """Convert to total feet"""
        return self.feet + (self.inches / 12)

def is_pipe_item(material_description: str) -> bool:
    """
    Determine if an item is a pipe based on its description.
    
    Args:
        material_description: The description of the material
        
    Returns:
        bool: True if it's a pipe item, False otherwise
    """
    if not isinstance(material_description, str):
        logger.warning(f"material_description is not a string: {material_description}")
        return False
    
    material_description = material_description.lower()
    
    # Check if it contains 'pipe' or 'pipe,'
    has_pipe = 'pipe,' in material_description or 'pipe' in material_description
    
    # Check if it contains exclusion words
    exclusion_words = ['bend', 'support', 'supports', 'shoe', 'guide', 'nipple', 'base', 'pipet', 'stanchin', 'stanchion', 
                       'pipe rack', 'clamp', 'anvil', 'pipe sits', 'repad', 'dummy leg', 'd-leg']
    has_exclusions = any(word in material_description for word in exclusion_words)
    
    # It's a pipe if it has pipe-related words but no exclusion words
    return has_pipe and not has_exclusions

def parse_linear_measurement(value: str) -> Optional[LinearMeasurement]:
    """
    Parse a string containing a linear measurement into feet and inches.
    
    Args:
        value: The string to parse
        
    Returns:
        Optional[LinearMeasurement]: Parsed measurement or None if invalid
    """
    if not isinstance(value, str):
        try:
            # Try to convert numbers to strings
            value = str(value)
        except:
            logger.error(f"Cannot convert {value} to string")
            return None
    
    # Clean the input value
    value = value.strip()
    
    # Define patterns for different formats
    
    # Pattern for "x'y\"" format (e.g., 1'2")
    pattern1 = r"(\d+(?:\.\d+)?)'[ -]*(\d+(?:\.\d+)?)(?:[ -]*(\d+)/(\d+))?\"?"
    
    # Pattern for feet only (e.g., 1')
    pattern2 = r"(\d+(?:\.\d+)?)'$"
    
    # Pattern for inches only (e.g., 1")
    pattern3 = r"(\d+(?:\.\d+)?)(?:[ -]*(\d+)/(\d+))?\""
    
    # Pattern for mixed fraction feet (e.g., 1-1/2')
    pattern4 = r"(\d+)[ -]+(\d+)/(\d+)'"
    
    # Pattern for mixed fraction inches (e.g., 1-1/2")
    pattern5 = r"(\d+)[ -]+(\d+)/(\d+)\""
    
    # Try to match the patterns
    # First pattern: x'y" (feet and inches)
    match = re.match(pattern1, value)
    if match:
        feet = float(match.group(1))
        inches = float(match.group(2))
        
        # Check for fractions
        if match.group(3) and match.group(4):
            fraction = float(match.group(3)) / float(match.group(4))
            inches += fraction
            
        return LinearMeasurement(original=value, feet=feet, inches=inches)
    
    # Second pattern: x' (feet only)
    match = re.match(pattern2, value)
    if match:
        feet = float(match.group(1))
        return LinearMeasurement(original=value, feet=feet, inches=0)
    
    # Third pattern: x" (inches only)
    match = re.match(pattern3, value)
    if match:
        inches = float(match.group(1))
        
        # Check for fractions
        if match.group(2) and match.group(3):
            fraction = float(match.group(2)) / float(match.group(3))
            inches += fraction
            
        return LinearMeasurement(original=value, feet=0, inches=inches)
    
    # Fourth pattern: mixed fraction feet (e.g., 1-1/2')
    match = re.match(pattern4, value)
    if match:
        whole = float(match.group(1))
        numerator = float(match.group(2))
        denominator = float(match.group(3))
        feet = whole + (numerator / denominator)
        return LinearMeasurement(original=value, feet=feet, inches=0)
    
    # Fifth pattern: mixed fraction inches (e.g., 1-1/2")
    match = re.match(pattern5, value)
    if match:
        whole = float(match.group(1))
        numerator = float(match.group(2))
        denominator = float(match.group(3))
        inches = whole + (numerator / denominator)
        return LinearMeasurement(original=value, feet=0, inches=inches)
    
    # Handle decimal numbers (assume feet)
    if re.match(r"^\d+(?:\.\d+)?$", value):
        try:
            feet = float(value)
            
            # For pipe items, we should create the measurement but not validate it yet
            # The validation for missing units will happen in validate_quantity
            return LinearMeasurement(original=value, feet=feet, inches=0)
        except ValueError:
            logger.error(f"Failed to convert {value} to float")
            return None
    
    # If no pattern matches
    logger.warning(f"No pattern matches for value: {value}")
    return None

def validate_quantity(quantity: str, is_pipe: bool) -> QuantityValidationResult:
    """
    Validate a quantity value based on whether it's for a pipe item or not.
    
    Args:
        quantity: The quantity to validate
        is_pipe: Whether the item is a pipe
        
    Returns:
        QuantityValidationResult: Validation result with issues if any
    """
    # Set of values to ignore (treat as valid)
    ignore_values = {'deleted', 'not used', 'n/a', 'na', 'none'}
    
    # Check for None/NaN values
    if quantity is None or (isinstance(quantity, float) and pd.isna(quantity)):
        return QuantityValidationResult(
            original_value="",
            is_valid=False,
            is_pipe_item=is_pipe,
            issues=[ValidationIssue(
                issue_type=IssueType.INVALID_FORMAT,
                severity=SeverityLevel.MEDIUM,
                message="Blank or null quantity value",
                original_value=""
            )]
        )
    
    # Handle empty strings
    if isinstance(quantity, str) and not quantity.strip():
        return QuantityValidationResult(
            original_value="",
            is_valid=False,
            is_pipe_item=is_pipe,
            issues=[ValidationIssue(
                issue_type=IssueType.INVALID_FORMAT,
                severity=SeverityLevel.MEDIUM,
                message="Empty quantity value",
                original_value=""
            )]
        )
    
    # Check for ignore values
    if isinstance(quantity, str) and quantity.lower().strip() in ignore_values:
        return QuantityValidationResult(
            original_value=str(quantity),
            is_valid=True,
            is_pipe_item=is_pipe,
            normalized_value=0  # Use 0 as normalized value for ignored values
        )
    
    result = QuantityValidationResult(
        original_value=str(quantity),
        is_valid=True,
        is_pipe_item=is_pipe
    )
    
    # Handle non-pipe items (should be integers)
    if not is_pipe:
        try:
            # Check if it's an integer
            if isinstance(quantity, int):
                result.normalized_value = float(quantity)
                return result
            
            # Check if it's a float that's an integer value
            if isinstance(quantity, float) and quantity.is_integer():
                result.normalized_value = quantity
                return result
            
            if isinstance(quantity, str):
                # Try to convert string to integer
                value = float(quantity)
                if value.is_integer():
                    result.normalized_value = value
                    return result
            
            # If we get here, it's not an integer
            result.is_valid = False
            result.issues.append(ValidationIssue(
                issue_type=IssueType.INVALID_FORMAT,
                severity=SeverityLevel.HIGH,
                message="Non-pipe item quantity should be an integer",
                original_value=str(quantity)
            ))
            
        except (ValueError, TypeError):
            result.is_valid = False
            result.issues.append(ValidationIssue(
                issue_type=IssueType.INVALID_FORMAT,
                severity=SeverityLevel.HIGH,
                message="Unable to parse quantity as a number",
                original_value=str(quantity)
            ))
            
        return result
    
    # Handle pipe items (can be complex formats)
    if isinstance(quantity, (int, float)):
        # Plain numbers for pipe items should have units (' or ")
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.CRITICAL,  # Increased from HIGH to CRITICAL
            message=f"Missing units (' or \") for pipe measurement: {quantity}",
            original_value=str(quantity)
        ))
        
        # Still provide a normalized value (assume feet)
        result.normalized_value = float(quantity)
        
        return result
    
    # Convert to string if not already
    if not isinstance(quantity, str):
        quantity = str(quantity)
    
    # Check for plain numeric strings (like "3") for pipe items
    if is_pipe and isinstance(quantity, str) and re.match(r"^\d+(?:\.\d+)?$", quantity.strip()):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.CRITICAL,  # Increased from HIGH to CRITICAL
            message=f"Missing units (' or \") for pipe measurement: {quantity}",
            original_value=quantity
        ))
        
        # Still provide a normalized value (assume feet)
        try:
            result.normalized_value = float(quantity)
        except (ValueError, TypeError):
            pass
        
        return result
    
    # Check for common OCR errors
    if quantity.count("'") > 1:
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.DOUBLE_FEET,
            severity=SeverityLevel.HIGH,
            message=f"Multiple feet symbols: {quantity}",
            original_value=quantity
        ))
    
    if quantity.count('"') > 1:
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.DOUBLE_INCHES,
            severity=SeverityLevel.HIGH,
            message=f"Multiple inches symbols: {quantity}",
            original_value=quantity
        ))
    
    # Check for wrong order (inches before feet)
    if '"' in quantity and "'" in quantity and quantity.find('"') < quantity.find("'"):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.WRONG_ORDER,
            severity=SeverityLevel.HIGH,
            message=f"Inches symbol before feet symbol: {quantity}",
            original_value=quantity
        ))
    
    # Check for partial formats (missing symbols)
    # Pattern for "x' y" (missing inches symbol)
    if re.search(r"(\d+(?:\.\d+)?)'[ -]*(\d+(?:\.\d+)?)$", quantity):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.HIGH,
            message=f"Missing inches symbol: {quantity}",
            original_value=quantity
        ))
    
    # Pattern for "x y\"" (missing feet symbol)
    # Only match if there's a space or dash between numbers
    if re.search(r"^(\d+(?:\.\d+)?)[ -]+(\d+(?:\.\d+)?)\"", quantity):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.CRITICAL,  # Increased to CRITICAL
            message=f"Missing feet symbol: {quantity}",
            original_value=quantity
        ))
    
    # Pattern for "x'-y" or "x-y" (missing inches symbol)
    if re.search(r"(\d+(?:\.\d+)?)'?[ -]+(\d+(?:\.\d+)?)$", quantity) and not quantity.endswith('"'):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.HIGH,
            message=f"Missing inches symbol: {quantity}",
            original_value=quantity
        ))
            
    # Pattern for "x-y\"" (missing feet symbol)
    if re.search(r"^(\d+(?:\.\d+)?)[ -]+(\d+(?:\.\d+)?)\"", quantity) and "'" not in quantity:
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.HIGH,
            message=f"Missing feet symbol: {quantity}",
            original_value=quantity
        ))
        
    # Pattern for "x\" y" (missing inches symbol)
    if re.search(r"(\d+(?:\.\d+)?)\"[ -]*(\d+(?:\.\d+)?)$", quantity):
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.MISSING_UNITS,
            severity=SeverityLevel.HIGH,
            message=f"Missing inches symbol after number: {quantity}",
            original_value=quantity
        ))
    
    # Attempt to parse the measurement
    measurement = parse_linear_measurement(quantity)
    
    if measurement is None:
        result.is_valid = False
        result.issues.append(ValidationIssue(
            issue_type=IssueType.INVALID_FORMAT,
            severity=SeverityLevel.CRITICAL,
            message=f"Cannot parse measurement: {quantity}",
            original_value=quantity
        ))
        return result
    
    # Flag small feet values (might be inches)
    if measurement.feet > 0 and measurement.feet < 12 and measurement.inches == 0:
        result.issues.append(ValidationIssue(
            issue_type=IssueType.SMALL_FEET_VALUE,
            severity=SeverityLevel.HIGH,
            message=f"Small feet value (possibly should be inches): {measurement.feet}'",
            original_value=quantity
        ))
    
    # Flag small inch values (need review)
    if measurement.feet == 0 and measurement.inches > 0 and measurement.inches < 12:
        result.issues.append(ValidationIssue(
            issue_type=IssueType.SMALL_INCH_VALUE,
            severity=SeverityLevel.MEDIUM,  # Changed back to MEDIUM since it has valid units
            message=f"Small inches value (< 12): {measurement.inches}\"",
            original_value=quantity
        ))
    
    # Flag decimal inches (uncommon)
    if measurement.inches > 0 and measurement.inches != int(measurement.inches):
        decimal_part = measurement.inches - int(measurement.inches)
        if decimal_part > 0:
            result.issues.append(ValidationIssue(
                issue_type=IssueType.DECIMAL_INCH,
                severity=SeverityLevel.MEDIUM,
                message=f"Decimal inches value: {measurement.inches}\"",
                original_value=quantity
            ))
    
    # Flag large inch values (might be feet)
    if measurement.inches >= 12 and measurement.feet == 0:
        result.issues.append(ValidationIssue(
            issue_type=IssueType.LARGE_INCH_VALUE,
            severity=SeverityLevel.MEDIUM,
            message=f"Large inches value (≥ 12): {measurement.inches}\"",
            original_value=quantity
        ))
    
    # Store normalized value (convert to total feet)
    result.normalized_value = measurement.total_feet()
    
    # Update validity based on issues
    if result.issues:
        high_severity_issues = [i for i in result.issues if i.severity in 
                               [SeverityLevel.HIGH, SeverityLevel.CRITICAL, SeverityLevel.INVALID]]
        if high_severity_issues:
            result.is_valid = False
    
    return result

def validate_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """
    Validate quantity values in a dataframe based on material description.
    
    Args:
        df: DataFrame with 'material_description' and 'quantity' columns
        
    Returns:
        pd.DataFrame: Original dataframe with validation columns added
    """
    # Ensure required columns exist
    required_cols = ['material_description', 'quantity']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"DataFrame is missing required column: {col}")
    
    # Create new columns for validation results
    result_df = df.copy()
    result_df['is_pipe_item'] = df['material_description'].apply(is_pipe_item)
    
    # Apply validation to each row
    validation_results = []
    
    for idx, row in df.iterrows():
        try:
            is_pipe = is_pipe_item(row['material_description'])
            validation = validate_quantity(row['quantity'], is_pipe)
            validation_results.append(validation)
        except Exception as e:
            logger.error(f"Error validating row {idx}: {e}")
            # Create an error result
            validation = QuantityValidationResult(
                original_value=str(row.get('quantity', 'unknown')),
                is_valid=False,
                is_pipe_item=False,
                issues=[ValidationIssue(
                    issue_type=IssueType.INVALID_FORMAT,
                    severity=SeverityLevel.CRITICAL,
                    message=f"Error during validation: {str(e)}",
                    original_value=str(row.get('quantity', 'unknown'))
                )]
            )
            validation_results.append(validation)
    
    # Add validation results to dataframe
    result_df['is_valid'] = [r.is_valid for r in validation_results]
    result_df['normalized_value'] = [r.normalized_value for r in validation_results]
    result_df['validation_issues'] = [r.issues for r in validation_results]
    result_df['severity'] = [max([i.severity.value for i in r.issues], default=0) if r.issues else 0 
                            for r in validation_results]
    
    # Add issue descriptions
    result_df['issue_description'] = [
        '; '.join([i.message for i in r.issues]) if r.issues else ""
        for r in validation_results
    ]
    
    return result_df

# Example usage
if __name__ == "__main__":
    # Create a sample dataframe for testing
    data = {
        'material_description': [
            'Steel pipe, schedule 40',
            'Pipe support bracket',
            'PVC pipe, 2 inch',
            'Pipe bend, 90 degree',
            'Copper pipe',
            'Steel plate'
        ],
        'quantity': [
            '10\'',
            5,
            '1\' 2"',
            '3"',
            '1" 1\'',  # wrong order
            '2.5'
        ]
    }
    
    test_df = pd.DataFrame(data)
    print("Original DataFrame:")
    print(test_df)
    
    # Validate the dataframe
    validated_df = validate_dataframe(test_df)
    print("\nValidated DataFrame:")
    print(validated_df[['material_description', 'quantity', 'is_pipe_item', 
                        'is_valid', 'normalized_value', 'severity', 'issue_description']])
    
    # Show rows that need review (severity > 0)
    review_df = validated_df[validated_df['severity'] > 0]
    print("\nRows that need review:")
    print(review_df[['material_description', 'quantity', 'severity', 'issue_description']])