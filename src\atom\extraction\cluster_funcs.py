import polars as pl

def infer_possible_columns(df: pl.DataFrame, tol_x=10):
    """
    Group boxes into candidate columns based on x_center proximity.
    Return the groups and the most common item count.

    df: Polars DataFrame with x0,x1
    tol_x: horizontal tolerance for column grouping
    """
    # Compute x_center
    df = df.with_columns(((pl.col("x0")+pl.col("x1"))/2).alias("x_center"))
    df = df.with_row_index("row")

    # Sort by x_center
    df_sorted = df.sort("x_center")
    x_centers = df_sorted["x_center"].to_numpy()

    # 1D clustering on x_center
    clusters = []
    current_cluster = [0]  # store row indices
    for i in range(1, len(x_centers)):
        if x_centers[i] - x_centers[i-1] <= tol_x:
            current_cluster.append(i)
        else:
            clusters.append(current_cluster)
            current_cluster = [i]
    clusters.append(current_cluster)

    # Map clusters to actual DataFrame rows
    column_groups = []
    for cluster in clusters:
        # cluster contains indices into df_sorted, not original row indices
        # We need to get the actual rows from df_sorted using these indices
        # column_groups.append(cluster)  # Keep the df_sorted indices
        indices = sorted(df_sorted[cluster]["row"].to_list())
        column_groups.append(indices)

    return column_groups
    # Count items per column
    # item_counts = [len(g) for g in column_groups]
    # most_common_count = Counter(item_counts).most_common(1)[0][0] if item_counts else 0

    # structure = []
    # column_no = 0
    # for c in column_groups:
    #     indices = c
    #     column_data = df_sorted[indices].sort("y0")
    #     values = column_data["value"].to_list()
    #     print(f"Column no. {column_no}", values, len(c))
    #     structure.append({"column": column_no, "values": values})
    #     column_no += 1

    return column_groups