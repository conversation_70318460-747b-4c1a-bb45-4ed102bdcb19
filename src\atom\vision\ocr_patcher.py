"""
Modified version for use with _worker.py

Patch Raw DataFrame with OCR Data

1. Two-Phase Processing:
    Phase 1: Collect all CV2 data first
    Phase 2: Compare entire datasets at once. I like to use pandas vectorized operations.

2. OCR the identified pages

3. Merge results as before

"""

import json
import fitz
import io
import os
import time

import numpy as np
import pandas as pd
from PIL import Image
import pyarrow as pa
import pyarrow.parquet as pq

import matplotlib.pyplot as plt
import matplotlib.patches as patches

from src.atom.vision.detect_text_regions import detect_text_regions_cv2, flag_text_regions
from src.atom.vision import convert_to_pdf_aws
from src.atom.fast_storage import load_df_fast, save_df_fast
from src.utils.logger import logger
from src.atom.vision.ocr_patch_multi import ocr_specific_pages

_CHUNK_SIZE = 100
MISSING_CHUNK_SIZE = 50

SAVE_CHUNKS = _CHUNK_SIZE


def load_json(file) -> dict:
    with open(file) as json_file:
        return json.load(json_file)


# Multiprocessing - Pickle friendly functions
def detect_page_regions(page: fitz.Page,
                        page_number: int,
                        dpi=300,
                        min_area: int = 10,
                        blur_ksize: tuple = (3, 3),
                        min_thresh: int = 250,
                        max_thresh: int = 255) -> pd.DataFrame:
    """
    Detect text regions for a single page. This converts back to the original coords after scaling
    with DPI value
    """
    cv_image = page_to_opencv(page, dpi)
    text_regions = detect_text_regions_cv2.detect_text_regions(cv_image,
                                                               min_area=min_area,
                                                               blur_ksize=blur_ksize,
                                                               min_thresh=min_thresh,
                                                               max_thresh=max_thresh)

    # Convert to DataFrame and add necessary columns
    df = pd.DataFrame(text_regions, columns=["x0", "y0", "width", "height"])
    df["x1"] = df["x0"] + df["width"]
    df["y1"] = df["y0"] + df["height"]
    df['pdf_page'] = page_number

    # Map this back to original scale
    scale_factor = 72 / dpi
    coord_columns = ['x0', 'y0', 'x1', 'y1', 'width', 'height']
    df_converted = df.copy()
    df_converted[coord_columns] = df_converted[coord_columns] * scale_factor

    return df_converted

def page_to_opencv(page: fitz.Page, dpi: int = None) -> np.ndarray:
    """Convert PyMuPDF page to OpenCV image"""
    zoom = dpi / 72 if dpi else 1
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)

    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    return open_cv_image[:, :, ::-1].copy()

def extract_raw_regions(df: pd.DataFrame):
    """Parses coordinates and extracts bounding box values"""
    raw_regions = []

    for record in df.to_dict("records"):
        try:
            coordinates = None
            # Workaround - parse string to tuple
            if isinstance(record["coordinates2"], str):
                record["coordinates2"] = record["coordinates2"].replace("(", "")
                record["coordinates2"] = record["coordinates2"].replace(")", "")
                coordinates = tuple(map(float, record["coordinates2"].split(', ')))
            elif isinstance(record["coordinates2"], tuple):
                coordinates = record["coordinates2"]
            record["x0"] = coordinates[0]
            record["y0"] = coordinates[1]
            record["x1"] = coordinates[2]
            record["y1"] = coordinates[3]
            record["width"] = record["x1"] - record["x0"]
            record["height"] = record["y1"] - record["y0"]
            raw_regions.append({
                "pdf_page": record["pdf_page"],
                "x0": record["x0"],
                "y0": record["y0"],
                "x1": record["x1"],
                "y1": record["y1"],
            })
        except Exception as e:
            continue
            # raw_regions.append(record["coordinates2"])

    return raw_regions

def process_textract_response(data) -> pd.DataFrame:
    res = []
    for b in data["Blocks"]:
        if not b.get("Text"):
            continue
        bbox = b["Geometry"]["BoundingBox"]
        confidence = b["Confidence"]
        text = b["Text"]
        page = b.get("Page")
        res.append({
            "pdf_page": page,
            "text": text,
            "x0": bbox["Left"],
            "y0": bbox["Top"],
            "x1": bbox["Left"] + bbox["Width"],
            "y1": bbox["Top"] + bbox["Height"],
            "confidence": confidence
        })
    return pd.DataFrame(res)

def find_missing_regions_vectorized(page: int, page_cv2, page_regions, page_rois=None) -> pd.DataFrame:
    """
    Find missing regions using vectorized operations

    Args:
        - page: unused, for print output
        - page_cv2: CV2 regions of page
        - page_regions: raw data regions
        - page_rois: Optional, exclude any missing region not in an ROI

    Returns:
        - Pandas DataFrame of CV regions which are not accounted for by in
          the page's PyMuPDF data (page_regions) and, optionally, within specified
          `page_rois` regions
    """

    # Extracts coordinates 2 from all raw df records
    print("Finding final missing regions for page", page)

    # Create a helper function for vectorized overlap checking
    def check_overlap(row, page_regions: pd.DataFrame):
        # Convert row coordinates to array for broadcasting
        r1 = np.array([row['x0'], row['y0'], row['x1'], row['y1']])

        if len(page_regions) == 0:
            return False

        # Convert all region coordinates to arrays
        r2 = page_regions[['x0', 'y0', 'x1', 'y1']].values # raw regions

        # Vectorized overlap check
        overlaps = (r1[0] < r2[:, 2]) & (r1[2] > r2[:, 0]) & \
                    (r1[1] < r2[:, 3]) & (r1[3] > r2[:, 1])

        # Need missing to NOT overlap with existing raw region but DOES overlap with
        # an ROI layout selection
        return overlaps.any()

    def check_overlap2(row, page_rois: pd.DataFrame):
        # Convert row coordinates to array for broadcasting
        r1 = np.array([row['x0'], row['y0'], row['x1'], row['y1']])

        # Convert all region coordinates to arrays
        rois = page_rois[['x0', 'y0', 'x1', 'y1']].values # roi regions

        overlaps = (r1[0] < rois[:, 2]) & (r1[2] > rois[:, 0]) & \
                    (r1[1] < rois[:, 3]) & (r1[3] > rois[:, 1])

        # Need missing to NOT overlap with existing raw region but DOES overlap with
        # an ROI layout selection
        return overlaps.any()

    # The absence of page regions may indicate that page is fully OCR (or blank page)
    if len(page_regions) == 0:
        missing = page_cv2.copy()
    else:
        # Vectorized overlap check for this page
        mask = page_cv2.apply(lambda row: ~check_overlap(row, page_regions), axis=1)
        # Store missing regions
        missing = page_cv2[mask].copy()

    if page_rois is not None:
        # Filter out missing regions which are not in the ROI layout
        mask2 = missing.apply(lambda row: check_overlap2(row, page_rois), axis=1)
        try:
            missing2 = missing[mask2].copy()
        except Exception:
            missing2 = missing
    else:
        missing2 = missing

    # visualize_pdf_regions(pdf_path, page-1, page_cv2, r"debug\cv2.pdf")
    # visualize_pdf_regions(pdf_path, page-1, page_regions, r"debug\raw.pdf")
    # visualize_pdf_regions(pdf_path, page-1, missing2, r"debug\missing.pdf")

    return missing2

def filter_missing_regions(missing_regions, page_rois=None) -> pd.DataFrame:
    """
    Find missing regions using vectorized operations

    Args:
        - missing_regions: Missing regions
        - page_rois: Include only regions inside ROIS

    """
    # Extracts coordinates 2 from all raw df records
    # print("Finding final missing regions for page")

    def check_overlap(row, page_rois: pd.DataFrame):
        # Convert row coordinates to array for broadcasting
        r1 = np.array([row['x0'], row['y0'], row['x1'], row['y1']])

        # Convert all region coordinates to arrays
        rois = page_rois[['x0', 'y0', 'x1', 'y1']].values # roi regions

        overlaps = (r1[0] < rois[:, 2]) & (r1[2] > rois[:, 0]) & \
                    (r1[1] < rois[:, 3]) & (r1[3] > rois[:, 1])

        # Need missing to NOT overlap with existing raw region but DOES overlap with
        # an ROI layout selection
        return overlaps.any()
    # Store missing regions

    # Filter out missing regions which are not in the ROI layout
    mask = missing_regions.apply(lambda row: check_overlap(row, page_rois), axis=1)
    missing_regions2 = missing_regions[mask].copy()

    # visualize_pdf_regions(pdf_path, page-1, page_cv2, r"debug\cv2.pdf")
    # visualize_pdf_regions(pdf_path, page-1, page_regions, r"debug\raw.pdf")
    # visualize_pdf_regions(pdf_path, page-1, missing2, r"debug\missing.pdf")

    return missing_regions2


def visualize_pdf_regions(pdf_path, page_number, page_region, output_file):
    # Render page to an image
    doc = fitz.open(pdf_path)
    page = doc.load_page(page_number)
    page.remove_rotation()

    for _, row in page_region.iterrows():
        x0, y0, x1, y1 = row['x0'], row['y0'], row['x1'], row['y1']
        rect = fitz.Rect(x0, y0, x1, y1)
        page.insert_text((x0, y0), f"{_}", fontsize=5, color=(0, 0, 1))
        page.add_rect_annot(rect)

        # y0, x0, y1, x1 = row['x0'], row['y0'], row['x1'], row['y1']
        # rect = fitz.Rect(page.rect.height-x1, y0, page.rect.height-x0, y1)
        # page.insert_text((page.rect.height-x0, y0-5), f"{_}", fontsize=3, rotate=270, color=(0, 0, 1))
        # page.add_rect_annot(rect)

    print("saved")
    doc.save(output_file)

def is_overlapping(rect_a, rect_b) -> bool:
    """
    Return True if two regions overlap, where lx and rx are top left
    and bottom right coordinates of a rect respectively
    """
    a_left, a_top = rect_a[0], rect_a[1]
    a_right, a_bottom = rect_a[2], rect_a[3]
    b_left, b_top = rect_b[0], rect_b[1]
    b_right, b_bottom = rect_b[2], rect_b[3]
    return (a_left < b_right
        and a_right > b_left
        and a_top < b_bottom
        and a_bottom > b_top)


def reduce_regions(regions: pd.DataFrame, inverse: bool = True) -> pd.DataFrame:
    """Remove smaller text regions if they are included in a larger region

    For example, AWS may return 3 regions as part of one line:

    1. "PIPE, SCH"
    2. "PIPE, "
    3. "SCH"

    We only want the region 1 as it includes the other regions

    Args:
        inverse: If True, use the full text component, instead of breaking up into smaller words
    """
    result = []
    page_groups = regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"], record["text"], record["confidence"]])

        # Reduce by including only if the regions has no overlaps
        # or is the largest of an overlapping group
        flagged = []
        ignore = set()
        for n1, r1 in enumerate(page_regions):
            include = True
            r1_text = r1[4]
            r1_conf = r1[5]
            if n1 in ignore:
                continue
            for n2, r2 in enumerate(page_regions):
                r2_text = r2[4]
                if n1 == n2:
                    continue
                r1_area = (r1[2] - r1[0]) * (r1[3] - r1[1])
                r2_area = (r2[2] - r2[0]) * (r2[3] - r2[1])

                if is_overlapping(r1, r2):
                    # if r1_area < r2_area and r1_text in r2_text and r1_text != r2_text:  # Make sure th smaller text is in larger text
                    if not inverse:
                        if r1_area > r2_area and r2_text in r1_text and r1_text != r2_text: # Make sure the smaller text is in larger text
                            include = False
                            break
                        elif r1_area == r2_area and r1_text == r2_text:
                            # This ignores checking duplicate regions
                            ignore.add(n2)
                    else:
                        if r1_area < r2_area and r1_text in r2_text and r1_text != r2_text: # Make sure the smaller text is in larger text
                            # There is a larger region which
                            # should be including this text
                            include = False
                            break
                        elif r1_area == r2_area and r1_text == r2_text:
                            # This ignores checking duplicate regions
                            ignore.add(n2)

            # r1 overlaps and is the largest
            if include:
                record = [pdf_page] + r1
                result.append(record)
                if r1_text == "TBE":
                    pass

        result.extend(flagged)

        print(f"Page {pdf_page} - Number of regions after reduction:", len(flagged))

    return pd.DataFrame(result, columns=["pdf_page", "x0", "y0", "x1", "y1", "text", "confidence"])



def extract_missing_text(roi_coords, regions) -> pd.DataFrame:
    """Return text which overlap with regions"""
    all_flagged = []
    page_groups = regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_roi_regions = roi_coords[roi_coords["pdf_page"] == pdf_page].to_dict("records")
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])

        # If text region does not overlap with any region of
        # the Raw DataFrame, then flag it for OCR
        flagged = []
        for n, r1 in enumerate(page_regions):
            for n2, r2 in enumerate(page_roi_regions):
                if r2 in flagged:
                    continue
                r2_coords = [r2["x0"], r2["y0"], r2["x1"], r2["y1"]]
                if is_overlapping(r1, r2_coords):
                    # Overlapping with ROI - add flag for OCR
                    record = [pdf_page] + r1 + [r2["text"]]
                    # flagged.append(record)
                    flagged.append(r2)
                    logger.debug(f"Page {pdf_page}, Text region index={n} flagged for OCR")
                    break

        all_flagged.extend(flagged)

        print(f"Page {pdf_page} - Number of missing regions which overlap with an ROI:", len(flagged))

    return pd.DataFrame(all_flagged, columns=["pdf_page", "x0", "y0", "x1", "y1", "text"])


def convert_coordinates_vectorized(df: pd.DataFrame, dpi=300) -> pd.DataFrame:
    """Convert coordinates using vectorized operations"""
    # Assuming DPI conversion is needed
    scale_factor = 72 / dpi

    coord_columns = ['x0', 'y0', 'x1', 'y1', 'width', 'height']
    df_converted = df.copy()
    df_converted[coord_columns] = df_converted[coord_columns] * scale_factor

    return df_converted


def get_patched_ocr_data(pdf_path,
                         pages_to_ocr,
                         combined_missing_rois,
                         raw_data: pd.DataFrame,
                         use_checkpoint: bool = False) -> list:
    """

    Args:
        pages_to_ocr: List of pages to OCR
    """
    use_checkpoint = use_checkpoint and not __file__.endswith(".pyc")

    try:
        # Replace the single page processing loop with our new function
        combined_textract = ocr_specific_pages(pdf_path, pages_to_ocr, use_checkpoint=use_checkpoint)

        if combined_textract.empty:
            logger.error("No OCR results were found in any pages")
            return None

        logger.info(f"OCR completed. Results shape: {combined_textract.shape}")

        # visualize_pdf_regions(pdf_path, 0, combined_textract, r"debug\ocr.pdf")

        # Discard smaller duplicate regions from textract
        reduced_textract = reduce_regions(combined_textract, inverse=False)

        # visualize_pdf_regions(pdf_path, 0, reduced_textract, r"debug\reduced.pdf")

        # Save combined results (same as before)
        if use_checkpoint:
            pass
            # combined_textract_file = "debug/ocr_patch/combined_textract"
            # save_df_fast(combined_textract, combined_textract_file, format="feather")
            # combined_textract.to_excel("debug/ocr_patch/combined_textract_help.xlsx")
            # raw_data.to_excel("debug/ocr_patch/raw_data_help.xlsx")
            # logger.info(f"Saved combined OCR results to {combined_textract_file}")

        # Map page number to pdf id
        page_to_id = {}
        for pdf_id, group in raw_data.groupby("pdf_id"):
            pdf_page = group.iloc[0]["pdf_page"]
            page_to_id[pdf_page] = pdf_id

        # Extract missing ROIs with error handling
        try:
            logger.info("Extracting missing text...")
            final_missing_rois = extract_missing_text(reduced_textract, combined_missing_rois)

            if final_missing_rois.empty:
                logger.error("No missing ROIs were found after OCR")
                return

            # final_missing_rois.to_excel("debug/final_missing_rois.xlsx")
            logger.info(f"Saved final missing ROIs. Shape: {final_missing_rois.shape}")

        except Exception as e:
            logger.error(f"Error extracting missing text: {e}", exc_info=True)
            return

        print("\n\nRAW DATA: \n\n", raw_data)

        # Process records with error handling for each page
        # visualize_pdf_regions(pdf_path, 0, final_missing_rois, r"debug\ocr_missing.pdf")

        raw_data_records = raw_data.to_dict("records")
        for page_number, page_group in final_missing_rois.groupby("pdf_page"):
            try:
                for record in page_group.itertuples():
                    try:
                        coordinates = (record.x0, record.y0, record.x1, record.y1)
                        raw_data_records.append({
                            "pdf_page": page_number,
                            "type": "OCR",
                            "value": str(record.text) if hasattr(record, 'text') else '',
                            "coordinates": coordinates,
                            "coordinates2": coordinates,
                            "font": "ArialMT",
                            "font_size": "12",
                            # "sys_build": sys_fields['sys_build'],
                            # "sys_path": sys_fields['sys_path'],
                            # "sys_filename": sys_fields['sys_filename'],
                            # "pdf_id": sys_fields['pdf_id']
                            "pdf_id": page_to_id.get(record.pdf_page, "unknown")
                        })
                    except Exception as e:
                        print(f"Warning: Error processing record for page {page_number}: {str(e)}")
                        continue
            except Exception as e:
                logger.error(f"Warning: Error processing page {page_number}: {e}", exc_info=True)(
                    f"Warning: Error processing page {page_number}: {str(e)}")
                continue

        return raw_data_records
        try:
            # Create final dataframe

            out_df = pd.DataFrame(raw_data_records)

            return out_df

            # Convert all object columns to string with error handling
            for col in out_df.select_dtypes(['object']):
                try:
                    out_df[col] = out_df[col].astype(str)

                except Exception as e:
                    logger.error(f"Warning: Error converting column {col} to string: {str(e)}")
                    out_df[col] = out_df[col].fillna('').astype(str)

            return out_df

            # Save as Feather

            save_df_fast(out_df, output_path, format='feather')

            # Optional:

            if EXPORT_FINAL_PATCH_DATA:
                out_df.to_excel(patch_file)

                print("Finished patching RAW Data:", patch_file)
            else:
                print("Finished patching RAW Data. EXPORT_FINAL_PATCH_DATA=FALSE")


        except Exception as e:
            print(f"Error saving final results: {str(e)}")
            # Try to save what we have as backup
            try:
                backup_path = r"debug\raw_data_full_patched_ocr_backup"
                save_df_fast(pd.DataFrame(raw_data_records), backup_path, format='feather')
                print(f"Saved backup to {backup_path}")
            except:
                print("Could not save backup")

    except Exception as e:
        logger.error(f"Critical error in OCR processing: {e}", exc_info=True)
