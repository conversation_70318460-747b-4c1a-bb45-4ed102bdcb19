
import asyncio
import os
from dotenv import load_dotenv  # Import the load_dotenv function from the python-dotenv package
from api_request_parallel_processor import process_api_requests_from_file
from generate_requests import generate_chat_completion_requests_stage1, generate_chat_completion_requests_stage2, filter_json_by_fields, category_filter
from save_generated_data_to_csv import save_generated_data_to_csv, create_dataframe_from_jsonl, transform_dataframe
from csv_to_array import convert_csv_to_array, dataframe_to_structure, convert_json_to_structure
from prompts import specialist_bio_prompt, clinic_bio_prompt, cat_prompt_1, categorization_table
import time  # Import the time module
import pandas as pd
import openpyxl
from src.utils.logger import logger
from logging.handlers import RotatingFileHandler
# Source: https://github.com/tiny-rawr/parallel_process_gpt

'''
    Current Stats:
    GPT 3.5 (tier 3):
        benchmark: 84 items @ 4.6 minutes
        18.26 items per min
        
       items per hr
    
    GPT 4 Turbo (Tier 3):
        benchmark: 84 items @ 4.13 minutes
        20.24 items per min
        1000 items per hr 
'''

# def setup_logging():
#     # Create a custom logger
#     global logger
#     logger = logging.getLogger()
#     logger.setLevel(logging.DEBUG)  # Logger is set to capture all messages from DEBUG and above.
#     #logger.setLevel(logging.INFO)  # Logger is set to capture all messages from info and above.
#
#     # Prevent adding multiple handlers to the logger
#     if not logger.handlers:
#         # Create console handler and set level to debug
#         console_handler = logging.StreamHandler()
#         #console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         console_handler.setLevel(logging.DEBUG)  # Console handler will only emit ERROR and CRITICAL messages.
#         formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
#         console_handler.setFormatter(formatter)
#
#         # Optionally, add a file handler for persistent logging
#         file_handler = RotatingFileHandler('app_log.log', maxBytes=1024*1024*5, backupCount=5)
#         file_handler.setLevel(logging.DEBUG)
#         file_handler.setFormatter(formatter)
#
#         # Add the handlers to the logger
#         logger.addHandler(console_handler)
#         logger.addHandler(file_handler)
#         print("<----------RotatingFileHandler enabled for logging------------->")
#     else:
#         print("\n\n-----------------------------")
#         print(" --> LOGGER IS ALREADY ACTIVE - COULD NOT CONFIGURE")

# Set true for testing as this will send many API requests
is_testing = False
test_limit = 10
selected_model= "gpt-4-turbo" #"gpt-3.5-turbo-0125" #"gpt-4-turbo"

load_dotenv()  # This loads the .env file's contents into the environment
ai_key=os.getenv("API_KEY2") # Load the OpenAI key

def merge_dataframes(df1, df2):
    # Merge df2 into df1 on '__uid__' to add 'rfq_scope' from df1 to df2
    merged_df = pd.merge(df2, df1[['__uid__', 'rfq_scope']], on='__uid__', how='left')

    # Combine 'answer_explanation' and 'review_explanation' into one column, handling NaNs and blanks
    for col in ['answer_explanation', 'review_explanation']:
        merged_df[col] = merged_df[col].fillna('')  # Replace NaN with empty strings

    # Combine the explanations into one field
    merged_df['combined_explanation'] = merged_df.apply(
        lambda x: f"{x['answer_explanation'].strip()} {x['review_explanation'].strip()}".strip(), axis=1)

    # Determine the review status
    merged_df['review'] = merged_df.apply(
        lambda x: x['review'] or (x['__uid__'] in df1['__uid__'].values and df1[df1['__uid__'] == x['__uid__']]['review'].any()), axis=1)

    # Drop redundant columns if needed
    columns_to_drop = ['answer_explanation', 'review_explanation']
    merged_df.drop(columns=columns_to_drop, inplace=True, errors='ignore')

    return merged_df

def optimize_rfq_data(item_df):
    # Initialize an empty list to hold DataFrame rows before concatenation
    data = []
    
    # Group by 'material_description' and aggregate __uid__'s
    grouped = item_df.groupby('material_description')['__uid__'].apply(list).reset_index()
    
    for _, row in grouped.iterrows():
        uid_list = row['__uid__']
        print(f"Full list: {uid_list}, Related UIDs: {uid_list[1:]}")  # Debugging line
        # Instead of appending, create a DataFrame for each row and add it to the list
        data.append(pd.DataFrame({
            'material_description': [row['material_description']],
            '__uid__': [uid_list[0]],  # First UID as the primary
            'related_uids': [uid_list[1:]]  # Rest of the UIDs as related
        }))
    
    # Concatenate all DataFrame rows in the list into a single DataFrame
    mtrl_df = pd.concat(data, ignore_index=True)
    
    return mtrl_df

def clear_file_contents(file_path):
    # Open the file in write mode which clears the existing contents
    with open(file_path, 'w') as file:
        pass  # Opening in 'w' mode and closing the file clears it

def process_ai_requests(API_KEY,requests_filepath, requests_output_filepath, request_stage_desc):
    print(f"\n\n-->ASYNC START - {request_stage_desc}")
    #Process multiple api requests to ChatGPT
    asyncio.run(
        process_api_requests_from_file(
            requests_filepath=requests_filepath,
            save_filepath=requests_output_filepath,
            request_url="https://api.openai.com/v1/chat/completions",
            api_key=API_KEY,
            max_requests_per_minute=float(90000),
            max_tokens_per_minute=float(170000),
            token_encoding_name="cl100k_base",
            max_attempts=int(5),
            logging_level=int(20),
        )
    )
    print(f"\n\n-->ASYNC END - {request_stage_desc}")

if __name__ == "__main__":
    '''
    Set up -->
    -----------------------------------------------------
    -----------------------------------------------------
    '''
    start_time = time.time()  # Capture the start time
    prompt = cat_prompt_1 # Custom prompt with detailed instructions for classifying. 
    
    item_file_path = "item_input.csv"
    cls_file_path = "cls_input.csv"
    
    # Simulate receiving a dataframe
    item_df = pd.read_excel('item_data.xlsx')
    
    # --> Reduce number of rows by getting unique material_descriptions. We will submit these as requests and map the others later 
    mtrl_df = optimize_rfq_data(item_df)
    
    print(f"\n\n > item_df rows: {len(item_df)}")
    print(f"\n\n > item_df columns: {item_df.columns}")
    print(f"\n\n > mtrl_df rows: {len(mtrl_df)}")
    
    #mtrl_df.to_excel("test_matrl_df.xlsx", index=False)
    
    # Create Data (This should be the rfq data)
    item_data = dataframe_to_structure(item_df)
    # item_data = dataframe_to_structure(mtrl_df)
    
    #print("\n\nITEM DATA (From DF):\n",item_data)
    
    cls_data = convert_json_to_structure(categorization_table)
    
    def test_key(k):
        api_key=os.getenv("API_KEY2")
        #print(f"KEY: {api_key}")
        return api_key
    
    #open_ai_key = test_key("TEST") # Uncomment to test key

    if is_testing:
        item_data = item_data[:test_limit] #limit data for testing
        
    requests_filepath_stage1 = "stage1_requests_chat_completion.jsonl"
    requests_filepath_stage2 = "stage2_requests_chat_completion.jsonl"

    requests_output_filepath_stage1 = "stage1_requests_chat_completion_results.jsonl"
    requests_output_filepath_stage2 = "stage2_requests_chat_completion_results.jsonl"

    print("\n\n Generating...")
    
    '''
    End Set up <--
    -----------------------------------------------------
    -----------------------------------------------------
    '''
       
# Stage 1 - Classify 'rfq_scope'
    # Clear json l                  < -------------------- WARNING: WILL CLEAR EXISTING CONTENTS
    clear_file_contents(requests_filepath_stage1)
    clear_file_contents(requests_output_filepath_stage1)

    clear_file_contents(requests_filepath_stage2)
    clear_file_contents(requests_output_filepath_stage2)
                                    # < -------------------- WARNING: WILL CLEAR EXISTING CONTENTS

    stage1_fields_to_include = ['rfq_scope']    
    stage1_fields = filter_json_by_fields(cls_data, stage1_fields_to_include) # <-- Filter by rfq_scope first 
    generate_chat_completion_requests_stage1(requests_filepath_stage1, item_data, stage1_fields, prompt, model_name=selected_model, temperature=0.1, top_p=0.1) # gpt-3.5-turbo-0125 # 
    process_ai_requests(ai_key, requests_filepath_stage1, requests_output_filepath_stage1, "Stage 1 ('rfq_scope')")
   
    # --> Output results for debugging
    df_1 = create_dataframe_from_jsonl(requests_output_filepath_stage1)
    trans_df_1 = transform_dataframe(df_1, item_df) # --> Turn the structure into a usable tabular format.
    # Apply the category_filter function to the 'rfq_scope' column and create a new 'cls_list' column with the result
    trans_df_1['cls_list'] = trans_df_1['rfq_scope'].apply(category_filter)
    
    print(f"\n\n -->DATAFRAME - STAGE 1: ", {len(df_1)})
    # print(df)
    
    #-----------------------------------------------------------------
    # DEBUG
    # Export the Raw DataFrame to an Excel file
    raw_excel_filename = 'raw_output_stage1.xlsx'  # You can choose your own file name and path
    df_1.to_excel(raw_excel_filename, index=False)
    print(f"DataFrame is exported to Excel file {raw_excel_filename} successfully.")
    
    # Export the Transformed (FINAL) DataFrame to an Excel file
    trans_excel_filename = 'transformed_output_stage1.xlsx'  # You can choose your own file name and path
    trans_df_1.to_excel(trans_excel_filename, index=False)
    print(f"DataFrame is exported to Excel file {trans_excel_filename} successfully.")
    # DEBUG
    #-----------------------------------------------------------------
    
# Stage 2 - Classify
    stage2_data = dataframe_to_structure(trans_df_1, stage_2=True)
    generate_chat_completion_requests_stage2(requests_filepath_stage2, stage2_data, cls_data, prompt, model_name=selected_model, temperature=0.1, top_p=0.1)
    
    process_ai_requests(ai_key, requests_filepath_stage2, requests_output_filepath_stage2, "stage 2 ('classify filtered fields')")

    df_2 = create_dataframe_from_jsonl(requests_output_filepath_stage2)
    trans_df_2 = transform_dataframe(df_2, item_df) # --> turn the structure into a usable tabular format.
    
    # debug
    # export the raw dataframe to an excel file
    raw_excel_filename = 'raw_output_stage2.xlsx' 
    df_2.to_excel(raw_excel_filename, index=False)
    print(f"dataframe is exported to excel file {raw_excel_filename} successfully.")
    
    # export the transformed (final) dataframe to an excel file
    trans_excel_filename = 'transformed_output_stage2.xlsx' 
    trans_df_2.to_excel(trans_excel_filename, index=False)
    print(f"dataframe is exported to excel file {trans_excel_filename} successfully.")
    # debug
    # #-----------------------------------------------------------------
    
# Stage 3 - Combine
    # Call the merge function
    final_df = merge_dataframes(trans_df_1, trans_df_2)    

    #-----------------------------------------------------------------
    # Save the final merged DataFrame to an Excel file
    final_excel_filename = 'final_merged_output.xlsx'
    final_df.to_excel(final_excel_filename, index=False)
    print(f"Final merged DataFrame is exported to Excel file {final_excel_filename} successfully.")
    #-----------------------------------------------------------------
    
    end_time = time.time()  # Capture the end time after the process completes
    duration = end_time - start_time  # Calculate the duration
    print(f"\n\n--> Total time taken: {duration} seconds <--\n\n")
    

    #save_generated_data_to_csv(requests_output_filepath)