# replace newlines in material description and values

# todo handle this in gemini extraction
import os
import pandas as pd
from natsort import natsort_keygen

# file = r"C:\Drawings\Clients\brockservices\BSLTX32287\data\gemini_bom_results_20250620_ the base.xlsx"
# outfile = r"C:\Drawings\Clients\brockservices\BSLTX32287\data\gemini_bom_results_20250620_ the base sorted.xlsx"

# bom_df = pd.read_excel(file)

# bom_df = bom_df.sort_values(by=['pdf_page', "pos"], key=natsort_keygen())

# bom_df.to_excel(outfile, index=False)

# print("Done")


file = r"c:\Drawings\Clients\brockservices\BRS_0012 - BSLTX32092-1\data\cwa200 bom - cleaned.xlsx"
outfile = os.path.join(os.path.dirname(file), f"{os.path.basename(file)} - newline cleaned.xlsx")

bom_df = pd.read_excel(file)

for index, row in bom_df.iterrows():
    material_description = row["material_description"]
    if "\n" in material_description:
        print(material_description)
        material_description = material_description.replace("\n", " ")
        print("converted to: ", material_description)

        bom_df.loc[index, "material_description"] = material_description.strip()



# for index, row in bom_df.iterrows():
#     size = row["size"]
#     if "\n" in size:
#         print(size)
#         size = size.replace("\n", " ")
#         print("converted to: ", size)

# for index, row in bom_df.iterrows():
#     quantity = row["quantity"]
#     if "\n" in quantity:
#         print(quantity)
#         quantity = quantity.replace("\n", " ")
#         print("converted to: ", quantity)


    # bom_df.loc[index, "material_description"] = material_description.strip()
    # print(index, material_description)
    # size = row["size"]
    # if pd.isna(size):
    #     continue
    # size = size.replace("\n", " ")
    # bom_df.loc[index, "size"] = size.strip()
    # print(index, size)



# bom_df = bom_df.sort_values(by=['pdf_page', "pos"], key=natsort_keygen())

bom_df = bom_df.reset_index().sort_values(by=['pdf_page', 'index'],
                                            ascending=[True, True],
                                            key=natsort_keygen()).drop('index', axis=1)
bom_df.to_excel(outfile, index=False)
print("Done")
