# ATOM Database Schemas

Generated on: get_db_schemas.py

## Projects

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | projectName | TEXT | 0 |  | 0 |
| 2 | projectNumber | TEXT | 0 |  | 0 |
| 3 | userCompanyName | TEXT | 0 |  | 0 |
| 4 | clientName | TEXT | 0 |  | 0 |
| 5 | projectLocation | TEXT | 0 |  | 0 |
| 6 | projectScope | TEXT | 0 |  | 0 |

## PDFStorage

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | project_id | INTEGER | 0 |  | 0 |
| 2 | originalFilename | TEXT | 0 |  | 0 |
| 3 | pdf_page | INTEGER | 0 |  | 0 |
| 4 | document | BLOB | 0 |  | 0 |
| 5 | documentVendor | TEXT | 0 |  | 0 |
| 6 | documentType | TEXT | 0 |  | 0 |
| 7 | documentName | TEXT | 0 |  | 0 |
| 8 | docSize | TEXT | 0 |  | 0 |
| 9 | skipped | INTEGER | 0 |  | 0 |

## General

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | pdf_id | INTEGER | 0 |  | 0 |
| 2 | annotMarkups | TEXT | 0 |  | 0 |
| 3 | area | TEXT | 0 |  | 0 |
| 4 | avg_elevation | TEXT | 0 |  | 0 |
| 5 | blockCoordinates | TEXT | 0 |  | 0 |
| 6 | clientDocumentId | TEXT | 0 |  | 0 |
| 7 | coordinates | TEXT | 0 |  | 0 |
| 8 | designCode | TEXT | 0 |  | 0 |
| 9 | documentDescription | TEXT | 0 |  | 0 |
| 10 | documentId | TEXT | 0 |  | 0 |
| 11 | documentTitle | TEXT | 0 |  | 0 |
| 12 | drawing | TEXT | 0 |  | 0 |
| 13 | elevation | TEXT | 0 |  | 0 |
| 14 | flangeID | TEXT | 0 |  | 0 |
| 15 | heatTrace | TEXT | 0 |  | 0 |
| 16 | insulationSpec | TEXT | 0 |  | 0 |
| 17 | insulationThickness | TEXT | 0 |  | 0 |
| 18 | lineNumber | TEXT | 0 |  | 0 |
| 19 | max_elevation | TEXT | 0 |  | 0 |
| 20 | mediumCode | TEXT | 0 |  | 0 |
| 21 | min_elevation | TEXT | 0 |  | 0 |
| 22 | modDate | TEXT | 0 |  | 0 |
| 23 | paintSpec | TEXT | 0 |  | 0 |
| 24 | pid | TEXT | 0 |  | 0 |
| 25 | pipeSpec | TEXT | 0 |  | 0 |
| 26 | pipeStandard | TEXT | 0 |  | 0 |
| 27 | processLineList | TEXT | 0 |  | 0 |
| 28 | processUnit | TEXT | 0 |  | 0 |
| 29 | projectNo | TEXT | 0 |  | 0 |
| 30 | projectName | TEXT | 0 |  | 0 |
| 31 | pwht | TEXT | 0 |  | 0 |
| 32 | revision | TEXT | 0 |  | 0 |
| 33 | sequence | TEXT | 0 |  | 0 |
| 34 | service | TEXT | 0 |  | 0 |
| 35 | sheet | TEXT | 0 |  | 0 |
| 36 | size | TEXT | 0 |  | 0 |
| 37 | sys_build | TEXT | 0 |  | 0 |
| 38 | sys_layout_valid | TEXT | 0 |  | 0 |
| 39 | sysDocument | TEXT | 0 |  | 0 |
| 40 | sysDocumentName | TEXT | 0 |  | 0 |
| 41 | sys_filename | TEXT | 0 |  | 0 |
| 42 | sys_path | TEXT | 0 |  | 0 |
| 43 | system | TEXT | 0 |  | 0 |
| 44 | totalSheets | TEXT | 0 |  | 0 |
| 45 | unit | TEXT | 0 |  | 0 |
| 46 | vendorDocumentId | TEXT | 0 |  | 0 |
| 47 | weldId | TEXT | 0 |  | 0 |
| 48 | weldClass | TEXT | 0 |  | 0 |
| 49 | xCoord | TEXT | 0 |  | 0 |
| 50 | xray | TEXT | 0 |  | 0 |
| 51 | yCoord | TEXT | 0 |  | 0 |
| 52 | lf | TEXT | 0 |  | 0 |
| 53 | sf | TEXT | 0 |  | 0 |
| 54 | ef | TEXT | 0 |  | 0 |
| 55 | elbows_90 | TEXT | 0 |  | 0 |
| 56 | elbows_45 | TEXT | 0 |  | 0 |
| 57 | bevels | TEXT | 0 |  | 0 |
| 58 | tees | TEXT | 0 |  | 0 |
| 59 | reducers | TEXT | 0 |  | 0 |
| 60 | caps | TEXT | 0 |  | 0 |
| 61 | flanges | TEXT | 0 |  | 0 |
| 62 | valves_flanged | TEXT | 0 |  | 0 |
| 63 | valves_welded | TEXT | 0 |  | 0 |
| 64 | cut_outs | TEXT | 0 |  | 0 |
| 65 | supports | TEXT | 0 |  | 0 |
| 66 | bends | TEXT | 0 |  | 0 |
| 67 | field_welds | TEXT | 0 |  | 0 |
| 68 | paint_color | TEXT | 0 |  | 0 |
| 69 | cwp | TEXT | 0 |  | 0 |
| 70 | isoNumber | TEXT | 0 |  | 0 |
| 71 | isoType | TEXT | 0 |  | 0 |
| 72 | calculated_eq_length | NUMERIC | 0 |  | 0 |
| 73 | calculated_area | NUMERIC | 0 |  | 0 |
| 74 | union_couplings | NUMERIC | 0 |  | 0 |
| 75 | expansion_joints | NUMERIC | 0 |  | 0 |
| 76 | pmi_req | TEXT | 0 |  | 0 |
| 77 | paut_req | TEXT | 0 |  | 0 |
| 78 | hardness_req | TEXT | 0 |  | 0 |
| 79 | flange_guard | TEXT | 0 |  | 0 |
| 80 | continued_on | TEXT | 0 |  | 0 |
| 81 | connects_to | TEXT | 0 |  | 0 |
| 82 | pickling_req | TEXT | 0 |  | 0 |
| 83 | flushing_req | TEXT | 0 |  | 0 |
| 84 | pipeline_num | TEXT | 0 |  | 0 |
| 85 | weight | TEXT | 0 |  | 0 |

## BOM

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | pdf_id | INTEGER | 0 |  | 0 |
| 2 | ef | TEXT | 0 |  | 0 |
| 3 | sf | TEXT | 0 |  | 0 |
| 4 | pos | TEXT | 0 |  | 0 |
| 5 | material_description | TEXT | 0 |  | 0 |
| 6 | size | TEXT | 0 |  | 0 |
| 7 | ident | TEXT | 0 |  | 0 |
| 8 | item | TEXT | 0 |  | 0 |
| 9 | tag | TEXT | 0 |  | 0 |
| 10 | quantity | TEXT | 0 |  | 0 |
| 11 | status | TEXT | 0 |  | 0 |
| 12 | nb | TEXT | 0 |  | 0 |
| 13 | fluid | TEXT | 0 |  | 0 |
| 14 | clean_spec | TEXT | 0 |  | 0 |
| 15 | line_number |  | 0 |  | 0 |
| 16 | componentCategory | TEXT | 0 |  | 0 |
| 17 ADD | material_code | TEXT | 0 |  | 0 |
| 18 ADD | sch_class | TEXT | 0 |  | 0 |
| 19 ADD | parts | TEXT | 0 |  | 0 |
| 20 ADD | type | TEXT | 0 |  | 0 |
| 21 ADD | weight | TEXT | 0 |  | 0 |
| 22 ADD| number | TEXT | 0 |  | 0 |
| 23 ADD| remarks | TEXT | 0 |  | 0 |
| 24 | ident_code | TEXT | 0 |  | 0 |

## ProjectSources

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | projectId | INTEGER | 0 |  | 1 |
| 1 | filename | TEXT | 0 |  | 2 |
| 2 | documentVendor | TEXT | 0 |  | 0 |
| 3 | dateAdded | TIMESTAMP | 0 |  | 0 |
| 4 | sortNumber | TEXT | 0 |  | 0 |

## Users

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | email | TEXT | 0 |  | 1 |

## SPEC

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | pdf_id | INTEGER | 0 |  | 0 |
| 2 | dp1 | TEXT | 0 |  | 0 |
| 3 | dp2 | TEXT | 0 |  | 0 |
| 4 | dt1 | TEXT | 0 |  | 0 |
| 5 | dt2 | TEXT | 0 |  | 0 |
| 6 | op1 | TEXT | 0 |  | 0 |
| 7 | op2 | TEXT | 0 |  | 0 |
| 8 | opt1 | TEXT | 0 |  | 0 |
| 9 | opt2 | TEXT | 0 |  | 0 |
| 10 | pipeSpec | TEXT | 0 |  | 0 |
| 11 | size | TEXT | 0 |  | 0 |

## SPOOL

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | pdf_id | INTEGER | 0 |  | 0 |
| 2 | cutPiece | TEXT | 0 |  | 0 |
| 3 | length | TEXT | 0 |  | 0 |
| 4 | spool | TEXT | 0 |  | 0 |
| 5 | spec | TEXT | 0 |  | 0 |
| 6 | size | TEXT | 0 |  | 0 |

## RFQ

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | material_description | TEXT | 0 |  | 1 |
| 1 | size | TEXT | 0 |  | 2 |
| 2 | ef | TEXT | 0 |  | 0 |
| 3 | sf | TEXT | 0 |  | 0 |
| 4 | general_category | TEXT | 0 |  | 0 |
| 5 | rfq_scope | TEXT | 0 |  | 0 |
| 6 | unit_of_measure | TEXT | 0 |  | 0 |
| 7 | size1 | TEXT | 0 |  | 0 |
| 8 | size2 | TEXT | 0 |  | 0 |
| 9 | schedule | TEXT | 0 |  | 0 |
| 10 | rating | TEXT | 0 |  | 0 |
| 11 | astm | TEXT | 0 |  | 0 |
| 12 | grade | TEXT | 0 |  | 0 |
| 13 | ansme_ansi | TEXT | 0 |  | 0 |
| 14 | material | TEXT | 0 |  | 0 |
| 15 | abbreviated_material | TEXT | 0 |  | 0 |
| 16 | coating | TEXT | 0 |  | 0 |
| 17 | forging | TEXT | 0 |  | 0 |
| 18 | ends | TEXT | 0 |  | 0 |
| 19 | item_tag | TEXT | 0 |  | 0 |
| 20 | tie_point | TEXT | 0 |  | 0 |
| 21 | pipe_category | TEXT | 0 |  | 0 |
| 22 | valve_type | TEXT | 0 |  | 0 |
| 23 | fitting_category | TEXT | 0 |  | 0 |
| 24 | weld_category | TEXT | 0 |  | 0 |
| 25 | bolt_category | TEXT | 0 |  | 0 |
| 26 | gasket_category | TEXT | 0 |  | 0 |
| 27 | answer_explanation | TEXT | 0 |  | 0 |
| 28 | review | TEXT | 0 |  | 0 |
| 29 | review_explanation | TEXT | 0 |  | 0 |
| 30 | last_updated | TIMESTAMP | 0 |  | 0 |
| 31 | componentCategory | TEXT | 0 |  | 0 |

## raw_data

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | id | INTEGER | 0 |  | 1 |
| 1 | sys_build | TEXT | 0 |  | 0 |
| 2 | sys_path | TEXT | 0 |  | 0 |
| 3 | sys_filename | TEXT | 0 |  | 0 |
| 4 | pdf_id | INTEGER | 0 |  | 0 |
| 5 | pdf_page | INTEGER | 0 |  | 0 |
| 6 | sys_layout_valid | INTEGER | 0 |  | 0 |
| 7 | type | TEXT | 0 |  | 0 |
| 8 | category | TEXT | 0 |  | 0 |
| 9 | value | TEXT | 0 |  | 0 |
| 10 | elevation | TEXT | 0 |  | 0 |
| 11 | x_position | TEXT | 0 |  | 0 |
| 12 | y_position | TEXT | 0 |  | 0 |
| 13 | coordinates | TEXT | 0 |  | 0 |
| 14 | coordinates2 | TEXT | 0 |  | 0 |
| 15 | words | TEXT | 0 |  | 0 |
| 16 | name | TEXT | 0 |  | 0 |
| 17 | title | TEXT | 0 |  | 0 |
| 18 | created_date | TEXT | 0 |  | 0 |
| 19 | mod_date | TEXT | 0 |  | 0 |
| 20 | id_annot_info | TEXT | 0 |  | 0 |
| 21 | annot_page_rotation | INTEGER | 0 |  | 0 |
| 22 | color | TEXT | 0 |  | 0 |
| 23 | annot_type | TEXT | 0 |  | 0 |
| 24 | annot_subtype | TEXT | 0 |  | 0 |
| 25 | vertices | TEXT | 0 |  | 0 |
| 26 | endpoint | TEXT | 0 |  | 0 |
| 27 | stroke_color | TEXT | 0 |  | 0 |
| 28 | font | TEXT | 0 |  | 0 |
| 29 | font_style | TEXT | 0 |  | 0 |
| 30 | font_size | REAL | 0 |  | 0 |
| 31 | flags | INTEGER | 0 |  | 0 |
| 32 | page_rotation | INTEGER | 0 |  | 0 |
| 33 | font_color | TEXT | 0 |  | 0 |
| 34 | annot_markups | TEXT | 0 |  | 0 |

## RawData

| Column ID | Name | Type | NotNull | DefaultValue | PK |
|-----------|------|------|---------|--------------|----|
| 0 | pdf_id | INTEGER | 1 |  | 1 |
| 1 | idx | INTEGER | 1 |  | 2 |
| 2 | sys_layout_valid | INTEGER | 0 |  | 0 |
| 3 | type | TEXT | 0 |  | 0 |
| 4 | category | TEXT | 0 |  | 0 |
| 5 | value | TEXT | 0 |  | 0 |
| 6 | elevation | TEXT | 0 |  | 0 |
| 7 | x_position | TEXT | 0 |  | 0 |
| 8 | y_position | TEXT | 0 |  | 0 |
| 9 | coordinates | TEXT | 0 |  | 0 |
| 10 | coordinates2 | TEXT | 0 |  | 0 |
| 11 | words | TEXT | 0 |  | 0 |
| 12 | name | TEXT | 0 |  | 0 |
| 13 | title | TEXT | 0 |  | 0 |
| 14 | created_date | TEXT | 0 |  | 0 |
| 15 | mod_date | TEXT | 0 |  | 0 |
| 16 | id_annot_info | TEXT | 0 |  | 0 |
| 17 | page_rotation | TEXT | 0 |  | 0 |
| 18 | color | TEXT | 0 |  | 0 |
| 19 | annot_type | TEXT | 0 |  | 0 |
| 20 | annot_subtype | TEXT | 0 |  | 0 |
| 21 | vertices | TEXT | 0 |  | 0 |
| 22 | endpoint | TEXT | 0 |  | 0 |
| 23 | stroke_color | TEXT | 0 |  | 0 |
| 24 | font | TEXT | 0 |  | 0 |
| 25 | font_color | TEXT | 0 |  | 0 |
| 26 | font_style | TEXT | 0 |  | 0 |
| 27 | font_size | TEXT | 0 |  | 0 |
| 28 | flags | TEXT | 0 |  | 0 |

