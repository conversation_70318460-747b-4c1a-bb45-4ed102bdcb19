from src.utils.logger import logger
from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QLineEdit, QVBoxLayout, QSizePolicy, QLabel
from PySide6.QtGui import QRegularExpressionValidator, QShowEvent
from PySide6.QtCore import Qt, QRegularExpression, Signal, QTimer
from src.views.forms.baseform import BaseForm
from functools import partial
from pubsub import pub
from src.pyside_util import get_resource_qicon
from src.config.atem_cloud import send_email_verification

# logger = logging.getLogger(__name__)

PHONE_VERIFY_MSG = ""
EMAIL_VERIFY_MSG = ""


class FourDigitPin(QWidget):

    sgnPinUpdated = Signal()

    # Single digit regular expression
    regEx = QRegularExpression("[0-9]")
    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QHBoxLayout())
        self.digits = []
        for n in range(4):
            digit = QLineEdit()
            digit.setObjectName("pinDigit")
            digit.setAlignment(Qt.AlignmentFlag.AlignCenter)
            inputValidator = QRegularExpressionValidator(self.regEx, digit)
            digit.setValidator(inputValidator)
            digit.textChanged.connect(partial(self.onDigitTextChanged, digit))
            digit.setFixedSize(64, 64)
            self.digits.append(digit)
            self.layout().addWidget(digit)

    def getValue(self) -> list:
        """Return list of 4 digit pin"""
        value = []
        for digit in self.digits:
            try:
                value.append(int(digit.text()))
            except:
                return False
        return value

    def clear(self):
        for digit in self.digits:
            digit.setText("")

    def onDigitTextChanged(self, digit, event):
        """Switch to next digit after number entered"""
        try:
            index = self.digits.index(digit)
            self.digits[index+1].setFocus()
        except Exception as e:
            # We dont really care about this error
            logger.info(e)
            # print(e, event, digit)
        self.sgnPinUpdated.emit()
    
    def showEvent(self, event) -> None:
        self.digits[0].setFocus()
        return super().showEvent(event)


class AuthenticateForm(BaseForm):

    sgnSendVerificationEmail = Signal(str, str, bool)

    def __init__(self, parent):
        super().__init__(parent)
        self.email = None
        self.idToken = None
        
    def initUi(self):
        self.formSize.setHeight(420)
        self.title.setText("Authenticate Account")
        self.subtitle.setTextFormat(Qt.TextFormat.RichText)
        self.subtitle.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)
        # self.subtitle.setOpenExternalLinks(True)
        self.email = "<EMAIL>"
        self.subtitle.setText(f"Enter the 4-digit PIN code sent to your email {self.email}")
        self.subtitle.setText(f"To complete account creation, please verify your account.")
        self.subtitle.setWordWrap(True)

        self.addStretchSpacer()

        self.status = QLabel("")
        self.status.setText("3333333")
        self.status.setText("Send Email verification or continue to <a href='LoginForm'>log in</a>")
        self.status.linkActivated.connect(self.onLinkActivated)
        self.layout().addRow(self.status)
        
        self.addVSpace()

        self.pbVerifyEmail = QPushButton("Verify By Email", self)
        self.pbVerifyEmail.setIcon(get_resource_qicon("mail.svg"))
        self.pbVerifyEmail.setObjectName("formButton")
        self.pbVerifyEmail.setMinimumHeight(64)
        self.pbVerifyEmail.clicked.connect(self.onVerifyEmail)
        self.layout().addRow(self.pbVerifyEmail)

        self.addVSpace(32)

        # self.pbVerifyPhone = QPushButton("Verify By Phone SMS", self)
        # self.pbVerifyPhone.setIcon(get_resource_qicon("smartphone.svg"))
        # self.pbVerifyPhone.setMinimumHeight(64)
        # self.pbVerifyPhone.setObjectName("formButton")
        # self.layout().addRow(self.pbVerifyPhone)

        self.widgetVerify = QWidget()
        self.widgetVerify.setLayout(QVBoxLayout())
        # self.pin = FourDigitPin(self)
        # self.widgetVerify.layout().addWidget(self.pin)
        self.layout().addWidget(self.widgetVerify)

        self.pbAuthenticate = QPushButton("Authenticate")
        self.pbAuthenticate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbAuthenticate.setMinimumHeight(48)
        self.pbAuthenticate.clicked.connect(self.onNext)
        self.pbAuthenticate.setContentsMargins(0, 132, 0, 32)
        self.pbAuthenticate.setDisabled(True)

        self.widgetVerify.layout().addWidget(self.pbAuthenticate)
        self.widgetVerify.hide()

        self.addStretchSpacer()

        self.setFloatingButtonCancel()

        # self.pin.sgnPinUpdated.connect(self.onPinUpdated)
        pub.subscribe(self.onNewUserDetailsAuthenticated, "authenticate-new-user-response")

        # self.sgnSendVerificationEmail.connect(self.onSendVerificationEmail)

        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.setInterval(2000)
        self.timer.timeout.connect(self.onTimeout)

    def initDefaults(self):
        return
        self.pin.clear()

    def setParams(self, params):
        self.email = params["email"]
        self.idToken = params["idToken"]
        self.subtitle.setText(f"To complete account creation, please verify your account - {self.email}")

    def onNext(self, event):
        self.setBusyState()
        pub.sendMessage("authenticate-new-user", data={"pin": self.pin.getValue()})
    
    def onPinUpdated(self):
        value = self.pin.getValue()
        if not value:
            self.pbAuthenticate.setEnabled(False)
            return
        else:
            self.pbAuthenticate.setEnabled(True)

    def setEmail(self, email):
        self.email = email
        # self.subtitle.setText(f"Enter the 4-digit PIN code sent to your email {self.email}")

    def onNewUserDetailsAuthenticated(self, response):
        self.setNormalState()
        if response.get("ok") is True:
            self.sgnSwitchTo.emit("CompanyProfileForm")

    def onFloatingButton(self):
        self.sgnSwitchTo.emit("LoginForm")

    def onVerifyEmail(self):
        if not self.idToken:
            return
        res = send_email_verification(self.idToken)
        self.pbVerifyEmail.setEnabled(False)
        if "error" in res:
            message = res["error"].get("message", "")
            # QMessageBox.information(self, "Email Verification", f"Failed to send email verification. {message}")
            self.status.setText(f"Failed to send email verification.\n{message}")
            self.pbVerifyEmail.setText("Email Verification Request Failed")
        else:
            self.hideErrorStatus()
            # QMessageBox.information(self, "Verify your email address",
            #         f"To complete account creation, please verify your account.\nA verification email has been sent")
            self.status.setText(f"Verification email sent. Once verified, continue to <a href='LoginForm'>log in</a>")
            self.style().unpolish(self.status)
            self.style().polish(self.status)
            # self.status.setText("Send Email verification or continue to <a href='LoginForm'>log in</a>")
            self.pbVerifyEmail.setText("Email Verification Sent")
        
        self.timer.start()

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)
    
    def showEvent(self, event: QShowEvent) -> None:
        self.hideErrorStatus()
        self.status.setText("Send Email verification or continue to <a href='LoginForm'>log in</a>")
        return super().showEvent(event)

    def onTimeout(self):
        self.pbVerifyEmail.setEnabled(True)
        self.pbVerifyEmail.setText("Verify By Email")


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    import sys
    sys.path[0] = ""
    from src.theme import stylesheet
    app = QApplication()
    app.setStyleSheet(stylesheet)
    w = AuthenticateForm(None)
    w.show()
    w.setMinimumSize(1280, 768)
    app.exec()