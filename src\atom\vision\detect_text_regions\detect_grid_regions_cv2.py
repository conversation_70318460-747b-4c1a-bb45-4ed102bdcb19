import cv2
import pandas as pd
import numpy as np
import openpyxl
import os
import argparse
import sys
import json

import time
from functools import wraps

# Code Explanation
'''
Purpose:
    Detect text elements within images while filtering out table gridlines and other non-text elements.
    This detection is used to cross-reference against PyMuPDF text/annotation bounding boxes to identify
    cases where tables contain both text and image elements (e.g., in projects Axis 017 & Axis 018).

Method:
    This implementation uses Connected Component Analysis (CCA), which is particularly effective for
    text detection in structured documents for several reasons:
    - CCA treats connected pixels as single objects, making it ideal for identifying distinct text elements
    - Unlike contour detection or edge detection, CCA naturally groups related pixels into meaningful units
    - CCA is less sensitive to noise and grid lines compared to other methods like Hough transforms
    - The method provides useful statistics (area, width, height) that help filter out non-text elements

Process Overview:
    1. Image Preprocessing:
       - Convert to grayscale for simpler processing
       - Apply Gaussian blur to reduce noise
       - Create binary image using threshold to separate text from background

    2. Connected Component Analysis:
       - Use cv2.connectedComponentsWithStats() to identify distinct regions
       - Each component represents a potential text region
       - Component statistics are used to filter out non-text elements:
         * Area (filters out noise and very large regions)
         * Density (ensures enough dark pixels to be text)
         * Dimensions (removes lines and dots)

    3. Region Filtering and Sorting:
       - Filter regions based on size, density, and position criteria
       - Sort regions by their position (top-to-bottom, left-to-right)
       - Group nearby regions that likely belong together

    4. Visualization (Debug Mode):
       - Generate debug images showing each processing step
       - Draw bounding boxes around detected text regions

Key OpenCV Methods Used:
    - cv2.cvtColor(): Convert between color spaces (BGR to grayscale)
    - cv2.GaussianBlur(): Reduce image noise and detail
    - cv2.threshold(): Create binary image
    - cv2.connectedComponentsWithStats(): Perform connected component analysis
        * Returns: number of labels, label matrix, stats matrix, centroids
        * Stats include: left, top, width, height, area for each component
    - cv2.rectangle(): Draw bounding boxes for visualization
    - cv2.putText(): Add region labels in debug output

Why This Method Works Well:
    - Robust against table gridlines and other document elements
    - Computationally efficient compared to deep learning approaches
    - Provides good control over text region identification through filtering parameters
    - Simple to debug and adjust with visualization options
    - Reliable for structured documents with clear text/background separation

Usage:
    Should be run during file upload to identify areas requiring OCR processing.
    Debug mode generates visualization of each processing step for verification.
'''

# Add this timing decorator
def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not wrapper.debug:  # Skip timing if debug is False
            return func(*args, **kwargs)

        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        print(f"Function '{func.__name__}' took {end_time - start_time:.4f} seconds")
        return result

    wrapper.debug = False  # Default debug state
    return wrapper

def remove_grid_lines(thresh, size: int = 30):
    """Removes horizontal and vertical lines

    1. Copies threshold input image
    2. Detect horizontal lines
    3. Detect vertical lines
    4. Subtract horizontal and vertical lines from threshol image

    Params:
        thresh: Thresholded image
        size: Vary the horizontal and vertical structure

    Returns:
        Image with removed grid lines
    """
    horizontal = np.copy(thresh)
    vertical = np.copy(thresh)

    # Create structure element for extracting horizontal lines through morphology operations
    cols = horizontal.shape[1]
    horizontal_size = cols // size
    horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
    # Apply morphology operations
    horizontal = cv2.erode(horizontal, horizontalStructure)
    horizontal = cv2.dilate(horizontal, horizontalStructure)
    # Remove horizontal lines
    thresh = cv2.subtract(thresh, horizontal)

    # Create structure element for extracting vertical lines through morphology operations
    rows = vertical.shape[0]
    verticalsize = rows // size
    verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
    # Apply morphology operations
    vertical = cv2.erode(vertical, verticalStructure)
    vertical = cv2.dilate(vertical, verticalStructure)
    # Remove horizontal lines
    thresh = cv2.subtract(thresh, vertical)

    return thresh

def extract_grid_lines(thresh, size: int = 30):
    """Extracts horizontal and vertical grid lines

    1. Copies threshold input image
    2. Detect horizontal lines
    3. Detect vertical lines
    4. Returns both horizontal and vertical lines

    Params:
        thresh: Thresholded image
        size: Vary the horizontal and vertical structure

    Returns:
        Dictionary with horizontal and vertical grid lines
    """
    # Copy the thresholded image
    horizontal = thresh.copy()
    vertical = thresh.copy()
    
    # Pre-process to enhance lines
    # Apply slight blur to connect broken lines
    kernel = np.ones((3, 3), np.uint8)
    preprocessed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    # Create copies for horizontal and vertical processing
    horizontal = preprocessed.copy()
    vertical = preprocessed.copy()

    # Specify size on horizontal axis
    cols = horizontal.shape[1]
    horizontal_size = cols // size

    # Create structure element for extracting horizontal lines through morphology operations
    horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
    
    # Create smaller structure for finer lines
    fineHorizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size // 2, 1))

    # Apply morphology operations
    horizontal1 = cv2.erode(horizontal, horizontalStructure)
    horizontal1 = cv2.dilate(horizontal1, horizontalStructure)
    
    # Process with finer structure to catch thinner lines
    horizontal2 = cv2.erode(horizontal, fineHorizontalStructure)
    horizontal2 = cv2.dilate(horizontal2, fineHorizontalStructure)
    
    # Combine results
    horizontal = cv2.bitwise_or(horizontal1, horizontal2)

    # Specify size on vertical axis
    rows = vertical.shape[0]
    verticalsize = rows // size

    # Create structure element for extracting vertical lines through morphology operations
    verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
    
    # Create smaller structure for finer lines
    fineVerticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize // 2))

    # Apply morphology operations
    vertical1 = cv2.erode(vertical, verticalStructure)
    vertical1 = cv2.dilate(vertical1, verticalStructure)
    
    # Process with finer structure to catch thinner lines
    vertical2 = cv2.erode(vertical, fineVerticalStructure)
    vertical2 = cv2.dilate(vertical2, fineVerticalStructure)
    
    # Combine results
    vertical = cv2.bitwise_or(vertical1, vertical2)

    # Combine horizontal and vertical lines if needed
    grid_lines = cv2.add(horizontal, vertical)

    return {
        'horizontal': horizontal,
        'vertical': vertical,
        'combined': grid_lines
    }

def detect_text_regions(image,
                        min_area=10,
                        debug=False,
                        debug_dir=None,
                        debug_bottleneck=False):
    """
    Detect text regions using connected component analysis (CCA) method.
    """
    timer.debug_bottleneck = debug_bottleneck  # Set the debug_bottleneck state for the timer decorator

    start_total = time.time() if debug_bottleneck else 0

    # Read the image
    start = time.time() if debug_bottleneck else 0

    if isinstance(image, str):
        img = cv2.imread(image)
    else:
        img = image  # Expect a CV image

    if debug_bottleneck:
        print(f"Image reading took {time.time() - start:.4f} seconds")

    if img is None:
        raise ValueError("Could not read the image")

    print(f"Image dimensions: {img.shape}")
    img_height, img_width = img.shape[:2]

    # Time image processing operations
    start = time.time() if debug_bottleneck else 0

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply binary threshold - values should be ignored when using OTSU
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

    # Perform bitwise operation
    bitwise = cv2.bitwise_not(thresh)

    # Remove horizontal and vertical border and grid lines
    img2 = remove_grid_lines(bitwise)

    # Horizontal dilation - this influences CCA into forming regions by word
    # and reduce grouping of vertical i.e. paragraphs or small regions
    dilated = cv2.dilate(img2, np.ones((1, 3), np.uint8), iterations=4)

    if debug_bottleneck:
        print(f"Image preprocessing took {time.time() - start:.4f} seconds")

    # Time connected component analysis
    start = time.time() if debug_bottleneck else 0
    # num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(dilated, connectivity=connectivity)
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStatsWithAlgorithm(dilated, 8, cv2.CV_32S, 0)
    if debug_bottleneck:
        print(f"Connected component analysis took {time.time() - start:.4f} seconds")

    # Time region extraction and filtering
    start = time.time() if debug_bottleneck else 0

    # Pre-calculate all component properties at once using NumPy operations
    areas = stats[:, cv2.CC_STAT_AREA]
    widths = stats[:, cv2.CC_STAT_WIDTH]
    heights = stats[:, cv2.CC_STAT_HEIGHT]
    left = stats[:, cv2.CC_STAT_LEFT]
    top = stats[:, cv2.CC_STAT_TOP]

    # Create boolean mask for size-based filtering
    max_area = img_width * img_height * 0.1
    size_mask = (
        (areas > min_area) &
        (areas < max_area) &
        (widths > 2) &
        (heights > 2)
    )

    # Get indices of components that pass size criteria (excluding background)
    valid_indices = np.where(size_mask)[0]
    valid_indices = valid_indices[valid_indices != 0]  # Remove background (label 0)

    # Extract regions that are likely to contain text
    text_regions = []
    labels_view = labels  # Create a view for faster access

    # Process only the valid components
    for label in valid_indices:
        x = left[label]
        y = top[label]
        w = widths[label]
        h = heights[label]

        # Calculate density using the original method but with optimized array access
        mask = (labels_view[y:y+h, x:x+w] == label)
        density = np.sum(mask) / (w * h)

        if density > 0.2:  # Has enough content
            text_regions.append((x, y, w, h))

    # Sort regions by position using the exact same criteria
    text_regions.sort(key=lambda r: (r[1] // 15, r[0]))

    if debug_bottleneck:
        print(f"Region extraction and filtering took {time.time() - start:.4f} seconds")

    # Save debug images if requested
    if debug and debug_dir:
        start = time.time() if debug_bottleneck else 0

        os.makedirs(debug_dir, exist_ok=True)

        # Save intermediate processing steps
        cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
        # cv2.imwrite(os.path.join(debug_dir, "2_blurred.png"), blurred)
        cv2.imwrite(os.path.join(debug_dir, "3_threshold.png"), thresh)

        # Create a color-coded visualization of components
        label_hue = np.uint8(179 * labels / np.max(labels))
        blank_ch = 255 * np.ones_like(label_hue)
        labeled_img = cv2.merge([label_hue, blank_ch, blank_ch])
        labeled_img = cv2.cvtColor(labeled_img, cv2.COLOR_HSV2BGR)
        labeled_img[label_hue == 0] = 0
        cv2.imwrite(os.path.join(debug_dir, "4_components.png"), labeled_img)

        # Draw bounding boxes for debug
        debug_img = img.copy()
        for x, y, w, h in text_regions:
            cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 1)
        cv2.imwrite(os.path.join(debug_dir, "5_detected_regions.png"), debug_img)

        if debug_bottleneck:
            print(f"Debug image saving took {time.time() - start:.4f} seconds")

    if debug_bottleneck:
        print(f"Total processing time: {time.time() - start_total:.4f} seconds")

    return text_regions

@timer
def visualize_regions(image_path, regions, output_path):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    img = cv2.imread(image_path)
    vis_img = img.copy()

    # Draw text regions with alternating colors
    for i, (x, y, w, h) in enumerate(regions):
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

    if not output_path.lower().endswith(('.png', '.jpg', '.jpeg')):
        output_path += '.png'

    cv2.imwrite(output_path, vis_img)
    return vis_img

def detect_grid_lines(image, grid_size=30, min_length=30, debug=False, debug_dir=None):
    """
    Detect grid lines in an image using morphological operations.

    Args:
        image: Input image path or numpy array
        grid_size: Size parameter for morphological operations
        min_length: Minimum length of lines to detect
        debug: Enable debug mode with visualizations
        debug_dir: Directory to save debug visualizations

    Returns:
        Dictionary with grid line regions and images
    """
    # Read the image if it's a path
    if isinstance(image, str):
        img = cv2.imread(image)
    else:
        img = image  # Expect a CV image

    if img is None:
        raise ValueError("Could not read the image")

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply binary threshold - values should be ignored when using OTSU
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

    # Perform bitwise operation to invert the image
    bitwise = cv2.bitwise_not(thresh)

    # Extract grid lines
    grid_lines = extract_grid_lines(bitwise, size=grid_size)

    # Find contours in the grid lines images with improved retrieval mode
    # Changed from RETR_EXTERNAL to RETR_LIST to get all contours, not just external ones
    horizontal_contours, _ = cv2.findContours(grid_lines['horizontal'], cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    vertical_contours, _ = cv2.findContours(grid_lines['vertical'], cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    
    # Additional processing to ensure we don't miss any lines
    # Apply additional dilation to make lines more prominent before finding contours again
    kernel = np.ones((3, 3), np.uint8)
    horizontal_dilated = cv2.dilate(grid_lines['horizontal'], kernel, iterations=1)
    vertical_dilated = cv2.dilate(grid_lines['vertical'], kernel, iterations=1)
    
    # Find contours in the dilated images and add them to the existing contours
    additional_h_contours, _ = cv2.findContours(horizontal_dilated, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    additional_v_contours, _ = cv2.findContours(vertical_dilated, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    
    # Combine the contours
    horizontal_contours = list(horizontal_contours) + list(additional_h_contours)
    vertical_contours = list(vertical_contours) + list(additional_v_contours)

    # Filter contours by size
    horizontal_regions = []
    for contour in horizontal_contours:
        x, y, w, h = cv2.boundingRect(contour)
        if w >= min_length:  # Only keep horizontal lines with sufficient length
            horizontal_regions.append((x, y, w, h))

    vertical_regions = []
    for contour in vertical_contours:
        x, y, w, h = cv2.boundingRect(contour)
        if h >= min_length:  # Only keep vertical lines with sufficient length
            vertical_regions.append((x, y, w, h))

    # Get image dimensions for relative coordinates
    img_height, img_width = img.shape[:2]
    
    # Detect rectangles formed by intersecting lines
    result = detect_rectangles(horizontal_regions, vertical_regions, img_width=img_width, img_height=img_height)
    
    # Unpack the result (absolute and relative coordinates)
    if isinstance(result, tuple):
        rectangles, relative_rectangles = result
    else:
        rectangles = result
        relative_rectangles = []

    # Save debug images if requested
    if debug and debug_dir:
        os.makedirs(debug_dir, exist_ok=True)

        # Save intermediate processing steps
        cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
        cv2.imwrite(os.path.join(debug_dir, "2_threshold.png"), thresh)
        cv2.imwrite(os.path.join(debug_dir, "3_bitwise.png"), bitwise)

        # Save grid line images
        cv2.imwrite(os.path.join(debug_dir, "4_horizontal_lines.png"), grid_lines['horizontal'])
        cv2.imwrite(os.path.join(debug_dir, "5_vertical_lines.png"), grid_lines['vertical'])
        cv2.imwrite(os.path.join(debug_dir, "6_combined_grid.png"), grid_lines['combined'])

        # Draw bounding boxes for debug with increased line thickness
        h_debug_img = img.copy()
        for x, y, w, h in horizontal_regions:
            cv2.rectangle(h_debug_img, (x, y), (x + w, y + h), (0, 255, 0), 2)  # Increased from 1 to 2
        cv2.imwrite(os.path.join(debug_dir, "7_horizontal_regions.png"), h_debug_img)
        
        v_debug_img = img.copy()
        for x, y, w, h in vertical_regions:
            cv2.rectangle(v_debug_img, (x, y), (x + w, y + h), (0, 0, 255), 2)  # Increased from 1 to 2
        cv2.imwrite(os.path.join(debug_dir, "8_vertical_regions.png"), v_debug_img)
        
        # Combined visualization with increased line thickness
        combined_debug_img = img.copy()
        for x, y, w, h in horizontal_regions:
            cv2.rectangle(combined_debug_img, (x, y), (x + w, y + h), (0, 255, 0), 2)  # Increased from 1 to 2
        for x, y, w, h in vertical_regions:
            cv2.rectangle(combined_debug_img, (x, y), (x + w, y + h), (0, 0, 255), 2)  # Increased from 1 to 2
        cv2.imwrite(os.path.join(debug_dir, "9_all_grid_regions.png"), combined_debug_img)

        # Draw detected rectangles
        rect_debug_img = img.copy()
        for x, y, w, h, rect_id in rectangles:
            # Use different colors for different rectangles
            color = ((rect_id * 50) % 255, (rect_id * 100) % 255, (rect_id * 150) % 255)
            # Increased line thickness from 2 to 4
            cv2.rectangle(rect_debug_img, (x, y), (x + w, y + h), color, 4)
            # Add rectangle ID with larger font (increased from 0.6 to 1.2) and thicker lines (from 2 to 3)
            cv2.putText(rect_debug_img, str(rect_id), (x + 10, y + 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)
        cv2.imwrite(os.path.join(debug_dir, "10_detected_rectangles.png"), rect_debug_img)

    return {
        'horizontal_regions': horizontal_regions,
        'vertical_regions': vertical_regions,
        'rectangles': rectangles,
        'relative_rectangles': relative_rectangles,
        'horizontal_image': grid_lines['horizontal'],
        'vertical_image': grid_lines['vertical'],
        'combined_image': grid_lines['combined']
    }

def detect_rectangles(horizontal_regions, vertical_regions, tolerance=5, img_width=None, img_height=None):
    """
    Detect rectangles formed by intersecting horizontal and vertical lines.
    
    Args:
        horizontal_regions: List of horizontal line regions (x, y, w, h)
        vertical_regions: List of vertical line regions (x, y, w, h)
        tolerance: Pixel tolerance for intersection detection
        img_width: Width of the original image (for relative coordinates)
        img_height: Height of the original image (for relative coordinates)
        
    Returns:
        List of detected rectangles in format (x, y, w, h, id)
        If img_width and img_height are provided, also returns relative coordinates
    """
    rectangles = []
    rect_id = 1
    
    # Convert horizontal and vertical lines to line segments for easier processing
    h_lines = []
    for x, y, w, h in horizontal_regions:
        h_lines.append({
            'y': y + h//2,  # Use the middle of the line height
            'x1': x,
            'x2': x + w,
            'original': (x, y, w, h)
        })
    
    v_lines = []
    for x, y, w, h in vertical_regions:
        v_lines.append({
            'x': x + w//2,  # Use the middle of the line width
            'y1': y,
            'y2': y + h,
            'original': (x, y, w, h)
        })
    
    # Sort lines by position for more efficient processing
    h_lines.sort(key=lambda l: l['y'])
    v_lines.sort(key=lambda l: l['x'])
    
    # Find all possible rectangle candidates
    for i, h1 in enumerate(h_lines):
        for j, h2 in enumerate(h_lines[i+1:], i+1):
            # Skip if horizontal lines are too close
            if h2['y'] - h1['y'] < 10:  # Minimum height threshold
                continue
                
            # Find vertical lines that intersect with both horizontal lines
            left_bounds = []
            right_bounds = []
            
            for v in v_lines:
                # Check if vertical line spans between the two horizontal lines
                if v['y1'] - tolerance <= h1['y'] <= v['y2'] + tolerance and \
                   v['y1'] - tolerance <= h2['y'] <= v['y2'] + tolerance:
                    
                    # Check if vertical line intersects with the horizontal lines
                    if h1['x1'] - tolerance <= v['x'] <= h1['x2'] + tolerance and \
                       h2['x1'] - tolerance <= v['x'] <= h2['x2'] + tolerance:
                        
                        # Categorize as left or right boundary
                        # We'll use these to form rectangles
                        if abs(v['x'] - h1['x1']) <= tolerance or abs(v['x'] - h2['x1']) <= tolerance:
                            left_bounds.append(v)
                        elif abs(v['x'] - h1['x2']) <= tolerance or abs(v['x'] - h2['x2']) <= tolerance:
                            right_bounds.append(v)
                        else:
                            # This is a vertical line somewhere in the middle
                            # Could be used for both left and right boundaries of different rectangles
                            left_bounds.append(v)
                            right_bounds.append(v)
            
            # Form rectangles from all possible left-right boundary combinations
            for left in left_bounds:
                for right in right_bounds:
                    if left['x'] >= right['x']:  # Skip invalid combinations
                        continue
                        
                    # Create rectangle
                    x = left['x']
                    y = h1['y']
                    w = right['x'] - left['x']
                    h = h2['y'] - h1['y']
                    
                    # Skip very small rectangles
                    if w < 10 or h < 10:
                        continue
                        
                    rectangles.append((int(x), int(y), int(w), int(h), rect_id))
                    rect_id += 1
    
    # Filter out duplicate and overlapping rectangles
    filtered_rectangles = []
    for rect in rectangles:
        x1, y1, w1, h1, id1 = rect
        is_duplicate = False
        
        for existing_rect in filtered_rectangles:
            x2, y2, w2, h2, id2 = existing_rect
            
            # Check if rectangles are nearly identical
            if abs(x1 - x2) <= tolerance and abs(y1 - y2) <= tolerance and \
               abs(w1 - w2) <= tolerance and abs(h1 - h2) <= tolerance:
                is_duplicate = True
                break
        
        if not is_duplicate:
            filtered_rectangles.append(rect)
    
    # If image dimensions are provided, also return relative coordinates
    if img_width is not None and img_height is not None and filtered_rectangles:
        relative_rectangles = []
        for x, y, w, h, rect_id in filtered_rectangles:
            # Convert to relative coordinates (percentages)
            rel_x = x / img_width
            rel_y = y / img_height
            rel_w = w / img_width
            rel_h = h / img_height
            relative_rectangles.append((rel_x, rel_y, rel_w, rel_h, rect_id))
        
        return filtered_rectangles, relative_rectangles
    
    return filtered_rectangles

# Add a main block to test the code directly from the IDE
if __name__ == "__main__":
    # Configure these settings directly
    input_image = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\images\page_1.png"  # Replace with your actual image path
    output_dir = "output"
    debug_mode = True
    grid_size = 30  # Adjust based on the scale of your drawings
    min_length = 0  # Minimum length for grid lines

    # Check if input file exists
    if not os.path.isfile(input_image):
        print(f"Error: Input file '{input_image}' does not exist.")
        #sys.exit(1)

    # Create output directory if it doesn't exist
    if debug_mode:
        os.makedirs(output_dir, exist_ok=True)

    print(f"Processing image: {input_image}")
    print(f"Debug mode: {'enabled' if debug_mode else 'disabled'}")
    print(f"Grid size: {grid_size}")
    print(f"Minimum line length: {min_length}")

    try:
        # Call the detect_grid_lines function
        grid_results = detect_grid_lines(
            input_image,
            grid_size=grid_size,
            min_length=min_length,
            debug=debug_mode,
            debug_dir=output_dir
        )

        # Print the results
        print(f"\nDetected {len(grid_results['horizontal_regions'])} horizontal grid lines")
        print(f"Detected {len(grid_results['vertical_regions'])} vertical grid lines")
        print(f"Detected {len(grid_results['rectangles'])} rectangles (title blocks)")
        
        # Print rectangle information
        if len(grid_results['rectangles']) > 0:
            print("\nRectangle details (absolute coordinates):")
            for i, (x, y, w, h, rect_id) in enumerate(grid_results['rectangles']):
                print(f"Rectangle {rect_id}: x={x}, y={y}, width={w}, height={h}")
            
            # Print relative coordinates (percentages)
            if len(grid_results['relative_rectangles']) > 0:
                print("\nRectangle details (relative coordinates - percentages):")
                for i, (rel_x, rel_y, rel_w, rel_h, rect_id) in enumerate(grid_results['relative_rectangles']):
                    print(f"Rectangle {rect_id}: x={rel_x:.6f}, y={rel_y:.6f}, width={rel_w:.6f}, height={rel_h:.6f}")
                
                # Save relative coordinates to JSON file for template usage
                template_file = os.path.join(output_dir, "template.json")
                template_data = {
                    "rectangles": [
                        {
                            "id": int(rect_id),
                            "x": float(rel_x),
                            "y": float(rel_y),
                            "width": float(rel_w),
                            "height": float(rel_h)
                        } for rel_x, rel_y, rel_w, rel_h, rect_id in grid_results['relative_rectangles']
                    ],
                    "image_path": input_image,
                    "grid_size": grid_size,
                    "min_length": min_length
                }
                
                with open(template_file, 'w') as f:
                    json.dump(template_data, f, indent=4)
                
                print(f"\nTemplate saved to: {template_file}")
        
        # Create visualization
        if debug_mode:
            # Visualizations are already saved in the detect_grid_lines function
            print(f"\nVisualizations saved to: {output_dir}")
            print(f"Check '{os.path.join(output_dir, '10_detected_rectangles.png')}' for rectangle visualization")
        
        print("\nProcessing completed successfully!")

    except Exception as e:
        print(f"Error processing image: {str(e)}")
        #sys.exit(1)

    # Create final dataframe
    #df = pd.DataFrame(all_regions_data)

    # Sort by page number and reset index to get region numbers
    #df = df.sort_values('pdf_page').reset_index(drop=True)

    #print("\nFinal DataFrame:")
    #print(df)

    # Export
    #df.to_excel(os.path.join(output_dir, 'text_regions.xlsx'), index=True)

# if __name__ == "__main__":

#     pg = "1"
#     image_path = fr"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\outputImages\CV2\page_{pg}.png"

#     output_dir = os.path.dirname(image_path)
#     debug_dir = os.path.join(output_dir, "debug")

#     try:
#         regions = detect_text_regions(
#             image_path,
#             min_area=10,
#             debug=True,
#             debug_dir=debug_dir,
#             debug_bottleneck=True
#         )

#         print(f"Found {len(regions)} text regions:")
#         for i, (x, y, w, h) in enumerate(regions, 1):
#             print(f"Region {i}: x={x}, y={y}, width={w}, height={h}")

#         output_path = os.path.join(output_dir, f"detected_regions_{pg}.png")
#         visualize_regions(image_path, regions, output_path)
#         print(f"\nVisualization saved to: {output_path}")

#     except Exception as e:
#         print(f"Error processing image: {str(e)}")



# # Code works but is slow on large images
# def detect_text_regions_nonvectorized(image_path, min_area=10, debug=False, debug_dir=None, debug_bottleneck=False):
#     """
#     Detect text regions using connected component analysis (CCA) method.
#     """

#     timer.debug_bottleneck = debug_bottleneck  # Set the debug_bottleneck state for the timer decorator

#     start_total = time.time() if debug_bottleneck else 0

#     # Read the image
#     start = time.time() if debug_bottleneck else 0

#     # Read the image
#     img = cv2.imread(image_path)

#     if debug_bottleneck:
#         print(f"Image reading took {time.time() - start:.4f} seconds")

#     if img is None:
#         raise ValueError("Could not read the image")

#     print(f"Image dimensions: {img.shape}")
#     img_height, img_width = img.shape[:2]

#     # Time image processing operations
#     start = time.time() if debug_bottleneck else 0

#     # Convert to grayscale
#     gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

#     # Apply Gaussian blur to reduce noise
#     blurred = cv2.GaussianBlur(gray, (3, 3), 0)

#     # Apply binary threshold
#     _, thresh = cv2.threshold(blurred, 200, 255, cv2.THRESH_BINARY_INV)

#     if debug_bottleneck:
#         print(f"Image preprocessing took {time.time() - start:.4f} seconds")

#     # Time region extraction and filtering
#     start = time.time() if debug_bottleneck else 0

#     # Perform connected component analysis
#     num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(thresh, connectivity=8)

#     if debug_bottleneck:
#         print(f"Connected component analysis took {time.time() - start:.4f} seconds")

#     # Time region extraction and filtering
#     start = time.time() if debug_bottleneck else 0

#     # Extract regions that are likely to contain text
#     text_regions = []

#     # Skip label 0 as it's the background
#     for label in range(1, num_labels):
#         x = stats[label, cv2.CC_STAT_LEFT]
#         y = stats[label, cv2.CC_STAT_TOP]
#         w = stats[label, cv2.CC_STAT_WIDTH]
#         h = stats[label, cv2.CC_STAT_HEIGHT]
#         area = stats[label, cv2.CC_STAT_AREA]

#         # Calculate density of the component
#         mask = (labels == label).astype(np.uint8)
#         density = cv2.countNonZero(mask[y:y+h, x:x+w]) / (w * h)

#         # Filter components based on various criteria
#         if (area > min_area and
#             area < (img_width * img_height * 0.1) and  # Not too large
#             w > 2 and h > 2 and  # Not too small
#             density > 0.2):  # Has enough content

#             text_regions.append((x, y, w, h))

#     # Sort regions by position
#     text_regions.sort(key=lambda r: (r[1] // 15, r[0]))

#     if debug_bottleneck:
#         print(f"Region extraction and filtering took {time.time() - start:.4f} seconds")

#     # Save debug images if requested
#     if debug and debug_dir:

#         # Time debug image saving
#         start = time.time() if debug_bottleneck else 0

#         os.makedirs(debug_dir, exist_ok=True)

#         # Save intermediate processing steps
#         cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
#         cv2.imwrite(os.path.join(debug_dir, "2_blurred.png"), blurred)
#         cv2.imwrite(os.path.join(debug_dir, "3_threshold.png"), thresh)

#         # Create a color-coded visualization of components
#         label_hue = np.uint8(179 * labels / np.max(labels))
#         blank_ch = 255 * np.ones_like(label_hue)
#         labeled_img = cv2.merge([label_hue, blank_ch, blank_ch])
#         labeled_img = cv2.cvtColor(labeled_img, cv2.COLOR_HSV2BGR)
#         labeled_img[label_hue == 0] = 0
#         cv2.imwrite(os.path.join(debug_dir, "4_components.png"), labeled_img)

#         # Draw bounding boxes for debug
#         debug_img = img.copy()
#         for x, y, w, h in text_regions:
#             cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 1)
#         cv2.imwrite(os.path.join(debug_dir, "5_detected_regions.png"), debug_img)

#         if debug_bottleneck:
#             print(f"Debug image saving took {time.time() - start:.4f} seconds")

#     if debug_bottleneck:
#         print(f"Total processing time: {time.time() - start_total:.4f} seconds")

#     return text_regions

