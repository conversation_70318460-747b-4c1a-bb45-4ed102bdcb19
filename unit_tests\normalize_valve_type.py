import pandas as pd
import re

'''
Normalize valve types for items classified as 'Valves' in rfq_scope.
This will set the 'general_category' based on end type patterns:
- Anything that maps to 'TE' -> 'Threaded Valve'
- Anything that maps to 'FLG' -> 'Flanged Valve'  
- All others -> 'Welded Valve'
- If none are specified -> leave blank (requires human review)
'''

# Use the same end_chart as normalize_end_types.py
end_chart = [
    ("BW", "BE"),
    ("BE", "BE"),
    ("WELDOLET", "BE"),
    ("BEVELED END", "BE"),
    ("BEVELED", "BE"),
    ("BEVELLED END", "BE"),
    ("BEVELLED", "BE"),
    ("BVLD", "BE"),
    ("BV", "BE"),
    ("BVL", "BE"),
    ("BLE", "BE"),
    ("BOE", "BE"),
    ("BSE", "BE"),
    ("WE", "BE"),
    ("BUTT WELD END", "BE"),
    ("BBE", "BE X BE"),
    ("BLD", "BL"),
    ("BLIND STUB END", "BL"),
    ("BLIND END", "BL"),
    ("FNPT", "TE"),
    ("FE", "FLG"),
    ("FLGD", "FLG"),
    ("FLG'D", "FLG"),
    ("MFE", "FLG"),
    ("FFE", "FLG"),
    ("FPT", "TE"),
    ("GE", "GR"),
    ("GROOVED END", "GR"),
    ("GROOVED", "GR"),
    ("MNPT", "TE"),
    ("MPT", "TE"),
    ("PE", "PE"),
    ("PSE", "PE"),
    ("PLE", "PE"),
    ("POE", "PE"),
    ("PLAIN END", "PE"),
    ("PLAIN", "PE"),
    ("PBE", "PE X PE"),
    ("SW", "SW"),
    ("SWE", "SW X SW"),
    ("SOCKOLET", "SW"),
    ("SOC", "SW"),
    ("SOL", "SW"),
    ("SOCKET WELD", "SW"),
    ("SOCKET WELDED", "SW"),
    ("SOCKET WELD END", "SW"),
    ("SOCKET WELDED", "SW"),
    ("TOE", "TE"),
    ("TSE", "TE"),
    ("TE", "TE"),
    ("THD", "TE"),
    ("THRD", "TE"),
    ("NPT", "TE"),
    ("THREADOLET", "TE"),
    ("THREDOLET", "TE"),
    ("FEM", "TE"),
    ("SCRD", "TE"),
    ("SCR'D", "TE"),
    ("SCREWED", "TE"),
    ("SCREW", "TE"),
    ("SE", "TE"),
    ("THR'D", "TE"),
    ("TLE", "TE"),
    ("TOL", "TE"),
    ("ME", "TE"),
    ("MTE", "TE"),
    ("FTE", "TE"),
    ("THREAD", "TE"),
    ("TH", "TE"),
    ("THDF", "TE"),
    ("THREDED", "TE"),
    ("THREADED", "TE"),
    ("THDM", "TE"),
    ("THREAD END", "TE"),
    ("TH END", "TE"),
    ("THREDED END", "TE"),
    ("THREADED END", "TE"),
    ("TF", "TE"),
    ("TBE", "TE X TE"),
    ("M&F", "TE X TE"),
    ("T&C", "TE X CE"),
    ("IRE", "BE"),
    ("RF", "FLG"),
    ("RFFE", "FLG"),
    ("FF", "FLG"),
    ("RFWN", "FLG"),
    ("FFWN", "FLG"),
    ("Raised Face", "FLG"),
    ("Flanged", "FLG"),
    ("Flanges", "FLG"),
    ("Flange", "FLG"),
    ("Flat Face", "FLG")
]

# Normalize keys for lookup
end_lookup = {}
for val, decoded in end_chart:
    norm_key = re.sub(r"[\s'\.\-]+", "", val.upper())
    end_lookup[norm_key] = decoded

def try_match_end_patterns(token):
    """
    Try to match a single token (already uppercase, punctuation removed) 
    to known end patterns or find sub-patterns within it.
    """
    # Direct match?
    if token in end_lookup:
        return [end_lookup[token]]
    
    # Try to segment it into known patterns
    results = []
    def backtrack(remaining, current):
        if not remaining:
            results.append(current[:])
            return
        for i in range(1, len(remaining)+1):
            part = remaining[:i]
            if part in end_lookup:
                current.append(end_lookup[part])
                backtrack(remaining[i:], current)
                current.pop()

    backtrack(token, [])
    if results:
        # Return the first successful segmentation
        return results[0]

    # No match found
    return []

def find_end_types(description):
    """
    Extract end types from the material description and return a list of normalized end types.
    """
    # Uppercase and remove some punctuations
    desc = description.upper()
    cleaned = re.sub(r"[\,\:\;\(\)\[\]\"]", " ", desc)
    # Insert spaces around X and replace hyphens with ' X ' to treat them as separators
    cleaned = re.sub(r"X", " X ", cleaned)
    cleaned = re.sub(r"[-]", " X ", cleaned)
    # Split by whitespace
    tokens = cleaned.split()

    decoded_ends = []
    for t in tokens:
        # Normalize token
        norm_t = re.sub(r"[\s'\.\-]+", "", t)
        if not norm_t:
            continue  # Skip empty tokens
        # Attempt to match known patterns
        matches = try_match_end_patterns(norm_t)
        # If we got any matches, add them to decoded_ends
        if matches:
            decoded_ends.extend(matches)

    return decoded_ends

def determine_valve_type(description, rfq_scope):
    """
    Determine the valve type based on end types found in the description.
    Only applies if rfq_scope is 'Valves'.
    
    Returns:
        str: 'Threaded Valve', 'Flanged Valve', 'Welded Valve', or '' (blank for human review)
    """
    if pd.isnull(description) or pd.isnull(rfq_scope):
        return ""
    
    # Only process if rfq_scope is 'Valves'
    if str(rfq_scope).strip().lower() != 'valves':
        return ""
    
    end_types = find_end_types(description)
    
    if not end_types:
        # No end types found - requires human review
        return ""
    
    # Check for threaded connections (TE)
    if 'TE' in end_types:
        return 'Threaded Valve'
    
    # Check for flanged connections (FLG)
    if 'FLG' in end_types:
        return 'Flanged Valve'
    
    # All other end types default to welded valve
    if end_types:
        return 'Welded Valve'
    
    # Fallback - no end types found
    return ""

def normalize_valve_types_dataframe(df):
    """
    Process a DataFrame to normalize valve types.
    Expects columns: 'material_description' and 'rfq_scope'
    Adds/updates column: 'general_category'
    
    Args:
        df (pd.DataFrame): DataFrame with material descriptions and rfq_scope
        
    Returns:
        pd.DataFrame: DataFrame with normalized valve types in general_category column
    """
    if 'material_description' not in df.columns:
        raise ValueError("DataFrame must contain 'material_description' column")
    
    if 'rfq_scope' not in df.columns:
        raise ValueError("DataFrame must contain 'rfq_scope' column")
    
    # Create a copy to avoid modifying the original
    result_df = df.copy()
    
    # Initialize general_category column if it doesn't exist
    if 'general_category' not in result_df.columns:
        result_df['general_category'] = ""
    
    # Apply valve type normalization only to rows where rfq_scope is 'Valves'
    valve_mask = result_df['rfq_scope'].str.lower().str.strip() == 'valves'

    for idx in result_df[valve_mask].index:
        description = result_df.loc[idx, 'material_description']
        rfq_scope = result_df.loc[idx, 'rfq_scope']
        valve_type = determine_valve_type(description, rfq_scope)

        # Always update the general_category for valves - either with determined type or blank
        result_df.loc[idx, 'general_category'] = valve_type
    
    return result_df

def main():
    """
    Main function for standalone execution.
    Processes a test file and saves the results.
    """
    # Define file paths
    input_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Combined Classification\BRS_12_13_14_15\RFQ_With_Data_20250627_134757.xlsx"
    output_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Combined Classification\BRS_12_13_14_15\RFQ - normalized valves.xlsx"
    
    try:
        # Read the Excel file
        df = pd.read_excel(input_file_path)
        print(f"Successfully loaded {len(df)} rows from {input_file_path}")
    except FileNotFoundError:
        print(f"Input file not found at {input_file_path}. Please check the path.")
        print("Creating a sample test file...")
        
        # Create sample data for testing
        sample_data = {
            'material_description': [
                '2" BALL VALVE 800# A105 SW',
                '4" GATE VALVE 150# WCB FLANGED RF',
                '1" GLOBE VALVE 300# A105 THREADED NPT',
                '6" BUTTERFLY VALVE 150# CI FLANGED',
                '1/2" NEEDLE VALVE 3000# SS316 SW',
                '3" CHECK VALVE 300# WCB RF FLANGED',
                '2" BALL VALVE 600# A105 BW',
                '1" RELIEF VALVE 150# BRONZE THREADED'
            ],
            'rfq_scope': ['Valves'] * 8
        }
        
        df = pd.DataFrame(sample_data)
        print("Created sample test data with 8 valve descriptions")
    except Exception as e:
        print(f"An error occurred while reading the input file: {e}")
        return

    # Check required columns
    required_columns = ['material_description', 'rfq_scope']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"The input file is missing required columns: {missing_columns}")
        return

    print(f"Processing {len(df)} rows...")
    
    # Apply valve type normalization
    try:
        result_df = normalize_valve_types_dataframe(df)
        
        # Show results
        valve_rows = result_df[result_df['rfq_scope'].str.lower().str.strip() == 'valves']
        print(f"\nProcessed {len(valve_rows)} valve items:")
        
        for idx, row in valve_rows.iterrows():
            description = row['material_description']
            valve_type = row['general_category']
            print(f"  {description[:50]}... -> {valve_type}")
        
        # Save results
        result_df.to_excel(output_file_path, index=False)
        print(f"\nResults saved to {output_file_path}")
        
        # Summary statistics
        valve_type_counts = valve_rows['general_category'].value_counts()
        print(f"\nSummary:")
        for valve_type, count in valve_type_counts.items():
            print(f"  {valve_type}: {count}")
        
        blank_count = len(valve_rows[valve_rows['general_category'] == ''])
        if blank_count > 0:
            print(f"  Blank (requires review): {blank_count}")
            
    except Exception as e:
        print(f"An error occurred while processing the data: {e}")

if __name__ == "__main__":
    main()
