"""
Example of a QPushButton with progress and busy indicators using PySide6.
Features:
1. Busy spinner animation
2. Progress bar mode
3. Alert states (success, warning, error)
4. Text updates while processing
"""
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *

class BusyButton(QPushButton):
    """Custom button that can show busy state and progress"""

    # Constants for styling
    PROGRESS_HEIGHT = 4
    SPINNER_SIZE = 16
    ALERT_TIMEOUT = 3000  # ms

    # Al<PERSON> types
    ALERT_NONE = 0
    ALERT_SUCCESS = 1
    ALERT_WARNING = 2
    ALERT_ERROR = 3

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)

        # State
        self._busy = False
        self._progress = -1  # -1 means indeterminate/busy mode
        self._alert_type = self.ALERT_NONE
        self._alert_text = ""
        self._spinner_angle = 0
        self._default_text = text

        # Styling
        self.setMinimumHeight(32)

        # Timer for spinner animation
        self._spinner_timer = QTimer(self)
        self._spinner_timer.timeout.connect(self._update_spinner)
        self._spinner_timer.setInterval(50)

        # Timer for alert auto-clear
        self._alert_timer = QTimer(self)
        self._alert_timer.timeout.connect(self.clear_alert)
        self._alert_timer.setSingleShot(True)

        # Colors
        self._color_map = {
            self.ALERT_SUCCESS: QColor("#4CAF50"),
            self.ALERT_WARNING: QColor("#FFC107"),
            self.ALERT_ERROR: QColor("#F44336"),
            self.ALERT_NONE: QColor("#2196F3")
        }

    def _update_spinner(self):
        """Update spinner animation angle"""
        self._spinner_angle = (self._spinner_angle + 30) % 360
        self._progress += 5
        if self._progress > 100:
            self._progress = 0
        self.update()

    def paintEvent(self, event):
        """Custom paint event to draw progress bar and spinner"""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw progress bar or busy indicator
        if self._busy or self._progress >= 0:
            # color = self._color_map[self._alert_type]
            color = QColor("#F7941D")
            # color.setAlpha(0.3)
            painter.setPen(Qt.NoPen)
            painter.setBrush(QBrush(color))

            # Progress bar mode
            if self._progress >= 0:
                width = int(self.width() * (self._progress / 100.0))
                painter.drawRect(0, self.height() - self.PROGRESS_HEIGHT,
                               width, self.PROGRESS_HEIGHT)

            # Busy spinner mode
            elif self._busy:
                painter.save()
                painter.translate(self.width() - self.SPINNER_SIZE - 8,
                                self.height() // 2)
                painter.rotate(self._spinner_angle)

                for i in range(8):
                    painter.save()
                    painter.rotate(i * 45)
                    alpha = 255 - (((self._spinner_angle + i * 45) % 360) / 360 * 200)
                    color.setAlpha(int(alpha))
                    painter.setBrush(QBrush(color))
                    painter.drawRect(-1, -self.SPINNER_SIZE//2, 2, 4)
                    painter.restore()

                painter.restore()

    def setBusy(self, busy=True):
        """Set busy state with spinner animation"""
        if busy == self._busy:
            return

        self._busy = busy
        self._progress = -1

        if busy:
            self._spinner_timer.start()
        else:
            self._spinner_timer.stop()
            self.setText(self._default_text)

        self.update()

    def setProgress(self, value=0):
        """Set progress value (0-100)"""
        if not 0 <= value <= 100:
            return

        if value >= 100:
            value = 0

        if self._spinner_timer.isActive():
            return
        self._progress = value
        self._busy = True
        self._spinner_timer.start()

    def stopProgress(self):
        self._progress = 0
        self._busy = False
        self._spinner_timer.stop()
        self.update()


    def set_alert(self, text, alert_type=ALERT_WARNING, timeout=True):
        """Show alert message with optional timeout"""
        self._alert_text = text
        self._alert_type = alert_type
        self.setText(text)

        if timeout:
            self._alert_timer.start(self.ALERT_TIMEOUT)

        self.update()

    def clear_alert(self):
        """Clear alert state"""
        self._alert_type = self.ALERT_NONE
        self._alert_text = ""
        self.setText(self._default_text)
        self.update()

class DemoWindow(QMainWindow):
    """Demo window to showcase BusyButton functionality"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Busy Button Demo")

        # Create central widget and layout
        central = QWidget()
        self.setCentralWidget(central)
        layout = QVBoxLayout(central)

        # Create buttons for different states
        self.busy_btn = BusyButton(text="Click to Start")
        self.progress_btn = BusyButton(text="Show Progress")
        self.alert_btn = BusyButton(text="Show Alerts")

        # Add buttons to layout
        layout.addWidget(self.busy_btn)
        layout.addWidget(self.progress_btn)
        layout.addWidget(self.alert_btn)

        # Connect button clicks
        self.busy_btn.clicked.connect(self._toggle_busy)
        self.progress_btn.clicked.connect(self._start_progress)
        self.alert_btn.clicked.connect(self._show_alerts)

        # Timer for progress demo
        self._progress = 0
        self._progress_timer = QTimer(self)
        self._progress_timer.timeout.connect(self._update_progress)

        # Timer for alert demo
        self._alert_state = 0
        self._alert_timer = QTimer(self)
        self._alert_timer.timeout.connect(self._update_alert)

        self.setMinimumWidth(300)

    def _toggle_busy(self):
        """Toggle busy state with text update"""
        if not self.busy_btn._busy:
            self.busy_btn.setBusy(True)
            self.busy_btn.setText("Processing...")
        else:
            self.busy_btn.set_busy(False)

    def _start_progress(self):
        """Start progress animation"""
        self._progress = 0
        self.progress_btn.setProgress(0)
        # self._progress_timer.start(50)

    def _update_progress(self):
        """Update progress value"""
        self._progress += 1
        self.progress_btn.set_progress(self._progress)


    def _show_alerts(self):
        """Demonstrate different alert states"""
        self._alert_state = 0
        self._update_alert()
        self._alert_timer.start(1500)

    def _update_alert(self):
        """Cycle through alert states"""
        if self._alert_state == 0:
            self.alert_btn.set_alert("Success!", BusyButton.ALERT_SUCCESS, timeout=False)
        elif self._alert_state == 1:
            self.alert_btn.set_alert("Warning!", BusyButton.ALERT_WARNING, timeout=False)
        elif self._alert_state == 2:
            self.alert_btn.set_alert("Error!", BusyButton.ALERT_ERROR, timeout=False)
        else:
            self._alert_timer.stop()
            self.alert_btn.clear_alert()
            return

        self._alert_state += 1

if __name__ == "__main__":
    app = QApplication([])
    window = DemoWindow()
    window.show()
    app.exec()
