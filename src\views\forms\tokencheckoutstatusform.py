"""
Allow user to select PDF from file explorer for new projectg
"""
from PySide6.QtGui import QHideEvent
from PySide6.QtWidgets import  QVBoxLayout, QLabel
from PySide6.QtCore import Qt, Signal, QUrl, QTimer
from PySide6.QtGui import QMovie
from .baseform import BaseForm
from pubsub import pub
from src.app_paths import resource_path
from src.pyside_util import get_resource_pixmap
from src.views.forms.tokencheckoutwindow import TokenCheckoutWindow

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 960

class TokenCheckoutStatusForm(BaseForm):

    sgnFilePicked = Signal(str)
    sgnFilePickedExisting = Signal(str)  # Add to existing project
    sgnOpenCheckoutWindow = Signal()

    def __init__(self, parent):
        super().__init__(parent)
        self.success: bool = False
        self.checkoutWindow = None
        self.sgnOpenCheckoutWindow.connect(self.onOpenCheckoutWindow)
    
    def initUi(self):
        self.formSize.setWidth(720)
        self.formSize.setHeight(480)

        self.title.setText("")
        self.subtitle.setText("Checkout")
        self.subtitle.setObjectName("titleLabel")
        self.subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.setLayout(QVBoxLayout())
        self.addStretchSpacer()
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        self.movie.start()
        self.lblMovie = QLabel()
        self.lblMovie.setMovie(self.movie)
        self.lblMovie.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.lblMovie)

        self.lblIcon = QLabel()
        self.lblIcon.setPixmap(get_resource_pixmap("check.svg"))
        self.lblIcon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.lblIcon)
        self.lblIcon.hide()

        self.addVSpace(32)
        self.status = QLabel()
        self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.status)

        self.addStretchSpacer()

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        pub.subscribe(self.onTokenCheckoutResponse, "request-tokens-checkout-response")

        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.onCheckoutClose)

    def updateUi(self):
        return

    def initDefaults(self):
        self.lblMovie.show()
        self.lblIcon.hide()
        self.status.setText("")

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onFloatingButton(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")
    
    def onNext(self):
        if self.filename:
            self.sgnFilePicked.emit(self.filename)
        
    def onNextExisting(self):    
        self.sgnFilePickedExisting.emit(self.filename)
    
    def setParams(self, params):
        self.tokens = int(params["tokens"])
        self.status.setText("Requesting checkout...")
        pub.sendMessage("request-tokens-checkout", quantity=self.tokens)

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)

    def onOpenCheckoutWindow(self):
        if self.url:
            self.checkoutWindow = TokenCheckoutWindow(None, self.url)
            self.checkoutWindow.closeEvent = self.checkoutWindowCloseEvent
            self.checkoutWindow.setWindowTitle("ATEM Tokens Checkout")
            self.checkoutWindow.view.urlChanged.connect(self.urlChanged)
            self.checkoutWindow.show()
            self.checkoutWindow.raise_()

    def onTokenCheckoutResponse(self, data):
        self.url = data.get("data", {}).get("url")
        self.status.setText("Awaiting checkout...")
        self.sgnOpenCheckoutWindow.emit()
    
    def checkoutWindowCloseEvent(self, event):
        self.timer.start(1000)
        self.checkoutWindow = None

    def onCheckoutClose(self):
        self.checkoutWindow = None
        self.initDefaults()
        # Redirect to other view
        if self.isVisible():
            if self.success:
                pub.sendMessage("tokens-get")
                pub.sendMessage("goto-workspace-view", name=None)
            else:
                pub.sendMessage("goto-form", name="TokenPaymentForm")

    def urlChanged(self, url: QUrl):
        if "atem-payment-success" in url.toString():
            self.status.setText("Payment Success! Redirecting...")
            self.lblMovie.hide()
            self.lblIcon.show()
            self.success = True
        else:
            self.status.setText("Checkout canceled")
            self.success = False

        try:
            self.checkoutWindow.close()
        except:
            pass
        self.checkoutWindow = None
        self.timer.start(1000)
