'''
Worker

# PyMuPDF examples

https://artifex.com/blog/text-extraction-with-pymupdf
https://artifex.com/blog/table-recognition-extraction-from-pdfs-pymupdf-python
https://pymupdf.readthedocs.io/en/latest/recipes-text.html#how-to-extract-table-content-from-documents
https://pymupdf.readthedocs.io/en/latest/page.html#Page.find_tables
'''
import enum
import random
import time
import uuid
import threading
from src.utils.logger import logger
import platform
import io
import sys
import os
import re
import json
import argparse
import fitz
import multiprocessing
import unicodedata

import numpy as np
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font
from pprint import pprint as pp
from PySide6.QtWidgets import QApplication, QMessageBox
from pubsub import pub
from collections import defaultdict
from multiprocessing import Lock
from functools import partial

from src.atom.vision.ocr_patch_multi import main
from unit_tests.get_tables_test import get_table_data
from src.atom.fast_storage import save_df_fast, load_df_fast
from src.utils import convert_roi_payload

from __features__ import ROI_EXTRACTION_MULTIPROCESS

# ROI_EXTRACTION_MULTIPROCESS = ROI_EXTRACTION_MULTIPROCESS #and __file__.endswith(".pyc")

from src.atom.ai_processing import Gpt4Turbo, analyze_bom_data
from src.atom.dbManager import DatabaseManager


def _getDataTempPath(filename):
    return filename

def _savePagePixmap(page):
    return page


try:
    from src.app_paths import savePagePixmap
    from src.app_paths import getDataTempPath
except:
    # Testing, if cannot access app_paths, just return the filename
    getDataTempPath = _getDataTempPath
    savePagePixmap = _savePagePixmap

# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)


# Increase the maximum number of columns displayed
pd.set_option('display.max_columns', None)

# Set to limit the number of workers/processes for debugging
IS_TESTING = False
OVERRIDE_EXTRACT_PAGES: list = None # [1,2,44]  # Use 1-indexed here e.g. [1] for page 1
OVERRIDE_PAGE_LIMIT = 1
DEBUG_MODE = False # Exports data to xlsx
TEST_TRANSFORM = 0

BOM_IMPORT = False # Import excel BOM data
GENERAL_IMPORT = False # Import excel general data
COMMIT_PDF = True

INSPECT_RAW = False # Export the raw data
RAW_IMPORT = False # <--- IMPORTS debug\raw_data_full_patched_ocr - Deprecated
COMMIT_RAW = True # Write raw_data to pyarrow parquet/feather (BE CAREFUL SETTING THIS TO TRUE AS IT WILL OVERWRITE YOUR RAW DATA
PATCH_RAW_DATA_FILE = r"debug\raw_data_full_patched_ocr" #r"C:\Users\<USER>\source\repos\Architekt ATOM\debug\raw_data_full_patched_ocr.xlsx"

ENABLE_OCR = True

PRINT_CONVERTED_COORDS = True # Print coordinates
EXPORT_OUTLIER = False

check_debug_log = {} # Test For debugging Check_update_row function (Line 1620 ish) 


def extract_coordinates(record):
    if not record:
        return None
    record = record.replace("(", "")
    record = record.replace(")", "")
    coordinates = tuple(map(float, record.split(', ')))
    return coordinates

raw_df_patch = None

if RAW_IMPORT:
    print("loading and parsing raw import, may take some time")

    # raw_df_patch = pd.read_excel(PATCH_RAW_DATA_FILE)
    raw_df_patch = load_df_fast(f"{PATCH_RAW_DATA_FILE}.feather")

    raw_df_patch["coordinates"].apply(extract_coordinates)
    raw_df_patch["coordinates2"].apply(extract_coordinates)

#############################################
#############################################
# Temporarily set the logger level to INFO
# #logger.setLevel(logging.INFO)

# # Create a console handler (if you haven't already)
# console_handler = logging.StreamHandler()

# # Optionally, set a formatter
# formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
# console_handler.setFormatter(formatter)

# # Add the handler to the logger
# logger.addHandler(console_handler)

#############################################
#############################################


# Check if the operating system is Windows
if platform.system() == 'Windows':
    # Import launch_window only if on Windows
    logger.debug("Windows OS Detected. --Importing launch_window.py")
    try:
        from src.atom.dynamic_rename import launch_window
    except:
        from dynamic_rename import launch_window  # Older import

#PyMuPDF examples
'''
#https://artifex.com/blog/text-extraction-with-pymupdf
#https://artifex.com/blog/table-recognition-extraction-from-pdfs-pymupdf-python
#https://pymupdf.readthedocs.io/en/latest/recipes-text.html#how-to-extract-table-content-from-documents
https://pymupdf.readthedocs.io/en/latest/page.html#Page.find_tables
'''

def check_bbox_overlap(bbox1, bbox2):
    """Check if two bounding boxes overlap"""
    x0_1, y0_1, x1_1, y1_1 = bbox1
    x0_2, y0_2, x1_2, y1_2 = bbox2
    
    # Check if one rectangle is to the left of the other
    if x1_1 < x0_2 or x1_2 < x0_1:
        return False
    
    # Check if one rectangle is above the other
    if y1_1 < y0_2 or y1_2 < y0_1:
        return False
    
    return True


class DataFrameHolder:
    def __init__(self):
        self.modified_df = None
        self.callback_received = threading.Event()

    def handle_modified_dataframe(self, df):
        self.modified_df = df
        logger.debug("Modified DataFrame received:")
        self.callback_received.set()  # Signal that the callback has been executed

    def wait_for_callback(self):
        self.callback_received.wait()  # Wait for the callback to be executed

def generate_unique_id(existing_ids, size=6):
    while True:
        new_id = random.randint(10**(size-1), (10**size)-1)
        if new_id not in existing_ids:
            return new_id
 
 #Used when importing General from xlsx
def merge_general_data(from_df, imported_df, columns_to_merge):
    """
    Merges specified columns from from_df into imported_df based on 'pdf_page',
    maintaining the row count of imported_df and generating unique ids for missing values.
    """
    # Ensure 'pdf_page' is of integer type
    from_df['pdf_page'] = pd.to_numeric(from_df['pdf_page'], errors='coerce').fillna(0).astype(int)
    imported_df['pdf_page'] = pd.to_numeric(imported_df['pdf_page'], errors='coerce').fillna(0).astype(int)
    
    # Replace 'nan', np.nan, and pd.NA with empty strings in both dataframes
    from_df = from_df.replace(['nan', np.nan, pd.NA], '')
    imported_df = imported_df.replace(['nan', np.nan, pd.NA], '')
    
    # Create a dataframe with only the columns to merge
    merge_df = from_df[['pdf_page'] + columns_to_merge].drop_duplicates('pdf_page')
    
    # Perform the merge operation
    merged_df = imported_df.merge(merge_df, on='pdf_page', how='left')
    
    # Fill NaN values with empty strings
    merged_df = merged_df.fillna('')
    
    # Ensure the data types are correct
    for col in columns_to_merge:
        if from_df[col].dtype == 'int64' or from_df[col].dtype == 'float64':
            merged_df[col] = pd.to_numeric(merged_df[col], errors='coerce').fillna(0).astype(int)
        else:
            merged_df[col] = merged_df[col].astype(from_df[col].dtype)
    
    # Get the highest pdf_id from from_df
    highest_pdf_id = from_df['pdf_id'].max()
    
    # Generate unique ids for missing pdf_id starting from highest_pdf_id + 1
    next_pdf_id = highest_pdf_id + 1
    for index, row in merged_df.iterrows():
        if row['pdf_id'] == 0 or row['pdf_id'] == '':
            merged_df.at[index, 'pdf_id'] = next_pdf_id
            next_pdf_id += 1
    
    return merged_df

def merge_bom_data(from_df, imported_df): # Used when importing BOM from xlsx
    '''
    Used to merge and imported BOM Dataframe (Created with Excel)
    This is necessary when performing operations outside of the app so that we can still run our Classifier
    '''
    
    db_manager = DatabaseManager() # Connect to database
    
    # Replace 'nan', np.nan, and pd.NA with empty strings in from_df
    from_df = from_df.replace(['nan', np.nan, pd.NA], '')
    
    # Perform the merge operation
    merged_df = pd.merge(imported_df, 
                         from_df[['pdf_page', 'sys_build', 'sys_path', 'pdf_id']], 
                         on='pdf_page', 
                         how='left')
    
    
    
    return merged_df

# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        #if non_standard in text:
            #print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text
        
def confirm_analysis():
    app = QApplication.instance()  # Check if there is already a running QApplication instance
    created_app = False
    if not app:  # If no instance exists, create a new one
        app = QApplication([])
        created_app = True
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle("Confirm Analysis")
    msg_box.setText("Do you want to run smart analysis on your extracted \"Bill of Materials\"?")
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)
    response = msg_box.exec()

    if created_app:
        app.exit()  # Properly terminate the app you created

    return response == QMessageBox.Yes

def parse_complex_size(value):
    """Parse complex size values that may include decimals followed by fractions. i.e '1.1/2' (1.5) """
    try:
        # Remove any non-numeric characters (except for decimal point, forward slash, and hyphen)
        value = re.sub(r'[^0-9./\-]', '', str(value))
        
        # Remove any leading or trailing "." or "," characters
        value = value.strip(".,")
        
        if '.' in value and '/' in value:
            # Handle cases where decimal and fraction are combined, e.g., "1.1/2"
            decimal_part, fraction_part = value.split('.')
            fraction_str = fraction_part.split('/')[0] + '/' + fraction_part.split('/')[1]
            fraction = convert_fraction_to_float(fraction_str)
            return float(decimal_part) + fraction
        else:
            # Handling cases without combined decimals
            parts = re.findall(r'(\d+/\d+|\d+\.\d+|\d+)', value)
            if parts:
                return sum(convert_fraction_to_float(part) for part in parts)
        return np.nan
    except Exception as e:
        logger.error(f"Failed to parse complex size. Value: '{value}'. Error: {e}", exc_info=True)
        return np.nan

def convert_fraction_to_float(fraction_str):
    try:
        if '/' in fraction_str:
            numerator, denominator = fraction_str.split('/')
            return float(numerator) / float(denominator)
        else:
            return float(fraction_str)
    except ValueError:
        return float(fraction_str)

def parse_elevation(elevation_str, pdf_id, convert_mm_to_feet=False, convert_m_to_feet=True):
    # Check if the string contains feet (') or inches (") symbols
    if "'" in elevation_str or '"' in elevation_str:
       # logger.debug(f"Elevation '{elevation_str}' is in standard format")
        # Handle standard format elevations
        # Check if the string does not start with "E" or "EL" (case-insensitive)
        if not re.match(r'^[EeZz][Ll]?', elevation_str):
            # logger.debug(f"Elevation string '{elevation_str}' does not start with 'E', 'EL', or 'Z'")
            return float('nan')  # Or any other value indicating non-processing
        
        try:
            # Remove 'E' or 'EL' prefix, any trailing double quotes, and the sign
            #elevation_str = re.sub(r'[Ee][Ll]?|\s*\"', '', elevation_str).strip()
            elevation_str = re.sub(r'[EeZz][Ll]?|\s*\"', '', elevation_str).strip()
            sign = -1 if elevation_str.startswith('-') else 1
            elevation_str = elevation_str.lstrip('+-')
        
            # Check if the elevation is given entirely in inches
            if "'" not in elevation_str:
                # logger.debug(f"Elevation '{elevation_str}' is given entirely in inches")
                if '/' in elevation_str:
                    if '.' in elevation_str:
                        whole_number, fraction = elevation_str.split('.')
                        numerator, denominator = map(int, fraction.split('/'))
                        inches = int(whole_number) + numerator / denominator
                    else:
                        numerator, denominator = map(int, elevation_str.split('/'))
                        inches = numerator / denominator
                else:
                    inches = float(elevation_str)
                decimal_feet = sign * inches / 12
                # logger.debug(f"Parsed elevation: {elevation_str} -> {decimal_feet:.2f} decimal feet")
                return decimal_feet
        
            # Split the string by the foot and inch separator ("'")
            parts = elevation_str.split("'")

            # Parse the feet and inch parts
            feet = int(parts[0]) if parts[0] else 0
            inches = 0
            fraction = 0

            # If there is an inches part, it might contain a whole number and a fractional part
            if len(parts) > 1:
                try: # Expects to find and split by a decimal
                    inch_parts = parts[1].split('.')
                    inches = int(inch_parts[0]) if inch_parts[0] else 0

                    # If there's a fractional part, convert it to a decimal
                    if len(inch_parts) > 1:
                        numerator, denominator = map(int, inch_parts[1].split('/'))
                        fraction = numerator / denominator
                        
                except ValueError:
                    # Fallback to whitespace handling if decimal parsing fails
                    inch_parts = parts[1].split()
                    inches = int(inch_parts[0]) if inch_parts[0] else 0
                    fraction = 0
                    if len(inch_parts) > 1:
                        fraction = parse_complex_size(inch_parts[1])    

            # Calculate total inches and convert to decimal feet
            total_inches = feet * 12 + inches + fraction
            # decimal_feet = total_inches / 12 
            decimal_feet = sign * total_inches / 12 # New Line 5/9
            # logger.debug(f"Parsed elevation: {elevation_str} -> {decimal_feet:.2f} decimal feet")
            return decimal_feet #sign * decimal_feet
        except Exception as e:
            # Handle any unexpected error during the parsing
            logger.warning(f"Error parsing elevation: PDF ID: {pdf_id} {elevation_str} - {e}", exc_info=True)
            return float('nan')  # Return NaN if there's an error
    else:
        # logger.debug(f"Elevation '{elevation_str}' is in metric format")
        try:
            # Remove 'E', 'EL', or 'Z' prefix and any trailing whitespace
            elevation_str = re.sub(r'[EeZz][Ll]?', '', elevation_str).strip()

            # Add check for 'nan'
            if elevation_str.lower() == 'nan':
                return float('nan')

            # Convert the elevation to millimeters
            # millimeters = int(elevation_str)

            # Convert the elevation to millimeters
            value  = float(elevation_str)  # <-- Changed to float()

            print(f"Parsed numeric value: {value}")

            if convert_mm_to_feet:
                # Convert millimeters to feet
                decimal_feet = value / 304.8
                return decimal_feet
          
            elif convert_m_to_feet:
                # Convert millimeters to feet
                decimal_feet = value * 0.328084
                # print(f"Converting from m to feet: {value} m = {decimal_feet} feet")
                return decimal_feet
            else:
                # print(f"No conversion performed, returning original value: {value}")
                # Return the value in millimeters
                return value
           
        except Exception as e:
            logger.error(f"Error parsing elevation (metric): PDF ID: {pdf_id} {elevation_str} - {e}", exc_info=True)
            return float('nan')  # Return NaN if there's an error
        
def calculate_elevation_metrics(row, pdf_id=0, convert_mm_to_feet=False, convert_m_to_feet=False):
    try:
        elevation_values = row['elevation']
    except Exception as e:
        logger.warning(f"'elevation' field not found in table {e}", exc_info=True)
        return 0, 0, 0
    try:
        
        elevation_strings = elevation_values.split(';') #row['elevation'].split(';')
        elevations = [parse_elevation(el.strip(), pdf_id, convert_mm_to_feet=convert_mm_to_feet, convert_m_to_feet=convert_m_to_feet) for el in elevation_strings if el.strip()]
    
        # logger.debug(f"Parsed elevations for page {pdf_id}: {elevations}")

        # Calculate min, max, and average elevations in decimal feet
        min_elevation = f"{min(elevations):.2f}" if elevations else '' #None
        max_elevation = f"{max(elevations):.2f}" if elevations else '' #None
        avg_elevation = f"{sum(elevations) / len(elevations):.2f}" if elevations else ''#None

        # logger.debug(f"Page {pdf_id} - Min: {min_elevation}, Max: {max_elevation}, Avg: {avg_elevation}")
    
        return min_elevation, max_elevation, avg_elevation
    except Exception as e:
        logger.error(f"Error in calculate_elevation_metrics. Elevation values: {elevation_values} -- {e}", exc_info=True)
        return 0, 0, 0
        
def trim_all_columns(df):
    """
    Trims leading and trailing whitespaces from all string values in all columns of the DataFrame.
    """
    trim_strings = lambda x: x.strip() if isinstance(x, str) else x
    return df.map(trim_strings) #return df.applymap(trim_strings)

# Function to trim all columns in a DataFrame
def trim_all_columns2(df):
    return df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

def remove_outliers_from_bom(bom_data, outlier_data):
    for outlier in outlier_data:
        # Extract identifiers from the outlier row
        outlier_text = outlier.get("Text", "")
        outlier_page = outlier.get("PdfPage", "")
        outlier_path = outlier.get("PdfPath", "")

        # Create a condition to match the relevant rows in the BoM data
        bom_data = [row for row in bom_data if not (
            row.get("PdfPage", "") == outlier_page and 
            row.get("PdfPath", "") == outlier_path and 
            (outlier_text in row.get("Text", "") or row.get("Text", "") in outlier_text)
        )]

    return bom_data

def get_correct_path(path):
    logger.debug("Checking OS")
    if os.name != 'nt':
        # Convert Windows-style paths to Linux-style paths
        # This assumes that the path starts with a Windows drive letter (e.g., C:/)
        path = re.sub(r'^[a-zA-Z]:', '', path)  # Remove drive letter
        path = path.replace('\\', '/')  # Replace backslashes with forward slashes
        path = '/app' + path  # Prefix with the base directory used in Docker
    return path

# Function to split the PDF into individual pages with unique temporary IDs
def split_pdf(pdf_path, output_dir, dataframe, page_limit=None):
    # logger.debug("Splitting PDF Pages")
    with fitz.open(pdf_path) as doc:
        max_page = range(len(doc)) if page_limit is None else range(min(page_limit, len(doc)))
        
        # Generate unique IDs only for the pages being processed
        unique_ids = [str(uuid.uuid4()) for _ in max_page]
        dataframe = dataframe.iloc[list(max_page)].copy()
        dataframe['sys_AISdocID'] = unique_ids

        for page_num in max_page:
            page = doc.load_page(page_num)
            page_pdf = fitz.open()
            
            page_pdf.insert_pdf(doc, from_page=page_num, to_page=page_num)
            
            # Use the unique ID from the dataframe for the filename
            unique_id = dataframe.iloc[page_num - max_page.start]['sys_AISdocID']
            filename = f"{unique_id}.pdf"
            page_pdf.save(os.path.join(output_dir, filename))
            page_pdf.close()
    
    return dataframe
            
def convert_tuple_to_string(row):
    return [str(item) if isinstance(item, tuple) else item for item in row]

def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None

def clean_row_values(row_values):
    return [remove_illegal_xml_characters(str(cell)) if isinstance(cell, str) else cell for cell in row_values]

def remove_illegal_xml_characters(input_str):
    # XML 1.0 legal characters:
    # - Any Unicode character, excluding the surrogate blocks, FFFE, and FFFF.
    # - The legal characters are Tab (0x09), LF (0x0A), CR (0x0D),
    #   and characters in the range 0x20 to 0xD7FF, 0xE000 to 0xFFFD, 0x10000 to 0x10FFFF.
    # This regex matches any character not in the ranges above.
    illegal_xml_chars_re = re.compile('[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]+')
    return illegal_xml_chars_re.sub('', input_str)

def export_large_data_to_excel(df, filename, directory):
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

    excel_filename = os.path.join(directory, filename)

    if len(df) > 0:
        logger.debug(f"Creating Workbook: {filename} in {directory} with {len(df)} rows")
        wb = Workbook()
        ws = wb.active

        # Adding header row (column names)
        ws.append(list(df.columns))

        ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
        hyperlink_font = Font(color="0000FF", underline="single")

        for index, row in df.iterrows():
            row_values = [str(value) if not is_scalar(value) else value for value in row]
            row_values_cleaned = clean_row_values(row_values)
            ws.append(row_values_cleaned)
            #ws.append(row_values)

            # Create a hyperlink for 'AIS_LINK'
            if ais_link_col_idx:
                ais_link_value = row['AIS_LINK']
                
                # Use the relative path directly
                if ais_link_value:
                    hyperlink = ais_link_value
                    cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                    cell.hyperlink = hyperlink
                    cell.font = hyperlink_font

        try:
            wb.save(excel_filename)
            logger.debug(f">>> Data exported and saved to: {excel_filename}")
        except PermissionError as e:
            logger.error(f"PermissionError: {e}")
            logger.error(f"Failed to write to {excel_filename}. The file might be open or locked.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
    else:
        logger.debug(f"The DataFrame for {filename} is empty.")
    
def load_json(file_path):
    try:
        with open(file_path, 'r') as file:
            logger.info("Loaded Json: %s", file_path)
            return json.load(file)
        
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {file_path}: {e}")
        return None  # or return an empty dict {}
    except FileNotFoundError as e:
        logger.error(f"File not found: {file_path}: {e}")
        return None  # or return an empty dict {}
    
def update_value_type(value, patterns):
    for category, category_patterns in patterns.items():
        for pattern in category_patterns:
            if re.match(pattern, value):
                return category, value
    return '', ''

def string_to_tuple(coords):
    if isinstance(coords, tuple):
        # It's already a tuple, return it as is
        return coords
    try:
        # Assuming the input is a string that needs to be converted to a tuple
        coords = coords.strip("() ")
        return tuple(map(float, coords.split(', ')))
    except (ValueError, SyntaxError):
        # Return a default value that indicates invalid coordinates
        return (0, 0, 0, 0)
    

def convert_coords_to_tuple(df): # Original
    try:
        # Convert the string representation of coordinates to actual tuples
        df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
        df['coordinates'] = df['coordinates'].apply(string_to_tuple)
        
        # Update 'coordinates2' if it's (0,0,0,0). Annotations are stored in 'coordinates'
        df.loc[df['coordinates2'] == (0, 0, 0, 0), 'coordinates2'] = df['coordinates']
    except Exception as e:
        logger.error(f"Error converting string to tuple: {e}", exc_info=True)
        return pd.DataFrame()
    return df

    
def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):

    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ROI payload: {e}")
        return []

    converted_payload = []
    
    for item in roi_payload:
        try:
            converted_item = {"columnName": item.get("columnName", "Unknown")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            if "headersSelected" in item:
                converted_item["headersSelected"] = item["headersSelected"]

            converted_payload.append(converted_item)
        except Exception as e:
            logger.error(f"Error processing item {item.get('columnName', 'Unknown')}: {e}")

    if PRINT_CONVERTED_COORDS:
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))
        
def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }

def calculate_overlap_area(box1, box2):
    # Calculate the overlapping area between two rectangles
    x_left = max(box1[0], box2[0])
    y_top = max(box1[1], box2[1])
    x_right = min(box1[2], box2[2])
    y_bottom = min(box1[3], box2[3])

    if x_right < x_left or y_bottom < y_top:
        return 0.0  # No overlap
    return (x_right - x_left) * (y_bottom - y_top)

def draw_coordinates_on_page(document, page_number, coordinates_list, sub_dir, x_offset=0, y_offset=0):
    try:
        page = document.load_page(page_number)
    
        rect = page.rect  # Get the page dimensions
        page_width, page_height = rect.width, rect.height  
        
    except Exception as e:
        logger.error(f"Failed to load page {page_number}: {e}")
        return None

    for coordinates in coordinates_list:
        if "tableCoordinates" in coordinates:
            # logger.debug("Drawing table coordinates")
            # Convert and draw table rectangle using page dimensions
            draw_rectangle(page, coordinates["tableCoordinates"], page_width, page_height, x_offset, y_offset)

            # Draw rectangles for each column in the table
            for column_dict in coordinates.get("tableColumns", []):
                for column_name, column_coords in column_dict.items():
                    # logger.debug(f"Drawing column: {column_name}")
                    # Convert and draw column rectangle using page dimensions
                    draw_rectangle(page, column_coords, page_width, page_height, x_offset, y_offset)
        else:
            # It's a single coordinate entry, not a table
            #logger.debug(f"Drawing coordinate: {coordinates.get('columnName')}")
            # Convert and draw single rectangle using page dimensions
            draw_rectangle(page, coordinates, page_width, page_height, x_offset, y_offset)

    # Save the page as an image for inspection
    try:
        image = page.get_pixmap()
        image_path = os.path.join(sub_dir, 'validate.png')
        image.save(image_path)
        logger.info(f"Image saved to {image_path}")
        return image_path
    except Exception as e:
        logger.error(f"Error saving the image: {e}")
        return None

def draw_rectangle(page, coordinates, page_width, page_height, x_offset=0, y_offset=0):
    try:
        # Scale the relative coordinates to the page dimensions
        x0 = (coordinates["relativeX0"] * page_width) + x_offset
        y0 = (coordinates["relativeY0"] * page_height) + y_offset
        x1 = (coordinates["relativeX1"] * page_width) + x_offset
        y1 = (coordinates["relativeY1"] * page_height) + y_offset

        rect = fitz.Rect(x0, y0, x1, y1)
        page.draw_rect(rect, color=(1, 0, 0), width=1.5)  # Draw red rectangle
        #logger.debug(f"Rectangle drawn with coordinates: {x0}, {y0}, {x1}, {y1}")
    except KeyError as e:
        logger.error(f"Missing coordinate key in {coordinates}: {e}")
    except Exception as e:
        logger.error(f"Error drawing rectangle: {e}")
              
# --------> Worker & Helper Functions
def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def adjust_bbox_90(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    
    logger.info(f"_worker rotation_matrix applied: {rotation_matrix}")

    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix
    
    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def dynamic_adjustment(page, bbox, rotation):
    """
    Dynamically adjusts a bounding box based on the page's rotation and orientation.

    Parameters:
    - page: The page object from PyMuPDF.
    - bbox: The original bounding box (x0, y0, x1, y1).

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    
    page_rect = page.rect
    derotation_needed = rotation in [90, 270]  # Common cases for landscape adjustments

    # Convert bbox to points
    p1 = fitz.Point(bbox[0], bbox[1])
    p2 = fitz.Point(bbox[2], bbox[3])

    if derotation_needed:
        logger.info(f"Derotation applied: {page}")
        # Apply derotation matrix for rotated documents
        p1 = p1 * page.derotation_matrix
        p2 = p2 * page.derotation_matrix
        # Swap x and y if necessary, based on rotation
        if rotation == 90:
            p1, p2 = fitz.Point(p1.y, page_rect.width - p1.x), fitz.Point(p2.y, page_rect.width - p2.x)
        elif rotation == 270:
            p1, p2 = fitz.Point(page_rect.height - p1.y, p1.x), fitz.Point(page_rect.height - p2.y, p2.x)

    # Ensure x0 < x1 and y0 < y1
    adjusted_bbox = (min(p1.x, p2.x), min(p1.y, p2.y), max(p1.x, p2.x), max(p1.y, p2.y))
    
    return adjusted_bbox

def get_structured_table(table_type, structured_tables):
    """
    Retrieves a specific table from the structured tables dictionary, 
    accounting for case-insensitivity and blank tables.

    Parameters:
    - table_type (str): The type of table to retrieve (e.g., 'BOM', 'SPEC', 'Spool').
    - structured_tables (dict): A dictionary of structured tables, 
                                where keys are table types and values are DataFrames.

    Returns:
    - pd.DataFrame: The DataFrame for the requested table type, or an empty DataFrame 
                    if the table does not exist or is blank.
    """
    # Convert the table_type to lowercase to ensure case-insensitive matching
    table_type = table_type.lower()

    # Search for the table in the structured tables dictionary
    for key, table_df in structured_tables.items():
        if key.lower() == table_type:
            # Check if the table DataFrame is not empty
            if not table_df.empty:
                return table_df
            else:
                # Return an empty DataFrame if the table exists but is blank
                #logger.info(f"The '{table_type}' table exists but is blank.")
                return pd.DataFrame()

    # Return an empty DataFrame if the table type was not found
    logger.info(f"No '{table_type}' table was found.")
    return pd.DataFrame()
        
def process_page_size(document: fitz.Document, 
                      page_number: int,
                      ref_width: float,
                      ref_height: float,
                      doc_tolerance: float,
                      skipped_pages: dict) -> bool:
    """Check if page size is valid
    
    Get document sizes. Skip pages for later processing. 
    Split the pages into batches later reselect ROIs

    Note - this function may become or is already obselete due to PDF grouping.
    So always return True
    """

    page_valid: bool = False
    x_offset, y_offset = (0, 0)

    page: fitz.Page = document.load_page(page_number)
    page_width, page_height = page.rect.width, page.rect.height
    
    width_diff = abs(page_width - ref_width)
    height_diff = abs(page_height - ref_height)

    # Check if differences are within the tolerance - Forced Valid
    if True or (width_diff <= doc_tolerance and height_diff <= doc_tolerance):
        page_valid = True
        # Adjust x_offset and y_offset based on the difference
        adjusted_x_offset = x_offset + (page_width - ref_width) / 2
        adjusted_y_offset = y_offset + (page_height - ref_height) / 2

        # Proceed with processing using the adjusted offsets
        # Make sure to use adjusted_x_offset and adjusted_y_offset for processing this page
        logger.info(f"Adjusting for page size within tolerance for page {page_number + 1}. Adjusted:{adjusted_x_offset}, {adjusted_y_offset}")
    else:
        # If the page size difference is beyond the tolerance, log and skip this page
        logger.info(f"Skipping page {page_number + 1} due to size mismatch ({page_width}x{page_height}). REF SIZE: {ref_width}x{ref_height}. OUT OF TOLERANCE")
        skipped_pages[page_number + 1] = (page_width, page_height)
        # If commit_pdf is False, skip the PDF insertion and processing
        logger.info(f"Skipping PDF insertion and processing for page {page_number + 1}")
        page_valid = False

    return page_valid, page

def clean_for_json(data):
    if isinstance(data, dict):
        return {key: clean_for_json(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [clean_for_json(item) for item in data]
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        return str(data)  # Convert any other types to string

# Function to replace non-ASCII characters using normalization
def replace_non_ascii(text):
    # Normalize the text to NFKD (Normalization Form KD)
    normalized_text = unicodedata.normalize('NFKD', text)
    # Encode to ASCII and ignore non-ASCII characters
    ascii_text = normalized_text.encode('ascii', 'ignore').decode('ascii')
    return ascii_text

def get_page_text(page, rotation_matrix):
    try:
        text_blocks = page.get_text("rawdict", flags=fitz.TEXTFLAGS_TEXT, sort=True)["blocks"]
        
        original_text_blocks_clean = clean_for_json(text_blocks)
        
        for block in text_blocks:
            if block["type"] == 0:  # Text block
                block['bbox'] = adjust_bbox(block['bbox'], rotation_matrix)
                
                for line in block["lines"]:
                    line['bbox'] = adjust_bbox(line['bbox'], rotation_matrix)
                    
                    for span in line["spans"]:
                        span['bbox'] = adjust_bbox(span['bbox'], rotation_matrix)

                        origin_x, origin_y = span['origin']
                        origin_point = fitz.Point(origin_x, origin_y) * rotation_matrix
                        span['origin'] = (origin_point.x, origin_point.y)

                        # Process individual characters to form words
                        words = []
                        current_word = ""
                        current_word_bbox = None
                        
                        for char in span['chars']:
                            char['bbox'] = adjust_bbox(char['bbox'], rotation_matrix)
                            
                            if not current_word_bbox:
                                current_word_bbox = list(char['bbox'])
                            
                            if char['c'] == " " or char['c'] == "\n":
                                if current_word:
                                    words.append({
                                        'text': current_word,
                                        'bbox': current_word_bbox,
                                        'origin': (current_word_bbox[0], current_word_bbox[1])
                                    })
                                    current_word = ""
                                    current_word_bbox = None
                            else:
                                current_word += char['c']
                                current_word_bbox[2] = max(current_word_bbox[2], char['bbox'][2])
                                current_word_bbox[3] = max(current_word_bbox[3], char['bbox'][3])
                        
                        # Add the last word if it exists
                        if current_word:
                            words.append({
                                'text': current_word,
                                'bbox': current_word_bbox,
                                'origin': (current_word_bbox[0], current_word_bbox[1])
                            })
                        
                        span['words'] = words
                        span['text'] = replace_non_ascii(" ".join(word['text'] for word in words))

        if IS_TESTING:
            with open("original_text_blocks.json", "w") as orig_file:
                json.dump(original_text_blocks_clean, orig_file, indent=4)

        return text_blocks

    except Exception as e:
        logger.error(f"Error getting page text: {e}", exc_info=True)
        return {}

# def get_page_text(page, rotation_matrix): 
#     # rotation = page.rotation
#     # rotation_matrix = page.rotation_matrix  # Get the page's rotation matrix

#     try:
#         text_blocks = page.get_text("dict")["blocks"]
        
#         # print("\n\nTEXT BLOCKS ORIGINAL: \n")
#         # pp(text_blocks)
        
#         original_text_blocks_clean = clean_for_json(text_blocks)
        
#         for block in text_blocks:
#             if block["type"] == 0:  # Text block
#                 # Adjust the bounding box of the entire block
#                 block['bbox'] = adjust_bbox(block['bbox'], rotation_matrix)
                
#                 for line in block["lines"]:
#                     # Adjust the bounding box of the entire line
#                     line['bbox'] = adjust_bbox(line['bbox'], rotation_matrix)
                    
#                     for span in line["spans"]:
#                         # Adjust the bounding box of each span
#                         span['bbox'] = adjust_bbox(span['bbox'], rotation_matrix)

#                         # If needed, also adjust other coordinate-related attributes like 'origin'
#                         origin_x, origin_y = span['origin']
#                         origin_point = fitz.Point(origin_x, origin_y) * rotation_matrix
#                         span['origin'] = (origin_point.x, origin_point.y)

#                         # Replace non-ASCII characters in the span text
#                         span['text'] = replace_non_ascii(span['text'])
                        
#         #transformed_text_blocks_clean = clean_for_json(text_blocks)
        
#         # print("\n\nTEXT BLOCKS TRANSFORMED: \n")
#         # pp(text_blocks)

#         if is_testing:
#             with open("original_text_blocks.json", "w") as orig_file:
#                 json.dump(original_text_blocks_clean, orig_file, indent=4)
        
#             # with open("transformed_text_blocks.json", "w") as trans_file:
#             #     json.dump(transformed_text_blocks_clean, trans_file, indent=4)

#         return text_blocks

#     except Exception as e:
#         logger.error(f"Error getting page text: {e}", exc_info=True)
#         return {}

def process_table_rois(pdfId, 
                       pdf_path, 
                       page_text_blocks,
                       raw_data,
                       page, 
                       page_number, 
                       converted_roi_payload, 
                       combined_text_tables, 
                       combined_annot_tables, 
                       combined_outlier_df, 
                       combined_annot_outlier_df):

    outliers_df = pd.DataFrame()
    annot_df = pd.DataFrame()

    try:
        #print("\n\n--> Extracting Tables")
        structured_text_tables, structured_annot_tables, outliers_df, annot_outlier_df = get_table_data(pdf_path, page, page_number, converted_roi_payload, page_text_blocks, raw_data)   
        #print(f"\n\n--> After Extracting Tables: {structured_text_tables}")
        # print(f"\n\n-------\nprocess_table_rois:(structured_annot_tables): {structured_annot_tables}\n\n")
        # print(f"\n\nprocess_table_rois:(structured_text_tables): {structured_text_tables}")
        
        # Insert the pdf_ID into each DataFrame in structured_text_tables and combine with combined_text_tables
        for table_name, df in structured_text_tables.items():
            if df is not None and not df.empty:
                df['pdf_id'] = pdfId
                normalized_table_name = table_name.lower()  # Normalize the table name
                
                # Find the matching key in combined_text_tables in a case-insensitive manner
                matching_key = next((k for k in combined_text_tables.keys() if k.lower() == normalized_table_name), None)
                if matching_key:
                    # Use the existing key for updating to preserve the original case of the first occurrence
                    combined_text_tables[matching_key] = pd.concat([combined_text_tables[matching_key], df], ignore_index=True)
                else:
                    # If not found, initialize using the normalized table name
                    combined_text_tables[normalized_table_name] = df
                    
        # Insert the pdf_ID into each DataFrame in structured_annot_tables and combine with combined_annot_tables
        for table_name, df in structured_annot_tables.items():
            if df is not None and not df.empty:
                df['pdf_id'] = pdfId
                normalized_table_name = table_name.lower()  # Normalize the table name
                # Find the matching key in combined_annot_tables in a case-insensitive manner
                matching_key = next((k for k in combined_annot_tables.keys() if k.lower() == normalized_table_name), None)
                if matching_key:
                    # Use the existing key for updating to preserve the original case of the first occurrence
                    combined_annot_tables[matching_key] = pd.concat([combined_annot_tables[matching_key], df], ignore_index=True)
                else:
                    # If not found, initialize using the normalized table name
                    combined_annot_tables[normalized_table_name] = df
                                
        # Insert the pdf_ID(db pdf_id into each DataFrame
        if outliers_df is not None and not outliers_df.empty:
            outliers_df['pdf_id'] = pdfId
        if annot_outlier_df is not None and not annot_outlier_df.empty:
            annot_outlier_df['pdf_id'] = pdfId
                                
        try:
            if outliers_df is not None and not outliers_df.empty:
                combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)
                            
        except Exception as e:
            logger.error(f"An error occurred concatting combined_outliers_df: {e}", exc_info=True)
                        
        # Try combining TABLE ANNOTATION DATA
        try:
            if len(annot_df) > 0:
                combined_annot_df = pd.concat([combined_annot_df, annot_df], ignore_index=True)  
                try:
                    if annot_outlier_df is not None and not annot_outlier_df.empty:
                        combined_annot_outlier_df = pd.concat([combined_annot_outlier_df, annot_outlier_df], ignore_index=True)
                except Exception as e:
                    logger.error(f"An error occurred concatting combined_annot_outlier_df: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"An error occurred concatting combined_annot_df: {e}", exc_info=True)
            
        #logger.debug(f"BOM Processing completed successfully for page {page_number}")
    except Exception as e:
        #logger.error(f"An error occurred in get_table_data_2: {e}")
        logger.error(f"Error getting table data on page {page_number + 1}: {e}", exc_info=True)  
        
    # print(f"\n\nprocess_table_rois (combined_annot_tables): {combined_annot_tables}")
    # print(f"\n\nprocess_table_rois:(combined_annot_outlier_df): {combined_annot_outlier_df}")

    return combined_annot_tables, combined_text_tables, combined_outlier_df, combined_annot_outlier_df

def process_image_type_rois(pdfID, document, page_number, img_data, block, parent_folders_str, pdf_path, filename):
    #----------- FUNCTION UNTESTED ----------------
    try:
        image_bbox = block["bbox"]
        xref = block["image"]  # xref number of the image

        # Ensure xref is an integer
        if not isinstance(xref, int):
            return img_data
            #print(f"Warning: Expected integer for xref, got {type(xref)} with value {xref}")

        # Extract and save the image
        try:
            image_info = document.extract_image(xref)
            if image_info is not None:
                image_bytes = image_info["image"]
                image_filename = f"image_{page_number}_{xref}.png"
                image_filepath = os.path.join(r"C:\Users\<USER>\AIS_work_dir\Test Integration\Test1", image_filename) ###Fix this Absolute Path
            
                with open(image_filepath, "wb") as img_file:
                    img_file.write(image_bytes)

                # Append image information to data
                img_data.append({
                    'sys_build': parent_folders_str,
                    'sys_path': pdf_path,
                    'sys_filename': filename,
                    'pdf_id': pdfID,
                    'pdf_page': page_number + 1,
                    'type': 'Image',
                    'image_xref': xref,
                    'coordinates': image_bbox,
                    'image_path': image_filepath
                })
            #logger.debug(f">>> Processed Text Type (1)(Images): {page_number}")      
        except Exception as e:
            logger.error(f"Error extracting image at xref: {e}", exc_info=True) #{xref}: {e}")
    except Exception as e:
        logger.error(f"Error processing image block on page {page_number}: {e}")
    return img_data

def normalize_text(text):
    # Replace all whitespace with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove zero-width spaces and similar invisible characters
    text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)

    # Using regex to remove leading and trailing whitespace
    text = re.sub(r'^\s+|\s+$', '', text)
    
    # Strip leading/trailing whitespace
    return text.strip() 

def process_text_rois(pdfID, rotation, text_blocks, data, img_data, document, page, page_number, pdf_path, filename, parent_folders_str, validate_string, value_patterns, page_validated=False):

    '''            
    Extract text along with its bounding box (coordinates)
    Coordinate (bbox format):(x1, y1, x2, y2)
    Where x0, y0 is top-left corner of rectangle bounding box
    and x1, y1 are the coordinates of the lower-right corner of the rectangle bounding the text.
    '''

    def int_to_rgb(color_int): # format pymupdf font color
        blue = color_int & 255
        green = (color_int >> 8) & 255
        red = (color_int >> 16) & 255
        return (red, green, blue)

    # # Existing code up to the extraction of bbox2
    # rotation = page.rotation # Get Page Rotation
    #print(f"process_text_rois ROTATION: {rotation}")

    text_blocks = clean_for_json(text_blocks)

    if IS_TESTING:
        with open(getDataTempPath("transformed_text_blocks.json"), "w") as trans_file:
            json.dump(text_blocks, trans_file, indent=4)
    
    logger.debug(f"Extracting TEXT: {page_number}")
    for block in text_blocks:
        # if block["type"] == 0:  # 0 for text block, 1 for image block
        if isinstance(block, dict) and block.get("type") == 0:  # Text block
            try:
                bbox1 = block["bbox"]
                
                for line in block["lines"]:
                    for span in line["spans"]:

                        words_data = []

                        for word in span.get("words", []):
                            words_data.append({
                            "word": word.get("text", ""),
                            "bbox": word.get("bbox", [])
                            #"origin": word.get("origin", ())
                        })

                        #text = span.get("text", "").strip()
                        text = span.get("text", "")
                        if text:
                            # Using regex to remove leading and trailing whitespace
                            #text = re.sub(r'^\s+|\s+$', '', text)
                            
                            # Using regex to remove leading and trailing whitespace
                            text = normalize_text(text) #re.sub(r'^\s+|\s+$', '', text)

                            # Get Font Info and Flags
                            font = span.get("font", "Unknown")  # Get the font name
                            font_size = span.get("size", 0)    # Get the font size
                            flags = span.get("flags", 0)       # Get the text flags
                            font_color_int = span.get("color", 0) # Default to 0 (Black)

                            # Convert color to RGB and hex formats
                            font_color_rgb = int_to_rgb(font_color_int)
                            font_color_hex = "#{:02x}{:02x}{:02x}".format(*font_color_rgb)


                        bbox2 = span.get("bbox", (0, 0, 0, 0))  # Use the bbox of the span for individual coordinates
                        dynamic_bbox = bbox2


                        if text: # Add text to data only if it's not empty
                            if validate_string and page_validated ==False:
                                if validate_string.replace(" ", "") in text.replace(" ", ""):
                                    #print("Page Validated")
                                    page_validated = True
                            
                            #Evaluate value with regex
                            category, extracted_value = update_value_type(text, value_patterns)    
                            
                            data.append({
                                'sys_build': parent_folders_str,
                                'sys_path': pdf_path,
                                'sys_filename': filename,
                                'pdf_id': pdfID,
                                'pdf_page': page_number + 1,
                                'sys_layout_valid': page_validated,
                                'type': 'Text',
                                'category': category,
                                'value': text, #text.strip(),
                                'elevation': extracted_value if category == 'elevation' else '',
                                'x_position': extracted_value if category == 'x_coordinate' else '',
                                'y_position': extracted_value if category == 'y_coordinate' else '',
                                'coordinates': bbox1, #The main bounding box
                                'coordinates2': dynamic_bbox, #bbox2 normalized_bbox2 #Precise Bounding Box for individual value
                                'words': json.dumps(words_data),  # Store as JSON string for DataFrame compatibility
                                'name': '',
                                'title': '',
                                'created_date': '',
                                'mod_date': '',
                                'id_annot_info': '',
                                'annot_'
                                'page_rotation': rotation,
                                'color':font_color_rgb,
                                'annot_type':'',
                                'annot_subtype':'',
                                'vertices':'',
                                'endpoint':'',
                                'stroke_color':'',
                                "font": font,
                                "font_style":"",
                                "font_size": font_size,
                                "flags": flags
                            })
                            
            except Exception as e:
                logger.error(f"Error processing text block on page {page_number}: {e}")             
        
                
        #Handle Images
        elif block["type"] == 1:  # Check if block is an image
            # --> Process Image Data Types
            pass
            img_data = process_image_type_rois(pdfID, document, page_number, img_data, block, parent_folders_str, pdf_path, filename)
            
    if DEBUG_MODE:
        try:
            raw_text_df = pd.DataFrame(data)
            raw_text_df.to_excel(getDataTempPath(f"Raw Text DF Page {page_number+1}.xlsx"))
        except Exception as e: 
            logger.error(f"{e}")
    return data, img_data

def process_annot_rois(doc: fitz.Document, pdfID: int, rotation: int, data, page: fitz.Page, page_number: int, pdf_path: str, filename: str, parent_folders_str: str, validate_string, value_patterns):
    try:
        annotations = page.annots()
        if not annotations:
            return data

        for n, annot in enumerate(annotations):
            color, font_name, font_size, font_style = '', '', '', ''  # Set to blank initially
            annot_info = annot.info
            annot_rect = annot.rect
            value = annot_info.get('content', '').strip()
            annot_flags = annot.flags

            try:
                xref_obj = doc.xref_object(annot.xref)
                if '/DA' in xref_obj:
                    da_string = xref_obj.split('/DA (')[1].split(')')[0]

                    # Parse the DA string
                    parts = da_string.split()
                    color = [float(parts[0]), float(parts[1]), float(parts[2])]
                    font_name = parts[4].lstrip('/')  # Remove leading '/' if present
                    font_size = float(parts[5])

                    # Extract font style from DS if available
                    if '/DS' in xref_obj:
                        ds_string = xref_obj.split('/DS (')[1].split(')')[0]
                        font_style = 'normal'
                        if 'bold' in ds_string.lower():
                            font_style = 'bold'
                        elif 'italic' in ds_string.lower():
                            font_style = 'italic'
                        elif 'bold' in ds_string.lower() and 'italic' in ds_string.lower():
                            font_style = 'bold-italic'
                    else:
                        font_style = 'normal'
                else:
                    color, font_name, font_size, font_style = '', '', '', ''
            except Exception as e:
                print(f"Error extracting font info: {e}")

            # Get the annotation color
            annot_color = annot.colors['stroke'] if annot.colors else None
            
            # Get the font color (fill color)
            font_color = annot.colors['fill'] if annot.colors and 'fill' in annot.colors else None

            # Get annotation type and subtype
            annot_type = annot.type[0]  # Main type
            annot_subtype = annot.type[1] if len(annot.type) > 1 else None  # Subtype if available
            
            # Get vertices if available
            vertices = annot.vertices if hasattr(annot, 'vertices') else None
            endpoint = vertices[-1] if vertices else None
            
            #Evaluate value with regex
            category, extracted_value = update_value_type(value, value_patterns)
            #elevation, category = update_if_elevation(value, elevation_patterns)

            # Adjust the BBOX
            adjusted_bbox = adjust_bbox(annot_rect, page.rotation_matrix)

            data.append({
                'sys_build': parent_folders_str,
                'sys_path': pdf_path,
                'sys_filename': filename,
                'pdf_id': pdfID,
                'pdf_page': page_number + 1,
                'sys_layout_valid':"",
                'type': annot_info.get('subject', ''),
                'annot_type': annot_type,
                'annot_subtype': annot_subtype,
                'category': category,
                'value': value,
                'elevation': extracted_value if category == 'elevation' else '',
                'x_position': extracted_value if category == 'x_coordinate' else '',
                'y_position': extracted_value if category == 'y_coordinate' else '',
                # 'coordinates': (annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                'coordinates': adjusted_bbox,
                'coordinates2': adjusted_bbox,
                'vertices': vertices,
                'endpoint': endpoint,
                'name': annot_info.get('name', ''),
                'title': annot_info.get('title', ''),
                'mod_date': annot_info.get('creationDate', ''),
                'modDate': annot_info.get('creationDate', ''),
                'id_annot_info': annot_info.get('id', ''),
                'page_rotation': rotation,
                'stroke_color': annot_color,  # Annotation color (stroke color)
                'font_color': font_color,  # Font color (fill color)
                'color': color,
                'font': font_name,
                'font_style': font_style,
                'font_size': font_size,
                'Flags': annot_flags
                
            })
                    
        #logger.debug(f">>> Processed Annotations Data: {page_number}")
    except Exception as e:
        logger.error(f"Error processing annotations on page {page_number}: {e}") 
    #print(f"\n\nTEXT ANNOT DATA", data)
    return data

def commit_pdf_page(project_id: int, page: fitz.Page, pdf_path: str, page_number: int, page_valid: bool) -> int:
    """Commit page to database. Retrieve id from insert and save local pixmap of page"""
    if not COMMIT_PDF or project_id is None:
        pdfId = page_number
        return pdfId
    db_manager = DatabaseManager() # Connect to database
    document_name = None
    pdfId = None  # Ensure pdfID has a default value
    doc_skip = 0 if page_valid else 1
    try:
        page_width, page_height = page.rect.width, page.rect.height
        doc_size = ",".join([str(page_width), str(page_height)])
        pdf_bytes = None # No longer commit page blob
        pdfId = db_manager.insert_pdf_page_to_storage(project_id, 
                                                      pdf_path,
                                                      page_number + 1, 
                                                      "Isometric", 
                                                      document_name, 
                                                      pdf_bytes, 
                                                      doc_size, 
                                                      doc_skip)
    except Exception as e:
        logger.critical(f"Error committing pdf page ({page_number+1}) to database. Page will be skipped in subsequent processes: {e}", exc_info=True)

    try:
        zoom = 2  # 144 / 2 . Default DPI in PyMuPDF is 72
        mat = fitz.Matrix(zoom, zoom)
        pixmap: fitz.Pixmap = page.get_pixmap(matrix=mat)
        savePagePixmap(pixmap, project_id, pdf_path, page_number + 1)
        document_name = f"{str(uuid.uuid4())}.pdf"  # Unique name for the stored PDF page
    except Exception as e:
        logger.warning(f"Error saving page pixmap", exc_info=True)
    
    return pdfId


def process_pdf_pages(project_id, pages: list[int], pdf_path, doc_tolerance, 
                      roi_payload: dict, value_patterns, validate_string=None, table_names=["BOM", "SPEC", "Spool"]):
    """
    Processes a chunk of pages

    Returns:
        raw_data:
        skipped_pages:
        combined_annot_tables:
        combined_text_tables:
        combined_outlier_df:
        page_roi_payload: Dictionary mapping of page number to converted payload
    """
    # Iniitalize empty data to avoid tracebacks
    raw_data = []   
    img_data = []   # Data is not currently used
    combined_outlier_df = pd.DataFrame()
    combined_annot_outlier_df = pd.DataFrame()
    page_roi_payloads = {}

    # Initialize structured_text_tables with blank DataFrames for each table name
    combined_text_tables = {table_name: pd.DataFrame() for table_name in table_names}
    combined_annot_tables = {table_name: pd.DataFrame() for table_name in table_names}

    skipped_pages = {} # Initialize a dictionary to collect skipped page numbers and their sizes    

    # Add this line - Get all pages' data at once if using RAW_IMPORT

    if RAW_IMPORT:
        raw_data = raw_df_patch[raw_df_patch["pdf_page"].isin([p+1 for p in pages])].copy()

    # Open document
    document: fitz.Document = fitz.open(pdf_path)
    
    # Extracting the filename
    filename = os.path.basename(pdf_path)

    # Extracting the directory path
    directory_path = os.path.dirname(pdf_path)

    # Splitting the directory path into a list of parent folders
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)
 
    for page_number in pages:
        # --> GET DOC SIZE, COMMIT TO DB, and SKIP PAGES
        try:
            page_valid, page = True, document.load_page(page_number)
            # page_valid, page = process_page_size(document, page_number, doc_tolerance, skipped_pages)
            pdfId = commit_pdf_page(project_id, page, document.name, page_number, page_valid)
        except Exception as e:
            logger.warning(f"Page {page_number + 1}. Error processing page - {e}")
            continue

        if not page_valid: # Skip processing if page is invalid
            logger.warning(f"Page {page_number + 1} was flagged as 'skipped' likely due to document size not matching initial document.")
            continue

        # Get the page's text and apply rotation if needed
        rotation = page.rotation
        rotation_matrix = page.rotation_matrix  # Get the page's rotation matrix
        
        if rotation != 0:
            logger.info(f"Page {page_number + 1} Rotation: {rotation}. Applying Rotation Matrix")

        # if IS_TESTING and TEST_TRANSFORM != 0:
        #     rotation_matrix = TEST_TRANSFORM
        #     logger.info(f"Page {page_number + 1} Rotation: {rotation}. TESTING ACTIVE - Applying Rotation Matrix: {rotation_matrix}")

        #print(f"\n\nPAGE ROTATION {page_number}: {rotation}")

        text_blocks = get_page_text(page, rotation_matrix) #page.get_text("dict")["blocks"]

        ##### ADDED 12.5.24
        # if RAW_IMPORT:
        #     # Update pdf_id for current page's data
        #     mask = raw_data["pdf_page"] == page_number + 1
        #     raw_data.loc[mask, "pdf_id"] = pdfId
        #     current_page_data = raw_data[mask]
        # else:
        # Process text and annotations for current page
        current_page_data = []
        current_page_data, img_data = process_text_rois(pdfId, rotation, text_blocks, current_page_data, img_data, document,page, page_number, 
                                            pdf_path, filename, parent_folders_str, validate_string, value_patterns)
        current_page_data = process_annot_rois(document, pdfId, rotation, current_page_data, page, page_number, pdf_path, filename, parent_folders_str, validate_string, value_patterns)

        raw_data.extend(current_page_data)

        # Added import here just for clarity
        from src.atom.vision.ocr_patcher import detect_page_regions, find_missing_regions_vectorized, extract_raw_regions, get_patched_ocr_data

        #print(f"process_pdf_pages(annot_data): REMEMBER TO ADJUST ANNOT DATA")
        page_group = roi_payload["pageToGroup"][page_number + 1]
        converted_roi_payload = convert_relative_coords_to_points(roi_payload["groupRois"][page_group], page.rect.width, page.rect.height)
        page_roi_payloads[page_number+1] = converted_roi_payload

        if ENABLE_OCR:
            # Detect the page regions using opencv
            cv2_page_regions = detect_page_regions(page=page, page_number=page_number+1)

            # Detect missing regions for the page
            page_raw_df = pd.DataFrame(current_page_data)
            page_raw_df.to_excel(r"debug/page_raw_df.xlsx")
            page_raw_regions = pd.DataFrame(extract_raw_regions(page_raw_df))
            page_raw_regions.to_excel(r"debug/page_raw_region.xlsx")
            if page_raw_regions.empty:
                continue # Blank page?
            page_roi_regions = [
                {
                    'pdf_page': page_number + 1,
                    'group_number': page_group,
                    'columnName': r['columnName'],
                    'x0': r.get('tableCoordinates', r)['x0'],
                    'y0': r.get('tableCoordinates', r)['y0'],
                    'x1': r.get('tableCoordinates', r)['x1'],
                    'y1': r.get('tableCoordinates', r)['y1'],
                }
                for r in converted_roi_payload
            ]
            page_roi_regions = pd.DataFrame(page_roi_regions)

            missing_regions = find_missing_regions_vectorized(page_number+1, cv2_page_regions, page_raw_regions, page_roi_regions)

            # Get unique pages requiring OCR
            requires_ocr = missing_regions['pdf_page'].unique().tolist() # Check whether we need OCR

            # Perform OCR here / Or defer until later
            patched_raw_data = get_patched_ocr_data(pdf_path, requires_ocr, missing_regions, page_raw_df)

            try:
                raw_data.extend(patched_raw_data)
            except Exception as e:
                print(f"Warning: Error extending raw_data: {str(e)}")

        # mask = raw_data["pdf_page"] == page_number + 1
        # raw_data.loc[mask, "pdf_id"] = pdfId
        # --> Get BOM table data \\\\\\\BOM TABLE LOGIC//////// bom_df=Text, annot_df=Annotations

        combined_annot_tables, combined_text_tables, combined_outlier_df, combined_annot_outlier_df = process_table_rois(
            pdfId,
            pdf_path,
            text_blocks,
            raw_data,
            page,
            page_number,
            converted_roi_payload,
            combined_text_tables,
            combined_annot_tables,
            combined_outlier_df,
            combined_annot_outlier_df
        )

        print(f"Processing page{page_number} finished...")
        #print(f"\n\nprocess_pdf_pages(combined_annot_tables): {type(combined_annot_tables)}, \nDATA {combined_annot_tables}")
        #print(f"\n\nprocess_pdf_pages(annot_data): {type(annot_data)}, \nDATA {annot_data}")
    document.close()
    print("document closed.")

    return raw_data, skipped_pages, combined_annot_tables, combined_text_tables, combined_outlier_df, page_roi_payloads
    
def worker(pdf_path, 
           pages: list[int],
           roi_payload: dict,
           value_patterns, 
           columns_to_aggregate, 
           columns_to_keep,
           overlap_threshold,
           project_id,
           doc_tolerance=2,
           validate_string=None):
    raw_data = []
    skipped_pages = {}  # Initialize a dictionary to collect skipped page numbers and their sizes
    combined_raw_df = pd.DataFrame()
    combined_bom_df = pd.DataFrame()
    combined_annot_df = pd.DataFrame()
    combined_annot_types_df = pd.DataFrame()
    combined_outlier_df = pd.DataFrame()
    logger.info(f"Worker started for {len(pages)} pages... {pages[0]} ... {pages[-1]}")

    # Initialize structured_text_tables with blank DataFrames for each table name
    combined_text_tables = {table_name: pd.DataFrame() for table_name in ["BOM", "SPEC", "Spool"]}
    combined_annot_tables = {table_name: pd.DataFrame() for table_name in ["BOM", "SPEC", "Spool"]}
    
    # Extracting the directory path
    directory_path = os.path.dirname(pdf_path)

    # Splitting the directory path into a list of parent folders
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)

    # --> Process PDF Pages
    res = process_pdf_pages(project_id, pages, pdf_path, doc_tolerance, roi_payload, value_patterns, validate_string)
    raw_data, skipped_pages, combined_annot_tables, combined_text_tables, combined_outlier_df, page_roi_payloads = res
    # Try assigning raw data to dataframe
    try:
        raw_data_df = pd.DataFrame(raw_data)
    except Exception as e:
        logger.error(f"Error assigning Raw Data to Dataframe: {e}")
        # Subsequent functions bypassed on failure to assign

    # Only combine if raw_data_df has one or more rows
    if not raw_data_df.empty:
        combined_raw_df = pd.concat([combined_raw_df, raw_data_df], ignore_index=True)

        # --> Consolidate non-table data
        annot_types_df = consolidate_ranges(combined_raw_df, page_roi_payloads, columns_to_aggregate, columns_to_keep, overlap_threshold)
        #print("8")

        # Only combine if annot_types_df has one or more rows
        if not annot_types_df.empty:
            combined_annot_types_df = pd.concat([combined_annot_types_df, annot_types_df], ignore_index=True)
            
        #print("9")

    if combined_raw_df is None:
        print("combined_raw_df is None")
    if combined_annot_types_df is None:
        print("combined_annot_types_df is None")
    # if combined_bom_df is None:
    #     print("combined_bom_df is None")
    if combined_outlier_df is None:
        print("combined_outlier_df is None")
        

    # print("ANNOT TYPES:", "\nLENGTH: ", len(combined_annot_types_df), "DATA: ", combined_annot_types_df)
    # print("ANNOT TABLES:", "\nLENGTH: ", len(combined_annot_tables), "DATA: ", combined_annot_tables)
        
    # After processing all pages, log the skipped pages and their sizes
    if skipped_pages:
        print("\n\n--> ATTENTIOM! \n")
        logger.warning(f"Skipped pages and their sizes: {skipped_pages}")
     
    #print("10")

    return combined_raw_df, combined_annot_types_df, combined_annot_tables, combined_text_tables, combined_outlier_df, skipped_pages

# --------> Worker & Helper Functions

def format_value_with_colors(value, stroke_color, font_color, font_name, font_style, font_size):
    formatted_value = value if value else ''
    attributes = []
    if stroke_color:
        attributes.append(f"stroke:{format_color(stroke_color)}")
    if font_color:
        attributes.append(f"font:{format_color(font_color)}")
    if font_name:
        attributes.append(f"font-name:{font_name}")
    if font_style:
        attributes.append(f"font-style:{font_style}")
    if font_size:
        attributes.append(f"font-size:{font_size}")
    if attributes:
        formatted_value += f"-[{','.join(attributes)}]"
    return formatted_value

def format_color(color):
    if isinstance(color, list) and len(color) == 3:
        rgb = [int(c * 255) for c in color]
        return f"rgb({rgb[0]},{rgb[1]},{rgb[2]})"
    else:
        return str(color)

def is_inside(inner_coords, outer_coords):
    ix0, iy0, ix1, iy1 = inner_coords
    ox0, oy0, ox1, oy1 = outer_coords
    return ox0 <= ix0 and ix1 <= ox1 and oy0 <= iy0 and iy1 <= oy1

def safe_literal_eval(coord_str):
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

def process_extracted_annot_data(df):
    """Builds 'annot_markups' dictionary"""

    if DEBUG_MODE:
        df.to_excel(getDataTempPath("annot_element_data.xlsx"))

    # Filter out both Text and OCR types
    df = df[~df['type'].isin(['Text', 'OCR'])]    
    
    # Separate Text Box annotations
    text_boxes = df[df['type'] == 'Text Box'].copy()
    other_annots = df[df['type'] != 'Text Box'].copy()

    # Safely convert coordinates2 to tuples of floats
    text_boxes['coordinates2'] = text_boxes['coordinates2'].apply(safe_literal_eval)
    other_annots['coordinates2'] = other_annots['coordinates2'].apply(safe_literal_eval)

    # Remove rows with invalid coordinates
    text_boxes = text_boxes.dropna(subset=['coordinates2'])
    other_annots = other_annots.dropna(subset=['coordinates2'])

    # Find parent annotations for each Text Box
    for idx, text_box in text_boxes.iterrows():
        for other_idx, other_annot in other_annots.iterrows():
            if (text_box['sys_path'] == other_annot['sys_path'] and
                text_box['pdf_page'] == other_annot['pdf_page'] and
                is_inside(text_box['coordinates2'], other_annot['coordinates2'])):
                # Assign the text box value to the parent annotation
                other_annots.at[other_idx, 'value'] = text_box['value']
                # Merge properties
                other_annots.at[other_idx, 'value'] = text_box['value']
                other_annots.at[other_idx, 'font_color'] = text_box['font_color']
                other_annots.at[other_idx, 'font'] = text_box['font']
                other_annots.at[other_idx, 'font_style'] = text_box['font_style']
                other_annots.at[other_idx, 'font_size'] = text_box['font_size']

    # Process the annotations
    annotation_data = {}

    for annotation_type in other_annots['type'].unique():

        if pd.isna(annotation_type):  # Skip NaN annotation types
            continue

        temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color', 'font', 'font_style', 'font_size']].copy()
        
        # Only process if we have data
        if not temp_df.empty:
            formatted_values = []
            grouped = temp_df.groupby(['sys_path', 'pdf_page'])
        
            for name, group in grouped:
                sys_path, pdf_page = name
                values = [format_value_with_colors(v, sc, fc, fn, fs, fsz) 
                         for v, sc, fc, fn, fs, fsz in zip(group['value'], 
                                                          group['stroke_color'], 
                                                          group['font_color'],
                                                          group['font'],
                                                          group['font_style'],
                                                          group['font_size'])]
                formatted_values.append({
                    'sys_path': sys_path,
                    'pdf_page': pdf_page,
                    'value': values
                })
            
            if formatted_values:  # Only create DataFrame if we have values
                annotation_data[annotation_type] = pd.DataFrame(formatted_values)

    # explicitly set the dtype of annot_markups to string when creating annot_types_df to avoid deprecation
    annot_types_df = other_annots[['sys_path', 'pdf_page', 'annot_markups']].drop_duplicates().reset_index(drop=True)
    annot_types_df['annot_markups'] = annot_types_df['annot_markups'].astype('str')  # Add this line

    for index, row in annot_types_df.iterrows():
        annotations_for_row = {}
        for annotation_type, annotations_df in annotation_data.items():
            if not annotations_df.empty:  # Check if DataFrame has data
                matching_annotations = annotations_df[
                    (annotations_df['sys_path'] == row['sys_path']) & 
                    (annotations_df['pdf_page'] == row['pdf_page'])
                ]['value'].tolist()

                if matching_annotations:
                    if matching_annotations and isinstance(matching_annotations[0], list):
                        matching_annotations = [item for sublist in matching_annotations for item in sublist]
            
                    annotations_for_row[annotation_type] = matching_annotations

        annot_types_df.at[index, 'annot_markups'] = str(annotations_for_row)
        
    return annot_types_df

def initialize_json_column_names(converted_roi):
    # Initialize a set to hold all column names from JSON
    json_column_names = set()
    for item in converted_roi:
        try:
            json_column_names.add(item['columnName'])
            
            # Check if 'tableColumns' is present in the item
            if 'tableColumns' in item:
                # Iterate through each column in 'tableColumns'
                for column_dict in item['tableColumns']:
                    # Each column_dict has the column name as the key
                    for column_name in column_dict.keys():
                        # Add the column name to the set of JSON column names
                        json_column_names.add(column_name)
        except Exception as e:
            logger.error(f"Error processing converted_roi item: {e} ---{item}", exc_info=True)
    return json_column_names

def consolidate_group(group, json_column_names, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon):

    #if all(group['sys_layout_valid']):
    # Initialize consolidated_data with JSON column names
    consolidated_data = {col: set() for col in json_column_names}
    # Initialize consolidated_data
    consolidated_data = {col: set() for col in columns_to_aggregate if col in group.columns}
    consolidated_data.update({col: group[col].iloc[0] for col in columns_to_keep if col in group.columns})
    
    # Check if any 'sys_layout_valid' value in the group is True
    if any(group['sys_layout_valid']):
        page_valid = True
        consolidated_data['sys_layout_valid'] = True

    overlap_sizes = {}
    processed_values = {}  # Track processed values and their bounding boxes

    # Apply logic for each coordinate range in JSON
    for _, row in group.iterrows():
        for col in columns_to_aggregate:
            if pd.isna(row[col]):  # Check value which are not NaN
                continue
            value = str(row[col])  # Convert to string
            if not value.strip():  # Now safe to use strip()
                continue
            # consolidated_data[col].add(value.strip()) Original 12.4.24

            # Check for duplicates based on value and coordinates
            coords = row['coordinates2']
            is_duplicate = False

            for existing_value, existing_coords in processed_values.get(col, []):
                if (value == existing_value or 
                    (len(value) > 0 and len(existing_value) > 0 and 
                        (value in existing_value or existing_value in value)) and
                    check_bbox_overlap(coords, existing_coords)):
                    is_duplicate = True
                    break
        
            if not is_duplicate:
                consolidated_data[col].add(value.strip())

                if col not in processed_values:
                    processed_values[col] = []
                processed_values[col].append((value, coords))

        for item in converted_roi:
            try:
                roi_column_name = item['columnName'].lower()
                if roi_column_name in ['bom', 'spec', 'spool']:
                    continue

                    # Calculate overlap for other items (Being Skipped???)
                    item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
                    overlap_area = calculate_overlap_area(text_box, item_box)

                    text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
                    overlap_ratio = overlap_area / text_area if text_area else 0



                    if overlap_ratio >= overlap_threshold:
                        check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold)

                if 'tableColumns' in item:
                    for col in item['tableColumns']:
                        check_and_update_data(row, col, consolidated_data, epsilon, overlap_threshold)
                else:
                    #print("/n/nERROR PASSING ITEM ON ELSE")
                    check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold, overlap_sizes)

                    # if roi_column_name == 'drawing':
                    #     print(f"\n\nDRAWING COORDINATE CHECK: CALLS check_and_update")

            except Exception as e:
                logger.error(f"Error analyzing field {item['columnName']}: {e}")
    try:
        # Convert sets to strings, joining unique values with '; '
        for col in columns_to_aggregate:
            consolidated_data[col] = '; '.join(consolidated_data[col])

    except Exception as e:
        logger.error(f"Error consolidating columns_to_aggregate: {e}")
        
    return consolidated_data

def check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold=0, overlap_sizes: dict={}, precedence_for_annot: bool=True, debug_log=check_debug_log): #debug_log: dict=None):

    if debug_log is None:
        debug_log = {}
    
    # Initialize debug log for this page if it doesn't exist
    page_num = str(row['pdf_page'])
    if page_num not in debug_log:
        debug_log[page_num] = []

    coords = row['coordinates2']
    item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
    text_box = (coords[0], coords[1], coords[2], coords[3])

    # Calculate overlap area
    overlap_area = calculate_overlap_area(text_box, item_box)
    text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
    overlap_ratio = overlap_area / text_area if text_area else 0

    # if item['columnName'] == "paint_color" and row['value']=="Yellow":
    #     print("Check:", row['value'], item_box, text_box, overlap_area, text_area)
    #     print(overlap_ratio >= overlap_threshold, overlap_ratio, overlap_threshold)

    if overlap_ratio >= overlap_threshold:

        column_name = item['columnName']
        new_value = row['value'].strip()

        # Get current values as a set (split by spaces since that's how we concatenate)
        current_value = str(consolidated_data.get(column_name, '')).strip()
        current_values = set(current_value.split())

        # Check if this value or a duplicate is already present
        is_duplicate = False
        for existing_value in current_values:
            if (existing_value == new_value or 
                (len(existing_value) > 0 and len(new_value) > 0 and 
                 (existing_value in new_value or new_value in existing_value))):
                is_duplicate = True
                break

        if not is_duplicate:
            # Update with non-duplicate value
            if consolidated_data.get(column_name):
                consolidated_data[column_name] = f"{consolidated_data[column_name]} {new_value}"
            else:
                consolidated_data[column_name] = new_value

        # Create debug log entry
        log_entry = {
            'column': item['columnName'],
            'current_value': current_value,
            'new_value': new_value,
            'coordinates': text_box,
            'overlap_ratio': overlap_ratio,
            'type': row.get('Type', '')
        }

        # Handle precedence for annotations
        if precedence_for_annot and row.get('Type') != 'Text':
            log_entry['action'] = 'replaced'
            log_entry['reason'] = 'annotation_precedence'
            debug_log[page_num].append(log_entry)
            return

        # Check for duplicate values
        if current_value and new_value in current_value:
            log_entry['action'] = 'skipped'
            log_entry['reason'] = 'duplicate_value'
            debug_log[page_num].append(log_entry)
            return

        # Append new value
        log_entry['action'] = 'appended' if current_value else 'added'
        log_entry['reason'] = 'normal_processing'
        debug_log[page_num].append(log_entry)



def consolidate_ranges(df, page_roi_payloads: dict, columns_to_aggregate, columns_to_keep, overlap_threshold=0):
    """
    Args:
        page_roi_payloads: Map of {page number: converted_roi_layout}
    """
    logger.info("Starting consolidation process...")

    consolidated_data_list = [] # Create a list to hold the consolidated data for each group
    final_df = pd.DataFrame()  # Initialize final_df to handle data loss on error

    # Validate inputs
    if columns_to_aggregate is None:
        columns_to_aggregate = []
    if columns_to_keep is None:
        columns_to_keep = []

    # Check for empty DataFrame and add 'annot_markups' column
    if df.empty:
        logger.warning("Dataframe is empty. Exiting consolidation process.")
        return pd.DataFrame()

    # Check if column is in df and add it
    if 'annot_markups' not in df.columns:
        df['annot_markups'] = pd.Series([None]*len(df))

    #print("2")

    # --> # Convert 'coordinates' and 'coordinates2' columns from string to tuple
    df = convert_coords_to_tuple(df) 

    #print("3")

    # --> Process and return annotation data
    annot_types_df = process_extracted_annot_data(df)
 
    epsilon = 0.001 # Consider a small epsilon for float comparison inaccuracy
    grouped = df.groupby(['sys_path', 'pdf_page']) # Group by file path and page number
    
    
    # --> Initialize JSON Column Names
    json_column_names = None 

    for (filepath, page_number), group in grouped: # Process each group
        try:
            converted_roi = page_roi_payloads[page_number]
            if not json_column_names:
                json_column_names = initialize_json_column_names(converted_roi)
            
            # --> Process and consolidate data groups
            consolidated_data = consolidate_group(group, json_column_names, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon)
            consolidated_data_list.append(consolidated_data) # Add the consolidated data dictionary to the list
        except Exception as e:
            logger.error(f"Error processing group Path: {filepath}, Page: {page_number}: Error Description: {e}", exc_info=True)

    try:
        if consolidated_data_list:
            consolidated_df = pd.DataFrame(consolidated_data_list) # Create a new DataFrame from the consolidated data list
            annot_types_df = annot_types_df.groupby(['sys_path', 'pdf_page'])['annot_markups'].apply(lambda x: ', '.join(x)).reset_index()  # Aggregate 'annot_markups' values into a comma-delimited string for each 'sys_path', 'pdf_page'
            
            #print(f"\n\nANNOT TYPES DF-2: {annot_types_df} \n\n")
            final_df = pd.merge(consolidated_df, annot_types_df, on=['sys_path', 'pdf_page'], how='outer')

            if DEBUG_MODE:
                print(f"Final DataFrame rows after merge: {len(final_df)}")

    except Exception as e:
        logger.error(f"Error creating final DataFrame: {e}", exc_info=True)

    return final_df

# --------> consolidate_ranges & Helper Functions


def organize_pages_by_size(document, limit=None):
    """Organize pages by their size, including original page numbers."""
    page_sizes = defaultdict(list)
    for page_num in range(min(len(document), limit) if limit else len(document)):
        page = document.load_page(page_num)
        size = (round(page.rect.width, 2), round(page.rect.height, 2))
        page_sizes[size].append(page_num)
    return dict(page_sizes)

def merge_spec_and_general_data(combined_spec_df, combined_general_df, columns_to_merge=None):
    if columns_to_merge is None:
        columns_to_merge = ['size', 'pipeSpec', 'insulationSpec', 'insulationThickness', 'heatTrace', 'paintSpec']

    try:
        # Ensure 'PDF ID' and 'PDF Page' columns are present and clean
        # combined_spec_df = combined_spec_df.rename(columns={'PDF ID': 'pdf_id', 'PDF Page': 'pdf_page'})
        # combined_general_df = combined_general_df.rename(columns={'PDF ID': 'pdf_id', 'PDF Page': 'pdf_page'})

        combined_spec_df['pdf_id'] = combined_spec_df['pdf_id'].astype(str)
        combined_spec_df['pdf_page'] = combined_spec_df['pdf_page'].astype(str)
        combined_general_df['pdf_id'] = combined_general_df['pdf_id'].astype(str)
        combined_general_df['pdf_page'] = combined_general_df['pdf_page'].astype(str)

        # Filter columns_to_merge to only include columns present in combined_spec_df
        available_columns = [col for col in columns_to_merge if col in combined_spec_df.columns]

        # Select the available columns from combined_spec_df
        spec_df_selected = combined_spec_df[['pdf_id', 'pdf_page'] + available_columns]

        # Merge 'combined_spec_df' and 'combined_general_df' based on both 'pdf_id' and 'pdf_page'
        merged_df = pd.merge(spec_df_selected, combined_general_df, on=['pdf_id', 'pdf_page'], how='left')
        
        #print("\n\n --> COLUMNS AFTER MERGE: ", merged_df.columns)

        return merged_df

    except Exception as e:
        print(f"Error occurred while merging spec and general data: {str(e)}")
        return combined_general_df


def run_extraction(pdf_path, roi_payload, value_patterns, payload_options, sub_dir, x_offset, y_offset, overlap_threshold, project_id, debug_page_limit=None, multiprocess = True):
    if IS_TESTING:
        debug_page_limit = OVERRIDE_PAGE_LIMIT
        logger.info(f"Initializing multiprocessing with page limit of {OVERRIDE_PAGE_LIMIT}...")
    else:
        logger.info("Initializing multiprocessing...")
    
    # Increase the maximum number of columns displayed
    pd.set_option('display.max_columns', None)

    run_extract_dir = False #Change to True to run extraction for full directory
    ##initialize blank dataframes
    all_data = []
    consolidated_skipped_pages = {}
    
    # Initialize blank combined text dataframes
    combined_raw_df, combined_general_df, combined_outlier_df, combined_bom_df, combined_spool_df, combined_spec_df = (pd.DataFrame() for _ in range(6))
    
    # Initialize blank dataframes
    bom_df, spool_df, spec_df, outliers_df = (pd.DataFrame() for _ in range(4))
    
    # Access elements in the dictionary
    columns_to_aggregate = payload_options.get('aggregate_cols_general', [])
    columns_to_keep = payload_options.get('keep_cols_general', [])
    columns_to_keep.append('sys_layout_valid')
          
    document = fitz.open(pdf_path)
    # Get the reference width and height from the first document. Sizes that do not match will be skipped
    # ref_rect = document.load_page(0).rect
    # ref_width, ref_height = ref_rect.width, ref_rect.height

    total_pages = len(document)
    extract_pages = [] # List of pages to extract.
    # Get the total pages
    if OVERRIDE_EXTRACT_PAGES and not __file__.endswith(".pyc"):
        extract_pages = [int(c)-1 for c in OVERRIDE_EXTRACT_PAGES if c < total_pages]
        print(f"Overriding extractiong to {len(extract_pages)} specific pages...")
    else:
        extract_pages = [n for n in range(total_pages)]  # Extract all pages

    if debug_page_limit:
        extract_pages = extract_pages[:debug_page_limit]

    total_pages = len(extract_pages)
    extract_pages.sort()
    document.close()

    # Convert relative coordinates to absolute based on the reference height and width
    # converted_group_rois, page_to_group = process_grouped_rois(roi_payload, ref_width, ref_height, x_offset, y_offset)

    if IS_TESTING:
        logger.info("Original ROI Payload")
        pp(roi_payload)
                
    # Determine the number of processes
    if IS_TESTING:
        num_processes = 1  # Use a small number for testing
    else:
        available_cores = multiprocessing.cpu_count()  # Use all available CPUs for production

        # Determine the number of processes based on the number of pages
        if total_pages <= 10:
            num_processes = 1
        elif 11 <= total_pages <= 25:
            num_processes = min(2, available_cores)
        elif 26 <= total_pages <= 40:
            num_processes = min(4, available_cores)
        else:
            num_processes = available_cores  # Use all available cores if more than 40 pages


    logger.info(f"Starting multiprocessing with {num_processes} processes for {total_pages} pages")

    #Track job progress
    progress_increment = total_pages // 10  # 10% of total pages
    pages_processed = 0

    process_worker_lock = Lock() # Require safe access to update final results
    def process_worker_result(chunk_no: int, pages_processed: int, result):
        """Process and merge the worker result into final results as they are received"""
        logger.debug(f"Processing worker result - chunk={chunk_no}")
        nonlocal all_data, progress_increment
        nonlocal combined_raw_df, combined_general_df, combined_outlier_df, combined_bom_df, combined_spool_df, combined_spec_df
        process_worker_lock.acquire()

        try:
            # Attempt to unpack all expected items
            raw_data_df, annot_types_df, combined_annot_tables, combined_text_tables, outliers_df, skipped_pages = result
            all_data.extend(raw_data_df)
        except Exception as e:
            logger.error(f"Error unpacking results: {e}", exc_info=True)
            process_worker_lock.release()
            return  # TODO - log these unpacking errors

        try:
            if skipped_pages:
                # Merge skipped_pages into the consolidated_skipped_pages dictionary
                consolidated_skipped_pages.update(skipped_pages)
        except Exception as e:
            logger.info("--No Skipped Pages")

        try:
            print("combined text tables")
            print(combined_text_tables)
            # Get the TEXT type tables from the structure
            bom_df = get_structured_table('BOM', combined_text_tables)
            spec_df = get_structured_table('SPEC', combined_text_tables)
            spool_df = get_structured_table('Spool', combined_text_tables)

            #print(f"\n\n------------\nrun_multiprocessing - PAGE Range{start_page} to {end_page} rows: \nBOM: {len(combined_bom_df)}\nSPEC: {len(spec_df)}\nSPOOL: {len(spool_df)}\n\n")  
        except Exception as e:
            logger.error(f"Could not get the text type table(s): {e}", exc_info=True)

        try:
            # Get the ANNOTATION type tables from the structure
            bom_annot_df = get_structured_table('BOM', combined_annot_tables)
            spec_annot_df = get_structured_table('SPEC', combined_annot_tables)
            spool_annot_df = get_structured_table('Spool', combined_annot_tables)

            #print(f"\n\n------------\nrun_multiprocessing - PAGE Range{start_page} to {end_page} rows: \nBOM: {len(combined_bom_df)}\nSPEC: {len(spec_df)}\nSPOOL: {len(spool_df)}\n\n")  
        except Exception as e:
            logger.error(f"Could not get the text type table(s): {e}", exc_info=True)

        # Decide whether to use annot or text tables (BOM ONLY FOR NOW)
        determine_bom_type = False
        if determine_bom_type:
            try:

                if len(bom_df) == 0 and len(bom_annot_df) == 0: # Check the overall length of bom DataFrames and apply the specified logic
                    # Both DataFrames have no rows, do nothing
                    # logger.debug("BOM: TEXT/ANNOTS ARE BLANK FOR")
                    pass
                elif len(bom_df) > 0 and len(bom_annot_df) == 0: # combined_bom_df has rows and combined_annot_df has 0 rows, keep combined_bom_df as is
        
                    # logger.debug("BOM: USING TEXT DATA")
                    pass
                elif len(bom_annot_df) > 0 and len(bom_df) == 0:  # combined_annot_df has rows and combined_bom_df has 0 rows, set combined_bom_df = combined_annot_df
    
                    bom_df = bom_annot_df
                    # logger.debug("BOM: USING ANNOT DATA")
                    
                elif len(bom_df) > 0 and len(bom_annot_df) > 0:  # Both have rows, use the one with the greatest number of rows
    
                    if len(bom_df) > len(bom_annot_df):
                        # logger.debug("BOM: USING TEXT DATA. -- TEXT ROWS > ANNOT ROWS ---HANDLE THIS CASE")
                        pass
                    elif len(bom_df) < len(bom_annot_df):
                        bom_df = bom_annot_df
                        # logger.debug("BOM: USING ANNOT DATA. -- ANNOT ROWS > TEXT ROWS ---HANDLE THIS CASE")
                    else:
                        pass
                        # logger.debug("BOM: Both DataFrames have the same number of rows. Defaulting to using TEXT DATA.")
            except Exception as e:
                logger.error(f"Error in deciding TEXT or ANNOT BOM Logic")

        combined_bom_df = pd.concat([combined_bom_df, bom_df], ignore_index=True)
        combined_spec_df = pd.concat([combined_spec_df, spec_df], ignore_index=True)
        combined_spool_df = pd.concat([combined_spool_df, spool_df], ignore_index=True)

        combined_raw_df = pd.concat([combined_raw_df, raw_data_df], ignore_index=True)
        combined_general_df = pd.concat([combined_general_df, annot_types_df], ignore_index=True)
        combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)

        print(f"ROWS IN GENERAL L:2463 : {len(annot_types_df)}")

        # Update pages processed and check for progress
        if pages_processed >= progress_increment:
            logger.info(f"Progress: {pages_processed / total_pages * 100:.2f}% completed")
            progress_increment += total_pages // 10

        process_worker_lock.release()

    # Initialize the set of args for all workers
    pages_per_chunk = max(1, total_pages // num_processes)
    worker_args = []
    for n in range(0, len(extract_pages), pages_per_chunk):
        pages = [extract_pages[m] for m in range(n, min(len(extract_pages), n+pages_per_chunk))]
        worker_args.append((pdf_path, 
                            pages,
                            roi_payload,
                            value_patterns, 
                            payload_options.get('aggregate_cols_general', []), 
                            payload_options.get('keep_cols_general', []) + ['sys_layout_valid'], 
                            overlap_threshold,
                            project_id))

    if multiprocess:
        with multiprocessing.Pool(processes=num_processes) as pool:

            for chunk_no, args in enumerate(worker_args, start=1):
                pages = args[1]
                print(f"Starting worker (f{chunk_no}/{len(worker_args)}) - no. pages to process {len(pages)}, pages [{pages[0]}, ... {pages[-1]}]")
                # Pass the worker result and some batch info,
                # Note this will pass `result` implicity into partial
                # so no need to specify `result` param
                pool.apply_async(worker, args, callback=partial(process_worker_result, chunk_no, len(pages), ))

            # Waits for all async results to complete processing
            pool.close()
            pool.join()

            logger.info(r"Progress: 100.00% completed")
    else:
        # Single-threaded processing
        logger.warning("Multiprocessing not enabled for ROI extraction. Expect GUI slowdown if using app")
        # for chunk_no, (start_page, end_page) in enumerate(pages_per_worker):
        for chunk_no, args in enumerate(worker_args, start=1):
            pages = args[1]
            print(f"Starting worker (f{chunk_no}/{len(worker_args)}) - no. pages to process {len(pages)}, pages [{pages[0]}, ... {pages[-1]}]")
            result = worker(*args)
            process_worker_result(chunk_no=chunk_no, pages_processed=len(pages), result=result)

    try:
        # Check for missing pages in combined_bom_df and combined_general_df
        expected_pages = set(range(1, total_pages + 1))  # 1-based page numbers

        bom_pages = set(combined_bom_df['pdf_page'].unique())
        general_pages = set(combined_general_df['pdf_page'].unique())

        missing_bom_pages = expected_pages - bom_pages
        missing_general_pages = expected_pages - general_pages

        if missing_bom_pages:
            print(f"\n\n--> Pages missing from combined_bom_df: {sorted(missing_bom_pages)}")
        else:
            print("--> All pages are present in combined_bom_df.")

        if missing_general_pages:
            print(f"--> Pages missing from combined_general_df: {sorted(missing_general_pages)}")
        else:
            print("--> All pages are present in combined_general_df.")
    except Exception as e:
        logger.error(f"Error checking expected BOM/General pages: {e}")

    return combined_bom_df, combined_spec_df, combined_spool_df, combined_raw_df, combined_general_df, combined_outlier_df, consolidated_skipped_pages, total_pages

def print_metrics(total_pages, extract_time):
    pages_per_second_app = (total_pages)  / extract_time if extract_time > 0 else 0
    # logger.info(f"Current App Performance: Processes {pages_per_second_app} pages/second")
    print("\n\n-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print(f"Current App Performance: Processes {pages_per_second_app} pages/second")
    # # Given values
    drawings_per_hour_person = 50

    # Converting drawings per hour to drawings per second for a person
    drawings_per_second_person = drawings_per_hour_person / 3600  # 3600 seconds in an hour
    drawings_per_minute_person = drawings_per_hour_person / 60  # 60 minute in an hour

    # Calculating how much faster the app is compared to one person
    speed_factor = pages_per_second_app / drawings_per_second_person

    # Calculating the number of people required to match the speed of the app
    people_needed = pages_per_second_app / drawings_per_second_person

    # Calculating the time it takes for a person to do one drawing (in seconds)
    time_per_drawing_person = 1 / drawings_per_second_person

    speed_factor, people_needed, time_per_drawing_person
    # logger.info("\n\n-------------------------------------------------------------------------------------------")
    # logger.info(f"Processed {total_pages} in {extract_time:.2f} seconds.")
    # logger.info(f"Average Person one drawing = {time_per_drawing_person:.2f} s.")
    # logger.info(f"Average Person: {drawings_per_minute_person:.2f} drawings /min.")
    # logger.info(f"Average Person: {drawings_per_hour_person} /hr")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info(f"AIS Pages per second: {pages_per_second_app:.2f}")
    # logger.info(f"AIS is approximately {speed_factor:.2f} times faster.")
    # logger.info(f"It would take about {people_needed:.0f} people to match the speed of the AIS.")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info("-------------------------------------------------------------------------------------------")

    print(f"Processed {total_pages} in {extract_time:.2f} seconds.")
    print(f"Average Person one drawing = {time_per_drawing_person:.2f} s.")
    print(f"Average Person: {drawings_per_minute_person:.2f} drawings /min.")
    print(f"Average Person: {drawings_per_hour_person} /hr")
    print("-------------------------------------------------------------------------------------------")
    print(f"AIS Pages per second: {pages_per_second_app:.2f}")
    print(f"AIS is approximately {speed_factor:.2f} times faster.")
    print(f"It would take about {people_needed:.0f} people to match the speed of the AIS.")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")

    print("Check and update data debug log:\n\n", check_debug_log)

def run(job_id, # uuid of AtemJob
        pdf_path,
        roi_payload, 
        payload_options, 
        project_id, 
        parent_dir=None, 
        sub_dir=None, 
        debug_page_limit=None, 
        analyze_bom=False, 
        rename_files=False,
        multiprocess: bool = ROI_EXTRACTION_MULTIPROCESS):

    cancel_event: threading.Event = threading.Event()

    def cancel_extraction(id: int):
        if job_id != id:
            return
        cancel_event.set()

    # Not Implemented
    # pub.subscribe(cancel_extraction, "cancel-job")

    # print(">>>>> Rotation: ", rotation) # TO REMOVE
    pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Starting Extraction", jobId=job_id)
    directory = None 

    # Ensure work_dir and work_project are defined
    work_project = parent_dir if parent_dir is not None else 'AIS Default'
    sub_folder = sub_dir if sub_dir is not None else 'AIS Project'
    
    #Adjustments
    x_offset = 0
    y_offset = 0
    overlap_threshold=.2 # -> .70.85 -> .92
    
    st_extract = time.time()
    
    #Load JSON Values
    value_patterns = {
        "elevation": [
            "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
            "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
            "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\"",
            "Z [-+]?\\d+",
            "EL [-+]?\\d+'-\\d+( \\d+/\\d+)?\"",
            "EL [-+]?[0-9]+\\.?[0-9]*"
        ],
        "x_coordinate": [
            "X [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
            "X [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
            "X [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\"",
            "[X|Y] [-+]?(\\d+' ?)?\\d+\\.\\d+/\\d+\\\"",
            "[X|Y] [-+]?(\\d+\\.?\\d*/?\\d+)"
        ],
        "y_coordinate": [
            "Y [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
            "Y [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
            "Y [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\""
        ]
    }

    payload_options = load_json(payload_options)
    try:
        roi_payload = load_json(roi_payload)
    except Exception as e:
        logger.info("...")
        logger.info("...")
        logger.info("Direct json format used. Loading a json file not neccessary...")
        logger.info("...")
        logger.info("...")

    # roi_payload = {
    #     "groupRois": {int(group): layout for group, layout in roi_payload["groupRois"].items()},
    #     "pageToGroup": {int(page): int(group) for page, group in roi_payload["pageToGroup"].items()},
    # }

    # import pprint as pp
    # pp.pprint(roi_payload)
    run_extract_dir = False #Change to True to run extraction for full directory
    
    # Construct the path for the "_work" directory in the user's home directory
    work_dir = os.path.join(os.path.expanduser('~'), 'AIS_work_dir')
    
    # Construct the path for the "work_project" subdirectory
    project_dir = os.path.join(work_dir, work_project)
    
    # Create the directories if they don't exist
    os.makedirs(project_dir, exist_ok=True) 
    
    sub_dir = os.path.join(project_dir, sub_folder)
    
    # Create the directories if they don't exist
    os.makedirs(project_dir, exist_ok=True)
    
    # Create a 'docs' folder inside your project_dir
    docs_dir = os.path.join(sub_dir, 'docs')
    os.makedirs(docs_dir, exist_ok=True)

    res = run_extraction(pdf_path, roi_payload, value_patterns, payload_options, sub_dir, x_offset, y_offset, overlap_threshold, project_id, debug_page_limit, multiprocess)
    combined_bom_df, combined_spec_df, combined_spool_df, combined_raw_df, combined_general_df, combined_outlier_df, consolidated_skipped_pages, total_pages = res

    # print(combined_general_df)
    # print("12")
    # print(f"12- combined_bom_df: {len(combined_bom_df)}")

    # Check if the consolidated_skipped_pages dictionary has at least one item
    if consolidated_skipped_pages:
        print("\n\nSkipped pages and their sizes (Handle by fetching the pages from the database and loading each size for ROI Selection):")
        for page_number, size in consolidated_skipped_pages.items():
            print(f"Page {page_number}: Width x Height = {size[0]} x {size[1]}")
    else:
        print("No pages were skipped.")
    
    # Ensure that the necessary columns exist in the DataFrame
    if 'elevation' in combined_general_df.columns:
        # Add or initialize 'min_elevation', 'max_elevation', and 'avg_elevation' columns
        combined_general_df['min_elevation'] = np.nan
        combined_general_df['max_elevation'] = np.nan
        combined_general_df['avg_elevation'] = np.nan
        
        # Check if 'pdf_page' column exists in the DataFrame
        if 'pdf_id' in combined_general_df.columns:
            combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = combined_general_df.apply(
                lambda row: calculate_elevation_metrics(row, row['pdf_id']), axis=1, result_type='expand'
            )
        else:
            logger.warning("'pdf_id' column not found in the DataFrame. Calculating elevation metrics without page information.")
            combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = combined_general_df.apply(
                calculate_elevation_metrics, axis=1, result_type='expand'
            )

        # combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = combined_general_df.apply(
        #     calculate_elevation_metrics, axis=1, result_type='expand'
        # )
    else:
        # Handle the case where 'elevation' column does not exist
         logger.debug("'elevation' column not found in the DataFrame.")
        
    
    # Replace 'nan' (string), np.nan and pd.NA with blank ('') in the DataFrames
    combined_bom_df = combined_bom_df.replace(['nan', np.nan, pd.NA], '')
    combined_spec_df = combined_spec_df.replace(['nan', np.nan, pd.NA], '')
    combined_spool_df = combined_spool_df.replace(['nan', np.nan, pd.NA], '')
    combined_general_df = combined_general_df.replace(['nan', np.nan, pd.NA], '')
    combined_raw_df = combined_raw_df.replace(['nan', np.nan, pd.NA], '')
    combined_outlier_df = combined_outlier_df.replace(['nan', np.nan, pd.NA], '')

    
    if BOM_IMPORT:
        imported_bom_df = pd.read_excel('imported_bom.xlsx') # Import from excel
        combined_bom_df = merge_bom_data(combined_general_df, imported_bom_df) # Merge PDF ID and system information
        #combined_bom_df.to_excel("BOM ASCII Validate.xlsx")
        
    if GENERAL_IMPORT:
        # Instantiate the DatabaseManager
        db_manager = DatabaseManager()

        highest_pdf_id = db_manager.get_highest_pdf_id()
    
        # Assign new pdf_ids based on unique pdf_page values
        unique_pdf_pages = combined_bom_df['pdf_page'].unique()
        pdf_page_to_id = {page: highest_pdf_id + i + 1 for i, page in enumerate(unique_pdf_pages)}

        # Update the DataFrame with the new pdf_id
        combined_bom_df['pdf_id'] = combined_bom_df['pdf_page'].map(pdf_page_to_id)

        imported_general_df = pd.read_excel('excel_006_gen.xlsx') # Import from excel
        #print("\n\nImported General DF 1: ", len(imported_general_df) )

        # imported_general_df.to_excel("imported_general_test1.xlsx")
        # combined_bom_df.to_excel("combined_bom_df.xlsx")
        
        # Define columns to merge
        columns_to_merge = ['sys_build', 'sys_path', 'pdf_id']

        # Merge the datas
        combined_general_df = merge_general_data(combined_bom_df, imported_general_df, columns_to_merge) # Merge PDF ID and system information

        #imported_general_df.to_excel("imported_general_test2.xlsx")
        #print("\n\nImported General DF 2: ", len(combined_general_df) )

        
    # Apply the function to the entire DataFrame
    try:
        combined_bom_df = combined_bom_df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)
    except Exception as e:
        logger.warning(f"Could not replace non standard characters in combined BOM: {e}")
        
    try:
        combined_general_df = combined_general_df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)
    except Exception as e:
        logger.warning(f"Could not replace non standard characters in combined General: {e}")
    
    #print("\n\n\nImported General DF 2: ", len(combined_general_df) )

    #End Extraction Timer
    et_extract = time.time()
    extract_time = et_extract - st_extract

    pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Data extracted")

    #Logging
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info(f"\n\nData Consolidated... \nGeneral Data Rows: {len(combined_general_df)}\nBOM Table Rows: {len(combined_bom_df)}\nSPOOL Table Rows: {len(combined_spool_df)}\nSPEC Table Rows: {len(combined_spec_df)}\nRAW Data Rows: {len(combined_raw_df)}")
    
    print_metrics(total_pages, extract_time)

    # if rename_files == False:
    #     export_large_data_to_excel(combined_general_df, 'General Data.xlsx', sub_dir)
        
    # export_large_data_to_excel(combined_bom_df, 'BOM Data.xlsx', sub_dir)  
    # export_large_data_to_excel(combined_outlier_df, 'Outlier Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_raw_df, 'consolidated_raw_data.xlsx', sub_dir)

    if rename_files == True and platform.system() == 'Windows':
        logger.debug("rename_files = True. Rename the files")
        #Start the timer
        start_time3 =  time.time()
            
        #Call Function to Split the PDF Pages
        combined_general_df = split_pdf(pdf_path, docs_dir, combined_general_df, debug_page_limit)
            
        #Prompt for rename
        # Assuming you have a DataFrame named combined_general_df
        general_df_columns = combined_general_df.columns.tolist()
            
        df_holder = DataFrameHolder()
        launch_window(combined_general_df, docs_dir, df_holder.handle_modified_dataframe)
            
        # Wait for the callback to complete
        df_holder.wait_for_callback()

        # Use the df_holder.modified_df here
        if df_holder.modified_df is not None:
            logger.debug("Using the modified DataFrame:")
            #print(df_holder.modified_df)
              
        #End Timer
        end_time3 = time.time()
        pdf_split_time = end_time3 - start_time3
            
        logger.debug(f"Split PDF Time for {total_pages} pages: {pdf_split_time}")
        
        #export_large_data_to_excel(df_holder.modified_df, 'General Data.xlsx', sub_dir)

#--------------------------------------------------------------
#--------------------------------------------------------------
    #print(f"\n\n--BOM TEST: \n{len(combined_bom_df)}")
    #Get Lists from json
    merge_to_bom = payload_options.get('merge_to_bom', [])
    bom_merge_on = payload_options.get('bom_merge_on', [])

    # Filter out the columns that do not exist in combined_general_df
    bom_merge_on = [col for col in bom_merge_on if col in combined_general_df.columns]
    merge_to_bom = [col for col in merge_to_bom if col in combined_general_df.columns]

    # Merge columns to BOM using 'merge_to_bom' list
    if not combined_bom_df.empty and not combined_general_df.empty :
        try:
            combined_bom_df = combined_bom_df.merge(combined_general_df[merge_to_bom + bom_merge_on], 
                                        on=bom_merge_on, 
                                        how='left')
        except Exception as e:
            logger.error(f"An error occurred when combining BOM: {e}")
            
 
    # Combine SPEC tables and General Data
    try:
        if len(combined_general_df) > 0 and len(combined_spec_df) > 0:
            
            # Check if 'size' field exists in combined_spec_df
            if 'size' in combined_spec_df.columns:
                # Apply parse_complex_size function to the 'size' column
                combined_spec_df['size'] = combined_spec_df['size'].apply(parse_complex_size)
        
            # Call the helper function to merge 'combined_spec_df' and 'combined_general_df'
            combined_general_df = merge_spec_and_general_data(combined_spec_df, combined_general_df)
    except Exception as e:
        logger.warning(f"Error checking general data length: {e}", exc_info = True)        


    # --> Trim all columns
    combined_raw_df = trim_all_columns(combined_raw_df)

    combined_bom_df = trim_all_columns(combined_bom_df)
    combined_spec_df = trim_all_columns(combined_spec_df)
    combined_spool_df = trim_all_columns(combined_spool_df)
    
    combined_general_df = trim_all_columns(combined_general_df)
    combined_outlier_df = trim_all_columns(combined_outlier_df)

    print(f"\n---------------------\nTOTAL BOM ROWS: {len(combined_bom_df)}")
    print(f"TOTAL GENERAL ROWS: {len(combined_general_df)}\n---------------------\n")

    if INSPECT_RAW:
        print("\n\n-------------------")
        print("-------------------")
        print(f"DEBUG: Exporting Raw data. Rows={len(combined_raw_df)}")
        print("-------------------")
        combined_raw_df.to_excel("_debug_raw_data_full.xlsx")
        print("--> RAW DATA EXPORTED - TESTING")
        
    if EXPORT_OUTLIER:
        print("\n\n-------------------")
        print("-------------------")
        print("-------------------")
        
        combined_outlier_df.to_excel("_outlier_data_full.xlsx")
        print("--> OUTLIER DATA EXPORTED - TESTING")

    

#------------------------------------------------------------------------------GPT ANALYZE

    pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Data Preprocessing", jobId=job_id)

    #Analyze Data; Get values with GPT4 Advanced Data Analysis
    if analyze_bom and len(combined_bom_df) > 0: #If 'material_description' in dataframe
        if confirm_analysis():  # Prompt user for confirmation
            ai_start_time = time.time()
            print("\n\n--Analyzing the data...\n")
            #combined_bom_df = trim_all_columns2(combined_bom_df)  # Trim dataframe columns if necessary

            # Create a copy of the DataFrame to ensure the original is not modified
            preprocessed_df = combined_bom_df.copy()
        
            # Retain only 'material_description' and 'nps_od' columns before dropping duplicates
            preprocessed_df = preprocessed_df[['material_description', 'nps_od']]

            # Drop rows where 'material_description' is null or blank
            preprocessed_df = preprocessed_df.dropna(subset=['material_description'])
            preprocessed_df = preprocessed_df[preprocessed_df['material_description'].str.strip() != '']

            # Drop duplicate rows based on the combination of 'material_description' and 'nps_od'
            preprocessed_df = preprocessed_df.drop_duplicates()

            print("\n\nPREPROCESS: \n", preprocessed_df)

        
            gpt4_turbo = Gpt4Turbo()  # Create an instance of the Gpt4Turbo class
            gpt_bom_results = analyze_bom_data(preprocessed_df, gpt4_turbo)  # Call the function with the dataframe and the Gpt4Turbo instance

            # Flatten the nested 'classification' into the main dictionary for each item
            flattened_data = []
            for response_wrapper in gpt_bom_results:  # Here, gpt_bom_results is your entire JSON structure
                try:
                    for item in response_wrapper['responses']:
                        classification_info = item.pop('classification', {})  # Safely remove and get 'classification'
                        item.update(classification_info)  # Merge the classification info into the main dictionary
                        flattened_data.append(item)
                    
                except Exception as e:
                    for item in response_wrapper['results']:
                        classification_info = item.pop('classification', {})  # Safely remove and get 'classification'
                        item.update(classification_info)  # Merge the classification info into the main dictionary
                        flattened_data.append(item)

            # Create the DataFrame from the flattened data
            gpt_bom_results_df = pd.DataFrame(flattened_data)
        
            # Assuming gpt_bom_results is the result of the analyze_bom_data function
            #gpt_bom_results_df = pd.DataFrame(gpt_bom_results)

            # Specify the filename for the Excel workbook
            filename = getDataTempPath("combined_results.xlsx")

            # Using ExcelWriter to write multiple DataFrames to separate sheets
            #with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # Write the gpt_bom_results DataFrame to the first sheet
                #gpt_bom_results_df.to_excel(writer, sheet_name='GPT_BOM_Results', index=False)
    
                # Write the preprocessed_df DataFrame to the second sheet
                #preprocessed_df.to_excel(writer, sheet_name='Preprocessed_Data', index=False)
        
            ai_end_time = time.time()
        
            # Print the time taken to preprocess the DataFrame
            print("\n\nTime taken to preprocess DataFrame: {:.2f} seconds".format(ai_end_time - ai_start_time, "\n"))

            print(f"Results and preprocessed data have been exported to {filename}")

    # print(cancel_event.is_set())
    # if cancel_event.is_set():
    #     raise Exception("Cancelled")
        
#-------------------------------------------------------------------------------------------

    # --> JSON Logic no longer need <-- Verify that it can be deleted

    # # Convert each DataFrame to a JSON string and then load it as a Python dictionary
    # bom_data_json = json.loads(combined_bom_df.to_json(orient='records'))
    # raw_data_json = json.loads(combined_raw_df.to_json(orient='records'))
    # outlier_data_json = json.loads(combined_outlier_df.to_json(orient='records'))

    # --> Reintegrate rename function to use database instead
    # if rename_files == True and platform.system() == 'Windows':
    #     general_data_json = json.loads(df_holder.modified_df.to_json(orient='records'))
    #     general_data_df = df_holder.modified_df
    #     #export_large_data_to_excel(df_holder.modified_df, 'General Data.xlsx', sub_dir)
    # else:
    #     general_data_json = json.loads(combined_general_df.to_json(orient='records'))
    #     general_data_df = combined_general_df
        
    # Set the output file path, you can modify this as needed
    # output_file_path = os.path.join(sub_dir, 'output.json')

    # Construct the final JSON object with three keys
    # final_json = {
    #     "bom_data": bom_data_json,
    #     "general_data": general_data_json,
    #     #"raw_data": raw_data_json,
    #     "outlier_data": outlier_data_json

    print("TODO - sort dfs as async results are returned in different order of batches")
    # TODO - sort df as async results are returned in different order of batches
    if not combined_bom_df.empty:
        combined_bom_df = combined_bom_df.sort_values(by=['pdf_page'], ascending=True)


    pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Finalizing Result", jobId=job_id)

    final_df = {
        "bom_data": combined_bom_df,
        "spool_data": combined_spool_df,
        "spec_data": combined_spec_df,
        "general_data": combined_general_df, #general_data_df,
        "outlier_data": combined_outlier_df
        #"raw_data": raw_data_json,
    }
    
    table_to_key = {
        "bom": "bom_data",
        "spool": "spool_data",
        "spec": "spec_data",
    }

    first_group_payload = roi_payload["groupRois"][1] # check the first group. All groups should have same rois
    # Ensure originally requested fields are returned in result df 
    for roi in first_group_payload: 
        try:
            columnName = roi["columnName"]
            if "tableCoordinates" in roi:
                df = final_df[table_to_key[columnName.lower()]]
                for tableColumn in roi.get("tableColumns", []):
                    field = list(tableColumn.keys())[0]
                    if field not in df:
                        df[field] = np.nan
            else:
                field = columnName
                df = final_df["general_data"]
                if field not in df:
                    df[field] = np.nan
        except Exception as e:
            logger.debug("Failed to insert requested field to final dataframe", exc_info=True)

    # with open(output_file_path, 'w') as file:
    #     json.dump(final_json, file)
    #     logger.debug(f"Exported json data to: {output_file_path}")

    #Export dataframes
    #export_large_data_to_excel(combined_bom_df, 'BOM Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_general_df, 'General Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_raw_df, 'Raw Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_outlier_df, 'Outlier Data.xlsx', sub_dir)
    
    end_time1 = time.time()

    # Filter out JSON objects that have the 'tableCoordinates' property
    #print(f"\n\n--BOM TEST 4: \n{len(combined_bom_df)}")
    try:
        print("\n\n\n CLEANING BOM USING OUTLIER DATA")
        filtered_json = [item for item in first_group_payload if 'tableCoordinates' not in item]

        # Extract the 'columnName' values
        json_column_names = {item['columnName'] for item in filtered_json}

        # Find the intersection with DataFrame columns
        matching_columns = set(combined_general_df.columns).intersection(json_column_names)

        # Convert to a list if needed
        matching_columns_list = list(matching_columns)
        
        # Sort the matching column names list in ascending order, case-insensitive
        sorted_columns_list = sorted(matching_columns_list, key=lambda s: s.lower())
        print(sorted_columns_list)
    
        #Clean data using the outlier data. 
    
    except Exception as e:
        logger.error(f"An Exception occured while trying to parse the JSON coordinates. EXCEPTION: {e}")
        return

    logger.info("--> EXTRACTION/ASSIGNMENT PROCESS COMPLETE")

    if COMMIT_RAW:
        print(f"\n---------------------\nSaving Raw Data to Parquet/Feather: {len(combined_raw_df)} Rows\n---------------------")
        # Convert all object columns to string
        for col in combined_raw_df.select_dtypes(['object']):
            combined_raw_df[col] = combined_raw_df[col].astype(str)

        # Option 1: Save as Parquet
        output_path = r"debug\raw_data"  # Adjust path as needed
        save_df_fast(combined_raw_df, output_path, format='feather')

        print("Raw Data Saved")

    return final_df, sorted_columns_list
   
def parse_arguments():
    #Useage: python main_mp.py "path/to/pdf.pdf" "path/to/roi_payload.json" "path/to/payload_options.json"
    #python main_mp.py "C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" "C:/Users/<USER>/source/repos/AIS_MarkupExtractor/Linde BCH1 Coords.json" "C:/Users/<USER>/source/repos/AIS_MarkupExtractor/payload_options.json"
    if len(sys.argv) > 1:  # Check if any arguments were passed
        parser = argparse.ArgumentParser(description='Process a PDF file.')
        parser.add_argument('pdf_path', type=str, help='Path to the PDF file')
        parser.add_argument('roi_payload', type=str, help='Path to the JSON file containing ROI payload')
        parser.add_argument('payload_options', type=str, help='Path to the JSON file containing payload options')
        parser.add_argument('parent_dir', type=str, help='Name of Parent Folder ex. Company Name Etc.')
        parser.add_argument('sub_dir', type=str, help='Name of Sub Parent Folder ex. Project Name, Job, Etc.')
        parser.add_argument('--debug_page_limit', type=int, default=None, help='Debug page limit (optional)')
        parser.add_argument('--analyze_bom', type=int, default=False, help='Run advanced analysis on returned bom data (optional)')
        parser.add_argument('--rename_files', type=int, default=None, help='Custom rename files (optional)')
        return parser.parse_args()
    else:
        # Default values for running in IDE
        class Args:
            pdf_path = r"c:\Users\<USER>\Documents\Drawings\PLNG BOG Drawings.pdf"
            roi_payload = convert_roi_payload.convert_roi_payload(r'c:\Users\<USER>\Documents\Drawings\PLNG BOG Drawings.json')
            #values_patterns = 'value_patterns.json'
            parent_dir = "Testing"
            sub_dir = "GPT"
            payload_options = 'src/atom/payload_options.json'
            debug_page_limit = None
            rename_files = False
            analyze_bom=True
            multiprocess=False
        return Args()

###------------------------------------->>>
if __name__ == '__main__':
    sys.path[0] = ""  # For relative resource paths
    args = parse_arguments()
    run(job_id=None,
        pdf_path=args.pdf_path, 
        roi_payload=args.roi_payload, 
        payload_options=args.payload_options, 
        project_id=None,
        parent_dir=args.parent_dir,
        sub_dir=args.sub_dir,
        debug_page_limit=args.debug_page_limit,
        analyze_bom=args.analyze_bom,
        rename_files=args.rename_files,
        multiprocess=args.multiprocess)