import os
import pandas as pd
from natsort import natsort_keygen

def plugin_clean_gemini_elevations(input_file: str, save_file:str="debug/gemini_elevations_cleaned.xlsx"):
    """
    Ensure that columns are named
    """

    df = pd.read_excel(input_file)
    df["cleaned"] = None

    # Clean records
    df_clean = df[df["source_file"].str.startswith("Isometric Drawing")]
    df_unclean = df[~df["source_file"].str.startswith("Isometric Drawing")]

    cleaned = []

    columns = ["source_file", "pdf_page", "a", "b", "c", "d", "e", "f"]
    columns = [c for c in columns if c in df_clean.columns]
    for _, row in df_unclean.iterrows():
        elevations = [row["elevation"]]
        pdf_page = None
        for c in columns:
            v = row[c]
            if "isometric" in str(v).lower():
                continue
            if not v:
                continue

            if "el" not in str(v).lower():
                try:
                    pdf_page = int(v)
                    continue
                except:
                    pass

            else:
                elevations.append(v)

        elevation_new = ";".join(elevations)

        cleaned.append({
            "elevation": elevation_new,
            "pdf_page": pdf_page,
            "cleaned": True
        })


    df_unclean = pd.DataFrame(cleaned)

    df_new = pd.concat([df_clean, df_unclean], ignore_index=True)
    df_new.sort_values("pdf_page", key=natsort_keygen(), inplace=True)

    df_new[["pdf_page", "elevation", "cleaned"]].to_excel(save_file)

    return f"saved to {save_file}"