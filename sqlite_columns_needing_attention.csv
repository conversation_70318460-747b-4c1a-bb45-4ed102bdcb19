SQLite Column,PostgreSQL Equivalent,Status,Notes
sysDocument,sys_document,POTENTIALLY MISSING,Check if this column exists in PostgreSQL with the snake_case name
sysDocumentName,sys_document_name,POTENTIALLY MISSING,Check if this column exists in PostgreSQL with the snake_case name
x<PERSON><PERSON><PERSON>,x_coord,POTENTIALLY MISSING,Check if this column exists in PostgreSQL with the snake_case name
yCoord,y_coord,POTENTIALLY MISSING,Check if this column exists in PostgreSQL with the snake_case name
lf,length,RENAMED,Column exists in PostgreSQL but was renamed from 'lf' to 'length'
sf,calculated_area,RENAMED,Column exists in PostgreSQL but was renamed from 'sf' to 'calculated_area'
ef,calculated_eq_length,RENAMED,Column exists in PostgreSQL but was renamed from 'ef' to 'calculated_eq_length'
