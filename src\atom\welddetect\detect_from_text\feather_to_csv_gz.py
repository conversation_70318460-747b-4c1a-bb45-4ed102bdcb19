import pandas as pd


def convert_to_compressed_csv(input_file, output_file):
    # Read feather file
    df = pd.read_feather(input_file)

    # Save as gzip-compressed CSV
    df.to_csv(output_file, index=False, compression='gzip')

def convert_subset(input_file, output_file):
    # Load the compressed CSV file
    df = pd.read_csv(input_file)

    # Filter data for page 1370
    page_1370_df = df[df['pdf_page'] == 1370]

    # Save to a new CSV file
    page_1370_df.to_csv(output_file, index=False)