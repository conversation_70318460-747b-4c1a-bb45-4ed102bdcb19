
    <!DOCTYPE html>
    <html>
    <head>
        <title>Piping Material Classification Examples</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .example { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .description { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
            .highlighted { margin: 10px 0; padding: 10px; background-color: #f9f9f9; border-radius: 3px; }
            .explanation { margin-top: 10px; font-size: 14px; color: #666; }
            .classification-type { font-weight: bold; color: #333; }
            .legend { margin: 20px 0; padding: 15px; background-color: #f0f0f0; border-radius: 5px; }
            .legend-item { display: inline-block; margin: 5px 10px; padding: 3px 8px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>Piping Material Classification Examples</h1>
        <div class="legend">
            <h3>Color Legend:</h3>
            <span class="legend-item" style="background-color: #FFE6E6;">Schedule</span>
            <span class="legend-item" style="background-color: #E6F3FF;">Rating</span>
            <span class="legend-item" style="background-color: #E6FFE6;">ASTM</span>
            <span class="legend-item" style="background-color: #FFF0E6;">Grade</span>
            <span class="legend-item" style="background-color: #F0E6FF;">End Types</span>
            <span class="legend-item" style="background-color: #FFFFE6;">Manufacturing</span>
        </div>
    
        <div class="example">
            <h3>Example 1: Complete pipe specification with multiple indicators</h3>
            <div class="description">Original: Pipe, 150lb, sch 10s, pe x be, seamless astm 106b</div>
            <div class="highlighted">Pipe, <span style="background-color: #E6F3FF; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="rating: 150 | Pattern: Pressure unit suffix | Found pressure rating "150LB" using Pressure unit suffix pattern, maps to rating "150"" data-classification="rating">150LB</span>, <span style="background-color: #FFE6E6; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="schedule: 10S | Pattern: SCH prefix pattern | Found explicit schedule designation "SCH 10S" which maps to schedule value "10S"" data-classification="schedule">SCH 10S</span>, pe x be, seamless astm 106b</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Schedule:</span>
                10S - Found explicit schedule designation "SCH 10S" which maps to schedule value "10S"<br>
            
                <span class="classification-type">Rating:</span>
                150 - Found pressure rating "150LB" using Pressure unit suffix pattern, maps to rating "150"<br>
            </div></div>
        <div class="example">
            <h3>Example 2: Flange identification with rating and material</h3>
            <div class="description">Original: SO Flange RF 300# A105 4"</div>
            <div class="highlighted">SO Flange RF <span style="background-color: #E6F3FF; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="rating: 300 | Pattern: Hash suffix (300#) | Found pressure rating "300#" using Hash suffix (300#) pattern, maps to rating "300"" data-classification="rating">300#</span> A105 4"</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Rating:</span>
                300 - Found pressure rating "300#" using Hash suffix (300#) pattern, maps to rating "300"<br>
            </div></div>
        <div class="example">
            <h3>Example 3: Valve classification with end type determination</h3>
            <div class="description">Original: Ball Valve 800# A105 SW 2"</div>
            <div class="highlighted">Ball Valve <span style="background-color: #E6F3FF; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="rating: 800 | Pattern: Hash suffix (300#) | Found pressure rating "800#" using Hash suffix (300#) pattern, maps to rating "800"" data-classification="rating">800#</span> A105 SW 2"</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Rating:</span>
                800 - Found pressure rating "800#" using Hash suffix (300#) pattern, maps to rating "800"<br>
            </div></div>
        <div class="example">
            <h3>Example 4: Fitting with ASTM/grade combination and standard</h3>
            <div class="description">Original: 90 LR Elbow A234 WPB SCH 40 BE B16.9</div>
            <div class="highlighted">90 LR Elbow A234 WPB <span style="background-color: #FFE6E6; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="schedule: 40 | Pattern: SCH prefix pattern | Found explicit schedule designation "SCH 40" which maps to schedule value "40"" data-classification="schedule">SCH 40</span> BE B16.9</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Schedule:</span>
                40 - Found explicit schedule designation "SCH 40" which maps to schedule value "40"<br>
            </div></div>
        <div class="example">
            <h3>Example 5: Common AI misclassification - nipple vs pipe</h3>
            <div class="description">Original: 2" NIPPLE SCH 40 A106 GR B SMLS</div>
            <div class="highlighted">2" NIPPLE <span style="background-color: #FFE6E6; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="schedule: 40 | Pattern: SCH prefix pattern | Found explicit schedule designation "SCH 40" which maps to schedule value "40"" data-classification="schedule">SCH 40</span> A106 GR B SMLS</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Schedule:</span>
                40 - Found explicit schedule designation "SCH 40" which maps to schedule value "40"<br>
            </div></div>
        <div class="example">
            <h3>Example 6: Wall thickness as schedule indicator</h3>
            <div class="description">Original: WALL THICKNESS 0.250" PIPE A312 TP316L</div>
            <div class="highlighted"><span style="background-color: #FFE6E6; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="schedule: 0.250 | Pattern: Wall thickness pattern | Found wall thickness specification "WALL THICKNESS 0.250" which indicates schedule value "0.250"" data-classification="schedule">WALL THICKNESS 0.250</span>" PIPE A312 TP316L</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Schedule:</span>
                0.250 - Found wall thickness specification "WALL THICKNESS 0.250" which indicates schedule value "0.250"<br>
            </div></div>
        <div class="example">
            <h3>Example 7: F-prefix grade indicating forged manufacturing</h3>
            <div class="description">Original: F316 FLANGE WN 150# RF</div>
            <div class="highlighted">F316 FLANGE WN <span style="background-color: #E6F3FF; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" title="rating: 150 | Pattern: Hash suffix (300#) | Found pressure rating "150#" using Hash suffix (300#) pattern, maps to rating "150"" data-classification="rating">150#</span> RF</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        
                <span class="classification-type">Rating:</span>
                150 - Found pressure rating "150#" using Hash suffix (300#) pattern, maps to rating "150"<br>
            </div></div>
        <div class="example">
            <h3>Example 8: Part number requiring human review</h3>
            <div class="description">Original: PART NO: ABC-123-XYZ</div>
            <div class="highlighted">PART NO: ABC-123-XYZ</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        </div></div></body></html>