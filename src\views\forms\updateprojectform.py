from src.utils.logger import logger
from PySide6.QtWidgets import QLabel, QPushButton, QLineEdit, QSizePolicy, QMessageBox
from PySide6.QtGui import QAction
from .baseform import BaseForm
from pubsub import pub
from src.pyside_util import get_resource_qicon

PROJECT_NAME_DEFAULT_MESSAGE = "Project Name *"
PROJECT_ID_DEFAULT_MESSAGE = "Project ID *"
PROJECT_LOCATION_DEFAULT_MESSAGE = "Project Location"
ENGINEER_DRAFTER_DEFAULT_MESSAGE = "Engineering/Drafter Company of Drawings"


class UpdateProjectForm(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)
        self.filename: str = None
        self._defaults = {}

    def initUi(self):
        self.formSize.setHeight(400)

        self.title.setText("Update Project Details")
        self.subtitle.clear()
        self.subtitle.hide()

        self.addVSpace()

        self.lblProjectName = QLabel(PROJECT_NAME_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectName)
        self.projectName = QLineEdit()
        revertName = self.projectName.addAction(get_resource_qicon("corner-up-left.svg"), QLineEdit.TrailingPosition)
        revertName.triggered.connect(lambda: self.revertField("projectName"))
        self.layout().addRow(self.projectName)
    
        self.addVSpace()

        self.lblProjectId = QLabel(PROJECT_ID_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectId)
        self.projectId = QLineEdit()
        revertId = self.projectId.addAction(get_resource_qicon("corner-up-left.svg"), QLineEdit.TrailingPosition)
        revertId.triggered.connect(lambda: self.revertField("projectNumber"))
        self.layout().addRow(self.projectId)
    
        self.addVSpace()

        self.lblProjectLocation = QLabel(PROJECT_LOCATION_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectLocation)
        self.projectLocation = QLineEdit()
        revertLocation = self.projectLocation.addAction(get_resource_qicon("corner-up-left.svg"), QLineEdit.TrailingPosition)
        revertLocation.triggered.connect(lambda: self.revertField("projectLocation"))
        self.layout().addRow(self.projectLocation)

        self.addStretchSpacer()
        self.addErrorStatusWidget()
        pbNext = QPushButton("Update")
        pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        pbNext.setMinimumHeight(48)
        pbNext.clicked.connect(self.onNext)
        self.layout().addRow(pbNext)

        pbNext.setContentsMargins(0, 132, 0, 32)

        self.setFloatingButtonCancel()

    def initDefaults(self):
        self._defaults = {}
        self.projectName.clear()
        self.projectId.clear()
        self.projectLocation.clear()

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onNext(self, event):
        """Validate user input"""
        ok = True
        projectName = self.projectName.text()
        if not projectName:
            self.lblProjectName.setText(PROJECT_NAME_DEFAULT_MESSAGE.replace("*", "required"))
            self.setWidgetError(self.lblProjectName)
            ok = False
        else:
            self.setWidgetDefault(self.lblProjectName)

        projectId = self.projectId.text()
        if not projectId:
            self.lblProjectId.setText(PROJECT_ID_DEFAULT_MESSAGE.replace("*", "required"))
            self.setWidgetError(self.lblProjectId)
            ok = False
        else:
            self.setWidgetDefault(self.lblProjectId)

        # Details are valid? Move on to explorer form
        projectData = {}
        projectData.update(self.getData())

        def callback(res):
            if res["status"] == "ok":
                QMessageBox.information(self, 
                                        "Update Project", 
                                        "Project details updated")
                self.onFloatingButton()
            else:
                QMessageBox.information(self, 
                                        "Update Project", 
                                        f"Update project details failed: {res['error']}")

        pub.sendMessage("project-update", data=projectData, callback=callback)

    def getData(self) -> dict:
        return {
            "projectId": self._defaults["id"],
            "projectName": self.projectName.text(),
            "projectNumber": self.projectId.text(),
            "projectLocation": self.projectLocation.text(),
        }

    def onFloatingButton(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def setParams(self, params):
        self.initDefaults()
        data = params["data"]
        self._defaults = data
        self.projectName.setText(data["projectName"])
        self.projectId.setText(data["projectNumber"])
        self.projectLocation.setText(data["projectLocation"])

    def revertField(self, key):
        if key == "projectName":
            self.projectName.setText(self._defaults["projectName"])
        elif key == "projectLocation":
            self.projectLocation.setText(self._defaults["projectLocation"])
        elif key == "projectNumber":
            self.projectId.setText(self._defaults["projectNumber"]) 