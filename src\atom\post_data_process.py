import pandas as pd
import numpy as np
from ast import literal_eval
import os, time
import fitz, json

def get_pdf_dimensions(pdf_path):
    # Open the PDF file
    pdf_document = fitz.open(pdf_path)
    
    # Get the first page
    first_page = pdf_document[0]
    
    # Get the width and height of the page
    width = first_page.rect.width
    height = first_page.rect.height
    
    # Close the PDF document
    pdf_document.close()
    
    return width, height

def load_json_file(file_path):
    try:
        with open(file_path, 'r') as file:
            content = file.read().strip()
            if not content:
                raise ValueError("JSON file is empty")
            return json.loads(content)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return []
    except Exception as e:
        print(f"Error loading JSON file {file_path}: {e}")
        return []

# def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):
#     if isinstance(roi_payload, str):
#         try:
#             roi_payload = json.loads(roi_payload)
#         except Exception as e:
#             #print("Error: ", roi_payload)
#             roi_payload = roi_payload
#             #return roi_payload

def ocr_convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):
    if isinstance(roi_payload, str):
        roi_payload = json.loads(roi_payload)
    
    converted_payload = []
    for item in roi_payload:
        converted_item = {"columnName": item["columnName"]}
        
        # Check if it's a table
        if "tableCoordinates" in item:
            coords = item["tableCoordinates"]
        else:
            coords = item
            
        if "relativeX0" in coords:
            converted_item.update({
                "x0": int(coords["relativeX0"] * width + x_offset),
                "y0": int(coords["relativeY0"] * height + y_offset),
                "x1": int(coords["relativeX1"] * width + x_offset),
                "y1": int(coords["relativeY1"] * height + y_offset)
            })
        
        
        converted_payload.append(converted_item)
    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))
        
def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }    
    
def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):

    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        print(f"Failed to parse ROI payload: {e}")
        return []

    converted_payload = []
    
    for item in roi_payload:
        try:
            converted_item = {"columnName": item.get("columnName", "Unknown")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            if "headersSelected" in item:
                converted_item["headersSelected"] = item["headersSelected"]


            converted_payload.append(converted_item)
        except Exception as e:
            print(f"Error processing item {item.get('columnName', 'Unknown')}: {e}")
    
    # print('\n\nCONVERTED COORDS')
    # pp(converted_payload)

    return converted_payload

def extract_images_pdf(pdf_path, ocr_guide, output_images_path='outputImages', dpi=5000, max_pages=None):
    doc = fitz.open(pdf_path)
    
    if not os.path.exists(output_images_path):
        os.makedirs(output_images_path)
    
    # Ensure BOM and General subdirectories exist
    bom_path = os.path.join(output_images_path, "BOM")
    general_path = os.path.join(output_images_path, "General")
    
    for path in [bom_path, general_path]:
        if not os.path.exists(path):
            os.makedirs(path)
    
    # Clear .png files from BOM and General folders
    for folder in [bom_path, general_path]:
        for file in os.listdir(folder):
            if file.lower().endswith('.png'):
                os.remove(os.path.join(folder, file))
    
    zoom = dpi / 72  # standard PDF resolution is 72 DPI
    matrix = fitz.Matrix(zoom, zoom)
    
    all_pages_data_storage = []
    expected_png_count = 0
    
    start_time = time.time()
    
    for page_num in ocr_guide:
        i = int(page_num) - 1  # OCR guide uses 1-indexed pages, fitz uses 0-indexed
        print(f">>> Processing PAGE: {i+1}")
        page = doc[i]
        page_data_storage = {'Page': i + 1}
        
        # Process general fields
        for field in ocr_guide[page_num]['general_fields']:
            try:
                crop_rect = fitz.Rect(*field['coords'])
                pix = page.get_pixmap(matrix=matrix, clip=crop_rect)
                output_path = os.path.join(general_path, f"{field['name']}_page_{i+1}.png")
                pix.save(output_path)
                page_data_storage[field['name']] = output_path
                expected_png_count += 1
            except Exception as e:
                print(f"Error processing field '{field['name']}' on page {i+1}: {e}")
        
        # Process BOM if needed
        if ocr_guide[page_num]['bom']:
            try:
                bom_coords = ocr_guide[page_num]['bom_coords']
                crop_rect = fitz.Rect(bom_coords['x0'], bom_coords['y0'], bom_coords['x1'], bom_coords['y1'])
                pix = page.get_pixmap(matrix=matrix, clip=crop_rect)
                output_path = os.path.join(bom_path, f"BOM_page_{i+1}.png")
                pix.save(output_path)
                page_data_storage['BOM'] = output_path
                expected_png_count += 1
            except Exception as e:
                print(f"Error processing BOM on page {i+1}: {e}")
        
        all_pages_data_storage.append(page_data_storage)
        
    end_time = time.time()
    total_time = end_time - start_time
    total_pages = len(ocr_guide)
    pages_per_second = total_pages / total_time if total_time > 0 else 0

    print(f"\nProcessed {total_pages} pages in {total_time:.2f} seconds.")
    print(f"Processing speed was {pages_per_second:.2f} pages per second.")
    
    df = pd.DataFrame(all_pages_data_storage)
    df.to_excel('outputData.xlsx', index=False)
    print("Data exported to 'outputData.xlsx'.")

    # Verify the number of generated PNG files
    actual_png_count = sum(len([f for f in os.listdir(d) if f.endswith('.png')]) for d in [bom_path, general_path])
    print(f"Expected PNG files: {expected_png_count}")
    print(f"Actual PNG files: {actual_png_count}")
    if expected_png_count != actual_png_count:
        print("WARNING: The number of generated PNG files does not match the expected count!")

    return df

def create_ocr_guide(no_value_found_report, bom_analysis, converted_payload):
    ocr_guide = {}
    
    # Process general fields
    for page, fields in no_value_found_report.items():
        ocr_guide[page] = {
            'general_fields': fields,
            'bom': False
        }
    
    # Process BOM
    for page in bom_analysis['missing_pages']:
        if page not in ocr_guide:
            ocr_guide[page] = {'general_fields': [], 'bom': True}
        else:
            ocr_guide[page]['bom'] = True
    
    # Add coordinate information
    field_coords = {item['columnName']: (item['x0'], item['y0'], item['x1'], item['y1']) 
                    for item in converted_payload 
                    if 'columnName' in item and 'tableCoordinates' not in item}
    
    try:
        bom_coords = next((item['tableCoordinates'] for item in converted_payload if item['columnName'] == 'BOM'), None)
    except Exception as e:
        print(field_coords)
    
    for page, data in ocr_guide.items():
        if data['general_fields']:
            data['general_fields'] = [{
                'name': field,
                'coords': field_coords.get(field, None)
            } for field in data['general_fields']]
        if data['bom'] and bom_coords:
            data['bom_coords'] = bom_coords
    
    return ocr_guide

def analyze_bom_pages(total_pages, bom_df):
    bom_pages = set(bom_df['pdf_page'].unique())
    all_pages = set(range(1, total_pages + 1))
    missing_pages = all_pages - bom_pages

    bom_analysis = {
        'total_pages': total_pages,
        'pages_with_bom': len(bom_pages),
        'pages_missing_bom': len(missing_pages),
        'missing_pages': sorted(list(missing_pages))
    }

    return bom_analysis

def consolidate_found_values(found_values_report, total_pages):
    consolidated_data = []
    for page in range(1, total_pages + 1):
        if page in found_values_report:
            page_data = {'pdf_page': page}
            page_data.update(found_values_report[page]['values'])
            consolidated_data.append(page_data)
    return pd.DataFrame(consolidated_data)

def find_values_with_expanded_coords(converted_payload, raw_df, missing_fields_report, expansion=3.5):
    def is_within_expanded_range(coord, adjusted_coords):
        x0, y0, x1, y1 = coord
        ax0, ay0, ax1, ay1 = adjusted_coords
        return (ax0 <= x0 and x1 <= ax1 and
                ay0 <= y0 and y1 <= ay1)

    field_coords = {item['columnName']: (item['x0'], item['y0'], item['x1'], item['y1']) 
                    for item in converted_payload 
                    if 'columnName' in item and 'tableCoordinates' not in item}

    found_values_report = {}
    no_value_found_report = {}

    for page, missing_fields in missing_fields_report.items():
        raw_df_page = raw_df[raw_df['pdf_page'] == page]
        found_values = {}
        multiple_values_flag = {}

        #print(f"\nPage {page} Adjusted Coordinates:")
        for field_name in missing_fields:
            if field_name in field_coords:
                field_coords_tuple = field_coords[field_name]
                fx0, fy0, fx1, fy1 = field_coords_tuple
                adjusted_coords = (fx0 - expansion, fy0 - expansion, fx1 + expansion, fy1 + expansion)
                #print(f"  {field_name}: Original {field_coords_tuple}, Adjusted {adjusted_coords}")
                
                for _, row in raw_df_page.iterrows():
                    row_coords = literal_eval(row['coordinates2'])
                    #print(f"Checking coordinates: {row_coords}")  # Debug print
                    if is_within_expanded_range(row_coords, adjusted_coords):
                        if field_name not in found_values:
                            found_values[field_name] = []
                        # Use the actual value, not str(row['value'])
                        found_values[field_name].append(row['value'])
                        #print(f"    Found: {row['value']} at {row_coords}")
                    # else:
                    #     print(f"    Not within range: {row['value']} at {row_coords}")  # Debug print

                if field_name in found_values and len(found_values[field_name]) > 1:
                    multiple_values_flag[field_name] = True

        if found_values:
            found_values_report[page] = {
                'values': found_values,
                'multiple_values': multiple_values_flag
            }
            
        no_value_found = [field for field in missing_fields if field not in found_values]
        if no_value_found:
            no_value_found_report[page] = no_value_found

    return found_values_report, no_value_found_report

def analyze_missed_fields(total_pages, converted_payload, general_df):
    fields_to_check = [item['columnName'] for item in converted_payload if 'columnName' in item and 'tableCoordinates' not in item]
    fields_to_check = [field for field in fields_to_check if field in general_df.columns]

    missing_fields_report = {}

    for page in range(1, total_pages + 1):
        page_df = general_df[general_df['pdf_page'] == page]
        
        if page_df.empty:
            continue

        missing_fields = []
        for field in fields_to_check:
            if field not in page_df.columns or page_df[field].isnull().all() or (page_df[field] == '').all():
                missing_fields.append(field)
        
        if missing_fields:
            missing_fields_report[page] = missing_fields

    return missing_fields_report

def read_excel_with_na(file_path):
    return pd.read_excel(file_path, 
                         keep_default_na=False,
                         na_values=[''])  # This will treat empty cells as NaN, but keep 'N/A' as a string

start_time = time.time()

# Load json
json_path = os.path.join(r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\temp", "oci_coordinates.json")

# Load dataframes from Excel workbooks
raw_df = read_excel_with_na(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\Data\COMBINED RAW DF.xlsx")
general_df = read_excel_with_na(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\Data\converted_general_grp2.xlsx")
bom_df = read_excel_with_na(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\Data\converted_bom_grp2.xlsx")
pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\Modified\Iso Group Edit_group_2.pdf"

# Load JSON data from the file
roi_payload = load_json_file(json_path)

bm1 = time.time()
print(f"Dataframes saved to variables. Time: {bm1 - start_time:.2f} seconds")

# Get Dimensions
width, height = get_pdf_dimensions(pdf_path)

# convert Coordinates to absolute
converted_payload = convert_relative_coords_to_points(roi_payload, width, height )

ocr_converted_payload = convert_relative_coords_to_points(roi_payload, width, height )


#converted_payload = [{'columnName': 'BOM', 'tableCoordinates': {'x0': 868.0, 'y0': 56.5, 'x1': 1171.5, 'y1': 612.5}, 'tableColumns': [{'pos': {'x0': 868.0, 'y0': 56.5, 'x1': 897.9999999999999, 'y1': 612.5}}, {'quantity': {'x0': 897.9999999999999, 'y0': 56.5, 'x1': 937.5000000000001, 'y1': 612.5}}, {'size': {'x0': 937.5000000000001, 'y0': 56.5, 'x1': 980.0, 'y1': 612.5}}, {'material_description': {'x0': 980.0, 'y0': 56.5, 'x1': 1171.5, 'y1': 612.5}}], 'headersSelected': True}, {'columnName': 'pid', 'x0': 584.0, 'y0': 667.5, 'x1': 695.5, 'y1': 678.5}, {'columnName': 'pipeSpec', 'x0': 583.5, 'y0': 680.0, 'x1': 695.5, 'y1': 692.0}, {'columnName': 'test_type', 'x0': 583.5, 'y0': 706.0, 'x1': 695.5, 'y1': 717.5}, {'columnName': 'test_psi', 'x0': 583.5, 'y0': 719.0, 'x1': 694.5, 'y1': 730.5}, {'columnName': 'xray', 'x0': 584.0, 'y0': 731.0, 'x1': 696.0, 'y1': 743.5}, {'columnName': 'pwht', 'x0': 583.5, 'y0': 744.5, 'x1': 695.5, 'y1': 754.0}, {'columnName': 'service', 'x0': 756.0, 'y0': 667.0, 'x1': 867.0, 'y1': 679.0}, {'columnName': 'paintSpec', 'x0': 755.0, 'y0': 680.0, 'x1': 867.0, 'y1': 691.5}, {'columnName': 'op1', 'x0': 755.0, 'y0': 693.5, 'x1': 797.0, 'y1': 704.0}, {'columnName': 'opt1', 'x0': 825.0000000000001, 'y0': 693.5, 'x1': 856.5, 'y1': 705.0}, {'columnName': 'dp1', 'x0': 755.0, 'y0': 705.5, 'x1': 797.0, 'y1': 717.5}, {'columnName': 'dt1', 'x0': 825.0000000000001, 'y0': 706.5, 'x1': 856.0, 'y1': 717.5}, {'columnName': 'heatTrace', 'x0': 755.0, 'y0': 718.5, 'x1': 866.5, 'y1': 730.5}, {'columnName': 'insulationSpec', 'x0': 755.0, 'y0': 731.5, 'x1': 866.5, 'y1': 743.5}, {'columnName': 'insulationThickness', 'x0': 755.0, 'y0': 744.0, 'x1': 866.5, 'y1': 754.0}, {'columnName': 'projectName', 'x0': 868.5, 'y0': 672.0, 'x1': 1171.5, 'y1': 687.5}, {'columnName': 'projectNo', 'x0': 868.0, 'y0': 693.0, 'x1': 1046.5, 'y1': 708.0}, {'columnName': 'vendorDocumentId', 'x0': 1048.0, 'y0': 693.5, 'x1': 1170.5, 'y1': 707.5}, {'columnName': 'lineNumber', 'x0': 885.5, 'y0': 725.0, 'x1': 1046.0, 'y1': 739.0}, {'columnName': 'drawing', 'x0': 902.0, 'y0': 740.5, 'x1': 1138.0, 'y1': 754.0}, {'columnName': 'revision', 'x0': 1140.5, 'y0': 744.0, 'x1': 1172.0, 'y1': 754.5}, {'columnName': 'area', 'x0': 1048.5, 'y0': 729.0, 'x1': 1082.5, 'y1': 739.0}, {'columnName': 'unit', 'x0': 1084.0, 'y0': 729.0, 'x1': 1124.0, 'y1': 739.0}, {'columnName': 'clean_spec', 'x0': 1125.0, 'y0': 728.5, 'x1': 1172.0, 'y1': 739.0}]

# Call the main function
total_pages = general_df['pdf_page'].max()

bom_analysis = analyze_bom_pages(total_pages, bom_df)

# Optionally, save the BOM analysis to a file
with open('bom_analysis.txt', 'w') as f:
    f.write(f"Total pages: {bom_analysis['total_pages']}\n")
    f.write(f"Pages with BOM data: {bom_analysis['pages_with_bom']}\n")
    f.write(f"Pages missing BOM data: {bom_analysis['pages_missing_bom']}\n")
    f.write(f"Missing pages: {', '.join(map(str, bom_analysis['missing_pages']))}\n")

print("BOM analysis saved to 'bom_analysis.txt'")

print("\nBOM Analysis:")
print(f"Total pages: {bom_analysis['total_pages']}")
print(f"Pages with BOM data: {bom_analysis['pages_with_bom']}")
print(f"Pages missing BOM data: {bom_analysis['pages_missing_bom']}")
print("Missing pages:", ', '.join(map(str, bom_analysis['missing_pages'])))

missing_fields_report = analyze_missed_fields(total_pages, converted_payload, general_df)

print("Missing fields report:", missing_fields_report)

bm2 = time.time()
print(f"Time taken for analyze_missed_fields: {bm2 - bm1:.2f} seconds")

# Check if there are any missing fields
if any(missing_fields_report.values()):
    # After calling find_values_with_expanded_coords
    # After calling find_values_with_expanded_coords
    found_values_report, no_value_found_report  = find_values_with_expanded_coords(converted_payload, raw_df, missing_fields_report)
    
    bm3 = time.time()
    print(f"Time taken for find_values_with_expanded_coords: {bm3 - bm2:.2f} seconds")

    # Consolidate found values into a dataframe
    consolidated_df = consolidate_found_values(found_values_report, total_pages)
    
    print("CONSOL:", consolidated_df.columns)
    print("GEN:", general_df.columns)

    # Merge consolidated values back into general_df
    if len(consolidated_df)>0:
        general_df = general_df.merge(consolidated_df, on='pdf_page', how='left', suffixes=('', '_found'))

    # Update general_df with found values
    for column in consolidated_df.columns:
        if column != 'pdf_page':
            general_df[column] = general_df[column].fillna(general_df[f'{column}_found'])
            general_df = general_df.drop(columns=[f'{column}_found'])

    # Save updated general_df
    general_df.to_excel("updated_general_df.xlsx", index=False)
    print("Updated general dataframe saved to 'updated_general_df.xlsx'")

    # Print report of pages and fields where no value was found
    print("\nPages and fields where no value was found:")
    for page, fields in no_value_found_report.items():
        print(f"Page {page}: {', '.join(fields)}")

    # Save no value found report to a file
    with open('no_value_found_report.txt', 'w') as f:
        for page, fields in no_value_found_report.items():
            f.write(f"Page {page}: {', '.join(fields)}\n")
    print("No value found report saved to 'no_value_found_report.txt'")

    for page, data in found_values_report.items():
        print(f"\nPage {page} Found Values:")
        for field, values in data['values'].items():
            print(f"  {field}: {', '.join(values)}")
            if field in data['multiple_values']:
                print(f"    WARNING: Multiple values found for {field}")
else:
    print("No missing fields to process.")
    general_df.to_excel("_General.xlsx")
    

# After running your analysis
ocr_guide = create_ocr_guide(no_value_found_report, bom_analysis, converted_payload)

# Save the OCR guide to a JSON file for later use
import json
with open('ocr_guide.json', 'w') as f:
    json.dump(ocr_guide, f, indent=2)
print("OCR guide saved to 'ocr_guide.json'")


# Usage
with open('ocr_guide.json', 'r') as f:
    ocr_guide = json.load(f)

pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\Modified\Iso Group Edit_group_1.pdf"
output_image_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 007\outputImages"

extract_images_pdf(pdf_path, ocr_guide, output_image_path, 500)
   
print(f"Total time taken: {bm3 - start_time:.2f} seconds")



# # Print the report
# for page, fields in missing_fields_report.items():
#     print(f"Page {page} missing fields: {', '.join(fields)}")
    
# bm3 = time.time()
# print(f"Time taken for printing report: {bm3 - bm2:.2f} seconds")

# # Optionally, save the report to a file
# with open('missing_fields_report.txt', 'w') as f:
#     for page, fields in missing_fields_report.items():
#         f.write(f"Page {page} missing fields: {', '.join(fields)}\n")

# bm4 = time.time()
# print(f"Time taken for saving report to file: {bm4 - bm3:.2f} seconds")

# print("Report saved to 'missing_fields_report.txt'")

# print(f"Total time taken: {bm4 - start_time:.2f} seconds")















# Call the main function
# total_pages = max(general_df['pdf_page'].max(), raw_df['pdf_page'].max())
# updated_general_df = analyze_missed_fields(total_pages, converted_payload, raw_df, general_df, bom_df)

# Save the updated dataframe
# updated_general_df.to_excel('updated_general_df.xlsx', index=False)


# # works kinda but is slow
# def analyze_missed_fields(total_pages, converted_payload, raw_df, general_df, bom_df):
#     def get_center_point(coords):
#         x0, y0, x1, y1 = coords
#         return ((x0 + x1) / 2, (y0 + y1) / 2)

#     def check_field(page_df, raw_df_page, field_name, field_coords):
#         if field_name not in page_df.columns or page_df[field_name].isnull().all():
#             field_center = get_center_point(field_coords)

#             for _, row in raw_df_page.iterrows():
#                 text_center = get_center_point(row['coordinates2'])
#                 if (abs(text_center[0] - field_center[0]) < 10 and
#                     abs(text_center[1] - field_center[1]) < 5):
#                     page_df.loc[page_df.index[0], field_name] = row['value']
#                     break

#         return page_df

#     updated_general_df = general_df.copy()

#     # Extract field information from converted_payload
#     fields_to_check = [item for item in converted_payload if 'columnName' in item and 'tableCoordinates' not in item]

#     for page in range(1, total_pages + 1):
#         page_df = updated_general_df[updated_general_df['pdf_page'] == page]
#         raw_df_page = raw_df[raw_df['pdf_page'] == page]

#         if page_df.empty:
#             continue

#         for field in fields_to_check:
#             field_name = field['columnName']
#             field_coords = (field['x0'], field['y0'], field['x1'], field['y1'])
            
#         print(f"Page: {page} Missing Fields: {f}")
#             #page_df = check_field(page_df, raw_df_page, field_name, field_coords)

#         #updated_general_df.update(page_df)

#     return updated_general_df
