from data_conversions import convert_quantity_to_float


def test_conversion():
    """Add conversion cases here"""

    # 19
    a = convert_quantity_to_float("19'\"")
    assert round(a, 3) == 19, round(a, 3)

    # 19.833
    a = convert_quantity_to_float("19'-10\"")
    assert round(a, 3) == 19.833, round(a, 3)

    # 21.25
    a = convert_quantity_to_float("21'-3\"")
    assert round(a, 3) == 21.25, round(a, 3)

    # 11"
    # a = convert_quantity_to_float("11\"")
    # assert round(a, 3) == 11, round(a, 3)

    # 0.25
    a = convert_quantity_to_float("1/4")
    assert round(a, 3) == 0.25, round(a, 3)

    # 12/4
    a = convert_quantity_to_float("12/4")
    assert round(a, 3) == 3, round(a, 3)


if __name__ == "__main__":

    a = convert_quantity_to_float('3/4')
    print(a)

    a = convert_quantity_to_float("19'-10\"")
    # assert round(a, 3) == 11, round(a, 3)

    a = convert_quantity_to_float('11\"')
    print(a)



    a = convert_quantity_to_float('4\'11.1/8"')
    print(a)

    a = convert_quantity_to_float('4\'11')
    print(a)


    a = convert_quantity_to_float('8.13/16"')
    print(a)


    a = convert_quantity_to_float('8.1/2"')
    print(a)

    a = convert_quantity_to_float('9\'3.1/2"')
    print(a)

    # Test with double quote
    a = convert_quantity_to_float('4\'11.11/1 6"')
    print(a)

    # Test with single quote
    a = convert_quantity_to_float('4\'11.11/1 6\'')
    print(a)

    # Test with trailing character and space in fraction
    a = convert_quantity_to_float('101\'11.13 /16" B')
    print(a)
    # Test with trailing character and space in fraction
    a = convert_quantity_to_float('101\'11.13 /16" Casas')
    print(a)

    # import pandas as pd
    # df = pd.read_excel(r"C:\Users\<USER>\Architekt\exported\shady hills\unedited_exported_bom_data.xlsx")

    # print(df["Quantity"].unique())

    # quantities = pd.DataFrame({"quantity": df["Quantity"].unique()})
    # quantities["converted"] = quantities["quantity"].apply(convert_quantity_to_float)

    # quantities= quantities.sort_values("quantity")
    # quantities.to_excel("quantities_converted.xlsx", index=False)
