'''
Apply a template of ROIs to subsequent pages with similar layout but potentially different scaling.

This module builds on the adjust_coords_to_template.py functionality to:
1. Load a template with ROIs and their coordinates
2. Detect grid structures in both template and target pages
3. Match structures between pages using clustering and pattern recognition
4. Transform ROI coordinates from template to target
5. Calculate confidence scores for each match
6. Generate visualizations and output files (including Excel for confidence scores)
'''
import os
import cv2
import json
import numpy as np
import pandas as pd
from sklearn.cluster import DBSCAN
from scipy.spatial import distance
from collections import defaultdict

# Import existing functionality
from src.atom.vision.detect_text_regions.read_roi import extract_roi_data_from_json
from src.atom.vision.detect_text_regions.detect_grid_regions_cv2 import detect_grid_lines
from src.atom.vision.detect_text_regions.adjust_coords_to_template import adjust_coords_to_template


def apply_template_to_page(template_json_path, target_image_path, output_dir=None, debug=True):
    """
    Apply a template of ROIs to a new page, adjusting for different scaling.

    Args:
        template_json_path: Path to the template JSON file with ROIs
        target_image_path: Path to the target image to apply the template to
        output_dir: Directory to save output files
        debug: Whether to enable debug mode (default is now True)

    Returns:
        Dictionary containing adjusted regions, confidence scores, and visualization paths
    """
    print(f"\n=== STARTING TEMPLATE APPLICATION ===\nTemplate: {template_json_path}\nTarget: {target_image_path}")

    if output_dir is None:
        output_dir = os.path.dirname(target_image_path)

    # Create a specific debug output directory
    debug_dir = os.path.join(output_dir, 'debug_apply_template')
    os.makedirs(debug_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    # Save function parameters for debugging
    with open(os.path.join(debug_dir, 'parameters.txt'), 'w') as f:
        f.write(f"Template JSON: {template_json_path}\n")
        f.write(f"Target Image: {target_image_path}\n")
        f.write(f"Output Directory: {output_dir}\n")
        f.write(f"Debug Mode: {debug}\n")

    # Step 1: Load the template data
    try:
        print(f"Loading template data from: {template_json_path}")
        with open(template_json_path, 'r') as f:
            template_data = json.load(f)

        # Debug: Print template data structure
        print(f"Template data keys: {list(template_data.keys())}")

        # Extract template ROIs
        template_rois = {}

        # Check for standard structure with groups and rois
        if 'groups' in template_data:
            print("Found standard template structure with 'groups' key")
            for group_id, group_data in template_data['groups'].items():
                if 'rois' in group_data:
                    template_rois[group_id] = group_data['rois']
                    print(f"  Found {len(group_data['rois'])} ROIs in group {group_id}")

        # Check for alternative structure with rectangles
        elif 'rectangles' in template_data:
            print(f"Found alternative template structure with 'rectangles' key ({len(template_data['rectangles'])} rectangles)")
            # Convert rectangles to ROI format
            template_rois = convert_rectangles_to_rois(template_data['rectangles'])
            print(f"  Converted to {sum(len(rois) for rois in template_rois.values())} ROIs")

            # Save the converted template for debugging
            debug_template_path = os.path.join(debug_dir, 'converted_template.json')
            with open(debug_template_path, 'w') as f:
                json.dump({'groups': template_rois}, f, indent=4)
            print(f"  Saved converted template to: {debug_template_path}")

        if not template_rois:
            print("No valid ROIs found in template JSON.")
            return None

    except Exception as e:
        print(f"Error loading template data: {e}")
        import traceback
        traceback.print_exc()
        return None

    # Step 2: Load the target image
    target_img = cv2.imread(target_image_path)
    if target_img is None:
        raise ValueError(f"Failed to load target image: {target_image_path}")

    print(f"Target image loaded successfully. Dimensions: {target_img.shape}")

    # Save a copy of the original target image for debugging
    original_target_path = os.path.join(debug_dir, 'original_target.png')
    cv2.imwrite(original_target_path, target_img)
    print(f"Original target image saved to: {original_target_path}")

    # Step 3: Detect grid regions in both template and target
    print("\n=== DETECTING GRID REGIONS ===")
    print(f"Detecting grid regions in template image...")
    template_image_path = template_data.get('image_path')
    if not template_image_path or not os.path.isfile(template_image_path):
        print(f"Error: Template image path not found or invalid: {template_image_path}")
        return None

    template_img = cv2.imread(template_image_path)
    if template_img is None:
        print(f"Error: Could not read template image at '{template_image_path}'")
        return None

    template_grid_results = detect_grid_lines(template_img, debug=debug, debug_dir=debug_dir)
    template_rectangles = template_grid_results['rectangles']
    print(f"Template rectangles detected: {len(template_rectangles)}")

    # Save template rectangles visualization
    template_rect_vis = template_img.copy()
    for rect in template_rectangles:
        x, y, w, h, rect_id = rect
        cv2.rectangle(template_rect_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(template_rect_vis, str(rect_id), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    template_rect_path = os.path.join(debug_dir, 'template_rectangles.png')
    cv2.imwrite(template_rect_path, template_rect_vis)
    print(f"Template rectangles visualization saved to: {template_rect_path}")

    print(f"\nDetecting grid regions in target image...")
    target_grid_results = detect_grid_lines(target_img, debug=debug, debug_dir=debug_dir)
    target_rectangles = target_grid_results['rectangles']
    print(f"Target rectangles detected: {len(target_rectangles)}")

    # Save target rectangles visualization
    target_rect_vis = target_img.copy()
    for rect in target_rectangles:
        x, y, w, h, rect_id = rect
        cv2.rectangle(target_rect_vis, (x, y), (x + w, y + h), (0, 0, 255), 2)
        cv2.putText(target_rect_vis, str(rect_id), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    target_rect_path = os.path.join(debug_dir, 'target_rectangles.png')
    cv2.imwrite(target_rect_path, target_rect_vis)
    print(f"Target rectangles visualization saved to: {target_rect_path}")

    # Save detailed rectangle information
    with open(os.path.join(debug_dir, 'rectangles_info.txt'), 'w') as f:
        f.write(f"=== TEMPLATE RECTANGLES ({len(template_rectangles)}) ===\n")
        for i, rect in enumerate(template_rectangles):
            x, y, w, h, rect_id = rect
            f.write(f"Rectangle {i}: ID={rect_id}, x={x}, y={y}, w={w}, h={h}\n")

        f.write(f"\n=== TARGET RECTANGLES ({len(target_rectangles)}) ===\n")
        for i, rect in enumerate(target_rectangles):
            x, y, w, h, rect_id = rect
            f.write(f"Rectangle {i}: ID={rect_id}, x={x}, y={y}, w={w}, h={h}\n")

    # Step 4: Cluster rectangles
    print("\n=== CLUSTERING RECTANGLES ===")
    print("Clustering template rectangles...")
    template_clusters = cluster_rectangles(template_rectangles)
    print(f"Template clusters created: {len(template_clusters)}")

    print("Clustering target rectangles...")
    target_clusters = cluster_rectangles(target_rectangles)
    print(f"Target clusters created: {len(target_clusters)}")

    # Save cluster information
    with open(os.path.join(debug_dir, 'clusters_info.txt'), 'w') as f:
        f.write(f"=== TEMPLATE CLUSTERS ({len(template_clusters)}) ===\n")
        for cluster_id, rects in template_clusters.items():
            f.write(f"Cluster {cluster_id}: {len(rects)} rectangles\n")
            for rect in rects:
                x, y, w, h, rect_id = rect
                f.write(f"  Rectangle ID={rect_id}, x={x}, y={y}, w={w}, h={h}\n")

        f.write(f"\n=== TARGET CLUSTERS ({len(target_clusters)}) ===\n")
        for cluster_id, rects in target_clusters.items():
            f.write(f"Cluster {cluster_id}: {len(rects)} rectangles\n")
            for rect in rects:
                x, y, w, h, rect_id = rect
                f.write(f"  Rectangle ID={rect_id}, x={x}, y={y}, w={w}, h={h}\n")

    # Create cluster visualizations
    template_cluster_vis = template_img.copy()
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (0, 255, 255), (255, 0, 255)]

    for i, (cluster_id, rects) in enumerate(template_clusters.items()):
        color = colors[i % len(colors)]
        for rect in rects:
            x, y, w, h, rect_id = rect
            cv2.rectangle(template_cluster_vis, (x, y), (x + w, y + h), color, 2)
            cv2.putText(template_cluster_vis, f"C{cluster_id}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    template_cluster_path = os.path.join(debug_dir, 'template_clusters.png')
    cv2.imwrite(template_cluster_path, template_cluster_vis)

    target_cluster_vis = target_img.copy()
    for i, (cluster_id, rects) in enumerate(target_clusters.items()):
        color = colors[i % len(colors)]
        for rect in rects:
            x, y, w, h, rect_id = rect
            cv2.rectangle(target_cluster_vis, (x, y), (x + w, y + h), color, 2)
            cv2.putText(target_cluster_vis, f"C{cluster_id}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    target_cluster_path = os.path.join(debug_dir, 'target_clusters.png')
    cv2.imwrite(target_cluster_path, target_cluster_vis)

    # Step 5: Match clusters between template and target
    print("\n=== MATCHING CLUSTERS ===")
    cluster_matches, match_scores = match_clusters(template_clusters, target_clusters)
    print(f"Cluster matches found: {len(cluster_matches)}")

    # Save match information
    with open(os.path.join(debug_dir, 'matches_info.txt'), 'w') as f:
        f.write(f"=== CLUSTER MATCHES ({len(cluster_matches)}) ===\n")
        for template_id, target_id in cluster_matches.items():
            score = match_scores.get((template_id, target_id), 0)
            f.write(f"Template Cluster {template_id} -> Target Cluster {target_id} (Score: {score:.4f})\n")
            f.write(f"  Template Cluster {template_id}: {len(template_clusters[template_id])} rectangles\n")
            f.write(f"  Target Cluster {target_id}: {len(target_clusters[target_id])} rectangles\n")

    # Create match visualization
    # Save the grid detection results for both template and target
    template_grid_vis = template_grid_results.get('combined_image', np.zeros_like(template_img))
    target_grid_vis = target_grid_results.get('combined_image', np.zeros_like(target_img))

    # Convert to color if needed
    if len(template_grid_vis.shape) == 2:
        template_grid_vis = cv2.cvtColor(template_grid_vis, cv2.COLOR_GRAY2BGR)
    if len(target_grid_vis.shape) == 2:
        target_grid_vis = cv2.cvtColor(target_grid_vis, cv2.COLOR_GRAY2BGR)

    # Resize images to the same height if needed
    t_height, t_width = template_img.shape[:2]
    target_height, target_width = target_img.shape[:2]

    if t_height != target_height:
        scale_factor = target_height / t_height
        resized_template = cv2.resize(template_img, (int(t_width * scale_factor), target_height))
        resized_template_grid = cv2.resize(template_grid_vis, (int(t_width * scale_factor), target_height))
    else:
        resized_template = template_img
        resized_template_grid = template_grid_vis

    # Save grid detection visualizations
    cv2.imwrite(os.path.join(debug_dir, 'template_grid_detection.png'), resized_template_grid)
    cv2.imwrite(os.path.join(debug_dir, 'target_grid_detection.png'), target_grid_vis)

    # Create side-by-side visualization
    match_vis = np.hstack((resized_template, target_img))
    h, w = resized_template.shape[:2]

    for template_id, target_id in cluster_matches.items():
        color = colors[template_id % len(colors)]

        # Draw template cluster
        for rect in template_clusters[template_id]:
            x, y, w_rect, h_rect, rect_id = rect
            cv2.rectangle(match_vis, (x, y), (x + w_rect, y + h_rect), color, 2)
            cv2.putText(match_vis, f"T{template_id}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # Draw target cluster
        for rect in target_clusters[target_id]:
            x, y, w_rect, h_rect, rect_id = rect
            cv2.rectangle(match_vis, (x + w, y), (x + w + w_rect, y + h_rect), color, 2)
            cv2.putText(match_vis, f"T{target_id}", (x + w, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # Draw connection line between centroids
        template_centroid = calculate_centroid(template_clusters[template_id])
        target_centroid = calculate_centroid(target_clusters[target_id])

        cv2.line(match_vis,
                (int(template_centroid[0]), int(template_centroid[1])),
                (int(target_centroid[0] + w), int(target_centroid[1])),
                color, 1, cv2.LINE_AA)

    match_vis_path = os.path.join(debug_dir, 'cluster_matches.png')
    cv2.imwrite(match_vis_path, match_vis)

    print(f"Matched {len(cluster_matches)} clusters between template and target.")

    # Step 6: Calculate transformations for each matched cluster
    transformations = {}
    for template_idx, target_idx in cluster_matches.items():
        transformations[template_idx] = calculate_transformation(
            template_clusters[template_idx],
            target_clusters[target_idx]
        )

    # Step 7: Transform ROI coordinates from template to target
    adjusted_rois = {}
    confidence_scores = {}

    # Create a visualization image
    vis_img = target_img.copy()
    target_height, target_width = target_img.shape[:2]

    # Process each ROI in the template
    for group_id, rois in template_rois.items():
        adjusted_group_rois = []
        group_scores = {}

        for roi in rois:
            # Get ROI coordinates
            roi_x0 = roi.get('relativeX0', 0)
            roi_y0 = roi.get('relativeY0', 0)
            roi_x1 = roi.get('relativeX1', 1)
            roi_y1 = roi.get('relativeY1', 1)
            roi_name = roi.get('name', 'unnamed')

            # Find which cluster this ROI belongs to
            roi_cluster, cluster_confidence = find_roi_cluster(
                (roi_x0, roi_y0, roi_x1, roi_y1),
                template_clusters
            )

            # If we found a matching cluster and it has a transformation
            if roi_cluster is not None and roi_cluster in transformations:
                # Apply transformation to ROI coordinates
                adjusted_coords, transform_confidence = transform_roi_coordinates(
                    (roi_x0, roi_y0, roi_x1, roi_y1),
                    transformations[roi_cluster]
                )

                # Find the nearest rectangle in the target image and snap to it
                target_img_size = (target_width, target_height)
                snapped_coords, snap_confidence = find_nearest_rectangle(
                    adjusted_coords,
                    target_rectangles,
                    target_img_size
                )

                # Use the snapped coordinates if they're valid, otherwise use the transformed coordinates
                final_coords = snapped_coords

                # Calculate overall confidence score
                cluster_match_score = match_scores.get((roi_cluster, cluster_matches[roi_cluster]), 0.5)
                overall_confidence = (cluster_confidence * 0.3 + transform_confidence * 0.3 +
                                     cluster_match_score * 0.2 + snap_confidence * 0.2)

                # Store adjusted ROI with original properties
                adjusted_roi = roi.copy()
                adjusted_roi.update({
                    'relativeX0': final_coords[0],
                    'relativeY0': final_coords[1],
                    'relativeX1': final_coords[2],
                    'relativeY1': final_coords[3],
                    'confidence': overall_confidence
                })

                adjusted_group_rois.append(adjusted_roi)
                group_scores[roi_name] = overall_confidence

                # Draw on visualization image
                color = get_confidence_color(overall_confidence)

                # Convert final (snapped) coordinates to absolute
                abs_x0 = int(final_coords[0] * target_width)
                abs_y0 = int(final_coords[1] * target_height)
                abs_x1 = int(final_coords[2] * target_width)
                abs_y1 = int(final_coords[3] * target_height)

                # Ensure the coordinates are valid for drawing
                if abs_x0 < abs_x1 and abs_y0 < abs_y1:
                    print(f"  Drawing ROI {roi_name} at ({abs_x0}, {abs_y0}, {abs_x1}, {abs_y1}) with confidence {overall_confidence:.2f}")

                    # Draw with thicker lines for visibility
                    cv2.rectangle(vis_img, (abs_x0, abs_y0), (abs_x1, abs_y1), color, 3)
                    label = f"{roi_name} ({overall_confidence:.2f})"
                    cv2.putText(vis_img, label, (abs_x0, abs_y0 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

                    # Draw the original transformed coordinates for comparison
                    orig_x0 = int(adjusted_coords[0] * target_width)
                    orig_y0 = int(adjusted_coords[1] * target_height)
                    orig_x1 = int(adjusted_coords[2] * target_width)
                    orig_y1 = int(adjusted_coords[3] * target_height)

                    if orig_x0 < orig_x1 and orig_y0 < orig_y1 and (orig_x0 != abs_x0 or orig_y0 != abs_y0 or orig_x1 != abs_x1 or orig_y1 != abs_y1):
                        # Draw with dashed line to show the original transformation
                        draw_dashed_rectangle(vis_img, (orig_x0, orig_y0), (orig_x1, orig_y1), (128, 128, 128), 1)

                    # Save a debug image of this specific ROI
                    roi_debug_img = target_img.copy()
                    cv2.rectangle(roi_debug_img, (abs_x0, abs_y0), (abs_x1, abs_y1), color, 5)
                    cv2.putText(roi_debug_img, label, (abs_x0, abs_y0 - 20),
                               cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 3)
                    roi_debug_path = os.path.join(debug_dir, f'roi_{roi_name}.png')
                    cv2.imwrite(roi_debug_path, roi_debug_img)
                else:
                    print(f"  WARNING: Invalid coordinates for ROI {roi_name}: ({abs_x0}, {abs_y0}, {abs_x1}, {abs_y1})")

                    # Use default coordinates for invalid ROIs
                    default_x0 = int(0.1 * target_width)
                    default_y0 = int(0.1 * target_height)
                    default_x1 = int(0.2 * target_width)
                    default_y1 = int(0.2 * target_height)

                    # Draw with red color to indicate an issue
                    cv2.rectangle(vis_img, (default_x0, default_y0), (default_x1, default_y1), (0, 0, 255), 3)
                    label = f"{roi_name} (INVALID)"
                    cv2.putText(vis_img, label, (default_x0, default_y0 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            else:
                # If no matching cluster, use original coordinates with low confidence
                group_scores[roi_name] = 0.1

        if adjusted_group_rois:
            adjusted_rois[group_id] = adjusted_group_rois
            confidence_scores[group_id] = group_scores

    # Step 8: Save visualization images
    # Create a copy of the target image for original rectangles visualization
    orig_vis_img = target_img.copy()

    # Draw detected rectangles on original visualization
    for cluster_id, rects in target_clusters.items():
        color = (255, 0, 0)  # Blue for detected rectangles
        for rect in rects:
            x, y, w, h, rect_id = rect
            cv2.rectangle(orig_vis_img, (x, y), (x + w, y + h), color, 1)
            cv2.putText(orig_vis_img, str(rect_id), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    # Save original rectangles visualization
    orig_vis_path = os.path.join(output_dir, 'detected_rectangles.png')
    cv2.imwrite(orig_vis_path, orig_vis_img)
    print(f"\nDetected rectangles visualization saved to: {orig_vis_path}")

    # Debug: Check if we have any adjusted ROIs
    print(f"Adjusted ROIs: {len(adjusted_rois)} groups")
    for group_id, rois in adjusted_rois.items():
        print(f"  Group {group_id}: {len(rois)} ROIs")

    # Always draw the template rectangles directly for debugging
    print("Drawing template rectangles directly on the output image.")
    target_height, target_width = target_img.shape[:2]

    # Create a separate debug image to show only the template rectangles
    debug_rect_img = target_img.copy()

    # If we have rectangles in the template data, draw them
    if 'rectangles' in template_data:
        print(f"Drawing {len(template_data['rectangles'])} rectangles from template data")
        for rect in template_data['rectangles']:
            rect_id = rect.get('id', 0)
            rel_x = rect.get('x', 0)
            rel_y = rect.get('y', 0)
            rel_w = rect.get('width', 0)
            rel_h = rect.get('height', 0)

            # Convert relative coordinates to absolute
            abs_x0 = int(rel_x * target_width)
            abs_y0 = int(rel_y * target_height)
            abs_x1 = int((rel_x + rel_w) * target_width)
            abs_y1 = int((rel_y + rel_h) * target_height)

            # Draw rectangle with medium confidence color
            color = get_confidence_color(0.6)  # Medium confidence
            cv2.rectangle(debug_rect_img, (abs_x0, abs_y0), (abs_x1, abs_y1), color, 2)
            label = f"Rectangle_{rect_id}"
            cv2.putText(debug_rect_img, label, (abs_x0, abs_y0 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # Always draw on the main visualization image
            # Use a different color if we have adjusted ROIs
            if not adjusted_rois:
                draw_color = color
            else:
                # Use a lighter color to not interfere with adjusted ROIs
                draw_color = (200, 200, 0)  # Light blue

            cv2.rectangle(vis_img, (abs_x0, abs_y0), (abs_x1, abs_y1), draw_color, 2)
            cv2.putText(vis_img, label, (abs_x0, abs_y0 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, draw_color, 1)

    # Save the debug rectangles image
    debug_rect_path = os.path.join(debug_dir, 'template_rectangles_direct.png')
    cv2.imwrite(debug_rect_path, debug_rect_img)
    print(f"Template rectangles direct visualization saved to: {debug_rect_path}")

    # Save template application visualization
    vis_path = os.path.join(output_dir, 'template_applied.png')
    cv2.imwrite(vis_path, vis_img)
    print(f"Template application visualization saved to: {vis_path}")

    # Create a combined visualization
    combined_img = np.zeros((target_img.shape[0], target_img.shape[1]*2, 3), dtype=np.uint8)
    combined_img[:, :target_img.shape[1]] = orig_vis_img
    combined_img[:, target_img.shape[1]:] = vis_img

    # Add labels
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(combined_img, "Detected Rectangles", (10, 30), font, 1, (255, 255, 255), 2)
    cv2.putText(combined_img, "Applied Template", (target_img.shape[1] + 10, 30), font, 1, (255, 255, 255), 2)

    # Save combined visualization
    combined_vis_path = os.path.join(output_dir, 'combined_visualization.png')
    cv2.imwrite(combined_vis_path, combined_img)
    print(f"Combined visualization saved to: {combined_vis_path}")

    # Step 9: Save adjusted ROIs to JSON
    output_json = {
        'template_path': template_json_path,
        'target_image': target_image_path,
        'groups': {}
    }

    for group_id, rois in adjusted_rois.items():
        output_json['groups'][group_id] = {
            'rois': rois
        }

    # Ensure the output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Normalize the output path to avoid any issues
    output_json_path = os.path.normpath(os.path.join(output_dir, 'adjusted_template_applied.json'))

    try:
        with open(output_json_path, 'w') as f:
            json.dump(output_json, f, indent=4)
        print(f"Adjusted template JSON saved to: {output_json_path}")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        print(f"Attempted to save to: {output_json_path}")
        # Try an alternative location
        alt_output_path = os.path.join(os.path.dirname(__file__), 'adjusted_template_applied.json')
        try:
            with open(alt_output_path, 'w') as f:
                json.dump(output_json, f, indent=4)
            print(f"Adjusted template JSON saved to alternative location: {alt_output_path}")
            output_json_path = alt_output_path
        except Exception as e2:
            print(f"Error saving to alternative location: {e2}")
            output_json_path = None

    # Step 10: Generate Excel file with confidence scores
    generate_confidence_report(confidence_scores, target_image_path, output_dir)

    print("\n=== TEMPLATE APPLICATION COMPLETE ===\n")

    return {
        'adjusted_rois': adjusted_rois,
        'confidence_scores': confidence_scores,
        'visualization_path': vis_path,
        'original_visualization_path': orig_vis_path,
        'combined_visualization_path': combined_vis_path,
        'output_json_path': output_json_path,
        'debug_directory': debug_dir
    }


def cluster_rectangles(rectangles, eps=50, min_samples=2):
    """
    Cluster rectangles based on proximity.

    Args:
        rectangles: List of rectangles in format (x, y, w, h, id)
        eps: DBSCAN epsilon parameter
        min_samples: DBSCAN min_samples parameter

    Returns:
        Dictionary of clusters
    """
    if not rectangles:
        return {}

    # Extract centers of rectangles
    centers = []
    for rect in rectangles:
        x, y, w, h, _ = rect
        center_x = x + w/2
        center_y = y + h/2
        centers.append([center_x, center_y])

    # If not enough rectangles, return a single cluster
    if len(centers) < min_samples:
        return {0: rectangles}

    # Use DBSCAN for clustering
    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(centers)
    labels = clustering.labels_

    # Group rectangles by cluster
    clusters = defaultdict(list)
    for i, rect in enumerate(rectangles):
        cluster_id = labels[i]
        if cluster_id != -1:  # -1 means noise (not part of any cluster)
            clusters[cluster_id].append(rect)

    return dict(clusters)


def match_clusters(template_clusters, target_clusters):
    """
    Match clusters between template and target based on structural similarity.

    Args:
        template_clusters: Dictionary of clusters from template
        target_clusters: Dictionary of clusters from target

    Returns:
        Dictionary mapping template cluster IDs to matching target cluster IDs,
        and dictionary of match scores
    """
    matches = {}
    scores = {}

    # For each template cluster
    for template_id, template_rects in template_clusters.items():
        best_match = None
        best_score = 0

        # Create a fingerprint of the template cluster
        template_fingerprint = create_cluster_fingerprint(template_rects)

        # Compare with each target cluster
        for target_id, target_rects in target_clusters.items():
            # Skip if this target cluster is already matched with higher score
            if target_id in matches.values() and target_id != best_match:
                continue

            # Create a fingerprint of the target cluster
            target_fingerprint = create_cluster_fingerprint(target_rects)

            # Calculate similarity score
            score = compare_fingerprints(template_fingerprint, target_fingerprint)

            if score > best_score:
                best_score = score
                best_match = target_id

        # If we found a good match
        if best_match is not None and best_score > 0.5:
            matches[template_id] = best_match
            scores[(template_id, best_match)] = best_score

    return matches, scores


def create_cluster_fingerprint(rectangles):
    """
    Create a structural fingerprint of a cluster of rectangles.

    Args:
        rectangles: List of rectangles in format (x, y, w, h, id)

    Returns:
        Dictionary containing structural features of the cluster
    """
    if not rectangles:
        return {}

    # Extract basic statistics
    n_rects = len(rectangles)

    # Calculate relative positions
    centers = []
    sizes = []
    for rect in rectangles:
        x, y, w, h, _ = rect
        centers.append((x + w/2, y + h/2))
        sizes.append((w, h))

    # Calculate centroid
    centroid_x = sum(c[0] for c in centers) / n_rects
    centroid_y = sum(c[1] for c in centers) / n_rects

    # Calculate relative positions to centroid
    rel_positions = []
    for cx, cy in centers:
        rel_x = cx - centroid_x
        rel_y = cy - centroid_y
        rel_positions.append((rel_x, rel_y))

    # Calculate average size
    avg_width = sum(s[0] for s in sizes) / n_rects
    avg_height = sum(s[1] for s in sizes) / n_rects

    # Calculate aspect ratios
    aspect_ratios = [w/h if h > 0 else 0 for w, h in sizes]

    # Calculate distances between rectangles
    distances = []
    for i in range(n_rects):
        for j in range(i+1, n_rects):
            dist = ((centers[i][0] - centers[j][0])**2 + (centers[i][1] - centers[j][1])**2)**0.5
            distances.append(dist)

    # Return fingerprint
    return {
        'n_rects': n_rects,
        'rel_positions': rel_positions,
        'avg_width': avg_width,
        'avg_height': avg_height,
        'aspect_ratios': aspect_ratios,
        'distances': distances if distances else [0],
        'centroid': (centroid_x, centroid_y)
    }


def compare_fingerprints(fp1, fp2):
    """
    Compare two cluster fingerprints and return a similarity score.

    Args:
        fp1: First fingerprint
        fp2: Second fingerprint

    Returns:
        Similarity score between 0 and 1
    """
    if not fp1 or not fp2:
        return 0

    # Compare number of rectangles
    n_rects_diff = abs(fp1['n_rects'] - fp2['n_rects']) / max(fp1['n_rects'], fp2['n_rects'])
    n_rects_score = 1 - min(n_rects_diff, 1)

    # Compare average sizes
    size_diff = (abs(fp1['avg_width'] - fp2['avg_width']) / max(fp1['avg_width'], fp2['avg_width']) +
                abs(fp1['avg_height'] - fp2['avg_height']) / max(fp1['avg_height'], fp2['avg_height'])) / 2
    size_score = 1 - min(size_diff, 1)

    # Compare aspect ratios
    # Use the smaller set of aspect ratios
    min_aspects = min(len(fp1['aspect_ratios']), len(fp2['aspect_ratios']))
    if min_aspects > 0:
        # Sort aspect ratios
        ar1 = sorted(fp1['aspect_ratios'])[:min_aspects]
        ar2 = sorted(fp2['aspect_ratios'])[:min_aspects]

        # Calculate differences
        ar_diffs = [abs(a1 - a2) / max(a1, a2, 1) for a1, a2 in zip(ar1, ar2)]
        ar_score = 1 - min(sum(ar_diffs) / min_aspects, 1)
    else:
        ar_score = 0

    # Compare relative positions
    # This is more complex as we need to match positions
    pos_score = 0
    if fp1['rel_positions'] and fp2['rel_positions']:
        # Normalize positions by maximum distance
        max_dist1 = max(max(abs(x), abs(y)) for x, y in fp1['rel_positions']) if fp1['rel_positions'] else 1
        max_dist2 = max(max(abs(x), abs(y)) for x, y in fp2['rel_positions']) if fp2['rel_positions'] else 1
        max_dist = max(max_dist1, max_dist2, 1)

        norm_pos1 = [(x/max_dist, y/max_dist) for x, y in fp1['rel_positions']]
        norm_pos2 = [(x/max_dist, y/max_dist) for x, y in fp2['rel_positions']]

        # Calculate minimum sum of distances between positions
        min_sum_dist = 0
        matched = set()

        for p1 in norm_pos1:
            best_dist = float('inf')
            best_idx = -1

            for i, p2 in enumerate(norm_pos2):
                if i in matched:
                    continue

                dist = ((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)**0.5
                if dist < best_dist:
                    best_dist = dist
                    best_idx = i

            if best_idx >= 0:
                min_sum_dist += best_dist
                matched.add(best_idx)

        # Calculate score
        pos_score = 1 - min(min_sum_dist / len(norm_pos1), 1)

    # Combine scores with weights
    weights = {
        'n_rects': 0.2,
        'size': 0.1,
        'aspect': 0.3,
        'position': 0.4
    }

    total_score = (weights['n_rects'] * n_rects_score +
                  weights['size'] * size_score +
                  weights['aspect'] * ar_score +
                  weights['position'] * pos_score)

    return total_score


def calculate_transformation(template_cluster, target_cluster):
    """
    Calculate transformation parameters between template and target clusters.

    Args:
        template_cluster: List of rectangles from template
        target_cluster: List of rectangles from target

    Returns:
        Dictionary with transformation parameters
    """
    # Calculate centroids
    template_centroid = calculate_centroid(template_cluster)
    target_centroid = calculate_centroid(target_cluster)

    # Calculate average sizes
    template_avg_size = calculate_average_size(template_cluster)
    target_avg_size = calculate_average_size(target_cluster)

    # Print debug info
    print(f"  Template cluster: {len(template_cluster)} rectangles")
    print(f"  Target cluster: {len(target_cluster)} rectangles")
    print(f"  Template centroid: ({template_centroid[0]:.2f}, {template_centroid[1]:.2f})")
    print(f"  Target centroid: ({target_centroid[0]:.2f}, {target_centroid[1]:.2f})")
    print(f"  Template avg size: ({template_avg_size[0]:.2f}, {template_avg_size[1]:.2f})")
    print(f"  Target avg size: ({target_avg_size[0]:.2f}, {target_avg_size[1]:.2f})")

    # Calculate scaling factors
    scale_x = target_avg_size[0] / template_avg_size[0] if template_avg_size[0] > 0 else 1
    scale_y = target_avg_size[1] / template_avg_size[1] if template_avg_size[1] > 0 else 1

    # Limit scaling factors to reasonable values
    if scale_x > 10 or scale_x < 0.1:
        print(f"  WARNING: Extreme scale_x value: {scale_x:.4f}, limiting to reasonable range")
        scale_x = max(0.1, min(10, scale_x))

    if scale_y > 10 or scale_y < 0.1:
        print(f"  WARNING: Extreme scale_y value: {scale_y:.4f}, limiting to reasonable range")
        scale_y = max(0.1, min(10, scale_y))

    # Calculate translation
    translation_x = target_centroid[0] - template_centroid[0] * scale_x
    translation_y = target_centroid[1] - template_centroid[1] * scale_y

    # Convert to relative coordinates (0-1 range)
    # This is critical for proper transformation of ROI coordinates
    template_img_width = 3300  # Default width, should be replaced with actual width
    template_img_height = 5100  # Default height, should be replaced with actual height
    target_img_width = 3300
    target_img_height = 5100

    # Convert to relative coordinates
    rel_scale_x = scale_x * (target_img_width / template_img_width)
    rel_scale_y = scale_y * (target_img_height / template_img_height)
    rel_translation_x = translation_x / target_img_width
    rel_translation_y = translation_y / target_img_height

    print(f"  Calculated transformation: scale_x={scale_x:.4f}, scale_y={scale_y:.4f}, translation_x={translation_x:.4f}, translation_y={translation_y:.4f}")
    print(f"  Relative transformation: scale_x={rel_scale_x:.4f}, scale_y={rel_scale_y:.4f}, translation_x={rel_translation_x:.4f}, translation_y={rel_translation_y:.4f}")

    return {
        'scale_x': rel_scale_x,
        'scale_y': rel_scale_y,
        'translation_x': rel_translation_x,
        'translation_y': rel_translation_y,
        'template_centroid': template_centroid,
        'target_centroid': target_centroid,
        'abs_scale_x': scale_x,
        'abs_scale_y': scale_y,
        'abs_translation_x': translation_x,
        'abs_translation_y': translation_y
    }


def calculate_centroid(rectangles):
    """
    Calculate the centroid of a list of rectangles.

    Args:
        rectangles: List of rectangles in format (x, y, w, h, id)

    Returns:
        Tuple (centroid_x, centroid_y)
    """
    if not rectangles:
        return (0, 0)

    sum_x = sum(rect[0] + rect[2]/2 for rect in rectangles)
    sum_y = sum(rect[1] + rect[3]/2 for rect in rectangles)

    return (sum_x / len(rectangles), sum_y / len(rectangles))


def calculate_average_size(rectangles):
    """
    Calculate the average size of a list of rectangles.

    Args:
        rectangles: List of rectangles in format (x, y, w, h, id)

    Returns:
        Tuple (avg_width, avg_height)
    """
    if not rectangles:
        return (1, 1)

    sum_w = sum(rect[2] for rect in rectangles)
    sum_h = sum(rect[3] for rect in rectangles)

    return (sum_w / len(rectangles), sum_h / len(rectangles))


def find_roi_cluster(roi_coords, clusters):
    """
    Find which cluster an ROI belongs to.

    Args:
        roi_coords: Tuple (x0, y0, x1, y1) with relative coordinates
        clusters: Dictionary of clusters

    Returns:
        Tuple (cluster_id, confidence) or (None, 0) if no match
    """
    roi_x0, roi_y0, roi_x1, roi_y1 = roi_coords
    roi_center_x = (roi_x0 + roi_x1) / 2
    roi_center_y = (roi_y0 + roi_y1) / 2

    best_cluster = None
    best_score = 0

    for cluster_id, rectangles in clusters.items():
        # Calculate distances from ROI center to each rectangle center
        distances = []
        for rect in rectangles:
            x, y, w, h, _ = rect
            rect_center_x = x + w/2
            rect_center_y = y + h/2

            dist = ((roi_center_x - rect_center_x)**2 + (roi_center_y - rect_center_y)**2)**0.5
            distances.append(dist)

        # Sort distances
        distances.sort()

        # Calculate score based on minimum distances
        if distances:
            min_dist = distances[0]
            score = 1 / (1 + 10 * min_dist)  # Closer distance = higher score

            if score > best_score:
                best_score = score
                best_cluster = cluster_id

    return (best_cluster, best_score)


def transform_roi_coordinates(roi_coords, transformation):
    """
    Apply transformation to ROI coordinates.

    Args:
        roi_coords: Tuple (x0, y0, x1, y1) with relative coordinates
        transformation: Dictionary with transformation parameters

    Returns:
        Tuple (adjusted_x0, adjusted_y0, adjusted_x1, adjusted_y1, confidence)
    """
    roi_x0, roi_y0, roi_x1, roi_y1 = roi_coords

    # Print debug info
    print(f"  Transforming ROI: ({roi_x0:.4f}, {roi_y0:.4f}, {roi_x1:.4f}, {roi_y1:.4f})")

    # Extract transformation parameters
    scale_x = transformation['scale_x']
    scale_y = transformation['scale_y']
    translation_x = transformation['translation_x']
    translation_y = transformation['translation_y']

    print(f"  Transformation: scale_x={scale_x:.4f}, scale_y={scale_y:.4f}, translation_x={translation_x:.4f}, translation_y={translation_y:.4f}")

    # Apply transformation
    adjusted_x0 = roi_x0 * scale_x + translation_x
    adjusted_y0 = roi_y0 * scale_y + translation_y
    adjusted_x1 = roi_x1 * scale_x + translation_x
    adjusted_y1 = roi_y1 * scale_y + translation_y

    print(f"  Raw adjusted coords: ({adjusted_x0:.4f}, {adjusted_y0:.4f}, {adjusted_x1:.4f}, {adjusted_y1:.4f})")

    # Calculate confidence based on how much the aspect ratio changed
    original_width = roi_x1 - roi_x0
    original_height = roi_y1 - roi_y0
    adjusted_width = adjusted_x1 - adjusted_x0
    adjusted_height = adjusted_y1 - adjusted_y0

    if original_width > 0 and original_height > 0:
        original_aspect = original_width / original_height
        adjusted_aspect = adjusted_width / adjusted_height if adjusted_height > 0 else 0

        aspect_change = abs(original_aspect - adjusted_aspect) / original_aspect if original_aspect > 0 else 1
        confidence = 1 - min(aspect_change, 1)
    else:
        confidence = 0.5

    # Check if the adjusted coordinates are valid
    if adjusted_x0 >= 1 or adjusted_y0 >= 1 or adjusted_x1 <= 0 or adjusted_y1 <= 0:
        print(f"  WARNING: Adjusted coordinates are outside the valid range!")

        # Use original coordinates with a low confidence instead
        adjusted_x0 = roi_x0
        adjusted_y0 = roi_y0
        adjusted_x1 = roi_x1
        adjusted_y1 = roi_y1
        confidence = 0.3

    # Ensure coordinates are within bounds [0, 1]
    adjusted_x0 = max(0, min(0.95, adjusted_x0))  # Limit to 0.95 to avoid edge cases
    adjusted_y0 = max(0, min(0.95, adjusted_y0))
    adjusted_x1 = max(0.05, min(1, adjusted_x1))  # Ensure at least 0.05 to avoid zero width
    adjusted_y1 = max(0.05, min(1, adjusted_y1))

    # Ensure the width and height are reasonable
    if adjusted_x1 - adjusted_x0 < 0.01:
        adjusted_x1 = adjusted_x0 + 0.05
    if adjusted_y1 - adjusted_y0 < 0.01:
        adjusted_y1 = adjusted_y0 + 0.05

    print(f"  Final adjusted coords: ({adjusted_x0:.4f}, {adjusted_y0:.4f}, {adjusted_x1:.4f}, {adjusted_y1:.4f}), confidence: {confidence:.4f}")

    return (adjusted_x0, adjusted_y0, adjusted_x1, adjusted_y1), confidence


def find_nearest_rectangle(roi_coords, target_rectangles, target_img_size):
    """
    Find the nearest rectangle in the target image to the transformed ROI coordinates.

    Args:
        roi_coords: Tuple (x0, y0, x1, y1) with relative coordinates
        target_rectangles: List of rectangles in the target image
        target_img_size: Tuple (width, height) of the target image

    Returns:
        Tuple (snapped_x0, snapped_y0, snapped_x1, snapped_y1, confidence)
    """
    roi_x0, roi_y0, roi_x1, roi_y1 = roi_coords
    target_width, target_height = target_img_size

    # Convert relative coordinates to absolute
    abs_x0 = int(roi_x0 * target_width)
    abs_y0 = int(roi_y0 * target_height)
    abs_x1 = int(roi_x1 * target_width)
    abs_y1 = int(roi_y1 * target_height)

    # Calculate ROI center
    roi_center_x = (abs_x0 + abs_x1) / 2
    roi_center_y = (abs_y0 + abs_y1) / 2

    # Calculate ROI area and dimensions
    roi_width = abs_x1 - abs_x0
    roi_height = abs_y1 - abs_y0
    roi_area = roi_width * roi_height

    # Skip if ROI is too small
    if roi_area < 100:
        print("  ROI is too small for snapping, using transformed coordinates")
        return roi_coords, 0.5

    # Find the nearest rectangle
    best_rect = None
    best_score = float('inf')
    best_area_ratio = 0
    best_distance = float('inf')
    best_overlap_ratio = 0

    filtered_rectangles = []

    # Filter out very large rectangles (likely page borders)
    for rect in target_rectangles:
        x, y, w, h, rect_id = rect

        # Skip very large rectangles (likely page borders)
        if w > 0.9 * target_width and h > 0.9 * target_height:
            continue

        # Skip very small rectangles
        if w < 10 or h < 10:
            continue

        filtered_rectangles.append(rect)

    print(f"  Considering {len(filtered_rectangles)} rectangles for snapping (filtered out page borders and tiny rectangles)")

    for rect in filtered_rectangles:
        x, y, w, h, rect_id = rect

        # Calculate rectangle center
        rect_center_x = x + w / 2
        rect_center_y = y + h / 2

        # Calculate distance between centers
        distance = ((roi_center_x - rect_center_x) ** 2 + (roi_center_y - rect_center_y) ** 2) ** 0.5

        # Calculate size similarity
        width_ratio = min(roi_width, w) / max(roi_width, w) if max(roi_width, w) > 0 else 0
        height_ratio = min(roi_height, h) / max(roi_height, h) if max(roi_height, h) > 0 else 0
        size_similarity = (width_ratio + height_ratio) / 2

        # Calculate area ratio (1.0 means perfect match)
        rect_area = w * h
        area_ratio = min(roi_area, rect_area) / max(roi_area, rect_area) if max(roi_area, rect_area) > 0 else 0

        # Calculate overlap ratio
        overlap_x0 = max(abs_x0, x)
        overlap_y0 = max(abs_y0, y)
        overlap_x1 = min(abs_x1, x + w)
        overlap_y1 = min(abs_y1, y + h)

        overlap_area = max(0, overlap_x1 - overlap_x0) * max(0, overlap_y1 - overlap_y0)
        overlap_ratio = overlap_area / min(roi_area, rect_area) if min(roi_area, rect_area) > 0 else 0

        # Calculate position similarity (1.0 means centers are identical)
        max_distance = ((target_width/2)**2 + (target_height/2)**2)**0.5  # Maximum possible distance
        position_similarity = 1 - min(distance / max_distance, 1)

        # Combine factors into a score (lower is better)
        # Heavily weight size similarity and overlap, and consider position similarity
        score = distance * (1 - size_similarity * 3) * (1 - overlap_ratio * 2) * (1 - position_similarity * 0.5)

        if score < best_score:
            best_score = score
            best_rect = rect
            best_area_ratio = area_ratio
            best_distance = distance
            best_overlap_ratio = overlap_ratio

            # Debug info for best match so far
            print(f"  New best match: Rectangle {rect_id} at ({x}, {y}, {x+w}, {y+h})")
            print(f"    Distance: {distance:.2f}, Size similarity: {size_similarity:.4f}, Overlap: {overlap_ratio:.4f}")
            print(f"    Score: {score:.4f}")

    # Minimum confidence threshold for snapping
    MIN_CONFIDENCE = 0.3

    # Calculate confidence based on multiple factors
    if best_rect is not None:
        x, y, w, h, rect_id = best_rect

        # Normalize distance (0 to 1, where 1 is best)
        max_distance = ((target_width/2)**2 + (target_height/2)**2)**0.5
        distance_factor = 1 - min(best_distance / max_distance, 1)

        # Calculate confidence from multiple factors
        confidence = (best_area_ratio * 0.3 + distance_factor * 0.3 + best_overlap_ratio * 0.4)

        # If confidence is too low, use original coordinates
        if confidence < MIN_CONFIDENCE:
            print(f"  Confidence too low ({confidence:.4f}), using transformed coordinates")
            return roi_coords, 0.5

        # Convert back to relative coordinates
        snapped_x0 = x / target_width
        snapped_y0 = y / target_height
        snapped_x1 = (x + w) / target_width
        snapped_y1 = (y + h) / target_height

        print(f"  Snapped to rectangle {rect_id}: ({snapped_x0:.4f}, {snapped_y0:.4f}, {snapped_x1:.4f}, {snapped_y1:.4f})")
        print(f"  Confidence from snapping: {confidence:.4f}")

        return (snapped_x0, snapped_y0, snapped_x1, snapped_y1), confidence
    else:
        print("  No suitable rectangle found, using transformed coordinates")
        return roi_coords, 0.5


def convert_rectangles_to_rois(rectangles):
    """
    Convert rectangles from template JSON to ROI format.

    Args:
        rectangles: List of rectangle objects from template JSON

    Returns:
        Dictionary of ROIs in the format expected by the rest of the code
    """
    # Create a single group with all rectangles as ROIs
    rois = []

    for i, rect in enumerate(rectangles):
        # Extract rectangle properties
        rect_id = rect.get('id', i)
        x = rect.get('x', 0)
        y = rect.get('y', 0)
        width = rect.get('width', 0)
        height = rect.get('height', 0)

        # Skip very large rectangles (likely page borders)
        if width > 0.9 and height > 0.9:
            print(f"  Skipping large rectangle {rect_id} (likely page border): {width:.2f}x{height:.2f}")
            continue

        # Skip very small rectangles
        if width < 0.01 or height < 0.01:
            print(f"  Skipping tiny rectangle {rect_id}: {width:.2f}x{height:.2f}")
            continue

        # Create ROI object
        roi = {
            'name': f'Rectangle_{rect_id}',
            'relativeX0': x,
            'relativeY0': y,
            'relativeX1': x + width,
            'relativeY1': y + height
        }

        rois.append(roi)

    print(f"  Converted {len(rois)} rectangles to ROIs (filtered out page borders and tiny rectangles)")

    # Return as a dictionary with a single group
    return {'1': rois}


def draw_dashed_rectangle(img, pt1, pt2, color, thickness=1, dash_length=10):
    """
    Draw a dashed rectangle on an image.

    Args:
        img: Image to draw on
        pt1: Top-left corner (x, y)
        pt2: Bottom-right corner (x, y)
        color: BGR color tuple
        thickness: Line thickness
        dash_length: Length of each dash

    Returns:
        None
    """
    x1, y1 = pt1
    x2, y2 = pt2

    # Draw top line
    x, y = x1, y1
    while x < x2:
        x_end = min(x + dash_length, x2)
        cv2.line(img, (x, y), (x_end, y), color, thickness)
        x = x_end + dash_length

    # Draw right line
    x, y = x2, y1
    while y < y2:
        y_end = min(y + dash_length, y2)
        cv2.line(img, (x, y), (x, y_end), color, thickness)
        y = y_end + dash_length

    # Draw bottom line
    x, y = x2, y2
    while x > x1:
        x_end = max(x - dash_length, x1)
        cv2.line(img, (x, y), (x_end, y), color, thickness)
        x = x_end - dash_length

    # Draw left line
    x, y = x1, y2
    while y > y1:
        y_end = max(y - dash_length, y1)
        cv2.line(img, (x, y), (x, y_end), color, thickness)
        y = y_end - dash_length


def get_confidence_color(confidence):
    """
    Get color based on confidence score.

    Args:
        confidence: Confidence score between 0 and 1

    Returns:
        BGR color tuple
    """
    if confidence >= 0.8:
        return (0, 255, 0)  # Green for high confidence
    elif confidence >= 0.5:
        return (0, 255, 255)  # Yellow for medium confidence
    else:
        return (0, 0, 255)  # Red for low confidence


def generate_confidence_report(confidence_scores, target_image_path, output_dir):
    """
    Generate Excel report with confidence scores.

    Args:
        confidence_scores: Dictionary of confidence scores by group and ROI
        target_image_path: Path to the target image
        output_dir: Directory to save the report
    """
    try:
        import pandas as pd

        # Extract page number from image path
        page_num = "unknown"
        image_name = os.path.basename(target_image_path)
        if "page_" in image_name:
            try:
                page_num = image_name.split("page_")[1].split(".")[0]
            except:
                pass

        # Prepare data for Excel
        data = []
        for group_id, scores in confidence_scores.items():
            for roi_name, score in scores.items():
                confidence_level = "High" if score >= 0.8 else "Medium" if score >= 0.5 else "Low"
                data.append({
                    'Page': page_num,
                    'Group': group_id,
                    'ROI': roi_name,
                    'Confidence': score,
                    'Level': confidence_level
                })

        # Create DataFrame
        df = pd.DataFrame(data)

        # Ensure the output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Save to Excel
        try:
            excel_path = os.path.normpath(os.path.join(output_dir, f'confidence_scores_page_{page_num}.xlsx'))
            df.to_excel(excel_path, index=False)
            print(f"Confidence report saved to: {excel_path}")
        except Exception as e:
            print(f"Error saving Excel file to {excel_path}: {e}")
            # Try an alternative location
            alt_excel_path = os.path.join(os.path.dirname(__file__), f'confidence_scores_page_{page_num}.xlsx')
            try:
                df.to_excel(alt_excel_path, index=False)
                print(f"Confidence report saved to alternative location: {alt_excel_path}")
            except Exception as e2:
                print(f"Error saving to alternative location: {e2}")

                # As a last resort, save as CSV
                try:
                    csv_path = os.path.join(os.path.dirname(__file__), f'confidence_scores_page_{page_num}.csv')
                    df.to_csv(csv_path, index=False)
                    print(f"Confidence report saved as CSV: {csv_path}")
                except Exception as e3:
                    print(f"Failed to save confidence report in any format: {e3}")

    except ImportError:
        print("Pandas not installed. Skipping confidence report generation.")
    except Exception as e:
        print(f"Error generating confidence report: {e}")


if __name__ == '__main__':
    # Example usage
    template_json_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output\adjusted_template.json"
    target_image_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\images\page_53.png"
    output_dir = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output"

    # Check if the template file exists
    if not os.path.isfile(template_json_path):
        print(f"WARNING: Template file not found at: {template_json_path}")
        print("Checking for alternative template files...")

        # Look for template.json in the output directory
        alt_template_path = os.path.join(output_dir, 'template.json')
        if os.path.isfile(alt_template_path):
            print(f"Found alternative template file: {alt_template_path}")
            template_json_path = alt_template_path
        else:
            print(f"No alternative template file found at: {alt_template_path}")

            # Look in the current directory
            current_dir_template = 'template.json'
            if os.path.isfile(current_dir_template):
                print(f"Found template file in current directory: {current_dir_template}")
                template_json_path = current_dir_template

    print(f"Using template file: {template_json_path}")

    try:
        print(f"Applying template to target page: {target_image_path}")
        result = apply_template_to_page(template_json_path, target_image_path, output_dir, debug=True)

        if result:
            print(f"\nTemplate application visualization saved to: {result['visualization_path']}")
            print(f"Adjusted template JSON saved to: {result['output_json_path']}\n")

            # Print summary of confidence scores
            print("Confidence Score Summary:")
            for group_id, scores in result['confidence_scores'].items():
                print(f"\n--- Group ID: {group_id} ---")
                for roi_name, score in scores.items():
                    confidence_level = "High" if score >= 0.8 else "Medium" if score >= 0.5 else "Low"
                    print(f"  {roi_name}: {score:.2f} ({confidence_level})")
        else:
            print("Failed to apply template to target page.")

    except FileNotFoundError as e:
        print(f"Error: One of the input files was not found: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()

