from src.utils.logger import logger
from PySide6.QtWidgets import QLabel, QPushButton, QLineEdit, QComboBox, QSizePolicy
from PySide6.QtCore import Qt
from .baseform import BaseForm
from pubsub import pub

PROJECT_NAME_DEFAULT_MESSAGE = "Project"
ENGINEER_DRAFTER_DEFAULT_MESSAGE = "Engineering/Drafter Company of Drawings"


class AddSourceExistingProject(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)

        self.filename: str = None
    
    def initUi(self):
        self.formSize.setHeight(480)
        self.filename = None

        self.title.setText("Add To Existing Project")
        self.subtitle.clear()
        self.subtitle.hide()

        self.addVSpace()

        self.lblFilename = QLabel(f"Filename: {self.filename}")
        self.layout().addRow(self.lblFilename)

        self.addVSpace()
    
        self.lblProjectName = QLabel(PROJECT_NAME_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectName)
        self.cboxProjects = QComboBox(self)
        self.layout().addRow(self.cboxProjects)
    
        self.addVSpace()

        self.lblEngineerDrafter = QLabel(ENGINEER_DRAFTER_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblEngineerDrafter)
        self.engineerDrafter = QLineEdit()
        self.layout().addRow(self.engineerDrafter)

        self.addStretchSpacer()
        self.addErrorStatusWidget()
        self.pbNext = pbNext = QPushButton("Confirm")
        pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        pbNext.setMinimumHeight(48)
        pbNext.clicked.connect(self.onNext)
        self.layout().addRow(pbNext)
        pbNext.setContentsMargins(0, 132, 0, 32)
        self.setFloatingButtonBack()
    
    def initDefaults(self):
        pass
    
    def showEvent(self, event) -> None:
        try:
            self.cboxProjects.setCurrentIndex(0)
        except:
            pass
        self.engineerDrafter.clear()
        return super().showEvent(event)

    def setProjectsData(self, data):
        super().setProjectsData(data)
        self.cboxProjects.clear()
        
        if not self._projects:
            self.pbNext.setEnabled(False)
            self.setErrorStatusMessage("You must create a project before a source can be added")
            return
        self.hideErrorStatus()
        self.pbNext.setEnabled(True)
        for p in self._projects:
            name = f"{p['id']}: {p['projectName']}"
            self.cboxProjects.addItem(name, p)

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onNext(self, event):
        """ Validate user input """
        data = self.cboxProjects.currentData(Qt.ItemDataRole.UserRole)
        projectId = data["id"]
        print(f"Not Implemented Yet - add project source to id: {projectId}, {data}")
        msg = {"projectId": projectId, "filename": self.filename}
        if self.engineerDrafter.text():
            msg["documentVendor"] = self.engineerDrafter.text()
        pub.sendMessage("project-source-add", data=msg)
        return
        ok = True
        projectName = self.projectName.text()
        if not projectName:
            self.lblProjectName.setText(PROJECT_NAME_DEFAULT_MESSAGE.replace("*", "required"))
            self.setWidgetError(self.lblProjectName)
            ok = False
        else:
            self.setWidgetDefault(self.lblProjectName)

        self.setWidgetDefault(self.lblEngineerDrafter)

        # Details are valid? Move on to explorer form
        # self.sgnSwitchTo.emit("NewProjectExplorerForm")
        projectData = {}
        projectData.update(self.getData())
        projectData["document"] = self.filename
        pub.sendMessage("project-source-add", data = {"projectId": projectId, "filename": self.filename})

    def getData(self) -> dict:
        return {
            "projectName": self.projectName.text(),
            "projectId": self.projectId.text(),
            "projectLocation": self.projectLocation.text(),
            "engineerDrafter": self.engineerDrafter.text(),
        }

    def onFilePicked(self, filename):
        """ See: Signal `sgnFilePicked` from AddSourceExistingProject """
        self.filename = filename
        self.lblFilename.setText(f"Filename: {self.filename}")
        self.sgnSwitchTo.emit("AddSourceExistingProject")
    
    def onFloatingButton(self):
        pub.sendMessage("goto-form", name="NewProjectExplorerForm")