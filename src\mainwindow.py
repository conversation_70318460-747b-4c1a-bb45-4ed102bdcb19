from src.utils.logger import logger
from PySide6.QtCore import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
import pandas as pd
from pubsub import pub
from functools import partial

from .views.projectview import ProjectView
from .views.workspaceview import WorkspaceView
from .views.formsview import FormsView
from .views.navigationsidebar import NavigationSidebar
from .views.popups import UserProfilePopup, QueueStatusPopup, TokenStatusPopup
from src import pyside_util
from src.pyside_util import get_resource_pixmap, applyDropShadowEffect
from src.app_paths import getSaved<PERSON>ieldMap<PERSON>son, saveFieldMapJson
from src.views.forms import TokenPaymentForm
from src.views.contactdetails import ContactDetails
from src.widgets.statusbar import *
from src.views.dialogs import ReportIssueDialog, TableImporter
from src.views.dialogs.auditdialog2 import AuditDialog2
from src.views.dialogs.welddetect import WeldDetect
from src.views.dialogs.plugindialog import PluginDialog
from src.views.dialogs.rawdataviewer import <PERSON><PERSON><PERSON><PERSON>ie<PERSON>
from src.data.tablemap import get_payload_name, get_table_from_payload
import __version__


class Sidebar(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        self.setObjectName("sidebarWidget")
        self.sidebarWidth = 300
        self.setLayout(QGridLayout())
        self.layout().setContentsMargins(0, 0, 0, 52)
        self.layout().setSpacing(0)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setMinimumWidth(self.sidebarWidth)
        # Sidebar - Always visible logo above navigation
        sidebarLogo = QLabel(self)
        sidebarLogo.setObjectName("sidebarLogo")
        pixmap = get_resource_pixmap("sidebar-logo.png")
        sidebarLogo.setPixmap(pixmap.scaledToWidth(self.sidebarWidth))
        self.layout().addWidget(sidebarLogo, 0, 0, 1, 1)

        w = QWidget()
        w.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        w.setStyleSheet('background-color:rgba(43, 43, 43, 0.1)')
        w.setAutoFillBackground(True)
        w.setWindowOpacity(0.2)
        self.layout().addWidget(w, 0, 1, 2, 1)
        pyside_util.applyDropShadowEffect(self)

    def layout(self) -> QGridLayout:
        return super().layout()

    def showEvent(self, event: QShowEvent) -> None:
        self.raise_()
        x = 0
        try:
            y = self.parent().toolbar.height()
        except:
            y = 0
        self.move(x, y)

        self.setFixedSize(QSize(300, self.topLevelWidget().height()-y))
        self.setFixedSize(QSize(self.topLevelWidget().width(), self.topLevelWidget().height()-y))

        return super().showEvent(event)

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)


class MainWidget(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QGridLayout())
        self.setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

    def layout(self) -> QGridLayout:
        return super().layout()


class MainWindow(QMainWindow):

    sgnSetStatusbarMessage = Signal(object)
    sgnShowMessageBox = Signal(str, str, str) # msg, title, icon
    sgnSwitchToWorkspace = Signal(str)
    sgnUpdateJobQueueStatus = Signal(object)
    windowResized = Signal(object)

    def __init__(self):
        super().__init__()
        self.setWindowTitle()
        self.setLayout(QVBoxLayout())
        self.projectData = {}
        self.lastRealtimeMessage = None
        self.contactDetails = None
        self.dlgReportIssue = None
        self.dlgProjectDataImporter = None
        self.dlgDetectWelds = None
        self.dlgAuditTool: AuditDialog2 = None
        self.dlgPluginDialog: PluginDialog = None
        self.dlgRawDataViewer: RawDataViewer = None

        mainWidget = MainWidget(self)

        class DisableCtrlTab(QObject):

            def __init__(self, parent: QObject) -> None:
                super().__init__(parent)

            def eventFilter(self, source, event):
                if event.type() == QEvent.KeyPress and (event.key() == 16777217 or event.key() == 16777218):
                    return True # eat alt+tab or alt+shift+tab key
                else:
                    return QObject.eventFilter(self, source, event)

        self.tabs = QTabWidget()
        self.tabs.setContentsMargins(0, 0, 0, 0)
        tabsFilter = DisableCtrlTab(self)
        self.tabs.installEventFilter(tabsFilter)
        self.formsView = FormsView(self)
        self.workspaceView: WorkspaceView = WorkspaceView(self)
        self.tabs.addTab(self.formsView, "Forms")
        self.tabs.addTab(self.workspaceView, "Workspace")

        self.tabs.tabBar().hide()
        # Vertical sidebar line
        self.sidebarVline = QFrame(mainWidget)
        self.sidebarVline.setObjectName("sidebarVline")
        self.sidebarVline.setFrameShape(QFrame.Shape.VLine)
        self.sidebarVline.setMaximumWidth(1)
        self.sidebarVline.hide()
        mainWidget.layout().addWidget(self.sidebarVline, 0, 0, 1, 1)

        mainWidget.layout().addWidget(self.tabs, 0, 1, 1, 1)
        self.setMinimumSize(1200, 800)
        self.setWindowIcon(QIcon(get_resource_pixmap("FFF_Architekt Integrated Systems_LOout.png")))

        # Sidebar container widget
        self.sidebarWidget = Sidebar(self)

        # Sidebar - Navigation
        self.sidebar = NavigationSidebar()
        self.sidebar.setMinimumWidth(self.sidebarWidget.sidebarWidth)
        self.sidebar.setFixedWidth(self.sidebarWidget.sidebarWidth)
        self.sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        self.sidebarWidget.layout().addWidget(self.sidebar, 1, 0, 1, 1)

        self.setCentralWidget(mainWidget)
        self.setContentsMargins(0, 0, 0, 0)
        self.tabs.setContentsMargins(0, 0, 0, 0)

        # Floating sidebar button
        self.pbOpenDrawer = QPushButton("", self)
        self.iconDrawerOpening = pyside_util.get_resource_qicon("chevron-right.svg")
        self.iconDrawerClosing = pyside_util.get_resource_qicon("chevron-left.svg")
        self.pbOpenDrawer.setIcon(self.iconDrawerOpening)
        self.pbOpenDrawer.setIconSize(QSize(24, 24))
        self.pbOpenDrawer.setFixedSize(24, 24)
        self.pbOpenDrawer.clicked.connect(self.toggleDrawer)
        self.pbOpenDrawer.setToolTip("Open Sidebar")
        self.pbOpenDrawer.setObjectName("openDrawerButton")
        self.pbOpenDrawer.setEnabled(False)
        self.pbOpenDrawer.setVisible(False)

        self.initToolbar()
        self.initStatusbar()

        pub.subscribe(self.switchToWorkspace, "goto-workspace-view")
        pub.subscribe(self.switchToForms, "goto-form")
        pub.subscribe(self.onSetWorkspaceData, "set-workspace-data")
        pub.subscribe(self.hidePopups, "toolbar-hide-popups")
        pub.subscribe(self.onUserDetailsGetResponse, "user-details-get-response")
        pub.subscribe(self.onOpenProject, "open-project")
        pub.subscribe(self.syncAiConfig, "sync-ai-config")
        pub.subscribe(self.onShowAppMessageBox, "show-app-messagebox")

        self.sgnSwitchToWorkspace.connect(self.onSwitchToWorkspace)

        pub.subscribe(self.onProjectSourceAddResponse, "project-source-add-response")

        self.statusBar().hide()
        self.statusBar().setSizeGripEnabled(False)
        self.resizeEvent(None)

        self.sgnShowMessageBox.connect(self.showAppMessageBox)

        self.workspaceView.views["ProjectView"].tabChanged.connect(self.onProjectTabChanged)

        # A keep alive token
        self.timerRefreshToken = QTimer(self)
        self.timerRefreshToken.setInterval(600000) # Check every 10 minutes
        self.timerRefreshToken.timeout.connect(self.onTimerRefreshToken)
        self.timerRefreshToken.start()

        self.installEventFilter(self)
        self.workspaceView.installEventFilter(self)
        self.sidebarWidget.hide()

    def initToolbar(self):
        """This is menubar combined with toolbar actions"""
        self.toolbar = toolbar = QToolBar("Main Toolbar")
        pyside_util.applyDropShadowEffect(self.toolbar)
        toolbar.setObjectName("topbar")
        toolbar.setMovable(False)
        toolbar.setContextMenuPolicy(Qt.ContextMenuPolicy.PreventContextMenu)
        self.addToolBar(toolbar)
        toolbar.setAcceptDrops(False)

        self.pbLogo = QPushButton("Architekt", self)
        self.pbLogo.setCheckable(True)
        self.pbLogo.setIconSize(QSize(96, 32))
        self.pbLogo.setMinimumWidth(96)
        self.pbLogo.setMinimumHeight(32)
        self.pbLogo.clicked.connect(self.toggleDrawer)
        self.pbLogo.setObjectName("toolbarLogo")
        self.pbLogo.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        toolbar.addWidget(self.pbLogo)

        # Projects menu
        self.projectBtn = projectBtn = QPushButton("Project", self.toolbar)
        self.projectBtn.setEnabled(False)
        projectBtn.setObjectName("toolbarButton")
        projectMenu = QMenu(toolbar)
        pyside_util.applyDropShadowEffect(projectMenu)
        projectMenu.aboutToShow.connect(self.hideDrawer)

        menu = [
            ("Show Project", None, self.onShowProject),
            ("Add Source", "plus.svg", self.onAddSource),
            ("Close Project", None, self.onCloseProject),
        ]
        for name, icon, callback in menu:
            act = QAction(name, self)
            if icon:
                act.setIcon(pyside_util.get_resource_qicon(icon))
            act.triggered.connect(partial(callback))
            projectMenu.addAction(act)

        actDetectWelds = projectMenu.addAction("Detect Welds")
        actDetectWelds.triggered.connect(self.onDetectWelds)

        importMenu = projectMenu.addMenu("Import")
        actImportBomGeneral = importMenu.addAction("BOM and General")
        actImportBomGeneral.triggered.connect(self.onImportProjectBomGeneral)
        actImportRfq = importMenu.addAction("RFQ")
        actImportRfq.triggered.connect(self.onImportProjectRfq)

        toolbar.addWidget(projectBtn)
        projectBtn.setMenu(projectMenu)

        # File menu
        if not any(m in __version__.mode for m in ["trial", "early_access"]):
            fileBtn = QPushButton("File")
            fileBtn.setObjectName("toolbarButton")
            fileBtn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
            fileMenu = QMenu(toolbar)
            fileImportMenu = QMenu(toolbar, title="Import")
            # fileImportMenu.addAction(QAction("Template for importing table data", fileImportMenu))
            # fileMenu.addMenu(fileImportMenu)
            fileMenu.addAction(QAction("Save", self))
            fileMenu.addAction(QAction("Create", self))
            fileMenu.addAction(QAction("Print", self))
            fileMenu.addAction(QAction("Report Issue", self))
            fileMenu.aboutToShow.connect(self.hideDrawer)

            fileMenu.triggered.connect(self.onFileMenu)
            fileBtn.setMenu(fileMenu)
            toolbar.addWidget(fileBtn)

        # Settings menu
        settingsBtn = QPushButton("Settings")
        settingsBtn.setObjectName("toolbarButton")
        settingsMenu = QMenu(toolbar)
        settingsMenu.aboutToShow.connect(self.hideDrawer)
        if not any(m in __version__.mode for m in ["trial", "early_access"]) and False:
            settingsMenu.addAction(QAction("My Account", self))
            settingsMenu.addAction(QAction("Export", self))
            settingsMenu.addAction(QAction("Display", self))
        settingsMenu.addAction(QAction("Revert table fields to default", self))
        settingsMenu.addAction(QAction("Activate a Product Key", self))
        settingsMenu.triggered.connect(self.onSettingsMenu)
        settingsBtn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)

        mtoMenu = QMenu(settingsMenu, title="MTO Assistant")
        settingsMenu.addMenu(mtoMenu)

        # Create action group for MTO models to ensure only one can be selected
        self.mtoModelGroup = QActionGroup(self)
        self.mtoModelGroup.setExclusive(True)

        # GPT4o-Mini (new default)
        self.actChatGpt4oMini = QAction("GPT4o-Mini", self)
        self.actChatGpt4oMini.setCheckable(True)
        self.actChatGpt4oMini.setChecked(True)  # Set as default
        self.mtoModelGroup.addAction(self.actChatGpt4oMini)

        # GPT 3.5
        self.actChatGpt35 = QAction("ChatGPT 3.5", self)
        self.actChatGpt35.setCheckable(True)
        self.mtoModelGroup.addAction(self.actChatGpt35)

        # ChatGPT 4
        self.actChatGpt4 = QAction("ChatGPT 4", self)
        self.actChatGpt4.setCheckable(True)
        self.mtoModelGroup.addAction(self.actChatGpt4)

        # GPT4o
        self.actChatGpt4o = QAction("GPT4o", self)
        self.actChatGpt4o.setCheckable(True)
        self.mtoModelGroup.addAction(self.actChatGpt4o)

        # self.actTestMode = QAction("Test Mode", self)
        # self.actTestMode.setCheckable(True)

        # Connect actions to update AI config when changed
        self.mtoModelGroup.triggered.connect(self.updateAiConfig)

        mtoMenu.addAction(self.actChatGpt4oMini)
        mtoMenu.addAction(self.actChatGpt35)
        mtoMenu.addAction(self.actChatGpt4)
        mtoMenu.addAction(self.actChatGpt4o)
        # mtoMenu.addAction(self.actTestMode)

        # Theme sub-menu
        themeMenu = QMenu(settingsBtn, title="Theme")
        themeGroup = QActionGroup(self)
        opts = ["System Theme", "Light Theme", "Dark Theme"] if __version__.mode != "trial" else ["System Theme"]
        themeActs = {}
        for name in opts:
            action = QAction(name, themeMenu)
            themeMenu.addAction(action)
            themeGroup.addAction(action)
            action.setCheckable(True)
            themeActs[name] = action

        themeActs["System Theme"].setChecked(True)
        if not any(m in __version__.mode for m in ["trial", "early_access"]):
            if name == "System Theme":
                themeActs["System Theme"].setChecked(True)
                themeActs["System Theme"].triggered.connect(lambda: action.setChecked(True))

        settingsMenu.addMenu(themeMenu)
        settingsBtn.setMenu(settingsMenu)
        toolbar.addWidget(settingsBtn)

        # Help menu
        helpBtn = QPushButton("Help")
        helpBtn.setObjectName("toolbarButton")
        contactMenu = QMenu(toolbar, title="Contact")
        self.actSupportTicket = QAction("Support Ticket", contactMenu)
        contactMenu.addAction(self.actSupportTicket)
        self.actContactDetails = QAction("Phone/Email", contactMenu)
        contactMenu.addAction( self.actContactDetails)
        self.actSupportTicket.triggered.connect(self.onSupportTicket)
        self.actContactDetails.triggered.connect(self.onContactDetails)

        helpMenu = QMenu(toolbar)
        helpMenu.aboutToShow.connect(self.hideDrawer)
        helpMenu.addMenu(contactMenu)
        helpBtn.setMenu(helpMenu)
        toolbar.addWidget(helpBtn)

        # Help menu
        devBtn = QPushButton("Dev")
        devBtn.setObjectName("toolbarButton")
        devMenu = QMenu(toolbar, title="Dev")
        self.actAudit = QAction("Open Audit", devMenu)
        devMenu.addAction(self.actAudit)
        self.actAudit.triggered.connect(self.openAuditTool)
        self.actPlugins = QAction("Plugins", devMenu)
        devMenu.addAction(self.actPlugins)
        self.actPlugins.triggered.connect(self.openPluginDialog)
        devMenu.aboutToShow.connect(self.hideDrawer)
        self.actRawDataViewer = QAction("Raw Data Viewer", devMenu)
        devMenu.addAction(self.actRawDataViewer)
        self.actRawDataViewer.triggered.connect(self.openRawDataViewer)
        devMenu.aboutToShow.connect(self.hideDrawer)
        devBtn.setMenu(devMenu)
        toolbar.addWidget(devBtn)

        spacer = QWidget(self)
        # spacer.setObjectName("toolbar")
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        toolbar.addWidget(spacer)

        # self.pbGoToProjectView = pbGoToProjectView = QPushButton("", self)
        # self.pbGoToProjectView.setToolTip("View Project")
        # self.pbGoToProjectView.setMinimumWidth(64)
        # self.pbGoToProjectView.clicked.connect(self.goToProjectView)
        # pbGoToProjectView.setObjectName("toolbarButton")
        # pbGoToProjectView.setIcon(pyside_util.get_resource_qicon("menubar-table.svg"))
        # toolbar.addWidget(pbGoToProjectView)
        # self.pbGoToProjectView.hide()

        self.btnQueueStatus = btnQueueStatus = QPushButton("", self)
        self.btnQueueStatus.setMinimumWidth(64)
        self.btnQueueStatus.setToolTip("Queue")
        btnQueueStatus.setObjectName("toolbarButton")
        btnQueueStatus.setIcon(pyside_util.get_resource_qicon("play-circle.svg"))
        self.movieRunning = QMovie(pyside_util.resource_path("src/resources/loading.gif"), parent=self)
        self.movieRunning.stop()
        self.movieRunning.setScaledSize(QSize(32, 32))
        self.movieRunning.frameChanged.connect(self.updateQueueAnimation)

        toolbar.addWidget(btnQueueStatus)
        self.btnQueueStatus.clicked.connect(self.toggleQueueStatusPopup)

        self.btnToken = btnToken = QPushButton("", self)
        self.btnToken.setToolTip("Tokens")
        btnToken.setObjectName("toolbarButton")
        btnToken.setIcon(pyside_util.get_resource_qicon("circle.svg"))
        btnToken.clicked.connect(self.toggleTokenStatusPopup)
        toolbar.addWidget(btnToken)

        self.btnUser = btnUser = QPushButton("", self)
        btnUser.setObjectName("toolbarButton")
        self.btnUser.setToolTip("User Profile")
        btnUser.setIcon(pyside_util.get_resource_qicon("user.svg"))
        btnUser.setStatusTip("User: ")
        btnUser.setCheckable(True)
        toolbar.addWidget(btnUser)
        btnUser.clicked.connect(self.toggleUserProfilePopup)

        # Create and hide toolbar popups
        self.userProfilePopup = UserProfilePopup(self)
        self.queueStatusPopup = QueueStatusPopup(self)
        self.tokenStatusPopup = TokenStatusPopup(self)
        self.queueStatusPopup.hide()
        self.userProfilePopup.hide()
        self.tokenStatusPopup.hide()

        # Make button fill vertical toolbar space
        for btn in [self.pbLogo, settingsBtn, projectBtn,
                    helpBtn, btnToken, btnUser, btnQueueStatus]:
            btn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)

        self.toolbar = toolbar
        toolbar.hide()

        self.sgnUpdateJobQueueStatus.connect(self.onJobQueueStatusUpdated)
        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")

    def initStatusbar(self):
        self.statusBar().layout().setSpacing(0)
        for widget in [BlueprintReaderStatusWidget,
                       DocumentViewerStatusWidget,
                       TableStatusWidget,
                       JobStatusWidget,
                       RealTimeStatusWidget]:
            w = widget()
            applyDropShadowEffect(w, offset=(0, 1))
            self.statusBar().addPermanentWidget(w, stretch=0)
            if widget == JobStatusWidget:
                w.clicked.connect(self.onJobStatusWidget)
        spacer = QLabel("")
        spacer.setObjectName("statusSpacer")
        self.statusBar().addPermanentWidget(spacer, stretch=1)
        self.statusBar().addPermanentWidget(ZoomStatusWidget())

    def toggleMainTabs(self):
        """For debugging, toggle tabs"""
        if self.tabs.tabBar().isHidden():
            self.tabs.tabBar().show()
        else:
            self.tabs.tabBar().hide()

    def showToolbar(self, show: bool = True):
        self.toolbar.setVisible(show)

    def onSwitchToWorkspace(self, name=None):
        self.showToolbar()
        self.pbOpenDrawer.setEnabled(True)
        self.pbOpenDrawer.setVisible(True)
        self.sidebarVline.show()
        self.statusBar().show()
        if self.tabs.currentIndex() != 1:
            self.tabs.setCurrentIndex(1)
            self.toggleDrawer()
        self.shortenDrawerHeight()

    def shortenDrawerHeight(self, shorten=True, extra: int = 0):
        """Add margin to drawer when tab bar is visible so it doesn't cover it

        Should autodetect. Shorten arg is deprecated
        """
        padding = 0
        tabBarHeight = 32
        if isinstance(self.workspaceView.currentWidget(), ProjectView):
            if self.workspaceView.currentWidget().tabs.count():
                padding = tabBarHeight # Tab bar height
        padding += extra
        padding += self.statusBar().height() - 2
        self.sidebarWidget.layout().setContentsMargins(0, 0, 0, padding)

    def switchToWorkspace(self, name=None):
        self.sgnSwitchToWorkspace.emit(name)
        self.shortenDrawerHeight(True)

    def switchToForms(self, name: str, params=None): # params arg due to FormsView signature
        """For specific forms, keep toolbar visible"""
        show = name in ["NewProjectSetupForm", "NewProjectFileExplorerForm"]
        self.showToolbar(show)
        if show:
            self.tabs.setCurrentIndex(0)
        else:
            self.tabs.setCurrentIndex(0)
        self.hideDrawer()
        self.statusBar().hide()
        self.sidebarVline.hide()
        self.pbOpenDrawer.setEnabled(False)
        self.pbOpenDrawer.setVisible(False)

    def onSetWorkspaceData(self, data):
        """Update workspace UI with data"""
        try:
            self.btnToken.setText(f" {data['tokens']}")
        except Exception as e:
            logger.info(f"Failed to update initial workspace UI {e}, {data}")

    def toggleDrawer(self):
        self.sidebarWidget.setVisible(not self.sidebarWidget.isVisible())
        self.pbLogo.setChecked(self.sidebarWidget.isVisible())
        self.hidePopups()
        self.btnUser.setChecked(False)

        if not self.sidebarWidget.isVisible():
            self.pbOpenDrawer.setIcon(self.iconDrawerOpening)
            self.pbOpenDrawer.setToolTip("Open Sidebar")
        else:
            self.pbOpenDrawer.setIcon(self.iconDrawerClosing)
            self.pbOpenDrawer.setToolTip("Close Sidebar")

        self.updatePbOpenDrawerPosition()

    def updatePbOpenDrawerPosition(self):
        if not self.sidebarWidget.isVisible():
            x = -2
        else:
            x = 300 - (self.pbOpenDrawer.width() // 2)

        y = (self.height() - 26) // 2
        self.pbOpenDrawer.move(x, y)
        self.pbOpenDrawer.raise_()

    def resizeEvent(self, event=None) -> None:
        """Anchor floating drawer button to left side"""
        if self.sidebarWidget.isVisible():
            self.sidebarWidget.setFixedHeight(self.topLevelWidget().height()-self.toolbar.height())
            self.sidebarWidget.setFixedWidth(self.topLevelWidget().width())
        self.updatePbOpenDrawerPosition()
        self.updateUserProfilePosition()
        self.updateQueueStatusPosition()
        self.updateTokenStatusPosition()
        self.windowResized.emit(event)

    def toggleUserProfilePopup(self):
        if self.userProfilePopup.isVisible():
            self.hidePopups()
            return
        self.hideDrawer()
        self.hidePopups()
        self.userProfilePopup.show()
        self.updateUserProfilePosition()

    def toggleQueueStatusPopup(self):
        if self.queueStatusPopup.isVisible():
            self.hidePopups()
            return
        self.hideDrawer()
        self.hidePopups()
        self.queueStatusPopup.show()
        self.updateQueueStatusPosition()

    def toggleTokenStatusPopup(self):
        if self.tokenStatusPopup.isVisible():
            self.hidePopups()
            return
        self.hideDrawer()
        self.hidePopups()
        self.tokenStatusPopup.show()
        self.updateTokenStatusPosition()

    def updateQueueStatusPosition(self):
        if not self.queueStatusPopup.isVisible():
            return
        x = self.btnQueueStatus.x() - (self.queueStatusPopup.width() // 2)
        x = min(x, self.topLevelWidget().width() - self.queueStatusPopup.width() - 16)
        y = self.toolbar.height()
        self.queueStatusPopup.move(x, y)

    def updateTokenStatusPosition(self):
        if not self.tokenStatusPopup.isVisible():
            return
        x = self.btnToken.x() - (self.tokenStatusPopup.width() // 2)
        x = min(x, self.topLevelWidget().width() - self.tokenStatusPopup.width() - 16)
        y = self.toolbar.height()
        self.tokenStatusPopup.move(x, y)

    def updateUserProfilePosition(self):
        """Enforce anchored to top right, do not care about width"""
        if not self.userProfilePopup.isVisible():
            return
        x = self.topLevelWidget().width() - self.userProfilePopup.width() - 4
        y = self.toolbar.height()
        self.userProfilePopup.move(x, y)

    def hidePopups(self):
        self.btnUser.setChecked(False)
        for popup in [self.queueStatusPopup, self.userProfilePopup, self.tokenStatusPopup]:
            if popup.isVisible():
                popup.hide()
                return

    def onUserDetailsGetResponse(self, data):
        try:
            self.btnToken.setText(f" {data['tokens']}")
        except Exception as e:
            logger.info(f"Failed to sync user details {e}")

    def onOpenProject(self, data):
        """Just for caching opened project data"""
        self.projectData = data
        self.projectBtn.setEnabled(True)

    def onAddSource(self):
        """ User chooses a PDF """
        projectId = self.projectData.get("id")
        if not projectId:
            return
        filename, _ = QFileDialog.getOpenFileName(self,
                                                  "Open PDF File",
                                                  "",
                                                  "PDF Files (*.pdf);;All Files (*)")
        if not filename:
            return
        for document in self.projectData.get("documents", []):
            if filename in document.get("filename", ""):
                msgBox = QMessageBox()
                msgBox.setWindowTitle("Already Added")
                msgBox.setIcon(QMessageBox.Critical)
                msgBox.setText("Document has already been added")
                msgBox.exec()
                return

        pub.sendMessage("project-source-add", data = {"projectId": projectId, "filename": filename})

    def onShowProject(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def onCloseProject(self):
        print("close project")
        self.projectBtn.setEnabled(False)
        projectId = self.projectData.get("id")
        if not projectId:
            return
        pub.sendMessage("project-close", projectId=projectId)
        self.workspaceView.setCurrentWidget()
        self.shortenDrawerHeight(False)
        self.toolbar.layout().update()
        self.toolbar.update()

    def onProjectSourceAddResponse(self, data):
        success = data["ok"]
        msgBox = QMessageBox()
        if success:
            msgBox.setWindowTitle("Added Source   ")
            text = f"{data['filename']}"
            text += "\n"
            # text += "\n"
            # text += "A new preprocessing job on this project source is running in the background. "
            # text += "Preprocessing is required for ROI extraction."
            msgBox.setText(text)
            msgBox.setIcon(QMessageBox.Information)
            msgBox.exec()
        else:
            msgBox.setWindowTitle("Insert Failed")
            msgBox.setIcon(QMessageBox.Critical)
            msgBox.setText(data.get("error"))
            msgBox.exec()
            return

        self.projectData["documents"] = data["documents"]
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def onFileMenu(self, action: QAction):

        def closed(event):
            self.dlgReportIssue = None

        if action.text() == "Report Issue":
            if not self.dlgReportIssue:
                self.dlgReportIssue = ReportIssueDialog(None)
                self.dlgReportIssue.closeEvent = closed
            self.dlgReportIssue.show()
            self.dlgReportIssue.raise_()

    def revertFieldMap(self):
        fieldMapJson = getSavedFieldMapJson()
        for a in ["fields", "ancillary_fields", "rfq_fields"]:
            for k, v in fieldMapJson[a].items():
                v["display"] = v["default"]
        saveFieldMapJson(fieldMapJson)

    def onSettingsMenu(self, action):
        if action.text() == "Revert table fields to default":
            if QMessageBox.question(self, "Revert field map?", "Confirm revert field map to default") == QMessageBox.Yes:
                self.revertFieldMap()
                pub.sendMessage("field-map-updated")
        elif action.text() == "Activate a Product Key":
            pub.sendMessage("goto-form", name="ActivateProductKeyForm")
        # elif action == self.actTestMode:
        #     self.updateAiConfig()
        # elif action == self.actChatGpt35:
        #     self.actChatGpt4.setEnabled(False)
        #     self.actChatGpt4.setChecked(not self.actChatGpt35.isChecked())
        #     self.actChatGpt4.setEnabled(True)
        #     self.updateAiConfig()
        # elif action == self.actChatGpt4:
        #     self.actChatGpt35.setEnabled(False)
        #     self.actChatGpt35.setChecked(not self.actChatGpt4.isChecked())
        #     self.actChatGpt35.setEnabled(True)
        #     self.updateAiConfig()

    def updateAiConfig(self):
        # testMode = self.actTestMode.isChecked()
        gptVersion = "4o-Mini" if self.actChatGpt4oMini.isChecked() else "3.5" if self.actChatGpt35.isChecked() else "4" if self.actChatGpt4.isChecked() else "4o"
        # data = {"testMode": testMode, "gptVersion": gptVersion}
        data = {"gptVersion": gptVersion}
        logger.info(f"AI Config: {data}")
        pub.sendMessage("ai-config-update", data=data)

    def syncAiConfig(self, data):
        # self.actTestMode.setChecked(data["testMode"])
        # Just check one, it will update the rest
        self.actChatGpt4oMini.setChecked(data["gptVersion"] == "4o-Mini")
        self.actChatGpt35.setChecked(data["gptVersion"] == "3.5")
        self.actChatGpt4.setChecked(data["gptVersion"] == "4")
        self.actChatGpt4o.setChecked(data["gptVersion"] == "4o")

    def showAppMessageBox(self, msg, title, icon):
        msgBox = QMessageBox()
        msgBox.setText(msg + "                                  ")
        msgBox.setWindowTitle(title + "              ")
        if icon == "critical":
            msgBox.setIcon(QMessageBox.Critical)
        elif icon == "question":
            msgBox.setIcon(QMessageBox.Question)
        else:
            msgBox.setIcon(QMessageBox.Information)
        msgBox.exec()

    def onShowAppMessageBox(self, msg, title, icon="info"):
        self.sgnShowMessageBox.emit(msg, title, icon)

    def goToProjectView(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def onBtnToken(self):
        if self.tokenStatusPopup:
            self.tokenStatusPopup.show()
            self.tokenStatusPopup.raise_()
            return
        self.tokenStatusPopup = TokenPaymentForm(parent=None)
        self.tokenStatusPopup.setWindowTitle("Payment")
        self.tokenStatusPopup.setMinimumSize(800, 600)
        self.tokenStatusPopup.show()
        self.tokenStatusPopup.closeEvent = self.ontokenPaymentClosed

    def onTokenPaymentClosed(self, event=None):
        self.tokenPaymentPopup = None

    def tempClosePrompt(self):
        res = QMessageBox.question(self,
                            "Confirm exit?",
                            "Any unsaved changes will be lost. Exit?                 ",
                            buttons=QMessageBox.Yes | QMessageBox.No)
        return res

    def closeEvent(self, event: QCloseEvent) -> None:
        """Check for unsaved tables"""
        from src.views.projectview import ProjectView
        projectView: ProjectView = self.workspaceView.getView("ProjectView")
        if projectView.getTableViews():
            res = self.tempClosePrompt() # projectView.promptSaveTables()
            if res == QMessageBox.No:
                event.ignore()
                return

        for window in QApplication.topLevelWidgets():
            window.close()
        return super().closeEvent(event)

    def onSupportTicket(self, action):
        resp = QMessageBox.question(self, 'Support Ticket',  f'Open new support ticket? (Opens mail client)',
                                    QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes)
        if resp == QMessageBox.Yes:
            from src.views.supportticket import mailto
            addresses = ["<EMAIL>", "<EMAIL>"]
            mailto(address=addresses, subject="ATEM Support Ticket:")

    def onContactDetails(self, action):

        def onClose(event):
            self.contactDetails = None

        if not self.contactDetails:
            self.contactDetails = ContactDetails(self)
            self.contactDetails.closeEvent = onClose
        self.contactDetails.raise_()

    def onProjectTabChanged(self, tabs):
        """Dynamically display status widgets"""
        widget = tabs.currentWidget()
        from src.views.tableresultsview import TableResultsViewBase
        from src.views.blueprintreaderview import BlueprintReaderView
        from src.views.documentsview import DocumentsView

        # Shorten the drawer to adjust for horizontal scrollbar of a table view
        self.shortenDrawerHeight(True, extra=0)

        for ch in self.statusBar().children():
            visible = True
            if isinstance(ch, RealTimeStatusWidget): # Let widget handle internally
                continue
            if isinstance(ch, JobStatusWidget): # Always visible
                continue
            elif ch.objectName() == "statusSpacer":
                continue
            elif isinstance(ch, TableStatusWidget) and isinstance(widget, TableResultsViewBase):
                pass
            elif isinstance(ch, BlueprintReaderView) and isinstance(widget, (BlueprintReaderView)):
                pass
            elif isinstance(ch, DocumentViewerStatusWidget) and isinstance(widget, DocumentsView):
                pass
            elif isinstance(ch, ZoomStatusWidget) and isinstance(widget, TableResultsViewBase):
                pass
            else:
                visible = False

            try:
                ch.setVisible(visible)
            except:
                pass

    def onJobStatusWidget(self):
        self.toggleQueueStatusPopup()

    def onJobQueueUpdated(self, data):
        self.sgnUpdateJobQueueStatus.emit(data)

    def onJobQueueStatusUpdated(self, data):
        jobs = data.get("jobs")
        incomplete = 0
        for n, j in enumerate(jobs):
            if j["finished"] is not True:
                incomplete += 1
                break
        if incomplete:
            self.movieRunning.start()
        else:
            self.movieRunning.stop()
            self.btnQueueStatus.setIcon(pyside_util.get_resource_qicon("play-circle.svg"))

    def updateQueueAnimation(self):
        self.btnQueueStatus.setIcon(QIcon(self.movieRunning.currentPixmap()))

    def setWindowTitle(self, text=None):
        if not text:
            return super().setWindowTitle(__version__.get_base_app_title())
        try:
            return super().setWindowTitle(f'{__version__.get_base_app_title()} - {text}')
        except:
            return super().setWindowTitle(__version__.get_base_app_title())

    def onTimerRefreshToken(self):
        pub.sendMessage("refresh-token")

    def eventFilter(self, source: QObject, event: QEvent) -> bool:
        if source != self.sidebarWidget:
            if self.sidebarWidget.isVisible():
                if event.type() == QEvent.MouseButtonPress:
                    if event.button() == Qt.LeftButton:
                        self.toggleDrawer()
        return super().eventFilter(source, event)

    def hideDrawer(self):
        self.hidePopups()
        if self.sidebarWidget.isVisible():
            self.sidebarWidget.setVisible(False)
            self.pbLogo.setChecked(False)
            self.pbOpenDrawer.setIcon(self.iconDrawerOpening)
            self.pbOpenDrawer.setToolTip("Open Sidebar")
            self.updatePbOpenDrawerPosition()

    def importProjectDialogCloseEvent(self, event:QCloseEvent):
        super(TableImporter, self.dlgProjectDataImporter).closeEvent(event)
        self.dlgProjectDataImporter = None

    def onImportProjectBomGeneral(self):
        if self.dlgProjectDataImporter:
            self.dlgProjectDataImporter.raise_()
            return
        projectId = self.projectData.get("id")
        self.dlgProjectDataImporter = TableImporter(projectId=projectId, importType=TableImporter.BOM_GENERAL)
        self.dlgProjectDataImporter.closeEvent = self.importProjectDialogCloseEvent
        self.dlgProjectDataImporter.importActivated.connect(self.onImportProjectData)
        self.dlgProjectDataImporter.show()

    def onImportProjectRfq(self):
        if self.dlgProjectDataImporter:
            self.dlgProjectDataImporter.raise_()
            return
        projectId = self.projectData.get("id")
        self.dlgProjectDataImporter = TableImporter(projectId=projectId, importType=TableImporter.RFQ)
        self.dlgProjectDataImporter.closeEvent = self.importProjectDialogCloseEvent
        self.dlgProjectDataImporter.rfqImported.connect(self.onImportRfqData)
        self.dlgProjectDataImporter.show()

    def onImportProjectData(self, data: dict):
        """Directly imports project data into tables"""
        # Ensure we have a results dictionary
        if "results" not in data:
            data = {"results": data}

        # Add status for compatibility with existing code
        data["status"] = "success"
        data["tokens_used"] = 0

        # Get list of tables that should be appended (if provided)
        append_tables = data.get("append_tables", [])

        # Handle tables that should be appended
        if append_tables:
            projectView: ProjectView = self.workspaceView.getView("ProjectView")
            tableViews = projectView.getTableViews()

            for payload_name in append_tables:
                # Skip if this payload isn't in the results
                if payload_name not in data["results"]:
                    continue

                # Find the corresponding table view
                table_name = get_table_from_payload(payload_name)
                if not table_name:
                    continue

                # Find the matching table view
                matching_views = [tv for tv in tableViews if str(tv).lower() == table_name]
                if not matching_views:
                    continue

                tableView = matching_views[0]

                # Get the data to append
                appendDf = data["results"][payload_name]

                # Get existing data and append
                existingDf = tableView.getTableData(drop_uid=True)
                newDf = pd.concat([existingDf, appendDf], ignore_index=True)

                # Replace the data in the results with the combined data
                data["results"][payload_name] = newDf

        # Send the data to be processed
        pub.sendMessage("roi-extraction-response", res=data)

    def onImportRfqData(self, data: dict):
        rfq_data = data["rfq_data"]
        self.workspaceView.views["ProjectView"].tabs.updateRfq(rfq_data)

    def detectWeldsCloseEvent(self, event:QCloseEvent):
        super(WeldDetect, self.dlgDetectWelds).closeEvent(event)
        self.dlgDetectWelds = None

    def auditToolCloseEvent(self, event: QCloseEvent):
        res = QMessageBox.question(self, "Confirm Close", "Confirm close?")
        if res != QMessageBox.Yes:
            event.ignore()
            self.dlgAuditTool.raise_()
            return
        super(AuditDialog2, self.dlgAuditTool).closeEvent(event)
        self.dlgAuditTool = None

    def pluginDialogCloseEvent(self, event: QCloseEvent):
        res = QMessageBox.question(self, "Confirm Close", "Confirm close?")
        if res != QMessageBox.Yes:
            event.ignore()
            self.dlgPluginDialog.raise_()
            return
        super(PluginDialog, self.dlgPluginDialog).closeEvent(event)
        self.dlgPluginDialog = None

    def onDetectWelds(self):
        if self.dlgDetectWelds:
            self.dlgDetectWelds.raise_()
            return
        self.dlgDetectWelds = WeldDetect(None, None)
        self.dlgDetectWelds.closeEvent = self.detectWeldsCloseEvent
        self.dlgDetectWelds.show()

    def openAuditTool(self):
        if self.dlgAuditTool:
            self.dlgAuditTool.raise_()
            return
        projectView: ProjectView = self.workspaceView.getView("ProjectView")
        tableViews =  projectView.getTableViews()
        bom_df = pd.DataFrame
        for tableView in tableViews:
            if str(tableView) == "BOM":
                bom_df = tableView.getTableData(drop_uid=True)
                break

        if bom_df.empty:
            QMessageBox.warning(self, "Audit Tool", "No BOM data available to audit")
            return

        self.dlgAuditTool = AuditDialog2(None, bom_df)
        self.dlgAuditTool.closeEvent = self.auditToolCloseEvent
        self.dlgAuditTool.show()

    def getAllTableData(self):
        projectView: ProjectView = self.workspaceView.getView("ProjectView")
        tableViews =  projectView.getTableViews()
        data = {}
        for tableView in tableViews:
            data[get_payload_name(str(tableView))] = tableView.getTableData(drop_uid=False)
        return data

    def openPluginDialog(self):
        if self.dlgPluginDialog:
            self.dlgPluginDialog.raise_()
            return
        self.dlgPluginDialog = PluginDialog(None)
        self.dlgPluginDialog.importActivated.connect(self.onImportProjectData)
        self.dlgPluginDialog.getAllTableData = self.getAllTableData
        self.dlgPluginDialog.closeEvent = self.pluginDialogCloseEvent
        self.dlgPluginDialog.show()

    def rawDataViewerCloseEvent(self, event: QCloseEvent):
        res = QMessageBox.question(self, "Confirm Close", "Confirm close?")
        if res != QMessageBox.Yes:
            event.ignore()
            self.dlgRawDataViewer.raise_()
            return
        super(RawDataViewer, self.dlgRawDataViewer).closeEvent(event)
        self.dlgRawDataViewer = None

    def openRawDataViewer(self):
        if self.dlgRawDataViewer:
            self.dlgRawDataViewer.raise_()
            return
        self.dlgRawDataViewer = RawDataViewer(None)
        self.dlgRawDataViewer.getAllTableData = self.getAllTableData
        self.dlgRawDataViewer.closeEvent = self.rawDataViewerCloseEvent
        self.dlgRawDataViewer.show()
