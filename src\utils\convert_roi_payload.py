
import json
from copy import deepcopy

def convert_rois(rois) -> list:
    """
    Convert ROIs suitable extraction handling

    - Key name change from columnName to name
    - For tables, convert and calculate column ratios to coordinates

    """
    converted = deepcopy(rois)

    for roi in converted:
        roi["columnName"] = roi["name"]
        del roi["name"]
        if not roi["isTable"]:
            continue
        tableColumns = []

        # The table relative coords
        coords = {}
        for c in ["X0", "Y0", "X1", "Y1"]:
            key = "relative" + c
            coords[key] = roi[key]
            del roi[key]
        roi["tableCoordinates"] = coords

        tableX0 = roi["tableCoordinates"]["relativeX0"]
        tableX1 = roi["tableCoordinates"]["relativeX1"]
        tableWidth = tableX1 - tableX0

        # Convert table column coords
        columnNames = roi["columnNames"]
        del roi["columnNames"]
        columnRatios = roi["columnRatios"]
        # Calculate the column rects and get coords
        for n, columnName in enumerate(columnNames):
            coords = {}

            if n == 0:
                from_ratio = 0
            else:
                from_ratio = columnRatios[n-1]

            if n == len(columnNames) - 1:
                to_ratio = 1
            else:
                to_ratio = columnRatios[n]

            columnX0 = tableX0 + (tableWidth * from_ratio)
            columnX1 = tableX0 + (tableWidth * to_ratio)

            # For bottomright x of column
            coords["relativeX0"] = columnX0
            coords["relativeY0"] = roi["tableCoordinates"]["relativeY0"]
            coords["relativeX1"] = columnX1
            coords["relativeY1"] = roi["tableCoordinates"]["relativeY1"]

            tableColumns.append({columnName: coords})

        roi["tableColumns"] = tableColumns

    return converted

def convert_roi_payload(state, force_extract: bool = False, ignore_bom: bool = False):
    """
    Converts ROI state from GUI to payload for worker

    Args:
        state - ROI layout state is a dictionary or a json path
        ignore_bom - Returns valid result even if BOM is not present
    """

    if isinstance(state, str):
        with open(state) as f:
            state = json.load(f)

    payload = {
        "pageToGroup": {},
        "pageOptions": {},
        "groupRois": {},
        "ocr": {},
        "ocrPages": set(), # Only valid OCR pages to extract i.e. if group is also enabled for OCR
        "ocrGroups": set()
    }

    for groupNumber, groupData in state["groups"].items():
        groupNumber = int(groupNumber)
        roiNames = set()
        rois = groupData["rois"]
        if not rois:
            continue
        pages = groupData["pages"]
        groupOcrEnabled = groupData.get("ocr", False)
        if groupOcrEnabled:
            payload["ocrGroups"].add(groupNumber)

        extractGroup = groupData.get("extract", True) or force_extract
        if not extractGroup:
            continue
        if not pages:
            continue

        for p in pages:
            pageNum = p["page"]
            if force_extract or p.get("extract", True):
                payload["pageToGroup"][int(pageNum)] = groupNumber
                if groupOcrEnabled and p.get("ocr", False):
                    payload["ocr"][pageNum] = p.get("ocr", False)
                    payload["ocrPages"].add(pageNum)

        for roi in rois:
            roiNames.add(roi["name"])

        if not ignore_bom and "BOM" not in roiNames:
            raise Exception(f"Invalid, group {groupNumber} requires BOM")

        # payload["groups"][int(groupNumber)] = groupData

        rois = convert_rois(rois)

        payload["groupRois"][groupNumber] = rois

    # Convert to list for json serialization
    payload["ocrPages"] = list(payload["ocrPages"])
    payload["ocrGroups"] = list(payload["ocrGroups"])

    return payload