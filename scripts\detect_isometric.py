import cv2
import numpy as np
import pandas as pd
import math
import fitz
from src.utils.pdf.page_to_opencv import page_to_opencv

page_width, page_height = [2448, 1584]
relative_isometric_area = [0.04811764705882353, 0.027676767676767676, 0.6834150326797386, 0.7986111111111112]
absolute_isometric_area = [relative_isometric_area[0] * page_width,
                           relative_isometric_area[1] * page_height,
                           relative_isometric_area[2] * page_width,
                           relative_isometric_area[3] * page_height]


def detect_text_regions(image,
                        min_area=50):
    """
    Detect text regions using connected component analysis (CCA) method.
    """

    if isinstance(image, str):
        img = cv2.imread(image)
    else:
        img = image  # Expect a CV image

    if img is None:
        raise ValueError("Could not read the image")

    print(f"Image dimensions: {img.shape}")
    img_height, img_width = img.shape[:2]

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply binary threshold - values should be ignored when using OTSU
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

    # Perform bitwise operation
    bitwise = cv2.bitwise_not(thresh)

    # Remove horizontal and vertical border and grid lines
    # if remove_grid:
    #     img2 = remove_grid_lines(bitwise)
    # else:
        # img2 = bitwise
    img2 = bitwise

    # Horizontal dilation - this influences CCA into forming regions by word
    # and reduce grouping of vertical i.e. paragraphs or small regions
    dilated = cv2.dilate(img2, np.ones((2, 3), np.uint8), iterations=1)

    # num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(dilated, connectivity=connectivity)
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStatsWithAlgorithm(dilated, 8, cv2.CV_32S, 0)

    # Pre-calculate all component properties at once using NumPy operations
    areas = stats[:, cv2.CC_STAT_AREA]
    widths = stats[:, cv2.CC_STAT_WIDTH]
    heights = stats[:, cv2.CC_STAT_HEIGHT]
    left = stats[:, cv2.CC_STAT_LEFT]
    top = stats[:, cv2.CC_STAT_TOP]

    # Create boolean mask for size-based filtering
    max_area = img_width * img_height * 0.1
    size_mask = (
        (areas > min_area) &
        (areas < max_area) &
        (widths > 2) &
        (heights > 2)
    )

    # Get indices of components that pass size criteria (excluding background)
    valid_indices = np.where(size_mask)[0]
    valid_indices = valid_indices[valid_indices != 0]  # Remove background (label 0)

    # Extract regions that are likely to contain text
    text_regions = []
    labels_view = labels  # Create a view for faster access

    # Process only the valid components
    for label in valid_indices:
        x = left[label]
        y = top[label]
        w = widths[label]
        h = heights[label]

        # Calculate density using the original method but with optimized array access
        mask = (labels_view[y:y+h, x:x+w] == label)
        density = np.sum(mask) / (w * h)

        if density > 0.2:  # Has enough content
            text_regions.append((x, y, w, h))

    # Sort regions by position using the exact same criteria
    text_regions.sort(key=lambda r: (r[1] // 15, r[0]))

    return text_regions


def detect_text_mser(img):
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    gray = cv2.GaussianBlur(gray, (5, 5), 0)
    gray = cv2.equalizeHist(gray)

    # Create MSER detector
    mser = cv2.MSER_create(min_area=30, max_area=300, delta=5)

    # Detect regions in grayscale
    regions, _ = mser.detectRegions(gray)

    # Create a mask for drawing
    mask = np.zeros_like(gray)
    for p in regions:
        hull = cv2.convexHull(p.reshape(-1, 1, 2))
        cv2.drawContours(mask, [hull], -1, 255, -1)

    # Bitwise-and mask with original image
    text_only = cv2.bitwise_and(img, img, mask=mask)

    # Optionally: draw bounding boxes
    output = img.copy()
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        aspect_ratio = w / float(h)
        area = cv2.contourArea(cnt)
        if area > 100 and 0.1 < aspect_ratio < 10:
            # likely text
        # if w > 30 and h > 10:
            cv2.rectangle(output, (x, y), (x+w, y+h), (0, 255, 0), 2)

    return output

def detect_text_regions(img):
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    # Apply gradient to highlight text edges
    grad = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, np.ones((3, 3), np.uint8))

    # Binarize the image
    _, binary = cv2.threshold(grad, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Use morphological closing to connect text lines
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 5))
    connected = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # Find contours
    contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter and draw bounding boxes
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        if w > 30 and h > 10:  # adjust thresholds as needed
            cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)

    return img

def line_length(line):
    x1, y1, x2, y2 = line
    return np.hypot(x2 - x1, y2 - y1)

def angle_between(p1, p2):
    """Returns the angle between two points in degrees."""
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    angle_rad = np.arctan2(dy, dx)
    angle_deg = np.degrees(angle_rad)
    # return angle_deg % 180  # ignore direction, keep in [0, 180)
    return (angle_deg + 360) % 360  # ignore direction, keep in [0, 360)

def parse_page_range(page_range_str, total_pages):
    """Parse page range string into list of page numbers (1-based)"""
    if type(page_range_str) == int:
        return [page_range_str]
    elif type(page_range_str) == list:
        return page_range_str
    elif page_range_str is None:
        return list(range(1, total_pages + 1))
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return list(range(1, total_pages + 1))

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            end = min(total_pages, end)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            if 1 <= page <= total_pages:
                pages.append(page)

    return sorted(set(pages))

def distance_to(p1, p2):
    """Returns the distance between two points."""
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])

def get_midline_segment(p1, p2):
    """
    p1 and p2 are each tuples of two points: ((x1, y1), (x2, y2)),
    representing two points on two parallel lines.
    Returns the start and end point of the midline segment.
    """
    A1, A2 = p1
    B1, B2 = p2

    # Compute midpoints between corresponding points
    M1 = ((A1[0] + B1[0]) / 2, (A1[1] + B1[1]) / 2)
    M2 = ((A2[0] + B2[0]) / 2, (A2[1] + B2[1]) / 2)

    return M1, M2  # Start and end point of midline

def detect_lines(img: np.ndarray):
    """Detect lines in an image

    Args:
        img: The input image
        dist_thresh: Distance threshold for merging
        angle_thresh: Angle threshold (in degrees)
        min_points: Minimum number of pixels per line segment

    """
    """Targets findings line segments belonging to arrow pointers"""
    # Make everything outside of isometric area black
    img = img.copy()
    masked = np.zeros_like(img)
    image_width = img.shape[1]
    image_height = img.shape[0]
    x1, y1, x2, y2 = relative_isometric_area
    x1 = int(x1) * image_width
    y1 = int(y1) * image_height
    x2 = int(x2) * image_width
    y2 = int(y2) * image_height
    masked[y1:y2, x1:x2] = img[y1:y2, x1:x2]
    # img = masked

    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    lsd = cv2.createLineSegmentDetector(refine=cv2.LSD_REFINE_ADV)

    # Detect lines
    lines = lsd.detect(gray)[0]  # Position 0 has the lines

    lines = [line[0] for line in lines]

    import random
    def random_color(min_val=100, max_val=255):
        return (random.randint(min_val, max_val),
                random.randint(min_val, max_val),
                random.randint(min_val, max_val))

    points = []
    line_data = []
    # Draw detected lines
    for line in lines:
        color = random_color()
        a, b = (line[0], line[1]), (line[2], line[3])
        d = {
            # 'pdf_page': page_num + 1,
            'x1': line[0] / image_width,
            'y1': line[1] / image_height,
            'x2': line[2] / image_width,
            'y2': line[3] / image_height,
            'angle': angle_between(a, b),
            'distance': line_length(line)
        }
        d["start_point"] = (d['x1'], d['y1'])
        d["end_point"] = (d['x2'], d['y2'])

        # Do not add if similar paired line already added
        thresh = 1
        scale = 100
        sp1 = (d['x1'] * scale, d['y1'] * scale)
        ep1 = (d['x2'] * scale, d['y2'] * scale)
        for p in points:
            if any([
                (distance_to(sp1, p["end_point"]) < thresh and distance_to(ep1, p["start_point"]) < thresh),
                # (distance_to(sp1 , p["start_point"]) < thresh and distance_to(ep1, p["end_point"]) < thresh),
            ]):
                pt_midline = get_midline_segment((d["start_point"], d["end_point"]), p["point"])
                d["start_point"] = pt_midline[0]
                d["end_point"] = pt_midline[1]
                break
                pass
        else:
            cv2.line(img, (int(line[0]), int(line[1])), (int(line[2]), int(line[3])), color, 2)
            points.append({"start_point": sp1, "end_point": ep1, "point": (d["start_point"], d["end_point"])})
            orientation = None
            d["orientation"] = orientation
            line_data.append(d)

    return line_data, img

def draw_fitz_page(page: fitz.Page,
                   zoom: int = 3,
                   lines: bool = True,
                   text: bool = False) -> np.ndarray:
    """Redraw specific page elements and return the image.

    Args:
        text: Whether to include text in the image. - Not implemented
        lines: Whether to include lines in the image.
        zoom: Zoom factor for the image.
    """
    # Get the page's drawing operations
    # img = page_to_opencv(page, zoom=zoom)
    drawings = page.get_drawings()

    # Create a blank image with white background
    img = np.ones((int(page.rect.height * zoom),
                   int(page.rect.width * zoom),
                  3), dtype=np.uint8) * 255

    # Scale the isometric area according to zoom factor
    iso_area = [
        int(absolute_isometric_area[0] * zoom),
        int(absolute_isometric_area[1] * zoom),
        int(absolute_isometric_area[2] * zoom),
        int(absolute_isometric_area[3] * zoom)
    ]

    # Draw only the vector graphics within isometric area
    for drawing in drawings:
        items = drawing["items"]  # List of drawing commands
        width = drawing.get("width", 1)
        if not width:
            width = 1
        width = int(math.ceil(width) * zoom)
        for item in items:
            if item[0] == "l":  # Line drawing command
                # Extract line coordinates and scale them
                point1 = item[1]
                point2 = item[2]
                x1, y1 = int(point1[0] * zoom), int(point1[1] * zoom)
                x2, y2 = int(point2[0] * zoom), int(point2[1] * zoom)

                # Check if line is within isometric area
                if (iso_area[0] <= x1 <= iso_area[2] and
                    iso_area[1] <= y1 <= iso_area[3] and
                    iso_area[0] <= x2 <= iso_area[2] and
                    iso_area[1] <= y2 <= iso_area[3]):
                    # Draw the line on our image
                    cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), width)

    return img



def get_diagonal_kernel(width, height, angle_deg, thickness=2):
    """
    Creates a rectangular structuring element (kernel) with a line at the specified angle.

    Parameters:
        width (int): Width of the kernel.
        height (int): Height of the kernel.
        angle_deg (float): Line angle in degrees (0° = horizontal, 90° = vertical).
        thickness (int): Line thickness in pixels.

    Returns:
        np.ndarray: Binary kernel with a rotated line.
    """
    # Normalize the angle to [0°, 360°)
    angle_deg = angle_deg % 360

    # Create a blank kernel (rectangular)
    kernel = np.zeros((height, width), dtype=np.uint8)

    # Convert angle to radians
    angle_rad = np.deg2rad(angle_deg)

    # Calculate the center of the kernel
    center_x = width // 2
    center_y = height // 2
    length = min(width, height) // 2 - 1

    # Calculate start and end points for the line based on the angle
    x1 = int(center_x - length * np.cos(angle_rad))
    y1 = int(center_y - length * np.sin(angle_rad))
    x2 = int(center_x + length * np.cos(angle_rad))
    y2 = int(center_y + length * np.sin(angle_rad))

    # Draw the line onto the kernel
    cv2.line(kernel, (x1, y1), (x2, y2), 1, thickness=thickness)

    return kernel

def remove_horizontal_lines(image):
    result = image.copy()
    if len(image.shape) == 3:
        result = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        result = image.copy()
    thresh = cv2.threshold(result, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40,1))
    remove_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
    cnts = cv2.findContours(remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255,255,255), 4)
    return result

def is_straight_line(contour, tolerance=0.09, aspect_ratio_thresh=7, max_deviation=1.8):
    """
    Determines if a contour is approximately a straight line.

    Parameters:
        contour (numpy.ndarray): A single contour from cv2.findContours().
        tolerance (float): Epsilon for approxPolyDP (smaller = more accurate).
        aspect_ratio_thresh (float): Minimum aspect ratio to consider it a line.
        max_deviation (float): Max average deviation from fitted line.

    Returns:
        bool: True if the contour is a straight line, False otherwise.
    """
    # Approximate contour
    epsilon = tolerance * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)

    if len(approx) <= 2:
        return True  # Very likely a line

    # Bounding rectangle aspect ratio
    x, y, w, h = cv2.boundingRect(approx)
    if h == 0 or w == 0:
        return False
    aspect_ratio = max(w, h) / min(w, h)
    if aspect_ratio < aspect_ratio_thresh:
        return False  # Not elongated enough to be a line

    # Fit a line and measure deviation
    [vx, vy, x0, y0] = cv2.fitLine(contour, cv2.DIST_L2, 0, 0.01, 0.01)
    direction = np.array([vx, vy]).reshape(2,)
    origin = np.array([x0, y0]).reshape(2,)

    deviations = []
    for point in contour:
        p = point[0]
        vec = np.array(p) - origin
        proj_len = np.dot(vec, direction)
        proj_point = origin + proj_len * direction
        dist = np.linalg.norm(np.array(p) - proj_point)
        deviations.append(dist)

    avg_deviation = np.mean(deviations)
    return avg_deviation < max_deviation


def contour_line_endpoints(contour):
    """
    Given a contour that's a straight line, return its endpoints (x1, y1), (x2, y2).

    Parameters:
        contour (numpy.ndarray): Input contour (already verified as a line)

    Returns:
        tuple: (x1, y1, x2, y2)
    """
    # Fit line
    [vx, vy, x0, y0] = cv2.fitLine(contour, cv2.DIST_L2, 0, 0.01, 0.01)
    direction = np.array([vx[0], vy[0]])
    origin = np.array([x0[0], y0[0]])

    # Project each contour point onto the line
    projections = []
    for point in contour:
        p = point[0]
        vec = np.array(p) - origin
        proj_len = np.dot(vec, direction)
        projections.append((proj_len, p))

    # Sort projections to get endpoints
    projections.sort(key=lambda x: x[0])
    p1 = projections[0][1]
    p2 = projections[-1][1]

    return tuple(p1), tuple(p2)

def detect_isometric_lines(image):
    """Image must be rgb if drawing contours color"""

    all_lines = []

    result = image.copy()
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

    # Remove horizontal lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
    remove_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=1)
    cnts = cv2.findContours(remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255, 255, 255), 4)

    # Remove vertical lines
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 16))
    remove_vertical = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel, iterations=3)
    cnts = cv2.findContours(remove_vertical, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        rect = cv2.boundingRect(c)
        # cv2.drawContours(result, [c], -1, (255, 0, 0), 4)
        p1, p2 = contour_line_endpoints(c)
        x1, y1, x2, y2 = p1 + p2  # Unpack the tuple into separate variables
        rect = cv2.boundingRect(c)
        # cv2.drawContours(result, [c], -1, (0, 0, 255), 4)
        cv2.line(result, p1, p2, (0, 0, 255), 3)

        length = distance_to(p1, p2)
        all_lines.append({
            'x1': x1,
            'y1': y1,
            'x2': x2,
            'y2': y2,
            'length': length,
            'rect': rect,
            'angle': angle_between(p1, p2),
            'base_angle': 90,
        })

    k = 40  # size of the diagonal kernel
    # diagonal_kernel = np.eye(k, dtype=np.uint8)

    def get_diagonal_lines(angle_deg, thickness=2, color=(0, 0, 255), kernel_width=30, kernel_height=20, min_line_length=70):
        lines = []
        diagonal_kernel = get_diagonal_kernel(kernel_width, kernel_height, angle_deg=angle_deg, thickness=2)

        # Step 1: Close to connect dashed lines
        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, diagonal_kernel, iterations=2)

        # Step 2: Then Open to remove them
        remove_diagonal = cv2.morphologyEx(closed, cv2.MORPH_OPEN, diagonal_kernel, iterations=1)

        cnts = cv2.findContours(remove_diagonal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cnts = cnts[0] if len(cnts) == 2 else cnts[1]
        for c in cnts:
            if not is_straight_line(c, tolerance=0.09, aspect_ratio_thresh=12, max_deviation=1.8):
                continue
            p1, p2 = contour_line_endpoints(c)
            x1, y1, x2, y2 = p1 + p2
            length = distance_to(p1, p2)
            area = cv2.contourArea(c)
            rect = cv2.boundingRect(c)

            if length < min_line_length:
                continue
            if area < 60:
                continue

            # cv2.drawContours(result, [c], -1, (255, 0, 0), 4)
            cv2.line(result, p1, p2, color, thickness)

            lines.append({
                'x1': x1,
                'y1': y1,
                'x2': x2,
                'y2': y2,
                'length': length,
                'rect': rect,
                'angle': angle_between(p1, p2),
                'base_angle': angle_deg
            })
        return lines


    # 330 lines
    lines = get_diagonal_lines(angle_deg=330, thickness=2, color=(255, 0, 0))
    all_lines.extend(lines)

    # 30 lines
    lines = get_diagonal_lines(angle_deg=30, thickness=2, color=(0, 255, 0))
    all_lines.extend(lines)

    lines_df = pd.DataFrame(all_lines)
    lines_df["category"] = "isometric_line"

    # Back to relative coordinates
    lines_df["x1"] *= 1 / image.shape[1]
    lines_df["y1"] *= 1 / image.shape[0]
    lines_df["x2"] *= 1 / image.shape[1]
    lines_df["y2"] *= 1 / image.shape[0]
    return lines_df, result

def diagonal_structure(image):
    result = image.copy()
    gray = cv2.cvtColor(image,cv2.COLOR_BGR2GRAY)
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]


def remove_thin_lines(image, mode='black', kernel_size=3, iterations=1):
    """
    Removes thin lines from a binary or grayscale image using morphological operations.

    Parameters:
        image (numpy.ndarray): Input image (grayscale or binary).
        mode (str): 'black' to remove thin black lines on white bg,
                    'white' to remove thin white lines on black bg.
        kernel_size (int): Size of the square kernel (must be odd).
        iterations (int): Number of times the operation is applied.

    Returns:
        numpy.ndarray: Image with thin lines removed.
    """
    # Ensure grayscale
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Convert to binary
    _, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)

    # Define kernel
    kernel = np.ones((kernel_size, kernel_size), np.uint8)

    # Morphological operation
    if mode == 'black':
        # Removes thin black lines (on white background)
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=iterations)
    elif mode == 'white':
        # Removes thin white lines (on black background)
        processed = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=iterations)
    else:
        raise ValueError("Mode must be either 'black' or 'white'.")

    return processed


def dilate_diagonally(image, direction='main', kernel_size=3, iterations=1):
    """
    Dilates an image along a diagonal direction.

    Parameters:
        image (numpy.ndarray): Input grayscale or binary image.
        direction (str): 'main' for top-left to bottom-right,
                         'anti' for top-right to bottom-left.
        kernel_size (int): Size of the square kernel (must be odd and >= 3).
        iterations (int): Number of dilation iterations.

    Returns:
        numpy.ndarray: Diagonally dilated image.
    """
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    if direction == 'main':
        kernel = np.eye(kernel_size, dtype=np.uint8)  # Top-left to bottom-right
    elif direction == 'anti':
        kernel = np.fliplr(np.eye(kernel_size, dtype=np.uint8))  # Top-right to bottom-left
    else:
        raise ValueError("Direction must be 'main' or 'anti'.")

    dilated = cv2.dilate(image, kernel, iterations=iterations)
    return dilated

def line_intersection(pt1, pt2):
    """Infinite line intersection"""
    (x1, y1), (x2, y2) = pt1
    (x3, y3), (x4, y4) = pt2

    # Compute the denominator
    denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

    if denom == 0:
        return None  # Lines are parallel or coincident

    # Compute intersection point
    px = ((x1*y2 - y1*x2)*(x3 - x4) - (x1 - x2)*(x3*y4 - y3*x4)) / denom
    py = ((x1*y2 - y1*x2)*(y3 - y4) - (y1 - y2)*(x3*y4 - y3*x4)) / denom

    return (px, py)

def segment_intersection(pt1, pt2):
    A1, A2, B1, B2 = pt1[0], pt1[1], pt2[0], pt2[1]
    """
    Returns the intersection point of two line segments if they intersect, otherwise None.
    """
    def ccw(P, Q, R):
        return (R[1] - P[1]) * (Q[0] - P[0]) > (Q[1] - P[1]) * (R[0] - P[0])

    def segments_intersect(P1, P2, P3, P4):
        return (ccw(P1, P3, P4) != ccw(P2, P3, P4)) and (ccw(P1, P2, P3) != ccw(P1, P2, P4))

    def line_intersection(A1, A2, B1, B2):
        x1, y1 = A1
        x2, y2 = A2
        x3, y3 = B1
        x4, y4 = B2

        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if denom == 0:
            return None  # Lines are parallel

        px = ((x1*y2 - y1*x2)*(x3 - x4) - (x1 - x2)*(x3*y4 - y3*x4)) / denom
        py = ((x1*y2 - y1*x2)*(y3 - y4) - (y1 - y2)*(x3*y4 - y3*x4)) / denom
        return (px, py)

    if segments_intersect(A1, A2, B1, B2):
        return line_intersection(A1, A2, B1, B2)
    else:
        return None

def detect_isometric(img: cv2.Mat):
    """

    Args:
        img: Ideal image should be cleaned of text and labels

    Returns:
        iso_lines_df: DataFrame of detected isometric lines
        img: Image with detected isometric lines drawn
    """
    # Thinning line can work help to isolate isometric lines if the isometric
    # itself is distinctly thicker from other lines
    img2 = remove_thin_lines(img.copy(), kernel_size=3, iterations=1)

    # Invert: black becomes white and vice versa
    inverted = cv2.bitwise_not(img2)
    kernel_size = 3
    iterations = 1
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    dilated = cv2.dilate(inverted, kernel, iterations=iterations)

    img2 = cv2.bitwise_not(dilated)

    # gray = cv2.cvtColor(img_thinned, cv2.COLOR_BGR2GRAY)
    img2_rgb = cv2.cvtColor(img2, cv2.COLOR_GRAY2RGB)
    iso_lines_df, img2 = detect_isometric_lines(img2_rgb)

    endpoints = []
    for index, line1 in iso_lines_df.iterrows():
        x1, y1, x2, y2 = line1["x1"], line1["y1"], line1["x2"], line1["y2"]
        endpoints.append({
            "x1": x1,
            "y1": y1,
            "length": line1["length"],
            "index": index,
            "base_angle": line1["base_angle"]
        })
        endpoints.append({
            "x1": x2,
            "y1": y2,
            "length": line1["length"],
            "index": index,
            "base_angle": line1["base_angle"]
        })

    endpoints_data = []
    for endpoint1 in endpoints:
        index1 = endpoint1["index"]
        x1, y1 = endpoint1["x1"] * img.shape[1], endpoint1["y1"] * img.shape[0]
        pt1 = (x1, y1)
        connected = False
        for endpoint2 in endpoints:
            x2, y2 = endpoint2["x1"] * img.shape[1], endpoint2["y1"] * img.shape[0]
            pt2 = (x2, y2)
            if index1 == endpoint2["index"]:
                continue
            if distance_to(pt1, pt2) < 40:
                connected = True
                cv2.circle(img2, (int(x1), int(y1)), 8, (0, 200, 0), -1)
                break
        else:
            cv2.circle(img2, (int(x1), int(y1)), 8, (0, 0, 200), -1)
            cv2.circle(img2, (int(x2), int(y2)), 8, (0, 0, 200), -1)

        endpoints_data.append({
            "x1": endpoint1["x1"],
            "y1": endpoint1["y1"],
            "x2": endpoint2["x1"],
            "y2": endpoint2["y1"],
            "length": endpoint1["length"],
            "base_angle": endpoint1["base_angle"],
            "connected": connected,
            "category": "endpoint"
        })

    endpoints_df = pd.DataFrame(endpoints_data)

    isometric_df = pd.concat([iso_lines_df, endpoints_df], ignore_index=True)

    if "connected" not in isometric_df.columns:
        isometric_df["connected"] = False

    # TODO find intersections
    # Maybe need to extend line or checks proximity to points on other line
    # lines_diagonal = iso_lines_df[iso_lines_df["base_angle"].isin([30, 330])]
    # for index, line1 in iso_lines_df[iso_lines_df["base_angle"] == 90].iterrows():
    #     ax1, ay1, ax2, ay2 = line1["x1"] * img.shape[1], line1["y1"] * img.shape[0], line1["x2"] * img.shape[1], line1["y2"] * img.shape[0]
    #     lineA = (ax1, ay1), (ax2, ay2)

    #     # cv2.line(img2, (int(ax1), int(ay1)), (int(ax2), int(ay2)), (0, 100, 255), 15)
    #     for index2, line2 in lines_diagonal.iterrows():
    #         if index == index2:
    #             continue
    #         bx1, by1, bx2, by2 = line2["x1"] * img.shape[1], line2["y1"] * img.shape[0], line2["x2"] * img.shape[1], line2["y2"] * img.shape[0]
    #         lineB = (bx1, by1), (bx2, by2)
    #         intersect = segment_intersection(lineB, lineA)
    #         if intersect is None:
    #             continue
    #         x, y = intersect
    #         print(lineB, lineA, x, y)
    #         cv2.circle(img2, (int(x), int(y)), 6, (100, 255, 0), -1)
    #         break

    # Detect junction points
    # for iso_lines in iso_lines_df.values:
    #     x1, y1, x2, y2 = iso_lines["x1"], iso_lines["y1"], iso_lines["x2"], iso_lines["y2"]
    #     cv2.line(img2, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)

    # Arrow thin line detection
    # img3 = img.copy()
    # d, img4 = detect_lines(img3)

    # cv2.imwrite("debug/lines.png", img4)
    # cv2.imshow("window", img4)
    # cv2.waitKey(0)

    # img = img_thinned
    return isometric_df, img2


def mask_image_area(img, relative_area, color="white"):
    # Mask everything in relation to relative area
    img = img.copy()
    if color == "white":
        masked = np.ones_like(img) * 255
    else:
        masked = np.zeros_like(img)
    image_width = img.shape[1]
    image_height = img.shape[0]
    x1, y1, x2, y2 = relative_area
    x1 = int(x1 * image_width)
    y1 = int(y1 * image_height)
    x2 = int(x2 * image_width)
    y2 = int(y2 * image_height)
    masked[y1:y2, x1:x2] = img[y1:y2, x1:x2]
    return masked


def main(page_range="59"):
# def main(page_range="0-20"):
    filename = r"S:\Shared Folders\Client Uploads\Brock Services\S1601 Insulation Only (1).pdf"
    doc = fitz.open(filename)
    page_count = len(doc)
    pages_to_process = parse_page_range(page_range, page_count)

    # Create a new PDF document for output
    output_doc = fitz.open()

    def insert_cv_image_into_pdf():
        pass

    # Naming a window
    cv2.namedWindow("window", cv2.WINDOW_NORMAL)

    for pdf_page in pages_to_process:
        print(f"processing pdf_page {pdf_page}")
        page = doc[pdf_page - 1]

        zoom = 1
        cv_img_original = page_to_opencv(page, zoom=zoom)

        # Only show isometric area
        # cv_img_original = mask_image_area(cv_img_original, relative_isometric_area)

        # Note: Zoom at 1 is critical
        cv_img_original = draw_fitz_page(page, zoom=zoom)

        # Isometric structure
        _, img = detect_isometric(cv_img_original)

        # img = remove_horizontal_lines(img)
        cv2.imshow("window", img)
        cv2.waitKey(0)

        continue
        cv_img = draw_fitz_page(page, zoom=zoom)
        cv_img_original = cv_img.copy()

        cv_lines = cv_img.copy()
        gray = cv2.cvtColor(cv_lines, cv2.COLOR_BGR2GRAY)
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        # Apply Canny edge detection
        edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
        lines = cv2.HoughLinesP(
            edges,
            rho=1,
            theta=np.pi/180,
            threshold=30,
            minLineLength=39,
            maxLineGap=4,
        )
        lines = [tuple(l[0]) for l in lines]


        for line in lines:
            line = [line[0], line[1], line[2], line[3]]
            x1, y1, x2, y2 = line
            cv2.line(cv_lines, (x1, y1), (x2, y2), (255,0,0), 2)

        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)

        success, img_data = cv2.imencode('.png', cv_lines)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())

        line_data, cv_img2 = detect_lines(cv_img)

        image_height = cv_img.shape[0]
        image_width = cv_img.shape[1]
        for l in line_data:
            start_point = l["start_point"]
            end_point = l["end_point"]
            start_point = (int(start_point[0] * image_width), int(start_point[1] * image_height))
            end_point = (int(end_point[0] * image_width), int(end_point[1] * image_height))
            if int(l["angle"]) in [88, 89, 90, 91, 269, 270, 271]:
                # cv2.line(cv_img, start_point, end_point, (255, 255, 255), 5)
                cv2.line(cv_img, start_point, end_point, (255, 0, 0), 3)
            else:
                print(l["angle"])
                # cv2.line(cv_img, start_point, end_point, (255, 0, 255), 2)
                cv2.line(cv_img, start_point, end_point, (0, 0, 255), 4)


        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)

        success, img_data = cv2.imencode('.png', cv_img)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())

        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)
        cv_img = remove_structure(cv_img_original)
        success, img_data = cv2.imencode('.png', cv_img)
        output_page.insert_image(output_page.rect, stream=img_data.tobytes())

        # Encode image as PNG bytes
        success, img_data = cv2.imencode('.png', cv_img)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())

        # ---------------

        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)

        # Convert to grayscale
        cv_img = cv_img_original.copy()
        gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

        # Apply binary threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Step 1: Morphological close to connect dashed diagonal lines
        k = 40  # kernel size — adjust based on line size
        diag_kernel = np.eye(k, dtype=np.uint8)
        diag_kernel = cv2.dilate(diag_kernel, np.ones((3, 3), np.uint8), iterations=1)

        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, diag_kernel, iterations=1)

        # Step 2: Edge detection
        edges = cv2.Canny(closed, 30, 150, apertureSize=3)

        # Step 3: Hough Line Transform to find lines
        lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=80, minLineLength=50, maxLineGap=20)

        # Step 4: Draw diagonal lines (based on angle)
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = abs(np.degrees(np.arctan2(y2 - y1, x2 - x1)))
                if 30 < angle < 60 or 120 < angle < 150:  # adjust for your expected diagonal
                    cv2.line(cv_img, (x1, y1), (x2, y2), (0, 0, 255), 2)

        # Encode image as PNG bytes
        success, img_data = cv2.imencode('.png', closed)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())

    if output_doc.page_count:
        output_doc.save("debug/doc_with_lsd_lines.pdf")



if __name__ == "__main__":
    main()