import os
import fitz


def process_page(page_num,
                 pdf_path,
                 roi_payload: dict,
                 output_images_path: str,
                 dpi: int,
                 gen_field_list=None,
                 width_limit=None):
    # print(f"\n==== PROCESSING PAGE {page_num + 1} ====")

    # preferred and default zoom
    zoom = dpi / 72

    doc = fitz.open(pdf_path)
    page = doc[page_num]
    page_data_storage = {'Page': page_num + 1}
    successful_extractions = 0

    use_pdfium = False

    if use_pdfium:
        import pypdfium2 as pdfium
        pdfium_pdf = pdfium.PdfDocument(pdf_path)
        pdfium_page = pdfium_pdf.get_page(page_num)
        bitmap = pdfium_page.render(scale=zoom)
        img = bitmap.to_pil()
    else:
        img = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))

    page_width = page.rect.width
    page_height = page.rect.height

    default_matrix = fitz.Matrix(zoom, zoom)

    rois = roi_payload["groupRois"].get(roi_payload["pageToGroup"].get(page_num + 1), [])
    fields_to_process = rois

    if not rois:
        print(f"Failed to find ROIs for {page_num + 1}")
        doc.close()
        return page_data_storage, successful_extractions

    # Use fields_to_process instead of converted_fields
    for field in fields_to_process:
        field['name'] = field['columnName']
        field_name = field['name']

        is_table = field.get('isTable', False)
        is_iso = field_name.lower() == "isometric drawing area"

        if use_pdfium:
            page_width = img.width
            page_height = img.height
        else:
            page_width = page.rect.width
            page_height = page.rect.height

        if is_table:
            x0 = field['tableCoordinates']['relativeX0'] * page_width
            y0 = field['tableCoordinates']['relativeY0'] * page_height
            x1 = field['tableCoordinates']['relativeX1'] * page_width
            y1 = field['tableCoordinates']['relativeY1'] * page_height
        else:
            x0 = field['relativeX0'] * page_width
            y0 = field['relativeY0'] * page_height
            x1 = field['relativeX1'] * page_width
            y1 = field['relativeY1'] * page_height

        # print(f"\n--> Processing field: {field_name}")
        # print(f"Field data: {field}")

        matrix = default_matrix
        if width_limit and (x1 - x0) > width_limit:
            adjusted_zoom = (width_limit - 1) / (x1 - x0)
            matrix = fitz.Matrix(adjusted_zoom, adjusted_zoom)
            print(f"\n--> Adjusted zoom for {field_name} to {adjusted_zoom} to fit width limit of {width_limit}")

        # Check if it's any type of table or in the general field list
        is_table = field.get('isTable', False)
        if gen_field_list is not None and not is_table and field_name not in [f.lower() for f in gen_field_list]:
            print(f"Skipping field {field_name} - not in gen_field_list and not a table")
            continue

        def get_largest_image():
            images = page.get_images(full=True)
            # Find the largest image by pixel area (width × height)
            largest_image = max(images, key=lambda img: img[2] * img[3])
            xref = largest_image[0]
            return fitz.Pixmap(doc, xref)

        for attempt in range(3):
            try:
                crop_box = (x0, y0, x1, y1)
                # print(f"Image size: {img.size}, crop box: {crop_box}, field name: {field_name}")
                if use_pdfium:
                    cropped_img = img.crop(crop_box)
                else:
                    crop_rect = fitz.Rect(x0, y0, x1, y1)
                    cropped_img = page.get_pixmap(matrix=matrix, clip=crop_rect)

                # print(f"Field type check:")
                # print(f"  Is table: {is_table}")
                # print(f"  Table type: {field.get('tableType', 'NOT SET')}")
                # print(f"  Is ISO: {is_iso}")

                if is_table:
                    table_type = field_name.lower()
                    # print(f"  Processing as table type: {table_type}")
                    output_path = os.path.join(table_type, f"table_{table_type}_page_{page_num+1}.png")
                elif is_iso:
                    output_path = os.path.join("iso", f"{field_name}_page_{page_num+1}.png")
                else:
                    os.makedirs(os.path.join(output_images_path, "general", field_name), exist_ok=True)
                    output_path = os.path.join("general", field_name, f"{field_name}_page_{page_num+1}.png")
                    # output_path = os.path.join("general", f"{field_name}_page_{page_num+1}.png")

                # print(f"  Saving to: {output_path}")

                if os.path.exists(os.path.join(output_images_path, output_path)):
                    print(f"Warning: File {output_path} already exists. Overwriting.")

                if use_pdfium:
                    cropped_img.save(os.path.join(output_images_path, output_path), dpi=(300, 300))
                else:
                    cropped_img.save(os.path.join(output_images_path, output_path))

                if is_table:
                    storage_key = f"table_{field['name']}"
                else:
                    storage_key = field['name']
                page_data_storage[storage_key] = output_path
                successful_extractions += 1
                break

            except Exception as e:
                print(f"Error processing field '{field['name']}' on page {page_num+1}, attempt {attempt+1}: {e}")
                if attempt == 2:
                    print(f"Failed to process field '{field['name']}' on page {page_num+1} after 3 attempts.")

    if use_pdfium:
        pdfium_page.close()
        pdfium_pdf.close()
    doc.close()
    return page_data_storage, successful_extractions
