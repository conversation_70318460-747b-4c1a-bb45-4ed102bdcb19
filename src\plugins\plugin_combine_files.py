import os
import pandas as pd
from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import logging

def plugin_combine_files(input_dir, save_file):
    """
    Plugin to combine PDF files from a directory into a single PDF.

    Args:
        input_dir (str): Directory containing PDF files to merge
        save_file (str): Path where the merged PDF will be saved

    Returns:
        dict: Dictionary containing information about the operation
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    # Validate inputs
    if not os.path.isdir(input_dir):
        logger.error(f"Input directory does not exist: {input_dir}")
        return {"error": f"Input directory does not exist: {input_dir}"}

    # Get all PDF files in the directory
    pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]

    if not pdf_files:
        logger.warning(f"No PDF files found in {input_dir}")
        return {"warning": f"No PDF files found in {input_dir}"}

    # Sort files to ensure consistent ordering
    pdf_files.sort()

    # Create PDF writer for the merged file
    pdf_writer = PdfWriter()

    # Track filename to page mapping (1-indexed)
    page_mapping = []
    current_page = 1

    try:
        # Process each PDF file
        for pdf_file in pdf_files:
            file_path = os.path.join(input_dir, pdf_file)
            logger.info(f"Processing: {pdf_file}")

            # Read the PDF
            try:
                pdf_reader = PdfReader(file_path)
                num_pages = len(pdf_reader.pages)

                # Add pages to the merged PDF
                for page_num in range(num_pages):
                    pdf_writer.add_page(pdf_reader.pages[page_num])

                # Record the page mapping
                page_mapping.append({
                    "original_filename": pdf_file,
                    "start_page": current_page,
                    "end_page": current_page + num_pages - 1,
                    "num_pages": num_pages
                })

                # Update the current page counter
                current_page += num_pages

            except Exception as e:
                logger.error(f"Error processing {pdf_file}: {str(e)}")
                continue

        # Save the merged PDF
        os.makedirs(os.path.dirname(os.path.abspath(save_file)), exist_ok=True)
        with open(save_file, "wb") as output_file:
            pdf_writer.write(output_file)
        logger.info(f"Merged PDF saved to: {save_file}")

        # Create and save the mapping file
        mapping_df = pd.DataFrame(page_mapping)

        # Generate mapping filename based on save_file
        base_name, ext = os.path.splitext(save_file)
        mapping_file = f"{base_name}_mapping.xlsx"

        # Save mapping to Excel
        mapping_df.to_excel(mapping_file, index=False)
        logger.info(f"Page mapping saved to: {mapping_file}")

        return {
            "success": True,
            "merged_file": save_file,
            "mapping_file": mapping_file,
            "total_files": len(pdf_files),
            "total_pages": current_page - 1,
            "page_mapping": page_mapping
        }

    except Exception as e:
        error_msg = f"Error combining PDF files: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}