import fitz
import os
import time
from multiprocessing import Pool
from functools import partial
import cv2
import numpy as np

# Code Explanation
'''
Purpose:
    Convert PDF pages to high-resolution images efficiently using multiprocessing.
    This module provides a streamlined way to batch convert PDF pages to PNG images
    with configurable DPI settings.

Key Features:
    - Parallel processing for improved performance
    - Configurable DPI for high-resolution output
    - Selective page processing
    - Option to export images or return pixmaps directly
    - Progress tracking and performance metrics

Process Flow:
    1. Input Validation:
       - Verify PDF path and accessibility
       - Create output directory if needed
       - Validate page numbers if specified
    
    2. Page Processing (Parallel):
       - Open PDF document for each page
       - Calculate zoom matrix based on desired DPI
       - Convert page to pixmap using PyMuPDF (fitz)
       - Optionally save as PNG or return pixmap
    
    3. Results Collection:
       - Gather results from all processes
       - Track successful/failed conversions
       - Generate performance metrics

Performance Considerations:
    - Uses multiprocessing for parallel page processing
    - Higher DPI settings will increase processing time and memory usage
    - Memory usage scales with page size and DPI

Usage Examples:
    # Convert entire PDF at 500 DPI
    results = convert_pdf_to_images(
        pdf_path="document.pdf",
        dpi=500
    )

    # Convert specific pages at 1000 DPI
    results = convert_pdf_to_images(
        pdf_path="document.pdf",
        page_list=[1, 3, 5],
        dpi=1000
    )

    # Get pixmaps without saving to disk
    pixmaps = convert_pdf_to_images(
        pdf_path="document.pdf",
        export_images=False
    )

Returns:
    dict: Dictionary mapping page numbers to either:
          - File paths (when export_images=True)
          - Pixmap objects (when export_images=False)
'''

def process_page(page_num, pdf_path, output_path, dpi, export_images=True):
    """
    Convert a single PDF page to a high-resolution image.
    
    Args:
        page_num (int): Page number to process (0-based index)
        pdf_path (str): Path to the PDF file
        output_path (str): Directory to save output images
        dpi (int): Resolution for image conversion
        export_images (bool): Whether to save images to disk
    
    Returns:
        tuple: (page_number, image_path or None)
    """
    try:
        doc = fitz.open(pdf_path)
        page = doc[page_num]
        
        # Calculate zoom factor
        zoom = dpi / 72  # standard PDF resolution is 72 DPI
        matrix = fitz.Matrix(zoom, zoom)
        
        # Get page pixmap
        pix = page.get_pixmap(matrix=matrix)
        
        if export_images:
            # Create output filename
            output_file = os.path.join(output_path, f"page_{page_num + 1}.png")
            pix.save(output_file)
            result = output_file
        else:
            result = pix
            
        doc.close()
        return page_num + 1, result
        
    except Exception as e:
        print(f"Error processing page {page_num + 1}: {e}")
        return page_num + 1, None


def convert_pdf_to_images(
    pdf_path, 
    output_path='output_images', 
    dpi=500, 
    page_list=None,
    export_images=True
):
    """
    Convert PDF pages to high-resolution images using multiprocessing.
    
    Args:
        pdf_path (str): Path to the PDF file
        output_path (str): Directory to save output images
        dpi (int): Resolution for image conversion (default: 500)
        page_list (list): List of specific pages to process (1-based indexing)
        export_images (bool): Whether to save images to disk (default: True)
    
    Returns:
        dict: Mapping of page numbers to their corresponding image paths or pixmaps
    """
    print(f"Processing PDF at {dpi} DPI")
    
    # Create output directory if exporting images
    if export_images and not os.path.exists(output_path):
        os.makedirs(output_path)
    
    # Determine pages to process
    with fitz.open(pdf_path) as doc:
        if page_list:
            pages_to_process = [p - 1 for p in page_list if 0 < p <= doc.page_count]
            total_pages = len(pages_to_process)
        else:
            total_pages = doc.page_count
            pages_to_process = range(total_pages)
    
    start_time = time.time()
    
    # Prepare partial function for multiprocessing
    process_page_partial = partial(
        process_page,
        pdf_path=pdf_path,
        output_path=output_path,
        dpi=dpi,
        export_images=export_images
    )
    
    # Process pages using multiprocessing
    with Pool() as pool:
        results = pool.map(process_page_partial, pages_to_process)
    
    # Organize results
    processed_pages = {page_num: result for page_num, result in results if result is not None}
    
    # Calculate and print statistics
    end_time = time.time()
    total_time = end_time - start_time
    pages_per_second = total_pages / total_time if total_time > 0 else 0
    
    print(f"\nProcessed {total_pages} pages in {total_time:.2f} seconds")
    print(f"Processing speed: {pages_per_second:.2f} pages per second")
    print(f"Successfully processed pages: {len(processed_pages)}")
    
    if len(processed_pages) != total_pages:
        print(f"WARNING: {total_pages - len(processed_pages)} pages failed to process")
    
    return processed_pages

if __name__ == "__main__":
    # Example usage
    pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 039 - Tesla\Binder-6.pdf"
    results = convert_pdf_to_images(
        pdf_path=pdf_path,
        output_path=r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\images",
        dpi=300,
        page_list=[1, 47, 53],  # Optional: specify pages to process
        export_images=True     # Optional: set to False to return pixmaps instead of saving
    )
