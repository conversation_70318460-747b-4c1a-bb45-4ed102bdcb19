"""
Test file for elevation calculations using existing app logic.

This test reads elevation data from an Excel workbook and calculates
min, max, and average elevations using the existing elevation calculation
functions from the main codebase.

Usage: Run from IDE using "Python: Current File" configuration.
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# Add the project root to the path so we can import from src
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the existing elevation calculation functions
try:
    from src.atom.roiextraction import parse_elevation, calculate_elevation_metrics
    print("Successfully imported elevation functions from roiextraction.py")
except ImportError as e:
    print(f"Failed to import from roiextraction.py: {e}")
    try:
        from src.atom._worker import parse_elevation, calculate_elevation_metrics
        print("Successfully imported elevation functions from _worker.py")
    except ImportError as e2:
        print(f"Failed to import from _worker.py: {e2}")
        print("Cannot proceed without elevation calculation functions")
        sys.exit(1)


def test_individual_elevation_parsing(elevation_string, pdf_id=0):
    """
    Test parsing of individual elevation strings to see which ones fail.

    Args:
        elevation_string (str): Single elevation string to test
        pdf_id (int): PDF ID for logging purposes

    Returns:
        tuple: (parsed_value, success_flag, error_message)
    """
    try:
        result = parse_elevation(elevation_string.strip(), pdf_id)
        if pd.isna(result) or np.isnan(result):
            return result, False, "Returned NaN"
        return result, True, None
    except Exception as e:
        return float('nan'), False, str(e)


def test_elevation_calculations_from_workbook(input_file, output_file=None):
    """
    Test elevation calculations using data from an Excel workbook.

    Args:
        input_file (str): Path to input Excel file with elevation data
        output_file (str, optional): Path to save output file. If None, creates one based on input name.
    """
    print(f"Testing elevation calculations from: {input_file}")

    # Read the workbook
    try:
        df = pd.read_excel(input_file)
        print(f"Successfully loaded workbook with {len(df)} rows")
    except Exception as e:
        print(f"Error reading workbook: {e}")
        return

    # Check if required columns exist
    required_columns = ['elevation']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Missing required columns: {missing_columns}")
        print(f"Available columns: {list(df.columns)}")
        return

    # Ensure the output columns exist
    output_columns = ['min_elevation', 'max_elevation', 'avg_elevation']
    for col in output_columns:
        if col not in df.columns:
            df[col] = ''

    # Add debugging columns
    df['elevation_debug'] = ''
    df['elevation_parsed_values'] = ''
    df['elevation_errors'] = ''

    print("\nProcessing elevation data...")

    # Process each row
    for index, row in df.iterrows():
        elevation_data = row['elevation']

        if pd.isna(elevation_data) or elevation_data == '':
            print(f"Row {index}: No elevation data")
            continue

        print(f"\nRow {index}: Processing '{elevation_data}'")

        # Test individual elevation strings
        elevation_strings = str(elevation_data).split(';')
        parsed_values = []
        errors = []
        debug_info = []

        for i, elev_str in enumerate(elevation_strings):
            elev_str = elev_str.strip()
            if elev_str:
                parsed_val, success, error = test_individual_elevation_parsing(elev_str, pdf_id=index)
                parsed_values.append(parsed_val)

                if success:
                    debug_info.append(f"'{elev_str}' -> {parsed_val:.4f}")
                    print(f"  ✓ '{elev_str}' -> {parsed_val:.4f}")
                else:
                    debug_info.append(f"'{elev_str}' -> FAILED ({error})")
                    errors.append(f"'{elev_str}': {error}")
                    print(f"  ✗ '{elev_str}' -> FAILED ({error})")

        # Store debug information
        df.at[index, 'elevation_debug'] = '; '.join(debug_info)
        df.at[index, 'elevation_parsed_values'] = '; '.join([str(v) for v in parsed_values if not pd.isna(v)])
        df.at[index, 'elevation_errors'] = '; '.join(errors)

        # Use the existing calculate_elevation_metrics function
        try:
            min_elev, max_elev, avg_elev = calculate_elevation_metrics(row, pdf_id=index)
            df.at[index, 'min_elevation'] = min_elev
            df.at[index, 'max_elevation'] = max_elev
            df.at[index, 'avg_elevation'] = avg_elev

            print(f"  Results: Min={min_elev}, Max={max_elev}, Avg={avg_elev}")

        except Exception as e:
            error_msg = f"Error in calculate_elevation_metrics: {e}"
            print(f"  ✗ {error_msg}")
            df.at[index, 'elevation_errors'] = df.at[index, 'elevation_errors'] + f"; {error_msg}"

    # Generate output filename if not provided
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_elevation_test_results.xlsx"

    # Save results
    try:
        df.to_excel(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
    except Exception as e:
        print(f"Error saving results: {e}")

    # Print summary
    total_rows = len(df[df['elevation'].notna() & (df['elevation'] != '')])
    successful_rows = len(df[(df['min_elevation'] != '') & (df['min_elevation'] != '0')])
    failed_rows = total_rows - successful_rows

    print(f"\n=== SUMMARY ===")
    print(f"Total rows with elevation data: {total_rows}")
    print(f"Successfully processed: {successful_rows}")
    print(f"Failed to process: {failed_rows}")

    if failed_rows > 0:
        print(f"\nRows with errors:")
        error_rows = df[df['elevation_errors'] != '']
        for index, row in error_rows.iterrows():
            print(f"  Row {index}: {row['elevation']} -> {row['elevation_errors']}")


def create_test_data():
    """
    Create a sample test workbook with various elevation formats for testing.
    """
    test_data = {
        'elevation': [
            'EL +241\'5.3/16"; EL +263\'2"',  # Your example
            'EL +100\'6"',                    # Simple feet-inches
            'EL +28\'',                       # Feet only
            'EL +43.5',                       # Decimal format
            'EL +65\'-3 3/8"',               # Feet-inches-fraction with hyphen
            'EL +184\'4.3/16"',              # Feet inches.fraction/denominator
            'EL +72\' 2.7/16"',              # Feet space inches.fraction/denominator
            'Z +150',                         # Z prefix
            'EL +102.5; EL +98.2',           # Multiple decimal elevations
            'EL +50\'6"; EL +52\'8"; EL +48\'4"',  # Multiple feet-inches
            '',                               # Empty
            'INVALID ELEVATION',              # Invalid format
            'EL.125\'11 3/16"'
        ],
        'min_elevation': [''] * 13,
        'max_elevation': [''] * 13,
        'avg_elevation': [''] * 13,
    }

    df = pd.DataFrame(test_data)
    test_file = r"debug/test_elevation_data.xlsx"
    df.to_excel(test_file, index=False)
    print(f"Created test data file: {test_file}")
    return test_file


if __name__ == "__main__":
    print("=== Elevation Calculation Test ===")


    # # Option 1: Create and test with sample data
    # print("\n1. Creating test data...")
    test_file = create_test_data()

    # print("\n2. Testing with sample data...")
    test_elevation_calculations_from_workbook(test_file)

    # Option 2: Test with your own file (uncomment and modify path as needed)
    print("\n3. Testing with custom file...")
    custom_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 041 - Novelis\Data\exported_general_data_nofieldmap.xlsx"
    if os.path.exists(custom_file):
        test_elevation_calculations_from_workbook(custom_file)
    else:
        print(f"Custom file not found: {custom_file}")

    print("\n=== Test Complete ===")
