#!/usr/bin/env python3
"""
Test script for intelligent cross-category conflict resolution.
Tests the enhanced logic that considers context clues when resolving conflicts.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from normalize_description import normalize_description

def test_schedule_vs_rating_conflict():
    """Test the specific case where S-160 should be recognized as schedule, not rating"""
    
    # Test case from the user's example
    test_description = ".75 X S-160 - NIPPLE, B36.10M, TBE, CS, ASTM A106-B, SMLS, 3\" LG"
    
    print("Testing intelligent conflict resolution...")
    print(f"Input: {test_description}")
    print()
    
    # Run normalization
    normalized, metadata_tags, review_tags, match_types = normalize_description(test_description)
    
    print(f"Normalized: {normalized}")
    print(f"Metadata tags: {metadata_tags}")
    print(f"Review tags: {review_tags}")
    print(f"Match types: {match_types}")
    print()
    
    # Check results
    has_schedule_160 = any('schedule:160' in tag for tag in metadata_tags)
    has_rating_160 = any('rating:160' in tag for tag in metadata_tags)
    
    print("Analysis:")
    print(f"- Contains schedule:160: {has_schedule_160}")
    print(f"- Contains rating:160: {has_rating_160}")
    
    if has_schedule_160 and not has_rating_160:
        print("✅ SUCCESS: Correctly identified S-160 as schedule, not rating")
        return True
    elif has_rating_160 and not has_schedule_160:
        print("❌ FAILURE: Incorrectly identified as rating instead of schedule")
        return False
    elif has_schedule_160 and has_rating_160:
        print("⚠️  WARNING: Both schedule and rating detected - check conflict resolution")
        return False
    else:
        print("❌ FAILURE: Neither schedule nor rating detected")
        return False

def test_contextual_exclusions():
    """Test cases where values should be excluded based on context"""

    exclusion_test_cases = [
        # Document number cases - should NOT extract schedules
        ("A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000",
         "schedule", "Should not extract STD x 3000 from document number"),

        # Pipe dimension cases - should NOT extract schedules
        (".75 - 45 ELL, B16.11, CL 3000, FTE, CS, ASTM A105",
         "schedule", "Should not extract 75 x 45 from pipe dimensions"),

        # Part number cases - should NOT extract schedules
        ("\"4\" HIGH WELDED PIPE SHOE: G4G-1410-07; TYPE 'A'",
         "schedule", "Should not extract 1410 x 07 from part number"),

        # S-160 case - should NOT extract rating when S- prefix present
        ("1 X .5 X S-160 X S-160 - CON. SWAGE, MSS SP-95, TBE, CS, ASTM A234-WPB-S",
         "rating", "Should not extract rating:160 when S-160 prefix present"),

        # Grade fragment cases - should NOT extract invalid grades
        ("GASKET, FLAT, 150LB, 5MM THK, 316 SS GROOVED FLAT PROFILE METAL CORE & LOOSE LOCATING RING W/GRAPHITE FACING",
         "grade", "Should not extract OOVED, ATING from GROOVED, GRATING"),

        ("GRATING PENETRATION COLLARS, G2G-5530-05A",
         "grade", "Should not extract ATING from GRATING"),

        # ASTM false positive cases - should NOT extract A36 from A3
        ("A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000",
         "astm", "Should not extract A36 from A3 in equipment description"),
    ]

    print("\nTesting contextual exclusions...")

    all_passed = True
    for description, should_not_extract, reason in exclusion_test_cases:
        print(f"\nTest: {description[:60]}...")
        print(f"Should NOT extract: {should_not_extract} ({reason})")

        normalized, metadata_tags, review_tags, match_types = normalize_description(description)

        # Check if the problematic extraction occurred
        problematic_found = False

        if should_not_extract == "schedule":
            # Look for invalid schedule extractions
            invalid_schedules = []
            for tag in metadata_tags:
                if tag.startswith('schedule:'):
                    schedule_value = tag.split(':', 1)[1]
                    # Check for patterns that shouldn't be schedules
                    if ('x' in schedule_value.lower() and
                        any(char.isdigit() and int(char) > 200 for char in schedule_value if char.isdigit())):
                        invalid_schedules.append(schedule_value)
                        problematic_found = True

            if problematic_found:
                print(f"❌ FAIL: Found invalid schedule extractions: {invalid_schedules}")
                all_passed = False
            else:
                print(f"✅ PASS: No invalid schedule extractions found")

        elif should_not_extract == "rating":
            # Look for invalid rating extractions (specifically 160 when S-160 present)
            invalid_ratings = []
            for tag in metadata_tags:
                if tag.startswith('rating:160') and 'S-160' in description.upper():
                    invalid_ratings.append(tag)
                    problematic_found = True

            if problematic_found:
                print(f"❌ FAIL: Found invalid rating extractions: {invalid_ratings}")
                all_passed = False
            else:
                print(f"✅ PASS: No invalid rating extractions found")

        elif should_not_extract == "grade":
            # Look for invalid grade extractions (word fragments)
            invalid_grades = []
            for tag in metadata_tags:
                if tag.startswith('grade:'):
                    grade_value = tag.split(':', 1)[1]
                    # Check for problematic grade fragments
                    if grade_value.upper() in ['OOVED', 'ATING', 'OVED', 'TING', 'OUND', 'ATED']:
                        invalid_grades.append(grade_value)
                        problematic_found = True

            if problematic_found:
                print(f"❌ FAIL: Found invalid grade extractions: {invalid_grades}")
                all_passed = False
            else:
                print(f"✅ PASS: No invalid grade extractions found")

        elif should_not_extract == "astm":
            # Look for invalid ASTM extractions (like A36 from A3)
            invalid_astm = []
            for tag in metadata_tags:
                if tag.startswith('astm:'):
                    astm_value = tag.split(':', 1)[1]
                    # Check if A36 was extracted when only A3 is in description
                    if astm_value.upper() == 'A36' and 'A36' not in description.upper() and 'A3 ' in description.upper():
                        invalid_astm.append(astm_value)
                        problematic_found = True

            if problematic_found:
                print(f"❌ FAIL: Found invalid ASTM extractions: {invalid_astm}")
                all_passed = False
            else:
                print(f"✅ PASS: No invalid ASTM extractions found")

        print(f"   Metadata: {metadata_tags}")
        if review_tags:
            print(f"   Review: {review_tags}")

    return all_passed

def test_clear_rating_indicators():
    """Test cases where rating indicators are clear"""

    test_cases = [
        ("VALVE, CLASS 150, RF, CS", "rating", "CLASS prefix should indicate rating"),
        ("FLANGE, 300#, RF, CS", "rating", "# suffix should indicate rating"),
        ("FITTING, #600, SW, CS", "rating", "# prefix should indicate rating"),
        ("PIPE, SCH 40, CS", "schedule", "SCH prefix should indicate schedule"),
        ("REDUCER, S-80, CS", "schedule", "S- prefix should indicate schedule"),
    ]
    
    print("\nTesting clear indicator cases...")
    
    all_passed = True
    for description, expected_category, reason in test_cases:
        print(f"\nTest: {description}")
        print(f"Expected: {expected_category} ({reason})")
        
        normalized, metadata_tags, review_tags, match_types = normalize_description(description)
        
        # Extract the numeric value to check
        import re
        numbers = re.findall(r'\d+', description)
        if numbers:
            target_number = numbers[0]
            
            has_expected = any(f'{expected_category}:{target_number}' in tag for tag in metadata_tags)
            other_category = 'rating' if expected_category == 'schedule' else 'schedule'
            has_other = any(f'{other_category}:{target_number}' in tag for tag in metadata_tags)
            
            if has_expected and not has_other:
                print(f"✅ PASS: Correctly identified as {expected_category}")
            elif has_other and not has_expected:
                print(f"❌ FAIL: Incorrectly identified as {other_category}")
                all_passed = False
            elif has_expected and has_other:
                print(f"⚠️  PARTIAL: Both categories detected")
                all_passed = False
            else:
                print(f"❌ FAIL: Neither category detected")
                all_passed = False
                
            print(f"   Metadata: {metadata_tags}")
            if review_tags:
                print(f"   Review: {review_tags}")
    
    return all_passed

def main():
    """Run all tests"""
    print("=" * 60)
    print("INTELLIGENT CONFLICT RESOLUTION TESTS")
    print("=" * 60)

    # Test the specific S-160 case
    test1_passed = test_schedule_vs_rating_conflict()

    # Test contextual exclusions
    test2_passed = test_contextual_exclusions()

    # Test clear indicator cases
    test3_passed = test_clear_rating_indicators()

    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)

    if test1_passed and test2_passed and test3_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The intelligent conflict resolution and contextual exclusions are working correctly.")
    else:
        print("❌ SOME TESTS FAILED")
        print("The logic may need further refinement.")
        print(f"S-160 test: {'✅' if test1_passed else '❌'}")
        print(f"Contextual exclusions: {'✅' if test2_passed else '❌'}")
        print(f"Clear indicators: {'✅' if test3_passed else '❌'}")

    return test1_passed and test2_passed and test3_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
