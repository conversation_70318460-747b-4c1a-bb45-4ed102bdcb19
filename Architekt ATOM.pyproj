<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>32842663-8b09-4d4a-afec-1eb2394217f7</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>app.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>Architekt ATOM</Name>
    <RootNamespace>Architekt ATOM</RootNamespace>
    <InterpreterId>MSBuild|vir_env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="app.py" />
    <Compile Include="build_atem.py" />
    <Compile Include="classify_iso_components.py" />
    <Compile Include="classify_iso_components_v3.py" />
    <Compile Include="classify_iso_merge_V2_V3.py" />
    <Compile Include="classify_iso_merge_V2_V3_b.py" />
    <Compile Include="classify_iso_vectorized.py" />
    <Compile Include="classify_iso_vectorized_v2.py" />
    <Compile Include="combine_files.py" />
    <Compile Include="convert_to_text.py" />
    <Compile Include="createDB.py" />
    <Compile Include="data_conversions.py" />
    <Compile Include="dbTest.py" />
    <Compile Include="detect_welds_text.py" />
    <Compile Include="edit_fieldmap.py" />
    <Compile Include="group_pdfs.py" />
    <Compile Include="jsonFunctions.py" />
    <Compile Include="misc\get_annot_objects.py" />
    <Compile Include="misc\ocrpages.py" />
    <Compile Include="misc\pymutesseract2.py" />
    <Compile Include="misc_help.py" />
    <Compile Include="modify_db.py" />
    <Compile Include="pymupdf_find_table.py" />
    <Compile Include="relate_drawing_objects.py" />
    <Compile Include="src\app_paths.py" />
    <Compile Include="src\atom\ai_classifier\api_request_parallel_processor.py" />
    <Compile Include="src\atom\ai_classifier\classify_main.py" />
    <Compile Include="src\atom\ai_classifier\classify_main_bu.py" />
    <Compile Include="src\atom\ai_classifier\cls_general.py" />
    <Compile Include="src\atom\ai_classifier\csv_to_array.py" />
    <Compile Include="src\atom\ai_classifier\generate_requests.py" />
    <Compile Include="src\atom\ai_classifier\OpenAI_Parallel_Processing.py" />
    <Compile Include="src\atom\ai_classifier\prompts.py" />
    <Compile Include="src\atom\ai_classifier\save_generated_data_to_csv.py" />
    <Compile Include="src\atom\ai_classifier\__init__.py" />
    <Compile Include="src\atom\ai_processing.py" />
    <Compile Include="src\atom\combine_files.py" />
    <Compile Include="src\atom\convert_excel_fields.py" />
    <Compile Include="src\atom\database\schemas\lookup_tables\create_lookup_tables.py" />
    <Compile Include="src\atom\dbManager.py" />
    <Compile Include="src\atom\dynamic_rename.py" />
    <Compile Include="src\atom\ef_lookup.py" />
    <Compile Include="src\atom\extract_symbols\detect_sw_types.py" />
    <Compile Include="src\atom\extract_symbols\matplotlib_draw_vector.py" />
    <Compile Include="src\atom\extract_symbols\vector_classify.py" />
    <Compile Include="src\atom\extract_symbols\__init__.py" />
    <Compile Include="src\atom\extract_tables.py" />
    <Compile Include="src\atom\extract_tables_v2.py" />
    <Compile Include="src\atom\fast_storage.py" />
    <Compile Include="src\atom\grouppdfs\detect_lines.py" />
    <Compile Include="src\atom\grouppdfs\grouppdfs.py" />
    <Compile Include="src\atom\grouppdfs\__init__.py" />
    <Compile Include="src\atom\group_pdfs.py" />
    <Compile Include="src\atom\multi_roi_funcs.py" />
    <Compile Include="src\atom\merge_rfq_into_bom.py" />
    <Compile Include="src\atom\not_used.py" />
    <Compile Include="src\atom\post_data_process.py" />
    <Compile Include="src\atom\roiextraction.py" />
    <Compile Include="src\atom\sourcepreprocess.py" />
    <Compile Include="src\atom\data_tables\table_extraction.py" />
    <Compile Include="src\atom\data_tables\table_processing.py" />
    <Compile Include="src\atom\data_tables\table_structure.py" />
    <Compile Include="src\atom\data_tables\table_utils.py" />
    <Compile Include="src\atom\tables\bomresultsview.py" />
    <Compile Include="src\atom\tables\generaldataview.py" />
    <Compile Include="src\atom\tables\lookuptable.py" />
    <Compile Include="src\atom\tables\outlierresultsview.py" />
    <Compile Include="src\atom\tables\rfqtableview.py" />
    <Compile Include="src\atom\tables\specresultsview.py" />
    <Compile Include="src\atom\tables\spoolresultsview.py" />
    <Compile Include="src\atom\tables\__init__.py" />
    <Compile Include="src\atom\vision\convert_to_pdf_aws.py" />
    <Compile Include="src\atom\vision\detect_text_regions\detect_text_regions_cv2.py" />
    <Compile Include="src\atom\vision\detect_text_regions\detect_text_regions_grouping.py" />
    <Compile Include="src\atom\vision\detect_text_regions\flag_text_regions.py" />
    <Compile Include="src\atom\vision\detect_text_regions\pdf_to_image.py" />
    <Compile Include="src\atom\vision\detect_text_regions\__init__.py" />
    <Compile Include="src\atom\vision\ocr_patcher.py" />
    <Compile Include="src\atom\vision\ocr_patch_multi.py" />
    <Compile Include="src\atom\vision\ocr_patch_raw.py" />
    <Compile Include="src\atom\vision\__init__.py" />
    <Compile Include="src\atom\worker_v2.py" />
    <Compile Include="src\atom\_worker.py" />
    <Compile Include="src\atom\__init__.py" />
    <Compile Include="src\config\atem_cloud.py" />
    <Compile Include="src\config\temp_api_keys.py" />
    <Compile Include="src\config\temp_api_keys_cm.py" />
    <Compile Include="src\config\workspace.py" />
    <Compile Include="src\config\workspace_old.py" />
    <Compile Include="src\config\__init__.py" />
    <Compile Include="src\data\modify_fieldmap.py" />
    <Compile Include="src\data\roi_table_scopes.py" />
    <Compile Include="src\data\scripts\generatefieldmapjson.py" />
    <Compile Include="src\data\tables\alwaysvisibletablefields.py" />
    <Compile Include="src\data\tables\defaultcolumnorder.py" />
    <Compile Include="src\data\tables\hiddentablefields.py" />
    <Compile Include="src\data\__init__.py" />
    <Compile Include="src\mainwindow.py" />
    <Compile Include="src\pyside_util.py" />
    <Compile Include="src\splashscreen.py" />
    <Compile Include="src\tests\database\databasetest.py" />
    <Compile Include="src\tests\fractiontest.py" />
    <Compile Include="src\tests\gui\basicpdfviewer.py" />
    <Compile Include="src\tests\gui\editroidialogtest.py" />
    <Compile Include="src\tests\gui\groupedtableviewtest.py" />
    <Compile Include="src\tests\gui\itemrolescomboboxtest.py" />
    <Compile Include="src\tests\gui\ondemandtable.py" />
    <Compile Include="src\tests\size_conversions.py" />
    <Compile Include="src\tests\uipreview.py" />
    <Compile Include="src\theme.py" />
    <Compile Include="src\util.py" />
    <Compile Include="src\utils\convert_roi_payload.py" />
    <Compile Include="src\utils\export.py" />
    <Compile Include="src\utils\logger.py" />
    <Compile Include="src\utils\visualize_welds.py" />
    <Compile Include="src\utils\__init__.py" />
    <Compile Include="src\views\blueprintreaderview.py" />
    <Compile Include="src\views\contactdetails.py" />
    <Compile Include="src\views\dialogs\columnorganizer.py" />
    <Compile Include="src\views\dialogs\reportissue.py" />
    <Compile Include="src\views\dialogs\roiextractionpreview.py" />
    <Compile Include="src\views\dialogs\tableexporter.py" />
    <Compile Include="src\views\dialogs\tableimporter.py" />
    <Compile Include="src\views\dialogs\__init__.py" />
    <Compile Include="src\views\documentsview.py" />
    <Compile Include="src\views\documentviewonly.py" />
    <Compile Include="src\views\formsview.py" />
    <Compile Include="src\views\forms\activateproductkeyform.py" />
    <Compile Include="src\views\forms\addsourceexistingproject.py" />
    <Compile Include="src\views\forms\authenticateform.py" />
    <Compile Include="src\views\forms\baseform.py" />
    <Compile Include="src\views\forms\companyprofileform.py" />
    <Compile Include="src\views\forms\createaccountform.py" />
    <Compile Include="src\views\forms\loginform.py" />
    <Compile Include="src\views\forms\newprojectexplorerform.py" />
    <Compile Include="src\views\forms\newprojectsetupform.py" />
    <Compile Include="src\views\forms\tokencheckoutform.py" />
    <Compile Include="src\views\forms\tokencheckoutstatusform.py" />
    <Compile Include="src\views\forms\tokencheckoutwindow.py" />
    <Compile Include="src\views\forms\tokenpaymentform.py" />
    <Compile Include="src\views\forms\useragreementform.py" />
    <Compile Include="src\views\forms\__init__.py" />
    <Compile Include="src\views\loadingview.py" />
    <Compile Include="src\views\navigationsidebar.py" />
    <Compile Include="src\views\placeholderview.py" />
    <Compile Include="src\views\popups\queuestatuspopup.py" />
    <Compile Include="src\views\popups\tokenstatuspopup.py" />
    <Compile Include="src\views\popups\userprofilepopup.py" />
    <Compile Include="src\views\popups\__init__.py" />
    <Compile Include="src\views\projectview.py" />
    <Compile Include="src\views\supportticket.py" />
    <Compile Include="src\views\tabbuttons.py" />
    <Compile Include="src\views\tableresultsview.py" />
    <Compile Include="src\views\tables\alwaysvisibletablefields.py" />
    <Compile Include="src\views\tables\bomresultsview.py" />
    <Compile Include="src\views\tables\generaldataview.py" />
    <Compile Include="src\views\tables\hiddentablefields.py" />
    <Compile Include="src\views\tables\lookuptable.py" />
    <Compile Include="src\views\tables\outlierresultsview.py" />
    <Compile Include="src\views\tables\rfqtableview.py" />
    <Compile Include="src\views\tables\specresultsview.py" />
    <Compile Include="src\views\tables\spoolresultsview.py" />
    <Compile Include="src\views\tables\__init__.py" />
    <Compile Include="src\views\uploadqueueview.py" />
    <Compile Include="src\views\workspaceview.py" />
    <Compile Include="src\views\__init__.py" />
    <Compile Include="src\widgets\groupedtableview.py" />
    <Compile Include="src\widgets\jobqueuecard.py" />
    <Compile Include="src\widgets\statusbar\blueprintreaderstatuswidget.py" />
    <Compile Include="src\widgets\statusbar\documentviewerstatuswidget.py" />
    <Compile Include="src\widgets\statusbar\jobstatuswidget.py" />
    <Compile Include="src\widgets\statusbar\realtimestatuswidget.py" />
    <Compile Include="src\widgets\statusbar\tablestatuswidget.py" />
    <Compile Include="src\widgets\statusbar\__init__.py" />
    <Compile Include="unit_tests\audit_bom_pos.py" />
    <Compile Include="unit_tests\clean_description.py" />
    <Compile Include="unit_tests\create_exact_copy.py" />
    <Compile Include="unit_tests\create_group_per_page.py" />
    <Compile Include="unit_tests\data_integrity.py" />
    <Compile Include="unit_tests\detect_duplicate_pages_mp.py" />
    <Compile Include="unit_tests\find_duplicate_pages.py" />
    <Compile Include="unit_tests\get_tables_test.py" />
    <Compile Include="unit_tests\get_text_test.py" />
    <Compile Include="unit_tests\handle_bom_section_labels.py" />
    <Compile Include="unit_tests\normalize_description.py" />
    <Compile Include="unit_tests\prompts.py" />
    <Compile Include="unit_tests\quantity_map.py" />
    <Compile Include="unit_tests\validate_main.py" />
    <Compile Include="unit_tests\validate_quantities.py" />
    <Compile Include="value_mappings.py" />
    <Compile Include="__features__.py" />
    <Compile Include="__version__.py" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="vir_env\">
      <Id>vir_env</Id>
      <Version>3.9</Version>
      <Description>vir_env (Python 3.9 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="auth\" />
    <Folder Include="config\" />
    <Folder Include="config\__pycache__\" />
    <Folder Include="docs\" />
    <Folder Include="docs\manuals\" />
    <Folder Include="docs\resources\" />
    <Folder Include="docs\resources\images\" />
    <Folder Include="misc\" />
    <Folder Include="src\atom\database\schemas\" />
    <Folder Include="src\atom\database\schemas\lookup_tables\" />
    <Folder Include="src\atom\grouppdfs\" />
    <Folder Include="src\atom\grouppdfs\__pycache__\" />
    <Folder Include="src\atom\data_tables\" />
    <Folder Include="src\atom\database\" />
    <Folder Include="src\atom\tables\" />
    <Folder Include="src\atom\tables\__pycache__\" />
    <Folder Include="src\atom\vision\" />
    <Folder Include="src\atom\vision\detect_text_regions\" />
    <Folder Include="src\atom\vision\detect_text_regions\__pycache__\" />
    <Folder Include="src\atom\vision\__pycache__\" />
    <Folder Include="src\utils\" />
    <Folder Include="src\utils\__pycache__\" />
    <Folder Include="src\views\dialogs\" />
    <Folder Include="src\views\dialogs\__pycache__\" />
    <Folder Include="unit_tests\" />
    <Folder Include="src\" />
    <Folder Include="src\atom\" />
    <Folder Include="src\atom\ai_classifier\" />
    <Folder Include="src\atom\ai_classifier\__pycache__\" />
    <Folder Include="src\atom\extract_symbols\" />
    <Folder Include="src\atom\extract_symbols\__pycache__\" />
    <Folder Include="src\atom\__pycache__\" />
    <Folder Include="src\config\" />
    <Folder Include="src\config\__pycache__\" />
    <Folder Include="src\data\" />
    <Folder Include="src\data\scripts\" />
    <Folder Include="src\data\scripts\generated\" />
    <Folder Include="src\data\tables\" />
    <Folder Include="src\data\tables\__pycache__\" />
    <Folder Include="src\data\themes\" />
    <Folder Include="src\data\__pycache__\" />
    <Folder Include="src\data_old\" />
    <Folder Include="src\resources\" />
    <Folder Include="src\resources\flags\" />
    <Folder Include="src\resources\flags\3x2\" />
    <Folder Include="src\resources\fonts\" />
    <Folder Include="src\tests\" />
    <Folder Include="src\tests\database\" />
    <Folder Include="src\tests\gui\" />
    <Folder Include="src\views\" />
    <Folder Include="src\views\forms\" />
    <Folder Include="src\views\forms\__pycache__\" />
    <Folder Include="src\views\popups\" />
    <Folder Include="src\views\popups\__pycache__\" />
    <Folder Include="src\views\tables\" />
    <Folder Include="src\views\tables\__pycache__\" />
    <Folder Include="src\views\__pycache__\" />
    <Folder Include="src\widgets\" />
    <Folder Include="src\widgets\statusbar\" />
    <Folder Include="src\widgets\statusbar\__pycache__\" />
    <Folder Include="src\widgets\__pycache__\" />
    <Folder Include="src\__pycache__\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="auth\atomproject-66dc6-firebase-adminsdk-aqxz5-6180c87339.json" />
    <Content Include="config\__pycache__\workspace.cpython-39.pyc" />
    <Content Include="config\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="docs\manuals\Firebase Login &amp; Token Tracking.docx" />
    <Content Include="docs\resources\developer_testing_template.md" />
    <Content Include="docs\resources\images\screen.png" />
    <Content Include="extract_tables_new_state.txt" />
    <Content Include="extract_tables_state_6.1.24.txt" />
    <Content Include="README.featurelocation.md" />
    <Content Include="README.md" />
    <Content Include="README.overview.md" />
    <Content Include="requirements.txt" />
    <Content Include="src\atom\ai_classifier\OpenAI_Parallel_Processing.pyproj" />
    <Content Include="src\atom\ai_classifier\OpenAI_Parallel_Processing.sln" />
    <Content Include="src\atom\ai_classifier\README.md" />
    <Content Include="src\atom\ai_classifier\__pycache__\api_request_parallel_processor.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\classify_main.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\cls_general.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\csv_to_array.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\generate_requests.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\prompts.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\save_generated_data_to_csv.cpython-39.pyc" />
    <Content Include="src\atom\ai_classifier\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\extract_symbols\__pycache__\detect_sw_types.cpython-39.pyc" />
    <Content Include="src\atom\extract_symbols\__pycache__\matplotlib_draw_vector.cpython-39.pyc" />
    <Content Include="src\atom\extract_symbols\__pycache__\vector_classify.cpython-39.pyc" />
    <Content Include="src\atom\extract_symbols\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\grouppdfs\__pycache__\detect_lines.cpython-39.pyc" />
    <Content Include="src\atom\grouppdfs\__pycache__\grouppdfs.cpython-39.pyc" />
    <Content Include="src\atom\grouppdfs\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\payload_options.json" />
    <Content Include="src\atom\tables\__pycache__\bomresultsview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\generaldataview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\hiddentablefields.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\lookuptable.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\outlierresultsview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\rfqtableview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\specresultsview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\spoolresultsview.cpython-39.pyc" />
    <Content Include="src\atom\tables\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\vision\detect_text_regions\__pycache__\detect_text_regions_cv2.cpython-39.pyc" />
    <Content Include="src\atom\vision\detect_text_regions\__pycache__\flag_text_regions.cpython-39.pyc" />
    <Content Include="src\atom\vision\detect_text_regions\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\vision\__pycache__\convert_to_pdf_aws.cpython-39.pyc" />
    <Content Include="src\atom\vision\__pycache__\ocr_patcher.cpython-39.pyc" />
    <Content Include="src\atom\vision\__pycache__\ocr_patch_multi.cpython-39.pyc" />
    <Content Include="src\atom\vision\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\ai_processing.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\convert_excel_fields.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\dbManager.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\dynamic_rename.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\ef_lookup.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\extract_tables.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\extract_tables_v2.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\fast_storage.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\group_pdfs.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\merge_rfq_into_bom.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\multi_roi_funcs.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\worker_v2.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\_worker.cpython-39.pyc" />
    <Content Include="src\atom\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\config\__pycache__\atem_cloud.cpython-39.pyc" />
    <Content Include="src\config\__pycache__\temp_api_keys.cpython-39.pyc" />
    <Content Include="src\config\__pycache__\workspace.cpython-39.pyc" />
    <Content Include="src\config\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\data\fieldmap - Copy.json" />
    <Content Include="src\data\fieldmap.json" />
    <Content Include="src\data\fieldmap_updated.json" />
    <Content Include="src\data\scripts\generated\fieldmaptemplate.json" />
    <Content Include="src\data\tables\ef_template - Copy.xlsx" />
    <Content Include="src\data\tables\ef_template.xlsx" />
    <Content Include="src\data\tables\__pycache__\alwaysvisibletablefields.cpython-39.pyc" />
    <Content Include="src\data\tables\__pycache__\hiddentablefields.cpython-39.pyc" />
    <Content Include="src\data\themes\backup-godot-theme.json" />
    <Content Include="src\data\themes\default-color-theme.json" />
    <Content Include="src\data\themes\stylesheet.qss" />
    <Content Include="src\data\useragreement.txt" />
    <Content Include="src\data\__pycache__\roi_table_scopes.cpython-39.pyc" />
    <Content Include="src\data\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\data_old\oci_coordinates.json" />
    <Content Include="src\data_old\output.xlsx" />
    <Content Include="src\data_old\output_gpt_1.csv" />
    <Content Include="src\data_old\sys_coordinates.json" />
    <Content Include="src\data_old\sys_coordinates1.json" />
    <Content Include="src\data_old\sys_coordinates2.json" />
    <Content Include="src\data_old\sys_coordinates3.json" />
    <Content Include="src\data_old\sys_coordinates4.json" />
    <Content Include="src\data_old\sys_coords.json" />
    <Content Include="src\ef_template.xlsx" />
    <Content Include="src\resources\alert-circle-orange.svg" />
    <Content Include="src\resources\alert-circle.svg" />
    <Content Include="src\resources\alert-triangle.svg" />
    <Content Include="src\resources\archive.svg" />
    <Content Include="src\resources\arrow-left-circle.svg" />
    <Content Include="src\resources\arrow-left.svg" />
    <Content Include="src\resources\BG-Burn Filter.png" />
    <Content Include="src\resources\blueprint.png" />
    <Content Include="src\resources\book.svg" />
    <Content Include="src\resources\build-rfq.svg" />
    <Content Include="src\resources\check-circle.svg" />
    <Content Include="src\resources\check.svg" />
    <Content Include="src\resources\chevron-down.svg" />
    <Content Include="src\resources\chevron-left.svg" />
    <Content Include="src\resources\chevron-right.svg" />
    <Content Include="src\resources\circle.svg" />
    <Content Include="src\resources\cloud-upload.svg" />
    <Content Include="src\resources\columns.svg" />
    <Content Include="src\resources\corner-left-up.svg" />
    <Content Include="src\resources\corner-up-left.svg" />
    <Content Include="src\resources\credit-card.svg" />
    <Content Include="src\resources\database.svg" />
    <Content Include="src\resources\download.svg" />
    <Content Include="src\resources\edit.svg" />
    <Content Include="src\resources\exit.png" />
    <Content Include="src\resources\external-link.svg" />
    <Content Include="src\resources\eye-off.svg" />
    <Content Include="src\resources\eye.svg" />
    <Content Include="src\resources\FFF_Architekt Integrated Systems_LOout.jpg" />
    <Content Include="src\resources\FFF_Architekt Integrated Systems_LOout.png" />
    <Content Include="src\resources\file-plus.svg" />
    <Content Include="src\resources\file-text.svg" />
    <Content Include="src\resources\file.svg" />
    <Content Include="src\resources\filter-clear-all.png" />
    <Content Include="src\resources\filter-minus-outline.png" />
    <Content Include="src\resources\filter-minus.png" />
    <Content Include="src\resources\filter-outline.png" />
    <Content Include="src\resources\filter-remove-outline.png" />
    <Content Include="src\resources\filter-remove-outline.svg" />
    <Content Include="src\resources\filter-remove.png" />
    <Content Include="src\resources\filter-sort-down.png" />
    <Content Include="src\resources\filter-sort-down.svg" />
    <Content Include="src\resources\filter-sort-up.png" />
    <Content Include="src\resources\filter-sort-up.svg" />
    <Content Include="src\resources\filter.png" />
    <Content Include="src\resources\filter.svg" />
    <Content Include="src\resources\flags\3x2\AC.svg" />
    <Content Include="src\resources\flags\3x2\AD.svg" />
    <Content Include="src\resources\flags\3x2\AE.svg" />
    <Content Include="src\resources\flags\3x2\AF.svg" />
    <Content Include="src\resources\flags\3x2\AG.svg" />
    <Content Include="src\resources\flags\3x2\AI.svg" />
    <Content Include="src\resources\flags\3x2\AL.svg" />
    <Content Include="src\resources\flags\3x2\AM.svg" />
    <Content Include="src\resources\flags\3x2\AO.svg" />
    <Content Include="src\resources\flags\3x2\AQ.svg" />
    <Content Include="src\resources\flags\3x2\AR.svg" />
    <Content Include="src\resources\flags\3x2\AS.svg" />
    <Content Include="src\resources\flags\3x2\AT.svg" />
    <Content Include="src\resources\flags\3x2\AU.svg" />
    <Content Include="src\resources\flags\3x2\AW.svg" />
    <Content Include="src\resources\flags\3x2\AX.svg" />
    <Content Include="src\resources\flags\3x2\AZ.svg" />
    <Content Include="src\resources\flags\3x2\BA.svg" />
    <Content Include="src\resources\flags\3x2\BB.svg" />
    <Content Include="src\resources\flags\3x2\BD.svg" />
    <Content Include="src\resources\flags\3x2\BE.svg" />
    <Content Include="src\resources\flags\3x2\BF.svg" />
    <Content Include="src\resources\flags\3x2\BG.svg" />
    <Content Include="src\resources\flags\3x2\BH.svg" />
    <Content Include="src\resources\flags\3x2\BI.svg" />
    <Content Include="src\resources\flags\3x2\BJ.svg" />
    <Content Include="src\resources\flags\3x2\BL.svg" />
    <Content Include="src\resources\flags\3x2\BM.svg" />
    <Content Include="src\resources\flags\3x2\BN.svg" />
    <Content Include="src\resources\flags\3x2\BO.svg" />
    <Content Include="src\resources\flags\3x2\BQ.svg" />
    <Content Include="src\resources\flags\3x2\BR.svg" />
    <Content Include="src\resources\flags\3x2\BS.svg" />
    <Content Include="src\resources\flags\3x2\BT.svg" />
    <Content Include="src\resources\flags\3x2\BV.svg" />
    <Content Include="src\resources\flags\3x2\BW.svg" />
    <Content Include="src\resources\flags\3x2\BY.svg" />
    <Content Include="src\resources\flags\3x2\BZ.svg" />
    <Content Include="src\resources\flags\3x2\CA.svg" />
    <Content Include="src\resources\flags\3x2\CC.svg" />
    <Content Include="src\resources\flags\3x2\CD.svg" />
    <Content Include="src\resources\flags\3x2\CF.svg" />
    <Content Include="src\resources\flags\3x2\CG.svg" />
    <Content Include="src\resources\flags\3x2\CH.svg" />
    <Content Include="src\resources\flags\3x2\CI.svg" />
    <Content Include="src\resources\flags\3x2\CK.svg" />
    <Content Include="src\resources\flags\3x2\CL.svg" />
    <Content Include="src\resources\flags\3x2\CM.svg" />
    <Content Include="src\resources\flags\3x2\CN.svg" />
    <Content Include="src\resources\flags\3x2\CO.svg" />
    <Content Include="src\resources\flags\3x2\CR.svg" />
    <Content Include="src\resources\flags\3x2\CU.svg" />
    <Content Include="src\resources\flags\3x2\CV.svg" />
    <Content Include="src\resources\flags\3x2\CW.svg" />
    <Content Include="src\resources\flags\3x2\CX.svg" />
    <Content Include="src\resources\flags\3x2\CY.svg" />
    <Content Include="src\resources\flags\3x2\CZ.svg" />
    <Content Include="src\resources\flags\3x2\DE.svg" />
    <Content Include="src\resources\flags\3x2\DJ.svg" />
    <Content Include="src\resources\flags\3x2\DK.svg" />
    <Content Include="src\resources\flags\3x2\DM.svg" />
    <Content Include="src\resources\flags\3x2\DO.svg" />
    <Content Include="src\resources\flags\3x2\DZ.svg" />
    <Content Include="src\resources\flags\3x2\EC.svg" />
    <Content Include="src\resources\flags\3x2\EE.svg" />
    <Content Include="src\resources\flags\3x2\EG.svg" />
    <Content Include="src\resources\flags\3x2\EH.svg" />
    <Content Include="src\resources\flags\3x2\ER.svg" />
    <Content Include="src\resources\flags\3x2\ES.svg" />
    <Content Include="src\resources\flags\3x2\ET.svg" />
    <Content Include="src\resources\flags\3x2\EU.svg" />
    <Content Include="src\resources\flags\3x2\FI.svg" />
    <Content Include="src\resources\flags\3x2\FJ.svg" />
    <Content Include="src\resources\flags\3x2\FK.svg" />
    <Content Include="src\resources\flags\3x2\FM.svg" />
    <Content Include="src\resources\flags\3x2\FO.svg" />
    <Content Include="src\resources\flags\3x2\FR.svg" />
    <Content Include="src\resources\flags\3x2\GA.svg" />
    <Content Include="src\resources\flags\3x2\GB.svg" />
    <Content Include="src\resources\flags\3x2\GD.svg" />
    <Content Include="src\resources\flags\3x2\GE-AB.svg" />
    <Content Include="src\resources\flags\3x2\GE-OS.svg" />
    <Content Include="src\resources\flags\3x2\GE.svg" />
    <Content Include="src\resources\flags\3x2\GF.svg" />
    <Content Include="src\resources\flags\3x2\GG.svg" />
    <Content Include="src\resources\flags\3x2\GH.svg" />
    <Content Include="src\resources\flags\3x2\GI.svg" />
    <Content Include="src\resources\flags\3x2\GL.svg" />
    <Content Include="src\resources\flags\3x2\GM.svg" />
    <Content Include="src\resources\flags\3x2\GN.svg" />
    <Content Include="src\resources\flags\3x2\GP.svg" />
    <Content Include="src\resources\flags\3x2\GQ.svg" />
    <Content Include="src\resources\flags\3x2\GR.svg" />
    <Content Include="src\resources\flags\3x2\GS.svg" />
    <Content Include="src\resources\flags\3x2\GT.svg" />
    <Content Include="src\resources\flags\3x2\GU.svg" />
    <Content Include="src\resources\flags\3x2\GW.svg" />
    <Content Include="src\resources\flags\3x2\GY.svg" />
    <Content Include="src\resources\flags\3x2\HK.svg" />
    <Content Include="src\resources\flags\3x2\HM.svg" />
    <Content Include="src\resources\flags\3x2\HN.svg" />
    <Content Include="src\resources\flags\3x2\HR.svg" />
    <Content Include="src\resources\flags\3x2\HT.svg" />
    <Content Include="src\resources\flags\3x2\HU.svg" />
    <Content Include="src\resources\flags\3x2\IC.svg" />
    <Content Include="src\resources\flags\3x2\ID.svg" />
    <Content Include="src\resources\flags\3x2\IE.svg" />
    <Content Include="src\resources\flags\3x2\IL.svg" />
    <Content Include="src\resources\flags\3x2\IM.svg" />
    <Content Include="src\resources\flags\3x2\IN.svg" />
    <Content Include="src\resources\flags\3x2\IO.svg" />
    <Content Include="src\resources\flags\3x2\IQ.svg" />
    <Content Include="src\resources\flags\3x2\IR.svg" />
    <Content Include="src\resources\flags\3x2\IS.svg" />
    <Content Include="src\resources\flags\3x2\IT.svg" />
    <Content Include="src\resources\flags\3x2\JE.svg" />
    <Content Include="src\resources\flags\3x2\JM.svg" />
    <Content Include="src\resources\flags\3x2\JO.svg" />
    <Content Include="src\resources\flags\3x2\JP.svg" />
    <Content Include="src\resources\flags\3x2\KE.svg" />
    <Content Include="src\resources\flags\3x2\KG.svg" />
    <Content Include="src\resources\flags\3x2\KH.svg" />
    <Content Include="src\resources\flags\3x2\KI.svg" />
    <Content Include="src\resources\flags\3x2\KM.svg" />
    <Content Include="src\resources\flags\3x2\KN.svg" />
    <Content Include="src\resources\flags\3x2\KP.svg" />
    <Content Include="src\resources\flags\3x2\KR.svg" />
    <Content Include="src\resources\flags\3x2\KW.svg" />
    <Content Include="src\resources\flags\3x2\KY.svg" />
    <Content Include="src\resources\flags\3x2\KZ.svg" />
    <Content Include="src\resources\flags\3x2\LA.svg" />
    <Content Include="src\resources\flags\3x2\LB.svg" />
    <Content Include="src\resources\flags\3x2\LC.svg" />
    <Content Include="src\resources\flags\3x2\LI.svg" />
    <Content Include="src\resources\flags\3x2\LK.svg" />
    <Content Include="src\resources\flags\3x2\LR.svg" />
    <Content Include="src\resources\flags\3x2\LS.svg" />
    <Content Include="src\resources\flags\3x2\LT.svg" />
    <Content Include="src\resources\flags\3x2\LU.svg" />
    <Content Include="src\resources\flags\3x2\LV.svg" />
    <Content Include="src\resources\flags\3x2\LY.svg" />
    <Content Include="src\resources\flags\3x2\MA.svg" />
    <Content Include="src\resources\flags\3x2\MC.svg" />
    <Content Include="src\resources\flags\3x2\MD.svg" />
    <Content Include="src\resources\flags\3x2\ME.svg" />
    <Content Include="src\resources\flags\3x2\MF.svg" />
    <Content Include="src\resources\flags\3x2\MG.svg" />
    <Content Include="src\resources\flags\3x2\MH.svg" />
    <Content Include="src\resources\flags\3x2\MK.svg" />
    <Content Include="src\resources\flags\3x2\ML.svg" />
    <Content Include="src\resources\flags\3x2\MM.svg" />
    <Content Include="src\resources\flags\3x2\MN.svg" />
    <Content Include="src\resources\flags\3x2\MO.svg" />
    <Content Include="src\resources\flags\3x2\MP.svg" />
    <Content Include="src\resources\flags\3x2\MQ.svg" />
    <Content Include="src\resources\flags\3x2\MR.svg" />
    <Content Include="src\resources\flags\3x2\MS.svg" />
    <Content Include="src\resources\flags\3x2\MT.svg" />
    <Content Include="src\resources\flags\3x2\MU.svg" />
    <Content Include="src\resources\flags\3x2\MV.svg" />
    <Content Include="src\resources\flags\3x2\MW.svg" />
    <Content Include="src\resources\flags\3x2\MX.svg" />
    <Content Include="src\resources\flags\3x2\MY.svg" />
    <Content Include="src\resources\flags\3x2\MZ.svg" />
    <Content Include="src\resources\flags\3x2\NA.svg" />
    <Content Include="src\resources\flags\3x2\NC.svg" />
    <Content Include="src\resources\flags\3x2\NE.svg" />
    <Content Include="src\resources\flags\3x2\NF.svg" />
    <Content Include="src\resources\flags\3x2\NG.svg" />
    <Content Include="src\resources\flags\3x2\NI.svg" />
    <Content Include="src\resources\flags\3x2\NL.svg" />
    <Content Include="src\resources\flags\3x2\NO.svg" />
    <Content Include="src\resources\flags\3x2\NP.svg" />
    <Content Include="src\resources\flags\3x2\NR.svg" />
    <Content Include="src\resources\flags\3x2\NU.svg" />
    <Content Include="src\resources\flags\3x2\NZ.svg" />
    <Content Include="src\resources\flags\3x2\OM.svg" />
    <Content Include="src\resources\flags\3x2\PA.svg" />
    <Content Include="src\resources\flags\3x2\PE.svg" />
    <Content Include="src\resources\flags\3x2\PF.svg" />
    <Content Include="src\resources\flags\3x2\PG.svg" />
    <Content Include="src\resources\flags\3x2\PH.svg" />
    <Content Include="src\resources\flags\3x2\PK.svg" />
    <Content Include="src\resources\flags\3x2\PL.svg" />
    <Content Include="src\resources\flags\3x2\PM.svg" />
    <Content Include="src\resources\flags\3x2\PN.svg" />
    <Content Include="src\resources\flags\3x2\PR.svg" />
    <Content Include="src\resources\flags\3x2\PS.svg" />
    <Content Include="src\resources\flags\3x2\PT.svg" />
    <Content Include="src\resources\flags\3x2\PW.svg" />
    <Content Include="src\resources\flags\3x2\PY.svg" />
    <Content Include="src\resources\flags\3x2\QA.svg" />
    <Content Include="src\resources\flags\3x2\RE.svg" />
    <Content Include="src\resources\flags\3x2\RO.svg" />
    <Content Include="src\resources\flags\3x2\RS.svg" />
    <Content Include="src\resources\flags\3x2\RU.svg" />
    <Content Include="src\resources\flags\3x2\RW.svg" />
    <Content Include="src\resources\flags\3x2\SA.svg" />
    <Content Include="src\resources\flags\3x2\SB.svg" />
    <Content Include="src\resources\flags\3x2\SC.svg" />
    <Content Include="src\resources\flags\3x2\SD.svg" />
    <Content Include="src\resources\flags\3x2\SE.svg" />
    <Content Include="src\resources\flags\3x2\SG.svg" />
    <Content Include="src\resources\flags\3x2\SH.svg" />
    <Content Include="src\resources\flags\3x2\SI.svg" />
    <Content Include="src\resources\flags\3x2\SJ.svg" />
    <Content Include="src\resources\flags\3x2\SK.svg" />
    <Content Include="src\resources\flags\3x2\SL.svg" />
    <Content Include="src\resources\flags\3x2\SM.svg" />
    <Content Include="src\resources\flags\3x2\SN.svg" />
    <Content Include="src\resources\flags\3x2\SO.svg" />
    <Content Include="src\resources\flags\3x2\SR.svg" />
    <Content Include="src\resources\flags\3x2\SS.svg" />
    <Content Include="src\resources\flags\3x2\ST.svg" />
    <Content Include="src\resources\flags\3x2\SV.svg" />
    <Content Include="src\resources\flags\3x2\SX.svg" />
    <Content Include="src\resources\flags\3x2\SY.svg" />
    <Content Include="src\resources\flags\3x2\SZ.svg" />
    <Content Include="src\resources\flags\3x2\TA.svg" />
    <Content Include="src\resources\flags\3x2\TC.svg" />
    <Content Include="src\resources\flags\3x2\TD.svg" />
    <Content Include="src\resources\flags\3x2\TF.svg" />
    <Content Include="src\resources\flags\3x2\TG.svg" />
    <Content Include="src\resources\flags\3x2\TH.svg" />
    <Content Include="src\resources\flags\3x2\TJ.svg" />
    <Content Include="src\resources\flags\3x2\TK.svg" />
    <Content Include="src\resources\flags\3x2\TL.svg" />
    <Content Include="src\resources\flags\3x2\TM.svg" />
    <Content Include="src\resources\flags\3x2\TN.svg" />
    <Content Include="src\resources\flags\3x2\TO.svg" />
    <Content Include="src\resources\flags\3x2\TR.svg" />
    <Content Include="src\resources\flags\3x2\TT.svg" />
    <Content Include="src\resources\flags\3x2\TV.svg" />
    <Content Include="src\resources\flags\3x2\TW.svg" />
    <Content Include="src\resources\flags\3x2\TZ.svg" />
    <Content Include="src\resources\flags\3x2\UA.svg" />
    <Content Include="src\resources\flags\3x2\UG.svg" />
    <Content Include="src\resources\flags\3x2\UM.svg" />
    <Content Include="src\resources\flags\3x2\US.svg" />
    <Content Include="src\resources\flags\3x2\UY.svg" />
    <Content Include="src\resources\flags\3x2\UZ.svg" />
    <Content Include="src\resources\flags\3x2\VA.svg" />
    <Content Include="src\resources\flags\3x2\VC.svg" />
    <Content Include="src\resources\flags\3x2\VE.svg" />
    <Content Include="src\resources\flags\3x2\VG.svg" />
    <Content Include="src\resources\flags\3x2\VI.svg" />
    <Content Include="src\resources\flags\3x2\VN.svg" />
    <Content Include="src\resources\flags\3x2\VU.svg" />
    <Content Include="src\resources\flags\3x2\WF.svg" />
    <Content Include="src\resources\flags\3x2\WS.svg" />
    <Content Include="src\resources\flags\3x2\XK.svg" />
    <Content Include="src\resources\flags\3x2\YE.svg" />
    <Content Include="src\resources\flags\3x2\YT.svg" />
    <Content Include="src\resources\flags\3x2\ZA.svg" />
    <Content Include="src\resources\flags\3x2\ZM.svg" />
    <Content Include="src\resources\flags\3x2\ZW.svg" />
    <Content Include="src\resources\folder.svg" />
    <Content Include="src\resources\fonts\FiraSans-Bold.ttf" />
    <Content Include="src\resources\fonts\FiraSans-Medium.ttf" />
    <Content Include="src\resources\fonts\FiraSans-Regular.ttf" />
    <Content Include="src\resources\fonts\Inter-Regular.ttf" />
    <Content Include="src\resources\fonts\Inter-VariableFont_slnt,wght.ttf" />
    <Content Include="src\resources\fonts\NotoSans-VariableFont_wdth,wght.ttf" />
    <Content Include="src\resources\fonts\OpenSans-Italic-VariableFont_wdth,wght.ttf" />
    <Content Include="src\resources\fonts\Roboto-Regular.ttf" />
    <Content Include="src\resources\icon-sort-down.png" />
    <Content Include="src\resources\icon-sort-down.svg" />
    <Content Include="src\resources\icon-sort-up.png" />
    <Content Include="src\resources\icon-sort-up.svg" />
    <Content Include="src\resources\info.svg" />
    <Content Include="src\resources\layers.svg" />
    <Content Include="src\resources\loading.gif" />
    <Content Include="src\resources\lock.svg" />
    <Content Include="src\resources\log-out.svg" />
    <Content Include="src\resources\login-background-blurred.png" />
    <Content Include="src\resources\login-background.png" />
    <Content Include="src\resources\mail.svg" />
    <Content Include="src\resources\map-pin.svg" />
    <Content Include="src\resources\menubar-table.svg" />
    <Content Include="src\resources\minus.svg" />
    <Content Include="src\resources\phone.svg" />
    <Content Include="src\resources\play-circle.svg" />
    <Content Include="src\resources\plus.svg" />
    <Content Include="src\resources\printer.svg" />
    <Content Include="src\resources\profile-icon.svg" />
    <Content Include="src\resources\save.svg" />
    <Content Include="src\resources\search.svg" />
    <Content Include="src\resources\sidebar-logo.png" />
    <Content Include="src\resources\smartphone.svg" />
    <Content Include="src\resources\square.svg" />
    <Content Include="src\resources\tableview-columns.svg" />
    <Content Include="src\resources\tableview-download.svg" />
    <Content Include="src\resources\tableview-edit.svg" />
    <Content Include="src\resources\tableview-minus.svg" />
    <Content Include="src\resources\tableview-save.svg" />
    <Content Include="src\resources\tool.svg" />
    <Content Include="src\resources\trash-2.svg" />
    <Content Include="src\resources\trash.svg" />
    <Content Include="src\resources\tree.svg" />
    <Content Include="src\resources\user.svg" />
    <Content Include="src\resources\users.svg" />
    <Content Include="src\resources\x-circle-red.svg" />
    <Content Include="src\resources\x-circle.svg" />
    <Content Include="src\resources\x-square.svg" />
    <Content Include="src\resources\x.svg" />
    <Content Include="src\resources\zap.svg" />
    <Content Include="src\utils\__pycache__\convert_roi_payload.cpython-39.pyc" />
    <Content Include="src\utils\__pycache__\export.cpython-39.pyc" />
    <Content Include="src\utils\__pycache__\handle_re_patterns.cpython-39.pyc" />
    <Content Include="src\utils\__pycache__\logger.cpython-39.pyc" />
    <Content Include="src\utils\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\columnorganizer.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\reportissue.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\roiextractionpreview.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\tableexporter.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\tableimporter.cpython-39.pyc" />
    <Content Include="src\views\dialogs\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\activateproductkeyform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\addsourceexistingproject.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\authenticateform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\baseform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\companyprofileform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\createaccountform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\loginform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\newprojectexplorerform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\newprojectsetupform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\tokencheckoutform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\tokencheckoutstatusform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\tokencheckoutwindow.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\tokenpaymentform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\useragreementform.cpython-39.pyc" />
    <Content Include="src\views\forms\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\views\popups\__pycache__\queuestatuspopup.cpython-39.pyc" />
    <Content Include="src\views\popups\__pycache__\tokenstatuspopup.cpython-39.pyc" />
    <Content Include="src\views\popups\__pycache__\userprofilepopup.cpython-39.pyc" />
    <Content Include="src\views\popups\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\bomresultsview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\generaldataview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\hiddentablefields.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\lookuptable.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\outlierresultsview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\rfqtableview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\specresultsview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\spoolresultsview.cpython-39.pyc" />
    <Content Include="src\views\tables\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\blueprintreaderview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\contactdetails.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\documentsview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\documentviewonly.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\formsview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\navigationsidebar.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\placeholderview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\projectview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\supportticket.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\tabbuttons.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\tableresultsview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\uploadqueueview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\workspaceview.cpython-39.pyc" />
    <Content Include="src\views\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\blueprintreaderstatuswidget.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\documentviewerstatuswidget.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\jobstatuswidget.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\realtimestatuswidget.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\tablestatuswidget.cpython-39.pyc" />
    <Content Include="src\widgets\statusbar\__pycache__\__init__.cpython-39.pyc" />
    <Content Include="src\widgets\__pycache__\groupedtableview.cpython-39.pyc" />
    <Content Include="src\widgets\__pycache__\jobqueuecard.cpython-39.pyc" />
    <Content Include="src\__pycache__\app_paths.cpython-39.pyc" />
    <Content Include="src\__pycache__\mainwindow.cpython-39.pyc" />
    <Content Include="src\__pycache__\pyside_util.cpython-39.pyc" />
    <Content Include="src\__pycache__\splashscreen.cpython-39.pyc" />
    <Content Include="src\__pycache__\theme.cpython-39.pyc" />
    <Content Include="src\__pycache__\util.cpython-39.pyc" />
    <Content Include="_worker_BU.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>