"""
These BOM widths vary in width, so extract based on values which are centrally aligned

This is intended for NON-OCR docs
"""
import os
import time
import pandas as pd
from src.app_paths import getSourceRawDataPath, getSourceExtractionOptionsPath
from src.utils.convert_roi_payload import convert_roi_payload
import numpy as np


def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip())  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(1, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part)  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return pages

def string_to_tuple(coords):
    if isinstance(coords, tuple):
        # It's already a tuple, return it as is
        return coords
    elif isinstance(coords, np.ndarray):
        # It's already a tuple, return it as is
        return coords
    try:
        # Assuming the input is a string that needs to be converted to a tuple
        coords = coords.strip("() ")
        return tuple(map(float, coords.split(', ')))
    except (ValueError, SyntaxError):
        # Return a default value that indicates invalid coordinates
        return (0, 0, 0, 0)

def convert_coords_to_tuple(df): # Original
    try:
        # Convert the string representation of coordinates to actual tuples
        df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
        df['coordinates'] = df['coordinates'].apply(string_to_tuple)

        # Update 'coordinates2' if it's (0,0,0,0). Annotations are stored in 'coordinates'
        df.loc[df['coordinates2'] == (0, 0, 0, 0), 'coordinates2'] = df['coordinates']
    except Exception as e:
        return pd.DataFrame()
    return df

def plugin_custom_brock_pes_extraction(project_source: tuple,
                                       page_range: str,
                                       extract_group_list: str = None,
                                       column_headers: str = "ITEM NO.,DESCRIPTION,SHAPE,QTY.,LENGTH,TOTAL LENGTH,MATERIAL",
                                       save_file: str = "debug/brock_pes_bom.xlsx"):
    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, fileExists)

    column_headers = [header.strip() for header in column_headers.split(",")]

    # Load raw data file
    raw_data_file = getSourceRawDataPath(projectId, filename)
    df = pd.read_feather(raw_data_file)

    # Filter by page range
    pages = parse_page_range(str(page_range), 9999)
    df = df[df["pdf_page"].isin(pages)]

    # Filter by group list
    if extract_group_list:
        try:
            group_list = [int(g) for g in extract_group_list.split(",")]
        except Exception:
            pass

        if not group_list:
            try:
                group_list = [int(extract_group_list)]
            except Exception:
                return "Invalid group list format."

        df = df[df["group"].isin(group_list)]

    # Filter by column headers
    # df = df[column_headers]
    df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
    df['coordinates'] = df['coordinates'].apply(string_to_tuple)
    # df = df[column_headers]
    df.to_excel(save_file, index=False)
    # print(df)

    page_headers = {}
    # Find headers first
    for row in df[df["value"].isin(column_headers)].itertuples():
        pdf_page = row.pdf_page
        header = row.value
        coordinates = row.coordinates2
        x1, y1, x2, y2 = coordinates
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        if center_x > 800: # ignore ifc
            continue
        page_headers.setdefault(pdf_page, {})
        page_headers[pdf_page][header] = (x1, x2)

    bom_records = []
    for pdf_page, header_positions in page_headers.items():
        page_values = {"pdf_page": pdf_page}
        for row in df[(df["pdf_page"] == pdf_page) & (~df["value"].isin(column_headers))].itertuples():
            coordinates = row.coordinates2
            x1, y1, x2, y2 = coordinates
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            height = y2 - y1
            if int(height) != 11:
                continue

            if center_y > 700: # ignore ifc
                continue
            value = row.value
            if value in column_headers:
                continue

            for header, (header_x1, header_x2) in header_positions.items():
                if header_x1 < center_x < header_x2:
                    # page_values[header] = value
                    bom_records.append({
                        "pdf_page": pdf_page,
                        header: value,
                        "x": center_x,
                        "y": center_y,
                        "height": height
                    })
                    break

        # bom_records.append(page_values)

    # Save bom
    bom_df = pd.DataFrame(bom_records)
    bom_df.to_excel("debug/bom_temp.xlsx")

    grouped = bom_df.groupby(["pdf_page", "y"])
    structured_bom = []
    for (pdf_page, y), group in grouped:
        row_items = {}
        row_items["pdf_page"] = pdf_page
        row_items["y"] = y
        for _, row in group.iterrows():
            for header in column_headers:
                if row[header] and pd.isna(row[header]) == False:
                    row_items[header] = row[header]
        if column_headers[0] in row_items:
            structured_bom.append(row_items)

    structured_bom_df = pd.DataFrame(structured_bom)
    new_order = ["pdf_page"] + column_headers
    structured_bom_df = structured_bom_df[new_order]
    if not structured_bom_df.empty:
        structured_bom_df.to_excel(save_file, index=False)
        return f"Data exported and saved to: {save_file}"

    return "No BOM records found to save"

