import pandas as pd
import os
import re

"""
SQLite to PostgreSQL column mapping dictionary.
Maps old SQLite column names to new PostgreSQL column names.
"""

GENERAL_TABLE_MAPPING = {
    # Primary keys and relationships
    "id": "id",
    "pdf_id": "pdf_id",
    
    # Renamed measurement columns
    "lf": "length",
    "sf": "calculated_area",
    "ef": "calculated_eq_length",
    
    # camelCase to snake_case conversions
    "annotMarkups": "annot_markups",
    "blockCoordinates": "block_coordinates",
    "clientDocumentId": "client_document_id",
    "documentId": "document_id",
    "documentTitle": "document_title",
    "flangeID": "flange_id",
    "heatTrace": "heat_trace",
    "insulationSpec": "insulation_spec",
    "insulationThickness": "insulation_thickness",
    "isoNumber": "iso_number",
    "isoType": "iso_type",
    "lineNumber": "line_number",
    "maxElevation": "max_elevation",
    "mediumCode": "medium_code",
    "minElevation": "min_elevation",
    "modDate": "mod_date",
    "paintSpec": "paint_spec",
    "pipeSpec": "pipe_spec",
    "pipeStandard": "pipe_standard",
    "processLineList": "process_line_list",
    "processUnit": "process_unit",
    "projectNo": "project_no",
    "projectName": "project_name",
    "totalSheets": "total_sheets",
    "vendorDocumentId": "vendor_document_id",
    "weldId": "weld_id",
    "weldClass": "weld_class",
    "xCoord": "x_coord",
    "yCoord": "y_coord",
    "paintColor": "paint_color",
    "sysDocument": "sys_document",
    "sysDocumentName": "sys_document_name",
    "sys_filename": "sys_filename",  # Already snake_case in original
    "sys_path": "sys_path",          # Already snake_case in original
    "sys_build": "sys_build",        # Already snake_case in original
    "sys_layout_valid": "sys_layout_valid",  # Already snake_case in original
    
    # Unchanged columns
    "area": "area",
    "coordinates": "coordinates",
    "cwp": "cwp",
    "design_code": "design_code",
    "document_description": "document_description",
    "drawing": "drawing",
    "elevation": "elevation",
    "pid": "pid",
    "pwht": "pwht",
    "revision": "revision",
    "sequence": "sequence",
    "service": "service",
    "sheet": "sheet",
    "size": "size",
    "system": "system",
    "unit": "unit",
    "xray": "xray",
    "elbows_90": "elbows_90",
    "elbows_45": "elbows_45",
    "bevels": "bevels",
    "tees": "tees",
    "reducers": "reducers",
    "caps": "caps",
    "flanges": "flanges",
    "valves_flanged": "valves_flanged",
    "valves_welded": "valves_welded",
    "cut_outs": "cut_outs",
    "supports": "supports",
    "bends": "bends",
    "field_welds": "field_welds",
    
    # New columns in PostgreSQL (not in SQLite)
    # These will need special handling during migration
    # "union_couplings": No SQLite equivalent
    # "expansion_joints": No SQLite equivalent
}

BOM_TABLE_MAPPING = {
    # Primary keys and relationships
    "id": "id",
    "pdf_id": "pdf_id",
    
    # Renamed measurement columns
    "ef": "calculated_eq_length",
    "sf": "calculated_eq_area",
    
    # camelCase to snake_case conversions
    "componentCategory": "component_category",
    "lineNumber": "line_number",
    
    # Unchanged columns
    "pos": "pos",
    "material_description": "material_description",
    "size": "size",
    "ident": "ident",
    "item": "item",
    "tag": "tag",
    "quantity": "quantity",
    "status": "status",
    "nb": "nb",
    "fluid": "fluid",
    "clean_spec": "clean_spec",
    "item_count": "item_count",
    "item_length": "item_length",
    "total_length": "total_length",
    "shape": "shape",
    
    # New columns in PostgreSQL (not in SQLite)
    # "size1": No direct SQLite equivalent
    # "size2": No direct SQLite equivalent
}


# For reverse lookups (PostgreSQL to SQLite)
GENERAL_TABLE_REVERSE_MAPPING = {v: k for k, v in GENERAL_TABLE_MAPPING.items()}
BOM_TABLE_REVERSE_MAPPING = {v: k for k, v in BOM_TABLE_MAPPING.items()}

# Special handling for new columns
NEW_POSTGRESQL_COLUMNS = {
    "general": ["union_couplings", "expansion_joints", "created_at", "updated_at"],
    "bom": ["size1", "size2", "created_at", "updated_at"]
}

# Migration helper function example
def get_postgresql_column_name(table, sqlite_column):
    """
    Returns the PostgreSQL column name for a given SQLite column name.
    
    Args:
        table (str): The table name ('general' or 'bom')
        sqlite_column (str): The SQLite column name
        
    Returns:
        str: The PostgreSQL column name, or None if no mapping exists
    """
    if table.lower() == 'general':
        return GENERAL_TABLE_MAPPING.get(sqlite_column)
    elif table.lower() == 'bom':
        return BOM_TABLE_MAPPING.get(sqlite_column) 
    return None

def convert_columns(df, table_type):
    """
    Convert PostgreSQL column names to SQLite column names
    
    Args:
        df (pandas.DataFrame): DataFrame with PostgreSQL column names
        table_type (str): 'general' or 'bom'
    
    Returns:
        tuple: (DataFrame with converted column names, list of columns that couldn't be converted)
    """
    mapping = GENERAL_TABLE_REVERSE_MAPPING if table_type == 'general' else BOM_TABLE_REVERSE_MAPPING
    
    # Keep track of columns that couldn't be converted
    unconverted_columns = []
    
    # Create a new column mapping dictionary
    column_rename_map = {}
    
    for col in df.columns:
        if col in mapping:
            column_rename_map[col] = mapping[col]
        elif col in NEW_POSTGRESQL_COLUMNS.get(table_type, []):
            # Skip columns that are new in PostgreSQL
            pass
        else:
            unconverted_columns.append(col)
            column_rename_map[col] = col  # Keep the original name
    
    # Rename columns
    df_converted = df.rename(columns=column_rename_map)
    
    return df_converted, unconverted_columns

def extract_roi_fields(roi_file_path):
    """
    Extract field names from ROI JSON file.
    """
    import json
    
    with open(roi_file_path, 'r') as f:
        roi_data = json.load(f)
    
    general_columns = []
    bom_columns = []
    
    for group_id, group_data in roi_data.get('groups', {}).items():
        for roi in group_data.get('rois', []):
            roi_name = roi.get('name')
            
            if roi_name == 'BOM':
                bom_columns = roi.get('columnNames', [])
            elif roi_name != 'Isometric Drawing Area':
                general_columns.append(roi_name)
    
    return general_columns, bom_columns

def to_proper_case(name):
    """
    Convert snake_case or camelCase to proper case without underscores.
    Example: "hello_world" -> "Hello World", "helloWorld" -> "Hello World"
    """
    # Handle camelCase
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1 \2', name)
    # Handle consecutive uppercase letters
    s2 = re.sub('([a-z0-9])([A-Z])', r'\1 \2', s1)
    # Replace underscores with spaces
    s3 = s2.replace('_', ' ')
    # Capitalize words
    return s3.title()

def main():
    # File paths
    general_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 035 - Formosa\Data\Workspace\PG\General.csv"
    bom_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 035 - Formosa\Data\Workspace\PG\bom.csv"
    rfq_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 035 - Formosa\Data\Workspace\PG\rfq.csv"
    
    # Output path
    output_dir = os.path.dirname(general_path)
    output_path = os.path.join(output_dir, "Formosa KSG.xlsx")
    
    # Load data
    try:
        general_df = pd.read_csv(general_path, low_memory=False)
        print(f"Loaded General table: {len(general_df)} rows, {len(general_df.columns)} columns")
    except Exception as e:
        general_df = pd.DataFrame()
        print(f"Error loading General table: {e}")
    
    try:
        bom_df = pd.read_csv(bom_path, low_memory=False)
        print(f"Loaded BOM table: {len(bom_df)} rows, {len(bom_df.columns)} columns")
    except Exception as e:
        bom_df = pd.DataFrame()
        print(f"Error loading BOM table: {e}")
    
    try:
        rfq_df = pd.read_csv(rfq_path, low_memory=False)
        print(f"Loaded RFQ table: {len(rfq_df)} rows, {len(rfq_df.columns)} columns")
    except Exception as e:
        rfq_df = pd.DataFrame()
        print(f"Error loading RFQ table: {e}")
    
    # ROI requested fields (from your roi.json)
    roi_general_fields = [
        'id', 'sys_filename', 'pdf_id', 'pdf_page', 'document_description',
        'drawing',
        'iso_number',
        'line_number',
        'drawing'
        'sheet',
        'total_sheets',
        'insulationSpec',
        'insulationThickness',
        
        'pid',
        'paint_color',
        'paintSpec',
        'projectName',
        'revision',
        'sequence',
        'heatTrace',
        'unit',
        'minElevation',
        'maxElevation',
        'avgElevation'
    ]
    
    roi_bom_fields = ['pos', 'quantity', 'size', 'material_description', 'item']
    
    # Always include these fields
    always_include_general = [
        'id', 'sys_filename', 'pdf_id', 'pdf_page', 'size', 
        'minElevation', 'maxElevation', 'avgElevation', 'avg_elevation', 'max_elevation', 'min_elevation', 
        'lf', 'sf', 'ef', 
        'elbows_90', 'elbows_45', 'bevels', 'tees', 
        'reducers', 'caps', 'flanges', 'valves_flanged', 'valves_welded', 
        'cut_outs', 'supports', 'bends', 'field_welds', 
        'union_couplings', 'expansion_joints'
    ]
    
    always_include_bom = [
        'id', 'sys_filename', 'pdf_id', 'pdf_page', 'size1', 'size2',
        'rfq_scope', 'general_category', 'unit_of_measure', 'material',
        'abbreviated_material', 'technical_standard', 'astm', 'grade',
        'rating', 'schedule', 'coating', 'forging', 'ends', 'item_tag',
        'tie_point', 'pipe_category', 'valve_type', 'fitting_category',
        'weld_category', 'bolt_category', 'gasket_category'
    ]
    
    # Track columns that weren't found
    general_not_found = []
    bom_not_found = []
    
    # Process General table
    if not general_df.empty:
        # Convert PostgreSQL column names to SQLite column names
        general_rename_dict = {}
        for pg_col in general_df.columns:
            # Try to find the SQLite equivalent
            sqlite_col = GENERAL_TABLE_REVERSE_MAPPING.get(pg_col)
            if sqlite_col:
                general_rename_dict[pg_col] = sqlite_col
        
        # Rename columns from PostgreSQL to SQLite format
        if general_rename_dict:
            general_df = general_df.rename(columns=general_rename_dict)
        
        # Identify columns to keep based on ROI and always include lists
        all_keep_general = set(roi_general_fields + always_include_general)
        general_cols_to_keep = []
        
        # First add the ROI requested fields in their original order
        for col in roi_general_fields:
            if col in general_df.columns:
                general_cols_to_keep.append(col)
            else:
                general_not_found.append(col)
        
        # Then add the always include fields if not already added
        for col in always_include_general:
            if col in general_df.columns and col not in general_cols_to_keep:
                general_cols_to_keep.append(col)
            elif col not in general_df.columns:
                general_not_found.append(col)
        
        # Create subset with only the columns we want to keep
        if general_cols_to_keep:
            general_subset = general_df[general_cols_to_keep].copy()
        else:
            # If no columns matched our criteria, keep all columns
            general_subset = general_df.copy()
            print("Warning: No general columns matched criteria, keeping all columns")
        
        # Convert column names to proper case for display
        general_display_names = {col: to_proper_case(col) for col in general_subset.columns}
        general_subset = general_subset.rename(columns=general_display_names)
        
        print(f"General table after processing: {len(general_subset.columns)} columns kept")
        print(f"General table columns not found: {general_not_found}")
    else:
        general_subset = pd.DataFrame()
    
    # Process BOM table
    if not bom_df.empty:
        # Convert PostgreSQL column names to SQLite column names
        bom_rename_dict = {}
        for pg_col in bom_df.columns:
            # Try to find the SQLite equivalent
            sqlite_col = BOM_TABLE_REVERSE_MAPPING.get(pg_col)
            if sqlite_col:
                bom_rename_dict[pg_col] = sqlite_col
        
        # Rename columns from PostgreSQL to SQLite format
        if bom_rename_dict:
            bom_df = bom_df.rename(columns=bom_rename_dict)
        
        # Identify columns to keep based on ROI and always include lists
        all_keep_bom = set(roi_bom_fields + always_include_bom)
        bom_cols_to_keep = []
        
        # First add the ROI requested fields in their original order
        for col in roi_bom_fields:
            if col in bom_df.columns:
                bom_cols_to_keep.append(col)
            else:
                bom_not_found.append(col)
        
        # Then add the always include fields if not already added
        for col in always_include_bom:
            if col in bom_df.columns and col not in bom_cols_to_keep:
                bom_cols_to_keep.append(col)
            elif col not in bom_df.columns:
                bom_not_found.append(col)
        
        # Create subset with only the columns we want to keep
        if bom_cols_to_keep:
            bom_subset = bom_df[bom_cols_to_keep].copy()
        else:
            # If no columns matched our criteria, keep all columns
            bom_subset = bom_df.copy()
            print("Warning: No BOM columns matched criteria, keeping all columns")
        
        # Convert column names to proper case for display
        bom_display_names = {col: to_proper_case(col) for col in bom_subset.columns}
        bom_subset = bom_subset.rename(columns=bom_display_names)
        
        print(f"BOM table after processing: {len(bom_subset.columns)} columns kept")
        print(f"BOM table columns not found: {bom_not_found}")
    else:
        bom_subset = pd.DataFrame()

    # Process RFQ table - normalize names but keep all columns
    if not rfq_df.empty:
        # Create a copy of the RFQ DataFrame
        rfq_subset = rfq_df.copy()
        
        # Convert column names to proper case for display
        rfq_display_names = {col: to_proper_case(col) for col in rfq_subset.columns}
        rfq_subset = rfq_subset.rename(columns=rfq_display_names)
        
        print(f"RFQ table after processing: {len(rfq_subset.columns)} columns (all kept, names normalized)")
    else:
        rfq_subset = pd.DataFrame()
    
    # Write to Excel
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        general_subset.to_excel(writer, sheet_name='General', index=False)
        bom_subset.to_excel(writer, sheet_name='BOM', index=False)
        rfq_df.to_excel(writer, sheet_name='RFQ', index=False)
    
    print(f"\nExcel file created: {output_path}")
    
    # Print columns that were kept
    print("\nColumns kept for General table:")
    for i, col in enumerate(general_subset.columns):
        print(f"  {i+1}. {col}")
    
    print("\nColumns kept for BOM table:")
    for i, col in enumerate(bom_subset.columns):
        print(f"  {i+1}. {col}")

if __name__ == "__main__":
    main()