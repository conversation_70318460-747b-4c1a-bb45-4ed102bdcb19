import re
from src.utils.logger import logger
from PySide6.QtWidgets import (QWidget, QHB<PERSON>Layout, Q<PERSON>ridLayout, QLabel, 
                               QPushButton, QLineEdit, QVBoxLayout, QComboBox)
from PySide6.QtGui import QPixmap, QShowEvent, QShortcut, QKeySequence
from PySide6.QtWidgets import QSizePolicy
from PySide6.QtCore import Qt
from src.views.forms.baseform import BaseForm
from pubsub import pub
from src.util import is_valid_email_string, parse_phone_number, get_region_code_map
from src.pyside_util import get_resource_qicon


USERNAME_DEFAULT_MESSAGE = "Username *"
EMAIL_DEFAULT_MESSAGE = "Email *"
PASSWORD_DEFAULT_MESSAGE = "Password *"

EMAIL_REQUIRED = "Error: Email required"
EMAIL_INVALID = "Error: Invalid email address"
PASSWORD_REQUIRED = "Error: Password required"
USERNAME_REQUIRED = "Error: Username required"
USERNAME_INVALID = "Error: Username invalid"
USERNAME_INVALID_HINT = "Username requirements:\n  4 to 32 characters long, [a-z,A-Z,1-9]\n  No spaces\n  No special characters"

NAME_REQUIRED = "Error: Name required"
PASSWORD_MISMATCH = "Error: Passwords do not match"

PHONE_REQUIRED = "Error: Phone number required"
PHONE_INVALID = "Error: Invalid phone number"

# https://regex101.com/r/lM0nC7/1
def is_valid_username(username):
    regex = "^(?=.{4,32}$)(?![_.-])(?!.*[_.]{2})[a-zA-Z0-9._-]+(?<![_.])$"
    r = re.compile(regex)
    if (re.search(r, username)):
        return True
    else:
        return False

def is_valid_name(name):
    if name.replace(" ", "").isalpha():
        return True
    else:
        return False


class PhoneWidget(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.setMaximumHeight(32)

        self.countryCodes = QComboBox()
        self.countryCodes.setMinimumWidth(80)
        self.phone = QLineEdit()
        self.phone.addAction(get_resource_qicon("smartphone.svg"), QLineEdit.LeadingPosition)
        self.phone.textEdited.connect(self.onPhoneEdit)
        self.phone.setEchoMode(QLineEdit.EchoMode.Normal)

        self.layout().addWidget(self.countryCodes)
        self.layout().addWidget(self.phone)

        self.refreshCountryCodeOptions()
    
    def onPhoneEdit(self, text: str):
        if not text:
            return
        text = text.replace("+", "")
        text = text.strip()
        index = None
        for n in range(self.countryCodes.count()):
            data = self.countryCodes.itemData(n)
            code = data["code"]
            if text == str(code):
                index = n
                break

        if index is not None:
            self.countryCodes.setCurrentIndex(index)
            return

        for n in range(self.countryCodes.count()):
            data = self.countryCodes.itemData(n)
            code = data["code"]
            if text.startswith(str(code)):
                self.countryCodes.setCurrentIndex(n)
                break

    def refreshCountryCodeOptions(self):
        self.countryCodes.clear()
        regionMap = get_region_code_map()
        default = -1
        for n, region_code in enumerate(sorted(regionMap.keys())):
            code = regionMap[region_code]
            self.countryCodes.insertItem(self.countryCodes.count(), 
                                         get_resource_qicon(f"flags/3x2/{region_code}.svg"),
                                         region_code,
                                         {"code": code, "region_code":  region_code})
            if region_code == "US":
                default = n
        self.countryCodes.setCurrentIndex(default)

    def isValid(self) -> bool:
        return "clean_phone" in parse_phone_number(self.phone.text())

    def getValue(self):
        return parse_phone_number(self.phone.text())


class CreateAccountForm(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)

    def initUi(self):
        self.formSize.setHeight(684)
        self.formSize.setWidth(896)

        self.title.setText("Create Account")
        self.subtitle.setTextFormat(Qt.TextFormat.RichText)
        self.subtitle.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)
        self.subtitle.setText("Enter your account details or <a href='LoginForm'>log in</a>")
        self.subtitle.linkActivated.connect(self.onLinkActivated)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addRow(hbox)

        gridName = QWidget()
        gridName.setLayout(QGridLayout())
        gridName.layout().setContentsMargins(4, 16, 16, 4)
        hbox.layout().addWidget(gridName)
    
        # self.lblName = QLabel("Name *")
        # gridName.layout().addWidget(self.lblName, 1, 0)

        # self.firstName = QLineEdit()
        # self.firstName.setEchoMode(QLineEdit.EchoMode.Normal)
        # self.firstName.setPlaceholderText("First")
        # gridName.layout().addWidget(self.firstName, 2, 0)

        # self.lastName = QLineEdit()
        # self.lastName.setPlaceholderText("Last")
        # self.lastName.setEchoMode(QLineEdit.EchoMode.Normal)
        # gridName.layout().addWidget(self.lastName, 2, 1)

        self.lblUsername = QLabel("Username *")
        gridName.layout().addWidget(self.lblUsername, 3, 0)
        self.username = QLineEdit()
        self.username.addAction(get_resource_qicon("user.svg"), QLineEdit.LeadingPosition)
        self.username.textEdited.connect(self.onUsernameEdit)
        self.username.setEchoMode(QLineEdit.EchoMode.Normal)
        gridName.layout().addWidget(self.username, 4, 0, 1, 2)

        # self.lblPhone = QLabel("Phone *")
        # gridName.layout().addWidget(self.lblPhone, 5, 0)
        # self.phone = PhoneWidget(self)
        # gridName.layout().addWidget(self.phone, 6, 0, 1, 2)

        self.lblEmail = QLabel("Email *")
        gridName.layout().addWidget(self.lblEmail, 7, 0)
        self.email = QLineEdit()
        self.email.addAction(get_resource_qicon("mail.svg"), QLineEdit.LeadingPosition)
        self.email.setEchoMode(QLineEdit.EchoMode.Normal)
        gridName.layout().addWidget(self.email, 8, 0, 1, 2)

        self.iconEye = get_resource_qicon("eye.svg")
        self.iconEyeOff = get_resource_qicon("eye-off.svg")

        self.lblPassword = QLabel("Password")
        gridName.layout().addWidget(self.lblPassword, 9, 0)
        self.password = QLineEdit()
        self.password.addAction(get_resource_qicon("lock.svg"), QLineEdit.LeadingPosition)
        self.password.setEchoMode(QLineEdit.EchoMode.Password)
        self.revealAction = self.password.addAction(self.iconEye, QLineEdit.ActionPosition.TrailingPosition)
        self.revealAction.setCheckable(True)
        self.revealAction.triggered.connect(self.onReveal)
        gridName.layout().addWidget(self.password, 10, 0, 1, 2)

        self.lblPassword2 = QLabel("Confirm Password *")
        gridName.layout().addWidget(self.lblPassword2, 11, 0)
        self.password2 = QLineEdit()
        self.password2.addAction(get_resource_qicon("lock.svg"), QLineEdit.LeadingPosition)
        self.password2.setEchoMode(QLineEdit.EchoMode.Password)
        self.revealAction2 = self.password2.addAction(self.iconEye, QLineEdit.ActionPosition.TrailingPosition)
        self.revealAction2.setCheckable(True)
        self.revealAction2.triggered.connect(self.onReveal2)
        gridName.layout().addWidget(self.password2, 12, 0, 1, 2)

        self.layout().addRow(self.errorMsg)

        self.addVSpace(16)

        self.pbNext = pbNext = QPushButton("Next")
        pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        pbNext.setMinimumHeight(48)
        pbNext.clicked.connect(self.onNext)
        self.layout().addRow(pbNext)

        pbNext.setContentsMargins(0, 132, 0, 32)
        pub.subscribe(self.onNewUserDetailsResponse, "check-new-user-details-response")

        # self.username.returnPressed.connect(self.firstName.setFocus)
        # self.firstName.returnPressed.connect(self.lastName.setFocus)
        # self.lastName.returnPressed.connect(self.username.setFocus)
        self.username.returnPressed.connect(self.email.setFocus)
        # self.phone.phone.returnPressed.connect(self.email.setFocus)
        self.email.returnPressed.connect(self.password.setFocus)
        self.password.returnPressed.connect(self.password2.setFocus)
        self.password2.returnPressed.connect(pbNext.setFocus)
        # pbNext.setShortcut()
        self.setButtonActivateOnEnter(pbNext, callback=self.onNext)

        vbox = QWidget()
        vbox.setLayout(QVBoxLayout())
        self.errorMsg.setParent(None)
        hbox.layout().addWidget(vbox)
        vbox.layout().addWidget(self.errorMsg)
        self.errorMsg.setAlignment(Qt.AlignmentFlag.AlignBottom)
        vbox.setFixedWidth(256)

    def initDefaults(self):
        self.username.clear()
        self.lblUsername.setText(USERNAME_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblUsername)
        self.lblEmail.clear()
        self.lblEmail.setText(EMAIL_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblEmail)
        self.lblPassword.clear()
        self.lblPassword.setText(PASSWORD_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblPassword)
        # self.firstName.setFocus()
        self.username.setFocus()

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onUsernameEdit(self, text):
        if is_valid_username(text):
            self.lblUsername.setText(USERNAME_DEFAULT_MESSAGE)
            self.hideErrorStatus()
        else:
            self.setErrorStatusMessage(USERNAME_INVALID_HINT)

    def onNext(self, event=None):
        """Validate user and password"""
        if not self.pbNext.hasFocus():
            return
        # firstName = self.firstName.text().strip()
        # self.firstName.setText(firstName.strip())
        # if not firstName:
        #     self.lblName.setText(NAME_REQUIRED)
        #     self.setWidgetError(self.lblName)
        #     return

        # lastName = self.lastName.text().strip()
        # self.lastName.setText(lastName.strip())
        # if not lastName:
        #     self.lblName.setText(NAME_REQUIRED)
        #     self.setWidgetError(self.lblName)
        #     return

        username = self.username.text()
        if not username:
            self.lblUsername.setText(USERNAME_REQUIRED)
            self.setWidgetError(self.lblUsername)
            return

        if not is_valid_username(username):
            self.lblUsername.setText(USERNAME_INVALID)
            self.setWidgetError(self.lblUsername)
            return
        
        # phone = self.phone.phone.text()
        # if not phone:
        #     self.lblPhone.setText(PHONE_REQUIRED)
        #     self.setWidgetError(self.lblPhone)
        #     return

        # if not self.phone.isValid():
        #     self.lblPhone.setText(PHONE_INVALID)
        #     self.setWidgetError(self.lblPhone)
        #     return

        email = self.email.text()
        if not email:
            self.lblEmail.setText(EMAIL_REQUIRED)
            self.setWidgetError(self.lblEmail)
            return
        if not is_valid_email_string(email):
            self.lblEmail.setText(EMAIL_INVALID)
            self.setWidgetError(self.lblEmail)
            return
        self.lblEmail.setText(EMAIL_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblEmail)

        if not self.password.text():
            self.lblPassword.setText(PASSWORD_REQUIRED)
            self.setWidgetError(self.lblPassword)
            self.setWidgetError(self.password)
            return
        # Make sure user confirms password
        if self.password.text() != self.password2.text():
            self.setWidgetError(self.lblPassword)
            self.lblPassword.setText(PASSWORD_MISMATCH)
            return

        self.setBusyState()
        pub.sendMessage("check-new-user-details", data=self.getData())

    def onNewUserDetailsResponse(self, response):
        self.setNormalState()
        if response.get("status") == "success":
            self.lblUsername.setText(USERNAME_DEFAULT_MESSAGE)
            self.setWidgetDefault(self.lblUsername)
            self.lblEmail.setText(EMAIL_DEFAULT_MESSAGE)
            self.setWidgetDefault(self.lblEmail)
            self.lblPassword.setText(PASSWORD_DEFAULT_MESSAGE)
            self.setWidgetDefault(self.lblPassword)
            self.hideErrorStatus()
            self.sgnSwitchTo.emit("CompanyProfileForm")

        if response.get("error"):
            self.setErrorStatusMessage(response.get("error"))

    def getData(self) -> dict:
        return {
            "username": self.username.text(),
            "email": self.email.text(),
            "password": self.password.text(),
            # "phone": self.phone.text(),
            # "firstName": self.firstName.text(),
            # "lastName": self.lastName.text(),
        }

    def showEvent(self, event: QShowEvent) -> None:
        # self.firstName.setFocus()
        self.username.setFocus()
        return super().showEvent(event)

    def onReveal(self, action):
        self.password.setEchoMode(QLineEdit.EchoMode.Normal if action else QLineEdit.EchoMode.Password)
        self.revealAction.setIcon(self.iconEyeOff if action else self.iconEye)

    def onReveal2(self, action):
        self.password2.setEchoMode(QLineEdit.EchoMode.Normal if action else QLineEdit.EchoMode.Password)
        self.revealAction2.setIcon(self.iconEyeOff if action else self.iconEye)
    
    

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    import sys
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    w = CreateAccountForm(None)
    w.show()
    w.setMinimumSize(1280, 768)
    app.exec()