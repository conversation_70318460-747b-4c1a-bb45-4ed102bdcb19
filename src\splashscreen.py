from genericpath import exists
from re import S
from PySide6.QtWidgets import QSplashScreen, QProgressBar
from PySide6.QtGui import QPixmap
from src.pyside_util import get_resource_pixmap, resource_path
from PySide6.QtCore import Qt, Signal, QPoint


class SplashScreen(QSplashScreen):

    finished = Signal()
    def __init__(self):
        self.splashPath = "FFF_Architekt Integrated Systems_LOout.jpg"
        pixmap = get_resource_pixmap(self.splashPath)
        pixmap = pixmap.scaledToWidth(600)
        super().__init__(pixmap)
        self.setStatus("Initializing...")

        self.progressBar = QProgressBar(self)
        # self.progressBar.setFixedHeight(32)
        self.progressBar.setFixedWidth(self.width())
        pos = QPoint(0, self.height() - self.progressBar.height())
        self.progressBar.move(pos)
        self.progressBar.hide()
        self.show()
        self.raise_()
        self.setEnabled(False)

    def setStatus(self, message):
        self.showMessage(message, 
                        color="white",
                        alignment=Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignTop)

    def setProgress(self, value):
        try:
            self.progressBar.show()
            self.progressBar.setValue(value)
        except:
            pass

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication, QMainWindow
    app = QApplication()
    window = QMainWindow()
    splash = SplashScreen()
    splash.show()
    splash.finish(window)
    window.show()
    app.exec()