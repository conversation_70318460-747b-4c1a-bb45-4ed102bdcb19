import pandas as pd
from value_mappings import fitting_to_category_map, material_to_abbreviation_map, scope_to_category_map, category_to_unit_map
from src.utils.logger import logger

# logger = logging.getLogger(__file__) # More comprehensive formatting already set

#############################################
#############################################
# Temporarily set the logger level to INFO
# logger.setLevel(logging.INFO)

# Create a console handler (if you haven't already)
# console_handler = logging.StreamHandler()

# Optionally, set a formatter
# formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
# console_handler.setFormatter(formatter)
#
# # Add the handler to the logger
# logger.addHandler(console_handler)

#############################################
#############################################

def map_abbreviated_material(material):
    # Mapping of materials to their abbreviated form
    material_to_abbreviation = material_to_abbreviation_map

    # Return the abbreviation for the given material
    return material_to_abbreviation.get(material, '')

def map_unit_of_measure(general_category):
    # Mapping of general categories to units of measure
    category_to_unit = category_to_unit_map

    # Return the unit of measure for the given category, default if not found
    return category_to_unit.get(general_category, category_to_unit["default"])

def extract_answer(response):
    """
    Safely extracts the classification answer from OpenAI response.

    Args:
        response (str or dict): The response from OpenAI API. It can be a plain string (GPT-3.5),
                                a JSON-formatted string (GPT-4o-mini), or already a parsed dict.

    Returns:
        str: The extracted answer as a clean string. Returns an empty string if parsing fails.
    """
    try:
        # If already a dictionary, get the 'answer' directly
        if isinstance(response, dict):
            answer = response.get("answer", "")
        # If it's a string, attempt JSON parsing
        elif isinstance(response, str):
            try:
                parsed_response = json.loads(response)
                # If parsed successfully and is dict, extract 'answer'
                if isinstance(parsed_response, dict):
                    answer = parsed_response.get("answer", "")
                else:
                    # JSON parsed but unexpected format
                    answer = response
            except json.JSONDecodeError:
                # JSON parsing failed, assume plain string format
                answer = response
        else:
            logger.error(f"Unexpected response type: {type(response)}")
            return ''

        # Clean the answer: remove extra single quotes and whitespace
        clean_answer = answer.strip("'").strip()
        return clean_answer

    except Exception as e:
        # Log unexpected exceptions clearly
        logger.exception(f"Error extracting answer from response '{response}': {e}")
        return ''

def classify_general_items(row):
    # Define classification based on 'rfq_scope'
    scope_to_category = scope_to_category_map
      #"Valves": "Valves" <-- Valves handled elsewhere

    def fitting_ef(fitting_type):
        pass
    
    # If rfq_scope = "FITTINGS"
    def classify_fittings(fitting_category):
        fitting_to_category = fitting_to_category_map
        # Extract answer safely
        fitting_category_str = extract_answer(fitting_category)

        # Now safely use the string as dictionary key
        return fitting_to_category.get(fitting_category_str, '')
    
        # # If the fitting type is not in the mapping, return an empty string
        # return fitting_to_category.get(fitting_category, '')
    
    def classify_valves(valve_type, ends):
        logger.info(f"Classify Valves: {valve_type} with ends {valve_type}")
        # Define the list of flanged and welded end types
        flanged_ends = ["flg", "bexbe", "bexsw", "bexpe", "bexte", "swxbe", "pexbe", "texbe"]
        welded_ends = ["be", "sw", "swxsw", "swxpe", "swxte", "pe", "pexpe", "pexsw", "texsw", "texpe", "bl", "ce", "cr", "fe", "gr", "me"] # "swexswe"
        
        # These are typically welded valves
        welded_valve_list = ["Butterfly Valve", "Wafer Check Valve", "Needle Valve"]
        
        # These are typically Flanged Valves
        flanged_valve_list = ["Angle Valve", "Ball Check Valve", "Ball Valve", "Check Valve", "Control Valve", 
                                "Cryogenic Valve", "Diaphragm Valve", "Float Valve", "Foot Valve", "Gate Valve", 
                                "Globe Valve", "Pinch Valve", "Piston Valve", "Plug Valve", "Pressure Reducing Valve", 
                                "Regulating Valve", "Relief Valve", "Solenoid Valve", "Steam Trap", "Thermostatic Valve", 
                                "Three-way Valve", "Y-type Valve", "Other"]

        # Normalize the 'ends' input to lower case and remove spaces
        normalized_ends = str(ends).replace(" ", "").lower()
        
        # Handle when ends are not specified
        if normalized_ends in ['nan', 'null', '']:
            if valve_type in welded_valve_list:
                # For these, we have a specific default
                return "Flanged Valve" if valve_type in ["Butterfly Valve", "Wafer Check Valve"] else "Welded Valve"
            elif valve_type in flanged_valve_list:
                # If the valve type can be either and 'ends' is unspecified, assume flanged
                return "Flanged Valve"
    
        # If 'ends' is specified, determine the classification based on end type
        if normalized_ends in flanged_ends:
            return "Flanged Valve"
        elif normalized_ends in welded_ends:
            return "Welded Valve"
        else:
            logger.warning(f"End type: {ends} is not in the flanged_ends or welded_ends list")
            pass
        
        # Ends were specified but were not in the list and none of the criteria above were met. Return a default value based on valve type (Fallback Method-Ends should always correlate to something)
        if valve_type in flanged_valve_list:
            return "Flanged Valve"
        if valve_type in welded_valve_list:
            return "Welded Valve"

        # If the valve type or 'ends' does not match any category, return None
        logger.warning(f"Valve Type: {valve_type} and end type: {ends} did not meet any criteria and were not classified")
        return ""
    
    if row['rfq_scope'] in scope_to_category:
        return scope_to_category[row['rfq_scope']]
    elif row['rfq_scope'] == 'Fittings':
        return classify_fittings(row['fitting_category'])
    elif row['rfq_scope'] == 'Valves':
        return classify_valves(row['valve_type'], row['ends'])
    else:
        return ''  # Default case

# # Simulate receiving a dataframe
# final_df = pd.read_excel('final_merged_output_gpt4.xlsx')

# # --> Apply the classification function to categorize fittings, flanges, valves, etc.
# final_df['general_category'] = final_df.apply(classify_general_items, axis=1)

# # --> Get unit of measure
# final_df['unit_of_measure'] = final_df['general_category'].apply(map_unit_of_measure)

# # --> Get Abbreviated Material
# final_df['abbreviated_material'] = final_df['material'].apply(map_abbreviated_material)

# # Save the final merged DataFrame to an Excel file
# final_excel_filename = 'final_w_general.xlsx'
# final_df.to_excel(final_excel_filename, index=False)
# print(f"Final merged DataFrame is exported to Excel file {final_excel_filename} successfully.")
