"""
Test Infrastructure Setup

This script tests that the LangGraph infrastructure is properly set up
and can create configurations and handlers without requiring API calls.
"""

import sys
import os
from pathlib import Path

# Add the parent directories to the path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent))
sys.path.insert(0, str(current_dir.parent.parent))

try:
    from integration_layer import (
        create_default_langgraph_config,
        create_stage1_only_config,
        LangGraphModelHandler,
        ModelType,
        EXISTING_INFRASTRUCTURE_AVAILABLE
    )
    from state_models import create_initial_state
    from classification_nodes import material_analysis_node
    print("✅ Successfully imported LangGraph modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)


def test_infrastructure_setup():
    """Test that the infrastructure is properly set up"""
    
    print("🔧 Testing Infrastructure Setup")
    print("=" * 50)
    
    # Test 1: Check if existing infrastructure is available
    print(f"📋 Existing infrastructure available: {EXISTING_INFRASTRUCTURE_AVAILABLE}")
    
    # Test 2: Create configurations
    try:
        config = create_default_langgraph_config(debug_mode=True)
        print("✅ Default configuration created successfully")
        print(f"   Material analysis model: {config.material_analysis_config.model_type.value}")
        print(f"   Fitting classification model: {config.fitting_classification_config.model_type.value}")
    except Exception as e:
        print(f"❌ Failed to create default configuration: {e}")
        return False
    
    # Test 3: Create Stage 1 only configuration
    try:
        stage1_config = create_stage1_only_config(
            model_type=ModelType.GEMINI_20_FLASH,
            debug_mode=True
        )
        print("✅ Stage 1 configuration created successfully")
        print(f"   Model: {stage1_config.material_analysis_config.model_type.value}")
        print(f"   Temperature: {stage1_config.material_analysis_config.temperature}")
    except Exception as e:
        print(f"❌ Failed to create Stage 1 configuration: {e}")
        return False
    
    # Test 4: Create initial state
    try:
        state = create_initial_state(
            item_id="test_001",
            material_description="45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9",
            original_classification={},
            debug_mode=True
        )
        print("✅ Initial state created successfully")
        print(f"   Item ID: {state['item_id']}")
        print(f"   Current stage: {state['current_stage']}")
        print(f"   Workflow path: {state['workflow_path']}")
    except Exception as e:
        print(f"❌ Failed to create initial state: {e}")
        return False
    
    # Test 5: Try to create model handler (this should fail without API key but gracefully)
    if EXISTING_INFRASTRUCTURE_AVAILABLE:
        try:
            # This should fail because we don't have an API key, but it should fail gracefully
            handler = LangGraphModelHandler(stage1_config)
            print("⚠️  Model handler created (unexpected - should fail without API key)")
        except RuntimeError as e:
            if "Cannot proceed without actual model integration" in str(e):
                print("✅ Model handler correctly requires API key")
            else:
                print(f"❌ Unexpected error creating model handler: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error creating model handler: {e}")
            return False
    else:
        print("⚠️  Existing infrastructure not available - using mock implementations")
    
    return True


def test_model_types():
    """Test that model types are properly defined"""
    
    print("\n🤖 Testing Model Types")
    print("=" * 30)
    
    try:
        # Test all model types
        models = [
            ModelType.GEMINI_20_FLASH,
            ModelType.GEMINI_25_FLASH,
            ModelType.GEMINI_25_PRO
        ]
        
        for model in models:
            print(f"✅ {model.name}: {model.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing model types: {e}")
        return False


def test_configuration_validation():
    """Test configuration validation"""
    
    print("\n⚙️  Testing Configuration Validation")
    print("=" * 40)
    
    try:
        # Test with different model types
        for model_type in [ModelType.GEMINI_20_FLASH, ModelType.GEMINI_25_FLASH]:
            config = create_stage1_only_config(
                model_type=model_type,
                debug_mode=True
            )
            
            # Validate configuration
            assert config.material_analysis_config.model_type == model_type
            assert config.material_analysis_config.temperature == 0.0
            assert config.debug_mode == True
            
            print(f"✅ Configuration valid for {model_type.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


def main():
    """Main test function"""
    
    print("LangGraph Infrastructure Test")
    print("=" * 50)
    
    tests = [
        ("Infrastructure Setup", test_infrastructure_setup),
        ("Model Types", test_model_types),
        ("Configuration Validation", test_configuration_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n📊 SUMMARY")
    print(f"=" * 20)
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total:.1%}")
    
    if passed == total:
        print("\n🎉 All infrastructure tests passed!")
        print("The system is ready for real model integration.")
        print("Next step: Set GOOGLE_API_KEY environment variable and test with real models.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the infrastructure setup.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
