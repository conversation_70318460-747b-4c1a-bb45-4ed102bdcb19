from venv import logger
from PySide6.QtWidgets import QWidget, QStackedWidget
from PySide6.QtGui import QMouseEvent
from PySide6.QtCore import Signal
from .uploadqueueview import UploadQueueView
from .documentsview import DocumentsView
from .projectview import ProjectView
from src.views.formsview import TokenPaymentForm
# from .loadingview import LoadingView
from pubsub import pub
from importlib.util import find_spec

# For certain builds, we exclude source, so detect
# which additional views we can add
_available = {
    "ProjectView": ProjectView,
}

PlaceholderView = None
if find_spec("src.views.placeholderview"):
    from .placeholderview import PlaceholderView


class WorkspaceView(QStackedWidget):

    setWorkspaceView = Signal(str)

    def __init__(self, parent):
        super().__init__(parent)
        self.views = {
            # "None": QWidget(self), # TODO - decide on default widget to show
            "ProjectView": ProjectView(self),
            "Projects": None,
            "Notepad": None,
            "Construction Calculator": None,
            "Dashboard": None,
            "Upload Queue": UploadQueueView(self),
            "Document Library": DocumentsView(self),
            "Manage Account": None,
            # "LoadingView": LoadingView(self),
        }
        for name, view in self.views.items():
            if not view:
                if not PlaceholderView:
                    continue
                view = PlaceholderView(self, name=name)
                self.views[name] = view
            self.addWidget(view)

        pub.subscribe(self.switchToView, "goto-workspace-view")
        self.setWorkspaceView.connect(self.onSetWorkspaceView)

        self.setObjectName("secondaryBackground")

    def switchToView(self, name):
        self.setWorkspaceView.emit(name)

    def onSetWorkspaceView(self, name):
        if name == "TokenPaymentForm":
            view = self.views.get("TokenPaymentForm")
            if not view:
                self.views["TokenPaymentForm"] = view = TokenPaymentForm(self)
                self.addWidget(view)
                view.hideEvent = self.onTokenPaymentClosed

        for k, view in self.views.items():
            try:
                # view.setEnabled(k == name)
                pass # TODO - recheck this but should be no need to disable view so comment out?
            except Exception:
                logger.debug("Should be None view i.e. excluded from source such as trial build")
        self.setCurrentWidget(self.views.get(name))

    def mousePressEvent(self, event: QMouseEvent) -> None:
        if event.button(): # == Qt.MouseButton.LeftButton:
            pub.sendMessage("toolbar-hide-popups")

    def getView(self, name: str):
        return self.views.get(name)

    def onTokenPaymentClosed(self, event):
        self.views["TokenPaymentForm"].hide()
        self.views["TokenPaymentForm"].setParent(None)
        self.views["TokenPaymentForm"].destroy()
        del self.views["TokenPaymentForm"]