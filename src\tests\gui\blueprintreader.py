
import os
import sys
import time
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *

sys.path[0] = ""  # For relative resource paths

import __features__
import __version__
# from src.splashscreen import SplashScreen
# from src.mainwindow import MainWindow
from src.theme import stylesheet, initFonts
# from src.config.workspace import Workspace
from src.app_paths import getDataTempPath, resource_path

from src.views.blueprintreaderview import BlueprintReaderView


if __name__ == "__main__":

    app = QApplication(sys.argv)
    initFonts(app)
    app.setStyleSheet(stylesheet)
    # window = MainWindow()
    # workspace = None
    # workspace = Workspace()
    bp = BlueprintReaderView(None)
    bp.setContentsMargins(0, 0, 0, 0)
    bp.layout().setContentsMargins(0, 0, 0, 0)
    bp.resize(QSize(800,600))

    from src.atom.dbManager import DatabaseManager

    project_id = 24
    db = DatabaseManager()
    sources = []
    for p in db.get_project_sources(project_id):
        projectId, filename, documentVendor, dataAdded, sortNumber = p
        sources.append({
            "dateAdded": dataAdded,
            "projectId": projectId,
            "filename": filename,
            "documentVendor": documentVendor,
            "sortNumber": sortNumber,
        })
    # print(project_data)
    # exit()
    bp._projectData = {
        "documents": sources,
        'id': project_id,
    }
    bp.syncProjectData(bp._projectData)
    bp.show()
    app.exec()