

'''
Determine Line Segments
Tasks:
- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can ensure isometric items are detected prior to running extraction
A. Classify objects
    1. Identify Isometric Sketch area
        - Filter vectors to identify possible vector lines
        Criteria: Isometrics are drawn at the angles: Horizontal [30, 210], [330, 150], Vertical: [90, 270] 
        Considerations: Handle odd angle segments that do not follow the usual angeles. Need a sample to identify the cosine area
            - Link similar conditions, i.e line width     
    
    2. Identify BOM label boxes:
        -Criteria: Small Square containing a number, series of numbers, or letter & number where a number corresponds back to a BOM Item
        - Vertical[90, 270], Actual Left Right [0, 180]
        
    3. Identify pointer lines
        - Criteria: Lines that touch a known BOM label item and intersect or almost intersect a portion of the isometric. 
        
    4. Link isometric segments, pointer lines, and BOM label
    
    5. Identify point of size change:
        - Look for a measurement using regex expressions to manually separate a size change point
        - Look for reducer symbols and pipe tees for hints

        
    Current implementation logic:
    
    
    Colors:
    Green: (0, 1, 0)
    Red: (1, 0, 0)
    Blue: (0, 0, 1)
    Yellow: (1, 1, 0)
    Cyan: (0, 1, 1)
    Magenta: (1, 0, 1)
    Orange: (1, 0.5, 0)
    Purple: (0.5, 0, 0.5)
    Pink: (1, 0.75, 0.8)
    Lime: (0.5, 1, 0)
    Teal: (0, 0.5, 0.5)
    Brown: (0.6, 0.3, 0)
    Navy: (0, 0, 0.5)
    Olive: (0.5, 0.5, 0)
    Maroon: (0.5, 0, 0)
        
'''

'''
Workflow logic:
Outstanding:
- Number all classified items internally for linking

- Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can 
  ensure isometric items are detected prior to running extraction
  
- Get continuation piping lines not detected by the initital isometric detection

- Draw anything in the isometric gaps/path and label as "Pipe Component". 

- Detect Squares (Circles for some drawings)

- Get arrows and vertices

- Need pointer origin and destination stored in data for grouping

- Draw "Other" with numbers for debugging

- Use BOM item #'s for hints

- Identify measurements, size callouts, and reduced size callouts (1"x1 1/2"). Can be done with regex. Dual sizes used to create iso size breaks

- Identify measurement breaks (Perpendicular Lines signifying a specific measurment of an item, like a valve)

- Identify insulation dashes

- Identify revision triangles and clouds. Need to research this.

- Identify Weep holes (Regex pattern or key word pickout. Research alternate terms to look for)

- Handle case where the break in isometric is because the line is running behind another pipe segment and is not really a break (Page 4 of "Weld Recognition Test.pdf)


* Purpose: Isolate piping components like pipe line segments, measurement lines, pointers, etc., to assist in relating items to their actual location on the isometric drawing.
* Input: PDF file containing isometric piping drawings.
* Output: 
  1. A new PDF file with color-coded line segments.
  2. An Excel file with detailed information about each line segment.

Steps:
1. Open the PDF and extract vector graphics (drawings) from each page. (Function: process_drawings)
2. For each line in the drawings: (Function: process_drawings)
   a. Calculate its angle and length. (Functions: calculate_angle, LineString.length)
   b. Determine if it's an isometric angle. (Function: is_isometric_angle)
   c. Store its properties (coordinates, width, etc.).
3. Determine the two most significant line widths on each page. (Function: determine_significant_widths)
   - Purpose: Identify potential pipe and measurement line widths.
4. Classify lines based on width and isometric property: (Function: process_drawings)
   a. Thicker isometric lines are classified as 'Pipe'.
   b. Other lines are initially classified as 'Other'.
5. Reclassify 'Other' lines: (Function: process_drawings)
   a. Check if they're parallel to pipes. (Function: is_parallel_to_pipe)
      If yes, classify as 'Measurement'.
   b. Check if they're pointing to pipes. (Function: is_pointer_line)
      If yes, classify as 'Pointer'.
6. Generate output PDF with color-coded lines and Excel file with line data. (Functions: output_isometric_lines_to_pdf, DataFrame.to_excel)

Key Functions:
- calculate_angle: Computes the angle of a line.
- is_isometric_angle: Checks if an angle is close to standard isometric angles.
- determine_significant_widths: Finds the two most common line widths for pipes and measurements.
- is_parallel_to_pipe: Checks if a line runs parallel to a pipe.
- is_pointer_line: Identifies lines pointing to pipes (likely annotations or labels).
- process_drawings: Main function that orchestrates the entire workflow.
- output_isometric_lines_to_pdf: Generates the color-coded PDF output.
- main: Calls process_drawings and handles the overall execution flow.

Adjustable Parameters:
- angle_tolerance (in is_isometric_angle): Tolerance for considering an angle as isometric. 
  Increasing it will classify more lines as isometric, potentially including non-standard angles.
- distance_threshold (in is_pointer_line): Maximum distance for a line to be considered close to a pipe. 
  Increasing it will detect pointers that are further from pipes, but may increase false positives.
- min_length (in process_drawings): Minimum length for a line to be considered. 
  Decreasing it will include shorter lines, potentially increasing noise in the detection.
- tolerance (in determine_significant_widths): Tolerance for grouping similar line widths. 
  Increasing it will group more varied line widths together, potentially misclassifying some lines.

Color Coding in Output PDF:
- Red: Pipe lines
- Brown: Measurement lines
- Blue: Pointer lines
- Orange: Other unclassified lines

Note: Adjusting these parameters may require fine-tuning based on the specific characteristics of the input drawings.
'''

import fitz, os, math, re, time
from fitz.utils import getColor
import pandas as pd
import numpy as np
from shapely.geometry import Point, LineString, box, Polygon
from src.utils.logger import logger
from collections import defaultdict
from scipy.spatial import distance

# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)

##### NEEDS WORK v
def is_arrow_tip(vector_group):
    items = vector_group
    logger.debug(f"Checking vector group with {len(items)} items")
    
    if len(items) < 2 or any(item['item_type'] != 'l' for item in items):
        logger.debug("Not an arrow tip: doesn't have at least 2 line items")
        return False

    lines = [item['geometry'] for item in items]
    
    # Collect all endpoints
    endpoints = set()
    for line in lines:
        endpoints.update(line.coords)
    
    # Check if lines form a connected structure
    connected = False
    for i, line1 in enumerate(lines):
        for line2 in lines[i+1:]:
            if set(line1.coords) & set(line2.coords):
                connected = True
                break
        if connected:
            break
    
    if not connected:
        logger.debug("Not an arrow tip: lines are not connected")
        return False
    
    # Check if the structure forms a point (one end should have multiple lines connecting)
    endpoint_counts = defaultdict(int)
    for endpoint in endpoints:
        endpoint_counts[endpoint] = sum(endpoint in line.coords for line in lines)
    
    max_connections = max(endpoint_counts.values())
    
    logger.debug(f"Max connections at an endpoint: {max_connections}")
    
    # We consider it an arrow tip if at least one point connects multiple lines
    return max_connections >= 2

def detect_arrow_tips(page_items):
    arrow_tips = []
    vector_groups = defaultdict(list)
    measurement_pointer_lines = [item for item in page_items if item['det_cat_1'] in ['Measurement', 'Pointer']]
    logger.debug(f"Found {len(measurement_pointer_lines)} Measurement/Pointer lines")
    
    # Group items by vector_id
    for item in page_items:
        vector_groups[item['vector_id']].append(item)
    
    for vector_id, group in vector_groups.items():
        if all(item['det_cat_1'] == 'Other' for item in group):
            logger.debug(f"Checking vector group {vector_id}: {[item['geometry'] for item in group]}")
            if is_arrow_tip(group):
                logger.debug(f"Vector group {vector_id} is a potential arrow tip")
                # Check if this arrow tip is at the end of a Measurement or Pointer line
                arrow_points = set([coord for item in group for coord in item['geometry'].coords])
                for line in measurement_pointer_lines:
                    line_end = Point(line['geometry'].coords[-1])
                    for arrow_point in arrow_points:
                        distance = Point(arrow_point).distance(line_end)
                        #logger.debug(f"Distance to line end: {distance}")
                        if distance < 1:  # Adjust this threshold as needed
                            logger.debug(f"Arrow tip found in vector group {vector_id}")
                            arrow_tips.extend([i for i, item in enumerate(page_items) if item['vector_id'] == vector_id])
                            break
                    if any(page_items[i]['vector_id'] == vector_id for i in arrow_tips):
                        break
    
    logger.debug(f"Total arrow tips found: {len(arrow_tips)}")
    return arrow_tips

# ----> Classsify rectangles
def detect_rectangles(page_items, tolerance=5, rectangularity_threshold=0.98):
    def are_connected(line1, line2):
        return any(distance.euclidean(p1, p2) < tolerance 
                   for p1 in [line1.coords[0], line1.coords[-1]] 
                   for p2 in [line2.coords[0], line2.coords[-1]])

    def is_rectangle(poly):
        if poly.is_valid and poly.area > 0:
            rectangularity = poly.area / poly.minimum_rotated_rectangle.area
            return rectangularity > rectangularity_threshold
        return False

    potential_items = [item for item in page_items if item['det_cat_1'] != 'Pipe' and item['item_type'] == 'l']
    
    rectangles = []
    processed = set()

    for i, item in enumerate(potential_items):
        if i in processed:
            continue
        
        connected_lines = [item['geometry']]
        connected_items = [item]
        processed.add(i)

        # Find connected lines
        for j, other_item in enumerate(potential_items):
            if j in processed:
                continue
            if are_connected(item['geometry'], other_item['geometry']):
                connected_lines.append(other_item['geometry'])
                connected_items.append(other_item)
                processed.add(j)

        # Try to form a polygon from the connected lines
        if len(connected_lines) >= 3:
            points = []
            for line in connected_lines:
                points.extend(line.coords)
            unique_points = list(set(points))
            
            if len(unique_points) >= 4:
                try:
                    poly = Polygon(unique_points)
                    if is_rectangle(poly):
                        rectangles.append(connected_items)
                except ValueError:
                    # If we can't form a valid polygon, skip this set of lines
                    pass

    return rectangles

# ----> Classsify rectangles

##### NEEDS WORK ^

def get_finish_params(path, color=None):
    if color is None:
        c = path.get("color")
    elif isinstance(color, tuple):
        c = color  # If color is already a tuple, use it directly
    else:
        c = getColor(color)  # This will handle string color names
    
    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),
        "fill": path.get("fill", None),
        "color": c,
        "dashes": path["dashes"] if path.get("dashes") is not None else None,
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
        "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,
        "width": path["width"] if path.get("width") is not None else None
    }

def draw_path(shape, path):
    for item in path["items"]:
        if item[0] == "l":  # line
            shape.draw_line(item[1], item[2])
        elif item[0] == "re":  # rectangle
            shape.draw_rect(item[1])
        elif item[0] == "qu":  # quad
            shape.draw_quad(item[1])
        elif item[0] == "c":  # curve
            shape.draw_bezier(item[1], item[2], item[3], item[4])
        else:
            print(f"Unhandled path type: {item[0]}")
            
def output_isometric_lines_to_pdf(df, input_filepath, output_filepath):
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    # Group the DataFrame by page
    grouped = df.groupby('page')

    for page_num, group in grouped:
        print(f"Processing page {page_num}")

        # Convert page_num to integer and subtract 1
        input_page_num = int(page_num) - 1
        
        # Create a new page with the same dimensions as the input PDF
        input_page = doc[input_page_num] #doc[page_num - 1]  # Subtract 1 because page numbers in df start from 1
        outpage = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
        shape = outpage.new_shape()

        # Draw all items for this page
        items_drawn = 0
        for _, item in group.iterrows():
            try:
                geometry = item['geometry']
                
                # Set color based on classification
                if item['det_cat_1'] == 'Pipe':
                    color = (1, 0, 0) # Red
                elif item['det_cat_1'] == 'Measurement':
                    color = (0.6, 0.3, 0) # Brown
                elif item['det_cat_1'] == 'Pointer':
                    color = (0, 0, 1)  # Blue
                elif item['det_cat_1'] == 'Text Callout':
                    color = (0.5, 0, 0.5)  # Purple
                elif item['det_cat_1'] == 'Weld Map':
                    color = (1, 0, 1) # Magenta
                elif item['det_cat_1'] == 'BOM Label':
                    color = (0, 1, 1) # Cyan
                elif item['det_cat_1'] == 'Size Label':
                    color = (0.5, 1, 0) # Lime
                elif item['det_cat_1'] == 'Size Break Label':
                    color = (1, 0, 0) # Red
                elif item['det_cat_1'] == 'Length Label':
                    color = (0.6, 0.3, 0)
                else:
                    color = (1, 0.5, 0) # Orange
                    

                    '''
                    Colors:
                        Green: (0, 1, 0)
                        Red: (1, 0, 0)
                        Blue: (0, 0, 1)
                        Yellow: (1, 1, 0)
                        Cyan: (0, 1, 1)
                        Magenta: (1, 0, 1)
                        Orange: (1, 0.5, 0)
                        Purple: (0.5, 0, 0.5)
                        Pink: (1, 0.75, 0.8)
                        Lime: (0.5, 1, 0)
                        Teal: (0, 0.5, 0.5)
                        Brown: (0.6, 0.3, 0)
                        Navy: (0, 0, 0.5)
                        Olive: (0.5, 0.5, 0)
                        Maroon: (0.5, 0, 0)
                    '''

                if item['det_cat_1'] in ['Weld Map', 'BOM Label', 'Size Label', 'Size Break Label', 'Length Label']:
                    # Draw bounding box for Weld Map and BOM Label
                    x0, y0, x1, y1 = item['coordinates']
                    rect = fitz.Rect(x0, y0, x1, y1)
                    shape.draw_rect(rect)
                    shape.finish(color=color, width=2)
                    
                    # Add label text for BOM Label
                    if item['det_cat_1'] == 'BOM Label' and 'value' in item:
                        shape.insert_text(rect.tl, str(item['value']), fontsize=8, color=color)
                else:
                
                    if item['item_type'] == 'l':
                        if len(geometry.coords) < 2:
                            print(f"Warning: Line with less than 2 coordinates: {geometry}")
                            continue
                        start = fitz.Point(geometry.coords[0][0], geometry.coords[0][1])
                        end = fitz.Point(geometry.coords[-1][0], geometry.coords[-1][1])
                        shape.draw_line(start, end)
                    elif item['item_type'] == 're':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Rectangle with less than 4 coordinates: {coords}")
                            continue
                        rect = fitz.Rect(coords[0][0], coords[0][1], coords[2][0], coords[2][1])
                        shape.draw_rect(rect)
                    elif item['item_type'] == 'qu':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Quad with less than 4 coordinates: {coords}")
                            continue
                        quad_points = [fitz.Point(coords[i][0], coords[i][1]) for i in range(4)]
                        shape.draw_quad(fitz.Quad(quad_points))
                    elif item['item_type'] == 'c':
                        points = [fitz.Point(x, y) for x, y in geometry.coords]
                        if len(points) < 2:
                            print(f"Warning: Curve with less than 2 points: {points}")
                            continue
                        elif len(points) == 2:
                            # If only 2 points, draw a line
                            shape.draw_line(points[0], points[1])
                        elif len(points) == 3:
                            # If 3 points, use the middle point as both control points
                            shape.draw_bezier(points[0], points[1], points[1], points[2])
                        else:
                            # If 4 or more points, use the first and last as endpoints and the middle two as control points
                            shape.draw_bezier(points[0], points[1], points[-2], points[-1])
                    else:
                        print(f"Unhandled item type: {item['item_type']}")
                        continue

                    finish_params = get_finish_params(item['drawing'])
                    finish_params['color'] = color
                    shape.finish(**finish_params)

                # Draw item number and rectangle if available
                if 'item_number' in item and pd.notna(item['item_number']):
                    if 'item_coordinates' in item and pd.notna(item['item_coordinates']):
                        item_coords = eval(item['item_coordinates'])
                        rect = fitz.Rect(item_coords[0], item_coords[1], item_coords[2], item_coords[3])
                        shape.draw_rect(rect)
                        shape.insert_text(rect.tl, str(item['item_number']), fontsize=8, color=(0, 0, 0))
                    
                items_drawn += 1
            except Exception as e:
                print(f"Error processing item: {item}")
                print(f"Error message: {str(e)}")

        shape.commit()

    print(f"Saving PDF to {output_filepath}")
    outpdf.save(output_filepath)
    print("PDF saved. Closing files...")
    outpdf.close()
    doc.close()
    print(f"PDF output process completed. Total items drawn: {items_drawn}")
    
def is_within_area(bounds, area):
    """Check if an item's bounding box is within the specified area."""
    x_min, y_min, x_max, y_max = bounds
    return (area['x0'] <= x_min <= area['x1'] and
            area['y0'] <= y_min <= area['y1'] and
            area['x0'] <= x_max <= area['x1'] and
            area['y0'] <= y_max <= area['y1'])

def calculate_angle(line):
    start, end = line.coords
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]

    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)

def is_pointer_line(item, items_to_check, angle_tolerance=20, distance_threshold=10, debug_func=False):
    '''
    Identifies "pointers", which point from descriptors/labels to items on the pipe or measurement lines.
    '''
    if item['item_type'] != 'l':
        return False

    line_geom = item['geometry']
    line_angle = item['angle']
    start_point = Point(line_geom.coords[0])
    end_point = Point(line_geom.coords[-1])
    
    for target_item in items_to_check:
        if target_item['item_type'] != 'l':
            continue

        if 'det_cat_1' in target_item and target_item['det_cat_1'] not in ['Pipe', 'Potential_Pipe', 'Measurement']:
            continue

        target_geom = target_item['geometry']
        target_angle = target_item['angle']
        target_start = Point(target_geom.coords[0])
        target_end = Point(target_geom.coords[-1])
        
        # Check if one end of the line is close to the target item
        start_to_target_start = start_point.distance(target_start)
        start_to_target_end = start_point.distance(target_end)
        end_to_target_start = end_point.distance(target_start)
        end_to_target_end = end_point.distance(target_end)
        
        if (start_to_target_start <= distance_threshold or
            start_to_target_end <= distance_threshold or
            end_to_target_start <= distance_threshold or
            end_to_target_end <= distance_threshold):
            
            if debug_func:
                print(f"Line close to {target_item.get('det_cat_1', 'pipe')}. Distances: {start_to_target_start}, {start_to_target_end}, {end_to_target_start}, {end_to_target_end}")
            
            # Check if the line angle is significantly different from the target angle
            angle_diff = min(abs(line_angle - target_angle), 360 - abs(line_angle - target_angle))
            if debug_func:
                print(f"Line angle: {line_angle}, Target angle: {target_angle}, Angle difference: {angle_diff}")
            
            if angle_diff > angle_tolerance:
                if debug_func:
                    print(f"Pointer line detected pointing to {target_item.get('det_cat_1', 'pipe')}!")
                return True
            else:
                if debug_func:
                    print(f"Line close to {target_item.get('det_cat_1', 'pipe')} but angle difference too small.")
    
    return False

def relate_pointers_to_items(df):
    pointer_items = df[df['det_cat_1'] == 'Pointer']
    target_items = df[df['det_cat_1'].isin(['Pipe', 'Potential_Pipe', 'Measurement', 'BOM Label', 'Size Label', 'Size Break Label', 'Length Label'])]
    
    for _, pointer in pointer_items.iterrows():
        pointer_geom = pointer['geometry']
        pointer_start = Point(pointer_geom.coords[0])
        pointer_end = Point(pointer_geom.coords[-1])
        
        closest_target = None
        min_distance = float('inf')
        
        for _, target in target_items.iterrows():
            target_geom = target['geometry']
            
            if isinstance(target_geom, LineString):
                target_points = [Point(target_geom.coords[0]), Point(target_geom.coords[-1])]
            elif isinstance(target_geom, Polygon):
                target_points = [Point(p) for p in target_geom.exterior.coords]
            else:
                continue  # Skip if geometry type is not handled
            
            for target_point in target_points:
                dist_start = pointer_start.distance(target_point)
                dist_end = pointer_end.distance(target_point)
                
                if min(dist_start, dist_end) < min_distance:
                    min_distance = min(dist_start, dist_end)
                    closest_target = target
        
        # if closest_target is not None:
        #     # Create a stable identifier for the target
        #     target_identifier = f"{closest_target['page']}_{closest_target['vector_id']}_{closest_target['item_position']}"

        if closest_target is not None:
            # Create a stable identifier for the target using the create_id function
            target_identifier = create_id(closest_target['page'], closest_target['vector_id'], closest_target['item_position'])
            
            # Update the pointer item with information about its target
            df.loc[pointer.name, 'points_to'] = closest_target['det_cat_1']
            df.loc[pointer.name, 'target_id'] = target_identifier
    
    return df

# def is_parallel_to_pipe(item, pipe_items, angle_tolerance=5, distance_threshold=100, min_parallel_ratio=0.5, min_length=1e-6, debug_func=False):
#     if item['item_type'] != 'l':
#         return False

#     line_geom = item['geometry']
#     line_angle = item.get('angle')
    
#     # Check for very short lines
#     if line_geom.length < min_length:
#         if debug_func:
#             print(f"Line is too short: length = {line_geom.length}")
#         return False

#     if line_angle is None:
#         if debug_func:
#             print("Line angle is None")
#         return False

#     for pipe_item in pipe_items:
#         if pipe_item['item_type'] != 'l':
#             continue

#         pipe_geom = pipe_item['geometry']
#         pipe_angle = pipe_item.get('angle')

#         if pipe_angle is None:
#             continue

#         try:
#             # Check if angles are parallel (considering the isometric nature)
#             angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
#             if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
#                 # Check if the line runs alongside the pipe
#                 pipe_buffer = pipe_geom.buffer(distance_threshold)
#                 parallel_part = line_geom.intersection(pipe_buffer)
                
#                 if line_geom.length > 0:
#                     parallel_ratio = parallel_part.length / line_geom.length
#                     if debug_func:
#                         print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Parallel ratio: {parallel_ratio}")
#                     if parallel_ratio >= min_parallel_ratio:
#                         return True
#                 else:
#                     if debug_func:
#                         print(f"Line has zero length: {line_geom}")
#         except Exception as e:
#             if debug_func:
#                 print(f"Error in parallel check: {str(e)}")
    
#     return False

def is_parallel_to_pipe(item, pipe_items, angle_tolerance=5, distance_threshold=100, min_parallel_ratio=0.5, min_length=1e-6, debug_func=False):
    if item['item_type'] != 'l':
        return False, []

    line_geom = item['geometry']
    line_angle = item.get('angle')
    
    # Check for very short lines
    if line_geom.length < min_length:
        if debug_func:
            print(f"Line is too short: length = {line_geom.length}")
        return False, []

    if line_angle is None:
        if debug_func:
            print("Line angle is None")
        return False, []

    parallel_pipes = []

    for pipe_item in pipe_items:
        if pipe_item['item_type'] != 'l':
            continue

        pipe_geom = pipe_item['geometry']
        pipe_angle = pipe_item.get('angle')

        if pipe_angle is None:
            continue

        try:
            # Check if angles are parallel (considering the isometric nature)
            angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
            if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
                # Check if the line runs alongside the pipe
                pipe_buffer = pipe_geom.buffer(distance_threshold)
                parallel_part = line_geom.intersection(pipe_buffer)
                
                if line_geom.length > 0:
                    parallel_ratio = parallel_part.length / line_geom.length
                    if debug_func:
                        print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Parallel ratio: {parallel_ratio}")
                    if parallel_ratio >= min_parallel_ratio:
                        pipe_id = create_id(pipe_item['page'], pipe_item['vector_id'], pipe_item['item_position'])
                        parallel_pipes.append(pipe_id)
                else:
                    if debug_func:
                        print(f"Line has zero length: {line_geom}")
        except Exception as e:
            if debug_func:
                print(f"Error in parallel check: {str(e)}")
    
    return len(parallel_pipes) > 0, parallel_pipes



def determine_significant_widths(df, tolerance=0.01, min_length=10):
    '''
    Considers only lines ('l' type) determined to be isometric angles to find the line_widths of the largest lengths. 
    Narrows down to the top 2 sizes. The larger size is assumed as the isometric pipe.
    '''
    valid_widths = df[(df['line_width'].notna()) & 
                      (df['line_width'] > 0) & 
                      (df['is_isometric'] == True) & 
                      (df['item_type'] == 'l') &
                      (df['length'] >= min_length)]
    
    if valid_widths.empty:
        print("Warning: No valid widths found for determining significant widths.")
        return None, None

    width_groups = valid_widths.groupby('line_width')['length'].sum().sort_values(ascending=False)
    
    significant_widths = []
    for width, total_length in width_groups.items():
        if not significant_widths or abs(width - significant_widths[0]) > tolerance:
            significant_widths.append(width)
            if len(significant_widths) == 2:
                break
    
    if len(significant_widths) < 2:
        print(f"Warning: Only found {len(significant_widths)} significant width(s).")
        return tuple(significant_widths + [None] * (2 - len(significant_widths)))
    
    return tuple(significant_widths)


def identify_pipe_continuations(page_lines, pipe_width, tolerance=0.01):
    pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
    potential_continuations = [line for line in page_lines if line['det_cat_1'] == 'Other' and abs(line['line_width'] - pipe_width) < tolerance]
    
    def lines_are_collinear(line1, line2):
        # Check if two lines are collinear (on the same path)
        angle1 = line1['angle']
        angle2 = line2['angle']
        return abs(angle1 - angle2) < 5 or abs(abs(angle1 - angle2) - 180) < 5

    def is_potential_elbow(line, pipe1, pipe2):
        # Check if a line could be an elbow connecting two pipe segments
        return (
            (line['start_x'] == pipe1['end_x'] and line['start_y'] == pipe1['end_y'] and
             line['end_x'] == pipe2['start_x'] and line['end_y'] == pipe2['start_y']) or
            (line['start_x'] == pipe2['end_x'] and line['start_y'] == pipe2['end_y'] and
             line['end_x'] == pipe1['start_x'] and line['end_y'] == pipe1['start_y'])
        )

    for potential in potential_continuations:
        # Check if the potential continuation is collinear with any existing pipe segment
        if any(lines_are_collinear(potential, pipe) for pipe in pipe_lines):
            potential['det_cat_1'] = 'Pipe'
            potential['det_cat_2'] = 'Continuation'
            continue

        # Check if the potential continuation could be an elbow
        for i, pipe1 in enumerate(pipe_lines):
            for pipe2 in pipe_lines[i+1:]:
                if is_potential_elbow(potential, pipe1, pipe2):
                    potential['det_cat_1'] = 'Pipe'
                    potential['det_cat_2'] = 'Elbow'
                    break
            if potential['det_cat_1'] == 'Pipe':
                break

    return page_lines


def is_connected(item1, item2, tolerance=5):
    """Check if two items are connected within a small tolerance."""
    geom1 = item1['geometry']
    geom2 = item2['geometry']
    
    # For curves, check if any point is close to the other geometry
    if item1['item_type'] == 'c':
        return any(Point(p).distance(geom2) <= tolerance for p in geom1.coords)
    elif item2['item_type'] == 'c':
        return any(Point(p).distance(geom1) <= tolerance for p in geom2.coords)
    
    return geom1.distance(geom2) <= tolerance

def classify_pipes(page_items, pipe_width, measurement_width, tolerance=0.01):
    pipe_items = []
    for item in page_items:
        # Check if line_width is None or if pipe_width or measurement_width are None
        if item['line_width'] is None or pipe_width is None or measurement_width is None:
            item['det_cat_1'] = 'Other'
            #print(f"Classified as Other: {item['item_type']} (missing width information)")
            continue

        try:
            if abs(item['line_width'] - max(pipe_width, measurement_width)) < tolerance:
                if item['item_type'] == 'l' and item['is_isometric']:
                    item['det_cat_1'] = 'Pipe'
                    #print(f"Classified as Pipe: {item['item_type']} (isometric)")
                else:
                    item['det_cat_1'] = 'Potential_Pipe'
                    #print(f"Classified as Potential_Pipe: {item['item_type']}")
                pipe_items.append(item)
            else:
                item['det_cat_1'] = 'Other'
                #print(f"Classified as Other: {item['item_type']} (width mismatch)")
        except TypeError as e:
            print(f"Error processing item: {item}")
            print(f"Error message: {str(e)}")
            item['det_cat_1'] = 'Other'

    # Additional pass to connect potential pipe segments
    for item in page_items:
        if item['det_cat_1'] == 'Potential_Pipe':
            connected_pipes = [pipe for pipe in pipe_items if pipe['det_cat_1'] == 'Pipe' and is_connected(item, pipe)]
            if connected_pipes:
                item['det_cat_1'] = 'Pipe'
                #print(f"Upgraded to Pipe: {item['item_type']} (connected to {len(connected_pipes)} pipes)")
                pipe_items.append(item)

    return pipe_items





def process_drawings(filepath, rm, pages_to_process=None, isometric_area_coords=None):
    doc = fitz.open(filepath)
    
    if pages_to_process is None:
        pages_to_process = range(len(doc))
    else:
        pages_to_process = [p - 1 for p in pages_to_process if 0 < p <= len(doc)]

    all_items = []
    total_items = 0
    # classified_items = {'Pipe': 0, 'Potential_Pipe': 0, 'Measurement': 0, 'Pointer': 0, 'Other': 0}
    classified_items = {'Pipe': 0, 'Potential_Pipe': 0, 'Measurement': 0, 'Pointer': 0, 'Other': 0, 'Arrow Tip': 0}

    for page_num in pages_to_process:
        page = doc[page_num]
        drawings = page.get_drawings()
        print(f"Page {page_num + 1}: Found {len(drawings)} drawings")

        page_items = []
        for vector_id, drawing in enumerate(drawings):
            for item_position, item in enumerate(drawing['items']):
                total_items += 1
                
                item_type = item[0]
                if item_type == 'l':  # line
                    start, end = item[1], item[2]
                    geometry = LineString([(start.x, start.y), (end.x, end.y)])
                    angle = calculate_angle(geometry)
                    length = geometry.length
                    is_iso = is_isometric_angle(angle)
                elif item_type == 're':  # rectangle
                    x0, y0, x1, y1 = item[1]
                    geometry = Polygon([(x0, y0), (x1, y0), (x1, y1), (x0, y1)])
                elif item_type == 'qu':  # quad
                    points = item[1]
                    geometry = Polygon([(p.x, p.y) for p in points])
                elif item_type == 'c':  # curve
                    # For curves, we'll use a LineString approximation
                    points = item[1:]  # The control points of the curve
                    geometry = LineString([(p.x, p.y) for p in points])
                else:
                    print(f"Unhandled item type: {item_type}")
                    continue

                # Check if the item is within the isometric drawing area
                if isometric_area_coords and not is_within_area(geometry.bounds, isometric_area_coords):
                    continue

                line_width = drawing.get('width', 0)
                
                page_items.append({
                    'page': page_num + 1,
                    'vector_id': vector_id,
                    'item_position': item_position,
                    'item_type': item_type,
                    'geometry': geometry,
                    'angle': angle,
                    'length': length,
                    'is_isometric': is_iso,
                    'drawing': drawing,
                    'line_width': line_width,
                    'det_cat_1': 'Other',  # Initialize as 'Other'
                    'det_cat_2': None,
                    'det_cat_3': None,
                    'size': None,
                    'det_weight': None
                })

        # Determine significant widths for this page
        page_df = pd.DataFrame(page_items)
        pipe_width, measurement_width = determine_significant_widths(page_df)

        pipe_items = []

        # Replace the existing pipe classification logic with this:
        pipe_items = classify_pipes(page_items, pipe_width, measurement_width)
        
        # Classify measurement lines and pointer lines
        for item in page_items:
            if item['item_type'] == 'l' and item['det_cat_1'] == 'Other':
                if item['line_width'] is None or pipe_width is None or measurement_width is None:
                    print(f"Skipping classification for item due to missing width information: {item}")
                    continue

                try:
                    is_parallel, parallel_pipes = is_parallel_to_pipe(item, pipe_items, debug_func=False)
                    if abs(item['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel:
                        item['det_cat_1'] = 'Measurement'
                        item_id = create_id(item['page'], item['vector_id'], item['item_position'])
        
                        # Add relationships to RelationshipManager
                        for pipe_id in parallel_pipes:
                            rm.add_relationship(item_id, pipe_id)
        
                        # Store parallel pipe IDs in the item for reference
                        item['parallel_pipes'] = parallel_pipes
                    else:
                        # Filter items to check for pointers
                        items_to_check = [i for i in page_items if i['det_cat_1'] in ['Pipe', 'Potential_Pipe', 'Measurement']]
                        if is_pointer_line(item, items_to_check):
                            item['det_cat_1'] = 'Pointer'
                except Exception as e:
                    print(f"Error processing item: {item}")
                    print(f"Error message: {str(e)}")

                # try:
                #     if abs(item['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel_to_pipe(item, pipe_items, debug_func=False):
                #         item['det_cat_1'] = 'Measurement'
                #         #print(f"Classified as Measurement: {item['item_type']}")
                #     # elif is_pointer_line(item, all_items):
                #     #     item['det_cat_1'] = 'Pointer'
                    
                # try:
                #     if abs(item['line_width'] - min(pipe_width, measurement_width)) < 0.01:
                #         is_parallel, parallel_pipes = is_parallel_to_pipe(item, pipe_items, debug_func=False)
                #         if is_parallel:
                #             item['det_cat_1'] = 'Measurement'
                #             item_id = create_id(item['page'], item['vector_id'], item['item_position'])
                            
                #             # Add relationships to RelationshipManager
                #             for pipe_id in parallel_pipes:
                #                 rm.add_relationship(item_id, pipe_id)
                            
                #             # Store parallel pipe IDs in the item for reference
                #             item['parallel_pipes'] = parallel_pipes
                    
                    
                #     else:
                #         # Filter items to check for pointers
                #         items_to_check = [i for i in page_items if i['det_cat_1'] in ['Pipe', 'Potential_Pipe', 'Measurement']]
                #         if is_pointer_line(item, items_to_check):
                #             item['det_cat_1'] = 'Pointer'
                #         #print(f"Classified as Pointer: {item['item_type']}")
                #     #else:
                #         #print(f"Item remains classified as Other: {item['item_type']}")
                # except TypeError as e:
                #     print(f"Error processing item for measurement/pointer classification: {item}")
                #     print(f"Error message: {str(e)}")
                    
        # # After classifying pipes, measurements, and pointers:
        # rectangles = detect_rectangles(page_items)
        # print(f"Detected {len(rectangles)} potential text callout rectangles.")

        # # Mark these items in your DataFrame:
        # for rect in rectangles:
        #     for item in rect:
        #         item['det_cat_1'] = 'Text Callout'

        # Update your classification counts
        classified_items['Text Callout'] = sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout')
        classified_items['Other'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Other') == 'Other')
        classified_items['Measurement'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Measurement') == 'Measurement')
       
        # Identify Arrow Tips needs work
        # # After classifying pipes, measurements, and pointers:
        # arrow_tip_indices = detect_arrow_tips(page_items)
        
        # logger.debug(f"Arrow tips detected: {arrow_tip_indices}")



        # # Classify arrow tips
        # for index in arrow_tip_indices:
        #     old_category = page_items[index]['det_cat_1']
        #     page_items[index]['det_cat_1'] = 'Arrow Tip'
        #     classified_items['Arrow Tip'] += 1
        #     classified_items[old_category] -= 1
        
        # Identify Arrow Tips needs work

        # Count classifications
        for item in page_items:
            classified_items[item['det_cat_1']] += 1

        all_items.extend(page_items)

        print(f"Page {page_num + 1}: Found {len(pipe_items)} pipe items")
        print(f"Page {page_num + 1}: Found {sum(1 for item in page_items if item['det_cat_1'] == 'Measurement')} measurement items")

    doc.close()
    print(f"\nTotal items processed: {total_items}")
    print(f"Classified items: {classified_items}")
    print(f"Total items found: {len(all_items)}")
    return pd.DataFrame(all_items)

def load_bom_data(filepath):
    # Load BOM data from Excel or feather file
    try:
        if filepath.endswith('.feather'):
            # Use the fast_storage utility if available
            try:
                from src.atom.fast_storage import load_df_fast
                return load_df_fast(filepath)
            except ImportError:
                # Fall back to direct pandas read
                return pd.read_feather(filepath)
        else:
            return pd.read_excel(filepath)
    except Exception as e:
        print(f"Error loading BOM data: {e}")
        return pd.DataFrame()

def find_labels(raw_data_df, bom_data_df, isometric_area, page):
    labels = []
    pos_items = bom_data_df[bom_data_df['pdf_page'] == page][['pos', 'size']].to_dict('records')
    
    for _, row in raw_data_df[raw_data_df['pdf_page'] == page].iterrows():
        coords = eval(row['coordinates2'])
        if is_within_area(coords, isometric_area):
            value = str(row['value']).strip()
            matched_items = match_bom_item(value, pos_items)
            for matched_item in matched_items:
                labels.append({
                    'page': row['pdf_page'],
                    'coordinates': coords,
                    'det_cat_1': 'BOM Label',
                    'value': matched_item['pos'],
                    'size': matched_item['size']
                })
            
            # Check for Size Label, Size Break Label, and Length Label
            label_type = classify_label(value)
            if label_type:
                labels.append({
                    'page': row['pdf_page'],
                    'coordinates': coords,
                    'det_cat_1': label_type,
                    'value': value
                })
    
    return labels

def match_bom_item(value, pos_items):
    matched_items = []
    
    # Split the value by spaces to handle "F# G# B#" format
    value_parts = value.split()
    
    for part in value_parts:
        # Check for exact match
        exact_matches = [item for item in pos_items if str(item['pos']) == part]
        matched_items.extend(exact_matches)
        
        # Check for "[A-Z]#" or "[A-Z]##" format
        if re.match(r'^[A-Za-z]\d{1,2}$', part):
            # Extract the number part
            number_part = re.search(r'\d+', part).group()
            prefix_matches = [item for item in pos_items if str(item['pos']) == number_part]
            matched_items.extend(prefix_matches)
    
    return matched_items

def classify_label(value):
    # Remove spaces before NB
    value = re.sub(r'\s+NB', 'NB', value)
    
    # Check for Size Break Label
    if '"' in value and 'x' in value.lower() and 'nb' in value.lower():
        return 'Size Break Label'
    
    # Check for Size Label
    if '"' in value and 'nb' in value.lower():
        return 'Size Label'
    
    # Check for Length Label
    if ('"' in value or "'" in value) and 'nb' not in value.lower():
        return 'Length Label'
    
    return None

def exact_match(value, pos_items):
    return value in [str(item) for item in pos_items]

def prefix_match(value, pos_items):
    # Split the value into parts
    parts = re.findall(r'[A-Za-z]+|\d+', value)
    
    for item in pos_items:
        item_parts = re.findall(r'[A-Za-z]+|\d+', str(item))
        
        # Check if all parts of the item are in the value parts
        if all(part in parts for part in item_parts):
            return True
    
    return False
def load_detected_welds(filepath):
    # Load detected welds from Excel or feather file
    try:
        if filepath.endswith('.feather'):
            # Use the fast_storage utility if available
            try:
                from src.atom.fast_storage import load_df_fast
                welds_df = load_df_fast(filepath)
            except ImportError:
                # Fall back to direct pandas read
                welds_df = pd.read_feather(filepath)
        else:
            welds_df = pd.read_excel(filepath)
            
        # Convert to list of dictionaries
        welds = welds_df.to_dict('records')
        # Add detection category
        for weld in welds:
            weld['det_cat_1'] = 'Weld Map'
        return welds
    except Exception as e:
        print(f"Error loading detected welds: {e}")
        return []


def load_detected_general_items(filepath):
    # Load detected general items (like gussets) from Excel or feather file
    try:
        if filepath.endswith('.feather'):
            # Use the fast_storage utility if available
            try:
                from src.atom.fast_storage import load_df_fast
                items_df = load_df_fast(filepath)
            except ImportError:
                # Fall back to direct pandas read
                items_df = pd.read_feather(filepath)
        else:
            items_df = pd.read_excel(filepath)
            
        # Convert to list of dictionaries
        items = items_df.to_dict('records')
        # Add detection category based on object_type column
        for item in items:
            # Set the detection category based on the object_type
            # This will show up in the output with the appropriate label
            item_type = item.get('object_type', '')
            item['det_cat_1'] = f'{item_type.capitalize()} Item'
        return items
    except Exception as e:
        print(f"Error loading detected general items: {e}")
        return []


def create_id(page, vector_id, item_position):
    try:
        page = int(float(page))
        # Check if vector_id is NaN
        if isinstance(vector_id, float) and math.isnan(vector_id):
            vector_id = "unknown"
        item_position = int(float(item_position))
        # Create a unique ID for the item
        return f"p{page}_v{vector_id}_pos{item_position}"
    except Exception as e:
        print(f"Error creating ID: page={page}, vector_id={vector_id}, item_position={item_position}")
        print(f"Error message: {str(e)}")
        return f"p{page}_v{vector_id}_pos{item_position}"

# In your main function, after creating the DataFrame:
def clean_dataframe(df):
    # Convert 'page', 'vector_id', and 'item_position' to integers
    for col in ['page', 'vector_id', 'item_position']:
        df[col] = df[col].fillna(0).astype(int)
    return df


class RelationshipManager:
    def __init__(self):
        self.items = {}  # Store all items
        self.relationships = {}  # Store relationships between items

    def add_item(self, item_id, item_data):
        self.items[item_id] = item_data
        if item_id not in self.relationships:
            self.relationships[item_id] = set()

    def add_relationship(self, item1_id, item2_id):
        # Ensure both items exist in the relationships dictionary
        for item_id in [item1_id, item2_id]:
            if item_id not in self.relationships:
                print(f"Warning: Item {item_id} not found. Adding it to relationships.")
                self.relationships[item_id] = set()
        
        self.relationships[item1_id].add(item2_id)
        self.relationships[item2_id].add(item1_id)

    def get_item(self, item_id):
        return self.items.get(item_id)

    def get_related_items(self, item_id):
        related_ids = self.relationships.get(item_id, set())
        return {related_id: self.items.get(related_id) for related_id in related_ids if related_id in self.items}

    def get_related_items_by_type(self, item_id, item_type):
        related_items = self.get_related_items(item_id)
        return {id: item for id, item in related_items.items() if item and item.get('det_cat_1') == item_type}

def main(input_filepath, output_filepath, raw_data_filepath, detected_welds_filepath, bom_data_filepath, detected_general_items_filepath=None, pages_to_process=None):

    rm = RelationshipManager()
    
    isometric_area = {
        'x0': 804.5,
        'x1': 2404.5,
        'y0': 40.0,
        'y1': 1234.0
    }

    # Process vector drawings and create DataFrame
    print("\n\nIsolating Isometric Properties...")
    df = process_drawings(input_filepath, rm, pages_to_process, isometric_area_coords=isometric_area)

    # Converts id columns to integers
    df = clean_dataframe(df)


    # Load detected welds
    print("\n\nLoading Weld, BOM, Raw Data...")
    weld_maps = load_detected_welds(detected_welds_filepath)

    # Load detected general items if filepath is provided
    general_items = []
    if detected_general_items_filepath:
        print("Loading detected general items (gussets, etc.)...")
        general_items = load_detected_general_items(detected_general_items_filepath)
        if general_items:
            print(f"Loaded {len(general_items)} general items")

    # Load BOM data
    bom_data = load_bom_data(bom_data_filepath)

    # Load raw data
    if raw_data_filepath.endswith('.feather'):
        # Use the fast_storage utility if available
        try:
            from src.atom.fast_storage import load_df_fast
            raw_data = load_df_fast(raw_data_filepath)
        except ImportError:
            # Fall back to direct pandas read
            raw_data = pd.read_feather(raw_data_filepath)
    else:
        raw_data = pd.read_excel(raw_data_filepath)

    bom_labels = []
    
    print("\n\nEvaluating BOM, Size, Length, and Size Break Label Locations...")
    for page in pages_to_process:
        bom_labels.extend(find_labels(raw_data, bom_data, isometric_area, page))
    
    # Filter weld maps and general items for specified pages
    print("\n\nEvaluating Weld Map and General Item Locations...")    
    if pages_to_process is not None:
        weld_maps = [wm for wm in weld_maps if wm['page'] in pages_to_process]
        general_items = [gi for gi in general_items if gi.get('pdf_page') in pages_to_process]
    
    print("\n\nConsolidating Data...")
    # Concatenate weld maps, general items, and BOM labels to the DataFrame
    df = pd.concat([df, pd.DataFrame(weld_maps), pd.DataFrame(general_items), pd.DataFrame(bom_labels)], ignore_index=True)


    print("\n\nRelating Pointers...")
    #print("\n\nRelating pointers to items...")
    df = relate_pointers_to_items(df)


    print(df.describe())
    print(f"Isometric lines after item identification: {len(df)}")
    
    # Print summary of the DataFrame
    print(f"\nDataFrame summary:")
    print(df.describe())
    #print(f"\nIsometric lines: {len(df)}")
    print(f"\nTotal items: {len(df)}")
    print(f"Weld Maps: {len(df[df['det_cat_1'] == 'Weld Map'])}")
    print(f"BOM Labels: {len(df[df['det_cat_1'] == 'BOM Label'])}")
    
    # Print counts of any general items (like gussets)
    general_item_types = [col for col in df['det_cat_1'].unique() if 'Item' in col and col not in ['Weld Map', 'BOM Label']]
    for item_type in general_item_types:
        print(f"{item_type}: {len(df[df['det_cat_1'] == item_type])}")
    
    # Output isometric lines to PDF
    output_isometric_lines_to_pdf(df, input_filepath, output_filepath)
    
    return df

# Updated paths for the current project
input_pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\TR-001 Combined.pdf"

# Set up output directory in the same folder as the detect_general_items.py output
base_output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001"
output_dir = base_output_dir
output_pdf_path = os.path.join(output_dir, "TR-001_iso_components.pdf")

# Data files
raw_data_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\74\sources\cuserscmccaonedrivedocumentsarchitekt-isclientsexcel-usaexc_0014modifiedtr-001-combinedpdf\data.feather"
bom_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001\bom_tr-001.xlsx"

# Path to detected general items (like gussets) - using the output from detect_general_items.py
detected_general_items_path = os.path.join(base_output_dir, "TR-001_gusset_items.xlsx")

# For this example, we don't have detected welds yet, so we'll use the gusset items as a placeholder
# In a real scenario, you would have a separate file for detected welds
detected_welds_path = detected_general_items_path

start_time = time.time()

# Process all pages by default, or specify pages if needed
pages_to_process = None  # Process all pages
# pages_to_process = [1, 2, 3]  # Or specify specific pages

df = main(input_pdf_path, output_pdf_path, raw_data_path, detected_welds_path, bom_data_path, 
         detected_general_items_filepath=detected_general_items_path, pages_to_process=pages_to_process)

end_time = time.time()

total_time = end_time - start_time


print(f"\n\nIdentified in {total_time} seconds...")

df.to_excel(os.path.join(output_dir, "vector_data.xlsx"))
#df.to_excel(os.path.join(output_dir, "vector_data_with_weld_maps.xlsx"))
#regions.to_excel(os.path.join(output_dir, "iso_regions_data.xlsx"))

###############################
###############################
###############################
###############################
