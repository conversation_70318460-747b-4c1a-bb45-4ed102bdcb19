import pandas as pd
from collections import defaultdict
import re
import time
from tqdm import tqdm
import numpy as np

def process_duplicates(filepath):
    start_time = time.time()
    print("Start Processing Duplicates")
    
    # Read Excel file
    df = pd.read_excel(filepath)
    print(f"Excel load time: {time.time() - start_time:.2f} seconds")
    print(f"Total rows: {len(df)}")
    
    df['Modified'] = False
    
    def vectorized_find_duplicates(series):
        # Vectorized operations for series
        mask = series.str.contains(r'(\b\w+(?:\s+\w+)*\b).*\b\1\b', regex=True, na=False)
        return mask

    def find_longest_repeated_substring_simple(text):
        if not isinstance(text, str):
            return text
        
        # Don't split on quotes, only on spaces
        parts = text.split()
        phrases = defaultdict(int)
    
        # Count occurrences of each part
        for part in parts:
            phrases[part] += 1
        
        # Return most frequent part
        most_common = max(phrases.items(), key=lambda x: x[1])[0]
        return most_common

    def find_longest_repeated_substring_complex(text):
        if not isinstance(text, str):
            return text
        
        # Split text into major components by comma and optional space
        major_components = [comp.strip() for comp in text.split(',') if comp.strip()]
    
        # Find unique repeated components
        unique_components = []
        seen = set()
        for comp in major_components:
            if comp and comp not in seen:
                count = text.count(comp)
                if count > 1:
                    unique_components.append(comp)
                seen.add(comp)
    
        # Join unique components if found, otherwise return original text
        if unique_components:
            return ', '.join(unique_components)
        return text.strip()

    def find_longest_repeated_substring_complex_special(text):
        if not isinstance(text, str):
            return text

        components = [comp.strip() for comp in text.split(',') if comp.strip()]
        processed = []
        seen = set()
    
        i = 0
        while i < len(components):
            current = components[i]
            if "TYPE E OR S" in current or "CUT TO" in current:
                full_spec = current
                # Find the measurement if it exists
                if "CUT TO" in current:
                    parts = current.split("CUT TO")
                    if len(parts) > 1 and parts[1].strip():
                        measurement = max(parts[1].strip().split(), key=lambda x: text.count(x))
                        full_spec = f"{parts[0].strip()} - CUT TO {measurement}"
            
                if full_spec not in seen:
                    count = text.count(full_spec.split(("TYPE E OR S" if "TYPE E OR S" in full_spec else "CUT TO"))[0])
                    if count > 1:
                        processed.append(full_spec)
                    seen.add(full_spec)
            else:
                if current not in seen:
                    count = text.count(current)
                    if count > 1:
                        processed.append(current)
                    seen.add(current)
            i += 1

        return ', '.join(processed) if processed else text.strip()

    def find_longest_repeated_substring_complex_special3(text):
        if not isinstance(text, str):
            return text

        # Handle measurements separately first
        measurement_match = re.search(r'\d+\s+\d+/\d+"|\d+\s*"', text)
        measurement = measurement_match.group() if measurement_match else None

        components = [comp.strip() for comp in text.split(',') if comp.strip()]
        processed = []
        seen = set()
    
        i = 0
        while i < len(components):
            current = components[i]
            if "TYPE E OR S" in current:
                full_spec = current
                while i + 1 < len(components) and "TYPE E OR S" in components[i + 1]:
                    i += 1
                if full_spec not in seen:
                    count = text.count(full_spec.split("TYPE E OR S")[0])
                    if count > 1:
                        processed.append(full_spec)
                    seen.add(full_spec)
            else:
                if current not in seen:
                    count = text.count(current)
                    if count > 1:
                        processed.append(current)
                    seen.add(current)
            i += 1

        result = ', '.join(processed) if processed else text.strip()
        if measurement:
            result = f"{result} {measurement}"

        return result

    def find_longest_repeated_substring_complex_special2(text):
        if not isinstance(text, str):
            return text

        # Handle measurements separately
        measurement_match = re.search(r'\d+\s+\d+/\d+"|\d+\s*"', text)
        measurement = measurement_match.group() if measurement_match else None

        components = [comp.strip() for comp in text.split(',') if comp.strip()]
        processed = []
        seen = set()
    
        i = 0
        while i < len(components):
            current = components[i]
            if current not in seen:
                count = text.count(current)
                if count > 1:
                    processed.append(current)
                seen.add(current)
            i += 1

        result = ', '.join(processed) if processed else text.strip()
        if measurement:
            result = f"{result} {measurement}"

        return result

    def find_longest_repeated_substring_complex_special4(text):
        if not isinstance(text, str):
            return text

        # Enhanced measurement pattern to include feet notation
        measurement_match = re.search(r'\d+\'-\d+\s+\d+/\d+"|\d+\'-\d+"|\d+\s+\d+/\d+"|\d+\s*"', text)
        measurement = measurement_match.group() if measurement_match else None

        components = [comp.strip() for comp in text.split(',') if comp.strip()]
        processed = []
        seen = set()
    
        i = 0
        while i < len(components):
            current = components[i]
            if "TYPE E OR S" in current:
                full_spec = current
                while i + 1 < len(components) and "TYPE E OR S" in components[i + 1]:
                    i += 1
                if full_spec not in seen:
                    count = text.count(full_spec.split("TYPE E OR S")[0])
                    if count > 1:
                        processed.append(full_spec)
                    seen.add(full_spec)
            else:
                if current not in seen:
                    count = text.count(current)
                    if count > 1:
                        processed.append(current)
                    seen.add(current)
            i += 1

        result = ', '.join(processed) if processed else text.strip()
        if measurement:
            result = f"{result} {measurement}"

        return result


    
    columns_to_process = ['Quantity', 'Size', 'Tag', 'Material Description']
    
    for col in columns_to_process:
        if col in df.columns:
            col_start = time.time()
            print(f"\nProcessing Column: {col}")
            
            df[f'Original_{col}'] = df[col]
            needs_processing = vectorized_find_duplicates(df[col])
            print(f"Rows requiring processing: {needs_processing.sum()}")
            
            if needs_processing.sum() > 0:
                tqdm.pandas(desc=f"Processing {col}")
                # Choose processing function based on column
                process_func = find_longest_repeated_substring_complex_special4 if col == 'Material Description' else find_longest_repeated_substring_simple
                df.loc[needs_processing, col] = (
                    df.loc[needs_processing, col].progress_apply(process_func)
                )
           
                
            # Mark modified rows
            df.loc[df[col] != df[f'Original_{col}'], 'Modified'] = True
            print(f"Modified rows in {col}: {(df[col] != df[f'Original_{col}']).sum()}")
            print(f"Column processing time: {time.time() - col_start:.2f} seconds")
    
    total_time = time.time() - start_time
    print(f"\nTotal processing time: {total_time:.2f} seconds")
    print(f"Total modified rows: {df['Modified'].sum()}")
    
    return df

# Example usage:
df = process_duplicates("S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis 018\Data\Working\exported_bom_data.xlsx")
print("\nExporting to Excel....")
export_start = time.time()
df.to_excel("processed_output.xlsx", index=False)
print(f"Export time: {time.time() - export_start:.2f} seconds")