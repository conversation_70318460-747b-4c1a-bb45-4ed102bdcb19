from normalize_description import normalize_astm

# The specific problematic case
test_description = "CAP, ASME B16.11, NPT, #3000, A182 F304/304L"

# Test the fix
print(f"Testing: {test_description}")
normalized, metadata, review, match_type = normalize_astm(test_description, review=True)
print(f"Normalized: {normalized}")
print(f"Metadata: {metadata}")
print(f"Review: {review}")
print(f"Match type: {match_type}")
