"""
See API for account funcs

https://cloud.google.com/identity-platform/docs/reference/rest/v1/accounts/signInWithPassword
"""

import requests
import json
from src.utils.logger import logger

# logger = logging.getLogger(__name__)

FIREBASE_WEB_API_KEY = 'AIzaSyANrt-T3Vc-NdeAbS32PoGmr0-6H_CV2J0'


SIGN_IN_WITH_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword"
def sign_in(email, password, return_secure_token=True):
    try:
        payload = json.dumps({"email":email, "password":password, "return_secure_token": return_secure_token})
        r = requests.post(SIGN_IN_WITH_PASSWORD_URL,
                        params={"key": FIREBASE_WEB_API_KEY},
                        data=payload)
        return r.json()
    except Exception:
        logger.warning("Login request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Login failed"
        }

GET_USER_DATA_URL = "https://get-user-data-4x7nbl4ifq-uc.a.run.app"
def get_user_data(id_token: str):
    payload = {"id_token": id_token}
    try:
        r = requests.post(GET_USER_DATA_URL, params=payload)
        return r.json()
    except Exception:
        logger.warning("Create user request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Create user request failed"
        }


CREATE_USER_URL = "https://create-user-4x7nbl4ifq-uc.a.run.app"
def create_user(payload: dict):
    try:
        r = requests.post(CREATE_USER_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Create user request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Create user request failed"
        }


PURCHASE_TOKENS_REQUEST_URL = "https://purchase-tokens-request-4x7nbl4ifq-uc.a.run.app"
def purchase_tokens_request(payload: dict) -> dict:
    try:
        assert "id_token" in payload
        assert "quantity" in payload
        r = requests.post(PURCHASE_TOKENS_REQUEST_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Purchase token request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Purchase token request failed"
        }


CHECK_NEW_USER_DETAILS_URL = "https://check-new-user-details-4x7nbl4ifq-uc.a.run.app"
def check_new_user_details(payload) -> dict:
    try:
        r = requests.post(CHECK_NEW_USER_DETAILS_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Check new user details request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Check new user details request failed"
        }


UPDATE_USER_DETAILS_URL = "https://update-user-details-4x7nbl4ifq-uc.a.run.app"
def update_user_details(payload: dict):
    try:
        r = requests.post(UPDATE_USER_DETAILS_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Update user details request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Update user details request failed"
        }


GET_TOKENS_URL = "https://get-user-tokens-4x7nbl4ifq-uc.a.run.app"
def get_user_tokens(payload: dict):
    try:
        r = requests.post(GET_TOKENS_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Update user details request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Update user details request failed"
        }


REQUEST_JOB_URL = "https://request-job-4x7nbl4ifq-uc.a.run.app"
def request_job(payload: dict):
    try:
        r = requests.post(REQUEST_JOB_URL, params=payload)
        return r.json()
    except Exception:
        logger.warning("Request job failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Job request failed"
        }


COMPLETE_JOB_URL = "https://complete-job-4x7nbl4ifq-uc.a.run.app"
def complete_job(payload: dict):
    try:
        r = requests.post(COMPLETE_JOB_URL, params=payload)
        return r.json()
    except Exception:
        logger.warning("Complete job failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Complete job request failed"
        }

COMPLETE_JOB_FAIL_URL = "https://complete-job-fail-4x7nbl4ifq-uc.a.run.app"
def complete_job_fail(payload: dict):
    try:
        r = requests.post(COMPLETE_JOB_FAIL_URL, params=payload)
        return r.json()
    except Exception:
        logger.warning("Complete job failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Complete job request failed"
        }


def send_password_reset_email(email):
    try:
        request_ref = "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getOobConfirmationCode?key={0}".format(FIREBASE_WEB_API_KEY)
        headers = {"content-type": "application/json; charset=UTF-8"}
        data = json.dumps({"requestType": "PASSWORD_RESET", "email": email})
        request_object = requests.post(request_ref, headers=headers, data=data)
        request_object = request_object.json()
        res = {
            "status": "ok",
        }
        res.update(request_object)
        if "error" in res:
            res["status"] = "fail"
        return res
    except Exception:
        logger.warning("Send password reset email request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Send password reset email request failed"
        }


def send_email_verification(id_token):
    try:
        request_ref = "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getOobConfirmationCode?key={0}".format(FIREBASE_WEB_API_KEY)
        headers = {"content-type": "application/json; charset=UTF-8"}
        data = json.dumps({"requestType": "VERIFY_EMAIL", "idToken": id_token})
        request_object = requests.post(request_ref, headers=headers, data=data)
        request_object = request_object.json()
        res = {
            "status": "ok",
        }
        res.update(request_object)
        if "error" in res:
            res["status"] = "fail"
        return res
    except Exception as e:
        print(e)
        return {
            "status": "fail",
            "error": "Send email verification request failed"
        }


# Need to periodically refresh token to avoid expiry
def token_refresh(refresh_token):
    try:
        request_ref = "https://securetoken.googleapis.com/v1/token?key={0}".format(FIREBASE_WEB_API_KEY)
        headers = {"content-type": "application/json; charset=UTF-8"}
        data = json.dumps({"grantType": "refresh_token", "refreshToken": refresh_token})
        request_object = requests.post(request_ref, headers=headers, data=data)
        request_object = request_object.json()
        res = {
            "status": "ok",
        }
        res.update(request_object)
        if "error" in res:
            res["status"] = "fail"
        return res
    except Exception as e:
        return {
            "status": "fail",
            "error": "Refresh token failed"
        }

ACTIVATE_PRODUCT_KEY_REQUEST_URL = "https://activate-product-key-4x7nbl4ifq-uc.a.run.app"
def activate_product_key_request(payload: dict) -> dict:
    try:
        assert "id_token" in payload
        assert "product_key" in payload
        r = requests.post(ACTIVATE_PRODUCT_KEY_REQUEST_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Activate product key request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Activate product key request failed"
        }

GET_ATEM_TOKEN_PRICE_REQUEST_URL = "https://get-atem-token-price-4x7nbl4ifq-uc.a.run.app"
def get_atem_token_price(payload: dict) -> dict:
    try:
        if "id_token" not in payload:
            raise KeyError("id_token not in payload")
        r = requests.post(GET_ATEM_TOKEN_PRICE_REQUEST_URL, 
                        params=payload)
        return r.json()
    except Exception:
        logger.warning("Get ATEM token price request failed", exc_info=True)
        return {
            "status": "fail",
            "error": "Get ATEM token price request failed"
        }
