import pandas as pd
import cv2
import numpy as np
import fitz  # PyMuPDF
from typing import Dict, List, Tuple

def load_weld_data(excel_path: str) -> pd.DataFrame:
    """Load weld data from Excel file."""
    return pd.read_excel(excel_path)

def get_color_mapping(weld_types: List[str]) -> Dict[str, Tuple[int, int, int]]:
    """Create a color mapping for different weld types."""
    # Generate distinct colors for each weld type
    colors = [
        (255, 0, 0),    # Red
        (0, 255, 0),    # Green
        (0, 0, 255),    # Blue
        (255, 255, 0),  # Yellow
        (255, 0, 255),  # <PERSON><PERSON><PERSON>
        (0, 255, 255),  # <PERSON><PERSON>
        (100, 80, 255),  # <PERSON><PERSON>
    ]
    return {weld_type: colors[i % len(colors)] for i, weld_type in enumerate(weld_types)}

def draw_welds(cv_image: np.ndarray, page_data: pd.DataFrame, color_mapping: Dict[str, Tuple[int, int, int]]) -> np.ndarray:
    """Draw weld circles and labels on the image."""
    image = cv_image.copy()

    # Add legend in top left corner
    y_offset = 30  # Starting y position for legend
    for weld_type, color in color_mapping.items():
        cv2.putText(image, str(weld_type), (20, y_offset),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        y_offset += 25  # Space between legend items

    # For each weld in the page
    for _, weld in page_data.iterrows():
        x, y = int(weld['x']), int(weld['y'])
        weld_type = weld['class']
        weld_id = str(weld['class'])
        color = color_mapping[weld_type]

        # Draw circle
        cv2.circle(image, (x, y), 10, color, 2)

        # Add label
        cv2.putText(image, str(weld_id), (x + 15, y),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    return image

def process_pdf(pdf_path: str, weld_data: pd.DataFrame, page_limit: int = None) -> List[np.ndarray]:
    """Process PDF pages and return list of annotated images."""
    # Open PDF
    pdf_document = fitz.open(pdf_path)
    annotated_pages = []

    # Get unique weld types for color mapping
    unique_weld_types = weld_data['class'].unique()
    color_mapping = get_color_mapping(unique_weld_types)

    # Determine number of pages to process
    total_pages = min(pdf_document.page_count, page_limit or float('inf'))

    # Process each page
    for page_num in range(total_pages):
        # Get page
        page = pdf_document[page_num]
        print(f"Processing page {page_num + 1} of {total_pages}")

        # Convert PDF page to image
        pix = page.get_pixmap()
        img_data = pix.samples

        # Convert to numpy array and then to CV2 image
        cv_image = np.frombuffer(img_data, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGBA2BGR)

        # Get welds for this page
        page_welds = weld_data[weld_data['page'] == page_num + 1]

        # Draw welds on the image
        annotated_image = draw_welds(cv_image, page_welds, color_mapping)
        annotated_pages.append(annotated_image)

    pdf_document.close()
    return annotated_pages

def save_to_pdf(images: List[np.ndarray], output_path: str):
    """Save list of images to PDF."""
    # Create PDF document
    doc = fitz.open()

    for img in images:
        # Convert BGR to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Create PDF page with image dimensions
        page = doc.new_page(width=img.shape[1], height=img.shape[0])

        # Convert numpy array to bytes
        img_bytes = cv2.imencode('.png', img_rgb)[1].tobytes()

        # Insert image into page
        page.insert_image(page.rect, stream=img_bytes)

    # Save PDF
    doc.save(output_path, garbage=3, deflate=True)
    doc.close()


def generate_pdf(pdf_path, weld_data, output_path):
    page_limit = 30
    annotated_images = process_pdf(pdf_path, weld_data, page_limit)
    save_to_pdf(annotated_images, output_path)


def main():

    pdf_path = r"C:\Drawings\Isometrics\joey_williams_combined_isometrics.pdf"
    excel_path = r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\all_detected_welds.xlsx"

    output_path = "output_welds.pdf"
    page_limit = 100  # Limit to first 10 pages

    # Load weld data
    weld_data = load_weld_data(excel_path)

    # Process PDF and get annotated images
    annotated_images = process_pdf(pdf_path, weld_data, page_limit)
    # Save to new PDF
    save_to_pdf(annotated_images, output_path)

    print(f"Annotated PDF saved to: {output_path}")

if __name__ == "__main__":
    main()
