import pandas as pd

def plugin_elevations_check_valid(input_file, output_file: str = "debug/elevations_valid.xlsx"):
    """Looks for valid elevations. Combines unique by semi-column for each page

    This expects an input df which has a cleaned elevation column. If it has value. It is added if unique

    """
    df = pd.read_excel(input_file)

    new_df = []

    for pdf_page, group in df.groupby("pdf_page"):

        elevations = set()
        for index, row in group.iterrows():
            elevation = row["elevation"]
            if elevation:
                elevations.add(elevation.strip())

        elevations_str = ";".join(elevations)

        data = {"pdf_page": pdf_page, "elevation": elevations_str}
        for n2, ev in enumerate(elevations, start=1):
            data[f"e{n2}"] = ev

        new_df.append(data)

    new_df = pd.DataFrame(new_df)
    new_df.sort_values(by="pdf_page", inplace=True)
    new_df.to_excel(output_file, index=False)

    return f"saved to {output_file}"


if __name__ == "__main__":
    input_file = r"c:\Users\<USER>\Documents\GitHub\ATEM_OCR\output\surya_data\surya full_ocr_output - batch 2.xlsx"
    df = pd.read_excel(input_file, encoding)

    for index, row in df.iterrows():
        elevation = row["elevation"]
        if not elevation or pd.isna(elevation):
            continue

        print(elevation, "old")

        mod = elevation[elevation.lower().index("el"):]
        print(mod, "new")

        mod = mod.replace("=", "-")

        clean = ["<", ","]


        df.at[index, "elevation"] = mod

    output_file = r"c:\Users\<USER>\Documents\GitHub\ATEM_OCR\output\surya_data\surya fulL_ocr_output - manually cleaned 4.xlsx"
    df.to_excel(output_file, index=False)
