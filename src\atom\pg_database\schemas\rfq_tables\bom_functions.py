INSERT_TEST_RECORD = """
    -- Insert a test BOM record
    INSERT INTO public.bom (
        project_id, profile_id, material_description, size, size1, size2
    ) VALUES (
        3, 2, 'Test Material', '2x1', 2.0, 1.0
    )
"""

FORCE_PROPOGATE_TO_BOM = """
    -- Propagate categories from RFQ to BOM
    UPDATE public.bom b
    SET rfq_scope = r.rfq_scope,
        general_category = r.general_category, 
        fitting_category = r.fitting_category,
        valve_type = r.valve_type,
        pipe_category = r.pipe_category,
        updated_at = CURRENT_TIMESTAMP
    FROM public.atem_rfq r
    WHERE b.rfq_ref_id = r.id
    AND b.project_id = 1;  -- Replace with your project_id
"""

SYNC_BOM_TO_RFQ_AFTER = """
    -- Drop existing triggers and functions
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq_after ON public.bom;
    DROP FUNCTION IF EXISTS sync_bom_to_rfq_after();

    -- Create an improved BOM to RFQ function that prevents recursive trigger firing
    CREATE OR REPLACE FUNCTION sync_bom_to_rfq_after()
    RETURNS TRIGGER AS $$
    DECLARE
        rfq_id INTEGER;
        existing_rfq_id INTEGER;
        old_refs_count INTEGER;
    BEGIN
        -- Skip this execution if we're in an update triggered by this function
        -- The key insight: check for ALREADY correct rfq_ref_id to avoid loops
        IF TG_OP = 'UPDATE' AND 
        NOT (NEW.material_description IS DISTINCT FROM OLD.material_description OR
                NEW.size IS DISTINCT FROM OLD.size) THEN
            -- No relevant fields changed, skip processing
            RETURN NULL;
        END IF;
        
        -- Check if an RFQ with these values already exists
        -- Modified to use only size and material_description for uniqueness
        SELECT id INTO existing_rfq_id
        FROM public.atem_rfq
        WHERE project_id = NEW.project_id
        AND UPPER(material_description) = UPPER(NEW.material_description)
        AND size = NEW.size
        LIMIT 1;
        
        IF existing_rfq_id IS NOT NULL THEN
            -- CRITICAL: Only update if the rfq_ref_id has changed to avoid recursion
            IF NEW.rfq_ref_id IS DISTINCT FROM existing_rfq_id THEN
                -- Use a direct connection/statement to avoid triggering events
                UPDATE public.bom
                SET rfq_ref_id = existing_rfq_id
                WHERE id = NEW.id;
                
                -- If this is an update and the old RFQ reference is changing
                IF TG_OP = 'UPDATE' AND OLD.rfq_ref_id IS NOT NULL AND OLD.rfq_ref_id != existing_rfq_id THEN
                    -- Count other references to the old RFQ
                    SELECT COUNT(*) INTO old_refs_count
                    FROM public.bom 
                    WHERE rfq_ref_id = OLD.rfq_ref_id 
                    AND id != OLD.id;
                    
                    -- If no other references, delete the old RFQ
                    IF old_refs_count = 0 THEN
                        DELETE FROM public.atem_rfq WHERE id = OLD.rfq_ref_id;
                    END IF;
                END IF;
            END IF;
        ELSE
            -- No matching RFQ exists, create a new one
            INSERT INTO public.atem_rfq (
                project_id, 
                material_description, 
                size, 
                size1, 
                size2,
                quantity,
                rfq_scope,
                general_category,
                unit_of_measure,
                material,
                rating,
                ends,
                fitting_category,
                valve_type,
                calculated_eq_length,
                calculated_area
            ) VALUES (
                NEW.project_id,
                NEW.material_description,
                NEW.size,
                NEW.size1,
                NEW.size2,
                COALESCE(NEW.quantity, 1),
                NEW.rfq_scope,
                NEW.general_category,
                NEW.unit_of_measure,
                NEW.material,
                NEW.rating,
                NEW.ends,
                NEW.fitting_category,
                NEW.valve_type,
                NEW.calculated_eq_length,
                NEW.calculated_area
            )
            RETURNING id INTO rfq_id;
            
            -- CRITICAL: Only update if the rfq_ref_id would change
            IF NEW.rfq_ref_id IS DISTINCT FROM rfq_id THEN
                UPDATE public.bom
                SET rfq_ref_id = rfq_id
                WHERE id = NEW.id;
                
                -- Handle orphaned RFQ if this is an update
                IF TG_OP = 'UPDATE' AND OLD.rfq_ref_id IS NOT NULL AND OLD.rfq_ref_id != rfq_id THEN
                    SELECT COUNT(*) INTO old_refs_count
                    FROM public.bom 
                    WHERE rfq_ref_id = OLD.rfq_ref_id 
                    AND id != OLD.id;
                    
                    IF old_refs_count = 0 THEN
                        DELETE FROM public.atem_rfq WHERE id = OLD.rfq_ref_id;
                    END IF;
                END IF;
            END IF;
        END IF;
        
        RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;

    -- Create the trigger
    CREATE TRIGGER trg_sync_bom_to_rfq_after
    AFTER INSERT OR UPDATE ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION sync_bom_to_rfq_after();
"""

SYNC_RFQ_TO_BOM = """
    CREATE OR REPLACE FUNCTION sync_rfq_to_bom()
    RETURNS TRIGGER AS $$
    DECLARE
        project_lock_key BIGINT;
    BEGIN
        -- Convert project_id to a lock key
        project_lock_key := 200000000 + NEW.project_id;
        
        -- Check if we're in a sync operation
        IF EXISTS (
            SELECT 1 FROM pg_locks
            WHERE objid = project_lock_key
            AND locktype = 'advisory'
        ) THEN
            RETURN NEW;
        END IF;
        
        -- Acquire a project-specific advisory lock
        PERFORM pg_advisory_xact_lock(project_lock_key);
        
        -- Update BOM records that reference this RFQ, excluding size fields
        UPDATE public.bom
        SET 
            general_category = NEW.general_category,
            rfq_scope = NEW.rfq_scope,
            material = NEW.material,
            rating = NEW.rating,
            ends = NEW.ends,
            fitting_category = NEW.fitting_category,
            valve_type = NEW.valve_type,
            -- calculated_eq_length = NEW.calculated_eq_length,
            -- calculated_area = NEW.calculated_area,
            updated_at = CURRENT_TIMESTAMP
            -- Explicitly NOT updating: material_description, size, size1, size2
        WHERE rfq_ref_id = NEW.id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
"""

HANDLE_RFQ_DELETION = """
    CREATE OR REPLACE FUNCTION handle_rfq_deletion()
    RETURNS TRIGGER AS $$
    BEGIN
        -- When an RFQ is deleted, remove references in BOM
        UPDATE public.bom
        SET rfq_ref_id = NULL
        WHERE rfq_ref_id = OLD.id;
        
        RETURN OLD;
    END;
    $$ LANGUAGE plpgsql;
"""

CREATE_TRIGGERS_IN_CORRECT_ORDER = """
    -- Drop existing triggers
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq ON public.bom;
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq_after ON public.bom;
    DROP TRIGGER IF EXISTS trg_sync_rfq_to_bom ON public.atem_rfq;
    DROP TRIGGER IF EXISTS trg_handle_rfq_deletion ON public.atem_rfq;

    -- Create BOM to RFQ trigger (AFTER trigger)
    CREATE TRIGGER trg_sync_bom_to_rfq_after
    AFTER INSERT OR UPDATE ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION sync_bom_to_rfq_after();

    -- Create RFQ to BOM trigger (ignoring size fields)
    CREATE TRIGGER trg_sync_rfq_to_bom
    AFTER UPDATE OF general_category, rfq_scope, material, rating, ends, 
                    fitting_category, valve_type, calculated_eq_length, calculated_area
    ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION sync_rfq_to_bom();

    /*-- Create RFQ deletion handler
    CREATE TRIGGER trg_handle_rfq_deletion
    BEFORE DELETE ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION handle_rfq_deletion();
    */

    -- Create a new AFTER trigger
    DROP TRIGGER IF EXISTS trg_handle_rfq_deletion ON public.atem_rfq;
    CREATE TRIGGER trg_handle_rfq_deletion
    AFTER DELETE ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION handle_rfq_deletion();
"""

FOCE_MANUAL_SYNC = """
    CREATE OR REPLACE FUNCTION sync_all_bom_to_rfq()
    RETURNS INTEGER AS $$
    DECLARE
        bom_rec RECORD;
        rfq_id INTEGER;
        existing_rfq_id INTEGER;
        counter INTEGER := 0;
    BEGIN
        -- Process each BOM record
        FOR bom_rec IN 
            SELECT * FROM public.bom 
            WHERE rfq_ref_id IS NULL
        LOOP
            -- Check if an RFQ with matching values already exists
            -- Modified to use only size and material_description for uniqueness
            SELECT id INTO existing_rfq_id
            FROM public.atem_rfq
            WHERE project_id = bom_rec.project_id
            AND UPPER(material_description) = UPPER(bom_rec.material_description)
            -- AND (
            --     (COALESCE(size1, 0) = COALESCE(bom_rec.size1, 0) AND COALESCE(size2, 0) = COALESCE(bom_rec.size2, 0))
            --     OR (COALESCE(size1, 0) = COALESCE(bom_rec.size2, 0) AND COALESCE(size2, 0) = COALESCE(bom_rec.size1, 0))
            -- )
            AND size = bom_rec.size
            LIMIT 1;
            
            IF existing_rfq_id IS NOT NULL THEN
                -- Link to existing RFQ record
                UPDATE public.bom
                SET rfq_ref_id = existing_rfq_id
                WHERE id = bom_rec.id;
            ELSE
                -- No matching RFQ exists, create a new one
                INSERT INTO public.atem_rfq (
                    project_id, 
                    material_description, 
                    size, 
                    size1, 
                    size2,
                    quantity,
                    rfq_scope,
                    general_category,
                    unit_of_measure,
                    material,
                    rating,
                    ends,
                    fitting_category,
                    valve_type,
                    calculated_eq_length,
                    calculated_area
                ) VALUES (
                    bom_rec.project_id,
                    bom_rec.material_description,
                    bom_rec.size,
                    bom_rec.size1,
                    bom_rec.size2,
                    bom_rec.quantity,
                    bom_rec.rfq_scope,
                    bom_rec.general_category,
                    bom_rec.unit_of_measure,
                    bom_rec.material,
                    bom_rec.rating,
                    bom_rec.ends,
                    bom_rec.fitting_category,
                    bom_rec.valve_type,
                    bom_rec.calculated_eq_length,
                    bom_rec.calculated_area
                )
                RETURNING id INTO rfq_id;
                
                -- Link the BOM record to the new RFQ record
                UPDATE public.bom
                SET rfq_ref_id = rfq_id
                WHERE id = bom_rec.id;
            END IF;
            
            counter := counter + 1;
        END LOOP;
        
        RETURN counter;
    END;
    $$ LANGUAGE plpgsql;
"""

FUNC_UPDATE_BOM_CALCULATIONS = """
    CREATE OR REPLACE FUNCTION update_bom_calculations()
    RETURNS TRIGGER AS $$
    DECLARE
        eq_length DECIMAL(20,12);  -- eq_length: The equivalent length
        area DECIMAL(20,12);  -- area: The calculated area
        eq_length_factor DECIMAL(10,3);  -- eq_length_factor: The factor for equivalent length calculation
        method VARCHAR(50);  -- method: The calculation method (lookup or factor)
        v_profile_name VARCHAR(255);  -- v_profile_name: The profile name
        v_lookup_category VARCHAR(100);  -- v_lookup_category: The category for lookup
        v_closest_size DECIMAL(8,3);  -- v_closest_size: The closest size found
        v_larger_size DECIMAL(8,3);  -- v_larger_size: The larger size for compound fittings
        lookup_found BOOLEAN := FALSE;  -- Flag to track if a lookup was successful
    BEGIN
        -- Set default values in case no match is found
        eq_length := 0;
        area := 0;

        -- Skip if profile_id is null
        IF NEW.profile_id IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- Retrieve calculation method and profile name
        SELECT equivalent_length_method, profile_name INTO method, v_profile_name
        FROM public.atem_client_profiles WHERE id = NEW.profile_id;
        
        -- Skip if no method found, but ensure defaults
        IF method IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- IMPROVED LOOKUP LOGIC
        IF method = 'lookup' THEN
            -- First try with the appropriate category based on scope/type
            IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                v_lookup_category := NEW.fitting_category;
            ELSIF NEW.rfq_scope = 'Pipe' AND NEW.pipe_category IS NOT NULL THEN
                v_lookup_category := NEW.pipe_category;
            ELSIF NEW.rfq_scope = 'Valves' AND NEW.valve_type IS NOT NULL THEN
                v_lookup_category := NEW.valve_type;
            END IF;
            
            -- Try exact match with the primary category if one was selected
            IF v_lookup_category IS NOT NULL THEN
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                -- Check if lookup succeeded
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using primary category=%', v_lookup_category;
                END IF;
            END IF;
            
            -- If primary category didn't find a match, fall back to general_category
            IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                v_lookup_category := NEW.general_category;
                
                -- Try exact match with general_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using general_category=%', v_lookup_category;
                END IF;
            END IF;
                
            -- If still no exact match, try alternative approaches
            IF NOT lookup_found THEN
                -- Reset lookup category for alternative approaches
                v_lookup_category := NULL;
                
                -- First try with the appropriate category based on scope/type
                IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                    v_lookup_category := NEW.fitting_category;
                ELSIF NEW.rfq_scope = 'Pipe' AND NEW.pipe_category IS NOT NULL THEN
                    v_lookup_category := NEW.pipe_category;
                ELSIF NEW.rfq_scope = 'Valves' AND NEW.valve_type IS NOT NULL THEN
                    v_lookup_category := NEW.valve_type;
                END IF;
                
                -- Try advanced matching with the primary category
                IF NOT lookup_found AND v_lookup_category IS NOT NULL THEN
                    
                    -- For compound sizes in BOM, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        
                        SELECT length_ft, area_ft INTO eq_length, area
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 = v_larger_size
                        AND size2 IS NULL
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found match using fitting_category with larger size=%', v_larger_size;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with fitting_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using fitting_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
                
                -- If primary category approaches failed, try general_category approaches
                IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                    v_lookup_category := NEW.general_category;
                    
                    -- For compound sizes in BOM, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        
                        SELECT length_ft, area_ft INTO eq_length, area
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 = v_larger_size
                        AND size2 IS NULL
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found match using general_category with larger size=%', v_larger_size;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with general_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using general_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END IF;

            -- Assign the calculated values 
            IF lookup_found THEN
                -- Match found - multiply by quantity to get the total equivalent length and area
                NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * COALESCE(eq_length, 0);
                NEW.calculated_area := COALESCE(NEW.quantity, 0) * COALESCE(area, 0);
            ELSE
                -- No match found in any category - explicitly set to 0
                NEW.calculated_eq_length := 0;
                NEW.calculated_area := 0;
            END IF;
            
        ELSIF method = 'factor' THEN
            lookup_found := FALSE;
            
            -- Try with the appropriate category based on scope/type first
            IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.fitting_category
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using fitting_category=%', NEW.fitting_category;
                END IF;
            ELSIF NEW.rfq_scope = 'Pipe' AND NEW.pipe_category IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.pipe_category
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using pipe_category=%', NEW.pipe_category;
                END IF;
            ELSIF NEW.rfq_scope = 'Valves' AND NEW.valve_type IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.valve_type
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using valve_type=%', NEW.valve_type;
                END IF;
            END IF;
            
            -- If no match found with primary category, always fall back to general_category
            IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.general_category 
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using general_category=%', NEW.general_category;
                END IF;
            END IF;

            -- Calculate with the factor if found, otherwise ensure it's 0
            IF lookup_found THEN
                NEW.calculated_eq_length := NEW.quantity * COALESCE(eq_length_factor, 0);
            ELSE
                -- No match found in any category, set to 0
                NEW.calculated_eq_length := 0;
            END IF;
            NEW.calculated_area := NULL; -- Area not calculated with factor method
        END IF;
        
        -- Ensure defaults if no lookup values were found
        IF NEW.calculated_eq_length IS NULL THEN
            NEW.calculated_eq_length := 0;
        END IF;
        
        IF NEW.calculated_area IS NULL AND method != 'factor' THEN
            NEW.calculated_area := 0;
        END IF;

        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create the trigger for BOM table
    DROP TRIGGER IF EXISTS trg_update_bom_calculations ON public.bom;

    CREATE TRIGGER trg_update_bom_calculations
    BEFORE INSERT OR UPDATE OF size1, size2, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.bom
    FOR EACH ROW EXECUTE FUNCTION update_bom_calculations();
"""