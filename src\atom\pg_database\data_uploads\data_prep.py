"""
Data preparation utilities for PostgreSQL uploads.

This module provides functions to prepare, clean, and format data
before uploading to PostgreSQL tables.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import re
from datetime import datetime

from .field_mapping import map_dataframe_columns, get_field_mapping
from .data_auditor import DataAuditor


class DataPreparator:
    """
    Prepares data for PostgreSQL upload by cleaning, validating, and formatting.
    """
    
    def __init__(self, table_name: str):
        """
        Initialize the DataPreparator for a specific table.
        
        Args:
            table_name: Name of the table ('general', 'bom', or 'rfq')
        """
        self.table_name = table_name.lower()
        self.auditor = DataAuditor(table_name)
        
        # Define default values for missing data
        self.default_values = {
            'general': {
                'length': 0.0,
                'calculated_area': 0.0,
                'calculated_eq_length': 0.0,
                'elbows_90': 0,
                'elbows_45': 0,
                'tees': 0,
                'reducers': 0,
                'flanges': 0,
                'valves_flanged': 0,
                'valves_welded': 0,
                'cut_outs': 0,
                'supports': 0,
                'bends': 0,
                'field_welds': 0,
                'union_couplings': 0,
                'expansion_joints': 0
            },
            'bom': {
                'quantity': 1.0,
                'calculated_eq_length': 0.0,
                'calculated_area': 0.0,
                'item_count': 1,
                'item_length': 0.0,
                'total_length': 0.0
            },
            'rfq': {
                'quantity': 1.0
            }
        }
    
    def prepare_dataframe(self, df: pd.DataFrame, 
                         map_columns: bool = True,
                         clean_data: bool = True,
                         validate_data: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Prepare a DataFrame for PostgreSQL upload.
        
        Args:
            df: Input DataFrame
            map_columns: Whether to map column names from SQLite to PostgreSQL
            clean_data: Whether to clean and format the data
            validate_data: Whether to validate the data
            
        Returns:
            Tuple of (prepared DataFrame, preparation report)
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'table_name': self.table_name,
            'original_rows': len(df),
            'original_columns': len(df.columns),
            'steps_performed': [],
            'issues_found': [],
            'warnings': [],
            'final_rows': 0,
            'final_columns': 0
        }
        
        df_prepared = df.copy()
        
        # Step 1: Map column names if requested
        if map_columns:
            df_prepared, unmapped_columns = map_dataframe_columns(df_prepared, self.table_name)
            report['steps_performed'].append('Column name mapping')
            if unmapped_columns:
                report['warnings'].append(f"Unmapped columns: {unmapped_columns}")
        
        # Step 2: Clean data if requested
        if clean_data:
            df_prepared = self._clean_dataframe(df_prepared, report)
            report['steps_performed'].append('Data cleaning')
        
        # Step 3: Validate data if requested
        if validate_data:
            audit_results = self.auditor.audit_dataframe(df_prepared)
            report['audit_results'] = audit_results
            report['steps_performed'].append('Data validation')
            
            if audit_results['issues']:
                report['issues_found'].extend(audit_results['issues'])
            if audit_results['warnings']:
                report['warnings'].extend(audit_results['warnings'])
        
        # Final statistics
        report['final_rows'] = len(df_prepared)
        report['final_columns'] = len(df_prepared.columns)
        
        return df_prepared, report
    
    def _clean_dataframe(self, df: pd.DataFrame, report: Dict[str, Any]) -> pd.DataFrame:
        """
        Clean the DataFrame by removing duplicates, empty rows, and fixing data types.
        """
        df_clean = df.copy()
        original_rows = len(df_clean)
        
        # Remove completely empty rows
        empty_rows_before = df_clean.isnull().all(axis=1).sum()
        df_clean = df_clean.dropna(how='all')
        empty_rows_removed = empty_rows_before - df_clean.isnull().all(axis=1).sum()
        if empty_rows_removed > 0:
            report['warnings'].append(f"Removed {empty_rows_removed} completely empty rows")
        
        # Remove duplicate rows
        duplicates_before = df_clean.duplicated().sum()
        df_clean = df_clean.drop_duplicates()
        duplicates_removed = duplicates_before - df_clean.duplicated().sum()
        if duplicates_removed > 0:
            report['warnings'].append(f"Removed {duplicates_removed} duplicate rows")
        
        # Clean text columns
        df_clean = self._clean_text_columns(df_clean, report)
        
        # Fix numeric columns
        df_clean = self._fix_numeric_columns(df_clean, report)
        
        # Apply default values for missing data
        df_clean = self._apply_default_values(df_clean, report)
        
        rows_removed = original_rows - len(df_clean)
        if rows_removed > 0:
            report['warnings'].append(f"Total rows removed during cleaning: {rows_removed}")
        
        return df_clean
    
    def _clean_text_columns(self, df: pd.DataFrame, report: Dict[str, Any]) -> pd.DataFrame:
        """Clean text columns by removing extra whitespace and fixing encoding issues."""
        df_clean = df.copy()
        text_columns = df_clean.select_dtypes(include=['object']).columns
        
        for col in text_columns:
            if col in df_clean.columns:
                # Convert to string and strip whitespace
                df_clean[col] = df_clean[col].astype(str).str.strip()
                
                # Replace 'nan' strings with actual NaN
                df_clean[col] = df_clean[col].replace(['nan', 'NaN', 'NULL', 'null', ''], np.nan)
                
                # Remove extra whitespace between words
                df_clean[col] = df_clean[col].str.replace(r'\s+', ' ', regex=True)
                
                # Fix common encoding issues
                df_clean[col] = df_clean[col].str.replace(r'[^\x00-\x7F]+', '', regex=True)
        
        return df_clean
    
    def _fix_numeric_columns(self, df: pd.DataFrame, report: Dict[str, Any]) -> pd.DataFrame:
        """Fix numeric columns by converting data types and handling invalid values."""
        df_clean = df.copy()
        
        # Define expected numeric columns for each table
        numeric_columns = {
            'general': ['length', 'calculated_area', 'calculated_eq_length', 'elevation',
                       'elbows_90', 'elbows_45', 'tees', 'reducers', 'flanges', 
                       'valves_flanged', 'valves_welded', 'cut_outs', 'supports', 
                       'bends', 'field_welds', 'union_couplings', 'expansion_joints'],
            'bom': ['quantity', 'calculated_eq_length', 'calculated_area', 
                   'item_count', 'item_length', 'total_length'],
            'rfq': ['quantity']
        }
        
        expected_numeric = numeric_columns.get(self.table_name, [])
        
        for col in expected_numeric:
            if col in df_clean.columns:
                # Convert to numeric, coercing errors to NaN
                original_dtype = df_clean[col].dtype
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
                
                # Check if conversion changed the data significantly
                if original_dtype != df_clean[col].dtype:
                    null_count = df_clean[col].isnull().sum()
                    if null_count > 0:
                        report['warnings'].append(f"Column '{col}': {null_count} values converted to NaN during numeric conversion")
        
        return df_clean
    
    def _apply_default_values(self, df: pd.DataFrame, report: Dict[str, Any]) -> pd.DataFrame:
        """Apply default values for missing data in important columns."""
        df_clean = df.copy()
        defaults = self.default_values.get(self.table_name, {})
        
        for col, default_value in defaults.items():
            if col in df_clean.columns:
                null_count_before = df_clean[col].isnull().sum()
                df_clean[col] = df_clean[col].fillna(default_value)
                null_count_after = df_clean[col].isnull().sum()
                
                filled_count = null_count_before - null_count_after
                if filled_count > 0:
                    report['warnings'].append(f"Column '{col}': filled {filled_count} null values with default value {default_value}")
        
        return df_clean
    
    def validate_for_upload(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate if DataFrame is ready for PostgreSQL upload.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Tuple of (is_valid, list of blocking issues)
        """
        issues = []
        
        # Check if DataFrame is empty
        if df.empty:
            issues.append("DataFrame is empty")
            return False, issues
        
        # Check required fields
        required_fields = {
            'general': ['project_id', 'pdf_id'],
            'bom': ['project_id', 'pdf_id', 'material_description'],
            'rfq': ['project_id', 'material_description']
        }
        
        required = required_fields.get(self.table_name, [])
        for field in required:
            if field not in df.columns:
                issues.append(f"Missing required column: {field}")
            elif df[field].isnull().all():
                issues.append(f"Required column '{field}' is completely empty")
        
        # Check for critical data type issues
        if self.table_name in ['general', 'bom', 'rfq']:
            if 'project_id' in df.columns and not pd.api.types.is_numeric_dtype(df['project_id']):
                issues.append("Column 'project_id' must be numeric")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def print_preparation_report(self, report: Dict[str, Any]) -> None:
        """Print a formatted preparation report."""
        print("=" * 60)
        print(f"DATA PREPARATION REPORT - {report['table_name'].upper()} TABLE")
        print("=" * 60)
        print(f"Preparation Time: {report['timestamp']}")
        print(f"Original: {report['original_rows']:,} rows, {report['original_columns']} columns")
        print(f"Final: {report['final_rows']:,} rows, {report['final_columns']} columns")
        print()
        
        print("STEPS PERFORMED:")
        for step in report['steps_performed']:
            print(f"  ✓ {step}")
        print()
        
        if report.get('issues_found'):
            print("CRITICAL ISSUES:")
            for issue in report['issues_found']:
                print(f"  ❌ {issue}")
            print()
        
        if report.get('warnings'):
            print("WARNINGS:")
            for warning in report['warnings']:
                print(f"  ⚠️  {warning}")
            print()
        
        # Overall assessment
        if not report.get('issues_found') and not report.get('warnings'):
            print("✅ PREPARATION STATUS: SUCCESS - Ready for upload")
        elif not report.get('issues_found'):
            print("⚠️  PREPARATION STATUS: SUCCESS - Minor warnings only")
        else:
            print("❌ PREPARATION STATUS: FAILED - Critical issues found")
        
        print("=" * 60)


def prepare_for_upload(df: pd.DataFrame, table_name: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Convenience function to prepare data for PostgreSQL upload.
    
    Args:
        df: Input DataFrame
        table_name: Name of the table ('general', 'bom', or 'rfq')
        
    Returns:
        Tuple of (prepared DataFrame, preparation report)
    """
    preparator = DataPreparator(table_name)
    return preparator.prepare_dataframe(df)


if __name__ == "__main__":
    # Example usage
    print("Data Preparation Example")
    print("=" * 30)
    
    # Create sample data for testing
    sample_data = pd.DataFrame({
        'id': [1, 2, 3, 4, 5],
        'project_id': ['1', '2', '3', '4', '5'],  # String that should be numeric
        'pdf_id': [101, 102, 103, 104, 105],
        'materialDescription': ['Pipe', 'Valve', 'Fitting', 'Pipe', 'Elbow'],  # CamelCase
        'quantity': ['10.5', '2.0', '1.0', 'invalid', '3.0'],  # Mixed types
        'size': ['2"', '4"', '1"', '2"', '3"']
    })
    
    # Prepare the sample data
    preparator = DataPreparator('bom')
    prepared_df, report = preparator.prepare_dataframe(sample_data)
    preparator.print_preparation_report(report)
    
    print("\nPrepared DataFrame:")
    print(prepared_df.head())
