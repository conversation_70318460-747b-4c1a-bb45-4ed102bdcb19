import cv2
import pandas as pd
import fitz
from src.atom.roiextraction import convert_relative_coords_to_points
from src.atom.vision.ocr_patcher import reduce_regions
from src.atom.roiextraction import process_page, get_structured_table
from src.utils import convert_roi_payload
from src.atom.fast_storage import load_df_fast
from PIL import Image
import io
import numpy as np
from pprint import pp


def page_to_opencv(page: fitz.Page, zoom=None, dpi=None):
    """Return open CV image from PyMuPDF page"""
    if zoom is None:
        zoom = 1
    if dpi:
        zoom = dpi / 72
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image

def visualize_regions(cv_image, regions, color=(0, 255, 0)):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    vis_img = cv_image.copy()

    # Draw text regions with alternating colors
    for i, region in enumerate(regions):
        x = int(region["x0"])
        y = int(region["y0"])
        w = int(region["x1"]- region["x0"])
        h = int(region["y1"]- region["y0"])
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        # cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.2, color, 1)

    return vis_img

def get_empty_raw_dataframe() -> pd.DataFrame:
    """If the whole drawing is blank, then the Raw Data would be blank

    Return an empty dataframe with correct columns
    """
    columns = [
        'sys_build',
        'sys_path',
        'sys_filename',
        'pdf_id',
        'pdf_page',
        'sys_layout_valid',
        'type',
        'category',
        'value', #text.strip(),
        'elevation',
        'x_position',
        'y_position',
        'coordinates', #The main bounding box
        'coordinates2', #bbox2 normalized_bbox2 #Precise Bounding Box for individual value
        'words',  # Store as JSON string for DataFrame compatibility
        'name',
        'title',
        'created_date',
        'mod_date',
        'id_annot_info',
        # 'annot_',
        'page_rotation',
        'color',
        'annot_type',
        'annot_subtype',
        'vertices',
        'endpoint',
        'stroke_color',
        "font",
        "font_style",
        "font_size",
        "flags"
    ]
    df = pd.DataFrame(columns=columns)
    return df


def extract_raw_regions(df: pd.DataFrame):
    """Parses coordinates and extracts bounding box values"""
    raw_regions = []

    for record in df.to_dict("records"):
        try:
            coordinates = None
            # Workaround - parse string to tuple
            if isinstance(record["coordinates2"], str):
                record["coordinates2"] = record["coordinates2"].replace("(", "")
                record["coordinates2"] = record["coordinates2"].replace(")", "")
                coordinates = tuple(map(float, record["coordinates2"].split(', ')))
            elif isinstance(record["coordinates2"], tuple):
                coordinates = record["coordinates2"]
            record["x0"] = coordinates[0]
            record["y0"] = coordinates[1]
            record["x1"] = coordinates[2]
            record["y1"] = coordinates[3]
            record["width"] = record["x1"] - record["x0"]
            record["height"] = record["y1"] - record["y0"]
            raw_regions.append({
                "pdf_page": record["pdf_page"],
                "x0": record["x0"],
                "y0": record["y0"],
                "x1": record["x1"],
                "y1": record["y1"],
                "value": record["value"],
            })
        except Exception as e:
            continue
            # raw_regions.append(record["coordinates2"])

    return raw_regions


def switch_ocr_on():
    """Loads and switches ocr on for all and resaves file"""
    import json

    f = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\11\sources\cuserss1downloadsshady-hills-hrsg-pipe-isos-10pdf\options.json"
    with open(f, "r") as file:
        data = json.load(file)

        for groupNumber, groupData in data["groups"].items():
            pages = []
            for p in groupData["pages"]:
                p["ocr"] = True
                pages.append(p)

            groupData["pages"] = pages

        print(data)

    with open(f, "w") as file:
        json.dump(data, file)

    exit()

def run():

    project_id = 10
    pdf_path = r"c:\Users\<USER>\Downloads\Shady Hills HRSG Pipe ISOs 10.pdf"
    roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\11\sources\cuserss1downloadsshady-hills-hrsg-pipe-isos-10pdf\options.json"
    roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

    ocr_file = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\11\sources\cuserss1downloadsshady-hills-hrsg-pipe-isos-10pdf\textract.feather"

    doc = fitz.open(pdf_path)

    # page_num = 12 # region overlap
    # page_num = 10 # pos column
    page_num = 15 # merged rows
    # page_num = 29 # bottom line
    # page_num = 17 # bottom line
    # page_num = 1 # hardware supports
    page_num = 51

    missing = pd.DataFrame()
    rois = roi_payload["groupRois"][1]
    use_ocr = True
    ocr_df = load_df_fast(ocr_file)
    ocr_df = ocr_df[ocr_df["pdf_page"] == page_num]

    page = doc[page_num-1]
    cv_image = page_to_opencv(page)

    ocr_df_scaled = ocr_df.copy()
    ocr_df_scaled["x0"] *= page.rect.width
    ocr_df_scaled["y0"] *= page.rect.height
    ocr_df_scaled["x1"] *= page.rect.width
    ocr_df_scaled["y1"] *= page.rect.height

    regions = ocr_df_scaled.to_records("dict")
    print(regions)
    # cv_image2 = visualize_regions(cv_image, regions)

    reduced = reduce_regions(ocr_df_scaled, inverse=False)
    regions = list(reduced.to_records("dict"))

    # cv_image2 = visualize_regions(cv_image, regions)

    converted_roi_payload = rois
    converted_roi_payload = convert_relative_coords_to_points(rois, page.rect.width, page.rect.height)

    # cv_image2 = visualize_regions(cv_image, regions, (0, 255, 0))

    # roiDf = []
    # for roi in converted_roi_payload:
    #     for item in roi["tableColumns"]:
    #         for k, pos in item.items():
    #             roiDf.append({
    #                 "x0": pos["x0"],
    #                 "y0": pos["y0"],
    #                 "x1": pos["x1"],
    #                 "y1": pos["y1"],
    #             })
    # # print(pd.DataFrame(roiDf))
    # cv_image2 = visualize_regions(cv_image2, roiDf, (0, 0, 255))

    # # bom crop
    # def crop(img):
    #     x0, y0, x1, y1 = 1838, 16, 2370, 571
    #     crop_img = img[y0:y1, x0:x1]
    #     return crop_img

    # # cv2.imshow("reduced", crop(cv_image2))
    # # cv2.waitKey()

    # def get_regions(path):
    #     df_filtered = pd.read_excel(path)
    #     return extract_raw_regions(df_filtered)


    # raw_regions_filtered = get_regions(r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\Debug Filtered Table Data Pg 1.xlsx")
    # d = pd.DataFrame(raw_regions_filtered)
    # reduced = reduce_regions(d, inverse=False)
    # reduced["pdf_page"] = 1
    # raw_regions_filtered = reduced.to_records("dict")

    # raw_regions_filtered = get_regions(r"final_ocr_df.xlsx")
    # print(raw_regions_filtered)
    # pd.DataFrame(raw_regions_filtered).to_excel("raw_regions.xlsx")
    # cv_image2 = visualize_regions(cv_image2, raw_regions_filtered, (255, 0, 0))

    # cv2.imshow("reduced", crop(cv_image2))
    # cv2.imshow("reduced", cv_image2)

    # cv2.imwrite("cv_image.png", cv_image2)

    res = process_page(doc, pdf_path, page_num - 1, get_empty_raw_dataframe(), missing, rois, use_ocr, ocr_df)
    page_num, raw_data, annot_types_df, annot_tables, text_tables, outlier_df, process_time, skipped, error = res

    print(text_tables["bom"])

    text_tables["bom"].to_excel(f"debug/bom_processed.xlsx")

    # cv2.waitKey()


if __name__ == "__main__":
    run()