"""
Pydantic models for LangGraph BOM Classification State and Structured Responses

This module defines the state management models and structured response schemas
for the LangGraph-based BOM classification workflow.
"""

from typing import Annotated, Dict, List, Optional, Any, Union
from typing_extensions import TypedDict
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from datetime import datetime


class ClassificationState(TypedDict):
    """
    State for BOM classification workflow
    
    This state is passed between all nodes in the LangGraph workflow and maintains
    the complete context of the classification process.
    """
    
    # Input data
    item_id: str
    material_description: str
    original_classification: Dict[str, Any]
    
    # Processing state
    messages: Annotated[List[BaseMessage], add_messages]
    current_stage: str  # "analysis", "classification", "qa_decisions", "self_audit"
    processing_path: str  # "pipe", "fitting", "valve", "flange", "gasket", "bolt", "support", "miscellaneous"
    
    # Extracted information
    extracted_properties: Dict[str, Any]
    category_intelligence: Dict[str, Any]
    
    # Classification results
    field_classifications: Dict[str, Any]
    confidence_scores: Dict[str, float]
    validation_results: Dict[str, Any]
    
    # Issues and corrections
    identified_issues: List[Dict[str, Any]]
    suggested_corrections: Dict[str, Any]
    
    # Metadata
    processing_time: float
    model_calls: int
    tokens_used: int
    workflow_path: List[str]
    
    # Debug and development flags
    debug_mode: bool
    stage_outputs: Dict[str, Any]  # Store intermediate outputs for debugging


class DetailedPropertyExtraction(BaseModel):
    """Detailed technical properties extracted from material description"""

    # Basic item identification
    item_type: Optional[str] = Field(description="Primary item type (Elbow, Tee, Valve, Flange, etc.)", default=None)
    item_subtype: Optional[str] = Field(description="Specific subtype (45°, 90°, Long Radius, Short Radius, etc.)", default=None)

    # Material specifications
    material_grade: Optional[str] = Field(description="Material grade (316/316L SS, Carbon Steel, etc.)", default=None)
    astm_standard: Optional[str] = Field(description="ASTM standard (A403, A106, A182, etc.)", default=None)
    astm_grade: Optional[str] = Field(description="ASTM grade (WP316/316L, WPB, F304, etc.)", default=None)

    # Manufacturing and testing
    manufacturing_process: Optional[str] = Field(description="Manufacturing process (Welded, Seamless, Forged, etc.)", default=None)
    testing_requirements: Optional[str] = Field(description="Testing requirements (100% X-Ray, etc.)", default=None)

    # Physical specifications
    end_connections: Optional[str] = Field(description="End connections (BE, PE, SW, RF, etc.)", default=None)
    schedule_thickness: Optional[str] = Field(description="Schedule/thickness (SCH 10S, SCH 40, etc.)", default=None)
    pressure_rating: Optional[str] = Field(description="Pressure rating/class (150#, 300#, 600#, etc.)", default=None)

    # Standards and specifications
    ansme_ansi_standard: Optional[str] = Field(description="ANSME/ANSI standard (B16.9, B16.11, B16.5, etc.)", default=None)

    # Size information
    primary_size: Optional[str] = Field(description="Primary size dimension", default=None)
    secondary_size: Optional[str] = Field(description="Secondary size dimension (for reducers, etc.)", default=None)

    # Additional properties
    coating: Optional[str] = Field(description="Surface coating or treatment", default=None)
    special_features: Optional[str] = Field(description="Special features or characteristics", default=None)


class CategoryClassificationMapping(BaseModel):
    """Mapping of extracted properties to categorization table fields"""

    # Primary categorization fields
    rfq_scope: Optional[str] = Field(description="Primary classification for RFQ purposes", default=None)
    unit_of_measure: Optional[str] = Field(description="Standard unit for quantification", default=None)

    # Material fields
    material: Optional[str] = Field(description="Primary material composition", default=None)
    abbreviated_material: Optional[str] = Field(description="Abbreviated material type", default=None)
    astm: Optional[str] = Field(description="ASTM standard designation", default=None)
    grade: Optional[str] = Field(description="Material grade designation", default=None)

    # Physical specifications
    ansme_ansi: Optional[str] = Field(description="ANSME/ANSI standard", default=None)
    rating: Optional[str] = Field(description="Pressure rating", default=None)
    schedule: Optional[str] = Field(description="Schedule designation", default=None)
    coating: Optional[str] = Field(description="Surface coating", default=None)
    forging: Optional[str] = Field(description="Manufacturing/fabrication method", default=None)
    ends: Optional[str] = Field(description="End connection type", default=None)

    # Size fields
    size1: Optional[str] = Field(description="Primary size", default=None)
    size2: Optional[str] = Field(description="Secondary size", default=None)

    # Category-specific fields
    pipe_category: Optional[str] = Field(description="Specific pipe type", default=None)
    valve_type: Optional[str] = Field(description="Specific valve type", default=None)
    fitting_category: Optional[str] = Field(description="Specific fitting type", default=None)
    bolt_category: Optional[str] = Field(description="Specific bolt type", default=None)
    gasket_category: Optional[str] = Field(description="Specific gasket type", default=None)


class MaterialAnalysisResponse(BaseModel):
    """Enhanced structured response for Stage 1: Material Analysis with two-phase approach"""

    # Phase 1: Detailed Property Extraction
    detailed_properties: DetailedPropertyExtraction = Field(
        description="Comprehensive technical properties extracted from material description"
    )

    # Phase 2: Category Classification
    category_mapping: CategoryClassificationMapping = Field(
        description="Mapping of extracted properties to categorization table fields"
    )

    # Legacy fields for backward compatibility
    primary_category: str = Field(
        description="Primary component category: pipe, fitting, valve, flange, gasket, bolt, support, miscellaneous"
    )

    extracted_properties: Dict[str, Any] = Field(
        description="Key properties extracted from material description (legacy format)",
        default_factory=dict
    )

    confidence: float = Field(
        description="Confidence in primary category determination (0.0-1.0)",
        ge=0.0,
        le=1.0
    )

    complexity_level: str = Field(
        description="Processing complexity: simple, standard, complex",
        default="standard"
    )

    reasoning: str = Field(
        description="Brief explanation of category determination and property extraction"
    )


class FieldClassificationResponse(BaseModel):
    """Structured response for individual field classification"""
    
    field_name: str = Field(description="Name of the classification field")
    classified_value: Optional[str] = Field(description="Classified value for the field")
    confidence: float = Field(description="Confidence in classification (0.0-1.0)", ge=0.0, le=1.0)
    reasoning: str = Field(description="Brief explanation of classification decision")
    requires_qa: bool = Field(description="Whether this field requires Q&A decision making", default=False)


class CategoryClassificationResponse(BaseModel):
    """Structured response for category-specific classification"""
    
    rfq_scope: Optional[str] = Field(description="Primary RFQ scope classification")
    material: Optional[str] = Field(description="Material type classification")
    astm: Optional[str] = Field(description="ASTM standard classification")
    grade: Optional[str] = Field(description="Material grade classification")
    
    # Category-specific fields (populated based on rfq_scope)
    pipe_category: Optional[str] = Field(description="Pipe category if rfq_scope is Pipe")
    valve_type: Optional[str] = Field(description="Valve type if rfq_scope is Valves")
    fitting_category: Optional[str] = Field(description="Fitting category if rfq_scope is Fittings")
    bolt_category: Optional[str] = Field(description="Bolt category if rfq_scope is Bolts")
    gasket_category: Optional[str] = Field(description="Gasket category if rfq_scope is Gaskets")
    
    # Physical properties
    size1: Optional[str] = Field(description="Primary size")
    size2: Optional[str] = Field(description="Secondary size")
    schedule: Optional[str] = Field(description="Pipe schedule")
    rating: Optional[str] = Field(description="Pressure rating")
    
    # Manufacturing and connection details
    forging: Optional[str] = Field(description="Manufacturing process")
    ends: Optional[str] = Field(description="End connection types")
    coating: Optional[str] = Field(description="Surface coating")
    ansme_ansi: Optional[str] = Field(description="ASME/ANSI standard")
    
    # Metadata
    field_confidence_scores: Dict[str, float] = Field(
        description="Confidence scores for each classified field",
        default_factory=dict
    )
    
    overall_confidence: float = Field(
        description="Overall confidence in classification",
        ge=0.0,
        le=1.0,
        default=0.0
    )
    
    processing_notes: List[str] = Field(
        description="Notes about the classification process",
        default_factory=list
    )


class QADecisionResponse(BaseModel):
    """Structured response for Q&A decision making"""
    
    decisions_made: List[Dict[str, Any]] = Field(
        description="List of decisions made during Q&A process",
        default_factory=list
    )
    
    field_corrections: Dict[str, str] = Field(
        description="Field corrections made based on Q&A decisions",
        default_factory=dict
    )
    
    confidence_adjustments: Dict[str, float] = Field(
        description="Confidence score adjustments based on decisions",
        default_factory=dict
    )
    
    requires_human_review: bool = Field(
        description="Whether human review is recommended",
        default=False
    )
    
    reasoning: str = Field(
        description="Summary of Q&A decision reasoning"
    )


class SelfAuditResponse(BaseModel):
    """Structured response for self-audit validation"""
    
    validation_status: str = Field(
        description="Overall validation status: passed, issues_found, failed"
    )
    
    cross_field_violations: List[Dict[str, Any]] = Field(
        description="Cross-field validation rule violations",
        default_factory=list
    )
    
    consistency_issues: List[Dict[str, Any]] = Field(
        description="Logical consistency issues found",
        default_factory=list
    )
    
    confidence_assessment: Dict[str, float] = Field(
        description="Final confidence assessment for each field",
        default_factory=dict
    )
    
    recommended_corrections: Dict[str, str] = Field(
        description="Recommended corrections based on audit",
        default_factory=dict
    )
    
    audit_summary: str = Field(
        description="Summary of audit findings"
    )


class WorkflowResult(BaseModel):
    """Final result of the LangGraph classification workflow"""
    
    item_id: str
    status: str  # "ok", "issues", "error"
    
    # Final classifications
    final_classifications: Dict[str, Any] = Field(default_factory=dict)
    confidence_scores: Dict[str, float] = Field(default_factory=dict)
    
    # Issues and corrections
    identified_issues: List[Dict[str, Any]] = Field(default_factory=list)
    suggested_corrections: Dict[str, Any] = Field(default_factory=dict)
    
    # Processing metadata
    workflow_path: List[str] = Field(default_factory=list)
    processing_time: float = Field(default=0.0)
    model_calls: int = Field(default=0)
    tokens_used: int = Field(default=0)
    
    # Debug information (if debug_mode enabled)
    stage_outputs: Optional[Dict[str, Any]] = Field(default=None)
    
    # Compatibility with existing AuditResult
    model_used: Optional[str] = Field(default=None)
    error_message: Optional[str] = Field(default=None)


# Helper functions for state management
def create_initial_state(
    item_id: str,
    material_description: str,
    original_classification: Dict[str, Any],
    debug_mode: bool = False
) -> ClassificationState:
    """Create initial classification state"""
    
    return ClassificationState(
        # Input data
        item_id=item_id,
        material_description=material_description,
        original_classification=original_classification,
        
        # Processing state
        messages=[],
        current_stage="analysis",
        processing_path="",
        
        # Extracted information
        extracted_properties={},
        category_intelligence={},
        
        # Classification results
        field_classifications={},
        confidence_scores={},
        validation_results={},
        
        # Issues and corrections
        identified_issues=[],
        suggested_corrections={},
        
        # Metadata
        processing_time=0.0,
        model_calls=0,
        tokens_used=0,
        workflow_path=[],
        
        # Debug
        debug_mode=debug_mode,
        stage_outputs={}
    )


def update_state_metadata(
    state: ClassificationState,
    stage_name: str,
    processing_time: float = 0.0,
    model_calls: int = 0,
    tokens_used: int = 0,
    stage_output: Any = None
) -> ClassificationState:
    """Update state metadata after stage completion"""
    
    updated_state = state.copy()
    updated_state["processing_time"] += processing_time
    updated_state["model_calls"] += model_calls
    updated_state["tokens_used"] += tokens_used
    updated_state["workflow_path"].append(stage_name)
    
    if updated_state["debug_mode"] and stage_output is not None:
        updated_state["stage_outputs"][stage_name] = stage_output
    
    return updated_state


if __name__ == "__main__":
    """Test state models and helper functions"""

    print("Testing LangGraph BOM Classification State Models")
    print("=" * 60)

    # Test initial state creation
    test_state = create_initial_state(
        item_id="test_001",
        material_description="Pipe SMLS, ASTM A106, SCH 40, 2\", BE",
        original_classification={"rfq_scope": "Pipe", "material": "Steel, Carbon"},
        debug_mode=True
    )

    print("✓ Initial state created successfully")
    print(f"  Item ID: {test_state['item_id']}")
    print(f"  Description: {test_state['material_description']}")
    print(f"  Current stage: {test_state['current_stage']}")
    print(f"  Debug mode: {test_state['debug_mode']}")

    # Test state metadata update
    updated_state = update_state_metadata(
        test_state,
        stage_name="material_analysis",
        processing_time=1.5,
        model_calls=1,
        tokens_used=150,
        stage_output={"category": "pipe", "confidence": 0.95}
    )

    print("\n✓ State metadata updated successfully")
    print(f"  Processing time: {updated_state['processing_time']}s")
    print(f"  Model calls: {updated_state['model_calls']}")
    print(f"  Tokens used: {updated_state['tokens_used']}")
    print(f"  Workflow path: {updated_state['workflow_path']}")

    if updated_state["debug_mode"]:
        print(f"  Stage outputs: {updated_state['stage_outputs']}")

    # Test structured response models
    print("\n✓ Testing structured response models")

    # Material Analysis Response
    analysis_response = MaterialAnalysisResponse(
        primary_category="pipe",
        extracted_properties={
            "astm": "A106",
            "schedule": "40",
            "size": "2",
            "ends": "BE"
        },
        confidence=0.95,
        complexity_level="standard",
        reasoning="Clear pipe indicators with ASTM A106 and schedule specification"
    )
    print(f"  Material Analysis: {analysis_response.primary_category} (confidence: {analysis_response.confidence})")

    # Category Classification Response
    category_response = CategoryClassificationResponse(
        rfq_scope="Pipe",
        material="Steel, Carbon",
        astm="A106",
        pipe_category="Pipe",
        size1="2",
        schedule="40",
        ends="BE",
        field_confidence_scores={
            "rfq_scope": 0.95,
            "material": 0.90,
            "astm": 0.98
        },
        overall_confidence=0.94
    )
    print(f"  Category Classification: {category_response.rfq_scope} (overall confidence: {category_response.overall_confidence})")

    # Workflow Result
    workflow_result = WorkflowResult(
        item_id="test_001",
        status="ok",
        final_classifications={
            "rfq_scope": "Pipe",
            "material": "Steel, Carbon",
            "astm": "A106"
        },
        confidence_scores={
            "rfq_scope": 0.95,
            "material": 0.90,
            "astm": 0.98
        },
        workflow_path=["material_analysis", "pipe_classification"],
        processing_time=2.1,
        model_calls=2,
        tokens_used=275
    )
    print(f"  Workflow Result: {workflow_result.status} (processing time: {workflow_result.processing_time}s)")

    print("\n" + "=" * 60)
    print("All state models tested successfully!")
    print("Ready for integration with LangGraph workflow nodes.")
