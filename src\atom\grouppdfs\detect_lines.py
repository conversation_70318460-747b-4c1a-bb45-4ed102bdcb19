import cv2
import numpy as np
import matplotlib
from matplotlib.pyplot import imshow
from matplotlib import pyplot as plt

import math

def get_contours(image, otsu: bool = True):
    # White color mask
    if type(image) is str:
        # read image
        image = cv2.imread(image)

    # Convert to gray
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Performing binary thresholding
    kernel_size = 3
    if otsu:
        ret, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
    else:
        ret, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    # finding contours
    contours = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)

    contours = contours[0] if len(contours) == 2 else contours[1]
    print("Contours count:", len(contours))
    return contours


def draw_contours(image, contours):
    # drawing Contours
    radius = 2
    color = (30,255,50)
    cv2.drawContours(image, contours, -1, color , radius)
    return image




def get_hough_lines(image):
    # Convert the img to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    kernel_size = 5
    blur_gray = cv2.GaussianBlur(gray, (kernel_size, kernel_size), 0)

    low_threshold = 50
    high_threshold = 150
    edges = cv2.Canny(blur_gray, low_threshold, high_threshold)

    # cv2.imshow('blur_gray.jpg', blur_gray)
    rho = 1  # distance resolution in pixels of the Hough grid
    theta = np.pi / 180  # angular resolution in radians of the Hough grid
    threshold = 15  # minimum number of votes (intersections in Hough grid cell)
    min_line_length = 50  # minimum number of pixels making up a line
    max_line_gap = 20  # maximum gap in pixels between connectable line segments
    # Run Hough on edge detected image
    # Output "lines" is an array containing endpoints of detected line segments
    lines = cv2.HoughLinesP(edges, rho, theta, threshold, np.array([]),
                        min_line_length, max_line_gap)

    return lines



def draw_hough_lines(image, lines):
    line_image = np.copy(image) * 0  # creating a blank to draw lines on
    for line in lines:
        for x1, y1, x2, y2 in line:
            p1 = (x1, y1)
            p2 = (x2, y2)
            image = cv2.line(image, p1, p2, (0, 255, 0), 1)

    # Draw the lines on the  image
    # lines_edges = cv2.addWeighted(image, 1, line_image, 1, 0)
    # return lines_edges
    return image
