from pdf2docx import Converter
import pandas as pd

# Specify the path to your PDF file
pdf_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Excel USA\Excel 007\All CF Combined\Grouped_PDFs\WP#06 COMBINED_group_1.pdf"

cv = Converter(pdf_file)
tables = cv.extract_tables(start=0, end=1)
cv.close()

for table in tables:
    print(table)



# Not quite

# import fitz
# import pandas as pd

# # Open the PDF document
# pdf_document = fitz.open(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Excel USA\Excel 007\All CF Combined\Grouped_PDFs\WP#06 COMBINED_group_1.pdf")

# # Get the first page
# page = pdf_document[0]

# # Find tables on the page with adjusted parameters
# tables = page.find_tables(
#     strategy="text",
#     vertical_strategy="text",
#     horizontal_strategy="text",
#     min_words_vertical=1,
#     min_words_horizontal=1,
#     edge_min_length=3,
#     intersection_tolerance=3,
#     text_tolerance=3
# )

# # Initialize an empty list to store dataframes
# dfs = []

# # Process each table on the page
# for table in tables:
#     # Extract the table content as a list of lists
#     table_data = table.extract()
    
#     # Convert the table data to a Pandas DataFrame
#     df = pd.DataFrame(table_data)
    
#     # Add a page number column
#     df['PAGE'] = 1
    
#     # Append to the list of dataframes
#     dfs.append(df)

# # Combine all dataframes
# if dfs:
#     combined_df = pd.concat(dfs, ignore_index=True)
    
#     combined_df.to_excel("Combined Table DF-Fitz.xlsx", index=False)
    
#     # Display the combined DataFrame
#     print(combined_df)
# else:
#     print("No tables found on the first page.")

# # Close the PDF document
# pdf_document.close()