from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import psycopg2
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI()

def get_db_connection():
    return psycopg2.connect(
        host=os.getenv("PG_HOST"),
        database=os.getenv("PG_DATABASE"),
        user=os.getenv("PG_USER"),
        password=os.getenv("PG_PASSWORD"),
        cursor_factory=RealDictCursor
    )

class QueryRequest(BaseModel):
    query: str

@app.post("/query")
async def execute_query(request: QueryRequest):
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(request.query)
        results = cur.fetchall()
        conn.commit()
        cur.close()
        conn.close()
        return {"results": [dict(row) for row in results]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}