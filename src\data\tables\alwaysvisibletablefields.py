"""Do not allow hiding these table fields"""

always_visible_table_fields = {}

"""
ef TEXT,
sf TEXT,
pos TEXT,
material_description TEXT,
size TEXT,
ident TEXT,
item TEXT,
tag TEXT,
quantity TEXT,
status TEXT,
nb TEXT,
fluid TEXT,
clean_spec TEXT,
line_number
"""
always_visible_table_fields["BOM"] = [
    'material_description',
    'size',
    'quantity',

    # Note - some fields brought over from RFQ merge
    'drawing',
    'sheet',
    'pid',
]

# Added all fields
always_visible_table_fields["SPOOL"] = [
    'cutPiece',
    'length',
    'spool',
    'spec',
    'size',
]


"""
pdf_id INTEGER,
dp1 TEXT,
dp2 TEXT,
dt1 TEXT,
dt2 TEXT,
op1 TEXT,
op2 TEXT,
opt1 TEXT,
opt2 TEXT,
pipeSpec TEXT,
size TEXT,
"""
always_visible_table_fields["SPEC"] = [

]


""" General fields
pdf_id INTEGER,
annotMarkups TEXT,
area TEXT,
avg_elevation TEXT,
blockCoordinates TEXT,
clientDocumentId TEXT,
coordinates TEXT,
designCode TEXT,
documentDescription TEXT,
documentId TEXT,
documentTitle TEXT,
drawing TEXT,
elevation TEXT,
flangeID TEXT,
heatTrace TEXT,
insulationSpec TEXT,
insulationThickness TEXT,
lineNumber TEXT,
max_elevation TEXT,
mediumCode TEXT,
min_elevation TEXT,
modDate TEXT,
paintSpec TEXT,
pid TEXT,
pipeSpec TEXT,
pipeStandard TEXT,
processLineList TEXT,
processUnit TEXT,
projectNo TEXT,
projectName TEXT,
pwht TEXT,
revision TEXT,
sequence TEXT,
service TEXT,
sheet TEXT,
size TEXT,
sys_build TEXT,
sys_layout_valid TEXT,
sysDocument TEXT,
sysDocumentName TEXT,
sys_filename TEXT,
sys_path TEXT,
system TEXT,
totalSheets TEXT,
unit TEXT,
vendorDocumentId TEXT,
weldId TEXT,
weldClass TEXT,
xCoord TEXT,
xray TEXT,
"""
# Always show ancillary fields
always_visible_table_fields["General"] = [
    'drawing',
    'lineNumber',
    'sheet',
    'avg_elevation',
    'elevation',
    'size',
    'lf',
    'sf',
    'ef',
    'elbows_90',
    'elbows_45',
    'bevels',
    'tees',
    'reducers',
    'caps',
    'flanges',
    'valves_flanged',
    'valves_welded',
    'cut_outs',
    'supports',
    'bends',
    'field_welds'
]

"""
material_description TEXT,
size TEXT,
ef TEXT,
sf TEXT,
general_category TEXT,
rfq_scope TEXT,
unit_of_measure TEXT,
size1 TEXT,
size2 TEXT,
schedule TEXT,
rating TEXT,
astm TEXT,
grade TEXT,
ansme_ansi TEXT,
material TEXT,
abbreviated_material TEXT,
coating TEXT,
forging TEXT,
ends TEXT,
item_tag TEXT,
tie_point TEXT,
pipe_category TEXT,
valve_type TEXT,
fitting_category TEXT,
weld_category TEXT,
bolt_category TEXT,
gasket_category TEXT,
answer_explanation TEXT,
review TEXT,
review_explanation TEXT,
"""

always_visible_table_fields["RFQ"] = [
    'material_description',
    'size',
    'quantity',
    'general_category',
    'fitting_category',
    'last_updated',
]

always_visible_table_fields["IFC"] = [
    "issue_no",
    "issue_date",
    "issue_description",
    "approved_by",
    "drawn_by",
    "checked_by"
]

always_visible_table_fields["generic_1"] = [
]

always_visible_table_fields["generic_2"] = [
]

always_visible_table_fields["Outlier"] = [
]


always_visible_table_fields["LookupTable"] = [
    'Category',
]