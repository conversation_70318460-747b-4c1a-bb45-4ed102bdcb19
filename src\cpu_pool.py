"""
Initialize CPU pool for multiprocessing

Intended to hide startup latency and reuse with the app in lieu of recreating the pool for every job
"""
import os
import threading

from concurrent.futures import ProcessPoolExecutor


_executor = None
_executor_ready = threading.Event()

def desktop_safe_max_workers():
    cores = os.cpu_count() or 1
    return max(1, cores - 2)  # leave 2 cores free

def _init_pool(max_workers=None):
    global _executor
    if max_workers is None:
        max_workers = desktop_safe_max_workers()
    _executor = ProcessPoolExecutor(max_workers=max_workers)
    _executor_ready.set()
    print(f"[cpu_pool] Process pool ready with {max_workers} workers")

def start_pool_in_background(max_workers=None):
    """Start pool initialization in a daemon thread."""
    threading.Thread(
        target=_init_pool,
        args=(max_workers,),
        daemon=True
    ).start()

def get_pool():
    """Block until pool is ready and return it."""
    _executor_ready.wait()
    return _executor

def shutdown_pool():
    """Shutdown the shared pool."""
    global _executor
    if _executor is not None:
        _executor.shutdown()
        _executor = None
        print("[cpu_pool] Process pool shut down")