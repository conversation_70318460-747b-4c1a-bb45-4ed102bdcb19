


def build_weld_prompt(column_headers, grid_lines=False):
    # Create the example dictionary structure based on the column headers
    example_dict = {header: "" for header in column_headers}
    # Assign "1" to the first header and keep the rest as empty strings
    first_key = column_headers[0]
    example_dict[first_key] = "1"
    example_str = str(example_dict).replace("'", '"')  # Convert to JSON format string

    gridline_stmt = "This table has gridlines so make sure the values are placed in the correct columns." if grid_lines else ""

    weld_prompt = f"""
    First, locate the table with these column headers: {", ".join(column_headers)}.
    The table may not start at the top of the image - it could begin in the middle or bottom of the page.
    Look for a row containing these specific headers to identify the start of the table.

    Once you've located the table, extract it in a structured tabular format based ONLY on the columns that are present.
    The table might contain only weld information, only cut piece information, or both - use the provided column headers
    ({", ".join(column_headers)}) to determine which data to extract.

    Row wrapping should be based on the first column ('{first_key}') to decide where rows end and begin. The items are usually numbered sequentially.
    Ensure you return all rows and do not leave any rows out. Your response will only contain the table with no other explanation.

    Return the data in the following dictionary format exactly as it is on the page you are extracting from:
    [
     {example_str},
     {example_str},
            ...
     ]

    Important:
    1. Use an empty string ("") for any blank or empty values in the image.
    2. Do not omit keys for blank values; include them with empty strings.
    3. Ensure every row has all keys ({", ".join(column_headers)}), even if some are empty.
    4. Focus only on the columns specified in the headers list - do not attempt to extract columns that aren't present.
    5. Pay special attention to any measurements or specifications that are present in the table.
    6. For any LENGTH values, properly notate feet (') and inches (") measurements if present.
    7. For any size measurements (like WELD SIZE), be extremely accurate with the fraction values (e.g., "1/8", "3/16").
    8. Accurately capture any type specifications or symbols that appear in the table.
    9. Do not use ASCII characters. Ensure to convert them to their decoded equivalents.
    10. {gridline_stmt}

    Example for a typical row with these specific headers:
    {example_str}

    Ensure the response is a valid JSON array of dictionaries.
    """

    return weld_prompt

def build_bom_prompt(column_headers, install_type=False, grid_lines=False):

    # Create the example dictionary structure based on the column headers
    example_dict = {header: "" for header in column_headers}
    # Assign "1" to the first header and keep the rest as empty strings
    first_key = column_headers[0]
    example_dict[first_key] = "1"
    example_str = str(example_dict).replace("'", '"')  # Convert to JSON format string

    gridline_stmt = "This table has gridlines so make sure the values are placed in the correct columns."

    install_type_str = """
    The table includes install type labels, 'FIELD MATERIAL' and 'SHOP MATERIAL', which is indicated by a header above the group of rows.
    Ensure you include this in the table output and ensure it is on its own row without any other column values in its row. Always place these install type labels in the first column.
    We will also have 'material category' labels such as: 'PIPE', 'FITTINGS', 'FLANGES', 'VALVES/IN-LINE ITEMS', 'GASKETS', 'BOLTS', etc.
    Treat these the same and place them in the first column in their own row.
    Ignore the 'PIPE SPOOLS' labels and contents.
    """ if install_type else ""

    bom_prompt = f"""
    Extract the table from the image in a structured tabular format ensuring the column order is the same as the example below. Use the column headers to decide what values belong in which columns.

    Row wrapping should be based on the left most column to decide where rows end and begin. The rows are numbered.
    Ensure you return all rows and do not leave any rows out. Your response will only contain the table with no other explanation.
    {install_type_str}
    Return the data in the following dictionary format exactly as it is on the page you are extrating from:
    [
     {example_str},
     {example_str},
            ...
     ]

    Important:
    1. Use an empty string ("") for any blank or empty values in the image.
    2. Do not omit keys for blank values; include them with empty strings.
    3. Ensure every row has all keys ({", ".join(column_headers)}), even if some are empty.
    4. Pay special attention to measurement values as these are the most important thing to get correct.
    5. Pay extra special attention to "QUANTITY"/"QTY" and make sure you place the value in the correct column and correctly notate the feet (') and inches (") values.
    6. Do not use ASCII characters. Ensure to convert them to their decoded equivalents.

    Example for a blank TAG:
    {example_str}

    Ensure the response is a valid JSON array of dictionaries.
    """

    return bom_prompt

def build_spec_prompt(column_headers, grid_lines=False):
    # Create the example dictionary structure based on the column headers
    example_dict = {header: "" for header in column_headers}
    # Assign a sample value to the first header (likely LINE NO or similar)
    first_key = column_headers[0]
    example_dict[first_key] = "101"
    example_str = str(example_dict).replace("'", '"')  # Convert to JSON format string

    gridline_stmt = "This table has gridlines so make sure the values are placed in the correct columns." if grid_lines else ""

    spec_prompt = f"""
        First, locate the specification table with these column headers: {", ".join(column_headers)}.
        The table may not start at the top of the image - it could begin in the middle or bottom of the page.
        Look for a row containing these specific headers to identify the start of the table.

        Once you've located the table, extract it in a structured tabular format based ONLY on the columns that are present.
        The table contains specifications for line numbers and may include temperature ratings, pressure ratings, insulation codes,
        paint codes, heat tracing codes, or other specifications - use the provided column headers
        ({", ".join(column_headers)}) to determine which data to extract.

        Row wrapping should be based on the first column ('{first_key}') to decide where rows end and begin.
        Each distinct line number or specification ID typically represents a new row.
        Ensure you return all rows and do not leave any rows out. Your response will only contain the table with no other explanation.

        Return the data in the following dictionary format exactly as it is on the page you are extracting from:
        [
         {example_str},
         {example_str},
                ...
         ]

        Important:
        1. Use an empty string ("") for any blank or empty values in the image.
        2. Do not omit keys for blank values; include them with empty strings.
        3. Ensure every row has all keys ({", ".join(column_headers)}), even if some are empty.
        4. Focus only on the columns specified in the headers list - do not attempt to extract columns that aren't present.
        5. Pay special attention to any alphanumeric codes, which may include both letters and numbers.
        6. For temperature ratings, ensure the units (�F, �C) are preserved if present.
        7. For pressure ratings, preserve units (PSI, kPa, bar) if present.
        8. Be extremely accurate with any specification codes as these are critical for proper construction and safety.
        9. Do not interpret or modify any codes - preserve them exactly as they appear in the image.
        10. Do not use ASCII characters. Ensure to convert them to their decoded equivalents.
        11. {gridline_stmt}

        Example for a typical row with these specific headers:
        {example_str}

        Ensure the response is a valid JSON array of dictionaries.
        """

    return spec_prompt


single_prompt = """
        "Extract the text from the image. Your response will only contain the extracted text with no other explanation.
        Return the data in the following dictionary format:
        [
         {"answer": "Value"}
         ]

        Important:
        1. Use an empty string ("") for any blank or empty values in the image.
        2. Do not omit keys for blank values; include them with empty strings.
        3. Your response should only have 1 answer. If rows are wrapped or contain multiple lines, join them with a space ensuring there is only one answer.

        Example for a blank TAG:
        {"asnwer": ""}

        Ensure the response is a valid JSON array of dictionaries.

        """

iso_bend_prompt = """
        "Extract only the number text that signifies degrees from the image. The degrees signify the bend angle on the drawing depiction and are followed by the degree symbol, which is equivalent to unicode 'U+00B0.'
         If there are multiple values, separate the values by a comma. Your response will only contain the extracted text with no other explanation.
        Return the data in the following dictionary format:
        [
         {"answer": "75.2, 75.2, 85"}
         ]

        Important:
        1. Use an empty string ("") for any blank or empty values in the image.
        2. Do not omit keys for blank values; include them with empty strings.
        3. Your response should only have 1 answer. If multiple values, join them with a comma.
        4. Only include values that end in a degree symbol.

        Example for a blank value:
        {"asnwer": ""}

        Ensure the response is a valid JSON array of dictionaries.

        """

iso_area_text_prompt = """
        "Extract the text that signifies a line number, isometric number, or any line identifier information from the image. Your response will only contain the extracted text with no other explanation.
        Return the data in the following dictionary format:
        [
         {"answer": "12-YWC-150-CS1-0025-ET-M-2.5, 1.5-BFW-159-CS1-0332-HC-M-1.5, 1-STC-150-CS2-0205-ET-M-2.0"}
         ]

        Important:
        1. Use an empty string ("") for any blank or empty values in the image.
        2. Do not omit keys for blank values; include them with empty strings.
        3. Your response should only have 1 answer. If multiple values, join them with a comma.
        4. Only include values that end in a degree symbol.

        Example for a blank value:
        {"asnwer": ""}

        Ensure the response is a valid JSON array of dictionaries.

        """

