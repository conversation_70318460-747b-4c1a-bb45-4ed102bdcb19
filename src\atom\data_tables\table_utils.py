# table_utils.py
import fitz  # PyMuPDF
import pandas as pd
import numpy as np
import json
from pprint import pp as pp

import logging
from difflib import SequenceMatcher

# Logger setup
logger = logging.getLogger(__name__)

def check_bbox_overlap(bbox1, bbox2):
    """Check if two bounding boxes overlap"""
    x0_1, y0_1, x1_1, y1_1 = bbox1
    x0_2, y0_2, x1_2, y1_2 = bbox2

    # Check if one rectangle is to the left of the other
    if x1_1 < x0_2 or x1_2 < x0_1:
        return False

    # Check if one rectangle is above the other
    if y1_1 < y0_2 or y1_2 < y0_1:
        return False

    return True

def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def adjust_bbox_90_swapped(bbox, rotation_matrix):
    """
    Adjust a bounding box for a 90-degree rotation by applying the rotation matrix
    and then swapping the x and y coordinates.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1), with x and y coordinates swapped.
    """
    #logger.debug("--> adjust_bbox_90_swapped accessed")
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Swap the coordinates directly, assigning transformed y to x and x to y
    # This corrects for the coordinate swap that occurs with a 90-degree rotation
    x0, y0 = p1_transformed.y, p1_transformed.x
    x1, y1 = p2_transformed.y, p2_transformed.x

    # It's important to ensure x0 < x1 and y0 < y1 for consistency
    adjusted_bbox = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_bbox

def adjust_clip_for_90_degree_rotation(rect, page):
    """
    Adjust a clipping rectangle for a 90-degree clockwise rotation.

    Parameters:
    - rect: The original clipping rectangle (x0, y0, x1, y1).
    - page: The page object for accessing dimensions.

    Returns:
    - The adjusted clipping rectangle as a tuple (x0, y0, x1, y1), correctly positioned for the rotated page.
    """

    logger.debug("--> adjust_clip_for_90_degree_rotation accessed")
    page_height = page.rect.height
    x0, y0 = rect[1], page_height - rect[2]
    x1, y1 = rect[3], page_height - rect[0]
    adjusted_clip = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_clip

def has_non_standard_characters(text):
    return any(ord(char) > 127 for char in text)

def apply_replacement_function(df):
    return df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)

# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        # if non_standard in text:
        #     print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text

def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):

    print_converted_coords = False

    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ROI payload: {e}")
        return []

    converted_payload = []

    for item in roi_payload:
        try:
            converted_item = {"columnName": item.get("columnName", "Unknown")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            if "headersSelected" in item:
                converted_item["headersSelected"] = item["headersSelected"]


            converted_payload.append(converted_item)
        except Exception as e:
            logger.error(f"Error processing item {item.get('columnName', 'Unknown')}: {e}")


    if print_converted_coords:
        print(f"WIDTH: {width}, Height: {height}")
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))

def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }

# Convert the JSON string back to a list of dictionaries, but only if it's a string
def safe_json_loads(item):
    if isinstance(item, str):
        try:
            return json.loads(item)
        except json.JSONDecodeError:
            return item  # Return the original string if it's not valid JSON
    return item  # Return the item as is if it's not a string




