# TODO - handle different colors

import os
import fitz  # PyMuPDF
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pandas as pd
from datetime import datetime


def check_blue_color(page, blue_rgb=(0, 0, 255), tolerance=0, show_image=False):
    """
    Check if a specific blue color exists in a PDF page past 60% of width and below 80% of height.

    Args:
        page: PyMuPDF page object
        blue_rgb: RGB tuple for the blue color to detect (default: #0000FF)
        tolerance: Color detection tolerance (default: 0)
        show_image: Whether to show the image with blue pixels highlighted (default: False)

    Returns:
        bool: True if blue color is detected in the specified region, False otherwise
        PIL.Image: The image with blue pixels highlighted (if show_image is True)
    """
    # Get the page as a pixmap (image)
    zoom = 2  # Increase resolution for better detection
    mat = fitz.Matrix(zoom, zoom)
    pix = page.get_pixmap(matrix=mat)

    # Convert to PIL Image
    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

    # Convert to numpy array for faster processing
    img_array = np.array(img)

    # Get image dimensions
    height, width = img_array.shape[:2]

    # Calculate the region boundaries
    width_threshold = int(width * 0.6)  # 60% of width
    height_threshold = int(height * 0.8)  # 80% of height

    # Check for blue color with tolerance
    blue_mask = (
        (np.abs(img_array[:, :, 0] - blue_rgb[0]) <= tolerance) &
        (np.abs(img_array[:, :, 1] - blue_rgb[1]) <= tolerance) &
        (np.abs(img_array[:, :, 2] - blue_rgb[2]) <= tolerance)
    )

    # Create a mask for the region we're interested in
    # (past 60% width and below 80% height)
    region_mask = np.zeros_like(blue_mask, dtype=bool)
    region_mask[0:height_threshold, width_threshold:] = True

    # Combine the region mask with the blue mask
    blue_in_region = blue_mask & region_mask

    # If we need to show the image
    if show_image:
        # Create a copy of the image to highlight blue pixels
        highlight_img = img.copy()
        highlight_array = np.array(highlight_img)

        # Make boundary lines MUCH more visible - very thick lines
        # Horizontal line at 80% height
        line_thickness = 20  # Increased from 5 to 20
        for i in range(line_thickness):
            if height_threshold-i >= 0:
                highlight_array[height_threshold-i, :, 0] = 255  # Red
                highlight_array[height_threshold-i, :, 1] = 0    # No green
                highlight_array[height_threshold-i, :, 2] = 0    # No blue

        # Vertical line at 60% width
        for i in range(line_thickness):
            if width_threshold+i < width:
                highlight_array[:, width_threshold+i, 0] = 255   # Red
                highlight_array[:, width_threshold+i, 1] = 0     # No green
                highlight_array[:, width_threshold+i, 2] = 0     # No blue

        # Add a border around the entire region of interest
        border_thickness = 10
        # Top border of region
        for i in range(border_thickness):
            if i < height:
                highlight_array[i, width_threshold:, 0] = 255  # Red
                highlight_array[i, width_threshold:, 1] = 255  # Yellow (Red + Green)
                highlight_array[i, width_threshold:, 2] = 0

        # Right border of region
        for i in range(border_thickness):
            if width-1-i >= 0:
                highlight_array[:height_threshold, width-1-i, 0] = 255  # Red
                highlight_array[:height_threshold, width-1-i, 1] = 255  # Yellow (Red + Green)
                highlight_array[:height_threshold, width-1-i, 2] = 0

        # Add text labels with larger font
        draw = ImageDraw.Draw(highlight_img)
        try:
            # Try to use a system font with larger size
            font = ImageFont.truetype("arial.ttf", 40)  # Increased from 20 to 40
        except:
            # Fall back to default font
            font = ImageFont.load_default()

        # Add labels with more visible colors
        draw.text((width_threshold + 20, 50), "60% Width", fill=(255, 0, 0), font=font)
        draw.text((50, height_threshold - 50), "80% Height", fill=(255, 0, 0), font=font)

        # Add a rectangle around the region of interest
        draw.rectangle(
            [(width_threshold, 0), (width, height_threshold)],
            outline=(255, 255, 0),  # Yellow outline
            width=5  # Thick outline
        )

        # Highlight blue pixels in the region with a green overlay
        blue_coords = np.where(blue_in_region)
        if len(blue_coords[0]) > 0:
            print(f"Found {len(blue_coords[0])} blue pixels in the target region")
            for y, x in zip(blue_coords[0], blue_coords[1]):
                # Make blue pixels more visible with larger markers
                marker_size = 10  # Increased from 2 to 10
                for dy in range(-marker_size, marker_size+1):
                    for dx in range(-marker_size, marker_size+1):
                        if 0 <= y+dy < height and 0 <= x+dx < width:
                            highlight_array[y+dy, x+dx] = [0, 255, 0]  # Green
        else:
            print("No blue pixels found in the target region")

        # Convert back to PIL Image
        highlight_img = Image.fromarray(highlight_array)

        return np.any(blue_in_region), highlight_img

    # Return True if any pixel in the region matches the blue color
    return np.any(blue_in_region)

def process_pdf(pdf_path, output_excel=None, show_first_page=False):
    """
    Process a PDF file to detect pages with blue color and mark them as revised.

    Args:
        pdf_path: Path to the PDF file
        output_excel: Path to save the Excel file (default: auto-generated)
        show_first_page: Whether to show the first page with blue pixels highlighted (default: False)

    Returns:
        DataFrame: Results with page numbers and revision status
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    # Generate default output filename if not provided
    if output_excel is None:
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_excel = f"{pdf_name}_revised_{timestamp}.xlsx"

    # Open the PDF file
    doc = fitz.open(pdf_path)
    total_pages = len(doc)

    print(f"Processing PDF: {pdf_path}")
    print(f"Total pages: {total_pages}")

    # Initialize results list
    results = []

    # Process each page
    for page_num in range(total_pages):
        page = doc[page_num]
        page_index = page_num + 1  # 1-based page numbering

        # Check if the page contains blue color in the specified region
        has_blue, page_image = check_blue_color(page, show_image=True)

        if has_blue:
            status = "Revised"
            print(f"Page {page_index}: {status} - Blue color detected in specified region")
            results.append({
                "Page Number": page_index,
                "Status": status,
                "Detection Date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

            # Save the image of the first page where blue is detected
            image_filename = f"{pdf_name}_page{page_index}_blue_detection.png"
            print(f"Saving detection image to: {image_filename}")
            page_image.save(image_filename)

            # Close the document
            doc.close()

            # Create DataFrame from results
            df = pd.DataFrame(results)

            # Save to Excel if we have any revised pages
            if not df.empty:
                df.to_excel(output_excel, index=False)
                print(f"\nResults saved to: {output_excel}")
                print(f"Total revised pages: {len(df)}")
            else:
                print("\nNo revised pages found.")

            # Return after saving the first page with blue
            return df

    # Close the document
    doc.close()

    # Create DataFrame from results
    df = pd.DataFrame(results)

    # Save to Excel if we have any revised pages
    if not df.empty:
        df.to_excel(output_excel, index=False)
        print(f"\nResults saved to: {output_excel}")
        print(f"Total revised pages: {len(df)}")
    else:
        print("\nNo revised pages found.")

    return df

def display_revised_pages(df):
    """
    Display only the pages marked as revised.

    Args:
        df: DataFrame with the results
    """
    if df.empty:
        print("No revised pages to display.")
        return

    print("\nRevised Pages:")
    print("=" * 60)
    for _, row in df.iterrows():
        print(f"Page {row['Page Number']} - {row['Status']} - {row['Detection Date']}")
    print("=" * 60)


# Simple function to run without command line arguments
def plugin_detect_color_in_pdf(pdf_path, output_excel=None, tolerance=5, show_first_page=True):
    """
    Run the blue color detection without command line arguments.

    Args:
        pdf_path: Path to the PDF file
        output_excel: Path to save the Excel file (optional)
        tolerance: Color detection tolerance (default: 5)
        show_first_page: Whether to show the first page with blue pixels highlighted (default: True)

    Returns:
        DataFrame: Results with page numbers and revision status
    """
    try:
        # Process the PDF
        results_df = process_pdf(pdf_path, output_excel, show_first_page)

        # Display only the revised pages
        display_revised_pages(results_df)

        return results_df

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

if __name__ == "__main__":
    pdf_path = r"C:/Drawings/24-march-charlie-test-files/sources/TR-001 Combined.pdf"
    results = plugin_detect_color_in_pdf(pdf_path)
