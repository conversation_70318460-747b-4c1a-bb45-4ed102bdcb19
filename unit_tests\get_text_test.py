
import unicodedata
import fitz
import json
from src.utils.logger import logger
import pandas as pd
import time
from pprint import pprint as pp



'''
Add more fine grained control to the data structure. Get the individual words and merge it with the span. 
Can use this for tables that span multiple columns
'''

is_testing = True


# logger = logging.getLogger(__name__)
#
# logger.setLevel(logging.DEBUG)
#
# # Create a console handler
# console_handler = logging.StreamHandler()
#
# # Set the log level for the handler
# console_handler.setLevel(logging.DEBUG)
#
# # Create formatters and add it to the handler
# log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# console_handler.setFormatter(log_format)
#
# # Add the handler to the logger
# logger.add<PERSON><PERSON><PERSON>(console_handler)


def clean_for_json(data):
    if isinstance(data, dict):
        return {key: clean_for_json(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [clean_for_json(item) for item in data]
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        return str(data)  # Convert any other types to string

def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

# Function to replace non-ASCII characters using normalization
def replace_non_ascii(text):
    # Normalize the text to NFKD (Normalization Form KD)
    normalized_text = unicodedata.normalize('NFKD', text)
    # Encode to ASCII and ignore non-ASCII characters
    ascii_text = normalized_text.encode('ascii', 'ignore').decode('ascii')
    return ascii_text

def text_to_dataframe(all_text_blocks):
    data = []
    for block in all_text_blocks:
        if isinstance(block, dict) and block.get("type") == 0:  # Text block
            for line in block.get("lines", []):
                for span in line.get("spans", []):
                    words_data = []
                    for word in span.get("words", []):
                        words_data.append({
                            "text": word.get("text", ""),
                            "bbox": word.get("bbox", []),
                            "origin": word.get("origin", ())
                        })
                    
                    data.append({
                        "page_num": block.get("page_num", 0),
                        "block_no": block.get("number", 0),
                        "line_no": line.get("number", 0),
                        "span_no": span.get("number", 0),
                        "span_text": span.get("text", ""),
                        "span_bbox": span.get("bbox", []),
                        "span_origin": span.get("origin", ()),
                        "line_bbox": line.get("bbox", []),
                        "block_bbox": block.get("bbox", []),
                        "words": json.dumps(words_data)  # Store as JSON string for DataFrame compatibility
                    })
    
    df = pd.DataFrame(data)
    
    # Convert the JSON string back to a list of dictionaries
    df['words'] = df['words'].apply(json.loads)
    
    return df



def get_page_text_og(page, rotation_matrix): 
    # rotation = page.rotation
    # rotation_matrix = page.rotation_matrix  # Get the page's rotation matrix

    try:
        text_blocks = page.get_text("dict")["blocks"]
        
        # print("\n\nTEXT BLOCKS ORIGINAL: \n")
        # pp(text_blocks)
        
        original_text_blocks_clean = clean_for_json(text_blocks)
        
        for block in text_blocks:
            if block["type"] == 0:  # Text block
                # Adjust the bounding box of the entire block
                block['bbox'] = adjust_bbox(block['bbox'], rotation_matrix)
                
                for line in block["lines"]:
                    # Adjust the bounding box of the entire line
                    line['bbox'] = adjust_bbox(line['bbox'], rotation_matrix)
                    
                    for span in line["spans"]:
                        # Adjust the bounding box of each span
                        span['bbox'] = adjust_bbox(span['bbox'], rotation_matrix)

                        # If needed, also adjust other coordinate-related attributes like 'origin'
                        origin_x, origin_y = span['origin']
                        origin_point = fitz.Point(origin_x, origin_y) * rotation_matrix
                        span['origin'] = (origin_point.x, origin_point.y)

                        # Replace non-ASCII characters in the span text
                        span['text'] = replace_non_ascii(span['text'])
                        
        #transformed_text_blocks_clean = clean_for_json(text_blocks)
        
        # print("\n\nTEXT BLOCKS TRANSFORMED: \n")
        # pp(text_blocks)

        if is_testing:
            with open("original_text_blocks.json", "w") as orig_file:
                json.dump(original_text_blocks_clean, orig_file, indent=4)
        
            # with open("transformed_text_blocks.json", "w") as trans_file:
            #     json.dump(transformed_text_blocks_clean, trans_file, indent=4)

        return text_blocks

    except Exception as e:
        logger.error(f"Error getting page text: {e}", exc_info=True)
        return {}

def get_page_text(page, rotation_matrix):
    try:
        text_blocks = page.get_text("rawdict", flags=fitz.TEXTFLAGS_TEXT, sort=True)["blocks"]
        
        original_text_blocks_clean = clean_for_json(text_blocks)
        
        for block in text_blocks:
            if block["type"] == 0:  # Text block

                block['bbox'] = adjust_bbox(block['bbox'], rotation_matrix)

                
                
                for line in block["lines"]:
                    line['bbox'] = adjust_bbox(line['bbox'], rotation_matrix)
                    
                    for span in line["spans"]:
                        span['bbox'] = adjust_bbox(span['bbox'], rotation_matrix)

                        origin_x, origin_y = span['origin']
                        origin_point = fitz.Point(origin_x, origin_y) * rotation_matrix
                        span['origin'] = (origin_point.x, origin_point.y)

                        # Process individual characters to form words
                        words = []
                        current_word = ""
                        current_word_bbox = None
                        
                        for char in span['chars']:
                            char['bbox'] = adjust_bbox(char['bbox'], rotation_matrix)
                            
                            if not current_word_bbox:
                                current_word_bbox = list(char['bbox'])
                            
                            if char['c'] == " " or char['c'] == "\n":
                                if current_word:
                                    words.append({
                                        'text': current_word,
                                        'bbox': current_word_bbox,
                                        'origin': (current_word_bbox[0], current_word_bbox[1])
                                    })
                                    current_word = ""
                                    current_word_bbox = None
                            else:
                                current_word += char['c']
                                current_word_bbox[2] = max(current_word_bbox[2], char['bbox'][2])
                                current_word_bbox[3] = max(current_word_bbox[3], char['bbox'][3])
                        
                        # Add the last word if it exists
                        if current_word:
                            words.append({
                                'text': current_word,
                                'bbox': current_word_bbox,
                                'origin': (current_word_bbox[0], current_word_bbox[1])
                            })
                        
                        span['words'] = words
                        span['text'] = replace_non_ascii(" ".join(word['text'] for word in words))

                        # Check if the span contains "Globe" (case insensitive)
                        if "globe" in span['text'].lower():
                            print("\n\n--------------\nSpan containing 'Globe':\n\n----------------")
                            print(json.dumps(span, indent=2))
                            print("-" * 50)  # Separator for readability


        if is_testing:
            with open("original_text_blocks.json", "w") as orig_file:
                json.dump(original_text_blocks_clean, orig_file, indent=4)

        return text_blocks

    except Exception as e:
        logger.error(f"Error getting page text: {e}", exc_info=True)
        return {}


def process_pdf(pdf_path):
    start_time = time.time()
    
    # Open the PDF
    doc = fitz.open(pdf_path)
    try:
        doc.scrub()
    except Exception as e:
        print(f"\n\nScrub Failed: {e}")
    
    all_text_blocks = []
    
    # Loop through all pages
    for page_num in range(len(doc)):
        page = doc[page_num]
        rotation_matrix = page.rotation_matrix
        
        # Get text blocks from the page
        text_blocks = get_page_text(page, rotation_matrix)
        
        # Add page number to each block for reference
        for block in text_blocks:
            block['page_num'] = page_num + 1
        
        all_text_blocks.extend(text_blocks)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    return all_text_blocks, processing_time



# Path to your PDF
pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Classic Industrial\Demo CO\Modified\Test\One Page.pdf"

# Process the PDF
text_blocks, time_taken = process_pdf(pdf_path)



span_df = text_to_dataframe(text_blocks)

print(f"Processing completed in {time_taken:.2f} seconds")
print(f"Total words extracted: {len(span_df)}")
print("\nFirst few rows of the word DataFrame:")
print(span_df.head())

# If you want to see the full structure of the first few words
# print("\nDetailed structure of the first word:")
# pp(span_df.iloc[0].to_dict())

print("Exporting..")
span_df.to_excel("SPAN DF.xlsx")