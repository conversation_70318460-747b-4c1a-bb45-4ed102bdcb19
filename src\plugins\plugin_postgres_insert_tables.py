import pandas as pd
from psycopg2.extras import execute_values
import re

# from src.atom.pg_database.schemas.base.insert_tables import INSERT_BOM, INSERT_GENERAL
from src.atom.pg_database.schemas.base.sqlite_column_mapping import BOM_TABLE_MAPPING, GENERAL_TABLE_MAPPING
from src.atom.pg_database import pg_connection


# Define column length limits based on PostgreSQL schema
BOM_COLUMN_LIMITS = {
    # Text fields have no length limit
    'material_description': None,  # TEXT type has no limit
    'normalized_description': None,  # TEXT type has no limit
    'notes': None,  # TEXT type has no limit

    # VARCHAR fields with their limits
    'pos': 50,
    'component_category': 100,
    'mtrl_category': 100,
    'size': 50,
    'ident': 100,
    'item': 255,
    'tag': 100,
    'status': 50,
    'nb': 50,
    'fluid': 100,
    'clean_spec': 100,
    'line_number': 100,
    'rfq_scope': 100,
    'general_category': 100,
    'unit_of_measure': 50,
    'material': 100,
    'abbreviated_material': 50,
    'technical_standard': 50,
    'astm': 50,
    'grade': 50,
    'rating': 50,
    'schedule': 50,
    'coating': 50,
    'forging': 50,
    'ends': 50,
    'item_tag': 100,
    'tie_point': 100,
    'pipe_category': 50,
    'valve_type': 50,
    'fitting_category': 50,
    'weld_category': 50,
    'bolt_category': 50,
    'gasket_category': 50,
    'validated_by': 100,
    'created_by': 100,
    'updated_by': 100,
}

GENERAL_COLUMN_LIMITS = {
    # Text fields have no length limit
    # VARCHAR fields with their limits
    'avg_elevation': 50,
    'client_document_id': 100,
    'design_code': 100,
    'document_id': 100,
    'document_title': 255,
    'drawing': 100,
    'elevation': 255,
    'flange_id': 100,
    'heat_trace': 50,
    'insulation_spec': 100,
    'insulation_thickness': 50,
    'iso_number': 100,
    'iso_type': 100,
    'line_number': 100,
    'max_elevation': 50,
    'medium_code': 100,
    'min_elevation': 50,
    'mod_date': 50,
    'paint_spec': 100,
    'pid': 100,
    'pipe_spec': 100,
    'pipe_standard': 100,
    'process_line_list': 100,
    'process_unit': 100,
    'project_no': 100,
    'project_name': 255,
    'pwht': 50,
    'revision': 50,
    'sequence': 50,
    'service': 100,
    'sheet': 50,
    'sys_build': 100,
    'sys_layout_valid': 50,
    'sys_document': 255,
    'sys_document_name': 255,
    'sys_filename': 255,
    'sys_path': 255,
    'system': 100,
    'total_sheets': 50,
    'unit': 50,
    'vendor_document_id': 100,
    'weld_id': 100,
    'weld_class': 100,
    'x_coord': 50,
    'xray': 50,
    'y_coord': 50,
    'paint_color': 100,
    'cwp': 100,
}

RFQ_COLUMN_LIMITS = BOM_COLUMN_LIMITS.copy()

def validate_string_lengths(df, column_limits, table_name):
    """
    Validate string column lengths against PostgreSQL limits.
    Returns a list of validation errors.
    """
    errors = []

    for col, max_length in column_limits.items():
        if col in df.columns:
            # Check if column contains string values
            if df[col].dtype == 'object':
                # Find values that exceed the limit
                mask = df[col].astype(str).str.len() > max_length
                if mask.any():
                    # Get the indices of rows with too-long values
                    problem_indices = df.index[mask].tolist()
                    for idx in problem_indices:
                        value = df.loc[idx, col]
                        if value is not None:  # Skip None values
                            errors.append({
                                'table': table_name,
                                'column': col,
                                'max_length': max_length,
                                'actual_length': len(str(value)),
                                'value': str(value)[:50] + '...' if len(str(value)) > 50 else str(value),
                                'row_index': idx
                            })

    return errors


def insert_bom(conn, bom_data: pd.DataFrame):
    # Define the valid columns from the BOM table
    valid_columns = [
        'id', 'pdf_id', 'pdf_page', 'project_id', 'profile_id', 'rfq_ref_id', 'gen_ref_id',
        'pos', 'material_description', 'normalized_description', 'component_category',
        'mtrl_category', 'size', 'size1', 'size2', 'ident', 'item', 'tag', 'quantity',
        'status', 'nb', 'fluid', 'clean_spec', 'line_number', 'rfq_scope',
        'general_category', 'unit_of_measure', 'material', 'abbreviated_material',
        'technical_standard', 'astm', 'grade', 'rating', 'schedule', 'coating',
        'forging', 'ends', 'item_tag', 'tie_point', 'pipe_category', 'valve_type',
        'fitting_category', 'weld_category', 'bolt_category', 'gasket_category',
        'notes', 'deleted', 'ignore_item', 'validated_date', 'validated_by',
        'calculated_eq_length', 'calculated_area', 'created_by', 'updated_by',
        'created_at', 'updated_at'
    ]

    # Define numeric columns that need type conversion
    numeric_columns = ['size1', 'size2', 'quantity', 'calculated_eq_length', 'calculated_area']

    # Map to pg column name
    bom_data = bom_data.rename(columns=BOM_TABLE_MAPPING)

    # Convert numeric columns to appropriate types
    for col in numeric_columns:
        if col in bom_data.columns:
            try:
                # Convert to numeric, coerce errors to NaN
                bom_data[col] = pd.to_numeric(bom_data[col], errors='coerce')
                # Replace NaN with None for SQL compatibility
                bom_data[col] = bom_data[col].where(pd.notnull(bom_data[col]), None)

                # Additional check to ensure no NaN values slip through
                if bom_data[col].isna().any():
                    print(f"Warning: Column {col} contains NaN values that will be converted to None", flush=True)
            except Exception as e:
                print(f"Error converting column {col}: {e}")

    # Filter the DataFrame to include only valid columns that exist in the DataFrame
    existing_valid_columns = [col for col in valid_columns if col in bom_data.columns]
    filtered_df = bom_data[existing_valid_columns]

    # Print information about removed columns
    removed_columns = [col for col in bom_data.columns if col not in valid_columns]
    if removed_columns:
        print(f"Removed columns not in BOM table: {', '.join(removed_columns)}", flush=True)

    # Validate string lengths
    validation_errors = validate_string_lengths(filtered_df, BOM_COLUMN_LIMITS, 'bom')
    if validation_errors:
        print(f"Found {len(validation_errors)} string length validation errors:")
        for i, error in enumerate(validation_errors[:10]):  # Show first 10 errors
            print(f"Error {i+1}: Column '{error['column']}' exceeds max length ({error['actual_length']} > {error['max_length']})")
            print(f"  Value: {error['value']}")
            print(f"  Row index: {error['row_index']}")
        if len(validation_errors) > 10:
            print(f"... and {len(validation_errors) - 10} more errors")
        raise ValueError(f"String length validation failed. {len(validation_errors)} columns exceed PostgreSQL length limits.")

    # Get total row count for progress reporting
    total_rows = len(filtered_df)
    print(f"Preparing to insert {total_rows} rows into BOM table")

    # Process in batches for better performance
    batch_size = 1000  # Adjust based on your database and memory constraints

    with conn.cursor() as cursor:
        # Get column names
        cols = ','.join(existing_valid_columns)

        # SQL query without RETURNING for better performance
        query_template = f"INSERT INTO public.bom({cols}) VALUES %s"

        # Process in batches
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = filtered_df.iloc[start_idx:end_idx]

            print(f"Processing batch {start_idx//batch_size + 1}/{(total_rows+batch_size-1)//batch_size}: rows {start_idx+1}-{end_idx}")

            # Convert batch to list of tuples with NaN handling
            batch_data = []
            for row in batch_df.to_numpy():
                # Replace NaN with None
                processed_row = tuple(None if pd.isna(val) else val for val in row)
                batch_data.append(processed_row)

            try:
                # Use execute_values for much faster bulk inserts
                execute_values(cursor, query_template, batch_data)

                # Commit each batch
                conn.commit()
                print(f"Successfully inserted batch {start_idx//batch_size + 1}")
            except Exception as e:
                conn.rollback()
                print(f"Error in batch {start_idx//batch_size + 1}: {e}")
                # Print a sample row for debugging
                if batch_data:
                    print("\nSample error data row:")
                    sample_dict = dict(zip(existing_valid_columns, batch_data[0]))
                    for k, v in sample_dict.items():
                        print(f"  {k}: {v} (type: {type(v).__name__})")
                raise

    print(f"Completed inserting {total_rows} rows into BOM table")


def insert_general(conn, general_data: pd.DataFrame):
    # Define the valid columns from the general table
    valid_columns = [
        'id', 'project_id', 'pdf_id', 'pdf_page', 'annot_markups', 'area', 'avg_elevation',
        'block_coordinates', 'client_document_id', 'coordinates', 'design_code',
        'document_description', 'document_id', 'document_title', 'drawing', 'elevation',
        'flange_id', 'heat_trace', 'insulation_spec', 'insulation_thickness', 'iso_number',
        'iso_type', 'line_number', 'max_elevation', 'medium_code', 'min_elevation',
        'mod_date', 'paint_spec', 'pid', 'pipe_spec', 'pipe_standard',
        'process_line_list', 'process_unit', 'project_no', 'project_name',
        'pwht', 'revision', 'sequence', 'service', 'sheet', 'size',
        'sys_build', 'sys_layout_valid', 'sys_document', 'sys_document_name',
        'sys_filename', 'sys_path', 'system', 'total_sheets', 'unit',
        'vendor_document_id', 'weld_id', 'weld_class', 'x_coord', 'xray',
        'y_coord', 'paint_color', 'cwp', 'length', 'calcualated_area',
        'calculated_eq_length', 'elbows_90', 'elbows_45', 'bevels', 'tees',
        'reducers', 'caps', 'flanges', 'valves_flanged', 'valves_welded',
        'cut_outs', 'supports', 'bends', 'union_couplings', 'expansion_joints',
        'field_welds', 'created_at', 'updated_at'
    ]

    # Define numeric columns that need type conversion
    numeric_columns = ['size', 'length', 'calcualated_area', 'calculated_eq_length',
                      'elbows_90', 'elbows_45', 'bevels', 'tees', 'reducers',
                      'caps', 'flanges', 'valves_flanged', 'valves_welded',
                      'cut_outs', 'supports', 'bends', 'union_couplings',
                      'expansion_joints', 'field_welds']

    # Map to pg column name
    general_data = general_data.rename(columns=GENERAL_TABLE_MAPPING)

    # Convert numeric columns to appropriate types
    for col in numeric_columns:
        if col in general_data.columns:
            try:
                # Convert to numeric, coerce errors to NaN
                general_data[col] = pd.to_numeric(general_data[col], errors='coerce')
                # Replace NaN with None for SQL compatibility
                general_data[col] = general_data[col].where(pd.notnull(general_data[col]), None)

                # Additional check to ensure no NaN values slip through
                if general_data[col].isna().any():
                    print(f"Warning: Column {col} contains NaN values that will be converted to None")
            except Exception as e:
                print(f"Error converting column {col}: {e}")

    # Filter the DataFrame to include only valid columns that exist in the DataFrame
    existing_valid_columns = [col for col in valid_columns if col in general_data.columns]
    filtered_df = general_data[existing_valid_columns]

    # Print information about removed columns
    removed_columns = [col for col in general_data.columns if col not in valid_columns]
    if removed_columns:
        print(f"Removed columns not in general table: {', '.join(removed_columns)}")

    # Validate string lengths
    validation_errors = validate_string_lengths(filtered_df, GENERAL_COLUMN_LIMITS, 'general')
    if validation_errors:
        print(f"Found {len(validation_errors)} string length validation errors:")
        for i, error in enumerate(validation_errors[:10]):  # Show first 10 errors
            print(f"Error {i+1}: Column '{error['column']}' exceeds max length ({error['actual_length']} > {error['max_length']})")
            print(f"  Value: {error['value']}")
            print(f"  Row index: {error['row_index']}")
        if len(validation_errors) > 10:
            print(f"... and {len(validation_errors) - 10} more errors")
        raise ValueError(f"String length validation failed. {len(validation_errors)} columns exceed PostgreSQL length limits.")

    # Get total row count for progress reporting
    total_rows = len(filtered_df)
    print(f"Preparing to insert {total_rows} rows into general table")

    # Process in batches for better performance
    batch_size = 1000  # Adjust based on your database and memory constraints

    with conn.cursor() as cursor:
        # Get column names
        cols = ','.join(existing_valid_columns)

        # SQL query without RETURNING for better performance
        query_template = f"INSERT INTO public.general({cols}) VALUES %s"

        # Process in batches
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = filtered_df.iloc[start_idx:end_idx]

            print(f"Processing batch {start_idx//batch_size + 1}/{(total_rows+batch_size-1)//batch_size}: rows {start_idx+1}-{end_idx}")

            # Convert batch to list of tuples with NaN handling
            batch_data = []
            for row in batch_df.to_numpy():
                # Replace NaN with None
                processed_row = tuple(None if pd.isna(val) else val for val in row)
                batch_data.append(processed_row)

            try:
                # Use execute_values for much faster bulk inserts
                execute_values(cursor, query_template, batch_data)

                # Commit each batch
                conn.commit()
                print(f"Successfully inserted batch {start_idx//batch_size + 1}")
            except Exception as e:
                conn.rollback()
                print(f"Error in batch {start_idx//batch_size + 1}: {e}")
                # Print a sample row for debugging
                if batch_data:
                    print("\nSample error data row:")
                    sample_dict = dict(zip(existing_valid_columns, batch_data[0]))
                    for k, v in sample_dict.items():
                        print(f"  {k}: {v} (type: {type(v).__name__})")
                raise

    print(f"Completed inserting {total_rows} rows into general table")


def insert_rfq(conn, rfq_data: pd.DataFrame, auto_commit=False):
    """
    Insert RFQ data into the public.atem_rfq table.
    
    Args:
        conn: PostgreSQL connection
        rfq_data: DataFrame containing RFQ data
        auto_commit: Whether to commit after each batch (default: False)
    """
    # Define the valid columns from the RFQ table (similar to BOM)
    valid_columns = [
        'id', 'project_id', 'profile_id', 'rfq_input_id',
        'material_description', 'normalized_description', 'size', 'size1', 'size2', 'quantity',
        'rfq_scope', 'general_category', 'unit_of_measure', 'material', 'abbreviated_material',
        'technical_standard', 'astm', 'grade', 'rating', 'schedule', 'coating',
        'forging', 'ends', 'item_tag', 'tie_point', 'pipe_category', 'valve_type',
        'fitting_category', 'weld_category', 'bolt_category', 'gasket_category',
        'notes', 'deleted', 'ignore_item', 'validated_date', 'validated_by',
        'calculated_eq_length', 'calculated_area', 'created_by', 'updated_by',
        'created_at', 'updated_at'
    ]

    # Define numeric columns that need type conversion
    numeric_columns = ['size1', 'size2', 'quantity', 'calculated_eq_length', 'calculated_area']

    # CHANGE: Add this section to properly initialize category columns
    # Ensure category fields are not defaulted improperly
    if 'rfq_scope' not in rfq_data.columns:
        rfq_data['rfq_scope'] = None  # Don't default to 'Pipe'

    # Preserve existing field categories if available
    for category_field in ['fitting_category', 'valve_type', 'pipe_category', 
                        'bolt_category', 'gasket_category', 'general_category']:
        if category_field not in rfq_data.columns:
            rfq_data[category_field] = None
    
    # Map to pg column name if there's a mapping defined for RFQ
    # If RFQ mapping doesn't exist yet, could use BOM mapping if columns are identical
    if 'RFQ_TABLE_MAPPING' in globals():
        rfq_data = rfq_data.rename(columns=RFQ_TABLE_MAPPING)
    elif BOM_TABLE_MAPPING:  # If columns are similar enough, could use BOM mapping
        rfq_data = rfq_data.rename(columns=BOM_TABLE_MAPPING)

    # Convert numeric columns to appropriate types
    for col in numeric_columns:
        if col in rfq_data.columns:
            try:
                # Convert to numeric, coerce errors to NaN
                rfq_data[col] = pd.to_numeric(rfq_data[col], errors='coerce')
                # Replace NaN with None for SQL compatibility
                rfq_data[col] = rfq_data[col].where(pd.notnull(rfq_data[col]), None)

                # Additional check to ensure no NaN values slip through
                if rfq_data[col].isna().any():
                    print(f"Warning: Column {col} contains NaN values that will be converted to None", flush=True)
            except Exception as e:
                print(f"Error converting column {col}: {e}")

    # Filter the DataFrame to include only valid columns that exist in the DataFrame
    existing_valid_columns = [col for col in valid_columns if col in rfq_data.columns]
    filtered_df = rfq_data[existing_valid_columns]

    # Print information about removed columns
    removed_columns = [col for col in rfq_data.columns if col not in valid_columns]
    if removed_columns:
        print(f"Removed columns not in RFQ table: {', '.join(removed_columns)}", flush=True)

    # Validate string lengths
    validation_errors = validate_string_lengths(filtered_df, RFQ_COLUMN_LIMITS, 'rfq')
    if validation_errors:
        print(f"Found {len(validation_errors)} string length validation errors:")
        for i, error in enumerate(validation_errors[:10]):  # Show first 10 errors
            print(f"Error {i+1}: Column '{error['column']}' exceeds max length ({error['actual_length']} > {error['max_length']})")
            print(f"  Value: {error['value']}")
            print(f"  Row index: {error['row_index']}")
        if len(validation_errors) > 10:
            print(f"... and {len(validation_errors) - 10} more errors")
        raise ValueError(f"String length validation failed. {len(validation_errors)} columns exceed PostgreSQL length limits.")

    # Re-initialize PostgreSQL session parameters to ensure they're set correctly
    initialize_session_parameters(conn)

    # Get total row count for progress reporting
    total_rows = len(filtered_df)
    print(f"Preparing to insert {total_rows} rows into RFQ table")

    # Process in batches for better performance
    batch_size = 1000  # Adjust based on your database and memory constraints

    with conn.cursor() as cursor:
        # Get column names
        cols = ','.join(existing_valid_columns)

        # Define key columns for conflict detection
        # These columns together should uniquely identify a record
        key_columns = ['project_id', 'material_description', 'size1', 'size2']
        valid_key_columns = [col for col in key_columns if col in existing_valid_columns]
        
        if not valid_key_columns:
            print("Warning: No key columns found for duplicate detection. Will insert all rows.")
            # SQL query without duplicate detection
            query_template = f"INSERT INTO public.atem_rfq({cols}) VALUES %s"
        else:
            # Create ON CONFLICT clause for duplicate detection
            conflict_keys = ', '.join(valid_key_columns)
            
            # For upsert, we need to define what happens on conflict
            # Here we'll exclude id, created_at from the update as those should remain unchanged
            update_columns = [col for col in existing_valid_columns 
                             if col not in ['id', 'created_at'] + valid_key_columns]
            
            if update_columns:
                # Create the SET clause for updating existing records
                set_clause = ', '.join([f"{col} = EXCLUDED.{col}" for col in update_columns])
                query_template = f"""
                    INSERT INTO public.atem_rfq({cols})
                    VALUES %s
                    ON CONFLICT ({conflict_keys})
                    DO UPDATE SET {set_clause}
                """
            else:
                # If there are no columns to update, just do nothing on conflict
                query_template = f"""
                    INSERT INTO public.atem_rfq({cols})
                    VALUES %s
                    ON CONFLICT ({conflict_keys})
                    DO NOTHING
                """
            
            print(f"Using upsert with conflict key columns: {valid_key_columns}")

        # Process in batches
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = filtered_df.iloc[start_idx:end_idx]

            print(f"Processing batch {start_idx//batch_size + 1}/{(total_rows+batch_size-1)//batch_size}: rows {start_idx+1}-{end_idx}")

            # Convert batch to list of tuples with NaN handling
            batch_data = []
            for row in batch_df.to_numpy():
                # Replace NaN with None
                processed_row = tuple(None if pd.isna(val) else val for val in row)
                batch_data.append(processed_row)

            try:
                # Use execute_values for much faster bulk inserts
                execute_values(cursor, query_template, batch_data)

                # Commit each batch only if auto_commit is True
                if auto_commit:
                    conn.commit()
                    print(f"Successfully inserted batch {start_idx//batch_size + 1}")
                else:
                    print(f"Successfully processed batch {start_idx//batch_size + 1} (not committed)")
            except Exception as e:
                conn.rollback()
                print(f"Error in batch {start_idx//batch_size + 1}: {e}")
                # Print a sample row for debugging
                if batch_data:
                    print("\nSample error data row:")
                    sample_dict = dict(zip(existing_valid_columns, batch_data[0]))
                    for k, v in sample_dict.items():
                        print(f"  {k}: {v} (type: {type(v).__name__})")
                raise

    print(f"Completed inserting {total_rows} rows into RFQ table")


def insert_spool(spool_data):
    pass


def insert_spec(spec_data):
    pass


def insert_outlier(outlier_data):
    pass


def initialize_session_parameters(conn):
    """
    Initialize PostgreSQL session parameters to prevent trigger errors.
    These parameters are used by database triggers.
    """
    with conn.cursor() as cursor:
        # Set sync_in_progress parameters to false
        cursor.execute("SELECT set_config('sync_in_progress.rfq_bom', 'false', false)")
        cursor.execute("SELECT set_config('sync_in_progress.input_rfq', 'false', false)")
    # Commit to ensure parameters are saved
    conn.commit()
    print("Session parameters initialized successfully.")


def plugin_insert_tables(project_id: int = 0,
                         profile_id: int = 0,
                         bom_data = None,
                         general_data = None,
                         rfq_data = None,
                         spool_data = None,
                         spec_data = None,
                         outlier_data = None,
                         commit_bom: bool = True,
                         commit_general: bool = True,
                         commit_spool: bool = True,
                         commit_spec: bool = True,
                         commit_outlier: bool = True,
                         commit_rfq: bool = True):
    """
    Inserts data into PostgreSQL tables.
    """
    print("Inserts data into PostgreSQL tables.")

    project_id = int(project_id)
    if project_id <= 0:
        print("Invalid project ID. No data committed.")
        return False

    if profile_id <= 0:
        print("Invalid profile ID. No data committed.")
        return False

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")
            
            # Initialize session parameters at the beginning
            initialize_session_parameters(conn)
            
            if commit_rfq:
                if rfq_data is not None and not rfq_data.empty:
                    rfq_data["project_id"] = project_id
                    rfq_data["profile_id"] = profile_id
                    insert_rfq(conn, rfq_data, auto_commit=False)
                    # Add explicit commit to ensure data is saved
                    conn.commit()
                    print("RFQ data committed.")
                else:
                    print("RFQ data is empty or None. No data committed")

            if commit_bom:
                if bom_data is not None and not bom_data.empty:
                    bom_data["project_id"] = project_id
                    bom_data["profile_id"] = profile_id
                    insert_bom(conn, bom_data)
                    # Add explicit commit to ensure data is saved
                    conn.commit()
                    print("BOM data committed.")
                else:
                    print("BOM data is empty or None. No data committed")

            if commit_general:
                if general_data is not None and not general_data.empty:
                    general_data["project_id"] = project_id
                    general_data["profile_id"] = profile_id
                    insert_general(conn, general_data)
                    # Add explicit commit to ensure data is saved
                    conn.commit()
                    print("General data committed.")
                else:
                    print("General data is empty or None. No data committed")

    except Exception as e:
        print(f"Failed to insert data: {e}")
        return False

    return True