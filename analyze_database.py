"""
For purposes of cleaning the database
"""

import sqlite3
import pandas as pd

db_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\ais_database.db"

def connect():
    return sqlite3.connect(db_path)

def get_project_source_map(projectId: int, filename: str) -> dict:
    """Returns a dictionary of {page_number: pdf_id} given a ProjectSource record"""
    result = {}
    with connect() as conn:
        statement = """SELECT id, page_number FROM PDFStorage WHERE project_id=? AND originalFilename=? ORDER BY page_number, id ASC"""
        df = pd.read_sql_query(statement, conn, params=(projectId, filename, ))
        for r in df.itertuples():
            result.setdefault(r.page_number, r.id)
    return result


pdf = r"C:/Drawings/SanMateoOCR/San Mateo Midstream_Black River_ISOs.pdf"
res = get_project_source_map(1, pdf)
# print(res)


from src.atom.dbManager import DatabaseManager

a = DatabaseManager().get_source_pdf_id_map(1, pdf, None)
print(a)

def get_pdf_storage_id(project_id, pdf_path, page_number):
    """Return the lowest PDFStorage pdf_id linked to a ProjeectSource record"""
    with connect() as conn:
        cursor = conn.cursor()
        cursor.execute('''SELECT id FROM PDFStorage WHERE project_id=? AND originalFilename=? AND page_number=? ORDER BY page_number, id ASC''',
                        (project_id, pdf_path, page_number))
        result = cursor.fetchall()
        if len(result) > 0:
            return result[0][0]

    return None

# pdf_id = get_pdf_storage_id(1, r"C:/Drawings/SanMateoOCR/San Mateo Midstream_Black River_ISOs.pdf", 1)

# print(pdf_id)