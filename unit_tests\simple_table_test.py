#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Table Extraction Test Harness

This script creates a simple test harness for the table extraction logic in get_tables_test.py.
It imports and calls the functions from get_tables_test.py without modifying the original file.

The script handles:
1. Loading data from feather files
2. Loading and converting ROI data from JSON files
3. Converting relative coordinates to absolute coordinates
4. Extracting tables using the existing table extraction logic
5. Saving extracted tables to CSV files

Usage:
    Simply run this script directly from the IDE by clicking the "Run" button.

    The script uses hardcoded default values:
    - Feather file: C:\\Users\\<USER>\\Documents\\EXC_0015\\AQT\\data.feather
    - ROI JSON file: C:\\Users\\<USER>\\Documents\\EXC_0015\\AQT\\roi_AQT.json
    - Page number: 1

    To use different values, modify the variables at the bottom of the script.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import ast
from datetime import datetime

# Add the repository root to the Python path to fix import issues
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Constants
TABLE_NAMES = ["bom", "ifc", "spec", "spool", "generic_1", "generic_2"]

def load_feather_data(feather_path, page_num=1):
    """
    Load data from a feather file and filter for a specific page.

    This function:
    1. Loads the raw data from the feather file
    2. Filters the data for the specified page
    3. Ensures coordinates are in the correct format

    Args:
        feather_path (str): Path to the feather file containing PDF text data
        page_num (int): Page number to filter (1-based)

    Returns:
        pd.DataFrame: DataFrame containing the filtered data for the specified page
    """
    print(f"Loading data from {feather_path}")
    try:
        # Load the raw data
        raw_data_df = pd.read_feather(feather_path)
        print(f"Loaded {len(raw_data_df)} rows from feather file")

        # Filter for the specified page
        page_data = raw_data_df[raw_data_df['pdf_page'] == page_num]
        print(f"Found {len(page_data)} rows for page {page_num}")

        # Handle coordinates to ensure they're in the correct format
        page_data = handle_coordinates(page_data)

        return page_data
    except Exception as e:
        print(f"Error loading feather data: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def handle_coordinates(df):
    """
    Ensure coordinates are in the correct format for table extraction.

    The table extraction code expects coordinates to be in a specific format:
    - A tuple of 4 floats: (x0, y0, x1, y1)

    This function converts coordinates from various formats (numpy arrays, strings, etc.)
    to the expected tuple format.

    Args:
        df (pd.DataFrame): DataFrame with a 'coordinates2' column containing coordinates

    Returns:
        pd.DataFrame: DataFrame with properly formatted coordinates
    """
    if 'coordinates2' not in df.columns:
        print("Warning: 'coordinates2' column not found in DataFrame")
        return df

    # Create a copy to avoid SettingWithCopyWarning
    df = df.copy()

    # Convert coordinates to the correct format
    df['coordinates2'] = df['coordinates2'].apply(safe_convert_coordinates)

    return df

def safe_convert_coordinates(coords):
    """
    Convert coordinates to a tuple of floats.

    This function handles various input formats:
    - Tuples: (x0, y0, x1, y1)
    - NumPy arrays: [x0, y0, x1, y1]
    - Strings: "(x0, y0, x1, y1)"

    Args:
        coords: Coordinates in various formats

    Returns:
        tuple: Tuple of 4 floats (x0, y0, x1, y1) representing the bounding box
              where (x0, y0) is the top-left corner and (x1, y1) is the bottom-right corner
    """
    try:
        if isinstance(coords, tuple) and len(coords) == 4:
            # Already in the correct format
            return coords
        elif isinstance(coords, np.ndarray):
            # Convert NumPy array to tuple
            return tuple(float(x) for x in coords)
        elif isinstance(coords, str):
            # Try to parse as a string representation of a tuple
            return ast.literal_eval(coords)
        else:
            print(f"Warning: Unhandled coordinate format: {type(coords)}")
            return (0, 0, 0, 0)
    except Exception as e:
        print(f"Error converting coordinates {coords}: {e}")
        return (0, 0, 0, 0)

def load_roi_data(roi_path):
    """
    Load ROI (Region of Interest) data from a JSON file.

    This function:
    1. Loads the ROI data from the specified JSON file
    2. Detects the format of the ROI data (standard or 'groups' format)
    3. Converts 'groups' format to the format expected by the table extraction code

    Args:
        roi_path (str): Path to the ROI JSON file containing table coordinates

    Returns:
        list: List of ROI items in the format expected by the table extraction code
    """
    print(f"Loading ROI data from {roi_path}")
    try:
        with open(roi_path, 'r') as f:
            roi_data = json.load(f)

        # Check if the ROI data is in the 'groups' format
        if isinstance(roi_data, dict) and 'groups' in roi_data:
            print("ROI data is in 'groups' format")
            # Convert to the format expected by get_table_data
            converted_roi = convert_groups_format(roi_data)
        else:
            print("ROI data is in standard format")
            converted_roi = roi_data

        return converted_roi
    except Exception as e:
        print(f"Error loading ROI data: {e}")
        import traceback
        traceback.print_exc()
        return []

def convert_groups_format(roi_data):
    """
    Convert ROI data from 'groups' format to the format expected by get_table_data.

    The 'groups' format is a hierarchical structure:
    {
        "groups": {
            "group_id": {
                "pages": [...],
                "rois": [
                    {
                        "name": "BOM",
                        "isTable": true,
                        "columnNames": ["pos", "item", ...],
                        "columnRatios": [0.05, 0.19, ...],
                        "relativeX0": 0.64,
                        "relativeY0": 0.09,
                        "relativeX1": 0.90,
                        "relativeY1": 0.71,
                        "headersSelected": true
                    },
                    ...
                ]
            },
            ...
        }
    }

    The expected format for get_table_data is a list of dictionaries:
    [
        {
            "columnName": "bom",
            "tableCoordinates": {
                "relativeX0": 0.64,
                "relativeY0": 0.09,
                "relativeX1": 0.90,
                "relativeY1": 0.71
            },
            "tableColumns": [
                {"pos": {"relativeX0": 0.64, "relativeY0": 0.09, "relativeX1": 0.65, "relativeY1": 0.71}},
                ...
            ],
            "headersSelected": true
        },
        ...
    ]

    Args:
        roi_data (dict): ROI data in 'groups' format

    Returns:
        list: List of ROI items in the format expected by get_table_data
    """
    converted_roi = []

    # Process each group
    for group_name, group_data in roi_data.get('groups', {}).items():
        # Get the ROIs for this group
        rois = group_data.get('rois', [])

        # Process each ROI
        for roi in rois:
            # Check if this is a table ROI
            if roi.get('isTable', False) and roi.get('name', '').lower() in TABLE_NAMES:
                table_type = roi.get('name', '').lower()

                # Create a table item
                table_item = {
                    'columnName': table_type,
                    'tableCoordinates': {
                        'relativeX0': roi.get('relativeX0', 0),
                        'relativeY0': roi.get('relativeY0', 0),
                        'relativeX1': roi.get('relativeX1', 0),
                        'relativeY1': roi.get('relativeY1', 0)
                    },
                    'tableColumns': [],
                    'headersSelected': roi.get('headersSelected', False)
                }

                # Get column names and ratios
                column_names = roi.get('columnNames', [])
                column_ratios = roi.get('columnRatios', [])

                # Create columns based on ratios
                if column_names and column_ratios and len(column_names) == len(column_ratios):
                    # Calculate column coordinates based on ratios
                    prev_ratio = 0
                    for i, (col_name, ratio) in enumerate(zip(column_names, column_ratios)):
                        # Calculate column boundaries based on ratios
                        x0 = roi['relativeX0'] + prev_ratio * (roi['relativeX1'] - roi['relativeX0'])
                        x1 = roi['relativeX0'] + ratio * (roi['relativeX1'] - roi['relativeX0'])
                        y0 = roi['relativeY0']
                        y1 = roi['relativeY1']

                        # Add column to table
                        table_item['tableColumns'].append({
                            col_name: {
                                'relativeX0': x0,
                                'relativeY0': y0,
                                'relativeX1': x1,
                                'relativeY1': y1
                            }
                        })

                        prev_ratio = ratio

                converted_roi.append(table_item)

    return converted_roi

def convert_relative_to_absolute(roi_data, width=1200, height=900):
    """
    Convert relative coordinates to absolute coordinates.

    The table extraction code expects coordinates in absolute pixel values,
    but the ROI data often contains relative coordinates (0.0 to 1.0).
    This function converts relative coordinates to absolute pixel values
    based on the specified width and height.

    Args:
        roi_data (list): List of ROI items with relative coordinates
        width (int): Page width in pixels
        height (int): Page height in pixels

    Returns:
        list: List of ROI items with absolute coordinates
    """
    # Create a copy of the ROI data to avoid modifying the original
    converted_roi = []

    # Check if coordinates are already absolute
    first_item = roi_data[0] if roi_data else {}
    if 'tableCoordinates' in first_item and 'x0' in first_item['tableCoordinates']:
        print("ROI data already has absolute coordinates")
        return roi_data

    print(f"Converting relative coordinates to absolute (width={width}, height={height})")

    # Process each ROI item
    for item in roi_data:
        # Create a copy of the item
        converted_item = item.copy()

        # Convert table coordinates
        if 'tableCoordinates' in item:
            table_coords = item['tableCoordinates']
            if 'relativeX0' in table_coords:
                # Convert relative coordinates to absolute
                converted_item['tableCoordinates'] = {
                    'x0': table_coords['relativeX0'] * width,
                    'y0': table_coords['relativeY0'] * height,
                    'x1': table_coords['relativeX1'] * width,
                    'y1': table_coords['relativeY1'] * height
                }

        # Convert column coordinates
        if 'tableColumns' in item:
            converted_columns = []
            for col_dict in item['tableColumns']:
                converted_col = {}
                for col_name, coords in col_dict.items():
                    if 'relativeX0' in coords:
                        # Convert relative coordinates to absolute
                        converted_col[col_name] = {
                            'x0': coords['relativeX0'] * width,
                            'y0': coords['relativeY0'] * height,
                            'x1': coords['relativeX1'] * width,
                            'y1': coords['relativeY1'] * height
                        }
                    else:
                        # Copy as is
                        converted_col[col_name] = coords
                converted_columns.append(converted_col)
            converted_item['tableColumns'] = converted_columns

        converted_roi.append(converted_item)

    return converted_roi

def extract_tables_from_existing_data(feather_path, roi_path, page_num=1):
    """
    Extract tables from existing data sources.

    This function:
    1. Loads data from the feather file
    2. Loads ROI data from the JSON file
    3. Converts relative coordinates to absolute coordinates
    4. Calls the get_table_data function to extract tables
    5. Saves the extracted tables to CSV files

    Args:
        feather_path (str): Path to the feather file containing PDF text data
        roi_path (str): Path to the ROI JSON file containing table coordinates
        page_num (int): Page number to process (1-based)

    Returns:
        tuple: (structured_tables, annotations_tables, outliers_df, annotations_df)
    """
    from unit_tests.get_tables_test import get_table_data

    # Load data
    raw_data = load_feather_data(feather_path, page_num)
    roi_data = load_roi_data(roi_path)

    # Convert ROI coordinates if needed
    converted_roi = convert_relative_to_absolute(roi_data)

    # Print ROI information
    print("\nROI Information:")
    for item in converted_roi:
        table_type = item.get('columnName', '').lower()
        if table_type in TABLE_NAMES:
            print(f"  Found table type: {table_type}")
            if 'tableColumns' in item:
                print(f"  Columns for {table_type}:")
                for col_dict in item['tableColumns']:
                    for col_name in col_dict.keys():
                        print(f"    {col_name}")

    # Call the get_table_data function
    print("\nExtracting tables...")
    try:
        # Convert to 0-based page number for internal processing
        zero_based_page_num = page_num - 1

        # Call the get_table_data function from get_tables_test.py
        structured_tables, annotations_tables, outliers_df, annotations_df = get_table_data(
            pdf_path="dummy_path.pdf",  # Dummy path, not actually used
            page=None,  # No page object needed
            page_num=zero_based_page_num,
            converted_roi_payload=converted_roi,
            page_text_blocks=None,
            raw_data_df=raw_data,
            missing_pos=False,
            remove_outliers=True
        )

        # Print results
        print("\nExtracted tables:")
        for table_type, df in structured_tables.items():
            print(f"\nTable '{table_type}':")
            print(f"  Rows: {len(df)}")
            if not df.empty:
                print(f"  Columns: {', '.join(df.columns)}")
                print("\nSample data:")
                print(df.head(3))

                # Save to CSV
                output_path = f"table_{table_type}_page_{page_num}.csv"
                df.to_csv(output_path, index=False)
                print(f"  Saved to {output_path}")

                # Save to XLSX for BOM table
                if table_type == 'bom':
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    xlsx_path = f"bom_export_{timestamp}.xlsx"

                    # Create a writer to save the DataFrame to Excel
                    with pd.ExcelWriter(xlsx_path, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name='BOM', index=False)

                        # Auto-adjust column widths
                        worksheet = writer.sheets['BOM']
                        for i, col in enumerate(df.columns):
                            max_width = max(
                                df[col].astype(str).map(len).max(),  # Width of data
                                len(str(col))                         # Width of column name
                            )
                            # Set column width (with some padding)
                            worksheet.column_dimensions[chr(65 + i)].width = max_width + 2

                    print(f"  Exported BOM to {xlsx_path}")

        return structured_tables, annotations_tables, outliers_df, annotations_df

    except Exception as e:
        print(f"Error extracting tables: {e}")
        import traceback
        traceback.print_exc()
        return {}, {}, pd.DataFrame(), pd.DataFrame()

if __name__ == "__main__":
    # Hardcoded default values for easy execution from IDE
    feather_path = r"C:\Users\<USER>\Documents\EXC_0015\AQT\data.feather"
    roi_path = r"C:\Users\<USER>\Documents\EXC_0015\AQT\roi_AQT.json"
    page_num = 1

    print("Running table extraction test with existing data...")
    print(f"Feather file: {feather_path}")
    print(f"ROI file: {roi_path}")
    print(f"Page number: {page_num}")

    # Extract tables from the specified page
    extract_tables_from_existing_data(
        feather_path=feather_path,
        roi_path=roi_path,
        page_num=page_num
    )

    print("\nTest completed.")
