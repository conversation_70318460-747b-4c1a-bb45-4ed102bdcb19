
# BOM Construction from Isometric Diagrams

## Overview
This module analyzes PDF files containing isometric diagrams to construct a bill of materials (BOM) by detecting pipe segments and their associated sizes and quantities.

## Input File
- Location: S:\Shared Folders\Client Uploads\Brock Services\S1601 Insulation Only (1).pdf
- Content: Multiple pages containing isometric diagrams with pipe segments, text labels, and measurements

## Requirements

### Pipe Segment Detection
- Detect isometric lines in diagrams (angles: 30°, 150°, 90°, 330°, 210°, 270°)
- Identify distinct pipe segments
- Visualize detected segments in output PDF

### Measurement Detection
- Identify quantity values that are:
  - Parallel to pipe segments OR
  - Connected by arrows pointing to pipe segments
- Format: Must start with numbers (e.g., "15'0.1/2")
- When multiple measurements exist:
  - Use measurement closest to pipe
  - Ignore span measurements that are further away
- Store original quantity value in output

### Size Detection
- Identify size labels ending in "NPD"
- Associate sizes with corresponding pipe segments

### Data Association Rules
1. Text must start with numbers to be considered a quantity
2. Ignore text starting with letters (e.g., "EL")
3. Use proximity and geometric relationships to associate measurements with pipes
4. For multiple parallel measurements, use closest to pipe

## Output

### Generated Files
1. Excel file (debug/s1601_pipe_segments.xlsx) containing:
   - pdf_page: Page number
   - material_description: Component type
   - size: Pipe size
   - quantity: Calculated length
   - original_quantity: Raw measurement text

2. Visualization PDF showing:
   - Original diagram
   - Highlighted pipe segments
   - Detected measurements (blue)
   - Detected arrows (red)
   - Isometric lines (black)

### Data Format Example
```csv
pdf_page, material_description, size, quantity
1, pipe, 10, 100
```

## Implementation Notes
- Uses PyMuPDF/fitz for PDF processing
- Implements geometric analysis for isometric line detection
- Employs proximity-based text association
- Includes visualization capabilities for verification
