# PostgreSQL Database Implementation for ATEM - System Flow Analysis

## Overview and Context

The ATEM (Agile Takeoff & Estimation Model) application extracts data from structured PDF documents, specifically piping isometric drawings, and collects the data into a structured tabular output for takeoffs and estimation. The current implementation uses complex pandas DataFrame logic and SQLite, but the goal is to simplify the effort by replacing much of this with PostgreSQL.

## Original Workflow (Pre-PostgreSQL)

1. **Load isometric drawings into app**
   - PDF documents are loaded into the application

2. **Select and label regions of interest (ROIs)**
   - The label corresponds to a column in the database
   - Two main types of data are extracted:
     - **General**: Single title block with identifying information like line number, pipe specs, temperatures, pressure ratings, etc. Each page (pdf_id) has one row of data.
     - **BOM (Bill of Materials)**: Tabular structure that can have many rows. BOM rows relate to their parent general row using the 'general.pdf_id'

3. **Build RFQ**
   - The RFQ is created from the BOM by taking all unique combinations of 'material_description', 'size1' and 'size2'

4. **Classify RFQ**
   - The RFQ is classified using various columns and mapping tables

5. **Merge RFQ into BOM**
   - Once classified, the RFQ is merged into the BOM by matching material_description, size1, size2 in both tables
   - Category values in BOM are updated from RFQ

6. **Aggregate General data**
   - The general table has columns that sum the 'quantity' from BOM for each page
   - It looks at 'general_category' to determine which column the value sums into
   - For each unique size in the BOM where a value should be counted, one row is inserted into general
   - Information for the page is copied, excluding the sum columns
   - 'calculated_eq_length' and 'calculated_area' are summed for each row relevant to the size

This creates a bidirectional flow:
- Initial data extraction: source_data → general → bom → rfq
- Classification merge: rfq → bom → general

## PostgreSQL Implementation Flow

The flow with the PostgreSQL implementation will be similar and should automatically populate each table:

1. **Initial Data Load**:
   ```
   source_data (simulated with xlsx) → public.general → public.bom → public.atem_rfq → public.rfq_input
   ```

2. **Bidirectional Updates**:
   Edits to RFQ or RFQ input automatically propagate using triggers:
   ```
   public.rfq_input ⟷ public.atem_rfq → public.bom → public.general
   ```

## Database Tables and Their Relationships

### 1. Client-Related Tables
- `atem_clients`: Stores client information (company name, contacts)
- `atem_projects`: Projects linked to clients
- `atem_client_profiles`: Defines calculation methods (lookup vs factor-based)

### 2. Core Processing Tables
- `public.general`: Page-level information with columns for aggregated quantities
- `public.bom`: Component-level data with multiple rows per page
- `public.atem_rfq`: Project-specific RFQ entries with calculated fields
- `public.rfq_input`: Master reference table with standardized materials

### 3. Supporting Tables
- `atem_bom_component_mapping`: Maps components to categories
- `atem_equiv_length_factors`: Defines multiplier factors for calculations
- Fitting lookup tables: `standard_fittings`, `reducers`, `elbows_reducing`, etc.
- `vw_fittings_lookup`: Unified view combining all fittings tables

## Functional Data Flow Details

### 1. Bidirectional RFQ_INPUT ⟷ RFQ Synchronization

Currently implemented and working via two key triggers:

#### From RFQ to RFQ_INPUT (`sync_rfq_to_input`):
- Triggered when RFQ records are inserted or updated
- Checks if matching RFQ_INPUT record exists
- If not, creates new RFQ_INPUT record with material attributes
- Links the RFQ record to RFQ_INPUT via `rfq_input_id`
- Prevention mechanism avoids infinite recursion

#### From RFQ_INPUT to RFQ (`sync_input_to_rfq`):
- Triggered when RFQ_INPUT records are updated
- Updates all linked RFQ records (based on `rfq_input_id`)
- Updates material attributes but preserves project-specific data
- Ensures consistent material definitions across projects

### 2. Calculation Processing

Several functions handle real-time calculations:

#### `update_rfq_calculations()`:
- Triggered when relevant fields in RFQ change
- Determines calculation method from client profile:
  - **Lookup Method**: Uses `vw_fittings_lookup` to find predefined values
  - **Factor Method**: Uses `atem_equiv_length_factors` multipliers
- Implements sophisticated size matching algorithm:
  1. Tries exact size match first
  2. For compound sizes, attempts reversals (4×2 vs 2×4)
  3. Falls back to larger size for compound fittings
  4. Uses nearest size approximation preferring rounding up
- Updates `calculated_eq_length` and `calculated_area` fields

#### `calculate_pipe_surface_area()`:
- Utility function for surface area calculations
- Supports multiple input/output units (inches, feet, mm, meters)
- Used by other functions when needed

### 3. Category Classification

Two main functions handle component classification:

#### `update_categories_from_mapping()`:
- Triggers when component fields change (pipe, fitting, gasket, bolt categories)
- Looks up appropriate classifications in `atem_bom_component_mapping`
- Updates `rfq_scope` and `general_category` fields
- Applied to both RFQ and RFQ_INPUT tables

#### `propagate_mapping_changes()`:
- Triggers when mappings in `atem_bom_component_mapping` are updated
- Finds all affected RFQ_INPUT records
- Updates their categories to maintain consistency

### 4. BOM to General Aggregation (Planned Implementation)

Based on the requirements:

- Group BOM data by project_id, page, size, and general_category
- For each unique size in the BOM:
  - Create a row in General if it doesn't exist
  - Sum quantities from BOM into the appropriate General columns based on general_category
  - Sum calculated_eq_length and calculated_area for accurate totals

## Current Status and Implementation Plans

### Currently Implemented:
- Complete database schema for client, project, and RFQ tables
- Bidirectional sync between RFQ_INPUT and RFQ
- Calculation functions and triggers
- Category mapping system
- Lookup tables for standardized fittings

### Planned/In Progress:
- BOM and General table implementation with proper references
- RFQ → BOM → General propagation
- BOM → General aggregation by size
- Fixing surface area calculations to be per-component
- Ensuring proper category-based summation in General table