from PySide6.QtWidgets import QPushButton, QMenu, QLabel
from PySide6.QtGui import QMovie, QIcon
from PySide6.QtCore import Signal
from pubsub import pub
from src.pyside_util import get_resource_qicon

class DocumentViewerStatusWidget(QPushButton):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        self.menuActions = {
        }
        self.jobItems = {}
        self.setText("")
        pub.subscribe(self.onStatus, "set-statusbar-documentlibrary")
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.setIcon(get_resource_qicon("book.svg"))
        self.setToolTip("Document Library")

    def onStatus(self, data):
        self.sgnUpdateStatus.emit(data)
    
    def updateAnimation(self):
        self.setIcon(QIcon(self.movie.currentPixmap()))

    def onUpdateStatus(self, data):
        n = data["params"].get("documentCount", 0)
        msg = f"Documents: {n}"
        self.setText(" " + msg)
