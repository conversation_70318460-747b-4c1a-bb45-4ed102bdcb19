"""
QPushButton support for multiple icons, animated icons and more general customizations
"""
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from pyparsing import col

from src.pyside_util import get_resource_pixmap, applyDropShadowEffect


class MyProxyStyle(QProxyStyle):

    """Reduces the delay in displaying a tooltip"""

    def styleHint(self, hint, option: QStyleOption = None, widget: QWidget = None, returnData: QStyleHintReturn = None):

        if (hint == QStyle.StyleHint.SH_ToolTip_WakeUpDelay):
            return 20

        return super().styleHint(hint, option, widget, returnData)

class MyButton(QPushButton):

    BUTTON_SECONDARY_STYLE = 0
    BUTTON_PRIMARY_STYLE = 1
    SIDEBAR_STYLE = 2
    BUTTON_MENU_STYLE = 2

    ALERT_NONE = -1
    ALERT_WARNING = 0
    ALERT_ERROR = 1
    ALERT_OK = 2
    def __init__(self,
                 parent=None,
                 text: str="",
                 style: int = BUTTON_PRIMARY_STYLE,
                 checkable: bool=False,
                 leading_icon=None,
                 trailing_icon=None):
        super().__init__(parent, text=text)

        self._alert = None
        self._alertType = self.ALERT_NONE
        self._busy = False
        self._currBusyFrame = 0
        self._busyFrames = 30
        self._currAlertIcon = None

        if leading_icon:
            self._leadingIcon = get_resource_pixmap(leading_icon)
        else:
            self._leadingIcon = None

        self._pixmap: QPixmap = None
        # self._pixmap: QPixmap = get_resource_pixmap("chevron-down.svg")

        self._iconOk = get_resource_pixmap("check.svg")
        self._iconOk = self._iconOk.scaled(18, 18, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)

        self._iconWarning = get_resource_pixmap("alert-circle.svg")
        self._iconWarning = self._iconWarning.scaled(18, 18, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)

        self._iconError = get_resource_pixmap("alert-circle.svg")
        self._iconError = self._iconError.scaled(18, 18, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)

        self._iconBusy = get_resource_pixmap("loader_frames_30_px_22.png")

        self.setLayout(QHBoxLayout())
        self.setCheckable(checkable)
        # self.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        # self.setFixedHeight(32)

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        if style == self.BUTTON_PRIMARY_STYLE:
            self.setObjectName("MyButtonPrimary")
        elif style == self.BUTTON_SECONDARY_STYLE:
            self.setObjectName("MyButtonSecondary")
        elif style == self.BUTTON_MENU_STYLE:
            self.setObjectName("MyButtonMenu")
        else:
            self.setObjectName("MyButtonSidebar")
        self.updateIcon()

        self.clicked.connect(self.updateIcon)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.onTimer)
        self.timer.start()
        self.timer.setInterval(30)

        self.setToolTip("")
        # self.setToolTipDuration(1000)
        self.setStyle(MyProxyStyle(self.style()))

        applyDropShadowEffect(self, offset=(0, 1), blurRadius=1, color="black")

    def setAlert(self, message, alertType=None):
        self.setToolTip(message)
        self._alert = True
        self._alertType = alertType

        if self._alertType == self.ALERT_OK:
            self._currAlertIcon = self._iconOk
        elif self._alertType == self.ALERT_ERROR:
            self._currAlertIcon = self._iconError
        elif self._alertType == self.ALERT_WARNING:
            self._currAlertIcon = self._iconWarning

    def removeAlert(self):
        self.setToolTip("")
        self._currAlertIcon = None
        self._alert = False
        self._alertType = None

    def enterEvent(self, event):

        return super().enterEvent(event)

    def setBusy(self, busy: bool = True):
        """Triggers an animated loading icon"""
        self._busy = busy

    def setChecked(self, event):
        super().setChecked(event)
        self.updateIcon()

    def updateIcon(self):
        self.update()
        return
        if self.isChecked():
            icon = "chevron-down"
        else:
            icon = "chevron-right"
        # self.setIcon(get_resource_qicon(f"{icon}.svg"))
        self._pixmap = get_resource_pixmap(f"{icon}.svg")
        self.update()

    def getPixmap(self) -> QPixmap:
        return self._pixmap

    def setPixmap(self, pixmap: QPixmap):
        self._pixmap = pixmap

    def paintEvent(self, event: QPaintEvent):
        super().paintEvent(event)
        checkable = self.isCheckable()

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)
        painter.setRenderHint(QPainter.RenderHint.LosslessImageRendering, True)

        if self._leadingIcon:
            icon = self._leadingIcon.scaled(16, 16, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)
            y = (self.height() - icon.height()) / 2
            x = 6
            painter.drawPixmap(x, y, icon)
            painter.setBackgroundMode(Qt.TransparentMode)

        if self._pixmap and checkable:
            self._pixmap = self._pixmap.scaled(16, 16, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)
            y = (self.height() - self._pixmap.height()) / 2
            x = self.width() - 26
            painter.drawPixmap(x, y, self._pixmap)
            painter.setBackgroundMode(Qt.TransparentMode)

        if self._alert and self._currAlertIcon:
            y = (self.height() - self._currAlertIcon.height()) / 2
            if checkable:
                x = self.width() - 48
            else:
                x = self.width() - 26
            painter.drawPixmap(x, y, self._currAlertIcon)

        if self._busy:
            icon = self._iconBusy.copy(QRect(22*self._currBusyFrame, 0, 22, 22))
            icon = icon.scaled(22, 22, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)
            painter.setOpacity(1)
            y = (self.height() - icon.height()) / 2
            if checkable:
                x = self.width() - 48
            else:
                x = self.width() - 26
            painter.drawPixmap(x, y, icon)

            # Loop over animated frames
            self._currBusyFrame += 1
            if self._currBusyFrame >= self._busyFrames:
                self._currBusyFrame = 0

    def onTimer(self):
        self.update()