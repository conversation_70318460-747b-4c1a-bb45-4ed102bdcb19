from PySide6.QtCore import QEvent
from PySide6.QtGui import QEnterEvent
from PySide6.QtWidgets import <PERSON><PERSON>oxLayout, QTextEdit, QWidget
from PySide6.QtWidgets import QWidget, QVBoxLayout
from src.pyside_util import applyDropShadowEffect


class HelpPopup(QWidget):

    def __init__(self, parent):
        super().__init__(parent)

        self.setMinimumSize(480, 400)
        
        self.setLayout(QVBoxLayout())
        self.text = QTextEdit(self)
        self.text.setReadOnly(True)
        
        self.text.setAcceptRichText(True)
        self.layout().addWidget(self.text)

        self.topLevelWidget().windowResized.connect(self.onWindowResize)
        self.text.setObjectName("helpPopup")
        self.setObjectName("helpPopup")

        applyDropShadowEffect(self)
        self.show()
        self.onWindowResize(None)

    def setText(self, text):
        self.text.setText(text)

    def onWindowResize(self, event):
        self.setFixedSize(320, self.topLevelWidget().height() - 196)
        self.move(self.topLevelWidget().width() - self.width() - 16, 
                (self.topLevelWidget().height()) // 2 - (self.height() // 2))
