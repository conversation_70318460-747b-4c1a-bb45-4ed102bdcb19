import cv2
import numpy as np


def angle_between(p1, p2, p3):
    # returns angle at p2 between p1 and p3
    a = np.array(p1) - np.array(p2)
    b = np.array(p3) - np.array(p2)
    cosine_angle = np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
    return np.degrees(angle)

def is_rectangle(approx, angle_thresh=10):
    if len(approx) != 4:
        return False
    angles = []
    for i in range(4):
        p1 = approx[i][0]
        p2 = approx[(i + 1) % 4][0]
        p3 = approx[(i + 2) % 4][0]
        angle = angle_between(p1, p2, p3)
        angles.append(angle)
    return all(abs(a - 90) < angle_thresh for a in angles)

def detect_rectangles(image: cv2.Mat|str) -> list:

    # Load the image
    try:
        image = cv2.imread(image)
    except:
        pass

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Optional: Blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Edge detection
    edges = cv2.Canny(blurred, 50, 150)

    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    rectangles = []

    for cnt in contours:
        # Approximate the contour
        epsilon = 0.02 * cv2.arcLength(cnt, True)
        approx = cv2.approxPolyDP(cnt, epsilon, True)

        # We're looking for quadrilaterals
        if len(approx) == 4 and cv2.isContourConvex(approx):
            if is_rectangle(approx):  # ← new check here!
                x, y, w, h = cv2.boundingRect(approx)
                if w > 30 and h > 30:
                    rectangles.append([x, y, x + w, y + h])

    # Draw detected rectangles
    output = image.copy()
    for rect in rectangles:
        a, b = (rect[0], rect[1]), (rect[2], rect[3])
        cv2.rectangle(output, a, b, (0, 255, 0), 2)

    # Show results
    # cv2.imshow("Detected Rectangles", output)
    cv2.imwrite("debug/rectangles.png", output)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    return rectangles

# Example usage
if __name__ == "__main__":
    from scripts.cv2_detection_utils.drawing import draw_rectangles

    # image_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\30\sources\cdrawingsjoeys1601-insulation-only-1pdf\ocr_data\output_images\iso_600 dpi\Isometric Drawing Area_page_2.png"  # <-- replace with your image path
    image_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\30\sources\cdrawingsjoeys1601-insulation-only-1pdf\ocr_data\output_images\iso_600 dpi\Isometric Drawing Area_page_1.png"  # <-- replace with your image path
    # image_path = r"debug\page_1.png"  # <-- replace with your image path
    rectangles = detect_rectangles(image_path)

    # scrub rectangles
    image = cv2.imread(image_path)
    image = draw_rectangles(
        image,
        rectangles=rectangles,
        # fill_color=(0, 0, 255),     # Red in BGR
        border_color=(255, 255, 255),   # White border
        fill_opacity=0,           # 20% opacity
        border_thickness=16
    )
    cv2.imwrite("debug/rectangles_scrubbed.png", image)