"""
Algorithm to manage column saving

TODO - test cases for different scenarios involving column saving
"""

saved_columns = [
    'drawing',
    'lineNumber',
    'sheet',
    'avg_elevation',
    'elevation',
    'lf',
    'sf',
    'ef',
    'elbows_90',
    'elbows_45',
    'bevels',
    'tees',
    'reducers',
    'caps',
    'flanges',
    'valves_flanged',
    'valves_welded',
    'cut_outs',
    'supports',
    'bends',
    'field_welds'
]

current = [
    'field_welds',
    'elevation',
    'lf',
    'caps',
    'sf',
    'flanges',
    'avg_elevation',
    'tees',
    'reducers',   
]

def bubble_sort(arr: list):
  # Outer loop to iterate through the list n times
  for n in range(len(arr) - 1, 0, -1):
    swapped = False
    # Inner loop to compare adjacent elements
    for i in range(n):
      print(i, arr[i], arr[i + 1])
      if arr[i] > arr[i + 1]:
        # Swap elements if they are in the wrong order
        swapped = True
        arr[i], arr[i + 1] = arr[i + 1], arr[i]
      # If we didn't make any swaps in a pass, 
      # the list is already sorted and we can 
      # exit the outer loop
      if not swapped:
        return
    return arr


if __name__ == "__main__":
    print(saved_columns)
    print(current)

    indexes = []
    
    for n, column in enumerate(current):
        print(n, column)
        index = saved_columns.index(column)
        print(index)
        indexes.append(index)

    indexes_sorted = indexes[:]
    indexes_sorted.sort()

    bubble_sort(indexes)

    print(indexes)
    print(indexes_sorted)
    print("Is column already sorted", indexes == indexes_sorted)

    new_saved_order = []
    