import fitz
import io
import numpy as np
from PIL import Image


def page_to_opencv(page: fitz.Page, zoom=None, dpi=None):
    """Return open CV image from PyMuPDF page"""
    if zoom is None:
        zoom = 1
    if dpi:
        zoom = dpi / 72
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image