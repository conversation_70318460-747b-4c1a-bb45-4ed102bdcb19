"""
Processes text regions and compares with Raw DF

https://github.com/pymupdf/PyMuPDF/discussions/1307#discussioncomment-1456711

Standard DPI is 72

So to scale co-ordinates back to source page, use dpi/72
"""

import cv2
import fitz
import io
import os

import numpy as np
import pandas as pd

from PIL import Image


def page_to_opencv(page: fitz.Page):
    """Return open CV image from PyMuPDF page"""
    rgb = page.get_pixmap()
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image


def visualize_regions(cv_image, regions):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    vis_img = cv_image.copy()

    # Draw text regions with alternating colors
    for i, region in enumerate(regions):
        x = int(region["x0"])
        y = int(region["y0"])
        w = int(region["x1"]- region["x0"])
        h = int(region["y1"]- region["y0"])
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.2, color, 1)

    return vis_img


def is_overlapping(rect_a, rect_b) -> bool:
    """
    Return True if two regions overlap, where lx and rx are top left
    and bottom right coordinates of a rect respectively
    """
    a_left, a_top = rect_a[0], rect_a[1]
    a_right, a_bottom = rect_a[2], rect_a[3]
    b_left, b_top = rect_b[0], rect_b[1]
    b_right, b_bottom = rect_b[2], rect_b[3]
    return (a_left < b_right
        and a_right > b_left
        and a_top < b_bottom
        and a_bottom > b_top)


def extract_raw_regions(page_raw_regions: list):
    """Parses coordinates and extracts bounding box values"""
    raw_regions = []

    for record in page_raw_regions:
        try:
            # Workaround - parse string to tuple
            record["coordinates2"] = record["coordinates2"].replace("(", "")
            record["coordinates2"] = record["coordinates2"].replace(")", "")
            coordinates = tuple(map(float, record["coordinates2"].split(', ')))
            record["x0"] = coordinates[0]
            record["y0"] = coordinates[1]
            record["x1"] = coordinates[2]
            record["y1"] = coordinates[3]
            record["width"] = record["x1"] - record["x0"]
            record["height"] = record["y1"] - record["y0"]
            raw_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])
        except Exception as e:
            raw_regions.append(record["coordinates2"])

    return raw_regions


def detect_missing_regions(raw_df: pd.DataFrame, text_regions: pd.DataFrame):
    """
    Compares CV2 detected regions with Raw DataFrame coordinates to detect missing
    ROIs which needs to be flagged for OCR
    """
    all_flagged = []
    page_groups = text_regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_raw_regions = raw_df[raw_df["pdf_page"] == pdf_page].to_dict("records")
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])

        # If text region does not overlap with any region of
        # the Raw DataFrame, then flag it for OCR
        flagged = []
        for n, r1 in enumerate(page_regions):
            for r2 in extract_raw_regions(page_raw_regions):
                if is_overlapping(r1, r2):
                    break
            else:
                # Not overlapping - add flag for OCR
                record = [pdf_page] + r1
                flagged.append(record)
                print(f"Page {pdf_page}, Text region index={n} flagged for OCR")

        all_flagged.extend(flagged)

        print(f"Page {pdf_page} - Number of text regions which do not overlap:", len(flagged))

    return pd.DataFrame(all_flagged, columns=["pdf_page", "x0", "y0", "x1", "y1"])


def filter_missing_from_rois(roi_coords: pd.DataFrame, text_regions: pd.DataFrame):
    """
    Filter regions which overlap with ROI coords i.e. from the ROI layout
    """
    all_flagged = []
    page_groups = text_regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_roi_regions = roi_coords[roi_coords["pdf_page"] == pdf_page].to_dict("records")
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])

        # If text region does not overlap with any region of
        # the Raw DataFrame, then flag it for OCR
        flagged = []
        for n, r1 in enumerate(page_regions):
            for r2 in page_roi_regions:
                r2_coords = [r2["x0"], r2["y0"], r2["x1"], r2["y1"]]
                if is_overlapping(r1, r2_coords):
                    # Overlapping with ROI - add flag for OCR
                    record = [pdf_page] + r1 + [r2["columnName"]]
                    flagged.append(record)
                    print(f"Page {pdf_page}, Text region index={n} flagged for OCR")
                    break

        all_flagged.extend(flagged)

        print(f"Page {pdf_page} - Number of missing regions which overlap with an ROI:", len(flagged))

    return pd.DataFrame(all_flagged, columns=["pdf_page", "x0", "y0", "x1", "y1", "columnName"])


def convert_coords(text_regions: pd.DataFrame, dpi: int = 72):
    """Map coordinate values back to standard DPI"""
    mapped_coords = text_regions.copy()
    v = 72 / dpi
    mapped_coords["x0"] = mapped_coords['x0'].multiply(v)
    mapped_coords["y0"] = mapped_coords['y0'].multiply(v)
    mapped_coords["x1"] = mapped_coords['x1'].multiply(v)
    mapped_coords["y1"] = mapped_coords['y1'].multiply(v)
    mapped_coords["width"] = mapped_coords['width'].multiply(v)
    mapped_coords["height"] = mapped_coords['height'].multiply(v)
    return mapped_coords


def visualize_results(doc: fitz.Document, regions, raw_df=None, output_path=None):
    """
    Visualize results for each page. Optionally display Raw Dataframe boxes for comparison
    """
    page_groups = regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page = doc[pdf_page-1]
        cv_image = page_to_opencv(page)
        raw_vis_image = None
        if raw_df is not None:
            page_raw_regions = raw_df[raw_df["pdf_page"] == pdf_page].to_dict("records")
            extracted_raw_regions = extract_raw_regions(page_raw_regions)
            extracted_raw_regions = pd.DataFrame(extracted_raw_regions, columns=["x0", "y0", "x1", "y1"]).to_dict("records")
            raw_vis_image = visualize_regions(cv_image, extracted_raw_regions)

        flagged_vis_image = visualize_regions(cv_image, page_text_regions.to_dict("records"))

        # Concat images horizontally for output image if raw image created
        if output_path:
            if raw_vis_image is not None:
                image = cv2.hconcat([raw_vis_image, flagged_vis_image])
            else:
                image = flagged_vis_image
            cv2.imwrite(os.path.join(output_path, f"flagged_regions_{pdf_page}.png"), image)


def visualize_page(page: fitz.Page, pdf_page: int, regions, raw_df=None, output_path=None):
    """
    Visualize results for each page. Optionally display Raw Dataframe boxes for comparison
    """
    page_text_regions = regions[regions["pdf_page"] == pdf_page]
    cv_image = page_to_opencv(page)
    raw_vis_image = None
    if raw_df is not None:
        page_raw_regions = raw_df[raw_df["pdf_page"] == pdf_page].to_dict("records")
        extracted_raw_regions = extract_raw_regions(page_raw_regions)
        extracted_raw_regions = pd.DataFrame(extracted_raw_regions, columns=["x0", "y0", "x1", "y1"]).to_dict("records")
        raw_vis_image = visualize_regions(cv_image, extracted_raw_regions)

    flagged_vis_image = visualize_regions(cv_image, page_text_regions.to_dict("records"))

    if raw_vis_image is not None:
        image = cv2.hconcat([raw_vis_image, flagged_vis_image])
    else:
        image = flagged_vis_image

    # Concat images horizontally for output image if raw image created
    if output_path:
        cv2.imwrite(os.path.join(output_path, f"flagged_regions_{pdf_page}.png"), image)

    return image


if __name__ == "__main__":

    # Output visualized results
    output_path = "outputImages/CV2"

    # PDF
    pdf_path = "Modified/Groups/Combined Remuriate_group_1.pdf"
    doc = fitz.open(pdf_path)

    # Raw DataFrame
    raw_df_path = "_debug_raw_data_full.xlsx"
    raw_df = pd.read_excel(raw_df_path)

    # Text Regions DataFrame
    text_regions_path = "outputImages/CV2/text_regions.xlsx"
    text_regions = pd.read_excel(text_regions_path)

    # Convert coords back to standard based on dpi value
    mapped_text_regions = convert_coords(text_regions, dpi=300)

    # Missing ROIs from Raw Data
    missing_rois = detect_missing_rois(doc, raw_df, mapped_text_regions)
    missing_rois.to_excel("flagged_text_regions_results.xlsx")

    # For debugging, visualize output of missing
    visualize_results(doc, missing_rois, raw_df, output_path)

