-- SQL file to adjust the PostgreSQL 'general' table schema
-- This file identifies columns that may be missing or need adjustment
-- between SQLite and PostgreSQL schemas

-- First, check for any SQLite columns that might be missing in PostgreSQL
-- and add them if necessary

-- Check for sysDocument column (SQLite) vs sys_document (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sys_document') THEN
        ALTER TABLE public.general ADD COLUMN sys_document TEXT;
        RAISE NOTICE 'Added sys_document column';
    END IF;
END $$;

-- Check for sysDocumentName column (SQLite) vs sys_document_name (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sys_document_name') THEN
        ALTER TABLE public.general ADD COLUMN sys_document_name VARCHAR(255);
        RAISE NOTICE 'Added sys_document_name column';
    END IF;
END $$;

-- Check for vendorDocumentId column (SQLite) vs vendor_document_id (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'vendor_document_id') THEN
        ALTER TABLE public.general ADD COLUMN vendor_document_id VARCHAR(100);
        RAISE NOTICE 'Added vendor_document_id column';
    END IF;
END $$;

-- Check for weldId column (SQLite) vs weld_id (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'weld_id') THEN
        ALTER TABLE public.general ADD COLUMN weld_id VARCHAR(100);
        RAISE NOTICE 'Added weld_id column';
    END IF;
END $$;

-- Check for weldClass column (SQLite) vs weld_class (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'weld_class') THEN
        ALTER TABLE public.general ADD COLUMN weld_class VARCHAR(100);
        RAISE NOTICE 'Added weld_class column';
    END IF;
END $$;

-- Check for xCoord column (SQLite) vs x_coord (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'x_coord') THEN
        ALTER TABLE public.general ADD COLUMN x_coord VARCHAR(50);
        RAISE NOTICE 'Added x_coord column';
    END IF;
END $$;

-- Check for yCoord column (SQLite) vs y_coord (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'y_coord') THEN
        ALTER TABLE public.general ADD COLUMN y_coord VARCHAR(50);
        RAISE NOTICE 'Added y_coord column';
    END IF;
END $$;

-- Check for flangeID column (SQLite) vs flange_id (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'flange_id') THEN
        ALTER TABLE public.general ADD COLUMN flange_id VARCHAR(100);
        RAISE NOTICE 'Added flange_id column';
    END IF;
END $$;

-- Check for totalSheets column (SQLite) vs total_sheets (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'total_sheets') THEN
        ALTER TABLE public.general ADD COLUMN total_sheets VARCHAR(50);
        RAISE NOTICE 'Added total_sheets column';
    END IF;
END $$;

-- Check for projectNo column (SQLite) vs project_no (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'project_no') THEN
        ALTER TABLE public.general ADD COLUMN project_no VARCHAR(100);
        RAISE NOTICE 'Added project_no column';
    END IF;
END $$;

-- Check for projectName column (SQLite) vs project_name (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'project_name') THEN
        ALTER TABLE public.general ADD COLUMN project_name VARCHAR(255);
        RAISE NOTICE 'Added project_name column';
    END IF;
END $$;

-- Check for processUnit column (SQLite) vs process_unit (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'process_unit') THEN
        ALTER TABLE public.general ADD COLUMN process_unit VARCHAR(100);
        RAISE NOTICE 'Added process_unit column';
    END IF;
END $$;

-- Check for processLineList column (SQLite) vs process_line_list (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'process_line_list') THEN
        ALTER TABLE public.general ADD COLUMN process_line_list VARCHAR(100);
        RAISE NOTICE 'Added process_line_list column';
    END IF;
END $$;

-- Check for pipeStandard column (SQLite) vs pipe_standard (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pipe_standard') THEN
        ALTER TABLE public.general ADD COLUMN pipe_standard VARCHAR(100);
        RAISE NOTICE 'Added pipe_standard column';
    END IF;
END $$;

-- Check for pipeSpec column (SQLite) vs pipe_spec (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pipe_spec') THEN
        ALTER TABLE public.general ADD COLUMN pipe_spec VARCHAR(100);
        RAISE NOTICE 'Added pipe_spec column';
    END IF;
END $$;

-- Check for mediumCode column (SQLite) vs medium_code (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'medium_code') THEN
        ALTER TABLE public.general ADD COLUMN medium_code VARCHAR(100);
        RAISE NOTICE 'Added medium_code column';
    END IF;
END $$;

-- Check for clientDocumentId column (SQLite) vs client_document_id (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'client_document_id') THEN
        ALTER TABLE public.general ADD COLUMN client_document_id VARCHAR(100);
        RAISE NOTICE 'Added client_document_id column';
    END IF;
END $$;

-- Check for documentId column (SQLite) vs document_id (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'document_id') THEN
        ALTER TABLE public.general ADD COLUMN document_id VARCHAR(100);
        RAISE NOTICE 'Added document_id column';
    END IF;
END $$;

-- Check for documentTitle column (SQLite) vs document_title (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'document_title') THEN
        ALTER TABLE public.general ADD COLUMN document_title VARCHAR(255);
        RAISE NOTICE 'Added document_title column';
    END IF;
END $$;

-- Check for documentDescription column (SQLite) vs document_description (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'document_description') THEN
        ALTER TABLE public.general ADD COLUMN document_description TEXT;
        RAISE NOTICE 'Added document_description column';
    END IF;
END $$;

-- Check for designCode column (SQLite) vs design_code (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'design_code') THEN
        ALTER TABLE public.general ADD COLUMN design_code VARCHAR(100);
        RAISE NOTICE 'Added design_code column';
    END IF;
END $$;

-- Check for blockCoordinates column (SQLite) vs block_coordinates (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'block_coordinates') THEN
        ALTER TABLE public.general ADD COLUMN block_coordinates TEXT;
        RAISE NOTICE 'Added block_coordinates column';
    END IF;
END $$;

-- Check for annotMarkups column (SQLite) vs annot_markups (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'annot_markups') THEN
        ALTER TABLE public.general ADD COLUMN annot_markups TEXT;
        RAISE NOTICE 'Added annot_markups column';
    END IF;
END $$;

-- Check for insulationSpec column (SQLite) vs insulation_spec (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'insulation_spec') THEN
        ALTER TABLE public.general ADD COLUMN insulation_spec VARCHAR(100);
        RAISE NOTICE 'Added insulation_spec column';
    END IF;
END $$;

-- Check for insulationThickness column (SQLite) vs insulation_thickness (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'insulation_thickness') THEN
        ALTER TABLE public.general ADD COLUMN insulation_thickness VARCHAR(50);
        RAISE NOTICE 'Added insulation_thickness column';
    END IF;
END $$;

-- Check for heatTrace column (SQLite) vs heat_trace (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'heat_trace') THEN
        ALTER TABLE public.general ADD COLUMN heat_trace VARCHAR(50);
        RAISE NOTICE 'Added heat_trace column';
    END IF;
END $$;

-- Check for paintSpec column (SQLite) vs paint_spec (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'paint_spec') THEN
        ALTER TABLE public.general ADD COLUMN paint_spec VARCHAR(100);
        RAISE NOTICE 'Added paint_spec column';
    END IF;
END $$;

-- Check for modDate column (SQLite) vs mod_date (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'mod_date') THEN
        ALTER TABLE public.general ADD COLUMN mod_date VARCHAR(50);
        RAISE NOTICE 'Added mod_date column';
    END IF;
END $$;

-- Check for lineNumber column (SQLite) vs line_number (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'line_number') THEN
        ALTER TABLE public.general ADD COLUMN line_number VARCHAR(100);
        RAISE NOTICE 'Added line_number column';
    END IF;
END $$;

-- Check for isoNumber column (SQLite) vs iso_number (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'iso_number') THEN
        ALTER TABLE public.general ADD COLUMN iso_number VARCHAR(100);
        RAISE NOTICE 'Added iso_number column';
    END IF;
END $$;

-- Check for isoType column (SQLite) vs iso_type (PostgreSQL)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'iso_type') THEN
        ALTER TABLE public.general ADD COLUMN iso_type VARCHAR(100);
        RAISE NOTICE 'Added iso_type column';
    END IF;
END $$;

-- Check for sys_layout_valid column (SQLite uses TEXT, PostgreSQL might use BOOLEAN)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sys_layout_valid') THEN
        ALTER TABLE public.general ADD COLUMN sys_layout_valid BOOLEAN;
        RAISE NOTICE 'Added sys_layout_valid column';
    END IF;
END $$;

-- Check for sys_build column
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sys_build') THEN
        ALTER TABLE public.general ADD COLUMN sys_build TEXT;
        RAISE NOTICE 'Added sys_build column';
    END IF;
END $$;

-- Check for additional columns from SQLite that might be missing
-- These were found in the SQLite schema but might not be in PostgreSQL
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'unit') THEN
        ALTER TABLE public.general ADD COLUMN unit VARCHAR(100);
        RAISE NOTICE 'Added unit column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'xray') THEN
        ALTER TABLE public.general ADD COLUMN xray VARCHAR(50);
        RAISE NOTICE 'Added xray column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pid') THEN
        ALTER TABLE public.general ADD COLUMN pid VARCHAR(100);
        RAISE NOTICE 'Added pid column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'service') THEN
        ALTER TABLE public.general ADD COLUMN service VARCHAR(255);
        RAISE NOTICE 'Added service column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sequence') THEN
        ALTER TABLE public.general ADD COLUMN sequence VARCHAR(100);
        RAISE NOTICE 'Added sequence column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'revision') THEN
        ALTER TABLE public.general ADD COLUMN revision VARCHAR(50);
        RAISE NOTICE 'Added revision column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pwht') THEN
        ALTER TABLE public.general ADD COLUMN pwht VARCHAR(50);
        RAISE NOTICE 'Added pwht column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'sheet') THEN
        ALTER TABLE public.general ADD COLUMN sheet VARCHAR(50);
        RAISE NOTICE 'Added sheet column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'system') THEN
        ALTER TABLE public.general ADD COLUMN system VARCHAR(100);
        RAISE NOTICE 'Added system column';
    END IF;
END $$;

-- Check for additional columns from update_database_schema function
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pmi_req') THEN
        ALTER TABLE public.general ADD COLUMN pmi_req VARCHAR(50);
        RAISE NOTICE 'Added pmi_req column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'paut_req') THEN
        ALTER TABLE public.general ADD COLUMN paut_req VARCHAR(50);
        RAISE NOTICE 'Added paut_req column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'hardness_req') THEN
        ALTER TABLE public.general ADD COLUMN hardness_req VARCHAR(50);
        RAISE NOTICE 'Added hardness_req column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'flange_guard') THEN
        ALTER TABLE public.general ADD COLUMN flange_guard VARCHAR(50);
        RAISE NOTICE 'Added flange_guard column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'continued_on') THEN
        ALTER TABLE public.general ADD COLUMN continued_on VARCHAR(100);
        RAISE NOTICE 'Added continued_on column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'connects_to') THEN
        ALTER TABLE public.general ADD COLUMN connects_to VARCHAR(100);
        RAISE NOTICE 'Added connects_to column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pickling_req') THEN
        ALTER TABLE public.general ADD COLUMN pickling_req VARCHAR(50);
        RAISE NOTICE 'Added pickling_req column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'flushing_req') THEN
        ALTER TABLE public.general ADD COLUMN flushing_req VARCHAR(50);
        RAISE NOTICE 'Added flushing_req column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'pipeline_num') THEN
        ALTER TABLE public.general ADD COLUMN pipeline_num VARCHAR(100);
        RAISE NOTICE 'Added pipeline_num column';
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'general' AND column_name = 'weight') THEN
        ALTER TABLE public.general ADD COLUMN weight VARCHAR(50);
        RAISE NOTICE 'Added weight column';
    END IF;
END $$;

-- Add a comment to the table to document the changes
COMMENT ON TABLE public.general IS 'General table with columns aligned between SQLite and PostgreSQL schemas. Last updated: ' || NOW();

-- Note: This script only adds missing columns. It does not modify existing columns
-- or change data types. If you need to modify existing columns, you'll need to
-- add ALTER TABLE statements to change column types or constraints.
