"""For validating parsing, formatting and summation of quantities"""

from cv2 import QT_STYLE_ITALIC
import pandas as pd
from data_conversions import convert_quantity_to_float

if __name__ == "__main__":

    bom_df = pd.read_excel(r"C:\Drawings\13-march-combined isos\audit\combined_bom_data_nofieldmap.xlsx")
    rfq_df = pd.read_excel(r"C:\Drawings\13-march-combined isos\audit\exported_rfq_data_nofieldmap.xlsx")

    bom_converted = bom_df.copy()
    bom_converted["Quantity_Converted"] = bom_converted["quantity"].apply(convert_quantity_to_float)

    columns = ["material_description", "size", "quantity", "Quantity_Converted"]
    bom_converted[columns].to_excel("debug/bom_converted.xlsx")
    print(bom_converted["Quantity_Converted"])


    grouped = bom_converted.groupby(["material_description", "size"])

    final = []
    for key, group in grouped:
        description, size = key
        print(key)

        group_res = []

        expected_sum = 0
        for row in group.itertuples():
            group_res.append(
                {
                    "material_description": description,
                    "pdf_page": row.pdf_page,
                    "pos": row.pos,
                    "original_quantity_value": row.quantity,
                    "quantity_converted": row.Quantity_Converted,
                    "rfq_quantity": None}
            )

            try:
                expected_sum += row.Quantity_Converted
            except:
                pass

        rfq_query = rfq_df[(rfq_df["material_description"] == description) & (rfq_df["size"] == size)]
        if not rfq_query.empty:
            rfq_result = rfq_query.iloc[0]["quantity"]
        else:
            rfq_result = "n/a"

        group_res.insert(0, {"material_description": ""})
        group_res.insert(0, {"material_description": description, "size": size, "expected_sum": expected_sum, "rfq_quantity": rfq_result})
        group_res.insert(0, {"material_description": "----------------START-------------------"})

        final.extend(group_res)

        # add some empty rows
        final.append({"material_description": ""})
        final.append({"material_description": "----------------END---------------"})
        final.append({"material_description": ""})
        final.append({"material_description": ""})
        final.append({"material_description": ""})

    print(final)
    final_df = pd.DataFrame(final)

    print(final_df.columns)
    columns = ["material_description", "pdf_page", "pos", "size", "original_quantity_value", "quantity_converted", "expected_sum", "rfq_quantity"]
    final_df = final_df[columns]
    final_df.to_excel("debug/final_converted_quantities_df.xlsx", index=False)



