import sys
from typing import Optional, Dict, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox, QTextEdit,
    QFileDialog, QFrame, QGridLayout, QGroupBox, QRadioButton, QButtonGroup,
    QProgressBar, QScrollArea, QSplitter, QHeaderView, QAbstractItemView,
    QSpacerItem, QSizePolicy, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient

# Import the database service
from .ais_database_service import AISDatabase
from .pg_connection import DatabaseConfig


class StatusIndicator(QWidget):
    """Modern status indicator with pill-style design"""

    def __init__(self, status="pending", text=""):
        super().__init__()
        self.status = status
        self.text = text
        self.setFixedHeight(28)
        self.setMinimumWidth(80)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Modern color scheme
        colors = {
            "pending": {"bg": QColor(243, 244, 246), "text": QColor(107, 114, 128), "dot": QColor(156, 163, 175)},
            "running": {"bg": QColor(219, 234, 254), "text": QColor(37, 99, 235), "dot": QColor(59, 130, 246)},
            "complete": {"bg": QColor(220, 252, 231), "text": QColor(22, 163, 74), "dot": QColor(34, 197, 94)},
            "error": {"bg": QColor(254, 226, 226), "text": QColor(220, 38, 38), "dot": QColor(239, 68, 68)},
            "conditional": {"bg": QColor(254, 243, 199), "text": QColor(180, 83, 9), "dot": QColor(245, 158, 11)}
        }

        color_scheme = colors.get(self.status, colors["pending"])

        # Draw pill background
        rect = self.rect().adjusted(2, 2, -2, -2)
        painter.setBrush(QBrush(color_scheme["bg"]))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 12, 12)

        # Draw status dot
        painter.setBrush(QBrush(color_scheme["dot"]))
        painter.drawEllipse(8, 10, 8, 8)

        # Draw text
        painter.setPen(color_scheme["text"])
        font = painter.font()
        font.setPointSize(9)
        font.setWeight(QFont.Weight.DemiBold)
        painter.setFont(font)
        painter.drawText(22, 18, self.text or self.status.title())

        # Properly end the painter
        painter.end()


class ProjectHeaderWidget(QWidget):
    """Widget showing active project information"""

    def __init__(self):
        super().__init__()
        self.db_service = AISDatabase()
        self.current_project_id = None
        self.setup_ui()
        self.hide()  # Hidden by default

    def setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 12, 20, 12)

        # Create modern card-style frame
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                border: 1px solid #cbd5e1;
                border-radius: 12px;
                padding: 16px;
            }
        """)

        frame_layout = QHBoxLayout(frame)
        frame_layout.setSpacing(24)

        # Active project section
        project_section = QWidget()
        project_layout = QVBoxLayout(project_section)
        project_layout.setContentsMargins(0, 0, 0, 0)
        project_layout.setSpacing(4)

        self.active_label = QLabel("ACTIVE PROJECT")
        self.active_label.setStyleSheet("""
            color: #475569;
            font-weight: 600;
            font-size: 11px;
            letter-spacing: 0.5px;
        """)

        self.project_name = QLabel("No Project Selected")
        self.project_name.setStyleSheet("""
            font-weight: 700;
            font-size: 16px;
            color: #1e293b;
        """)

        project_layout.addWidget(self.active_label)
        project_layout.addWidget(self.project_name)

        # Info sections
        info_sections = QWidget()
        info_layout = QHBoxLayout(info_sections)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(20)

        # Client section
        client_section = self.create_info_section("Client", "-")
        self.client_name = client_section.findChild(QLabel, "value")

        # Profile section
        profile_section = self.create_info_section("Profile", "-")
        self.profile_name = profile_section.findChild(QLabel, "value")

        # Status section
        status_section = self.create_info_section("Status", "-")
        self.status_badge = status_section.findChild(QLabel, "value")

        # General table row count section
        general_count_section = self.create_info_section("General Rows", "0")
        self.general_count = general_count_section.findChild(QLabel, "value")

        # BOM table row count section
        bom_count_section = self.create_info_section("BOM Rows", "0")
        self.bom_count = bom_count_section.findChild(QLabel, "value")

        info_layout.addWidget(client_section)
        info_layout.addWidget(profile_section)
        info_layout.addWidget(status_section)
        info_layout.addWidget(general_count_section)
        info_layout.addWidget(bom_count_section)

        # Buttons section
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(12)

        # Reset Uploads button
        self.reset_uploads_btn = QPushButton("📁 Reset Uploads")
        self.reset_uploads_btn.setStyleSheet("""
            QPushButton {
                background-color: #06b6d4;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #0891b2;
            }
            QPushButton:pressed {
                background-color: #0e7490;
            }
        """)
        self.reset_uploads_btn.setFixedHeight(40)
        self.reset_uploads_btn.setToolTip("Reset file paths and upload status without clearing project")

        # Reset Steps button
        self.reset_steps_btn = QPushButton("🔄 Reset Steps")
        self.reset_steps_btn.setStyleSheet("""
            QPushButton {
                background-color: #8b5cf6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #7c3aed;
            }
            QPushButton:pressed {
                background-color: #6d28d9;
            }
        """)
        self.reset_steps_btn.setFixedHeight(40)
        self.reset_steps_btn.setToolTip("Reset workflow steps without clearing project or files")

        # Clear button
        self.clear_btn = QPushButton("🗑️ Clear All")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f59e0b;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #d97706;
            }
            QPushButton:pressed {
                background-color: #b45309;
            }
        """)
        self.clear_btn.setFixedHeight(40)
        self.clear_btn.setToolTip("Clear all inputs and messages to start fresh")

        # Change project button
        self.change_btn = QPushButton("Change Project")
        self.change_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
        """)
        self.change_btn.setFixedHeight(40)

        buttons_layout.addWidget(self.reset_uploads_btn)
        buttons_layout.addWidget(self.reset_steps_btn)
        buttons_layout.addWidget(self.clear_btn)
        buttons_layout.addWidget(self.change_btn)

        frame_layout.addWidget(project_section)
        frame_layout.addWidget(info_sections)
        frame_layout.addStretch()
        frame_layout.addWidget(buttons_widget)

        layout.addWidget(frame)

    def create_info_section(self, label_text, value_text):
        """Create a modern info section"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(0, 0, 0, 0)
        section_layout.setSpacing(2)

        label = QLabel(label_text)
        label.setStyleSheet("""
            color: #64748b;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        """)

        value = QLabel(value_text)
        value.setObjectName("value")
        value.setStyleSheet("""
            font-weight: 600;
            font-size: 13px;
            color: #334155;
        """)

        section_layout.addWidget(label)
        section_layout.addWidget(value)

        return section

    def create_separator(self):
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setStyleSheet("color: #bfdbfe;")
        separator.setFixedWidth(1)
        return separator

    def set_project(self, project_name, client_name, profile_name, status, project_id=None):
        # Store project ID for row count filtering
        self.current_project_id = project_id

        # Display project name with ID if available
        if project_id is not None:
            display_name = f"{project_name} (ID: {project_id})"
        else:
            display_name = project_name

        self.project_name.setText(display_name)
        self.client_name.setText(client_name)
        self.profile_name.setText(profile_name)
        self.status_badge.setText(status.replace("-", " ").title())

        # Update row counts
        self.update_row_counts()

        self.show()

    def update_row_counts(self):
        """Update the row counts for general and BOM tables for the current project"""
        try:
            # Get row counts from database filtered by current project
            general_count = self.db_service.get_table_row_count("public.general", self.current_project_id)
            bom_count = self.db_service.get_table_row_count("public.bom", self.current_project_id)

            # Format the counts with commas for readability
            self.general_count.setText(f"{general_count:,}")
            self.bom_count.setText(f"{bom_count:,}")
        except Exception as e:
            # If there's an error, show 0 counts
            self.general_count.setText("0")
            self.bom_count.setText("0")
            print(f"Error updating row counts: {e}")


class ProjectSelectionTab(QWidget):
    """Project selection and creation tab"""

    project_selected = Signal(dict)  # Emits project data

    def __init__(self):
        super().__init__()
        self.db_service = AISDatabase()
        self.clients_data = []
        self.profiles_data = []
        self.projects_data = []
        self.setup_ui()
        self.load_database_data()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)

        # Title section
        title_section = QWidget()
        title_layout = QVBoxLayout(title_section)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)

        title = QLabel("Project Management")
        title.setStyleSheet("""
            font-size: 22px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        """)

        subtitle = QLabel("Select an existing project or create a new one to begin working")
        subtitle.setStyleSheet("""
            color: #64748b;
            font-size: 14px;
            font-weight: 400;
        """)

        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)

        layout.addWidget(title_section)

        # Mode selection
        mode_group = QGroupBox()
        mode_layout = QHBoxLayout(mode_group)

        self.mode_group = QButtonGroup()
        self.select_radio = QRadioButton("Select Existing Project")
        self.create_radio = QRadioButton("Create New Project")
        self.select_radio.setChecked(True)

        self.mode_group.addButton(self.select_radio, 0)
        self.mode_group.addButton(self.create_radio, 1)

        mode_layout.addWidget(self.select_radio)
        mode_layout.addWidget(self.create_radio)

        layout.addWidget(mode_group)

        # Content area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)

        # Search and table for existing projects
        self.setup_project_selection()

        # Form for new project creation
        self.setup_project_creation()

        layout.addWidget(self.content_widget)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.action_btn = QPushButton("Check In to Selected Project")
        self.action_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 14px 28px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1d4ed8, stop:1 #1e40af);
            }
            QPushButton:disabled {
                background-color: #cbd5e1;
                color: #94a3b8;
            }
        """)
        self.action_btn.setEnabled(False)
        self.action_btn.setFixedHeight(48)

        button_layout.addWidget(self.action_btn)
        layout.addLayout(button_layout)

        # Connect signals
        self.mode_group.buttonClicked.connect(self.on_mode_changed)
        self.action_btn.clicked.connect(self.on_action_clicked)

    def setup_project_selection(self):
        # Search with modern styling
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search projects by name or client...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e2e8f0;
                border-radius: 10px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #94a3b8;
            }
        """)
        self.search_edit.setFixedHeight(48)
        self.search_edit.textChanged.connect(self.filter_projects)

        # Projects table with modern styling
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(5)
        self.projects_table.setHorizontalHeaderLabels(["ID", "Project Name", "Client", "Location", "Status"])
        self.projects_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.projects_table.setAlternatingRowColors(True)
        self.projects_table.horizontalHeader().setStretchLastSection(True)
        self.projects_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                background-color: white;
                gridline-color: #f1f5f9;
                selection-background-color: #dbeafe;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f5f9;
            }
            QTableWidget::item:selected {
                background-color: #dbeafe;
                color: #1e40af;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: none;
                border-bottom: 2px solid #e2e8f0;
                padding: 12px;
                font-weight: 600;
                color: #475569;
            }
        """)
        self.projects_table.selectionModel().selectionChanged.connect(self.on_project_selection_changed)

        self.content_layout.addWidget(self.search_edit)
        self.content_layout.addWidget(self.projects_table)

    def setup_project_creation(self):
        # This will be shown when create mode is selected
        self.create_widget = QWidget()
        create_layout = QVBoxLayout(self.create_widget)

        # Client and Profile selection
        client_group = QGroupBox("Client & Profile Selection")
        client_layout = QGridLayout(client_group)

        client_layout.addWidget(QLabel("Client *:"), 0, 0)
        self.client_combo = QComboBox()
        self.client_combo.addItems(["ABC Construction", "XYZ Engineering", "BuildCorp Inc"])
        client_layout.addWidget(self.client_combo, 0, 1)

        client_layout.addWidget(QLabel("Profile *:"), 1, 0)
        self.profile_combo = QComboBox()
        self.profile_combo.addItems(["Standard Profile", "High-Rise Profile", "Industrial Profile"])
        client_layout.addWidget(self.profile_combo, 1, 1)

        create_layout.addWidget(client_group)

        # Project details
        details_group = QGroupBox("Project Details")
        details_layout = QGridLayout(details_group)

        self.project_name_edit = QLineEdit()
        self.location_edit = QLineEdit()
        self.jobsite_edit = QLineEdit()
        self.revision_edit = QLineEdit()
        self.drafter_edit = QLineEdit()
        self.status_combo = QComboBox()
        self.status_combo.addItems(["Pending", "In Progress", "Under Review", "Completed", "On Hold"])
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)

        details_layout.addWidget(QLabel("Project Name *:"), 0, 0)
        details_layout.addWidget(self.project_name_edit, 0, 1)
        details_layout.addWidget(QLabel("Location:"), 0, 2)
        details_layout.addWidget(self.location_edit, 0, 3)

        details_layout.addWidget(QLabel("Jobsite Location:"), 1, 0)
        details_layout.addWidget(self.jobsite_edit, 1, 1)
        details_layout.addWidget(QLabel("Bid Revision:"), 1, 2)
        details_layout.addWidget(self.revision_edit, 1, 3)

        details_layout.addWidget(QLabel("Engineering Drafter:"), 2, 0)
        details_layout.addWidget(self.drafter_edit, 2, 1)
        details_layout.addWidget(QLabel("Status:"), 2, 2)
        details_layout.addWidget(self.status_combo, 2, 3)

        details_layout.addWidget(QLabel("Notes:"), 3, 0)
        details_layout.addWidget(self.notes_edit, 3, 1, 1, 3)

        create_layout.addWidget(details_group)

        self.content_layout.addWidget(self.create_widget)
        self.create_widget.hide()

        # Connect signals for button state updates
        self.project_name_edit.textChanged.connect(self.on_project_name_changed)

    def load_database_data(self):
        """Load real data from the database"""
        try:
            # Test database connection first
            if not self.db_service.test_connection():
                self.show_error("Database connection failed. Please check your connection settings.")
                return

            # Load clients, profiles, and projects
            self.clients_data = self.db_service.get_clients()
            self.profiles_data = self.db_service.get_profiles()
            self.projects_data = self.db_service.get_projects()

            # Populate the UI
            self.populate_client_combo()
            self.populate_profile_combo()
            self.populate_projects_table()

        except Exception as e:
            self.show_error(f"Error loading database data: {str(e)}")

    def populate_client_combo(self):
        """Populate the client combo box with real data"""
        self.client_combo.clear()
        # Add blank option as default
        self.client_combo.addItem("-- Select Client --", None)
        for client in self.clients_data:
            self.client_combo.addItem(client['client_name'], client['id'])
        # Connect signal to update button state when selection changes
        self.client_combo.currentIndexChanged.connect(self.on_client_profile_changed)

    def populate_profile_combo(self):
        """Populate the profile combo box with real data"""
        self.profile_combo.clear()
        # Add blank option as default
        self.profile_combo.addItem("-- Select Profile --", None)
        for profile in self.profiles_data:
            display_text = f"{profile['profile_name']} ({profile['client_name']})"
            self.profile_combo.addItem(display_text, profile['id'])
        # Connect signal to update button state when selection changes
        self.profile_combo.currentIndexChanged.connect(self.on_client_profile_changed)

    def populate_projects_table(self):
        """Populate the projects table with real data"""
        self.projects_table.setRowCount(len(self.projects_data))

        for row, project in enumerate(self.projects_data):
            # ID
            self.projects_table.setItem(row, 0, QTableWidgetItem(str(project['id'])))
            # Project Name
            self.projects_table.setItem(row, 1, QTableWidgetItem(project['project_name'] or ''))
            # Client
            self.projects_table.setItem(row, 2, QTableWidgetItem(project['client_name'] or ''))
            # Location
            self.projects_table.setItem(row, 3, QTableWidgetItem(project['location'] or ''))
            # Status
            status = project['ais_project_status'] or 'Unknown'
            self.projects_table.setItem(row, 4, QTableWidgetItem(status))

    def show_error(self, message: str):
        """Show an error message to the user"""
        QMessageBox.critical(self, "Error", message)
        print(f"Database Error: {message}")  # Also print to console for debugging

    def filter_projects(self, text):
        for row in range(self.projects_table.rowCount()):
            match = False
            for col in range(self.projects_table.columnCount()):
                item = self.projects_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    match = True
                    break
            self.projects_table.setRowHidden(row, not match)

    def on_mode_changed(self, button):
        if button == self.select_radio:
            self.projects_table.show()
            self.search_edit.show()
            self.create_widget.hide()
            self.action_btn.setText("Check In to Selected Project")
            # Update button state for selection mode
            self.update_button_state()
        else:
            self.projects_table.hide()
            self.search_edit.hide()
            self.create_widget.show()
            self.action_btn.setText("Create Project & Continue")
            # Update button state for creation mode
            self.update_button_state()

    def on_project_selection_changed(self):
        # Only update button state if we're in selection mode
        if self.select_radio.isChecked():
            self.update_button_state()

    def on_project_name_changed(self):
        """Called when project name field changes"""
        # Only update button state if we're in creation mode
        if self.create_radio.isChecked():
            self.update_button_state()

    def on_client_profile_changed(self):
        """Called when client or profile selection changes"""
        # Only update button state if we're in creation mode
        if self.create_radio.isChecked():
            self.update_button_state()

    def update_button_state(self):
        """Update the action button enabled state based on current mode"""
        if self.select_radio.isChecked():
            # Selection mode: enable if a project is selected
            has_selection = bool(self.projects_table.selectionModel().selectedRows())
            self.action_btn.setEnabled(has_selection)
        else:
            # Creation mode: enable if all required fields are filled
            has_project_name = bool(self.project_name_edit.text().strip())
            has_client = self.client_combo.currentData() is not None
            has_profile = self.profile_combo.currentData() is not None

            all_required_filled = has_project_name and has_client and has_profile
            self.action_btn.setEnabled(all_required_filled)

    def on_action_clicked(self):
        if self.select_radio.isChecked():
            # Get selected project
            selected_rows = self.projects_table.selectionModel().selectedRows()
            if selected_rows:
                row = selected_rows[0].row()
                project_id = int(self.projects_table.item(row, 0).text())

                # Find the full project data
                project_data = None
                for project in self.projects_data:
                    if project['id'] == project_id:
                        project_data = {
                            'id': project['id'],
                            'name': project['project_name'],
                            'client': project['client_name'],
                            'location': project['location'],
                            'status': project['ais_project_status'] or 'Unknown',
                            'profile': project['profile_name'] or 'No Profile',
                            'client_id': project['client_id'],
                            'profile_id': project['profile_id']
                        }
                        break

                if project_data:
                    self.project_selected.emit(project_data)
        else:
            # Create new project - show confirmation dialog first
            if self.project_name_edit.text():
                # Get selected client and profile IDs
                client_id = self.client_combo.currentData()
                profile_id = self.profile_combo.currentData()

                # Validate required fields
                if client_id is None:
                    self.show_error("Please select a client before creating the project.")
                    return
                if profile_id is None:
                    self.show_error("Please select a profile before creating the project.")
                    return

                # Show confirmation dialog
                client_name = self.client_combo.currentText()
                profile_name = self.profile_combo.currentText()
                project_name = self.project_name_edit.text()

                confirmation_msg = f"""
<b>Confirm Project Creation</b><br><br>
<b>Project Name:</b> {project_name}<br>
<b>Client:</b> {client_name}<br>
<b>Profile:</b> {profile_name}<br><br>
Are you sure you want to create this project with these settings?
                """.strip()

                reply = QMessageBox.question(
                    self,
                    'Confirm Project Creation',
                    confirmation_msg,
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.Cancel,
                    QMessageBox.StandardButton.Cancel  # Default to Cancel
                )

                if reply != QMessageBox.StandardButton.Yes:
                    return  # User cancelled

                try:
                    new_project_data = {
                        'client_id': client_id,
                        'project_name': project_name,
                        'location': self.location_edit.text(),
                        'jobsite_location': self.jobsite_edit.text(),
                        'bid_revision': self.revision_edit.text(),
                        'engineering_drafter': self.drafter_edit.text(),
                        'ais_project_status': self.status_combo.currentText(),
                        'notes': self.notes_edit.toPlainText(),
                        'profile_id': profile_id
                    }

                    # Create the project in the database
                    project_id = self.db_service.create_project(new_project_data)

                    if project_id:
                        # Get the full project data for the interface
                        project_data = {
                            'id': project_id,
                            'name': new_project_data['project_name'],
                            'client': self.client_combo.currentText(),
                            'location': new_project_data['location'],
                            'status': new_project_data['ais_project_status'],
                            'profile': self.profile_combo.currentText().split(' (')[0],  # Remove client name
                            'client_id': client_id,
                            'profile_id': profile_id
                        }

                        # Refresh the projects list
                        self.load_database_data()

                        self.project_selected.emit(project_data)
                    else:
                        self.show_error("Failed to create project in database")

                except Exception as e:
                    self.show_error(f"Error creating project: {str(e)}")

    def clear_all_inputs(self):
        """Clear all form inputs and reset selections"""
        # Clear search field
        if hasattr(self, 'search_edit'):
            self.search_edit.clear()

        # Clear project table selection
        if hasattr(self, 'projects_table'):
            self.projects_table.clearSelection()

        # Clear project creation form
        if hasattr(self, 'project_name_edit'):
            self.project_name_edit.clear()
        if hasattr(self, 'location_edit'):
            self.location_edit.clear()
        if hasattr(self, 'jobsite_edit'):
            self.jobsite_edit.clear()
        if hasattr(self, 'revision_edit'):
            self.revision_edit.clear()
        if hasattr(self, 'drafter_edit'):
            self.drafter_edit.clear()
        if hasattr(self, 'notes_edit'):
            self.notes_edit.clear()

        # Reset combo boxes to first item
        if hasattr(self, 'client_combo') and self.client_combo.count() > 0:
            self.client_combo.setCurrentIndex(0)
        if hasattr(self, 'profile_combo') and self.profile_combo.count() > 0:
            self.profile_combo.setCurrentIndex(0)
        if hasattr(self, 'status_combo') and self.status_combo.count() > 0:
            self.status_combo.setCurrentIndex(0)

        # Reset to select mode
        if hasattr(self, 'select_radio'):
            self.select_radio.setChecked(True)
            self.on_mode_changed(self.select_radio)

        # Disable action button
        if hasattr(self, 'action_btn'):
            self.action_btn.setEnabled(False)

    def reset_workflow_steps(self):
        """Reset workflow steps - ProjectSelectionTab doesn't have workflow steps"""
        # This tab doesn't have workflow steps, but we include this method for consistency
        pass

    def reset_upload_sections(self):
        """Reset upload sections - ProjectSelectionTab doesn't have upload sections"""
        # This tab doesn't have upload sections, but we include this method for consistency
        pass


class DataImportTab(QWidget):
    """Data import tab with file upload grid"""

    # Signal emitted when data is uploaded successfully
    data_uploaded = Signal()

    def __init__(self):
        super().__init__()
        self.project_id = None
        self.client_id = None
        self.profile_id = None
        self.db_service = AISDatabase()
        self.setup_ui()

    def setup_ui(self):
        # Create main scroll area for the entire tab
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        main_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        main_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)

        # Create the main content widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(32)

        # Title section with more space
        title_section = QWidget()
        title_layout = QVBoxLayout(title_section)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(12)

        title = QLabel("📁 Data Import & Processing Pipeline")
        title.setStyleSheet("""
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        """)

        subtitle = QLabel("Upload your data files and follow the automated processing workflow to prepare your project data.")
        subtitle.setStyleSheet("""
            color: #64748b;
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
        """)

        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)

        layout.addWidget(title_section)

        # Collapsible Import Sections
        self.setup_collapsible_import_sections()
        layout.addWidget(self.import_sections_widget)

        # Workflow Checklist with more space
        self.setup_workflow_checklist()
        layout.addWidget(self.workflow_widget)

        # Set the content widget to the scroll area
        main_scroll.setWidget(content_widget)

        # Set the scroll area as the main layout
        tab_layout = QVBoxLayout(self)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(main_scroll)

    def test_upload(self):
        """Test upload functionality for debugging"""
        print(f"Test Upload - Project ID: {self.project_id}")
        print(f"Test Upload - Client ID: {self.client_id}")
        print(f"Test Upload - Profile ID: {self.profile_id}")

        if self.project_id is None:
            print("ERROR: No project selected! Please check in to a project first.")
        else:
            print("SUCCESS: Project is set correctly!")

            # Test the database service
            try:
                result = self.db_service.import_data_file(
                    file_path="test.xlsx",  # This will fail but we can see the error
                    table_name="public.general",
                    client_id=self.client_id,
                    profile_id=self.profile_id,
                    project_id=self.project_id
                )
                print(f"Import test result: {result}")
            except Exception as e:
                print(f"Import test error: {e}")

    def setup_collapsible_import_sections(self):
        """Create collapsible sections for each import type"""
        self.import_sections_widget = QWidget()
        sections_layout = QVBoxLayout(self.import_sections_widget)
        sections_layout.setContentsMargins(0, 0, 0, 0)
        sections_layout.setSpacing(20)

        # Section title
        section_title = QLabel("📤 Data Upload Sections")
        section_title.setStyleSheet("""
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        """)
        sections_layout.addWidget(section_title)

        # Import sections data
        tables = [
            {
                "name": "Verified Material Classifications",
                "schema": "public.verified_material_classifications",
                "color": "#2563eb",
                "description": "Upload material classification data to categorize and validate BOM items",
                "icon": "🏷️"
            },
            {
                "name": "Bill of Materials (BOM)",
                "schema": "public.bom",
                "color": "#059669",
                "description": "Upload your project's Bill of Materials for processing and analysis",
                "icon": "📋"
            },
            {
                "name": "General Project Data",
                "schema": "public.general",
                "color": "#7c3aed",
                "description": "Upload additional project data and aggregated information",
                "icon": "📊"
            }
        ]

        # Create collapsible sections
        self.import_sections = []
        for table_data in tables:
            section_widget = self.create_collapsible_import_section(table_data)
            sections_layout.addWidget(section_widget)
            self.import_sections.append(section_widget)

    def create_collapsible_import_section(self, table_data):
        """Create a collapsible section for a single import type"""
        section_widget = QWidget()
        section_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #e5e7eb;
                border-radius: 16px;
                margin: 4px 0px;
            }
        """)

        main_layout = QVBoxLayout(section_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Header (always visible)
        header_widget = QWidget()
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                border-radius: 14px 14px 0px 0px;
                padding: 20px;
            }
        """)
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(24, 20, 24, 20)
        header_layout.setSpacing(20)

        # Icon and info
        info_layout = QVBoxLayout()
        info_layout.setSpacing(8)

        # Title with icon
        title_layout = QHBoxLayout()
        title_layout.setSpacing(12)

        icon_label = QLabel(table_data["icon"])
        icon_label.setStyleSheet("font-size: 24px;")

        title_label = QLabel(table_data["name"])
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
        """)

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # Description
        desc_label = QLabel(table_data["description"])
        desc_label.setStyleSheet("""
            color: #64748b;
            font-size: 14px;
            line-height: 1.4;
        """)
        desc_label.setWordWrap(True)

        # Schema label
        schema_label = QLabel(table_data["schema"])
        schema_label.setStyleSheet("""
            color: #6b7280;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            background-color: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
        """)

        info_layout.addLayout(title_layout)
        info_layout.addWidget(desc_label)
        info_layout.addWidget(schema_label)

        header_layout.addLayout(info_layout, 1)

        # Status and toggle
        status_layout = QVBoxLayout()
        status_layout.setSpacing(12)
        status_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Status indicator
        status_widget = StatusIndicator("pending", "No File")
        status_layout.addWidget(status_widget)

        # Toggle button
        toggle_btn = QPushButton("▼ Show Upload")
        toggle_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {table_data["color"]};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                opacity: 0.9;
            }}
        """)

        status_layout.addWidget(toggle_btn)
        header_layout.addLayout(status_layout)

        # Content (initially hidden)
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 0px 0px 14px 14px;
                padding: 24px;
            }
        """)
        content_layout = self.create_upload_content(table_data)
        content_widget.setLayout(content_layout)
        content_widget.setVisible(False)

        # Connect toggle functionality
        def toggle_content():
            is_visible = content_widget.isVisible()
            content_widget.setVisible(not is_visible)
            toggle_btn.setText("▲ Hide Upload" if not is_visible else "▼ Show Upload")

        toggle_btn.clicked.connect(toggle_content)

        main_layout.addWidget(header_widget)
        main_layout.addWidget(content_widget)

        # Store references for later access
        section_widget.table_name = table_data["schema"]
        section_widget.status_widget = status_widget
        section_widget.content_widget = content_widget
        section_widget.toggle_btn = toggle_btn

        return section_widget

    def create_upload_content(self, table_data):
        """Create the upload content for a collapsible section"""
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(24)

        # File selection area
        file_section = QWidget()
        file_section.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                border: 2px dashed #cbd5e1;
                border-radius: 12px;
                padding: 24px;
            }
        """)
        file_layout = QVBoxLayout(file_section)
        file_layout.setSpacing(16)

        # File selection header
        file_header = QLabel("📁 Select Data File")
        file_header.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #374151;
        """)

        # File input row
        file_input_layout = QHBoxLayout()
        file_input_layout.setSpacing(12)

        file_edit = QLineEdit()
        file_edit.setPlaceholderText("No file selected - Click Browse to select a file")
        file_edit.setReadOnly(True)
        file_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: white;
                color: #6b7280;
            }
        """)

        browse_btn = QPushButton("📂 Browse Files")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        browse_btn.clicked.connect(lambda: self.browse_file(file_edit))

        file_input_layout.addWidget(file_edit, 1)
        file_input_layout.addWidget(browse_btn)

        file_layout.addWidget(file_header)
        file_layout.addLayout(file_input_layout)

        # File info and actions
        info_actions_layout = QHBoxLayout()
        info_actions_layout.setSpacing(24)

        # File info
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setSpacing(8)

        info_title = QLabel("📊 File Information")
        info_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        """)

        # Row count display
        count_layout = QHBoxLayout()
        count_layout.setSpacing(8)

        count_label = QLabel("0")
        count_label.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
        """)

        count_text = QLabel("rows detected")
        count_text.setStyleSheet("""
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        """)

        count_layout.addWidget(count_label)
        count_layout.addWidget(count_text)
        count_layout.addStretch()

        info_layout.addWidget(info_title)
        info_layout.addLayout(count_layout)

        # Upload actions
        actions_widget = QWidget()
        actions_layout = QVBoxLayout(actions_widget)
        actions_layout.setSpacing(12)

        actions_title = QLabel("🚀 Upload Actions")
        actions_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        """)

        # Upload button
        upload_btn = QPushButton("⬆️ Upload to Database")
        upload_btn.setEnabled(False)
        upload_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #e5e7eb;
                color: #9ca3af;
                border: none;
                border-radius: 8px;
                padding: 16px 24px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:enabled {{
                background-color: {table_data["color"]};
                color: white;
            }}
            QPushButton:enabled:hover {{
                opacity: 0.9;
            }}
        """)

        actions_layout.addWidget(actions_title)
        actions_layout.addWidget(upload_btn)

        info_actions_layout.addWidget(info_widget, 1)
        info_actions_layout.addWidget(actions_widget, 1)

        content_layout.addWidget(file_section)
        content_layout.addLayout(info_actions_layout)

        # Store references for later access
        content_layout.file_edit = file_edit
        content_layout.count_label = count_label
        content_layout.upload_btn = upload_btn

        return content_layout

    def create_import_row(self, name, schema, color):
        row_widget = QWidget()
        row_widget.table_name = schema  # Store the table name for later use
        row_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 12px;
            }
            QWidget:hover {
                border-color: #cbd5e1;
                background-color: #f8fafc;
            }
        """)
        layout = QHBoxLayout(row_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # Table type with color accent
        type_widget = QWidget()
        type_layout = QVBoxLayout(type_widget)
        type_layout.setContentsMargins(0, 0, 0, 0)
        type_layout.setSpacing(4)

        # Add color accent bar
        accent_bar = QFrame()
        accent_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 2px;
            }}
        """)
        accent_bar.setFixedSize(4, 40)

        name_label = QLabel(name)
        name_label.setStyleSheet("""
            font-weight: 700;
            font-size: 15px;
            color: #1e293b;
        """)
        schema_label = QLabel(schema)
        schema_label.setStyleSheet("""
            color: #64748b;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        """)

        type_layout.addWidget(name_label)
        type_layout.addWidget(schema_label)

        type_container = QWidget()
        type_container_layout = QHBoxLayout(type_container)
        type_container_layout.setContentsMargins(0, 0, 0, 0)
        type_container_layout.setSpacing(12)
        type_container_layout.addWidget(accent_bar)
        type_container_layout.addWidget(type_widget)

        # File selection with modern styling
        file_widget = QWidget()
        file_layout = QHBoxLayout(file_widget)
        file_layout.setContentsMargins(0, 0, 0, 0)
        file_layout.setSpacing(8)

        file_edit = QLineEdit()
        file_edit.setPlaceholderText("No file selected")
        file_edit.setReadOnly(True)
        file_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 13px;
                background-color: #f8fafc;
            }
        """)

        browse_btn = QPushButton("Browse")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #f1f5f9;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px 16px;
                font-weight: 600;
                color: #475569;
            }
            QPushButton:hover {
                background-color: #e2e8f0;
                border-color: #cbd5e1;
            }
        """)
        browse_btn.clicked.connect(lambda: self.browse_file(file_edit))

        file_layout.addWidget(file_edit, 1)
        file_layout.addWidget(browse_btn)

        # Row count with modern styling
        count_widget = QWidget()
        count_layout = QVBoxLayout(count_widget)
        count_layout.setContentsMargins(0, 0, 0, 0)
        count_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        count_layout.setSpacing(2)

        count_label = QLabel("0")
        count_label.setStyleSheet("""
            font-size: 24px;
            font-family: 'Segoe UI', system-ui;
            font-weight: 700;
            color: #1e293b;
        """)
        rows_label = QLabel("rows")
        rows_label.setStyleSheet("""
            color: #64748b;
            font-size: 11px;
            font-weight: 500;
        """)

        count_layout.addWidget(count_label)
        count_layout.addWidget(rows_label)

        # Status
        status_widget = StatusIndicator("pending", "Pending")

        # Action button
        upload_btn = QPushButton("Upload")
        upload_btn.setEnabled(False)
        upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px 16px;
                font-weight: 600;
                color: #64748b;
            }
            QPushButton:enabled {
                background-color: #3b82f6;
                border-color: #3b82f6;
                color: white;
            }
            QPushButton:enabled:hover {
                background-color: #2563eb;
                border-color: #2563eb;
            }
        """)

        layout.addWidget(type_container, 3)
        layout.addWidget(file_widget, 4)
        layout.addWidget(count_widget, 2)
        layout.addWidget(status_widget, 2)
        layout.addWidget(upload_btn, 1)

        # Store table name on row widget for reference
        row_widget.table_name = schema
        return row_widget

    def setup_workflow_checklist(self):
        """Create the workflow checklist section"""
        self.workflow_widget = QWidget()
        self.workflow_widget.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 16px;
                margin-top: 32px;
            }
        """)

        main_layout = QVBoxLayout(self.workflow_widget)
        main_layout.setContentsMargins(32, 28, 32, 32)
        main_layout.setSpacing(24)

        # Header section
        header_section = QWidget()
        header_layout = QVBoxLayout(header_section)
        header_layout.setSpacing(12)

        title_label = QLabel("⚙️ Automated Processing Workflow")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        """)

        subtitle_label = QLabel("Follow these steps in order after uploading your data files. Each step depends on the completion of previous steps.")
        subtitle_label.setStyleSheet("""
            color: #64748b;
            font-size: 16px;
            line-height: 1.5;
        """)
        subtitle_label.setWordWrap(True)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        main_layout.addWidget(header_section)

        # Progress indicator
        progress_section = QWidget()
        progress_layout = QHBoxLayout(progress_section)
        progress_layout.setSpacing(16)

        progress_label = QLabel("📊 Progress Overview:")
        progress_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        """)

        # Status legend with more space
        legend_layout = QHBoxLayout()
        legend_layout.setSpacing(20)
        statuses = [
            ("pending", "⏳ Pending"),
            ("running", "🔄 Running"),
            ("complete", "✅ Complete"),
            ("error", "❌ Error"),
            ("conditional", "🔶 Optional")
        ]

        for status, text in statuses:
            status_widget = StatusIndicator(status, text)
            legend_layout.addWidget(status_widget)

        progress_layout.addWidget(progress_label)
        progress_layout.addLayout(legend_layout)
        progress_layout.addStretch()
        main_layout.addWidget(progress_section)

        # Workflow steps with more spacing
        steps_container = QWidget()
        steps_layout = QVBoxLayout(steps_container)
        steps_layout.setSpacing(16)

        self.workflow_steps = self.create_workflow_steps()
        for step_widget in self.workflow_steps:
            steps_layout.addWidget(step_widget)

        main_layout.addWidget(steps_container)

        return self.workflow_widget

    def create_workflow_steps(self):
        """Create the workflow checklist steps based on the Abstract Workflow"""
        steps_data = [
            {
                "step": "1",
                "title": "Upload Data Files",
                "description": "Upload Verified Material Classifications and/or BOM data using the file upload section above",
                "type": "prerequisite",
                "status": "pending",
                "dependencies": [],
                "sql": None
            },
            {
                "step": "1.1",
                "title": "🔍 Check Multiple Classifications",
                "description": "Integrity check: Verify no items have multiple classification fields filled",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT
    id,
    project_id,
    material_description,
    rfq_scope,
    general_category,
    unit_of_measure,
    pipe_category,
    fitting_category,
    valve_type,
    gasket_category,
    bolt_category,
    (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) AS value_count
FROM public.verified_material_classifications
WHERE project_id = {project_id}
    AND (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) > 1
ORDER BY value_count DESC, material_description ASC;"""
            },
            {
                "step": "1.2",
                "title": "🔍 Check BOM Size1 Blanks",
                "description": "Integrity check: Identify BOM items with missing size1 values",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT id, project_id, material_description, rfq_scope, general_category, pipe_category, fitting_category, valve_type, bolt_category
FROM public.bom
WHERE project_id = {project_id}
AND size1 IS NULL
ORDER BY material_description ASC;"""
            },
            {
                "step": "1.3",
                "title": "🔍 Check BOM Quantity Issues",
                "description": "Integrity check: Identify BOM items with missing or zero quantities",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT id, project_id, material_description, rfq_scope, general_category, pipe_category, fitting_category, valve_type, bolt_category
FROM public.bom
WHERE project_id = {project_id}
AND (quantity IS NULL OR quantity = 0)
ORDER BY material_description ASC;"""
            },
            {
                "step": "2a",
                "title": "Generate RFQ Input from BOM",
                "description": "Create RFQ input records from uploaded BOM data",
                "type": "function",
                "status": "pending",
                "dependencies": ["bom_uploaded"],
                "sql": "SELECT * FROM generate_rfq_input_from_bom(ARRAY[{project_id}]);"
            },
            {
                "step": "2b",
                "title": "Update RFQ Input Categories",
                "description": "Categorize RFQ input records using verified material classifications",
                "type": "function",
                "status": "pending",
                "dependencies": ["bom_uploaded"],
                "sql": "SELECT * FROM update_rfq_input_categories(ARRAY[{project_id}]);"
            },
            {
                "step": "2b.1",
                "title": "🔍 Check RFQ Input Integrity",
                "description": "Integrity check: Verify RFQ input records have proper general categories",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT id, project_id, material_description, rfq_scope, general_category, pipe_category, fitting_category, valve_type, bolt_category
FROM public.atem_rfq_input
WHERE project_id = {project_id}
AND general_category IS NULL
AND rfq_scope NOT IN ('Instruments', 'Miscellaneous', 'Bolts', 'Gaskets')
AND material_description NOT ILIKE '%PLUG%'
AND material_description NOT ILIKE '%HOSE%'
AND material_description NOT ILIKE '%NIPPLE%'
AND material_description NOT ILIKE '%COUPLING%'
AND material_description NOT ILIKE '%BLANK%'
ORDER BY material_description ASC;"""
            },
            {
                "step": "2c",
                "title": "Update BOM Sizes (Optional)",
                "description": "Update size1 and size2 fields if not present in BOM data",
                "type": "function",
                "status": "pending",
                "dependencies": ["bom_uploaded"],
                "sql": "SELECT * FROM update_bom_sizes_for_projects(ARRAY[{project_id}]);",
                "optional": True
            },
            {
                "step": "2c.1",
                "title": "🔍 Check RFQ Multiple Classifications",
                "description": "Integrity check: Verify no RFQ items have multiple classification fields filled",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT
    id,
    project_id,
    material_description,
    rfq_scope,
    general_category,
    unit_of_measure,
    pipe_category,
    fitting_category,
    valve_type,
    gasket_category,
    bolt_category,
    (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) AS value_count
FROM public.atem_rfq_input
WHERE project_id = {project_id}
    AND (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
         CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) > 1
ORDER BY value_count DESC, material_description ASC;"""
            },
            {
                "step": "3",
                "title": "Update BOM from RFQ Input",
                "description": "Synchronize BOM table with processed RFQ input data",
                "type": "function",
                "status": "pending",
                "dependencies": ["step_2a", "step_2b"],
                "sql": "SELECT * FROM update_bom_from_rfq_input(ARRAY[{project_id}]);"
            },
                        {
                "step": "3.1",
                "title": "🔍 Check BOM Integrity",
                "description": "Integrity check: Verify BOM records have proper general categories",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT id, project_id, material_description, rfq_scope, general_category, pipe_category, fitting_category, valve_type, bolt_category
FROM public.bom
WHERE project_id = {project_id}
AND general_category IS NULL
AND rfq_scope NOT IN ('Instruments', 'Miscellaneous', 'Bolts', 'Gaskets')
AND material_description NOT ILIKE '%PLUG%'
AND material_description NOT ILIKE '%HOSE%'
AND material_description NOT ILIKE '%NIPPLE%'
AND material_description NOT ILIKE '%COUPLING%'
AND material_description NOT ILIKE '%BLANK%'
ORDER BY material_description ASC;"""
            },
            {
                "step": "4",
                "title": "🔍 Final RFQ Classification Check",
                "description": "Integrity check: Final verification of RFQ classification integrity",
                "type": "integrity_check",
                "status": "pending",
                "dependencies": [],
                "sql": """SELECT
    id,
    project_id,
    material_description,
    rfq_scope,
    general_category,
    unit_of_measure,
    pipe_category,
    fitting_category,
    valve_type,
    gasket_category,
    bolt_category,
    (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) AS value_count
FROM public.atem_rfq_input
WHERE project_id = {project_id}
AND (CASE WHEN pipe_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN fitting_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN valve_type IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN gasket_category IS NOT NULL THEN 1 ELSE 0 END +
     CASE WHEN bolt_category IS NOT NULL THEN 1 ELSE 0 END) > 1
ORDER BY value_count DESC, material_description ASC;"""
            },
            {
                "step": "4a",
                "title": "Validate BOM vs RFQ View",
                "description": "Validate data consistency between BOM and RFQ view",
                "type": "validation",
                "status": "pending",
                "dependencies": ["step_3"],
                "sql": "SELECT * FROM validate_bom_vs_view({project_id});"
            },
            {
                "step": "4b",
                "title": "Generate General Aggregation",
                "description": "Calculate comprehensive totals for all BOM components and areas",
                "type": "validation",
                "status": "pending",
                "dependencies": ["step_3"],
                "sql": """SELECT
    SUM(length) AS total_length,
    SUM(elbows_90) AS total_elbows_90,
    SUM(elbows_45) AS total_elbows_45,
    SUM(bevels) AS total_bevels,
    SUM(tees) AS total_tees,
    SUM(reducers) AS total_reducers,
    SUM(caps) AS total_caps,
    SUM(flanges) AS total_flanges,
    SUM(valves_flanged) AS total_valves_flanged,
    SUM(valves_welded) AS total_valves_welded,
    SUM(cut_outs) AS total_cut_outs,
    SUM(supports) AS total_supports,
    SUM(bends) AS total_bends,
    SUM(union_couplings) AS total_union_couplings,
    SUM(expansion_joints) AS total_expansion_joints,
    SUM(field_welds) AS total_field_welds,
    SUM(calculated_eq_length) AS total_calculated_eq_length,
    SUM(calculated_area) AS total_calculated_area
FROM manage_bom_to_general_aggregation_full({project_id}, FALSE);"""
            }
        ]

        step_widgets = []
        for step_data in steps_data:
            step_widget = self.create_checklist_step(step_data)
            step_widgets.append(step_widget)

        return step_widgets

    def create_checklist_step(self, step_data):
        """Create a checklist step widget"""
        step_widget = QWidget()

        # Special styling for integrity checks
        if step_data.get("type") == "integrity_check":
            step_widget.setStyleSheet("""
                QWidget {
                    background-color: #fef3c7;
                    border: 2px solid #f59e0b;
                    border-radius: 12px;
                    margin: 6px 0px;
                }
                QWidget:hover {
                    border-color: #d97706;
                    background-color: #fef3c7;
                }
            """)
        else:
            step_widget.setStyleSheet("""
                QWidget {
                    background-color: white;
                    border: 2px solid #e5e7eb;
                    border-radius: 12px;
                    margin: 6px 0px;
                }
                QWidget:hover {
                    border-color: #cbd5e1;
                    background-color: #f9fafb;
                }
            """)

        layout = QHBoxLayout(step_widget)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(24)

        # Step number/checkbox with larger size
        step_indicator = QWidget()
        step_indicator.setFixedSize(48, 48)
        step_indicator.setStyleSheet("""
            QWidget {
                background-color: #f1f5f9;
                border: 3px solid #e2e8f0;
                border-radius: 24px;
            }
        """)

        step_layout = QVBoxLayout(step_indicator)
        step_layout.setContentsMargins(0, 0, 0, 0)
        step_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        step_label = QLabel(step_data["step"])
        step_label.setStyleSheet("""
            font-weight: 700;
            font-size: 14px;
            color: #475569;
        """)
        step_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        step_layout.addWidget(step_label)

        layout.addWidget(step_indicator)

        # Step content with more space
        content_layout = QVBoxLayout()
        content_layout.setSpacing(8)

        # Title
        title_label = QLabel(step_data["title"])
        title_label.setStyleSheet("""
            font-weight: 600;
            font-size: 16px;
            color: #1e293b;
        """)

        # Description
        desc_label = QLabel(step_data["description"])
        desc_label.setStyleSheet("""
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        """)
        desc_label.setWordWrap(True)

        # Dependencies info (if any)
        if step_data.get("dependencies"):
            deps_text = f"Requires: {', '.join(step_data['dependencies'])}"
            deps_label = QLabel(deps_text)
            deps_label.setStyleSheet("""
                color: #9ca3af;
                font-size: 12px;
                font-style: italic;
            """)
            content_layout.addWidget(title_label)
            content_layout.addWidget(desc_label)
            content_layout.addWidget(deps_label)
        else:
            content_layout.addWidget(title_label)
            content_layout.addWidget(desc_label)

        layout.addLayout(content_layout, 1)

        # Status and action with more space
        action_layout = QVBoxLayout()
        action_layout.setSpacing(12)
        action_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Status indicator
        status_text = "Optional" if step_data.get("optional", False) else "Pending"
        status_color = "conditional" if step_data.get("optional", False) else "pending"
        status_widget = StatusIndicator(status_color, status_text)
        action_layout.addWidget(status_widget)

        # Action button (for function, validation, and integrity check steps)
        if step_data["type"] in ["function", "validation", "integrity_check"]:
            if step_data["type"] == "integrity_check":
                run_btn = QPushButton("🔍 Run Check")
                run_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f59e0b;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: 600;
                        color: white;
                    }
                    QPushButton:hover {
                        background-color: #d97706;
                    }
                    QPushButton:disabled {
                        background-color: #e5e7eb;
                        color: #9ca3af;
                    }
                """)
            else:
                run_btn = QPushButton("▶️ Execute Step")
                run_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3b82f6;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: 600;
                        color: white;
                    }
                    QPushButton:hover {
                        background-color: #2563eb;
                    }
                    QPushButton:disabled {
                        background-color: #e5e7eb;
                        color: #9ca3af;
                    }
                """)
            # Always enable if project is selected - remove dependency checking
            run_btn.setEnabled(True)
            run_btn.clicked.connect(lambda: self.run_checklist_step(step_data))
            action_layout.addWidget(run_btn)

            # Add diagnostic button for BOM-related steps
            if step_data["step"] in ["2a", "2b"]:
                diag_btn = QPushButton("🔍 Check Data")
                diag_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #6b7280;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: 600;
                        color: white;
                    }
                    QPushButton:hover {
                        background-color: #4b5563;
                    }
                """)
                diag_btn.clicked.connect(lambda: self.run_diagnostic_query(step_data["step"]))
                action_layout.addWidget(diag_btn)

            # Store reference for later updates
            step_widget.run_button = run_btn
            step_widget.status_widget = status_widget

        layout.addLayout(action_layout)

        # Store step data for reference
        step_widget.step_data = step_data
        return step_widget

    def run_checklist_step(self, step_data):
        """Execute a checklist step"""
        if not self.project_id:
            print("No project selected")
            return

        if not step_data.get("sql"):
            print(f"No SQL command for step: {step_data['title']}")
            return

        # Format SQL with project_id
        sql = step_data["sql"].format(project_id=self.project_id)

        if step_data.get("type") == "integrity_check":
            print(f"\n🔍 Running integrity check {step_data['step']}: {step_data['title']}")
        else:
            print(f"Executing step {step_data['step']}: {step_data['title']}")
        print(f"SQL: {sql}")

        # Update step status to running
        self.update_step_status(step_data["step"], "running")

        try:
            # Import the execute_query function
            from .pg_connection import execute_query

            # Check if required functions exist before executing
            self.ensure_workflow_functions_exist()

            # Execute the SQL command
            result = execute_query(sql, config=self.db_service.db_config, fetch="all")

            # Log the result and handle data storage for validation and integrity check steps
            if result:
                if step_data.get("type") == "integrity_check":
                    # Special handling for integrity checks
                    if isinstance(result, list):
                        if len(result) == 0:
                            print(f"✅ INTEGRITY CHECK PASSED: No issues found")
                        else:
                            print(f"⚠️  INTEGRITY CHECK FOUND {len(result)} ISSUES:")
                            # Show first few problematic records
                            for i, row in enumerate(result[:5]):
                                row_dict = dict(row) if hasattr(row, '_asdict') else row
                                print(f"  Issue {i+1}: {row_dict}")
                            if len(result) > 5:
                                print(f"  ... and {len(result) - 5} more issues")

                            # Store results for export
                            self.store_validation_results(step_data["step"], result, step_data["title"])
                            # Add export button for integrity checks with issues
                            self.add_export_button(step_data["step"])
                else:
                    print(f"Step executed successfully. Result: {result}")

                    # Check for zero results and provide diagnostic information
                    if isinstance(result, list) and len(result) > 0:
                        first_result = result[0]
                        if hasattr(first_result, 'values') and len(first_result.values()) > 0:
                            result_value = list(first_result.values())[0]
                            if result_value == 0:
                                print(f"⚠️  WARNING: Function returned 0 - no records were processed!")
                                print(f"   This might indicate:")
                                print(f"   - No BOM data exists for project {self.project_id}")
                                print(f"   - BOM data doesn't meet the criteria for this operation")
                                print(f"   - Data may need to be uploaded first")

                                # Add diagnostic query for BOM data
                                self.run_diagnostic_query(step_data["step"])

                    # Store results for validation steps (4a and 4b) for export
                    if step_data["step"] in ["4a", "4b"]:
                        self.store_validation_results(step_data["step"], result, step_data["title"])

                    if isinstance(result, list) and len(result) > 0:
                        # For functions that return values, show the first few results
                        if len(result) <= 5:
                            for row in result:
                                print(f"  Result row: {dict(row) if hasattr(row, '_asdict') else row}")
                        else:
                            print(f"  Returned {len(result)} rows")
                            for i, row in enumerate(result[:3]):
                                print(f"  Row {i+1}: {dict(row) if hasattr(row, '_asdict') else row}")
                            print(f"  ... and {len(result) - 3} more rows")
            else:
                if step_data.get("type") == "integrity_check":
                    print(f"✅ INTEGRITY CHECK PASSED: No issues found")
                else:
                    print("Step executed successfully (no results returned)")

            # Update step status to complete
            self.update_step_status(step_data["step"], "complete")

            # Add export button for validation steps (handled above for integrity checks)
            if step_data["step"] in ["4a", "4b"] and result and step_data.get("type") != "integrity_check":
                self.add_export_button(step_data["step"])

            # Check and enable dependent steps
            self.update_step_dependencies()

        except Exception as e:
            print(f"Error executing step: {e}")
            import traceback
            traceback.print_exc()
            self.update_step_status(step_data["step"], "error")

    def run_diagnostic_query(self, step_id):
        """Run diagnostic queries to help troubleshoot zero results"""
        try:
            from .pg_connection import execute_query

            print(f"\n🔍 Running diagnostic queries for step {step_id}...")

            if step_id in ["2a", "2b"]:  # BOM-related steps
                # Check if BOM data exists for this project
                bom_check_sql = "SELECT COUNT(*) as bom_count FROM public.bom WHERE project_id = %s;"
                bom_result = execute_query(bom_check_sql, params=(self.project_id,), config=self.db_service.db_config, fetch="one")
                print(f"   📊 BOM records for project {self.project_id}: {bom_result[0] if bom_result else 0}")

                # Check if any BOM data exists at all
                total_bom_sql = "SELECT COUNT(*) as total_bom FROM public.bom;"
                total_result = execute_query(total_bom_sql, config=self.db_service.db_config, fetch="one")
                print(f"   📊 Total BOM records in database: {total_result[0] if total_result else 0}")

                # Check if RFQ input already exists (try different possible table names)
                rfq_tables_to_check = [
                    "public.atem_rfq_input",
                    "public.atem_rfq",
                    "public.rfq_input"
                ]

                rfq_count = 0
                rfq_table_found = None
                for table_name in rfq_tables_to_check:
                    try:
                        rfq_check_sql = f"SELECT COUNT(*) as rfq_count FROM {table_name} WHERE project_id = %s;"
                        rfq_result = execute_query(rfq_check_sql, params=(self.project_id,), config=self.db_service.db_config, fetch="one")
                        if rfq_result:
                            rfq_count = rfq_result[0]
                            rfq_table_found = table_name
                            break
                    except Exception as e:
                        print(f"   ⚠️  Table {table_name} not found or accessible")
                        continue

                if rfq_table_found:
                    print(f"   📊 Existing RFQ input records in {rfq_table_found} for project {self.project_id}: {rfq_count}")
                else:
                    print(f"   ❌ No RFQ input table found - this might be the issue!")
                    print(f"   💡 Expected tables: {', '.join(rfq_tables_to_check)}")

                    # List available tables to help debug
                    try:
                        tables_sql = """
                            SELECT table_name
                            FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name LIKE '%rfq%'
                            ORDER BY table_name;
                        """
                        available_tables = execute_query(tables_sql, config=self.db_service.db_config, fetch="all")
                        if available_tables:
                            print(f"   📋 Available RFQ-related tables:")
                            for table in available_tables:
                                print(f"      - public.{table[0]}")
                        else:
                            print(f"   📋 No RFQ-related tables found in database")
                    except Exception as e:
                        print(f"   ⚠️  Could not list available tables: {e}")

                # Show sample BOM data if it exists
                if bom_result and bom_result[0] > 0:
                    sample_sql = "SELECT item_number, material_description, quantity FROM public.bom WHERE project_id = %s LIMIT 3;"
                    sample_result = execute_query(sample_sql, params=(self.project_id,), config=self.db_service.db_config, fetch="all")
                    print(f"   📋 Sample BOM data:")
                    for row in sample_result:
                        print(f"      - {dict(row)}")
                else:
                    print(f"   ❌ No BOM data found for project {self.project_id}")
                    print(f"   💡 Suggestion: Upload BOM data first using the upload sections above")

        except Exception as e:
            print(f"   ❌ Error running diagnostic queries: {e}")

    def store_validation_results(self, step_id, result, title):
        """Store validation results for export"""
        from datetime import datetime

        if not hasattr(self, 'validation_results'):
            self.validation_results = {}

        # Convert result to a format suitable for export
        if isinstance(result, list) and len(result) > 0:
            # Convert to list of dictionaries for easier export
            export_data = []
            for row in result:
                if hasattr(row, '_asdict'):
                    export_data.append(dict(row))
                elif isinstance(row, dict):
                    export_data.append(row)
                else:
                    # Handle other row types
                    export_data.append({'result': str(row)})

            self.validation_results[step_id] = {
                'title': title,
                'data': export_data,
                'timestamp': str(datetime.now())
            }
            print(f"Stored {len(export_data)} rows for step {step_id}")
        else:
            print(f"No data to store for step {step_id}")

    def add_export_button(self, step_id):
        """Add export button to a validation step"""
        for step_widget in self.workflow_steps:
            if hasattr(step_widget, 'step_data') and step_widget.step_data["step"] == step_id:
                # Find the action layout
                action_layout = None
                for child in step_widget.children():
                    if isinstance(child, QHBoxLayout):
                        # Find the action layout (rightmost layout)
                        layouts = []
                        for i in range(child.count()):
                            item = child.itemAt(i)
                            if isinstance(item.widget(), type(None)) and isinstance(item, QVBoxLayout):
                                layouts.append(item)
                        if layouts:
                            action_layout = layouts[-1]  # Get the rightmost VBoxLayout
                        break

                if action_layout:
                    # Check if export button already exists
                    has_export_btn = False
                    for i in range(action_layout.count()):
                        item = action_layout.itemAt(i)
                        if item and item.widget() and isinstance(item.widget(), QPushButton):
                            if "Export" in item.widget().text():
                                has_export_btn = True
                                break

                    if not has_export_btn:
                        export_btn = QPushButton("📊 Export to Excel")
                        export_btn.setStyleSheet("""
                            QPushButton {
                                background-color: #059669;
                                border: none;
                                border-radius: 8px;
                                padding: 12px 20px;
                                font-size: 14px;
                                font-weight: 600;
                                color: white;
                            }
                            QPushButton:hover {
                                background-color: #047857;
                            }
                        """)
                        export_btn.clicked.connect(lambda: self.export_validation_results(step_id))
                        action_layout.addWidget(export_btn)
                        print(f"Added export button for step {step_id}")
                break

    def export_validation_results(self, step_id):
        """Export validation results to Excel"""
        if not hasattr(self, 'validation_results') or step_id not in self.validation_results:
            print(f"No results to export for step {step_id}")
            return

        try:
            import pandas as pd
            from datetime import datetime

            # Get the stored results
            results_data = self.validation_results[step_id]

            # Create DataFrame
            df = pd.DataFrame(results_data['data'])

            # Convert specific columns to numeric for proper Excel functionality
            if step_id == "4a":  # Validate BOM vs RFQ View
                numeric_columns = ['bom_value', 'view_value', 'difference']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        print(f"   📊 Converted column '{col}' to numeric for Excel sum functions")

            elif step_id == "4b":  # Generate General Aggregation
                # Convert all numeric columns (typically all columns except maybe identifiers)
                for col in df.columns:
                    # Skip non-numeric columns (usually text/string identifiers)
                    if not col.lower() in ['project_id', 'project_name', 'description', 'notes']:
                        try:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                            print(f"   📊 Converted column '{col}' to numeric for Excel sum functions")
                        except:
                            pass  # Skip columns that can't be converted

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = "".join(c for c in results_data['title'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_title}_{timestamp}.xlsx"

            # Get save location from user
            from PySide6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"Export {results_data['title']}",
                filename,
                "Excel Files (*.xlsx)"
            )

            if file_path:
                # Export to Excel
                df.to_excel(file_path, index=False, sheet_name=safe_title[:31])  # Excel sheet name limit
                print(f"Exported {len(df)} rows to {file_path}")

                # Show success message
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Successfully exported {len(df)} rows to:\n{file_path}"
                )

        except ImportError:
            print("pandas is required for Excel export. Please install pandas.")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "Missing Dependency",
                "pandas is required for Excel export.\nPlease install pandas: pip install pandas"
            )
        except Exception as e:
            print(f"Error exporting results: {e}")
            import traceback
            traceback.print_exc()
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "Export Error",
                f"Error exporting results:\n{str(e)}"
            )

    def ensure_workflow_functions_exist(self):
        """Ensure all required PostgreSQL functions exist in the database"""
        try:
            from .pg_connection import execute_query

            # Check if the main workflow functions exist
            functions_to_check = [
                'generate_rfq_input_from_bom',
                'update_rfq_input_categories',
                'update_bom_from_rfq_input',
                'validate_bom_vs_view',
                'manage_bom_to_general_aggregation',
                'update_bom_sizes_for_projects'
            ]

            for func_name in functions_to_check:
                check_sql = """
                    SELECT EXISTS (
                        SELECT 1
                        FROM pg_proc p
                        JOIN pg_namespace n ON p.pronamespace = n.oid
                        WHERE n.nspname = 'public' AND p.proname = %s
                    );
                """

                result = execute_query(check_sql, params=(func_name,), config=self.db_service.db_config, fetch="one")

                if not result[0]:
                    print(f"Warning: Function {func_name} does not exist in the database")
                    print("Please ensure the workflow functions are installed from project_workflow/new_functions.sql")
                else:
                    print(f"✓ Function {func_name} exists")

        except Exception as e:
            print(f"Error checking workflow functions: {e}")

    def update_step_status(self, step_id, status):
        """Update the status of a workflow step"""
        for step_widget in self.workflow_steps:
            if hasattr(step_widget, 'step_data') and step_widget.step_data["step"] == step_id:
                if hasattr(step_widget, 'status_widget'):
                    step_widget.status_widget.status = status
                    if status == "complete":
                        step_widget.status_widget.text = "Complete"
                    elif status == "error":
                        step_widget.status_widget.text = "Error"
                    elif status == "running":
                        step_widget.status_widget.text = "Running"
                    step_widget.status_widget.update()
                break

    def update_step_dependencies(self):
        """Update step button states based on dependencies"""
        # Track completed steps
        completed_steps = set()
        upload_status = self.check_upload_status()

        # Check which steps are completed
        for step_widget in self.workflow_steps:
            if hasattr(step_widget, 'status_widget'):
                if step_widget.status_widget.status == "complete":
                    completed_steps.add(step_widget.step_data["step"])

        # Add upload status to completed if files are uploaded
        if upload_status.get("bom_uploaded", False):
            completed_steps.add("bom_uploaded")
        if upload_status.get("verified_material_uploaded", False):
            completed_steps.add("verified_material_uploaded")

        # Simply enable all buttons when project is selected - no dependency checking
        for step_widget in self.workflow_steps:
            if hasattr(step_widget, 'run_button'):
                # Always enable if project is selected
                should_enable = self.project_id is not None
                step_widget.run_button.setEnabled(should_enable)

    def check_upload_status(self):
        """Check which files have been uploaded"""
        status = {
            "bom_uploaded": False,
            "verified_material_uploaded": False,
            "general_uploaded": False
        }

        # Check upload status by looking at status indicators in import sections
        if hasattr(self, 'import_sections'):
            for section_widget in self.import_sections:
                if hasattr(section_widget, 'table_name') and hasattr(section_widget, 'status_widget'):
                    if section_widget.status_widget.status == "complete":
                        table_name = section_widget.table_name
                        if table_name == "public.bom":
                            status["bom_uploaded"] = True
                        elif table_name == "public.verified_material_classifications":
                            status["verified_material_uploaded"] = True
                        elif table_name == "public.general":
                            status["general_uploaded"] = True

        return status

    def browse_file(self, file_edit):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File", "", "Data Files (*.csv *.xlsx *.xls)"
        )
        if file_path:
            file_edit.setText(file_path)
            # Automatically validate the file when selected
            self.validate_file(file_edit, file_path)

    def validate_file(self, file_edit, file_path):
        """Validate a selected file and update the UI accordingly"""
        try:
            print(f"Validating file: {file_path}")
            print(f"Current project_id: {self.project_id}")

            # Find the parent section widget to update status and count
            section_widget = file_edit.parent()
            while section_widget and not hasattr(section_widget, 'table_name'):
                section_widget = section_widget.parent()

            if not section_widget:
                print("Could not find section widget with table_name")
                return

            # Get the table name from the section
            table_name = getattr(section_widget, 'table_name', 'public.general')
            print(f"Table name: {table_name}")

            # Validate the file using the database service
            result = self.db_service.validate_data_file(file_path, table_name)
            print(f"Validation result: {result}")

            # Find the content layout to update count and upload button
            content_widget = section_widget.content_widget
            content_layout = content_widget.layout()

            # Update the row count
            if hasattr(content_layout, 'count_label'):
                content_layout.count_label.setText(str(result.get('total_rows', 0)))
                print(f"Updated row count to: {result.get('total_rows', 0)}")

            # Update the status indicator in the header
            status_widget = section_widget.status_widget
            if result['status'] == 'success':
                status_widget.status = 'complete'
                status_widget.text = 'File Ready'
            else:
                status_widget.status = 'error'
                status_widget.text = 'File Error'
            status_widget.update()
            print(f"Updated status to: {status_widget.status}")

            # Enable/disable the upload button
            if hasattr(content_layout, 'upload_btn'):
                upload_btn = content_layout.upload_btn
                should_enable = result['status'] == 'success' and self.project_id is not None
                upload_btn.setEnabled(should_enable)
                print(f"Upload button enabled: {should_enable} (validation={result['status']}, project={self.project_id is not None})")

                if result['status'] == 'success' and self.project_id is not None:
                    try:
                        upload_btn.clicked.disconnect()  # Remove old connections
                    except:
                        pass  # No connections to disconnect
                    upload_btn.clicked.connect(lambda fp=file_path, tn=table_name: self.upload_file(fp, tn))
                    print("Connected upload handler")

        except Exception as e:
            print(f"Error validating file: {e}")
            import traceback
            traceback.print_exc()

    def upload_file(self, file_path, table_name):
        """Upload a file to the database"""
        section_widget = None

        try:
            # Find the section widget for this table
            for section in self.import_sections:
                if hasattr(section, 'table_name') and section.table_name == table_name:
                    section_widget = section
                    break

            if not section_widget:
                print(f"Could not find section widget for table: {table_name}")
                return

            # Update status to running
            status_widget = section_widget.status_widget
            status_widget.status = 'running'
            status_widget.text = 'Uploading...'
            status_widget.update()

            # Perform the import
            result = self.db_service.import_data_file(
                file_path=file_path,
                table_name=table_name,
                client_id=self.client_id,
                profile_id=self.profile_id,
                project_id=self.project_id
            )

            # Update status based on result
            if result['status'] == 'success':
                status_widget.status = 'complete'
                status_widget.text = 'Upload Complete'

                # Find and disable the upload button
                content_layout = section_widget.content_widget.layout()
                if hasattr(content_layout, 'upload_btn'):
                    content_layout.upload_btn.setEnabled(False)
                    content_layout.upload_btn.setText("✅ Uploaded Successfully")
            else:
                status_widget.status = 'error'
                status_widget.text = 'Upload Failed'

            status_widget.update()

            # Show result message
            if result['status'] == 'success':
                print(f"Upload successful: {result['inserted']} rows inserted")
                # Update workflow dependencies when upload is successful
                self.update_step_dependencies()
                # Emit signal to notify that data was uploaded
                self.data_uploaded.emit()
            else:
                print(f"Upload failed: {result['message']}")

        except Exception as e:
            print(f"Error uploading file: {e}")
            import traceback
            traceback.print_exc()

            # Update status to error
            if section_widget:
                status_widget = section_widget.status_widget
                status_widget.status = 'error'
                status_widget.text = 'Upload Error'
                status_widget.update()

    def setup_summary(self):
        self.summary_widget = QWidget()
        self.summary_widget.setStyleSheet("""
            QWidget {
                background-color: #f9fafb;
                border-radius: 8px;
                padding: 16px;
            }
        """)

        layout = QHBoxLayout(self.summary_widget)

        summary_label = QLabel("Import Summary")
        summary_label.setStyleSheet("font-weight: bold;")

        details_label = QLabel("Total files selected: 0 | Total rows: 0")
        details_label.setStyleSheet("color: #6b7280; font-size: 12px;")

        left_layout = QVBoxLayout()
        left_layout.addWidget(summary_label)
        left_layout.addWidget(details_label)

        layout.addLayout(left_layout)
        layout.addStretch()

        # Status legend
        legend_layout = QHBoxLayout()
        statuses = [("pending", "Pending"), ("running", "Processing"), ("complete", "Complete"), ("error", "Error")]

        for status, text in statuses:
            status_widget = StatusIndicator(status, text)
            legend_layout.addWidget(status_widget)

        layout.addLayout(legend_layout)

    def set_project(self, project_data):
        """Set the current project for data import"""
        self.project_id = project_data.get('id')
        self.client_id = project_data.get('client_id')
        self.profile_id = project_data.get('profile_id')

        # Update any UI elements that show project info
        print(f"DataImportTab: Set project {self.project_id} (Client: {self.client_id}, Profile: {self.profile_id})")

        # Refresh all upload buttons now that we have a project
        self.refresh_upload_buttons()

        # Update workflow step dependencies
        if hasattr(self, 'workflow_steps'):
            self.update_step_dependencies()

    def refresh_upload_buttons(self):
        """Refresh the state of all upload buttons based on current project and file status"""
        # Find all import section widgets
        if hasattr(self, 'import_sections') and self.import_sections:
            for section_widget in self.import_sections:
                if hasattr(section_widget, 'table_name') and hasattr(section_widget, 'content_widget'):
                    # Find the upload button in this section
                    content_layout = section_widget.content_widget.layout()
                    if hasattr(content_layout, 'upload_btn') and hasattr(content_layout, 'file_edit'):
                        upload_btn = content_layout.upload_btn
                        file_edit = content_layout.file_edit

                        # Check if this section has a valid file
                        has_file = file_edit.text() and file_edit.text() != "No file selected - Click Browse to select a file"

                        # Enable button if we have both project and file
                        should_enable = bool(self.project_id is not None and has_file)
                        upload_btn.setEnabled(should_enable)
                        print(f"Button for {section_widget.table_name}: Project={self.project_id is not None}, File={has_file}, Enabled={should_enable}")
        else:
            print("Import sections not yet initialized")

    def clear_all_inputs(self):
        """Clear all file inputs and reset status indicators"""
        # Clear all file inputs and reset status
        if hasattr(self, 'import_sections') and self.import_sections:
            for section_widget in self.import_sections:
                if hasattr(section_widget, 'content_widget'):
                    content_layout = section_widget.content_widget.layout()

                    # Find and clear file input
                    for i in range(content_layout.count()):
                        item = content_layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            # Look for file edit widgets
                            if hasattr(widget, 'layout'):
                                for j in range(widget.layout().count()):
                                    sub_item = widget.layout().itemAt(j)
                                    if sub_item and sub_item.widget():
                                        sub_widget = sub_item.widget()
                                        if isinstance(sub_widget, QLineEdit) and hasattr(sub_widget, 'setPlaceholderText'):
                                            # This is likely a file input
                                            sub_widget.clear()
                                            sub_widget.setText("No file selected - Click Browse to select a file")

                    # Reset status indicator
                    if hasattr(section_widget, 'status_widget'):
                        section_widget.status_widget.status = 'pending'
                        section_widget.status_widget.text = 'Pending'
                        section_widget.status_widget.update()

                    # Reset row count
                    if hasattr(section_widget, 'count_label'):
                        section_widget.count_label.setText("0 rows")

        # Refresh upload buttons to disabled state
        self.refresh_upload_buttons()

    def reset_workflow_steps(self):
        """Reset all workflow step statuses without clearing project or file inputs"""
        if hasattr(self, 'workflow_steps') and self.workflow_steps:
            for step_widget in self.workflow_steps:
                if hasattr(step_widget, 'status_widget'):
                    # Reset status to pending/optional based on step type
                    if step_widget.step_data.get("optional", False):
                        step_widget.status_widget.status = 'conditional'
                        step_widget.status_widget.text = 'Optional'
                    elif step_widget.step_data.get("type") == "integrity_check":
                        step_widget.status_widget.status = 'pending'
                        step_widget.status_widget.text = 'Ready'
                    else:
                        step_widget.status_widget.status = 'pending'
                        step_widget.status_widget.text = 'Pending'
                    step_widget.status_widget.update()

                # Reset upload button text if it was changed
                if hasattr(step_widget, 'content_widget'):
                    content_layout = step_widget.content_widget.layout()
                    if hasattr(content_layout, 'upload_btn'):
                        upload_btn = content_layout.upload_btn
                        if upload_btn.text() == "✅ Uploaded Successfully":
                            upload_btn.setText("⬆️ Upload to Database")
                            upload_btn.setEnabled(True)  # Re-enable if file is present

        # Remove any export buttons that were added
        self.remove_export_buttons()

        print("Workflow steps have been reset to initial state")

    def reset_upload_sections(self):
        """Reset file paths and status messages in Data Upload sections without clearing project"""
        if hasattr(self, 'import_sections') and self.import_sections:
            for section_widget in self.import_sections:
                if hasattr(section_widget, 'content_widget'):
                    content_layout = section_widget.content_widget.layout()

                    # Find and clear file input
                    for i in range(content_layout.count()):
                        item = content_layout.itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            # Look for file edit widgets
                            if hasattr(widget, 'layout'):
                                for j in range(widget.layout().count()):
                                    sub_item = widget.layout().itemAt(j)
                                    if sub_item and sub_item.widget():
                                        sub_widget = sub_item.widget()
                                        if isinstance(sub_widget, QLineEdit) and hasattr(sub_widget, 'setPlaceholderText'):
                                            # This is likely a file input
                                            sub_widget.clear()
                                            sub_widget.setText("No file selected - Click Browse to select a file")

                    # Reset upload button text and state
                    if hasattr(content_layout, 'upload_btn'):
                        upload_btn = content_layout.upload_btn
                        upload_btn.setText("⬆️ Upload to Database")
                        upload_btn.setEnabled(False)  # Disable until file is selected again

                    # Reset row count
                    if hasattr(content_layout, 'count_label'):
                        content_layout.count_label.setText("0")

                # Reset status indicator in header
                if hasattr(section_widget, 'status_widget'):
                    section_widget.status_widget.status = 'pending'
                    section_widget.status_widget.text = 'No File'
                    section_widget.status_widget.update()

        print("Data upload sections have been reset (file paths and status cleared)")

    def remove_export_buttons(self):
        """Remove any export buttons that were dynamically added to workflow steps"""
        if hasattr(self, 'workflow_steps') and self.workflow_steps:
            for step_widget in self.workflow_steps:
                if hasattr(step_widget, 'step_data'):
                    # Look for export buttons in validation steps
                    if step_widget.step_data["step"] in ["4a", "4b"]:
                        # Find and remove export buttons
                        for child in step_widget.findChildren(QPushButton):
                            if "Export" in child.text():
                                child.setParent(None)
                                child.deleteLater()


class DataQueryTab(QWidget):
    """Data query tab with SQL command pipeline"""

    def __init__(self):
        super().__init__()
        self.project_id = None
        self.setup_ui()

    def setup_ui(self):
        # Create main scroll area for the entire page
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        main_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        main_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)

        # Create the main content widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)

        # Title section
        title_section = QWidget()
        title_layout = QVBoxLayout(title_section)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)

        title = QLabel("Data Processing Pipeline")
        title.setStyleSheet("""
            font-size: 22px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        """)

        subtitle = QLabel("Execute data processing commands in sequence for the active project")
        subtitle.setStyleSheet("""
            color: #64748b;
            font-size: 14px;
            font-weight: 400;
        """)

        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        layout.addWidget(title_section)

        # Pipeline controls (reduced size)
        self.setup_pipeline_controls()
        layout.addWidget(self.pipeline_controls)

        # Commands (no scroll area, just direct layout)
        self.commands_layout = QVBoxLayout()
        self.setup_commands()
        layout.addLayout(self.commands_layout)

        # Progress summary
        self.setup_progress_summary()
        layout.addWidget(self.progress_widget)

        # Output terminal (ensure it takes at least 25% of height)
        self.setup_output_terminal()
        layout.addWidget(self.output_widget)

        # Set the content widget in the scroll area
        main_scroll.setWidget(content_widget)

        # Set the scroll area as the main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_scroll)

    def setup_pipeline_controls(self):
        self.pipeline_controls = QWidget()
        self.pipeline_controls.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                border: 1px solid #cbd5e1;
                border-radius: 10px;
                padding: 12px;
            }
        """)
        self.pipeline_controls.setFixedHeight(60)  # Reduced height

        layout = QHBoxLayout(self.pipeline_controls)
        layout.setSpacing(16)

        # Compact status info
        self.status_detail = QLabel("Ready to execute commands for project: No Project Selected")
        self.status_detail.setStyleSheet("""
            color: #475569;
            font-size: 13px;
            font-weight: 500;
        """)

        layout.addWidget(self.status_detail)
        layout.addStretch()

        # Compact control buttons
        reset_btn = QPushButton("Reset All")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f1f5f9;
                color: #475569;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: 500;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e2e8f0;
            }
        """)
        reset_btn.setFixedHeight(32)

        run_all_btn = QPushButton("Run All Commands")
        run_all_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 16px;
                font-weight: 600;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
            }
        """)
        run_all_btn.setFixedHeight(32)

        layout.addWidget(reset_btn)
        layout.addWidget(run_all_btn)

    def setup_commands(self):
        commands = [
            {
                "name": "Generate RFQ Input from BOM",
                "description": "Create RFQ input records from uploaded BOM data",
                "sql": "SELECT generate_rfq_input_from_bom(ARRAY[{project_id}]);",
                "tooltip": "Generates RFQ input records from the uploaded BOM data. This creates the foundation for material requests and ensures all BOM items are properly categorized for RFQ processing.",
                "conditional": False
            },
            {
                "name": "Update RFQ Input Categories",
                "description": "Categorize RFQ input records using verified material classifications",
                "sql": "SELECT update_rfq_input_categories(ARRAY[{project_id}]);",
                "tooltip": "Updates and categorizes RFQ input records based on verified material classifications. This ensures proper material categorization for accurate pricing and procurement.",
                "conditional": False
            },
            {
                "name": "Update BOM Sizes",
                "description": "Update size1 and size2 fields if not present in BOM data",
                "sql": "SELECT * FROM update_bom_sizes_for_projects(ARRAY[{project_id}]);",
                "tooltip": "Updates BOM size fields (size1, size2) if they are not present in the uploaded data. This step is only required when size information is missing from the original BOM.",
                "conditional": True
            },
            {
                "name": "Update BOM from RFQ Input",
                "description": "Synchronize BOM table with processed RFQ input data",
                "sql": "SELECT * FROM update_bom_from_rfq_input(ARRAY[{project_id}]);",
                "tooltip": "Updates the BOM table with processed data from RFQ input. This synchronizes the BOM with categorized and validated RFQ data, ensuring consistency between systems.",
                "conditional": False
            },
            {
                "name": "Validate BOM vs RFQ View",
                "description": "Validate data consistency between BOM and RFQ view",
                "sql": "SELECT * FROM validate_bom_vs_view({project_id});",
                "tooltip": "Validates consistency between BOM data and RFQ view. This quality check ensures data integrity and identifies any discrepancies between the two data sources.",
                "conditional": False
            },
            {
                "name": "Generate General Aggregation",
                "description": "Calculate comprehensive totals for all BOM components and areas",
                "sql": "SELECT ... FROM manage_bom_to_general_aggregation({project_id}, FALSE);",
                "tooltip": "Generates comprehensive aggregation of all BOM components including lengths, fittings, flanges, valves, and calculated areas. This produces the final summary totals for the project.",
                "conditional": False
            }
        ]

        for i, cmd in enumerate(commands):
            command_widget = self.create_command_widget(cmd, i == 0)
            self.commands_layout.addWidget(command_widget)

        # Add stretch to push everything up
        self.commands_layout.addStretch()

    def create_command_widget(self, command, enabled=False):
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 8px;
            }
            QWidget:hover {
                background-color: #f9fafb;
            }
        """)

        layout = QHBoxLayout(widget)

        # Status indicator
        status_color = "conditional" if command["conditional"] else "pending"
        status_widget = StatusIndicator(status_color, "Conditional" if command["conditional"] else "Pending")

        # Command info
        info_layout = QVBoxLayout()

        name_layout = QHBoxLayout()
        name_label = QLabel(command["name"])
        name_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        name_layout.addWidget(name_label)

        if command["conditional"]:
            conditional_badge = QLabel("Conditional")
            conditional_badge.setStyleSheet("""
                QLabel {
                    background-color: #fbbf24;
                    color: white;
                    border-radius: 4px;
                    padding: 2px 6px;
                    font-size: 10px;
                    font-weight: bold;
                }
            """)
            name_layout.addWidget(conditional_badge)

        name_layout.addStretch()

        desc_label = QLabel(command["description"])
        desc_label.setStyleSheet("color: #6b7280; font-size: 12px;")

        info_layout.addLayout(name_layout)
        info_layout.addWidget(desc_label)

        # Action button
        run_btn = QPushButton("Run")
        run_btn.setEnabled(enabled)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:enabled:hover {
                background-color: #f9fafb;
            }
            QPushButton:disabled {
                color: #9ca3af;
            }
        """)

        # Set tooltip
        widget.setToolTip(f"SQL Function: {command['sql']}\n\n{command['tooltip']}")

        layout.addWidget(status_widget)
        layout.addLayout(info_layout, 1)
        layout.addWidget(run_btn)

        return widget

    def setup_progress_summary(self):
        self.progress_widget = QWidget()
        self.progress_widget.setStyleSheet("""
            QWidget {
                background-color: #dbeafe;
                border: 1px solid #bfdbfe;
                border-radius: 8px;
                padding: 16px;
            }
        """)

        layout = QVBoxLayout(self.progress_widget)

        # Header
        header_layout = QHBoxLayout()

        progress_label = QLabel("Pipeline Progress")
        progress_label.setStyleSheet("font-weight: bold; color: #1e40af;")

        count_label = QLabel("0 of 6 commands completed")
        count_label.setStyleSheet("color: #2563eb; font-size: 12px;")

        left_layout = QVBoxLayout()
        left_layout.addWidget(progress_label)
        left_layout.addWidget(count_label)

        header_layout.addLayout(left_layout)
        header_layout.addStretch()

        # Status legend
        legend_layout = QHBoxLayout()
        statuses = [
            ("pending", "Pending"), ("running", "Running"), ("complete", "Complete"),
            ("error", "Error"), ("conditional", "Conditional")
        ]

        for status, text in statuses:
            status_widget = StatusIndicator(status, text)
            legend_layout.addWidget(status_widget)

        header_layout.addLayout(legend_layout)
        layout.addLayout(header_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #e5e7eb;
                border-radius: 4px;
                height: 8px;
            }
            QProgressBar::chunk {
                background-color: #2563eb;
                border-radius: 4px;
            }
        """)
        self.progress_bar.setValue(0)

        layout.addWidget(self.progress_bar)

    def setup_output_terminal(self):
        self.output_widget = QWidget()
        output_layout = QVBoxLayout(self.output_widget)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(12)

        # Header section
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(4)

        output_title = QLabel("Command Output")
        output_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
        """)

        output_subtitle = QLabel("Real-time output and logs from executed SQL commands")
        output_subtitle.setStyleSheet("""
            color: #64748b;
            font-size: 13px;
            font-weight: 400;
        """)

        header_layout.addWidget(output_title)
        header_layout.addWidget(output_subtitle)

        # Terminal with proper sizing (at least 25% of height)
        self.output_text = QTextEdit()
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #0f172a;
                color: #22c55e;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                border: 1px solid #334155;
                border-radius: 10px;
                padding: 16px;
                line-height: 1.4;
            }
            QScrollBar:vertical {
                background-color: #1e293b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #475569;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #64748b;
            }
        """)
        # Set minimum height to ensure at least 25% of typical window height
        self.output_text.setMinimumHeight(250)
        self.output_text.setPlainText(
            "# Ready to execute SQL commands...\n"
            "# Project: No Project Selected\n"
            "# Waiting for command execution...\n"
            "# \n"
            "# This terminal will display real-time output from SQL commands\n"
            "# as they are executed in the pipeline.\n"
        )

        output_layout.addWidget(header_widget)
        output_layout.addWidget(self.output_text, 1)  # Give it stretch factor

    def set_project(self, project_id, project_name):
        self.project_id = project_id
        self.status_detail.setText(f"Ready to execute commands for project: {project_name} (ID: {project_id})")

        # Update output terminal
        self.output_text.setPlainText(
            f"# Ready to execute SQL commands...\n"
            f"# Project: {project_name}\n"
            f"# Project ID: {project_id}\n"
            f"# Waiting for command execution...\n"
            f"# \n"
            f"# Expected SQL Commands:\n"
            f"# SELECT generate_rfq_input_from_bom(ARRAY[{project_id}]);\n"
            f"# SELECT update_rfq_input_categories(ARRAY[{project_id}]);\n"
            f"# SELECT * FROM update_bom_sizes_for_projects(ARRAY[{project_id}]); -- if needed\n"
            f"# SELECT * FROM update_bom_from_rfq_input(ARRAY[{project_id}]);\n"
            f"# SELECT * FROM validate_bom_vs_view({project_id});\n"
            f"# SELECT ... FROM manage_bom_to_general_aggregation({project_id}, FALSE);\n"
        )

    def clear_all_inputs(self):
        """Clear the output terminal and reset status"""
        if hasattr(self, 'output_text'):
            self.output_text.setPlainText(
                "# Ready to execute SQL commands...\n"
                "# Project: No Project Selected\n"
                "# Waiting for command execution...\n"
                "# \n"
                "# This terminal will display real-time output from SQL commands\n"
                "# as they are executed in the pipeline.\n"
            )

        # Reset status detail if it exists
        if hasattr(self, 'status_detail'):
            self.status_detail.setText("No project selected. Please select a project to begin.")

    def reset_workflow_steps(self):
        """Reset workflow steps without clearing project - for DataQueryTab this just clears output"""
        if hasattr(self, 'output_text'):
            current_project_text = ""
            if self.project_id:
                # Keep project info but clear execution history
                current_project_text = f"# Ready to execute SQL commands...\n# Project ID: {self.project_id}\n# Workflow steps have been reset\n# \n"
            else:
                current_project_text = "# Ready to execute SQL commands...\n# No Project Selected\n# \n"

            self.output_text.setPlainText(
                current_project_text +
                "# This terminal will display real-time output from SQL commands\n"
                "# as they are executed in the pipeline.\n"
            )

    def reset_upload_sections(self):
        """Reset upload sections - DataQueryTab doesn't have upload sections"""
        # This tab doesn't have upload sections, but we include this method for consistency
        pass


class AISInterfaceTab(QWidget):
    """Main AIS Interface Tab that can be imported into other projects"""

    def __init__(self):
        super().__init__()
        self.current_project = None
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # Set overall background
        self.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                font-family: 'Segoe UI', system-ui, sans-serif;
            }
        """)

        # Header with title and project info
        header_layout = QVBoxLayout()
        header_layout.setSpacing(16)

        # Title bar
        title_layout = QHBoxLayout()
        title_label = QLabel("AIS PostgreSQL Interface")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 4px;
        """)

        subtitle_label = QLabel("Modern data management and processing pipeline")
        subtitle_label.setStyleSheet("""
            font-size: 14px;
            color: #64748b;
            font-weight: 400;
        """)

        title_section = QWidget()
        title_section_layout = QVBoxLayout(title_section)
        title_section_layout.setContentsMargins(0, 0, 0, 0)
        title_section_layout.setSpacing(4)
        title_section_layout.addWidget(title_label)
        title_section_layout.addWidget(subtitle_label)

        title_layout.addWidget(title_section)
        title_layout.addStretch()

        header_layout.addLayout(title_layout)

        # Project header (initially hidden)
        self.project_header = ProjectHeaderWidget()
        self.project_header.change_btn.clicked.connect(self.change_project)
        self.project_header.clear_btn.clicked.connect(self.clear_all_inputs)
        self.project_header.reset_steps_btn.clicked.connect(self.reset_workflow_steps)
        self.project_header.reset_uploads_btn.clicked.connect(self.reset_upload_sections)
        header_layout.addWidget(self.project_header)

        layout.addLayout(header_layout)

        # Tab widget with modern styling
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                background-color: white;
                padding: 0px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #e2e8f0;
                border-bottom: none;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
                font-size: 13px;
                color: #475569;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #3b82f6;
                color: #1e293b;
                font-weight: 600;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f1f5f9, stop:1 #e2e8f0);
                color: #334155;
            }
        """)

        # Create tabs
        self.project_tab = ProjectSelectionTab()
        self.import_tab = DataImportTab()
        self.query_tab = DataQueryTab()

        # Add tabs
        self.tabs.addTab(self.project_tab, "Project Selection")
        self.tabs.addTab(self.import_tab, "Data Import")
        self.tabs.addTab(self.query_tab, "Data Query")

        # Initially disable import and query tabs
        self.tabs.setTabEnabled(1, False)
        self.tabs.setTabEnabled(2, False)

        layout.addWidget(self.tabs)

        # Connect signals
        self.project_tab.project_selected.connect(self.on_project_selected)
        self.import_tab.data_uploaded.connect(self.on_data_uploaded)

    def on_project_selected(self, project_data):
        """Handle project selection"""
        # Clear all inputs first to start fresh
        self.clear_all_inputs()

        self.current_project = project_data

        # Update project header (this will also update row counts)
        self.project_header.set_project(
            project_data['name'],
            project_data['client'],
            project_data['profile'],
            project_data['status'],
            project_data['id']  # Pass the project ID
        )

        # Enable other tabs
        self.tabs.setTabEnabled(1, True)
        self.tabs.setTabEnabled(2, True)

        # Set project in other tabs
        self.import_tab.set_project(project_data)
        self.query_tab.set_project(project_data['id'], project_data['name'])

        # Switch to import tab
        self.tabs.setCurrentIndex(1)

    def on_data_uploaded(self):
        """Handle data upload completion - update row counts in project header"""
        if hasattr(self, 'project_header'):
            self.project_header.update_row_counts()

    def clear_all_inputs(self):
        """Clear all inputs and messages across all tabs"""
        # Clear Data Import tab
        if hasattr(self, 'import_tab'):
            self.import_tab.clear_all_inputs()

        # Clear Data Query tab
        if hasattr(self, 'query_tab'):
            self.query_tab.clear_all_inputs()

        # Clear Project Selection tab
        if hasattr(self, 'project_tab'):
            self.project_tab.clear_all_inputs()

    def reset_workflow_steps(self):
        """Reset workflow steps without clearing project or file inputs"""
        # Reset workflow steps in Data Import tab (where the main workflow is)
        if hasattr(self, 'import_tab'):
            self.import_tab.reset_workflow_steps()

        # Reset output in Data Query tab
        if hasattr(self, 'query_tab'):
            self.query_tab.reset_workflow_steps()

        # Reset in Project Selection tab (no-op but for consistency)
        if hasattr(self, 'project_tab'):
            self.project_tab.reset_workflow_steps()

        print("All workflow steps have been reset while maintaining current project and file selections")

    def reset_upload_sections(self):
        """Reset file paths and upload status without clearing project"""
        # Reset upload sections in Data Import tab (where the upload sections are)
        if hasattr(self, 'import_tab'):
            self.import_tab.reset_upload_sections()

        print("Upload sections have been reset while maintaining current project")

    def change_project(self):
        """Handle change project button"""
        # Clear all inputs first
        self.clear_all_inputs()

        # Reset project
        self.current_project = None
        self.project_header.hide()

        # Disable tabs
        self.tabs.setTabEnabled(1, False)
        self.tabs.setTabEnabled(2, False)

        # Switch back to project tab
        self.tabs.setCurrentIndex(0)


# Example usage and test application
if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Create and show the interface
    interface = AISInterfaceTab()
    interface.setWindowTitle("AIS PostgreSQL Interface")
    interface.resize(1200, 800)
    interface.show()

    sys.exit(app.exec())
