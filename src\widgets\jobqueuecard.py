
from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QWidget, QLabel, QSizePolicy, QPushButton)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QMovie
from src.pyside_util import resource_path, get_resource_pixmap
from pubsub import pub


class JobQueueCard(QWidget):

    def __init__(self, parent, data={}):
        super().__init__(parent)
        self.setLayout(QHBoxLayout())

        self.uuid = data["uuid"]
        name = data.get("name")

        self.hbox = QWidget()
        self.hbox.setLayout(QHBoxLayout())
        self.hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addWidget(self.hbox)

        self.jobIcon = QLabel("")
        if "mto" in name.lower():
            icon =  "ph--open-ai-logo-duotone.svg"
        elif "roi" in name.lower():
            icon = "layers.svg"
        elif "build rfq" in name.lower():
            icon = "build-rfq.svg"
        elif "apply" in name.lower():
            icon = "tableview-edit.svg"
        else:
            icon = "tool.svg"
        pixmap = get_resource_pixmap(icon)
        pixmap = pixmap.scaledToWidth(32)
        self.jobIcon.setPixmap(pixmap)
        self.hbox.layout().addWidget(self.jobIcon)

        self.vbox = QWidget()
        self.vbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        self.vbox.setLayout(QVBoxLayout())
        self.hbox.layout().addWidget(self.vbox)
        self.lblName = QLabel(name)
        self.vbox.layout().addWidget(self.lblName)

        self.lblName = QLabel("Time Elapsed: n/a")
        self.vbox.layout().addWidget(self.lblName)

        self.lblEta = QLabel("ETA: n/a")
        self.vbox.layout().addWidget(self.lblEta)

        self.pbCancel = QPushButton("Cancel")
        self.pbCancel.setMinimumHeight(24)
        self.pbCancel.setContentsMargins(0,2,4,0)
        self.pbCancel.clicked.connect(self.onCancel)
        if "mto" in name.lower() or "preprocessing" in name.lower(): # TODO - only supported for specific jobs
            self.hbox.layout().addWidget(self.pbCancel)
        self.hbox.layout().setAlignment(self.pbCancel, Qt.AlignmentFlag.AlignTop)

        self.lblRunning = QLabel("Running")
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        self.movie.setScaledSize(QSize(32, 32))
        self.lblRunning.setMovie(self.movie)
        self.lblRunning.setFixedSize(48, 48)
        self.lblRunning.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hbox.layout().addWidget(self.lblRunning)

        self.lblIcon = QLabel()
        self.lblIcon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lblIcon.setFixedSize(48, 48)
        self.hbox.layout().addWidget(self.lblIcon)
        self.lblIcon.hide()

        self.setMinimumWidth(256)
        self.lblIcon.setObjectName("jobQueueCard")
        self.lblRunning.setObjectName("jobQueueCard")
        self.hbox.setObjectName("jobQueueCard")
        self.style().unpolish(self)
        self.style().polish(self)
        self.movie.start()

    def updateObjectName(self, name):
        if not name:
            return
        # objects = [self.hbox, self.lblIcon, self.lblRunning, self.vbox]
        objects = [self.hbox, self.lblRunning, self.lblIcon]
        for obj in objects:
            obj.setObjectName(name)
            self.style().unpolish(obj)
            self.style().polish(obj)

    def setIconState(self, state):
        states = {
            "ok": "check-circle.svg",
            "error": "x-circle-red.svg",
            "warning": "alert-circle.svg",
        }
        icon = states.get(state, "check-circle.svg")
        pixmap = get_resource_pixmap(icon)
        pixmap = pixmap.scaledToWidth(32)
        pixmap = get_resource_pixmap(icon)
        self.lblIcon.setPixmap(pixmap)
        if state == "error":
            name = "jobQueueCardError"
        elif state == "ok":
            name = "jobQueueCardOk"
        else:
            name = None
        self.updateObjectName(name)

    def updateData(self, data: dict):
        v = data.get('time_elapsed')
        if v is not None:
            v = f"{v}s"
        else:
            v = "n/a"
        time_elapsed = f"Time Elapsed: {v}"
        self.lblName.setText(time_elapsed)
        eta = data.get('eta', 'N/A')
        self.lblEta.setText(f"ETA: {eta}{'s' if eta else ''}")
        if data["finished"]:
            self.pbCancel.hide()
            self.lblRunning.hide()
            self.lblEta.hide()
            self.movie.stop()
            self.lblIcon.show()
            self.setIconState(data.get("status")) # Update icon to reflect job success/failure

    def onCancel(self):
        """Request Job Cancellation"""
        pub.sendMessage("job-cancel", uuid=self.uuid)


class JobQueueCardExtended(JobQueueCard):

    def __init__(self, parent, data={}):
        super().__init__(parent, data)
        self.lblState = QLabel()
        self.vbox.layout().addWidget(self.lblState)

    def updateData(self, data: dict):
        super().updateData(data)
        error = data.get("error")
        if error is not None:
            if error == "Canceled":
                self.lblState.setText(f"Canceled")
            else:
                self.lblState.setText(f"Error: {error}")

    def updateStatus(self, status):
        self.lblState.setText(f"{status}")