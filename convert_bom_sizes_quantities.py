"""
BOM Size and Quantity Converter
===============================

This script converts BOM sizes and quantities in Excel workbooks using existing conversion functions.

Features:
- Converts 'size' column into 'size1' and 'size2' columns
- Converts 'quantity' column to standardized float format
- Preserves all original data and columns
- Exports to specified output path

Usage:
- Set input_file_path to your Excel file
- Set output_file_path to desired output location
- Optionally set max_rows for testing with subset of data
- Run from IDE
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path

# Import existing conversion functions
from data_conversions import (
    process_size_column,
    convert_quantity_to_float,
    format_size_value
)

def clean_non_ascii_characters(value):
    """
    Clean non-ASCII characters from size and quantity strings.

    Replaces common non-ASCII quote characters with ASCII equivalents:
    - ′ (prime) and ' (left single quotation mark) → ' (apostrophe)
    - ″ (double prime) and " (left double quotation mark) → " (quotation mark)

    Args:
        value: String value to clean

    Returns:
        str: Cleaned string with ASCII characters
    """
    if pd.isna(value) or not isinstance(value, str):
        return value

    # Replace non-ASCII quote characters with ASCII equivalents
    cleaned = value.replace('\u2032', "'")  # Prime to apostrophe
    cleaned = cleaned.replace('\u2018', "'")  # Left single quotation mark to apostrophe
    cleaned = cleaned.replace('\u2019', "'")  # Right single quotation mark to apostrophe
    cleaned = cleaned.replace('\u2033', '"')  # Double prime to quotation mark
    cleaned = cleaned.replace('\u201c', '"')  # Left double quotation mark to quotation mark
    cleaned = cleaned.replace('\u201d', '"')  # Right double quotation mark to quotation mark

    return cleaned

def convert_bom_sizes_and_quantities(input_file_path, output_file_path, max_rows=None):
    """
    Convert sizes and quantities in a BOM Excel file.
    
    Args:
        input_file_path (str): Path to input Excel file
        output_file_path (str): Path for output Excel file
        max_rows (int, optional): Limit processing to first N rows for testing
    
    Returns:
        str: Success message with file path
    """
    
    print(f"Loading data from: {input_file_path}")
    
    # Load the Excel file
    try:
        df = pd.read_excel(input_file_path)
        print(f"Loaded {len(df)} rows from input file")
    except Exception as e:
        raise Exception(f"Failed to load input file: {e}")
    
    # Limit rows if specified
    if max_rows and max_rows < len(df):
        df = df.head(max_rows)
        print(f"Limited processing to first {max_rows} rows")
    
    # Make a copy to preserve original data
    df_converted = df.copy()
    
    # Process sizes if 'size' column exists
    if 'size' in df_converted.columns:
        print("Processing size column...")

        # Clean non-ASCII characters from size column
        print("Cleaning non-ASCII characters from size column...")
        df_converted['size'] = df_converted['size'].apply(clean_non_ascii_characters)

        # Debug: Print first few size values to see their format
        print("DEBUG: First 5 size values and their types:")
        for i in range(min(5, len(df_converted))):
            size_val = df_converted['size'].iloc[i]
            print(f"  Row {i}: '{size_val}' (type: {type(size_val)})")

        # Apply size conversion to get size1 and size2
        size_results = df_converted['size'].apply(lambda x: process_size_column(x) if pd.notna(x) else (np.nan, np.nan))

        # Extract size1 and size2 from the results
        df_converted['size1'] = [result[0] for result in size_results]
        df_converted['size2'] = [result[1] for result in size_results]

        print(f"Created size1 and size2 columns from {df_converted['size'].notna().sum()} size values")
    else:
        print("No 'size' column found in input data")
    
    # Process quantities if 'quantity' column exists
    if 'quantity' in df_converted.columns:
        print("Processing quantity column...")

        # Clean non-ASCII characters from quantity column
        print("Cleaning non-ASCII characters from quantity column...")
        df_converted['quantity'] = df_converted['quantity'].apply(clean_non_ascii_characters)

        # Debug: Print first few quantity values to see their format
        print("DEBUG: First 5 quantity values and their types:")
        for i in range(min(5, len(df_converted))):
            qty_val = df_converted['quantity'].iloc[i]
            print(f"  Row {i}: '{qty_val}' (type: {type(qty_val)})")

        # Apply quantity conversion
        print("Applying convert_quantity_to_float to quantity values...")
        df_converted['quantity_converted'] = df_converted['quantity'].apply(convert_quantity_to_float)

        # Debug: Print first few converted values
        print("DEBUG: First 5 converted quantity values:")
        for i in range(min(5, len(df_converted))):
            original_val = df_converted['quantity'].iloc[i]
            converted_val = df_converted['quantity_converted'].iloc[i]
            print(f"  Row {i}: '{original_val}' -> {converted_val}")

        # Replace original quantity with converted values
        #df_converted['quantity'] = df_converted['quantity_converted']
        df_converted['quantity'] = df_converted['quantity_converted']
        df_converted.drop('quantity_converted', axis=1, inplace=True)

        print(f"Converted {df_converted['quantity'].notna().sum()} quantity values")
    else:
        print("No 'quantity' column found in input data")

    if 'item_length' in df_converted.columns:
        print("Processing item_length column...")

        # Clean non-ASCII characters from item_length column
        print("Cleaning non-ASCII characters from item_length column...")
        df_converted['item_length'] = df_converted['item_length'].apply(clean_non_ascii_characters)

        # Debug: Print first few item_length values to see their format
        print("DEBUG: First 5 item_length values and their types:")
        for i in range(min(5, len(df_converted))):
            length_val = df_converted['item_length'].iloc[i]
            print(f"  Row {i}: '{length_val}' (type: {type(length_val)})")

        # Apply quantity conversion
        print("Applying convert_quantity_to_float to item_length values...")
        df_converted['item_length_converted'] = df_converted['item_length'].apply(convert_quantity_to_float)

        # Debug: Print first few converted values
        print("DEBUG: First 5 converted item_length values:")
        for i in range(min(5, len(df_converted))):
            original_val = df_converted['item_length'].iloc[i]
            converted_val = df_converted['item_length_converted'].iloc[i]
            print(f"  Row {i}: '{original_val}' -> {converted_val}")

        # Replace original quantity with converted values
        df_converted['item_length'] = df_converted['item_length_converted']
        df_converted.drop('item_length_converted', axis=1, inplace=True)

        print(f"Converted {df_converted['item_length'].notna().sum()} item_length values")
    else:
        print("No 'item_length' column found in input data")

    if 'total_length' in df_converted.columns:
        print("Processing total_length column...")

        # Clean non-ASCII characters from total_length column
        print("Cleaning non-ASCII characters from total_length column...")
        df_converted['total_length'] = df_converted['total_length'].apply(clean_non_ascii_characters)

        # Apply quantity conversion
        df_converted['total_length_converted'] = df_converted['total_length'].apply(convert_quantity_to_float)

        # Replace original quantity with converted values
        df_converted['total_length'] = df_converted['total_length_converted']
        df_converted.drop('total_length_converted', axis=1, inplace=True)

        print(f"Converted {df_converted['total_length'].notna().sum()} total_length values")
    else:
        print("No 'total_length' column found in input data")
    
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_file_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")
    
    # Export to Excel
    try:
        df_converted.to_excel(output_file_path, index=False)
        print(f"Successfully exported converted data to: {output_file_path}")
        
        # Print summary statistics
        print("\n=== Conversion Summary ===")
        print(f"Total rows processed: {len(df_converted)}")
        
        if 'size1' in df_converted.columns:
            size1_count = df_converted['size1'].notna().sum()
            size2_count = df_converted['size2'].notna().sum()
            print(f"Size1 values created: {size1_count}")
            print(f"Size2 values created: {size2_count}")
        
        if 'quantity' in df_converted.columns:
            quantity_count = df_converted['quantity'].notna().sum()
            print(f"Quantity values converted: {quantity_count}")
        
        print("\n Sameple Data: \n\n",df_converted.head(5))

        return f"Conversion completed successfully. Output saved to: {output_file_path}"
    
    
        
    except Exception as e:
        raise Exception(f"Failed to save output file: {e}")

    
if __name__ == "__main__":
    # =================================================================
    # CONFIGURATION - Update these paths for your specific use case
    # =================================================================
    
    # Input Excel file path
    input_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0028 - BSLTX34282 Phillips 66 Lake Charles I-10 General Construction\Workspace\BOM - Load.xlsx"
    # Output Excel file path
    output_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0028 - BSLTX34282 Phillips 66 Lake Charles I-10 General Construction\Workspace\BOM - Load2.xlsx"
    # Optional: Limit rows for testing (set to None to process all rows)
    max_rows = None  # Set to a number like 100 for testing
    
    # =================================================================
    # EXECUTION
    # =================================================================
    
    try:
        # Validate input file exists
        if not os.path.exists(input_file_path):
            print(f"ERROR: Input file not found: {input_file_path}")
            print("Please update the input_file_path variable with the correct path to your Excel file.")
        else:
            # Run the conversion
            result = convert_bom_sizes_and_quantities(
                input_file_path=input_file_path,
                output_file_path=output_file_path,
                max_rows=max_rows
            )
            print(f"\n{result}")
            
    except Exception as e:
        print(f"ERROR: {e}")
        print("\nPlease check your file paths and try again.")
