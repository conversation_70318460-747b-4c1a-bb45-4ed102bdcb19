-- Simple inspection of tables
-- No assumptions about column names

CREATE OR REPLACE FUNCTION public.inspect_tables(
    p_project_id INTEGER
)
RETURNS TABLE (
    table_name TEXT,
    column_names TEXT
)
AS $$
BEGIN
    -- Return column names from information_schema
    RETURN QUERY
    
    SELECT 'BOM table columns' AS table_name,
           string_agg(column_name, ', ' ORDER BY ordinal_position) AS column_names
    FROM information_schema.columns 
    WHERE table_name = 'bom' AND table_schema = 'public'
    
    UNION ALL
    
    SELECT 'GENERAL table columns' AS table_name,
           string_agg(column_name, ', ' ORDER BY ordinal_position) AS column_names
    FROM information_schema.columns 
    WHERE table_name = 'general' AND table_schema = 'public';
    
END;
$$ LANGUAGE plpgsql;
