from PySide6.QtWidgets import QPushButton, QMenu, QLabel
from PySide6.QtGui import QMovie, QIcon
from PySide6.QtCore import Signal
from pubsub import pub
from src.pyside_util import get_resource_qicon

class BlueprintReaderStatusWidget(QPushButton):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        self.menuActions = {}
        self.jobItems = {}
        self.setText("")
        pub.subscribe(self.onStatus, "set-statusbar-blueprintreader")
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.setIcon(get_resource_qicon("columns.svg"))
        self.setToolTip("Document Viewer")

    def onStatus(self, data):
        self.sgnUpdateStatus.emit(data)
    
    def updateAnimation(self):
        self.setIcon(QIcon(self.movie.currentPixmap()))

    def onUpdateStatus(self, data):
        msg = data["params"].get("Pos", "")
        self.setText(" " + msg)
        if not msg:
            self.hide()
        else:
            self.show()
