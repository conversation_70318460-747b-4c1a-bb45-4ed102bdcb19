import fitz
import hashlib
from typing import Dict, List, Tuple, Set
from collections import defaultdict
import os
import multiprocessing
import time
import traceback
from functools import partial
import pandas as pd
import re


class PageIdentifier:
    """Class to uniquely identify a page within multiple documents"""
    def __init__(self, file_index: int, file_name: str, page_num: int):
        self.file_index = file_index
        self.file_name = file_name
        self.page_num = page_num  # 0-based
    
    def __str__(self):
        return f"{os.path.basename(self.file_name)} (page {self.page_num + 1})"
    
    def __repr__(self):
        return self.__str__()
    
    def as_dict(self):
        return {
            'file_index': self.file_index,
            'file_name': self.file_name,
            'file_basename': os.path.basename(self.file_name),
            'page_num': self.page_num,
            'page_num_human': self.page_num + 1  # 1-based for human reading
        }


def process_page(args: Tuple[int, int, str]) -> Tuple[int, int, str, str]:
    """
    Process a single page to generate its checksum.
    This function is designed to be called by multiprocessing.
    
    Args:
        args: Tuple of (file_index, page_num, pdf_path)
        
    Returns:
        Tuple[int, int, str, str]: Tuple of (file_index, page_num, pdf_path, checksum)
    """
    file_index, page_num, pdf_path = args
    
    try:
        # Open document (each process needs its own handle)
        doc = fitz.open(pdf_path)
        
        # Get the page
        page = doc[page_num]
        
        # Get full resolution pixmap for maximum accuracy
        pix = page.get_pixmap()
        
        # Get the raw image data
        img_data = pix.samples
        
        # Generate checksum from the image data
        checksum = hashlib.sha256(img_data).hexdigest()
        
        # Close the document
        doc.close()
        
        return (file_index, page_num, pdf_path, checksum)
    except Exception as e:
        print(f"Error processing page {page_num} in file {pdf_path}: {str(e)}")
        return (file_index, page_num, pdf_path, "ERROR")


def find_identical_pages_multi_pdf(pdf_paths: List[str], max_workers=None) -> Tuple[Dict[str, List[PageIdentifier]], Dict[str, List[PageIdentifier]], Dict[str, List[PageIdentifier]]]:
    """
    Identifies groups of identical pages across multiple PDF files.
    
    Args:
        pdf_paths (List[str]): List of paths to PDF files
        max_workers (int, optional): Maximum number of worker processes. 
                                    If None, uses CPU count.
        
    Returns:
        Tuple containing:
            - Dict[str, List[PageIdentifier]]: Dictionary mapping checksums to lists of page identifiers (duplicates)
            - Dict[str, List[PageIdentifier]]: Dictionary mapping checksums to lists of page identifiers (unique pages)
            - Dict[str, List[PageIdentifier]]: Dictionary mapping checksums to lists of all page identifiers
    """
    # Determine number of processes to use
    if max_workers is None:
        max_workers = multiprocessing.cpu_count()
    
    try:
        # Get total page count and build a list of tasks
        tasks = []
        total_pages = 0
        
        print("Collecting page information from all PDF files...")
        for file_index, pdf_path in enumerate(pdf_paths):
            try:
                with fitz.open(pdf_path) as doc:
                    page_count = len(doc)
                    print(f"File {file_index+1}: {os.path.basename(pdf_path)} - {page_count} pages")
                    
                    # Add tasks for each page
                    for page_num in range(page_count):
                        tasks.append((file_index, page_num, pdf_path))
                    
                    total_pages += page_count
            except Exception as e:
                print(f"Error opening file {pdf_path}: {str(e)}")
        
        print(f"Total files: {len(pdf_paths)}, Total pages: {total_pages}")
        
        if not tasks:
            print("No valid PDF files to process.")
            return {}, {}, {}
        
        # Prepare the process pool
        print(f"Starting multiprocessing with {max_workers} workers...")
        start_time = time.time()
        
        # Need to use 'spawn' method for Windows compatibility
        ctx = multiprocessing.get_context('spawn')
        
        # Create a process pool
        with ctx.Pool(processes=max_workers) as pool:
            # Process pages in chunks and show progress
            results = []
            chunk_size = max(1, len(tasks) // (max_workers * 10))  # Adjust chunk size based on total workload
            
            for i, result in enumerate(pool.imap_unordered(process_page, tasks, chunk_size)):
                results.append(result)
                if (i + 1) % max(1, total_pages // 100) == 0 or (i + 1) == total_pages:
                    progress = (i + 1) / total_pages * 100
                    print(f"Processed {i + 1}/{total_pages} pages ({progress:.1f}%)")
        
        end_time = time.time()
        print(f"Multiprocessing completed in {end_time - start_time:.2f} seconds")
        
        # Group pages by checksum
        checksum_pages = defaultdict(list)
        for file_index, page_num, pdf_path, checksum in results:
            # Skip any pages that had errors
            if checksum != "ERROR":
                page_id = PageIdentifier(file_index, pdf_path, page_num)
                checksum_pages[checksum].append(page_id)
        
        # Filter out unique pages (those without duplicates)
        duplicate_pages = {
            checksum: pages 
            for checksum, pages in checksum_pages.items() 
            if len(pages) > 1
        }
        
        # Also create a set of unique pages (only appear once)
        all_checksums = set(checksum_pages.keys())
        duplicate_checksums = set(duplicate_pages.keys())
        unique_checksums = all_checksums - duplicate_checksums
        
        unique_pages = {
            checksum: pages
            for checksum, pages in checksum_pages.items()
            if checksum in unique_checksums
        }
        
        return duplicate_pages, unique_pages, checksum_pages
        
    except Exception as e:
        print(f"Error processing PDFs: {str(e)}")
        traceback.print_exc()  # Print the full stack trace for debugging
        return {}, {}, {}  # Return empty dictionaries in case of error


def create_excel_report(duplicate_groups: Dict[str, List[PageIdentifier]], 
                        unique_pages: Dict[str, List[PageIdentifier]],
                        all_pages: Dict[str, List[PageIdentifier]],
                        output_path: str = "duplicate_report.xlsx"):
    """
    Creates a detailed Excel report of duplicate and unique pages.
    
    Args:
        duplicate_groups: Dictionary mapping checksums to lists of page identifiers (duplicates)
        unique_pages: Dictionary mapping checksums to lists of page identifiers (unique pages)
        all_pages: Dictionary mapping checksums to lists of all page identifiers
        output_path: Path to save the Excel file
    """
    print(f"Creating Excel report at: {output_path}")
    
    # Create a Pandas Excel writer
    writer = pd.ExcelWriter(output_path, engine='openpyxl')
    
    # 1. Summary sheet
    total_pages = sum(len(pages) for pages in all_pages.values())
    total_unique_checksums = len(all_pages)
    total_duplicate_groups = len(duplicate_groups)
    total_duplicate_pages = sum(len(pages) for pages in duplicate_groups.values()) - len(duplicate_groups)
    total_unique_pages = sum(len(pages) for pages in unique_pages.values())
    
    summary_data = {
        'Metric': [
            'Total Pages Processed', 
            'Total Unique Content Signatures', 
            'Duplicate Groups', 
            'Duplicate Pages (excluding first occurrence)',
            'Pages with Unique Content'
        ],
        'Value': [
            total_pages,
            total_unique_checksums,
            total_duplicate_groups,
            total_duplicate_pages,
            total_unique_pages
        ]
    }
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    # 2. Duplicate groups sheet
    duplicate_rows = []
    
    for group_id, (checksum, pages) in enumerate(duplicate_groups.items(), 1):
        for i, page in enumerate(pages):
            page_dict = page.as_dict()
            duplicate_rows.append({
                'Group ID': group_id,
                'Page Order': i + 1,
                'Is First Occurrence': i == 0,
                'File Name': page_dict['file_basename'],
                'Full Path': page_dict['file_name'],
                'Page Number': page_dict['page_num_human'],
                'Content Hash': checksum[:10] + '...'  # Shortened for readability
            })
    
    if duplicate_rows:
        duplicates_df = pd.DataFrame(duplicate_rows)
        duplicates_df.to_excel(writer, sheet_name='Duplicate Pages', index=False)
    
    # 3. Unique pages sheet
    unique_rows = []
    
    for checksum, pages in unique_pages.items():
        for page in pages:  # Will only be one page per checksum
            page_dict = page.as_dict()
            unique_rows.append({
                'File Name': page_dict['file_basename'],
                'Full Path': page_dict['file_name'],
                'Page Number': page_dict['page_num_human'],
                'Content Hash': checksum[:10] + '...'  # Shortened for readability
            })
    
    if unique_rows:
        unique_df = pd.DataFrame(unique_rows)
        unique_df.to_excel(writer, sheet_name='Unique Pages', index=False)
    
    # 4. Per-file statistics
    file_stats = defaultdict(lambda: {'total_pages': 0, 'unique_pages': 0, 'duplicate_pages': 0})
    
    # Count pages in each file
    for checksum, pages in all_pages.items():
        is_duplicate = len(pages) > 1
        
        for page in pages:
            file_name = page.file_name
            file_stats[file_name]['total_pages'] += 1
            
            if is_duplicate:
                file_stats[file_name]['duplicate_pages'] += 1
            else:
                file_stats[file_name]['unique_pages'] += 1
    
    file_rows = []
    for file_name, stats in file_stats.items():
        file_rows.append({
            'File Name': os.path.basename(file_name),
            'Full Path': file_name,
            'Total Pages': stats['total_pages'],
            'Pages with Unique Content': stats['unique_pages'],
            'Pages with Duplicate Content': stats['duplicate_pages'],
            'Duplicate Percentage': f"{stats['duplicate_pages'] / stats['total_pages'] * 100:.1f}%"
        })
    
    files_df = pd.DataFrame(file_rows)
    files_df.to_excel(writer, sheet_name='File Statistics', index=False)
    
    # 5. Single consolidated page comparison sheet (for comparing page ordering between files)
    if len(file_stats) > 1:  # Only create if we have multiple files
        # Get a list of file indices and names
        file_list = [(i, name) for i, name in enumerate(file_stats.keys())]
        
        # Create a mapping of checksum -> list of pages in each file
        comparison_map = {}
        for checksum, pages in all_pages.items():
            # Initialize an entry for each file with empty lists
            file_pages = {file_idx: [] for file_idx, _ in file_list}
            
            # Populate with pages from each file
            for page in pages:
                for file_idx, file_name in file_list:
                    if page.file_name == file_name:
                        file_pages[file_idx].append(page.page_num + 1)  # Store 1-based page numbers
            
            # Add to the comparison map
            comparison_map[checksum] = file_pages
        
        # Create rows for the comparison sheet
        comparison_rows = []
        for checksum, file_pages in comparison_map.items():
            # Get page numbers for each file (comma-separated if multiple)
            page_columns = {}
            for file_idx, file_name in file_list:
                base_name = os.path.basename(file_name)
                pages_str = ", ".join(map(str, sorted(file_pages[file_idx]))) if file_pages[file_idx] else "Not Present"
                page_columns[f"File {file_idx+1}: {base_name}"] = pages_str
            
            # Determine if this content appears in all files
            in_all_files = all(len(pages) > 0 for pages in file_pages.values())
            
            # Check if order matches across files
            same_position = False
            if in_all_files:
                # Check if any page position is common across all files
                for pos in file_pages[0]:
                    if all(pos in pages for pages in file_pages.values()):
                        same_position = True
                        break
            
            # Create a row for this content signature
            row = {
                'Content ID': checksum[:8],  # First 8 chars of hash for reference
                'In All Files': in_all_files,
                'Same Position': same_position
            }
            row.update(page_columns)  # Add the file-specific page columns
            
            comparison_rows.append(row)
        
        # Sort rows by whether they appear in all files, then by same position
        comparison_rows.sort(key=lambda x: (x['In All Files'], x['Same Position']), reverse=True)
        
        # Create the dataframe
        comparison_df = pd.DataFrame(comparison_rows)
        comparison_df.to_excel(writer, sheet_name='Page Comparison', index=False)
    
    # Save the Excel file
    writer.close()
    
    print(f"Excel report created successfully: {output_path}")
    
    return unique_pages  # Return the unique pages dictionary


def get_unique_pages_list(unique_pages: Dict[str, List[PageIdentifier]]) -> List[Tuple[str, int]]:
    """
    Returns a list of unique pages as (file_path, page_number) tuples.
    
    Args:
        unique_pages: Dictionary mapping checksums to lists of page identifiers
        
    Returns:
        List[Tuple[str, int]]: List of (file_path, page_number) tuples for unique pages
    """
    unique_pages_list = []
    
    for pages in unique_pages.values():
        for page in pages:
            unique_pages_list.append((page.file_name, page.page_num))
    
    # Sort by file name and then page number
    unique_pages_list.sort()
    
    return unique_pages_list


def print_duplicate_summary(duplicate_pages: Dict[str, List[PageIdentifier]]):
    """
    Prints a summary of duplicate pages.
    
    Args:
        duplicate_pages: Dictionary mapping checksums to lists of page identifiers
    """
    if not duplicate_pages:
        print("No duplicate pages found across the files.")
        return
    
    total_groups = len(duplicate_pages)
    total_duplicates = sum(len(pages) - 1 for pages in duplicate_pages.values())
    
    print(f"\nFound {total_groups} groups of duplicate content")
    print(f"Total duplicate pages: {total_duplicates} (excluding first occurrences)")
    
    # Print sample of duplicate groups (up to 5)
    print("\nSample of duplicate groups:")
    for i, (checksum, pages) in enumerate(list(duplicate_pages.items())[:5], 1):
        pages_str = ', '.join(str(p) for p in pages)
        print(f"Group {i}: {pages_str}")
    
    if len(duplicate_pages) > 5:
        print(f"... and {len(duplicate_pages) - 5} more groups")


def save_group_pdfs(duplicate_pages: Dict[str, List[PageIdentifier]], output_dir: str = "duplicate_groups"):
    """
    Saves each group of identical pages as a separate PDF file.
    
    Args:
        duplicate_pages: Dictionary mapping checksums to lists of page identifiers
        output_dir: Directory to save the group PDFs
    """
    if not duplicate_pages:
        print("No duplicate pages to save.")
        return
        
    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create a mapping of file paths to open documents
        open_docs = {}
        
        # Process each group
        print(f"Saving duplicate page groups to {output_dir}...")
        for i, (checksum, pages) in enumerate(duplicate_pages.items(), 1):
            # Create new PDF for this group
            output_path = os.path.join(output_dir, f"group_{i}.pdf")
            new_doc = fitz.open()
            
            # Add all pages from this group
            for page_id in pages:
                # Get or open the source document
                if page_id.file_name not in open_docs:
                    open_docs[page_id.file_name] = fitz.open(page_id.file_name)
                
                src_doc = open_docs[page_id.file_name]
                
                # Add page to the new document
                new_doc.insert_pdf(src_doc, from_page=page_id.page_num, to_page=page_id.page_num)
            
            # Save the group PDF
            new_doc.save(output_path)
            new_doc.close()
            
            if i % 10 == 0 or i == len(duplicate_pages):
                print(f"Saved {i}/{len(duplicate_pages)} groups")
        
        print(f"Successfully saved {len(duplicate_pages)} duplicate page groups")
            
    except Exception as e:
        print(f"Error saving group PDFs: {str(e)}")
    finally:
        # Close all open documents
        for doc in open_docs.values():
            doc.close()


# Example usage
if __name__ == "__main__":
    # This ensures correct multiprocessing on Windows
    multiprocessing.freeze_support()
    
    # Configuration options
    max_workers = None  # Number of worker processes (None = auto-detect based on CPU cores)
    create_report = True  # Whether to create a detailed Excel report
    save_groups = False  # Whether to save each group as a separate PDF
    
    # Start timing the full process
    full_process_start = time.time()
    print(f"Starting multi-PDF duplicate detection at: {time.strftime('%H:%M:%S', time.localtime())}")
    
    # List of PDF paths to compare
    pdf_paths = [
        r"C:\Users\<USER>\Downloads\Combined Casey Best Performance Novelis.pdf",
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 037\Binder1 - 745 isos COMBINED.pdf",
        # Add more PDF paths as needed
    ]
    
    # Run the multiprocessing analysis
    duplicate_pages, unique_pages, all_pages = find_identical_pages_multi_pdf(pdf_paths, max_workers=max_workers)
    
    # Print summary of duplicate pages
    print_duplicate_summary(duplicate_pages)
    
    # Create Excel report if requested
    if create_report:
        create_excel_report(duplicate_pages, unique_pages, all_pages, 
                            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 037\pdf_comparison_report.xlsx")
    
    # Save group PDFs if requested
    if save_groups:
        save_group_pdfs(duplicate_pages)
    
    # End timing and print results
    full_process_end = time.time()
    elapsed_time = full_process_end - full_process_start
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n" + "="*50)
    print(f"Full process completed at: {time.strftime('%H:%M:%S', time.localtime())}")
    print(f"Total execution time: {int(hours):02d}:{int(minutes):02d}:{seconds:.2f}")
    print("="*50)