"""
View BOM Table Columns
======================

Simple utility to display the columns in the SQLite BOM table.
"""

import sqlite3
import os
from src.app_paths import getDatabasePath

def view_bom_table_columns():
    """
    Connect to the SQLite database and display the BOM table columns.
    """
    try:
        # Get the database path
        db_path = getDatabasePath()
        print(f"Database path: {db_path}")
        
        # Check if the database file exists
        if not os.path.exists(db_path):
            print(f"Error: Database file not found at {db_path}")
            return
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get BOM table schema
        cursor.execute("PRAGMA table_info(BOM);")
        columns = cursor.fetchall()
        
        if not columns:
            print("BOM table not found or has no columns.")
            return
        
        print("\n" + "="*60)
        print("BOM TABLE COLUMNS")
        print("="*60)
        print(f"{'ID':<3} | {'Column Name':<25} | {'Data Type':<15} | {'Not Null':<8} | {'Default':<10} | {'PK':<3}")
        print("-" * 60)
        
        for column in columns:
            # Column format: (cid, name, type, notnull, dflt_value, pk)
            cid, name, type_name, not_null, default_val, pk = column
            default_val = str(default_val) if default_val is not None else ""
            
            print(f"{cid:<3} | {name:<25} | {type_name:<15} | {not_null:<8} | {default_val:<10} | {pk:<3}")
        
        print("-" * 60)
        print(f"Total columns: {len(columns)}")
        
        # Also get a count of records in the BOM table
        cursor.execute("SELECT COUNT(*) FROM BOM;")
        record_count = cursor.fetchone()[0]
        print(f"Total records in BOM table: {record_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    view_bom_table_columns()
