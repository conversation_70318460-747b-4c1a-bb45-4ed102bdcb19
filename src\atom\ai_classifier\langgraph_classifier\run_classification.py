"""
LangGraph BOM Classification - Direct IDE Runner

Simple script to run the complete BOM classification process directly from IDE.
Just set your file paths and run!
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the parent directories to the path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent))
sys.path.insert(0, str(current_dir.parent.parent))

try:
    from process_and_export import load_bom_data, process_bom_with_langgraph
    from export_results import export_classified_results
    from integration_layer import ModelType
    print("✅ Successfully imported LangGraph modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the correct directory")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# =============================================================================
# CONFIGURATION - SET YOUR PATHS HERE
# =============================================================================

# Input BOM file path
BOM_FILE_PATH = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Employee Testing\TEST 3 - Heartwell (AX_0044)\Unclassified - Subset.xlsx"

# Output folder path
OUTPUT_FOLDER = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Employee Testing\TEST 3 - Heartwell (AX_0044)"

# Job name (optional)
JOB_NAME = "LangGraph_Classification_Test"

# Max items to process (None for all items, or set a number like 10 for testing)
MAX_ITEMS = 10  # Set to 10 for testing, None for full file

# Model choice: ModelType.GEMINI_20_FLASH (faster) or ModelType.GEMINI_25_FLASH (more accurate)
MODEL_TYPE = ModelType.GEMINI_20_FLASH

# Debug mode (True for detailed output, False for minimal output)
DEBUG_MODE = False

# Stage only (True for Stage 1 only - faster testing, False for complete workflow)
STAGE_ONLY = False

# Concurrent workers (10=safe, 15=recommended for large datasets, 20+=aggressive)
MAX_CONCURRENT_WORKERS = 15  # Recommended for 4800 rows

# =============================================================================


async def run_classification():
    """Run the classification process with the configured settings"""

    print("🚀 LangGraph BOM Classification - Direct IDE Runner")
    print("=" * 60)

    # Load environment variables
    load_dotenv()

    # Check API key
    api_key = os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
    if api_key:
        print(f"✅ API Key found: {api_key[:10]}...")
    else:
        print("❌ API Key not found in .env file!")
        print("Please check your .env file has: GEMINI_API_KEY=your_key_here")
        return False

    # Validate configuration
    print(f"\n📋 Configuration:")
    print(f"   BOM File: {BOM_FILE_PATH}")
    print(f"   Output Folder: {OUTPUT_FOLDER}")
    print(f"   Job Name: {JOB_NAME}")
    print(f"   Max Items: {MAX_ITEMS if MAX_ITEMS else 'All items'}")
    print(f"   Model: {MODEL_TYPE.value}")
    print(f"   Debug Mode: {DEBUG_MODE}")
    print(f"   Stage Only: {STAGE_ONLY}")

    # Validate BOM file
    bom_path = Path(BOM_FILE_PATH)
    if not bom_path.exists():
        print(f"❌ BOM file not found: {BOM_FILE_PATH}")
        print("Please update BOM_FILE_PATH in the configuration section")
        return False

    if bom_path.suffix.lower() not in ['.xlsx', '.xls', '.csv']:
        print(f"❌ Unsupported file type: {bom_path.suffix}")
        print("Supported formats: .xlsx, .xls, .csv")
        return False

    # Create output folder
    output_path = Path(OUTPUT_FOLDER)
    try:
        output_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Output folder ready: {OUTPUT_FOLDER}")
    except Exception as e:
        print(f"❌ Cannot create output folder: {e}")
        return False


    try:
        # Load BOM data
        print("\n� Loading BOM data...")
        bom_df = load_bom_data(str(bom_path))
        print(f"✅ Loaded {len(bom_df)} items from BOM file")

        # Apply max items limit if specified
        if MAX_ITEMS and MAX_ITEMS < len(bom_df):
            bom_df = bom_df.head(MAX_ITEMS)
            print(f"🔢 Limited to {MAX_ITEMS} items for processing")

        # Process with LangGraph
        print(f"\n🤖 Processing with LangGraph...")
        print(f"   Model: {MODEL_TYPE.value}")
        print(f"   Items: {len(bom_df)}")
        print(f"   Mode: {'Stage 1 Only' if STAGE_ONLY else 'Complete Workflow'}")

        results = await process_bom_with_langgraph(
            bom_df=bom_df,
            model_type=MODEL_TYPE,
            stage_only=STAGE_ONLY,
            max_items=None,  # Already limited above
            debug_mode=DEBUG_MODE
        )

        # Export results
        print(f"\n📊 Exporting results...")
        export_path = export_classified_results(
            workflow_results=results,
            job_folder=OUTPUT_FOLDER,
            original_data=bom_df,
            job_name=JOB_NAME
        )

        print(f"\n🎉 SUCCESS!")
        print(f"📁 Results exported to: {export_path}")
        print(f"📊 Processed {len(results)} items")

        # Calculate success rate
        successful_items = sum(1 for r in results if r.get("processing_path") != "error")
        success_rate = (successful_items / len(results) * 100) if results else 0
        print(f"✅ Success rate: {success_rate:.1f}% ({successful_items}/{len(results)})")

        return True

    except Exception as e:
        print(f"\n❌ FAILED: {e}")
        if DEBUG_MODE:
            import traceback
            traceback.print_exc()
        return False


if __name__ == "__main__":
    """
    Direct IDE execution - just run this file!

    1. Set your file paths in the configuration section above
    2. Run this file directly from your IDE
    3. Watch the complete process run automatically
    """

    print("🚀 Starting LangGraph BOM Classification...")
    print("📝 Edit the configuration section above to set your file paths")
    print()

    # Run the classification
    success = asyncio.run(run_classification())

    if success:
        print("\n✅ Classification completed successfully!")
    else:
        print("\n❌ Classification failed - check configuration and file paths")
