import os
import time
import pandas as pd

from src.app_paths import getSourceRawDataPath, getSourceExtractionOptionsPath
from src.utils.convert_roi_payload import convert_roi_payload
from src.atom.roiextraction import RoiExtraction

def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip())  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(1, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part)  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return pages


def plugin_draw_raw_data_coordinates(project_source):

    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, fileExists)

    group_list = []
    if extract_group_list:
        try:
            group_list = [int(g) for g in extract_group_list.split(",")]
        except Exception:
            pass

        if not group_list:
            try:
                group_list = [int(extract_group_list)]
            except Exception:
                return "Invalid group list format."

    # Group list takes precedence over page range

    if not roi_payload:
        print("Loading saved ROI payload")
        roi_payload = getSourceExtractionOptionsPath(projectId, filename)
        if not os.path.exists(roi_payload):
            error = "A saved ROI payload not found for this project source."
            print(error)
            return error

    roi_payload = convert_roi_payload(roi_payload, force_extract=True, ignore_bom=True)

    print(roi_payload)

    cleaned_roi_payload = {}
    cleaned_roi_payload["ocr"] = roi_payload["ocr"]

    general_fields = [g.strip().lower() for g in general_fields.split(",")] if general_fields else []

    pages = parse_page_range(str(page_range), 9999)
    final_pages = set()
    unique_groups = set()
    cleaned_roi_payload['pageToGroup'] = {}
    for page, group in roi_payload['pageToGroup'].items():
        if extract_group_list and group not in group_list:
            continue
        if page in pages:
            cleaned_roi_payload['pageToGroup'][page] = group
            final_pages.add(page)
            unique_groups.add(group)

    table_names = [
        "bom",
        "general",
        "spool",
        "spec",
        "ifc",
        "generic_1",
        "generic_2"
    ]