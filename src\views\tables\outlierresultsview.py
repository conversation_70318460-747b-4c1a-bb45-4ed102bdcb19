from src.utils.logger import logger
from src.views.tableresultsview import TableResultsViewBase

# logger = logging.getLogger(__file__)


class OutlierDataView(TableResultsViewBase):

    def __init__(self, parent) -> None:
        super().__init__(parent)

    def  __repr__(self) -> str:
        return "Outlier"

    def initToolbar(self):
        super().initToolbar()

    def onToolbarBtn(self, name):
        super().onToolbarBtn(name)