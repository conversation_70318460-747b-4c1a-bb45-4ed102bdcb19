import fitz
import pandas as pd


def plugin_detect_missing_pages(input_file,
                                project_source: tuple,
                                use_pdf_file: bool = False,
                                pdf_file: str = "",
                                pdf_page_key: str = "pdf_page",
                                use_page_count: bool = False,
                                custom_page_count: int = -1):
    """
    Detect missing pages in a file against a the page count from project source or pdf, or a custom page count.

    See output to grab missing pages list

    Args:
        input_file (str): Input file path
        project_source (tuple): Project source tuple.
        use_pdf_file (bool, optional): Use PDF file. Defaults to False.
        pdf_file (str, optional): PDF file path. Defaults to "". If use_pdf_file is checked, this is required. This overrides project_source
        pdf_page_key (str, optional): PDF page key. The name of the page key in input_file
        use_page_count (bool, optional): Use page count. Defaults to False.
        custom_page_count (int, optional): Custom page count. Defaults to -1. If use_page_count is checked, this is required. This overrides use_pdf_file
    """
    df = pd.read_excel(input_file)

    if use_page_count:
        if custom_page_count < 1:
            return "Custom page count must be greater than 0"
        elif custom_page_count > 10000:
            return "Custom page count must be less than 10000"
        else:
            page_numbers = custom_page_count
    else:
        if not use_pdf_file:
            if not project_source:
                return "Project source selection required"
            _, pdf_file = project_source

        else:
            if not pdf_file:
                return "PDF File is required if use_pdf_file is checked"

        print()
        print(pdf_file)
        print()
        doc = fitz.open(pdf_file)

        page_numbers = doc.page_count

    missing_pages = []

    if pdf_page_key not in df.columns:
        return f"PDF Page key ({pdf_page_key}) is not in input_file Dataframe"

    pages = sorted(df[pdf_page_key].unique().tolist())

    for page_num in range(1, page_numbers + 1):
        if page_num not in pages:
            missing_pages.append(page_num)

    print(f"Present Page count: {len(pages)}")
    print("Pages:")
    print(pages)
    print()
    print(f"Missing pages: count = {len(missing_pages)}")
    print(missing_pages)

    return {
        "message": "Missing pages - see output for missing pages list",
        "missing_count": len(missing_pages)
    }

def plugin_unique_material_description(bom_file, save_file: str = None):
    bom_df = pd.read_excel(bom_file)
    descriptions = bom_df["material_description"].unique()
    print(descriptions)

    if save_file:
        df = pd.DataFrame(descriptions, columns=["material_description"])
        df = df.sort_values(by="material_description")
        df.to_excel(save_file, index=False)
    return descriptions
