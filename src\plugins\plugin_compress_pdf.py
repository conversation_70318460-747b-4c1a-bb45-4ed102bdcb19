import fitz  # PyMuPDF
import os
from io import Bytes<PERSON>


def plugin_compress_pdf(input_file: str, output_file: str = "debug/optimized_pdf.pdf", quality=80, use_memory=True):
    doc = fitz.open(input_file)

    os.makedirs("debug/temp", exist_ok=True)

    for page_num in range(doc.page_count):
        print(page_num)
        page = doc[page]
        images = page.get_images(full=True)
        for img in images:
            xref = img[0]
            base_image = doc.extract_image(xref)

            if base_image["ext"] == "jpeg":
                continue  # Skip JPEGs (already compressed)

            pix = fitz.Pixmap(doc, xref)
            if pix.n > 4:  # Convert CMYK or grayscale with alpha to RGB
                pix = fitz.Pixmap(fitz.csRGB, pix)

            if use_memory:
                # In-memory JPEG compression
                buf = BytesIO()
                pix.save(buf, format="jpeg", quality=quality)
                buf.seek(0)
                doc.update_image(xref, buf.read())
            else:
                # Write to disk, then clean up
                temp_file = f"debug/temp/temp_img_{xref}.jpg"
                pix.writeImage(temp_file, quality=quality)
                with open(temp_file, "rb") as f:
                    doc.update_image(xref, f.read())
                os.remove(temp_file)

    doc.save(output_file, deflate=True, garbage=4)
    doc.close()
    print(f"Saved optimized PDF: {output_file}")


if __name__ == "__main__":
    input_file = r"C:\Drawings\Clients\brockservices\BSL30251 Extracted ISO files.pdf"
    output_file = "debug/optimized_pdf.pdf"
    plugin_compress_pdf(input_file, output_file)