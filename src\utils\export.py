from src.utils.logger import logger
import re

import pandas as pd

from openpyxl import Workbook
from openpyxl.styles import Font
from openpyxl.workbook.child import INVALID_TITLE_REGEX

# logger = logging.getLogger(__name__)


def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None


def _clean_dataframe_for_excel(df):
    """
    Clean DataFrame values to remove characters that are illegal in Excel worksheets.

    Args:
        df (pd.DataFrame): DataFrame to clean

    Returns:
        pd.DataFrame: Cleaned DataFrame
    """
    # Create a copy to avoid modifying the original
    df_clean = df.copy()

    # Define a regex pattern for illegal Excel characters (control characters)
    # This covers ASCII control characters from 0-31 except tab (9), LF (10), and CR (13)
    illegal_chars_pattern = r'[\x00-\x08\x0B\x0C\x0E-\x1F]'

    # Clean string columns only
    for col in df_clean.select_dtypes(include=['object']).columns:
        # Replace illegal characters with empty string
        df_clean[col] = df_clean[col].astype(str).str.replace(illegal_chars_pattern, '', regex=True)

    return df_clean


def _insert_sheet(wb: Workbook,
                  df: pd.DataFrame,
                  sheet_name: str = None,
                  autosize_columns: bool = True,
                  max_column_width: int = None):

    # Handle illegal chars
    title = re.sub(INVALID_TITLE_REGEX, '_', sheet_name)
    ws = wb.create_sheet(title)
    # Adding header row (column names)
    ws.append(list(df.columns))

    ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
    hyperlink_font = Font(color="0000FF", underline="single")

    for index, row in df.iterrows():
        row_values = [str(value) if not is_scalar(value) else value for value in row]
        ws.append(row_values)

        # Create a hyperlink for 'AIS_LINK'
        if ais_link_col_idx:
            ais_link_value = row['AIS_LINK']
            if ais_link_value:
                hyperlink = ais_link_value
                cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                cell.hyperlink = hyperlink
                cell.font = hyperlink_font

    if autosize_columns:
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter # Get the column name
            for cell in col:
                try: # Necessary to avoid error on empty cells
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.1
            if max_column_width:
                adjusted_width = min(max_column_width, adjusted_width)
            ws.column_dimensions[column].width = adjusted_width


def export_to_excel(data=None,
                    filename: str=None,
                    autosize_columns=True,
                    max_column_width=100,
                    sheet_names=[]):
    """_summary_

    Args:
        data (_type_, optional): A Dataframe or a list<Dataframes>. Defaults to None.
        filename (str, optional): Export filename. Defaults to None.
        autosize_columns (bool, optional): Resize columns to fit content. Defaults to True.
        sheet_names (list, optional): An ordered list of sheet names list<str>. Defaults to [].

    """
    assert filename
    # Single dataframe exported to a single sheet
    if isinstance(data, pd.DataFrame):
        data = [data]

    wb = Workbook()
    try:
        wb.remove(wb.active)
    except Exception as e:
        pass

    for n, df in enumerate(data):
        if len(df) == 0:
            continue

        try:
            # Clean the DataFrame to remove illegal Excel characters
            df = _clean_dataframe_for_excel(df)

            sheet_name = sheet_names[n]
            _insert_sheet(wb, df, sheet_name, autosize_columns, max_column_width)
        except Exception as e:
            logger.error(f"Error inserting sheet {n}: {e}")
            sheet_name = None # No sheet name provided, leave as default

    try:
        wb.save(filename)
        logger.info(f">>> Data exported and saved to: {filename}")
        print(f">>> Data exported and saved to: {filename}")
        return True
    except PermissionError as e:
        print(f"PermissionError: {e}")
        logger.error(f"Failed to write to {filename}. The file might be open or locked.")
        raise Exception(f"Failed to write to {filename}. The file might be open or locked.")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise Exception(f"Failed to write to {filename}")
