from src.utils.logger import logger
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QLabel, QGridLayout, QPushButton, QSizePolicy, QVBoxLayout, QLineEdit
from src.pyside_util import get_resource_qicon, applyDropShadowEffect
from pubsub import pub
from src.util import is_valid_email_string

# logger = logging.getLogger(__file__)


class UserProfilePopup(QWidget):

    sgnUpdateUserDetails = Signal(object)

    def __init__(self, parent):
        super().__init__(parent)

        self.data = {}  # Cache the latest details
        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(360)
        self.setMinimumHeight(380)

        widget = QWidget()
        widget.setObjectName("popup")
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())

        self.layout().addWidget(widget)

        profile = QPushButton("", widget)
        # profile.setFixedSize(128, 128)
        profile.setFixedHeight(128)
        self.iconProfile = get_resource_qicon("profile-icon.svg")
        profile.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        profile.setIcon(self.iconProfile)
        widget.layout().addWidget(profile)

        lblName = QLabel("Name:", self)
        grid.layout().addWidget(lblName, 1, 0, 1, 1)
        self.nameValue = QLineEdit("", self)
        grid.layout().addWidget(self.nameValue, 1, 1, 1, 1)

        lblEmail = QLabel("Email:", self)
        grid.layout().addWidget(lblEmail, 2, 0, 1, 1)
        self.emailValue = QLineEdit("", self)
        self.emailValue.setReadOnly(True)
        grid.layout().addWidget(self.emailValue, 2, 1, 1, 1)

        lblTokens = QLabel("Tokens:", self)
        grid.layout().addWidget(lblTokens, 3, 0, 1, 1)
        self.tokenValue = QLineEdit("", self)
        self.tokenValue.setReadOnly(True)
        grid.layout().addWidget(self.tokenValue, 3, 1, 1, 1)

        spacer = QLabel("", self)
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        grid.layout().addWidget(spacer, 4, 0, 1, 1)

        for v in [self.nameValue, self.emailValue, self.tokenValue]:
            v.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
            # v.setObjectName("popup")

        widget.layout().addWidget(grid)

        self.lblError = QLabel("", self)
        widget.layout().addWidget(self.lblError)

        self.pbUpdate = QPushButton("Update")
        self.pbUpdate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        widget.layout().addWidget(self.pbUpdate)
        self.pbUpdate.clicked.connect(self.onUpdateDetails)

        pub.subscribe(self.onUserDetailsGetResponse, "user-details-get-response")
        pub.subscribe(self.onUserDetailsUpdateResponse, "user-details-update-response")
        self.sgnUpdateUserDetails.connect(self.onUpdateUserDetails)

        applyDropShadowEffect(self)

    def setVisible(self, visible: bool) -> None:
        if visible:
            pub.sendMessage("user-details-get")
        return super().setVisible(visible)

    def onUpdateUserDetails(self, data):
        try:
            self.nameValue.setText(data.get("username"))
            self.emailValue.setText(data.get("email"))
            self.tokenValue.setText(str(data.get("tokens")))
            self.data = data
            self.setErrorMessage("")
        except Exception as e:
            logger.info(f"Failed to sync user details {e}")

    def onUserDetailsGetResponse(self, data):
        self.sgnUpdateUserDetails.emit(data)

    # https://regex101.com/r/lM0nC7/1
    def is_valid_username(self, username):
        import re
        regex = "^(?=.{4,32}$)(?![_.-])(?!.*[_.]{2})[a-zA-Z0-9._-]+(?<![_.])$"
        r = re.compile(regex)
        if (re.search(r, username)):
            return True
        else:
            return False

    def onUpdateDetails(self):
        newData = {}
        name = self.nameValue.text()
        email = self.emailValue.text()
        tokens = self.tokenValue.text()
        if self.is_valid_username(name):
            newData["username"] = name
        else:
            self.setErrorMessage("Invalid username")
            return
        newData["email"] = email
        if not is_valid_email_string(email):
            self.setErrorMessage("Invalid email address")
            return

        if not newData.get("username") or not newData.get("email"):
            return

        try:
            if tokens and tokens != self.data.get("tokens") and False:
                newData["tokens"] = int(tokens)
            if int(tokens) < 0:
                raise ValueError
        except Exception as e:
            self.tokenValue.setText(str(self.data.get("tokens", "n/a")))
            logger.info("Invalid token value", exc_info=True)

        self.pbUpdate.setEnabled(False)
        pub.sendMessage("user-details-update", data=newData)

    def onUserDetailsUpdateResponse(self, data: dict):
        if data.get("status") == "fail":
            self.setErrorMessage(data.get("error", ""))
        self.pbUpdate.setEnabled(True)

    def setErrorMessage(self, text):
        self.lblError.setText(text)

    def onTokenUpdate(self):
        try:
            value = int(self.tokenValue.text())
        except Exception as e:
            self.tokenValue.setText(self.data.get("tokens"))

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    window = UserProfilePopup(None)
    window.show()
    app.exec()