"""
Draw supports.

used to detect if supports are grouped
"""
import os
import fitz
import pandas as pd
from src.app_paths import getSourceRawDataPath

def main(filename, raw_df: pd.DataFrame, bom_df: pd.DataFrame = None, save_dir="debug", draw_pdf: bool = False, draw_non_support: bool = False):
    os.makedirs(save_dir, exist_ok=True)
    doc = fitz.open(filename)

    # print(raw_df)

    print("Raw data count", len(raw_df))

    # Sort by pdf_page and original index. create index
    raw_df = raw_df.reset_index()
    raw_df = raw_df.sort_values(by=['pdf_page', 'index'])

    # Filter rows where col is exactly s{number}
    all_supports_df = raw_df[raw_df["value"].str.match(r"^S\d+$", na=False)]
    # print(all_supports_df)
    all_supports_df.to_excel(os.path.join(save_dir, "all_supports_filtered.xlsx"), index=False)

    print("All supports row count", len(all_supports_df))

    # property value
    info = []

    new_doc = fitz.open()
    multiple_supports_page_mapping = []
    if draw_pdf:
        # Draw bounding boxes on PDF

        grouped_supports_df = all_supports_df.groupby('pdf_page')
        for n, (pdf_page, support_group) in enumerate(grouped_supports_df):
            new_doc.insert_pdf(doc, from_page=int(pdf_page)-1, to_page=int(pdf_page)-1)
            # Draw pdf_page text
            page = new_doc[new_doc.page_count-1]
            page.insert_text((10, 10), f"Page {pdf_page}. Support count: {len(support_group)}, Supports: {support_group['value'].tolist()}", fontsize=12)

            # Draw rect
            for index, row in support_group.iterrows():
                coordinates = row['coordinates2']  # [x0, y0, x1, y1]
                rect = fitz.Rect(coordinates[0], coordinates[1], coordinates[2], coordinates[3])
                page.draw_rect(rect, color=(1, 0, 0), width=1)

            multiple_supports_page_mapping.append((n + 1, int(pdf_page), len(support_group), ','.join(support_group['value'].tolist())))

        # grouped_supports_df.to_excel(os.path.join(save_dir, "grouped_supports.xlsx"), index=False)
        # print("Multiple supports detected in raw data")
        # multiple_raw_supports_detected_pages = grouped_supports_df["pdf_page"].unique().tolist()

        # Save the annotated PDF
        output_pdf = os.path.join(save_dir, "raw_pages_with_support.pdf")
        new_doc.save(output_pdf, deflate=True)
        new_doc.close()

        multiple_supports_page_mapping_df = pd.DataFrame(multiple_supports_page_mapping, columns=["pdf_page", "original_pdf_page", "support_count", "supports"] )

        # Insert detection columns
        for n in range(1, 4):
            multiple_supports_page_mapping_df[f"detect_group_{n}"] = ""

        multiple_supports_page_mapping_df.to_excel(os.path.join(save_dir, "raw_multiple_supports_page_mapping.xlsx"), index=False)

        print(f"Annotated PDF saved to: {output_pdf}")

    # Filter by pages which have multiple supports
    grouped_supports_df = all_supports_df.groupby('pdf_page').filter(lambda x: len(x) > 1)
    # print(grouped_supports_df)
    grouped_supports_df.to_excel(os.path.join(save_dir, "grouped_supports.xlsx"), index=False)
    print("Multiple supports detected in raw data")
    multiple_raw_supports_detected_pages = grouped_supports_df["pdf_page"].unique().tolist()

    print("Grouped supports row count", len(grouped_supports_df))


    zero_supports_detected_df = raw_df[~raw_df["pdf_page"].isin(all_supports_df["pdf_page"].unique().tolist())]
    print("Zero supports row count", len(zero_supports_detected_df))
    print(zero_supports_detected_df["pdf_page"].unique().tolist())

    zero_supports_detected_pages = zero_supports_detected_df["pdf_page"].unique().tolist()
    # zero_supports_detected_df.to_excel(os.path.join(save_dir, "zero_supports_detected.xlsx"), index=False, engine="xlsxwriter")
    # zero_supports_detected_df.to_csv(os.path.join(save_dir, "zero_supports_detected.csv"), index=False)

    doc.close()

    zero_support_page_mapping = {}
    for n, page in enumerate(zero_supports_detected_pages):
        zero_support_page_mapping[n] = page

    if draw_non_support:
        print("Drawing PDF with zero support components")
        # create subset pdf
        new_doc = fitz.open()
        doc = fitz.open(filename)
        for page in zero_supports_detected_pages:
            new_doc.insert_pdf(doc, from_page=int(page)-1, to_page=int(page)-1)

        # Draw pdf_page text
        for n, page in enumerate(new_doc):
            page.insert_text((10, 10), f"Page {zero_supports_detected_pages[n]}", fontsize=12)

        new_doc.save(os.path.join(save_dir, "zero_supports_detected.pdf"))
        new_doc.close()

        zero_support_page_mapping_df = pd.DataFrame(zero_support_page_mapping.items(), columns=["pdf_page", "original_pdf_page"])
        zero_support_page_mapping_df.to_excel(os.path.join(save_dir, "zero_support_page_mapping.xlsx"), index=False)

    info = [
        ("Multiple support pages", grouped_supports_df["pdf_page"].unique().tolist()),
        ("Total page count with multiple support", len(grouped_supports_df["pdf_page"].unique())),
        ("Zero support pages", zero_supports_detected_df["pdf_page"].unique().tolist()),
        ("Total page count with zero support", len(zero_supports_detected_df["pdf_page"].unique())),
    ]

    info_df = pd.DataFrame(info, columns=["Property", "Value"])
    info_df.to_excel(os.path.join(save_dir, "info.xlsx"), index=False)

    print(f"Found {len(all_supports_df)} support components")

    #
    if bom_df is None:
        return

    print("Checking BOM data")

    # Filter BOM pages which have at least one support
    bom_df_supports = bom_df[bom_df["general_category"] == "Support"].copy()
    # Get page list from this
    bom_support_pages = bom_df_supports["pdf_page"].unique().tolist()

    print(all_supports_df["pdf_page"].unique().tolist())

    print(bom_support_pages)

    in_non_support_pages = [page for page in bom_support_pages if page not in all_supports_df["pdf_page"].unique().tolist()]

    print("BOM df pages with supports but not detected in Raw DF", in_non_support_pages)

    # Bom pages with multiple supports
    multiple_bom_supports_df = bom_df_supports.groupby('pdf_page').filter(lambda x: len(x) > 1)
    multiple_bom_supports_pages = multiple_bom_supports_df["pdf_page"].unique().tolist()
    print("BOM df pages with multiple supports", multiple_bom_supports_pages)

    checks = [p for p in multiple_bom_supports_pages if p not in multiple_raw_supports_detected_pages]
    print("BOM df pages with multiple supports but not detected in Raw DF", checks)

    doc = fitz.open(filename)
    # Create multiple support pdf
    new_doc = fitz.open()
    for p in multiple_bom_supports_pages:
        new_doc.insert_pdf(doc, from_page=p-1, to_page=p-1)
        # Draw pdf_page text
        page = new_doc[new_doc.page_count-1]
        page.insert_text((10, 10), f"Page {p}", fontsize=12)

        page_raw_supports = all_supports_df[all_supports_df["pdf_page"] == p]
        # Draw rect
        for index, row in page_raw_supports.iterrows():
            coordinates = row['coordinates2']  # [x0, y0, x1, y1]
            rect = fitz.Rect(coordinates[0], coordinates[1], coordinates[2], coordinates[3])
            page.draw_rect(rect, color=(1, 0, 0), width=2)

    new_doc.save(os.path.join(save_dir, f"bom_pages_with_multiple_supports.pdf"))
    new_doc.close()

    doc.close()


if __name__ == "__main__":
    projectId = 1
    filename = "C:/Drawings/Clients/brockservices/BRS_0031 - BSLE34891 - Valero St Charles/received/BSLE34891 - Valero St Charles - Combined.pdf"
    feather = getSourceRawDataPath(projectId, filename)

    bom_file = r"c:\Drawings\Clients\brockservices\BRS_0031 - BSLE34891 - Valero St Charles\priority\BOM - merged with RFQ_Template_Final_20250818_151124.xlsx"
    bom_df = pd.read_excel(bom_file)

    save_dir = r"C:\Drawings\Clients\brockservices\BRS_0031 - BSLE34891 - Valero St Charles\priority\output"

    draw_non_support = False
    draw_pdf = True
    main(filename, pd.read_feather(feather), bom_df, save_dir, draw_pdf=draw_pdf, draw_non_support=draw_non_support)