from src.utils.logger import logger
from PySide6.QtWidgets import QWidget, QHBoxLayout, QGroupBox, QSizePolicy, QLabel, QGraphicsDropShadowEffect
from PySide6.QtGui import QColor, QPixmap
from PySide6.QtCore import QRect, QPoint, QSize, Slot, Signal
from .forms import (BaseForm, LoginForm, CreateAccountForm, AuthenticateForm, CompanyProfileForm,
                    UserAgreementForm, NewProjectSetupForm, NewProjectExplorerForm, AddSourceExistingProject,
                    TokenPaymentForm, TokenCheckoutStatusForm, ActivateProductKeyForm, UpdateProjectForm)
from pubsub import pub
from src.pyside_util import get_resource_pixmap

# logger = logging.getLogger(__name__)

class FormsView(QWidget):

    sgnSwitchTo = Signal(str, object)

    def __init__(self, parent):
        super().__init__(parent=parent)
        self.setLayout(QHBoxLayout())

        self.formBackground = QLabel()
        self.formBackground.setObjectName("formBackground")
        self.layout().addWidget(self.formBackground)
        self.background = None

        self.group = group = QGroupBox(self)
        self.group.setObjectName("formWidget")
        group.setLayout(QHBoxLayout())

        self.forms = {
            "LoginForm": LoginForm(group),
            "CreateAccountForm": CreateAccountForm(group),
            "AuthenticateForm": AuthenticateForm(group),
            "UserAgreementForm": UserAgreementForm(group),
            "CompanyProfileForm": CompanyProfileForm(group),
            "NewProjectSetupForm": NewProjectSetupForm(group),
            "AddSourceExistingProject": AddSourceExistingProject(group),
            "NewProjectExplorerForm": NewProjectExplorerForm(group),
            "TokenPaymentForm": TokenPaymentForm(group),
            # "TokenCheckoutForm": TokenCheckoutForm(group),
            "TokenCheckoutStatusForm": TokenCheckoutStatusForm(group),
            "ActivateProductKeyForm": ActivateProductKeyForm(group),
            "UpdateProjectForm": UpdateProjectForm(group),
        }
        form: BaseForm
        for _, form in self.forms.items():
            # form.sgnSwitchTo[str].connect(self.switchTo)
            form.sgnSwitchTo[str, dict].connect(self.onSwitchTo)
            form.sgnSwitchTo.connect(lambda x: self.switchTo(x))
            form.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Preferred)

        group.show()
        self.setAutoFillBackground(True)
        self.onSwitchTo("LoginForm")
        pub.subscribe(self.switchTo, "goto-form")

        effect = QGraphicsDropShadowEffect(group)
        effect.setOffset(0, 0)
        effect.setBlurRadius(32)
        effect.setColor(QColor("black"))
        group.setGraphicsEffect(effect)

        # Post connections
        self.forms["UserAgreementForm"].sgnUserAgreementSigned.connect(self.onUserAgreementSigned)
        self.forms["NewProjectExplorerForm"].sgnFilePicked.connect(self.forms["NewProjectSetupForm"].onFilePicked)
        self.forms["NewProjectExplorerForm"].sgnFilePickedExisting.connect(self.forms["AddSourceExistingProject"].onFilePicked)

        pub.subscribe(self.clearNewProjectForms, "clear-new-project-forms")
        pub.subscribe(self.onSetWorkspaceData, "set-workspace-data")

        self.sgnSwitchTo.connect(self.onSwitchTo)

    def onSetWorkspaceData(self, data):
        # Pass data to forms which need it
        projectsData = data["projects"]
        self.forms["AddSourceExistingProject"].setProjectsData(projectsData)

    def resizeEvent(self, event) -> None:
        """ Anchor logo and form to top center """
        geom = self.geometry()
        self.group.show()
        self.group.move((geom.width()//2)-(self.group.width()//2), (geom.height()//2)-self.group.height()//1.9)

        if self.background:
            pix = self.background.copy()
            maxWidth = self.topLevelWidget().width()
            maxHeight = self.topLevelWidget().height()
            bestRatio = min(maxWidth/self.background.width(), maxHeight/self.background.height())
            newWidth = self.background.width() * bestRatio
            newHeight = self.background.height() * bestRatio

            x = (newWidth // 4)
            offset = QPoint(x, 0)
            size = QSize(newWidth, newHeight)
            rect = QRect()
            rect.setSize(size)
            rect.setTopLeft(offset)
            pix = pix.scaled(self.topLevelWidget().size())
            self.formBackground.setPixmap(pix)
        return super().resizeEvent(event)

    def showForm(self, name, params):
        form = self.forms[name]
        # Specify the forms which show the custom background
        if name:# in ["LoginForm"]:
            if not self.background:
                self.background = get_resource_pixmap("BG-Burn Filter.png") #("login-background.png") #
        else:
            self.formBackground.setPixmap(QPixmap())
            self.background = None

        if name == "AuthenticateForm":
            data = self.forms["CreateAccountForm"].getData()
            form.setEmail(data["email"])

        form.setParams(params)
        form.setParent(self.group)
        form.show()
        self.group.layout().addWidget(form)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.group.setFixedSize(form.formSize.width(), form.formSize.height())
        self.resizeEvent(None)

    def switchTo(self, name, params={}):
        self.sgnSwitchTo.emit(name, params)

    @Slot(str)
    @Slot(str, dict)
    def onSwitchTo(self, name, params=None):
        logger.info(f"Form switch request to `{name}`, params={params}")
        # Remove current form
        for form in self.forms.values():
            form.setParent(None)
            form.hide()
        # Switch form or context
        if name == "Workspace":
            pub.sendMessage("goto-workspace-view", name=None)
        elif self.forms.get(name):
            self.showForm(name, params)
        else:
            raise ValueError(name)

    def onUserAgreementSigned(self):
        """ Now retrieve input data from multiple forms and create the user """
        data = {}
        data.update(self.forms["CreateAccountForm"].getData())
        data.update(self.forms["CompanyProfileForm"].getData())
        pub.sendMessage("create-new-user", data=data)

    def clearNewProjectForms(self):
        form: BaseForm
        for f in ["NewProjectSetupForm", "NewProjectExplorerForm"]:
            form = self.forms[f]
            form.sgnInitDefaults.emit()


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    window = FormsView()
    window.setMinimumSize(800, 600)
    window.show()
    app.exec()