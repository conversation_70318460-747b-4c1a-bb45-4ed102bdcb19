"""
Example of efficient multiprocessing with PyMuPDF (fitz).
This demonstrates:
1. Loading the document once per worker process
2. Processing pages in parallel
3. Proper cleanup and error handling
"""
import glob
import fitz
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
from typing import Tuple

# Global variable to store the document in each worker process
_process_doc = None

preload = True # toggle this to compare init times. Note - preload True is faster

def init_worker(pdf_path: str) -> None:
    """Initialize worker process with PDF document.
    This runs once per worker process."""
    global _process_doc
    global preload
    if preload and not _process_doc:
        print("preloading doc", preload)
        _process_doc = fitz.open(pdf_path)

def process_page(path, page_num: int) -> Tuple[int, dict]:
    """Process a single page using the pre-initialized document.
    Returns page number and extracted data."""
    global _process_doc, preload
    if not preload:
        _process_doc = None

    if preload and not _process_doc:
        raise RuntimeError("Document not initialized in worker")
    
    try:
        # Get the page from the pre-loaded document
        page = _process_doc[page_num]

        page.annots()
        
        # Example processing: extract text and some basic page info
        text = page.get_text()
        info = {
            'page_number': page_num + 1,  # 1-based page number
            'width': page.rect.width,
            'height': page.rect.height,
            'rotation': page.rotation,
            'char_count': len(text),
            'word_count': len(text.split())
        }
        return page_num, info
    except Exception as e:
        return page_num, {'error': str(e)}

def process_pdf(pdf_path: str, max_workers: int = None) -> dict:
    """Process PDF document using multiple worker processes.
    
    Args:
        pdf_path: Path to the PDF file
        max_workers: Number of worker processes (default: CPU count)
    
    Returns:
        Dictionary with results for each page
    """

    start = time.perf_counter()
    if max_workers is None:
        max_workers = multiprocessing.cpu_count()

    # Open document briefly to get page count
    doc = fitz.open(pdf_path)
    page_count = len(doc)
    doc.close()
    
    results = {}
    start_time = time.time()
    
    print(f"Processing {page_count} pages with {max_workers} workers...")
    
    with ProcessPoolExecutor(
        max_workers=max_workers,
        initializer=init_worker,
        initargs=(pdf_path,)
    ) as executor:
        # Submit all pages for processing
        futures = [
            executor.submit(process_page, pdf_path,page_num)
            for page_num in range(page_count)
        ]
        
        # Process results as they complete
        for future in as_completed(futures):
            try:
                page_num, info = future.result()
                results[page_num + 1] = info  # Store with 1-based page numbers
            except Exception as e:
                print(f"Error processing page: {str(e)}")
    
    end_time = time.time()
    print(f"Processed {page_count} pages in {end_time - start_time:.2f} seconds")
    
    return results

if __name__ == "__main__":
    # Example usage
    pdf_path = "C:/Drawings/Axis 25 - Combined Sources/axis25-combined.pdf"  # Replace with your PDF path
    
    try:
        results = process_pdf(pdf_path)
        
        # Print results
        print("\nResults:")
        # for page_num in sorted(results.keys()):
        #     info = results[page_num]
        #     if 'error' in info:
        #         print(f"Page {page_num}: Error - {info['error']}")
        #     else:
        #         print(f"Page {page_num}: {info['word_count']} words, "
        #               f"{info['char_count']} characters, "
        #               f"size: {info['width']}x{info['height']}")
    
    except Exception as e:
        print(f"Error processing PDF: {str(e)}")

    print("Note - After multiprocessing _process_doc is still: ", _process_doc)
