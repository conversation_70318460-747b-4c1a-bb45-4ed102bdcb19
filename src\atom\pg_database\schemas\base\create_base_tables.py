
SCHEMA_CLIENTS = """

    CREATE TABLE IF NOT EXISTS public.atem_clients
    (
        id SERIAL PRIMARY KEY,
        client_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
        client_domain VARCHAR(255), -- Domain name for reliably checking if client exists (ex. @domain.com)
        client_prefix VARCHAR(10), -- Prefix used by AIS for project names (ex. Excel USA is 'EXC')
        contact_name character varying(255) COLLATE pg_catalog."default",
        contact_email character varying(255) COLLATE pg_catalog."default",
        contact_phone character varying(50) COLLATE pg_catalog."default",
        address text COLLATE pg_catalog."default",
        created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT atem_clients_pkey PRIMARY KEY (id),
        CONSTRAINT atem_clients_client_name_key UNIQUE (client_name)
    )

    TABLESPACE pg_default;

    ALTER TABLE IF EXISTS public.atem_clients
        OWNER to "<EMAIL>";

    COMMENT ON TABLE public.atem_clients
        IS 'Stores client information including contact details.';

    COMMENT ON COLUMN public.atem_clients.id
        IS 'Unique identifier for each client.';

    COMMENT ON COLUMN public.atem_clients.client_name
        IS 'Name of the client.';

    COMMENT ON COLUMN public.atem_clients.contact_name
        IS 'Name of the contact person.';

    COMMENT ON COLUMN public.atem_clients.contact_email
        IS 'Email of the contact person.';

    COMMENT ON COLUMN public.atem_clients.contact_phone
        IS 'Phone number of the contact person.';

    COMMENT ON COLUMN public.atem_clients.address
        IS 'Address of the client.';

    COMMENT ON COLUMN public.atem_clients.created_at
        IS 'Timestamp when the record was created.';

    COMMENT ON COLUMN public.atem_clients.updated_at
        IS 'Timestamp when the record was last updated.';

    """

SCHEMA_CLIENT_PROFILES = """
    /* SCHEMA_CLIENT_PROFILES */
    /* Stores client profiles and their related information. */

    /* DROP TABLE statement */
    DROP TABLE IF EXISTS public.atem_client_profiles;

    /* Create table: atem_client_profiles */
    CREATE TABLE public.atem_client_profiles (
        id SERIAL PRIMARY KEY,
        ref_id INTEGER REFERENCES public.atem_clients(id) NOT NULL,
        client_name VARCHAR(255) NOT NULL,
        profile_name VARCHAR(255) NOT NULL,
        profile_description TEXT,
        usage_scope VARCHAR(255),
        equivalent_length_method VARCHAR(100),
        area_method VARCHAR(100),
        flange_calculation_method VARCHAR(50),
        pipe_area_method VARCHAR(25) NOT NULL DEFAULT 'lookup',
        pipe_length_method VARCHAR(25) NOT NULL DEFAULT 'lookup',
        pipe_minimum_size INTEGER,
        comments TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    /* Create useful indexes for the client profiles table */
    CREATE INDEX IF NOT EXISTS idx_client_profiles_client_name ON public.atem_client_profiles(client_name);
    CREATE INDEX IF NOT EXISTS idx_client_profiles_profile_name ON public.atem_client_profiles(profile_name);
    CREATE INDEX IF NOT EXISTS idx_client_profiles_client_profile ON public.atem_client_profiles(client_name, profile_name);
    CREATE INDEX IF NOT EXISTS idx_client_profiles_ref_id ON public.atem_client_profiles(ref_id);


    /* Comments for table and columns */
    COMMENT ON TABLE public.atem_client_profiles IS 'Stores client profiles and their related information.';
    COMMENT ON COLUMN public.atem_client_profiles.id IS 'Unique identifier for each client profile.';
    COMMENT ON COLUMN public.atem_client_profiles.ref_id IS 'Foreign key reference to the client in atem_clients table.';
    COMMENT ON COLUMN public.atem_client_profiles.client_name IS 'Name of the client.';
    COMMENT ON COLUMN public.atem_client_profiles.profile_name IS 'Name of the client profile.';
    COMMENT ON COLUMN public.atem_client_profiles.profile_description IS 'Description of the client profile.';
    COMMENT ON COLUMN public.atem_client_profiles.usage_scope IS 'Scope of usage for the client profile.';
    COMMENT ON COLUMN public.atem_client_profiles.equivalent_length_method IS 'Method for equivalent FITTING length calculation.';
    COMMENT ON COLUMN public.atem_client_profiles.area_method IS 'Method for FITTING area calculation.';
    COMMENT ON COLUMN public.atem_client_profiles.flange_calculation_method IS 'Method for flange calculation.';
    COMMENT ON COLUMN public.atem_client_profiles.pipe_area_method IS 'Method for pipe area calculation.';
    COMMENT ON COLUMN public.atem_client_profiles.pipe_length_method IS 'Method for pipe length calculation.';
    COMMENT ON COLUMN public.atem_client_profiles.pipe_minimum_size IS 'Minimum pipe size. Set this to set a minimum size to always use. Example, paint likes to count everything under 3" as 3" pipe OD';
    COMMENT ON COLUMN public.atem_client_profiles.comments IS 'Additional comments related to the client profile.';
    COMMENT ON COLUMN public.atem_client_profiles.created_at IS 'Timestamp when the record was created.';
    COMMENT ON COLUMN public.atem_client_profiles.updated_at IS 'Timestamp when the record was last updated.';
    """

SCHEMA_PROJECTS = """

    CREATE TABLE IF NOT EXISTS public.atem_projects
    (
        id SERIAL PRIMARY KEY,
        client_id integer,
        project_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
        location character varying(255) COLLATE pg_catalog."default",
        jobsite_location character varying(255) COLLATE pg_catalog."default",
        bid_revision character varying(50) COLLATE pg_catalog."default",
        bid_due_date timestamp without time zone,
        received_from_client timestamp without time zone,
        engineering_drafter character varying(100) COLLATE pg_catalog."default",
        ais_project_status character varying(50) COLLATE pg_catalog."default",
        notes text COLLATE pg_catalog."default",
        created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
        profile_id integer,
        CONSTRAINT atem_projects_client_id_fkey FOREIGN KEY (client_id)
            REFERENCES public.atem_clients (id) MATCH SIMPLE
            ON UPDATE NO ACTION
            ON DELETE NO ACTION
    );

    TABLESPACE pg_default;

    ALTER TABLE IF EXISTS public.atem_projects
        OWNER to "<EMAIL>";

    COMMENT ON TABLE public.atem_projects
        IS 'Stores project information.';

    COMMENT ON COLUMN public.atem_projects.id
        IS 'Unique identifier for each project.';

    COMMENT ON COLUMN public.atem_projects.client_id
        IS 'Identifier for the client associated with the project.';

    COMMENT ON COLUMN public.atem_projects.project_name
        IS 'Name of the project.';

    COMMENT ON COLUMN public.atem_projects.location
        IS 'General location of the project.';

    COMMENT ON COLUMN public.atem_projects.jobsite_location
        IS 'Specific jobsite location of the project.';

    COMMENT ON COLUMN public.atem_projects.bid_revision
        IS 'Bid revision version.';

    COMMENT ON COLUMN public.atem_projects.bid_due_date
        IS 'Date when bid is due.';

    COMMENT ON COLUMN public.atem_projects.received_from_client
        IS 'Date when the project was received from client.';

    COMMENT ON COLUMN public.atem_projects.engineering_drafter
        IS 'Engineering drafter assigned to the project.';

    COMMENT ON COLUMN public.atem_projects.ais_project_status
        IS 'Status of the project at AIS.';

    COMMENT ON COLUMN public.atem_projects.notes
        IS 'Additional notes related to the project.';

    COMMENT ON COLUMN public.atem_projects.created_at
        IS 'Timestamp when the record was created.';

    COMMENT ON COLUMN public.atem_projects.updated_at
        IS 'Timestamp when the record was last updated.';
    -- Index: idx_projects_client_id

    -- DROP INDEX IF EXISTS public.idx_projects_client_id;

    CREATE INDEX IF NOT EXISTS idx_projects_client_id
        ON public.atem_projects USING btree
        (client_id ASC NULLS LAST)
        TABLESPACE pg_default;
    """

SCHEMA_GENERAL = """
    --DROP TABLE IF EXISTS public.general;

    CREATE TABLE IF NOT EXISTS public.general (
        id SERIAL PRIMARY KEY,
        sys_filename VARCHAR(255),
        sys_path VARCHAR(1024),
        project_id INTEGER REFERENCES public.atem_projects(id) ON DELETE CASCADE,
        pdf_id INTEGER, /* Manually assigned for now by will go link to pdf storage later */
        pdf_page INTEGER,
        annot_markups TEXT,
        area TEXT,
        avg_elevation VARCHAR(50),
        block_coordinates TEXT,
        client_document_id VARCHAR(100),
        coordinates TEXT,
        design_code VARCHAR(100),
        document_description TEXT,
        document_id VARCHAR(100),
        document_title VARCHAR(255),
        drawing VARCHAR(100),
        elevation TEXT,
        flange_id VARCHAR(100),
        heat_trace VARCHAR(50),
        insulation_spec VARCHAR(100),
        insulation_thickness VARCHAR(50),
        iso_number VARCHAR(100),
        iso_type VARCHAR(100),
        line_number VARCHAR(100),
        max_elevation VARCHAR(50),
        medium_code VARCHAR(100),
        min_elevation VARCHAR(50),
        mod_date VARCHAR(50),
        paint_spec VARCHAR(100),
        pid VARCHAR(100),
        pipe_spec VARCHAR(100),
        pipe_standard VARCHAR(100),
        process_line_list VARCHAR(100),
        process_unit VARCHAR(100),
        project_no VARCHAR(100),
        project_name VARCHAR(255),
        pwht VARCHAR(50),
        revision VARCHAR(50),
        sequence VARCHAR(50),
        service VARCHAR(100),
        sheet VARCHAR(50),
        size DECIMAL(12,3),
        sys_build VARCHAR(100),
        sys_layout_valid VARCHAR(50),
        sys_document VARCHAR(255),
        sys_document_name VARCHAR(255),
        system VARCHAR(100),
        total_sheets VARCHAR(50),
        unit VARCHAR(50),
        vendor_document_id VARCHAR(100),
        weld_id VARCHAR(100),
        weld_class VARCHAR(100),
        x_coord VARCHAR(50),
        xray VARCHAR(50),
        y_coord VARCHAR(50),
        paint_color VARCHAR(100),
        cwp VARCHAR(100),
        pipe_schedule VARCHAR(50),
        wall_thickness DECIMAL,
        pmi_req VARCHAR(75),
        paut_req VARCHAR(75),
        hardness_req VARCHAR(75),
        flange_guard VARCHAR(75),
        continued_on VARCHAR(100),
        connects_to VARCHAR(100),
        pickling_req VARCHAR(75),
        flushing_req VARCHAR(75),
        pipeline_num VARCHAR(100),
        weight DECIMAL;

        
        /* Ancillary */
        length DECIMAL,
        calculated_area DECIMAL,
        calculated_eq_length DECIMAL,
        elbows_90 DECIMAL,
        elbows_45 DECIMAL,
        bevels DECIMAL,
        tees DECIMAL,
        reducers DECIMAL,
        caps DECIMAL,
        flanges DECIMAL,
        valves_flanged DECIMAL,
        valves_welded DECIMAL,
        cut_outs DECIMAL,
        supports DECIMAL,
        bends DECIMAL,
        union_couplings DECIMAL,
        expansion_joints DECIMAL,
        field_welds DECIMAL,


        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

    );
    CREATE INDEX idx_general_project_id ON public.general(project_id);
    CREATE INDEX idx_general_size ON public.general(size);

    COMMENT ON COLUMN public.general.pipe_schedule IS 'Standard pipe schedule designation (e.g., "Schedule 10S", "Schedule 40") assigned from the BOM. Populated by cross-referencing the nominal size of this isometric drawing with corresponding pipe items in the BOM. If no pipe items exist, uses available schedule from other components.';
    COMMENT ON COLUMN public.general.wall_thickness IS 'Actual decimal wall thickness measurement corresponding to the pipe_schedule. Used to provide specific material specifications for each isometric drawing based on the assigned schedule.';


"""

SCHEMA_BOM = """
    DROP TABLE IF EXISTS public.bom;
    -- BOM TABLE
    CREATE TABLE IF NOT EXISTS public.bom (
        id SERIAL PRIMARY KEY,
        sys_path VARCHAR(1024),
        sys_filename VARCHAR(255),
        pdf_id INTEGER,
        pdf_page INTEGER,
        project_id INTEGER REFERENCES public.atem_projects(id) ON DELETE CASCADE,
		profile_id INTEGER REFERENCES public.atem_client_profiles(id),
        rfq_ref_id INTEGER, -- REFERENCES public.atem_rfq(id),
        gen_ref_id INTEGER,

        /* BOM table columns */
        pos VARCHAR(50),
        material_description TEXT,
        normalized_description TEXT,
        component_category VARCHAR(100),
        mtrl_category VARCHAR(100), -- Material category
        size VARCHAR(50),
        size1 DECIMAL(12,3),
        size2 DECIMAL(12,3),
        ident VARCHAR(100),
        item VARCHAR(100),
        tag VARCHAR(100),
        quantity DECIMAL,
        status VARCHAR(50),
        nb VARCHAR(50),
        fluid VARCHAR(100),
        clean_spec VARCHAR(100),
        line_number VARCHAR(100),
        material_code VARCHAR(100),
        sch_class VARCHAR(100),
        parts VARCHAR(100),
        type VARCHAR(100),
        weight DECIMAL,
        number VARCHAR(100),
        remarks TEXT,
        item_count INTEGER,
        item_length  DECIMAL(12,3),
        total_length DECIMAL(12,3),
        shape VARCHAR(100),

        /* Classification/category columns */
        rfq_scope VARCHAR(100), -- RFQ scope category
        general_category VARCHAR(100), -- General categorization for aggregation
        unit_of_measure VARCHAR(50), -- Unit of measure (e.g., inches, feet)
        material VARCHAR(100), -- Material type
        abbreviated_material VARCHAR(50), -- Short form of material type
        technical_standard VARCHAR(50), -- Technical standard (previously ansme_ansi)
        astm VARCHAR(50), -- ASTM standard
        grade VARCHAR(50), -- Grade specification
        rating VARCHAR(50), -- Rating specification
        schedule VARCHAR(50), -- Schedule specification
        coating VARCHAR(50), -- Coating applied to material
        forging VARCHAR(50), -- Forging type
        ends VARCHAR(50), -- Ends specification
        item_tag VARCHAR(100), -- Item tag for reference
        tie_point VARCHAR(100), -- Tie point designation
        pipe_category VARCHAR(50), -- Pipe category
        valve_type VARCHAR(50), -- Valve type category
        fitting_category VARCHAR(50), -- Fitting category
        weld_category VARCHAR(50), -- Weld category
        bolt_category VARCHAR(50), -- Bolt category
        gasket_category VARCHAR(50), -- Gasket category

        /* Miscellaneous columns */
        notes TEXT, -- Additional notes
        deleted BOOLEAN DEFAULT FALSE, -- Flag to indicate deletion status
        ignore_item BOOLEAN DEFAULT FALSE, -- Flag to ignore during aggregations
        validated_date TIMESTAMP, -- Date of validation
        validated_by VARCHAR(100), -- Validator identity

        /* Calculated values */
        calculated_eq_length DECIMAL,
        calculated_area DECIMAL,

		/* Data Record Info */
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
        created_by VARCHAR(100), -- User who created the record
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Record update timestamp
        updated_by VARCHAR(100) -- User who updated the record
    );
    CREATE INDEX idx_bom_project_id ON public.bom(project_id);
    CREATE INDEX idx_bom_material_description ON public.bom(material_description);
    CREATE INDEX idx_bom_size ON public.bom(size);
"""

SCHEMA_TRIGGER_LOGS = """
    -- Update the log function to track the new field
    CREATE OR REPLACE FUNCTION log_mapping_operation()
    RETURNS TRIGGER AS $$
    BEGIN
        INSERT INTO public.trigger_logs (
            trigger_name,
            table_name,
            operation,
            record_id,
            before_values,
            after_values,
            mapping_not_found
        ) VALUES (
            TG_NAME,
            TG_TABLE_NAME,
            TG_OP,
            NEW.id,
            jsonb_build_object(
                'rfq_scope', OLD.rfq_scope,
                'general_category', OLD.general_category
            ),
            jsonb_build_object(
                'rfq_scope', NEW.rfq_scope,
                'general_category', NEW.general_category,
                'changed_component', TG_ARGV[0]
            ),
            NEW.mapping_not_found
        );

        RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;

    -- Add an AFTER trigger to log results (optional)
    CREATE TRIGGER trg_log_rfq_input_mapping
    AFTER UPDATE OF rfq_scope, general_category ON public.atem_rfq_input
    FOR EACH ROW
    EXECUTE FUNCTION log_mapping_operation('component_field');


    -- Modify your function to log to this table
        CREATE OR REPLACE FUNCTION update_categories_from_mapping()
        RETURNS TRIGGER AS $$
        DECLARE
            component_val VARCHAR(100);
            v_profile_id INTEGER;
            v_rfq_scope VARCHAR(100);
            v_general_category VARCHAR(100);
            changed_column VARCHAR(50);
            log_id INTEGER;
            err_message TEXT;
        BEGIN
            -- Insert initial log entry
            INSERT INTO public.trigger_logs (
                trigger_name, table_name, operation, record_id,
                before_values, after_values
            ) VALUES (
                TG_NAME, TG_TABLE_NAME, TG_OP,
                CASE WHEN TG_OP = 'INSERT' THEN NULL ELSE OLD.id END,
                CASE WHEN TG_OP = 'INSERT' THEN NULL
                    ELSE jsonb_build_object(
                        'pipe_category', OLD.pipe_category,
                        'fitting_category', OLD.fitting_category,
                        'gasket_category', OLD.gasket_category,
                        'bolt_category', OLD.bolt_category,
                        'rfq_scope', OLD.rfq_scope,
                        'general_category', OLD.general_category
                    )
                END,
                jsonb_build_object(
                    'pipe_category', NEW.pipe_category,
                    'fitting_category', NEW.fitting_category,
                    'gasket_category', NEW.gasket_category,
                    'bolt_category', NEW.bolt_category,
                    'rfq_scope', NEW.rfq_scope,
                    'general_category', NEW.general_category
                )
            ) RETURNING id INTO log_id;

            -- Regular function execution with error handling
            BEGIN
                -- Determine which column has changed and store its value
                IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND
                    (NEW.pipe_category IS DISTINCT FROM OLD.pipe_category OR
                    NEW.fitting_category IS DISTINCT FROM OLD.fitting_category OR
                    NEW.gasket_category IS DISTINCT FROM OLD.gasket_category OR
                    NEW.bolt_category IS DISTINCT FROM OLD.bolt_category)) THEN

                    -- Determine which component field changed and get its value
                    IF NEW.pipe_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.pipe_category IS DISTINCT FROM OLD.pipe_category) THEN
                        component_val := NEW.pipe_category;
                        changed_column := 'pipe_category';
                    ELSIF NEW.fitting_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.fitting_category IS DISTINCT FROM OLD.fitting_category) THEN
                        component_val := NEW.fitting_category;
                        changed_column := 'fitting_category';
                    ELSIF NEW.gasket_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.gasket_category IS DISTINCT FROM OLD.gasket_category) THEN
                        component_val := NEW.gasket_category;
                        changed_column := 'gasket_category';
                    ELSIF NEW.bolt_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.bolt_category IS DISTINCT FROM OLD.bolt_category) THEN
                        component_val := NEW.bolt_category;
                        changed_column := 'bolt_category';
                    ELSE
                        -- No relevant change or no value to map
                        RETURN NEW;
                    END IF;

                    -- For atem_rfq_input, get profile_id from projects table
                    IF TG_TABLE_NAME = 'atem_rfq_input' THEN
                        SELECT profile_id INTO v_profile_id
                        FROM public.atem_projects
                        WHERE id = NEW.project_id;
                    ELSE
                        v_profile_id := NEW.profile_id;
                    END IF;

                    -- Look up rfq_scope and general_category from component mapping
                    IF v_profile_id IS NOT NULL AND component_val IS NOT NULL THEN
                        SELECT takeoff_category, general_category INTO v_rfq_scope, v_general_category
                        FROM public.atem_bom_component_mapping
                        WHERE profile_id = v_profile_id AND component_name = component_val
                        LIMIT 1;

                        -- Update the categories only if mapping was found
                        IF v_rfq_scope IS NOT NULL OR v_general_category IS NOT NULL THEN
                            NEW.rfq_scope := v_rfq_scope;
                            NEW.general_category := v_general_category;

                            -- Update log entry with final values
                            UPDATE public.trigger_logs
                            SET after_values = jsonb_build_object(
                                'pipe_category', NEW.pipe_category,
                                'fitting_category', NEW.fitting_category,
                                'gasket_category', NEW.gasket_category,
                                'bolt_category', NEW.bolt_category,
                                'rfq_scope', NEW.rfq_scope,
                                'general_category', NEW.general_category,
                                'component_val', component_val,
                                'changed_column', changed_column,
                                'profile_id', v_profile_id,
                                'mapping_found', TRUE
                            )
                            WHERE id = log_id;
                        ELSE
                            -- Update log entry noting no mapping found
                            UPDATE public.trigger_logs
                            SET after_values = jsonb_build_object(
                                'component_val', component_val,
                                'changed_column', changed_column,
                                'profile_id', v_profile_id,
                                'mapping_found', FALSE
                            )
                            WHERE id = log_id;
                        END IF;
                    ELSE
                        -- Update log entry with missing data
                        UPDATE public.trigger_logs
                        SET after_values = jsonb_build_object(
                            'component_val', component_val,
                            'changed_column', changed_column,
                            'profile_id', v_profile_id,
                            'project_id', NEW.project_id,
                            'error', 'Missing profile_id or component_val'
                        )
                        WHERE id = log_id;
                    END IF;
                END IF;

                EXCEPTION WHEN OTHERS THEN
                    GET STACKED DIAGNOSTICS err_message = MESSAGE_TEXT;
                    UPDATE public.trigger_logs
                    SET error_message = err_message
                    WHERE id = log_id;
            END;

            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
"""

SCHEMA_VALIDATED_MATERIALS = """
    DROP TABLE IF EXISTS public.verified_material_classifications;

    CREATE TABLE public.verified_material_classifications (
        id SERIAL PRIMARY KEY,
        material_description TEXT NOT NULL,
        normalized_description TEXT,
        rfq_scope CHARACTER VARYING,
        general_category CHARACTER VARYING,
        fitting_category CHARACTER VARYING,
        pipe_category CHARACTER VARYING,
        bolt_category CHARACTER VARYING,
        gasket_category CHARACTER VARYING,
        valve_type CHARACTER VARYING,
        weld_category CHARACTER VARYING,
        forging CHARACTER VARYING,
        coating CHARACTER VARYING,
        ends CHARACTER VARYING,
        material CHARACTER VARYING,
        abbreviated_material CHARACTER VARYING,
        grade CHARACTER VARYING,
        technical_standard CHARACTER VARYING,
        astm CHARACTER VARYING,
        schedule CHARACTER VARYING,
        rating CHARACTER VARYING,
        unit_of_measure CHARACTER VARYING,
        tie_point CHARACTER VARYING,
        item_tag CHARACTER VARYING,
        notes TEXT,
        created_by CHARACTER VARYING,
        updated_by CHARACTER VARYING,
        validated_by CHARACTER VARYING,
        validated_date TIMESTAMP WITHOUT TIME ZONE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- Add a unique constraint to prevent duplicates based on material_description
        CONSTRAINT unique_material_description UNIQUE (material_description)
    );

    -- Add an index on material_description for faster lookups
    CREATE INDEX idx_verified_material_desc ON public.verified_material_classifications(material_description);

    -- Add an index on normalized_description for faster lookups
    CREATE INDEX idx_verified_normalized_desc ON public.verified_material_classifications(normalized_description);

"""

FUNC_AGG_GENERAL_BY_SIZE = """
    -- USAGE
    -- SELECT * FROM preview_bom_to_general_aggregation(1)
    -- ORDER BY pdf_page, size;

    DROP FUNCTION preview_bom_to_general_aggregation(integer);

    CREATE OR REPLACE FUNCTION preview_bom_to_general_aggregation(p_project_id INTEGER)
    RETURNS TABLE (
        id INTEGER,
        pdf_id INTEGER,
        pdf_page INTEGER,
        size DECIMAL(12,3),
        length DECIMAL,
        elbows_90 DECIMAL,
        elbows_45 DECIMAL,
        bevels DECIMAL,
        tees DECIMAL,
        reducers DECIMAL,
        caps DECIMAL,
        flanges DECIMAL,
        valves_flanged DECIMAL,
        valves_welded DECIMAL,
        cut_outs DECIMAL,
        supports DECIMAL,
        bends DECIMAL,
        union_couplings DECIMAL,
        expansion_joints DECIMAL,
        field_welds DECIMAL,
        calculated_eq_length DECIMAL,
        calculated_area DECIMAL,
        change_type TEXT
    ) AS $$
    DECLARE
        v_changes INTEGER;
        v_row RECORD;
        v_template_id INTEGER;
        v_has_non_zero BOOLEAN;
    BEGIN
        -- Drop temporary tables if they exist
        DROP TABLE IF EXISTS temp_general;
        DROP TABLE IF EXISTS bom_agg;
        
        -- Create a temp copy of the general table for our project
        CREATE TEMP TABLE temp_general ON COMMIT DROP AS
        SELECT g.* FROM public.general g WHERE g.project_id = p_project_id;
        
        -- Add change_type column
        ALTER TABLE temp_general ADD COLUMN change_type TEXT DEFAULT 'unchanged';
        
        -- Create table with BOM aggregations by PDF and size
        CREATE TEMP TABLE bom_agg AS
        WITH mapping AS (
            SELECT DISTINCT m.general_category, m.map_to_gen 
            FROM public.atem_bom_component_mapping m
            WHERE m.profile_id = (
                SELECT ap.profile_id FROM public.atem_projects ap WHERE ap.id = p_project_id
            )
            AND m.map_to_gen IS NOT NULL
            AND m.general_category IS NOT NULL
        )
        SELECT 
            b.pdf_id,
            b.pdf_page,
            COALESCE(b.size1, b.size2) AS size,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'length'
            ) THEN b.quantity ELSE 0 END) AS length,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'elbows_90'
            ) THEN b.quantity ELSE 0 END) AS elbows_90,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'elbows_45'
            ) THEN b.quantity ELSE 0 END) AS elbows_45,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'bevels'
            ) THEN b.quantity ELSE 0 END) AS bevels,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'tees'
            ) THEN b.quantity ELSE 0 END) AS tees,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'reducers'
            ) THEN b.quantity ELSE 0 END) AS reducers,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'caps'
            ) THEN b.quantity ELSE 0 END) AS caps,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'flanges'
            ) THEN b.quantity ELSE 0 END) AS flanges,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'valves_flanged'
            ) THEN b.quantity ELSE 0 END) AS valves_flanged,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'valves_welded'
            ) THEN b.quantity ELSE 0 END) AS valves_welded,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'cut_outs'
            ) THEN b.quantity ELSE 0 END) AS cut_outs,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'supports'
            ) THEN b.quantity ELSE 0 END) AS supports,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'bends'
            ) THEN b.quantity ELSE 0 END) AS bends,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'union_couplings'
            ) THEN b.quantity ELSE 0 END) AS union_couplings,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'expansion_joints'
            ) THEN b.quantity ELSE 0 END) AS expansion_joints,
            SUM(CASE WHEN EXISTS (
                SELECT 1 FROM mapping m 
                WHERE m.general_category = b.general_category 
                AND m.map_to_gen = 'field_welds'
            ) THEN b.quantity ELSE 0 END) AS field_welds,
            SUM(COALESCE(b.calculated_eq_length, 0)) AS calculated_eq_length,
            SUM(COALESCE(b.calculated_area, 0)) AS calculated_area
        FROM 
            public.bom b
        WHERE 
            b.project_id = p_project_id
        GROUP BY 
            b.pdf_id, b.pdf_page, COALESCE(b.size1, b.size2);
        
        -- Update existing rows in general table
        UPDATE temp_general tg
        SET
            length = ba.length,
            elbows_90 = ba.elbows_90,
            elbows_45 = ba.elbows_45,
            bevels = ba.bevels,
            tees = ba.tees,
            reducers = ba.reducers,
            caps = ba.caps,
            flanges = ba.flanges,
            valves_flanged = ba.valves_flanged,
            valves_welded = ba.valves_welded,
            cut_outs = ba.cut_outs,
            supports = ba.supports,
            bends = ba.bends,
            union_couplings = ba.union_couplings,
            expansion_joints = ba.expansion_joints,
            field_welds = ba.field_welds,
            calculated_eq_length = ba.calculated_eq_length,
            calculated_area = ba.calculated_area,
            change_type = 'updated'
        FROM 
            bom_agg ba
        WHERE 
            tg.pdf_id = ba.pdf_id AND
            tg.pdf_page = ba.pdf_page AND
            tg.size = ba.size;
        
        -- Find combinations that need new rows
        FOR v_row IN 
            SELECT ba.pdf_id, ba.pdf_page, ba.size
            FROM bom_agg ba
            WHERE NOT EXISTS (
                SELECT 1 
                FROM temp_general tg
                WHERE tg.pdf_id = ba.pdf_id AND
                    tg.pdf_page = ba.pdf_page AND
                    tg.size = ba.size
            )
        LOOP
            -- Check if we have at least one non-zero value in the aggregation
            SELECT 
                (ba.length > 0 OR ba.elbows_90 > 0 OR ba.elbows_45 > 0 OR 
                ba.bevels > 0 OR ba.tees > 0 OR ba.reducers > 0 OR 
                ba.caps > 0 OR ba.flanges > 0 OR ba.valves_flanged > 0 OR 
                ba.valves_welded > 0 OR ba.cut_outs > 0 OR ba.supports > 0 OR 
                ba.bends > 0 OR ba.union_couplings > 0 OR ba.expansion_joints > 0 OR 
                ba.field_welds > 0 OR ba.calculated_eq_length > 0 OR ba.calculated_area > 0)
            INTO v_has_non_zero
            FROM bom_agg ba
            WHERE ba.pdf_id = v_row.pdf_id AND ba.pdf_page = v_row.pdf_page AND ba.size = v_row.size;
            
            -- Only proceed if we have at least one non-zero value
            IF v_has_non_zero THEN
                -- Find a template to use from the same PDF and page
                SELECT tg.id INTO v_template_id
                FROM temp_general tg
                WHERE tg.pdf_id = v_row.pdf_id AND tg.pdf_page = v_row.pdf_page
                LIMIT 1;
                
                -- If no template found for this page, use one from the same PDF
                IF v_template_id IS NULL THEN
                    SELECT tg.id INTO v_template_id
                    FROM temp_general tg
                    WHERE tg.pdf_id = v_row.pdf_id
                    LIMIT 1;
                END IF;
                
                -- If we found a template, create a new row
                IF v_template_id IS NOT NULL THEN
                    INSERT INTO temp_general (
                        project_id, pdf_id, pdf_page, size,
                        -- Metadata fields from template
                        annot_markups, area, avg_elevation, block_coordinates, client_document_id,
                        coordinates, design_code, document_description, document_id, document_title,
                        drawing, elevation, flange_id, heat_trace, insulation_spec, insulation_thickness,
                        iso_number, iso_type, line_number, max_elevation, medium_code, min_elevation,
                        mod_date, paint_spec, pid, pipe_spec, pipe_standard, process_line_list,
                        process_unit, project_no, project_name, pwht, revision, sequence, service,
                        sheet, sys_build, sys_layout_valid, sys_document, sys_document_name,
                        sys_filename, sys_path, system, total_sheets, unit, vendor_document_id,
                        weld_id, weld_class, x_coord, xray, y_coord, paint_color, cwp,
                        -- Quantity fields from BOM aggregation
                        length, elbows_90, elbows_45, bevels, tees, reducers, caps, flanges,
                        valves_flanged, valves_welded, cut_outs, supports, bends,
                        union_couplings, expansion_joints, field_welds,
                        calculated_eq_length, calculated_area,
                        -- Timestamps and change tracking
                        created_at, updated_at, change_type
                    )
                    SELECT
                        tg.project_id, v_row.pdf_id, v_row.pdf_page, v_row.size,
                        -- Metadata fields from template
                        tg.annot_markups, tg.area, tg.avg_elevation, tg.block_coordinates, tg.client_document_id,
                        tg.coordinates, tg.design_code, tg.document_description, tg.document_id, tg.document_title,
                        tg.drawing, tg.elevation, tg.flange_id, tg.heat_trace, tg.insulation_spec, tg.insulation_thickness,
                        tg.iso_number, tg.iso_type, tg.line_number, tg.max_elevation, tg.medium_code, tg.min_elevation,
                        tg.mod_date, tg.paint_spec, tg.pid, tg.pipe_spec, tg.pipe_standard, tg.process_line_list,
                        tg.process_unit, tg.project_no, tg.project_name, tg.pwht, tg.revision, tg.sequence, tg.service,
                        tg.sheet, tg.sys_build, tg.sys_layout_valid, tg.sys_document, tg.sys_document_name,
                        tg.sys_filename, tg.sys_path, tg.system, tg.total_sheets, tg.unit, tg.vendor_document_id,
                        tg.weld_id, tg.weld_class, tg.x_coord, tg.xray, tg.y_coord, tg.paint_color, tg.cwp,
                        -- Quantity fields from BOM aggregation
                        ba.length, ba.elbows_90, ba.elbows_45, ba.bevels, ba.tees, ba.reducers, ba.caps, ba.flanges,
                        ba.valves_flanged, ba.valves_welded, ba.cut_outs, ba.supports, ba.bends,
                        ba.union_couplings, ba.expansion_joints, ba.field_welds,
                        ba.calculated_eq_length, ba.calculated_area,
                        -- Timestamps and change tracking
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'inserted'::TEXT
                    FROM
                        temp_general tg
                        JOIN bom_agg ba ON ba.pdf_id = v_row.pdf_id AND ba.pdf_page = v_row.pdf_page AND ba.size = v_row.size
                    WHERE
                        tg.id = v_template_id;
                END IF;
            END IF;
        END LOOP;
        
        -- Count changes
        SELECT COUNT(*) INTO v_changes FROM temp_general WHERE temp_general.change_type != 'unchanged';
        
        -- Return results
        IF v_changes = 0 THEN
            RETURN QUERY
            SELECT 
                tg.id, tg.pdf_id, tg.pdf_page, tg.size, tg.length,
                tg.elbows_90, tg.elbows_45, tg.bevels, tg.tees, 
                tg.reducers, tg.caps, tg.flanges, tg.valves_flanged, tg.valves_welded, 
                tg.cut_outs, tg.supports, tg.bends, tg.union_couplings, 
                tg.expansion_joints, tg.field_welds, tg.calculated_eq_length, 
                tg.calculated_area, 'no_changes_found'::TEXT as change_type
            FROM 
                temp_general tg
            LIMIT 10;
        ELSE
            RETURN QUERY
            SELECT 
                tg.id, tg.pdf_id, tg.pdf_page, tg.size, tg.length,
                tg.elbows_90, tg.elbows_45, tg.bevels, tg.tees, 
                tg.reducers, tg.caps, tg.flanges, tg.valves_flanged, tg.valves_welded, 
                tg.cut_outs, tg.supports, tg.bends, tg.union_couplings, 
                tg.expansion_joints, tg.field_welds, tg.calculated_eq_length, 
                tg.calculated_area, tg.change_type
            FROM 
                temp_general tg
            WHERE 
                tg.change_type != 'unchanged'
            ORDER BY
                tg.pdf_id, tg.pdf_page, tg.size;
        END IF;
    END;
    $$ LANGUAGE plpgsql;
"""

FUNC_PREVIEW_OR_MERGE_GENERAL_TEMP = """

    -- Just preview (equivalent to your original query)
    --SELECT * FROM manage_bom_to_general_aggregation(1, FALSE)
    --ORDER BY pdf_page, size;

    -- Preview and execute the changes
    -- SELECT * FROM manage_bom_to_general_aggregation(1, TRUE)
    -- ORDER BY pdf_page, size;
    
    DECLARE
        v_changes INTEGER;
        v_result RECORD;
        v_existing_id INTEGER;
    BEGIN
        -- First, always preview the changes
        FOR v_result IN SELECT * FROM preview_bom_to_general_aggregation(p_project_id)
        LOOP
            -- Add an additional column for execution status
            action_taken := CASE WHEN p_execute THEN 'Executed' ELSE 'Preview only' END;
            
            -- Return row to output
            id := v_result.id;
            pdf_id := v_result.pdf_id;
            pdf_page := v_result.pdf_page;
            size := v_result.size;
            length := v_result.length;
            elbows_90 := v_result.elbows_90;
            elbows_45 := v_result.elbows_45;
            bevels := v_result.bevels;
            tees := v_result.tees;
            reducers := v_result.reducers;
            caps := v_result.caps;
            flanges := v_result.flanges;
            valves_flanged := v_result.valves_flanged;
            valves_welded := v_result.valves_welded;
            cut_outs := v_result.cut_outs;
            supports := v_result.supports;
            bends := v_result.bends;
            union_couplings := v_result.union_couplings;
            expansion_joints := v_result.expansion_joints;
            field_welds := v_result.field_welds;
            calculated_eq_length := v_result.calculated_eq_length;
            calculated_area := v_result.calculated_area;
            change_type := v_result.change_type;
            
            -- If execute flag is true, apply the changes
            IF p_execute THEN
                IF v_result.change_type = 'inserted' THEN
                    -- Check if there's an existing record with same pdf_id and pdf_page but NULL/0 size
                    SELECT g.id INTO v_existing_id
                    FROM public.general g
                    WHERE g.project_id = p_project_id
                    AND g.pdf_id = v_result.pdf_id
                    AND g.pdf_page = v_result.pdf_page
                    AND (g.size IS NULL OR g.size = 0);
                    
                    IF v_existing_id IS NOT NULL THEN
                        -- If found, update the existing record instead of inserting
                        UPDATE public.general g
                        SET
                            size = v_result.size,
                            length = v_result.length,
                            elbows_90 = v_result.elbows_90,
                            elbows_45 = v_result.elbows_45,
                            bevels = v_result.bevels,
                            tees = v_result.tees,
                            reducers = v_result.reducers,
                            caps = v_result.caps,
                            flanges = v_result.flanges,
                            valves_flanged = v_result.valves_flanged,
                            valves_welded = v_result.valves_welded,
                            cut_outs = v_result.cut_outs,
                            supports = v_result.supports,
                            bends = v_result.bends,
                            union_couplings = v_result.union_couplings,
                            expansion_joints = v_result.expansion_joints,
                            field_welds = v_result.field_welds,
                            calculated_eq_length = v_result.calculated_eq_length,
                            calculated_area = v_result.calculated_area,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE 
                            g.id = v_existing_id;
                            
                        -- Update the action_taken message
                        action_taken := 'Updated existing record with NULL/zero size';
                    ELSE
                        -- No matching record with NULL/0 size found, perform normal insert
                        INSERT INTO public.general (
                            project_id, pdf_id, pdf_page, size, length,
                            elbows_90, elbows_45, bevels, tees, reducers, 
                            caps, flanges, valves_flanged, valves_welded, cut_outs, 
                            supports, bends, union_couplings, expansion_joints, field_welds,
                            calculated_eq_length, calculated_area
                        ) VALUES (
                            p_project_id, v_result.pdf_id, v_result.pdf_page, v_result.size, v_result.length,
                            v_result.elbows_90, v_result.elbows_45, v_result.bevels, v_result.tees, v_result.reducers, 
                            v_result.caps, v_result.flanges, v_result.valves_flanged, v_result.valves_welded, v_result.cut_outs, 
                            v_result.supports, v_result.bends, v_result.union_couplings, v_result.expansion_joints, v_result.field_welds,
                            v_result.calculated_eq_length, v_result.calculated_area
                        );
                    END IF;
                ELSIF v_result.change_type = 'updated' THEN
                    UPDATE public.general g
                    SET
                        length = v_result.length,
                        elbows_90 = v_result.elbows_90,
                        elbows_45 = v_result.elbows_45,
                        bevels = v_result.bevels,
                        tees = v_result.tees,
                        reducers = v_result.reducers,
                        caps = v_result.caps,
                        flanges = v_result.flanges,
                        valves_flanged = v_result.valves_flanged,
                        valves_welded = v_result.valves_welded,
                        cut_outs = v_result.cut_outs,
                        supports = v_result.supports,
                        bends = v_result.bends,
                        union_couplings = v_result.union_couplings,
                        expansion_joints = v_result.expansion_joints,
                        field_welds = v_result.field_welds,
                        calculated_eq_length = v_result.calculated_eq_length,
                        calculated_area = v_result.calculated_area,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE 
                        g.id = v_result.id;
                END IF;
            END IF;
            
            RETURN NEXT;
        END LOOP;
        
        -- Log status
        IF p_execute THEN
            RAISE NOTICE 'Changes applied to public.general table for project %', p_project_id;
        ELSE
            RAISE NOTICE 'Preview only - no changes applied to public.general table';
        END IF;
    END;

"""

FUNC_UPDATE_RFQ_QUANTITIES = """

    DECLARE
        v_new_rfq_id INTEGER := NULL;
        v_old_rfq_id INTEGER := NULL;
        v_new_quantity NUMERIC;
        in_progress BOOLEAN;
    BEGIN
        -- Check for recursion
        BEGIN
            SELECT current_setting('bom_quantity_update_in_progress', TRUE)::BOOLEAN INTO in_progress;
        EXCEPTION WHEN OTHERS THEN
            in_progress := FALSE;
        END;
        
        IF in_progress THEN
            RAISE NOTICE 'Update already in progress, skipping to avoid recursion';
            IF TG_OP = 'DELETE' THEN
                RETURN OLD;
            ELSE
                RETURN NEW;
            END IF;
        END IF;
        
        PERFORM set_config('bom_quantity_update_in_progress', 'true', TRUE);
        
        BEGIN
            -- Determine which RFQ IDs are affected
            IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
                v_new_rfq_id := NEW.rfq_ref_id;
            END IF;
            
            IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
                v_old_rfq_id := OLD.rfq_ref_id;
            END IF;
            
            -- Update new RFQ ID if it exists
            IF v_new_rfq_id IS NOT NULL THEN
                -- Use NUMERIC type to preserve decimal precision
                SELECT COALESCE(SUM(quantity), 0)::NUMERIC(15,4) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_new_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_new_rfq_id, v_new_quantity;
                
                -- Directly update with the EXACT value, no rounding
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_new_rfq_id;
            END IF;
            
            -- Update old RFQ ID if it exists and is different from new
            IF v_old_rfq_id IS NOT NULL AND v_old_rfq_id IS DISTINCT FROM v_new_rfq_id THEN
                SELECT COALESCE(SUM(quantity), 0)::NUMERIC(15,4) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_old_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_old_rfq_id, v_new_quantity;
                
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_old_rfq_id;
            END IF;
            
            PERFORM set_config('bom_quantity_update_in_progress', 'false', TRUE);
            
            EXCEPTION WHEN OTHERS THEN
                PERFORM set_config('bom_quantity_update_in_progress', 'false', TRUE);
                RAISE;
        END;
        
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            RETURN NEW;
        END IF;
    END;
"""

FUNC_UPDATE_RFQ_QUANTITIES_UNTESTED = """
    CREATE OR REPLACE FUNCTION update_rfq_quantities() RETURNS TRIGGER AS $$
    DECLARE
        v_new_rfq_id INTEGER := NULL;
        v_old_rfq_id INTEGER := NULL;
        v_new_quantity NUMERIC;
        in_progress BOOLEAN := FALSE;
    BEGIN
        -- Create temp table if it doesn't exist (will exist only for this session)
        CREATE TEMP TABLE IF NOT EXISTS bom_quantity_update_status (
            is_in_progress BOOLEAN PRIMARY KEY
        );
        
        -- Try to insert a row indicating we're in progress
        BEGIN
            INSERT INTO bom_quantity_update_status VALUES (TRUE);
            in_progress := FALSE; -- We succeeded, so we're not already in progress
        EXCEPTION WHEN unique_violation THEN
            -- Row already exists, we're in a recursive call
            in_progress := TRUE;
        END;
        
        IF in_progress THEN
            RAISE NOTICE 'Update already in progress, skipping to avoid recursion';
            IF TG_OP = 'DELETE' THEN
                RETURN OLD;
            ELSE
                RETURN NEW;
            END IF;
        END IF;
        
        BEGIN
            -- Determine which RFQ IDs are affected
            IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
                v_new_rfq_id := NEW.rfq_ref_id;
            END IF;
            
            IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
                v_old_rfq_id := OLD.rfq_ref_id;
            END IF;
            
            -- Update new RFQ ID if it exists
            IF v_new_rfq_id IS NOT NULL THEN
                -- Use NUMERIC type to preserve decimal precision
                SELECT COALESCE(SUM(quantity), 0)::NUMERIC(15,4) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_new_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_new_rfq_id, v_new_quantity;
                
                -- Directly update with the EXACT value, no rounding
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_new_rfq_id;
            END IF;
            
            -- Update old RFQ ID if it exists and is different from new
            IF v_old_rfq_id IS NOT NULL AND v_old_rfq_id IS DISTINCT FROM v_new_rfq_id THEN
                SELECT COALESCE(SUM(quantity), 0)::NUMERIC(15,4) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_old_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_old_rfq_id, v_new_quantity;
                
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_old_rfq_id;
            END IF;
            
            -- Clean up
            DELETE FROM bom_quantity_update_status;
            
            EXCEPTION WHEN OTHERS THEN
                -- Clean up in case of error
                DELETE FROM bom_quantity_update_status;
                RAISE;
        END;
        
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            RETURN NEW;
        END IF;
    END;
    $$ LANGUAGE plpgsql;
"""

FUNC_MANAGE_BOM_TO_GENERAL_AGGREGATION = """
    -- Create the missing manage_bom_to_general_aggregation function
    -- This function is called by other functions but was missing from the codebase

    DROP FUNCTION IF EXISTS manage_bom_to_general_aggregation(integer, boolean);

    CREATE OR REPLACE FUNCTION manage_bom_to_general_aggregation(p_project_id INTEGER, p_execute BOOLEAN)
    RETURNS TABLE (
        pdf_id INTEGER,
        pdf_page INTEGER,
        size DECIMAL(12,3),
        length DECIMAL,
        elbows_90 DECIMAL,
        elbows_45 DECIMAL,
        bevels DECIMAL,
        tees DECIMAL,
        reducers DECIMAL,
        caps DECIMAL,
        flanges DECIMAL,
        valves_flanged DECIMAL,
        valves_welded DECIMAL,
        cut_outs DECIMAL,
        supports DECIMAL,
        bends DECIMAL,
        union_couplings DECIMAL,
        expansion_joints DECIMAL,
        field_welds DECIMAL,
        calculated_eq_length DECIMAL,
        calculated_area DECIMAL,
        stress_req VARCHAR(50)
    ) AS $$
    BEGIN
        -- This function wraps preview_bom_to_general_aggregation and returns only the aggregated data
        -- The p_execute parameter is ignored for now (preview mode only)
        RETURN QUERY
        SELECT
            p.pdf_id,
            p.pdf_page,
            p.size,
            p.length,
            p.elbows_90,
            p.elbows_45,
            p.bevels,
            p.tees,
            p.reducers,
            p.caps,
            p.flanges,
            p.valves_flanged,
            p.valves_welded,
            p.cut_outs,
            p.supports,
            p.bends,
            p.union_couplings,
            p.expansion_joints,
            p.field_welds,
            p.calculated_eq_length,
            p.calculated_area,
            NULL::VARCHAR(50) AS stress_req  -- Add stress_req column as NULL for now
        FROM preview_bom_to_general_aggregation(p_project_id) p
        WHERE p.change_type != 'no_changes_found';
    END;
    $$ LANGUAGE plpgsql;
"""

FUNC_GET_AGGREGATED_GENERAL_DATA = """
    -- SELECT * FROM report_general_with_length(6);

    DROP FUNCTION IF EXISTS report_general_with_length(integer);
    CREATE OR REPLACE FUNCTION report_general_with_length(p_project_id INTEGER)
    RETURNS TABLE (
        -- All columns from the general table - EXACT MATCHES ONLY
        id INTEGER,
        project_id INTEGER,
        pdf_id INTEGER,
        annot_markups TEXT,
        area TEXT,
        avg_elevation VARCHAR,
        block_coordinates TEXT,
        client_document_id VARCHAR,
        coordinates TEXT,
        design_code VARCHAR,
        document_description TEXT,
        document_id VARCHAR,
        document_title VARCHAR,
        drawing VARCHAR,
        elevation VARCHAR,
        flange_id VARCHAR,
        heat_trace VARCHAR,
        insulation_spec VARCHAR,
        insulation_thickness VARCHAR,
        iso_number VARCHAR,
        iso_type VARCHAR,
        line_number VARCHAR,
        max_elevation VARCHAR,
        medium_code VARCHAR,
        min_elevation VARCHAR,
        mod_date VARCHAR,
        paint_spec VARCHAR,
        pid VARCHAR,
        pipe_spec VARCHAR,
        pipe_standard VARCHAR,
        process_line_list VARCHAR,
        process_unit VARCHAR,
        project_no VARCHAR,
        project_name VARCHAR,
        pwht VARCHAR,
        revision VARCHAR,
        sequence VARCHAR,
        service VARCHAR,
        sheet VARCHAR,
        size DECIMAL,
        sys_build VARCHAR,
        sys_layout_valid VARCHAR,
        sys_document VARCHAR,
        sys_document_name VARCHAR,
        sys_filename VARCHAR,
        sys_path VARCHAR,
        system VARCHAR,
        total_sheets VARCHAR,
        unit VARCHAR,
        vendor_document_id VARCHAR,
        weld_id VARCHAR,
        weld_class VARCHAR,
        x_coord VARCHAR,
        xray VARCHAR,
        y_coord VARCHAR,
        paint_color VARCHAR,
        cwp VARCHAR,
        length DECIMAL,
        calculated_area DECIMAL,
        calculated_eq_length DECIMAL,
        elbows_90 DECIMAL,
        elbows_45 DECIMAL,
        bevels DECIMAL,
        tees DECIMAL,
        reducers DECIMAL,
        caps DECIMAL,
        flanges DECIMAL,
        valves_flanged DECIMAL,
        valves_welded DECIMAL,
        cut_outs DECIMAL,
        supports DECIMAL,
        bends DECIMAL,
        union_couplings DECIMAL,
        expansion_joints DECIMAL,
        field_welds DECIMAL,
        created_at TIMESTAMP WITHOUT TIME ZONE,
        updated_at TIMESTAMP WITHOUT TIME ZONE,
        pdf_page INTEGER
    ) AS $$
    BEGIN
        RETURN QUERY
        WITH 
        length_data AS (
            SELECT 
                m.pdf_id AS ld_pdf_id,
                m.pdf_page AS ld_pdf_page,
                m.size AS ld_size,
                m.length AS ld_length,
                m.elbows_90 AS ld_elbows_90,
                m.elbows_45 AS ld_elbows_45,
                m.bevels AS ld_bevels,
                m.tees AS ld_tees,
                m.reducers AS ld_reducers,
                m.caps AS ld_caps,
                m.flanges AS ld_flanges,
                m.valves_flanged AS ld_valves_flanged,
                m.valves_welded AS ld_valves_welded,
                m.cut_outs AS ld_cut_outs,
                m.supports AS ld_supports,
                m.bends AS ld_bends,
                m.union_couplings AS ld_union_couplings,
                m.expansion_joints AS ld_expansion_joints,
                m.field_welds AS ld_field_welds,
                m.calculated_eq_length AS ld_calculated_eq_length,
                m.calculated_area AS ld_calculated_area
            FROM manage_bom_to_general_aggregation(p_project_id, FALSE) AS m
        )
        SELECT 
            -- All columns from general table - EXACT MATCHES ONLY
            g.id,
            g.project_id,
            g.pdf_id,
            g.annot_markups,
            g.area,
            g.avg_elevation,
            g.block_coordinates,
            g.client_document_id,
            g.coordinates,
            g.design_code,
            g.document_description,
            g.document_id,
            g.document_title,
            g.drawing,
            g.elevation,
            g.flange_id,
            g.heat_trace,
            g.insulation_spec,
            g.insulation_thickness,
            g.iso_number,
            g.iso_type,
            g.line_number,
            g.max_elevation,
            g.medium_code,
            g.min_elevation,
            g.mod_date,
            g.paint_spec,
            g.pid,
            g.pipe_spec,
            g.pipe_standard,
            g.process_line_list,
            g.process_unit,
            g.project_no,
            g.project_name,
            g.pwht,
            g.revision,
            g.sequence,
            g.service,
            g.sheet,
            g.size,
            g.sys_build,
            g.sys_layout_valid,
            g.sys_document,
            g.sys_document_name,
            g.sys_filename,
            g.sys_path,
            g.system,
            g.total_sheets,
            g.unit,
            g.vendor_document_id,
            g.weld_id,
            g.weld_class,
            g.x_coord,
            g.xray,
            g.y_coord,
            g.paint_color,
            g.cwp,
            -- Replace existing numeric values with calculated ones if available
            ld.ld_length,
            COALESCE(ld.ld_calculated_area, g.calculated_area) AS calculated_area,
            COALESCE(ld.ld_calculated_eq_length, g.calculated_eq_length) AS calculated_eq_length,
            COALESCE(ld.ld_elbows_90, g.elbows_90) AS elbows_90,
            COALESCE(ld.ld_elbows_45, g.elbows_45) AS elbows_45,
            COALESCE(ld.ld_bevels, g.bevels) AS bevels,
            COALESCE(ld.ld_tees, g.tees) AS tees,
            COALESCE(ld.ld_reducers, g.reducers) AS reducers,
            COALESCE(ld.ld_caps, g.caps) AS caps,
            COALESCE(ld.ld_flanges, g.flanges) AS flanges,
            COALESCE(ld.ld_valves_flanged, g.valves_flanged) AS valves_flanged,
            COALESCE(ld.ld_valves_welded, g.valves_welded) AS valves_welded,
            COALESCE(ld.ld_cut_outs, g.cut_outs) AS cut_outs,
            COALESCE(ld.ld_supports, g.supports) AS supports,
            COALESCE(ld.ld_bends, g.bends) AS bends,
            COALESCE(ld.ld_union_couplings, g.union_couplings) AS union_couplings,
            COALESCE(ld.ld_expansion_joints, g.expansion_joints) AS expansion_joints,
            COALESCE(ld.ld_field_welds, g.field_welds) AS field_welds,
            g.created_at,
            g.updated_at,
            g.pdf_page
        FROM 
            public.general g
        LEFT JOIN 
            length_data ld ON g.pdf_id = ld.ld_pdf_id AND g.pdf_page = ld.ld_pdf_page AND g.size = ld.ld_size
        WHERE 
            g.project_id = p_project_id
        ORDER BY 
            g.pdf_page, g.size;
    END;
    $$ LANGUAGE plpgsql;
"""