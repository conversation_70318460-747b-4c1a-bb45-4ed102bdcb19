"""
Simple and reliable field mapping for data uploads.
Maps SQLite column names to PostgreSQL column names using simple key-value pairs.

This module provides a clean, straightforward approach to column mapping
without the complexity of the existing mapping systems.
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional


# Simple SQLite to PostgreSQL field mappings
# Format: 'sqlite_name': 'postgresql_name'

GENERAL_TABLE_MAPPING = {
    # Primary keys and identifiers
    'id': 'id',
    'pdf_id': 'pdf_id',
    'pdf_page': 'pdf_page',
    'project_id': 'project_id',

    # Elevation Fields
    'min_elevation': 'min_elevation',
    'max_elevation': 'max_elevation',
    'avg_elevation': 'avg_elevation',

    # Measurement fields (common abbreviations to full names)
    'lf': 'length',
    'sf': 'calculated_area',
    'ef': 'calculated_eq_length',

    # CamelCase to snake_case conversions
    'lineNumber': 'line_number',
    'isoNumber': 'iso_number',
    'projectName': 'project_name',
    'projectNo': 'project_no',
    'documentId': 'document_id',
    'documentTitle': 'document_title',
    'clientDocumentId': 'client_document_id',
    'vendorDocumentId': 'vendor_document_id',
    'processUnit': 'process_unit',
    'processLineList': 'process_line_list',
    'isoType': 'iso_type',
    'pipeSpec': 'pipe_spec',
    'pipeStandard': 'pipe_standard',
    'insulationSpec': 'insulation_spec',
    'insulationThickness': 'insulation_thickness',
    'paintSpec': 'paint_spec',
    'paintColor': 'paint_color',
    'heatTrace': 'heat_trace',
    'mediumCode': 'medium_code',
    'minElevation': 'min_elevation',
    'maxElevation': 'max_elevation',
    'totalSheets': 'total_sheets',
    'modDate': 'mod_date',
    'flangeID': 'flange_id',
    'weldId': 'weld_id',
    'weldClass': 'weld_class',
    'xCoord': 'x_coord',
    'yCoord': 'y_coord',
    'blockCoordinates': 'block_coordinates',
    'annotMarkups': 'annot_markups',
    'sysDocument': 'sys_document',
    'sysDocumentName': 'sys_document_name',
    'fluid':'fluid',
    'test_psi':'test_pressure',

    # System fields (already in snake_case)
    'sys_filename': 'sys_filename',
    'sys_path': 'sys_path',
    'sys_build': 'sys_build',
    'sys_layout_valid': 'sys_layout_valid',

    # Fields that remain unchanged
    'area': 'area',
    'coordinates': 'coordinates',
    'coordinates2': 'coordinates',
    'cwp': 'cwp',
    'design_code': 'design_code',
    'document_description': 'document_description',
    'drawing': 'drawing',
    'elevation': 'elevation',
    'pid': 'pid',
    'pwht': 'pwht',
    'revision': 'revision',
    'sequence': 'sequence',
    'service': 'service',
    'sheet': 'sheet',
    'size': 'size',
    'system': 'system',
    'unit': 'unit',
    'xray': 'xray',
    'op1': 'op1',
    'op2': 'op2',
    'dt1': 'dt1',
    'dt2': 'dt2',
    'dp1': 'dp1',
    'dp2': 'dp2',
    'paint_color': 'paint_color',
    'test_type': 'test_type',

    # Fitting counts
    'elbows_90': 'elbows_90',
    'elbows_45': 'elbows_45',
    'bevels': 'bevels',
    'tees': 'tees',
    'reducers': 'reducers',
    'caps': 'caps',
    'flanges': 'flanges',
    'valves_flanged': 'valves_flanged',
    'valves_welded': 'valves_welded',
    'valves_threaded': 'valves_threaded',
    'cut_outs': 'cut_outs',
    'supports': 'supports',
    'bends': 'bends',
    'field_welds': 'field_welds',
    'union_couplings': 'union_couplings',
    'expansion_joints': 'expansion_joints',

    # Generic fields
    'general_field_1': 'field_1',
    'general_field_2': 'field_2',
    'general_field_3': 'field_3',
    'general_field_4': 'field_4',
    'general_field_5': 'field_5',
    'general_field_6': 'field_6',
    'general_field_7': 'field_7',
    'general_field_8': 'field_8',
    'general_field_9': 'field_9',
    'general_field_10': 'field_10',
    'general_field_11': 'field_11',
    'general_field_12': 'field_12',
    'general_field_13': 'field_13',
    'general_field_14': 'field_14',
    'general_field_15': 'field_15',
    'general_field_16': 'field_16',
    'general_field_17': 'field_17',
    'general_field_18': 'field_18',
    'general_field_19': 'field_19',
    'general_field_20': 'field_20',
}

BOM_TABLE_MAPPING = {
    # Primary keys and identifiers
    'id': 'id',
    'pdf_id': 'pdf_id',
    'project_id': 'project_id',
    'pdf_page': 'pdf_page',
    'profile_id': 'profile_id',
    'rfq_ref_id': 'rfq_ref_id',
    'gen_ref_id': 'gen_ref_id',

    # Measurement fields
    'ef': 'calculated_eq_length',
    'sf': 'calculated_area',

    # CamelCase to snake_case conversions
    'componentCategory': 'component_category',
    'lineNumber': 'line_number',
    'materialDescription': 'material_description',
    'material_description': 'material_description',
    'normalizedDescription': 'normalized_description',
    'material_scope': 'mtrl_category',

    # System fields
    'sys_filename': 'sys_filename',
    'sys_path': 'sys_path',
    'sys_build': 'sys_build',

    # Fields that remain unchanged
    'pos': 'pos',
    'size': 'size',
    'ident': 'ident',
    'ident_code':'ident',
    'item': 'item',
    'tag': 'tag',
    'quantity': 'quantity',
    'status': 'status',
    'nb': 'nb',
    'fluid': 'fluid',
    'clean_spec': 'clean_spec',
    'item_count': 'item_count',
    'item_length': 'item_length',
    'total_length': 'total_length',
    'shape': 'shape',
    'size1': 'size1',
    'size2': 'size2',
    'notes': 'notes',
    'deleted': 'deleted',
    'ignore_item': 'ignore_item',
    'validated_date': 'validated_date',
    'validated_by': 'validated_by',
    'mapping_not_found': 'mapping_not_found',

    # Additional fields
    '_index': 'index'
}

RFQ_TABLE_MAPPING = {
    # Primary keys and identifiers
    'id': 'id',
    'project_id': 'project_id',
    'rfq_ref_id': 'rfq_ref_id',

    # CamelCase to snake_case conversions
    'materialDescription': 'material_description',
    'lineNumber': 'line_number',

    # Fields that remain unchanged
    'size': 'size',
    'quantity': 'quantity',
    'unit_of_measure': 'unit_of_measure',
    'material': 'material',
    'abbreviated_material': 'abbreviated_material',
    'technical_standard': 'technical_standard',
    'astm': 'astm',
    'grade': 'grade',
    'rating': 'rating',
    'schedule': 'schedule',
    'coating': 'coating',
    'forging': 'forging',
    'ends': 'ends',
    'item_tag': 'item_tag',
    'tie_point': 'tie_point',
    'notes': 'notes',
    'deleted': 'deleted',
    'ignore_item': 'ignore_item',
    'validated_date': 'validated_date',
    'validated_by': 'validated_by',
    'mapping_not_found': 'mapping_not_found',
}


def get_field_mapping(table_name: str) -> Dict[str, str]:
    """
    Get the field mapping dictionary for a specific table.

    Args:
        table_name: Name of the table ('general', 'bom', or 'rfq')

    Returns:
        Dictionary mapping SQLite column names to PostgreSQL column names
    """
    table_name = table_name.lower()

    if table_name == 'general':
        return GENERAL_TABLE_MAPPING
    elif table_name == 'bom':
        return BOM_TABLE_MAPPING
    elif table_name in ['rfq', 'atem_rfq']:
        return RFQ_TABLE_MAPPING
    else:
        raise ValueError(f"Unknown table name: {table_name}")


def map_column_name(table_name: str, sqlite_column: str) -> str:
    """
    Map a single SQLite column name to its PostgreSQL equivalent.

    Args:
        table_name: Name of the table ('general', 'bom', or 'rfq')
        sqlite_column: SQLite column name to map

    Returns:
        PostgreSQL column name, or original name if no mapping exists
    """
    mapping = get_field_mapping(table_name)
    return mapping.get(sqlite_column, sqlite_column)


def validate_columns(table_name: str, columns: List[str]) -> Tuple[List[str], List[str]]:
    """
    Validate and map a list of column names.

    Args:
        table_name: Name of the table ('general', 'bom', or 'rfq')
        columns: List of SQLite column names to validate and map

    Returns:
        Tuple of (mapped_columns, unmapped_columns)
    """
    mapping = get_field_mapping(table_name)
    mapped_columns = []
    unmapped_columns = []

    for col in columns:
        if col in mapping:
            mapped_columns.append(mapping[col])
        else:
            mapped_columns.append(col)  # Keep original name
            unmapped_columns.append(col)

    return mapped_columns, unmapped_columns


def map_dataframe_columns(df: pd.DataFrame, table_name: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    Map DataFrame column names from SQLite to PostgreSQL format.

    Args:
        df: DataFrame with SQLite column names
        table_name: Name of the table ('general', 'bom', or 'rfq')

    Returns:
        Tuple of (DataFrame with mapped columns, list of unmapped column names)
    """
    mapping = get_field_mapping(table_name)

    # Create column rename mapping
    rename_mapping = {}
    unmapped_columns = []

    for col in df.columns:
        if col in mapping:
            rename_mapping[col] = mapping[col]
        else:
            unmapped_columns.append(col)

    # Rename columns
    df_mapped = df.rename(columns=rename_mapping)

    return df_mapped, unmapped_columns


def audit_workbook(input_file: str, output_file: str = None,
                  audit_general: bool = True,
                  audit_bom: bool = True,
                  audit_verified_materials: bool = True):
    """
    Audit an Excel workbook for data quality and field mapping compliance.

    Args:
        input_file: Path to input Excel file
        output_file: Path to output file (optional)
        audit_general: Whether to audit general table
        audit_bom: Whether to audit bom table
        audit_verified_materials: Whether to audit verified_material_classifications table
    """
    import pandas as pd
    import os
    from datetime import datetime

    print("=" * 80)
    print("ARCHITEKT ATOM - DATA UPLOAD AUDITOR")
    print("=" * 80)
    print(f"Input File: {input_file}")
    print(f"Audit Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Check if input file exists
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file not found: {input_file}")

    # Load the workbook
    try:
        xl_file = pd.ExcelFile(input_file)
        print(f"✅ Successfully loaded workbook with {len(xl_file.sheet_names)} sheets")
        print(f"Available sheets: {xl_file.sheet_names}")
        print()
    except Exception as e:
        raise Exception(f"Failed to load Excel file: {e}")

    audit_results = {}
    processed_dataframes = {}

    # Define fields to ignore for each table
    IGNORE_FIELDS = {
        'general': [
            'id', 'lf', 'sf', 'ef', 'elbows_90', 'elbows_45', 'bevels', 'tees',
            'reducers', 'caps', 'flanges', 'valves_flanged', 'valves_welded',
            'valves_threaded', 'cut_outs', 'supports', 'bends', 'field_welds',
            'union_couplings', 'expansion_joints', '__checked__', 'calculated_eq_length',
            'calculated_area', 'length'
        ],
        'bom': [
            'id', 'ef', 'sf', '__checked__', 'calculated_eq_length', 'calculated_area',
            'sys_build', 'rfq_scope', 'general_category', 'unit_of_measure', 'material',
            'abbreviated_material', 'ansme_ansi', 'astm', 'grade', 'rating', 'schedule',
            'coating', 'forging', 'ends', 'item_tag', 'tie_point', 'pipe_category',
            'valve_type', 'fitting_category', 'weld_category', 'bolt_category',
            'gasket_category', 'technical_standard'
        ],
        'verified_material_classifications': [
            'id', '__checked__'
        ]
    }

    # Audit General Table
    if audit_general:
        # Use the first worksheet instead of looking for a specific sheet name
        first_sheet_name_general = xl_file.sheet_names[0]
        print(f"📋 Using first worksheet for General table: '{first_sheet_name_general}'")

        audit_results['general'], processed_dataframes['general'] = audit_table_data(
            xl_file, 'general', first_sheet_name_general, IGNORE_FIELDS['general']
        )

    # Audit BOM Table
    if audit_bom:
        # Use the first worksheet instead of looking for a specific sheet name
        first_sheet_name_bom = xl_file.sheet_names[0]
        print(f"📋 Using first worksheet for BOM table: '{first_sheet_name_bom}'")

        audit_results['bom'], processed_dataframes['bom'] = audit_table_data(
            xl_file, 'bom', first_sheet_name_bom, IGNORE_FIELDS['bom']
        )

    # Audit Verified Material Classifications
    if audit_verified_materials:
        audit_results['verified_material_classifications'], processed_dataframes['verified_material_classifications'] = audit_table_data(
            xl_file, 'verified_material_classifications', 'Verified Material Classifications',
            IGNORE_FIELDS['verified_material_classifications']
        )

    # Print summary
    print_audit_summary(audit_results)

    # Save results if output file specified
    if output_file:
        save_audit_results(audit_results, output_file, processed_dataframes)

    return audit_results


def audit_table_data(xl_file, table_name: str, sheet_name: str, ignore_fields: list):
    """
    Audit a specific table's data.

    Args:
        xl_file: Pandas ExcelFile object
        table_name: Name of the table (general, bom, etc.)
        sheet_name: Name of the Excel sheet to read
        ignore_fields: List of fields to ignore during mapping check

    Returns:
        Tuple of (audit results dictionary, processed dataframe)
    """
    print(f"🔍 AUDITING {table_name.upper()} TABLE")
    print("-" * 50)

    results = {
        'table_name': table_name,
        'sheet_name': sheet_name,
        'status': 'PASSED',
        'critical_errors': [],
        'warnings': [],
        'unmapped_fields': [],
        'row_count': 0,
        'column_count': 0
    }

    # Check if sheet exists
    if sheet_name not in xl_file.sheet_names:
        results['status'] = 'FAILED'
        results['critical_errors'].append(f"Sheet '{sheet_name}' not found in workbook")
        print(f"❌ Sheet '{sheet_name}' not found")
        print()
        return results, None

    # Load the data
    try:
        df = pd.read_excel(xl_file, sheet_name=sheet_name)
        results['row_count'] = len(df)
        results['column_count'] = len(df.columns)
        print(f"📊 Loaded {len(df)} rows, {len(df.columns)} columns")
    except Exception as e:
        results['status'] = 'FAILED'
        results['critical_errors'].append(f"Failed to read sheet '{sheet_name}': {e}")
        print(f"❌ Failed to read sheet: {e}")
        print()
        return results, None

    # Check for empty DataFrame
    if df.empty:
        results['status'] = 'FAILED'
        results['critical_errors'].append("DataFrame is empty")
        print("❌ DataFrame is empty")
        print()
        return results, None

    # Critical checks for general and bom tables
    if table_name in ['general', 'bom']:
        critical_error = check_critical_fields(df, table_name, results)
        if critical_error:
            results['status'] = 'FAILED'
            print()
            return results, None

    # Check field mappings
    check_field_mappings(df, table_name, ignore_fields, results)

    print(f"✅ {table_name.upper()} audit completed")
    print()
    return results, df


def check_critical_fields(df, table_name: str, results: dict) -> bool:
    """
    Check critical fields that must exist and be populated.
    Attempts to extract sys_filename from sys_path if sys_filename is missing.

    Returns:
        True if critical error found, False otherwise
    """
    import os
    import pandas as pd

    critical_fields = ['pdf_id', 'sys_filename']
    has_critical_error = False

    print("🔒 Checking critical fields...")

    # Special handling for sys_filename - try to extract from sys_path if missing
    if 'sys_filename' not in df.columns and 'sys_path' in df.columns:
        print("⚠️  sys_filename missing, attempting to extract from sys_path...")
        success_count = 0

        try:
            # Extract filename from sys_path
            extracted_filenames = []
            for path in df['sys_path']:
                if pd.isna(path) or path == '':
                    extracted_filenames.append('')
                else:
                    filename = os.path.basename(str(path))
                    # Validate it's a PDF file
                    if filename.lower().endswith('.pdf'):
                        extracted_filenames.append(filename)
                        success_count += 1
                    else:
                        extracted_filenames.append('')

            # Add the extracted sys_filename column to the DataFrame
            df['sys_filename'] = extracted_filenames

            if success_count > 0:
                print(f"✅ Successfully extracted sys_filename from sys_path for {success_count} rows")
                print(f"   Example extracted filename: {next((f for f in extracted_filenames if f), 'None')}")
            else:
                print("❌ Failed to extract any valid PDF filenames from sys_path")

        except Exception as e:
            print(f"❌ Error extracting sys_filename from sys_path: {e}")

    for field in critical_fields:
        # Check if field exists
        if field not in df.columns:
            error_msg = f"CRITICAL: Required field '{field}' is missing from {table_name} table"
            results['critical_errors'].append(error_msg)
            print(f"❌ {error_msg}")
            has_critical_error = True
            continue

        # Check if field has any null/empty values
        null_count = df[field].isnull().sum()
        empty_count = (df[field] == '').sum() if df[field].dtype == 'object' else 0
        total_missing = null_count + empty_count

        if total_missing > 0:
            error_msg = f"CRITICAL: Required field '{field}' has {total_missing} missing values in {table_name} table"
            results['critical_errors'].append(error_msg)
            print(f"❌ {error_msg}")
            has_critical_error = True
        else:
            print(f"✅ {field}: All {len(df)} rows populated")

    if has_critical_error:
        print("🚨 CRITICAL ERRORS FOUND - Cannot proceed with upload")
        raise Exception(f"Critical validation failed for {table_name} table. Fix required fields before proceeding.")

    return has_critical_error


def check_field_mappings(df, table_name: str, ignore_fields: list, results: dict):
    """
    Check which fields don't have mappings and report them.
    """
    print("🗺️  Checking field mappings...")

    # Get the mapping for this table
    try:
        field_mapping = get_field_mapping(table_name)
    except ValueError:
        results['warnings'].append(f"No field mapping defined for table '{table_name}'")
        print(f"⚠️  No field mapping defined for table '{table_name}'")
        return

    # Check each column in the DataFrame
    unmapped_fields = []
    mapped_count = 0
    ignored_count = 0

    for column in df.columns:
        if column in ignore_fields:
            ignored_count += 1
            continue
        elif column in field_mapping:
            mapped_count += 1
        else:
            unmapped_fields.append(column)

    results['unmapped_fields'] = unmapped_fields

    print(f"📈 Mapping Summary:")
    print(f"   • Mapped fields: {mapped_count}")
    print(f"   • Ignored fields: {ignored_count}")
    print(f"   • Unmapped fields: {len(unmapped_fields)}")

    if unmapped_fields:
        print(f"⚠️  UNMAPPED FIELDS in {table_name.upper()}:")
        for i, field in enumerate(unmapped_fields, 1):
            print(f"   {i:2d}. {field}")
        results['warnings'].append(f"Found {len(unmapped_fields)} unmapped fields")
    else:
        print(f"✅ All non-ignored fields have mappings")


def print_audit_summary(audit_results: dict):
    """Print a summary of all audit results."""
    print("=" * 80)
    print("AUDIT SUMMARY")
    print("=" * 80)

    total_tables = len(audit_results)
    passed_tables = sum(1 for r in audit_results.values() if r['status'] == 'PASSED')
    failed_tables = total_tables - passed_tables

    print(f"Tables Audited: {total_tables}")
    print(f"✅ Passed: {passed_tables}")
    print(f"❌ Failed: {failed_tables}")
    print()

    for table_name, results in audit_results.items():
        status_icon = "✅" if results['status'] == 'PASSED' else "❌"
        print(f"{status_icon} {table_name.upper()}: {results['status']}")
        print(f"   Rows: {results['row_count']:,} | Columns: {results['column_count']}")

        if results['critical_errors']:
            print(f"   Critical Errors: {len(results['critical_errors'])}")
        if results['warnings']:
            print(f"   Warnings: {len(results['warnings'])}")
        if results['unmapped_fields']:
            print(f"   Unmapped Fields: {len(results['unmapped_fields'])}")
        print()

    if failed_tables > 0:
        print("🚨 UPLOAD NOT RECOMMENDED - Fix critical errors first")
    else:
        print("✅ ALL TABLES PASSED - Ready for upload")

    print("=" * 80)


def validate_postgresql_columns(df, table_name: str):
    """
    Validate that all DataFrame columns exist in the PostgreSQL table.
    REQUIRES actual database connection - no fallback allowed.

    Args:
        df: DataFrame to validate
        table_name: Name of the PostgreSQL table

    Returns:
        Tuple of (valid_columns, invalid_columns, validation_results)
    """

    # Get actual PostgreSQL columns - NO FALLBACK
    actual_columns = get_postgresql_table_columns(table_name)
    if not actual_columns:
        raise Exception(f"❌ CRITICAL: Could not connect to PostgreSQL or retrieve columns for table '{table_name}'. Database validation is required!")

    print(f"   � Connected to PostgreSQL - found {len(actual_columns)} columns in {table_name}")

    # Ensure all column names are lowercase
    df_columns_lower = [col.lower() for col in df.columns]
    actual_columns_lower = [col.lower() for col in actual_columns]

    # Check which columns are valid
    valid_columns = []
    invalid_columns = []

    for col in df_columns_lower:
        if col in actual_columns_lower:
            valid_columns.append(col)
        else:
            invalid_columns.append(col)

    validation_results = {
        'total_columns': len(df_columns_lower),
        'valid_columns': len(valid_columns),
        'invalid_columns': len(invalid_columns),
        'validation_passed': len(invalid_columns) == 0,
        'used_database_connection': True
    }

    return valid_columns, invalid_columns, validation_results


def get_postgresql_table_columns(table_name: str):
    """
    Get actual column names from PostgreSQL table.

    Args:
        table_name: Name of the PostgreSQL table

    Returns:
        List of column names or None if connection fails
    """
    try:
        # Try to import and use existing PostgreSQL connection
        import sys
        import os

        # Add the pg_database path to sys.path to import pg_connection
        pg_db_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if pg_db_path not in sys.path:
            sys.path.append(pg_db_path)

        from pg_connection import get_db_connection

        # Query to get column names
        query = """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = %s
        ORDER BY ordinal_position;
        """

        # Use context manager for database connection
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (table_name,))
                columns = [row[0] for row in cursor.fetchall()]

        return columns

    except Exception as e:
        print(f"   ❌ Database connection error: {e}")
        return None


def get_fallback_columns(table_name: str):
    """Get fallback column list when database connection is not available."""
    POSTGRESQL_COLUMNS = {
        'general': [
            'id', 'pdf_id', 'project_id', 'pdf_page', 'document_title', 'drawing',
            'line_number', 'pid', 'project_no', 'sheet', 'total_sheets', 'field_1',
            'field_2', 'field_3', 'field_4', 'field_5', 'field_6', 'field_7',
            'field_8', 'field_9', 'field_10', 'field_11', 'field_12', 'field_13',
            'field_14', 'field_15', 'field_16', 'field_17', 'field_18', 'field_19',
            'field_20', 'elevation', 'min_elevation', 'max_elevation', 'avg_elevation',
            'sys_path', 'sys_filename', 'iso_number', 'iso_type', 'pipe_spec',
            'pipe_standard', 'insulation_spec', 'insulation_thickness', 'paint_spec',
            'paint_color', 'heat_trace', 'medium_code', 'process_unit', 'process_line_list',
            'client_document_id', 'vendor_document_id', 'mod_date', 'flange_id',
            'weld_id', 'weld_class', 'x_coord', 'y_coord', 'block_coordinates',
            'annot_markups', 'sys_document', 'sys_document_name', 'sys_build',
            'sys_layout_valid', 'area', 'coordinates', 'coordinates2', 'cwp',
            'design_code', 'document_description', 'revision', 'sequence', 'service',
            'size', 'system', 'unit', 'xray', 'pwht', 'fluid', 'length', 'calculated_area',
            'calculated_eq_length', 'elbows_90', 'elbows_45', 'bevels', 'tees',
            'reducers', 'caps', 'flanges', 'valves_flanged', 'valves_welded',
            'valves_threaded', 'cut_outs', 'supports', 'bends', 'field_welds',
            'union_couplings', 'expansion_joints', 'created_at', 'updated_at'
        ],
        'bom': [
            'id', 'pdf_id', 'project_id', 'pdf_page', 'profile_id', 'rfq_ref_id',
            'gen_ref_id', 'calculated_eq_length', 'calculated_area', 'component_category',
            'line_number', 'material_description', 'normalized_description', 'mtrl_category',
            'sys_filename', 'sys_path', 'sys_build', 'pos', 'size', 'ident', 'item',
            'tag', 'quantity', 'status', 'nb', 'fluid', 'clean_spec', 'item_count',
            'item_length', 'total_length', 'shape', 'size1', 'size2', 'notes',
            'deleted', 'ignore_item', 'validated_date', 'validated_by', 'mapping_not_found',
            'created_at', 'updated_at'
        ],
        'verified_material_classifications': [
            'id', 'project_id', 'material_description', 'size', 'quantity',
            'unit_of_measure', 'material', 'abbreviated_material', 'technical_standard',
            'astm', 'grade', 'rating', 'schedule', 'coating', 'forging', 'ends',
            'item_tag', 'tie_point', 'notes', 'deleted', 'ignore_item', 'validated_date',
            'validated_by', 'mapping_not_found', 'created_at', 'updated_at'
        ]
    }

    return POSTGRESQL_COLUMNS.get(table_name, [])


def prepare_dataframe_for_upload(df, table_name: str):
    """
    Prepare a dataframe for upload by cleaning values and removing ignore fields.

    Args:
        df: DataFrame to prepare
        table_name: Name of the table ('general', 'bom', etc.)

    Returns:
        Cleaned DataFrame ready for upload
    """
    import numpy as np

    # Define fields to ignore/remove for each table
    IGNORE_FIELDS = {
        'general': [
            'id', 'lf', 'sf', 'ef', 'elbows_90', 'elbows_45', 'bevels', 'tees',
            'reducers', 'caps', 'flanges', 'valves_flanged', 'valves_welded',
            'valves_threaded', 'cut_outs', 'supports', 'bends', 'field_welds',
            'union_couplings', 'expansion_joints', '__checked__', 'last_updated', 'mod_date',
            'sys_layout_valid', 'sys_build', 'id_annot_info'
        ],
        'bom': [
            'id', 'ef', 'sf', '__checked__', 'calculated_eq_length', 'calculated_area',
            'sys_build', 'rfq_scope', 'general_category', 'unit_of_measure', 'material',
            'abbreviated_material', 'ansme_ansi', 'astm', 'grade', 'rating', 'schedule',
            'coating', 'forging', 'ends', 'item_tag', 'tie_point', 'pipe_category',
            'valve_type', 'fitting_category', 'weld_category', 'bolt_category',
            'gasket_category', 'technical_standard'
        ],
        'verified_material_classifications': [
            'id', '__checked__'
        ]
    }

    # Make a copy to avoid modifying original
    df_clean = df.copy()

    # Apply field mappings
    try:
        field_mapping = get_field_mapping(table_name)
        # Rename columns according to mapping
        rename_dict = {}
        for col in df_clean.columns:
            if col in field_mapping:
                rename_dict[col] = field_mapping[col]

        if rename_dict:
            df_clean = df_clean.rename(columns=rename_dict)
            print(f"   Mapped {len(rename_dict)} column names for {table_name}")
    except ValueError:
        print(f"   No field mapping available for {table_name}")

    # Ensure all column names are lowercase
    lowercase_columns = {}
    for col in df_clean.columns:
        lowercase_col = col.lower()
        if col != lowercase_col:
            lowercase_columns[col] = lowercase_col

    if lowercase_columns:
        df_clean = df_clean.rename(columns=lowercase_columns)
        print(f"   Converted {len(lowercase_columns)} column names to lowercase")

    # Remove ignore fields (after mapping and lowercasing)
    ignore_list = IGNORE_FIELDS.get(table_name, [])
    # Make ignore list lowercase too
    ignore_list_lower = [field.lower() for field in ignore_list]
    columns_to_drop = [col for col in ignore_list_lower if col in df_clean.columns]
    if columns_to_drop:
        df_clean = df_clean.drop(columns=columns_to_drop)
        print(f"   Removed {len(columns_to_drop)} ignored columns: {columns_to_drop}")

    # Validate PostgreSQL columns
    print(f"   Validating PostgreSQL columns for {table_name}...")
    _, invalid_cols, validation_results = validate_postgresql_columns(df_clean, table_name)

    if validation_results['validation_passed']:
        connection_status = "🔗 (live database)" if validation_results.get('used_database_connection') else "📋 (fallback list)"
        print(f"   ✅ All {validation_results['valid_columns']} columns are valid PostgreSQL columns {connection_status}")
    else:
        connection_status = "🔗 (live database)" if validation_results.get('used_database_connection') else "📋 (fallback list)"
        print(f"   ❌ Found {validation_results['invalid_columns']} invalid PostgreSQL columns {connection_status}:")
        for col in invalid_cols:
            print(f"      - {col}")
        print(f"   ✅ {validation_results['valid_columns']} columns are valid")

        # If we have invalid columns, this is a critical issue
        if invalid_cols:
            print(f"   🚨 WARNING: Invalid columns detected! These will cause PostgreSQL upload to fail.")
            print(f"   💡 Suggestion: Remove these columns or add them to the ignore list.")

    # Clean NaN, nan, None values - replace with empty strings
    # Handle different types of null/nan values
    df_clean = df_clean.replace({
        np.nan: '',
        'NaN': '',
        'nan': '',
        'None': '',
        'NULL': '',
        'null': '',
        None: ''
    })

    # Also handle string representations that might have been read from Excel
    for col in df_clean.select_dtypes(include=['object']).columns:
        df_clean[col] = df_clean[col].astype(str)
        df_clean[col] = df_clean[col].replace({
            'nan': '',
            'NaN': '',
            'None': '',
            'NULL': '',
            'null': ''
        })
        # Convert back empty strings to actual empty strings (not string 'nan')
        df_clean[col] = df_clean[col].replace('', '')

    print(f"   Cleaned null/NaN values in {table_name} data")

    # Generate test SQL query
    column_list = ', '.join(df_clean.columns)
    test_query = f"SELECT {column_list}\nFROM public.{table_name}\nLIMIT 1;"
    print(f"   📝 Test SQL Query:")
    print(f"   {test_query}")

    return df_clean


def save_audit_results(audit_results: dict, output_file: str, processed_dataframes: dict = None):
    """Save audit results to an Excel file with cleaned data ready for upload."""
    import pandas as pd
    from datetime import datetime
    import os

    # Create directory if it doesn't exist
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 Created directory: {output_dir}")

    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

            # First, write the cleaned dataframes ready for upload
            if processed_dataframes:
                for table_name, df in processed_dataframes.items():
                    if df is not None and not df.empty:
                        # Clean the dataframe for upload
                        df_clean = prepare_dataframe_for_upload(df, table_name)
                        sheet_name = f"{table_name.title()}_Ready_For_Upload"
                        df_clean.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"✅ {table_name.title()} data ready for upload saved to sheet: {sheet_name}")

            # Create summary sheet
            summary_data = {
                'Metric': [
                    'Audit Timestamp',
                    'Total Tables Audited',
                    'Tables Passed',
                    'Tables Failed',
                    'Overall Status'
                ],
                'Value': [
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    len(audit_results),
                    sum(1 for r in audit_results.values() if r['status'] == 'PASSED'),
                    sum(1 for r in audit_results.values() if r['status'] == 'FAILED'),
                    'PASSED' if all(r['status'] == 'PASSED' for r in audit_results.values()) else 'FAILED'
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # Create detailed results sheet for each table
            for table_name, results in audit_results.items():

                # Table overview
                overview_data = {
                    'Property': [
                        'Table Name',
                        'Sheet Name',
                        'Status',
                        'Row Count',
                        'Column Count',
                        'Critical Errors Count',
                        'Warnings Count',
                        'Unmapped Fields Count'
                    ],
                    'Value': [
                        results['table_name'],
                        results['sheet_name'],
                        results['status'],
                        results['row_count'],
                        results['column_count'],
                        len(results['critical_errors']),
                        len(results['warnings']),
                        len(results['unmapped_fields'])
                    ]
                }

                # Create detailed breakdown
                max_items = max(
                    len(results['critical_errors']),
                    len(results['warnings']),
                    len(results['unmapped_fields'])
                )

                # Pad lists to same length
                critical_errors = results['critical_errors'] + [''] * (max_items - len(results['critical_errors']))
                warnings = results['warnings'] + [''] * (max_items - len(results['warnings']))
                unmapped_fields = results['unmapped_fields'] + [''] * (max_items - len(results['unmapped_fields']))

                details_data = {
                    'Critical Errors': critical_errors,
                    'Warnings': warnings,
                    'Unmapped Fields': unmapped_fields
                }

                # Combine overview and details
                overview_df = pd.DataFrame(overview_data)
                details_df = pd.DataFrame(details_data)

                # Add some spacing
                spacer_df = pd.DataFrame({'Property': [''], 'Value': ['']})
                details_header_df = pd.DataFrame({
                    'Property': ['DETAILED BREAKDOWN'],
                    'Value': ['']
                })

                # Combine all data for this table
                combined_df = pd.concat([
                    overview_df,
                    spacer_df,
                    details_header_df,
                    spacer_df
                ], ignore_index=True)

                # Write overview to sheet
                combined_df.to_excel(writer, sheet_name=f'{table_name.title()}', index=False)

                # Write details starting from row after overview
                start_row = len(combined_df) + 2
                details_df.to_excel(writer, sheet_name=f'{table_name.title()}',
                                  startrow=start_row, index=False)

            # Create a consolidated unmapped fields sheet
            all_unmapped = []
            for table_name, results in audit_results.items():
                for field in results['unmapped_fields']:
                    all_unmapped.append({
                        'Table': table_name,
                        'Unmapped Field': field,
                        'Status': results['status']
                    })

            if all_unmapped:
                unmapped_df = pd.DataFrame(all_unmapped)
                unmapped_df.to_excel(writer, sheet_name='All Unmapped Fields', index=False)

        print(f"📊 Audit results saved to Excel: {output_file}")

    except Exception as e:
        print(f"⚠️  Failed to save audit results to Excel: {e}")
        # Fallback to JSON if Excel fails
        try:
            import json
            json_file = output_file.replace('.xlsx', '.json').replace('.xls', '.json')

            # Create directory for JSON file if it doesn't exist
            json_dir = os.path.dirname(json_file)
            if json_dir and not os.path.exists(json_dir):
                os.makedirs(json_dir, exist_ok=True)
                print(f"📁 Created directory for JSON: {json_dir}")

            serializable_results = {}
            for table_name, results in audit_results.items():
                serializable_results[table_name] = {
                    'table_name': results['table_name'],
                    'sheet_name': results['sheet_name'],
                    'status': results['status'],
                    'critical_errors': results['critical_errors'],
                    'warnings': results['warnings'],
                    'unmapped_fields': results['unmapped_fields'],
                    'row_count': results['row_count'],
                    'column_count': results['column_count']
                }

            output_data = {
                'audit_timestamp': datetime.now().isoformat(),
                'total_tables': len(audit_results),
                'passed_tables': sum(1 for r in audit_results.values() if r['status'] == 'PASSED'),
                'failed_tables': sum(1 for r in audit_results.values() if r['status'] == 'FAILED'),
                'results': serializable_results
            }

            with open(json_file, 'w') as f:
                json.dump(output_data, f, indent=2)
            print(f"📄 Fallback: Audit results saved to JSON: {json_file}")

        except Exception as json_error:
            print(f"⚠️  Failed to save fallback JSON: {json_error}")


if __name__ == "__main__":
    # Configuration
    INPUT_FILE = r"c:\Drawings\Clients\brockservices\BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL #107\data\4 - classified\exported_bom_data_nofieldmap.xlsx"
    OUTPUT_FILE = r"c:\Drawings\Clients\brockservices\BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL #107\data\4 - classified\BOM - Load.xlsx"

    # Control flags
    AUDIT_GENERAL = False
    AUDIT_BOM = True
    AUDIT_VERIFIED_MATERIALS = False

    print("Auditing General Table")
    try:
        # Run the audit
        results = audit_workbook(
            input_file=INPUT_FILE,
            output_file=OUTPUT_FILE,
            audit_general=AUDIT_GENERAL,
            audit_bom=AUDIT_BOM,
            audit_verified_materials=AUDIT_VERIFIED_MATERIALS
        )

        print("\n🎉 GENERAL Audit completed successfully!")

    except Exception as e:
        print(f"\n💥 GENERAL Audit failed with error: {e}")
        import traceback
        traceback.print_exc()


