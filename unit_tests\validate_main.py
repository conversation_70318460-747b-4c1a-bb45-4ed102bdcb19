import pandas as pd
from pandas.io.excel import _XlsxWriter
from unit_tests.validate_quantities import validate_dataframe

if __name__ == "__main__":
    print("Starting validation...\n -> Reading Excel file...")
    # Your existing dataframe
    df = pd.read_excel(
        r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\PreProcessed\Binder2-A BOM OCR - Processed.xlsx"
                       )

    print("\n -> Validating quantities...")
    # Validate quantities
    validated_df = validate_dataframe(df)

    print("\n -> Exporting to Excel...")
    validated_df.to_excel(
        r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\PreProcessed\Pre-Validated\Binder 2A - validated_bom.xlsx", index=False
        )

    print("\n -> Exported to Validated BOM Data to Excel...")

    # Filter rows that need review (any severity > 0)
    rows_to_review = validated_df[validated_df['severity'] > 0]

    # Filter rows with critical issues (severity >= 4)
    critical_issues = validated_df[validated_df['severity'] >= 3]

    print(f"\n\nTotal rows: {len(validated_df)}")
    print(f"Rows to review: {len(rows_to_review)}")
    print(f"Critical issues: {len(critical_issues)}")
