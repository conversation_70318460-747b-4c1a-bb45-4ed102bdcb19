"""
Plugin to read column names from PostgreSQL tables

This plugin connects to the PostgreSQL database and retrieves column information
for specified tables, which can be used to generate export profiles or for data analysis.
"""

import pandas as pd
import os
from typing import List, Dict, Any, Optional, Union

from src.atom.pg_database.pg_connection import get_db_connection, DatabaseConfig

# Some fields not caught by field due to snake_case rename
other_field_map = {
    "project_id": "Project ID",
    "profile_id": "Profile ID",
    "project_name": "Project Name",
    "project_no": "Project No.",
    "component_category": "Component Category",
    "mtrl_category": "Material Category",
    "line_number": "Line Number",
    "technical_standard": "Technical Standard",
    "created_at": "Created At",
    "created_by": "Created By",
    "updated_at": "Updated At",
    "updated_by": "Updated By",
    "validated_date": "Validated Date",
    "validated_by": "Validated By",
    "iso_number": "ISO Number",
    "vendor_document_id": "Vendor Document Id",
    "client_document_id": "Client Document Id",
    "total_sheets": "Total Sheets",
    "design_code": "Design Code",
    "flange_id": "Flange Id",
    "document_id": "Document Id",
    "heat_trace": "Heat Trace",
    "iso_type": "ISO Type",
    "pipe_standard": "Pipe Standard",
    "mod_date": "Mod Date",
    "paint_spec": "Paint Spec",
    "paint_color": "Paint Color",
    "insulation_spec": "Insulation Spec",
    "insulation_thickness": "Insulation Thickness",
    "medium_code": "Medium Code",
    "weld_id": "Weld Id",
    "weld_class": "Weld Class",
    "process_line_list": "Process Line List",
    "process_unit": "Process Unit",
    "document_title": "Document Title",
    "document_description": "Document Description",
    "normalized_description": "Normalized Description",
    "annot_markups": "Annot Types Data",
    "rfq_ref_id": "RFQ Ref Id",
    "gen_ref_id": "Gen Ref Id",
    "notes": "Notes",
}

# Define some preferred order for columns

# Globally preferred columns
INITIAL_GENERAL_COLUMNS = ["sys_filename", "pdf_id", "pdf_page"] # File identifiers
INITIAL_COLUMNS = ["pdf_id", "pdf_page"] # File identifiers

# Table-specific preferred columns
TABLE_COLUMNS = {
    "public.bom": {
        "project_id": 1,
        "pos": 1,
        "material_description": 1,
        "size": 1,
        "ident": 1,
        "tag": 1,
        "quantity": 1,
        "status": 1,
        "item_count": 1,        # ADD THIS
        "item_length": 1,       # ADD THIS
        "total_length": 1,      # ADD THIS
        "shape": 1,             # ADD THIS
        "normalized_description": 1,
        "component_category": 1,
        "mtrl_category": 1,
        "size1": 1,
        "size2": 1,
        "nb": 1,
        "fluid": 1,
        "clean_spec": 1,
        "line_number": 1,
        "rfq_scope": 1,
        "general_category": 1,
        "unit_of_measure": 1,
        "material": 1,
        "abbreviated_material": 1,
        "technical_standard": 1,
        "astm": 1,
        "grade": 1,
        "rating": 1,
        "schedule": 1,
        "coating": 1,
        "forging": 1,
        "ends": 1,
        "item_tag": 1,
        "tie_point": 1,
        "pipe_category": 1,
        "valve_type": 1,
        "fitting_category": 1,
        "weld_category": 1,
        "bolt_category": 1,
        "gasket_category": 1,
        "notes": 1,
        "deleted": 1,
        "ignore_item": 1,
        "validated_date": 1,
        "validated_by": 1,
        "calculated_eq_length": 1,
        "calculated_area": 1,
        "created_at": 1,
        "created_by": 1,
        "updated_at": 1,
        "updated_by": 1,
        "id": 1,
        "profile_id": 1,
        "rfq_ref_id": 1,
        "gen_ref_id": 1,
    },
    "public.general": {
        "project_id": 1,

        # Line identifiers
        "line_number": 1,
        "pid": 1,
        "drawing": 1,
        "iso_number": 1,

        # Sheet information
        "vendor_document_id": 1,
        "client_document_id": 1,
        "sheet": 1,
        "total_sheets": 1,
        "project_no": 1,
        "project_name": 1,
        "revision": 1,

        # Specification Data
        "area": 1,
        "coordinates": 1,
        "design_code": 1,
        "document_description": 1,
        "document_id": 1,
        "document_title": 1,
        "flange_id": 1,
        "heat_trace": 1,
        "insulation_spec": 1,
        "insulation_thickness": 1,
        "iso_type": 1,
        "elevation": 1,
        "avg_elevation": 1,
        "min_elevation": 1,
        "max_elevation": 1,
        "medium_code": 1,
        "mod_date": 1,

        "pipe_standard": 1,
        "process_line_list": 1,
        "process_unit": 1,
        "pwht": 1,
        "sequence": 1,
        "service": 1,
        "size": 1,
        "sys_build": 1,
        "sys_layout_valid": 1,
        "sys_document": 0,
        "sys_document_name": 0,
        "sys_path": 0,
        "system": 1,
        "unit": 1,
        "pipe_spec": 1,
        "weld_id": 1,
        "weld_class": 1,
        "xray": 1,

        "paint_color": 1,
        "cwp": 1,
        "length": 1,
        "calculated_area": 1,
        "calculated_eq_length": 1,

        # Insulation and paint information
        "paint_spec": 1,

        # Calculated columns
        "elbows_90": 1,
        "elbows_45": 1,
        "bevels": 1,
        "tees": 1,
        "reducers": 1,
        "caps": 1,
        "flanges": 1,
        "valves_flanged": 1,
        "valves_welded": 1,
        "cut_outs": 1,
        "supports": 1,
        "bends": 1,
        "union_couplings": 1,
        "expansion_joints": 1,
        "field_welds": 1,

        # Other
        "id": 1,
        "created_at": 1,
        "updated_at": 1,
        "stress_req": 1,
        "annot_markups": 0,
        "block_coordinates": 0,
        "x_coord": 0,
        "y_coord": 0,
    },
    "public.atem_rfq": {
        "id": 1,
        "project_id": 1,
        "rfq_input_id": 1,
        "material_description": 1,
        "normalized_description": 1,
        "size": 1,
        "size1": 1,
        "size2": 1,
        "quantity": 1,
        "mtrl_category": 1,
        "rfq_scope": 1,
        "general_category": 1,
        "unit_of_measure": 1,
        "material": 1,
        "abbreviated_material": 1,
        "technical_standard": 1,
        "astm": 1,
        "grade": 1,
        "rating": 1,
        "schedule": 1,
        "coating": 1,
        "forging": 1,
        "ends": 1,
        "item_tag": 1,
        "tie_point": 1,
        "pipe_category": 1,
        "valve_type": 1,
        "fitting_category": 1,
        "weld_category": 1,
        "bolt_category": 1,
        "gasket_category": 1,
        "calculated_eq_length": 1,
        "calculated_area": 1,
        "notes": 1,
        "deleted": 1,
        "ignore_item": 1,
        "validated_date": 1,
        "validated_by": 1,
        "created_at": 1,
        "created_by": 1,
        "updated_at": 1,
        "updated_by": 1,
        "profile_id": 1,
        "mapping_not_found": 1,
    },
    "general_aggregate": {
        "id": 1,
        "pdf_id": 1,
        "pdf_page": 1,
        "size": 1,
        "length": 1,
        "elbows_90": 1,
        "elbows_45": 1,
        "bevels": 1,
        "tees": 1,
        "reducers": 1,
        "caps": 1,
        "flanges": 1,
        "valves_flanged": 1,
        "valves_welded": 1,
        "cut_outs": 1,
        "supports": 1,
        "bends": 1,
        "union_couplings": 1,
        "expansion_joints": 1,
        "field_welds": 1,
        "calculated_eq_length": 1,
        "calculated_area": 1,
        "stress_req": 1,
        "change_type": 1,
        "action_taken": 1,
    }
}

# Some avaiable default queries
QUERIES = [
    {
        "id": "general_aggregate",
        "sheet_name": "General Agg.",
        "do_not_export": 0,
        "query": "SELECT {fields_to_export} FROM manage_bom_to_general_aggregation_full(%s, FALSE)",
        "params": "project_id",
        "order_by": "pdf_page asc, size desc",
        "column_groups": "",
    },
    {
        "id": "public.bom",
        "do_not_export": 0,
        "query": "SELECT {fields_to_export} FROM public.bom WHERE project_id = %s",
        "sheet_name": "BOM",
        "params": "project_id",
        "order_by": "quantity asc",
        "column_groups": "pos-status:1,normalized_description-fluid:2",
    },
    {
        "id": "public.general",
        "do_not_export": 0,
        "query": "SELECT {fields_to_export} FROM public.general WHERE project_id = %s",
        "sheet_name": "General",
        "params": "project_id",
        "order_by": "pdf_page asc",
        "column_groups": "",
    },
    {
        "id": "public.atem_rfq",
        "do_not_export": 0,
        "query": "SELECT {fields_to_export} FROM public.atem_rfq WHERE project_id = %s",
        "sheet_name": "RFQ",
        "params": "project_id",
        "order_by": "material_description asc",
        "column_groups": "",
    },
    {
        "id": "custom_bom",
        "do_not_export": 0,
        "query": "SELECT {fields_to_export} FROM public.bom WHERE material_description = 'ECCENTRIC SWAGE MSS SP-95 - A403-WP304/304L DG PE PE SMLS SCH1-40S SCH2-40S R22FQL2FZZ04' ",
        "sheet_name": "Custom BOM Query",
        "params": "project_id",
        "order_by": "material_description asc",
        "column_groups": "",
    }
]

def getSimpleFieldMap():
    import sys
    sys.path[0] = ""
    from src.app_paths import getSavedFieldMapJson
    fieldMap = getSavedFieldMapJson()
    simpleFieldMap = {}
    for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
        subData = fieldMap.get(key, {})
        # Discard unuseful data
        for _, v in subData.items():
            try:
                del v["options"]
            except Exception as e:
                pass
        simpleFieldMap.update(subData)
    return simpleFieldMap

def plugin_postgres_generate_export_profile(
    # tables: List[str] = "public.bom, public.general, public.atem_rfq, manage_bom_to_general_aggregation",
    save_file: str = "debug/template_export_profile.xlsx"
) -> Union[pd.DataFrame, Dict[str, List[Dict[str, Any]]]]:
    """
    Generates an export profile for specified PostgreSQL tables and saves it to an Excel file.

    Args:
        tables (List[str]): List of tables to include in the export profile.
        save_file (str): Path to save the export profile Excel file.

    Returns:
        Export parameters dictionary

    Export Parameters Documentation:
        The export profile contains the following parameters:

        use_field_name (int): Whether to use field names instead of display names in the exported file
            0 = Use display names (default), 1 = Use field names

        bold_header_title (int): Whether to make the header row bold
            0 = Normal, 1 = Bold (default)

        auto_adjust_column_width (int): Whether to automatically adjust column widths based on content
            0 = Fixed width, 1 = Auto-adjust (default)

        max_column_width (int): Maximum column width in characters (0 = no limit)
            Example: 50 would limit all columns to 50 characters width

        min_column_width (int): Minimum column width in characters (0 = no minimum)
            Example: 10 would ensure all columns are at least 10 characters wide

        save_dir (str): Directory where exported files will be saved

        filename (str): Base filename for the exported Excel file (without timestamp)

        include_time_stamp (int): Whether to include a timestamp in the filename
            0 = No timestamp, 1 = Include timestamp (default)

        timestamp_format (str): Format for the timestamp in the filename
            Uses Excel date format codes

    Query Parameters:
        column_groups (str): Column grouping for specific tables
            Format: "column_groups": "col1-col2:1,col3-col4:2"
            This creates collapsible column groups in Excel:
            - col1 through col2 will be grouped at level 1 (highest level)
            - col3 through col4 will be grouped at level 2 (nested within level 1)
            You can specify columns by name or by index (0-based)
            Examples:
            - Using column names: "pos-material_description:1,qty-unit:2"
            - Using column indices: "0-3:1,4-5:2"
    """
    if not save_file or not save_file.endswith(".xlsx"):
        return "Save file location must specified and an Excel file."

    tables = TABLE_COLUMNS.keys()

    field_map = getSimpleFieldMap()

    field_map = [{"field": f, "display": d.get("display", f)} for f, d in field_map.items()]

    for f, d in other_field_map.items():
        field_map.append({"field": f, "display": d})

    # Global field map
    field_map_df = pd.DataFrame(field_map)

    field_map_df.sort_values(by="field", ascending=True, inplace=True)

    queries_df = pd.DataFrame(QUERIES)
    queries_df.sort_values(by="id", ascending=True, inplace=True)

    results = {}

    for table in tables:
        print(f"Querying columns for table: {table}")

        columns = TABLE_COLUMNS.get(table, {})
        if not columns:
            print("Warning: No columns found for table: " + table)

        # if not table.endswith(["atem_rfq", "bom"]):
        if table.endswith("general"):
            columns = {j: 1 for j in INITIAL_GENERAL_COLUMNS} | columns
        elif table.endswith("bom"):
            columns = {j: 1 for j in INITIAL_COLUMNS} | columns

        for field, export in columns.items():
            do_not_export = 1 if not export else None
            # display = other_field_map.get(field, None)
            # if not display:
            #     display = field_map.get(field, {}).get("display", field)
            res = {
                "field": field,
                "do_not_export": do_not_export,
                # "display": display,
                "min_column_width": None,
                "max_column_width": None,
                "background_color": None,  # Default no fill
                "foreground_color": None,  # Default color
            }
            results.setdefault(table, []).append(res)

    # Global Export Parameters
    export_params = {
        "use_field_name": 0,
        "bold_header_title": 1,
        "auto_adjust_column_width": 1,
        "max_column_width": 0,
        "min_column_width": 0,
        "save_dir": "debug",
        "filename": "export_project.xlsx",
        "include_time_stamp": 1,
        "timestamp_format": "YYYY-MM-DD-HHMMSS",
    }

    # Create directory if it doesn't exist
    save_dir = os.path.dirname(save_file)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"Created directory: {save_dir}")


    # Save to Excel file
    try:
        print(f"Saving export profile to {save_file}")

        # Create a Pandas Excel writer using XlsxWriter as the engine
        with pd.ExcelWriter(save_file, engine='xlsxwriter') as writer:
            # Save export_params to the "params" sheet
            params_df = pd.DataFrame(list(export_params.items()), columns=['Parameter', 'Value'])
            params_df.to_excel(writer, sheet_name='params', index=False)

            # Get the xlsxwriter workbook and worksheet objects for params
            workbook = writer.book
            worksheet = writer.sheets['params']

            # Auto-size columns for params sheet
            for idx, col in enumerate(params_df.columns):
                # Get the maximum length of the column
                max_len = max(
                    params_df[col].astype(str).map(len).max(),  # Length of largest item
                    len(str(col))  # Length of column name
                ) + 2  # Add a little extra space
                worksheet.set_column(idx, idx, max_len)  # Set column width

            # Save field map to the "field_map" sheet
            field_map_df.to_excel(writer, sheet_name='field_map', index=False)
            worksheet = writer.sheets['field_map']

            # Auto-size columns for field_map sheet
            for idx, col in enumerate(field_map_df.columns):
                # Get the maximum length of the column
                max_len = max(
                    field_map_df[col].astype(str).map(len).max(),  # Length of largest item
                    len(str(col))  # Length of column name
                ) + 2  # Add a little extra space
                worksheet.set_column(idx, idx, max_len)  # Set column width

            # Save custom queries to the "custom_queries" sheet
            queries_df.to_excel(writer, sheet_name='queries', index=False)
            worksheet = writer.sheets['queries']

            # Auto-size columns for custom_queries sheet
            for idx, col in enumerate(queries_df.columns):
                # Get the maximum length of the column
                max_len = max(
                    queries_df[col].astype(str).map(len).max(),  # Length of largest item
                    len(str(col))  # Length of column name
                ) + 2  # Add a little extra space
                worksheet.set_column(idx, idx, max_len)  # Set column width

            # Save each table's columns to its own sheet
            for table, table_data in results.items():
                # Convert list of dictionaries to DataFrame
                sheet_name = table  # Use table name without schema as sheet name
                table_df = pd.DataFrame(table_data)
                table_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # Get the worksheet object
                worksheet = writer.sheets[sheet_name]

                # Create formats for each color combination
                formats = {}

                # Auto-size columns based on content
                for idx, col in enumerate(table_df.columns):
                    # Get the maximum length in the column
                    column_len = table_df[col].astype(str).map(len).max()

                    # Get the length of the column name
                    header_len = len(str(col))

                    # Set width to the max plus some padding
                    width = max(column_len, header_len) + 2

                    # Set column width
                    worksheet.set_column(idx, idx, width)

                # Apply cell formatting for color columns
                if 'background_color' in table_df.columns and 'foreground_color' in table_df.columns:
                    bg_idx = table_df.columns.get_loc('background_color')
                    fg_idx = table_df.columns.get_loc('foreground_color')

                    # Apply formatting to each row
                    for row_idx, row in enumerate(table_df.itertuples(index=False), start=1):  # Start from 1 to skip header
                        bg_color = getattr(row, 'background_color')
                        fg_color = getattr(row, 'foreground_color')

                        # Write empty string for None values, otherwise write the color value
                        worksheet.write(row_idx, bg_idx, "" if bg_color is None else bg_color)
                        worksheet.write(row_idx, fg_idx, "" if fg_color is None else fg_color)

                # Add a filter to make it easier to sort and filter the data
                worksheet.autofilter(0, 0, len(table_df), len(table_df.columns) - 1)

        print(f"Successfully saved export profile to {save_file}")
        return "done"

    except Exception as e:
        print(f"Error saving export profile: {e}")
        import traceback
        traceback.print_exc()
        return f"Error saving export profile: {str(e)}"


def plugin_postgres_read_columns(
    tables: List[str] = "public.bom, public.general, public.atem_rfq",
) -> Union[pd.DataFrame, Dict[str, List[Dict[str, Any]]]]:
    """
    Retrieves column information from specified PostgreSQL tables.

    Args:
        tables (List[str]): List of tables to get column information for.
                           Format should be schema.table_name (e.g., "public.bom")
        schema_only (bool): If True, only include columns from the specified schema.
                           If False, include inherited columns from parent tables.
        as_dataframe (bool): Return results as pandas DataFrame if True, else as dict of lists.
        include_data_types (bool): Include data type information for each column.

    """
    print(f"Retrieving column information for tables: {tables}")

    # Validate input
    if not tables:
        print("Error: No tables specified.")
        return pd.DataFrame() if as_dataframe else {}

    # Prepare query
    base_query = """
    SELECT
        column_name
    FROM
        information_schema.columns
    WHERE
        table_schema || '.' || table_name = %s
    """

    if schema_only:
        # Only include columns defined in the table itself, not inherited
        base_query += " AND NOT is_generated = 'ALWAYS'"

    base_query += " ORDER BY ordinal_position;"

    results = {}
    all_columns = []

    tables: list[str] = [t.strip() for t in tables.split(',')]

    try:
        with get_db_connection() as conn:
            print("Database connection established.")

            for table in tables:
                print(f"Querying columns for table: {table}")

                with conn.cursor() as cursor:
                    cursor.execute(base_query, (table,))
                    columns = [desc[0] for desc in cursor.description]
                    table_columns = cursor.fetchall()

                    if not table_columns:
                        print(f"No columns found for table: {table}")
                        results[table] = []
                        continue

                    # Process results
                    table_results = []
                    for row in table_columns:
                        column = row[0]

                        # Add to table-specific results
                        table_results.append({column})

                        print(f"    \"{column}\",")

                    results[table] = table_results
                    print(f"Found {len(table_results)} columns for table: {table}")

    except Exception as e:
        print(f"Error retrieving column information: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame() if as_dataframe else {}

if __name__ == "__main__":
    # Example usage
    result = plugin_postgres_generate_export_profile()
    print(result)
