# AIS Web Portal Database Integration - Developer Task

## Project Overview

You are tasked with connecting the AIS web ecosystem (AIS Web, AIS Client Portal, and AIS Company Portal) to the PostgreSQL database. This integration will enable a complete workflow from client project submission through internal project management.

## System Architecture

### Web Applications
- **AIS Web**: Public website where clients submit projects
- **AIS Client Portal**: Client-facing portal for project tracking and status monitoring
- **AIS Company Portal**: Internal portal for AIS team project management

### Database Tables (PostgreSQL)
- `atem_clients`: Client company information with domain-based identification
- `organization_members`: Individual users within client organizations
- `client_submissions`: Project submissions before they become official projects
- `file_uploads`: Document storage metadata (files stored in Digital Ocean Spaces)
- `submission_comments`: Comments and communication threads
- `atem_projects`: Official projects (created after submission acceptance)

## Core Workflow Requirements

### Phase 1: Client Submission Process

#### 1. User Authentication & Company Detection
**Objective**: Automatically identify client companies and manage user accounts

**Implementation Requirements**:
- Extract email domain from user input (e.g., `<EMAIL>` → `company.com`)
- Query `atem_clients` table using `client_domain` field to check if company exists
- If company doesn't exist: Prompt user to create company profile
- Query `organization_members` table using `email` field to check if user exists
- If user doesn't exist: Prompt user to create user profile
- **Important**: Remove "Company Name" field from frontend form - determine automatically from email domain

#### 2. Company Registration Flow
**Trigger**: When email domain not found in `atem_clients.client_domain`

**Database Operations**:
```sql
INSERT INTO atem_clients (client_name, client_domain, contact_name, contact_email, contact_phone, address)
VALUES (?, ?, ?, ?, ?, ?)
```

**Required Fields**:
- `client_name`: Company name (user input)
- `client_domain`: Extracted from email (e.g., "company.com")
- `contact_name`, `contact_email`, `contact_phone`, `address`: From user input

#### 3. User Registration Flow
**Trigger**: When email not found in `organization_members.email`

**Database Operations**:
```sql
INSERT INTO organization_members (client_id, email, client_domain, first_name, last_name, phone, title, )
VALUES (?, ?, ?, ?, ?, ?, ?)
```

**Required Fields**:
- `client_id`: Retrieved from `atem_clients` table
- `email`: User email address
- `client_domain`: Extracted domain
- `first_name`, `last_name`, `phone`, `title`: From user input
- `firebase_uid`: From authentication system ** Architekt handles authentication in Firebase and will handle this field manually for now. It should not be required in form submission **

#### 4. Project Submission
**Objective**: Store project details and uploaded documents

**Database Operations**:
```sql
-- Create submission record
INSERT INTO client_submissions (client_project_name, client_project_id, project_description, client_id, submitted_by, project_scope, engineering_company, drawings_count, project_deadline)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)

-- Store each uploaded file
INSERT INTO file_uploads (filename, storage_path, content_type, size_bytes, file_category, submission_id, uploaded_by)
VALUES (?, ?, ?, ?, ?, ?, ?)

** `storage_path` should be the full path to the file in Digital Ocean Spaces. ** 

-- Store submission comments if provided
INSERT INTO submission_comments (content, comment_type, submission_id, author_id, author_name, author_type)
VALUES (?, ?, ?, ?, ?, ?)
```

#### 5. File Upload Progress Tracking
**Objective**: Real-time progress indication during file uploads

**Technical Requirements**:
- Implement chunked file upload to Digital Ocean Spaces
- Track upload progress percentage (0% → 100%)
- Update `file_uploads.processing_status` field:
  - `pending`: Initial state
  - `uploading`: During upload process
  - `completed`: Upload successful
  - `failed`: Upload failed
- Display progress bar with actual transfer progress, not just form submission

#### 6. Email Notifications
**Trigger**: Successful project submission

**Email Recipients**:
1. **Client Confirmation** (`client_confirmation.html`)
   - Send to: `organization_members.email` (submitter)
   - Include: Project details, submission ID, next steps, etc in the existing HTML template format.

2. **Internal Notification** (`project_notification.html`)
   - Send to: AIS team distribution list
   - Include: Client name, company name, project summary, submission timestamp in the existing HTML template format

### Phase 2: Portal Integration

#### 7. AIS Company Portal - Projects View
**Objective**: Display all client submissions for internal team management

**API Endpoint Requirements**:
```sql
SELECT 
    cs.id,
    cs.client_project_name,
    cs.project_description,
    cs.submission_status,
    cs.priority,
    cs.created_at,
    ac.client_name,
    om.first_name || ' ' || om.last_name as submitted_by_name,
    COUNT(fu.id) as file_count
FROM client_submissions cs
JOIN atem_clients ac ON cs.client_id = ac.id
JOIN organization_members om ON cs.submitted_by = om.id
LEFT JOIN file_uploads fu ON cs.id = fu.submission_id
GROUP BY cs.id, ac.client_name, om.first_name, om.last_name
ORDER BY cs.created_at DESC
```

**Features Required**:
- Sortable columns (date, status, client, priority)
- Filterable by status, client, date range
- Click-through to detailed submission view
- Status update functionality (pending → received → accepted → in_progress)

#### 8. AIS Client Portal - Projects View
**Objective**: Display client-specific submissions with row-level security

**API Endpoint Requirements**:
```sql
SELECT 
    cs.id,
    cs.client_project_name,
    cs.client_project_id,
    cs.project_description,
    cs.submission_status,
    cs.created_at,
    cs.project_deadline,
    COUNT(fu.id) as file_count
FROM client_submissions cs
LEFT JOIN file_uploads fu ON cs.id = fu.submission_id
WHERE cs.client_id = ? -- Authenticated user's client_id
GROUP BY cs.id
ORDER BY cs.created_at DESC
```

**Security Requirements**:
- Implement row-level security based on authenticated user's `client_id`
- Users can only see submissions from their own organization
- Validate user authentication before data access

### Phase 3: Project Lifecycle Management

#### 9. Submission to Project Conversion
**Trigger**: AIS team updates submission status to "accepted"

**Database Operations**:
```sql
-- Create official project
INSERT INTO atem_projects (client_id, project_name, location, ais_project_status, received_from_client)
SELECT client_id, client_project_name, 'TBD', 'active', created_at
FROM client_submissions WHERE id = ?

-- Update file_uploads to link to new project
UPDATE file_uploads 
SET project_id = ? 
WHERE submission_id = ?

-- Update submission status
UPDATE client_submissions 
SET submission_status = 'accepted', updated_at = CURRENT_TIMESTAMP 
WHERE id = ?
```

## Technical Implementation Guidelines

### Authentication & Authorization
- Integrate with Firebase Authentication for user management
- Store `firebase_uid` in `organization_members` table
- Implement JWT token validation for API endpoints
- Use middleware to extract user context from tokens

### API Design Patterns
- RESTful endpoints with consistent response formats
- Implement proper HTTP status codes
- Use pagination for large datasets
- Include metadata in responses (total count, page info)

### Error Handling
- Graceful handling of database connection failures
- Validation of required fields before database operations
- Meaningful error messages for client-side display
- Logging of all database operations for debugging

### Performance Considerations
- Use database indexes on frequently queried fields
- Implement connection pooling for database access
- Cache frequently accessed data (client lists, status options)
- Optimize file upload handling for large documents

### Data Validation
- Email format validation and domain extraction
- File type and size validation before upload
- Required field validation on both client and server side
- SQL injection prevention through parameterized queries

## Testing Requirements

### Unit Tests
- Database connection and query functions
- Email domain extraction logic
- File upload progress tracking
- Authentication middleware

### Integration Tests
- Complete submission workflow (company creation → user creation → project submission)
- Portal data retrieval with proper filtering
- Email notification delivery
- File upload to Digital Ocean Spaces

### Security Tests
- Row-level security enforcement
- Authentication bypass attempts
- SQL injection prevention
- File upload security (malicious files)

## Deployment Considerations

### Environment Configuration
- Database connection strings for different environments
- Digital Ocean Spaces credentials and bucket configuration
- Email service configuration (SMTP/SendGrid)
- Firebase project configuration

### Database Migrations
- Create migration scripts for new table structures
- Ensure backward compatibility during updates
- Plan for data migration from existing systems

### Monitoring & Logging
- Database query performance monitoring
- File upload success/failure rates
- Email delivery confirmation
- User authentication events

## Success Criteria

1. **Functional Requirements**:
   - Users can submit projects without manual company name entry
   - Real-time file upload progress indication
   - Automatic email notifications on submission
   - Portal data displays correctly with proper security

2. **Performance Requirements**:
   - File uploads complete within reasonable time for typical document sizes
   - Portal pages load within 2 seconds
   - Database queries execute efficiently under normal load

3. **Security Requirements**:
   - Users can only access their organization's data
   - All database operations are protected against injection attacks
   - File uploads are validated and stored securely

4. **User Experience**:
   - Intuitive workflow with clear error messages
   - Responsive design across devices
   - Consistent branding and styling across portals

## Next Steps After Implementation

1. **Phase 4**: Advanced project management features (task assignment, timeline tracking, subscription/client management)
2. **Phase 5**: Document processing automation (PDF parsing, BOM extraction) on the server side triggered on upload.
3. **Phase 6**: Reporting and analytics dashboards
4. **Phase 7**: Mobile application development

---

**Note**: This document provides the framework and requirements. Implementation details should be adapted based on your chosen technology stack and existing codebase patterns. Focus on building a robust, secure foundation that can scale with future requirements.
