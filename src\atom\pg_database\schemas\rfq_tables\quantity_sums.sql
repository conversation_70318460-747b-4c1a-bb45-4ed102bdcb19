-- Properly Qualified BOM and General Quantity Sums
-- All column references are properly qualified to avoid ambiguity errors

CREATE OR REPLACE FUNCTION public.get_bom_general_quantity_sums(
    p_project_id INTEGER DEFAULT NULL
)
RETURNS TABLE (
    project_id INTEGER,
    pdf_id INTEGER,
    pdf_page INTEGER,
    bom_quantity_sum NUMERIC,
    general_quantity_sum NUMERIC,
    difference NUMERIC
)
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(b.project_id, g.project_id) AS project_id,
        COALESCE(b.pdf_id, g.pdf_id) AS pdf_id,
        COALESCE(b.pdf_page, g.pdf_page) AS pdf_page,
        COALESCE(b.quantity_sum, 0) AS bom_quantity_sum,
        COALESCE(g.quantity_sum, 0) AS general_quantity_sum,
        COALESCE(g.quantity_sum, 0) - COALESCE(b.quantity_sum, 0) AS difference
    FROM (
        -- Sum of quantity column in BOM
        SELECT 
            bom_table.project_id,
            bom_table.pdf_id,
            bom_table.pdf_page,
            SUM(bom_table.quantity) AS quantity_sum
        FROM public.bom AS bom_table
        WHERE (p_project_id IS NULL OR bom_table.project_id = p_project_id)
        AND bom_table.pdf_id IS NOT NULL 
        AND bom_table.pdf_page IS NOT NULL
        GROUP BY bom_table.project_id, bom_table.pdf_id, bom_table.pdf_page
    ) AS b
    FULL OUTER JOIN (
        -- Sum of quantity column in general
        SELECT 
            gen_table.project_id,
            gen_table.pdf_id,
            gen_table.pdf_page,
            SUM(gen_table.quantity) AS quantity_sum
        FROM public.general AS gen_table
        WHERE (p_project_id IS NULL OR gen_table.project_id = p_project_id)
        GROUP BY gen_table.project_id, gen_table.pdf_id, gen_table.pdf_page
    ) AS g ON 
        b.project_id = g.project_id AND
        b.pdf_id = g.pdf_id AND
        b.pdf_page = g.pdf_page
    ORDER BY 
        COALESCE(b.project_id, g.project_id),
        COALESCE(b.pdf_id, g.pdf_id),
        COALESCE(b.pdf_page, g.pdf_page);
END;
$$ LANGUAGE plpgsql;
