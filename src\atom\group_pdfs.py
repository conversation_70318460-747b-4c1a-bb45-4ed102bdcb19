"""
THIS VERSION IS DEPRECATED. SEE src.atom.grouppdfs.grouppdfs
PDF Grouping
"""
from typing import Iterable
import fitz  # PyMuPDF
import pandas as pd
import os
import shutil
import tempfile
import multiprocessing
from multiprocessing import freeze_support, Manager, Lock, Event
import time
from fitz import Rect
from pypdf import PdfReader, PdfWriter
import os
from src.utils.logger import logger
from pubsub import pub
import asyncio
import statistics
from threading import Thread


# logger = logging.getLogger()

# For performance boost, speed up using multiprocessing
MULTIPROCESS: bool = True

# Debug flags

# Save results to local .xlsx file
SAVE_DATAFRAME_RESULTS: bool = True
# Split and generate PDF groups to output_path
CREATE_GROUPED_PDFS: bool = True

lock = multiprocessing.Lock()



def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def is_within_tolerance(val1, val2, tolerance=2):
    return abs(val1 - val2) <= tolerance

def save_pdf(original_doc_path: str, output_path: str, group_number: int, pages_to_keep: Iterable):
    """Keep this as outer function so multiprocessing can pickle it"""
    original_filename = os.path.splitext(os.path.basename(original_doc_path))[0]
    output_filename = f"{original_filename}_group_{group_number}.pdf"
    output_file_path = os.path.join(output_path, output_filename)  

    reader = PdfReader(original_doc_path)
    writer = PdfWriter()
    for page_num, page in enumerate(reader.pages, 1):
        if page_num in pages_to_keep: # +1 not needed here as enum starts at 1
            writer.add_page(page)
    with open(output_file_path, 'wb') as out:
        writer.write(out)

    print(f"Created: {output_filename}")

def create_grouped_pdfs(df, original_doc_path, output_path, cancel_event: asyncio.Event):
    """Two-stage to create grouped pdfs. 
    
    First stage is to split and save using pypdf
    Second stage is to reopen these grouped docs and annotate them
    """
    print()
    print("Saving grouped pdfs...")
    grouped = df.groupby('group_number')

    # PyPDF to split docs
    n = multiprocessing.cpu_count()
    with multiprocessing.Pool(n) as pool:
        # Can't seem to pass group_df directly to a process, so pass the page number list
        pool.starmap(save_pdf, [(original_doc_path, output_path, group_number, set(group_df['page_number'].tolist())) for group_number, group_df in grouped])

    if cancel_event.is_set():
        return

    # Reopen saved files with PyMuPDF and add annotations
    original_filename = os.path.splitext(os.path.basename(original_doc_path))[0]
    for group_number, group_df in grouped:
        pages_to_keep = list(set(group_df['page_number'].tolist()))
        pages_to_keep.sort()
        output_filename = f"{original_filename}_group_{group_number}.pdf"
        output_file_path = os.path.join(output_path, output_filename) 
        print(f"Drawing results on pdf: {output_file_path}")
        src = fitz.open(output_file_path)

        first_row = group_df.iloc[0]
        page: fitz.Page = None
        for n, page in enumerate(src):
            page.remove_rotation()
            actual_page = pages_to_keep[n]
            margin_rect = [first_row["left_margin"], 
                        first_row["top_margin"], 
                        page.rect.width - first_row["right_margin"], 
                        page.rect.height - first_row["bottom_margin"]]
            # print(margin_rect)
            try:
                page.insert_text((50,50), f"Group: {group_number}, Page: {actual_page}", fontsize=30, color=(0, 0, 1))
            except Exception as e:
                logger.info("Failed to insert PDF text", exc_info=True)

            try:
                page.insert_text((50,100), f"Margin Rect: {margin_rect}", fontsize=30, color=(0, 0, 1))
            except Exception as e:
                logger.info("Failed to insert PDF text", exc_info=True)

            try:
                page.add_rect_annot(margin_rect)
            except Exception as e:
                logger.info("Failed to annotate PDF margins rect", exc_info=True)

        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_path = temp_file.name
        src.save(temp_path, deflate=True, garbage=4)
        shutil.move(temp_path, output_file_path)

        if cancel_event.is_set():
            return

    # Update the dataframe with the new page numbers
    for group_number, group_df in grouped:
        group_df['new_page_number'] = range(1, len(group_df) + 1)
        df.loc[group_df.index, 'new_page_number'] = group_df['new_page_number']


class CancelException(Exception):
    
    def __init__(self):
        super().__init__()

def process_page(doc, 
                 doc_path: str, 
                 page_num: int, 
                 shared_mem: list,
                 process_times: list, 
                 cancel_event: asyncio.Event):
    
    def check_cancelled():
        # Raising exception helps to break out of starmap
        # more immediately
        if cancel_event.is_set():
            raise CancelException

    check_cancelled()

    start = time.time()

    #print(f"Processing Page {page_num + 1}")
    page: fitz.Page = doc[page_num]
    page.remove_rotation()
    width, height = round(page.rect.width, 2), round(page.rect.height, 2)

    # When a outer box is found, cache it in shared_mem
    # Use index to check when new results are added
    shared_mem_index = 0 # Keep last index check

    crop_box = tuple(round(v, 2) for v in page.cropbox)
    media_box = tuple(round(v, 2) for v in page.mediabox)
    rotation = page.rotation
    rotation_matrix = page.rotation_matrix

    c_drawings = page.get_cdrawings(extended=False)
    
    # Initialize variables to track the outermost drawing
    left_margin = width
    right_margin = 0
    top_margin = height
    bottom_margin = 0

    # Extract text blocks and annotations to exclude them from the calculations
    text_blocks = page.get_text("blocks")
    annotations = page.annots()

    excluded_areas = []

    for block in text_blocks:
        bbox = block[:4]
        adjusted_bbox = adjust_bbox(bbox, rotation_matrix)
        excluded_areas.append(adjusted_bbox)

    if annotations:
        for annot in annotations:
            bbox = annot.rect
            adjusted_bbox = adjust_bbox((bbox.x0, bbox.y0, bbox.x1, bbox.y1), rotation_matrix)
            excluded_areas.append(adjusted_bbox)

    def is_in_excluded_area(bbox):
        for area in excluded_areas:
            if (bbox[0] >= area[0] and bbox[2] <= area[2] and
                bbox[1] >= area[1] and bbox[3] <= area[3]):
                return True
        return False

    # Find the outer most rect
    largest_rect = None
    largest_rect_area: float = None
    line_widths = []
    line_heights = []
    fallback: int = 0  # Status flag for debugging, if no bounds found use page rect as bounds
    found = None
    for n, drawing in enumerate(c_drawings):

        # On each drawing check, see if other page processing have added their outer box
        # And recheck to see if it applies this page so we can quit early
        if shared_mem_index < len(shared_mem):
            for result_index in range(shared_mem_index, len(shared_mem)):
                result_drawings, margins = shared_mem[result_index]
                try:
                    matched = [str(c_drawings[index]["rect"]) == str(rect2) for index, rect2 in result_drawings]
                    found = all(matched)
                except IndexError:
                    pass   # Drawings may have more/less items
                if found:
                    found = shared_mem[result_index]
                    logger.debug("Found result from other process. End early")
                    break
            if found:
                break
            shared_mem_index = len(shared_mem)

        if 'rect' in drawing:
            rect = drawing['rect']
            r = Rect(rect)
            # Ignore small rects which are not considered lines
            if r.width < 100 and r.height < 100:
                continue

            # Continue checking lines
            if r.get_area() == 0:
                if r.width < 100 and r.height > page.rect.height // 1.5:
                    # Vertical line
                    line_heights.append((n, drawing))
                elif r.height < 100 and r.width > page.rect.width // 1.5:
                    # Horizontal line
                    line_widths.append((n, drawing))
                continue

            # Check box is full size rect and within bounds of page
            if not all((r.width < page.rect.width, r.height < page.rect.height)):
                continue 
            if r.get_area() > page.rect.get_area() * 0.7:
                if not largest_rect_area or r.get_area() > largest_rect_area:
                    largest_rect = (n, drawing)
                    largest_rect_area = r.get_area()

    if not found:
        left_rect, left_margin = None, None
        top_rect, top_margin = None, None
        right_rect, right_margin = None, None
        bottom_rect, bottom_margin = None, None

        for n, drawing in line_widths:
            r = Rect(drawing["rect"])
            if not top_margin or r.y0 < top_margin.y0:
                top_margin = r
                top_rect = (n, drawing["rect"])
            if not bottom_margin or r.y1 > bottom_margin.y1:
                bottom_margin = r
                bottom_rect = (n, drawing["rect"])

        for n, drawing in line_heights:
            r = Rect(drawing["rect"])
            if not left_margin or r.x0 < left_margin.x0:
                left_margin = r
                left_rect = (n, drawing["rect"])
            if not right_margin or r.x1 > right_margin.x1:
                right_margin = r
                right_rect = (n, drawing["rect"])

        def rectFromMargins(left, top, right, bottom) -> Rect:
            return Rect([left[0], top[1], right[2], bottom[3]])

        # Fallback, if no largest rect nor line margins, use page rect until better solution found
        cache_result = None # To be shared between processes
        outerBox = None
        if all([left_margin, top_margin, right_margin, bottom_margin]):
            cache_result = [left_rect, top_rect, right_rect, bottom_rect]
            outerBox = rectFromMargins(left_rect[1], top_rect[1], right_rect[1], bottom_rect[1])

        if largest_rect and outerBox:
            r = Rect(largest_rect[1]["rect"])
            if r.get_area() > outerBox.get_area():
                outerBox = r
                cache_result = [largest_rect]
                left_margin = top_margin = right_margin = bottom_margin = r
        elif largest_rect and not outerBox:
            r = Rect(largest_rect[1]["rect"])
            outerBox = r
            cache_result = [largest_rect]
            left_margin = top_margin = right_margin = bottom_margin = r
        elif outerBox:
            pass
        else:
            # Fallback, use page rect. We don't cache result for this
            fallback = 1
            page_rect = Rect(page.rect)
            left_margin = page_rect.x0
            right_margin = page_rect.x1
            top_margin = page_rect.y0
            bottom_margin = page_rect.y1
    
        if not fallback:
            # Calculate margins
            left_margin = round(left_margin.x0, 2)
            right_margin = round(width - right_margin.x1, 2)
            top_margin = round(top_margin.y0, 2)
            bottom_margin = round(height - bottom_margin.y1, 2)

        if cache_result:
            lock.acquire()
            if cache_result not in shared_mem:
                margins = [left_margin, top_margin, right_margin, bottom_margin]
                shared_mem.append((cache_result, margins))
            lock.release()

    else:
        result_drawings, box_rect = found
        left_margin, top_margin, right_margin, bottom_margin = box_rect

    logger.debug("Final outer box", [left_margin, top_margin, right_margin, bottom_margin], "Using fallback bounds?", fallback)
    logger.debug(f"Page {page_num + 1} width: {width}, height: {height}, rotation: {rotation}")

    # Close the document
    doc.close()

    # Add page info to results
    result = {
        'document_path': doc_path,
        'page_number': page_num + 1,
        'group_number': None,  # defer assigning groups until all all pages processed
        'width': width,
        'height': height,
        'crop_box': crop_box,
        'media_box': media_box,
        'left_margin': left_margin,
        'right_margin': right_margin,
        'top_margin': top_margin,
        'bottom_margin': bottom_margin,
        'rotation': rotation,
        'fallback': fallback,
    }

    process_time = time.time() - start
    lock.acquire()
    process_times.append(process_time)
    lock.release()

    return result


def group_pages_by_layout_multiprocess(filename, page_num: int, shared_mem: list, process_times: list, cancel_event):
    doc = fitz.open(filename)
    return process_page(doc, filename, page_num, shared_mem, process_times, cancel_event)


class GroupPdfs():

    def __init__(self, 
                 filename: dict,
                 pages = None,
                 jobId: str = None,
                 output_path = None,
                 multiprocess = True):
        self.jobId: str = jobId
        self.filename: str = filename
        self.pages = pages
        self.output_path = output_path
        self._multiprocess = multiprocess
        self._result = None
        self._cancel = Event()
        self._total_page_processes = None
        self._time_of_last_request = None
        self._times = []
        self._eta_updated_cb = None
        self._last_eta = None
        self._timer = Thread(target=self.on_timer)
        self._result_df = pd.DataFrame()

        self.doc = None

        if self._multiprocess:
            manager = Manager()
            self._cancel = manager.Event()

    def process_pages(self):
        """Process PDF and group pages and return a dataframe.

        Args:
            filename: Input PDF filename.
            output_path: Optionally generate.
            pages: A list[int] of pages to be processed. If None is supplied, all pages
                are analyzed.
            multiprocess: Enable multiprocessing
                
        Returns:
            A dataframe containing results of pages grouped
        """

        # Ensure the output directory exists
        if self.output_path:
            os.makedirs(self.output_path, exist_ok=True)

        if self.pages:
            pages = [int(p) for p in self.pages]
        else:
            pages = range(fitz.open(self.filename).page_count)

        self._total_page_processes = len(pages)

        if self._multiprocess:
            freeze_support()
            manager = Manager()
            shared_mem = manager.list()
            self._times = manager.list()
            with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
                try:
                    results = pool.starmap(group_pages_by_layout_multiprocess, 
                                        [(self.filename, page_num, shared_mem, self._times, self._cancel) for page_num in pages])
                except CancelException:
                    pass # Externally cancelled
                if self._cancel.is_set():
                    return
                results.sort(key=lambda x: x["page_number"])  # Ensure the results are in order of page numbers

        else:
            manager = Manager() # Can still use this without multiprocessing
            shared_mem = manager.list()
            # Group by pages by width, height, mediabox, bbox, vectors(page.get_cdrawings)
            results = []
            self._times = []
            for page_num in pages:
                doc = fitz.open(filename)
                results.append(process_page(doc, 
                                            filename, 
                                            page_num, 
                                            shared_mem,
                                            self._times,
                                            self._cancel))
                if self._cancel.is_set():
                    return

        if self._cancel.is_set():
            return

        # Group by pages by width, height, mediabox, bbox, vectors(page.get_cdrawings)
        groups = {}
        group_counter = 0
        for r in results:
            # Create a signature that includes the page size, margins, and rotation
            page_signature = (r["width"], r["height"], r["left_margin"], r["top_margin"], 
                              r["right_margin"], r["bottom_margin"], r["rotation"])
            # Apply tolerance to the margins
            found_group = False
            for sig in groups:
                if all(is_within_tolerance(sig[i], page_signature[i]) for i in range(len(sig))):
                    current_group = groups[sig]
                    r["group_number"] = current_group
                    found_group = True
                    break

            if not found_group:
                group_counter += 1
                groups[page_signature] = group_counter
                current_group = group_counter
                r["group_number"] = current_group

        # Create a DataFrame from the results
        result_df = pd.DataFrame(results)

        print()
        print(f"Pages processed. Time - {time.time() - self.start_time}")

        if self._cancel.is_set():
            return

        # Save the dataframe
        if SAVE_DATAFRAME_RESULTS:
            result_df.to_excel('Page Group Summary.xlsx', index=False)

        print(f"Finished. Time elapsed - {time.time() - self.start_time}")

        self._result_df = result_df
        return result_df

    def create_grouped_pdfs(self):
        """Optional step for debugging"""
        # Create new grouped PDFs by size group
        if CREATE_GROUPED_PDFS and self.output_path:
            if self._cancel.is_set():
                return
            create_grouped_pdfs(self._result_df, self.filename, self.output_path, self._cancel)
            print(f"Saved grouped pdfs processed. Time - {time.time() - self.start_time}")

    def run(self):
        """Run classification"""
        pub.sendMessage("set-statusbar-realtime", message="Preprocessing Started", jobId=self.jobId)
        self.start_time = time.time()  # Capture the start time
        self._timer.start()
        for n, state in enumerate([
            self.process_pages,
            self.create_grouped_pdfs,
        ]):
            if self._cancel.is_set():
                print(f"Cancelled preprocessing at step {n} - function `{state.__name__}`")
                self._result_df = None
                return
            state()

        self._cancel.set()  # Done

    def cancel(self):
        """Cancel classification and all running tasks"""
        logger.debug("Cancelling classification")
        self._cancel.set()

    def on_timer(self):
        smoothing_factor = 0.05
        while not self._cancel.is_set():
            time.sleep(1)
            if not self._eta_updated_cb:
                continue

            if self._total_page_processes is None:
                continue
            lock.acquire()
            times = self._times[-5:]
            remaining_pages = max(0, self._total_page_processes - len(self._times))
            lock.release()
            if len(times) < 5:
                continue
            eta = int(statistics.fmean(times) * remaining_pages)
            if self._last_eta is None:
                self._last_eta = eta
                continue
            eta = smoothing_factor * self._last_eta + (1 - smoothing_factor) * eta
            self._last_eta = int(eta)
            if self._eta_updated_cb:
                self._eta_updated_cb(self._last_eta)

        if self._eta_updated_cb:
            self._eta_updated_cb(0)

    def results_valid(self) -> bool:
        """Returns valid status of results
        
        Validity is only based on page count matching df row count. Not
        a reliable measure for data quality
        """
        print(self._total_page_processes == self._result_df.shape[0], self._total_page_processes, self._result_df.shape[0])
        if self._total_page_processes and self._total_page_processes == self._result_df.shape[0]:
            return True
        return False


if __name__ == "__main__":
    filename = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Excel USA\Excel 008\ISOs.A.pdf"
    output_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Excel USA\Excel 008"

    filename = r"/home/<USER>/Desktop/0 Binder.pdf"
    filename = r"/home/<USER>/Desktop/Binder1_original_test.pdf"
    filename = r"/home/<USER>/Desktop/pdf_group_790.0_610.0.pdf"
    output_path = r"/home/<USER>/Desktop/grouped_output"
    pages = None # All pages
    # pages = [199] # Page 200

    processor = GroupPdfs(filename=filename,
                        pages=pages,
                        output_path=output_path, 
                        multiprocess=True)

    def print_eta(eta):
        print(f"ETA: {eta}")

    def test_cancel(n=None):
        # Test function to cancel processing after `n` seconds
        import time
        if n is None:
            return
        time.sleep(n)
        processor.cancel()

    time_to_cancel = None
    Thread(target=lambda: test_cancel(time_to_cancel)).start()
    processor._eta_updated_cb = print_eta
    processor.run()
    print("Result valid -", processor.results_valid())

    print("Result DF")
    print(processor._result_df)
