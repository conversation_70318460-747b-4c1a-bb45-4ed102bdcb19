# ATEM (Agile Takeoff & Estimation Model)

<img align="left" src="resources/images/icon.ico" width="64" height="64" />

An advanced PDF analysis and data extraction tool designed for construction and engineering professionals.

<br clear="all" />

## Overview

ATEM is a specialized application designed to streamline the process of extracting, analyzing, and organizing data from engineering blueprints and technical documents. It employs advanced computer vision and text recognition technologies to simplify what has traditionally been a time-consuming manual process.

By combining an intuitive interface with powerful extraction capabilities, ATEM enables users to quickly identify and capture critical information from construction documents, creating a structured dataset that can be used for estimation, planning, and analysis.

## Key Features

### Project Management

ATEM organizes work into project-based workspaces, allowing users to:

- Create and manage multiple projects
- Import and organize PDF documents within each project
- Track project metadata (client, location, scope, etc.)
- Maintain project-specific configurations and templates

### PDF Document Processing

The application provides robust document handling capabilities:

- Load and view construction blueprints and technical PDFs
- Navigate through multi-page documents with intuitive controls
- Automatically detect and group similar pages for batch processing
- Configure document-specific settings including vendor information and processing options
- Support for large-format documents common in engineering and construction fields

### ROI (Region of Interest) Extraction

At the core of ATEM is the ability to define and extract data from specific regions:

- Select rectangular regions on blueprints containing important information
- Define regions as either simple areas or tabular structures with multiple columns
- Name and categorize regions for consistent data organization
- Save ROI layouts as templates for reuse across similar documents
- Synchronize ROI definitions across document groups to speed up processing
- Interactive visualization of defined regions overlaid on documents

### Data Recognition and Extraction

ATEM leverages advanced recognition technologies:

- Extract text from defined regions using OCR (Optical Character Recognition)
- Process and interpret extracted content into structured data
- Support for parsing tabular data with multi-column detection
- Preview extraction results before finalizing
- Batch processing capabilities for efficiency

### Data Organization and Analysis

Once extracted, ATEM helps organize data for immediate use:

- View extracted data in customizable tabular formats
- Filter and sort data based on various criteria
- Categorize information into logical groupings
- Export structured data for use in other applications

## Technical Architecture

### Core Libraries

ATEM is built on several powerful open-source technologies:

#### PyMuPDF/fitz
- Provides the foundation for PDF document handling
- Enables high-quality document rendering for visual analysis
- Supports navigation, zooming, and interaction with PDF content
- Facilitates extraction of document metadata and structure

#### PySide6 (Qt)
- Powers the entire graphical user interface
- Provides a comprehensive framework for desktop application development
- Enables responsive and interactive document viewing
- Supports custom widgets and controls for specialized functionality

#### Pandas
- Handles efficient data manipulation and organization
- Provides the foundation for tabular data representation
- Enables sophisticated filtering, sorting, and analysis capabilities
- Facilitates data export and interchange with other systems

### Component Architecture

ATEM follows a modular architecture with several key components:

#### Main Application (app.py)
- Application entry point and initialization
- Splash screen and update management
- Core application setup and configuration

#### MainWindow
- Primary application container
- Navigation and workspace management
- Integration point for all major views

#### BlueprintReaderView
- Document viewing and interaction
- ROI definition and management
- Integration with extraction pipeline
- Preview functionality for extracted data

#### ROI Management
- `RoiItem`: Core component for defining and visualizing regions of interest
- `RoiSidebar`: Interface for managing ROIs
- `EditRoiDialog`: Dialog for configuring ROI properties and data mapping

#### Page Group Management
- `PageGroups`: System for organizing and categorizing document pages
- Group-level configuration for extraction settings
- Batch operations across document groups

#### Data Extraction Pipeline
- OCR processing for text recognition
- Table structure analysis for columnar data
- Data parsing and normalization
- Result formatting and presentation

## Workflow

ATEM is designed around a streamlined workflow that guides users from document import to data extraction:

### 1. Project Setup
- Create a new project or open an existing one
- Configure project details (name, client, location, etc.)
- Import PDF documents into the project
- Specify document metadata such as vendor/source information

### 2. Document Analysis
- View documents in the BlueprintReaderView
- Use automatic page analysis to detect and group similar pages
- Configure extraction options for page groups
- Define processing parameters for different document types

### 3. ROI Definition
- Navigate to relevant pages in the document
- Use Shift+Left Click to select regions of interest
- Define data types for each region
- For tabular data, configure column definitions
- Save ROI layouts for reuse across similar documents

### 4. Data Extraction
- Run extraction on defined ROIs
- Review extraction results in the preview panel
- Adjust ROIs if needed and re-extract
- Validate extracted data for accuracy

### 5. Data Organization and Export
- Organize extracted data into appropriate categories
- View and filter data in structured tables
- Export data for use in estimation, planning, or reporting systems
- Save project state for future reference or updates

## User Interface Guide

### Navigation Sidebar
- Project navigation and view selection
- Quick access to commonly used features
- Status indicators for processing activities

### Document Viewer
- Central viewing area for PDF documents
- Zoom and pan controls
- ROI visualization overlay
- Page navigation tools

### ROI Management Panel
- List of defined regions of interest
- Tools for creating, editing, and removing ROIs
- Options for synchronizing ROIs across documents

### Page Group Panel
- Hierarchical view of document pages organized by groups
- Configuration options for extraction settings
- Group-level operations for batch processing

### Extraction Preview
- Tabbed interface for viewing extracted data
- Filtering and sorting controls
- Data validation indicators

## Keyboard Shortcuts

ATEM provides several keyboard shortcuts to enhance productivity:

- **Shift + Left Click**: Create new ROI
- **Ctrl + Mouse Wheel**: Zoom in/out of document
- **Middle Mouse Button + Drag**: Pan document
- **Ctrl + S**: Save project
- **Ctrl + E**: Run extraction on selected ROIs
- **Ctrl + Z**: Undo last action
- **Ctrl + Y**: Redo last action

## System Requirements

### Recommended Hardware
- Processor: Quad-core 2.5 GHz or higher
- Memory: 8GB RAM minimum, 16GB recommended
- Storage: 1GB free space plus additional space for project data
- Graphics: Hardware acceleration recommended for larger documents

### Software Requirements
- Operating System: Windows 10/11 (64-bit)
- Python 3.8 or higher
- Required Python packages:
  - PyMuPDF/fitz: PDF processing
  - PySide6: GUI framework (Python bindings for Qt)
  - Pandas: Data analysis and manipulation
  - Numpy: Numerical computing
  - Additional dependencies as specified in requirements.txt

## AI and Machine Learning Features

ATEM incorporates advanced AI capabilities that significantly enhance the document processing workflow:

### Optical Character Recognition (OCR)
- Custom-trained OCR models for technical document text recognition
- Support for engineering symbols and notations
- High accuracy extraction from CAD-generated documents
- Multi-language support for international documents

### Multi-Class Classification
- AI-powered document type identification
- Automatic categorization of drawing sheets (electrical, structural, mechanical, etc.)
- Smart grouping of similar documents for batch processing
- Learning capability that improves with use

### Intelligent Data Extraction
- Pattern recognition for common document elements
- Automatic table structure detection
- Field mapping based on content context
- Confidence scoring for extraction results

### Neural Network Integration
- Deep learning models for complex pattern recognition
- Transfer learning to adapt to specific document types
- Continual improvement through usage feedback
- On-demand processing for resource optimization

These AI capabilities enable ATEM to go beyond simple text extraction, providing intelligent analysis and classification that drastically reduces manual effort and increases accuracy in the takeoff process.

## Development and Extensibility

ATEM is built with extensibility in mind:

### Custom Field Definitions
- Define custom field types for specialized data extraction
- Create templates for industry-specific document types
- Configure validation rules for extracted data

### Integration Capabilities
- Export APIs for connecting with external systems
- Structured data output in standard formats (CSV, JSON, etc.)
- Batch processing support for automated workflows

### Plugin Architecture
- Support for custom extraction modules
- Extensible document processing pipeline
- Integration points for specialized analysis tools

## Technical Troubleshooting

### Common Issues and Solutions

#### PDF Rendering Problems
- Ensure documents are valid PDF format
- Check for document protection/encryption
- Verify sufficient system resources for large documents

#### Extraction Accuracy Issues
- Adjust ROI boundaries for better precision
- Consider document quality and resolution
- Use table extraction for structured data instead of individual ROIs

#### Performance Optimization
- Limit the number of concurrent extraction processes
- Close unused projects to free system resources
- Consider hardware acceleration settings

## Getting Started

To begin using ATEM:

1. Install the application using the provided installer
2. Create a new project or open an example project
3. Import your first PDF document
4. Follow the guided workflow to define ROIs and extract data
5. Explore the extraction results and export options

## Conclusion

ATEM represents a significant advancement in the field of construction document analysis and data extraction. By automating what has traditionally been a manual process, it enables professionals to focus on analysis and decision-making rather than tedious data entry.

With its intuitive interface, powerful extraction capabilities, and flexible data management, ATEM is designed to become an essential tool in the construction estimation and planning workflow.

---

© 2025 Architekt Integrated Systems. All rights reserved.
