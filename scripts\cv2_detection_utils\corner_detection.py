import sys
import cv2
import fitz
import numpy as np
from PySide6.QtWidgets import (QApp<PERSON>, QMainWindow, QWidget, QVBoxLayout,
                              QHBoxLayout, QPushButton, QLabel, QSpinBox,
                              QFileDialog, QComboBox, QMessageBox, QDoubleSpinBox,
                              QCheckBox, QGroupBox, QRadioButton)
from PySide6.QtCore import Qt
from PySide6.QtGui import QImage, QPixmap

class CornerDetectionDialog(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Corner Detection Tool")
        self.setMinimumSize(1200, 800)

        # Initialize variables
        self.doc = None
        self.current_page = None
        self.original_cv_image = None
        self.processed_image = None
        self.MAX_PAGES = 10

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Controls
        controls_layout = QHBoxLayout()

        # File selection
        self.file_btn = QPushButton("Open PDF")
        self.file_btn.clicked.connect(self.open_pdf)
        controls_layout.addWidget(self.file_btn)

        # Page selection
        self.page_combo = QComboBox()
        self.page_combo.currentIndexChanged.connect(self.page_changed)
        controls_layout.addWidget(self.page_combo)

        # Detection method selection
        method_group = QGroupBox("Detection Method")
        method_layout = QHBoxLayout()
        self.harris_radio = QRadioButton("Harris")
        self.shitomasi_radio = QRadioButton("Shi-Tomasi")
        self.canny_radio = QRadioButton("Canny Edges")
        self.harris_radio.setChecked(True)
        method_layout.addWidget(self.harris_radio)
        method_layout.addWidget(self.shitomasi_radio)
        method_layout.addWidget(self.canny_radio)
        method_group.setLayout(method_layout)
        controls_layout.addWidget(method_group)

        # Connect radio buttons
        for radio in [self.harris_radio, self.shitomasi_radio, self.canny_radio]:
            radio.toggled.connect(self.process_image)

        # Parameters
        params_layout = QHBoxLayout()

        # Quality/Threshold
        self.quality_label = QLabel("Quality Threshold:")
        params_layout.addWidget(self.quality_label)
        self.quality_threshold = QDoubleSpinBox()
        self.quality_threshold.setRange(0.01, 1.0)
        self.quality_threshold.setSingleStep(0.01)
        self.quality_threshold.setValue(0.1)
        self.quality_threshold.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.quality_threshold)

        # Min Distance
        self.min_distance_label = QLabel("Min Distance:")
        params_layout.addWidget(self.min_distance_label)
        self.min_distance = QSpinBox()
        self.min_distance.setRange(1, 100)
        self.min_distance.setValue(10)
        self.min_distance.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.min_distance)

        # Block Size
        self.block_size_label = QLabel("Block Size:")
        params_layout.addWidget(self.block_size_label)
        self.block_size = QSpinBox()
        self.block_size.setRange(2, 15)
        self.block_size.setValue(3)
        self.block_size.setSingleStep(2)  # Must be odd
        self.block_size.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.block_size)

        # Max Corners
        self.max_corners_label = QLabel("Max Corners:")
        params_layout.addWidget(self.max_corners_label)
        self.max_corners = QSpinBox()
        self.max_corners.setRange(1, 1000)
        self.max_corners.setValue(100)
        self.max_corners.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.max_corners)

        # Edge parameters (for Canny)
        self.low_threshold_label = QLabel("Low Threshold:")
        params_layout.addWidget(self.low_threshold_label)
        self.low_threshold = QSpinBox()
        self.low_threshold.setRange(0, 255)
        self.low_threshold.setValue(50)
        self.low_threshold.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.low_threshold)

        self.high_threshold_label = QLabel("High Threshold:")
        params_layout.addWidget(self.high_threshold_label)
        self.high_threshold = QSpinBox()
        self.high_threshold.setRange(0, 255)
        self.high_threshold.setValue(150)
        self.high_threshold.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.high_threshold)

        controls_layout.addLayout(params_layout)

        # Preprocessing options
        self.use_preprocessing = QCheckBox("Enable Preprocessing")
        self.use_preprocessing.setChecked(True)
        self.use_preprocessing.stateChanged.connect(self.process_image)
        controls_layout.addWidget(self.use_preprocessing)

        # Save button
        self.save_btn = QPushButton("Save PDF")
        self.save_btn.clicked.connect(self.save_pdf)
        self.save_btn.setEnabled(False)
        controls_layout.addWidget(self.save_btn)

        layout.addLayout(controls_layout)

        # Image display
        display_layout = QHBoxLayout()
        
        # Original image
        self.original_label = QLabel()
        self.original_label.setMinimumSize(500, 600)
        self.original_label.setAlignment(Qt.AlignCenter)
        display_layout.addWidget(self.original_label)

        # Processed image
        self.processed_label = QLabel()
        self.processed_label.setMinimumSize(500, 600)
        self.processed_label.setAlignment(Qt.AlignCenter)
        display_layout.addWidget(self.processed_label)

        layout.addLayout(display_layout)

    def open_pdf(self):
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open PDF", "", "PDF Files (*.pdf)")
        if filename:
            self.doc = fitz.open(filename)
            self.page_combo.clear()
            page_count = min(self.doc.page_count, self.MAX_PAGES)
            self.page_combo.addItems([f"Page {i+1}" for i in range(page_count)])
            self.page_changed(0)
            self.save_btn.setEnabled(True)

    def page_changed(self, index):
        if self.doc is None:
            return

        # Get page
        page = self.doc[index]
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        self.original_cv_image = img
        self.process_image()

    def preprocess_image(self, image):
        """Apply preprocessing steps to enhance corner detection"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)
        
        return enhanced

    def detect_harris_corners(self, gray):
        """Detect corners using Harris corner detection"""
        block_size = self.block_size.value()
        if block_size % 2 == 0:
            block_size += 1
        
        corners = cv2.cornerHarris(gray, block_size, 3, 0.04)
        corners = cv2.dilate(corners, None)
        
        # Threshold for corner detection
        threshold = self.quality_threshold.value()
        img_with_corners = self.original_cv_image.copy()
        img_with_corners[corners > threshold * corners.max()] = [0, 0, 255]
        
        return img_with_corners

    def detect_shitomasi_corners(self, gray):
        """Detect corners using Shi-Tomasi corner detection"""
        corners = cv2.goodFeaturesToTrack(
            gray,
            self.max_corners.value(),
            self.quality_threshold.value(),
            self.min_distance.value()
        )
        
        img_with_corners = self.original_cv_image.copy()
        if corners is not None:
            corners = np.int0(corners)
            for corner in corners:
                x, y = corner.ravel()
                cv2.circle(img_with_corners, (x, y), 3, [0, 0, 255], -1)
        
        return img_with_corners

    def detect_canny_edges(self, gray):
        """Detect edges using Canny edge detection"""
        edges = cv2.Canny(gray,
                         self.low_threshold.value(),
                         self.high_threshold.value())
        
        # Convert back to RGB for display
        return cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)

    def process_image(self):
        if self.original_cv_image is None:
            return

        # Preprocess the image if enabled
        if self.use_preprocessing.isChecked():
            processed = self.preprocess_image(self.original_cv_image)
        else:
            processed = cv2.cvtColor(self.original_cv_image, cv2.COLOR_RGB2GRAY)

        # Apply selected detection method
        if self.harris_radio.isChecked():
            self.processed_image = self.detect_harris_corners(processed)
        elif self.shitomasi_radio.isChecked():
            self.processed_image = self.detect_shitomasi_corners(processed)
        else:  # Canny edges
            self.processed_image = self.detect_canny_edges(processed)

        # Add text annotation
        current_page = self.page_combo.currentIndex() + 1
        method = "Harris" if self.harris_radio.isChecked() else "Shi-Tomasi" if self.shitomasi_radio.isChecked() else "Canny"
        text = f"Page: {current_page} | Method: {method}"
        cv2.putText(self.processed_image, text, (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # Update display
        self.update_image_label(self.original_label, self.original_cv_image)
        self.update_image_label(self.processed_label, self.processed_image)

    def save_pdf(self):
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save PDF", "", "PDF Files (*.pdf)")
        
        if filename:
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'

            # Create new PDF
            output_doc = fitz.open()
            page_count = min(self.doc.page_count, self.MAX_PAGES)

            for i in range(page_count):
                # Process each page
                self.page_combo.setCurrentIndex(i)
                self.process_image()

                # Convert processed image to PDF page
                img_bytes = cv2.imencode('.png', cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2BGR))[1].tobytes()
                img_doc = fitz.open("png", img_bytes)
                pdfbytes = img_doc.convert_to_pdf()
                img_pdf = fitz.open("pdf", pdfbytes)
                output_doc.insert_pdf(img_pdf)

            # Save PDF
            output_doc.save(filename)
            output_doc.close()
            QMessageBox.information(self, "Success", f"PDF saved to:\n{filename}")

    def update_image_label(self, label, cv_image):
        height, width = cv_image.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(cv_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

        # Scale to fit label while maintaining aspect ratio
        scaled_pixmap = QPixmap.fromImage(q_img).scaled(
            label.width(), label.height(),
            Qt.KeepAspectRatio, Qt.SmoothTransformation)
        label.setPixmap(scaled_pixmap)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self.original_cv_image is not None:
            self.process_image()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CornerDetectionDialog()
    window.show()
    sys.exit(app.exec())