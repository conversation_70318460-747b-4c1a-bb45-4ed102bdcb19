from src.utils.logger import logger
import pandas as pd
from src.widgets.groupedtableview import GroupedTableView, GroupedTableFlags
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QSizePolicy, QSplitter, QLabel, QInputDialog, QMessageBox,
                               QToolBar, QHBoxLayout, QSpacerItem, QPushButton, QHeaderView)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from functools import partial
from src.pyside_util import get_resource_qicon, resource_path, get_resource_pixmap
from os.path import exists
from src.data.tables.alwaysvisibletablefields import always_visible_table_fields
from src.app_paths import getEfLookupPath

def _resource_path(path):
    return path

# Override resource_path for testing
if __name__ == "__main__":
    resource_path = _resource_path

# logger = logging.getLogger(__file__)


class LookupTable(QWidget):

    def __init__(self, parent=None, file=getEfLookupPath()) -> None:
        super().__init__(parent)
        logger.info("Creating Lookup Table...")
        self._toolbarBtns = {}
        self.file = file
        self.setLayout(QVBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.lblTimestamp = QLabel("Last updated: Never")
        self.initToolbar()
        self.splitter: QSplitter = QSplitter(Qt.Orientation.Horizontal)
        self.splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.layout().addWidget(self.splitter)
        self.initTable()
        # self.restoreColumnOrder()
        self.setObjectName("dataTable")
        self.setMinimumSize(QSize(1200, 768))
        self.loadLookupFile()
        self.table.setEditFreezeColumns(["Category"])
        self.setWindowIcon(QIcon(get_resource_pixmap("FFF_Architekt Integrated Systems_LOout.png")))
    
    def  __repr__(self) -> str:
        return "LookupTable"

    @property
    def title(self):
        return f"ATEM - Lookup Table - {self.file}"

    def updateWindowTitle(self):
        self.setWindowTitle(self.title)

    def initTable(self):
        self.table = GroupedTableView(self.splitter, checkboxStyle=GroupedTableFlags.HEADER_CHECKBOX_OFF)
        # self.table.horizontalHeader().setSectionsMovable(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table._forceHiddenColumns = ["__uid__"]
        self.table._alwaysVisibleColumns = always_visible_table_fields.get(str(self), [])
        self.table._allowRenameColumn = False
        # self.table.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignRight)
        # self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        # self.table.customContextMenuRequested.connect(self.onTableRightClick)
        self.splitter.addWidget(self.table)

    def initToolbar(self):
        self.toolbar = QToolBar("Table Toolbar", self)
        self.layout().addWidget(self.toolbar)
        self.toolbarWidget = QWidget(self.toolbar)
        self.toolbarWidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.toolbarWidget.setLayout(QHBoxLayout())
        self.toolbar.addWidget(self.toolbarWidget)

        self.toolbarWidget.layout().addWidget(self.lblTimestamp)
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.toolbarWidget.layout().addItem(spacer)

        buttons = [
            # ("save column order", "tableview-columns.svg"),
            ("insert size column", "plus.svg"),
            ("remove size column", "tableview-minus.svg"),
            ("save", "tableview-save.svg"),
            ("download", "tableview-download.svg")
        ]
        for btn_name, icon_path in buttons:
            self.addToolbarButton(btn_name, icon_path)

    def addToolbarButton(self, name: str, icon: str) -> QPushButton:
        btn = QPushButton(get_resource_qicon(icon), "", self)
        btn.setObjectName(f"{name}Btn")
        btn.setMinimumSize(42, 42)
        btn.setToolTip(name.replace("-", " ").title())
        self._toolbarBtns[name] = btn
        self.toolbarWidget.layout().addWidget(btn)
        self.toolbarWidget.layout().setSpacing(12)
        # Connect button click to onToolbarBtn method
        btn.clicked.connect(partial(self.onToolbarBtn, name))
        return btn

    def onSave(self):

        def preferInt(value):
            try:
                return int(value)
            except:
                return str(value)

        df = self.table.getDataFrame()
        df['Category'] = [preferInt(v) for v in df['Category']]
        df.set_index("Category", inplace=True)
        # df.rename(columns={"Category": ""}, inplace=True)

        return # TODO

        sheetname = "Standard EF"
        with pd.ExcelWriter(self.file, engine='openpyxl', mode='a') as writer: 
            workBook = writer.book
            try:
                workBook.remove(workBook[sheetname])
            except:
                print("Worksheet does not exist")
            finally:
                df.to_excel(writer, sheet_name=sheetname,index=False)

    def onToolbarBtn(self, name):
        # Example logging or print statement for debugging
        print(f"Toolbar button clicked: {name}")

        if name == "remove size column": # Tree hieratchy view (QTreeWidget or similar)
            self.onRemoveSize()
        elif name == "save": # Push to database
            self.onSave()
        elif name == "insert size column":
            newSize, ok = QInputDialog.getDouble(self, 
                                                "New Size", 
                                                "Enter new size", 
                                                value=1, 
                                                minValue=0, 
                                                decimals=2)
            if ok:
                if newSize in self.table.getDataFrame().columns:
                    # Size already exists
                    return
                df = self.table.getDataFrame()
                df[newSize] = None
                df[newSize] = df[newSize].astype(float)
                # Sort sizes
                sorted_columns = sorted(df.columns, key=lambda v: (not isinstance(v, str), v))
                df = df.reindex(sorted_columns, axis=1)
                self.setTableData(df)

    def onRemoveSize(self):
        columns = set()
        df = self.table.getDataFrame(drop_uid=False)
        for index in self.table.selectedIndexes():
            name = df.columns[index.column()]
            if name in ["Category", "__uid__"]:
                continue
            columns.add(name)
        if not columns:
            return
        resp = QMessageBox.question(self, 'Remove selected sizes',  f"Remove sizes? {columns}", 
                                        QMessageBox.Yes | QMessageBox.No, 
                                        QMessageBox.No)
        if resp == QMessageBox.No:
            return
        try:
            df.drop(columns=columns, errors='ignore', inplace=True)
            self.setTableData(df)
        except Exception as e:
            logger.info("Could not remove column", exc_info=True)

    def setTableData(self, data: pd.DataFrame):
        # super().setTableData(data, autosize=False)
        self.table.setTableData(data)
        self.table.setHiddenColumns(["__uid__"])
        self.table.setLineEditDelegateForColumn("Category", readonly=True)

    def loadLookupFile(self):
        if self.file is None:
            return
        ef_lookup_template: pd.DataFrame = pd.read_excel(resource_path('src/data/tables/ef_template.xlsx'), 
                                                         index_col=0,
                                                         sheet_name="Standard EF")
        ef_lookup_file = f"{self.file}"
        ef_lookup_user: pd.DataFrame = pd.DataFrame()
        # Merge with user EF if exists
        print("Using User EF lookup table")
        try:
            ef_lookup_user = pd.read_excel(ef_lookup_file, index_col=0)
        except:
            ef_lookup_user = pd.DataFrame()
            ef_lookup_template.to_excel(ef_lookup_file)
        
        # If merge fails, likely invalid user EF file, so just use template
        try:
            df_merged: pd.DataFrame = ef_lookup_user.combine_first(ef_lookup_template)
            df_merged = df_merged.fillna(0)
        except:
            df_merged = ef_lookup_template

        df_merged = df_merged.reset_index()
        df_merged.rename(columns={"index": "Category"}, inplace=True)
        # df_merged['id'] = df_merged['id'].astype(str)
        df_merged['Category'] = df_merged['Category'].astype(str)
        self.setTableData(df_merged)
        self.updateWindowTitle()
        return df_merged


if __name__ == "__main__":
    import sys
    sys.path[0] = ""

    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication()
        lookup = LookupTable(None)
        lookup.show()

        try:
            from src.theme import stylesheet
            app.setStyleSheet(stylesheet)
        except:
            pass
        app.exec()
    except Exception as e:
        logger.exception(f"Unhandled exception caused the application to crash: {e}", exc_info=True)   