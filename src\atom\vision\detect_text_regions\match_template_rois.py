'''
Match template ROIs to corresponding rectangles in target documents.

This module focuses specifically on:
1. Loading a template with ROIs and their coordinates
2. Detecting rectangles in both template and target documents
3. Matching ROIs from the template to corresponding rectangles in the target
4. Snapping ROIs to the matched rectangles with high precision
5. Generating output files with the adjusted coordinates

The key improvement is in the rectangle matching algorithm, which uses structural
pattern matching to identify corresponding rectangles between documents rather than
simple coordinate transformation.
'''
import os
import cv2
import json
import numpy as np

# Import existing functionality
import sys
import os
# Add the project root to the path to make imports work
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))
from src.atom.vision.detect_text_regions.detect_grid_regions_cv2 import detect_grid_lines


def match_template_to_target(template_json_path, target_image_path, roi_json_path=None, output_dir=None, debug=True):
    """
    Match ROIs from a template to corresponding rectangles in a target document.

    Args:
        template_json_path: Path to the template JSON file with detected rectangles
        target_image_path: Path to the target image
        roi_json_path: Path to the ROI JSON file with user-selected regions (optional)
        output_dir: Directory to save output files
        debug: Whether to enable debug mode

    Returns:
        Dictionary containing adjusted ROIs and visualization paths
    """
    print(f"\n=== MATCHING TEMPLATE TO TARGET ===")
    print(f"Template: {template_json_path}")
    print(f"Target: {target_image_path}")
    if roi_json_path:
        print(f"ROI JSON: {roi_json_path}")

    # Set up output directory
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(target_image_path), 'match_template')

    debug_dir = os.path.join(output_dir, 'debug_match_template')
    os.makedirs(debug_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    # Import the extract_roi_data_from_json function if it's not already imported
    try:
        from src.atom.vision.detect_text_regions.read_roi import extract_roi_data_from_json
    except ImportError:
        print("Warning: Could not import extract_roi_data_from_json. User labels will not be available.")
        extract_roi_data_from_json = None

    # Step 1: Load the template data
    try:
        print(f"Loading template data from: {template_json_path}")
        with open(template_json_path, 'r') as f:
            template_data = json.load(f)

        # Get template image path
        template_image_path = template_data.get('image_path')
        if not template_image_path or not os.path.isfile(template_image_path):
            print(f"Error: Template image path not found or invalid: {template_image_path}")
            return None

        # Load user-selected ROIs from ROI JSON if provided
        user_labels = {}
        if roi_json_path and extract_roi_data_from_json:
            try:
                print(f"Loading user-selected ROIs from: {roi_json_path}")
                with open(roi_json_path, 'r') as f:
                    roi_data = json.load(f)

                roi_details = extract_roi_data_from_json(roi_data)
                if roi_details:
                    print("Successfully extracted user labels from ROI JSON")
                    # Create a mapping of coordinates to user labels
                    for group_id, rois in roi_details.items():
                        for roi in rois:
                            roi_name = roi['name']
                            coords = roi['coordinates']
                            # Create a key based on coordinates
                            coord_key = (
                                round(coords['x0'], 4),
                                round(coords['y0'], 4),
                                round(coords['x1'], 4),
                                round(coords['y1'], 4)
                            )
                            user_labels[coord_key] = {
                                'name': roi_name,
                                'isTable': roi.get('isTable', False),
                                'columnNames': roi.get('columnNames', []),
                                'columnRatios': roi.get('columnRatios', [])
                            }
                    print(f"Extracted {len(user_labels)} user labels from ROI JSON")
            except Exception as e:
                print(f"Warning: Failed to extract user labels from ROI JSON: {e}")
                import traceback
                traceback.print_exc()

        # Extract template ROIs
        template_rois = {}

        # Check for standard structure with groups and rois
        if 'groups' in template_data:
            print("Found standard template structure with 'groups' key")
            for group_id, group_data in template_data['groups'].items():
                if 'rois' in group_data:
                    template_rois[group_id] = group_data['rois']
                    print(f"  Found {len(group_data['rois'])} ROIs in group {group_id}")

        # Check for alternative structure with rectangles
        elif 'rectangles' in template_data:
            print(f"Found alternative template structure with 'rectangles' key ({len(template_data['rectangles'])} rectangles)")
            # Convert rectangles to ROI format
            template_rois = {'1': []}
            for rect in template_data['rectangles']:
                rect_id = rect.get('id', 0)
                rel_x = rect.get('x', 0)
                rel_y = rect.get('y', 0)
                rel_w = rect.get('width', 0)
                rel_h = rect.get('height', 0)

                # Skip very large rectangles (likely page borders)
                if rel_w > 0.9 and rel_h > 0.9:
                    continue

                # Skip very small rectangles
                if rel_w < 0.01 or rel_h < 0.01:
                    continue

                # Create ROI object
                roi = {
                    'name': f'Rectangle_{rect_id}',
                    'relativeX0': rel_x,
                    'relativeY0': rel_y,
                    'relativeX1': rel_x + rel_w,
                    'relativeY1': rel_y + rel_h
                }

                # Check if we have a user label for this rectangle
                coord_key = (
                    round(rel_x, 4),
                    round(rel_y, 4),
                    round(rel_x + rel_w, 4),
                    round(rel_y + rel_h, 4)
                )

                if coord_key in user_labels:
                    roi['user_label'] = user_labels[coord_key]['name']
                    roi['isTable'] = user_labels[coord_key]['isTable']
                    if user_labels[coord_key]['isTable']:
                        roi['columnNames'] = user_labels[coord_key]['columnNames']
                        roi['columnRatios'] = user_labels[coord_key]['columnRatios']

                template_rois['1'].append(roi)

            print(f"  Converted to {len(template_rois['1'])} ROIs")

        if not template_rois:
            print("No valid ROIs found in template JSON.")
            return None

    except Exception as e:
        print(f"Error loading template data: {e}")
        import traceback
        traceback.print_exc()
        return None

    # Step 2: Load the template and target images
    template_img = cv2.imread(template_image_path)
    if template_img is None:
        print(f"Error: Could not read template image at '{template_image_path}'")
        return None

    target_img = cv2.imread(target_image_path)
    if target_img is None:
        print(f"Error: Could not read target image at '{target_image_path}'")
        return None

    template_height, template_width = template_img.shape[:2]
    target_height, target_width = target_img.shape[:2]

    print(f"Template image dimensions: {template_width}x{template_height}")
    print(f"Target image dimensions: {target_width}x{target_height}")

    # Step 3: Detect rectangles in both template and target images
    print("\n=== DETECTING RECTANGLES ===")

    print("Detecting rectangles in template image...")
    template_grid_results = detect_grid_lines(template_img, debug=debug, debug_dir=debug_dir)
    template_rectangles = template_grid_results['rectangles']
    print(f"Detected {len(template_rectangles)} rectangles in template")

    print("Detecting rectangles in target image...")
    target_grid_results = detect_grid_lines(target_img, debug=debug, debug_dir=debug_dir)
    target_rectangles = target_grid_results['rectangles']
    print(f"Detected {len(target_rectangles)} rectangles in target")

    # Save rectangle visualizations
    template_vis = template_img.copy()
    for rect in template_rectangles:
        x, y, w, h, rect_id = rect
        cv2.rectangle(template_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(template_vis, str(rect_id), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    cv2.imwrite(os.path.join(debug_dir, 'template_rectangles.png'), template_vis)

    target_vis = target_img.copy()
    for rect in target_rectangles:
        x, y, w, h, rect_id = rect
        cv2.rectangle(target_vis, (x, y), (x + w, y + h), (0, 0, 255), 2)
        cv2.putText(target_vis, str(rect_id), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    cv2.imwrite(os.path.join(debug_dir, 'target_rectangles.png'), target_vis)

    # Step 4: Create structural fingerprints for rectangles
    print("\n=== CREATING STRUCTURAL FINGERPRINTS ===")

    # Create fingerprints for template rectangles
    template_fingerprints = {}
    for rect in template_rectangles:
        x, y, w, h, rect_id = rect
        fingerprint = create_rectangle_fingerprint(rect, template_rectangles)
        template_fingerprints[rect_id] = fingerprint

    # Create fingerprints for target rectangles
    target_fingerprints = {}
    for rect in target_rectangles:
        x, y, w, h, rect_id = rect
        fingerprint = create_rectangle_fingerprint(rect, target_rectangles)
        target_fingerprints[rect_id] = fingerprint

    # Step 5: Match rectangles between template and target
    print("\n=== MATCHING RECTANGLES ===")
    rectangle_matches = match_rectangles(template_rectangles, target_rectangles,
                                        template_fingerprints, target_fingerprints)

    print(f"Found {len(rectangle_matches)} rectangle matches")

    # Save match visualization
    match_vis = np.hstack((template_vis, target_vis))
    h, w = template_vis.shape[:2]

    # Draw lines connecting matched rectangles
    for template_id, target_id in rectangle_matches.items():
        # Find the rectangles
        template_rect = next((r for r in template_rectangles if r[4] == template_id), None)
        target_rect = next((r for r in target_rectangles if r[4] == target_id), None)

        if template_rect and target_rect:
            # Get centers
            tx, ty, tw, th, _ = template_rect
            t_center_x = tx + tw // 2
            t_center_y = ty + th // 2

            dx, dy, dw, dh, _ = target_rect
            d_center_x = dx + dw // 2 + w  # Adjust for side-by-side image
            d_center_y = dy + dh // 2

            # Draw line connecting centers
            color = (0, 255, 255)  # Yellow
            cv2.line(match_vis, (t_center_x, t_center_y), (d_center_x, d_center_y), color, 1)

            # Add match labels
            cv2.putText(match_vis, f"Match: {template_id}->{target_id}",
                       (t_center_x, t_center_y - 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    cv2.imwrite(os.path.join(debug_dir, 'rectangle_matches.png'), match_vis)

    # Step 6: Match ROIs to target rectangles and snap
    print("\n=== MATCHING ROIs TO TARGET RECTANGLES ===")

    # Create a visualization image
    output_vis = target_img.copy()

    # Process each ROI in the template
    adjusted_rois = {}

    # Track which rectangles have already been matched to avoid duplicates
    previously_matched_rects = set()

    # First pass: Process and match all ROIs
    initial_matches = {}

    for group_id, rois in template_rois.items():
        group_matches = []

        for roi in rois:
            roi_name = roi.get('name', 'unnamed')
            roi_x0 = roi.get('relativeX0', 0)
            roi_y0 = roi.get('relativeY0', 0)
            roi_x1 = roi.get('relativeX1', 1)
            roi_y1 = roi.get('relativeY1', 1)

            # Get user label if available
            user_label = roi.get('user_label', roi_name)
            is_table = roi.get('isTable', False)
            column_names = roi.get('columnNames', [])
            column_ratios = roi.get('columnRatios', [])

            print(f"\nProcessing ROI: {roi_name}")
            if user_label != roi_name:
                print(f"  User label: {user_label}")
            print(f"  Original coordinates: ({roi_x0:.4f}, {roi_y0:.4f}, {roi_x1:.4f}, {roi_y1:.4f})")

            # Find the closest template rectangle to this ROI
            closest_template_rect = find_closest_rectangle(
                (roi_x0, roi_y0, roi_x1, roi_y1),
                template_rectangles,
                (template_width, template_height),
                previously_matched_rects
            )

            if closest_template_rect:
                template_rect_id = closest_template_rect[4]
                print(f"  Closest template rectangle: {template_rect_id}")

                # Check if this template rectangle has a match in the target
                if template_rect_id in rectangle_matches:
                    target_rect_id = rectangle_matches[template_rect_id]
                    print(f"  Matched target rectangle: {target_rect_id}")

                    # Find the target rectangle
                    target_rect = next((r for r in target_rectangles if r[4] == target_rect_id), None)

                    if target_rect:
                        # Add to previously matched rectangles
                        previously_matched_rects.add(target_rect_id)

                        # Snap the ROI to the target rectangle
                        tx, ty, tw, th, _ = target_rect

                        # Convert to relative coordinates
                        snapped_x0 = tx / target_width
                        snapped_y0 = ty / target_height
                        snapped_x1 = (tx + tw) / target_width
                        snapped_y1 = (ty + th) / target_height

                        print(f"  Snapped coordinates: ({snapped_x0:.4f}, {snapped_y0:.4f}, {snapped_x1:.4f}, {snapped_y1:.4f})")

                        # Calculate distance between original and snapped centers
                        orig_center_x = (roi_x0 + roi_x1) / 2
                        orig_center_y = (roi_y0 + roi_y1) / 2
                        snapped_center_x = (snapped_x0 + snapped_x1) / 2
                        snapped_center_y = (snapped_y0 + snapped_y1) / 2

                        distance = ((orig_center_x - snapped_center_x) ** 2 +
                                   (orig_center_y - snapped_center_y) ** 2) ** 0.5

                        # Flag potentially problematic matches (high distance)
                        is_problematic = distance > 0.1  # Threshold for flagging

                        # Store match information for post-processing
                        match_info = {
                            'roi': roi.copy(),
                            'roi_name': roi_name,
                            'user_label': user_label,
                            'is_table': is_table,
                            'column_names': column_names,
                            'column_ratios': column_ratios,
                            'original_coords': {
                                'x0': roi_x0,
                                'y0': roi_y0,
                                'x1': roi_x1,
                                'y1': roi_y1
                            },
                            'snapped_coords': {
                                'x0': snapped_x0,
                                'y0': snapped_y0,
                                'x1': snapped_x1,
                                'y1': snapped_y1
                            },
                            'template_rect_id': template_rect_id,
                            'target_rect_id': target_rect_id,
                            'distance': distance,
                            'is_problematic': is_problematic
                        }

                        group_matches.append(match_info)
                    else:
                        print(f"  Error: Target rectangle {target_rect_id} not found")
                else:
                    print(f"  No match found for template rectangle {template_rect_id}")
            else:
                print(f"  No close template rectangle found for this ROI")

        if group_matches:
            initial_matches[group_id] = group_matches

    # Step 7: Post-process matches to correct problematic ones
    print("\n=== POST-PROCESSING MATCHES ===")

    # For each group, identify and correct problematic matches
    for group_id, matches in initial_matches.items():
        adjusted_group_rois = []

        # First, identify reliable matches (non-problematic ones)
        reliable_matches = [m for m in matches if not m['is_problematic']]
        problematic_matches = [m for m in matches if m['is_problematic']]

        print(f"Group {group_id}: {len(reliable_matches)} reliable matches, {len(problematic_matches)} problematic matches")

        # Process reliable matches first
        for match in reliable_matches:
            roi = match['roi']
            roi_name = match['roi_name']
            user_label = match['user_label']
            is_table = match['is_table']
            column_names = match['column_names']
            column_ratios = match['column_ratios']
            snapped_coords = match['snapped_coords']
            target_rect_id = match['target_rect_id']

            # Create adjusted ROI
            adjusted_roi = roi.copy()
            adjusted_roi.update({
                'relativeX0': snapped_coords['x0'],
                'relativeY0': snapped_coords['y0'],
                'relativeX1': snapped_coords['x1'],
                'relativeY1': snapped_coords['y1'],
                'matched_rectangle': target_rect_id,
                'original_coords': match['original_coords'],
                'is_problematic': False
            })

            # Ensure user label is included
            if user_label != roi_name:
                adjusted_roi['user_label'] = user_label

            # Include table information if applicable
            if is_table:
                adjusted_roi['isTable'] = True
                adjusted_roi['columnNames'] = column_names
                adjusted_roi['columnRatios'] = column_ratios

            adjusted_group_rois.append(adjusted_roi)

            # Draw on visualization
            abs_x0 = int(snapped_coords['x0'] * target_width)
            abs_y0 = int(snapped_coords['y0'] * target_height)
            abs_x1 = int(snapped_coords['x1'] * target_width)
            abs_y1 = int(snapped_coords['y1'] * target_height)

            # Use different colors for tables and regular ROIs
            color = (255, 0, 0) if is_table else (0, 255, 0)  # Blue for tables, green for regular ROIs

            cv2.rectangle(output_vis, (abs_x0, abs_y0), (abs_x1, abs_y1), color, 2)

            # Display user label if available, otherwise use ROI name
            if user_label != roi_name:
                # Draw a filled rectangle as background for the text to make it more readable
                text_size = cv2.getTextSize(user_label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(output_vis,
                             (abs_x0, abs_y0 - 30),
                             (abs_x0 + text_size[0] + 10, abs_y0 - 5),
                             (0, 0, 0), -1)  # Black background

                # Draw the user label in a larger, more visible font
                cv2.putText(output_vis, user_label, (abs_x0 + 5, abs_y0 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)  # White text

                # Draw the rectangle ID in smaller font below
                cv2.putText(output_vis, f"ID: {roi_name}", (abs_x0, abs_y0 + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            else:
                # Just display the rectangle name if no user label
                cv2.putText(output_vis, roi_name, (abs_x0, abs_y0 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # Now process problematic matches with correction
        for match in problematic_matches:
            roi = match['roi']
            roi_name = match['roi_name']
            user_label = match['user_label']
            is_table = match['is_table']
            column_names = match['column_names']
            column_ratios = match['column_ratios']
            original_coords = match['original_coords']
            snapped_coords = match['snapped_coords']
            target_rect_id = match['target_rect_id']

            print(f"\nAttempting to correct problematic match for ROI: {roi_name}")
            print(f"  Original distance: {match['distance']:.4f}")

            # Try to find a better match based on relative positions to reliable matches
            corrected_coords = None
            corrected_rect_id = None

            if reliable_matches:
                print("  Using reliable matches as reference points for correction")

                # Calculate the average relative position of this ROI to reliable ROIs in the template
                rel_positions = []

                for ref_match in reliable_matches:
                    ref_orig = ref_match['original_coords']
                    ref_snapped = ref_match['snapped_coords']

                    # Calculate center points
                    ref_orig_center_x = (ref_orig['x0'] + ref_orig['x1']) / 2
                    ref_orig_center_y = (ref_orig['y0'] + ref_orig['y1']) / 2

                    orig_center_x = (original_coords['x0'] + original_coords['x1']) / 2
                    orig_center_y = (original_coords['y0'] + original_coords['y1']) / 2

                    # Calculate relative vector from reference to this ROI in template
                    rel_x = orig_center_x - ref_orig_center_x
                    rel_y = orig_center_y - ref_orig_center_y

                    # Calculate distance
                    rel_distance = (rel_x**2 + rel_y**2)**0.5

                    # Only use nearby references (within reasonable distance)
                    if rel_distance < 0.3:  # Threshold for considering a reference
                        rel_positions.append({
                            'ref_match': ref_match,
                            'rel_x': rel_x,
                            'rel_y': rel_y,
                            'distance': rel_distance
                        })

                # Sort by distance (closest first)
                rel_positions.sort(key=lambda p: p['distance'])

                # Use the closest references (up to 3)
                closest_refs = rel_positions[:min(3, len(rel_positions))]

                if closest_refs:
                    print(f"  Found {len(closest_refs)} close reference points")

                    # Calculate expected position based on each reference
                    expected_positions = []

                    for ref_pos in closest_refs:
                        ref_match = ref_pos['ref_match']
                        ref_snapped = ref_match['snapped_coords']

                        # Calculate reference center in target
                        ref_target_center_x = (ref_snapped['x0'] + ref_snapped['x1']) / 2
                        ref_target_center_y = (ref_snapped['y0'] + ref_snapped['y1']) / 2

                        # Apply relative offset to get expected position
                        expected_center_x = ref_target_center_x + ref_pos['rel_x']
                        expected_center_y = ref_target_center_y + ref_pos['rel_y']

                        # Calculate weight based on inverse distance
                        weight = 1 / (ref_pos['distance'] + 0.01)  # Add small constant to avoid division by zero

                        expected_positions.append({
                            'center_x': expected_center_x,
                            'center_y': expected_center_y,
                            'weight': weight
                        })

                    # Calculate weighted average of expected positions
                    total_weight = sum(pos['weight'] for pos in expected_positions)
                    avg_expected_x = sum(pos['center_x'] * pos['weight'] for pos in expected_positions) / total_weight
                    avg_expected_y = sum(pos['center_y'] * pos['weight'] for pos in expected_positions) / total_weight

                    print(f"  Expected position: ({avg_expected_x:.4f}, {avg_expected_y:.4f})")

                    # Find the closest rectangle to this expected position
                    # (No need to convert to absolute coordinates for rectangle search)

                    # Calculate original width and height
                    orig_width = original_coords['x1'] - original_coords['x0']
                    orig_height = original_coords['y1'] - original_coords['y0']

                    # Create a synthetic ROI at the expected position with original dimensions
                    expected_roi_x0 = avg_expected_x - orig_width / 2
                    expected_roi_y0 = avg_expected_y - orig_height / 2
                    expected_roi_x1 = avg_expected_x + orig_width / 2
                    expected_roi_y1 = avg_expected_y + orig_height / 2

                    # Find the closest rectangle to this expected position
                    closest_rect = find_closest_rectangle(
                        (expected_roi_x0, expected_roi_y0, expected_roi_x1, expected_roi_y1),
                        target_rectangles,
                        (target_width, target_height),
                        previously_matched_rects
                    )

                    if closest_rect:
                        tx, ty, tw, th, rect_id = closest_rect

                        # Convert to relative coordinates
                        corrected_x0 = tx / target_width
                        corrected_y0 = ty / target_height
                        corrected_x1 = (tx + tw) / target_width
                        corrected_y1 = (ty + th) / target_height

                        # Calculate distance to expected position
                        corrected_center_x = (corrected_x0 + corrected_x1) / 2
                        corrected_center_y = (corrected_y0 + corrected_y1) / 2

                        correction_distance = ((avg_expected_x - corrected_center_x)**2 +
                                             (avg_expected_y - corrected_center_y)**2)**0.5

                        # Only use correction if it's significantly better than original match
                        if correction_distance < match['distance'] * 0.7:  # At least 30% improvement
                            print(f"  Found better match: Rectangle {rect_id}")
                            print(f"  Correction distance: {correction_distance:.4f} (vs original {match['distance']:.4f})")

                            corrected_coords = {
                                'x0': corrected_x0,
                                'y0': corrected_y0,
                                'x1': corrected_x1,
                                'y1': corrected_y1
                            }
                            corrected_rect_id = rect_id
                            previously_matched_rects.add(rect_id)
                        else:
                            print(f"  Correction not significantly better: {correction_distance:.4f} vs {match['distance']:.4f}")
                    else:
                        print("  Could not find a suitable rectangle near the expected position")
                else:
                    print("  No close reference points found for correction")
            else:
                print("  No reliable matches available for correction")

            # Use corrected coordinates if available, otherwise use original match
            final_coords = corrected_coords if corrected_coords else snapped_coords
            final_rect_id = corrected_rect_id if corrected_rect_id else target_rect_id
            is_still_problematic = corrected_coords is None

            # Create adjusted ROI
            adjusted_roi = roi.copy()
            adjusted_roi.update({
                'relativeX0': final_coords['x0'],
                'relativeY0': final_coords['y0'],
                'relativeX1': final_coords['x1'],
                'relativeY1': final_coords['y1'],
                'matched_rectangle': final_rect_id,
                'original_coords': original_coords,
                'is_problematic': is_still_problematic
            })

            # Ensure user label is included
            if user_label != roi_name:
                adjusted_roi['user_label'] = user_label

            # Include table information if applicable
            if is_table:
                adjusted_roi['isTable'] = True
                adjusted_roi['columnNames'] = column_names
                adjusted_roi['columnRatios'] = column_ratios

            adjusted_group_rois.append(adjusted_roi)

            # Draw on visualization
            abs_x0 = int(final_coords['x0'] * target_width)
            abs_y0 = int(final_coords['y0'] * target_height)
            abs_x1 = int(final_coords['x1'] * target_width)
            abs_y1 = int(final_coords['y1'] * target_height)

            # Use different colors based on whether correction was successful
            if is_still_problematic:
                color = (0, 0, 255)  # Red for still problematic
            else:
                color = (0, 165, 255)  # Orange for corrected

            cv2.rectangle(output_vis, (abs_x0, abs_y0), (abs_x1, abs_y1), color, 2)

            # Display user label if available, otherwise use ROI name
            if user_label != roi_name:
                # Draw a filled rectangle as background for the text to make it more readable
                text_size = cv2.getTextSize(user_label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(output_vis,
                             (abs_x0, abs_y0 - 30),
                             (abs_x0 + text_size[0] + 10, abs_y0 - 5),
                             (0, 0, 0), -1)  # Black background

                # Draw the user label in a larger, more visible font
                cv2.putText(output_vis, user_label, (abs_x0 + 5, abs_y0 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)  # White text

                # Draw the rectangle ID in smaller font below
                status = "CORRECTED" if not is_still_problematic else "PROBLEMATIC"
                cv2.putText(output_vis, f"ID: {roi_name} ({status})", (abs_x0, abs_y0 + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            else:
                # Just display the rectangle name if no user label
                status = "CORRECTED" if not is_still_problematic else "PROBLEMATIC"
                cv2.putText(output_vis, f"{roi_name} ({status})", (abs_x0, abs_y0 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        if adjusted_group_rois:
            adjusted_rois[group_id] = adjusted_group_rois

    # Step 7: Save results
    print("\n=== SAVING RESULTS ===")

    # Add a legend to the visualization
    legend_height = 80  # Increased height for additional legend items
    legend = np.ones((legend_height, output_vis.shape[1], 3), dtype=np.uint8) * 255  # White background

    # Draw legend items
    cv2.putText(legend, "Legend:", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Regular ROI example
    cv2.rectangle(legend, (120, 10), (150, 30), (0, 255, 0), 2)
    cv2.putText(legend, "Regular ROI", (160, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Table ROI example
    cv2.rectangle(legend, (300, 10), (330, 30), (255, 0, 0), 2)
    cv2.putText(legend, "Table ROI", (340, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Problematic match example
    cv2.rectangle(legend, (450, 10), (480, 30), (0, 0, 255), 2)
    cv2.putText(legend, "Problematic Match", (490, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Corrected match example
    cv2.rectangle(legend, (120, 40), (150, 60), (0, 165, 255), 2)
    cv2.putText(legend, "Corrected Match", (160, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # User label example
    cv2.rectangle(legend, (300, 40), (330, 60), (0, 0, 0), -1)
    cv2.putText(legend, "User Label", (340, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    cv2.putText(legend, "Rectangle ID shown below each labeled rectangle", (120, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    # Combine the visualization with the legend
    output_with_legend = np.vstack((output_vis, legend))

    # Save visualization
    vis_path = os.path.join(output_dir, 'matched_rois.png')
    cv2.imwrite(vis_path, output_with_legend)
    print(f"Visualization saved to: {vis_path}")

    # Save adjusted ROIs to JSON
    output_json = {
        'template_path': template_json_path,
        'target_image': target_image_path,
        'groups': {}
    }

    for group_id, rois in adjusted_rois.items():
        output_json['groups'][group_id] = {
            'rois': []
        }

        # Process each ROI to ensure it has the correct format
        for roi in rois:
            # Create a clean ROI object with standard fields
            roi_data = {
                'name': roi.get('name', 'unnamed'),
                'relativeX0': roi.get('relativeX0', 0),
                'relativeY0': roi.get('relativeY0', 0),
                'relativeX1': roi.get('relativeX1', 1),
                'relativeY1': roi.get('relativeY1', 1),
                'matched_rectangle': roi.get('matched_rectangle', 0)
            }

            # Include user label if available
            if 'user_label' in roi:
                roi_data['user_label'] = roi['user_label']

            # Include table information if applicable
            if roi.get('isTable', False):
                roi_data['isTable'] = True
                roi_data['columnNames'] = roi.get('columnNames', [])
                roi_data['columnRatios'] = roi.get('columnRatios', [])

            # Include problematic flag if available
            if 'is_problematic' in roi:
                roi_data['is_problematic'] = roi['is_problematic']

            output_json['groups'][group_id]['rois'].append(roi_data)

    output_json_path = os.path.join(output_dir, 'matched_template.json')
    with open(output_json_path, 'w') as f:
        json.dump(output_json, f, indent=4)

    print(f"Adjusted template JSON saved to: {output_json_path}")

    # Step 8: Export ROI mapping to Excel for validation
    print("\n=== EXPORTING ROI MAPPING TO EXCEL ===")

    try:
        # Import the export function
        from src.atom.vision.detect_text_regions.export_roi_mapping import export_roi_mapping_to_excel

        # Create a dictionary with the format expected by the export function
        matched_regions = {}

        for group_id, rois in adjusted_rois.items():
            matched_regions[group_id] = []

            for roi in rois:
                # Extract original coordinates if available
                original_coords = roi.get('original_coords', {})
                if not original_coords:
                    # If not available, use relative coordinates as a fallback
                    original_coords = {
                        'x0': roi.get('relativeX0', 0),
                        'y0': roi.get('relativeY0', 0),
                        'x1': roi.get('relativeX1', 1),
                        'y1': roi.get('relativeY1', 1)
                    }

                # Create region info
                region_info = {
                    'name': roi.get('name', 'unnamed'),
                    'user_label': roi.get('user_label', roi.get('name', 'unnamed')),
                    'original_coords': original_coords,
                    'adjusted_coords': {
                        'x0': roi.get('relativeX0', 0),
                        'y0': roi.get('relativeY0', 0),
                        'x1': roi.get('relativeX1', 1),
                        'y1': roi.get('relativeY1', 1)
                    },
                    'matched_rectangle': roi.get('matched_rectangle', 0),
                    'is_table': roi.get('isTable', False),
                    'column_names': roi.get('columnNames', []),
                    'column_ratios': roi.get('columnRatios', []),
                    'is_problematic': roi.get('is_problematic', False)
                }

                matched_regions[group_id].append(region_info)

        # Export to Excel
        excel_path = os.path.join(output_dir, 'roi_mapping.xlsx')

        # Print debug info
        print(f"Exporting to Excel: {excel_path}")
        print(f"Template image path: {template_image_path}")
        print(f"Target image path: {target_image_path}")
        print(f"Number of ROIs: {sum(len(rois) for rois in matched_regions.values())}")

        # Make sure the output directory exists
        os.makedirs(os.path.dirname(excel_path), exist_ok=True)

        # Export to Excel
        export_roi_mapping_to_excel(
            matched_regions,
            template_rectangles,
            target_rectangles,
            template_image_path,
            target_image_path,
            excel_path
        )

        print(f"ROI mapping exported to Excel: {excel_path}")

        # Check if the file was created successfully
        if os.path.exists(excel_path):
            file_size = os.path.getsize(excel_path)
            print(f"Excel file created successfully. Size: {file_size} bytes")
        else:
            print(f"Warning: Excel file was not created at {excel_path}")

    except Exception as e:
        print(f"Error exporting ROI mapping to Excel: {e}")
        import traceback
        traceback.print_exc()

    return {
        'adjusted_rois': adjusted_rois,
        'visualization_path': vis_path,
        'output_json_path': output_json_path,
        'debug_directory': debug_dir
    }


def create_rectangle_fingerprint(rectangle, all_rectangles):
    """
    Create a structural fingerprint for a rectangle based on its relationships with other rectangles.

    Args:
        rectangle: The rectangle to create a fingerprint for (x, y, w, h, id)
        all_rectangles: List of all rectangles in the document

    Returns:
        Dictionary containing the fingerprint features
    """
    x, y, w, h, rect_id = rectangle

    # Calculate center point
    center_x = x + w/2
    center_y = y + h/2

    # Calculate aspect ratio
    aspect_ratio = w/h if h > 0 else 0

    # Calculate relative positions of other rectangles
    relative_positions = []
    distances = []

    # Track quadrant distribution (top-left, top-right, bottom-left, bottom-right)
    quadrant_counts = [0, 0, 0, 0]

    # Track section neighbors (rectangles that are likely in the same document section)
    section_neighbors = []

    # Define section proximity threshold (rectangles closer than this are likely in same section)
    section_threshold = max(w, h) * 3  # Adjust this multiplier as needed

    for other_rect in all_rectangles:
        if other_rect[4] == rect_id:  # Skip self
            continue

        ox, oy, ow, oh, o_id = other_rect
        o_center_x = ox + ow/2
        o_center_y = oy + oh/2

        # Calculate relative position (vector from this rectangle to other)
        rel_x = o_center_x - center_x
        rel_y = o_center_y - center_y

        # Calculate distance
        distance = np.sqrt(rel_x**2 + rel_y**2)

        # Calculate angle (in degrees, 0-360)
        angle = np.degrees(np.arctan2(rel_y, rel_x)) % 360

        # Determine quadrant (0: top-left, 1: top-right, 2: bottom-left, 3: bottom-right)
        quadrant = 0
        if rel_x >= 0 and rel_y < 0:  # top-right
            quadrant = 1
        elif rel_x < 0 and rel_y >= 0:  # bottom-left
            quadrant = 2
        elif rel_x >= 0 and rel_y >= 0:  # bottom-right
            quadrant = 3

        quadrant_counts[quadrant] += 1

        # Check if this rectangle is likely in the same section
        if distance < section_threshold:
            section_neighbors.append(o_id)

        # Store relative position data with enhanced features
        relative_positions.append({
            'id': o_id,
            'distance': distance,
            'angle': angle,
            'rel_x': rel_x,
            'rel_y': rel_y,
            'quadrant': quadrant,
            'aspect_ratio': ow/oh if oh > 0 else 0,
            'size_ratio': (ow * oh) / (w * h) if (w * h) > 0 else 0,
            'is_section_neighbor': distance < section_threshold
        })

        distances.append(distance)

    # Sort relative positions by distance
    relative_positions.sort(key=lambda p: p['distance'])

    # Take only the closest N neighbors to keep the fingerprint manageable
    max_neighbors = min(12, len(relative_positions))  # Increased from 8 to 12
    closest_neighbors = relative_positions[:max_neighbors]

    # Calculate quadrant distribution (percentage of neighbors in each quadrant)
    total_neighbors = sum(quadrant_counts)
    quadrant_distribution = [count / total_neighbors if total_neighbors > 0 else 0
                            for count in quadrant_counts]

    # Calculate directional density (how evenly neighbors are distributed around)
    angles = [n['angle'] for n in relative_positions[:min(16, len(relative_positions))]]
    angle_diffs = []
    if len(angles) > 1:
        sorted_angles = sorted(angles)
        for i in range(len(sorted_angles)):
            next_angle = sorted_angles[(i + 1) % len(sorted_angles)]
            diff = next_angle - sorted_angles[i]
            if diff < 0:
                diff += 360
            angle_diffs.append(diff)
        directional_density = np.std(angle_diffs) / 180.0  # Normalized to [0,1]
    else:
        directional_density = 1.0  # Max value if not enough neighbors

    # Calculate neighborhood density (how clustered the neighbors are)
    if distances:
        nearest_distances = distances[:min(5, len(distances))]
        neighborhood_density = np.mean(nearest_distances) / np.max(distances) if np.max(distances) > 0 else 0
    else:
        neighborhood_density = 0

    return {
        'rect_id': rect_id,
        'width': w,
        'height': h,
        'aspect_ratio': aspect_ratio,
        'neighbors': closest_neighbors,
        'avg_distance': np.mean(distances) if distances else 0,
        'center': (center_x, center_y),
        'quadrant_distribution': quadrant_distribution,
        'directional_density': directional_density,
        'neighborhood_density': neighborhood_density,
        'section_neighbors': section_neighbors
    }


def match_rectangles(template_rectangles, target_rectangles, template_fingerprints, target_fingerprints):
    """
    Match rectangles between template and target based on structural fingerprints.

    Args:
        template_rectangles: List of rectangles from template
        target_rectangles: List of rectangles from target
        template_fingerprints: Dictionary of fingerprints for template rectangles
        target_fingerprints: Dictionary of fingerprints for target rectangles

    Returns:
        Dictionary mapping template rectangle IDs to matching target rectangle IDs
    """
    matches = {}

    # For each template rectangle
    for t_rect in template_rectangles:
        t_id = t_rect[4]
        t_fingerprint = template_fingerprints.get(t_id)

        if not t_fingerprint:
            continue

        best_match = None
        best_score = 0

        # Compare with each target rectangle
        for d_rect in target_rectangles:
            d_id = d_rect[4]
            d_fingerprint = target_fingerprints.get(d_id)

            if not d_fingerprint:
                continue

            # Calculate similarity score
            score = compare_fingerprints(t_fingerprint, d_fingerprint)

            if score > best_score:
                best_score = score
                best_match = d_id

        # If we found a good match
        if best_match is not None and best_score > 0.6:  # Threshold for a good match
            matches[t_id] = best_match
            print(f"Matched template rectangle {t_id} to target rectangle {best_match} (score: {best_score:.4f})")

    return matches


def compare_fingerprints(fp1, fp2):
    """
    Compare two rectangle fingerprints and return a similarity score.

    Args:
        fp1: First fingerprint
        fp2: Second fingerprint

    Returns:
        Similarity score between 0 and 1
    """
    # Compare aspect ratios
    aspect_ratio_diff = abs(fp1['aspect_ratio'] - fp2['aspect_ratio'])
    aspect_ratio_score = 1 / (1 + aspect_ratio_diff)

    # Compare quadrant distributions
    quadrant_score = 0
    if 'quadrant_distribution' in fp1 and 'quadrant_distribution' in fp2:
        qd1 = fp1['quadrant_distribution']
        qd2 = fp2['quadrant_distribution']

        # Calculate cosine similarity between quadrant distributions
        dot_product = sum(q1 * q2 for q1, q2 in zip(qd1, qd2))
        magnitude1 = sum(q * q for q in qd1) ** 0.5
        magnitude2 = sum(q * q for q in qd2) ** 0.5

        if magnitude1 > 0 and magnitude2 > 0:
            quadrant_score = dot_product / (magnitude1 * magnitude2)
        else:
            quadrant_score = 0

    # Compare directional density
    density_score = 0
    if 'directional_density' in fp1 and 'directional_density' in fp2:
        dd1 = fp1['directional_density']
        dd2 = fp2['directional_density']
        density_score = 1 - min(abs(dd1 - dd2), 1)

    # Compare neighborhood density
    neighborhood_score = 0
    if 'neighborhood_density' in fp1 and 'neighborhood_density' in fp2:
        nd1 = fp1['neighborhood_density']
        nd2 = fp2['neighborhood_density']
        neighborhood_score = 1 - min(abs(nd1 - nd2), 1)

    # Compare neighbor patterns (enhanced)
    neighbor_score = 0

    if fp1['neighbors'] and fp2['neighbors']:
        # Compare the pattern of angles between neighbors
        angles1 = sorted([n['angle'] for n in fp1['neighbors']])
        angles2 = sorted([n['angle'] for n in fp2['neighbors']])

        # Use the smaller set of angles
        min_angles = min(len(angles1), len(angles2))
        if min_angles > 0:
            angle_diffs = []
            for i in range(min_angles):
                # Calculate circular difference between angles
                diff = min(abs(angles1[i % len(angles1)] - angles2[i % len(angles2)]),
                          360 - abs(angles1[i % len(angles1)] - angles2[i % len(angles2)]))
                angle_diffs.append(diff)

            avg_angle_diff = sum(angle_diffs) / len(angle_diffs)
            angle_score = 1 - min(avg_angle_diff / 180, 1)

            # Compare distance patterns
            distances1 = sorted([n['distance'] for n in fp1['neighbors']])
            distances2 = sorted([n['distance'] for n in fp2['neighbors']])

            # Normalize distances
            if distances1 and distances2:
                norm_distances1 = [d / max(distances1) for d in distances1]
                norm_distances2 = [d / max(distances2) for d in distances2]

                # Calculate distance pattern difference
                dist_diffs = []
                for i in range(min(len(norm_distances1), len(norm_distances2))):
                    dist_diffs.append(abs(norm_distances1[i] - norm_distances2[i]))

                avg_dist_diff = sum(dist_diffs) / len(dist_diffs) if dist_diffs else 1
                dist_score = 1 - avg_dist_diff

                # Compare quadrant patterns
                quadrants1 = [n.get('quadrant', 0) for n in fp1['neighbors']]
                quadrants2 = [n.get('quadrant', 0) for n in fp2['neighbors']]

                # Count matching quadrants
                quadrant_matches = sum(1 for i in range(min(len(quadrants1), len(quadrants2)))
                                     if quadrants1[i] == quadrants2[i])
                quadrant_match_score = quadrant_matches / min(len(quadrants1), len(quadrants2))

                # Compare size ratios if available
                size_ratio_score = 0
                if all('size_ratio' in n for n in fp1['neighbors']) and all('size_ratio' in n for n in fp2['neighbors']):
                    size_ratios1 = sorted([n['size_ratio'] for n in fp1['neighbors']])
                    size_ratios2 = sorted([n['size_ratio'] for n in fp2['neighbors']])

                    # Calculate size ratio difference
                    size_ratio_diffs = []
                    for i in range(min(len(size_ratios1), len(size_ratios2))):
                        size_ratio_diffs.append(abs(size_ratios1[i] - size_ratios2[i]))

                    avg_size_ratio_diff = sum(size_ratio_diffs) / len(size_ratio_diffs) if size_ratio_diffs else 1
                    size_ratio_score = 1 - min(avg_size_ratio_diff, 1)
                else:
                    size_ratio_score = 0.5  # Neutral score if not available

                # Combine all neighbor pattern scores
                neighbor_score = (0.4 * angle_score +
                                 0.3 * dist_score +
                                 0.2 * quadrant_match_score +
                                 0.1 * size_ratio_score)

    # Section neighbor overlap score
    section_score = 0
    if 'section_neighbors' in fp1 and 'section_neighbors' in fp2:
        section1 = set(fp1['section_neighbors'])
        section2 = set(fp2['section_neighbors'])

        # Calculate Jaccard similarity for section neighbors
        if section1 or section2:  # If either set is non-empty
            intersection = len(section1.intersection(section2))
            union = len(section1.union(section2))
            section_score = intersection / union if union > 0 else 0

    # Combine all scores with weights
    final_score = (0.15 * aspect_ratio_score +
                  0.40 * neighbor_score +
                  0.15 * quadrant_score +
                  0.10 * density_score +
                  0.10 * neighborhood_score +
                  0.10 * section_score)

    return final_score


def find_closest_rectangle(roi_coords, rectangles, img_size, previously_matched_rects=None):
    """
    Find the closest rectangle to an ROI, prioritizing smaller rectangles that closely match the ROI size.
    Enhanced to consider structural context and avoid previously matched rectangles when appropriate.

    Args:
        roi_coords: Tuple (x0, y0, x1, y1) with relative coordinates
        rectangles: List of rectangles in format (x, y, w, h, id)
        img_size: Tuple (width, height) of the image
        previously_matched_rects: Set of rectangle IDs that have already been matched to other ROIs

    Returns:
        The closest rectangle or None if no close rectangle is found
    """
    roi_x0, roi_y0, roi_x1, roi_y1 = roi_coords
    img_width, img_height = img_size

    # Convert relative ROI coordinates to absolute
    abs_x0 = int(roi_x0 * img_width)
    abs_y0 = int(roi_y0 * img_height)
    abs_x1 = int(roi_x1 * img_width)
    abs_y1 = int(roi_y1 * img_height)

    # Calculate ROI center and dimensions
    roi_center_x = (abs_x0 + abs_x1) / 2
    roi_center_y = (abs_y0 + abs_y1) / 2
    roi_width = abs_x1 - abs_x0
    roi_height = abs_y1 - abs_y0
    roi_area = roi_width * roi_height

    # Initialize previously matched rectangles if not provided
    if previously_matched_rects is None:
        previously_matched_rects = set()

    # Skip very large ROIs (likely page borders)
    if roi_area > 0.8 * img_width * img_height:
        print(f"  Skipping very large ROI (likely page border): {roi_area} pixels")
        return None

    # Filter out very large rectangles (likely page borders) and very small rectangles
    filtered_rectangles = []
    for rect in rectangles:
        x, y, w, h, rect_id = rect
        rect_area = w * h

        # Skip very large rectangles (likely page borders)
        if rect_area > 0.8 * img_width * img_height:
            continue

        # Skip very small rectangles
        if rect_area < 100:  # Minimum area threshold
            continue

        filtered_rectangles.append(rect)

    print(f"  Considering {len(filtered_rectangles)} rectangles after filtering out page borders and tiny rectangles")

    if not filtered_rectangles:
        print("  No suitable rectangles found after filtering")
        return None

    # Find rectangles that contain the ROI
    containing_rectangles = []
    for rect in filtered_rectangles:
        x, y, w, h, rect_id = rect

        # Check if rectangle contains the ROI
        if (x <= abs_x0 and y <= abs_y0 and
            x + w >= abs_x1 and y + h >= abs_y1):
            containing_rectangles.append(rect)

    # If we found containing rectangles, use those
    if containing_rectangles:
        print(f"  Found {len(containing_rectangles)} rectangles that contain this ROI")

        # Find the smallest containing rectangle
        smallest_rect = None
        smallest_area = float('inf')

        for rect in containing_rectangles:
            x, y, w, h, rect_id = rect
            area = w * h

            # Prefer rectangles that haven't been matched yet, but don't exclude previously matched ones
            area_factor = 0.9 if rect_id not in previously_matched_rects else 1.0
            adjusted_area = area * area_factor

            if adjusted_area < smallest_area:
                smallest_area = adjusted_area
                smallest_rect = rect

        if smallest_rect:
            print(f"  Selected the smallest containing rectangle: ID={smallest_rect[4]}, area={smallest_area}")
            return smallest_rect

    # If no containing rectangles, find the closest match
    print("  No containing rectangles found, looking for closest match")

    # Find the closest rectangle
    closest_rect = None
    best_score = float('inf')  # Lower is better

    # Define a maximum distance threshold (as a fraction of image diagonal)
    img_diagonal = (img_width**2 + img_height**2)**0.5
    max_distance_threshold = 0.2 * img_diagonal  # 20% of image diagonal

    # Track if we're in a sparse region (few rectangles nearby)
    nearby_rect_count = 0
    for rect in filtered_rectangles:
        x, y, w, h, rect_id = rect
        rect_center_x = x + w / 2
        rect_center_y = y + h / 2
        distance = ((roi_center_x - rect_center_x)**2 + (roi_center_y - rect_center_y)**2)**0.5
        if distance < max_distance_threshold:
            nearby_rect_count += 1

    sparse_region = nearby_rect_count < 5  # Consider it sparse if fewer than 5 rectangles nearby

    # Adjust scoring based on region density
    for rect in filtered_rectangles:
        x, y, w, h, rect_id = rect

        # Calculate rectangle center
        rect_center_x = x + w / 2
        rect_center_y = y + h / 2

        # Calculate distance between centers
        distance = ((roi_center_x - rect_center_x) ** 2 + (roi_center_y - rect_center_y) ** 2) ** 0.5

        # Skip rectangles that are too far away
        if distance > max_distance_threshold and not sparse_region:
            continue

        # Calculate area difference
        rect_area = w * h
        area_diff = abs(roi_area - rect_area) / max(roi_area, rect_area, 1)

        # Calculate overlap
        overlap_x0 = max(abs_x0, x)
        overlap_y0 = max(abs_y0, y)
        overlap_x1 = min(abs_x1, x + w)
        overlap_y1 = min(abs_y1, y + h)

        overlap_area = max(0, overlap_x1 - overlap_x0) * max(0, overlap_y1 - overlap_y0)
        overlap_ratio = overlap_area / min(roi_area, rect_area) if min(roi_area, rect_area) > 0 else 0

        # Calculate aspect ratio similarity
        roi_aspect = roi_width / roi_height if roi_height > 0 else 0
        rect_aspect = w / h if h > 0 else 0
        aspect_diff = abs(roi_aspect - rect_aspect)
        aspect_score = 1 / (1 + aspect_diff)

        # Penalize previously matched rectangles
        previously_matched_penalty = 1.5 if rect_id in previously_matched_rects else 1.0

        # Calculate position score - prefer rectangles in the same relative position
        # This helps with fields that should be in the same section of the document
        position_score = 1.0
        if sparse_region:
            # In sparse regions, be more lenient with position matching
            position_score = 0.5

        # Calculate score (lower is better)
        # Enhanced scoring that prioritizes:
        # 1. Rectangles with similar size and high overlap
        # 2. Rectangles that haven't been matched yet
        # 3. Rectangles with similar aspect ratio
        # 4. Rectangles that are closer to the ROI
        score = (
            distance *
            (1 + 2 * area_diff) *
            (1 - 2 * overlap_ratio) *
            previously_matched_penalty *
            (2 - aspect_score) *
            position_score
        )

        if score < best_score:
            best_score = score
            closest_rect = rect

    if closest_rect:
        x, y, w, h, rect_id = closest_rect
        print(f"  Selected closest matching rectangle: ID={rect_id}, position=({x},{y}), size={w}x{h}")

        # Check if the match is potentially problematic (very far from ROI)
        rect_center_x = x + w / 2
        rect_center_y = y + h / 2
        match_distance = ((roi_center_x - rect_center_x)**2 + (roi_center_y - rect_center_y)**2)**0.5

        if match_distance > 0.15 * img_diagonal:  # If match is more than 15% of diagonal away
            print(f"  WARNING: Selected rectangle is far from ROI (distance={match_distance:.2f})")
            print(f"  This may indicate a potentially problematic match")

    return closest_rect


if __name__ == '__main__':
    # Example usage
    template_json_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output\template.json"
    target_image_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\images\page_53.png"
    roi_json_path = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output\adjusted_template.json"
    output_dir = r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis Batch 05-02-25\Binder6\output\match_template"

    try:
        result = match_template_to_target(template_json_path, target_image_path, roi_json_path, output_dir, debug=True)

        if result:
            print("\n=== TEMPLATE MATCHING COMPLETE ===")
            print(f"Visualization saved to: {result['visualization_path']}")
            print(f"Adjusted template JSON saved to: {result['output_json_path']}")
            print(f"Debug files saved to: {result['debug_directory']}")

            # Print summary of adjusted ROIs
            print("\nAdjusted ROIs Summary:")
            for group_id, rois in result['adjusted_rois'].items():
                print(f"\n--- Group {group_id}: {len(rois)} ROIs ---")
                for roi in rois:
                    # Display user label if available
                    display_name = roi.get('user_label', roi['name']) if 'user_label' in roi else roi['name']
                    print(f"  {roi['name']}" + (f" (User Label: {display_name})" if 'user_label' in roi else ""))
                    print(f"    Coordinates: ({roi['relativeX0']:.4f}, {roi['relativeY0']:.4f}, "
                          f"{roi['relativeX1']:.4f}, {roi['relativeY1']:.4f})")
                    print(f"    Matched to rectangle: {roi.get('matched_rectangle', 'None')}")

                    # Display table information if applicable
                    if roi.get('isTable', False):
                        print(f"    Table: Yes")
                        if roi.get('columnNames'):
                            print(f"    Column Names: {roi.get('columnNames')}")
                        if roi.get('columnRatios'):
                            print(f"    Column Ratios: {roi.get('columnRatios')}")
        else:
            print("\nTemplate matching failed.")

    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()
