<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>7a63fe78-52f8-44b7-8c6d-43fb1234d769</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>
    </StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>OpenAI_Parallel_Processing</Name>
    <RootNamespace>OpenAI_Parallel_Processing</RootNamespace>
    <InterpreterId>MSBuild|venv|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="api_request_parallel_processor.py" />
    <Compile Include="csv_to_array.py" />
    <Compile Include="generate_requests.py" />
    <Compile Include="OpenAI_Parallel_Processing.py" />
    <Compile Include="prompts.py" />
    <Compile Include="save_generated_data_to_csv.py" />
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
    <Content Include=".git\config" />
    <Content Include=".git\description" />
    <Content Include=".git\HEAD" />
    <Content Include=".git\hooks\applypatch-msg.sample" />
    <Content Include=".git\hooks\commit-msg.sample" />
    <Content Include=".git\hooks\fsmonitor-watchman.sample" />
    <Content Include=".git\hooks\post-update.sample" />
    <Content Include=".git\hooks\pre-applypatch.sample" />
    <Content Include=".git\hooks\pre-commit.sample" />
    <Content Include=".git\hooks\pre-merge-commit.sample" />
    <Content Include=".git\hooks\pre-push.sample" />
    <Content Include=".git\hooks\pre-rebase.sample" />
    <Content Include=".git\hooks\pre-receive.sample" />
    <Content Include=".git\hooks\prepare-commit-msg.sample" />
    <Content Include=".git\hooks\push-to-checkout.sample" />
    <Content Include=".git\hooks\sendemail-validate.sample" />
    <Content Include=".git\hooks\update.sample" />
    <Content Include=".git\index" />
    <Content Include=".git\info\exclude" />
    <Content Include=".git\logs\HEAD" />
    <Content Include=".git\logs\refs\heads\main" />
    <Content Include=".git\logs\refs\remotes\origin\HEAD" />
    <Content Include=".git\objects\pack\pack-1dda50abca1373c23394a1f96ce05c352e549a4a.idx" />
    <Content Include=".git\objects\pack\pack-1dda50abca1373c23394a1f96ce05c352e549a4a.pack" />
    <Content Include=".git\objects\pack\pack-1dda50abca1373c23394a1f96ce05c352e549a4a.rev" />
    <Content Include=".git\packed-refs" />
    <Content Include=".git\refs\heads\main" />
    <Content Include=".git\refs\remotes\origin\HEAD" />
    <Content Include="cls_input.csv" />
    <Content Include="input.csv" />
    <Content Include="item_input.csv" />
    <Content Include="README.md" />
    <Content Include="requirements.txt" />
    <Content Include=".env" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include=".git\" />
    <Folder Include=".git\hooks\" />
    <Folder Include=".git\info\" />
    <Folder Include=".git\logs\" />
    <Folder Include=".git\logs\refs\" />
    <Folder Include=".git\logs\refs\heads\" />
    <Folder Include=".git\logs\refs\remotes\" />
    <Folder Include=".git\logs\refs\remotes\origin\" />
    <Folder Include=".git\objects\" />
    <Folder Include=".git\objects\info\" />
    <Folder Include=".git\objects\pack\" />
    <Folder Include=".git\refs\" />
    <Folder Include=".git\refs\heads\" />
    <Folder Include=".git\refs\remotes\" />
    <Folder Include=".git\refs\remotes\origin\" />
    <Folder Include=".git\refs\tags\" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="venv\">
      <Id>venv</Id>
      <Version>3.9</Version>
      <Description>venv (Python 3.9 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>