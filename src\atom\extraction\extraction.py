"""
A module for extracting data from preprocessed data using ROIs.

Example Usage:

brock_pid = 1
filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0014 - MPLX Secretariat 1/received/MPLX ISO Combined.pdf", brock_pid)

config = RoiExtractionConfig(
    project_id = project_id,
    filename = filename,
    # roi_layout = roi_layout,
)
# Specific configuration
# config.extract_general = False
# config.extract_tables = False
# config.tables_to_extract = ["bom"]
config.replace_non_standard_characters = True
config.enable_concurrency(True)
# config.enable_concurrency(False)

# config.pages_to_extract = "1"
# config.pages_to_extract = "170"
# config.pages_to_extract = ""
# print(config.pages_to_extract)

extractor = RoiExtraction(config=config)
extractor.run_extraction()

extractor.save_results("debug/extraction_results/all_results.xlsx")

print("Finished extraction:", extractor.duration, "seconds")

# TODO
# "merge_to_bom": [ "lineNumber", "drawing", "sheet", "area", "revision", "pid" ],

"""

import os
import re
import time
import logging
import unicodedata
import orjson
import structlog
import multiprocessing
import asyncio
import logging.handlers

import polars as pl
import pandas as pd


from functools import wraps
from collections import defaultdict
from difflib import SequenceMatcher
from concurrent.futures import ProcessPoolExecutor, as_completed
from google import genai
from google.genai import types


from src.app_paths import getSourceRawDataPath, getSourceExtractionOptionsPath
from src.utils.spatial_utils import find_overlapping_rectangles_with_areas
from src.utils import convert_roi_payload
from src.atom.ocr.gemini.prompt_builder import prompt_builder

from .extractionconfig import RoiExtractionConfig
from .table_builder import create_logical_table_structure
from .bbox_utils import find_duplicate_text_bboxes
from .elevation_utils import parse_elevation

# Global for the workers
log_queue = None


class StructLogOnlyHandler(logging.handlers.QueueHandler):

    """Ensure only structlog messages are sent to the queue e.g. ignores third party library logging"""
    def emit(self, record: logging.LogRecord):
        # Multi-layer check for structlog messages
        msg = record.getMessage()

        # print(1111, msg)
        # print(record.name)
        # super().emit(record)

        # return

        # Fast pre-filter: must contain our marker
        if '"_is_structlog": true' not in msg:
            return

        # Additional check: must start with '{' (JSON object)
        if not msg.strip().startswith('{'):
            return

        # If both checks pass, it's very likely a structlog message
        super().emit(record)


def add_logger_name(_, __, event_dict):
    """For third party libraries"""
    record = event_dict.get("_record")
    if record:
        event_dict["logger"] = record.name
    else:
        event_dict["logger"] = "unknown"

    event_dict["_is_structlog"] = True
    return event_dict

def init_worker_logging(queue):
    global log_queue
    log_queue = queue
    handler = StructLogOnlyHandler(log_queue)
    root = logging.getLogger()
    root.setLevel(logging.DEBUG)
    root.handlers = [handler]
    configure_structlog()

    # Process third party logs to structlog compatible format
    from structlog.stdlib import ProcessorFormatter
    formatter = ProcessorFormatter(
        processor=structlog.processors.JSONRenderer(),
        foreign_pre_chain=[
            structlog.processors.TimeStamper(fmt="iso", utc=True),
            structlog.processors.add_log_level,
            add_logger_name
        ],
    )
    handler2 = StructLogOnlyHandler(log_queue)
    # handler2.setFormatter(formatter)

    # Add names of third party libraries to capture
    third_party_logs = ["google_genai", "httpcore"]
    for log in third_party_logs:
        logging.getLogger(log).addHandler(handler2)
        logging.getLogger(log).setLevel(logging.DEBUG)

def add_structlog_flag(_, __, event_dict):
    """
    This processor auto marks all structlog events
    """
    try:
        event_dict["_is_structlog"] = True
    except Exception as e:
        pass
    return event_dict

def configure_structlog():
    structlog.configure(
        processors=[
            add_structlog_flag, # mark as structlog event
            structlog.processors.TimeStamper(fmt="iso", utc=True),
            add_full_traceback_calls,
            structlog.processors.add_log_level,
            structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.make_filtering_bound_logger(logging.DEBUG),
        cache_logger_on_first_use=True,
    )

def relative_path(filename, base_dirs=None):
    filename = os.path.abspath(filename)
    if ".venv" in filename:
        return None
    if base_dirs is None:
        base_dirs = [os.getcwd()]
    for base in base_dirs:
        try:
            rel = os.path.relpath(filename, base)
            if not rel.startswith(".."):
                return rel
        except Exception:
            continue
    filename = filename.replace("\\", "/")
    return filename

def get_full_traceback_calls(exc: Exception, base_dirs=None):
    """
    Return a list of dicts for every frame in the exception traceback, with:
      - relative filename
      - line number
      - function name
    """
    tb = exc.__traceback__
    calls = []
    while tb is not None:
        frame = tb.tb_frame
        filename = frame.f_code.co_filename
        lineno = tb.tb_lineno
        funcname = frame.f_code.co_name
        rel_filename = relative_path(filename, base_dirs)
        if rel_filename is None:
            break
        calls.append(f"{rel_filename}:{lineno}:{funcname}")
        tb = tb.tb_next
    return calls

def add_full_traceback_calls(_, __, event_dict):
    exc = event_dict.pop("exception", None)
    if not isinstance(exc, BaseException):
        return event_dict
    calls = get_full_traceback_calls(exc)[-2:]
    calls.reverse()
    event_dict["traceback"] = calls
    event_dict["exception_message"] = f"{type(exc).__name__}: {str(exc)}"
    return event_dict

def timer(func):
    """"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        end = time.perf_counter()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

def worker_configurer(log_queue):
    """Setup logging for worker process to send logs to queue."""
    handler = logging.handlers.QueueHandler(log_queue)
    root = logging.getLogger()
    root.setLevel(logging.DEBUG)
    # Remove other handlers
    root.handlers = []
    root.addHandler(handler)

def drop_by_indices(df: pl.DataFrame, indices_to_remove: set[int]) -> pl.DataFrame:
    return df.filter(~pl.arange(0, df.height).is_in(list(indices_to_remove)))


class SafeFileHandler(logging.FileHandler):
    """A file handler that silently ignores errors in logging."""
    def handleError(self, record):
        # Override to silently ignore errors in logging
        pass

class BomKeywordFilter:

    header_values = [
                "PT", "PT.", "ID", "NO", "DESCRIPTION", "NPD", "(IN)", "CMDTY CODE", "QTY",
                "IDENT", "QTY", "POS", "HOLD", "COMPONENT DESCRIPTION", "N.B. (INS)", "ITEM CODE",
                "N.B.", "(INS)", "N.P.S.", "(IN)", "N.S. (INS)", "N.S.", "N.P.S.", "MATERIALS", "GMN",
                "MK", "MK.", "MK NO", "MK NO.", "MK NO NPD", "MK NO NPD (IN)", "NO NPD", "NPD","SPEC",
                "PART NO", "PART NO.", "PART", "MK NPD","NO.", "ND", "ND.", "BILL OF MATERIALS", "SIZE",
                "NO (IN)", "DN", "SCH/CLASS", "MATERIAL CODE"
            ]

    ignore_terms = [
                "CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION", "IN VISUAL CHECK FROM ENPPA",
                "COMPONENT DESCRIPTION", "N.B. (INS)", "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R",
                "4S", "5R", "5S", "HOLD", "HARDWARES"
                ] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    bottom_limit_stop_flag = [
            "PIECE MARKS", "PIECE MARK", "[1]", "[2]", "[3]", "[4]", "[5]", "[6]", "[7]",
            "PIPE SPOOLS", "PIPE SPOOL", "NOTE:", "1.GM", "TOTAL FABRICATION WEIGHT", "CUT PIECE LIST"
        ]

    field_keywords = ["ERECTION ITEMS","ERECTION MATERIALS", "ERECTION", "OTHER THAN SHOP MATERIALS", "FIELD INSTALL"]
    shop_keywords = ["FABRICATION ITEMS", "FABRICATION MATERIALS", "FABRICATION", "SHOP MATERIALS",
                "FABRICATION MATERIALS COMPONENT DESCRIPTION"]
    misc_keywords = ["MISC.", "MISCELLANEOUS COMPONENTS", "OFFSHORE", "OFFSHORE MATERIALS", ]
    instr_keywords = ["INSTRUMENTS", "MISCELLANEOUS COMPONENTS"]

    component_keywords = ["BOLTS", "PIPE", "FITTINGS", "FLANGES", "GASKETS", "VALVES", "INSTRUMENTS", "INSTR.",
            "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS", "MISCELLANEOUS COMPONENTS",
            "MISC.","PIPE SUPPORTS", "OLETS", "FASTENERS", "GASKETS / BOLTS", "WELDS"]


    # Define the prefixes for partial matches (Checks if it begins to classify the Material Scope)
    field_prefixes = ("FIELD", "ERECTION", "OTHER THAN SHOP")
    shop_prefixes = ("SHOP", "FABRICAT", "OTHER THAN FIELD")
    misc_prefixes = ("MISCELLANEOUS",)  # Note the comma to make it a tuple with one element
    instr_prefixes = ("INSTR/", "ISTR", "INSTRUMENTS")

    # Define list of Exceptions
    exceptions = ["I-ROD", "SUPPORT - UBOLT", "SUPPORT - GUIDE", "SUPPORT - ANCHOR", "SHIM PLATE", "INSTRUMENT COMPONENT", "INSTRUMENT COMPONENTS"]

    # Combine all keywords and prefixes into a single list
    # all_keywords = field_keywords + list(field_prefixes) + list(shop_keywords) + list(shop_prefixes) #+ misc_keywords + list(misc_prefixes)# + instr_keywords + list(instr_prefixes)
    #all_keywords = field_keywords + field_prefixes + shop_keywords + shop_prefixes

    # Ensure all keywords are lists
    field_keywords = list(field_keywords)
    shop_keywords = list(shop_keywords)
    misc_keywords = list(misc_keywords)
    instr_keywords = list(instr_keywords)

    filter_terms = header_values + ignore_terms

    all_prefixes = field_prefixes + shop_prefixes + misc_prefixes #+ instr_prefixes
    all_keywords = field_keywords + shop_keywords + misc_keywords #+ instr_keywords


# Normalization handles accented characters (é → e, ö → o, etc.)
def normalize_ascii(text: str) -> str:
    return unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')

# Precompiled regex for punctuation and symbols
REPLACEMENTS = [
    (re.compile(r'[\u2018\u2019]'), "'"),   # Curly single quotes
    (re.compile(r'[\u201c\u201d]'), '"'),   # Curly double quotes
    (re.compile(r'[\u2013\u2014]'), '-'),   # En and Em dashes
    (re.compile(r'\u2026'), '...'),         # Ellipsis
    (re.compile(r'\u00B7'), '-'),           # Middle dot
    (re.compile(r'[\u00AB\u00BB]'), '"'),   # Angle quotes
    (re.compile(r'\u00A0'), ' '),           # Non-breaking space
]

def fast_regex_ascii_clean(text: str) -> str:
    if not isinstance(text, str):
        return text
    text = normalize_ascii(text)
    for pattern, replacement in REPLACEMENTS:
        text = pattern.sub(replacement, text)
    return text

def safe_json_parse(s):
    try:
        return orjson.loads(s)
    except Exception:
        return {}

def sort_bbox_logical(text_data, y_tolerance=20):
    # Get the y-center and x-left for each box
    items = []
    for bbox, text, _ in text_data:
        x_min = min(p[0] for p in bbox)
        y_center = sum(p[1] for p in bbox) / 4
        items.append((y_center, x_min, bbox, text))

    # Sort primarily by y, then x
    items.sort()

    # Group into lines using y_tolerance
    lines = []
    current_line = []
    last_y = None

    for y, x, bbox, text in items:
        if last_y is None or abs(y - last_y) <= y_tolerance:
            current_line.append((x, bbox, text))
        else:
            lines.append(sorted(current_line))
            current_line = [(x, bbox, text)]
        last_y = y
    if current_line:
        lines.append(sorted(current_line))

    # Flatten the sorted lines into final list
    sorted_items = [(bbox, text) for line in lines for x, bbox, text in line]
    return sorted_items


def analyze_and_filter_outliers(raw_table_df: pl.DataFrame, font_size_tolerance=0.5):
    """Generic filter for outliers"""
    # Define red color for revisions as a tuple
    red_color = [255, 0, 0]

    # Ensure 'color' column is properly formatted
    raw_table_df = raw_table_df.with_columns(
        pl.col("color").map_elements(
            lambda x: list(x) if hasattr(x, '__len__') and len(x) == 3 else [0, 0, 0],
            return_dtype=pl.List(pl.Int64)
        ).alias("color")
    )

    # Separate annotations and text data
    annotations_df = raw_table_df.filter(pl.col("type") != "Text")
    text_df = raw_table_df.filter(pl.col("type") == "Text")

    # Separate red text and non-red text
    red_text_df = text_df.filter(pl.col("color") == red_color)
    non_red_text_df = text_df.filter(pl.col("color") != red_color)

    # Filter out 'Times-Roman' font - DEBUG TEMPORARY! Lines contains hidden characters!!
    non_red_text_df = non_red_text_df.filter(pl.col("font") != "Times-Roman")

    # Find common attributes from non-red text
    if len(non_red_text_df) > 0:
        # Get mode (most common value) for each attribute
        common_font = non_red_text_df.select(pl.col("font").mode().first()).item()
        common_font_size = non_red_text_df.select(pl.col("font_size").mode().first()).item()
        common_font_color = non_red_text_df.select(pl.col("color").mode().first()).item()
    elif len(text_df) > 0:
        # If all text is red, use the most common attributes from all text
        common_font = text_df.select(pl.col("font").mode().first()).item()
        common_font_size = text_df.select(pl.col("font_size").mode().first()).item()
        common_font_color = text_df.select(pl.col("color").mode().first()).item()
    else:
        # Both are blank
        common_font, common_font_size, common_font_color = None, None, None


    # non_red_text_df = non_red_text_df.with_columns(
    #     pl.col("font_size").cast(pl.Float64).alias("font_size")
    # )

    # Create outlier conditions and reasons
    if common_font and len(non_red_text_df) > 10: # workaround
        common_font_size = float(common_font_size)

        # Add outlier detection columns
        non_red_text_df = non_red_text_df.with_columns([
            (pl.col("font") != common_font).alias("font_outlier"),
            (pl.col("font_size") - pl.lit(common_font_size)).abs().gt(font_size_tolerance).alias("size_outlier")
        ])

        # Create outlier reason column
        non_red_text_df = non_red_text_df.with_columns([
            pl.when(pl.col("font_outlier") & pl.col("size_outlier"))
            .then(pl.lit("Uncommon Font; Font Size Outlier"))
            .when(pl.col("font_outlier"))
            .then(pl.lit("Uncommon Font"))
            .when(pl.col("size_outlier"))
            .then(pl.lit("Font Size Outlier"))
            .otherwise(pl.lit(""))
            .alias("outlier_reason")
        ])

        # Filter outliers
        text_outliers_df = non_red_text_df.filter(
            pl.col("font_outlier") | pl.col("size_outlier")
        ).drop(["font_outlier", "size_outlier"])

        # Get regular non-red text (not outliers)
        regular_non_red_df = non_red_text_df.filter(
            ~(pl.col("font_outlier") | pl.col("size_outlier"))
        ).drop(["font_outlier", "size_outlier"])

        # Combine regular data: non-red text that's not an outlier + all red text
        regular_data_df = pl.concat([regular_non_red_df, red_text_df], how="diagonal")
    else:
        # No common font found or no non-red text
        text_outliers_df = pl.DataFrame()
        regular_data_df = text_df

    # Add outlier_reason column to annotations (if any)
    if len(annotations_df) > 0:
        annotations_df = annotations_df.with_columns([
            pl.lit("Annotation").alias("outlier_reason")
        ])

    # Combine text outliers with annotations
    if len(text_outliers_df) > 0 and len(annotations_df) > 0:
        outliers_df = pl.concat([text_outliers_df, annotations_df])
    elif len(text_outliers_df) > 0:
        outliers_df = text_outliers_df
    elif len(annotations_df) > 0:
        outliers_df = annotations_df
    else:
        outliers_df = pl.DataFrame()

    # Ensure regular_data_df has outlier_reason column for consistency
    if len(regular_data_df) > 0 and "outlier_reason" not in regular_data_df.columns:
        regular_data_df = regular_data_df.with_columns([
            pl.lit("").alias("outlier_reason")
        ])

    return regular_data_df, outliers_df

def filter_outliers():
    pass

def process_table_data(config: RoiExtractionConfig, pdf_page: int, raw_table_df: pl.DataFrame, column_coords, table_type):
    """
    """
    regular_data_df, potential_outliers_df = analyze_and_filter_outliers(raw_table_df)

    if config.remove_outliers and len(regular_data_df) > 10: # workaround
        structured_data = regular_data_df #raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
        outliers_data = potential_outliers_df
        outliers_data = outliers_data.with_columns([pl.lit("dropped").alias("outlier_action")])
    else:
        structured_data = raw_table_df
        outliers_data = pl.DataFrame()  # Empty DataFrame if not removing outliers

    headers_selected = True # TODO
    structured_table_df = create_logical_table_structure(config, pdf_page, structured_data, column_coords, headers_selected, table_type)
    structured_table_df = structured_table_df.with_columns([pl.lit(pdf_page).alias("pdf_page")])
    return structured_table_df

def preprocess_text(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = ''.join(c for c in unicodedata.normalize('NFD', text) if unicodedata.category(c) != 'Mn')
    text = re.sub(r'[^\w\s]', '', text)
    text = ' '.join(text.split())
    return text

def find_similar_keyword_match(text, keywords, threshold: float = 0.8):
    """Returns keyword if meets similarity threshold"""
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if SequenceMatcher(None, preprocess_text(keyword), processed_text).ratio() >= threshold:
            return keyword

    return None

def starts_with_prefix_and_short(text, prefixes, max_words=2):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False
    words = text.split()
    result = any(text.startswith(prefix) for prefix in prefixes) and len(words) <= max_words
    return result

def keyword_in_first_words(text, keywords, first_n_words=5, min_total_words=4):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False

    words = text.split()

    # Check for exact match first
    if text in keywords:
        return True

    if len(words) < min_total_words:
        return False

    # Only check the first 'n' words
    first_words = ' '.join(words[:first_n_words])

    # Check if any of the keywords match the first 'n' words
    result = any(keyword in first_words for keyword in keywords)
    #print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")

    # if result:
    #     print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    # else:
    #     print(f"DEBUG: '{text}' does not contain a keyword in first {first_n_words} words")
    return result
    # return any(keyword in first_words for keyword in keywords)

def replace_non_standard_characters(df: pl.DataFrame) -> pl.DataFrame:
    """Replace non-standard characters with ASCII characters"""
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    exprs = []
    for col in df.columns:
        if df.schema[col] == pl.Utf8:
            expr = pl.col(col)
            for target, replacement in replacements.items():
                expr = expr.str.replace_all(target, replacement)
            exprs.append(expr.alias(col))
        else:
            exprs.append(pl.col(col))
    return df.select(exprs)


def combine_nearby_words(raw_df: pl.DataFrame, y_tolerance=1.5, x_tolerance=5):

    def are_y_coords_close(coord1, coord2, tolerance):
        # Require one y coord closer and the other less strictly close

        # return abs(coord1[1] - coord2[1]) <= tolerance and abs(coord1[3] - coord2[3]) <= tolerance
        yd1 = abs(coord1[1] - coord2[1])
        yd2 = abs(coord1[3] - coord2[3])
        return any([
            (yd1 <= tolerance * 2 and yd2 <= tolerance),
            (yd1 <= tolerance and yd2 <= tolerance * 2)
        ])

    def should_combine(word1, word2, tolerance=x_tolerance):
        return word2['coordinates2'][0] - word1['coordinates2'][2] <= tolerance

    # Sort the dataframe by y0 coordinates using Polars
    sorted_df = raw_df.with_row_index("original_index").sort(
        pl.col("coordinates2").map_elements(lambda x: x[1], return_dtype=pl.Float64)
    )

    combined_texts = {}
    current_line = []

    def process_line(line):
        line.sort(key=lambda x: x[1]['coordinates2'][0])  # Sort by x0

        row_text = " ".join([l[1]["value"] for l in line])
        # combined_text = line[0][1]['Text']
        combined_text = line[0][1]['value']
        start_index = 0

        # Experimental - if horizontal line components are smaller, more likely to be category,
        # so increase tolerance to increase chance of grouping
        x_tol = x_tolerance
        if len(row_text) < 20 and len(line) < 6:
            x_tol = x_tolerance * 3
        else:
            x_tol = x_tolerance

        for j in range(1, len(line)):
            if should_combine(line[j-1][1], line[j][1], x_tol):
                combined_text += ' ' + line[j][1]['value']
            else:
                for x in line[start_index:j]:
                    combined_texts[x[0]] = combined_text
                start_index = j
                # combined_text = line[j][1]['Text']                #
                combined_text = line[j][1]['value']
        for x in line[start_index:]:
            combined_texts[x[0]] = combined_text

    # Convert to list of tuples for processing (similar to iterrows)
    rows_list = []
    for i in range(sorted_df.height):
        row_dict = sorted_df.row(i, named=True)
        rows_list.append((row_dict['original_index'], row_dict))

    for i, row in rows_list:
        if not current_line or are_y_coords_close(current_line[-1][1]['coordinates2'], row['coordinates2'], y_tolerance):
            current_line.append((i, row))
            continue
        process_line(current_line)
        current_line = [(i, row)]  # New line

    # Process the last line
    if current_line:
        process_line(current_line)

    # Create a Polars DataFrame with the combined texts
    if combined_texts:
        combined_data = [
            {"original_index": idx, "combined_text": text}
            for idx, text in combined_texts.items()
        ]
        combined_df = pl.DataFrame(combined_data)

        # Join the combined text back to the original dataframe
        result_df = sorted_df.join(
            combined_df,
            on="original_index",
            how="left"
        ).drop("original_index")
    else:
        # If no combinations were made, add an empty combined_text column
        result_df = sorted_df.with_columns(
            pl.lit(None).alias("combined_text")
        ).drop("original_index")

    return result_df

def bom_pass_filter(df: pl.DataFrame) -> pl.DataFrame:
    """Detect BOM terms, limits and update fields"""
    # Find the y0 value from the row where the text matches any value in bottom_limit_stop_flag
    table_filter = BomKeywordFilter()

    bottom_limit_y0 = None
    match_row = (
        df.filter(pl.col("combined_text").is_in(table_filter.bottom_limit_stop_flag))
        .head(1)
    )
    if not match_row.is_empty():
        bottom_limit_y0 = match_row["y0"].item()
        # Drop rows where y0 is greater than or equal to bottom_limit_y0
        df = df.filter(pl.col("y0") < bottom_limit_y0)

    # Filter out rows where the "combined_text" column matches any value in filter_terms
    df = df.filter(~pl.col("combined_text").is_in(table_filter.filter_terms))

    # Fill nulls in 'combined_text' with empty string, strip and uppercase
    df = df.with_columns([
        pl.col("combined_text")
        .fill_null("")
        .str.strip_chars()
        .str.to_uppercase()
        .alias("combined_text")
    ])

    # Initialize new columns using Polars
    df = df.with_columns([
        pl.lit(None).alias("material_scope"),
        pl.lit(None).alias("component_category"),
        pl.lit(False).alias("is_component_keyword"),
        pl.lit(False).alias("is_keyword")
    ])

    # Use Polars map_elements to apply the helper functions vectorized
    # Material scope detection using cascading when conditions with forward fill
    df = df.with_columns([
        pl.when(
            pl.col("combined_text").map_elements(
                lambda x: starts_with_prefix_and_short(x, table_filter.field_prefixes),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Field"))
        .when(
            pl.col("combined_text").map_elements(
                lambda x: starts_with_prefix_and_short(x, table_filter.shop_prefixes),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Shop"))
        .when(
            pl.col("combined_text").map_elements(
                lambda x: starts_with_prefix_and_short(x, table_filter.misc_prefixes),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Misc."))
        .when(
            pl.col("combined_text").map_elements(
                lambda x: keyword_in_first_words(x, table_filter.field_keywords),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Field"))
        .when(
            pl.col("combined_text").map_elements(
                lambda x: keyword_in_first_words(x, table_filter.shop_keywords),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Shop"))
        .when(
            pl.col("combined_text").map_elements(
                lambda x: keyword_in_first_words(x, table_filter.misc_keywords),
                return_dtype=pl.Boolean
            )
        ).then(pl.lit("Misc."))
        .otherwise(None)
        .forward_fill()
        .alias("material_scope")
    ])

    # Component category detection with stateful carry-forward
    df = df.with_columns([
        pl.col("combined_text").map_elements(
            lambda x: find_similar_keyword_match(x, table_filter.component_keywords),
            return_dtype=pl.String
        ).alias("component_category")
    ])

    # Mark rows with component keywords
    df = df.with_columns([
        pl.col("component_category").is_not_null().alias("is_component_keyword")
    ])

    # Forward fill component categories
    df = df.with_columns([
        pl.col("component_category").forward_fill().alias("component_category")
    ])

    # Mark keyword rows using vectorized operations (with exceptions check)
    df = df.with_columns([
        (
            (
                pl.col("combined_text").map_elements(
                    lambda x: starts_with_prefix_and_short(x, table_filter.all_prefixes),
                    return_dtype=pl.Boolean
                ) |
                pl.col("combined_text").map_elements(
                    lambda x: keyword_in_first_words(x, table_filter.all_keywords),
                    return_dtype=pl.Boolean
                )
            ) &
            ~pl.col("combined_text").map_elements(
                lambda x: x.strip().upper() in table_filter.exceptions,
                return_dtype=pl.Boolean
            )
        ).alias("is_keyword")
    ])

    # Add keyword-matched rows to outliers_df
    keyword_matches = df.filter((pl.col("is_keyword") | pl.col("is_component_keyword")))
    if not keyword_matches.is_empty():
        keyword_matches = keyword_matches.with_columns([pl.lit("Keyword Match").alias("outlier_reason")])
        # outliers_df = pd.concat([outliers_df, keyword_matches], ignore_index=True)

    # Filter out keyword and component keyword rows
    df = df.filter(~(pl.col("is_keyword") | pl.col("is_component_keyword")))

    # TODO - concat keyword matches with df
    return df

def process_table_rois(config: RoiExtractionConfig,
                       pdf_page: int,
                       page_df: pl.DataFrame,
                       table_rois: dict):
    """
    Args:
        pdf_page: Page number (1-indexed)
        page_df: Page DataFrame
        table_rois: All Table ROIs
    """
    logger = structlog.get_logger(f"page-worker-{pdf_page}")
    logger.debug("process_table_rois", pdf_page=pdf_page)

    text_bboxes = page_df.select(["x0", "y0", "x1", "y1"]).rows()

    annotations_df = page_df.filter(pl.col("type") != "Text")
    tables_data: dict = {}
    logs: list = []

    if config.replace_non_standard_characters:
        page_df = replace_non_standard_characters(page_df)

    logger.debug("Checking page font sizes", pdf_page=pdf_page)
    logger.debug(f"Table ROIs: {list(table_rois.keys())}", pdf_page=pdf_page)

    for table_type, table_roi_config in table_rois.items():

        logger.debug(f"Processing {table_type} table", table_type=table_type, pdf_page=pdf_page)
        logger.debug(str(table_roi_config), table_type=table_type, pdf_page=pdf_page)

        try:
            table_bbox = table_roi_config["bbox"]
            table_overlaps = find_overlapping_rectangles_with_areas([table_bbox], text_bboxes)[0]
            text_bbox_indices = [index for index, _ in table_overlaps]
            raw_table_df = page_df[text_bbox_indices]

            if raw_table_df.is_empty():
                # No overlapping data with region
                continue

            if config.combine_nearby_text:
                raw_table_df = combine_nearby_words(raw_table_df, y_tolerance=2, x_tolerance=8)
            else:
                raw_table_df = raw_table_df.with_columns(pl.col("value").alias("combined_text"))

            raw_table_df = raw_table_df.sort("y0", descending=False)

            filtered_df = raw_table_df.clone()

            if config.filter_table_terms and table_type.lower() == "bom":
                filtered_df = bom_pass_filter(filtered_df)

            raw_table_df = filtered_df

            if raw_table_df.is_empty():
                # log - no page raw table data after filter table terms
                continue

            column_coords = table_roi_config["columns"]
            structured_table_df = process_table_data(config, pdf_page, raw_table_df, column_coords, table_type)

            logger.debug(f"Processed {table_type} table - {len(structured_table_df)} rows", table_type=table_type, pdf_page=pdf_page)
            tables_data[table_type] = structured_table_df

        except Exception as e:
            logger.error(f"Failed to process {table_type} table", table_type=table_type, pdf_page=pdf_page, exception=e)

    return {"pdf_page": pdf_page, "data": {"tables": tables_data}}

def process_table_rois_wrapper(config: RoiExtractionConfig,
                               pdf_page: int,
                               page_df: pl.DataFrame,
                               table_rois: dict):
    try:
        return process_table_rois(config, pdf_page, page_df, table_rois)
    except Exception as e:
        logger = structlog.get_logger()
        logger.error("Failed to process table rois - uncaught error", pdf_page=pdf_page, exception=e)
        return {"pdf_page": pdf_page, "data": {}}

def sort_by_pdf_page(df: pl.DataFrame):
    """Retain original index after sorting"""
    return df.with_row_index(name="original_index").sort(by=["pdf_page", "original_index"]).drop("original_index")

def process_general_rois(config: RoiExtractionConfig,
                         pdf_page: int,
                         page_df: pl.DataFrame,
                         general_rois: dict):
    """
    Args:
        pdf_page: Page number (1-indexed)
        page_df: Page DataFrame
        general_rois: General ROIs
    """
    logger = structlog.get_logger(f"page-worker-{pdf_page}")
    logger.debug("Processing General ROIs", pdf_page=pdf_page, table_type="general")

    # Process general overlaps
    text_bboxes = page_df.select(["x0", "y0", "x1", "y1"]).rows()
    general_overlaps = find_overlapping_rectangles_with_areas(general_rois.values(), text_bboxes)
    general_overlaps = {list(general_rois.keys())[i]: values for i, values in general_overlaps.items()} # Gives table_type to overlapping indices mapping

    general_data = {"pdf_page": pdf_page}
    elevations_data = []
    # Filter within ROI and extract text values
    for roi_name, roi_overlaps in general_overlaps.items():
        text_bbox_indices = [(index, overlap_area >= config.general_min_area_threshold) for index, overlap_area in roi_overlaps]
        filtered_df = page_df[[index for index, overlap_ok in text_bbox_indices if overlap_ok]]
        values = filtered_df["value"].to_list()

        if config.general_exclude_detected_labels:
            # Exclude detected labels e.g. "Label:"
            detected_labels = [n for n, v in enumerate(values) if v.endswith(":")]
            logger.debug("Ignoring detected labels",
                         pdf_page=pdf_page,
                         table_type="general",
                         roi_name=roi_name,
                         outlier_reason="Detected Label",
                         outlier=page_df[detected_labels]["value"].to_list())
            filtered_df = filtered_df[[n for n in range(len(values)) if n not in detected_labels]]
            values = filtered_df["value"].to_list()

        excluded_indices = [index for index, overlap_ok in text_bbox_indices if not overlap_ok]
        if excluded_indices:
            logger.debug("Ignoring low overlapping text",
                         pdf_page=pdf_page,
                         table_type="general",
                         roi_name=roi_name,
                         outlier_reason="Below overlap threshold",
                         outlier=page_df[excluded_indices]["value"].to_list())

        if config.deduplicate_general_bboxes and len(values) > 1:
            bboxes = filtered_df.select(["x0", "y0", "x1", "y1"]).to_numpy().tolist()

            if roi_name.lower() != "isometric drawing area":
                duplicates = find_duplicate_text_bboxes(values, bboxes)
                if duplicates:
                    outliers = [values[i] for i in duplicates]
                    logger.debug("Ignoring duplicates",
                                pdf_page=pdf_page,
                                table_type="general",
                                outlier=outliers,
                                roi_name=roi_name,
                                outlier_reason="Duplicate Text BBOX")
                filtered_df = drop_by_indices(filtered_df, duplicates)

            if roi_name.lower() == "isometric drawing area":
                if not config.extract_elevations:
                    logger.warning("Skipping isometric drawing area", pdf_page=pdf_page, table_type="general")
                else:
                    elevations_data = filtered_df.filter(pl.col("elevation").is_not_null() & (pl.col("elevation") != "")).select(["pdf_page", "elevation"]).to_dicts()
                continue

            else:
                # Standard ROI extraction
                values = filtered_df["value"].to_list()

        general_data[roi_name] = " ".join(values)

    return {"pdf_page": pdf_page, "data": {"general": general_data, "elevations": elevations_data}}

def safe_table_concat(dfs: list[pl.DataFrame]) -> pl.DataFrame:
    all_columns = sorted(set(col for df in dfs for col in df.columns))

    # Determine a common schema (pick widest types)
    def get_common_schema(all_columns):
        schema = {}
        for col in all_columns:
            # Find the column type from any DataFrame that has this column
            col_type = None
            for df in dfs:
                if col in df.columns:
                    col_type = df.schema[col]
                    break
            # Default to Utf8 if column not found in any DataFrame (shouldn't happen)
            schema[col] = col_type if col_type is not None else pl.Utf8
        return schema

    # Get the common schema once
    common_schema = get_common_schema(all_columns)

    # Align all DFs
    aligned_dfs = []
    for df in dfs:
        # Add missing columns as nulls with appropriate types
        missing_cols = [col for col in all_columns if col not in df.columns]
        if missing_cols:
            df = df.with_columns([
                pl.lit(None, dtype=common_schema[col]).alias(col)
                for col in missing_cols
            ])

        # Reorder columns to match common schema
        df = df.select(all_columns)
        aligned_dfs.append(df)

    # Concatenate
    combined = pl.concat(aligned_dfs, how="vertical")
    return combined


def get_page_rois(config: RoiExtractionConfig, roi_cache: dict, rois: list, roi_group:int, page_width:float, page_height:float, rois_to_extract: list=None):
    logger = structlog.get_logger()
    cache_key = (roi_group, page_width, page_height)
    try:
        table_rois, general_rois = roi_cache[cache_key]
    except:
        table_rois = {}
        general_rois = {}
        for roi in rois:
            roi_name = roi["columnName"]
            is_table = roi["isTable"]
            if rois_to_extract and roi_name not in rois_to_extract:
                continue

            if not is_table:
                # Prep general coords
                general_rois[roi_name] = [
                    roi["relativeX0"] * page_width,
                    roi["relativeY0"] * page_height,
                    roi["relativeX1"] * page_width,
                    roi["relativeY1"] * page_height,
                ]
                continue

            if (config.tables_to_skip and roi_name.lower() in {item.lower() for item in config.tables_to_skip}
                or config.tables_to_extract and roi_name.lower() not in {item.lower() for item in config.tables_to_extract}):
                logger.debug("Extraction Configuration - Skipping table", table=roi_name)
                continue

            # Prep table coords
            table_rois[roi_name] = {
                # Outer table bounding box
                "bbox": [
                    roi["tableCoordinates"]["relativeX0"] * page_width,
                    roi["tableCoordinates"]["relativeY0"] * page_height,
                    roi["tableCoordinates"]["relativeX1"] * page_width,
                    roi["tableCoordinates"]["relativeY1"] * page_height,
                ],
                "headersSelected": roi.get("headersSelected", False),
                "columns": {}
            }
            # Per column coords
            for table_roi in roi["tableColumns"]:
                column_name = next(iter(table_roi))
                table_coords = table_roi[column_name]
                table_rois[roi_name]["columns"][column_name] = [
                    table_coords['relativeX0'] * page_width,
                    table_coords['relativeY0'] * page_height,
                    table_coords['relativeX1'] * page_width,
                    table_coords['relativeY1'] * page_height,
                ]
        roi_cache[cache_key] = (table_rois, general_rois)

    return table_rois, general_rois

def compress_page_ranges(pages):
    """Compress a list of page ranges into a string of ranges."""
    pages = sorted(set(pages))
    ranges = []
    start = prev = pages[0]
    for p in pages[1:]:
        if p == prev + 1:
            prev = p
        else:
            ranges.append((start, prev))
            start = prev = p
    ranges.append((start, prev))
    return ",".join(
        f"{s}-{e}" if s != e else f"{s}" for s, e in ranges
    )

async def crop_page_images(config: RoiExtractionConfig,
                           pdf_page: int,
                           general_rois: dict,
                           table_rois: dict):
    import fitz
    doc = fitz.open(config.filename)
    # os.makedirs("debug/extraction_images", exist_ok=True)
    images = []
    with doc:
        page = doc.load_page(pdf_page - 1)

        zoom = config.dpi_ocr / 72
        default_matrix = fitz.Matrix(zoom, zoom)

        width_limit = 4000

        rois = []

        for roi_name, roi_coords in general_rois.items():
            rois.append(("general", roi_name, roi_coords))

        for roi_name, roi_coords in table_rois.items():
            rois.append(("table", roi_name, roi_coords["bbox"]))

        for roi_type, roi_name, bbox in rois:
            matrix = default_matrix
            if width_limit and (bbox[2] - bbox[0]) * zoom > width_limit:
                adjusted_zoom = (width_limit - 1) / (bbox[2] - bbox[0])
                matrix = fitz.Matrix(adjusted_zoom, adjusted_zoom)

            crop_rect = fitz.Rect(bbox)
            pix = page.get_pixmap(matrix=matrix, clip=crop_rect)

            image_bytes = pix.tobytes("png")
            images.append({"roi_type": roi_type, "roi_name": roi_name, "image_bytes": image_bytes})

            # Save to debug folder
            # if roi_type == "general":
            #     os.makedirs(f"debug/extraction_images/general/{roi_name}", exist_ok=True)
            #     pix.save(f"debug/extraction_images/general/{roi_name}/{roi_name}_page_{pdf_page}.png")
            # else:
            #     os.makedirs(f"debug/extraction_images/{roi_name.lower()}", exist_ok=True)
            #     pix.save(f"debug/extraction_images/{roi_name.lower()}/{roi_name}_page_{pdf_page}.png")
    return images

def _batch_process_page(page_task_list):
    """Process multiple tasks for a single page in one process."""
    results = []
    for sync_func, kwargs in page_task_list:
        try:
            result = sync_func(**kwargs)
            results.append(result)
        except Exception as e:
            # Return error result for failed task
            pdf_page = kwargs['pdf_page'] if 'pdf_page' in kwargs else "unknown"
            results.append({"pdf_page": pdf_page, "data": {}, "error": str(e)})
    return results

def extract_json_from_ai_response(response_text: str):
    """
    Extract JSON data from an AI response string.

    Handles:
    - Raw JSON strings
    - JSON wrapped in markdown code fences (```json ... ```)
    - Extra text before or after JSON

    Raises ValueError if no valid JSON found.
    """
    # Regex to find JSON code fence blocks
    fenced_json_pattern = re.compile(
        r"```json\s*(\{.*?\}|\[.*?\])\s*```",
        re.DOTALL
    )

    match = fenced_json_pattern.search(response_text)
    if match:
        json_str = match.group(1).strip()
        try:
            return orjson.loads(json_str)
        except orjson.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON inside fenced code block: {e}")

    # If no fenced block, try to find any JSON substring inside the text
    # A bit heuristic: find first { ... } or [ ... ] pair with balanced brackets

    # Attempt to extract JSON by locating balanced braces/brackets
    def find_balanced_json(s):
        stack = []
        start = None
        for i, ch in enumerate(s):
            if ch in '{[':
                if start is None:
                    start = i
                stack.append(ch)
            elif ch in '}]' and stack:
                open_ch = stack.pop()
                # quick check matching pairs
                if (open_ch == '{' and ch != '}') or (open_ch == '[' and ch != ']'):
                    # mismatched brackets, reset
                    start = None
                    stack.clear()
                if not stack and start is not None:
                    # found balanced JSON substring
                    yield s[start:i+1]
                    start = None
        # no balanced found
        return

    # Try extracting balanced JSON snippets and parse them
    for candidate_json in find_balanced_json(response_text):
        try:
            return orjson.loads(candidate_json)
        except orjson.JSONDecodeError:
            continue

    def sanitize_json_with_inner_quotes(s: str):
        """
        Fix JSON with unescaped quotes inside string values.
        Example: {"key": "value with " quote"} -> {"key": "value with \" quote"}
        """
        import re

        # Remove any double-escaped quotes first
        s = s.replace(r'\"', '"')

        # Pattern to match JSON string values that may contain unescaped quotes
        # This finds: "key": "value potentially with quotes"
        def fix_value(match):
            key = match.group(1)
            value = match.group(2)

            # Escape any quotes inside the value
            escaped_value = value.replace('"', '\\"')
            return f'"{key}": "{escaped_value}"'

        # Match key-value pairs, being greedy to capture the whole value
        # This pattern handles: "key": "anything until end quote before }, or ]"
        pattern = r'"([^"]+)":\s*"(.*?)"\s*(?=[,}\]]|$)'
        result = re.sub(pattern, fix_value, s)

        return result

    # Attempt to sanitize the response text
    try:
        sanitized = sanitize_json_with_inner_quotes(response_text)
        return orjson.loads(sanitized)
    except orjson.JSONDecodeError:
        pass

class GeminiOcr:

    """Gemini OCR using genai.Client with retries, multi-image, and async support."""

    def __init__(self, api_key: str):
        self.api_key = api_key or os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("Gemini API key is required.")
        self.client = genai.Client(api_key=self.api_key)
        self.model = "gemini-2.5-flash"

    def _process_image_sync(self, image_bytes: bytes, prompt: str, metadata: dict={}):

        logger = structlog.get_logger()
        logger.debug(f"Init OCR image request {metadata}", pdf_page=metadata.get("pdf_page", -1))

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        data=image_bytes,
                        mime_type="image/jpeg"  # This will work for PNG too
                    ),
                    types.Part.from_text(text=prompt),
                ],
            ),
        ]
        config = types.GenerateContentConfig(response_mime_type="text/plain")

        self.max_retries = 1
        last_error = ""
        combined_usage_metadata = []
        for attempt in range(1, self.max_retries + 1):
            logger.debug(f"OCR image request {metadata}. Attempt {attempt}", pdf_page=metadata.get("pdf_page", -1))
            try:
                response: types.GenerateContentResponse = self.client.models.generate_content(
                    model=self.model,
                    contents=contents,
                    config=config
                )
                raw_text = response.text.strip()
                if not raw_text:
                    return {"success": False, "error": "Gemini returned empty text."}

                pdf_page = metadata.get("pdf_page", -1)
                roi_name = metadata.get("roi_name", "unknown")
                roi_type = metadata.get("roi_type", "unknown")

                try:
                    usage_metadata = {
                        "pdf_page": pdf_page,
                        "attempt": attempt,
                        "roi_name": roi_name,
                        "roi_type": roi_type,
                        "total_tokens": response.usage_metadata.total_token_count,
                        "prompt_tokens": response.usage_metadata.prompt_token_count,
                        "thoughts_token_count": response.usage_metadata.thoughts_token_count,
                    }
                    combined_usage_metadata.append(usage_metadata)

                    parsed = extract_json_from_ai_response(raw_text)
                    return {"success": True, "data": parsed, "metadata": metadata, "usage_metadata": combined_usage_metadata}

                except ValueError as e:
                    logger.error(f"Failed to parse OCR response", pdf_page=pdf_page, exception=e, roi_name=roi_name, roi_type=roi_type)
                    return {"success": False, "error": str(e), "metadata": metadata, "usage_metadata": combined_usage_metadata}

            except Exception as e:
                last_error = str(e)
                if attempt < self.max_retries:
                    wait = 2 ** attempt
                    asyncio.run(asyncio.sleep(wait))  # Blocking sleep for sync context
                else:
                    return {
                        "success": False,
                        "error": f"Failed after {self.max_retries} retries. Last error: {last_error}"
                    }

    def process_image_async(self, image_bytes: bytes, prompt: str, metadata: dict={}):
        """Wrapper for process_image_sync to be used in async context"""
        return asyncio.to_thread(self._process_image_sync, image_bytes, prompt, metadata)

class RoiExtraction():
    """
    A class to handle ROI extraction and processing.
    """

    def __init__(self, config: RoiExtractionConfig=None, cpu_pool: ProcessPoolExecutor=None, api_key: str=None):
        self.results = {}
        self.config = config
        self.duration = None
        self.initialized = False
        self.cpu_pool: Optional[ProcessPoolExecutor] = None
        self.network_semaphore = asyncio.Semaphore(config.max_network_concurrency)

        self.ocr_client = GeminiOcr(api_key=api_key)

        if not config:
            print("Using default extraction config")
            self.config = RoiExtractionConfig()

    def setup_logging(self):
        global log_queue
        log_queue = multiprocessing.Queue(-1)

        # Set up file and console handlers in the main process
        try:
            os.makedirs(os.path.dirname(self.config.extraction_log_file), exist_ok=True)
            file_handler = SafeFileHandler(self.config.extraction_log_file, mode="w")
        except Exception as e:
            file_handler = None
            print("Failed to create file handler: " + str(e))

        formatter = logging.Formatter('%(message)s') # structlog outputs JSON as strings
        file_handler.setFormatter(formatter)

        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s %(name)s %(levelname)s: %(message)s')
        console_handler.setFormatter(formatter)
        listener = logging.handlers.QueueListener(log_queue, console_handler, file_handler)
        # listener = logging.handlers.QueueListener(log_queue, file_handler)
        listener.start()

        configure_structlog()  # structlog for main process too
        return listener

    async def process_image_async(self, image: dict, prompt: str):
        return await self.ocr_client.process_images_async([image], prompt)

    async def process_page_rois_ocr(self,
                                    pdf_page: int,
                                    general_rois: dict,
                                    table_rois: dict,
                                    prompts: dict):

        logger = structlog.get_logger()

        if not general_rois:
            general_rois = {}
        if not table_rois:
            table_rois = {}

        logger.debug("Cropping OCR page rois", pdf_page=pdf_page)

        images = await crop_page_images(self.config, pdf_page, general_rois, table_rois)

        def get_prompt(image_config):
            if image_config["roi_name"].lower() == "isometric drawing area":
                return prompt_builder.ELEVATION_PROMPT
            elif image_config["roi_type"] == "general":
                return prompt_builder.GENERAL_PROMPT.format(roi_name=image_config["roi_name"])
            else:
                return prompts.get(image_config["roi_name"])

        tasks = [self.ocr_client.process_image_async(image_config["image_bytes"],
                                                     get_prompt(image_config),
                                                     {"pdf_page": pdf_page, "roi_type": image_config["roi_type"], "roi_name": image_config["roi_name"]}) for image_config in images]
        results = await asyncio.gather(*tasks)

        page_general_data = {"pdf_page": pdf_page}
        page_elevations = []
        page_tables_data = {}

        logger.debug("Processing OCR page rois", pdf_page=pdf_page)
        usage_metadata = []
        for res in results:
            usage_metadata.extend(res.get("usage_metadata", []))
            if not res["success"]:
                logger.error(f"Failed to process page rois", pdf_page=pdf_page)
                continue
            metadata = res["metadata"]
            if metadata["roi_name"].lower() == "isometric drawing area":
                if res.get("data"):
                    page_elevations = [{"pdf_page": pdf_page, "elevation": elevation} for elevation in res["data"].get("elevations", [])]
                else:
                    # No elevations found
                    page_elevations = []
            elif metadata["roi_type"] == "general":
                if "data" not in res:
                    page_general_data.update({metadata["roi_name"].lower(): "error: " + res.get("error", "Unknown error")})
                    continue
                page_general_data.update(res["data"])
            else:
                table_data = res["data"]
                if table_data:
                    table_df = pl.DataFrame(table_data)
                    table_df = table_df.with_columns([
                        pl.lit(pdf_page).alias("pdf_page"),
                        pl.lit("ocr").alias("extraction_method")
                    ])
                    page_tables_data[metadata["roi_name"]] = table_df

        data = {}
        if page_general_data and general_rois:
            data["general"] = page_general_data
        if page_elevations and general_rois:
            data["elevations"] = page_elevations
        if page_tables_data and table_rois:
            data["tables"] = page_tables_data
        result = {"pdf_page": pdf_page, "data": data, "usage_metadata": usage_metadata}

        return result

    async def run_extraction(self):
        # if not self.initialized:
        #     raise Exception("Extraction not initialized. Call init_extraction() first")
        listener = self.setup_logging()
        init_worker_logging(log_queue) # For use in single-threaded processing
        logger = structlog.get_logger()

        # Important. Log any less used columns that will be may appear in the log
        # as nread_ndjson infers schema from inital rows
        logger.exception("Init Logger. Initializing logger columns", exception_message="", outlier="", outlier_reason="", roi_name="", traceback="", pdf_page=-1)

        start_time = time.perf_counter()
        project_id = self.config.project_id
        pdf_path = self.config.filename
        raw_df_path = getSourceRawDataPath(project_id, pdf_path)
        try:
            raw_df: pl.DataFrame = pl.read_ipc(raw_df_path, memory_map=False)
            raw_df = raw_df.with_row_index(name="row_idx")
        except Exception as e:
            logger.error("Failed to read raw df. Check project source preprocessed state", exception=e)
            raise

        roi_layout = self.config.roi_layout
        if not self.config.roi_payload:
            if not roi_layout:
                print("Using internal ROI payload")
                roi_layout = getSourceExtractionOptionsPath(project_id, pdf_path)
            roi_payload = convert_roi_payload.convert_roi_payload(roi_layout, ignore_bom=True)
        else:
            roi_payload = self.config.roi_payload

        from src.atom.dbManager import DatabaseManager
        db = DatabaseManager()
        logger.debug("Getting project source given projectId={project_id} and filename={filename}", project_id=project_id, filename=pdf_path)
        try:
            pdf_storage = db.get_project_source_pdf_storage(project_id, pdf_path)
        except Exception as e:
            logger.error("Failed to get project source given projectId={project_id}: {e} and filename={filename}")
            raise

        if pdf_storage is None:
            raise Exception("Project source needs to be preprocessed first.")

        logger.debug("Fetching PDF storage")
        split_col = pl.col("docSize").str.split(",")
        pdf_storage = pdf_storage.with_columns([
            split_col.alias("size_split"),
            split_col.list.get(0).cast(pl.Float64).alias("width"),
            split_col.list.get(1).cast(pl.Float64).alias("height")
        ]).drop("size_split")

        self.max_page = pdf_storage.select(pl.col("pdf_page").max()).item()

        pages_to_extract = self.config.pages_to_extract
        if not pages_to_extract:
            # If no pages_to_extract specified, extract all pages from layout with ROIs
            pages_to_extract = list(roi_payload["pageToGroup"].keys())

        if self.config.test_page_limit:
            pages_to_extract = pages_to_extract[:self.config.test_page_limit]

        rois_to_extract = self.config.rois_to_extract

        all_general_data = []
        all_elevations_data = []
        all_table_data = defaultdict(list)

        logger.debug(f"Initializing extraction for project {project_id}, pdf {pdf_path}, page_count {len(pages_to_extract)}")

        def prepare_raw_df(raw_df: pl.DataFrame):
            logger.info(f"Preparing raw df")

            logger.debug(f"Filtering raw df by pages_to_extract")
            raw_df = raw_df.filter(pl.col("pdf_page").is_in(pages_to_extract))

            logger.debug(f"Excluding rows without `value`. TODO check if this doesn't exclude valid annot data")
            raw_df = raw_df.filter(pl.col("value").is_not_null())

            logger.debug(f"Adding empty `words` column")
            raw_df = raw_df.with_columns(pl.lit(None, dtype=pl.Utf8).alias("words"))

            words_struct_type = pl.List(
                pl.Struct([
                    pl.Field("word", pl.Utf8),
                    pl.Field("bbox", pl.List(pl.Float64))
                ])
            )

            # Fill words json for annotations
            fallback_words_json = (
                pl.struct(["value", "coordinates2"]).map_elements(
                    lambda x: orjson.dumps([{"word": x["value"], "bbox": x["coordinates2"]}]).decode("utf-8"),
                    return_dtype=pl.String
                )
            )

            logger.debug(f"Filling empty words with fallback json for annotations")

            raw_df = raw_df.with_columns(
                pl.when(
                    pl.col("words").is_null() |
                    (pl.col("words") == "") |
                    ~pl.col("words").str.contains(r"^\s*\[.*\{\s*\"word\"\s*:\s*")  # crude JSON list check
                )
                .then(fallback_words_json)
                .otherwise(pl.col("words"))
                .alias("words")
            )

            logger.debug(f"Decoding words json")

            # Prefer vectorized
            raw_df = raw_df.with_columns(
                pl.col("words").str.json_decode(dtype=words_struct_type).alias("words")
            )

            logger.debug(f"Unpacking coordinates2 into x0, y0, x1, y1 columns")

            raw_df = raw_df.with_columns([
                pl.col("coordinates2").list.get(0).alias("x0"),
                pl.col("coordinates2").list.get(1).alias("y0"),
                pl.col("coordinates2").list.get(2).alias("x1"),
                pl.col("coordinates2").list.get(3).alias("y1"),
            ])

            logger.debug(f"Casting font_size to float")

            # If font_size is not float, we exppect some mixed annotations, so
            # clean this before casting
            if raw_df.schema["font_size"] != pl.Float64:
                raw_df = raw_df.with_columns(
                    pl.when(
                        pl.col("font_size").is_null() |
                        (pl.col("font_size") == "") |
                        ~pl.col("font_size").cast(str).str.contains(r"^\s*-?\d+(\.\d+)?\s*$")
                    )
                    .then(pl.lit("0"))
                    .otherwise(pl.col("font_size"))
                    .alias("font_size")
                )

            raw_df = raw_df.with_columns(pl.col("annot_type").cast(pl.String).alias("annot_type"))
            raw_df = raw_df.with_columns(pl.col("font_size").cast(pl.Float64).alias("font_size"))
            return raw_df

        raw_df = raw_df.filter(pl.col("pdf_page").is_in(pages_to_extract))
        raw_df = prepare_raw_df(raw_df)

        roi_cache = {}
        extraction_tasks = []
        ocr_task_count = 0
        group_prompts = prompt_builder.build_prompts(roi_payload)
        for n, pdf_page in enumerate(pages_to_extract, start=1):
            logger.debug(f"Init page process  ({n}/{len(pages_to_extract)})", pdf_page=pdf_page)

            roi_group: int = roi_payload["pageToGroup"].get(pdf_page, None)
            if not roi_group:
                logger.debug(f"No roi group for page {pdf_page}", pdf_page=pdf_page)
                continue

            page_width = pdf_storage.filter(pl.col("pdf_page") == pdf_page).select(pl.col("width")).item()
            page_height = pdf_storage.filter(pl.col("pdf_page") == pdf_page).select(pl.col("height")).item()
            page_df = raw_df.filter(pl.col("pdf_page") == pdf_page)
            ocr_page = pdf_page in roi_payload["ocrPages"]
            if not ocr_page and page_df.is_empty():
                # Log this. Page has no raw data
                logger.debug(f"No raw data for non-ocr job", pdf_page=pdf_page, error="No raw data")
                continue

            rois = roi_payload["groupRois"][roi_group]
            logger.debug(f"Init page rois", pdf_page=pdf_page, roi_group=roi_group)
            table_rois, general_rois = get_page_rois(self.config, roi_cache, rois, roi_group, page_width, page_height, rois_to_extract)

            if ocr_page:
                if self.config.enable_ocr:
                    extraction_tasks.append((self.process_page_rois_ocr, {"pdf_page": pdf_page,
                                                                          "general_rois": general_rois if self.config.extract_general else None,
                                                                          "table_rois": table_rois if self.config.extract_tables else None,
                                                                          "prompts": group_prompts[roi_group]}))
                    ocr_task_count += 1
                else:
                    logger.debug(f"Skipping ocr page", pdf_page=pdf_page, error="OCR not enabled")
                    continue
            else:
                # Process general data
                if self.config.extract_general:
                    extraction_tasks.append((process_general_rois, {"config": self.config, "pdf_page": pdf_page, "page_df": page_df, "general_rois": general_rois}))

                # Process tables
                if self.config.extract_tables:
                    extraction_tasks.append((process_table_rois_wrapper, {"config": self.config, "pdf_page": pdf_page, "page_df": page_df, "table_rois": table_rois}))

        max_workers = self.config.max_workers
        available_workers = os.cpu_count() // 2 or 1
        max_workers = min(max_workers, available_workers)
        max_workers = min(max_workers, len(extraction_tasks))
        if ocr_task_count == 0 and len(extraction_tasks) < self.config.task_count_concurrency_threshold:
            max_workers = 1

        # Execute tasks
        if not self.config.enable_concurrency or max_workers == 1:
            logger.debug(f"Ignoring CPU pool - concurrency config disabled or max workers is 1")
            self.cpu_pool = None
        elif not self.cpu_pool:
            logger.debug(f"Initializing CPU pool with {self.config.max_workers} workers [DEV]")
            self.cpu_pool = ProcessPoolExecutor(max_workers=max_workers, initializer=init_worker_logging, initargs=(log_queue,))

        logger.debug(f"Concurrency - max workers: {self.cpu_pool._max_workers if self.cpu_pool else 0}")

        results = []

        # Start both OCR and CPU tasks concurrently for true parallelism
        all_tasks = []

        # Handle OCR tasks with async concurrency (I/O bound)
        if any(extract_func == self.process_page_rois_ocr for extract_func, _ in extraction_tasks):
            logger.debug(f"Processing {len([task for task in extraction_tasks if task[0] == self.process_page_rois_ocr])} OCR tasks with async concurrency")
            ocr_coroutines = [extract_func(**kwargs) for extract_func, kwargs in extraction_tasks if extract_func == self.process_page_rois_ocr]
            # Create a single task that gathers all OCR results
            ocr_task = asyncio.gather(*ocr_coroutines)
            all_tasks.append(ocr_task)

        # Handle CPU tasks with ProcessPoolExecutor or synchronously
        if any(extract_func != self.process_page_rois_ocr for extract_func, _ in extraction_tasks):
            if self.config.enable_concurrency and self.cpu_pool:
                logger.debug(f"Processing {len([task for task in extraction_tasks if task[0] != self.process_page_rois_ocr])} CPU tasks with multiprocessing")
                # Use ProcessPoolExecutor for true parallelism with batching
                loop = asyncio.get_event_loop()

                # Group tasks by page for better batching
                page_tasks = {}
                for extract_func, kwargs in extraction_tasks:
                    if extract_func == self.process_page_rois_ocr:
                        continue
                    pdf_page = kwargs['pdf_page']
                    if pdf_page not in page_tasks:
                        page_tasks[pdf_page] = []

                    # Functions are now synchronous, use directly
                    sync_func = extract_func

                    page_tasks[pdf_page].append((sync_func, kwargs))

                # Execute all tasks for each page in parallel
                cpu_tasks = []
                for pdf_page, page_task_list in page_tasks.items():
                    # Multiple tasks for this page - batch them
                    task = loop.run_in_executor(self.cpu_pool, _batch_process_page, page_task_list)
                    cpu_tasks.append(task)

                # Add CPU tasks to the overall task list
                all_tasks.extend(cpu_tasks)
            else:
                # Fallback to synchronous execution (no concurrency)
                logger.debug(f"Processing {len([task for task in extraction_tasks if task[0] != self.process_page_rois_ocr])} CPU tasks synchronously")
                for extract_func, kwargs in extraction_tasks:
                    if extract_func == self.process_page_rois_ocr:
                        continue
                    try:
                        result = extract_func(**kwargs)
                        results.append(result)
                    except Exception as e:
                        logger.error("Failed to process task", exception=e)
                        pdf_page = kwargs['pdf_page'] if 'pdf_page' in kwargs else "unknown"
                        results.append({"pdf_page": pdf_page, "data": {}})

        # Wait for all tasks (OCR + CPU) to complete concurrently
        if all_tasks:
            logger.debug(f"Waiting for {len(all_tasks)} concurrent task groups to complete")
            all_results = await asyncio.gather(*all_tasks)

            # Flatten and combine results
            for task_result in all_results:
                if not isinstance(task_result, list):
                    task_result = [task_result]

                # CPU batch results or OCR results
                for batch_result in task_result:
                    if isinstance(batch_result, list):
                        results.extend(batch_result)
                    else:
                        results.append(batch_result)

        token_usage_metadata = []
        for res in results:
            pdf_page = res["pdf_page"]
            data = res["data"]
            for key, value in data.items():
                if key == "general":
                    all_general_data.append(value)
                elif key == "elevations":
                    all_elevations_data.extend(value)
                elif key == "tables":
                    for table_type, table_df in value.items():
                        all_table_data[table_type].append(table_df)

            token_usage_metadata.extend(res.get("usage_metadata", []))

        # Combine and post-process results
        pdf_id_map: pl.DataFrame = None
        if self.config.enable_pdf_id_mapping:
            try:
                logger.debug("Fetching PDF page to id mapping")
                pdf_id_map = pdf_storage.with_columns([
                    pl.col("id").alias("pdf_id"),
                ]).select([
                    pl.col("pdf_page"),
                    pl.col("pdf_id")
                ])
            except Exception as e:
                logger.error(f"Failed to fetch PDF page to id mapping: {e}")

        if all_general_data:
            general_df = pl.DataFrame(all_general_data)

            if self.config.extract_elevations and all_elevations_data:
                logger.info("Processing all elevations", pdf_page=-1)
                elevations_df = pl.DataFrame(all_elevations_data)
                elevations_df = elevations_df.with_columns(pl.col("elevation").str.strip_chars().alias("elevation"))
                unique_elevations = pl.DataFrame(elevations_df["elevation"].unique())

                unique_elevations = unique_elevations.with_columns(
                    pl.col("elevation").map_elements(
                        lambda x: parse_elevation(x),
                        return_dtype=pl.Float64
                    ).alias("elevation_value")
                )
                elevations_joined = elevations_df.join(unique_elevations, on="elevation", how="left")

                # Aggregate elevations and calculate metrics
                logger.debug("Aggregating elevations and calculating metrics", pdf_page=-1)
                elevations_calculated = elevations_joined.filter(pl.col("elevation_value").is_not_null()).group_by("pdf_page").agg([
                    pl.col("elevation").implode().map_elements(lambda lst: ";".join(lst)).alias("elevation"),
                    pl.col("elevation_value").min().round(2).alias("min_elevation"),
                    pl.col("elevation_value").max().round(2).alias("max_elevation"),
                    pl.col("elevation_value").mean().round(2).alias("avg_elevation"),
                ])

                # Merge calculated into general
                general_df = general_df.join(elevations_calculated, on="pdf_page", how="left")

                # Sort data
                general_df = sort_by_pdf_page(general_df)

                elevations_joined = elevations_joined.with_columns(
                    pl.when(pl.col("elevation_value").is_not_null() & (pl.col("elevation_value") != float('nan')))
                    .then(pl.lit(True))
                    .otherwise(pl.lit(False))
                    .alias("valid")
                )
                elevations_joined = elevations_joined.with_columns(pl.col("elevation_value").round(2).alias("elevation_value"))
                self.results["page_elevations"] = elevations_joined

            else:
                logger.warning("No elevations data", pdf_page=-1)

            if pdf_id_map is not None:
                general_df = general_df.join(pdf_id_map, on="pdf_page", how="left")
            self.results["general_data"] = general_df

        for table_type, table_data in all_table_data.items():
            combined_table_df = safe_table_concat(table_data)
            combined_table_df = sort_by_pdf_page(combined_table_df)
            if pdf_id_map is not None:
                combined_table_df = combined_table_df.join(pdf_id_map, on="pdf_page", how="left")
            self.results[f"{table_type.lower()}_data"] = combined_table_df
            # combined_table_df.write_excel(f"debug/extracted_{table_type}.xlsx")
            # logger.debug(f"Saved to: debug/extracted_{table_type}.xlsx")

        # Move to end
        if "page_elevations" in self.results:
            self.results["page_elevations"] = self.results.pop("page_elevations")

        usage_df = pl.DataFrame(token_usage_metadata)
        self.results["token_usage_metadata"] = usage_df

        # Optional summary df
        try:
            end_time = time.perf_counter()
            self.duration = end_time - start_time
            self.duration = round(self.duration, 2)
            summary_stats = self.create_summary_stats()
            self.results["summary"] = pl.DataFrame(summary_stats)
        except Exception as e:
            logger.error("Failed to create summary stats: " + str(e))

        # Optional save configuration
        config_dict = self.config.asdict()
        config_df = pl.DataFrame({
            "key": list(config_dict.keys()),
            "value": [str(v) for v in list(config_dict.values())]
        })
        self.results["config"] = config_df

        # Retreive extraction log
        try:
            log_df = pl.read_ndjson(self.config.extraction_log_file, infer_schema_length=400)
            log_df = log_df.with_columns(
                pl.when(pl.col("pdf_page").is_null())
                .then(pl.lit(-1))
                .otherwise(pl.col("pdf_page"))
                .alias("pdf_page")
            )
            log_df = log_df.with_columns(pl.lit(self.config.project_id).alias("project_id"))
            log_df = log_df.with_columns(pl.lit(self.config.filename).alias("sys_path"))
            outlier_df = log_df.filter((pl.col("outlier_reason").is_not_null()) & (pl.col("pdf_page") >= 1))
            outlier_df = sort_by_pdf_page(outlier_df)
            if pdf_id_map is not None:
                outlier_df = outlier_df.join(pdf_id_map, on="pdf_page", how="left")
            self.results["outlier_data"] = outlier_df
            self.results["extraction_log"] = log_df
        except Exception as e:
            print("Failed to save extraction log: " + str(e))

        end_time = time.perf_counter()
        self.duration = end_time - start_time
        self.duration = round(self.duration, 2)
        print(f"Execution time: {self.duration} seconds")
        logger.debug(f"Execution time: {self.duration} seconds")

        listener.stop()

    def run_extraction_sync(self):
        """
        Synchronous wrapper for run_extraction() that can be used in threads.
        Creates a new event loop to run the async method.
        """
        import asyncio
        try:
            # Try to get existing loop (will fail if in thread)
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we're probably in a thread - create new loop
                return asyncio.run(self.run_extraction())
        except RuntimeError:
            # No event loop exists - create new one
            pass

        # Run in new event loop
        return asyncio.run(self.run_extraction())

    def get_results(self, as_pandas: bool = False):
        if not as_pandas:
            return self.results
        return {k: v.to_pandas() for k, v in self.results.items()}

    def create_summary_stats(self, format="dict"):
        """Generate stats post-extraction"""

        expected_pages = list(range(1, self.max_page + 1))

        summary_stats = [
            {"table_type": "overview", "property": "process_time (before finalizing summary and log/outlier results)", "value": self.duration}
        ]
        all_missing_pages = []
        for table_type, table_df in self.results.items():
            table_type = table_type.replace("_data", "")

            if table_type == "token_usage_metadata":
                if table_df.is_empty():
                    summary_stats.extend([
                        {"table_type": table_type, "property": "total_tokens", "value": 0},
                    ])
                    continue
                summary_stats.extend([
                    {"table_type": table_type, "property": "total_tokens", "value": table_df.select("total_tokens").sum().item()},
                    {"table_type": table_type, "property": "prompt_tokens", "value": table_df.select("prompt_tokens").sum().item()},
                    {"table_type": table_type, "property": "thoughts_token_count", "value": table_df.select("thoughts_token_count").sum().item()},
                ])
                continue

            if "pdf_page" in table_df:
                unique_pages = table_df.select("pdf_page").unique().sort("pdf_page")["pdf_page"].to_list()
                missing_pages = list(set(expected_pages) - set(unique_pages))
            else:
                unique_pages = []
                missing_pages = []

            summary_stats.extend([
                {"table_type": table_type, "property": f"{table_type}_total_pages", "value": table_df.select("pdf_page").n_unique()},
                {"table_type": table_type, "property": f"{table_type}_total_rows", "value": len(table_df)},
                {"table_type": table_type, "property": f"{table_type}_unique_pages", "value": compress_page_ranges(unique_pages) if unique_pages else ""},
                {"table_type": table_type, "property": f"{table_type}_missing_pages", "value": compress_page_ranges(missing_pages) if missing_pages else ""},
                {"table_type": table_type, "property": f"{table_type}_pages_with_outliers", "value": "todo"},
            ])

            if missing_pages:
                all_missing_pages.extend([{"table_type": table_type, "pdf_page": page} for page in missing_pages])

            if table_type == "bom":
                summary_stats.extend([
                    {"table_type": table_type, "property": "total_materials", "value": len(self.results["bom_data"])},
                    {"table_type": table_type, "property": "unique_materials", "value": self.results["bom_data"].select("material_description").n_unique()},
                ])

        if all_missing_pages:
            self.results["missing_pages"] = pl.DataFrame(all_missing_pages)

        return summary_stats

    def save_results(self, save_file:str="debug/extraction_results/all_results.xlsx", styling=True):
        """Save results to Excel"""
        os.makedirs(os.path.dirname(save_file), exist_ok=True)
        with pd.ExcelWriter(save_file, engine="xlsxwriter") as writer:

            for key, df in self.results.items():
                # Reordering columns
                if "pdf_page" in df:
                    df = df.select(["pdf_page"] + [col for col in df.columns if col != "pdf_page"])

                df = df.to_pandas()
                sheet_name = key[:31]  # Excel sheet names limited to 31 chars
                if not df.empty:
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # Create an empty sheet with a message
                    pd.DataFrame({"Message": ["No data found"]}).to_excel(
                        writer, sheet_name=sheet_name, index=False
                    )

                if not styling:
                    continue

                try:
                    from openpyxl.utils import get_column_letter
                    worksheet = writer.sheets[sheet_name]
                    worksheet.freeze_panes(1, 1)
                    n_rows, n_cols = df.shape
                    col_letter_end = get_column_letter(n_cols)
                    worksheet.autofilter(f"A1:{col_letter_end}{n_rows + 1}")

                    # Auto resize column widths
                    for i, col in enumerate(df.columns):
                        max_length = max(
                            df[col].astype(str).map(len).max(),
                            len(str(col))
                        )
                        # Clamp width between 10 and 30
                        width = max(10, min(max_length + 2, 30))
                        worksheet.set_column(i, i, width)
                except Exception as e:
                    print("Failed to style sheet", sheet_name)
                    print(f"Failed to style sheet {sheet_name}: {e}")

            print(f"Saved all results to {save_file}")
