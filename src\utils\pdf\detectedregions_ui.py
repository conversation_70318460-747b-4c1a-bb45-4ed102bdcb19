import os
import sys
import numpy as np
import pandas as pd
import fitz  # PyMuPDF
import cv2
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
import tempfile
import winsdk.windows.media.ocr as ocr
import winsdk.windows.graphics.imaging as imaging
import asyncio
from io import BytesIO

from src.utils.pdf.page_to_opencv import page_to_opencv
from src.atom.vision.detect_text_regions.detect_text_regions_cv2 import detect_text_regions


class PageListModel(QAbstractTableModel):
    """Model for displaying PDF page list."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.df = pd.DataFrame(columns=['pdf_page'])
        self.column_names = {0: "Page"}

    def rowCount(self, parent=None):
        return len(self.df)

    def columnCount(self, parent=None):
        return len(self.column_names)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.column_names.get(section, str(section))
        return None

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.df)):
            return None

        row = index.row()
        col = index.column()

        if role == Qt.DisplayRole:
            value = self.df.iloc[row, col]
            return str(value)

        return None

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

    def setDataFrame(self, df):
        """Set the DataFrame for the model."""
        self.beginResetModel()
        self.df = df
        self.endResetModel()

    def getData(self):
        """Get the current DataFrame."""
        return self.df.copy()


class RegionOCRModel(QAbstractTableModel):
    """Model for displaying OCR results from detected regions."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.df = pd.DataFrame(columns=['region_id', 'text'])
        self.column_names = {0: "Region ID", 1: "Text"}

    def rowCount(self, parent=None):
        return len(self.df)

    def columnCount(self, parent=None):
        return len(self.column_names)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.column_names.get(section, str(section))
        return None

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.df)):
            return None

        row = index.row()
        col = index.column()

        if role == Qt.DisplayRole:
            value = self.df.iloc[row, col]
            return str(value)

        return None

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

    def setDataFrame(self, df):
        """Set the DataFrame for the model."""
        self.beginResetModel()
        self.df = df
        self.endResetModel()

    def getData(self):
        """Get the current DataFrame."""
        return self.df.copy()


class PDFViewer(QGraphicsView):
    """Custom QGraphicsView for displaying PDF pages with detected regions."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.pixmap_item = None
        self.regions = []  # Store detected regions
        self.zoomFactor = 1.0
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setDragMode(QGraphicsView.ScrollHandDrag)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.show_regions = False
        self.original_pixmap = None
        self.page_size = (0, 0)  # Store page size for scaling regions
        self.scale_factor = 1.0  # Scale factor between pixmap and original page
        self.detection_zoom = 1.0  # Store the zoom used for detection

    def wheelEvent(self, event):
        """Handle zoom with mouse wheel."""
        if event.modifiers() & Qt.ControlModifier:
            # Zoom factor
            zoom_in_factor = 1.25
            zoom_out_factor = 1 / zoom_in_factor

            # Set anchor under mouse
            self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)

            # Scale view
            if event.angleDelta().y() > 0:
                zoom_factor = zoom_in_factor
                self.zoomFactor *= zoom_factor
            else:
                zoom_factor = zoom_out_factor
                self.zoomFactor *= zoom_factor

            self.scale(zoom_factor, zoom_factor)
            event.accept()
        else:
            super().wheelEvent(event)

    def resetZoom(self):
        """Reset zoom to original size."""
        self.resetTransform()
        self.zoomFactor = 1.0

    def fitInView(self):
        """Fit the content in view."""
        if self.pixmap_item:
            super().fitInView(self.pixmap_item, Qt.KeepAspectRatio)
            # Update zoom factor
            rect = self.pixmap_item.boundingRect()
            view_rect = self.viewport().rect()
            x_ratio = view_rect.width() / rect.width()
            y_ratio = view_rect.height() / rect.height()
            self.zoomFactor = min(x_ratio, y_ratio)

    def displayPage(self, pixmap, page_size=None, scale_factor=1.0):
        """Display a PDF page as a pixmap."""
        self.scene.clear()
        self.regions = []
        self.original_pixmap = pixmap
        if page_size:
            self.page_size = page_size
        self.scale_factor = scale_factor

        if pixmap:
            self.pixmap_item = self.scene.addPixmap(pixmap)
            self.setSceneRect(self.pixmap_item.boundingRect())
            self.fitInView()
        else:
            self.pixmap_item = None

    def setShowRegions(self, show):
        """Set whether to show detected regions."""
        self.show_regions = show
        if self.original_pixmap:
            self.updateDisplay()

    def updateDisplay(self):
        """Update the display with or without regions."""
        if not self.original_pixmap:
            return

        if self.show_regions and self.regions:
            # Create a copy of the original pixmap to draw on
            pixmap = QPixmap(self.original_pixmap)
            painter = QPainter(pixmap)

            # Set up the pen for drawing regions
            pen = QPen(QColor(0, 255, 0))  # Green color
            pen.setWidth(3)
            painter.setPen(pen)

            # Draw each region
            for region in self.regions:
                x, y, w, h = region
                # Scale the region coordinates to match the pixmap, accounting for detection zoom
                x_scaled = (x / self.detection_zoom) * self.scale_factor
                y_scaled = (y / self.detection_zoom) * self.scale_factor
                w_scaled = (w / self.detection_zoom) * self.scale_factor
                h_scaled = (h / self.detection_zoom) * self.scale_factor
                painter.drawRect(x_scaled, y_scaled, w_scaled, h_scaled)

            painter.end()

            # Update the display
            if self.pixmap_item:
                self.scene.removeItem(self.pixmap_item)
            self.pixmap_item = self.scene.addPixmap(pixmap)
            self.setSceneRect(self.pixmap_item.boundingRect())
        else:
            # Just show the original pixmap
            if self.pixmap_item:
                self.scene.removeItem(self.pixmap_item)
            self.pixmap_item = self.scene.addPixmap(self.original_pixmap)
            self.setSceneRect(self.pixmap_item.boundingRect())

    def setRegions(self, regions, detection_zoom=1.0):
        """Set the detected regions and update display if needed."""
        self.regions = regions
        self.detection_zoom = detection_zoom
        if self.show_regions:
            self.updateDisplay()


class DetectedRegionsUI(QMainWindow):
    """UI for viewing PDF pages and detected text regions."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("PDF Text Regions Detector")
        self.setMinimumSize(1200, 800)

        # Create central widget and layout
        self.centralWidget = QWidget()
        self.setCentralWidget(self.centralWidget)
        self.mainLayout = QVBoxLayout(self.centralWidget)

        # Create status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready")

        # PDF document
        self.doc = None
        self.current_page_idx = -1
        self.current_regions = []
        self.current_cv_img = None

        # Create UI components
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        # PDF selection area
        self.pdfSelectionLayout = QHBoxLayout()
        self.lblPdfPath = QLabel("No PDF selected")
        self.lblPdfPath.setStyleSheet("font-weight: bold;")
        self.pbBrowse = QPushButton("Browse...")
        self.pbBrowse.clicked.connect(self.on_browse_pdf)

        self.pdfSelectionLayout.addWidget(QLabel("PDF:"))
        self.pdfSelectionLayout.addWidget(self.lblPdfPath, 1)
        self.pdfSelectionLayout.addWidget(self.pbBrowse)

        self.mainLayout.addLayout(self.pdfSelectionLayout)

        # Create main splitter for top and bottom sections
        self.mainSplitter = QSplitter(Qt.Vertical)

        # Top section with page list and PDF viewer
        self.topWidget = QWidget()
        self.topLayout = QHBoxLayout(self.topWidget)
        self.topLayout.setContentsMargins(0, 0, 0, 0)

        # Create splitter for page list and PDF viewer
        self.splitter = QSplitter(Qt.Horizontal)

        # Page list on left
        self.pageListWidget = QWidget()
        self.pageListLayout = QVBoxLayout(self.pageListWidget)
        self.lblPageList = QLabel("Pages")
        self.lblPageList.setStyleSheet("font-weight: bold;")

        self.pageListTable = QTableView()
        self.pageListTable.setSelectionBehavior(QTableView.SelectRows)
        self.pageListTable.setSelectionMode(QTableView.SingleSelection)
        self.pageListModel = PageListModel()
        self.pageListTable.setModel(self.pageListModel)

        # Connect selection change to update PDF viewer
        self.pageListTable.selectionModel().selectionChanged.connect(self.on_page_selection_changed)

        self.pageListLayout.addWidget(self.lblPageList)
        self.pageListLayout.addWidget(self.pageListTable)

        # PDF viewer on right
        self.pdfViewerWidget = QWidget()
        self.pdfViewerLayout = QVBoxLayout(self.pdfViewerWidget)

        # PDF viewer header with controls
        self.pdfViewerHeader = QHBoxLayout()
        self.lblPdfPage = QLabel("No page selected")
        self.lblPdfPage.setStyleSheet("font-weight: bold;")

        # Add region detection controls
        self.regionControlsGroup = QGroupBox("Region Detection")
        self.regionControlsLayout = QFormLayout(self.regionControlsGroup)

        # Zoom control
        self.zoomSpin = QSpinBox()
        self.zoomSpin.setRange(1, 10)
        self.zoomSpin.setValue(2)
        self.zoomSpin.setSingleStep(1)
        self.zoomSpin.setSuffix("x")
        self.zoomSpin.setToolTip("Zoom level for region detection (higher values provide more detail but take longer)")

        # Minimum area control
        self.minAreaSpin = QSpinBox()
        self.minAreaSpin.setRange(10, 1000)
        self.minAreaSpin.setValue(100)
        self.minAreaSpin.setSingleStep(10)
        self.minAreaSpin.setToolTip("Minimum area for detected regions (smaller values detect smaller text)")

        # Horizontal dilation iterations
        self.horizontalDilationSpin = QSpinBox()
        self.horizontalDilationSpin.setRange(0, 100)
        self.horizontalDilationSpin.setValue(4)  # Default value from detect_text_regions
        self.horizontalDilationSpin.setSingleStep(1)
        self.horizontalDilationSpin.setToolTip("Horizontal dilation iterations (higher values merge words into larger regions)")

        # Vertical dilation iterations
        self.verticalDilationSpin = QSpinBox()
        self.verticalDilationSpin.setRange(0, 100)
        self.verticalDilationSpin.setValue(0)  # Default is no vertical dilation
        self.verticalDilationSpin.setSingleStep(1)
        self.verticalDilationSpin.setToolTip("Vertical dilation iterations (higher values merge lines into larger regions)")

        # Density threshold
        self.densityThresholdSpin = QDoubleSpinBox()
        self.densityThresholdSpin.setRange(0.1, 0.9)
        self.densityThresholdSpin.setValue(0.2)  # Default value from detect_text_regions
        self.densityThresholdSpin.setSingleStep(0.05)
        self.densityThresholdSpin.setDecimals(2)
        self.densityThresholdSpin.setToolTip("Density threshold for region filtering (higher values require more text content)")

        # Remove grid checkbox
        self.removeGridCheckbox = QCheckBox("Remove Grid")
        self.removeGridCheckbox.setChecked(True)
        self.removeGridCheckbox.setToolTip("Remove horizontal and vertical lines before detecting text")

        # Detect regions checkbox
        self.detectRegionsCheckbox = QCheckBox("Show Detected Regions")
        self.detectRegionsCheckbox.setChecked(False)
        self.detectRegionsCheckbox.toggled.connect(self.on_detect_regions_toggled)

        # Detect button
        self.detectButton = QPushButton("Detect Regions")
        self.detectButton.clicked.connect(self.on_detect_button_clicked)

        # OCR button
        self.ocrButton = QPushButton("OCR Regions")
        self.ocrButton.clicked.connect(self.on_ocr_button_clicked)
        self.ocrButton.setEnabled(False)

        # OCR whole page button
        self.ocrPageButton = QPushButton("OCR Whole Page")
        self.ocrPageButton.clicked.connect(self.on_ocr_page_button_clicked)
        self.ocrPageButton.setEnabled(False)

        # Add controls to layout
        self.regionControlsLayout.addRow("Zoom:", self.zoomSpin)
        self.regionControlsLayout.addRow("Min Area:", self.minAreaSpin)
        self.regionControlsLayout.addRow("H-Dilation:", self.horizontalDilationSpin)
        self.regionControlsLayout.addRow("V-Dilation:", self.verticalDilationSpin)
        self.regionControlsLayout.addRow("Density:", self.densityThresholdSpin)
        self.regionControlsLayout.addRow("", self.removeGridCheckbox)
        self.regionControlsLayout.addRow("", self.detectRegionsCheckbox)
        self.regionControlsLayout.addRow("", self.detectButton)
        self.regionControlsLayout.addRow("", self.ocrButton)
        self.regionControlsLayout.addRow("", self.ocrPageButton)

        # Add viewer controls
        self.pbZoomIn = QPushButton("Zoom In")
        self.pbZoomIn.clicked.connect(self.onZoomIn)

        self.pbZoomOut = QPushButton("Zoom Out")
        self.pbZoomOut.clicked.connect(self.onZoomOut)

        self.pbZoomReset = QPushButton("Reset Zoom")
        self.pbZoomReset.clicked.connect(self.onZoomReset)

        self.pbFitView = QPushButton("Fit to View")
        self.pbFitView.clicked.connect(self.onFitView)

        # Add controls to header
        self.pdfViewerHeader.addWidget(self.lblPdfPage, 1)
        self.pdfViewerHeader.addWidget(self.regionControlsGroup)
        self.pdfViewerHeader.addWidget(self.pbZoomIn)
        self.pdfViewerHeader.addWidget(self.pbZoomOut)
        self.pdfViewerHeader.addWidget(self.pbZoomReset)
        self.pdfViewerHeader.addWidget(self.pbFitView)

        # PDF viewer
        self.pdfViewer = PDFViewer()

        self.pdfViewerLayout.addLayout(self.pdfViewerHeader)
        self.pdfViewerLayout.addWidget(self.pdfViewer, 1)

        # Add widgets to splitter
        self.splitter.addWidget(self.pageListWidget)
        self.splitter.addWidget(self.pdfViewerWidget)

        # Set initial splitter sizes (30% for page list, 70% for PDF viewer)
        self.splitter.setSizes([300, 700])

        # Add splitter to top layout
        self.topLayout.addWidget(self.splitter)

        # Bottom section with OCR results
        self.bottomWidget = QWidget()
        self.bottomLayout = QVBoxLayout(self.bottomWidget)

        # OCR results table
        self.ocrResultsLabel = QLabel("OCR Results")
        self.ocrResultsLabel.setStyleSheet("font-weight: bold;")

        self.ocrResultsTable = QTableView()
        self.ocrResultsTable.setSelectionBehavior(QTableView.SelectRows)
        self.ocrResultsTable.setSelectionMode(QTableView.SingleSelection)
        self.ocrResultsModel = RegionOCRModel()
        self.ocrResultsTable.setModel(self.ocrResultsModel)

        self.bottomLayout.addWidget(self.ocrResultsLabel)
        self.bottomLayout.addWidget(self.ocrResultsTable)

        # Add widgets to main splitter
        self.mainSplitter.addWidget(self.topWidget)
        self.mainSplitter.addWidget(self.bottomWidget)

        # Set initial main splitter sizes (70% for top, 30% for bottom)
        self.mainSplitter.setSizes([700, 300])

        # Add main splitter to main layout
        self.mainLayout.addWidget(self.mainSplitter, 1)

        # Disable viewer controls initially
        self.enable_viewer_controls(False)

    def enable_viewer_controls(self, enabled):
        """Enable or disable viewer controls."""
        self.pbZoomIn.setEnabled(enabled)
        self.pbZoomOut.setEnabled(enabled)
        self.pbZoomReset.setEnabled(enabled)
        self.pbFitView.setEnabled(enabled)
        self.detectRegionsCheckbox.setEnabled(enabled)
        self.detectButton.setEnabled(enabled)
        self.zoomSpin.setEnabled(enabled)
        self.minAreaSpin.setEnabled(enabled)
        self.horizontalDilationSpin.setEnabled(enabled)
        self.verticalDilationSpin.setEnabled(enabled)
        self.densityThresholdSpin.setEnabled(enabled)
        self.removeGridCheckbox.setEnabled(enabled)
        # OCR button is enabled only when regions are detected
        self.ocrPageButton.setEnabled(enabled)

    def on_browse_pdf(self):
        """Open file dialog to select a PDF file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select PDF File", "", "PDF Files (*.pdf)"
        )

        if file_path:
            self.load_pdf(file_path)

    def load_pdf(self, file_path):
        """Load a PDF file and populate the page list."""
        try:
            # Close previous document if open
            if self.doc:
                self.doc.close()
                self.doc = None

            # Open the PDF
            self.doc = fitz.open(file_path)

            # Update PDF path label
            self.lblPdfPath.setText(os.path.basename(file_path))

            # Create page list data
            data = []
            for page_num in range(1, len(self.doc) + 1):  # 1-based page numbering
                data.append({'pdf_page': page_num})

            # Update page list model
            df = pd.DataFrame(data)
            self.pageListModel.setDataFrame(df)

            # Resize columns to content
            self.pageListTable.resizeColumnsToContents()

            # Select first page if available
            if len(self.doc) > 0:
                self.pageListTable.selectRow(0)

            # Update status
            self.statusBar.showMessage(f"Loaded PDF with {len(self.doc)} pages")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load PDF: {str(e)}")
            self.statusBar.showMessage("Error loading PDF")

    def on_page_selection_changed(self, selected, deselected):
        """Handle page selection change in the page list."""
        indexes = selected.indexes()
        if indexes:
            row = indexes[0].row()
            page_num = self.pageListModel.df.iloc[row]['pdf_page']
            self.display_page(int(page_num))

    def display_page(self, page_num):
        """Display the selected PDF page."""
        if not self.doc or page_num < 1 or page_num > len(self.doc):
            return

        # Convert from 1-based to 0-based page numbering
        page_idx = page_num - 1

        try:
            # Get the page
            page = self.doc[page_idx]

            # Get the zoom factor for display (not for detection)
            zoom = 2.0  # Default display zoom

            # Render page to pixmap
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)

            # Get page size for scaling regions
            page_size = (page.rect.width, page.rect.height)

            # Calculate scale factor between pixmap and original page
            scale_factor = pix.width / page.rect.width

            # Convert to QPixmap
            img = QImage(pix.samples, pix.width, pix.height, pix.stride, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(img)

            # Display in viewer
            self.pdfViewer.displayPage(pixmap, page_size, scale_factor)

            # Update page label
            self.lblPdfPage.setText(f"Page {page_num} of {len(self.doc)}")

            # Enable viewer controls
            self.enable_viewer_controls(True)

            # Store current page index
            self.current_page_idx = page_idx

            # Update status
            self.statusBar.showMessage(f"Displaying page {page_num}")

            # If detect regions is checked, detect and show regions
            if self.detectRegionsCheckbox.isChecked():
                self.detect_and_show_regions()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to display page: {str(e)}")
            self.statusBar.showMessage("Error displaying page")

    def on_detect_regions_toggled(self, checked):
        """Handle detect regions toggle."""
        self.pdfViewer.setShowRegions(checked)

        # If toggled on and we have a page displayed, detect regions
        if checked and self.current_page_idx >= 0:
            self.detect_and_show_regions()

    def on_detect_button_clicked(self):
        """Handle detect button click."""
        if self.current_page_idx >= 0:
            self.detect_and_show_regions()
            # Ensure regions are shown
            self.detectRegionsCheckbox.setChecked(True)

    def detect_and_show_regions(self):
        """Detect text regions in the current page and display them."""
        if self.current_page_idx < 0 or not self.doc:
            return

        try:
            # Show status
            self.statusBar.showMessage("Detecting text regions...")
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Get detection parameters
            zoom = self.zoomSpin.value()
            min_area = self.minAreaSpin.value()
            remove_grid = self.removeGridCheckbox.isChecked()
            h_dilation = self.horizontalDilationSpin.value()
            v_dilation = self.verticalDilationSpin.value()
            density_threshold = self.densityThresholdSpin.value()

            # Get the page
            page = self.doc[self.current_page_idx]

            # Convert page to OpenCV image using the page_to_opencv function
            cv_img = page_to_opencv(page, zoom=zoom)

            # Store the current CV image for OCR
            self.current_cv_img = cv_img

            # Apply custom dilations if needed
            if h_dilation != 4 or v_dilation > 0 or density_threshold != 0.2:
                # Convert to grayscale
                gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

                # Apply binary threshold
                thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

                # Perform bitwise operation
                bitwise = cv2.bitwise_not(thresh)

                # Remove grid lines if requested
                if remove_grid:
                    from src.atom.vision.detect_text_regions.detect_text_regions_cv2 import remove_grid_lines
                    img2 = remove_grid_lines(bitwise)
                else:
                    img2 = bitwise

                # Apply horizontal dilation
                if h_dilation > 0:
                    dilated = cv2.dilate(img2, np.ones((1, 3), np.uint8), iterations=h_dilation)
                else:
                    dilated = img2

                # Apply vertical dilation if requested
                if v_dilation > 0:
                    dilated = cv2.dilate(dilated, np.ones((3, 1), np.uint8), iterations=v_dilation)

                # Perform connected component analysis
                num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(dilated, connectivity=8)

                # Extract regions with custom density threshold
                img_height, img_width = cv_img.shape[:2]
                max_area = img_width * img_height * 0.1
                text_regions = []

                for label in range(1, num_labels):  # Skip background (label 0)
                    x = stats[label, cv2.CC_STAT_LEFT]
                    y = stats[label, cv2.CC_STAT_TOP]
                    w = stats[label, cv2.CC_STAT_WIDTH]
                    h = stats[label, cv2.CC_STAT_HEIGHT]
                    area = stats[label, cv2.CC_STAT_AREA]

                    if area > min_area and area < max_area and w > 2 and h > 2:
                        # Calculate density
                        mask = (labels[y:y+h, x:x+w] == label)
                        density = np.sum(mask) / (w * h)

                        if density > density_threshold:
                            text_regions.append((x, y, w, h))

                # Sort regions by position
                text_regions.sort(key=lambda r: (r[1] // 15, r[0]))
                regions = text_regions
            else:
                # Use the standard detect_text_regions function
                regions = detect_text_regions(cv_img, min_area=min_area, remove_grid=remove_grid)

            # Store current regions
            self.current_regions = regions

            # Set regions in the viewer
            self.pdfViewer.setRegions(regions, zoom)

            # Enable OCR button if regions were detected
            self.ocrButton.setEnabled(len(regions) > 0)
            self.ocrPageButton.setEnabled(True)

            # Update status
            self.statusBar.showMessage(f"Detected {len(regions)} text regions")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to detect text regions: {str(e)}")
            self.statusBar.showMessage("Error detecting text regions")
        finally:
            QApplication.restoreOverrideCursor()

    def on_ocr_button_clicked(self):
        """Handle OCR button click."""
        if not self.current_regions or self.current_cv_img is None:
            QMessageBox.warning(self, "Warning", "No regions detected to OCR.")
            return

        try:
            # Show status
            self.statusBar.showMessage("Performing OCR on regions...")
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Create a list to store OCR results
            ocr_results = []

            # Get the OCR engine with English language
            try:
                # Import Windows language module
                from winsdk.windows.globalization import Language

                # Create OCR engine with English language
                engine = ocr.OcrEngine.try_create_from_language(Language("en-US"))

                if engine is None:
                    # Fallback to default language if English is not available
                    available_languages = ocr.OcrEngine.available_recognizer_languages
                    if available_languages and len(available_languages) > 0:
                        engine = ocr.OcrEngine.try_create_from_language(available_languages[0])
                    else:
                        raise Exception("No OCR languages available on this system")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to create OCR engine: {str(e)}")
                self.statusBar.showMessage("Error creating OCR engine")
                QApplication.restoreOverrideCursor()
                return

            # Process each region
            for i, (x, y, w, h) in enumerate(self.current_regions):
                # Crop the region from the image
                region_img = self.current_cv_img[y:y+h, x:x+w]

                # Skip if region is empty
                if region_img.size == 0:
                    ocr_results.append({'region_id': i+1, 'text': ''})
                    continue

                # Convert to BGR (Windows SDK expects BGR format)
                if len(region_img.shape) == 2:  # Grayscale
                    region_img = cv2.cvtColor(region_img, cv2.COLOR_GRAY2BGR)

                # Save the region to a temporary file
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    temp_filename = temp_file.name
                    cv2.imwrite(temp_filename, region_img)

                try:
                    # Use a simpler approach with Windows SDK
                    async def recognize_text():
                        try:
                            # Import needed modules
                            from winsdk.windows.storage import StorageFile
                            from winsdk.windows.graphics.imaging import BitmapPixelFormat, BitmapAlphaMode

                            # Get the file as a storage file
                            storage_file = await StorageFile.get_file_from_path_async(os.path.abspath(temp_filename))

                            # Open the file
                            stream = await storage_file.open_read_async()

                            try:
                                # Create decoder from stream
                                decoder = await imaging.BitmapDecoder.create_async(
                                    imaging.BitmapDecoder.png_decoder_id,
                                    stream
                                )

                                # Get frame
                                bitmap_frame = await decoder.get_frame_async(0)

                                # Create software bitmap directly from frame
                                software_bitmap = await bitmap_frame.get_software_bitmap_async(
                                    BitmapPixelFormat.BGRA8,
                                    BitmapAlphaMode.PREMULTIPLIED
                                )

                                # Recognize text
                                result = await engine.recognize_async(software_bitmap)

                                # Get text
                                text = result.text if result.text else ""

                                return text
                            finally:
                                # Always close the stream
                                stream.close()
                        except Exception as e:
                            print(f"OCR error: {str(e)}")
                            return ""

                    # Run the async function
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    text = loop.run_until_complete(recognize_text())
                    loop.close()

                    # Add to results
                    ocr_results.append({'region_id': i+1, 'text': text.strip()})

                finally:
                    # Clean up the temporary file
                    try:
                        os.unlink(temp_filename)
                    except:
                        pass

            # Update the OCR results table
            df = pd.DataFrame(ocr_results)
            self.ocrResultsModel.setDataFrame(df)

            # Resize columns to content
            self.ocrResultsTable.resizeColumnsToContents()

            # Update status
            self.statusBar.showMessage(f"OCR completed for {len(ocr_results)} regions")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to perform OCR: {str(e)}")
            self.statusBar.showMessage("Error performing OCR")
        finally:
            QApplication.restoreOverrideCursor()

    def on_ocr_page_button_clicked(self):
        """Handle OCR whole page button click."""
        if self.current_cv_img is None:
            QMessageBox.warning(self, "Warning", "No page loaded to OCR.")
            return

        try:
            # Show status
            self.statusBar.showMessage("Performing OCR on whole page...")
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Create a list to store OCR results
            ocr_results = []

            # Get the OCR engine with English language
            try:
                # Import Windows language module
                from winsdk.windows.globalization import Language

                # Create OCR engine with English language
                engine = ocr.OcrEngine.try_create_from_language(Language("en-US"))

                if engine is None:
                    # Fallback to default language if English is not available
                    available_languages = ocr.OcrEngine.available_recognizer_languages
                    if available_languages and len(available_languages) > 0:
                        engine = ocr.OcrEngine.try_create_from_language(available_languages[0])
                    else:
                        raise Exception("No OCR languages available on this system")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to create OCR engine: {str(e)}")
                self.statusBar.showMessage("Error creating OCR engine")
                QApplication.restoreOverrideCursor()
                return

            # Save the whole page to a temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_filename = temp_file.name
                cv2.imwrite(temp_filename, self.current_cv_img)

            try:
                # Use Windows SDK for OCR
                async def recognize_text():
                    try:
                        # Import needed modules
                        from winsdk.windows.storage import StorageFile
                        from winsdk.windows.graphics.imaging import BitmapPixelFormat, BitmapAlphaMode

                        # Get the file as a storage file
                        storage_file = await StorageFile.get_file_from_path_async(os.path.abspath(temp_filename))

                        # Open the file
                        stream = await storage_file.open_read_async()

                        try:
                            # Create decoder from stream
                            decoder = await imaging.BitmapDecoder.create_async(
                                imaging.BitmapDecoder.png_decoder_id,
                                stream
                            )

                            # Get frame
                            bitmap_frame = await decoder.get_frame_async(0)

                            # Create software bitmap directly from frame
                            software_bitmap = await bitmap_frame.get_software_bitmap_async(
                                BitmapPixelFormat.BGRA8,
                                BitmapAlphaMode.PREMULTIPLIED
                            )

                            # Recognize text
                            result = await engine.recognize_async(software_bitmap)

                            # Process all lines and words
                            page_results = []
                            for line in result.lines:
                                for word in line.words:
                                    try:
                                        # Get bounding box
                                        bbox = word.bounding_rect
                                        # Convert to integers to avoid type issues
                                        x = int(bbox.x)
                                        y = int(bbox.y)
                                        w = int(bbox.width)
                                        h = int(bbox.height)

                                        # Add to results
                                        page_results.append({
                                            'text': word.text,
                                            'bbox': (x, y, w, h)
                                        })
                                    except Exception as e:
                                        print(f"Error processing word: {e}")
                                        continue
                            return page_results
                        finally:
                            # Always close the stream
                            stream.close()
                    except Exception as e:
                        print(f"OCR error: {str(e)}")
                        return []

                # Run the async function
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                ocr_results = loop.run_until_complete(recognize_text())
                loop.close()

                # Display the results on the page
                self.display_ocr_results(ocr_results)

                # Update status
                self.statusBar.showMessage(f"OCR completed for whole page, found {len(ocr_results)} text elements")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to perform OCR: {str(e)}")
                self.statusBar.showMessage("Error performing OCR")
            finally:
                # Clean up the temporary file
                try:
                    os.unlink(temp_filename)
                except:
                    pass
                QApplication.restoreOverrideCursor()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to perform OCR: {str(e)}")
            self.statusBar.showMessage("Error performing OCR")
            QApplication.restoreOverrideCursor()

    def display_ocr_results(self, ocr_results):
        """Display OCR results with bounding boxes on the page."""
        if not ocr_results:
            return

        # Create a copy of the current image to draw on
        display_img = self.current_cv_img.copy()

        # Draw bounding boxes and text for each result
        for result in ocr_results:
            try:
                x, y, w, h = result['bbox']
                text = result['text']

                # Ensure coordinates are within image bounds
                height, width, _ = display_img.shape
                x = max(0, min(int(x), width-1))
                y = max(0, min(int(y), height-1))
                w = max(1, min(int(w), width-x))
                h = max(1, min(int(h), height-y))

                # Draw bounding box
                cv2.rectangle(display_img, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # Draw text above the box (ensure y-5 doesn't go negative)
                text_y = max(10, y - 5)
                cv2.putText(display_img, text, (x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            except Exception as e:
                print(f"Error drawing result: {e}")
                continue
        # Update the PDF viewer with the annotated image
        height, width, channel = display_img.shape
        bytesPerLine = 3 * width
        qImg = QImage(display_img.data, width, height, bytesPerLine, QImage.Format_RGB888).rgbSwapped()
        self.pdfViewer.scene.clear()
        self.pdfViewer.pixmap_item = self.pdfViewer.scene.addPixmap(QPixmap.fromImage(qImg))
        self.pdfViewer.setSceneRect(self.pdfViewer.pixmap_item.boundingRect())

        # Also update the OCR results table
        df = pd.DataFrame([{'region_id': i+1, 'text': result['text']} for i, result in enumerate(ocr_results)])
        self.ocrResultsModel.setDataFrame(df)

        # Resize columns to content
        self.ocrResultsTable.resizeColumnsToContents()

    def onZoomIn(self):
        """Zoom in the PDF viewer."""
        self.pdfViewer.scale(1.25, 1.25)
        self.pdfViewer.zoomFactor *= 1.25

    def onZoomOut(self):
        """Zoom out the PDF viewer."""
        self.pdfViewer.scale(0.8, 0.8)
        self.pdfViewer.zoomFactor *= 0.8

    def onZoomReset(self):
        """Reset zoom in the PDF viewer."""
        self.pdfViewer.resetZoom()

    def onFitView(self):
        """Fit the PDF page to the view."""
        self.pdfViewer.fitInView()

    def closeEvent(self, event):
        """Handle window close event."""
        # Close PDF document
        if self.doc:
            self.doc.close()
        event.accept()


def main():
    app = QApplication(sys.argv)
    window = DetectedRegionsUI()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()