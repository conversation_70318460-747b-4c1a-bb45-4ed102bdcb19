from PySide6.QtWidgets import (
    QWidget,
    Q<PERSON>p<PERSON><PERSON>,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QFrame,
    QApplication,
    QHeaderView,
    QComboBox,
    QLineEdit,
    QLabel,
    QStyledItemDelegate,
    QStyle,
    QToolTip,
    QTreeView,
    QMainWindow
)
from PySide6.QtCore import Qt, QAbstractItemModel, QModelIndex, Signal, QEvent, QPoint, QTimer, QSize, QSortFilterProxyModel
from PySide6.QtGui import QIcon, QMouseEvent, QClipboard
from dataclasses import dataclass
from typing import List, Optional, Any
import pandas as pd
import numpy as np
from src.views.documentviewonly import DocumentViewOnly  # Import DocumentViewOnly


class SeverityComboDelegate(QStyledItemDelegate):
    SEVERITY_LEVELS = ["none", "low", "medium", "high"]

    def createEditor(self, parent, option, index):
        editor = Q<PERSON>omboBox(parent)
        editor.addItems(self.SEVERITY_LEVELS)
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.DisplayRole)
        idx = editor.findText(value) if value else 0
        editor.setCurrentIndex(idx)

    def setModelData(self, editor, model, index):
        value = editor.currentText()
        # Convert "none" to None for the model
        model.setData(index, None if value == "none" else value, Qt.EditRole)

    def displayText(self, value, locale):
        # Don't display anything if the value is None
        return "" if value is None else value


class TextEditDelegate(QStyledItemDelegate):
    def createEditor(self, parent, option, index):
        editor = QLineEdit(parent)
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.DisplayRole)
        editor.setText(value if value else "")

    def setModelData(self, editor, model, index):
        model.setData(index, editor.text(), Qt.EditRole)


class ActionButtonDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.edit_icon = parent.style().standardIcon(QStyle.SP_FileIcon)
        self.confirm_icon = parent.style().standardIcon(QStyle.SP_DialogApplyButton)
        self.cancel_icon = parent.style().standardIcon(QStyle.SP_DialogCancelButton)
        self.copy_icon = parent.style().standardIcon(QStyle.SP_DialogSaveButton)
        self.icon_spacing = 4
        self.icon_size = 16
        self.tooltip_timer = QTimer()
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self.hide_tooltip)

    def hide_tooltip(self):
        QToolTip.hideText()

    def show_tooltip(self, pos, text):
        QToolTip.showText(pos, text)
        self.tooltip_timer.start(1500)  # Hide after 1.5 seconds

    def paint(self, painter, option, index):
        if not index.isValid():
            return

        item = index.internalPointer()
        if len(item.children) > 0:  # Don't show icons for non-leaf nodes
            return

        # Draw icons
        right_edge = option.rect.right()
        icon_y = option.rect.center().y() - self.icon_size // 2

        if item.is_editing:
            # Draw confirm icon
            confirm_rect = option.rect.adjusted(0, 0, -(self.icon_size + self.icon_spacing), 0)
            self.confirm_icon.paint(painter, confirm_rect, Qt.AlignRight | Qt.AlignVCenter)

            # Draw cancel icon
            cancel_rect = option.rect
            self.cancel_icon.paint(painter, cancel_rect, Qt.AlignRight | Qt.AlignVCenter)
        else:
            # Draw copy icon first (rightmost)
            copy_rect = option.rect
            self.copy_icon.paint(painter, copy_rect, Qt.AlignRight | Qt.AlignVCenter)

            # Draw edit icon to the left of copy icon
            edit_rect = option.rect.adjusted(0, 0, -(self.icon_size + self.icon_spacing), 0)
            self.edit_icon.paint(painter, edit_rect, Qt.AlignRight | Qt.AlignVCenter)

    def editorEvent(self, event, model, option, index):
        if not index.isValid():
            return False

        item = index.internalPointer()
        if len(item.children) > 0:  # Don't handle events for non-leaf nodes
            return False

        if event.type() == QEvent.Type.MouseButtonRelease:
            click_x = event.pos().x()
            right_edge = option.rect.right()

            if item.is_editing:
                # Check if clicked on confirm or cancel icon
                if click_x > right_edge - self.icon_size:
                    model.set_item_editing(index, False)  # Cancel
                elif click_x > right_edge - (2 * self.icon_size + self.icon_spacing):
                    model.set_item_editing(index, False)  # Confirm
            else:
                # Check if clicked on copy icon
                if click_x > right_edge - self.icon_size:
                    # Get QTY value (column 4) and copy to clipboard
                    qty = str(item.data[4]) if item.data[4] is not None else ""
                    if qty:
                        QApplication.clipboard().setText(qty)
                        self.show_tooltip(event.globalPos(), f"Copied: {qty}")
                # Check if clicked on edit icon
                elif click_x > right_edge - (2 * self.icon_size + self.icon_spacing):
                    model.set_item_editing(index, True)
            return True

        return False


class TreeItem:
    def __init__(self, data, parent=None, children=None):
        self.data = data
        self.parent = parent
        self.children = children if children is not None else []
        self.is_editing = False

    def row(self):
        if self.parent:
            return self.parent.children.index(self)
        return 0


class AuditTreeModel(QAbstractItemModel):
    COLUMNS = ["pos", "material_description", "componentCategory", "size", "quantity", "severity", "actions"]
    EDITABLE_COLUMNS = [1, 2, 3, 4, 5]  # material_description, category, size, quantity, severity
    editingChanged = Signal(QModelIndex, bool)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.root = TreeItem(["Root", "", "", "", "", "", ""], None)
        self.df = pd.DataFrame(columns=self.COLUMNS[:-1] + ["issue_description"])  # Include hidden column
        self.currently_editing = None  # Track currently edited row
        # self.setup_sample_data()

    def setup_sample_data(self):
        # Create sample DataFrame
        data = {
            'pos': ['P1', 'P1', 'P1', 'P2', 'P2', 'P3', 'P3', 'P4'],
            'material_description': [
                'Carbon Steel Pipe', 'Stainless Steel Pipe', 'Gate Valve',
                'Check Valve', 'Ball Valve', 'Flange', 'Gasket', 'Support'
            ],
            'componentCategory': ['pipe', 'pipe', 'valve', 'valve', 'valve', 'flange', 'gasket', 'support'],
            'size': ['2"', '3"', '2"', '3"', '2"', '3"', '3"', '-'],
            'quantity': [10, 20, 2, 1, 3, 4, 4, 2],
            'severity': ['high', 'medium', 'low', 'high', 'medium', 'low', 'high', 'medium'],
            'pdf_page': [1, 1, 1, 2, 2, 3, 3, 4],
            'issue_description': [
                'Small inches value (< 12): 2"',
                'Material grade missing',
                'Valve class not specified',
                'Check valve direction not shown',
                'Ball valve handle position unclear',
                'Flange rating missing',
                'Gasket material not specified',
                'Support type not defined'
            ]
        }
        self.df = pd.DataFrame(data)
        self.set_dataframe(self.df)
        # self.update_tree_from_df()

    def update_tree_from_df(self):
        # Clear existing tree
        self.root.children.clear()
        self.layoutAboutToBeChanged.emit()

        self.df["componentCategory"].fillna("Unknown", inplace=True)

        # First group by pdf_page, then by Category
        grouped = self.df.groupby(['pdf_page', 'componentCategory'])

        print(self.df["componentCategory"].unique().tolist())
        print([n for n in grouped])

        current_page = None
        current_page_item = None

        for (page, category), group in grouped:
            # Create or get page node
            if current_page != page:
                current_page = page
                page_data = [f"Page {page}", "", "", "", "", "", ""]
                current_page_item = TreeItem(page_data, self.root)
                self.root.children.append(current_page_item)

            # Create category node under page
            category_data = [category, "", "", "", "", "", ""]
            category_item = TreeItem(category_data, current_page_item)
            current_page_item.children.append(category_item)

            # Add items under the category
            for idx, row in group.iterrows():
                item_data = [
                    row['pos'],
                    row['material_description'],
                    row['componentCategory'],
                    row['size'],
                    str(row['quantity']),
                    row['severity'],
                    ""  # actions column
                ]
                child_item = TreeItem(item_data, category_item)
                category_item.children.append(child_item)

        self.layoutChanged.emit()

    def update_item_data(self, index, value):
        if not index.isValid():
            return False

        item = index.internalPointer()
        if not item or not item.parent or len(item.children) > 0:  # Only update leaf nodes
            return False

        col = index.column()
        if col not in self.EDITABLE_COLUMNS:
            return False

        # Update both tree item and DataFrame
        item.data[col] = value

        # Find the corresponding row in DataFrame
        pos = item.data[0]  # pos
        desc = item.data[1]  # material_description
        mask = (self.df['pos'] == pos) & (self.df['material_description'] == desc)

        # Update DataFrame
        if col == 1:  # material_description
            # For description changes, we need to update the identifier
            old_desc = self.df.loc[mask, 'material_description'].iloc[0]
            self.df.loc[mask, 'material_description'] = value
        else:
            col_name = self.COLUMNS[col]
            if col_name == 'quantity':
                # Convert to integer if possible
                try:
                    value = int(value)
                except ValueError:
                    return False
            self.df.loc[mask, col_name] = value

        return True

    def rowCount(self, parent):
        if parent.column() > 0:
            return 0
        if not parent.isValid():
            return len(self.root.children)
        return len(parent.internalPointer().children)

    def columnCount(self, parent):
        return len(self.COLUMNS)

    def data(self, index, role):
        if not index.isValid():
            return None

        item = index.internalPointer()
        col = index.column()

        if role == Qt.DisplayRole or role == Qt.EditRole:
            return item.data[col]
        elif role == Qt.ToolTipRole:
            # Only show tooltip for leaf nodes (items)
            if len(item.children) == 0:
                # Find the corresponding row in DataFrame
                pos = item.data[0]  # pos
                desc = item.data[1]  # material_description
                mask = (self.df['pos'] == pos) & (self.df['material_description'] == desc)
                if not mask.empty:
                    issue = self.df.loc[mask, 'issue_description'].iloc[0]
                    if pd.notna(issue):
                        return f"Issue: {issue}"

        return None

    def setData(self, index, value, role):
        if role == Qt.EditRole:
            if self.update_item_data(index, value):
                self.dataChanged.emit(index, index, [role])
                return True
        return False

    def headerData(self, section, orientation, role):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self.COLUMNS[section]
        return None

    def index(self, row, column, parent):
        if not self.hasIndex(row, column, parent):
            return QModelIndex()

        if not parent.isValid():
            parent_item = self.root
        else:
            parent_item = parent.internalPointer()

        if row < len(parent_item.children):
            child_item = parent_item.children[row]
            return self.createIndex(row, column, child_item)
        return QModelIndex()

    def parent(self, index):
        if not index.isValid():
            return QModelIndex()

        child_item = index.internalPointer()
        parent_item = child_item.parent

        if parent_item == self.root:
            return QModelIndex()

        return self.createIndex(parent_item.row(), 0, parent_item)

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        item = index.internalPointer()
        col = index.column()

        # Base flags
        flags = Qt.ItemIsEnabled | Qt.ItemIsSelectable

        # Only allow text selection for non-action columns
        if col != 6:  # Not actions column
            flags |= Qt.ItemIsSelectable

        # Only allow editing for leaf nodes (items under sections) when in edit mode
        is_leaf = len(item.children) == 0
        if is_leaf and col in self.EDITABLE_COLUMNS and item.is_editing:
            flags |= Qt.ItemIsEditable

        return flags

    def set_item_editing(self, index, editing):
        if not index.isValid():
            return

        item = index.internalPointer()
        if not item or not item.parent:
            return

        # If trying to edit a new row while another is being edited, cancel the edit
        if editing and self.currently_editing and self.currently_editing != item:
            old_item = self.currently_editing
            old_item.is_editing = False
            # Find the index for the old item to emit dataChanged
            old_parent = old_item.parent
            old_row = old_parent.children.index(old_item) if old_parent else 0
            old_parent_index = self.createIndex(old_parent.row(), 0, old_parent) if old_parent != self.root else QModelIndex()
            old_index = self.index(old_row, 0, old_parent_index)
            self.editingChanged.emit(old_index, False)
            self.dataChanged.emit(
                self.index(old_row, 0, old_parent_index),
                self.index(old_row, len(self.COLUMNS) - 1, old_parent_index)
            )

        # Update the new item's edit state
        item.is_editing = editing
        self.currently_editing = item if editing else None
        self.editingChanged.emit(index, editing)
        self.dataChanged.emit(
            self.index(index.row(), 0, index.parent()),
            self.index(index.row(), len(self.COLUMNS) - 1, index.parent())
        )

    def get_dataframe(self):
        """Return the underlying DataFrame"""
        return self.df.copy()

    def set_dataframe(self, df):
        """Set a new DataFrame as the data source"""
        self.df = df.copy()
        self.update_tree_from_df()


class AuditDialog(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("popup")
        self.setWindowTitle("Audit Data Visualization")
        self.setup_ui()

    def on_selection_changed(self, selected, deselected):
        """Handle selection changes in the tree view"""
        # Get unique rows from selection (avoid processing same row multiple times)
        selected_items = set()
        for index in selected.indexes():
            item = index.internalPointer()
            if len(item.children) == 0:  # Only leaf nodes contain actual data
                selected_items.add(item)

        # Print data for each selected item
        for item in selected_items:
            # Get the corresponding row from DataFrame
            pos = item.data[0]  # pos
            desc = item.data[1]  # material_description
            mask = (self.tree_model.df['pos'] == pos) & (self.tree_model.df['material_description'] == desc)
            if not mask.empty:
                row = self.tree_model.df.loc[mask].iloc[0]
                print("\nSelected Row:")
                print(row)

    def setup_ui(self):
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins since it's now a widget

        # Top toolbar
        toolbar = QWidget()
        toolbar.setFixedHeight(32)
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # Add buttons to toolbar
        save_button = QPushButton("Save Audit")
        export_button = QPushButton("Export Report")
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(save_button)
        toolbar_layout.addWidget(export_button)

        # Filter toolbar
        filter_toolbar = QWidget()
        filter_toolbar.setFixedHeight(32)
        filter_layout = QHBoxLayout(filter_toolbar)
        filter_layout.setContentsMargins(8, 0, 8, 0)

        # Severity filter
        severity_label = QLabel("Severity:")
        self.severity_combo = QComboBox()
        self.severity_combo.addItems(["all items"] + SeverityComboDelegate.SEVERITY_LEVELS)

        # Page filter
        page_label = QLabel("Page:")
        self.page_combo = QComboBox()
        self.page_combo.addItems(["all pages", "page 1", "page 2", "page 3"])

        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search items...")
        self.search_box.setFixedWidth(200)

        # Item count
        self.item_count_label = QLabel("9 items, 4 with issues")

        # Add widgets to filter layout
        filter_layout.addWidget(severity_label)
        filter_layout.addWidget(self.severity_combo)
        filter_layout.addSpacing(16)
        filter_layout.addWidget(page_label)
        filter_layout.addWidget(self.page_combo)
        filter_layout.addSpacing(16)
        filter_layout.addWidget(self.search_box)
        filter_layout.addStretch()
        filter_layout.addWidget(self.item_count_label)

        # Create splitter
        splitter = QSplitter(Qt.Horizontal)

        # Left side document viewer
        self.document_viewer = DocumentViewOnly()
        # self.document_viewer.sgnClose.connect(self.close)  # Close dialog when document viewer is closed

        # Right side tree view
        self.tree_view = QTreeView()
        self.tree_model = AuditTreeModel()
        self.tree_view.setModel(self.tree_model)

        # Connect selection changed signal
        selection_model = self.tree_view.selectionModel()
        selection_model.selectionChanged.connect(self.on_selection_changed)

        # Set up delegates
        self.text_delegate = TextEditDelegate(self.tree_view)
        self.severity_delegate = SeverityComboDelegate(self.tree_view)
        self.action_delegate = ActionButtonDelegate(self.tree_view)

        # Apply delegates to columns
        for col in self.tree_model.EDITABLE_COLUMNS:
            if col == 5:  # severity column
                self.tree_view.setItemDelegateForColumn(col, self.severity_delegate)
            else:
                self.tree_view.setItemDelegateForColumn(col, self.text_delegate)
        self.tree_view.setItemDelegateForColumn(6, self.action_delegate)  # actions column

        # Configure tree view
        self.tree_view.setAlternatingRowColors(True)
        self.tree_view.expandAll()
        self.tree_view.setSelectionMode(QTreeView.ExtendedSelection)  # Allow multiple selection
        self.tree_view.setTextElideMode(Qt.ElideMiddle)  # Show ... in middle of long text
        self.tree_view.setSelectionBehavior(QTreeView.SelectItems)  # Select individual cells

        # Configure header
        header = self.tree_view.header()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # material_description column
        header.setStretchLastSection(False)

        # Set column widths and visibility
        self.tree_view.setColumnWidth(0, 120)   # pos/Page/Category
        self.tree_view.setColumnWidth(3, 80)    # size
        self.tree_view.setColumnWidth(4, 80)    # quantity
        self.tree_view.setColumnWidth(5, 100)   # severity
        self.tree_view.setColumnWidth(6, 80)    # actions
        self.tree_view.hideColumn(2)            # Hide category column

        # Add widgets to splitter
        splitter.addWidget(self.document_viewer)
        splitter.addWidget(self.tree_view)

        # Set initial sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Add widgets to main layout
        main_layout.addWidget(toolbar)
        main_layout.addWidget(filter_toolbar)
        main_layout.addWidget(splitter)

        # Set dialog size
        self.resize(1200, 800)  # Increased size to accommodate document viewer


def main():

    df = pd.read_excel(r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\temp\validated_df.xlsx")
    # df = df.head(10)
    df["componentCategory"].fillna("Unknown Category", inplace=True)
    df.fillna("", inplace=True)
    print(df.columns)
    print(df)
    import sys
    sys.path[0] = ''
    from src.theme import stylesheet
    app = QApplication(sys.argv)
    app.setStyleSheet(stylesheet)

    # Create main window to hold the widget
    main_window = QMainWindow()
    main_window.setObjectName("popup")
    main_window.setWindowTitle("Audit View")

    # Create and set the audit widget as central widget
    audit_widget = AuditDialog()
    main_window.setCentralWidget(audit_widget)
    main_window.resize(1200, 800)

    main_window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()

    main_window.show()
    audit_widget.tree_model.set_dataframe(df)

    sys.exit(app.exec())


if __name__ == "__main__":
    main()