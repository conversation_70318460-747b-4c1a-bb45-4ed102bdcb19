from src.app_paths import getSavedFieldMapJson
import pandas as pd
import os

rfq_column_order = [
        "material_description", "size", "size1", "size2", "quantity", "componentCategory",
        "rfq_scope", "general_category", "unit_of_measure", "material",
        "abbreviated_material", "ansme_ansi", "astm", "grade", "rating", "schedule",
        "coating", "forging", "ends", "item_tag", "tie_point", "pipe_category",
        "valve_type", "fitting_category", "weld_category", "bolt_category",
        "gasket_category", "ef", "sf", "last_updated", "__checked__", "exists_in_verified_materials"
        ]

# Ensures Pandas does not interpret "N/A" as nan
def read_excel_with_na(file_path):
    return pd.read_excel(file_path,
                         keep_default_na=False,
                         na_values=[''])  # This will treat empty cells as NaN, but keep 'N/A' as a string

def convert_display_to_key(df, column_order=None):
    """
    Convert display names to database keys in DataFrame columns.

    Args:
        df: DataFrame with display name columns
        column_order: Optional list of column names for reordering
        

    Returns:
        DataFrame with converted column names and optional reordering
    """
    # Load the field mapping
    field_mapping = getSavedFieldMapJson()

    # Create a dictionary to map display names to key names
    display_to_key = {}

    # Populate the dictionary from both 'fields' and 'rfq_fields'
    for field_type in ['fields', 'rfq_fields', 'ancillary_fields']:
        if field_type in field_mapping:
            for key, value in field_mapping[field_type].items():
                if 'display' in value:
                    display_to_key[value['display']] = key

    # Create a dictionary to rename the columns
    rename_dict = {col: display_to_key.get(col, col) for col in df.columns}

    # Rename the columns
    df_converted = df.rename(columns=rename_dict)

    # Apply column reordering if provided
    if column_order:
        df_converted = reorder_columns(df_converted, column_order)

    return df_converted


def reorder_columns(df, column_order):
    """
    Reorder DataFrame columns according to a specified order.
    Adds empty columns for any columns in column_order that are not in the DataFrame.

    Args:
        df: DataFrame to reorder
        column_order: List of column names in desired order

    Returns:
        DataFrame with reordered columns exactly matching column_order
    """
    # Check if there are any columns in the DataFrame not in column_order
    missing_in_order = [col for col in df.columns if col not in column_order]

    # Check if there are any columns in column_order not in the DataFrame
    not_in_df = [col for col in column_order if col not in df.columns]

    # Report results
    if missing_in_order:
        print(f"Warning: The following columns in the DataFrame are not in the column_order list: {missing_in_order}")

    if not_in_df:
        print(f"Warning: The following columns in the column_order list are not in the DataFrame: {not_in_df}")

    # Create a copy of the DataFrame to avoid modifying the original
    result_df = df.copy()

    # Add missing columns with empty values
    for col in not_in_df:
        result_df[col] = ""

    # Reorder columns to match column_order exactly
    result_df = result_df[column_order]

    return result_df


def convert_and_save_excel(input_path, output_name=None, column_order=None):
    """
    Convert display names to keys in an Excel file and optionally reorder columns.
    If column_order is provided, the output will contain exactly those columns,
    adding empty columns for any that are not in the input file.

    Args:
        input_path: Path to input Excel file
        output_name: Optional path for output file, defaults to "converted_{filename}"
        column_order: Optional list of column names for reordering

    Returns:
        Path to the saved output file
    """
    # Load the Excel workbook into a DataFrame
    df = pd.read_excel(input_path)

    # Convert the column names and optionally reorder columns
    df_converted = convert_display_to_key(df, column_order)

    # Generate the output file path
    directory, filename = os.path.split(input_path)
    name, ext = os.path.splitext(filename)

    if not output_name:
        output_path = os.path.join(directory, f"converted_{name}{ext}")
    else:
        output_path = output_name

    # Save the converted DataFrame to a new Excel file
    df_converted.to_excel(output_path, index=False)

    print(f"Converted file saved as: {output_path}")
    print("Converted columns:")
    print(df_converted.columns)

    return output_path

# Example usage
if __name__ == "__main__":
    import sys
    sys.path[0] = ""
    # Define the RFQ column order
    

    input_excel_path = r"S:\Favorites\Client Work Space\Enerfab\Enerfab 0000\data\modified\exported_rfq_data_nofieldmap - rev 1 - classified.xlsx"
    output_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\EnerFab\Workspace\RFQ-Rev 1.xlsx"
    converted_file_path = convert_and_save_excel(input_excel_path,
                                                 output_path,
                                                 column_order=rfq_column_order)



    # input_excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 019\Data\exported_general_data.xlsx"
    # converted_file_path = convert_and_save_excel(input_excel_path,r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 019\Data\In\general.xlsx")

    # input_excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 013\Data\Merged RFQ.xlsx" #'exported_rfq_data.xlsx'
    # converted_file_path = convert_and_save_excel(input_excel_path, r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 013\Data\converted_rfq.xlsx")

