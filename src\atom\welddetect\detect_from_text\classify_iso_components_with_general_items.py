"""
This module extends the classify_iso_components.py functionality to work with general items
detected by the detect_general_items.py script (like gussets).

It serves as a wrapper around the original functionality but with updated paths and
the ability to read feather files directly.
"""

import os
import sys
import time
import pandas as pd
import fitz  # PyMuPDF

# Add the parent directory to the path so we can import from old
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

# Import the functions from the original module
try:
    from old.classify_iso_components import process_drawings, clean_dataframe, RelationshipManager
    from old.classify_iso_components import load_detected_general_items, load_detected_welds, load_bom_data
    from old.classify_iso_components import process_raw_data, create_output_pdf
except ImportError:
    print("Error importing from old.classify_iso_components. Make sure the path is correct.")
    sys.exit(1)

def main(pages_to_process=None):
    """
    Main function that runs the iso component classification with general items detection.
    
    Args:
        pages_to_process: List of page numbers to process. If None, all pages will be processed.
    """
    # Ensure pages_to_process is a list of integers or None
    if pages_to_process is not None:
        # Make sure we're working with a list of integers
        pages_to_process = [int(p) for p in pages_to_process]
    # Updated paths for the current project
    input_pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\TR-001 Combined.pdf"

    # Set up output directory in the same folder as the detect_general_items.py output
    base_output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001"
    output_dir = base_output_dir
    output_pdf_path = os.path.join(output_dir, "TR-001_iso_components.pdf")

    # Data files
    raw_data_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\74\sources\cuserscmccaonedrivedocumentsarchitekt-isclientsexcel-usaexc_0014modifiedtr-001-combinedpdf\data.feather"
    bom_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001\bom_tr-001.xlsx"

    # Path to detected general items (like gussets) - using the output from detect_general_items.py
    detected_general_items_path = os.path.join(base_output_dir, "TR-001_gusset_items.xlsx")

    # For this example, we don't have detected welds yet, so we'll use the gusset items as a placeholder
    # In a real scenario, you would have a separate file for detected welds
    detected_welds_path = detected_general_items_path

    # Verify that files exist
    for filepath in [input_pdf_path, raw_data_path, bom_data_path, detected_general_items_path]:
        if not os.path.exists(filepath):
            print(f"Warning: File not found: {filepath}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    start_time = time.time()

    # Print confirmation of which pages will be processed
    if pages_to_process:
        print(f"\n*** LIMITING PROCESSING TO THESE PAGES ONLY: {pages_to_process} ***\n")
    else:
        print("\n*** WARNING: PROCESSING ALL PAGES - THIS COULD TAKE A LONG TIME ***\n")
        
    # Initialize the relationship manager
    rm = RelationshipManager()
    
    # Define isometric area
    isometric_area = {
        'x0': 804.5,
        'x1': 2404.5,
        'y0': 40.0,
        'y1': 1234.0
    }
    
    # Process vector drawings and create DataFrame
    print("\n\nIsolating Isometric Properties...")
    
    # IMPORTANT: We're directly calling process_drawings here to ensure page filtering works
    df = process_drawings(input_pdf_path, rm, pages_to_process, isometric_area_coords=isometric_area)
    
    # Converts id columns to integers
    df = clean_dataframe(df)
    
    # Load detected welds
    print("\n\nLoading Weld, BOM, Raw Data...")
    weld_maps = load_detected_welds(detected_welds_path)
    
    # Load detected general items
    general_items = []
    if detected_general_items_path:
        print("Loading detected general items (gussets, etc.)...")
        general_items = load_detected_general_items(detected_general_items_path)
        if general_items:
            print(f"Loaded {len(general_items)} general items")
    
    # Load BOM data
    bom_data = load_bom_data(bom_data_path)
    
    # Process raw data
    print("\n\nProcessing Raw Data...")
    df = process_raw_data(df, raw_data_path, weld_maps, general_items, bom_data)
    
    # Create output PDF
    print("\n\nCreating Output PDF...")
    create_output_pdf(input_pdf_path, output_pdf_path, df, pages_to_process)

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n\nClassification completed in {total_time:.2f} seconds.")
    
    # Save the results to Excel
    results_path = os.path.join(output_dir, "TR-001_classification_results.xlsx")
    df.to_excel(results_path, index=False)
    print(f"Results saved to {results_path}")

    return df

if __name__ == "__main__":
    # CONFIGURATION: Specify which pages to process here for IDE execution
    # Set to None to process all pages, or specify a list of page numbers
    # Example: PAGES_TO_PROCESS = [1, 2, 3]  # Process only pages 1, 2, and 3
    PAGES_TO_PROCESS = [326, 519, 535, 586, 594]  # Process all pages
    
    # Check if running with command line arguments
    import sys
    if len(sys.argv) > 1:
        import argparse
        # Set up command line argument parsing
        parser = argparse.ArgumentParser(description='Process ISO components with general items detection')
        parser.add_argument('--pages', type=int, nargs='+', help='Specific pages to process (e.g., --pages 1 2 3)')
        
        args = parser.parse_args()
        
        # Command line arguments override the configuration
        if args.pages:
            PAGES_TO_PROCESS = args.pages
    
    # Run the main function with the specified pages
    main(PAGES_TO_PROCESS)
