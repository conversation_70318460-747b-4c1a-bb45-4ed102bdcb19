
import pandas as pd


def plugin_fill_field(data_file, unfilled_file, save_file: str = "debug/filled_data.xlsx", column_to_fill: str = "elevation", matching_column: str = "pdf_page"):
    """Fills in cells values given matching column

    e.g. to fill in elevation values extracted externally from OCR
    """
    df = pd.read_excel(data_file)
    df2 = pd.read_excel(unfilled_file)

    numbers = {}
    for _, row in df.iterrows():
        print(row[matching_column])
        if not row[column_to_fill]:
            continue
        numbers[row[matching_column]] = row[column_to_fill]

    if column_to_fill not in df2.columns:
        print(df2.columns)
        return f"Column {column_to_fill} not found in {unfilled_file}"


    for index, row in df2[df2[column_to_fill].isna()].iterrows():
        v = numbers.get(row[matching_column])
        if v:
            df2.loc[index, column_to_fill] = v

    df2.to_excel(save_file)
    print(numbers)
