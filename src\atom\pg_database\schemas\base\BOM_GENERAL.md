# PostgreSQL Migration Guidance

## Column Mapping Overview

I've created a comprehensive mapping between your SQLite columns and the new PostgreSQL columns. Here's a summary of the key changes:

# BOM and General Tables Integration Guide

- See base_tables_schemas.md for relevant table definitions converted from SQL Lite
- src\atom\pg_database\README.pgDatabase.md for existing pg resources
- rest of the files in src\atom\pg_database\schemas\base folder for additional context

## Goal

Extend our existing RFQ system to create BOM and General tables that integrate with our current data flow:

```
rfq_input ⟷ rfq → bom → general
```

Use the existing functions in `functions.py` as a reference to maintain consistency with our established patterns, particularly the bidirectional propagation between rfq and rfq_input.

## Reference Materials

Before starting, review these existing resources:

1. **Existing Function Definitions**:
   - `functions.py` - Contains trigger functions for bidirectional sync between tables
   - Study especially the `FUNC_SYNC_RFQ_TO_INPUT` and `FUNC_SYNC_INPUT_TO_RFQ` sections
   - These provide the pattern you should follow for downstream propagation

2. **Schema Definitions**:
   - Location: `src\atom\pg_database\schemas\base`
   - Contains schemas and explanation for all existing tables
   - Look for BOM and General logic documentation

3. **PostgreSQL Database**:
   - Use pgAdmin to inspect the existing tables and triggers
   - The functions implementing the bidirectional sync are already in the database

## Implementation Tasks

1. **Create BOM and General Tables**:
   - Create both tables without PDF references (use project_id instead)
   - BOM should include an rfq_ref_id column to link back to originating RFQ entries
   - Follow the table structure outlined in the schema definitions folder

2. **Extend Propagation Pattern**:
   - Create functions modeled after the existing sync functions in `functions.py`
   - Extend propagation to flow: rfq → bom → general
   - Implement bidirectional sync between rfq and bom similar to existing rfq/rfq_input sync

3. **Fix Surface Area Calculations**:
   - The current version uses the same SF for each inserted row (incorrect)
   - Ensure calculations are performed on a per-component basis
   - Aggregate correctly in the General table by size

## Key Implementation Points

1. **BOM Table**:
   - Should initially load from RFQ
   - Use rfq_ref_id to maintain the relationship with RFQ data
   - Handle component categories correctly
   - Calculate surface area properly based on component type and size

2. **General Table**:
   - Aggregate BOM data by size
   - Fix the surface area calculation issues in the current version
   - Maintain real-time updates when BOM changes

3. **Bidirectional Updates**:
   - When BOM is modified, changes should propagate back to RFQ
   - Follow the pattern in `FUNC_SYNC_RFQ_TO_INPUT` and `FUNC_SYNC_INPUT_TO_RFQ`
   - Handle deletions, material_description modifications, size changes

## Next Steps After Table Creation

Once the tables and triggers are working:
1. Create a Python function to load initial data from RFQ to BOM
2. Let the triggers handle the propagation to General
3. Test with various modification scenarios to ensure bidirectional updates work

## Technical Notes

- The database is on neon.tech (you should have received an invite)
- Use pgAdmin for database inspection and testing
- Claude 3.7 Sonnet Extended mode or GPT 4.5 are good resources if you get stuck

This integration builds on our existing patterns rather than introducing new approaches, so reference the existing code as much as possible.

### General Table Changes

1. **Measurement columns renamed**:
   - `lf` → `length`
   - `sf` → `calculated_area`
   - `ef` → `calculated_eq_length`

2. **New fields added**:
   - `union_couplings`
   - `expansion_joints`
   - `created_at`
   - `updated_at`

3. **All camelCase converted to snake_case**:
   - `annotMarkups` → `annot_markups`
   - `clientDocumentId` → `client_document_id`
   - `isoNumber` → `iso_number`
   - etc.

### BOM Table Changes

1. **Measurement columns renamed**:
   - `ef` → `calculated_eq_length`
   - `sf` → `calculated_eq_area`

2. **New fields added**:
   - `size1` and `size2` for standardized size handling
   - `created_at` and `updated_at` for audit

3. **camelCase to snake_case**:
   - `componentCategory` → `component_category`
   - `lineNumber` → `line_number`

## Using the Mapping Dictionary

The provided mapping dictionary can be used in your migration scripts to:

1. **Generate SQL statements** for extracting data from SQLite and inserting into PostgreSQL
2. **Transform data** during migration where column names have changed
3. **Map queries** between systems during the transition period

## Migration Considerations

When migrating your data:

### 1. Data Type Conversion

Pay special attention to:
- Converting TEXT fields to DECIMAL for measurements
- Ensure size fields are properly formatted as DECIMAL(12,3)

Example conversion SQL:
```sql
-- Example of converting a text field to decimal
UPDATE general SET size = CASE 
    WHEN size ~ '^[0-9.]+$' THEN size::DECIMAL(12,3)
    ELSE NULL
END;
```

### 2. Handling New Fields

For new fields like `union_couplings` and `expansion_joints`:
- Set default values during migration
- Or update these fields after initial migration based on computed/derived data

### 3. Date Fields

For the new timestamp fields:
- `created_at`: Either use the current migration timestamp or derive from existing data
- `updated_at`: Initially set to the same as `created_at`

### 4. Splitting Size Fields

If you need to split combined size fields:
```python
def split_size(combined_size):
    """Split a combined size field (e.g., "4 x 2") into size1 and size2."""
    if 'x' in combined_size:
        parts = combined_size.split('x')
        return parts[0].strip(), parts[1].strip()
    return combined_size.strip(), None
```

## Migration Script Structure

A typical migration script would follow this pattern:

```python
import psycopg2
import sqlite3
from column_mapping import GENERAL_TABLE_MAPPING, BOM_TABLE_MAPPING

# Connect to SQLite
sqlite_conn = sqlite3.connect('your_sqlite_db.db')
sqlite_cursor = sqlite_conn.cursor()

# Connect to PostgreSQL
pg_conn = psycopg2.connect(
    "dbname=your_db user=user password=pass host=host"
)
pg_cursor = pg_conn.cursor()

# Migrate General table
sqlite_cursor.execute("SELECT * FROM General")
for row in sqlite_cursor.fetchall():
    # Create a dictionary with SQLite column names and values
    row_dict = dict(zip([col[0] for col in sqlite_cursor.description], row))
    
    # Map to PostgreSQL column names
    pg_values = {}
    for sqlite_col, value in row_dict.items():
        pg_col = GENERAL_TABLE_MAPPING.get(sqlite_col)
        if pg_col:
            pg_values[pg_col] = value
    
    # Insert into PostgreSQL
    columns = ', '.join(pg_values.keys())
    placeholders = ', '.join(['%s'] * len(pg_values))
    query = f"INSERT INTO general ({columns}) VALUES ({placeholders})"
    pg_cursor.execute(query, list(pg_values.values()))

# Commit changes
pg_conn.commit()
```

## Testing the Migration

Always test your migration thoroughly:

1. Verify row counts match between systems
2. Sample random rows to verify content matches
3. Test key queries to ensure they return the same results
4. Verify foreign key relationships are intact

The mapping dictionaries will help make the migration process smoother and more reliable.