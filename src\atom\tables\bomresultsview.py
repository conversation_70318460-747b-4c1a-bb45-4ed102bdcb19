from src.utils.logger import logger
import time

from pubsub import pub
import pandas as pd
from PySide6.QtWidgets import *
from PySide6.QtCore import *

from data_conversions import convert_quantity_to_float
from src.atom.dbManager import DatabaseManager
from src.views.tableresultsview import TableResultsViewBase

# logger = logging.getLogger(__file__)


class PrepareRfqWorker(QObject):
    """Peforms processing on BOM data for conversion to RFQ

    Results - a dataframe with material_desciption, size, quantity and componentCategory columns
    """

    finished = Signal()

    def __init__(self, bomData):
        super().__init__()
        self._stop_flag = []
        self.error = None
        self.result = None  # Final DF
        self.bomData = bomData

    def run(self):
        # time.sleep(0.5)
        try:
            # Process quantity to ensure it's in a uniform format
            self.bomData['quantity'] = self.bomData['quantity'].apply(convert_quantity_to_float)
            # Group and sum quantity
            self.bomData['size'] = self.bomData['size'].astype("string") # Needed for summation
        except Exception as e:
            logger.info("Prepare BOM data for RFQ failed", exc_info=True)
            self.error = e
            raise Exception("Failed to process BOM data for RFQ")

        try:
            # Store the componentCategory information before grouping
            category_info = self.bomData[['material_description', 'size', 'componentCategory']].drop_duplicates()

            grouped_df = self.bomData.groupby(['material_description', 'size'])['quantity'].sum().reset_index()

            # Merge the componentCategory back into the grouped data
            self.result = pd.merge(grouped_df, category_info, on=['material_description', 'size'], how='left')

            # If there are multiple componentCategory values for a single material_description and size,
            # this will create duplicate rows. To handle this, you might want to:
            # 1. Keep the first occurrence:
            self.result = self.result.drop_duplicates(subset=['material_description', 'size'], keep='first')
            # 2. Combine multiple values (uncomment the next line if you prefer this approach)
            # final_df['componentCategory'] = final_df.groupby(['material_description', 'size'])['componentCategory'].transform(lambda x: ', '.join(set(x)))

            self.result["size"] = self.result["size"].astype("object") # Important - Convert back to object
        except Exception as e:
            logger.error(f"Failed to group bom {e}")
            self.error = e
            raise Exception("Failed to group BOM data by Material Description and Size")

        self.finished.emit()

    def stop(self):
        if not self._stop_flag:
            logger.info("Build RFQ Canceled")
            self._stop_flag.append(True)


class BomResultsView(TableResultsViewBase):

    sgnUpdateRfq = Signal(object)
    def __init__(self, parent) -> None:
        super().__init__(parent)
        logger.info("Creating BOM Table...")
        self.thread: QThread = None
        self.worker: PrepareRfqWorker = None

    def  __repr__(self) -> str:
        return "BOM"

    def cleanup(self):
        super().cleanup()

    def initToolbar(self):
        super().initToolbar()  # Initialize the base toolbar
        # Define the buttons and their icons
        buttons = [
            ("build-rfq", "build-rfq.svg")
        ]
        for btn_name, icon_path in buttons:
            self.addToolbarButton(btn_name, icon_path)

    def setTableData(self, data: pd.DataFrame):
        logger.info("Setting BOM Table Data...")
        super().setTableData(data, autosize=False)
        logger.info("Appending RFQ Fields to BOM Table Data...")
        self._addRFQFields()
        self.restoreColumnOrder()

        self.table.setEditFreezeColumns(["material_description", "size"])

    def onToolbarBtn(self, name):
        logger.info(f"Toolbar button clicked: {name}", exc_info=True)
        if name == "build-rfq":
            self.buildRfq()
        elif name == "tree":
            pass
        elif name == "save":
            df = self.getTableData()
            print("\n\nBOM DF ROWS FOR DB COMMIT: \n", len(df))
            db_manager = DatabaseManager()
            db_manager.insert_dataframe(df, "BOM")
            # Need to refresh
            pub.sendMessage("request-project-table-data", data=self.projectData, types=["bom_data"])
        else:
            super().onToolbarBtn(name)

    def buildRfq(self):
        """Process Bill of Materials data and display in a new RFQ tab."""
        bomData = self.getTableData()

        # Checks
        needed = []
        bomData = self.getTableData().copy()
        if 'quantity' not in bomData.columns:
            needed.append("Quantity")
        if 'material_description' not in bomData.columns:
            needed.append("Material Description")
        if needed:
            QMessageBox.information(self, "Cannot build RFQ", f"Cannot build RFQ without {needed}")
            return

        progress = QProgressDialog("Preparing RFQ...   ", "Cancel", 0, 0, self)
        progress.setWindowTitle("ATEM")
        progress.setModal(True)
        progress.show()

        def finished():
            if not progress.wasCanceled():
                progress.canceled.disconnect()
            progress.close()
            rfq_df = self.worker.result
            error = self.worker.error
            self.worker = None
            self.thread.exit()
            if error:
                QMessageBox.information(self, "ATEM", "Build RFQ Error")
            elif rfq_df is not None:
                self.sgnUpdateRfq.emit(rfq_df)
            else:
                logger.warning("Build RFQ no exception error but no result")

        def canceled():
            self.worker.finished.disconnect()
            self.worker.stop()
            self.thread.exit()
            self.worker = None
            self.thread.wait()

        self.thread = QThread()
        self.worker = PrepareRfqWorker(bomData)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(finished)
        self.thread.start()

        progress.canceled.connect(canceled)
        progress.setAutoClose(True)
        progress.setWindowModality(Qt.WindowModality.WindowModal)
