# RFQ Logic Documentation

## Overview

This document describes the logic, relationships, and calculation methods between tables used in the Request for Quote (RFQ) process within the project database. It explains the purpose, required columns, and how each table interacts with others, providing clarity for ongoing development.

## Tables and Relationships

### 1. Clients (atem_clients)

Stores information about each client.

- **Primary Key**: id
- **Key fields**: client_name, contact_name, contact_email, contact_phone, address

### 2. Projects (atem_projects)

Represents individual projects associated with clients.

- **Primary Key**: id
- **Foreign Key**: client_id (links to atem_clients)
- **Key fields**: project_name, location, jobsite_location, bid_revision, engineering_drafter, ais_project_status

### 3. Client Profiles (atem_client_profiles)

Stores profiles that define calculation methods (lookup-based or factor-based) and standards per client project.

- **Primary Key**: id
- **Foreign Key**: ref_id (links to atem_clients)
- **Key fields**: client_name, profile_name, profile_description, usage_scope, equivalent_length_method (values: lookup, factor), area_method
- **Notes**: A trigger automatically populates client_name based on the ref_id

### 4. RFQ Input (atem_rfq_input)

Master reference table containing standardized and deduplicated descriptions and attributes for materials.

Ensures uniformity and data integrity across RFQ and BOM tables.

- **Primary Key**: id
- **Foreign Key**: project_id (links to atem_projects)
- **Key fields**: material_description, normalized_description, technical_standard, unit_of_measure, and various material-specific attributes.
- **Status flags**: deleted (removes from RFQ), ignore_item (excludes from aggregation)
- **Audit fields**: created_at, created_by, updated_at, updated_by

### 5. RFQ (atem_rfq)

Stores detailed RFQ entries for project-specific materials, using information from atem_rfq_input.

Contains calculated fields: calculated_eq_length (equivalent length), calculated_area (surface area).

- **Primary Key**: id
- **Foreign Keys**:
  - project_id (links to atem_projects)
  - profile_id (links to atem_client_profiles)
  - rfq_input_id (links to atem_rfq_input)
- **Key fields**: size (raw value), size1, size2 (standardized decimals for accurate aggregation), quantity, categorization fields.
- **Calculated fields**: calculated_eq_length, calculated_area
- **Status flags**: deleted, ignore_item
- **Audit fields**: created_at, created_by, updated_at, updated_by, validated_date, validated_by

### 6. BOM Component Mapping (atem_bom_component_mapping)

Defines component mapping relationships governing categorization of RFQ items.

- **Primary Key**: id
- **Foreign Keys**:
  - project_id (links to atem_projects)
  - profile_id (links to atem_client_profiles)
- **Key fields**: class_description, component_name, takeoff_category, general_category

### 7. Equivalent Length Factors (atem_equiv_length_factors)

Defines standard multiplier factors used when the profile method is set to 'factor' for equivalent length calculations.

- **Primary Key**: id
- **Foreign Key**: profile_id (links to atem_client_profiles)
- **Key fields**: component_type, eq_length_factor

### 8. Standard Fittings (standard_fittings)

Contains standardized fittings data for single-size components.

- **Primary Key**: id
- **Key fields**: client_id, client, profile, lookup_category, size1
- **Measurement fields**: length_ft, length_in, length_mm, area_ft, area_in, area_mm

### 9. Reducers (reducers)

Contains data for two-size fittings like reducers.

- **Primary Key**: id
- **Key fields**: client_id, client, profile, size1 (larger), size2 (smaller)
- **Measurement fields**: large_end dimensions, small_end dimensions, length and area in multiple units

### 10. Reducing Elbows (elbows_reducing)

Contains data for two-size reducing elbow fittings.

- **Primary Key**: id
- **Key fields**: client_id, client, profile, size1 (larger), size2 (smaller)
- **Measurement fields**: Similar to reducers

### 11. Reducing Tees (tee_reducing)

Contains data for three-size tee fittings.

- **Primary Key**: id
- **Key fields**: client_id, client, profile, size1, size2, size3
- **Measurement fields**: Dimensions, lengths, and areas in multiple units

### 12. Fittings Lookup View (vw_fittings_lookup)

Unified view combining all fittings tables (standard, reducers, elbows, tees) for simplified lookups.

- Standardizes output format across all fitting types
- Essential for the 'lookup' calculation method

## Workflow and Logic

### Step-by-Step Calculation Logic:

#### Step 1: Client and Project Setup

Clients are added to atem_clients. Projects linked to these clients are created in atem_projects.

#### Step 2: Define Calculation Methods in Client Profiles

In atem_client_profiles, specify the method for calculating equivalent length:

- **lookup**: Uses predefined values from a lookup table (vw_fittings_lookup).
- **factor**: Uses standardized multiplier factors (atem_equiv_length_factors).

#### Step 3: Populate RFQ Input

Materials and their attributes are standardized in atem_rfq_input.

#### Step 4: RFQ Entries and Real-time Calculations

Each RFQ entry (atem_rfq) references atem_rfq_input for consistent attribute assignment.

Automatic calculation trigger:

- If profile method is **lookup**:
  - Matches fitting_category, size1, and size2 with values in vw_fittings_lookup.
  - Assigns matched length_ft and area_ft to calculated_eq_length and calculated_area.

- If profile method is **factor**:
  - Multiplies quantity by corresponding eq_length_factor from atem_equiv_length_factors.
  - Assigns this product to calculated_eq_length. (Area calculation typically not applicable here.)

#### Step 5: Data Synchronization

Bidirectional sync between RFQ and RFQ_INPUT tables:
- Changes from RFQ to RFQ_INPUT via sync_rfq_to_input trigger
- Changes from RFQ_INPUT to RFQ via sync_input_to_rfq trigger
- Each trigger is designed to prevent infinite recursion

#### Step 6: BOM Component Mapping

Categorization and standardization via atem_bom_component_mapping for further aggregation and reporting.

## Key Functions and Triggers

### 1. calculate_pipe_surface_area

Calculates pipe surface area with support for multiple input and output units:
- Accepts diameter, length, and their respective units
- Returns area in square inches, square feet, square mm, and square meters
- Handles unit conversion internally

### 2. update_rfq_calculations

Trigger that automatically updates calculated_eq_length and calculated_area on RFQ inserts or updates:
- Determines calculation method from client profile
- Uses appropriate lookup or factor method
- Updates calculated fields before save

### 3. update_client_name

Trigger that automatically populates client_name in client_profiles based on ref_id:
- Ensures consistency between client tables
- Fires before insert or update of ref_id

### 4. sync_rfq_to_input / sync_input_to_rfq

Pair of triggers that maintain bidirectional synchronization:
- Create matching records in the other table when needed
- Update existing records with changed fields
- Include safeguards against infinite recursion

### Column Naming Standards

- **calculated_eq_length**: Clearly distinguishes calculated equivalent length (imperial/metric units).
- **calculated_area**: Clearly distinguishes calculated surface area.
- **length_ft/in/mm**: Standardized naming for length measurements in different units
- **area_ft/in/mm**: Standardized naming for area measurements in different units

These naming standards help avoid ambiguity between imperial and metric units and improve consistency across the application.

## Indexes and Performance Considerations

The database schema includes strategic indexes to optimize query performance:

- Indexes on foreign keys (project_id, client_id, profile_id)
- Indexes on frequently searched fields (material_description, component_name)
- Composite indexes on tables with multi-field lookups (client/profile combinations, size1/size2 pairs)

These indexes support efficient data retrieval for both transactional operations and reporting queries.