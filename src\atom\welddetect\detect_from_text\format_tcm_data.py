import pandas as pd
import re
import sys
import os

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))

# Import the elevation patterns from sourcepreprocess.py
try:
    from src.atom.sourcepreprocess import VALUE_PATTERNS
except ImportError:
    # Fallback if the import fails
    print("WARNING: Could not import VALUE_PATTERNS from sourcepreprocess.py. Using fallback patterns.")
    VALUE_PATTERNS = {
        "elevation": [
            # Pattern for "EL +157'10.1/4"" format
            r'EL\s+([+-]?\d+\'[\d\.]+(?:/\d+)?\")',
            # Pattern for "EL +102'" format (feet only)
            r'EL\s+([+-]?\d+\')',
            # Pattern for "EL +102.5" format (decimal)
            r'EL\s+([+-]?\d+\.?\d*)'
        ]
    }

def format_tcm_data(connections_df):
    """
    Format connection data for TCM format by extracting additional columns.

    Args:
        connections_df: DataFrame containing the connection data with columns
                       'connected_component', 'connection_details', 'location_info', 'bom_callouts'

    Returns:
        DataFrame with original columns plus additional TCM format columns
    """
    # Create a copy of the input DataFrame to avoid modifying the original
    result_df = connections_df.copy()

    # Initialize new columns with empty strings
    result_df['component_id'] = ''
    result_df['connection_point'] = ''
    result_df['connection_size'] = ''
    result_df['connection_units'] = ''
    result_df['connection_type'] = ''
    result_df['connection_rating'] = ''
    result_df['component_elevation'] = ''
    result_df['bom_flange_num'] = ''
    result_df['bom_gasket_num'] = ''
    result_df['bom_bolt_num'] = ''
    result_df['survey_coordinates'] = ''

    # Process each row
    for idx, row in result_df.iterrows():
        # Extract component_id and connection_point from connected_component
        if pd.notna(row['connected_component']) and row['connected_component']:
            connected_parts = row['connected_component'].split('/', 1)
            result_df.at[idx, 'component_id'] = connected_parts[0] if connected_parts else ''
            result_df.at[idx, 'connection_point'] = connected_parts[1] if len(connected_parts) > 1 else ''

        # Extract connection details
        if pd.notna(row['connection_details']) and row['connection_details']:
            conn_parts = row['connection_details'].split('/')

            # Extract size, units, type, rating
            if len(conn_parts) > 0:
                # Size is typically the first element
                result_df.at[idx, 'connection_size'] = conn_parts[0] if len(conn_parts) > 0 else ''

                # Units is typically the second element
                result_df.at[idx, 'connection_units'] = conn_parts[1] if len(conn_parts) > 1 else ''

                # Type is typically the third element
                result_df.at[idx, 'connection_type'] = conn_parts[2] if len(conn_parts) > 2 else ''

                # Rating is typically the fourth element
                result_df.at[idx, 'connection_rating'] = conn_parts[3] if len(conn_parts) > 3 else ''

        # Extract elevation from location_info
        if pd.notna(row['location_info']) and row['location_info']:
            # Look for elevation pattern using the comprehensive patterns from sourcepreprocess.py
            elevation_value = None

            # Try each pattern in the elevation patterns list
            for pattern in VALUE_PATTERNS["elevation"]:
                try:
                    # Extract the elevation using the pattern
                    match = re.search(pattern, row['location_info'])
                    if match:
                        # Get the full match and remove the "EL " prefix
                        full_match = match.group(0)
                        if full_match.startswith("EL "):
                            elevation_value = full_match[3:].strip()  # Remove "EL " and any leading/trailing spaces
                        elif full_match.startswith("Z "):
                            elevation_value = full_match[2:].strip()  # Handle "Z " prefix
                        else:
                            elevation_value = full_match  # No prefix to remove

                        print(f"Found elevation: '{elevation_value}' using pattern: '{pattern}'")
                        break
                except Exception as e:
                    print(f"Error with pattern {pattern}: {e}")
                    continue

            if elevation_value:
                result_df.at[idx, 'component_elevation'] = elevation_value

            # For debugging
            if 'EL' in row['location_info'] and not elevation_value:
                print(f"WARNING: Could not extract elevation from: {row['location_info']}")

            # Extract survey coordinates (E/W and N/S pairs)
            e_match = re.search(r'([EW]\s+\d+\'[\d\.]+(?:/\d+)?")', row['location_info'])
            n_match = re.search(r'([NS]\s+\d+\'[\d\.]+(?:/\d+)?")', row['location_info'])

            coordinates = []
            if e_match:
                coordinates.append(e_match.group(1))
            if n_match:
                coordinates.append(n_match.group(1))

            result_df.at[idx, 'survey_coordinates'] = '; '.join(coordinates) if coordinates else ''

        # Extract BOM callout numbers
        if pd.notna(row['bom_callouts']) and row['bom_callouts']:
            # Extract flange number
            f_match = re.search(r'F(\d+)', row['bom_callouts'])
            if f_match:
                result_df.at[idx, 'bom_flange_num'] = f_match.group(1)

            # Extract gasket number
            g_match = re.search(r'G(\d+)', row['bom_callouts'])
            if g_match:
                result_df.at[idx, 'bom_gasket_num'] = g_match.group(1)

            # Extract bolt number
            b_match = re.search(r'B(\d+)', row['bom_callouts'])
            if b_match:
                result_df.at[idx, 'bom_bolt_num'] = b_match.group(1)

    return result_df

def save_tcm_format(connections_df, output_path):
    """
    Process connection data and save in TCM format.

    Args:
        connections_df: DataFrame containing the connection data
        output_path: Path to save the formatted data

    Returns:
        Formatted DataFrame
    """
    # Format the data
    formatted_df = format_tcm_data(connections_df)

    # Save to Excel
    try:
        formatted_df.to_excel(output_path, index=False)
        print(f"TCM formatted data saved to {output_path}")
    except Exception as e:
        print(f"Error saving TCM formatted data: {e}")

    return formatted_df

# Example usage (commented out)
"""
if __name__ == "__main__":
    # Load connection data
    connections_path = "path/to/connections.xlsx"
    connections_df = pd.read_excel(connections_path)

    # Format and save in TCM format
    output_path = "path/to/tcm_formatted_connections.xlsx"
    formatted_df = save_tcm_format(connections_df, output_path)

    # Display a sample of the formatted data
    print(formatted_df.head())
"""
