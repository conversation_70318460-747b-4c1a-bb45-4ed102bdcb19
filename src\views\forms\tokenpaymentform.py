"""
Allow user to select PDF from file explorer for new projectg
"""
from PySide6.QtGui import QHideEvent
from PySide6.QtWidgets import (QPushButton, QSizePolicy, QWidget, QVBoxLayout, 
                               QHBoxLayout, QSpinBox, QLabel, QSpacerItem)
from PySide6.QtCore import Qt
from .baseform import BaseForm
from pubsub import pub

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 960

class TokenPaymentForm(BaseForm):

    def __init__(self, parent):
        self.tokenPrice = None
        super().__init__(parent)

    def initUi(self):
        self.formSize.setWidth(720)
        self.formSize.setHeight(480)

        self.title.setText("")
        self.subtitle.setText("Purchase Tokens")
        self.subtitle.setObjectName("titleLabel")
        self.subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.layout().addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding))

        vbox = QWidget()
        vbox.setLayout(QVBoxLayout())
        self.layout().addRow(vbox)

        self.lblPrice = QLabel("")
        self.lblPrice.setAlignment(Qt.AlignmentFlag.AlignCenter)
        vbox.layout().addWidget(self.lblPrice)

        hbox = QWidget()
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        hbox.setLayout(QHBoxLayout())

        hbox.layout().addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding))

        self.spinTokens = QSpinBox()
        self.spinTokens.setValue(5)
        self.spinTokens.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.spinTokens.setMaximum(99999)
        self.spinTokens.setMinimum(5)
        self.spinTokens.setValue(100)
        self.spinTokens.setSingleStep(10)
        self.spinTokens.setMinimumWidth(128)
        self.spinTokens.setMinimumHeight(96)
        self.spinTokens.valueChanged.connect(self.onSpinTokensChanged)
        hbox.layout().addWidget(self.spinTokens)

        hbox.layout().addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding))

        vbox.layout().addWidget(hbox)

        self.addStretchSpacer()
        self.pbProceed = QPushButton("Proceed")
        self.pbProceed.setFixedHeight(32)
        self.pbProceed.clicked.connect(self.onProceed)
        self.layout().addWidget(self.pbProceed)

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        self.setFloatingButtonCancel(True)
        self.updateUi()

    def initDefaults(self):
        return

    def onSpinTokensChanged(self, event):
        self.updateUi()

    def onFloatingButton(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)

    def updateUi(self):
        tokens = self.spinTokens.value()
        if self.tokenPrice is None:
            self.fetchTokenPrice()
            subtitle = f"Tokens: {tokens}"
        else:
            total = tokens * self.tokenPrice
            subtitle = f"Tokens: {tokens} = " + "${:.2f}".format(total)

        self.lblPrice.setText(subtitle)

    def onProceed(self):
        self.sgnSwitchTo[str, dict].emit("TokenCheckoutStatusForm", 
                                         {"tokens": self.spinTokens.value()})

    def fetchTokenPrice(self):

        def callback(res):
            try:
                self.tokenPrice = float(res["unit_amount"]) / 100
                print("Token price fetched")
                self.updateUi()
            except Exception as e:
                print("Failed to display ATEM token price")
        
        pub.sendMessage("atem-token-price-get", callback=callback)
        
    def showEvent(self, event):
        # if self.tokenPrice is None:
        self.fetchTokenPrice()
        return super().showEvent(event)