from math import isnan
import sys
import os
from typing import TYPE_CHECKING
from pathlib import Path
import pandas as pd
import numpy as np

if TYPE_CHECKING:  # This block is only for type checking tools like mypy.
    from PySide6.QtWidgets import QWidget

from PySide6.QtGui import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from functools import partial
from pathlib import Path

from src.app_paths import getSavedFieldMapJson, getSourceMetadataPath
from src.atom.fast_storage import load_df_fast
from src.atom.dbManager import DatabaseManager


def read_file(file: str) -> pd.DataFrame:
    """Reads either an xlsx or feather file"""
    if not file:
        raise ValueError("Need a value for file")
    if file.endswith(".xlsx"):
        return pd.read_excel(file)
    elif file.endswith(".feather"):
        return load_df_fast(file)


class DocumentMapWidget(QWidget):

    """Displays chosen document to map to"""

    def __init__(self,
                 parent=None):
        super().__init__(parent)

        self.setLayout(QGridLayout())
        self.layout().setColumnStretch(0, 1)
        self.layout().setColumnStretch(1, 1)
        self.layout().setAlignment(Qt.AlignLeft | Qt.AlignTop)

        self.addHeader()

        self.options = []

    def addHeader(self):
        self.layout().addWidget(QLabel("Source Names Found From ['sys_path', 'SYS PATH']"), 0, 0)
        self.layout().addWidget(QLabel("Map PDF Page To Existing Project Document"), 0, 1)

    def setNames(self, uniqueNames: list[str], mappedNames: list[tuple]):
        """Add mappable doc names to existing project source names"""

        existing = set()
        newOptions = []
        for name, le, options in self.options:
            # Only remove options if not in uniqueNames
            # So we can keep choices
            if name in uniqueNames:
                existing.add(name)
                newOptions.append((name, le, options))
                continue
            le.setParent(None)
            le.deleteLater()
            options.setParent(None)
            options.deleteLater()

        self.options = newOptions

        self.addHeader()

        row = len(newOptions) + 1
        for name in uniqueNames:
            if name in existing:
                continue
            try:
                if np.isnan(name):
                    le = QLineEdit(f"Missing 'sys_path' values [Recommeded - fill in missing sys_path values]")
            except Exception as e:
                le = QLineEdit(str(name))
            le.setReadOnly(True)
            options = QComboBox()
            options.setEditable(True)
            options.addItems([""] + list(mappedNames))
            completer = QCompleter(list(mappedNames), options)
            completer.setCaseSensitivity(Qt.CaseInsensitive)
            completer.setFilterMode(Qt.MatchContains)  # optional: match substring
            options.setCompleter(completer)
            for n in range(options.count()):
                options.setItemData(n, name, Qt.ItemDataRole.UserRole)
            self.options.append((name, le, options))
            self.layout().addWidget(le, row, 0)
            self.layout().addWidget(options, row, 1)
            row += 1

        print("Document Mapping state:", self.getState())

    def getState(self) -> dict:
        state = {}
        for _, _, options in self.options:
            if not isinstance(options, QComboBox):
                continue
            mapTo = options.currentText()
            mapFrom = options.currentData(Qt.ItemDataRole.UserRole)
            state[mapFrom] = mapTo
        return state


def isPreprocessed(projectId, filename) -> bool:
    metadata = getSourceMetadataPath(projectId, filename)
    # return os.path.exists(metadata)
    return os.path.exists(metadata)


class TableImporter(QWidget):

    BOM_GENERAL = 0
    RFQ = 1
    importActivated = Signal(dict)
    rfqImported = Signal(dict)

    def __init__(self,
                 parent=None,
                 projectId: int = None,
                 importType: int = None,
                 fields: list = []):
        super().__init__(parent)

        self.projectId: int = projectId
        self.setObjectName("popup")
        self.importType: str = importType
        self.fieldMap: dict = {}
        self.displayMap: dict[str, str] = {}
        self.loadFieldMap()
        self.validated: dict[str, pd.DataFrame] = {}
        self.projectSources = [s[1] for s in DatabaseManager().get_project_sources(self.projectId)]
        self.dataTypes = ["GENERAL", "BOM", "IFC", "SPOOL", "OUTLIER", "SPEC", "GENERIC_1", "GENERIC_2"]

        self.uniqueNames = set()

        self.setLayout(QVBoxLayout())

        self.lineEdits: dict[str, QLineEdit] = {}
        self.fieldMaps: dict[str, QComboBox] = {}
        self.dfs: dict[str, pd.DataFrame] = {}

        if importType is None:
            raise ValueError("importType must not be None.")

        def addHbox(tables: list = [str]):
            for label in tables:
                hbox = QWidget()
                hbox.setLayout(QHBoxLayout())
                hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
                self.layout().addWidget(hbox)

                hbox.layout().addWidget(QLabel("Data Profile:"))

                cbox = QComboBox(self)
                cbox.addItem("ATEM Field Map")
                self.fieldMaps[label] = cbox
                hbox.layout().addWidget(cbox)

                hbox.layout().addWidget(QLabel("File:"))

                self.lineEdits[label] = QLineEdit()
                self.lineEdits[label].setReadOnly(True)
                hbox.layout().addWidget(self.lineEdits[label])
                pbSelect = QPushButton()
                pbSelect.setMinimumHeight(32)
                pbSelect.setText(f"Select {label}")
                pbSelect.clicked.connect(partial(self.onSelectData, label))
                hbox.layout().addWidget(pbSelect)

                pbUnset = QPushButton()
                pbUnset.setMinimumHeight(32)
                pbUnset.setText(f"Unset")
                pbUnset.clicked.connect(partial(self.onRemoveData, label))
                hbox.layout().addWidget(pbUnset)

        if importType == TableImporter.BOM_GENERAL:
            addHbox(self.dataTypes)
        elif importType == TableImporter.RFQ:
            addHbox(["RFQ"])
        else:
            raise ValueError("importType invalid. Must be 0 for BOM_GENERAL, 1 for RFQ, 2 for IFC, 3 for GENERIC_1, 4 for GENERIC_2, 5 for SPOOL, 6 for OUTLIER, or 7 for SPEC")

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setMinimumHeight(48)
        self.layout().addWidget(hbox)
        lbl = QLabel("If changes have been made externally after selecting file(s), click recheck+validate compatible data")
        hbox.layout().addWidget(lbl)
        pbRecheck = QPushButton("Revalidate")
        pbRecheck.setMinimumHeight(48)
        hbox.layout().addWidget(pbRecheck)
        pbRecheck.clicked.connect(self.onRecheck)

        if self.importType == TableImporter.BOM_GENERAL:
            self.chkBypassValidation = QCheckBox("Bypass Validation + Directly import without remapping")
        else:
            self.chkBypassValidation = QCheckBox("Bypass Validation")
        self.chkBypassValidation.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        self.chkBypassValidation.setMinimumHeight(48)
        hbox.layout().addWidget(self.chkBypassValidation)

        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addWidget(splitter)

        self.sourceMap = DocumentMapWidget()
        self.sourceMap.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        splitter.addWidget(self.sourceMap)

        self.info = QTextEdit()
        self.info.setReadOnly(True)
        splitter.addWidget(self.info)

        if self.importType == TableImporter.BOM_GENERAL:
            self.pbImportAppend = QPushButton("Import (Append To Existing Tables)")
            self.pbImportAppend.clicked.connect(lambda: self.onImport(True))
            self.pbImportAppend.setMinimumHeight(48)
            self.layout().addWidget(self.pbImportAppend)

        self.pbImport = QPushButton("Import (Replace Tables)")
        self.pbImport.clicked.connect(self.onImport)
        self.pbImport.setMinimumHeight(48)
        self.layout().addWidget(self.pbImport)

        self.setMinimumWidth(800)

        self.setWindowTitle(f"Project Data Importer: Project ID = {self.projectId}")

        self.refreshInfo()

    @property
    def presetNames(self):
        return self._presetNames

    @presetNames.setter
    def presetNames(self, value: list):
        try:
            self._presetNames = value
        except Exception as e:
            self._presetNames = []

    def eventFilter(self, target, event):
        if event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key.Key_Escape:
                self.close()
                return True

        return super().eventFilter(target, event)

    def getSimpleFieldMap(self):
        fieldMap = getSavedFieldMapJson()
        simpleFieldMap = {}
        for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
            subData = fieldMap.get(key, {})
            # Discard unuseful data
            for _, v in subData.items():
                try:
                    del v["options"]
                except Exception as e:
                    pass
            simpleFieldMap.update(subData)
        return simpleFieldMap

    def closeEvent(self, event: QCloseEvent) -> None:
        return super().closeEvent(event)

    def onSelectData(self, table: str):
        """Open file picker to select data"""
        dialog = QFileDialog(self)
        dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        dialog.setWindowTitle(f"Select {table} data")
        dialog.setNameFilter(f"{table} Data File (*.xlsx *.feather)")
        dialog.setViewMode(QFileDialog.ViewMode.List)
        if dialog.exec():
            filenames = dialog.selectedFiles()
            if filenames:
                file = filenames[0]
                self.lineEdits[table].setText(file)
                self.refreshInfo()

    def refreshInfo(self):
        print("Refresh import info")

        # Check if general ids exist in project
        db = DatabaseManager()

        bypassValidation = self.chkBypassValidation.isChecked()

        self.validated = {}
        self.dfs = {}
        text = ""
        # if self.importType == TableImporter.BOM_GENERAL:

        for dataType, lineEdit in self.lineEdits.items():
            dataPath = lineEdit.text().lower()
            if not dataPath:
                text += f"No {dataType} file selected.\n"
                continue
            try:
                df = read_file(dataPath)
                self.dfs[dataType] = df
                text += f"[OK] {dataType} file provided\n"
            except Exception as e:
                text += f"Failed to read {dataType} file\n"
                text += f"Exception - {e}"

        text += "\n"

        if self.importType == TableImporter.BOM_GENERAL:
            uniqueDocNames = set()
            for dataType in self.dataTypes:
                self.dfs.setdefault(dataType, pd.DataFrame())

            for key, df in self.dfs.items():
                if df.empty:
                    text += f"Not importing {key}. Empty or imported file row count: {len(df)}\n"
                    continue

                text += f"Data type {key} row count: {len(df)}\n"

                # Substitute display names to internal field names
                dfColumns = df.columns.tolist()
                sub = {col: self.displayMap.get(col, col) for col in dfColumns}
                df = df.rename(columns=sub)

                if key.lower() == "bom":
                    requiredKeys = ["material_description", "pos", "pdf_page"]
                else:
                    requiredKeys = ["pdf_page"]

                missingKeys = [k for k in requiredKeys if k not in df]
                if missingKeys:
                    warning = [
                        f"{missingKeys} columns are not present in selected {key} data. {key} data cannot be imported",
                        "Is the correct data profile selected to map column name to database names? If not, select the data profile and revalidate"
                    ]
                    QMessageBox.critical(self, f"Warning: Invalid {key} data columns", "\n\n".join(warning))

                text += f"{key} columns: {df.columns.tolist()}\n"

                if "sys_path" not in df:
                    warning = [
                        f"{key}: Could not detect sources to map as 'sys_path' column not in {key} data.",
                        "This warning can be safely ignored if the {key} data belongs to a single source document",
                        "If the data belongs to a single source, this data can be mapped from 'All {key} Data group'.",
                    ]
                    QMessageBox.warning(self, f"Warning: sys_path not found in {key} data", "\n\n".join(warning))
                    df["sys_path"] = f"All {key} Data"
                    uniqueDocNames.add(f"All {key} Data")
                else:
                    if df["sys_path"].isnull().any():
                        QMessageBox.warning(self, f"{key} Missing Values in sys_path", f"Warning: sys_path found in {key} data but there are some missing values in this column")
                    for name in df["sys_path"].unique().tolist():
                        uniqueDocNames.update(df[df["sys_path"].notnull()]["sys_path"].unique().tolist())

                cleanup = ["sys_build", "sys_filename", "sysDocument", "sysDocument"]
                for name in cleanup:
                    try:
                        del df[name]
                    except Exception as e:
                        print(f"Error: {e}")
                        pass

                if not bypassValidation:
                    text += f"\n[NOTE] Finding IDs from {key} database\n\n"
                else:
                    text += f"\n[NOTE] Database validation bypassed - using file data directly\n\n"

                if key.lower() == "bom":
                    # Look for this block in the refreshInfo method where it checks for the 'ef' column
                    if 'ef' in df.columns:
                        print(df["ef"].dtype)
                    else:
                        print("Column 'ef' not found in DataFrame")
                        # Add the missing column with empty strings
                        df['ef'] = ""
                        print("Added 'ef' column to DataFrame")

                df = df.fillna("")
                self.validated[f"{key.lower()}_data"] = df

            self.sourceMap.setNames(uniqueDocNames, self.projectSources)
            text += f"\n"

        elif self.importType == TableImporter.RFQ:

            rfqDf = self.dfs.get("RFQ", pd.DataFrame())
            rfqColumns = rfqDf.columns.tolist()
            sub = {col: self.displayMap.get(col, col) for col in rfqColumns}
            rfqDf = rfqDf.rename(columns=sub)
            if not rfqDf.empty:
                self.validated["rfq_data"] = rfqDf

        self.info.setText(text)

    def onImport(self, append=False):
        """Update config with new column order"""
        mapState: dict = self.sourceMap.getState()
        warnings = []
        critical = []
        bypassValidation = self.chkBypassValidation.isChecked()

        # Check for any missing mappings
        db = DatabaseManager()

        no_source_caption = "data error - No project source is selected from dropdown to map pdf_page to pdf_id. This is required when pdf_id column is not present in import data"

        validated = {}
        for dataKey, df in self.validated.items():
            dataType = dataKey.split("_")[0].upper()
            if self.lineEdits.get(dataType) and self.lineEdits[dataType].text() and df.empty:
                warnings.append(f"{dataType} selected but cannot be imported")
                continue

            if not df.empty and bypassValidation:
                validated[dataKey] = df
                continue

            # Map pdf_page to pdf_id
            if dataType == "RFQ":
                print("TODO - RFQ input validation. Bypassing as of now")
                continue
            print(f"{dataType} - mapping pdf_page to pdf_id")
            unique = df["sys_path"].unique().tolist()

            # Map BOM id to pdf id
            for mapFrom, mapTo in mapState.items():
                print(f"Mapping {dataType} pdf_pages from {mapFrom} -> pdf_ids of {mapTo}")

                if not mapTo:
                    if "pdf_id" not in df:
                        critical.append(f"{dataType} {no_source_caption}")
                    continue
                if mapFrom not in unique:
                    continue

                if mapFrom.lower() == f"all {dataType} data".lower():
                    df["sys_path"] = mapTo
                    filtered_bom_df = df
                else:
                    filtered_bom_df = df[df["sys_path"] == mapFrom]

                max_page = df["pdf_page"].max()
                pageToId: dict = db.get_source_pdf_id_map(projectId=self.projectId, filename=mapTo)

                if not isPreprocessed(self.projectId, mapTo):
                    critical.append(f"Source {mapTo} has not been preprocessed. Preprocessing is required to fetch PDF IDs")
                    continue

                max_source_page = max(pageToId)

                missing = max_page > max_source_page
                if missing:
                    critical.append(f"{mapTo} - There are pdf_page numbers (max_page={max_page}) which are greater than the selected source page_count={max_source_page})")
                    critical.append(f"Check {mapFrom} is correctly mapped to {mapTo}")
                    continue

                for row in filtered_bom_df.itertuples():
                    pdf_id = pageToId.get(row.pdf_page, None)
                    if pdf_id is None:
                        continue
                    index = row.Index
                    df.loc[index, "pdf_id"] = pdf_id

                # Note - Update fields this should not impact database save value, as this comes from PDFStorage
                df[df["sys_path"] == mapFrom]["sys_path"] = mapTo

            if "pdf_id" in df:
                df["pdf_id"] = df["pdf_id"].astype(int)

        if critical:
            c = critical + warnings
            c = "\n".join(c)
            QMessageBox.critical(self, "Cannot Import", f"{c}")
            return

        if self.importType == TableImporter.BOM_GENERAL:
            message = [f"{dataType} rows to import: {len(df)}" for dataType, df in self.validated.items()] + ["\n"]
        elif self.importType == TableImporter.RFQ:
            message = [
                f"RFQ rows to import: {len(self.validated.get('rfq_data', pd.DataFrame()))}",
                "\n",
            ]
        message.extend(warnings)
        message = '\n\n'.join(message)

        if self.validated:
            data = {"status": "success", "results": self.validated, "tokens_used": 0, "message": "Data imported"}

            if self.importType == self.BOM_GENERAL:
                message += f"\n\nThis will append {list(self.validated.keys())} to any existing tables or create them if they are not visible. Proceed?"
            else:
                message += "\n\nThis will append data to any existing tables or create them if they are not visible. Proceed?"

            if QMessageBox.question(self, "Import", message) != QMessageBox.Yes:
                return

            if self.importType == self.RFQ:
                rfq_data = self.validated["rfq_data"]
                # rfq_data["ef"] = rfq_data["ef"].astype(float)
                # rfq_data["sf"] = rfq_data["sf"].astype(float)
                self.rfqImported.emit({"append": append, "rfq_data": rfq_data})
            else:
                # For all other import types (including BOM_GENERAL and the new types)
                # Use BOM_GENERAL import type for all tables other than RFQ
                if append:
                    data["append_tables"] = [
                        "bom_data",
                        "general_data",
                        "spool_data",
                        "spec_data",
                        "ifc_data",
                        "generic_1_data",
                        "generic_2_data",
                        "outlier_data"
                    ]
                self.importActivated.emit(data)

            self.close()
            return

        QMessageBox.information(self, "Import failed", "Cannot import invalid data")

    def onClose(self):
        self.close()

    def loadFieldMap(self):
        fieldMap = getSavedFieldMapJson()
        self.fieldMap = {}
        self.displayMap = {}
        for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
            subData = fieldMap.get(key, {})
            for internal, v in subData.items():
                self.displayMap[v["display"]] = internal
                try:
                    del v["options"]
                except Exception as e:
                    pass
            self.fieldMap.update(subData)

    def onRecheck(self):
        self.refreshInfo()
        QMessageBox.information(self, "Rechecked data", "Info updated")

    def onRemoveData(self, table):
        self.lineEdits[table].setText("")
        try:
            del self.dfs[table]
        except:
            pass
        self.refreshInfo()


if __name__ == "__main__":
    import sys
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    try:
        from src.theme import stylesheet
        app.setStyleSheet(stylesheet)
    except:
        pass
    # c = TableImporter(projectId=9, importType=TableImporter.BOM_GENERAL)
    c = TableImporter(projectId=9, importType=TableImporter.RFQ)
    c.show()
    app.exec()