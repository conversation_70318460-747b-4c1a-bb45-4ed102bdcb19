import cv2
import numpy as np
import math
from matplotlib import pyplot as plt
from src.utils.pdf.page_to_opencv import page_to_opencv
import fitz

filename = r"S:\Shared Folders\Client Uploads\Brock Services\S1601 Insulation Only (1).pdf"

doc = fitz.open(filename)
page = doc.load_page(0)

# === 1. Load and preprocess image ===
img = page_to_opencv(page, zoom=3)

# text = page.get_text("dict")
# for block in text["blocks"]:
#     if block["type"] == 0:  # text block
#         x0, y0, x1, y1 = map(int, block["bbox"])
#         cv2.rectangle(img, (x0, y0), (x1, y1), (255, 255, 255), -1)  # fill with white


gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
blurred = cv2.<PERSON><PERSON><PERSON><PERSON>lur(gray, (5, 5), 0)
edges = cv2.Canny(blurred, 50, 150)

# === 2. Detect lines ===
lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=50,
                        minLineLength=40, maxLineGap=5)

# === 3. Annotate and visualize lines ===
annotated_img = img.copy()
arrow_points = []

if lines is not None:
    for i, line in enumerate(lines):
        x1, y1, x2, y2 = line[0]

        # Compute angle of line
        dx, dy = x2 - x1, y2 - y1
        angle = math.degrees(math.atan2(dy, dx))

        # Draw arrow line
        cv2.arrowedLine(annotated_img, (x1, y1), (x2, y2),
                        (0, 0, 255), 2, tipLength=0.2)

        # Annotate with index and angle
        mid_x, mid_y = (x1 + x2) // 2, (y1 + y2) // 2
        cv2.putText(annotated_img, f"{i} ({int(angle)}°)",
                    (mid_x, mid_y), cv2.FONT_HERSHEY_SIMPLEX,
                    0.5, (255, 0, 0), 1)

        arrow_points.append(((x1, y1), (x2, y2), angle))

# === 4. Show annotated result ===
annotated_img_rgb = cv2.cvtColor(annotated_img, cv2.COLOR_BGR2RGB)
cv2.imwrite("debug/annotated_arrows.png", annotated_img_rgb)

# === 5. (Optional) Export to CSV ===
import pandas as pd
df = pd.DataFrame(arrow_points, columns=["start", "end", "angle"])
df.to_csv("debug/detected_arrows.csv", index=False)
