import os
import json
import pandas as pd


def plugin_generate_group_layout(input_file: str, save_file: str = "debug/group_layout.json"):
    df = pd.read_excel(input_file)
    groups = df.groupby("group")

    data = {
        "groups": {}
    }
    for group, group_df in groups:
        pages = group_df["pdf_page"].unique().tolist()
        print(f"Group {group} has pages {len(pages)}")

        data["groups"][group] = {
            "pages": [{"page": page} for page in pages],
            "rois": []
        }

    with open(save_file, 'w') as f:
        json.dump(data, f, indent=2)