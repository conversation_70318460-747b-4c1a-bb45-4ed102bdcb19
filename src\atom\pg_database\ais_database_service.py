"""
Database service for AIS PostgreSQL Interface

This module provides database operations for the AIS interface including
fetching clients, projects, profiles, and handling project operations.
"""

from typing import List, Dict, Optional, Any
import logging
from datetime import datetime

from .pg_connection import DatabaseConfig, execute_query, get_db_connection
import pandas as pd
import os

# Configure logging
logger = logging.getLogger(__name__)


class AISDatabase:
    """Database service class for AIS interface operations"""

    def __init__(self, db_config: Optional[DatabaseConfig] = None):
        """
        Initialize the AIS database service

        Args:
            db_config: Database configuration. If None, uses default configuration.
        """
        self.db_config = db_config or DatabaseConfig()

    def test_connection(self) -> bool:
        """
        Test the database connection

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            with get_db_connection(self.db_config) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    return result[0] == 1
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

    def get_clients(self) -> List[Dict[str, Any]]:
        """
        Fetch all clients from the database

        Returns:
            List of client dictionaries with id, client_name, contact_name, etc.
        """
        try:
            query = """
                SELECT id, client_name, contact_name, contact_email, contact_phone, address
                FROM public.atem_clients
                ORDER BY client_name
            """
            results = execute_query(query, config=self.db_config, fetch="all")
            return [dict(row) for row in results] if results else []
        except Exception as e:
            logger.error(f"Error fetching clients: {e}")
            return []

    def get_profiles(self, client_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch client profiles from the database

        Args:
            client_id: Optional client ID to filter profiles. If None, returns all profiles.

        Returns:
            List of profile dictionaries with id, client_name, profile_name, etc.
        """
        try:
            if client_id:
                query = """
                    SELECT cp.id, cp.ref_id, cp.client_name, cp.profile_name,
                           cp.profile_description, cp.usage_scope, cp.equivalent_length_method,
                           cp.area_method, cp.flange_calculation_method, cp.pipe_area_method,
                           cp.pipe_length_method, cp.pipe_minimum_size
                    FROM public.atem_client_profiles cp
                    JOIN public.atem_clients c ON cp.client_name = c.client_name
                    WHERE c.id = %s
                    ORDER BY cp.profile_name
                """
                params = (client_id,)
            else:
                query = """
                    SELECT id, ref_id, client_name, profile_name, profile_description,
                           usage_scope, equivalent_length_method, area_method,
                           flange_calculation_method, pipe_area_method, pipe_length_method,
                           pipe_minimum_size
                    FROM public.atem_client_profiles
                    ORDER BY client_name, profile_name
                """
                params = None

            results = execute_query(query, params=params, config=self.db_config, fetch="all")
            return [dict(row) for row in results] if results else []
        except Exception as e:
            logger.error(f"Error fetching profiles: {e}")
            return []

    def get_projects(self, client_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch projects from the database with client and profile information

        Args:
            client_id: Optional client ID to filter projects. If None, returns all projects.

        Returns:
            List of project dictionaries with full details including client and profile names
        """
        try:
            if client_id:
                query = """
                    SELECT p.id, p.client_id, p.project_name, p.location, p.jobsite_location,
                           p.bid_revision, p.bid_due_date, p.received_from_client,
                           p.engineering_drafter, p.ais_project_status, p.notes,
                           p.created_at, p.updated_at, p.profile_id,
                           c.client_name,
                           cp.profile_name
                    FROM public.atem_projects p
                    LEFT JOIN public.atem_clients c ON p.client_id = c.id
                    LEFT JOIN public.atem_client_profiles cp ON p.profile_id = cp.id
                    WHERE p.client_id = %s
                    ORDER BY p.created_at DESC
                """
                params = (client_id,)
            else:
                query = """
                    SELECT p.id, p.client_id, p.project_name, p.location, p.jobsite_location,
                           p.bid_revision, p.bid_due_date, p.received_from_client,
                           p.engineering_drafter, p.ais_project_status, p.notes,
                           p.created_at, p.updated_at, p.profile_id,
                           c.client_name,
                           cp.profile_name
                    FROM public.atem_projects p
                    LEFT JOIN public.atem_clients c ON p.client_id = c.id
                    LEFT JOIN public.atem_client_profiles cp ON p.profile_id = cp.id
                    ORDER BY p.created_at DESC
                """
                params = None

            results = execute_query(query, params=params, config=self.db_config, fetch="all")
            return [dict(row) for row in results] if results else []
        except Exception as e:
            logger.error(f"Error fetching projects: {e}")
            return []

    def fix_sequence(self, table_name: str, sequence_name: str, id_column: str = 'id') -> bool:
        """
        Fix a PostgreSQL sequence that's out of sync with the table data

        Args:
            table_name: Name of the table (e.g., 'public.atem_projects')
            sequence_name: Name of the sequence (e.g., 'atem_projects_id_seq')
            id_column: Name of the ID column (default: 'id')

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the maximum ID from the table
            max_id_query = f"SELECT COALESCE(MAX({id_column}), 0) as max_id FROM {table_name}"
            result = execute_query(max_id_query, config=self.db_config, fetch="one")
            max_id = result['max_id'] if result else 0

            # Set the sequence to the next value after the maximum ID
            next_val = max_id + 1
            fix_query = f"SELECT setval('{sequence_name}', {next_val}, false)"
            execute_query(fix_query, config=self.db_config, fetch="one")

            logger.info(f"Fixed sequence {sequence_name} - set to {next_val}")
            return True
        except Exception as e:
            logger.error(f"Error fixing sequence {sequence_name}: {e}")
            return False

    def create_project(self, project_data: Dict[str, Any]) -> Optional[int]:
        """
        Create a new project in the database

        Args:
            project_data: Dictionary containing project information

        Returns:
            The ID of the created project, or None if creation failed
        """
        try:
            query = """
                INSERT INTO public.atem_projects
                (client_id, project_name, location, jobsite_location, bid_revision,
                 engineering_drafter, ais_project_status, notes, profile_id)
                VALUES (%(client_id)s, %(project_name)s, %(location)s, %(jobsite_location)s,
                        %(bid_revision)s, %(engineering_drafter)s, %(ais_project_status)s,
                        %(notes)s, %(profile_id)s)
                RETURNING id
            """

            result = execute_query(query, params=project_data, config=self.db_config, fetch="one")
            if result:
                project_id = result['id']
                logger.info(f"Created new project with ID: {project_id}")
                return project_id
            return None
        except Exception as e:
            # If we get a duplicate key error, try to fix the sequence and retry once
            if "duplicate key value violates unique constraint" in str(e) and "pkey" in str(e):
                logger.warning("Sequence appears to be out of sync, attempting to fix...")
                if self.fix_sequence('public.atem_projects', 'atem_projects_id_seq'):
                    try:
                        # Retry the insert after fixing the sequence
                        result = execute_query(query, params=project_data, config=self.db_config, fetch="one")
                        if result:
                            project_id = result['id']
                            logger.info(f"Created new project with ID: {project_id} (after sequence fix)")
                            return project_id
                    except Exception as retry_e:
                        logger.error(f"Error creating project after sequence fix: {retry_e}")
                        return None

            logger.error(f"Error creating project: {e}")
            return None

    def get_project_by_id(self, project_id: int) -> Optional[Dict[str, Any]]:
        """
        Fetch a specific project by ID with full details

        Args:
            project_id: The project ID to fetch

        Returns:
            Project dictionary with full details, or None if not found
        """
        try:
            query = """
                SELECT p.id, p.client_id, p.project_name, p.location, p.jobsite_location,
                       p.bid_revision, p.bid_due_date, p.received_from_client,
                       p.engineering_drafter, p.ais_project_status, p.notes,
                       p.created_at, p.updated_at, p.profile_id,
                       c.client_name,
                       cp.profile_name
                FROM public.atem_projects p
                LEFT JOIN public.atem_clients c ON p.client_id = c.id
                LEFT JOIN public.atem_client_profiles cp ON p.profile_id = cp.id
                WHERE p.id = %s
            """

            result = execute_query(query, params=(project_id,), config=self.db_config, fetch="one")
            return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error fetching project {project_id}: {e}")
            return None

    def get_table_row_count(self, table_name: str, project_id: int = None) -> int:
        """
        Get the row count for a specific table, optionally filtered by project

        Args:
            table_name: The name of the table (e.g., 'public.general', 'public.bom')
            project_id: Optional project ID to filter by. If None, returns total count.

        Returns:
            Number of rows in the table, or 0 if error
        """
        try:
            if project_id is not None:
                query = f"SELECT COUNT(*) as row_count FROM {table_name} WHERE project_id = %s"
                result = execute_query(query, params=(project_id,), config=self.db_config, fetch="one")
            else:
                query = f"SELECT COUNT(*) as row_count FROM {table_name}"
                result = execute_query(query, config=self.db_config, fetch="one")
            return result['row_count'] if result else 0
        except Exception as e:
            logger.error(f"Error getting row count for {table_name} (project_id={project_id}): {e}")
            return 0

    def validate_data_file(self, file_path: str, table_name: str,
                          sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate a data file without importing it

        Args:
            file_path: Path to the file to validate
            table_name: Target table name (e.g., "public.bom")
            sheet_name: Optional sheet name for Excel files

        Returns:
            Dictionary with validation results
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "status": "error",
                    "message": "File not found",
                    "total_rows": 0,
                    "valid_rows": 0,
                    "errors": [{"row_index": "N/A", "error": "File not found"}]
                }

            # Read the Excel file to get basic info
            if sheet_name is None:
                df = pd.read_excel(file_path, sheet_name=0)
            else:
                df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Drop completely empty rows
            df = df.dropna(how='all')

            # Basic validation
            total_rows = len(df)
            valid_rows = len(df.dropna(subset=df.columns[:3]))  # Check first 3 columns for data

            return {
                "status": "success",
                "message": f"File validated successfully",
                "total_rows": total_rows,
                "valid_rows": valid_rows,
                "columns": list(df.columns),
                "errors": []
            }

        except Exception as e:
            logger.error(f"Error validating file {file_path}: {e}")
            return {
                "status": "error",
                "message": str(e),
                "total_rows": 0,
                "valid_rows": 0,
                "errors": [{"row_index": "N/A", "error": str(e)}]
            }

    def import_data_file(self, file_path: str, table_name: str,
                        client_id: Optional[int] = None,
                        profile_id: Optional[int] = None,
                        project_id: Optional[int] = None,
                        sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Import data from a file to the database

        Args:
            file_path: Path to the file to import
            table_name: Target table name (e.g., "public.bom")
            client_id: Optional client ID to add to the data
            profile_id: Optional profile ID to add to the data
            project_id: Optional project ID to add to the data
            sheet_name: Optional sheet name for Excel files

        Returns:
            Dictionary with import results
        """
        try:
            # First validate the file
            validation_result = self.validate_data_file(file_path, table_name, sheet_name)
            if validation_result["status"] == "error":
                return validation_result

            # Read the Excel file
            if sheet_name is None:
                df = pd.read_excel(file_path, sheet_name=0)
            else:
                df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Drop completely empty rows
            df = df.dropna(how='all')

            # Add required IDs if they don't already exist in the DataFrame
            if client_id is not None and 'client_id' not in df.columns:
                df['client_id'] = client_id
                logger.info(f"Added client_id = {client_id}")

            if profile_id is not None and 'profile_id' not in df.columns:
                df['profile_id'] = profile_id
                logger.info(f"Added profile_id = {profile_id}")

            if project_id is not None and 'project_id' not in df.columns:
                df['project_id'] = project_id
                logger.info(f"Added project_id = {project_id}")

            # For now, we'll implement a basic import using direct SQL
            # This is a simplified version - the full import logic would use the existing import functions
            return self._import_dataframe_direct(df, table_name)

        except Exception as e:
            logger.error(f"Error importing file {file_path}: {e}")
            return {
                "status": "error",
                "message": str(e),
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 1,
                "errors": [{"row_index": "N/A", "error": str(e)}]
            }

    def _import_dataframe_direct(self, df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
        """
        Direct import of a DataFrame to PostgreSQL table

        Args:
            df: DataFrame to import
            table_name: Target table name

        Returns:
            Dictionary with import results
        """
        try:
            total_rows = len(df)
            logger.info(f"Starting import of {total_rows} rows to {table_name}")

            # Use the existing import functions directly
            from .data_import.import_base_tables import (
                import_verified_material_classifications_direct,
                import_bom_direct,
                import_general_direct
            )

            # Call the appropriate import method based on table name
            if table_name == "public.verified_material_classifications":
                result = import_verified_material_classifications_direct(
                    dataframe=df,
                    db_config=self.db_config
                )
            elif table_name == "public.bom":
                result = import_bom_direct(
                    dataframe=df,
                    db_config=self.db_config
                )
            elif table_name == "public.general":
                result = import_general_direct(
                    dataframe=df,
                    db_config=self.db_config
                )
            else:
                # For other tables, use a generic import approach
                result = self._generic_table_import(df, table_name)

            if result and result.get('status') == 'success':
                return {
                    "status": "success",
                    "message": f"Successfully imported {total_rows} rows to {table_name}",
                    "total_rows": total_rows,
                    "valid_rows": total_rows,
                    "inserted": total_rows,
                    "updated": 0,
                    "error_rows": 0,
                    "errors": []
                }
            else:
                error_msg = result.get('message', 'Unknown error') if result else 'Import failed'
                return {
                    "status": "error",
                    "message": error_msg,
                    "total_rows": total_rows,
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": total_rows,
                    "errors": [{"row_index": "N/A", "error": error_msg}]
                }

        except Exception as e:
            logger.error(f"Error in direct import: {e}")
            return {
                "status": "error",
                "message": str(e),
                "total_rows": len(df) if df is not None else 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 1,
                "errors": [{"row_index": "N/A", "error": str(e)}]
            }

    def _generic_table_import(self, df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
        """
        Generic import method for tables not specifically handled

        Args:
            df: DataFrame to import
            table_name: Target table name

        Returns:
            Dictionary with import results
        """
        try:
            # For now, return an error for unsupported tables
            return {
                "status": "error",
                "message": f"Import for table {table_name} is not yet implemented",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": [{"row_index": "N/A", "error": f"Table {table_name} not supported"}]
            }
        except Exception as e:
            logger.error(f"Error in generic import: {e}")
            return {
                "status": "error",
                "message": str(e),
                "total_rows": len(df) if df is not None else 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 1,
                "errors": [{"row_index": "N/A", "error": str(e)}]
            }
