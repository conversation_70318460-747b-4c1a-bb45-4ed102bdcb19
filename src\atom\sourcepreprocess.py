"""
Worker - Source Preprocessing

For each page:

    Detect CV2 Regions
    Detect Missing Regions

Notes:
    Found (basic comparison) that apply_async on single pages performed same/better than on a function which handles chunks of pages
"""

import time
import uuid
import sys
import os
import re
import json
import fitz
import multiprocessing
import unicodedata
import asyncio
import statistics
import io
import math
import cv2

import numpy as np
import pandas as pd
from pubsub import pub
from PIL import Image
from multiprocessing import Manager, Event
from threading import Thread
from functools import partial

from src.atom.vision.detect_text_regions.detect_text_regions_cv2 import detect_text_regions
from src.atom.fast_storage import save_df_fast, clean_dataframe_for_pyarrow
# from src.atom.vision.ocr_patcher import find_missing_regions_vectorized, extract_raw_regions
from src.utils.logger import logger
from src.atom.dbManager import DatabaseManager
from src.app_paths import getSourceDataDir
from src.utils.spatial_utils import find_non_overlapping_rectangles

DEDUPLICATE_ANNOTATIONS = False # Keep the largest for each overlapping group
CLEAN_ANNOTATIONS = True # Substitue common encoded text with decoded text

def _getDataTempPath(filename):
    return filename

def _savePagePixmap(page):
    return page


try:
    from src.app_paths import savePagePixmap
    from src.app_paths import getDataTempPath
except:
    # Testing, if cannot access app_paths, just return the filename
    getDataTempPath = _getDataTempPath
    savePagePixmap = _savePagePixmap

# Increase the maximum number of columns displayed
pd.set_option('display.max_columns', None)

check_debug_log = {} # Test For debugging Check_update_row function (Line 1620 ish)


VALUE_PATTERNS = {
    "elevation": [

        # Pattern for "EL 100 '6"" format (with space before foot mark)
        "EL [-+]?\\d+ '\\d+\"",
        # Patterns with decimal fractions in specific formats
        # Pattern for "EL +184'4.3/16"" format (feet inches.fraction/denominator without space)
        "EL [-+]?\\d+'\\d+\\.\\d+/\\d+\"",

        # Pattern for "EL +72' 2.7/16"" format (feet space inches.fraction/denominator with quote)
        "EL [-+]?\\d+' \\d+\\.\\d+/\\d+\"",

        # Pattern for "EL +108' 6.9/16" format (feet space inches.fraction/denominator without quote)
        "EL [-+]?\\d+' \\d+\\.\\d+/\\d+",

        # Patterns with hyphens and spaces
        # Pattern for "EL +65'-3 3/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",

        # Pattern for "EL +43'-10 1/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",

        # Pattern for "EL +43'-10"" format (feet-inches)
        "EL [-+]?\\d+'[-]\\d+\"",

        # Patterns without hyphens
        # Pattern for "EL +185'3"" format (feet inches without space)
        "EL [-+]?\\d+'\\d+\"",

        # Feet-only patterns
        # Pattern for "EL +28'" format (feet only)
        "EL [-+]?\\d+'",

        # Decimal and integer formats
        "EL [-+]?\\d+\\.\\d+",  # EL +43.5
        "EL [-+]?\\d+",  # EL +43

        # Legacy patterns (keeping for compatibility)
        "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "Z [-+]?\\d+",
        "EL [-+]?\\d+'-\\d+( \\d+/\\d+)?\"",
        "EL [-+]?[0-9]+\\.?[0-9]*",

        # Patterns for "EL." format (with period and space)
        "EL\\. [-+]?\\d+'[-]\\d+\"",  # EL. +43'-10"
        "EL\\. [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",  # EL. +43'-10 1/8"
        "EL\\. [-+]?\\d+'\\d+\"",  # EL. +185'3"
        "EL\\. [-+]?\\d+'",  # EL. +28'
        "EL\\. [-+]?\\d+\\.\\d+",  # EL. +43.5
        "EL\\. [-+]?\\d+",  # EL. +43

        # Patterns for "EL." format (with period but NO space)
        "EL\\.[-+]?\\d+'[-]\\d+\"",  # EL.+43'-10"
        "EL\\.[-+]?\\d+'[-]\\d+ \\d+/\\d+\"",  # EL.+43'-10 1/8"
        "EL\\.[-+]?\\d+'\\d+\"",  # EL.+185'3"
        "EL\\.[-+]?\\d+'",  # EL.+28'
        "EL\\.[-+]?\\d+\\.\\d+",  # EL.+43.5
        "EL\\.[-+]?\\d+"  # EL.+43
    ],
    "x_coordinate": [
        "X [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
        "X [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "X [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\"",
        "[X|Y] [-+]?(\\d+' ?)?\\d+\\.\\d+/\\d+\\\"",
        "[X|Y] [-+]?(\\d+\\.?\\d*/?\\d+)"
    ],
    "y_coordinate": [
        "Y [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
        "Y [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "Y [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\""
    ]
}

def visualize_regions(cv_image, regions, color=(0, 255, 0)):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    vis_img = cv_image.copy()

    # Draw text regions with alternating colors
    for i, region in enumerate(regions):
        x = int(region["x0"])
        y = int(region["y0"])
        w = int(region["x1"]- region["x0"])
        h = int(region["y1"]- region["y0"])
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        # cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.2, color, 1)

    return vis_img

def extract_coordinates(record):
    if not record:
        return None
    record = record.replace("(", "")
    record = record.replace(")", "")
    coordinates = tuple(map(float, record.split(', ')))
    return coordinates

# Multiprocessing lock
lock = multiprocessing.Lock()

COMMIT_PDF: bool = True
COMMIT_RAW_DATA: bool = COMMIT_PDF and True # Require COMMIT_PDF to be True

# Commit the raw data in chunks if set to True.
# If False, commit only full results at the end.
COMMIT_CHUNKS: bool = True
COMMIT_PAGE_CHUNK_SIZE: int = 5  # Number of results to

# For performance boost, speed up using multiprocessing
MULTIPROCESS: bool = True
# Debug MODE
DEBUG_MODE: bool = False

def distance_to(p1, p2):
    # Gives the absolute distance between two points
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])


def page_to_opencv(page: fitz.Page, zoom=None, dpi=None):
    """Return open CV image from PyMuPDF page"""
    if zoom is None:
        zoom = 1
    if dpi:
        zoom = dpi / 72
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.

    Note: same function as calculate_angle but supply two co-ords
    instead of line
    """
    x1, y1 = p1
    x2, y2 = p2
    dx = x2 - x1
    dy = y2 - y1
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def normalize_angle(angle):
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]
    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)

def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

# Function to replace non-ASCII characters using normalization
def replace_non_ascii(text):
    # Normalize the text to NFKD (Normalization Form KD)
    normalized_text = unicodedata.normalize('NFKD', text)
    # Encode to ASCII and ignore non-ASCII characters
    ascii_text = normalized_text.encode('ascii', 'ignore').decode('ascii')
    return ascii_text

def update_value_type(value, patterns=VALUE_PATTERNS):
    for category, category_patterns in patterns.items():
        for pattern in category_patterns:
            if category == 'elevation':
                match = re.search(pattern, value)
                if match:
                    return category, match.group(0)
            else:
                if re.match(pattern, value):
                    return category, value
    return '', ''

def get_bbox_polygon(bbox, angle_degrees):

    # Convert angle from degrees to radians
    angle_radians = math.radians(angle_degrees)

    # Unpack the bounding box coordinates
    x1, y1, x2, y2 = bbox

    # Calculate the center of the bounding box
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2

    # Function to rotate a point (x, y) around the origin by the angle
    def rotate_point(x, y, center_x, center_y, angle_radians):
        # Translate point to the origin (center of rotation)
        x -= center_x
        y -= center_y

        # Apply the rotation formula
        x_new = x * math.cos(angle_radians) - y * math.sin(angle_radians)
        y_new = x * math.sin(angle_radians) + y * math.cos(angle_radians)

        # Translate point back
        x_new += center_x
        y_new += center_y

        return x_new, y_new

    # Rotate all four corners of the bounding box
    corners = [
        (x1, y1),
        (x2, y1),
        (x2, y2),
        (x1, y2),
    ]

    rotated_corners = [rotate_point(x, y, center_x, center_y, angle_radians) for x, y in corners]
    # Get the new bounding box by finding the min/max of the rotated corner coordinates
    return rotated_corners

def get_page_text(page: fitz.Page, rotation_matrix) -> dict:
    """
    Extract text with PyMuPDF

    Params:
        - page: The page object from PyMuPDF

    Returns:
        - PyMuPDF text blocks with adjusted bounding boxes and word values
    """
    try:
        # Extract text with a single call
        text_dict = page.get_text("rawdict", flags=fitz.TEXTFLAGS_TEXT, sort=True)
        if not text_dict or "blocks" not in text_dict:
            return {}

        # Filter to only text blocks (type 0) early
        text_blocks = [block for block in text_dict["blocks"] if block["type"] == 0]

        for block in text_blocks:

            block['bbox'] = adjust_bbox(block['bbox'], rotation_matrix)

            for line in block["lines"]:
                line['bbox'] = adjust_bbox(line['bbox'], rotation_matrix)

                # Calculate angle if direction is available
                angle = None
                if 'dir' in line:
                    direction = line['dir']
                    angle_radians = math.atan2(direction[1], direction[0])
                    angle = normalize_angle(math.degrees(angle_radians))
                    line['angle'] = angle

                for span in line["spans"]:
                    original_bbox = span['bbox']
                    span['bbox'] = adjust_bbox(span['bbox'], rotation_matrix)

                    origin_x, origin_y = span['origin']
                    origin_point = fitz.Point(origin_x, origin_y) * rotation_matrix
                    span['origin'] = (origin_point.x, origin_point.y)

                    # Process individual characters to form words
                    words = []
                    current_word = ""
                    current_word_bbox = None

                    for char in span['chars']:
                        char['bbox'] = adjust_bbox(char['bbox'], rotation_matrix)

                        if not current_word_bbox:
                            current_word_bbox = list(char['bbox'])

                        if char['c'] == " " or char['c'] == "\n":
                            if current_word:
                                words.append({
                                    'text': current_word,
                                    'bbox': current_word_bbox,
                                    'origin': (current_word_bbox[0], current_word_bbox[1])
                                })
                                current_word = ""
                                current_word_bbox = None
                        else:
                            current_word += char['c']
                            current_word_bbox[2] = max(current_word_bbox[2], char['bbox'][2])
                            current_word_bbox[3] = max(current_word_bbox[3], char['bbox'][3])

                    # Add the last word if it exists
                    if current_word:
                        words.append({
                            'text': current_word,
                            'bbox': current_word_bbox,
                            'origin': (current_word_bbox[0], current_word_bbox[1])
                        })

                    if line.get("angle") is None and "matrix" in span:
                        span_matrix = fitz.Matrix(*span["matrix"])  # unpack matrix
                        angle = span_matrix.rotation  # rotation in degrees

                    # -+90 bboxes might not need rotation
                    if angle is not None and abs(angle) not in [0, 90, 180, 270, 360]:
                        angle = normalize_angle(angle)
                        span['polygon'] = get_bbox_polygon(original_bbox, angle)

                    span['angle'] = angle
                    span['words'] = words
                    span['text'] = replace_non_ascii(" ".join(word['text'] for word in words))

        return text_blocks

    except Exception as e:
        logger.error(f"Error processing page text blocks: {e}", exc_info=True)
        return {}

def normalize_text(text):
    # Replace all whitespace with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove zero-width spaces and similar invisible characters
    text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)

    # Using regex to remove leading and trailing whitespace
    text = re.sub(r'^\s+|\s+$', '', text)

    # Strip leading/trailing whitespace
    return text.strip()

def process_text_rois(pdf_id: int, rotation: int, text_blocks: dict, document: fitz.Document, page_number: int, pdf_path, filename, parent_folders_str):
    """
    Extract text along with its bounding box (coordinates)
    Coordinate (bbox format):(x1, y1, x2, y2)
    Where x0, y0 is top-left corner of rectangle bounding box
    and x1, y1 are the coordinates of the lower-right corner of the rectangle bounding the text.
    """
    data = []
    img_data = []

    def int_to_rgb(color_int): # format pymupdf font color
        blue = color_int & 255
        green = (color_int >> 8) & 255
        red = (color_int >> 16) & 255
        return (red, green, blue)


    logger.debug(f"Extracting TEXT: {page_number}")
    for block in text_blocks:
        # 0 for text block, 1 for image block
        if isinstance(block, dict) and block.get("type") == 0:  # Text block
            try:
                bbox1 = block["bbox"]

                for line in block["lines"]:
                    for span in line["spans"]:

                        words_data = []

                        for word in span.get("words", []):
                            words_data.append({
                            "word": word.get("text", ""),
                            "bbox": word.get("bbox", [])
                            #"origin": word.get("origin", ())
                            })

                        #text = span.get("text", "").strip()
                        text = span.get("text", "")
                        if text:

                            # Using regex to remove leading and trailing whitespace
                            #text = re.sub(r'^\s+|\s+$', '', text)

                            # Using regex to remove leading and trailing whitespace
                            text = normalize_text(text) #re.sub(r'^\s+|\s+$', '', text)

                            # Get Font Info and Flags
                            font = span.get("font", "Unknown")  # Get the font name
                            font_size = span.get("size", 0)    # Get the font size
                            flags = span.get("flags", 0)       # Get the text flags
                            font_color_int = span.get("color", 0) # Default to 0 (Black)

                            # Convert color to RGB and hex formats
                            font_color_rgb = int_to_rgb(font_color_int)
                            font_color_hex = "#{:02x}{:02x}{:02x}".format(*font_color_rgb)


                        bbox2 = span.get("bbox", (0, 0, 0, 0))  # Use the bbox of the span for individual coordinates
                        dynamic_bbox = bbox2
                        angle = span.get("angle", 0)

                        # Add text to data only if it's not empty
                        if text:
                            # continue

                            #Evaluate value with regex
                            category, extracted_value = update_value_type(text, VALUE_PATTERNS)

                            data.append({
                                'sys_build': parent_folders_str,
                                'sys_path': pdf_path,
                                'sys_filename': filename,
                                'pdf_id': pdf_id,
                                'pdf_page': page_number + 1,
                                'sys_layout_valid': True,
                                'type': 'Text',
                                'category': category,
                                'value': text, #text.strip(),
                                'elevation': extracted_value if category == 'elevation' else '',
                                'x_position': extracted_value if category == 'x_coordinate' else '',
                                'y_position': extracted_value if category == 'y_coordinate' else '',
                                'coordinates': bbox1, #The main bounding box
                                'coordinates2': dynamic_bbox, #bbox2 normalized_bbox2 #Precise Bounding Box for individual value
                                'words': json.dumps(words_data),  # Store as JSON string for DataFrame compatibility
                                'name': '',
                                'title': '',
                                'created_date': '',
                                'mod_date': '',
                                'id_annot_info': '',
                                # 'annot_'
                                'page_rotation': rotation,
                                'color':font_color_rgb,
                                'annot_type':'',
                                'annot_subtype':'',
                                'vertices':'',
                                'endpoint':'',
                                'stroke_color':'',
                                'font': font,
                                'font_style':'',
                                'font_size': font_size,
                                'flags': flags,
                                'angle': angle,
                                'polygon': span.get('polygon', None),
                            })

            except Exception as e:
                logger.error(f"Error processing text block on page {page_number}: {e}")


        #Handle Images
        elif block["type"] == 1:  # Check if block is an image
            # --> Process Image Data Types
            img_data = process_image_type_rois(pdf_id, document, page_number, block, parent_folders_str, pdf_path, filename)

    return data, img_data

def process_image_type_rois(pdfID, document, page_number, block, parent_folders_str, pdf_path, filename):

    img_data = {}
    try:
        image_bbox = block["bbox"]
        xref = block["image"]  # xref number of the image

        # Ensure xref is an integer
        if not isinstance(xref, int):
            return {}

        # Extract and save the image
        try:
            image_info = document.extract_image(xref)
            if image_info is not None:
                image_bytes = image_info["image"]
                image_filename = f"image_{page_number}_{xref}.png"
                image_filepath = os.path.join(r"C:\Users\<USER>\AIS_work_dir\Test Integration\Test1", image_filename) ###Fix this Absolute Path

                with open(image_filepath, "wb") as img_file:
                    img_file.write(image_bytes)

                # Append image information to data
                img_data = {
                    'sys_build': parent_folders_str,
                    'sys_path': pdf_path,
                    'sys_filename': filename,
                    'pdf_id': pdfID,
                    'pdf_page': page_number + 1,
                    'type': 'Image',
                    'image_xref': xref,
                    'coordinates': image_bbox,
                    'image_path': image_filepath
                }
            #logger.debug(f">>> Processed Text Type (1)(Images): {page_number}")
        except Exception as e:
            logger.error(f"Error extracting image at xref: {e}", exc_info=True) #{xref}: {e}")
    except Exception as e:
        logger.error(f"Error processing image block on page {page_number}: {e}")

    return img_data

def clean_annotation_text(text):
    """
    Clean annotation text from PDF encoding artifacts

    - Handle escaped percent signs (%%% → %)
    - Preserve legitimate double percent signs when appropriate
    - Maintain other text as is

    Example `5%%% RT` becomes `5% RT`
    """
    if not text:
        return ""

    # Replace triple percent with single percent (common AutoCAD artifact)
    text = re.sub(r'%%%', '%', text)

    # Only replace double percent when it's likely an escape sequence
    # This is more conservative than replacing all instances
    text = re.sub(r'%%(?=[^%]|$)', '%', text)

    return text.strip()

def is_annotation_visible(annot):
    """Comprehensive check if annotation is visible on screen"""
    # Check standard hidden flag
    if annot.flags & 2:  # Hidden flag
        return False

    # Check NoView flag
    if annot.flags & 32:  # NoView flag
        return False

    # Check dimensions
    rect = annot.rect
    if rect.width <= 0 or rect.height <= 0:
        return False

    # Check opacity
    if hasattr(annot, 'opacity') and annot.opacity == 0:
        return False

    # Check colors for visibility
    if annot.colors:
        stroke = annot.colors.get('stroke')
        fill = annot.colors.get('fill')

        # If both colors are None or have zero alpha
        if ((not stroke or (len(stroke) == 4 and stroke[3] <= 0)) and
            (not fill or (len(fill) == 4 and fill[3] <= 0))):
            return False

    return True

def deduplicate_annotations(annotations_data):
    """
    Deduplicate annotations by grouping those with identical content and spatial overlap,
    then keeping only the largest annotation from each group.
    """
    if not annotations_data:
        return []

    # Remove debug prints
    # print("Total annotations before deduplication:", len(annotations_data))

    # First pass: Group by content
    content_groups = {}
    for idx, annot in enumerate(annotations_data):
        content = annot.get('value', '').strip()
        if content:
            if content not in content_groups:
                content_groups[content] = []
            content_groups[content].append((idx, annot))

    # Second pass: For each content group, create spatial overlap groups
    to_remove = set()
    for content, annots in content_groups.items():
        if len(annots) <= 1:
            continue

        # Create overlap groups
        overlap_groups = []
        processed = set()

        for i, (idx1, annot1) in enumerate(annots):
            if idx1 in processed:
                continue

            # Start a new group with this annotation
            current_group = [(idx1, annot1)]
            processed.add(idx1)

            # Find all other annotations that overlap with this one
            rect1 = [annot1['x0'], annot1['y0'], annot1['x1'], annot1['y1']]

            # Check against all other annotations with the same content
            for j, (idx2, annot2) in enumerate(annots):
                if idx2 in processed:
                    continue

                rect2 = [annot2['x0'], annot2['y0'], annot2['x1'], annot2['y1']]

                # If they overlap, add to current group
                if rectangles_overlap(rect1, rect2):
                    current_group.append((idx2, annot2))
                    processed.add(idx2)

            # Add the group if it has at least one annotation
            if current_group:
                overlap_groups.append(current_group)

        # For each overlap group, keep only the largest annotation
        for group in overlap_groups:
            if len(group) <= 1:
                continue

            # Sort by area (largest first)
            group.sort(key=lambda x: (x[1]['x1'] - x[1]['x0']) * (x[1]['y1'] - x[1]['y0']), reverse=True)

            # Mark all but the largest for removal
            for idx, _ in group[1:]:
                to_remove.add(idx)

    # Filter out duplicates
    result = [annot for idx, annot in enumerate(annotations_data) if idx not in to_remove]
    # print("Total annotations after deduplication:", len(result))
    return result

def rectangles_overlap(rect1, rect2):
    """
    Check if two rectangles overlap
    Each rectangle is [x0, y0, x1, y1]
    """
    # If one rectangle is to the left of the other
    if rect1[2] < rect2[0] or rect2[2] < rect1[0]:
        return False

    # If one rectangle is above the other
    if rect1[3] < rect2[1] or rect2[3] < rect1[1]:
        return False

    # Rectangles overlap
    return True

def process_annot_rois(pdf_id: int, doc: fitz.Document, page: fitz.Page, page_number: int, pdf_path: str, filename: str, parent_folders_str: str):
    data = []

    try:
        annotations = page.annots()
        if not annotations:
            return []

        for n, annot in enumerate(annotations):
            color, font_name, font_size, font_style = '', '', '', ''  # Set to blank initially
            annot_info = annot.info
            annot_rect = annot.rect
            value = annot_info.get('content', '').strip()

            value = clean_annotation_text(value)
            annot_flags = annot.flags
            angle = annot.rotation

            if "dir" in annot.info:
                try:
                    direction = annot.info["dir"]
                    angle_radians = math.atan2(direction[1], direction[0])
                    angle = math.degrees(angle_radians)
                    angle = normalize_angle(angle)
                except Exception as e:
                    logger.warning(f"Error processing direction: {e}")

            try:
                xref_obj = doc.xref_object(annot.xref)
                if '/DA' in xref_obj:
                    da_string = xref_obj.split('/DA (')[1].split(')')[0]

                    # Parse the DA string
                    parts = da_string.split()
                    color = [float(parts[0]), float(parts[1]), float(parts[2])]
                    font_name = parts[4].lstrip('/')  # Remove leading '/' if present
                    font_size = float(parts[5])

                    # Extract font style from DS if available
                    if '/DS' in xref_obj:
                        ds_string = xref_obj.split('/DS (')[1].split(')')[0]
                        font_style = 'normal'
                        if 'bold' in ds_string.lower():
                            font_style = 'bold'
                        elif 'italic' in ds_string.lower():
                            font_style = 'italic'
                        elif 'bold' in ds_string.lower() and 'italic' in ds_string.lower():
                            font_style = 'bold-italic'
                    else:
                        font_style = 'normal'
            except Exception as e:
                print(f"Error extracting font info: {e}")

            # Get the annotation color
            annot_color = annot.colors['stroke'] if annot.colors else None

            # Get the font color (fill color)
            font_color = annot.colors['fill'] if annot.colors and 'fill' in annot.colors else None
            font_color = font_color if not font_color else font_color

            # Get annotation type and subtype
            annot_type = annot.type[0]  # Main type
            annot_subtype = annot.type[1] if len(annot.type) > 1 else None  # Subtype if available

            # Get vertices if available
            vertices = annot.vertices if hasattr(annot, 'vertices') else None
            endpoint = vertices[-1] if vertices else None

            #Evaluate value with regex
            category, extracted_value = update_value_type(value, VALUE_PATTERNS)
            #elevation, category = update_if_elevation(value, elevation_patterns)

            # Adjust the BBOX
            adjusted_bbox = adjust_bbox(annot_rect, page.rotation_matrix)

            data.append({
                'sys_build': parent_folders_str,
                'sys_path': pdf_path,
                'sys_filename': filename,
                'pdf_id': pdf_id,
                'pdf_page': page_number + 1,
                'sys_layout_valid': None,
                'type': annot_info.get('subject', ''),
                'annot_type': annot_type,
                'annot_subtype': annot_subtype,
                'category': category,
                'value': value,
                'elevation': extracted_value if category == 'elevation' else '',
                'x_position': extracted_value if category == 'x_coordinate' else '',
                'y_position': extracted_value if category == 'y_coordinate' else '',
                'coordinates': adjusted_bbox,
                'coordinates2': adjusted_bbox,
                'x0': adjusted_bbox[0],
                'y0': adjusted_bbox[1],
                'x1': adjusted_bbox[2],
                'y1': adjusted_bbox[3],
                'vertices': vertices,
                'endpoint': endpoint,
                'name': annot_info.get('name', ''),
                'title': annot_info.get('title', ''),
                'mod_date': annot_info.get('creationDate', ''),
                'modDate': annot_info.get('creationDate', ''),
                'id_annot_info': annot_info.get('id', ''),
                'page_rotation': page.rotation,
                'stroke_color': annot_color,  # Annotation color (stroke color)
                'font_color': font_color,  # Font color (fill color)
                'color': color,
                'font': font_name,
                'font_style': font_style,
                'font_size': font_size,
                'flags': annot_flags,
                'angle': angle,
                'polygon': None,
            })

        #logger.debug(f">>> Processed Annotations Data: {page_number}")
    except Exception as e:
        logger.error(f"Error processing annotations on page {page_number}: {e}")
    #print(f"\n\nTEXT ANNOT DATA", data)

    # Deduplicate annotations before returning
    if DEDUPLICATE_ANNOTATIONS:
        data = deduplicate_annotations(data)

    return data

def commit_pdf_page(project_id: int, page: fitz.Page, pdf_path: str, page_number: int, page_valid: bool) -> int:
    """Commit page to database. Retrieve id from insert and save local pixmap of page"""

    if not COMMIT_PDF or project_id is None:
        pdf_id = page_number + 1
        return pdf_id

    db_manager = DatabaseManager() # Connect to database
    document_name = None
    pdfId = None  # Ensure pdfID has a default value
    doc_skip = 0 if page_valid else 1
    try:
        page_width, page_height = page.rect.width, page.rect.height
        doc_size = ",".join([str(page_width), str(page_height)])
        pdf_bytes = None # No longer commit page blob
        pdfId = db_manager.insert_pdf_page_to_storage(project_id,
                                                      pdf_path,
                                                      page_number + 1,
                                                      "Isometric",
                                                      document_name,
                                                      pdf_bytes,
                                                      doc_size,
                                                      doc_skip)
    except Exception as e:
        logger.critical(f"Error committing pdf page ({page_number+1}) to database. Page will be skipped in subsequent processes: {e}", exc_info=True)

    try:
        zoom = 2  # 144 / 2 . Default DPI in PyMuPDF is 72
        mat = fitz.Matrix(zoom, zoom)
        pixmap: fitz.Pixmap = page.get_pixmap(matrix=mat)
        savePagePixmap(pixmap, project_id, pdf_path, page_number + 1)
        document_name = f"{str(uuid.uuid4())}.pdf"  # Unique name for the stored PDF page
    except Exception as e:
        logger.warning(f"Error saving page pixmap", exc_info=True)

    return pdfId

def detect_page_regions(page: fitz.Page, page_num: int, dpi: int = 72, return_rectangles: bool = False):
    """
    Detect text regions for a single page. This converts back to the original coords after scaling
    with DPI value

    Args:
        page: PyMuPDF page object
        page_num: Page number
        dpi: DPI value for processing
        return_rectangles: If True, also return a list of rectangles in [x0, y0, x1, y1] format

    Returns:
        DataFrame with text regions, or tuple of (DataFrame, list of rectangles) if return_rectangles=True
    """
    cv_image = page_to_opencv(page, dpi=dpi)
    text_regions = detect_text_regions(cv_image)

    # Convert to DataFrame and add necessary columns
    df = pd.DataFrame(text_regions, columns=["x0", "y0", "width", "height"])
    df["x1"] = df["x0"] + df["width"]
    df["y1"] = df["y0"] + df["height"]
    df['pdf_page'] = page_num

    # Map this back to original scale
    scale_factor = 72 / dpi
    if scale_factor != 1:
        coord_columns = ['x0', 'y0', 'x1', 'y1', 'width', 'height']
        df_converted = df.copy()
        df_converted[coord_columns] = df_converted[coord_columns] * scale_factor
        df = df_converted

    # If requested, create and return rectangles list
    if return_rectangles:
        # Most efficient way to create rectangles list directly from numpy arrays
        # This avoids Python loops and is much faster for large datasets
        rectangles = df[['x0', 'y0', 'x1', 'y1']].values.tolist()
        return rectangles

    return df

def process_page(project_id: int,
                 doc: fitz.Document,
                 pdf_path: str,
                 page_num: int,
                 process_times: list,
                 cancel_event: asyncio.Event):
    """
    Core logic of page preprocessing

    Params:
        project_id: Project id
        doc: Fitz Document
        pdf_path: filename of pdf
        page_num: zero-indexed Page number
        process_times: Array to keep record of time taken to process page

    """

    t1_start = time.perf_counter()

    def check_cancelled():
        # Raising exception helps to break out of starmap
        # more immediately
        if cancel_event.is_set():
            raise CancelException

    check_cancelled()

    page: fitz.Page = doc[page_num]

    # Extracting the directory path
    directory_path = os.path.dirname(pdf_path)

    # Splitting the directory path into a list of parent folders
    parent_folders_str = str(directory_path.split(os.sep))

    # Extracting the filename
    filename = os.path.basename(pdf_path)

    try:
        page_valid = True
        pdf_id = commit_pdf_page(project_id, page, doc.name, page_num, page_valid)
    except Exception as e:
        logger.warning(f"Page {page_num + 1}. Error processing page - {e}")
        raise Exception(e)

    # Process page text blocks
    # Get the page's text and apply rotation if needed
    rotation = page.rotation
    rotation_matrix = page.rotation_matrix  # Get the page's rotation matrix
    if rotation != 0:
        logger.info(f"Page {page_num + 1} Rotation: {rotation}. Applying Rotation Matrix")

    text_blocks: dict = get_page_text(page, rotation_matrix)

    # Initialize page raw data by extracting text from text blocks
    page_raw_data, page_img_data = process_text_rois(pdf_id, rotation, text_blocks, doc, page_num, pdf_path, filename, parent_folders_str)

    # Process page annotations and add to page raw data
    page_annots_data = process_annot_rois(pdf_id, doc, page, page_num, pdf_path, filename, parent_folders_str)
    page_raw_data.extend(page_annots_data)

    idx = 1
    for p in page_raw_data:
        p["idx"] = idx
        idx += 1

    #
    logger.debug(f"Detecting regions for {page_num+1}")
    cv2_page_regions = detect_page_regions(page=page, page_num=page_num+1, return_rectangles=True)
    page_raw_df = pd.DataFrame(page_raw_data)

    if not page_raw_df.empty:
        page_raw_regions = page_raw_df["coordinates2"].values.tolist()
    else:
        page_raw_regions = []

    # page_raw_regions = page_raw_df[['x0', 'y0', 'x1', 'y1']].values.tolist()
    if not page_raw_regions:
        # Blank page? Or Full OCR?
        logger.info(f"Missing regions detected for {page_num+1}")
        missing_regions = cv2_page_regions
    else:
        missing_regions_indices = find_non_overlapping_rectangles(cv2_page_regions, page_raw_regions)
        missing_regions = [cv2_page_regions[i] for i in missing_regions_indices]

    missing_regions = [{
            "pdf_page": page_num + 1,
            "x0": rect[0],
            "y0": rect[1],
            "x1": rect[2],
            "y1": rect[3],
            "width": rect[2] - rect[0],
            "height": rect[3] - rect[1]
        }
        for rect in missing_regions]

    process_time = time.perf_counter() - t1_start
    lock.acquire()
    process_times.append(process_time)
    lock.release()

    err = None
    return page_num + 1, page_raw_data, missing_regions, process_time, err

def process_page_wrapper(project_id: int, filename, page_num: int, process_times: list, cancel_event, doc: fitz.Document=None):
    """Function to support multiprocessing of page preprocessing"""
    if not doc:
        doc = fitz.open(filename)
    try:
        return process_page(project_id, doc, filename, page_num, process_times, cancel_event)
    except Exception as e:
        page_raw_data = []
        page_missing_regions = []
        process_time = -1
        return page_num + 1, page_raw_data, page_missing_regions, process_time, e


def process_chunk_pages(project_id: int, filename, page_numbers: list[int], process_times: list, cancel_event, doc: fitz.Document=None):
    """Function to support multiprocessing of page preprocessing"""
    doc = fitz.open(filename)

    chunk_results = []
    for page_num in page_numbers:
        try:
            res = process_page_wrapper(project_id, filename, page_num, process_times, cancel_event, doc)
            chunk_results.append(res)
        except Exception as e:
            logger.error(e)

    return chunk_results


class CancelException(Exception):
    """Polled for canceling preprocessing job"""
    def __init__(self):
        super().__init__()


class SourcePreprocess():

    def __init__(self,
                 project_id: int,
                 filename: str,
                 output_path: str = None,
                 pages = None,
                 multiprocess: bool = False,
                 jobId = None,
                 debug: bool = False):

        self.project_id: int = project_id
        self._debug_mode = debug
        self.pages = pages
        self.filename = filename
        self.output_path = output_path
        self.doc = fitz.open(filename)
        self.jobId = jobId
        self.total_groups = None

        self._multiprocess = multiprocess
        self._cancel = Event()
        self._total_pages = None
        self._time_of_last_request = None
        self._times = []
        self._eta_updated_cb = None
        self._last_eta = None
        self._timer = Thread(target=self.on_timer)
        self._page_results = []
        self._results = None

        self._start_time = None

        if COMMIT_CHUNKS and COMMIT_PAGE_CHUNK_SIZE <= 0:
            raise ValueError("COMMIT_PAGE_CHUNK_SIZE must be >= 1 if COMMIT_CHUNKS is True")

    def _process_pages(self):
        """Process PDF and group pages and return a dataframe.

        Args:
            filename: Input PDF filename.
            output_path: Optionally generate.
            pages: A list[int] of pages to be processed. If None is supplied, all pages
                are analyzed.
            multiprocess: Enable multiprocessing

        Returns:
            A dataframe containing results of pages grouped
        """
        print("Processing pages...")
        # Ensure the output directory exists
        if self.output_path:
            os.makedirs(self.output_path, exist_ok=True)

        if self.pages:
            extract_pages = [int(p) - 1 for p in self.pages]
        else:
            extract_pages = [n for n in range(fitz.open(self.filename).page_count)]

        extract_pages.sort()
        self._total_pages = total_pages = len(extract_pages)
        self._extract_pages = extract_pages

        # COMMIT_CHUNKS
        results = []
        summary = []
        chunks_saved = 0

        remaining = total_pages
        db = DatabaseManager()

        process_worker_lock = multiprocessing.Lock()
        def handle_page_result(result):
            nonlocal results, chunks_saved, summary, remaining, db
            print("Handling result for pages:", result[0])
            self._page_results.append(result)
            results.append(result)
            remaining -= 1

        def handle_chunk_result(chunk_no: int, num_pages: int, chunk_result: list):
            nonlocal results, chunks_saved, summary, remaining, db
            # print(1212312312, chunk_result, chunk_no, num_pages)
            print(f"Handling result for Chunk No. {chunk_no}", f"Num Pages {num_pages}")
            results.extend(chunk_result)
            remaining -= len(chunk_result)
            chunks_saved += 1

        if self._multiprocess:
            self._manager = Manager()
            self._cancel = self._manager.Event()
            self._times = self._manager.list()
        else:
            self._times = []

        # Initialize the set of args for all workers
        cpu_count = multiprocessing.cpu_count()
        pages_per_chunk = max(1, self._total_pages // cpu_count)
        self._pages_per_chunk = pages_per_chunk
        chunk_args = []
        for n in range(0, len(extract_pages), pages_per_chunk):
            pages = [extract_pages[m] for m in range(n, min(len(extract_pages), n + pages_per_chunk))]
            chunk_args.append([self.project_id, self.filename, pages, self._times, self._cancel])

        if self._multiprocess:
            with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
                for chunk_no, args in enumerate(chunk_args, start=1):
                    pages = args[2] #
                    logger.info(f"Processing chunk {chunk_no} - Pages {pages[0]} to {pages[-1]}")
                    pool.apply_async(process_chunk_pages, args, callback=partial(handle_chunk_result, chunk_no, len(pages), ))

                pool.close()
                pool.join()
        else:
            results = []
            doc = fitz.open(self.filename)
            for chunk_no, args in enumerate(chunk_args, start=1):
                pages = args[2]
                logger.info(f"Processing chunk {chunk_no} - Pages {pages[0]} to {pages[-1]}")
                args = args + [doc]
                result = process_chunk_pages(*args)
                # handle_chunk_result(result, chunk_no, len(pages))
                partial(handle_chunk_result, chunk_no, len(pages), result)()

                if self._cancel.is_set():
                    return

        if self._cancel.is_set():
            return

        self._page_results = results
        self._chunks_saved = chunks_saved

    def _process_results(self):
        """Group the page results"""
        logger.info("Processing results...")

        summary_data = []
        combined_raw_data = []
        combined_missing_regions = []

        for page_num, page_raw_data, page_missing_regions, process_time, err in self._page_results:
            summary_data.append({
                "filename": os.path.basename(self.filename),
                "pdf_page": page_num,
                "raw_data_count": len(page_raw_data),
                "missing_region_count": len(page_missing_regions),
                "process_time": process_time,
                "error": err,
            })
            combined_raw_data.extend(page_raw_data)
            combined_missing_regions.extend(page_missing_regions)

        combined_raw_data = pd.DataFrame(combined_raw_data)
        combined_raw_data = clean_dataframe_for_pyarrow(combined_raw_data, ignore=["coordinates", "coordinates2"])
        combined_missing_regions = pd.DataFrame(combined_missing_regions)

        logger.info("Saving preprocessing data...")

        source_dir = getSourceDataDir(self.project_id, self.filename, makedir=True)

        if DEBUG_MODE and not __file__.endswith(".pyc"):
            logger.debug("Saving debug data...can be slow if saving large df to excel")
            summary_pages_fn = "debug/preprocessing_summary_pages.xlsx"
            summary_fn = "debug/preprocessing_summary.xlsx"
            debug_raw_df_fn = "debug/preprocessing_raw_data.xlsx"
            summary_pages_df = pd.DataFrame(summary_data)
            summary_pages_df.to_excel(summary_pages_fn)
            print("Saved summary_pages_fn", summary_pages_fn)

            combined_raw_data.to_excel(debug_raw_df_fn)
            print("Saved debug_raw_df_fn", summary_pages_fn)

            # Single record summary
            summary_stats = [{
                "filename": os.path.basename(self.filename),
                "page_count": len(self._extract_pages),
                "chunks": self._chunks_saved,
                "chunk_processing": self._chunks_saved > 0,
                "multiprocessing?": self._multiprocess,
                "total_time": round(time.perf_counter() - self._start_time, 3),
                "avg_time_per_page": round(summary_pages_df["process_time"][summary_pages_df["process_time"] > 0].mean(), 3),
                "longest_process_time": round(summary_pages_df["process_time"].max(), 3),
                "shortest_process_time": round(summary_pages_df["process_time"][summary_pages_df["process_time"] > 0].min(), 3),
                "error page count": summary_pages_df["error"][summary_pages_df["error"].notna()].count(),
                "pages_per_chunk": self._pages_per_chunk,
            }]

            try:
                pd.DataFrame(summary_stats).to_excel(summary_fn)
                print("Saved debug summary to f{summary_fn}")
            except Exception as e:
                logger.warning("Failed to save debug")

        # Save raw data
        logger.debug("Saving raw data...")
        raw_data_fn = os.path.join(source_dir, "data")
        if not combined_raw_data.empty:
            save_df_fast(combined_raw_data, raw_data_fn, format="feather")

        # Save missing regions data
        logger.debug("Saving missing regions data...")
        combined_missing_regions_fn = os.path.join(source_dir, "pmr")
        if not combined_missing_regions.empty:
            save_df_fast(combined_missing_regions, combined_missing_regions_fn, format="feather")

        metadata_fn = os.path.join(source_dir, "metadata.json")
        metadata = {
            "missing_regions": len(combined_missing_regions),
            "raw_data": len(raw_data_fn),
        }

        with open(metadata_fn, "w") as metadata_file:
            json.dump(metadata, metadata_file)

        self._results = {
            "jobId": self.jobId,
        }

    def run(self):
        """Run preprocessing"""
        pub.sendMessage("set-statusbar-realtime", message="Preprocessing Started", jobId=self.jobId)
        self._start_time = time.perf_counter()  # Capture the start time
        self._timer.start()
        for n, state in enumerate([
            self._process_pages,
            self._process_results,
        ]):
            if self._cancel.is_set():
                print(f"Cancelled preprocessing at step {n} - function `{state.__name__}`")
                self._results_df = None
                return
            print(f"\n--- State {n} - function `{state.__name__}` \n\n")
            state()

        self._cancel.set()  # Done

    def cancel(self):
        """Cancel and all running tasks"""
        logger.debug("Canceling")
        self._cancel.set()

    def on_timer(self):
        smoothing_factor = 0.05
        while not self._cancel.is_set():
            time.sleep(1)
            if not self._eta_updated_cb:
                continue

            if self._total_pages is None:
                continue
            lock.acquire()
            times = self._times[-5:]
            remaining_pages = max(0, self._total_pages - len(self._times))
            lock.release()
            if len(times) < 5:
                continue
            eta = int(statistics.fmean(times) * remaining_pages)
            if self._last_eta is None:
                self._last_eta = eta
                continue
            eta = smoothing_factor * self._last_eta + (1 - smoothing_factor) * eta
            self._last_eta = int(eta)
            if self._eta_updated_cb:
                self._eta_updated_cb(self._last_eta)

        if self._eta_updated_cb:
            self._eta_updated_cb(0)

    def get_results(self):
        return self._results


pdfs = [
    r"C:/Users/<USER>/Documents/Drawings/Binder1.pdf",
    r"C:\Drawings\SanMateoOCR\San Mateo Midstream_Black River_ISOs.pdf",
    r"C:\Users\<USER>\Documents\Drawings\PLNG BOG Drawings.pdf",
    r"C:\Users\<USER>\Documents\Drawings\RCM Combined.pdf",
    r"c:\Users\<USER>\Documents\Drawings\Classification - ISO -  25 - TEST 3.pdf",

    # # sharefile
    r"C:\Drawings\FromSharefile\Modified Sarpy Co Jones St Total Combined.pdf",
    r"C:\Drawings\FromSharefile\Combined Drawings - CF B vs C.pdf",
    r"C:\Drawings\FromSharefile\SCOPED-PQ-376510-PIP-ISO-KZV-0E2Z6-001 (E2Z6)ISOMETRIC BLANK.pdf",
    r"C:\Drawings\FromSharefile\81272 Weld Maps.pdf",
    r"C:\Drawings\FromSharefile\Excel 012 - BUNGE Combined.pdf",
    r"C:\Drawings\FromSharefile\Group 4.pdf",
    r"C:\Drawings\FromSharefile\1601 - Insulation Only.pdf",
    r"C:\Drawings\FromSharefile\P&S 008 - combined ISOs.pdf",
    r"C:\Drawings\FromSharefile\P&S 004 - Exxon LT Combined.pdf",
    r"C:\Drawings\FromSharefile\P&S 006 - 24-P-1002.pdf",
]

if __name__ == "__main__":

        # pages = [1,2,3,4,5]
    # pages = [n + 1 for n in range(50)]
    pages = [1]
    pages = [16]
    pages = None
    MULTIPROCESS = False
    # Thread(target=lambda: test_cancel(time_to_cancel)).start()
    project_id = 7
    # filename = r"C:/Drawings/Clients/brockservices/27_BSLE32128/received/20250617+Carbon_Steel_COMBINED.pdf"
    filename = r"C:/Drawings/Clients/brockservices/BSL7043/received/IFC Post Turnaround ISO Combined.pdf"
    filename = r"C:/Drawings/Clients/axisindustries/2025-06-30 Field Routes 6.19.25/received/Field Routes 6.19.25.pdf"

    from time import perf_counter
    start = perf_counter()

    MULTIPROCESS = True

    processor = SourcePreprocess(project_id=project_id,
                                 filename=filename,
                                 output_path=None,
                                 pages=pages,
                                 multiprocess=MULTIPROCESS,
                                 debug=DEBUG_MODE)
    processor.run()

    print(f"Processing time {perf_counter() - start}s")

    source_dir = getSourceDataDir(project_id, filename)
    raw_data_file = os.path.join(source_dir, "data.feather")
    feather = pd.read_feather(raw_data_file)
    feather.to_excel("debug/page16_preprocess_data.xlsx")
    # print(feather)
    exit()

    run_ide = True # Run from IDE
    profile_code = True # In depth analysis of code through CProfile

    if profile_code:
        import cProfile, pstats
        from io import StringIO

        profiler = cProfile.Profile()
        profiler.enable()

    if run_ide:
        filename = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Demo Files\Lin10.pdf"  # Path to file
        pages = None                     # Or a list [1, 2, 3]

        # start = time.perf_counter()
    else:

        print(sys.argv[1:])

        try:
            filename = sys.argv[1]
        except Exception as e:
            raise ValueError("Filename required for preprocessing")

        try:
            pages = sys.argv[2]
        except Exception as e:
            pages = None # All pages

    start = time.perf_counter()

    def print_eta(eta):
        print(f"ETA: {eta}")

    def test_cancel(n=None):
        # Test function to cancel processing after `n` seconds
        import time
        if n is None:
            return
        time.sleep(n)
        processor.cancel()

    time_to_cancel = None

    filename = r"C:\Drawings\FromSharefile\P&S 006 - 24-P-1002.pdf"
    filename = r"C:\Users\<USER>\Desktop\remuriate\Combined Remuriate.pdf"

    project_id = None
    pages = [1]


    for filename in pdfs:
        doc = fitz.open(filename)
        page_count = doc.page_count
        basename = os.path.basename(filename)
        page_count = min(page_count, 20)
        for n in range(page_count):
            print(basename, n)
            page_num, page_raw_data, page_missing_regions, process_time, e = process_page(project_id, doc, filename, n, [], cancel_event=Event())

            # print(page_num, page_raw_data, page_missing_regions, process_time, e)

            raw_df = pd.DataFrame(page_raw_data)

            raw_regions = extract_raw_regions(raw_df)
            # pd.DataFrame(raw_regions).to_excel("debug/raw_regions.xlsx")

            page = doc[n]
            image_cv = page_to_opencv(page)
            raw_regions_image = visualize_regions(image_cv, raw_regions, color=(0,255,0))
            missing_regions_image = visualize_regions(image_cv, page_missing_regions, color=(255,0,0))
            mixed_regions_image = visualize_regions(raw_regions_image, page_missing_regions, color=(255,0,0))

            # Draw raw over missing
            # mixed_regions_image = visualize_regions(raw_regions_image, page_missing_regions, color=(255,0,0))

            cv2.putText(raw_regions_image, f"raw_regions", (40, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,0,255), 3)
            cv2.putText(missing_regions_image, f"missing_regions", (40, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,0,255), 3)
            cv2.putText(mixed_regions_image, f"raw+missing", (40, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,0,255), 3)

            images = [
                mixed_regions_image,
                raw_regions_image,
                missing_regions_image,
            ]

            os.makedirs(f"debug/raw_and_missing/{basename}", exist_ok=True)
            full_image = cv2.hconcat(images)
            cv2.imwrite(f"debug/raw_and_missing/{basename}/raw_and_missing_page_{n+1}.png", full_image)

    exit()
    # # pages = [1,2,3,4,5]
    # # pages = [n + 1 for n in range(50)]
    # MULTIPROCESS = False
    # Thread(target=lambda: test_cancel(time_to_cancel)).start()
    # processor = SourcePreprocess(project_id=project_id,
    #                              filename=filename,
    #                              output_path=None,
    #                              pages=pages,
    #                              multiprocess=MULTIPROCESS,
    #                              debug=DEBUG_MODE)
    # processor._eta_updated_cb = print_eta
    # processor.run()

    print(f"Processing time {time.perf_counter() - start}s")

    if profile_code:
        profiler.disable()


        profiler.dump_stats(r"debug/output_profile.prof")


        s = StringIO()
        sortby = 'tottime'  # or 'cumulative'
        ps = pstats.Stats(profiler, stream=s).sort_stats(sortby)
        ps.print_stats()
        # print(s.getvalue())  # prints cProfile stats to console

        print("\n\n --> CProfile: Saved output to ", r"debug/output_profile.prof")

        import pstats

        p = pstats.Stats(r"debug/output_profile.prof")
        p.strip_dirs()                 # remove directory paths for cleaner output
        p.sort_stats("tottime")        # or "cumulative"
        p.print_stats(30)              # top 30