"""
Apply Audit Corrections Script

This script applies corrections from audit results back to the original BOM data.

Usage:
1. Run the audit system to generate audit_results_programmatic.xlsx
2. Open the Excel file and set accept_merge = True for corrections you want to apply
3. Run this script to apply the corrections and create a new corrected workbook

The script will:
- Load the audit results with your accept_merge decisions
- Load the original BOM data
- Apply only the corrections where accept_merge = True
- Save a new corrected workbook with all original columns in exact order
"""

import pandas as pd
import os
import sys
from datetime import datetime

# Add the parent directory to the path to import audit_main
sys.path.append(os.path.dirname(__file__))

from audit_main import apply_audit_corrections


def apply_corrections_workflow():
    """
    Main workflow to apply audit corrections
    """
    print("BOM Audit Corrections Application")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    print()
    
    # File paths - update these as needed
    audit_results_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output\audit_results_programmatic.xlsx"
    original_bom_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\rfq_template.xlsx"
    output_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output"
    
    try:
        # Step 1: Load audit results
        print("Step 1: Loading audit results...")
        if not os.path.exists(audit_results_file):
            print(f"❌ Audit results file not found: {audit_results_file}")
            print("Please run the audit system first to generate audit results.")
            return
        
        audit_results_df = pd.read_excel(audit_results_file)
        print(f"✓ Loaded audit results: {len(audit_results_df)} rows")
        
        # Check for accept_merge column
        if 'accept_merge' not in audit_results_df.columns:
            print("❌ 'accept_merge' column not found in audit results")
            print("Please ensure the audit results file has the correct format.")
            return
        
        # Count accepted corrections
        accepted_count = len(audit_results_df[audit_results_df['accept_merge'] == True])
        print(f"✓ Found {accepted_count} accepted corrections")
        
        if accepted_count == 0:
            print("⚠️  No corrections marked for acceptance (accept_merge = True)")
            print("Please edit the audit results file to mark corrections you want to apply.")
            return
        
        # Step 2: Load original BOM data
        print("\nStep 2: Loading original BOM data...")
        if not os.path.exists(original_bom_file):
            print(f"❌ Original BOM file not found: {original_bom_file}")
            return
        
        original_df = pd.read_excel(original_bom_file)
        print(f"✓ Loaded original BOM data: {len(original_df)} rows, {len(original_df.columns)} columns")
        
        # Step 3: Apply corrections
        print("\nStep 3: Applying corrections...")
        corrected_df = apply_audit_corrections(audit_results_df, original_df)
        
        # Step 4: Save corrected data
        print("\nStep 4: Saving corrected data...")
        os.makedirs(output_folder, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_folder, f"corrected_bom_data_{timestamp}.xlsx")
        
        corrected_df.to_excel(output_file, index=False)
        print(f"✓ Corrected data saved to: {output_file}")
        
        # Step 5: Summary
        print("\n" + "=" * 60)
        print("CORRECTION SUMMARY")
        print("=" * 60)
        print(f"📁 Original file: {os.path.basename(original_bom_file)}")
        print(f"📁 Audit results: {os.path.basename(audit_results_file)}")
        print(f"📁 Corrected file: {os.path.basename(output_file)}")
        print(f"📊 Total corrections applied: {accepted_count}")
        print(f"📊 Original rows: {len(original_df)}")
        print(f"📊 Corrected rows: {len(corrected_df)}")
        print(f"📊 Original columns: {len(original_df.columns)}")
        print(f"📊 Corrected columns: {len(corrected_df.columns)}")
        
        # Verify column order is preserved
        if list(original_df.columns) == list(corrected_df.columns):
            print("✓ Column order preserved")
        else:
            print("⚠️  Column order may have changed")
        
        print("\n🎉 Corrections applied successfully!")
        print(f"📄 Full path to corrected file:")
        print(f"   {output_file}")
        
    except Exception as e:
        print(f"\n❌ Error applying corrections: {e}")
        import traceback
        traceback.print_exc()


def preview_corrections():
    """
    Preview what corrections would be applied without actually applying them
    """
    print("BOM Audit Corrections Preview")
    print("=" * 50)
    
    audit_results_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output\audit_results_programmatic.xlsx"
    
    try:
        if not os.path.exists(audit_results_file):
            print(f"❌ Audit results file not found: {audit_results_file}")
            return
        
        audit_results_df = pd.read_excel(audit_results_file)
        
        # Show all corrections
        print(f"Total audit results: {len(audit_results_df)}")
        print()
        
        # Show accepted corrections
        if 'accept_merge' in audit_results_df.columns:
            accepted = audit_results_df[audit_results_df['accept_merge'] == True]
            print(f"Corrections marked for acceptance: {len(accepted)}")
            
            if len(accepted) > 0:
                print("\nCorrections that will be applied:")
                print("-" * 50)
                for _, row in accepted.iterrows():
                    print(f"ID {row['id']}: {row['column_name']} = '{row['suggested_value']}'")
                    print(f"  Current: '{row['current_value']}'")
                    print(f"  Reason: {row['explanation']}")
                    print()
            else:
                print("No corrections marked for acceptance.")
        else:
            print("❌ 'accept_merge' column not found")
            
    except Exception as e:
        print(f"❌ Error previewing corrections: {e}")


if __name__ == "__main__":
    print("BOM Audit Corrections Tool")
    print("=" * 60)
    print()
    print("Choose an option:")
    print("1. Preview corrections (see what would be applied)")
    print("2. Apply corrections (actually make the changes)")
    print()
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        preview_corrections()
    elif choice == "2":
        apply_corrections_workflow()
    else:
        print("Invalid choice. Please run again and enter 1 or 2.")
