"""
Comprehensive column mapping module for Architekt ATOM.

This module provides functionality to convert between:
1. SQLite column names
2. PostgreSQL column names
3. Human-readable display names

It supports all major tables: general, bom, and atem_rfq.
"""

import os
import json
import pandas as pd
import re
from typing import Dict, List, Optional, Tuple, Union, Any

# Load the fieldmap.json file for display names
def load_fieldmap() -> Dict:
    """Load the fieldmap.json file containing display and default names."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    fieldmap_path = os.path.join(script_dir, '..', '..', '..', '..', 'data', 'fieldmap.json')

    try:
        with open(fieldmap_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading fieldmap.json: {e}")
        return {}

# Global mappings - these will be initialized in initialize_mappings()
FIELDMAP = {}
SQLITE_TO_PG = {}
PG_TO_SQLITE = {}
DISPLAY_TO_PG = {}
PG_TO_DISPLAY = {}
DISPLAY_TO_SQLITE = {}
SQLITE_TO_DISPLAY = {}

# Initialize all mappings
def initialize_mappings():
    """Initialize all mapping dictionaries."""
    global FIELDMAP, SQLITE_TO_PG, PG_TO_SQLITE, DISPLAY_TO_PG, PG_TO_DISPLAY, DISPLAY_TO_SQLITE, SQLITE_TO_DISPLAY

    # Load fieldmap
    FIELDMAP = load_fieldmap()

    # Define the SQLite to PostgreSQL mappings - general table
    SQLITE_TO_PG["general"] = {
        # Primary keys and relationships
        "id": "id",
        "pdf_id": "pdf_id",

        # Renamed measurement columns
        "lf": "length",
        "sf": "calculated_area",
        "ef": "calculated_eq_length",

        # camelCase to snake_case conversions
        "annotMarkups": "annot_markups",
        "blockCoordinates": "block_coordinates",
        "clientDocumentId": "client_document_id",
        "documentId": "document_id",
        "documentTitle": "document_title",
        "flangeID": "flange_id",
        "heatTrace": "heat_trace",
        "insulationSpec": "insulation_spec",
        "insulationThickness": "insulation_thickness",
        "isoNumber": "iso_number",
        "isoType": "iso_type",
        "lineNumber": "line_number",
        "maxElevation": "max_elevation",
        "mediumCode": "medium_code",
        "minElevation": "min_elevation",
        "modDate": "mod_date",
        "paintSpec": "paint_spec",
        "pipeSpec": "pipe_spec",
        "pipeStandard": "pipe_standard",
        "processLineList": "process_line_list",
        "processUnit": "process_unit",
        "projectNo": "project_no",
        "projectName": "project_name",
        "totalSheets": "total_sheets",
        "vendorDocumentId": "vendor_document_id",
        "weldId": "weld_id",
        "weldClass": "weld_class",
        "xCoord": "x_coord",
        "yCoord": "y_coord",
        "paintColor": "paint_color",
        "sysDocument": "sys_document",
        "sysDocumentName": "sys_document_name",
        "sys_filename": "sys_filename",  # Already snake_case in original
        "sys_path": "sys_path",          # Already snake_case in original
        "sys_build": "sys_build",        # Already snake_case in original
        "sys_layout_valid": "sys_layout_valid",  # Already snake_case in original

        # Unchanged columns
        "area": "area",
        "coordinates": "coordinates",
        "cwp": "cwp",
        "design_code": "design_code",
        "document_description": "document_description",
        "drawing": "drawing",
        "elevation": "elevation",
        "pid": "pid",
        "pwht": "pwht",
        "revision": "revision",
        "sequence": "sequence",
        "service": "service",
        "sheet": "sheet",
        "size": "size",
        "system": "system",
        "unit": "unit",
        "xray": "xray",
        "elbows_90": "elbows_90",
        "elbows_45": "elbows_45",
        "bevels": "bevels",
        "tees": "tees",
        "reducers": "reducers",
        "caps": "caps",
        "flanges": "flanges",
        "valves_flanged": "valves_flanged",
        "valves_welded": "valves_welded",
        "cut_outs": "cut_outs",
        "supports": "supports",
        "bends": "bends",
        "field_welds": "field_welds",
        "project_id": "project_id",
        "pdf_page": "pdf_page",
        "avg_elevation": "avg_elevation",
        # New columns added to general table
        "isometric_drawing_area": "isometric_drawing_area",
        "fluid": "fluid",
        "revision_date": "revision_date",
        "clean_spec": "clean_spec"
    }

    # Define the SQLite to PostgreSQL mappings - bom table
    SQLITE_TO_PG["bom"] = {
        # Primary keys and relationships
        "id": "id",
        "pdf_id": "pdf_id",
        "project_id": "project_id",
        "pdf_page": "pdf_page",
        "profile_id": "profile_id",
        "rfq_ref_id": "rfq_ref_id",
        "gen_ref_id": "gen_ref_id",

        # Renamed measurement columns
        "ef": "calculated_eq_length",
        "sf": "calculated_area",

        # camelCase to snake_case conversions
        "componentCategory": "component_category",
        "lineNumber": "line_number",
        "materialDescription": "material_description",
        "normalizedDescription": "normalized_description",
        "material_scope": "mtrl_category",
        #"Drawing": "drawing",

        # System fields
        "sys_filename": "sys_filename",
        "sys_path": "sys_path",

        # Unchanged columns
        "pos": "pos",
        "material_description": "material_description",
        "size": "size",
        "size1": "size1",
        "size2": "size2",
        "ident": "ident",
        "item": "item",
        "tag": "tag",
        "quantity": "quantity",
        "status": "status",
        "nb": "nb",
        "fluid": "fluid",
        "clean_spec": "clean_spec",
        "item_count": "item_count",
        "item_length": "item_length",
        "total_length": "total_length", 
        "shape": "shape",

        # RFQ-related columns
        "rfq_scope": "rfq_scope",
        "general_category": "general_category",
        "unit_of_measure": "unit_of_measure",
        "material": "material",
        "abbreviated_material": "abbreviated_material",
        "technical_standard": "technical_standard",
        "astm": "astm",
        "grade": "grade",
        "rating": "rating",
        "schedule": "schedule",
        "coating": "coating",
        "forging": "forging",
        "ends": "ends",
        "item_tag": "item_tag",
        "tie_point": "tie_point",
        "pipe_category": "pipe_category",
        "valve_type": "valve_type",
        "fitting_category": "fitting_category",
        "weld_category": "weld_category",
        "bolt_category": "bolt_category",
        "gasket_category": "gasket_category",

        # Miscellaneous columns
        "notes": "notes",
        "deleted": "deleted",
        "ignore_item": "ignore_item",
        "validated_date": "validated_date",
        "validated_by": "validated_by"
    }

    # Define the SQLite to PostgreSQL mappings - verified_material_classifications table
    SQLITE_TO_PG["verified_material_classifications"] = {
        # Primary keys
        "id": "id",

        # Content fields
        "material_description": "material_description",
        "normalized_description": "normalized_description",
        "rfq_scope": "rfq_scope",
        "general_category": "general_category",
        "fitting_category": "fitting_category",
        "pipe_category": "pipe_category",
        "bolt_category": "bolt_category",
        "gasket_category": "gasket_category",
        "valve_type": "valve_type",
        "weld_category": "weld_category",
        "forging": "forging",
        "coating": "coating",
        "ends": "ends",
        "material": "material",
        "abbreviated_material": "abbreviated_material",
        "grade": "grade",
        "technical_standard": "technical_standard",
        "astm": "astm",
        "schedule": "schedule",
        "rating": "rating",
        "unit_of_measure": "unit_of_measure",
        "tie_point": "tie_point",
        "item_tag": "item_tag",
        "notes": "notes",
        "created_by": "created_by",
        "updated_by": "updated_by",
        "validated_by": "validated_by",
        "validated_date": "validated_date",
        "created_at": "created_at",
        "updated_at": "updated_at"
    }

    # Define the SQLite to PostgreSQL mappings - atem_rfq_input table
    SQLITE_TO_PG["atem_rfq_input"] = {
        # Primary keys and relationships
        "id": "id",
        "project_id": "project_id",
        "profile_id": "profile_id",

        # Content fields
        "material_description": "material_description",
        "material_code": "material_code",
        "sch_class": "sch_class",
        "parts": "parts",
        "type": "type",
        "weight": "weight",
        "number": "number",
        "remarks": "remarks",
        "ident": "ident",

        # Flags and metadata
        "deleted": "deleted",
        "ignore_item": "ignore_item",
        "mapping_not_found": "mapping_not_found"
    }

    # Define the SQLite to PostgreSQL mappings - atem_rfq table
    SQLITE_TO_PG["atem_rfq"] = {
        # Primary keys and relationships
        "id": "id",
        "project_id": "project_id",
        "profile_id": "profile_id",
        "rfq_input_id": "rfq_input_id",

        # Content fields
        "material_description": "material_description",
        "normalized_description": "normalized_description",
        "component_category": "component_category",
        "material_scope": "mtrl_category",
        "size": "size",
        "size1": "size1",
        "size2": "size2",
        "quantity": "quantity",

        # Classification fields
        "rfq_scope": "rfq_scope",
        "general_category": "general_category",
        "unit_of_measure": "unit_of_measure",
        "material": "material",
        "abbreviated_material": "abbreviated_material",
        "technical_standard": "technical_standard",
        "astm": "astm",
        "grade": "grade",
        "rating": "rating",
        "schedule": "schedule",
        "coating": "coating",
        "forging": "forging",
        "ends": "ends",
        "item_tag": "item_tag",
        "tie_point": "tie_point",
        "pipe_category": "pipe_category",
        "valve_type": "valve_type",
        "fitting_category": "fitting_category",
        "weld_category": "weld_category",
        "bolt_category": "bolt_category",
        "gasket_category": "gasket_category",

        # Calculated fields
        "calculated_eq_length": "calculated_eq_length",
        "calculated_area": "calculated_area",

        # Flags and metadata
        "notes": "notes",
        "deleted": "deleted",
        "ignore_item": "ignore_item",
        "validated_date": "validated_date",
        "validated_by": "validated_by",
        "mapping_not_found": "mapping_not_found"
    }

    # Create reverse mappings
    PG_TO_SQLITE = {
        table: {v: k for k, v in mapping.items()}
        for table, mapping in SQLITE_TO_PG.items()
    }

    # Create display to PostgreSQL mappings based on fieldmap
    DISPLAY_TO_PG = {
        table: {} for table in ["general", "bom", "atem_rfq", "atem_rfq_input", "verified_material_classifications"]
    }

    # Add special case mappings that may not be in fieldmap.json
    special_cases = {
        "SYS PATH": "sys_path",
        "SYS FILENAME": "sys_filename",
        "Filename": "sys_filename",
        "PDF ID": "pdf_id",
        "PDF Page": "pdf_page",
        "Position": "pos",
        "Material Scope": "material_scope",
        "Technical Standard": "technical_standard",
        "Fluid": "medium_code",
        "Maximum Elevation": "max_elevation",
        "Minimum Elevation": "min_elevation",
        "Average Elevation": "avg_elevation",
        "Project ID": "project_id",
        "Drawing": "drawing",
        "Sheet": "sheet",
        "P&ID": "pid",
        "LF": "length",
        "EF": "calculated_eq_length",
        "SF": "calculated_area",
        "90's": "elbows_90",
        "45's": "elbows_45",
        "Bevels": "bevels",
        "Tees": "tees",
        "Red.": "reducers",
        "Caps": "caps",
        "Flanges": "flanges",
        "Flanged Valves": "valves_flanged",
        "Welded Valves": "valves_welded",
        "Cut Outs": "cut_outs",
        "Supports": "supports",
        "Bends": "bends",
        "FW": "field_welds",
        'Client ID': 'client_id',
        'Profile ID': 'profile_id',
        'Project ID': 'project_id',
        'client_id': 'client_id',
        'profile_id': 'profile_id',
        'project_id': 'project_id',
        # Additional fields for general table
        'Line Number': 'line_number',
        'Revision': 'revision',
        'Paint Spec': 'paint_spec',
        'Paint Color': 'paint_color',
        'Insulation Spec': 'insulation_spec',
        'Weld Class': 'weld_class',
        'CWP': 'cwp',
        'Op. Press.1 (P)': 'op1',
        'Op. Temp.1 (T)': 'opt1',
        'Design Press.1 (P)': 'dp1',
        'Design Temp.1 (T)': 'dt1',
        'Test Type': 'test_type',
        'Test PSI': 'test_pressure',
        'Test Pressure': 'test_pressure',
        'Size': 'size',
        # Handle field mappings
        'field_1': 'field_1',
        'field_2': 'field_2',
        'field_3': 'field_3',
        'field_4': 'field_4',
        'field_5': 'field_5',
        'field_6': 'field_6',
        'field_7': 'field_7',
        'field_8': 'field_8',
        'field3': 'field_3',
        'field4': 'field_4',
        'field5': 'field_5',
        'elevations': 'elevations',
        # General field mappings
        'general_field_1': 'field_1',
        'general_field_2': 'field_2',
        'general_field_3': 'field_3',
        'general_field_4': 'field_4',
        'general_field_5': 'field_5',
        'general_field_6': 'field_6',
        'general_field_7': 'field_7',
        'general_field_8': 'field_8',
        'general_field_9': 'field_9',
        'general_field_10': 'field_10',
        'general_field_11': 'field_11',
        'general_field_12': 'field_12',
        'general_field_13': 'field_13',
        'general_field_14': 'field_14',
        'general_field_15': 'field_15',
        'general_field_16': 'field_16',
        'general_field_17': 'field_17',
        'general_field_18': 'field_18',
        'general_field_19': 'field_19',
        'general_field_20': 'field_20',
        # BOM specific mappings
        'ansme_ansi': 'technical_standard',
        # Field display name mappings
        'Abbrev. Mtrl': 'abbreviated_material',
        'Technical Standard': 'technical_standard',
        # New general table columns
        'Isometric Drawing Area': 'isometric_drawing_area',
        'Fluid': 'fluid',
        'Revision Date': 'revision_date',
        'Clean Spec': 'clean_spec'
    }

    # Add special case mappings to all tables
    for table in ["general", "bom", "atem_rfq", "atem_rfq_input", "verified_material_classifications"]:
        for display_name, pg_col in special_cases.items():
            DISPLAY_TO_PG[table][display_name] = pg_col

    if FIELDMAP and 'fields' in FIELDMAP:
        for key, value in FIELDMAP['fields'].items():
            display_name = value.get('display', '')

            # For all tables, use the snake_case version of the display name as fallback
            snake_case_display = to_snake_case(display_name)

            for table in ["general", "bom", "atem_rfq", "atem_rfq_input", "verified_material_classifications"]:
                # First try to find direct mapping
                pg_col = SQLITE_TO_PG[table].get(key)

                # If no direct mapping, try to find a field that matches the snake_case of display name
                if not pg_col and snake_case_display in SQLITE_TO_PG[table].values():
                    pg_col = snake_case_display

                # If a mapping is found, add it to the display mappings
                if pg_col:
                    DISPLAY_TO_PG[table][display_name] = pg_col
                # If not found but we have snake_case, use that as a fallback
                elif not DISPLAY_TO_PG[table].get(display_name):
                    DISPLAY_TO_PG[table][display_name] = snake_case_display

    # Add RFQ specific fields from rfq_fields section
    if FIELDMAP and 'rfq_fields' in FIELDMAP:
        for key, value in FIELDMAP['rfq_fields'].items():
            display_name = value.get('display', '')
            # These fields apply mostly to BOM and RFQ tables
            if key in SQLITE_TO_PG["bom"]:
                DISPLAY_TO_PG["bom"][display_name] = SQLITE_TO_PG["bom"][key]
            if key in SQLITE_TO_PG["atem_rfq"]:
                DISPLAY_TO_PG["atem_rfq"][display_name] = SQLITE_TO_PG["atem_rfq"][key]
            if key in SQLITE_TO_PG["atem_rfq_input"]:
                DISPLAY_TO_PG["atem_rfq_input"][display_name] = SQLITE_TO_PG["atem_rfq_input"][key]
            if key in SQLITE_TO_PG["verified_material_classifications"]:
                DISPLAY_TO_PG["verified_material_classifications"][display_name] = SQLITE_TO_PG["verified_material_classifications"][key]

    # Create reverse mappings for display names
    PG_TO_DISPLAY = {
        table: {v: k for k, v in mapping.items()}
        for table, mapping in DISPLAY_TO_PG.items()
    }

    # Create mapping between display and SQLite
    DISPLAY_TO_SQLITE = {
        table: {} for table in ["general", "bom", "atem_rfq", "atem_rfq_input", "verified_material_classifications"]
    }

    # Populate display to SQLite mappings
    for table in ["general", "bom", "atem_rfq", "atem_rfq_input", "verified_material_classifications"]:
        for display_name, pg_col in DISPLAY_TO_PG[table].items():
            sqlite_col = PG_TO_SQLITE[table].get(pg_col)
            if sqlite_col:
                DISPLAY_TO_SQLITE[table][display_name] = sqlite_col

    # Create reverse mappings
    SQLITE_TO_DISPLAY = {
        table: {v: k for k, v in mapping.items()}
        for table, mapping in DISPLAY_TO_SQLITE.items()
    }


class ColumnMapper:
    """
    Utility class for mapping between different column naming conventions.
    Provides methods to convert column names and transform DataFrames.
    """

    def __init__(self):
        """Initialize the ColumnMapper."""
        # Make sure mappings are loaded
        if not FIELDMAP:
            initialize_mappings()

    def display_to_pg(self, table: str, display_column: str) -> Optional[str]:
        """
        Convert a display column name to a PostgreSQL column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            display_column: The human-readable display column name

        Returns:
            The PostgreSQL column name, or None if no mapping exists
        """
        return DISPLAY_TO_PG.get(table, {}).get(display_column)

    def pg_to_display(self, table: str, pg_column: str) -> Optional[str]:
        """
        Convert a PostgreSQL column name to a display column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            pg_column: The PostgreSQL column name

        Returns:
            The display column name, or None if no mapping exists
        """
        return PG_TO_DISPLAY.get(table, {}).get(pg_column)

    def sqlite_to_pg(self, table: str, sqlite_column: str) -> Optional[str]:
        """
        Convert a SQLite column name to a PostgreSQL column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            sqlite_column: The SQLite column name

        Returns:
            The PostgreSQL column name, or None if no mapping exists
        """
        return SQLITE_TO_PG.get(table, {}).get(sqlite_column)

    def pg_to_sqlite(self, table: str, pg_column: str) -> Optional[str]:
        """
        Convert a PostgreSQL column name to a SQLite column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            pg_column: The PostgreSQL column name

        Returns:
            The SQLite column name, or None if no mapping exists
        """
        return PG_TO_SQLITE.get(table, {}).get(pg_column)

    def display_to_sqlite(self, table: str, display_column: str) -> Optional[str]:
        """
        Convert a display column name to a SQLite column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            display_column: The display column name

        Returns:
            The SQLite column name, or None if no mapping exists
        """
        return DISPLAY_TO_SQLITE.get(table, {}).get(display_column)

    def sqlite_to_display(self, table: str, sqlite_column: str) -> Optional[str]:
        """
        Convert a SQLite column name to a display column name.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')
            sqlite_column: The SQLite column name

        Returns:
            The display column name, or None if no mapping exists
        """
        return SQLITE_TO_DISPLAY.get(table, {}).get(sqlite_column)

    def convert_dataframe_columns(self, df: pd.DataFrame, table: str,
                                  from_format: str, to_format: str) -> Tuple[pd.DataFrame, List[str]]:
        """
        Convert column names in a DataFrame from one format to another.

        Args:
            df: DataFrame with source format column names
            table: The table name ('general', 'bom', or 'atem_rfq')
            from_format: Source format ('display', 'sqlite', or 'pg')
            to_format: Target format ('display', 'sqlite', or 'pg')

        Returns:
            tuple: (DataFrame with converted column names, list of columns that couldn't be converted)
        """
        converted_df = df.copy()
        unconverted_columns = []

        # Choose the appropriate conversion function
        if from_format == 'display' and to_format == 'pg':
            convert_func = self.display_to_pg
        elif from_format == 'pg' and to_format == 'display':
            convert_func = self.pg_to_display
        elif from_format == 'sqlite' and to_format == 'pg':
            convert_func = self.sqlite_to_pg
        elif from_format == 'pg' and to_format == 'sqlite':
            convert_func = self.pg_to_sqlite
        elif from_format == 'display' and to_format == 'sqlite':
            convert_func = self.display_to_sqlite
        elif from_format == 'sqlite' and to_format == 'display':
            convert_func = self.sqlite_to_display
        else:
            raise ValueError(f"Unsupported conversion: {from_format} to {to_format}")

        # Convert each column
        column_mapping = {}
        for col in df.columns:
            new_col = convert_func(table, col)
            if new_col:
                column_mapping[col] = new_col
            else:
                unconverted_columns.append(col)

        # Rename columns
        if column_mapping:
            converted_df = converted_df.rename(columns=column_mapping)

        return converted_df, unconverted_columns

    def get_all_mappings(self, table: str) -> Dict[str, Dict[str, str]]:
        """
        Get all mappings for a specific table.

        Args:
            table: The table name ('general', 'bom', or 'atem_rfq')

        Returns:
            Dict containing all mappings for the specified table
        """
        return {
            'sqlite_to_pg': SQLITE_TO_PG.get(table, {}),
            'pg_to_sqlite': PG_TO_SQLITE.get(table, {}),
            'display_to_pg': DISPLAY_TO_PG.get(table, {}),
            'pg_to_display': PG_TO_DISPLAY.get(table, {}),
            'display_to_sqlite': DISPLAY_TO_SQLITE.get(table, {}),
            'sqlite_to_display': SQLITE_TO_DISPLAY.get(table, {})
        }


# Utility function to convert a string to snake_case
def to_snake_case(s: str) -> str:
    """
    Convert a string to snake_case.

    Args:
        s: Input string (can be camelCase, TitleCase, etc.)

    Returns:
        String converted to snake_case
    """
    # Replace any non-alphanumeric character with underscore
    s = re.sub(r'[^a-zA-Z0-9]', '_', s)

    # Insert underscore before uppercase letters
    s = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', s)

    # Convert to lowercase
    return s.lower()


# Utility function to convert a string to camelCase
def to_camel_case(s: str) -> str:
    """
    Convert a string to camelCase.

    Args:
        s: Input string (can be snake_case, TitleCase, etc.)

    Returns:
        String converted to camelCase
    """
    # Replace any non-alphanumeric character with space
    s = re.sub(r'[^a-zA-Z0-9]', ' ', s)

    # Split and join with title case, then make first character lowercase
    words = s.split()
    if not words:
        return ""

    return words[0].lower() + ''.join(word.title() for word in words[1:])


# Initialize mappings when the module is loaded
initialize_mappings()


# For testing and debugging
if __name__ == "__main__":
    mapper = ColumnMapper()

    # Example conversions
    print("Display to PostgreSQL for 'general' table:")
    print(mapper.display_to_pg("general", "Line Number"))

    print("\nSQLite to PostgreSQL for 'bom' table:")
    print(mapper.sqlite_to_pg("bom", "mtrl_category"))

    print("\nDisplay to SQLite for 'atem_rfq' table:")
    print(mapper.display_to_sqlite("atem_rfq", "Material Description"))

    # Example DataFrame conversion with the user's specific test file
    test_df = pd.read_excel(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001\Workspace\GENERAL EXPORT.xlsx")

    print("\nOriginal DataFrame:")
    # print(test_df)

    converted_df, unconverted = mapper.convert_dataframe_columns(
        test_df, "general", "display", "pg"
    )

    print("\nConverted DataFrame:")
    print(converted_df.columns)

    print("\nUnconverted columns:")
    print(unconverted)
