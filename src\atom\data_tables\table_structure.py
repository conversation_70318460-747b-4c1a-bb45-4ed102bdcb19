# table_structure.py
import pandas as pd
import numpy as np
import fitz
import re
import statistics
import logging
from .table_utils import check_bbox_overlap

def detect_bom_rows(page_num, df, leftmost_column, skip_words = ["PIPE SPOOL", "PIPE SPOOLS"],
                    bottom_limit_flags = [["PIECE MARKS", "PIECE MARK", "CUT PIECE LIST", "TOTAL FABRICATION WEIGHT"]], y0_deviation=1.5):

    if debug_row_content:
        df.to_excel(f"debug/detect_bom_row PG {page_num + 1}.xlsx")

        print(f"\n\nBOM_ROWS: {len(df)}")

    # Add bottom limit check here, right after the debug output but before any processing
    bottom_limit_y0 = None
    for flag_list in bottom_limit_flags:
        for flag in flag_list:
            # Convert values to string to handle any non-string types
            mask = df['value'].astype(str).str.contains(flag, case=False, na=False)
            matching_rows = df[mask]
            if not matching_rows.empty:
                bottom_limit_y0 = matching_rows.iloc[0]['coordinates2'][1]
                if debug_mode:
                    print(f"Found bottom limit flag '{flag}' at y0: {bottom_limit_y0}")
                break
        if bottom_limit_y0 is not None:
            break

    # Filter out rows below the bottom limit
    if bottom_limit_y0 is not None:
        df = df[df['coordinates2'].apply(lambda coord: coord[1] < bottom_limit_y0)]
        if debug_mode:
            print(f"Filtered out rows below y0: {bottom_limit_y0}")

    def is_valid_bom_item(row):
        text = row.value
        item_type = row.type
        try:
            first_part = text.split()[0]
            pattern = r'^\d{1,3}[a-zA-Z]?$'

            if item_type == 'Text':
                return bool(re.match(pattern, first_part))

            else:
                # Handle non-text annotations here
                # You might want to add specific handling for different annotation types
                return bool(re.match(pattern, first_part))  # Or use different validation logic

        except (AttributeError, IndexError):
            return False

        # return bool(re.match(pattern, first_part))

    def is_in_leftmost_column(x, y, leftmost_column):
        return (leftmost_column['x0'] <= x <= leftmost_column['x1'] and
                leftmost_column['y0'] <= y <= leftmost_column['y1'])

    def is_red_text(color): # For Revisions
        return color == (255, 0, 0)

    left_column_df = df[df["x0"].between(leftmost_column["x0"], leftmost_column["x1"]) & df["y0"].between(leftmost_column["y0"], leftmost_column["y1"])]
    bom_items = left_column_df[left_column_df.apply(is_valid_bom_item, axis=1)]

    # Sort by y0 coordinate
    bom_items_sorted = bom_items.sort_values('y0')

    # Calculate row ranges and store corresponding text
    row_ranges = []
    row_texts = []
    row_heights = []

    for i in range(len(bom_items_sorted)):

        start = bom_items_sorted.iloc[i]['y0']
        # text = bom_items_sorted.iloc[i]['Text']
        text = bom_items_sorted.iloc[i]['value']

        # Collect vertical (y1 - y0) heights of BOM rows
        if i < len(bom_items_sorted) - 1:
            end = bom_items_sorted.iloc[i+1]['y0']
        else:
            end = leftmost_column['y1']

        row_ranges.append((start, end))
        row_texts.append(text)
        row_heights.append(end - start)


    # Calculate average row height and standard deviation
    if len(row_heights) > 2:
        # If we have more than 2 rows, we can use statistics on all but the last row
        row_heights_for_calc = row_heights[:-1]
        avg_row_height = statistics.mean(row_heights_for_calc)
        std_row_height = statistics.stdev(row_heights_for_calc)
        outlier_threshold = avg_row_height + 2 * std_row_height
    elif len(row_heights) == 2:
        # If we have exactly 2 rows, use the first row's height
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = avg_row_height * 2  # Set a reasonable threshold
    elif len(row_heights) == 1:
        # If we have only 1 row, use its height directly
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = float('inf')  # No outliers possible with one row
    else:
        # Handle the case where there are no rows
        logger.warning(f"Table has no rows on page {page_num + 1}!")
        avg_row_height = 0
        std_row_height = 0
        outlier_threshold = float('inf')

    # Function to assign row based on coordinates
    def assign_row(y0, y1, color, text):
        # Perform checks for: Both y0, y1 in row, y1

        # Check vertical center line to assign row
        for i, (start, end) in enumerate(row_ranges):
            if start <= y0 + ((y1 - y0) / 2) <= end:
                return i

        # Alternative - Assign if both y0 and y1 are within row height range
        for i, (start, end) in enumerate(row_ranges):
            if start <= y0 <= end and start <= y1 <= end:
                return i

        # Fallback - Assign if y1 is within row height range
        for i, (start, end) in enumerate(row_ranges):
            if start <= y1 <= end:
                return i

        intersecting_rows = [i for i, (start, end) in enumerate(row_ranges) if start <= y1 and y0 <= end]
        if intersecting_rows:
            return min(intersecting_rows, key=lambda i: abs(row_ranges[i][0] - y0))

        # If all other checks fail, check for red text (Could be Revisions, which can be off center)
        if is_red_text(color):
            distances = []
            for i, (start, end) in enumerate(row_ranges):
                mid = (start + end) / 2
                distance_y0 = abs(y0 - mid)
                distance_y1 = abs(y1 - mid)
                distances.append(min(distance_y0, distance_y1))
            return np.argmin(distances)

        # If nothing worked, return None
        return None

    # Assign rows to all items
    row_assignments = [-1] * len(df)  # Initialize all assignments to -1
    unassigned_items = [] # Items that could not be assigned but likely should have been
    skipped_items = [] # Intentionally skipped
    skip_processing = False
    last_valid_row = -1
    last_row_items = []
    buffer_items = [] # Holds items for use in loop. Accounts for minor deviations in y0 to ensure we do not permanently discard valid items in 'skip_processing'

    for i, (index, row) in enumerate(df.iterrows()):
        y0, y1 = row['coordinates2'][1], row['coordinates2'][3]
        color = row['color']
        # text = row['Text']
        text = str(row['value'])
        text_type = str(row['type'])

        # Check if we need to start skipping
        if any(skip_word in text for skip_word in skip_words):
            skip_processing = True
            skipped_items.append((i, text, row['coordinates2'], "Contains skip word"))
            continue

        # Check if we need to stop skipping
        if skip_processing and is_valid_bom_item(text, text_type) and is_in_leftmost_column(row['x0'], row['y0'], leftmost_column):
            skip_processing = False

            # Process buffered items
            for buffered_item in buffer_items:
                if abs(buffered_item[1] - y0) <= y0_deviation:
                    assigned_row = assign_row(buffered_item[1], buffered_item[2], buffered_item[3], buffered_item[4])
                    if assigned_row is not None:
                        row_assignments[buffered_item[0]] = assigned_row
                    else:
                        unassigned_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Could not be assigned to a row after buffer processing"))
            buffer_items.clear()

        if skip_processing:
            buffer_items.append((i, y0, y1, color, text))
            # skipped_items.append((i, text, df.iloc[i]['Coordinates'], "In skip processing mode"))
            continue

        assigned_row = assign_row(y0, y1, color, text)
        if assigned_row is not None:
            # row_assignments.append(assigned_row)
            row_assignments[i] = assigned_row
            last_valid_row = assigned_row

            # Store items for the last row
            if assigned_row == len(row_ranges) - 1:
                last_row_items.append((i, y0, y1, text))

        else:
            unassigned_items.append((index,text, row['coordinates2'], "Could not be assigned to a row"))
            row_assignments[i] = last_valid_row  # Assign to the last valid row instead of -1

    # Process any remaining buffered items
    for buffered_item in buffer_items:
        skipped_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Remained in buffer after processing"))

    # Process the last row items for outliers
    if last_row_items:
        last_row_start, last_row_end = row_ranges[-1]
        last_row_height = last_row_end - last_row_start

        if last_row_height > outlier_threshold:
            #print(f"\nLast row is an outlier. Removing trash data:")
            valid_last_row_items = []
            for i, y0, y1, text in last_row_items:
                if y0 - last_row_start > outlier_threshold:
                    #print(f"Removing: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")
                    #unassigned_items.append((i, text, df.iloc[i]['Coordinates']))
                    skipped_items.append((i, text, df.iloc[i]['coordinates2'], "Greater than outlier threshold"))
                    row_assignments[i] = -1
                else:
                    valid_last_row_items.append((i, y0, y1, text))
                    #print(f"Keeping: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")

            # Update the last row range with the valid items
            if valid_last_row_items:
                new_last_row_end = max(item[2] for item in valid_last_row_items)
                row_ranges[-1] = (last_row_start, new_last_row_end)
                #print(f"\nUpdated last row range: {row_ranges[-1]}")
            else:
                print("\n--> Warning: All items in the last row were removed as trash.")
        # else:
        #     print("\nLast row is not an outlier. Keeping all items.")


    # df['assigned_row'] = row_assignments

    df = df.copy()
    df.loc[:, 'assigned_row'] = row_assignments

    # Create filtered contents based on row assignments
    filtered_contents = [df[df['assigned_row'] == i] for i in range(len(row_ranges))]

    # Debug information
    if debug_row_content:
        print("\nFinal Row Ranges: - Line 1593")
        for i, (start, end) in enumerate(row_ranges):
            print(f"Row {i}: {start} - {end} (height: {end - start})")

        print(f"Average row height: {avg_row_height}")
        print(f"Row height standard deviation: {std_row_height}")
        print(f"Outlier threshold: {outlier_threshold}")

    if debug_discarded:
        print("\nIntentionally Skipped Items:")
        if skipped_items:  # Only print if there are skipped items
            for item in skipped_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

        if unassigned_items:  # Only print if there are unassigned items
            print("\nUnassigned Items:")
            for item in unassigned_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")
    if debug_mode:
        try:
            print(f"\n\n-------\nReturning {i} Rows for page {page_num}\n")
        except Exception as e:
            logger.info(f"Returning {i} Rows for page ERROR GETTING PAGE NUM: {e}")
    return row_ranges, row_texts, filtered_contents, unassigned_items, skipped_items

def find_spanning_columns(bbox, column_coords):
    """Determine which columns the given bbox spans based on 'x0' and 'x1' coordinates in the column definitions."""
    x1, y1, x2, y2 = bbox
    spanning_columns = []

    # Logging the column coordinates to ensure correct data structure is received
    #logger.debug(f"Column Coords: {column_coords}")

    for idx, col_data in enumerate(column_coords):
        try:
            col_start = col_data['x0']
            col_end = col_data['x1']

            if (x1 < col_end and x2 > col_start):
                spanning_columns.append(idx)

        except KeyError as e:
            logger.error(f"Key error processing column boundaries for index {idx} with data {col_data}: {e}")

    return spanning_columns

def find_spanning_columns(bbox, column_coords):
    """Determine which columns the given bbox spans based on 'x0' and 'x1' coordinates in the column definitions."""
    x1, y1, x2, y2 = bbox
    spanning_columns = []

    # Logging the column coordinates to ensure correct data structure is received
    #logger.debug(f"Column Coords: {column_coords}")

    for idx, col_data in enumerate(column_coords):
        try:
            col_start = col_data['x0']
            col_end = col_data['x1']

            if (x1 < col_end and x2 > col_start):
                spanning_columns.append(idx)

        except KeyError as e:
            logger.error(f"Key error processing column boundaries for index {idx} with data {col_data}: {e}")

    return spanning_columns

def split_text_by_columns(text, original_bbox, column_coords):
    try:
        words = text.split()
        spans = []
        estimated_x_start = original_bbox[0]
        average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Ensure len(text) is not zero to avoid division by zero error
        if len(text) == 0:
            average_char_width = 0
        else:
            average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Calculate bounding boxes for each word
        for word in words:
            word_width = len(word) * average_char_width
            word_bbox = (estimated_x_start, original_bbox[1], estimated_x_start + word_width, original_bbox[3])
            spans.append((word, word_bbox))
            estimated_x_start += word_width + average_char_width  # Account for the space between words

        # Initialize text data for each column
        column_text_data = [('', None)] * len(column_coords)

        # Assign words to columns based on bounding boxes
        for word, bbox in spans:
            assigned = False
            for idx, col_data in enumerate(column_coords):
                col_start = col_data['x0']
                col_end = col_data['x1']

                # Check if the word fits within the column boundaries
                # Adjusting condition to consider word's start point and its midpoint
                if bbox[0] >= col_start and (bbox[0] + bbox[2]) / 2 <= col_end:
                    existing_text, existing_bbox = column_text_data[idx]
                    combined_text = (existing_text + word + ' ') #.strip()
                    if existing_bbox:
                        # Update the bounding box to encompass the new word
                        new_bbox = (existing_bbox[0], existing_bbox[1], bbox[2], existing_bbox[3])
                    else:
                        new_bbox = bbox
                    column_text_data[idx] = (combined_text, new_bbox)
                    assigned = True
                    break
            if not assigned:
                #logger.warning(f"Word '{word}' at bbox {bbox} was not assigned to any column. Assigning to the first column.")
                existing_text, existing_bbox = column_text_data[0]
                combined_text = (existing_text + word + ' ').strip()
                if existing_bbox:
                    new_bbox = (min(existing_bbox[0], bbox[0]), existing_bbox[1], max(existing_bbox[2], bbox[2]), existing_bbox[3])
                else:
                    new_bbox = bbox
                column_text_data[0] = (combined_text, new_bbox)

                #print("\n\nCOLUMN TEXT DATA: \n", column_text_data)

        # Clean up results to remove empty entries and prepare for output
        cleaned_data = [(text.rstrip(), bbox) for text, bbox in column_text_data if text.strip()]
        #print("\n\nCLEANED DATA: \n", cleaned_data)
        return cleaned_data

    except ZeroDivisionError:
        # Return the original data in case of an error
        return [(text, original_bbox)]

def merge_wrapped_rows_text(structured_data, annot_table=False):
    #Convert Data to a Dataframe
    if annot_table: #Return the structured dataframe
        return pd.DataFrame(structured_data)

    structured_df = pd.DataFrame(structured_data)

    # The first column is considered the reference for merging
    try:
        leftmost_column = structured_df.columns[0]
    except Exception as e:
        logger.error(f"Could not get left most columns. {e}")
        #structured_df.to_excel(f"Structured_df_merge_wrapped.xlsx")

    # Iterate backwards through the DataFrame to merge rows
    for i in range(len(structured_df) - 1, 0, -1):

        # If the leftmost column is empty, merge this row with the one above
        if pd.isna(structured_df.at[i, leftmost_column]) or structured_df.at[i, leftmost_column].strip() == '':

            for col in structured_df.columns:
                # Skip the 'material_scope' column entirely
                if col != 'material_scope':

                    # Only merge if the current row's cell is not empty
                    if not pd.isna(structured_df.at[i, col]) and structured_df.at[i, col].strip() != '':
                        structured_df.at[i - 1, col] = structured_df.at[i - 1, col].strip() + ' ' + structured_df.at[i, col].strip()
                        #print(f"\n\nMerging row {i} into row {i-1}. Content before merge: {structured_df.at[i-1, col]}, Content being merged: {structured_df.at[i, col]}")

            # After merging, drop the current row
            structured_df = structured_df.drop(index=i)

    # Reset index after dropping rows
    structured_df = structured_df.reset_index(drop=True)

    #export_large_data_to_excel(structured_df,"merged_wrapped_df_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
    return structured_df

def get_table_coordinates(converted_roi_payload):
    identified_tables = {}  # Dictionary to hold coordinates and column details for each table type

    try:
        for item in converted_roi_payload:
            table_type = item.get('columnName', '').lower()
            # Check if the current item is one of the identified table types and has table coordinates
            if table_type in ['bom', 'spec', 'spool'] and 'tableCoordinates' in item:
                # logger.debug(f"Found '{table_type}' item with table coordinates")

                # Extract table coordinates
                table_coords = item['tableCoordinates']

                # Initialize list to hold column coordinates for the current table
                column_coords = []
                # Extract column coordinates
                if 'tableColumns' in item:
                    for column_dict in item['tableColumns']:
                        for column_name, coords in column_dict.items():
                            coords['columnName'] = column_name  # Add columnName for compatibility
                            column_coords.append(coords)

                # Get the value of 'headersSelected'
                headers_selected = item.get('headersSelected', False)

                # print("\n\nHEADERS SELECTED", headers_selected)

                # Correctly structure the coordinates and column details for compatibility
                identified_tables[table_type] = (table_coords, column_coords, headers_selected)

    except Exception as e:
        logger.error(f"Error getting table coordinates: {e}", exc_info=True)


    return identified_tables # <-- IN USE

def combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5):

    def are_y_coords_close(coord1, coord2, tolerance):
        # Require one y coord closer and the other less strictly close

        # return abs(coord1[1] - coord2[1]) <= tolerance and abs(coord1[3] - coord2[3]) <= tolerance
        yd1 = abs(coord1[1] - coord2[1])
        yd2 = abs(coord1[3] - coord2[3])
        return any([
            (yd1 <= tolerance * 2 and yd2 <= tolerance),
            (yd1 <= tolerance and yd2 <= tolerance * 2)
        ])

    def should_combine(word1, word2, tolerance=x_tolerance):
        return word2['coordinates2'][0] - word1['coordinates2'][2] <= tolerance

    # Sort the dataframe by y0 and then x0 coordinates
    sorted_df = raw_df.sort_values(by=['coordinates2'], key=lambda x: [coord[1] for coord in x])

    combined_texts = {}
    current_line = []

    def process_line(line):
        line.sort(key=lambda x: x[1]['coordinates2'][0])  # Sort by x0

        row_text = " ".join([l[1]["value"] for l in line])
        # combined_text = line[0][1]['Text']
        combined_text = line[0][1]['value']
        start_index = 0

        # Experimental - if horizontal line components are smaller, more likely to be category,
        # so increase tolerance to increase chance of grouping
        x_tol = x_tolerance
        if len(row_text) < 20 and len(line) < 6:
            x_tol = x_tolerance * 3
        else:
            x_tol = x_tolerance

        for j in range(1, len(line)):
            if should_combine(line[j-1][1], line[j][1], x_tol):
                combined_text += ' ' + line[j][1]['value']
            else:
                for x in line[start_index:j]:
                    combined_texts[x[0]] = combined_text
                start_index = j
                # combined_text = line[j][1]['Text']                #
                combined_text = line[j][1]['value']
        for x in line[start_index:]:
            combined_texts[x[0]] = combined_text

    for i, row in sorted_df.iterrows():
        if not current_line or are_y_coords_close(current_line[-1][1]['coordinates2'], row['coordinates2'], y_tolerance):
            current_line.append((i, row))
            continue
        process_line(current_line)
        current_line = [(i, row)]  # New line

    # Process the last line
    if current_line:
        process_line(current_line)

    # Create a new dataframe with the combined texts
    combined_df = pd.DataFrame.from_dict(combined_texts, orient='index', columns=['Combined_Text'])

    # Update or add the 'Combined_Text' column efficiently
    if 'Combined_Text' in sorted_df.columns:
        sorted_df.update(combined_df)
    else:
        sorted_df = sorted_df.join(combined_df)

    return sorted_df

def sort_by_y0(df):
    if 'coordinates2' in df.columns:
        # Create a new column 'y0' to hold the y0 values from the 'Coordinates' tuples
        df['y0'] = df['coordinates2'].apply(lambda coord: coord[1])
        # Sort the DataFrame by the 'y0' column
        df = df.sort_values(by='y0')
        # Drop the temporary 'y0' column
        df = df.drop(columns=['y0'])
        return df
    else:
        logger.warning("Column 'coordinates2' does not exist in the DataFrame.")
        return df

