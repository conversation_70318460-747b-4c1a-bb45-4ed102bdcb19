import sqlite3
import os
import sys
from src.app_paths import getDatabasePath

def get_all_tables_and_schemas():
    """
    Connect to the SQLite database and get all tables and their schemas.
    Returns a dictionary with table names as keys and their schema information as values.
    """
    try:
        # Get the database path
        db_path = getDatabasePath()
        print(f"Connecting to database at: {db_path}")
        
        # Check if the database file exists
        if not os.path.exists(db_path):
            print(f"Error: Database file not found at {db_path}")
            return None
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names (excluding SQLite system tables)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
        tables = cursor.fetchall()
        
        if not tables:
            print("No tables found in the database.")
            return None
        
        # Dictionary to store table schemas
        schemas = {}
        
        # Get schema for each table
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            schemas[table_name] = columns
            
        conn.close()
        return schemas
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

def write_schemas_to_markdown(schemas):
    """
    Write the table schemas to a markdown file.
    """
    if not schemas:
        print("No schemas to write.")
        return
    
    output_path = os.path.join(os.path.dirname(getDatabasePath()), "db_schemas.md")
    
    with open(output_path, 'w') as f:
        f.write("# ATOM Database Schemas\n\n")
        f.write(f"Generated on: {os.path.basename(__file__)}\n\n")
        
        for table_name, columns in schemas.items():
            f.write(f"## {table_name}\n\n")
            f.write("| Column ID | Name | Type | NotNull | DefaultValue | PK |\n")
            f.write("|-----------|------|------|---------|--------------|----|\n")
            
            for column in columns:
                # Column format: (cid, name, type, notnull, dflt_value, pk)
                cid, name, type_name, not_null, default_val, pk = column
                # Convert None to empty string for markdown table
                default_val = default_val if default_val is not None else ""
                
                f.write(f"| {cid} | {name} | {type_name} | {not_null} | {default_val} | {pk} |\n")
            
            f.write("\n")
    
    print(f"Schema information written to: {output_path}")
    return output_path

if __name__ == "__main__":
    schemas = get_all_tables_and_schemas()
    if schemas:
        output_file = write_schemas_to_markdown(schemas)
        print(f"Total tables found: {len(schemas)}")
        print(f"Schema information written to: {output_file}")
    else:
        print("Failed to retrieve database schemas.")
