#!/usr/bin/env python3
"""
Debug script to check which columns in the export profile don't exist in the database.
This helps identify mismatches between the export profile and actual database schema.
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_export_profile_columns(export_profile_path: str = "debug/template_export_profile.xlsx"):
    """
    Check which columns in the export profile exist in the database.
    
    Args:
        export_profile_path: Path to the export profile Excel file
    """
    try:
        from src.plugins.pg.plugin_export_project_data import read_export_profile
        from src.atom.pg_database.pg_connection import get_db_connection
        
        print(f"Checking export profile: {export_profile_path}")
        print("=" * 60)
        
        # Read the export profile
        if not os.path.exists(export_profile_path):
            print(f"❌ ERROR: Export profile not found: {export_profile_path}")
            return False
            
        export_config = read_export_profile(export_profile_path)
        tables_config = export_config.get('tables', {})
        queries = export_config.get('queries', {})
        
        print(f"Found {len(tables_config)} table configurations in export profile")
        print(f"Found {len(queries)} queries in export profile")
        print()
        
        # Get database connection and check each table
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                
                # Process each query to check the corresponding table configuration
                for _, query_row in queries.iterrows():
                    table_id = query_row.get('id')
                    sheet_name = query_row.get('sheet_name')
                    
                    if table_id in ['queries', 'params', 'field_map']:
                        continue
                        
                    print(f"--- Checking Table: {table_id} (Sheet: {sheet_name}) ---")
                    
                    # Get the table configuration
                    table_config = tables_config.get(table_id, pd.DataFrame())
                    if table_config.empty:
                        print(f"⚠️  WARNING: No table configuration found for {table_id}")
                        continue
                    
                    # Get fields from export profile
                    profile_fields = []
                    for _, row in table_config.iterrows():
                        field = row.get('field')
                        do_not_export = row.get('do_not_export')
                        if field and (do_not_export is None or do_not_export != 1):
                            profile_fields.append(field)
                    
                    print(f"Export profile defines {len(profile_fields)} fields for {table_id}")
                    
                    # Get actual database columns
                    if table_id.startswith('public.'):
                        # Direct table query
                        table_name = table_id.split('.')[1]
                        cursor.execute("""
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_schema = 'public' AND table_name = %s
                            ORDER BY ordinal_position
                        """, (table_name,))
                    else:
                        # For views or functions, we'll try to get columns differently
                        # This is more complex and might not work for all cases
                        print(f"⚠️  WARNING: Cannot easily check columns for {table_id} (not a simple table)")
                        continue
                    
                    db_columns = [row[0] for row in cursor.fetchall()]
                    print(f"Database has {len(db_columns)} columns for {table_id}")
                    
                    # Compare fields
                    profile_set = set(profile_fields)
                    db_set = set(db_columns)
                    
                    # Fields in profile but not in database
                    missing_in_db = profile_set - db_set
                    if missing_in_db:
                        print(f"❌ Fields in export profile but NOT in database:")
                        for field in sorted(missing_in_db):
                            print(f"   - {field}")
                    
                    # Fields in database but not in profile
                    missing_in_profile = db_set - profile_set
                    if missing_in_profile:
                        print(f"ℹ️  Fields in database but NOT in export profile:")
                        for field in sorted(missing_in_profile):
                            print(f"   - {field}")
                    
                    if not missing_in_db and not missing_in_profile:
                        print("✅ All fields match between export profile and database")
                    
                    print()
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_export_profile_columns(export_profile_path: str = "debug/template_export_profile.xlsx"):
    """
    Create a fixed version of the export profile by removing non-existent columns.
    
    Args:
        export_profile_path: Path to the export profile Excel file
    """
    try:
        from src.plugins.pg.plugin_export_project_data import read_export_profile
        from src.atom.pg_database.pg_connection import get_db_connection
        
        print(f"Creating fixed export profile from: {export_profile_path}")
        print("=" * 60)
        
        # Read the original export profile
        if not os.path.exists(export_profile_path):
            print(f"❌ ERROR: Export profile not found: {export_profile_path}")
            return False
            
        # Read all sheets from the original file
        excel_file = pd.ExcelFile(export_profile_path)
        
        # Create output path
        output_path = export_profile_path.replace('.xlsx', '_fixed.xlsx')
        
        # Get database connection
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                
                # Create a new Excel writer
                with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                    
                    # Copy params, field_map, and queries sheets as-is
                    for sheet_name in ['params', 'field_map', 'queries']:
                        if sheet_name in excel_file.sheet_names:
                            df = pd.read_excel(excel_file, sheet_name=sheet_name)
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            print(f"✅ Copied {sheet_name} sheet")
                    
                    # Process table configuration sheets
                    for sheet_name in excel_file.sheet_names:
                        if sheet_name not in ['params', 'field_map', 'queries']:
                            print(f"--- Processing table config: {sheet_name} ---")
                            
                            table_df = pd.read_excel(excel_file, sheet_name=sheet_name)
                            
                            # Get database columns for this table
                            if sheet_name.startswith('public.'):
                                table_name = sheet_name.split('.')[1]
                                cursor.execute("""
                                    SELECT column_name 
                                    FROM information_schema.columns 
                                    WHERE table_schema = 'public' AND table_name = %s
                                """, (table_name,))
                                db_columns = set(row[0] for row in cursor.fetchall())
                                
                                # Filter out non-existent columns
                                original_count = len(table_df)
                                table_df = table_df[table_df['field'].isin(db_columns)]
                                filtered_count = len(table_df)
                                
                                removed_count = original_count - filtered_count
                                if removed_count > 0:
                                    print(f"⚠️  Removed {removed_count} non-existent columns from {sheet_name}")
                                else:
                                    print(f"✅ All columns in {sheet_name} exist in database")
                            else:
                                print(f"⚠️  Skipping validation for {sheet_name} (not a simple table)")
                            
                            # Save the filtered table configuration
                            table_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n✅ Fixed export profile saved to: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to run the diagnostic."""
    print("Export Profile Column Checker")
    print("=" * 40)
    
    # Check if export profile exists
    export_profile = "debug/template_export_profile.xlsx"
    
    if not os.path.exists(export_profile):
        print(f"❌ Export profile not found: {export_profile}")
        print("Please run the plugin_postgres_generate_export_profile plugin first to create it.")
        return False
    
    # Run the column check
    print("1. Checking export profile columns against database...")
    success = check_export_profile_columns(export_profile)
    
    if success:
        print("\n2. Creating fixed export profile...")
        fix_export_profile_columns(export_profile)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
