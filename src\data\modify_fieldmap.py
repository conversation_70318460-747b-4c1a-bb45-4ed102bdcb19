
import json

filename = "src/data/fieldmap.json"


def revert_field_map_names():
    with open(filename, "r") as f:
        data = json.load(f)
        for a in ["fields", "ancillary_fields", "rfq_fields"]:
            for k, v in data[a].items():
                v["display"] = v["default"]
    with open(filename, "w") as f:
        json.dump(data, f, indent=4)
    return data


if __name__ == "__main__":
    revert_field_map_names()