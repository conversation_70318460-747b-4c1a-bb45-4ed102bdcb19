"""
Mapping between table names and payload names

And reversed mapping
"""


TABLE_TO_PAYLOAD = {
    "bom": "bom_data",
    "general": "general_data",
    "spool": "spool_data",
    "spec": "spec_data",
    "outlier": "outlier_data",
    "rfq": "rfq_data",
    "ifc": "ifc_data",
    "generic 1": "generic_1_data",
    "generic 2": "generic_2_data",
}

PAYLOAD_TO_TABLE = {v: k for k, v in TABLE_TO_PAYLOAD.items()}


def get_payload_name(table_name: str) -> str:
    return TABLE_TO_PAYLOAD.get(table_name.lower())

def get_table_from_payload(payload_name: str) -> str:
    return PAYLOAD_TO_TABLE.get(payload_name)