from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor

with get_db_cursor(DatabaseConfig()) as cursor:
    cursor.execute("SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'atem_bom_component_mapping' ORDER BY ordinal_position")
    columns = cursor.fetchall()
    print('Column Name | Data Type | Nullable')
    print('-' * 50)
    for col in columns:
        print(f"{col['column_name']} | {col['data_type']} | {col['is_nullable']}")
