"""
Sample printer plugin that demonstrates the plugin dialog features.
"""

def plugin_hello(name: str = "World", show_exclamation: bool = True) -> str:
    """
    A simple plugin that prints and returns a hello message.

    This plugin demonstrates how docstrings and parameters are displayed
    in the enhanced plugin dialog.

    Args:
        name: The name to greet (default: "World")
        show_exclamation: Whether to add an exclamation mark (default: True)

    Returns:
        A greeting message as a string
    """
    greeting = f"Hello, {name}"
    if show_exclamation:
        greeting += "!"

    print(f"Executing greeting plugin...")
    print(f"Generated message: {greeting}")

    return greeting

def plugin_calculate(a: int, b: int, operation: str = "add") -> int:
    """
    Performs a basic calculation on two numbers.

    This plugin demonstrates how multiple parameters with type annotations
    are displayed in the plugin dialog.

    Args:
        a: First number
        b: Second number
        operation: The operation to perform (add, subtract, multiply, divide)

    Returns:
        The result of the calculation
    """
    print(f"Calculating {a} {operation} {b}")

    if operation == "add":
        result = a + b
    elif operation == "subtract":
        result = a - b
    elif operation == "multiply":
        result = a * b
    elif operation == "divide":
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = a / b
    else:
        raise ValueError(f"Unknown operation: {operation}")

    print(f"Result: {result}")
    return result