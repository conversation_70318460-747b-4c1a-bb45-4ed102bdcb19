import os
import cv2
import fitz  # PyMuPDF
import numpy as np
import ast
import io

from PIL import Image
from src.utils.pdf.page_to_opencv import page_to_opencv


def blur_and_overlay_cv2(image, rect, blur_radius=21, zoom=2):
    """
    Apply a Gaussian blur to a specific region of an OpenCV image.

    Args:
        image: OpenCV image (numpy array)
        rect: Rectangle coordinates [x0, y0, x1, y1] in PDF coordinates
        blur_radius: Size of the Gaussian blur kernel
        zoom: Zoom factor used when rendering the PDF page

    Returns:
        Modified OpenCV image with the blurred region
    """
    # Make a copy of the image to ensure it's writable
    img = image.copy()

    # Calculate the coordinates of the rectangle in the image
    # We need to scale the coordinates by the zoom factor
    x0, y0, x1, y1 = rect[0] * zoom, rect[1] * zoom, rect[2] * zoom, rect[3] * zoom

    # Ensure coordinates are integers and within bounds
    height, width = img.shape[:2]
    x0, y0 = max(0, int(x0)), max(0, int(y0))
    x1, y1 = min(width, int(x1)), min(height, int(y1))

    # Extract the region we want to blur
    if x1 > x0 and y1 > y0:  # Ensure valid rectangle
        # Apply Gaussian blur to the region
        region = img[y0:y1, x0:x1].copy()
        if region.size > 0:  # Ensure region is not empty
            blurred_region = cv2.GaussianBlur(region, (blur_radius, blur_radius), 0)
            img[y0:y1, x0:x1] = blurred_region

    return img


def replace_page_with_image(doc, page_num, image, image_quality=85):
    """
    Replace a page in a PDF document with an OpenCV image.

    Args:
        doc: PyMuPDF document
        page_num: Page number to replace
        image: OpenCV image (numpy array)
        image_quality: JPEG compression quality (1-100, higher is better quality but larger size)

    Returns:
        The new page object
    """
    # Get the page to be replaced
    page = doc[page_num]
    page.remove_rotation()

    # Get original page properties
    original_rotation = page.rotation
    original_mediabox = page.mediabox

    # Convert OpenCV image to a format PyMuPDF can use
    # OpenCV uses BGR, PIL uses RGB
    if image.shape[2] == 3:
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    else:
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGRA2RGB)

    # Use JPEG compression for smaller file size
    pil_img = Image.fromarray(image_rgb)
    buf = io.BytesIO()
    # Use JPEG format with quality setting for compression
    pil_img.save(buf, format="JPEG", quality=image_quality, optimize=True)
    buf.seek(0)

    # Delete the current page
    doc.delete_page(page_num)

    # Insert a new page at the same position
    new_page = doc.new_page(page_num,
                           width=original_mediabox.width,
                           height=original_mediabox.height)

    # Add the image to the new page, covering the entire page
    new_page.insert_image(new_page.rect, stream=buf.read())

    # Set the rotation to match the original page
    if original_rotation != 0:
        new_page.set_rotation(original_rotation)

    return new_page


def plugin_blur_pdf(input_file=r"C:/Drawings/All Revisions-New ISOs 2.7.25.pdf",
                    save_file: str = "debug/pdf_blurred_regions_not_redacted.pdf",
                    blur_images: bool = True,
                    blur_regions: list = "[[0,600,1500,1000], [800,500,1500, 800], [0,600,260,1000],[0,600,240,1000]]",
                    blur_radius: int = 21,
                    zoom = 2,
                    image_quality: int = 85,
                    compress_pdf: bool = True):
    """
    Blur regions of the PDF using OpenCV.

    Args:
        input_file: Path to the input PDF file
        save_file: Path to save the output PDF file
        blur_images: Whether to blur images found in the PDF
        blur_regions: List of regions to blur, each as [x0, y0, x1, y1]
        blur_radius: Size of the Gaussian blur kernel (must be odd)
        zoom: Zoom factor for rendering the PDF (higher = better quality but larger file)
        image_quality: JPEG compression quality (1-100, higher is better quality but larger file)
        compress_pdf: Whether to apply additional PDF compression when saving

    WARNING - THIS DOES NOT REDACT SENSITIVE INFORMATION EMBEDDED
    """
    os.makedirs(os.path.dirname(save_file), exist_ok=True)

    if blur_regions:
        blur_regions = ast.literal_eval(blur_regions)
    else:
        blur_regions = []

    if blur_radius % 2 == 0:
        return "Blur radius must be an odd number"

    # Open the input file
    doc = fitz.open(input_file)

    for i, page in enumerate(doc):
        # Convert the page to an OpenCV image
        image = page_to_opencv(page, zoom)

        # Process all regions to blur
        regions_to_blur = []

        # Optional: blur images on page
        if blur_images:
            for img_info in page.get_images(full=True):
                xref = img_info[0]
                try:
                    bbox = page.get_image_bbox(xref)
                    print(f"xref: {xref}, bbox: {bbox}")
                    regions_to_blur.append([int(coord) for coord in bbox])
                except RuntimeError as e:
                    print(f"Warning: Skipping xref {xref}: {e}")
                    continue

        # Add user-specified rectangles
        regions_to_blur.extend(blur_regions)

        # Apply blur to all regions in a single pass
        for rect in regions_to_blur:
            image = blur_and_overlay_cv2(image, rect, blur_radius, zoom)

        # Replace the page with the modified image
        replace_page_with_image(doc, i, image, image_quality)

        # if i > 10:
        #     break

    # Save the modified document with compression options
    if compress_pdf:
        # Use garbage collection to reduce file size
        doc.save(save_file, garbage=3, deflate=True, clean=True)
    else:
        doc.save(save_file)

    print(f"Blurred PDF saved to: {save_file}")
    return f"Blurred PDF saved to: {save_file}"