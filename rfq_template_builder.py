"""
RFQ Template Builder - Consolidated Workflow
============================================

This file consolidates the workflow for building a final RFQ Template by running
multiple processing steps in sequence. Each step can be enabled/disabled with flags.

Workflow Steps:
1. Create Combined Workbook (misc_help.py) - Combines multiple Excel files into one
2. Check Material Descriptions in Database (src/atom/pg_database/misc.py) - Validates against verified materials
3. Normalize Descriptions (unit_tests/normalize_description.py) - Extracts and normalizes material properties
4. Create RFQ Template (unit_tests/rfq_template.py) - Creates final template with validation dropdowns

Key Features:
- Uses first worksheet approach (follows data upload auditing pattern)
- Configurable flags to enable/disable each step
- Robust error handling and import management
- Automatic deduplication of material descriptions
- Validation dropdowns for classification fields
- Progress tracking and detailed output

Usage Instructions:
1. Update the input_excel_path variable to point to your Excel file
2. Update the output_dir variable to your desired output location
3. Set the step flags (run_step1_create_combined, etc.) to True/False as needed
4. Configure options for each step (combine_bom, enable_validation, etc.)
5. Run the script: python rfq_template_builder.py

Requirements:
- All referenced modules must be available in the specified paths
- Database connection required for Step 2 (will skip if unavailable)
- Input Excel file must contain 'material_description' column
"""

import os
import sys
import pandas as pd
from datetime import datetime
from pathlib import Path

# Add necessary paths to sys.path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / "src" / "atom" / "pg_database"))
sys.path.append(str(current_dir / "unit_tests"))

# Import required modules
try:
    from misc_help import create_combined_workbook
    print("✓ Successfully imported misc_help")
except ImportError as e:
    print(f"⚠ Warning: Could not import misc_help: {e}")

try:
    from src.atom.pg_database.misc import (
        check_material_descriptions_in_db,
        load_dataframe_from_excel,
        export_dataframe_to_excel,
        test_database_connection,
        DatabaseConfig
    )
    print("✓ Successfully imported database functions")
except ImportError as e:
    print(f"⚠ Warning: Could not import database functions: {e}")

try:
    from unit_tests.normalize_description import (
        normalize_description,
        extract_metadata_tags_to_columns
    )
    print("✓ Successfully imported normalization functions")
except ImportError as e:
    print(f"⚠ Warning: Could not import normalization functions: {e}")

try:
    from unit_tests.rfq_template import load_data_to_template, RFQ_COLUMNS
    print("✓ Successfully imported RFQ template functions")
except ImportError as e:
    print(f"⚠ Warning: Could not import RFQ template functions: {e}")

def load_excel_first_sheet(file_path):
    """
    Load the first worksheet from an Excel file, regardless of sheet name.
    This follows the data upload auditing pattern of using the first worksheet.
    Also handles the critical column renaming from 'ansme_ansi' to 'technical_standard'.
    """
    try:
        # Read the first sheet (index 0)
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"Loaded first worksheet with {len(df)} rows and {len(df.columns)} columns")

        # Show some column names for debugging
        print(f"Sample columns: {list(df.columns[:10])}")

        # CRITICAL: Rename 'ansme_ansi' to 'technical_standard' before any processing
        if 'ansme_ansi' in df.columns:
            print("✓ Found 'ansme_ansi' column - renaming to 'technical_standard'")
            df = df.rename(columns={'ansme_ansi': 'technical_standard'})
            print("✓ Column renamed successfully")
        else:
            print("ℹ 'ansme_ansi' column not found in data")
            # Check if technical_standard already exists
            if 'technical_standard' in df.columns:
                print("✓ 'technical_standard' column already exists")
            else:
                print("ℹ Neither 'ansme_ansi' nor 'technical_standard' found")

        # Final verification
        if 'technical_standard' in df.columns:
            non_null_count = df['technical_standard'].notna().sum()
            print(f"✓ 'technical_standard' column available with {non_null_count} non-null values")

        return df
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        raise

def rename_columns_in_file(file_path):
    """
    Rename 'ansme_ansi' to 'technical_standard' in an Excel file
    Returns the modified DataFrame
    """
    df = load_excel_first_sheet(file_path)
    if 'ansme_ansi' in df.columns:
        print(f"Renaming 'ansme_ansi' to 'technical_standard' in {os.path.basename(file_path)}")
        df = df.rename(columns={'ansme_ansi': 'technical_standard'})
    return df

def run_create_combined_workbook(input_excel_path, output_dir, output_filename,
                                combine_general=False, combine_bom=True,
                                combine_spec=False, combine_rfq=True):
    """
    Step 1: Create combined workbook from input Excel file(s)
    Uses the create_combined_workbook function from misc_help.py
    Handles both single files (with column renaming) and multiple files (combining)
    """
    print("=" * 60)
    print("STEP 1: Creating Combined Workbook")
    print("=" * 60)

    try:
        # Import here to handle any import issues gracefully
        try:
            from misc_help import create_combined_workbook
        except ImportError:
            # Try alternative import path
            sys.path.append(str(Path(__file__).parent))
            from misc_help import create_combined_workbook

        # Handle both single file and multiple files
        if isinstance(input_excel_path, list):
            # Multiple files - use original combining logic
            file_list = input_excel_path

            # Validate all files exist
            for file_path in file_list:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"Input file not found: {file_path}")

            # Create file lists for each type
            general_files = file_list if combine_general else []
            bom_files = file_list if combine_bom else []
            spec_files = file_list if combine_spec else []
            rfq_files = file_list if combine_rfq else []

            # Call the function
            create_combined_workbook(
                output_path=output_dir,
                output_filename=output_filename,
                combine_general=combine_general,
                general_files=general_files,
                combine_bom=combine_bom,
                combine_rfq=combine_rfq,
                bom_files=bom_files,
                combine_spec=combine_spec,
                spec_files=spec_files,
                rfq_files=rfq_files
            )

        else:
            # Single file - rename columns and save as "combined" file
            if not os.path.exists(input_excel_path):
                raise FileNotFoundError(f"Input file not found: {input_excel_path}")

            print(f"Processing single file: {os.path.basename(input_excel_path)}")
            df = rename_columns_in_file(input_excel_path)

            # Save the processed file
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, output_filename)

            # Create Excel writer and save to appropriate sheet
            with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                if combine_general:
                    df.to_excel(writer, sheet_name='General', index=False)
                if combine_bom:
                    df.to_excel(writer, sheet_name='BOM', index=False)
                if combine_spec:
                    df.to_excel(writer, sheet_name='Spec', index=False)
                if combine_rfq:
                    df.to_excel(writer, sheet_name='RFQ', index=False)

                # If no specific sheet selected, save to first sheet
                if not any([combine_general, combine_bom, combine_spec, combine_rfq]):
                    df.to_excel(writer, sheet_name='Data', index=False)

        output_path = os.path.join(output_dir, output_filename)
        print(f"✓ Combined workbook created: {output_path}")
        return output_path

    except Exception as e:
        print(f"✗ Error in Step 1: {e}")
        raise

def run_material_check(input_excel_path, output_dir, sheet_name=None):
    """
    Step 2: Check material descriptions against database
    Uses functions from src/atom/pg_database/misc.py
    """
    print("=" * 60)
    print("STEP 2: Checking Material Descriptions in Database")
    print("=" * 60)

    try:
        # Import here to handle any import issues gracefully
        try:
            from src.atom.pg_database.misc import (
                check_material_descriptions_in_db,
                load_dataframe_from_excel,
                export_dataframe_to_excel,
                test_database_connection,
                DatabaseConfig
            )
        except ImportError:
            # Try alternative import paths
            sys.path.append(str(Path(__file__).parent / "src" / "atom" / "pg_database"))
            from misc import (
                check_material_descriptions_in_db,
                load_dataframe_from_excel,
                export_dataframe_to_excel,
                test_database_connection,
                DatabaseConfig
            )
        
        # Test database connection first
        db_config = DatabaseConfig()
        if not test_database_connection(db_config):
            print("⚠ Database connection failed. Skipping material check.")
            return input_excel_path  # Return input path to continue workflow
        
        # Load data - use first worksheet approach (includes column renaming)
        print(f"Loading data from: {input_excel_path}")
        if sheet_name is None:
            # Use our custom function to load first sheet (handles column renaming)
            df = load_excel_first_sheet(input_excel_path)
        else:
            df = load_dataframe_from_excel(input_excel_path, sheet_name)
            # Also handle column renaming for non-first sheet loads
            if 'ansme_ansi' in df.columns:
                print("✓ Renaming 'ansme_ansi' column to 'technical_standard'")
                df = df.rename(columns={'ansme_ansi': 'technical_standard'})
        
        # Check material descriptions
        result_df = check_material_descriptions_in_db(
            input_df=df,
            material_description_column='material_description',
            db_config=db_config,
            output_column='exists_in_verified_materials'
        )
        
        # Export results
        output_filename = "Stage2_Material_Check.xlsx"
        output_path = os.path.join(output_dir, output_filename)
        export_dataframe_to_excel(result_df, output_path, 'Results')
        
        print(f"✓ Material check completed: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"✗ Error in Step 2: {e}")
        print("Continuing with original file...")
        return input_excel_path  # Return input to continue workflow

def run_normalize_descriptions(input_excel_path, output_dir):
    """
    Step 3: Normalize descriptions
    Uses functions from unit_tests/normalize_description.py
    """
    print("=" * 60)
    print("STEP 3: Normalizing Descriptions")
    print("=" * 60)

    try:
        # Import here to handle any import issues gracefully
        try:
            from unit_tests.normalize_description import (
                normalize_description,
                extract_metadata_tags_to_columns
            )
        except ImportError:
            # Try alternative import paths
            sys.path.append(str(Path(__file__).parent / "unit_tests"))
            from normalize_description import (
                normalize_description,
                extract_metadata_tags_to_columns
            )
        
        # Load data - use first worksheet approach (includes automatic column renaming)
        print(f"Loading data from: {input_excel_path}")
        df = load_excel_first_sheet(input_excel_path)

        # Verify the column renaming worked
        if 'technical_standard' in df.columns:
            print("✓ 'technical_standard' column is available for processing")
        else:
            print("ℹ 'technical_standard' column not found - may not be needed for this data")
        
        # Normalize descriptions
        print("Normalizing descriptions and generating metadata...")
        normalized_results = df['material_description'].apply(normalize_description)
        
        # Split results into separate columns
        df['normalized_description'] = normalized_results.apply(lambda x: x[0])
        df['metadata_tags'] = normalized_results.apply(lambda x: ','.join(x[1]) if x[1] else '')
        df['review_tags'] = normalized_results.apply(lambda x: ','.join(x[2]) if x[2] else '')
        df['match_type'] = normalized_results.apply(lambda x: ','.join(x[3]) if x[3] else '')
        
        # Extract metadata tags to columns
        print("Extracting metadata tags to separate columns...")
        df = extract_metadata_tags_to_columns(df)
        
        # Format ASTM values to uppercase
        def astm_format(val):
            if val is None:
                return None
            if isinstance(val, str):
                val = val.strip()
                return val.upper() if val else None
            return val
        
        for col in ['extracted_astm', 'astm']:
            if col in df.columns:
                print(f"Formatting {col} values to uppercase...")
                df[col] = df[col].apply(astm_format)
        
        # Normalize valve types if rfq_scope column exists
        if 'rfq_scope' in df.columns:
            try:
                print("Normalizing valve types for items classified as 'Valves'...")
                try:
                    from unit_tests.normalize_valve_type import normalize_valve_types_dataframe
                except ImportError:
                    sys.path.append(str(Path(__file__).parent / "unit_tests"))
                    from normalize_valve_type import normalize_valve_types_dataframe
                df = normalize_valve_types_dataframe(df)
            except ImportError:
                print("Warning: normalize_valve_type module not found. Skipping valve type normalization.")
        else:
            print("Warning: 'rfq_scope' column not found. Skipping valve type normalization.")
        
        # Save results
        output_filename = "Stage3_Normalized.xlsx"
        output_path = os.path.join(output_dir, output_filename)
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        df.to_excel(output_path, index=False)
        
        print(f"✓ Description normalization completed: {output_path}")
        return output_path, df
        
    except Exception as e:
        print(f"✗ Error in Step 3: {e}")
        raise

def run_create_rfq_template(df, output_dir, enable_validation=True, deduplicate_materials=True):
    """
    Step 4: Create RFQ Template
    Uses functions from unit_tests/rfq_template.py
    """
    print("=" * 60)
    print("STEP 4: Creating RFQ Template")
    print("=" * 60)

    try:
        # Import here to handle any import issues gracefully
        try:
            from unit_tests.rfq_template import load_data_to_template, RFQ_COLUMNS
        except ImportError:
            # Try alternative import paths
            sys.path.append(str(Path(__file__).parent / "unit_tests"))
            from rfq_template import load_data_to_template, RFQ_COLUMNS
        
        # Deduplicate material descriptions if enabled
        if deduplicate_materials and 'material_description' in df.columns:
            original_count = len(df)
            print(f"Deduplicating material descriptions (case-insensitive)...")
            
            # Convert to lowercase for comparison but keep original values
            df['material_description_lower'] = df['material_description'].astype(str).str.lower()
            df = df.drop_duplicates(subset=['material_description_lower'])
            df = df.drop(columns=['material_description_lower'])
            
            deduplicated_count = len(df)
            removed_count = original_count - deduplicated_count
            print(f"Deduplication complete: {removed_count} duplicate rows removed ({original_count} -> {deduplicated_count} rows)")
        
        # Create RFQ template with data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"RFQ_Template_Final_{timestamp}.xlsx"
        output_path = os.path.join(output_dir, output_filename)
        
        validation_options = load_data_to_template(
            df=df,
            output_path=output_path,
            enable_validation=enable_validation
        )
        
        print(f"✓ RFQ Template created: {output_path}")
        print(f"  - Total rows: {len(df)}")
        print(f"  - Total columns: {len(RFQ_COLUMNS)}")
        if enable_validation:
            print(f"  - Validation fields: {len(validation_options)}")
        
        return output_path
        
    except Exception as e:
        print(f"✗ Error in Step 4: {e}")
        raise

# =============================================================================
# EASY CONFIGURATION SECTION - MODIFY THESE VALUES
# =============================================================================

# INPUT/OUTPUT PATHS - UPDATE THESE TO YOUR ACTUAL PATHS
INPUT_EXCEL_PATH = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 055 - Bluebonnet field route Isos\data\4 - classified\rfq - classified.xlsx"
OUTPUT_DIR = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 055\Workspace"

# STEP FLAGS - Set to True/False to enable/disable each step
RUN_STEP1_CREATE_COMBINED = False    # Create combined workbook
RUN_STEP2_MATERIAL_CHECK = True     # Check materials in database
RUN_STEP3_NORMALIZE = True          # Normalize descriptions
RUN_STEP4_RFQ_TEMPLATE = True       # Create final RFQ template

# STEP 1 OPTIONS - What to combine from input file
COMBINE_GENERAL = False
COMBINE_BOM = True
COMBINE_SPEC = False
COMBINE_RFQ = True

# STEP 4 OPTIONS - RFQ Template settings
ENABLE_VALIDATION = True        # Add dropdown validation
DEDUPLICATE_MATERIALS = True    # Remove duplicate material descriptions

# =============================================================================

def main():
    """
    Main function to run the consolidated RFQ Template Builder workflow
    """
    print("RFQ Template Builder - Consolidated Workflow")
    print("=" * 60)

    # Use the configuration variables defined above
    input_excel_path = INPUT_EXCEL_PATH
    output_dir = OUTPUT_DIR
    run_step1_create_combined = RUN_STEP1_CREATE_COMBINED
    run_step2_material_check = RUN_STEP2_MATERIAL_CHECK
    run_step3_normalize = RUN_STEP3_NORMALIZE
    run_step4_rfq_template = RUN_STEP4_RFQ_TEMPLATE
    combine_general = COMBINE_GENERAL
    combine_bom = COMBINE_BOM
    combine_spec = COMBINE_SPEC
    combine_rfq = COMBINE_RFQ
    enable_validation = ENABLE_VALIDATION
    deduplicate_materials = DEDUPLICATE_MATERIALS
    
    # =============================================================================
    # VALIDATION AND ERROR HANDLING
    # =============================================================================

    # Validate input file exists
    if not os.path.exists(input_excel_path):
        print(f"❌ ERROR: Input file does not exist: {input_excel_path}")
        print("Please update the 'input_excel_path' variable to point to your actual Excel file.")
        return False
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # =============================================================================
    # WORKFLOW EXECUTION
    # =============================================================================
    
    current_file = input_excel_path
    df = None
    
    try:
        # Step 1: Create Combined Workbook
        if run_step1_create_combined:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            combined_filename = f"Stage1_Combined_{timestamp}.xlsx"
            current_file = run_create_combined_workbook(
                input_excel_path=current_file,
                output_dir=output_dir,
                output_filename=combined_filename,
                combine_general=combine_general,
                combine_bom=combine_bom,
                combine_spec=combine_spec,
                combine_rfq=combine_rfq
            )
        
        # Step 2: Material Check
        if run_step2_material_check:
            current_file = run_material_check(
                input_excel_path=current_file,
                output_dir=output_dir,
                sheet_name=None  # Use first sheet
            )
        
        # Step 3: Normalize Descriptions
        if run_step3_normalize:
            current_file, df = run_normalize_descriptions(
                input_excel_path=current_file,
                output_dir=output_dir
            )
        
        # Step 4: Create RFQ Template
        if run_step4_rfq_template:
            if df is None:
                # Load the data if we skipped step 3 - use first worksheet approach
                print("Loading data for RFQ template creation...")
                df = load_excel_first_sheet(current_file)

            final_template_path = run_create_rfq_template(
                df=df,
                output_dir=output_dir,
                enable_validation=enable_validation,
                deduplicate_materials=deduplicate_materials
            )
        
        print("\n" + "=" * 60)
        print("WORKFLOW COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"📁 Output Directory: {output_dir}")
        print(f"📊 Final RFQ Template: {os.path.basename(final_template_path)}")

        # Print summary of what was created
        print("\n📋 Files Created:")
        for file in os.listdir(output_dir):
            if file.endswith('.xlsx'):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  • {file} ({file_size:.1f} KB)")

        print(f"\n🎯 Next Steps:")
        print(f"  1. Open the final RFQ template: {os.path.basename(final_template_path)}")
        print(f"  2. Review the data and use dropdown validation for classification")
        print(f"  3. The template includes all {len(RFQ_COLUMNS) if 'RFQ_COLUMNS' in globals() else '47'} required columns")

    except Exception as e:
        print(f"\n✗ Workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def validate_configuration():
    """
    Validate the configuration before running the workflow
    """
    errors = []

    if not os.path.exists(INPUT_EXCEL_PATH):
        errors.append(f"Input file does not exist: {INPUT_EXCEL_PATH}")

    if not INPUT_EXCEL_PATH.endswith(('.xlsx', '.xls')):
        errors.append(f"Input file must be an Excel file (.xlsx or .xls): {INPUT_EXCEL_PATH}")

    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
    except Exception as e:
        errors.append(f"Cannot create output directory {OUTPUT_DIR}: {e}")

    if errors:
        print("❌ Configuration Errors:")
        for error in errors:
            print(f"  • {error}")
        print("\nPlease fix these errors and try again.")
        return False

    return True

if __name__ == "__main__":
    print("🚀 Starting RFQ Template Builder...")
    print(f"📁 Input: {os.path.basename(INPUT_EXCEL_PATH)}")
    print(f"📁 Output: {OUTPUT_DIR}")
    print()

    if not validate_configuration():
        exit(1)

    success = main()
    if success:
        print("\n🎉 RFQ Template Builder completed successfully!")
        print("Check the output directory for your files.")
    else:
        print("\n❌ RFQ Template Builder failed. Check the error messages above.")
        exit(1)
