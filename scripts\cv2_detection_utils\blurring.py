import sys
import cv2
import fitz
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                              QHBoxLayout, QPushButton, QLabel, QSpinBox,
                              QFileDialog, QComboBox, QMessageBox, QCheckBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QImage, QPixmap

class BlurringDialog(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PDF Blurring Tool")
        self.setMinimumSize(1200, 800)

        # Initialize variables
        self.doc = None
        self.current_page = None
        self.original_cv_image = None
        self.contour_image = None
        self.MAX_PAGES = 10

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Controls
        controls_layout = QHBoxLayout()

        # File selection
        self.file_btn = QPushButton("Open PDF")
        self.file_btn.clicked.connect(self.open_pdf)
        controls_layout.addWidget(self.file_btn)

        # Page selection
        self.page_combo = QComboBox()
        self.page_combo.currentIndexChanged.connect(self.page_changed)
        controls_layout.addWidget(self.page_combo)

        # Add thin line removal controls
        line_removal_layout = QHBoxLayout()
        self.remove_lines_cb = QCheckBox("Remove Thin Lines")
        self.remove_lines_cb.setChecked(False)
        self.remove_lines_cb.stateChanged.connect(self.process_image)
        line_removal_layout.addWidget(self.remove_lines_cb)

        self.line_threshold_label = QLabel("Line Thickness:")
        line_removal_layout.addWidget(self.line_threshold_label)
        self.line_threshold = QSpinBox()
        self.line_threshold.setRange(1, 20)
        self.line_threshold.setValue(2)
        self.line_threshold.valueChanged.connect(self.process_image)
        line_removal_layout.addWidget(self.line_threshold)
        controls_layout.addLayout(line_removal_layout)

        # Blur type
        self.blur_type = QComboBox()
        self.blur_type.addItems(["Gaussian Blur", "Median Blur", "Bilateral Filter"])
        self.blur_type.currentTextChanged.connect(self.process_image)
        controls_layout.addWidget(self.blur_type)

        # Kernel size
        self.kernel_label = QLabel("Kernel Size:")
        controls_layout.addWidget(self.kernel_label)
        self.kernel_size = QSpinBox()
        self.kernel_size.setRange(1, 99)
        self.kernel_size.setSingleStep(2)  # Only odd numbers
        self.kernel_size.setValue(5)
        self.kernel_size.valueChanged.connect(self.process_image)
        controls_layout.addWidget(self.kernel_size)

        # Toggle original image button
        self.toggle_btn = QPushButton("Show Original")  # Changed text to be more clear
        self.toggle_btn.setCheckable(True)
        self.toggle_btn.setChecked(True)
        self.toggle_btn.clicked.connect(self.toggle_original_image)
        self.toggle_btn.setMinimumWidth(100)  # Make button wider
        controls_layout.addWidget(self.toggle_btn)

        # Save buttons
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("Save Contour Image")
        self.save_btn.clicked.connect(self.save_contour_image)
        self.save_btn.setEnabled(False)
        buttons_layout.addWidget(self.save_btn)

        self.save_pdf_btn = QPushButton("Generate PDF")
        self.save_pdf_btn.clicked.connect(self.generate_pdf)
        self.save_pdf_btn.setEnabled(False)
        buttons_layout.addWidget(self.save_pdf_btn)

        controls_layout.addLayout(buttons_layout)
        layout.addLayout(controls_layout)

        # Image display
        image_layout = QHBoxLayout()

        # Original image
        self.original_label = QLabel("Original Image")
        self.original_label.setAlignment(Qt.AlignCenter)
        image_layout.addWidget(self.original_label)

        # Contours image
        self.contours_label = QLabel("Contours")
        self.contours_label.setAlignment(Qt.AlignCenter)
        image_layout.addWidget(self.contours_label)

        layout.addLayout(image_layout)

    def toggle_original_image(self):
        """Toggle visibility of the original image and update button text"""
        is_visible = self.toggle_btn.isChecked()
        self.original_label.setVisible(is_visible)
        self.toggle_btn.setText("Hide Original" if is_visible else "Show Original")

    def open_pdf(self):
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open PDF", "", "PDF Files (*.pdf)")
        if filename:
            self.doc = fitz.open(filename)
            self.page_combo.clear()
            page_count = min(self.doc.page_count, self.MAX_PAGES)
            self.page_combo.addItems([f"Page {i+1}" for i in range(page_count)])
            self.page_changed(0)
            self.save_pdf_btn.setEnabled(True)

    def page_changed(self, index):
        if self.doc is None:
            return

        # Get page
        page = self.doc[index]

        # Convert to cv2 image
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        self.original_cv_image = img
        self.process_image()

    def remove_thin_lines(self, image):
        """Remove thin lines from the image using morphological operations"""
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()

        # Create binary image
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)

        # Create kernel for morphological operations
        thickness = self.line_threshold.value()
        kernel = np.ones((thickness, thickness), np.uint8)

        # Remove thin lines using morphological opening
        opened = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # Invert back
        result = cv2.bitwise_not(opened)

        # Convert back to RGB if input was RGB
        if len(image.shape) == 3:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)

        return result

    def process_image(self):
        if self.original_cv_image is None:
            return

        # Start with a copy of the original image
        processed_image = self.original_cv_image.copy()

        # Remove thin lines if enabled
        if self.remove_lines_cb.isChecked():
            processed_image = self.remove_thin_lines(processed_image)

        # Get kernel size (must be odd)
        k_size = self.kernel_size.value()
        if k_size % 2 == 0:
            k_size += 1
            self.kernel_size.setValue(k_size)

        # Apply blur based on selected type
        blur_type = self.blur_type.currentText()
        if blur_type == "Gaussian Blur":
            blurred = cv2.GaussianBlur(processed_image, (k_size, k_size), 0)
        elif blur_type == "Median Blur":
            blurred = cv2.medianBlur(processed_image, k_size)
        else:  # Bilateral Filter
            blurred = cv2.bilateralFilter(processed_image, k_size, 75, 75)

        # Find contours
        gray = cv2.cvtColor(blurred, cv2.COLOR_RGB2GRAY)
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # Draw contours
        self.contour_image = self.original_cv_image.copy()
        cv2.drawContours(self.contour_image, contours, -1, (0, 255, 0), 2)

        # Add text annotation
        current_page = self.page_combo.currentIndex() + 1
        text = f"Page: {current_page} | Method: {blur_type} | Kernel: {k_size}"
        if self.remove_lines_cb.isChecked():
            text += f" | Line Removal: {self.line_threshold.value()}"
        cv2.putText(self.contour_image, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
                    1, (0, 0, 255), 2)

        # Update display
        self.update_image_label(self.original_label, self.original_cv_image)
        self.update_image_label(self.contours_label, self.contour_image)
        self.save_btn.setEnabled(True)

    def generate_pdf(self):
        if self.doc is None:
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save PDF", "", "PDF Files (*.pdf)")

        if filename:
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'

            # Create new PDF
            output_doc = fitz.open()
            page_count = min(self.doc.page_count, self.MAX_PAGES)

            for i in range(page_count):
                # Process each page
                self.page_combo.setCurrentIndex(i)
                self.process_image()

                # Convert contour image to PDF page
                img_bytes = cv2.imencode('.png', cv2.cvtColor(self.contour_image, cv2.COLOR_RGB2BGR))[1].tobytes()
                img_doc = fitz.open("png", img_bytes)
                pdfbytes = img_doc.convert_to_pdf()
                img_pdf = fitz.open("pdf", pdfbytes)
                output_doc.insert_pdf(img_pdf)

            # Save PDF
            output_doc.save(filename)
            output_doc.close()
            QMessageBox.information(self, "Success", f"PDF saved to:\n{filename}")

    def save_contour_image(self):
        if self.contour_image is None:
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Contour Image", "", "PNG Files (*.png);;All Files (*)")

        if filename:
            if not filename.lower().endswith('.png'):
                filename += '.png'
            cv2.imwrite(filename, cv2.cvtColor(self.contour_image, cv2.COLOR_RGB2BGR))
            QMessageBox.information(self, "Success", f"Image saved to:\n{filename}")

    def update_image_label(self, label, cv_image):
        height, width = cv_image.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(cv_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

        # Scale to fit label while maintaining aspect ratio
        scaled_pixmap = QPixmap.fromImage(q_img).scaled(
            label.width(), label.height(),
            Qt.KeepAspectRatio, Qt.SmoothTransformation)
        label.setPixmap(scaled_pixmap)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self.original_cv_image is not None:
            self.process_image()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BlurringDialog()
    window.show()
    sys.exit(app.exec())

