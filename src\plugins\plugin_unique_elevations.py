import fitz
import pandas as pd

def plugin_unique_elevations(raw_file, save_file: str = "debug/unique_elevations.xlsx"):
    """
    Filters raw data from preprocessed data to unique elevations
    """
    try:
        raw_df = pd.read_excel(raw_file)
    except Exception as e:
        raw_df = pd.read_feather(raw_file)

    print(raw_df["elevation"])

    elevations = raw_df[raw_df["elevation"] != ""]["value"].unique()
    print(elevations.tolist())

    if save_file:
        elevations = pd.DataFrame(elevations, columns=["elevation"])
        elevations = elevations.sort_values(by="elevation")
        elevations.to_excel(save_file, index=False)

    return elevations
