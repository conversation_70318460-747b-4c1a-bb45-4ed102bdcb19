"""
File and folder paths for ATEM
For convenience, make the directories every time (if they dont exist) the dir needs access

Related issues using Windows paths

https://github.com/platformdirs/platformdirs/issues/90
https://github.com/python/cpython/issues/85368


"""
import sys
import os
import json
import logging
from os import path, makedirs
from platformdirs import user_config_dir, user_data_dir
from PySide6.QtCore import QStandardPaths
import unicodedata
import re
import fitz
from PySide6.QtGui import *

import platformdirs

sys.path[0] = ""

# logger = logging.getLogger(__name__)

# Do not use user_data_dir
__appname__ = "Architekt"
__config_filename__ = "architekt.json"
__roi_filename__ = "roi.json"
__eflookup_filename__ = "ef_lookup_user.xlsx"
__log_filename__ = "architekt.log"


def set_logname(logname: str):
    global __log_filename__
    __log_filename__ = logname

def resource_path(relative_path: str):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    base_path = getattr(sys, '_MEIPASS', sys.path[0])
    return os.path.join(base_path, relative_path)

def getConfigDir() -> str:
    """Base directory for storing config files"""
    d = user_config_dir(__appname__, ensure_exists=True)
    return user_config_dir(__appname__)

def getDataDir() -> str:
    """Base directory for storing data files"""
    d = user_data_dir(__appname__, ensure_exists=True)
    return user_config_dir(__appname__)

def getConfigPath() -> str:
    """Config file name"""
    return path.join(getConfigDir(), __config_filename__)

def getPdfDir() -> str:
    p = path.join(getDataDir(), "pdfs")
    makedirs(p, exist_ok=True)
    return p

def getRoiDir() -> str:
    """ROI dir"""
    p = path.join(getDataDir(), "rois")
    makedirs(p, exist_ok=True)
    return p

def getLogDir() -> str:
    """ROI dir"""
    p = path.join(getDataDir(), "logs")
    makedirs(p, exist_ok=True)
    return p

def getRoiConfig() -> str:
    """ROI config - storing layouts"""
    return path.join(getRoiDir(), __roi_filename__)

def getLogPath() -> str:
    """ROI config - storing layouts"""
    return path.join(getLogDir(), __log_filename__)

def getColumnsConfigPath() -> str:
    """Columns config - storing column order"""
    return path.join(getDataDir(), "columns.json")

def getColumnsConfigJson() -> dict:
    try:
        with open(getColumnsConfigPath(), mode="r", encoding='utf-8') as jsonData:
            data = json.load(jsonData)
            return data
    except:
        return {}

def saveColumnsConfigJson(data: dict):
    try:
        with open(getColumnsConfigPath(), mode="w", encoding='utf-8') as f:
            json.dump(data, f, indent=4)
    except Exception as e:
        print("Failed to save columns config", e)

def saveReportFile():
    try:
        with open(getReportPath(), mode="w", encoding='utf-8') as f:
            f.write('')
    except Exception as e:
        print("Failed to save report file", e)

def clearReportFile():
    try:
        os.remove(getReportPath())
        print("Report file has been deleted.")
    except Exception as e:
        print(f"Failed to delete report file", e)

def getDatabasePath() -> str:
    return path.join(getDataDir(), "ais_database.db")
    # return "ais_database.db"

def getReportPath() -> str:
    return path.join(getDataDir(), "report")

def getSavedFieldMapJson(file=None):
    """Merges user field map with internal field map"""
    if file is None:
        file = resource_path("src/data/fieldmap.json")
    # Load the default internal fieldmap. Update it with the saved one if
    with open(file, encoding='utf-8') as jsonData:
        fieldMapping = json.load(jsonData)
    savedFieldMapPath = path.join(getDataDir(), "fieldmap.json")
    try:
        with open(savedFieldMapPath, encoding='utf-8') as jsonData2:
            fieldMappingUser = json.load(jsonData2)
    except FileNotFoundError:
        pass

    for fieldType in fieldMapping.keys():
        for field, values in fieldMapping[fieldType].items():
            # Merge display values into main field map
            try:
                values["display"] = fieldMappingUser[fieldType][field]["display"]
            except:
                # print("display" in values)
                pass

    # Auto-insert ID as the same as key name
    # This ID should never changed
    for field in fieldMapping.get("fields", {}).keys():
        fieldMapping["fields"][field]["id"] = field

    return fieldMapping

def saveFieldMapJson(data):
    savedFieldMapPath = path.join(getDataDir(), "fieldmap.json")
    with open(savedFieldMapPath, "w") as f:
        json.dump(data, f, indent=4)

def getEfLookupPath() -> str:
    """Config file name"""
    # return "lookup_temp.xlsx"
    return path.join(getConfigDir(), __eflookup_filename__)

def getTableExportPath(file: str = None) -> str:
    """Return table export base dir with optional filename"""
    export_dir = os.path.join(os.path.expanduser('~'), 'Architekt', 'exported')
    makedirs(export_dir, exist_ok=True)
    if file:
        return os.path.join(export_dir, file)
    return export_dir

def getExportPath(file: str = None) -> str:
    """Return table export base dir with optional filename"""
    export_dir = os.path.join(os.path.expanduser('~'), 'Architekt', 'exported')
    makedirs(export_dir, exist_ok=True)
    if file:
        return os.path.join(export_dir, file)
    return export_dir

def getDataTempDir() -> str:
    """Base directory for storing data files"""
    p = path.join(getDataDir(), "temp")
    makedirs(p, exist_ok=True)
    return p

def getDataTempPath(filename: str):
    temp_path = path.join(getDataTempDir(), filename)
    #print("TEMP PATH: ", temp_path)
    return temp_path
    # return path.join(getDataTempDir(), filename)

def getReleaseUrlPath() -> str:
    return "https://atemexe.blob.core.windows.net/atem-pr-01/latest-release.txt"
    # return resource_path('src/data/updater/latest-release.txt')

def getSetupDownloadPath() -> str:
    atem_updater_dir = platformdirs.user_data_dir("atem_updater", ensure_exists=True)
    return path.realpath(path.join(atem_updater_dir, "atem-setup.exe"))
    return path.join(atem_updater_dir, "atem-setup.exe")
    # return path.join(desktop, "firefox-setup.exe")


def slugify(value, allow_unicode=False):
    """
    Taken from https://github.com/django/django/blob/master/django/utils/text.py
    Convert to ASCII if 'allow_unicode' is False. Convert spaces or repeated
    dashes to single dashes. Remove characters that aren't alphanumerics,
    underscores, or hyphens. Convert to lowercase. Also strip leading and
    trailing whitespace, dashes, and underscores.
    """
    value = str(value)
    if allow_unicode:
        value = unicodedata.normalize('NFKC', value)
    else:
        value = unicodedata.normalize('NFKD', value).encode('ascii', 'ignore').decode('ascii')
    value = re.sub(r'[^\w\s-]', '', value.lower())
    return re.sub(r'[-\s]+', '-', value).strip('-_')

def getPixmapDir(projectId) -> str:
    """Base directory for storing data files"""
    p = path.join(getDataDir(), "pixmaps", str(projectId))
    makedirs(p, exist_ok=True)
    return p

def savePagePixmap(pixmap: fitz.Pixmap,
                   projectId: int,
                   filename: str,
                   page_number: int):
    d = getPixmapDir(projectId)
    slug = slugify(filename)
    p = path.join(d, slug)
    makedirs(p, exist_ok=True)
    print(p)
    fn = path.join(p, f"{page_number}.png")
    pixmap.save(fn)


def getPagePixmap(projectId: int, filename: str, page_number):
    d = getPixmapDir(projectId)
    slug = slugify(filename)
    fn = path.join(d, slug, f"{page_number}.png")
    logging.debug(f"Loading pixmap from - {fn}")
    if os.path.exists(fn):
        try:
            pixmap = QPixmap(fn)
            return pixmap
        except Exception as e:
            pass  # Corrupt file possibly. No handling here

    return None


def uid_to_hex(uid) -> str:
    return uid.encode().hex()


def getExportPresetsDir(uid) -> str:
    """Base directory for storing data files"""
    uid = uid_to_hex(uid)
    p = path.join(getDataDir(), "data", f"{uid}", "export_presets")
    makedirs(p, exist_ok=True)
    return p

def getExportPresetPath(uid, presetName: str) -> str:
    """Columns config - storing column order"""
    return path.join(getExportPresetsDir(uid), f"{presetName}.json")

def getSourceDataDir(projectId, filename, makedir=False):
    """Store source preprocessing data missing regions"""
    slug = slugify(filename)
    print(projectId, filename)
    p = path.join(getDataDir(), "data", f"{projectId}", "sources", slug)
    if makedir:
        makedirs(p, exist_ok=True)
    return p

def getSourceMissingRegionsPath(projectId, filename) -> str:
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "pmr.feather")

def getSourceMetadataPath(projectId, filename) -> str:
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "metadata.json")

def getSourceRawDataPath(projectId, filename) -> str:
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "data.feather")

def getSourceOcrPath(projectId, filename) -> str:
    """Deprecated"""
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "textract.feather")

def getSourceOcrDataDir(projectId, filename) -> str:
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "ocr_data")

def getSourceOcrImagesDir(projectId, filename) -> str:
    return os.path.join(getSourceOcrDataDir(projectId, filename), "output_images")

def getSourceExtractionOptionsPath(projectId, filename) -> str:
    sourceDir = getSourceDataDir(projectId, filename)
    return os.path.join(sourceDir, "options.json")

if __name__ == "__main__":
    from collections import OrderedDict
    from pprint import pp
    paths = OrderedDict()

    print("------------------------------")
    print()
    def printPath(name, func, args=[]):
        print(name)
        print()
        print(f"\t{str(func.__name__)}() == {func(*args)} | Exists={path.exists(func(*args))}")
        print("\tReal path:", path.realpath(func(*args)))
        print()
        print("------------------------------")
        print()

    printPath("Data dir", getDataDir)
    printPath("Config base dir", getConfigDir)
    printPath("PDF dir path", getPdfDir)
    printPath("Config path", getRoiConfig)
    printPath("ROI config path", getConfigPath)
    printPath("Columns config path", getColumnsConfigPath)
    printPath("AIS Database path", getDatabasePath)
    printPath("EF lookup path", getEfLookupPath)
    printPath("Export table path", getTableExportPath)
    printPath("Data temp dir", getDataTempDir)
    printPath("Data temp file (should be lambda())", lambda: getDataTempPath("oci_coordinates.json"))
    printPath("Setup download location", getSetupDownloadPath)
    printPath("Preprocessing dir", getSourceDataDir, [3, "test"])

    # Manually check database path
    dbPath = rf"C:\Users\<USER>\AppData\Local\Architekt\Architekt\ais_database.db"
    print("Is manual db path is same as getDataBasePath?", dbPath == getDatabasePath())

    from PySide6 import QtCore, QtGui, QtWidgets

    # Should open explorer of path
    print()
    print("Opening getConfigDir in explorer.....")
    app = QtWidgets.QApplication(sys.argv)
    fullpath = os.path.realpath(getDatabasePath())
    QtGui.QDesktopServices.openUrl(QtCore.QUrl.fromLocalFile(getConfigDir()))

    import os
    dataPath = QtCore.QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppLocalDataLocation)
    dataPathDir = QtCore.QDir(dataPath)
    print(dataPath, path.exists(dataPath), path.realpath(dataPath))