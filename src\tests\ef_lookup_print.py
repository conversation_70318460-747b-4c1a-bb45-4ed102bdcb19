from os.path import exists
import pandas as pd


ef_lookup_template: pd.DataFrame = pd.read_excel('src/data/tables/ef_template.xlsx', index_col=0)
ef_lookup_file = "lookup_temp.xlsx"
ef_lookup_user: pd.DataFrame = pd.DataFrame()
# Merge with user EF if exists
print("Using User EF lookup table")
try:
    ef_lookup_user = pd.read_excel(ef_lookup_file, index_col=0)
except:
    ef_lookup_user = pd.DataFrame()
    ef_lookup_template.to_excel(ef_lookup_file)

print(ef_lookup_template.columns)
# for col in ef_lookup_template.columns:
#     print(col)
#     if col not in ef_lookup_user.columns:
#         ef_lookup_user[col] = 0
#         print("added col", col)
# df_merged: pd.DataFrame = ef_lookup_template.merge(ef_lookup_user, left_index=True, right_index=True, how="right")
df_merged: pd.DataFrame = ef_lookup_user.combine_first(ef_lookup_template)
df_merged = df_merged.fillna(0)
print(df_merged)
df_merged.to_excel("test_ef.xlsx")