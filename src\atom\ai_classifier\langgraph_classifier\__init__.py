"""
LangGraph BOM Classification System

A modular, multi-stage classification workflow that replaces the monolithic
approach with specialized, category-specific processing for improved accuracy.

Key Features:
- Multi-stage workflow: Analysis → Classification → Q&A → Audit
- Category-specific intelligence and rules
- Focused prompts for better accuracy
- Backward compatibility with existing systems
- Comprehensive testing and debugging support

Usage:
    from langgraph_classifier import audit_bom_dataframe_langgraph
    
    results = await audit_bom_dataframe_langgraph(df, config, debug_mode=True)
"""

# Core workflow components
from .langgraph_main import create_classification_workflow
from .integration_layer import (
    audit_bom_dataframe_langgraph,
    convert_to_programmatic_format,
    convert_to_audit_result,
    get_model_handler
)

# State management
from .state_models import (
    ClassificationState,
    WorkflowResult,
    MaterialAnalysisResponse,
    CategoryClassificationResponse,
    QADecisionResponse,
    SelfAuditResponse,
    create_initial_state,
    update_state_metadata
)

# Classification nodes
from .classification_nodes import (
    material_analysis_node,
    fitting_classification_node,
    pipe_classification_node,
    qa_decision_node,
    self_audit_node
)

# Category intelligence
from .category_intelligence import (
    COMPONENT_LOOKUP_TABLE,
    ABBREVIATION_DICTIONARIES,
    CATEGORY_INTELLIGENCE,
    lookup_component,
    resolve_abbreviations,
    get_component_intelligence,
    is_vendor_code
)

# Prompt templates
from .langgraph_prompts import (
    build_material_analysis_prompt,
    build_fitting_classification_prompt,
    build_pipe_classification_prompt,
    build_qa_decision_prompt,
    build_self_audit_prompt
)

# Version and metadata
__version__ = "1.0.0"
__author__ = "Augment Agent"
__description__ = "LangGraph-based BOM Classification System"

# Public API
__all__ = [
    # Main workflow functions
    "audit_bom_dataframe_langgraph",
    "create_classification_workflow",
    "convert_to_programmatic_format",
    "convert_to_audit_result",
    
    # State management
    "ClassificationState",
    "WorkflowResult",
    "create_initial_state",
    
    # Classification nodes
    "material_analysis_node",
    "fitting_classification_node", 
    "pipe_classification_node",
    "qa_decision_node",
    "self_audit_node",
    
    # Intelligence and utilities
    "lookup_component",
    "resolve_abbreviations",
    "get_component_intelligence",
    "is_vendor_code",
    
    # Configuration
    "COMPONENT_LOOKUP_TABLE",
    "ABBREVIATION_DICTIONARIES",
    "CATEGORY_INTELLIGENCE"
]


def get_system_info():
    """Get system information and status"""
    
    try:
        from langgraph.graph import StateGraph
        langgraph_available = True
    except ImportError:
        langgraph_available = False
    
    try:
        from ..ai_review_audit.audit_main import AuditConfig
        existing_infrastructure = True
    except ImportError:
        existing_infrastructure = False
    
    return {
        "version": __version__,
        "langgraph_available": langgraph_available,
        "existing_infrastructure_available": existing_infrastructure,
        "modules": {
            "state_models": "✓ Available",
            "classification_nodes": "✓ Available", 
            "category_intelligence": "✓ Available",
            "langgraph_prompts": "✓ Available",
            "integration_layer": "✓ Available",
            "langgraph_main": "✓ Available"
        },
        "features": {
            "material_analysis": "✓ Implemented",
            "fitting_classification": "✓ Implemented",
            "pipe_classification": "✓ Implemented", 
            "qa_decisions": "✓ Implemented",
            "self_audit": "✓ Implemented",
            "valve_classification": "⚠️ Placeholder",
            "flange_classification": "⚠️ Placeholder",
            "gasket_classification": "⚠️ Placeholder",
            "bolt_classification": "⚠️ Placeholder",
            "support_classification": "⚠️ Placeholder"
        }
    }


if __name__ == "__main__":
    """Module self-test and information display"""
    
    print("LangGraph BOM Classification System")
    print("=" * 50)
    
    # Display system information
    info = get_system_info()
    
    print(f"Version: {info['version']}")
    print(f"LangGraph Available: {info['langgraph_available']}")
    print(f"Existing Infrastructure: {info['existing_infrastructure_available']}")
    
    print("\nModules:")
    for module, status in info['modules'].items():
        print(f"  {module}: {status}")
    
    print("\nFeatures:")
    for feature, status in info['features'].items():
        print(f"  {feature}: {status}")
    
    print("\nQuick Test:")
    
    # Test component lookup
    test_descriptions = [
        "Pipe Nipple 2\" SCH 40",
        "90 LR Elbow 4\"",
        "Ball Valve 3\"",
        "VENDOR ABC123"
    ]
    
    for desc in test_descriptions:
        component_info = lookup_component(desc)
        if component_info:
            print(f"  '{desc}' → {component_info['category']}: {component_info['full_name']}")
        else:
            vendor_check = is_vendor_code(desc)
            print(f"  '{desc}' → {'Vendor Code' if vendor_check else 'No match'}")
    
    print("\n" + "=" * 50)
    print("System ready for use!")
    print("\nUsage example:")
    print("  from langgraph_classifier import audit_bom_dataframe_langgraph")
    print("  results = await audit_bom_dataframe_langgraph(df, config)")
