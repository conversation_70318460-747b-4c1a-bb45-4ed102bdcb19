"""
Export LangGraph BOM Classification Results

This module provides functionality to export classified BOM results to Excel files
in job-specific folders for easy access and review.
"""

import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import json

try:
    from .state_models import ClassificationState, WorkflowResult
    from .integration_layer import convert_to_programmatic_format
    from ..prompts import categorization_table
except ImportError:
    from state_models import ClassificationState, WorkflowResult
    from integration_layer import convert_to_programmatic_format
    # Mock categorization_table for direct execution
    categorization_table = [
        {"field": "rfq_scope"}, {"field": "unit_of_measure"}, {"field": "material"},
        {"field": "astm"}, {"field": "grade"}, {"field": "fitting_category"}
    ]

try:
    from ...utils.export import export_to_excel
except ImportError:
    # Fallback export function
    def export_to_excel(data, filename, sheet_names=None, **kwargs):
        """Fallback export function using pandas"""
        if isinstance(data, list):
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                for i, df in enumerate(data):
                    sheet_name = sheet_names[i] if sheet_names and i < len(sheet_names) else f'Sheet{i+1}'
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            data.to_excel(filename, index=False)


def create_classified_results_dataframe(
    workflow_results: List[Union[WorkflowResult, ClassificationState]],
    original_data: Optional[pd.DataFrame] = None
) -> pd.DataFrame:
    """
    Create a comprehensive DataFrame with classified results
    
    Args:
        workflow_results: List of WorkflowResult or ClassificationState objects
        original_data: Original BOM data for reference
        
    Returns:
        DataFrame with classified results
    """
    
    results_data = []
    
    for result in workflow_results:
        # Handle both WorkflowResult and ClassificationState
        if isinstance(result, dict):  # ClassificationState (dict-like)
            item_id = result.get("item_id", "unknown")
            material_description = result.get("material_description", "")
            field_classifications = result.get("field_classifications", {})
            confidence_scores = result.get("confidence_scores", {})
            processing_path = result.get("processing_path", "unknown")
            workflow_path = result.get("workflow_path", [])
            processing_time = result.get("processing_time", 0.0)
            model_calls = result.get("model_calls", 0)
            tokens_used = result.get("tokens_used", 0)
            identified_issues = result.get("identified_issues", [])
            extracted_properties = result.get("extracted_properties", {})
            
        else:  # WorkflowResult
            item_id = result.item_id
            material_description = ""  # Get from original_data if available
            field_classifications = result.final_classifications
            confidence_scores = result.confidence_scores
            processing_path = ""  # Not directly available in WorkflowResult
            workflow_path = result.workflow_path
            processing_time = result.processing_time
            model_calls = result.model_calls
            tokens_used = result.tokens_used
            identified_issues = result.identified_issues
            extracted_properties = {}
            
            # Try to get material description from original data
            if original_data is not None and not original_data.empty:
                matching_rows = original_data[original_data['id'] == item_id]
                if not matching_rows.empty:
                    material_description = matching_rows['material_description'].iloc[0]
        
        # Create row data
        row_data = {
            'id': item_id,
            'material_description': material_description,
            'processing_path': processing_path,
            'workflow_stages': ' → '.join(workflow_path),
            'processing_time_seconds': processing_time,
            'model_calls': model_calls,
            'tokens_used': tokens_used,
            'issues_count': len(identified_issues),
            'status': 'issues' if identified_issues else 'ok'
        }
        
        # Add all field classifications
        for field, value in field_classifications.items():
            row_data[f'classified_{field}'] = value
        
        # Add confidence scores
        for field, confidence in confidence_scores.items():
            row_data[f'confidence_{field}'] = confidence
        
        # Add extracted properties
        for prop, value in extracted_properties.items():
            row_data[f'extracted_{prop}'] = value
        
        results_data.append(row_data)
    
    return pd.DataFrame(results_data)


def create_final_classified_dataframe(
    workflow_results: List[Union[WorkflowResult, ClassificationState]],
    original_data: Optional[pd.DataFrame] = None
) -> pd.DataFrame:
    """
    Create final classified DataFrame with exact categorization_table column structure

    This function extracts the final classifications from the workflow results and
    maps them to the exact column structure specified in categorization_table.

    Args:
        workflow_results: List of WorkflowResult or ClassificationState objects
        original_data: Original BOM data for reference

    Returns:
        DataFrame with exact column structure: id,material_description,rfq_scope,general_category,unit_of_measure,material,abbreviated_material,ansme_ansi,astm,grade,rating,schedule,coating,forging,ends,item_tag,tie_point,pipe_category,valve_type,fitting_category,weld_category,bolt_category,gasket_category
    """

    # Define the exact column order from your specification
    # NOTE: Excluding size, size1, size2, quantity as these are handled elsewhere in the pipeline
    exact_columns = [
        'id', 'material_description', 'rfq_scope', 'general_category', 'unit_of_measure',
        'material', 'abbreviated_material', 'ansme_ansi', 'astm', 'grade', 'rating',
        'schedule', 'coating', 'forging', 'ends', 'item_tag', 'tie_point',
        'pipe_category', 'valve_type', 'fitting_category', 'weld_category',
        'bolt_category', 'gasket_category'
    ]

    results_data = []

    for result in workflow_results:
        # Handle both WorkflowResult and ClassificationState
        if isinstance(result, dict):  # ClassificationState (dict-like)
            item_id = result.get("item_id", "unknown")
            material_description = result.get("material_description", "")
            field_classifications = result.get("field_classifications", {})

            # If field_classifications is empty, try to extract from classified_response
            if not field_classifications:
                classified_response = result.get("classified_response", "")
                if classified_response:
                    try:
                        import json
                        # Handle JSON string that might be wrapped in markdown
                        json_str = classified_response.strip()
                        if json_str.startswith('```json'):
                            json_str = json_str[7:-3].strip()
                        elif json_str.startswith('```'):
                            json_str = json_str[3:-3].strip()

                        parsed_data = json.loads(json_str)
                        field_classifications = parsed_data

                        print(f"   🔧 Extracted classifications from classified_response for item {item_id}")

                    except (json.JSONDecodeError, Exception) as e:
                        print(f"   ❌ Failed to parse classified_response for item {item_id}: {e}")
                        print(f"   🔄 Attempting model call to fix malformed response...")

                        # Try to make a new model call to fix the response
                        try:
                            # Import model handler
                            from .integration_layer import get_or_create_model_handler

                            # Create a simple prompt to reformat the response
                            fix_prompt = f"""
The following JSON response is malformed. Please fix it and return valid JSON:

{classified_response}

Return only valid JSON with the classification fields. Do not include any markdown formatting.
"""

                            # Get model handler (this might fail if not available)
                            model_handler = get_or_create_model_handler()

                            # Make model call to fix the response
                            import asyncio
                            fixed_response = asyncio.run(model_handler.call_model_for_stage(
                                stage_name="json_fix",
                                prompt=fix_prompt,
                                response_schema=dict,
                                item_id=item_id
                            ))

                            if isinstance(fixed_response, dict):
                                field_classifications = fixed_response
                                print(f"   ✅ Fixed response for item {item_id}")
                            else:
                                field_classifications = {}

                        except Exception as fix_error:
                            print(f"   ❌ Could not fix response for item {item_id}: {fix_error}")
                            field_classifications = {}

        else:  # WorkflowResult
            item_id = result.item_id
            material_description = ""  # Get from original_data if available
            field_classifications = result.final_classifications

            # Try to get material description from original data
            if original_data is not None and not original_data.empty:
                matching_rows = original_data[original_data['id'] == item_id]
                if not matching_rows.empty:
                    material_description = matching_rows['material_description'].iloc[0]

        # Create row data with exact column structure
        row_data = {
            'id': item_id,
            'material_description': material_description
        }

        # Add all categorization fields using exact field names (no prefixes)
        for column in exact_columns[2:]:  # Skip id and material_description
            # Get the value from field_classifications, default to empty string
            value = field_classifications.get(column, "")
            row_data[column] = value

        # Debug output to help troubleshoot column mapping issues (enable for debugging)
        debug_export = False  # Set to True for debugging
        if debug_export:
            if field_classifications:
                print(f"   🔧 Item {item_id}: Extracted {len(field_classifications)} fields: {list(field_classifications.keys())}")
                # Show first few field mappings for debugging
                for field, value in list(field_classifications.items())[:5]:
                    print(f"      {field}: '{value}'")

                # Check if there's a nested structure that needs flattening
                if 'category_mapping' in field_classifications:
                    print(f"   🔍 Found nested 'category_mapping' structure")
                    category_mapping = field_classifications['category_mapping']
                    if isinstance(category_mapping, dict):
                        print(f"      category_mapping fields: {list(category_mapping.keys())}")
                        # Flatten the category_mapping into the main field_classifications
                        for nested_field, nested_value in category_mapping.items():
                            if nested_field not in field_classifications:
                                field_classifications[nested_field] = nested_value
                                print(f"      Flattened: {nested_field} = '{nested_value}'")
            else:
                print(f"   ⚠️  Item {item_id}: No field_classifications found")

        results_data.append(row_data)

    # Create DataFrame with exact column order
    df = pd.DataFrame(results_data)

    # Ensure all columns exist in the correct order
    for column in exact_columns:
        if column not in df.columns:
            df[column] = ""

    # Reorder columns to match exact specification
    df = df[exact_columns]

    return df


def create_issues_dataframe(workflow_results: List[Union[WorkflowResult, ClassificationState]]) -> pd.DataFrame:
    """
    Create a DataFrame with identified issues for review and correction
    
    Args:
        workflow_results: List of WorkflowResult or ClassificationState objects
        
    Returns:
        DataFrame with issues in programmatic format
    """
    
    issues_data = []
    
    for result in workflow_results:
        # Handle both WorkflowResult and ClassificationState
        if isinstance(result, dict):  # ClassificationState
            item_id = result.get("item_id", "unknown")
            material_description = result.get("material_description", "")
            identified_issues = result.get("identified_issues", [])
        else:  # WorkflowResult
            item_id = result.item_id
            material_description = ""  # Could be enhanced to get from original data
            identified_issues = result.identified_issues
        
        # Add each issue as a separate row
        for issue in identified_issues:
            issues_data.append({
                'id': item_id,
                'status': 'issues',
                'material_description': material_description,
                'column_name': issue.get('field', 'unknown'),
                'current_value': issue.get('current_value', ''),
                'suggested_value': issue.get('suggested_value', ''),
                'confidence': issue.get('confidence', 0.0),
                'explanation': issue.get('explanation', ''),
                'accept_merge': None  # User decision column
            })
    
    return pd.DataFrame(issues_data)


def create_summary_dataframe(workflow_results: List[Union[WorkflowResult, ClassificationState]]) -> pd.DataFrame:
    """
    Create a summary DataFrame with processing statistics
    
    Args:
        workflow_results: List of WorkflowResult or ClassificationState objects
        
    Returns:
        DataFrame with summary statistics
    """
    
    if not workflow_results:
        return pd.DataFrame()
    
    total_items = len(workflow_results)
    total_issues = sum(len(r.get("identified_issues", []) if isinstance(r, dict) else r.identified_issues) 
                      for r in workflow_results)
    total_processing_time = sum(r.get("processing_time", 0.0) if isinstance(r, dict) else r.processing_time 
                               for r in workflow_results)
    total_model_calls = sum(r.get("model_calls", 0) if isinstance(r, dict) else r.model_calls 
                           for r in workflow_results)
    total_tokens = sum(r.get("tokens_used", 0) if isinstance(r, dict) else r.tokens_used 
                      for r in workflow_results)
    
    # Calculate accuracy (items without issues)
    items_without_issues = total_items - sum(1 for r in workflow_results 
                                           if (r.get("identified_issues", []) if isinstance(r, dict) 
                                               else r.identified_issues))
    accuracy = (items_without_issues / total_items * 100) if total_items > 0 else 0
    
    # Processing path distribution
    processing_paths = {}
    for result in workflow_results:
        path = result.get("processing_path", "unknown") if isinstance(result, dict) else "unknown"
        processing_paths[path] = processing_paths.get(path, 0) + 1
    
    summary_data = [
        {'Metric': 'Total Items Processed', 'Value': total_items},
        {'Metric': 'Items with Issues', 'Value': total_issues},
        {'Metric': 'Items without Issues', 'Value': items_without_issues},
        {'Metric': 'Accuracy (%)', 'Value': f"{accuracy:.1f}%"},
        {'Metric': 'Total Processing Time (s)', 'Value': f"{total_processing_time:.2f}"},
        {'Metric': 'Average Time per Item (s)', 'Value': f"{total_processing_time/total_items:.2f}" if total_items > 0 else "0.00"},
        {'Metric': 'Total Model Calls', 'Value': total_model_calls},
        {'Metric': 'Total Tokens Used', 'Value': total_tokens},
        {'Metric': 'Average Tokens per Item', 'Value': f"{total_tokens/total_items:.0f}" if total_items > 0 else "0"},
    ]
    
    # Add processing path distribution
    for path, count in processing_paths.items():
        summary_data.append({
            'Metric': f'Processing Path: {path.title()}', 
            'Value': f"{count} ({count/total_items*100:.1f}%)" if total_items > 0 else "0"
        })
    
    return pd.DataFrame(summary_data)


def export_classified_results(
    workflow_results: List[Union[WorkflowResult, ClassificationState]],
    job_folder: str,
    original_data: Optional[pd.DataFrame] = None,
    job_name: Optional[str] = None
) -> str:
    """
    Export classified results to Excel file in the specified job folder
    
    Args:
        workflow_results: List of classification results
        job_folder: Path to job folder for export
        original_data: Original BOM data for reference
        job_name: Optional job name for filename
        
    Returns:
        Path to exported file
    """
    
    # Ensure job folder exists
    job_path = Path(job_folder)
    job_path.mkdir(parents=True, exist_ok=True)
    
    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if job_name:
        safe_job_name = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).strip()
        filename = f"LangGraph_BOM_Classification_{safe_job_name}_{timestamp}.xlsx"
    else:
        filename = f"LangGraph_BOM_Classification_{timestamp}.xlsx"
    
    export_path = job_path / filename
    
    # Create DataFrames
    print(f"📊 Creating export DataFrames...")

    # Final classified results with exact column structure
    final_classified_df = create_final_classified_dataframe(workflow_results, original_data)
    print(f"   ✓ Final Classifications: {len(final_classified_df)} items")

    # Main results (detailed with processing info)
    results_df = create_classified_results_dataframe(workflow_results, original_data)
    print(f"   ✓ Detailed Results: {len(results_df)} items")

    # Issues for correction
    issues_df = create_issues_dataframe(workflow_results)
    print(f"   ✓ Issues: {len(issues_df)} issues")

    # Summary statistics
    summary_df = create_summary_dataframe(workflow_results)
    print(f"   ✓ Summary: {len(summary_df)} metrics")

    # Export to Excel with multiple sheets
    dataframes = [final_classified_df, results_df, issues_df, summary_df]
    sheet_names = ['Final_Classifications', 'Detailed_Results', 'Issues_for_Review', 'Summary_Statistics']
    
    try:
        export_to_excel(
            data=dataframes,
            filename=str(export_path),
            sheet_names=sheet_names,
            autosize_columns=True
        )
        
        print(f"✅ Export successful!")
        print(f"📁 File saved to: {export_path}")
        print(f"📊 Sheets created:")
        print(f"   • Final_Classifications: {len(final_classified_df)} rows (exact column structure)")
        print(f"   • Detailed_Results: {len(results_df)} rows (with processing info)")
        print(f"   • Issues_for_Review: {len(issues_df)} rows")
        print(f"   • Summary_Statistics: {len(summary_df)} rows")
        
        return str(export_path)
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
        raise


if __name__ == "__main__":
    """Test export functionality"""
    
    print("Testing LangGraph Results Export")
    print("=" * 50)
    
    # Create test data
    test_results = [
        {
            "item_id": "test_001",
            "material_description": "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9",
            "processing_path": "fitting",
            "field_classifications": {
                "rfq_scope": "Fittings",
                "fitting_category": "45 Elbow",
                "material": "Steel, Stainless",
                "astm": "A403",
                "grade": "WP316/316L",
                "schedule": "10S",
                "ends": "BE",
                "ansme_ansi": "B16.9",
                "unit_of_measure": "EA"
            },
            "confidence_scores": {
                "rfq_scope": 0.98,
                "fitting_category": 0.95,
                "material": 0.95
            },
            "workflow_path": ["material_analysis", "fitting_classification", "qa_decisions", "self_audit"],
            "processing_time": 5.2,
            "model_calls": 4,
            "tokens_used": 850,
            "identified_issues": [],
            "extracted_properties": {
                "item_type": "ELBOW",
                "item_subtype": "45 LR",
                "material_grade": "316/316L SS"
            }
        }
    ]

    # Test the new final classified dataframe function
    print("\n🧪 Testing Final Classified DataFrame Creation...")
    final_df = create_final_classified_dataframe(test_results)
    print(f"   ✓ Created DataFrame with {len(final_df)} rows and {len(final_df.columns)} columns")
    print(f"   ✓ Columns: {list(final_df.columns)}")
    if not final_df.empty:
        print(f"   ✓ Sample data: rfq_scope='{final_df.iloc[0]['rfq_scope']}', material='{final_df.iloc[0]['material']}'")
        print(f"   ✓ All required fields present: {all(col in final_df.columns for col in ['id', 'material_description', 'rfq_scope', 'fitting_category'])}")
    
    # Test export
    test_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Employee Testing\TEST 3 - Heartwell (AX_0044)"
    
    try:
        export_path = export_classified_results(
            workflow_results=test_results,
            job_folder=test_folder,
            job_name="Heartwell_Test"
        )
        print(f"\n🎉 Test export successful: {export_path}")
    except Exception as e:
        print(f"\n❌ Test export failed: {e}")
        import traceback
        traceback.print_exc()
