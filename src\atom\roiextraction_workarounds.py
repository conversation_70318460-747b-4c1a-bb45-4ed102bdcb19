import fitz
import json
import pandas as pd
from copy import deepcopy
import numpy as np
import time

from PIL import Image
import fitz
import io
import cv2

def page_to_opencv(page: fitz.Page, zoom=None, dpi=None):
    """Return open CV image from PyMuPDF page"""
    if zoom is None:
        zoom = 1
    if dpi:
        zoom = dpi / 72
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image


def safe_json_loads(item):
    """Parse JSON strings safely, handling both standard JSON and Python-style single quotes.
    Also handles pandas DataFrame serialization issues and escaped quotes in content."""
    if isinstance(item, str):
        try:
            return json.loads(item)
        except json.JSONDecodeError:
            try:
                # Try to handle Python-style single quotes by replacing them with double quotes
                # First, replace any double quotes with a temporary marker
                temp_str = item.replace('"', '___DOUBLEQUOTE___')
                # Replace single quotes with double quotes
                temp_str = temp_str.replace("'", '"')
                # Restore original double quotes
                temp_str = temp_str.replace('___DOUBLEQUOTE___', '\\"')

                # Special case handling for words with quotes like '5"'
                temp_str = temp_str.replace('5\\"', '5\\\\"')

                # Try to parse the modified string
                try:
                    return json.loads(temp_str)
                except json.JSONDecodeError:
                    # If that fails, try a more aggressive approach - use ast.literal_eval
                    import ast
                    try:
                        return ast.literal_eval(item)
                    except (SyntaxError, ValueError):
                        print(f"Failed to parse with ast.literal_eval: '{item}'")
                        return item
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e} for value {item}")
                return item  # Return the original string if it's not valid JSON
    return item  # Return the item as is if it's not a string


def detect_text_bbox(cv: cv2.Mat, region: list[int], page_width, page_height, zoom):
    """From a cropped region, detect the text region

    This assumes that the text is red and all the text inside the cropped region is of interest
    """
    x, y, w, h = [int(x) * zoom for x in region]
    crop_img = cv[y:y+h, x:x+w]

    # Create a mask for red color
    hsv_img = cv2.cvtColor(crop_img, cv2.COLOR_BGR2HSV)

    # Define range for red color in HSV
    # Red color has two ranges in HSV due to the circular nature of hue
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    # Create masks for both red ranges
    mask1 = cv2.inRange(hsv_img, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv_img, lower_red2, upper_red2)

    # Combine the masks
    red_mask = cv2.bitwise_or(mask1, mask2)

    # Find contours in the red mask
    contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Create a copy of the original image to draw bounding boxes on
    result_img = crop_img.copy()

    # Process each contour to find bounding boxes of red regions
    red_bboxes = []
    for contour in contours:
        # Filter out very small contours
        if cv2.contourArea(contour) > 1:  # Adjust this threshold as needed
            # Get bounding box coordinates
            x_cont, y_cont, w_cont, h_cont = cv2.boundingRect(contour)

            # Add to our list of red bounding boxes (relative to the crop_img)
            red_bboxes.append((x_cont, y_cont, w_cont, h_cont))

    # Convert red bounding boxes to absolute coordinates (relative to the original image)
    abs_red_bboxes = []
    for bbox in red_bboxes:
        x_bbox, y_bbox, w_bbox, h_bbox = bbox
        abs_x = x + x_bbox
        abs_y = y + y_bbox
        abs_red_bboxes.append((abs_x, abs_y, w_bbox, h_bbox))

    # Print the bounding boxes
    if red_bboxes:
        # print(f"Red regions in crop_img: {red_bboxes}")
        # print(f"Red regions in absolute coordinates: {abs_red_bboxes}")

        # Find the leftmost bounding box
        leftmost_x_relative = min(bbox[0] for bbox in red_bboxes)

        # Find the rightmost bounding box (x + width = right edge)
        rightmost_x1_relative = max(bbox[0] + bbox[2] for bbox in red_bboxes)

        # Get width of the cropped image
        crop_width = crop_img.shape[1]
        crop_height = crop_img.shape[0]

        # Calculate the relative positions (0.0 to 1.0)
        leftmost_relative_position = leftmost_x_relative / crop_width
        rightmost_relative_position = rightmost_x1_relative / crop_width

        # Calculate unzoomed coordinates (original scale)
        leftmost_x_unzoomed = leftmost_x_relative / zoom
        rightmost_x1_unzoomed = rightmost_x1_relative / zoom

        # Calculate absolute position in original unzoomed page
        abs_x0_unzoomed = (x + leftmost_x_relative) / zoom
        abs_x1_unzoomed = (x + rightmost_x1_relative) / zoom

        # Find top-most and bottom-most y coordinates among all red regions
        top_y = min(bbox[1] for bbox in red_bboxes)
        bottom_y = max(bbox[1] + bbox[3] for bbox in red_bboxes)

        # Calculate y coordinates in original unzoomed page
        abs_y0_unzoomed = (y + top_y) / zoom
        abs_y1_unzoomed = (y + bottom_y) / zoom

        # Get the original page dimensions

        # Calculate relative position in the original page (0.0 to 1.0)
        rel_x0_in_page = abs_x0_unzoomed / page_width
        rel_x1_in_page = abs_x1_unzoomed / page_width
        rel_y0_in_page = abs_y0_unzoomed / page_height
        rel_y1_in_page = abs_y1_unzoomed / page_height

        # print(f"Leftmost x coordinate (x0): {leftmost_x_relative}")
        # print(f"Rightmost x coordinate (x1): {rightmost_x1_relative}")
        # print(f"Width of cropped image: {crop_width}")
        # print(f"Relative positions: x0: {leftmost_relative_position:.4f} ({leftmost_relative_position*100:.2f}%), x1: {rightmost_relative_position:.4f} ({rightmost_relative_position*100:.2f}%)")
        # print(f"Unzoomed coordinates: x0: {leftmost_x_unzoomed:.2f}, x1: {rightmost_x1_unzoomed:.2f}")

        # print(f"\nBounding box in original unzoomed page:")
        # print(f"[{abs_x0_unzoomed:.2f}, {abs_y0_unzoomed:.2f}, {abs_x1_unzoomed:.2f}, {abs_y1_unzoomed:.2f}]")
        # print(f"Format: [x0, y0, x1, y1]")

        # Calculate width and height of the bounding box
        bbox_width = abs_x1_unzoomed - abs_x0_unzoomed
        bbox_height = abs_y1_unzoomed - abs_y0_unzoomed
        # print(f"Width: {bbox_width:.2f}, Height: {bbox_height:.2f}")

        # print(f"Original page dimensions: {page_width} x {page_height}")
        # print(f"Relative position in original page:")
        # print(f"x0: {rel_x0_in_page:.4f} ({rel_x0_in_page*100:.2f}%)")
        # print(f"y0: {rel_y0_in_page:.4f} ({rel_y0_in_page*100:.2f}%)")
        # print(f"x1: {rel_x1_in_page:.4f} ({rel_x1_in_page*100:.2f}%)")
        # print(f"y1: {rel_y1_in_page:.4f} ({rel_y1_in_page*100:.2f}%)")

        # Draw a bounding box from leftmost x0 to rightmost x1
        # Use the top-most and bottom-most y coordinates to cover all red regions
        cv2.rectangle(result_img,
                     (leftmost_x_relative, top_y),
                     (rightmost_x1_relative, bottom_y),
                     (0, 255, 0), 2)  # Green rectangle spanning from leftmost to rightmost
    else:
        print("No red regions found")

    return {
        "bbox": [abs_x0_unzoomed, abs_y0_unzoomed, abs_x1_unzoomed, abs_y1_unzoomed] if red_bboxes else None,
        "rel_bbox": [rel_x0_in_page, rel_y0_in_page, rel_x1_in_page, rel_y1_in_page] if red_bboxes else None,
        "crop_rel_bbox": [leftmost_x_relative, top_y, rightmost_x1_relative, bottom_y] if red_bboxes else None,
        "crop_rel_pos": [leftmost_relative_position, top_y/crop_height, rightmost_relative_position, bottom_y/crop_height] if red_bboxes else None,
        "img": result_img,
    }

def draw_result(result_df: pd.DataFrame, page: fitz.Page, zoom: int = 1, save: bool=False):
    """Draw bounding boxes for the result DataFrame"""
    cv_image = page_to_opencv(page, zoom=zoom)

    # Display all the new rows in the page CV image
    display_img = cv_image.copy()

    # First, draw individual word bounding boxes in blue
    for _, row in result_df.iterrows():
        if "words" in row and isinstance(row["words"], list):
            for word_obj in row["words"]:
                if "bbox" in word_obj:
                    # Get the coordinates
                    word_bbox = word_obj["bbox"]

                    # Scale coordinates according to zoom
                    wx0, wy0, wx1, wy1 = int(word_bbox[0] * zoom), int(word_bbox[1] * zoom), int(word_bbox[2] * zoom), int(word_bbox[3] * zoom)

                    # Draw the rectangle in blue
                    cv2.rectangle(display_img, (wx0, wy0), (wx1, wy1), (255, 0, 0), 1)  # Blue color (BGR)

                    # Optionally add the word text
                    if "word" in word_obj:
                        word_text = word_obj["word"]
                        cv2.putText(display_img, word_text, (wx0, wy0 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

    # Then draw bounding boxes for all the new rows in green
    for _, row in result_df.iterrows():
        if "coordinates" in row and row["coordinates"]:
            # Get the coordinates
            coords = row["coordinates"]

            # Scale coordinates according to zoom
            x0, y0, x1, y1 = int(coords[0] * zoom), int(coords[1] * zoom), int(coords[2] * zoom), int(coords[3] * zoom)

            # Draw the rectangle
            cv2.rectangle(display_img, (x0, y0), (x1, y1), (0, 255, 0), 2)

            # Add the text value near the rectangle
            if "value" in row:
                text = row["value"]
                # Truncate text if it's too long
                if len(text) > 30:
                    text = text[:27] + "..."
                cv2.putText(display_img, text, (x0, y0 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    # Display the image with all bounding boxes
    cv2.namedWindow("Patched regions", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Patched regions", 1920, 1080)
    cv2.imshow("Patched regions", display_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

    if save:
        cv2.imwrite("debug/all_detected_regions.png", display_img)


def patch_bboxes(raw_df: pd.DataFrame, page: fitz.Page, zoom: int = 1):
    """Patch bounding boxes for red regions in the document"""
    # Use a higher zoom for better detection
    zoom = 4
    cv_image = page_to_opencv(page, zoom=zoom)

    # Make a copy of the dataframe to avoid modifying the original
    result_df = raw_df.copy()

    # Apply the safe_json_loads function to the words column
    result_df['words'] = result_df['words'].apply(safe_json_loads)

    # Initialize list for new rows
    new_rows = []

    # Process each row
    for _, row in result_df.iterrows():
        try:
            # Initialize variables for tracking segments
            current_segment_words = []
            current_segment_word_objects = []
            current_segment_detection_results = []
            OFFSET = 0.1

            if not row["words"]:
                new_rows.append(row)
                continue

            # Process each word in the row
            for word in row["words"]:
                word_text = word["word"]
                full_bbox = word["bbox"]

                # Extract the region for detection
                region = [full_bbox[0], full_bbox[1], full_bbox[2]-full_bbox[0], full_bbox[3]-full_bbox[1]]

                # Detect red text in this region
                detection_result = detect_text_bbox(cv_image, region, page.rect.width, page.rect.height, zoom)

                # Store the image before we potentially modify detection_result
                detection_img = None
                if detection_result["bbox"]:
                    detection_img = detection_result.pop("img")
                    # Store the detection result for this word
                    current_segment_detection_results.append(detection_result)

                    # Update the word's bbox with the detection result bbox
                    word_copy = word.copy()
                    word_copy["bbox"] = detection_result["bbox"]
                else:
                    word_copy = word.copy()

                # Check if we need to split based on the OFFSET threshold or spatial positioning
                should_split = False

                # Calculate the width of the original word bbox
                word_width = full_bbox[2] - full_bbox[0]

                # Only consider splitting if the word width is not too small
                # Define a minimum width threshold (adjust as needed)
                MIN_WIDTH_THRESHOLD = 10  # in points

                if word_width >= MIN_WIDTH_THRESHOLD:
                    # Check for leading spatial whitespace (OFFSET threshold)
                    if detection_result["bbox"] and detection_result["crop_rel_pos"][0] > OFFSET:
                        should_split = True

                    # Check for trailing spatial whitespace
                    # If the current word's rightmost position is far from the edge of the crop region
                    if detection_result["bbox"] and detection_result["crop_rel_pos"][2] < (1.0 - OFFSET) and len(current_segment_words) > 0:
                        should_split = True

                if should_split:

                    # cv2.imshow("red bounding boxes", detection_img)
                    # cv2.waitKey(0)
                    # cv2.destroyAllWindows()

                    # If we have words in the current segment, create a new row for them
                    if current_segment_words:
                        # Create a new row for the current segment
                        new_row = row.copy()
                        new_row["value"] = " ".join(current_segment_words)
                        new_row["words"] = current_segment_word_objects.copy()

                        # Calculate the bounding box for the segment using word coordinates
                        if current_segment_word_objects:
                            # Find min/max coordinates from all words in this segment
                            min_x0 = min(word_obj["bbox"][0] for word_obj in current_segment_word_objects)
                            min_y0 = min(word_obj["bbox"][1] for word_obj in current_segment_word_objects)
                            max_x1 = max(word_obj["bbox"][2] for word_obj in current_segment_word_objects)
                            max_y1 = max(word_obj["bbox"][3] for word_obj in current_segment_word_objects)

                            # Create the full segment bounding box
                            full_segment_bbox = (min_x0, min_y0, max_x1, max_y1)
                            new_row["coordinates"] = full_segment_bbox
                            new_row["coordinates2"] = full_segment_bbox
                            new_row["patched"] = True

                            # Update each word's bbox to use the segment's y0 and y1
                            for word_obj in new_row["words"]:
                                if "bbox" in word_obj:
                                    # Keep the word's x-coordinates but use the segment's y-coordinates from coordinates2
                                    word_obj["bbox"] = (word_obj["bbox"][0], new_row["coordinates2"][1], word_obj["bbox"][2], new_row["coordinates2"][3])

                        # Add the new row to our results
                        new_rows.append(new_row)

                    # Start a new segment with this word
                    current_segment_words = [word_text]
                    current_segment_word_objects = [word_copy]
                    current_segment_detection_results = [detection_result] if detection_result["bbox"] else []

                    # Debug visualization if needed
                    # if detection_img is not None:
                    #     cv2.imshow("red bounding boxes", detection_img)
                    #     cv2.waitKey(0)
                    #     cv2.destroyAllWindows()
                else:
                    # Add this word to the current segment
                    current_segment_words.append(word_text)
                    current_segment_word_objects.append(word_copy)
            # Add the final segment if it has any words
            if current_segment_words:
                new_row = row.copy()
                new_row["value"] = " ".join(current_segment_words)
                new_row["words"] = current_segment_word_objects

                # Calculate the bounding box for the segment using word coordinates
                if current_segment_word_objects:
                    # Find min/max coordinates from all words in this segment
                    min_x0 = min(word_obj["bbox"][0] for word_obj in current_segment_word_objects)
                    min_y0 = min(word_obj["bbox"][1] for word_obj in current_segment_word_objects)
                    max_x1 = max(word_obj["bbox"][2] for word_obj in current_segment_word_objects)
                    max_y1 = max(word_obj["bbox"][3] for word_obj in current_segment_word_objects)

                    # Create the full segment bounding box
                    full_segment_bbox = (min_x0, min_y0, max_x1, max_y1)
                    new_row["coordinates"] = full_segment_bbox
                    new_row["coordinates2"] = full_segment_bbox
                    new_row["patched"] = True

                    # Update each word's bbox to use the segment's y0 and y1
                    for word_obj in new_row["words"]:
                        if "bbox" in word_obj:
                            # Keep the word's x-coordinates but use the segment's y-coordinates from coordinates2
                            word_obj["bbox"] = (word_obj["bbox"][0], new_row["coordinates2"][1], word_obj["bbox"][2], new_row["coordinates2"][3])

                new_rows.append(new_row)
        except Exception as e:
            print(f"Error processing row: {e}")
            print(f"Row value: {row.get('value', 'N/A')}")
            # Keep the original row in case of error
            new_rows.append(row)

    # Create a new DataFrame with the updated rows
    result_df = pd.DataFrame(new_rows)

    # Save the result DataFrame to a CSV file
    # result_df.to_csv("debug/all_detected_regions.csv", index=False)

    return result_df


def draw_original_coordinates(df, page, save=False, zoom=2):
    """
    Draw the original coordinates from the DataFrame on the page image

    Args:
        df: DataFrame with coordinates
        page: PyMuPDF page object
        save: Whether to save the image
        zoom: Zoom factor
    """
    # Get the page as a pixmap
    pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))

    # Convert to OpenCV image
    img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

    # Draw each coordinate
    for _, row in df.iterrows():
        if "coordinates2" in row and row["coordinates2"]:
            coords = row["coordinates2"]
            if isinstance(coords, (list, tuple)) and len(coords) >= 4:
                x0, y0, x1, y1 = coords
                # Scale coordinates by zoom factor
                x0, y0, x1, y1 = int(x0 * zoom), int(y0 * zoom), int(x1 * zoom), int(y1 * zoom)
                # Draw rectangle for the row
                cv2.rectangle(img, (x0, y0), (x1, y1), (0, 255, 0), 2)

                # Draw word bounding boxes if available
                if "words" in row and row["words"]:
                    words = row["words"]
                    if isinstance(words, str):
                        try:
                            words = json.loads(words)
                        except:
                            pass

                    if isinstance(words, list):
                        for word in words:
                            if "bbox" in word:
                                word_bbox = word["bbox"]
                                if isinstance(word_bbox, (list, tuple)) and len(word_bbox) >= 4:
                                    wx0, wy0, wx1, wy1 = word_bbox
                                    # Scale coordinates by zoom factor
                                    wx0, wy0, wx1, wy1 = int(wx0 * zoom), int(wy0 * zoom), int(wx1 * zoom), int(wy1 * zoom)
                                    # Draw rectangle for the word
                                    cv2.rectangle(img, (wx0, wy0), (wx1, wy1), (255, 0, 0), 1)

    # Display the image
    if save:
        cv2.imwrite("debug/original_coordinates.png", img)
    else:
        cv2.imshow("Original Coordinates", img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

    return img


if __name__ == "__main__":

    from src.atom.roiextraction import safe_parse_color, safe_literal_eval

    raw_data_path = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\23\sources\cdrawings24-march-charlie-test-filessourcescombinedarea-111pdf\data.feather"
    df = pd.read_feather(raw_data_path)

    pdf_page = 290

    # df[df["pdf_page"] == pdf_page][["value", "color", "coordinates", "coordinates2", "words"]].to_excel(r"C:\Drawings\24-march-charlie-test-files\extraction_results\raw_data_222.xlsx")

    df_page = df[df["pdf_page"] == pdf_page]

    df_page.to_excel(rf"C:\Drawings\24-march-charlie-test-files\extraction_results\raw_data_{pdf_page}.xlsx")

    exit()

    print(df_page)

    df_page["color"] = df_page["color"].apply(safe_parse_color)

    df_page["coordinates"] = df_page["coordinates"].apply(safe_literal_eval)
    df_page["coordinates2"] = df_page["coordinates2"].apply(safe_literal_eval)

    pdf_path = r"C:/Drawings//24-march-charlie-test-files\sources/combined/AREA 111.pdf"

    doc = fitz.open(pdf_path)

    page = doc[pdf_page - 1]

    # Display the original coordinates
    # draw_original_coordinates(df_page[df_page["color"] == (255,0,0)], page, save=True, zoom=4)

    patched_df = patch_bboxes(df_page[df_page["color"] == (255,0,0)], page)

    patched_df[["value", "color", "coordinates", "coordinates2", "words"]].to_excel(r"C:\Drawings\24-march-charlie-test-files\extraction_results\raw_data_222.xlsx")

    draw_result(patched_df, page, save=True, zoom=4)

    print(patched_df)
