import os
import shutil
import sys

from os import path
from subprocess import Popen

from src.updater import getHashBlake2b

basedir = "C:/Program Files (x86)/Inno Setup 6"
# compiler_exe = path.join(basedir, "Compil32.exe")
compiler_exe = path.join(basedir, "ISCC.exe")

iss_path = os.path.abspath("inno/setup_temp.iss")

# DO NOT CHANGE THIS
APP_GUID = "EC539667-9B83-4A38-B41A-73932750094B"

APP_URL = "https://www.architekt-is.com/"

APP_PUBLISHER = "Architekt IS"

# Copy the source here
DEST_EXE = path.join(path.dirname(__file__), "ATEM.exe")

OUTPUT_DIR = path.join(path.dirname(__file__), "Output")

def get_template(full_exe_path, version) -> str:
    dest = DEST_EXE
    shutil.copy(full_exe_path, dest)
    full_exe_path = dest
    exe_name = path.basename(full_exe_path)
    print(version)

    return rf"""; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "ATEM"
#define MyAppVersion "{version}"
#define MyAppPublisher "{APP_PUBLISHER}"
#define MyAppURL "{APP_URL}"
#define MyAppExeName "{exe_name}"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
SignTool=azuresign
AppId={{{{{APP_GUID}}}
AppName={{#MyAppName}}
AppVersion={{#MyAppVersion}}
;AppVerName={{#MyAppName}} {{#MyAppVersion}}
AppPublisher={{#MyAppPublisher}}
AppPublisherURL={{#MyAppURL}}
AppSupportURL={{#MyAppURL}}
AppUpdatesURL={{#MyAppURL}}
DefaultDirName={{autopf}}\{{#MyAppName}}
DisableDirPage=yes
DisableProgramGroupPage=yes
LicenseFile={path.dirname(__file__)}\license.txt
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
OutputBaseFilename=atem-setup-{version}
SetupIconFile={path.dirname(__file__)}\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked

[Files]
Source: "{full_exe_path}"; DestDir: "{{app}}"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{{autoprograms}}\\{{#MyAppName}}"; Filename: "{{app}}\{{#MyAppExeName}}"
Name: "{{autodesktop}}\\{{#MyAppName}}"; Filename: "{{app}}\{{#MyAppExeName}}"; Tasks: desktopicon

[Run]
Filename: "{{app}}\{{#MyAppExeName}}"; Description: "{{cm:LaunchProgram,{{#StringChange(MyAppName, '&', '&&')}}}}"; Flags: nowait postinstall skipifsilent


"""

def compiler_exists() -> bool:
    return (path.exists(compiler_exe))

def build_installer(full_exe_path, signtool_exe: str = "", dlib: str = "", metadata_path = "") -> str:
    exe_name = path.basename(full_exe_path)
    version = exe_name.split("-")[1]

    if signtool_exe:
        signtool_cmd = f"\'/Sazuresign={signtool_exe} sign /v /debug /fd SHA256 /tr \"http://timestamp.acs.microsoft.com\" /td SHA256 /dlib \"{dlib}\" /dmdf \"{metadata_path}\" $f\'"
    else:
        signtool_cmd = ""

    template_iss = get_template(full_exe_path=full_exe_path, version=version)
    # print(template_iss)
    with open(iss_path, "w") as specfile:
        specfile.write(template_iss)
    # "c:\Users\<USER>\Documents\GitHub\Architekt-ATOM\build\Microsoft.Windows.SDK.BuildTools\bin\10.0.26100.0\x64\signtool.exe" $p
    
    # check_output([compiler_exe, iss_path])

    cmd = ["&", f"'{compiler_exe}'", "/q", f"\"{iss_path}\""]
    if signtool_cmd:
        cmd = cmd + [f"{signtool_cmd}"]

    print(' '.join(cmd))

    Popen(['powershell.exe'] + cmd, stdout=sys.stdout, stderr=sys.stderr).communicate()[0]


    print("Cleanup...")
    try:
        os.remove(DEST_EXE)
        print("Removed", DEST_EXE)
    except:
        pass

    print()
    print()
    print("Installer created!!!")

    setup_exe = path.join(OUTPUT_DIR, f"atem-setup-{version}.exe")

    print("Output dir -", setup_exe)

    hash = getHashBlake2b(path.join(OUTPUT_DIR, f"atem-setup-{version}.exe"))
    print(version)
    print("IMPORTANT - To deploy, update latest-release.txt on Azure")
    print("Note - if url of blob is different to the one below, it must be manually updated to the correct one")
    print("Example latest-release.txt...")
    print()
    print()

    print("--- latest-release.txt ----")
    print(version)
    print(f"https://atemexe.blob.core.windows.net/atem-pr-01/atem-setup-{version}.exe")
    print(hash)

    print("--- END ----")

    print()
    print()
    

    # Popen(f'explorer {path.dirname(OUTPUT_DIR)}\\')
    print("Building finished")
    return setup_exe
