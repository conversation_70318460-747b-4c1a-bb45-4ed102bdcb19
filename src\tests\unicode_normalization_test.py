# https://towardsdatascience.com/what-on-earth-is-unicode-normalization-56c005c55ad0
# https://towardsdatascience.com/difference-between-nfd-nfc-nfkd-and-nfkc-explained-with-python-code-e2631f96ae6c
# https://proinsias.github.io/til/Python-Unicodedata-normalize-text/
# https://stackoverflow.com/a/77432079
# https://www.unicode.org/faq/normalization.html

import pandas as pd
from unicodedata import normalize  # Python func for normalizing

# Populate this column with whatever characters/string to measure length
d = {
    'Character Test': ['ABC', 'normal set', '\xa0', '\u00C7', u'C\u0327'],
}

df = pd.DataFrame(data=d)

is_string_type = df['Character Test'].apply(lambda x: isinstance(x, (str, bytes))).all()
print(is_string_type)

# Get the lengths of each
lengths = df['Character Test'].str.len()
df['lengths'] = lengths

normalized_lengths = df['Character Test'].str.normalize('NFKD').str.len()
df['normalized_lengths'] = normalized_lengths

print(df)

print()

print(f"Maximum field length is {lengths.max()} (normalized={normalized_lengths.max()})")
