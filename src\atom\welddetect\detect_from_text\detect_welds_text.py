import pandas as pd
import re, os, ast
from time import time

from src.utils.logger import logger

# Set up logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# def parse_coordinates(coord_str):
#     try:
#         return ast.literal_eval(coord_str)
#     except (ValueError, SyntaxError):
#         return None

def parse_coordinates(coord_str):
    try:
        coords = ast.literal_eval(coord_str)
        if isinstance(coords, (list, tuple)) and len(coords) == 4:
            return coords
        else:
            return [None, None, None, None]
    except (ValueError, SyntaxError):
        return [None, None, None, None]
    
def safe_string_conversion(value):
    """Safely convert various types to string."""
    if pd.isna(value):
        return ""
    elif isinstance(value, (int, float)):
        return str(value)
    elif isinstance(value, str):
        return value
    else:
        return str(value)

def detect_field_welds(df, initial_patterns, use_regex=False):
    fw_locators = initial_patterns['fw']
    exact_match_patterns = {
    "pattern1": r"(?i)(?<!\w)(?<!E)(?:FW|FFW|F\.W|F\.F\.W|F\.W\.|F\.F\.W\.)(?:\s*-\s*\d{1,5}|\s*-?\s*W\d{1,10}_\d{1,4})\b",
    "pattern2": r"(?i)(?<!\w)(?<!E)(?:W\d{1,10}_\d{1,4})\b",
    "pattern3": r"(?i)(?<!\w)(?<!E)(?:FW|FFW|F\.W|F\.F\.W|F\.W\.|F\.F\.W\.)\s*-?\s*[A-Z0-9._-]{5,30}\b"
}

    columns = ['pdf_id', 'pdf_page', 'object_type', 'object_label', 'object_value', 
               'object_group_num', 'size', 'coordinates', 'coordinates2',
               'c1_x0', 'c1_y0', 'c1_x1', 'c1_y1', 'c2_x0', 'c2_y0', 'c2_x1', 'c2_y1']
    result_df = pd.DataFrame(columns=columns)
    
    group_num = 0
    processed_coords = set()

    for _, row in df.iterrows():
        value = safe_string_conversion(row['value']).strip().upper()
        coords = safe_string_conversion(row['coordinates'])
        coords2 = safe_string_conversion(row['coordinates2'])

        if coords in processed_coords:
            continue

        # # Check for full string matches first
        # full_match = False
        # for pattern in exact_match_patterns.values():
        #     match = re.search(pattern, value)
        #     if match and match.group() == value:
        #         group_num += 1
        #         logger.info(f"Found full match: {value} (Group {group_num})")
        #         result_df = pd.concat([result_df, pd.DataFrame({
        #             'pdf_id': [row['pdf_id']],
        #             'pdf_page': [row['pdf_page']],
        #             'object_type': ['fw'],
        #             'object_label': ['FW'],
        #             'object_value': [value],
        #             'object_group_num': [group_num],
        #             'size': [None],
        #             'coordinates': [coords],
        #             'coordinates2': [coords2]
        #         })], ignore_index=True)
        #         processed_coords.add(coords)
        #         full_match = True
        #         break

        # Check for exact matches in initial_patterns
        full_match = False
        for locator in fw_locators:
            if locator.upper() == value:
                group_num += 1
                logger.info(f"Found exact match: {value} (Group {group_num})")
                result_df = pd.concat([result_df, pd.DataFrame({
                    'pdf_id': [row['pdf_id']],
                    'pdf_page': [row['pdf_page']],
                    'object_type': ['fw'],
                    'object_label': [locator],
                    'object_value': [value],
                    'object_group_num': [group_num],
                    'size': [None],
                    'coordinates': [coords],
                    'coordinates2': [coords2]
                })], ignore_index=True)
                processed_coords.add(coords)
                full_match = True
                break
        
        if full_match:
            continue

        if use_regex:

            # Check for locator words
            for locator in fw_locators:
                if locator.upper() in value:
                    group_num += 1
                    logger.info(f"Found locator: {locator} (Group {group_num})")
                
                    # Find the corresponding value
                    value_row = df[(df['coordinates'] == coords) & (df['value'] != value)].iloc[0] if not df[(df['coordinates'] == coords) & (df['value'] != value)].empty else row
                    weld_value = value_row['value']
                
                    for pattern in exact_match_patterns.values():
                        match = re.search(pattern, weld_value)
                        if match:
                            weld_value = match.group()
                            break
                
                    logger.info(f"Corresponding value: {weld_value}")
                
                    result_df = pd.concat([result_df, pd.DataFrame({
                        'pdf_id': [row['pdf_id']],
                        'pdf_page': [row['pdf_page']],
                        'object_type': ['fw'],
                        'object_label': [locator],
                        'object_value': [weld_value],
                        'object_group_num': [group_num],
                        'size': [None],
                        'coordinates': [coords],
                        'coordinates2': [coords2]
                    })], ignore_index=True)
                    processed_coords.add(coords)
                    break

    # # Add parsed coordinate columns
    # for coord_type in ['coordinates', 'coordinates2']:
    #     parsed = result_df[coord_type].apply(parse_coordinates)
    #     result_df[[f'{coord_type[0]}1_x0', f'{coord_type[0]}1_y0', f'{coord_type[0]}1_x1', f'{coord_type[0]}1_y1']] = pd.DataFrame(parsed.tolist(), index=result_df.index)

    # In the detect_field_welds function:
    for coord_type in ['coordinates', 'coordinates2']:
        parsed = result_df[coord_type].apply(parse_coordinates)
        result_df[[f'{coord_type[0]}1_x0', f'{coord_type[0]}1_y0', f'{coord_type[0]}1_x1', f'{coord_type[0]}1_y1']] = pd.DataFrame(parsed.tolist(), index=result_df.index)

    return result_df



# Usage
initial_patterns = {
    "fw": ["FW", "FFW", "F.W", "F.F.W", "F.W.", "F.F.W."]
}

ignore_patterns = ["EFW"]  # Add any other patterns to ignore here

#////// REQ. INPUT \\\\\\\
filename_prefix = "T-Rack"
#raw_data_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Data\Final\Raw Data"

#raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Data\Final\Raw Data\Z5 Raw.xlsx"
#raw_data_path = os.path.join(raw_data_dir, f"{filename_prefix} Raw.xlsx")

raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\Weld Detection\T-Rack\T-Rack Raw Data.xlsx"

#////// REQ. INPUT \\\\\\\


output_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\Weld Detection\T-Rack"

excel_output_path =  os.path.join(output_folder, f"{filename_prefix}_field_maps.xlsx")

start_time = time()

# Load the Excel file
df = pd.read_excel(raw_data_path)

bm1 = time()

print(f"\n\n--> BM1: Load Raw Data in {bm1-start_time} seconds...")


# Call detect field weld function
result_df = detect_field_welds(df, initial_patterns,use_regex=False)

bm2 = time()

print(f"\n\n--> BM2: Detect Field Welds in {bm2-bm1} seconds...\nElapsed time: {bm2-start_time}\n\nExporting Results...")


result_df.to_excel(excel_output_path, index=False)

end_time = time()

print(f"\n\nFinished...\nData exported to {excel_output_path}..\n Total elapsed time: {end_time-start_time}")
# print(result_df)