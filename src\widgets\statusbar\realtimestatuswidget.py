from PySide6.QtWidgets import QPushButton, QMenu, QLabel
from PySide6.QtGui import QMovie, QIcon
from PySide6.QtCore import Signal, QTimer
from pubsub import pub
from src.pyside_util import get_resource_qicon


class RealTimeStatusWidget(QPushButton):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        # self.setObjectName("")
        self.menuActions = {
        }
        self.timer = QTimer(self)
        self.jobItems = {}
        self.setText("")
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        # self.setMenu(QMenu(self))
        pub.subscribe(self.setStatusbarMessageRealtime, "set-statusbar-realtime")

        self.setIcon(get_resource_qicon("info.svg"))
        self.hide()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.onTimer)

    def setStatusbarMessageRealtime(self, message, jobId=None):
        self.sgnUpdateStatus.emit(message)

    def updateAnimation(self):
        self.setIcon(QIcon(self.movie.currentPixmap()))

    def onUpdateStatus(self, message):
        self.show()
        self.setText(f" {message}")
        self.timer.start(3000)

    def onTimer(self, event=None):
        self.setText("")
        self.hide()

