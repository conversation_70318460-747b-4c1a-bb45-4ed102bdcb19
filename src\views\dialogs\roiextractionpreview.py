from PySide6.QtGui import QCloseEvent
from PySide6.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QPushButton, QTab<PERSON>idget, QLabel, QButtonGroup
from PySide6.QtCore import QSize, Signal
from src.views.tabbuttons import TabButtons
from src.views.tables import (BomResultsView, GeneralDataView, OutlierDataView, RfqDataView, 
                            SpecResultsView, SpoolResultsView)
from src.widgets.groupedtableview import GroupedTableView
from collections import OrderedDict
from src.utils.logger import logger
import pandas as pd
from src.data.tables.hiddentablefields import hidden_table_fields
from src.app_paths import getSavedFieldMapJson


# logger = logging.getLogger()

_TABLE_VIEWS = {
    "Bill of Materials": BomResultsView,
    "General Data": GeneralDataView,
    "Outlier Data": OutlierDataView,
    "Spec Data": SpecResultsView,
    "Spool Data": SpoolResultsView,
    "RFQ": RfqDataView,
}

data_sections = OrderedDict()

data_sections["BOM"] = {
    "payload_name": "bom_data",
    "title": "Bill of Materials",
}

data_sections["General"] = {
    "payload_name": "general_data",
    "title": "General Data",
}

data_sections["SPOOL"] = {
    "payload_name": "spool_data",
    "title": "Spool Data",
}

data_sections["SPEC"] = {
    "payload_name": "spec_data",
    "title": "Spec Data",
}

data_sections["Outlier"] = {
    "payload_name": "outlier_data",
    "title": "Outlier Data",
}


class PreviewResults(GroupedTableView):

    def __init__(self, parent):
        super().__init__(parent, checkboxStyle=None)


class RoiExtractionPreview(QWidget):

    cancelled = Signal()
    sgnConfirmExtraction = Signal(object, int)  # rotation
    sgnTabBarUpdated = Signal()
    def __init__(self, parent, projectData):
        super().__init__(parent)
        self.projectData = projectData
        self.jsonData = {}
        self.views = {}
        self.proceeded: bool = False

        self.setLayout(QVBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.setMinimumSize(QSize(1024, 768))
        self.setWindowTitle("ROI Extraction Preview")

        desc = QLabel("Page transformation can be applied to drawing analysis. To proceed, preview the different transformation results and select the best fit.")
        desc.setContentsMargins(8,8,8,8)
        self.layout().addWidget(desc)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        self.btns = {}
        self.btnGroup = QButtonGroup(self)
        for rot in [0, 90, 180, 270]:
            pb = QPushButton(f"{rot}°")
            self.btns[rot] = pb
            pb.setMinimumHeight(48)
            pb.setCheckable(True)
            hbox.layout().addWidget(pb)
            self.btnGroup.addButton(pb)
        self.btns[0].setChecked(True)

        self.tabs = QTabWidget(self)
        self.tabs.tabBar().hide()
        self.tabButtons = TabButtons(self, self.tabs, objectName="projectTabs")

        self.layout().addWidget(self.tabs)
        self.layout().addWidget(self.tabButtons)

        fieldMapJson = getSavedFieldMapJson()
        self.simpleFieldMap = {}
        self.simpleFieldMap.update(fieldMapJson.get("fields", {}))

        self.proceed = QPushButton("Proceed")
        self.proceed.setMinimumHeight(48)
        self.layout().addWidget(self.proceed)
        self.proceed.clicked.connect(self.onProceed)

        self.setWindowIcon(self.topLevelWidget().windowIcon())
    
    @property
    def projectId(self):
        """Returns the projectId for currently opened project, else None"""
        return self.projectData.get("projectId")

    def setResult(self, result):
        self.jsonData = result["jsonData"]
        self.result = result
        finalDf = self.result["data"]
        self.updateTableResults(finalDf)
        self.updateTabButtons()
        self.raise_()

    def updateTableResults(self, tableResults):
        """This will remove Tables tabs if no data exists"""
        # Check and create tabs for each section if they exist
        print("Results for: ", tableResults.keys())
        try:
            for name, params in data_sections.items():
                payload_name = params["payload_name"]
                title = params["title"]
                if payload_name not in tableResults.keys():
                    continue
                title = params["title"]
                data = tableResults.get(payload_name, None)
                self.setTableResult(name, 
                                    title, 
                                    data,
                                    displayName=f"{title}*")
        except Exception as e:
            logger.error(f"Failed to set table results: {e}", exc_info=True)

    def setTableResult(self,
                       name, 
                       title, 
                       data, 
                       displayName=None):
        displayName=None # override
        if data is None or not isinstance(data, pd.DataFrame) or data.empty:
            return
        # Create a new table view
        try:
            tableView = self.views[name] = PreviewResults(self)
            self.tabs.addTab(tableView, displayName if displayName else title)
            tableView.fieldMap = self.simpleFieldMap
            tableView.setTableData(data)
            tableView.setHiddenColumns(hidden_table_fields["ALL"])
            tableView.restoreDefaultColumnOrder()
            tableView.autoSizeColumns()
        except Exception as e:
            logger.error(e)
            logger.error(f"TableView class for `{title}` not implemented? {e}", exc_info=True)
            return
        self.tabs.setTabText(self.tabs.indexOf(tableView), displayName if displayName else title)
        # self.sgnTabBarUpdated.emit()
        # tableView.sgnUpdateDf.emit(data)

    def updateTabButtons(self):
        self.tabButtons.update()
    
    def onProceed(self):
        for rot, btn in self.btns.items():
            if btn.isChecked():
                self.sgnConfirmExtraction.emit(self.jsonData.copy(), rot)
                break

    def closeEvent(self, event: QCloseEvent) -> None:
        self.cancelled.emit()
        return super().closeEvent(event)