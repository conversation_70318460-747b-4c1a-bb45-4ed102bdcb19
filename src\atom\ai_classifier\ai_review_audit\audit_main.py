"""
BOM Classification Audit System

This module provides a comprehensive audit system for validating AI-classified
Bill of Materials (BOM) items using Gemini models with structured JSON output.

The system validates classifications against field descriptions, valid options,
and cross-field validation rules to detect and correct classification errors.
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import pandas as pd
from datetime import datetime

# Import the categorization table from the existing prompts module
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from prompts import categorization_table

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import SystemMessage, HumanMessage
    from pydantic import BaseModel, Field
    LANGCHAIN_GENAI_AVAILABLE = True
except ImportError:
    LANGCHAIN_GENAI_AVAILABLE = False
    logging.warning("Langchain Google GenAI not available. Install with: pip install langchain-google-genai")



def normalize_rating_column(df: pd.DataFrame) -> pd.DataFrame:
    """
    Normalize rating column values by removing decimal points for whole numbers.
    This ensures clean data for the audit system while preserving non-numeric formats.

    Args:
        df: DataFrame that may contain a 'rating' column

    Returns:
        DataFrame with normalized rating values
    """
    if 'rating' not in df.columns:
        return df

    def normalize_rating_value(value):
        if pd.isna(value) or value is None:
            return value
        try:
            # Convert to float first, then to int if it's a whole number
            float_val = float(value)
            if float_val.is_integer():
                return int(float_val)
            return float_val
        except (ValueError, TypeError):
            # Keep original value if it can't be converted (e.g., '3000#', '3000lb')
            return value

    df = df.copy()
    df['rating'] = df['rating'].apply(normalize_rating_value)
    return df


class ModelType(Enum):
    """Supported model types for BOM classification audit"""
    GEMINI_20_FLASH = "gemini-2.0-flash"
    GEMINI_25_FLASH = "gemini-2.5-flash-preview-04-17"
    GEMINI_25_PRO = "gemini-2.5-pro-preview-03-25"


@dataclass
class AuditConfig:
    """Configuration for the BOM audit system"""
    primary_model: ModelType = ModelType.GEMINI_20_FLASH
    fallback_model: ModelType = ModelType.GEMINI_25_FLASH
    max_retries: int = 3
    timeout: int = 30
    temperature: float = 0.0
    max_output_tokens: int = 2000
    api_key: Optional[str] = None


@dataclass
class AuditResult:
    """Result of auditing a single BOM item"""
    id: str
    status: str  # "ok", "issues", "error"
    issues: Dict[str, Dict[str, Any]] = None
    model_used: str = None
    processing_time: float = 0.0
    error_message: str = None

    def __post_init__(self):
        if self.issues is None:
            self.issues = {}


class CrossFieldValidationRules:
    """Defines cross-field validation rules for BOM classification"""

    @staticmethod
    def get_rules() -> List[Dict[str, Any]]:
        """Returns a list of cross-field validation rules"""
        return [
            # ASTM to Material mapping rules
            {
                "name": "ASTM A106 Carbon Steel Rule",
                "condition": {"field": "astm", "values": ["A106"]},
                "requirement": {"field": "material", "values": ["Steel, Carbon"]},
                "explanation": "ASTM A106 is specifically for seamless carbon steel pipes"
            },
            {
                "name": "ASTM A312 Stainless Steel Rule",
                "condition": {"field": "astm", "values": ["A312"]},
                "requirement": {"field": "material", "values": ["Steel, Stainless"]},
                "explanation": "ASTM A312 is for seamless and welded austenitic stainless steel pipes"
            },
            {
                "name": "ASTM A182 Stainless Steel Rule",
                "condition": {"field": "astm", "values": ["A182"]},
                "requirement": {"field": "material", "values": ["Steel, Stainless"]},
                "explanation": "ASTM A182 covers forged or rolled alloy and stainless steel pipe flanges"
            },
            {
                "name": "ASTM A105 Carbon Steel Rule",
                "condition": {"field": "astm", "values": ["A105"]},
                "requirement": {"field": "material", "values": ["Steel, Carbon"]},
                "explanation": "ASTM A105 is for carbon steel forgings for piping applications"
            },
            # Insufficient context rules
            {
                "name": "Insufficient Context Classification Rule",
                "condition": {"field": "material_description", "pattern": r"insufficient_context"},
                "requirement": {"field": "rfq_scope", "values": ["Miscellaneous"]},
                "explanation": "Items with insufficient context should be classified as Miscellaneous"
            },
            # Category-specific validation rules
            {
                "name": "Pipe Category Consistency Rule",
                "condition": {"field": "rfq_scope", "values": ["Pipe"]},
                "requirement": {"field": "pipe_category", "not_null": True},
                "explanation": "Items classified as Pipe must have a specific pipe_category"
            },
            {
                "name": "Valve Type Consistency Rule",
                "condition": {"field": "rfq_scope", "values": ["Valves"]},
                "requirement": {"field": "valve_type", "not_null": True},
                "explanation": "Items classified as Valves must have a specific valve_type"
            },
            {
                "name": "Fitting Category Consistency Rule",
                "condition": {"field": "rfq_scope", "values": ["Fittings", "Flanges"]},
                "requirement": {"field": "fitting_category", "not_null": True},
                "explanation": "Items classified as Fittings or Flanges must have a specific fitting_category"
            },
            {
                "name": "Bolt Category Consistency Rule",
                "condition": {"field": "rfq_scope", "values": ["Bolts"]},
                "requirement": {"field": "bolt_category", "not_null": True},
                "explanation": "Items classified as Bolts must have a specific bolt_category"
            },
            {
                "name": "Gasket Category Consistency Rule",
                "condition": {"field": "rfq_scope", "values": ["Gaskets"]},
                "requirement": {"field": "gasket_category", "not_null": True},
                "explanation": "Items classified as Gaskets must have a specific gasket_category"
            }
        ]


class SystemPromptBuilder:
    """Builds comprehensive system prompts for BOM classification audit"""

    def __init__(self, excluded_fields=None):
        self.categorization_fields = {item["field"]: item for item in categorization_table}
        self.validation_rules = CrossFieldValidationRules.get_rules()
        # Fields to exclude from prompts (already handled elsewhere)
        self.excluded_fields = excluded_fields or [
            'unit_of_measure', 'size', 'size1', 'size2', 'quantity',
            'componentCategory', 'component_category', 'ef', 'sf'
        ]

    def build_system_prompt(self) -> str:
        """Builds the complete system prompt for BOM audit"""

        prompt = """You are a precise BOM (Bill of Materials) classification auditor for an industrial piping company.

Your task is to review AI-classified piping materials and validate them against material descriptions, field definitions, valid options, and cross-field validation rules.

CRITICAL INSTRUCTIONS:
1. Only classify based on explicit information in the material description
2. For rfq_scope classification: Assign 'Miscellaneous' to rfq_scope only when the material description provides no substantive information about the item's actual type and consists primarily of an opaque identifier such as a vendor-specific code (e.g., 'VENDOR P123-ABC'), an internal part number (e.g., 'ID-98765'), or a similar non-descriptive code without further context. Crucially, do not default rfq_scope to 'Miscellaneous' if the general item type (e.g., pipe, valve, fitting) is discernible from the description, even if other specific attributes of that item are unclear or prove difficult to classify.
3. Follow the specific cross-field validation rules provided
4. Provide confidence scores between 0.0 and 1.0 for any suggested corrections
5. If a field's value is correct according to the description and rules, do not list it as an issue
6. If you cannot confidently suggest a correction, set "suggested" to null and use a low confidence score

FIELD DEFINITIONS AND VALID OPTIONS:
"""

        # Add field definitions (excluding specified fields)
        for field_name, field_info in self.categorization_fields.items():
            if field_name not in self.excluded_fields:
                prompt += f"\n{field_name.upper()}:\n"
                prompt += f"  Description: {field_info['description']}\n"
                prompt += f"  Valid Options: {field_info['options']}\n"

        prompt += "\nCROSS-FIELD VALIDATION RULES:\n"

        # Add validation rules
        for rule in self.validation_rules:
            prompt += f"\n- {rule['name']}: {rule['explanation']}\n"
            if 'condition' in rule and 'requirement' in rule:
                condition = rule['condition']
                requirement = rule['requirement']
                prompt += f"  If {condition['field']} is {condition.get('values', condition.get('pattern', 'matched'))}, "
                prompt += f"then {requirement['field']} should be {requirement.get('values', 'not null')}\n"

        prompt += """
RESPONSE FORMAT:
You must respond with valid JSON in exactly this structure:

{
    "status": "ok" | "issues",
    "issues": {
        "field_name": {
            "current_value": "the_current_value_from_input",
            "confidence": 0.0-1.0,
            "explanation": "Brief reason why the current value is incorrect based on description and rules",
            "suggested": "corrected_value_or_null"
        }
    }
}

IMPORTANT:
- If status is "ok", the issues object should be empty: {}
- If status is "issues", include only fields that have problems
- Confidence should reflect your certainty in the suggested correction
- Set suggested to null if you cannot confidently provide a correction
- Base all decisions on the material description and the rules provided above
"""

        return prompt


# Pydantic models for structured output
from pydantic import field_validator

class AuditResponse(BaseModel):
    """Structured response from the audit model"""
    status: str = Field(..., description="Either 'ok' or 'issues'")
    issues: Dict[str, Any] = Field(default_factory=dict, description="Dictionary of field issues where each value is a dict with current_value, confidence, explanation, suggested")

    @field_validator('issues', mode='before')
    @classmethod
    def parse_issues_json(cls, v):
        """Parse issues field if it comes as a JSON string"""
        if isinstance(v, str):
            try:
                import json
                return json.loads(v)
            except json.JSONDecodeError:
                # If parsing fails, return empty dict
                return {}
        return v


class RequestBuilder:
    """Builds individual audit requests for BOM items"""

    def __init__(self, excluded_fields=None):
        self.categorization_fields = {item["field"]: item for item in categorization_table}
        # Fields to exclude from prompts (already handled elsewhere)
        self.excluded_fields = excluded_fields or [
            'unit_of_measure', 'size', 'size1', 'size2', 'quantity',
            'componentCategory', 'component_category', 'ef', 'sf'
        ]

    def _normalize_rating_value(self, value):
        """
        Normalize rating values by removing decimal points if they represent whole numbers

        Args:
            value: The rating value to normalize

        Returns:
            Normalized rating value as string
        """
        if value is None or pd.isna(value):
            return ""

        # Convert to string first
        str_value = str(value).strip()

        # If it's empty, return empty
        if not str_value:
            return ""

        try:
            # Try to convert to float, then to int if it's a whole number
            float_value = float(str_value)
            if float_value.is_integer():
                return str(int(float_value))
            else:
                return str_value
        except (ValueError, TypeError):
            # If conversion fails, return original string
            return str_value

    def build_request(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Builds an audit request for a single BOM item

        Args:
            row_data: Dictionary containing BOM item data with 'id', 'material_description',
                     and classification field values

        Returns:
            Dictionary containing the formatted request
        """

        # Extract required fields
        item_id = row_data.get('id', 'unknown')
        material_description = row_data.get('material_description', '')

        # Extract current classification values (excluding specified fields)
        current_classification = {}
        for field_name in self.categorization_fields.keys():
            if field_name not in self.excluded_fields and field_name in row_data:
                value = row_data[field_name]
                # Convert NaN/None to empty string for consistency
                if pd.isna(value) or value is None:
                    value = ""

                # Apply special normalization for rating field
                if field_name == "rating":
                    value = self._normalize_rating_value(value)
                else:
                    value = str(value).strip()

                current_classification[field_name] = value

        # Build the user prompt
        user_prompt = f"""
REVIEW THE FOLLOWING BOM CLASSIFICATION:

Material Description: "{material_description}"

Current Classification:
{json.dumps(current_classification, indent=2)}

Analyze each field in the current classification against the material description and the validation rules provided in the system prompt.

If any field is incorrect or violates the rules, include it in the issues object with:
- current_value: the value that was provided
- confidence: your confidence level (0.0-1.0) in the suggested correction
- explanation: why the current value is incorrect
- suggested: your recommended correction (or null if uncertain)

If all fields are correct, return status "ok" with an empty issues object.
"""

        return {
            "id": item_id,
            "material_description": material_description,
            "current_classification": current_classification,
            "user_prompt": user_prompt.strip()
        }


class LangchainModelHandler:
    """Handles Langchain Google GenAI model configuration and API calls"""

    def __init__(self, config: AuditConfig):
        self.config = config
        self.system_prompt = SystemPromptBuilder().build_system_prompt()
        self.models = {}

        if LANGCHAIN_GENAI_AVAILABLE:
            self._setup_models()
        else:
            logging.error("Langchain Google GenAI not available. Please install langchain-google-genai package.")

    def _setup_models(self):
        """Initialize Langchain Google GenAI models with configuration"""

        # Configure API key - Langchain uses GOOGLE_API_KEY
        api_key = self.config.api_key or os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY or GEMINI_API_KEY not found in environment variables or config")

        # Set the environment variable for Langchain
        if api_key:
            os.environ["GOOGLE_API_KEY"] = api_key

        # Initialize models with structured output
        for model_type in [self.config.primary_model, self.config.fallback_model]:
            try:
                # Create base model with enhanced retry settings for production
                base_model = ChatGoogleGenerativeAI(
                    model=model_type.value,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_output_tokens,
                    max_retries=5,  # Increased from config default for production
                    timeout=60      # 60 second timeout per request
                    # Langchain handles exponential backoff internally
                )

                # Create structured output model
                structured_model = base_model.with_structured_output(AuditResponse)
                self.models[model_type] = structured_model
                logging.info(f"Initialized {model_type.value} model successfully")
            except Exception as e:
                logging.error(f"Failed to initialize {model_type.value}: {e}")

    async def call_model(self, model_type: ModelType, user_prompt: str, item_id: str = None) -> Dict[str, Any]:
        """
        Call the specified Langchain model with retry logic

        Args:
            model_type: The model type to use
            user_prompt: The user prompt to send
            item_id: Optional item ID for logging

        Returns:
            Parsed response from the model as dictionary
        """

        if not LANGCHAIN_GENAI_AVAILABLE:
            raise RuntimeError("Langchain Google GenAI not available")

        if model_type not in self.models:
            raise ValueError(f"Model {model_type.value} not initialized")

        model = self.models[model_type]

        try:
            # Create messages for Langchain
            messages = [
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=user_prompt)
            ]

            # Log the request for debugging (COMMENTED OUT FOR PRODUCTION)
            # self._log_request_response(item_id, user_prompt, None, "REQUEST")

            # Make the API call - Langchain handles retries internally based on max_retries parameter
            response = await model.ainvoke(messages)

            # Handle structured output response
            if hasattr(response, 'model_dump'):
                # Pydantic v2 structured response
                result = response.model_dump()
            elif hasattr(response, 'dict'):
                # Pydantic v1 structured response
                result = response.dict()
            else:
                # This shouldn't happen with structured output, but handle it
                raise ValueError(f"Unexpected response type: {type(response)}")

            # Validate response structure
            if not isinstance(result, dict) or "status" not in result:
                raise ValueError("Invalid response structure from model")

            # Log the response for debugging (COMMENTED OUT FOR PRODUCTION)
            # self._log_request_response(item_id, user_prompt, result, "RESPONSE")

            return result

        except Exception as e:
            # Log the error - Langchain has already handled retries at this point
            error_str = str(e).lower()
            is_rate_limit = any(keyword in error_str for keyword in [
                'rate limit', 'quota', 'too many requests', '429', 'resource_exhausted'
            ])

            if is_rate_limit:
                logging.error(f"Rate limit exceeded for item {item_id} after retries: {e}")
            else:
                logging.error(f"Model call failed for item {item_id} after retries: {e}")

            raise

    def _log_request_response(self, item_id: str, user_prompt: str, response: Dict[str, Any], log_type: str):
        """Log request and response data for debugging"""
        try:
            log_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\test_output"
            os.makedirs(log_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_item_id = str(item_id).replace("/", "_").replace("\\", "_") if item_id else "unknown"

            if log_type == "REQUEST":
                log_file = os.path.join(log_dir, f"request_{safe_item_id}_{timestamp}.txt")
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.write(f"ITEM ID: {item_id}\n")
                    f.write(f"TIMESTAMP: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    f.write("USER PROMPT:\n")
                    f.write("=" * 80 + "\n")
                    f.write(user_prompt)
                    f.write("\n" + "=" * 80 + "\n")

            elif log_type == "RESPONSE":
                log_file = os.path.join(log_dir, f"response_{safe_item_id}_{timestamp}.txt")
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.write(f"ITEM ID: {item_id}\n")
                    f.write(f"TIMESTAMP: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    f.write("MODEL RESPONSE:\n")
                    f.write("=" * 80 + "\n")
                    f.write(json.dumps(response, indent=2, ensure_ascii=False))
                    f.write("\n" + "=" * 80 + "\n")

        except Exception as e:
            logging.warning(f"Failed to log {log_type}: {e}")


class BOMAuditSystem:
    """Main BOM classification audit system"""

    def __init__(self, config: AuditConfig = None):
        self.config = config or AuditConfig()
        self.request_builder = RequestBuilder()
        self.model_handler = LangchainModelHandler(self.config) if LANGCHAIN_GENAI_AVAILABLE else None
        self.metrics = {
            "total_processed": 0,
            "successful_audits": 0,
            "failed_audits": 0,
            "items_with_issues": 0,
            "primary_model_calls": 0,
            "fallback_model_calls": 0,
            "rate_limit_hits": 0,
            "total_backoff_time": 0
        }

    async def audit_item(self, row_data: Dict[str, Any]) -> AuditResult:
        """
        Audit a single BOM item

        Args:
            row_data: Dictionary containing BOM item data

        Returns:
            AuditResult containing the audit outcome
        """

        start_time = datetime.now()
        item_id = row_data.get('id', 'unknown')

        try:
            # Build the request
            request = self.request_builder.build_request(row_data)

            # Try primary model first
            try:
                result = await self.model_handler.call_model(
                    self.config.primary_model,
                    request["user_prompt"],
                    item_id
                )
                model_used = self.config.primary_model.value
                self.metrics["primary_model_calls"] += 1

            except Exception as e:
                logging.warning(f"Primary model failed for item {item_id}: {e}")

                # Fallback to secondary model
                result = await self.model_handler.call_model(
                    self.config.fallback_model,
                    request["user_prompt"],
                    item_id
                )
                model_used = self.config.fallback_model.value
                self.metrics["fallback_model_calls"] += 1

            # Process the result
            processing_time = (datetime.now() - start_time).total_seconds()

            audit_result = AuditResult(
                id=item_id,
                status=result.get("status", "error"),
                issues=result.get("issues", {}),
                model_used=model_used,
                processing_time=processing_time
            )

            # Update metrics
            self.metrics["total_processed"] += 1
            self.metrics["successful_audits"] += 1

            if audit_result.status == "issues" and audit_result.issues:
                self.metrics["items_with_issues"] += 1

            return audit_result

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()

            self.metrics["total_processed"] += 1
            self.metrics["failed_audits"] += 1

            return AuditResult(
                id=item_id,
                status="error",
                processing_time=processing_time,
                error_message=str(e)
            )

    async def audit_dataframe(self, df: pd.DataFrame, max_concurrent: int = 10) -> List[AuditResult]:
        """
        Audit multiple BOM items from a DataFrame

        Args:
            df: DataFrame containing BOM items with required columns
            max_concurrent: Maximum number of concurrent API calls

        Returns:
            List of AuditResult objects
        """

        # Validate required columns
        required_columns = ['id', 'material_description']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Convert DataFrame to list of dictionaries
        items = df.to_dict('records')

        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)

        async def audit_with_semaphore(item):
            async with semaphore:
                return await self.audit_item(item)

        # Process all items concurrently
        logging.info(f"Starting audit of {len(items)} BOM items with max concurrency {max_concurrent}")

        tasks = [audit_with_semaphore(item) for item in items]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions that occurred
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(AuditResult(
                    id=items[i].get('id', f'item_{i}'),
                    status="error",
                    error_message=str(result)
                ))
            else:
                final_results.append(result)

        logging.info(f"Completed audit. Processed: {self.metrics['total_processed']}, "
                    f"Successful: {self.metrics['successful_audits']}, "
                    f"Failed: {self.metrics['failed_audits']}, "
                    f"With Issues: {self.metrics['items_with_issues']}")

        return final_results

    def get_metrics(self) -> Dict[str, Any]:
        """Get current processing metrics"""
        return self.metrics.copy()

    def reset_metrics(self):
        """Reset processing metrics"""
        for key in self.metrics:
            self.metrics[key] = 0


# Convenience function for easy usage
async def audit_bom_dataframe(df: pd.DataFrame,
                             config: AuditConfig = None,
                             max_concurrent: int = 10) -> List[AuditResult]:
    """
    Convenience function to audit a BOM DataFrame

    Args:
        df: DataFrame containing BOM items
        config: Optional audit configuration
        max_concurrent: Maximum concurrent API calls

    Returns:
        List of AuditResult objects
    """

    # Normalize rating column to ensure clean data
    df_normalized = normalize_rating_column(df)

    audit_system = BOMAuditSystem(config)
    return await audit_system.audit_dataframe(df_normalized, max_concurrent)


def apply_audit_corrections(audit_results_df: pd.DataFrame, original_df: pd.DataFrame) -> pd.DataFrame:
    """
    Apply audit corrections to the original DataFrame based on accepted audit results.

    Args:
        audit_results_df: DataFrame with audit results in programmatic format containing:
                         - id, column_name, suggested_value, accept_merge columns
        original_df: Original DataFrame to apply corrections to

    Returns:
        New DataFrame with corrections applied, maintaining original column order
    """

    # Create a copy of the original DataFrame to avoid modifying the original
    corrected_df = original_df.copy()

    # Filter audit results to only include accepted corrections
    accepted_corrections = audit_results_df[
        (audit_results_df['accept_merge'] == True) &
        (audit_results_df['column_name'].notna()) &
        (audit_results_df['suggested_value'].notna())
    ]

    corrections_applied = 0
    corrections_failed = 0

    print(f"Applying {len(accepted_corrections)} accepted corrections...")

    for _, correction in accepted_corrections.iterrows():
        item_id = correction['id']
        column_name = correction['column_name']
        suggested_value = correction['suggested_value']

        try:
            # Find the row in the original DataFrame by id
            if 'id' not in corrected_df.columns:
                print(f"⚠️  Warning: 'id' column not found in original DataFrame")
                corrections_failed += 1
                continue

            mask = corrected_df['id'] == item_id
            matching_rows = corrected_df[mask]

            if len(matching_rows) == 0:
                print(f"⚠️  Warning: No row found with id '{item_id}'")
                corrections_failed += 1
                continue
            elif len(matching_rows) > 1:
                print(f"⚠️  Warning: Multiple rows found with id '{item_id}', updating all")

            # Check if the column exists in the original DataFrame
            if column_name not in corrected_df.columns:
                print(f"⚠️  Warning: Column '{column_name}' not found in original DataFrame for id '{item_id}'")
                corrections_failed += 1
                continue

            # Apply the correction
            corrected_df.loc[mask, column_name] = suggested_value
            corrections_applied += 1

            print(f"✓ Applied correction for id '{item_id}', column '{column_name}': '{suggested_value}'")

        except Exception as e:
            print(f"❌ Error applying correction for id '{item_id}', column '{column_name}': {e}")
            corrections_failed += 1

    print(f"\nCorrection Summary:")
    print(f"  ✓ Successfully applied: {corrections_applied}")
    print(f"  ❌ Failed to apply: {corrections_failed}")
    print(f"  📊 Total attempted: {len(accepted_corrections)}")

    return corrected_df


def create_sample_dataframe() -> pd.DataFrame:
    """Creates a sample DataFrame for testing the audit system"""

    sample_data = {
        'id': ['item_1', 'item_2', 'item_3', 'item_4', 'item_5'],
        'material_description': [
            'Pipe SMLS, ASTM A106, SCH 60, #3000, PBE',  # Should be carbon steel, not stainless
            'VENDOR ASYM18456',  # Should be miscellaneous
            'Ball Valve, ASTM A182, 2", #600, RF',  # Correct stainless steel
            'Elbow 90 LR, ASTM A234, WPB, 4", SCH 40, BE',  # Carbon steel fitting
            'Flange WN, ASTM A105, 6", #300, RF'  # Carbon steel flange
        ],
        'rfq_scope': ['Pipe', 'Pipe', 'Valves', 'Fittings', 'Flanges'],
        'material': ['Steel, Stainless', 'Steel, Carbon', 'Steel, Stainless', 'Steel, Carbon', 'Steel, Carbon'],
        'astm': ['A106', '', 'A182', 'A234', 'A105'],
        'valve_type': ['', '', 'Ball Valve', '', ''],
        'fitting_category': ['', '', '', '90 LR Elbow', 'WN Flange RF'],
        'pipe_category': ['Pipe', '', '', '', ''],
        'schedule': ['60', '', '', '40', ''],
        'rating': ['3000', '', '600', '', '300']
    }

    return pd.DataFrame(sample_data)


def print_audit_results(results: List[AuditResult]):
    """Pretty print audit results for analysis"""

    print(f"\n{'='*80}")
    print("BOM CLASSIFICATION AUDIT RESULTS")
    print(f"{'='*80}")

    total_items = len(results)
    items_with_issues = sum(1 for r in results if r.status == "issues")
    error_items = sum(1 for r in results if r.status == "error")
    ok_items = sum(1 for r in results if r.status == "ok")

    print(f"\nSUMMARY:")
    print(f"  Total Items: {total_items}")
    print(f"  Items OK: {ok_items}")
    print(f"  Items with Issues: {items_with_issues}")
    print(f"  Items with Errors: {error_items}")

    for result in results:
        print(f"\n{'-'*60}")
        print(f"Item ID: {result.id}")
        print(f"Status: {result.status}")
        print(f"Model Used: {result.model_used}")
        print(f"Processing Time: {result.processing_time:.3f}s")

        if result.status == "error":
            print(f"Error: {result.error_message}")
        elif result.status == "issues" and result.issues:
            print("Issues Found:")
            for field, issue in result.issues.items():
                print(f"  {field}:")
                print(f"    Current: {issue.get('current_value', 'N/A')}")
                print(f"    Suggested: {issue.get('suggested', 'N/A')}")
                print(f"    Confidence: {issue.get('confidence', 'N/A')}")
                print(f"    Explanation: {issue.get('explanation', 'N/A')}")


if __name__ == "__main__":
    # Example usage and testing

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    print("BOM Classification Audit System")
    print("=" * 50)

    # Create sample DataFrame
    df = create_sample_dataframe()

    print("\nSample BOM DataFrame:")
    print(df.to_string(index=False))

    print(f"\nSystem Configuration:")
    print(f"  Primary Model: {ModelType.GEMINI_20_FLASH.value}")
    print(f"  Fallback Model: {ModelType.GEMINI_25_FLASH.value}")
    print(f"  Langchain GenAI Available: {LANGCHAIN_GENAI_AVAILABLE}")

    if not LANGCHAIN_GENAI_AVAILABLE:
        print("\n⚠️  Langchain Google GenAI package not installed.")
        print("   Install with: pip install langchain-google-genai")

    api_key_available = bool(os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY"))
    print(f"  API Key Available: {api_key_available}")

    if not api_key_available:
        print("\n⚠️  GOOGLE_API_KEY or GEMINI_API_KEY environment variable not set.")
        print("   Set your API key to enable actual model calls.")

    print("\n" + "=" * 50)
    print("SYSTEM READY FOR AUDIT")
    print("=" * 50)

    print("\nTo run the audit system:")
    print("1. Set GOOGLE_API_KEY environment variable (or GEMINI_API_KEY)")
    print("2. Run the following code:")
    print()
    print("import asyncio")
    print("from audit_main import audit_bom_dataframe, create_sample_dataframe, print_audit_results, apply_audit_corrections")
    print()
    print("async def main():")
    print("    df = create_sample_dataframe()")
    print("    results = await audit_bom_dataframe(df)")
    print("    print_audit_results(results)")
    print("    # To apply corrections:")
    print("    # corrected_df = apply_audit_corrections(audit_results_df, original_df)")
    print()
    print("asyncio.run(main())")

    print("\nExpected Issues to be Detected:")
    print("- item_1: ASTM A106 should be 'Steel, Carbon' not 'Steel, Stainless'")
    print("- item_2: Vendor code should be 'Miscellaneous' not 'Pipe'")
    print("- Various missing category-specific fields")

    # Demonstrate system prompt building
    print("\n" + "=" * 50)
    print("SYSTEM PROMPT PREVIEW")
    print("=" * 50)

    prompt_builder = SystemPromptBuilder()
    system_prompt = prompt_builder.build_system_prompt()

    # Show first 500 characters of the system prompt
    print("\nSystem Prompt (first 500 characters):")
    print("-" * 40)
    print(system_prompt[:500] + "...")
    print("-" * 40)

    print(f"\nTotal System Prompt Length: {len(system_prompt)} characters")
    print(f"Number of Classification Fields: {len(prompt_builder.categorization_fields)}")
    print(f"Number of Validation Rules: {len(prompt_builder.validation_rules)}")

    # Demonstrate request building
    print("\n" + "=" * 50)
    print("REQUEST BUILDING DEMO")
    print("=" * 50)

    request_builder = RequestBuilder()
    sample_row = df.iloc[0].to_dict()

    print(f"\nSample Row Data:")
    for key, value in sample_row.items():
        print(f"  {key}: {value}")

    request = request_builder.build_request(sample_row)

    print(f"\nBuilt Request Preview:")
    print(f"  ID: {request['id']}")
    print(f"  Material Description: {request['material_description']}")
    print(f"  Classification Fields: {len(request['current_classification'])}")
    print(f"  User Prompt Length: {len(request['user_prompt'])} characters")

    print("\n" + "=" * 50)
    print("READY TO AUDIT!")
    print("=" * 50)
