
import os
import datetime

from pubsub import pub
from collections import OrderedDict
import pandas as pd

from PySide6.QtWidgets import *
from PySide6.QtCore import *

from __features__ import MANUAL_EXTRACTION_RESULTS
from .blueprintreaderview import BlueprintReaderView
from .documentsview import DocumentsView
from .tableresultsview import TableResultsViewBase
from src.views.tables import (BomResultsView, GeneralDataView, OutlierDataView, RfqDataView,
                            SpecResultsView, SpoolResultsView)
from src.views.tables.genericdataview import GenericDataView
from src.views.dialogs.tableexporter import TableExporter
from src.app_paths import getTableExportPath
from src.utils.export import export_to_excel
from src.utils.logger import logger

from __features__ import EXPORT_WITH_FIELDMAP

# logger = logging.getLogger(__name__)

_PREFERED_TAB_ORDER = [
    "Document Viewer",
    "Documents",
    "<PERSON><PERSON>",
    "General",
    "SPOOL",
    "SPEC",
    "Outlier",
    "RFQ",
    "IFC",
    "Generic 1",
    "Generic 2",
]

_VIEWS = {
    "Document Viewer": BlueprintReaderView,
    "Documents": DocumentsView,
}

_TABLE_VIEWS = {
    "Bill of Materials": BomResultsView,
    "General Data": GeneralDataView,
    "Outlier Data": OutlierDataView,
    "Spec Data": SpecResultsView,
    "Spool Data": SpoolResultsView,
    "RFQ": RfqDataView,
    "IFC": GenericDataView,
    "Generic 1": GenericDataView,
    "Generic 2": GenericDataView,
}

data_sections = OrderedDict()

data_sections["BOM"] = {
    "payload_name": "bom_data",
    "title": "Bill of Materials",
}

data_sections["General"] = {
    "payload_name": "general_data",
    "title": "General Data",
}

data_sections["SPOOL"] = {
    "payload_name": "spool_data",
    "title": "Spool Data",
}

data_sections["SPEC"] = {
    "payload_name": "spec_data",
    "title": "Spec Data",
}

data_sections["Outlier"] = {
    "payload_name": "outlier_data",
    "title": "Outlier Data",
}

data_sections["IFC"] = {
    "payload_name": "ifc_data",
    "title": "IFC",
}
data_sections["generic_1"] = {
    "payload_name": "generic_1_data",
    "title": "Generic 1",
}
data_sections["generic_2"] = {
    "payload_name": "generic_2_data",
    "title": "Generic 2",
}


class ProjectViewTabs(QTabWidget):

    sgnCloseProject = Signal(object)
    sgnUpdateTableResults = Signal(object)
    sgnReplaceTableResults = Signal(object)
    selectTabByName = Signal(str)
    selectNextAvailableTable = Signal()
    sgnTabBarUpdated = Signal()
    sgnCreateNewTable = Signal(object, str)  # (table<TableResultsViewBase>: object, title: str)
    # sgnShowRoiExtractionPreview = Signal(dict)

    def __init__(self, parent):
        super().__init__(parent)
        self.views = {}
        self.projectData = {}  # A mapped record of the current active project
        self.dlgRoiExtract = None
        self.dlgTableExport = None
        self.setDocumentMode(True)
        self.setTabPosition(self.TabPosition.South)
        pub.subscribe(self.onOpenProject, "open-project")
        pub.subscribe(self.onCloseProject, "project-close")
        pub.subscribe(self.loadProjectData, "load-project-data")
        pub.subscribe(self.onRoiExtractionResponse, "roi-extraction-response")
        pub.subscribe(self.onProjectCreateTable, "project-create-table")
        pub.subscribe(self.onSwitchToTable, "project-switch-to-table")
        pub.subscribe(self.onSwitchToView, "project-switch-to-view")
        pub.subscribe(self.onSyncProjectData, "sync-project-data")
        self.sgnUpdateTableResults.connect(self.onUpdateTableResults)
        self.sgnReplaceTableResults.connect(self.onUpdateRemoveTableResults)
        self.sgnCreateNewTable.connect(self.onCreateNewTable)
        self.currentChanged.connect(self.onCurrentChanged)
        self.sgnCloseProject.connect(self.closeProject)
        self.selectTabByName.connect(self.onSelectTabByName)
        self.selectNextAvailableTable.connect(self.onSelectNextAvailableTable)
        # self.sgnShowRoiExtractionPreview.connect(self.showExtractionPreview)
        self.setDocumentMode(True)

        self.setLayout(QHBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

    @property
    def projectId(self):
        """Returns the projectId for currently opened project"""
        return self.projectData.get("projectId")

    def onOpenProject(self, data):
        """Clean up any existing project before opening"""
        pub.sendMessage("goto-workspace-view", name="ProjectView")
        logger.info(f"ProjectsView opening project: `{data['projectName']}`")
        logger.debug(f"Project data: {data}")
        if self.projectData.get("id") == data["id"]:
            logger.debug(f"Project already opened")
            return
        self.closeProject()
        self.initViews()
        self.projectData = data
        pub.sendMessage("request-project-table-data", data=self.projectData)

        self.syncProjectData()
        title = f"{self.projectData['projectName']} - id: {self.projectData['id']}"
        self.topLevelWidget().setWindowTitle(title)

    def onCloseProject(self, projectId):
        self.sgnCloseProject.emit(projectId)

    def closeProject(self, data=None):
        for name, view in self.views.items():
            try:
                view.cleanup()
            except Exception as e:
                logger.debug(f"{name}", exc_info=True)  # Not all views needs cleanup implemented
        self.clear()  # Remove all tabs
        try:
            self.dlgTableExport.close()
        except:
            pass
        self.dlgTableExport = None
        self.views = {}
        self.projectData = {}

    def initViews(self):
        """Initialize views"""
        for name, view in _VIEWS.items():
            view = view(None)
            self.views[name] = view
            self.addTab(view, name)

        self.views["Document Viewer"].extracted.connect(self.onExtraction)

    def onExtraction(self, data: dict):
        """Include project info with payload before sending off for processing"""
        pages = self.views["Document Viewer"].pdfViewer.pages
        if not pages:
            return False
        self.confirmExtraction(data)

    def confirmExtraction(self, data):
        projectId = self.projectData["id"]
        pdfPath = self.views["Document Viewer"].pdfViewer.currentPdfPath
        # pages = self.views["Document Viewer"].pdfViewer.pages
        pages = data["pageCount"]

        # Dev - ask if want to use manual data
        if not __file__.endswith(".pyc") and MANUAL_EXTRACTION_RESULTS:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setWindowTitle("Use manual data from OVERRIDE_EXTRACTION_RESULT_DF")
            msg_box.setText(f"Bypass results with manual data from OVERRIDE_EXTRACTION_RESULT_DF (y/n)?\n")
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.No)
            res = msg_box.exec()
            if res == QMessageBox.Yes:
                pub.sendMessage("request-roi-extraction-job-manual")
                self.views["Document Viewer"].roiSidebar.updateButtonState()
                return

        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setWindowTitle("Confirm Extraction")
        msg_box.setText(f"\n1 token per page analyzed\n\nThis extraction will require a maximum of {pages} tokens    \n\nContinue?\n")
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)

        res = msg_box.exec()

        if res == QMessageBox.Yes:
            from __features__ import ATEM_USE_NEW_EXTRACTION
            pub.sendMessage("request-roi-extraction-job",
                            projectId=projectId,
                            pdfPath=pdfPath,
                            data=data,
                            legacy=not bool(ATEM_USE_NEW_EXTRACTION))
            try:
                if self.dlgRoiExtract is not None:
                    self.dlgRoiExtract.proceeded = True
                    self.dlgRoiExtract.close()
            except Exception:
                logger.info("Cleanup ROI extraction preview failed", exc_info=True)

            # Try to switch to table
            for name in data_sections.keys():
                ok = self.onSwitchToTable(name)
                if ok:
                    break
        else:
            self.views["Document Viewer"].enableExtraction.emit(True)

    def onSwitchToTable(self, name):
        self.selectTabByName.emit(name)

    def onSwitchToView(self, name):
        self.selectTabByName.emit(name)

    def onSelectTabByName(self, name):
        try:
            self.setCurrentWidget(self.views[name])
            return True
        except:
            return False

    def onSelectNextAvailableTable(self):
        for key, params in data_sections.items():
            ok = self.onSelectTabByName(key)
            if ok:
                break

    def loadProjectData(self, finalDf: dict, switchTo: bool = True):
        """Dynamically add table results"""
        logger.info("Final json received, adding table results...")
        # Note - for thread safety let the signal handler this
        self.sgnUpdateTableResults.emit(finalDf)
        if switchTo:
            self.selectNextAvailableTable.emit()

    def onReplaceTableData(self, finalDf: dict, switchTo: bool = True):
        logger.info("Final json received, adding table results...")
        self.sgnReplaceTableResults.emit(finalDf)
        if switchTo:
            self.selectNextAvailableTable.emit()

    def onRoiExtractionResponse(self, res: dict, switchTo: bool = True):
        """Replace tables with results data"""
        tokensUsed = 0
        success = res.get("status") == "success"
        if success:
            self.onReplaceTableData(res["results"], True)
            tokensUsed = res["tokens_used"]

        self.views["Document Viewer"].enableExtraction.emit(True)

        msg = res.get("message", "")
        if msg:
            msg += " - "
        msg += f"Tokens used: {tokensUsed}"
        pub.sendMessage("show-app-messagebox",
                        msg=msg,
                        title=f"ATEM Extraction" + (" Complete" if success else " Failed"),
                        icon="info")

    def deleteTableView(self, name):
        tableView: TableResultsViewBase = self.views.get(name)
        if not tableView:
            return # Nothing to delete
        # Autosave so we can restore order after table refresh
        tableView.saveColumnOrder()
        tableView.cleanup()
        tableView.sgnUpdateDf.disconnect(tableView.setTableData)
        self.removeTab(self.indexOf(tableView))
        del self.views[name]
        if name not in self.views:
            return

    def setTableResult(self,
                       name,
                       title,
                       data,
                       switchTo: bool = False,
                       removeEmptyTable: bool = False,
                       displayName=None):
        displayName=None # override
        # Reopend dialog if open prior to replacement
        columnOrganizer = None
        try:
            columnOrganizer = self.views.get(name)._columnOrganizer
        except Exception as e:
            pass
        self.deleteTableView(name)
        if data is None or not isinstance(data, pd.DataFrame) or data.empty:
            return
        # Create a new table view
        try:
            tableView: TableResultsViewBase = None
            tableClass = _TABLE_VIEWS[title]
            if tableClass is GenericDataView:
                tableView = self.views[name] = tableClass(self, tableName=name)
            else:
                tableView = self.views[name] = tableClass(self)

            tableView.sgnSaveAll.connect(self.onSaveAllTableData)
            tableView.tableExportDialog.connect(self.showTableExportDialog)
            self.addTab(tableView, displayName if displayName else title)
            if name == "BOM":
                tableView.sgnUpdateRfq.connect(self.onProjectUpdateRfq)
            if True: #switchTo:
                self.setCurrentWidget(tableView)
        except Exception as e:
            logger.error(e)
            logger.error(f"TableView class for `{title}` not implemented? {e}", exc_info=True)
            return
        self.setTabText(self.indexOf(tableView), displayName if displayName else title)
        tableView.sgnUpdateDf.emit(data)
        if columnOrganizer:
            tableView._columnOrganizer = columnOrganizer
            tableView.showColumnOrganizer()

    def onUpdateTableResults(self, tableResults, switchTo: bool = False):
        # Check and create tabs for each section if they exist
        print("Results for: ", tableResults.keys())
        try:
            for name, params in data_sections.items():
                payload_name = params["payload_name"]
                title = params["title"]
                if payload_name not in tableResults.keys():
                    continue
                data = tableResults.get(payload_name, None)
                self.setTableResult(name,
                                    title,
                                    data,
                                    switchTo,
                                    removeEmptyTable=False)
        except Exception as e:
            logger.error(f"Failed to set table results: {e}", exc_info=True)

    def onUpdateRemoveTableResults(self, tableResults, switchTo: bool = False):
        """This will remove Tables tabs if no data exists"""
        # Check and create tabs for each section if they exist
        print("Results for: ", tableResults.keys())
        try:
            for name, params in data_sections.items():
                payload_name = params["payload_name"]
                title = params["title"]
                if payload_name not in tableResults.keys():
                    continue
                title = params["title"]
                data = tableResults.get(payload_name, None)
                self.setTableResult(name,
                                    title,
                                    data,
                                    switchTo,
                                    removeEmptyTable=True,
                                    displayName=f"{title}*")
                switchTo = False
        except Exception as e:
            logger.error(f"Failed to set table results: {e}", exc_info=True)

    def addTab(self, widget, title):
        super().addTab(widget, title)
        self.ensureTabOrder()

    def syncProjectData(self):
        try:
            self.views["Document Viewer"].sgnProjectDataSync.emit(self.projectData)  # Sync project data with child views
        except KeyError:
            pass

    def onCreateNewTable(self, table: TableResultsViewBase, title: str):
        self.views[title] = table
        self.addTab(table, title)

    def onProjectCreateTable(self, table: TableResultsViewBase, title: str):
        self.sgnCreateNewTable.emit(table, title)

    def onProjectUpdateRfq(self, data):
        """This will update table model or create a new if table not exists"""
        self.updateRfq(data)

    def createRfqTable(self) -> RfqDataView:
        table: RfqDataView = RfqDataView(self)
        table.sgnSaveAll.connect(self.onSaveAllTableData)
        table.sgnRequestTableData.connect(self.onTableDataRequested)
        table.updateOtherTableData.connect(self.onRfqMergeData)
        table.sgnRemoveTable.connect(self.onRfqRemoveTable)
        table.sgnUpdateMtoData.connect(self.onMtoData)
        table.tableExportDialog.connect(self.showTableExportDialog)
        return table

    def updateRfq(self, data, apply=False):
        table: RfqDataView = self.views.get("RFQ")
        # Destroy table and create new one
        if table:
            table.cleanup()
            table.hide()
            table.setParent(None)
            table.destroy()
        self.views["RFQ"] = self.createRfqTable()
        self.sgnCreateNewTable.emit(self.views["RFQ"], "RFQ")
        self.views["RFQ"].sgnUpdateDf.emit(data)
        self.selectTabByName.emit("RFQ")

    def onMtoData(self, data, mtoStatus: dict):
        table: TableResultsViewBase = self.views.get("RFQ")
        table.saveColumnOrder()  # So we have the same when refreshed
        # Destroy table and create new one
        if table:
            table.cleanup()
            table.hide()
            table.setParent(None)
            table.destroy()
        self.views["RFQ"] = self.createRfqTable()
        self.views["RFQ"].lastMtoStatus = mtoStatus
        self.sgnCreateNewTable.emit(self.views["RFQ"], "RFQ")
        self.views["RFQ"].setTableData(data, apply=True)
        self.selectTabByName.emit("RFQ")
        self.views["RFQ"]._mtoDone = True

    def onCurrentChanged(self, event):
        """Disable all tabs other than current one"""
        for n in range(self.count()):
            self.widget(n).setEnabled(event == n)

    def onTableDataRequested(self, requestingTable, tableNames: list):
        data = {}
        for name in tableNames:
            table: TableResultsViewBase = self.views.get(name)
            if table:
                data[name] = table.getTableData()
        self.views.get(requestingTable).sgnTableDataReceived.emit(data)

    def onRfqMergeData(self, data: dict):
        error_msg = ""
        if "general_skipped_pdf_ids" in data:
            _error_msg = f"Note - Could not map for pdf_ids: [{','.join(data['general_skipped_pdf_ids'])}].\nReason - Not found in General data."
            logger.error(_error_msg)
            del data['general_skipped_pdf_ids']

        tableKeys = {
            "bom_data": "BOM",
            "outlier_data": "Outlier",
            "general_data": "General",
            "spool_data": "SPOOL",
            "spec_data": "SPEC",
            "generic_1_data": "Generic 1",
            "generic_2_data": "Generic 2",
            "ifc_data": "IFC",
        }

        msg = ""
        tableNames = [tableKeys.get(c, c) for c in data.keys() if tableKeys.get(c) in self.views]
        if tableNames:
            msg = f'RFQ merged into {", ".join(tableNames)} Data'
        else:
            error_msg = "Failed to merge RFQ data. No receiving tables are open"
        if error_msg:
            msg += f'\n\n{error_msg}'
        self.sgnUpdateTableResults.emit(data)
        QMessageBox.information(self, 'RFQ Applied', msg)
        self.selectNextAvailableTable.emit()

    def onRfqRemoveTable(self):
        table = self.views.get("RFQ")
        # Destroy table and create new one
        if table:
            table.cleanup()
            table.hide()
            table.setParent(None)
            table.destroy()
        self.views["RFQ"] = None

    def onSyncProjectData(self, projectId: int, key: str, value: any):
        if key == "documents":
            assert type(value) == list
        if self.projectData.get("id") != projectId:
            return
        self.projectData[key] = value
        self.syncProjectData()

    def onDrawerStateRunning(self, running: bool):
        """Allow table repaints if drawer is not running"""
        t: TableResultsViewBase = None
        for t in self.getTableViews():
            t.enableModelDataUpdates(not running)

    def getTableViews(self) -> list:
        res = []
        for k, v in self.views.items():
            if isinstance(v, TableResultsViewBase):
                res.append(v)
        return res

    def showExtractionPreview(self, result):

        def cancelled():
            try:
                a = self.dlgRoiExtract.proceeded
                if not self.dlgRoiExtract.proceeded:
                    self.views["Document Viewer"].roiSidebar.updateButtonState()
            except Exception:
                logger.info("Could not update UI after cancelling preview", exc_info=True)

            self.dlgRoiExtract = None

        self.dlgRoiExtract.show()
        self.dlgRoiExtract.setResult(result)

    def onSaveAllTableData(self):
        logger.info("Saving all table data")
        print("saved")
        res = QMessageBox.question(self,
                                   "Save tables",
                                   f"Save all tables?                 ",
                                   buttons=QMessageBox.Yes | QMessageBox.Cancel)
        if res != QMessageBox.Yes:
            return

        view: TableResultsViewBase
        for view in self.getTableViews():
            view.saveToDatabase()

        pub.sendMessage("request-project-table-data", data=self.projectData)

    def showTableExportDialog(self, table):
        if not self.dlgTableExport:
            self.dlgTableExport = TableExporter(None, table, [])
            self.dlgTableExport.exportActivated.connect(self.onTableExport)
        self.dlgTableExport.show()
        self.dlgTableExport.raise_()

    def onTableExport(self, state: dict, useFieldMap=True):

        sheets = state["sheets"]
        fields = state["fields"]
        useSheetName = state.get("useSheetName", True)
        combinedExport = state.get("combinedExport", True)
        includeDateTime = state.get("includeDateTime", True)
        exportDir = state.get("exportDir", getTableExportPath())
        baseName = state["baseName"]

        def get_time_string():
            return datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        data = []
        sheetNames = []
        fieldMap = None
        for sheet in sheets:
            try:
                tableName, sheetName, save = sheet
                if not save:
                    continue
                table: TableResultsViewBase = self.views[tableName]
                df: pd.DataFrame = table.getTableData()
                columns = fields[tableName]
                exportedOrder = []
                for c in columns:
                    if c in df:
                        exportedOrder.append(c)
                exportedDf = df[exportedOrder]
                # Rename fields

                if EXPORT_WITH_FIELDMAP: # can be disabled in features
                    if fieldMap is None:
                        fieldMap = table.getSimpleFieldMap()
                    renames = {}
                    for c in exportedOrder:
                        renames[c] = fieldMap.get(c, {}).get("display", c)
                    exportedDf.rename(columns=renames, inplace=True)

                data.append((tableName, exportedDf))
                if useSheetName and sheetName:
                    sheetNames.append(sheetName)
                else:
                    sheetNames.append(str(table))
            except Exception as e:
                pass

        if not baseName:
            baseName = "Table-Export"

        if not exportDir:
            exportDir = getTableExportPath()

        def toQUrl(string) -> str:
            return QUrl.fromLocalFile(string).toString()

        msg = ""
        if combinedExport:
            ext = ".xlsx"
            dfs = []
            for tableName, df in data:
                dfs.append(df)
            dateTimeString = get_time_string() if includeDateTime else ""
            filename = f'{baseName}-{dateTimeString}{ext}'
            exportPath = os.path.join(exportDir, filename)
            try:
                res = export_to_excel(dfs, filename=exportPath, sheet_names=sheetNames)
                msg = f"Exported to directory: <a href='{toQUrl(exportDir)}'>{exportDir}</a>"
                msg += f"<br><br><a href='{toQUrl(exportPath)}'>Export: {filename}</a><br><br>"
            except Exception as e:
                msg = f"Failed to export table data: {e}"
        else:
            # Export each table individually
            failed = []
            ext = ".xlsx"
            dateTimeString = get_time_string() if includeDateTime else ""
            msg = f"Exported to directory: <a href='{toQUrl(exportDir)}'>{exportDir}</a><br><br>"
            for n, (tableName, df) in enumerate(data):
                filename = f'{baseName}-{sheetNames[n]}-{dateTimeString}{ext}'
                exportPath = os.path.join(exportDir, filename)
                try:
                    export_to_excel(df, filename=exportPath, sheet_names=[sheetNames[n]])
                    msg += f"<br><a href='{toQUrl(exportPath)}'>{tableName}: {filename}</a>"
                    msg += "<br>"
                except Exception as e:
                    failed.append((tableName, e))

            for tableName, exc in failed:
                msg += f"<br><br>Failed to export {tableName} table data: {exc}"

        msg += "<br>"
        msg += "<br>"
        msgBox = QMessageBox()
        msgBox.setTextFormat(Qt.TextFormat.RichText)
        msgBox.setWindowTitle("Table Export              ")  # Enlarges message box
        msgBox.setIcon(QMessageBox.Icon.Information)
        msgBox.setText(msg)
        msgBox.exec()

    def ensureTabOrder(self):
        """Ensures tab order"""
        n = 0
        for name in _PREFERED_TAB_ORDER:
            w = self.views.get(name)
            if w:
                index = self.indexOf(w)
                print(name, n)
                self.tabBar().moveTab(index, n)
                n += 1


class ProjectView(QWidget):

    tabChanged = Signal(object)
    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        self.tabs: ProjectViewTabs = ProjectViewTabs(self)
        self.tabs.currentChanged.connect(self.onTabChanged)
        self.layout().addWidget(self.tabs)
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.tabs.setDocumentMode(True)
        self.tabs.setAutoFillBackground(True)
        self.setAutoFillBackground(True)
        self.setObjectName("popup")

    def onTabChanged(self):
        self.tabChanged.emit(self.tabs)

    def getTableViews(self) -> list[TableResultsViewBase]:
        return self.tabs.getTableViews()

    def saveAllTables(self):
        return self.tabs.views

    def promptSaveTables(self):
        unsaved = []
        t: TableResultsViewBase = None
        for t in self.getTableViews():
            if t.isUnsaved():
                unsaved.append(t)

        if unsaved:
            res = QMessageBox.question(self,
                                   "Save tables before closing?",
                                   f"You have unsaved data for {unsaved}                 ",
                                   buttons=QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if res == QMessageBox.Yes:
                print("Saving")
                return QMessageBox.No
            return res

        # No unsaved tables return
        return QMessageBox.No
