import json

# TEMP FUNCTION TO CONVERT FIELDS TO FIELDMAP.JSON FORMAT

def convert_structure(items):
    new_structure = {}
    for item in items:
        field_key = item["field"]
        # Convert field value to Proper Text Form. Replace underscores with spaces and capitalize each word.
        proper_text_form = field_key.replace("_", " ").title()
        new_item = {
            "default": proper_text_form,
            "display": proper_text_form,
        }
        # Include options only if more than one option is present
        if "options" in item and len(item["options"]) > 1:
            new_item["options"] = item["options"]
        
        new_structure[field_key] = new_item
    
    return new_structure

# Load JSON data from a file
input_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\rfq_fields.json"  # Update this to your actual input file path
with open(input_file_path, 'r') as file:
    data = json.load(file)

# Convert the data
converted_data = convert_structure(data)

# Export the converted data to a new JSON file
output_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\converted_rfq_fields.json"  # Update this to your desired output file path
with open(output_file_path, 'w') as file:
    json.dump(converted_data, file, indent=4)

print(f"Conversion complete. Data exported to '{output_file_path}'.")

