import pandas as pd

from PySide6.QtGui import *
from PySide6.QtCore import *
from PySide6.QtWidgets import *


class BooleanDelegate(QItemDelegate):

    def __init__(self, *args, **kwargs):
        super(BooleanDelegate, self).__init__(*args, **kwargs)

    def paint(self, painter, option, index):
        value = index.data(Qt.CheckStateRole)
        rect: QRect = option.rect
        center = rect.center()
        rect.setHeight(int(rect.height() * 0.75))
        rect.setWidth(rect.width() // 5)
        rect.moveCenter(center)
        self.drawCheck(painter, option, rect, value)
        self.drawFocus(painter, option, rect)

    def editorEvent(self, event, model, option, index):
        if event.type() == QEvent.MouseButtonRelease:

            if event.button() == Qt.MouseButton.LeftButton:
                pos: QPoint = event.position().toPoint()
                # Adjust the hitbox for checkbox selection
                rect: QRect = QRect(option.rect)
                center = rect.center()
                rect.setHeight(int(rect.height() * 0.75))
                rect.setWidth(rect.width() // 5)
                rect.moveCenter(center)
                if rect.contains(pos):
                    value = model.data(index, Qt.CheckStateRole)
                    value = True if value is Qt.CheckState.Checked else False
                    model.setData(index, not value, Qt.ItemDataRole.EditRole)
                    event.accept()
                return True

        return super(BooleanDelegate, self).editorEvent(event, model, option, index)


class PandasModel(QAbstractTableModel):

    checkableColumnsChanged = Signal()
    def __init__(self, data: pd.DataFrame):
        super().__init__()
        self._data: pd.DataFrame = data
        self._original_data: pd.DataFrame = data.copy()  # Keep original unsorted data
        self.checkableColumns = set()
        self._sort_column = -1
        self._sort_order = Qt.SortOrder.AscendingOrder

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._data.columns)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        row, col = index.row(), index.column()

        if role == Qt.TextAlignmentRole:
            return Qt.AlignmentFlag.AlignCenter

        if role == Qt.UserRole:
            return self._data.iloc[row].to_dict()

        if col in self.checkableColumns:
            # For checkable columns, only return data for CheckStateRole and TextAlignmentRole
            if role == Qt.CheckStateRole:
                return Qt.Checked if self._data.iat[row, col] else Qt.Unchecked
            elif role == Qt.TextAlignmentRole:
                return Qt.AlignmentFlag.AlignCenter
            # Return None for all other roles, including DisplayRole
            # elif role == Qt.DisplayRole:
            #     return "checked"
            return None
        else:
            # For non-checkable columns, handle as before
            if role == Qt.DisplayRole:
                value = self._data.iloc[row, col]
                return str(value)

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                try:
                    return str(self._data.columns[section])
                except:
                    return str(section)
            else:
                return str(section + 1)
        return None

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        if index.column() in self.checkableColumns:
            return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsUserCheckable

        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid():
            return False
        row, col = index.row(), index.column()
        if col in self.checkableColumns:
            self._data.iat[row, col] = bool(value)
            self.dataChanged.emit(index, index, [Qt.CheckStateRole])
            return True
        else:
            self._data.iat[row, col] = value

        return False

    def setCheckableColumns(self, columns):
        """Set which columns should have checkboxes"""
        self.checkableColumns = set(columns)
        self.layoutChanged.emit()
        self.checkableColumnsChanged.emit()

    def removeRow(self, row, parent=QModelIndex()):
        return self.removeRows(row, 1, parent)

    def removeRows(self, row: int, count: int, parent=QModelIndex()) -> bool:
        if row < 0 or row + count > len(self._data):
            return False

        self.beginRemoveRows(parent, row, row + count - 1)
        self._data.drop(self._data.index[row:row + count], inplace=True)
        self._data.reset_index(drop=True, inplace=True)
        self.endRemoveRows()
        return True

    def removeRowsByQuery(self, query: str) -> bool:
        """Remove rows that match the given pandas query expression.

        Args:
            query: A string query expression that can be passed to pandas.DataFrame.query()
                  For example: "column1 > 5 and column2 == 'value'"

        Returns:
            bool: True if any rows were removed, False otherwise
        """
        try:
            # Get indices of rows to remove
            rows_to_remove = self._data.query(query).index.tolist()
            if not rows_to_remove:
                return False

            # Group consecutive indices to minimize number of removeRows calls
            groups = []
            current_group = [rows_to_remove[0]]

            for idx in rows_to_remove[1:]:
                if idx == current_group[-1] + 1:
                    current_group.append(idx)
                else:
                    groups.append(current_group)
                    current_group = [idx]
            groups.append(current_group)

            # Remove groups of rows from last to first to maintain correct indices
            for group in reversed(groups):
                start_idx = group[0]
                count = len(group)
                self.removeRows(start_idx, count, QModelIndex())

            return True

        except Exception as e:
            print(f"Error removing rows by query: {e}")
            return False

    def insertRow(self, row: int, data: dict, parent=QModelIndex()) -> bool:
        """Insert a single row into the model at the specified position.

        Args:
            row: Row index where to insert the new row
            data: Dictionary containing the data for the new row
            parent: Parent model index
        """
        return self.insertRows(row, 1, parent, pd.DataFrame([data]))

    def insertRows(self, row: int, count: int, parent=QModelIndex(), data: pd.DataFrame = None) -> bool:
        """Insert multiple rows into the model at the specified position.

        Args:
            row: Starting row index where to insert the new rows
            count: Number of rows to insert
            parent: Parent model index
            data: DataFrame containing the data for the new rows
        """
        if data is None or len(data) != count:
            return False

        self.beginInsertRows(parent, row, row + count - 1)

        # If inserting at the end
        if row >= len(self._data):
            self._data = pd.concat([self._data, data], ignore_index=True)
        else:
            # Split the dataframe and insert the new rows
            upper = self._data.iloc[:row]
            lower = self._data.iloc[row:]
            self._data = pd.concat([upper, data, lower], ignore_index=True)

        self.endInsertRows()
        return True

    def appendRow(self, data: dict) -> bool:
        """Append a single row to the end of the model.

        Args:
            data: Dictionary containing the data for the new row
        """
        return self.insertRow(len(self._data), data)

    def appendRows(self, data: pd.DataFrame) -> bool:
        """Append multiple rows to the end of the model.

        Args:
            data: DataFrame containing the data for the new rows
        """
        return self.insertRows(len(self._data), len(data), data=data)

    def sort(self, column: int, order: Qt.SortOrder = Qt.SortOrder.AscendingOrder) -> None:
        """Sort table by specified column.

        Args:
            column: Column index to sort by
            order: Sort order (Qt.AscendingOrder or Qt.DescendingOrder)
        """
        self.layoutAboutToBeChanged.emit()

        try:
            column_name = self._data.columns[column]
            ascending = order == Qt.SortOrder.AscendingOrder

            # Sort the dataframe
            self._data = self._data.sort_values(
                by=column_name,
                ascending=ascending,
                na_position='last'
            )

            self._sort_column = column
            self._sort_order = order

        except Exception as e:
            pass
            # logger.debug(f"Error sorting column {column}: {e}")

        self.layoutChanged.emit()

    def resetSort(self) -> None:
        """Reset the table to its original unsorted state."""
        self.layoutAboutToBeChanged.emit()
        self._data = self._original_data.copy()
        self._sort_column = -1
        self._sort_order = Qt.SortOrder.AscendingOrder
        self.layoutChanged.emit()


class PandasTable(QTableView):

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setModel(PandasModel(pd.DataFrame()))
        self.model().checkableColumnsChanged.connect(self.onCheckableColumnChanged)
        self.booleanDelegate = BooleanDelegate()

        # Enable sorting
        self.setSortingEnabled(True)
        self.horizontalHeader().setSectionsClickable(True)

        # Default sort indicator
        self.horizontalHeader().setSortIndicatorShown(True)

    def model(self) -> PandasModel:
        return super().model()

    def dataframe(self) -> pd.DataFrame:
        return self.model()._data

    def setDataFrame(self, df: pd.DataFrame):
        self.model().beginResetModel()
        self.model()._data = df
        self.model()._original_data = df.copy()  # Store original unsorted data
        self.model()._sort_column = -1  # Reset sort state
        self.model()._sort_order = Qt.SortOrder.AscendingOrder
        self.model().endResetModel()
        self.verticalHeader().hide()

    def onCheckableColumnChanged(self):
        for c in self.model().checkableColumns:
            self.setItemDelegateForColumn(c, self.booleanDelegate)
