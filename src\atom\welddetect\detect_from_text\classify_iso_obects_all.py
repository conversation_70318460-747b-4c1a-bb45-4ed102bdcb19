"""
    Isometric Component Classification with General Items Detection

    This module provides functionality to process PDF drawings and classify isometric components
    including pipes, measurements, pointers, and general items like gussets.

    Key features:
    - Process only specific pages from large PDFs
    - Detect and classify isometric components
    - Integrate with detected general items (like gussets)
    - Generate color-coded output PDF
    - Export results to Excel

    The workflow is as follows:
    1. Load the PDF and process only specified pages
    2. Extract vector drawings from each page
    3. Classify components based on geometry and properties
    4. Integrate with detected general items
    5. Generate output visualization
    6. Export results to Excel
            
            Colors:
                Green: (0, 1, 0)
                Red: (1, 0, 0)
                Blue: (0, 0, 1)
                Yellow: (1, 1, 0)
                Cyan: (0, 1, 1)
                Magenta: (1, 0, 1)
                Orange: (1, 0.5, 0)
                Purple: (0.5, 0, 0.5)
                Pink: (1, 0.75, 0.8)
                Lime: (0.5, 1, 0)
                Teal: (0, 0.5, 0.5)
                Brown: (0.6, 0.3, 0)
                Navy: (0, 0, 0.5)
                Olive: (0.5, 0.5, 0)
                Maroon: (0.5, 0, 0)
                
    
        Workflow logic:
        Outstanding:
        - Number all classified items internally for linking

        - Need user inputted configureation (Thresholds, andgle_tolerances, etc) for fine tuning specific jobs. Should show update in real time so user can 
        ensure isometric items are detected prior to running extraction
        
        - Get continuation piping lines not detected by the initital isometric detection

        - Draw anything in the isometric gaps/path and label as "Pipe Component". 

        - Detect Squares (Circles for some drawings)

        - Get arrows and vertices

        - Need pointer origin and destination stored in data for grouping

        - Draw "Other" with numbers for debugging

        - Use BOM item #'s for hints

        - Identify measurements, size callouts, and reduced size callouts (1"x1 1/2"). Can be done with regex. Dual sizes used to create iso size breaks

        - Identify measurement breaks (Perpendicular Lines signifying a specific measurment of an item, like a valve)

        - Identify insulation dashes

        - Identify revision triangles and clouds. Need to research this.

        - Identify Weep holes (Regex pattern or key word pickout. Research alternate terms to look for)

        - Handle case where the break in isometric is because the line is running behind another pipe segment and is not really a break (Page 4 of "Weld Recognition Test.pdf)


        * Purpose: Isolate piping components like pipe line segments, measurement lines, pointers, etc., to assist in relating items to their actual location on the isometric drawing.
        * Input: PDF file containing isometric piping drawings.
        * Output: 
        1. A new PDF file with color-coded line segments.
        2. An Excel file with detailed information about each line segment.

        Steps:
        1. Open the PDF and extract vector graphics (drawings) from each page. (Function: process_drawings)
        2. For each line in the drawings: (Function: process_drawings)
        a. Calculate its angle and length. (Functions: calculate_angle, LineString.length)
        b. Determine if it's an isometric angle. (Function: is_isometric_angle)
        c. Store its properties (coordinates, width, etc.).
        3. Determine the two most significant line widths on each page. (Function: determine_significant_widths)
        - Purpose: Identify potential pipe and measurement line widths.
        4. Classify lines based on width and isometric property: (Function: process_drawings)
        a. Thicker isometric lines are classified as 'Pipe'.
        b. Other lines are initially classified as 'Other'.
        5. Reclassify 'Other' lines: (Function: process_drawings)
        a. Check if they're parallel to pipes. (Function: is_parallel_to_pipe)
            If yes, classify as 'Measurement'.
        b. Check if they're pointing to pipes. (Function: is_pointer_line)
            If yes, classify as 'Pointer'.
        6. Generate output PDF with color-coded lines and Excel file with line data. (Functions: output_isometric_lines_to_pdf, DataFrame.to_excel)

        Key Functions:
        - calculate_angle: Computes the angle of a line.
        - is_isometric_angle: Checks if an angle is close to standard isometric angles.
        - determine_significant_widths: Finds the two most common line widths for pipes and measurements.
        - is_parallel_to_pipe: Checks if a line runs parallel to a pipe.
        - is_pointer_line: Identifies lines pointing to pipes (likely annotations or labels).
        - process_drawings: Main function that orchestrates the entire workflow.
        - output_isometric_lines_to_pdf: Generates the color-coded PDF output.
        - main: Calls process_drawings and handles the overall execution flow.

        Adjustable Parameters:
        - angle_tolerance (in is_isometric_angle): Tolerance for considering an angle as isometric. 
        Increasing it will classify more lines as isometric, potentially including non-standard angles.
        - distance_threshold (in is_pointer_line): Maximum distance for a line to be considered close to a pipe. 
        Increasing it will detect pointers that are further from pipes, but may increase false positives.
        - min_length (in process_drawings): Minimum length for a line to be considered. 
        Decreasing it will include shorter lines, potentially increasing noise in the detection.
        - tolerance (in determine_significant_widths): Tolerance for grouping similar line widths. 
        Increasing it will group more varied line widths together, potentially misclassifying some lines.

        Color Coding in Output PDF:
        - Red: Pipe lines
        - Brown: Measurement lines
        - Blue: Pointer lines
        - Orange: Other unclassified lines

        Note: Adjusting these parameters may require fine-tuning based on the specific characteristics of the input drawings.
"""

import random
import math
import multiprocessing
import fitz, os, math, re, time
from fitz.utils import getColor
import pandas as pd
import numpy as np
from shapely.geometry import Point, LineString, Polygon
from src.utils.logger import logger
from collections import defaultdict, deque
from scipy.spatial import distance, cKDTree

from src.utils.export import export_to_excel
from src.atom.fast_storage import load_df_fast

USE_MULTIPROCESS = True

# Global configuration flags - CHANGE THESE DIRECTLY FOR IDE DEBUGGING

# Set to True to enable detailed debug output
PRINT_DETAILED_OUTPUT = False

# Set to True to skip Excel file generation (avoids errors there)
SKIP_EXCEL_EXPORT = False

# Set to process only one specific page (for targeted debugging)
# Set to None to use the full page list below
DEBUG_SINGLE_PAGE = 326  # Set to a specific page number, or None

# Default pages to process
PAGES_TO_PROCESS = [326, 519, 535, 586, 594]

# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)


def is_arrow_tip(vector_group):
    items = vector_group
    logger.debug(f"Checking vector group with {len(items)} items")
    
    if len(items) < 2 or any(item['item_type'] != 'l' for item in items):
        logger.debug("Not an arrow tip: doesn't have at least 2 line items")
        return False

    lines = [item['geometry'] for item in items]
    
    # Collect all endpoints
    endpoints = set()
    for line in lines:
        endpoints.update(line.coords)
    
    # Check if lines form a connected structure
    connected = False
    for i, line1 in enumerate(lines):
        for line2 in lines[i+1:]:
            if set(line1.coords) & set(line2.coords):
                connected = True
                break
        if connected:
            break
    
    if not connected:
        logger.debug("Not an arrow tip: lines are not connected")
        return False
    
    # Check if the structure forms a point (one end should have multiple lines connecting)
    endpoint_counts = defaultdict(int)
    for endpoint in endpoints:
        endpoint_counts[endpoint] = sum(endpoint in line.coords for line in lines)
    
    max_connections = max(endpoint_counts.values())
    
    logger.debug(f"Max connections at an endpoint: {max_connections}")
    
    # We consider it an arrow tip if at least one point connects multiple lines
    return max_connections >= 2


def detect_arrow_tips(page_items: pd.DataFrame):
    arrow_tips = []
    measurement_pointer_lines = [item for _, item in page_items.iterrows() if item['det_cat_1'] in ['Measurement', 'Pointer']]
    logger.debug(f"Found {len(measurement_pointer_lines)} Measurement/Pointer lines")
    
    # Some arrows have a bottom closing line, so just work with first two
    potential_tips = [m for m in measurement_pointer_lines if len(m['drawing']['items']) in [2, 3]]
    potential_shafts = [m for m in measurement_pointer_lines if len(m['drawing']['items']) <= 2]

    def rounded(point, precision=2):
        return (round(point[0], precision), round(point[1], precision))

    maximum_arrow_tip_length = 40

    checked = set()
    # Filter out tips
    tmp = []
    for m in potential_tips:
        if m['vector_id'] in checked:
            continue
        checked.add(m['vector_id'])
        drawing = m['drawing']
        # line_items = [n for n in m['drawing']['items'] if n[0]
        line1 = drawing['items'][0]
        line2 = drawing['items'][1]

        try:
            line1_ls = LineString([line1[1], line1[2]])
            line2_ls = LineString([line2[1], line2[2]])
        except Exception as e:
            print(f"Invalid potential_tips - {m}")
            continue

        unique_coords = set()
        target_point = None
        for pt in (line1[1], line1[2], line2[1], line2[2]):
            r = rounded(pt)
            if r in unique_coords:
                target_point = pt
            unique_coords.add(r)

        # Only expect 3 unique coords from an arrow tip
        if len(unique_coords) != 3:
            continue

        # Arrow head lines too long?
        if line1_ls.length > maximum_arrow_tip_length:
            continue
        # Should be similar length
        if abs(line1_ls.length - line2_ls.length) > 2:
            continue
        
        m['target_point'] = target_point
        tmp.append(m)

    potential_tips = tmp
    tmp = []

    arrow_tips = [] # single ended
    for m in potential_tips:
        print()
        m_vector_id = m['vector_id']
        m_line_length = m['geometry'].length # arrow tip length
        target_point = m['target_point']
        for s in potential_shafts:
            source_vector_id = s['vector_id']
            if source_vector_id == 40199 and m_vector_id == 40200:
                pass
            
            items = s['drawing']['items']
            if len(items) == 1:
                line = items[0]
                line_ls = LineString([line[1], line[2]])
            else:
                # TODO
                # Arrow lines might not be straight so are multiline
                # Allow 2 line shafts for now
                if not (items[0][0] == 'l' and items[1][0] == 'l'):
                    continue
                line1 = items[0]
                line2 = items[1]
                line1_end = line1[2]
                line2_start = line2[1]
                if line1_end != line2_start:
                    continue
                line = ('l', line1[1], line2[2])
                line_ls = LineString([line[1], line[2]])

            # Require that the shaft is longer the arrow tip length
            if abs(m_line_length - line_ls.length) < 2:
                continue 
            pt1 = rounded(line[1])
            pt2 = rounded(line[2])
            pt3 = rounded(target_point)
            if pt1 != pt3 and pt2 != pt3:
                continue
            if pt1 != pt3:
                source_point = line[1]
            else:
                source_point = line[2]

            m['det_cat_2'] = m['det_cat_1']
            m['det_cat_1'] = 'Arrow Tip'
            m['pointer_start'] = source_point
            m['target_point'] = target_point

            # print(m_vector_id, 'found', m['drawing']["items"], source_vector_id, source_point)
            arrow_tips.append(m)

    logger.debug(f"Total arrow tips found: {len(arrow_tips)}")
    return arrow_tips

def distance_to(p1, p2):
    # Gives the absolute distance between two points
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])

def get_centroid(vertexes):
    x_list = [vertex[0] for vertex in vertexes]
    y_list = [vertex[1] for vertex in vertexes]
    x = sum(x_list) / len(vertexes)
    y = sum(y_list) / len(vertexes)
    return (x, y)

def get_rect(points) -> fitz.Rect:
    assert len(points) == 4
    x_list = [points[0] for points in points]
    y_list = [points[1] for points in points]
    return fitz.Rect(min(x_list), min(y_list), max(x_list), max(y_list))

def find_nearest_pipe(point, pipelines_df: pd.DataFrame, near_threshold):
    """If point is within near_threshold of a pipe, set that as the nearest pipe"""
    min_dist = None
    nearest_pipe = None
    for _, pipeline in pipelines_df.iterrows():
        # Check if point is close to coords along the pipe
        pipe_coords = pipeline['pipe_coords']
        for pipe_point in get_pipeline_coords(pipe_coords):
            dist = distance_to(pipe_point, point)
            if dist > near_threshold:
                continue
            if min_dist is None or dist < min_dist:
                nearest_pipe = pipeline
                if min_dist == 0:
                    return nearest_pipe

    return nearest_pipe


class RelationshipManager:
    def __init__(self):
        self.items = {}  # Store all items
        self.relationships = {}  # Store relationships between items

    def add_item(self, item_id, item_data):
        self.items[item_id] = item_data
        if item_id not in self.relationships:
            self.relationships[item_id] = set()

    def add_relationship(self, item1_id, item2_id):
        # Ensure both items exist in the relationships dictionary
        for item_id in [item1_id, item2_id]:
            if item_id not in self.relationships:
                #print(f"Warning: Item {item_id} not found. Adding it to relationships.")
                self.relationships[item_id] = set()
        
        self.relationships[item1_id].add(item2_id)
        self.relationships[item2_id].add(item1_id)

    def get_item(self, item_id):
        return self.items.get(item_id)

    def get_related_items(self, item_id):
        related_ids = self.relationships.get(item_id, set())
        return {related_id: self.items.get(related_id) for related_id in related_ids if related_id in self.items}

    def get_related_items_by_type(self, item_id, item_type):
        related_items = self.get_related_items(item_id)
        return {id: item for id, item in related_items.items() if item and item.get('det_cat_1') == item_type}


def get_finish_params(path={}, color=None, width=None):
    if color is None:
        c = path.get("color")
    elif isinstance(color, tuple):
        c = color  # If color is already a tuple, use it directly
    else:
        c = getColor(color)  # This will handle string color names
    
    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),
        "fill": path.get("fill", None),
        "color": c,
        "dashes": path["dashes"] if path.get("dashes") is not None else None,
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path["lineJoin"] if path.get("lineJoin") is not None else 0,
        "lineCap": max(path["lineCap"]) if path.get("lineCap") and path["lineCap"] else 0,
        "width": path["width"] if path.get("width") is not None else width
    }

def draw_path(shape, path):
    for item in path["items"]:
        if item[0] == "l":  # line
            shape.draw_line(item[1], item[2])
        elif item[0] == "re":  # rectangle
            shape.draw_rect(item[1])
        elif item[0] == "qu":  # quad
            shape.draw_quad(item[1])
        elif item[0] == "c":  # curve
            shape.draw_bezier(item[1], item[2], item[3], item[4])
        else:
            print(f"Unhandled path type: {item[0]}")

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.
        
    Note: same function as calculate_angle but supply two co-ords
    instead of line
    """
    x1, y1 = p1
    x2, y2 = p2
    dx = x2 - x1
    dy = y2 - y1
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def get_pipe_angles(pipelines) -> dict:
    """Return the start and end angle of each pipe"""
    # Starting angle is the two first coords of pipe
    # Ending angle is the last two coords of pipe
    pipe_angles = {}
    for n, (pipe_start, pipeline) in enumerate(pipelines.items()):
        pipe_coords = [pipe_start] + pipeline
        pipe_end = pipe_coords[-1]
        pipe_start_angle = int(angle_between_points(pipe_start, pipe_coords[1]))
        pipe_end_angle = int(angle_between_points(pipe_coords[-2], pipe_end))
        pipe_angles[n] = {
            "start_angle": pipe_start_angle, 
            "end_angle": pipe_end_angle
        }
    return pipe_angles

def get_pipe_connections(pipelines: dict, 
                         pipe_angles: dict, 
                         connected_angle_tolerance=2, 
                         connected_distance_threshold=40):
    pipe_connections = {}
    for n1, (pipe_start1, pipeline1) in enumerate(pipelines.items()):
        pipe_connections.setdefault(n1, {})
        pipe_start1_angle = pipe_angles[n1]["start_angle"]
        for n2, (pipe_start2, pipeline2) in enumerate(pipelines.items()):
            if n1 == n2:
                continue

            def is_connected(pt1, pt1_angle, pt2, pt2_angle, min_dist) -> bool:
                dist = distance_to(pt1, pt2)
                if n1 == 0 and n2 in [1, 2]:
                    pass
                if min_dist is not None and dist > min_dist:
                    return False
                if dist > connected_distance_threshold:
                    return False
                # Check if pipe starting point is connected to another pipe starting point
                # If same angle and within distance, we could consider them connected end-to-end
                angle_delta = abs(pt1_angle - pt2_angle)
                if angle_delta < connected_angle_tolerance or dist < 1:
                    return True
                return False

            pipe2_coords = [pipe_start2] + pipeline2
            pipe_end2 = pipe2_coords[-1]
            pipe_start2_angle = pipe_angles[n2]["start_angle"]
            pipe_end2_angle = pipe_angles[n2]["end_angle"]

            # Workaround - Is this sufficient. Ignore rougly 120 angles 
            # between pipes which may be junctions
            if abs(abs(pipe_start2_angle - pipe_start1_angle) - 120) < 2:
                continue
            elif abs(abs(pipe_start2_angle - pipe_start1_angle) - 60) < 2:
                continue

            # Check if this pipe is connected to other pipe, checking both ends
            if is_connected(pipe_start1, pipe_start1_angle, pipe_start2, pipe_start2_angle, pipe_connections[n1].get('before_dist')):
                pipe_connections[n1]["before_dist"] = distance_to(pipe_start1, pipe_start2)
                pipe_connections[n1]["before_pipe"] = f"{n2}_start"
                pipe_connections[n1]["before"] = n2

            if is_connected(pipe_start1, pipe_start1_angle, pipe_end2, pipe_end2_angle, pipe_connections[n1].get('before_dist')):
                pipe_connections[n1]["before_dist"] = distance_to(pipe_start1, pipe_end2)
                pipe_connections[n1]["before_pipe"] = f"{n2}_end"
                pipe_connections[n1]["before"] = n2
    
    # Update the `after pipes` now that we have detected `before pipe` connections
    for pipe_no, connection in pipe_connections.items():
        before = connection.get('before')
        if before is None:
            continue
        pipe_connections[before]['after'] = pipe_no
        pipe_connections[before]['after_pipe'] = f'{pipe_no}_start'
        pipe_connections[before]['after_dist'] = connection['before_dist']

    return pipe_connections

def merge_pipelines(pipelines, pipe_angles, pipe_connections):
    """
    Gaps on the pipeline can segment into smaller pipes. We can merge pipes 
    by detecting pipe chains and looking the `after` pipe of each pipe

    A pipe chain is a continuation of pipe segments.

    For example, consider two pipes: pipe A -> B, and pipe C -> D.
    There is a gap between B and C, hence why they were detected as separate.
    Merge them by creating a new pipe chain (A -> B -> C -> D).

    Do not consider junction pipes to be part of the same continuation 
    of the pipe they are connected to

    The detected pipe chains will become the result of the new merged pipelines
    """
    pipelines_merged = {}
    pipe_chain = []
    unused = set(pipe_connections.keys())
    for pipe_no, connection in pipe_connections.items():
        pipe_start = list(pipelines.keys())[pipe_no]
        pipe_angle_end = pipe_angles[pipe_no]['end_angle']
        pipe_coords = [p for p in pipelines[pipe_start]]
        after_pipe_no = connection.get("after")
        after_dist = connection.get("after_dist")
        after_pipe_angle_start = pipe_angles[pipe_no]['start_angle']
        if after_dist is None:
            continue
        angle_delta = abs(pipe_angle_end - after_pipe_angle_start)
        # Consider a more dynamic after_dist value
        if after_dist < 0.3 and angle_delta < 2:
            unused.discard(pipe_no)
            unused.discard(after_pipe_no)
            for chain in pipe_chain:
                if chain[0] == after_pipe_no:
                    ends = [e[-1] for e in pipe_chain]
                    print("Pipe chain:", pipe_chain)
                    if pipe_no in ends:
                        index = ends.index(pipe_no)
                        left_chain = pipe_chain[index]
                        left_chain.reverse()
                        chain.extendleft(left_chain)
                        del pipe_chain[index]
                    else:
                        chain.insert(0, pipe_no)
                    break
                elif chain[-1] == pipe_no:
                    starts = [s[0] for s in pipe_chain]
                    if after_pipe_no in starts:
                        index = starts.index(after_pipe_no)
                        right_chain = pipe_chain[index]
                        chain.extend(right_chain)
                        del pipe_chain[index]
                    else:
                        chain.append(after_pipe_no)
                    break
            else:
                pipe_chain.append(deque([pipe_no, after_pipe_no]))

    for u in unused:
        pipe_chain.append(deque([u]))

    # Replace pipeline results with pipe merging results
    for chain in pipe_chain:
        start = chain.popleft()
        pipe_start = list(pipelines.keys())[start]
        pipe_coords = pipelines[pipe_start]

        before = start           
        for node in chain:
            pipe_start2 = list(pipelines.keys())[node]
            side = pipe_connections[before]['after_pipe'].split('_')[1]
            pipe_coords2 = pipelines[pipe_start2]
            if side == "end":
                pipe_coords2.reverse()

            pipe_coords.extend(pipe_coords2)
            before = node

        pipelines_merged[pipe_start] = pipe_coords

    return pipelines_merged

def group_pipes(page_items: pd.DataFrame, page_num: int) -> list:
    """Iterate through Pipe classified items (i.e. line items) and join them 
    based on the start and end point of each item, with consideration
    of the line angle
    """
    pipelines = {}

    # Initial coordinate grouping of pipes
    for _, item in page_items[page_items['det_cat_1'] == 'Pipe'].iterrows():
        geometry = item['geometry']
        start = (geometry.coords[0][0], geometry.coords[0][1])
        end = (geometry.coords[-1][0], geometry.coords[-1][1])
        for pipe_start, pipe_points in pipelines.items():
            if start == pipe_points[-1]:
                pipe_points.append(end)
                break
        else:
            if start not in pipelines:
                pipelines[start] = [end]
            else:
                # Some lines may start at same point but we need to separate
                # the pipes. Look at the angle and offset the start point to
                # create unique pipe
                dx = start[0] - end[0]
                dy = start[1] - end[1]
                if dy >= dx: # Offset pipe vertically or horizontally?
                    if dy <= 0:
                        start = (start[0], start[1] + 0.05) # offset south
                    else:
                        start = (start[0], start[1] - 0.05) # offset north
                else:
                    if dx <= 0:
                        start = (start[0] + 0.05, start[1]) # offset east
                    else:
                        start = (start[0] - 0.05, start[1]) # offset west

                pipelines[start] = [end]

    # Pipe whose `end` connects to another pipe `end` can be merged
    to_delete = []
    for n1, (pipe1_start, pipe1) in enumerate(pipelines.items()):
        if n1 in to_delete:
            continue
        pipe1_coords = [pipe1_start] + pipe1
        pipe1_end = pipe1_coords[-1]
        for n2, (other_pipe_start, pipe2) in enumerate(pipelines.items()):
            if n1 == n2:
                continue
            other_pipe_coords = [other_pipe_start] + pipe2
            other_pipe_end = other_pipe_coords[-1]
            if distance_to(pipe1_end, other_pipe_end) < 1:
                # Merge the other pipe into querying pipe i.e. pipe1
                other_pipe_coords.pop()
                other_pipe_coords.reverse()
                pipe1.extend(other_pipe_coords)
                to_delete.append(other_pipe_start)

    # Remove pipes which have been merged into another pipe
    for key in to_delete:
        try:
            del pipelines[key]
        except KeyError:
            pass

    pipe_angles = get_pipe_angles(pipelines)

    # Based on angles and distance, detect connection of the pipes
    # Format e.g. {n: { before: n1, after: n2 }}
    pipe_connections = get_pipe_connections(pipelines, pipe_angles)

    # Second pass at merging pipes. 
    # This merge should greatly reduce number of pipes
    perform_merge = True # Toggle this to see the difference
    if perform_merge:
        pipelines = merge_pipelines(pipelines, pipe_angles, pipe_connections)
        pipe_angles = get_pipe_angles(pipelines)
        pipe_connections = get_pipe_connections(pipelines, pipe_angles)
    
    # Possible junction pipe or incorrectly disconnected pipes
    # have no before or after
    for n1, (pipe1_start, pipe1) in enumerate(pipelines.items()):
        pipe1_end = pipe1[-1]
        before = pipe_connections[n1].get("before")
        after = pipe_connections[n1].get("after")
        if before is not None or after is not None:
            continue
        print(n1, "junction pipe")
        for n2, (other_pipe_start, pipe2) in enumerate(pipelines.items()):
            if n1 == n2:
                continue
            pipe2_coords = [other_pipe_start] + pipe2
            for point in get_pipeline_coords(pipe2_coords):
                if distance_to(pipe1_start, point) < 2:
                    pipe_connections[n1]["before"] = n2
                    pipe_connections[n1]["before_pipe"] = f"{n2}"
                    pipe_connections[n1]["potential_junction"] = point
                    break

    print()
    print("Printing Pipe connection results")
    print()

    result = []
    print(pipe_connections)
    for pipe_no, (pipe_start, pipeline) in enumerate(pipelines.items()):
        pipe_coords = [pipe_start] + pipeline
        data = {
            "page": page_num,
            "pipe_no": int(pipe_no),
            "pipe_coords": pipe_coords,
            "pipe_start": pipe_start,
            "pipe_end": pipe_coords[-1],
            "start_angle": pipe_angles[pipe_no]["start_angle"],
            "end_angle": pipe_angles[pipe_no]["end_angle"],
            "before_pipe": pipe_connections[pipe_no].get("before_pipe"),
            "after_pipe": pipe_connections[pipe_no].get("after_pipe"),
            "before_pipe_no": pipe_connections[pipe_no].get("before"),
            "after_pipe_no": pipe_connections[pipe_no].get("after"),
            "before_dist": pipe_connections[pipe_no].get("before_dist"),
            "after_dist": pipe_connections[pipe_no].get("after_dist"),
            "potential_junction": pipe_connections[pipe_no].get("potential_junction"),
            "size": None,
            "junction": pipe_connections[pipe_no].get("junction"),
        }
        result.append(data)

    return result

def match_rect_to_bom_labels(quad_items: list, bom_labels_df: pd.DataFrame):
    # Match quad items to its BOM label (i.e. rect)
    candidate_quads = []
    bom_labels_matched = set() # index of bom_labels
    centroid_distance_threshold = 5 # Try to make reduce this as small as possible
    for quad_item in quad_items:
        quad_points = quad_item['quad_points']
        quad_centroid = get_centroid(quad_points)
        for bom_label_index, bom_label_item in bom_labels_df.iterrows():
            if bom_label_index in bom_labels_matched:
                continue
            rect = bom_label_item['coordinates']
            vertexes = [
                (rect[0], rect[1]),
                (rect[2], rect[1]),
                (rect[0], rect[3]),
                (rect[2], rect[3]),
            ]
            centroid = get_centroid(vertexes)
            dist = distance_to(quad_centroid, centroid)
            if dist < centroid_distance_threshold:
                value = bom_label_item['value']
                size = bom_label_item['size']
                bom_labels_matched.add(bom_label_index)
                quad_item['bom_label_index'] = bom_label_index
                quad_item['bom_coordinates'] = rect
                quad_item['bom_size'] = size
                quad_item['bom_value'] = value
                quad_item['centroid'] = centroid
                candidate_quads.append(quad_item)
                break
    return candidate_quads

def get_points_along_line(x1, y1, x2, y2, number_of_points=50):
    """Generate number of points points along a line i.e. from point A (x1, y1) to B (x2, y2)"""
    xs = np.linspace(x1, x2, num=number_of_points)
    ys = np.linspace(y1, y2, num=number_of_points)
    for i in range(len(xs)):
        x = xs[i].astype(int)
        y = ys[i].astype(int)
        yield (x, y)

def get_pipeline_coords(pipe_coords: list):
    """Generator for fetching coords from a pipe section
    
    e.g. pipe_coords = [(10, 20), (40, 50), (80, 50)]

    Yield coords from (10,20) to (40,50), then yield coords from (40,50) to (80,50), and so on...
    """
    for start, end in zip(pipe_coords, pipe_coords[1:]):
        start = (int(start[0]), int(start[1]))
        end = (int(end[0]), int(end[1]))
        x1 = start[0]
        y1 = start[1]
        x2 = end[0]
        y2 = end[1]
        # Create the points
        number_of_points = max(abs(x2 - x1), abs(y2 - y1))
        number_of_points = max(1, number_of_points // 6) # 6 is a `magic` number to reduce points
        for point in get_points_along_line(x1, y1, x2, y2, number_of_points=number_of_points):
            yield point

def find_non_arrowed_bom_labels(quad_items: list,
                                pipelines_df: pd.DataFrame,
                                near_pipe_threshold: int = 15):
    """Return the quads which are close and parallel to pipe"""
    # IMPORTANT, removed bottom line angle checking for now
    matched = []
    unmatched = []
    for quad_item in quad_items:
        quad_points = quad_item['quad_points']
        # bottom_line_angle = quad_item['bottom_line_angle']
        centroid = quad_item['centroid']
        vector_id = quad_item['vector_id']

        found = False
        # The bottom line angle is usually in the middle of a pipe segment, 
        # so should match a start or end angle as a validity check
        # If pipe is parallel, check each coord of parallel pipe
        # to see if it is near the quad bottom line
        for _, pipeline in pipelines_df.iterrows():
            pipe_no = pipeline['pipe_no']
            start_angle = pipeline['start_angle']
            end_angle = pipeline['end_angle']
            # print(pipe_no, bottom_line_angle, start_angle, end_angle, parallel_angle_tolerance)
            # Check pipe n to see if any coords are very close to quad bottom line
            pipe_coords = pipeline['pipe_coords']

            # Iterate over every single coord of pipe
            # If one coord meets the threshold, consider that a match and save 
            # the pipe number and coord position
            for pipe_point in get_pipeline_coords(pipe_coords):
                dist = distance_to(pipe_point, centroid)
                if dist < near_pipe_threshold:
                    found = True
                    item = {
                        'page': quad_item['page'],
                        'vector_id': quad_item['vector_id'],
                        'item_position': quad_item['item_position'],
                        'pipe_no': pipe_no,
                        'pipe_coord': pipe_point,
                        'bom_value': quad_item['bom_value'],
                        'bom_size': quad_item['bom_size'],
                        'arrow_tip_id': None,
                        'bom_coordinates': quad_item['bom_coordinates'],
                        'centroid': centroid,
                    }
                    matched.append(item)
                    break

            if found:
                break

        if not found:
            unmatched.append(quad_item)    

    return matched, unmatched

def find_arrowed_bom_labels(quad_items: list, 
                            pipelines_df: pd.DataFrame,
                            arrow_tips_df: pd.DataFrame,
                            near_label_threshold: int = 8,
                            near_pipe_threshold: int = 8):
    """Return all quad items which have an arrow which starts close to the 
    quad and points close to the pipe"""
    matched = []
    unmatched = []
    found = False
    for quad_item in quad_items:
        quad_points = quad_item['quad_points']
        for _, arrow in arrow_tips_df.iterrows():
            arrow_vector_id = arrow['vector_id']
            # Check if start of arrow is connected to bom_label i.e. the rect
            pointer_start = arrow['pointer_start']
            pointer_start = (pointer_start.x, pointer_start.y)
            for quad_point in quad_points:
                dist = distance_to(quad_point, pointer_start)
                if dist < near_label_threshold:
                    break
            else:
                continue # No quad point near to this arrow

            # Check if end (arrow tip) is pointing to a pipe
            target_point = arrow['target_point']
            target_point = (target_point.x, target_point.y)

            found = False
            for _, pipeline in pipelines_df.iterrows():
                pipe_no = pipeline['pipe_no']
                # Check pipe n to see if any coords are very close to bottom line
                pipe_coords = pipeline['pipe_coords']
                for pipe_point in get_pipeline_coords(pipe_coords):
                    dist = distance_to(pipe_point, target_point)
                    if dist < near_pipe_threshold:
                        found = True
                        item = {
                            'page': quad_item['page'],
                            'vector_id': quad_item['vector_id'],
                            'item_position': quad_item['item_position'],
                            'pipe_no': pipe_no,
                            'pipe_coord': pipe_point,
                            'bom_value': quad_item['bom_value'],
                            'bom_size': quad_item['bom_size'],
                            'arrow_tip_id': arrow_vector_id,
                            'bom_coordinates': quad_item['bom_coordinates'],
                            'centroid': quad_item['centroid'],
                        }
                        matched.append(item)
                        break

                if found:
                    break

        if not found:
            unmatched.append(quad_item)
    
    return matched, unmatched

def match_bom_label_with_pipe(page_items: pd.DataFrame,
                              pipelines_df: pd.DataFrame,
                              bom_label_df: pd.DataFrame,
                              arrow_tips_df: pd.DataFrame,
                              page_num: int) -> pd.DataFrame:
    """With the grouped pipes, find quad items which matches the df"""
    # Split into categories
    bom_labels = bom_label_df[bom_label_df['det_cat_1'].str.match('BOM Label')]
    size_labels = bom_label_df[bom_label_df['det_cat_1'].str.match('Size Label')]
    length_labels = bom_label_df[bom_label_df['det_cat_1'].str.match('Length Label')]
    size_break_labels = bom_label_df[bom_label_df['det_cat_1'].str.match('Size Break Label')]

    def get_rect_items(page_items):
        """Extract re, quad items from page_items dataframe"""
        rect_items = []
        for _, item in page_items[page_items['item_type'].isin(['qu', 're'])].iterrows():
            try:
                vector_id = item['vector_id']
                geometry = item['geometry']
                coords = list(geometry.exterior.coords)
                if PRINT_DETAILED_OUTPUT:
                    print(len(coords), len(coords) < 4)
                if len(coords) < 4:
                    continue
                quad_points = [fitz.Point(coords[i][0], coords[i][1]) for i in range(4)]
                start = fitz.Point(quad_points[1][0], quad_points[1][1])
                end = fitz.Point(quad_points[3][0], quad_points[3][1])
                # bottom_line_angle = int(angle_between_points(start, end))
                centroid = get_centroid(quad_points)
                item['quad_points'] = quad_points
                # item['bottom_line_angle'] = bottom_line_angle
                item['centroid'] = centroid
                rect_items.append(item.to_dict())
            except Exception as e:
                if PRINT_DETAILED_OUTPUT:
                    print(f"Error processing rect item: {str(e)}")
                continue

        # Some rect items are constructed by 4 lines
        # Find vectors with 4 lines
        # Note this logic might be useful for other shapes
        line_groups = page_items.loc[page_items['item_type'] == 'l'].groupby('vector_id')
        new_items = []
        for vector_id, line_group in line_groups:
            if len(line_group) != 4:
                continue
            lines = [(_, line['geometry']) for _, line in line_group.iterrows()]
            valid = True
            unique = set()
            bottom_line_angles = set()
            for n1, (_, l1) in enumerate(lines):
                # bottom_line_angle = int(angle_between_points(l1.coords[0], l1.coords[1]))
                # bottom_line_angles.add(bottom_line_angle)
                # Check if each line has two connectionss
                count = 0
                unique.add(l1.coords[0])
                unique.add(l1.coords[1])
                for n2, (_, l2) in enumerate(lines):
                    if n1 == n2: 
                        continue
                    if l1.coords[0] == l2.coords[1]:
                        count += 1
                    if l1.coords[1] == l2.coords[0]:
                        count += 1

                if count != 2:
                    valid = False
                    break

            if valid:
                new_vector_id = page_items['vector_id'].max() + 1 + len(new_items)
                quad_points = [v for v in unique]
                centroid = get_centroid(quad_points)
                new_item = {'vector_id': new_vector_id,
                            'item_position': 0,
                            'item_type': 're',
                            'det_cat_1': 'Other2',
                            'quad_points': quad_points,
                            # 'bottom_line_angle': list(bottom_line_angles),
                            'centroid': centroid,
                            'page': page_num,
                        }
                # new_item = pd.DataFrame(new_row)
                new_items.append(new_item)
                rect_items.append(new_item)
        
        return rect_items

    rect_items = get_rect_items(page_items)
    candidate_rects = match_rect_to_bom_labels(rect_items, bom_labels)
    
    # Assign quad items to pipe
    # Do this in two stages
    # Stage 1 - find BOM labels which are parallel and close to pipe
    non_arrowed_rects, unmatched_rects = find_non_arrowed_bom_labels(candidate_rects, pipelines_df)
    # Stage 2 - find BOM labels which have an arrow pointing to pipe
    # Onl pass in quads which have yet to be matched
    arrowed_rects, _ = find_arrowed_bom_labels(unmatched_rects, pipelines_df, arrow_tips_df)

    results_df = pd.concat([pd.DataFrame(non_arrowed_rects), pd.DataFrame(arrowed_rects)])    
    return results_df

def match_welds_with_pipe(page_items: pd.DataFrame,
                        pipelines_df: pd.DataFrame,
                        arrow_tips_df: pd.DataFrame) -> pd.DataFrame:
    results = []
    checks = []
    near_label_threshold = 24
    near_pipe_threshold = 15
    centroid_near_pipe_threshold = 25

    for _, item in page_items[page_items['det_cat_1'].str.match('Weld Map')].iterrows():
        checks.append(item['coordinates'])

    for coordinates in checks:
        x0, y0, x1, y1 = coordinates
        rect = fitz.Rect(x0, y0, x1, y1)
        # Check for a number of points on the sides of the rect
        points = []
        points.extend([pt for pt in get_points_along_line(rect.tl.x, rect.tl.y, rect.bl.x, rect.bl.y, 5)])
        points.extend([pt for pt in get_points_along_line(rect.tl.x, rect.tl.y, rect.tr.x, rect.tr.y, 5)])
        points.extend([pt for pt in get_points_along_line(rect.tr.x, rect.tl.y, rect.br.x, rect.br.y, 5)])
        points.extend([pt for pt in get_points_along_line(rect.bl.x, rect.tl.y, rect.br.x, rect.br.y, 5)])
        for _, arrow in arrow_tips_df.iterrows():
            arrow_vector_id = arrow['vector_id']

            # Check if start of arrow is connected to weld rect
            pointer_start = arrow['pointer_start']
            pointer_start = (pointer_start.x, pointer_start.y)
            for point in points:
                dist = distance_to(point, pointer_start)
                if dist < near_label_threshold:
                    break
            else:
                # No point on rect near to this arrow
                continue

            # Check if end (arrow tip) is pointing to a pipe
            target_point = arrow['target_point']
            target_point = (target_point.x, target_point.y)

            nearest_pipe = find_nearest_pipe(target_point, pipelines_df, near_pipe_threshold)
            if nearest_pipe is not None:
                pipe_no = nearest_pipe['pipe_no']
                pipe_size = nearest_pipe['size']
                item = {
                    'coordinates': coordinates,
                    'target_point': target_point,
                    'pipe_no': pipe_no,
                    'size': pipe_size,
                    'assign_method': 'Arrow Tip',
                }
                if x0 == 1864.4859619140625:
                    pass
                results.append(item)
                break

            if nearest_pipe:
                break
        else:
            # No arrow so check the centroid for nearest pipe
            vertexes = [
                (rect[0], rect[1]),
                (rect[2], rect[1]),
                (rect[0], rect[3]),
                (rect[2], rect[3]),
            ]
            centroid = get_centroid(vertexes)
            # TODO - find nearest weld would probably me more reliable
            nearest_pipe = find_nearest_pipe(centroid, pipelines_df, centroid_near_pipe_threshold)
            if nearest_pipe is not None:
                pipe_no = nearest_pipe['pipe_no']
                pipe_size = nearest_pipe['size']
                item = {
                    'coordinates': coordinates,
                    'target_point': centroid,
                    'pipe_no': pipe_no,
                    'size': pipe_size,
                    'assign_method': 'Centroid',
                }
                results.append(item)

    return results

def draw_pipeline_data(pipelines: pd.DataFrame, 
                       outpage: fitz.Page, 
                       pipe_size_colors: dict,
                       color_code_pipesize: bool = False):
    # Draw pipeline data
    print("")
    print("Drawing pipeline lines with random color...")
    for _, pipeline in pipelines.iterrows():
        pipe_no = pipeline['pipe_no']
        pipe_coords = pipeline['pipe_coords']
        pipe_start = pipeline['pipe_start']
        pipe_size = pipeline['size']
        shape = outpage.new_shape()
        for start, end in zip(pipe_coords, pipe_coords[1:]):
            print(f"pipe_{pipe_no} drawing line shape from", start, "to", end)
            shape.draw_line(start, end)

        rect = fitz.Rect(pipe_start[0], pipe_start[1], pipe_start[0]+64, pipe_start[1]+32)

        # yellow rect behind pipe label
        shape2 = outpage.new_shape()
        shape2.draw_rect(fitz.Rect(pipe_start[0], pipe_start[1]-8, pipe_start[0]+28, pipe_start[1]))
        finish_params = {'even_odd': True,
                         'stroke_opacity': 1.0, 'fill_opacity': 1, 'fill': (1,1,0), 'color': (1,1,0), 'dashes': '[] 0',
                         'closePath': False, 'lineJoin': 1.0, 'lineCap': 0, 'width': 1}
        shape2.finish(**finish_params)
        shape2.commit()
    
        shape.insert_text(rect.tl, f"pipe_{pipe_no}", fontsize=8, color=(1, 0, 0))

        for start, end in zip(pipe_coords, pipe_coords[1:]):
            shape.draw_line(fitz.Point(start[0], start[1]), fitz.Point(end[0], end[1]))

        color = (random.uniform(0, 1), random.uniform(0, 1), random.uniform(0, 1))
        line_width = 3
        if color_code_pipesize:
            if pipe_size is None:
                color = (0, 0, 0)
            else:
                color = pipe_size_colors.setdefault(pipe_size, color)
            line_width = 4
        finish_params = {'even_odd': True,
                         'stroke_opacity': 1.0, 'fill_opacity': 0, 'fill': None, 'color': color, 'dashes': '[] 0',
                         'closePath': False, 'lineJoin': 1.0, 'lineCap': 0, 'width': line_width}
        shape.finish(**finish_params)
        shape.commit()

def output_isometric_lines_to_pdf(page_items: pd.DataFrame,
                                  input_filepath: str,
                                  bom_labels_df: pd.DataFrame,
                                  pipelines_df: pd.DataFrame,
                                  page_welds: pd.DataFrame,
                                  page_num: int,
                                  extra_info: bool = True):
    print("Starting PDF output process...")
    doc = fitz.open(input_filepath)
    outpdf = fitz.open()

    pipe_size_colors = {}

    print(f"Processing page {page_num}")

    #pipes_drawn = 0
    bom_labels_drawn = 0

    page_pipelines = pipelines_df

    def draw(outpage: fitz.Page, color_code_pipesize=False, draw_page_welds: bool = False):
        # Draw all items for this page
        items_drawn = 0
        for _, item in page_items.iterrows():
            try:
                shape = outpage.new_shape()
                geometry = item['geometry']

                if item['det_cat_1'] == 'Measurement':
                        color = (0.6, 0.3, 0) # Brown
                elif item['det_cat_1'] == 'Pointer':
                    color = (0, 0, 1)  # Blue
                elif item['det_cat_1'] == 'Text Callout':
                    color = (0.5, 0, 0.5)  # Purple
                elif item['det_cat_1'] == 'Weld Map':
                    color = (1, 0, 1) # Magenta
                elif item['det_cat_1'] == 'BOM Label':
                    color = (0, 1, 1) # Cyan
                elif item['det_cat_1'] == 'Size Label':
                    color = (0.5, 1, 0) # Lime
                elif item['det_cat_1'] == 'Size Break Label':
                    color = (1, 0, 0) # Red
                elif item['det_cat_1'] == 'Length Label':
                    color = (0.6, 0.3, 0)
                elif item['det_cat_1'] == 'Arrow Tip':
                    source_point = item['pointer_start']
                    rect = fitz.Rect(source_point.x-1, source_point.y-1, source_point.x+1, source_point.y+1)
                    shape.draw_rect(rect) # draw dot for source point
                    color = (0.7, 0.1, 0.8) # lighter purple
                else:
                    # continue
                    color = (1, 0.5, 0) # Orange

                # # New code for debugging pointers
                if item['det_cat_1'] == 'Pointer':
                    pass

                if item['det_cat_1'] == 'BOM Label':
                    # Draw bounding box for Weld Map and BOM Label
                    if item['target_point']:
                        color = (0.3, 0.8, 0.2)
                    x0, y0, x1, y1 = item['coordinates']
                    rect = fitz.Rect(x0, y0, x1, y1)
                    shape.draw_rect(rect)
                    shape.finish(color=color, width=1)
                    rect2 = fitz.Rect(x0+2, y0+2, x1+2, y1+2)
                    # Add label text for BOM Label
                    if item['target_point'] and item.get('value'):
                        value = f"{item['value']}, p={item['pipe_no']}"
                        print("BOM label value", value)
                        centroid: Point = item['centroid']
                        centroid_point = fitz.Point(centroid.x, centroid.y)
                        print(centroid)
                        rect3 = fitz.Rect(centroid.x-1, centroid.y-1, centroid.x+1, centroid.y+1)
                        shape.draw_rect(rect3) # draw dot for source centroid
                        shape.finish(color=(0.9, 0.1, 0.1), width=1)
                        shape.insert_text(rect2.tl, value, fontsize=8, color=(0, 0, 0))
                else:
                    if item['det_cat_1'] == 'Other' and item["item_type"] != "qu":
                        continue
                    # rect = fitz.Rect(start[0], start[1], end[0], end[1])
                    # orange_drawn += 1
                    # shape.insert_text(rect.tl, f"({orange_drawn})", fontsize=6, color=color)

                    if item['item_type'] == 'l':
                        if len(geometry.coords) < 2:
                            print(f"Warning: Line with less than 2 coordinates: {geometry}")
                            continue
                        start = fitz.Point(geometry.coords[0][0], geometry.coords[0][1])
                        end = fitz.Point(geometry.coords[-1][0], geometry.coords[-1][1])
                        shape.draw_line(start, end)
                    elif item['item_type'] == 're':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Rectangle with less than 4 coordinates: {coords}")
                            continue
                        rect = fitz.Rect(coords[0][0], coords[0][1], coords[2][0], coords[2][1])
                        shape.draw_rect(rect)
                    elif item['item_type'] == 'qu':
                        coords = list(geometry.exterior.coords)
                        if len(coords) < 4:
                            print(f"Warning: Quad with less than 4 coordinates: {coords}")
                            continue
                        # elif len(coords) > 4:
                        #     continue # big arrow candidate
                        quad_points = [fitz.Point(coords[i][0], coords[i][1]) for i in range(4)]
                        shape.draw_quad(fitz.Quad(quad_points))
                    elif item['item_type'] == 'c':
                        points = [fitz.Point(x, y) for x, y in geometry.coords]
                        if len(points) < 2:
                            print(f"Warning: Curve with less than 2 points: {points}")
                            continue
                        elif len(points) == 2:
                            # If only 2 points, draw a line
                            shape.draw_line(points[0], points[1])
                        elif len(points) == 3:
                            # If 3 points, use the middle point as both control points
                            shape.draw_bezier(points[0], points[1], points[1], points[2])
                        else:
                            # If 4 or more points, use the first and last as endpoints and the middle two as control points
                            shape.draw_bezier(points[0], points[1], points[-2], points[-1])
                    else:
                        print(f"Unhandled item type: {item['item_type']}")
                        continue

                    finish_params = get_finish_params(item['drawing'])
                    finish_params['color'] = color
                    shape.finish(**finish_params)

                # Draw item number and rectangle if available
                if 'item_number' in item and pd.notna(item['item_number']):
                    if 'item_coordinates' in item and pd.notna(item['item_coordinates']):
                        item_coords = eval(item['item_coordinates'])
                        rect = fitz.Rect(item_coords[0], item_coords[1], item_coords[2], item_coords[3])
                        shape.draw_rect(rect)
                        shape.insert_text(rect.tl, str(item['item_number']), fontsize=8, color=(0, 0, 0))

                shape.commit()

                items_drawn += 1
            except Exception as e:
                print(f"Error processing item: {item}")
                print(f"Error message: {str(e)}")

        draw_pipeline_data(page_pipelines, outpage, pipe_size_colors, color_code_pipesize)

        unique_sizes = len(page_pipelines.groupby('size'))
        unassigned = len(page_pipelines.loc[page_pipelines['size'].isnull()])

        if extra_info:
            # Draw colored rect for text overlay clarity
            shape = outpage.new_shape()
            shape.draw_rect(fitz.Rect(0, 0, 800, outpage.rect.height))
            finish_params = {
                "fill": (1, 1, 1),  # fill color
                "color": (1, 1, 1),  # line color
                "dashes": None,  # line dashing
                "even_odd": True,  # control color of overlaps
                "closePath": True,  # whether to connect last and first point
                "lineJoin": False,  # how line joins should look like
                "lineCap": 1,  # how line ends should look like
                "width": 1,  # line width
                "stroke_opacity": 1,  # same value for both
                "fill_opacity": 0.8,  # opacity parameters
            }
            shape.finish(**finish_params)
            shape.commit()
            shape = outpage.new_shape()
            y = 32
            text =  f"Page: {page_num}\t  Unique Pipe Sizes={unique_sizes}\t  Unassigned Pipes: {unassigned}\n\n"
            for _, pipe in page_pipelines.iterrows():
                pipe_no = pipe["pipe_no"]
                size = pipe["size"]
                before = pipe["before_pipe"]
                try:
                    int(before.split('_')[0])
                except:
                    before = None
                
                after = pipe["after_pipe"]
                try:
                    int(after.split('_')[0])
                except:
                    after = None

                start_angle = pipe["start_angle"]
                end_angle = pipe["end_angle"]
                try:
                    before_dist = round(pipe["before_dist"], 3)
                except:
                    before_dist = None
                
                log_ = pipe["log"]
                text += f"\t\tpipe_no={pipe_no}, size={size}, before_pipe={before}, after_pipe={after},"
                text += f" start_angle={start_angle}, end_angle={end_angle}, before_dist={before_dist}, " 
                text += f"{'note= '+ log_ if log_ else ''}"
                text += "\n"

            shape.insert_text((16, y), text, fontsize=22, color=(0, 0, 0))
            shape.commit()

            for _, weld_result in page_welds.iterrows():
                shape = outpage.new_shape()
                x0, y0, x1, y1 = weld_result["coordinates"]
                rect = fitz.Rect(x0, y0, x1, y1)
                size = weld_result["size"]
                pipe_no = weld_result["pipe_no"]
                shape.draw_rect(rect)
                text = f"size={size}, p_no={pipe_no}"
                color = (0, 0.65, 0.45) if size else getColor("lightblue")
                shape.insert_text((x0, y0), text, fontsize=18, color=color)
                finish_params = get_finish_params(color=color, width=2)
                shape.finish(**finish_params)
                shape.commit()

        print()
        print()
        print(f"PDF pages drawn for page {page_num}. Total items drawn: {items_drawn}")

    # Create 2 additional pages for PDF, second page color codes pipes based on pipe size
    # Create a new page with the same dimensions as the input PDF
    insert_page = int(page_num) - 1
    input_page = doc[insert_page]

    outpdf.insert_pdf(doc, from_page=insert_page, to_page=insert_page)

    outpdf.insert_pdf(doc, from_page=insert_page, to_page=insert_page)
    outpage1 = outpdf[1]
    draw(outpage1, color_code_pipesize=True)

    outpage2 = outpdf.new_page(width=input_page.rect.width, height=input_page.rect.height)
    draw(outpage2)

    doc.close()

    return outpdf

def is_within_area(bounds, area):
    """Check if an item's bounding box is within the specified area.
    
    Args:
        bounds: A list, tuple or array of coordinates [x_min, y_min, x_max, y_max] or [x, y, width, height]
        area: A dictionary with keys 'x0', 'y0', 'x1', 'y1' defining the area boundaries
    
    Returns:
        bool: True if the bounds are within the area, False otherwise
    """
    try:
        # Handle different coordinate formats
        if len(bounds) >= 4:
            # Standard format: [x_min, y_min, x_max, y_max]
            x_min, y_min, x_max, y_max = bounds[:4]
            
            # Check if we have width/height format instead of min/max
            if x_max < x_min or y_max < y_min:
                # Likely [x, y, width, height] format
                x_min, y_min = bounds[0], bounds[1]
                x_max, y_max = x_min + bounds[2], y_min + bounds[3]
        elif len(bounds) == 2:
            # Just a point [x, y]
            x_min = x_max = bounds[0]
            y_min = y_max = bounds[1]
        else:
            # Invalid format
            return False
            
        # Check if the bounds are within the area
        return (area['x0'] <= x_min <= area['x1'] and
                area['y0'] <= y_min <= area['y1'] and
                area['x0'] <= x_max <= area['x1'] and
                area['y0'] <= y_max <= area['y1'])
    except (IndexError, TypeError, ValueError) as e:
        print(f"Error in is_within_area: {str(e)}, bounds={bounds}")
        return False

def calculate_angle(line):
    start, end = line.coords
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]

    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)

def is_pointer_line(item, items_to_check, angle_tolerance=20, distance_threshold=8, debug_func=False):
    """
    Identifies "pointers", which point from descriptors/labels to items on the pipe or measurement lines.
    """
    if item['item_type'] != 'l':
        return False

    line_geom = item['geometry']
    line_angle = item['angle']
    start_point = Point(line_geom.coords[0])
    end_point = Point(line_geom.coords[-1])

    for target_item in items_to_check:
        if target_item['item_type'] != 'l':
            continue

        if item['vector_id'] == target_item['vector_id']:
            continue

        if target_item.get('det_cat_1') not in ['Pipe', 'Potential_Pipe', 'Measurement']:
            continue

        target_geom = target_item['geometry']
        target_angle = target_item['angle']
        target_start = Point(target_geom.coords[0])
        target_end = Point(target_geom.coords[-1])

        x1, y1 = target_start.x, target_start.y
        x2, y2 = target_end.x, target_end.y

        # Ignore lines where the angle difference witht the target line is too small
        # i.e close to parallel
        angle_diff = min(abs(line_angle - target_angle), 360 - abs(line_angle - target_angle))
        if angle_diff < angle_tolerance:
            if debug_func:
                print(f"Line angle different with target line is too small. Skip and continue to next check")
            continue

        # Reduce checks by only comparing the
        # end which is closest to the target line 
        # (TODO - remove this assumption if incorrect)
        if (min(start_point.distance(target_start), start_point.distance(target_end))
            < min(end_point.distance(target_start), end_point.distance(target_end))):
            closest_end = start_point
        else:
            closest_end = end_point

        number_of_points = max(abs(x1 - x2), abs(y1 - y2)) // 5
        number_of_points = min(10, int(number_of_points))
        for point in get_points_along_line(x1, y1, x2, y2, number_of_points=number_of_points):
            target_point = Point(point)
            dist_to_target_point = closest_end.distance(target_point)

            if dist_to_target_point > distance_threshold:
                continue

            if debug_func:
                print(f"Line close to {target_item.get('det_cat_1', 'pipe')}. Distance: {dist_to_target_point}")
                print(f"Line angle: {line_angle}, Target angle: {target_angle}, Angle difference: {angle_diff}")
                print(f"Pointer line detected pointing to {target_item.get('det_cat_1', 'pipe')}!")

            return True # Found valid target lines

    return False


def is_parallel_to_pipe(item, pipe_items, angle_tolerance=5, distance_threshold=100, min_parallel_ratio=0.5, min_length=1e-6, debug_func=False):
    if item['item_type'] != 'l':
        return False, []

    line_geom = item['geometry']
    line_angle = item.get('angle')
    
    # Check for very short lines
    if line_geom.length < min_length:
        if debug_func:
            print(f"Line is too short: length = {line_geom.length}")
        return False, []

    if line_angle is None:
        if debug_func:
            print("Line angle is None")
        return False, []

    parallel_pipes = []

    for pipe_item in pipe_items:
        if pipe_item['item_type'] != 'l':
            continue

        pipe_geom = pipe_item['geometry']
        pipe_angle = pipe_item.get('angle')

        if pipe_angle is None:
            continue

        try:
            # Check if angles are parallel (considering the isometric nature)
            angle_diff = min(abs(line_angle - pipe_angle), 360 - abs(line_angle - pipe_angle))
            if angle_diff <= angle_tolerance or abs(angle_diff - 180) <= angle_tolerance:
                # Check if the line runs alongside the pipe
                pipe_buffer = pipe_geom.buffer(distance_threshold)
                parallel_part = line_geom.intersection(pipe_buffer)
                
                if line_geom.length > 0:
                    parallel_ratio = parallel_part.length / line_geom.length
                    if debug_func:
                        print(f"Line angle: {line_angle}, Pipe angle: {pipe_angle}, Parallel ratio: {parallel_ratio}")
                    if parallel_ratio >= min_parallel_ratio:
                        pipe_id = create_id(pipe_item['page'], pipe_item['vector_id'], pipe_item['item_position'])
                        parallel_pipes.append(pipe_id)
                else:
                    if debug_func:
                        print(f"Line has zero length: {line_geom}")
        except Exception as e:
            if debug_func:
                print(f"Error in parallel check: {str(e)}")
    
    return len(parallel_pipes) > 0, parallel_pipes

def determine_significant_widths(df, tolerance=0.01, min_length=10):
    """
    Considers only lines ('l' type) determined to be isometric angles to find the line_widths of the largest lengths. 
    Narrows down to the top 2 sizes. The larger size is assumed as the isometric pipe.
    """
    valid_widths = df[(df['line_width'].notna()) & 
                      (df['line_width'] > 0) & 
                      (df['is_isometric'] == True) & 
                      (df['item_type'] == 'l') &
                      (df['length'] >= min_length)]
    
    if valid_widths.empty:
        print("Warning: No valid widths found for determining significant widths.")
        return None, None

    width_groups = valid_widths.groupby('line_width')['length'].sum().sort_values(ascending=False)
    
    significant_widths = []
    for width, total_length in width_groups.items():
        if not significant_widths or abs(width - significant_widths[0]) > tolerance:
            significant_widths.append(width)
            if len(significant_widths) == 2:
                break
    
    if len(significant_widths) < 2:
        print(f"Warning: Only found {len(significant_widths)} significant width(s).")
        return tuple(significant_widths + [None] * (2 - len(significant_widths)))
    
    return tuple(significant_widths)


def identify_pipe_continuations(page_lines, pipe_width, tolerance=0.01):
    pipe_lines = [line for line in page_lines if line['det_cat_1'] == 'Pipe']
    potential_continuations = [line for line in page_lines if line['det_cat_1'] == 'Other' and abs(line['line_width'] - pipe_width) < tolerance]
    
    def lines_are_collinear(line1, line2):
        # Check if two lines are collinear (on the same path)
        angle1 = line1['angle']
        angle2 = line2['angle']
        return abs(angle1 - angle2) < 5 or abs(abs(angle1 - angle2) - 180) < 5

    def is_potential_elbow(line, pipe1, pipe2):
        # Check if a line could be an elbow connecting two pipe segments
        return (
            (line['start_x'] == pipe1['end_x'] and line['start_y'] == pipe1['end_y'] and
             line['end_x'] == pipe2['start_x'] and line['end_y'] == pipe2['start_y']) or
            (line['start_x'] == pipe2['end_x'] and line['start_y'] == pipe2['end_y'] and
             line['end_x'] == pipe1['start_x'] and line['end_y'] == pipe1['start_y'])
        )

    for potential in potential_continuations:
        # Check if the potential continuation is collinear with any existing pipe segment
        if any(lines_are_collinear(potential, pipe) for pipe in pipe_lines):
            potential['det_cat_1'] = 'Pipe'
            potential['det_cat_2'] = 'Continuation'
            continue

        # Check if the potential continuation could be an elbow
        for i, pipe1 in enumerate(pipe_lines):
            for pipe2 in pipe_lines[i+1:]:
                if is_potential_elbow(potential, pipe1, pipe2):
                    potential['det_cat_1'] = 'Pipe'
                    potential['det_cat_2'] = 'Elbow'
                    break
            if potential['det_cat_1'] == 'Pipe':
                break

    return page_lines

def is_connected(item1, item2, tolerance=5):
    """Check if two items are connected within a small tolerance."""
    geom1 = item1['geometry']
    geom2 = item2['geometry']
    
    # For curves, check if any point is close to the other geometry
    if item1['item_type'] == 'c':
        return any(Point(p).distance(geom2) <= tolerance for p in geom1.coords)
    elif item2['item_type'] == 'c':
        return any(Point(p).distance(geom1) <= tolerance for p in geom2.coords)
    
    return geom1.distance(geom2) <= tolerance

def classify_pipes(page_items, pipe_width, measurement_width, tolerance=0.01):
    pipe_items = []
    for item in page_items:
        # Check if line_width is None or if pipe_width or measurement_width are None
        if item['line_width'] is None or pipe_width is None or measurement_width is None:
            item['det_cat_1'] = 'Other'
            #print(f"Classified as Other: {item['item_type']} (missing width information)")
            continue

        try:
            if abs(item['line_width'] - max(pipe_width, measurement_width)) < tolerance:
                if item['item_type'] == 'l' and item['is_isometric']:
                    item['det_cat_1'] = 'Pipe'
                    #print(f"Classified as Pipe: {item['item_type']} (isometric)")
                else:
                    item['det_cat_1'] = 'Potential_Pipe'
                    #print(f"Classified as Potential_Pipe: {item['item_type']}")
                pipe_items.append(item)
            else:
                item['det_cat_1'] = 'Other'
                #print(f"Classified as Other: {item['item_type']} (width mismatch)")
        except TypeError as e:
            print(f"Error processing item: {item}")
            print(f"Error message: {str(e)}")
            item['det_cat_1'] = 'Other'

    # Additional pass to connect potential pipe segments
    potential_pipes = [item for item in page_items if item['det_cat_1'] == 'Potential_Pipe']
    for item in potential_pipes:
        connected_pipes = [pipe for pipe in pipe_items if pipe['det_cat_1'] == 'Pipe' and is_connected(item, pipe)]
        if connected_pipes:
            item['det_cat_1'] = 'Pipe'
            #print(f"Upgraded to Pipe: {item['item_type']} (connected to {len(connected_pipes)} pipes)")
            pipe_items.append(item)

    return pipe_items



# ----> Classify rectangles
def detect_rectangles(page_items, tolerance=5, rectangularity_threshold=0.98):
    def are_connected(line1, line2):
        return any(distance.euclidean(p1, p2) < tolerance 
                   for p1 in [line1.coords[0], line1.coords[-1]] 
                   for p2 in [line2.coords[0], line2.coords[-1]])

    def is_rectangle(poly):
        if poly.is_valid and poly.area > 0:
            rectangularity = poly.area / poly.minimum_rotated_rectangle.area
            return rectangularity > rectangularity_threshold
        return False

    potential_items = [item for item in page_items if item['det_cat_1'] != 'Pipe' and item['item_type'] == 'l']
    
    rectangles = []
    processed = set()

    for i, item in enumerate(potential_items):
        if i in processed:
            continue
        
        connected_lines = [item['geometry']]
        connected_items = [item]
        processed.add(i)

        # Find connected lines
        for j, other_item in enumerate(potential_items):
            if j in processed:
                continue
            if are_connected(item['geometry'], other_item['geometry']):
                connected_lines.append(other_item['geometry'])
                connected_items.append(other_item)
                processed.add(j)

        # Try to form a polygon from the connected lines
        if len(connected_lines) >= 3:
            points = []
            for line in connected_lines:
                points.extend(line.coords)
            unique_points = list(set(points))
            
            if len(unique_points) >= 4:
                try:
                    poly = Polygon(unique_points)
                    if is_rectangle(poly):
                        rectangles.append(connected_items)
                except ValueError:
                    # If we can't form a valid polygon, skip this set of lines
                    pass

    return rectangles

# ----> Classsify rectangles


def is_pointer_line_vectorized(items, angle_tolerance=0, distance_threshold=40):
    coords, angles, categories, geometries = preprocess_items(items)
    
    if len(coords) == 0:
        return []  # Return empty list if no valid items

    # Create KD-Tree for efficient nearest neighbor search
    tree = cKDTree(coords)
    
    pointers = []
    for i, item in enumerate(items):
        if item['item_type'] != 'l':
            continue
        if 'det_cat_1' in item and item['det_cat_1'] not in [None, 'Other']:
            continue
        
        line_geom = item['geometry']
        line_angle = item['angle']
        start_point = Point(line_geom.coords[0])
        end_point = Point(line_geom.coords[-1])
        
        # Find nearby points for both start and end of the line
        start_indices = tree.query_ball_point([start_point.x, start_point.y], distance_threshold)
        end_indices = tree.query_ball_point([end_point.x, end_point.y], distance_threshold)
        nearby_indices = list(set(start_indices + end_indices))
        
        # Filter out invalid indices and non-target categories
        valid_indices = [idx for idx in nearby_indices if idx < len(categories) and 
                         categories[idx] in ['Pipe', 'Potential_Pipe', 'Measurement']]
        
        if not valid_indices:
            continue
        
        nearby_categories = categories[valid_indices]
        nearby_angles = angles[valid_indices]
        nearby_geometries = [geometries[idx] for idx in valid_indices]
        
        for idx, (target_cat, target_angle, target_geom) in enumerate(zip(nearby_categories, nearby_angles, nearby_geometries)):
            # Check if one end of the line is close to the target item
            target_start, target_end = target_geom.coords[0], target_geom.coords[-1]
            start_to_target_start = start_point.distance(Point(target_start))
            start_to_target_end = start_point.distance(Point(target_end))
            end_to_target_start = end_point.distance(Point(target_start))
            end_to_target_end = end_point.distance(Point(target_end))
            
            if (start_to_target_start <= distance_threshold or
                start_to_target_end <= distance_threshold or
                end_to_target_start <= distance_threshold or
                end_to_target_end <= distance_threshold):
                
                # Check if the line angle is significantly different from the target angle
                angle_diff = min(abs(line_angle - target_angle), 360 - abs(line_angle - target_angle))
                
                if angle_diff > angle_tolerance:
                    # Determine which end of the pointer is closest to the target
                    distances = [start_to_target_start, start_to_target_end, end_to_target_start, end_to_target_end]
                    min_distance_index = distances.index(min(distances))
                    
                    if min_distance_index in [0, 1]:
                        pointer_end = start_point
                    else:
                        pointer_end = end_point
                    
                    if min_distance_index in [0, 2]:
                        target_point = Point(target_start)
                    else:
                        target_point = Point(target_end)
                    
                    pointer_info = {
                        'pointer_index': i,
                        'target_index': valid_indices[idx],
                        'points_to': target_cat,
                        'pointer_start': start_point,
                        'pointer_end': end_point,
                        'target_point': target_point
                    }
                    pointers.append(pointer_info)
                    
                    print(f"Detected pointer: {create_id(items[i]['page'], items[i]['vector_id'], items[i]['item_position'])}")
                    print(f"  Points to: {target_cat}")
                    print(f"  Target ID: {create_id(items[valid_indices[idx]]['page'], items[valid_indices[idx]]['vector_id'], items[valid_indices[idx]]['item_position'])}")
                    print(f"  Target det_cat_1: {items[valid_indices[idx]]['det_cat_1']}")
                    
                    break  # We found a valid pointer, no need to check other nearby items
    
    return pointers

def preprocess_items(items):
    coords = []
    angles = []
    categories = []
    geometries = []
    for item in items:
        if item['item_type'] == 'l' and item['det_cat_1'] in ['Pipe', 'Potential_Pipe', 'Measurement', 'Other']:
            geom = item['geometry']
            coords.append(geom.coords[0])  # Only use start point for KD-Tree
            angles.append(item['angle'])
            categories.append(item.get('det_cat_1', None))  # Use None if 'det_cat_1' doesn't exist
            geometries.append(geom)
    return np.array(coords), np.array(angles), np.array(categories), geometries


def get_page_items(page: fitz.Page, page_num, isometric_area_coords = None) -> list:
    """Returns a list of processed items (dict) which are handled in classification"""
    drawings = page.get_drawings()
    print(f"Page {page_num}: Found {len(drawings)} drawings")
    page_items = []
    for vector_id, drawing in enumerate(drawings):
        for item_position, item in enumerate(drawing['items']):
            angle = None
            length = None
            is_iso = None
            item_type = item[0]
            if item_type == 'l':  # line
                start, end = item[1], item[2]
                geometry = LineString([(start.x, start.y), (end.x, end.y)])
                angle = calculate_angle(geometry)
                length = geometry.length
                is_iso = is_isometric_angle(angle)
            elif item_type == 're':  # rectangle
                x0, y0, x1, y1 = item[1]
                geometry = Polygon([(x0, y0), (x1, y0), (x1, y1), (x0, y1)])
            elif item_type == 'qu':  # quad
                points = item[1]
                geometry = Polygon([(p.x, p.y) for p in points])
            elif item_type == 'c':  # curve
                # For curves, we'll use a LineString approximation
                points = item[1:]  # The control points of the curve
                geometry = LineString([(p.x, p.y) for p in points])
            else:
                print(f"Unhandled item type: {item_type}")
                continue

            # Check if the item is within the isometric drawing area
            if isometric_area_coords and not is_within_area(geometry.bounds, isometric_area_coords):
                continue

            page_items.append({
                'page': page_num,
                'vector_id': vector_id,
                'item_position': item_position,
                'item_type': item_type,
                'geometry': geometry,
                'angle': angle,
                'length': length,
                'is_isometric': is_iso,
                'drawing': drawing,
                'line_width': drawing.get('width', 0),
                'det_cat_1': 'Other',  # Initialize as 'Other'
                'det_cat_2': None,
                'det_cat_3': None,
                'size': None,
                'det_weight': None
            })
    return page_items

def classify_pointers_and_measurements(page_items, pipe_items, pipe_width, measurement_width, rm):
    """
    Measurement lines are parallel to pipe and close to pipe. 
    Pointer lines point to the pipe or to measurement
    """
    if pipe_width is None and measurement_width is None:
        return

    # Filter items to check for pointers
    # Note that 'Measurements' count should be zero
    items_to_check = [item for item in page_items if item['det_cat_1'] 
                    in ['Pipe', 'Potential_Pipe', 'Measurement']]
    
    # Classify measurement lines and pointer lines
    for item in page_items:

        if not (item['item_type'] == 'l' and item['det_cat_1'] == 'Other'):
            continue

        if item['line_width'] is None:
            # Suppress excessive output
            if PRINT_DETAILED_OUTPUT:
                print(f"Skipping classification for item due to missing width information: {item['page']}-{item['vector_id']}-{item['item_position']}")
            continue

        try:
            is_parallel, parallel_pipes = is_parallel_to_pipe(item, pipe_items, debug_func=False)
            if abs(item['line_width'] - min(pipe_width, measurement_width)) < 0.01 and is_parallel:
                item['det_cat_1'] = 'Measurement'
                item_id = create_id(item['page'], item['vector_id'], item['item_position'])

                # Add relationships to RelationshipManager
                for pipe_id in parallel_pipes:
                    rm.add_relationship(item_id, pipe_id)

                # Store parallel pipe IDs in the item for reference
                item['parallel_pipes'] = parallel_pipes
            else:
                # Update items to check, increases processing
                # IMPORTANT - this can drastically increase time !!!!!!
                # items_to_check = [item for item in page_items if item['det_cat_1'] 
                #     in ['Pipe', 'Potential_Pipe', 'Measurement']]
                # if is_pointer_line(item, items_to_check, distance_threshold=35, debug_func=False):
                
                # The faster way, everything else is a pointer
                item['det_cat_1'] = 'Pointer'
        except Exception as e:
            print(f"Error processing item: {item}")
            print(f"Error message: {str(e)}")


def process_page_drawings(filepath, rm: RelationshipManager, page_num, isometric_area_coords=None):
    doc = fitz.open(filepath)

    classified_items = {'Pipe': 0, 
                        'Potential_Pipe': 0, 
                        'Measurement': 0, 
                        'Pointer': 0, 
                        'Other': 0, 
                        'Arrow Tip': 0}

    page = doc[page_num - 1]
    page_items = get_page_items(page, page_num, isometric_area_coords)

    # Determine significant widths for this page
    page_df = pd.DataFrame(page_items)
    pipe_width, measurement_width = determine_significant_widths(page_df)

    # Manual override
    override_widths = False
    if override_widths:
        pipe_width, measurement_width = 2.039, 0.719 

    # Replace the existing pipe classification logic with this
    pipe_items = classify_pipes(page_items, pipe_width, measurement_width)

    classify_pointers_and_measurements(page_items, pipe_items, pipe_width, measurement_width, rm)

    # # Use the vectorized function for pointer detection
    # pointer_indices = is_pointer_line_vectorized(page_items)
    # for index in pointer_indices:
    #     item = page_items[index]
    #     # Check if the item is unclassified or classified as 'Other'
    #     if 'det_cat_1' not in item or item['det_cat_1'] in [None, 'Other']:
    #         item['det_cat_1'] = 'Pointer'

    pointer_info = is_pointer_line_vectorized(page_items)
    for info in pointer_info:
        item = page_items[info['pointer_index']]
        target_item = page_items[info['target_index']]
        item['det_cat_1'] = 'Pointer'
        item['points_to'] = target_item['det_cat_1']  # Use the actual category of the target item
        item['target_id'] = create_id(target_item['page'], target_item['vector_id'], target_item['item_position'])
        item['pointer_start'] = info['pointer_start']
        item['pointer_end'] = info['pointer_end']
        item['target_point'] = info['target_point']

        print(f"Pointer {create_id(item['page'], item['vector_id'], item['item_position'])} points to {item['points_to']} (ID: {item['target_id']})")

    # Verify classifications after pointer detection
    print("Classifications after pointer detection:")
    for item in page_items:
        print(f"{create_id(item['page'], item['vector_id'], item['item_position'])}: {item['det_cat_1']}")
                
    # # After classifying pointers, ensure all items have a classification
    # for item in page_items:
    #     if 'det_cat_1' not in item or item['det_cat_1'] is None:
    #         item['det_cat_1'] = 'Other'

    # # After classifying pipes, measurements, and pointers:
    # rectangles = detect_rectangles(page_items)
    # print(f"Detected {len(rectangles)} potential text callout rectangles.")

    # # Mark these items in your DataFrame:
    # for rect in rectangles:
    #     for item in rect:
    #         item['det_cat_1'] = 'Text Callout'

    # Update your classification counts
    classified_items['Text Callout'] = sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout')
    classified_items['Other'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Other') == 'Other')
    classified_items['Measurement'] -= sum(1 for item in page_items if item['det_cat_1'] == 'Text Callout' and item.get('old_category', 'Measurement') == 'Measurement')
    
    # Count classifications
    for item in page_items:
        classified_items[item['det_cat_1']] += 1

    # Convert to DataFrame first so we can use DataFrame operations
    page_items_df = pd.DataFrame(page_items)

    if PRINT_DETAILED_OUTPUT or True:  
        # Only try to filter if we have data and the column exists
        if not page_items_df.empty and 'det_cat_1' in page_items_df.columns:
            pipe_count = len(page_items_df[page_items_df['det_cat_1'] == 'Pipe'])
            measurement_count = len(page_items_df[page_items_df['det_cat_1'] == 'Measurement'])
            print(f"Page {page_num}: Found {pipe_count} pipe items")
            print(f"Page {page_num}: Found {measurement_count} measurement items")
            
            # Print classified item counts
            classified_counts = page_items_df['det_cat_1'].value_counts().to_dict()
            print(f"Classified items: {classified_counts}")
        print(f"Total items found: {len(page_items)}")

    doc.close()
    return page_items_df

def load_bom_data(filepath):
    try:
        df = pd.read_excel(filepath)
        if PRINT_DETAILED_OUTPUT:
            print(f"Successfully loaded BOM data from {filepath}. Found {len(df)} rows.")
        return df
    except Exception as e:
        print(f"Error loading BOM data from {filepath}: {str(e)}")
        # Return empty DataFrame instead of failing
        return pd.DataFrame()

def find_labels(raw_data_df, bom_data_df, isometric_area, page):
    labels = []
    try:
        # First check if BOM data is available for this page
        if isinstance(bom_data_df, pd.DataFrame) and not bom_data_df.empty:
            if all(col in bom_data_df.columns for col in ['pdf_page', 'pos', 'size']):
                # Use .equals for scalar comparison instead of == which would create an array
                matching_rows = bom_data_df['pdf_page'].apply(lambda x: x == page if not pd.isna(x) else False)
                if matching_rows.any():
                    pos_items = bom_data_df.loc[matching_rows, ['pos', 'size']].to_dict('records')
                else:
                    pos_items = []
                    if PRINT_DETAILED_OUTPUT:
                        print(f"Warning: No BOM items found for page {page}")
            else:
                print(f"Warning: BOM data missing required columns for page {page}. Available columns: {bom_data_df.columns.tolist()}")
                pos_items = []
        else:
            print(f"Warning: BOM data is empty or not a DataFrame for page {page}")
            pos_items = []
        
        # Check if raw data is valid
        if not isinstance(raw_data_df, pd.DataFrame) or raw_data_df.empty:
            print(f"Warning: Raw data is empty or not a DataFrame for page {page}")
            return labels
            
        if 'pdf_page' not in raw_data_df.columns:
            print(f"Warning: Raw data missing 'pdf_page' column for page {page}. Available columns: {raw_data_df.columns.tolist()}")
            return labels
    except Exception as e:
        import traceback
        print(f"Error preparing BOM data for page {page}: {str(e)}")
        traceback.print_exc()
        pos_items = []

    # Process each row in raw data - safely filter by page
    # Use .equals for scalar comparison to avoid array truth value errors
    matching_rows = raw_data_df['pdf_page'].apply(lambda x: x == page if not pd.isna(x) else False)
    filtered_data = raw_data_df[matching_rows] if matching_rows.any() else pd.DataFrame()
    
    for _, row in filtered_data.iterrows():
        try:
            # Safely parse the coordinates string
            if 'coordinates2' not in row or pd.isna(row['coordinates2']):
                continue
                
            coords_str = str(row['coordinates2']).strip()
            
            # Try different parsing approaches
            coords = None
            try:
                # First try regular eval
                coords = eval(coords_str)
            except SyntaxError:
                # If that fails, try to clean up the string
                coords_str = coords_str.replace('(', '[').replace(')', ']')
                try:
                    coords = eval(coords_str)
                except:
                    try:
                        # If all else fails, try to parse it manually
                        coords_str = coords_str.strip('()[]{}').split(',')
                        coords = [float(x.strip()) for x in coords_str if x.strip()]
                    except:
                        # Skip this row if we can't parse the coordinates
                        continue
            
            if not coords or not isinstance(coords, (list, tuple)) or len(coords) < 2:
                # Skip if we couldn't parse valid coordinates
                continue
                
            if is_within_area(coords, isometric_area):
                value = str(row['value']).strip()
                matched_items = match_bom_item(value, pos_items)
                for matched_item in matched_items:
                    labels.append({
                        'page': row['pdf_page'],
                        'coordinates': coords,
                        'det_cat_1': 'BOM Label',
                        'value': matched_item['pos'],
                        'size': matched_item['size']
                    })
                
                # Check for Size Label, Size Break Label, and Length Label
                label_type = classify_label(value)
                if label_type:
                    labels.append({
                        'page': row['pdf_page'],
                        'coordinates': coords,
                        'det_cat_1': label_type,
                        'value': value
                    })
        except Exception as e:
            if PRINT_DETAILED_OUTPUT:
                print(f"Error processing row on page {page}: {str(e)}")
            continue
    
    return labels

def match_bom_item(value, pos_items):
    matched_items = []
    
    # Split the value by spaces to handle "F# G# B#" format
    value_parts = value.split()
    
    for part in value_parts:
        # Check for exact match
        exact_matches = [item for item in pos_items if str(item['pos']) == part]
        matched_items.extend(exact_matches)
        
        # Check for "[A-Z]#" or "[A-Z]##" format
        if re.match(r'^[A-Za-z]\d{1,2}$', part):
            # Extract the number part
            number_part = re.search(r'\d+', part).group()
            prefix_matches = [item for item in pos_items if str(item['pos']) == number_part]
            matched_items.extend(prefix_matches)
    
    return matched_items

def classify_label(value):
    # Remove spaces before NB
    value = re.sub(r'\s+NB', 'NB', value)
    
    # Check for Size Break Label
    if '"' in value and 'x' in value.lower() and 'nb' in value.lower():
        return 'Size Break Label'
    
    # Check for Size Label
    if '"' in value and 'nb' in value.lower():
        return 'Size Label'
    
    # Check for Length Label
    if ('"' in value or "'" in value) and 'nb' not in value.lower():
        return 'Length Label'
    
    return None

def exact_match(value, pos_items):
    return value in [str(item) for item in pos_items]

def prefix_match(value, pos_items):
    # Split the value into parts
    parts = re.findall(r'[A-Za-z]+|\d+', value)
    
    for item in pos_items:
        item_parts = re.findall(r'[A-Za-z]+|\d+', str(item))
        
        # Check if all parts of the item are in the value parts
        if all(part in parts for part in item_parts):
            return True
    
    return False

def load_detected_welds(filepath):
    try:
        df = pd.read_excel(filepath)
        if PRINT_DETAILED_OUTPUT:
            print(f"Successfully loaded detected welds from {filepath}. Found {len(df)} rows.")
    except Exception as e:
        import traceback
        print(f"Error opening detected welds file {filepath}: {str(e)}")
        print("Detailed error information:")
        traceback.print_exc()
        return []
    
    # Debug output for coordinate data
    if PRINT_DETAILED_OUTPUT:
        try:
            print("Sample data from detected_welds:")
            sample = df.head(1)
            for idx, row in sample.iterrows():
                print(f"Row {idx}:")
                if 'coordinates2' in row:
                    print(f"  coordinates2: {row['coordinates2']}")
                    print(f"  Type: {type(row['coordinates2'])}")
                else:
                    print("  coordinates2 column not found")
                    print(f"  Available columns: {df.columns}")
        except Exception as e:
            print(f"Error in debug output: {str(e)}")
        
    weld_maps = []
    for _, row in df.iterrows():
        try:
            # Skip rows without required data
            if 'coordinates2' not in row or pd.isna(row['coordinates2']):
                continue
                
            # Safely parse the coordinates string
            coords_str = str(row['coordinates2']).strip()
            
            # Try different parsing approaches
            coords = None
            try:
                # First try regular eval
                coords = eval(coords_str)
            except SyntaxError:
                # If that fails, try to clean up the string
                coords_str = coords_str.replace('(', '[').replace(')', ']')
                try:
                    coords = eval(coords_str)
                except:
                    try:
                        # If all else fails, try to parse it manually
                        coords_str = coords_str.strip('()[]{}').split(',')
                        coords = [float(x.strip()) for x in coords_str if x.strip()]
                    except:
                        # If we can't parse it at all, use default coordinates
                        coords = [0, 0, 10, 10]  # Default coordinates
                        if PRINT_DETAILED_OUTPUT:
                            print(f"Using default coordinates for weld map on page {row.get('pdf_page', 'N/A')}")
            
            if not coords or not isinstance(coords, (list, tuple)) or len(coords) < 2:
                # Use default coordinates if we couldn't parse valid ones
                coords = [0, 0, 10, 10]  # Default coordinates
                if PRINT_DETAILED_OUTPUT:
                    print(f"Using default coordinates for weld map on page {row.get('pdf_page', 'N/A')}")
            
            # Make sure coordinates have at least 4 elements
            if len(coords) < 4:
                # Extend with zeroes if needed
                coords = list(coords) + [0] * (4 - len(coords))
                
            page = row.get('pdf_page', 0)
            try:
                # Ensure page is an integer
                page = int(float(page))
            except:
                # Use 0 as default page if conversion fails
                page = 0
                
            weld_maps.append({
                'page': page,
                'coordinates': coords[:4],  # Use only the first 4 coordinates
                'det_cat_1': 'Weld Map',
                'object_value': str(row.get('object_value', '')),
            })
        except Exception as e:
            if PRINT_DETAILED_OUTPUT:
                print(f"Error processing weld map row on page {row.get('pdf_page', 'N/A')}: {str(e)}")
            continue
    
    if PRINT_DETAILED_OUTPUT:
        print(f"Processed {len(weld_maps)} weld maps from {len(df)} rows.")
    return weld_maps

def create_id(page, vector_id, item_position):
    try:
        page = int(float(page))
        # Check if vector_id is NaN
        if isinstance(vector_id, float) and math.isnan(vector_id):
            vector_id = 0  # or some other default value
        else:
            vector_id = int(float(vector_id))
        item_position = int(float(item_position))
        return f"{page}-{vector_id}-{item_position}"
    except ValueError as e:
        print(f"Error creating ID: page={page}, vector_id={vector_id}, item_position={item_position}")
        print(f"Error message: {str(e)}")
        return None  # or some default value


# In your main function, after creating the DataFrame:
def clean_dataframe(df):
    # Convert 'page', 'vector_id', and 'item_position' to integers
    for col in ['page', 'vector_id', 'item_position']:
        df[col] = df[col].fillna(0).astype(int)
    return df

def process_page(input_filepath: str, 
                 output_dir: str, 
                 raw_data_filepath: str, 
                 detected_welds_filepath: str, 
                 bom_data_filepath: str,
                 page_num: int):
    
    process_time = time.time()

    rm: RelationshipManager = RelationshipManager()

    isometric_area = {
        'x0': 804.5,
        'x1': 2404.5,
        'y0': 40.0,
        'y1': 1234.0
    }

    # Process vector drawings and create DataFrame
    print("\n\nIsolating Isometric Properties...")
    page_items: pd.DataFrame = process_page_drawings(input_filepath, 
                                                     rm, 
                                                     page_num, 
                                                     isometric_area_coords=isometric_area)

    # Use it in your main function:
    page_items = clean_dataframe(page_items)

    # Add items to RelationshipManager
    for _, item in page_items.iterrows():
        item_id = create_id(item['page'], item['vector_id'], item['item_position'])
        rm.add_item(item_id, item.to_dict())

    # Load detected welds
    if PRINT_DETAILED_OUTPUT:
        print("\n\nLoading Weld, BOM, Raw Data...")
    
    try:
        weld_maps = load_detected_welds(detected_welds_filepath)
    except Exception as e:
        print(f"Error loading detected welds: {str(e)}")
        # Return early with empty results
        return page_num, pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), rm, None, f"Failed to load detected welds: {str(e)}", time.time() - process_time

    # Add weld maps to RelationshipManager
    for weld_map in weld_maps:
        # Assuming unique position for each weld map
        weld_id = create_id(weld_map['page'], 0, len(rm.items))
        rm.add_item(weld_id, weld_map)

    # Load BOM data
    bom_data = load_bom_data(bom_data_filepath)
    # Load raw data with error handling
    try:
        # Check file extension to determine how to load it
        if raw_data_filepath.lower().endswith('.feather'):
            # Use the specialized function for feather files
            raw_data = load_df_fast(raw_data_filepath)
        else:
            # Fall back to pandas for Excel files
            raw_data = pd.read_excel(raw_data_filepath)
            
        if PRINT_DETAILED_OUTPUT:
            print(f"Successfully loaded raw data from {raw_data_filepath}. Found {len(raw_data)} rows.")
    except Exception as e:
        import traceback
        print(f"Error loading raw data from {raw_data_filepath}: {str(e)}")
        print("Detailed error information:")
        traceback.print_exc()
        # Return early with empty results
        return page_num, pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), rm, None, f"Failed to load raw data: {str(e)}", time.time() - process_time

    bom_labels = []

    if PRINT_DETAILED_OUTPUT:
        print("\n\nEvaluating BOM, Size, Length, and Size Break Label Locations...")

    page_labels = find_labels(raw_data, bom_data, isometric_area, page_num)
    bom_labels.extend(page_labels)
    
    # Add labels to RelationshipManager
    for label in page_labels:
        # Assuming unique position for each label
        label_id = create_id(label['page'], 0, len(rm.items))
        rm.add_item(label_id, label)

    bom_labels_df = pd.DataFrame(bom_labels)
    bom_labels_df['target_point'] = ''
    bom_labels_df['pipe_no'] = ''
    bom_labels_df['centroid'] = ''

    page_pipelines = group_pipes(page_items, page_num)
    page_pipelines = pd.DataFrame(page_pipelines)
    page_pipelines['log'] = ''

    # Check if 'page' column exists before filtering
    if 'page' in bom_labels_df.columns:
        page_bom_labels_df = bom_labels_df[bom_labels_df['page'] == page_num]
    else:
        print(f"WARNING: 'page' column not found in BOM labels. Available columns: {bom_labels_df.columns.tolist()}")
        # Create an empty DataFrame with the same columns
        page_bom_labels_df = pd.DataFrame(columns=bom_labels_df.columns)

    updated = set() # prevent duplicate updates
    arrow_tip_items = detect_arrow_tips(page_items)
    for a in arrow_tip_items:
        v = (a['vector_id'], a['page'])
        if v in updated:
            continue
        updated.add(v)
        condition = (page_items['vector_id'] == a['vector_id']) & (page_items['page'] == page_num)
        page_items.loc[condition, 'det_cat_1'] = a['det_cat_1']
        page_items.loc[condition, 'det_cat_2'] = a['det_cat_2']
        page_items.loc[condition, 'pointer_start'] = Point(a['pointer_start'])
        page_items.loc[condition, 'target_point'] = Point(a['target_point'])

    arrow_tip_items_df = pd.DataFrame(arrow_tip_items)
    results = match_bom_label_with_pipe(page_items, 
                                        page_pipelines, 
                                        page_bom_labels_df, 
                                        arrow_tip_items_df, 
                                        page_num)

    # Update pipelines df with sizes
    if not results.empty:
        results_grouped = results.groupby('pipe_no')
        for pipe_no, pipe_match in results_grouped:
            sizes = pipe_match['bom_size'].to_list()
            sizes = [s for s in sizes if 'x' not in s.lower()]
            if len(set(sizes)) > 1:
                print("This needs handling! There are multiple sizes assigned to this pipe")
                page_pipelines.loc[(page_pipelines['pipe_no'] == pipe_no), 'log'] = 'Check. Multiple sizes'
            if not sizes:
                continue
            page_pipelines.loc[(page_pipelines['pipe_no'] == pipe_no), 'size'] = sizes[0]

    # Look for any pipes without any assignment and try to check
    # before pipe and after pipe for a size
    for _, pipe_row in page_pipelines.iterrows():
        if pipe_row['size']:
            continue
        pipe_no = pipe_row['pipe_no']
        before = pipe_row['before_pipe_no']
        after = pipe_row['after_pipe_no']
        size = None
        # TODO - neighboring pipes may also not have a pipe size assigned

        # Prioritize before pipe
        try:
            size = page_pipelines.loc[(page_pipelines['pipe_no'] == before), 'size'].values[0]
        except Exception as e:
            pass
        try:
            if size is None:
                size = page_pipelines.loc[(page_pipelines['pipe_no'] == after), 'size'].values[0]
        except Exception as e:
            pass

        page_pipelines.loc[(page_pipelines['pipe_no'] == pipe_no), 'size'] = size
        print(pipe_no, size, before, after)

    for _, b in results.iterrows():
        bom_coordinates = b['bom_coordinates']
        bom_labels_df.loc[(bom_labels_df['coordinates'] == bom_coordinates)
                        & (bom_labels_df['page'] == page_num) 
                        & (bom_labels_df['det_cat_1'] == 'BOM Label'), 'target_point'] = Point(b['pipe_coord'])
        
        bom_labels_df.loc[(bom_labels_df['coordinates'] == bom_coordinates)
                        & (bom_labels_df['page'] == page_num) 
                        & (bom_labels_df['det_cat_1'] == 'BOM Label'), 'pipe_no'] = b['pipe_no']

        try:
            bom_labels_df.loc[(bom_labels_df['coordinates'] == bom_coordinates)
                            & (bom_labels_df['page'] == page_num) 
                            & (bom_labels_df['det_cat_1'] == 'BOM Label'), 'centroid'] = Point(b['centroid'])
        except Exception as e:
            pass

    # Filter weld maps for specified pages
    if PRINT_DETAILED_OUTPUT:
        print("\n\nEvaluating Weld Map Locations...")    
    # if pages_to_process is not None:
    page_welds = [wm for wm in weld_maps if wm['page'] == page_num]
    page_welds = pd.DataFrame(page_welds)

    print("\n\nConsolidating Data...")
    # Concatenate weld maps and BOM labels to the DataFrame
    page_items = pd.concat([page_items, page_welds, bom_labels_df], ignore_index=True)

    page_weld_results = match_welds_with_pipe(page_items, page_pipelines, arrow_tip_items_df)
    page_welds['pipe_no'] = ''
    page_welds['size'] = ''
    for weld_match in page_weld_results:
        size = weld_match['size']
        pipe_no = weld_match['pipe_no']
        page_welds.loc[(page_welds['coordinates'] == weld_match['coordinates']), 'pipe_no'] = pipe_no
        page_welds.loc[(page_welds['coordinates'] == weld_match['coordinates']), 'size'] = size

    print(page_items.describe())
    print(f"Isometric lines after item identification: {len(page_items)}")

    # Print summary of the DataFrame
    print(f"\nDataFrame summary:")
    print(page_items.describe())
    #print(f"\nIsometric lines: {len(page_items)}")
    print(f"\n\nTotal items: {len(page_items)}")
    print(f"Weld Maps: {len(page_items[page_items['det_cat_1'] == 'Weld Map'])}")
    print(f"BOM Labels: {len(page_items[page_items['det_cat_1'] == 'BOM Label'])}")

    # print(f"\n\nPointers originating from labels: {len(page_items[page_items['originates_from'].str.endswith('Label', na=False)])}")
    # print(f"Unique origin types: {page_items['originates_from'].nunique()}")
    # print(f"Unique target types: {page_items['points_to'].nunique()}")

    # Output isometric lines to PDF
    doc = output_isometric_lines_to_pdf(page_items,
                                        input_filepath,
                                        pd.DataFrame(bom_labels), 
                                        page_pipelines,
                                        page_welds,
                                        page_num)

    process_time = time.time() - process_time
    return page_num, page_items, page_pipelines, page_welds, rm, doc.tobytes(), None, process_time

def process_page_mp(input_filepath: str, 
                    output_dir: str, 
                    raw_data_filepath: str, 
                    detected_welds_filepath: str, 
                    bom_data_filepath: str,
                    page_num: int):
    """This is more forgiving and will prevent multiprocessing failing
    We report the error as a return value
    """
    try:
        process_time = time.time()
        return process_page(input_filepath, 
                            output_dir, 
                            raw_data_filepath, 
                            detected_welds_filepath, 
                            bom_data_filepath, 
                            page_num)
    except Exception as exc:
        import traceback
        print(f'Failed page {page_num}: {str(exc)}')
        print("Full error traceback:")
        traceback.print_exc()
        rm = RelationshipManager()
        page_items = pd.DataFrame()
        page_pipelines = pd.DataFrame()
        page_welds = pd.DataFrame()
        process_time = time.time() - process_time
        
        # Create a blank PDF with error message instead of returning None
        try:
            # Open the input PDF to get the page dimensions
            doc = fitz.open(input_filepath)
            if page_num <= len(doc):
                page = doc[page_num-1]
                width, height = page.rect.width, page.rect.height
            else:
                width, height = 612, 792  # Standard letter size
            doc.close()
            
            # Create a new PDF document
            error_doc = fitz.open()
            error_page = error_doc.new_page(width=width, height=height)
            
            # Add error message
            error_page.insert_text((50, 50), f"Error processing page {page_num}:", fontsize=12)
            error_page.insert_text((50, 70), str(exc), fontsize=10)
            
            # Return the error document bytes
            return page_num, page_items, page_pipelines, page_welds, rm, error_doc.tobytes(), exc, process_time
        except Exception as e:
            print(f"Error creating error document: {str(e)}")
            # If we can't create an error document, return None
            return page_num, page_items, page_pipelines, page_welds, rm, None, exc, process_time

def main(input_filepath, 
         output_dir, 
         raw_data_filepath, 
         detected_welds_filepath, 
         bom_data_filepath,
         pages_to_process=None,
         max_pages: int = None,
         use_multiprocess: bool = False):
    """Main function for processing PDF drawings.
    
    This function handles the overall processing workflow:
    1. Process each page in the PDF to extract vector graphics
    2. Classify components like pipes, welds, and BOM labels
    3. Generate output files including CSV, Excel, and annotated PDFs
    
    Args:
        input_filepath: Path to the input PDF file
        output_dir: Directory to save output files
        raw_data_filepath: Path to raw data Excel file
        detected_welds_filepath: Path to detected welds Excel file
        bom_data_filepath: Path to BOM data Excel file
        pages_to_process: List of page numbers to process (None for all pages)
        max_pages: Maximum number of pages to process
        use_multiprocess: Use multiprocessing for parallel processing
    
    Returns:
        Tuple containing processing results
    """
    start_time = time.time()

    if pages_to_process is None:
        pages_to_process = [n + 1 for n in range(fitz.open(input_filepath).page_count)]
    elif type(pages_to_process) == int:
            pages_to_process = [pages_to_process]
    else:
        try:
            iter(pages_to_process)
        except TypeError as e:
            raise e

    if max_pages is not None:
        assert max_pages > 0, 'Max pages must be >= 1'

    pages_to_process = pages_to_process[:max_pages]
    pages_to_process.sort()

    assert pages_to_process[0] - 1 >= 0, 'Page numbers are zero indexed, ensure page is >= 1'

    results = []
    if not use_multiprocess:
        for page_num in pages_to_process:
            r = process_page(input_filepath, 
                             output_dir, 
                             raw_data_filepath, 
                             detected_welds_filepath, 
                             bom_data_filepath, 
                             page_num)
            results.append(r)

    else:
        # Multiprocessing
        with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
            args = [(input_filepath, 
                    output_dir, 
                    raw_data_filepath, 
                    detected_welds_filepath, 
                    bom_data_filepath, 
                    page_num) for page_num in pages_to_process]
            results = pool.starmap(process_page_mp, args)
            results.sort(key=lambda x: x[0])  # Sorted results by page_num

    # Save results
    docs = []
    vector_data = []
    pipelines_data = []
    weld_results_data = []
    sheet_names = [f"Page{page_num}" for page_num in pages_to_process]
    sheet_names = []
    summary_pages = []
    for r in results:
        page_num, page_items, page_pipelines, page_welds, rm, doc_bytes, error, process_time = r
        if error is not None:
            summary_pages.append({"page": page_num, "error": str(error), "process_time": process_time})
            continue
        sheet_names.append(f"Page{page_num}")
        docs.append(doc_bytes)
        vector_data.append(page_items)
        pipelines_data.append(page_pipelines)
        weld_results_data.append(page_welds)
        summary_pages.append({"page": page_num, "error": None, "process_time": process_time})

    if summary_pages:
        error_file = os.path.join(output_dir, "process_summary.xlsx")
        pd.DataFrame(summary_pages).to_excel(error_file)

    pdf_outpath = os.path.join(output_dir, "output_clusters.pdf")
    print(f"Joining PDF results and saving to {pdf_outpath}")
    outpdf = fitz.open()
    
    # Check if we have any document bytes to process
    if docs:
        # Join docs for output file
        for doc_bytes in docs:
            try:
                doc = fitz.open(stream=doc_bytes, filetype="pdf")
                if len(doc) > 0:  # Make sure the document has pages
                    outpdf.insert_pdf(doc, from_page=0, to_page=len(doc)-1)
            except Exception as e:
                print(f"Error processing document: {str(e)}")
    
    # Save the PDF only if it has pages
    if len(outpdf) > 0:
        outpdf.save(pdf_outpath)
        print(f"PDF saved with {len(outpdf)} pages")
    else:
        print("WARNING: No pages to save in PDF. Creating a blank page to avoid error.")
        # Create a blank page to avoid the 'cannot save with zero pages' error
        page = outpdf.new_page(width=612, height=792)  # Standard letter size
        page.insert_text((50, 50), "No valid pages were processed", fontsize=12)
        outpdf.save(pdf_outpath)
        print("Created PDF with a blank page")

    # Only export vector data if we have valid data
    if vector_data and any(not df.empty for df in vector_data):
        # Export results to Excel if not skipped
        vector_data_outfile = os.path.join(output_dir, "vector_data.xlsx")
        print(f"Saving vector data to {vector_data_outfile}")
        export_to_excel(vector_data, vector_data_outfile, sheet_names=sheet_names, max_column_width=80)
    else:
        print("No vector data to export. Skipping vector_data.xlsx creation.")
        # Create a simple Excel file with a message
        vector_data_outfile = os.path.join(output_dir, "vector_data.xlsx")
        df = pd.DataFrame({"Message": ["No valid data was processed", "Check process_summary.xlsx for errors"]})
        df.to_excel(vector_data_outfile, index=False)
        print(f"Created empty vector data file at {vector_data_outfile}")

    # Only export pipelines data if we have valid data and export is not skipped
    if pipelines_data and any(not df.empty for df in pipelines_data):
        pipelines_data_outfile = os.path.join(output_dir, "pipelines_data.xlsx")
        print(f"Saving pipelines data to {pipelines_data_outfile}")
        export_to_excel(pipelines_data, pipelines_data_outfile, sheet_names=sheet_names, max_column_width=80)
    else:
        print("No pipelines data to export. Skipping pipelines_data.xlsx creation.")
        # Create a simple Excel file with a message
        pipelines_data_outfile = os.path.join(output_dir, "pipelines_data.xlsx")
        df = pd.DataFrame({"Message": ["No valid data was processed", "Check process_summary.xlsx for errors"]})
        df.to_excel(pipelines_data_outfile, index=False)
        print(f"Created empty pipelines data file at {pipelines_data_outfile}")

    # Only export weld results data if we have valid data and export is not skipped
    if weld_results_data and any(not df.empty for df in weld_results_data):
        weld_results_outfile = os.path.join(output_dir, "weld_results_data.xlsx")
        print(f"Saving weld results data to {weld_results_outfile}")
        export_to_excel(weld_results_data, weld_results_outfile, sheet_names=sheet_names, max_column_width=80)
    else:
        print("No weld results data to export. Skipping weld_results_data.xlsx creation.")
        # Create a simple Excel file with a message
        weld_results_outfile = os.path.join(output_dir, "weld_results_data.xlsx")
        df = pd.DataFrame({"Message": ["No valid weld data was processed", "Check process_summary.xlsx for errors"]})
        df.to_excel(weld_results_outfile, index=False)
        print(f"Created empty weld results file at {weld_results_outfile}")

    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n\nIdentified in {total_time} seconds...")
    print(f"\n\nBenchmark: Average page process in {total_time//len(pages_to_process)}s")
    print(f"\n\Multiprocessing: {USE_MULTIPROCESS}")

    # # Insert bytes page into pdf
    # for weld_counts, pdf_bytes in results:
    #     weld_counts_list.append(weld_counts)
    #     try:
    #         d = fitz.open(stream=pdf_bytes, filetype="pdf")
    #         outpdf.insert_pdf(d)
    #     except fitz.EmptyFileError:
    #         page = doc[weld_counts['page_num']]
    #         outpdf.new_page(width=page.rect.width, height=page.rect.height)


if __name__ == "__main__":
    # Debug configuration happens at the top of the file - no command line needed
    if DEBUG_SINGLE_PAGE is not None:
        print(f"\n*** PROCESSING ONLY PAGE {DEBUG_SINGLE_PAGE} IN DEBUG MODE ***\n")
        pages_to_process = [DEBUG_SINGLE_PAGE]
    else:
        print(f"\n*** PROCESSING PAGES: {PAGES_TO_PROCESS} ***\n")

    start_time = time.time()

    # Updated paths for the current project
    base_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014"
    output_dir = os.path.join(base_path, "Data\TR-001")
    os.makedirs(output_dir, exist_ok=True)
    
    # Input PDF path
    input_filepath = os.path.join(base_path, "Modified\TR-001 Combined.pdf")
    
    # Data files
    raw_data_filepath = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\74\sources\cuserscmccaonedrivedocumentsarchitekt-isclientsexcel-usaexc_0014modifiedtr-001-combinedpdf\data.feather"
    detected_welds_filepath = os.path.join(output_dir, "TR-001_gusset_items.xlsx")  # Using gusset items as welds for now
    bom_data_filepath = os.path.join(output_dir, "bom_tr-001.xlsx")

    # Print configuration settings
    print(f"\nInput PDF: {input_filepath}")
    print(f"Output directory: {output_dir}")
    print(f"Raw data file: {raw_data_filepath}")
    print(f"Detected welds file: {detected_welds_filepath}")
    print(f"BOM data file: {bom_data_filepath}")
    
    # Input PDF path
    input_filepath = os.path.join(base_path, "Modified\TR-001 Combined.pdf")
    
    # Data files
    raw_data_filepath = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\74\sources\cuserscmccaonedrivedocumentsarchitekt-isclientsexcel-usaexc_0014modifiedtr-001-combinedpdf\data.feather"
    detected_welds_filepath = os.path.join(output_dir, "TR-001_gusset_items.xlsx")  # Using gusset items as welds for now
    bom_data_filepath = os.path.join(output_dir, "bom_tr-001.xlsx")

    
    def detect_pages_to_check() -> list:
        """Returns a list of unique pdf pages from the detected_welds data"""
        pages_to_check = pd.read_excel(detected_welds_filepath)
        unique = pages_to_check['pdf_page'].unique().tolist()
        return unique

    # CONFIGURATION: Specify which pages to process here
    # Set to None to process all pages, or specify a list of page numbers
    # Example: pages_to_process = [1, 2, 3]  # Process only pages 1, 2, and 3
    
    # Pages to process already set above by debug configuration
    
    # Uncomment this line to process all pages (WARNING: very slow for large PDFs)
    # pages_to_process = None
    
    # Uncomment this line to process only pages with detected welds
    # pages_to_process = detect_pages_to_check()
    
    print(f"\n*** PROCESSING ONLY THESE PAGES: {pages_to_process} ***\n")

    # Process only pages 1, 2, 3, 4, and 5
    results = main(input_filepath, 
                   output_dir, 
                   raw_data_filepath, 
                   detected_welds_filepath, 
                   bom_data_filepath, 
                   pages_to_process=pages_to_process,
                   use_multiprocess=USE_MULTIPROCESS)
    
    end_time = time.time() - start_time
    print(f"Time to complete: {end_time}s")

    #related_items = relationship_manager.get_related_items(item_id)
    #related_pipes = relationship_manager.get_related_items_by_type(item_id, 'Pipe')

    #print(f"Related items: {related_items}")
    #print(f"Related pipes: {related_pipes}")

    # vector_data_filepath = os.path.join(output_dir, "vector_data.xlsx")
    # df.to_excel(vector_data_filepath)
    #df.to_excel(os.path.join(output_dir, "vector_data_with_weld_maps.xlsx"))
    #regions.to_excel(os.path.join(output_dir, "iso_regions_data.xlsx"))
