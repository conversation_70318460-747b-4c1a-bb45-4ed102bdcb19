CREATE OR REPLACE FUNCTION public.update_categories_from_mapping()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
AS $function$
DECLARE
    component_val VARCHAR(100);
    v_profile_id INTEGER;
    v_rfq_scope VARCHAR(100);
    v_general_category VARCHAR(100);
    changed_column VARCHAR(50);
    mapping_found BOOLEAN := FALSE;
    force_update BOOLEAN := FALSE;
BEGIN
    -- Debug entry point
    RAISE NOTICE 'TRIGGER FIRED: Table=%, Op=%, Time=%', 
        TG_TABLE_NAME, TG_OP, now();
    
    -- Check for force_update flag in the NEW record
    IF TG_OP = 'UPDATE' AND NEW.mapping_not_found = TRUE THEN
        force_update := TRUE;
        RAISE NOTICE 'Force update detected via mapping_not_found flag';
    END IF;
    
    -- Modified condition to allow forced updates
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        
        RAISE NOTICE 'Processing component fields';
        
        -- Reset mapping_not_found flag
        NEW.mapping_not_found := FALSE;
        
        -- Determine which component field to use (prioritize non-null values)
        IF NEW.pipe_category IS NOT NULL THEN
            component_val := NEW.pipe_category;
            changed_column := 'pipe_category';
        ELSIF NEW.fitting_category IS NOT NULL THEN
            component_val := NEW.fitting_category;
            changed_column := 'fitting_category';
        ELSIF NEW.valve_type IS NOT NULL THEN
            component_val := NEW.valve_type;
            changed_column := 'valve_type';
        ELSIF NEW.gasket_category IS NOT NULL THEN
            component_val := NEW.gasket_category;
            changed_column := 'gasket_category';
        ELSIF NEW.bolt_category IS NOT NULL THEN
            component_val := NEW.bolt_category;
            changed_column := 'bolt_category';
        ELSE
            -- No relevant value to map
            RAISE NOTICE 'No component fields with values found';
            RETURN NEW;
        END IF;
        
        RAISE NOTICE 'Using column: %, Value: %', changed_column, component_val;
        
        -- For atem_rfq_input, get profile_id from projects table
        IF TG_TABLE_NAME = 'atem_rfq_input' THEN
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = NEW.project_id;
            
            RAISE NOTICE 'Looking up profile_id for project_id=%: Found profile_id=%', 
                NEW.project_id, v_profile_id;
        ELSE
            v_profile_id := NEW.profile_id;
            RAISE NOTICE 'Using direct profile_id=%', v_profile_id;
        END IF;
        
        -- Look up rfq_scope and general_category from component mapping
        IF v_profile_id IS NOT NULL AND component_val IS NOT NULL THEN
            RAISE NOTICE 'Looking up mapping for profile_id=% and component_name=%', 
                v_profile_id, component_val;
                
            SELECT takeoff_category, general_category INTO v_rfq_scope, v_general_category
            FROM public.atem_bom_component_mapping
            WHERE profile_id = v_profile_id AND component_name = component_val
            LIMIT 1;
            
            RAISE NOTICE 'Mapping lookup result: rfq_scope=%, general_category=%', 
                v_rfq_scope, v_general_category;
            
            -- Update the categories if mapping was found
            IF v_rfq_scope IS NOT NULL OR v_general_category IS NOT NULL THEN
                RAISE NOTICE 'Mapping found! Updating with mapped values';
                
                -- Only update values that were found in the mapping
                IF v_rfq_scope IS NOT NULL THEN
                    NEW.rfq_scope := v_rfq_scope;
                END IF;
                
                IF v_general_category IS NOT NULL THEN
                    NEW.general_category := v_general_category;
                END IF;
                
                mapping_found := TRUE;
            ELSE
                -- No mapping found, set flag
                NEW.mapping_not_found := TRUE;
                RAISE NOTICE 'No mapping found in lookup table. Using defaults for %', changed_column;
                
                -- Apply default values based on the column type
                IF component_val IS NOT NULL THEN
                    CASE
                        WHEN changed_column = 'valve_type' THEN
                            -- Only set if rfq_scope is NULL (don't overwrite existing)
                            IF NEW.rfq_scope IS NULL THEN
                                RAISE NOTICE 'Applying valve_type default: rfq_scope = Valves';
                                NEW.rfq_scope := 'Valves';
                            END IF;
                            
                        WHEN changed_column = 'pipe_category' THEN
                            -- Only set if rfq_scope is NULL (don't overwrite existing)
                            IF NEW.rfq_scope IS NULL THEN
                                RAISE NOTICE 'Applying pipe_category defaults: rfq_scope = Pipe, general_category = LF';
                                NEW.rfq_scope := 'Pipe';
                                NEW.general_category := 'LF';
                            END IF;
                            
                        WHEN changed_column = 'bolt_category' THEN
                            -- Only set if rfq_scope is NULL (don't overwrite existing)
                            IF NEW.rfq_scope IS NULL THEN
                                RAISE NOTICE 'Applying bolt_category default: rfq_scope = Bolts';
                                NEW.rfq_scope := 'Bolts';
                            END IF;
                            
                        WHEN changed_column = 'gasket_category' THEN
                            -- Only set if rfq_scope is NULL (don't overwrite existing)
                            IF NEW.rfq_scope IS NULL THEN
                                RAISE NOTICE 'Applying gasket_category default: rfq_scope = Gaskets';
                                NEW.rfq_scope := 'Gaskets';
                            END IF;
                            
                        ELSE
                            RAISE NOTICE 'No default handling for % column', changed_column;
                    END CASE;
                ELSE
                    RAISE NOTICE 'Skipping defaults since component_val is NULL';
                END IF;
            END IF;
        ELSE
            RAISE NOTICE 'Missing required data: profile_id=%, component_val=%', v_profile_id, component_val;
        END IF;
    ELSE
        RAISE NOTICE 'Operation not supported: %', TG_OP;
    END IF;
    
    RETURN NEW;
END;
$function$