# Piping Material Classification Reference Guide

## Table of Contents

1. [Creating RFQ Templates from Existing Data](#creating-rfq-templates-from-existing-data)
2. [Introduction](#introduction)
3. [RFQ Template Column Definitions](#rfq-template-column-definitions)
4. [Classification Process Overview](#classification-process-overview)
5. [Common Industry Abbreviations](#common-industry-abbreviations)
6. [Classification Categories and Options](#classification-categories-and-options)
7. [Special Rules and Edge Cases](#special-rules-and-edge-cases)
8. [Example Classifications](#example-classifications)

---

## Creating RFQ Templates from Existing Data

Before diving into classification details, you'll often need to create an RFQ template from existing material data. This section explains how to generate a properly formatted Excel template with dropdown validation for efficient classification work

### Overview

The RFQ template system creates Excel files with:

- **47 standardized columns** for complete material specifications
- **Dropdown validation** in classification fields to prevent errors
- **All source data preserved** (no automatic deduplication)
- **Manual deduplication required** for unique material lists

### When to Create RFQ Templates

Create RFQ templates when you need to:

- **Classify new project materials** for the first time
- **Review and correct** existing classifications
- **Standardize material data** from various sources
- **Prepare materials** for vendor quotation requests

### Step-by-Step Process

#### 1. Prepare Your Source Data

Your source data should be in Excel format with at minimum:

- **material_description**: The primary field for material identification
- **quantity**: Number of items (optional but recommended)
- **size**: Item size (optional - will be consolidated anyway)

**Important**: The system will preserve ALL rows from your source data. If you need unique materials, you must deduplicate your source data either after or before. Dedupliation should be done on the `material_description` *only*. Size, size1, size2, quantity is irrelevant for classification.

#### 2. Configure the RFQ Template Script

The RFQ template is generated using `unit_tests/rfq_template.py`. Key configuration options:

```python
# Configuration flags in rfq_template.py
create_template = False      # Creates blank template
load_sample_data = False     # Uses sample data for testing
load_from_excel = True       # Load from existing Excel file
enable_validation = True     # Add dropdown validation

# Specify your source Excel file
excel_file_path = r"path\to\your\source_data.xlsx"

# Output directory (saves outside repository)
output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\[Client]\Workspace"
```

#### 3. Run the Template Generation

Execute the script to generate your RFQ template:

```bash
python unit_tests/rfq_template.py
```

The script will:

1. **Load your source data** from the specified Excel file
2. **Preserve all rows** from your source data (no deduplication)
3. **Add all 47 RFQ columns** with proper formatting
4. **Create dropdown validation** for classification fields
5. **Generate timestamped output** files

#### 4. Template Output Files

The system generates two types of files:

**RFQ_Template_[timestamp].xlsx**:

- Empty template with validation dropdowns
- Use for manual data entry

**RFQ_With_Data_[timestamp].xlsx**:

- Template pre-populated with your source data
- Ready for classification review and completion

### Understanding Dropdown Validation

The RFQ template includes dropdown validation for key classification fields:

#### Primary Classification Dropdowns

- **rfq_scope**: Pipe, Fittings, Flanges, Valves, Bolts, Gaskets, Instruments, Supports, Miscellaneous
- **material**: Steel Carbon, Steel Stainless, Duplex, Bronze/Brass, etc.
- **abbreviated_material**: CS, SS, DSS, CuZn, TI, MON, etc.

#### Technical Specification Dropdowns

- **technical_standard**: B16.9, B16.11, B16.5, API 600, etc.
- **rating**: 150, 300, 600, 800, 1500, 2500, 3000, etc.
- **schedule**: 5, 10, 20, 30, 40, 80, STD, XS, XXS, etc.
- **astm**: A105, A106, A182, A234, A312, A403, etc.
- **grade**: WPB, F304, F316, TP304L, WP316/316L, etc.

#### Category-Specific Dropdowns

- **fitting_category**: 90 LR Elbow, Tee, Reducer Concentric, Cap, etc.
- **valve_type**: Ball Valve, Gate Valve, Check Valve, Globe Valve, etc.
- **bolt_category**: Hex Bolt, Stud Bolts, Cap Screws, etc.
- **gasket_category**: Spiral Wound Gasket, O-Ring, Full-Face Gasket, etc.

### Data Handling Rules

When creating templates from existing data:

#### No Automatic Deduplication

- **Data Preservation**: All rows from source data are preserved
- **No Consolidation**: Duplicate material descriptions remain as separate rows
- **Manual Deduplication**: Users must clean source data before template generation
- **Quantity Handling**: Original quantities are preserved for each row

#### Example Data Handling

**Source Data**:

```
material_description          | size | quantity
2" ELBOW 90 LR A234 WPB      | 2"   | 5
2" ELBOW 90 LR A234 WPB      | 2"   | 3
4" ELBOW 90 LR A234 WPB      | 4"   | 2
```

**Template Result** (All rows preserved):

```
material_description          | size | quantity
2" ELBOW 90 LR A234 WPB      | 2"   | 5
2" ELBOW 90 LR A234 WPB      | 2"   | 3
4" ELBOW 90 LR A234 WPB      | 4"   | 2
```

#### Manual Deduplication Options

If you need unique materials, deduplicate your source data first using:

- **Excel**: Remove Duplicates feature (Data tab)
- **Pandas**: `df.drop_duplicates(subset=['material_description'])`
- **Manual Review**: Identify and consolidate similar descriptions

### Template Validation Features

#### Dropdown Validation Benefits

- **Error Prevention**: Only valid options can be selected
- **Consistency**: Standardized terminology across all classifications
- **Efficiency**: No typing required for classification fields
- **Quality Control**: Reduces classification errors

#### Validation Behavior

- **Allow Invalid Input**: Users can type values not in dropdown (for special cases)
- **No Error Messages**: Invalid entries don't trigger Excel error dialogs
- **Visual Indicators**: Dropdown arrow shows available options
- **Tooltips**: Hover over cells to see validation options

### Best Practices

#### Before Creating Templates

1. **Clean Source Data**: Remove duplicates and formatting issues if unique materials needed
2. **Deduplicate if Required**: Use Excel or other tools to consolidate identical material descriptions
3. **Standardize Descriptions**: Ensure material descriptions are consistent
4. **Verify File Path**: Confirm source Excel file location is correct
5. **Check Output Directory**: Ensure output folder exists and is accessible

#### After Template Creation

1. **Review Data Transfer**: Verify all source data was transferred correctly
2. **Check for Duplicates**: Identify any duplicate material descriptions that need consolidation
3. **Validate Dropdowns**: Test dropdown functionality in Excel
4. **Backup Original**: Keep copy of source data before modifications
5. **Document Changes**: Note any manual adjustments made

#### During Classification

1. **Use Dropdowns**: Always select from dropdown options when available
2. **Flag Unknowns**: Mark items requiring research or special handling
3. **Maintain Consistency**: Follow established classification patterns
4. **Document Decisions**: Note reasoning for unusual classifications

### Troubleshooting Common Issues

#### Template Generation Problems

- **File Not Found**: Verify Excel file path is correct and accessible
- **Permission Errors**: Ensure output directory has write permissions
- **Memory Issues**: Large datasets may require processing in chunks

#### Validation Issues

- **Dropdowns Not Working**: Check Excel version compatibility
- **Missing Options**: Verify categorization_table is up to date
- **Performance Slow**: Large validation lists may impact Excel performance

#### Data Quality Issues

- **Duplicate Materials**: Multiple rows with same material_description need manual consolidation
- **Missing Classifications**: Some fields may require manual completion
- **Inconsistent Formats**: Source data formatting may need cleanup
- **Quantity Discrepancies**: Duplicate materials may have different quantities requiring summation

---

## Introduction

This guide is designed to help employees understand the piping material takeoff classification process. Our system uses AI to initially classify Bill of Materials (BOM) items, but human review is essential to ensure accuracy and catch AI hallucinations. This document explains what each classification field means, common abbreviations you'll encounter, and how to make proper classifications.

**Key Principle**: The AI provides a starting point, but your expertise and understanding of these guidelines ensure accurate classifications that drive proper project estimates.

---

## RFQ Template Column Definitions

The RFQ template contains 47 columns. Here's what each column represents and how it's used in the classification process:

### Core Identification Fields

- **material_description**: The original material description from the takeoff. This is your primary source of information.
- **size**: The nominal size of the item (e.g., "2\"", "4\"", "6\"")
- **quantity**: Number of items required
- **mto_id**: Material takeoff identifier for tracking

### Primary Classification Fields

- **rfq_scope**: Main category of the item (Pipe, Fittings, Flanges, Valves, Bolts, Gaskets, Instruments, Supports, Miscellaneous)
- **general_category**: Specific subcategory within the scope (e.g., "90 Long Radius", "Threaded Valve", "WN Flange")
- **unit_of_measure**: How the item is quantified (EA=Each, LF=Linear Feet, etc.)

### Material Specification Fields

- **material**: Full material description (e.g., "Steel, Carbon", "Steel, Stainless")
- **abbreviated_material**: Short material code (e.g., "CS", "SS", "DSS")
- **astm**: ASTM standard specification (e.g., "A106", "A403", "A182")
- **grade**: Material grade (e.g., "WPB", "WP316/316L", "F304")

### Physical Specification Fields

- **technical_standard**: Applicable code/standard (e.g., "B16.9", "B16.11", "B16.5")
- **rating**: Pressure rating (e.g., "150", "300", "600", "1500")
- **schedule**: Wall thickness designation (e.g., "40", "80", "10S", "STD")
- **ends**: End connection types (e.g., "BE", "SW", "RF", "TE")
- **forging**: Manufacturing process (e.g., "Seamless", "Welded", "Forged")
- **coating**: Surface treatment if specified

### Category-Specific Fields

- **pipe_category**: Specific pipe type (Pipe, Tube, Hose, etc.)
- **valve_type**: Specific valve type (Ball Valve, Gate Valve, Check Valve, etc.)
- **fitting_category**: Specific fitting type (90 LR Elbow, Tee, Reducer Concentric, etc.)
- **bolt_category**: Specific bolt type (Hex Bolt, Stud Bolts, etc.)
- **gasket_category**: Specific gasket type (Spiral Wound Gasket, O-Ring, etc.)

### Reference and Tracking Fields

- **component_category**: Informational field used for reference in classification
- **item_tag**: Item tag number if specified
- **tie_point**: Connection point reference

### Quality Control Fields

- **Review**: Boolean flag indicating if item needs human review
- **Category Check**: Validation status for category assignment
- **Rating Check**: Validation status for rating assignment
- **SCH CHECK**: Validation status for schedule assignment
- **ASTM Check**: Validation status for ASTM assignment
- **Grade Check**: Validation status for grade assignment
- **Tag Check**: Validation status for tag assignment

### Processing Fields

- **normalized_description**: Cleaned and standardized description
- **metadata_tags**: Extracted technical information tags
- **review_tags**: Flags for items requiring attention
- **match_type**: How the classification was determined
- **exists_in_verified_materials**: Whether item exists in verified database
- **option_review**: Items with questionable option selections
- **invalid_options**: Items with invalid classification options

### Unused Fields

- **size1**: Not currently used in classification
- **size2**: Not currently used in classification
- **ef**: Not currently used
- **sf**: Not currently used
- **last_updated**: Timestamp field
- **__checked__**: Internal processing flag
- **__checked_2__**: Internal processing flag

---

## Classification Process Overview

The classification process follows these steps:

1. **AI Initial Classification**: The system uses a 4-stage LangGraph process:

   - Stage 1: Material Analysis (extracts technical properties)
   - Stage 2: Category-Specific Classification
   - Stage 3: Q&A Decision Making
   - Stage 4: Self-Audit Validation
2. **Human Review**: You review AI classifications, focusing on:

   - Items flagged for review
   - Unusual or complex descriptions
   - Cross-validation of related fields
3. **Validation**: Ensure classifications match available options and make technical sense

---

## Common Industry Abbreviations

Understanding these abbreviations is crucial for accurate classification:

### End Types

- **BE**: Beveled End
- **PE**: Plain End
- **SW**: Socket Weld
- **TE**: Threaded End
- **RF**: Raised Face
- **FF**: Flat Face
- **RTJ**: Ring Type Joint
- **FLG**: Flanged

### Flange Types

- **WN**: Weld Neck
- **SO**: Slip On
- **SW**: Socket Weld
- **THRD**: Threaded
- **LJ**: Lap Joint
- **BL**: Blind

### Schedule Designations

- **SCH**: Schedule (e.g., SCH 40, SCH 80, S-, WT, Wall Thickness)
- **STD**: Standard Weight
- **XS**: Extra Strong
- **XXS**: Double Extra Strong
- **XH**: Extra Heavy
- **XXH**: Double Extra Heavy
- **S**: Stainless designation (e.g., 10S, 40S)
- **Wall Thickness**: Wall thickness (e.g., 0.125", 0.250")
- ** XH/XS, XXH/XXS** are interchangeable and refer to the same thing

### Material Abbreviations

- **CS**: Carbon Steel
- **SS**: Stainless Steel
- **DSS**: Duplex Stainless Steel
- **CuZn**: Bronze/Brass
- **TI**: Titanium
- **MON**: Monel
- **Inconel**: Inconel
- **HAST**: Hastelloy

### Manufacturing Processes

- **SMLS**: Seamless
- **WLD**: Welded
- **ERW**: Electric Resistance Welded
- **EFW**: Electric Fusion Welded
- **FRGD**: Forged

### Pressure Ratings

- **#**: Pound rating (e.g., 150#, 300#)
- **LB**: Pound rating (e.g., 150LB)
- **PSI**: Pounds per square inch
- **PSIG**: Pounds per square inch gauge

---

## Classification Categories and Options

### RFQ Scope Options

The main categories for material classification:

- **Pipe**: All pipe products including tubes and hoses
- **Fittings**: Elbows, tees, reducers, caps, etc.
- **Flanges**: All flange types and related items
- **Valves**: All valve types and actuators
- **Bolts**: Bolts, nuts, washers, and fasteners
- **Gaskets**: All sealing materials
- **Instruments**: Gauges, transmitters, etc.
- **Supports**: Pipe supports, hangers, guides
- **Miscellaneous**: Items that don't fit other categories

### Material Types

Primary material classifications:

- **Steel, Carbon**: Standard carbon steel
- **Steel, Stainless**: 300 and 400 series stainless
- **Steel, Stainless (Lined)**: Lined stainless steel
- **Duplex**: Duplex and super duplex stainless
- **Bronze/Brass**: Copper alloys
- **Titanium**: Titanium alloys
- **Monel**: Nickel-copper alloys
- **Inconel**: Nickel-chromium alloys
- **Hastelloy**: Nickel-molybdenum alloys

---

## Special Rules and Edge Cases

### Schedule Classification Rules

**Critical**: There is a major difference between "SCH10" and "SCH10S"

- **SCH10**: Standard schedule 10
- **SCH10S**: Stainless steel schedule 10 (thinner wall)
- Always check for the "S" suffix in stainless steel applications
- Cross schedules: "SCH 10S X SCH 40S" indicates different schedules on each end

### ASTM vs Technical Standards

- **ASTM**: Material specifications (A106, A403, A182)
- **ASME/ANSI**: Dimensional standards (B16.9, B16.11, B16.5)
- Don't confuse "B16.11" (ASME standard) with "A16" (not a valid ASTM)

### Rating Extraction Rules

- Look for: 150#, 300LB, 600 PSI, #1500, CL 3000
- Exclude: S-160 (schedule), 160°F (temperature), Part #3000
- Context matters: "SPRING CAN SIZE 100" ≠ "CLASS 100"

### Grade Handling

- **Dual Grades**: "316/316L" or "WP316/316L"
- **Suffixed Grades**: "WPB-S", "F304-W"
- **GR Prefix**: "GR. B", "GRADE F316"
- Avoid word fragments: "GR" from "GROOVED" is not a grade

### End Type Combinations

- **Single**: BE, PE, SW, TE, RF, FF
- **Combinations**: BE X PE, SW X TE, RF X FF
- **Flanged items**: RF and FF typically indicate flange face types

### Valve Classification by End Types

For items classified as "Valves":

- **TE** (Threaded) → "Threaded Valve"
- **FLG** (Flanged) → "Flanged Valve"
- **BE, SW, PE** → "Welded Valve"
- **No end type** → Leave blank for human review

### Manufacturing Type Identification

Manufacturing type (forging field) can be determined through several indicators:

#### 1. Explicit Manufacturing Indicators

Look for these abbreviations in the description:

- **SMLS**: Seamless → "(SMLS) Seamless"
- **ERW**: Electric Resistance Welded → "(ERW) Electric Resistance Welding"
- **EFW**: Electric Fusion Welded → "(EFW) Electric Fusion Welding"
- **HFW**: High-Frequency Welded → "(HFW) High-Frequency Welding"
- **FRGD/Forged**: Forged → "Forged"
- **WLD/Welded**: Welded → "Welded"
- **THRD/Threaded**: Threaded → "Threaded"

#### 2. ASTM Standard and Grade Combinations

Certain ASTM/grade combinations indicate specific manufacturing types:

**Seamless Pipe Standards**:

- A106 (all grades) → Seamless
- A312 TP grades → Seamless
- A333 grades → Seamless

**Welded Pipe Standards**:

- A53 Type E → ERW
- A53 Type F → Furnace Welded
- A358 grades → Welded

**Forged Material Standards**:

- A105 → Forged (flanges, fittings)
- A182 → Forged (flanges, fittings)
- A350 → Forged (low temperature)

#### 3. Grade Prefix Indicators

- **F-prefix grades**: F304, F316, F321, F347 → "Forged"
  - **This is the ONLY reliable grade indicator for manufacturing type**
  - F-prefix always indicates forged manufacturing
  - Common in flange and fitting applications
- **TP-prefix grades**: TP304, TP316, TP321 → **Does NOT indicate manufacturing type**
- **WP-prefix grades**: WPB, WP304, WP316 → **Does NOT indicate manufacturing type**

**Important**: Only F-prefix grades reliably indicate manufacturing. TP and WP prefixes do not determine forging type.

#### 4. Item Type Implications

- **Flanges**: Typically forged (A105, A182 standards)
- **Fittings**: Can be forged (A182) or welded (A234, A403)
- **Pipe**: Can be seamless (A106, A312) or welded (A53, A358)
- **Valves**: Body typically forged or cast

### Context-Dependent Abbreviations

Some abbreviations have different meanings depending on context:

#### SWG - Multiple Meanings

- **Gasket Context**: "SWG" = Spiral Wound Gasket
  - Example: "2\" 150# SWG" → gasket_category: "Spiral Wound Gasket"
- **Fitting Context**: "SWG" = Swage (reducing fitting)
  - Example: "4\" X 2\" SWG NIPPLE" → fitting_category: "Swage Nipple"

#### SW - Multiple Meanings

- **End Type**: "SW" = Socket Weld
  - Example: "90° ELBOW SW" → ends: "SW"
- **Flange Type**: "SW FLANGE" = Socket Weld Flange
  - Example: "2\" SW FLANGE" → fitting_category: "SW Flange RF"

#### RF - Multiple Meanings

- **Flange Face**: "RF" = Raised Face
  - Example: "WN FLANGE RF" → fitting_category: "WN Flange RF"
- **End Connection**: "RF" = Raised Face connection
  - Example: "VALVE RF ENDS" → ends: "RF"

#### RED/REDU - Multiple Meanings

- **Fitting Type**: "RED" = Reducer
  - Example: "CON RED 4\" X 2\"" → fitting_category: "Reducer Concentric"
- **Valve Type**: "RED" = Reduced (port/bore)
  - Example: "BALL VALVE RED PORT" → Special feature, not fitting type

### Manufacturing Type Decision Matrix

**CRITICAL**: Only use this matrix when explicit manufacturing indicators are present in the description or when F-prefix grades are identified.

| Explicit Indicator                | Manufacturing Type                |
| --------------------------------- | --------------------------------- |
| SMLS, Seamless                    | (SMLS) Seamless                   |
| ERW                               | (ERW) Electric Resistance Welding |
| EFW                               | (EFW) Electric Fusion Welding     |
| HFW                               | (HFW) High-Frequency Welding      |
| FRGD, Forged                      | Forged                            |
| WLD, Welded                       | Welded                            |
| F-prefix grade (F304, F316, etc.) | Forged                            |

**Important**: ASTM standards and TP/WP grade prefixes alone do NOT reliably indicate manufacturing type. Only classify manufacturing when you have explicit indicators.

### Special Manufacturing Notes

#### Dual Manufacturing Designations

Some items may have multiple manufacturing processes:

- "PIPE A53 GR B ERW/SMLS" → Both processes acceptable
- "FITTING A234 WPB SMLS" → Seamless fitting (less common)

#### Cast vs Forged

- **Cast**: A216, A217, A351, A352 (valve bodies)
- **Forged**: A105, A182, A350 (flanges, small fittings)
- **Wrought**: A240 (plate), A479 (bar)

#### Stainless Steel Considerations

- Stainless fittings can be forged (A182) or welded (A403)
- Grade prefix helps: F316 (forged) vs WP316 (welded)
- TP grades typically indicate seamless pipe

### Additional Context-Dependent Abbreviations

#### CAP - Multiple Meanings

- **Fitting Context**: "CAP" = Pipe Cap
  - Example: "2\" CAP A234 WPB" → fitting_category: "Cap"
- **Bolt Context**: "CAP" = Cap Screw
  - Example: "M16 CAP SCREW" → bolt_category: "Cap Screws"

#### UNION - Multiple Meanings

- **Fitting Context**: "UNION" = Union Fitting
  - Example: "1\" UNION SW" → fitting_category: "Union"
- **Coupling Context**: Sometimes refers to coupling
  - Example: "UNION COUPLING" → fitting_category: "Coupling"

#### CROSS - Multiple Meanings

- **Fitting Type**: "CROSS" = Cross Fitting (4-way)
  - Example: "2\" CROSS A234 WPB" → fitting_category: "Cross"
- **End Configuration**: "CROSS" = Crossing pattern
  - Context determines if it's a fitting type or connection description

#### TEE/T - Variations

- **Standard**: "TEE" = Tee Fitting
- **Equal**: "EQ TEE" = Equal Tee
- **Reducing**: "RED TEE" = Reducing Tee
- **Branch**: "BRANCH TEE" = Same as reducing tee
- **Outlet**: "TEE OUTLET" = Threadolet/Weldolet type

### Manufacturing Identification Tips

#### When Manufacturing is Ambiguous

1. **Check ASTM first** - Most reliable indicator
2. **Look for explicit callouts** - SMLS, ERW, FRGD
3. **Examine grade prefix** - F=Forged, TP=Seamless, WP=Welded
4. **Consider item type** - Flanges usually forged, pipe usually seamless/welded
5. **Flag for review** if multiple indicators conflict

#### Common Manufacturing Mistakes

❌ **Wrong**: Assuming TP304 means seamless manufacturing
✅ **Right**: TP304 grade does not indicate manufacturing - look for explicit indicators

❌ **Wrong**: Assuming WPB means welded manufacturing
✅ **Right**: WPB grade does not indicate manufacturing - look for explicit indicators

❌ **Wrong**: F316 as "Welded" (F-prefix always forged)
✅ **Right**: F316 as "Forged" (F-prefix is reliable indicator)

❌ **Wrong**: Guessing manufacturing type from ASTM standard alone
✅ **Right**: Only classify when explicit indicators are present

#### Size-Dependent Manufacturing

- **Small fittings** (≤2"): Often forged (A182)
- **Large fittings** (>2"): Often welded (A234, A403)
- **Small pipe** (≤2"): Can be seamless or welded
- **Large pipe** (>12"): Usually welded due to manufacturing limitations

### Abbreviation Disambiguation Strategy

When encountering ambiguous abbreviations:

1. **Read the full context** - surrounding words provide clues
2. **Check item scope** - is it pipe, fitting, valve, gasket?
3. **Look for size indicators** - helps determine item type
4. **Consider technical standards** - B16.9 (fittings) vs B16.5 (flanges)
5. **Use process of elimination** - what makes technical sense?

#### Example Disambiguation

**Description**: "2\" SWG 150# A105"

**Analysis**:

- Size: 2" (could be gasket or fitting)
- Rating: 150# (suggests pressure component, not gasket)
- Material: A105 (forged carbon steel, used for flanges/fittings)
- **Conclusion**: SWG = Swage (fitting), not Spiral Wound Gasket

**Description**: "4\" 300# SWG METALLIC"

**Analysis**:

- Size: 4" (gasket size)
- Rating: 300# (flange rating)
- Context: "METALLIC" (gasket material)
- **Conclusion**: SWG = Spiral Wound Gasket

### Quality Indicators for Manufacturing Classification

#### High Confidence Indicators

- Explicit manufacturing callouts (SMLS, ERW, FRGD)
- F-prefix grades (always forged)
- Standard ASTM/grade combinations (A106/B = seamless)

#### Medium Confidence Indicators

- Item type implications (flanges usually forged)
- Size considerations (small fittings often forged)
- TP/WP grade prefixes

#### Low Confidence Indicators

- Ambiguous descriptions without clear manufacturing callouts
- Non-standard ASTM/grade combinations
- Missing or unclear technical specifications

**Rule**: When confidence is low, flag for human review rather than guessing.

---

## Common AI Classification Issues

The AI classifier, while powerful, has predictable patterns of errors. Understanding these helps you focus your review efforts:

### Frequent AI Misclassifications

#### 1. Nipple vs Pipe Confusion

**Issue**: AI often classifies "Nipple" as "Pipe" instead of "Fittings"

- **Wrong AI Classification**: "2\" NIPPLE SCH 40" → rfq_scope: "Pipe"
- **Correct Classification**: "2\" NIPPLE SCH 40" → rfq_scope: "Fittings", fitting_category: "Nipple"

**Rule**: Nipples are ALWAYS fittings, never pipe. Look for keywords:

- "NIPPLE", "NPL", "NIP"
- "CLOSE NIPPLE", "LONG NIPPLE", "SHORT NIPPLE"
- "SWAGE NIPPLE", "HEX NIPPLE"

#### 2. Part Number Hallucinations

**Issue**: AI classifies part numbers without context as random fittings

**Examples of Part Numbers Requiring Review**:

- "P-1234-567" → AI might classify as "Tee" or "Elbow" (wrong)
- "ITEM-ABC-123" → AI might assign random fitting category (wrong)
- "REF: DWG-456" → AI might classify as component (wrong)
- "SEE SPEC XYZ-789" → AI might assign material properties (wrong)

**Red Flags for Part Numbers**:

- Alphanumeric codes without descriptive text
- "REF:", "SEE:", "ITEM:", "P/N:", "PART NO:"
- Drawing references: "DWG", "DRAWING", "SPEC"
- Generic identifiers without technical content

**Action**: Flag all part-number-only descriptions for human review.

#### 3. Support Hardware Misclassification

**Issue**: AI classifies pipe supports as fittings or pipe

**Examples**:

- "PIPE ANCHOR" → AI might classify as "Fittings" (wrong)
- "PIPE SHOE" → AI might classify as "Pipe" (wrong)
- "HANGER ROD" → AI might classify as "Bolts" (wrong)

**Correct Classification**: All should be rfq_scope: "Supports"

#### 4. Instrument Connection Confusion

**Issue**: AI misclassifies instrument connections and small bore tubing

**Examples**:

- "1/4\" INSTRUMENT TUBING" → AI might classify as "Pipe" (could be correct)
- "PRESSURE GAUGE CONNECTION" → AI might classify as "Fittings" (review needed)
- "THERMOWELL" → AI might classify as "Instruments" or "Fittings" (context dependent)

### Items That Always Require Human Review

#### 1. Ambiguous Descriptions

- Descriptions with multiple possible interpretations
- Missing critical technical information
- Conflicting specifications within the description

#### 2. Non-Standard Items

- Custom fabricated components
- Specialty alloys not in standard lists
- Non-standard pressure ratings or schedules
- Proprietary or vendor-specific items

#### 3. Complex Assemblies

- Multi-component assemblies
- Items with multiple materials or specifications
- Kits or packages with various components

#### 4. Incomplete Information

- Descriptions missing size, material, or rating
- Partial specifications requiring engineering judgment
- Items requiring additional documentation reference

### AI Confidence Indicators

#### High Confidence (Usually Accurate)

- Standard pipe descriptions with complete specifications
- Common fittings with standard ASTM/grade combinations
- Flanges with clear type and rating information
- Standard bolts with complete specifications

#### Medium Confidence (Review Recommended)

- Items with unusual size/rating combinations
- Non-standard material specifications
- Items with multiple possible classifications
- Descriptions with ambiguous abbreviations

#### Low Confidence (Always Review)

- Part numbers without descriptive text
- Incomplete or partial descriptions
- Non-standard or specialty items
- Items flagged by AI for review

### Review Priority Guidelines

#### Immediate Review Required

1. **Part numbers only** - No descriptive content
2. **Nipples classified as Pipe** - Always fittings
3. **Support items** - Often misclassified
4. **Items flagged by AI** - System identified issues
5. **Unusual material combinations** - Non-standard specs

#### Secondary Review Priority

1. **Complex assemblies** - Multiple components
2. **Instrument connections** - Context dependent
3. **Large diameter items** - Often have special requirements
4. **High-pressure items** - Critical for safety

#### Lower Priority (Spot Check)

1. **Standard pipe** - Usually classified correctly
2. **Common fittings** - Well-understood patterns
3. **Standard flanges** - Clear classification rules
4. **Standard bolts** - Straightforward specifications

### Quality Control Checkpoints

When reviewing AI classifications, systematically check:

1. **Scope Accuracy**: Is the main category (Pipe, Fittings, etc.) correct?
2. **Nipple Check**: Are any nipples misclassified as pipe?
3. **Part Number Flag**: Are part numbers classified without context?
4. **Manufacturing Logic**: Are F-prefix grades marked as forged?
5. **Option Validation**: Are all selections from valid dropdown lists?
6. **Technical Consistency**: Do ASTM, grade, and material align?
7. **Engineering Sense**: Does the overall classification make sense?

---

## Example Classifications

### Example 1: Pipe Description

**Description**: "Pipe, 150lb, sch 10s, pe x be, seamless astm 106b"

**Classification Breakdown**:

- **Keywords Identified**:
  - "Pipe" → rfq_scope: "Pipe"
  - "150lb" → rating: "150"
  - "sch 10s" → schedule: "10S" (note the S suffix)
  - "pe x be" → ends: "PE X BE"
  - "seamless" → forging: "(SMLS) Seamless"
  - "astm 106b" → astm: "A106", grade: "B"

**Final Classification**:

- rfq_scope: "Pipe"
- rating: "150"
- schedule: "10S"
- ends: "PE X BE"
- forging: "(SMLS) Seamless"
- astm: "A106"
- grade: "B"
- material: "Steel, Carbon"
- abbreviated_material: "CS"

### Example 2: Flange Description

**Description**: "SO Flange RF 300# A105 4\""

**Classification Breakdown**:

- **Keywords Identified**:
  - "SO Flange" → rfq_scope: "Flanges", fitting_category: "SO Flange RF"
  - "RF" → ends: "RF" (Raised Face)
  - "300#" → rating: "300"
  - "A105" → astm: "A105"
  - "4\"" → size: "4\""

**Final Classification**:

- rfq_scope: "Flanges"
- fitting_category: "SO Flange RF"
- ends: "RF"
- rating: "300"
- astm: "A105"
- material: "Steel, Carbon"
- abbreviated_material: "CS"

### Example 3: Valve Description

**Description**: "Ball Valve 800# A105 SW 2\""

**Classification Breakdown**:

- **Keywords Identified**:
  - "Ball Valve" → rfq_scope: "Valves", valve_type: "Ball Valve"
  - "800#" → rating: "800"
  - "A105" → astm: "A105"
  - "SW" → ends: "SW"
  - "2\"" → size: "2\""

**Final Classification**:

- rfq_scope: "Valves"
- valve_type: "Ball Valve"
- general_category: "Welded Valve" (because ends: "SW")
- rating: "800"
- astm: "A105"
- ends: "SW"
- material: "Steel, Carbon"
- abbreviated_material: "CS"

### Example 4: Complex Fitting Description

**Description**: "90 LR Elbow A234 WPB SCH 40 BE B16.9"

**Classification Breakdown**:

- **Keywords Identified**:
  - "90 LR Elbow" → rfq_scope: "Fittings", fitting_category: "90 LR Elbow"
  - "A234" → astm: "A234"
  - "WPB" → grade: "WPB"
  - "SCH 40" → schedule: "40"
  - "BE" → ends: "BE"
  - "B16.9" → technical_standard: "B16.9"

**Final Classification**:

- rfq_scope: "Fittings"
- fitting_category: "90 LR Elbow"
- general_category: "90 Long Radius"
- astm: "A234"
- grade: "WPB"
- schedule: "40"
- ends: "BE"
- technical_standard: "B16.9"
- material: "Steel, Carbon"
- abbreviated_material: "CS"

---

## Common Mistakes to Avoid

### 1. Schedule Confusion

❌ **Wrong**: Classifying "S-160" as schedule: "160"
✅ **Right**: "S-160" in context of supports is not a schedule

### 2. Rating vs Schedule Confusion

❌ **Wrong**: "CL 160" as rating: "160" (160 is not a valid rating)
✅ **Right**: Check valid rating list: 150, 300, 600, 800, 1500, etc.

### 3. ASTM vs ASME Confusion

❌ **Wrong**: "B16.11" as astm: "B16"
✅ **Right**: "B16.11" as technical_standard: "B16.11"

### 4. Grade Word Fragments

❌ **Wrong**: "GROOVED" extracting grade: "OOVED"
✅ **Right**: Recognize "GROOVED" as a word, not containing a grade

### 5. End Type Misclassification

❌ **Wrong**: "RF Flange" with ends: "RF" and separate flange classification
✅ **Right**: "RF" is the flange face type, part of the flange description

---

## Review Checklist

When reviewing AI classifications, check:

### Technical Consistency

- [ ] Do the ASTM and grade match? (e.g., A234 with WPB grade)
- [ ] Is the schedule appropriate for the material? (10S for stainless)
- [ ] Does the rating make sense for the application?
- [ ] Are end types compatible with the item type?

### Option Validation

- [ ] Are all selected options from the valid dropdown lists?
- [ ] Do category-specific fields match the main scope?
- [ ] Are material and abbreviated_material consistent?

### Description Analysis

- [ ] Have all technical specifications been extracted?
- [ ] Are there any missed abbreviations or specifications?
- [ ] Does the classification make engineering sense?

### Flag for Review When

- [ ] Multiple valid interpretations exist
- [ ] Unusual or non-standard specifications
- [ ] Conflicting information in description
- [ ] Missing critical information
- [ ] AI confidence appears low

---

## Quick Reference Tables

### Valid Ratings

150, 300, 600, 800, 1500, 2500, 3000, 4000, 5000, 6000, 9000, 10000

### Common Schedule Values

5, 10, 20, 30, 40, 60, 80, 100, 120, 140, 160, STD, XS, XXS, XH, XXH
(Add "S" suffix for stainless: 5S, 10S, 40S, 80S, etc.)

### Primary ASTM Standards

- **Pipe**: A53, A106, A312, A358
- **Fittings**: A234, A403, A815
- **Flanges**: A105, A182, A350
- **Bolts**: A193, A194, A320
- **Valves**: A216, A217, A351, A352

This reference guide provides the foundation for accurate piping material classification. When in doubt, flag items for review rather than guessing. Consistent, accurate classifications are essential for proper project estimates and material procurement.
