import os
import json
import asyncio
from typing import Optional, Dict, Any, List
from mimetypes import guess_type
from google import genai
from google.genai import types


class GeminiOCR:
    """Gemini OCR using genai.Client with retries, multi-image, and async support."""

    def __init__(self, api_key: Optional[str] = None, max_retries: int = 3):
        self.api_key = api_key or os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("Gemini API key is required.")
        self.client = genai.Client(api_key=self.api_key)
        self.model = "gemini-2.5-flash"
        self.max_retries = max_retries

    def encode_image(self, image_path: str) -> types.Part:
        try:
            with open(image_path, "rb") as f:
                data = f.read()
            mime_type, _ = guess_type(image_path)
            return types.Part.from_bytes(data=data, mime_type=mime_type or "image/jpeg")
        except Exception as e:
            raise ValueError(f"Failed to encode image '{image_path}': {e}")

    def _process_images_sync(self, image_paths: List[str], prompt: str) -> Dict[str, Any]:
        try:
            image_parts = [self.encode_image(path) for path in image_paths]
        except Exception as e:
            return {"success": False, "error": str(e)}

        contents = [
            types.Content(
                role="user",
                parts=image_parts + [types.Part.from_text(text=prompt)]
            )
        ]

        config = types.GenerateContentConfig(response_mime_type="text/plain")

        last_error = ""
        for attempt in range(1, self.max_retries + 1):
            try:
                response = self.client.models.generate_content(
                    model=self.model,
                    contents=contents,
                    config=config
                )
                raw_text = response.text.strip()

                if not raw_text:
                    return {"success": False, "error": "Gemini returned empty text."}

                # Try to extract JSON
                try:
                    if "```json" in raw_text:
                        json_start = raw_text.find("```json") + 7
                        json_end = raw_text.rfind("```")
                        json_str = raw_text[json_start:json_end].strip()
                        parsed = json.loads(json_str)
                        return {"success": True, "data": parsed}
                    elif raw_text.startswith("{") or raw_text.startswith("["):
                        parsed = json.loads(raw_text)
                        return {"success": True, "data": parsed}
                    else:
                        return {"success": True, "text": raw_text}
                except json.JSONDecodeError:
                    return {"success": True, "text": raw_text}

            except Exception as e:
                last_error = str(e)
                if attempt < self.max_retries:
                    wait = 2 ** attempt
                    asyncio.run(asyncio.sleep(wait))  # Blocking sleep for sync context
                else:
                    return {
                        "success": False,
                        "error": f"Failed after {self.max_retries} retries. Last error: {last_error}"
                    }

    async def process_images_async(self, image_paths: List[str], prompt: str) -> Dict[str, Any]:
        return await asyncio.to_thread(self._process_images_sync, image_paths, prompt)

component_category = ""
BOM_PROMPT = f"""
    **Objective:**
    Extract tabular data and return it as a structured JSON array of objects. Each object in the array should represent a single row from the input table.

    **Output Structure and Column Mapping:**
    Each JSON object must contain the following keys, with their values extracted from the corresponding table columns:
    * `"pos"`: From the first column of the table (may be labeled 'MARK','MK', 'ID', 'POS', or 'ITEM' in the source). This column's value signals the beginning of a new data row.
    * `"quantity"`: From the quantity column (may be labeled 'QTY' or 'QUANTITY' in the source).
    * `"size"`: From the size column (may be labeled 'NPD', 'ND', 'NS', or 'SIZE' in the source).
    * `"material_description"`: From the description column
    {component_category}

    **Data Handling and Formatting Instructions:**

    1.  **New Row Detection:** A new data row begins when there is content in the far-left column (designated as 'MK', 'ID', 'POS', or 'ITEM').
    2.  **Wrapped Rows:** Carefully and accurately combine text that wraps across multiple lines within a single cell into a single string for its respective field in the JSON output. Ensure correct spacing when concatenating wrapped lines.
    3.  **Red Text (Revisions):**
        * Text styled in red indicates a revised value. This red text should be extracted as the **current and definitive** value for the field it appears in or is closest to.
        * If red text modifies only a portion of a cell's content (e.g., a single word or number), use the red text to replace the original segment it revises.
        * If an entire cell's value is red, that red text is the complete and current value for that field.
        * Prioritize red text as the correct data when present.
    4.  **Strikethrough Text (Voided Items):**
        * If any text within a 'DESCRIPTION' cell is formatted with a strikethrough, or if an entire row appears to be voided via prominent strikethrough formatting across multiple cells for that row:
            * Prefix the *entire content* of the 'DESCRIPTION' field for that specific item with the string: `"<ITEM VOIDED> "`.
            * The original strikethrough text should still be included after this prefix.
    5.  **Data Integrity:**
        * Preserve all original characters, symbols, and formatting within the data values as accurately as possible, except where overridden by rules for red text or strikethrough. This includes units (e.g., ', ", FT, EA), special characters, and case sensitivity.
        * Handle measurements and mixed alphanumeric strings (e.g., `2"X1"`, `59'-9"`) correctly.
    6.  **Empty or Missing Values:** If a cell in the input table corresponding to QTY, SIZE, or DESCRIPTION, ITEM CODE is empty, represent its value as an empty string (`""`) or `null` in the output JSON. The 'MK' field should always be present for a valid row.
"""

def build_bom_prompts():
    pass


async def main():
    key = "AIzaSyCGl6nc3-XZXf_2DNaafrtDrM0B6WCejsk"
    ocr = GeminiOCR(api_key=key)
    image = r"C:\Drawings\Clients\axisindustries\Axis 2025-08-07 - Bluebonnet field route Isos\Bluebonnet field route Isos - output images\bom\table_bom_page_1.png"
    result = await ocr.process_images_async([image], BOM_PROMPT)
    print(result)

asyncio.run(main())
