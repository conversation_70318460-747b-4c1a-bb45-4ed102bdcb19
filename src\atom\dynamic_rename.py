import sys, os, shutil
# from PyQt5.QtWidgets import QA<PERSON>lication, QMain<PERSON>indow, QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QComboBox, QLabel, QLineEdit
# from PyQt5.QtCore import pyqtSignal  # Import pyqtSignal
from PySide6.QtWidgets import QApplication, Q<PERSON>ainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QComboBox, QLabel, QLineEdit
from PySide6.QtCore import Signal  # Import Signal
import pandas as pd


class DynamicRowsWidget(QWidget):
    renamingDone = Signal(pd.DataFrame)  # Define the signal here    

    def __init__(self, dataframe, directory, parent=None):
        super().__init__(parent)
        self.dataframe = dataframe
        self.directory = directory
        self.columns = dataframe.columns.tolist()
        self.rows = []
        self.initUI()

    def initUI(self):
        self.mainLayout = QVBoxLayout()
        self.rowsLayout = QVBoxLayout()

        self.addRow()
        self.mainLayout.addLayout(self.rowsLayout)
        
        #Add Preview Label
        self.previewLabel = QLabel(str(""))

        # Add Preview Button
        self.previewButton = QPushButton("Preview")
        self.previewButton.clicked.connect(self.previewOutput)
        
        # Add Rename Button
        self.renameButton = QPushButton("Rename Files")
        self.renameButton.clicked.connect(self.renameFiles)
        
        self.mainLayout.addWidget(self.previewLabel)
        self.mainLayout.addWidget(self.previewButton)
        self.mainLayout.addWidget(self.renameButton)

        self.setLayout(self.mainLayout)

    def addRow(self):
        row_number = len(self.rows) + 1
        row = RowWidget(self.columns, row_number, self)
        row.removeButton.clicked.connect(lambda: self.removeRow(row))
        row.addButton.clicked.connect(self.addRow)
        self.rowsLayout.addWidget(row)
        self.rows.append(row)

    def removeRow(self, row):
        if len(self.rows) > 1:  # Prevent removing the last row
            self.rows.remove(row)
            row.deleteLater()
            self.updateRowNumbers()

    def updateRowNumbers(self):
        for i, row in enumerate(self.rows):
            row.label.setText(str(i + 1))
            
    def previewOutput(self):
        final_output = ""
        if not self.dataframe.empty:
            first_row = self.dataframe.iloc[0]  # Get the first row of the DataFrame
            for row in self.rows:
                combo_value = row.comboBox.currentText().strip()  # Trim the combo value
                delimiter = row.delimiterEdit.text().strip()  # Trim the delimiter

                if combo_value in self.dataframe.columns:
                    # If combo_value is a column name, use the value from that column
                    column_value = first_row[combo_value]
                    final_output += str(column_value).strip() + (delimiter if delimiter else "")  # Trim column value
                else:
                    # If combo_value is not a column name, use it directly
                    final_output += combo_value + (delimiter if delimiter else "")
        final_output += ".pdf"
        
        self.previewLabel.setText(final_output)  # Display in preview label
        
    def renameFiles(self):
        if not self.dataframe.empty and os.path.exists(self.directory):
            self.dataframe['AIS_LINK'] = ""  # Initialize the new column

            for index, row in self.dataframe.iterrows():
                base_new_filename = self.buildFileName(index)
                old_file_path = os.path.join(self.directory, str(row['sys_AISdocID']) + '.pdf')
                new_file_path = os.path.join(self.directory, base_new_filename)

                # Check if the new file name already exists and modify it if necessary
                file_extension = '.pdf'  # Assuming the file extension is always .pdf
                counter = 1
                while os.path.exists(new_file_path):
                    # Append a number to the base filename
                    new_filename = f"{os.path.splitext(base_new_filename)[0]}({counter}){file_extension}"
                    new_file_path = os.path.join(self.directory, new_filename)
                    counter += 1

                # Rename the file
                if os.path.exists(old_file_path):
                    shutil.move(old_file_path, new_file_path)
                    logger.info(f"Renamed {old_file_path} to {new_file_path}")

                # Update the DataFrame with the relative path from the workbook's directory
                # Assuming the parent of `self.directory` is the workbook's directory
                workbook_dir = os.path.dirname(self.directory)
                relative_path = os.path.relpath(new_file_path, workbook_dir)
                #self.dataframe.at[index, 'AIS_LINK'] = relative_path
                # Update the DataFrame with just the filename (relative to 'docs')
                #self.dataframe.at[index, 'AIS_LINK'] = os.path.basename(new_file_path)
                self.dataframe.at[index, 'AIS_LINK'] = 'docs/' + os.path.basename(new_file_path)

            # Emit the signal with the modified DataFrame
            self.renamingDone.emit(self.dataframe)

            # Close the main window
            self.parent().close()  # Assuming the parent of DynamicRowsWidget is the main window
            
    def buildFileName(self, row_index):
        final_output = ""
        for row in self.rows:
            combo_value = row.comboBox.currentText().strip()
            delimiter = row.delimiterEdit.text().strip()

            if combo_value in self.dataframe.columns:
                # Use the value from the DataFrame's column
                column_value = self.dataframe.at[row_index, combo_value]
                final_output += str(column_value).strip() + (delimiter if delimiter else "")
            else:
                # Use the value directly from the ComboBox
                final_output += combo_value + (delimiter if delimiter else "")

        final_output += ".pdf"
        return final_output
    
    def sanitize_filename(self, filename):
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Remove leading hyphen
        if filename.startswith('-'):
            filename = filename[1:]

        filename = filename.rstrip('. ')  # Remove trailing periods and spaces
        return filename


class RowWidget(QWidget):
    def __init__(self, columns, row_number, parent=None):
        super().__init__(parent)
        self.initUI(columns, row_number)

    def initUI(self, columns, row_number):
        layout = QHBoxLayout()

        self.label = QLabel(str(row_number))
        self.comboBox = QComboBox()
        self.comboBox.setEditable(True)  # Enable typing in the ComboBox
        
        sorted_columns = sorted(columns)  # Sort the columns list in ascending order
        self.comboBox.addItems(sorted_columns)
        
        self.delimiterEdit = QLineEdit()  # Text box for the delimiter
        self.addButton = QPushButton("+")
        self.removeButton = QPushButton("-")

        layout.addWidget(self.label)
        layout.addWidget(self.comboBox)
        layout.addWidget(self.delimiterEdit)
        layout.addWidget(self.addButton)
        layout.addWidget(self.removeButton)

        self.setLayout(layout)

class MainWindow(QMainWindow):
    def __init__(self, dataframe, directory):
        super().__init__()
        self.dataframe = dataframe
        self.directory = directory
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Dynamic Rename Structure')
        self.dynamicRowsWidget = DynamicRowsWidget(self.dataframe, self.directory, self)
        #self.dynamicRowsWidget.renamingDone.connect(self.onRenamingDone)
        self.setCentralWidget(self.dynamicRowsWidget)
        self.show()

    def onRenamingDone(self, modified_dataframe):
        # Handle the modified DataFrame here
        # For example, print or save it
        print("")
        
def launch_window(dataframe, directory, callback):
    # Check if QApplication already exists
    app = QApplication.instance()
    if app is None:  # Only create a new one if it doesn't exist
        app = QApplication(sys.argv)
        
        mainWindow = MainWindow(dataframe, directory)
        mainWindow.dynamicRowsWidget.renamingDone.connect(callback)
    # else:
    #     mainWindow = MainWindow(dataframe, directory)
    #    # Connect the signal from DynamicRowsWidget to the callback
    #     mainWindow.dynamicRowsWidget.renamingDone.connect(callback)
    #     app.exec_()

def handle_dataframe(modified_dataframe):
    # Handle the modified DataFrame here
    # For example, print or save it
    print(modified_dataframe)
    




# class DynamicRowsWidget(QWidget):
#     renamingDone = pyqtSignal(pd.DataFrame)  # Define the signal here    

#     def __init__(self, dataframe, directory, parent=None):
#         super().__init__(parent)
#         self.dataframe = dataframe
#         self.directory = directory
#         self.columns = dataframe.columns.tolist()
#         self.rows = []
#         self.initUI()

#     def initUI(self):
#         self.mainLayout = QVBoxLayout()
#         self.rowsLayout = QVBoxLayout()

#         self.addRow()
#         self.mainLayout.addLayout(self.rowsLayout)
        
#         #Add Preview Label
#         self.previewLabel = QLabel(str(""))

#         # Add Preview Button
#         self.previewButton = QPushButton("Preview")
#         self.previewButton.clicked.connect(self.previewOutput)
        
#         # Add Rename Button
#         self.renameButton = QPushButton("Rename Files")
#         self.renameButton.clicked.connect(self.renameFiles)
        
#         self.mainLayout.addWidget(self.previewLabel)
#         self.mainLayout.addWidget(self.previewButton)
#         self.mainLayout.addWidget(self.renameButton)

#         self.setLayout(self.mainLayout)

#     def addRow(self):
#         row_number = len(self.rows) + 1
#         row = RowWidget(self.columns, row_number, self)
#         row.removeButton.clicked.connect(lambda: self.removeRow(row))
#         row.addButton.clicked.connect(self.addRow)
#         self.rowsLayout.addWidget(row)
#         self.rows.append(row)

#     def removeRow(self, row):
#         if len(self.rows) > 1:  # Prevent removing the last row
#             self.rows.remove(row)
#             row.deleteLater()
#             self.updateRowNumbers()

#     def updateRowNumbers(self):
#         for i, row in enumerate(self.rows):
#             row.label.setText(str(i + 1))
            
#     def previewOutput(self):
#         final_output = ""
#         if not self.dataframe.empty:
#             first_row = self.dataframe.iloc[0]  # Get the first row of the DataFrame
#             for row in self.rows:
#                 combo_value = row.comboBox.currentText().strip()  # Trim the combo value
#                 delimiter = row.delimiterEdit.text().strip()  # Trim the delimiter

#                 if combo_value in self.dataframe.columns:
#                     # If combo_value is a column name, use the value from that column
#                     column_value = first_row[combo_value]
#                     final_output += str(column_value).strip() + (delimiter if delimiter else "")  # Trim column value
#                 else:
#                     # If combo_value is not a column name, use it directly
#                     final_output += combo_value + (delimiter if delimiter else "")
#         final_output += ".pdf"
        
#         self.previewLabel.setText(final_output)  # Display in preview label
        
#     def renameFiles(self):
#         if not self.dataframe.empty and os.path.exists(self.directory):
#             self.dataframe['AIS_LINK'] = ""  # Initialize the new column

#             for index, row in self.dataframe.iterrows():
#                 base_new_filename = self.buildFileName(index)
#                 old_file_path = os.path.join(self.directory, str(row['sys_AISdocID']) + '.pdf')
#                 new_file_path = os.path.join(self.directory, base_new_filename)

#                 # Check if the new file name already exists and modify it if necessary
#                 file_extension = '.pdf'  # Assuming the file extension is always .pdf
#                 counter = 1
#                 while os.path.exists(new_file_path):
#                     # Append a number to the base filename
#                     new_filename = f"{os.path.splitext(base_new_filename)[0]}({counter}){file_extension}"
#                     new_file_path = os.path.join(self.directory, new_filename)
#                     counter += 1

#                 # Rename the file
#                 if os.path.exists(old_file_path):
#                     shutil.move(old_file_path, new_file_path)
#                     logger.info(f"Renamed {old_file_path} to {new_file_path}")

#                 # Update the DataFrame with the relative path from the workbook's directory
#                 # Assuming the parent of `self.directory` is the workbook's directory
#                 workbook_dir = os.path.dirname(self.directory)
#                 relative_path = os.path.relpath(new_file_path, workbook_dir)
#                 #self.dataframe.at[index, 'AIS_LINK'] = relative_path
#                 # Update the DataFrame with just the filename (relative to 'docs')
#                 #self.dataframe.at[index, 'AIS_LINK'] = os.path.basename(new_file_path)
#                 self.dataframe.at[index, 'AIS_LINK'] = 'docs/' + os.path.basename(new_file_path)

#             # Emit the signal with the modified DataFrame
#             self.renamingDone.emit(self.dataframe)

#             # Close the main window
#             self.parent().close()  # Assuming the parent of DynamicRowsWidget is the main window
            
#     def buildFileName(self, row_index):
#         final_output = ""
#         for row in self.rows:
#             combo_value = row.comboBox.currentText().strip()
#             delimiter = row.delimiterEdit.text().strip()

#             if combo_value in self.dataframe.columns:
#                 # Use the value from the DataFrame's column
#                 column_value = self.dataframe.at[row_index, combo_value]
#                 final_output += str(column_value).strip() + (delimiter if delimiter else "")
#             else:
#                 # Use the value directly from the ComboBox
#                 final_output += combo_value + (delimiter if delimiter else "")

#         final_output += ".pdf"
#         return final_output
    
#     def sanitize_filename(self, filename):
#         invalid_chars = '<>:"/\\|?*'
#         for char in invalid_chars:
#             filename = filename.replace(char, '_')

#         # Remove leading hyphen
#         if filename.startswith('-'):
#             filename = filename[1:]

#         filename = filename.rstrip('. ')  # Remove trailing periods and spaces
#         return filename


# class RowWidget(QWidget):
#     def __init__(self, columns, row_number, parent=None):
#         super().__init__(parent)
#         self.initUI(columns, row_number)

#     def initUI(self, columns, row_number):
#         layout = QHBoxLayout()

#         self.label = QLabel(str(row_number))
#         self.comboBox = QComboBox()
#         self.comboBox.setEditable(True)  # Enable typing in the ComboBox
        
#         sorted_columns = sorted(columns)  # Sort the columns list in ascending order
#         self.comboBox.addItems(sorted_columns)
        
#         self.delimiterEdit = QLineEdit()  # Text box for the delimiter
#         self.addButton = QPushButton("+")
#         self.removeButton = QPushButton("-")

#         layout.addWidget(self.label)
#         layout.addWidget(self.comboBox)
#         layout.addWidget(self.delimiterEdit)
#         layout.addWidget(self.addButton)
#         layout.addWidget(self.removeButton)

#         self.setLayout(layout)

# class MainWindow(QMainWindow):
#     #renamingDone = pyqtSignal(pd.DataFrame)  # Signal to emit the modified DataFrame

#     def __init__(self, dataframe, directory):
#         super().__init__()
#         self.dataframe = dataframe
#         self.directory = directory
#         self.initUI()

#     def initUI(self):
#         self.setWindowTitle('Dynamic Rename Structure')
#         self.dynamicRowsWidget = DynamicRowsWidget(self.dataframe, self.directory, self)
#         #self.dynamicRowsWidget.renamingDone.connect(self.onRenamingDone)
#         self.setCentralWidget(self.dynamicRowsWidget)
#         self.show()

#     def onRenamingDone(self, modified_dataframe):
#         # Handle the modified DataFrame here
#         # For example, print or save it
#         print("")
        
# def launch_window(dataframe, directory, callback):
#     app = QApplication(sys.argv)
#     mainWindow = MainWindow(dataframe, directory)
#    # Connect the signal from DynamicRowsWidget to the callback
#     mainWindow.dynamicRowsWidget.renamingDone.connect(callback)
#     app.exec_()

# def handle_dataframe(modified_dataframe):
#     # Handle the modified DataFrame here
#     # For example, print or save it
#     print(modified_dataframe)
    

