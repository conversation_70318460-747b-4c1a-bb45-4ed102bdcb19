import cv2
import numpy as np
import os

'''
 - Created to handle cases where part of a table is text, and the other is an image (Project: Axis 017 & Axis 018)
 - Elements are detected well, but grouping seems unpredictable especially when dealing with different scale drawings. 
 - Storing this code for reference and reverting back to 'detect_text_regions_cv2.py'
'''

def detect_text_regions(image_path, min_area=10, debug=False, debug_dir=None):
    """
    Detect individual text elements in an image and return their coordinates.
    Optimized for both single characters and longer text.
    """
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError("Could not read the image")
        
    # Print image dimensions to verify full image is being processed
    print(f"Image dimensions: {img.shape}")
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Apply binary threshold instead of adaptive
    # This might work better for clear black text on white background
    _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
    
    # Create a very small kernel for connecting components within characters
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 2))
    
    # Minimal dilation to preserve small text
    dilated = cv2.dilate(thresh, kernel, iterations=1)
    
    # Find contours with hierarchy to separate nested contours
    contours, hierarchy = cv2.findContours(dilated, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    # Extract regions that are likely to contain text
    text_regions = []
    img_height, img_width = img.shape[:2]
    
    for i, contour in enumerate(contours):
        x, y, w, h = cv2.boundingRect(contour)
        area = w * h
        aspect_ratio = float(w) / h if h > 0 else 0
        
        # Much more permissive filtering conditions
        if (area > min_area and  # Very small minimum area
            area < (img_width * img_height * 0.1) and  # Reasonable maximum area
            aspect_ratio < 15 and  # More permissive aspect ratio
            h > 2 and  # Allow very small height
            w > 2):   # Allow very small width
            
            # Check if the region contains actual content (not just noise)
            roi = thresh[y:y+h, x:x+w]
            if np.mean(roi) > 10:  # Check if there's significant content
                text_regions.append((x, y, w, h))
    
    # Sort regions by y-coordinate first, then x-coordinate
    text_regions.sort(key=lambda r: (r[1] // 15, r[0]))  # Smaller tolerance for row grouping
    
    # Save debug images if requested
    if debug and debug_dir:
        os.makedirs(debug_dir, exist_ok=True)
        cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
        cv2.imwrite(os.path.join(debug_dir, "2_threshold.png"), thresh)
        cv2.imwrite(os.path.join(debug_dir, "3_dilated.png"), dilated)
        
        # Draw contours on original image
        debug_img = img.copy()
        cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 1)
        cv2.imwrite(os.path.join(debug_dir, "4_all_contours.png"), debug_img)
    
    return text_regions

def visualize_regions(image_path, regions, output_path):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    img = cv2.imread(image_path)
    
    # Create a copy for the visualization
    vis_img = img.copy()
    
    for i, (x, y, w, h) in enumerate(regions):
        # Alternate between different colors for better visibility
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        # Add region number with smaller font and offset
        cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
    
    # Ensure output path has extension
    if not output_path.lower().endswith(('.png', '.jpg', '.jpeg')):
        output_path += '.png'
    
    cv2.imwrite(output_path, vis_img)
    return vis_img

def group_text_regions(regions, max_horizontal_distance=20, max_vertical_distance=5):
    """
    Group text regions that are likely part of the same word.
    
    Args:
        regions: List of (x, y, w, h) tuples
        max_horizontal_distance: Maximum horizontal pixel distance between regions to be grouped
        max_vertical_distance: Maximum vertical pixel difference between regions to be grouped
    
    Returns:
        List of grouped regions (x, y, w, h) and list of lists containing original region indices
    """
    if not regions:
        return [], []
    
    # Sort regions by x coordinate
    sorted_regions = sorted(enumerate(regions), key=lambda x: x[1][0])
    original_indices = [x[0] for x in sorted_regions]
    sorted_regions = [x[1] for x in sorted_regions]
    
    groups = []
    group_indices = []
    current_group = [sorted_regions[0]]
    current_indices = [original_indices[0]]
    
    for i in range(1, len(sorted_regions)):
        prev_region = sorted_regions[i-1]
        curr_region = sorted_regions[i]
        
        # Calculate distances
        prev_right = prev_region[0] + prev_region[2]  # x + width
        horizontal_dist = curr_region[0] - prev_right
        vertical_dist = abs(curr_region[1] - prev_region[1])
        
        # Check if regions should be grouped
        if (horizontal_dist <= max_horizontal_distance and 
            vertical_dist <= max_vertical_distance):
            current_group.append(curr_region)
            current_indices.append(original_indices[i])
        else:
            # Create bounding box for current group
            if current_group:
                min_x = min(r[0] for r in current_group)
                min_y = min(r[1] for r in current_group)
                max_x = max(r[0] + r[2] for r in current_group)
                max_y = max(r[1] + r[3] for r in current_group)
                groups.append((min_x, min_y, max_x - min_x, max_y - min_y))
                group_indices.append(current_indices)
            
            # Start new group
            current_group = [curr_region]
            current_indices = [original_indices[i]]
    
    # Add last group
    if current_group:
        min_x = min(r[0] for r in current_group)
        min_y = min(r[1] for r in current_group)
        max_x = max(r[0] + r[2] for r in current_group)
        max_y = max(r[1] + r[3] for r in current_group)
        groups.append((min_x, min_y, max_x - min_x, max_y - min_y))
        group_indices.append(current_indices)
    
    return groups, group_indices

def visualize_regions_with_groups(image_path, regions, grouped_regions, group_indices, output_path):
    """
    Draw both individual regions and their groupings.
    """
    img = cv2.imread(image_path)
    vis_img = img.copy()
    
    # Draw individual regions in alternating colors
    for i, (x, y, w, h) in enumerate(regions):
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
    
    # Draw grouped regions in purple with thicker lines
    for i, (x, y, w, h) in enumerate(grouped_regions):
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), (255, 0, 255), 2)
        # Add group number
        cv2.putText(vis_img, f'G{i+1}', (x, y-3), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
    
    if not output_path.lower().endswith(('.png', '.jpg', '.jpeg')):
        output_path += '.png'
    
    cv2.imwrite(output_path, vis_img)
    return vis_img

if __name__ == "__main__":
    pg = "1"
    image_path = fr"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\outputImages\BOM\table_BOM_page_{pg}_0.png"
    output_dir = os.path.dirname(image_path)
    debug_dir = os.path.join(output_dir, "debug")
    
    try:
        # Detect individual text regions
        regions = detect_text_regions(image_path, min_area=10, debug=True, debug_dir=debug_dir)
        
        # Group the regions
        grouped_regions, group_indices = group_text_regions(
            regions, 
            max_horizontal_distance=150,  # Adjust this value based on your needs
            max_vertical_distance=150     # Adjust this value based on your needs
        )
        
        # Print information
        print(f"Found {len(regions)} individual regions and {len(grouped_regions)} groups")
        
        # Print group information
        for i, (group_region, indices) in enumerate(zip(grouped_regions, group_indices), 1):
            x, y, w, h = group_region
            print(f"\nGroup {i}:")
            print(f"Bounding box: x={x}, y={y}, width={w}, height={h}")
            print("Contains regions:", indices)
        
        # Visualize both individual regions and groups
        output_path = os.path.join(output_dir, f"detected_regions_with_groups_{pg}.png")
        visualize_regions_with_groups(image_path, regions, grouped_regions, group_indices, output_path)
        print(f"\nVisualization saved to: {output_path}")
        
    except Exception as e:
        print(f"Error processing image: {str(e)}")
