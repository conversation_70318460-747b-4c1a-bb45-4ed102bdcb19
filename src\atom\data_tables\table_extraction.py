
# table_extraction.py
import os
import fitz
import pandas as pd
import numpy as np
import json
import logging
from .table_utils import convert_relative_coords_to_points, check_bbox_overlap
from .table_processing import filter_data_within_table, process_table_data, starts_with_prefix_and_short, keyword_in_first_words
from .table_structure import get_table_coordinates, combine_nearby_words, sort_by_y0
from .table_utils import safe_json_loads, apply_replacement_function

logger_debug_mode=False # Ovveride App Logger settings
debug_mode= False # Export pages as xlsx files
debug_row_content = False # Debug row information at the end of detect_bom_rows
debug_discarded = False # Print intentionally skipped and unassigned items


logger = logging.getLogger(__name__)

def get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data_df, missing_pos=False, remove_outliers=True): # <-- IN USE
    #get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data, missing_pos=False, remove_outliers=True): # <-- IN USE
    logger.info("Getting table data")

    # raw_data_debug = pd.DataFrame(raw_data)

    '''
    -> Add more lists for Material Descriptors/Labels

    -> change outlier data to not filter the values out, but to flag/ignore the values instead. Need additional column
    -> Outlier data to keep word with outlier and without for easy merging later (Let the user decide)
    -> Add reason for outlier
    -> Check font color
    -> check type (text, annot)
    -> Add dropped keywords somewhere for review
    -> check for header values using similar logic to 'combine_nearby_words'

    '''

    # Dictionaries to store DataFrames for each table type
    structured_tables = {}
    annotations_tables = {}
    outliers_tables = {}
    annot_outliers_tables = {}
    annot_outliers_df = pd.DataFrame()
    collected_outliers = []

    # logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()
    annotations_df = pd.DataFrame()  # DataFrame for annotations
    outliers_df = pd.DataFrame()
    annot_outliers_df = pd.DataFrame()

    # Set file info
    filename = os.path.basename(pdf_path)
    directory_path = os.path.dirname(pdf_path)
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)

    # --> Find the items (e.g., 'BOM', 'SPEC', 'Spool') and their table coordinates
    identified_tables = get_table_coordinates(converted_roi_payload)  # Adjust this function to return a list of table types with their coordinates

    #print("IDENTIFIED TABLES: ", identified_tables)

    #print("\n\n --> TABLES: ", identified_tables)

    if not identified_tables:
        logger.error("No table coordinates found for items")
        # Return empty DataFrames for each expected table type if needed
        return structured_tables, annotations_tables, outliers_df, annot_outliers_df

    # List of header values and ignore terms
    header_values = ["PT", "PT.", "ID", "NO", "DESCRIPTION", "NPD", "(IN)", "CMDTY CODE", "QTY", "IDENT", "QTY", "POS",
            "HOLD", "COMPONENT DESCRIPTION", "N.B. (INS)", "ITEM CODE", "N.B.", "(INS)", "N.P.S.", "(IN)",
            "N.S. (INS)", "N.S.", "N.P.S.", "MATERIALS", "GMN", "MK", "MK.", "MK NO", "MK NO.", "MK NO NPD", "MK NO NPD (IN)",
            "NO NPD", "NPD","SPEC", "PART NO", "PART NO.", "PART", "MK NPD","NO.", "ND", "ND.", "BILL OF MATERIALS", "SIZE", "NO (IN)", "DN"]

    # ignore_terms = ["PIPE SUPPORTS", "CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
    #                 "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS",
    #                 "FLANGES", "GASKETS", "BOLTS", "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS",
    #                 "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    # ignore_terms = ["CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
    #                 "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS",
    #                 "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    ignore_terms = ["CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
                    "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)",
                    "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S", "HOLD", "HARDWARES"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

    bottom_limit_stop_flag = ["PIECE MARKS", "PIECE MARK", "[1]", "[2]", "[3]", "[4]", "[5]", "[6]", "[7]" ,"PIPE SPOOLS", "PIPE SPOOL"]

    field_keywords = ["ERECTION ITEMS","ERECTION MATERIALS", "ERECTION", "OTHER THAN SHOP MATERIALS", "FIELD INSTALL"]
    shop_keywords = ["FABRICATION ITEMS", "FABRICATION MATERIALS", "FABRICATION","SHOP MATERIALS", "FABRICATION MATERIALS COMPONENT DESCRIPTION"] # "FABRICATION",
    misc_keywords = ["MISC.", "MISCELLANEOUS COMPONENTS", "OFFSHORE", "OFFSHORE MATERIALS", ]
    instr_keywords = ["INSTRUMENTS", "MISCELLANEOUS COMPONENTS"]

    component_keywords = ["BOLTS", "PIPE", "FITTINGS", "FLANGES", "GASKETS", "VALVES", "INSTRUMENTS", "INSTR.",
                          "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS", "MISCELLANEOUS COMPONENTS",
                          "MISC.","PIPE SUPPORTS", "OLETS", "FASTENERS", "GASKETS / BOLTS"]

    # Define the prefixes for partial matches (Checks if it begins to classify the Material Scope)
    field_prefixes = ("FIELD", "ERECTION", "OTHER THAN SHOP")
    shop_prefixes = ("SHOP", "FABRICAT", "OTHER THAN FIELD")
    misc_prefixes = ("MISCELLANEOUS",)  # Note the comma to make it a tuple with one element
    instr_prefixes = ("INSTR/", "ISTR", "INSTRUMENTS")

    # Define list of Exceptions
    exceptions = ["I-ROD", "SUPPORT - UBOLT", "SUPPORT - GUIDE", "SUPPORT - ANCHOR", "SHIM PLATE", "INSTRUMENT COMPONENT", "INSTRUMENT COMPONENTS"]

    # Combine all keywords and prefixes into a single list
    all_keywords = field_keywords + list(field_prefixes) + shop_keywords + list(shop_prefixes) #+ misc_keywords + list(misc_prefixes)# + instr_keywords + list(instr_prefixes)
    #all_keywords = field_keywords + field_prefixes + shop_keywords + shop_prefixes

    # Ensure all prefixes are tuples
    field_prefixes = tuple(field_prefixes)
    shop_prefixes = tuple(shop_prefixes)
    misc_prefixes = tuple(misc_prefixes)
    instr_prefixes = tuple(instr_prefixes)

    # Ensure all keywords are lists
    field_keywords = list(field_keywords)
    shop_keywords = list(shop_keywords)
    misc_keywords = list(misc_keywords)
    instr_keywords = list(instr_keywords)

    # Prepare Raw data
    if isinstance(raw_data_df, pd.DataFrame):
        if debug_mode:
            logger.debug("Using Raw DataFrame directly")
    else:
        try:
            raw_data_df = pd.DataFrame(raw_data_df)
        except Exception as e:
            logger.error(f"Error converting to DataFrame: {e}")
            raw_data_df = pd.DataFrame()  # Create an empty DataFrame if conversion fails

    if not raw_data_df.empty:

        # print(f"\n\n\n--> TESTING: Exporting raw_data_df - get_tables_test\n   Rows: {len(raw_data_df)}\n\n-------------------------")
        # raw_data_df.to_excel("_DEBUG raw_data_df get_tables_test.xlsx")
        try:
            raw_data_df['font_size'] = pd.to_numeric(raw_data_df['font_size'], errors='coerce')
        except Exception as e:
            logger.error(f"Error converting 'font_size' to numeric: {e}", exc_info=True)

        # Option 2
        # raw_data_df['Fontsize'] = raw_data_df['Fontsize'].astype(float)

        # Convert the JSON string back to a list of dictionaries
        #raw_data_df['words'] = raw_data_df['words'].apply(json.loads)
        try:
            raw_data_df['words'] = raw_data_df['words'].apply(safe_json_loads)
        except Exception as e:
            logger.error("No `words` in raw_data_df", exc_info=True)

        # Filter raw_data_df where 'pdf_page' is equal to page_num + 1
        raw_data_df = raw_data_df[raw_data_df['pdf_page'] == page_num + 1]

        # Filter annotations_df
        # print(f"ANNOT COLUMNS: {annotations_df.columns}")
        annotations_df = raw_data_df[raw_data_df['type'] != 'Text']

    for table_type, (table_coords, column_coords, headers_selected) in identified_tables.items():
        # logger.debug(f"Processing {table_type} table data")
        rect = fitz.Rect(table_coords['x0'], table_coords['y0'], table_coords['x1'], table_coords['y1'])

        # raw_data_df = pd.DataFrame(raw_data)
        # raw_data_df = filter_data_within_table(raw_data_df, rect, table_type)
        # raw_data_df.to_excel(f"Passed Raw Data Pg {page_num}.xlsx")
        # print("RAW DATA COLUMNS: ", raw_data_df.columns)

        if not raw_data_df.empty:
            try:
                # Filters the raw data which reside inside table ROI
                raw_table_df = filter_data_within_table(page_num + 1, raw_data_df, rect, table_type)

                # --> Debug
                if debug_mode:
                    raw_table_df.to_excel(f"debug/Debug Filtered Table Data Pg {page_num + 1}.xlsx")

            except Exception as e:
                print()
                logger.error(f"Error in filter_data_within_table: {e}", exc_info=True)
                # You might want to return an empty DataFrame or handle this error in some way
                raw_table_df = pd.DataFrame()

            # print(f"\n\nAnnotations Rows: {len(annotations_df)}")
            # print(f"\n\n --> Length of annotations df: {len(annotations_df)}

        if debug_mode:
            annotations_df.to_excel("Annot - get_table_data.xlsx")
            print(f"\n--------------Page: {page_num + 1}\n--------------")

        if raw_table_df.empty and annotations_df.empty:
            logger.warning(f"No data found for table type '{table_type}' on page {page_num}. Returning empty DataFrames.")
            continue  # Skip to the next table type

        current_material_scope = None
        current_component_category = None
        component_match = None

        if not raw_table_df.empty:

            # Add debugging print statements
            if debug_mode:
                print(f"\n\nColumns in raw_df: {raw_table_df.columns}")
                print(f"Shape of raw_df: {raw_table_df.shape}")


            # Apply non ASCII replacement function and debug
            raw_table_df = apply_replacement_function(raw_table_df)

            # Group phrases that the replacement functions can analyze
            raw_table_df = combine_nearby_words(raw_table_df, y_tolerance=2, x_tolerance=8)

            raw_table_df = sort_by_y0(raw_table_df)

            # Create a copy for filtering
            filtered_df = raw_table_df.copy()

            # Find the y0 value from the row where the text matches any value in bottom_limit_stop_flag
            bottom_limit_y0 = None

            for term in bottom_limit_stop_flag:
                if term in filtered_df['Combined_Text'].values:
                    bottom_limit_y0 = filtered_df.loc[filtered_df['Combined_Text'] == term, 'coordinates2'].iloc[0][1]
                    break

            # Drop rows where y0 is greater than or equal to bottom_limit_y0
            if bottom_limit_y0 is not None:
                filtered_df = filtered_df[filtered_df['coordinates2'].apply(lambda coord: coord[1] < bottom_limit_y0)]

            # Filter out rows where the "Combined_Text" column matches any value in filter_terms
            filter_terms = header_values + ignore_terms

            filtered_df = filtered_df[~filtered_df['Combined_Text'].isin(filter_terms)]

            # # DEBUG!
            # debug_drop_df_1 = filtered_df[filtered_df['Combined_Text'].isin(filter_terms)]

            # debug_drop_df_1.to_excel(f"_Debug-1 Page {page_num} Drop Terms.xlsx")

            #filtered_df.to_excel(f"Filtered DF {page_num}.xlsx")

            filtered_df.fillna({"Combined_Text": ""}, inplace=True)
            filtered_df["Combined_Text"] = filtered_df["Combined_Text"].str.strip().str.upper()

            filtered_df['is_component_keyword'] = None
            # Distinguish Shop/Fabrication Items
            for row in filtered_df.itertuples():
                index = row.Index
                text = row.Combined_Text

                # print(f"{page_num + 1} Text: {text}")

                # Check for prefixes first
                if starts_with_prefix_and_short(text, field_prefixes):
                    current_material_scope = "Field"
                elif starts_with_prefix_and_short(text, shop_prefixes):
                    current_material_scope = "Shop"
                elif starts_with_prefix_and_short(text, misc_prefixes):
                    current_material_scope = "Misc."
                # elif starts_with_prefix_and_short(text, instr_prefixes):
                #     current_material_scope = "Instr."

                # If no prefix match, check for keywords
                elif keyword_in_first_words(text, field_keywords):
                    current_material_scope = "Field"
                elif keyword_in_first_words(text, shop_keywords):
                    current_material_scope = "Shop"
                elif keyword_in_first_words(text, misc_keywords):
                    current_material_scope = "Misc."
                # elif keyword_in_first_words(text, instr_keywords):
                #     current_material_scope = "Instr."
                # else:
                #     current_material_scope = None

                # elif find_exact_keyword_match(text, component_keywords):
                #     current_component_category = result

                # Check for exact keyword match (for component category)
                # component_match = find_exact_keyword_match(text, component_keywords)

                component_match = find_similar_keyword_match(text, component_keywords)
                if component_match:
                    current_component_category = component_match
                    filtered_df.at[index, 'is_component_keyword'] = True

                # Assign the current material scope to the row
                if current_material_scope:
                    filtered_df.at[index, 'Material Scope'] = current_material_scope

                # Assign the current component category to the row
                if current_component_category:
                    # print(f"COMPONENT MATCH: {current_component_category}")
                    filtered_df.at[index, 'componentCategory'] = current_component_category
                    # print(f"Current Material Scope Pg. {page_num}: {current_material_scope}")

            # Filter rows based on the new criteria
            all_prefixes = field_prefixes + shop_prefixes + misc_prefixes #+ instr_prefixes
            all_keywords = field_keywords + shop_keywords + misc_keywords #+ instr_keywords

            # Instead of dropping rows, let's mark them
            filtered_df['is_keyword'] = filtered_df['Combined_Text'].apply(lambda x:
                (starts_with_prefix_and_short(x, all_prefixes) or
                keyword_in_first_words(x, all_keywords)) and
                x.strip().upper() not in exceptions  # Add this condition
            )

            # Mark rows for component category keywords
            # filtered_df['is_component_keyword'] = filtered_df['Combined_Text'].apply(lambda x:
            #     # find_exact_keyword_match(x, component_keywords) is not None
            #     find_similar_keyword_match(x, component_keywords) is not None
            # )

            # Add keyword-matched rows to outliers_df
            keyword_matches = filtered_df[filtered_df['is_keyword'] | filtered_df['is_component_keyword']].copy()
            if not keyword_matches.empty:
                keyword_matches['outlier_reason'] = 'Keyword Match'
                keyword_matches['outlier_action'] = 'filtered_out'
                outliers_df = pd.concat([outliers_df, keyword_matches], ignore_index=True)

            # Now, actually filter out the rows marked as keywords
            # Now, filter out the rows marked as keywords or component keywords
            filtered_df = filtered_df[~(filtered_df['is_keyword'] | filtered_df['is_component_keyword'])]

            # filtered_df = filtered_df[~filtered_df['is_keyword']]

            # debug_drop_df_2 = filtered_df[filtered_df['is_keyword']]

            # # DEBUG!
            # debug_drop_df_2.to_excel(f"_Debug-2 Page {page_num} Drop Terms.xlsx")

            # Drop the 'is_keyword' column as it's no longer needed
            #filtered_df = filtered_df.drop('is_keyword', axis=1)

            #print(f"-->Final shape of filtered_df: {filtered_df.shape}")
            raw_table_df = filtered_df.copy()

            # Continue with the rest of your processing using raw_table_df
            structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df,
                                                                                                    annot_outliers_df, column_coords, headers_selected,
                                                                                                    filename, pdf_path, page_num, parent_folders_str,
                                                                                                    table_type, True)
            # print(f"Exporting Structured Table Page {page_num}....")
            #structured_table_df.to_excel(f"_Page {page_num} Structured Table.xlsx")

        #
        # --> Process and structure the raw table data and annotations (Outlier, table creation)
        else:
            if not annotations_df.empty:

                print(f"\n\n\n--> TESTING: Exporting annotations_df - get_tables_test\n   Rows: {len(annotations_df)}\n\n-------------------------")
                # annotations_df.to_excel("_DEBUG annotations_data get_tables_test.xlsx")

                structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df,
                                                                                                        annot_outliers_df, column_coords, headers_selected,
                                                                                                        filename, pdf_path, page_num, parent_folders_str, table_type)


                print(f"Rows: {len(annotations_df)} after process_table_data -- Line 922")

        # List column names from column_coords based on 'columnName' field
        try:
            column_names = [col['columnName'] for col in column_coords]
        except Exception as e:
            logger.error(f"Could not get ROI table column names: {e}", exc_info = True)

        ### NOT SURE IF NEEDED?? Outliers are being processed in process_table_data

        # Initialize a list to hold indices of outlier rows
        outlier_indices = []

        # Add outlier detection and logging here:
        try:
            for index, row in structured_table_df.iterrows():
                # Count non-empty and non-NaN values in the specified columns
                valid_values = row[column_names].replace('', np.nan).dropna().shape[0]
                if valid_values <= 1:  # Checking for rows with only one or no valid entries
                    #print(f"Outlier detected in {table_type} table at row {index}: {row[column_names].to_dict()}")
                    outlier_indices.append(index)  # Append index of outlier to list
        except Exception as e:
            logger.error(f"Error looping ROI column names to detect outlier rows in {table_type}: {e}", exc_info=True)

        #Remove outliers from structured_table_df
        if remove_outliers:
            if outlier_indices:
                structured_table_df = structured_table_df.drop(outlier_indices)
                # logger.info(f"Removed {len(outlier_indices)} outliers from {table_type} table.")

            # Append the outliers DataFrame to the list for later combination
        if not outliers_df.empty:
            collected_outliers.append(outliers_df)

        ### NOT SURE IF NEEDED??

        # Store the processed DataFrames in their respective dictionaries

        # annotations_df.to_excel(f"Annot Df get_table_data.xlsx {page_num + 1}.xlsx")

        # print(f"\n\nDEBUG: LINE 973 in get_tables_test: LEN {len(structured_table_df)}\n EXPORTING...\n\n")
        # structured_table_df.to_excel("debug_973_table_data.xlsx")

        structured_tables[table_type] = structured_table_df
        annotations_tables[table_type] = annotations_df
        # Outliers are collected and combined outside the loop
        #outliers_tables[table_type] = outliers_df
        annot_outliers_tables[table_type] = annot_outliers_df

    # Combine all collected outliers DataFrames into one
    combined_outliers_df = pd.concat(collected_outliers, ignore_index=True) if collected_outliers else pd.DataFrame()

    #print("\n\nCombined Outliers:", combined_outliers_df.head())  # For debugging

    return structured_tables, annotations_tables, combined_outliers_df, annotations_df # <-- Annotations should follow the same logic as text. Fix this --> BUG

def assign_texts_to_columns(row_texts: list[dict], column_coords: list[dict], column_names: list[str], table_type: str, previous_row=None):
    '''
    <DEV>
    Handle Annotation Rows differently

    Collect text and annotation data (if it a row can be created) separately.
    Handle these data structures separately and join after.
    Need to identify row types (Text, Annot, Text w/Rev Markup)
    '''

    if debug_row_content:
        print(f"\n=== Starting new row assignment in assign_texts_to_columns===")
        print(f"Number of texts to process: {len(row_texts)}")
        print(f"Initial row_texts: {row_texts}")

    row_structure = {col: [] for col in column_names}  # Use lists instead of strings

    row_colors = {col: [] for col in column_names}  # To keep track of font colors
    row_material_scope = None
    row_component_category = None
    non_text_items = [] # New list to store non-text items (Annots)
    leftmost_column = column_names[0]  # Assuming the first column is the leftmost

    replace_mat_desc = False # Will replace values in material_description if 'True'

    sorted_texts = sorted(row_texts, key=lambda item: (item['bbox'][1], item['bbox'][0]))

    if debug_row_content:
        print(f"Sorted texts: {sorted_texts}")

    # print("\n\n-->INCOMING ROW TEXTS:\n")
    # pp(row_texts)
    # print("\n\n")

    col_rects = {}
    for col_name, col_info in zip(column_names, column_coords):
        col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
        col_rects[col_name] = col_rect

    for text_item in sorted_texts:
        # if text_item.get('type', '') != 'Text':
        try:
            val_type = text_item.get('type')

            if debug_row_content:
                print(f"\nProcessing text item: {text_item}")
                print(f"Type: {val_type}")

        except Exception as e:
            logger.error(f"Could not get 'val_type': {e}")
            val_type = ''

        # if val_type.upper() != 'TEXT':
        # if text_item.get('type', '') != 'Text':

        # Modified condition to properly handle nan values # Treat empty type as valid text # Temporarily ovveride for annotations ATTENTION
        if pd.isna(val_type) or val_type == '': #or val_type == 'OCR':
            # This is actually a valid text item
            pass
        elif val_type not in ['Text', '', 'OCR']:
            print(f"Skipping non-text item (val_type not in configured) options")

            print(f"\n--> \n   Non-text item found: {text_item}")
            print(f"       Value Type: {val_type}")
            print(f"       Table Type: {table_type}")
            non_text_items.append(text_item)
            continue

        text, bbox, color = str(text_item['text']).strip(), text_item['bbox'], text_item['color'] #, text_item['words']

        if debug_row_content:
            print(f"Text: {text}")
            print(f"BBox: {bbox}")

        # Handle the case where 'words' might be a string or missing
        try:
            words = text_item.get('words', [])

            if isinstance(words, str):
                words = [{'text': words, 'bbox': bbox}]
            elif not isinstance(words, list):
                words = [{'text': text, 'bbox': bbox}]

        except Exception as e:
            print(f"\n\n--> Failure on initial words get: {e}")

        if table_type == 'bom':

            material_scope = text_item.get('material_scope', '')
            if material_scope and not row_material_scope:
                row_material_scope = material_scope

            component_category = text_item.get('componentCategory', '')
            if component_category and not row_component_category:
                row_component_category = component_category

        # Check for multi-column span
        spanning_columns = []
        for col_name, col_info in zip(column_names, column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            if col_rect.intersects(bbox):
                spanning_columns.append(col_name)

        # Sort words by y-coordinate and then x-coordinate
        # sorted_words = sorted(words, key=lambda w: (w['bbox'][1], w['bbox'][0]))

        # Handle multi-column span
        if len(spanning_columns) > 1:

            for word_info in words:
                if isinstance(word_info, dict):
                    if 'word' in word_info and 'bbox' in word_info:
                        # Handle the case where word_info is already in the correct format
                        word = word_info['word']
                        word_bbox = fitz.Rect(word_info['bbox'])
                    elif 'text' in word_info:
                        # Handle the case where word_info contains a 'text' key
                        word = word_info['text']
                        word_bbox = fitz.Rect(word_info.get('bbox', bbox))  # Use the overall bbox if no specific bbox
                    else:
                        print(f"\n\nWARNING: Unexpected word_info structure: {word_info}")
                        continue
                else:
                    print(f"\n\nWARNING: word_info is not a dictionary: {word_info}")
                    continue

                for col_name, col_info in zip(column_names, column_coords):
                    col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                    if col_rect.intersects(word_bbox):

                        # Check for duplicates before adding
                        is_duplicate = False
                        for i, existing_word in enumerate(row_structure[col_name]):
                            existing_bbox = None
                            for t in sorted_texts:
                                if str(t['text']).strip() == existing_word:
                                    existing_bbox = t['bbox']
                                    break

                            if existing_word == word and existing_bbox and check_bbox_overlap(word_bbox, existing_bbox):
                                is_duplicate = True
                                break

                        if not is_duplicate:

                            if col_name == "material_description" or (color == (255, 0, 0) and row_structure[col_name]):
                                if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                    row_structure[col_name][-1] += f" {word}"
                                else:
                                    if replace_mat_desc:
                                        row_structure[col_name] = [word]
                                        row_colors[col_name] = [color]
                                    else:
                                        row_structure[col_name].append(word)
                                        row_colors[col_name].append(color)
                            else:
                                row_structure[col_name].append(word)
                                row_colors[col_name].append(color)
                        break

        else:
            assigned = False # Where text fits in a single column
            for col_name, col_info in zip(column_names, column_coords):
                col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                if col_rect.intersects(bbox):

                    # Check for duplicates before adding
                    is_duplicate = False
                    for i, existing_text in enumerate(row_structure[col_name]):
                        # Get bbox of existing text if available
                        existing_bbox = None
                        for t in sorted_texts:
                            if str(t['text']).strip() == existing_text:
                                existing_bbox = t['bbox']
                                break

                        if existing_text == text and existing_bbox and check_bbox_overlap(bbox, existing_bbox):
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        if color == (255, 0, 0) and text not in ["1", "2", "3"]:


                    # if color == (255, 0, 0) and text not in ["1", "2", "3"]:
                            if col_name == "material_description" or row_structure[col_name]:
                                # For material_description or if the column already has content
                                if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                    # If last text was red, append
                                    row_structure[col_name][-1] += f" {text}"
                                else:
                                    # If last text wasn't red or column is empty, replace
                                    row_structure[col_name] = [text]
                                    row_colors[col_name] = [color]
                            else:
                                # For other columns or if it's the first entry, replace
                                row_structure[col_name] = [text]
                                row_colors[col_name] = [color]
                        else:

                            # For black text, append only if the last entry wasn't red
                            if not row_structure[col_name] or row_colors[col_name][-1] != (255, 0, 0):
                                row_structure[col_name].append(text)
                                row_colors[col_name].append(color)
                    assigned = True
                    break

            if not assigned:
                print(f"\n\n--> !Warning!: Text '{text}' could not be assigned to any column.")


    # Join texts in each column
    for col in row_structure:
        row_structure[col] = ' '.join(row_structure[col])

    if table_type == 'bom':
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''
        row_structure['componentCategory'] = row_component_category if row_component_category else ''

    # Check for duplicates
    if previous_row is not None:
        if all(row_structure[col].strip() == previous_row[col].strip() for col in row_structure if col in previous_row):
            print(f"Duplicate row detected: {row_structure}")
            return None  # Return None for duplicate rows

    # print(f"\n\nROW STRUCTURE: {row_structure}\n\n")
    # print(f"\n\nNON TEXT ITEMS: {non_text_items}\n\n")
    if debug_row_content:
        print(f"\n=== Final row structure ===")
        print(row_structure)
        print()
        print()

    return row_structure

def get_group_for_page(page_number):
    if page_group_dict:
        return page_group_dict.get(page_number, None)  # Returns None if page_number is not found

def call_get_table_data(pdf_path, page_num, roi_json_path, raw_data_df, multi_roi=False, page_group_dict=None, x_offset=0, y_offset=0):
    # Load the PDF
    pdf_document = fitz.open(pdf_path)
    page = pdf_document[page_num]

    # Not used and replaced by raw_data_df (Function still requires argument as fallback method)
    page_text_blocks = []

    ref_rect = pdf_document.load_page(0).rect
    ref_width, ref_height = ref_rect.width, ref_rect.height

    if multi_roi:
        #print(f"\n\nLookup ROI for page number: {page_number + 1} - (1 Based)")
        roi_group_num = get_group_for_page(page_num + 1)
        #print(f"   PAGE: {page_number + 1}. --> USE ROI GROUP {roi_group_num}")

        # Select ROI Payload

        # Select ROI Payload
        if roi_group_num is not None and str(roi_group_num) in converted_multi_payload:
            converted_roi_payload = converted_multi_payload[str(roi_group_num)]
            #print(f"\n\n-->   Selected ROI payload for group {roi_group_num}")

            # if roi_group_num == 3:
            #     print("\n\nGROUP 3 DEBUG: \n")
            #     pp(converted_roi_payload)
            # You can now use selected_roi_payload for further processing
        else:
            logger.error(f"   No ROI payload found for group {roi_group_num}. Using native payload from selection")
            # Handle the case where no payload is found for the group
            converted_roi_payload = None #original_roi_payload


    else:
        # Load the ROI payload
        with open(roi_json_path, 'r') as f:
            roi_payload = json.load(f)

        # Convert Relative to Absolute Coordinates
        converted_roi_payload = convert_relative_coords_to_points(roi_payload, ref_width, ref_height, x_offset, y_offset)

    # # Call the get_table_data function



    structured_tables, annotations_tables, combined_outliers_df, annotations_df = get_table_data(
        pdf_path,
        page,
        page_num,
        converted_roi_payload,
        page_text_blocks,
        raw_data_df,
        missing_pos=False,
        remove_outliers=True
    )

    # get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data_df, missing_pos=False, remove_outliers=True)

    # Close the PDF
    pdf_document.close()

    return structured_tables, annotations_tables, combined_outliers_df, annotations_df
