"""
Construct BOM from isometric diagrams in PDF file
Detects pipe segments and associates them with sizes and quantities
"""
import fitz
import pandas as pd
from typing import List, Dict, Tuple
import re
from shapely.geometry import Point, box, LineString
import math

def calculate_angle(start, end):
    """Calculate angle between two points"""
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    """Check if angle is isometric (30°, 150°, 90°, 330°, 210°, 270°)"""
    isometric_angles = [30, 150, 90, 330, 210, 270]
    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance
              for iso_angle in isometric_angles)

def extract_pipe_info(text: str) -> Dict:
    """
    Extract pipe information from text
    Returns dict with type (size/quantity) and value
    """
    text = text.strip()
    result = {
        'type': None,
        'value': None,
        'original_value': text
    }

    # Ignore text starting with letters (e.g., "EL")
    if text[0].isalpha():
        return result

    # Check for size (ends with NPD)
    if text.upper().endswith('NPD'):
        try:
            size = float(re.findall(r'\d+\.?\d*', text)[0])
            result['type'] = 'size'
            result['value'] = size
        except (IndexError, ValueError):
            pass
        return result

    # Check for quantity (starts with numbers)
    if text[0].isdigit():
        try:
            quantity = float(re.findall(r'\d+\.?\d*', text)[0])
            result['type'] = 'quantity'
            result['value'] = quantity
        except (IndexError, ValueError):
            pass

    return result

def get_pipe_segments(page) -> List[Dict]:
    """Get pipe segments with their associated measurements"""
    pipe_segments = []

    # Extract drawings and text blocks
    drawings = page.get_drawings()
    blocks = page.get_text("dict")["blocks"]

    # Process drawings to find pipe segments
    for drawing in drawings:
        for item in drawing['items']:
            if item[0] != 'l':  # Skip non-line items
                continue

            start = (item[1], item[2])
            end = (item[3], item[4])
            angle = calculate_angle(start, end)

            if is_isometric_angle(angle):
                pipe_segments.append({
                    'start': start,
                    'end': end,
                    'angle': angle,
                    'rect': (min(start[0], end[0]),
                            min(start[1], end[1]),
                            max(start[0], end[0]),
                            max(start[1], end[1])),
                    'line': LineString([start, end])
                })

    # Associate measurements with pipe segments
    for pipe in pipe_segments:
        pipe['texts'] = []
        pipe_box = box(*pipe['rect'])
        pipe_line = pipe['line']

        # Collect all potential measurements
        potential_measurements = []

        for block in blocks:
            if "lines" not in block:
                continue

            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if not text:
                        continue

                    info = extract_pipe_info(text)
                    if info['type']:
                        text_point = Point(span['origin'])

                        # Calculate distance and check if parallel
                        distance = text_point.distance(pipe_box)
                        is_parallel = abs(pipe['angle'] - calculate_angle(
                            span['origin'],
                            (span['origin'][0] + span['bbox'][2] - span['bbox'][0],
                             span['origin'][1]))) < 10

                        if distance < 100:  # Reasonable distance threshold
                            potential_measurements.append({
                                'text': text,
                                'info': info,
                                'distance': distance,
                                'is_parallel': is_parallel,
                                'original_value': info['original_value']
                            })

        # Sort measurements by distance and parallel status
        potential_measurements.sort(key=lambda x: (not x['is_parallel'], x['distance']))

        # For quantities, take the closest one
        quantities = [m for m in potential_measurements if m['info']['type'] == 'quantity']
        if quantities:
            pipe['texts'].append(quantities[0])

        # For sizes, take all within close range
        sizes = [m for m in potential_measurements
                if m['info']['type'] == 'size' and m['distance'] < 50]
        pipe['texts'].extend(sizes)

    return pipe_segments

def associate_text_with_pipes(page, pipe_segments: List[Dict]) -> List[Dict]:
    """Associate nearby text (sizes/quantities) with pipe segments"""
    blocks = page.get_text("dict")["blocks"]

    for pipe in pipe_segments:
        pipe['texts'] = []
        pipe_box = box(*pipe['rect'])

        # Collect all potential measurements
        potential_measurements = []

        for block in blocks:
            if "lines" not in block:
                continue

            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if not text:
                        continue

                    info = extract_pipe_info(text)
                    if info['type']:
                        # Calculate distance to pipe segment
                        text_point = Point(span['origin'])
                        distance = text_point.distance(pipe_box)

                        # Only consider measurements within reasonable distance
                        if distance < 100:  # Threshold to catch all potential measurements
                            potential_measurements.append({
                                'text': text,
                                'info': info,
                                'distance': distance,
                                'original_value': info['original_value']
                            })

        # Sort measurements by distance
        potential_measurements.sort(key=lambda x: x['distance'])

        # For quantities, only take the closest one
        quantities = [m for m in potential_measurements if m['info']['type'] == 'quantity']
        if quantities:
            pipe['texts'].append(quantities[0])

        # For sizes, take all within close range
        sizes = [m for m in potential_measurements
                if m['info']['type'] == 'size' and m['distance'] < 50]
        pipe['texts'].extend(sizes)

    return pipe_segments

def process_page(page: fitz.Page) -> Tuple[List[Dict], Dict]:
    """
    Process a single page to extract pipe segments and measurements

    Args:
        page: PyMuPDF page object

    Returns:
        Tuple containing:
        - List of pipe data dictionaries
        - Dictionary of pipe segments
    """
    try:
        pipe_data = []

        # Get pipe segments with measurements
        pipe_segments = get_pipe_segments(page)

        # Convert pipe segments to output format
        for idx, segment in enumerate(pipe_segments):
            try:
                # Initialize pipe data
                pipe_info = {
                    'segment_id': idx,
                    'material_description': 'pipe',
                    'size': None,
                    'quantity': None,
                    'original_quantity': None
                }

                # Process associated texts
                for text in segment.get('texts', []):
                    if not text or 'info' not in text:
                        continue

                    info = text['info']
                    if info['type'] == 'size':
                        pipe_info['size'] = info['value']
                    elif info['type'] == 'quantity':
                        pipe_info['quantity'] = info['value']
                        pipe_info['original_quantity'] = info['original_value']

                pipe_data.append(pipe_info)

            except Exception as e:
                print(f"Error processing segment {idx}: {str(e)}")
                continue

        return pipe_data, pipe_segments

    except Exception as e:
        print(f"Error in process_page: {str(e)}")
        # Return empty results instead of raising exception
        return [], {}

def detect_arrows_and_measurements(page):
    """
    Detects arrows and measurement lines in the drawing.
    Returns two lists: arrows and measurements.
    """
    try:
        drawings = page.get_drawings()
        arrows = []
        measurements = []

        def get_coordinate(point):
            """Extract numerical coordinates from either Point object or number"""
            if hasattr(point, 'x') and hasattr(point, 'y'):
                return (float(point.x), float(point.y))
            elif isinstance(point, (tuple, list)) and len(point) >= 2:
                return (float(point[0]), float(point[1]))
            else:
                return (float(point), float(point))

        def get_line_length(line):
            """Calculate length of line considering different coordinate formats"""
            try:
                start_x, start_y = get_coordinate(line[1])
                end_x, end_y = get_coordinate(line[2])
                return math.hypot(end_x - start_x, end_y - start_y)
            except Exception as e:
                print(f"Error calculating line length: {str(e)}")
                return 0

        def lines_connected(line1, line2, tolerance=2):
            """Check if two lines share an endpoint within tolerance"""
            try:
                p1_start_x, p1_start_y = get_coordinate(line1[1])
                p1_end_x, p1_end_y = get_coordinate(line1[2])
                p2_start_x, p2_start_y = get_coordinate(line2[1])
                p2_end_x, p2_end_y = get_coordinate(line2[2])

                def points_close(x1, y1, x2, y2):
                    return math.hypot(x1 - x2, y1 - y2) <= tolerance

                return (points_close(p1_start_x, p1_start_y, p2_start_x, p2_start_y) or
                       points_close(p1_start_x, p1_start_y, p2_end_x, p2_end_y) or
                       points_close(p1_end_x, p1_end_y, p2_start_x, p2_start_y) or
                       points_close(p1_end_x, p1_end_y, p2_end_x, p2_end_y))
            except Exception as e:
                print(f"Error checking connected lines: {str(e)}")
                return False

        def angle_between_lines(line1, line2):
            """Calculate angle between two lines in degrees"""
            try:
                p1_start_x, p1_start_y = get_coordinate(line1[1])
                p1_end_x, p1_end_y = get_coordinate(line1[2])
                p2_start_x, p2_start_y = get_coordinate(line2[1])
                p2_end_x, p2_end_y = get_coordinate(line2[2])

                angle1 = math.degrees(math.atan2(p1_end_y - p1_start_y,
                                               p1_end_x - p1_start_x))
                angle2 = math.degrees(math.atan2(p2_end_y - p2_start_y,
                                               p2_end_x - p2_start_x))

                diff = abs(angle1 - angle2)
                return min(diff, 360 - diff)
            except Exception as e:
                print(f"Error calculating angle: {str(e)}")
                return 0

        for drawing in drawings:
            items = drawing.get('items', [])
            lines = [item for item in items if isinstance(item, (list, tuple)) and
                    len(item) >= 3 and item[0] == 'l']

            # Process lines in pairs
            for i, line1 in enumerate(lines):
                for line2 in lines[i+1:]:
                    try:
                        # Skip if lines aren't connected
                        if not lines_connected(line1, line2):
                            continue

                        len1 = get_line_length(line1)
                        len2 = get_line_length(line2)
                        angle = angle_between_lines(line1, line2)

                        # Skip invalid measurements
                        if len1 == 0 or len2 == 0 or angle == 0:
                            continue

                        # Arrow detection
                        if (15 <= angle <= 45 and
                            min(len1, len2) < max(len1, len2) * 0.5):
                            arrows.append({
                                'head': line1 if len1 < len2 else line2,
                                'shaft': line2 if len1 < len2 else line1,
                                'angle': angle
                            })

                        # Measurement detection
                        elif (85 <= angle <= 95 and
                              abs(len1 - len2) > max(len1, len2) * 0.8):
                            measurements.append({
                                'main_line': line1 if len1 > len2 else line2,
                                'end_line': line2 if len1 > len2 else line1,
                                'length': max(len1, len2)
                            })
                    except Exception as e:
                        print(f"Error processing line pair: {str(e)}")
                        continue

        return arrows, measurements

    except Exception as e:
        print(f"Error in detect_arrows_and_measurements: {str(e)}")
        return [], []

def draw_isometric_lines(page, new_page):
    """Draw isometric lines and highlight arrows and measurements"""
    try:
        # First draw isometric lines
        shape = new_page.new_shape()
        drawings = page.get_drawings()

        # Draw isometric lines
        for drawing in drawings:
            for item in drawing.get('items', []):
                # Ensure item is a line and has enough coordinates
                if not isinstance(item, (list, tuple)) or len(item) < 5 or item[0] != "l":
                    continue

                try:
                    # Safely extract coordinates
                    start = (float(item[1]), float(item[2]))
                    end = (float(item[3]), float(item[4]))

                    # Calculate and check if it's an isometric angle
                    angle = calculate_angle(start, end)
                    if is_isometric_angle(angle):
                        shape.draw_line(start, end)
                except (IndexError, ValueError, TypeError) as e:
                    print(f"Error processing line item: {str(e)}")
                    continue

        # Finish the isometric lines
        shape.finish(width=1, color=(0, 0, 0))  # Black for isometric lines
        shape.commit()

        # Get arrows and measurements
        arrows, measurements = detect_arrows_and_measurements(page)

        # Draw arrows
        arrow_shape = new_page.new_shape()
        for arrow in arrows:
            try:
                head = arrow.get('head', [])
                shaft = arrow.get('shaft', [])

                # Extract coordinates safely
                if isinstance(head, (list, tuple)) and len(head) >= 5:
                    head_start = (float(head[1]), float(head[2]))
                    head_end = (float(head[3]), float(head[4]))
                    arrow_shape.draw_line(head_start, head_end)

                if isinstance(shaft, (list, tuple)) and len(shaft) >= 5:
                    shaft_start = (float(shaft[1]), float(shaft[2]))
                    shaft_end = (float(shaft[3]), float(shaft[4]))
                    arrow_shape.draw_line(shaft_start, shaft_end)

            except (IndexError, KeyError, ValueError, TypeError) as e:
                print(f"Error drawing arrow (details: {arrow}): {str(e)}")
                continue

        arrow_shape.finish(width=1, color=(1, 0, 0))  # Red for arrows
        arrow_shape.commit()

        # Draw measurements
        meas_shape = new_page.new_shape()
        for meas in measurements:
            try:
                main = meas.get('main_line', [])
                end = meas.get('end_line', [])

                # Extract coordinates safely
                if isinstance(main, (list, tuple)) and len(main) >= 5:
                    main_start = (float(main[1]), float(main[2]))
                    main_end = (float(main[3]), float(main[4]))
                    meas_shape.draw_line(main_start, main_end)

                if isinstance(end, (list, tuple)) and len(end) >= 5:
                    end_start = (float(end[1]), float(end[2]))
                    end_end = (float(end[3]), float(end[4]))
                    meas_shape.draw_line(end_start, end_end)

            except (IndexError, KeyError, ValueError, TypeError) as e:
                print(f"Error drawing measurement (details: {meas}): {str(e)}")
                continue

        meas_shape.finish(width=1, color=(0, 0, 1))  # Blue for measurements
        meas_shape.commit()

    except Exception as e:
        print(f"Error in draw_isometric_lines: {str(e)}")

def visualize_results(doc, all_pipe_data, all_pipe_segments):
    """Generate visualization PDF with isometric lines and annotations"""
    try:
        output_pdf = fitz.open()

        for page_num in range(len(doc)):
            try:
                page = doc[page_num]
                new_page = output_pdf.new_page(width=page.rect.width, height=page.rect.height)

                # Show original content
                new_page.show_pdf_page(new_page.rect, doc, page_num)

                # Draw isometric lines
                draw_isometric_lines(page, new_page)
            except:
                print(f"Error processing page {page_num + 1}")
                continue

        output_pdf.save("debug/visualization.pdf")
        print("Visualization saved to debug/visualization.pdf")

    except Exception as e:
        print(f"Error in visualization: {str(e)}")

def main(pdf_path: str = "S:/Shared Folders/Client Uploads/Brock Services/S1601 Insulation Only (1).pdf"):
    """Main function to process PDF and generate BOM"""
    try:
        doc = fitz.open(pdf_path)
        all_pipe_data = []
        all_pipe_segments = {}

        # Track problematic pages
        error_pages = []

        for page_num in range(len(doc)):
            try:
                page = doc[page_num]
                print(f"\nProcessing page {page_num + 1} of {len(doc)}")

                # Add debug info for problematic pages
                if page_num + 1 in [151, 152]:  # Add any problematic page numbers here
                    print(f"Debug info for page {page_num + 1}:")
                    print(f"Page size: {page.rect}")
                    print(f"Number of drawings: {len(page.get_drawings())}")
                    print(f"Number of text blocks: {len(page.get_text('dict')['blocks'])}")

                pipe_data, pipe_segments = process_page(page)

                if not pipe_data and not pipe_segments:
                    error_pages.append(page_num + 1)
                    print(f"Warning: No data extracted from page {page_num + 1}")
                    continue

                # Add page number to items
                for item in pipe_data:
                    item['pdf_page'] = page_num + 1

                all_pipe_data.extend(pipe_data)
                all_pipe_segments[page_num] = pipe_segments

            except Exception as e:
                error_pages.append(page_num + 1)
                print(f"Error processing page {page_num + 1}: {str(e)}")
                continue

        # Convert to DataFrame and save
        df = pd.DataFrame(all_pipe_data)
        output_file = "debug/s1601_pipe_segments.xlsx"
        df.to_excel(output_file, index=False)
        print(f"\nData saved to: {output_file}")

        if error_pages:
            print(f"\nPages with errors: {error_pages}")

        # Generate visualization
        if all_pipe_data and all_pipe_segments:
            visualize_results(doc, all_pipe_data, all_pipe_segments)

    except Exception as e:
        print(f"Error in main: {str(e)}")
        raise
    finally:
        if 'doc' in locals():
            doc.close()  # Close the document in case of an error

if __name__ == "__main__":
    main()
