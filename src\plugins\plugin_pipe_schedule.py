import pandas as pd
from src.utils import fieldmap_utils

def plugin_pipe_schedule_to_general(bom_file=None,
                                    general_file=None,
                                    general_patch_file: str = "debug/general_schedule_applied.xlsx",
                                    use_internal_fieldmap: bool = True,
                                    save_debug_results: bool = True,
                                    debug_results_file: str = "debug/pipe_schedules.xlsx",
                                    bom_schedule_key: str = "schedule",
                                    rfq_scope_key: str = "rfq_scope",
                                    bom_pdf_page_key: str = "pdf_page",
                                    general_pdf_page_key: str = "pdf_page",
                                    general_schedule_column: str = "schedule",
                                    pos_column: str = "pos",
                                    material_description_column: str = "material_description",
                                    signals: dict = None
                                ):
    """
    Extract pipe schedule information from BOM data and apply to General data.

    For each PDF page, finds the first item with rfq_scope="Pipe" and extracts its schedule value.
    If no pipe item is found, uses the first available schedule value as a fallback.
    The extracted schedule values are then applied to the General data based on matching PDF page numbers.

    Notes:

        1. If use_internal_fieldmap is False, ensure that the bom_schedule_key, rfq_scope_key, bom_pdf_page_key,
        general_pdf_page_key

        2. general_schedule_column - Any column name is valid. This will create the column in General data.

        3. material_description_column and pos_column are not essential. But need to be correctly assigned
        to be correctly displayed in save_debug_results file.

        4. If importing staged data back to ATEM, ensure that ATEM's field map can convert the column names
        to internal field names.

        5. Warning - There is no field name called "schedule" in General, so this column will not show on import

    Args:
        bom_file (str): Path to BOM Excel file
        general_file (str): Path to general data Excel file
        general_patch_file (str, optional): Path to save patched general data Excel file.
            Defaults to "debug/general_schedule_applied.xlsx".
        use_internal_fieldmap (bool, optional): Whether to apply internal field mapping to column names.
            Defaults to True.
        save_debug_results (bool, optional): Whether to save debug results to Excel.
            Defaults to True.
        debug_results_file (str, optional): Path to save debug results.
            Defaults to "debug/pipe_schedules.xlsx".
        bom_schedule_key (str, optional): Column name for schedule in BOM data.
            Defaults to "schedule".
        rfq_scope_key (str, optional): Column name for rfq_scope in BOM data.
            Defaults to "rfq_scope".
        bom_pdf_page_key (str, optional): Column name for PDF page in BOM data.
            Defaults to "pdf_page".
        general_pdf_page_key (str, optional): Column name for PDF page in General data.
            Defaults to "pdf_page".
        general_schedule_column (str, optional): Column name to store schedule values in General data.
            Defaults to "schedule".
        pos_column (str, optional): Column name for position in BOM data.
            Defaults to "pos".
        material_description_column (str, optional): Column name for material description in BOM data.
            Defaults to "material_description".
        signals (dict, optional): Signal dictionary for UI integration.
            Defaults to None.

    Returns:
        str: "Done" if successful, error message otherwise

    """
    # Load data from file if provided, otherwise use the passed DataFrame
    if not bom_file:
        return "Error: No BOM data provided. Please provide either a BOM file or BOM data."
    try:
        bom_df = pd.read_excel(bom_file)
        # Ensure using internal field map
        if use_internal_fieldmap:
            bom_df = fieldmap_utils.map_from_field_map(bom_df)
    except Exception as e:
        return f"Error loading BOM file: {str(e)}"

    if not general_file:
        return "Error: No General data provided. Please provide either a General file or General data."
    try:
        general_df = pd.read_excel(general_file)
        # Ensure using internal field map
        if use_internal_fieldmap:
            general_df = fieldmap_utils.map_from_field_map(general_df)
    except Exception as e:
        return f"Error loading General file: {str(e)}"

    if bom_schedule_key not in bom_df:
        return f"Error: {bom_schedule_key} column not found in BOM data"

    if rfq_scope_key not in bom_df:
        return f"Error: {rfq_scope_key} column not found in BOM data"

    if bom_pdf_page_key not in bom_df:
        return f"Error: {bom_pdf_page_key} column not found in BOM data"

    if general_pdf_page_key not in general_df:
        return f"Error: {general_pdf_page_key} column not found in General data"

    # Initialize result list
    result = []
    # Group by pdf_page
    for pdf_page, group in bom_df.groupby(bom_pdf_page_key):
        print(f"Page {pdf_page}: {len(group)} rows")

        # Find rows where rfq_scope is "Pipe"
        pipe_rows = group[group[rfq_scope_key] == "Pipe"]

        fallback_schedule = None
        schedule = None

        if len(pipe_rows) > 0:
            # Get the first pipe row
            first_pipe = pipe_rows.iloc[0]

            # Extract schedule value, with fallback to empty string if not available
            schedule = first_pipe.get(bom_schedule_key, "")
            pos = ""
            material_description = ""

            if not pd.isna(schedule) or schedule:
                # Add to results
                result.append({
                    "pdf_page": pdf_page,
                    "schedule": schedule,
                    "fallback_schedule": fallback_schedule,
                    "pos": first_pipe.get(pos_column, ""),
                    "material_description": first_pipe.get(material_description_column, ""),
                })
                continue

        # No pipe found on this page, look for first available schedule value as fallback
        valid_schedules = group[group[bom_schedule_key].notna()]
        if not valid_schedules.empty:
            schedule = valid_schedules.iloc[0][bom_schedule_key]
            pos = valid_schedules.iloc[0].get(pos_column, "")
            material_description = valid_schedules.iloc[0].get(material_description_column, "")
            fallback_schedule = True
            print(f"  Using fallback schedule: {schedule}")

        # Add to results with fallback schedule
        result.append({
            "pdf_page": pdf_page,
            "schedule": schedule if schedule is not None else "",
            "fallback_schedule": fallback_schedule,
            "pos": pos,
            "material_description": material_description,
        })

    # Convert result to DataFrame
    result_df = pd.DataFrame(result)

    # Create a dictionary mapping pdf_page to schedule for quick lookup
    schedule_map = dict(zip(result_df['pdf_page'], result_df['schedule']))

    # Add the schedule column to general_df
    general_df[general_schedule_column] = general_df[general_pdf_page_key].map(schedule_map)

    # If we have signals and stageData is available, stage the data for import
    if signals and "stageData" in signals:
        if not use_internal_fieldmap:
            general_df = fieldmap_utils.map_from_field_map(general_df.copy())
        else:
            general_df = general_df.copy()
        import_data = {"general_data": general_df}
        signals["stageData"].emit(import_data)
        print("Data has been staged for import")

    # For debugging
    print(f"Applied schedule to {general_df[general_schedule_column].notna().sum()} rows in general data")

    print(f"Found pipe schedule information for {len(result_df)} pages")
    if save_debug_results:
        result_df.to_excel(debug_results_file)

    general_df.to_excel(general_patch_file)
    return "Done"