import csv, json, ast
import pandas as pd
import ast


# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)

def transform_dataframe(df, item_df):
    # Convert 'tko_id' in df to int if it's not already, handle errors in case of non-convertible values
    print("\n\nDATAFRAME: ", df)

    df['tko_id'] = pd.to_numeric(df['tko_id'], errors='coerce')
    item_df['__uid__'] = pd.to_numeric(item_df['__uid__'], errors='coerce')
    
    # Normalize 'review' field to boolean True or False
    #df['review'] = df['review'].str.lower() == 'true'
    
    # Convert 'review' field to string and normalize to boolean True or False
    df['review_original'] = df['review']  # Preserve original review data for logging
    df['review'] = df['review'].astype(str).str.lower() == 'true'

    # Check for and log any values that were not initially strings
    non_string_reviews = df[df['review_original'] != df['review_original'].astype(str)]
    if not non_string_reviews.empty:
        print("Values defaulted to True in 'review' due to non-string types:")
        print(non_string_reviews['review_original'])

    # Normalize entries where conversion to string failed (setting NaNs to True after conversion attempt)
    df.loc[df['review'].isna(), 'review'] = True


    # Drop duplicate entries based on 'tko_id' and 'classification_field' before pivoting
    df_no_duplicates = df.drop_duplicates(subset=['tko_id', 'classification_field'])

    # Pivot the 'classification_field' to columns with corresponding 'answer' values
    df_pivoted = df_no_duplicates.pivot(index='tko_id', columns='classification_field', values='answer').reset_index()

    # Concatenate all 'answer_explanation' values with the classification_field for each 'tko_id'
    # answer_explanation_combined = df[df['answer_explanation'].astype(bool)].groupby('tko_id').apply(
    #     lambda x: '\n'.join([f"{row['classification_field']}: {row['answer_explanation']}" for _, row in x.iterrows()])
    # ).rename('answer_explanation')
    
    # # Check for non-empty 'answer_explanation' and ensure it's treated as a string
    # df['answer_explanation'] = df['answer_explanation'].fillna('').astype(str)
    # answer_explanation_combined = df[df['answer_explanation'].astype(bool)].groupby('tko_id').apply(
    #     lambda x: '\n'.join([f"{row['classification_field']}: {row['answer_explanation']}" for _, row in x.iterrows()])
    # ).rename('answer_explanation')
    
    try:
        # Handle 'answer_explanation' column
        if 'answer_explanation' in df.columns:
            df['answer_explanation'] = df['answer_explanation'].fillna('').astype(str)
        else:
            df['answer_explanation'] = ''

        # Group and combine 'answer_explanation'
        if 'classification_field' in df.columns:
            explanation_groups = df.groupby('tko_id')['answer_explanation']
            answer_explanation_combined = explanation_groups.apply(
                lambda x: '\n'.join(x[x != ''])
            ).rename('answer_explanation')
        else:
            df['answer_explanation_combined'] = ''
    except Exception as e:
        print(f"\n\nERROR COMBINING ANSWER EXPLANATIONS: {e}")

    # Handle 'review' column
    if 'review' in df.columns:
        df['review'] = pd.to_numeric(df['review'], errors='coerce').fillna(1).astype(bool)
    else:
        df['review'] = True
    # Determine 'review' for each 'tko_id': True if any True, otherwise False
    review_combined = df.groupby('tko_id')['review'].any().rename('review')
    
    # df['review_explanation'] = df['review_explanation'].fillna('').astype(str)
    # review_explanation_combined = df[df['review_explanation'].astype(bool)].groupby('tko_id')['review_explanation'].apply(
    #     lambda x: ' '.join(x.dropna())
    # ).rename('review_explanation')

    try: 
            # Handle 'review' column
        if 'review' in df.columns:
            df['review'] = pd.to_numeric(df['review'], errors='coerce').fillna(1).astype(bool)
        else:
            df['review'] = True

        # Handle 'review_explanation' column
        if 'review_explanation' in df.columns:
            df['review_explanation'] = df['review_explanation'].fillna('').astype(str)
        else:
            df['review_explanation'] = ''

        # Group and combine 'review_explanation'
        if 'classification_field' in df.columns:
            review_groups = df.groupby('tko_id')['review_explanation']
            review_explanation_combined = review_groups.apply(
                lambda x: ' '.join(x[x != ''])
            ).rename('review_explanation')
        else:
            df['review_explanation_combined'] = ''
    except Exception as e:
        print(f"\n\nERROR COMBINING REVIEW EXPLANATIONS: {e}")
        
    # Join the combined fields back to the pivoted DataFrame
    result_df = df_pivoted.join(answer_explanation_combined, on='tko_id')
    result_df = result_df.join(review_combined, on='tko_id')
    result_df = result_df.join(review_explanation_combined, on='tko_id')
    
    # Merge 'material_description' and 'size' from item_df
    result_df = result_df.merge(item_df[['__uid__', 'material_description', 'size']], left_on='tko_id', right_on='__uid__', how='left')

    # You might want to drop the __uid__ column if it's no longer needed
    #result_df.drop(columns=['__uid__'], inplace=True)

    return result_df

def create_dataframe_from_jsonl(filename):
    data = []

    with open(filename, 'r', encoding='utf-8') as file:
        for line in file:
            try:
                response = json.loads(line)
                
                # Extracting "Request Data"
                request_data = response[0]['messages'][1]['content']
                # Assuming the string is now in valid JSON format
                request_data_dict = json.loads(request_data)
                
                # print("\n\nrequest_data -->: ", request_data, "\n\n")
                # print("\n\nrequest_data_dict -->: ", request_data_dict, "\n\n")

                # Extracting "CLS Data"
                cls_data = response[1]['choices'][0]['message']['content']
                cls_data_dict = json.loads(cls_data)
                # print("\n\ncls_data -->: ", cls_data, "\n\n")
                # print("\n\ncls_dict -->: ", cls_data_dict, "\n\n")


                # Combining extracted data into a record
                record = {
                    'tko_id': request_data_dict.get('tko_id', ''),
                    'classification_field': request_data_dict.get('classification field', ''),
                    'answer': cls_data_dict.get('answer', ''),
                    'answer_explanation': cls_data_dict.get('answer explanation', ''),
                    'review': cls_data_dict.get('review', False),  # Assuming Boolean, defaulting to False if not present
                    'review_explanation': cls_data_dict.get('review explanation', '')
                }

                data.append(record)

            except Exception as e:
                print(f"Error processing line: {e}")
    
    # Create and return the DataFrame
    df = pd.DataFrame(data)
    return df

def save_generated_data_to_csv(filename):
    with open(filename, 'r', encoding='utf-8') as file:
        responses = [json.loads(line) for line in file]

    with open('output.csv', 'w', newline='', encoding='utf-8') as csv_file:
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['Request Data', 'CLS Data'])

        for response in responses:
            try:
                # Assuming the first part of the response contains the original data
                original_data = response[0]['messages'][1]['content']

                # Assuming the second part of the response contains the generated bio
                cls_data = response[1]['choices'][0]['message']['content']

                csv_writer.writerow([original_data, cls_data])
                
            except Exception as e:
                print(f"Error processing response: {e}")
                print(f"Response causing error: {response}")

    print("CSV file created successfully.")

# def parse_request_data(content):
#     # Manual parsing of the request_data_content to extract needed values
#     # This is a simplified parser and might need adjustments for edge cases
#     lines = content.split('\n')
#     data = {}
#     for line in lines:
#         if ':' in line:
#             key, value = line.split(':', 1)
#             key = key.strip('" ').lower().replace(' ', '_')
#             value = value.strip().strip('"')
#             data[key] = value
#     return data


# def create_dataframe_from_jsonl(filename):
#     # Prepare a list to hold our extracted data
#     data = []

#     with open(filename, 'r', encoding='utf-8') as file:
#         for line in file:
#             try:
#                 response = json.loads(line)

#                 # Extracting 'Request Data'
#                 request_data_content = response[0]['messages'][1]['content']
                
#                 # print("\n\nRequest_data_content: \n", request_data_content)
#                 # Assuming 'Request Data' is a stringified JSON, convert it to a dict
#                 request_data_dict = parse_request_data(request_data_content)
                


#                 # Extracting 'CLS Data'
#                 cls_data_content = response[1]['choices'][0]['message']['content']
#                 cls_data_dict = parse_request_data(cls_data_content) #json.loads(cls_data_content)
                

#                 print("\n\nRequest_data_dict: \n", request_data_dict)
#                 print("\n\ncls_data_content: \n", cls_data_content)
#                 print("\n\nCLS_Data_dict: \n", cls_data_dict)

#                 # Compile the data for this record into a dictionary
#                 record = {
#                     'tko_id': request_data_dict.get('tko_id', ''),
#                     'classification_field': request_data_dict.get('classification field', ''),
#                     'answer': cls_data_dict.get('answer', ''),
#                     'answer_explanation': cls_data_dict.get('answer explanation', ''),
#                     'review': cls_data_dict.get('review', ''),
#                     'review_explanation': cls_data_dict.get('review explanation', '')
#                 }

#                 # Append the record to our data list
#                 data.append(record)

#             except Exception as e:
#                 print(f"Error processing line: {e}")
#                 continue

#     # Convert the list of data into a DataFrame
#     df = pd.DataFrame(data)
#     return df

################## Edited to work ^^^

# def save_generated_data_to_csv(filename):

#     responses = []

#     with open(filename, 'r', encoding='utf-8') as file:
#         for line in file:
#             data = json.loads(line)
#             responses.append(data)

#     # Create a CSV file for writing
#     with open('output.csv', 'w', newline='', encoding='utf-8') as csv_file:
#         csv_writer = csv.writer(csv_file)

#         # Write the header row
#         csv_writer.writerow(['Original Data', 'Generated Bio'])

#         # Iterate through the specialists and write data to the CSV
#         for response in responses:
#             original_data = response[0]["messages"][1]["content"]
#             try:
#                # Assuming 'response' is a dictionary containing the response data
#                 #generated_bio = response["choices"][0]["message"]["content"]
#                 generated_bio = response[1]["choices"][0]["message"]["tool_calls"][0]["function"]["arguments"]
#             except Exception as e:
#                 # print(f"Error processing response: {e}")
#                 # generated_bio = "Error retrieving generated bio"
#               generated_bio = response[1]["choices"][0]["message"]["content"]

#             # Write data to the CSV file
#             csv_writer.writerow([original_data, generated_bio])

#     print("CSV file created successfully.")
