import os
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
from openpyxl import load_workbook
from sqlalchemy import create_engine, inspect, Table, MetaData, Column, String, Integer, Float, DateTime, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pydantic import BaseModel, Field, field_validator, create_model
from datetime import datetime
import numpy as np

# Import database connection utilities from project
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor, execute_query, get_db_connection

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# We'll still keep a SQLAlchemy URL for schema inspection
DEFAULT_DB_URL = "postgresql://{user}:{password}@{host}:{port}/{database}"


def import_general(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import general data from a workbook into the public.general table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.general"

    # Key columns used to identify existing records
    key_columns = ["project_id", "pdf_id", "pdf_page", "size"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for general from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'pdf_id', 'pdf_page']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            errors = []
            for col in missing_required:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': errors
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['pdf_id'].isna() |
            df['pdf_page'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'pdf_id', 'pdf_page', 'size',
            'length', 'calculated_area', 'calculated_eq_length',
            'elbows_90', 'elbows_45', 'bevels', 'tees', 'reducers',
            'caps', 'flanges', 'valves_flanged', 'valves_welded',
            'cut_outs', 'supports', 'bends', 'field_welds'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'pdf_id', 'pdf_page']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"General data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for general: {e}")
        raise


def import_bom(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import BOM data from a workbook into the public.bom table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.bom"

    # Key columns used to identify existing records
    key_columns = ["project_id", "pdf_id", "pdf_page", "pos", "material_description"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for BOM from {workbook_path}")
    try:
        # Validate workbook path exists
        if not os.path.exists(workbook_path):
            raise FileNotFoundError(f"Workbook not found: {workbook_path}")

        # Load the Excel data
        try:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            if not isinstance(df, pd.DataFrame):
                raise ValueError("Failed to read Excel file as DataFrame")
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return {
                'total_rows': 0,
                'valid_rows': 0,
                'error_rows': 0,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Error reading Excel file: {str(e)}"}]
            }

        original_rows = len(df)

        # Clean up the data
        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'material_description']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['material_description'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'pdf_id', 'pdf_page', 'profile_id', 'rfq_ref_id', 'gen_ref_id',
            'size1', 'size2', 'quantity', 'calculated_eq_length', 'calculated_area'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Handle boolean columns
        bool_cols = ['deleted', 'ignore_item']
        for col in bool_cols:
            if col in df.columns:
                # Convert various values to boolean
                df[col] = df[col].map({
                    'yes': True, 'y': True, 'true': True, 'True': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    'no': False, 'n': False, 'false': False, 'False': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    pd.NA: False, None: False, np.nan: False
                }, na_action='ignore')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'pdf_id', 'pdf_page', 'profile_id', 'rfq_ref_id', 'gen_ref_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"BOM data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for BOM: {e}")
        return {
            'total_rows': 0,
            'valid_rows': 0,
            'error_rows': 0,
            'inserted': 0,
            'updated': 0,
            'errors': [{'error': f"Error processing BOM data: {str(e)}"}]
        }


def import_atem_rfq_0(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import RFQ data from a workbook into the public.atem_rfq table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.atem_rfq"

    # Key columns used to identify existing records
    key_columns = ["project_id", "material_description", "size1", "size2"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for RFQ from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'material_description']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['material_description'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'profile_id', 'rfq_input_id', 'size1', 'size2', 'quantity',
            'calculated_eq_length', 'calculated_area'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Explicitly handle the 'size' column as string
        if 'size' in df.columns:
            # Convert any non-string values to string representations
            # This includes handling NaN, numeric values, etc.
            df['size'] = df['size'].apply(lambda x: str(x) if pd.notna(x) else '')
            # Replace 'nan' string with empty string
            df['size'] = df['size'].replace('nan', '')

        # Handle boolean columns
        bool_cols = ['deleted', 'ignore_item', 'mapping_not_found']
        for col in bool_cols:
            if col in df.columns:
                # Convert various values to boolean
                df[col] = df[col].map({
                    'yes': True, 'y': True, 'true': True, 'True': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    'no': False, 'n': False, 'false': False, 'False': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    pd.NA: False, None: False, np.nan: False
                }, na_action='ignore')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'profile_id', 'rfq_input_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        ##############################
        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # DEBUG: Add these lines before calling importer.import_workbook

        # Export the dataframe to a CSV for inspection
        debug_export_path = os.path.join(os.path.dirname(workbook_path), 'debug_rfq_export.csv')
        logger.info(f"Exporting debug data to {debug_export_path}")
        df.to_csv(debug_export_path, index=False)

        # Debug size column specifically
        if 'size' in df.columns:
            size_na_count = df['size'].isna().sum()
            size_empty_count = (df['size'] == '').sum()
            print(f"'size' column stats before database commit:")
            print(f" - NaN values: {size_na_count}")
            print(f" - Empty strings: {size_empty_count}")
            print(f" - Total rows: {len(df)}")
            print(f" - Unique values: {df['size'].nunique()}")
            print(f" - First 10 values: {df['size'].head(10).tolist()}")

            # Inspect size column in detail
            size_debug_df = pd.DataFrame()
            size_debug_df['size_value'] = df['size']
            size_debug_df['value_type'] = df['size'].apply(lambda x: type(x).__name__)
            size_debug_df['is_na'] = df['size'].isna()
            size_debug_df['is_empty'] = df['size'] == ''
            size_debug_df.to_csv(os.path.join(os.path.dirname(workbook_path), 'size_column_debug.csv'), index=False)

        # Continue with original code - call import_workbook
        ##############################

        print(f"EXPORTING DEBUG RFQ...")
        df.to_csv(os.path.join(os.path.dirname(workbook_path), 'debug_rfq.csv'), index=False)
        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"RFQ data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for RFQ: {e}")
        raise

def import_general(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.general table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "pdf_id", "pdf_page"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "general", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size", "length", "calculated_area",
            "calculated_eq_length", "elbows_90", "elbows_45", "bevels",
            "tees", "reducers", "caps", "flanges", "valves_flanged",
            "valves_welded", "cut_outs", "supports", "bends", "field_welds"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'general')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'general',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.general table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_bom_old(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.bom table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "bom", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size1", "size2", "quantity",
            "length", "calculated_area", "calculated_eq_length"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'bom')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # IMPORTANT: Set session_replication_role to disable triggers
                cursor.execute("SET session_replication_role = 'replica';")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Commit setting of session variables within the transaction
                connection.commit()

                # Get fresh connection from the engine with session variables properly set
                connection = engine.connect().connection

                # Now do the insert
                df_pg.to_sql(
                    'bom',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                # Reset session_replication_role before committing
                cursor = connection.cursor()
                cursor.execute("SET session_replication_role = 'origin';")
                connection.commit()
        except Exception as e:
            connection.rollback()
            print(f"Error during database insertion: {e}")
            raise e
        finally:
            try:
                # Always reset session_replication_role to be safe
                cursor = connection.cursor()
                cursor.execute("SET session_replication_role = 'origin';")
                connection.commit()
            except:
                pass
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.bom table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_atem_rfq(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.atem_rfq table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "size1", "size2", "quantity",
            "calculated_eq_length", "calculated_area"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "rfq_input_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'atem_rfq',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }
