import os
import fitz
import time

import pandas as pd
from functools import partial
from multiprocessing import Pool

from src.atom.ocr.core.pdf_processor import process_page


def get_image_folder_paths(output_images_path):
    bom_path = os.path.join(output_images_path, "bom")
    general_path = os.path.join(output_images_path, "general")
    spec_path = os.path.join(output_images_path, "spec")
    spool_path = os.path.join(output_images_path, "spool")
    ifc_path = os.path.join(output_images_path, "ifc")
    weld_path = os.path.join(output_images_path, "weld")
    iso_path = os.path.join(output_images_path, "iso")
    generic_1_path = os.path.join(output_images_path, "generic_1")
    generic_2_path = os.path.join(output_images_path, "generic_2")

    return {
        "bom": bom_path,
        "general": general_path,
        "spec": spec_path,
        "spool": spool_path,
        "ifc": ifc_path,
        "weld": weld_path,
        "iso": iso_path,
        "generic_1": generic_1_path,
        "generic_2": generic_2_path
    }


def extract_images_pdf(pdf_path,
                       roi_payload: dict,
                       output_images_path='outputImages',
                       dpi=500,
                       gen_field_list=None,
                       page_list=[],
                       width_limit=None):
    print(f"Processing at '{dpi}' DPI. Width limit set to {width_limit}")

    # Create directory if it doesn't exist (General, BOM)

    os.makedirs(output_images_path, exist_ok=True)

    # Ensure subdirectories exist
    bom_path = os.path.join(output_images_path, "bom")
    general_path = os.path.join(output_images_path, "general")
    spec_path = os.path.join(output_images_path, "spec")
    spool_path = os.path.join(output_images_path, "spool")
    ifc_path = os.path.join(output_images_path, "ifc")
    weld_path = os.path.join(output_images_path, "weld")
    iso_path = os.path.join(output_images_path, "iso")
    generic_1_path = os.path.join(output_images_path, "generic_1")
    generic_2_path = os.path.join(output_images_path, "generic_2")

    paths = [
        bom_path,
        general_path,
        spec_path,
        spool_path,
        ifc_path,
        weld_path,
        iso_path,
        generic_1_path,
        generic_2_path
    ]
    for path in paths:
        print(f"Created directory {path}")
        os.makedirs(path, exist_ok=True)

    # Clear .png files from all folders
    for folder in paths:
        for file in os.listdir(folder):
            if file.lower().endswith('.png'):
                os.remove(os.path.join(folder, file))
            print(f"Cleared images in {folder}")
        else:
            os.makedirs(folder, exist_ok=True)
            print(f"Created directory {folder}")

    start_time = time.time()

    doc = fitz.open(pdf_path)

    if not page_list:
        pages_to_process = [i for i in range(0, doc.page_count)]
    else:
        pages_to_process = page_list

    total_pages = len(pages_to_process)

    # Prepare the partial function for multiprocessing
    # page_num, pdf_path, converted_fields, output_images_path, dpi, multi_roi, page_group_dict
    process_page_partial = partial(process_page,
                                   pdf_path=pdf_path,
                                   roi_payload=roi_payload,
                                   output_images_path=output_images_path,
                                   dpi=dpi,
                                   gen_field_list=gen_field_list,
                                   width_limit=width_limit)

    with Pool() as pool:
        results = pool.map(process_page_partial, pages_to_process)

    all_pages_data_storage, successful_extractions = zip(*results)
    successful_extractions = sum(successful_extractions)

    end_time = time.time()
    total_time = end_time - start_time
    pages_per_second = total_pages / total_time if total_time > 0 else 0

    print(f"\nProcessed {total_pages} pages in {total_time:.2f} seconds.")
    print(f"Processing speed was {pages_per_second:.2f} pages per second.")

    df = pd.DataFrame(all_pages_data_storage)
    df.to_excel('debug/ocr/outputData.xlsx', index=False)
    print("Data exported to 'outputData.xlsx'.")

    # Removes any empty directories on completion
    for folder in paths:
        for file in os.listdir(folder):
            break
        else:
            # empty directory
            os.rmdir(folder)
            print(f"Removed empty directory {folder}")
            return

    # Calculate expected PNG files more accurately
    expected_pngs = 0
    for page_num in pages_to_process:
        group_num = page_group_dict.get(page_num + 1)  # +1 because page_num is 0-indexed
        if group_num is not None and str(group_num) in converted_fields:
            expected_pngs += len(converted_fields[str(group_num)])

    print(f"Expected PNG files:  {expected_pngs}") #{len(converted_fields) * total_pages}")
    print(f"Successfully extracted PNG files: {successful_extractions}")
    if expected_pngs != successful_extractions:
        print("WARNING: The number of successfully extracted PNG files does not match the expected count!")

    return df

def clear_images(output_images_path='outputImages'):
    bom_path = os.path.join(output_images_path, "BOM")
    general_path = os.path.join(output_images_path, "General")
    iso_path = os.path.join(output_images_path, "ISO")

    for path in [bom_path, general_path, iso_path]:
        if os.path.exists(path):
            for file in os.listdir(path):
                if file.lower().endswith('.png'):
                    os.remove(os.path.join(path, file))
            print(f"Cleared images in {path}")
        else:
            os.makedirs(path)
            print(f"Created directory {path}")
