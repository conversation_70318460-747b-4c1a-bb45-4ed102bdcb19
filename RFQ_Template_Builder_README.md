# RFQ Template Builder - Usage Guide

## Overview

The RFQ Template Builder (`rfq_template_builder.py`) consolidates your multi-step workflow into a single, configurable script. Instead of running multiple Python files separately, you can now run one file with customizable flags to control which steps execute.

## Quick Start

1. **Update Configuration**: Edit the configuration section at the top of `rfq_template_builder.py`
2. **Set Input Path**: Update `INPUT_EXCEL_PATH` to point to your Excel file
3. **Set Output Path**: Update `OUTPUT_DIR` to your desired output location
4. **Configure Steps**: Set step flags to `True`/`False` to enable/disable each step
5. **Run**: Execute `python rfq_template_builder.py`

## Configuration Section

```python
# INPUT/OUTPUT PATHS - UPDATE THESE TO YOUR ACTUAL PATHS
INPUT_EXCEL_PATH = r"C:\path\to\your\input\file.xlsx"
OUTPUT_DIR = r"C:\path\to\your\output\directory"

# STEP FLAGS - Set to True/False to enable/disable each step
RUN_STEP1_CREATE_COMBINED = True    # Create combined workbook
RUN_STEP2_MATERIAL_CHECK = True     # Check materials in database  
RUN_STEP3_NORMALIZE = True          # Normalize descriptions
RUN_STEP4_RFQ_TEMPLATE = True       # Create final RFQ template

# STEP 1 OPTIONS - What to combine from input file
COMBINE_GENERAL = False
COMBINE_BOM = True
COMBINE_SPEC = False
COMBINE_RFQ = True

# STEP 4 OPTIONS - RFQ Template settings
ENABLE_VALIDATION = True        # Add dropdown validation
DEDUPLICATE_MATERIALS = True    # Remove duplicate material descriptions
```

## Workflow Steps

### Step 1: Create Combined Workbook
- **Function**: `create_combined_workbook` from `misc_help.py`
- **Purpose**: Combines data from your input Excel file into a standardized format
- **Output**: `Stage1_Combined_YYYYMMDD_HHMMSS.xlsx`
- **Options**: Choose which sheet types to combine (BOM, RFQ, General, Spec)

### Step 2: Material Check
- **Function**: Database functions from `src/atom/pg_database/misc.py`
- **Purpose**: Validates material descriptions against verified materials database
- **Output**: `Stage2_Material_Check.xlsx`
- **Note**: Requires database connection; will skip if unavailable

### Step 3: Normalize Descriptions
- **Function**: `normalize_description` from `unit_tests/normalize_description.py`
- **Purpose**: Extracts and normalizes material properties (ASTM, schedule, rating, etc.)
- **Output**: `Stage3_Normalized.xlsx`
- **Features**: Adds metadata tags, review flags, and extracted properties

### Step 4: Create RFQ Template
- **Function**: `load_data_to_template` from `unit_tests/rfq_template.py`
- **Purpose**: Creates final RFQ template with validation dropdowns
- **Output**: `RFQ_Template_Final_YYYYMMDD_HHMMSS.xlsx`
- **Features**: 47 columns, dropdown validation, deduplication

## Key Features

### First Worksheet Approach
- Follows data upload auditing pattern
- Always uses the first worksheet regardless of sheet name
- Handles various Excel file formats

### Robust Error Handling
- Graceful import management with fallback paths
- Continues workflow even if individual steps fail
- Detailed error messages and progress tracking

### Configurable Execution
- Enable/disable any step with simple flags
- Customize options for each step
- Pass DataFrames between steps for efficiency

### Data Validation
- Input file existence checking
- Column validation for required fields
- Automatic directory creation

## Common Use Cases

### Full Workflow
```python
RUN_STEP1_CREATE_COMBINED = True
RUN_STEP2_MATERIAL_CHECK = True
RUN_STEP3_NORMALIZE = True
RUN_STEP4_RFQ_TEMPLATE = True
```

### Skip Database Check (No DB Access)
```python
RUN_STEP1_CREATE_COMBINED = True
RUN_STEP2_MATERIAL_CHECK = False  # Skip if no database
RUN_STEP3_NORMALIZE = True
RUN_STEP4_RFQ_TEMPLATE = True
```

### Only Normalization and Template
```python
RUN_STEP1_CREATE_COMBINED = False
RUN_STEP2_MATERIAL_CHECK = False
RUN_STEP3_NORMALIZE = True
RUN_STEP4_RFQ_TEMPLATE = True
```

### Only Create Template from Existing Data
```python
RUN_STEP1_CREATE_COMBINED = False
RUN_STEP2_MATERIAL_CHECK = False
RUN_STEP3_NORMALIZE = False
RUN_STEP4_RFQ_TEMPLATE = True
```

## Output Files

The script creates timestamped files in your output directory:

1. `Stage1_Combined_YYYYMMDD_HHMMSS.xlsx` - Combined workbook
2. `Stage2_Material_Check.xlsx` - Material validation results
3. `Stage3_Normalized.xlsx` - Normalized descriptions with metadata
4. `RFQ_Template_Final_YYYYMMDD_HHMMSS.xlsx` - Final RFQ template

## Requirements

- Python with pandas, xlsxwriter, openpyxl
- Access to the referenced modules:
  - `misc_help.py`
  - `src/atom/pg_database/misc.py`
  - `unit_tests/normalize_description.py`
  - `unit_tests/rfq_template.py`
- Input Excel file with `material_description` column
- Database connection (optional, for Step 2)

## Troubleshooting

### Import Errors
- The script includes fallback import paths
- Check that all referenced files are in the correct locations
- Verify Python path includes necessary directories

### Database Connection Issues
- Step 2 will automatically skip if database is unavailable
- Check database configuration in `DatabaseConfig()`
- Ensure network access to database server

### File Path Issues
- Use raw strings (r"path") for Windows paths
- Ensure input file exists and is readable
- Verify output directory is writable

### Memory Issues
- Large Excel files may require more memory
- Consider processing in smaller batches
- Close other applications if needed

## Support

For issues or questions:
1. Check the console output for detailed error messages
2. Verify all configuration paths are correct
3. Ensure all required modules are available
4. Test with a smaller sample file first
