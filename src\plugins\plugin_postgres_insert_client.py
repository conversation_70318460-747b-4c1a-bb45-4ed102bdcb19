import pandas as pd
from datetime import datetime

from src.atom.pg_database import pg_connection
from src.atom.pg_database.schemas.base.create_base_tables import SCHEMA_CLIENTS


def plugin_postgres_insert_client(client_id: int = 0,
                                 client_name: str = "",
                                 contact_name: str = "",
                                 contact_email: str = "",
                                 contact_phone: str = "",
                                 address: str = ""):
    """
    Inserts a new client into the PostgreSQL database.

    Args:
        client_id (int): Client ID (must be >= 1 if specified, or 0 for auto-increment)
        client_name (str): Name of the client (required)
        contact_name (str): Name of the contact person
        contact_email (str): Email of the contact person
        contact_phone (str): Phone number of the contact person
        address (str): Address of the client

    Returns:
        bool: True if successful, False otherwise
        int: Client ID of the inserted/updated client
    """
    print("Inserting new client into PostgreSQL database.")

    created_at = datetime.now()
    updated_at = datetime.now()

    # Validate inputs
    if not client_name:
        print("Error: client_name is required.")
        return False, 0

    if client_id != 0:
        try:
            client_id = int(client_id)
            if client_id < 1:
                print("Error: If specified, client_id must be >= 1.")
                return False, 0
        except (ValueError, TypeError):
            print("Error: client_id must be an integer.")
            return False, 0

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")
            print(f"Connection autocommit status: {conn.autocommit}")

            # Create cursor
            with conn.cursor() as cursor:
                if client_id > 0:
                    # Check if client_id already exists
                    cursor.execute("SELECT id FROM public.atem_clients WHERE id = %s", (client_id,))
                    if cursor.fetchone():
                        raise Exception(f"Client with ID {client_id} already exists. Updating not supported.")
                        print(f"Client with ID {client_id} already exists. Updating instead of inserting.")

                        # Update existing client
                        update_query = """
                        UPDATE public.atem_clients
                        SET
                            client_name = %s,
                            contact_name = %s,
                            contact_email = %s,
                            contact_phone = %s,
                            address = %s,
                            updated_at = %s
                        WHERE id = %s
                        RETURNING id
                        """

                        cursor.execute(update_query, (
                            client_name,
                            contact_name,
                            contact_email,
                            contact_phone,
                            address,
                            updated_at,
                            client_id
                        ))

                        result = cursor.fetchone()
                        print(f"Client updated successfully. Client ID: {result[0]}")
                        conn.commit()
                        return True, result[0]

                # Check if client_name already exists (unique constraint)
                cursor.execute("SELECT id FROM public.atem_clients WHERE client_name = %s", (client_name,))
                existing_client = cursor.fetchone()
                if existing_client:
                    print(f"Client with name '{client_name}' already exists (ID: {existing_client[0]}). Updating instead of inserting.")

                    # Update existing client
                    update_query = """
                    UPDATE public.atem_clients
                    SET
                        contact_name = %s,
                        contact_email = %s,
                        contact_phone = %s,
                        address = %s,
                        updated_at = %s
                    WHERE client_name = %s
                    RETURNING id
                    """

                    cursor.execute(update_query, (
                        contact_name,
                        contact_email,
                        contact_phone,
                        address,
                        updated_at,
                        client_name
                    ))

                    result = cursor.fetchone()
                    print(f"Client updated successfully. Client ID: {result[0]}")
                    conn.commit()
                    return True, result[0]

                # Insert new client
                if client_id > 0:
                    # Insert with specific ID
                    insert_query = """
                    INSERT INTO public.atem_clients (
                        id, client_name, contact_name, contact_email, contact_phone,
                        address, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    RETURNING id
                    """

                    cursor.execute(insert_query, (
                        client_id,
                        client_name,
                        contact_name,
                        contact_email,
                        contact_phone,
                        address,
                        created_at,
                        updated_at
                    ))
                else:
                    # Insert with auto-increment ID
                    insert_query = """
                    INSERT INTO public.atem_clients (
                        client_name, contact_name, contact_email, contact_phone,
                        address, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s
                    )
                    RETURNING id
                    """

                    cursor.execute(insert_query, (
                        client_name,
                        contact_name,
                        contact_email,
                        contact_phone,
                        address,
                        created_at,
                        updated_at
                    ))

                result = cursor.fetchone()

                # Add explicit commit to ensure data is saved
                conn.commit()

                # Verify the insert worked by querying the database
                verify_query = "SELECT * FROM public.atem_clients WHERE id = %s"
                cursor.execute(verify_query, (result[0],))
                verification = cursor.fetchone()

                if verification:
                    print(f"Verification successful - Client found in database: {verification}")
                else:
                    print(f"WARNING: Verification failed - Client with ID {result[0]} not found after insert!")

                print(f"Client inserted successfully. Client ID: {result[0]}")
                return True, result[0]

    except Exception as e:
        print(f"Failed to insert client data: {e}")
        import traceback
        traceback.print_exc()
        return False, 0