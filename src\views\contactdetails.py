from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSizePolicy, QHeaderView,
                               QLabel, QPushButton, QToolBar, QSpacerItem, QMenu,
                               QSplitter, QMessageBox)
from PySide6.QtCore import Qt, QSize

addresses = [
    ("Tyler Eiland", "<EMAIL>"),
    ("<PERSON>all", "C.Mc<PERSON><EMAIL>"),
]

class ContactDetails(QWidget):

    def __init__(self, parent) -> None:
        super().__init__()
        self.setObjectName("popup")

        self.setLayout(QVBoxLayout())
        # self.layout().setSpacing(0)
        # self.layout().setContentsMargins(0, 0, 0, 0)
        label = QLabel("Contact", self)
        self.layout().addWidget(label)

        for name, addr in addresses:
            label = QLabel(addr, self)
            label.setText(f"<a href='{addr}'>{name} &lt;{addr}&gt;</a>")
            label.setTextInteractionFlags(Qt.TextBrowserInteraction)
            label.setToolTip(f"mailto: {addr}")
            label.linkActivated.connect(self.onLinkActivated)
            label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
            label.setMaximumHeight(64)
            self.layout().addWidget(label)

        temp = QLabel("", self) # TODO spacer
        self.layout().addWidget(temp)

        self.show()
        self.setMinimumSize(QSize(600, 400))
        self.setWindowTitle("Contact Details")
        self.setWindowIcon(parent.windowIcon())

    def onLinkActivated(self, event):
        from src.views.supportticket import mailto
        mailto(address=event)