# extract_tables.py
import pprint, ast, json
import sys, os, re
from typing import Annotated
#from tkinter.tix import COLUMN
import fitz  # PyMuPDF fitz uses points coordinate system.
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import numpy as np
import statistics
from statistics import mean, stdev
from src.app_paths import getDataTempPath

from src.utils.logger import logger

debug_mode= False # Export pages as xlsx files
debug_row_content = False # Debug row information at the end of detect_bom_rows

# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)


####
# Set the log level
# if debug_mode:
    # logger.setLevel(logging.DEBUG)
    #
    # # Create a console handler
    # console_handler = logging.StreamHandler()
    #
    # # Set the log level for the handler
    # console_handler.setLevel(logging.DEBUG)
    #
    # # Create formatters and add it to the handler
    # log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # console_handler.setFormatter(log_format)
    #
    # # Add the handler to the logger
    # logger.addHandler(console_handler)
###

def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None

def filter_data_within_table(page_num, raw_data, rect, table_type):
    def is_within_rect(coords, rect):
        # # Convert string representation of coordinates to a tuple of floats
        # coords = eval(coords_str)
        # # Create a Rect object from the coordinates
        # item_rect = fitz.Rect(coords)
        # # Check if the item's rectangle intersects with the table rectangle
        # return rect.intersects(item_rect)

        try:
            # If coords is already a tuple, use it directly
            if isinstance(coords, tuple):
                item_rect = fitz.Rect(coords)
            else:
                # If it's a string, try to evaluate it
                coords = ast.literal_eval(coords)
                item_rect = fitz.Rect(coords)

            # Check if the item's rectangle intersects with the table rectangle
            return rect.intersects(item_rect)

        except (SyntaxError, ValueError, TypeError) as e:
            print()
            logger.warning(f"Warning: Unable to parse coordinates on page {page_num + 1}: Coordinates: {coords}, Rect: {rect}. Error: {e}", exc_info=True)
            return False

    # Apply the filter to the DataFrame
    filtered_data = raw_data[raw_data['coordinates2'].apply(lambda x: is_within_rect(x, rect))]

    #debug_raw_with_error = pd.DataFrame(raw_data)

    # Rename the columns
    column_mapping = {
        "sys_path": "PdfPath",
        "pdf_page": "PdfPage",
        "value": "Text",
        "coordinates2": "Coordinates",
        "flags": "Flags",
        "createdDate": "CreationDate",
        "modDate": "ModDate"
    }

    filtered_data = filtered_data.rename(columns=column_mapping)

    # Ensure 'Coordinates' column contains tuples, not strings
    filtered_data['Coordinates'] = filtered_data['Coordinates'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)

    # try:
    #     # Convert 'Fontsize' to numeric
    #     filtered_data['FontSize'] = pd.to_numeric(filtered_data['FontSize'], errors='coerce')
    # except Exception as e:
    #     logger.error(f"Error setting 'FontSize' to numeric. {e}", exc_info=True)

    # Add empty columns
    new_columns = ["Type", "Subject", "Contents", "outlierScope"]
    for col in new_columns:
        filtered_data[col] = ""

    # Assign table_type to outlierScope column
    filtered_data["outlierScope"] = table_type

    return filtered_data


def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def has_non_standard_characters(text):
    return any(ord(char) > 127 for char in text)

def apply_replacement_function(df):
    return df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)

# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        # if non_standard in text:
        #     print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text

def adjust_bbox_90_swapped(bbox, rotation_matrix):
    """
    Adjust a bounding box for a 90-degree rotation by applying the rotation matrix
    and then swapping the x and y coordinates.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1), with x and y coordinates swapped.
    """
    #logger.debug("--> adjust_bbox_90_swapped accessed")
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Swap the coordinates directly, assigning transformed y to x and x to y
    # This corrects for the coordinate swap that occurs with a 90-degree rotation
    x0, y0 = p1_transformed.y, p1_transformed.x
    x1, y1 = p2_transformed.y, p2_transformed.x

    # It's important to ensure x0 < x1 and y0 < y1 for consistency
    adjusted_bbox = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_bbox

def adjust_clip_for_90_degree_rotation(rect, page):
    """
    Adjust a clipping rectangle for a 90-degree clockwise rotation.

    Parameters:
    - rect: The original clipping rectangle (x0, y0, x1, y1).
    - page: The page object for accessing dimensions.

    Returns:
    - The adjusted clipping rectangle as a tuple (x0, y0, x1, y1), correctly positioned for the rotated page.
    """

    logger.debug("--> adjust_clip_for_90_degree_rotation accessed")
    page_height = page.rect.height
    x0, y0 = rect[1], page_height - rect[2]
    x1, y1 = rect[3], page_height - rect[0]
    adjusted_clip = (min(x0, x1), min(y0, y1), max(x0, x1), max(y0, y1))
    return adjusted_clip

# ^^^^^ General Functions ^^^^^


def analyze_and_filter_outliers(page_num, raw_table_df, table_type, font_size_tolerance=0.5, is_annot=True): # Changed

    if is_annot:
        return pd.DataFrame(), raw_table_df

    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Initialize DataFrames
    red_text_df = pd.DataFrame()
    non_red_text_df = pd.DataFrame()
    text_outliers_df = pd.DataFrame()

    # Ensure 'color' column is a tuple of three values for the entire DataFrame
    raw_table_df['color'] = raw_table_df['color'].apply(lambda x: tuple(x) if isinstance(x, (list, np.ndarray)) and len(x) == 3 else (0, 0, 0))

    # Separate annotations and text data
    annotations_df = raw_table_df[raw_table_df['type'] != 'Text'].copy()
    text_df = raw_table_df #[raw_table_df['type'] == 'Text']

    # Add annotations to outliers immediately
    annotations_df['drop_reason'] = 'Annotation'

    # Separate red text and non-red text
    red_text_df = text_df[text_df['color'] == red_color]
    non_red_text_df = text_df[text_df['color'] != red_color]

    # Add this line to filter out 'Times-Roman' font - DEBUG TEMPORARY! Lines contains hidden characters!!
    non_red_text_df = non_red_text_df[non_red_text_df['Font'] != 'Times-Roman']

    # non_red_text_df['FontSize'] = pd.to_numeric(non_red_text_df['FontSize'], errors='coerce')

    # Find common attributes from non-red text
    if len(non_red_text_df) > 0:
        common_font = non_red_text_df['Font'].mode()[0]
        common_font_size = non_red_text_df['FontSize'].mode()[0]
        common_font_color = non_red_text_df['color'].mode()[0]
    elif len(text_df) > 0:
        # If all text is red, use the most common attributes from all text
        common_font = text_df['Font'].mode()[0]
        common_font_size = text_df['FontSize'].mode()[0]
        common_font_color = text_df['color'].mode()[0]
    else:
        # Both are blank
        common_font, common_font_size, common_font_color = None, None, None

    print(f"\n\nPAGE {page_num + 1}:\n COMMON FONT: {common_font} \n SIZE: {common_font_size} \n COLOR: {common_font_color}")

    # Create separate conditions for each outlier reason
    if common_font:
        font_condition = non_red_text_df['Font'] != common_font
        size_condition = abs(non_red_text_df['FontSize'] - common_font_size) > font_size_tolerance

        # Combine conditions
        outlier_condition = font_condition | size_condition

        # Create a DataFrame for outliers with reasons
        text_outliers_df = non_red_text_df[outlier_condition].copy()
        text_outliers_df['drop_reason'] = ''
        text_outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
        text_outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
        text_outliers_df['drop_reason'] = text_outliers_df['drop_reason'].str.rstrip('; ')

    # Combine text outliers with annotations
    outliers_df = pd.concat([text_outliers_df, annotations_df])


    # Regular data is non-red text that's not an outlier, plus all red text
    if common_font is not None:
        regular_data_df = pd.concat([non_red_text_df[~outlier_condition], red_text_df])
    else:
        regular_data_df = text_df  # If no common font, all text data is considered regular

    # regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")
    # outliers_df.to_excel(f"Outliers DF Pg {page_num + 1}.xlsx")

    # print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df



def export_large_data_to_excel(df, filename, directory):
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

    excel_filename = os.path.join(directory, filename)

    if len(df) > 0:
        logger.info(f"Creating Workbook: {filename} in {directory} with {len(df)} rows")
        wb = Workbook()
        ws = wb.active

        # Adding header row (column names)
        ws.append(list(df.columns))

        ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
        hyperlink_font = Font(color="0000FF", underline="single")

        for index, row in df.iterrows():
            row_values = [str(value) if not is_scalar(value) else value for value in row]
            ws.append(row_values)

            # Create a hyperlink for 'AIS_LINK'
            if ais_link_col_idx:
                ais_link_value = row['AIS_LINK']

                # Use the relative path directly
                if ais_link_value:
                    hyperlink = ais_link_value
                    cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                    cell.hyperlink = hyperlink
                    cell.font = hyperlink_font

        try:
            wb.save(excel_filename)
            logger.info(f">>> Data exported and saved to: {excel_filename}")
        except PermissionError as e:
            logger.error(f"PermissionError: {e}")
            logger.error(f"Failed to write to {excel_filename}. The file might be open or locked.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
    else:
        logger.warning(f"The DataFrame for {filename} is empty.")

# ^^^^^ General Functions ^^^^^
# Debugging: Check for non-standard characters before and after replacement
def debug_non_standard_characters(df, step):
    for col in df.columns:
        non_standard = df[df[col].apply(lambda x: has_non_standard_characters(str(x)))]
        #if not non_standard.empty:
            #print(f"Non-standard characters found in column '{col}' at step '{step}':")
            #print(non_standard)

def sort_by_y0(df):
    if 'Coordinates' in df.columns:
        # Create a new column 'y0' to hold the y0 values from the 'Coordinates' tuples
        df['y0'] = df['Coordinates'].apply(lambda coord: coord[1])
        # Sort the DataFrame by the 'y0' column
        df = df.sort_values(by='y0')
        # Drop the temporary 'y0' column
        df = df.drop(columns=['y0'])
        return df
    else:
        logger.warning("Column 'Coordinates' does not exist in the DataFrame.")
        return df

def get_table_coordinates(converted_roi_payload):
    identified_tables = {}  # Dictionary to hold coordinates and column details for each table type

    try:
        for item in converted_roi_payload:
            table_type = item.get('columnName', '').lower()
            # Check if the current item is one of the identified table types and has table coordinates
            if table_type in ['bom', 'spec', 'spool'] and 'tableCoordinates' in item:
                # logger.debug(f"Found '{table_type}' item with table coordinates")

                # Extract table coordinates
                table_coords = item['tableCoordinates']

                # Initialize list to hold column coordinates for the current table
                column_coords = []
                # Extract column coordinates
                if 'tableColumns' in item:
                    for column_dict in item['tableColumns']:
                        for column_name, coords in column_dict.items():
                            coords['columnName'] = column_name  # Add columnName for compatibility
                            column_coords.append(coords)

                # Get the value of 'headersSelected'
                headers_selected = item.get('headersSelected', False)

                # print("\n\nHEADERS SELECTED", headers_selected)

                # Correctly structure the coordinates and column details for compatibility
                identified_tables[table_type] = (table_coords, column_coords, headers_selected)

    except Exception as e:
        logger.error(f"Error getting table coordinates: {e}", exc_info=True)


    return identified_tables # <-- IN USE

def combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5):
    def are_y_coords_close(coord1, coord2, tolerance):
        return abs(coord1[1] - coord2[1]) <= tolerance and abs(coord1[3] - coord2[3]) <= tolerance

    def should_combine(word1, word2):
        return word2['Coordinates'][0] - word1['Coordinates'][2] <= x_tolerance

    # Sort the dataframe by y0 and then x0 coordinates
    sorted_df = raw_df.sort_values(by=['Coordinates'], key=lambda x: [coord[1] for coord in x])

    combined_texts = {}
    current_line = []

    def process_line(line):
        line.sort(key=lambda x: x[1]['Coordinates'][0])  # Sort by x0
        combined_text = line[0][1]['Text']
        start_index = 0
        for j in range(1, len(line)):
            if should_combine(line[j-1][1], line[j][1]):
                combined_text += ' ' + line[j][1]['Text']
            else:
                for x in line[start_index:j]:
                    combined_texts[x[0]] = combined_text
                start_index = j
                combined_text = line[j][1]['Text']
        for x in line[start_index:]:
            combined_texts[x[0]] = combined_text

    for i, row in sorted_df.iterrows():
        if not current_line or are_y_coords_close(current_line[-1][1]['Coordinates'], row['Coordinates'], y_tolerance):
            current_line.append((i, row))
        else:
            process_line(current_line)
            current_line = [(i, row)]

    # Process the last line
    if current_line:
        process_line(current_line)

    # Create a new dataframe with the combined texts
    combined_df = pd.DataFrame.from_dict(combined_texts, orient='index', columns=['Combined_Text'])

    # Update or add the 'Combined_Text' column efficiently
    if 'Combined_Text' in sorted_df.columns:
        sorted_df.update(combined_df)
    else:
        sorted_df = sorted_df.join(combined_df)

    return sorted_df

def is_keyword(text, prefixes, keywords, max_words=3):
    '''
    Checks keywords like Material Labels/Descriptors, Install Type (Shop,Field etc)
    to determine whether or not to drop/ignore them when creating the table structure
    '''

    # Convert text to uppercase for case-insensitive matching
    text = text.strip().upper()

    # Split the text into words
    words = text.split()

    # Check if the text starts with any of the prefixes and has <= max_words
    if len(words) <= max_words and any(text.startswith(prefix) for prefix in prefixes):
        #print(f"\n\nDEBUG: '{text}' is a keyword (prefix match and short)")
        return True

    # Check if the entire text matches any of the keywords
    if text in keywords:
        #print(f"DEBUG: '{text}' is a keyword (exact match)")
        return True

    return False

def word_count(text):
    return len(text.split())

# def starts_with_prefix_and_short(text, prefixes, max_words=1):
#     result = any(text.startswith(prefix) for prefix in prefixes) and word_count(text) <= max_words
#     if result:
#         print(f"\n\nPrefixes: {prefixes}")
#         print(f"DEBUG: '{text}' starts with prefix and is short")
#     return result

def starts_with_prefix_and_short(text, prefixes, max_words=2):
    words = text.split()
    result = any(text.startswith(prefix) for prefix in prefixes) and len(words) <= max_words
    # if result:
    #     print(f"DEBUG: '{text}' starts with prefix {[p for p in prefixes if text.startswith(p)]} and has {len(words)} words")
    return result

def keyword_in_first_words(text, keywords, first_n_words=5, min_total_words=4):
    words = text.split()

    # Check for exact match first
    if text in keywords:
        return True

    if len(words) < min_total_words:
        return False

    # Only check the first 'n' words
    first_words = ' '.join(words[:first_n_words])

    # Check if any of the keywords match the first 'n' words
    result = any(keyword in first_words for keyword in keywords)
    #print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")

    # if result:
    #     print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    # else:
    #     print(f"DEBUG: '{text}' does not contain a keyword in first {first_n_words} words")
    return result
    # return any(keyword in first_words for keyword in keywords)

def get_table_data(pdf_path, page, page_num, converted_roi_payload, page_text_blocks, raw_data, missing_pos=False, remove_outliers=True): # <-- IN USE
    logger.info("Getting table data")

    # raw_data_debug = pd.DataFrame(raw_data)

    '''
    -> Add more lists for Material Descriptors/Labels

    -> change outlier data to not filter the values out, but to flag/ignore the values instead. Need additional column
    -> Outlier data to keep word with outlier and without for easy merging later (Let the user decide)
    -> Add reason for outlier
    -> Check font color
    -> check type (text, annot)
    -> Add dropped keywords somewhere for review
    -> check for header values using similar logic to 'combine_nearby_words'

    '''

    # Dictionaries to store DataFrames for each table type
    structured_tables = {}
    annotations_tables = {}
    outliers_tables = {}
    annot_outliers_tables = {}
    annot_outliers_df = pd.DataFrame()
    collected_outliers = []

    # logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()
    annotations_df = pd.DataFrame()  # DataFrame for annotations
    outliers_df = pd.DataFrame()
    annot_outliers_df = pd.DataFrame()

    # Set file info
    filename = os.path.basename(pdf_path)
    directory_path = os.path.dirname(pdf_path)
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)

    # --> Find the items (e.g., 'BOM', 'SPEC', 'Spool') and their table coordinates
    identified_tables = get_table_coordinates(converted_roi_payload)  # Adjust this function to return a list of table types with their coordinates

    #print("IDENTIFIED TABLES: ", identified_tables)

    #print("\n\n --> TABLES: ", identified_tables)

    if not identified_tables:
        logger.error("No table coordinates found for items")
        # Return empty DataFrames for each expected table type if needed
        return structured_tables, annotations_tables, outliers_df, annot_outliers_df

    for table_type, (table_coords, column_coords, headers_selected) in identified_tables.items():
        # logger.debug(f"Processing {table_type} table data")
        rect = fitz.Rect(table_coords['x0'], table_coords['y0'], table_coords['x1'], table_coords['y1'])


        # raw_data_df = pd.DataFrame(raw_data)
        # raw_data_df = filter_data_within_table(raw_data_df, rect, table_type)
        # raw_data_df.to_excel(f"Passed Raw Data Pg {page_num}.xlsx")

        # raw_data_df = pd.DataFrame(raw_data)

        if raw_data:
            raw_data_df = pd.DataFrame(raw_data)
        # if len(raw_data_df) > 0:

            try:
                raw_data_df['FontSize'] = pd.to_numeric(raw_data_df['FontSize'], errors='coerce')
            except Exception as e:
                logger.error(f"Error converting 'FontSize' to numeric: {e}", exc_info=True)

            # Option 2
            # raw_data_df['Fontsize'] = raw_data_df['Fontsize'].astype(float)

            # Convert the JSON string back to a list of dictionaries, but only if it's a string
            def safe_json_loads(item):
                if isinstance(item, str):
                    try:
                        return json.loads(item)
                    except json.JSONDecodeError:
                        return item  # Return the original string if it's not valid JSON
                return item  # Return the item as is if it's not a string

            # Convert the JSON string back to a list of dictionaries
            #raw_data_df['words'] = raw_data_df['words'].apply(json.loads)
            raw_data_df['words'] = raw_data_df['words'].apply(safe_json_loads)

            # Filter raw_data_df where 'pdf_page' is equal to page_num + 1
            raw_data_df = raw_data_df[raw_data_df['pdf_page'] == page_num + 1]

            print("RAW DATA COLUMNS: ", raw_data_df.columns)

            try:
                raw_df = filter_data_within_table(page_num + 1, raw_data_df, rect, table_type)

                # --> Debug
                #raw_df.to_excel(f"Table Data Pg {page_num + 1}.xlsx")
            except Exception as e:
                print()
                logger.error(f"Error in filter_data_within_table: {e}", exc_info=True)
                # You might want to return an empty DataFrame or handle this error in some way
                raw_df = pd.DataFrame()

            # Filter annotations_df
            annotations_df = raw_df[raw_df['type'] != 'Text']

            print(f"\n\n --> Length of annotations df: {len(annotations_df)}")

        else:
            if len(raw_table_df)==0:

                annotations_data = extract_annotations(pdf_path, page, page_num, rect, table_type, raw_data)#raw_data_df) # CHANGED
                annotations_df = pd.DataFrame(annotations_data)

                # # --> Extract text blocks and annotations from the defined areas
                # raw_table_data = extract_text_blocks(pdf_path, page, page_num, rect, column_coords, table_type, page_text_blocks, raw_data)
                # # Convert raw_table_data and annotations_data to DataFrames
                # raw_df = pd.DataFrame(raw_table_data)

            #raw_df.to_excel(f"Converted Text Block Pg {page_num}.xlsx")

        # Handle Annotations
        annotations_data = extract_annotations(pdf_path, page, page_num, rect, table_type)
        annotations_df = pd.DataFrame(annotations_data)

        if debug_mode:
            annotations_df.to_excel("Annot - get_table_data.xlsx")

        if raw_df.empty and annotations_df.empty:
            logger.warning(f"No data found for table type '{table_type}' on page {page_num}. Returning empty DataFrames.")
            continue  # Skip to the next table type

        # List of header values and ignore terms
        header_values = ["PT", "ID", "NO", "DESCRIPTION", "NPD", "(IN)", "CMDTY CODE", "QTY", "IDENT", "QTY", "POS",
                        "HOLD", "COMPONENT DESCRIPTION", "N.B. (INS)", "ITEM CODE", "N.B.", "(INS)", "N.P.S.", "(IN)", "MATERIALS",
                        "GMN", "MK", "MK.", "LONG DESCRIPTION (SIZE)", "SIZE"]

        ignore_terms = ["PIPE SUPPORTS", "CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION",
                        "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS",
                        "FLANGES", "GASKETS", "BOLTS", "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS",
                        "1R","0R","1S", "0S", "2R", "2S", "3R", "3S", "4R", "4S", "5R", "5S"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]

        bottom_limit_stop_flag = ["PIECE MARKS", "PIECE MARK", "[1]", "[2]", "[3]", "[4]", "[5]", "[6]", "[7]" ] # "PIPE SPOOLS", "PIPE SPOOL" TEST ADD LATER

        field_keywords = ["ERECTION ITEMS","ERECTION MATERIALS","ERECTION", "OTHER THAN SHOP MATERIALS", "FIELD INSTALL"]
        shop_keywords = ["FABRICATION ITEMS", "FABRICATION MATERIALS", "FABRICATION","SHOP MATERIALS", "FABRICATION MATERIALS COMPONENT DESCRIPTION"] # "FABRICATION",
        misc_keywords = ["MISC.", "MISCELLANEOUS COMPONENTS"]
        instr_keywords = ["INSTRUMENTS", "MISCELLANEOUS COMPONENTS"]

        # Define the prefixes for partial matches (Checks if it begins to classify the Material Scope)
        field_prefixes = ("FIELD", "ERECTION", "OTHER THAN SHOP")
        shop_prefixes = ("SHOP", "FABRICAT", "OTHER THAN FIELD")
        misc_prefixes = ("MISCELLANEOUS",)  # Note the comma to make it a tuple with one element
        instr_prefixes = ("INSTR/", "ISTR", "INSTRUMENTS")

        # Define list of Exceptions
        exceptions = ["I-ROD", "SUPPORT - UBOLT", "SUPPORT - GUIDE", "SUPPORT - ANCHOR", "SHIM PLATE"]

        # Combine all keywords and prefixes into a single list
        all_keywords = field_keywords + list(field_prefixes) + shop_keywords + list(shop_prefixes) #+ misc_keywords + list(misc_prefixes)# + instr_keywords + list(instr_prefixes)
        #all_keywords = field_keywords + field_prefixes + shop_keywords + shop_prefixes

        # Ensure all prefixes are tuples
        field_prefixes = tuple(field_prefixes)
        shop_prefixes = tuple(shop_prefixes)
        misc_prefixes = tuple(misc_prefixes)
        instr_prefixes = tuple(instr_prefixes)

        # Ensure all keywords are lists
        field_keywords = list(field_keywords)
        shop_keywords = list(shop_keywords)
        misc_keywords = list(misc_keywords)
        instr_keywords = list(instr_keywords)

        current_material_scope = None

        if not raw_df.empty:

            # Add debugging print statements
            if debug_mode:
                print(f"\n\nColumns in raw_df: {raw_df.columns}")
                print(f"Shape of raw_df: {raw_df.shape}")

            # Apply non ASCII replacement function and debug
            raw_df = apply_replacement_function(raw_df)

            # Group phrases that the replacement functions can analyze
            raw_df = combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5)

            raw_df = sort_by_y0(raw_df)

            # Create a copy for filtering
            filtered_df = raw_df.copy()

            # Find the y0 value from the row where the text matches any value in bottom_limit_stop_flag
            bottom_limit_y0 = None
            for term in bottom_limit_stop_flag:
                if term in filtered_df['Combined_Text'].values:
                    bottom_limit_y0 = filtered_df.loc[filtered_df['Combined_Text'] == term, 'Coordinates'].iloc[0][1]
                    break

            # Drop rows where y0 is greater than or equal to bottom_limit_y0
            if bottom_limit_y0 is not None:
                filtered_df = filtered_df[filtered_df['Coordinates'].apply(lambda coord: coord[1] < bottom_limit_y0)]

            # Filter out rows where the "Combined_Text" column matches any value in filter_terms
            filter_terms = header_values + ignore_terms
            filtered_df = filtered_df[~filtered_df['Combined_Text'].isin(filter_terms)]

            #filtered_df.to_excel(f"Filtered DF {page_num}.xlsx")

            # Distinguish Shop/Fabrication Items
            for index, row in filtered_df.iterrows():
                text = row['Combined_Text'].strip().upper()

                # print(f"{page_num + 1} Text: {text}")

                # Check for prefixes first
                if starts_with_prefix_and_short(text, field_prefixes):
                    current_material_scope = "Field"
                elif starts_with_prefix_and_short(text, shop_prefixes):
                    current_material_scope = "Shop"
                elif starts_with_prefix_and_short(text, misc_prefixes):
                    current_material_scope = "Misc."
                # elif starts_with_prefix_and_short(text, instr_prefixes):
                #     current_material_scope = "Instr."

                # If no prefix match, check for keywords
                elif keyword_in_first_words(text, field_keywords):
                    current_material_scope = "Field"
                elif keyword_in_first_words(text, shop_keywords):
                    current_material_scope = "Shop"
                elif keyword_in_first_words(text, misc_keywords):
                    current_material_scope = "Misc."
                # elif keyword_in_first_words(text, instr_keywords):
                #     current_material_scope = "Instr."
                # else:
                #     current_material_scope = None

                # Assign the current material scope to the row
                if current_material_scope:
                    filtered_df.at[index, 'Material Scope'] = current_material_scope

                    # print(f"Current Material Scope Pg. {page_num}: {current_material_scope}")

            # Filter rows based on the new criteria
            all_prefixes = field_prefixes + shop_prefixes + misc_prefixes #+ instr_prefixes
            all_keywords = field_keywords + shop_keywords + misc_keywords #+ instr_keywords

            # Instead of dropping rows, let's mark them
            filtered_df['is_keyword'] = filtered_df['Combined_Text'].apply(lambda x:
                (starts_with_prefix_and_short(x, all_prefixes) or
                keyword_in_first_words(x, all_keywords)) and
                x.strip().upper() not in exceptions  # Add this condition
            )

            # Now, actually filter out the rows marked as keywords
            filtered_df = filtered_df[~filtered_df['is_keyword']]

            # Drop the 'is_keyword' column as it's no longer needed
            #filtered_df = filtered_df.drop('is_keyword', axis=1)

            #print(f"-->Final shape of filtered_df: {filtered_df.shape}")

            raw_table_df = filtered_df.copy()

            # Continue with the rest of your processing using raw_table_df
            structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords,
                                                                                headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type,True)

            # print(f"Exporting Structured Table Page {page_num}....")

            # structured_table_df.to_excel(f"_Page {page_num} Structured Table.xlsx")

        #
        # --> Process and structure the raw table data and annotations (Outlier, table creation)
        else:
            if not annotations_df.empty:
                structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords,
                                                                                     headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type)

        print(f"\n\n get_table_data rows page {page_num + 1}: {len(annotations_df)}")

        # List column names from column_coords based on 'columnName' field
        try:
            column_names = [col['columnName'] for col in column_coords]
        except Exception as e:
            logger.error(f"Could not get ROI table column names: {e}", exc_info = True)


        ### NOT SURE IF NEEDED?? Outliers are being processed in process_table_data
        ### NOT SURE IF NEEDED??
        ### NOT SURE IF NEEDED??

        # Initialize a list to hold indices of outlier rows
        outlier_indices = []

        # Add outlier detection and logging here:
        try:
            for index, row in structured_table_df.iterrows():
                # Count non-empty and non-NaN values in the specified columns
                valid_values = row[column_names].replace('', np.nan).dropna().shape[0]
                if valid_values <= 1:  # Checking for rows with only one or no valid entries
                    #print(f"Outlier detected in {table_type} table at row {index}: {row[column_names].to_dict()}")
                    outlier_indices.append(index)  # Append index of outlier to list
        except Exception as e:
            logger.error(f"Error looping ROI column names to detect outlier rows in {table_type}: {e}", exc_info=True)

        #Remove outliers from structured_table_df
        if remove_outliers:
            if outlier_indices:
                structured_table_df = structured_table_df.drop(outlier_indices)
                # logger.info(f"Removed {len(outlier_indices)} outliers from {table_type} table.")

            # Append the outliers DataFrame to the list for later combination
        if not outliers_df.empty:
            collected_outliers.append(outliers_df)

        ### NOT SURE IF NEEDED??
        ### NOT SURE IF NEEDED??
        ### NOT SURE IF NEEDED??

        # Store the processed DataFrames in their respective dictionaries



        structured_tables[table_type] = structured_table_df
        annotations_tables[table_type] = annotations_df
        # Outliers are collected and combined outside the loop
        #outliers_tables[table_type] = outliers_df
        annot_outliers_tables[table_type] = annot_outliers_df

    # Combine all collected outliers DataFrames into one
    combined_outliers_df = pd.concat(collected_outliers, ignore_index=True) if collected_outliers else pd.DataFrame()

    #print("\n\nCombined Outliers:", combined_outliers_df.head())  # For debugging

    return structured_tables, annotations_tables, combined_outliers_df, annotations_df # <-- Annotations should follow the same logic as text. Fix this --> BUG
    # return structured_tables, annotations_tables, combined_outliers_df, annot_outliers_tables

    # Return dictionaries containing structured table data, annotations, outliers for each table type
    #return structured_tables, annotations_tables, outliers_tables, annot_outliers_tables
    #return structured_table_df, annotations_df, combined_outliers_df, annot_outliers_df #, annotations_df

def process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords, headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type, remove_outliers=True):
    structured_table_df = pd.DataFrame()
    #Identify outliers in the text data and logically structure it
    if len(raw_table_df)>0:
        # export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )

        # Regular data is the data where the majority of the data is the the same font, font size (Red text is added to regular data to preserve revisions)
        potential_outliers_df, regular_data_df = analyze_and_filter_outliers(page_num, raw_table_df, table_type)


        #potential_outliers_df.to_excel(f"potential_ouliers {page_num + 1}.xlsx")

        # Filter out outliers from the raw data - REMOVE OUTLIERS
        # structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]

        if remove_outliers:
            #print("REMOVE OUTLIERS = TRUE")
            structured_data = regular_data_df #raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
            outliers_data = potential_outliers_df
        else:
            #print("REMOVE OUTLIERS = FALSE")
            structured_data = raw_table_df
            outliers_data = pd.DataFrame()  # Empty DataFrame if not removing outliers

        #outliers_data = raw_table_df[raw_table_df.isin(potential_outliers_df).all(1)]

        # print(f"PAGE {page_num + 1} OUTLIER AFTER FILTER: {len(raw_table_df)}")
        # print(f"PAGE {page_num + 1} OUTLIER SIZE: {len(outliers_data)}")

        # Process only non-outlier data for structured_table_df
        #structured_table_df = create_logical_structure_with_dynamic_rows(structured_data, column_coords, annot_table=False)

        print("\n\nTEXT - Calling create_logical_structure_text")
        structured_table_df = create_logical_structure_text(page_num, structured_data, column_coords, headers_selected, table_type, numbered_rows=True, annot_table=False)

        # Insert columns at the beginning of the dataframe
        structured_table_df.insert(0, 'pdf_page', page_num + 1)
        structured_table_df.insert(0, 'sys_filename', filename)
        structured_table_df.insert(0, 'sys_path', pdf_path)
        structured_table_df.insert(0, 'sys_build', parent_folders_str)

        # Process outliers_df
        outliers_df = outliers_data

    if len(annotations_df):

        if debug_mode:
            annotations_df.to_excel("Annot Before create_logical_structure.xlsx")

        #annotations_df = create_logical_structure_2(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)

        print("\n\nANNOT - Calling create_logical_structure_annot")
        annotations_df = create_logical_structure_annot(annotations_df, column_coords, annot_table=True)
        # # Insert columns at the beginning of the dataframe
        annotations_df.insert(0, 'pdf_page', page_num + 1)
        annotations_df.insert(0, 'sys_filename', filename)
        annotations_df.insert(0, 'sys_path', pdf_path)
        annotations_df.insert(0, 'sys_build', parent_folders_str)

        if debug_mode:
            annotations_df.to_excel("Annot After create_logical_structure.xlsx")

        #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
    else:
            # logger.debug("\n\nAnnotations table is empty.")
            pass
    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df #, annotations_df


def extract_text_blocks(pdf_path, page, page_num, rect, column_coords, table_type, page_text_blocks, raw_data, raw_table_data=None):

    def int_to_rgb(color_int): # format pymupdf font color
        blue = color_int & 255
        green = (color_int >> 8) & 255
        red = (color_int >> 16) & 255
        return (red, green, blue)

    if raw_table_data is None:
        raw_table_data = []

    rotation = page.rotation # Get Page Rotation

    # if rotation == 90:
    #     print(f"\n\nPAGE {page_num} is rotated {rotation}")

    ####################################################### DO NOT DELETE --> NEEDS TO BE TESTED ON MIXED ROTATED DOCUMENTS TO ENSURE THAT
    ####################################################### DO NOT DELETE --> THE ROIs ARE ALREADY ROTATED CORRECTLY BEFORE THIS FUNCTION
    # # Adjust the clipping rectangle based on the rotation, if necessary
    # if rotation == 90:  # Adjust for 90-degree rotation
    #     adjusted_clip_rect = adjust_clip_for_90_degree_rotation(rect, page)
    # else:
    #     adjusted_clip_rect = rect  # No adjustment needed for other rotations or no rotation

    # blocks = page.get_text("dict", clip=adjusted_clip_rect)["blocks"]
    ####################################################### DO NOT DELETE --> NEEDS TO BE TESTED ON MIXED ROTATED DOCUMENTS TO ENSURE THAT
    ####################################################### DO NOT DELETE --> THE ROIs ARE ALREADY ROTATED CORRECTLY BEFORE THIS FUNCTION

    #print(f"\n\nROI COORDINATE: {adjusted_clip_rect}")

    #print("\n\nADJUSTED BLOCK: \n", blocks)

    #blocks = page.get_text("dict", clip=rect)["blocks"]
    #print(f"\n\nROI COORDINATE: {rect}")
    processed_blocks = 0

    #print("PAGE TEXT BLOCKS: ", page_text_blocks)

    for block in page_text_blocks: #blocks:

        #print(f"BLOCK: {block}")
    #for block in blocks:
        # Each block contains one or more lines of text
        try:
            # if "lines" not in block:
            #     print(f"Block without 'lines' key found on page {page_num}: BBOX: \n{block}")

            if block["type"] == 1:  # 0 for text block, 1 for image block
                continue  # This will skip the current block and proceed with the next one

            for line in block["lines"]:
                #print(f"\nLINE: {line}")
            # for line in block.get("lines", []):  # Returns an empty list if 'lines' key is absent
                # Each line contains one or more spans (text elements)
                for span in line["spans"]:
                    text = span["text"].strip()
                    if text:

                        font = span.get("font", "Unknown")  # Get the font name
                        font_size = span.get("size", 0)    # Get the font size
                        flags = span.get("flags", 0)       # Get the text flags

                        font_color_int = span.get("color", 0) # Default to 0 (Black)

                        # Convert color to RGB and hex formats
                        font_color_rgb = int_to_rgb(font_color_int)
                        font_color_hex = "#{:02x}{:02x}{:02x}".format(*font_color_rgb)


                    # Check Rotation and Adjust bbox if necessary
                    # if rotation == 90:
                    #     # Assuming adjust_bbox_90 properly adjusts the bbox for rotation
                    #     print(f"\n\nROTATION = {rotation}")
                    #     adjusted_bbox = adjust_bbox_90_swapped(bbox, page.rotation_matrix)
                    #     # logger.info("-------> ADJUSTED BBOX FOR TABLE")
                    # else:
                    #     adjusted_bbox = bbox  # No rotation, keep original bbox

                    bbox = span["bbox"]
                    adjusted_bbox = bbox  # No rotation, keep original bbox

                    # Check intersection with the table area
                    if not rect.intersects(fitz.Rect(adjusted_bbox)):
                        continue


                    # Check if the value spans multiple columns
                    span_columns = find_spanning_columns(adjusted_bbox, column_coords)

                    # Determine which columns the text spans
                    if len(span_columns) > 1:
                        #logger.info(f"Text spans multiple columns: {page_num+1}: {text}")
                        column_text_data = split_text_by_columns(text, adjusted_bbox, column_coords)
                        for text, bbox in column_text_data:
                            if text:  # Only add non-empty text entries
                                # print("TEXT: ", text)
                                # print("BBOX: ", bbox)

                                try:
                                    #print(f"\n\n-->MULTI - Appending multi-column text: {text}: {bbox}. FULL: {column_text_data}")
                                    raw_table_data.append({
                                        "PdfPath": pdf_path,
                                        "PdfPage": page_num + 1,
                                        "outlierScope": table_type,
                                        "Type": '',
                                        "Text": text,
                                        "Subject": '',
                                        "Contents": '',
                                        "Coordinates": bbox,
                                        "Font": font,
                                        "FontSize": font_size,
                                        "color": font_color_rgb,
                                        "Flags": flags,
                                        "CreationDate": '',
                                        "ModDate": ''
                                    })

                                    #print(f"--> Multi Data appended. Current length of raw_table_data: {len(raw_table_data)}")
                                except Exception as e:
                                    logger.error(f"Multi Text not appended: {text}:{bbox}")

                    else:
                        #print(f"SINGLE- Appending data for single column text: {text}")
                        raw_table_data.append({
                            "PdfPath": pdf_path,  # Include PDF path
                            "PdfPage": page_num + 1,  # Include PDF page number
                            "outlierScope": table_type,
                            "Type": '',  # Include PDF page number
                            "Text": text, #split_texts, #text,
                            "Subject": '',
                            "Contents": '',
                            "Coordinates": adjusted_bbox,
                            "Font": font,
                            "FontSize": font_size,
                            "Flags": flags,
                            "CreationDate": '',
                            "ModDate": ''
                        })
                    processed_blocks += 1

        except Exception as e:
            logger.error(f"'lines' key missing in block on page {page_num}: {e}", exc_info=True)
    #print(f"\n\nRAW TABLE DATA page {page_num}:\n", raw_table_data)
    #print(f"Processed {processed_blocks} text blocks for {table_type}")
    #print(f"\n\nRAW TABLE DATA: {page_num} \n", raw_table_data)

    ###DEBUG
    if debug_mode == True:
        raw_table_df = pd.DataFrame(raw_table_data)
        raw_table_df.to_excel(getDataTempPath(f"Raw Table DF Page {page_num+1}.xlsx"))

        #raw_table_df.to_excel(getDataTempPath(f"EXPORTED RAW TABLE DF_{page_num}.xlsx")) # <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< REMOVE!!!
    ###

    return raw_table_data # <-- IN USE

def find_spanning_columns(bbox, column_coords):
    """Determine which columns the given bbox spans based on 'x0' and 'x1' coordinates in the column definitions."""
    x1, y1, x2, y2 = bbox
    spanning_columns = []

    # Logging the column coordinates to ensure correct data structure is received
    #logger.debug(f"Column Coords: {column_coords}")

    for idx, col_data in enumerate(column_coords):
        try:
            col_start = col_data['x0']
            col_end = col_data['x1']

            if (x1 < col_end and x2 > col_start):
                spanning_columns.append(idx)

        except KeyError as e:
            logger.error(f"Key error processing column boundaries for index {idx} with data {col_data}: {e}")

    return spanning_columns

def split_text_by_columns(text, original_bbox, column_coords):
    try:
        words = text.split()
        spans = []
        estimated_x_start = original_bbox[0]
        average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Ensure len(text) is not zero to avoid division by zero error
        if len(text) == 0:
            average_char_width = 0
        else:
            average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)

        # Calculate bounding boxes for each word
        for word in words:
            word_width = len(word) * average_char_width
            word_bbox = (estimated_x_start, original_bbox[1], estimated_x_start + word_width, original_bbox[3])
            spans.append((word, word_bbox))
            estimated_x_start += word_width + average_char_width  # Account for the space between words

        # Initialize text data for each column
        column_text_data = [('', None)] * len(column_coords)

        # Assign words to columns based on bounding boxes
        for word, bbox in spans:
            assigned = False
            for idx, col_data in enumerate(column_coords):
                col_start = col_data['x0']
                col_end = col_data['x1']

                # Check if the word fits within the column boundaries
                # Adjusting condition to consider word's start point and its midpoint
                if bbox[0] >= col_start and (bbox[0] + bbox[2]) / 2 <= col_end:
                    existing_text, existing_bbox = column_text_data[idx]
                    combined_text = (existing_text + word + ' ') #.strip()
                    if existing_bbox:
                        # Update the bounding box to encompass the new word
                        new_bbox = (existing_bbox[0], existing_bbox[1], bbox[2], existing_bbox[3])
                    else:
                        new_bbox = bbox
                    column_text_data[idx] = (combined_text, new_bbox)
                    assigned = True
                    break
            if not assigned:
                #logger.warning(f"Word '{word}' at bbox {bbox} was not assigned to any column. Assigning to the first column.")
                existing_text, existing_bbox = column_text_data[0]
                combined_text = (existing_text + word + ' ').strip()
                if existing_bbox:
                    new_bbox = (min(existing_bbox[0], bbox[0]), existing_bbox[1], max(existing_bbox[2], bbox[2]), existing_bbox[3])
                else:
                    new_bbox = bbox
                column_text_data[0] = (combined_text, new_bbox)

                #print("\n\nCOLUMN TEXT DATA: \n", column_text_data)



        # Clean up results to remove empty entries and prepare for output
        cleaned_data = [(text.rstrip(), bbox) for text, bbox in column_text_data if text.strip()]
        #print("\n\nCLEANED DATA: \n", cleaned_data)
        return cleaned_data

    except ZeroDivisionError:
        # Return the original data in case of an error
        return [(text, original_bbox)]

def merge_wrapped_rows_text(structured_data, annot_table=False):
    #Convert Data to a Dataframe
    if annot_table: #Return the structured dataframe
        return pd.DataFrame(structured_data)

    structured_df = pd.DataFrame(structured_data)

    # The first column is considered the reference for merging
    try:
        leftmost_column = structured_df.columns[0]
    except Exception as e:
        logger.error(f"Could not get left most columns. {e}")
        #structured_df.to_excel(f"Structured_df_merge_wrapped.xlsx")

    # Iterate backwards through the DataFrame to merge rows
    for i in range(len(structured_df) - 1, 0, -1):

        # If the leftmost column is empty, merge this row with the one above
        if pd.isna(structured_df.at[i, leftmost_column]) or structured_df.at[i, leftmost_column].strip() == '':

            for col in structured_df.columns:
                # Skip the 'material_scope' column entirely
                if col != 'material_scope':

                    # Only merge if the current row's cell is not empty
                    if not pd.isna(structured_df.at[i, col]) and structured_df.at[i, col].strip() != '':
                        structured_df.at[i - 1, col] = structured_df.at[i - 1, col].strip() + ' ' + structured_df.at[i, col].strip()
                        #print(f"\n\nMerging row {i} into row {i-1}. Content before merge: {structured_df.at[i-1, col]}, Content being merged: {structured_df.at[i, col]}")

            # After merging, drop the current row
            structured_df = structured_df.drop(index=i)

    # Reset index after dropping rows
    structured_df = structured_df.reset_index(drop=True)

    #export_large_data_to_excel(structured_df,"merged_wrapped_df_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
    return structured_df

def create_logical_structure_text(page_num, raw_table_df, column_coords, headers_selected, table_type, numbered_rows=True, include_annot_rows=False, annot_table=False, y_tolerance=5):

    '''
    include_annot_rows: (If True, Attempt to create annotation rows in table and include with text)
    '''


    # create_logical_structure_text(page_num, structured_data, column_coords, headers_selected, table_type, numbered_rows=True, include_annot_rows=False, annot_table=False)

    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    # --> Debug
    # --> Debug
    #raw_table_df.to_excel(f"Raw Data - create_logical - Pg {page_num + 1}.xlsx")

    table_type = table_type.lower().strip()

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    structured_data = []

    raw_table_df = raw_table_df.copy()
    raw_table_df.loc[:, 'x0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[0])
    raw_table_df.loc[:, 'y0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[1])

    raw_table_df_sorted = raw_table_df.sort_values(by=['y0', 'x0'])

    if len(raw_table_df)>0:
        if table_type == "bom":
            leftmost_column = column_coords[0]

            try:
                bom_rows, bom_texts, filtered_contents, unassigned_items, skipped_items = detect_bom_rows(page_num, raw_table_df_sorted, leftmost_column)
            except Exception as e:
                print()
                logger.error(f"Error in detect_bom_rows on page {page_num + 1}: {e}", exc_info=True)
                print()
                # You might want to return empty lists or handle this error in some way
                bom_rows, bom_texts, filtered_contents, unassigned_items = [], [], [], []

            if unassigned_items:
                print(f"\n\n -----> ERROR! UNASSIGNED TABLE ITEMS!\n {unassigned_items}")
                for index, text, coords, reason in unassigned_items:
                    #unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
                    print(f" Index: {index}, \n Text: '{text}', \n Coordinates: {coords}, \n Reason: {reason}")


            for content in filtered_contents:
                row_texts = []
                for _, item in content.iterrows():
                    text = item['Contents'] if annot_table else item['Text']
                    color = item['color']
                    coords = item['Coordinates']
                    words = item.get('words', None)
                    bbox = fitz.Rect(coords)

                    row_data = {
                        'text': text,
                        'bbox': bbox,
                        'color': color,
                        'words': words,
                        'material_scope': item.get('Material Scope', '')
                    }
                    row_texts.append(row_data)

                structured_data.append(assign_texts_to_columns(row_texts, column_coords, column_names, table_type))
        else:
            for _, row in raw_table_df_sorted.iterrows():
                text = row['Contents'] if annot_table else row['Text']

                try:
                    color = row.get('Color', None)
                    if color is None:
                        logger.warning(f"Unable to get color value for row at index {row.name}")
                except Exception as e:
                    logger.error(f"Critical error getting color for row at index {row.name}: {e}")

                coords = row['Coordinates']
                words = row.get('words', None)
                bbox = fitz.Rect(coords)

                row_data = {
                    'text': text,
                    'bbox': bbox,
                    'color': color,
                    'words': words
                }

                structured_data.append(assign_texts_to_columns([row_data], column_coords, column_names, table_type))

        structured_df = pd.DataFrame(structured_data)

    else:
        structured_df = pd.DataFrame()

        # logger.warning("Structured DF is empty - Text")

    # structured_df.to_excel(f"Structured DF - Pg {page_num + 1}.xlsx")

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))

    return structured_df

def detect_bom_rows(page_num, df, leftmost_column, skip_words = ["PIPE SPOOL", "PIPE SPOOLS"], y0_deviation=1.5):

    if debug_row_content:
        df.to_excel(f"debug/detect_bom_row PG {page_num + 1}.xlsx")

    def is_valid_bom_item(text):
        if not isinstance(text, str) or not text.strip():
            return False

        first_part = text.split()[0]
        pattern = r'^\d{1,3}[a-zA-Z]?$'
        return bool(re.match(pattern, first_part))

    def is_in_leftmost_column(x, y, leftmost_column):
        return (leftmost_column['x0'] <= x <= leftmost_column['x1'] and
                leftmost_column['y0'] <= y <= leftmost_column['y1'])

    def is_red_text(color): # For Revisions
        return color == (255, 0, 0)

    def get_text_content(row):
        for col in row.index:
            if col not in ['Coordinates', 'color'] and isinstance(row[col], str) and row[col].strip():
                return row[col]
        return ""

    # Filter the dataframe to get only the valid BOM items in the leftmost column
    # bom_items = df[df.apply(lambda row: is_valid_bom_item(row['Text']) and
    #                         is_in_leftmost_column(row['x0'], row['y0'], leftmost_column), axis=1)]

    bom_items = df[df.apply(lambda row: any(is_valid_bom_item(row[col]) for col in row.index if col != 'Coordinates' and col != 'color') and
                        is_in_leftmost_column(row['x0'], row['y0'], leftmost_column), axis=1)]

    # Sort by y0 coordinate
    bom_items_sorted = bom_items.sort_values('y0')

    # Calculate row ranges and store corresponding text
    row_ranges = []
    row_texts = []
    row_heights = []

    for i in range(len(bom_items_sorted)):
        start = bom_items_sorted.iloc[i]['y0']
        #text = bom_items_sorted.iloc[i]['Text']
        text = get_text_content(bom_items_sorted.iloc[i])

        if i < len(bom_items_sorted) - 1:
            end = bom_items_sorted.iloc[i+1]['y0']
        else:
            end = leftmost_column['y1']

        row_ranges.append((start, end))
        row_texts.append(text)
        row_heights.append(end - start)


    # Calculate average row height and standard deviation
    if len(row_heights) > 2:
        # If we have more than 2 rows, we can use statistics on all but the last row
        row_heights_for_calc = row_heights[:-1]
        avg_row_height = statistics.mean(row_heights_for_calc)
        std_row_height = statistics.stdev(row_heights_for_calc)
        outlier_threshold = avg_row_height + 2 * std_row_height
    elif len(row_heights) == 2:
        # If we have exactly 2 rows, use the first row's height
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = avg_row_height * 2  # Set a reasonable threshold
    elif len(row_heights) == 1:
        # If we have only 1 row, use its height directly
        avg_row_height = row_heights[0]
        std_row_height = 0
        outlier_threshold = float('inf')  # No outliers possible with one row
    else:
        # Handle the case where there are no rows
        logger.warning(f"Table has no rows on page {page_num + 1}!")
        avg_row_height = 0
        std_row_height = 0
        outlier_threshold = float('inf')

    # Function to assign row based on coordinates
    def assign_row(y0, y1, color, text):
        # Perform checks for: Both y0, y1 in row, y1
        for i, (start, end) in enumerate(row_ranges):
            if start <= y0 <= end and start <= y1 <= end:
                return i
        for i, (start, end) in enumerate(row_ranges):
            if start <= y1 <= end:
                return i
        intersecting_rows = [i for i, (start, end) in enumerate(row_ranges) if start <= y1 and y0 <= end]
        if intersecting_rows:
            return min(intersecting_rows, key=lambda i: abs(row_ranges[i][0] - y0))

        # If all other checks fail, check for red text (Could be Revisions, which can be off center)
        if is_red_text(color):
            distances = []
            for i, (start, end) in enumerate(row_ranges):
                mid = (start + end) / 2
                distance_y0 = abs(y0 - mid)
                distance_y1 = abs(y1 - mid)
                distances.append(min(distance_y0, distance_y1))
            return np.argmin(distances)

        # If nothing worked, return None
        return None

    # Assign rows to all items
    row_assignments = [-1] * len(df)  # Initialize all assignments to -1
    unassigned_items = [] # Items that could not be assigned but likely should have been
    skipped_items = [] # Intentionally skipped
    skip_processing = False
    last_valid_row = -1
    last_row_items = []
    buffer_items = [] # Holds items for use in loop. Accounts for minor deviations in y0 to ensure we do not permanently discard valid items in 'skip_processing'

    # for index, row in df.iterrows():
    for i, (index, row) in enumerate(df.iterrows()):
        y0, y1 = row['Coordinates'][1], row['Coordinates'][3]
        color = row['color']
        text = row['Text']

        # Check if we need to start skipping
        if any(skip_word in text for skip_word in skip_words):
            skip_processing = True
            skipped_items.append((i, text, row['Coordinates'], "Contains skip word"))
            continue

        # Check if we need to stop skipping
        if skip_processing and is_valid_bom_item(text) and is_in_leftmost_column(row['x0'], row['y0'], leftmost_column):
            skip_processing = False

            # Process buffered items
            for buffered_item in buffer_items:
                if abs(buffered_item[1] - y0) <= y0_deviation:
                    assigned_row = assign_row(buffered_item[1], buffered_item[2], buffered_item[3], buffered_item[4])
                    if assigned_row is not None:
                        row_assignments[buffered_item[0]] = assigned_row
                    else:
                        unassigned_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Could not be assigned to a row after buffer processing"))
            buffer_items.clear()

        if skip_processing:
            buffer_items.append((i, y0, y1, color, text))
            # skipped_items.append((i, text, df.iloc[i]['Coordinates'], "In skip processing mode"))
            continue

        assigned_row = assign_row(y0, y1, color, text)
        if assigned_row is not None:
            # row_assignments.append(assigned_row)
            row_assignments[i] = assigned_row
            last_valid_row = assigned_row

            # Store items for the last row
            if assigned_row == len(row_ranges) - 1:
                last_row_items.append((i, y0, y1, text))

        else:
            unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
            row_assignments[i] = last_valid_row  # Assign to the last valid row instead of -1

    # Process any remaining buffered items
    for buffered_item in buffer_items:
        skipped_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Remained in buffer after processing"))

    # Process the last row items for outliers
    if last_row_items:
        last_row_start, last_row_end = row_ranges[-1]
        last_row_height = last_row_end - last_row_start

        if last_row_height > outlier_threshold:
            #print(f"\nLast row is an outlier. Removing trash data:")
            valid_last_row_items = []
            for i, y0, y1, text in last_row_items:
                if y0 - last_row_start > outlier_threshold:
                    #print(f"Removing: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")
                    #unassigned_items.append((i, text, df.iloc[i]['Coordinates']))
                    skipped_items.append((i, text, df.iloc[i]['Coordinates'], "Greater than outlier threshold"))
                    row_assignments[i] = -1
                else:
                    valid_last_row_items.append((i, y0, y1, text))
                    #print(f"Keeping: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")

            # Update the last row range with the valid items
            if valid_last_row_items:
                new_last_row_end = max(item[2] for item in valid_last_row_items)
                row_ranges[-1] = (last_row_start, new_last_row_end)
                #print(f"\nUpdated last row range: {row_ranges[-1]}")
            else:
                print("\n--> Warning: All items in the last row were removed as trash.")
        # else:
        #     print("\nLast row is not an outlier. Keeping all items.")


    df['assigned_row'] = row_assignments

    # Create filtered contents based on row assignments
    filtered_contents = [df[df['assigned_row'] == i] for i in range(len(row_ranges))]

    # Debug information
    if debug_row_content:
        print("\nFinal Row Ranges:")
        for i, (start, end) in enumerate(row_ranges):
            print(f"Row {i}: {start} - {end} (height: {end - start})")
        print(f"Average row height: {avg_row_height}")
        print(f"Row height standard deviation: {std_row_height}")
        print(f"Outlier threshold: {outlier_threshold}")

        print("\nIntentionally Skipped Items:")
        for item in skipped_items:
            print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

        print("\nUnassigned Items:")
        for item in unassigned_items:
            print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

    return row_ranges, row_texts, filtered_contents, unassigned_items, skipped_items



def assign_texts_to_columns(row_texts, column_coords, column_names, table_type):
    row_structure = {col: [] for col in column_names}  # Use lists instead of strings
    row_colors = {col: [] for col in column_names}  # To keep track of font colors
    row_material_scope = None
    skip_processing = False
    leftmost_column = column_names[0]  # Assuming the first column is the leftmost
    #skip_words = ["PIPE SPOOL", "PIPE SPOOLS"]

    # print(f"\n\nINCOMING ROW TEXTS: {row_texts}")

    # Sort text items by x-coordinate (left to right)
    # sorted_texts = sorted(row_texts, key=lambda item: item['bbox'][0])

    sorted_texts = sorted(row_texts, key=lambda item: (item['bbox'][1], item['bbox'][0]))

    for text_item in sorted_texts:
        text, bbox, color, words = text_item['text'].strip(), text_item['bbox'], text_item['color'], text_item['words']

        if table_type == 'bom':
            material_scope = text_item.get('material_scope', '')
            if material_scope and not row_material_scope:
                row_material_scope = material_scope

        # Check for multi-column span
        spanning_columns = []
        for col_name, col_info in zip(column_names, column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            if col_rect.intersects(bbox):
                spanning_columns.append(col_name)

        # Sort words by y-coordinate and then x-coordinate
        sorted_words = sorted(words, key=lambda w: (w['bbox'][1], w['bbox'][0]))

        if len(spanning_columns) > 1:
            #print(f"Multi-column span detected: Value: \n'{text}', \n Coordinates: {bbox}, \n Spanning columns: {', '.join(spanning_columns)}, \n Words: {words}\n\n")

            # Handle multi-column span
            for word_info in words:
                word = word_info['word']
                word_bbox = fitz.Rect(word_info['bbox'])
                for col_name, col_info in zip(column_names, column_coords):
                    col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                    if col_rect.x0 <= word_bbox.x1 <= col_rect.x1 and col_rect.y0 <= word_bbox.y1 <= col_rect.y1:
                        if col_name == "material_description" or (color == (255, 0, 0) and row_structure[col_name]):
                            # For material_description or if it's red text, append or replace
                            if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                # If last text was red, append
                                row_structure[col_name][-1] += f" {word}"
                            else:
                                # If last text wasn't red or column is empty, replace
                                row_structure[col_name] = [word]
                                row_colors[col_name] = [color]
                        else:
                            # For other columns or if it's black text, add as a new item
                            row_structure[col_name].append(word)
                            row_colors[col_name].append(color)
                        break

        else:
            assigned = False
            for col_name, col_info in zip(column_names, column_coords):
                col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                if col_rect.intersects(bbox):
                    if color == (255, 0, 0) and text not in ["1", "2", "3"]:
                        if col_name == "material_description" or row_structure[col_name]:
                            # For material_description or if the column already has content
                            if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                # If last text was red, append
                                row_structure[col_name][-1] += f" {text}"
                            else:
                                # If last text wasn't red or column is empty, replace
                                row_structure[col_name] = [text]
                                row_colors[col_name] = [color]
                        else:
                            # For other columns or if it's the first entry, replace
                            row_structure[col_name] = [text]
                            row_colors[col_name] = [color]
                    else:

                        # For black text, append only if the last entry wasn't red
                        if not row_structure[col_name] or row_colors[col_name][-1] != (255, 0, 0):
                            row_structure[col_name].append(text)
                            row_colors[col_name].append(color)
                    assigned = True
                    break

            if not assigned:
                print(f"Warning: Text '{text}' could not be assigned to any column.")

    # Join texts in each column
    for col in row_structure:
        row_structure[col] = ' '.join(row_structure[col])

    # print(f"\n\nROW STRUCTURE: {row_structure}")

    # # Sort words in each column by y-coordinate and join
    # for col in row_structure:
    #     sorted_column = sorted(row_structure[col], key=lambda x: x[1])
    #     row_structure[col] = ' '.join(word for word, _ in sorted_column)

    if table_type == 'bom':
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''

    return row_structure


def extract_annotations(pdf_path, page, page_num, rect, table_type, annotations_data=None, multi_roi=False):
    # Now, process annotations
    if annotations_data is None:
        annotations_data = []

    annotations = page.annots()
    if annotations:
        for annot in annotations:

            annot_rect = annot.rect

            if not multi_roi: # Use the annotation directly since the transformation matrix should already be applied
                # Adjust rect for rotation matrix if needed
                annot_rect = adjust_bbox(annot_rect, page.rotation_matrix)

            #adjust_bbox(annot_rect, page.rotation_matrix)

            if rect.intersects(annot_rect):  # Check if the annotation intersects with the table
                annot_info = annot.info
                annotations_data.append({
                    "PdfPath": pdf_path,
                    "PdfPage": page_num + 1,
                    "outlierScope": table_type,
                    "Type": annot_info.get("type", ""),
                    "Text": '',
                    "Subject": annot_info.get("subject", ""),  # Subject might be a better descriptor
                    "Contents": annot_info.get("content", "").strip(),
                    "Coordinates": annot_rect, #(annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                    "coordinates2": annot_rect, #(annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                    "Font": '',
                    "FontSize": '',
                    "color":'',
                    "Flags": '',
                    "CreationDate": annot_info.get("creationDate", ""),
                    "ModDate": annot_info.get("modDate", "")
                })
    if debug_mode:
        print(f"Page Rotation: {page.rotation}")
        print("\n\nAnnotations Data Rect:\n", annot_rect)
        print("\n\nAnnotations Data:\n", annotations_data)
    return annotations_data

# Temporarily used for Annotation type tables until a more robust solution is found
def create_logical_structure_annot(raw_table_df, column_coords, overlap_threshold=0, numbered_rows=True, annot_table=True):

    print(f"ANNOT DF LENGTH: {len(raw_table_df)}\n Columns{raw_table_df.columns}")


    if debug_mode:
        print("\n\n--> ANNOT CREATE LOGICAL STRUCTURE ACCESSED...")
    #logger.debug("Starting create_logical_structure_2")
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    # Data structure to hold the text for each column
    structured_data = {col: [] for col in column_names}

    # Track the last processed y-coordinate for the first column
    last_y = None

    # Process each text item in raw_table_df
    for _, row in raw_table_df.iterrows():
        if annot_table:
             text = row['Contents'] # CHANGED
            #text = row['Text']
        else:
            text = row['Text']
        #
        #print("\n\nTEXT: ", text)
        coords = row['Coordinates']
        bbox = fitz.Rect(coords)
        #print(f"\n\nText: '{text}', \nCoordinates: \n {bbox}\n")
        ##pp(bbox)

        # Determine which column the text belongs to
        for i, col_info in enumerate(column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])

            if col_rect.intersects(bbox):
                column_name = col_info['columnName']

                # Check if we need to start a new row
                #print(f"\nChecking new row criteria for text: '{text}', Y: {coords[1]}, Last Y: {last_y}, Overlap Threshold: {overlap_threshold}")
                if last_y is None or coords[1] > last_y + overlap_threshold:
                    # Append empty strings to columns that have no data yet
                    for col_name in structured_data:
                        structured_data[col_name].append('')
                    last_y = coords[3]

                if i == 0 and numbered_rows:  # Leftmost column and numbered rows
                    match = re.match(r'(\d+)(?:\.\s+)?(?:\s+(.*))?', text)
                    if match:
                        structured_data[column_name][-1] = match.group(1)
                        if match.group(2):
                            next_column_name = column_names[i+1]
                            structured_data[next_column_name][-1] = match.group(2)
                    else:
                        structured_data[column_name][-1] = text
                else:
                    structured_data[column_name][-1] = text
                break

    structured_df_bf = pd.DataFrame(structured_data) # TYPO???

    # Call the merge_wrapped_rows function on the structured DataFrame
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    print(f"\n\nANNOT CREATE LOGICAL END ROWS {len(structured_df)}")

    return structured_df

def extract_headers_from_structured_data(structured_df, column_coords):
    headers = {}
    header_coords = {}

    if len(structured_df) > 0:
        first_row = structured_df.iloc[0]

        for col_info in column_coords:
            column_name = col_info['columnName']
            if column_name in first_row:
                headers[column_name] = first_row[column_name]
                header_coords[column_name] = {
                    'x0': col_info['x0'],
                    'y0': col_info['y0'],
                    'x1': col_info['x1'],
                    'y1': col_info['y1']
                }
            else:
                headers[column_name] = ''
                header_coords[column_name] = {}

    #print(f"IDENTIFIED HEADERS: {headers}. COORDS: {header_coords}")

    return headers, header_coords

def search_headers_above_table(raw_table_df, column_coords):
    headers = {}
    header_coords = {}

    # Implement the logic to search for headers above the table coordinates
    # based on the raw_table_df and column_coords
    # Store the found headers and their coordinates in the headers and header_coords dictionaries

    return headers, header_coords

def store_header_reference(headers, header_coords):
    # Store the headers and their coordinates for future reference
    # You can save them to a file, database, or any other storage mechanism
    pass
# ^^^^^ General Functions ^^^^^

def analyze_and_filter_outliers_og(page_num, raw_table_df, table_type, font_size_tolerance=0.8):
    # print("\n\n------------------\n\nAnalyze and filter outliers accessed...\n\n------------------")
    # Analyze the most common font and font size
    common_font = raw_table_df['Font'].mode()[0]
    common_font_size = raw_table_df['FontSize'].mode()[0]
    common_font_color = raw_table_df['color'].mode()[0]

    # print(f"\n\nPAGE {page_num + 1}:\nCOMMON FONT: {common_font} \n SIZE: {common_font_size}")


    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Identify outliers with tolerance, excluding red text
    outlier_condition = (
        ((raw_table_df['Font'] != common_font) |
         (abs(raw_table_df['FontSize'] - common_font_size) > font_size_tolerance)) &
        (~raw_table_df['color'].apply(lambda x: isinstance(x, tuple) and x == red_color))
    )

    outliers_df = raw_table_df[outlier_condition]
    regular_data_df = raw_table_df[~outlier_condition]

    # Add red text to regular data, even if it doesn't match common font or size
    red_text_df = raw_table_df[raw_table_df['color'] == red_color]

    red_text_df.to_excel(f"Red Text DF Pg {page_num + 1}.xlsx")

    regular_data_df = pd.concat([regular_data_df, red_text_df]).drop_duplicates()

    regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")

    # Remaining data (non-outliers)
    #regular_data_df = raw_table_df[~outlier_condition]

    #print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df

def analyze_and_filter_outliers_edit_1(page_num, raw_table_df, table_type, font_size_tolerance=0.5):
    common_font = raw_table_df['Font'].mode()[0]
    common_font_size = raw_table_df['FontSize'].mode()[0]
    common_font_color = raw_table_df['color'].mode()[0]

    #print(f"\n\nPAGE {page_num + 1}:\n COMMON FONT: {common_font} \n SIZE: {common_font_size} \n COLOR: {common_font_color}")

    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Create separate conditions for each outlier reason
    font_condition = raw_table_df['Font'] != common_font
    size_condition = abs(raw_table_df['FontSize'] - common_font_size) > font_size_tolerance
    color_condition = ~raw_table_df['color'].apply(lambda x: isinstance(x, tuple) and x == red_color)

    # Combine conditions
    outlier_condition = (font_condition | size_condition) & color_condition

    # Store the common attributes

    # Create a DataFrame for outliers with reasons
    outliers_df = raw_table_df[outlier_condition].copy()
    outliers_df['drop_reason'] = ''


    outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
    outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
    outliers_df['drop_reason'] = outliers_df['drop_reason'].str.rstrip('; ')

    regular_data_df = raw_table_df[~outlier_condition]

    # Add red text to regular data, even if it doesn't match common font or size
    red_text_df = raw_table_df[raw_table_df['color'] == red_color]

    red_text_df.to_excel(f"Red Text DF Pg {page_num + 1}.xlsx")

    regular_data_df = pd.concat([regular_data_df, red_text_df]).drop_duplicates()

    regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")
    outliers_df.to_excel(f"Outliers DF Pg {page_num + 1}.xlsx")

    #print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df

def analyze_and_filter_outliers_og_reform(page_num, raw_table_df, table_type, font_size_tolerance=0.5):
    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Separate red text and non-red text
    red_text_df = raw_table_df[raw_table_df['color'] == red_color]
    non_red_text_df = raw_table_df[raw_table_df['color'] != red_color]

    # Add this line to filter out 'Times-Roman' font - DEBUG TEMPORARY! Lines contains hidden characters!!
    non_red_text_df = non_red_text_df[non_red_text_df['Font'] != 'Times-Roman']

    # non_red_text_df['FontSize'] = pd.to_numeric(non_red_text_df['FontSize'], errors='coerce')

    # Find common attributes from non-red text
    if len(non_red_text_df) > 0:
        common_font = non_red_text_df['Font'].mode()[0]
        common_font_size = non_red_text_df['FontSize'].mode()[0]
        common_font_color = non_red_text_df['color'].mode()[0]
    else:
        # If all text is red, use the most common attributes from all text
        common_font = raw_table_df['Font'].mode()[0]
        common_font_size = raw_table_df['FontSize'].mode()[0]
        common_font_color = raw_table_df['color'].mode()[0]

    print(f"\n\nPAGE {page_num + 1}:\n COMMON FONT: {common_font} \n SIZE: {common_font_size} \n COLOR: {common_font_color}")

    # Create separate conditions for each outlier reason
    font_condition = non_red_text_df['Font'] != common_font
    size_condition = abs(non_red_text_df['FontSize'] - common_font_size) > font_size_tolerance

    # Combine conditions
    outlier_condition = font_condition | size_condition

    # Create a DataFrame for outliers with reasons
    outliers_df = non_red_text_df[outlier_condition].copy()
    outliers_df['drop_reason'] = ''
    outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
    outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
    outliers_df['drop_reason'] = outliers_df['drop_reason'].str.rstrip('; ')

    # Regular data is non-red text that's not an outlier, plus all red text
    regular_data_df = pd.concat([non_red_text_df[~outlier_condition], red_text_df])

    # regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")
    # outliers_df.to_excel(f"Outliers DF Pg {page_num + 1}.xlsx")

    # print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df # 10/3

def create_logical_structure_text_og(page_num, raw_table_df, column_coords, headers_selected, table_type, numbered_rows=True, annot_table=False, y_tolerance=5): # ADDED 7-2. Accounts for minor deviation in y0 values (VG Drawings for example)):
    #logger.debug("Starting create_logical_structure_2")
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    # Set up for case insensitivity
    table_type = table_type.lower().strip()

    overlap_threshold=5

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    # Prepare data structure for rows and columns
    structured_data = []

    # Extracting x0 and y0 from Coordinates for sorting
    raw_table_df = raw_table_df.copy()
    raw_table_df.loc[:, 'x0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[0])
    raw_table_df.loc[:, 'y0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[1])

    # Sort by y0 and then by x0 for logical reading order
    raw_table_df_sorted = raw_table_df.sort_values(by=['y0', 'x0'])


    #raw_table_df_sorted.to_excel(f"TABLE DATA PG {page_num + 1}.xlsx")

    # New code for BOM table type
    if table_type.lower().strip() == "bom":
        leftmost_column = column_coords[0]  # Assuming the first column in the list is the leftmost
        bom_rows, bom_texts, filtered_contents, unassigned_items, skipped_items = detect_bom_rows(page_num, raw_table_df_sorted, leftmost_column)

        if unassigned_items:
            print(f"\n\n -----> ERROR! UNASSIGNED ITEMS!\n {unassigned_items}")
            for text, coords in unassigned_items:
                print(f"  Text: '{text}', Coordinates: {coords}")

        print(f"BOM Rows detected for page {page_num + 1}:")
        for i, ((start, end), text, content) in enumerate(zip(bom_rows, bom_texts, filtered_contents), 1):
            print(f"\n\nRow {i}: {start:.2f} - {end:.2f}")
            print(f"POS #: '{text}'")
            print("Contents:")
            for _, item in content.iterrows():
                print(f"   Text: '{item['Text']}'\n   Coordinates: {item['Coordinates']}")


    # Initialize variables for row tracking
    current_row_texts = []
    last_y0 = None

    for _, row in raw_table_df_sorted.iterrows():

        text = row['Contents'] if annot_table else row['Text']
        color = row['color']
        coords = row['Coordinates']
        words = row['words']
        bbox = fitz.Rect(coords)

        #print(f"\nProcessing row: Text: {text}, Color: {color) , #Type of color: {type(color)}")

        row_data = {'text': text, 'bbox': bbox, 'color': color, 'words': words}

        if table_type == "bom":
            #material_scope = row.get('Material Scope', '')  # Get the Material Category if it exists
            row_data['material_scope'] = row.get('Material Scope', '')


        # Check if we should start a new row
        if last_y0 is None or (bbox.y0 - last_y0) > overlap_threshold:

            # Before starting a new row, assign existing row texts to columns
            if current_row_texts:
                structured_data.append(assign_texts_to_columns(current_row_texts, column_coords, column_names, table_type))
                current_row_texts = []
            last_y0 = bbox.y0

        current_row_texts.append(row_data)
            # current_row_texts.append(row_data)

        # if table_type == "bom":
        #     current_row_texts.append({'text': text, 'bbox': bbox, 'material_scope': material_scope})
        # else:
        #     current_row_texts.append({'text': text, 'bbox': bbox})

    # Don't forget to process the last row
    if current_row_texts:
        structured_data.append(assign_texts_to_columns(current_row_texts, column_coords, column_names, table_type))

    # --Debug
    # Convert structured data to DataFrame for easier handling later on
    structured_df = pd.DataFrame(structured_data)
    #print("\nBEFORE MERGE DF: \n", structured_data)

    structured_df.to_excel(f"Structured DF Before Merge - {page_num+1}.xlsx")

    if debug_mode == True:
        structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))
        #structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    # --> Handle rows that should be wrapped
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    if debug_mode == True:
        structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))

    return structured_df



def assign_texts_to_columns_og(row_texts, column_coords, column_names, table_type): # Original function 9/30
    # Initialize row structure with empty strings for each column
    row_structure = {col: '' for col in column_names} # Lists to hold texts temporarily

    # Variable to hold the material category for this row
    row_material_scope = None

    red_text_columns = set()  # Keep track of columns with red text (Revisions)


    # print("\n\nASSIGN TEXT", row_texts)

    for text_item in row_texts:
        text, bbox, color = text_item['text'].strip(), text_item['bbox'], text_item['color']
        #color = text_item.get('color', '')  # Get color, default to empty string if not present

        if table_type == 'bom':
            material_scope = text_item.get('material_scope', '')  # Get material_scope, or default to empty string

        for col_name, col_info in zip(column_names, column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            if col_rect.intersects(bbox):

                # # Check if we need to replace existing text with red text (Revision)
                # if color == "(255, 0, 0)" and text not in ["1", "2", "3"]:

                #     print("\n\n   --> IS REVISION:", text, "COLOR: ", color)

                #     if not row_structure[col_name] or row_structure[col_name] not in ["1", "2", "3"]:
                #         row_structure[col_name] = text
                # else:
                #     # If the column already has text, add a space before appending more text
                #     separator = ' ' if row_structure[col_name] else ''
                #     row_structure[col_name] += separator + text

                # If the column already has text, add a space before appending more text
                separator = ' ' if row_structure[col_name] else ''
                row_structure[col_name] += separator + text.strip()

                if table_type == 'bom':
                    # Store the material category for this row (if not already set)
                    if material_scope and not row_material_scope:
                        row_material_scope = material_scope

                break  # Break after assigning text to the first intersecting column

    if table_type == 'bom':
        # Assign the material category for the entire row
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''

    return row_structure # Original


#### vvvvv NOT USED vvvvv
def parse_complex_size(value):
    """Parse complex size values that may include decimals followed by fractions. i.e '1.1/2' (1.5) """
    try:
        # Remove any non-numeric characters (except for decimal point, forward slash, and hyphen)
        value = re.sub(r'[^0-9./\-]', '', str(value))

        # Remove any leading or trailing "." or "," characters
        value = value.strip(".,")

        if '.' in value and '/' in value:
            # Handle cases where decimal and fraction are combined, e.g., "1.1/2"
            decimal_part, fraction_part = value.split('.')
            fraction_str = fraction_part.split('/')[0] + '/' + fraction_part.split('/')[1]
            fraction = convert_fraction_to_float(fraction_str)
            return float(decimal_part) + fraction
        else:
            # Handling cases without combined decimals
            parts = re.findall(r'(\d+/\d+|\d+\.\d+|\d+)', value)
            if parts:
                return sum(convert_fraction_to_float(part) for part in parts)
        return np.nan
    except Exception as e:
        logger.error(f"Failed to parse complex size. Value: '{value}'. Error: {e}", exc_info=True)
        return np.nan

def convert_fraction_to_float(fraction_str):
    try:
        if '/' in fraction_str:
            numerator, denominator = fraction_str.split('/')
            return float(numerator) / float(denominator)
        else:
            return float(fraction_str)
    except ValueError:
        return float(fraction_str)

def get_table_coordinates_latest(converted_roi_payload):
    table_coords = None
    column_coords = []
    try:
        for item in converted_roi_payload:
            #if item['columnName'].lower() == 'bom' and 'tableCoordinates' in item:
            if item['columnName'].lower() in ['bom', 'spec', 'spool'] and 'tableCoordinates' in item:
                #logger.debug("Found 'bom' item with table coordinates")
                table_coords = item['tableCoordinates']
                # Adjust how you extract column coordinates to match the new format
                for column_dict in item['tableColumns']:
                    for column_name, coords in column_dict.items():
                        coords['columnName'] = column_name  # Add columnName for compatibility with existing logic
                        column_coords.append(coords)
                break
    except Exception as e:
        logger.error(f"Error getting tabe coordinates for item {item}: {e}", exc_info=True)
    return table_coords, column_coords

def get_specific_table(table_type, structured_tables):
    """
    Retrieves a specific table from the structured tables dictionary,
    accounting for case-insensitivity and blank tables.

    Parameters:
    - table_type (str): The type of table to retrieve (e.g., 'BOM', 'SPEC', 'Spool').
    - structured_tables (dict): A dictionary of structured tables,
                                where keys are table types and values are DataFrames.

    Returns:
    - pd.DataFrame: The DataFrame for the requested table type, or an empty DataFrame
                    if the table does not exist or is blank.
    """
    # Convert the table_type to lowercase to ensure case-insensitive matching
    table_type = table_type.lower()

    # Search for the table in the structured tables dictionary
    for key, table_df in structured_tables.items():
        if key.lower() == table_type:
            # Check if the table DataFrame is not empty
            if not table_df.empty:
                return table_df
            else:
                # Return an empty DataFrame if the table exists but is blank
                print(f"The '{table_type}' table exists but is blank.")
                return pd.DataFrame()

    # Return an empty DataFrame if the table type was not found
    print(f"No '{table_type}' table was found.")
    return pd.DataFrame()


def _get_table_data(pdf_path, page, page_num, converted_roi_payload, missing_pos=False):
    # logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()
    annotations_df = pd.DataFrame()  # DataFrame for annotations
    outliers_df = pd.DataFrame()
    annot_outliers_df = pd.DataFrame()
    raw_table_data = []

    ######
    structured_data_results = {}
    outliers_results = {}
    annotations_results = {}

    table_types = ['bom', 'spec', 'spool']  # Define the table types you are working with
    ####

    # Find the 'BOM' item and its table coordinates
    # table_coords = None
    # outliers_data = []
    # annot_outliers_data = []
    # column_coords = []


    for table_type in table_types:
        # Initialize empty structures for each table type
        structured_data = {'text': [], 'annotations': []}
        raw_table_data = []
        annotations_data = []
        outliers_data = []
        column_coords = []

        table_coords=None
        for item in converted_roi_payload:
            #if item['columnName'].lower() == 'bom' and 'tableCoordinates' in item:
            if item['columnName'].lower() == table_type and 'tableCoordinates' in item:
                # logger.debug(f"Found {item['columnName']} item with table coordinates")
                table_coords = item['tableCoordinates']

                for column_dict in item['tableColumns']:
                    for column_name, coords in column_dict.items():
                        coords['columnName'] = column_name
                        column_coords.append(coords)
                break

        if not table_coords:
            logger.error("No table coordinates found for 'bom' item")
            continue
            #return structured_table_df, annotations_df, outliers_df, annot_outliers_df # <<<<<<<< Exit the function #MAKE SURE TO ADJUST THIS RETURN STATEMENT

        if table_coords:
            # logger.debug("Processing table data")

            rect = fitz.Rect(table_coords['x0'], table_coords['y0'],
                             table_coords['x1'], table_coords['y1'])

            ##########################################################
            filename = os.path.basename(pdf_path) # <-- Why is this here?
            directory_path = os.path.dirname(pdf_path)
            parent_folders = directory_path.split(os.sep)
            parent_folders_str = str(parent_folders)
            ##########################################################

            # Extract text blocks from the defined area
            blocks = page.get_text("dict", clip=rect)["blocks"]
            raw_table_data = []
            annotations_data = []  # List to hold annotations data

            for block in blocks:
                try: # Each block contains one or more lines of text
                    for line in block["lines"]:
                    # for line in block.get("lines", []):  # Returns an empty list if 'lines' key is absent
                        # Each line contains one or more spans (text elements)
                        for span in line["spans"]:
                            text = span["text"].strip()
                            bbox = span["bbox"]
                            font = span.get("font", "Unknown")  # Get the font name
                            font_size = span.get("size", 0)    # Get the font size
                            flags = span.get("flags", 0)       # Get the text flags

                            #structured_data.append({
                            raw_table_data.append({
                                "TableType": table_type,
                                "DataSource": "text",
                                "PdfPath": pdf_path,  # Include PDF path
                                "PdfPage": page_num + 1,  # Include PDF page number
                                "Type": '',  # Include PDF page number
                                "Text": text,
                                "Subject": '',
                                "Contents": '',
                                "Coordinates": bbox,
                                "Font": font,
                                "FontSize": font_size,
                                "Flags": flags,
                                "CreationDate": '',
                                "ModDate": ''
                            })
                except Exception as e:
                    logger.error(f"'lines' key missing in block on page {page_num}: {e}", exc_info=True)
                #raw_table_df = pd.DataFrame(raw_table_data)

            # Now, process annotations
            annotations = page.annots()
            if annotations:
                for annot in annotations:
                    annot_rect = annot.rect
                    if rect.intersects(annot_rect):  # Check if the annotation intersects with the table
                        annot_info = annot.info
                        # annotations_data.append({
                        annotations_data.append({
                            "TableType": table_type,
                            "DataSource": "annotations",
                            "PdfPath": pdf_path,
                            "PdfPage": page_num + 1,
                            "Type": annot_info.get("type", ""),
                            "Text": '',
                            "Subject": annot_info.get("subject", ""),  # Subject might be a better descriptor
                            "Contents": annot_info.get("content", "").strip(),
                            "Coordinates": (annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                            "Font": '',
                            "FontSize": '',
                            "Flags": '',
                            "CreationDate": annot_info.get("creationDate", ""),
                            "ModDate": annot_info.get("modDate", "")
                        })

            # Convert raw_table_data and annotations_data to DataFrames
            raw_table_df = pd.DataFrame(raw_table_data)
            annotations_df = pd.DataFrame(annotations_data)

            #Identify outliers in the text data and logically structure it
            #if len(raw_table_df)>0:
            if not raw_table_df.empty:
                #export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
                potential_outliers_df, regular_data_df = analyze_and_filter_outliers(raw_table_df)

                # Filter out outliers from the raw data
                structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
                outliers_data = raw_table_df[raw_table_df.isin(potential_outliers_df).all(1)]

                # Process only non-outlier data for structured_table_df
                structured_table_df = create_logical_structure_text(structured_data, column_coords, numbered_rows=True, annot_table=False)

                # Insert columns at the beginning of the dataframe
                structured_table_df.insert(0, 'pdf_page', page_num + 1)
                structured_table_df.insert(0, 'sys_filename', filename)
                structured_table_df.insert(0, 'sys_path', pdf_path)
                structured_table_df.insert(0, 'sys_build', parent_folders_str)

                # Process outliers_df
                outliers_df = outliers_data
            else:
                structured_table_df = pd.DataFrame()
                outliers_df = pd.DataFrame()

            #if len(annotations_df):
            if not annotations_df.empty:

                annotations_df = create_logical_structure_annot(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)

                # # Insert columns at the beginning of the dataframe
                annotations_df.insert(0, 'pdf_page', page_num + 1)
                annotations_df.insert(0, 'sys_filename', filename)
                annotations_df.insert(0, 'sys_path', pdf_path)
                annotations_df.insert(0, 'sys_build', parent_folders_str)

                #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
            else:
                #logger.debug("\n\nAnnotations table is empty for '{table_type}'.")
                pass
            # Store results for each table type
            structured_data_results[table_type] = structured_table_df
            outliers_results[table_type] = outliers_df
            annotations_results[table_type] = annotations_df


    # Accessing the structured data for 'bom'
    bom_structured_df = structured_data_results['bom']
    # Do something with bom_structured_df

    # Accessing the outliers for 'spec'
    spec_structured_df = structured_data_results['spec']
    # Do something with spec_outliers_df

    # Accessing the annotations for 'spool'
    spool_structured_df = structured_data_results['spool']

    # print(f"\n\nBOM {page_num}: \n {bom_structured_df}\n-----------------------------------\n")
    # print(f"\n\nSPEC: {page_num} \n {spec_structured_df}\n-----------------------------------\n")
    # print(f"\n\nSPOOL: {page_num} \n {spool_structured_df}\n-----------------------------------\n")

    #return structured_data_results, annotations_results, outliers_results

    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df #, annotations_df

def get_table_data_latest(pdf_path, page, page_num, converted_roi_payload, missing_pos=False):
    #logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()
    annotations_df = pd.DataFrame()  # DataFrame for annotations
    outliers_df = pd.DataFrame()
    annot_outliers_df = pd.DataFrame()

    # Set file info
    filename = os.path.basename(pdf_path)
    directory_path = os.path.dirname(pdf_path)
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)

    # --> Find the 'BOM', 'SPEC', or 'Spool' item and its table coordinates
    table_coords, column_coords = get_table_coordinates_latest(converted_roi_payload)

    if not table_coords:
        logger.error("No table coordinates found for 'bom' item")
        return structured_table_df, annotations_df, outliers_df, annot_outliers_df # <<<<<<<< Exit the function

    #if table_coords:
    # logger.debug("Processing table data")

    rect = fitz.Rect(table_coords['x0'], table_coords['y0'],
                        table_coords['x1'], table_coords['y1'])

    # --> # Extract text blocks and annotations from the defined area
    raw_table_data = extract_text_blocks(pdf_path, page, page_num, rect)
    annotations_data = extract_annotations(pdf_path, page, page_num, rect)

    # Convert raw_table_data and annotations_data to DataFrames
    raw_table_df = pd.DataFrame(raw_table_data)
    annotations_df = pd.DataFrame(annotations_data)

    # --> get_outliers_text
    #Identify outliers in the text data and logically structure it
    if len(raw_table_df)>0:
        # export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
        potential_outliers_df, regular_data_df = analyze_and_filter_outliers(raw_table_df)

        # Filter out outliers from the raw data
        structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
        outliers_data = raw_table_df[raw_table_df.isin(potential_outliers_df).all(1)]

        # Process only non-outlier data for structured_table_df
        #structured_table_df = create_logical_structure_with_dynamic_rows(structured_data, column_coords, annot_table=False)
        structured_table_df = create_logical_structure_text(structured_data, column_coords, numbered_rows=True, annot_table=False)

        # Insert columns at the beginning of the dataframe
        structured_table_df.insert(0, 'pdf_page', page_num + 1)
        structured_table_df.insert(0, 'sys_filename', filename)
        structured_table_df.insert(0, 'sys_path', pdf_path)
        structured_table_df.insert(0, 'sys_build', parent_folders_str)

        # Process outliers_df
        outliers_df = outliers_data

    if len(annotations_df):

        # print(f"\n\nGET TABLE DATA LATEST: {annotations_df}")

        #annotations_df = create_logical_structure_2(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)
        annotations_df = create_logical_structure_annot(annotations_df, column_coords, annot_table=True)
        # # Insert columns at the beginning of the dataframe
        annotations_df.insert(0, 'pdf_page', page_num + 1)
        annotations_df.insert(0, 'sys_filename', filename)
        annotations_df.insert(0, 'sys_path', pdf_path)
        annotations_df.insert(0, 'sys_build', parent_folders_str)



        #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
    else:
            # logger.debug("\n\nAnnotations table is empty.")
            pass

    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df #, annotations_df # NOT IN USE


############################################################################
# def adjust_bbox_90(bbox, rotation_matrix):
#     """
#     Adjust a bounding box based on the page's rotation matrix.

#     Parameters:
#     - bbox: The original bounding box (x0, y0, x1, y1).
#     - rotation_matrix: The matrix used to transform points based on the page's rotation.

#     Returns:
#     - The adjusted bounding box as a tuple (x0, y0, x1, y1).
#     """
#     # Extract the bounding box corners as points
#     p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
#     p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

#     # Apply the rotation matrix to each point
#     p1_transformed = p1 * rotation_matrix
#     p2_transformed = p2 * rotation_matrix

#     # Ensure x1 > x0 and y1 > y0
#     x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
#     y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

#     # Construct and return the adjusted bounding box with corrected order
#     adjusted_bbox = (x0, y0, x1, y1)
#     return adjusted_bbox


