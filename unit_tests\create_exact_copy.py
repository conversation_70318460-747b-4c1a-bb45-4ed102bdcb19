import fitz  # PyMuPDF
import os
from datetime import datetime

"""
Create an exact copy of a pdf. (Solely to have new created/modified time stamps)
"""

# Define file paths
source_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 025\Modified\Shady Hills.pdf"
target_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\Joey <PERSON>\<PERSON> - <PERSON> Hills Set 1.pdf"

# Ensure the target directory exists
target_dir = os.path.dirname(target_path)
if not os.path.exists(target_dir):
    os.makedirs(target_dir)

# Open the source PDF
source_doc = fitz.open(source_path)

# Create a new PDF document
target_doc = fitz.open()

# Copy each page from source to target
for page_num in range(len(source_doc)):
    page = source_doc.load_page(page_num)
    target_doc.insert_pdf(source_doc, from_page=page_num, to_page=page_num)

# Save the target document - this will automatically set new creation/modification dates
target_doc.save(target_path)

# Close both documents
source_doc.close()
target_doc.close()

print(f"PDF copied successfully to: {target_path}")
print(f"New creation time: {datetime.fromtimestamp(os.path.getctime(target_path))}")
print(f"New modification time: {datetime.fromtimestamp(os.path.getmtime(target_path))}")
