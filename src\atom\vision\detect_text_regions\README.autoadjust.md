# Template Application System

## Overview

The Template Application System is a core component of the Architekt ATOM platform that enables the automatic identification and extraction of Regions of Interest (ROIs) from construction drawings. This system works by:

1. Creating a template from a reference drawing with known ROI locations
2. Detecting structural elements (grid lines, rectangles) in both the template and target drawings
3. Matching structures between the template and target drawings
4. Transforming ROI coordinates from the template to the target drawing
5. Calculating confidence scores for each match
6. Generating visualizations and output files for review

This README provides a detailed explanation of the system's components, workflow, and implementation details to help developers understand and extend the functionality.

## Key Files

- `apply_template.py`: Main module for applying templates to target drawings
- `detect_grid_regions_cv2.py`: Detects grid lines and rectangles in drawings
- `adjust_coords_to_template.py`: Adjusts coordinates based on detected grid regions
- `read_roi.py`: Utilities for reading ROI data from JSON files

## Quick Start Guide

### Running Grid Detection

The `detect_grid_regions_cv2.py` script can be run directly to detect grid lines and rectangles in an image:

```python
from src.atom.vision.detect_text_regions.detect_grid_regions_cv2 import detect_grid_lines

# Detect grid lines and rectangles in an image
results = detect_grid_lines(
    image_path="path/to/image.png",
    debug=True,
    debug_dir="path/to/debug_output"
)

# Access detected rectangles
rectangles = results['rectangles']
```

Parameters:
- `image_path`: Path to the image file or a loaded image (numpy array)
- `debug`: Whether to generate debug visualizations (default: False)
- `debug_dir`: Directory to save debug visualizations (default: same as image_path)
- `grid_size`: Size of the grid for line detection (default: 30)
- `min_length`: Minimum length of lines to consider (default: 100)

The function returns a dictionary containing:
- `rectangles`: List of detected rectangles in format (x, y, width, height, id)
- `horizontal_lines`: List of detected horizontal lines
- `vertical_lines`: List of detected vertical lines
- `combined_image`: Visualization of detected lines and rectangles

### Creating a Template

The `adjust_coords_to_template.py` script is used to create a template from a reference drawing:

```python
from src.atom.vision.detect_text_regions.adjust_coords_to_template import adjust_coords_to_template

# Create a template from a reference drawing
result = adjust_coords_to_template(
    roi_json_path="path/to/roi.json",
    image_path="path/to/reference_image.png",
    output_dir="path/to/output",
    debug=True
)

# Access the output template path
template_path = result['output_json_path']
```

Parameters:
- `roi_json_path`: Path to the JSON file containing ROI definitions
- `image_path`: Path to the reference image
- `output_dir`: Directory to save output files (default: same as image_path)
- `debug`: Whether to generate debug visualizations (default: False)

The function returns a dictionary containing:
- `matched_regions`: Dictionary of matched regions
- `visualization_path`: Path to the visualization image
- `output_json_path`: Path to the output template JSON file

The input ROI JSON should contain definitions of regions to be detected, with their names and coordinates. The output template JSON will contain these regions adjusted to match the detected grid structure.

## Workflow Explanation

### 1. Template Creation

Before applying a template, you need to create one. This is typically done using the `adjust_coords_to_template.py` script:

```python
from src.atom.vision.detect_text_regions.adjust_coords_to_template import adjust_coords_to_template

# Create a template from a reference drawing
adjust_coords_to_template(
    roi_json_path="path/to/roi.json",
    image_path="path/to/reference_image.png",
    output_dir="path/to/output",
    debug=True
)
```

This generates a template JSON file containing:
- Original image path
- ROI coordinates (relative to the image size)
- Group information
- Additional metadata

### 2. Template Application

The main functionality is in `apply_template.py`, which applies a template to a target drawing:

```python
from src.atom.vision.detect_text_regions.apply_template import apply_template_to_page

# Apply template to a target drawing
result = apply_template_to_page(
    template_json_path="path/to/template.json",
    target_image_path="path/to/target_image.png",
    output_dir="path/to/output",
    debug=True
)
```

### 3. Running from Command Line

You can also run the scripts directly from the command line:

```bash
# Run grid detection
python -m src.atom.vision.detect_text_regions.detect_grid_regions_cv2 --image_path="path/to/image.png" --debug

# Create a template
python -m src.atom.vision.detect_text_regions.adjust_coords_to_template --roi_json_path="path/to/roi.json" --image_path="path/to/image.png" --debug

# Apply a template
python -m src.atom.vision.detect_text_regions.apply_template
```

The `apply_template.py` script has default paths configured in the `__main__` section, which you can modify before running.

## Detailed Implementation

The `apply_template_to_page` function in `apply_template.py` implements the following steps:

### Step 1: Loading Template Data

```python
# Load the template JSON file
with open(template_json_path, 'r') as f:
    template_data = json.load(f)

# Extract template ROIs
template_rois = template_data.get('regions', {})
```

The template JSON contains ROI coordinates and metadata from the reference drawing.

### Step 2: Loading Images

```python
# Load template and target images
template_img = cv2.imread(template_data.get('image_path'))
target_img = cv2.imread(target_image_path)
```

Both the template and target images are loaded for processing.

### Step 3: Detecting Grid Regions

```python
# Detect grid regions in template and target images
template_grid_results = detect_grid_lines(template_img, debug=debug, debug_dir=debug_dir)
template_rectangles = template_grid_results['rectangles']

target_grid_results = detect_grid_lines(target_img, debug=debug, debug_dir=debug_dir)
target_rectangles = target_grid_results['rectangles']
```

The `detect_grid_lines` function from `detect_grid_regions_cv2.py` identifies horizontal and vertical lines in the drawings, then detects rectangles formed by their intersections. These rectangles often correspond to title blocks, tables, and other structural elements.

### Step 4: Clustering Rectangles

```python
# Cluster rectangles based on proximity
template_clusters = cluster_rectangles(template_rectangles)
target_clusters = cluster_rectangles(target_rectangles)
```

The `cluster_rectangles` function groups nearby rectangles into clusters using DBSCAN (Density-Based Spatial Clustering of Applications with Noise). This helps identify logical groups of rectangles that likely belong together, such as title blocks or tables.

### Step 5: Matching Clusters

```python
# Match clusters between template and target
cluster_matches, match_scores = match_clusters(template_clusters, target_clusters)
```

The `match_clusters` function identifies corresponding clusters between the template and target drawings based on structural similarity. It creates a "fingerprint" for each cluster (based on relative positions, sizes, etc.) and matches them using similarity metrics.

### Step 6: Calculating Transformations

```python
# Calculate transformations for each matched cluster
transformations = {}
for template_id, target_id in cluster_matches.items():
    transformation = calculate_transformation(
        template_clusters[template_id],
        target_clusters[target_id]
    )
    transformations[template_id] = transformation
```

For each matched cluster, a transformation is calculated that maps coordinates from the template to the target drawing. This includes scaling factors and translation offsets.

### Step 7: Transforming ROI Coordinates

```python
# Transform ROI coordinates from template to target
adjusted_rois = {}
confidence_scores = {}

for group_id, rois in template_rois.items():
    # Find best matching cluster for this group
    best_cluster, cluster_confidence = find_roi_cluster(...)

    # Apply transformation to ROI coordinates
    for roi_name, roi_coords in rois.items():
        adjusted_coords, confidence = transform_roi_coordinates(
            roi_coords,
            transformations[best_cluster]
        )
        # Store adjusted coordinates and confidence scores
        ...
```

Each ROI from the template is assigned to the best matching cluster, and its coordinates are transformed using the corresponding transformation. A confidence score is calculated for each ROI based on how well the transformation preserves its properties.

The `transform_roi_coordinates` function applies scaling and translation to the ROI coordinates:

```python
def transform_roi_coordinates(roi_coords, transformation):
    # Extract ROI coordinates
    roi_x0, roi_y0, roi_x1, roi_y1 = roi_coords

    # Extract transformation parameters
    scale_x = transformation['scale_x']
    scale_y = transformation['scale_y']
    translation_x = transformation['translation_x']
    translation_y = transformation['translation_y']

    # Apply transformation
    adjusted_x0 = roi_x0 * scale_x + translation_x
    adjusted_y0 = roi_y0 * scale_y + translation_y
    adjusted_x1 = roi_x1 * scale_x + translation_x
    adjusted_y1 = roi_y1 * scale_y + translation_y

    # Calculate confidence based on aspect ratio preservation
    # ...

    # Ensure coordinates are within bounds [0, 1]
    adjusted_x0 = max(0, min(0.95, adjusted_x0))
    adjusted_y0 = max(0, min(0.95, adjusted_y0))
    adjusted_x1 = max(0.05, min(1, adjusted_x1))
    adjusted_y1 = max(0.05, min(1, adjusted_y1))

    return (adjusted_x0, adjusted_y0, adjusted_x1, adjusted_y1), confidence
```

The function includes validation to ensure that the transformed coordinates are valid and within the image boundaries. If the transformation would result in invalid coordinates (e.g., all set to 1.0), the function applies fallback logic to ensure the ROI remains visible.

### Step 8: Generating Visualizations

```python
# Create visualizations
vis_img = target_img.copy()
for group_id, rois in adjusted_rois.items():
    for roi_name, (coords, confidence) in rois.items():
        # Draw rectangle with color based on confidence
        color = get_confidence_color(confidence)
        cv2.rectangle(vis_img, ...)
        cv2.putText(vis_img, ...)
```

Visualizations are generated to show the adjusted ROIs on the target drawing, with colors indicating confidence levels.

### Step 9: Saving Results

```python
# Save adjusted ROIs to JSON
output_json = {
    'template_path': template_json_path,
    'target_path': target_image_path,
    'regions': adjusted_rois
}
with open(output_json_path, 'w') as f:
    json.dump(output_json, f, indent=4)

# Generate Excel report with confidence scores
generate_confidence_report(confidence_scores, target_image_path, output_dir)
```

The adjusted ROIs and confidence scores are saved to JSON and Excel files for further processing and review.

## Confidence Scoring

Confidence scores are calculated based on multiple factors:

1. **Aspect Ratio Preservation**: How well the transformation preserves the aspect ratio of the ROI
2. **Scale Reasonableness**: Whether the scaling factors are within reasonable bounds
3. **Position Shift**: How much the ROI's position shifts relative to its cluster
4. **Boundary Check**: Whether the adjusted coordinates are within the image boundaries

These scores help identify ROIs that may require manual review.

## Debugging and Visualization

When `debug=True`, the system generates various visualizations to help understand the process:

- Original images with detected rectangles
- Clustered rectangles with different colors for each cluster
- Matched clusters with connection lines
- Adjusted ROIs with color-coded confidence levels
- Combined visualizations for side-by-side comparison

These visualizations are saved to the specified output directory.

## Extending the System

### Adding New ROI Types

To add support for new ROI types (e.g., specific table formats):

1. Update the `read_roi.py` module to recognize the new ROI type
2. Modify the `transform_roi_coordinates` function if special handling is needed
3. Update visualization code to properly display the new ROI type

### Improving Matching Accuracy

To improve the accuracy of structure matching:

1. Enhance the `calculate_cluster_fingerprint` function to capture more distinctive features
2. Modify the `compare_fingerprints` function to use more sophisticated similarity metrics
3. Adjust the weights in the confidence score calculation to prioritize relevant factors

### Performance Optimization

For processing large batches of drawings:

1. Implement parallel processing for multiple target drawings
2. Add caching for template data and intermediate results
3. Optimize image processing operations for speed

## Common Issues and Solutions

### No Rectangles Detected

If no rectangles are detected in a drawing:

1. Check if the drawing has clear grid lines and structural elements
2. Adjust the `grid_size` and `min_length` parameters in `detect_grid_lines`
3. Try preprocessing the image to enhance contrast and reduce noise

### Poor Matching Quality

If ROIs are not correctly matched:

1. Verify that the template and target drawings have similar structural layouts
2. Adjust the clustering parameters (`eps` and `min_samples`) in `cluster_rectangles`
3. Modify the matching threshold in `match_clusters`

### Low Confidence Scores

If confidence scores are consistently low:

1. Check if the template and target drawings have significant differences in scale or layout
2. Adjust the weights in the confidence score calculation
3. Consider creating a more specific template for the target drawing type

### ROIs Not Visible in Output

If ROIs are not visible in the output visualization:

1. Check the `adjusted_template_applied.json` file to see if the ROI coordinates are valid
2. Look for coordinates like `(1, 1, 1, 1)` which indicate a transformation issue
3. Ensure the transformation function is correctly handling relative coordinates
4. Add debugging to the `transform_roi_coordinates` function to see what's happening
5. Verify that the drawing code is using the correct coordinates and color values

### Template Structure Mismatch

The system supports two template JSON structures:

1. Standard structure with `groups` and `rois` keys
2. Alternative structure with `rectangles` key (used by some grid detection tools)

If you're using an alternative structure, the system will automatically convert it to the standard format. If you encounter issues:

1. Check the template JSON structure to ensure it has the expected keys
2. Add debugging to see what structure is being detected
3. Modify the template loading code to handle your specific structure

## Template JSON Structure

The system supports two different template JSON structures:

### Standard Structure (Created by adjust_coords_to_template.py)

```json
{
  "original_roi_path": "path/to/original_roi.json",
  "image_path": "path/to/template_image.png",
  "groups": {
    "1": {
      "rois": [
        {
          "name": "lineNumber",
          "relativeX0": 0.1,
          "relativeY0": 0.1,
          "relativeX1": 0.2,
          "relativeY1": 0.2,
          "isTable": false
        },
        ...
      ]
    }
  }
}
```

### Alternative Structure (Created by detect_grid_regions_cv2.py)

```json
{
  "rectangles": [
    {
      "id": 1,
      "x": 0.023,
      "y": 0.023,
      "width": 0.953,
      "height": 0.953
    },
    ...
  ],
  "image_path": "path/to/image.png",
  "grid_size": 30,
  "min_length": 0
}
```

The system will automatically detect which structure is being used and handle it appropriately.

## Conclusion

The Template Application System provides a robust solution for automatically identifying and extracting ROIs from construction drawings. By understanding the underlying algorithms and implementation details, developers can extend and customize the system to meet specific requirements.

The recent updates to the system include:
1. Support for multiple template JSON structures
2. Improved coordinate transformation with validation
3. Better error handling and debugging
4. Enhanced visualization of detected regions

For further assistance or to report issues, please contact the Architekt ATOM development team.
