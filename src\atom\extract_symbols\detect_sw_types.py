"""
Demo skript: Turn Drawings into PNGs
------------------------------------
Function to check if a detected 'shop_bw' is actually a SW type. Add support later to identify if joint is also an 'Olet' type.

Logic inspired from:  https://github.com/pymupdf/PyMuPDF-Utilities/blob/master/examples/extract-vector-graphics/detect_graphics.py
-------------------
# New detect_connect_lines and calculate_angle on 6.23.24 - See bu_detect_sw_types.py for last version
"""


import json, fitz, math
from shapely.geometry import Polygon, LineString
from fitz.utils import getColor
import random
try:
    from .matplotlib_draw_vector import plot_vectors_from_dict
except:
    from matplotlib_draw_vector import plot_vectors_from_dict


def get_finish_params(path, color=None, fill_opacity=None):
    if color:
        c = getColor(color)
    else:
        c = path.get("color")

    return {
        "even_odd": True if path.get("even_odd") is None else path["even_odd"],
        "stroke_opacity": 1.0 if path.get("stroke_opacity") is None else path["stroke_opacity"],
        "fill_opacity": 0 if path.get("fill") is None else path.get("fill_opacity", 1.0),  # Set fill_opacity to 0 if no fill
        "fill": path.get("fill", None),  # Set fill to None if no fill
        "color": c if path.get("color") is not None else None,
        "dashes": path.get("dashes", None),
        "closePath": False if path.get("closePath") is None else path["closePath"],
        "lineJoin": path.get("lineJoin", 0),
        "lineCap": max(path.get("lineCap", [0])),  # Default to 0 if None or empty
        "width": path.get("width", 1)
    }

def draw_vector(original_data, intersecting_data, connected_data, output_pdf, page_width, page_height):
    doc = fitz.open()  # Create a new PDF document
    page = doc.new_page(width=page_width, height=page_height)  # Create a new page with original dimensions
    shape = page.new_shape()  # Create a new shape object

    def draw_items(items):
        for item in items:
            if item[0] == "l":  # line
                shape.draw_line(item[1], item[2])
            elif item[0] == "re":  # rectangle
                shape.draw_rect(item[1])
            elif item[0] == "qu":  # quad
                shape.draw_quad(item[1])
            elif item[0] == "c":  # curve
                shape.draw_bezier(item[1], item[2], item[3], item[4])

    # Draw original vector elements in their order
    draw_items(original_data["items"])

    # Draw intersecting vector elements
    draw_items(intersecting_data["items"])

    # Draw connected vector elements
    draw_items(connected_data["items"])

    shape.finish(**get_finish_params(original_data))
    shape.commit()

    # Save the document
    doc.save(output_pdf)
    doc.close()
    
def extract_intersecting_lines(original_data, path_data):
    """
    Extracts lines that intersect with the original vector path data.
    
    Args:
        original_data (dict): A dictionary containing the original vector path data.
        path_data (dict): A dictionary containing the vector path data to check for intersections.
    
    Returns:
        list: A list of intersecting path data.
    """
    original_lines = [LineString([tuple(item[1]), tuple(item[2])]) for item in original_data['items'] if item[0] == 'l']
    intersecting_lines = []

    for item in path_data['items']:
        if item[0] == 'l':
            line = LineString([tuple(item[1]), tuple(item[2])])
            for original_line in original_lines:
                if original_line.intersects(line):
                    intersecting_lines.append(item)
                    break

    intersecting_path_data = path_data.copy()
    intersecting_path_data['items'] = intersecting_lines

    return intersecting_path_data # For detecting Socket weld intersections. 

def calculate_angle(line1, line2):
    """
    Calculate the angle between two lines.
    """
    def unit_vector(vector):
        magnitude = math.sqrt(vector[0]**2 + vector[1]**2)
        if magnitude == 0:
            return (0, 0)
        return (vector[0] / magnitude, vector[1] / magnitude)
    
    def angle_between(v1, v2):
        v1_u = unit_vector(v1)
        v2_u = unit_vector(v2)
        dot_product = v1_u[0] * v2_u[0] + v1_u[1] * v2_u[1]
        # Clamp the dot product to be within the range [-1, 1] to avoid math domain errors
        cos_angle = max(min(dot_product, 1.0), -1.0)
        return math.degrees(math.acos(cos_angle))

    x1, y1 = line1.coords[0]
    x2, y2 = line1.coords[1]
    x3, y3 = line2.coords[0]
    x4, y4 = line2.coords[1]
    
    v1 = (x2 - x1, y2 - y1)
    v2 = (x4 - x3, y4 - y3)
    
    return angle_between(v1, v2)

def detect_connected_lines(intersecting_data, path_data):
    """
    Detects lines connected to the ends of the intersecting lines.
    
    Args:
        intersecting_data (dict): A dictionary containing the intersecting vector path data.
        path_data (dict): A dictionary containing the vector path data.
    
    Returns:
        dict: A dictionary containing the connected path data.
    """
    #is_connected = False
    is_sw = False
    valid_paths = []  # Store validity of each path
    valid_path_ids = set()  # Track IDs of valid paths
    connected_lines = []
    connected_pairs = []
    
    intersect_lines = []
    intersected_lines_data = {}

    path_id = 0  # Initialize path ID

    
    for item in path_data["items"]:
        print(item)
        # plot_vectors_from_dict({"items": [item]})
    # path_data["items"] = path_data["items"][:7]
    # plot_vectors_from_dict(path_data)
    # plot_vectors_from_dict(intersecting_data)
    
    # Merge intersecting lines if they are joined end-to-end
    ignored = set()
    # intersecting_data_new = []
    # for intersecting_item in intersecting_data['items']:
    #     if str(intersecting_item) in ignored:
    #         continue
    #     intersecting_line = LineString([tuple(intersecting_item[1]), tuple(intersecting_item[2])])
    #     intersecting_data_new.append(intersecting_item)
    #     for path_item in path_data['items']:
    #         if str(path_item) in ignored:
    #             continue
    #         if path_item[0] == 'l' and path_item != intersecting_item:
    #             path_line = LineString([tuple(path_item[1]), tuple(path_item[2])])
    #             angle = calculate_angle(intersecting_line, path_line)
    #             if angle == 0:
    #                 ignored.add(str(intersecting_item))
    #                 ignored.add(str(path_item))
    #                 points = [intersecting_item[1], intersecting_item[2], path_item[1], path_item[2]]
    #                 min_x = min(points, key=lambda x: x[0])
    #                 max_x = max(points, key=lambda x: x[0])
    #                 del intersecting_data_new[-1]
    #                 intersecting_data_new.append(["l", min_x, max_x])
    #                 break

    # # del intersecting_data_new[-1]
    # # plot_vectors_from_dict({"items": intersecting_data_new}, title="Joined")
    # intersecting_data["items"] = intersecting_data_new

    for intersecting_item in intersecting_data['items']:
        # intersecting_path_data_2 = extract_intersecting_lines({"items": [intersecting_item]}, path_data)
        # intersecting_path_data_2["items"].append(intersecting_item)
        # plot_vectors_from_dict(intersecting_path_data_2, title="Joined")
        # continue
        if intersecting_item[0] == 'l':
            intersecting_line = LineString([tuple(intersecting_item[1]), tuple(intersecting_item[2])])
            for path_item in path_data['items']:
                if str(path_item) in ignored:
                    continue
                if path_item[0] == 'l' and path_item != intersecting_item:
                    path_line = LineString([tuple(path_item[1]), tuple(path_item[2])])
                    # print(intersecting_line, path_line, 333)
                    if intersecting_line.coords[1] == path_line.coords[0] or intersecting_line.coords[1] == path_line.coords[1] or \
                       intersecting_line.coords[0] == path_line.coords[0] or intersecting_line.coords[0] == path_line.coords[1]:
                        angle = calculate_angle(intersecting_line, path_line)
                        #print("CHECK ANGLE:", angle)
                        # plot_vectors_from_dict({"items": [intersecting_item, path_item]}, title=f"Joined {angle}")
                        v_counter=0
                        # Check if a line is connected to the intersecting line. (These lines would form the two sides of the "U" shape if true)
                        if 55 <= angle <= 65 or 115 <= angle <= 125:  # Check if angle is approximately 90 degrees (Account for 30 degree skew of isometric)
                            #print(f"Debug connected_line angle: {angle}")
                            
                            # Check for continuation Lines: if another line touches path_item that is not the intersecting_line or path_item itself
                            path_id += 1  # Assign an ID to the path
                            print(f"Path ID {path_id}: Connected line angle: {angle}")

                            path_line_endpoints = [path_line.coords[0], path_line.coords[1]]
                            continuation_detected = False
                            continuation_valid = True  # Assume continuation is valid unless proven otherwise
                            
                            for additional_item in path_data['items']:
                                if additional_item[0] == 'l' and additional_item not in [intersecting_item, path_item]:
                                    additional_line = LineString([tuple(additional_item[1]), tuple(additional_item[2])])
                                    if additional_line.coords[0] in path_line_endpoints or additional_line.coords[1] in path_line_endpoints:
                                        # continuation_angle = calculate_angle(path_line, additional_line)
                                        continuation_angle = calculate_angle(additional_line, path_line)
                                        print(f"Path ID {path_id}: Continuation angle: {continuation_angle}")
                                        if 55 <= continuation_angle <= 65 or 115 <= continuation_angle <= 125:
                                            continuation_detected = True
                                            connected_lines.append(additional_item)
                                            connected_pairs.append((path_item, additional_item))
                                            
                                            # Check for another continuation from the additional line
                                            another_continuation_detected = False
                                            for further_item in path_data['items']:
                                                if further_item[0] == 'l' and further_item not in [intersecting_item, path_item, additional_item]:
                                                    further_line = LineString([tuple(further_item[1]), tuple(further_item[2])])
                                                    if further_line.coords[0] in [additional_line.coords[0], additional_line.coords[1]] or \
                                                       further_line.coords[1] in [additional_line.coords[0], additional_line.coords[1]]:
                                                        another_continuation_detected = True
                                                        
                                                        valid_paths.append(False) # <-- Only 1 continuation allowed 
                                                        print(f"Path ID {path_id}: Another continuation detected.")
                                                        break
                                        
                                            if not another_continuation_detected:
                                                if path_id not in valid_path_ids:
                                                    valid_paths.append(True)
                                                    valid_path_ids.add(path_id)
                                                    print(f"Path ID {path_id}: Valid path with no further continuation")
                                        
                                            break  # Break after processing continuation
                                            
                                        else:
                                            print(f"Path ID {path_id}: Invalid continuation angle: {continuation_angle}")
                                            continuation_valid = False  # Invalid continuation angle
                                            valid_paths.append(False)
                                            break  # Exit loop if invalid continuation is found
                                    
                            if continuation_detected:
                                if continuation_valid:
                                    if path_id not in valid_path_ids:
                                        valid_paths.append(True)
                                        valid_path_ids.add(path_id)
                                        print(f"Path ID {path_id}: Valid path with valid continuation")
                                        break
                                    else:
                                        print(f"Path ID {path_id}: Valid path with valid continuation but path_id is already in set. Not added")
                                else:
                                    print(f"Path ID {path_id}: Invalid path due to invalid continuation")
                                    ## Set false here?
                            else:
                                if path_id not in valid_path_ids:
                                    valid_paths.append(True)
                                    valid_path_ids.add(path_id)
                                    print(f"Path ID {path_id}: Valid path with no continuation")
                       
                            connected_lines.append(path_item)
                            connected_pairs.append((intersecting_item, path_item))
                            # intersect_lines.append(intersecting_item)  # Add the intersecting line to the connected lines
                            #connected_lines.append(intersecting_item)  # Add the intersecting line to the connected lines

    # Determine if `is_sw` should be True based on the valid_paths
    if valid_paths.count(True) >= 2 and valid_paths.count(False) == 0:
        is_sw = True

    connected_path_data = path_data.copy()
    connected_path_data['items'] = connected_lines
    intersected_lines_data['items'] = intersect_lines
    
    print("Connected Path Data: ", connected_path_data)
    print("Intersect Path Data: ", connected_path_data)

    print(f"Valid Paths: {valid_paths}")
    return connected_path_data, is_sw

def process_vectors(original_vector_data, overlap_vector_data, page, vector_id):
    # Get page dimensions
    page_width = page.rect.width
    page_height = page.rect.height

    # Extract intersecting lines from the path data
    intersecting_path_data = extract_intersecting_lines(original_vector_data, overlap_vector_data)

    # Detect U-shaped lines from the intersecting path data using unfiltered path data
    u_shape_path_data, is_sw = detect_connected_lines(intersecting_path_data, overlap_vector_data)
    
    if is_sw: # Is a SW. Criteria: Line intersects original vector and has lines on either end that create a 'U' shape. 
        # Reset is_connected
        #num = random.random()

        # Draw and save the filtered vectors
        #output_pdf_path = f"filtered_vector_{vector_id}.pdf"
        #draw_vector(original_vector_data, intersecting_path_data, u_shape_path_data, output_pdf_path, page_width, page_height)
        #return True
        return original_vector_data, intersecting_path_data, u_shape_path_data, True
    
    else:
        return original_vector_data, None, None, False
        #return False  
    
        # Convert Rect and Point objects to serializable format
        def serialize_objects(data):
            if isinstance(data, dict):
                return {k: serialize_objects(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [serialize_objects(item) for item in data]
            elif isinstance(data, fitz.Rect):
                return (data.x0, data.y0, data.x1, data.y1)
            elif isinstance(data, fitz.Point):
                return (data.x, data.y)
            else:
                return data

        #intersecting_path_data_serializable = serialize_objects(intersecting_path_data)

        # Print the filtered path data
        #print(json.dumps(intersecting_path_data_serializable, indent=4))
    
# Example usage
# original_vector_data = ... # Load your original vector data here
# overlap_vector_data = ...  # Load your overlap vector data here
# page = ...  # Pass the fitz page object here
# process_vectors(original_vector_data, overlap_vector_data, page)

