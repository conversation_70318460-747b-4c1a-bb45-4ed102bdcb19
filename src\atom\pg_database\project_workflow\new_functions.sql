/*
    -- NEED TO ADD TO LOGIC: 
    SELECT public.fill_support_size_from_lf(project_id);
    SELECT public.fill_material_from_pipe(project_id);

    File uses new updated functions. 
    Test using new PostGreSQL branch 'dev'. New workflow uses a view making 'public.atem_rfq' obsolete

    Abstract Workflow:
    1. --Upload the Directly to RFQ Input, then BOM if present using workbook_db_importer.py
    OR: Upload direct to `public.verified_material_classifications` (Recommended)
        ** UPLOAD BOM data to public.bom **
            
        --> Ensure the RFQ_INPUT records are present
    2. Upload BOM
        Run:
            -- Generate RFQ Input from BOM
            SELECT generate_rfq_input_from_bom(ARRAY[project_id]);
            -- Update RFQ Input Categories 
            SELECT update_rfq_input_categories(ARRAY[project_id]);


        ** If 'size1' and 'size2' functions are not present, run: **
            -- To process projects with IDs 13, 42, and 105
            `SELECT * FROM public.update_bom_sizes_for_projects(ARRAY[project_id]);`

    3. Run `SELECT * FROM update_bom_from_rfq_input(ARRAY[project_id]);` to update the BOM from the RFQ Input
    
    4. Validate Quantities
        -- 1. Validate BOM vs RFQ View
            SELECT * FROM validate_bom_vs_view(project_id);

        -- 2. Verify General
            SELECT
                SUM(length) AS total_length,
                SUM(elbows_90) AS total_elbows_90,
                SUM(elbows_45) AS total_elbows_45,
                SUM(bevels) AS total_bevels,
                SUM(tees) AS total_tees,
                SUM(reducers) AS total_reducers,
                SUM(caps) AS total_caps,
                SUM(flanges) AS total_flanges,
                SUM(valves_flanged) AS total_valves_flanged,
                SUM(valves_welded) AS total_valves_welded,
                SUM(cut_outs) AS total_cut_outs,
                SUM(supports) AS total_supports,
                SUM(bends) AS total_bends,
                SUM(union_couplings) AS total_union_couplings,
                SUM(expansion_joints) AS total_expansion_joints,
                SUM(field_welds) AS total_field_welds,
                SUM(calculated_eq_length) AS total_calculated_eq_length,
                SUM(calculated_area) AS total_calculated_area
            FROM manage_bom_to_general_aggregation(project_id, FALSE);
            

    5. Force refresh qunatities if validation fails
        SELECT public.force_refresh_bom_categories(21);
*/


/*
-- Manually propogate BOM to RFQ
    - Updates 'public.bom' from 'atem_rfq_input'
    - Accepts an array of project IDs to update

-- Usage:
-- Test with a single project ID
SELECT * FROM update_bom_from_rfq_input(ARRAY[id]);

*/



-- Drop the existing function
DROP FUNCTION IF EXISTS update_bom_from_rfq_input(integer[]);

-- Create the function with explicit table aliases
CREATE OR REPLACE FUNCTION update_bom_from_rfq_input(project_ids integer[])
RETURNS TABLE(result_project_id integer, updated_count integer) AS $$
DECLARE
    project_id_var integer;
    updated_count_var integer;
BEGIN
    -- Process each project ID
    FOREACH project_id_var IN ARRAY project_ids
    LOOP
        -- Update BOM records for this project
        WITH input_data AS (
            -- Get the latest version of each material description from rfq_input
            SELECT DISTINCT ON (LOWER(ri.material_description))
                ri.material_description,
                ri.rfq_scope,
                ri.general_category,
                ri.unit_of_measure,
                ri.material,
                ri.abbreviated_material,
                ri.technical_standard,
                ri.astm,
                ri.grade,
                ri.rating,
                ri.schedule,
                ri.coating,
                ri.forging,
                ri.ends,
                ri.pipe_category,
                ri.valve_type,
                ri.fitting_category,
                ri.weld_category,
                ri.bolt_category,
                ri.gasket_category,
                ri.notes
            FROM public.atem_rfq_input AS ri
            WHERE 
                ri.project_id = project_id_var
                AND ri.deleted IS NOT TRUE
            ORDER BY LOWER(ri.material_description), ri.updated_at DESC
        )
        UPDATE public.bom AS b
        SET
            rfq_scope = i.rfq_scope,
            general_category = i.general_category,
            unit_of_measure = i.unit_of_measure,
            material = i.material,
            abbreviated_material = i.abbreviated_material,
            technical_standard = i.technical_standard,
            astm = i.astm,
            grade = i.grade,
            rating = i.rating,
            schedule = i.schedule,
            coating = i.coating,
            forging = i.forging,
            ends = i.ends,
            pipe_category = i.pipe_category,
            valve_type = i.valve_type,
            fitting_category = i.fitting_category,
            weld_category = i.weld_category,
            bolt_category = i.bolt_category,
            gasket_category = i.gasket_category,
            notes = i.notes,
            updated_at = CURRENT_TIMESTAMP
        FROM input_data AS i
        WHERE 
            b.project_id = project_id_var
            AND LOWER(b.material_description) = LOWER(i.material_description)
            AND b.deleted IS NOT TRUE;
        
        -- Get the count of updated rows
        GET DIAGNOSTICS updated_count_var = ROW_COUNT;
        
        -- Return the result for this project
        result_project_id := project_id_var;
        updated_count := updated_count_var;
        RETURN NEXT;
    END LOOP;
    
CREATE OR REPLACE FUNCTION generate_rfq_input_from_bom(project_ids integer[])

/*
-- Example usage:
SELECT generate_rfq_input_from_bom(ARRAY[1, 2, 3]);
*/

RETURNS integer AS $$
DECLARE
    records_created integer := 0;
BEGIN
    -- Insert unique material descriptions from bom into atem_rfq_input
    INSERT INTO public.atem_rfq_input (
        material_description,
        project_id,
        created_at,
        updated_at
    )
    SELECT DISTINCT
        b.material_description,
        b.project_id,
        NOW(),
        NOW()
    FROM 
        public.bom b
    WHERE 
        b.project_id = ANY(project_ids)
        AND b.material_description IS NOT NULL
        AND NOT EXISTS (
            -- Check if this material description already exists for this project
            SELECT 1 
            FROM public.atem_rfq_input ri 
            WHERE ri.material_description = b.material_description
            AND ri.project_id = b.project_id
        );
    
    GET DIAGNOSTICS records_created = ROW_COUNT;
    
    RETURN records_created;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_rfq_input_categories(project_ids integer[])
RETURNS integer AS $$
DECLARE
    records_updated integer := 0;
BEGIN
    -- Update category columns in atem_rfq_input from verified_material_classifications
    UPDATE public.atem_rfq_input ri
    SET 
        rfq_scope = vc.rfq_scope,
        general_category = vc.general_category,
        pipe_category = vc.pipe_category,
        fitting_category = vc.fitting_category,
        valve_type = vc.valve_type,
        bolt_category = vc.bolt_category,
        gasket_category = vc.gasket_category,
        abbreviated_material = vc.abbreviated_material,
        material = vc.material,
        grade = vc.grade,
        schedule = vc.schedule,
        rating = vc.rating,
        astm = vc.astm,
        technical_standard = vc.technical_standard,
        unit_of_measure = vc.unit_of_measure,
        coating = vc.coating,
        forging = vc.forging,
        weld_category = vc.weld_category,
        ends = vc.ends,
        normalized_description = vc.normalized_description,
        updated_at = NOW()
    FROM 
        public.verified_material_classifications vc
    WHERE 
        ri.project_id = ANY(project_ids)
        AND (
            -- Match on normalized_description if available
            (ri.normalized_description IS NOT NULL AND ri.normalized_description = vc.normalized_description)
            OR 
            -- Otherwise match on material_description
            (ri.normalized_description IS NULL AND ri.material_description = vc.material_description)
        );
    
    GET DIAGNOSTICS records_updated = ROW_COUNT;
    
    RETURN records_updated;
END;
$$ LANGUAGE plpgsql;

-- Example usage:
--SELECT update_rfq_input_categories(ARRAY[1, 2, 3]);


-- Function to force refresh BOM categories for a specific job number
CREATE OR REPLACE FUNCTION public.force_refresh_bom_categories(job_id INTEGER)
-- Example of how to call the function:
-- SELECT public.force_refresh_bom_categories(20);
RETURNS VOID AS
$$
BEGIN
    -- Update general_category for rows where it's not NULL
    UPDATE public.bom
    SET general_category = general_category
    WHERE project_id = job_id 
    AND general_category IS NOT NULL;
    
    -- Update rfq_scope for rows where general_category is NULL but rfq_scope is not
    UPDATE public.bom
    SET rfq_scope = rfq_scope
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NOT NULL;
    
    -- Update fitting_category for rows where higher priority columns are NULL
    UPDATE public.bom
    SET fitting_category = fitting_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NOT NULL;
    
    -- Update pipe_category for rows where higher priority columns are NULL
    UPDATE public.bom
    SET pipe_category = pipe_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NOT NULL;
    
    -- Update valve_type for rows where higher priority columns are NULL
    UPDATE public.bom
    SET valve_type = valve_type
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NOT NULL;
    
    -- Update weld_category for rows where higher priority columns are NULL
    UPDATE public.bom
    SET weld_category = weld_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NULL
    AND weld_category IS NOT NULL;
    
    -- Update bolt_category for rows where higher priority columns are NULL
    UPDATE public.bom
    SET bolt_category = bolt_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NULL
    AND weld_category IS NULL
    AND bolt_category IS NOT NULL;
    
END;
$$ LANGUAGE plpgsql;


-- Function to force refresh BOM categories for a specific job number
CREATE OR REPLACE FUNCTION public.force_refresh_input_categories(job_id INTEGER)
RETURNS VOID AS
$$
BEGIN
    
    -- Update fitting_category for rows where higher priority columns are NULL
    UPDATE public.atem_rfq_input
    SET fitting_category = fitting_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NOT NULL;
    
    -- Update pipe_category for rows where higher priority columns are NULL
    UPDATE public.atem_rfq_input
    SET pipe_category = pipe_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NOT NULL;
    
    -- Update valve_type for rows where higher priority columns are NULL
    UPDATE public.atem_rfq_input
    SET valve_type = valve_type
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NOT NULL;
    
    -- Update weld_category for rows where higher priority columns are NULL
    UPDATE public.atem_rfq_input
    SET weld_category = weld_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NULL
    AND weld_category IS NOT NULL;
    
    -- Update bolt_category for rows where higher priority columns are NULL
    UPDATE public.atem_rfq_input
    SET bolt_category = bolt_category
    WHERE project_id = job_id 
    AND general_category IS NULL
    AND rfq_scope IS NULL
    AND fitting_category IS NULL
    AND pipe_category IS NULL
    AND valve_type IS NULL
    AND weld_category IS NULL
    AND bolt_category IS NOT NULL;
    
END;
$$ LANGUAGE plpgsql;

-- Example of how to call the function:
-- SELECT public.force_refresh_input_categories(20);

/*
Fill in Supports with blank sizes based on the largest pipe (LF) size on the same page
*/

-- Example usage:
--SELECT public.fill_support_size_from_lf(20);

CREATE OR REPLACE FUNCTION public.fill_support_size_from_lf(project_id_param INTEGER)
RETURNS INTEGER AS
$$
DECLARE
    rows_updated INTEGER := 0;
BEGIN
    -- Update Support rows with NULL size1 using the largest size1 from LF rows with the same pdf_id
    WITH lf_sizes AS (
        -- Find the largest size1 for each pdf_id where general_category = 'LF'
        SELECT 
            pdf_id,
            MAX(size1) AS max_size1
        FROM 
            public.bom
        WHERE 
            project_id = project_id_param
            AND general_category = 'LF'
            AND size1 IS NOT NULL
        GROUP BY 
            pdf_id
    )
    UPDATE public.bom AS b
    SET 
        size1 = ls.max_size1
    FROM 
        lf_sizes AS ls
    WHERE 
        b.project_id = project_id_param
        AND b.general_category = 'Support'
        AND b.size1 IS NULL
        AND b.pdf_id = ls.pdf_id
        AND ls.max_size1 IS NOT NULL;
    
    -- Get the number of rows updated
    GET DIAGNOSTICS rows_updated = ROW_COUNT;
    
    RETURN rows_updated;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.fill_material_from_pipe(project_id_param INTEGER)
RETURNS INTEGER AS
$$
DECLARE
    rows_updated INTEGER := 0;
BEGIN
    -- Update rows with NULL material using material from Pipe rows
    WITH pipe_materials AS (
        -- Get all pipe materials with their sizes for matching
        SELECT 
            pdf_id,
            size1,
            material,
            abbreviated_material,
            -- Rank by size1 descending to get largest size as fallback
            ROW_NUMBER() OVER (PARTITION BY pdf_id ORDER BY size1 DESC NULLS LAST) as size_rank
        FROM 
            public.bom
        WHERE 
            project_id = project_id_param
            AND rfq_scope = 'Pipe'
            AND material IS NOT NULL
            AND material != ''
    ),
    material_source AS (
        -- For each row needing material, find the best pipe match
        SELECT DISTINCT
            b.pdf_id,
            b.size1 as target_size1,
            -- First try to find exact size1 match
            COALESCE(
                (SELECT pm1.material 
                 FROM pipe_materials pm1 
                 WHERE pm1.pdf_id = b.pdf_id AND pm1.size1 = b.size1 
                 LIMIT 1),
                -- If no exact match, use the largest size1 pipe material
                (SELECT pm2.material 
                 FROM pipe_materials pm2 
                 WHERE pm2.pdf_id = b.pdf_id AND pm2.size_rank = 1 
                 LIMIT 1)
            ) as source_material,
            -- Same logic for abbreviated_material
            COALESCE(
                (SELECT pm1.abbreviated_material 
                 FROM pipe_materials pm1 
                 WHERE pm1.pdf_id = b.pdf_id AND pm1.size1 = b.size1 
                 LIMIT 1),
                (SELECT pm2.abbreviated_material 
                 FROM pipe_materials pm2 
                 WHERE pm2.pdf_id = b.pdf_id AND pm2.size_rank = 1 
                 LIMIT 1)
            ) as source_abbreviated_material
        FROM 
            public.bom b
        WHERE 
            b.project_id = project_id_param
            AND b.rfq_scope IN ('Valves', 'Fittings', 'Pipe')
            AND (b.material IS NULL OR b.material = '')
    )
    UPDATE public.bom AS b
    SET 
        material = ms.source_material,
        abbreviated_material = ms.source_abbreviated_material
    FROM 
        material_source AS ms
    WHERE 
        b.project_id = project_id_param
        AND b.rfq_scope IN ('Valves', 'Fittings', 'Pipe')
        AND (b.material IS NULL OR b.material = '')
        AND b.pdf_id = ms.pdf_id
        AND b.size1 = ms.target_size1
        AND ms.source_material IS NOT NULL;
    
    -- Get the number of rows updated
    GET DIAGNOSTICS rows_updated = ROW_COUNT;
    
    RETURN rows_updated;
END;
$$ LANGUAGE plpgsql;

SELECT public.fill_support_size_from_lf(48);


