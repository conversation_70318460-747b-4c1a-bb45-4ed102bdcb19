"""
Example usage of the BOM Classification Audit System

This script demonstrates how to use the audit system with real API calls.
Make sure to set GEMINI_API_KEY environment variable before running.
"""

import asyncio
import pandas as pd
import os
from audit_main import (
    audit_bom_dataframe, 
    create_sample_dataframe, 
    print_audit_results,
    AuditConfig,
    ModelType,
    BOMAuditSystem
)


async def basic_audit_example():
    """Basic example of auditing a DataFrame"""
    print("=" * 60)
    print("BASIC AUDIT EXAMPLE")
    print("=" * 60)
    
    # Create sample data with known issues
    df = create_sample_dataframe()
    
    print("Input DataFrame:")
    print(df.to_string(index=False))
    
    try:
        # Run the audit
        results = await audit_bom_dataframe(df, max_concurrent=3)
        
        # Print results
        print_audit_results(results)
        
        return results
        
    except Exception as e:
        print(f"Error during audit: {e}")
        return None


async def custom_config_example():
    """Example using custom configuration"""
    print("\n" + "=" * 60)
    print("CUSTOM CONFIGURATION EXAMPLE")
    print("=" * 60)
    
    # Create custom configuration
    config = AuditConfig(
        primary_model=ModelType.GEMINI_20_FLASH,  # Use 2.0 as primary
        fallback_model=ModelType.GEMINI_15_FLASH,
        max_retries=5,
        temperature=0.1,  # Slightly higher temperature
        max_output_tokens=1500
    )
    
    print(f"Using custom config:")
    print(f"  Primary Model: {config.primary_model.value}")
    print(f"  Fallback Model: {config.fallback_model.value}")
    print(f"  Max Retries: {config.max_retries}")
    print(f"  Temperature: {config.temperature}")
    
    # Create test data
    df = pd.DataFrame({
        'id': ['test_1', 'test_2'],
        'material_description': [
            'Pipe SMLS, ASTM A312, SCH 40, #150, BE',  # Should be stainless
            'VENDOR XYZ123'  # Should be miscellaneous
        ],
        'rfq_scope': ['Pipe', 'Pipe'],
        'material': ['Steel, Carbon', 'Steel, Stainless'],  # Both wrong
        'astm': ['A312', ''],
        'pipe_category': ['Pipe', '']
    })
    
    print("\nInput DataFrame:")
    print(df.to_string(index=False))
    
    try:
        # Run audit with custom config
        results = await audit_bom_dataframe(df, config=config, max_concurrent=2)
        print_audit_results(results)
        
        return results
        
    except Exception as e:
        print(f"Error during audit: {e}")
        return None


async def detailed_analysis_example():
    """Example showing detailed analysis of results"""
    print("\n" + "=" * 60)
    print("DETAILED ANALYSIS EXAMPLE")
    print("=" * 60)
    
    # Create audit system for metrics tracking
    audit_system = BOMAuditSystem()
    
    # Create test data with various scenarios
    df = pd.DataFrame({
        'id': ['analysis_1', 'analysis_2', 'analysis_3', 'analysis_4'],
        'material_description': [
            'Elbow 90 LR, ASTM A234, WPB, 6", SCH 80, BE',  # Carbon steel fitting
            'Gate Valve, ASTM A216, WCB, 4", #600, RF',     # Carbon steel valve
            'Flange WN, ASTM A182, F316L, 8", #300, RF',    # Stainless steel flange
            'VENDOR PART ABC789'                              # Vendor code
        ],
        'rfq_scope': ['Fittings', 'Valves', 'Flanges', 'Pipe'],  # Last one wrong
        'material': ['Steel, Stainless', 'Steel, Carbon', 'Steel, Stainless', 'Steel, Carbon'],  # First one wrong
        'astm': ['A234', 'A216', 'A182', ''],
        'valve_type': ['', 'Gate Valve', '', ''],
        'fitting_category': ['90 LR Elbow', '', 'WN Flange RF', ''],
        'schedule': ['80', '', '', ''],
        'rating': ['', '600', '300', '']
    })
    
    print("Input DataFrame:")
    print(df.to_string(index=False))
    
    try:
        # Run audit
        results = await audit_system.audit_dataframe(df, max_concurrent=2)
        
        # Detailed analysis
        print("\n" + "=" * 60)
        print("DETAILED ANALYSIS")
        print("=" * 60)
        
        # Get metrics
        metrics = audit_system.get_metrics()
        print(f"\nProcessing Metrics:")
        for key, value in metrics.items():
            print(f"  {key}: {value}")
        
        # Analyze results by category
        issues_by_field = {}
        total_processing_time = 0
        
        for result in results:
            total_processing_time += result.processing_time
            
            if result.status == "issues":
                for field, issue in result.issues.items():
                    if field not in issues_by_field:
                        issues_by_field[field] = []
                    issues_by_field[field].append({
                        'id': result.id,
                        'current': issue.get('current_value'),
                        'suggested': issue.get('suggested'),
                        'confidence': issue.get('confidence')
                    })
        
        print(f"\nPerformance Analysis:")
        print(f"  Total Processing Time: {total_processing_time:.3f}s")
        print(f"  Average Time per Item: {total_processing_time/len(results):.3f}s")
        
        print(f"\nIssues by Field:")
        for field, field_issues in issues_by_field.items():
            print(f"  {field}: {len(field_issues)} issues")
            for issue in field_issues:
                print(f"    {issue['id']}: {issue['current']} → {issue['suggested']} (conf: {issue['confidence']})")
        
        # Print full results
        print_audit_results(results)
        
        return results
        
    except Exception as e:
        print(f"Error during audit: {e}")
        return None


async def batch_processing_example():
    """Example of processing larger batches"""
    print("\n" + "=" * 60)
    print("BATCH PROCESSING EXAMPLE")
    print("=" * 60)
    
    # Create larger dataset
    batch_data = []
    for i in range(20):
        batch_data.append({
            'id': f'batch_item_{i+1}',
            'material_description': f'Test Item {i+1}, ASTM A106, SCH 40',
            'rfq_scope': 'Pipe',
            'material': 'Steel, Stainless' if i % 2 == 0 else 'Steel, Carbon',  # Half wrong
            'astm': 'A106',
            'pipe_category': 'Pipe'
        })
    
    df = pd.DataFrame(batch_data)
    
    print(f"Processing {len(df)} items in batch...")
    print(f"Sample rows:")
    print(df.head(3).to_string(index=False))
    print("...")
    
    try:
        # Process with higher concurrency
        results = await audit_bom_dataframe(df, max_concurrent=8)
        
        # Summary statistics
        total_items = len(results)
        items_with_issues = sum(1 for r in results if r.status == "issues")
        error_items = sum(1 for r in results if r.status == "error")
        ok_items = sum(1 for r in results if r.status == "ok")
        
        print(f"\nBatch Processing Results:")
        print(f"  Total Items: {total_items}")
        print(f"  Items OK: {ok_items}")
        print(f"  Items with Issues: {items_with_issues}")
        print(f"  Items with Errors: {error_items}")
        print(f"  Success Rate: {(ok_items + items_with_issues) / total_items:.1%}")
        
        # Show timing analysis
        processing_times = [r.processing_time for r in results if r.processing_time > 0]
        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            
            print(f"\nTiming Analysis:")
            print(f"  Average Processing Time: {avg_time:.3f}s")
            print(f"  Min Processing Time: {min_time:.3f}s")
            print(f"  Max Processing Time: {max_time:.3f}s")
            print(f"  Total Processing Time: {sum(processing_times):.3f}s")
        
        return results
        
    except Exception as e:
        print(f"Error during batch processing: {e}")
        return None


async def main():
    """Run all examples"""
    print("BOM Classification Audit System - Usage Examples")
    print("=" * 80)
    
    # Check if API key is available
    if not os.environ.get("GEMINI_API_KEY"):
        print("⚠️  GEMINI_API_KEY environment variable not set!")
        print("   Please set your API key to run these examples.")
        print("   Example: export GEMINI_API_KEY='your_api_key_here'")
        return
    
    try:
        # Run examples
        print("Running examples...")
        
        await basic_audit_example()
        await custom_config_example()
        await detailed_analysis_example()
        await batch_processing_example()
        
        print("\n" + "=" * 80)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\nExample execution failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
