"""
Custom detection logic for elevations.
"""

import os
import fitz
import pandas as pd
from src.app_paths import getSourceRawDataPath

def plugin_detect_elevations_aff(project_source, output_file: str = "debug/elevations.xlsx", save_pdf: str = "debug/elevations.pdf"):
    """
    This one looks for elevations in raw data feather which are formatted as EL ... +- A.F.F

    Args:
        project_source (tuple): Project source tuple.
        output_file (str, optional): Output file path. Defaults to "debug/elevations.xlsx".
        save_pdf (str, optional): Save PDF file path for debug. Defaults to None.
    """
    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source
    feather = getSourceRawDataPath(projectId, filename)
    if not os.path.exists(feather):
        return "Project source needs to be preprocessed first."

    df = pd.read_feather(feather)

    df["elevation"] = df["value"].apply(lambda x: x if x.startswith("EL.") else None)

    all_coords = {}
    duplicate_coords = {}

    results = []
    groups = df.groupby("pdf_page")
    for pdf_page, group in groups:
        has_dupes = None
        duplicates = []
        elevations = []
        all_coords[pdf_page] = []
        duplicate_coords[pdf_page] = []
        for row in group[group["elevation"].notna()].itertuples():
            if row.value.strip() in elevations:
                has_dupes = True
                duplicates.append(row.value.strip())
                duplicate_coords[pdf_page].append(row.coordinates2)
                continue
            elevations.append(row.value.strip())
            all_coords[pdf_page].append(row.coordinates2)

        elevations_cleaned = []
        for elevation in elevations:
            if not elevation.startswith("EL. "):
                elevation = elevation.replace("EL.", "EL. ")
            elevation = elevation[:elevation.index("(")]
            elevations_cleaned.append(elevation.strip())

        results.append({
                        "sys_path": filename,
                        "pdf_page": pdf_page,
                        "elevation_uncleaned": ";".join(elevations),
                        "elevation": ";".join(elevations_cleaned),
                        "has_dupe": has_dupes,
                        "duplicates": ";".join(duplicates)})

    df = pd.DataFrame(results)
    df.to_excel(output_file, index=False)

    if save_pdf:
        doc = fitz.open(filename)
        for pdf_page, coords in all_coords.items():
            page = doc[pdf_page - 1]
            for coord in coords:
                x0, y0, x1, y1 = coord
                page.draw_rect(fitz.Rect(x0, y0, x1, y1), color=(0, 1, 0))

        for pdf_page, coords in duplicate_coords.items():
            page = doc[pdf_page - 1]
            for coord in coords:
                x0, y0, x1, y1 = coord
                page.draw_rect(fitz.Rect(x0, y0, x1, y1), color=(1, 0, 0))

        doc.save(save_pdf)

    return f"saved to {output_file}. saved pdf to {save_pdf}"

def plugin_detect_elevations_contains(project_source, output_file: str = "debug/elevations.xlsx", save_pdf: str = "debug/elevations.pdf"):
    """
    This one looks for elevations in raw data feather which are formatted as EL ... +- A.F.F

    Args:
        project_source (tuple): Project source tuple.
        output_file (str, optional): Output file path. Defaults to "debug/elevations.xlsx".
        save_pdf (str, optional): Save PDF file path for debug. Defaults to None.
    """
    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source
    feather = getSourceRawDataPath(projectId, filename)
    if not os.path.exists(feather):
        return "Project source needs to be preprocessed first."

    df = pd.read_feather(feather)

    df["elevation"] = df["value"].apply(lambda x: x if "EL." in x else None)

    all_coords = {}
    duplicate_coords = {}

    results = []
    groups = df.groupby("pdf_page")
    for pdf_page, group in groups:
        has_dupes = None
        duplicates = []
        elevations = []
        all_coords[pdf_page] = []
        duplicate_coords[pdf_page] = []
        for row in group[group["elevation"].notna()].itertuples():
            value = row.value[row.value.index("EL."):]
            value = value.strip()
            if value in elevations:
                has_dupes = True
                duplicates.append(value)
                duplicate_coords[pdf_page].append(row.coordinates2)
                continue
            elevations.append(value)
            all_coords[pdf_page].append(row.coordinates2)

        elevations_cleaned = elevations
        # elevations_cleaned = []
        # for elevation in elevations:
        #     if not elevation.startswith("EL. "):
        #         elevation = elevation.replace("EL.", "EL. ")
        #     elevation = elevation[:elevation.index("(")]
        #     elevations_cleaned.append(elevation.strip())

        results.append({"sys_path": filename,
                        "pdf_page": pdf_page,
                        "elevation_uncleaned": ";".join(elevations),
                        "elevation": ";".join(elevations_cleaned),
                        "has_dupe": has_dupes,
                        "duplicates": ";".join(duplicates)})

    df = pd.DataFrame(results)
    df.to_excel(output_file, index=False)

    if save_pdf:
        doc = fitz.open(filename)
        for pdf_page, coords in all_coords.items():
            page = doc[pdf_page - 1]
            for coord in coords:
                x0, y0, x1, y1 = coord
                page.draw_rect(fitz.Rect(x0, y0, x1, y1), color=(0, 1, 0))

        for pdf_page, coords in duplicate_coords.items():
            page = doc[pdf_page - 1]
            for coord in coords:
                x0, y0, x1, y1 = coord
                page.draw_rect(fitz.Rect(x0, y0, x1, y1), color=(1, 0, 0))

        doc.save(save_pdf)

    return f"saved to {output_file}. saved pdf to {save_pdf}"
