import sqlite3
from src.app_paths import getDatabasePath
import os
from src.utils.logger import logger

# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)

CREATE_RAW_DATA = '''
    CREATE TABLE IF NOT EXISTS RawData (
        pdf_id INTEGER NOT NULL,
        idx INTEGER NOT NULL, -- Index of RawData, unique to pdf_id
        sys_layout_valid INTEGER,  -- Boolean value: 0 (false), 1 (true)
        type TEXT,
        category TEXT,
        value TEXT,
        elevation TEXT,
        x_position TEXT,
        y_position TEXT,
        coordinates TEXT,
        coordinates2 TEXT,
        words TEXT,
        name TEXT,
        title TEXT,
        created_date TEXT,
        mod_date TEXT,
        id_annot_info TEXT,
        page_rotation TEXT,
        color TEXT,
        annot_type TEXT,
        annot_subtype TEXT,
        vertices TEXT,
        endpoint TEXT,
        stroke_color TEXT,
        font TEXT,
        font_color TEXT,
        font_style TEXT,
        font_size TEXT,
        flags TEXT,
        <PERSON>OREIGN KEY (pdf_id) REFERENCES PDFStorage(id),
        PRIMARY KEY (pdf_id, idx)
    )
'''

def update_database_schema(filename):
    """Ensure previous database schema are updated to the latest"""
    if not os.path.exists(filename):
        raise FileExistsError

    conn = sqlite3.connect(filename)
    cursor = conn.cursor()

    general_columns = [
        "calculated_eq_length",
        "calculated_area",
        "union_couplings",
        "expansion_joints",
    ]
    for column in general_columns:
        try:
            cursor.execute(f'ALTER TABLE General ADD COLUMN {column} NUMERIC')
        except sqlite3.OperationalError as e:
            logger.info(f'Could not add {column} column to General table')
            logger.debug(str(e))

    general_columns = [
        "pmi_req",
        "paut_req",
        "hardness_req",
        "flange_guard",
        "continued_on",
        "connects_to",
        "pickling_req",
        "flushing_req",
        "pipeline_num",
        "weight"
    ]
    for column in general_columns:
        try:
            cursor.execute(f'ALTER TABLE General ADD COLUMN {column} TEXT')
        except sqlite3.OperationalError as e:
            logger.info(f'Could not add {column} column to General table')
            logger.debug(str(e))

    bom_columns = [
        "material_code",
        "sch_class",
        "parts",
        "type",
        "weight",
        "number",
        "remarks",
        "ident_code"
    ]
    for column in bom_columns:
        try:
            cursor.execute(f'ALTER TABLE BOM ADD COLUMN {column} TEXT')
        except sqlite3.OperationalError as e:
            logger.info(f'Could not add {column} column to BOM table')
            logger.debug(str(e))

    # Modified for versions < 0.15.2
    try:
        cursor.execute('ALTER TABLE RFQ ADD Column last_updated TIMESTAMP')
    except sqlite3.OperationalError as e:
        logger.info('Could not add last_updated column to RFQ table')
        logger.debug(str(e))

    try:
        cursor.execute('ALTER TABLE RFQ ADD COLUMN componentCategory TEXT')
    except sqlite3.OperationalError as e:
        logger.info('Could not add last_updated column to RFQ table')
        logger.debug(str(e))

    # Switch page_number to pdf_page
    try:
        cursor.execute('ALTER TABLE PDFStorage RENAME COLUMN page_number TO pdf_page')
    except sqlite3.OperationalError as e:
        logger.info('Could not rename page_number to pdf_page for PDFStorage table')
        logger.debug(str(e))

    # Drop Raw Data table if it is the old by checking for layoutValid column name
    try:
        cursor.execute("SELECT COUNT(*) AS CNTREC FROM pragma_table_info('RawData') WHERE name='layoutValid'")
        column_exists = cursor.fetchone()[0]
        if column_exists:
            cursor.execute("DROP TABLE IF EXISTS RawData")
            logger.info("Dropped deprecated RawData table")
    except sqlite3.OperationalError as e:
        logger.info('Could not drop deprecated RawData table')
        logger.debug(str(e))

    cursor.execute(CREATE_RAW_DATA)

    conn.commit()
    conn.close()

def create_database(filename=getDatabasePath(), exists_ok: bool = False):
    print("Real database path:", os.path.realpath(filename))
    if not exists_ok:
        if os.path.exists(filename):
            update_database_schema(filename)
            # Database already exist, we do not overwite
            return
    reset_tables=True
    cleanse_db=True

    # Connect to SQLite database (or create it if it doesn't exist)
    conn = sqlite3.connect(filename)

    # Create a cursor object using the connection
    cursor = conn.cursor()
    # Drop tables
    if reset_tables:
        cursor.execute('DROP TABLE IF EXISTS PDFStorage')
        cursor.execute('DROP TABLE IF EXISTS General')
        cursor.execute('DROP TABLE IF EXISTS BOM')
        #cursor.execute('DROP TABLE IF EXISTS Users')
        #cursor.execute('DROP TABLE IF EXISTS Projects')
        #cursor.execute('DROP TABLE IF EXISTS ProjectSources')
        #cursor.execute('DROP TABLE IF EXISTS Rois')
        #cursor.execute('DROP TABLE IF EXISTS RoiColumn')

    # SQL statement for creating the Projects table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectName TEXT,
        projectNumber TEXT,  -- corresponds Project ID in form
        userCompanyName TEXT,
        clientName TEXT,
        projectLocation TEXT,
        projectScope TEXT
    )
    ''')

    # SQL statement for creating the PDFStorage table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS PDFStorage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER,
        originalFilename TEXT,
        pdf_page INTEGER,
        document BLOB,
        documentVendor TEXT,
        documentType TEXT,
        documentName TEXT,
        docSize TEXT,
        skipped INTEGER,           -- 0 (false), 1 (true)
        FOREIGN KEY (project_id) REFERENCES Projects(id)
    )
    ''')

    # SQL statement for creating the RawData table
    cursor.execute(CREATE_RAW_DATA)

    # SQL statement for creating the General table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS General (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pdf_id INTEGER,
            annotMarkups TEXT,
            area TEXT,
            avg_elevation TEXT,
            blockCoordinates TEXT,
            clientDocumentId TEXT,
            coordinates TEXT,
            designCode TEXT,
            documentDescription TEXT,
            documentId TEXT,
            documentTitle TEXT,
            drawing TEXT,
            elevation TEXT,
            flangeID TEXT,
            heatTrace TEXT,
            insulationSpec TEXT,
            insulationThickness TEXT,
            lineNumber TEXT,
            max_elevation TEXT,
            mediumCode TEXT,
            min_elevation TEXT,
            modDate TEXT,
            paintSpec TEXT,
            pid TEXT,
            pipeSpec TEXT,
            pipeStandard TEXT,
            processLineList TEXT,
            processUnit TEXT,
            projectNo TEXT,
            projectName TEXT,
            pwht TEXT,
            revision TEXT,
            sequence TEXT,
            service TEXT,
            sheet TEXT,
            size TEXT,
            sys_build TEXT,
            sys_layout_valid TEXT,
            sysDocument TEXT,
            sysDocumentName TEXT,
            sys_filename TEXT,
            sys_path TEXT,
            system TEXT,
            totalSheets TEXT,
            unit TEXT,
            vendorDocumentId TEXT,
            weldId TEXT,
            weldClass TEXT,
            xCoord TEXT,
            xray TEXT,
            yCoord TEXT,
            lf TEXT,
            sf TEXT,
            ef TEXT,
            elbows_90 TEXT,
            elbows_45 TEXT,
            bevels TEXT,
            tees TEXT,
            reducers TEXT,
            caps TEXT,
            flanges TEXT,
            valves_flanged TEXT,
            valves_welded TEXT,
            cut_outs TEXT,
            supports TEXT,
            bends TEXT,
            field_welds TEXT,
            FOREIGN KEY (pdf_id) REFERENCES PDFStorage(id)
        )
    ''')

    # SQL statement for creating the BOM table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS BOM (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pdf_id INTEGER,
        ef TEXT,
        sf TEXT,
        pos TEXT,
        material_description TEXT,
        size TEXT,
        ident TEXT,
        item TEXT,
        tag TEXT,
        quantity TEXT,
        status TEXT,
        nb TEXT,
        fluid TEXT,
        clean_spec TEXT,
        line_number,
        FOREIGN KEY (pdf_id) REFERENCES PDFStorage(id)
    )
    ''')

    # SQL statement for creating the ProjectSources table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS ProjectSources (
        projectId INTEGER,
        filename TEXT,
        documentVendor TEXT,
        dateAdded TIMESTAMP,
        sortNumber TEXT,
        FOREIGN KEY (projectId) REFERENCES Projects(id),
        PRIMARY KEY (projectId, filename)
    )
    ''')

    # SQL statement for creating the ROIColumn table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Users (
        email TEXT PRIMARY KEY
    )
    ''')

    # SQL statement for creating the SPEC table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS SPEC (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pdf_id INTEGER,
        dp1 TEXT,
        dp2 TEXT,
        dt1 TEXT,
        dt2 TEXT,
        op1 TEXT,
        op2 TEXT,
        opt1 TEXT,
        opt2 TEXT,
        pipeSpec TEXT,
        size TEXT,
        FOREIGN KEY (pdf_id) REFERENCES PDFStorage(id)
    )
    ''')

    # SQL statement for creating the SPOOL table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS SPOOL (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pdf_id INTEGER,
        cutPiece TEXT,
        length TEXT,
        spool TEXT,
        spec TEXT,
        size TEXT,
        FOREIGN KEY (pdf_id) REFERENCES PDFStorage(id)
    )
    ''')

    # SQL statement for creating the RFQ table
    # note ansme/ansi to ansme_ansi
    cursor.execute('''CREATE TABLE IF NOT EXISTS RFQ (
        material_description TEXT,
        size TEXT,
        ef TEXT,
        sf TEXT,
        general_category TEXT,
        rfq_scope TEXT,
        unit_of_measure TEXT,
        size1 TEXT,
        size2 TEXT,
        schedule TEXT,
        rating TEXT,
        astm TEXT,
        grade TEXT,
        ansme_ansi TEXT,
        material TEXT,
        abbreviated_material TEXT,
        coating TEXT,
        forging TEXT,
        ends TEXT,
        item_tag TEXT,
        tie_point TEXT,
        pipe_category TEXT,
        valve_type TEXT,
        fitting_category TEXT,
        weld_category TEXT,
        bolt_category TEXT,
        gasket_category TEXT,
        answer_explanation TEXT,
        review TEXT,
        review_explanation TEXT,
        last_updated TIMESTAMP,
        componentCategory TEXT,
        PRIMARY KEY (material_description, size)
    )
    ''')

    # Commit the changes to the database
    conn.commit()

    if cleanse_db:
        # Clean up the database and reclaim space by defragmenting
        print("Applying VACUUM to Database")
        cursor.execute('VACUUM')

    # Close the connection to the database
    conn.close()
    print("Connection Closed...")


# Call the function to create the database and tables
if __name__ == "__main__":
    create_database()

