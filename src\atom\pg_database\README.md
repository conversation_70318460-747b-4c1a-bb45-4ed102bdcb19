# PostgreSQL Database Implementation for ATEM

This directory contains the PostgreSQL database implementation for the ATEM (Agile Takeoff & Estimation Model) application, designed to replace the current pandas DataFrame-based data processing with a more robust database solution.

## Overview

The database implementation aims to:
- Provide real-time data updates across different views
- Standardize data formats (especially size formatting)
- Improve performance for large datasets
- Eliminate redundant code and mappings

## Database Connection

The PostgreSQL database is hosted on Neon. Use the following connection string (with proper credentials):

```
postgresql://c.mccall%40architekt-is.com:<EMAIL>/architekt-atem?sslmode=require
```

Or environment variables:

```
PGHOST='ep-silent-dew-a5ukl5n3-pooler.us-east-2.aws.neon.tech'
PGDATABASE='architekt-atem'
PGUSER='c.mccall%40architekt-is.com'
PGPASSWORD='PASSWORD'
```

## Key Files to Replicate

To successfully implement the PostgreSQL version, the following key Python files need to be analyzed and their functionality replicated in SQL:

### 1. `value_mappings.py`
This file contains all the mapping dictionaries used throughout the application:
- `category_to_unit_map`: Maps general categories to units of measure
- `scope_to_category_map`: Maps rfq_scope values to general_category
- `fitting_to_category_map`: Maps specific fitting types to general categories
- `material_to_abbreviation_map`: Maps materials to their abbreviations

These mappings should be converted to PostgreSQL lookup tables.

### 2. `data_conversions.py`
This file handles size formatting and conversions, particularly:
- `process_size_column()`: Splits combined size values into size1 and size2
- `convert_quantity_to_float()`: Converts various string formats to numerical values
- Size formatting functions to standardize values

These functions should be converted to PostgreSQL functions.

### 3. `src/atom/tables/rfqtableview.py`
This file contains the UI components and data processing logic:
- `BuildRfqWorker`: Processes RFQ data, applies size conversions
- `ApplyRfqWorker`: Handles the merging of RFQ data into BOM

### 4. `src/atom/merge_rfq_into_bom.py`
This file contains the core merging logic:
- `_group_bom_data()`: Groups BOM data by key fields
- `_handle_special_categories()`: Manages special cases like 'LF'
- `_update_general_data_quantities()`: Core logic for summing values into general table
- `merge_rfq_into_bom()`: Main merging function

## Implementation Steps

### 1. Test Update from public.rfq_input to rfq

Execute the following function to test transferring data from the public.rfq_input table to the rfq table:

```sql
-- Function to transfer data from public.rfq_input to rfq
CREATE OR REPLACE FUNCTION import_rfq_input_data() RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER;
BEGIN
    INSERT INTO rfq (
        project_id, material_description, size1, size2, material, 
        rating, ends, rfq_scope, fitting_category, valve_type, 
        quantity, page
    )
    SELECT 
        project_id, material_description, size1, size2, material, 
        rating, ends, rfq_scope, fitting_category, valve_type, 
        quantity, page
    FROM public.rfq_input
    ON CONFLICT (project_id, material_description, size1, size2, page) 
    DO UPDATE SET
        material = EXCLUDED.material,
        rating = EXCLUDED.rating,
        ends = EXCLUDED.ends,
        rfq_scope = EXCLUDED.rfq_scope,
        fitting_category = EXCLUDED.fitting_category,
        valve_type = EXCLUDED.valve_type,
        quantity = EXCLUDED.quantity;
        
    GET DIAGNOSTICS v_count = ROW_COUNT;
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT import_rfq_input_data();
```

### 2. Test Update from rfq to rfq_input

To test updating public.rfq_input from rfq (for backward compatibility):

```sql
-- Function to update public.rfq_input from rfq
CREATE OR REPLACE FUNCTION export_rfq_data() RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER;
BEGIN
    INSERT INTO public.rfq_input (
        project_id, material_description, size1, size2, material, 
        rating, ends, rfq_scope, fitting_category, valve_type, 
        quantity, page
    )
    SELECT 
        project_id, material_description, size1, size2, material, 
        rating, ends, rfq_scope, fitting_category, valve_type, 
        quantity, page
    FROM rfq
    ON CONFLICT (id) 
    DO UPDATE SET
        material_description = EXCLUDED.material_description,
        size1 = EXCLUDED.size1,
        size2 = EXCLUDED.size2,
        material = EXCLUDED.material,
        rating = EXCLUDED.rating,
        ends = EXCLUDED.ends,
        rfq_scope = EXCLUDED.rfq_scope,
        fitting_category = EXCLUDED.fitting_category,
        valve_type = EXCLUDED.valve_type,
        quantity = EXCLUDED.quantity,
        page = EXCLUDED.page;
        
    GET DIAGNOSTICS v_count = ROW_COUNT;
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT export_rfq_data();
```

### 3. Size Formatting Functions

Create PostgreSQL functions to handle size formatting:

```sql
-- Function to process size column (similar to process_size_column in data_conversions.py)
CREATE OR REPLACE FUNCTION process_size(size_value TEXT) 
RETURNS TABLE(size1 TEXT, size2 TEXT) AS $$
BEGIN
    -- Handle null/empty values
    IF size_value IS NULL OR size_value = '' OR size_value = 'nan' THEN
        RETURN QUERY SELECT NULL::TEXT, NULL::TEXT;
        RETURN;
    END IF;
    
    -- Check if it contains an 'x' (indicating two sizes)
    IF position('x' IN lower(size_value)) > 0 THEN
        -- Split on 'x' and return both parts
        RETURN QUERY 
        SELECT 
            trim(split_part(size_value, 'x', 1)),
            trim(split_part(size_value, 'x', 2));
    ELSE
        -- Just one size, return it as size1
        RETURN QUERY SELECT trim(size_value), NULL::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to format size values consistently
CREATE OR REPLACE FUNCTION format_size_value(size_value TEXT) 
RETURNS TEXT AS $$
DECLARE
    numeric_value NUMERIC;
BEGIN
    -- Handle null/empty values
    IF size_value IS NULL OR size_value = '' OR size_value = 'nan' THEN
        RETURN NULL;
    END IF;
    
    -- Try to convert to numeric
    BEGIN
        numeric_value := size_value::NUMERIC;
        -- Format with 3 decimal places
        RETURN to_char(numeric_value, 'FM999990.000');
    EXCEPTION WHEN OTHERS THEN
        -- If conversion fails, return the original value
        RETURN size_value;
    END;
END;
$$ LANGUAGE plpgsql;
```

### 4. Classification Function with Mapping Logic

```sql
-- Create a function to classify RFQ items using the mappings from value_mappings.py
CREATE OR REPLACE FUNCTION classify_rfq_item(
    p_rfq_scope TEXT,
    p_fitting_category TEXT,
    p_valve_type TEXT,
    p_ends TEXT
) RETURNS TABLE(general_category TEXT, unit_of_measure TEXT) AS $$
BEGIN
    -- If rfq_scope has a direct mapping
    IF EXISTS (SELECT 1 FROM scope_category_map WHERE rfq_scope = p_rfq_scope) THEN
        RETURN QUERY
        SELECT scm.general_category, cum.unit_of_measure
        FROM scope_category_map scm
        LEFT JOIN category_unit_map cum ON cum.general_category = scm.general_category
        WHERE scm.rfq_scope = p_rfq_scope;
        
    -- If it's a fitting
    ELSIF p_rfq_scope = 'Fittings' AND p_fitting_category IS NOT NULL THEN
        RETURN QUERY
        SELECT fcm.general_category, cum.unit_of_measure
        FROM fitting_category_map fcm
        LEFT JOIN category_unit_map cum ON cum.general_category = fcm.general_category
        WHERE fcm.fitting_category = p_fitting_category;
        
    -- If it's a valve
    ELSIF p_rfq_scope = 'Valves' AND p_valve_type IS NOT NULL THEN
        -- Call a separate function for valve classification logic
        RETURN QUERY
        SELECT 
            CASE 
                WHEN classify_valve(p_valve_type, p_ends) = 'Flanged Valve' THEN 'Flanged Valve'
                WHEN classify_valve(p_valve_type, p_ends) = 'Welded Valve' THEN 'Welded Valve'
                ELSE ''
            END,
            CASE 
                WHEN classify_valve(p_valve_type, p_ends) IN ('Flanged Valve', 'Welded Valve') THEN 'EA'
                ELSE ''
            END;
    ELSE
        RETURN QUERY SELECT ''::TEXT, ''::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;
```

### 5. Implementing Size Matching and Aggregation Logic

The key challenge is to ensure that each page has one unique size per applicable type in the General Category, with quantities correctly summed. Use this function to implement the logic:

```sql
CREATE OR REPLACE FUNCTION aggregate_to_general(p_project_id INTEGER) RETURNS VOID AS $$
DECLARE
    rec RECORD;
BEGIN
    -- Clear existing general data for this project
    DELETE FROM general WHERE project_id = p_project_id;
    
    -- First, process all pipe sizes (these will serve as the base sizes)
    INSERT INTO general (project_id, page, size, pipe)
    SELECT 
        project_id,
        page,
        format_size_value(size1) AS size,
        SUM(quantity) AS pipe
    FROM rfq
    WHERE project_id = p_project_id
    AND general_category = 'LF'
    GROUP BY project_id, page, size1;
    
    -- Now process other categories and try to match to existing sizes
    FOR rec IN (
        SELECT 
            r.id,
            r.project_id,
            r.page,
            r.general_category,
            format_size_value(r.size1) AS size1,
            format_size_value(r.size2) AS size2,
            r.quantity
        FROM rfq r
        WHERE r.project_id = p_project_id
        AND r.general_category != 'LF'
        AND r.general_category IS NOT NULL
        AND r.general_category != ''
    ) LOOP
        -- Try to match on size1 first
        IF EXISTS (
            SELECT 1 FROM general 
            WHERE project_id = rec.project_id 
            AND page = rec.page 
            AND size = rec.size1
        ) THEN
            -- Update the matching size row
            EXECUTE format(
                'UPDATE general SET %I = COALESCE(%I, 0) + $1 WHERE project_id = $2 AND page = $3 AND size = $4',
                rec.general_category, rec.general_category
            ) USING rec.quantity, rec.project_id, rec.page, rec.size1;
        
        -- Then try size2 if size1 didn't match
        ELSIF rec.size2 IS NOT NULL AND rec.size2 != '' AND EXISTS (
            SELECT 1 FROM general 
            WHERE project_id = rec.project_id 
            AND page = rec.page 
            AND size = rec.size2
        ) THEN
            -- Update the matching size row
            EXECUTE format(
                'UPDATE general SET %I = COALESCE(%I, 0) + $1 WHERE project_id = $2 AND page = $3 AND size = $4',
                rec.general_category, rec.general_category
            ) USING rec.quantity, rec.project_id, rec.page, rec.size2;
            
        -- If no match found for either size, create a new row with size1
        ELSE
            -- Check if we have an existing page record to copy
            IF EXISTS (
                SELECT 1 FROM general
                WHERE project_id = rec.project_id
                AND page = rec.page
                LIMIT 1
            ) THEN
                -- Insert a new row with size1 and quantity
                INSERT INTO general (project_id, page, size)
                VALUES (rec.project_id, rec.page, rec.size1);
                
                -- Now update with the quantity
                EXECUTE format(
                    'UPDATE general SET %I = $1 WHERE project_id = $2 AND page = $3 AND size = $4',
                    rec.general_category
                ) USING rec.quantity, rec.project_id, rec.page, rec.size1;
            ELSE
                -- No existing page record, create one with all necessary columns
                EXECUTE format(
                    'INSERT INTO general (project_id, page, size, %I) VALUES ($1, $2, $3, $4)',
                    rec.general_category
                ) USING rec.project_id, rec.page, rec.size1, rec.quantity;
            END IF;
        END IF;
    END LOOP;
    
    -- Handle special case: ensure each page has all pipe sizes
    -- This replicates the logic in _handle_special_categories
    PERFORM ensure_all_pipe_sizes(p_project_id);
END;
$$ LANGUAGE plpgsql;

-- Function to ensure all pipe sizes exist on all pages
CREATE OR REPLACE FUNCTION ensure_all_pipe_sizes(p_project_id INTEGER) RETURNS VOID AS $$
DECLARE
    pipe_sizes CURSOR FOR 
        SELECT DISTINCT size 
        FROM general 
        WHERE project_id = p_project_id 
        AND pipe > 0;
    pages CURSOR FOR 
        SELECT DISTINCT page 
        FROM general 
        WHERE project_id = p_project_id;
    v_size TEXT;
    v_page TEXT;
BEGIN
    -- For each page, ensure all pipe sizes are represented
    FOR v_page IN pages LOOP
        FOR v_size IN pipe_sizes LOOP
            -- Check if this size exists for this page
            IF NOT EXISTS (
                SELECT 1 FROM general 
                WHERE project_id = p_project_id 
                AND page = v_page 
                AND size = v_size
            ) THEN
                -- Insert a placeholder row
                INSERT INTO general (
                    project_id, page, size
                ) VALUES (
                    p_project_id, v_page, v_size
                );
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 6. Set Up Real-time Updates with Triggers

Create triggers to automatically update related tables when an RFQ item is updated:

```sql
-- Trigger to update rfq classification when fields change
CREATE OR REPLACE FUNCTION rfq_classification_trigger() RETURNS TRIGGER AS $$
DECLARE
    v_result RECORD;
BEGIN
    -- If classification-related fields are updated
    IF NEW.rfq_scope IS DISTINCT FROM OLD.rfq_scope OR
       NEW.fitting_category IS DISTINCT FROM OLD.fitting_category OR
       NEW.valve_type IS DISTINCT FROM OLD.valve_type OR
       NEW.ends IS DISTINCT FROM OLD.ends THEN
        
        -- Get the classification
        SELECT * INTO v_result FROM classify_rfq_item(
            NEW.rfq_scope, 
            NEW.fitting_category,
            NEW.valve_type,
            NEW.ends
        );
        
        -- Update the general category and unit of measure
        NEW.general_category = v_result.general_category;
        NEW.unit_of_measure = v_result.unit_of_measure;
        NEW.is_classified = TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER rfq_before_update
BEFORE UPDATE ON rfq
FOR EACH ROW
EXECUTE FUNCTION rfq_classification_trigger();

-- Trigger to update general table when rfq is updated
CREATE OR REPLACE FUNCTION rfq_after_update_trigger() RETURNS TRIGGER AS $$
BEGIN
    -- If relevant fields have changed
    IF OLD.general_category IS DISTINCT FROM NEW.general_category OR
       OLD.quantity IS DISTINCT FROM NEW.quantity OR
       OLD.size1 IS DISTINCT FROM NEW.size1 OR
       OLD.size2 IS DISTINCT FROM NEW.size2 THEN
        -- Update the general table for this project
        PERFORM aggregate_to_general(NEW.project_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER rfq_after_update
AFTER UPDATE ON rfq
FOR EACH ROW
EXECUTE FUNCTION rfq_after_update_trigger();

-- Trigger to update bom when rfq is updated
CREATE OR REPLACE FUNCTION rfq_to_bom_trigger() RETURNS TRIGGER AS $$
BEGIN
    -- Update matching BOM entries
    UPDATE bom
    SET material = NEW.material,
        rating = NEW.rating,
        ends = NEW.ends,
        general_category = NEW.general_category,
        last_updated = CURRENT_TIMESTAMP
    WHERE project_id = NEW.project_id
    AND material_description = NEW.material_description
    AND COALESCE(size1, '') = COALESCE(NEW.size1, '')
    AND COALESCE(size2, '') = COALESCE(NEW.size2, '')
    AND page = NEW.page;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER rfq_to_bom_after_update
AFTER UPDATE ON rfq
FOR EACH ROW
EXECUTE FUNCTION rfq_to_bom_trigger();
```

## Testing the Implementation

1. First, verify the data in the existing tables:
```sql
SELECT * FROM rfq LIMIT 10;
SELECT * FROM public.rfq_input LIMIT 10;
```

2. Run the import function to load data:
```sql
SELECT import_rfq_input_data();
```

3. Classify some items:
```sql
-- Update an item
UPDATE rfq 
SET rfq_scope = 'Fittings', fitting_category = 'Elbow' 
WHERE id = 1;

-- Verify the trigger worked
SELECT * FROM general WHERE project_id = (SELECT project_id FROM rfq WHERE id = 1);
```

4. Test the bidirectional sync:
```sql
-- Update in rfq_input
UPDATE public.rfq_input SET quantity = 25 WHERE id = 2;
SELECT import_rfq_input_data();

-- Update in rfq
UPDATE rfq SET quantity = 30 WHERE id = 3;
SELECT export_rfq_data();
```

## Next Steps

1. Optimize with indexes on frequently queried columns
2. Implement error handling
3. Integrate with Python application
4. Add transaction handling for data consistency

## Troubleshooting

If you encounter issues:

1. Check PostgreSQL logs
2. Test queries individually
3. Use EXPLAIN ANALYZE to debug slow queries:
```sql
EXPLAIN ANALYZE SELECT * FROM rfq WHERE project_id = 1;
