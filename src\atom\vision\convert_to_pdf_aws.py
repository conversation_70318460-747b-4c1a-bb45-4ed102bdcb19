import os
import boto3
import json
import time
import fitz
import pandas as pd
from rsa import verify
import math

# from aws.process import make_pdf_doc_searchable

'''
Main Entry Point

Possibly reduce cost with lambda trigger function: https://docs.aws.amazon.com/textract/latest/dg/lambda.html
'''

S3_BUCKET = "testatem"
AWS_REGION = "us-east-2"

folder_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Modified"


def upload_to_s3(file_path: str) -> str:
    """Upload file to S3 and return the S3 object name"""
    s3_client = boto3.client('s3')
    object_name = os.path.basename(file_path)  # Use filename as S3 object name

    try:
        s3_client.upload_file(file_path, S3_BUCKET, object_name)
        print(f"Successfully uploaded {file_path} to {S3_BUCKET}/{object_name}")
        return object_name
    except Exception as e:
        print(f"Error uploading to S3: {e}")
        raise


def convert_single_page(page_bytes: bytes) -> dict:
    """Performs textract on single page of PDF. Outputs textract results to response.json"""
    start_time = time.time()

    # s3_object_name = upload_to_s3(pdf_path)

    # textract_client = boto3.client("textract")
    textract_client = boto3.client("textract") #, region_name="us-east-2")

    read_time = time.time()
    print(f"\nREAD TIME: {read_time - start_time}")

    try:
        response = textract_client.analyze_document(
            Document={"Bytes": page_bytes},
            FeatureTypes=['TABLES']
        )
    except Exception as e:
        print()
        print("BOTO error...", e)
        print()
        response = {}

    response_time = time.time()

    print(f"\nRESPONSE TIME: {response_time - start_time}")

    # json.dump(response, open(os.path.join(folder_dir, "output", "response.json"), "w"))

    end_time = time.time()

    print(f"\nCOMPLETE TIME: {end_time - start_time}")
    return response


def convert_multiple_pages_2(pdf_path, existing_file=False, existing_filename="Test"):
    """Original textract function - modified to handle temp files"""
    if not existing_file:
        # Upload PDF to S3 first
        s3_object_name = upload_to_s3(pdf_path)
    else:
        s3_object_name=existing_filename

    textract_client = boto3.client("textract", region_name=AWS_REGION)

    # Start the async job
    response = textract_client.start_document_analysis(
        DocumentLocation={
            "S3Object": {
                "Bucket": S3_BUCKET,
                "Name": s3_object_name
            }
        },
        FeatureTypes=['TABLES']
    )

    job_id = response['JobId']
    print(f"Started job with ID: {job_id}")

    while True:
        response = textract_client.get_document_analysis(JobId=job_id)
        status = response['JobStatus']
        print(f"Job status: {status}", flush=True)

        if status in ['SUCCEEDED', 'FAILED']:
            break
        time.sleep(5)

    if status == 'SUCCEEDED':
        pages = []
        next_token = None

        while True:
            if next_token:
                response = textract_client.get_document_analysis(
                    JobId=job_id,
                    NextToken=next_token
                )
            else:
                response = textract_client.get_document_analysis(JobId=job_id)

            pages.append(response)

            if 'NextToken' in response:
                next_token = response['NextToken']
                continue
            break

        combined_response = {
            'Blocks': [],
            'DocumentMetadata': response['DocumentMetadata']
        }

        for page in pages:
            combined_response['Blocks'].extend(page['Blocks'])

        return combined_response
    else:
        print(f"Job failed: {response['StatusMessage'] if 'StatusMessage' in response else 'Unknown error'}")
        return None

def convert_multiple_pages(pdf_path, existing_file=False, existing_filename="Test"):
    """Perform textract on document and return combined page results"""
    if not existing_file:
        # Upload PDF to S3 first
        s3_object_name = upload_to_s3(pdf_path)
    else:
        s3_object_name=existing_filename

    textract_client = boto3.client("textract", region_name=AWS_REGION)

    # Start the async job
    response = textract_client.start_document_analysis(
        DocumentLocation={
            "S3Object": {
                "Bucket": S3_BUCKET,
                "Name": s3_object_name
            }
        },
        FeatureTypes=['TABLES']
    )

    job_id = response['JobId']
    print(f"Started job with ID: {job_id}")

    while True:
        response = textract_client.get_document_analysis(JobId=job_id)
        status = response['JobStatus']
        print(f"Job status: {status}", flush=True)

        if status in ['SUCCEEDED', 'FAILED']:
            break
        time.sleep(5)

    if status == 'SUCCEEDED':
        pages = []
        next_token = None

        while True:
            if next_token:
                response = textract_client.get_document_analysis(
                    JobId=job_id,
                    NextToken=next_token
                )
            else:
                response = textract_client.get_document_analysis(JobId=job_id)

            pages.append(response)

            if 'NextToken' in response:
                next_token = response['NextToken']
                continue
            break

        combined_response = {
            'Blocks': [],
            'DocumentMetadata': response['DocumentMetadata']
        }

        for page in pages:
            combined_response['Blocks'].extend(page['Blocks'])

        return combined_response
    else:
        raise Exception(response['StatusMessage'] if 'StatusMessage' in response else 'Unknown error')


def test_s3_connection():
    """Test S3 connection and upload functionality"""
    try:
        # Create a simple test file
        test_file = "debug/s3_test.txt"
        os.makedirs(os.path.dirname(test_file), exist_ok=True)

        with open(test_file, 'w') as f:
            f.write("Test content")

        print("Created test file")

        # Test S3 client creation
        s3_client = boto3.client('s3')
        print(f"Successfully created S3 client for region: {AWS_REGION}")

        # Test bucket listing
        try:
            s3_client.head_bucket(Bucket=S3_BUCKET)
            print(f"Successfully connected to bucket: {S3_BUCKET}")
        except Exception as e:
            print(f"Error accessing bucket {S3_BUCKET}: {str(e)}")
            return

        # Test upload
        object_name = upload_to_s3(test_file)
        print(f"Test upload successful: {object_name}")

        # Clean up
        os.remove(test_file)
        print("Test completed successfully")

    except Exception as e:
        print(f"Test failed: {str(e)}")

# Add the orientation detection function
def calculate_text_orientation(block):
    """
    Calculate the orientation of a text block based on its polygon coordinates.
    
    Args:
        block: A Textract block with Geometry.Polygon
        
    Returns:
        Dictionary with orientation details
    """
    if "Geometry" not in block or "Polygon" not in block["Geometry"]:
        return {"angle_degrees": 0, "orientation": "unknown", "direction": "unknown"}
    
    # Extract polygon points
    polygon = block["Geometry"]["Polygon"]
    if len(polygon) < 4:
        return {"angle_degrees": 0, "orientation": "unknown", "direction": "unknown"}
    
    # Get the first two points (typically top-left and top-right for horizontal text)
    p1 = (polygon[0]["X"], polygon[0]["Y"])
    p2 = (polygon[1]["X"], polygon[1]["Y"])
    p3 = (polygon[2]["X"], polygon[2]["Y"])
    
    # Calculate the angle between the first two points
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    
    # Calculate primary angle (for determining if horizontal or vertical)
    angle_radians = math.atan2(dy, dx)
    angle_degrees = math.degrees(angle_radians)
    
    # Normalize angle to 0-360
    if angle_degrees < 0:
        angle_degrees += 360
    
    # Calculate width and height of the text block
    width = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
    height = math.sqrt((p3[0] - p2[0])**2 + (p3[1] - p2[1])**2)
    
    # Determine orientation based on angle and dimensions
    if -45 <= angle_degrees <= 45 or angle_degrees >= 315:
        # Horizontal text
        orientation = "horizontal"
        angle_rounded = 0
        direction = "left_to_right"
    elif 45 < angle_degrees < 135:
        # Vertical text (rotated 90 degrees clockwise)
        orientation = "vertical"
        angle_rounded = 90
        direction = "top_to_bottom"
    elif 135 <= angle_degrees <= 225:
        # Upside down text
        orientation = "upside_down"
        angle_rounded = 180
        direction = "right_to_left"
    else:  # 225 < angle_degrees < 315
        # Vertical text (rotated 270 degrees clockwise)
        orientation = "vertical_reversed"
        angle_rounded = 270
        direction = "bottom_to_top"
    
    # Additional refinement based on width/height ratio
    if orientation in ["horizontal", "upside_down"] and width < height * 0.8:
        # This is likely vertical text despite the angle
        if orientation == "horizontal":
            orientation = "vertical"
            angle_rounded = 90
            direction = "top_to_bottom"
        else:
            orientation = "vertical_reversed"
            angle_rounded = 270
            direction = "bottom_to_top"
    elif orientation in ["vertical", "vertical_reversed"] and width * 0.8 > height:
        # This is likely horizontal text despite the angle
        if orientation == "vertical":
            orientation = "horizontal"
            angle_rounded = 0
            direction = "left_to_right"
        else:
            orientation = "upside_down"
            angle_rounded = 180
            direction = "right_to_left"
    
    return {
        "angle_degrees": angle_rounded,
        "orientation": orientation,
        "direction": direction
    }

if __name__ == "__main__":

    # Run the test
    # test_s3_connection()

    # Test convert_multiple_pages with the specified PDF
    pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Bluefinn\test_m_2.pdf"
    
    print(f"Started with file: {pdf_path}")
    
    # Convert all pages using AWS Textract
    print("Processing PDF with AWS Textract...")
    res = convert_multiple_pages(pdf_path, existing_file=False)
    
    textract_blocks = res["Blocks"]
    print(f"Number of blocks: {len(textract_blocks)}")
    
    # Convert to DataFrame for analysis
    ocr_data = []
    for block in textract_blocks:
        if "Text" in block and "Geometry" in block:
            # Get orientation information# Get orientation information
            orientation_info = calculate_text_orientation(block)
           
            bbox = block["Geometry"]["BoundingBox"]
            confidence = block["Confidence"]
            text = block["Text"]
            page = block.get("Page", 0)
            text_type = block.get("TextType", "")  # Get TextType if available
            block_type = block.get("BlockType", "")  # Get BlockType

            ocr_data.append({
                "page": page,
                "text": text,
                "x0": bbox["Left"],
                "y0": bbox["Top"],
                "x1": bbox["Left"] + bbox["Width"],
                "y1": bbox["Top"] + bbox["Height"],
                "confidence": confidence,
                "text_type": text_type,
                "block_type": block_type,
                # Add orientation fields
                "angle_degrees": orientation_info["angle_degrees"],
                "orientation": orientation_info["orientation"],
                "direction": orientation_info["direction"]
            })
    
    # Convert to DataFrame if needed
    df = pd.DataFrame(ocr_data)
    print(f"Collected {len(ocr_data)} text elements")
    print(df.head())  # Show first few entries
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(pdf_path), "output")
    os.makedirs(output_dir, exist_ok=True)
    
    # Export to Excel
    excel_outfile = os.path.join(output_dir, "bluefinn_test_m_aws_output.xlsx")
    print(f"Exporting to Excel: {excel_outfile}")
    df.to_excel(excel_outfile)
    
    # Save JSON response for reference
    json_outfile = os.path.join(output_dir, "bluefinn_test_m_aws_response.json")
    print(f"Saving raw response to: {json_outfile}")
    with open(json_outfile, 'w') as f:
        json.dump(res, f, indent=2)
    
    print("Done")