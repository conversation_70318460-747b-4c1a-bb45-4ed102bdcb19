import os
import pandas as pd
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

from src.atom.dbManager import DatabaseManager
from src.app_paths import getSourceRawDataPath
from src.utils.pdf.autorotation import detect_orientation_parallel, apply_rotations_to_pdf_pypdf, create_preview_grid

from src.utils.pdf.autorotation_ui import AutorotationTool
from src.utils.pdf.detectgroups_ui import DetectGroupsTool


class SourcePreparationUtility(QWidget):
    """
    A utility for managing project sources.
    """
    def __init__(self, parent=None):
        super().__init__(parent)

        self.db = DatabaseManager()
        self.currentProjectId = None

        self.setObjectName("popup")
        self.setWindowTitle("Project Source Utility")
        self.setLayout(QVBoxLayout())
        self.layout().setContentsMargins(10, 10, 10, 10)
        self.layout().setSpacing(10)
        self.setMinimumWidth(1024)
        self.setMinimumHeight(768)

        # Create the UI components
        self.setupTabWidget()
        self.setupStatusBar()

    def setupTabWidget(self):
        """Set up the tabbed interface."""
        self.tabWidget = QTabWidget()

        # Create tabs
        self.orientationTab = AutorotationTool()
        self.detectGroupsTab = DetectGroupsTool()

        # Add tabs to tab widget
        self.tabWidget.addTab(self.orientationTab, "Orientation")
        self.tabWidget.addTab(self.detectGroupsTab, "Detect Groups")

        # Add tab widget to main layout
        self.layout().addWidget(self.tabWidget)

    def setupOrientationTab(self):
        """Set up the Orientation tab."""
        layout = QVBoxLayout(self.orientationTab)

        # Add source selection group
        sourceGroup = QGroupBox("PDF Source")
        sourceLayout = QFormLayout(sourceGroup)

        # Create file path input and browse button
        fileLayout = QHBoxLayout()
        self.orientationFilePathEdit = QLineEdit()
        self.orientationFilePathEdit.setReadOnly(True)  # Ensure the file path is read-only
        self.orientationFilePathEdit.setPlaceholderText("Select a PDF file...")

        self.orientationBrowseButton = QPushButton("Browse...")
        self.orientationBrowseButton.clicked.connect(self.onBrowseOrientationFile)

        fileLayout.addWidget(self.orientationFilePathEdit)
        fileLayout.addWidget(self.orientationBrowseButton)

        sourceLayout.addRow("File:", fileLayout)

        # Create detect orientation button
        self.detectOrientationButton = QPushButton("Detect Orientation")
        self.detectOrientationButton.clicked.connect(self.onDetectOrientation)
        self.detectOrientationButton.setEnabled(False)

        # Connect file path changes to enable/disable detect button
        self.orientationFilePathEdit.textChanged.connect(
            lambda text: self.detectOrientationButton.setEnabled(bool(text))
        )

        # Create a splitter for the table and preview
        self.orientationSplitter = QSplitter(Qt.Horizontal)

        # Left side: Results table
        resultsGroup = QGroupBox("Orientation Results")
        resultsLayout = QVBoxLayout(resultsGroup)

        self.orientationResultsTable = QTableView()
        self.orientationResultsTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orientationResultsTable.setAlternatingRowColors(True)
        self.orientationResultsTable.setSelectionMode(QAbstractItemView.SingleSelection)

        # Set up the model
        self.orientationResultsModel = OrientationResultsModel()
        self.orientationResultsTable.setModel(self.orientationResultsModel)

        # Set up the delegate for rotation editing
        self.rotationDelegate = RotationDelegate()
        self.orientationResultsTable.setItemDelegateForColumn(1, self.rotationDelegate)

        # Connect selection change to update preview
        self.orientationResultsTable.selectionModel().selectionChanged.connect(self.updateOrientationPreview)

        # Connect model data change to update preview
        self.orientationResultsModel.dataChanged.connect(self.onOrientationDataChanged)

        # Add export/import buttons
        exportImportLayout = QHBoxLayout()

        self.exportOrientationButton = QPushButton("Export Table")
        self.exportOrientationButton.setIcon(QIcon.fromTheme("document-save"))
        self.exportOrientationButton.clicked.connect(self.onExportOrientationTable)
        self.exportOrientationButton.setEnabled(False)

        self.importOrientationButton = QPushButton("Import Table")
        self.importOrientationButton.setIcon(QIcon.fromTheme("document-open"))
        self.importOrientationButton.clicked.connect(self.onImportOrientationTable)
        self.importOrientationButton.setEnabled(False)

        exportImportLayout.addWidget(self.exportOrientationButton)
        exportImportLayout.addWidget(self.importOrientationButton)

        # Add table and export/import buttons to results layout
        resultsLayout.addWidget(self.orientationResultsTable)
        resultsLayout.addLayout(exportImportLayout)

        # Right side: Preview pane
        previewGroup = QGroupBox("Page Preview")
        previewLayout = QVBoxLayout(previewGroup)

        # Create labels for before and after rotation
        self.beforeRotationLabel = QLabel("Before Rotation")
        self.beforeRotationLabel.setAlignment(Qt.AlignCenter)
        self.beforeRotationLabel.setStyleSheet("font-weight: bold;")

        self.afterRotationLabel = QLabel("After Rotation")
        self.afterRotationLabel.setAlignment(Qt.AlignCenter)
        self.afterRotationLabel.setStyleSheet("font-weight: bold;")

        # Create image labels for the previews
        self.beforePreviewLabel = QLabel()
        self.beforePreviewLabel.setAlignment(Qt.AlignCenter)
        self.beforePreviewLabel.setMinimumSize(300, 300)
        self.beforePreviewLabel.setStyleSheet("background-color: #f0f0f0; border: 1px solid #d0d0d0;")
        self.beforePreviewLabel.setText("Select a page to preview")

        self.afterPreviewLabel = QLabel()
        self.afterPreviewLabel.setAlignment(Qt.AlignCenter)
        self.afterPreviewLabel.setMinimumSize(300, 300)
        self.afterPreviewLabel.setStyleSheet("background-color: #f0f0f0; border: 1px solid #d0d0d0;")
        self.afterPreviewLabel.setText("Select a page to preview")

        # Add preview widgets to layout
        previewLayout.addWidget(self.beforeRotationLabel)
        previewLayout.addWidget(self.beforePreviewLabel, 1)
        previewLayout.addWidget(self.afterRotationLabel)
        previewLayout.addWidget(self.afterPreviewLabel, 1)

        # Add widgets to splitter
        self.orientationSplitter.addWidget(resultsGroup)
        self.orientationSplitter.addWidget(previewGroup)
        self.orientationSplitter.setSizes([500, 500])  # Equal initial sizes

        # Create action buttons
        actionsLayout = QHBoxLayout()

        self.createPreviewButton = QPushButton("Create Preview Grid")
        self.createPreviewButton.clicked.connect(self.onCreatePreviewGrid)
        self.createPreviewButton.setEnabled(False)

        self.applyRotationsButton = QPushButton("Generate Applied Rotations PDF")
        self.applyRotationsButton.clicked.connect(self.onApplyRotations)
        self.applyRotationsButton.setEnabled(False)

        actionsLayout.addStretch()
        actionsLayout.addWidget(self.createPreviewButton)
        actionsLayout.addWidget(self.applyRotationsButton)

        # Add widgets to layout
        layout.addWidget(sourceGroup)
        layout.addWidget(self.detectOrientationButton)
        layout.addWidget(self.orientationSplitter, 1)  # Give the splitter more space
        layout.addLayout(actionsLayout)

        # Store the current PDF path for previews
        self.currentPdfPath = None

    def setupDetectGroupsTab(self):
        """Set up the Detect Groups tab."""
        layout = QVBoxLayout(self.detectGroupsTab)

        # Add source selection group
        sourceGroup = QGroupBox("PDF Source")
        sourceLayout = QFormLayout(sourceGroup)

        # Create file path input and browse button
        fileLayout = QHBoxLayout()
        self.groupsFilePathEdit = QLineEdit()
        self.groupsFilePathEdit.setReadOnly(True)
        self.groupsFilePathEdit.setPlaceholderText("Select a PDF file...")

        self.groupsBrowseButton = QPushButton("Browse...")
        self.groupsBrowseButton.clicked.connect(self.onBrowseGroupsFile)

        fileLayout.addWidget(self.groupsFilePathEdit)
        fileLayout.addWidget(self.groupsBrowseButton)

        sourceLayout.addRow("File:", fileLayout)

        # Add group detection options
        optionsGroup = QGroupBox("Group Detection Options")
        optionsLayout = QFormLayout(optionsGroup)

        # Threshold spinbox
        self.thresholdSpinBox = QSpinBox()
        self.thresholdSpinBox.setRange(1, 100)
        self.thresholdSpinBox.setValue(5)
        self.thresholdSpinBox.setSuffix(" px")

        # Minimum group size
        self.minGroupSizeSpinBox = QSpinBox()
        self.minGroupSizeSpinBox.setRange(2, 50)
        self.minGroupSizeSpinBox.setValue(3)
        self.minGroupSizeSpinBox.setSuffix(" items")

        # Use color detection
        self.useColorCheckBox = QCheckBox("Use color information for grouping")
        self.useColorCheckBox.setChecked(True)

        # Export options
        self.exportResultsCheckBox = QCheckBox("Export results to Excel")
        self.exportResultsCheckBox.setChecked(True)

        # Add options to form layout
        optionsLayout.addRow("Distance threshold:", self.thresholdSpinBox)
        optionsLayout.addRow("Minimum group size:", self.minGroupSizeSpinBox)
        optionsLayout.addRow("", self.useColorCheckBox)
        optionsLayout.addRow("", self.exportResultsCheckBox)

        # Process button
        self.processGroupsButton = QPushButton("Detect Groups")
        self.processGroupsButton.clicked.connect(self.onDetectGroups)
        self.processGroupsButton.setEnabled(False)

        # Connect file path changes to enable/disable process button
        self.groupsFilePathEdit.textChanged.connect(
            lambda text: self.processGroupsButton.setEnabled(bool(text))
        )

        # Add widgets to layout
        layout.addWidget(sourceGroup)
        layout.addWidget(optionsGroup)
        layout.addWidget(self.processGroupsButton)

    def setupStatusBar(self):
        """Set up the status bar."""
        self.statusBar = QStatusBar()
        self.statusBar.showMessage("Ready")
        self.layout().addWidget(self.statusBar)

    def refreshProjectSources(self):
        """Refresh the list of projects and their sources."""
        # Fetch projects from database
        try:
            projects = self.db.executeQuery("SELECT id, project_name FROM atem_projects ORDER BY project_name")

            # Update project combo box
            self.projectComboBox.clear()
            for project in projects:
                self.projectComboBox.addItem(f"{project[1]} (ID: {project[0]})", project[0])

            # If there are projects, select the first one
            if self.projectComboBox.count() > 0:
                self.projectComboBox.setCurrentIndex(0)
            else:
                self.statusBar.showMessage("No projects found")
        except Exception as e:
            self.statusBar.showMessage(f"Error loading projects: {str(e)}")

    def loadProjectSources(self, projectId):
        """Load sources for the selected project."""
        if not projectId:
            return

        try:
            # Query to get project sources
            query = """
            SELECT ps.id, ps.project_id, ps.filename, ps.page_count, ps.status
            FROM project_sources ps
            WHERE ps.project_id = %s
            ORDER BY ps.filename
            """

            sources = self.db.executeQuery(query, (projectId,))

            # Convert to DataFrame
            df = pd.DataFrame(sources, columns=["id", "project_id", "filename", "page_count", "status"])

            # Update models
            self.sourceModel.setData(df)
            self.groupSourceModel.setData(df)

            # Update status
            self.statusBar.showMessage(f"Loaded {len(df)} sources for project ID {projectId}")

        except Exception as e:
            self.statusBar.showMessage(f"Error loading sources: {str(e)}")

    def onProjectSelected(self, index):
        """Handle selection of a project."""
        if index < 0:
            return

        projectId = self.projectComboBox.itemData(index)
        self.currentProjectId = projectId

        # Load sources for this project
        self.loadProjectSources(projectId)

    def onAddSource(self):
        """Add a new source to the current project."""
        if not self.currentProjectId:
            QMessageBox.warning(self, "Warning", "Please select a project first.")
            return

        # Open file dialog to select PDF files
        files, _ = QFileDialog.getOpenFileNames(
            self, "Select PDF Files", "", "PDF Files (*.pdf)"
        )

        if not files:
            return

        # Add each file as a source
        for file_path in files:
            filename = os.path.basename(file_path)

            try:
                # Check if source already exists
                check_query = """
                SELECT id FROM project_sources
                WHERE project_id = %s AND filename = %s
                """
                existing = self.db.executeQuery(check_query, (self.currentProjectId, filename))

                if existing:
                    QMessageBox.information(
                        self, "Information", f"Source '{filename}' already exists for this project."
                    )
                    continue

                # Insert new source
                insert_query = """
                INSERT INTO project_sources (project_id, filename, status)
                VALUES (%s, %s, 'new')
                """
                self.db.executeQuery(insert_query, (self.currentProjectId, filename))

                # Copy file to project directory
                target_dir = getSourceRawDataPath(self.currentProjectId)
                os.makedirs(target_dir, exist_ok=True)
                target_path = os.path.join(target_dir, filename)

                # Copy file if it doesn't exist
                if not os.path.exists(target_path):
                    import shutil
                    shutil.copy2(file_path, target_path)

                self.statusBar.showMessage(f"Added source: {filename}")

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to add source '{filename}': {str(e)}"
                )

        # Refresh sources
        self.loadProjectSources(self.currentProjectId)

    def onRemoveSource(self):
        """Remove the selected source from the current project."""
        # Get selected row from the active tab
        if self.tabWidget.currentIndex() == 0:
            tableView = self.sourceTableView
            model = self.sourceModel
        else:
            tableView = self.groupSourceTableView
            model = self.groupSourceModel

        selected = tableView.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "Warning", "Please select a source to remove.")
            return

        # Get source ID
        row_idx = selected[0].row()
        source_id = model._data.iloc[row_idx, 0]
        filename = model._data.iloc[row_idx, 2]

        # Confirm deletion
        reply = QMessageBox.question(
            self, "Confirm Removal",
            f"Are you sure you want to remove '{filename}'?\n\nThis will only remove the database entry, not the actual file.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Delete source
                delete_query = "DELETE FROM project_sources WHERE id = %s"
                self.db.executeQuery(delete_query, (source_id,))

                self.statusBar.showMessage(f"Removed source: {filename}")

                # Refresh sources
                self.loadProjectSources(self.currentProjectId)

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to remove source: {str(e)}"
                )

    def onBrowseOrientationFile(self):
        """Open file dialog to select a PDF file for orientation processing."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select PDF File", "", "PDF Files (*.pdf)"
        )

        if file_path:
            self.orientationFilePathEdit.setText(file_path)
            self.statusBar.showMessage(f"Selected file: {os.path.basename(file_path)}")

            # Store the current PDF path for previews
            self.currentPdfPath = file_path

            # Populate the table with all pages from the PDF
            self.populateOrientationTable(file_path)

    def populateOrientationTable(self, pdf_path):
        """Populate the orientation table with all pages from the PDF."""
        try:
            # Show processing message
            filename = os.path.basename(pdf_path)
            self.statusBar.showMessage(f"Loading pages from {filename}...")

            # Disable UI during processing
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Open the PDF to get page count
            import fitz  # PyMuPDF
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            doc.close()

            if total_pages > 0:
                # Create initial data with all pages set to 0 rotation and no detection failures
                data = []
                for page_num in range(1, total_pages + 1):  # 1-based page numbering
                    data.append({
                        'pdf_page': page_num,
                        'apply_rotation': 0,
                        'no_rotation_detected': None
                    })

                # Create DataFrame and update the model
                df = pd.DataFrame(data)
                self.orientationResultsModel.setDataFrame(df)

                # Resize columns to content
                self.orientationResultsTable.resizeColumnsToContents()

                # Enable the buttons
                self.detectOrientationButton.setEnabled(True)
                self.createPreviewButton.setEnabled(True)
                self.applyRotationsButton.setEnabled(True)
                self.exportOrientationButton.setEnabled(True)
                self.importOrientationButton.setEnabled(True)

                # Select the first row to show a preview
                if len(df) > 0:
                    self.orientationResultsTable.selectRow(0)

                self.statusBar.showMessage(f"Loaded {total_pages} pages from {filename}")
            else:
                QMessageBox.warning(self, "Warning", "The selected PDF has no pages.")
                self.statusBar.showMessage("Selected PDF has no pages")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load PDF: {str(e)}")
            self.statusBar.showMessage(f"Error loading PDF: {str(e)}")

        finally:
            # Restore cursor
            QApplication.restoreOverrideCursor()

    def onBrowseGroupsFile(self):
        """Open file dialog to select a PDF file for group detection."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select PDF File", "", "PDF Files (*.pdf)"
        )

        if file_path:
            self.groupsFilePathEdit.setText(file_path)
            self.statusBar.showMessage(f"Selected file: {os.path.basename(file_path)}")

    def onDetectGroups(self):
        pass

    def onDetectOrientation(self):
        """Detect orientation for all pages in the selected PDF file."""
        file_path = self.orientationFilePathEdit.text()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Warning", "Please select a valid PDF file.")
            return

        # Show confirmation dialog
        reply = QMessageBox.question(
            self, "Confirm Orientation Detection",
            "This will detect the orientation for all pages in the PDF.\n"
            "This process may take some time for large documents.\n\n"
            "Do you want to continue?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return  # User canceled

        # Show processing message
        filename = os.path.basename(file_path)
        self.statusBar.showMessage(f"Detecting orientation for {filename}...")

        # Disable UI during processing
        self.detectOrientationButton.setEnabled(False)
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # Use the detect_orientation_parallel function from autorotation module
            results_df = detect_orientation_parallel(file_path)

            # Update the model with results
            self.orientationResultsModel.setDataFrame(results_df)

            # Resize columns to content
            self.orientationResultsTable.resizeColumnsToContents()

            # Enable action buttons
            self.createPreviewButton.setEnabled(True)
            self.applyRotationsButton.setEnabled(True)

            # Update the preview if a row is selected
            selected_indexes = self.orientationResultsTable.selectionModel().selectedRows()
            if not selected_indexes and len(results_df) > 0:
                self.orientationResultsTable.selectRow(0)
            else:
                self.updateOrientationPreview()

            # Show success message
            self.statusBar.showMessage(f"Orientation detection completed for {filename}")

            # Show summary
            rotation_counts = results_df['apply_rotation'].value_counts()
            failed_count = results_df['no_rotation_detected'].sum()

            summary = f"Orientation detection completed for {filename}.\n\n"
            summary += f"Total pages: {len(results_df)}\n"
            summary += f"Pages with rotation detected: {len(results_df) - failed_count - rotation_counts.get(0, 0)}\n"
            summary += f"Pages with detection failure: {failed_count}\n\n"
            summary += "Rotation breakdown:\n"

            for rotation, count in rotation_counts.items():
                if rotation is not None and rotation != 0:
                    summary += f"  {rotation}°: {count} pages\n"

            QMessageBox.information(self, "Detection Results", summary)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to detect orientation: {str(e)}")
            self.statusBar.showMessage(f"Error detecting orientation: {str(e)}")

        finally:
            # Re-enable UI
            self.detectOrientationButton.setEnabled(True)
            QApplication.restoreOverrideCursor()

    def onCreatePreviewGrid(self):
        """Create a preview grid of the PDF with orientation applied."""
        file_path = self.orientationFilePathEdit.text()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Warning", "Please select a valid PDF file.")
            return

        # Get the results dataframe from the model
        results_df = self.orientationResultsModel.getData()

        # Show confirmation dialog
        reply = QMessageBox.question(
            self, "Confirm Preview Grid Creation",
            "This will generate a low-DPI grid of pages to visually verify the orientation.\n"
            "The grid will be opened in your default image viewer.\n\n"
            "Do you want to continue?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return  # User canceled

        # Show processing message
        filename = os.path.basename(file_path)
        self.statusBar.showMessage(f"Creating preview grid for {filename}...")

        # Disable UI during processing
        self.createPreviewButton.setEnabled(False)
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # Use the create_preview_grid function from autorotation module
            preview_path = create_preview_grid(file_path, results_df, grid_size=(5, 7), dpi=16)

            if preview_path:
                # Show success message
                self.statusBar.showMessage(f"Preview grid created: {preview_path}")

                # Create a message box with a clickable link
                msgBox = QMessageBox(self)
                msgBox.setWindowTitle("Preview Grid Created")
                msgBox.setIcon(QMessageBox.Information)
                msgBox.setText(f"Preview grid has been created successfully.")

                # Create a label with a clickable link
                link_label = QLabel(f'<a href="file:///{preview_path}">Click here to open the preview grid</a>')
                link_label.setOpenExternalLinks(True)
                link_label.setTextFormat(Qt.RichText)

                # Create a layout for the message box
                layout = msgBox.layout()
                layout.addWidget(link_label, 1, 1, 1, layout.columnCount(), Qt.AlignCenter)

                # Add OK button
                msgBox.addButton(QMessageBox.Ok)

                # Show the message box
                msgBox.exec_()
            else:
                QMessageBox.warning(self, "Warning", "Failed to create preview grid.")
                self.statusBar.showMessage("Failed to create preview grid")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to create preview grid: {str(e)}")
            self.statusBar.showMessage(f"Error creating preview grid: {str(e)}")

        finally:
            # Re-enable UI
            self.createPreviewButton.setEnabled(True)
            QApplication.restoreOverrideCursor()

    def onApplyRotations(self):
        """Apply rotations to the PDF and save the result."""
        file_path = self.orientationFilePathEdit.text()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Warning", "Please select a valid PDF file.")
            return

        # Get the results dataframe from the model
        results_df = self.orientationResultsModel.getData()

        # Show confirmation dialog
        reply = QMessageBox.question(
            self, "Confirm Apply Rotations",
            "This will create a new PDF with the rotations applied.\n"
            "The original PDF will not be modified.\n\n"
            "Do you want to continue?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return  # User canceled

        # Show processing message
        filename = os.path.basename(file_path)
        self.statusBar.showMessage(f"Applying rotations to {filename}...")

        # Disable UI during processing
        self.applyRotationsButton.setEnabled(False)
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # Use the apply_rotations_to_pdf_pypdf function from autorotation module
            rotated_path = apply_rotations_to_pdf_pypdf(file_path, results_df)

            if rotated_path:
                # Show success message
                self.statusBar.showMessage(f"Rotations applied: {rotated_path}")

                # Create a message box with a clickable link
                msgBox = QMessageBox(self)
                msgBox.setWindowTitle("Rotations Applied")
                msgBox.setIcon(QMessageBox.Information)
                msgBox.setText(f"Rotated PDF has been created successfully.")

                # Create a label with a clickable link
                link_label = QLabel(f'<a href="file:///{rotated_path}">Click here to open the rotated PDF</a>')
                link_label.setOpenExternalLinks(True)
                link_label.setTextFormat(Qt.RichText)

                # Create a layout for the message box
                layout = msgBox.layout()
                layout.addWidget(link_label, 1, 1, 1, layout.columnCount(), Qt.AlignCenter)

                # Add OK button
                msgBox.addButton(QMessageBox.Ok)

                # Show the message box
                msgBox.exec_()
            else:
                QMessageBox.warning(self, "Warning", "Failed to apply rotations.")
                self.statusBar.showMessage("Failed to apply rotations")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to apply rotations: {str(e)}")
            self.statusBar.showMessage(f"Error applying rotations: {str(e)}")

        finally:
            # Re-enable UI
            self.applyRotationsButton.setEnabled(True)
            QApplication.restoreOverrideCursor()

    def updateOrientationPreview(self):
        """Update the orientation preview based on the selected row."""
        # Check if a PDF is loaded
        if not self.currentPdfPath or not os.path.exists(self.currentPdfPath):
            return

        # Get the selected row
        selected_indexes = self.orientationResultsTable.selectionModel().selectedRows()
        if not selected_indexes:
            # Clear previews if no row is selected
            self.beforePreviewLabel.setText("Select a page to preview")
            self.afterPreviewLabel.setText("Select a page to preview")
            return

        # Get the page number and rotation from the selected row
        row_idx = selected_indexes[0].row()
        df = self.orientationResultsModel.getData()

        # Make sure we're using the correct column names
        if 'pdf_page' not in df.columns or 'apply_rotation' not in df.columns:
            self.statusBar.showMessage("Error: DataFrame is missing required columns")
            return

        # Access by column name to avoid errors
        page_num = df.at[row_idx, 'pdf_page']
        rotation = df.at[row_idx, 'apply_rotation']

        # Show processing message
        self.statusBar.showMessage(f"Loading preview for page {page_num}...")

        # Disable UI during processing
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # Open the PDF
            import fitz  # PyMuPDF
            doc = fitz.open(self.currentPdfPath)

            # Convert from 1-based to 0-based page numbering
            page_idx = int(page_num) - 1

            if 0 <= page_idx < len(doc):
                page = doc[page_idx]

                # Render before rotation preview
                before_matrix = fitz.Matrix(1.5, 1.5)  # Scale for better visibility
                before_pix = page.get_pixmap(matrix=before_matrix)
                before_qimage = QImage(before_pix.samples, before_pix.width, before_pix.height,
                                      before_pix.stride, QImage.Format_RGB888)
                before_pixmap = QPixmap.fromImage(before_qimage)

                # Scale pixmap to fit the label while maintaining aspect ratio
                before_pixmap = before_pixmap.scaled(
                    self.beforePreviewLabel.width(), self.beforePreviewLabel.height(),
                    Qt.KeepAspectRatio, Qt.SmoothTransformation
                )

                # Set the before preview
                self.beforePreviewLabel.setPixmap(before_pixmap)

                # Render after rotation preview
                after_matrix = fitz.Matrix(1.5, 1.5)  # Scale for better visibility
                if rotation != 0:
                    after_matrix.prerotate(rotation)

                after_pix = page.get_pixmap(matrix=after_matrix)
                after_qimage = QImage(after_pix.samples, after_pix.width, after_pix.height,
                                     after_pix.stride, QImage.Format_RGB888)
                after_pixmap = QPixmap.fromImage(after_qimage)

                # Scale pixmap to fit the label while maintaining aspect ratio
                after_pixmap = after_pixmap.scaled(
                    self.afterPreviewLabel.width(), self.afterPreviewLabel.height(),
                    Qt.KeepAspectRatio, Qt.SmoothTransformation
                )

                # Set the after preview
                self.afterPreviewLabel.setPixmap(after_pixmap)

                # Update status
                self.statusBar.showMessage(f"Previewing page {page_num} with {rotation}° rotation")
            else:
                # Invalid page number
                self.beforePreviewLabel.setText(f"Invalid page number: {page_num}")
                self.afterPreviewLabel.setText(f"Invalid page number: {page_num}")

            # Close the document
            doc.close()

        except Exception as e:
            self.beforePreviewLabel.setText(f"Error loading preview: {str(e)}")
            self.afterPreviewLabel.setText(f"Error loading preview: {str(e)}")
            self.statusBar.showMessage(f"Error loading preview: {str(e)}")

        finally:
            # Restore cursor
            QApplication.restoreOverrideCursor()

    def onOrientationDataChanged(self, topLeft, bottomRight):
        """Handle data changes in the orientation model and update preview if needed."""
        # Only update preview if the rotation column was changed
        if topLeft.column() <= 1 <= bottomRight.column():
            # Check if the changed row is the currently selected row
            selected_indexes = self.orientationResultsTable.selectionModel().selectedRows()
            if selected_indexes and topLeft.row() <= selected_indexes[0].row() <= bottomRight.row():
                # Update the preview for the selected row
                self.updateOrientationPreview()

    def onExportOrientationTable(self):
        """Export the orientation table to an Excel file."""
        if not self.currentPdfPath:
            QMessageBox.warning(self, "Warning", "No PDF file is currently loaded.")
            return

        # Get the current data
        df = self.orientationResultsModel.getData()
        if df.empty:
            QMessageBox.warning(self, "Warning", "No data to export.")
            return

        # Get the filename for the export
        pdf_filename = os.path.basename(self.currentPdfPath)
        pdf_name = os.path.splitext(pdf_filename)[0]
        default_export_name = f"{pdf_name}_orientation.xlsx"

        export_path, _ = QFileDialog.getSaveFileName(
            self, "Export Orientation Table", default_export_name, "Excel Files (*.xlsx)"
        )

        if not export_path:
            return  # User canceled

        try:
            # Show processing message
            self.statusBar.showMessage(f"Exporting orientation table to {export_path}...")

            # Disable UI during processing
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Export the DataFrame to Excel
            df.to_excel(export_path, index=False, sheet_name="Orientation Data")

            # Show success message
            self.statusBar.showMessage(f"Orientation table exported to {export_path}")
            QMessageBox.information(
                self, "Export Successful",
                f"Orientation table has been exported to:\n{export_path}"
            )

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export orientation table: {str(e)}")
            self.statusBar.showMessage(f"Error exporting orientation table: {str(e)}")

        finally:
            # Restore cursor
            QApplication.restoreOverrideCursor()

    def onImportOrientationTable(self):
        """Import an orientation table from an Excel file with validation."""
        if not self.currentPdfPath:
            QMessageBox.warning(self, "Warning", "No PDF file is currently loaded.")
            return

        # Get the filename for the import
        import_path, _ = QFileDialog.getOpenFileName(
            self, "Import Orientation Table", "", "Excel Files (*.xlsx)"
        )

        if not import_path:
            return  # User canceled

        try:
            # Show processing message
            self.statusBar.showMessage(f"Importing orientation table from {import_path}...")

            # Disable UI during processing
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Import the Excel file
            imported_df = pd.read_excel(import_path, sheet_name=0)

            # Validate the imported data
            validation_result = self.validateImportedOrientationTable(imported_df)

            if validation_result["valid"]:
                # Update the model with the imported data
                self.orientationResultsModel.setDataFrame(imported_df)

                # Resize columns to content
                self.orientationResultsTable.resizeColumnsToContents()

                # Select the first row to show a preview
                if len(imported_df) > 0:
                    self.orientationResultsTable.selectRow(0)

                # Show success message
                self.statusBar.showMessage(f"Orientation table imported from {import_path}")
                QMessageBox.information(
                    self, "Import Successful",
                    f"Orientation table has been imported from:\n{import_path}"
                )
            else:
                # Show validation error
                QMessageBox.critical(
                    self, "Import Error",
                    f"Failed to import orientation table: {validation_result['message']}"
                )
                self.statusBar.showMessage(f"Error importing orientation table: {validation_result['message']}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import orientation table: {str(e)}")
            self.statusBar.showMessage(f"Error importing orientation table: {str(e)}")

        finally:
            # Restore cursor
            QApplication.restoreOverrideCursor()

    def validateImportedOrientationTable(self, imported_df):
        """
        Validate the imported orientation table.

        Args:
            imported_df (pd.DataFrame): The imported DataFrame

        Returns:
            dict: A dictionary with 'valid' (bool) and 'message' (str) keys
        """
        # Check if the required columns exist
        required_columns = ['pdf_page', 'apply_rotation', 'no_rotation_detected']
        missing_columns = [col for col in required_columns if col not in imported_df.columns]

        if missing_columns:
            return {
                "valid": False,
                "message": f"Missing required columns: {', '.join(missing_columns)}"
            }

        # Get the current PDF page count
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(self.currentPdfPath)
            total_pages = len(doc)
            doc.close()
        except Exception as e:
            return {
                "valid": False,
                "message": f"Failed to get PDF page count: {str(e)}"
            }

        # Check if all pages are specified exactly once
        imported_pages = imported_df['pdf_page'].tolist()
        expected_pages = list(range(1, total_pages + 1))  # 1-based page numbering

        # Check for missing pages
        missing_pages = [page for page in expected_pages if page not in imported_pages]
        if missing_pages:
            return {
                "valid": False,
                "message": f"Missing pages in imported data: {', '.join(map(str, missing_pages))}"
            }

        # Check for extra pages
        extra_pages = [page for page in imported_pages if page not in expected_pages]
        if extra_pages:
            return {
                "valid": False,
                "message": f"Extra pages in imported data: {', '.join(map(str, extra_pages))}"
            }

        # Check for duplicate pages
        duplicate_pages = [page for page in imported_pages if imported_pages.count(page) > 1]
        if duplicate_pages:
            return {
                "valid": False,
                "message": f"Duplicate pages in imported data: {', '.join(map(str, set(duplicate_pages)))}"
            }

        # Check if rotation values are valid
        valid_rotations = [0, 90, 180, 270]
        invalid_rotations = [rot for rot in imported_df['apply_rotation'] if rot not in valid_rotations]
        if invalid_rotations:
            return {
                "valid": False,
                "message": f"Invalid rotation values: {', '.join(map(str, set(invalid_rotations)))}"
            }

        # All checks passed
        return {
            "valid": True,
            "message": "Validation successful"
        }


if __name__ == "__main__":
    import sys

    # Ensure the application can find modules
    sys.path[0] = ""

    app = QApplication(sys.argv)
    window = SourcePreparationUtility()
    window.show()
    sys.exit(app.exec())