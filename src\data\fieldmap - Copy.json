{"fields": {"Isometric Drawing Area": {"default": "Isometric Drawing Area", "display": "Isometric Drawing Area"}, "SPEC": {"default": "SPEC", "display": "SPEC"}, "Spool": {"default": "Spool", "display": "Spool"}, "BOM": {"default": "BOM", "display": "BOM", "tags": ["bill of materials"]}, "annotMarkups": {"default": "Annot Types Data", "display": "Annot Types Data"}, "area": {"default": "Area", "display": "Area", "tags": ["unit"]}, "avg_elevation": {"default": "Average Elevation", "display": "Average Elevation"}, "clientDocumentId": {"default": "Client Document Id", "display": "Client Document Id"}, "coordinates": {"default": "Block Coordinates", "display": "Block Coordinates"}, "coordinates2": {"default": "Precise Coordinates", "display": "Precise Coordinates"}, "designCode": {"default": "Design Code", "display": "Design Code"}, "documentDescription": {"default": "Document Description", "display": "Document Description"}, "documentId": {"default": "Document Id", "display": "Document Id"}, "documentTitle": {"default": "Document Title", "display": "Document Title"}, "drawing": {"default": "Drawing", "display": "Drawing", "tags": ["line", "isometric"]}, "elevation": {"default": "Elevation", "display": "Elevation"}, "heatTrace": {"default": "Tracing", "display": "Tracing", "tags": ["tracing", "electical trace", "ht", "et"]}, "ident": {"default": "Identifier", "display": "Identifier", "tags": ["id"]}, "id_annot_info": {"default": "Annotation ID", "display": "Annotation ID"}, "insulationSpec": {"default": "Insulation Spec", "display": "Insulation Spec", "tags": ["spec"]}, "insulationThickness": {"default": "Insulation Thickness", "display": "Insulation Thickness", "tags": ["thck", "thick"]}, "item": {"default": "<PERSON><PERSON>", "display": "<PERSON><PERSON>"}, "lineNumber": {"default": "Line Number", "display": "Line Number", "tags": ["drawing", "dwg"]}, "material_description": {"default": "Material Description", "display": "Material Description", "tags": ["description"]}, "max_elevation": {"default": "Maximum Elevation", "display": "Maximum Elevation"}, "min_elevation": {"default": "Minimum Elevation", "display": "Minimum Elevation"}, "mediumCode": {"default": "Medium Code", "display": "Medium Code"}, "modDate": {"default": "modDate", "display": "modDate"}, "paintSpec": {"default": "Paint Spec", "display": "Paint Spec"}, "pdf_id": {"default": "PDF ID", "display": "PDF ID"}, "pdf_page": {"default": "PDF Page", "display": "PDF Page"}, "pid": {"default": "P&ID", "display": "P&ID"}, "pipeSpec": {"default": "Pipe S<PERSON>", "display": "Pipe S<PERSON>", "tags": ["spec", "class"]}, "pipeStandard": {"default": "Pipe Standard", "display": "Pipe Standard", "tags": ["standard", "class", "cls", "std"]}, "pos": {"default": "Position", "display": "Position", "tags": ["no", "number"]}, "processLineList": {"default": "Process Line List", "display": "Process Line List", "tags": ["line list", "linelist"]}, "processUnit": {"default": "Process Unit", "display": "Process Unit", "tags": ["unit"]}, "projectName": {"default": "Project Name", "display": "Project Name"}, "projectNo": {"default": "Project No.", "display": "Project No."}, "pwht": {"default": "PWHT", "display": "PWHT", "tags": ["post weld heat treat", "heat treat", "stress relief"]}, "quantity": {"default": "Quantity", "display": "Quantity", "tags": ["qty"]}, "ref_elevation": {"default": "Reference Elevation", "display": "Reference Elevation", "tags": ["elevation"]}, "revision": {"default": "Revision", "display": "Revision", "tags": ["issue", "alteration"]}, "sequence": {"default": "Sequence", "display": "Sequence"}, "service": {"default": "Service", "display": "Service", "tags": ["fluid", "medium code"]}, "sheet": {"default": "Sheet", "display": "Sheet", "tags": ["page"]}, "size": {"default": "Size", "display": "Size", "tags": ["nps", "npd", "nd", "dn", "ns", "nominal", "dia.", "diameter"]}, "system": {"default": "System", "display": "System"}, "sys_filename": {"default": "SYS FILENAME", "display": "SYS FILENAME"}, "sys_path": {"default": "SYS PATH", "display": "SYS PATH"}, "sys_build": {"default": "SYS BUILD", "display": "SYS BUILD"}, "sys_layout_valid": {"default": "Valid Layout", "display": "Valid Layout"}, "status": {"default": "Status", "display": "Status"}, "tag": {"default": "Tag", "display": "Tag", "tags": ["item tag"]}, "totalSheets": {"default": "Total Sheets", "display": "Total Sheets", "tags": ["of sheet"]}, "unit": {"default": "Unit", "display": "Unit", "tags": ["area"]}, "weldClass": {"default": "Weld Class", "display": "Weld Class"}, "vendorDocumentId": {"default": "Vendor Document Id", "display": "Vendor Document Id"}, "xray": {"default": "Xray", "display": "Xray", "tags": ["nde", "rt", "paut"]}, "cutPiece": {"default": "Piece No.", "display": "Piece No.", "tags": ["spool", "piece mark", "pc mark"]}, "length": {"default": "Length", "display": "Length"}, "spool": {"default": "Spool #", "display": "Spool #"}, "spec": {"default": "Pipe S<PERSON>", "display": "Pipe S<PERSON>", "tags": ["pipe spec"]}, "dp1": {"default": "Design Press.1 (P)", "display": "Design Press.1 (P)"}, "dp2": {"default": "Design Press.2 (P)", "display": "Design Press.2 (P)"}, "dt1": {"default": "Design Temp.1 (T)", "display": "Design Temp.1 (T)"}, "dt2": {"default": "Design Temp.2 (T)", "display": "Design Temp.2 (T)"}, "op1": {"default": "Op. Press.1 (P)", "display": "Op. Press.1 (P)"}, "op2": {"default": "Op. Press.2 (P)", "display": "Op. Press.2 (P)"}, "opt1": {"default": "Op. Temp.1 (T)", "display": "Op. Temp.1 (T)"}, "opt2": {"default": "Op. Temp.2 (T)", "display": "Op. Temp.2 (T)"}, "nb": {"default": "NB", "display": "NB"}, "fluid": {"default": "Fluid", "display": "Fluid", "tags": ["service", "medium code"]}, "clean_spec": {"default": "Cleaning Spec.", "display": "Cleaning Spec."}}, "ancillary_fields": {"lf": {"default": "LF", "display": "LF"}, "sf": {"default": "SF", "display": "SF"}, "ef": {"default": "EF", "display": "EF"}, "90s": {"default": "90's", "display": "90's"}, "45s": {"default": "45's", "display": "45's"}, "bevels": {"default": "Bevels", "display": "Bevels"}, "tees": {"default": "<PERSON><PERSON>", "display": "<PERSON><PERSON>"}, "reducers": {"default": "Red.", "display": "Red."}, "caps": {"default": "Caps", "display": "Caps"}, "flanges": {"default": "<PERSON><PERSON><PERSON>", "display": "<PERSON><PERSON><PERSON>"}, "valves_flanged": {"default": "Flanged <PERSON>", "display": "Flanged <PERSON>"}, "valves_welded": {"default": "Welded Valves", "display": "Welded Valves"}, "cut_outs": {"default": "Cut Outs", "display": "Cut Outs"}, "supports": {"default": "Supports", "display": "Supports"}, "bends": {"default": "Bends", "display": "Bends"}, "field_welds": {"default": "FW", "display": "FW"}}, "rfq_fields": {"ef": {"default": "EF", "display": "EF"}, "sf": {"default": "SF", "display": "SF"}, "general_category": {"default": "General Category", "display": "General Category", "options": ["LF", "90 Short Radius", "90 Long Radius", "45", "<PERSON><PERSON>", "<PERSON><PERSON>", "Reducer (Concentric)", "Reducer (Eccentric)", "Cap", "<PERSON><PERSON><PERSON>", "Flanged Valve", "Welded Valve", "Cut Out", "Bend", "Field Weld", "Support"], "gen_map": {"LF": "lf", "90 Short Radius": "90s", "90 Long Radius": "90s", "45": "45s", "Bevel": "bevels", "Tee": "tees", "Reducer (Concentric)": "reducers", "Reducer (Eccentric)": "reducers", "Cap": "caps", "Flanges": "flanges", "Flanged Valve": "valves_flanged", "Welded Valve": "valves_welded", "Cut Out": "cut_outs", "Bend": "bends", "Field Weld": "field_welds", "Support": "supports"}}, "rfq_scope": {"default": "RFQ <PERSON>ope", "display": "RFQ <PERSON>ope", "options": ["PIPE", "FITTINGS", "FLANGES", "BOLTS", "GASKETS", "SUPPORTS", "VALVES", "OTHER", "TAKEOFF_ITEM"]}, "unit_of_measure": {"default": "Unit Of Measure", "display": "Unit Of Measure", "options": ["EA", "LF", "SF", "SY", "CF", "CY", "LOT", "PAIR", "TONS"]}, "size1": {"default": "Size1", "display": "Size1", "options": ["0.03125", "0.0625", "0.125", "0.1875", "0.25", "0.375", "0.5", "0.625", "0.75", "0.875", "1", "1.125", "1.25", "1.375", "1.5", "1.625", "1.75", "1.875", "2", "2.125", "2.25", "2.375", "2.5", "2.625", "2.75", "2.875", "3", "3.125", "3.25", "3.375", "3.5", "3.625", "3.75", "3.875", "4", "4.125", "4.25", "4.375", "4.5", "4.625", "4.75", "4.875", "5", "5.125", "5.25", "5.375", "5.5", "5.625", "5.75", "5.875", "6", "6.125", "6.25", "6.375", "6.5", "6.625", "6.75", "6.875", "7", "7.125", "7.25", "7.375", "7.5", "7.625", "7.75", "7.875", "8", "8.125", "8.25", "8.375", "8.5", "8.625", "8.75", "8.875", "9", "9.125", "9.25", "9.375", "9.5", "9.625", "9.75", "9.875", "10", "10.125", "10.25", "10.375", "10.5", "10.625", "10.75", "10.875", "11", "11.125", "11.25", "11.375", "11.5", "11.625", "11.75", "11.875", "12", "12.125", "12.25", "12.375", "12.5", "12.625", "12.75", "12.875", "13", "13.125", "13.25", "13.375", "13.5", "13.625", "13.75", "13.875", "14", "14.125", "14.25", "14.375", "14.5", "14.625", "14.75", "14.875", "15", "15.125", "15.25", "15.375", "15.5", "15.625", "15.75", "15.875", "16", "16.125", "16.25", "16.375", "16.5", "16.625", "16.75", "16.875", "17", "17.125", "17.25", "17.375", "17.5", "17.625", "17.75", "17.875", "18", "18.125", "18.25", "18.375", "18.5", "18.625", "18.75", "18.875", "19", "19.125", "19.25", "19.375", "19.5", "19.625", "19.75", "19.875", "20", "20.125", "20.25", "20.375", "20.5", "20.625", "20.75", "20.875", "21", "21.125", "21.25", "21.375", "21.5", "21.625", "21.75", "21.875", "22", "22.125", "22.25", "22.375", "22.5", "22.625", "22.75", "22.875", "23", "23.125", "23.25", "23.375", "23.5", "23.625", "23.75", "23.875", "24", "24.125", "24.25", "24.375", "24.5", "24.625", "24.75", "24.875", "25", "25.125", "25.25", "25.375", "25.5", "25.625", "25.75", "25.875", "26", "26.125", "26.25", "26.375", "26.5", "26.625", "26.75", "26.875", "27", "27.125", "27.25", "27.375", "27.5", "27.625", "27.75", "27.875", "28", "28.125", "28.25", "28.375", "28.5", "28.625", "28.75", "28.875", "29", "29.125", "29.25", "29.375", "29.5", "29.625", "29.75", "29.875", "30", "30.125", "30.25", "30.375", "30.5", "30.625", "30.75", "30.875", "31", "31.125", "31.25", "31.375", "31.5", "31.625", "31.75", "31.875", "32", "32.125", "32.25", "32.375", "32.5", "32.625", "32.75", "32.875", "33", "33.125", "33.25", "33.375", "33.5", "33.625", "33.75", "33.875", "34", "34.125", "34.25", "34.375", "34.5", "34.625", "34.75", "34.875", "35", "35.125", "35.25", "35.375", "35.5", "35.625", "35.75", "35.875", "36", "36.125", "36.25", "36.375", "36.5", "36.625", "36.75", "36.875", "37", "37.125", "37.25", "37.375", "37.5", "37.625", "37.75", "37.875", "38", "38.125", "38.25", "38.375", "38.5", "38.625", "38.75", "38.875", "39", "39.125", "39.25", "39.375", "39.5", "39.625", "39.75", "39.875", "40", "42", "44", "46", "48", "50", "52", "54", "56", "58", "60", "62", "64", "66", "68", "72", "80", "84", "88", "100"]}, "size2": {"default": "Size2", "display": "Size2", "options": ["0.03125", "0.0625", "0.125", "0.1875", "0.25", "0.375", "0.5", "0.625", "0.75", "0.875", "1", "1.125", "1.25", "1.375", "1.5", "1.625", "1.75", "1.875", "2", "2.125", "2.25", "2.375", "2.5", "2.625", "2.75", "2.875", "3", "3.125", "3.25", "3.375", "3.5", "3.625", "3.75", "3.875", "4", "4.125", "4.25", "4.375", "4.5", "4.625", "4.75", "4.875", "5", "5.125", "5.25", "5.375", "5.5", "5.625", "5.75", "5.875", "6", "6.125", "6.25", "6.375", "6.5", "6.625", "6.75", "6.875", "7", "7.125", "7.25", "7.375", "7.5", "7.625", "7.75", "7.875", "8", "8.125", "8.25", "8.375", "8.5", "8.625", "8.75", "8.875", "9", "9.125", "9.25", "9.375", "9.5", "9.625", "9.75", "9.875", "10", "10.125", "10.25", "10.375", "10.5", "10.625", "10.75", "10.875", "11", "11.125", "11.25", "11.375", "11.5", "11.625", "11.75", "11.875", "12", "12.125", "12.25", "12.375", "12.5", "12.625", "12.75", "12.875", "13", "13.125", "13.25", "13.375", "13.5", "13.625", "13.75", "13.875", "14", "14.125", "14.25", "14.375", "14.5", "14.625", "14.75", "14.875", "15", "15.125", "15.25", "15.375", "15.5", "15.625", "15.75", "15.875", "16", "16.125", "16.25", "16.375", "16.5", "16.625", "16.75", "16.875", "17", "17.125", "17.25", "17.375", "17.5", "17.625", "17.75", "17.875", "18", "18.125", "18.25", "18.375", "18.5", "18.625", "18.75", "18.875", "19", "19.125", "19.25", "19.375", "19.5", "19.625", "19.75", "19.875", "20", "20.125", "20.25", "20.375", "20.5", "20.625", "20.75", "20.875", "21", "21.125", "21.25", "21.375", "21.5", "21.625", "21.75", "21.875", "22", "22.125", "22.25", "22.375", "22.5", "22.625", "22.75", "22.875", "23", "23.125", "23.25", "23.375", "23.5", "23.625", "23.75", "23.875", "24", "24.125", "24.25", "24.375", "24.5", "24.625", "24.75", "24.875", "25", "25.125", "25.25", "25.375", "25.5", "25.625", "25.75", "25.875", "26", "26.125", "26.25", "26.375", "26.5", "26.625", "26.75", "26.875", "27", "27.125", "27.25", "27.375", "27.5", "27.625", "27.75", "27.875", "28", "28.125", "28.25", "28.375", "28.5", "28.625", "28.75", "28.875", "29", "29.125", "29.25", "29.375", "29.5", "29.625", "29.75", "29.875", "30", "30.125", "30.25", "30.375", "30.5", "30.625", "30.75", "30.875", "31", "31.125", "31.25", "31.375", "31.5", "31.625", "31.75", "31.875", "32", "32.125", "32.25", "32.375", "32.5", "32.625", "32.75", "32.875", "33", "33.125", "33.25", "33.375", "33.5", "33.625", "33.75", "33.875", "34", "34.125", "34.25", "34.375", "34.5", "34.625", "34.75", "34.875", "35", "35.125", "35.25", "35.375", "35.5", "35.625", "35.75", "35.875", "36", "36.125", "36.25", "36.375", "36.5", "36.625", "36.75", "36.875", "37", "37.125", "37.25", "37.375", "37.5", "37.625", "37.75", "37.875", "38", "38.125", "38.25", "38.375", "38.5", "38.625", "38.75", "38.875", "39", "39.125", "39.25", "39.375", "39.5", "39.625", "39.75", "39.875", "40", "42", "44", "46", "48", "50", "52", "54", "56", "58", "60", "62", "64", "66", "68", "72", "80", "84", "88", "100"]}, "schedule": {"default": "SCH", "display": "SCH", "options": ["5", "5S", "10", "10S", "20", "30", "40", "40S", "60", "80", "80S", "100", "120", "140", "160", "STD", "XH", "XXH", "Custom-L", "Custom-M", "Custom-H", "5 x 5S", "5 x 10", "5 x 10S", "5 x 20", "5 x 30", "5 x 40", "5 x 40S", "5 x 60", "5 x 80", "5 x 80S", "5 x 100", "5 x 120", "5 x 140", "5 x 160", "5 x STD", "5 x XH", "5 x XXH", "5S x 5", "5S x 10", "5S x 10S", "5S x 20", "5S x 30", "5S x 40", "5S x 40S", "5S x 60", "5S x 80", "5S x 80S", "5S x 100", "5S x 120", "5S x 140", "5S x 160", "5S x STD", "5S x XH", "5S x XXH", "10 x 5", "10 x 5S", "10 x 10S", "10 x 20", "10 x 30", "10 x 40", "10 x 40S", "10 x 60", "10 x 80", "10 x 80S", "10 x 100", "10 x 120", "10 x 140", "10 x 160", "10 x STD", "10 x XH", "10 x XXH", "10S x 5", "10S x 5S", "10S x 10", "10S x 20", "10S x 30", "10S x 40", "10S x 40S", "10S x 60", "10S x 80", "10S x 80S", "10S x 100", "10S x 120", "10S x 140", "10S x 160", "10S x STD", "10S x XH", "10S x XXH", "20 x 5", "20 x 5S", "20 x 10", "20 x 10S", "20 x 30", "20 x 40", "20 x 40S", "20 x 60", "20 x 80", "20 x 80S", "20 x 100", "20 x 120", "20 x 140", "20 x 160", "20 x STD", "20 x XH", "20 x XXH", "30 x 5", "30 x 5S", "30 x 10", "30 x 10S", "30 x 20", "30 x 40", "30 x 40S", "30 x 60", "30 x 80", "30 x 80S", "30 x 100", "30 x 120", "30 x 140", "30 x 160", "30 x STD", "30 x XH", "30 x XXH", "40 x 5", "40 x 5S", "40 x 10", "40 x 10S", "40 x 20", "40 x 30", "40 x 40S", "40 x 60", "40 x 80", "40 x 80S", "40 x 100", "40 x 120", "40 x 140", "40 x 160", "40 x STD", "40 x XH", "40 x XXH", "40S x 5", "40S x 5S", "40S x 10", "40S x 10S", "40S x 20", "40S x 30", "40S x 40", "40S x 60", "40S x 80", "40S x 80S", "40S x 100", "40S x 120", "40S x 140", "40S x 160", "40S x STD", "40S x XH", "40S x XXH", "60 x 5", "60 x 5S", "60 x 10", "60 x 10S", "60 x 20", "60 x 30", "60 x 40", "60 x 40S", "60 x 80", "60 x 80S", "60 x 100", "60 x 120", "60 x 140", "60 x 160", "60 x STD", "60 x XH", "60 x XXH", "80 x 5", "80 x 5S", "80 x 10", "80 x 10S", "80 x 20", "80 x 30", "80 x 40", "80 x 40S", "80 x 60", "80 x 80S", "80 x 100", "80 x 120", "80 x 140", "80 x 160", "80 x STD", "80 x XH", "80 x XXH", "80S x 5", "80S x 5S", "80S x 10", "80S x 10S", "80S x 20", "80S x 30", "80S x 40", "80S x 40S", "80S x 60", "80S x 80", "80S x 100", "80S x 120", "80S x 140", "80S x 160", "80S x STD", "80S x XH", "80S x XXH", "100 x 5", "100 x 5S", "100 x 10", "100 x 10S", "100 x 20", "100 x 30", "100 x 40", "100 x 40S", "100 x 60", "100 x 80", "100 x 80S", "100 x 120", "100 x 140", "100 x 160", "100 x STD", "100 x XH", "100 x XXH", "120 x 5", "120 x 5S", "120 x 10", "120 x 10S", "120 x 20", "120 x 30", "120 x 40", "120 x 40S", "120 x 60", "120 x 80", "120 x 80S", "120 x 100", "120 x 140", "120 x 160", "120 x STD", "120 x XH", "120 x XXH", "140 x 5", "140 x 5S", "140 x 10", "140 x 10S", "140 x 20", "140 x 30", "140 x 40", "140 x 40S", "140 x 60", "140 x 80", "140 x 80S", "140 x 100", "140 x 120", "140 x 160", "140 x STD", "140 x XH", "140 x XXH", "160 x 5", "160 x 5S", "160 x 10", "160 x 10S", "160 x 20", "160 x 30", "160 x 40", "160 x 40S", "160 x 60", "160 x 80", "160 x 80S", "160 x 100", "160 x 120", "160 x 140", "160 x STD", "160 x XH", "160 x XXH", "STD x 5", "STD x 5S", "STD x 10", "STD x 10S", "STD x 20", "STD x 30", "STD x 40", "STD x 40S", "STD x 60", "STD x 80", "STD x 80S", "STD x 100", "STD x 120", "STD x 140", "STD x 160", "STD x XH", "STD x XXH", "XH x 5", "XH x 5S", "XH x 10", "XH x 10S", "XH x 20", "XH x 30", "XH x 40", "XH x 40S", "XH x 60", "XH x 80", "XH x 80S", "XH x 100", "XH x 120", "XH x 140", "XH x 160", "XH x STD", "XH x XXH", "XXH x 5", "XXH x 5S", "XXH x 10", "XXH x 10S", "XXH x 20", "XXH x 30", "XXH x 40", "XXH x 40S", "XXH x 60", "XXH x 80", "XXH x 80S", "XXH x 100", "XXH x 120", "XXH x 140", "XXH x 160", "XXH x STD", "XXH x XH"]}, "rating": {"default": "Rating", "display": "Rating", "options": ["100", "125", "150", "200", "300", "325", "500", "600", "800", "900", "1500", "2000", "2500", "3000", "3500", "6000"]}, "astm": {"default": "ASTM", "display": "ASTM", "options": ["A105", "A105N", "A106", "A108", "A126", "A134", "A139", "A155", "A181", "A182", "A193", "A194", "A197", "A198", "A199", "A200", "A201", "A202", "A203", "A204", "A205", "A206", "A207", "A208", "A209", "A210", "A211", "A212", "A213", "A214", "A215", "A216", "A217", "A218", "A219", "A220", "A221", "A222", "A223", "A224", "A225", "A226", "A227", "A228", "A229", "A230", "A231", "A232", "A233", "A234", "A235", "A236", "A237", "A238", "A239", "A240", "A241", "A242", "A243", "A244", "A245", "A246", "A247", "A248", "A249", "A250", "A251", "A252", "A253", "A254", "A255", "A256", "A257", "A258", "A259", "A260", "A261", "A262", "A263", "A264", "A265", "A266", "A267", "A268", "A269", "A270", "A271", "A272", "A273", "A274", "A275", "A276", "A277", "A278", "A279", "A280", "A281", "A282", "A283", "A284", "A285", "A286", "A287", "A288", "A289", "A290", "A291", "A292", "A293", "A294", "A295", "A296", "A297", "A298", "A299", "A36", "A300", "A301", "A302", "A303", "A304", "A304L", "A305", "A306", "A307", "A308", "A309", "A310", "A311", "A312", "A313", "A314", "A314L", "A315", "A316", "A316L", "A317", "A318", "A319", "A320", "A321", "A322", "A323", "A324", "A325", "A326", "A327", "A328", "A329", "A330", "A331", "A332", "A333", "A334", "A335", "A336", "A337", "A338", "A339", "A340", "A341", "A342", "A343", "A344", "A345", "A346", "A347", "A348", "A349", "A350", "A351", "A352", "A353", "A354", "A355", "A356", "A357", "A358", "A381", "A387", "A395", "A403", "A420", "A515", "A516", "A520", "A522", "A53", "A587", "A671", "A694", "A733", "A789", "A790", "A815", "A860", "A865", "ALLOY 20", "API", "API5L", "APT5LX", "B11", "B127", "B152", "B160", "B161", "B162", "B164", "B165", "B166", "B167", "B168", "B209", "B241", "B247", "B265", "B283", "B361", "B363", "B366", "B369", "B377", "B381", "B402", "B407", "B408", "B409", "B42", "B420", "B423", "B462", "B464", "B466", "B467", "B468", "B473", "B564", "B705", "B75", "B861", "B861-05", "B861-14", "B862", "B862-05", "B862-14", "B88", "C276", "D1784", "F1545", "F439", "F441", "F714", "SA182", "SDR11", "SDR9", "YOLOY"]}, "grade": {"default": "Grade", "display": "Grade", "options": ["0", "1", "2", "3", "4", "5", "7", "8", "9", "11", "12", "18.2.2", "18.22.1", "22", "70", "304", "309", "310", "316", "321", "333", "335", "347", "355", "400", "410", "464", "825", "904", "904L", "2205", "23447", "65-45-12", "2H", "2HM", "304/304L", "304L", "310S", "316/316L", "316L", "40S", "410S", "430B", "5L", "7M", "8F", "8M", "II", "A", "API 600", "B", "C", "D", "L", "6", "A36", "B7", "B7M", "B8", "B8-CL2", "B8M", "C12", "C5", "CB60", "CC65", "CC65-CL13", "CD4MCU", "CE8MN", "CH20", "CK20", "CF3", "CF3A", "CF3M", "CF8", "CF8A", "CF8C", "CF8M", "CF8/304L", "F1", "F11", "F11-CL.1", "F11-CL.2", "F12", "F22", "F304", "F304/304L", "F304/F304L", "F304L", "F309", "F310", "F310H", "F316", "F316/316L", "F316/F316L", "F316L", "F321", "F347", "F347H", "F42", "F5", "F50", "F51", "F6", "F60", "F6A", "F7", "F9", "FM", "L2", "L7", "LC3", "LF2", "LF2-CL1", "LF3", "LF9", "P1", "P11", "P12", "P2", "P21", "P22", "P3", "P5", "P7", "P9", "P235", "P235GH", "S31200", "S31260", "S31803", "S32900", "TP304", "TP304/304L", "TP304L", "TP309", "TP310", "TP316", "TP316/316L", "TP316/A182", "TP316L", "TP321", "TP347", "TP347H", "TP405", "TP409", "TP410", "TP430", "UNS.N04400", "WC1", "WC4", "WC5", "WC6", "WC9", "WCB", "WCB/API 600", "WCC", "WP1", "WP11", "WP12", "WP20CB", "WP22", "WP3003", "WP304", "WP304/304L", "WP304/WP304L", "WP304L", "WP304S", "WP304W/304LW", "WP304W/304LWX", "WP304WX", "WP304WX/304LWX", "WP309", "WP310", "WP310S", "WP316", "WP316/316L", "WP316L", "WP316L-S", "WP316L-WX", "WP321", "WP347", "WP347H", "WP5", "WP6061", "WP6063", "WP7", "WP9", "WPA", "WPB", "WPB-M", "WPB-S", "WPB-W", "WPHY-42", "WPHY-52", "WPHY-60", "WPL3", "WPL6", "WPL6-S", "WPL8", "WPL9", "WPN", "WPNC", "WPNCS", "WPNC1", "WPNIC", "WPT2W", "WP-S304/304L", "WP-S316/316L", "WP-W304/304L", "WP-W316/316L", "WP316WX", "WR5", "WR7", "X42", "X52", "X60", "X60MS", "Y53", "0, S235JR", "0, P265GH"]}, "ansme_ansi": {"default": "ANSME/ANSI", "display": "ANSME/ANSI", "options": ["API 600", "API 602", "B16.1", "B16.3", "B16.4", "B16.5", "B16.9", "B16.10", "B16.11", "B16.12", "B16.14", "B16.18", "B16.20", "B16.21", "B16.22", "B16.23", "B16.24", "B16.25", "B16.26", "B16.29", "B16.33", "B16.34", "B16.36", "B16.38", "B16.39", "B16.40", "B16.42", "B16.44", "B16.47", "B16.48", "B16.49", "B16.50", "B16.51", "B16.52", "B18.2.1", "B18.2.2", "B31.1", "B31.3", "B31.4", "B31.5", "B31.10", "B36.10", "B36.10M", "B36.10/19", "B31.11", "B36.19", "B36.19M", "BPE", "MSS SP-43", "MSS SP-44", "MSS SP-75", "MSS SP-79", "MSS SP-80", "MSS SP-83", "MSS SP-84", "MSS SP-85", "MSS SP-86", "MSS SP-87", "MSS SP-95", "MSS SP-96", "MSS SP-97", "MSS SP-114"]}, "material": {"default": "Material", "display": "Material", "options": ["ALUMINUM", "BRONZE", "CARBON STEEL", "CARBON STEEL (LINED)", "CARPENTER 20", "CAST IRON", "CHROME", "COPPER", "CST MI", "CST MI HTDP", "DUCTILE IRON", "DUCTILE IRON (LINED)", "DUPLEX", "FUSION BOND", "GALV.", "HASTELLOY", "HDPE", "INCOLOY", "INCONEL", "LT CARBON STEEL", "MALLEABLE IRON", "MONEL", "NICKEL", "POLYPROPYLENE", "STAINLESS STEEL", "STAINLESS STEEL (LINED)", "TITANIUM", "CPVC", "PVC", "UPVC", "FRP", "FIBERGLASS", "GRP"]}, "abbreviated_material": {"default": "Abbrev. MTRL", "display": "Abbrev. MTRL", "options": ["ALUMINUM", "CU/BRASS", "CS", "CS", "CS", "CS", "CHROME", "CU/BRASS", "CS", "CS", "CS", "CS", "SS", "CS", "CS", "HASTELLOY", "HDPE", "Incoloy", "Inconel", "LTCS", "CS", "SS", "SS", "CS", "SS", "SS", "Titanium", "PVC", "PVC", "PVC", "FRP", "FRP", "FRP"]}, "coating": {"default": "Coating", "display": "Coating", "options": ["ZINC PLATED", "ZINC PLATED - CHROME MOLY ALLOY", "ZINC GLAVANIZED", "GALVANIZED", "FLUOROPOLYMER COATING", "TEFLON", "TEFLON - BLUE", "PTFE", "CADMIUM", "CADMIUM (CAD PLTD)"]}, "forging": {"default": "Forging", "display": "Forging", "options": ["EFW", "ERW", "HFW", "SMLS", "THREADED", "WELDED", "FORGED"]}, "ends": {"default": "Ends", "display": "Ends", "options": ["FLG", "BE", "BE X BE", "BE X SW", "BE X PE", "BE X TE", "SW", "SW X SW", "SW X BE", "SW X PE", "SW X TE", "PE", "PE X PE", "PE X SW", "PE X BE", "PE X TE", "TE", "TE X TE", "TE X SW", "TE X BE", "TE X PE"]}, "item_tag": {"default": "<PERSON><PERSON>", "display": "<PERSON><PERSON>"}, "tie_point": {"default": "Tie Point", "display": "Tie Point"}, "pipe_category": {"default": "Pipe Category", "display": "Pipe Category", "options": ["PIPE", "TUBE", "OTHER PIPE"]}, "valve_type": {"default": "Valve Type", "display": "Valve Type", "options": ["ANGLE VALVE", "BALL VALVE", "BUTTERFLY VALVE", "CHECK VALVE", "CONTROL VALVE", "DIAPH. VALVE", "GATE VALVE", "GLOBE VALVE", "NEEDLE VALVE", "PLUG VALVE", "RELIEF VALVE", "REGULATING VALVE", "STEAM TRAP", "THREE-WAY VALVE", "WAFER CHECK VALVE", "Y-TYPE VALVE", "OTHER VALVE"]}, "fitting_category": {"default": "Fitting Category", "display": "Fitting Category", "options": ["180 RED RETURN LR", "180 RETURN LR", "180 RETURN SR", "45 ELBOW", "45 SR ELBOW", "90 LR ELBOW", "90 LR RED ELBOW", "90 SR ELBOW", "BUSHING", "BUSHING REDUCING", "CAP", "CONICAL STRAINER", "COUPLING", "COUPLING (HALF)", "COUPLING REDUCING", "COUPLING (HALF) REDUCING", "CROSS", "CROSS REDUCING", "ELBOLET BW", "ELBOLET PE", "ELBOLET THRD", "FLANGEOLET", "FLATOLET", "HEX PLUG", "HOSE COUPLING", "LATERAL", "LATERAL REDUCING", "LATROLET BW", "LATROLET PE", "LATROLET THRD", "NIPOLET BW", "NIPOLET PE", "NIPOLET THRD", "NIPPLE", "PIPE BEND", "REDUCER CONCENTRIC", "REDUCER ECCENTRIC", "ROUND PLUG", "SOCKOLET", "SPACER", "STUB END BW", "STUB END SW", "STUB END PE", "STUB END TE", "STUB END FLGD", "SQUARE PLUG", "SWAGE CON. REDUCER", "SWAGE ECC. REDUCER", "SWEEPOLET BW", "SWEEPOLET PE", "SWEEPOLET THRD", "TEE", "TEE REDUCING", "THREADOLET", "UNION", "WELDOLET", "WYE (STANDARD)", "WYE (STANDARD) REDUCING", "WYE (COMPACT)", "WYE (COMPACT) REDUCING", "BASKET STRAINER", "BUCKET STRAINER", "T-STRAINER", "T-STRAINER REDUCING", "Y-STRAINER", "Y-STRAINER REDUCING", "BELLOW", "BELLOW REDUCING", "OTHER STRAINER", "OTHER FITTING", "OTHER FITTING REDUCING", "LJ FLANGE FF", "LJ FLANGE LONG FF", "LJ FLANGE LONG RF", "LJ FLANGE ORIFICE FF", "LJ FLANGE ORIFICE RF", "LJ FLANGE REDUCING FF", "LJ FLANGE REDUCING RF", "LJ FLANGE RF", "LJ FLANGE RING JOINT FF", "LJ FLANGE RING JOINT RF", "SO FLANGE FF", "SO FLANGE LONG FF", "SO FLANGE LONG RF", "SO FLANGE ORIFICE FF", "SO FLANGE ORIFICE RF", "SO FLANGE REDUCING FF", "SO FLANGE REDUCING RF", "SO FLANGE RF", "SO FLANGE RING JOINT FF", "SO FLANGE RING JOINT RF", "THRD FLANGE FF", "THRD FLANGE LONG FF", "THRD FLANGE LONG RF", "THRD FLANGE ORIFICE FF", "THRD FLANGE ORIFICE RF", "THRD FLANGE REDUCING FF", "THRD FLANGE REDUCING RF", "THRD FLANGE RF", "THRD FLANGE RING JOINT FF", "THRD FLANGE RING JOINT RF", "WN FLANGE FF", "WN FLANGE LONG FF", "WN FLANGE LONG RF", "WN FLANGE ORIFICE FF", "WN FLANGE ORIFICE RF", "WN FLANGE REDUCING FF", "WN FLANGE REDUCING RF", "WN FLANGE RF", "WN FLANGE RING JOINT FF", "WN FLANGE RING JOINT RF", "SW FLANGE FF", "SW FLANGE LONG FF", "SW FLANGE LONG RF", "SW FLANGE ORIFICE FF", "SW FLANGE ORIFICE RF", "SW FLANGE REDUCING FF", "SW FLANGE REDUCING RF", "SW FLANGE RF", "SW FLANGE RING JOINT FF", "SW FLANGE RING JOINT RF", "BLIND FLANGE", "BLIND FLANGE - DRILL AND TAP", "BLIND FLANGE - DRILL ONLY", "FIGURE 8", "HAMMER BLIND", "SPADE BLIND", "PADDLE SPACER", "PUDDLE FLANGE", "SPECTACLE BLIND", "PLATE FLANGE", "ORIFICE PLATE", "FLANGE OTHER"]}, "weld_category": {"default": "Weld Category", "display": "Weld Category"}, "bolt_category": {"default": "Bolt Category", "display": "Bolt Category", "options": ["STUD BOLTS", "CAP SCREWS", "HEAVY HEX NUT", "WASHER", "OTHER BOLT"]}, "gasket_category": {"default": "Gasket Category", "display": "Gasket Category", "options": ["GASKET", "INSULATION KIT", "OTHER GASKET"]}}, "rfq_fields2": {"review_explanation": {"default": "Review Explanation", "display": "Review Explanation"}, "answer_explanation": {"default": "Answer Explanation", "display": "Answer Explanation"}, "review": {"default": "Review", "display": "Review"}}}