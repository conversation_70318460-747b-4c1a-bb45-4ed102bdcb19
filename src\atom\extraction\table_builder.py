"""
Utils to build table structure from raw data and coords
"""
import structlog
import re
import polars as pl
from .extractionconfig import RoiExtractionConfig
from .cluster_funcs import infer_possible_columns


def preprocess_compound_entries(df: pl.DataFrame, leftmost_column):
    """
    Preprocesses the dataframe to identify and split compound entries that contain
    both a position number and description text in a single value.

    Args:
        df: The dataframe containing table data
        leftmost_column: x0, y0, x1, y1 coordinates of the leftmost column

    Returns:
        DataFrame with compound entries split into separate rows
    """

    # Add row index for tracking
    if "original_index" in df.columns:
        df = df.drop("original_index")
    df = df.with_row_index("original_index")

    # Find compound entries using vectorized operations
    compound_mask = df.with_columns([
        # Check if value matches the pattern
        pl.col("value").str.extract(r'^(\d{1,3}[a-zA-Z]?)\s+(.*)$', 1).is_not_null().alias("has_pattern"),
        # Check if in leftmost column region
        ((pl.col("x0") >= leftmost_column[0]) & (pl.col("x0") <= leftmost_column[2])).alias("in_leftmost")
    ]).filter(
        pl.col("has_pattern") & pl.col("in_leftmost")
    )

    if len(compound_mask) == 0:
        # No compound entries found, return original dataframe without row index
        return df.drop("original_index")

    # Extract compound entries for processing
    compound_entries = compound_mask.with_columns([
        pl.col("value").str.extract(r'^(\d{1,3}[a-zA-Z]?)\s+(.*)$', 1).alias("position_part"),
        pl.col("value").str.extract(r'^(\d{1,3}[a-zA-Z]?)\s+(.*)$', 2).alias("description_part")
    ])

    # Create position rows
    position_rows = compound_entries.with_columns([
        pl.col("position_part").alias("value"),
        # Calculate position width proportionally
        (pl.col("position_part").str.len_chars() / pl.col("value").str.len_chars() *
         (pl.col("x1") - pl.col("x0"))).alias("pos_width")
    ]).with_columns([
        # Update coordinates2 for position
        pl.concat_list([
            pl.col("x0"),  # x0
            pl.col("y0"),  # y0
            pl.col("x0") + pl.col("pos_width"),  # x1
            pl.col("y1")   # y1
        ]).alias("coordinates2"),
        # Update x1
        (pl.col("x0") + pl.col("pos_width")).alias("x1"),
        # Take first word only
        pl.col("words").list.slice(0, 1).alias("words")
    ]).drop(["position_part", "description_part", "pos_width", "has_pattern", "in_leftmost"])

    position_rows = position_rows.with_columns(
        pl.struct(["value", "coordinates2"]).map_elements(
            lambda row: [{"word": row["value"], "bbox": row["coordinates2"]}]
        ).alias("words")
    )
    # Create description rows
    description_rows = compound_entries.with_columns([
        pl.col("description_part").alias("value"),
        # Calculate position width for offset
        (pl.col("position_part").str.len_chars() / pl.col("value").str.len_chars() *
         (pl.col("x1") - pl.col("x0"))).alias("pos_width")
    ]).with_columns([
        # Update coordinates2 for description
        pl.concat_list([
            pl.col("x0") + pl.col("pos_width"),  # x0
            pl.col("y0"),  # y0
            pl.col("x1"),  # x1
            pl.col("y1")   # y1
        ]).alias("coordinates2"),
        # Update x0
        (pl.col("x0") + pl.col("pos_width")).alias("x0"),
    ]).drop(["position_part", "description_part", "pos_width", "has_pattern", "in_leftmost"])

    description_rows = description_rows.with_columns(
        pl.struct(["value", "coordinates2"]).map_elements(
            lambda row: [{"word": row["value"], "bbox": row["coordinates2"]}]
        ).alias("words")
    )

    # Get indices of compound entries to remove
    compound_indices = compound_mask.select("original_index").to_series()

    # Remove original compound entries
    remaining_df = df.filter(~pl.col("original_index").is_in(compound_indices))

    # Combine all dataframes
    new_rows = pl.concat([position_rows, description_rows], how="vertical")
    new_rows = new_rows.with_columns(pl.col("value").alias("combined_text"))

    result_df = pl.concat([remaining_df, new_rows], how="diagonal")

    # Sort by y0 and x0 to maintain correct order
    result_df = result_df.sort(["y0", "x0"])

    # Debug output
    if len(compound_indices) > 0:
        print(f"Preprocessed {len(compound_indices)} compound entries")

    # Remove the temporary index column
    return result_df.drop("original_index")

def is_pos_contiguous(values):
    """
    Check if numeric sequence is contiguous in the given order,
    allowing repeats (for things like '2a', '2b') but not out-of-order jumps.

    Example:
    ['1','2','3','4'] -> True
    ['1','2','4'] -> False
    ['1','2','2a','3'] -> True
    ['1','2','3','2'] -> False
    """
    def parse_num(val):
        match = re.match(r"(\d+)", str(val))
        return int(match.group(1)) if match else None

    nums = [parse_num(v) for v in values if parse_num(v) is not None]
    if not nums:
        return False

    for i in range(1, len(nums)):
        diff = nums[i] - nums[i-1]
        # Allowed: same number (suffix case) or +1
        if diff not in (0, 1):
            return False
        # Disallow going backwards unless it's a suffix of same number
        if diff < 0:
            return False

    return True

def compute_row_ranges(bom_items: pl.DataFrame, leftmost_column_y1: float, last_row_multiplier: float = 1.5) -> pl.DataFrame:
    """
    Vectorized row range calculation in Polars.

    Args:
        bom_items: DataFrame containing BOM items
        leftmost_column_y1: Y1 coordinate of the leftmost column
        last_row_multiplier: Multiplier for the last row height
    """
    # Shift y0 upward to get "next_y0" as end
    df = bom_items.with_columns([
        pl.col("y0").shift(-1).alias("row_end"),  # Next row's y0 becomes this row's end
        pl.col("value").alias("text"),
        (pl.col("coordinates2").list.get(3) - pl.col("y0")).alias("item_height")
    ])

    df = df.with_columns(
        (pl.col("row_end") - pl.col("y0")).alias("row_height_raw")
    )

    # Calculate average row height (exclude nulls from last row)
    non_null_heights = df["row_height_raw"].drop_nulls()
    avg_height = (
        non_null_heights.mean()
        if len(non_null_heights) > 0
        else df["item_height"].mean() * last_row_multiplier # Fallback is only one row item
    )

    # Last row height rule: start + 1.5 * avg_height
    df = df.with_columns(
        pl.when(pl.col("row_end").is_null())
        .then(
            (pl.col("y0") + avg_height * last_row_multiplier).clip(None, leftmost_column_y1)
        )
        .otherwise(pl.col("row_end"))
        .alias("row_end")
    )

    # Compute row height and row range tuple
    df = df.with_columns([
        (pl.col("row_end") - pl.col("y0")).alias("row_height"),
        pl.col("y0").alias("row_start")
    ])

    return df

def detect_bom_rows(config: RoiExtractionConfig, pdf_page, df, leftmost_column, y0_deviation=1.5, skip_number_check=False):

    logger = structlog.get_logger()
    logger.debug("Detecting BOM rows", pdf_page=pdf_page)

    lc_x0, lc_y0, lc_x1, lc_y1 = leftmost_column

    # Mark items in the leftmost column
    df = df.with_columns(
        pl.when((pl.col("x0") >= lc_x0) & (pl.col("x1") <= lc_x1)).then(True).otherwise(False).alias("leftmost_column")
    )

    # if debug_row_content:
    #     # Export the dataframe for analysis
    #     os.makedirs("debug", exist_ok=True)
    #     df.to_excel(f"debug/detect_bom_row PG {pdf_page}.xlsx")
    #     print(f"\n\nBOM_ROWS: {len(df)}")

    # Debug the leftmost column definition
    # print(f"\n=== DEBUG: LEFTMOST COLUMN DEFINITION ===")
    # print(f"Leftmost column x0: {lc_x0}, x1: {lc_x1}")
    # print(f"Leftmost column y0: {lc_y0}, y1: {lc_y1}")
    # print(f"Y0 deviation parameter: {y0_deviation}")

    red_text_color = [255, 0, 0]
    # df = df.with_columns((pl.col("color") == pl.lit(red_text_color)).alias("is_red_text"))

    # if debug_row_content:
    #     print(f"\n=== DEBUG: LEFTMOST COLUMN FILTERING ===")
    #     print(f"Found {len(left_column_df)} items in the leftmost column")
    #     if len(left_column_df) > 0:
    #         print("Sample items in leftmost column:")
    #         for i, (idx, row) in enumerate(left_column_df.head(5).iterrows()):
    #             print(f"  Item {i+1}: '{row['value']}' at y0={row['y0']:.2f}, y1={row['coordinates2'][3]:.2f}")

    # Mark left most column and valid bom column

    if skip_number_check:
        # Just check if text is non-empty after stripping
        expr = (
            (pl.col("value").is_not_null()) &
            (pl.col("value").str.strip_chars().str.len_chars() > 0)
        )
    else:
        # Validate against number pattern
        expr = (
            (pl.col("value").is_not_null()) &
            (pl.col("value").str.strip_chars().str.len_chars() > 0) &
            (
                pl.col("value")
                .str.split(" ")
                .list.get(0)
                .str.contains(r'^\d{1,3}[a-zA-Z]?$')
            )
        )

    df = df.with_columns(
        pl.when(pl.col("leftmost_column") == True)
        .then(expr)
        .otherwise(None)
        .alias("is_valid_first_column"))

    bom_items = df.filter(pl.col("is_valid_first_column").is_not_null()).sort("y0")
    contiguous = is_pos_contiguous(bom_items["value"].to_list())
    if not contiguous:
        logger.debug("Initial BOM pos contiguous check", outlier="BOM items are not contiguous", pdf_page=pdf_page)
        # Check if additional outliers to be removed from pos column
        column_groups = infer_possible_columns(bom_items)
        if len(column_groups) > 1:
            logger.debug("Multiple column clusters found for leftmost column. Keeping the largest one", pdf_page=pdf_page)
            max_cluster = max(column_groups, key=len)
            bom_items = bom_items[max_cluster]
            bom_items_x0 = bom_items["x0"].min()
            pos_outliers = df.filter(pl.col("x0") < bom_items_x0)
            df = df.filter(pl.col("x0") >= bom_items_x0)
            if not pos_outliers.is_empty():
                logger.debug("Dropped pos outlier items", outlier=f"Values dropped {pos_outliers['value'].to_list()}", outlier_action="Removed", pdf_page=pdf_page)

    # Calculate row ranges and store corresponding text
    row_ranges = compute_row_ranges(bom_items, leftmost_column_y1=lc_y1)

    if len(row_ranges) > 2:
        avg_row_height = row_ranges["row_height"].head(len(row_ranges) - 1).mean()
        std_row_height = row_ranges["row_height"].head(len(row_ranges) - 1).std()
        outlier_threshold = avg_row_height + 2 * std_row_height
    elif len(row_ranges) == 2:
        avg_row_height = row_ranges["row_height"].to_list()[0]
        std_row_height = 0
        outlier_threshold = avg_row_height * 2
    elif len(row_ranges) == 1:
        avg_row_height = row_ranges["row_height"].to_list()[0]
        std_row_height = 0
        outlier_threshold = float('inf')
    else:
        # logger.warning(f"Table has no rows on page {pdf_page + 1}!")
        avg_row_height = 0
        std_row_height = 0
        outlier_threshold = float('inf')

    # Assigned row index
    row_ranges = row_ranges.with_row_index("assigned_row", offset=1)
    row_ranges = row_ranges.with_columns(pl.lit("first_column_item").alias("row_assignment_reason"))

    # Initial row assignments for first column `pos`` items
    df = df.join(row_ranges["row_idx", "assigned_row", "row_assignment_reason"], on="row_idx", how="left")

    def assign_by_row_start():
        nonlocal df

        # Row assignment condition - check y0 is within % of row start
        start_row_threshold = 0.05

        # Cross join to compare every item with every row_range
        joined = df.filter(pl.col("assigned_row").is_null()).drop("assigned_row").join(row_ranges, how="cross")

        joined = joined.with_columns([
            (pl.col("y0") - pl.col("row_start")).abs().alias("abs_dist_to_row_start"),
            (pl.col("row_height") * start_row_threshold).alias("close_threshold")
        ])

        # Filter where condition matches
        matched = joined.filter(pl.col("abs_dist_to_row_start") < pl.col("close_threshold"))

        best_matches = (
            matched
            .sort(["row_idx", "abs_dist_to_row_start"])
            .group_by("row_idx")
            .agg([
                pl.first("assigned_row").alias("assigned_row")
            ])
        )

        df = df.join(best_matches, on="row_idx", how="left", suffix="_update")

        # Update row assignments and reason for new assignments
        df = df.with_columns(
            pl.coalesce([pl.col("assigned_row_update"), pl.col("assigned_row")]).alias("assigned_row")
        ).drop("assigned_row_update")

        df = df.with_columns(
            pl.when(
                ((pl.col("assigned_row").is_not_null()) & (pl.col("row_assignment_reason").is_null()))
            )
            .then(pl.lit("y0 near row_start"))
            .otherwise(pl.col("row_assignment_reason"))
            .alias("row_assignment_reason")
        )

    def assign_by_row_overlap():
        nonlocal df

        # Try to assign unassigned items by overlaps
        joined = df.filter(pl.col("assigned_row").is_null()).drop("assigned_row").join(row_ranges, how="cross")

        joined = joined.with_columns([
            pl.min_horizontal(["y1", "row_end"]).alias("overlap_end"),
            pl.max_horizontal(["y0", "row_start"]).alias("overlap_start"),
        ])

        # Valid if end > start
        joined = joined.with_columns([
            (pl.col("overlap_end") - pl.col("overlap_start")).alias("overlap_height"),
            (pl.col("y1") - pl.col("y0")).alias("text_height"),
            (pl.col("y0") - pl.col("row_start")).alias("distance_from_start"),
        ])

        # Only where there's real overlap
        joined = joined.filter(pl.col("overlap_height") > 0)

        # Compute percentage overlap
        joined = joined.with_columns([
            (pl.col("overlap_height") / pl.col("text_height")).alias("overlap_pct")
        ])

        joined = joined.filter(pl.col("overlap_pct") >= 0.65)

        best_matches = (
            joined
            .sort(["row_idx", "overlap_pct", "distance_from_start"], descending=[False, True, False])
            .group_by("row_idx")
            .agg(pl.first("assigned_row").alias("assigned_row"))
        )

        df = df.join(best_matches, on="row_idx", how="left", suffix="_update")

        df = df.with_columns(
            pl.coalesce([pl.col("assigned_row_update"), pl.col("assigned_row")]).alias("assigned_row")
        ).drop("assigned_row_update")

        # Optionally update 'assigned_row' if it is still null
        df = df.with_columns(
            pl.when(
                ((pl.col("assigned_row").is_not_null()) & (pl.col("row_assignment_reason").is_null()))
            )
            .then(pl.lit("row_overlap"))
            .otherwise(pl.col("row_assignment_reason"))
            .alias("row_assignment_reason")
        )

    def assign_by_row_vcenter():
        """If vertical center line inside a row range"""
        nonlocal df

        # Try to assign unassigned items by overlaps
        joined = df.filter(pl.col("assigned_row").is_null()).drop("assigned_row").join(row_ranges, how="cross")

        joined = joined.with_columns([
            (pl.col("y0") + (pl.col("y1") - pl.col("y0")).abs() / 2).alias("y_center"),
            (pl.col("row_start") + (pl.col("row_end") - pl.col("row_start")).abs() / 2).alias("row_center"),
        ])

        joined = joined.with_columns([
            (pl.col("y_center") - pl.col("row_center")).abs().alias("abs_dist_to_row_center"),
            (
                (pl.col("y_center") > pl.col("row_start")) &
                (pl.col("y_center") < pl.col("row_end"))
            ).alias("v_center_within"),
        ])

        # Filter where condition matches
        matched = joined.filter(pl.col("v_center_within") == True)

        best_matches = (
            matched
            .sort(["row_idx", "abs_dist_to_row_center"])
            .group_by("row_idx")
            .agg([
                pl.first("assigned_row").alias("assigned_row")
            ])
        )

        df = df.join(best_matches, on="row_idx", how="left", suffix="_update")

        # Update row assignments and reason for new assignments
        df = df.with_columns(
            pl.coalesce([pl.col("assigned_row_update"), pl.col("assigned_row")]).alias("assigned_row")
        ).drop("assigned_row_update")

        df = df.with_columns(
            pl.when(
                ((pl.col("assigned_row").is_not_null()) & (pl.col("row_assignment_reason").is_null()))
            )
            .then(pl.lit("y_center within row_start and row_end"))
            .otherwise(pl.col("row_assignment_reason"))
            .alias("row_assignment_reason")
        )

    assign_by_row_start()
    assign_by_row_overlap()
    assign_by_row_vcenter()

    # TODO
    # assign if y center within row start and end
    # check red text

    # handle unassigned items
    return df

    # Function to assign row based on coordinates
    def assign_row(y0, y1, color, text):

        # Check vertical center line to assign row - but be more strict
        y_center = y0 + ((y1 - y0) / 2)

        # If no significant overlap, use center line with stricter bounds
        for i, (start, end) in enumerate(row_ranges):
            # Use the first 75% of the row height for center line check
            adjusted_end = start + ((end - start) * 0.75)
            if start <= y_center <= adjusted_end:
                if debug_row_assignment:
                    print(f"  ASSIGNED TO ROW {i} by strict center line check")
                return i

        # Alternative - Assign if both y0 and y1 are within row height range
        for i, (start, end) in enumerate(row_ranges):
            if start <= y0 <= end and start <= y1 <= end:
                if debug_row_assignment:
                    print(f"  ASSIGNED TO ROW {i} by full containment check")
                return i

        # If all other checks fail, check for red text (Could be Revisions, which can be off center)
        if is_red_text(color):
            distances = []
            for i, (start, end) in enumerate(row_ranges):
                mid = (start + end) / 2
                distance_y0 = abs(y0 - mid)
                distance_y1 = abs(y1 - mid)
                distances.append(min(distance_y0, distance_y1))
            best_row = np.argmin(distances)
            if debug_row_assignment:
                print(f"  ASSIGNED TO ROW {best_row} by red text check")
            return best_row

        # If nothing worked, return None
        if debug_row_assignment:
            print(f"  COULD NOT ASSIGN TO ANY ROW")

        return None

    # Assign rows to all items
    row_assignments = [-1] * len(df)  # Initialize all assignments to -1
    unassigned_items = [] # Items that could not be assigned but likely should have been
    skipped_items = [] # Intentionally skipped
    skip_processing = False
    last_valid_row = -1
    last_row_items = []
    buffer_items = [] # Holds items for use in loop. Accounts for minor deviations in y0 to ensure we do not permanently discard valid items in 'skip_processing'

    print(bom_items)
    # exit()
    for i, (index, row) in enumerate(df.iterrows()):

        y0, y1 = row['coordinates2'][1], row['coordinates2'][3]
        color = row['color']
        # text = row['Text']
        text = str(row['value'])
        text_type = str(row['type'])

        # TODO - Check if we need to start skipping - Should be handled by pre-filter
        # if any(skip_word in text for skip_word in skip_words):
        #     skip_processing = True
        #     skipped_items.append((i, text, row['coordinates2'], "Contains skip word"))
        #     continue

        # Check if we need to stop skipping
        if skip_processing and is_valid_bom_item(text, text_type) and is_in_leftmost_column(row['x0'], row['y0'], leftmost_column):
            skip_processing = False

            # Process buffered items
            for buffered_item in buffer_items:
                if abs(buffered_item[1] - y0) <= y0_deviation:
                    assigned_row = assign_row(buffered_item[1], buffered_item[2], buffered_item[3], buffered_item[4])
                    if assigned_row is not None:
                        row_assignments[buffered_item[0]] = assigned_row
                    else:
                        unassigned_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Could not be assigned to a row after buffer processing"))
            buffer_items.clear()

        if skip_processing:
            buffer_items.append((i, y0, y1, color, text))
            # skipped_items.append((i, text, df.iloc[i]['Coordinates'], "In skip processing mode"))
            continue

        assigned_row = assign_row(y0, y1, color, text)

        if assigned_row is not None:
            # row_assignments.append(assigned_row)
            row_assignments[i] = assigned_row
            last_valid_row = assigned_row

            # Store items for the last row
            if assigned_row == len(row_ranges) - 1:
                last_row_items.append((i, y0, y1, text))
        else:
            unassigned_items.append((index,text, row['coordinates2'], "Could not be assigned to a row"))
            row_assignments[i] = last_valid_row  # Assign to the last valid row instead of -1

    # Process any remaining buffered items
    for buffered_item in buffer_items:
        skipped_items.append((buffered_item[0], buffered_item[4], (buffered_item[1], buffered_item[2]), "Remained in buffer after processing"))

    # Process the last row items for outliers
    if last_row_items:
        last_row_start, last_row_end = row_ranges[-1]
        last_row_height = last_row_end - last_row_start

        if last_row_height > outlier_threshold:
            #print(f"\nLast row is an outlier. Removing trash data:")
            valid_last_row_items = []
            for i, y0, y1, text in last_row_items:
                if y0 - last_row_start > outlier_threshold:
                    #print(f"Removing: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")
                    #unassigned_items.append((i, text, df.iloc[i]['Coordinates']))
                    skipped_items.append((i, text, df.iloc[i]['coordinates2'], "Greater than outlier threshold"))
                    row_assignments[i] = -1
                else:
                    valid_last_row_items.append((i, y0, y1, text))
                    #print(f"Keeping: Index: {i}, Y0: {y0}, Y1: {y1}, Text: '{text}'")

            # Update the last row range with the valid items
            if valid_last_row_items:
                new_last_row_end = max(item[2] for item in valid_last_row_items)
                row_ranges[-1] = (last_row_start, new_last_row_end)
                #print(f"\nUpdated last row range: {row_ranges[-1]}")
            else:
                print("\n--> Warning: All items in the last row were removed as trash.")
        # else:
        #     print("\nLast row is not an outlier. Keeping all items.")

    # df['assigned_row'] = row_assignments
    df = df.copy()
    df.loc[:, 'assigned_row'] = row_assignments

    # Create filtered contents based on row assignments
    filtered_contents = [df[df['assigned_row'] == i] for i in range(len(row_ranges))]

    # Debug information
    if debug_row_content:
        print("\nFinal Row Ranges: - Line 1593")
        for i, (start, end) in enumerate(row_ranges):
            print(f"Row {i}: {start} - {end} (height: {end - start})")

        print(f"Average row height: {avg_row_height}")
        print(f"Row height standard deviation: {std_row_height}")
        print(f"Outlier threshold: {outlier_threshold}")

    if debug_discarded:
        print("\nIntentionally Skipped Items:")
        if skipped_items:  # Only print if there are skipped items
            for item in skipped_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

        if unassigned_items:  # Only print if there are unassigned items
            print("\nUnassigned Items:")
            for item in unassigned_items:
                print(f"Index: {item[0]}, Text: '{item[1]}', Coordinates: {item[2]}, Reason: {item[3]}")

    if debug_mode:
        try:
            print(f"\n\n-------\nReturning {i} Rows for page {page_num}\n")
        except Exception as e:
            logger.info(f"Returning {i} Rows for page ERROR GETTING PAGE NUM: {e}")

    return row_ranges, row_texts, filtered_contents, unassigned_items, skipped_items

def merge_wrapped_rows_text(structured_data, annot_table=False):
    #Convert Data to a Dataframe
    if annot_table: #Return the structured dataframe
        return pd.DataFrame(structured_data)

    structured_df = pd.DataFrame(structured_data)

    if debug_row_merging:
        print("\n=== DEBUG: ROW MERGING STARTED ===")
        print(f"Initial DataFrame has {len(structured_df)} rows")
        print("Initial DataFrame structure:")
        for i, row in structured_df.iterrows():
            print(f"Row {i}:")
            for col in structured_df.columns:
                value = row[col]
                if isinstance(value, str) and len(value) > 50:
                    value = value[:50] + "..."
                print(f"  {col}: {value}")

    # Check if DataFrame is empty
    if len(structured_df) == 0:
        if debug_row_merging:
            print("DataFrame is empty, returning empty DataFrame")
        return structured_df

    # The first column is considered the reference for merging
    try:
        leftmost_column = structured_df.columns[0]
        if debug_row_merging:
            print(f"\nUsing '{leftmost_column}' as the reference column for merging")
    except Exception as e:
        logger.error(f"Could not get left most columns. {e}")
        if debug_row_merging:
            print(f"ERROR: Could not get leftmost column: {e}")
        #structured_df.to_excel(f"Structured_df_merge_wrapped.xlsx")
        return structured_df

    # Iterate backwards through the DataFrame to merge rows
    rows_merged = 0
    for i in range(len(structured_df) - 1, 0, -1):
        # If the leftmost column is empty, merge this row with the one above
        if pd.isna(structured_df.at[i, leftmost_column]) or structured_df.at[i, leftmost_column].strip() == '':
            if debug_row_merging:
                print(f"\n--- Merging row {i} into row {i-1} ---")
                print(f"Row {i} has empty leftmost column value: '{structured_df.at[i, leftmost_column]}'")

            for col in structured_df.columns:
                # Skip the 'material_scope' column entirely
                if col not in ['material_scope', 'has_revision']:
                    # Only merge if the current row's cell is not empty
                    if not pd.isna(structured_df.at[i, col]) and structured_df.at[i, col].strip() != '':
                        if debug_row_merging:
                            print(f"  Merging column '{col}':")
                            print(f"    Before: '{structured_df.at[i-1, col]}'")
                            print(f"    Adding: '{structured_df.at[i, col]}'")

                        structured_df.at[i - 1, col] = structured_df.at[i - 1, col].strip() + ' ' + structured_df.at[i, col].strip()

                        if debug_row_merging:
                            print(f"    After: '{structured_df.at[i-1, col]}'")

            # After merging, drop the current row
            structured_df = structured_df.drop(index=i)
            rows_merged += 1

    # Reset index after dropping rows
    structured_df = structured_df.reset_index(drop=True)

    if debug_row_merging:
        print(f"\n=== ROW MERGING COMPLETED ===")
        print(f"Merged {rows_merged} rows")
        print(f"Final DataFrame has {len(structured_df)} rows")
        if rows_merged > 0:
            print("Final DataFrame structure:")
            for i, row in structured_df.iterrows():
                print(f"Row {i}:")
                for col in structured_df.columns:
                    value = row[col]
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {col}: {value}")

    #export_large_data_to_excel(structured_df,"merged_wrapped_df_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
    return structured_df

def assign_texts_to_columns(pdf_page: int,
                            words_df: pl.DataFrame,
                            column_coords: list[dict],
                            column_names: list[str],
                            table_type: str = None,
                            previous_row=None):
    """
    Handle text and annotation data separately then join after.
    Need to identify row types (Text, Annot, Text w/Rev Markup)

    # Function originally per row
    """
    logger = structlog.get_logger()
    logger.info("Assigning texts to columns", pdf_page=pdf_page, table_type=table_type)

    # Add center y and center x
    words_df = words_df.with_columns([
        ((pl.col("x0") + pl.col("x1")) / 2).alias("center_x"),
        ((pl.col("y0") + pl.col("y1")) / 2).alias("center_y")
    ])

    words_df = words_df.with_columns(
        pl.lit(None).alias("assigned_column"),
        pl.lit(None).alias("column_assignment_reason"),
    )

    # Priority assign words which are within the x0 and x1 coordinates
    # of a column bounds
    for name, coord in zip(column_names, column_coords):
        logger.info(f"Assigning column {name} to words - coords: {coord}", pdf_page=pdf_page, table_type=table_type)
        x0, x1 = coord[0], coord[2]
        words_df = words_df.with_columns(
            pl.when((pl.col("center_x") >= x0) & (pl.col("center_x") <= x1))
            .then(pl.lit(name))
            .otherwise(pl.col("assigned_column"))
            .alias("assigned_column")
        )
        words_df = words_df.with_columns(
            pl.when((pl.col("assigned_column").is_not_null() & pl.col("column_assignment_reason").is_null()))
            .then(pl.lit("within_x0_x1"))
            .otherwise(pl.col("column_assignment_reason"))
            .alias("column_assignment_reason")
        )

    # words_df.write_excel("debug/table_assignments.xlsx")
    # exit()

    grouped_agg_expr = [
        pl.col("word").str.strip_chars().alias("stripped_words"),
        pl.col("center_y").mean().alias("avg_y"),
    ]
    if table_type.lower() == "bom":
        grouped_agg_expr.append(pl.col("material_scope").first().alias("material_scope"))
        grouped_agg_expr.append(pl.col("component_category").first().alias("component_category"))

    # Group by row and column, sort within each group, and join the text
    grouped = (
        words_df
        .filter(pl.col("assigned_row").is_not_null() & pl.col("assigned_column").is_not_null())
        .sort(["assigned_column", "assigned_row", "center_y", "center_x"])
        .group_by(["assigned_row", "assigned_column"])
        .agg(grouped_agg_expr)
        .with_columns([
            pl.col("stripped_words").list.join(" ").alias("combined_words"),
        ])
    )

    row_order = (grouped.group_by("assigned_row").agg(pl.col("avg_y").mean().alias("row_y")))

    # Pivot to make each assigned_column a key in the record
    select_columns = ["assigned_row", "assigned_column", "combined_words"]
    if table_type.lower() == "bom":
        select_columns.append("material_scope")
        select_columns.append("component_category")

    pivoted = (
        grouped.select(select_columns)
        .pivot(
            values="combined_words",
            index="assigned_row",
            columns="assigned_column"
        )
    )

    # Join with row_order to sort top-to-bottom
    pivoted = pivoted.join(row_order, on="assigned_row").sort("row_y").drop("row_y")

    if table_type.lower() == "bom":
        # Map material scope to each row
        row_material_scope = (grouped.select(["assigned_row", "material_scope"]).unique())
        pivoted = pivoted.join(row_material_scope, on="assigned_row", how="left")
        # Map component category to each row
        row_category = (grouped.select(["assigned_row", "component_category"]).unique())
        pivoted = pivoted.join(row_category, on="assigned_row", how="left")

    return pivoted

    # TODO - assign material scope and component category

    row_structure = {col: [] for col in column_names}  # Use lists instead of strings
    has_revision = None
    row_colors = {col: [] for col in column_names}  # To keep track of font colors
    row_material_scope = None
    row_component_category = None
    leftmost_column = column_names[0]  # Assuming the first column is the leftmost

    # Debug column info
    print("\n=== DEBUG: Column Coordinates ===")
    for i, (col_name, col_info) in enumerate(zip(column_names, column_coords)):
        print(f"Column {i}: {col_name} - x0: {col_info['x0']}, x1: {col_info['x1']}")

    # Sort text items by y-coordinate (top to bottom) and then x-coordinate (left to right)
    sorted_texts = sorted(words_df, key=lambda item: (item['bbox'][1], item['bbox'][0]))

    # STEP 1: Extract and validate all words from all text items
    all_words = []
    text_already_processed = set()  # Track which text has been processed

    print("\n=== STEP 1: Extract Words ===")

    for text_item in sorted_texts:
        # Get the text item's center coordinates
        text_center_x = (text_item['bbox'][0] + text_item['bbox'][2]) / 2
        text_center_y = (text_item['bbox'][1] + text_item['bbox'][3]) / 2

        # Create unique ID to avoid duplicates
        text_id = f"{text_center_x:.1f}_{text_center_y:.1f}"

        if text_id in text_already_processed:
            print(f"Skipping duplicate text at position {text_id}")
            continue

        text_already_processed.add(text_id)

        text = str(text_item['text']).strip()
        color = text_item['color']

        region = [
            text_item['bbox'][0],
            text_item['bbox'][1],
            text_item['bbox'][2]-text_item['bbox'][0],
            text_item['bbox'][3]-text_item['bbox'][1]
        ]
        print(text, region)
        print(region)
        print(text_center_x, text_center_y)

        # Get material scope and category
        if table_type == 'bom':
            material_scope = text_item.get('material_scope', '')
            if material_scope and not row_material_scope:
                row_material_scope = material_scope

            component_category = text_item.get('componentCategory', '')
            if component_category and not row_component_category:
                row_component_category = component_category

        # Get and parse 'words' from the text item
        try:
            words_raw = text_item.get('words', [])
            parsed_words = []

            # Parse string representation if needed
            if isinstance(words_raw, str) and words_raw.startswith('[') and words_raw.endswith(']'):
                import ast
                try:
                    parsed_words = ast.literal_eval(words_raw)
                except Exception as e:
                    print(f"Error parsing words string: {e}")
                    parsed_words = []
            # Handle nested string representation
            elif isinstance(words_raw, list) and len(words_raw) == 1 and isinstance(words_raw[0], dict) and 'text' in words_raw[0]:
                text_content = words_raw[0]['text']
                if isinstance(text_content, str) and text_content.startswith('[') and text_content.endswith(']'):
                    import ast
                    try:
                        nested_words = ast.literal_eval(text_content)
                        if isinstance(nested_words, list):
                            parsed_words = nested_words
                    except Exception as e:
                        print(f"Error parsing nested words: {e}")
                        parsed_words = words_raw
                else:
                    parsed_words = words_raw
            else:
                parsed_words = words_raw

            # For text entries with no words, create a single word entry
            if not parsed_words:
                word_entry = {
                    'text': text,
                    'bbox': text_item['bbox'],
                    'x_center': text_center_x,
                    'y_center': text_center_y,
                    'color': color,
                    'original_text': text  # Keep track of the original text this came from
                }
                all_words.append(word_entry)
            else:
                # Process each word entry
                for word_info in parsed_words:
                    if not isinstance(word_info, dict):
                        continue

                    # Extract word text and bbox
                    word_text = word_info.get('word', word_info.get('text', ''))
                    word_bbox = word_info.get('bbox', text_item['bbox'])

                    if not word_text or not isinstance(word_bbox, (list, tuple)) or len(word_bbox) != 4:
                        continue

                    # Add word to our collection
                    word_entry = {
                        'text': word_text,
                        'bbox': word_bbox,
                        'x_center': (word_bbox[0] + word_bbox[2]) / 2,
                        'y_center': (word_bbox[1] + word_bbox[3]) / 2,
                        'color': color,
                        'original_text': text  # Keep track of the original text this came from
                    }
                    all_words.append(word_entry)
        except Exception as e:
            print(f"Error processing words: {e}")
            # Fallback to using the whole text
            word_entry = {
                'text': text,
                'bbox': text_item['bbox'],
                'x_center': text_center_x,
                'y_center': text_center_y,
                'color': color,
                'original_text': text
            }
            all_words.append(word_entry)

    # STEP 2: Sort all words by Y position (rows) and then X position (reading order)
    # First, group words with similar y_center values

    # Group words by similar y_center values
    y_groups = {}
    for word in all_words:
        y_center = word['y_center']
        assigned_to_group = False

        # Try to find an existing group this word belongs to
        for group_y, group_words in y_groups.items():
            if abs(y_center - group_y) <= y_tolerance:
                group_words.append(word)
                assigned_to_group = True
                break

        # If not assigned to any existing group, create a new group
        if not assigned_to_group:
            y_groups[y_center] = [word]

    # Update y_center values within each group to be the same
    # Use the maximum y0 (top) value for better alignment with actual text
    aligned_words = []
    for group_y, group_words in y_groups.items():
        # Calculate the average y-center for the group
        avg_y = sum(word['y_center'] for word in group_words) / len(group_words)

        # Find any red text in the group (if it exists)
        red_words = [w for w in group_words if w.get('color') == (255, 0, 0) or w.get('color') == [255, 0, 0]]

        # If red text exists, prioritize its y_center
        if red_words:
            red_avg_y = sum(word['y_center'] for word in red_words) / len(red_words)
            # Use red text y_center if it's significantly different
            if abs(red_avg_y - avg_y) > 2.0:
                avg_y = red_avg_y

        # Update all words in the group with the same y_center
        for word in group_words:
            word_copy = word.copy()
            word_copy['original_y_center'] = word['y_center']  # Keep original for reference
            word_copy['y_center'] = avg_y
            aligned_words.append(word_copy)

    # Sort the aligned words by y_center and then x_center
    aligned_words.sort(key=lambda w: (w['y_center'], w['x_center']))

    # Replace the original all_words with the aligned version
    all_words = aligned_words

    print(f"\n=== STEP 2: Sorted {len(all_words)} words by position after y-alignment ===")

    # STEP 3: Assign each word to a column based on its center X position
    print("\n=== STEP 3: Assign words to columns ===")

    for word in all_words:
        x_center = word['x_center']
        word_text = word['text']
        color = word['color']

        # Find which column this word belongs to
        assigned_col = None
        for col_name, col_info in zip(column_names, column_coords):
            if col_info['x0'] <= x_center <= col_info['x1']:
                assigned_col = col_name
                break

        # If not found, find the closest column
        if not assigned_col:
            closest_col = min(column_coords, key=lambda c: min(abs(x_center - c['x0']), abs(x_center - c['x1'])))
            assigned_col = closest_col.get('columnName', column_names[0])  # Fallback to first column if columnName is missing

        # Check if this word's original text is already in this column
        # This helps prevent duplicate position numbers, etc.
        original_text = word['original_text']
        is_duplicate = False

        for existing_text in row_structure[assigned_col]:
            if existing_text == word_text or existing_text == original_text:
                # is_duplicate = True
                break

        if not is_duplicate:
            # Special handling for "pos" column to prevent duplicates like "6 6"
            if assigned_col == 'pos' and row_structure[assigned_col] and word_text in row_structure[assigned_col][0]:
                continue

            # Special handling for red text
            if color == (255, 0, 0):# and word_text not in ["1", "2", "3"]:
                has_revision = True
                if assigned_col == "material_description" or row_structure[assigned_col]:
                    # For material_description or if the column already has content
                    if row_structure[assigned_col] and row_colors[assigned_col] and row_colors[assigned_col][-1] == (255, 0, 0):
                        # If last text was red, append
                        row_structure[assigned_col][-1] += f" {word_text}"
                    else:
                        # If last text wasn't red or column is empty, replace
                        row_structure[assigned_col] = [word_text]
                        row_colors[assigned_col] = [color]
                else:
                    # For other columns or if it's the first entry, replace
                    row_structure[assigned_col] = [word_text]
                    row_colors[assigned_col] = [color]
            else:
                # For black text, append only if the last entry wasn't red
                if not row_structure[assigned_col] or row_colors[assigned_col][-1] != (255, 0, 0):
                    row_structure[assigned_col].append(word_text)
                    row_colors[assigned_col].append(color)

            print(f"Assigned '{word_text}' to column '{assigned_col}'")

        else:
            pass

    # STEP 4: Join text in each column
    print("\n=== STEP 4: Join text in each column ===")

    for col in row_structure:
        row_structure[col] = ' '.join(row_structure[col])
        print(f"Final column {col}: {row_structure[col][:50]}{'...' if len(row_structure[col]) > 50 else ''}")

    # Add material scope and component category
    if table_type == 'bom':
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''
        row_structure['componentCategory'] = row_component_category if row_component_category else ''

    # Check for duplicates with previous row
    if previous_row is not None:
        if all(row_structure[col].strip() == previous_row[col].strip() for col in row_structure if col in previous_row):
            print(f"Duplicate row detected: {row_structure}")
            return None  # Return None for duplicate rows

    # Final output for debugging
    print("\n=== Final row structure ===")
    for col, value in row_structure.items():
        try:
            print(f"{col}: {value[:50]}{'...' if len(value) > 50 else ''}")
        except Exception as e:
            print(f"Error printing column {col}, value: {value}, error: {e}")
    print()

    row_structure["has_revision"] = has_revision
    return row_structure

def create_logical_table_structure(config: RoiExtractionConfig,
                                   pdf_page: int,
                                   raw_table_df: pl.DataFrame,
                                   column_coords: dict,
                                   headers_selected=True,
                                   table_type=None) -> pl.DataFrame:

    logger = structlog.get_logger()
    column_names, column_coords = zip(*sorted(column_coords.items(), key=lambda item: item[1][0]))
    column_names, column_coords = list(column_names), list(column_coords)

    raw_table_df = raw_table_df.clone()

    if "words" not in raw_table_df.columns:
        raw_table_df = raw_table_df.with_columns(pl.lit(None).alias("words"))

    structured_data = []

    raw_table_df = raw_table_df.sort(["y0", "x0"])
    raw_table_df = raw_table_df.with_row_index(name="original_index")
    leftmost_column = column_coords[0]

    filter_cond = (
        (pl.col("annot_type") != "") &
        (pl.col("combined_text").str.contains("  "))
    )

    filtered = raw_table_df.filter(filter_cond) # Annots which contain multiple spaces
    unfiltered = raw_table_df.filter(~filter_cond)

    if not filtered.is_empty():
        logger.info("Splitting annotations into individual bboxes", table_type=table_type, pdf_page=pdf_page)

        # Adjust into individual bboxes
        phrase_bboxes = []

        def split_phrases(combined_text, rect, original_index):
            content = combined_text.strip()

            if not content:
                return

            rect_width = rect[2] - rect[0]
            rect_height = rect[3] - rect[1]

            lines = content.splitlines()
            num_lines = len(lines)
            max_line_length = max((len(line) for line in lines), default=1)

            char_width = rect_width / max_line_length
            line_height = rect_height / num_lines

            split_bboxes = []

            for i, line in enumerate(lines):
                phrases = re.split(r'\s{2,}', line)
                cursor = 0

                for phrase in phrases:
                    start_idx = line.find(phrase, cursor)
                    end_idx = start_idx + len(phrase)

                    x0 = rect[0] + start_idx * char_width
                    x1 = rect[0] + end_idx * char_width
                    y0 = rect[1] + i * line_height
                    y1 = y0 + line_height

                    bbox = [x0, y0, x1, y1]
                    split_bboxes.append([phrase, bbox, original_index])
                    cursor = end_idx

            return split_bboxes

        annot_records = filtered.select(["combined_text", "coordinates2", "original_index"]).to_dicts()
        for record in annot_records:
            phrases = split_phrases(record["combined_text"], record["coordinates2"], record["original_index"])
            phrase_bboxes.extend(phrases)

        phrase_bboxes_df = pl.DataFrame(phrase_bboxes, schema=[("combined_text", pl.String), ("coordinates2", pl.List(pl.Float64)), ("original_index", pl.Int64)])

        # join
        phrase_bboxes_df = phrase_bboxes_df.join(filtered.drop(["combined_text", "coordinates2", "words"]), on=["original_index"], how="left")
        phrase_bboxes_df = phrase_bboxes_df.with_columns(pl.col("combined_text").alias("value"))

        # Update x0, y0, x1, y1
        phrase_bboxes_df = phrase_bboxes_df.with_columns(
            pl.col("coordinates2").list.get(0).alias("x0"),
            pl.col("coordinates2").list.get(1).alias("y0"),
            pl.col("coordinates2").list.get(2).alias("x1"),
            pl.col("coordinates2").list.get(3).alias("y1")
        )

        phrase_bboxes_df = phrase_bboxes_df.with_columns(
            pl.struct(["value", "coordinates2"]).map_elements(
                lambda row: [{"word": row["value"], "bbox": row["coordinates2"]}]
            ).alias("words")
        )

        unfiltered = unfiltered.drop("original_index")

        # Concat with unfiltered
        phrase_bboxes_df = phrase_bboxes_df.select(unfiltered.columns).vstack(unfiltered)

        raw_table_df = phrase_bboxes_df.sort(["y0", "x0"])

    # Add this line to preprocess and handle compound entries (pos (row numbers) merged with
    # neighboring columns causes rows not to be wrapped. Ex. "10 Some Material Description.")
    if config.bom_split_compound_entries and table_type.lower() == "bom":
        raw_table_df = preprocess_compound_entries(raw_table_df, leftmost_column)

    detected_rows_df = detect_bom_rows(config, pdf_page, raw_table_df, leftmost_column, skip_number_check=False)

    # Assign texts to columns
    assigned_rows_df = detected_rows_df.filter(pl.col("assigned_row").is_not_null())
    unassigned_rows_df = detected_rows_df.filter(pl.col("assigned_row").is_null())

    # assigned_rows_df = assigned_rows_df.with_columns(pl.col("words").list.len().alias("word_count"))

    # TODO ! fill empty words (e.g. annots) with value and coordinates2. As they are discarded in explode

    if assigned_rows_df.filter(pl.col("words").is_not_null()).is_empty():
        return pl.DataFrame([{"pdf_page": pdf_page, "error": "No words found in assigned rows"}])

    # Explode words column into separate rows and unnest the dicts into separate columns
    # words_df = assigned_rows_df.filter(pl.col("words").is_not_null()).explode("words").unnest("words")
    words_df = assigned_rows_df.explode("words").unnest("words")

    words_df = words_df.with_columns(
        pl.col("bbox").cast(pl.Array(pl.Float64, 4)).alias("bbox")
    )

    # Update the rect coordinates from individual bbox
    words_df = words_df.with_columns(
        pl.col("bbox").arr.get(0).alias("x0"),
        pl.col("bbox").arr.get(1).alias("y0"),
        pl.col("bbox").arr.get(2).alias("x1"),
        pl.col("bbox").arr.get(3).alias("y1")
    )

    structured_table_df = assign_texts_to_columns(pdf_page, words_df, column_coords, column_names, table_type=table_type)

    if config.bom_merge_wrapped_rows:
        logger.warning("Merging wrapped rows enabled but not implemented", table_type=table_type, pdf_page=pdf_page)
        structured_table_df = structured_table_df #merge_wrapped_rows(structured_table_df)

    return structured_table_df

    for content in filtered_contents:
        row_texts = []
        for _, item in content.iterrows():
            # text = item['Contents'] if annot_table else item['Text']
            text = item['value']
            # color = item['color']
            coords = item['coordinates2']
            words = item.get('words', None)
            bbox = fitz.Rect(coords)

            try:
                color = item['color']
            except Exception as e:
                try:
                    color = item.get('Color', None)
                    if color is None:
                        logger.warning(f"Unable to get color value for row at item {row.name}: {e}")
                except Exception as e2:
                    logger.error(f"Critical error getting color for row at item {row.name}: {e2}")
                    color = None

                logger.info(f"Color Item Value: {item}")

            # print(f"\n\nCONTENT: {content}")

            row_data = {
                'text': text,
                'bbox': bbox,
                'color': color,
                'words': words,
                'material_scope': item.get('Material Scope', ''),
                'componentCategory': item.get('componentCategory', ''),
                'type': item.get('type', '')
            }
            row_texts.append(row_data)

        # file = pd.DataFrame(row_texts)
        # file.to_excel(f'debug/raw_table_df{index}.xlsx')

        # print(f"\n\n----------DEBUG ROW TEXTS (create_logical_structure_text): \n{row_texts}----------------\n\n")
        structured_data.append(assign_texts_to_columns(row_texts, column_coords, column_names, table_type))

        # print(f"\n\nStructured data after assign texts to columns (filtered_contents):\n\n")
        # structured_df_debug = pd.DataFrame(structured_data)
        # pp(structured_data)

    # if debug_mode:
    #     structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    # if debug_mode:
    #     structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))

    # print(f"\n\nDEBUG: Structured DF Rows Before Return: {len(structured_df)}")
    # if debug_row_content:
    #     print(f"\n\nLENTH OF STRUCTURE DF!: \n{len(structured_df)}\n\n")

    return structured_df
