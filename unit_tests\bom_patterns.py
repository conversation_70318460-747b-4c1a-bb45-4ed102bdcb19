class SchedulePatterns:
    """Container for all schedule-related regex patterns and valid values"""

    # Valid schedule values
    VALID_SCHEDULES = {
        "5", "10", "20", "30", "40", "60", "80", "100", "120", "140", "160",
        "5S", "10S", "40S", "80S", "STD", "XH", "XXH", "XS", "XXS", "STW"
    }

    # Schedule components for building patterns
    COMPONENTS = {
        'basic_number': r'\d+',                    # Basic numbers like 40, 80
        'number_with_s': r'\d+S',                  # Numbers with S suffix like 40S
        'special_types': r'XXH|XXS|XH|XS|STW|STD', # Special schedule types
        'std_with_wt': r'STD(?:\s?WT)?',          # STD or STD WT variation
        'custom_base': r'CUST(?:OM)?[\s.-]*',     # Base for custom patterns
        'custom_types': r'[HLM]',                  # Custom type indicators
        'cross_separator': r'[\s/\-]+X?[\s/\-]*',  # Separators for cross schedules
    }

    # Schedule prefixes with optional suffixes
    PREFIX_PATTERNS = [
        r'SCHEDULE\.?/?',
        r'SCH\.?/?',
        r'SCHED\.?/?',
        r'S/?'
    ]

    # Custom patterns for special handling
    CUSTOM_PATTERNS = [
        (r'CUST(?:OM)?[\s.-]*([HLM])', 'CUSTOM-{}'),
    ]

    # Cleanup patterns for final formatting
    CLEANUP_PATTERNS = [
        (r'SCH\.|SCHED\.|SCHEDULE', 'SCH'),
        (r'SCH\s+(\d+\s*S?)', r'SCH \1'),
        (r'SCH/(\d+\s*S?)', r'SCH \1'),
        (r',,', ','),
    ]

    @classmethod
    def build_schedule_values(cls):
        """Build pattern for all possible schedule values"""
        return (
            f"(?:{cls.COMPONENTS['basic_number']}|"
            f"{cls.COMPONENTS['number_with_s']}|"
            f"{cls.COMPONENTS['special_types']}|"
            f"{cls.COMPONENTS['std_with_wt']})"
        )

    @classmethod
    def build_cross_schedule(cls):
        """Build pattern for cross schedule formats"""
        schedule_values = cls.build_schedule_values()
        return f"(?:{cls.COMPONENTS['cross_separator']}{schedule_values})?"

    @classmethod
    def get_schedule_pattern(cls):
        """Get the complete prefixed schedule pattern"""
        prefix_pattern = f"(?:{'|'.join(cls.PREFIX_PATTERNS)})"
        schedule_values = cls.build_schedule_values()
        cross_schedule = cls.build_cross_schedule()

        return (
            r'\b'                    # Word boundary
            f'{prefix_pattern}'      # All possible prefixes
            f'(?:\s*({schedule_values}))'  # Main schedule value (captured)
            f'{cross_schedule}'      # Optional cross schedule
            r'\b'                    # Word boundary
        )

    @classmethod
    def get_standalone_pattern(cls):
        """Get the pattern for standalone schedule values"""
        schedule_values = cls.build_schedule_values()
        custom_pattern = (
            f"{cls.COMPONENTS['custom_base']}"
            f"{cls.COMPONENTS['custom_types']}"
        )

        return (
            r',\s*'                  # Start with comma and whitespace
            r'('                     # Start capture group
            f'(?:{custom_pattern}|'  # Custom patterns
            f'{schedule_values}'     # Standard patterns
            f'{cls.build_cross_schedule()})'  # Optional cross schedule
            r')\s*'                  # End with optional whitespace
            r'(?:,|$)'               # End with comma or end of string
        )

class RatingPatterns:
    """Container for all rating-related regex patterns and valid values"""

    # Valid rating values
    VALID_RATINGS = {
        "100","125","130","150","175","200","240","250","275","300",
        "325","400","420","450","500","520","600","620","750","800","900",
        "1000","1040","1200","1360","1440","1500","2000","2080","2500",
        "3000","3500","4000","5000","5200","6000","7000","7500","9000","10000"
    }

    # Rating components for building patterns
    COMPONENTS = {
        'prefixes': {
            'class': r'CL(?:ASS)?\.?',     # CLASS, CL.
            'cls': r'CLS\.?',              # CLS, CLS.
            'hash': r'[#]',                # #
            'pn': r'PN',                   # PN
            'rating': r'RATING',           # RATING
            'lbs': r'LBS?',                # LB, LBS
            'psi': r'PSI'                  # PSI
        },
        'suffixes': {
            'flange_type': r'(?:RF|FF)',   # RF or FF
            'hash': r'[#]',                # #
            'units': r'(?:LBS?|PSI)'       # LB, LBS, PSI
        },
        'separators': r'[\s.-]*',          # Spaces, dots, hyphens
        'number': r'\d+'                   # The rating number
    }

    # Cleanup patterns for final formatting
    CLEANUP_PATTERNS = [
        (r'CLASS\.|CLS\.|CL\.', 'CLASS'),
        (r',,', ','),
    ]

    @classmethod
    def build_prefix_pattern(cls):
        """Build pattern for all possible prefixes"""
        return r'(?:' + '|'.join(cls.COMPONENTS['prefixes'].values()) + r')'

    @classmethod
    def build_suffix_pattern(cls):
        """Build pattern for all possible suffixes"""
        return r'(?:' + '|'.join(cls.COMPONENTS['suffixes'].values()) + r')'

    @classmethod
    def get_rating_pattern(cls):
        """Get the complete rating pattern with prefixes or suffixes"""
        prefixes = cls.build_prefix_pattern()
        suffixes = cls.build_suffix_pattern()
        separators = cls.COMPONENTS['separators']
        number = cls.COMPONENTS['number']

        # Add a specific pattern for #rating format
        hash_pattern = r'#\s*({0})'.format(number)

        return (
            r'\b(?:'  # Word boundary and start non-capturing group
            # Prefix-number-optional suffix pattern
            f'(?:{prefixes}{separators}({number})(?:{separators}{suffixes})?)|'
            # Number-suffix pattern
            f'(?:({number}){separators}{suffixes})|'
            # Hash-number pattern (#3000 format)
            f'(?:{hash_pattern})'
            r')\b'  # End non-capturing group and word boundary
        )

    @classmethod
    def get_standalone_pattern(cls):
        """Get the pattern for truly standalone rating values"""
        number = cls.COMPONENTS['number']
        prefixes = cls.build_prefix_pattern()
        suffixes = cls.build_suffix_pattern()

        return (
            r'(?:^|,|\s)\s*'  # Start with beginning of string, comma, or whitespace
            r'('              # Start capture group
            f'{number}'       # Just the number
            r')'             # End capture group
            r'(?![a-zA-Z#])'  # Negative lookahead to ensure no letters or # follows
            r'\s*(?:,|\s|$)'  # End with comma, whitespace, or end of string
        )
