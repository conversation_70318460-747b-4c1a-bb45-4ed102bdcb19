import cv2
import numpy as np
from typing import <PERSON><PERSON>, List, Union


def draw_rectangle(image: np.ndarray,
                   rect: <PERSON>ple[int, int, int, int],
                   fill_color: Tuple[int, int, int] = (0, 255, 0),
                   border_color: Tuple[int, int, int] = (0, 255, 0),
                   fill_opacity: float = 0.3,
                   border_thickness: int = 2) -> np.ndarray:
    """
    Draw a filled rectangle on an OpenCV image.

    Args:
        image: Input image (numpy array)
        rect: Rectangle coordinates (x, y, w, h) or (x1, y1, x2, y2)
        fill_color: BGR color tuple for fill
        border_color: BGR color tuple for border
        fill_opacity: Opacity of fill (0.0 to 1.0)
        border_thickness: Thickness of border in pixels

    Returns:
        Image with drawn rectangle
    """
    output = image.copy()

    # Create overlay for transparent fill
    overlay = output.copy()

    # Convert rect format if needed (x,y,w,h) -> (x1,y1,x2,y2)
    if len(rect) == 4:
        if rect[2] < rect[0] or rect[3] < rect[1]:  # If w,h format
            x1, y1, w, h = rect
            x2, y2 = x1 + w, y1 + h
        else:  # If x2,y2 format
            x1, y1, x2, y2 = rect

    # Draw filled rectangle on overlay
    cv2.rectangle(overlay, (x1, y1), (x2, y2), fill_color, -1)

    # Blend the overlay with main image
    cv2.addWeighted(overlay, fill_opacity, output, 1 - fill_opacity, 0, output)

    # Draw border
    cv2.rectangle(output, (x1, y1), (x2, y2), border_color, border_thickness)

    return output


def draw_rectangles(image: np.ndarray,
                    rectangles: List[Tuple[int, int, int, int]],
                    fill_color: Tuple[int, int, int] = (0, 255, 0),
                    border_color: Tuple[int, int, int] = (0, 255, 0),
                    fill_opacity: float = 0.3,
                    border_thickness: int = 2) -> np.ndarray:
    """
    Draw multiple filled rectangles on an OpenCV image.

    Args:
        image: Input image (numpy array)
        rectangles: List of rectangle coordinates (x,y,w,h) or (x1,y1,x2,y2)
        fill_color: BGR color tuple for fill
        border_color: BGR color tuple for border
        fill_opacity: Opacity of fill (0.0 to 1.0)
        border_thickness: Thickness of border in pixels

    Returns:
        Image with drawn rectangles
    """
    output = image.copy()

    for rect in rectangles:
        output = draw_rectangle(
            output,
            rect,
            fill_color=fill_color,
            border_color=border_color,
            fill_opacity=fill_opacity,
            border_thickness=border_thickness
        )

    return output
