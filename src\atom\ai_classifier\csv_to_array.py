import csv, json
#import pandas as pd

def dataframe_to_structure(df, stage_2=False): # Convert RFQ into items for classification.
    #print("\n\nCOLUMNS: \n", df.columns)
    data_structure = []
    for index, row in df.iterrows():
        if stage_2:
            # Constructing the string for each item - with column existence check
            try:
                # Check if cls_list exists in the DataFrame columns
                cls_list_value = row.get('cls_list')
                cls_list_value = cls_list_value if cls_list_value is not None else 'Not Available'

                item_str = f"id: {row['__uid__']}\n" \
                           f"rfq_scope: {row['rfq_scope']}\n" \
                           f"cls_list: {cls_list_value}\n" \
                           f"material_description: {row['material_description']} #\n" \
                           f"size: {row['size']}"
            except Exception as e:
                print(f"ERROR in stage 2 processing: {e}\n{row}")
                # Provide a fallback that still allows processing to continue
                item_str = f"id: {row['__uid__']}\n" \
                           f"rfq_scope: {row.get('rfq_scope', 'Unknown')}\n" \
                           f"cls_list: Not Available\n" \
                           f"material_description: {row.get('material_description', 'Unknown')} #\n" \
                           f"size: {row.get('size', 'Unknown')}"
        else:    # --> Stage 1
            try:
                item_str = f"id: {row['__uid__']}\n" \
                           f"material_description: {row['material_description']}\n" \
                           f"size: {row['size']}"
            except Exception as e:
                print(f"ERROR in stage 1 processing: {e}\n{row}")
        data_structure.append(item_str)
    return data_structure


def convert_json_to_structure(json_input):
    structured_data = []
    for item in json_input:
        # Directly constructing the dictionary for each item
        item_dict = {
            "classification field": item["field"],
            "description": item["description"],
            "options": item["options"],
            "answer": "",
            "answer explanation": "",
            "review": "",
            "review explanation": ""
        }
        # Add the dictionary itself to the list; delay JSON conversion if possible
        structured_data.append(item_dict)

    # If you need to convert each item to a JSON string, do it outside this loop
    # or ensure the conversion is appropriately handled.
    return structured_data

def convert_json_to_structure_NoAnswer(json_input):
    structured_data = []
    for item in json_input:
        # Directly constructing the dictionary for each item
        item_dict = {
            "classification field": item["field"],
            "description": item["description"],
            "options": item["options"]
        }
        # Add the dictionary itself to the list; delay JSON conversion if possible
        structured_data.append(item_dict)
    return structured_data

# def convert_json_to_structure(json_input):
#     structured_data = []
#     for item in json_input:
#         # Formatting the string for each dictionary in the list
#         item_str = f'"classification field": "{item["field"]}" \n' \
#                    f'"description": "{item["description"]}"\n' \
#                    f'"options": {item["options"]}\n' \
#                    f'"answer":\n' \
#                    f'"answer explanation":\n' \
#                    f'"review":\n' \
#                    f'"review explanation":'
#         structured_data.append(item_str)
#     return structured_data


def convert_csv_to_array(input_csv_path):
    data = []
    with open(input_csv_path, "r") as input_file:
        csv_reader = csv.reader(input_file)
        for row in csv_reader:
            if row:
                data.append(row[0])
    return data


if __name__ == "__main__":
  convert_csv_to_array("output.csv", "summarised_book_chunks.py")