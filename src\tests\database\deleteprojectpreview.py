"""
Preview the data to be deleted from project
"""
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from src.config.workspace import Workspace
from src.atom.dbManager import DatabaseManager
import pandas as pd
import tempfile
from os import path
import os

class Window(QWidget):

    def __init__(self) -> None:
        super().__init__(None)
        self.setMinimumSize(QSize(1200,800))

        self.workspace = Workspace()
        self.db = DatabaseManager()
        self.setLayout(QVBoxLayout())
        
        self.layout().addWidget(QLabel("Select Project:"))
        self.cbox = QComboBox()
        for project in self.get_projects():
            _id, name, number, companyName, _, _, _ = project
            data = {
                "projectId": _id,
                "name": name,
                "number": number,
                "companyName": companyName,
            }    
            self.cbox.addItem(f"{data}", userData=data)

        self.layout().addWidget(self.cbox)

        self.text = QTextEdit()
        self.layout().addWidget(self.text)

        pb = QPushButton("Preview Deleted Data")
        pb.clicked.connect(self.onPreview)
        self.layout().addWidget(pb)

        self.setWindowTitle("AIS Delete Project Preview")
        self.show()

        self.cbox.currentIndexChanged.connect(self.onSelectionChanged)
        self.onSelectionChanged()

    def onSelectionChanged(self, event=None):
        data = self.cbox.currentData()
        if not data:
            self.text.setText("No project data")
            return
        statements = self.get_statement_preview(data["projectId"])
        
        text = f"Selected Project - {data}\n"
        text += f"\n"
        text += f"\n"
        for n, s in enumerate(statements):
            table, statement = s
            text += f"----------------- {n} - {table} -----------------"
            text += f"\n"
            text += f"\n"
            text += str(statement)
            text += f"\n"
            text += f"\n"
            text += f"\n"
        self.text.setText(text)

    def onPreview(self, event):
        data = self.cbox.currentData()
        if not data:
            return
        projectId = data["projectId"]
        """Delete project given projectId"""
        statements = [
            ("BOM", ("SELECT * from BOM WHERE id in (SELECT BOM.id from BOM INNER JOIN PDFStorage WHERE BOM.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("GENERAL", ("SELECT * from GENERAL WHERE id in (SELECT GENERAL.id from GENERAL INNER JOIN PDFStorage WHERE GENERAL.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("SPEC", ("SELECT * from SPEC WHERE id in (SELECT SPEC.id from SPEC INNER JOIN PDFStorage WHERE SPEC.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("SPOOL", ("SELECT * from SPOOL WHERE id in (SELECT SPOOL.id from SPOOL INNER JOIN PDFStorage WHERE SPOOL.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("PDFStorage", "SELECT * from PDFStorage WHERE project_id=?"),
            ("ProjectSources", "SELECT * from ProjectSources WHERE projectId=?"),
            ("Projects", "SELECT * from Projects WHERE id=?"),
        ]
        name = None
        # Preview only - Delete all project related data
        results = []
        try:
            with self.db.connect() as conn:
                for name, statement in statements:
                    df = pd.read_sql_query(statement, conn, params=(projectId, ))
                    if name == "PDFStorage":
                        df.drop(["document"], axis=1, inplace=True)
                    results.append([name, df])
        except Exception as e:
            print(f"Failed to preview project data (id:{projectId}), {name}, `{statement}` data {e}")
        
        # Generate outfile
        with tempfile.TemporaryDirectory() as tmpdirname:
            outfile = path.join(tmpdirname, "project_delete_preview_data.xlsx")
            writer = pd.ExcelWriter(outfile, engine='xlsxwriter')

            overview_df = self.get_statement_preview(projectId)
            for n, d in enumerate(overview_df):
                records = len(results[n][1])
                d_new = (d[0], d[1], records)
                overview_df[n] = d_new
            overview_df = pd.DataFrame(overview_df, columns=["Sheet Name", "Query", "Records"])

            overview_df.to_excel(writer, sheet_name="Overview", index=False, na_rep='NaN')
            writer.sheets["Overview"].set_column(0, 0, 50)
            writer.sheets["Overview"].set_column(1, 1, 150)

            for name, df in results:
                df.to_excel(writer, sheet_name=name, index=False, na_rep='NaN')
                for column in df:
                    try:
                        col_idx = df.columns.get_loc(column)
                        column_length = max(df[column].astype(str).map(len).max(), len(column))
                    except Exception as e:
                        column_length = 100

                    writer.sheets[name].set_column(col_idx, col_idx, column_length)
            writer.close()

            # res = export_table_data_to_excel(self.table, exportPath) # Export to Excel
            
            exportPathUrl = QUrl.fromLocalFile(outfile).toString()
            msg = f"Exported project data to <a href='{exportPathUrl}'>{exportPathUrl}</a>"
            msgBox = QMessageBox()
            msgBox.setTextFormat(Qt.TextFormat.RichText)
            msgBox.setWindowTitle(f"Export {outfile} data" + "              ")  # Enlarges message box
            msgBox.setIcon(QMessageBox.Icon.Information)
            msgBox.setText(msg)
            msgBox.exec()


    def get_statement_preview(self, projectId):
        statements = [
            ("BOM", (f"SELECT * from BOM WHERE id in (SELECT BOM.id from BOM INNER JOIN PDFStorage WHERE BOM.pdf_id=PDFStorage.id AND PDFStorage.project_id={projectId})") ),
            ("GENERAL", (f"SELECT * from GENERAL WHERE id in (SELECT GENERAL.id from GENERAL INNER JOIN PDFStorage WHERE GENERAL.pdf_id=PDFStorage.id AND PDFStorage.project_id={projectId})") ),
            ("SPEC", (f"SELECT * from SPEC WHERE id in (SELECT SPEC.id from SPEC INNER JOIN PDFStorage WHERE SPEC.pdf_id=PDFStorage.id AND PDFStorage.project_id={projectId})") ),
            ("SPOOL", (f"SELECT * from SPOOL WHERE id in (SELECT SPOOL.id from SPOOL INNER JOIN PDFStorage WHERE SPOOL.pdf_id=PDFStorage.id AND PDFStorage.project_id={projectId})") ),
            ("PDFStorage", f"SELECT * from PDFStorage WHERE project_id={projectId}"),
            ("ProjectSources", f"SELECT * from ProjectSources WHERE projectId={projectId}"),
            ("Projects", f"SELECT * from Projects WHERE id={projectId}"),
        ]
        return statements

    def get_projects(self):
        """Get all projects' records"""
        try:
            statement = "Select * from Projects p"
            with self.db.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement) 
                return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get projects, {e}")
            return None

    def get_project_data(self):
        pass

if __name__ == "__main__":
    app = QApplication()
    win = Window()
    app.exec()
