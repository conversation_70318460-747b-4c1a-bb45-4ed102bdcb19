
from collections import defaultdict

component_category = """* `"componentCategory"`: This is section header on the left which may be labelled PIP<PERSON>, FITTINGS, FLANGES, VALVES / IN-LINE ITEMS, BOLTS, GASKETS, INSTRUMENTS, PIPE SUPPORTS, OLETS.
# The rows belows the corresponding header will have the corresponding component category. If no header is present, leave the component category empty.
# """

BOM_PROMPT_TEMPLATE = """
    **Objective:**
    Extract tabular data and return it as a structured JSON array of objects. Each object in the array should represent a single row from the input table.

    **Output Structure and Column Mapping:**
    Each JSON object must contain the following keys, with their values extracted from the corresponding table columns:
    * `"pos"`: From the first column of the table (may be labeled 'MARK','MK', 'ID', 'POS', or 'ITEM' in the source). This column's value signals the beginning of a new data row.
    {column_prompts}
    {component_category}

    **Data Handling and Formatting Instructions:**

    1.  **New Row Detection:** A new data row begins when there is content in the far-left column (designated as 'MK', 'ID', 'POS', or 'ITEM').
    2.  **Wrapped Rows:** Carefully and accurately combine text that wraps across multiple lines within a single cell into a single string for its respective field in the JSON output. Ensure correct spacing when concatenating wrapped lines.
    3.  **Red Text (Revisions):**
        * Text styled in red indicates a revised value. This red text should be extracted as the **current and definitive** value for the field it appears in or is closest to.
        * If red text modifies only a portion of a cell's content (e.g., a single word or number), use the red text to replace the original segment it revises.
        * If an entire cell's value is red, that red text is the complete and current value for that field.
        * Prioritize red text as the correct data when present.
    4.  **Strikethrough Text (Voided Items):**
        * If any text within a 'DESCRIPTION' cell is formatted with a strikethrough, or if an entire row appears to be voided via prominent strikethrough formatting across multiple cells for that row:
            * Prefix the *entire content* of the 'DESCRIPTION' field for that specific item with the string: `"<ITEM VOIDED> "`.
            * The original strikethrough text should still be included after this prefix.
    5.  **Data Integrity:**
        * Preserve all original characters, symbols, and formatting within the data values as accurately as possible, except where overridden by rules for red text or strikethrough. This includes units (e.g., ', ", FT, EA), special characters, and case sensitivity.
        * Handle measurements and mixed alphanumeric strings (e.g., `2"X1"`, `59'-9"`) correctly.
    6.  **Empty or Missing Values:** If a cell in the input table corresponding to QTY, SIZE, or DESCRIPTION, ITEM CODE is empty, represent its value as an empty string (`""`) or `null` in the output JSON. The 'MK' field should always be present for a valid row.


Use only plain ASCII characters. Do not use typographic punctuation like curly quotes (“”, ‘’), en-dashes (–), or em-dashes (—). Prefer straight quotes (" and ') and simple hyphens (-)
"""

STANDARD_TABLE_PROMPT_TEMPLATE = """
    **Objective:**
    Extract tabular data and return it as a structured JSON array of objects. Each object in the array should represent a single row from the input table.

    **Output Structure and Column Mapping:**
    Each JSON object must contain the following keys, with their values extracted from the corresponding table columns:

    **Data Handling and Formatting Instructions:**

    1.  **Wrapped Rows:** Carefully and accurately combine text that wraps across multiple lines within a single cell into a single string for its respective field in the JSON output. Ensure correct spacing when concatenating wrapped lines.
    2.  **Red Text (Revisions):**
        * Text styled in red indicates a revised value. This red text should be extracted as the **current and definitive** value for the field it appears in or is closest to.
        * If red text modifies only a portion of a cell's content (e.g., a single word or number), use the red text to replace the original segment it revises.
        * If an entire cell's value is red, that red text is the complete and current value for that field.
        * Prioritize red text as the correct data when present.
    3.  **Strikethrough Text (Voided Items):**
        * If any text within a 'DESCRIPTION' cell is formatted with a strikethrough, or if an entire row appears to be voided via prominent strikethrough formatting across multiple cells for that row:
            * Prefix the *entire content* of the 'DESCRIPTION' field for that specific item with the string: `"<ITEM VOIDED> "`.
            * The original strikethrough text should still be included after this prefix.
    4.  **Data Integrity:**
        * Preserve all original characters, symbols, and formatting within the data values as accurately as possible, except where overridden by rules for red text or strikethrough. This includes units (e.g., ', ", FT, EA), special characters, and case sensitivity.
        * Handle measurements and mixed alphanumeric strings (e.g., `2"X1"`, `59'-9"`) correctly.
    5.  **Empty or Missing Values:** If a cell in the input table corresponding to QTY, SIZE, or DESCRIPTION, ITEM CODE is empty, represent its value as an empty string (`""`) or `null` in the output JSON.

Use only plain ASCII characters. Do not use typographic punctuation like curly quotes (“”, ‘’), en-dashes (–), or em-dashes (—). Prefer straight quotes (" and ') and simple hyphens (-)
"""

GENERAL_PROMPT = """
    "Respond with a JSON object containing the field {roi_name} and the extracted text value from the image. Return ONLY valid JSON — no markdown, no explanation.
    Return the data in the following format:

    {{"{roi_name}": value}}

    Important:
    1. Use an empty string ("") for any blank or empty values in the image.
    2. Do not omit keys for blank values; include them with empty strings.
    3. Your response should only have 1 answer. If rows are wrapped or contain multiple lines, join them with a space ensuring there is only one answer.
    4. Do NOT include code fences or markdown formatting

    Example for a blank TAG:
    {{"{roi_name}": ""}}

    Ensure the response is a valid JSON array of dictionaries.

Use only plain ASCII characters. Do not use typographic punctuation like curly quotes (“”, ‘’), en-dashes (–), or em-dashes (—). Prefer straight quotes (" and ') and simple hyphens (-)
"""

ELEVATION_PROMPT = """
    "Extract the text that signifies an elevation. Return ONLY valid JSON — no markdown, no explanation.

    elevations =
        [
        # Pattern for "EL +65'-3 3/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",

        # Pattern for "EL +43'-10 1/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",

        # Pattern for "EL +43'-10"" format (feet-inches)
        "EL [-+]?\\d+'[-]\\d+\"",

        # Pattern for "EL +28'" format (feet only)
        "EL [-+]?\\d+'",

        # Pattern for decimal and integer formats
        "EL [-+]?\\d+\\.\\d+",  # EL +43.5
        "EL [-+]?\\d+",  # EL +43

        # Legacy patterns (keeping for compatibility)
        "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "Z [-+]?\\d+",
        "EL [-+]?\\d+'-\\d+( \\d+/\\d+)?\"",
        "EL [-+]?[0-9]+\\.?[0-9]*"
    ]

    The above elevations list are regex patterns to extract elevation values from the image. Use this list to extract the elevation values from the image.

    Example elevations per page:

    EL +120'-6 1/2"; EL +120'-1 3/16"
    EL +39'-4 3/4"; EL +41'-8 3/16"
    EL +65'-3 3/8"; EL +43'-10 1/8"; EL +64'-6 1/2"; EL +61'-1 1/4"
    EL +76'-0 3/8"; EL +79'-9 1/2"; EL +65'-3 3/8"
    EL +73'-0 1/4"; EL +67'-2"; EL +48'-4 1/16"
    EL +27'-6 3/16"; EL +38'-11 13/16"; EL +29'-5 1/16"; EL +28'-4 1/16"; EL +29'-0 15/16"; EL +36'-11 11/16"
    EL +29'-0 15/16"; EL +26'-10 13/16"; EL +28'-0 1/8"
    EL +27'; EL +28'-0 1/8"; EL +27'-3 15/16"
    EL +25'-3 1/8"; EL +29'-8 7/8"; EL +24'-3"; EL +28'-8 3/4"; EL +26'-7 1/16"
    EL +25'-2 1/4"; EL +25'-10 1/8"; EL +28'-8 3/4"; EL +24'-2 1/8"
    EL +28'-2 1/4"; EL +29'-2 3/8"; EL +25'-2 1/4"; EL +25'-10 1/8"
    EL +25'-0 15/16"; EL +25'-2 1/4"; EL +17'-2 3/16"; EL +24'-3 11/16"; EL +18'-2 1/4"
    EL +55'-10 11/16"; EL +60'-4 15/16"; EL +24'-4 7/16"; EL +23'-7 9/16"
    EL +18'-7 7/8"; EL +26'-8 15/16"; EL +17'-8 1/4"; EL +29'-7 7/8"
    EL +50'-1 1/2"; EL +50'-4 5/16"
    EL +45'-9 5/16"; EL +30'-4 1/4"
    EL +26'-9 3/4"; EL +30'-4 1/4"; EL +27'-0 9/16"; EL +25'-9 5/8"
    EL +25'-9 5/8"; EL +26'-0 1/4"; EL +24'-10 11/16"; EL +28'-1 3/4"
    EL +27'-10 1/2"; EL +28'-1 3/4"; EL +28'-5"; EL +28'-4 3/8"
    EL +47'-3 15/16"; EL +47'-11 9/16"; EL +47'-9 9/16"
    EL +28'-5"; EL +29'-7 7/8"; EL +38'-0 1/2"; EL +32'-8 7/8"; EL +40'-9 7/8"
    EL +38'-0 1/2"; EL +36'-7 1/4"; EL +35'-8 7/8"; EL +35'-11 1/4"

    End example

    Return the data in the following dictionary format:
    {"elevations": ["EL .113", "EL 113'-8\""]}

    Important:
    1. Use an empty string ("") for any blank or empty values in the image.
    2. Do not omit keys for blank values; include them with empty strings.
    3. Make sure only unique elevation values are returned per page.
    4. Return value(s) as a list.
    5. Do NOT include code fences or markdown formatting

    Example for a blank value:
    {"elevations": []}

    Ensure the response is a valid JSON array of dictionaries.

    Use only plain ASCII characters. Do not use typographic punctuation like curly quotes (“”, ‘’), en-dashes (–), or em-dashes (—). Prefer straight quotes (" and ') and simple hyphens (-)
"""

bom_column_prompts = {
    "material_description": "From the description column (may be labeled 'DESCRIPTION' in the source)",
    "quantity": "From the quantity column (may be labeled 'QTY' or 'QUANTITY' in the source)",
    "size": "From the size column (may be labeled 'NPD', 'ND', 'NS', or 'SIZE' in the source)",
    "ident_code": "From the ident code column (may be labeled 'ITEM CODE' in the source)",
    "item_length": "If applicable, from the item length column (may be labeled 'LENGTH' or 'ITEM LENGTH' in the source)",
}


def build_prompts(roi_payload):

    prompts = defaultdict(dict)

    for group_number, group_rois in roi_payload["groupRois"].items():
        for roi_data in group_rois:

            roi_name = roi_data["columnName"]
            roi_name_lower = roi_name.lower()

            is_table = roi_data["isTable"]
            if roi_name_lower == "isometric drawing area":
                # prompts[group_number][roi_name] = ELEVATION_PROMPT # Static prompt
                continue
            if not is_table:
                # prompts[group_number][roi_name] = GENERAL_PROMPT
                continue

            # Get table columns
            table_columns = roi_data["tableColumns"]
            column_prompts = []
            if roi_name_lower == "bom":
                for n, column in enumerate(table_columns):
                    column_name = list(column.keys())[0]
                    if column_name == "pos":
                        continue
                    column_prompt = bom_column_prompts.get(column_name, "")
                    if not column_prompt:
                        column_prompt = "extract column"

                    column_prompt = f"{column_prompt}"
                    column_prompts.append(f"* `\"{column_name}\"`: {column_prompt}. Likely column number {n}\n")
            else:
                for n, column in enumerate(table_columns):
                    column_name = list(column.keys())[0]
                    column_prompt = f"extract column"
                    column_prompts.append(f"* `\"{column_name}\"`: {column_prompt}. Likely column number {n}\n")

            prompt = BOM_PROMPT_TEMPLATE.format(column_prompts="\n\t".join(column_prompts), component_category=component_category)
            prompts[group_number][roi_name] = prompt

    return prompts

if __name__ == "__main__":
    from pprint import pp
    from src.utils import convert_roi_payload

    roi_payload = convert_roi_payload.convert_roi_payload(
        r'c:\Drawings\Clients\axisindustries\Axis 2025-08-07 - Bluebonnet field route Isos\layout.json',
        ignore_bom=True
    )

    res = build_prompts(roi_payload)
    pp(res)

