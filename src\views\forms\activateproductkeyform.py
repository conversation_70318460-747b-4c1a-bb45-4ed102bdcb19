"""
Allow user to select PDF from file explorer for new projectg
"""
from PySide6.QtWidgets import  QVBoxLayout, QLabel, QLineEdit, QPushButton, QSizePolicy
from PySide6.QtCore import Qt, Signal, QUrl, QTimer
from PySide6.QtGui import QMovie, QHideEvent
from .baseform import BaseForm
from pubsub import pub
from src.app_paths import resource_path
from src.pyside_util import get_resource_pixmap
from src.views.forms.tokencheckoutwindow import TokenCheckoutWindow

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 960

class ActivateProductKeyForm(BaseForm):

    sgnFilePicked = Signal(str)
    sgnFilePickedExisting = Signal(str)  # Add to existing project
    sgnOpenCheckoutWindow = Signal()
    sgnActivateProductKeyRespone = Signal(dict)

    def __init__(self, parent):
        super().__init__(parent)
        self.success: bool = False
    
    def initUi(self):
        self.formSize.setWidth(480)
        self.formSize.setHeight(480)

        self.title.setText("")
        self.subtitle.setText("Activate Product Key")
        self.subtitle.setObjectName("titleLabel")
        self.subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.setLayout(QVBoxLayout())
        self.addStretchSpacer()
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        self.movie.stop()
        self.lblMovie = QLabel()
        self.lblMovie.setMovie(self.movie)
        self.lblMovie.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.lblMovie)
        self.lblMovie.hide()

        self.lblIcon = QLabel()
        self.lblIcon.setPixmap(get_resource_pixmap("check.svg"))
        self.lblIcon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.lblIcon)
        self.lblIcon.hide()

        self.productKey = QLineEdit()
        self.productKey.setFixedHeight(64)
        self.productKey.textEdited.connect(self.onProductKeyEdit)
        # self.productKey.setFixedWidth(420)
        # self.productKey.setContentsMargins(32, 8, 8, 32)
        self.productKey.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.productKey.returnPressed.connect(self.activateProductKeyRequest)
        self.layout().addWidget(self.productKey)

        self.addVSpace(32)
        self.status = QLabel()
        self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.status)

        self.addStretchSpacer()

        self.pbActivate = QPushButton("Activate")
        self.pbActivate.clicked.connect(self.activateProductKeyRequest)
        self.pbActivate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbActivate.setMinimumHeight(48)
        self.layout().addWidget(self.pbActivate)

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        pub.subscribe(self.onActivateProductKeyResponse, "activate-product-key-response")

        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.setInterval(2500)
        self.timer.timeout.connect(self.onTimeout)

        self.setFloatingButtonCancel()

        self.sgnActivateProductKeyRespone.connect(self.onSgnActivateProductKeyResponse)

    def updateUi(self):
        return

    def initDefaults(self):
        self.productKey.show()
        self.productKey.setFocus()
        self.movie.stop()
        self.productKey.clear()
        self.lblMovie.hide()
        self.lblIcon.hide()
        self.status.setText("")
        self.pbActivate.setEnabled(True)
        self.productKey.setEnabled(True)
        self.pbFloating.setEnabled(True)
    
    def setParams(self, params):
        return

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)

    def activateProductKeyRequest(self):
        self.pbFloating.setEnabled(False)
        self.pbActivate.setEnabled(False)
        self.productKey.setEnabled(False)
        self.productKey.hide()
        product_key = self.productKey.text().upper()
        product_key = product_key.strip()
        self.productKey.setText(product_key)
        if not product_key or len(product_key) < 10:
            self.status.setText(f"Invalid Product Key")
            self.productKey.show()
            self.lblMovie.hide()
            self.lblIcon.hide()
            self.pbActivate.setEnabled(True)
            self.productKey.setEnabled(True)
            self.productKey.setFocus()
            self.pbFloating.setEnabled(True)
            return
        self.status.setText("Checking Product Key...")
        self.lblMovie.show()
        self.movie.start()
        pub.sendMessage("activate-product-key", product_key=product_key)

    def onActivateProductKeyResponse(self, data):
        self.sgnActivateProductKeyRespone.emit(data)
    
    def onSgnActivateProductKeyResponse(self, data):
        if data.get("status", "fail") == "success":
            self.productKey.clear()
            # item = data["product"]["item"]
            quantity = data["product"]["quantity"]
            self.lblIcon.show()
            self.lblMovie.hide()
            self.movie.stop()
            self.status.setText(f"Product key activated succesfully! \n{quantity} tokens have been added to account")
            self.timer.start()
        else:
            self.status.setText(f"{data['message']}")
            self.productKey.show()
            self.lblMovie.hide()
            self.lblIcon.hide()
            self.pbActivate.setEnabled(True)
            self.productKey.setEnabled(True)
            self.productKey.setFocus()
            self.pbFloating.setEnabled(True)

    def onTimeout(self):
        # Redirect to other view
        pub.sendMessage("goto-workspace-view", name="ProjectView")
        self.initDefaults()

    def onFloatingButton(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")
        self.initDefaults()
    
    def onProductKeyEdit(self):
        self.productKey.setText(self.productKey.text().upper())