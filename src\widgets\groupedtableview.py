"""
Pandas Dataframe Custom TableView
"""
import os
import time
import re
import math
from src.utils.logger import logger
import json
import weakref

from datetime import datetime
from typing import Union
from fractions import Fraction
from copy import deepcopy
from enum import Flag, auto

import numpy as np
from PySide6.QtGui import QMouseEvent, QWheelEvent
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
import pandas as pd
from pandas.api.types import is_numeric_dtype, is_float_dtype
from pubsub import pub
from natsort import natsort_keygen

from src.app_paths import getSavedFieldMapJson, saveFieldMapJson
from src.pyside_util import get_resource_qicon, get_resource_pixmap


# logger = logging.getLogger(__file__)

# When in edit mode, change cell by arrow key
# instead of changing char
DELEGATE_ARROW_KEY_NAVIGATION = False

READ_ONLY_FIELDS = [
    "__uid__",
    "pdf_id",
    "Category",
    "last_updated",
    "sys_path",
]

class GroupedTableFlags(Flag):

    HEADER_CHECKBOX_OFF = auto()
    HEADER_CHECKBOX_FIRST = auto()


class TableClipboard:

    def __init__(self, data=None, shape=None) -> None:
        self.data = data
        self.shape = shape

    def set(self, data, shape):
        self.data = data
        self.shape = shape

    def clear(self):
        self.data = None
        self.shape = None


class BaseDelegate(QStyledItemDelegate):

    DEFAULT_STYLE = 'border: 0px'
    EDITING_STYLE = 'border: 1px solid white; font: bold'
    RED_STYLE = 'border: 1px solid red'
    # DEFAULT_STYLE = 'border: 1px solid white; font: bold'

    leftMouseReleased = Signal()
    editingCancelled = Signal()
    modifierPressed = Signal(object)
    moveCurrentCellBy = Signal(int, int) # row, column
    showCellTooltip = Signal(str, object)

    def __init__(self, parent, filterModel) -> None:
        super().__init__(parent)
        self._parent = parent
        self._editorRef = None

    def clone(self) -> QStyledItemDelegate:
        logger.error("Not Implemented - clone")
        raise NotImplementedError

    def setCurrentText(self, text):
        # Not implemented. Use this value to override current value
        # Used when cell editing triggered by key input
        pass

    def commitAndCloseEditor(self):
        self.removeEventFilter(self)
        self.cleanup()
        editor = self._editorRef()
        if isinstance(editor, QWidget):
            self.commitData.emit(editor)
            self.closeEditor.emit(editor)
            editor.setStyleSheet(self.DEFAULT_STYLE)

    def cleanup(self):
        signals = [self.moveCurrentCellBy,
                  self.modifierPressed,
                  self.editingCancelled,
                  self.showCellTooltip]
        for signal in signals:
            try:
                signal.disconnect()
            except:
                pass


class Completer(QCompleter):

    tabPressed = Signal(object)
    def __init__(self, completions, parent):
        super().__init__(completions, parent)

        self.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.setCompletionMode(self.CompletionMode.PopupCompletion)
        self.setWrapAround(True)
        self.setFilterMode(Qt.MatchFlag.MatchStartsWith)
        self.popup().installEventFilter(self)

    def eventFilter(self, target: QObject, event: QEvent) -> bool:
        if target == self.popup():
            if event.type() == QEvent.KeyPress:
                if event.key() in [Qt.Key.Key_Tab, Qt.Key.Key_Backtab]:
                    self.tabPressed.emit(event.key())
                    return True

        return super().eventFilter(target, event)


class ComboBoxDelegate(BaseDelegate):

    def __init__(self, parent, options: list, filterModel, showFractions=False) -> None:
        super().__init__(parent, list)
        self._options: list = options
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = None
        self._showFractions = showFractions
        self._fractionCache = {}

    def clone(self) -> QStyledItemDelegate:
        return ComboBoxDelegate(parent=self._parent,
                                options=self._options,
                                filterModel=self._filterrRef(),
                                showFractions=self._showFractions)

    def isEditing(self):
        try:
            return self._editorRef().hasFocus()
        except Exception as e:
            logger.info("Editor ref has already been destroyed")
        return False

    def createEditor(self, parent, option, index):
        self.mappedIndex = self._filterrRef().mapToSource(index) # Map filter index to source model index
        editor: QComboBox = QComboBox(parent)
        editor.setItemDelegate(QStyledItemDelegate())
        editor.style().unpolish(editor)
        editor.style().polish(editor)
        editor.setStyleSheet('border: 1px solid white; font: bold')
        self._editorRef = weakref.ref(editor)
        editor.setEditable(True)
        editor.setObjectName("cellEditor")
        return editor

    def getFraction(self, v):
        dec, whole = math.modf(v)
        r = Fraction(dec)
        whole = int(whole)
        if dec == 0 and whole == 0:
            r = "0"
        elif whole == 0:
            r = f"{r.numerator}/{r.denominator} ({v})"
        elif r.numerator == 0:
            r = f"{int(v)}"
        else:
            r = f"{int(whole)} {r.numerator}/{r.denominator} ({v})"
        return r

    def setEditorData(self, editor: QComboBox, index):
        option = self.mappedIndex.data()
        editor.clear()
        editor.view().installEventFilter(self)
        option = "" if option is None else option
        try:
            n = self._options.index(option)
            options = self._options
        except ValueError:
            options = [option] + self._options

        completer = Completer(self._options, self)
        completer.tabPressed.connect(self.completerTabPressed)
        editor.setCompleter(completer)

        editor.addItems(options)
        editor.setCurrentText(option)

        if self._showFractions:
            for row in range(editor.count()):
                try:
                    v = editor.itemData(row, Qt.ItemDataRole.DisplayRole)
                    r = self.getFraction(float(v))
                    editor.setItemData(row, r, Qt.ItemDataRole.DisplayRole)
                    self._fractionCache[r] = v
                except Exception as e:
                    pass
            if option != "":
                self._value = float(option)
            else:
                pass
        else:
            self._value = option

        try:
            editor.setCurrentIndex(n)
        except:
            pass
        lineEdit = editor.lineEdit()
        lineEdit.setSelection(0, len(lineEdit.text()))
        editor.setFocus()

    def completerTabPressed(self, event: QKeyEvent):
        """Tab handler when delegate popup is shown"""
        editor = self._editorRef()

        # If item is selected, prioritize setting that value
        index = editor.completer().popup().currentIndex()
        if index.isValid():
            value = editor.completer().completionModel().data(index, Qt.ItemDataRole.DisplayRole)
            self._editorRef().setCurrentText(value)
            return

        text = editor.currentText()
        row = -1
        # Alternatively, cycle the matched item
        for n in range(editor.completer().completionModel().rowCount()):
            index = editor.completer().completionModel().index(n, 0)
            value = editor.completer().completionModel().data(index, Qt.ItemDataRole.DisplayRole)
            if value == text:
                row = n
                break
        # Forward cycle
        if event == Qt.Key.Key_Tab:
            row += 1
            if row >= editor.completer().completionModel().rowCount():
                row = 0
        else: # Backward cycle
            row -= 1
            if row < 0:
                row = editor.completer().completionModel().rowCount() - 1
        index = editor.completer().completionModel().index(row, 0)
        value = editor.completer().completionModel().data(index, Qt.ItemDataRole.DisplayRole)
        if index.isValid():
            self._editorRef().setCurrentText(value)

        return True

    def onTextEdit(self, editor, text):
        editor.showPopup()
        editor.setFocus()

    def setModelData(self, editor: QComboBox, model, index):
        option = editor.currentText()
        if option == "":
            option = None
        if self._showFractions:
            try:
                v = self._fractionCache.get(option, option)
                float(v)
                option = v
            except Exception as e:
                option = self._value

        try:
            model.setData(index, option, Qt.EditRole)
            self.commitData.emit(editor)
        except ValueError as e:
            logger.info(f"Combobox Invalid value - {e}")
        self.closeEditor.emit(editor)

    def hasCompletions(self) -> bool:
        return self._editorRef().completer().completionModel().rowCount() > 1

    def eventFilter(self, target, event):

        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                moveCell = False
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().lineEdit().setText(self._value)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.EndEditHint.NoHint)
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.editingCancelled.emit()
                    return True

                if QApplication.keyboardModifiers() != Qt.KeyboardModifier.NoModifier:
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab, Qt.Key.Key_Backtab):
                        self.removeEventFilter(self)
                        self.commitData.emit(self._editorRef())
                        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                        self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                        self.modifierPressed.emit(event)
                        return True
                    if QApplication.keyboardModifiers() == (Qt.ControlModifier):
                        if event.key() == Qt.Key.Key_Space:
                            self._editorRef().showPopup()
                            return True

                if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                    modifierPressed = False
                    row, column = None, None
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab):
                        modifierPressed = True
                        moveCell = True
                    if DELEGATE_ARROW_KEY_NAVIGATION:
                        if event.key() == Qt.Key_Down:
                            moveCell, row, column = True, 1, 0
                        elif event.key() == Qt.Key_Right:
                            moveCell, row, column = True, 0, 1
                        elif event.key() == Qt.Key_Left:
                            moveCell, row, column = True, 0, -1
                        elif event.key() == Qt.Key_Up:
                            moveCell, row, column = True, -1, 0
                    elif event.key() in (Qt.Key_Left, Qt.Key_Up, Qt.Key_Right, Qt.Key_Down):
                        return False
                    if moveCell:
                        self.save()
                        if modifierPressed:
                            self.modifierPressed.emit(event)
                        elif row or column:
                            self.moveCurrentCellBy.emit(row, column)

                        return True
        return False

    def setCurrentText(self, text):
        try:
            self._editorRef().setCurrentText(text)
        except Exception as e:
            logger.info(f"Invalid char to initiate cell value {text}", exc_info=True)

    def commitAndCloseEditor(self):
        self.removeEventFilter(self)
        self.cleanup()
        self.save()

    def save(self):
        """Restrict valid values to dropdown options"""
        editor: QComboBox = self._editorRef()
        if isinstance(editor, QComboBox):
            # Restrict values to Combobox options
            currentText = None
            try:
                currentText = editor.currentText()
                currentText = currentText.strip()
                if self._showFractions:
                    key = self._fractionCache.get(currentText)
                    if key is None:
                        key = next(value for value in self._fractionCache.values() if float(value) == float(currentText))
                    self._options.index(key)
                else:
                    self._options.index(currentText)
                self.commitData.emit(editor)
            except Exception as e:
                if currentText:
                    self.showCellTooltip.emit("Invalid value", editor.pos())
            self.closeEditor.emit(editor)
            editor.setStyleSheet(self.DEFAULT_STYLE)

    def updateEditorGeometry(self, editor: QWidget, option: QStyleOptionViewItem, index: QModelIndex) -> None:
        """This ensures the delegate is size of cell irrespective of decoration role"""
        ret = super().updateEditorGeometry(editor, option, index)
        editor.setGeometry(option.rect)
        return ret


class LineEditDelegate(BaseDelegate):

    def __init__(self, parent, filterModel):
        super().__init__(parent, list)
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = ""

    def clone(self) -> QStyledItemDelegate:
        return LineEditDelegate(parent=self._parent,
                                filterModel=self._filterrRef())

    def createEditor(self, parent, option, index):
        # Map filter index to source model index
        self.mappedIndex: QModelIndex = self._filterrRef().mapToSource(index)
        editor = QLineEdit(parent)
        editor.textEdited.connect(self.onTextEdit)
        editor.setStyleSheet(self.EDITING_STYLE)
        self._editorRef = weakref.ref(editor)
        editor.setFrame(False)
        editor.installEventFilter(self)
        editor.setFocus()
        return editor

    def onTextEdit(self):
        self._editorRef().setStyleSheet(self.EDITING_STYLE)

    def setEditorData(self, editor, index):
        value = index.model().data(index, Qt.EditRole)
        if value is not None:
            editor.setText(str(value))
        self._value = value # Revert to this if cancelling

    def setModelData(self, editor, model, index):
        value = editor.text()
        # Convert value back to original
        dtype = None
        # TODO - cache or some better way. Cast value to same type as column
        modelData = self._filterrRef().sourceModel()._data
        for n, y in enumerate(modelData.columns):
            if index.column() != n:
                continue
            dtype = modelData[y].dtype
            try:
                if dtype == np.int64:
                    value = int(value)
            except:
                # self.showCellTooltip.emit("Invalid value", self._editorRef().pos())
                value = self._value
            break
        try:
            if isinstance(self._value, np.floating):
                value = np.fromstring(value, dtype=float, sep=',')[0]
            elif isinstance(self._value, float):
                value = float(value)
            elif isinstance(self._value, int):
                value = int(value)
        except:
            self.showCellTooltip.emit("Invalid value", self._editorRef().pos())
            value = self._value

        try:
            model.setData(index, value, Qt.EditRole)
            self.commitData.emit(editor)
        except Exception as e:
            # self.showCellTooltip.emit("Invalid value", self._editorRef().pos())
            logger.info(f"Incompatible or unsupported value {e}", exc_info=True)
        self.closeEditor.emit(editor)

    def updateEditorGeometry(self, editor, option, index):
        editor.setGeometry(option.rect)

    def eventFilter(self, target, event):

        if event.type() == QEvent.FocusOut:
            self.commitData.emit(self._editorRef())
            self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
            self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
            self.editingCancelled.emit()
            return True

        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().setText(str(self._value) if self._value is not None else "")
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self.editingCancelled.emit()
                    return True
                moveCell, row, column = False, 0, 0
                modifierPressed = False

                if QApplication.keyboardModifiers() != Qt.KeyboardModifier.NoModifier:
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab, Qt.Key.Key_Backtab):
                        self.removeEventFilter(self)
                        self.commitData.emit(self._editorRef())
                        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                        self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                        self.modifierPressed.emit(event)
                        return True

                if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                    modifierPressed = False
                    row, column = None, None
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab):
                        modifierPressed = True
                        moveCell = True
                    if DELEGATE_ARROW_KEY_NAVIGATION:
                        if event.key() == Qt.Key_Down:
                            moveCell, row, column = True, 1, 0
                        elif event.key() == Qt.Key_Right:
                            moveCell, row, column = True, 0, 1
                        elif event.key() == Qt.Key_Left:
                            moveCell, row, column = True, 0, -1
                        elif event.key() == Qt.Key_Up:
                            moveCell, row, column = True, -1, 0
                    elif event.key() in (Qt.Key_Left, Qt.Key_Up, Qt.Key_Right, Qt.Key_Down):
                        return False
                    if moveCell:
                        self.removeEventFilter(self)
                        self.commitData.emit(self._editorRef())
                        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                        self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                        if modifierPressed:
                            self.modifierPressed.emit(event)
                        elif row or column:
                            self.moveCurrentCellBy.emit(row, column)

                        return True

        return False

    # def commitAndCloseEditor(self):
    #     self.removeEventFilter(self)
    #     if self._editorRef:
    #         editor = self._editorRef()
    #         if isinstance(editor, QWidget):
    #             self.commitData.emit(editor)
    #             self.closeEditor.emit(editor)
    #         editor.setStyleSheet(self.DEFAULT_STYLE)

    def setCurrentText(self, text):
        try:
            self._editorRef().setText(text)
        except Exception as e:
            logger.info(f"Invalid char to initiate cell value {text}", exc_info=True)


class ReadOnlyEditDelegate(BaseDelegate):

    DEFAULT_STYLE = 'border: 1px solid red; font: bold'

    def __init__(self, parent, filterModel):
        super().__init__(parent, list)
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = ""

    def clone(self) -> QStyledItemDelegate:
        return ReadOnlyEditDelegate(parent=self._parent,
                                filterModel=self._filterrRef())

    def createEditor(self, parent, option, index):
        self.mappedIndex = self._filterrRef().mapToSource(index) # Map filter index to source model index
        editor = QLineEdit(parent)
        # Avoid setting read only becasue it messes with events to
        # change cell
        # editor.setReadOnly(True)
        editor.textEdited.connect(self.onTextEdit)
        editor.setStyleSheet(self.DEFAULT_STYLE)
        self._editorRef = weakref.ref(editor)
        editor.setFrame(False)
        editor.installEventFilter(self)
        editor.setFocus()
        return editor

    def onTextEdit(self):
        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
        self._editorRef().setText(self._value)
        self.showCellTooltip.emit("Read only", self._editorRef().pos())

    def setEditorData(self, editor, index):
        value = index.model().data(index, Qt.EditRole)
        if value is not None:
            editor.setText(str(value))
        self._value = value # Revert to this if cancelling

    def setModelData(self, editor, model, index):
        value = editor.text()
        # Convert value back to original
        dtype = None
        # TODO - cache or some better way. Cast value to same type as column
        modelData = self._filterrRef().sourceModel()._data
        for n, y in enumerate(modelData.columns):
            if index.column() != n:
                continue
            dtype = modelData[y].dtype
            try:
                if dtype == np.int64:
                    value = int(value)
            except:
                value = self._value
            break
        try:
            if isinstance(self._value, np.floating):
                value = np.fromstring(value, dtype=float, sep=',')[0]
            elif isinstance(self._value, float):
                value = float(value)
            elif isinstance(self._value, int):
                value = int(value)
        except:
            value = self._value

        model.setData(index, value, Qt.EditRole)
        self.commitData.emit(editor)
        self.closeEditor.emit(editor)

    def updateEditorGeometry(self, editor, option, index):
        editor.setGeometry(option.rect)

    def eventFilter(self, target, event):

        if event.type() == QEvent.FocusOut:
            self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
            self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
            self.editingCancelled.emit()
            return True

        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().setText(str(self._value) if self._value is not None else "")
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self.editingCancelled.emit()
                    return True

                moveCell, row, column = False, 0, 0
                modifierPressed = False

                if QApplication.keyboardModifiers() != Qt.KeyboardModifier.NoModifier:
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab, Qt.Key.Key_Backtab):
                        self.removeEventFilter(self)
                        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                        self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                        self.modifierPressed.emit(event)
                        return True

                if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                    modifierPressed = False
                    if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Tab):
                        modifierPressed = True
                        moveCell = True
                    if DELEGATE_ARROW_KEY_NAVIGATION:
                        if event.key() == Qt.Key_Down:
                            moveCell, row, column = True, 1, 0
                        elif event.key() == Qt.Key_Right:
                            moveCell, row, column = True, 0, 1
                        elif event.key() == Qt.Key_Left:
                            moveCell, row, column = True, 0, -1
                        elif event.key() == Qt.Key_Up:
                            moveCell, row, column = True, -1, 0
                    if moveCell:
                        self.removeEventFilter(self)
                        self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                        if modifierPressed:
                            self.modifierPressed.emit(event)
                        else:
                            self.moveCurrentCellBy.emit(row, column)
                        # self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                        return True

        return False

    def setCurrentText(self, text):
        self._value = str(text) if text is not None else ""
        self._editorRef().setText(self._value)

class PandasDataFrameModel(QAbstractTableModel):
    """Class to populate a table view with a pandas dataframe"""
    sgnSort = Signal(int, object)
    sgnIndexDataChanged = Signal(object, object, object)  # Model (i.e. self), index, value
    def __init__(self, data: pd.DataFrame, fieldMap: dict, parent=None, firstColumn=None):
        """_summary_

        Args:
            data (pd.DataFrame): The table data. A panda dataframe
            fieldMap (dict): Allows showing a display value for the column header item.
                    Format is {id: {"display": alias} }, where id is the Qt.UserRole value
                    and the `display` value `alias` is the Qt.DisplayRole
        """
        super().__init__(parent)
        self._fieldMap: dict = fieldMap
        self._data: pd.DataFrame = data
        self.sgnSort.connect(self.sort)
        self._fieldFilters = {} # Read only
        self._firstColumn = firstColumn

    @property
    def firstColumn(self):
        return self._firstColumn

    @firstColumn.setter
    def firstColumn(self, value):
        if self._firstColumn == value:
            return
        self._firstColumn = value

    @property
    def fieldMap(self):
        return self._fieldMap

    @fieldMap.setter
    def fieldMap(self, newFieldMap):
        self.layoutAboutToBeChanged.emit()
        self._fieldMap = newFieldMap
        self.layoutChanged.emit()

    def rowCount(self, parent=None):
        return self._data.shape[0]

    def columnCount(self, parent=None):
        return self._data.shape[1]

    def data(self, index: QModelIndex, role=Qt.DisplayRole):
        if index.isValid():
            if role == Qt.DisplayRole or role == Qt.EditRole:
                v = self._data.iloc[index.row(), index.column()]
                if v is None or v is np.nan:
                    return ""
                if is_float_dtype(v):
                    return round(v, 2)
                return str(v)
            if role == Qt.UserRole:
                return self._data.iloc[index.row(), index.column()]
        return None

    def setData(self, index, value, role):
        if role == Qt.EditRole:
            value = None if value == "" else value
            dtype = None
            field = self._data.columns[index.column()]
            if value is not None:
                if dtype == np.int64:
                    value = int(value)
                elif is_float_dtype(dtype):
                    value = float(value)

            self._data.iloc[index.row(), index.column()] = value
            self.sgnIndexDataChanged.emit(self, index, value)
            self.dataChanged.emit(index, index)
            return True
        return False

    def headerData(self, section: int, orientation: Qt.Orientation, role: Qt.ItemDataRole):
        """Try to get display value from either standard field or RFQ"""
        if orientation == Qt.Horizontal:
            if role == Qt.DisplayRole:
                field = self._data.columns[section]
                display = self.fieldMap.get(field, {}).get("display", field)
                return display
            elif role == Qt.UserRole:
                return self._data.columns[section]
        elif orientation == Qt.Vertical and role == Qt.DisplayRole:
            return section + 1

        return super().headerData(section, orientation, role)

    def setHeaderData(self, section, orientation, value, role) -> bool:
        if orientation == Qt.Horizontal and role in [Qt.DisplayRole, Qt.EditRole]:
            field = self._data.columns[section]
            for k, v in self.fieldMap.items():
                if field == k:
                    v["display"] = value
                    return True
            return False
        if orientation == Qt.Horizontal and role == Qt.ItemDataRole.DecorationRole:
            return True
        return False

    def flags(self, index):
        return Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable

    def sort(self, column: int, order: Qt.SortOrder) -> None:
        if len(self._data):
            asc = order == Qt.AscendingOrder
            self.layoutAboutToBeChanged.emit()
            self._data.sort_values(self._data.columns[column], ascending=asc, inplace=True, ignore_index=False)
            self.layoutChanged.emit()

    def addColumn(self, name):
        newColumn = self.columnCount()
        self.beginInsertColumns(QModelIndex(), newColumn, newColumn)
        self._data[name] = None
        # TODO: switch loc suppress warning
        self.endInsertColumns()

    def addColumns(self, columns: list):
        newColumn = self.columnCount()
        self.beginInsertColumns(QModelIndex(), newColumn, newColumn+len(columns)-1)
        d = dict.fromkeys(columns, None)
        temp_df = pd.DataFrame(d, index=self._data.index)
        self._data = pd.concat([self._data, temp_df], axis=1)
        self.endInsertColumns()

    def fetchChunk(self, start_row, chunk_size=100):
        end_row = min(start_row + chunk_size, len(self._data))
        # return {row: self._data.iloc[row].to_dict() for row in range(start_row, end_row)}
        # print(start_row, end_row, chunk_size, len(self._data))
        return self._data.iloc[start_row:end_row]
        return {row: self._data.iloc[row].to_dict() for row in range(start_row, end_row)}


class FetchWorker(QThread):
    """A timed worker"""

    sgnFetchCheckUpdate = Signal()

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.fetchTimer = QTimer(self)
        self.done = False
        # self.fetchTimer.start(1000)
        # self.fetchTimer.timeout.connect(self.fetchMore)

    def run(self):
        while True:
            time.sleep(2)
            if self.done:
                break
            self.sgnFetchCheckUpdate.emit()


class VScrollbar(QWidget):
    """TODO: experiment with fake scrollbar"""

    sgnScrollValueChanged = Signal(float)

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.setFixedWidth(32)
        self.setLayout(QVBoxLayout())
        self.handle = QPushButton("")
        self.layout().addWidget(self.handle)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        pos = event.position().toPoint()
        pos.setX(0)
        self.handle.move(pos)
        pos.setX(self.parent().width() - 16)
        self.move(pos)
        print(pos)
        value = self.handle.y() / self.height()
        value = max(0, value)
        value = min(1, value)
        self.sgnScrollValueChanged.emit(value)
        return super().mousePressEvent(event)

    def value(self):
        pass

    def maximum(self):
        pass

    def resizeEvent(self, event: QResizeEvent) -> None:
        self.setFixedHeight(self.parent().height() - 64)
        return super().resizeEvent(event)


class LazyLoadingPandasDataFrameModel(PandasDataFrameModel):
    """Modified PandasDataFrameModel

    Features include:
        - Lazy loading
    """
    CHUNK_SIZE = 256

    sgnRowsLoadedChanged = Signal(int)
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.updateColumnIndexCache()
        # self._dataActive: pd.DataFrame = self.fetchChunk(0, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        self.totalRows = self._data.shape[0]
        self._rowsLoaded = min(self.totalRows, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        self._dataCache = {}

        self._dataReadUpdates: bool = True

        self.iconDropdown = get_resource_qicon("chevron-right.svg")
        self.iconDropdownActivated = get_resource_qicon("chevron-down.svg")

        # self._colIndexToFieldId = []
        self.mutex = QMutex()

        self._index: int = 0 # This is for future.
        # self.worker = FetchWorker(self)
        # self.worker.start()
        # self.worker.sgnFetchCheckUpdate.connect(self.fetchMore)

    @property
    def rowsLoaded(self):
        return self._rowsLoaded

    @rowsLoaded.setter
    def rowsLoaded(self, value):
        self._rowsLoaded = value
        self.sgnRowsLoadedChanged.emit(self._rowsLoaded)

    def realRowCount(self):
        """Returns the actual row count of raw data"""
        return super().rowCount()

    def rowCount(self, index=QModelIndex()):
        return self.rowsLoaded

    def loadAll(self):
        self.fetchMore(itemsToFetch=self.totalRows)

    def canFetchMore(self, index=QModelIndex()):
        return self.realRowCount() > self.rowsLoaded

    def fetchMore(self, index=QModelIndex(), itemsToFetch=None):
        self._index = self.rowCount()

        self.mutex.lock()
        remainder = self.totalRows - self.rowsLoaded
        if remainder == 0: # Fully loaded
            self.mutex.unlock()
            return
        if itemsToFetch is not None:
            itemsToFetch = min(remainder, itemsToFetch)
        else:
            itemsToFetch = min(remainder, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        # Insert he rows
        self.beginInsertRows(QModelIndex(), self.rowsLoaded, self.rowsLoaded + itemsToFetch - 1)
        # chunk = self.fetchChunk(self.rowsLoaded, itemsToFetch)
        self.rowsLoaded += itemsToFetch
        # self._dataActive = pd.concat([self._dataActive, chunk], ignore_index=True)
        self.endInsertRows()

        self.mutex.unlock()

    def removeRows(self, row: int, count: int, parent) -> bool:
        self.beginRemoveRows(parent, row, count)
        self._data = self._data.iloc[row:row+count]
        self.endRemoveRows()

    def data(self, index: QModelIndex, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        # if not self._dataReadUpdates and role != Qt.DisplayRole:
        #     return
        data: pd.DataFrame = self._data

        # if (self.firstColumn
        #     and role == Qt.CheckStateRole
        #     and index.column() == data.columns.get_loc(self.firstColumn)):
        #     checked = data.iloc[index.row()]["__checked__"]
        #     if checked:
        #         return Qt.CheckState.Checked
        #     else:
        #         return Qt.CheckState.Unchecked
        if role == Qt.EditRole:
            # v = self._dataActive.iloc[index.row(), index.column()]
            # v = self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            v = data.iloc[index.row(), index.column()]
            return v
        elif role == Qt.DisplayRole:
            if not self._dataReadUpdates:
                return super().data(index, role)
            # v = self._dataActive.iloc[index.row(), index.column()]
            # v = self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            v = data.iloc[index.row(), index.column()]
            if v is None or v == "":
                return ""
            fieldName = data.columns[index.column()]
            # Prioritize field map properties to determine display role
            try:
                if v is np.nan or np.isnan(v):
                    return ""
                propRound = self._fieldMap[fieldName]["round"]
                v = str(round(float(v), propRound))
                return v
            except Exception as e:
                pass # No round property. Ok

            try:
                t = is_numeric_dtype(v)
                if t:
                    if math.isnan(float(v)):
                        return None
                    return str(v.round(2))
            except Exception as e:
                pass

            try:
                if math.isnan(float(v)):
                    return ""
            except Exception as e:
                pass

            return str(v)

        elif role == Qt.UserRole:
            # return self._dataActive.iloc[index.row(), index.column()]
            # return self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            return data.iloc[index.row(), index.column()]
        # elif role == Qt.ItemDataRole.ForegroundRole:
        #     return QBrush(QColor("green"))
        elif role == Qt.ItemDataRole.BackgroundRole:
            fieldName = data.columns[index.column()]
            propBg = self._fieldMap.get(fieldName, {}).get("background")
            if not propBg:
                return super().data(index, role)
            return QBrush(QColor(propBg))
        elif role == Qt.DecorationRole:
            fieldName = data.columns[index.column()]
            if self._fieldMap.get(fieldName, {}).get("options"):
                if self._fieldMap.get("__dropdown_icon__") == (index.column(), index.row()):
                    return self.iconDropdown

        return None

    def reset(self):
        self.beginResetModel()
        self.endResetModel()

    def resetFill(self):
        self.beginResetModel()
        self.endResetModel()
        self.layoutChanged.emit()

    #     default_flags = super().flags(index)
    #     if not index.isValid():
    #         return default_flags

    #     # Check if the column (field ID) is editable
    #     field_id = self.col_index_to_field_id.get(index.column())
    #     if self.id_to_editable.get(field_id, False):
    #         return default_flags | Qt.ItemIsEditable
    #     else:
    #         return default_flags

    # def setData(self, index, value, role=Qt.EditRole):
    #     if role == Qt.EditRole and index.isValid():
    #         chunk_index = index.row() // self.CHUNK_SIZE * self.CHUNK_SIZE
    #         local_row_index = index.row() % self.CHUNK_SIZE

    #         if chunk_index not in self.data_cache:
    #             # If the chunk is not in cache, it indicates inconsistency; reloading it might be necessary
    #             self.data_cache[chunk_index] = self.data_source.fetch_chunk(chunk_index, self.CHUNK_SIZE)

    #         df_index = self.data_cache[chunk_index][local_row_index]['df_index']

    #         # Update the DataFrame with the new value
    #         column_id = str(self.col_index_to_field_id[index.column()])  # Ensure using the correct identifier
    #         self.data_source.df.at[df_index, column_id] = value

    #         # Update the cache with the new value
    #         self.data_cache[chunk_index][local_row_index][column_id] = value

    #         # Notify the view about the data change
    #         self.dataChanged.emit(index, index, [role])
    #         return True
    #     return False

class MyHeaderView(QHeaderView):
    """A logical first checkable column"""
    headerChecked = Signal(bool)
    def __init__(self,
                 parent,
                 orientation=Qt.Orientation.Horizontal,
                 checkboxStyle=GroupedTableFlags.HEADER_CHECKBOX_FIRST):
        super().__init__(orientation, parent)
        self._fieldFilters = {}
        self.setModel(parent.model())
        self.setSectionsClickable(False)
        self._sectionPosClicked = None
        self.pos = None
        self.state = QStyle.StateFlag.State_Enabled
        self._on = False
        self._checkOption: QStyleOptionButton = QStyleOptionButton()
        self._checkOption.rect = QRect(4, 7, 14, 14)
        self.checkboxStyle = checkboxStyle

        self.sectionResized.connect(self.onSectionResized)

    @property
    def sectionSizes(self):
        return [float(self.sectionSize(n)) for n in range(self.count())]

    def sectionText(self, section, role: Qt.ItemDataRole = Qt.ItemDataRole.UserRole):
        if isinstance(self.model(), CustomFilterProxyModel):
            return self.model().headerData(section, self.orientation(), role)

    def onSectionResized(self, logicalIndex: int, oldSize: int, newSize: int) -> None:
        self.update(self.currentIndex())
        # if logicalIndex < len(self._sectionSizes):
        #     self._sectionSizes[logicalIndex] = newSize
        # for idx, widget in self._header_widgets.items():
        #     if idx < logicalIndex:
        #         continue
            # widget.move(widget.x() + newSize - oldSize, widget.y())

    def mousePressEvent(self, event: QMouseEvent) -> None:
        self.pos = event.position().toPoint()
        return super().mousePressEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """Detect header checkbox toggling"""
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            p = event.position().toPoint()
            if event.button() == Qt.MouseButton.LeftButton:
                section = self.logicalIndexAt(p)
                if self.pos == p:
                    # Check if hit checkbox
                    hitrect = self.style().subElementRect(QStyle.SE_ItemViewItemCheckIndicator, self._checkOption, self)
                    if hitrect.contains(p):
                        self._on = not self._on
                        self.headerChecked.emit(self._on)
                        self.viewport().update()
                    elif section != -1:
                        self._sectionPosClicked = QCursor.pos()
                        self.sectionClicked.emit(section)
        else:
            p = event.position().toPoint()
            if event.button() == Qt.MouseButton.LeftButton:
                if self.pos == p:
                    section = self.logicalIndexAt(p)
                    if section != -1:
                        self._sectionPosClicked = QCursor.pos()
                        self.sectionClicked.emit(section)

        return super().mouseReleaseEvent(event)

    def setZoomRatio(self, ratio: float):
        sectionSizes = [n * ratio for n in self.sectionSizes]
        self.sectionResized.disconnect(self.onSectionResized)
        try:
            for idx, size in enumerate(sectionSizes):
                self.resizeSection(idx, int(size))
        finally:
            self.sectionResized.connect(self.onSectionResized)
        return None


class MyVerticalHeaderView(MyHeaderView):
    """A logical first checkable column"""
    headerChecked = Signal(bool)
    def __init__(self,
                 parent,
                 checkboxStyle=GroupedTableFlags.HEADER_CHECKBOX_FIRST):
        super().__init__(parent, orientation=Qt.Orientation.Vertical, checkboxStyle=checkboxStyle)

        self._fieldFilters = {}
        self._sectionPosClicked = None
        self.pos = None
        self.state = QStyle.StateFlag.State_Enabled
        self._on = False
        self._sectionSizes = np.zeros(0, dtype=np.float32)
        self._checkOption: QStyleOptionButton = QStyleOptionButton()
        self._checkOption.rect = QRect(4, 7, 14, 14)
        self.checkboxStyle = checkboxStyle

        self.sectionResized.connect(self.onSectionResized)


class MyHorizontalHeaderView(MyHeaderView):
    """A logical first checkable column"""
    headerChecked = Signal(bool)
    def __init__(self,
                 parent,
                 checkboxStyle=GroupedTableFlags.HEADER_CHECKBOX_FIRST):
        super().__init__(parent, orientation=Qt.Orientation.Horizontal, checkboxStyle=checkboxStyle)
        self._fieldFilters = {}
        self.setModel(parent.model())
        self.setSectionsClickable(False)
        self._sectionPosClicked = None
        self.pos = None
        self.state = QStyle.StateFlag.State_Enabled
        self._on = False
        self._sectionSizes = np.zeros(0, dtype=np.float32)
        self._checkOption: QStyleOptionButton = QStyleOptionButton()
        self._checkOption.rect = QRect(4, 7, 14, 14)
        self.checkboxStyle = checkboxStyle

        def scaledPixmap(name: str) -> QPixmap:
            p = get_resource_pixmap(name)
            return p.scaledToHeight(16, Qt.TransformationMode.SmoothTransformation)

        self.iconDefault = scaledPixmap("chevron-down.svg")
        self.iconFiltered = scaledPixmap("filter.svg")
        self.iconAz = scaledPixmap("icon-sort-down.svg")
        self.iconZa = scaledPixmap("icon-sort-up.svg")
        self.iconFilteredAz = scaledPixmap("filter-sort-down.svg")
        self.iconFilteredZa = scaledPixmap("filter-sort-up.svg")

        self.setFixedHeight(28)

        self.sectionResized.connect(self.onSectionResized)

    def paintSection(self, painter: QPainter, rect: QRect, logicalIndex: int) -> None:
        painter.save()
        r = super().paintSection(painter, rect, logicalIndex)
        painter.restore()
        painter.setRenderHint(painter.RenderHint.SmoothPixmapTransform)

        field = self.sectionText(logicalIndex)
        def get_icon(field):
            icon = None
            sortIcon = None
            for f in self._fieldFilters.get("sorting", []):
                if f[0] == field:
                    if f[1] == Qt.AscendingOrder:
                        sortIcon = self.iconAz
                        icon = self.iconFilteredAz
                    else:
                        sortIcon = self.iconZa
                        icon = self.iconFilteredZa
                    break
            if self._fieldFilters.get(field):
                if icon:
                    return icon
                else:
                    return self.iconFiltered
            elif sortIcon:
                return sortIcon
            else:
                return self.iconDefault

        pixmap = get_icon(field)
        x = self.sectionViewportPosition(logicalIndex)
        w = self.sectionSize(logicalIndex)
        self.style().drawItemPixmap(painter,
                                    QRect(x-3, 6, w, pixmap.height()),
                                    Qt.AlignmentFlag.AlignTrailing,
                                    pixmap)

        # Checkbox
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            self._checkOption.state = QStyle.StateFlag.State_None
            self._checkOption.state |= QStyle.StateFlag.State_Enabled
            if self._on:
                self._checkOption.state |= QStyle.StateFlag.State_On
            else:
                self._checkOption.state |= QStyle.StateFlag.State_Off

        return r


class CustomFilterProxyModel(QSortFilterProxyModel):

    sgnColumnRenamed = Signal(int, str)

    def __init__(self, parent, realModel, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.fieldFilters = {}  # {id: [ filter values ]} to hold filters for each field ID
        self.appliedFilters = {}
        self.loadFilters()

        self._allowRenameColumn = True
        self._unselectableColumns = set()

        self.realModel: PandasDataFrameModel = realModel
        self.setSourceModel(realModel)
        self.filteredModel = None

        self._cacheFieldIdToColumnIndex = []
        self._sortColumns = []

        self.ignore_list_widget_changes = False
        self.ignore_select_all_cb_changes = False

    def sourceModel(self) -> pd.DataFrame:
        return super().sourceModel()

    def setData(self, index, value, role):
        """TODO - needs improvements when factoring in deferred load"""
        if role == Qt.EditRole:
            value = None if value == "" else value
            self.sourceModel().setData(index, value, role)
            return True
        return False

    def setFieldFilter(self, fieldId, values: list):
        """Set filter values for a specific field ID"""
        self.fieldFilters[fieldId] = values
        self.parent().horizontalHeader()._fieldFilters = self.fieldFilters
        self.invalidateFilter()

    def filterIndexDataChanged(self, model, index: QModelIndex, value):
        """Reflect filter model changes in unfiltered model"""
        uid = model.index(index.row(), 0).data(Qt.ItemDataRole.UserRole)
        df = self.realModel._data
        realRow = df.loc[df['__uid__'] == int(uid)].iloc[0, 0]
        realIndex = self.realModel.index(realRow, index.column())

        self.realModel._data.iloc[realRow, index.column()] = value
        return
        # If row loading is deferred,
        if not realIndex.isValid():
            self.realModel._data.iloc[realRow, index.column()] = value
            return
        self.realModel.setData(realIndex, value, Qt.ItemDataRole.EditRole)

    def invalidateFilter(self):
        self._cacheFieldIdToColumnIndex = []
        for fieldId, _ in enumerate(self.fieldFilters.items()):
            column: int = self.findColumnIndexByFieldId(fieldId)
            self._cacheFieldIdToColumnIndex.append(column)

        if not self.fieldFilters and not self._sortColumns:
            # All filters have been removed so we show unfiltered
            self.filteredModel = None
            self.realModel._fieldFilters = {}
            self.setSourceModel(self.realModel)
        else:
            data: pd.DataFrame = self.realModel._data
            for fieldId, filterValues in self.fieldFilters.items():
                # Add float checks
                filterValues2 = set()
                for f in filterValues:
                    filterValues2.add(f)
                    try:
                        filterValues2.add(float(f))
                    except:
                        logger.info("Not a float so can safely ignore")

                data = data[data[fieldId].isin(filterValues2)]
            fieldMap = self.realModel.fieldMap

            fields = []
            ascending = []
            for f in self._sortColumns:
                fields.append(f[0])
                ascending.append(True if f[1] == Qt.AscendingOrder else False)

            if fields:
                data = data.sort_values(by=fields, ascending=ascending, na_position="first", key=natsort_keygen())

            self.filteredModel = LazyLoadingPandasDataFrameModel(data=data,
                                                                fieldMap=fieldMap,
                                                                firstColumn=self.realModel.firstColumn)
            # self.filteredModel.firstColumn = self.realModel.firstColumn
            filters = deepcopy(self.fieldFilters)
            filters["sorting"] = self._sortColumns
            self.parent().horizontalHeader()._fieldFilters = filters
            self.parent().frozenTable.horizontalHeader()._fieldFilters = filters
            self.filteredModel._fieldFilters = filters
            self.filteredModel.sgnIndexDataChanged.connect(self.filterIndexDataChanged)
            self.setSourceModel(self.filteredModel)

        super().invalidateFilter()

    def parseDate(self, text):
        for fmt in ("%Y-%m-%d", "%m/%d/%Y"):  # Add more formats as needed
            try:
                return datetime.strptime(text, fmt)
            except ValueError:
                pass
            return None

    def filterAcceptsRow(self, sourceRow, sourceParent) -> bool:
        return True

    def findColumnIndexByFieldId(self, fieldId):
        """If column return Truth array, then we have duplicate columns"""
        try:
            column = self.sourceModel()._data.columns.get_loc(fieldId)
            return column
        except Exception as e:
            return -1

    def lessThan(self, left, right):
        """
        Override the lessThan method to use the alphanumeric sorting.
        """
        leftData = self.sourceModel().data(left)
        rightData = self.sourceModel().data(right)
        leftKey = self.alphanumericSortKey(leftData)
        rightKey = self.alphanumericSortKey(rightData)
        return leftKey < rightKey

    def buildFilterMenu(self, parent, column: int, closeEventHandler=None) -> QMenu:
        """Creates a menu which displays a custom widget with filter controls"""
        sourceModel = self.realModel  # Get the source model from the proxy
        if sourceModel is None:
            print("Source model is not set for proxyModel.")
            return

        # Fetch the id and name from selected column
        fieldId = sourceModel.headerData(column, Qt.Horizontal, Qt.UserRole)
        if fieldId == "__uid__":
            return
        columnName = sourceModel.headerData(column, Qt.Horizontal, Qt.DisplayRole)

        self.menu = QMenu(parent)
        widgetAction: QWidgetAction = QWidgetAction(self.menu)
        self.menu.addAction(widgetAction)

        # UI for widget
        menuWidget = QWidget()
        menuWidget.setLayout(QVBoxLayout())

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        menuWidget.layout().addWidget(hbox)
        # Use column name and field ID as needed to build your filter menu
        self.columnNameLabel = QLineEdit()
        self.columnNameLabel.setClearButtonEnabled(False)
        self.columnNameLabel.setText(f"{columnName}")
        self.columnNameLabel.setEnabled(False)
        hbox.layout().addWidget(self.columnNameLabel)

        def onEditFinished():
            """Now check and update field filter"""
            self.sgnColumnRenamed.emit(column, self.columnNameLabel.text())

        def onEditColumn():
            if self.pbEditColumn.isChecked():
                self.columnNameLabel.setEnabled(True)
                self.columnNameLabel.setFocus()
                self.columnNameLabel.setSelection(0, len(self.columnNameLabel.text()))
            else:
                onEditFinished()
                self.columnNameLabel.setEnabled(False)

        pbEditColumn = None
        if self._allowRenameColumn:
            self.pbEditColumn = pbEditColumn = QPushButton("")
            pbEditColumn.setCheckable(True)
            pbEditColumn.setFixedWidth(32)
            pbEditColumn.setIcon(get_resource_qicon("edit.svg"))
            pbEditColumn.setToolTip("Rename field")
            self.columnNameLabel.returnPressed.connect(onEditFinished)
            pbEditColumn.clicked.connect(onEditColumn)
            hbox.layout().addWidget(pbEditColumn)

        # Sort A to Z Button
        btnContainer = QHBoxLayout()
        tbSortAZ = QPushButton()
        tbSortAZ.setIcon(get_resource_qicon("icon-sort-down.svg"))
        tbSortAZ.setText(" Sort A to Z")
        tbSortAZ.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        btnContainer.addWidget(tbSortAZ)

        # Sort Z to A Button
        tbSortZA = QPushButton()
        tbSortZA.setIcon(get_resource_qicon("icon-sort-up.svg"))
        tbSortZA.setText(" Sort Z to A")
        tbSortZA.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        btnContainer.addWidget(tbSortZA)
        menuWidget.layout().addLayout(btnContainer)

        # Remove Filter Button
        pbRemoveFilter = QPushButton()
        pbRemoveFilter.setIcon(get_resource_qicon("filter.svg"))
        pbRemoveFilter.setText(f" Remove filter from {columnName}")
        pbRemoveFilter.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        pbRemoveFilter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        menuWidget.layout().addWidget(pbRemoveFilter)

        self.searchFilter = QLineEdit()
        self.searchFilter.setPlaceholderText("Search...")
        self.searchFilter.setClearButtonEnabled(True)
        menuWidget.layout().addWidget(self.searchFilter)

        self.cbSelectAll = QCheckBox('Select All', menuWidget)
        self.cbSelectAll.setTristate(False)
        self.cbSelectAll.setChecked(True)
        self.cbSelectAll.setStyleSheet('margin-left: 5px; font: bold')
        self.cbSelectAll.stateChanged.connect(self.selectAllCheckChanged)
        menuWidget.layout().addWidget(self.cbSelectAll)

        self.listWidget = QListWidget(menuWidget)
        self.listWidget.setEditTriggers(QListWidget.NoEditTriggers)

        self.listWidget.itemChanged.connect(self.listviewCheckChanged)  # Connect to itemChanged for check state updates

        _filtered = self.fieldFilters.get(fieldId, [])
        uniqueValues: set = self.getUniqueColumnItems(column)
        uniqueValues = sorted(uniqueValues, key=self.alphanumericSortKey)
        for value in uniqueValues:
            item = QListWidgetItem(str(value) if value else "<< no value >>")
            item.setData(Qt.ItemDataRole.UserRole, value)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            if _filtered:
                item.setCheckState(Qt.CheckState.Checked if str(value) in _filtered else Qt.CheckState.Unchecked)
            else:
                item.setCheckState(Qt.CheckState.Checked)
            self.listWidget.addItem(item)

        menuWidget.layout().addWidget(self.listWidget)

        # TODO: Additional button for adding to filter
        pbApplyFilter = QPushButton("Apply Filter")
        pbOk = QPushButton("Ok")
        pbCancel = QPushButton("Cancel")

        def sortAscending():
            newKey = (fieldId, Qt.AscendingOrder)
            # If field already sorted, update the sort direction
            for n, key in enumerate(self._sortColumns):
                _field, order = key
                if _field == fieldId:
                    # Unsort if key already sorted in same direction
                    if order == newKey[1]:
                        del self._sortColumns[n]
                    else:
                        self._sortColumns[n] = newKey
                    self.invalidateFilter()
                    return
            # No key found, so add it
            self._sortColumns.append(newKey)
            self.invalidateFilter()

        def sortDescending():
            newKey = (fieldId, Qt.DescendingOrder)
            # If field already sorted, update the sort direction
            for n, key in enumerate(self._sortColumns):
                _field, order = key
                if _field == fieldId:
                     # Unsort if key already sorted in same direction
                    if order == newKey[1]:
                        del self._sortColumns[n]
                    else:
                        self._sortColumns[n] = newKey
                    self.invalidateFilter()
                    return
            # No key found, so add it
            self._sortColumns.append(newKey)
            self.invalidateFilter()

        # Connect Button Signals
        pbOk.clicked.connect(lambda: self.applyFilter(fieldId, self.listWidget, column, False, self.menu))
        pbApplyFilter.clicked.connect(lambda: self.applyFilter(fieldId, self.listWidget, column, True, self.menu))
        pbCancel.clicked.connect(self.menu.close)
        tbSortAZ.clicked.connect(sortAscending) # Connect the Sort A to Z button
        tbSortZA.clicked.connect(sortDescending) # Connect the Sort Z to A button

        def onRemoveFilter(fieldId):
            self.removeFilter(fieldId)
            self.menu.close()

        pbRemoveFilter.clicked.connect(lambda: onRemoveFilter(fieldId)) # Connect the Remove Filter button

        # Add buttons to layout
        btnContainer = QHBoxLayout()
        btnContainer.addWidget(pbOk)
        btnContainer.addWidget(pbCancel)
        menuWidget.layout().addWidget(pbApplyFilter)
        menuWidget.layout().addLayout(btnContainer)

        # Connect the searchEdit signal
        self.searchFilter.textChanged.connect(self.onSearchFilterTextChanged)

        def blockMouseEvent(event=None):
            pass # Prevent clicking on empty space closing the menu

        menuWidget.mousePressEvent = blockMouseEvent
        widgetAction.setDefaultWidget(menuWidget)
        self.menu.addAction(widgetAction)

        if closeEventHandler:
            self.menu.aboutToHide.connect(closeEventHandler)

        return self.menu

    def sort(self, column, order):
        self.sourceModel().sgnSort.emit(column, order)

    def removeFilter(self, fieldId):
        # Check if the field_id exists in the applied filters and remove it
        for n, key in enumerate(self._sortColumns):
            _field, order = key
            if _field == fieldId:
                del self._sortColumns[n]
                self.invalidateFilter()  # Refresh the filter view

        # Assuming you have some mechanism to track and save the applied filters,
        # you might want to update that here as well.
        if fieldId in self.appliedFilters:
            del self.appliedFilters[fieldId]
            self.saveFilters()  # Save the updated filters to persist the changes

        # Update the list items
        for index in range(self.listWidget.count()):
            item = self.listWidget.item(index)
            item.setCheckState(Qt.CheckState.Checked)

    def onSearchFilterTextChanged(self):
        """Hide item if not in filter"""
        text = self.searchFilter.text()
        for index in range(self.listWidget.count()):
            item = self.listWidget.item(index)
            item.setHidden(text.lower() not in item.text().lower())

    def getUniqueColumnItems(self, column: int):
        """Extract unique values from a column in the proxy model"""
        if self.filteredModel:
            df: pd.DataFrame = self.filteredModel._data
        else:
            df: pd.DataFrame = self.realModel._data
        uniqueValues = df.iloc[:, column].unique().tolist()
        return uniqueValues

    def alphanumericSortKey(self, text):
        """
        Generate a key for sorting strings that may contain numbers (including floats).
        """
        # Ensure s is a string to prevent TypeError in re.split
        text = str(text) if text is not None else ""
        convert = lambda text: int(text) if text.isdigit() else text
        # alphanum_key = lambda key: [ convert(c) for c in re.split('([0-9]+)', key) ]
        return [ convert(c) for c in re.split('([0-9]+)', text) ]

    def filterList(self, listWidget, text):
        for i in range(listWidget.count()):
            item = listWidget.item(i)
            item.setHidden(text.lower() not in item.text().lower())

    def applyFilter(self, fieldId, listWidget, column: int, keepOpen, menu):

        def getUserValue(i):
            userData = listWidget.item(i).data(Qt.UserRole)
            return str(userData) if userData is not None else None

        def getNumericUserValue(i):
            userData = listWidget.item(i).data(Qt.UserRole)
            if userData is not None:
                try:
                    return float(userData)
                except:
                    return None
            return None

        dtype = self.realModel._data[fieldId].dtype
        if is_numeric_dtype(dtype):
            selectedValues = [getNumericUserValue(i) for i in range(listWidget.count()) if listWidget.item(i).checkState() == Qt.Checked]
        else:
            selectedValues = [getUserValue(i) for i in range(listWidget.count()) if listWidget.item(i).checkState() == Qt.Checked]
        # Ensure the retrieval matches your model setup for storing/retrieving field IDs
        if fieldId:
            if selectedValues:
                self.appliedFilters[fieldId] = selectedValues
            elif fieldId in self.fieldFilters:
                del self.appliedFilters[fieldId]

            # No values are unselected? Remove from filtering
            if listWidget.count() == len(selectedValues):
                try:
                    del self.fieldFilters[fieldId]
                    self.sourceModel()._fieldFilters = self.fieldFilters
                    self.invalidateFilter()
                    return
                except KeyError:
                    pass
            else:
                # Implement the saveFilters method to persist filters as needed
                # Apply the filter to the proxy model
                self.setFieldFilter(fieldId, selectedValues)

            if not keepOpen:
                menu.close()  # Close the menu directly
        else:
            print("Error: Could not retrieve field ID for column")

    def selectAllCheckChanged(self, state):
        # Check if the signal is from user interaction with the checkbox itself,
        # not from programmatic changes to its state
        if not self.ignore_select_all_cb_changes:
            self.ignore_list_widget_changes = True  # Flag to ignore itemChanged signals during this operation

            checkState = Qt.Checked if self.cbSelectAll.isChecked() else Qt.Unchecked
            for index in range(self.listWidget.count()):
                item = self.listWidget.item(index)
                if not item.isHidden():  # Apply only to visible items
                    item.setCheckState(checkState)

            self.ignore_list_widget_changes = False
            self.updateSelectAllCheckbox()

    def listviewCheckChanged(self, item=None):
        # Ignore changes if flagged to do so (during bulk select/deselect operations)
        if not self.ignore_list_widget_changes:
            self.updateSelectAllCheckbox()

    def updateSelectAllCheckbox(self):
        self.ignore_select_all_cb_changes = True  # Prevent triggering selectAllCheckChanged

        checkedItems = [self.listWidget.item(i) for i in range(self.listWidget.count()) if not self.listWidget.item(i).isHidden()]
        allChecked = all(item.checkState() == Qt.Checked for item in checkedItems)
        anyChecked = any(item.checkState() == Qt.Checked for item in checkedItems)

        if allChecked:
            self.cbSelectAll.setTristate(False)
            self.cbSelectAll.setCheckState(Qt.Checked)
        elif anyChecked:
            self.cbSelectAll.setTristate(True)
            self.cbSelectAll.setCheckState(Qt.PartiallyChecked)
        else:
            self.cbSelectAll.setTristate(False)
            self.cbSelectAll.setCheckState(Qt.Unchecked)

        self.ignore_select_all_cb_changes = False  # Reset the flag after updating checkbox state

    def saveFilters(self):
        """TODO: Save filter state"""
        return
        with open("assets/filters.json", "w") as file:
            json.dump(self.appliedFilters, file)
            print("Filters Added")

    def loadFilters(self):
        """TODO: Restore filter state"""
        try:
            with open("assets/filters.json", "r") as file:
                self.appliedFilters = json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            self.appliedFilters = {}

    def clearFilter(self, column_index, menu):
        self.appliedFilters.pop(column_index, None)
        self.saveFilters()
        menu.close()

    def resetFilters(self):
        """Removes persisted filter file"""
        print("Filters Reset")
        self.appliedFilters = {}
        if os.path.exists("assets/filters.json"):
            os.remove("assets/filters.json")

    def flags(self, index: QModelIndex):
        """If column is hidden, make sure items are not selectable"""
        itemFlags = super().flags(index)
        if index.column() in self._unselectableColumns:
            return itemFlags & ~Qt.ItemIsSelectable

        return itemFlags

    def setColumnItemsSelectable(self, column: int, selectable: bool = True):
        if selectable:
            try:
                self._unselectableColumns.remove(column)
            except KeyError:
                pass
        else:
            self._unselectableColumns.add(column)

class FrozenTableView(QTableView):
    """Subset of table features"""

    def __init__(self, parent, checkboxStyle) -> None:
        super().__init__(parent=parent)
        self._checkboxStyle = checkboxStyle
        self.setHorizontalHeader(MyHorizontalHeaderView(self, self._checkboxStyle))
        self.setVerticalHeader(MyVerticalHeaderView(self, self._checkboxStyle))
        self._frozenEditorIndex = None
        self.setObjectName("frozenTable")
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollMode(self.ScrollMode.ScrollPerPixel)
        self.installEventFilter(self)
        self.setEditTriggers(self.EditTrigger.NoEditTriggers)
        self.doubleClicked.connect(self.onDoubleClicked)
        headers = self.horizontalHeader()
        headers.setContextMenuPolicy(Qt.CustomContextMenu)

    def horizontalHeader(self) -> MyHorizontalHeaderView:
        return super().horizontalHeader()

    def verticalHeader(self) -> MyHorizontalHeaderView:
        return super().verticalHeader()

    def getSwappedColumnNameOrder(self,
                                  excludeHidden=False,
                                  role=Qt.ItemDataRole.UserRole) -> list:
        """Use `x` section position to tell us column order"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            pos = self.horizontalHeader().sectionPosition(n)
            if excludeHidden:
                if self.isColumnHidden(n):
                    continue
            value = (self.model().headerData(n, Qt.Orientation.Horizontal, role), pos)
            columns.append(value)
        columns = sorted(columns, key=lambda k: k[1])
        columns = [c[0] for c in columns]
        return columns

    def setColumnOrder(self, columns: list):
        unfrozenOrder = self.getSwappedColumnNameOrder(excludeHidden=False)
        columnToIndex = {}
        fieldToIndex = {}
        for n, field in enumerate(unfrozenOrder):
            columnToIndex[field] = n
            fieldToIndex[n] = field

        for pos, field in enumerate(columns):
            v: int = pos
            fieldIdx = columnToIndex.get(field, None)
            if fieldIdx is None:
                continue
            if fieldIdx == pos: # Already in correct position
                continue
            movedField = fieldToIndex[v]
            self.horizontalHeader().swapSections(fieldIdx, v)

            # Update the mapping of the swapped sections
            columnToIndex[field] = v
            fieldToIndex[v] = field
            columnToIndex[movedField] = fieldIdx
            fieldToIndex[fieldIdx] = movedField

    def currentChanged(self, current: Union[QModelIndex, QPersistentModelIndex], previous: Union[QModelIndex, QPersistentModelIndex]) -> None:
        """Trigger editing on the selecting cell"""
        try:
            if self._frozenEditorIndex:
                delegate = self.itemDelegateForIndex(self._frozenEditorIndex)
                delegate.moveCurrentCellBy.disconnect()
                if isinstance(delegate, BaseDelegate):
                    delegate.commitAndCloseEditor()
                self.closePersistentEditor(self._frozenEditorIndex)

            self._frozenEditorIndex = None
        except Exception as e:
            logger.info(f"Could not close table delegate editor. {e}", exc_info=True)

        super().currentChanged(current, previous)

    def selectSingleIndex(self, index):
        self.selectionModel().select(index, QItemSelectionModel.SelectionFlag.ClearAndSelect)

    def moveCell(self, row: int, column: int):
        """Remember to account for swapped column order"""
        key = None
        if row == 1:
            key = Qt.Key.Key_Down
        elif row == -1:
            key = Qt.Key.Key_Up
        elif column == -1:
            key = Qt.Key.Key_Left
        elif column == 1:
            key = Qt.Key.Key_Right
        handmade = QKeyEvent(QEvent.Type.KeyPress, key, Qt.KeyboardModifier.NoModifier)
        self.keyPressEvent(handmade)

    def onDoubleClicked(self, event):
        self.startEditing(event)

    def startEditing(self, index, char=None):
        try:
            if self._frozenEditorIndex is not None:
                delegate = self.itemDelegateForIndex(self._frozenEditorIndex)
                if isinstance(delegate, BaseDelegate):
                    delegate.commitAndCloseEditor()
                    self.closePersistentEditor(self._frozenEditorIndex)
                    try:
                        delegate.cleanup()
                    except:
                        pass
            self._frozenEditorIndex = None

            delegateNew: BaseDelegate = self.itemDelegateForColumn(index.column())
            if delegateNew:
                delegateNew.moveCurrentCellBy.connect(self.moveCell)
                self.openPersistentEditor(index)
                if char:
                    delegateNew.setCurrentText(char)
                self._frozenEditorIndex = index
        except Exception as e:
            logger.info("Could not close table delegate editor. Can be due to model switch", exc_info=True)


class GroupedTableView(QTableView):
    """Custom TableView which supports filtering and lazy loading"""

    sgnSelectionChanged = Signal()
    sgnUpdateDf = Signal(object)
    rowContextMenuRequested = Signal(object)
    contextHeaderMenuClicked = Signal(QAction)
    zoomChanged = Signal(float)
    def __init__(self,
                 parent,
                 lazyloading: bool = True,
                 checkboxStyle:GroupedTableFlags = GroupedTableFlags.HEADER_CHECKBOX_FIRST) -> None:
        super().__init__(parent=parent)
        self.checkboxStyle = checkboxStyle
        self.setSelectionBehavior(self.SelectionBehavior.SelectItems)
        self.setHorizontalHeader(MyHorizontalHeaderView(self, checkboxStyle=checkboxStyle)) # Set it early as it seems to disappear without
        self.setVerticalHeader(MyVerticalHeaderView(self, checkboxStyle=checkboxStyle)) # Set it early as it seems to disappear without
        self._alwaysVisibleColumns = []
        self._forceHiddenColumns = []
        self._lazyloading: bool = lazyloading
        self._currentEditorIndex = None  # Used for keeping only one editor open
        self.firstSelected = None
        self.lastSelected: QModelIndex = None # For shift select
        self._fieldMap = {}
        self.frozenTable: FrozenTableView = self.initFreezePane()
        self.frozenColumns = None
        self.editFreezeColumns: list = None # Columns to freeze when editing cell
        self.cacheFrozenColumnWidth = {}
        self.cacheColumnWidth = {}
        self.cacheSwappedOrder = None
        self._cacheDefaultColumnOrder = None
        self._cacheColumnToIndex = {}
        self._cacheIndexToColumn = {}
        self._cacheSwappedColumnToIndex = {}
        self._cacheSwappedIndexToColumn = {}
        self._allowRenameColumn = True
        self._mouseDown = False
        self._mousePos = None
        self._autoScrollPos: QPoint = None
        self._autoScrollTimer: QTimer = QTimer(self)
        self._autoScrollTimer.timeout.connect(self.onAutoScroll)
        self._autoScrollTimer.setInterval(300)
        self.filterProxyModel: CustomFilterProxyModel = None
        self.clipboard: TableClipboard = TableClipboard()

        # Table, Font scaling
        self._defaultPointSize = None
        self._zoom = 1
        self.maxZoom = 10
        self.minZoom = -8

        self.sgnUpdateDf.connect(self.setTableData)
        self.mouseMoveEvent = self.tableMouseMoveEvent
        self.setMouseTracking(True) # We can disable this for read-only table
        self.horizontalHeader().sectionPressed.disconnect()
        self.verticalHeader().sectionPressed.disconnect()
        self.horizontalHeader().sectionMoved.connect(self.onHorizontalHeaderSectionMoved)
        self.verticalHeader().sectionPressed.connect(self.onVerticalHeaderSectionPressed)

        # Stop corner from selecting all
        self.findChild(QAbstractButton).setDisabled(True)

        # self.scroll = QScrollBar(self)
        # self.scroll.hide()
        # self.scroll.setValue(2000)
        # self.scroll.setMaximum(10000)
        # self.setVerticalScrollBar(self.scroll)
        # vscroll = VScrollbar(self)
        # vscroll.setFixedHeight(self.height())
        # vscroll.show()
        self.verticalScrollBar().valueChanged.connect(self.onVerticalScrollbar)
        self.horizontalScrollBar().valueChanged.connect(self.onHorizontalScrollbar)
        self.setHorizontalScrollMode(self.ScrollMode.ScrollPerPixel)
        self.setVerticalScrollMode(self.ScrollMode.ScrollPerPixel)

        self.horizontalScrollBar().setSingleStep(24)
        self.verticalScrollBar().setSingleStep(24)

        self.viewport().installEventFilter(self)
        self.setEditTriggers(self.EditTrigger.NoEditTriggers)
        self.doubleClicked.connect(self.onDoubleClicked)
        self.installEventFilter(self)

        self.onHorizontalScrollbar(self.horizontalScrollBar().value())

        self.verticalHeader().setSelectionBehavior(QHeaderView.SelectionBehavior.SelectRows)
        self.verticalHeader().setSectionsClickable(True)

        QApplication.clipboard().dataChanged.connect(self.onExternalClipboardChanged)

    @property
    def fieldMap(self):
        return self._fieldMap

    @fieldMap.setter
    def fieldMap(self, newFieldMap: dict):
        self._fieldMap = newFieldMap
        try:
            if self.model():
                self.model().realModel.fieldMap = newFieldMap
        except Exception as e:
            print(e)

    @property
    def defaultPointSize(self):
        return self._defaultPointSize

    def model(self) -> CustomFilterProxyModel:
        return super().model()

    def horizontalHeader(self) -> MyHorizontalHeaderView:
        return super().horizontalHeader()

    def getSwappedColumnIndex(self, column):
        """Returns the index of the column for swapped columns"""
        return column

    def currentChanged(self, current: QModelIndex, previous: QModelIndex) -> None:
        super().currentChanged(current, previous)

        # Scroll horizontally to ensure that current index is fully visible
        # accounting for the frozen table overlay.
        column = current.column()
        frozenTableWidth = self.frozenTable.width()
        x = self.columnViewportPosition(column)
        d = frozenTableWidth - x
        # Workaround - after filter, index resets to (0,0) so prevent
        # reset of horizontal scrollbar
        if previous.column() != -1 and frozenTableWidth > 2 and d > 0:
            scrollX = self.horizontalScrollBar().value()
            self.horizontalScrollBar().setValue(scrollX - d)

        self.closePersistentEditor(previous)

        # Update current index without deselecting selection
        self.selectionModel().setCurrentIndex(current, self.selectionModel().SelectionFlag.Select)

        # Helps with shift select starting from frozen into non-frozen
        self.updateCurrentIndexFocus()

    def moveCell(self, row: int, column: int):
        """Remember to account for swapped column order"""
        key = None
        if row == 1:
            key = Qt.Key.Key_Down
        elif row == -1:
            key = Qt.Key.Key_Up
        elif column == -1:
            key = Qt.Key.Key_Left
        elif column == 1:
            key = Qt.Key.Key_Right
        handmade = QKeyEvent(QEvent.Type.KeyPress, key, Qt.KeyboardModifier.NoModifier)
        self.keyPressEvent(handmade)

    def goToNextNonBlankCell(self, direction):

        def isCellBlank(v):
            return v is None or v == ""

        dx, dy = direction
        limit = max(self.model().columnCount(), self.model().rowCount()) # just to impose more safety
        index = self.currentIndex()
        row = index.row()
        if dy != 0:
            for n in range(1, limit + 1):
                r = row + (n * dy)
                index2 = self.model().index(r, index.column())
                data = index2.data(Qt.ItemDataRole.UserRole)
                if isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                self.updateCurrentIndexFocus()
                break
        elif dx != 0:
            swappedColumns = self.getSwappedColumnNameOrder(excludeHidden=True)
            columnName = self._cacheIndexToColumn[index.column()]
            columnIndex = swappedColumns.index(columnName)
            for n in range(1, limit + 1):
                c = columnIndex + (n * dx)
                if c == -1: # Prevent wrapping
                    return
                try:
                    nextColumnName = swappedColumns[c]
                    nextColumnIndex = self._cacheColumnToIndex[nextColumnName]
                except Exception as e:
                    return # Out of bounds. Nothing to check. All good
                index2 = self.model().index(row, nextColumnIndex)
                data = index2.data(Qt.ItemDataRole.UserRole)
                if isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                self.updateCurrentIndexFocus()
                break

    def goToNextBlankCell(self, direction):

        def isCellBlank(v):
            return v is None or v == ""

        dx, dy = direction
        limit = max(self.model().columnCount(), self.model().rowCount()) # just to impose more safety
        index = self.currentIndex()
        row = index.row()
        if dy != 0:
            for n in range(1, limit + 1):
                r = row + (n * dy)
                index2 = self.model().index(r, index.column())
                if not index2.isValid():
                    return
                data = index2.data(Qt.ItemDataRole.UserRole)
                if not isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                self.updateCurrentIndexFocus()
                break
        elif dx != 0:
            swappedColumns = self.getSwappedColumnNameOrder(excludeHidden=True)
            columnName = self._cacheIndexToColumn[index.column()]
            columnIndex = swappedColumns.index(columnName)
            for n in range(1, limit + 1):
                c = columnIndex + (n * dx)
                if c == -1: # Prevent wrapping
                    return
                try:
                    nextColumnName = swappedColumns[c]
                    nextColumnIndex = self._cacheColumnToIndex[nextColumnName]
                except Exception as e:
                    return # Out of bounds. Nothing to check. All good
                index2 = self.model().index(row, nextColumnIndex)
                data = index2.data(Qt.ItemDataRole.UserRole)
                if not isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                self.updateCurrentIndexFocus()
                break

    def copyFromRowAbove(self):
        if not self.selectedIndexes():
            return
        rowData = {}
        first = self.selectedIndexes()[0]
        # Grab the row data above the first selected index
        for index in self.selectedIndexes():
            if index.row() != first.row():
                continue
            r = index.row() - 1
            if r < 0: # Can't duplicate from invalid cell
                return
            indexAbove = self.model().index(r, index.column())
            data = indexAbove.data(Qt.ItemDataRole.UserRole)
            rowData[index.column()] = data

        for index in self.selectedIndexes():
            newData = rowData[index.column()]
            self.filterProxyModel.setData(index, newData, Qt.ItemDataRole.EditRole)
            self.update(index)

    def onDoubleClicked(self, event):
        self.hideCellDropdownIcon()
        self.startEditing(event)

    # def startEditing(self, index, char=None):
    def startEditing(self, index, char=None, deselect=False):
        """Render the item delegate. If an edited item is in the frozen column,
        switch to editing on the frozen table. Optional char value to replace the current text"""
        try:
            if self._currentEditorIndex:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                # if isinstance(delegate, ComboBoxDelegate) and delegate.isEditing():
                #     return False
                if isinstance(delegate, BaseDelegate):
                    try:
                        self.closePersistentEditor(self._currentEditorIndex)
                    except Exception:
                        pass
                self._currentEditorIndex = None

            def connectDelegate(delegate: BaseDelegate):
                delegate.editingCancelled.connect(self.onDelegateEditingCancelled)
                delegate.moveCurrentCellBy.connect(self.moveCell)
                delegate.modifierPressed.connect(self.delegateModifierPressed)
                delegate.showCellTooltip.connect(self.onShowCellTooltip)

            if self.isIndexFrozen(index):
                # delegateNew: BaseDelegate = self.frozenTable.itemDelegateForColumn(index.column())
                # # connectDelegate(delegateNew)
                # if delegateNew:
                frozenIndex = self.model().index(index.row(), index.column())
                self.frozenTable.startEditing(frozenIndex, char=char)
                delegate = self.frozenTable.itemDelegateForIndex(frozenIndex)
                self._currentEditorIndex = frozenIndex
                connectDelegate(delegate)
            else:
                delegateNew: BaseDelegate = self.itemDelegateForColumn(index.column())
                if delegateNew:
                    self.openPersistentEditor(index)
                    connectDelegate(delegateNew)
                    self._currentEditorIndex = index
                    if isinstance(delegateNew, ReadOnlyEditDelegate):
                        delegateNew.setCurrentText(self.model().data(index, Qt.ItemDataRole.DisplayRole))
                    elif char:
                        delegateNew.setCurrentText(char)

                    if deselect:
                        delegateNew.setCurrentText(self.model().data(index, Qt.ItemDataRole.DisplayRole))

        except Exception as e:
            logger.info(f"Failed initialize delegate editing {e}", exc_info=True)

    def selectionChanged(self, selected: QItemSelection, deselected: QItemSelection) -> None:
        """Mirror selections to frozen table"""
        self.sgnSelectionChanged.emit()
        super().selectionChanged(selected, deselected)

    def isIndexFrozen(self, index: QModelIndex) -> bool:
        if not self.frozenTable.isVisible():
            return False
        frozenIndices = [self.horizontalHeader().logicalIndex(n) for n in range(len(self.editFreezeColumns))]
        return index.column() in frozenIndices

    def getDataFrame(self, drop_uid: bool = True) -> pd.DataFrame:
        if not self.model():
            return None
        df = self.model().realModel._data
        if drop_uid:
            return df.drop('__uid__', axis=1)
        return df
        # return df.loc[:, df.columns != '__uid__']

    def isFiltered(self):
        if self.model():
            return self.model().filteredModel is not None
        return False

    def getFilteredDataFrame(self) -> pd.DataFrame:
        if not self.model():
            return None
        if not self.model().filteredModel:
            return None
        return self.model().filteredModel._data

    def getDataFrameAsDisplayed(self, exludehidden: bool=False) -> pd.DataFrame:
        """Returns the currently displayed filtered and sorted data"""
        if not self.model():
            return None
        df = self.getDataFrame(False)
        swapped = self.getSwappedColumnNameOrder(excludeHidden=exludehidden)
        swappedDisplay = self.getSwappedColumnNameOrder(excludeHidden=exludehidden, role=Qt.ItemDataRole.DisplayRole)
        df = df[swapped]
        df = df.set_axis(swappedDisplay, axis=1)
        try:
            df.drop('__uid__', axis=1)
        except KeyError:
            pass # might be excluded already so nothing to drop
        return df

    def clearAllItemDelegates(self):
        """Clear all delegates"""
        try:
            self.itemDelegate().closeEditor()
            self._currentEditorIndex = None
        except Exception as e:
            # Likely no editor is open so nothing to close
            pass
        if self.model():
            # Might not be necessary as we reset model
            for column in range(self.model().columnCount()):
                self.setItemDelegateForColumn(column, None)
            self.model().beginResetModel()
            self.model().endResetModel()
            self.filterProxyModel = None

    def clearTable(self):
        """Reset table"""
        self.clearAllItemDelegates()
        if self.horizontalHeader():
            try:
                self.horizontalHeader().disconnect(self.showHeaderContextMenu)
                self.horizontalHeader().disconnect(self.onHorizontalHeaderChecked)
            except:
                pass

    def setTableData(self, df: pd.DataFrame):
        self.clearTable()
        try:
            # Prevent emitting more than once
            self.horizontalHeader().customContextMenuRequested.disconnect(self.showHeaderContextMenu)
            self.horizontalHeader().sectionClicked.disconnect()
            self.horizontalHeader().headerChecked.disconnect()
        except:
            pass
        if not isinstance(self.horizontalHeader(), MyHorizontalHeaderView):
            self.setHorizontalHeader(MyHorizontalHeaderView(self, checkboxStyle=self.checkboxStyle))
        self.horizontalHeader().headerChecked.connect(self.onHorizontalHeaderChecked)
        self.horizontalHeader()._on = False

        if not df.empty:
            if "__uid__" in df.columns:
                df = df.drop('__uid__', axis=1)
            df.insert(0, '__uid__', range(0, len(df)))
            df["__checked__"] = False
            if self._lazyloading:
                model = LazyLoadingPandasDataFrameModel(df, self.fieldMap, parent=self)
            else:
                model = PandasDataFrameModel(df, self.fieldMap, parent=self)

            self.filterProxyModel = CustomFilterProxyModel(self, realModel=model)
            self.filterProxyModel._allowRenameColumn = self._allowRenameColumn
            self.setModel(self.filterProxyModel)  # This will directly set the model
            self.frozenTable.setModel(self.filterProxyModel)
            self.frozenTable.setSelectionModel(self.selectionModel())
            self.filterProxyModel.sgnColumnRenamed.connect(self.onColumnRenamed)

            # Horizontal header context menu handling
            self.horizontalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
            try:
                self.horizontalHeader().customContextMenuRequested.disconnect()
                self.horizontalHeader().sectionClicked.disconnect()
            except Exception as e:
                pass # Harmless exception
            self.horizontalHeader().customContextMenuRequested.connect(self.showColumnHeaderContextMenu)
            self.horizontalHeader().sectionClicked.connect(self.onHeaderLeftClicked)

            self.verticalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
            try:
                self.verticalHeader().customContextMenuRequested.disconnect()
            except Exception as e:
                pass
            # Vertical header context menu handling
            self.verticalHeader().customContextMenuRequested.connect(self._rowHeaderContextMenuRequested)

            columns = self.getDefaultColumnNameOrder()
            for field in columns:
                self.setLineEditDelegateForColumn(field)

        else:
            self.setModel(None)
            try:
                self.horizontalHeader().sectionClicked.disconnect()
            except Exception:
                pass # Safe exception as signal may not have any connections

    def showColumnHeaderContextMenu(self, position):
        menu = QMenu()
        section = self.horizontalHeader().logicalIndexAt(position)
        field = self.filterProxyModel.headerData(section, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
        editColumnOrder = menu.addAction(f"Edit Columns")
        hideColumn = menu.addAction(f"Hide Column")
        hideColumn.setData({"section": section, "field": field})
        if field in self._alwaysVisibleColumns:
            hideColumn.setDisabled(True)
        clearAllFilters = menu.addAction(f"Clear All Filters")
        ac: QAction = menu.exec(self.mapToGlobal(position))
        if hideColumn and ac == hideColumn:
            self.contextHeaderMenuClicked.emit(ac)
            self.refreshCheckboxColumn()
        elif ac == clearAllFilters:
            self.clearAllFilters()
            # self.contextHeaderMenuClicked.emit(ac)
            self.refreshCheckboxColumn()
        elif ac == editColumnOrder:
            self.contextHeaderMenuClicked.emit(ac)

    def _rowHeaderContextMenuRequested(self, position):
        self.rowContextMenuRequested.emit(position)

    def onColumnRenamed(self, column: int, name: str):
        """TODO: cleaner approach, handle this logic elsewhere"""
        try:
            fieldId = self.filterProxyModel.headerData(column, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
            fieldMapJson = getSavedFieldMapJson()
            # print(fieldId, fieldMapJson)
            for a in ["fields", "ancillary_fields", "rfq_fields"]:
                for field, v in fieldMapJson[a].items():
                    if field == fieldId:
                        v["display"] = name
                        break
            saveFieldMapJson(fieldMapJson)
            # print(fieldMapJson)
            pub.sendMessage("field-map-updated")
        except Exception as e:
            print(e)
            logger.info("Failed to rename column", exc_info=True)

    def onHeaderLeftClicked(self, columnIndex):
        self.horizontalHeader().sectionClicked.disconnect()
        pos = QCursor.pos()
        start = time.time()
        self.menu: QMenu = self.filterProxyModel.buildFilterMenu(self, columnIndex, self.onMenuClose)
        if not self.menu:
            self.onMenuClose()
            return True
        print(f"Build filter menu execution time: {time.time() - start}s")
        self.menu.exec(pos)

    def onMenuClose(self):
        self.horizontalHeader().sectionClicked.connect(self.onHeaderLeftClicked)

    def autoSizeColumns(self):
        max_column_width = 400
        min_column_width = 100  # Define a minimum column width
        width_multiplier = 15
        start = time.time()
        try:
            if self.model():
                df = self.model().sourceModel()._data[:80]
                for column in range(self.model().columnCount()):
                    field = self.model().headerData(column, Qt.Orientation.Horizontal, Qt.UserRole)
                    display = self.model().headerData(column, Qt.Orientation.Horizontal, Qt.DisplayRole)

                    try:
                        # Ensure the column data is of string type
                        if df[field].apply(lambda x: isinstance(x, (str, bytes))).all():
                            longest = max(int(df[field].str.normalize('NFKD').str.len().max()), len(display))
                        else:
                            # Handle non-string data by converting to string and calculating length
                            longest = max(int(df[field].astype(str).str.len().max()), len(display))
                    except Exception as e:
                        logger.warn("Could not find longest field for column width", exc_info=True)
                        longest = min_column_width
                    column_width = longest * width_multiplier  # Increase for readability
                    column_width = min(max(column_width, min_column_width), max_column_width)
                    self.setColumnWidth(column, column_width)
            #logger.info(f"Autosize columns time {time.time() - start}")
            print(f"\n\nAutosize columns time {time.time() - start}")
        except Exception as e:
            logger.warn(f"Error sizing contents: {e}", exc_info=True)

    def addColumn(self, column: str):
        assert isinstance(column, str)
        self.model().sourceModel().addColumn(column)

    def addColumns(self, columns: list = [], allowDuplicates: bool = False):
        """Append empty columns. List parameter is a list<str> of column names"""
        assert isinstance(columns, list)
        if allowDuplicates:
            self.model().sourceModel().addColumns(columns)
            return
        unique = [c for c in columns if c not in self.model().sourceModel()._data.columns]
        self.model().sourceModel().addColumns(unique)
        self.getDefaultColumnNameOrder() # update cache

    def setComboboxDelegateForColumn(self, field, options, showFractions=False):
        """This sets a ComboBox delegate for a specific column"""
        delegate = ComboBoxDelegate(self, options=options, filterModel=self.filterProxyModel, showFractions=showFractions)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)

    def setFloatDelegateForColumn(self, field, options, showFractions=False):
        """This sets a ComboBox delegate for a specific column"""
        raise NotImplementedError
        delegate = ComboBoxDelegate(self, options=options, filterModel=self.filterProxyModel, showFractions=showFractions)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)

    def setLineEditDelegateForColumn(self, field, readonly=False):
        """This sets a ComboBox delegate for a specific column"""
        # delegate = LineEditDelegate(self, options=options, filterModel=self.filterProxyModel)
        if field in READ_ONLY_FIELDS:
            delegate = ReadOnlyEditDelegate(self, filterModel=self.filterProxyModel)
        else:
            delegate = LineEditDelegate(self, filterModel=self.filterProxyModel)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)

    def getDefaultColumnNameOrder(self):
        """Header data will always remain the same. So this is the default order"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            name = self.model().headerData(n, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
            columns.append(name)
            self._cacheColumnToIndex[name] = n
            self._cacheIndexToColumn[n] = name
        self._cacheDefaultColumnOrder = columns
        return columns

    def getSwappedColumnNameOrder(self,
                                  excludeHidden=False,
                                  role=Qt.ItemDataRole.UserRole,
                                  cache: bool = True) -> list:
        """Use `x` section position to tell us column order. Maybe a better official way?"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            pos = self.horizontalHeader().sectionPosition(n)
            if excludeHidden:
                if self.isColumnHidden(n):
                    continue
            value = (self.model().headerData(n, Qt.Orientation.Horizontal, role), pos)
            columns.append(value)
        columns = sorted(columns, key=lambda k: k[1])
        columns = [c[0] for c in columns]
        if cache:
            for n, c in enumerate(columns):
                self._cacheSwappedColumnToIndex[c] = n
                self._cacheSwappedIndexToColumn[n] = c
        return columns

    def setColumnOrder(self, columns: list):
        """Not the most efficient but swaps until order is sorted"""
        defaultOrder = self.getDefaultColumnNameOrder()
        hidden = []
        columns = [c for c in columns if c in defaultOrder]
        # Append any columns not specified and hide them
        for c in defaultOrder:
            if c not in columns:
                columns.append(c)
                if c not in self._alwaysVisibleColumns:
                    hidden.append(c)
        # columns = [c for c in columns if c in defaultOrder]

        currentOrder = self.getSwappedColumnNameOrder()
        if len(columns) != len(defaultOrder):  # Should not fail
            raise ValueError("Number of columns should be same as current table column count")
        for n, c in enumerate(columns):
            index = currentOrder.index(c)
            if index == -1:
                continue
            if index == n:
                self.showColumn(n)
                continue
            # if c == "__uid__":
            #     print(index, n)

            self.horizontalHeader().swapSections(index, n)
            self.showColumn(n)
            currentOrder = self.getSwappedColumnNameOrder()

        self.setHiddenColumns(hidden)

    def restoreDefaultColumnOrder(self):
        self.setColumnOrder(self.getDefaultColumnNameOrder())

    def unhideAllColumns(self):
        for n in range(self.filterProxyModel.columnCount()):
            field = self.filterProxyModel.headerData(n, Qt.Orientation.Horizontal, Qt.UserRole)
            if field not in self._forceHiddenColumns:
                self.showColumn(n)

    def setHiddenColumns(self, hidden):
        if not self.filterProxyModel:
            return
        for n in range(self.filterProxyModel.columnCount()):
            field = self.filterProxyModel.headerData(n, Qt.Orientation.Horizontal, Qt.UserRole)
            if field in hidden:
                self.hideColumn(n)

    def frozenTableSelectionChanged(self, selected, deselected):
        """Mirror selections"""
        # if self.frozenTable.currentIndex().column() == -1:
        #     return
        super(FrozenTableView, self.frozenTable).selectionChanged(selected, deselected)
        # if self.selectionModel():
        #     self.selectionModel().select(selected, QItemSelectionModel.SelectionFlag.Select)
        #     self.selectionModel().select(deselected, QItemSelectionModel.SelectionFlag.Deselect)
        return super().selectionChanged(selected, deselected)

    def initFreezePane(self) -> FrozenTableView:
        self.frozenTable = FrozenTableView(self, checkboxStyle=self.checkboxStyle)
        self.frozenTable.hide()
        self.frozenTable.installEventFilter(self)

        # Horizontal header link
        self.horizontalHeader().sectionResized.connect(self.updateSectionWidth)
        self.verticalHeader().sectionResized.connect(self.updateSectionHeight)
        self.frozenTable.verticalScrollBar().valueChanged.connect(self.verticalScrollBar().setValue)
        self.verticalScrollBar().valueChanged.connect(self.frozenTable.verticalScrollBar().setValue)
        self.frozenTable.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.frozenTable.verticalHeader().hide()
        self.viewport().stackUnder(self.frozenTable)
        self.frozenTable.horizontalHeader().headerChecked.connect(self.onHorizontalHeaderChecked)
        self.frozenTable.horizontalHeader().sectionClicked.connect(self.onHeaderLeftClicked)

        self.setHorizontalScrollMode(self.ScrollMode.ScrollPerPixel)
        self.setVerticalScrollMode(self.ScrollMode.ScrollPerPixel)

        self.frozenTable.selectionChanged = self.frozenTableSelectionChanged
        self.frozenTable.mouseMoveEvent = self.frozenTableMouseMoveEvent
        return self.frozenTable

    def freezePane(self):
        """Display the frozen table"""
        if not self.editFreezeColumns:
            return
        if not self.frozenTable.isHidden():
            return
        # When frozen, swap columns to freeze at the start on normal table
        self.getDefaultColumnNameOrder() # call to update cache
        swappedColumnOrder = self.getSwappedColumnNameOrder(excludeHidden=True)
        self.cacheSwappedOrder = [c for c in swappedColumnOrder]
        validColumns = []
        for name in self.editFreezeColumns:
            try:
                self._cacheSwappedColumnToIndex[name]
                validColumns.append(name)
            except ValueError:
                pass
            except KeyError:
                pass

        if not validColumns:
            return

        for name in swappedColumnOrder:
            try:
                v = self._cacheColumnToIndex[name]
                self.cacheColumnWidth[name] = self.columnWidth(v)
            except ValueError:
                pass
            except KeyError:
                pass

        # Prepend the frozen columns
        for name in reversed(validColumns):
            del swappedColumnOrder[swappedColumnOrder.index(name)]
        for name in reversed(validColumns):
            swappedColumnOrder = [name] + swappedColumnOrder

        validColumnsIndex = []
        for name in validColumns:
            try:
                v = self._cacheColumnToIndex[name]  # Index on default, not swapped for frozen
                validColumnsIndex.append(v)
            except ValueError:
                pass
        self.setColumnOrder(swappedColumnOrder)

        for col in range(0, self.model().columnCount()):
            if col not in validColumnsIndex:
                self.frozenTable.setColumnHidden(col, True)
            else:
                delegate = self.itemDelegateForColumn(col)
                if isinstance(delegate, BaseDelegate):
                    # options = [d for d in delegate._options]
                    self.frozenTable.setItemDelegateForColumn(col, delegate.clone())

        self.frozenTable.setColumnOrder(validColumns)
        self.frozenColumns = validColumns
        self.horizontalHeader().sectionMoved.connect(self.onSectionMoved)
        self.horizontalHeader().sectionResized.connect(self.onColumnResize)
        self.frozenTable.horizontalHeader().sectionResized.connect(self.onFrozenColumnResize)
        # Restore column width to prevent reset
        self.frozenTable.show()
        max_column_width = 400
        min_column_width = 100  # Define a minimum column width
        width_multiplier = 15
        try:
            df = self.model().sourceModel()._data[:80]
            for _, field in enumerate(self.frozenColumns):
                try:
                    v = self._cacheColumnToIndex[field]
                    longest = max(int(df[field].astype(bytes).str.len().max()), len(field))
                    column_width = longest * width_multiplier  # Increase for readability
                    column_width = min(max(column_width, min_column_width), max_column_width)
                    self.frozenTable.setColumnWidth(v, column_width)
                except ValueError:
                    pass
        except Exception as e:
            logger.debug("No model to base column resize on")

        # So we can perform hitbox detection on frozen pane
        self.frozenTable.mousePressEvent = self.frozenMousePressEvent
        self.frozenTable.mouseReleaseEvent = self.frozenMouseReleaseEvent
        self.onFrozenColumnResize()
        self.onColumnResize()
        self.refreshCheckboxColumn()

    def onFrozenColumnResize(self):
        """When resizing columns on the frozen table, mirror onto the main"""
        columnOrder = self.getDefaultColumnNameOrder()
        for name in self.frozenColumns:
            try:
                v = columnOrder.index(name)
                width = self.frozenTable.columnWidth(v)
                self.setColumnWidth(v, width)
            except ValueError:
                pass
        self.updateFrozenTableGeometry()

    def onColumnResize(self):
        """When resizing columns on the main table, mirror onto the frozen"""
        # Mirror the column widths into the frozen table columns
        columnOrder = self.getDefaultColumnNameOrder()
        for n, name in enumerate(self.frozenColumns):
            try:
                v = columnOrder.index(name)
                width = self.columnWidth(v)
                # if self.frozenTable:
                self.frozenTable.setColumnWidth(v, width)
            except ValueError:
                pass
        self.updateFrozenTableGeometry()

    def updateSectionWidth(self, logicalIndex, oldSize, newSize):
        if logicalIndex == 0:
            self.frozenTable.setColumnWidth(0, newSize)
            self.updateFrozenTableGeometry()

    def updateSectionHeight(self, logicalIndex, oldSize, newSize):
        self.frozenTable.setRowHeight(logicalIndex, newSize)

    def resizeEvent(self, event):
        super(GroupedTableView, self).resizeEvent(event)
        self.updateFrozenTableGeometry()

    def getSumFrozenColumnWidth(self):
        if not self.frozenTable.isVisible():
            return 0
        sumColumnWidth = 0
        # for col in range(0, self.frozenTable.model().columnCount()):
        #     if self.frozenTable.isColumnHidden(col):
        #         continue
        #     sumColumnWidth += self.frozenTable.columnWidth(col)
        for col in range(0, self.frozenTable.model().columnCount()):
            if self.frozenTable.isColumnHidden(col):
                continue
            w = self.columnWidth(col)
            # if self.frozenTable.isVisible():
            #     w = max(200, w)
            self.setColumnWidth(col, w)
            sumColumnWidth += w
        return sumColumnWidth

    def updateFrozenTableGeometry(self):
        # if not self.frozenTable.isVisible():
        #     return
        if not self.frozenTable.model():
            return

        self.frozenTable.setGeometry(
                self.verticalHeader().width() + self.frameWidth(),
                self.frameWidth(), self.getSumFrozenColumnWidth() + 2,
                self.viewport().height() + self.horizontalHeader().height())

    def unFreezePane(self):
        if self.frozenTable and not self.frozenTable.isHidden():
            try:
                self.horizontalHeader().sectionMoved.disconnect(self.onSectionMoved)
                self.horizontalHeader().sectionResized.disconnect(self.onColumnResize)
                self.frozenTable.horizontalHeader().sectionResized.disconnect(self.onFrozenColumnResize)
            except Exception as e:
                print(e)
            # Cache frozen column widths for next reopen
            if self.editFreezeColumns:
                for name in self.editFreezeColumns:
                    try:
                        v = self._cacheColumnToIndex[name]
                        width = self.frozenTable.columnWidth(v)
                        self.cacheFrozenColumnWidth[name] = width
                    except ValueError:
                        pass
            self.frozenTable.hide()
            # Revert column order before freeze
            self.setColumnOrder(self.cacheSwappedOrder)
            for name in self.cacheSwappedOrder:
                try:
                    v = self._cacheColumnToIndex[name]
                    self.setColumnWidth(v, self.cacheColumnWidth.get(name, 100))
                except ValueError:
                    pass
            self.cacheSwappedOrder = None
        self.refreshCheckboxColumn()

    def setEditFreezeColumns(self, columns: list):
        """Set columns we want to freeze when editing"""
        self.editFreezeColumns = columns

    def selectSingleIndex(self, index):
        self.selectionModel().select(index, QItemSelectionModel.SelectionFlag.ClearAndSelect)

    def deleteSelection(self):
        """Clear selected cells"""
        if not self.selectedIndexes():
            return
        for index in self.selectedIndexes():
            try:
                delegate = self.itemDelegateForIndex(index)
                if isinstance(delegate, ReadOnlyEditDelegate):
                    continue
                self.filterProxyModel.setData(index, None, Qt.ItemDataRole.EditRole)
                self.update(index)
            except Exception as e:
                logger.info("Could not clear cell")

    def copySelection(self):
        """Copy selected cells"""
        if not self.selectedIndexes():
            return
        self.updateClipboardSelections(clearCells=False)

    def getSortedSelectedIndexes(self):
        """Return sorted selected indexes visual top left to bottom right"""
        indexes = self.selectedIndexes()

        def visualX(column) -> int:
            # Map column index to visual x
            columnName = self._cacheIndexToColumn[column]
            x = self._cacheSwappedColumnToIndex[columnName]
            return x

        return sorted(indexes, key=lambda idx: (visualX(idx.column()), idx.row()))

    def updateClipboardSelections(self, clearCells: bool = False):
        """Set the clipboard to the currently selected cells"""
        if True or not self._cacheSwappedColumnToIndex:
            #TODO more efficient cache
            self.getSwappedColumnNameOrder()
            self.getDefaultColumnNameOrder()

        values = []
        xs = set()
        ys = set()
        invalidSelect = False # If selection is not contiguous block, we dont allow

        # Sort selected indexes visual top left to bottom right
        for index in self.getSortedSelectedIndexes():
            columnName = self._cacheIndexToColumn[index.column()]
            mapX = self._cacheSwappedColumnToIndex[columnName]
            xs.add(mapX)
            ys.add(index.row())
            data = index.data(Qt.ItemDataRole.UserRole)
            values.append(data)

        width = len(xs)
        height = max(ys) - min(ys) + 1

        # Must be a contiguous block for valid copy
        invalidSelect = width * height == len(values)
        if not invalidSelect:
            QMessageBox.question(self,
                    "Information",
                    "This function cannot be used with current selection",
                    QMessageBox.Ok,
                    QMessageBox.Ok)
            return

        if clearCells:
            for index in self.selectedIndexes():
                delegate = self.itemDelegateForIndex(index)
                if isinstance(delegate, ReadOnlyEditDelegate):
                    continue
                self.filterProxyModel.setData(index, None, Qt.ItemDataRole.EditRole)
                self.update(index)

        shape = [width, height]
        self.clipboard.set(values, shape)

    def cutSelection(self):
        """Cut selected cells"""
        if not self.selectedIndexes():
            return
        self.updateClipboardSelections(clearCells=True)

    def canPasteFit(self, firstIndex):
        """Test paste, return result"""
        rows = self.filterProxyModel.rowCount()
        columns = len(self._cacheSwappedIndexToColumn)
        if not self.clipboard.shape:
            return False
        width, height = self.clipboard.shape
        tooTall = firstIndex.row() + height > rows
        columnName = self._cacheIndexToColumn[firstIndex.column()]
        nextSwappedIndex = self._cacheSwappedColumnToIndex[columnName]
        tooWide = nextSwappedIndex + width > columns
        if tooTall or tooWide:
            return False
        return True

    def pasteOneToManyCells(self, value):
        """Paste a single value to all the selected cells"""
        for index in self.selectedIndexes():
            try:
                self.filterProxyModel.setData(index, value, Qt.ItemDataRole.EditRole)
                self.update(index)
            except Exception as e:
                logger.info("Cannot paste item", exc_info=True)

    def pasteSelection(self):
        """Cut selected cells"""
        if not self.selectedIndexes():
            return
        if len(self.selectedIndexes()) > 1 and len(self.clipboard.data) == 1:
            self.pasteOneToManyCells(self.clipboard.data[0])

        first = self.getSortedSelectedIndexes()[0]
        startX = first.column()
        newSelections = []
        canPaste = self.canPasteFit(first)
        if not canPaste:
            resp = QMessageBox.question(self,
                                        'Confirm Paste',
                                        "Content of clipboard is greater than insertion space. Insert anyway?",
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.Yes)
            if resp == QMessageBox.No:
                return

        swappedColumns = self.getSwappedColumnNameOrder(excludeHidden=True)
        if self.clipboard.shape:
            clipboardData = self.clipboard.data
            width, height = self.clipboard.shape
            ignored = 0
            for x in range(width):
                # TODO - Ugly code. Cleaner way of mapping indexes
                columnName = self._cacheIndexToColumn[startX]
                nextSwappedIndex = self._cacheSwappedColumnToIndex[columnName] + x
                if nextSwappedIndex >= len(swappedColumns):
                    continue
                nextColumnName = swappedColumns[nextSwappedIndex]
                if not nextColumnName:
                    continue
                nextColumn = self._cacheColumnToIndex[nextColumnName]
                # TODO
                readOnly = nextColumnName in READ_ONLY_FIELDS
                startY = first.row()
                for y in range(height):
                    if startY + y > self.filterProxyModel.rowCount() - 1:
                        break
                    if y + (height * x) > len(clipboardData) - 1:
                        continue
                    v = clipboardData[y + (height * x)]
                    try:
                        indexPaste = self.model().index(startY+y, nextColumn)
                        if not readOnly:
                            self.filterProxyModel.setData(indexPaste, v, Qt.ItemDataRole.EditRole)
                            self.update(indexPaste)
                        newSelections.append(indexPaste)
                    except Exception as e:
                        ignored += 1
                        logger.info("Some values could not be pasted. Due to strict pandas data type. TODO?", exc_info=True)

            if ignored:
                QMessageBox.question(self,
                                'Paste result',
                                f"Invalid data types - Could not paste {ignored} values. ",
                                QMessageBox.Ok,
                                QMessageBox.Ok)

        # Select all newly pasted cells
        self.clearSelection()
        for n in newSelections:
            self.selectionModel().select(n, QItemSelectionModel.SelectionFlag.Select)

    def eventFilter(self, source, event: QEvent):

        if source == self.viewport():
            if event.type() == QEvent.Type.MouseButtonRelease:
                if self.frozenTable:
                    self.frozenTable.setDisabled(False)
                self._mouseDown = False
            elif event.type() == QEvent.Type.MouseButtonPress:
                return False  # TODO - test if this breaks any selection requirements
                # If selecting a cell after multiple selected, deselect the others
                if self.frozenTable:
                    self.frozenTable.setDisabled(True)

                if not self.selectionModel():
                    return False
                selectedIndexes = self.selectionModel().selectedIndexes()
                selectionCount = len(selectedIndexes)
                if not self._mouseDown and selectionCount > 0:
                    index = self.selectedIndexes()[0]
                    self.selectSingleIndex(index)
                self._mouseDown = True

        if source in [self, self.frozenTable]:
            if event.type() == QEvent.KeyPress:

                if QApplication.keyboardModifiers() == Qt.ShiftModifier:
                    if event.key() in [Qt.Key.Key_Left, Qt.Key.Key_Right, Qt.Key.Key_Up, Qt.Key.Key_Down]:
                        self.shiftSelect(event.key())
                        return True

                # Disable keys if table index is in frozen table
                index = self.currentIndex()
                frozenIndex = self.frozenTable.currentIndex().column()
                frozenActive = frozenIndex != -1
                if frozenActive:
                    pass
                # Control modifiers
                if QApplication.keyboardModifiers() == (Qt.ControlModifier | Qt.ShiftModifier):
                    # Go to next non-blank cell - Ctrl + Shift + Arrow
                    if event.key() == Qt.Key.Key_Left:
                        direction = [-1, 0]
                    elif event.key() == Qt.Key.Key_Right:
                        direction = [1, 0]
                    elif event.key() == Qt.Key.Key_Up:
                        direction = [0, -1]
                    elif event.key() == Qt.Key.Key_Down:
                        direction = [0, 1]
                    else:
                        return False
                    self.goToNextNonBlankCell(direction)
                    return True
                if QApplication.keyboardModifiers() == Qt.ControlModifier:
                    if event.key() == Qt.Key.Key_Space:
                        return True
                    if event.key() == Qt.Key.Key_C:
                        self.copySelection()
                    elif event.key() == Qt.Key.Key_X:
                        self.cutSelection()
                    elif event.key() == Qt.Key.Key_V:
                        self.pasteSelection()
                    elif event.key() == Qt.Key.Key_A:
                        self.selectAll()
                    elif event.key() == Qt.Key.Key_Tab:
                        return True
                    elif event.key() == Qt.Key.Key_Home:
                        # Scroll top left index and select
                        self.verticalScrollBar().setValue(self.verticalScrollBar().minimum())
                        self.horizontalScrollBar().setValue(self.horizontalScrollBar().minimum())
                        firstColumn = self.getSwappedColumnNameOrder(excludeHidden=True, cache=False)[0]
                        firstIndex = self.model().index(0, self._cacheColumnToIndex[firstColumn])
                        self.scrollTo(firstIndex)
                        self.selectionModel().select(firstIndex, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                        return True
                    elif event.key() == Qt.Key.Key_End:
                        # Scroll to bottom right index and select
                        self.verticalScrollBar().setValue(self.verticalScrollBar().maximum())
                        self.horizontalScrollBar().setValue(self.horizontalScrollBar().maximum())
                        lastColumn = self.getSwappedColumnNameOrder(excludeHidden=True, cache=False)[-1]
                        lastIndex = self.model().index(self.model().rowCount()-1, self._cacheColumnToIndex[lastColumn])
                        self.scrollTo(lastIndex)
                        self.selectionModel().select(lastIndex, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                        return True
                    elif event.key() == Qt.Key.Key_D:
                        # Copy from row above
                        self.copyFromRowAbove()
                        return True
                    else:
                        # Go to next blank cell
                        if event.key() == Qt.Key.Key_Left:
                            direction = [-1, 0]
                        elif event.key() == Qt.Key.Key_Right:
                            direction = [1, 0]
                        elif event.key() == Qt.Key.Key_Up:
                            direction = [0, -1]
                        elif event.key() == Qt.Key.Key_Down:
                            direction = [0, 1]
                        else:
                            return False
                        self.goToNextBlankCell(direction)
                        return True
                    return True

                direction = None
                if event.key() == Qt.Key.Key_Return:
                    if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                        self.goToNextYIndex()
                    elif QApplication.keyboardModifiers() == Qt.KeyboardModifier.ShiftModifier:
                        self.goToNextYIndex(reverse=True)
                    else:
                        return False
                    return True
                elif event.key() == Qt.Key.Key_Tab:
                    if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                        self.goToNextXIndex()
                        return True
                elif event.key() == Qt.Key.Key_Backtab: # Shift modifies the Key_Tab
                    if QApplication.keyboardModifiers() == Qt.KeyboardModifier.ShiftModifier:
                        self.goToNextXIndex(reverse=True)
                        return True

                elif event.key() == Qt.Key.Key_Delete:
                    self.deleteSelection()
                    return True

                elif event.key() == Qt.Key.Key_F2:
                    self.startEditing(self.currentIndex(), deselect=True)
                    return True

                # Start editing when typing alphanumeric and space
                # Override the starting text with this char
                char = event.text()
                if char == ' ' or char.isalpha() or char.isalnum():
                    # if source == self:
                    self.startEditing(self.currentIndex(), char=char)
                    return True

        return False

    def getSelectedRowData(self, includeUid=True):
        """TODO, currently return uid and associated id. Add ability to choose columns"""
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            return self.getCheckedSelections()
        else:
            # Old
            self.getDefaultColumnNameOrder()
            uidIndex = self._cacheColumnToIndex.get("__uid__")
            idIndex = self._cacheColumnToIndex.get("id")
            rows = set()
            uids = []
            ids = []
            selectedIndexes = []
            if self.frozenTable.isVisible():
                selectedIndexes = self.frozenTable.selectedIndexes()
            if not selectedIndexes:
                selectedIndexes =  self.selectedIndexes()
            for index in selectedIndexes:
                row = index.row()
                if row in rows:
                    continue
                rows.add(row)
                uid = self.filterProxyModel.index(index.row(), uidIndex).data(Qt.ItemDataRole.UserRole)
                if idIndex is not None:
                    _id = self.filterProxyModel.index(index.row(), idIndex).data(Qt.ItemDataRole.UserRole)
                    ids.append(_id)
                uids.append(uid)
            data = {
                "__uid__": uids,
            }
            if ids:
                data["id"] = ids
            df = pd.DataFrame(data)
            return df

    def onHorizontalHeaderChecked(self, checked: bool):
        """Select/deselect all table rows"""
        columnIndex = self._cacheColumnToIndex["__checked__"]
        self.frozenTable.horizontalHeader()._on = checked
        # Updates rows checkbox selection
        for n in range(self.model().rowCount()):
            index = self.model().index(n, columnIndex)
            self.model().setData(index, checked, Qt.EditRole)
        self.refreshCheckboxColumn()

    def onHorizontalHeaderSectionMoved(self, event):
        self.refreshCheckboxColumn()

    def refreshCheckboxColumn(self):
        """Detects the first column field so we can add a checkbox to the cell"""
        if self.checkboxStyle != GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            return
        firstField = None
        if self.frozenTable.isVisible() and self.frozenColumns:
            firstField = self.frozenColumns[0]
        else:
            for n in range(self.horizontalHeader().count()):
                if self.isColumnHidden(n):
                    continue
                pos = self.horizontalHeader().sectionPosition(n)
                if pos == 0:
                    firstField = self.model().headerData(n, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
                    break
        try:
            self.model().sourceModel().firstColumn = firstField
            self.frozenTable.model().firstColumn = firstField
            for n in range(self.model().rowCount()):
                index = self.model().index(n, self._cacheColumnToIndex[firstField])
                self.model().dataChanged.emit(index, index)
        except Exception as e:
            logger.info(e)
            if self.model():
                self.model().sourceModel().firstColumn = None

    def getCheckedSelections(self) -> pd.DataFrame:
        """Return dataframe of items which have been selected"""
        df = self.model().sourceModel()._data
        return df[df['__checked__'] == True]

    def mousePressEvent(self, event: QMouseEvent):
        if not self.selectionModel():
            return super().mousePressEvent(event)

        if event.button() == Qt.MouseButton.MiddleButton:
            self.autoScroll(event.position().toPoint())
            return

        super().mousePressEvent(event)
        if event.button() == Qt.MouseButton.LeftButton:
            self._mousePos = event.position().toPoint()
        elif event.button() == Qt.MouseButton.RightButton:
            self._mousePos = event.position().toPoint()

        if len(self.selectionModel().selectedIndexes()) == 1:
            self.firstSelected = self.selectionModel().selectedIndexes()[0]
        else:
            self.firstSelected = None

        self.lastSelected = self.currentIndex()
        self.updateCurrentIndexFocus()

    def mouseReleaseEvent(self, event: QMouseEvent):
        self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self._autoScrollPos = None
        # Checkbox selection check
        if event.button() == Qt.MouseButton.LeftButton:
            if self._mousePos:
                if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
                    pos = event.position().toPoint()
                    index = self.indexAt(pos)
                    sectionPosition = self.horizontalHeader().sectionPosition(index.column())
                    if sectionPosition == 0:
                        opt: QStyleOptionButton = QStyleOptionButton()
                        opt.rect = self.visualRect(index)
                        # Both mouse down and release must be inside hitbox
                        rect = self.style().subElementRect(QStyle.SubElement.SE_ItemViewItemCheckIndicator, opt)
                        if (rect.contains(pos)) and rect.contains(self._mousePos):
                            self.toggleRowChecked(index.row())

            self._mousePos = None

        super().mouseReleaseEvent(event)

        # Update focus, but avoid losing delegate focus on double click
        if not self._currentEditorIndex:
            self.updateCurrentIndexFocus()

    def frozenMousePressEvent(self, event: QMouseEvent):
        self.selectionModel().clear()
        self.frozenTable.selectionModel().clear()

        if event.button() == Qt.MouseButton.LeftButton:
            self._mousePos = event.position().toPoint()

        x = self.horizontalScrollBar().value() # Restore horizontal scroll position
        super(FrozenTableView, self.frozenTable).mousePressEvent(event)

        if len(self.selectionModel().selectedIndexes()) == 1:
            self.firstSelected = self.selectionModel().selectedIndexes()[0]
        else:
            self.firstSelected = None

        self.horizontalScrollBar().setValue(x)

    def frozenMouseReleaseEvent(self, event: QMouseEvent):
        # Checkbox selection check
        if event.button() == Qt.MouseButton.LeftButton:
            if self._mousePos:
                if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
                    pos = event.position().toPoint()
                    index = self.indexAt(pos)
                    sectionPosition = self.horizontalHeader().sectionPosition(index.column())
                    if sectionPosition == 0:
                        opt: QStyleOptionButton = QStyleOptionButton()
                        opt.rect = self.visualRect(index)
                        # Both mouse down and release must be inside hitbox
                        rect = self.style().subElementRect(QStyle.SubElement.SE_ItemViewItemCheckIndicator, opt)
                        if (rect.contains(pos)) and rect.contains(self._mousePos):
                            self.toggleRowChecked(index.row())

            self._mousePos = None
        super(FrozenTableView, self.frozenTable).mouseReleaseEvent(event)

    def toggleRowChecked(self, row):
        columnIndex = self._cacheColumnToIndex["__checked__"]
        index = self.model().index(row, columnIndex)
        checked = self.model().data(index, Qt.UserRole)
        self.model().setData(index, not checked, Qt.EditRole)

    def removeCheckedRows(self):
        excludeDf = self.getCheckedSelections()
        sourceDf = self.getDataFrame(drop_uid=False)
        newDf = sourceDf[~sourceDf.loc[:,'__uid__'].isin(excludeDf['__uid__'])]
        self.setTableData(newDf)
        self.refreshCheckboxColumn()
        if self.model():
            self.model().invalidateFilter() # This will reapply the filters

    def removeSelectedRows(self):
        excludeDf = self.getSelectedRowData()
        sourceDf = self.getDataFrame(drop_uid=False)
        newDf = sourceDf[~sourceDf.loc[:,'__uid__'].isin(excludeDf['__uid__'])]
        self.setTableData(newDf)
        self.refreshCheckboxColumn()
        if self.model():
            self.model().invalidateFilter() # This will reapply the filters

    def onSectionMoved(self, section, oldVisualIndex, newVisualIndex):
        """
        If a column is swapped with frozen column, alter the behavior to move to
        first unfrozen column
        """
        if self.frozenTable.isVisible():
            if newVisualIndex < len(self.editFreezeColumns):
                self.horizontalHeader().moveSection(newVisualIndex, len(self.editFreezeColumns))

    def onVerticalHeaderSectionPressed(self, row):
        # TODO - check if need to clear
        # self.selectionModel().clearSelection()
        # self.frozenTable.selectionModel().clearSelection()
        self.selectRow(row)

    def tableMouseMoveEvent(self, event: QMouseEvent):
        super().mouseMoveEvent(event)
        scrollX = self.horizontalScrollBar().value()
        scrollY = self.verticalScrollBar().value()
        if not self.model():
            return
        # Ensure first selected cell remains the active for a given drag selection
        if self.state() not in [self.State.ExpandingState,
                                self.State.EditingState,
                                self.State.CollapsingState,
                                self.State.DraggingState]:
            if event.buttons() and Qt.MouseButton.LeftButton:
                self.lastSelected = self.currentIndex()
                if self.firstSelected and self.firstSelected.isValid():
                    self.selectionModel().setCurrentIndex(self.firstSelected, self.selectionModel().SelectionFlag.NoUpdate)

        if not event.buttons():
            index = self.indexAt(event.pos())
            data = self.model().sourceModel()._data
            fieldName = data.columns[index.column()]
            if self._fieldMap.get(fieldName, {}).get("options"):
                self.model().sourceModel()._fieldMap["__dropdown_icon__"] = (index.column(), index.row())
            else:
                self.hideCellDropdownIcon()
        else:
            self.hideCellDropdownIcon()

        # Ensure that scrollbar is being moved as we mouse drag outside of window bounds
        self.horizontalScrollBar().setValue(scrollX)
        self.verticalScrollBar().setValue(scrollY)

    def frozenTableMouseMoveEvent(self, event: QMouseEvent):
        super(FrozenTableView, self.frozenTable).mouseMoveEvent(event)
        # Ensure first selected cell remains the active for a given drag selection
        if self.frozenTable.state() not in [self.State.ExpandingState,
                                self.State.EditingState,
                                self.State.CollapsingState,
                                self.State.DraggingState]:
            if event.buttons() and Qt.MouseButton.LeftButton:
                if self.firstSelected and self.firstSelected.isValid():
                    self.selectionModel().setCurrentIndex(self.firstSelected, self.selectionModel().SelectionFlag.NoUpdate)

        if not event.buttons():
            index = self.indexAt(event.pos())
            data = self.model().sourceModel()._data
            fieldName = data.columns[index.column()]
            if self._fieldMap.get(fieldName, {}).get("options"):
                self.model().sourceModel()._fieldMap["__dropdown_icon__"] = (index.column(), index.row())
            else:
                self.hideCellDropdownIcon()
        else:
            self.hideCellDropdownIcon()

    def viewportEvent(self, event):
        if event.type() == QEvent.Leave:
            self.hideCellDropdownIcon()
        return super().viewportEvent(event)

    def onHorizontalScrollbar(self, value: int):
        """To allow crossover selection from frozen to non-frozen table"""
        if value == 0:
            self.frozenTable.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, on=True)
        else:
            self.frozenTable.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, on=False)

        self.hideCellDropdownIcon()

    def onVerticalScrollbar(self, event):
        self.hideCellDropdownIcon()

    def hideCellDropdownIcon(self):
        """Hides the indicator when hovering over a cell with dropdown options"""
        try:
            self.model().sourceModel()._fieldMap["__dropdown_icon__"] = None
        except Exception as e:
            logger.debug(e)

    def toggleFreezePane(self):
        if self.frozenTable.isHidden():
            self.freezePane()
        else:
            self.unFreezePane()

        self.updateCurrentIndexFocus()

    # def frozenTableMoveCursor(self, cursorAction: QAbstractItemView.CursorAction, modifiers: Qt.KeyboardModifier) -> QModelIndex:
    #     current: QModelIndex = super().moveCursor(cursorAction, modifiers)
    #     if (cursorAction == Qt.MoveLeft and current.column() > 0
    #           and self.visualRect(current).topLeft().x() < self.frozenTable.columnWidth(0) ):
    #         newValue: int = self.horizontalScrollBar().value() + self.visualRect(current).topLeft().x() - self.frozenTable.columnWidth(0)
    #         self.horizontalScrollBar().setValue(newValue)

    #     return current

    def closePersistentEditor(self, index: QModelIndex) -> None:
        res = None
        delegate: BaseDelegate = None
        if self._currentEditorIndex:
            try:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                # delegate.cleanup()
                # delegate.save()
                delegate.commitAndCloseEditor()
                super().closePersistentEditor(index)
            except Exception as e:
                logger.info("Could not disconnect delegate signal")

            # If that fails, attempt to close delegate owned by the frozen table
            try:
                delegate = self.frozenTable.itemDelegateForIndex(self._currentEditorIndex)
                delegate.cleanup()
                # delegate.save()
                self.frozenTable.closePersistentEditor(self.frozenTable._frozenEditorIndex)
                self.frozenTable._frozenEditorIndex = None
            except Exception as e:
                logger.info("Could not disconnect delegate signal")

        self._currentEditorIndex = None

    def delegateModifierPressed(self, event):
        self.closePersistentEditor(self._currentEditorIndex)
        if event.key() == Qt.Key.Key_Return:
            if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                self.goToNextYIndex()
            elif QApplication.keyboardModifiers() == Qt.KeyboardModifier.ShiftModifier:
                self.goToNextYIndex(reverse=True)
        elif event.key() == Qt.Key.Key_Tab:
            if QApplication.keyboardModifiers() == Qt.KeyboardModifier.NoModifier:
                self.goToNextXIndex()
        elif event.key() == Qt.Key.Key_Backtab:
            if QApplication.keyboardModifiers() == Qt.KeyboardModifier.ShiftModifier:
                self.goToNextXIndex(reverse=True)

    def goToNextYIndex(self, reverse=False):
        """Select next index. Top to bottom, left to right. Cycle if end reached"""
        self.closePersistentEditor(self._currentEditorIndex)
        selectedIndexes = self.selectedIndexes() # This grabs visible selections only. And ignores hidden columns
        selectedIndexes.sort(key=lambda k: [self.horizontalHeader().sectionPosition(k.column()), k.row()])
        if reverse:
            selectedIndexes.reverse()
        selectedCount = len(selectedIndexes)
        index = self.currentIndex()

        nextIndex = None
        n = None
        nextN = None
        if selectedCount > 1:
            try:
                n = selectedIndexes.index(index)
                if n + 1 < selectedCount:
                    nextN = n + 1
                else:
                    nextN = 0
                nextIndex = selectedIndexes[nextN]
            except ValueError:
                nextN = 0
                nextIndex = selectedIndexes[nextN]

            if nextIndex:
                self.selectionModel().setCurrentIndex(nextIndex, QItemSelectionModel.SelectionFlag.NoUpdate)

        else:
            if not reverse:
                self.moveCell(1, 0)
            else:
                self.moveCell(-1, 0)

        self.updateCurrentIndexFocus()

    def goToNextXIndex(self, reverse=False):
        """
        Select next index. Top to bottom, left to right. Cycle if end reached.
        Optionally navigate in reverse order
        """
        self.closePersistentEditor(self._currentEditorIndex)
        selectedIndexes = self.selectedIndexes() # This grabs visible selections only. And ignores hidden columns
        selectedIndexes.sort(key=lambda k: [k.row(), self.horizontalHeader().sectionPosition(k.column())])
        if reverse:
            selectedIndexes.reverse()
        selectedCount = len(selectedIndexes)
        index = self.currentIndex()

        nextIndex = None
        n = None
        nextN = None
        if selectedCount > 1:
            try:
                n = selectedIndexes.index(index)
                if n + 1 < selectedCount:
                    nextN = n + 1
                else:
                    nextN = 0
                nextIndex = selectedIndexes[nextN]
            except ValueError:
                nextN = 0
                nextIndex = selectedIndexes[nextN]

            if nextIndex:
                self.selectionModel().setCurrentIndex(nextIndex, QItemSelectionModel.SelectionFlag.NoUpdate)

        else:
            if not reverse:
                self.moveCell(0, 1)
            else:
                self.moveCell(0, -1)

        self.updateCurrentIndexFocus()

    def updateCurrentIndexFocus(self):
        """
        If the current index is visible in the frozen table, make
        sure it has focus, otherwise active cell is hidden underneath
        """
        if not self.frozenTable.isVisible():
            return

        # Check if active index is a frozen column cell
        column = self.currentIndex().column()
        for name in self.editFreezeColumns:
            v = self._cacheColumnToIndex[name]
            if column == v:
                self.frozenTable.setFocus()
                return

        self.setFocus()

    def hideColumn(self, n):
        super().hideColumn(n)
        self.model().setColumnItemsSelectable(n, False)
        self.updateCurrentIndexFocus()

    def showColumn(self, n):
        # if not self.isColumnHidden(n):
        #     return
        super().showColumn(n)
        self.model().setColumnItemsSelectable(n, True)

    def clearAllFilters(self):
        self.filterProxyModel.fieldFilters = {}
        self.horizontalHeader()._fieldFilters = {}
        self.filterProxyModel._sortColumns = []
        self.filterProxyModel.invalidateFilter()

    def onDelegateEditingCancelled(self):
        """Escape pressed on delegate"""
        self.closePersistentEditor(self._currentEditorIndex)
        self._currentEditorIndex = None

    def onShowCellTooltip(self, message: str, pos: QPoint):
        """Shows a tooltip at the mouse cursor position"""
        if self._currentEditorIndex:
            x = self.columnViewportPosition(self._currentEditorIndex.column())
            QTimer.singleShot(100, lambda: QToolTip().showText(self.mapToGlobal(pos) + QPoint(72, 42),
                                    message,
                                    w=self,
                                    msecShowTime=1500))

    def selectAll(self):
        super().selectAll()
        # Select the bottom right index so we resize bounds from there
        c = self.horizontalHeader().logicalIndex(self.model().columnCount() - 1)
        index = self.model().index(self.model().rowCount() - 1, c)
        if index.isValid():
            self.lastSelected = index

    def shiftSelect(self, event: QKeyEvent):
        """Implement own shift select logic as two tables confuses selection with respect to widget focus"""
        selectedIndexes = self.selectionModel().selectedIndexes()
        if not selectedIndexes:
            return

        # This is different to the last index of a sorted order
        # We need this index to decide when to expand or shrink bounds
        lastSelected = self.lastSelected
        if not lastSelected or len(selectedIndexes) == 1:
            lastSelected = self.lastSelected = self.currentIndex()

        selectedIndexes.sort(key=lambda k: [self.horizontalHeader().sectionPosition(k.column()), k.row()])

        first = selectedIndexes[0]
        last = selectedIndexes[-1]

        # The selected rect top left to top bottom
        x1 = first.column()
        y1 = first.row()
        x2 = last.column()
        y2 = last.row()
        x1 = self.horizontalHeader().visualIndex(x1)
        x2 = self.horizontalHeader().visualIndex(x2)
        # print((x1, y1), (x2, y2))

        lx = lastSelected.column()
        ly = lastSelected.row()

        lx = self.horizontalHeader().visualIndex(lx)

        columnCount = self.horizontalHeader().count() - self.horizontalHeader().hiddenSectionCount()

        newLast = None
        newSelections = []
        if event == Qt.Key.Key_Left:
            if lx > x1:
                x = self.horizontalHeader().logicalIndex(lx)
            else:
                x = self.horizontalHeader().logicalIndex(lx - 1)
            height = range(y2 - y1 + 1)
            newLast = (lx - 1, ly)
            for y in height:
                index = self.model().index(y1 + y, x)
                newSelections.append(index)

        elif event == Qt.Key.Key_Right:
            height = range(y2 - y1 + 1)
            if lx < x2:
                x = self.horizontalHeader().logicalIndex(lx)
            else:
                x = self.horizontalHeader().logicalIndex(lx + 1)
            newLast = (min(columnCount, lx + 1), ly)
            for y in height:
                index = self.model().index(y1 + y, x)
                newSelections.append(index)

        elif event == Qt.Key.Key_Up:
            width = range(x2 - x1 + 1)
            if ly <= y1:
                y = ly - 1
            elif ly == y1 + 1:
                y = ly - 2
            else:
                y = y2
            newLast = (lx, y)
            for x in width:
                x = self.horizontalHeader().logicalIndex(x1 + x)
                index = self.model().index(y, x)
                newSelections.append(index)

        elif event == Qt.Key.Key_Down:
            width = range(x2 - x1 + 1)
            if ly < y2:
                y = ly
            else:
                y = y2 + 1
            newLast = (lx, ly + 1)
            if ly + 1 >= self.model().rowCount():
                newLast = (lx, ly)
            for x in width:
                x = self.horizontalHeader().logicalIndex(x1 + x)
                index = self.model().index(y, x)
                newSelections.append(index)

        logger.info("newLast Index - keep track for shift selection", newLast)
        newLast = self.model().index(newLast[1], self.horizontalHeader().logicalIndex(newLast[0]))
        if not newLast.isValid():
            return
        self.lastSelected = newLast
        self.keepIndexInView(self.lastSelected)

        # Toggle selections
        for s in newSelections:
            if index.isValid():
                self.selectionModel().select(s, self.selectionModel().SelectionFlag.Toggle)

    def keepIndexInView(self, index: QModelIndex):
        """Account for a number of index position to put it back in view"""
        if not index.isValid():
            return

        scrollX = self.horizontalScrollBar().value()
        scrollY = self.verticalScrollBar().value()

        x = self.columnViewportPosition(index.column())
        y = self.rowViewportPosition(index.row())

        # Scroll horizontally to ensure that current index is fully visible
        # accounting for the frozen table overlay.
        xd = self.frozenTable.width() - x
        if xd > 0:
            self.horizontalScrollBar().setValue(scrollX - xd)
            # return

        # Exceeded the right side
        width = self.columnWidth(index.column())
        xd = (x + width) - self.viewport().width()
        if xd > 0:
            self.horizontalScrollBar().setValue(scrollX + xd)
            # return

        # Beyond top
        height = self.rowHeight(index.row())
        yd = y
        if yd < 0:
            self.verticalScrollBar().setValue(scrollY + yd)
            # return

        # Beyond bottom
        height = self.rowHeight(index.row())
        yd = (y + height) - self.viewport().height()
        if yd > 0:
            self.verticalScrollBar().setValue(scrollY + yd)
            # return

    def wheelEvent(self, event: QWheelEvent) -> None:
        if QApplication.keyboardModifiers() == Qt.KeyboardModifier.ControlModifier:
            return self.scaleContents(event.angleDelta().y() / 120)

        return super().wheelEvent(event)

    def setZoom(self, value: float) -> None:
        if not 0.25 <= value <= 4.0:
            logger.debug("Zoom factor must between 0.25 and 4.0.")
            return

        if self.defaultPointSize is None:
            self._defaultPointSize = self.fontInfo().pointSize()

        # To keep table at the same position.
        zoomRatio = 1 / self.zoom * value
        pos = self.verticalScrollBar().sliderPosition()
        self.verticalScrollBar().setSliderPosition(int(pos * zoomRatio))
        pos = self.horizontalScrollBar().sliderPosition()
        self.horizontalScrollBar().setSliderPosition(int(pos * zoomRatio))

        # Zoom section size of headers
        self.horizontalHeader().setZoomRatio(zoomRatio)
        self.verticalHeader().setZoomRatio(zoomRatio)

        # Update table fonts
        self._zoom = value
        font = self.font()
        font.setPointSize(int(self.defaultPointSize * value))
        self.setFont(font)
        self.verticalHeader().setFont(font)
        self.horizontalHeader().setFont(font)

        # Update frozen table fonts
        self.frozenTable.setFont(font)
        self.frozenTable.verticalHeader().setFont(font)
        self.frozenTable.verticalHeader().setFont(font)

        self.zoomChanged.emit(self._zoom)

    @property
    def zoom(self) -> float:
        """Get current zoom factor."""
        return self._zoom

    def scaleContents(self, delta: int = 1):
        """Zoom in or out"""
        zoom = self.zoom + 0.05 * delta
        self.setZoom(min(max(zoom, 0.25), 4.0))
        return None

        scale = self.scale
        pointSize = self.fontInfo().pointSize()
        if self.defaultPointSize is None:
            self._defaultPointSize = pointSize

        columnDelta = 10
        rowDelta = 2

        if scaleIn:
            scale += 1
            scale = min(self.maxScale, scale)
        else:
            scale -= 1
            scale = max(self.minScale, scale)
            columnDelta = -columnDelta
            rowDelta = -rowDelta

        if scale == self.scale:
            return

        self.scale = scale
        newPointSize = self.defaultPointSize + scale
        print(scale, newPointSize)

        font = self.font()
        font.setPointSize(newPointSize)
        self.setFont(font)

        try:
            if self.model():
                for column in range(self.model().columnCount()):
                    columnWidth = self.columnWidth(column) + columnDelta
                    self.setColumnWidth(column, columnWidth)

                for row in range(self.model().rowCount()):
                    rowHeight = self.rowHeight(row) + rowDelta
                    self.setRowHeight(row, rowHeight)

        except Exception as e:
            logger.warn(f"Error sizing contents: {e}", exc_info=True)

        self.zoomChanged.emit(self.getScaleValue())

    def getScaleValue(self):
        print(self.defaultPointSize, self.fontInfo().pointSize())
        return self.fontInfo().pointSize() - self.defaultPointSize

    def autoScroll(self, startPos: QPoint):
        self._autoScrollPos = QCursor.pos()
        self.setCursor(QCursor(Qt.CursorShape.DragMoveCursor))
        self._autoScrollTimer.start()

    def onAutoScroll(self):
        """Auto scrolling based on current cursor position relative to start position"""
        if self._autoScrollPos is None:
            self._autoScrollTimer.stop()
            return
        delta = QCursor.pos() - self._autoScrollPos

        def getFactor(value) -> int:
            value = abs(value)
            if value > 1000:
                factor = 32
            elif value > 500:
                factor = 16
            elif value > 400:
                factor = 12
            elif value > 300:
                factor = 8
            elif value > 200:
                factor = 4
            elif value > 100:
                factor = 2
            elif value > 50:
                factor = 1.5
            elif value > 20:
                factor = 1
            else:
                factor = 0

            return factor

        hScroll = getFactor(delta.x()) * 16
        vScroll = getFactor(delta.y()) * 32

        # Depending on magnitude of x, y scrolling, we may
        # only want to scroll one scrollbar
        if abs(delta.x()) > abs(delta.y()) and abs(delta.x()) > 100 and abs(delta.y()) < 100:
            vScroll = 0

        if abs(delta.y()) > abs(delta.x()) and abs(delta.y()) > 100 and abs(delta.x()) < 100:
            hScroll = 0

        if delta.x() > 0:
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() + hScroll)
        elif delta.x() < 0:
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() - hScroll)

        if delta.y() > 0:
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() + vScroll)
        elif delta.y() < 0:
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() - vScroll)

    def onExternalClipboardChanged(self):
        """
        Clipboard text from the OS level has changed. Update
        the internal clipboard with this text
        """
        text = QApplication.clipboard().text()
        self.clipboard.set([text], [1, 1])
