"""
Allow user to select PDF from file explorer for new projectg
"""
from src.utils.logger import logger
from PySide6.QtWidgets import QToolButton, QFileDialog, QPushButton, QSizePolicy
from PySide6.QtGui import QAction
from PySide6.QtCore import Qt, QSize, Signal
from .baseform import BaseForm
from pubsub import pub
from src.pyside_util import get_resource_qicon

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 480

class NewProjectExplorerForm(BaseForm):

    sgnFilePicked = Signal(str)
    sgnFilePickedExisting = Signal(str)  # Add to existing project

    def __init__(self, parent):
        super().__init__(parent)
        self.filename = None

    def initUi(self):
        self.formSize.setHeight(FORM_SIZE)
        self.formSize.setWidth(WIDTH)
        self.title.setText("")
        self.subtitle.clear()
        self.subtitle.hide()

        self.addVSpace()

        self.pbSelectFile = QToolButton(self)
        self.pbSelectFile.setText("Choose File...")
        self.pbSelectFile.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.pbSelectFile.setIcon(get_resource_qicon("file-text.svg"))
        self.pbSelectFile.setIconSize(QSize(ICON_SIZE, ICON_SIZE))
        self.pbSelectFile.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.pbSelectFile.clicked.connect(self.onSelectFile)
        self.layout().addRow(self.pbSelectFile)

        self.addStretchSpacer()
        self.pbNext = QPushButton(self)
        self.pbNext.setText("Add To New")
        self.pbNext.clicked.connect(self.onNext)
        self.layout().addRow(self.pbNext)

        self.addVSpace()

        self.pbNextExisting = QPushButton(self)
        self.pbNextExisting.setText("Add To Existing")
        self.pbNextExisting.clicked.connect(self.onNextExisting)
        self.layout().addRow(self.pbNextExisting)

        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.pbSelectFile.setContentsMargins(0, 0, 0, 0)

        self.pbNext.setEnabled(False)
        self.pbNextExisting.setEnabled(False)

        self.setFloatingButtonCancel()

    def initDefaults(self):
        self.filename = None
        if self.filename:
            self.pbSelectFile.setText(self.filename)
        else:
            self.pbSelectFile.setText("Choose File...")

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onSelectFile(self, event):
        """ User chooses a PDF """
        filename, _ = QFileDialog.getOpenFileName(self,
                                                  "Open PDF File",
                                                  "",
                                                  "PDF Files (*.pdf);;All Files (*)")
        if filename:
            self.filename = filename
            self.pbSelectFile.setText(filename)
        else:
            self.pbSelectFile.setText("Choose File...")
            # self.sgnFilePicked.emit(filename)

        self.pbNextExisting.setEnabled(filename is not None)
        self.pbNext.setEnabled(filename is not None)

    def onFloatingButton(self):
        pub.sendMessage("goto-workspace-view", name="ProjectView")

    def onNext(self):
        if self.filename:
            self.sgnFilePicked.emit(self.filename)

    def onNextExisting(self):
        if not self.filename:
            return
        self.sgnFilePickedExisting.emit(self.filename)
