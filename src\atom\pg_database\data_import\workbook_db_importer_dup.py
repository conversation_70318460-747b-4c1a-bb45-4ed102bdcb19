"""
PostgreSQL Database Table Importer from Excel Workbooks.

This module provides functionality to import data from Excel workbooks into PostgreSQL database tables.
It handles data validation, transformation, and insertion/update operations with special attention to
data type requirements and precision specifications of the target database schema.
"""

import os
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
from openpyxl import load_workbook
from sqlalchemy import create_engine, inspect, Table, MetaData, Column, String, Integer, Float, DateTime, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pydantic import BaseModel, Field, field_validator, create_model
from datetime import datetime
import numpy as np

# Import database connection utilities from project
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor, execute_query, get_db_connection



# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# We'll still keep a SQLAlchemy URL for schema inspection
DEFAULT_DB_URL = "postgresql://{user}:{password}@{host}:{port}/{database}"

# Configure database connection string
# For now, we'll create a placeholder that can be overridden
# DEFAULT_DB_URL = "postgresql://username:password@localhost:5432/database_name"


class DataTypeValidator:
    """
    Utility class for validating and converting data types according to PostgreSQL schema requirements.
    """

    @staticmethod
    def validate_integer(value: Any) -> Optional[int]:
        """Validate and convert a value to integer."""
        if pd.isna(value):
            # For required fields, we need to indicate this clearly
            raise ValueError(f"Value is required but got NaN/empty value")
        try:
            return int(float(value))  # Convert to float first to handle values like "123.0"
        except (ValueError, TypeError):
            raise ValueError(f"Invalid integer value: {value}")

    @staticmethod
    def validate_decimal(value: Any, precision: int, scale: int) -> Optional[float]:
        """
        Validate and convert a value to a decimal with specific precision and scale.

        Args:
            value: The value to validate
            precision: Total number of digits
            scale: Number of digits after the decimal point

        Returns:
            Properly formatted decimal value or None if invalid
        """
        if pd.isna(value):
            return None

        try:
            # Convert to float first
            float_val = float(value)

            # Check if value exceeds precision
            str_val = f"{float_val:.{scale}f}"
            whole_digits = len(str_val.split('.')[0])

            if whole_digits > (precision - scale):
                raise ValueError(f"Value {value} exceeds precision {precision}/{scale}")

            # Return the properly formatted decimal
            return round(float_val, scale)

        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid decimal value: {value}. Error: {str(e)}")

    @staticmethod
    def validate_varchar(value: Any, max_length: int) -> Optional[str]:
        """Validate and convert a value to VARCHAR with specified max length."""
        if pd.isna(value):
            return None

        try:
            str_val = str(value).strip()
            if len(str_val) > max_length:
                logger.warning(f"String value '{str_val}' exceeds max length {max_length}")
                return str_val[:max_length]  # Truncate to max length
            return str_val
        except Exception as e:
            raise ValueError(f"Invalid string value: {value}. Error: {str(e)}")


class SchemaValidator:
    """
    Class for validating dataframe against a PostgreSQL table schema.
    """

    def __init__(self, table_name: str, db_config: DatabaseConfig):
        """
        Initialize the schema validator.

        Args:
            table_name: Fully qualified table name (schema.table)
            db_config: DatabaseConfig instance
        """
        self.table_name = table_name
        self.db_config = db_config
        self._load_schema()

    def _load_schema(self):
        """Load the database schema for the specified table."""
        schema_name, table_name = self._parse_table_name()

        try:
            # Use the project's database connection utilities
            with get_db_cursor(self.db_config) as cursor:
                query = f"SELECT column_name, data_type, character_maximum_length, numeric_precision, numeric_scale, is_nullable " \
                        f"FROM information_schema.columns WHERE table_schema = '{schema_name}' AND table_name = '{table_name}'"
                cursor.execute(query)
                columns = cursor.fetchall()

            if not columns:
                raise ValueError(f"No columns found for table {self.table_name}")

            logger.info(f"Loaded schema for {self.table_name} with {len(columns)} columns")

            # Create a mapping of column name to type information
            self.columns = []  # Store column info for create_pydantic_model
            self.column_types = {}
            for col in columns:
                col_name = col['column_name']
                col_type = col['data_type']
                is_nullable = col['is_nullable'] == 'YES'

                # Create a column info dict similar to SQLAlchemy format for compatibility
                col_info = {
                    'name': col_name,
                    'type': col_type,
                    'nullable': is_nullable
                }

                type_info = {
                    'type': col_type,
                    'nullable': is_nullable
                }

                # Extract precision and scale for DECIMAL types
                if col_type == 'numeric':
                    precision = col['numeric_precision']
                    scale = col['numeric_scale']
                    type_info['precision'] = precision
                    type_info['scale'] = scale

                # Get string length for VARCHAR types
                if col_type == 'character varying':
                    length = col['character_maximum_length']
                    type_info['length'] = length

                self.column_types[col_name] = type_info
                self.columns.append(col_info)

        except Exception as e:
            logger.error(f"Error loading schema for {self.table_name}: {str(e)}")
            raise

    def _parse_table_name(self) -> Tuple[Optional[str], str]:
        """Parse the fully qualified table name into schema and table components."""
        parts = self.table_name.split('.')
        if len(parts) == 1:
            return None, parts[0]
        elif len(parts) == 2:
            return parts[0], parts[1]
        else:
            raise ValueError(f"Invalid table name format: {self.table_name}")

    def create_pydantic_model(self):
        """
        Dynamically create a Pydantic model based on the database schema.

        Returns:
            A Pydantic model class for validating data against the schema
        """
        fields = {}
        validators = {}

        for col in self.columns:
            col_name = col['name']
            nullable = col.get('nullable', True)
            col_type = col['type']

            # Skip primary key and timestamp columns for imports
            if col_name in ('id', 'created_at', 'updated_at'):
                continue

            # Determine Python type and any validation needed
            if col_type == 'integer':
                field_type = Optional[int] if not nullable else int
                fields[col_name] = (field_type, Field(None))

                # Add validator
                validator_name = f'validate_{col_name}'
                validators[validator_name] = field_validator(col_name)(
                    lambda v, info: DataTypeValidator.validate_integer(v)
                )

            elif col_type == 'numeric':
                field_type = Optional[float] if not nullable else float
                precision = self.column_types[col_name]['precision']
                scale = self.column_types[col_name]['scale']

                fields[col_name] = (field_type, Field(None))

                # Add validator with closure to capture precision/scale
                def make_decimal_validator(name, prec, sc):
                    @field_validator(name)
                    def validate_decimal(cls, v, info):
                        return DataTypeValidator.validate_decimal(v, prec, sc)
                    return validate_decimal

                validators[f'validate_{col_name}'] = make_decimal_validator(col_name, precision, scale)

            elif col_type == 'character varying':
                field_type = Optional[str] if not nullable else str
                length = self.column_types[col_name]['length']

                fields[col_name] = (field_type, Field(None))

                # Add validator with closure to capture length
                def make_varchar_validator(name, max_length):
                    @field_validator(name)
                    def validate_varchar(cls, v, info):
                        return DataTypeValidator.validate_varchar(v, max_length)
                    return validate_varchar

                validators[f'validate_{col_name}'] = make_varchar_validator(col_name, length)

            else:
                # Default to string for other types
                field_type = Optional[str] if not nullable else str
                fields[col_name] = (field_type, Field(None))

        # Create the model dynamically
        model_name = ''.join(word.capitalize() for word in self.table_name.replace('.', '_').split('_')) + 'Model'
        model = create_model(
            model_name,
            **fields,
            __validators__=validators
        )

        return model

    def validate_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        Validate a dataframe against the table schema.

        Args:
            df: Pandas DataFrame containing the data to validate

        Returns:
            Tuple of (validated_df, error_records)
        """
        model = self.create_pydantic_model()
        valid_records = []
        error_records = []

        # Check for missing columns
        db_columns = set(self.column_types.keys())
        df_columns = set(df.columns)

        # Identify required columns (those that are not nullable)
        required_columns = {col for col, info in self.column_types.items()
                           if not info.get('nullable', True) and col not in {'id', 'created_at', 'updated_at'}}

        missing_columns = db_columns - df_columns - {'id', 'created_at', 'updated_at'}
        if missing_columns:
            logger.warning(f"Missing columns in input data: {missing_columns}")

            # If any required columns are missing, this is a critical error
            missing_required = missing_columns.intersection(required_columns)
            if missing_required:
                logger.error(f"Missing REQUIRED columns: {missing_required}")

        extra_columns = df_columns - db_columns
        if extra_columns:
            logger.warning(f"Extra columns in input data (will be ignored): {extra_columns}")

        # Pre-check for missing values in required columns
        for col in required_columns:
            if col in df.columns and df[col].isna().any():
                null_rows = df[df[col].isna()].index.tolist()
                logger.warning(f"Column '{col}' is required but has NULL values in rows: {null_rows}")

        # Process each row
        for idx, row in df.iterrows():
            try:
                # Check for missing required values before creating model
                missing_required_values = []
                for col in required_columns:
                    if col in row.index and pd.isna(row[col]):
                        missing_required_values.append(col)

                if missing_required_values:
                    raise ValueError(f"Required columns have NULL values: {', '.join(missing_required_values)}")

                # Convert row to dict, filtering out extra columns
                row_data = {col: row[col] for col in row.index if col in db_columns}

                # Validate with Pydantic model
                valid_data = model(**row_data).dict(exclude_none=True)
                valid_records.append(valid_data)

            except Exception as e:
                error_info = {
                    'row_index': idx,
                    'error': str(e),
                    'data': {k: str(v) if not pd.isna(v) else "NULL" for k, v in row.to_dict().items()}
                }
                error_records.append(error_info)
                logger.warning(f"Validation error in row {idx}: {e}")

        # Create a new dataframe from validated records
        if valid_records:
            validated_df = pd.DataFrame(valid_records)
        else:
            validated_df = pd.DataFrame(columns=list(db_columns - {'id', 'created_at', 'updated_at'}))

        # Log summary of validation results
        logger.info(f"Validation complete: {len(valid_records)} valid rows, {len(error_records)} errors")
        if error_records:
            error_summary = {}
            for err in error_records:
                error_msg = err['error']
                if error_msg not in error_summary:
                    error_summary[error_msg] = 1
                else:
                    error_summary[error_msg] += 1

            logger.info("Error summary:")
            for error, count in error_summary.items():
                logger.info(f"  - {error}: {count} occurrences")

        return validated_df, error_records


class WorkbookImporter:
    """
    Class for importing data from Excel workbooks into PostgreSQL database tables.
    """

    def __init__(self, db_config: DatabaseConfig):
        """
        Initialize the workbook importer.

        Args:
            db_config: DatabaseConfig instance
        """
        self.db_config = db_config

    def import_workbook(
        self,
        table_name: str,
        workbook_path: str,
        sheet_name: Optional[str] = None,
        key_columns: Optional[List[str]] = None,
        batch_size: int = 100,
        dataframe: Optional[pd.DataFrame] = None
    ) -> Dict:
        """
        Import data from an Excel workbook into a database table.

        Args:
            table_name: Fully qualified table name (schema.table)
            workbook_path: Path to the Excel workbook
            sheet_name: Name of the sheet to import (optional)
            key_columns: List of columns to use as identifying keys for updates
            batch_size: Number of records to process in each batch
            dataframe: Optional dataframe to import instead of reading from workbook

        Returns:
            Dictionary with import statistics
        """
        # Validate workbook exists
        if not os.path.exists(workbook_path):
            raise FileNotFoundError(f"Workbook not found: {workbook_path}")

        logger.info(f"Loading workbook: {workbook_path}")

        # Track statistics
        stats = {
            'total_rows': 0,
            'valid_rows': 0,
            'error_rows': 0,
            'inserted': 0,
            'updated': 0,
            'errors': []
        }

        try:
            # 1. Test database connection
            logger.info("Testing database connection...")
            try:
                with get_db_connection(self.db_config) as conn:
                    logger.info(f"Database connection successful.")
                    logger.info(f"PostgreSQL server version: {conn.server_version}")

                    # Disable triggers before processing any data
                    with conn.cursor() as cursor:
                        logger.info("Disabling triggers for import operation...")
                        cursor.execute("SET session_replication_role = 'replica';")
                        conn.commit()
            except Exception as e:
                logger.error(f"Error connecting to database: {e}")
                raise RuntimeError(f"Database connection failed: {e}")

            # 2. Load Excel file
            logger.info(f"Reading Excel file: {workbook_path}")
            try:
                if dataframe is None:
                    if sheet_name:
                        logger.info(f"Reading sheet: {sheet_name}")
                        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
                    else:
                        # If no sheet specified, use the first sheet
                        logger.info("No sheet specified, using first sheet")
                        df = pd.read_excel(workbook_path)
                else:
                    df = dataframe

                stats['total_rows'] = len(df)
                logger.info(f"Loaded {stats['total_rows']} rows from workbook")
                logger.info(f"Columns in workbook: {', '.join(df.columns.tolist())}")
            except Exception as e:
                logger.error(f"Error reading Excel file: {e}")
                raise RuntimeError(f"Failed to read Excel file: {e}")

            # 3. Clean column names
            df.columns = [str(col).strip() for col in df.columns]

            # 4. Initialize schema validator
            logger.info(f"Validating data against schema for {table_name}")
            try:
                validator = SchemaValidator(table_name, self.db_config)
            except Exception as e:
                logger.error(f"Error initializing schema validator: {e}")
                raise RuntimeError(f"Failed to initialize schema validator: {e}")

            # 5. Validate the data
            try:
                validated_df, errors = validator.validate_dataframe(df)
                stats['valid_rows'] = len(validated_df)
                stats['error_rows'] = len(errors)
                stats['errors'] = errors
                logger.info(f"Validation completed: {stats['valid_rows']} valid rows, {stats['error_rows']} errors")
            except Exception as e:
                logger.error(f"Error during data validation: {e}")
                raise RuntimeError(f"Data validation failed: {e}")

            # 6. If no valid records, return early
            if stats['valid_rows'] == 0:
                logger.warning("No valid records found in workbook")
                return stats

            # 7. Insert or update the data
            logger.info(f"Beginning database import to {table_name}")
            schema_name, table_name_only = table_name.split('.') if '.' in table_name else (None, table_name)

            # 8. Process in batches
            batch_count = 0
            for i in range(0, len(validated_df), batch_size):
                batch_count += 1
                batch_df = validated_df.iloc[i:i+batch_size]
                logger.info(f"Processing batch {batch_count} with {len(batch_df)} records")

                for _, row in batch_df.iterrows():
                    # Convert row to dict
                    row_dict = row.to_dict()

                    # Remove None values
                    row_dict = {k: v for k, v in row_dict.items() if pd.notna(v)}

                    # Check if record exists (if key columns provided)
                    if key_columns:
                        # Build filter condition
                        filter_conditions = []
                        for key in key_columns:
                            if key in row_dict:
                                value = row_dict[key]
                                if isinstance(value, str):
                                    filter_conditions.append(f"""{key} = '{value.replace("'", "''")}'""")

                                else:
                                    filter_conditions.append(f"{key} = {value}")

                        if filter_conditions:
                            # Try to update existing record
                            try:
                                # Check if record exists
                                check_query = f"SELECT COUNT(*) FROM {table_name} WHERE {' AND '.join(filter_conditions)}"
                                result = execute_query(check_query, config=self.db_config, fetch="one")
                                count = result['count'] if isinstance(result, dict) else result[0]

                                if count > 0:
                                    # Update existing record
                                    update_cols = []
                                    for k, v in row_dict.items():
                                        if k not in key_columns:  # Don't update key columns
                                            if isinstance(v, str):
                                                update_cols.append(f"""{k} = '{v.replace("'", "''")}'""")


                                            elif isinstance(v, (int, float)):
                                                update_cols.append(f"{k} = {v}")

                                    if update_cols:
                                        update_query = f"UPDATE {table_name} SET {', '.join(update_cols)} WHERE {' AND '.join(filter_conditions)}"
                                        execute_query(update_query, config=self.db_config, fetch=None)
                                        stats['updated'] += 1
                                        continue  # Skip to next record
                            except Exception as e:
                                logger.error(f"Error checking/updating record: {e}")
                                stats['errors'].append(f"Update error: {e}")
                                continue

                    # If we get here, insert a new record
                    try:
                        cols = ', '.join(row_dict.keys())
                        vals = []
                        for v in row_dict.values():
                            if isinstance(v, str):
                                vals.append(f"""'{v.replace("'", "''")}'""")


                            elif isinstance(v, (int, float)):
                                vals.append(str(v))
                            else:
                                vals.append("NULL")

                        insert_query = f"INSERT INTO {table_name} ({cols}) VALUES ({', '.join(vals)})"
                        execute_query(insert_query, config=self.db_config, fetch=None)
                        stats['inserted'] += 1
                    except Exception as e:
                        logger.error(f"Error inserting record: {e}")
                        logger.error(f"Failed query: {insert_query if 'insert_query' in locals() else 'N/A'}")
                        stats['errors'].append(f"Insert error: {e}")
                        continue

                logger.info(f"Batch {batch_count} processed: {stats['inserted']} inserts, {stats['updated']} updates")


            # After processing all batches, re-enable triggers
            try:
                with get_db_connection(self.db_config) as conn:
                    with conn.cursor() as cursor:
                        logger.info("Re-enabling triggers after import operation...")
                        cursor.execute("SET session_replication_role = 'origin';")
                        conn.commit()
            except Exception as e:
                logger.error(f"Error re-enabling triggers: {e}")
                # Continue with returning stats, don't raise here

            logger.info(f"Import completed successfully")

            return stats

        except Exception as e:
            logger.error(f"Error during workbook import: {e}")

            # IMPORTANT: Make sure to re-enable triggers even if there's an error
            try:
                with get_db_connection(self.db_config) as conn:
                    with conn.cursor() as cursor:
                        logger.info("Re-enabling triggers after error...")
                        cursor.execute("SET session_replication_role = 'origin';")
                        conn.commit()
            except Exception as re_enable_error:
                logger.error(f"Error re-enabling triggers after error: {re_enable_error}")

            stats['errors'].append(f"Import error: {e}")
            return stats


def import_flange_data(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import flange data from a workbook into the public.flange_data table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.flange_data"

    # Key columns used to identify existing records
    key_columns = ["profile_id", "size_in", "lbs"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        if 'lbs' not in df.columns:
            logger.error("Required column 'lbs' not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': "Required column 'lbs' missing from Excel"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing lbs values - identify rows with NaN in required fields
        missing_lbs_rows = df[df['lbs'].isna()].index.tolist()
        if missing_lbs_rows:
            logger.warning(f"Found {len(missing_lbs_rows)} rows with missing 'lbs' values")
            logger.warning(f"These rows will be skipped: {missing_lbs_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=['lbs'])
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = ['size_in', 'size_mm', 'lbs', 'o_in', 'c_in', 'o_mm', 'c_mm',
                        'bolt_circle_mm', 'bolt_circle_in', 'no_holes', 'hole_size_in', 'hole_size_mm']

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Save pre-processed data to a temporary Excel file if needed
        # df.to_excel(f"{workbook_path}.cleaned.xlsx", index=False)

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_lbs_rows) if missing_lbs_rows else 0

        # Log results
        logger.info(f"Flange data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data: {e}")
        raise


def import_tee_reducing(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import reducing tee data from a workbook into the public.tee_reducing table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.tee_reducing"

    # Key columns used to identify existing records
    key_columns = ["client_id", "profile", "size1", "size2", "size3"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for tee_reducing from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['client_id', 'client', 'profile', 'size1', 'size2', 'size3']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['client_id'].isna() |
            df['client'].isna() |
            df['profile'].isna() |
            df['size1'].isna() |
            df['size2'].isna() |
            df['size3'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'client_id', 'size1', 'size2', 'size3',
            'run_mm', 'run_in', 'outlet_mm', 'outlet_in',
            'run_c_mm', 'run_c_in', 'outlet_m_mm', 'outlet_m_in',
            'length_in', 'length_ft', 'length_mm',
            'area_mm', 'area_in', 'area_ft'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['client_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"Tee reducing data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for tee_reducing: {e}")
        raise


def import_standard_fittings(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import standard fittings data from a workbook into the public.standard_fittings table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.standard_fittings"

    # Key columns used to identify existing records
    key_columns = ["client_id", "client", "profile", "lookup_category", "size1"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for standard_fittings from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['client_id', 'client', 'profile', 'lookup_category', 'size1']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['client_id'].isna() |
            df['client'].isna() |
            df['profile'].isna() |
            df['lookup_category'].isna() |
            df['size1'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure lookup_category is a string - this addresses the validation error
        if 'lookup_category' in df.columns:
            df['lookup_category'] = df['lookup_category'].astype(str)
            logger.info("Converted lookup_category to string to fix validation errors")

        # Ensure numeric columns are numeric
        numeric_cols = [
            'client_id', 'size1',
            'length_ft', 'length_in', 'length_mm',
            'area_ft', 'area_in', 'area_mm'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['client_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"Standard fittings data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for standard_fittings: {e}")
        raise


def import_general(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import general data from a workbook into the public.general table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.general"

    # Key columns used to identify existing records
    key_columns = ["project_id", "pdf_id", "pdf_page", "size"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for general from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'pdf_id', 'pdf_page']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            errors = []
            for col in missing_required:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': errors
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['pdf_id'].isna() |
            df['pdf_page'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'pdf_id', 'pdf_page', 'size',
            'length', 'calculated_area', 'calculated_eq_length',
            'elbows_90', 'elbows_45', 'bevels', 'tees', 'reducers',
            'caps', 'flanges', 'valves_flanged', 'valves_welded',
            'cut_outs', 'supports', 'bends', 'field_welds'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'pdf_id', 'pdf_page']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"General data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for general: {e}")
        raise


def import_bom(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import BOM data from a workbook into the public.bom table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.bom"

    # Key columns used to identify existing records
    key_columns = ["project_id", "pdf_id", "pdf_page", "pos", "material_description"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for BOM from {workbook_path}")
    try:
        # Validate workbook path exists
        if not os.path.exists(workbook_path):
            raise FileNotFoundError(f"Workbook not found: {workbook_path}")

        # Load the Excel data
        try:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            if not isinstance(df, pd.DataFrame):
                raise ValueError("Failed to read Excel file as DataFrame")
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return {
                'total_rows': 0,
                'valid_rows': 0,
                'error_rows': 0,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Error reading Excel file: {str(e)}"}]
            }

        original_rows = len(df)

        # Clean up the data
        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'material_description']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['material_description'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'pdf_id', 'pdf_page', 'profile_id', 'rfq_ref_id', 'gen_ref_id',
            'size1', 'size2', 'quantity', 'calculated_eq_length', 'calculated_area'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Handle boolean columns
        bool_cols = ['deleted', 'ignore_item']
        for col in bool_cols:
            if col in df.columns:
                # Convert various values to boolean
                df[col] = df[col].map({
                    'yes': True, 'y': True, 'true': True, 'True': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    'no': False, 'n': False, 'false': False, 'False': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    pd.NA: False, None: False, np.nan: False
                }, na_action='ignore')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'pdf_id', 'pdf_page', 'profile_id', 'rfq_ref_id', 'gen_ref_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"BOM data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for BOM: {e}")
        return {
            'total_rows': 0,
            'valid_rows': 0,
            'error_rows': 0,
            'inserted': 0,
            'updated': 0,
            'errors': [{'error': f"Error processing BOM data: {str(e)}"}]
        }


def import_atem_rfq_0(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import RFQ data from a workbook into the public.atem_rfq table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.atem_rfq"

    # Key columns used to identify existing records
    key_columns = ["project_id", "material_description", "size1", "size2"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for RFQ from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['project_id', 'material_description']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['project_id'].isna() |
            df['material_description'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'project_id', 'profile_id', 'rfq_input_id', 'size1', 'size2', 'quantity',
            'calculated_eq_length', 'calculated_area'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Explicitly handle the 'size' column as string
        if 'size' in df.columns:
            # Convert any non-string values to string representations
            # This includes handling NaN, numeric values, etc.
            df['size'] = df['size'].apply(lambda x: str(x) if pd.notna(x) else '')
            # Replace 'nan' string with empty string
            df['size'] = df['size'].replace('nan', '')

        # Handle boolean columns
        bool_cols = ['deleted', 'ignore_item', 'mapping_not_found']
        for col in bool_cols:
            if col in df.columns:
                # Convert various values to boolean
                df[col] = df[col].map({
                    'yes': True, 'y': True, 'true': True, 'True': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    'no': False, 'n': False, 'false': False, 'False': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    pd.NA: False, None: False, np.nan: False
                }, na_action='ignore')

        # Ensure integer columns are integers
        int_cols = ['project_id', 'profile_id', 'rfq_input_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        ##############################
        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # DEBUG: Add these lines before calling importer.import_workbook

        # Export the dataframe to a CSV for inspection
        debug_export_path = os.path.join(os.path.dirname(workbook_path), 'debug_rfq_export.csv')
        logger.info(f"Exporting debug data to {debug_export_path}")
        df.to_csv(debug_export_path, index=False)

        # Debug size column specifically
        if 'size' in df.columns:
            size_na_count = df['size'].isna().sum()
            size_empty_count = (df['size'] == '').sum()
            print(f"'size' column stats before database commit:")
            print(f" - NaN values: {size_na_count}")
            print(f" - Empty strings: {size_empty_count}")
            print(f" - Total rows: {len(df)}")
            print(f" - Unique values: {df['size'].nunique()}")
            print(f" - First 10 values: {df['size'].head(10).tolist()}")

            # Inspect size column in detail
            size_debug_df = pd.DataFrame()
            size_debug_df['size_value'] = df['size']
            size_debug_df['value_type'] = df['size'].apply(lambda x: type(x).__name__)
            size_debug_df['is_na'] = df['size'].isna()
            size_debug_df['is_empty'] = df['size'] == ''
            size_debug_df.to_csv(os.path.join(os.path.dirname(workbook_path), 'size_column_debug.csv'), index=False)

        # Continue with original code - call import_workbook
        ##############################

        print(f"EXPORTING DEBUG RFQ...")
        df.to_csv(os.path.join(os.path.dirname(workbook_path), 'debug_rfq.csv'), index=False)
        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"RFQ data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for RFQ: {e}")
        raise

def get_db_engine(db_config=None):
    """
    Create a SQLAlchemy engine for database operations.

    Args:
        db_config: Database configuration object

    Returns:
        SQLAlchemy engine
    """
    if db_config is None:
        db_config = DatabaseConfig()

    # Create SQLAlchemy engine
    db_url = f"postgresql://{db_config.user}:{db_config.password}@{db_config.host}:{db_config.port}/{db_config.database}"
    return create_engine(db_url)

def import_general(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.general table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "pdf_id", "pdf_page"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "general", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size", "length", "calculated_area",
            "calculated_eq_length", "elbows_90", "elbows_45", "bevels",
            "tees", "reducers", "caps", "flanges", "valves_flanged",
            "valves_welded", "cut_outs", "supports", "bends", "field_welds"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'general')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'general',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.general table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_bom_old(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.bom table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "bom", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size1", "size2", "quantity",
            "length", "calculated_area", "calculated_eq_length"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'bom')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # IMPORTANT: Set session_replication_role to disable triggers
                cursor.execute("SET session_replication_role = 'replica';")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Commit setting of session variables within the transaction
                connection.commit()

                # Get fresh connection from the engine with session variables properly set
                connection = engine.connect().connection

                # Now do the insert
                df_pg.to_sql(
                    'bom',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                # Reset session_replication_role before committing
                cursor = connection.cursor()
                cursor.execute("SET session_replication_role = 'origin';")
                connection.commit()
        except Exception as e:
            connection.rollback()
            print(f"Error during database insertion: {e}")
            raise e
        finally:
            try:
                # Always reset session_replication_role to be safe
                cursor = connection.cursor()
                cursor.execute("SET session_replication_role = 'origin';")
                connection.commit()
            except:
                pass
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.bom table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_with_ids(workbook_path, table_name, client_id=None, profile_id=None, project_id=None, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into a PostgreSQL table, adding required IDs if they don't already exist.

    Args:
        workbook_path (str): Path to the Excel workbook
        table_name (str): Name of the target table (e.g., "public.atem_rfq")
        client_id (int, optional): Client ID to add to the dataframe if needed
        profile_id (int, optional): Profile ID to add to the dataframe if needed
        project_id (int, optional): Project ID to add to the dataframe if needed
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet
        db_config (dict, optional): Database configuration. If None, uses default

    Returns:
        dict: Result of the import operation
    """
    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Add required IDs if they don't already exist in the DataFrame
        if client_id is not None and 'client_id' not in df.columns:
            df['client_id'] = client_id
            print(f"Added client_id = {client_id}")

        if profile_id is not None and 'profile_id' not in df.columns:
            df['profile_id'] = profile_id
            print(f"Added profile_id = {profile_id}")

        if project_id is not None and 'project_id' not in df.columns:
            df['project_id'] = project_id
            print(f"Added project_id = {project_id}")

        # Display confirmation message
        print(f"\nTable: {table_name}")
        print(f"Rows to be imported: {len(df)}")
        print(f"Client ID: {df['client_id'].iloc[0] if 'client_id' in df.columns else 'Not specified'}")
        print(f"Profile ID: {df['profile_id'].iloc[0] if 'profile_id' in df.columns else 'Not specified'}")
        print(f"Project ID: {df['project_id'].iloc[0] if 'project_id' in df.columns else 'Not specified'}")

        confirm = input("\nDo you want to proceed with this import? (y/n): ")
        if confirm.lower() not in ['y', 'yes']:
            return {
                "status": "cancelled",
                "message": "Import cancelled by user",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": []
            }

        # Call the appropriate import function based on the table name
        if table_name == "public.atem_rfq":
            # Check if we should also import to atem_rfq_input
            import_to_input = input("\nDo you want to also import to atem_rfq_input table first? (y/n): ")
            import_to_input_first = import_to_input.lower() in ['y', 'yes']
            return import_atem_rfq_df(df, db_config, import_to_input_first=import_to_input_first)
        elif table_name == "public.atem_rfq_input":
            return import_atem_rfq_input_df(df, db_config)
        elif table_name == "public.bom":
            # Use the direct import method for BOM table to avoid timeouts
            print("Using direct SQL import for BOM table to avoid timeouts")
            return import_bom_direct(db_config=db_config, dataframe=df)
            # Comment out the original method in case we need to revert
            # return import_bom_df(df, db_config)
        elif table_name == "public.general":
            return import_general_df(df, db_config)
        else:
            return {"status": "error", "message": f"Import to {table_name} not implemented"}

    except Exception as e:
        error_msg = str(e)
        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": [{"row_index": "N/A", "error": error_msg}]
        }


def import_atem_rfq_input_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.atem_rfq_input table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names and first row to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            print("Converting display column names to PostgreSQL format...")
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq_input", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert integer columns
        integer_columns = ["project_id", "profile_id", "client_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq_input')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'atem_rfq_input',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq_input table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_atem_rfq_df(df, db_config=None, import_to_input_first=False):
    """
    Import data from a DataFrame into the public.atem_rfq table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.
        import_to_input_first (bool, optional): If True, also import to atem_rfq_input table first.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # If requested, import to atem_rfq_input table first
        input_result = None
        if import_to_input_first:
            print("\nImporting to atem_rfq_input table first...")
            # Create a deduplicated dataframe based on material_description (case-insensitive)
            df_input = df.copy()

            # Check if required columns already exist in PostgreSQL format or need conversion
            required_pg_columns = ["project_id", "material_description"]
            if all(col in df_input.columns for col in required_pg_columns):
                # Columns are already in PostgreSQL format, use as is
                print("Using columns as-is, already in PostgreSQL format")
            else:
                # Convert display names to PostgreSQL column names
                print("Converting display column names to PostgreSQL format...")
                mapper = ColumnMapper()
                df_input, unmapped_columns = mapper.convert_dataframe_columns(
                    df_input, "atem_rfq", "display", "pg"
                )

                if unmapped_columns:
                    print(f"Warning: Could not map columns: {unmapped_columns}")

            # Now check if the required column exists after potential conversion
            if 'material_description' not in df_input.columns:
                print(f"Error: 'material_description' column not found in dataframe. Columns: {df_input.columns.tolist()}")
                return {
                    "status": "error",
                    "message": "Required column 'material_description' not found",
                    "total_rows": len(df),
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": len(df),
                    "errors": [{"row_index": "N/A", "error": "Required column 'material_description' not found"}]
                }

            # Convert material_description to lowercase for case-insensitive comparison
            # but keep the original values in the dataframe
            # Create a temporary lowercase column for deduplication
            df_input['material_description_lower'] = df_input['material_description'].astype(str).str.lower()

            # Deduplicate based on the lowercase column
            df_input = df_input.drop_duplicates(subset=['material_description_lower'])

            # Remove the temporary column
            df_input = df_input.drop(columns=['material_description_lower'])

            print(f"Created case-insensitive deduplicated dataframe with {len(df_input)} rows (from original {len(df)} rows)")

            try:
                # Import to atem_rfq_input table in a separate try/except block
                # to ensure transaction isolation
                input_result = import_atem_rfq_input_df(df_input, db_config)
                print(f"Import to atem_rfq_input complete: {input_result['inserted']} rows inserted")

                # Ask user if they want to continue with atem_rfq import
                if input_result['status'] == 'error':
                    print("\nWarning: There were errors importing to atem_rfq_input table.")
                    continue_import = input("Do you want to continue with importing to atem_rfq? (y/n): ")
                    if continue_import.lower() not in ['y', 'yes']:
                        return {
                            "status": "cancelled",
                            "message": "Import to atem_rfq cancelled by user after atem_rfq_input import",
                            "total_rows": len(df),
                            "valid_rows": 0,
                            "inserted": 0,
                            "updated": 0,
                            "error_rows": 0,
                            "errors": [],
                            "input_table_result": input_result
                        }
            except Exception as e:
                # If there's an error importing to atem_rfq_input, ask if user wants to continue
                error_msg = str(e)
                print(f"\nError importing to atem_rfq_input: {error_msg}")
                continue_import = input("Do you want to continue with importing to atem_rfq anyway? (y/n): ")
                if continue_import.lower() not in ['y', 'yes']:
                    return {
                        "status": "error",
                        "message": f"Error importing to atem_rfq_input: {error_msg}",
                        "total_rows": len(df),
                        "valid_rows": 0,
                        "inserted": 0,
                        "updated": 0,
                        "error_rows": len(df),
                        "errors": [{"row_index": "N/A", "error": error_msg}]
                    }
                # If user wants to continue despite errors, set input_result to indicate the error
                input_result = {
                    "status": "error",
                    "message": error_msg,
                    "total_rows": len(df_input),
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": len(df_input),
                    "errors": [{"row_index": "N/A", "error": error_msg}]
                }

        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names and first row to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "size1", "size2", "quantity",
            "calculated_eq_length", "calculated_area"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "rfq_input_id", "profile_id", "client_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more controln
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'atem_rfq',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        result = {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

        # Add input table results if applicable
        if input_result:
            result["input_table_result"] = input_result

        return result

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_atem_rfq(workbook_path, sheet_name=None, db_config=None):
    """
    Import data from an Excel workbook into the public.atem_rfq table.

    Args:
        workbook_path (str): Path to the Excel workbook
        sheet_name (str, optional): Name of the sheet to import. Defaults to first sheet.
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Read the Excel file
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(workbook_path, sheet_name=0)
        else:
            df = pd.read_excel(workbook_path, sheet_name=sheet_name)

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "size1", "size2", "quantity",
            "calculated_eq_length", "calculated_area"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "rfq_input_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Now do the insert
                df_pg.to_sql(
                    'atem_rfq',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,  # Process in smaller batches to avoid timeouts
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def get_table_columns(engine, table_name, schema='public'):
    """
    Get the column names for a table in the database.

    Args:
        engine: SQLAlchemy engine
        table_name: Name of the table
        schema: Schema name

    Returns:
        List of column names
    """
    inspector = inspect(engine)
    return [column['name'] for column in inspector.get_columns(table_name, schema=schema)]

def import_bom_direct(workbook_path=None, sheet_name=None, db_config=None, dataframe=None):
    """
    Import data from an Excel workbook or DataFrame into the public.bom table using direct SQL
    to bypass trigger issues entirely.

    Args:
        workbook_path (str, optional): Path to the Excel workbook. Not used if dataframe is provided.
        sheet_name (str, optional): Name of the sheet to import. Not used if dataframe is provided.
        db_config (dict, optional): Database configuration. If None, uses default.
        dataframe (pd.DataFrame, optional): DataFrame containing the data to import. If provided, workbook_path is ignored.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np
    import psycopg2
    import psycopg2.extras
    from psycopg2.extras import execute_values

    try:
        # Use provided dataframe or read from Excel file
        if dataframe is not None:
            df = dataframe.copy()
            print("Using provided DataFrame")
        else:
            # Read the Excel file
            if sheet_name is None:
                # Use the first sheet by default
                df = pd.read_excel(workbook_path, sheet_name=0)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            print(f"Read DataFrame from {workbook_path}")

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "bom", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size1", "size2", "quantity",
            "length", "calculated_area", "calculated_eq_length"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection using the raw database configuration
        if db_config is None:
            from src.atom.pg_database.pg_connection import DatabaseConfig
            db_config = DatabaseConfig()

        # Get the columns that would exist in the database table
        engine = get_db_engine(db_config)
        existing_columns = get_table_columns(engine, 'bom')

        # Filter the DataFrame to only include columns that exist in the database
        extra_columns = [col for col in df_pg.columns if col not in existing_columns]
        if extra_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {extra_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Create a direct connection to PostgreSQL using psycopg2
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )

        try:
            # Make sure the connection doesn't autocommit
            conn.autocommit = False

            with conn.cursor() as cursor:
                # Disable BOM triggers for better import performance
                print("Disabling BOM triggers for faster import...")
                cursor.execute("""
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq;
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq_after;
                ALTER TABLE public.bom DISABLE TRIGGER trg_update_bom_component_calculations;
                """)
                # Get the column names in the dataframe
                columns = list(df_pg.columns)

                # Create the INSERT statement with ON CONFLICT DO NOTHING
                # This will skip rows that would cause constraint violations
                insert_query = f"""
                INSERT INTO public.bom ({', '.join(columns)})
                VALUES %s
                ON CONFLICT DO NOTHING
                """

                # Process the data in batches to avoid memory issues
                batch_size = 1000
                total_rows = len(df_pg)
                inserted_rows = 0

                print(f"Importing {total_rows} rows in batches of {batch_size}...")

                # Convert the dataframe to a list of tuples for direct insertion
                # This bypasses many of the SQLAlchemy and pandas overhead
                for i in range(0, total_rows, batch_size):
                    batch_df = df_pg.iloc[i:min(i+batch_size, total_rows)]

                    # Replace all forms of NaN with None so they become SQL NULL
                    # Create a copy to avoid modifying the original dataframe
                    batch_df_copy = batch_df.copy()

                    # Replace pandas NA, NaN and numpy nan with None
                    batch_df_copy = batch_df_copy.replace([pd.NA, pd.NaT, np.nan], None)

                    # Also replace any 'NaN' strings that might exist
                    batch_df_copy = batch_df_copy.replace('NaN', None)

                    # Convert to list of tuples for database insertion
                    batch_values = [tuple(row) for row in batch_df_copy.values]

                    # Use execute_values for bulk insertion (much faster than individual inserts)
                    execute_values(cursor, insert_query, batch_values)
                    inserted_rows += len(batch_values)
                    # More detailed progress reporting
                    current_percentage = (inserted_rows / total_rows) * 100
                    print(f"Processed {inserted_rows}/{total_rows} rows ({current_percentage:.1f}%)...")

                # Note: Would run manual sync here if we had disabled triggers, but skipping as triggers were active during import

                # Commit the transaction
                conn.commit()
                print(f"Successfully imported {inserted_rows} rows directly.")

        except Exception as e:
            # Rollback the transaction if there's an error
            conn.rollback()
            print(f"Error during direct SQL import: {e}")
            raise
        finally:
            try:
                # Re-enable BOM triggers that were disabled
                with conn.cursor() as cursor:
                    print("Re-enabling BOM triggers...")
                    cursor.execute("""
                    ALTER TABLE public.bom ENABLE TRIGGER trg_update_bom_component_calculations;
                    """)

                    # Intentionally disabled triggers as manual workflow works better
                    # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq;
                    # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq_after;
                    conn.commit()
            except Exception as e:
                print(f"Warning: Failed to re-enable triggers: {e}")
            finally:
                # Always close the connection
                conn.close()

        # Format result to match expected output format
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.bom table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }


def import_general_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.general table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "pdf_id", "pdf_page"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "general", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = ["size1", "size2", "quantity", "tally_count"]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'general')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Use the database connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Insert the data
                df_pg.to_sql(
                    'general',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,
                    method='multi'
                )

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.general table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }


def import_bom_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.bom table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "pdf_id", "pdf_page", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "bom", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = ["size1", "size2", "quantity", "calculated_eq_length", "calculated_area"]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page", "profile_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'bom')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Use the psycopg2 connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Temporarily disable triggers to speed up bulk inserts and avoid calculation errors
                cursor.execute("""
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq;
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq_after;
                ALTER TABLE public.bom DISABLE TRIGGER trg_update_bom_component_calculations;
                """)

                # Insert the data
                df_pg.to_sql(
                    'bom',
                    engine,
                    schema='public',
                    if_exists='append',
                    index=False,
                    chunksize=100,
                    method='multi'
                )

                # Re-enable triggers
                cursor.execute("""
                ALTER TABLE public.bom ENABLE TRIGGER trg_update_bom_component_calculations;
                """)

                # Intentionally removed because it causes errors during manual process
                # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq;
                # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq_after;

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.bom table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }


if __name__ == "__main__":
    import traceback

    # Configuration
    # Add required IDs if they don't already exist in the dataframe
    client_id = 6  # Specified client_id
    project_id = 11  # Specified project_id
    profile_id = 4  # Specified profile_id


    # Example paths for different table types
    # general_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-003\In\general.xlsx"
    workbook_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Classic Industrial\CIS-BR_0001\Data\In\PG\general.xlsx"

    # Choose which file to import
    # workbook_path = general_path  # For general table
    #workbook_path = rfq_path  # For RFQ tables

    # Table to import into
    # Options: "public.general", "public.atem_rfq", "public.atem_rfq_input", "public.bom"
    table_name = "public.general"  # Change this to match your workbook_path selection

    # For testing the atem_rfq_input table directly
    test_input_table = False  # Set to True to test importing directly to atem_rfq_input
    if test_input_table:
        table_name = "public.atem_rfq_input"

    # Optional sheet name (None uses first sheet)
    sheet_name = None

    # Create database configuration
    db_config = DatabaseConfig()

    # First test database connection
    try:
        with get_db_connection(db_config) as conn:
            print(f"Database connection successful.")
            print(f"PostgreSQL server version: {conn.server_version}")
    except Exception as e:
        print(f"Error connecting to database: {e}")
        exit(1)

    # Use the new import_with_ids function that adds IDs and confirms before importing
    result = import_with_ids(
        workbook_path=workbook_path,
        table_name=table_name,
        client_id=client_id,
        profile_id=profile_id,
        project_id=project_id,
        sheet_name=sheet_name,
        db_config=db_config
    )

    # Print results
    print("\nImport results:")
    print(f"Total rows in workbook: {result['total_rows']}")
    print(f"Valid rows: {result['valid_rows']}")
    print(f"Inserted: {result['inserted']}")
    print(f"Updated: {result['updated']}")
    print(f"Errors: {result['error_rows']}")

    # Print error details if any
    if result['error_rows'] > 0:
        print("\nError details:")
        for i, error in enumerate(result['errors'][:5]):  # Show first 5 errors
            print(f"Error {i+1}: Row {error['row_index']}: {error['error']}")

        if len(result['errors']) > 5:
            print(f"...and {len(result['errors']) - 5} more errors")
