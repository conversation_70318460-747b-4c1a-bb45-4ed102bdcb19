-- Function to run the aggregation and return column sums
-- This is a one-step process to get component totals

-- USAGE:
SELECT * FROM public.aggregate_and_sum_components(project_id, False);

CREATE OR REPLACE FUNCTION public.aggregate_and_sum_components(
    p_project_id INTEGER,
    p_force_relink BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    length NUMERIC,
    elbows_90 NUMERIC,
    elbows_45 NUMERIC,
    bevels NUMERIC,
    tees NUMERIC,
    reducers NUMERIC,
    caps NUMERIC,
    flanges NUMERIC,
    valves_flanged NUMERIC,
    valves_welded NUMERIC,
    cut_outs NUMERIC,
    supports NUMERIC,
    bends NUMERIC,
    union_couplings NUMERIC,
    expansion_joints NUMERIC,
    field_welds NUMERIC,
    calculated_eq_length NUMERIC,
    calculated_area NUMERIC
)
AS $$
BEGIN
    -- First run the aggregation
    PERFORM manage_bom_to_general_aggregation(p_project_id, p_force_relink);
    
    -- Then return the summed components
    RETURN QUERY
    SELECT 
        COALESCE(SUM(g.length), 0) AS length,
        COALESCE(SUM(g.elbows_90), 0) AS elbows_90,
        COALESCE(SUM(g.elbows_45), 0) AS elbows_45,
        COALESCE(SUM(g.bevels), 0) AS bevels,
        COALESCE(SUM(g.tees), 0) AS tees,
        COALESCE(SUM(g.reducers), 0) AS reducers,
        COALESCE(SUM(g.caps), 0) AS caps,
        COALESCE(SUM(g.flanges), 0) AS flanges,
        COALESCE(SUM(g.valves_flanged), 0) AS valves_flanged,
        COALESCE(SUM(g.valves_welded), 0) AS valves_welded,
        COALESCE(SUM(g.cut_outs), 0) AS cut_outs,
        COALESCE(SUM(g.supports), 0) AS supports,
        COALESCE(SUM(g.bends), 0) AS bends,
        COALESCE(SUM(g.union_couplings), 0) AS union_couplings,
        COALESCE(SUM(g.expansion_joints), 0) AS expansion_joints,
        COALESCE(SUM(g.field_welds), 0) AS field_welds,
        COALESCE(SUM(g.calculated_eq_length), 0) AS calculated_eq_length,
        COALESCE(SUM(g.calculated_area), 0) AS calculated_area
    FROM public.general g
    WHERE g.project_id = p_project_id;
END;
$$ LANGUAGE plpgsql;
