from src.atom.dbManager import DatabaseManager

def select_print(statement, message=''):
    print(message, statement())

if __name__ == "__main__":
    db = DatabaseManager()

    # select_print(lambda: db.get_project_pdf_storage(2), 'Get PDF storage for project id')
    # select_print(lambda: db.get_user_pdf_storage('ab'), 'Get user pdf storage')

    select_print(lambda: db.get_user_projects('1d12'))
    select_print(lambda: db.get_user_projects('dqwas'))
