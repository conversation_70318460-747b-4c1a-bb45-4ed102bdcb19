#!/usr/bin/env python3
"""
Test script to verify that project-specific row counts work correctly
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.atom.pg_database.ais_database_service import AISDatabase

def test_project_row_counts():
    """Test project-specific row counts vs total row counts"""
    print("Testing project-specific row counts...")
    print("="*60)
    
    try:
        db_service = AISDatabase()
        
        # Test connection
        if not db_service.test_connection():
            print("✗ Database connection failed")
            return
            
        print("✓ Database connection successful")
        
        # Get total row counts (no project filter)
        total_general = db_service.get_table_row_count("public.general")
        total_bom = db_service.get_table_row_count("public.bom")
        
        print(f"\nTotal row counts (all projects):")
        print(f"  - public.general: {total_general:,} rows")
        print(f"  - public.bom: {total_bom:,} rows")
        
        # Get some sample project IDs to test with
        from src.atom.pg_database.pg_connection import execute_query
        
        # Get a few project IDs that have data
        projects_query = """
            SELECT DISTINCT p.id, p.project_name, 
                   COUNT(g.id) as general_count,
                   COUNT(b.id) as bom_count
            FROM public.atem_projects p
            LEFT JOIN public.general g ON p.id = g.project_id
            LEFT JOIN public.bom b ON p.id = b.project_id
            GROUP BY p.id, p.project_name
            HAVING COUNT(g.id) > 0 OR COUNT(b.id) > 0
            ORDER BY (COUNT(g.id) + COUNT(b.id)) DESC
            LIMIT 3
        """
        
        projects = execute_query(projects_query, config=db_service.db_config, fetch="all")
        
        if projects:
            print(f"\nProject-specific row counts:")
            print("-" * 60)
            
            total_project_general = 0
            total_project_bom = 0
            
            for project in projects:
                project_id = project['id']
                project_name = project['project_name']
                
                # Get project-specific counts using our new method
                general_count = db_service.get_table_row_count("public.general", project_id)
                bom_count = db_service.get_table_row_count("public.bom", project_id)
                
                total_project_general += general_count
                total_project_bom += bom_count
                
                print(f"Project ID {project_id}: {project_name}")
                print(f"  - General: {general_count:,} rows")
                print(f"  - BOM: {bom_count:,} rows")
                print()
            
            print(f"Sample totals from {len(projects)} projects:")
            print(f"  - General: {total_project_general:,} rows")
            print(f"  - BOM: {total_project_bom:,} rows")
            
            # Test with a non-existent project ID
            fake_project_id = 99999
            fake_general = db_service.get_table_row_count("public.general", fake_project_id)
            fake_bom = db_service.get_table_row_count("public.bom", fake_project_id)
            
            print(f"\nTest with non-existent project ID {fake_project_id}:")
            print(f"  - General: {fake_general:,} rows (should be 0)")
            print(f"  - BOM: {fake_bom:,} rows (should be 0)")
            
            if fake_general == 0 and fake_bom == 0:
                print("✓ Non-existent project correctly returns 0 rows")
            else:
                print("✗ Non-existent project should return 0 rows")
                
        else:
            print("No projects with data found")
            
        print("\n" + "="*60)
        print("✓ Project-specific row count testing completed!")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_project_row_counts()
