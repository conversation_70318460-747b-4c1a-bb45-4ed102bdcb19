import sys
import cv2
import fitz
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                              QHBoxLayout, QPushButton, QLabel, QSpinBox,
                              QFileDialog, QComboBox, QMessageBox, QDoubleSpinBox,
                              QCheckBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QImage, QPixmap

class CircleDetectionDialog(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Circle Detection Tool")
        self.setMinimumSize(1200, 800)

        # Initialize variables
        self.doc = None
        self.current_page = None
        self.original_cv_image = None
        self.circle_image = None
        self.MAX_PAGES = 10

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Controls
        controls_layout = QHBoxLayout()

        # File selection
        self.file_btn = QPushButton("Open PDF")
        self.file_btn.clicked.connect(self.open_pdf)
        controls_layout.addWidget(self.file_btn)

        # Page selection
        self.page_combo = QComboBox()
        self.page_combo.currentIndexChanged.connect(self.page_changed)
        controls_layout.addWidget(self.page_combo)

        # Circle detection parameters
        params_layout = QHBoxLayout()

        # Minimum radius
        self.min_radius_label = QLabel("Min Radius:")
        params_layout.addWidget(self.min_radius_label)
        self.min_radius = QSpinBox()
        self.min_radius.setRange(1, 500)
        self.min_radius.setValue(20)
        self.min_radius.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.min_radius)

        # Maximum radius
        self.max_radius_label = QLabel("Max Radius:")
        params_layout.addWidget(self.max_radius_label)
        self.max_radius = QSpinBox()
        self.max_radius.setRange(1, 1000)
        self.max_radius.setValue(100)
        self.max_radius.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.max_radius)

        # Minimum distance between circles
        self.min_dist_label = QLabel("Min Distance:")
        params_layout.addWidget(self.min_dist_label)
        self.min_dist = QSpinBox()
        self.min_dist.setRange(1, 1000)
        self.min_dist.setValue(50)
        self.min_dist.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.min_dist)

        # Edge detection threshold
        self.edge_threshold_label = QLabel("Edge Threshold:")
        params_layout.addWidget(self.edge_threshold_label)
        self.edge_threshold = QSpinBox()
        self.edge_threshold.setRange(1, 300)
        self.edge_threshold.setValue(50)
        self.edge_threshold.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.edge_threshold)

        # Circle detection threshold
        self.circle_threshold_label = QLabel("Circle Threshold:")
        params_layout.addWidget(self.circle_threshold_label)
        self.circle_threshold = QDoubleSpinBox()
        self.circle_threshold.setRange(0.1, 1.0)
        self.circle_threshold.setSingleStep(0.05)
        self.circle_threshold.setValue(0.85)
        self.circle_threshold.valueChanged.connect(self.process_image)
        params_layout.addWidget(self.circle_threshold)

        # Add preprocessing options
        self.use_preprocessing = QCheckBox("Enable Preprocessing")
        self.use_preprocessing.setChecked(True)
        self.use_preprocessing.stateChanged.connect(self.process_image)
        params_layout.addWidget(self.use_preprocessing)

        controls_layout.addLayout(params_layout)

        # Save button
        self.save_btn = QPushButton("Save PDF")
        self.save_btn.clicked.connect(self.save_pdf)
        self.save_btn.setEnabled(False)
        controls_layout.addWidget(self.save_btn)

        layout.addLayout(controls_layout)

        # Image display
        display_layout = QHBoxLayout()
        
        # Original image
        self.original_label = QLabel()
        self.original_label.setMinimumSize(500, 600)
        self.original_label.setAlignment(Qt.AlignCenter)
        display_layout.addWidget(self.original_label)

        # Processed image with circles
        self.circles_label = QLabel()
        self.circles_label.setMinimumSize(500, 600)
        self.circles_label.setAlignment(Qt.AlignCenter)
        display_layout.addWidget(self.circles_label)

        layout.addLayout(display_layout)

    def open_pdf(self):
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open PDF", "", "PDF Files (*.pdf)")
        if filename:
            self.doc = fitz.open(filename)
            self.page_combo.clear()
            page_count = min(self.doc.page_count, self.MAX_PAGES)
            self.page_combo.addItems([f"Page {i+1}" for i in range(page_count)])
            self.page_changed(0)
            self.save_btn.setEnabled(True)

    def page_changed(self, index):
        if self.doc is None:
            return

        # Get page
        page = self.doc[index]
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        self.original_cv_image = img
        self.process_image()

    def preprocess_image(self, image):
        """Apply preprocessing steps to enhance circle detection"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (9, 9), 2)
        
        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)
        
        return enhanced

    def process_image(self):
        if self.original_cv_image is None:
            return

        # Create a copy for drawing
        self.circle_image = self.original_cv_image.copy()

        # Preprocess the image if enabled
        if self.use_preprocessing.isChecked():
            processed = self.preprocess_image(self.original_cv_image)
        else:
            processed = cv2.cvtColor(self.original_cv_image, cv2.COLOR_RGB2GRAY)

        # Detect circles using Hough Circle Transform
        circles = cv2.HoughCircles(
            processed,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=self.min_dist.value(),
            param1=self.edge_threshold.value(),
            param2=int(self.circle_threshold.value() * 100),
            minRadius=self.min_radius.value(),
            maxRadius=self.max_radius.value()
        )

        # Draw detected circles
        if circles is not None:
            circles = np.uint16(np.around(circles))
            for i in circles[0, :]:
                # Draw the outer circle
                cv2.circle(self.circle_image, (i[0], i[1]), i[2], (0, 255, 0), 2)
                # Draw the center of the circle
                cv2.circle(self.circle_image, (i[0], i[1]), 2, (0, 0, 255), 3)

        # Add text annotation
        current_page = self.page_combo.currentIndex() + 1
        text = f"Page: {current_page} | Circles detected: {len(circles[0]) if circles is not None else 0}"
        cv2.putText(self.circle_image, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
                    1, (0, 0, 255), 2)

        # Update display
        self.update_image_label(self.original_label, self.original_cv_image)
        self.update_image_label(self.circles_label, self.circle_image)

    def save_pdf(self):
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save PDF", "", "PDF Files (*.pdf)")
        
        if filename:
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'

            # Create new PDF
            output_doc = fitz.open()
            page_count = min(self.doc.page_count, self.MAX_PAGES)

            for i in range(page_count):
                # Process each page
                self.page_combo.setCurrentIndex(i)
                self.process_image()

                # Convert circle image to PDF page
                img_bytes = cv2.imencode('.png', cv2.cvtColor(self.circle_image, cv2.COLOR_RGB2BGR))[1].tobytes()
                img_doc = fitz.open("png", img_bytes)
                pdfbytes = img_doc.convert_to_pdf()
                img_pdf = fitz.open("pdf", pdfbytes)
                output_doc.insert_pdf(img_pdf)

            # Save PDF
            output_doc.save(filename)
            output_doc.close()
            QMessageBox.information(self, "Success", f"PDF saved to:\n{filename}")

    def update_image_label(self, label, cv_image):
        height, width = cv_image.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(cv_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

        # Scale to fit label while maintaining aspect ratio
        scaled_pixmap = QPixmap.fromImage(q_img).scaled(
            label.width(), label.height(),
            Qt.KeepAspectRatio, Qt.SmoothTransformation)
        label.setPixmap(scaled_pixmap)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self.original_cv_image is not None:
            self.process_image()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CircleDetectionDialog()
    window.show()
    sys.exit(app.exec())