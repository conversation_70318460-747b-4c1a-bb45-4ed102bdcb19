"""
Pandas Dataframe Custom TableView
"""
import json
import os
import re
from datetime import datetime
from PySide6.QtWidgets import (QWidget, QApplication, QHBoxLayout, QVBoxLayout, QMenu, QTableView, 
                               QPushButton, QSizePolicy, QComboBox, QStyle, QHeaderView, QAbstractButton,
                               QWidgetAction, QLineEdit, QCheckBox, QPushButton, QListWidget, QListWidgetItem,
                               QStyledItemDelegate, QProgressBar, QScrollBar, QAbstractItemDelegate, 
                               QMessageBox, QStyleOptionButton, QLabel)
from PySide6.QtGui import QCursor, QKeyEvent, QMouseEvent, QResizeEvent, QPainter, QPixmap, QIcon, QBrush, QColor
import pandas as pd
from pandas.api.types import is_object_dtype, is_numeric_dtype, is_bool_dtype, is_float_dtype
from PySide6.QtCore import (QObject, QPersistentModelIndex, Qt, QSize, QSortFilterProxyModel, 
                            QAbstractTableModel, QModelIndex, Signal, QTimer,
                            QThread, QEvent, QItemSelectionModel, QItemSelection, QRect)
from src.utils.logger import logger
import weakref
from typing import Union
import time
from pubsub import pub
import numpy as np
import math
from fractions import Fraction
from copy import deepcopy
from src.app_paths import getSavedFieldMapJson, saveFieldMapJson
from src.pyside_util import get_resource_qicon, get_resource_pixmap
from PySide6.QtCore import QMutex
from enum import Enum, Flag, auto
from typing import Optional, Type, TypeVar

# logger = logging.getLogger(__file__)


def is_float(s):
    if s is None:
        return False

    if len(s) == 0:
        return False

    digits_count = 0
    dots_count = 0
    signs_count = 0

    for c in s:
        if '0' <= c <= '9':
            digits_count += 1
        elif c == '.':
            dots_count += 1
        elif c == '-' or c == '+':
            signs_count += 1
        else:
            return False

    if digits_count == 0:
        return False

    if dots_count > 1:
        return False

    if signs_count > 1:
        return False

    return True


class GroupedTableFlags(Flag):

    HEADER_CHECKBOX_OFF = auto()
    HEADER_CHECKBOX_FIRST = auto()



class TableClipboard:
    
    def __init__(self, data=None, shape=None) -> None:
        self.data = data
        self.shape = shape

    def set(self, data, shape):
        self.data = data
        self.shape = shape
    
    def clear(self):
        self.data = None
        self.shape = None


class BaseDelegate(QStyledItemDelegate):

    EDITING_STYLE = 'border: 0px'
    DEFAULT_STYLE = 'border: 1px solid white; font: bold'

    leftMouseReleased = Signal()
    editingCancelled = Signal()
    moveCurrentCellBy = Signal(int, int) # row, column

    def __init__(self, parent, filterModel) -> None:
        super().__init__(parent)
        self._parent = parent
    
    def clone(self) -> QStyledItemDelegate:
        logger.error("Not Implemented - clone")
        raise NotImplementedError

class ComboBoxDelegate(BaseDelegate):

    def __init__(self, parent, options: list, filterModel, showFractions=False) -> None:
        super().__init__(parent, list)
        self._options: list = options
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = None
        self._showFractions = showFractions
        self._fractionCache = {}
    
    def clone(self) -> QStyledItemDelegate:
        return ComboBoxDelegate(parent=self._parent, 
                                options=self._options, 
                                filterModel=self._filterrRef(), 
                                showFractions=self._showFractions)

    def isEditing(self):
        try:
            return self._editorRef().hasFocus()
        except Exception as e:
            logger.info("Editor ref has already been destroyed")
        return False

    def createEditor(self, parent, option, index):
        self.mappedIndex = self._filterrRef().mapToSource(index) # Map filter index to source model index
        editor: QComboBox = QComboBox(parent)
        editor.setItemDelegate(QStyledItemDelegate())
        editor.style().unpolish(editor)
        editor.style().polish(editor)
        editor.setStyleSheet('border: 1px solid white; font: bold')
        self._editorRef = weakref.ref(editor)
        editor.setEditable(True)
        editor.setObjectName("cellEditor")
        return editor

    def getFraction(self, v):
        dec, whole = math.modf(v)
        r = Fraction(dec)
        whole = int(whole)
        if dec == 0 and whole == 0:
            r = "0"
        elif whole == 0:
            r = f"{r.numerator}/{r.denominator} ({v})"
        elif r.numerator == 0:
            r = f"{int(v)}"
        else:
            r = f"{int(whole)} {r.numerator}/{r.denominator} ({v})"
        return r

    def setEditorData(self, editor: QComboBox, index):
        option = self.mappedIndex.data()
        editor.clear()
        option = "" if option is None else option
        try:
            n = self._options.index(option)
            options = self._options
        except ValueError:
            options = [option] + self._options
        editor.addItems(options)
        editor.setCurrentText(option)
        if self._showFractions:
            for row in range(editor.count()):
                try:
                    v = editor.itemData(row, Qt.ItemDataRole.DisplayRole)
                    r = self.getFraction(float(v))
                    editor.setItemData(row, r, Qt.ItemDataRole.DisplayRole)
                    self._fractionCache[r] = v
                except Exception as e:
                    pass
            if option != "":
                self._value = float(option)
            else:
                pass
        else:
            self._value = option
        
        try:
            editor.setCurrentIndex(n)
        except:
            pass
        lineEdit = editor.lineEdit()
        lineEdit.setSelection(0, len(lineEdit.text()))
        editor.setFocus()
    
    def onTextEdit(self, editor, text):
        editor.showPopup()
        editor.setFocus()

    def setModelData(self, editor: QComboBox, model, index):
        option = editor.currentText()
        if option == "":
            option = None
        if self._showFractions:
            try:
                v = self._fractionCache.get(option, option)
                float(v)
                option = v
            except Exception as e:
                option = self._value
        model.setData(index, option, Qt.EditRole)
        self.commitData.emit(editor)
        self.closeEditor.emit(editor)

    def commitAndCloseEditor(self):
        self.removeEventFilter(self)
        editor = self._editorRef()
        if isinstance(editor, QWidget):
            self.commitData.emit(editor)
            self.closeEditor.emit(editor)
        editor.setStyleSheet(self.DEFAULT_STYLE)

    def eventFilter(self, target, event):
        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().lineEdit().setText(self._value)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.editingCancelled.emit()
                    return True
                moveCell, row, column = False, 0, 0
                if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Down):
                    moveCell, row, column = True, 1, 0
                if event.key() in (Qt.Key_Right, Qt.Key_Tab):
                    moveCell, row, column = True, 0, 1
                if event.key() in (Qt.Key_Left, Qt.Key_Backtab):
                    moveCell, row, column = True, 0, -1
                if event.key() == Qt.Key_Up:
                    moveCell, row, column = True, -1, 0
                if moveCell:
                    self.commitData.emit(self._editorRef())
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self.moveCurrentCellBy.emit(row, column)
                    self.removeEventFilter(self)
                    return True
        return False


class LineEditDelegate(BaseDelegate):

    def __init__(self, parent, filterModel):
        super().__init__(parent, list)
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = ""

    def clone(self) -> QStyledItemDelegate:
        return LineEditDelegate(parent=self._parent,
                                filterModel=self._filterrRef())

    def createEditor(self, parent, option, index):
        self.mappedIndex = self._filterrRef().mapToSource(index) # Map filter index to source model index
        editor = QLineEdit(parent)
        editor.textEdited.connect(self.onTextEdit)
        editor.setStyleSheet(self.DEFAULT_STYLE)
        self._editorRef = weakref.ref(editor)
        editor.setFrame(False)
        editor.installEventFilter(self)
        editor.setFocus()
        return editor

    def onTextEdit(self):
        self._editorRef().setStyleSheet(self.EDITING_STYLE)

    def setEditorData(self, editor, index):
        value = index.model().data(index, Qt.EditRole)
        if value is not None:
            editor.setText(str(value))
        self._value = value # Revert to this if cancelling

    def setModelData(self, editor, model, index):
        value = editor.text()
        # Convert value back to original
        dtype = None
        # TODO - cache or some better way. Cast value to same type as column
        modelData = self._filterrRef().sourceModel()._data
        for n, y in enumerate(modelData.columns):
            if index.column() != n:
                continue
            dtype = modelData[y].dtype
            try:
                if dtype == np.int64:
                    value = int(value)
            except:
                value = self._value
            break
        try:
            if isinstance(self._value, np.floating):
                value = np.fromstring(value, dtype=float, sep=',')[0]
            elif isinstance(self._value, float):
                value = float(value)
            elif isinstance(self._value, int):
                value = int(value)
        except:
            value = self._value

        model.setData(index, value, Qt.EditRole)
        self.commitData.emit(editor)
        self.closeEditor.emit(editor)

    def updateEditorGeometry(self, editor, option, index):
        editor.setGeometry(option.rect)

    def eventFilter(self, target, event):
        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().setText(str(self._value) if self._value is not None else "")
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.editingCancelled.emit()
                    return True
                moveCell, row, column = False, 0, 0
                if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Down):
                    moveCell, row, column = True, 1, 0
                if event.key() in (Qt.Key_Right, Qt.Key_Tab):
                    moveCell, row, column = True, 0, 1
                if event.key() in (Qt.Key_Left, Qt.Key_Backtab):
                    moveCell, row, column = True, 0, -1
                if event.key() == Qt.Key_Up:
                    moveCell, row, column = True, -1, 0
                if moveCell:
                    self.commitData.emit(self._editorRef())
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self.moveCurrentCellBy.emit(row, column)
                    self.removeEventFilter(self)
                    return True
        return False

    def commitAndCloseEditor(self):
        self.removeEventFilter(self)
        if self._editorRef:
            editor = self._editorRef()
            if isinstance(editor, QWidget):
                self.commitData.emit(editor)
                self.closeEditor.emit(editor)
            editor.setStyleSheet(self.DEFAULT_STYLE)


class ReadOnlyEditDelegate(BaseDelegate):

    DEFAULT_STYLE = 'border: 1px solid red; font: bold'

    def __init__(self, parent, filterModel):
        super().__init__(parent, list)
        self._editorRef = None
        self._filterrRef = weakref.ref(filterModel)
        self.mappedIndex = None
        self._value = ""

    def clone(self) -> QStyledItemDelegate:
        return ReadOnlyEditDelegate(parent=self._parent,
                                filterModel=self._filterrRef())

    def createEditor(self, parent, option, index):
        self.mappedIndex = self._filterrRef().mapToSource(index) # Map filter index to source model index
        editor = QLineEdit(parent)
        editor.setReadOnly(True)
        editor.textEdited.connect(self.onTextEdit)
        editor.setStyleSheet(self.DEFAULT_STYLE)
        self._editorRef = weakref.ref(editor)
        editor.setFrame(False)
        editor.installEventFilter(self)
        editor.setFocus()
        return editor

    def onTextEdit(self):
        self._editorRef().setStyleSheet(self.EDITING_STYLE)

    def setEditorData(self, editor, index):
        value = index.model().data(index, Qt.EditRole)
        if value is not None:
            editor.setText(str(value))
        self._value = value # Revert to this if cancelling

    def setModelData(self, editor, model, index):
        value = editor.text()
        # Convert value back to original
        dtype = None
        # TODO - cache or some better way. Cast value to same type as column
        modelData = self._filterrRef().sourceModel()._data
        for n, y in enumerate(modelData.columns):
            if index.column() != n:
                continue
            dtype = modelData[y].dtype
            try:
                if dtype == np.int64:
                    value = int(value)
            except:
                value = self._value
            break
        try:
            if isinstance(self._value, np.floating):
                value = np.fromstring(value, dtype=float, sep=',')[0]
            elif isinstance(self._value, float):
                value = float(value)
            elif isinstance(self._value, int):
                value = int(value)
        except:
            value = self._value
        
        model.setData(index, value, Qt.EditRole)
        self.commitData.emit(editor)
        self.closeEditor.emit(editor)

    def updateEditorGeometry(self, editor, option, index):
        editor.setGeometry(option.rect)

    def eventFilter(self, target, event):
        if target is self._editorRef():
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key.Key_Escape:
                    self._editorRef().setText(str(self._value) if self._value is not None else "")
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.editingCancelled.emit()
                    return True
                moveCell, row, column = False, 0, 0
                if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_Down):
                    moveCell, row, column = True, 1, 0
                if event.key() in (Qt.Key_Right, Qt.Key_Tab):
                    moveCell, row, column = True, 0, 1
                if event.key() in (Qt.Key_Left, Qt.Key_Backtab):
                    moveCell, row, column = True, 0, -1
                if event.key() == Qt.Key_Up:
                    moveCell, row, column = True, -1, 0
                if moveCell:
                    self.commitData.emit(self._editorRef())
                    self._editorRef().setStyleSheet(self.DEFAULT_STYLE)
                    self.closeEditor.emit(self._editorRef(), QAbstractItemDelegate.NoHint)
                    self.moveCurrentCellBy.emit(row, column)
                    self.removeEventFilter(self)
                    return True
        return False

    def commitAndCloseEditor(self):
        self.removeEventFilter(self)
        if self._editorRef:
            editor = self._editorRef()
            if isinstance(editor, QWidget):
                self.commitData.emit(editor)
                self.closeEditor.emit(editor)
            editor.setStyleSheet(self.DEFAULT_STYLE)

class PandasDataFrameModel(QAbstractTableModel):
    """Class to populate a table view with a pandas dataframe"""
    sgnSort = Signal(int, object)
    sgnIndexDataChanged = Signal(object, object, object)  # Model (i.e. self), index, value
    def __init__(self, data: pd.DataFrame, fieldMap: dict, parent=None, firstColumn=None):
        """_summary_

        Args:
            data (pd.DataFrame): The table data. A panda dataframe
            fieldMap (dict): Allows showing a display value for the column header item. 
                    Format is {id: {"display": alias} }, where id is the Qt.UserRole value 
                    and the `display` value `alias` is the Qt.DisplayRole
        """
        super().__init__(parent)
        self._fieldMap: dict = fieldMap
        self._data: pd.DataFrame = data
        self.sgnSort.connect(self.sort)
        self._fieldFilters = {} # Read only
        self._firstColumn = firstColumn

    @property
    def firstColumn(self):
        return self._firstColumn

    @firstColumn.setter
    def firstColumn(self, value):
        if self._firstColumn == value:
            return
        self._firstColumn = value

    @property
    def fieldMap(self):
        return self._fieldMap

    @fieldMap.setter
    def fieldMap(self, newFieldMap):
        self.layoutAboutToBeChanged.emit()
        self._fieldMap = newFieldMap
        self.layoutChanged.emit()

    def rowCount(self, parent=None):
        return self._data.shape[0]

    def columnCount(self, parent=None):
        return self._data.shape[1]

    def data(self, index: QModelIndex, role=Qt.DisplayRole):
        if index.isValid():
            if role == Qt.DisplayRole or role == Qt.EditRole:
                v = self._data.iloc[index.row(), index.column()]
                if v is None:
                    return ""
                if is_float_dtype(v):
                    return round(v, 2)
                return str(v)
            if role == Qt.UserRole:
                return self._data.iloc[index.row(), index.column()]
        return None

    def setData(self, index, value, role):
        if role == Qt.EditRole:
            value = None if value == "" else value
            dtype = None
            field = self._data.columns[index.column()]
            dtype = self._data[field].dtype
            if dtype == np.int64:
                value = int(value)
            elif is_float_dtype(dtype):
                value = float(value)
            self._data.iloc[index.row(), index.column()] = value
            self.sgnIndexDataChanged.emit(self, index, value)
            self.dataChanged.emit(index, index)
            return True
        return False

    def headerData(self, section: int, orientation: Qt.Orientation, role: Qt.ItemDataRole):
        """Try to get display value from either standard field or RFQ"""
        if orientation == Qt.Horizontal:
            if role == Qt.DisplayRole:
                field = self._data.columns[section]
                display = self.fieldMap.get(field, {}).get("display", field)
                return display
            elif role == Qt.UserRole:
                return self._data.columns[section]
        elif orientation == Qt.Vertical and role == Qt.DisplayRole:
            return section + 1
        
        return super().headerData(section, orientation, role)

    def setHeaderData(self, section, orientation, value, role) -> bool:
        if orientation == Qt.Horizontal and role in [Qt.DisplayRole, Qt.EditRole]:
            field = self._data.columns[section]
            for k, v in self.fieldMap.items():
                if field == k:
                    v["display"] = value
                    return True
            return False
        if orientation == Qt.Horizontal and role == Qt.ItemDataRole.DecorationRole:
            return True
        return False
    
    def flags(self, index):
        return Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable

    def sort(self, column: int, order: Qt.SortOrder) -> None:
        if len(self._data):
            asc = order == Qt.AscendingOrder
            self.layoutAboutToBeChanged.emit()
            self._data.sort_values(self._data.columns[column], ascending=asc, inplace=True, ignore_index=False)
            self.layoutChanged.emit()

    def addColumn(self, name):
        newColumn = self.columnCount()
        self.beginInsertColumns(QModelIndex(), newColumn, newColumn)
        self._data[name] = None
        # TODO: switch loc suppress warning
        self.endInsertColumns()
    
    def addColumns(self, columns: list):
        newColumn = self.columnCount()
        self.beginInsertColumns(QModelIndex(), newColumn, newColumn+len(columns)-1)
        d = dict.fromkeys(columns, None)
        temp_df = pd.DataFrame(d, index=self._data.index)
        self._data = pd.concat([self._data, temp_df], axis=1)
        self.endInsertColumns()

    def fetchChunk(self, start_row, chunk_size=100):
        end_row = min(start_row + chunk_size, len(self._data))
        # return {row: self._data.iloc[row].to_dict() for row in range(start_row, end_row)}
        # print(start_row, end_row, chunk_size, len(self._data))
        return self._data.iloc[start_row:end_row]
        return {row: self._data.iloc[row].to_dict() for row in range(start_row, end_row)}


class FetchWorker(QThread):
    """A timed worker"""
    
    sgnFetchCheckUpdate = Signal()

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.fetchTimer = QTimer(self)
        self.done = False
        # self.fetchTimer.start(1000)
        # self.fetchTimer.timeout.connect(self.fetchMore)

    def run(self):
        while True:
            time.sleep(2)
            if self.done:
                break
            self.sgnFetchCheckUpdate.emit()


class VScrollbar(QWidget):
    """TODO: experiment with fake scrollbar"""

    sgnScrollValueChanged = Signal(float)

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.setFixedWidth(32)
        self.setLayout(QVBoxLayout())
        self.handle = QPushButton("")
        self.layout().addWidget(self.handle)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        pos = event.position().toPoint()
        pos.setX(0)
        self.handle.move(pos)
        pos.setX(self.parent().width() - 16)
        self.move(pos)
        print(pos)
        value = self.handle.y() / self.height()
        value = max(0, value)
        value = min(1, value)
        self.sgnScrollValueChanged.emit(value)
        return super().mousePressEvent(event)

    def value(self):
        pass

    def maximum(self):
        pass

    def resizeEvent(self, event: QResizeEvent) -> None:
        self.setFixedHeight(self.parent().height() - 64)
        return super().resizeEvent(event)


class LazyLoadingPandasDataFrameModel(PandasDataFrameModel):
    """Modified PandasDataFrameModel

    Features include:
        - Lazy loading
    """
    CHUNK_SIZE = 256

    sgnRowsLoadedChanged = Signal(int)
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.updateColumnIndexCache()
        # self._dataActive: pd.DataFrame = self.fetchChunk(0, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        self.totalRows = self._data.shape[0]
        self._rowsLoaded = min(self.totalRows, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        self._dataCache = {}

        self._dataReadUpdates: bool = True

        self.iconDropdown = get_resource_qicon("chevron-right.svg")
        self.iconDropdownActivated = get_resource_qicon("chevron-down.svg")

        # self._colIndexToFieldId = []
        self.mutex = QMutex()

        self._index: int = 0 # This is for future. 
        # self.worker = FetchWorker(self)
        # self.worker.start()
        # self.worker.sgnFetchCheckUpdate.connect(self.fetchMore)

    @property
    def rowsLoaded(self):
        return self._rowsLoaded
    
    @rowsLoaded.setter
    def rowsLoaded(self, value):
        self._rowsLoaded = value
        self.sgnRowsLoadedChanged.emit(self._rowsLoaded)

    def realRowCount(self):
        """Returns the actual row count of raw data"""
        return super().rowCount() 

    def rowCount(self, index=QModelIndex()):
        return self.rowsLoaded

    def loadAll(self):
        self.fetchMore(itemsToFetch=self.totalRows)
 
    def canFetchMore(self, index=QModelIndex()):
        return self.realRowCount() > self.rowsLoaded

    def fetchMore(self, index=QModelIndex(), itemsToFetch=None):
        self._index = self.rowCount()

        self.mutex.lock()
        remainder = self.totalRows - self.rowsLoaded
        if remainder == 0: # Fully loaded
            self.mutex.unlock()
            return
        if itemsToFetch is not None:
            itemsToFetch = min(remainder, itemsToFetch)
        else:
            itemsToFetch = min(remainder, LazyLoadingPandasDataFrameModel.CHUNK_SIZE)
        # Insert he rows
        self.beginInsertRows(QModelIndex(), self.rowsLoaded, self.rowsLoaded + itemsToFetch - 1)
        # chunk = self.fetchChunk(self.rowsLoaded, itemsToFetch)
        self.rowsLoaded += itemsToFetch
        # self._dataActive = pd.concat([self._dataActive, chunk], ignore_index=True)
        self.endInsertRows()

        self.mutex.unlock()
    
    def removeRows(self, row: int, count: int, parent) -> bool:
        self.beginRemoveRows(parent, row, count)
        self._data = self._data.iloc[row:row+count]
        self.endRemoveRows()

    def data(self, index: QModelIndex, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        # if not self._dataReadUpdates and role != Qt.DisplayRole:
        #     return
        data: pd.DataFrame = self._data

        if (self.firstColumn 
            and role == Qt.CheckStateRole 
            and index.column() == data.columns.get_loc(self.firstColumn)):
            checked = data.iloc[index.row()]["__checked__"]
            if checked:
                return Qt.CheckState.Checked
            else:
                return Qt.CheckState.Unchecked
        elif role == Qt.EditRole:
            # v = self._dataActive.iloc[index.row(), index.column()]
            # v = self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            v = data.iloc[index.row(), index.column()]
            return v
        elif role == Qt.DisplayRole:
            if not self._dataReadUpdates:
                return super().data(index, role)
            # v = self._dataActive.iloc[index.row(), index.column()]
            # v = self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            v = data.iloc[index.row(), index.column()]
            if v is None:
                return ""
            fieldName = data.columns[index.column()]
            # Prioritize field map properties to determine display role
            propRound = self._fieldMap.get(fieldName, {}).get("round")
            if propRound is not None:
                try:
                    if v is None or v == "":
                        return ""
                    v = str(round(float(v), propRound))
                    return v
                except Exception as e:
                    return v

            try:
                t = is_numeric_dtype(v)
                if t:
                    if math.isnan(float(v)):
                        return None
                    return str(v.round(2))
            except Exception as e:
                pass
            try:
                if math.isnan(float(v)):
                    return ""
            except Exception as e:
                pass

            return str(v)
        
        elif role == Qt.UserRole:
            # return self._dataActive.iloc[index.row(), index.column()]
            # return self._data.iloc[index.row()+self._index, index.column()] # Future TODO
            return data.iloc[index.row(), index.column()]
        # elif role == Qt.ItemDataRole.ForegroundRole:
        #     return QBrush(QColor("green"))
        elif role == Qt.ItemDataRole.BackgroundRole:
            fieldName = data.columns[index.column()]
            propBg = self._fieldMap.get(fieldName, {}).get("background")
            if not propBg:
                return super().data(index, role)
            return QBrush(QColor(propBg))
        elif role == Qt.DecorationRole:
            fieldName = data.columns[index.column()]
            if self._fieldMap.get(fieldName, {}).get("options"):
                if self._fieldMap.get("__dropdown_icon__") == (index.column(), index.row()):
                    return self.iconDropdown

        return None

    def reset(self):
        self.beginResetModel()
        self.endResetModel()

    def resetFill(self):
        self.beginResetModel()
        self.endResetModel()
        self.layoutChanged.emit()

    # def flags(self, index):
    #     default_flags = super().flags(index)
    #     if not index.isValid():
    #         return default_flags

    #     # Check if the column (field ID) is editable
    #     field_id = self.col_index_to_field_id.get(index.column())
    #     if self.id_to_editable.get(field_id, False):
    #         return default_flags | Qt.ItemIsEditable
    #     else:
    #         return default_flags
        
    # def setData(self, index, value, role=Qt.EditRole):
    #     if role == Qt.EditRole and index.isValid():
    #         chunk_index = index.row() // self.CHUNK_SIZE * self.CHUNK_SIZE
    #         local_row_index = index.row() % self.CHUNK_SIZE

    #         if chunk_index not in self.data_cache:
    #             # If the chunk is not in cache, it indicates inconsistency; reloading it might be necessary
    #             self.data_cache[chunk_index] = self.data_source.fetch_chunk(chunk_index, self.CHUNK_SIZE)
        
    #         df_index = self.data_cache[chunk_index][local_row_index]['df_index']
        
    #         # Update the DataFrame with the new value
    #         column_id = str(self.col_index_to_field_id[index.column()])  # Ensure using the correct identifier
    #         self.data_source.df.at[df_index, column_id] = value

    #         # Update the cache with the new value
    #         self.data_cache[chunk_index][local_row_index][column_id] = value

    #         # Notify the view about the data change
    #         self.dataChanged.emit(index, index, [role])
    #         return True
    #     return False

class MyHorizontalHeaderView(QHeaderView):
    """A logical first checkable column"""
    headerChecked = Signal(bool)
    def __init__(self, 
                 parent, 
                 checkboxStyle=GroupedTableFlags.HEADER_CHECKBOX_FIRST):
        super().__init__(Qt.Orientation.Horizontal, parent)
        self._fieldFilters = {}
        self.setModel(parent.model())
        self.setSectionsClickable(False)
        self._sectionPosClicked = None
        self.pos = None
        self.state = QStyle.StateFlag.State_Enabled
        self._on = False
        self._checkOption: QStyleOptionButton = QStyleOptionButton()
        self._checkOption.rect = QRect(4, 7, 14, 14)
        self.sectionResized.connect(self.onSectionResized)
        self.checkboxStyle = checkboxStyle

        def scaledPixmap(name: str) -> QPixmap:
            p = get_resource_pixmap(name)
            return p.scaledToHeight(16, Qt.TransformationMode.SmoothTransformation)

        self.iconDefault = scaledPixmap("chevron-down.svg")
        self.iconFiltered = scaledPixmap("filter.svg")
        self.iconAz = scaledPixmap("icon-sort-down.svg")
        self.iconZa = scaledPixmap("icon-sort-up.svg")
        self.iconFilteredAz = scaledPixmap("filter-sort-down.svg")
        self.iconFilteredZa = scaledPixmap("filter-sort-up.svg")

        self.setFixedHeight(28)

    def sectionText(self, section, role: Qt.ItemDataRole = Qt.ItemDataRole.UserRole):
        if isinstance(self.model(), CustomFilterProxyModel):
            return self.model().headerData(section, self.orientation(), role)

    def onSectionResized(self, section):
        self.update(self.currentIndex())

    def paintSection(self, painter: QPainter, rect: QRect, logicalIndex: int) -> None:
        painter.save()
        r = super().paintSection(painter, rect, logicalIndex)
        painter.restore()
        painter.setRenderHint(painter.RenderHint.SmoothPixmapTransform)

        field = self.sectionText(logicalIndex)
        def get_icon(field):
            icon = None
            sortIcon = None
            for f in self._fieldFilters.get("sorting", []):
                if f[0] == field:
                    if f[1] == Qt.AscendingOrder:
                        sortIcon = self.iconAz
                        icon = self.iconFilteredAz
                    else:
                        sortIcon = self.iconZa
                        icon = self.iconFilteredZa
                    break
            if self._fieldFilters.get(field):
                if icon:
                    return icon
                else:
                    return self.iconFiltered
            elif sortIcon:
                return sortIcon
            else:
                return self.iconDefault

        pixmap = get_icon(field)
        x = self.sectionViewportPosition(logicalIndex)
        w = self.sectionSize(logicalIndex)
        self.style().drawItemPixmap(painter,
                                    QRect(x-3, 6, w, pixmap.height()), 
                                    Qt.AlignmentFlag.AlignTrailing,
                                    pixmap)

        # Checkbox
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            self._checkOption.state = QStyle.StateFlag.State_None
            self._checkOption.state |= QStyle.StateFlag.State_Enabled
            if self._on:
                self._checkOption.state |= QStyle.StateFlag.State_On
            else:
                self._checkOption.state |= QStyle.StateFlag.State_Off
        
        return r

    def mousePressEvent(self, event: QMouseEvent) -> None:
        self.pos = event.position().toPoint()
        return super().mousePressEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """Detect header checkbox toggling"""
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            p = event.position().toPoint()
            if event.button() == Qt.MouseButton.LeftButton:
                section = self.logicalIndexAt(p)
                if self.pos == p:
                    # Check if hit checkbox
                    hitrect = self.style().subElementRect(QStyle.SE_ItemViewItemCheckIndicator, self._checkOption, self)
                    if hitrect.contains(p):
                        self._on = not self._on
                        self.headerChecked.emit(self._on)
                        self.viewport().update()
                    elif section != -1:
                        self._sectionPosClicked = QCursor.pos()
                        self.sectionClicked.emit(section)
        else:
            p = event.position().toPoint()
            if event.button() == Qt.MouseButton.LeftButton:
                if self.pos == p:
                    section = self.logicalIndexAt(p)
                    if section != -1:
                        self._sectionPosClicked = QCursor.pos()
                        self.sectionClicked.emit(section)

        return super().mouseReleaseEvent(event)


class CustomFilterProxyModel(QSortFilterProxyModel):

    sgnColumnRenamed = Signal(int, str)

    def __init__(self, parent, realModel, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.fieldFilters = {}  # {id: [ filter values ]} to hold filters for each field ID
        self.appliedFilters = {}
        self.loadFilters()

        self._allowRenameColumn = True

        self.realModel = realModel
        self.setSourceModel(realModel)
        self.filteredModel = None

        self._cacheFieldIdToColumnIndex = []
        self._sortColumns = []
        
        self.ignore_list_widget_changes = False
        self.ignore_select_all_cb_changes = False

    def sourceModel(self) -> pd.DataFrame:
        return super().sourceModel()

    def setData(self, index, value, role):
        """TODO - needs improvements when factoring in deferred load"""
        if role == Qt.EditRole:
            value = None if value == "" else value
            self.sourceModel().setData(index, value, role)
            return True
        return False

    def setFieldFilter(self, fieldId, values: list):
        """Set filter values for a specific field ID"""
        self.fieldFilters[fieldId] = values
        self.parent().horizontalHeader()._fieldFilters = self.fieldFilters
        self.invalidateFilter()

    def invalidateFilter(self):
        self._cacheFieldIdToColumnIndex = []
        for fieldId, _ in enumerate(self.fieldFilters.items()):
            column: int = self.findColumnIndexByFieldId(fieldId)
            self._cacheFieldIdToColumnIndex.append(column)

        def filterIndexDataChanged(model, index: QModelIndex, value):
            # Need to update the filtered model data to the unsorted model data
            uid = model.index(index.row(), 0).data(Qt.ItemDataRole.UserRole)
            df = self.realModel._data
            realRow = df.loc[df['__uid__'] == int(uid)].iloc[0, 0]
            realIndex = self.realModel.index(realRow, index.column())
            self.realModel.setData(realIndex, value, Qt.ItemDataRole.EditRole)

        if not self.fieldFilters and not self._sortColumns:
            # All filters have been removed so we show unfiltered
            self.filteredModel = None
            self.realModel._fieldFilters = {}
            self.setSourceModel(self.realModel)
        else:
            data: pd.DataFrame = self.realModel._data
            for fieldId, filterValues in self.fieldFilters.items():
                # Add float checks
                filterValues2 = set()
                for f in filterValues:
                    filterValues2.add(f)    
                    try:
                        filterValues2.add(float(f))
                    except:
                        logger.info("Not a float so can safely ignore")

                data = data[data[fieldId].isin(filterValues2)]
            fieldMap = self.realModel.fieldMap

            fields = []
            ascending = []
            for f in self._sortColumns:
                fields.append(f[0])
                ascending.append(True if f[1] == Qt.AscendingOrder else False)

            if fields:
                data = data.sort_values(by=fields, ascending=ascending, na_position="first")

            self.filteredModel = LazyLoadingPandasDataFrameModel(data=data, 
                                                                fieldMap=fieldMap, 
                                                                firstColumn=self.realModel.firstColumn)
            # self.filteredModel.firstColumn = self.realModel.firstColumn
            filters = deepcopy(self.fieldFilters)
            filters["sorting"] = self._sortColumns
            self.parent().horizontalHeader()._fieldFilters = filters
            self.parent().frozenTable.horizontalHeader()._fieldFilters = filters
            self.filteredModel._fieldFilters = filters
            self.filteredModel.sgnIndexDataChanged.connect(filterIndexDataChanged)
            self.setSourceModel(self.filteredModel)

        super().invalidateFilter()

    def parseDate(self, text):
        for fmt in ("%Y-%m-%d", "%m/%d/%Y"):  # Add more formats as needed
            try:
                return datetime.strptime(text, fmt)
            except ValueError:
                pass
            return None

    def filterAcceptsRow(self, sourceRow, sourceParent) -> bool:
        return True

    def findColumnIndexByFieldId(self, fieldId):
        """If column return Truth array, then we have duplicate columns"""
        try:
            column = self.sourceModel()._data.columns.get_loc(fieldId)
            return column
        except Exception as e:
            return -1

    def lessThan(self, left, right):
        """
        Override the lessThan method to use the alphanumeric sorting.
        """
        leftData = self.sourceModel().data(left)
        rightData = self.sourceModel().data(right)
        leftKey = self.alphanumericSortKey(leftData)
        rightKey = self.alphanumericSortKey(rightData)
        return leftKey < rightKey

    def buildFilterMenu(self, parent, column: int, closeEventHandler=None) -> QMenu:
        """Creates a menu which displays a custom widget with filter controls"""
        sourceModel = self.realModel  # Get the source model from the proxy
        if sourceModel is None:
            print("Source model is not set for proxyModel.")
            return

        # Fetch the id and name from selected column
        fieldId = sourceModel.headerData(column, Qt.Horizontal, Qt.UserRole)
        if fieldId == "__uid__":
            return
        columnName = sourceModel.headerData(column, Qt.Horizontal, Qt.DisplayRole)

        self.menu = QMenu(parent)
        widgetAction: QWidgetAction = QWidgetAction(self.menu)
        self.menu.addAction(widgetAction)

        # UI for widget
        menuWidget = QWidget()
        menuWidget.setLayout(QVBoxLayout())

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        menuWidget.layout().addWidget(hbox)
        # Use column name and field ID as needed to build your filter menu
        self.columnNameLabel = QLineEdit()
        self.columnNameLabel.setClearButtonEnabled(False)
        self.columnNameLabel.setText(f"{columnName}")
        self.columnNameLabel.setEnabled(False)
        hbox.layout().addWidget(self.columnNameLabel)

        def onEditFinished():
            """Now check and update field filter"""
            self.sgnColumnRenamed.emit(column, self.columnNameLabel.text())

        def onEditColumn():
            if self.pbEditColumn.isChecked():
                self.columnNameLabel.setEnabled(True)
                self.columnNameLabel.setFocus()
                self.columnNameLabel.setSelection(0, len(self.columnNameLabel.text()))
            else:
                onEditFinished()
                self.columnNameLabel.setEnabled(False)

        pbEditColumn = None
        if self._allowRenameColumn:
            self.pbEditColumn = pbEditColumn = QPushButton("")
            pbEditColumn.setCheckable(True)
            pbEditColumn.setFixedWidth(32)
            pbEditColumn.setIcon(get_resource_qicon("edit.svg"))
            pbEditColumn.setToolTip("Rename field")
            self.columnNameLabel.returnPressed.connect(onEditFinished)
            pbEditColumn.clicked.connect(onEditColumn)
            hbox.layout().addWidget(pbEditColumn)

        # Sort A to Z Button
        btnContainer = QHBoxLayout()
        tbSortAZ = QPushButton()
        tbSortAZ.setIcon(get_resource_qicon("icon-sort-down.svg"))
        tbSortAZ.setText(" Sort A to Z")
        tbSortAZ.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        btnContainer.addWidget(tbSortAZ)

        # Sort Z to A Button
        tbSortZA = QPushButton()
        tbSortZA.setIcon(get_resource_qicon("icon-sort-up.svg"))
        tbSortZA.setText(" Sort Z to A")
        tbSortZA.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        btnContainer.addWidget(tbSortZA)
        menuWidget.layout().addLayout(btnContainer)
        
        # Remove Filter Button
        pbRemoveFilter = QPushButton()
        pbRemoveFilter.setIcon(get_resource_qicon("filter.svg"))
        pbRemoveFilter.setText(f" Remove filter from {columnName}")
        pbRemoveFilter.setIconSize(QSize(16, 16))  # Adjust icon size as needed
        pbRemoveFilter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        menuWidget.layout().addWidget(pbRemoveFilter)
        
        self.searchFilter = QLineEdit()
        self.searchFilter.setPlaceholderText("Search...")
        self.searchFilter.setClearButtonEnabled(True)
        menuWidget.layout().addWidget(self.searchFilter)

        self.cbSelectAll = QCheckBox('Select All', menuWidget)
        self.cbSelectAll.setTristate(False)
        self.cbSelectAll.setChecked(True)
        self.cbSelectAll.setStyleSheet('margin-left: 5px; font: bold')
        self.cbSelectAll.stateChanged.connect(self.selectAllCheckChanged)
        menuWidget.layout().addWidget(self.cbSelectAll)

        self.listWidget = QListWidget(menuWidget)
        self.listWidget.setEditTriggers(QListWidget.NoEditTriggers)
        
        self.listWidget.itemChanged.connect(self.listviewCheckChanged)  # Connect to itemChanged for check state updates

        _filtered = self.fieldFilters.get(fieldId, [])
        uniqueValues: set = self.getUniqueColumnItems(column)
        uniqueValues = sorted(uniqueValues, key=self.alphanumericSortKey)
        for value in uniqueValues:
            item = QListWidgetItem(str(value) if value else "<< no value >>")
            item.setData(Qt.ItemDataRole.UserRole, value)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            if _filtered:
                item.setCheckState(Qt.CheckState.Checked if str(value) in _filtered else Qt.CheckState.Unchecked)
            else:
                item.setCheckState(Qt.CheckState.Checked)
            self.listWidget.addItem(item)

        menuWidget.layout().addWidget(self.listWidget)

        # TODO: Additional button for adding to filter
        pbApplyFilter = QPushButton("Apply Filter")
        pbOk = QPushButton("Ok")
        pbCancel = QPushButton("Cancel")

        def sortAscending():
            newKey = (fieldId, Qt.AscendingOrder)
            # If field already sorted, update the sort direction
            for n, key in enumerate(self._sortColumns):
                _field, order = key
                if _field == fieldId:
                    # Unsort if key already sorted in same direction
                    if order == newKey[1]:
                        del self._sortColumns[n]
                    else:
                        self._sortColumns[n] = newKey
                    self.invalidateFilter()
                    return
            # No key found, so add it
            self._sortColumns.append(newKey)
            self.invalidateFilter()

        def sortDescending():
            newKey = (fieldId, Qt.DescendingOrder)
            # If field already sorted, update the sort direction
            for n, key in enumerate(self._sortColumns):
                _field, order = key
                if _field == fieldId:
                     # Unsort if key already sorted in same direction
                    if order == newKey[1]:
                        del self._sortColumns[n]
                    else:
                        self._sortColumns[n] = newKey
                    self.invalidateFilter()
                    return
            # No key found, so add it
            self._sortColumns.append(newKey)
            self.invalidateFilter()

        # Connect Button Signals
        pbOk.clicked.connect(lambda: self.applyFilter(fieldId, self.listWidget, column, False, self.menu))
        pbApplyFilter.clicked.connect(lambda: self.applyFilter(fieldId, self.listWidget, column, True, self.menu))
        pbCancel.clicked.connect(self.menu.close)
        tbSortAZ.clicked.connect(sortAscending) # Connect the Sort A to Z button
        tbSortZA.clicked.connect(sortDescending) # Connect the Sort Z to A button

        def onRemoveFilter(fieldId):
            self.removeFilter(fieldId)
            self.menu.close()

        pbRemoveFilter.clicked.connect(lambda: onRemoveFilter(fieldId)) # Connect the Remove Filter button

        # Add buttons to layout
        btnContainer = QHBoxLayout()
        btnContainer.addWidget(pbOk)
        btnContainer.addWidget(pbCancel)
        menuWidget.layout().addWidget(pbApplyFilter)
        menuWidget.layout().addLayout(btnContainer)

        # Connect the searchEdit signal
        self.searchFilter.textChanged.connect(self.onSearchFilterTextChanged)

        widgetAction.setDefaultWidget(menuWidget)
        self.menu.addAction(widgetAction)

        if closeEventHandler:
            self.menu.aboutToHide.connect(closeEventHandler)

        return self.menu

    def sort(self, column, order):
        self.sourceModel().sgnSort.emit(column, order)

    def removeFilter(self, fieldId):
        # Check if the field_id exists in the applied filters and remove it
        if fieldId in self.fieldFilters:
            del self.fieldFilters[fieldId]
            self.invalidateFilter()  # Refresh the filter view

        # Assuming you have some mechanism to track and save the applied filters,
        # you might want to update that here as well.
        if fieldId in self.appliedFilters:
            del self.appliedFilters[fieldId]
            self.saveFilters()  # Save the updated filters to persist the changes

        # Update the list items
        for index in range(self.listWidget.count()):
            item = self.listWidget.item(index)
            item.setCheckState(Qt.CheckState.Checked)
        
    def onSearchFilterTextChanged(self):
        """Hide item if not in filter"""
        text = self.searchFilter.text()
        for index in range(self.listWidget.count()):
            item = self.listWidget.item(index)
            item.setHidden(text.lower() not in item.text().lower())

    def getUniqueColumnItems(self, column: int):
        """Extract unique values from a column in the proxy model"""
        if self.filteredModel:
            df: pd.DataFrame = self.filteredModel._data
        else:
            df: pd.DataFrame = self.realModel._data
        uniqueValues = df.iloc[:, column].unique().tolist()
        return uniqueValues

    def alphanumericSortKey(self, text):
        """
        Generate a key for sorting strings that may contain numbers (including floats).
        """
        # Ensure s is a string to prevent TypeError in re.split
        text = str(text) if text is not None else ""
        convert = lambda text: int(text) if text.isdigit() else text 
        # alphanum_key = lambda key: [ convert(c) for c in re.split('([0-9]+)', key) ] 
        return [ convert(c) for c in re.split('([0-9]+)', text) ]

    def filterList(self, listWidget, text):
        for i in range(listWidget.count()):
            item = listWidget.item(i)
            item.setHidden(text.lower() not in item.text().lower())

    def applyFilter(self, fieldId, listWidget, column: int, keepOpen, menu):

        def getUserValue(i):
            userData = listWidget.item(i).data(Qt.UserRole)
            return str(userData) if userData is not None else None
        
        def getNumericUserValue(i):
            userData = listWidget.item(i).data(Qt.UserRole)
            if userData is not None:
                try:
                    return float(userData)
                except:
                    return None
            return None

        dtype = self.realModel._data[fieldId].dtype
        if is_numeric_dtype(dtype):
            selectedValues = [getNumericUserValue(i) for i in range(listWidget.count()) if listWidget.item(i).checkState() == Qt.Checked]
        else:
            selectedValues = [getUserValue(i) for i in range(listWidget.count()) if listWidget.item(i).checkState() == Qt.Checked]
        # Ensure the retrieval matches your model setup for storing/retrieving field IDs
        if fieldId:
            if selectedValues:
                self.appliedFilters[fieldId] = selectedValues
            elif fieldId in self.fieldFilters:
                del self.appliedFilters[fieldId]

            # No values are unselected? Remove from filtering
            if listWidget.count() == len(selectedValues):
                try:
                    del self.fieldFilters[fieldId]
                    self.sourceModel()._fieldFilters = self.fieldFilters
                    self.invalidateFilter()
                    return
                except KeyError:
                    pass
            else:
                # Implement the saveFilters method to persist filters as needed
                # Apply the filter to the proxy model
                self.setFieldFilter(fieldId, selectedValues)
                
            if not keepOpen:
                menu.close()  # Close the menu directly
        else:
            print("Error: Could not retrieve field ID for column")

    def selectAllCheckChanged(self, state):
        # Check if the signal is from user interaction with the checkbox itself,
        # not from programmatic changes to its state
        if not self.ignore_select_all_cb_changes:
            self.ignore_list_widget_changes = True  # Flag to ignore itemChanged signals during this operation

            checkState = Qt.Checked if self.cbSelectAll.isChecked() else Qt.Unchecked
            for index in range(self.listWidget.count()):
                item = self.listWidget.item(index)
                if not item.isHidden():  # Apply only to visible items
                    item.setCheckState(checkState)

            self.ignore_list_widget_changes = False
            self.updateSelectAllCheckbox()

    def listviewCheckChanged(self, item=None):
        # Ignore changes if flagged to do so (during bulk select/deselect operations)
        if not self.ignore_list_widget_changes:
            self.updateSelectAllCheckbox()

    def updateSelectAllCheckbox(self):
        self.ignore_select_all_cb_changes = True  # Prevent triggering selectAllCheckChanged

        checkedItems = [self.listWidget.item(i) for i in range(self.listWidget.count()) if not self.listWidget.item(i).isHidden()]
        allChecked = all(item.checkState() == Qt.Checked for item in checkedItems)
        anyChecked = any(item.checkState() == Qt.Checked for item in checkedItems)

        if allChecked:
            self.cbSelectAll.setTristate(False)
            self.cbSelectAll.setCheckState(Qt.Checked)
        elif anyChecked:
            self.cbSelectAll.setTristate(True)
            self.cbSelectAll.setCheckState(Qt.PartiallyChecked)
        else:
            self.cbSelectAll.setTristate(False)
            self.cbSelectAll.setCheckState(Qt.Unchecked)

        self.ignore_select_all_cb_changes = False  # Reset the flag after updating checkbox state

    def saveFilters(self):
        """TODO: Save filter state"""
        return
        with open("assets/filters.json", "w") as file:
            json.dump(self.appliedFilters, file)
            print("Filters Added")

    def loadFilters(self):
        """TODO: Restore filter state"""
        try:
            with open("assets/filters.json", "r") as file:
                self.appliedFilters = json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            self.appliedFilters = {}

    def clearFilter(self, column_index, menu):
        self.appliedFilters.pop(column_index, None)
        self.saveFilters()
        menu.close()

    def resetFilters(self):
        """Removes persisted filter file"""
        print("Filters Reset")
        self.appliedFilters = {}
        if os.path.exists("assets/filters.json"):
            os.remove("assets/filters.json")


class FrozenTableView(QTableView):
    """Subset of table features"""

    def __init__(self, parent, checkboxStyle) -> None:
        super().__init__(parent=parent)
        self._checkboxStyle = checkboxStyle
        self.setHorizontalHeader(MyHorizontalHeaderView(self, self._checkboxStyle))
        self._currentEditorIndex = None
        self.setObjectName("frozenTable")
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollMode(self.ScrollMode.ScrollPerPixel)
        self.installEventFilter(self)
        self.setEditTriggers(self.EditTrigger.NoEditTriggers)
        self.doubleClicked.connect(self.onDoubleClicked)
        headers = self.horizontalHeader()
        headers.setContextMenuPolicy(Qt.CustomContextMenu)
    
    def horizontalHeader(self) -> MyHorizontalHeaderView:
        return super().horizontalHeader()
    
    def getSwappedColumnNameOrder(self, 
                                  excludeHidden=False, 
                                  role=Qt.ItemDataRole.UserRole) -> list:
        """Use `x` section position to tell us column order"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            pos = self.horizontalHeader().sectionPosition(n)
            if excludeHidden:
                if self.isColumnHidden(n):
                    continue
            value = (self.model().headerData(n, Qt.Orientation.Horizontal, role), pos)
            columns.append(value)
        columns = sorted(columns, key=lambda k: k[1])
        columns = [c[0] for c in columns]
        return columns

    def setColumnOrder(self, columns: list):
        unfrozenOrder = self.getSwappedColumnNameOrder(excludeHidden=False)
        columnToIndex = {}
        fieldToIndex = {}
        for n, field in enumerate(unfrozenOrder):
            columnToIndex[field] = n
            fieldToIndex[n] = field

        for pos, field in enumerate(columns):
            v: int = pos
            fieldIdx = columnToIndex.get(field, None)
            if fieldIdx is None:
                continue
            if fieldIdx == pos: # Already in correct position
                continue
            movedField = fieldToIndex[v]
            self.horizontalHeader().swapSections(fieldIdx, v)
            
            # Update the mapping of the swapped sections
            columnToIndex[field] = v
            fieldToIndex[v] = field
            columnToIndex[movedField] = fieldIdx
            fieldToIndex[fieldIdx] = movedField

    def currentChanged(self, current: Union[QModelIndex, QPersistentModelIndex], previous: Union[QModelIndex, QPersistentModelIndex]) -> None:
        """Trigger editing on the selecting cell"""
        try:
            if self._currentEditorIndex:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                if isinstance(delegate, BaseDelegate):
                    delegate.commitAndCloseEditor()
                self.closePersistentEditor(self._currentEditorIndex)
                delegate.moveCurrentCellBy.disconnect(self.moveCell)
            self._currentEditorIndex = None
        except Exception as e:
            logger.info("Could not close table delegate editor. Can be due to model switch", exc_info=True)
        
        super().currentChanged(current, previous)
    
    def selectSingleIndex(self, index):
        self.selectionModel().select(index, QItemSelectionModel.SelectionFlag.ClearAndSelect)

    def moveCell(self, row: int, column: int):
        """Remember to account for swapped column order"""
        key = None
        if row == 1:
            key = Qt.Key.Key_Down
        elif row == -1:
            key = Qt.Key.Key_Up
        elif column == -1:
            key = Qt.Key.Key_Left
        elif column == 1:
            key = Qt.Key.Key_Right
        handmade = QKeyEvent(QEvent.Type.KeyPress, key, Qt.KeyboardModifier.NoModifier)
        self.keyPressEvent(handmade)
    
    def onDoubleClicked(self, event):
        self.startEditing(event)

    def startEditing(self, index):
        try:
            if self._currentEditorIndex is not None:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                if isinstance(delegate, BaseDelegate):
                    delegate.commitAndCloseEditor()
                    self.closePersistentEditor(self._currentEditorIndex)
                    try:
                        delegate.moveCurrentCellBy.disconnect(self.moveCell)
                    except:
                        pass
            self._currentEditorIndex = None
            if len(self.selectedIndexes()) > 1:
                return
            delegateNew: BaseDelegate = self.itemDelegateForColumn(index.column())
            if delegateNew: # TODO?: get column from data role as columns may switch
                delegateNew.moveCurrentCellBy.connect(self.moveCell)
                self.openPersistentEditor(index)
                self._currentEditorIndex = index
        except Exception as e:
            logger.info("Could not close table delegate editor. Can be due to model switch", exc_info=True)


class GroupedTableView(QTableView):
    """Custom TableView which supports filtering and lazy loading"""

    sgnSelectionChanged = Signal()
    sgnUpdateDf = Signal(object)
    def __init__(self, 
                 parent, 
                 lazyloading: bool = True,
                 checkboxStyle:GroupedTableFlags = GroupedTableFlags.HEADER_CHECKBOX_FIRST) -> None:
        super().__init__(parent=parent)
        self.checkboxStyle = checkboxStyle
        self.setSelectionBehavior(self.SelectionBehavior.SelectItems)
        self.setHorizontalHeader(MyHorizontalHeaderView(self, checkboxStyle=checkboxStyle)) # Set it early as it seems to disappear without
        self._alwaysVisibleColumns = []
        self._forceHiddenColumns = []
        self._lazyloading: bool = lazyloading
        self._currentEditorIndex = None  # Used for keeping only one editor open
        self._fieldMap = {}
        self.frozenTable: FrozenTableView = self.initFreezePane()
        self.frozenColumns = None
        self.editFreezeColumns: list = None # Columns to freeze when editing cell
        self.cacheFrozenColumnWidth = {}
        self.cacheColumnWidth = {}
        self.cacheSwappedOrder = None
        self._cacheDefaultColumnOrder = None
        self._cacheColumnToIndex = {}
        self._cacheIndexToColumn = {}
        self._cacheSwappedColumnToIndex = {}
        self._cacheSwappedIndexToColumn = {}
        self._allowRenameColumn = True
        self._mouseDown = False
        self._mousePos = None
        self.filterProxyModel: CustomFilterProxyModel = None
        self.clipboard: TableClipboard = TableClipboard()

        self.sgnUpdateDf.connect(self.setTableData)
        self.mouseMoveEvent = self.tableMouseMoveEvent
        self.setMouseTracking(True) # We can disable this for read-only table
        self.horizontalHeader().sectionPressed.disconnect()
        self.verticalHeader().sectionPressed.disconnect()
        self.horizontalHeader().sectionMoved.connect(self.onHorizontalHeaderSectionMoved)
        self.verticalHeader().sectionPressed.connect(self.onVerticalHeaderSectionPressed)

        # Stop corner from selecting all
        self.findChild(QAbstractButton).setDisabled(True)

        # self.scroll = QScrollBar(self)
        # self.scroll.hide()
        # self.scroll.setValue(2000)
        # self.scroll.setMaximum(10000)
        # self.setVerticalScrollBar(self.scroll)
        # vscroll = VScrollbar(self)
        # vscroll.setFixedHeight(self.height())
        # vscroll.show()
        self.verticalScrollBar().valueChanged.connect(self.onVerticalScrollbar)
        self.horizontalScrollBar().valueChanged.connect(self.onHorizontalScrollbar)

        self.viewport().installEventFilter(self)

        # self.setEditTriggers(self.EditTrigger.NoEditTriggers | self.EditTrigger.DoubleClicked)
        self.setEditTriggers(self.EditTrigger.NoEditTriggers)
        self.doubleClicked.connect(self.onDoubleClicked)
        self.installEventFilter(self)

    @property
    def fieldMap(self):
        return self._fieldMap

    @fieldMap.setter
    def fieldMap(self, newFieldMap: dict):
        self._fieldMap = newFieldMap
        try:
            if self.model():
                self.model().realModel.fieldMap = newFieldMap
        except Exception as e:
            print(e)
    
    def model(self) -> CustomFilterProxyModel:
        return super().model()

    def horizontalHeader(self) -> MyHorizontalHeaderView:
        return super().horizontalHeader()

    def getSwappedColumnIndex(self, column):
        """Returns the index of the column for swapped columns"""
        return column

    def moveCell(self, row: int, column: int):
        """Remember to account for swapped column order"""
        key = None
        if row == 1:
            key = Qt.Key.Key_Down
        elif row == -1:
            key = Qt.Key.Key_Up
        elif column == -1:
            key = Qt.Key.Key_Left
        elif column == 1:
            key = Qt.Key.Key_Right
        handmade = QKeyEvent(QEvent.Type.KeyPress, key, Qt.KeyboardModifier.NoModifier)
        self.keyPressEvent(handmade)
    
    def goToNextNonBlankCell(self, direction):

        def isCellBlank(v):
            return v is None or v == ""

        dx, dy = direction
        limit = max(self.model().columnCount(), self.model().rowCount()) # just to impose more safety
        index = self.currentIndex()
        row = index.row()
        if dy != 0:
            for n in range(1, limit + 1):
                r = row + (n * dy)
                index2 = self.model().index(r, index.column())
                data = index2.data(Qt.ItemDataRole.UserRole)
                if isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                break
        elif dx != 0:
            swappedColumns = self.getSwappedColumnNameOrder(excludeHidden=True)
            columnName = self._cacheIndexToColumn[index.column()]
            columnIndex = swappedColumns.index(columnName)
            for n in range(1, limit + 1):
                c = columnIndex + (n * dx)
                if c == -1: # Prevent wrapping
                    return
                try:
                    nextColumnName = swappedColumns[c]
                    nextColumnIndex = self._cacheColumnToIndex[nextColumnName]
                except Exception as e:
                    return # Out of bounds. Nothing to check. All good
                index2 = self.model().index(row, nextColumnIndex)
                data = index2.data(Qt.ItemDataRole.UserRole)
                if isCellBlank(data):
                    continue
                self.selectionModel().select(index2, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                self.setCurrentIndex(index2)
                break

    def copyFromRowAbove(self):
        if not self.selectedIndexes():
            return
        rowData = {}
        first = self.selectedIndexes()[0]
        # Grab the row data above the first selected index
        for index in self.selectedIndexes():
            if index.row() != first.row():
                continue
            r = index.row() - 1
            if r < 0: # Can't duplicate from invalid cell
                return
            indexAbove = self.model().index(r, index.column())
            data = indexAbove.data(Qt.ItemDataRole.UserRole)
            rowData[index.column()] = data

        for index in self.selectedIndexes():
            newData = rowData[index.column()]
            self.filterProxyModel.setData(index, newData, Qt.ItemDataRole.EditRole)
            self.update(index)
    
    def onDelegateLeftMouseReleased(self):
        pass

    def onDoubleClicked(self, event):
        self.hideCellDropdownIcon()
        self.startEditing(event)
    
    def startEditing(self, index):
        """Render the item delegate. If an edited item is in the frozen column, 
        switch to editing on the frozen table"""
        try:
            if self._currentEditorIndex:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                # if isinstance(delegate, ComboBoxDelegate) and delegate.isEditing():
                #     return False
                if isinstance(delegate, BaseDelegate):
                    try:
                        delegate.commitAndCloseEditor()
                        self.closePersistentEditor(self._currentEditorIndex)
                        delegate.moveCurrentCellBy.disconnect(self.moveCell)
                        delegate.editingCancelled.disconnect(self.onDelegateEditingCancelled)
                    except Exception:
                        pass
            self._currentEditorIndex = None
            if len(self.selectedIndexes()) > 1:
                return

            self.getDefaultColumnNameOrder() # <-- Added
            columnName = self._cacheIndexToColumn[index.column()]
            delegateNew: BaseDelegate = self.itemDelegateForColumn(index.column())
            if delegateNew: # TODO?: get column from data role as columns may switch
                # Open the edit in the frozen table?
                if self.editFreezeColumns:
                    self.freezePane()
                if self.editFreezeColumns and columnName in self.editFreezeColumns:
                    frozenIndex = self.model().index(index.row(), index.column())
                    self.frozenTable.startEditing(frozenIndex)
                else:
                    delegateNew.moveCurrentCellBy.connect(self.moveCell)
                    delegateNew.editingCancelled.connect(self.onDelegateEditingCancelled)
                    self.openPersistentEditor(index)
                    self._currentEditorIndex = index

        except Exception as e:
            logger.info("Could not close table delegate editor. Can be due to model switch", exc_info=True)

    def onDelegateEditingCancelled(self):
        """Escape pressed on delegate"""
        self.unFreezePane()

    def selectionChanged(self, selected: QItemSelection, deselected: QItemSelection) -> None:
        if self.frozenTable:
            if self.editFreezeColumns:
                for index in selected.indexes():
                    columnName = self._cacheIndexToColumn[index.column()]
                    if columnName in self.editFreezeColumns:
                        frozenIndex = self.model().index(index.row(), index.column())
                        self.frozenTable.selectionModel().select(frozenIndex, QItemSelectionModel.SelectionFlag.Select)
                for index in deselected.indexes():
                    columnName = self._cacheIndexToColumn[index.column()]
                    if columnName in self.editFreezeColumns:
                        frozenIndex = self.model().index(index.row(), index.column())
                        self.frozenTable.selectionModel().select(frozenIndex, QItemSelectionModel.SelectionFlag.Deselect)

        r = super().selectionChanged(selected, deselected)
        self.sgnSelectionChanged.emit()
        return r
    
    def currentChanged(self, current: Union[QModelIndex, QPersistentModelIndex], previous: Union[QModelIndex, QPersistentModelIndex]) -> None:
        """Trigger editing on the selecting cell"""
        try:
            if self._currentEditorIndex:
                delegate = self.itemDelegateForIndex(self._currentEditorIndex)
                # if isinstance(delegate, ComboBoxDelegate) and delegate.isEditing():
                #     return False
                if isinstance(delegate, BaseDelegate):
                    delegate.commitAndCloseEditor()
                self.closePersistentEditor(self._currentEditorIndex)
                delegate.moveCurrentCellBy.disconnect(self.moveCell)
                delegate.editingCancelled.disconnect(self.onDelegateEditingCancelled)
            self._currentEditorIndex = None

            if current.column() == -1:
                return
            if not self._cacheIndexToColumn:
                self.getDefaultColumnNameOrder()
            # We may have moven over to the frozen pane
            columnName = self._cacheIndexToColumn[current.column()]
            if self.frozenTable:
                if self.editFreezeColumns:
                    if columnName in self.editFreezeColumns:
                        frozenIndex = self.model().index(current.row(), current.column())
                        # self.frozenTable.setCurrentIndex(frozenIndex)
                    else:
                        # self.frozenTable.clearSelection()
                        pass

        except Exception as e:
            logger.info("Could not close table delegate editor. Can be due to model switch", exc_info=True)
        
        super().currentChanged(current, previous)

    def getDataFrame(self, drop_uid: bool = True) -> pd.DataFrame:
        if not self.model():
            return None
        df = self.model().realModel._data
        if drop_uid:
            return df.drop('__uid__', axis=1)
        return df
        # return df.loc[:, df.columns != '__uid__']
    
    def getDataFrameAsDisplayed(self) -> pd.DataFrame:
        """Returns the currently displayed filtered and sorted data"""
        if not self.model():
            return None
        model = self.model()
        df = self.getDataFrame(False)
        swapped = self.getSwappedColumnNameOrder(excludeHidden=True)
        swappedDisplay = self.getSwappedColumnNameOrder(excludeHidden=True, role=Qt.ItemDataRole.DisplayRole)
        df = df[swapped]
        df = df.set_axis(swappedDisplay, axis=1)
        try:
            df.drop('__uid__', axis=1)
        except KeyError:
            pass # might be excluded already so nothing to drop
        return df

    def clearAllItemDelegates(self):
        """Clear all delegates"""
        try:
            self.itemDelegate().closeEditor()
            self._currentEditorIndex = None
        except Exception as e:
            # Likely no editor is open so nothing to close
            pass
        if self.model():
            # Might not be necessary as we reset model
            for column in range(self.model().columnCount()):
                self.setItemDelegateForColumn(column, None)
            self.model().beginResetModel()
            self.model().endResetModel()
            self.filterProxyModel = None

    def clearTable(self):
        """Reset table"""
        self.clearAllItemDelegates()
        if self.horizontalHeader():
            try:
                self.horizontalHeader().disconnect(self.showHeaderContextMenu)
                self.horizontalHeader().disconnect(self.onHorizontalHeaderChecked)
            except:
                pass

    def setTableData(self, dataframe: pd.DataFrame):
        self.clearTable()
        try:
            # Prevent emitting more than once
            self.horizontalHeader().customContextMenuRequested.disconnect(self.showHeaderContextMenu)
            self.horizontalHeader().sectionClicked.disconnect()
            self.horizontalHeader().headerChecked.disconnect()
        except:
            pass
        if not isinstance(self.horizontalHeader(), MyHorizontalHeaderView):
            self.setHorizontalHeader(MyHorizontalHeaderView(self, checkboxStyle=self.checkboxStyle))
            # self.setHorizontalHeader(QHeaderView(Qt.Orientation.Horizontal))
        self.horizontalHeader().headerChecked.connect(self.onHorizontalHeaderChecked)
        self.horizontalHeader()._on = False

        if not dataframe.empty:
            if "__uid__" in dataframe.columns:
                dataframe = dataframe.drop('__uid__', axis=1)
            dataframe.insert(0, '__uid__', range(0, len(dataframe)))
            dataframe["__checked__"] = False
            if self._lazyloading:
                model = LazyLoadingPandasDataFrameModel(dataframe, self.fieldMap, parent=self)
            else:
                model = PandasDataFrameModel(dataframe, self.fieldMap, parent=self)

            self.filterProxyModel = CustomFilterProxyModel(self, realModel=model)
            self.filterProxyModel._allowRenameColumn = self._allowRenameColumn
            self.frozenTable.setModel(self.filterProxyModel)
            self.setModel(self.filterProxyModel)  # This will directly set the model
            self.filterProxyModel.sgnColumnRenamed.connect(self.onColumnRenamed)

            header: MyHorizontalHeaderView = self.horizontalHeader()
            header.setContextMenuPolicy(Qt.CustomContextMenu)
            header.customContextMenuRequested.connect(self.showHeaderContextMenu)
            header.sectionClicked.connect(self.onHeaderLeftClicked)
            columns = self.getDefaultColumnNameOrder()
            for field in columns:
                self.setLineEditDelegateForColumn(field)
        else:
            self.setModel(None)
            try:
                self.horizontalHeader().sectionClicked.disconnect()
            except Exception:
                pass # Safe exception as signal may not have any connections

    def showHeaderContextMenu(self, position):
        menu = QMenu()
        section = self.horizontalHeader().logicalIndexAt(position)
        userRole = self.filterProxyModel.headerData(section, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
        display = self.filterProxyModel.headerData(section, Qt.Orientation.Horizontal, Qt.ItemDataRole.DisplayRole)
        hideColumn = None
        if userRole not in self._alwaysVisibleColumns:
            hideColumn = menu.addAction(f"Hide Column {display}")
        unhideAllColumns = menu.addAction(f"Unhide All Columns")
        clearAllFilters = menu.addAction(f"Clear All Filters")
        ac = menu.exec(self.mapToGlobal(position))
        if hideColumn and ac == hideColumn:
            self.hideColumn(section)
            self.refreshCheckboxColumn()
        elif ac == unhideAllColumns:
            self.unhideAllColumns()
            self.refreshCheckboxColumn()
        elif ac == clearAllFilters:
            self.filterProxyModel.fieldFilters = {}
            self.horizontalHeader()._fieldFilters = {}
            self.filterProxyModel._sortColumns = []
            self.filterProxyModel.invalidateFilter()
            self.refreshCheckboxColumn()

    def onColumnRenamed(self, column: int, name: str):
        """TODO: cleaner approach, handle this logic elsewhere"""
        try:
            fieldId = self.filterProxyModel.headerData(column, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
            fieldMapJson = getSavedFieldMapJson()
            # print(fieldId, fieldMapJson)
            for a in ["fields", "ancillary_fields", "rfq_fields"]:
                for field, v in fieldMapJson[a].items():
                    if field == fieldId:
                        v["display"] = name
                        break
            saveFieldMapJson(fieldMapJson)
            # print(fieldMapJson)
            pub.sendMessage("field-map-updated")
        except Exception as e:
            print(e)
            logger.info("Failed to rename column", exc_info=True)

    def onHeaderLeftClicked(self, columnIndex):
        self.horizontalHeader().sectionClicked.disconnect()
        pos = QCursor.pos()
        # pos = self.horizontalHeader()._sectionPosClicked
        start = time.time()
        self.menu: QMenu = self.filterProxyModel.buildFilterMenu(self, columnIndex, self.onMenuClose)
        if not self.menu:
            self.onMenuClose()
            return True
        print(f"Build filter menu execution time: {time.time() - start}s")
        self.menu.exec(pos)

    def onMenuClose(self):
        self.horizontalHeader().sectionClicked.connect(self.onHeaderLeftClicked)

    def autoSizeColumns(self):
        max_column_width = 400
        min_column_width = 100  # Define a minimum column width
        width_multiplier = 15
        import time
        start = time.time()
        try:
            if self.model():
                df = self.model().sourceModel()._data[:100]
                for column in range(self.model().columnCount()):
                    field = self.model().headerData(column, Qt.Orientation.Horizontal, Qt.UserRole)
                    display = self.model().headerData(column, Qt.Orientation.Horizontal, Qt.DisplayRole)
                    
                    # --> Added
                    # Ensure the column data is of string type
                    if df[field].apply(lambda x: isinstance(x, (str, bytes))).all():
                        longest = max(int(df[field].astype(bytes).str.len().max()), len(display))
                    else:
                        # Handle non-string data by converting to string and calculating length
                        longest = max(int(df[field].astype(str).str.len().max()), len(display))
                    # --> Added
                    
                    #longest = max(int(df[field].astype(bytes).str.len().max()), len(field))
                        
                    column_width = longest * width_multiplier  # Increase for readability
                    column_width = min(max(column_width, min_column_width), max_column_width)
                    self.setColumnWidth(column, column_width)
            #logger.info(f"Autosize columns time {time.time() - start}")
            print(f"\n\nAutosize columns time {time.time() - start}")
        except Exception as e:
            logger.error(f"Error sizing contents: {e}", exc_info=True)

    def addColumn(self, column: str):
        assert isinstance(column, str)
        self.model().sourceModel().addColumn(column)

    def addColumns(self, columns: list = [], allowDuplicates: bool = False):
        """Append empty columns. List parameter is a list<str> of column names"""
        assert isinstance(columns, list)
        if allowDuplicates:
            self.model().sourceModel().addColumns(columns)
            return
        unique = [c for c in columns if c not in self.model().sourceModel()._data.columns]
        self.model().sourceModel().addColumns(unique)
        self.getDefaultColumnNameOrder() # update cache

    def setComboboxDelegateForColumn(self, field, options, showFractions=False):
        """This sets a ComboBox delegate for a specific column"""
        delegate = ComboBoxDelegate(self, options=options, filterModel=self.filterProxyModel, showFractions=showFractions)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)
    
    def setFloatDelegateForColumn(self, field, options, showFractions=False):
        """This sets a ComboBox delegate for a specific column"""
        raise NotImplementedError
        delegate = ComboBoxDelegate(self, options=options, filterModel=self.filterProxyModel, showFractions=showFractions)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)

    def setLineEditDelegateForColumn(self, field, readonly=False):
        """This sets a ComboBox delegate for a specific column"""
        # delegate = LineEditDelegate(self, options=options, filterModel=self.filterProxyModel)
        if field in ["Category", "pdf_id"]:
            delegate = ReadOnlyEditDelegate(self, filterModel=self.filterProxyModel)
        else:
            delegate = LineEditDelegate(self, filterModel=self.filterProxyModel)
        column: int = self.model().sourceModel()._data.columns.get_loc(field)
        if column == -1:
            # Ignore setting delegate as column index could not be found
            return
        self.setItemDelegateForColumn(column, delegate)

    def getDefaultColumnNameOrder(self):
        """Header data will always remain the same. So this is the default order"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            name = self.model().headerData(n, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
            columns.append(name)
            self._cacheColumnToIndex[name] = n
            self._cacheIndexToColumn[n] = name
        self._cacheDefaultColumnOrder = columns
        return columns

    def getSwappedColumnNameOrder(self, 
                                  excludeHidden=False, 
                                  role=Qt.ItemDataRole.UserRole, 
                                  cache: bool = True) -> list:
        """Use `x` section position to tell us column order. Maybe a better official way?"""
        columns = []
        for n in range(self.horizontalHeader().count()):
            pos = self.horizontalHeader().sectionPosition(n)
            if excludeHidden:
                if self.isColumnHidden(n):
                    continue
            value = (self.model().headerData(n, Qt.Orientation.Horizontal, role), pos)
            columns.append(value)
        columns = sorted(columns, key=lambda k: k[1])
        columns = [c[0] for c in columns]
        if cache:
            for n, c in enumerate(columns):
                self._cacheSwappedColumnToIndex[c] = n
                self._cacheSwappedIndexToColumn[n] = c
        return columns

    def setColumnOrder(self, columns: list):
        """Not the most efficient but swaps until order is sorted"""
        start = time.time()
        defaultOrder = self.getDefaultColumnNameOrder()
        for c in defaultOrder:
            if c not in columns:
                columns.append(c)
        columns = [c for c in columns if c in defaultOrder]
        currentOrder = self.getSwappedColumnNameOrder()
        assert len(columns) == len(defaultOrder)  # Should not fail
        for n, c in enumerate(columns):
            index = currentOrder.index(c)
            if index == -1:
                continue
            if index == n:
                continue
            self.horizontalHeader().swapSections(index, n)
            currentOrder = self.getSwappedColumnNameOrder()

    def restoreDefaultColumnOrder(self):
        self.setColumnOrder(self.getDefaultColumnNameOrder())

    def unhideAllColumns(self):
        for n in range(self.filterProxyModel.columnCount()):
            field = self.filterProxyModel.headerData(n, Qt.Orientation.Horizontal, Qt.UserRole)
            if field not in self._forceHiddenColumns:
                self.showColumn(n)

    def setHiddenColumns(self, hidden):
        for n in range(self.filterProxyModel.columnCount()):
            field = self.filterProxyModel.headerData(n, Qt.Orientation.Horizontal, Qt.UserRole)
            if field in hidden:
                self.hideColumn(n)
            else:
                self.showColumn(n)

    def frozenTableSelectionChanged(self, selected, deselected):
        """Mirror selections"""
        super(FrozenTableView, self.frozenTable).selectionChanged(selected, deselected)
        if self.selectionModel():
            self.selectionModel().select(selected, QItemSelectionModel.SelectionFlag.Select)
            self.selectionModel().select(deselected, QItemSelectionModel.SelectionFlag.Deselect)
        return super().selectionChanged(selected, deselected)

    def initFreezePane(self) -> FrozenTableView:
        frozenTable: FrozenTableView = FrozenTableView(self, checkboxStyle=self.checkboxStyle)
        frozenTable.hide()
        frozenTable.installEventFilter(self)
        # frozenTable.sgnSelectionChanged.connect(self.frozenTableSelectionChanged)
        frozenTable.selectionChanged = self.frozenTableSelectionChanged
        # Horizontal header link
        self.horizontalHeader().sectionResized.connect(self.updateSectionWidth)
        self.verticalHeader().sectionResized.connect(self.updateSectionHeight)
        frozenTable.verticalScrollBar().valueChanged.connect(self.verticalScrollBar().setValue)
        self.verticalScrollBar().valueChanged.connect(frozenTable.verticalScrollBar().setValue)
        frozenTable.setFocusPolicy(Qt.NoFocus)
        frozenTable.verticalHeader().hide()
        self.viewport().stackUnder(frozenTable)
        frozenTable.horizontalHeader().headerChecked.connect(self.onHorizontalHeaderChecked)
        frozenTable.horizontalHeader().sectionClicked.connect(self.onHeaderLeftClicked)

        self.setHorizontalScrollMode(self.ScrollMode.ScrollPerPixel)
        self.setVerticalScrollMode(self.ScrollMode.ScrollPerPixel)
        return frozenTable

    def freezePane(self):
        """Display the frozen table"""
        if not self.editFreezeColumns:
            return
        if not self.frozenTable.isHidden():
            return
        # When frozen, swap columns to freeze at the start on normal table
        self.getDefaultColumnNameOrder() # call to update cache
        swappedColumnOrder = self.getSwappedColumnNameOrder()
        self.cacheSwappedOrder = [c for c in swappedColumnOrder]
        validColumns = []
        for name in self.editFreezeColumns:
            try:
                self._cacheSwappedColumnToIndex[name]
                validColumns.append(name)
            except ValueError:
                pass
        if not validColumns:
            return

        for name in swappedColumnOrder:
            try:
                v = self._cacheColumnToIndex[name]
                self.cacheColumnWidth[name] = self.columnWidth(v)
            except ValueError:
                pass

        # Prepend the frozen columns
        for name in reversed(validColumns):
            del swappedColumnOrder[swappedColumnOrder.index(name)]
        for name in reversed(validColumns):
            swappedColumnOrder = [name] + swappedColumnOrder

        validColumnsIndex = []
        for name in validColumns:
            try:
                v = self._cacheColumnToIndex[name]  # Index on default, not swapped for frozen
                validColumnsIndex.append(v)
            except ValueError:
                pass
        self.setColumnOrder(swappedColumnOrder)

        for col in range(0, self.model().columnCount()):
            if col not in validColumnsIndex:
                self.frozenTable.setColumnHidden(col, True)
            else:
                delegate = self.itemDelegateForColumn(col)
                if isinstance(delegate, BaseDelegate):
                    # options = [d for d in delegate._options]
                    self.frozenTable.setItemDelegateForColumn(col, delegate.clone())

        self.frozenTable.setColumnOrder(validColumns)
        self.frozenColumns = validColumns
        self.horizontalHeader().sectionMoved.connect(self.onSectionMoved)
        self.horizontalHeader().sectionResized.connect(self.onColumnResize)
        self.frozenTable.horizontalHeader().sectionResized.connect(self.onFrozenColumnResize)
        # Restore column width to prevent reset
        # self.frozenTable.resizeColumnsToContents()
        self.frozenTable.show()
        max_column_width = 400
        min_column_width = 100  # Define a minimum column width
        width_multiplier = 15
        try:
            df = self.model().sourceModel()._data[:100]
            for _, field in enumerate(self.frozenColumns):
                try:
                    v = self._cacheColumnToIndex[field]
                    # column_width = self.cacheFrozenColumnWidth.get(field, None)
                    # if column_width is None:
                    longest = max(int(df[field].astype(bytes).str.len().max()), len(field))
                    column_width = longest * width_multiplier  # Increase for readability
                    column_width = min(max(column_width, min_column_width), max_column_width)
                    self.frozenTable.setColumnWidth(v, column_width)
                except ValueError:
                    pass
        except Exception as e:
            logger.debug("No model to base column resize on")

        # So we can perform hitbox detection on frozen pane
        self.frozenTable.mousePressEvent = self.frozenMousePressEvent
        self.frozenTable.mouseReleaseEvent = self.frozenMouseReleaseEvent
        self.onFrozenColumnResize()
        self.onColumnResize()
        self.refreshCheckboxColumn()

    def onFrozenColumnResize(self):
        """When resizing columns on the frozen table, mirror onto the main"""
        columnOrder = self.getDefaultColumnNameOrder()
        for name in self.frozenColumns:
            try:
                v = columnOrder.index(name)
                width = self.frozenTable.columnWidth(v)
                self.setColumnWidth(v, width)
            except ValueError:
                pass
        self.updateFrozenTableGeometry()

    def onColumnResize(self):
        """When resizing columns on the main table, mirror onto the frozen"""
        # Mirror the column widths into the frozen table columns
        columnOrder = self.getDefaultColumnNameOrder()
        for n, name in enumerate(self.frozenColumns):
            try:
                v = columnOrder.index(name)
                width = self.columnWidth(v)
                # if self.frozenTable:
                self.frozenTable.setColumnWidth(v, width)
            except ValueError:
                pass
        self.updateFrozenTableGeometry()

    def updateSectionWidth(self, logicalIndex, oldSize, newSize):
        if logicalIndex == 0:
            self.frozenTable.setColumnWidth(0, newSize)
            self.updateFrozenTableGeometry()

    def updateSectionHeight(self, logicalIndex, oldSize, newSize):
        self.frozenTable.setRowHeight(logicalIndex, newSize)

    def resizeEvent(self, event):
        super(GroupedTableView, self).resizeEvent(event)
        self.updateFrozenTableGeometry()

    def getSumFrozenColumnWidth(self):
        if not self.frozenTable.isVisible():
            return 0
        sumColumnWidth = 0
        # for col in range(0, self.frozenTable.model().columnCount()):
        #     if self.frozenTable.isColumnHidden(col):
        #         continue
        #     sumColumnWidth += self.frozenTable.columnWidth(col)  
        for col in range(0, self.frozenTable.model().columnCount()):
            if self.frozenTable.isColumnHidden(col):
                continue
            w = self.columnWidth(col)
            # if self.frozenTable.isVisible():
            #     w = max(200, w)
            self.setColumnWidth(col, w)
            sumColumnWidth += w
        return sumColumnWidth

    def updateFrozenTableGeometry(self):
        # if not self.frozenTable.isVisible():
        #     return
        if not self.frozenTable.model():
            return

        self.frozenTable.setGeometry(
                self.verticalHeader().width() + self.frameWidth(),
                self.frameWidth(), self.getSumFrozenColumnWidth() + 2,
                self.viewport().height() + self.horizontalHeader().height())

    def unFreezePane(self):
        if self.frozenTable and not self.frozenTable.isHidden():
            try:
                self.horizontalHeader().sectionMoved.disconnect(self.onSectionMoved)
                self.horizontalHeader().sectionResized.disconnect(self.onColumnResize)
                self.frozenTable.horizontalHeader().sectionResized.disconnect(self.onFrozenColumnResize)
            except Exception as e:
                print(e)
            # Cache frozen column widths for next reopen
            if self.editFreezeColumns:
                for name in self.editFreezeColumns:
                    try:
                        v = self._cacheColumnToIndex[name]
                        width = self.frozenTable.columnWidth(v)
                        self.cacheFrozenColumnWidth[name] = width
                    except ValueError:
                        pass
            self.frozenTable.hide()
            # Revert column order before freeze
            self.setColumnOrder(self.cacheSwappedOrder)
            for name in self.cacheSwappedOrder:
                try:
                    v = self._cacheColumnToIndex[name]
                    self.setColumnWidth(v, self.cacheColumnWidth.get(name, 100))
                except ValueError:
                    pass
            self.cacheSwappedOrder = None
        self.refreshCheckboxColumn()
    
    def setEditFreezeColumns(self, columns: list):
        """Set columns we want to freeze when editing"""
        self.editFreezeColumns = columns

    def selectSingleIndex(self, index):
        self.selectionModel().select(index, QItemSelectionModel.SelectionFlag.ClearAndSelect)
        # self.currentChanged(index, None)

    def deleteSelection(self):
        """Clear selected cells"""
        if not self.selectedIndexes():
            return
        for index in self.selectedIndexes():
            self.filterProxyModel.setData(index, None, Qt.ItemDataRole.EditRole)
            self.update(index)

    def copySelection(self):
        """Copy selected cells"""
        if not self.selectedIndexes():
            return
        self.updateClipboardSelections(clearCells=False)

    def indexToFilteredIndex(self, index):
        """"""
        return

    def updateClipboardSelections(self, clearCells: bool = False):
        """Set the clipboard to the currently selected cells"""
        if True or not self._cacheSwappedColumnToIndex:
            #TODO more efficient cache
            self.getSwappedColumnNameOrder()
            self.getDefaultColumnNameOrder()

        values = []
        xs = set()
        ys = set()
        lastColumn = None
        lastRow = None
        invalidSelect = False # If selection is not contiguous block, we dont allow
        for index in self.selectedIndexes():
            self.indexToFilteredIndex(index)
            columnName = self._cacheIndexToColumn[index.column()]
            mapX = self._cacheSwappedColumnToIndex[columnName]
            xs.add(mapX)
            ys.add(index.row())
            data = index.data(Qt.ItemDataRole.UserRole)
            values.append(data)
       
        width = max(xs) - min(xs) + 1
        height = max(ys) - min(ys) + 1
        shape = [height, width]

        # Must be a contiguous block for valid copy
        invalidSelect = width * height == len(values)
        if not invalidSelect:
            QMessageBox.question(self, 
                    "Information", 
                    "This function cannot be used with current selection", 
                    QMessageBox.Ok, 
                    QMessageBox.Ok)
            return

        if clearCells:
            for index in self.selectedIndexes():
                self.filterProxyModel.setData(index, None, Qt.ItemDataRole.EditRole)
                self.update(index)

        self.clipboard.set(values, shape)

    def cutSelection(self):
        """Cut selected cells"""
        if not self.selectedIndexes():
            return
        self.updateClipboardSelections(clearCells=True)

    def canPasteFit(self, firstIndex):
        """Test paste, return result"""
        rows = self.filterProxyModel.rowCount()
        columns = len(self._cacheSwappedIndexToColumn)
        if not self.clipboard.shape:
            return False
        height, width = self.clipboard.shape
        tooTall = firstIndex.row() + height > rows
        columnName = self._cacheIndexToColumn[firstIndex.column()]
        nextSwappedIndex = self._cacheSwappedColumnToIndex[columnName]
        tooWide = nextSwappedIndex + width > columns
        if tooTall or tooWide:
            return False
        return True

    def pasteOneToManyCells(self, value):
        """Paste a single value to all the selected cells"""
        for index in self.selectedIndexes():
            try:
                self.filterProxyModel.setData(index, value, Qt.ItemDataRole.EditRole)
                self.update(index)
            except Exception as e:
                logger.info("Cannot paste item", exc_info=True)
    
    def pasteSelection(self):
        """Cut selected cells"""
        if not self.selectedIndexes():
            return
        if len(self.selectedIndexes()) > 1 and len(self.clipboard.data) == 1:
            self.pasteOneToManyCells(self.clipboard.data[0])

        first = self.selectedIndexes()[0]
        startX = first.column()
        newSelections = []
        canPaste = self.canPasteFit(first)
        if not canPaste:
            resp = QMessageBox.question(self, 
                                        'Confirm Paste', 
                                        "Content of clipboard is greater than insertion space. Insert anyway?", 
                                        QMessageBox.Yes | QMessageBox.No, 
                                        QMessageBox.Yes)
            if resp == QMessageBox.No:
                return

        swappedColumns = self.getSwappedColumnNameOrder(excludeHidden=True)
        if self.clipboard.shape:
            data = self.clipboard.data
            height, width = self.clipboard.shape
            ignored = 0
            for x in range(width):
                # TODO - Ugly code. Cleaner way of mapping indexes
                columnName = self._cacheIndexToColumn[startX]
                nextSwappedIndex = self._cacheSwappedColumnToIndex[columnName] + x
                if nextSwappedIndex >= len(swappedColumns):
                    continue
                nextColumnName = swappedColumns[nextSwappedIndex]
                if not nextColumnName:
                    continue
                nextColumn = self._cacheColumnToIndex[nextColumnName]
                # TODO - avoid pasting into read only fields
                if nextColumnName in ["pdf_id", "Category"]:
                    continue
                startY = first.row()
                for y in range(height):
                    if startY + y > self.filterProxyModel.rowCount() - 1:
                        break
                    if y + (height * x) > len(data) - 1:
                        continue
                    v = data[y + (height * x)]
                    try:
                        indexPaste = self.model().index(startY+y, nextColumn)
                        self.filterProxyModel.setData(indexPaste, v, Qt.ItemDataRole.EditRole)
                        self.update(indexPaste)
                        newSelections.append(indexPaste)
                    except Exception as e:
                        ignored += 1
                        logger.info("Some values could not be pasted. Due to strict pandas data type. TODO?", exc_info=True)
                
            if ignored:
                QMessageBox.question(self, 
                                'Paste result', 
                                f"Invalid data types - Could not paste {ignored} values. ", 
                                QMessageBox.Ok, 
                                QMessageBox.Ok)
                   
        # Select all newly pasted cells
        self.clearSelection()
        for n in newSelections:
            self.selectionModel().select(n, QItemSelectionModel.SelectionFlag.Select)
    
    def eventFilter(self, source, event: QEvent):
        if source == self.viewport():
            if event.type() == QEvent.Type.MouseButtonRelease:
                if self.frozenTable:
                    self.frozenTable.setDisabled(False)
                self._mouseDown = False
            elif event.type() == QEvent.Type.MouseButtonPress:
                """If selecting a cell after multiple selected, deselected the others"""
                if self.frozenTable:
                    self.frozenTable.setDisabled(True)
                selectedIndexes = self.selectionModel().selectedIndexes()
                selectionCount = len(selectedIndexes)
                if not self._mouseDown and selectionCount > 0:
                    index = self.selectedIndexes()[0]
                    self.selectSingleIndex(index)
                self._mouseDown = True

        if source == self:
            if event.type() == QEvent.KeyPress:
                # Disable keys if table index is in frozen table
                index = self.currentIndex()
                try:
                    columnName = self._cacheIndexToColumn(index.column())
                    if columnName in self.editFreezeColumns:
                        return True
                except:
                    logger.info("Index is visible in frozen table. Events suppressed in main table")
                # if event.key() in [Qt.Key.Key_Left, Qt.Key.Key_Right, Qt.Key.Key_Up, Qt.Key.Key_Down]:
                #     return True
                # Control modifiers
                if QApplication.keyboardModifiers() == (Qt.ControlModifier | Qt.ShiftModifier):
                    # Go to next blank cell
                    if event.key() == Qt.Key.Key_Left:
                        direction = [-1, 0]
                    elif event.key() == Qt.Key.Key_Right:
                        direction = [1, 0]
                    elif event.key() == Qt.Key.Key_Up:
                        direction = [0, -1]
                    elif event.key() == Qt.Key.Key_Down:
                        direction = [0, 1]
                    else:
                        return False
                    self.goToNextNonBlankCell(direction)
                    return True
                if QApplication.keyboardModifiers() == Qt.ControlModifier:
                    if event.key() == Qt.Key.Key_C:
                        self.copySelection()
                    elif event.key() == Qt.Key.Key_X:
                        self.cutSelection()
                    elif event.key() == Qt.Key.Key_V:
                        self.pasteSelection()
                    elif event.key() == Qt.Key.Key_A:
                        self.selectAll()
                    elif event.key() == Qt.Key.Key_Home:
                        # Scroll top left index and select
                        self.verticalScrollBar().setValue(self.verticalScrollBar().minimum())
                        self.horizontalScrollBar().setValue(self.horizontalScrollBar().minimum())
                        firstColumn = self.getSwappedColumnNameOrder(excludeHidden=True, cache=False)[0]
                        firstIndex = self.model().index(0, self._cacheColumnToIndex[firstColumn])
                        self.scrollTo(firstIndex)
                        self.selectionModel().select(firstIndex, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                        return True
                    elif event.key() == Qt.Key.Key_End:
                        # Scroll to bottom right index and select
                        self.verticalScrollBar().setValue(self.verticalScrollBar().maximum())
                        self.horizontalScrollBar().setValue(self.horizontalScrollBar().maximum())
                        lastColumn = self.getSwappedColumnNameOrder(excludeHidden=True, cache=False)[-1]
                        lastIndex = self.model().index(self.model().rowCount()-1, self._cacheColumnToIndex[lastColumn])
                        self.scrollTo(lastIndex)
                        self.selectionModel().select(lastIndex, QItemSelectionModel.SelectionFlag.ClearAndSelect)
                        return True
                    elif event.key() == Qt.Key.Key_D:
                        # Copy from row above
                        self.copyFromRowAbove()
                        return True
                    else:
                        return True
                    return True

                if event.key() == Qt.Key.Key_Return:
                    self.moveCell(1, 0)
                elif event.key() == Qt.Key.Key_Escape:
                    self.unFreezePane()
                elif event.key() == Qt.Key.Key_Delete:
                    self.deleteSelection()
                else:
                    # Start editing when typing alphanumeric and space
                    # Override the starting text with this char
                    s = event.text()
                    if s == ' ' or s.isalpha() or s.isalnum():
                        self.startEditing(self.currentIndex())
                    else:
                        pass
        return False

    def getSelectedRowData(self, includeUid=True):
        """TODO, currently return uid and associated id. Add ability to choose columns"""
        if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            return self.getCheckedSelections()
        else:
            # Old
            self.getDefaultColumnNameOrder()
            uidIndex = self._cacheColumnToIndex.get("__uid__")
            idIndex = self._cacheColumnToIndex.get("id")
            rows = set()
            uids = []
            ids = []
            selectedIndexes = []
            if self.frozenTable.isVisible():
                selectedIndexes = self.frozenTable.selectedIndexes()
            if not selectedIndexes:
                selectedIndexes =  self.selectedIndexes()
            for index in selectedIndexes:
                row = index.row()
                if row in rows:
                    continue
                rows.add(row)
                uid = self.filterProxyModel.index(index.row(), uidIndex).data(Qt.ItemDataRole.UserRole)
                if idIndex is not None:
                    _id = self.filterProxyModel.index(index.row(), idIndex).data(Qt.ItemDataRole.UserRole)
                    ids.append(_id)
                # values = (uid, _id)
                uids.append(uid)
            data = {
                "__uid__": uids,
            }
            if ids:
                data["id"] = ids
            df = pd.DataFrame(data)
            return df

    def onHorizontalHeaderChecked(self, checked: bool):
        """Select/deselect all table rows"""
        columnIndex = self._cacheColumnToIndex["__checked__"]
        self.frozenTable.horizontalHeader()._on = checked
        # Updates rows checkbox selection
        for n in range(self.model().rowCount()):
            index = self.model().index(n, columnIndex)
            self.model().setData(index, checked, Qt.EditRole)
        self.refreshCheckboxColumn()

    def onHorizontalHeaderSectionMoved(self, event):
        self.refreshCheckboxColumn()

    def refreshCheckboxColumn(self):
        """Detects the first column field so we can add a checkbox to the cell"""
        if self.checkboxStyle != GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            return
        firstField = None
        if self.frozenTable.isVisible() and self.frozenColumns:
            firstField = self.frozenColumns[0]
        else:
            for n in range(self.horizontalHeader().count()):
                if self.isColumnHidden(n):
                    continue
                pos = self.horizontalHeader().sectionPosition(n)
                if pos == 0:
                    firstField = self.model().headerData(n, Qt.Orientation.Horizontal, Qt.ItemDataRole.UserRole)
                    break
        try:
            self.model().sourceModel().firstColumn = firstField
            self.frozenTable.model().firstColumn = firstField
            for n in range(self.model().rowCount()):
                index = self.model().index(n, self._cacheColumnToIndex[firstField])
                self.model().dataChanged.emit(index, index)
        except Exception as e:
            logger.info(e)
            if self.model():
                self.model().sourceModel().firstColumn = None

    def getCheckedSelections(self) -> pd.DataFrame:
        """Return dataframe of items which have been selected"""
        df = self.model().sourceModel()._data
        return df[df['__checked__'] == True]

    def mousePressEvent(self, event: QMouseEvent):
        self.selectionModel().clear()
        self.frozenTable.selectionModel().clear()
        if event.button() == Qt.MouseButton.LeftButton:
            self._mousePos = event.position().toPoint()
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        # Checkbox selection check
        if event.button() == Qt.MouseButton.LeftButton:
            if self._mousePos:
                if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
                    pos = event.position().toPoint()
                    index = self.indexAt(pos)
                    sectionPosition = self.horizontalHeader().sectionPosition(index.column())
                    if sectionPosition == 0:
                        opt: QStyleOptionButton = QStyleOptionButton()
                        opt.rect = self.visualRect(index)
                        # Both mouse down and release must be inside hitbox
                        rect = self.style().subElementRect(QStyle.SubElement.SE_ItemViewItemCheckIndicator, opt)
                        if (rect.contains(pos)) and rect.contains(self._mousePos):
                            self.toggleRowChecked(index.row())

            self._mousePos = None
        super().mouseReleaseEvent(event)
    
    def frozenMousePressEvent(self, event: QMouseEvent):
        self.selectionModel().clear()
        self.frozenTable.selectionModel().clear()
        if event.button() == Qt.MouseButton.LeftButton:
            self._mousePos = event.position().toPoint()
        super(FrozenTableView, self.frozenTable).mousePressEvent(event)

    def frozenMouseReleaseEvent(self, event: QMouseEvent):
        # Checkbox selection check
        if event.button() == Qt.MouseButton.LeftButton:
            if self._mousePos:
                if self.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
                    pos = event.position().toPoint()
                    index = self.indexAt(pos)
                    sectionPosition = self.horizontalHeader().sectionPosition(index.column())
                    if sectionPosition == 0:
                        opt: QStyleOptionButton = QStyleOptionButton()
                        opt.rect = self.visualRect(index)
                        # Both mouse down and release must be inside hitbox
                        rect = self.style().subElementRect(QStyle.SubElement.SE_ItemViewItemCheckIndicator, opt)
                        if (rect.contains(pos)) and rect.contains(self._mousePos):
                            self.toggleRowChecked(index.row())

            self._mousePos = None
        super(FrozenTableView, self.frozenTable).mouseReleaseEvent(event)

    def toggleRowChecked(self, row):
        columnIndex = self._cacheColumnToIndex["__checked__"]
        index = self.model().index(row, columnIndex)
        checked = self.model().data(index, Qt.UserRole)
        self.model().setData(index, not checked, Qt.EditRole)

    def removeCheckedRows(self):
        excludeDf = self.getCheckedSelections()
        sourceDf = self.getDataFrame(drop_uid=False)
        newDf = sourceDf[~sourceDf.loc[:,'__uid__'].isin(excludeDf['__uid__'])]
        self.setTableData(newDf)
        self.refreshCheckboxColumn()
        if self.model():
            self.model().invalidateFilter() # This will reapply the filters 

    def removeSelectedRows(self):
        excludeDf = self.getSelectedRowData()
        sourceDf = self.getDataFrame(drop_uid=False)
        newDf = sourceDf[~sourceDf.loc[:,'__uid__'].isin(excludeDf['__uid__'])]
        self.setTableData(newDf)
        self.refreshCheckboxColumn()
        if self.model():
            self.model().invalidateFilter() # This will reapply the filters 

    def onSectionMoved(self, section, oldVisualIndex, newVisualIndex):
        """
        If a column is swapped with frozen column, alter the behavior to move to
        first unfrozen column
        """
        if self.frozenTable.isVisible():
            if newVisualIndex < len(self.editFreezeColumns):
                self.horizontalHeader().moveSection(newVisualIndex, len(self.editFreezeColumns))

    def onVerticalHeaderSectionPressed(self, row):
        self.selectionModel().clearSelection()
        self.frozenTable.selectionModel().clearSelection()
        self.selectRow(row)

    def tableMouseMoveEvent(self, event):
        super().mouseMoveEvent(event)
        if not event.buttons():
            index = self.indexAt(event.pos())
            data = self.model().sourceModel()._data
            fieldName = data.columns[index.column()]
            if self._fieldMap.get(fieldName, {}).get("options"):
                self.model().sourceModel()._fieldMap["__dropdown_icon__"] = (index.column(), index.row())
            else:
                self.hideCellDropdownIcon()
        else:
            self.hideCellDropdownIcon()

    def viewportEvent(self, event):
        if event.type() == QEvent.Leave:
            self.hideCellDropdownIcon()
        return super().viewportEvent(event)            

    def onHorizontalScrollbar(self, event):
        self.hideCellDropdownIcon()

    def onVerticalScrollbar(self, event):
        self.hideCellDropdownIcon()
    
    def hideCellDropdownIcon(self):
        """Hides the indicator when hovering over a cell with dropdown options"""
        self.model().sourceModel()._fieldMap["__dropdown_icon__"] = None