# relative x0, y0, x1, y1
full_size = [0, 0, 1, 1]

# relative x0, y0, x1, y1 region of full_size
region = [0.2, 0.2, 0.3, 0.3]

# relative x, y point of region
point = [0.25, 0.25]

def map_point_to_full_size(point, region):
    """
    Maps a point that is relative to a region back to coordinates relative to full_size.

    Args:
        point (list): [x, y] coordinates relative to region
        region (list): [x0, y0, x1, y1] coordinates of region relative to full_size
        full_size (list): [x0, y0, x1, y1] base coordinates

    Returns:
        list: [x, y] coordinates relative to full_size
    """
    # Calculate region's actual width and height
    region_width = region[2] - region[0]
    region_height = region[3] - region[1]

    # Map point to actual position within region
    x = region[0] + (point[0] * region_width)
    y = region[1] + (point[1] * region_height)

    return [x, y]

# Example usage with the given coordinates
mapped_point = map_point_to_full_size(point, region)
print(f"Point {point} in region {region} maps to {mapped_point} in full_size {full_size}")
