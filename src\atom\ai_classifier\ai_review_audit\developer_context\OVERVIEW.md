# BOM Classification Audit System - Development Guide

## 📋 Executive Summary

This document outlines the development of a post-processing audit system for Bill of Materials (BOM) classification. The system validates AI-classified piping materials, reduces hallucinations, and ensures field-level consistency through rule-based validation and LLM review.

## 🎯 Purpose & Objectives

### Core Purpose
Create an intelligent audit layer that reviews AI-classified BOM items to:
- Detect and correct hallucinations (e.g., ASTM A106 classified as "stainless" instead of "carbon")
- Flag low-confidence classifications for human review
- Handle ambiguous descriptions (e.g., "VENDOR ASYM18456") appropriately
- Ensure consistency across related fields

### Expected Outcomes
1. **Reduced Error Rate**: Catch 95%+ of classification errors before downstream processing
2. **Structured Feedback**: Provide confidence scores and explanations for all corrections
3. **Dynamic Rule Application**: Apply category-specific rules without code changes
4. **Model Agnostic**: Support multiple LLMs with easy switching for A/B testing

## 🔑 Key Features

### 1. Dynamic Rule Management
- **Rule Bank**: Category-specific rules stored in a database/configuration
- **Rule Types**:
  - Cross-field validation (e.g., ASTM → Material mapping)
  - Valid value constraints
  - Pattern matching rules
  - Semantic consistency checks

### 2. Category-Aware Processing
- Each category has:
  - Description for LLM context
  - Valid options list
  - Specific validation rules
  - Confidence thresholds

### 3. Multi-Model Support
- Primary: Gemini 1.5 Flash
- Fallback: Gemini 2.0 Flash
- Easy model switching for testing
- Performance metrics per model

### 4. Async Processing Pipeline
- Concurrent item processing
- Step-based architecture
- Easy step modification
- Built-in retry logic

### 5. Semantic Filtering (Future Enhancement)
- Pre-filter valid options using semantic similarity
- Reduce token usage
- Improve model accuracy

## 🏗️ System Architecture

```mermaid
graph TD
    A[BOM Item] --> B[Pre-Processing]
    B --> C[Rule Extraction]
    C --> D[Initial Validation]
    D --> E{Needs LLM?}
    E -->|No| F[Return Result]
    E -->|Yes| G[Semantic Filter]
    G --> H[LLM Review]
    H --> I[Post-Processing]
    I --> F
    F --> J[Audit Report]
```

## 💻 Implementation Steps

### Step 1: Set Up Core Infrastructure

```python
# config.py
from enum import Enum
from typing import Dict, List, Optional
from pydantic import BaseModel

class ModelType(Enum):
    GEMINI_15_FLASH = "gemini-1.5-flash"
    GEMINI_20_FLASH = "gemini-2.0-flash"
    GPT4O_MINI = "gpt-4o-mini"  # For future expansion

class CategoryConfig(BaseModel):
    name: str
    description: str
    valid_options: List[str]
    rules: List[Dict]
    confidence_threshold: float = 0.7

class AuditConfig(BaseModel):
    primary_model: ModelType = ModelType.GEMINI_15_FLASH
    fallback_model: ModelType = ModelType.GEMINI_20_FLASH
    max_retries: int = 3
    timeout: int = 30
    batch_size: int = 10
```

### Step 2: Rule Bank Implementation

```python
# rule_bank.py
from typing import Dict, List, Any
import json

class RuleBank:
    def __init__(self):
        self.rules: Dict[str, CategoryConfig] = {}
        self.load_rules()
    
    def load_rules(self):
        """Load rules from database or configuration file"""
        # Example rule structure
        self.rules = {
            "Material": CategoryConfig(
                name="Material",
                description="The base material type of the piping component",
                valid_options=["carbon", "stainless", "alloy", "plastic", "brass"],
                rules=[
                    {
                        "type": "cross_field",
                        "condition": {"field": "Astm", "value": "a106"},
                        "requirement": {"field": "Material", "value": "carbon"},
                        "explanation": "ASTM A106 is specifically for carbon steel pipes"
                    },
                    {
                        "type": "cross_field",
                        "condition": {"field": "Astm", "value": "a312"},
                        "requirement": {"field": "Material", "value": "stainless"},
                        "explanation": "ASTM A312 is for stainless steel pipes"
                    }
                ]
            ),
            "Category": CategoryConfig(
                name="Category",
                description="The type of piping component",
                valid_options=["pipe", "fitting", "valve", "flange", "gasket", "miscellaneous"],
                rules=[
                    {
                        "type": "pattern",
                        "pattern": r"^VENDOR\s+[A-Z0-9]+$",
                        "requirement": {"field": "Category", "value": "uncategorized"},
                        "explanation": "Vendor codes provide no material information"
                    }
                ]
            )
        }
    
    def get_rules_for_category(self, category: str) -> CategoryConfig:
        return self.rules.get(category, CategoryConfig(
            name=category,
            description="",
            valid_options=[],
            rules=[]
        ))
```

### Step 3: Audit Pipeline Steps

```python
# pipeline_steps.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import re

class PipelineStep(ABC):
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        pass

class PreProcessingStep(PipelineStep):
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and normalize input data"""
        description = context["description"].strip().upper()
        classification = {k: v.lower().strip() if isinstance(v, str) else v 
                         for k, v in context["classification"].items()}
        
        context["normalized_description"] = description
        context["normalized_classification"] = classification
        return context

class RuleValidationStep(PipelineStep):
    def __init__(self, rule_bank: RuleBank):
        self.rule_bank = rule_bank
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rule-based validation"""
        issues = {}
        classification = context["normalized_classification"]
        
        for field, value in classification.items():
            config = self.rule_bank.get_rules_for_category(field)
            
            # Check valid options
            if config.valid_options and value not in config.valid_options:
                issues[field] = {
                    "value": value,
                    "confidence": 0.1,
                    "explanation": f"'{value}' is not in valid options",
                    "suggested": None
                }
            
            # Apply rules
            for rule in config.rules:
                if not self._check_rule(rule, classification, context["normalized_description"]):
                    issues[field] = {
                        "value": value,
                        "confidence": 0.2,
                        "explanation": rule["explanation"],
                        "suggested": rule["requirement"]["value"]
                    }
        
        context["rule_issues"] = issues
        context["needs_llm"] = len(issues) > 0 or self._is_ambiguous(context["normalized_description"])
        return context
    
    def _check_rule(self, rule: Dict, classification: Dict, description: str) -> bool:
        """Check if a rule is satisfied"""
        if rule["type"] == "cross_field":
            condition_field = rule["condition"]["field"].lower()
            if classification.get(condition_field) == rule["condition"]["value"]:
                req_field = rule["requirement"]["field"].lower()
                return classification.get(req_field) == rule["requirement"]["value"]
        elif rule["type"] == "pattern":
            if re.match(rule["pattern"], description):
                req_field = rule["requirement"]["field"].lower()
                return classification.get(req_field) == rule["requirement"]["value"]
        return True
    
    def _is_ambiguous(self, description: str) -> bool:
        """Check if description is ambiguous"""
        vendor_pattern = r"^VENDOR\s+[A-Z0-9]+$"
        return bool(re.match(vendor_pattern, description)) or len(description) < 10
```

### Step 4: LLM Integration

```python
# llm_handler.py
import google.generativeai as genai
from typing import Dict, Any, Optional
import json
import asyncio
from functools import lru_cache

class LLMHandler:
    def __init__(self, config: AuditConfig, rule_bank: RuleBank):
        self.config = config
        self.rule_bank = rule_bank
        genai.configure(api_key=os.environ["GEMINI_API_KEY"])
        self._setup_models()
    
    def _setup_models(self):
        """Initialize primary and fallback models"""
        self.models = {
            ModelType.GEMINI_15_FLASH: self._create_gemini_model("gemini-1.5-flash"),
            ModelType.GEMINI_20_FLASH: self._create_gemini_model("gemini-2.0-flash")
        }
    
    def _create_gemini_model(self, model_name: str):
        return genai.GenerativeModel(
            model_name,
            generation_config={
                "temperature": 0,
                "response_mime_type": "application/json",
                "max_output_tokens": 500
            },
            system_instruction=self._get_system_prompt()
        )
    
    @lru_cache(maxsize=1)
    def _get_system_prompt(self) -> str:
        return """You are a precise BOM classification auditor.
        
        Your task is to review classifications against the material description and rules.
        
        CRITICAL RULES:
        1. Only classify based on explicit information in the description
        2. Vendor codes (VENDOR XXXXX) should be 'uncategorized'
        3. Follow the specific rules provided for each field
        4. Provide confidence scores based on certainty
        
        Response format:
        {
            "status": "ok" | "issues",
            "issues": {
                "field_name": {
                    "value": "current_value",
                    "confidence": 0.0-1.0,
                    "explanation": "reason for issue",
                    "suggested": "corrected_value"
                }
            }
        }"""
    
    async def review_classification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Review classification using LLM"""
        prompt = self._build_prompt(context)
        
        try:
            # Try primary model
            result = await self._call_model(self.config.primary_model, prompt)
            context["model_used"] = self.config.primary_model.value
        except Exception as e:
            # Fallback to secondary model
            result = await self._call_model(self.config.fallback_model, prompt)
            context["model_used"] = self.config.fallback_model.value
        
        context["llm_result"] = result
        return context
    
    def _build_prompt(self, context: Dict[str, Any]) -> str:
        """Build detailed prompt with rules and context"""
        classification = context["normalized_classification"]
        description = context["normalized_description"]
        
        # Get relevant rules for each field
        field_rules = {}
        for field in classification.keys():
            config = self.rule_bank.get_rules_for_category(field)
            field_rules[field] = {
                "description": config.description,
                "valid_options": config.valid_options,
                "rules": config.rules
            }
        
        prompt = f"""Review this BOM classification:
        
        Description: {description}
        Current Classification: {json.dumps(classification, indent=2)}
        
        Field Rules and Valid Options:
        {json.dumps(field_rules, indent=2)}
        
        Previous Rule-Based Issues Found:
        {json.dumps(context.get("rule_issues", {}), indent=2)}
        
        Analyze each field against the description and rules. Return your assessment."""
        
        return prompt
    
    async def _call_model(self, model_type: ModelType, prompt: str) -> Dict:
        """Call the specified model"""
        model = self.models[model_type]
        response = await asyncio.to_thread(model.generate_content, prompt)
        return json.loads(response.text)
```

### Step 5: Main Audit Pipeline

```python
# audit_pipeline.py
import asyncio
from typing import List, Dict, Any, Optional
from collections import defaultdict
import logging

class BOMAuditPipeline:
    def __init__(self, config: AuditConfig):
        self.config = config
        self.rule_bank = RuleBank()
        self.llm_handler = LLMHandler(config, self.rule_bank)
        self.steps = self._initialize_steps()
        self.metrics = defaultdict(int)
    
    def _initialize_steps(self) -> List[PipelineStep]:
        """Initialize pipeline steps in order"""
        return [
            PreProcessingStep(),
            RuleValidationStep(self.rule_bank),
            ConditionalLLMStep(self.llm_handler),
            PostProcessingStep(),
            MetricsCollectionStep(self.metrics)
        ]
    
    async def audit_item(self, description: str, classification: Dict[str, str]) -> Dict[str, Any]:
        """Audit a single BOM item"""
        context = {
            "description": description,
            "classification": classification,
            "timestamp": datetime.utcnow()
        }
        
        for step in self.steps:
            try:
                context = await step.execute(context)
            except Exception as e:
                logging.error(f"Error in {step.__class__.__name__}: {e}")
                context["error"] = str(e)
                break
        
        return self._format_result(context)
    
    async def audit_batch(self, items: List[Dict[str, Any]], max_concurrent: int = 10) -> List[Dict[str, Any]]:
        """Audit multiple items concurrently"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def audit_with_semaphore(item):
            async with semaphore:
                return await self.audit_item(
                    item["description"], 
                    item["classification"]
                )
        
        tasks = [audit_with_semaphore(item) for item in items]
        return await asyncio.gather(*tasks)
    
    def _format_result(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Format the final audit result"""
        if context.get("error"):
            return {"status": "error", "error": context["error"]}
        
        # Merge rule-based and LLM issues
        all_issues = context.get("rule_issues", {})
        if context.get("llm_result", {}).get("status") == "issues":
            llm_issues = context["llm_result"].get("issues", {})
            for field, issue in llm_issues.items():
                if field not in all_issues or issue["confidence"] > all_issues[field]["confidence"]:
                    all_issues[field] = issue
        
        if all_issues:
            return {"status": "issues", "issues": all_issues}
        else:
            return {"status": "ok"}

class ConditionalLLMStep(PipelineStep):
    def __init__(self, llm_handler: LLMHandler):
        self.llm_handler = llm_handler
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Only call LLM if needed"""
        if context.get("needs_llm", False):
            return await self.llm_handler.review_classification(context)
        return context

class PostProcessingStep(PipelineStep):
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Clean up and finalize results"""
        # Add any post-processing logic here
        return context

class MetricsCollectionStep(PipelineStep):
    def __init__(self, metrics: Dict[str, int]):
        self.metrics = metrics
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Collect metrics for monitoring"""
        self.metrics["total_processed"] += 1
        if context.get("needs_llm"):
            self.metrics["llm_calls"] += 1
        if context.get("rule_issues"):
            self.metrics["rule_violations"] += 1
        if context.get("model_used"):
            self.metrics[f"model_{context['model_used']}"] += 1
        return context
```

### Step 6: Usage Example

```python
# main.py
import asyncio
from audit_pipeline import BOMAuditPipeline, AuditConfig

async def main():
    # Initialize pipeline
    config = AuditConfig(
        primary_model=ModelType.GEMINI_15_FLASH,
        fallback_model=ModelType.GEMINI_20_FLASH,
        batch_size=20
    )
    pipeline = BOMAuditPipeline(config)
    
    # Example items to audit
    items = [
        {
            "description": "Pipe SMLS, ASTM A106, SCH 60, #3000 , PBE",
            "classification": {
                "Category": "miscellaneous",
                "Astm": "a106",
                "Material": "stainless",  # Wrong!
                "schedule": "60",
                "Rating": "3000",
                "End type": "be x be",
                "Forging": "forged"
            }
        },
        {
            "description": "VENDOR ASYM18456",
            "classification": {
                "Category": "pipe",  # Wrong!
                "Material": "carbon"  # Wrong!
            }
        }
    ]
    
    # Audit items
    results = await pipeline.audit_batch(items)
    
    # Process results
    for item, result in zip(items, results):
        print(f"\nDescription: {item['description']}")
        print(f"Result: {result}")
        
        if result["status"] == "issues":
            for field, issue in result["issues"].items():
                print(f"  - {field}: {issue['value']} → {issue['suggested']}")
                print(f"    Confidence: {issue['confidence']}")
                print(f"    Reason: {issue['explanation']}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔄 Future Enhancements

### 1. Semantic Similarity Filtering
```python
# semantic_filter.py
from sentence_transformers import SentenceTransformer
import numpy as np

class SemanticFilter:
    def __init__(self, model_name='all-MiniLM-L6-v2'):
        self.model = SentenceTransformer(model_name)
        self._cache = {}
    
    async def filter_options(self, description: str, options: List[str], top_k: int = 5) -> List[str]:
        """Filter options by semantic similarity to description"""
        # Encode description and options
        desc_embedding = self.model.encode(description)
        option_embeddings = self.model.encode(options)
        
        # Calculate similarities
        similarities = np.dot(option_embeddings, desc_embedding)
        
        # Get top K most similar options
        top_indices = np.argsort(similarities)[-top_k:]
        return [options[i] for i in top_indices]
```

### 2. A/B Testing Framework
```python
# ab_testing.py
class ABTestingFramework:
    def __init__(self):
        self.model_performance = defaultdict(lambda: {"correct": 0, "total": 0})
    
    async def compare_models(self, items: List[Dict], models: List[ModelType]):
        """Run same items through different models and compare"""
        results = {}
        for model in models:
            config = AuditConfig(primary_model=model)
            pipeline = BOMAuditPipeline(config)
            results[model] = await pipeline.audit_batch(items)
        
        return self.analyze_results(results)
```

### 3. Continuous Learning
- Store human feedback on corrections
- Periodically retrain or fine-tune models
- Update rule bank based on patterns

## 📊 Monitoring & Metrics

### Key Metrics to Track
1. **Accuracy Metrics**
   - False positive rate (flagged incorrectly)
   - False negative rate (missed errors)
   - Correction acceptance rate

2. **Performance Metrics**
   - Items processed per second
   - LLM calls per 100 items
   - Average response time

3. **Cost Metrics**
   - Tokens used per item
   - Cost per 1000 audits
   - Model usage distribution

### Dashboard Example
```python
# metrics_dashboard.py
async def get_metrics_summary(pipeline: BOMAuditPipeline) -> Dict:
    return {
        "total_processed": pipeline.metrics["total_processed"],
        "llm_utilization": pipeline.metrics["llm_calls"] / pipeline.metrics["total_processed"],
        "rule_catch_rate": pipeline.metrics["rule_violations"] / pipeline.metrics["total_processed"],
        "model_distribution": {
            k: v for k, v in pipeline.metrics.items() 
            if k.startswith("model_")
        }
    }
```

## 🚀 Deployment Considerations

### 1. Environment Variables
```bash
# .env
GEMINI_API_KEY=your_api_key_here
AUDIT_LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=20
CACHE_TTL=3600
```

### 2. Error Handling
- Implement exponential backoff for API calls
- Log all errors with context
- Provide graceful degradation

### 3. Scaling
- Use Redis for caching common patterns
- Implement queue-based processing for large batches
- Consider deploying as microservice

## 📚 Testing Strategy

### Unit Tests
```python
# test_audit_pipeline.py
import pytest
from audit_pipeline import BOMAuditPipeline

@pytest.mark.asyncio
async def test_vendor_code_detection():
    pipeline = BOMAuditPipeline(AuditConfig())
    result = await pipeline.audit_item(
        "VENDOR ASYM18456",
        {"Category": "pipe", "Material": "carbon"}
    )
    assert result["status"] == "issues"
    assert "Category" in result["issues"]
    assert result["issues"]["Category"]["suggested"] == "uncategorized"

@pytest.mark.asyncio
async def test_astm_material_validation():
    pipeline = BOMAuditPipeline(AuditConfig())
    result = await pipeline.audit_item(
        "Pipe ASTM A106",
        {"Astm": "a106", "Material": "stainless"}
    )
    assert result["status"] == "issues"
    assert result["issues"]["Material"]["suggested"] == "carbon"
```

### Integration Tests
- Test with real Gemini API
- Verify model fallback behavior
- Test batch processing limits

### Performance Tests
- Measure throughput under load
- Monitor memory usage
- Test concurrent request limits

## 🎯 Success Criteria

1. **Accuracy**: 95%+ detection rate for known error patterns
2. **Performance**: < 200ms average response time per item
3. **Cost**: < $0.05 per 1000 items audited
4. **Reliability**: 99.9% uptime with automatic failover
5. **Flexibility**: New rules deployable without code changes

## 📞 Support & Maintenance

- **Documentation**: Keep this guide updated with any changes
- **Monitoring**: Set up alerts for error rates and performance degradation
- **Updates**: Regularly update model versions and rule sets
- **Feedback Loop**: Implement mechanism to collect and analyze user corrections