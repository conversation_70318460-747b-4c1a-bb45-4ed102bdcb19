-- Sum specific columns by project_id
-- Returns the sum of all the component columns for a specific project

CREATE OR REPLACE FUNCTION public.get_component_sums(
    p_project_id INTEGER
)
RETURNS TABLE (
    length NUMERIC,
    elbows_90 NUMERIC,
    elbows_45 NUMERIC,
    bevels NUMERIC,
    tees NUMERIC,
    reducers NUMER<PERSON>,
    caps NUMERIC,
    flanges NUMERIC,
    valves_flanged NUMERIC,
    valves_welded NUMERIC,
    cut_outs NUMERIC,
    supports NUMERIC,
    bends NUMERIC,
    union_couplings NUMERIC,
    expansion_joints NUMERIC,
    field_welds NUMERIC,
    calculated_eq_length NUMERIC,
    calculated_area NUMERIC
)
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(b.length) AS length,
        SUM(b.elbows_90) AS elbows_90,
        SUM(b.elbows_45) AS elbows_45,
        SUM(b.bevels) AS bevels,
        SUM(b.tees) AS tees,
        SUM(b.reducers) AS reducers,
        SUM(b.caps) AS caps,
        SUM(b.flanges) AS flanges,
        SUM(b.valves_flanged) AS valves_flanged,
        SUM(b.valves_welded) AS valves_welded,
        SUM(b.cut_outs) AS cut_outs,
        SUM(b.supports) AS supports,
        SUM(b.bends) AS bends,
        SUM(b.union_couplings) AS union_couplings,
        SUM(b.expansion_joints) AS expansion_joints,
        SUM(b.field_welds) AS field_welds,
        SUM(b.calculated_eq_length) AS calculated_eq_length,
        SUM(b.calculated_area) AS calculated_area
    FROM public.bom b
    WHERE b.project_id = p_project_id;
END;
$$ LANGUAGE plpgsql;
