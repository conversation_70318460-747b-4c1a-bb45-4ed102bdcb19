from src.utils.logger import logger
from PySide6.QtWidgets import QLabel, QPushButton, QLineEdit
from PySide6.QtWidgets import QSizePolicy
from .baseform import BaseForm
from pubsub import pub

PROJECT_NAME_DEFAULT_MESSAGE = "Project Name *"
PROJECT_ID_DEFAULT_MESSAGE = "Project ID *"
PROJECT_LOCATION_DEFAULT_MESSAGE = "Project Location"
ENGINEER_DRAFTER_DEFAULT_MESSAGE = "Engineering/Drafter Company of Drawings"


class NewProjectSetupForm(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)

        self.filename: str = None
    
    def initUi(self):
        self.formSize.setHeight(480)

        self.title.setText("New Project Set Up")
        self.subtitle.clear()
        self.subtitle.hide()

        self.addVSpace()

        self.lblProjectName = QLabel(PROJECT_NAME_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectName)
        self.projectName = QLineEdit()
        self.layout().addRow(self.projectName)
    
        self.addVSpace()

        self.lblProjectId = QLabel(PROJECT_ID_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectId)
        self.projectId = QLineEdit()
        self.layout().addRow(self.projectId)
    
        self.addVSpace()

        self.lblProjectLocation = QLabel(PROJECT_LOCATION_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblProjectLocation)
        self.projectLocation = QLineEdit()
        self.layout().addRow(self.projectLocation)

        self.addVSpace()

        self.lblEngineerDrafter = QLabel(ENGINEER_DRAFTER_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblEngineerDrafter)
        self.engineerDrafter = QLineEdit()
        self.layout().addRow(self.engineerDrafter)

        self.addStretchSpacer()
        self.addErrorStatusWidget()
        pbNext = QPushButton("Next")
        pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        pbNext.setMinimumHeight(48)
        pbNext.clicked.connect(self.onNext)
        self.layout().addRow(pbNext)

        pbNext.setContentsMargins(0, 132, 0, 32)

        self.setFloatingButtonBack()

    def initDefaults(self):
        self.projectName.clear()
        self.projectId.clear()
        self.projectLocation.clear()
        self.engineerDrafter.clear()

    def onLinkActivated(self, event):
        if event == "LoginForm":
            self.sgnSwitchTo.emit(event)

    def onNext(self, event):
        """ Validate user input """
        ok = True
        projectName = self.projectName.text()
        if not projectName:
            self.lblProjectName.setText(PROJECT_NAME_DEFAULT_MESSAGE.replace("*", "required"))
            self.setWidgetError(self.lblProjectName)
            ok = False
        else:
            self.setWidgetDefault(self.lblProjectName)

        projectId = self.projectId.text()
        if not projectId:
            self.lblProjectId.setText(PROJECT_ID_DEFAULT_MESSAGE.replace("*", "required"))
            self.setWidgetError(self.lblProjectId)
            ok = False
        else:
            self.setWidgetDefault(self.lblProjectId)

        # projectLocation = self.projectLocation.text()
        # if not projectLocation:
        #     self.lblProjectLocation.setText(PROJECT_LOCATION_DEFAULT_MESSAGE.replace("*", "required"))
        #     self.setWidgetError(self.lblProjectLocation)
        #     ok = False
        # else:
        #     self.setWidgetDefault(self.lblProjectLocation)

        # engineerDrafter = self.engineerDrafter.text()
        # if not engineerDrafter:
        #     self.lblEngineerDrafter.setText(ENGINEER_DRAFTER_DEFAULT_MESSAGE.replace("*", "required"))
        #     self.setWidgetError(self.engineerDrafter)
        #     ok = False
        # else:
        self.setWidgetDefault(self.lblEngineerDrafter)

        # Details are valid? Move on to explorer form
        # self.sgnSwitchTo.emit("NewProjectExplorerForm")
        projectData = {}
        projectData.update(self.getData())
        projectData["document"] = self.filename
        print(projectData)
        pub.sendMessage("create-new-project", data=projectData)

    def getData(self) -> dict:
        return {
            "projectName": self.projectName.text(),
            "projectNumber": self.projectId.text(),
            "projectLocation": self.projectLocation.text(),
            "engineerDrafter": self.engineerDrafter.text(),
        }

    def onFilePicked(self, filename):
        """ See: Signal `sgnFilePicked` from NewProjectExplorerForm """
        self.filename = filename
        self.sgnSwitchTo.emit("NewProjectSetupForm")
    
    def onFloatingButton(self):
        pub.sendMessage("goto-form", name="NewProjectExplorerForm")