# Elevation Extraction Logic in Architekt ATOM

This document explains the elevation extraction functionality in the Architekt ATOM codebase, including how regex patterns are used to identify and process elevation data from PDF documents.

## Overview

The elevation extraction system is designed to:

1. Identify elevation values in text extracted from PDF documents using regex patterns
2. Parse these values into standardized formats
3. Calculate metrics (min, max, average) for elevations on each page
4. Store the results for further processing

## Key Files and Functions

### 1. Pattern Definition

The regex patterns for elevation detection are defined in two main locations:

#### `src/atom/sourcepreprocess.py`

This file contains the most comprehensive set of elevation patterns in the `VALUE_PATTERNS` dictionary:

```python
VALUE_PATTERNS = {
    "elevation": [
        # Pattern for "EL 100 '6"" format (with space before foot mark)
        "EL [-+]?\\d+ '\\d+\"",
        # Patterns with decimal fractions in specific formats
        # Pattern for "EL +184'4.3/16"" format (feet inches.fraction/denominator without space)
        "EL [-+]?\\d+'\\d+\\.\\d+/\\d+\"",
        # Pattern for "EL +72' 2.7/16"" format (feet space inches.fraction/denominator with quote)
        "EL [-+]?\\d+' \\d+\\.\\d+/\\d+\"",
        # Pattern for "EL +108' 6.9/16" format (feet space inches.fraction/denominator without quote)
        "EL [-+]?\\d+' \\d+\\.\\d+/\\d+",
        # Patterns with hyphens and spaces
        # Pattern for "EL +65'-3 3/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",
        # Pattern for "EL +43'-10 1/8"" format (feet-inches-fraction)
        "EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\"",
        # Pattern for "EL +43'-10"" format (feet-inches)
        "EL [-+]?\\d+'[-]\\d+\"",
        # Patterns without hyphens
        # Pattern for "EL +185'3"" format (feet inches without space)
        "EL [-+]?\\d+'\\d+\"",
        # Feet-only patterns
        # Pattern for "EL +28'" format (feet only)
        "EL [-+]?\\d+'",
        # Decimal and integer formats
        "EL [-+]?\\d+\\.\\d+",  # EL +43.5
        "EL [-+]?\\d+",  # EL +43
        # Legacy patterns (keeping for compatibility)
        "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "Z [-+]?\\d+",
        "EL [-+]?\\d+'-\\d+( \\d+/\\d+)?\"",
        "EL [-+]?[0-9]+\\.?[0-9]*"
    ],
    # Other pattern types omitted for brevity
}
```

#### `src/atom/_worker.py`

This file contains a simplified version of the patterns in the `run` function:

```python
value_patterns = {
    "elevation": [
        "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "Z [-+]?\\d+",
        "EL [-+]?\\d+'-\\d+( \\d+/\\d+)?\"",
        "EL [-+]?[0-9]+\\.?[0-9]*"
    ],
    # Other pattern types omitted for brevity
}
```

### 2. Pattern Usage

#### `update_value_type` Function

This function is used to match text against the regex patterns and categorize the text:

```python
# In src/atom/sourcepreprocess.py
def update_value_type(value, patterns=VALUE_PATTERNS):
    for category, category_patterns in patterns.items():
        for pattern in category_patterns:
            if category == 'elevation':
                match = re.search(pattern, value)
                if match:
                    return category, match.group(0)
            else:
                if re.match(pattern, value):
                    return category, value
    return '', ''
```

Note that for elevations specifically, it uses `re.search` instead of `re.match` to find the pattern anywhere in the text, and returns the matched portion.

### 3. Elevation Parsing

#### `parse_elevation` Function

This function in `src/atom/_worker.py` and `src/atom/roiextraction.py` parses the extracted elevation strings into numerical values:

```python
def parse_elevation(elevation_str, pdf_id, convert_mm_to_feet=False, convert_m_to_feet=True):
    # Check if the string contains feet (') or inches (") symbols
    if "'" in elevation_str or '"' in elevation_str:
        # Handle standard format elevations
        # Check if the string does not start with "E" or "EL" (case-insensitive)
        if not re.match(r'^[EeZz][Ll]?', elevation_str):
            return float('nan')  # Or any other value indicating non-processing

        try:
            # Remove 'E' or 'EL' prefix, any trailing double quotes, and the sign
            elevation_str = re.sub(r'[EeZz][Ll]?|\s*\"', '', elevation_str).strip()
            sign = -1 if elevation_str.startswith('-') else 1
            elevation_str = elevation_str.lstrip('+-')

            # Parse feet, inches, and fractions
            # ... (parsing logic)

            # Calculate total inches and convert to decimal feet
            total_inches = feet * 12 + inches + fraction
            decimal_feet = sign * total_inches / 12
            return decimal_feet
        except Exception as e:
            logger.warning(f"Error parsing elevation: PDF ID: {pdf_id} {elevation_str} - {e}", exc_info=True)
            return float('nan')  # Return NaN if there's an error
    else:
        # Handle metric format
        try:
            # Remove 'E', 'EL', or 'Z' prefix and any trailing whitespace
            elevation_str = re.sub(r'[EeZz][Ll]?', '', elevation_str).strip()

            # Convert to appropriate units
            value = float(elevation_str)

            if convert_mm_to_feet:
                decimal_feet = value / 304.8
                return decimal_feet
            elif convert_m_to_feet:
                decimal_feet = value * 0.328084
                return decimal_feet
            else:
                return value
        except Exception as e:
            logger.error(f"Error parsing elevation (metric): PDF ID: {pdf_id} {elevation_str} - {e}", exc_info=True)
            return float('nan')  # Return NaN if there's an error
```

### 4. Elevation Metrics Calculation

#### `calculate_elevation_metrics` Function

This function calculates minimum, maximum, and average elevations for a given set of elevation values:

```python
def calculate_elevation_metrics(row, pdf_id=0, convert_mm_to_feet=False, convert_m_to_feet=False):
    try:
        elevation_values = row['elevation']
    except Exception as e:
        logger.warning(f"'elevation' field not found in table {e}", exc_info=True)
        return 0, 0, 0
    try:
        elevation_strings = elevation_values.split(';')
        elevations = [parse_elevation(el.strip(), pdf_id, convert_mm_to_feet=convert_mm_to_feet, convert_m_to_feet=convert_m_to_feet) for el in elevation_strings if el.strip()]

        # Calculate min, max, and average elevations in decimal feet
        min_elevation = f"{min(elevations):.2f}" if elevations else ''
        max_elevation = f"{max(elevations):.2f}" if elevations else ''
        avg_elevation = f"{sum(elevations) / len(elevations):.2f}" if elevations else ''

        return min_elevation, max_elevation, avg_elevation
    except Exception as e:
        logger.error(f"Error in calculate_elevation_metrics. Elevation values: {elevation_values} -- {e}", exc_info=True)
        return 0, 0, 0
```

## Workflow

### Elevation Extraction Process

The elevation extraction process follows these steps:

1. **Text Extraction**: Text is extracted from PDF documents using PyMuPDF.

2. **Pattern Matching**: The extracted text is matched against the regex patterns in `value_patterns["elevation"]` using the `update_value_type` function.

3. **Data Collection**: When a match is found, the matched text is stored in the `elevation` field of the raw data.

4. **Parsing**: The `parse_elevation` function converts the elevation strings to numerical values, handling different formats (imperial with feet/inches or metric).

5. **Metrics Calculation**: The `calculate_elevation_metrics` function processes all elevations for a page to calculate minimum, maximum, and average values.

6. **Storage**: The results are stored in the DataFrame for further processing or database storage.

### Integration with Main Workflow

The elevation extraction is part of a larger document processing system:

1. **Document Loading**: PDF documents are loaded using PyMuPDF.

2. **Page Processing**: Each page is processed individually or in batches using multiprocessing.

3. **Text and Annotation Extraction**: Both text and annotations are extracted from the pages.

4. **Pattern Recognition**: The `update_value_type` function is called to identify various data types, including elevations.

5. **Data Consolidation**: The extracted data is consolidated by page and category.

6. **Metrics Calculation**: For elevations specifically, the `calculate_elevation_metrics` function is called to compute min/max/avg values.

7. **Table Processing**: The data is organized into tables for further analysis.

8. **Results Storage**: The processed data is stored in DataFrames and can be exported to Excel or saved to a database.

### Code Flow

The main code flow for elevation extraction is:

```
worker() function in _worker.py
  ↓
process_pdf_pages()
  ↓
process_text_rois() / process_annot_rois()
  ↓
update_value_type() - Identifies elevations using regex patterns
  ↓
consolidate_ranges() - Combines data from the same page
  ↓
calculate_elevation_metrics() - Computes metrics for identified elevations
  ↓
parse_elevation() - Converts elevation strings to numerical values
```

This process is typically triggered as part of the `run_extraction()` function, which orchestrates the entire document processing workflow.

## Plugin Integration

Several plugins use the elevation extraction functionality:

1. **plugin_recalculate_elevations**: Recalculates elevation metrics for existing data.
2. **plugin_elevations_check_valid**: Validates and combines unique elevations by page.
3. **plugin_unique_elevations**: Extracts unique elevation values from raw data.
4. **plugin_detect_elevations_aff**: Detects elevations with specific formatting (A.F.F).

## Extending the Functionality

To extend the elevation extraction functionality:

1. Add new regex patterns to the `VALUE_PATTERNS["elevation"]` list in `src/atom/sourcepreprocess.py`.
2. Update the `parse_elevation` function if new formats need special parsing logic.
3. Create new plugins for specific elevation-related tasks.

### Adding New Patterns

When adding new patterns, consider:

1. **Prefix Variations**: Different documents may use different prefixes for elevations (EL, ELEV, Z, etc.)
2. **Format Variations**: Imperial vs metric, different fraction formats, etc.
3. **Special Characters**: Handling of spaces, hyphens, plus/minus signs, etc.

#### Example: Adding a New Pattern

To add support for elevations with the format "ELEV. +100'-6":

```python
# In src/atom/sourcepreprocess.py
VALUE_PATTERNS = {
    "elevation": [
        # Existing patterns...

        # New pattern for "ELEV. +100'-6" format
        "ELEV\\.? [-+]?\\d+'[-]\\d+\"?",

        # Other patterns...
    ],
    # Other categories...
}
```

### Updating the Parsing Logic

If the new pattern requires special parsing logic, update the `parse_elevation` function:

```python
def parse_elevation(elevation_str, pdf_id, convert_mm_to_feet=False, convert_m_to_feet=True):
    # Existing code...

    # Add handling for new format
    if re.match(r'^ELEV\.?\s*', elevation_str, re.IGNORECASE):
        # Special handling for ELEV prefix
        elevation_str = re.sub(r'^ELEV\.?\s*', '', elevation_str, flags=re.IGNORECASE)
        # Continue with existing parsing logic...

    # Rest of the function...
```

### Creating a New Plugin

To create a specialized plugin for a specific elevation-related task:

```python
def plugin_detect_custom_elevations(input_file, output_file="debug/custom_elevations.xlsx"):
    """
    Detects and processes elevations with custom formatting
    """
    df = pd.read_excel(input_file)

    # Process elevations with custom logic
    results = []
    for _, row in df.iterrows():
        if pd.notna(row["elevation"]):
            # Custom processing logic
            processed_elevation = custom_process_elevation(row["elevation"])
            results.append({
                "pdf_page": row["pdf_page"],
                "original_elevation": row["elevation"],
                "processed_elevation": processed_elevation
            })

    # Save results
    result_df = pd.DataFrame(results)
    result_df.to_excel(output_file, index=False)
    return f"Processed {len(results)} elevations, saved to {output_file}"
```

## Example Pattern Explanation

- `"EL [-+]?\\d+'\\d+\""`: Matches elevations like "EL +185'3"" (feet and inches without space)
- `"EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\""`: Matches elevations like "EL +65'-3 3/8"" (feet-inches-fraction)
- `"EL [-+]?\\d+\\.\\d+"`: Matches decimal elevations like "EL +43.5"
- `"Z [-+]?\\d+"`: Matches Z-coordinate elevations like "Z +100"

### Regex Pattern Breakdown

Let's break down one of the more complex patterns:

```
"EL [-+]?\\d+'[-]\\d+ \\d+/\\d+\""
```

- `EL `: Matches the literal "EL " prefix
- `[-+]?`: Matches an optional plus or minus sign
- `\\d+`: Matches one or more digits (the feet value)
- `'`: Matches the foot mark
- `[-]`: Matches a hyphen
- `\\d+`: Matches one or more digits (the inches value)
- ` `: Matches a space
- `\\d+/\\d+`: Matches a fraction (numerator/denominator)
- `\"`: Matches the inch mark

This pattern would match strings like "EL +65'-3 3/8"" or "EL -10'-6 1/2"".

## Best Practices and Common Issues

### Best Practices

1. **Pattern Ordering**: Place more specific patterns before more general ones to ensure the most precise match is found first.

2. **Pattern Testing**: Test new regex patterns with a variety of real-world examples before adding them to the production code.

3. **Error Handling**: Always include robust error handling in parsing functions to prevent failures when encountering unexpected formats.

4. **Documentation**: Document the purpose and expected format for each regex pattern to make maintenance easier.

5. **Modular Design**: Keep the pattern definitions separate from the parsing logic to allow for easier updates.

### Common Issues

1. **Overlapping Patterns**: Multiple patterns matching the same text can lead to duplicate or inconsistent results.

2. **Missing Formats**: Some elevation formats may not be covered by existing patterns, requiring regular updates.

3. **False Positives**: Text that resembles elevation formats but isn't actually an elevation may be incorrectly matched.

4. **Parsing Errors**: Complex formats may cause parsing errors if the `parse_elevation` function doesn't handle them correctly.

5. **Performance Concerns**: Large documents with many elevation values may experience performance issues due to regex processing.

### Troubleshooting

If you encounter issues with elevation extraction:

1. **Check the Raw Data**: Examine the raw text extracted from the PDF to ensure it contains the expected elevation values.

2. **Test Individual Patterns**: Test each regex pattern separately against problematic text to identify which pattern is failing.

3. **Add Logging**: Add detailed logging to the `update_value_type` and `parse_elevation` functions to track the matching and parsing process.

4. **Validate Results**: Compare the extracted elevations with the original document to verify accuracy.

5. **Update Patterns**: If necessary, update the regex patterns to handle new or problematic formats.
