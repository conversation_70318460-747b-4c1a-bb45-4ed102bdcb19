"""
Standalone Edit Roi Dialog

P:1. Filter columns depending on the database (General, BOM, SPEC, etc)

Ensures selected values match the database field options.
If column is added for that table, remove it. If ROI is deleted, add it back to the options

"""

from src.views.blueprintreaderview import EditRoiDialog, RoiItem
from PySide6.QtWidgets import QGraphicsScene, QGraphicsView
from PySide6.QtCore import QPoint, Signal


class TestView(QGraphicsView):

    def __init__(self):
        super().__init__()
        self.dpi = 300


class TestScene(QGraphicsScene):

    sgnRemoveItem = Signal(object)
    def __init__(self):
        super().__init__()
        self.dpi = 300



if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    viewport = TestView()
    scene = TestScene()
    viewport.setScene(scene)
    roi = RoiItem(QPoint(0,0), QPoint(10,10), scene)
    roi.name = "BOM"
    roi.setTableMode(True)
    roi.addColumn("pos")
    roi.addColumn("quantity")
    dlg = EditRoiDialog(None, roi)
    dlg.exec()
    app.exec()