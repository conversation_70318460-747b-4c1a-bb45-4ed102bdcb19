FUNCTION_CREATE_COPY_OF_COMPONENT_MAPPING = """
    -- Example usage SYNTAX: (source_profile_id, new_profile_id):
    -- SELECT copy_bom_component_mappings(1, 4);

    CREATE OR REPLACE FUNCTION copy_bom_component_mappings(
    source_profile_id INTEGER,
    target_profile_id INTEGER
    ) RETURNS INTEGER AS $$
    DECLARE
        copied_count INTEGER;
    BEGIN
        -- Insert copies of rows with the new profile_id
        INSERT INTO public.atem_bom_component_mapping (
            profile_id,
            class_description,
            component_name,
            takeoff_category,
            general_category,
            created_at,
            updated_at,
            map_to_gen
        )
        SELECT
            target_profile_id, -- Use the target profile_id instead of the source
            class_description,
            component_name,
            takeoff_category,
            general_category,
            NOW(), -- Current timestamp for created_at
            NOW(), -- Current timestamp for updated_at
            map_to_gen
        FROM
            public.atem_bom_component_mapping
        WHERE
            profile_id = source_profile_id;
            
        -- Get the count of copied rows
        GET DIAGNOSTICS copied_count = ROW_COUNT;
        
        -- Return the number of rows copied
        RETURN copied_count;
    END;
    $$ LANGUAGE plpgsql;

    
    """


FUNCTION_CREATE_COPY_OF_REDUCER_DATA= """
    CREATE OR REPLACE FUNCTION copy_reducers_data(
    source_client_id INTEGER,
    target_client_id INTEGER,
    target_client_name TEXT
    ) RETURNS INTEGER AS $$
    DECLARE
        copied_count INTEGER;
    BEGIN
        -- Insert copies of rows with the new client_id and client name
        INSERT INTO public.reducers (
            client_id,
            client,
            profile,
            nps,
            size1,
            size2,
            large_end_mm,
            large_end_in,
            large_end_ft,
            small_end_mm,
            small_end_in,
            small_end_ft,
            length_mm,
            length_in,
            length_ft,
            area_mm,
            area_in,
            area_ft,
            created_at,
            updated_at
        )
        SELECT
            target_client_id,
            target_client_name,
            profile,
            nps,
            size1,
            size2,
            large_end_mm,
            large_end_in,
            large_end_ft,
            small_end_mm,
            small_end_in,
            small_end_ft,
            length_mm,
            length_in,
            length_ft,
            area_mm,
            area_in,
            area_ft,
            NOW(), -- Current timestamp for created_at
            NOW()  -- Current timestamp for updated_at
        FROM
            public.reducers
        WHERE
            client_id = source_client_id;
            
        -- Get the count of copied rows
        GET DIAGNOSTICS copied_count = ROW_COUNT;
        
        -- Return the number of rows copied
        RETURN copied_count;
    END;
    $$ LANGUAGE plpgsql;

    -- Example usage:
    SELECT copy_reducers_data(1, 5, 'Excel USA');
    """