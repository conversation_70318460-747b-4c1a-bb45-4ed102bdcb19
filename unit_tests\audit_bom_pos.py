import pandas as pd
import numpy as np

def audit_pos_column(file_path):
    """
    Audit the 'pos' column in a processed Excel file to check for non-consecutive numbering.
    
    Parameters:
    file_path (str): Path to the Excel file
    
    Returns:
    list: List of page numbers with non-consecutive 'pos' values
    """
    # Read the Excel file
    df = pd.read_excel(file_path)
    
    # Pages with issues
    problem_pages = []
    
    # Process each page separately
    for page in df['pdf_page'].unique():
        page_df = df[df['pdf_page'] == page].copy()
        
        # Filter out rows where pos is NaN or empty string
        page_df = page_df[page_df['pos'].notna() & (page_df['pos'] != '')]
        
        # Ensure pos is numeric (convert if needed)
        page_df['pos'] = pd.to_numeric(page_df['pos'], errors='coerce')
        page_df = page_df.dropna(subset=['pos'])
        
        if len(page_df) <= 1:
            # Not enough items to check for gaps
            continue
        
        # Sort by pos
        page_df = page_df.sort_values('pos')
        
        # Get the list of pos values
        pos_values = page_df['pos'].tolist()
        
        # Check for gaps
        has_gap = False
        gaps = []
        
        for i in range(1, len(pos_values)):
            # Check if there's a gap (difference > 1)
            if pos_values[i] - pos_values[i-1] > 1:
                has_gap = True
                missing_values = list(range(int(pos_values[i-1]) + 1, int(pos_values[i])))
                gaps.append(f"{int(pos_values[i-1])} to {int(pos_values[i])} (missing: {missing_values})")
        
        # If we found gaps, add this page to the problem list
        if has_gap:
            problem_pages.append({'page': page, 'gaps': gaps})
    
    # Print the results
    if problem_pages:
        print("Pages with non-consecutive 'pos' values:")
        for problem in problem_pages:
            print(f"\nPage {problem['page']}:")
            for gap in problem['gaps']:
                print(f"  Gap from {gap}")
    else:
        print("No pages with non-consecutive 'pos' values found.")
    
    # Return list of problematic page numbers for further processing if needed
    return [problem['page'] for problem in problem_pages]

# Example usage
if __name__ == "__main__":
    file_path = "processed_categories.xlsx"
    problematic_pages = audit_pos_column(file_path)
