from src.utils.logger import logger
from src.views.tableresultsview import TableResultsViewBase
from src.atom.dbManager import DatabaseManager

# logger = logging.getLogger(__file__)


class SpecResultsView(TableResultsViewBase):

    def __init__(self, parent) -> None:
        super().__init__(parent)
        logger.info("Creating SPEC Table...")
    
    def  __repr__(self) -> str:
        return "SPEC"

    def initToolbar(self):
        super().initToolbar()

    def onToolbarBtn(self, name):
        # Example logging or print statement for debugging
        print(f"Toolbar button clicked: {name}")
        
        if name == "tree": # Tree hieratchy view (QTreeWidget or similar)
            pass
        elif name == "save": # Push to database
            df = self.getTableData()  # Use your existing method
            print("\n\nSPEC DF ROWS FOR DB COMMIT: \n", len(df))
            db_manager = DatabaseManager()
            db_manager.insert_dataframe(df, "SPEC")
        else:
            super().onToolbarBtn(name)
