
def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):
    if not isinstance(roi_payload, list):
        print(f"Warning: roi_payload is not a list. Type: {type(roi_payload)}, Value: {roi_payload}")
        return []

    converted_payload = []
    for item in roi_payload:
        if not isinstance(item, dict):
            print(f"Warning: item is not a dict. Type: {type(item)}, Value: {item}")
            continue

        try:
            # print(f"\nDEBUG - Converting ROI item:")
            # print(f"Original item: {item}")
            converted_item = {
                "name": item["name"],
                "isTable": item.get("isTable", False),
                "x0": int(item["relativeX0"] * width + x_offset),
                "y0": int(item["relativeY0"] * height + y_offset),
                "x1": int(item["relativeX1"] * width + x_offset),
                "y1": int(item["relativeY1"] * height + y_offset)
            }

            # Set table type based on name for tables
            converted_item = {
                "name": item["name"],
                "isTable": item.get("isTable", False),
                "x0": int(item["relativeX0"] * width + x_offset),
                "y0": int(item["relativeY0"] * height + y_offset),
                "x1": int(item["relativeX1"] * width + x_offset),
                "y1": int(item["relativeY1"] * height + y_offset)
            }

            # Determine table type based on name
            if converted_item["isTable"]:
                name_upper = converted_item["name"].upper()
                if name_upper == "SPEC":
                    converted_item["tableType"] = "SPEC"
                elif name_upper == "SPOOL":
                    converted_item["tableType"] = "Spool"
                else:
                    converted_item["tableType"] = "BOM"
                # print(f"Setting table type for {name_upper} to: {converted_item['tableType']}")

            # print(f"Converted item: {converted_item}")
            converted_payload.append(converted_item)
        except Exception as e:
            print(f"Error processing item: {item}")
            print(f"Error: {str(e)}")
            continue

    return converted_payload

def multi_convert_relative_coords_to_points(json_data, page_groups_df):
    """Convert all group ROIs to absolute coordinates"""
    if not isinstance(json_data, dict):
        print(f"Warning: json_data is not a dict. Type: {type(json_data)}")
        return {}

    converted_payload = {}

    try:
        for group_number, group_data in json_data['groups'].items():
            # print(f"Processing group {group_number}")
            # print(f"Group data: {group_data}")

            # Get ROIs from group data
            rois = group_data.get('rois', [])
            if not rois:
                print(f"No ROIs found for group {group_number}")
                continue

            # Get dimensions from page_groups_df
            group_df_rows = page_groups_df[page_groups_df['group_number'] == int(group_number)]
            if group_df_rows.empty:
                print(f"No dimension data found for group {group_number}")
                continue

            group_df_data = group_df_rows.iloc[0]
            width, height = group_df_data['width'], group_df_data['height']

            converted_group_payload = convert_relative_coords_to_points(rois, width, height)
            converted_payload[group_number] = converted_group_payload

    except Exception as e:
        print(f"Error in multi_convert: {str(e)}")
        return {}

    return converted_payload

