"""
Allow user to select PDF from file explorer for new projectg
"""
from PySide6.QtGui import QHideEvent, QIcon
from PySide6.QtWidgets import QSizePolicy, QWidget, QVBoxLayout
from PySide6.QtCore import Signal, QUrl
from PySide6.QtWebEngineWidgets import QWebEngineView
from src.pyside_util import get_resource_pixmap

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 960

class TokenCheckoutWindow(QWidget):

    sgnFilePicked = Signal(str)
    sgnFilePickedExisting = Signal(str)  # Add to existing project

    def __init__(self, parent, url):
        super().__init__(parent)

        self.setLayout(QVBoxLayout())
        self.view = QWebEngineView(self)
        self.view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.view.setUrl(QUrl(url))
        self.layout().addWidget(self.view)
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.setWindowIcon(QIcon(get_resource_pixmap("FFF_Architekt Integrated Systems_LOout.png")))
        self.setMinimumSize(1280, 768)

    def initDefaults(self):
        return

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)
