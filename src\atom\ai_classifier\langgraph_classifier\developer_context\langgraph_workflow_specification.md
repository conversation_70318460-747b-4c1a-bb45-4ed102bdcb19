# LangGraph-Based BOM Classification Workflow Specification

## 📋 Executive Summary

This document specifies the transition from the current monolithic BOM audit system to a specialized, multi-stage LangGraph classification workflow. The new architecture addresses accuracy limitations through focused, category-specific processing while maintaining compatibility with existing infrastructure.

## 🎯 Design Objectives

### Primary Goals
1. **Eliminate Monolithic Prompt Overload**: Replace single 15+ field prompts with focused, sequential processing
2. **Category-Specific Intelligence**: Implement specialized rules and abbreviation handling per component type
3. **Improved Accuracy**: Achieve 95%+ classification accuracy through targeted workflows
4. **Maintain Compatibility**: Preserve existing async processing and correction application systems

### Success Metrics
- **Accuracy Improvement**: 15-20% increase in classification precision
- **Processing Efficiency**: 30% reduction in token usage through focused prompts
- **Maintainability**: Easy addition of new category-specific rules without code changes
- **Performance**: Maintain <200ms average response time per item

## 🏗️ System Architecture Overview

### Current vs. Proposed Architecture

**Current Monolithic Approach:**
```
Material Description → Single Comprehensive Prompt → All 15+ Fields → Result
```

**Proposed LangGraph Workflow:**
```
Material Description → Stage 1: Analysis → Stage 2: Sequential Classification → Stage 3: Q&A Decisions → Stage 4: Self-Audit → Result
```

### Core Components Integration

**Leveraged Existing Infrastructure:**
- `categorization_table` from `prompts.py` - Field definitions and validation rules
- `CrossFieldValidationRules` - ASTM→Material mappings and consistency checks
- `LangchainModelHandler` - Gemini model configuration and API handling
- `apply_audit_corrections` - Programmatic correction workflow
- Async processing patterns with semaphore control

**New LangGraph Components:**
- `ClassificationState` - Workflow state management
- `CategorySpecificNodes` - Specialized processing nodes per component type
- `SequentialFieldProcessor` - Dependency-aware field classification
- `AbbreviationResolver` - Category-specific terminology handling
- `ComponentLookupTable` - Enhanced fitting/component knowledge base

## 📊 Multi-Stage Classification Workflow

### Stage 1: Initial Material Description Analysis

**Purpose**: Extract key attributes and determine primary processing path

**Node**: `material_analysis_node`

**Process**:
1. **Property Extraction**: Identify explicit attributes (ASTM, size, material type, etc.)
2. **Category Determination**: Primary classification (Pipe, Fitting, Valve, Flange, etc.)
3. **Complexity Assessment**: Determine if standard or complex processing required
4. **Context Building**: Create focused context for subsequent stages

**Prompt Strategy**:
```python
# Focused extraction prompt - ~200 tokens vs current ~2000+ tokens
ANALYSIS_PROMPT = """
Extract key properties from this piping component description:
Description: "{material_description}"

Identify:
1. Component type (pipe, fitting, valve, flange, gasket, bolt, support, misc)
2. Explicit ASTM standard (if mentioned)
3. Size information (if mentioned)
4. Material indicators (steel, stainless, carbon, etc.)
5. Special characteristics (seamless, forged, threaded, etc.)

Response format: JSON with extracted properties
"""
```

### Stage 2: Sequential Field Classification

**Purpose**: Process fields in logical dependency order with category-specific intelligence

**Nodes**: Category-specific processing nodes
- `pipe_classification_node`
- `fitting_classification_node`
- `valve_classification_node`
- `flange_classification_node`
- `gasket_classification_node`
- `bolt_classification_node`
- `support_classification_node`
- `miscellaneous_classification_node`

**Processing Order**:
1. **Primary Classification**: `rfq_scope` determination
2. **Material Foundation**: `astm` → `material` → `grade` (with cross-validation)
3. **Category-Specific Fields**: Based on rfq_scope result
4. **Physical Properties**: `size1`, `size2`, `schedule`, `rating`
5. **Manufacturing Details**: `forging`, `ends`, `coating`
6. **Standards Compliance**: `ansme_ansi`

**Category-Specific Intelligence Examples**:

**Fitting Classification Rules**:
```python
FITTING_INTELLIGENCE = {
    "terminology_mapping": {
        "Pipe Nipple": "fitting_category: Nipple",
        "NPL": "fitting_category: Nipple", 
        "Pipe Bend": "fitting_category: Pipe Bend",
        "SO": "Socket Weld in flange context",
        "WN": "Weld Neck in flange context",
        "THRD": "Threaded connection"
    },
    "classification_rules": [
        "If description contains 'Nipple' or 'NPL' → always classify as Fitting, not Pipe",
        "If description contains 'Bend' → classify as Fitting (Pipe Bend), not Pipe",
        "Elbow variants: 45°, 90°, LR (Long Radius), SR (Short Radius)"
    ]
}
```

**Valve Classification Rules**:
```python
VALVE_INTELLIGENCE = {
    "terminology_mapping": {
        "BV": "Ball Valve",
        "GV": "Gate Valve", 
        "GLV": "Globe Valve",
        "CV": "Check Valve",
        "BFV": "Butterfly Valve"
    },
    "classification_rules": [
        "Ball Valve indicators: 'Ball', 'BV', 'Full Port', 'Reduced Port'",
        "Gate Valve indicators: 'Gate', 'GV', 'Rising Stem', 'Non-Rising'",
        "Check Valve indicators: 'Check', 'CV', 'Swing', 'Lift', 'Wafer'"
    ]
}
```

### Stage 3: Q&A-Style Decision Making

**Purpose**: Handle complex classifications requiring contextual reasoning

**Node**: `qa_decision_node`

**Process**:
1. **Ambiguity Detection**: Identify unclear or conflicting information
2. **Contextual Questioning**: Ask specific clarifying questions
3. **Decision Logic**: Apply business rules for edge cases
4. **Confidence Scoring**: Assign confidence levels to decisions

**Q&A Decision Examples**:
```python
QA_DECISIONS = [
    {
        "condition": "Contains 'VENDOR' + alphanumeric code",
        "question": "Is this a vendor-specific part number with no material information?",
        "decision_logic": "If yes → rfq_scope: Miscellaneous",
        "confidence": 0.9
    },
    {
        "condition": "ASTM A106 + Material: Stainless",
        "question": "ASTM A106 is carbon steel standard. Correct material classification?",
        "decision_logic": "Force material: Steel, Carbon",
        "confidence": 0.95
    },
    {
        "condition": "Description contains both 'Pipe' and 'Nipple'",
        "question": "Is this a pipe nipple (short pipe fitting) or standard pipe?",
        "decision_logic": "If length < 12 inches or 'Nipple' explicit → Fitting",
        "confidence": 0.8
    }
]
```

### Stage 4: Self-Audit Review Process

**Purpose**: Validate classifications against business rules and cross-field consistency

**Node**: `self_audit_node`

**Process**:
1. **Cross-Field Validation**: Apply existing `CrossFieldValidationRules`
2. **Consistency Checking**: Verify logical field relationships
3. **Confidence Assessment**: Evaluate overall classification confidence
4. **Issue Identification**: Flag potential problems for human review

**Validation Rules Integration**:
```python
# Leverage existing validation rules from audit_main.py
CROSS_FIELD_VALIDATION = [
    {
        "rule": "ASTM A106 Carbon Steel Rule",
        "condition": {"field": "astm", "values": ["A106"]},
        "requirement": {"field": "material", "values": ["Steel, Carbon"]},
        "confidence_impact": 0.95
    },
    {
        "rule": "Pipe Category Consistency Rule", 
        "condition": {"field": "rfq_scope", "values": ["Pipe"]},
        "requirement": {"field": "pipe_category", "not_null": True},
        "confidence_impact": 0.8
    }
]
```

## 🗃️ Enhanced Data Structure Design

### Component Lookup Table

**Purpose**: Comprehensive knowledge base for accurate classification

**Structure**:
```python
COMPONENT_LOOKUP_TABLE = {
    "fittings": {
        "nipples": {
            "aliases": ["Pipe Nipple", "NPL", "Hex Nipple", "Swage Nipple"],
            "full_name": "Pipe Nipple",
            "category": "Fittings",
            "fitting_category": "Nipple",
            "description": "Short length of pipe with male threads on both ends",
            "typical_sizes": ["0.5", "0.75", "1", "1.25", "1.5", "2", "3", "4"],
            "common_materials": ["Steel, Carbon", "Steel, Stainless"],
            "typical_astm": ["A106", "A312", "A53"]
        },
        "elbows": {
            "aliases": ["90 Elbow", "45 Elbow", "LR Elbow", "SR Elbow"],
            "full_name": "Pipe Elbow",
            "category": "Fittings", 
            "fitting_category": "90 LR Elbow",  # Default, refined by description
            "description": "Pipe fitting that changes flow direction",
            "variants": {
                "90_lr": "90 LR Elbow",
                "90_sr": "90 SR Elbow", 
                "45_lr": "45 Elbow",
                "45_sr": "45 SR Elbow"
            }
        }
    },
    "valves": {
        "ball_valves": {
            "aliases": ["Ball Valve", "BV", "Ball", "Full Port Ball", "Reduced Port Ball"],
            "full_name": "Ball Valve",
            "category": "Valves",
            "valve_type": "Ball Valve",
            "description": "Quarter-turn valve with spherical closure element"
        }
    }
}
```

### Abbreviation Dictionaries

**Category-Specific Terminology**:
```python
ABBREVIATION_DICTIONARIES = {
    "flanges": {
        "SO": "Slip On",
        "WN": "Weld Neck", 
        "SW": "Socket Weld",
        "THRD": "Threaded",
        "BL": "Blind",
        "LJ": "Lap Joint",
        "RF": "Raised Face",
        "FF": "Flat Face",
        "RTJ": "Ring Type Joint"
    },
    "materials": {
        "CS": "Steel, Carbon",
        "SS": "Steel, Stainless", 
        "AS": "Alloy Steel",
        "CI": "Cast Iron",
        "DI": "Iron, Ductile"
    },
    "manufacturing": {
        "SMLS": "Seamless",
        "ERW": "Electric Resistance Welded",
        "EFW": "Electric Fusion Welded",
        "SAW": "Submerged Arc Welded"
    }
}
```

### PostgreSQL Integration Design

**Equivalent/Surface Area Approach**:
```python
POSTGRESQL_INTEGRATION = {
    "lookup_tables": {
        "component_equivalents": {
            "table": "public.component_equivalents",
            "columns": ["component_name", "equivalent_names", "category", "subcategory"],
            "purpose": "Map component variations to standard classifications"
        },
        "surface_area_calculations": {
            "table": "public.surface_area_lookup", 
            "columns": ["component_type", "size1", "size2", "surface_area_sqft"],
            "purpose": "Support existing surface area estimation workflows"
        }
    },
    "integration_points": [
        "Pre-classification lookup for known components",
        "Post-classification validation against known patterns",
        "Surface area calculation for cost estimation"
    ]
}
```

## 🔄 LangGraph State Management

### Classification State Definition

```python
from typing import Annotated, Dict, List, Optional, Any
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

class ClassificationState(TypedDict):
    """State for BOM classification workflow"""

    # Input data
    item_id: str
    material_description: str
    original_classification: Dict[str, Any]

    # Processing state
    messages: Annotated[List[BaseMessage], add_messages]
    current_stage: str  # "analysis", "classification", "qa_decisions", "self_audit"
    processing_path: str  # "pipe", "fitting", "valve", etc.

    # Extracted information
    extracted_properties: Dict[str, Any]
    category_intelligence: Dict[str, Any]

    # Classification results
    field_classifications: Dict[str, Any]
    confidence_scores: Dict[str, float]
    validation_results: Dict[str, Any]

    # Issues and corrections
    identified_issues: List[Dict[str, Any]]
    suggested_corrections: Dict[str, Any]

    # Metadata
    processing_time: float
    model_calls: int
    tokens_used: int
    workflow_path: List[str]
```

### Node Definitions

**Material Analysis Node**:
```python
async def material_analysis_node(state: ClassificationState) -> ClassificationState:
    """Stage 1: Extract key properties and determine processing path"""

    # Build focused analysis prompt
    analysis_prompt = build_analysis_prompt(state["material_description"])

    # Call model with structured output
    response = await model_handler.call_model_structured(
        prompt=analysis_prompt,
        response_schema=MaterialAnalysisResponse
    )

    # Update state
    return {
        **state,
        "extracted_properties": response.properties,
        "processing_path": response.primary_category,
        "current_stage": "classification",
        "workflow_path": state["workflow_path"] + ["material_analysis"]
    }
```

**Category-Specific Classification Node**:
```python
async def fitting_classification_node(state: ClassificationState) -> ClassificationState:
    """Stage 2: Fitting-specific classification logic"""

    # Load fitting-specific intelligence
    fitting_rules = FITTING_INTELLIGENCE
    abbreviations = ABBREVIATION_DICTIONARIES["fittings"]

    # Build category-specific prompt
    classification_prompt = build_fitting_prompt(
        description=state["material_description"],
        extracted_properties=state["extracted_properties"],
        rules=fitting_rules,
        abbreviations=abbreviations
    )

    # Process fields in dependency order
    field_results = {}
    for field_group in FITTING_FIELD_ORDER:
        field_response = await process_field_group(
            fields=field_group,
            prompt=classification_prompt,
            context=state
        )
        field_results.update(field_response)

    return {
        **state,
        "field_classifications": field_results,
        "current_stage": "qa_decisions",
        "workflow_path": state["workflow_path"] + ["fitting_classification"]
    }
```

### Workflow Graph Definition

```python
from langgraph.graph import StateGraph, END

def create_classification_workflow(config: AuditConfig) -> StateGraph:
    """Create the LangGraph classification workflow"""

    # Initialize workflow
    workflow = StateGraph(ClassificationState)

    # Add nodes
    workflow.add_node("material_analysis", material_analysis_node)
    workflow.add_node("pipe_classification", pipe_classification_node)
    workflow.add_node("fitting_classification", fitting_classification_node)
    workflow.add_node("valve_classification", valve_classification_node)
    workflow.add_node("flange_classification", flange_classification_node)
    workflow.add_node("gasket_classification", gasket_classification_node)
    workflow.add_node("bolt_classification", bolt_classification_node)
    workflow.add_node("support_classification", support_classification_node)
    workflow.add_node("miscellaneous_classification", miscellaneous_classification_node)
    workflow.add_node("qa_decisions", qa_decision_node)
    workflow.add_node("self_audit", self_audit_node)

    # Set entry point
    workflow.set_entry_point("material_analysis")

    # Add conditional edges for category routing
    workflow.add_conditional_edges(
        "material_analysis",
        route_to_category_node,
        {
            "pipe": "pipe_classification",
            "fitting": "fitting_classification",
            "valve": "valve_classification",
            "flange": "flange_classification",
            "gasket": "gasket_classification",
            "bolt": "bolt_classification",
            "support": "support_classification",
            "miscellaneous": "miscellaneous_classification"
        }
    )

    # Add edges from category nodes to Q&A decisions
    for category in ["pipe", "fitting", "valve", "flange", "gasket", "bolt", "support", "miscellaneous"]:
        workflow.add_edge(f"{category}_classification", "qa_decisions")

    # Add conditional edge for Q&A decisions
    workflow.add_conditional_edges(
        "qa_decisions",
        should_continue_to_audit,
        {
            "continue": "self_audit",
            "end": END
        }
    )

    # Add final edge
    workflow.add_edge("self_audit", END)

    return workflow.compile()

def route_to_category_node(state: ClassificationState) -> str:
    """Route to appropriate category-specific node"""
    return state["processing_path"]

def should_continue_to_audit(state: ClassificationState) -> str:
    """Determine if self-audit is needed"""
    if state["confidence_scores"] and min(state["confidence_scores"].values()) < 0.8:
        return "continue"
    return "end"
```

## 🛠️ Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
1. **State Management Setup**
   - Define `ClassificationState` structure
   - Implement state persistence and recovery
   - Create workflow orchestration framework

2. **Category Intelligence Development**
   - Build component lookup tables
   - Create abbreviation dictionaries
   - Implement category-specific rule engines

3. **Basic Node Implementation**
   - Material analysis node
   - One category-specific node (Fitting as pilot)
   - Simple Q&A decision node

### Phase 2: Core Workflow (Weeks 3-4)
1. **Complete Node Implementation**
   - All category-specific classification nodes
   - Advanced Q&A decision logic
   - Self-audit validation node

2. **Integration with Existing System**
   - Maintain compatibility with `apply_audit_corrections`
   - Preserve async processing patterns
   - Integrate with existing model handlers

3. **Testing and Validation**
   - Unit tests for each node
   - Integration tests with real data
   - Performance benchmarking

### Phase 3: Enhancement (Weeks 5-6)
1. **Advanced Features**
   - PostgreSQL lookup integration
   - Enhanced confidence scoring
   - Workflow optimization

2. **Production Deployment**
   - A/B testing framework
   - Monitoring and metrics
   - Documentation and training

### Phase 4: Future Enhancements (Weeks 7+)
1. **Machine Learning Integration**
   - Semantic similarity filtering
   - Pattern learning from corrections
   - Automated rule discovery

2. **Advanced Workflows**
   - Multi-component analysis
   - Batch optimization
   - Real-time classification

## 🔗 Integration with Existing Infrastructure

### Compatibility Preservation

**Async Processing**:
```python
# Maintain existing async patterns
async def audit_bom_dataframe_langgraph(
    df: pd.DataFrame,
    config: AuditConfig = None,
    max_concurrent: int = 10
) -> List[AuditResult]:
    """LangGraph-based audit maintaining existing interface"""

    # Create LangGraph workflow
    workflow = create_classification_workflow(config)

    # Process items with existing concurrency control
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_item_with_semaphore(item):
        async with semaphore:
            return await workflow.ainvoke({
                "item_id": item["id"],
                "material_description": item["material_description"],
                "original_classification": item
            })

    # Maintain existing result format
    tasks = [process_item_with_semaphore(item) for item in df.to_dict('records')]
    results = await asyncio.gather(*tasks)

    # Convert to existing AuditResult format
    return [convert_to_audit_result(result) for result in results]
```

**Correction Application**:
```python
# Preserve existing correction workflow
def convert_langgraph_results_to_programmatic_format(
    langgraph_results: List[Dict],
    original_df: pd.DataFrame
) -> pd.DataFrame:
    """Convert LangGraph results to existing programmatic format"""

    programmatic_data = []
    for result in langgraph_results:
        if result["identified_issues"]:
            for issue in result["identified_issues"]:
                programmatic_data.append({
                    'id': result["item_id"],
                    'status': "issues",
                    'material_description': result["material_description"],
                    'column_name': issue["field"],
                    'current_value': issue["current_value"],
                    'suggested_value': issue["suggested_value"],
                    'confidence': issue["confidence"],
                    'explanation': issue["explanation"],
                    'accept_merge': None
                })

    return pd.DataFrame(programmatic_data)
```

### Migration Strategy

**Gradual Transition**:
1. **Parallel Processing**: Run both systems simultaneously for comparison
2. **Category-by-Category**: Migrate one component category at a time
3. **A/B Testing**: Compare accuracy and performance metrics
4. **Fallback Mechanism**: Automatic fallback to monolithic system if needed

**Performance Monitoring**:
```python
MIGRATION_METRICS = {
    "accuracy_comparison": "LangGraph vs Monolithic accuracy rates",
    "processing_time": "Average response time per classification method",
    "token_usage": "Token consumption comparison",
    "user_acceptance": "Correction acceptance rates",
    "error_rates": "Classification error frequency"
}
```

## 📈 Expected Outcomes

### Accuracy Improvements
- **Specialized Processing**: 15-20% improvement in category-specific accuracy
- **Reduced Hallucinations**: Focused prompts reduce irrelevant field confusion
- **Better Edge Case Handling**: Q&A decisions improve ambiguous classification

### Efficiency Gains
- **Token Reduction**: 30% decrease in token usage through focused prompts
- **Processing Speed**: Maintained or improved response times
- **Maintenance Efficiency**: Easier rule updates and category additions

### System Benefits
- **Modularity**: Independent category workflows enable targeted improvements
- **Scalability**: Easy addition of new component categories
- **Observability**: Detailed workflow tracking and performance metrics
- **Flexibility**: Configurable processing paths based on component complexity

## 🎯 Workflow Architecture Diagram

```mermaid
graph TD
    A[Material Description Input] --> B[Stage 1: Material Analysis]
    B --> B1[Extract Properties]
    B1 --> B2[Determine Category]
    B2 --> B3[Assess Complexity]

    B3 --> C{Route to Category}
    C -->|Pipe| D1[Pipe Classification Node]
    C -->|Fitting| D2[Fitting Classification Node]
    C -->|Valve| D3[Valve Classification Node]
    C -->|Flange| D4[Flange Classification Node]
    C -->|Gasket| D5[Gasket Classification Node]
    C -->|Bolt| D6[Bolt Classification Node]
    C -->|Support| D7[Support Classification Node]
    C -->|Misc| D8[Miscellaneous Classification Node]

    D1 --> E[Stage 3: Q&A Decisions]
    D2 --> E
    D3 --> E
    D4 --> E
    D5 --> E
    D6 --> E
    D7 --> E
    D8 --> E

    E --> E1[Ambiguity Detection]
    E1 --> E2[Contextual Reasoning]
    E2 --> E3[Decision Logic]

    E3 --> F{Confidence Check}
    F -->|High Confidence| G[Final Result]
    F -->|Low Confidence| H[Stage 4: Self-Audit]

    H --> H1[Cross-Field Validation]
    H1 --> H2[Consistency Checking]
    H2 --> H3[Issue Identification]
    H3 --> G

    G --> I[Programmatic Output]

    subgraph "Category Intelligence"
        CI1[Component Lookup Tables]
        CI2[Abbreviation Dictionaries]
        CI3[Classification Rules]
        CI4[Cross-Field Validation]
    end

    subgraph "State Management"
        SM1[Classification State]
        SM2[Workflow Tracking]
        SM3[Confidence Scoring]
        SM4[Issue Logging]
    end

    CI1 -.-> D2
    CI2 -.-> D2
    CI3 -.-> D2
    CI4 -.-> H1

    SM1 -.-> B
    SM2 -.-> E
    SM3 -.-> F
    SM4 -.-> H

    style A fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    style G fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    style B fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style H fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style C fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
```

This comprehensive specification provides a detailed roadmap for transitioning from the current monolithic BOM audit system to a specialized, multi-stage LangGraph classification workflow that addresses accuracy limitations through focused, category-specific processing while maintaining full compatibility with existing infrastructure and workflows.
