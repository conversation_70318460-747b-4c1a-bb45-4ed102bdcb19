def detect_overlap_region(rectangles, region):
    """
    Detects rectangles from a list that overlap with a given region.

    Args:
        rectangles: List of rectangles, where each rectangle is [x1, y1, x2, y2]
        region: A region defined as [x1, y1, x2, y2]

    Returns:
        Set of indices of rectangles that overlap with the given region
    """
    import rtree

    if not rectangles:
        return set()

    # Create R-tree spatial index
    idx = rtree.index.Index()

    # Insert all rectangles into the index
    for i, rect in enumerate(rectangles):
        x1, y1, x2, y2 = rect
        idx.insert(i, (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)))

    # Extract region coordinates
    rx1, ry1, rx2, ry2 = region
    rx1, rx2 = min(rx1, rx2), max(rx1, rx2)
    ry1, ry2 = min(ry1, ry2), max(ry1, ry2)

    # Query the index for rectangles that overlap with the region
    overlapping_indices = set(idx.intersection((rx1, ry1, rx2, ry2)))

    return sorted(list(overlapping_indices))

def find_non_overlapping_rectangles(rectangles_a, rectangles_b):
    """
    Finds rectangles from list A that do not overlap with any rectangle from list B.

    Args:
        rectangles_a: First list of rectangles, where each rectangle is [x1, y1, x2, y2]
        rectangles_b: Second list of rectangles, where each rectangle is [x1, y1, x2, y2]

    Returns:
        Set of indices from rectangles_a that do not overlap with any rectangle in rectangles_b
    """
    import rtree

    if not rectangles_a or not rectangles_b:
        # If either list is empty, all rectangles in A don't overlap with B
        return set(range(len(rectangles_a)))

    # Create R-tree spatial index for rectangles_b
    idx_b = rtree.index.Index()

    # Insert all rectangles from list B into the index
    for i, rect in enumerate(rectangles_b):
        x1, y1, x2, y2 = rect
        idx_b.insert(i, (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)))

    # Find rectangles in A that don't overlap with any in B
    non_overlapping_indices = set()

    for i, rect_a in enumerate(rectangles_a):
        x1, y1, x2, y2 = rect_a
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)

        # If no rectangles in B overlap with this rectangle from A
        if not list(idx_b.intersection((x1, y1, x2, y2))):
            non_overlapping_indices.add(i)

    return sorted(list(non_overlapping_indices))

def find_overlapping_rectangles_with_areas(rectangles_a, rectangles_b) -> dict:
    """
    Finds rectangles from list A that overlap with any rectangle from list B,
    and returns both the indices and the overlap areas.

    Args:
        rectangles_a: First list of rectangles, where each rectangle is [x1, y1, x2, y2]
        rectangles_b: Second list of rectangles, where each rectangle is [x1, y1, x2, y2]

    Returns:
        Dictionary mapping indices from rectangles_a to a list of tuples (b_index, overlap_area)
        where b_index is the index from rectangles_b and overlap_area is the area of overlap
    """
    import rtree

    if not rectangles_a or not rectangles_b:
        return {}

    # Create R-tree spatial index for rectangles_b
    idx_b = rtree.index.Index()

    # Insert all rectangles from list B into the index
    for i, rect in enumerate(rectangles_b):
        x1, y1, x2, y2 = rect
        idx_b.insert(i, (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)))

    # Find rectangles in A that overlap with any in B and calculate areas
    overlap_results = {}

    for i, rect_a in enumerate(rectangles_a):
        x1, y1, x2, y2 = rect_a
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)

        # Find all rectangles in B that potentially overlap with this rectangle from A
        overlapping_b_indices = list(idx_b.intersection((x1, y1, x2, y2)))

        if overlapping_b_indices:
            overlap_info = []

            for b_idx in sorted(overlapping_b_indices):
                b_rect = rectangles_b[b_idx]
                b_x1, b_y1, b_x2, b_y2 = b_rect
                b_x1, b_x2 = min(b_x1, b_x2), max(b_x1, b_x2)
                b_y1, b_y2 = min(b_y1, b_y2), max(b_y1, b_y2)

                # Calculate the overlapping area
                x_left = max(x1, b_x1)
                y_top = max(y1, b_y1)
                x_right = min(x2, b_x2)
                y_bottom = min(y2, b_y2)

                if x_right > x_left and y_bottom > y_top:
                    overlap_area = (x_right - x_left) * (y_bottom - y_top)
                    overlap_info.append((b_idx, overlap_area))

            if overlap_info:
                overlap_results[i] = overlap_info

    return overlap_results

def calculate_overlap_area(box1, box2):
    """
    Calculate the overlapping area between two rectangles.

    Args:
        box1: First rectangle as [x1, y1, x2, y2]
        box2: Second rectangle as [x1, y1, x2, y2]

    Returns:
        Float representing the area of overlap
    """
    # Ensure coordinates are properly ordered
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_1, x2_1 = min(x1_1, x2_1), max(x1_1, x2_1)
    y1_1, y2_1 = min(y1_1, y2_1), max(y1_1, y2_1)

    x1_2, y1_2, x2_2, y2_2 = box2
    x1_2, x2_2 = min(x1_2, x2_2), max(x1_2, x2_2)
    y1_2, y2_2 = min(y1_2, y2_2), max(y1_2, y2_2)

    # Calculate the overlapping area
    x_left = max(x1_1, x1_2)
    y_top = max(y1_1, y1_2)
    x_right = min(x2_1, x2_2)
    y_bottom = min(y2_1, y2_2)

    if x_right <= x_left or y_bottom <= y_top:
        return 0.0  # No overlap

    return (x_right - x_left) * (y_bottom - y_top)

if __name__ == "__main__":
    rectTarget = [10, 10, 101, 101]
    rect1 = [100, 100, 200, 200]
    rect2 = [150, 150, 210, 210]
    rect3 = [205, 205, 500, 400]
    rect4 = [50, 50, 90, 90]

    rectangles_a = [rect1, rect2, rect3, rect4]
    rectangles_b = [rectTarget]

    # Test detect_overlap_region
    overlapping = detect_overlap_region(rectangles_a, rectTarget)
    print(f"Rectangles overlapping with target: {overlapping}")

    # Test find_non_overlapping_rectangles
    non_overlapping = find_non_overlapping_rectangles(rectangles_a, rectangles_b)
    print(f"Rectangles not overlapping with any in list B: {non_overlapping}")

    # Test find_overlapping_rectangles_with_areas
    overlapping_with_areas = find_overlapping_rectangles_with_areas(rectangles_a, rectangles_b)
    print(f"Rectangles overlapping with areas: {overlapping_with_areas}")
