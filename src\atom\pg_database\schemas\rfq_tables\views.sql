/*
-- View to aggregate BOM data, creating an RFQ. This is more reliable and stays in sync more easily without 
the need for complex triggers and propogation. 
*/
CREATE OR REPLACE VIEW vw_atem_rfq_aggregated AS
SELECT 
    MIN(b.id) AS id,
    b.project_id,
    MIN(b.id) AS rfq_input_id,
    b.material_description,
    b.normalized_description,
    b.size,
    b.size1,
    b.size2,
    SUM(b.quantity) AS quantity,
    b.mtrl_category,
    b.rfq_scope,
    b.general_category,
    b.unit_of_measure,
    b.material,
    b.abbreviated_material,
    b.technical_standard,
    b.astm,
    b.grade,
    b.rating,
    b.schedule,
    b.coating,
    b.forging,
    b.ends,
    MAX(b.item_tag) AS item_tag,
    MAX(b.tie_point) AS tie_point,
    b.pipe_category,
    b.valve_type,
    b.fitting_category,
    b.weld_category,
    b.bolt_category,
    b.gasket_category,
    SUM(b.calculated_eq_length) AS calculated_eq_length,
    SUM(b.calculated_area) AS calculated_area,
    MAX(b.notes) AS notes,
    FALSE AS deleted,
    FALSE AS ignore_item,
    MAX(b.validated_date) AS validated_date,
    MAX(b.validated_by) AS validated_by,
    MIN(b.created_at) AS created_at,
    MAX(b.created_by) AS created_by,
    MAX(b.updated_at) AS updated_at,
    MAX(b.updated_by) AS updated_by,
    b.profile_id
FROM 
    bom b
WHERE 
    b.deleted IS NOT TRUE
    AND b.ignore_item IS NOT TRUE
GROUP BY
    b.project_id,
    b.material_description,
    b.normalized_description,
    b.size,
    b.size1,
    b.size2,
    b.mtrl_category,
    b.rfq_scope,
    b.general_category,
    b.unit_of_measure,
    b.material,
    b.abbreviated_material,
    b.technical_standard,
    b.astm,
    b.grade,
    b.rating,
    b.schedule,
    b.coating,
    b.forging,
    b.ends,
    b.pipe_category,
    b.valve_type,
    b.fitting_category,
    b.weld_category,
    b.bolt_category,
    b.gasket_category,
    b.profile_id;



    CREATE OR REPLACE FUNCTION public.validate_bom_view_comparison(p_project_id integer)
RETURNS TABLE(metric_type text, category text, bom_value numeric, view_value numeric, difference numeric, difference_percentage numeric)
LANGUAGE plpgsql
AS $function$
BEGIN
    -- Validate quantities
    RETURN QUERY
    SELECT 
        'Quantity'::TEXT AS metric_type,
        COALESCE(b.general_category, r.general_category)::TEXT AS category,
        COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
        COALESCE(r.rfq_sum, 0)::NUMERIC AS view_value,
        (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
        CASE 
            WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
            ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
        END AS difference_percentage
    FROM
        (SELECT 
            general_category, 
            SUM(quantity)::NUMERIC AS bom_sum
        FROM 
            public.bom
        WHERE 
            project_id = p_project_id
            AND deleted IS NOT TRUE
            AND ignore_item IS NOT TRUE
        GROUP BY 
            general_category) b
    FULL OUTER JOIN
        (SELECT 
            general_category, 
            SUM(quantity)::NUMERIC AS rfq_sum
        FROM 
            public.vw_atem_rfq_aggregated
        WHERE 
            project_id = p_project_id
        GROUP BY 
            general_category) r
    ON b.general_category = r.general_category;

    -- Validate equivalent lengths
    RETURN QUERY
    SELECT 
        'Equivalent Length'::TEXT AS metric_type,
        COALESCE(b.general_category, r.general_category)::TEXT AS category,
        COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
        COALESCE(r.rfq_sum, 0)::NUMERIC AS view_value,
        (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
        CASE 
            WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
            ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
        END AS difference_percentage
    FROM
        (SELECT 
            general_category, 
            SUM(calculated_eq_length)::NUMERIC AS bom_sum
        FROM 
            public.bom
        WHERE 
            project_id = p_project_id
            AND deleted IS NOT TRUE
            AND ignore_item IS NOT TRUE
        GROUP BY 
            general_category) b
    FULL OUTER JOIN
        (SELECT 
            general_category, 
            SUM(calculated_eq_length)::NUMERIC AS rfq_sum
        FROM 
            public.vw_atem_rfq_aggregated
        WHERE 
            project_id = p_project_id
        GROUP BY 
            general_category) r
    ON b.general_category = r.general_category;

    -- Validate areas
    RETURN QUERY
    SELECT 
        'Area'::TEXT AS metric_type,
        COALESCE(b.general_category, r.general_category)::TEXT AS category,
        COALESCE(b.bom_sum, 0)::NUMERIC AS bom_value,
        COALESCE(r.rfq_sum, 0)::NUMERIC AS view_value,
        (COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0))::NUMERIC AS difference,
        CASE 
            WHEN COALESCE(b.bom_sum, 0) = 0 THEN NULL
            ELSE ROUND((COALESCE(r.rfq_sum, 0) - COALESCE(b.bom_sum, 0)) / COALESCE(b.bom_sum, 1) * 100, 2)
        END AS difference_percentage
    FROM
        (SELECT 
            general_category, 
            SUM(calculated_area)::NUMERIC AS bom_sum
        FROM 
            public.bom
        WHERE 
            project_id = p_project_id
            AND deleted IS NOT TRUE
            AND ignore_item IS NOT TRUE
        GROUP BY 
            general_category) b
    FULL OUTER JOIN
        (SELECT 
            general_category, 
            SUM(calculated_area)::NUMERIC AS rfq_sum
        FROM 
            public.vw_atem_rfq_aggregated
        WHERE 
            project_id = p_project_id
        GROUP BY 
            general_category) r
    ON b.general_category = r.general_category;
END;
$function$;