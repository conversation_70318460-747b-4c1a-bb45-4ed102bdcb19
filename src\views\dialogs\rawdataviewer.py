import os
import fitz  # PyMuPDF

import pandas as pd
import openpyxl
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtCore import QUrl
from PySide6.QtGui import QDesktopServices

from src.atom.dbManager import DatabaseManager
from src.app_paths import getSourceRawDataPath
from src.utils.convert_roi_payload import convert_roi_payload


def getSimpleFieldMap():
    import sys
    sys.path[0] = ""
    from src.app_paths import getSavedFieldMapJson
    fieldMap = getSavedFieldMapJson()
    simpleFieldMap = {}
    for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
        subData = fieldMap.get(key, {})
        # Discard unuseful data
        for _, v in subData.items():
            try:
                del v["options"]
            except Exception as e:
                pass
        simpleFieldMap.update(subData)
    return simpleFieldMap

class PandasModel(QAbstractTableModel):
    """
    A model for displaying pandas DataFrames in a QTableView.
    Optimized for large datasets.
    """
    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else pd.DataFrame()

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._data.columns)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            value = self._data.iloc[index.row(), index.column()]
            # Handle different data types appropriately
            try:
                # Try pandas isna first which handles various NA types
                if pd.isna(value):
                    return ""
                # Handle numpy arrays and other array-like objects
                elif hasattr(value, 'dtype') and hasattr(value, 'shape'):
                    if hasattr(value, 'tolist'):
                        return str(value.tolist())
                    return str(value)
                elif isinstance(value, (int, float, complex)):
                    return str(value)
                elif isinstance(value, (list, tuple)):
                    return str(value)
                else:
                    return str(value)
            except (TypeError, ValueError):
                # Fallback for any values that cause issues
                return str(value)

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._data.columns[section])
            else:
                return str(section + 1)
        return None

    def setData(self, data):
        self.beginResetModel()
        self._data = data if data is not None else pd.DataFrame()
        self.endResetModel()

class ColoredPandasModel(PandasModel):
    """
    An extension of PandasModel that supports cell background colors based on row values.
    """
    def __init__(self, data=None, color_column=None, color_map=None):
        super().__init__(data)
        self.color_column = color_column
        self.color_map = color_map or {}

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.BackgroundRole and self.color_column is not None:
            row = index.row()
            if row < len(self._data):
                value = self._data.iloc[row][self.color_column]
                if value in self.color_map:
                    return QBrush(QColor(self.color_map[value]))

        return super().data(index, role)

    def setColorColumn(self, column):
        self.color_column = column
        self.layoutChanged.emit()

    def setColorMap(self, color_map):
        self.color_map = color_map
        self.layoutChanged.emit()

class DrawingsTableModel(QAbstractTableModel):
    """
    A model for displaying PDF drawings in a QTableView.
    """
    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else []
        self._headers = ["ID", "Type", "Color", "Fill", "Line Width", "bbox", "Area", "Length", "Page"]

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            drawing = self._data[index.row()]
            col = index.column()

            if col == 0:  # ID
                return str(index.row() + 1)
            elif col == 1:  # Type
                return self._get_drawing_type(drawing['drawing'])
            elif col == 2:  # Color
                return self._get_drawing_color(drawing['drawing'])
            elif col == 3:  # Fill
                return "Yes" if self._has_fill(drawing['drawing']) else "No"
            elif col == 4:  # Line Width
                return self._get_line_width(drawing['drawing'])
            elif col == 5:  # bbox
                return self._get_bounds(drawing['drawing'])
            elif col == 6:  # Area
                return self._calculate_area(drawing['drawing'])
            elif col == 7:  # Length
                return self._calculate_length(drawing['drawing'])
            elif col == 8:  # Page
                return str(drawing['page'] + 1)  # 1-based page number for display

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return self._headers[section]
            else:
                return str(section + 1)
        return None

    def setData(self, data):
        self.beginResetModel()
        self._data = data if data is not None else []
        self.endResetModel()

    def _get_drawing_type(self, drawing):
        """Get the type of drawing (line, rectangle, etc.)"""
        if 'type' in drawing:
            return drawing['type']

        # Try to determine type from items
        items = drawing.get('items', [])
        if not items:
            return "Unknown"

        # Check first item's type
        item = items[0]
        if item.get('type') == 'l':
            return "Line"
        elif item.get('type') == 're':
            return "Rectangle"
        elif item.get('type') == 'qu':
            return "Quad"
        elif item.get('type') == 'c':
            return "Curve"
        else:
            return item.get('type', "Unknown")

    def _get_drawing_color(self, drawing):
        """Get the color of the drawing as a hex string"""
        # Try to get stroke color
        stroke_color = drawing.get('stroke_color')
        if stroke_color:
            print(f"Found stroke_color: {stroke_color}")
            if isinstance(stroke_color, (list, tuple)) and len(stroke_color) >= 3:
                r, g, b = stroke_color[:3]
                # Convert from 0-1 range to 0-255 range
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(stroke_color)

        # Try to get color from items
        items = drawing.get('items', [])
        if items:
            print(f"Checking items: {len(items)} items")

            # Check all items for any color-related attributes
            for item in items:
                if not isinstance(item, dict):
                    continue

                # Check for 'color' attribute
                if 'color' in item:
                    color = item['color']
                    print(f"Found color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

                # Check for 'colors' attribute
                if 'colors' in item:
                    colors = item['colors']
                    print(f"Found colors in item: {colors}")
                    if isinstance(colors, (list, tuple)) and len(colors) > 0:
                        color = colors[0]
                        if isinstance(color, (list, tuple)) and len(color) >= 3:
                            r, g, b = color[:3]
                            r = min(255, max(0, int(r * 255)))
                            g = min(255, max(0, int(g * 255)))
                            b = min(255, max(0, int(b * 255)))
                            return f"#{r:02x}{g:02x}{b:02x}"
                        return str(colors[0])

                # Check for 'stroke_color' attribute
                if 'stroke_color' in item:
                    color = item['stroke_color']
                    print(f"Found stroke_color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

                # Check for 'fill_color' attribute
                if 'fill_color' in item:
                    color = item['fill_color']
                    print(f"Found fill_color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

        # Check for fill color as a fallback
        fill_color = drawing.get('fill_color')
        if fill_color:
            print(f"Found fill_color: {fill_color}")
            if isinstance(fill_color, (list, tuple)) and len(fill_color) >= 3:
                r, g, b = fill_color[:3]
                # Convert from 0-1 range to 0-255 range
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(fill_color)

        # Check for any other color-related attributes
        for key in drawing.keys():
            if 'color' in key.lower():
                color_value = drawing[key]
                print(f"Found color in key {key}: {color_value}")
                if isinstance(color_value, (list, tuple)) and len(color_value) >= 3:
                    r, g, b = color_value[:3]
                    r = min(255, max(0, int(r * 255)))
                    g = min(255, max(0, int(g * 255)))
                    b = min(255, max(0, int(b * 255)))
                    return f"#{r:02x}{g:02x}{b:02x}"
                return str(color_value)

        # If we still don't have a color, use a default based on drawing type
        drawing_type = self._get_drawing_type(drawing)
        if drawing_type == "Line":
            return "#000000"  # Black for lines
        elif drawing_type == "Rectangle":
            return "#0000FF"  # Blue for rectangles
        elif drawing_type == "Curve":
            return "#FF0000"  # Red for curves
        elif drawing_type == "Quad":
            return "#00FF00"  # Green for quads

        print(f"No color found in drawing: {drawing.keys()}")
        return "Unknown"

    def _has_fill(self, drawing):
        """Check if the drawing has a fill color"""
        # Check for fill color
        if drawing.get('fill_color'):
            return True

        # Check for fill in items
        items = drawing.get('items', [])
        for item in items:
            # Check if item is a dictionary before using get()
            if isinstance(item, dict) and item.get('fill'):
                return True

        return False

    def _get_line_width(self, drawing):
        """Get the line width of the drawing"""
        # Check for width
        width = drawing.get('width')
        if width is not None:
            return f"{width:.2f}"

        # Check for width in items
        items = drawing.get('items', [])
        if items and 'width' in items[0]:
            return f"{items[0]['width']:.2f}"

        return "1.00"  # Default width

    def _get_bounds(self, drawing):
        """Get the bounding box coordinates of the drawing"""
        rect = drawing.get('rect')
        if rect and len(rect) == 4:
            x0, y0, x1, y1 = rect
            return f"({x0:.1f},{y0:.1f},{x1:.1f},{y1:.1f})"

        # Try to get bounds from items
        items = drawing.get('items', [])
        if items:
            # For lines, use start and end points
            if self._get_drawing_type(drawing) == "Line" and len(items) >= 1:
                item = items[0]
                if isinstance(item, dict) and 'start' in item and 'end' in item:
                    x0, y0 = item['start']
                    x1, y1 = item['end']
                    return f"({x0:.1f},{y0:.1f},{x1:.1f},{y1:.1f})"

        return "N/A"

    def _calculate_area(self, drawing):
        """Calculate the approximate area of the drawing"""
        rect = drawing.get('rect')
        if rect and len(rect) == 4:
            width = abs(rect[2] - rect[0])
            height = abs(rect[3] - rect[1])
            return f"{width * height:.2f}"

        return "N/A"

    def _calculate_length(self, drawing):
        """Calculate the approximate length/perimeter of the drawing"""
        items = drawing.get('items', [])
        if not items:
            return "N/A"

        # For lines, calculate length
        if self._get_drawing_type(drawing) == "Line" and len(items) >= 1:
            item = items[0]
            if 'start' in item and 'end' in item:
                x1, y1 = item['start']
                x2, y2 = item['end']

                # Calculate length
                length = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                return f"{length:.2f}"

        # For rectangles, calculate perimeter
        if self._get_drawing_type(drawing) == "Rectangle" and 'rect' in drawing:
            rect = drawing['rect']
            width = abs(rect[2] - rect[0])
            height = abs(rect[3] - rect[1])
            return f"{2 * (width + height):.2f}"

        return "N/A"


class PDFGraphicsView(QGraphicsView):
    """
    A custom QGraphicsView for displaying PDF pages with zoom and scroll functionality.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setScene(QGraphicsScene(self))
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setDragMode(QGraphicsView.ScrollHandDrag)

        # Set background color
        self.setBackgroundBrush(QBrush(QColor(240, 240, 240)))

        # Initialize variables
        self.currentPage = None
        self.pdfDocument = None
        self.currentProjectId = None
        self.currentFilename = None
        self.zoomFactor = 1.0
        self.pixmapItem = None
        self.maintainPosition = False

    def wheelEvent(self, event):
        """Handle zoom in/out with mouse wheel"""
        if event.modifiers() & Qt.ControlModifier:
            # Zoom factor
            zoomInFactor = 1.25
            zoomOutFactor = 1 / zoomInFactor

            # Save the scene pos
            oldPos = self.mapToScene(event.position().toPoint())

            # Zoom
            if event.angleDelta().y() > 0:
                zoomFactor = zoomInFactor
            else:
                zoomFactor = zoomOutFactor
            self.scale(zoomFactor, zoomFactor)
            self.zoomFactor *= zoomFactor

            # Get the new position
            newPos = self.mapToScene(event.position().toPoint())

            # Move scene to old position
            delta = newPos - oldPos
            self.translate(delta.x(), delta.y())
        else:
            super().wheelEvent(event)

    def resetZoom(self):
        """Reset zoom to original size"""
        self.resetTransform()
        self.zoomFactor = 1.0

    def zoomIn(self):
        """Zoom in by 25%"""
        self.scale(1.25, 1.25)
        self.zoomFactor *= 1.25

    def zoomOut(self):
        """Zoom out by 20%"""
        self.scale(0.8, 0.8)
        self.zoomFactor *= 0.8

    def fitInView(self):
        """Fit the current page in view"""
        if self.pixmapItem:
            self.resetTransform()
            self.zoomFactor = 1.0
            super().fitInView(self.pixmapItem, Qt.KeepAspectRatio)
            # Update zoom factor based on the transformation
            self.zoomFactor = self.transform().m11()  # Get the horizontal scale factor

    def loadPDF(self, projectId, filename):
        """Load the PDF document"""
        self.currentProjectId = projectId
        self.currentFilename = filename

        try:
            # Load the PDF directly using the filename as the path
            self.pdfDocument = fitz.open(filename)
            print(f"Successfully loaded PDF: {filename}")
            return True
        except Exception as e:
            print(f"Error loading PDF: {e}")

        self.pdfDocument = None
        return False

    def displayPage(self, page_number):
        """Display the specified page number"""
        if not self.pdfDocument or page_number < 0 or page_number >= len(self.pdfDocument):
            return False

        try:
            # Get the page
            page = self.pdfDocument[page_number]

            # Render page to pixmap
            # Higher zoom factor for better quality
            matrix = fitz.Matrix(2, 2)
            pixmap = page.get_pixmap(matrix=matrix)

            # Convert pixmap to QImage
            img = QImage(pixmap.samples, pixmap.width, pixmap.height,
                         pixmap.stride, QImage.Format_RGB888)

            # Convert QImage to QPixmap
            pixmap = QPixmap.fromImage(img)

            # Clear previous content
            self.scene().clear()

            # Add pixmap to scene
            self.pixmapItem = self.scene().addPixmap(pixmap)

            # Fit the page in view
            self.fitInView()

            self.currentPage = page_number
            return True
        except Exception as e:
            print(f"Error displaying page {page_number}: {e}")
            return False


class RawDataViewer(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.db = DatabaseManager()
        self.currentProjectId = None
        self.currentFilename = None
        self.projectSourcesDf = None
        self.availablePages = None
        self.currentPage = None
        self.currentDataFrame = None

        self.setObjectName("popup")
        self.setWindowTitle("Raw Data Viewer")
        self.setLayout(QVBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setMinimumWidth(900)
        self.setMinimumHeight(600)

        # Create toolbar
        self.setupToolbar()

        # Create filter toolbar
        self.setupFilterToolbar()

        self.setupFilterUI()

        # Create export UI
        self.setupExportUI()

        # Create tabbed interface
        self.setupTabbedInterface()

        # Initialize data
        self.refreshProjectSources()

    def setupToolbar(self):
        """Set up the horizontal toolbar with controls"""
        self.toolbar = QToolBar()
        self.toolbar.setMovable(False)
        self.toolbar.setFloatable(False)
        self.toolbar.setIconSize(QSize(16, 16))

        # Add project source selection
        self.lblSource = QLabel("Project Source:")
        self.toolbar.addWidget(self.lblSource)

        self.cboxSource = QComboBox()
        self.cboxSource.setMinimumWidth(300)
        self.cboxSource.currentIndexChanged.connect(self.onSourceSelected)
        self.toolbar.addWidget(self.cboxSource)

        # Add load button
        self.pbLoad = QPushButton("Load Data")
        self.pbLoad.clicked.connect(self.loadData)
        self.toolbar.addWidget(self.pbLoad)

        # Add refresh button
        self.pbRefresh = QPushButton("Refresh Sources")
        self.pbRefresh.clicked.connect(self.refreshProjectSources)
        self.toolbar.addWidget(self.pbRefresh)

        # Add generate report button
        self.pbGenerateReport = QPushButton("Generate Report")
        self.pbGenerateReport.clicked.connect(self.onGenerateReport)
        self.toolbar.addWidget(self.pbGenerateReport)

        # Add separator
        self.toolbar.addSeparator()

        # Add position anchoring toggle button
        self.cbAnchorPosition = QCheckBox("Maintain Position")
        self.cbAnchorPosition.setToolTip("Maintain zoom level and scroll position when changing pages")
        self.cbAnchorPosition.setChecked(False)
        self.cbAnchorPosition.stateChanged.connect(self.onAnchorPositionChanged)
        self.toolbar.addWidget(self.cbAnchorPosition)

        # Add toolbar to layout
        self.layout().addWidget(self.toolbar)

    def setupFilterToolbar(self):
        """Set up the filter toolbar with page navigation controls"""
        filterToolbarWidget = QWidget()
        filterToolbarLayout = QHBoxLayout(filterToolbarWidget)
        filterToolbarLayout.setContentsMargins(10, 5, 10, 5)

        # Page filter section
        self.lblPage = QLabel("Page:")
        self.cboxPage = QComboBox()
        self.cboxPage.setMinimumWidth(100)
        self.cboxPage.setEditable(True)
        self.cboxPage.currentTextChanged.connect(self.onPageTextChanged)
        self.cboxPage.currentIndexChanged.connect(self.onPageSelected)

        # Navigation buttons
        self.pbPrevPage = QPushButton("◀ Previous")
        self.pbPrevPage.clicked.connect(self.onPreviousPage)
        self.pbPrevPage.setEnabled(False)

        self.pbNextPage = QPushButton("Next ▶")
        self.pbNextPage.clicked.connect(self.onNextPage)
        self.pbNextPage.setEnabled(False)

        # Text filter
        self.lblFilter = QLabel("Filter:")
        self.txtFilter = QLineEdit()
        self.txtFilter.setPlaceholderText("Enter text to filter...")
        self.txtFilter.setEnabled(False)
        self.txtFilter.textChanged.connect(self.applyFilters)
        self.txtFilter.setClearButtonEnabled(True)

        # Reset button
        self.pbResetFilters = QPushButton("Reset Filters")
        self.pbResetFilters.clicked.connect(self.resetFilters)
        self.pbResetFilters.setEnabled(False)

        # Add widgets to toolbar
        filterToolbarLayout.addWidget(self.lblPage)
        filterToolbarLayout.addWidget(self.cboxPage)
        filterToolbarLayout.addWidget(self.pbPrevPage)
        filterToolbarLayout.addWidget(self.pbNextPage)
        filterToolbarLayout.addStretch(1)
        filterToolbarLayout.addWidget(self.lblFilter)
        filterToolbarLayout.addWidget(self.txtFilter)
        filterToolbarLayout.addWidget(self.pbResetFilters)

        self.layout().addWidget(filterToolbarWidget)

    def setupTabbedInterface(self):
        """Set up the tabbed interface with table and PDF viewer"""
        self.tabWidget = QTabWidget()

        # Table tab
        self.tableTab = QWidget()
        self.tableTab.setLayout(QVBoxLayout())
        self.tableTab.layout().setContentsMargins(0, 0, 0, 0)

        self.setupTableView()
        self.tableTab.layout().addWidget(self.tableView)

        # PDF Viewer tab
        self.pdfTab = QWidget()
        self.pdfTab.setLayout(QVBoxLayout())
        self.pdfTab.layout().setContentsMargins(0, 0, 0, 0)

        self.setupPdfViewer()
        self.pdfTab.layout().addWidget(self.pdfSplitter)

        # Initialize Review tab
        self.reviewTab = QWidget()
        self.setupReviewTab()

        # Add tabs to tab widget
        self.tabWidget.addTab(self.tableTab, "Table View")
        self.tabWidget.addTab(self.pdfTab, "PDF Viewer")
        self.tabWidget.addTab(self.reviewTab, "Initialize Review")

        # Connect tab changed signal
        self.tabWidget.currentChanged.connect(self.onTabChanged)

        # Add tab widget to layout
        self.layout().addWidget(self.tabWidget)

    def setupReviewTab(self):
        """Set up the Initialize Review tab with toolbar and table views"""
        # Create main layout
        mainLayout = QVBoxLayout()
        self.reviewTab.setLayout(mainLayout)
        mainLayout.setContentsMargins(0, 0, 0, 0)

        # Create toolbar
        toolbar = QToolBar()
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        # Create form layout for the inputs
        formWidget = QWidget()
        formLayout = QFormLayout(formWidget)
        formLayout.setContentsMargins(10, 10, 10, 10)

        # BOM Data file input
        bomInputLayout = QHBoxLayout()
        self.txtBomDataFile = QLineEdit()
        self.txtBomDataFile.setPlaceholderText("Select BOM data Excel file...")
        self.txtBomDataFile.setMinimumWidth(300)
        self.pbBrowseBomData = QPushButton("Browse...")
        self.pbBrowseBomData.clicked.connect(self.onBrowseBomData)
        self.pbLoadBomData = QPushButton("Load")
        self.pbLoadBomData.clicked.connect(self.onLoadBomData)

        bomInputLayout.addWidget(self.txtBomDataFile)
        bomInputLayout.addWidget(self.pbBrowseBomData)
        bomInputLayout.addWidget(self.pbLoadBomData)

        # General Data file input
        generalInputLayout = QHBoxLayout()
        self.txtGeneralDataFile = QLineEdit()
        self.txtGeneralDataFile.setPlaceholderText("Select General data Excel file...")
        self.txtGeneralDataFile.setMinimumWidth(300)
        self.pbBrowseGeneralData = QPushButton("Browse...")
        self.pbBrowseGeneralData.clicked.connect(self.onBrowseGeneralData)
        self.pbLoadGeneralData = QPushButton("Load")
        self.pbLoadGeneralData.clicked.connect(self.onLoadGeneralData)

        generalInputLayout.addWidget(self.txtGeneralDataFile)
        generalInputLayout.addWidget(self.pbBrowseGeneralData)
        generalInputLayout.addWidget(self.pbLoadGeneralData)

        # Report file input
        reportInputLayout = QHBoxLayout()
        self.txtReportFile = QLineEdit()
        self.txtReportFile.setPlaceholderText("Select report Excel file...")
        self.txtReportFile.setMinimumWidth(300)
        self.pbBrowseReportFile = QPushButton("Browse...")
        self.pbBrowseReportFile.clicked.connect(self.onBrowseReportFile)
        self.pbLoadReportFile = QPushButton("Load")
        self.pbLoadReportFile.clicked.connect(self.onLoadReportFile)

        reportInputLayout.addWidget(self.txtReportFile)
        reportInputLayout.addWidget(self.pbBrowseReportFile)
        reportInputLayout.addWidget(self.pbLoadReportFile)

        # ROI Payload file input
        roiInputLayout = QHBoxLayout()
        self.txtRoiPayloadFile = QLineEdit()
        self.txtRoiPayloadFile.setPlaceholderText("Select ROI payload file...")
        self.txtRoiPayloadFile.setMinimumWidth(300)
        self.pbBrowseRoiPayloadFile = QPushButton("Browse...")
        self.pbBrowseRoiPayloadFile.clicked.connect(self.onBrowseRoiPayloadFile)
        self.pbLoadRoiPayloadFile = QPushButton("Load")
        self.pbLoadRoiPayloadFile.clicked.connect(self.onLoadRoiPayloadFile)

        roiInputLayout.addWidget(self.txtRoiPayloadFile)
        roiInputLayout.addWidget(self.pbBrowseRoiPayloadFile)
        roiInputLayout.addWidget(self.pbLoadRoiPayloadFile)

        # Analysis buttons
        analysisLayout = QHBoxLayout()
        self.pbAnalyzeBom = QPushButton("Analyze BOM")
        self.pbAnalyzeBom.clicked.connect(self.onAnalyzeBom)
        self.pbAnalyzeGeneral = QPushButton("Analyze General")
        self.pbAnalyzeGeneral.clicked.connect(self.onAnalyzeGeneral)

        analysisLayout.addWidget(self.pbAnalyzeBom)
        analysisLayout.addWidget(self.pbAnalyzeGeneral)
        analysisLayout.addStretch()

        # Add to form layout
        formLayout.addRow("BOM Data:", bomInputLayout)
        formLayout.addRow("General Data:", generalInputLayout)
        formLayout.addRow("Report File:", reportInputLayout)
        formLayout.addRow("ROI Payload:", roiInputLayout)
        formLayout.addRow("Analysis:", analysisLayout)

        # Add form to main layout
        mainLayout.addWidget(formWidget)

        # Create tab widget for data types
        self.reviewTabWidget = QTabWidget()

        # Create tabs for BOM and General data
        self.bomDataTab = QWidget()
        self.bomDataTab.setLayout(QVBoxLayout())
        self.bomDataTab.layout().setContentsMargins(0, 0, 0, 0)

        self.generalDataTab = QWidget()
        self.generalDataTab.setLayout(QVBoxLayout())
        self.generalDataTab.layout().setContentsMargins(0, 0, 0, 0)

        # Create horizontal splitter for each tab
        self.bomSplitter = QSplitter(Qt.Horizontal)
        self.generalSplitter = QSplitter(Qt.Horizontal)

        # Create left pane table for each tab (empty for now)
        self.bomLeftTable = QTableView()
        self.bomLeftTable.setAlternatingRowColors(True)
        self.bomLeftTable.setSortingEnabled(True)
        self.bomLeftTable.horizontalHeader().setStretchLastSection(True)
        self.bomLeftTable.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        self.generalLeftTable = QTableView()
        self.generalLeftTable.setAlternatingRowColors(True)
        self.generalLeftTable.setSortingEnabled(True)
        self.generalLeftTable.horizontalHeader().setStretchLastSection(True)
        self.generalLeftTable.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Create right pane vertical splitter for each tab
        self.bomRightSplitter = QSplitter(Qt.Vertical)
        self.generalRightSplitter = QSplitter(Qt.Vertical)

        # Create PDF viewers for top pane
        self.bomPdfViewer = PDFGraphicsView()
        self.generalPdfViewer = PDFGraphicsView()

        # Create data tables for bottom pane
        self.bomTableView = QTableView()
        self.bomTableView.setAlternatingRowColors(True)
        self.bomTableView.setSortingEnabled(True)
        self.bomTableView.horizontalHeader().setStretchLastSection(True)
        self.bomTableView.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.bomTableView.setSelectionBehavior(QAbstractItemView.SelectRows)

        self.generalTableView = QTableView()
        self.generalTableView.setAlternatingRowColors(True)
        self.generalTableView.setSortingEnabled(True)
        self.generalTableView.horizontalHeader().setStretchLastSection(True)
        self.generalTableView.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.generalTableView.setSelectionBehavior(QAbstractItemView.SelectRows)

        # Create bottom table for sample data (not used in splitter)
        self.bomBottomTable = QTableView()
        self.bomBottomTable.setAlternatingRowColors(True)
        self.bomBottomTable.setSortingEnabled(True)
        self.bomBottomTable.horizontalHeader().setStretchLastSection(True)
        self.bomBottomTable.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        self.generalBottomTable = QTableView()
        self.generalBottomTable.setAlternatingRowColors(True)
        self.generalBottomTable.setSortingEnabled(True)
        self.generalBottomTable.horizontalHeader().setStretchLastSection(True)
        self.generalBottomTable.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Add PDF viewers and data tables to vertical splitters
        # PDF viewer on top, data table at the bottom
        self.bomRightSplitter.addWidget(self.bomPdfViewer)
        self.bomRightSplitter.addWidget(self.bomTableView)
        self.bomRightSplitter.setSizes([600, 400])  # 60% top, 40% bottom

        self.generalRightSplitter.addWidget(self.generalPdfViewer)
        self.generalRightSplitter.addWidget(self.generalTableView)
        self.generalRightSplitter.setSizes([600, 400])  # 60% top, 40% bottom

        # Add tables and splitters to horizontal splitters
        self.bomSplitter.addWidget(self.bomLeftTable)
        self.bomSplitter.addWidget(self.bomRightSplitter)
        self.bomSplitter.setSizes([300, 700])  # 30% left, 70% right

        self.generalSplitter.addWidget(self.generalLeftTable)
        self.generalSplitter.addWidget(self.generalRightSplitter)
        self.generalSplitter.setSizes([300, 700])  # 30% left, 70% right

        # Add splitters to tabs
        self.bomDataTab.layout().addWidget(self.bomSplitter)
        self.generalDataTab.layout().addWidget(self.generalSplitter)

        # Add tabs to tab widget
        self.reviewTabWidget.addTab(self.bomDataTab, "BOM Data")
        self.reviewTabWidget.addTab(self.generalDataTab, "General Data")

        # Add tab widget to main layout
        mainLayout.addWidget(self.reviewTabWidget)

        # Initialize models
        self.bomModel = ColoredPandasModel()
        self.bomTableView.setModel(self.bomModel)

        self.generalModel = ColoredPandasModel()
        self.generalTableView.setModel(self.generalModel)

        self.bomLeftModel = ColoredPandasModel()
        self.bomLeftTable.setModel(self.bomLeftModel)

        self.generalLeftModel = ColoredPandasModel()
        self.generalLeftTable.setModel(self.generalLeftModel)

        self.bomBottomModel = ColoredPandasModel()
        self.bomBottomTable.setModel(self.bomBottomModel)

        self.generalBottomModel = ColoredPandasModel()
        self.generalBottomTable.setModel(self.generalBottomModel)

    def setupTableView(self):
        """Set up the QTableView with pandas model"""
        # Create table view
        self.tableView = QTableView()
        self.tableView.setAlternatingRowColors(True)
        self.tableView.setSortingEnabled(True)
        self.tableView.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tableView.horizontalHeader().setStretchLastSection(True)
        self.tableView.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Create and set model
        self.model = ColoredPandasModel()
        self.tableView.setModel(self.model)

    def setupPdfViewer(self):
        """Set up the PDF viewer with controls"""
        # Create horizontal splitter for PDF tab
        self.pdfSplitter = QSplitter(Qt.Horizontal)

        # Left pane - Drawings list
        self.drawingsListWidget = QWidget()
        self.drawingsListWidget.setLayout(QVBoxLayout())
        self.drawingsListWidget.layout().setContentsMargins(5, 5, 5, 5)

        # Add button to fetch drawings
        self.pbFetchDrawings = QPushButton("Fetch Drawings")
        self.pbFetchDrawings.clicked.connect(self.onFetchDrawings)

        # Create table view for drawings
        self.drawingsTable = QTableView()
        self.drawingsTable.setAlternatingRowColors(True)
        self.drawingsTable.setSortingEnabled(True)
        self.drawingsTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.drawingsTable.horizontalHeader().setStretchLastSection(True)
        self.drawingsTable.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.drawingsTable.verticalHeader().setVisible(False)

        # Create and set model
        self.drawingsModel = DrawingsTableModel()
        self.drawingsTable.setModel(self.drawingsModel)

        # Connect selection changed signal
        self.drawingsTable.selectionModel().selectionChanged.connect(self.onDrawingSelectionChanged)

        # Add widgets to left pane
        self.drawingsListWidget.layout().addWidget(self.pbFetchDrawings)
        self.drawingsListWidget.layout().addWidget(self.drawingsTable)

        # Right pane - PDF viewer
        self.pdfViewerWidget = QWidget()
        self.pdfViewerWidget.setLayout(QVBoxLayout())
        self.pdfViewerWidget.layout().setContentsMargins(0, 0, 0, 0)

        # Create vertical splitter for PDF viewer and intersections text
        self.rightSplitter = QSplitter(Qt.Vertical)

        # PDF viewer controls
        self.pdfControlsWidget = QWidget()
        self.pdfControlsWidget.setLayout(QHBoxLayout())
        self.pdfControlsWidget.layout().setContentsMargins(5, 5, 5, 5)

        self.pbZoomIn = QPushButton("Zoom In")
        self.pbZoomOut = QPushButton("Zoom Out")
        self.pbZoomReset = QPushButton("Reset Zoom")
        self.pbFitToView = QPushButton("Fit to View")
        self.lblZoom = QLabel("100%")

        self.pbZoomIn.clicked.connect(self.onZoomIn)
        self.pbZoomOut.clicked.connect(self.onZoomOut)
        self.pbZoomReset.clicked.connect(self.onZoomReset)
        self.pbFitToView.clicked.connect(self.onFitToView)

        self.pdfControlsWidget.layout().addWidget(self.pbZoomIn)
        self.pdfControlsWidget.layout().addWidget(self.pbZoomOut)
        self.pdfControlsWidget.layout().addWidget(self.pbZoomReset)
        self.pdfControlsWidget.layout().addWidget(self.pbFitToView)
        self.pdfControlsWidget.layout().addWidget(self.lblZoom)
        self.pdfControlsWidget.layout().addStretch()

        # PDF viewer
        self.pdfViewer = PDFGraphicsView()

        # Upper part of right pane (PDF viewer and controls)
        self.pdfViewerContainer = QWidget()
        self.pdfViewerContainer.setLayout(QVBoxLayout())
        self.pdfViewerContainer.layout().setContentsMargins(0, 0, 0, 0)
        self.pdfViewerContainer.layout().addWidget(self.pdfControlsWidget)
        self.pdfViewerContainer.layout().addWidget(self.pdfViewer)

        # Lower part of right pane (Intersections text)
        self.intersectionsWidget = QWidget()
        self.intersectionsWidget.setLayout(QVBoxLayout())
        self.intersectionsWidget.layout().setContentsMargins(0, 0, 0, 0)

        # Add label for intersections
        self.intersectionsLabel = QLabel("Intersecting Raw Data:")
        self.intersectionsLabel.setStyleSheet("font-weight: bold;")

        # Add text edit for displaying intersections
        self.intersectionsText = QTextEdit()
        self.intersectionsText.setReadOnly(True)

        self.intersectionsWidget.layout().addWidget(self.intersectionsLabel)
        self.intersectionsWidget.layout().addWidget(self.intersectionsText)

        # Add widgets to right splitter
        self.rightSplitter.addWidget(self.pdfViewerContainer)
        self.rightSplitter.addWidget(self.intersectionsWidget)

        # Set initial sizes (70% PDF viewer, 30% intersections)
        self.rightSplitter.setSizes([700, 300])

        # Add right splitter to the PDF viewer widget
        self.pdfViewerWidget.layout().addWidget(self.rightSplitter)

        # Add widgets to splitter
        self.pdfSplitter.addWidget(self.drawingsListWidget)
        self.pdfSplitter.addWidget(self.pdfViewerWidget)

        # Set initial sizes (30% for list, 70% for viewer)
        self.pdfSplitter.setSizes([300, 700])

    def refreshProjectSources(self):
        """Refresh the project sources and update the combobox"""
        statement = ("SELECT projectId, filename from ProjectSources")
        try:
            with self.db.connect() as conn:
                df = pd.read_sql_query(statement, conn)
                self.projectSourcesDf = df

                # Update combobox
                self.cboxSource.clear()
                if not df.empty:
                    for _, row in df.iterrows():
                        fileExists = os.path.exists(getSourceRawDataPath(row['projectId'], row['filename']))
                        displayText = f"{row['filename']} (Project ID: {row['projectId']}) (Preprocessed: {fileExists})"
                        self.cboxSource.addItem(displayText, (row['projectId'], row['filename']))

                return df
        except Exception as e:
            print(f"Failed to refresh all project sources info: {e}")
            QMessageBox.warning(self, "Database Error", f"Failed to refresh project sources: {e}")
            return None

    def onSourceSelected(self, index):
        """Handle selection of a project source from the combobox"""
        if index >= 0:
            # Get the selected project ID and filename from the combobox's user data
            self.currentProjectId, self.currentFilename = self.cboxSource.itemData(index)
            print(f"Selected project ID: {self.currentProjectId}, filename: {self.currentFilename}")

            # Optionally load data automatically when selection changes
            # self.loadData()

    def loadData(self):
        """Load data for the selected project source"""
        if self.currentProjectId is None or self.currentFilename is None:
            QMessageBox.information(self, "Selection Required", "Please select a project source first.")
            return

        try:
            # Load table data
            rawFile = getSourceRawDataPath(self.currentProjectId, self.currentFilename)
            df = pd.read_feather(rawFile)

            print(df["coordinates2"].head(), df["coordinates2"].dtype, type(df["coordinates2"].iloc[0]))
            print(df["coordinates2"].head(), df["coordinates2"].dtype, type(df["color"].iloc[0]))

            # Check if DataFrame is empty using len() instead of .empty property
            if len(df) == 0:
                QMessageBox.information(self, "No Data", "No data found for the selected project source.")
            else:
                # Update the model with the new data
                self.currentDataFrame = df
                self.model.setData(df)

                # Resize columns to content - do this after model is updated
                try:
                    self.tableView.resizeColumnsToContents()
                except Exception as resize_error:
                    print(f"Warning: Could not resize columns: {resize_error}")

                # Update window title with selection info
                self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId})")

                # Populate page filter combobox
                self.availablePages = df['pdf_page'].unique().tolist()
                self.availablePages.sort()
                self.cboxPage.clear()
                for page in self.availablePages:
                    self.cboxPage.addItem(str(page))

                # Enable text filter
                self.txtFilter.setEnabled(True)

                # Load PDF document
                if self.pdfViewer.loadPDF(self.currentProjectId, self.currentFilename):
                    # If PDF loaded successfully and we have pages, display the first page
                    if self.availablePages and len(self.availablePages) > 0:
                        self.displayPDFPage(self.availablePages[0])
                else:
                    QMessageBox.warning(self, "PDF Loading Error",
                                       "Could not load the PDF document. PDF viewing will not be available.")

        except Exception as e:
            print(f"Error loading data: {e}")
            QMessageBox.warning(self, "Data Loading Error", f"Failed to load data: {e}")

    def onPageTextChanged(self, text):
        """Handle direct input of page number"""
        if not text or not hasattr(self, 'availablePages') or not self.availablePages:
            return

        try:
            # Only process if the text is a valid integer
            page_number = int(text)

            # Check if this page exists in our available pages
            if page_number in self.availablePages:
                # Find the index of this page in our sorted list
                index = self.availablePages.index(page_number)

                # Block signals to prevent recursive calls
                self.cboxPage.blockSignals(True)

                # Set the combobox to this index
                self.cboxPage.setCurrentIndex(index)

                # Unblock signals
                self.cboxPage.blockSignals(False)

                # Apply the filter directly
                self.currentPage = page_number
                self.applyFilters()

                # Update PDF view if on PDF tab
                self.displayPDFPage(page_number)

                # Update navigation buttons
                self.pbPrevPage.setEnabled(index > 0)
                self.pbNextPage.setEnabled(index < len(self.availablePages) - 1)
            # Don't show error messages during typing - it would be annoying
        except ValueError:
            # Not a valid integer, ignore
            pass

    def onPageSelected(self, index):
        """Handle page selection from the combobox"""
        if index >= 0 and self.availablePages and index < len(self.availablePages):
            self.currentPage = self.availablePages[index]
            self.applyFilters()

            # Update PDF view if on PDF tab
            self.displayPDFPage(self.currentPage)

            # Update navigation buttons
            self.pbPrevPage.setEnabled(index > 0)
            self.pbNextPage.setEnabled(index < len(self.availablePages) - 1)

    def onPreviousPage(self):
        """Navigate to the previous page"""
        currentIndex = self.cboxPage.currentIndex()
        if currentIndex > 0:
            self.cboxPage.setCurrentIndex(currentIndex - 1)

    def onNextPage(self):
        """Navigate to the next page"""
        currentIndex = self.cboxPage.currentIndex()
        if currentIndex < self.cboxPage.count() - 1:
            self.cboxPage.setCurrentIndex(currentIndex + 1)

    def resetFilters(self):
        """Reset all filters and show the full dataset"""
        self.txtFilter.clear()
        self.cboxPage.setCurrentIndex(0)  # Select first page
        if self.currentDataFrame is not None:
            self.model.setData(self.currentDataFrame)
            self.pbResetFilters.setEnabled(False)

    def setupFilterUI(self):
        # Existing filter layout code...
        filterToolbarWidget = QWidget()
        filterLayout = QHBoxLayout(filterToolbarWidget)
        filterLayout.setContentsMargins(10, 5, 10, 5)

        # Add Region Filter group
        regionGroupBox = QGroupBox("Region Filter")
        regionLayout = QGridLayout()

        # Coordinate inputs
        self.txtX0 = QLineEdit()
        self.txtY0 = QLineEdit()
        self.txtX1 = QLineEdit()
        self.txtY1 = QLineEdit()

        # Set validators for numeric input
        validator = QDoubleValidator()
        self.txtX0.setValidator(validator)
        self.txtY0.setValidator(validator)
        self.txtX1.setValidator(validator)
        self.txtY1.setValidator(validator)

        # Labels
        regionLayout.addWidget(QLabel("X0:"), 0, 0)
        regionLayout.addWidget(self.txtX0, 0, 1)
        regionLayout.addWidget(QLabel("Y0:"), 0, 2)
        regionLayout.addWidget(self.txtY0, 0, 3)
        regionLayout.addWidget(QLabel("X1:"), 1, 0)
        regionLayout.addWidget(self.txtX1, 1, 1)
        regionLayout.addWidget(QLabel("Y1:"), 1, 2)
        regionLayout.addWidget(self.txtY1, 1, 3)

        # Filter type
        self.cboxRegionFilter = QComboBox()
        self.cboxRegionFilter.addItems(["Show Intersecting", "Show Non-Intersecting"])
        regionLayout.addWidget(self.cboxRegionFilter, 2, 0, 1, 2)

        # Apply button
        self.pbApplyRegion = QPushButton("Apply Region Filter")
        self.pbApplyRegion.clicked.connect(self.applyRegionFilter)
        regionLayout.addWidget(self.pbApplyRegion, 2, 2, 1, 2)

        # Clear button
        self.pbClearRegion = QPushButton("Clear Region")
        self.pbClearRegion.clicked.connect(self.clearRegionFilter)
        regionLayout.addWidget(self.pbClearRegion, 3, 0, 1, 4)

        regionGroupBox.setLayout(regionLayout)
        filterLayout.addWidget(regionGroupBox)  # Add to existing filter layout
        self.layout().addWidget(filterToolbarWidget)

    def clearRegionFilter(self):
        """Clear the region filter inputs"""
        self.txtX0.clear()
        self.txtY0.clear()
        self.txtX1.clear()
        self.txtY1.clear()
        self.applyFilters()  # Reapply other filters

    def applyRegionFilter(self):
        """Apply the region filter to the data"""
        try:
            x0 = float(self.txtX0.text())
            y0 = float(self.txtY0.text())
            x1 = float(self.txtX1.text())
            y1 = float(self.txtY1.text())
            region = [x0, y0, x1, y1]
            self.applyFilters()  # This will now include region filtering
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Please enter valid numeric coordinates.")

    def applyFilters(self):
        """Apply all active filters to the dataset"""
        if self.currentDataFrame is None:
            return

        filtered_df = self.currentDataFrame.copy()

        # Apply existing filters (page and text)
        if self.currentPage is not None:
            filtered_df = filtered_df[filtered_df['pdf_page'] == self.currentPage]

        filter_text = self.txtFilter.text().strip()
        if filter_text:
            mask = pd.Series(False, index=filtered_df.index)
            for column in filtered_df.select_dtypes(include=['object']).columns:
                mask |= filtered_df[column].astype(str).str.contains(filter_text, case=False, na=False)
            filtered_df = filtered_df[mask]

        # Apply region filter if coordinates are provided
        try:
            if all([self.txtX0.text(), self.txtY0.text(), self.txtX1.text(), self.txtY1.text()]):
                x0 = float(self.txtX0.text())
                y0 = float(self.txtY0.text())
                x1 = float(self.txtX1.text())
                y1 = float(self.txtY1.text())
                region = [x0, y0, x1, y1]

                # Filter based on intersection
                show_intersecting = self.cboxRegionFilter.currentText() == "Show Intersecting"
                mask = filtered_df['coordinates2'].apply(
                    lambda coords: self.bboxesIntersect(
                        region,
                        ast.literal_eval(coords) if isinstance(coords, str) else coords
                    )
                )

                if not show_intersecting:
                    mask = ~mask

                filtered_df = filtered_df[mask]
        except (ValueError, TypeError) as e:
            print(f"Error applying region filter: {e}")

        # Update model with filtered data
        self.model.setData(filtered_df)

        # Enable/disable reset button
        has_filters = (self.currentPage is not None) or bool(filter_text) or \
                     all([self.txtX0.text(), self.txtY0.text(), self.txtX1.text(), self.txtY1.text()])
        self.pbResetFilters.setEnabled(has_filters)

        # Update status info
        if len(filtered_df) < len(self.currentDataFrame):
            status = f"Showing {len(filtered_df)} of {len(self.currentDataFrame)} rows"
            self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId}) - {status}")
        else:
            self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId})")

    def get_current_selection(self):
        """Return the currently selected project ID and filename"""
        return {
            "projectId": self.currentProjectId,
            "filename": self.currentFilename
        }

    def displayPDFPage(self, page_number):
        """Display the specified page in the PDF viewer"""
        if self.pdfViewer.pdfDocument is None:
            return

        # In the PDF, page numbers are 0-based, but our data uses 1-based page numbers
        pdf_page_index = page_number - 1

        # Check if the page index is valid
        if pdf_page_index >= 0 and pdf_page_index < len(self.pdfViewer.pdfDocument):
            if self.pdfViewer.displayPage(pdf_page_index):
                # Update zoom label
                self.updateZoomLabel()

    def onZoomIn(self):
        """Handle zoom in button click"""
        self.pdfViewer.zoomIn()
        self.updateZoomLabel()

    def onZoomOut(self):
        """Handle zoom out button click"""
        self.pdfViewer.zoomOut()
        self.updateZoomLabel()

    def onZoomReset(self):
        """Handle zoom reset button click"""
        self.pdfViewer.resetZoom()
        self.updateZoomLabel()

    def onFitToView(self):
        """Handle fit to view button click"""
        self.pdfViewer.fitInView()
        self.updateZoomLabel()

    def updateZoomLabel(self):
        """Update the zoom percentage label"""
        zoom_percent = int(self.pdfViewer.zoomFactor * 100)
        self.lblZoom.setText(f"Zoom: {zoom_percent}%")

    def onTabChanged(self, index):
        """Handle tab widget tab change"""
        if index == 1:  # PDF View tab
            # If we have a current page and PDF document, display it
            if self.currentPage is not None and self.pdfViewer.pdfDocument is not None:
                self.displayPDFPage(self.currentPage)

    def onFetchDrawings(self):
        """Fetch and display drawings from the current PDF page"""
        if not self.pdfViewer.pdfDocument:
            QMessageBox.warning(self, "No PDF Loaded", "Please load a PDF document first.")
            return

        if self.pdfViewer.currentPage is None:
            QMessageBox.warning(self, "No Page Selected", "Please select a page first.")
            return

        try:
            # Clear previous drawings
            self.drawingsList = []

            # Get drawings from the current page using PyMuPDF
            doc = self.pdfViewer.pdfDocument
            page_idx = self.pdfViewer.currentPage
            drawings = []

            # Get the page
            page = doc[page_idx]

            # Get drawings from the page
            page_drawings = page.get_drawings()
            if page_drawings:
                for i, drawing in enumerate(page_drawings):
                    # Add to our list with page number and index
                    drawings.append({
                        'page': page_idx,
                        'index': i,
                        'drawing': drawing,
                        'rect': drawing.get('rect', None)
                    })

            # Update the drawings model
            self.drawingsList = drawings
            self.drawingsModel.setData(drawings)

            # Resize columns to content
            self.drawingsTable.resizeColumnsToContents()

            # Show message with count
            if drawings:
                QMessageBox.information(self, "Drawings Found", f"Found {len(drawings)} drawings on page {page_idx+1}.")
            else:
                QMessageBox.information(self, "No Drawings", f"No drawings found on page {page_idx+1}.")

        except Exception as e:
            print(f"Error fetching drawings: {e}")
            QMessageBox.warning(self, "Error", f"Failed to extract drawings: {e}")

    def onDrawingSelectionChanged(self, selected, deselected):
        """Handle selection change in the drawings table"""
        # Clear any previous highlighting by refreshing the view
        self.refreshPdfView()

        indexes = selected.indexes()
        if not indexes:
            # Clear intersections text
            self.intersectionsText.clear()
            return

        # Get the selected row
        row = indexes[0].row()
        if row < 0 or row >= len(self.drawingsList):
            return

        # Get the drawing data
        drawing_data = self.drawingsList[row]

        # Navigate to the page if needed
        page_idx = drawing_data['page']
        if self.pdfViewer.currentPage != page_idx:
            self.pdfViewer.displayPage(page_idx)
            self.updateZoomLabel()

        # Highlight the drawing
        self.highlightDrawing(drawing_data)

        # Find intersecting bboxes in raw data
        self.findIntersectingBboxes(drawing_data)

    def highlightDrawing(self, drawing_data):
        """Highlight the selected drawing on the PDF page"""
        if not drawing_data or not self.pdfViewer.pixmapItem:
            return

        # Get the drawing and its rect
        drawing = drawing_data['drawing']
        rect = drawing_data.get('rect')

        if not rect or len(rect) != 4:
            print("No valid rectangle for highlighting")
            return

        try:
            # Create a copy of the current pixmap
            original_pixmap = self.pdfViewer.pixmapItem.pixmap()
            pixmap = QPixmap(original_pixmap)

            # Create a painter to draw on the pixmap
            painter = QPainter(pixmap)

            # Set up the pen for highlighting - changed to green
            pen = QPen(QColor(0, 255, 0))  # Green
            pen.setWidth(3)
            painter.setPen(pen)

            # Get the drawing type
            drawing_type = self.drawingsModel._get_drawing_type(drawing_data)

            # Draw the highlight based on the type
            x0, y0, x1, y1 = rect

            # Convert from PDF coordinates to pixmap coordinates
            # The pixmap might be at a different scale than the PDF
            scale_x = pixmap.width() / self.pdfViewer.pdfDocument[self.pdfViewer.currentPage].rect.width
            scale_y = pixmap.height() / self.pdfViewer.pdfDocument[self.pdfViewer.currentPage].rect.height

            x0 = int(x0 * scale_x)
            y0 = int(y0 * scale_y)
            x1 = int(x1 * scale_x)
            y1 = int(y1 * scale_y)

            if drawing_type == "Rectangle":
                painter.drawRect(x0, y0, x1 - x0, y1 - y0)
            elif drawing_type == "Line":
                # For lines, try to use the actual line coordinates
                items = drawing.get('items', [])
                if items and 'start' in items[0] and 'end' in items[0]:
                    start_x, start_y = items[0]['start']
                    end_x, end_y = items[0]['end']

                    # Convert coordinates
                    start_x = int(start_x * scale_x)
                    start_y = int(start_y * scale_y)
                    end_x = int(end_x * scale_x)
                    end_y = int(end_y * scale_y)

                    painter.drawLine(start_x, start_y, end_x, end_y)
                else:
                    # Fall back to drawing a line across the bounding box
                    painter.drawLine(x0, y0, x1, y1)
            else:
                # For other shapes, just draw the bounding box
                painter.drawRect(x0, y0, x1 - x0, y1 - y0)

            # End painting
            painter.end()

            # Update the pixmap in the scene
            self.pdfViewer.scene().clear()
            self.pdfViewer.pixmapItem = self.pdfViewer.scene().addPixmap(pixmap)

        except Exception as e:
            print(f"Error highlighting drawing: {e}")

    def refreshPdfView(self):
        """Refresh the PDF view to clear any highlighting"""
        if self.pdfViewer.currentPage is not None and self.pdfViewer.pdfDocument is not None:
            self.pdfViewer.displayPage(self.pdfViewer.currentPage)

    def findIntersectingBboxes(self, drawing_data):
        """Find raw data entries with bboxes that intersect with the selected drawing"""
        if not self.currentDataFrame is not None or len(self.currentDataFrame) == 0:
            self.intersectionsText.setText("No raw data available.")
            return

        # Get the drawing bbox
        drawing_rect = drawing_data.get('rect')
        if not drawing_rect or len(drawing_rect) != 4:
            self.intersectionsText.setText("No bounding box available for selected drawing.")
            return

        # Get the current page
        page_idx = drawing_data['page']

        # Filter data for current page
        page_data = self.currentDataFrame[self.currentDataFrame['pdf_page'] == page_idx + 1]  # +1 because PDF pages are 0-based but data might be 1-based

        if len(page_data) == 0:
            self.intersectionsText.setText(f"No raw data for page {page_idx + 1}.")
            return

        # Convert drawing rect to format comparable with data
        x0, y0, x1, y1 = drawing_rect
        drawing_bbox = [x0, y0, x1, y1]

        # Check for intersections
        intersections = []

        for idx, row in page_data.iterrows():
            # Check if coordinates2 exists and is not null
            if 'coordinates2' in row and row['coordinates2'] is not None:
                try:
                    # Get raw data bbox
                    raw_bbox = row['coordinates2']

                    if self.bboxesIntersect(drawing_bbox, raw_bbox):
                        # Format the intersection information
                        value = row.get('value', 'No value')
                        confidence = row.get('confidence', 'N/A')

                        intersection_info = f"Row {idx}:\n"
                        intersection_info += f"  Value: {value}\n"
                        intersection_info += f"  Confidence: {confidence}\n"
                        intersection_info += f"  BBox: {raw_bbox}\n"
                        intersection_info += f"  Overlap: {self.calculateOverlap(drawing_bbox, raw_bbox):.2f}%\n"
                        intersection_info += "-" * 50 + "\n"

                        intersections.append((self.calculateOverlap(drawing_bbox, raw_bbox), intersection_info))
                except Exception as e:
                    print(f"Error processing row {idx}: {e}")

        # Sort by overlap percentage (descending)
        intersections.sort(reverse=True)

        if intersections:
            # Display intersections
            result_text = f"Found {len(intersections)} intersecting items on page {page_idx + 1}:\n\n"
            result_text += "\n".join([info for _, info in intersections])
            self.intersectionsText.setText(result_text)
        else:
            self.intersectionsText.setText(f"No intersecting items found on page {page_idx + 1}.")

    def bboxesIntersect(self, bbox1, bbox2):
        """Check if two bounding boxes intersect"""
        # Extract coordinates
        x0_1, y0_1, x1_1, y1_1 = bbox1

        # Handle different formats of bbox2
        if isinstance(bbox2, (list, tuple)) and len(bbox2) == 4:
            x0_2, y0_2, x1_2, y1_2 = bbox2
        elif hasattr(bbox2, 'tolist'):  # numpy array
            bbox_list = bbox2.tolist()
            if len(bbox_list) == 4:
                x0_2, y0_2, x1_2, y1_2 = bbox_list
            else:
                return False
        else:
            return False

        # Check for intersection
        # Two boxes intersect if they overlap in both x and y directions
        return not (x1_1 < x0_2 or x1_2 < x0_1 or y1_1 < y0_2 or y1_2 < y0_1)

    def calculateOverlap(self, bbox1, bbox2):
        """Calculate the percentage of overlap between two bounding boxes"""
        # Extract coordinates
        x0_1, y0_1, x1_1, y1_1 = bbox1

        # Handle different formats of bbox2
        if isinstance(bbox2, (list, tuple)) and len(bbox2) == 4:
            x0_2, y0_2, x1_2, y1_2 = bbox2
        elif hasattr(bbox2, 'tolist'):  # numpy array
            bbox_list = bbox2.tolist()
            if len(bbox_list) == 4:
                x0_2, y0_2, x1_2, y1_2 = bbox_list
            else:
                return 0
        else:
            return 0

        # Calculate areas
        area1 = (x1_1 - x0_1) * (y1_1 - y0_1)
        area2 = (x1_2 - x0_2) * (y1_2 - y0_2)

        if area1 <= 0 or area2 <= 0:
            return 0

        # Calculate intersection area
        x_overlap = max(0, min(x1_1, x1_2) - max(x0_1, x0_2))
        y_overlap = max(0, min(y1_1, y1_2) - max(y0_1, y0_2))
        intersection_area = x_overlap * y_overlap

        # Calculate percentage of overlap relative to the smaller area
        smaller_area = min(area1, area2)
        return (intersection_area / smaller_area) * 100 if smaller_area > 0 else 0

    def onGenerateReport(self):
        """Generate a report of blue drawings and their intersections with raw data"""
        if not self.pdfViewer.pdfDocument:
            QMessageBox.warning(self, "No PDF Loaded", "Please load a PDF document first.")
            return

        if not self.currentDataFrame is not None or len(self.currentDataFrame) == 0:
            QMessageBox.warning(self, "No Data", "Please load raw data first.")
            return

        # Ask for save file location
        default_path = os.path.join("debug/analysis_report.xlsx")
        default_dir = os.path.dirname(default_path)

        # Create directory if it doesn't exist
        os.makedirs(default_dir, exist_ok=True)

        save_path, _ = QFileDialog.getSaveFileName(
            self, "Save Report", default_path, "Excel Files (*.xlsx)"
        )

        if not save_path:
            return  # User cancelled

        # Show progress dialog
        progress = QProgressDialog("Generating report...", "Cancel", 0, self.pdfViewer.pdfDocument.page_count, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)

        try:
            # Create a pandas DataFrame to store results
            results = []

            # Process each page
            for page_idx in range(self.pdfViewer.pdfDocument.page_count):
                if progress.wasCanceled():
                    break

                progress.setValue(page_idx)
                progress.setLabelText(f"Processing page {page_idx+1} of {self.pdfViewer.pdfDocument.page_count}...")
                QApplication.processEvents()

                # Get drawings for this page
                page = self.pdfViewer.pdfDocument[page_idx]
                drawings = page.get_drawings()

                # Filter raw data for this page
                page_data = self.currentDataFrame[self.currentDataFrame['pdf_page'] == page_idx + 1]

                # Process each drawing
                for i, drawing in enumerate(drawings):
                    # Check if drawing is blue
                    is_blue = self._is_blue_drawing(drawing)

                    if is_blue:
                        # Get drawing rect
                        rect = drawing.get('rect')
                        if rect and len(rect) == 4:
                            drawing_bbox = rect

                            # Find intersections
                            for idx, row in page_data.iterrows():
                                if 'coordinates2' in row and row['coordinates2'] is not None:
                                    try:
                                        raw_bbox = row['coordinates2']

                                        if self.bboxesIntersect(drawing_bbox, raw_bbox):
                                            overlap = self.calculateOverlap(drawing_bbox, raw_bbox)

                                            # Add to results
                                            result = {
                                                'pdf_page': page_idx + 1,
                                                'drawing_id': i + 1,
                                                'drawing_type': self._get_drawing_type_simple(drawing),
                                                'drawing_bbox': [drawing_bbox[0], drawing_bbox[1], drawing_bbox[2], drawing_bbox[3]],
                                                'raw_data_row': idx,
                                                'raw_data_value': row.get('value', 'No value'),
                                                'raw_data_bbox': str(raw_bbox),
                                                'overlap_percent': overlap,
                                                'confidence': row.get('confidence', 'N/A')
                                            }
                                            results.append(result)
                                    except Exception as e:
                                        print(f"Error processing row {idx} on page {page_idx+1}: {e}")

            progress.setValue(self.pdfViewer.pdfDocument.page_count)

            # Create DataFrame from results
            if results:
                df = pd.DataFrame(results)

                # Convert column names to snake_case
                df.columns = [col.lower().replace(' ', '_') for col in df.columns]

                # Sort by page and overlap
                df = df.sort_values(['pdf_page', 'overlap_percent'], ascending=[True, False])

                # Save to Excel
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='revisions', index=False)

                    # Create summary sheet
                    summary = df.groupby('pdf_page').agg({
                        'drawing_id': 'nunique',
                        'raw_data_row': 'nunique',
                        'overlap_percent': ['mean', 'min', 'max']
                    })
                    summary.columns = ['_'.join(col).strip() for col in summary.columns.values]
                    summary = summary.reset_index()
                    summary.columns = [col.lower().replace(' ', '_') for col in summary.columns]
                    summary.to_excel(writer, sheet_name='summary', index=False)

                    # Create revision_summary sheet with page-level intersection info
                    # Get all pages from the PDF
                    all_pages = list(range(1, self.pdfViewer.pdfDocument.page_count + 1))

                    # Get pages with intersections
                    pages_with_intersections = df['pdf_page'].unique().tolist()

                    # Create data for revision_summary
                    revision_data = []
                    for page in all_pages:
                        has_intersections = page in pages_with_intersections
                        intersection_count = 0
                        if has_intersections:
                            intersection_count = len(df[df['pdf_page'] == page])

                        revision_data.append({
                            'page': page,
                            'has_intersections': 'Yes' if has_intersections else 'No',
                            'intersection_count': intersection_count
                        })

                    # Create DataFrame and save to Excel
                    revision_summary_df = pd.DataFrame(revision_data)
                    revision_summary_df.columns = [col.lower() for col in revision_summary_df.columns]
                    revision_summary_df.to_excel(writer, sheet_name='revision_summary', index=False)

                QMessageBox.information(self, "Report Generated", f"Report saved to {save_path}")
            else:
                QMessageBox.information(self, "No Results", "No blue drawings with intersections found.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate report: {e}")
        finally:
            progress.close()

    def _is_blue_drawing(self, drawing):
        """Check if a drawing is blue"""
        # Get color as hex
        color_hex = self._get_drawing_color_simple(drawing)

        # Check if it's a blue-ish color (more blue than red and green)
        if color_hex.startswith('#'):
            try:
                # Parse hex color
                r = int(color_hex[1:3], 16)
                g = int(color_hex[3:5], 16)
                b = int(color_hex[5:7], 16)

                # Check if blue component is dominant
                return b > r * 1.5 and b > g * 1.5 and b > 100
            except:
                pass

        # Check for blue in the type
        drawing_type = self._get_drawing_type_simple(drawing)
        return "blue" in drawing_type.lower()

    def _get_drawing_color_simple(self, drawing):
        """Simplified version of _get_drawing_color that doesn't print debug info"""
        # Try to get stroke color
        stroke_color = drawing.get('stroke_color')
        if stroke_color:
            if isinstance(stroke_color, (list, tuple)) and len(stroke_color) >= 3:
                r, g, b = stroke_color[:3]
                # Convert from 0-1 range to 0-255 range
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(stroke_color)

        # Try to get color from items
        items = drawing.get('items', [])
        if items:
            # Check all items for any color-related attributes
            for item in items:
                if not isinstance(item, dict):
                    continue

                # Check for 'color' attribute
                if 'color' in item:
                    color = item['color']
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

                # Check for other color attributes
                for attr in ['colors', 'stroke_color', 'fill_color']:
                    if attr in item:
                        color = item[attr]
                        if isinstance(color, (list, tuple)) and len(color) >= 3:
                            r, g, b = color[:3]
                            r = min(255, max(0, int(r * 255)))
                            g = min(255, max(0, int(g * 255)))
                            b = min(255, max(0, int(b * 255)))
                            return f"#{r:02x}{g:02x}{b:02x}"
                        return str(color)

        # Check for fill color as a fallback
        fill_color = drawing.get('fill_color')
        if fill_color:
            if isinstance(fill_color, (list, tuple)) and len(fill_color) >= 3:
                r, g, b = fill_color[:3]
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(fill_color)

        # Check for any other color-related attributes
        for key in drawing.keys():
            if 'color' in key.lower():
                color_value = drawing[key]
                if isinstance(color_value, (list, tuple)) and len(color_value) >= 3:
                    r, g, b = color_value[:3]
                    r = min(255, max(0, int(r * 255)))
                    g = min(255, max(0, int(g * 255)))
                    b = min(255, max(0, int(b * 255)))
                    return f"#{r:02x}{g:02x}{b:02x}"
                return str(color_value)

        # If we still don't have a color, use a default based on drawing type
        drawing_type = self._get_drawing_type_simple(drawing)
        if drawing_type == "Line":
            return "#000000"  # Black for lines
        elif drawing_type == "Rectangle":
            return "#0000FF"  # Blue for rectangles
        elif drawing_type == "Curve":
            return "#FF0000"  # Red for curves
        elif drawing_type == "Quad":
            return "#00FF00"  # Green for quads

        return "Unknown"

    def _get_drawing_type_simple(self, drawing):
        """Simplified version of _get_drawing_type that doesn't use self"""
        if 'type' in drawing:
            return drawing['type']

        # Try to determine type from items
        items = drawing.get('items', [])
        if not items:
            return "Unknown"

        # Check first item's type
        item = items[0]
        if isinstance(item, dict):
            if item.get('type') == 'l':
                return "Line"
            elif item.get('type') == 're':
                return "Rectangle"
            elif item.get('type') == 'qu':
                return "Quad"
            elif item.get('type') == 'c':
                return "Curve"
            else:
                return item.get('type', "Unknown")
        return "Unknown"

    def onReviewSearch(self):
        """Handle search in the Initialize Review tab"""
        data_type = self.cboxDataType.currentText()
        search_term = self.txtReviewInput.text().strip()

        if not search_term:
            QMessageBox.warning(self, "Input Required", "Please enter a search term.")
            return

        if not self.currentProjectId:
            QMessageBox.warning(self, "No Project Selected", "Please select a project source first.")
            return

        try:
            # Show loading cursor
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Get data path
            raw_file = getSourceRawDataPath(self.currentProjectId, self.currentFilename)

            if not os.path.exists(raw_file):
                QMessageBox.warning(self, "File Not Found", f"Raw data file not found: {raw_file}")
                QApplication.restoreOverrideCursor()
                return

            # Load data
            df = pd.read_feather(raw_file)

            # Filter data based on type
            if data_type == "bom_data":
                # Filter for BOM data (customize this based on your data structure)
                filtered_df = df[df['type'].str.contains('bom', case=False, na=False)]

                # Apply search term filter
                if search_term:
                    # Search across all string columns
                    mask = pd.Series(False, index=filtered_df.index)
                    for col in filtered_df.select_dtypes(include=['object']).columns:
                        mask = mask | filtered_df[col].astype(str).str.contains(search_term, case=False, na=False)
                    filtered_df = filtered_df[mask]

                # Create a sample dataset for left and bottom tables (for demonstration)
                if not filtered_df.empty:
                    # For left table - create a summary by type
                    left_df = filtered_df.groupby('type').size().reset_index()
                    left_df.columns = ['Type', 'Count']

                    # For bottom table - show a subset of columns for selected rows
                    # This is just a placeholder - customize based on your needs
                    bottom_df = filtered_df.head(10)[['type', 'value', 'confidence']].copy()
                    if 'page' in filtered_df.columns:
                        bottom_df['page'] = filtered_df.head(10)['page']
                else:
                    left_df = pd.DataFrame(columns=['Type', 'Count'])
                    bottom_df = pd.DataFrame(columns=['type', 'value', 'confidence'])

                # Update BOM tables
                self.bomModel.setData(filtered_df)
                self.bomTableView.resizeColumnsToContents()

                self.bomLeftModel.setData(left_df)
                self.bomLeftTable.resizeColumnsToContents()

                self.bomBottomModel.setData(bottom_df)
                self.bomBottomTable.resizeColumnsToContents()

                self.reviewTabWidget.setCurrentIndex(0)  # Switch to BOM tab

            elif data_type == "general_data":
                # Filter for general data (customize this based on your data structure)
                filtered_df = df[~df['type'].str.contains('bom', case=False, na=False)]

                # Apply search term filter
                if search_term:
                    # Search across all string columns
                    mask = pd.Series(False, index=filtered_df.index)
                    for col in filtered_df.select_dtypes(include=['object']).columns:
                        mask = mask | filtered_df[col].astype(str).str.contains(search_term, case=False, na=False)
                    filtered_df = filtered_df[mask]

                # Create a sample dataset for left and bottom tables (for demonstration)
                if not filtered_df.empty:
                    # For left table - create a summary by type
                    left_df = filtered_df.groupby('type').size().reset_index()
                    left_df.columns = ['Type', 'Count']

                    # For bottom table - show a subset of columns for selected rows
                    # This is just a placeholder - customize based on your needs
                    bottom_df = filtered_df.head(10)[['type', 'value', 'confidence']].copy()
                    if 'page' in filtered_df.columns:
                        bottom_df['page'] = filtered_df.head(10)['page']
                else:
                    left_df = pd.DataFrame(columns=['Type', 'Count'])
                    bottom_df = pd.DataFrame(columns=['type', 'value', 'confidence'])

                # Update General tables
                self.generalModel.setData(filtered_df)
                self.generalTableView.resizeColumnsToContents()

                self.generalLeftModel.setData(left_df)
                self.generalLeftTable.resizeColumnsToContents()

                self.generalBottomModel.setData(bottom_df)
                self.generalBottomTable.resizeColumnsToContents()

                self.reviewTabWidget.setCurrentIndex(1)  # Switch to General tab

            # Show result message
            QMessageBox.information(self, "Search Results", f"Found {len(filtered_df)} matching records.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to search data: {e}")
            print(f"Error in onReviewSearch: {e}")
            import traceback
            traceback.print_exc()
        finally:
            QApplication.restoreOverrideCursor()

    def onBrowseBomData(self):
        """Browse for BOM data Excel file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select BOM Data Excel File", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.txtBomDataFile.setText(file_path)

    def onBrowseGeneralData(self):
        """Browse for General data Excel file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select General Data Excel File", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.txtGeneralDataFile.setText(file_path)

    def setupExportUI(self):
        """Setup the export UI components"""
        exportGroup = QGroupBox("Export Options")
        exportLayout = QVBoxLayout(exportGroup)

        # Export type selection
        self.cboxExportType = QComboBox()
        self.cboxExportType.addItems([
            "Export Filtered Data Only",
            "Export Unfiltered Data Only",
            "Export All Raw Data"
        ])
        exportLayout.addWidget(self.cboxExportType)

        # Export directory selection
        dirWidget = QWidget()
        dirLayout = QHBoxLayout(dirWidget)
        dirLayout.setContentsMargins(0, 0, 0, 0)

        self.leExportPath = QLineEdit()
        self.leExportPath.setPlaceholderText("Select export location...")
        dirLayout.addWidget(self.leExportPath)

        btnBrowse = QPushButton("Browse...")
        btnBrowse.clicked.connect(self.onBrowseExportLocation)
        dirLayout.addWidget(btnBrowse)

        exportLayout.addWidget(dirWidget)

        # Export button
        btnExport = QPushButton("Export")
        btnExport.clicked.connect(self.onExport)
        exportLayout.addWidget(btnExport)

        # Add to main layout (assuming there's a main layout)
        self.layout().addWidget(exportGroup)

    def onBrowseExportLocation(self):
        """Handle browse button click for export location"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Select Export Location",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*.*)"
        )
        if filename:
            self.leExportPath.setText(filename)

    def onExport(self):
        """Handle export button click"""
        if not self.currentDataFrame is not None:
            QMessageBox.warning(self, "No Data", "No data available to export.")
            return

        export_path = self.leExportPath.text().strip()
        if not export_path:
            QMessageBox.warning(self, "Export Path Required", "Please select an export location.")
            return

        try:
            # Determine which data to export based on selection
            export_type = self.cboxExportType.currentText()

            if export_type == "Export Filtered Data Only":
                # Get currently filtered data from the model
                data_to_export = self.model._data
            elif export_type == "Export Unfiltered Data Only":
                # Get unfiltered data while respecting current page filter
                if self.currentDataFrame is None:
                    return

                # Start with the full dataset
                data_to_export = self.currentDataFrame.copy()

                # Apply only the page filter if a page is selected
                if self.currentPage is not None:
                    data_to_export = data_to_export[data_to_export['pdf_page'] == self.currentPage]

            else:  # "Export All Raw Data"
                data_to_export = self.currentDataFrame

            # Export based on file extension
            if export_path.lower().endswith('.xlsx'):
                data_to_export.to_excel(export_path, index=False)
            elif export_path.lower().endswith('.csv'):
                data_to_export.to_csv(export_path, index=False)
            else:
                # Default to Excel if no extension specified
                export_path = f"{export_path}.xlsx"
                data_to_export.to_excel(export_path, index=False)

            # Show success message with option to open file/folder
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("Export Successful")
            msg.setText("Data exported successfully!")

            folder_path = os.path.dirname(export_path)
            file_name = os.path.basename(export_path)

            detailed_text = f"Exported to:\n{export_path}\n\n"
            detailed_text += f"Rows exported: {len(data_to_export)}"
            if self.currentPage is not None:
                detailed_text += f"\nPage: {self.currentPage}"
            msg.setDetailedText(detailed_text)

            # Add buttons to open file or containing folder
            msg.addButton("Open File", QMessageBox.AcceptRole)
            msg.addButton("Open Folder", QMessageBox.ActionRole)
            msg.addButton("Close", QMessageBox.RejectRole)

            result = msg.exec_()

            if result == 0:  # Open File
                QDesktopServices.openUrl(QUrl.fromLocalFile(export_path))
            elif result == 1:  # Open Folder
                QDesktopServices.openUrl(QUrl.fromLocalFile(folder_path))

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export data: {str(e)}")

    def reorderAndSortDataFrame(self, df, data_type="bom"):
        """
        Reorder columns to move sys_path, pdf_page, and pdf_id to the beginning
        and sort the DataFrame based on the specified data type.
        """
        if df.empty:
            return df

        # Create a copy to avoid modifying the original
        df = df.copy()

        # List of columns to move to the beginning
        priority_columns = ["sys_path", "pdf_page", "pdf_id"]

        # Reorder columns: priority columns first, then all other columns
        existing_priority_cols = [col for col in priority_columns if col in df.columns]
        other_cols = [col for col in df.columns if col not in priority_columns]
        new_column_order = existing_priority_cols + other_cols

        # Apply new column order if any priority columns exist
        if existing_priority_cols:
            df = df[new_column_order]

        # Sort based on data type
        if data_type.lower() == "bom":
            # Sort by sys_path, pdf_page, pos for BOM data
            # sort_columns = [col for col in ["sys_path", "pdf_page", "pos"] if col in df.columns]
            sort_columns = [col for col in ["sys_path", "pdf_page"] if col in df.columns]
            if sort_columns:
                df = df.sort_values(by=sort_columns)
        else:
            # Sort by sys_path, pdf_page for general data
            sort_columns = [col for col in ["sys_path", "pdf_page"] if col in df.columns]
            if sort_columns:
                df = df.sort_values(by=sort_columns)

        return df

    def displayPdfInReviewTab(self, page_number):
        """
        Display a PDF page in the review tab's PDF viewer.
        Uses the combobox loaded filename if available and avoids reloading if the page is already loaded.
        """
        filename = self.currentFilename
        if not filename or not page_number:
            QMessageBox.warning(self, "Missing Data", "Cannot display PDF page: Load data from correct project source. Note - sys_path of selected row is ignored.")
            return False
        try:
            # Determine which tab is active
            current_tab = self.reviewTabWidget.currentIndex()

            # Load and display PDF
            if current_tab == 0:  # BOM tab
                # Check if we need to load a new PDF or just change the page
                if not hasattr(self.bomPdfViewer, 'currentFilename') or \
                    self.bomPdfViewer.currentFilename != filename:
                    self.bomPdfViewer.loadPDF(self.currentProjectId, filename)
                # Display the page (this will skip if already on this page)
                self.bomPdfViewer.displayPage(int(page_number) - 1)
            else:  # General tab
                # Check if we need to load a new PDF or just change the page
                if not hasattr(self.generalPdfViewer, 'currentFilename') or \
                    self.generalPdfViewer.currentFilename != filename:
                    self.generalPdfViewer.loadPDF(self.currentProjectId, filename)
                # Display the page (this will skip if already on this page)
                self.generalPdfViewer.displayPage(int(page_number) - 1)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error Displaying PDF",
                f"Failed to display PDF: {e}\n\n"
                "Please make sure you have loaded the raw data from the correct source. And that project source filename exists."
            )
            print(f"Error displaying PDF: {e}")
            import traceback
            traceback.print_exc()
            return False

    def onBomTableSelectionChanged(self, selected, deselected):
        """
        Handle selection change in the BOM table view.
        Display the corresponding PDF page when a row is selected.
        """
        indexes = selected.indexes()
        if not indexes:
            return

        # Get the selected row
        row = indexes[0].row()

        # Get sys_path and pdf_page from the model
        sys_path_idx = self.bomModel._data.columns.get_loc("sys_path") if "sys_path" in self.bomModel._data.columns else None
        pdf_page_idx = self.bomModel._data.columns.get_loc("pdf_page") if "pdf_page" in self.bomModel._data.columns else None

        if sys_path_idx is not None and pdf_page_idx is not None:
            sys_path = self.bomModel._data.iloc[row, sys_path_idx]
            pdf_page = self.bomModel._data.iloc[row, pdf_page_idx]

            # Display the PDF
            self.displayPdfInReviewTab(pdf_page)

    def onGeneralTableSelectionChanged(self, selected, deselected):
        """
        Handle selection change in the General table view.
        Display the corresponding PDF page when a row is selected.
        """
        indexes = selected.indexes()
        if not indexes:
            return

        # Get the selected row
        row = indexes[0].row()

        # Get sys_path and pdf_page from the model
        sys_path_idx = self.generalModel._data.columns.get_loc("sys_path") if "sys_path" in self.generalModel._data.columns else None
        pdf_page_idx = self.generalModel._data.columns.get_loc("pdf_page") if "pdf_page" in self.generalModel._data.columns else None

        if sys_path_idx is not None and pdf_page_idx is not None:
            sys_path = self.generalModel._data.iloc[row, sys_path_idx]
            pdf_page = self.generalModel._data.iloc[row, pdf_page_idx]

            # Display the PDF
            self.displayPdfInReviewTab(pdf_page)

    def onLoadBomData(self):
        """Load BOM data from Excel file"""
        file_path = self.txtBomDataFile.text().strip()
        if not file_path:
            QMessageBox.warning(self, "No File Selected", "Please select a BOM data Excel file.")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "File Not Found", f"File not found: {file_path}")
            return

        try:
            # Show loading cursor
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Load Excel file
            df = pd.read_excel(file_path)

            if df.empty:
                QMessageBox.warning(self, "Empty File", "The selected Excel file is empty.")
                QApplication.restoreOverrideCursor()
                return

            # Rename columns from display names to internal field names
            df = self.renameColumnsFromDisplayToInternal(df)

            # Reorder columns and sort data
            df = self.reorderAndSortDataFrame(df, "bom")

            # Update BOM tables
            self.bomModel.setData(df)
            self.bomTableView.resizeColumnsToContents()

            # Connect selection changed signal after model data is set
            try:
                # Disconnect existing connection if any to avoid multiple connections
                try:
                    self.bomTableView.selectionModel().selectionChanged.disconnect(self.onBomTableSelectionChanged)
                except:
                    pass
                # Connect the signal
                self.bomTableView.selectionModel().selectionChanged.connect(self.onBomTableSelectionChanged)
                print("Connected BOM table selection signal")
            except Exception as e:
                print(f"Error connecting BOM selection signal: {e}")

            # Create summary for left table
            try:
                # Try to group by first column (should be sys_path now)
                left_df = df.iloc[:, 0].value_counts().reset_index()
                left_df.columns = ['Value', 'Count']
                self.bomLeftModel.setData(left_df)
                self.bomLeftTable.resizeColumnsToContents()
            except Exception as e:
                print(f"Error creating summary: {e}")
                # Fallback to empty dataframe
                self.bomLeftModel.setData(pd.DataFrame())

            # Create sample for bottom table
            try:
                # Use first 10 rows and first 5 columns
                bottom_df = df.head(10).iloc[:, :5].copy()
                self.bomBottomModel.setData(bottom_df)
                self.bomBottomTable.resizeColumnsToContents()
            except Exception as e:
                print(f"Error creating sample: {e}")
                # Fallback to empty dataframe
                self.bomBottomModel.setData(pd.DataFrame())

            # Switch to BOM tab
            self.reviewTabWidget.setCurrentIndex(0)

            # Show success message
            QMessageBox.information(self, "Data Loaded", f"Successfully loaded {len(df)} rows of BOM data.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load BOM data: {e}")
            print(f"Error loading BOM data: {e}")
            import traceback
            traceback.print_exc()
        finally:
            QApplication.restoreOverrideCursor()

    def onLoadGeneralData(self):
        """Load General data from Excel file"""
        file_path = self.txtGeneralDataFile.text().strip()
        if not file_path:
            QMessageBox.warning(self, "No File Selected", "Please select a General data Excel file.")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "File Not Found", f"File not found: {file_path}")
            return

        try:
            # Show loading cursor
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Load Excel file
            df = pd.read_excel(file_path)

            if df.empty:
                QMessageBox.warning(self, "Empty File", "The selected Excel file is empty.")
                QApplication.restoreOverrideCursor()
                return

            # Rename columns from display names to internal field names
            df = self.renameColumnsFromDisplayToInternal(df)

            # Reorder columns and sort data
            df = self.reorderAndSortDataFrame(df, "general")

            # Update General tables
            self.generalModel.setData(df)
            self.generalTableView.resizeColumnsToContents()

            # Connect selection changed signal after model data is set
            try:
                # Disconnect existing connection if any to avoid multiple connections
                try:
                    self.generalTableView.selectionModel().selectionChanged.disconnect(self.onGeneralTableSelectionChanged)
                except:
                    pass
                # Connect the signal
                self.generalTableView.selectionModel().selectionChanged.connect(self.onGeneralTableSelectionChanged)
                print("Connected General table selection signal")
            except Exception as e:
                print(f"Error connecting General selection signal: {e}")

            # Create summary for left table
            try:
                # Try to group by first column (should be sys_path now)
                left_df = df.iloc[:, 0].value_counts().reset_index()
                left_df.columns = ['Value', 'Count']
                self.generalLeftModel.setData(left_df)
                self.generalLeftTable.resizeColumnsToContents()
            except Exception as e:
                print(f"Error creating summary: {e}")
                # Fallback to empty dataframe
                self.generalLeftModel.setData(pd.DataFrame())

            # Create sample for bottom table
            try:
                # Use first 10 rows and first 5 columns
                bottom_df = df.head(10).iloc[:, :5].copy()
                self.generalBottomModel.setData(bottom_df)
                self.generalBottomTable.resizeColumnsToContents()
            except Exception as e:
                print(f"Error creating sample: {e}")
                # Fallback to empty dataframe
                self.generalBottomModel.setData(pd.DataFrame())

            # Switch to General tab
            self.reviewTabWidget.setCurrentIndex(1)

            # Show success message
            QMessageBox.information(self, "Data Loaded", f"Successfully loaded {len(df)} rows of General data.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load General data: {e}")
            print(f"Error loading General data: {e}")
            import traceback
            traceback.print_exc()
        finally:
            QApplication.restoreOverrideCursor()

    def renameColumnsFromDisplayToInternal(self, df):
        """
        Rename DataFrame columns from display names to internal field names if they exist in the field map.
        This allows for loading Excel files with display names and converting them to internal names.
        """
        if df.empty:
            return df

        # Get the field map
        field_map = getSimpleFieldMap()

        # Create a mapping from display names to internal field names
        display_to_internal = {}
        for internal_name, field_type in field_map.items():
            display_name = field_type.get('display', internal_name)
            if display_name and display_name != internal_name:
                display_to_internal[display_name] = internal_name

        # Rename columns if they match display names
        rename_dict = {}
        for col in df.columns:
            if col in display_to_internal:
                rename_dict[col] = display_to_internal[col]

        # Apply renaming if any matches found
        if rename_dict:
            df = df.rename(columns=rename_dict)
            print(f"Renamed columns: {rename_dict}")

        return df

    def onAnchorPositionChanged(self, state):
        """
        Toggle position anchoring for all PDF viewers
        """
        maintain_position = state == Qt.Checked

        # Update all PDF viewers
        if hasattr(self, 'pdfViewer'):
            self.pdfViewer.maintainPosition = maintain_position

        if hasattr(self, 'bomPdfViewer'):
            self.bomPdfViewer.maintainPosition = maintain_position

        if hasattr(self, 'generalPdfViewer'):
            self.generalPdfViewer.maintainPosition = maintain_position

    def onBrowseReportFile(self):
        """Browse for report Excel file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Report Excel File", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.txtReportFile.setText(file_path)

    def onLoadReportFile(self):
        """Load report from Excel file"""
        file_path = self.txtReportFile.text().strip()
        if not file_path:
            QMessageBox.warning(self, "No File Selected", "Please select a report Excel file.")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "File Not Found", f"File not found: {file_path}")
            return

        try:
            # Show loading cursor
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # Load Excel file
            self.reportDf = pd.read_excel(file_path)

            if self.reportDf.empty:
                QMessageBox.warning(self, "Empty File", "The selected Excel file is empty.")
            else:
                QMessageBox.information(self, "Report Loaded", f"Successfully loaded {len(self.reportDf)} rows of report data.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load report data: {e}")
            print(f"Error loading report data: {e}")
            import traceback
            traceback.print_exc()
        finally:
            QApplication.restoreOverrideCursor()

    def onBrowseRoiPayloadFile(self):
        """Browse for ROI payload file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select ROI Payload File", "", "All Files (*.*)"
        )
        if file_path:
            self.txtRoiPayloadFile.setText(file_path)

    def onLoadRoiPayloadFile(self):
        """Load ROI payload file"""
        file_path = self.txtRoiPayloadFile.text().strip()
        if not file_path:
            QMessageBox.warning(self, "No File Selected", "Please select an ROI payload file.")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "File Not Found", f"File not found: {file_path}")
            return

        self.roiPayload = convert_roi_payload(file_path, force_extract=True)

    def onAnalyzeBom(self):
        """Analyze BOM data"""
        # Placeholder for BOM analysis functionality
        print("Analyze BOM functionality will be implemented here")

        if self.bomModel._data.empty:
            QMessageBox.warning(self, "No BOM Data", "Please load BOM data first.")
            return

        if self.reportDf.empty:
            QMessageBox.warning(self, "No Report Data", "Please load report data first.")
            return

        if not self.roiPayload:
            QMessageBox.warning(self, "No ROI Payload", "Please load ROI payload first.")
            return

        groups = self.reportDf.groupby('pdf_page')

        doc = fitz.open(self.currentFilename)

        def get_bom_coords(pdf_page):
            rois = self.roiPayload["groupRois"][self.roiPayload["pageToGroup"][pdf_page]]
            for roi in rois:
                if roi["columnName"] == "BOM":
                    return roi["tableCoordinates"]
            return None

        bomDf = self.bomModel._data.copy()  # Create a copy to avoid modifying the original

        # Ensure _action_proposed column exists
        if '_action_proposed' not in bomDf.columns:
            bomDf['_action_proposed'] = None
        if '_staged_action' not in bomDf.columns:
            bomDf['_staged_action'] = None

        # columns to check for revisions
        bomColumns = ["material_description", "size", "pos", "quantity", "item", "status"]
        bomColumns = [b for b in bomColumns if b in bomDf.columns]

        for pdf_page, group in groups:

            bomPageDf = bomDf[bomDf["pdf_page"] == pdf_page]

            page = doc[pdf_page - 1]
            page_width = page.rect.width
            page_height = page.rect.height

            bomCoords = get_bom_coords(pdf_page)
            if not bomCoords:
                continue

            bom_x0 = bomCoords["relativeX0"] * page_width
            bom_y0 = bomCoords["relativeY0"] * page_height
            bom_x1 = bomCoords["relativeX1"] * page_width
            bom_y1 = bomCoords["relativeY1"] * page_height
            bom_bbox = [bom_x0, bom_y0, bom_x1, bom_y1]

            has_intersects = False

            if bomPageDf["has_revision"].any():
                # Get all indices from bomPageDf
                revision_indices = bomPageDf.index.tolist()
                # Update _action_proposed for all these indices
                bomDf.loc[revision_indices, "_action_proposed"] = "revision"

            row_removed = set()

            for drawing_bbox, bbox_group in group.groupby('drawing_bbox'):

                # Convert drawing_bbox from string to list if needed
                if isinstance(drawing_bbox, str):
                    drawing_bbox = eval(drawing_bbox)  # Be careful with eval, only use if you're sure the input is safe

                # Check if drawing_bbox intersects with bom_bbox. i.e. filter within bom roi
                if not self.bboxesIntersect(drawing_bbox, bom_bbox):
                    continue  # Skip this drawing_bbox if it doesn't intersect with bom_bbox

                has_intersects = True
                # Process this drawing_bbox and its group
                intersecting_values = bbox_group['raw_data_value'].unique().tolist()
                print(pdf_page)
                print(drawing_bbox)
                print(intersecting_values)

                bomPageDf = bomPageDf[bomPageDf['pdf_page'] == pdf_page]

                for index, row in bomPageDf.iterrows():

                    if index in row_removed:
                        continue

                    if row["has_revision"] and not bomDf.loc[index, "_action_proposed"]:
                        bomDf.loc[index, "_action_proposed"] = "revision"

                    for value in intersecting_values:

                        for b in bomColumns:
                            if len(value) < 5 and b == "material_description":
                                continue
                            if b == "material_description" and value in str(row[b]):
                                action = "remove_row"
                                bomDf.loc[index, "_action_proposed"] = action
                                bomDf.loc[index, "_staged_action"] = action
                                # Row removed. No need to check further columns in row
                                row_removed.add(index)
                                break
                            # elif value == str(row[b]):
                            #     print(value, str(row[b]))
                            #     action = "cell_update"
                            #     bomDf.loc[index, "_action_proposed"] = action
                            #     bomDf.loc[index, "_staged_action"] = action

                        if index in row_removed:
                            break

                    if not bomDf.loc[index, "_action_proposed"]:
                        bomDf.loc[index, "_action_proposed"] = "review"
                        bomDf.loc[index, "_staged_action"] = "review"

        bomDf.to_excel("debug/bom_proposed.xlsx", index=False)

        # Create debug directory if it doesn't exist
        os.makedirs("debug", exist_ok=True)

        # Save with color highlighting based on _action_proposed
        excel_file = "debug/bom_proposed.xlsx"

        # First save the dataframe to Excel
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            bomDf.to_excel(writer, index=False, sheet_name='BOM Analysis')

            # Access the openpyxl workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['BOM Analysis']

            # Add autofilter to the worksheet
            worksheet.auto_filter.ref = f"A1:{openpyxl.utils.get_column_letter(len(bomDf.columns))}{len(bomDf) + 1}"

            # Define fill colors
            red_fill = openpyxl.styles.PatternFill(start_color='FFFF0000', end_color='FFFF0000', fill_type='solid')
            yellow_fill = openpyxl.styles.PatternFill(start_color='FFFFFF00', end_color='FFFFFF00', fill_type='solid')
            blue_fill = openpyxl.styles.PatternFill(start_color='FF99CCFF', end_color='FF99CCFF', fill_type='solid')
            purple_fill = openpyxl.styles.PatternFill(start_color='99FFCCFF', end_color='99FFCCFF', fill_type='solid')

            # Process rows by action type (much faster than row-by-row)
            # For rows with 'remove_row' action
            remove_indices = bomDf.index[bomDf['_action_proposed'] == 'remove_row'].tolist()
            for idx in remove_indices:
                # Excel rows are 1-based and we need to add 1 more for the header row
                row_idx = bomDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(bomDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = red_fill

            # For rows with 'review' action
            review_indices = bomDf.index[bomDf['_action_proposed'] == 'review'].tolist()
            for idx in review_indices:
                row_idx = bomDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(bomDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = yellow_fill

            # For rows with 'revision' action
            revision_indices = bomDf.index[bomDf['_action_proposed'] == 'revision'].tolist()
            for idx in revision_indices:
                row_idx = bomDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(bomDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = blue_fill

            # For rows with 'revision' action
            revision_indices = bomDf.index[bomDf['_action_proposed'] == 'cell_update'].tolist()
            for idx in revision_indices:
                row_idx = bomDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(bomDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = purple_fill

        #
        # Display the analyzed BOM data in the BOM tab with color highlighting
        # Define color map for the table view
        color_map = {
            "remove_row": "#FF0000",  # Red
            "review": "#FFFF00",      # Yellow
            "revision": "#99CCFF",     # Blue
            "cell_update": "#CC99FF"   # Purple
        }

        # Create a colored model for the BOM table
        colored_model = ColoredPandasModel(bomDf, color_column='_action_proposed', color_map=color_map)

        # Set the model to the BOM table view
        self.bomTableView.setModel(colored_model)

        # Resize columns to fit content
        self.bomTableView.resizeColumnsToContents()

        # Switch to the BOM Data tab in the review tab widget
        self.reviewTabWidget.setCurrentIndex(0)

        # Switch to the Initialize Review tab in the main tab widget
        self.tabWidget.setCurrentIndex(2)

        QMessageBox.information(self, "Analysis Complete", f"BOM analysis completed. Results saved to {excel_file}")

    def onAnalyzeGeneral(self):
        """Analyze General data"""
        # Placeholder for General data analysis functionality

        if self.generalModel._data.empty:
            QMessageBox.warning(self, "No General Data", "Please load General data first.")
            return

        if self.reportDf.empty:
            QMessageBox.warning(self, "No Report Data", "Please load report data first.")
            return

        if not self.roiPayload:
            QMessageBox.warning(self, "No ROI Payload", "Please load ROI payload first.")
            return

        doc = fitz.open(self.currentFilename)

        def get_iso_coords(pdf_page):
            rois = self.roiPayload["groupRois"][self.roiPayload["pageToGroup"][pdf_page]]
            for roi in rois:
                if roi["columnName"] == "Isometric Drawing Area":
                    return roi
            return None

        generalDf = self.generalModel._data.copy()  # Create a copy to avoid modifying the original

        # Ensure _action_proposed column exists
        if '_action_proposed' not in generalDf.columns:
            generalDf['_action_proposed'] = None
        if '_staged_action' not in generalDf.columns:
            generalDf['_staged_action'] = None

        # columns to check for revisions

        for index, row in generalDf.iterrows():
            pdf_page = row['pdf_page']

            group = self.reportDf[self.reportDf['pdf_page'] == pdf_page]

            page = doc[pdf_page - 1]
            page_width = page.rect.width
            page_height = page.rect.height

            isoCoords = get_iso_coords(pdf_page)
            if not isoCoords:
                continue

            iso_x0 = isoCoords["relativeX0"] * page_width
            iso_y0 = isoCoords["relativeY0"] * page_height
            iso_x1 = isoCoords["relativeX1"] * page_width
            iso_y1 = isoCoords["relativeY1"] * page_height
            iso_bbox = [iso_x0, iso_y0, iso_x1, iso_y1]

            elevation = row["elevation"]

            for drawing_bbox, bbox_group in group.groupby('drawing_bbox'):

                # Convert drawing_bbox from string to list if needed
                if isinstance(drawing_bbox, str):
                    drawing_bbox = eval(drawing_bbox)  # Be careful with eval, only use if you're sure the input is safe

                # Check if drawing_bbox intersects with bom_bbox. i.e. filter within bom roi
                if not self.bboxesIntersect(drawing_bbox, iso_bbox):
                    continue  # Skip this drawing_bbox if it doesn't intersect with bom_bbox

                # Process this drawing_bbox and its group
                intersecting_values = bbox_group['raw_data_value'].unique().tolist()
                print(pdf_page)
                print(drawing_bbox)
                print(intersecting_values)

                for value in intersecting_values:
                    # Basic elevation check
                    if value.startswith("EL"):
                        action = "review"
                        generalDf.loc[index, "_action_proposed"] = action
                        generalDf.loc[index, "_staged_action"] = action

        excel_file = "debug/general_proposed.xlsx"

        # First save the dataframe to Excel
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            generalDf.to_excel(writer, index=False, sheet_name='General Analysis')

            # Access the openpyxl workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['General Analysis']

            # Add autofilter to the worksheet
            worksheet.auto_filter.ref = f"A1:{openpyxl.utils.get_column_letter(len(generalDf.columns))}{len(generalDf) + 1}"

            # Define fill colors
            red_fill = openpyxl.styles.PatternFill(start_color='FFFF0000', end_color='FFFF0000', fill_type='solid')
            yellow_fill = openpyxl.styles.PatternFill(start_color='FFFFFF00', end_color='FFFFFF00', fill_type='solid')
            blue_fill = openpyxl.styles.PatternFill(start_color='FF99CCFF', end_color='FF99CCFF', fill_type='solid')
            purple_fill = openpyxl.styles.PatternFill(start_color='99FFCCFF', end_color='99FFCCFF', fill_type='solid')

            # Process rows by action type (much faster than row-by-row)
            # For rows with 'remove_row' action
            remove_indices = generalDf.index[generalDf['_action_proposed'] == 'remove_row'].tolist()
            for idx in remove_indices:
                # Excel rows are 1-based and we need to add 1 more for the header row
                row_idx = generalDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(generalDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = red_fill

            # For rows with 'review' action
            review_indices = generalDf.index[generalDf['_action_proposed'] == 'review'].tolist()
            for idx in review_indices:
                row_idx = generalDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(generalDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = yellow_fill

            # For rows with 'revision' action
            revision_indices = generalDf.index[generalDf['_action_proposed'] == 'revision'].tolist()
            for idx in revision_indices:
                row_idx = generalDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(generalDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = blue_fill

            # For rows with 'revision' action
            revision_indices = generalDf.index[generalDf['_action_proposed'] == 'cell_update'].tolist()
            for idx in revision_indices:
                row_idx = generalDf.index.get_loc(idx) + 2
                for col_idx in range(1, len(generalDf.columns) + 1):
                    worksheet.cell(row=row_idx, column=col_idx).fill = purple_fill

        return


if __name__ == "__main__":
    import sys
    sys.path[0] = ""
    app = QApplication([])
    window = RawDataViewer()
    window.show()
    app.exec()
