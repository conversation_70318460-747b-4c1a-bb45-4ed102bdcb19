from src.utils.logger import logger
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from pubsub import pub
from src.utils.logger import logger
from threading import Thread
from src.pyside_util import get_resource_pixmap, applyDropShadowEffect

# logger = logging.getLogger()


class DocumentThumbnail(QWidget):

    sgnSetPixmap = Signal(object)

    def __init__(self, parent, document: dict):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())

        self.document = document
        _id = document["projectId"]
        pageNumber = document["pageNumber"]
        try:
            self.blob = document["document"]
        except:
            self.blob = None
        filename = document["documentName"]
        fileNameShort = document["documentName"]

        try:
            fileNameShort = filename[:8] + "..."
        except Exception as e:
            pass

        self.pb = QPushButton("")
        self.pb.setToolTip(filename)
        self.pb.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addWidget(self.pb)
        self.label = QLabel(f"{fileNameShort} - Page: {pageNumber}", self)
        self.layout().addWidget(self.label)
        self.pb.setObjectName("documentThumbnail")
        self.setObjectName("documentThumbnail")
        self.setFixedSize(256, 256)
        self.pb.clicked.connect(self.onDocumentClicked)
        self.sgnSetPixmap.connect(self.onSetPixmap)
        self.update()

        applyDropShadowEffect(self)

    def update(self):
        if not self.blob:
            self.pb.setIcon(QIcon(get_resource_pixmap("blueprint.png")))
            return
        # self.processBlob(self.blob, self.width(), self.onFinishedScale)
        Thread(target=self.processBlob, args=(self.blob, self.width(), self.onFinishedScale)).start()

    def processBlob(self, blob, width, callback):
        qimg = QImage.fromData(blob)
        pixmap = QPixmap.fromImage(qimg)
        pixmap = pixmap.scaledToWidth(width)
        callback(pixmap)

    def onDocumentClicked(self):
        pdfId = self.document["id"]
        print(pdfId)

    def onFinishedScale(self, pixmap):
        self.sgnSetPixmap.emit(pixmap)

    def onSetPixmap(self, pixmap: QPixmap):
        self.pb.setIconSize(pixmap.rect().size())
        self.pb.setIcon(QIcon(pixmap))


class FlowLayout(QLayout):

    def __init__(self, parent=None):
        super().__init__(parent)

        if parent is not None:
            self.setContentsMargins(0, 0, 0, 0)

        self._item_list = []

    def __del__(self):
        item = self.takeAt(0)
        while item:
            item = self.takeAt(0)

    def addItem(self, item):
        self._item_list.append(item)

    def count(self):
        return len(self._item_list)

    def itemAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list[index]

        return None

    def takeAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list.pop(index)

        return None

    def expandingDirections(self):
        return Qt.Orientation(0)

    def hasHeightForWidth(self):
        return True

    def heightForWidth(self, width):
        height = self._do_layout(QRect(0, 0, width, 0), True)
        return height

    def setGeometry(self, rect):
        super(FlowLayout, self).setGeometry(rect)
        self._do_layout(rect, False)

    def sizeHint(self):
        return self.minimumSize()

    def minimumSize(self):
        size = QSize()

        for item in self._item_list:
            size = size.expandedTo(item.minimumSize())

        size += QSize(2 * self.contentsMargins().top(), 2 * self.contentsMargins().top())
        return size

    def _do_layout(self, rect, test_only):
        x = rect.x()
        y = rect.y()
        line_height = 0
        spacing = self.spacing()

        for item in self._item_list:
            style = item.widget().style()
            layout_spacing_x = style.layoutSpacing(
                QSizePolicy.PushButton, QSizePolicy.PushButton, Qt.Horizontal
            )
            layout_spacing_y = style.layoutSpacing(
                QSizePolicy.PushButton, QSizePolicy.PushButton, Qt.Vertical
            )
            space_x = spacing + layout_spacing_x
            space_y = spacing + layout_spacing_y
            next_x = x + item.sizeHint().width() + space_x
            if next_x - space_x > rect.right() and line_height > 0:
                x = rect.x()
                y = y + line_height + space_y
                next_x = x + item.sizeHint().width() + space_x
                line_height = 0

            if not test_only:
                item.setGeometry(QRect(QPoint(x, y), item.sizeHint()))

            x = next_x
            line_height = max(line_height, item.sizeHint().height())

        return y + line_height - rect.y()


class DocumentsGrid(QWidget):

    sgnDocumentsUpdated = Signal(object)
    nDocuments = 0

    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().setSpacing(16)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.sgnDocumentsUpdated.connect(self.onUpdateDocuments)

        self.flowWidget = QWidget(self)
        self.flowWidget.setLayout(FlowLayout())
        self.flowWidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        self.layout().addWidget(self.flowWidget)

    def onUpdateDocuments(self, documents: list):
        """ Display thumbnails and links in grid view """
        return
        logger.info("Refreshing document thumbnails", exc_info=True)
        # Clear docs
        for ch in reversed(self.flowWidget.children()):
            if isinstance(ch, DocumentThumbnail):
                ch.setParent(None)
                ch.deleteLater()
        PDF_LIMIT = 24
        for n, document in enumerate(documents):
            pb = DocumentThumbnail(self, document)
            self.flowWidget.layout().addWidget(pb)
            self.nDocuments = n
            if n > PDF_LIMIT:
                break
        self.updateStatusBar()

    def updateStatusBar(self):
        data = {"type": "DocumentViewer", "params": {"documentCount": self.nDocuments} }
        pub.sendMessage("set-statusbar-documentlibrary", data=data)

class DocumentsContainer(QScrollArea):

    def __init__(self, parent):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.showScrollbars()
        self.setAlignment(Qt.AlignmentFlag.AlignHCenter)

    def showScrollbars(self):
        """ Only show vertical scrollbars """
        try:
            self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        except Exception as e:
            logger.error(f"Error showing scrollbars: {e}", exc_info=True)

class DocumentsView(QWidget):

    def __init__(self, parent):
        super().__init__(parent)
        self._parent = parent
        self.documentsContainer = DocumentsContainer(self)
        self.documentsGrid = DocumentsGrid(self.documentsContainer)
        self.documentsContainer.setWidget(self.documentsGrid)

        self.setLayout(QVBoxLayout())

        self.toolbar = QWidget(self)
        self.toolbar.setLayout(QHBoxLayout())
        self.toolbar.setFixedHeight(48)
        self.toolbar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Maximum)

        self.label = QLabel("Scans")
        self.label.setObjectName("header")
        self.toolbar.layout().addWidget(self.label)

        # self.pbAddSource = QPushButton(" Add")
        # self.pbAddSource.setFixedHeight(32)
        # self.pbAddSource.setIcon(get_resource_qicon("plus.svg"))
        # self.pbAddSource.clicked.connect(self.onAddSource)
        # self.toolbar.layout().addWidget(self.label)
        # self.toolbar.layout().addWidget(self.pbAddSource)

        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.toolbar.layout().addItem(spacer)

        self.layout().addWidget(self.toolbar)
        self.layout().addWidget(self.documentsContainer)
        # self.zoomSlider = DocumentThumbnail(self)
        # self.zoomSlider.move(500,500)
        # self.zoomSlider.show()

        pub.subscribe(self.onDocuments, "documents-view-update")

    @property
    def projectData(self):
        try:
            return self._parent.projectData
        except Exception:
            return {}

    def onDocuments(self, data: dict):
        self.documentsGrid.sgnDocumentsUpdated.emit(data["documents"])

    def showEvent(self, event: QShowEvent) -> None:
        self.documentsGrid.updateStatusBar()
        return super().showEvent(event)

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    app = QApplication()
    window = DocumentsView(None)
    window.setMinimumSize(800, 600)
    window.show()
    app.exec()