import os
import cv2
import fitz  # PyMuPDF
import numpy as np
import ast
import io

from PIL import Image


def rotate_rect(rect, page_rect, rotation):
    if rotation == 0:
        return rect
    elif rotation == 90:
        return fitz.Rect(
            page_rect.height - rect.y1,
            rect.x0,
            page_rect.height - rect.y0,
            rect.x1
        )
    elif rotation == 180:
        return fitz.Rect(
            page_rect.width - rect.x1,
            page_rect.height - rect.y1,
            page_rect.width - rect.x0,
            page_rect.height - rect.y0
        )
    elif rotation == 270:
        return fitz.Rect(
            rect.y0,
            page_rect.width - rect.x1,
            rect.y1,
            page_rect.width - rect.x0
        )
    else:
        return rect  # unknown rotation fallback

def blur_and_overlay(page, rect, zoom=2, blur_radius=21):
    """
    Apply a Gaussian blur to a specific region of a PDF page.

    This function takes a direct approach:
    1. Render the entire page as a pixmap
    2. Apply blur to the specific region in the pixmap
    3. Replace the entire page with the modified pixmap

    This preserves all positioning, sizing, and rotation since we're
    working with the rendered page exactly as it appears.
    """
    # Create a Rect object if not already
    rect_obj = fitz.Rect(rect)

    # Step 1: Render the entire page at high resolution
    pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))

    # Step 2: Convert to numpy array - make a copy to ensure it's writable
    img_data = np.frombuffer(pix.samples, dtype=np.uint8)
    img = np.copy(img_data).reshape(pix.height, pix.width, pix.n)

    if pix.n == 4:
        img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

    # Step 3: Calculate the coordinates of the rectangle in the pixmap
    # We need to scale the coordinates by the zoom factor
    x0, y0, x1, y1 = rect_obj.x0 * zoom, rect_obj.y0 * zoom, rect_obj.x1 * zoom, rect_obj.y1 * zoom

    # Ensure coordinates are integers and within bounds
    x0, y0 = max(0, int(x0)), max(0, int(y0))
    x1, y1 = min(img.shape[1], int(x1)), min(img.shape[0], int(y1))

    # Step 4: Extract the region we want to blur
    if x1 > x0 and y1 > y0:  # Ensure valid rectangle
        region = img[y0:y1, x0:x1].copy()  # Make a copy to ensure it's writable

        # Step 5: Apply Gaussian blur to the region
        if region.size > 0:  # Ensure region is not empty
            blurred_region = cv2.GaussianBlur(region, (blur_radius, blur_radius), 0)

            # Step 6: Put the blurred region back into the image
            img[y0:y1, x0:x1] = blurred_region

    # Step 7: Convert the modified image back to a format PyMuPDF can use
    pil_img = Image.fromarray(img)
    buf = io.BytesIO()
    pil_img.save(buf, format="PNG")
    buf.seek(0)

    # Step 8: Create a new page with the same dimensions and rotation
    doc = page.parent
    page_num = page.number

    # Get original page properties
    original_rotation = page.rotation
    original_mediabox = page.mediabox

    # Delete the current page
    doc.delete_page(page_num)

    # Insert a new page at the same position
    new_page = doc.new_page(page_num,
                           width=original_mediabox.width,
                           height=original_mediabox.height)

    # Add the modified image to the new page, covering the entire page
    new_page.insert_image(new_page.rect, stream=buf.read())

    # Set the rotation to match the original page
    if original_rotation != 0:
        new_page.set_rotation(original_rotation)


def plugin_blur_pdf(input_file=r"C:/Drawings/All Revisions-New ISOs 2.7.25.pdf",
                    save_file: str = "debug/pdf_blurred_regions_not_redacted.pdf",
                    blur_images: bool = True,
                    blur_regions: list = "[[57,600,1000,1000]]",
                    blur_radius: int = 21,
                    zoom = 2):
    """
    Blur regions of the PDF

    WARNING - THIS DOES NOT REDACT SENSITIVE INFORMATION EMBEDDED
    """

    os.makedirs(os.path.dirname(save_file), exist_ok=True)

    if blur_regions:
        blur_regions = ast.literal_eval(blur_regions)
    else:
        blur_regions = []
    if blur_radius % 2 == 0:
        return "Blur radius must be an odd number"

    # Open the input file
    doc = fitz.open(input_file)

    for i, page in enumerate(doc):
        # Optional: blur images on page
        if blur_images:
            for img_info in page.get_images(full=True):
                xref = img_info[0]
                try:
                    bbox = page.get_image_bbox(xref)
                    print(f"xref: {xref}, bbox: {bbox}")
                except RuntimeError as e:
                    print(f"⚠️ Skipping xref {xref}: {e}")
                    continue

                rect_px = [int(coord) for coord in bbox]
                blur_and_overlay(page, rect_px, zoom, blur_radius)

        # Blur specified rectangles
        for rect in blur_regions:
            blur_and_overlay(page, rect, zoom, blur_radius)

        break  # Only process the first page

    # Save the modified document
    doc.save(save_file)
    print(f"Blurred PDF saved to: {save_file}")
    return f"Blurred PDF saved to: {save_file}"