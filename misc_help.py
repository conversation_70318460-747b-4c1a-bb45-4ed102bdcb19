import os, re
import pandas as pd
from pathlib import Path
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from src.atom.convert_excel_fields import convert_display_to_key
from data_conversions import process_size_column
import fitz

# Set the folder path
folder_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Data\Final\Final Clean"

# List of Excel files to process
excel_files = [
    "U1Z3 Utilities.xlsx",
    "U1Z5 Utilities.xlsx",
    "U1Z6 Utilities.xlsx",
    "U1Z7 Utilities.xlsx"
]

# Function to get unique PDF Page numbers from a sheet
def get_pdf_page_numbers(df):
    if 'PDF Page' in df.columns:
        return set(df['PDF Page'].dropna().astype(int).unique())
    return set()

def check_missing_pages():
    '''
    Check For Missing Page Numbers
    '''
    
    # Set to store all unique PDF Page numbers
    all_pdf_pages = set()

    # Process each Excel file
    for file in excel_files:
        file_path = os.path.join(folder_path, file)
    
        if not os.path.exists(file_path):
            print(f"File not found: {file}")
            continue
    
        # Read the Excel file
        xls = pd.ExcelFile(file_path)
    
        # Process 'General' and 'BOM' sheets
        for sheet in ['General', 'BOM']:
            if sheet in xls.sheet_names:
                df = pd.read_excel(xls, sheet_name=sheet)
                pdf_pages = get_pdf_page_numbers(df)
                all_pdf_pages.update(pdf_pages)
            else:
                print(f"Sheet '{sheet}' not found in {file}")

    # Check for missing numbers
    if all_pdf_pages:
        min_page = min(all_pdf_pages)
        max_page = max(all_pdf_pages)
        all_possible_pages = set(range(min_page, max_page + 1))
        missing_pages = all_possible_pages - all_pdf_pages

        print(f"Unique PDF Page numbers found: {sorted(all_pdf_pages)}")
    
        if missing_pages:
            print(f"Missing PDF Page numbers: {sorted(missing_pages)}")
        else:
            print("No missing PDF Page numbers found.")
    else:
        print("No PDF Page numbers found in the processed files.")
        

# Read all files into dataframes. Handles NA and blank values correctly
def read_excel_with_na(file_path):
    return pd.read_excel(file_path, 
                         keep_default_na=False, 
                         na_values=[''])
        

def combine_xlsx(filepaths):
    '''
    Function to combine tables. Used for consolidating exported groups
    Accepts list of filenames
    '''
    
    # Read the files into dataframes
    dfs = [read_excel_with_na(file) for file in filepaths]

    # Assuming we want to maintain the column order of the first file
    first_df_columns = dfs[0].columns

    # Combine all dataframes while keeping the same column order
    combined_df = pd.concat(dfs, ignore_index=True)[first_df_columns]
    
    return combined_df


def create_combined_workbook(output_path, output_filename, combine_general, general_files, combine_bom, combine_rfq, bom_files, combine_spec, spec_files, rfq_files):
    # Create the output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Create a new Excel writer
    with pd.ExcelWriter(f"{output_path}/{output_filename}", engine='xlsxwriter') as writer:
        
        if combine_general:
            print("Combining General Files")
            general_df = combine_xlsx(general_files)
            
            # convert to db field names
            converted_general_df = convert_display_to_key(general_df)

            converted_general_df.to_excel(writer, sheet_name='General', index=False)
        
        if combine_bom:
            print("Combining BOM Files")
            
            bom_df = combine_xlsx(bom_files)

            # convert to db field names
            converted_bom_df = convert_display_to_key(bom_df)

            converted_bom_df.to_excel(writer, sheet_name='BOM', index=False)
        
        if combine_spec:
            print("Combining SPEC Files")

            spec_df = combine_xlsx(spec_files)

            # convert to db field names
            converted_spec_df = convert_display_to_key(spec_df)

            converted_spec_df.to_excel(writer, sheet_name='Spec', index=False)

        if combine_rfq:
            print("Combining RFQ Files")

            rfq_df = combine_xlsx(rfq_files)

            # convert to db field names
            converted_rfq_df = convert_display_to_key(rfq_df)

            converted_rfq_df.to_excel(writer, sheet_name='RFQ', index=False)

    print(f"Workbook created at: {output_path}/{output_filename}")


def merge_rfq_files(full_rfq_path, output_path=None, sub_rfq_path=None, one_workbook=False):
    """
    Merges data from 'Sub RFQ.xlsx' into 'Full RFQ.xlsx' on 'material_description',
    excluding the columns 'size', 'size1', 'size2', and 'quantity'.
    
    Args:
    - full_rfq_path: Path to the 'Full RFQ.xlsx' file.
    - sub_rfq_path: Path to the 'Sub RFQ.xlsx' file.
    - output_path: Path where the merged output file will be saved.
    - one_workbook: If True, reads both datasets from different worksheets in full_rfq_path.
    
    Returns:
    - A DataFrame of the merged content.
    """
    print("Merging RFQ data. Loading Workbooks...")

    # # Load the Excel files into DataFrames using openpyxl
    # full_rfq_df = pd.read_excel(full_rfq_path, engine='openpyxl')
    # sub_rfq_df = pd.read_excel(sub_rfq_path, engine='openpyxl')

    # Load the data based on one_workbook flag
    if one_workbook:
        full_rfq_df = pd.read_excel(full_rfq_path, sheet_name="Full", engine='openpyxl')
        sub_rfq_df = pd.read_excel(full_rfq_path, sheet_name="Deduplicated", engine='openpyxl')
    else:
        full_rfq_df = pd.read_excel(full_rfq_path, engine='openpyxl')
        sub_rfq_df = pd.read_excel(sub_rfq_path, engine='openpyxl')

    # Strip whitespace and convert material_description to lowercase for matching
    full_rfq_df['material_description'] = full_rfq_df['material_description'].str.strip()
    sub_rfq_df['material_description'] = sub_rfq_df['material_description'].str.strip()

    # Define the columns to exclude from merging
    exclude_columns = ['size', 'size1', 'size2', 'quantity']

    print("Merging RFQ data...")
    # Filter out the columns to merge from 'sub_rfq_df'
    merge_columns = [col for col in sub_rfq_df.columns if col not in exclude_columns and col != 'material_description']

    # Find common columns between both DataFrames (for matching update)
    common_columns = list(set(merge_columns) & set(full_rfq_df.columns))

    # Print debug information
    print("Common columns:", common_columns)

    # Update the 'full_rfq_df' DataFrame by iterating over all rows with matching material_description
    print("Updating Full RFQ data...")
    for idx, row in full_rfq_df.iterrows():
        # Get the matching row(s) from sub_rfq_df
        matching_rows = sub_rfq_df[sub_rfq_df['material_description'] == row['material_description']]

        # If there's a match, update the common columns, excluding specified columns
        if not matching_rows.empty:
            for col in common_columns:
                full_rfq_df.at[idx, col] = matching_rows.iloc[0][col]
    
    print("Exporting Full RFQ data...")
    # Use xlsxwriter for writing the updated DataFrame to Excel
    with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
        full_rfq_df.to_excel(writer, index=False)

    return full_rfq_df

def create_final_workbook(base_directory):
    # Loop through subdirectories
    for subdirectory in os.listdir(base_directory):
        subdirectory_path = os.path.join(base_directory, subdirectory)
        
        # Skip if not a directory
        if not os.path.isdir(subdirectory_path):
            continue

        # Create a new workbook for each subdirectory
        new_workbook_name = f"GTPP PCL Area {subdirectory}.xlsx"
        wb = Workbook()
        wb.remove(wb.active)  # Remove the default sheet

        # Loop through all files in the subdirectory
        for filename in os.listdir(subdirectory_path):
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                file_path = os.path.join(subdirectory_path, filename)
                
                # Read the Excel file
                df = pd.read_excel(file_path)
                
                # Determine the new sheet name
                if 'bom' in filename.lower():
                    sheet_name = 'BOM'
                elif 'rfq' in filename.lower():
                    sheet_name = 'RFQ'
                elif 'general' in filename.lower():
                    sheet_name = 'General'
                elif 'spec' in filename.lower():
                    sheet_name = 'Spec'
                else:
                    sheet_name = os.path.splitext(filename)[0]  # Use filename without extension
                
                # Create a new sheet
                ws = wb.create_sheet(title=sheet_name)
                
                # Write the dataframe to the sheet
                for r in dataframe_to_rows(df, index=False, header=True):
                    ws.append(r)

        # Save the combined workbook in the subdirectory
        wb.save(os.path.join(subdirectory_path, new_workbook_name))
        print(f"Combined workbook for {subdirectory} saved as {new_workbook_name}")

def extract_key_value_pairs(df):
    

    # Define the keys we're looking for
    keys = [
        "MAXIMUM AMBIENT TEMP",
        "NORMAL PROCESS OPER. TEMP",
        "MINIMUM STARTUP TEMP",
        "MAX HEATER EXPOSURE TEMP",
        "AREA HAZ. CLASSIFICATION",
        "SYSTEM LIMIT TEMP",
        "AUTO IGNITION TEMP",
        "PIPE MATERIAL",
        "TEMPERATURE CLASS"
    ]

    # Function to extract value for a given key
    def extract_value(row, key):
        try:
            # Convert value to string if it's not already
            value = str(row['value'])
            pattern = f"{key}:\\s*(.*)"
            match = re.search(pattern, value, re.IGNORECASE)
            return match.group(1) if match else None
        except Exception as e:
            print(f"Error processing row: {row}")
            print(f"Error message: {str(e)}")
            return None

    # Create a new DataFrame with pdf_page as index and keys as columns
    result_df = pd.DataFrame(index=df['pdf_page'].unique(), columns=keys)

    # Populate the result DataFrame
    for page in result_df.index:
        page_data = df[df['pdf_page'] == page]
        for key in keys:
            try:
                value = page_data.apply(extract_value, args=(key,), axis=1).dropna().iloc[0] if not page_data.empty else None
                result_df.at[page, key] = value
            except IndexError:
                print(f"No value found for key '{key}' on page {page}")
            except Exception as e:
                print(f"Error processing key '{key}' on page {page}: {str(e)}")

    # Display the first few rows of the result
    print(result_df.head())

    # Save the result to a new Excel file
    result_df.to_excel("extracted_data.xlsx")

def normalize_size_column(df, size_column):
    """Normalize the size column in a dataframe."""
    new_sizes = df[size_column].apply(lambda x: pd.Series(process_size_column(x)))
    df['size'] = new_sizes[0]
    #df['size2'] = new_sizes[1]
    # df['last_updated'] = None


def update_fw_count(general_data_path, weld_data_path):
    '''
    Update FW count for General Data
    '''

    # Load the Excel workbooks
    general_data = pd.read_excel(general_data_path)
    weld_data = pd.read_excel(weld_data_path)

    # Normalize size columns
    general_data = normalize_size_column(general_data, 'Size')
    weld_data = normalize_size_column(weld_data, 'size')
    
    # Group the weld data by 'page' and 'size', and count the occurrences
    weld_counts = weld_data.groupby(['page', 'size']).size().reset_index(name='FW_Count')
    
    # Rename columns in weld_counts to match general_data
    weld_counts = weld_counts.rename(columns={'page': 'PDF Page'})
    
    print(weld_counts)

    # Merge the general data with the weld counts
    merged_data = pd.merge(general_data, weld_counts, on=['PDF Page', 'size'], how='left')
    
    # Fill NaN values in FW_Count with 0
    merged_data['FW_Count'] = merged_data['FW_Count'].fillna(0)
    
    # Update the 'FW' column
    merged_data['FW'] = merged_data['FW'] + merged_data['FW_Count']
    
    return merged_data


def rotate_and_remove_pages(input_path, pages_to_rotate, output_path, pages_to_remove=None):
    """
    Rotate specified pages by 90 degrees and remove specified pages.
    All page numbers are 1-indexed.
    """
    # Open the PDF document
    doc = fitz.open(input_path)

    # Convert 1-indexed page numbers to 0-indexed
    pages_to_rotate = set(page - 1 for page in pages_to_rotate)
    pages_to_remove = set(page - 1 for page in (pages_to_remove or []))

    # Create a list to store pages we want to keep
    pages_to_keep = []

    # Iterate over all the pages in the document
    for page_num, page in enumerate(doc):
        if page_num in pages_to_remove:
            continue  # Skip this page if it's in the remove list
        
        if page_num in pages_to_rotate:
            # Get the current rotation
            current_rotation = page.rotation
            # Calculate new rotation (add 90 degrees)
            new_rotation = (current_rotation + 90) % 360
            # Set the new rotation
            page.set_rotation(new_rotation)
        
        # Add this page to the list of pages to keep
        pages_to_keep.append(page_num)

    # Create a new PDF with only the pages we want to keep
    doc.select(pages_to_keep)

    # Save the changes to the PDF
    doc.save(output_path)
    doc.close()
    print(f"Output saved to: {output_path}" )

def compare_workbooks(original_path, corrected_path, ignore_columns=None):
    '''
    Highlights differences in classified data between original and corrected Excel workbooks.
    Used to illustrate how data was corrected for training or validation purposes.
    '''

    if ignore_columns is None:
        ignore_columns = []

    original_df = pd.read_excel(original_path, sheet_name='Deduplicated')
    corrected_df = pd.read_excel(corrected_path, sheet_name='Deduplicated')

    original_df.set_index('material_description', inplace=True)
    corrected_df.set_index('material_description', inplace=True)

    differences = []

    deleted_rows = set(original_df.index) - set(corrected_df.index)
    for description in deleted_rows:
        differences.append({
            'material_description': description,
            'column': 'Row Deleted',
            'value': 'Present',
            'corrected': 'Deleted'
        })

    common_rows = set(original_df.index).intersection(corrected_df.index)
    columns_to_compare = [col for col in original_df.columns if col not in ignore_columns]

    for description in common_rows:
        for col in columns_to_compare:
            original_value = original_df.at[description, col]
            corrected_value = corrected_df.at[description, col]
            if pd.notna(original_value) or pd.notna(corrected_value):
                if original_value != corrected_df.at[description, col]:
                    differences.append({
                        'material_description': description,
                        'column': col,
                        'value': original_value,
                        'corrected': corrected_df.at[description, col]
                    })

    differences_df = pd.DataFrame(differences)
    differences_df.reset_index(drop=True, inplace=True)
    return differences_df


import pandas as pd
from pathlib import Path

# Map of unicode fractions to ascii
unicode_fractions = {
    '½': '1/2',
    '¼': '1/4',
    '¾': '3/4',
    '⅓': '1/3',
    '⅔': '2/3',
    '⅛': '1/8',
    '⅜': '3/8',
    '⅝': '5/8',
    '⅞': '7/8'
}

def normalize_unicode_fractions(text):
    if not isinstance(text, str):
        return text
    for uni, ascii_frac in unicode_fractions.items():
        text = text.replace(uni, ascii_frac)
    return text

def normalize_excel_unicode_fractions(filepath):
    filepath = Path(filepath)
    df_dict = pd.read_excel(filepath, sheet_name=None)  # all sheets

    # Apply normalization to all sheets
    normalized_sheets = {
        sheet_name: df.applymap(normalize_unicode_fractions)
        for sheet_name, df in df_dict.items()
    }

    # Output path
    output_path = filepath.with_stem(filepath.stem + "_normalized")

    # Write to new Excel workbook
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        for sheet_name, df in normalized_sheets.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"Normalized file written to:\n{output_path}")


if __name__ == '__main__':

    create_workbooks = True
    merge_rfq_data = False
    create_final = False
    extract_keys = False
    update_fw = False
    correct_rotation = False

    fix_unicode_fractions = False

    compare_workbooks = False # used for training and validation purposes

    if fix_unicode_fractions:
        filepath = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 034 - Contech\Data\BINDER1_COMBINED-BOM-GROUPS-1-2-3.xlsx"
        normalize_excel_unicode_fractions(filepath)

    if create_workbooks:
        combine_general = False
        combine_bom = True
        combine_spec = False
        combine_rfq = True

        output_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0028,29 Combined\Data"
        output_filename = "BRS_0028&0029 - Combined.xlsx"

        general_files = [
            r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis 2025-07-14 Air Liquide - Main Binder\data\cleaned - general.xlsx",
            r"S:\Shared Folders\Architekt Integrated Systems\ATEM\Client Work Space\Axis\Axis 2025-07-14 Air Liquide - TRS_AFI\data\cleaned - general.xlsx"
          ]

        bom_files = [
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0028 - BSLTX34282 Phillips 66 Lake Charles I-10 General Construction\data\2 - cleaned\bom - cleaned.xlsx",
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL #107\data\2 - cleaned\bom - cleaned.xlsx"
                            ]
        
        rfq_files = [
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0028 - BSLTX34282 Phillips 66 Lake Charles I-10 General Construction\data\4 - classified\rfq - classified.xlsx",
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL #107\data\4 - classified\exported_rfq_data_nofieldmap.xlsx"
        ]

        spec_files = [
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1400\Grp 1\exported_spec_data.xlsx",
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1400\Grp 2\exported_spec_data.xlsx",
            r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1400\Grp 4\exported_spec_data.xlsx"
    
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1200\Grp 1\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1200\Grp 2\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1200\Grp 3\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1200\Grp 5\exported_spec_data.xlsx",

            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1300\Grp 1\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1300\Grp 2\exported_spec_data.xlsx",
    
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1500\Grp 1\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1500\Grp 2\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1500\Grp 3\exported_spec_data.xlsx",

            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1600\Grp 1\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1600\Grp 2\exported_spec_data.xlsx",
    
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1700\Grp 1\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1700\Grp 2\exported_spec_data.xlsx",
            # r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\1700\Grp 4\exported_spec_data.xlsx"
            ]

        


        # Call the function to create the combined workbook
        create_combined_workbook(output_path, output_filename, combine_general, general_files, combine_bom, combine_rfq, bom_files, combine_spec, spec_files, rfq_files)
    
    if merge_rfq_data:
        same_workbook=True # Use the rfq template sheets instead of 2 workbboks

        full_rfq_filepath = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0000_Dow\rfq_template_combined dow.xlsx"
        sub_rfq_filepath = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-003\In\rfq.xlsx"
        output_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0000_Dow\Data\In\combined_rfq_in.xlsx"

        merged_rfq = merge_rfq_files(full_rfq_filepath, output_path, sub_rfq_filepath, same_workbook)
        print(f"\n\nLen RFQ: {len(merged_rfq)}")

    if create_final:
        # Run the function with your specified directory
        directory = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Data\Final"
        create_final_workbook(directory)

    if extract_keys:
        # Read the Excel file
        df = pd.read_excel(r"C:\Users\<USER>\source\repos\Architekt ATOM\_debug_raw_data_full.xlsx")
        extract_key_value_pairs(df)

    if update_fw:

        general_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\LM2500\Final\exported_general_data.xlsx"
        weld_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\LM2500\Verified Welds.xlsx"

        result = update_fw_count(general_data_path, weld_data_path)
        result.to_excel(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\LM2500\General with FW.xlsx")

    if correct_rotation:

        '''
        Need to convert to dict with rotatation amounts (Page 390 of Classic Demo 'Combined Drawings.pdf need 180 applied)
        '''
    
        # Suppress warnings about PolyLine annotations
        # warnings.filterwarnings("ignore", category=UserWarning, message="cannot set rect: code=4: PolyLine annotations have no Rect property")



        input_pdf = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Classic Industrial\Demo CO\Modified\Combined Drawings.pdf"
        output_pdf = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Classic Industrial\Demo CO\Modified\Strobel Combined.pdf"


        # rotate_test(input_pdf, output_pdf)

        pages_to_rotate = [262, 263, 276, 277, 278, 289, 290, 322, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 415, 416, 417, 418, 419, 
                           420, 421, 422, 423, 424, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 
                           456, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 
                           492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 
                           526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 563, 564, 
                           565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 595, 596, 597, 598, 599, 600, 
                           601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 
                           635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 828, 834, 835, 836, 837, 838, 839, 840, 
                           841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 876, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903]  # Page numbers (0-indexed)

        # pages_to_rotate = [1,2,3,4,5]

        pages_to_remove =  [184, 185]  # Page numbers to remove (0-indexed)

        rotate_and_remove_pages(input_pdf, pages_to_rotate, output_pdf, pages_to_remove)
    

