"""
Process BOM Data with LangGraph and Export Results

This module provides a complete workflow to:
1. Load BOM data from Excel/CSV files
2. Process it through the LangGraph classification system
3. Export results to job-specific folders

Usage:
    python process_and_export.py --input "path/to/bom.xlsx" --job_folder "path/to/job/folder"
"""

import asyncio
import argparse
import os
import sys
import pandas as pd
from pathlib import Path
from typing import List, Optional
import time
from dotenv import load_dotenv

# Add the parent directories to the path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent))
sys.path.insert(0, str(current_dir.parent.parent))

try:
    from integration_layer import (
        create_default_langgraph_config,
        create_stage1_only_config,
        ModelType
    )
    from state_models import create_initial_state
    from classification_nodes import material_analysis_node
    from langgraph_main import create_classification_workflow
    from export_results import export_classified_results
    print("✅ Successfully imported LangGraph modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the correct directory")
    import traceback
    traceback.print_exc()
    sys.exit(1)


def load_bom_data(file_path: str) -> pd.DataFrame:
    """
    Load BOM data from Excel or CSV file
    
    Args:
        file_path: Path to the BOM file
        
    Returns:
        DataFrame with BOM data
    """
    
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"BOM file not found: {file_path}")
    
    print(f"📂 Loading BOM data from: {file_path}")
    
    # Determine file type and load accordingly
    if file_path.suffix.lower() in ['.xlsx', '.xls']:
        # Try to load Excel file
        try:
            df = pd.read_excel(file_path)
            print(f"✅ Loaded Excel file: {len(df)} rows")
        except Exception as e:
            print(f"❌ Failed to load Excel file: {e}")
            raise
    elif file_path.suffix.lower() == '.csv':
        # Try to load CSV file
        try:
            df = pd.read_csv(file_path)
            print(f"✅ Loaded CSV file: {len(df)} rows")
        except Exception as e:
            print(f"❌ Failed to load CSV file: {e}")
            raise
    else:
        raise ValueError(f"Unsupported file format: {file_path.suffix}")
    
    # Validate required columns
    required_columns = ['material_description']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"⚠️  Missing required columns: {missing_columns}")
        print(f"Available columns: {list(df.columns)}")
        
        # Try to find similar column names
        for missing_col in missing_columns:
            similar_cols = [col for col in df.columns if missing_col.lower() in col.lower()]
            if similar_cols:
                print(f"💡 Possible matches for '{missing_col}': {similar_cols}")
        
        raise ValueError(f"Required columns missing: {missing_columns}")
    
    # Add ID column if not present
    if 'id' not in df.columns:
        df['id'] = [f"item_{i+1:04d}" for i in range(len(df))]
        print(f"✅ Added ID column: item_0001 to item_{len(df):04d}")
    
    # Clean data
    df = df.dropna(subset=['material_description'])
    df['material_description'] = df['material_description'].astype(str).str.strip()
    
    print(f"📊 Final dataset: {len(df)} rows with valid material descriptions")
    
    return df


async def process_bom_with_langgraph(
    bom_df: pd.DataFrame,
    model_type: ModelType = ModelType.GEMINI_20_FLASH,
    stage_only: bool = False,
    max_items: Optional[int] = None,
    debug_mode: bool = False
) -> List[dict]:
    """
    Process BOM data through LangGraph classification system
    
    Args:
        bom_df: DataFrame with BOM data
        model_type: Gemini model to use
        stage_only: If True, run Stage 1 only (faster)
        max_items: Maximum number of items to process (for testing)
        debug_mode: Enable debug output
        
    Returns:
        List of classification results
    """
    
    # Load environment variables from .env file
    load_dotenv()

    # Check for API key (try multiple sources)
    api_key = os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ API Key not found!")
        print("Please set your Google API key in one of these ways:")
        print("1. Create a .env file in the project root with: GEMINI_API_KEY=your_key_here")
        print("2. Set environment variable: set GEMINI_API_KEY=your_key_here")
        print("3. Set environment variable: set GOOGLE_API_KEY=your_key_here")
        raise ValueError("API key required for model calls")
    
    print(f"🤖 Processing BOM data with LangGraph")
    print(f"   Model: {model_type.value}")
    print(f"   Stage only: {stage_only}")
    print(f"   Debug mode: {debug_mode}")
    
    # Limit items for testing
    if max_items:
        bom_df = bom_df.head(max_items)
        print(f"   Limited to: {len(bom_df)} items")
    
    # Create configuration
    if stage_only:
        config = create_stage1_only_config(
            model_type=model_type,
            debug_mode=debug_mode,
            api_key=api_key
        )
    else:
        config = create_default_langgraph_config(
            debug_mode=debug_mode,
            api_key=api_key
        )
    
    # Process items with async concurrency (like your existing audit system)
    max_concurrent = 20  # Same as your audit system's proven settings
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_item_with_semaphore(row_data):
        """Process a single item with semaphore control"""
        async with semaphore:
            item_id = row_data['id']
            material_description = row_data['material_description']

            try:
                # Create initial state
                state = create_initial_state(
                    item_id=item_id,
                    material_description=material_description,
                    original_classification={},
                    debug_mode=debug_mode
                )

                # Add model configuration to state
                state["model_config"] = config

                if stage_only:
                    # Run Stage 1 only
                    result_state = await material_analysis_node(state)
                else:
                    # Run complete workflow
                    workflow = create_classification_workflow(config)
                    result_state = await workflow.ainvoke(state)

                return result_state

            except Exception as e:
                if debug_mode:
                    print(f"   ❌ Error processing {item_id}: {str(e)}")

                # Create error result
                error_result = {
                    "item_id": item_id,
                    "material_description": material_description,
                    "processing_path": "error",
                    "field_classifications": {"rfq_scope": "Error"},
                    "confidence_scores": {"rfq_scope": 0.0},
                    "workflow_path": ["error"],
                    "processing_time": 0.0,
                    "model_calls": 0,
                    "tokens_used": 0,
                    "identified_issues": [{"field": "processing", "explanation": str(e)}],
                    "extracted_properties": {}
                }
                return error_result

    # Prepare items for concurrent processing
    items = bom_df.to_dict('records')

    print(f"🚀 Processing {len(items)} items with {max_concurrent} concurrent workers...")
    start_time = time.time()

    # Process all items concurrently
    tasks = [process_item_with_semaphore(item) for item in items]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions that weren't caught
    final_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            if debug_mode:
                print(f"   ❌ Unhandled exception for item {items[i].get('id', f'item_{i}')}: {result}")

            # Create error result for unhandled exceptions
            error_result = {
                "item_id": items[i].get('id', f'item_{i}'),
                "material_description": items[i].get('material_description', ''),
                "processing_path": "error",
                "field_classifications": {"rfq_scope": "Error"},
                "confidence_scores": {"rfq_scope": 0.0},
                "workflow_path": ["error"],
                "processing_time": 0.0,
                "model_calls": 0,
                "tokens_used": 0,
                "identified_issues": [{"field": "processing", "explanation": str(result)}],
                "extracted_properties": {}
            }
            final_results.append(error_result)
        else:
            final_results.append(result)

    results = final_results
    
    total_time = time.time() - start_time

    print(f"\n📊 Processing Complete")
    print(f"   Total items: {len(results)}")
    print(f"   Total time: {total_time:.2f}s")
    print(f"   Average time per item: {total_time/len(results):.2f}s")

    # Calculate detailed success metrics
    error_items = sum(1 for r in results if r.get("processing_path") == "error")
    successful_items = len(results) - error_items
    items_with_issues = sum(1 for r in results if r.get("identified_issues") and len(r.get("identified_issues", [])) > 0 and r.get("processing_path") != "error")

    success_rate = (successful_items / len(results) * 100) if results else 0
    print(f"   Successful: {successful_items}/{len(results)} ({success_rate:.1f}%)")
    print(f"   Errors: {error_items}")
    print(f"   Items with issues: {items_with_issues}")

    return results


async def main():
    """Main function for command-line usage"""
    
    parser = argparse.ArgumentParser(description="Process BOM data with LangGraph and export results")
    parser.add_argument("--input", "-i", required=True, help="Path to BOM Excel/CSV file")
    parser.add_argument("--job_folder", "-j", required=True, help="Path to job folder for export")
    parser.add_argument("--job_name", "-n", help="Job name for filename")
    parser.add_argument("--model", "-m", choices=["gemini-2.0", "gemini-2.5"], default="gemini-2.0", help="Model to use")
    parser.add_argument("--stage_only", "-s", action="store_true", help="Run Stage 1 only (faster)")
    parser.add_argument("--max_items", "-x", type=int, help="Maximum number of items to process")
    parser.add_argument("--debug", "-d", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    print("LangGraph BOM Classification and Export")
    print("=" * 60)
    
    try:
        # Load BOM data
        bom_df = load_bom_data(args.input)
        
        # Map model argument to ModelType
        model_map = {
            "gemini-2.0": ModelType.GEMINI_20_FLASH,
            "gemini-2.5": ModelType.GEMINI_25_FLASH
        }
        model_type = model_map[args.model]
        
        # Process with LangGraph
        results = await process_bom_with_langgraph(
            bom_df=bom_df,
            model_type=model_type,
            stage_only=args.stage_only,
            max_items=args.max_items,
            debug_mode=args.debug
        )
        
        # Export results
        export_path = export_classified_results(
            workflow_results=results,
            job_folder=args.job_folder,
            original_data=bom_df,
            job_name=args.job_name
        )
        
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Results exported to: {export_path}")
        
    except Exception as e:
        print(f"\n❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
