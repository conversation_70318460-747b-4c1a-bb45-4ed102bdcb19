"""
"""
from PySide6.QtGui import QHideEvent
from PySide6.QtWidgets import (QPushButton, QSizePolicy, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QLabel, QComboBox, QLineEdit, QGridLayout, QStackedWidget)
from PySide6.QtCore import Qt, Signal
from .baseform import BaseForm
from pubsub import pub
from src.pyside_util import get_resource_qicon

ICON_SIZE = 100
PADDING = 128
FORM_SIZE = ICON_SIZE + PADDING + 96
WIDTH = 960

class CardDetails(QWidget):

    sgnCardSaved = Signal()

    def __init__(self, parent) -> None:
        super().__init__(parent)

        grid = QGridLayout()
        grid.setSpacing(16)
        self.name = QLineEdit()
        self.number = QLineEdit()
        self.number.addAction(get_resource_qicon("credit-card.svg"), 
                            QLineEdit.ActionPosition.LeadingPosition)
        grid.addWidget(QLabel("Cardholder Name"), 0, 0, 1, 1)
        grid.addWidget(self.name, 1, 0, 1, 2)
        grid.addWidget(QLabel("Card Number"), 2, 0, 1, 1)
        grid.addWidget(self.number, 3, 0, 1, 2)

        grid.addWidget(QLabel("CVV"), 4, 0, 1, 1)
        grid.addWidget(QLabel("Expired Date"), 4, 1, 1, 1)

        self.cvv = QLineEdit()
        grid.addWidget(self.cvv, 5, 0, 1, 1)
        hbox = QWidget(self)
        hbox.setLayout(QHBoxLayout())
        self.expiryMM = QLineEdit()
        self.expiryYY = QLineEdit()
        hbox.layout().addWidget(self.expiryMM)
        hbox.layout().addWidget(self.expiryYY)
        grid.addWidget(hbox, 5, 1, 1, 1)
        
        pbSave = QPushButton("Save Card")
        pbSave.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        pbSave.setFixedSize(256, 32)
        grid.addWidget(pbSave, 6, 1, 1, 1)
        pbSave.clicked.connect(self.onSave)
    
        self.setLayout(grid)
    
    def onSave(self):
        self.sgnCardSaved.emit()
        print("Save payment card")

class CardSelection(QWidget):

    def __init__(self, parent) -> None:
        super().__init__(parent)

        self.setLayout(QGridLayout())
        self.layout().setSpacing(8)
        self.cboxCards = QComboBox(self)
        self.cboxCards.setMinimumHeight(32)
        self.cboxCards.addItem(get_resource_qicon("credit-card.svg"), "Card **** 4242 | 12/42")
        lblSelect = QLabel("Select Payment Card")
        lblSelect.setMaximumHeight(32)
        lblSelect.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.layout().addWidget(lblSelect, 0, 0, 1, 1)
        self.layout().addWidget(self.cboxCards, 1, 0, 1, 2)

        hbox = QWidget(self)
        hbox.setMaximumWidth(256)
        # hbox.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        hbox.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hbox.setLayout(QHBoxLayout())
        self.pbRemove = QPushButton("Remove Card")
        # self.pbRemove.setFixedSize(128, 32)
        hbox.layout().addWidget(self.pbRemove)
        self.pbAdd = QPushButton("Add New Card")
        # self.pbAdd.setFixedSize(128, 32)
        hbox.layout().addWidget(self.pbAdd)
        self.layout().addWidget(hbox, 2, 1, 1, 1)

        self.layout().addWidget(QLabel(""), 3, 1, 2, 1)


class TokenCheckoutForm(BaseForm):

    def __init__(self, parent):
        super().__init__(parent)
        self._cards: list = []

    def initUi(self):
        self.formSize.setWidth(720)
        self.formSize.setHeight(480)
        
        self.title.setText("")
        self.subtitle.setText("Checkout")
        self.subtitle.setObjectName("titleLabel")
        self.subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.setLayout(QVBoxLayout())
        self.addVSpace()

        self.stackedWidget = QStackedWidget(self)

        self.cardSelect = CardSelection(self)
        self.cardSelect.pbAdd.clicked.connect(self.onAddNewCardDetails)

        self.cardDetails = CardDetails(self)
        self.cardDetails.sgnCardSaved.connect(self.onCardSaved)

        self.stackedWidget.addWidget(self.cardSelect)
        self.stackedWidget.addWidget(self.cardDetails)
        self.stackedWidget.setCurrentWidget(self.cardSelect)
        self.layout().addWidget(self.stackedWidget)

        self.addStretchSpacer()
        self.pbProceed = QPushButton("Confirm Payment")
        self.pbProceed.setFixedHeight(32)
        self.pbProceed.clicked.connect(self.onProceed)
        # self.pbProceed.setEnabled(False)
        self.layout().addWidget(self.pbProceed)

        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        self.setFloatingButtonBack(True)
        self.updateUi()

    def initDefaults(self):
        return

    def onSpinTokensChanged(self, event):
        self.updateUi()

    def onFloatingButton(self):
        self.sgnSwitchTo.emit("TokenPaymentForm")

    def hideEvent(self, event: QHideEvent) -> None:
        return super().hideEvent(event)

    def updateUi(self):
        return
        tokens = self.spinTokens.value()
        tokenPrice = 1
        total = tokens * tokenPrice
        subtitle = f"Tokens: {tokens} = ${total}"
        self.lblPrice.setText(subtitle)

    def onAddNewCardDetails(self):
        self.stackedWidget.setCurrentWidget(self.cardDetails)

    def onCardSaved(self):
        self.stackedWidget.setCurrentWidget(self.cardSelect)
        self.pbProceed.setEnabled(True)
    
    def setParams(self, params):
        self.tokens = int(params["tokens"])

    def onProceed(self):
        pub.sendMessage("request-tokens-checkout", quantity=self.tokens)