"""
Data Uploads Module

A modular system for preparing, auditing, and uploading data to PostgreSQL tables.
This module provides simple, reliable tools for data validation and field mapping.

Main Components:
- field_mapping: Simple SQLite to PostgreSQL column name mapping
- data_auditor: Data quality auditing and validation
- data_prep: Data preparation and cleaning utilities

Example Usage:
    from data_uploads import prepare_for_upload, quick_audit
    from data_uploads.field_mapping import map_dataframe_columns
    
    # Quick data preparation
    prepared_df, report = prepare_for_upload(df, 'general')
    
    # Quick audit
    audit_results = quick_audit(df, 'bom')
    
    # Column mapping only
    mapped_df, unmapped = map_dataframe_columns(df, 'rfq')
"""

from .field_mapping import (
    get_field_mapping,
    map_column_name,
    validate_columns,
    map_dataframe_columns,
    GENERAL_TABLE_MAPPING,
    BOM_TABLE_MAPPING,
    RFQ_TABLE_MAPPING
)

from .data_auditor import (
    DataAuditor,
    quick_audit
)

from .data_prep import (
    DataPreparator,
    prepare_for_upload
)

__version__ = "1.0.0"
__author__ = "Architekt ATOM"

# Convenience imports for easy access
__all__ = [
    # Field mapping
    'get_field_mapping',
    'map_column_name', 
    'validate_columns',
    'map_dataframe_columns',
    'GENERAL_TABLE_MAPPING',
    'BOM_TABLE_MAPPING', 
    'RFQ_TABLE_MAPPING',
    
    # Data auditing
    'DataAuditor',
    'quick_audit',
    
    # Data preparation
    'DataPreparator',
    'prepare_for_upload'
]
