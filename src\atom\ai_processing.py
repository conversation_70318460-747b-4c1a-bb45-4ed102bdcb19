#https://priya-dwivedi.medium.com/testing-out-the-gpt4-turbo-model-4eadba9c42e4
#https://platform.openai.com/docs/guides/embeddings
import openai
from openai import OpenAI
import os, json
import pandas as pd
from dotenv import load_dotenv
import openpyxl
from src.utils.logger import logger

# Load the API key from the environment
load_dotenv()
API_KEY="***************************************************"
# OPEN_AI_API_KEY = os.getenv("API_KEY")
OPEN_AI_API_KEY = API_KEY

# This logger will inherit configurations from the root logger configured in main.py
# logger = logging.getLogger(__name__)

# Define the Gpt4Turbo class
class Gpt4Turbo:
    def __init__(self):
        self.MODEL = 'gpt-4-0125-preview'  # Replace with the actual model name
        #self.MODEL = 'gpt-3.5-turbo-0125'  # Replace with the actual model name
        self.TOKEN_LIMIT = 4000  # The token limit for each API call
        self.client = openai.OpenAI(api_key=OPEN_AI_API_KEY)

    def gptCall_json(self, temperature, streaming: bool, messages: list):
        # Add 'json' to the system message content to satisfy API requirement
        #messages.insert(0, {"role": "system", "content": "You are an AI assistant designed to assist with estimating material takeoffs from isometric drawings for industrial construction projects. You respond in JSON format."})
        messages.insert(0, {"role": "system", "content": "You are an AI assistant designed to assist with estimating material takeoffs from isometric drawings for industrial construction projects. You respond in JSON format."}) #json array format?
        try:
            response = self.client.chat.completions.create(
                model=self.MODEL,
                messages=messages,
                temperature=temperature,
                max_tokens=self.TOKEN_LIMIT,
                stream=streaming,
                response_format={"type": "json_object"}  # Enforce output format
            )

            if streaming:
                for event in response:
                    #print("Waiting for streamed event...")
                    event_text = event.choices[0].delta.content
                    if event_text is not None:
                        #print("Received event:", event_text)
                        yield event_text
            else:
                output = response.choices[0].message.content
                if output:
                    print("\nOutput: \n", output)
                    yield json.loads(output)  # Decode JSON from the output
                else:
                    yield {}  # Yield an empty dict if there's no content

        except json.JSONDecodeError as e:
            print(f"JSON decoding error: {e}")
            yield {}
            
# Function to trim all columns in a DataFrame
def trim_all_columns(df):
    return df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

# Function to create batches of descriptions
# Function to create batches of descriptions along with nps
def create_batches(df, batch_size):
    # Extract the descriptions and nps values from the DataFrame
    material_info = df[['material_description', 'nps']].to_dict(orient='records')
    
    # Create batches
    for i in range(0, len(material_info), batch_size):
        batch_material_info = material_info[i:i+batch_size]
        batch_indices = df.index[i:i+batch_size].tolist()
        yield batch_indices, batch_material_info

# Function to categorize materials using GPT-4
def analyze_bom_data(df, gpt4_turbo):
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = 10
    
    BATCH_SIZE = 10  # Adjust the batch size as needed
    results = []
    temperature = 0.5  # Adjust the temperature as needed
    streaming = False  # Set streaming as needed
    
    # JSON structure for categorization options
    categorization_table = [
        {
            "field":"rfq_scope",
            "description": "The main material_description category.",
            "options":["PIPE", "FITTINGS", "FLANGES", "BOLTS", "GASKETS", "SUPPORTS", "VALVES", "OTHER", "TAKEOFF_ITEM" ]
        },
        {
            "field":"unit_of_measure",
            "description": "The abbreviated indicator of the unit in which the item is typically quantified. EA=Each, LF=Linear Feet, SF=Square Feet, SY=Square Yards, CF=Cubic Feet, CY=Cubic Yard",
            "options":["EA", "LF", "SF", "SY", "CF", "CY", "LOT", "PAIR", "TONS"]
        },
        {
            "field":"size1",
            "description": "The primary size of the material item. Example: Larger Pipe Size, Bolt Dia, Gasket Size, Nipple Size, Support Line Size",
            "options":["0.03125","0.0625","0.125","0.1875","0.25","0.375","0.5","0.625","0.75","0.875","1","1.125","1.25","1.375","1.5","1.625","1.75","1.875","2","2.125","2.25","2.375","2.5","2.625","2.75","2.875","3","3.125","3.25","3.375","3.5","3.625","3.75","3.875","4","4.125","4.25","4.375","4.5","4.625","4.75","4.875","5","5.125","5.25","5.375","5.5","5.625","5.75","5.875","6","6.125","6.25","6.375","6.5","6.625","6.75","6.875","7","7.125","7.25","7.375","7.5","7.625","7.75","7.875","8","8.125","8.25","8.375","8.5","8.625","8.75","8.875","9","9.125","9.25","9.375","9.5","9.625","9.75","9.875","10","10.125","10.25","10.375","10.5","10.625","10.75","10.875","11","11.125","11.25","11.375","11.5","11.625","11.75","11.875","12","12.125","12.25","12.375","12.5","12.625","12.75","12.875","13","13.125","13.25","13.375","13.5","13.625","13.75","13.875","14","14.125","14.25","14.375","14.5","14.625","14.75","14.875","15","15.125","15.25","15.375","15.5","15.625","15.75","15.875","16","16.125","16.25","16.375","16.5","16.625","16.75","16.875","17","17.125","17.25","17.375","17.5","17.625","17.75","17.875","18","18.125","18.25","18.375","18.5","18.625","18.75","18.875","19","19.125","19.25","19.375","19.5","19.625","19.75","19.875","20","20.125","20.25","20.375","20.5","20.625","20.75","20.875","21","21.125","21.25","21.375","21.5","21.625","21.75","21.875","22","22.125","22.25","22.375","22.5","22.625","22.75","22.875","23","23.125","23.25","23.375","23.5","23.625","23.75","23.875","24","24.125","24.25","24.375","24.5","24.625","24.75","24.875","25","25.125","25.25","25.375","25.5","25.625","25.75","25.875","26","26.125","26.25","26.375","26.5","26.625","26.75","26.875","27","27.125","27.25","27.375","27.5","27.625","27.75","27.875","28","28.125","28.25","28.375","28.5","28.625","28.75","28.875","29","29.125","29.25","29.375","29.5","29.625","29.75","29.875","30","30.125","30.25","30.375","30.5","30.625","30.75","30.875","31","31.125","31.25","31.375","31.5","31.625","31.75","31.875","32","32.125","32.25","32.375","32.5","32.625","32.75","32.875","33","33.125","33.25","33.375","33.5","33.625","33.75","33.875","34","34.125","34.25","34.375","34.5","34.625","34.75","34.875","35","35.125","35.25","35.375","35.5","35.625","35.75","35.875","36","36.125","36.25","36.375","36.5","36.625","36.75","36.875","37","37.125","37.25","37.375","37.5","37.625","37.75","37.875","38","38.125","38.25","38.375","38.5","38.625","38.75","38.875","39","39.125","39.25","39.375","39.5","39.625","39.75","39.875","40","42","44","46","48","50","52","54","56","58","60","62","64","66","68","72","80","84","88","100"]   },
        {
            "field":"size2",
            "description": "The secondary size of the material item if applicable. This includes things like: Smaller Pipe Size, Bolt Length (in.), Gasket Thickness, Nipple Length (in.), Support Length (If Applicable), Tap Dia for Blinds with Tap small side of reducer and tees",
            "options":["0.03125","0.0625","0.125","0.1875","0.25","0.375","0.5","0.625","0.75","0.875","1","1.125","1.25","1.375","1.5","1.625","1.75","1.875","2","2.125","2.25","2.375","2.5","2.625","2.75","2.875","3","3.125","3.25","3.375","3.5","3.625","3.75","3.875","4","4.125","4.25","4.375","4.5","4.625","4.75","4.875","5","5.125","5.25","5.375","5.5","5.625","5.75","5.875","6","6.125","6.25","6.375","6.5","6.625","6.75","6.875","7","7.125","7.25","7.375","7.5","7.625","7.75","7.875","8","8.125","8.25","8.375","8.5","8.625","8.75","8.875","9","9.125","9.25","9.375","9.5","9.625","9.75","9.875","10","10.125","10.25","10.375","10.5","10.625","10.75","10.875","11","11.125","11.25","11.375","11.5","11.625","11.75","11.875","12","12.125","12.25","12.375","12.5","12.625","12.75","12.875","13","13.125","13.25","13.375","13.5","13.625","13.75","13.875","14","14.125","14.25","14.375","14.5","14.625","14.75","14.875","15","15.125","15.25","15.375","15.5","15.625","15.75","15.875","16","16.125","16.25","16.375","16.5","16.625","16.75","16.875","17","17.125","17.25","17.375","17.5","17.625","17.75","17.875","18","18.125","18.25","18.375","18.5","18.625","18.75","18.875","19","19.125","19.25","19.375","19.5","19.625","19.75","19.875","20","20.125","20.25","20.375","20.5","20.625","20.75","20.875","21","21.125","21.25","21.375","21.5","21.625","21.75","21.875","22","22.125","22.25","22.375","22.5","22.625","22.75","22.875","23","23.125","23.25","23.375","23.5","23.625","23.75","23.875","24","24.125","24.25","24.375","24.5","24.625","24.75","24.875","25","25.125","25.25","25.375","25.5","25.625","25.75","25.875","26","26.125","26.25","26.375","26.5","26.625","26.75","26.875","27","27.125","27.25","27.375","27.5","27.625","27.75","27.875","28","28.125","28.25","28.375","28.5","28.625","28.75","28.875","29","29.125","29.25","29.375","29.5","29.625","29.75","29.875","30","30.125","30.25","30.375","30.5","30.625","30.75","30.875","31","31.125","31.25","31.375","31.5","31.625","31.75","31.875","32","32.125","32.25","32.375","32.5","32.625","32.75","32.875","33","33.125","33.25","33.375","33.5","33.625","33.75","33.875","34","34.125","34.25","34.375","34.5","34.625","34.75","34.875","35","35.125","35.25","35.375","35.5","35.625","35.75","35.875","36","36.125","36.25","36.375","36.5","36.625","36.75","36.875","37","37.125","37.25","37.375","37.5","37.625","37.75","37.875","38","38.125","38.25","38.375","38.5","38.625","38.75","38.875","39","39.125","39.25","39.375","39.5","39.625","39.75","39.875","40","42","44","46","48","50","52","54","56","58","60","62","64","66","68","72","80","84","88","100"]},
        {
            "field":"schedule",
            "description": "Pipe wall thickness: SCH80, SCH40, etc",
            "options":["5","5S","10","10S","20","30","40","40S","60","80","80S","100","120","140","160","STD","XH","XXH","Custom-L","Custom-M","Custom-H","5 x 5S","5 x 10","5 x 10S","5 x 20","5 x 30","5 x 40","5 x 40S","5 x 60","5 x 80","5 x 80S","5 x 100","5 x 120","5 x 140","5 x 160","5 x STD","5 x XH","5 x XXH","5S x 5","5S x 10","5S x 10S","5S x 20","5S x 30","5S x 40","5S x 40S","5S x 60","5S x 80","5S x 80S","5S x 100","5S x 120","5S x 140","5S x 160","5S x STD","5S x XH","5S x XXH","10 x 5","10 x 5S","10 x 10S","10 x 20","10 x 30","10 x 40","10 x 40S","10 x 60","10 x 80","10 x 80S","10 x 100","10 x 120","10 x 140","10 x 160","10 x STD","10 x XH","10 x XXH","10S x 5","10S x 5S","10S x 10","10S x 20","10S x 30","10S x 40","10S x 40S","10S x 60","10S x 80","10S x 80S","10S x 100","10S x 120","10S x 140","10S x 160","10S x STD","10S x XH","10S x XXH","20 x 5","20 x 5S","20 x 10","20 x 10S","20 x 30","20 x 40","20 x 40S","20 x 60","20 x 80","20 x 80S","20 x 100","20 x 120","20 x 140","20 x 160","20 x STD","20 x XH","20 x XXH","30 x 5","30 x 5S","30 x 10","30 x 10S","30 x 20","30 x 40","30 x 40S","30 x 60","30 x 80","30 x 80S","30 x 100","30 x 120","30 x 140","30 x 160","30 x STD","30 x XH","30 x XXH","40 x 5","40 x 5S","40 x 10","40 x 10S","40 x 20","40 x 30","40 x 40S","40 x 60","40 x 80","40 x 80S","40 x 100","40 x 120","40 x 140","40 x 160","40 x STD","40 x XH","40 x XXH","40S x 5","40S x 5S","40S x 10","40S x 10S","40S x 20","40S x 30","40S x 40","40S x 60","40S x 80","40S x 80S","40S x 100","40S x 120","40S x 140","40S x 160","40S x STD","40S x XH","40S x XXH","60 x 5","60 x 5S","60 x 10","60 x 10S","60 x 20","60 x 30","60 x 40","60 x 40S","60 x 80","60 x 80S","60 x 100","60 x 120","60 x 140","60 x 160","60 x STD","60 x XH","60 x XXH","80 x 5","80 x 5S","80 x 10","80 x 10S","80 x 20","80 x 30","80 x 40","80 x 40S","80 x 60","80 x 80S","80 x 100","80 x 120","80 x 140","80 x 160","80 x STD","80 x XH","80 x XXH","80S x 5","80S x 5S","80S x 10","80S x 10S","80S x 20","80S x 30","80S x 40","80S x 40S","80S x 60","80S x 80","80S x 100","80S x 120","80S x 140","80S x 160","80S x STD","80S x XH","80S x XXH","100 x 5","100 x 5S","100 x 10","100 x 10S","100 x 20","100 x 30","100 x 40","100 x 40S","100 x 60","100 x 80","100 x 80S","100 x 120","100 x 140","100 x 160","100 x STD","100 x XH","100 x XXH","120 x 5","120 x 5S","120 x 10","120 x 10S","120 x 20","120 x 30","120 x 40","120 x 40S","120 x 60","120 x 80","120 x 80S","120 x 100","120 x 140","120 x 160","120 x STD","120 x XH","120 x XXH","140 x 5","140 x 5S","140 x 10","140 x 10S","140 x 20","140 x 30","140 x 40","140 x 40S","140 x 60","140 x 80","140 x 80S","140 x 100","140 x 120","140 x 160","140 x STD","140 x XH","140 x XXH","160 x 5","160 x 5S","160 x 10","160 x 10S","160 x 20","160 x 30","160 x 40","160 x 40S","160 x 60","160 x 80","160 x 80S","160 x 100","160 x 120","160 x 140","160 x STD","160 x XH","160 x XXH","STD x 5","STD x 5S","STD x 10","STD x 10S","STD x 20","STD x 30","STD x 40","STD x 40S","STD x 60","STD x 80","STD x 80S","STD x 100","STD x 120","STD x 140","STD x 160","STD x XH","STD x XXH","XH x 5","XH x 5S","XH x 10","XH x 10S","XH x 20","XH x 30","XH x 40","XH x 40S","XH x 60","XH x 80","XH x 80S","XH x 100","XH x 120","XH x 140","XH x 160","XH x STD","XH x XXH","XXH x 5","XXH x 5S","XXH x 10","XXH x 10S","XXH x 20","XXH x 30","XXH x 40","XXH x 40S","XXH x 60","XXH x 80","XXH x 80S","XXH x 100","XXH x 120","XXH x 140","XXH x 160","XXH x STD","XXH x XH"]
        },
        {
            "field":"rating",
            "description": "Rating of the takeoff item",
            "options":["100","125","150","200","300","325","500","600","800","900","1500","2000","2500","3000","3500","6000"]
        },
        {
            "field":"astm",
            "description": "The ASTM specification to which the material conforms, indicating material standards for performance requirements.",
            "options":["A105","A105N","A106","A108","A126","A134","A139","A155","A181","A182","A193","A194","A197","A198","A199","A200","A201","A202","A203","A204","A205","A206","A207","A208","A209","A210","A211","A212","A213","A214","A215","A216","A217","A218","A219","A220","A221","A222","A223","A224","A225","A226","A227","A228","A229","A230","A231","A232","A233","A234","A235","A236","A237","A238","A239","A240","A241","A242","A243","A244","A245","A246","A247","A248","A249","A250","A251","A252","A253","A254","A255","A256","A257","A258","A259","A260","A261","A262","A263","A264","A265","A266","A267","A268","A269","A270","A271","A272","A273","A274","A275","A276","A277","A278","A279","A280","A281","A282","A283","A284","A285","A286","A287","A288","A289","A290","A291","A292","A293","A294","A295","A296","A297","A298","A299","A36","A300","A301","A302","A303","A304","A304L","A305","A306","A307","A308","A309","A310","A311","A312","A313","A314","A314L","A315","A316","A316L","A317","A318","A319","A320","A321","A322","A323","A324","A325","A326","A327","A328","A329","A330","A331","A332","A333","A334","A335","A336","A337","A338","A339","A340","A341","A342","A343","A344","A345","A346","A347","A348","A349","A350","A351","A352","A353","A354","A355","A356","A357","A358","A381","A387","A395","A403","A420","A515","A516","A520","A522","A53","A587","A671","A694","A733","A789","A790","A815","A860","A865","ALLOY 20","API","API5L","APT5LX","B11","B127","B152","B160","B161","B162","B164","B165","B166","B167","B168","B209","B241","B247","B265","B283","B361","B363","B366","B369","B377","B381","B402","B407","B408","B409","B42","B420","B423","B462","B464","B466","B467","B468","B473","B564","B705","B75","B861","B861-05","B861-14","B862","B862-05","B862-14","B88","C276","D1784","F1545","F439","F441","F714","SA182","SDR11","SDR9","YOLOY"]
        },
        {
            "field":"grade",
            "description": "Material Grade",
            "options":["0","1","2","3","4","5","7","8","9","11","12","18.2.2","18.22.1","22","70","304","309","310","316","321","333","335","347","355","400","410","464","825","904","904L","2205","23447","65-45-12","2H","2HM","304/304L","304L","310S","316/316L","316L","40S","410S","430B","5L","7M","8F","8M","II","A","API 600","B","C","D","L","6","A36","B7","B7M","B8","B8-CL2","B8M","C12","C5","CB60","CC65","CC65-CL13","CD4MCU","CE8MN","CH20","CK20","CF3","CF3A","CF3M","CF8","CF8A","CF8C","CF8M","CF8/304L","F1","F11","F11-CL.1","F11-CL.2","F12","F22","F304","F304/304L","F304/F304L","F304L","F309","F310","F310H","F316","F316/316L","F316/F316L","F316L","F321","F347","F347H","F42","F5","F50","F51","F6","F60","F6A","F7","F9","FM","L2","L7","LC3","LF2","LF2-CL1","LF3","LF9","P1","P11","P12","P2","P21","P22","P3","P5","P7","P9","P235","P235GH","S31200","S31260","S31803","S32900","TP304","TP304/304L","TP304L","TP309","TP310","TP316","TP316/316L","TP316/A182","TP316L","TP321","TP347","TP347H","TP405","TP409","TP410","TP430","UNS.N04400","WC1","WC4","WC5","WC6","WC9","WCB","WCB/API 600","WCC","WP1","WP11","WP12","WP20CB","WP22","WP3003","WP304","WP304/304L","WP304/WP304L","WP304L","WP304S","WP304W/304LW","WP304W/304LWX","WP304WX","WP304WX/304LWX","WP309","WP310","WP310S","WP316","WP316/316L","WP316L","WP316L-S","WP316L-WX","WP321","WP347","WP347H","WP5","WP6061","WP6063","WP7","WP9","WPA","WPB","WPB-M","WPB-S","WPB-W","WPHY-42","WPHY-52","WPHY-60","WPL3","WPL6","WPL6-S","WPL8","WPL9","WPN","WPNC","WPNCS","WPNC1","WPNIC","WPT2W","WP-S304/304L","WP-S316/316L","WP-W304/304L","WP-W316/316L","WP316WX","WR5","WR7","X42","X52","X60","X60MS","Y53","0, S235JR","0, P265GH"]
        },
        {
            "field":"ansme_ansi",
            "description": "Applicable Code",
            "options":["API 600","API 602","B16.1","B16.3","B16.4","B16.5","B16.9","B16.10","B16.11","B16.12","B16.14","B16.18","B16.20","B16.21","B16.22","B16.23","B16.24","B16.25","B16.26","B16.29","B16.33","B16.34","B16.36","B16.38","B16.39","B16.40","B16.42","B16.44","B16.47","B16.48","B16.49","B16.50","B16.51","B16.52","B18.2.1","B18.2.2","B31.1","B31.3","B31.4","B31.5","B31.10","B36.10","B36.10M","B36.10/19","B31.11","B36.19","B36.19M","BPE","MSS SP-43","MSS SP-44","MSS SP-75","MSS SP-79","MSS SP-80","MSS SP-83","MSS SP-84","MSS SP-85","MSS SP-86","MSS SP-87","MSS SP-95","MSS SP-96","MSS SP-97","MSS SP-114"
        ]
        },
        {
            "field":"material",
            "description": "Type of Material, specifying the primary material composition of the item.",
            "options":["ALUMINUM","BRONZE","CARBON STEEL","CARBON STEEL (LINED)","CARPENTER 20","CAST IRON","CHROME","COPPER","CST MI","CST MI HTDP","DUCTILE IRON","DUCTILE IRON (LINED)","DUPLEX","FUSION BOND","GALV.","HASTELLOY","HDPE","INCOLOY","INCONEL","LT CARBON STEEL","MALLEABLE IRON","MONEL","NICKEL","POLYPROPYLENE","STAINLESS STEEL","STAINLESS STEEL (LINED)","TITANIUM","CPVC","PVC","UPVC","FRP","FIBERGLASS","GRP"]
        },
        {
            "field":"abbreviated_material",
            "description": "Abbreviated type of Material",
            "options":["ALUMINUM","CU/BRASS","CS","CS","CS","CS","CHROME","CU/BRASS","CS","CS","CS","CS","SS","CS","CS","HASTELLOY","HDPE","Incoloy","Inconel","LTCS","CS","SS","SS","CS","SS","SS","Titanium","PVC","PVC","PVC","FRP","FRP","FRP"]
        },
        {
            "field":"coating",
            "description": "Coating type",
            "options":["ZINC PLATED","ZINC PLATED - CHROME MOLY ALLOY","ZINC GLAVANIZED","GALVANIZED","FLUOROPOLYMER COATING","TEFLON","TEFLON - BLUE","PTFE","CADMIUM","CADMIUM (CAD PLTD)"]
        },
        {
            "field":"forging",
            "description": "Process of manufacturing or shaping the material, indicating how the material was prepared or formed.",
            "options":["EFW","ERW","HFW","SMLS","THREADED","WELDED","FORGED"]
        },
        {
            "field":"ends",
            "description": "Type of connection end for the item, identifying how it connects to other components in the system.",
            "options":["FLG","BE","BE X BE","BE X SW","BE X PE","BE X TE","SW","SW X SW","SW X BE","SW X PE","SW X TE","PE","PE X PE","PE X SW","PE X BE","PE X TE","TE","TE X TE","TE X SW","TE X BE","TE X PE"]
        },
        {
            "field":"item_tag",
            "description": "Item Tag, Support #, Valve Tag, etc",
            "options":[""]
        },
        {
            "field":"tie_point",
            "description": "Tiepoint #",
            "options":[""]
        },
        {
            "field":"pipe_category",
            "description": "Specific Category if category pipe",
            "options":["PIPE",
                "TUBE",
                "OTHER PIPE"
            ]
        },
        {
            "field":"valve_type",
            "description": "Specific Category of valve if category is valve",
            "options":["ANGLE VALVE",
                "BALL VALVE",
                "BUTTERFLY VALVE",
                "CHECK VALVE",
                "CONTROL VALVE",
                "DIAPH. VALVE",
                "GATE VALVE",
                "GLOBE VALVE",
                "NEEDLE VALVE",
                "PLUG VALVE",
                "RELIEF VALVE",
                "REGULATING VALVE",
                "STEAM TRAP",
                "THREE-WAY VALVE",
                "WAFER CHECK VALVE",
                "Y-TYPE VALVE",
                "OTHER VALVE"
            ]
        },
        {
            "field":"fitting_category",
            "description": "Specific Category if category is fittings or flanges",
            "options":["180 RED RETURN LR",
            "180 RETURN LR",
            "180 RETURN SR",
            "45 ELBOW",
            "45 SR ELBOW",
            "90 LR ELBOW",
            "90 LR RED ELBOW",
            "90 SR ELBOW",
            "BUSHING",
            "BUSHING REDUCING",
            "CAP",
            "CONICAL STRAINER",
            "COUPLING",
            "COUPLING (HALF)",
            "COUPLING REDUCING",
            "COUPLING (HALF) REDUCING",
            "CROSS",
            "CROSS REDUCING",
            "ELBOLET BW",
            "ELBOLET PE",
            "ELBOLET THRD",
            "FLANGEOLET",
            "FLATOLET",
            "HEX PLUG",
            "HOSE COUPLING",
            "LATERAL",
            "LATERAL REDUCING",
            "LATROLET BW",
            "LATROLET PE",
            "LATROLET THRD",
            "NIPOLET BW",
            "NIPOLET PE",
            "NIPOLET THRD",
            "NIPPLE",
            "PIPE BEND",
            "REDUCER CONCENTRIC",
            "REDUCER ECCENTRIC",
            "ROUND PLUG",
            "SOCKOLET",
            "SPACER",
            "STUB END BW",
            "STUB END SW",
            "STUB END PE",
            "STUB END TE",
            "STUB END FLGD",
            "SQUARE PLUG",
            "SWAGE CON. REDUCER",
            "SWAGE ECC. REDUCER",
            "SWEEPOLET BW",
            "SWEEPOLET PE",
            "SWEEPOLET THRD",
            "TEE",
            "TEE REDUCING",
            "THREADOLET",
            "UNION",
            "WELDOLET",
            "WYE (STANDARD)",
            "WYE (STANDARD) REDUCING",
            "WYE (COMPACT)",
            "WYE (COMPACT) REDUCING",
            "BASKET STRAINER",
            "BUCKET STRAINER",
            "T-STRAINER",
            "T-STRAINER REDUCING",
            "Y-STRAINER",
            "Y-STRAINER REDUCING",
            "BELLOW",
            "BELLOW REDUCING",
            "OTHER STRAINER",
            "OTHER FITTING",
            "OTHER FITTING REDUCING",
            "LJ FLANGE FF",
            "LJ FLANGE LONG FF",
            "LJ FLANGE LONG RF",
            "LJ FLANGE ORIFICE FF",
            "LJ FLANGE ORIFICE RF",
            "LJ FLANGE REDUCING FF",
            "LJ FLANGE REDUCING RF",
            "LJ FLANGE RF",
            "LJ FLANGE RING JOINT FF",
            "LJ FLANGE RING JOINT RF",
            "SO FLANGE FF",
            "SO FLANGE LONG FF",
            "SO FLANGE LONG RF",
            "SO FLANGE ORIFICE FF",
            "SO FLANGE ORIFICE RF",
            "SO FLANGE REDUCING FF",
            "SO FLANGE REDUCING RF",
            "SO FLANGE RF",
            "SO FLANGE RING JOINT FF",
            "SO FLANGE RING JOINT RF",
            "THRD FLANGE FF",
            "THRD FLANGE LONG FF",
            "THRD FLANGE LONG RF",
            "THRD FLANGE ORIFICE FF",
            "THRD FLANGE ORIFICE RF",
            "THRD FLANGE REDUCING FF",
            "THRD FLANGE REDUCING RF",
            "THRD FLANGE RF",
            "THRD FLANGE RING JOINT FF",
            "THRD FLANGE RING JOINT RF",
            "WN FLANGE FF",
            "WN FLANGE LONG FF",
            "WN FLANGE LONG RF",
            "WN FLANGE ORIFICE FF",
            "WN FLANGE ORIFICE RF",
            "WN FLANGE REDUCING FF",
            "WN FLANGE REDUCING RF",
            "WN FLANGE RF",
            "WN FLANGE RING JOINT FF",
            "WN FLANGE RING JOINT RF",
            "SW FLANGE FF",
            "SW FLANGE LONG FF",
            "SW FLANGE LONG RF",
            "SW FLANGE ORIFICE FF",
            "SW FLANGE ORIFICE RF",
            "SW FLANGE REDUCING FF",
            "SW FLANGE REDUCING RF",
            "SW FLANGE RF",
            "SW FLANGE RING JOINT FF",
            "SW FLANGE RING JOINT RF",
            "BLIND FLANGE",
            "BLIND FLANGE - DRILL AND TAP",
            "BLIND FLANGE - DRILL ONLY",
            "FIGURE 8",
            "HAMMER BLIND",
            "SPADE BLIND",
            "PADDLE SPACER",
            "PUDDLE FLANGE",
            "SPECTACLE BLIND",
            "PLATE FLANGE",
            "ORIFICE PLATE",
            "FLANGE OTHER"
            ]
        },
        {
            "field":"weld_category",
            "description": "The type of weld",
            "options":[""]
        },
        {
            "field":"bolt_category",
            "description": "The type of bolts if category is bolts",
            "options":["STUD BOLTS",
            "CAP SCREWS",
            "HEAVY HEX NUT",
            "WASHER",
            "OTHER BOLT"]
        },
        {
            "field":"gasket_category",
            "description": "The type of gasket if category is gasket",
            "options":["GASKET",
            "INSULATION KIT",
            "OTHER GASKET"
            ]
        },
        {
            "field":"explanation_of_result",
            "description": "Short description on how you came up with your results.",
            "options":[""]
        },
        {
            "field":"additional_key_points",
            "description": "Short description of anything notable",
            "options":[""]
        },
        {
            "field":"review",
            "description": "If the item is outlier data or you do not understand, input here. This field indicates that a particular item requires further review. Leave a short but detailed description of why.",
            "options":[""]
        }

    ]
    
    return_format = """[{'material_description': '1 - STUD BOLT .625 * 3.25, ASTM A193 GR.B7', 'nps': '5/8', 'rfq_scope': 'BOLTS', 'unit_of_measure': 'EA', 'size1': '0.625', 'size2': '3.25', 'schedule': '', 'rating': '', 'astm': 'A193', 'grade': 'B7', 'ansme_ansi': '', 'material': 'CARBON STEEL', 'abbreviated_material': 'CS', 'coating': 'ZINC PLATED', 'forging': '', 'ends': '', 'item_tag': '', 'tie_point': '', 'pipe_category': '', 'valve_type': '', 'fitting_category': '', 'weld_category': '', 'bolt_category': 'STUD BOLTS', 'gasket_category': '', 'explanation_of_result': 'Classified as a bolt with ASTM and grade identified.', 'additional_key_points': '', 'review': ''}]"""

    # Convert the categorization table to a JSON string to include in the prompt
    categorization_info = json.dumps(categorization_table, indent=4)
    # Convert the categorization table to a list of field names
    field_names_list = [field['field'] for field in categorization_table]
    # Construct a string of field names for inclusion in the prompt
    field_names = "', '".join([field['field'] for field in categorization_table])

    # Construct the custom prompt with field names dynamically included
    custom_prompt = (
        #"You are an AI assistant designed to assist with estimating material takeoffs from isometric drawings for industrial construction projects. "
        # f"Given the 'material_description' '{desc}' and 'nps' '{nps}', categorize the item according to predefined categories. "
        f"Given the 'material_description' and 'nps', categorize the item according to predefined categories. "
        "For each row of data, return an output in a format where each material item is represented by a dictionary. "
        "Include all the following fields in each dictionary, ensuring 'material_description' and 'nps' are always included at the beginning: "
        f"'material_description', 'nps', {field_names}. "
        "The 'size1' field should always be set to the value of 'nps'. "
        "If a field does not apply or is uncertain, leave the value blank. "
        "This format, being a list of dictionaries, is suitable for direct loading into a pandas DataFrame. "
        "Each dictionary should represent a single material item, including all specified fields, even if some values are empty. "
        "Provide the output as a list of dictionaries, where each dictionary represents a row of data with key-value pairs corresponding to column names and cell values like this example row: "
        f"{return_format}"
    )
    
    # Create a message for the system role to ensure JSON format
    #system_message = {"role": "system", "content": f"Respond in JSON format. Use the following fields: {field_names_list}."}

    # Iterate over batches of descriptions to generate prompts and call the GPT model
    for batch_indices, batch_material_info in create_batches(df, BATCH_SIZE):
        # Initialize the messages list with the system message including categorization options
        messages = [
            {"role": "user", "content": f"Use the following fields and descriptions to classify the materials: {categorization_info}"}
        ]
        #messages.append({"role": "user", "content": custom_prompt})
    
        # Generate the prompt for each material description with corresponding nps
        for i, material in enumerate(batch_material_info, start=1):
            desc = material['material_description']
            nps = material['nps']
            
            prompt = f"Given the bill of material item 'material_description': '{desc}' with the Nominal Pipe Size ('nps'): '{nps}', classify it using the available options."
            messages.append({"role": "user", "content": custom_prompt})
            messages.append({"role": "user", "content": prompt})

        # At the end of generating messages for a batch
        total_material_descriptions = len(batch_material_info)
        messages.append({"role": "user", "content": f"Ensure you analyze and provide data for all {total_material_descriptions} material description and size combinations you have been provided."})
        
        # Print the messages to be sent to GPT
        print("\n\nGPT Input Messages: \n", json.dumps(messages, indent=2))
        
    
        
        # # Call the GPT model with the prepared messages
        for result in gpt4_turbo.gptCall_json(temperature, streaming, messages):
            # # Append the result if not empty (Original Version)
            # if result:
            #     results.append(result)
            #     print(result)


            # Check if result is not empty and is a dictionary
            if result and isinstance(result, dict):
                print("\nResult, Nested: \n", result)
                # Check if there's only one key and its value is a list of dictionaries
                if len(result) == 1 and isinstance(next(iter(result.values())), list):
                    # Extract the list directly
                    items = next(iter(result.values()))
                    # Append each item in the list to results
                    results.extend(items)
                else:
                    # If the structure is not nested, append the result directly
                    print("\nResult, not nested: \n", result)
                    results.append(result)
                
        print("\n\nResults 1\n", results)
        # Assuming `results` is your list of dictionaries or nested dictionaries
        normalized_results = []
        for item in results:
            # Check if 'data' key exists and its value is a list
            if isinstance(item, dict) and 'data' in item and isinstance(item['data'], list):
                # Extend the normalized list with the contents of 'data'
                normalized_results.extend(item['data'])
            elif isinstance(item, dict) and 'materials' in item and isinstance(item['materials'], list):  # Handling 'materials' key similarly
                normalized_results.extend(item['materials'])
            else:
                # If there's no 'data' key, add the item directly
                normalized_results.append(item)

        # Convert the normalized list of dictionaries to DataFrame
        results_df = pd.DataFrame(normalized_results)
        
        # Export DataFrame to CSV file
        results_df.to_excel("output.xlsx", index=False)


        # # Convert results to DataFrame and display
        # results_df = pd.DataFrame(results)
        print("\n\nCATEGORIZED RESULTS: \n", type(results),"\n\n",results)
        #print("\n\nCATEGORIZED RESULTS DF: \n", type(results),"\n\n",results_df)
        # print("\n\n")
    
    return results_df


