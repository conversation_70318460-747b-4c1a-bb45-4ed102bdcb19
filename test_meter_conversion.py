"""
Test script for meter conversion functionality in convert_quantity_to_float
"""

from data_conversions import convert_quantity_to_float, convert_meters_to_feet

def test_meter_conversions():
    """Test various meter and millimeter input formats"""
    
    print("=== Testing Meter Conversion Logic ===\n")
    
    # Test cases: (input, expected_feet, description)
    test_cases = [
        # Meter tests
        ("20.5M", 67.257, "20.5 meters (no space)"),
        ("20.5 M", 67.257, "20.5 meters (with space)"),
        ("20.5m", 67.257, "20.5 meters (lowercase)"),
        ("20.5 m", 67.257, "20.5 meters (lowercase with space)"),
        ("1M", 3.28084, "1 meter"),
        ("10M", 32.8084, "10 meters"),
        
        # Millimeter tests  
        ("2050MM", 6.726, "2050 millimeters (no space)"),
        ("2050 MM", 6.726, "2050 millimeters (with space)"),
        ("2050mm", 6.726, "2050 millimeters (lowercase)"),
        ("2050 mm", 6.726, "2050 millimeters (lowercase with space)"),
        ("1000MM", 3.281, "1000 millimeters"),
        
        # Non-metric tests (should use existing logic)
        ("20.5", 20.5, "Plain number (should remain unchanged)"),
        ("21'3\"", 21.25, "Feet and inches"),
        ("5'", 5.0, "Feet only"),
        ("12\"", 1.0, "Inches only"),
    ]
    
    print("Testing convert_meters_to_feet function:")
    print("-" * 50)
    
    for input_val, expected, description in test_cases[:11]:  # Only metric tests
        if 'M' in input_val.upper():
            result = convert_meters_to_feet(input_val)
            print(f"Input: '{input_val:8}' -> {result:8.3f} feet | Expected: {expected:8.3f} | {description}")
            if result is not None and abs(result - expected) < 0.01:
                print("  ✓ PASS")
            else:
                print("  ✗ FAIL")
            print()
    
    print("\nTesting convert_quantity_to_float function (full integration):")
    print("-" * 60)
    
    for input_val, expected, description in test_cases:
        result = convert_quantity_to_float(input_val)
        print(f"Input: '{input_val:8}' -> {result:8.3f} feet | Expected: {expected:8.3f} | {description}")
        if abs(result - expected) < 0.01:
            print("  ✓ PASS")
        else:
            print("  ✗ FAIL")
        print()

if __name__ == "__main__":
    test_meter_conversions()
