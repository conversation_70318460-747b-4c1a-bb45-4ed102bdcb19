from normalize_description import normalize_rating

# Test cases for the rating format
test_cases = [
    "CAP, ASME B16.11, NPT, #3000, A182 F304/304L",  # The format we're adding support for
    "FLANGE, WELD NECK, CLASS 150, RF, A105",  # Standard CLASS format
    "VALVE, BALL, 300#, RF, A105",  # Standard number# format
    "GASKET, SPIRAL WOUND, # 300, 316 SS",  # The new format with space
    "FITTING, ELBOW, #150, A105",  # The new format without space
    "PIPE, SEAMLESS, 2500 PSI, A106 GR.B"  # PSI format
]

# Test the fix
for test in test_cases:
    print(f"\nTesting: {test}")
    normalized, metadata, review, match_type = normalize_rating(test, review=True)
    print(f"Normalized: {normalized}")
    print(f"Metadata: {metadata}")
    print(f"Review: {review}")
    print(f"Match type: {match_type}")
