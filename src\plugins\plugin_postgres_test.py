from src.atom.pg_database import pg_connection


def plugin_test_connection():
    """
    Tests PostgreSQL connection to NEON.

    """
    print("Tests PostgreSQL connection to NEON.")
    try:
        with pg_connection.get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result[0] == 1:
                    print("Connection test successful")
                    return True
    except Exception as e:
        print(f"Connection test failed: {e}")
        return False