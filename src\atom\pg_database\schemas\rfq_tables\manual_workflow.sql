
/* Updated Workflow
Use manual functions to sync RFQ and RFQ_INPUT instead of a full trigger-based sync
1. Upload the RFQ, then <PERSON><PERSON> if present using workbook_db_importer.py
2. Ensure the RFQ_INPUT records are created (Should be created from the trigger)
3. Clear the public.atem_rfq table. Current function generate_rfq_from_bom() correctly creates 
    them using size1 and size2. Function in workbook_db_imported.py needs some adjustment
    
    - ENSURE trg_sync_rfq_to_input is disabled!

4. Run SELECT * FROM generate_rfq_from_bom(project_id) to create RFQ records
5. Run SELECT * FROM link_rfq_to_rfq_input(project_id) to link RFQ_INPUT to RFQ
6. Run SELECT * FROM link_bom_references(project_id, force_relink (boolean)) to link BOM references to RFQ
7. Run SELECT * FROM refresh_rfq_input_data(project_id) to refresh RFQ_INPUT data
8. Upload the General Data using workbook_db_importer.py
9. Run SELECT * FROM link_bom_to_general(project_id, force_relink (boolean)) to link BOM to General
10. Validate Quantities match between RFQ_INPUT and RFQ by running `validate_bom_rfq_totals(project_id)`
    'category' null is currently displaying a mismatch but is correct (2 rows of null cancel each other out)
11. Build aggregated public.general with `manage_bom_to_general_aggregation(7, FALSE)`.
    *** ENSURE TO USE FALSE FOR THE SECOND PARAMETER TRUE DOES NOT WORK CORRECTLY. 
    CONSIDER PLACING RESULTS IN A NEW TABLE INSTEAD OF TEMPORARY TABLE ***
9. Verify counts with:
SELECT
    SUM(length) AS total_length,
    SUM(elbows_90) AS total_elbows_90,
    SUM(elbows_45) AS total_elbows_45,
    SUM(bevels) AS total_bevels,
    SUM(tees) AS total_tees,
    SUM(reducers) AS total_reducers,
    SUM(caps) AS total_caps,
    SUM(flanges) AS total_flanges,
    SUM(valves_flanged) AS total_valves_flanged,
    SUM(valves_welded) AS total_valves_welded,
    SUM(cut_outs) AS total_cut_outs,
    SUM(supports) AS total_supports,
    SUM(bends) AS total_bends,
    SUM(union_couplings) AS total_union_couplings,
    SUM(expansion_joints) AS total_expansion_joints,
    SUM(field_welds) AS total_field_welds,
    SUM(calculated_eq_length) AS total_calculated_eq_length,
    SUM(calculated_area) AS total_calculated_area
FROM manage_bom_to_general_aggregation(7, FALSE);

*/


/* Creates the RFQ in public.atem_rfq.
Unique items are: project_id, material_description, size1, size2
*** Need to carry over any existing BOM categories before merging if any ***
*/

/*
-- USAGE --
/*SELECT * FROM generate_rfq_from_bom(8);

-- Links rfq to rfq_input
SELECT * FROM link_rfq_to_rfq_input(8);

-- Links bom to rfq record
SELECT * FROM link_bom_references(8, True);

/*
-- ENSURE BOM & RFQ COMPONENETS AND EQ/AREA CALCULATION ARE REFRESHED!!!
Ensure these BOM Triggers are enabled:
	- trg_update_bom_component_calculations
	- trg_update_rfq_quantities
	Force a refresh:
		-- Force update on all BOM records to trigger calculations
		-- We'll update the general_category to itself to trigger the calculations
		UPDATE public.bom
		SET 
		    general_category = general_category
		WHERE 
		    project_id = 8
		    AND general_category IS NOT NULL;

Ensure these atem_rfq Triggers are enabled:
	- trg_update_component_calculations
	- trg_update_rfq_categories
	- trg_update_rfq_profile_id
*/
-- Force a refresh of rfq_input data -> atem_rfq -> bom
SELECT * FROM refresh_rfq_input_data(8);

SELECT * FROM validate_bom_rfq_totals(8);
*/

-- Links bom to general record
SELECT * FROM link_bom_to_general(8, True)

-- Verify General
SELECT
    SUM(length) AS total_length,
    SUM(elbows_90) AS total_elbows_90,
    SUM(elbows_45) AS total_elbows_45,
    SUM(bevels) AS total_bevels,
    SUM(tees) AS total_tees,
    SUM(reducers) AS total_reducers,
    SUM(caps) AS total_caps,
    SUM(flanges) AS total_flanges,
    SUM(valves_flanged) AS total_valves_flanged,
    SUM(valves_welded) AS total_valves_welded,
    SUM(cut_outs) AS total_cut_outs,
    SUM(supports) AS total_supports,
    SUM(bends) AS total_bends,
    SUM(union_couplings) AS total_union_couplings,
    SUM(expansion_joints) AS total_expansion_joints,
    SUM(field_welds) AS total_field_welds,
    SUM(calculated_eq_length) AS total_calculated_eq_length,
    SUM(calculated_area) AS total_calculated_area
FROM manage_bom_to_general_aggregation(8, FALSE);

*/

CREATE OR REPLACE FUNCTION generate_rfq_from_bom(project_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
    rfq_count INTEGER := 0;
BEGIN
    -- Insert into atem_rfq table, avoiding duplicates based on size1/size2
    INSERT INTO public.atem_rfq (
        material_description,
        size,  -- We'll still populate this for display purposes
        size1,
        size2,
        rfq_scope,
        quantity,
        project_id,
        profile_id,
        created_at,
        updated_at,
        created_by,
        updated_by
    )
    SELECT 
        b.material_description,
        CASE 
            WHEN b.size2 IS NOT NULL THEN b.size1::TEXT || ' x ' || b.size2::TEXT
            ELSE b.size1::TEXT
        END AS size,  -- Generate size string from size1/size2
        b.size1,
        b.size2,
        b.rfq_scope,
        SUM(b.quantity) AS quantity,
        b.project_id,
        b.profile_id,
        NOW(),
        NOW(),
        'system',
        'system'
    FROM 
        public.bom b
    WHERE 
        b.project_id = project_id_param
        AND b.deleted IS NOT TRUE
    GROUP BY 
        LOWER(b.material_description),  -- Group by lowercase material description
        b.size1,
        b.size2,
        b.rfq_scope,
        b.project_id,
        b.profile_id,
        b.material_description  -- Keep original case for insertion
    HAVING 
        NOT EXISTS (
            SELECT 1 
            FROM public.atem_rfq r
            WHERE r.project_id = b.project_id
            AND LOWER(r.material_description) = LOWER(b.material_description)
            AND COALESCE(r.size1, -1) = COALESCE(b.size1, -1)
            AND COALESCE(r.size2, -1) = COALESCE(b.size2, -1)
            AND r.deleted IS NOT TRUE
        );

    -- Get count of inserted records
    GET DIAGNOSTICS rfq_count = ROW_COUNT;

    RETURN rfq_count;
END;
$$ LANGUAGE plpgsql;

/* Link Records to the public.rfq_input table
These are deduplicated items by project_id, material_description. Updates propogate to RFQ
*/
CREATE OR REPLACE FUNCTION link_rfq_to_rfq_input(project_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
    update_count INTEGER := 0;
BEGIN
    -- Update atem_rfq records to set rfq_input_id by matching on project_id and material_description
    UPDATE public.atem_rfq r
    SET 
        rfq_input_id = i.id,
        updated_at = NOW(),
        updated_by = 'system'
    FROM 
        public.atem_rfq_input i
    WHERE 
        r.project_id = project_id_param
        AND i.project_id = r.project_id
        AND LOWER(i.material_description) = LOWER(r.material_description)
        AND r.rfq_input_id IS NULL
        AND r.deleted IS NOT TRUE
        AND i.deleted IS NOT TRUE;

    -- Get count of updated records
    GET DIAGNOSTICS update_count = ROW_COUNT;

    RETURN update_count;
END;
$$ LANGUAGE plpgsql;

-- Then link them to RFQ input records
SELECT link_rfq_to_rfq_input(7);


/* Manually build the Full General Data table 
SELECT * FROM manage_bom_to_general_aggregation_full(7, FALSE)
*/
DROP FUNCTION IF EXISTS manage_bom_to_general_aggregation_full(integer, boolean);

CREATE OR REPLACE FUNCTION manage_bom_to_general_aggregation_full(p_project_id INTEGER, p_commit BOOLEAN)
RETURNS TABLE (
    -- We'll return all columns from general plus our tracking columns
    id INTEGER,
    sys_filename VARCHAR(255),
    sys_path VARCHAR(1024),
    project_id INTEGER,
    pdf_id INTEGER,
    pdf_page INTEGER,
    annot_markups TEXT,
    area TEXT,
    avg_elevation VARCHAR(50),
    block_coordinates TEXT,
    base_material VARCHAR(50),
    client_document_id VARCHAR(100),
    coordinates TEXT,
    design_code VARCHAR(100),
    document_description TEXT,
    document_id VARCHAR(100),
    document_title VARCHAR(255),
    drawing VARCHAR(100),
    dp1 VARCHAR(50),
    dp2 VARCHAR(50),
    dt1 VARCHAR(50),
    dt2 VARCHAR(50),
    op1 VARCHAR(50),
    op2 VARCHAR(50),
    opt1 VARCHAR(50),
    opt2 VARCHAR(50),
    elevation TEXT,
    flange_id VARCHAR(100),
    heat_trace VARCHAR(50),
    insulation_spec VARCHAR(100),
    insulation_thickness VARCHAR(50),
    iso_number VARCHAR(100),
    iso_type VARCHAR(100),
    line_number VARCHAR(100),
    max_elevation VARCHAR(50),
    medium_code VARCHAR(100),
    min_elevation VARCHAR(50),
    mod_date VARCHAR(50),
    paint_spec VARCHAR(100),
    pid VARCHAR(100),
    pipe_spec VARCHAR(100),
    pipe_standard VARCHAR(100),
    process_line_list VARCHAR(100),
    process_unit VARCHAR(100),
    project_no VARCHAR(100),
    project_name VARCHAR(255),
    pwht VARCHAR(50),
    revision VARCHAR(50),
    sequence VARCHAR(50),
    service VARCHAR(100),
    sheet VARCHAR(50),
    size DECIMAL(12,3),
    sys_build VARCHAR(100),
    sys_layout_valid VARCHAR(50),
    sys_document VARCHAR(255),
    sys_document_name VARCHAR(255),
    system VARCHAR(100),
    test_pressure VARCHAR(50),
    test_type VARCHAR(50),
    total_sheets VARCHAR(50),
    unit VARCHAR(50),
    vendor_document_id VARCHAR(100),
    weld_id VARCHAR(100),
    weld_class VARCHAR(100),
    x_coord VARCHAR(50),
    xray VARCHAR(50),
    y_coord VARCHAR(50),
    paint_color VARCHAR(100),
    cwp VARCHAR(100),
    length DECIMAL,
    calculated_area DECIMAL,
    calculated_eq_length DECIMAL,
    elbows_90 DECIMAL,
    elbows_45 DECIMAL,
    bevels DECIMAL,
    tees DECIMAL,
    reducers DECIMAL,
    caps DECIMAL,
    flanges DECIMAL,
    valves_flanged DECIMAL,
    valves_welded DECIMAL,
    cut_outs DECIMAL,
    supports DECIMAL,
    bends DECIMAL,
    union_couplings DECIMAL,
    expansion_joints DECIMAL,
    field_welds DECIMAL,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    stress_req VARCHAR(50),
    change_type TEXT,
    action_taken TEXT,
    field_1 VARCHAR(255),
    field_2 VARCHAR(255),
    field_3 VARCHAR(255),
    field_4 VARCHAR(255),
    field_5 VARCHAR(255),
    field_6 VARCHAR(255),
    field_7 VARCHAR(255),
    field_8 VARCHAR(255),
    field_9 VARCHAR(255),
    field_10 VARCHAR(255),
    field_11 VARCHAR(255),
    field_12 VARCHAR(255),
    field_13 VARCHAR(255),
    field_14 VARCHAR(255),
    field_15 VARCHAR(255),
    field_16 VARCHAR(255),
    field_17 VARCHAR(255),
    field_18 VARCHAR(255),
    field_19 VARCHAR(255),
    field_20 VARCHAR(255),
    field_21 VARCHAR(255),
    field_22 VARCHAR(255),
    field_23 VARCHAR(255),
    field_24 VARCHAR(255),
    field_25 VARCHAR(255),      
    field_26 VARCHAR(255)
    ) AS $$
DECLARE
    v_changes INTEGER;
    v_row RECORD;
    v_template_id INTEGER;
    v_has_non_zero BOOLEAN;
    v_action_taken TEXT;
    v_column_list TEXT;
    v_insert_columns TEXT;
    v_select_columns TEXT;
BEGIN
    -- Set action taken based on commit parameter
    IF p_commit THEN
        v_action_taken := 'Committed to database';
    ELSE
        v_action_taken := 'Preview only';
    END IF;

    -- Drop temporary tables if they exist
    DROP TABLE IF EXISTS temp_general;
    DROP TABLE IF EXISTS bom_agg;
    
    -- Create a temp copy of the general table for our project
    CREATE TEMP TABLE temp_general ON COMMIT DROP AS
    SELECT g.* FROM public.general g WHERE g.project_id = p_project_id;
    

    -- Add change_type and action_taken columns without default values
	ALTER TABLE temp_general ADD COLUMN change_type TEXT DEFAULT 'unchanged';
	ALTER TABLE temp_general ADD COLUMN action_taken TEXT;

	-- Set action_taken column explicitly
	UPDATE temp_general SET action_taken = v_action_taken;
    
    -- Create table with BOM aggregations by PDF and size
    CREATE TEMP TABLE bom_agg AS
    WITH mapping AS (
        SELECT DISTINCT m.general_category, m.map_to_gen 
        FROM public.atem_bom_component_mapping m
        WHERE m.profile_id = (
            SELECT ap.profile_id FROM public.atem_projects ap WHERE ap.id = p_project_id
        )
        AND m.map_to_gen IS NOT NULL
        AND m.general_category IS NOT NULL
    )
    SELECT 
        b.pdf_id,
        b.pdf_page,
        COALESCE(b.size1, b.size2) AS size,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'length'
        ) THEN b.quantity ELSE 0 END) AS length,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'elbows_90'
        ) THEN b.quantity ELSE 0 END) AS elbows_90,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'elbows_45'
        ) THEN b.quantity ELSE 0 END) AS elbows_45,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'bevels'
        ) THEN b.quantity ELSE 0 END) AS bevels,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'tees'
        ) THEN b.quantity ELSE 0 END) AS tees,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'reducers'
        ) THEN b.quantity ELSE 0 END) AS reducers,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'caps'
        ) THEN b.quantity ELSE 0 END) AS caps,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'flanges'
        ) THEN b.quantity ELSE 0 END) AS flanges,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'valves_flanged'
        ) THEN b.quantity ELSE 0 END) AS valves_flanged,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'valves_welded'
        ) THEN b.quantity ELSE 0 END) AS valves_welded,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'cut_outs'
        ) THEN b.quantity ELSE 0 END) AS cut_outs,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'supports'
        ) THEN b.quantity ELSE 0 END) AS supports,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'bends'
        ) THEN b.quantity ELSE 0 END) AS bends,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'union_couplings'
        ) THEN b.quantity ELSE 0 END) AS union_couplings,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'expansion_joints'
        ) THEN b.quantity ELSE 0 END) AS expansion_joints,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'field_welds'
        ) THEN b.quantity ELSE 0 END) AS field_welds,
        SUM(COALESCE(b.calculated_eq_length, 0)) AS calculated_eq_length,
        SUM(COALESCE(b.calculated_area, 0)) AS calculated_area
    FROM 
        public.bom b
    WHERE 
        b.project_id = p_project_id
        AND b.deleted IS NOT TRUE
    GROUP BY 
        b.pdf_id, b.pdf_page, COALESCE(b.size1, b.size2);
    
    -- Update existing rows in general table
    UPDATE temp_general tg
    SET
        length = ba.length,
        elbows_90 = ba.elbows_90,
        elbows_45 = ba.elbows_45,
        bevels = ba.bevels,
        tees = ba.tees,
        reducers = ba.reducers,
        caps = ba.caps,
        flanges = ba.flanges,
        valves_flanged = ba.valves_flanged,
        valves_welded = ba.valves_welded,
        cut_outs = ba.cut_outs,
        supports = ba.supports,
        bends = ba.bends,
        union_couplings = ba.union_couplings,
        expansion_joints = ba.expansion_joints,
        field_welds = ba.field_welds,
        calculated_eq_length = ba.calculated_eq_length,
        calculated_area = ba.calculated_area,
        change_type = 'updated'
    FROM 
        bom_agg ba
    WHERE 
        tg.pdf_id = ba.pdf_id AND
        tg.pdf_page = ba.pdf_page AND
        tg.size = ba.size;
    
    -- Find combinations that need new rows
    FOR v_row IN 
        SELECT ba.pdf_id, ba.pdf_page, ba.size
        FROM bom_agg ba
        WHERE NOT EXISTS (
            SELECT 1 
            FROM temp_general tg
            WHERE tg.pdf_id = ba.pdf_id AND
                  tg.pdf_page = ba.pdf_page AND
                  tg.size = ba.size
        )
    LOOP
        -- Check if we have at least one non-zero value in the aggregation
        SELECT 
            (ba.length > 0 OR ba.elbows_90 > 0 OR ba.elbows_45 > 0 OR 
             ba.bevels > 0 OR ba.tees > 0 OR ba.reducers > 0 OR 
             ba.caps > 0 OR ba.flanges > 0 OR ba.valves_flanged > 0 OR 
             ba.valves_welded > 0 OR ba.cut_outs > 0 OR ba.supports > 0 OR 
             ba.bends > 0 OR ba.union_couplings > 0 OR ba.expansion_joints > 0 OR 
             ba.field_welds > 0 OR ba.calculated_eq_length > 0 OR ba.calculated_area > 0)
        INTO v_has_non_zero
        FROM bom_agg ba
        WHERE ba.pdf_id = v_row.pdf_id AND ba.pdf_page = v_row.pdf_page AND ba.size = v_row.size;
        
        -- Only proceed if we have at least one non-zero value
        IF v_has_non_zero THEN
            -- Find a template to use from the same PDF and page
            SELECT tg.id INTO v_template_id
            FROM temp_general tg
            WHERE tg.pdf_id = v_row.pdf_id AND tg.pdf_page = v_row.pdf_page
            LIMIT 1;
            
            -- If no template found for this page, use one from the same PDF
            IF v_template_id IS NULL THEN
                SELECT tg.id INTO v_template_id
                FROM temp_general tg
                WHERE tg.pdf_id = v_row.pdf_id
                LIMIT 1;
            END IF;
            
            -- If we found a template, create a new row
            IF v_template_id IS NOT NULL THEN
                -- Get column list dynamically
                SELECT string_agg(column_name, ', ') 
                INTO v_column_list
                FROM information_schema.columns
                WHERE table_name = 'general' AND table_schema = 'public';
                
                -- Create insert and select column lists
                v_insert_columns := v_column_list || ', change_type, action_taken';
                v_select_columns := 'tg.' || replace(v_column_list, ', ', ', tg.');
                
                -- Use dynamic  to insert all columns
                EXECUTE '
                INSERT INTO temp_general (
                    project_id, pdf_id, pdf_page, size,
                    -- Metadata fields from template
                    annot_markups, area, avg_elevation, block_coordinates, base_material, client_document_id,
                    coordinates, design_code, document_description, document_id, document_title,
                    drawing, dp1, dp2, dt1, dt2, op1, op2, opt1, opt2,
                    elevation, flange_id, heat_trace, insulation_spec, insulation_thickness,
                    iso_number, iso_type, line_number, max_elevation, medium_code, min_elevation,
                    mod_date, paint_spec, pid, pipe_spec, pipe_standard, process_line_list,
                    process_unit, project_no, project_name, pwht, revision, sequence, service,
                    sheet, sys_build, sys_layout_valid, sys_document, sys_document_name,
                    sys_filename, sys_path, system, test_pressure, test_type, total_sheets, unit, vendor_document_id,
                    weld_id, weld_class, x_coord, xray, y_coord, paint_color, cwp,
                    -- Quantity fields from BOM aggregation
                    length, elbows_90, elbows_45, bevels, tees, reducers, caps, flanges,
                    valves_flanged, valves_welded, cut_outs, supports, bends,
                    union_couplings, expansion_joints, field_welds,
                    calculated_eq_length, calculated_area,
                    -- Timestamps and change tracking
                    created_at, updated_at, change_type, action_taken,
                    -- General fields
                    field_1, field_2, field_3, field_4, field_5, field_6, field_7, field_8, field_9, field_10,
                    field_11, field_12, field_13, field_14, field_15, field_16, field_17, field_18, field_19, field_20,
                    field_21, field_22, field_23, field_24, field_25, field_26
                )
                SELECT
                    tg.project_id, $1, $2, $3, 
                    -- Metadata fields from template
                    tg.annot_markups, tg.area, tg.avg_elevation, tg.block_coordinates, tg.base_material, tg.client_document_id,
                    tg.coordinates, tg.design_code, tg.document_description, tg.document_id, tg.document_title,
                    tg.drawing, tg.opt1, tg.opt2, tg.dt1, tg.dt2, tg.op1, tg.op2, tg.opt1, tg.opt2,
                    tg.elevation, tg.flange_id, tg.heat_trace, tg.insulation_spec, tg.insulation_thickness,
                    tg.iso_number, tg.iso_type, tg.line_number, tg.max_elevation, tg.medium_code, tg.min_elevation,
                    tg.mod_date, tg.paint_spec, tg.pid, tg.pipe_spec, tg.pipe_standard, tg.process_line_list,
                    tg.process_unit, tg.project_no, tg.project_name, tg.pwht, tg.revision, tg.sequence, tg.service,
                    tg.sheet, tg.sys_build, tg.sys_layout_valid, tg.sys_document, tg.sys_document_name,
                    tg.sys_filename, tg.sys_path, tg.system, tg.test_pressure, tg.test_type, tg.total_sheets, tg.unit, tg.vendor_document_id,
                    tg.weld_id, tg.weld_class, tg.x_coord, tg.xray, tg.y_coord, tg.paint_color, tg.cwp,
                    -- Quantity fields from BOM aggregation
                    ba.length, ba.elbows_90, ba.elbows_45, ba.bevels, ba.tees, ba.reducers, ba.caps, ba.flanges,
                    ba.valves_flanged, ba.valves_welded, ba.cut_outs, ba.supports, ba.bends,
                    ba.union_couplings, ba.expansion_joints, ba.field_welds,
                    ba.calculated_eq_length, ba.calculated_area,
                    -- Timestamps and change tracking
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ''inserted''::TEXT, $4,
                    -- General fields
                    field_1, field_2, field_3, field_4, field_5, field_6, field_7, field_8, field_9, field_10,
                    field_11, field_12, field_13, field_14, field_15, field_16, field_17, field_18, field_19, field_20,
                    field_21, field_22, field_23, field_24, field_25, field_26
                FROM
                    temp_general tg
                    JOIN bom_agg ba ON ba.pdf_id = $1 AND ba.pdf_page = $2 AND ba.size = $3
                WHERE
                    tg.id = $5
                '
                USING v_row.pdf_id, v_row.pdf_page, v_row.size, v_action_taken, v_template_id;
            END IF;
        END IF;
    END LOOP;
    
    -- If commit is true, update the actual general table
    IF p_commit THEN
        -- Delete existing rows for this project
        DELETE FROM public.general WHERE project_id = p_project_id;
        
        -- Get column list for insert
        SELECT string_agg(column_name, ', ') 
        INTO v_column_list
        FROM information_schema.columns
        WHERE table_name = 'general' AND table_schema = 'public';
        
        -- Insert all rows from temp_general (excluding our added columns)
        EXECUTE '
        INSERT INTO public.general (' || v_column_list || ')
        SELECT ' || v_column_list || '
        FROM temp_general tg
        WHERE tg.change_type != ''unchanged''
        ';
    END IF;
    
    -- Count changes
    SELECT COUNT(*) INTO v_changes FROM temp_general WHERE temp_general.change_type != 'unchanged';
    
    -- Return results
    IF v_changes = 0 THEN
        RETURN QUERY
        SELECT 
            tg.id,
		    tg.sys_filename,
		    tg.sys_path,
		    tg.project_id,
		    tg.pdf_id,
		    tg.pdf_page,
		    tg.annot_markups,
		    tg.area,
		    tg.avg_elevation,
		    tg.block_coordinates,
            tg.base_material,
		    tg.client_document_id,
		    tg.coordinates,
		    tg.design_code,
		    tg.document_description,
		    tg.document_id,
		    tg.document_title,
		    tg.drawing,
            tg.dp1,
            tg.dp2,
            tg.dt1,
            tg.dt2,
            tg.op1,
            tg.op2,
            tg.opt1,
            tg.opt2,
		    tg.elevation,
		    tg.flange_id,
		    tg.heat_trace,
		    tg.insulation_spec,
		    tg.insulation_thickness,
		    tg.iso_number,
		    tg.iso_type,
		    tg.line_number,
		    tg.max_elevation,
		    tg.medium_code,
		    tg.min_elevation,
		    tg.mod_date,
		    tg.paint_spec,
		    tg.pid,
		    tg.pipe_spec,
		    tg.pipe_standard,
		    tg.process_line_list,
		    tg.process_unit,
		    tg.project_no,
		    tg.project_name,
		    tg.pwht,
		    tg.revision,
		    tg.sequence,
		    tg.service,
		    tg.sheet,
		    tg.size,
		    tg.sys_build,
		    tg.sys_layout_valid,
		    tg.sys_document,
		    tg.sys_document_name,
		    tg.system,
            tg.test_type,
            tg.test_pressure,
		    tg.total_sheets,
		    tg.unit,
		    tg.vendor_document_id,
		    tg.weld_id,
		    tg.weld_class,
		    tg.x_coord,
		    tg.xray,
		    tg.y_coord,
		    tg.paint_color,
		    tg.cwp,
		    tg.length,
		    tg.calculated_area,
		    tg.calculated_eq_length,
		    tg.elbows_90,
		    tg.elbows_45,
		    tg.bevels,
		    tg.tees,
		    tg.reducers,
		    tg.caps,
		    tg.flanges,
		    tg.valves_flanged,
		    tg.valves_welded,
		    tg.cut_outs,
		    tg.supports,
		    tg.bends,
		    tg.union_couplings,
		    tg.expansion_joints,
		    tg.field_welds,
		    tg.created_at,
		    tg.updated_at,
		    tg.stress_req,
		    tg.change_type,
		    tg.action_taken,
            tg.field_1,
            tg.field_2,
            tg.field_3,
            tg.field_4,
            tg.field_5,
            tg.field_6,
            tg.field_7,
            tg.field_8,
            tg.field_9,
            tg.field_10,
            tg.field_11,
            tg.field_12,
            tg.field_13,
            tg.field_14,
            tg.field_15,
            tg.field_16,
            tg.field_17,
            tg.field_18,
            tg.field_19,
            tg.field_20,
            tg.field_21,
            tg.field_22,
            tg.field_23,
            tg.field_24,
            tg.field_25,
            tg.field_26
        FROM 
            temp_general tg
        LIMIT 10;
    ELSE
        RETURN QUERY
        SELECT 
            tg.id,
		    tg.sys_filename,
		    tg.sys_path,
		    tg.project_id,
		    tg.pdf_id,
		    tg.pdf_page,
		    tg.annot_markups,
		    tg.area,
		    tg.avg_elevation,
		    tg.block_coordinates,
            tg.base_material,
		    tg.client_document_id,
		    tg.coordinates,
		    tg.design_code,
		    tg.document_description,
		    tg.document_id,
		    tg.document_title,
		    tg.drawing,
            tg.dp1,
            tg.dp2,
            tg.dt1,
            tg.dt2,
            tg.op1,
            tg.op2,
            tg.opt1,
            tg.opt2,
		    tg.elevation,
		    tg.flange_id,
		    tg.heat_trace,
		    tg.insulation_spec,
		    tg.insulation_thickness,
		    tg.iso_number,
		    tg.iso_type,
		    tg.line_number,
		    tg.max_elevation,
		    tg.medium_code,
		    tg.min_elevation,
		    tg.mod_date,
		    tg.paint_spec,
		    tg.pid,
		    tg.pipe_spec,
		    tg.pipe_standard,
		    tg.process_line_list,
		    tg.process_unit,
		    tg.project_no,
		    tg.project_name,
		    tg.pwht,
		    tg.revision,
		    tg.sequence,
		    tg.service,
		    tg.sheet,
		    tg.size,
		    tg.sys_build,
		    tg.sys_layout_valid,
		    tg.sys_document,
		    tg.sys_document_name,
		    tg.system,
            tg.test_type,
            tg.test_pressure,
		    tg.total_sheets,
		    tg.unit,
		    tg.vendor_document_id,
		    tg.weld_id,
		    tg.weld_class,
		    tg.x_coord,
		    tg.xray,
		    tg.y_coord,
		    tg.paint_color,
		    tg.cwp,
		    tg.length,
		    tg.calculated_area,
		    tg.calculated_eq_length,
		    tg.elbows_90,
		    tg.elbows_45,
		    tg.bevels,
		    tg.tees,
		    tg.reducers,
		    tg.caps,
		    tg.flanges,
		    tg.valves_flanged,
		    tg.valves_welded,
		    tg.cut_outs,
		    tg.supports,
		    tg.bends,
		    tg.union_couplings,
		    tg.expansion_joints,
		    tg.field_welds,
		    tg.created_at,
		    tg.updated_at,
		    tg.stress_req,
		    tg.change_type,
		    tg.action_taken,
            tg.field_1,
            tg.field_2,
            tg.field_3,
            tg.field_4,
            tg.field_5,
            tg.field_6,
            tg.field_7,
            tg.field_8,
            tg.field_9,
            tg.field_10,
            tg.field_11,
            tg.field_12,
            tg.field_13,
            tg.field_14,
            tg.field_15,
            tg.field_16,
            tg.field_17,
            tg.field_18,
            tg.field_19,
            tg.field_20,
            tg.field_21,
            tg.field_22,
            tg.field_23,
            tg.field_24,
            tg.field_25,
            tg.field_26
        FROM 
            temp_general tg
        WHERE 
            tg.change_type != 'unchanged'
        ORDER BY
            tg.pdf_id, tg.pdf_page, tg.size;
    END IF;
END;
$$ LANGUAGE plpgsql;