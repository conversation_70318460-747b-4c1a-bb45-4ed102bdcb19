import re
from bom_patterns import SchedulePatterns
from normalize_description import normalize_schedule

# Test cases for compound/cross schedules
test_cases = [
    "PIPE, SCH 10S X SCH 40S, 01.500",
    "PIPE, SCHEDULE 10S X SCHEDULE 40S, 01.500",
    "PIPE, SCH. 10S X SCH. 40S, 01.500",
    "PIPE, SCH 10S x SCH 40S, 01.500",
    "PIPE, SCH 10S/SCH 40S, 01.500",
    "PIPE, SCH 10S-SCH 40S, 01.500",
    "PIPE, SCH 10S TO SCH 40S, 01.500",
    "PIPE, SCH 10S AND SCH 40S, 01.500",
    "PIPE, SCH 10S BY SCH 40S, 01.500",
    "PIPE, SCH1-10S SCH2-40S, 01.500",
    "PIPE, SCH1 10S SCH2 40S, 01.500",
    "PIPE, S-10S S-40S, 01.500",
    "PIPE, 10S X 40S, 01.500",
    "PIPE, 10S/40S, 01.500",
    "PIPE, 10S-40S, 01.500",
    
    # Edge cases that should NOT be detected as schedules
    "PIPE, 3 X 4, 01.500",  # This is likely a size, not a schedule
    "PIPE, 1/2, 01.500",    # This is likely a size, not a schedule
    "PIPE, 3-4, 01.500",    # This is likely a size, not a schedule
]

print("Testing schedule normalization for compound/cross schedules:")
for test in test_cases:
    normalized, tag = normalize_schedule(test)
    print(f"\nOriginal: {test}")
    print(f"Normalized: {normalized}")
    print(f"Tag: {tag}")
