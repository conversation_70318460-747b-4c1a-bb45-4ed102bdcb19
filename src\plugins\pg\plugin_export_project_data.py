"""
A plugin to read data from a PostgreSQL database and export using an export profile
"""

import os
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import time
import re
from natsort import natsort_keygen

from src.atom.pg_database.pg_connection import get_db_connection, DatabaseConfig


def read_export_profile(export_profile_path: str) -> Dict[str, Any]:
    """
    Read an export profile from an Excel file.

    Args:
        export_profile_path (str): Path to the export profile Excel file

    Returns:
        Dict[str, Any]: Dictionary containing export parameters and table configurations
    """
    if not os.path.exists(export_profile_path):
        raise FileNotFoundError(f"Export profile not found: {export_profile_path}")

    print(f"Reading export profile from {export_profile_path}")

    # Read all sheets from the Excel file
    excel_file = pd.ExcelFile(export_profile_path)

    # Initialize the result dictionary
    export_config = {
        'params': {},
        'field_map': {},
        'queries': {},
        'tables': {}
    }

    # Read the params sheet
    if 'params' in excel_file.sheet_names:
        params_df = pd.read_excel(excel_file, sheet_name='params')
        # Convert params DataFrame to dictionary
        for _, row in params_df.iterrows():
            if len(row) >= 2:  # Ensure we have both parameter and value
                param_name = str(row['Parameter'])
                param_value = row['Value']
                export_config['params'][param_name] = param_value

    # Read the field_map sheet
    if 'field_map' in excel_file.sheet_names:
        field_map_df = pd.read_excel(excel_file, sheet_name='field_map')
        # Convert field_map DataFrame to dictionary
        for _, row in field_map_df.iterrows():
            if len(row) >= 2:  # Ensure we have both field and display name
                field_name = str(row['field'])
                display_name = str(row['display'])
                export_config['field_map'][field_name] = display_name


    export_config['queries'] = pd.read_excel(excel_file, sheet_name='queries')

    # Read table configuration sheets
    for sheet_name in excel_file.sheet_names:
        if sheet_name not in ['params', 'field_map', 'queries']:
            table_df = pd.read_excel(excel_file, sheet_name=sheet_name)
            export_config['tables'][sheet_name] = table_df

    print(f"Found {len(export_config['tables'])} table configurations")
    return export_config


def export_to_excel(dataframes: Dict[str, pd.DataFrame],
                    output_file: str,
                    params: Dict[str, Any] = None,
                    column_groups: Dict[str, str] = {}) -> str:
    """
    Export DataFrames to Excel with formatting.

    Args:
        dataframes: Dictionary mapping table names to DataFrames
        output_file: Path to save the Excel file
        params: Additional parameters for formatting

    Returns:
        Path to the saved Excel file
    """
    if not dataframes:
        print("No data to export")
        return None

    # Create directory if it doesn't exist
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    print(f"Exporting {len(dataframes)} tables to {output_file}")

    try:
        # Create a Pandas Excel writer using XlsxWriter as the engine
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            workbook = writer.book

            # Define formats if parameters are provided
            header_format = None
            if params and params.get('bold_header_title', 0) == 1:
                header_format = workbook.add_format({'bold': True})

            # Write each DataFrame to a separate sheet
            for table_name, df in dataframes.items():
                # if df.empty:
                #     print(f"Skipping empty table: {table_name}")
                #     continue

                print(f"Writing {len(df)} rows to sheet: {table_name}")

                # Write DataFrame to Excel
                df.to_excel(writer, sheet_name=table_name, index=False)

                # Get the worksheet
                worksheet = writer.sheets[table_name]

                # Apply header format if specified
                if header_format:
                    for col_num, _ in enumerate(df.columns):
                        worksheet.write(0, col_num, df.columns[col_num], header_format)

                # Create column formats based on the export profile
                column_formats = {}

                # Find the table configuration in the export profile
                if params and 'tables' in params:
                    table_config = None
                    for t_name, t_config in params['tables'].items():
                        if t_name == table_name:
                            table_config = t_config
                            break

                    # If we found the table config, check for column formatting
                    if table_config:
                        for col_idx, col_name in enumerate(df.columns):
                            # Find the column in the table config
                            col_config = None
                            if isinstance(table_config, pd.DataFrame):
                                # Try to find the column in the DataFrame
                                if 'field' in table_config.columns:
                                    matching_rows = table_config[table_config['field'] == col_name]
                                    if not matching_rows.empty:
                                        col_config = matching_rows.iloc[0].to_dict()
                            else:
                                for field_config in table_config:
                                    if isinstance(field_config, dict) and field_config.get('field') == col_name:
                                        col_config = field_config
                                        break

                            # If we found column config with color settings, create a format
                            if col_config:
                                bg_color = col_config.get('background_color')
                                fg_color = col_config.get('foreground_color')

                                if bg_color or fg_color:
                                    format_dict = {}

                                    # Handle background color if specified
                                    if bg_color and not pd.isna(bg_color):
                                        # bg_color can be a hex value (#FF0000), a named color (red),
                                        # or an Excel color index - XlsxWriter accepts all these formats
                                        format_dict['bg_color'] = bg_color

                                    # Handle foreground color if specified
                                    if fg_color and not pd.isna(fg_color):
                                        # fg_color can be a hex value (#000000), a named color (blue),
                                        # or an Excel color index - XlsxWriter accepts all these formats
                                        format_dict['font_color'] = fg_color

                                    if format_dict:
                                        column_formats[col_idx] = workbook.add_format(format_dict)

                # Auto-adjust column widths
                for idx, col in enumerate(df.columns):
                    # Approach 1: Sample data for large datasets
                    try:
                        if len(df) > 1000:
                            # Get the maximum length in the column using sampling
                            sample_size = min(1000, len(df))
                            sample_df = df.head(sample_size)
                            # Check if the column exists and is a Series, not a DataFrame
                            if isinstance(sample_df[col], pd.Series):
                                column_len = sample_df[col].astype(str).str.len().max()
                            else:
                                column_len = 10  # Default width if column doesn't exist or is not a Series
                        else:
                            # Approach 2: Use vectorized operations for smaller datasets
                            # Check if the column exists and is a Series, not a DataFrame
                            if isinstance(df[col], pd.Series):
                                column_len = df[col].astype(str).str.len().max()
                            else:
                                column_len = 10  # Default width if column doesn't exist or is not a Series
                    except Exception as e:
                        print(f"Error calculating column width for '{col}': {e}")
                        column_len = 10  # Default width on error

                    # Get the length of the header
                    header_len = len(str(col))

                    # Set width to the max plus some padding
                    width = max(column_len, header_len) + 3

                    # Apply min/max constraints if specified in params
                    if params:
                        min_width = params.get('min_column_width', 0)
                        max_width = params.get('max_column_width', 0)

                        if min_width > 0:
                            width = max(width, min_width)
                        if max_width > 0:
                            width = min(width, max_width)

                    # Ensure reasonable limits
                    width = max(10, min(width, 50))

                    # Apply column width setting
                    worksheet.set_column(idx, idx, width)

                    # If we have a format for this column, apply it to all data cells
                    format_obj = column_formats.get(idx)
                    if format_obj:
                        # Apply format to each cell in the column (skip header row)
                        for row_idx in range(1, len(df) + 1):  # +1 because Excel rows are 0-indexed
                            try:
                                cell_value = df.iloc[row_idx-1, idx] if row_idx <= len(df) else ""
                                worksheet.write(row_idx, idx, cell_value, format_obj)
                            except Exception as e:
                                print(f"Error applying format to cell ({row_idx}, {idx}): {e}")

                # Apply column grouping if specified in the export profile
                # Look for table-specific column groups
                column_groups_key = column_groups.get(table_name)
                #if column_groups_key:
                if column_groups_key and not pd.isna(column_groups_key):
                    try:
                        # Parse the column groups - format: "start1-end1:level1,start2-end2:level2,..."
                        # or "column_name1-column_name2:level1,column_name3-column_name4:level2,..."
                        groups = column_groups_key.split(',')
                        for group in groups:
                            if '-' in group:
                                # Split into range and level parts
                                range_part = group
                                level = 1  # Default level

                                if ':' in range_part:
                                    range_part, level_part = range_part.split(':')
                                    try:
                                        level = int(level_part.strip())
                                        # Excel supports levels 1-7
                                        level = max(1, min(level, 7))
                                    except ValueError:
                                        print(f"Invalid level in column group: {level_part}")
                                        level = 1

                                # Parse the range
                                try:
                                    # Check if the range is specified as column indices or column names
                                    start_str, end_str = range_part.split('-')
                                    start_str = start_str.strip()
                                    end_str = end_str.strip()

                                    # Try to convert to integers (column indices)
                                    try:
                                        start_col = int(start_str)
                                        end_col = int(end_str)
                                    except ValueError:
                                        # Not integers, assume they are column names
                                        if start_str in df.columns and end_str in df.columns:
                                            start_col = df.columns.get_loc(start_str)
                                            end_col = df.columns.get_loc(end_str)
                                            print(f"Mapped column names {start_str}-{end_str} to indices {start_col}-{end_col}")
                                        else:
                                            # Column names not found
                                            if start_str not in df.columns:
                                                print(f"Column name not found: {start_str}")
                                            if end_str not in df.columns:
                                                print(f"Column name not found: {end_str}")
                                            continue

                                    # Validate column indices
                                    if 0 <= start_col < len(df.columns) and 0 <= end_col < len(df.columns):
                                        # Apply the grouping
                                        worksheet.set_column(start_col, end_col, None, None, {'level': level, 'hidden': False})
                                        print(f"Applied column grouping from {start_col} to {end_col} at level {level}")
                                    else:
                                        print(f"Column indices out of range: {start_col}-{end_col}")
                                except ValueError:
                                    print(f"Invalid column range format: {range_part}")
                    except Exception as e:
                        print(f"Error applying column grouping: {e}")

                # Add filter
                #worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
                # Apply filter only if the DataFrame has columns
                if not df.empty and len(df.columns) > 0:
                    worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

        print(f"Successfully exported data to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error exporting to Excel: {e}")
        import traceback
        traceback.print_exc()
        return None


def plugin_export_postgres(project_id: int = 0,
                           export_profile: str = "debug/template_export_profile.xlsx",
                           use_save_file: bool = False,
                           save_file: str = "debug/exported_data.xlsx",
                           max_retries: int = 3,
                           ignore_do_not_export: bool = False,
                           ignore_display_names: bool = False,
                           ignore_sorting: bool = False,
                           general_data_workbook: str = "",
                           signals: str = ""):
    """
    Export data from PostgreSQL database using an export profile.

    Args:
        project_id (int): Project ID to export data for. Must be greater than 0 when actually exporting.
        export_profile (str): Path to the export profile Excel file
        use_save_file (bool): Whether to use the provided save_file or use parameters from export profile
        save_file (str): Path to save the exported data. Used only if use_save_file is True.
        max_retries (int): Maximum number of retries for database connection attempts
        ignore_do_not_export (bool): If True, export all fields even if do_not_export is set to 1
        ignore_display_names (bool): If True, use field names directly instead of display names
        ignore_sorting (bool): If True, skip sorting of data even if sort parameters are specified
        general_data_workbook (str): Optional path to general data workbook. If provided, will filter
                                   columns for public.general and general_aggregate queries to only include
                                   columns that exist in the workbook's first row, plus core required columns.
        signals (str): Optional signals for the export process

    Returns:
        str: Message indicating success or failure
    """
    # Validate project_id
    if project_id <= 0:
        return "Error: project_id must be greater than 0"

    print(f"Exporting data for project_id: {project_id} using profile: {export_profile}")

    try:
        export_config = read_export_profile(export_profile)
    except Exception as e:
        print(f"Error reading export profile: {e}")
        import traceback
        traceback.print_exc()
        return f"Error reading export profile: {str(e)}"

    # Get export parameters
    params = export_config.get('params', {})
    tables_config = export_config.get('tables', {})
    field_map = export_config.get('field_map', {})
    queries = export_config.get('queries', {})
    print(field_map)
    print(queries)

    # Read available columns from general data workbook if provided
    workbook_columns = None
    if general_data_workbook and os.path.exists(general_data_workbook):
        try:
            print(f"Reading column headers from general data workbook: {general_data_workbook}")
            # Read just the first row to get column names
            workbook_df = pd.read_excel(general_data_workbook, nrows=0)
            workbook_columns = set(workbook_df.columns.tolist())
            print(f"Found {len(workbook_columns)} columns in workbook")
        except Exception as e:
            print(f"Warning: Could not read general data workbook: {e}")
            workbook_columns = None
    elif general_data_workbook:
        print(f"Warning: General data workbook not found: {general_data_workbook}")

    # Determine the save location
    output_file = None

    if use_save_file:
        # Use the provided save_file
        output_file = save_file
        if not output_file.lower().endswith('.xlsx'):
            error_msg = "Invalid save file: must end with .xlsx extension"
            print(error_msg)
            return error_msg
    else:
        # Use parameters from the export profile
        save_dir = params.get('save_dir')
        filename = params.get('filename')
        include_timestamp = params.get('include_time_stamp', 1)
        timestamp_format = params.get('timestamp_format', 'YYYY-MM-DD')

        # Return early if filename is not provided in params
        if not filename:
            error_msg = "Export profile must specify a filename parameter if `use_save_file` is not Checked"
            print(error_msg)
            return error_msg

        # Ensure filename has .xlsx extension
        if not filename.lower().endswith('.xlsx'):
            filename = f"{filename}.xlsx"
            error_msg = "Invalid filename: must end with .xlsx extension"
            print(error_msg)
            return error_msg

        # Add timestamp if requested
        if include_timestamp:
            # Remove .xlsx extension temporarily
            base_filename = filename[:-5] if filename.lower().endswith('.xlsx') else filename

            # Format timestamp based on the specified format
            timestamp = ""
            if timestamp_format == 'YYYY-MM-DD':
                timestamp = datetime.now().strftime("%Y-%m-%d")
            elif timestamp_format == 'YYYYMMDD':
                timestamp = datetime.now().strftime("%Y%m%d")
            elif timestamp_format == 'YYYY-MM-DD-HHMM':
                timestamp = datetime.now().strftime("%Y-%m-%d-%H%M")
            else:
                # Default format
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Add timestamp and extension
            filename = f"{base_filename}_{timestamp}.xlsx"

        # Set the full output path
        if save_dir:
            # Create the directory if it doesn't exist
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                print(f"Created directory: {save_dir}")
            output_file = os.path.join(save_dir, filename)
        else:
            # Use debug directory as default
            debug_dir = "debug"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)
                print(f"Created directory: {debug_dir}")
            output_file = os.path.join(debug_dir, filename)

    # Validate the final output file
    if not output_file.lower().endswith('.xlsx'):
        error_msg = "Invalid output file: must end with .xlsx extension"
        print(error_msg)
        return error_msg

    print(f"Will save export to: {output_file}")

    # Initialize result dictionary to store DataFrames
    result_dataframes = {}
    all_column_groups = {}

    for _, row in queries.iterrows():
        table_id = row.get('id')
        sheet_name = row.get('sheet_name')

        if table_id in ['queries', 'params', 'field_map']:
            print(f"Skipping export for {table_id} - {sheet_name}. Invalid table_id name")
            continue

        if row.get('do_not_export') == 1:
            print(f"Skipping export for {table_id} - {sheet_name}. Do not export is set")
            continue

        query = row.get('query', '')
        query_params = row.get('params', '')
        order_by = row.get('order_by', '')
        column_groups = row.get('column_groups', '')

        print(f"Processing table: {sheet_name}")

        # Define core columns that should never be dropped for general tables
        core_general_columns = {
            'avg_elevation', 'min_elevation', 'max_elevation', 'size', 'length',
            'calculated_eq_length', 'calculated_area', 'elbows_90', 'elbows_45',
            'bevels', 'tees', 'reducers', 'caps', 'flanges', 'valves_flanged',
            'valves_welded', 'cut_outs', 'supports', 'bends', 'union_couplings',
            'expansion_joints', 'field_welds', 'created_at', 'updated_at',
            'sys_path', 'pdf_id', 'pdf_page', 'sys_filename'
        }

        # Build SQL query based on table configuration
        fields_to_export = []

        table_config = tables_config.get(table_id, pd.DataFrame())
        if table_config.empty:
            print(f"Warning - No configuration found for table. Defaulting to all fields: Expected sheet_name = {table_id} in export_profile")

        # CRITICAL DEBUG: Show what's in the table config
        if table_id == "general_aggregate":
            print(f"🔥🔥🔥 DEBUGGING general_aggregate TABLE CONFIG:")
            print(f"🔥 Table config shape: {table_config.shape}")
            if not table_config.empty:
                print(f"🔥 Table config columns: {list(table_config.columns)}")
                print(f"🔥 First 10 fields in config:")
                for i, (_, row) in enumerate(table_config.iterrows()):
                    if i >= 10:
                        break
                    field = row.get('field')
                    do_not_export = row.get('do_not_export')
                    print(f"🔥   {i+1}. field='{field}', do_not_export={do_not_export}")

        for _, row in table_config.iterrows():
            field = row.get('field')
            do_not_export = row.get('do_not_export')

            # Include field if it's not marked as do_not_export
            if field and (ignore_do_not_export or do_not_export is None or do_not_export != 1):
                fields_to_export.append(field)
                # CRITICAL DEBUG: Show which fields are being added
                if table_id == "general_aggregate":
                    print(f"🔥 ADDING FIELD: '{field}'")

        if not fields_to_export:
            # No detected fields to export, default to all
            fields_to_export = ["*"]

        # Filter fields based on workbook columns for general tables
        if (workbook_columns is not None and
            table_id in ['public.general', 'general_aggregate'] and
            fields_to_export != ["*"]):

            original_count = len(fields_to_export)
            filtered_fields = []

            for field in fields_to_export:
                # Always include core columns or columns that exist in workbook
                if field in core_general_columns or field in workbook_columns:
                    filtered_fields.append(field)
                else:
                    print(f"Excluding field '{field}' - not found in workbook and not a core column")

            fields_to_export = filtered_fields
            print(f"Filtered {table_id} fields from {original_count} to {len(fields_to_export)} based on workbook columns")


        # CRITICAL DEBUG: Print before formatting
        if "manage_bom_to_general_aggregation_full" in query:
            print(f"🔥🔥🔥 FOUND manage_bom_to_general_aggregation_full QUERY!")
            print(f"🔥 Original query: {query}")
            print(f"🔥 Fields to export: {fields_to_export}")
            print(f"🔥 About to format query...")

        query = query.format(fields_to_export=", ".join(fields_to_export))

        # CRITICAL DEBUG: Print after formatting
        if "manage_bom_to_general_aggregation_full" in query:
            print(f"🔥🔥🔥 FORMATTED manage_bom_to_general_aggregation_full QUERY!")
            print(f"🔥 Final query: {query}")
            print(f"🔥" + "=" * 80)

        # DEBUG: Print the actual SQL being executed
        print(f"🔍 DEBUG SQL for {table_id}:")
        print(f"🔍 Query: {query}")
        print(f"🔍 Fields: {fields_to_export}")
        print(f"🔍 Params: {query_params}")
        print("=" * 80)

        query_params = [s.strip() for s in str(query_params).split(",")]
        cleaned_params = []
        valid_params = {"project_id": project_id}
        for param in query_params:
            if param in valid_params:
                cleaned_params.append(valid_params[param])
            else:
                print(f"Invalid query parameter: {param}")

        if cleaned_params:
            cleaned_params = tuple(cleaned_params)

        # Execute the query
        retry_count = 0
        retry_delay = 2  # seconds
        last_error = None

        while retry_count <= max_retries:
            try:
                print(f"Executing query for table: {sheet_name} (Attempt {retry_count + 1}/{max_retries + 1})")

                # PRINT THE EXACT SQL QUERY TO CONSOLE
                print("=" * 100)
                print("EXACT SQL QUERY BEING EXECUTED:")
                print(query)
                print("PARAMETERS:")
                print(cleaned_params)
                print("=" * 100)

                with get_db_connection() as conn:
                    with conn.cursor() as cursor:
                        if cleaned_params:
                            cursor.execute(query, cleaned_params)
                        else:
                            cursor.execute(query)
                        columns = [desc[0] for desc in cursor.description]
                        results = cursor.fetchall()

                        if not results:
                            print(f"No data found for table: {sheet_name}")
                            # Create empty DataFrame with correct columns
                            result_dataframes[sheet_name] = df = pd.DataFrame(columns=columns)
                        else:
                            # Create DataFrame from results
                            df = pd.DataFrame(results, columns=columns)

                        # Convert specific columns to numeric for certain tables
                        if not df.empty:
                            numeric_columns = []

                            # Define columns that should be numeric for specific tables
                            if table_id in ['public.bom', 'public.atem_rfq']:
                                numeric_columns = ['quantity', 'calculated_eq_length', 'calculated_area', 'size1', 'size2']
                            elif table_id == 'public.general':
                                numeric_columns = ['quantity', 'calculated_eq_length', 'calculated_area', 'size1', 'size2']
                            elif table_id == 'general_aggregate':
                                numeric_columns = ['avg_elevation', 'min_elevation', 'max_elevation', 'length', 'calculated_eq_length', 'calculated_area',
                                                 'elbows_90', 'elbows_45', 'bevels', 'tees', 'reducers', 'caps', 'flanges', 'valves_flanged',
                                                 'valves_welded', 'cut_outs', 'supports', 'bends', 'union_couplings', 'expansion_joints',
                                                 'field_welds', 'test_pressure', 'dt1', 'dt2', 'dp1', 'dp2', 'op1', 'op2', 'opt1', 'opt2']

                            # Convert columns to numeric if they exist
                            for col in numeric_columns:
                                if col in df.columns:
                                    try:
                                        df[col] = pd.to_numeric(df[col], errors='coerce')
                                        print(f"Converted column '{col}' to numeric for table {table_id}")
                                    except Exception as e:
                                        print(f"Warning: Could not convert column '{col}' to numeric for table {table_id}: {e}")

                            # Replace NaN values with blanks for clean export
                            df = df.fillna('')

                        # Rename columns in DataFrame to match display names
                        field_display_mapping = {}
                        if not ignore_display_names:
                            for field in df.columns:
                                display = field_map.get(field)
                                if display and display != field:
                                    field_display_mapping[field] = display

                        if field_display_mapping:
                            # Only rename columns that exist in the DataFrame
                            rename_dict = {col: field_display_mapping[col]
                                            for col in df.columns
                                            if col in field_display_mapping}

                            if rename_dict:
                                print(f"Renaming {len(rename_dict)} columns with display names")
                                df.rename(columns=rename_dict, inplace=True)

                                cleaned_column_groups = []
                                if not pd.isna(column_groups):
                                    for group in column_groups.split(","):
                                        if '-' in group:
                                            # Split into range and level parts
                                            range_part = group
                                            level = 1  # Default level

                                            if ':' in range_part:
                                                range_part, level_part = range_part.split(':')
                                                try:
                                                    level = int(level_part.strip())
                                                except ValueError:
                                                    level = 1

                                            start, end = range_part.split('-')
                                            start = start.strip()
                                            end = end.strip()
                                            start = field_map.get(start, start)
                                            end = field_map.get(end, end)

                                            cleaned_column_groups.append(f"{start}-{end}:{level}")
                                    all_column_groups[sheet_name] = ",".join(cleaned_column_groups)

                        # Apply sorting if specified in the export profile (before renaming columns)
                        if not df.empty and not ignore_sorting and order_by:
                            sort_fields = [field.strip() for field in order_by.split(',')]
                            sort_fields = [field for field in sort_fields if field in df.columns]
                            ascending_values = [True] * len(sort_fields)

                            for n, s in enumerate(sort_fields):
                                field, asc = s.split(' ')
                                if asc.lower() == "asc":
                                    pass
                                elif asc.lower() == "desc":
                                    ascending_values[n] = False
                                else:
                                    print("Warning. Invalid sort!", s)

                                if sort_fields:
                                    # print(f"Sorting {table_name} by {', '.join(sort_fields)}")
                                    # Sort the DataFrame by the specified fields (ascending for all)
                                    df = df.sort_values(by=sort_fields, ascending=ascending_values, key=natsort_keygen())

                            result_dataframes[sheet_name] = df
                            print(f"Retrieved {len(df)} rows from {sheet_name}")

                # If we get here, the query was successful
                break

            except Exception as e:
                last_error = e
                retry_count += 1
                if retry_count <= max_retries:
                    print(f"Database connection error: {str(e)}. Retrying in {retry_delay} seconds... (Attempt {retry_count + 1}/{max_retries + 1})")
                    time.sleep(retry_delay)
                    # Increase delay for next retry (exponential backoff)
                    retry_delay = min(retry_delay * 2, 30)  # Cap at 30 seconds
                else:
                    print(f"Error querying table {sheet_name} after {max_retries + 1} attempts: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    # Add empty DataFrame for this table
                    result_dataframes[sheet_name] = pd.DataFrame()

    # Export the data to Excel if we have any results
    if result_dataframes:
        output_file = export_to_excel(result_dataframes, output_file, params, all_column_groups)
        if output_file:
            print(f"Data exported successfully to {output_file}")
            return f"Data exported successfully to {output_file}"
        else:
            error_msg = "Failed to export data to Excel"
            print(error_msg)
            return error_msg
    else:
        error_msg = "No data to export"
        print(error_msg)
        return error_msg


if __name__ == "__main__":
    # Example usage
    result = plugin_export_postgres(
        project_id=1,
        export_profile="debug/template_export_profile.xlsx",
        general_data_workbook=""  # Optional: path to general data workbook for column filtering
    )
    print(f"Export result: {result}")
