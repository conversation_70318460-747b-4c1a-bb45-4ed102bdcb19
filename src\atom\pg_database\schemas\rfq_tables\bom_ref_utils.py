"""
BOM Reference ID Utilities

This module contains functions to handle the linking of BOM records to reference tables
using rfq_ref_id and gen_ref_id fields. These functions can be used independently of
the automatic trigger system, allowing for manual synchronization when needed.
"""

import logging
from typing import Dict, List, Optional, Tuple

import psycopg2
from psycopg2.extras import execute_values

from src.atom.pg_database.pg_connection import get_db_connection, get_db_cursor
from src.atom.pg_database.schemas.base.db_config import DatabaseConfig

# Set up logger
logger = logging.getLogger(__name__)


def link_bom_to_rfq(project_id: Optional[int] = None, 
                   bom_ids: Optional[List[int]] = None,
                   db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Link BOM records to RFQ records by setting the rfq_ref_id field.
    
    The function will:
    1. For each BOM record, find a matching RFQ record based on project_id, 
       material_description, and size
    2. If a match is found, update the BOM record with the rfq_ref_id
    3. If no match is found, create a new RFQ record and set the rfq_ref_id
    
    Args:
        project_id: Optional project ID to filter BOM records
        bom_ids: Optional list of specific BOM record IDs to process
        db_config: Optional database configuration
        
    Returns:
        A dictionary with the results of the operation
    """
    try:
        # Initialize counter variables
        linked = 0
        created = 0
        unchanged = 0
        errors = []
        
        # Get a database connection
        with get_db_connection(db_config) as conn:
            with conn.cursor() as cursor:
                # Build the query to get BOM records without rfq_ref_id
                query = """
                SELECT 
                    id, project_id, profile_id, material_description, size, size1, size2,
                    rfq_scope, general_category, unit_of_measure,
                    material, rating, ends, fitting_category, valve_type,
                    calculated_eq_length, calculated_area, quantity
                FROM public.bom
                WHERE 1=1
                """
                
                params = []
                
                # Apply filters if provided
                if project_id is not None:
                    query += " AND project_id = %s"
                    params.append(project_id)
                
                if bom_ids is not None and len(bom_ids) > 0:
                    query += f" AND id IN %s"
                    params.append(tuple(bom_ids))
                else:
                    # Only process records without rfq_ref_id if not explicitly specified
                    query += " AND rfq_ref_id IS NULL"
                
                # Execute the query
                cursor.execute(query, params)
                bom_records = cursor.fetchall()
                
                logger.info(f"Found {len(bom_records)} BOM records to process")
                
                # Process each BOM record
                for bom_rec in bom_records:
                    try:
                        bom_id = bom_rec[0]
                        bom_project_id = bom_rec[1]
                        bom_material_desc = bom_rec[3]
                        bom_size = bom_rec[4]
                        
                        # Check if a matching RFQ record already exists
                        cursor.execute("""
                            SELECT id FROM public.atem_rfq
                            WHERE project_id = %s
                            AND UPPER(material_description) = UPPER(%s)
                            AND size = %s
                            LIMIT 1
                        """, (bom_project_id, bom_material_desc, bom_size))
                        
                        existing_rfq = cursor.fetchone()
                        
                        if existing_rfq:
                            # Use the existing RFQ record
                            rfq_id = existing_rfq[0]
                            
                            # Update the BOM record with the rfq_ref_id
                            cursor.execute("""
                                UPDATE public.bom
                                SET rfq_ref_id = %s, updated_at = CURRENT_TIMESTAMP
                                WHERE id = %s
                            """, (rfq_id, bom_id))
                            
                            linked += 1
                            logger.debug(f"Linked BOM ID {bom_id} to RFQ ID {rfq_id}")
                        else:
                            # Create a new RFQ record
                            cursor.execute("""
                                INSERT INTO public.atem_rfq (
                                    project_id, material_description, size, size1, size2,
                                    quantity, rfq_scope, general_category, unit_of_measure,
                                    material, rating, ends, fitting_category, valve_type,
                                    calculated_eq_length, calculated_area
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                                    %s, %s, %s, %s, %s, %s, %s
                                ) RETURNING id
                            """, (
                                bom_project_id, bom_material_desc, bom_size, 
                                bom_rec[5], bom_rec[6], # size1, size2
                                bom_rec[17] or 1, # quantity, default to 1
                                bom_rec[7], bom_rec[8], bom_rec[9], # rfq_scope, general_category, unit_of_measure
                                bom_rec[10], bom_rec[11], bom_rec[12], # material, rating, ends
                                bom_rec[13], bom_rec[14], # fitting_category, valve_type
                                bom_rec[15], bom_rec[16] # calculated_eq_length, calculated_area
                            ))
                            
                            new_rfq_id = cursor.fetchone()[0]
                            
                            # Update the BOM record with the new rfq_ref_id
                            cursor.execute("""
                                UPDATE public.bom
                                SET rfq_ref_id = %s, updated_at = CURRENT_TIMESTAMP
                                WHERE id = %s
                            """, (new_rfq_id, bom_id))
                            
                            created += 1
                            logger.debug(f"Created new RFQ ID {new_rfq_id} for BOM ID {bom_id}")
                    
                    except Exception as e:
                        # Log the error and continue with the next record
                        error_msg = f"Error processing BOM ID {bom_id}: {str(e)}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                
                # Commit the transaction
                conn.commit()
        
        # Return the results
        return {
            "status": "success",
            "bom_processed": len(bom_records),
            "linked_to_existing": linked,
            "new_rfq_created": created,
            "unchanged": unchanged,
            "errors": len(errors),
            "error_details": errors[:10] if errors else []  # Limit error details to the first 10
        }
    
    except Exception as e:
        logger.error(f"Error in link_bom_to_rfq: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "bom_processed": 0,
            "linked_to_existing": 0,
            "new_rfq_created": 0,
            "unchanged": 0,
            "errors": 1,
            "error_details": [str(e)]
        }


def link_bom_to_general(project_id: Optional[int] = None, 
                       bom_ids: Optional[List[int]] = None,
                       db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Link BOM records to General records by setting the gen_ref_id field.
    
    The function will:
    1. For each BOM record, find a matching General record based on project_id, 
       pdf_id, pdf_page, and size
    2. If a match is found, update the BOM record with the gen_ref_id
    
    Args:
        project_id: Optional project ID to filter BOM records
        bom_ids: Optional list of specific BOM record IDs to process
        db_config: Optional database configuration
        
    Returns:
        A dictionary with the results of the operation
    """
    try:
        # Initialize counter variables
        linked = 0
        unchanged = 0
        errors = []
        
        # Get a database connection
        with get_db_connection(db_config) as conn:
            with conn.cursor() as cursor:
                # Build the query to get BOM records without gen_ref_id
                query = """
                SELECT 
                    id, project_id, pdf_id, pdf_page, size
                FROM public.bom
                WHERE 1=1
                """
                
                params = []
                
                # Apply filters if provided
                if project_id is not None:
                    query += " AND project_id = %s"
                    params.append(project_id)
                
                if bom_ids is not None and len(bom_ids) > 0:
                    query += f" AND id IN %s"
                    params.append(tuple(bom_ids))
                else:
                    # Only process records without gen_ref_id if not explicitly specified
                    query += " AND gen_ref_id IS NULL"
                    
                    # Don't process records with missing pdf information
                    query += " AND pdf_id IS NOT NULL AND pdf_page IS NOT NULL"
                
                # Execute the query
                cursor.execute(query, params)
                bom_records = cursor.fetchall()
                
                logger.info(f"Found {len(bom_records)} BOM records to process for general linking")
                
                # Process each BOM record
                for bom_rec in bom_records:
                    try:
                        bom_id = bom_rec[0]
                        bom_project_id = bom_rec[1]
                        bom_pdf_id = bom_rec[2]
                        bom_pdf_page = bom_rec[3]
                        bom_size = bom_rec[4]
                        
                        # Skip if we're missing required information
                        if None in (bom_project_id, bom_pdf_id, bom_pdf_page):
                            logger.warning(f"Skipping BOM ID {bom_id} due to missing PDF information")
                            unchanged += 1
                            continue
                        
                        # Check if a matching General record exists
                        cursor.execute("""
                            SELECT id FROM public.general
                            WHERE project_id = %s
                            AND pdf_id = %s
                            AND pdf_page = %s
                            AND size = %s
                            LIMIT 1
                        """, (bom_project_id, bom_pdf_id, bom_pdf_page, bom_size))
                        
                        existing_general = cursor.fetchone()
                        
                        if existing_general:
                            # Use the existing General record
                            gen_id = existing_general[0]
                            
                            # Update the BOM record with the gen_ref_id
                            cursor.execute("""
                                UPDATE public.bom
                                SET gen_ref_id = %s, updated_at = CURRENT_TIMESTAMP
                                WHERE id = %s
                            """, (gen_id, bom_id))
                            
                            linked += 1
                            logger.debug(f"Linked BOM ID {bom_id} to General ID {gen_id}")
                        else:
                            # No matching General record found
                            logger.debug(f"No matching General record found for BOM ID {bom_id}")
                            unchanged += 1
                    
                    except Exception as e:
                        # Log the error and continue with the next record
                        error_msg = f"Error processing BOM ID {bom_id}: {str(e)}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                
                # Commit the transaction
                conn.commit()
        
        # Return the results
        return {
            "status": "success",
            "bom_processed": len(bom_records),
            "linked_to_general": linked,
            "unchanged": unchanged,
            "errors": len(errors),
            "error_details": errors[:10] if errors else []  # Limit error details to the first 10
        }
    
    except Exception as e:
        logger.error(f"Error in link_bom_to_general: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "bom_processed": 0,
            "linked_to_general": 0,
            "unchanged": 0,
            "errors": 1,
            "error_details": [str(e)]
        }


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Example usage
    result = link_bom_to_rfq()
    logger.info(f"RFQ Link Result: {result}")
    
    result = link_bom_to_general()
    logger.info(f"General Link Result: {result}")
