"""
Miscellaneous PostgreSQL Database Utilities

This file contains standalone utility functions for PostgreSQL database operations
that can be run independently from the IDE using the if __name__ == '__main__' block.
"""

import pandas as pd
import os
from typing import Optional, List, Dict, Any
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor
from psycopg2.extras import RealDictCursor


def check_material_descriptions_in_db(
    input_df: pd.DataFrame, 
    material_description_column: str = 'material_description',
    db_config: Optional[DatabaseConfig] = None,
    output_column: str = 'exists_in_verified_materials'
) -> pd.DataFrame:
    """
    Check if material descriptions from a DataFrame exist in the PostgreSQL 
    'public.verified_material_classifications' table.
    
    Args:
        input_df (pd.DataFrame): Input DataFrame containing material descriptions
        material_description_column (str): Name of the column containing material descriptions
        db_config (DatabaseConfig, optional): Database configuration. If None, uses default.
        output_column (str): Name of the new column to add indicating existence in DB
        
    Returns:
        pd.DataFrame: Original DataFrame with new column indicating if material exists in DB
        
    Raises:
        ValueError: If the specified material description column doesn't exist
        Exception: If database connection or query fails
    """
    # Validate input
    if material_description_column not in input_df.columns:
        raise ValueError(f"Column '{material_description_column}' not found in DataFrame. "
                        f"Available columns: {list(input_df.columns)}")
    
    # Create a copy of the input DataFrame to avoid modifying the original
    result_df = input_df.copy()
    
    # Get unique material descriptions to minimize database queries
    unique_materials = input_df[material_description_column].dropna().unique()
    
    if len(unique_materials) == 0:
        print("Warning: No material descriptions found in the specified column.")
        result_df[output_column] = False
        return result_df
    
    print(f"Checking {len(unique_materials)} unique material descriptions against database...")
    
    # Dictionary to store results for each material description
    material_exists_dict = {}
    
    try:
        # Use the database connection context manager
        with get_db_cursor(db_config, cursor_factory=RealDictCursor) as cursor:
            # Query to check if material descriptions exist
            # Using parameterized query for safety
            query = """
                SELECT DISTINCT material_description 
                FROM public.verified_material_classifications 
                WHERE material_description = ANY(%s)
            """
            
            # Execute query with list of unique materials
            cursor.execute(query, (list(unique_materials),))
            existing_materials = cursor.fetchall()
            
            # Convert results to a set for fast lookup
            existing_materials_set = {row['material_description'] for row in existing_materials}
            
            print(f"Found {len(existing_materials_set)} materials that exist in the database.")
            
            # Create lookup dictionary
            for material in unique_materials:
                material_exists_dict[material] = material in existing_materials_set
                
    except Exception as e:
        print(f"Error querying database: {e}")
        raise
    
    # Map the results back to the original DataFrame
    result_df[output_column] = result_df[material_description_column].map(
        lambda x: material_exists_dict.get(x, False) if pd.notna(x) else False
    )
    
    # Print summary statistics
    total_rows = len(result_df)
    exists_count = result_df[output_column].sum()
    not_exists_count = total_rows - exists_count
    
    print(f"\nSummary:")
    print(f"Total rows: {total_rows}")
    print(f"Materials found in database: {exists_count}")
    print(f"Materials NOT found in database: {not_exists_count}")
    print(f"Percentage found: {(exists_count/total_rows)*100:.1f}%")
    
    return result_df


def export_dataframe_to_excel(
    df: pd.DataFrame, 
    output_path: str, 
    sheet_name: str = 'Sheet1'
) -> None:
    """
    Export a DataFrame to Excel file.
    
    Args:
        df (pd.DataFrame): DataFrame to export
        output_path (str): Full path where to save the Excel file
        sheet_name (str): Name of the Excel sheet
    """
    try:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Export to Excel
        df.to_excel(output_path, sheet_name=sheet_name, index=False)
        print(f"DataFrame exported successfully to: {output_path}")
        
    except Exception as e:
        print(f"Error exporting DataFrame to Excel: {e}")
        raise


def load_dataframe_from_excel(
    excel_path: str, 
    sheet_name: Optional[str] = None
) -> pd.DataFrame:
    """
    Load a DataFrame from an Excel file.
    
    Args:
        excel_path (str): Path to the Excel file
        sheet_name (str, optional): Name of the sheet to read. If None, uses first sheet.
        
    Returns:
        pd.DataFrame: Loaded DataFrame
    """
    try:
        if sheet_name is None:
            # Use the first sheet by default
            df = pd.read_excel(excel_path, sheet_name=0)
        else:
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
        
        # Drop any completely empty rows
        df = df.dropna(how='all')
        
        print(f"Loaded DataFrame with {len(df)} rows and {len(df.columns)} columns from: {excel_path}")
        return df
        
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        raise


def test_database_connection(db_config: Optional[DatabaseConfig] = None) -> bool:
    """
    Test the PostgreSQL database connection.
    
    Args:
        db_config (DatabaseConfig, optional): Database configuration. If None, uses default.
        
    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        with get_db_connection(db_config) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result[0] == 1:
                    print("Database connection test successful")
                    print(f"PostgreSQL server version: {conn.server_version}")
                    return True
    except Exception as e:
        print(f"Database connection test failed: {e}")
        return False


if __name__ == "__main__":
    # Configuration flags - set these to True to run specific functions
    test_connection = True
    check_materials = True
    
    # Database configuration
    db_config = DatabaseConfig()
    
    # Test 1: Database connection test
    if test_connection:
        print("=== Testing Database Connection ===")
        success = test_database_connection(db_config)
        if not success:
            print("Database connection failed. Please check your configuration.")
            print("Skipping material checking due to connection failure.")
            check_materials = False
        print()
    
    # Test 2: Check material descriptions against database
    if check_materials:
        print("=== Checking Material Descriptions ===")
        
        # Configuration for material checking
        input_excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 054\data\4 - classified\exported_rfq_data_nofieldmap - classified.xlsx"
        output_excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 054\Workspace\RFQ - Stage2 - Material Check.xlsx"
        sheet_name = None  # Use None for first sheet, or specify sheet name
        material_column = 'material_description'  # Column name containing material descriptions
        
        try:
            # Check if input file exists
            if not os.path.exists(input_excel_path):
                print(f"ERROR: Input file does not exist: {input_excel_path}")
                print("Please update the 'input_excel_path' variable to point to your actual Excel file.")
                print("Skipping material checking.")
            else:
                # Load the input DataFrame
                print(f"Loading data from: {input_excel_path}")
                input_df = load_dataframe_from_excel(input_excel_path, sheet_name)

                # Check if the material description column exists
                if material_column not in input_df.columns:
                    print(f"Available columns: {list(input_df.columns)}")
                    print(f"Please update the 'material_column' variable to match your data.")
                else:
                    # Check material descriptions against database
                    result_df = check_material_descriptions_in_db(
                        input_df=input_df,
                        material_description_column=material_column,
                        db_config=db_config,
                        output_column='exists_in_verified_materials'
                    )

                    # Export results
                    print(f"\nExporting results to: {output_excel_path}")
                    export_dataframe_to_excel(result_df, output_excel_path, 'Results')

                    print("\nOperation completed successfully!")
            
        except Exception as e:
            print(f"Error during material checking operation: {e}")
            print("Material checking failed.")
    
    print("All selected operations completed.")
