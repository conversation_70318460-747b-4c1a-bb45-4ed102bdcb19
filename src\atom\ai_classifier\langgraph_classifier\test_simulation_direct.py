"""
Direct test of the enhanced simulation function to verify it's working correctly
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from classification_nodes import simulate_material_analysis

def test_enhanced_simulation():
    """Test the enhanced simulation function directly"""
    
    print("🧪 Testing Enhanced Simulation Function Directly")
    print("=" * 60)
    
    # Test cases from the real BOM data
    test_cases = [
        "45 ELBOW, 304/304L SS A182-F304/304L, SW, CL3000, B16.11",
        "45 LR ELBOW, SUPER DUPLEX SS A815-WPS32750-S, SMLS, BE, SCH 10S, B16.9",
        "EXPANSION JOINT",
        "BLIND FLG, 2507 SUPER DUPLEX SS A182-F53, CL150, RF, B16.5",
        "BLIND FLG, 304/304L SS A182-F304/304L, CL150, RF, B16.5",
        "Pipe <PERSON>pple 2\" SCH 40 ASTM A106 BE",
        "Ball Valve 3\" 600# CS",
        "WN Flange 6\" 300# RF ASTM A105",
        "Pipe SMLS 8\" SCH 40 ASTM A106",
        "VENDOR ASYM18456"
    ]
    
    for i, description in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}:")
        print(f"   Description: {description}")
        
        result = simulate_material_analysis(description)
        
        print(f"   ✅ Category: {result['primary_category']}")
        print(f"   ✅ Confidence: {result['confidence']}")
        print(f"   ✅ Reasoning: {result['reasoning']}")
        print(f"   ✅ Properties: {result['extracted_properties']}")
    
    print(f"\n🎉 Enhanced simulation test completed!")

if __name__ == "__main__":
    test_enhanced_simulation()
