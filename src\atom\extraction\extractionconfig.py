"""
A configuration class for the RoiExtraction class.
"""
import os
from dataclasses import dataclass, field, asdict
from typing import List, Optional, Set, Union


def parse_page_range(page_range_str):
    """Parse page range string into list of page numbers (1-based)"""
    print("page_range_str", page_range_str, type(page_range_str))
    if type(page_range_str) == int:
        if page_range_str < 1:
            raise ValueError("Invalid page range. Page numbers must be greater than 0")
        return [page_range_str]
    elif type(page_range_str) == list:
        page_range = sorted([int(p) for p in page_range_str])
        return page_range
    elif page_range_str is None:
        return None
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return None

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            pages.append(page)

    return sorted(set(pages))


@dataclass
class RoiExtractionConfig:
    """
    A configuration class for the RoiExtraction class.
    """

    project_id: int
    filename: str
    roi_layout: str = None

    # General extraction
    extract_general: bool = True
    # Extract tables
    extract_tables: bool = True

    # List of pages to extract
    pages_to_extract: Optional[List[int]] = None
    # List of pages to skip
    pages_to_skip: Optional[List[int]] = None

    test_page_limit: Optional[int] = None

    # List of ROIs to extract
    rois_to_extract: Optional[List[str]] = None

    # List of ROIs to skip
    rois_to_skip: Optional[List[str]] = None

    # Extract elevations
    extract_elevations: bool = True

    # Replace non-Standard characters with ASCII characters
    replace_non_standard_characters: bool = True

    # Combine table text for identifying terms. If disabled, will
    # use the value column as combined
    combine_nearby_text: bool = True

    # Pass filter through tables. Can detect preprocessing information e.g. component_keyword
    filter_table_terms: bool = True

    # Outlier handling
    remove_outliers: bool = True

    # Deduplicate
    deduplicate_general_bboxes: bool = True
    deduplicate_table_bboxes: bool = False

    # Debugging
    logging_level: int = 1
    benchmark_functions: bool = False

    # Enable saving of debug files to `save_debug_dir`
    save_debug_files: bool = False
    save_debug_dir: str = "debug"

    # Table specific
    tables_to_extract: Set[str] = field(default_factory=set)
    tables_to_skip: Set[str] = field(default_factory=set)

    # BOM specific
    bom_require_numbered_rows: bool = False
    bom_include_annot_rows: bool = False
    bom_merge_wrapped_rows: bool = True
    bom_split_compound_entries: bool = True  # Splits first column into separate rows e.g. "1 Stud"

    # Concurrency
    enable_concurrency: bool = True # depends on task_count_concurrency_threshold
    # Triggers concurrency if the number of tasks is greater than this value
    task_count_concurrency_threshold: int = 200
    max_workers = 4

    _extraction_log_file: str = None

    # Safe json decode - slower but more stable
    safe_json_decode: bool = True

    # Convert ROI payload
    roi_payload: dict = None

    # Minimum area threshold to be considered within ROI
    general_min_area_threshold: float = 10

    # Removes title, tag, header text from general extraction
    general_exclude_detected_labels: bool = True

    # Map to pdf_id
    enable_pdf_id_mapping: bool = True

    # OCR
    enable_ocr: bool = True
    enable_ocr_tables: bool = True
    enable_ocr_elevations: bool = True
    enable_ocr_general: bool = True
    max_network_concurrency: int = 4

    # DPI for OCR cropped images
    dpi_ocr: int = 300

    def __post_init__(self):
        # Allows parsing page range and stores value privately
        self.pages_to_extract = self.pages_to_extract  # triggers the setter

    @property
    def pages_to_extract(self):
        return self._pages_to_extract

    @pages_to_extract.setter
    def pages_to_extract(self, pages):
        try:
            if isinstance(pages, set):
                pages = list(pages)
            self._pages_to_extract = parse_page_range(pages)
        except Exception as e:
            self._pages_to_extract = None

    @property
    def extraction_log_file(self):
        if self._extraction_log_file:
            return self._extraction_log_file
        return os.path.join("logs", "extraction_log.json")

    @extraction_log_file.setter
    def extraction_log_file(self, path):
        self._extraction_log_file = path

    # Helper functions to toggle groups of settings

    # def enable_logging(self, enabled: bool = True):
    #     self.logging_level = 2

    def asdict(self):
        """Returns a dictionary representation of the extraction configuration."""
        return asdict(self)

    def save_debug_df(self, filename: str, df):
        if filename.endswith(".xlsx"):
            try:
                df.write_excel(f"{self.save_debug_dir}/{filename}")
                return
            except Exception as e:
                pass

            try:
                df.to_excel(f"{self.save_debug_dir}/{filename}", engine="openpyxl")
                return
            except Exception as e:
                print(f"Failed to save dataframe: {e}")
        else:
            # TODO support more formats
            print(f"Unsupported file format: {filename}")
