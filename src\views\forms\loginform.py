from PySide6.QtWidgets import Q<PERSON>abel, QPushButton, QCheckBox, QLineEdit, QSizePolicy, QMessageBox
from PySide6.QtGui import QMovie
from PySide6.QtCore import Qt, QSize, Signal
from .baseform import BaseForm
from pubsub import pub
from src.pyside_util import get_resource_qicon, applyDropShadowEffect
from src.app_paths import resource_path, set_logname, saveReportFile
import keyring
import re
from threading import Thread
from src.config.atem_cloud import send_password_reset_email
from src.utils.logger import update_log_handler

KR_SERVICE_NAME = "architekt_atem"

EMAIL_DEFAULT_MESSAGE = "Email *"
PASSWORD_DEFAULT_MESSAGE = "Password *"

EMAIL_LOGIN_REQUIRED = "Error: login required"
EMAIL_LOGIN_INVALID = "Error: invalid email address"
PASSWORD_REQUIRED = "Error: password required"


def valid_email_format(email) -> bool:
    regex = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b'
    return re.fullmatch(regex, email)


class LoginForm(BaseForm):

    sgnSendVerificationEmail = Signal(str, str, bool)
    def __init__(self, parent):
        super().__init__(parent)

    def initUi(self):
        self.formSize.setHeight(480)

        self.title.setText("Login Form")
        self.subtitle.setTextFormat(Qt.TextFormat.RichText)
        self.subtitle.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)
        self.subtitle.setText("Login below or <a href='CreateAccountForm'>create an account</a>")
        self.subtitle.linkActivated.connect(self.onLinkActivated)

        self.addVSpace()

        self.lblEmail = QLabel(EMAIL_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblEmail)
        self.email: QLineEdit = QLineEdit()
        self.email.addAction(get_resource_qicon("mail.svg"), QLineEdit.LeadingPosition)
        self.email.setEchoMode(QLineEdit.EchoMode.Normal)
        self.email.returnPressed.connect(self.login)
        self.layout().addRow(self.email)

        self.addVSpace()

        self.lblPassword = QLabel(PASSWORD_DEFAULT_MESSAGE)
        self.layout().addRow(self.lblPassword)
        self.password = QLineEdit(self)
        self.password.addAction(get_resource_qicon("lock.svg"), QLineEdit.LeadingPosition)
        self.password.setEchoMode(QLineEdit.EchoMode.Password)
        self.password.textEdited.connect(self.onPasswordEdited)
        self.password.returnPressed.connect(self.login)


        self.iconEye = get_resource_qicon("eye.svg")
        self.iconEyeOff = get_resource_qicon("eye-off.svg")
        self.revealAction = self.password.addAction(self.iconEye, QLineEdit.ActionPosition.TrailingPosition)
        self.revealAction.setCheckable(True)
        self.revealAction.triggered.connect(self.onReveal)
        self.layout().addRow(self.password)

        self.addVSpace(32)

        self.chkRemember = QCheckBox(self, text="Remember Me?")
        self.layout().addRow(self.chkRemember)

        self.addStretchSpacer()
        self.addErrorStatusWidget()
        self.pbSignIn = QPushButton("Sign In")
        self.pbSignIn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbSignIn.setMinimumHeight(48)
        self.pbSignIn.clicked.connect(self.onSignIn)
        self.layout().addRow(self.pbSignIn)

        self.lblSigningIn = QLabel()
        applyDropShadowEffect(self.lblSigningIn)
        self.lblSigningIn.setMaximumHeight(48)
        self.lblSigningIn.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        self.movie.setScaledSize(QSize(30, 30))
        self.lblSigningIn.setMovie(self.movie)
        self.layout().addRow(self.lblSigningIn)
        self.lblSigningIn.hide()

        self.addVSpace(6)
        self.forgot = QLabel(" <a href='Create'>Forgot Password</a>")
        self.layout().addRow(self.forgot)
        self.forgot.linkActivated.connect(self.onForgotPassword)
        self.addVSpace()

        pub.subscribe(self.onLoginFail, "login-failed")
        pub.subscribe(self.onLoginSuccess, "login-success")

        if (keyring.get_password(KR_SERVICE_NAME, "username")
            and keyring.get_password(KR_SERVICE_NAME, keyring.get_password(KR_SERVICE_NAME, "username"))):
            self.chkRemember.setChecked(True)
            self.email.setText(keyring.get_password(KR_SERVICE_NAME, "username"))
            self.password.setText(keyring.get_password(KR_SERVICE_NAME,
                                                       keyring.get_password(KR_SERVICE_NAME, "username")))
            self.revealAction.setVisible(False)

    def onLoginFail(self, message, data=None):
        try:
            self.setErrorStatusMessage(message.get("message"))
        except:
            self.setErrorStatusMessage(str(message))
        self.lblSigningIn.hide()
        self.movie.stop()
        self.pbSignIn.setEnabled(True)
        self.pbSignIn.show()
        if message in ["Email verification required", "Email verification sent"]:
            email = data["email"]
            idToken = data["idToken"]
            prompt = data.get("prompt", True)
            params = {
                "email": email,
                "idToken": idToken,
            }
            self.sgnSwitchTo.emit("AuthenticateForm", params)
            # self.sgnSendVerificationEmail.emit(email, idToken, prompt)

    def initDefaults(self):
        self.lblEmail.clear()
        self.lblEmail.setText(EMAIL_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblEmail)
        self.lblPassword.clear()
        self.lblPassword.setText(PASSWORD_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblPassword)
        if not self.chkRemember.isChecked():
            self.email.clear()
            self.password.clear()
            # self.login() # Autologin
        self.pbSignIn.setEnabled(True)

    def onLinkActivated(self, event):
        if event == "CreateAccountForm":
            self.sgnSwitchTo.emit("CreateAccountForm")
        elif event == "Forgot":
            pass

    def onSignIn(self, event):
        """ Validate user and password """
        if not self.email.text():
            self.lblEmail.setText(EMAIL_LOGIN_REQUIRED)
            self.setWidgetError(self.lblEmail)
            return
        self.lblEmail.setText(EMAIL_DEFAULT_MESSAGE)
        self.setWidgetDefault(self.lblEmail)
        if not self.password.text():
            self.lblPassword.setText(PASSWORD_REQUIRED)
            self.setWidgetError(self.lblPassword)
            self.setWidgetError(self.password)
            return
        self.hideErrorStatus()
        self.login()

    def clearStoredCredentials(self):
        try:
            username = keyring.get_password(KR_SERVICE_NAME, "username")
            keyring.delete_password(KR_SERVICE_NAME, username)
            keyring.delete_password(KR_SERVICE_NAME, "username")
        except:
            pass # Credential do not exist

    def login(self):
        set_logname(f"{self.email.text()}.log")
        update_log_handler()
        saveReportFile()

        self.clearStoredCredentials()
        if self.chkRemember.isChecked():
            keyring.set_password(KR_SERVICE_NAME, "username", self.email.text())
            keyring.set_password(KR_SERVICE_NAME, self.email.text(), self.password.text())
        pub.sendMessage("login", email=self.email.text(), password=self.password.text())
        self.initDefaults()
        self.pbSignIn.setEnabled(False)
        self.pbSignIn.hide()
        self.lblSigningIn.show()
        self.movie.start()

    def showEvent(self, event) -> None:
        self.email.setFocus()
        self.pbSignIn.show()
        self.pbSignIn.setEnabled(True)
        self.lblSigningIn.hide()
        return super().showEvent(event)

    def onReveal(self, action):
        self.password.setEchoMode(QLineEdit.EchoMode.Normal if action else QLineEdit.EchoMode.Password)
        self.revealAction.setIcon(self.iconEyeOff if action else self.iconEye)

    def onPasswordEdited(self, event):
        if not self.revealAction.isVisible():
            self.revealAction.setVisible(True)
            self.password.clear()
            self.password.clear
            self.password.selectAll()

            password = QLineEdit()
            password.setEchoMode(QLineEdit.EchoMode.Password)
            password.textEdited.connect(self.onPasswordEdited)
            password.returnPressed.connect(self.login)

            self.layout().replaceWidget(self.password, password)
            # Cleanup
            self.password = password
            self.password.setFocus()
            if event:
                self.password.setText(event[-1])

            self.revealAction = self.password.addAction(self.iconEye, QLineEdit.ActionPosition.TrailingPosition)
            self.revealAction.setCheckable(True)
            self.revealAction.triggered.connect(self.onReveal)

    def onLoginSuccess(self):
        self.revealAction.setVisible(False)
        self.password.setEchoMode(QLineEdit.EchoMode.Password)
        self.movie.stop()

    def onForgotPassword(self):
        if not valid_email_format(self.email.text()):
            QMessageBox.information(self,
                                "Forgot password",
                                "Enter a valid email in order to confirm reset password")
            return

        res = QMessageBox.question(self,
                                   "Password reset", f"Send reset password email to {self.email.text()}?",
                                   QMessageBox.Yes|QMessageBox.No)
        if (res==QMessageBox.Yes):
            res = send_password_reset_email(self.email.text())
            print(res)
            if "error" in res:
                message = res["error"].get("message", "")
                QMessageBox.information(self, "Password Reset", f"Failed to send password reset email. {message}")
            else:
                QMessageBox.information(self, "Password Reset", f"A password reset email has been sent")
