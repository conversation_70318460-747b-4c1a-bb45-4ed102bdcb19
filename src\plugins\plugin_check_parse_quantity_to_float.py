import pandas as pd

from data_conversions import convert_quantity_to_float

def plugin_check_parse_quantity_to_float(input_file, quantity_key: str = "quantity", output_file: str = "debug/quantity_float.xlsx"):
    """
    This plugin checks unique quantity values and converts them to float.

    Saved to output file
    """
    df = pd.read_excel(input_file)

    if quantity_key not in df.columns:
        return f"{quantity_key} is not a column in the input file. Ensure correct column name."

    quantities = df[quantity_key].unique()

    results = []
    for quantity in quantities:
        results.append({
            "quantity": quantity,
            "converted": convert_quantity_to_float(quantity)
        })

    df2 = pd.DataFrame(results)
    df2.sort_values(by="converted", ascending=False, inplace=True)
    df2.to_excel(output_file, index=False)

    return output_file