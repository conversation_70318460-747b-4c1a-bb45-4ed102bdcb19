# extract_tables.py
import pprint, ast
import sys, os, re
from typing import Annotated
#from tkinter.tix import COLUMN
import fitz  # PyMuPDF fitz uses points coordinate system. 
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import numpy as np
from src.app_paths import getDataTempPath
from src.utils.logger import logger

debug_mode= False # Export pages as xlsx files

# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)


####
# Set the log level
# if debug_mode:
#     logger.setLevel(logging.DEBUG)
#
#     # Create a console handler
#     console_handler = logging.StreamHandler()
#
#     # Set the log level for the handler
#     console_handler.setLevel(logging.DEBUG)
#
#     # Create formatters and add it to the handler
#     log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#     console_handler.setFormatter(log_format)
#
#     # Add the handler to the logger
#     logger.addHandler(console_handler)
###
# Debugging: Check for non-standard characters before and after replacement
def debug_non_standard_characters(df, step):
    for col in df.columns:
        non_standard = df[df[col].apply(lambda x: has_non_standard_characters(str(x)))]
        #if not non_standard.empty:
            #print(f"Non-standard characters found in column '{col}' at step '{step}':")
            #print(non_standard)

def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def has_non_standard_characters(text):
    return any(ord(char) > 127 for char in text)

def apply_replacement_function(df):
    return df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)

# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        # if non_standard in text:
        #     print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text

# ^^^^^ General Functions ^^^^^
def analyze_and_filter_outliers(raw_table_df, table_type):
    # Analyze the most common font and font size
    common_font = raw_table_df['Font'].mode()[0]
    common_font_size = raw_table_df['FontSize'].mode()[0]

    # Identify outliers
    outlier_condition = (raw_table_df['Font'] != common_font) | (raw_table_df['FontSize'] != common_font_size)
    outliers_df = raw_table_df[outlier_condition]
    
    # Remaining data (non-outliers)
    #regular_data_df = raw_table_df[~outlier_condition]

    return outliers_df


def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None


def export_large_data_to_excel(df, filename, directory):
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

    excel_filename = os.path.join(directory, filename)

    if len(df) > 0:
        logger.info(f"Creating Workbook: {filename} in {directory} with {len(df)} rows")
        wb = Workbook()
        ws = wb.active

        # Adding header row (column names)
        ws.append(list(df.columns))

        ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
        hyperlink_font = Font(color="0000FF", underline="single")

        for index, row in df.iterrows():
            row_values = [str(value) if not is_scalar(value) else value for value in row]
            ws.append(row_values)

            # Create a hyperlink for 'AIS_LINK'
            if ais_link_col_idx:
                ais_link_value = row['AIS_LINK']
                
                # Use the relative path directly
                if ais_link_value:
                    hyperlink = ais_link_value
                    cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                    cell.hyperlink = hyperlink
                    cell.font = hyperlink_font

        try:
            wb.save(excel_filename)
            logger.info(f">>> Data exported and saved to: {excel_filename}")
        except PermissionError as e:
            logger.error(f"PermissionError: {e}")
            logger.error(f"Failed to write to {excel_filename}. The file might be open or locked.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
    else:
        logger.warning(f"The DataFrame for {filename} is empty.")

# ^^^^^ General Functions ^^^^^


def sort_by_y0(df):
    if 'Coordinates' in df.columns:
        # Create a new column 'y0' to hold the y0 values from the 'Coordinates' tuples
        df['y0'] = df['Coordinates'].apply(lambda coord: coord[1])
        # Sort the DataFrame by the 'y0' column
        df = df.sort_values(by='y0')
        # Drop the temporary 'y0' column
        df = df.drop(columns=['y0'])
        return df
    else:
        logger.warning("Column 'Coordinates' does not exist in the DataFrame.")
        return df



def get_table_coordinates(converted_roi_payload):
    identified_tables = {}  # Dictionary to hold coordinates and column details for each table type
    
    try:
        for item in converted_roi_payload:
            table_type = item.get('columnName', '').lower()
            # Check if the current item is one of the identified table types and has table coordinates
            if table_type in ['bom', 'spec', 'spool'] and 'tableCoordinates' in item:
                # logger.debug(f"Found '{table_type}' item with table coordinates")
                
                # Extract table coordinates
                table_coords = item['tableCoordinates']
                
                # Initialize list to hold column coordinates for the current table
                column_coords = []
                # Extract column coordinates
                if 'tableColumns' in item:
                    for column_dict in item['tableColumns']:
                        for column_name, coords in column_dict.items():
                            coords['columnName'] = column_name  # Add columnName for compatibility
                            column_coords.append(coords)
                            
                # Get the value of 'headersSelected'
                headers_selected = item.get('headersSelected', False)
                
                # print("\n\nHEADERS SELECTED", headers_selected)
                
                # Correctly structure the coordinates and column details for compatibility
                identified_tables[table_type] = (table_coords, column_coords, headers_selected)
    
    except Exception as e:
        logger.error(f"Error getting table coordinates: {e}", exc_info=True)
    

    return identified_tables # <-- IN USE

def combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5):
    def are_y_coords_close(coord1, coord2, tolerance):
        return abs(coord1[1] - coord2[1]) <= tolerance and abs(coord1[3] - coord2[3]) <= tolerance

    def should_combine(word1, word2):
        return word2['Coordinates'][0] - word1['Coordinates'][2] <= x_tolerance

    # Sort the dataframe by y0 and then x0 coordinates
    sorted_df = raw_df.sort_values(by=['Coordinates'], key=lambda x: [coord[1] for coord in x])
    
    combined_texts = {}
    current_line = []

    def process_line(line):
        line.sort(key=lambda x: x[1]['Coordinates'][0])  # Sort by x0
        combined_text = line[0][1]['Text']
        start_index = 0
        for j in range(1, len(line)):
            if should_combine(line[j-1][1], line[j][1]):
                combined_text += ' ' + line[j][1]['Text']
            else:
                for x in line[start_index:j]:
                    combined_texts[x[0]] = combined_text
                start_index = j
                combined_text = line[j][1]['Text']
        for x in line[start_index:]:
            combined_texts[x[0]] = combined_text

    for i, row in sorted_df.iterrows():
        if not current_line or are_y_coords_close(current_line[-1][1]['Coordinates'], row['Coordinates'], y_tolerance):
            current_line.append((i, row))
        else:
            process_line(current_line)
            current_line = [(i, row)]

    # Process the last line
    if current_line:
        process_line(current_line)

    # Create a new dataframe with the combined texts
    combined_df = pd.DataFrame.from_dict(combined_texts, orient='index', columns=['Combined_Text'])
    
    # Update or add the 'Combined_Text' column efficiently
    if 'Combined_Text' in sorted_df.columns:
        sorted_df.update(combined_df)
    else:
        sorted_df = sorted_df.join(combined_df)

    return sorted_df

def is_keyword(text, prefixes, keywords, max_words=3):
    '''
    Checks keywords like Material Labels/Descriptors, Install Type (Shop,Field etc)
    to determine whether or not to drop/ignore them when creating the table structure
    '''

    # Convert text to uppercase for case-insensitive matching
    text = text.strip().upper()
    
    # Split the text into words
    words = text.split()
    
    # Check if the text starts with any of the prefixes and has <= max_words
    if len(words) <= max_words and any(text.startswith(prefix) for prefix in prefixes):
        #print(f"\n\nDEBUG: '{text}' is a keyword (prefix match and short)")
        return True
    
    # Check if the entire text matches any of the keywords
    if text in keywords:
        #print(f"DEBUG: '{text}' is a keyword (exact match)")
        return True
    
    return False

def word_count(text):
    return len(text.split())


def starts_with_prefix_and_short(text, prefixes, max_words=2):
    words = text.split()
    result = any(text.startswith(prefix) for prefix in prefixes) and len(words) <= max_words
    # if result:
    #     print(f"DEBUG: '{text}' starts with prefix {[p for p in prefixes if text.startswith(p)]} and has {len(words)} words")
    return result

def keyword_in_first_words(text, keywords, first_n_words=5, min_total_words=5):
    words = text.split()
    if len(words) < min_total_words:
        return False
    first_words = ' '.join(words[:first_n_words])
    result = any(keyword in first_words for keyword in keywords)
    #print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    
    # if result:
    #     print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    # else:
    #     print(f"DEBUG: '{text}' does not contain a keyword in first {first_n_words} words")
    return result
    # return any(keyword in first_words for keyword in keywords)


def filter_data_by_rect(data, rect):
    return data[data.apply(lambda row: fitz.Rect(eval(row['coordinates2'])).intersects(rect), axis=1)]


def get_table_data(pdf_path, page, page_num, converted_roi_payload, page_data, missing_pos=False, remove_outliers=True): # <-- IN USE
    
    logger.info("Getting table data")

    '''
    -> Add more lists for Material Descriptors/Labels
        
    -> change outlier data to not filter the values out, but to flag/ignore the values instead. Need additional column
    -> Outlier data to keep word with outlier and without for easy merging later (Let the user decide)
    -> Add reason for outlier
    -> Check font color
    -> check type (text, annot)
    -> Add dropped keywords somewhere for review
    -> check for header values using similar logic to 'combine_nearby_words'

    '''

    #print("Getting table data")
    
    # if debug_mode==True:
    #     print(page_text_blocks)
    
    #print(f"PAGE: {page_num}", page_text_blocks)
    
    # Dictionaries to store DataFrames for each table type
    structured_tables = {}
    annotations_tables = {}
    outliers_tables = {}
    annot_outliers_tables = {}
    annot_outliers_df = pd.DataFrame()
    collected_outliers = []
    
    # logger.debug("Getting table data")
    raw_table_df = pd.DataFrame()
    structured_table_df = pd.DataFrame()
    annotations_df = pd.DataFrame()  # DataFrame for annotations
    outliers_df = pd.DataFrame()
    annot_outliers_df = pd.DataFrame()

    # Set file info
    filename = os.path.basename(pdf_path)
    directory_path = os.path.dirname(pdf_path)
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)
    
    # --> Find the items (e.g., 'BOM', 'SPEC', 'Spool') and their table coordinates
    identified_tables = get_table_coordinates(converted_roi_payload)  # Adjust this function to return a list of table types with their coordinates
    
    print(f"Identified Tables Pg. {page_num} : {identified_tables}")
    
    #print("IDENTIFIED TABLES: ", identified_tables)

    #print("\n\n --> TABLES: ", identified_tables)
    
    if not identified_tables:
        logger.error("No table coordinates found for items")
        # Return empty DataFrames for each expected table type if needed
        return structured_tables, annotations_tables, outliers_df, annot_outliers_df
    
    for table_type, (table_coords, column_coords, headers_selected) in identified_tables.items():
        # logger.debug(f"Processing {table_type} table data")
        rect = fitz.Rect(table_coords['x0'], table_coords['y0'], table_coords['x1'], table_coords['y1'])
        
        
        # # Use ast.literal_eval to safely evaluate 'coordinates2'
        # filtered_page_data = page_data[page_data.apply(
        #     lambda row: rect.intersects(fitz.Rect(ast.literal_eval(row['coordinates2']))), axis=1
        # )]
        
        # Apply a debug statement to check the exact value of coordinates2 before eval
        def safe_eval_coordinates(row):
            coordinates = row['coordinates2']
            try:
                # Log the exact value before eval for debugging purposes
                #print(f"Evaluating: {coordinates}")
                return fitz.Rect(ast.literal_eval(coordinates.strip()))  # Ensure no leading/trailing spaces
            except (ValueError, SyntaxError) as e:
                print(f"Error evaluating coordinates: {coordinates}, Error: {e}")
                return None  # Handle the case where eval fails

        # Apply the safe evaluation and filtering
        filtered_page_data = page_data[page_data.apply(
            lambda row: rect.intersects(safe_eval_coordinates(row)), axis=1
        )]
        
        # Using an f-string to insert the page number into the file path
        debug_fname = fr"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\debug\filtered_page_table_data_{page_num}.xlsx"

        # Save the DataFrame to an Excel file
        filtered_page_data.to_excel(debug_fname, index=False)

        print(f"\n\nFilter Page Data {page_num}:\nRect: {rect} \n {filtered_page_data}")
    
        # --> Extract text blocks and annotations from the defined areas
        raw_table_data = extract_text_blocks(pdf_path, page_num, rect, column_coords, table_type, filtered_page_data)

        annotations_data = pd.DataFrame() # TEMPORARY #extract_annotations(pdf_path, page, page_num, rect, table_type)
        
        # # Immediately add 'outlier_scope' to raw_table_df
        # raw_table_df['outlier_scope'] = table_type  # Add the table type as soon as the DataFrame is created
        
        # Convert raw_table_data and annotations_data to DataFrames
        raw_df = pd.DataFrame(raw_table_data)
        annotations_df = pd.DataFrame(annotations_data)
        
        if debug_mode:
            annotations_df.to_excel("Annot - get_table_data.xlsx")

        if raw_df.empty and annotations_df.empty:
            logger.warning(f"No data found for table type '{table_type}' on page {page_num}. Returning empty DataFrames.")
            continue  # Skip to the next table type

        # List of header values and ignore terms
        header_values = ["PT", "NO", "DESCRIPTION", "NPD", "(IN)", "CMDTY CODE", "QTY", "IDENT", "QTY", "POS", 
                        "HOLD", "COMPONENT DESCRIPTION", "N.B. (INS)", "ITEM CODE", "N.B.", "(INS)", "N.P.S.", "(IN)", "MATERIALS", 
                        "GMN", "MK", "MK."]
        
        ignore_terms = ["PIPE SUPPORTS", "CUT-PIECE-NO", "NPS", "LENGTH", "CONFIDENTIAL", "DESCRIPTION", 
                        "IN VISUAL CHECK FROM ENPPA", "COMPONENT DESCRIPTION", "N.B. (INS)", "PIPE", "FITTINGS", 
                        "FLANGES", "GASKETS", "BOLTS", "VALVES / IN-LINE ITEMS", "SUPPORTS", "INSTR./SP ITEMS"] # , "OTHER THAN SHOP MATERIALS", "SHOP MATERIALS"]
        
        bottom_limit_stop_flag = ["PIECE MARKS", "PIECE MARK","PIPE SPOOLS", "PIPE SPOOL", "[1]", "[2]", "[3]", "[4]", "[5]", "[6]", "[7]" ]
        
        field_keywords = ["ERECTION", "OTHER THAN SHOP MATERIALS", "ERECTION MATERIALS", "FIELD INSTALL"]
        shop_keywords = ["FABRICATION","SHOP MATERIALS", "FABRICATION MATERIALS", "FABRICATION MATERIALS COMPONENT DESCRIPTION"] # "FABRICATION",
        misc_keywords = ["MISC.", "MISCELLANEOUS COMPONENTS"]
        instr_keywords = ["INSTRUMENTS", "MISCELLANEOUS COMPONENTS"]

        # Define the prefixes for partial matches (Checks if it begins to classify the Material Scope)
        field_prefixes = ("FIELD", "ERECTION", "OTHER THAN SHOP")
        shop_prefixes = ("SHOP", "FABRICAT", "OTHER THAN FIELD")
        misc_prefixes = ("MISCELLANEOUS",)  # Note the comma to make it a tuple with one element
        instr_prefixes = ("INSTR/", "ISTR")

        # Define list of Exceptions
        exceptions = ["I-ROD", "SUPPORT - UBOLT", "SUPPORT - GUIDE", "SUPPORT - ANCHOR", "SHIM PLATE"]

        # Combine all keywords and prefixes into a single list
        all_keywords = field_keywords + list(field_prefixes) + shop_keywords + list(shop_prefixes) #+ misc_keywords + list(misc_prefixes)# + instr_keywords + list(instr_prefixes)
        #all_keywords = field_keywords + field_prefixes + shop_keywords + shop_prefixes

        # Ensure all prefixes are tuples
        field_prefixes = tuple(field_prefixes)
        shop_prefixes = tuple(shop_prefixes)
        misc_prefixes = tuple(misc_prefixes)
        instr_prefixes = tuple(instr_prefixes)

        # Ensure all keywords are lists
        field_keywords = list(field_keywords)
        shop_keywords = list(shop_keywords)
        misc_keywords = list(misc_keywords)
        instr_keywords = list(instr_keywords)

        current_material_scope = None
        
        if not raw_df.empty:

            # Add debugging print statements
            if debug_mode:
                print(f"\n\nColumns in raw_df: {raw_df.columns}")
                print(f"Shape of raw_df: {raw_df.shape}")

            # Apply non ASCII replacement function and debug
            raw_df = apply_replacement_function(raw_df)

            # Group phrases that the replacement functions can analyze
            raw_df = combine_nearby_words(raw_df, y_tolerance=1.5, x_tolerance=5)

            raw_df = sort_by_y0(raw_df)

            # Create a copy for filtering
            filtered_df = raw_df.copy()

            # Find the y0 value from the row where the text matches any value in bottom_limit_stop_flag
            bottom_limit_y0 = None
            for term in bottom_limit_stop_flag:
                if term in filtered_df['Combined_Text'].values:
                    bottom_limit_y0 = filtered_df.loc[filtered_df['Combined_Text'] == term, 'Coordinates'].iloc[0][1]
                    break

            # Drop rows where y0 is greater than or equal to bottom_limit_y0
            if bottom_limit_y0 is not None:
                filtered_df = filtered_df[filtered_df['Coordinates'].apply(lambda coord: coord[1] < bottom_limit_y0)]

            # Filter out rows where the "Combined_Text" column matches any value in filter_terms
            filter_terms = header_values + ignore_terms
            filtered_df = filtered_df[~filtered_df['Combined_Text'].isin(filter_terms)]
            
            # Distinguish Shop/Fabrication Items
            # Distinguish Shop/Fabrication Items
            for index, row in filtered_df.iterrows():
                text = row['Combined_Text'].strip().upper()
        
                # Check for prefixes first
                if starts_with_prefix_and_short(text, field_prefixes):
                    current_material_scope = "Field"
                elif starts_with_prefix_and_short(text, shop_prefixes):
                    current_material_scope = "Shop"
                elif starts_with_prefix_and_short(text, misc_prefixes):
                    current_material_scope = "Misc."
                elif starts_with_prefix_and_short(text, instr_prefixes):
                    current_material_scope = "Instr."
                    
                # If no prefix match, check for keywords
                elif keyword_in_first_words(text, field_keywords):
                    current_material_scope = "Field"
                elif keyword_in_first_words(text, shop_keywords):
                    current_material_scope = "Shop"
                elif keyword_in_first_words(text, misc_keywords):
                    current_material_scope = "Misc."
                elif keyword_in_first_words(text, instr_keywords):
                    current_material_scope = "Instr."
                # else:
                #     current_material_scope = None

                # Assign the current material scope to the row
                if current_material_scope:
                    filtered_df.at[index, 'Material Scope'] = current_material_scope

            # Filter rows based on the new criteria
            all_prefixes = field_prefixes + shop_prefixes + misc_prefixes + instr_prefixes
            all_keywords = field_keywords + shop_keywords + misc_keywords + instr_keywords
    
            # filtered_df['to_drop'] = filtered_df['Combined_Text'].apply(lambda x: 
            #     starts_with_prefix_and_short(x, all_prefixes) or
            #     keyword_in_first_words(x, all_keywords)
            # )

            # # Instead of dropping rows, let's mark them
            # filtered_df['is_keyword'] = filtered_df['Combined_Text'].apply(lambda x: 
            #     starts_with_prefix_and_short(x, all_prefixes) or
            #     keyword_in_first_words(x, all_keywords)
            # )

            # Instead of dropping rows, let's mark them
            filtered_df['is_keyword'] = filtered_df['Combined_Text'].apply(lambda x: 
                (starts_with_prefix_and_short(x, all_prefixes) or
                keyword_in_first_words(x, all_keywords)) and
                x.strip().upper() not in exceptions  # Add this condition
            )

            # Now, actually filter out the rows marked as keywords
            filtered_df = filtered_df[~filtered_df['is_keyword']]

            # Drop the 'is_keyword' column as it's no longer needed
            #filtered_df = filtered_df.drop('is_keyword', axis=1)

            #print(f"-->Final shape of filtered_df: {filtered_df.shape}")

            raw_table_df = filtered_df.copy()

            # Continue with the rest of your processing using raw_table_df
            structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords,
                                                                                headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type)

        #     
        # --> Process and structure the raw table data and annotations (Outlier, table creation)
        else:
            if not annotations_df.empty:
                structured_table_df, annotations_df, outliers_df, annot_outliers_df = process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords,
                                                                                     headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type)

        # List column names from column_coords based on 'columnName' field
        try:
            column_names = [col['columnName'] for col in column_coords]
        except Exception as e:
            logger.error(f"Could not get ROI table column names: {e}", exc_info = True)
            
        # Initialize a list to hold indices of outlier rows
        outlier_indices = []

        # Add outlier detection and logging here:
        try:
            for index, row in structured_table_df.iterrows():
                # Count non-empty and non-NaN values in the specified columns
                valid_values = row[column_names].replace('', np.nan).dropna().shape[0]
                if valid_values <= 1:  # Checking for rows with only one or no valid entries
                    #print(f"Outlier detected in {table_type} table at row {index}: {row[column_names].to_dict()}")
                    outlier_indices.append(index)  # Append index of outlier to list
        except Exception as e:
            logger.error(f"Error looping ROI column names to detect outlier rows in {table_type}: {e}", exc_info=True)
            
        #Remove outliers from structured_table_df   <-- Chaparral Test (Unexpected results with POS column in DOW BOM table)
        if remove_outliers:
            if outlier_indices:
                structured_table_df = structured_table_df.drop(outlier_indices)
                # logger.info(f"Removed {len(outlier_indices)} outliers from {table_type} table.")

            # Append the outliers DataFrame to the list for later combination
        if not outliers_df.empty:
            collected_outliers.append(outliers_df)
        
        # Store the processed DataFrames in their respective dictionaries
        
     

        structured_tables[table_type] = structured_table_df
        annotations_tables[table_type] = annotations_df
        # Outliers are collected and combined outside the loop
        #outliers_tables[table_type] = outliers_df
        annot_outliers_tables[table_type] = annot_outliers_df
    
    # Combine all collected outliers DataFrames into one
    combined_outliers_df = pd.concat(collected_outliers, ignore_index=True) if collected_outliers else pd.DataFrame()
    
    #print("\n\nCombined Outliers:", combined_outliers_df.head())  # For debugging

    return structured_tables, annotations_tables, combined_outliers_df, annotations_df # <-- Annotations should follow the same logic as text. Fix this --> BUG
    # return structured_tables, annotations_tables, combined_outliers_df, annot_outliers_tables
    
    # Return dictionaries containing structured table data, annotations, outliers for each table type
    #return structured_tables, annotations_tables, outliers_tables, annot_outliers_tables
    #return structured_table_df, annotations_df, combined_outliers_df, annot_outliers_df #, annotations_df

def process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords, headers_selected, filename, pdf_path, page_num, parent_folders_str, table_type, remove_outliers=True):
    structured_table_df = pd.DataFrame()
    #Identify outliers in the text data and logically structure it        
    if len(raw_table_df)>0:
        # export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
        potential_outliers_df = analyze_and_filter_outliers(raw_table_df, table_type)

        # Filter out outliers from the raw data - REMOVE OUTLIERS
        # structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)] #<-- Chaparal TEST
        if remove_outliers:
            structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
        else:
            structured_data = raw_table_df

        outliers_data = raw_table_df[raw_table_df.isin(potential_outliers_df).all(1)]

        # Process only non-outlier data for structured_table_df
        #structured_table_df = create_logical_structure_with_dynamic_rows(structured_data, column_coords, annot_table=False)
        structured_table_df = create_logical_structure_text(page_num, structured_data, column_coords, headers_selected, table_type, numbered_rows=True, annot_table=False)

        # Insert columns at the beginning of the dataframe
        structured_table_df.insert(0, 'pdf_page', page_num + 1)
        structured_table_df.insert(0, 'sys_filename', filename)
        structured_table_df.insert(0, 'sys_path', pdf_path)
        structured_table_df.insert(0, 'sys_build', parent_folders_str)

        # Process outliers_df
        outliers_df = outliers_data
            
    if len(annotations_df):
        
        if debug_mode:
            annotations_df.to_excel("Annot Before create_logical_structure.xlsx")

        #annotations_df = create_logical_structure_2(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)
        annotations_df = create_logical_structure_annot(annotations_df, column_coords, annot_table=True)
        # # Insert columns at the beginning of the dataframe
        annotations_df.insert(0, 'pdf_page', page_num + 1)
        annotations_df.insert(0, 'sys_filename', filename)
        annotations_df.insert(0, 'sys_path', pdf_path)
        annotations_df.insert(0, 'sys_build', parent_folders_str)
        
        if debug_mode:
            annotations_df.to_excel("Annot After create_logical_structure.xlsx")
            
        #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
    else:
            # logger.debug("\n\nAnnotations table is empty.")
            pass
    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df #, annotations_df

def process_raw_df_text(pdf_path, page_num, rect, column_coords, table_type, raw_data_df):
    processed_data = []
    
    for _, row in raw_data_df.iterrows():
        try:
            text = row['value'].strip()
            bbox = eval(row['coordinates2'])  # Assuming coordinates2 contains the more precise coordinates
            
            # Check intersection with the table area
            if not rect.intersects(fitz.Rect(bbox)):
                continue

            # Check if the value spans multiple columns
            span_columns = find_spanning_columns(bbox, column_coords)
            
            if len(span_columns) > 1:
                column_text_data = split_text_by_columns(text, bbox, column_coords)
                for split_text, split_bbox in column_text_data:
                    if split_text:
                        processed_data.append({
                            "PdfPath": pdf_path,
                            "PdfPage": page_num + 1,
                            "outlierScope": table_type,
                            "Type": row['type'],
                            "Text": split_text,
                            "Subject": row.get('subject', ''),
                            "Contents": row.get('contents', ''),
                            "Coordinates": split_bbox,
                            "Font": row.get('font', 'Unknown'),
                            "FontSize": row.get('font_size', 0),
                            "Flags": row.get('flags', 0),
                            "CreationDate": row.get('createdDate', ''),
                            "ModDate": row.get('modDate', '')
                        })
            else:
                processed_data.append({
                    "PdfPath": pdf_path,
                    "PdfPage": page_num + 1,
                    "outlierScope": table_type,
                    "Type": row['type'],
                    "Text": text,
                    "Subject": row.get('subject', ''),
                    "Contents": row.get('contents', ''),
                    "Coordinates": bbox,
                    "Font": row.get('font', 'Unknown'),
                    "FontSize": row.get('font_size', 0),
                    "Flags": row.get('flags', 0),
                    "CreationDate": row.get('createdDate', ''),
                    "ModDate": row.get('modDate', '')
                })

        except Exception as e:
            logger.error(f"Error processing row on page {page_num}: {e}", exc_info=True)

    if debug_mode:
        raw_table_df = pd.DataFrame(processed_data)
        raw_table_df.to_excel(getDataTempPath(f"Raw Table DF Page {page_num+1}.xlsx"))

    return processed_data

def extract_text_blocks(pdf_path, page_num, rect, column_coords, table_type, raw_data_df):
    processed_data = []
    
    # Filter the raw_data_df for the current page
    page_data = raw_data_df[raw_data_df['pdf_page'] == page_num + 1]
    
    for _, row in page_data.iterrows():
        try:
            text = row['value'].strip()
            bbox = eval(row['coordinates2'])  # Assuming coordinates2 contains the more precise coordinates
            
            # Check intersection with the table area
            if not rect.intersects(fitz.Rect(bbox)):
                continue

            # Check if the value spans multiple columns
            span_columns = find_spanning_columns(bbox, column_coords)
            
            if len(span_columns) > 1:
                column_text_data = split_text_by_columns(text, bbox, column_coords)
                for split_text, split_bbox in column_text_data:
                    if split_text:
                        processed_data.append({
                            "PdfPath": pdf_path,
                            "PdfPage": page_num + 1,
                            "outlierScope": table_type,
                            "Type": row['type'],
                            "Text": split_text,
                            "Subject": row.get('subject', ''),
                            "Contents": row.get('contents', ''),
                            "Coordinates": split_bbox,
                            "Font": row.get('font', 'Unknown'),
                            "FontSize": row.get('font_size', 0),
                            "Flags": row.get('flags', 0),
                            "CreationDate": row.get('createdDate', ''),
                            "ModDate": row.get('modDate', '')
                        })
            else:
                processed_data.append({
                    "PdfPath": pdf_path,
                    "PdfPage": page_num + 1,
                    "outlierScope": table_type,
                    "Type": row['type'],
                    "Text": text,
                    "Subject": row.get('subject', ''),
                    "Contents": row.get('contents', ''),
                    "Coordinates": bbox,
                    "Font": row.get('font', 'Unknown'),
                    "FontSize": row.get('font_size', 0),
                    "Flags": row.get('flags', 0),
                    "CreationDate": row.get('createdDate', ''),
                    "ModDate": row.get('modDate', '')
                })

        except Exception as e:
            logger.error(f"Error processing row on page {page_num}: {e}", exc_info=True)

    if debug_mode:
        raw_table_df = pd.DataFrame(processed_data)
        raw_table_df.to_excel(getDataTempPath(f"Raw Table DF Page {page_num+1}.xlsx"))

    return processed_data

def find_spanning_columns(bbox, column_coords):
    """Determine which columns the given bbox spans based on 'x0' and 'x1' coordinates in the column definitions."""
    x1, y1, x2, y2 = bbox
    spanning_columns = []

    # Logging the column coordinates to ensure correct data structure is received
    #logger.debug(f"Column Coords: {column_coords}")

    for idx, col_data in enumerate(column_coords):
        try:
            col_start = col_data['x0']
            col_end = col_data['x1']

            if (x1 < col_end and x2 > col_start):
                spanning_columns.append(idx)

        except KeyError as e:
            logger.error(f"Key error processing column boundaries for index {idx} with data {col_data}: {e}")

    return spanning_columns

def split_text_by_columns(text, original_bbox, column_coords):
    try:
        words = text.split()
        spans = []
        estimated_x_start = original_bbox[0]
        average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)
    
        # Ensure len(text) is not zero to avoid division by zero error
        if len(text) == 0:
            average_char_width = 0
        else:
            average_char_width = (original_bbox[2] - original_bbox[0]) / len(text)
    
        # Calculate bounding boxes for each word
        for word in words:
            word_width = len(word) * average_char_width
            word_bbox = (estimated_x_start, original_bbox[1], estimated_x_start + word_width, original_bbox[3])
            spans.append((word, word_bbox))
            estimated_x_start += word_width + average_char_width  # Account for the space between words

        # Initialize text data for each column
        column_text_data = [('', None)] * len(column_coords)

        # Assign words to columns based on bounding boxes
        for word, bbox in spans:
            assigned = False
            for idx, col_data in enumerate(column_coords):
                col_start = col_data['x0']
                col_end = col_data['x1']

                # Check if the word fits within the column boundaries
                # Adjusting condition to consider word's start point and its midpoint
                if bbox[0] >= col_start and (bbox[0] + bbox[2]) / 2 <= col_end:
                    existing_text, existing_bbox = column_text_data[idx]
                    combined_text = (existing_text + word + ' ') #.strip()
                    if existing_bbox:
                        # Update the bounding box to encompass the new word
                        new_bbox = (existing_bbox[0], existing_bbox[1], bbox[2], existing_bbox[3])
                    else:
                        new_bbox = bbox
                    column_text_data[idx] = (combined_text, new_bbox)
                    assigned = True
                    break
            if not assigned:
                #logger.warning(f"Word '{word}' at bbox {bbox} was not assigned to any column. Assigning to the first column.")
                existing_text, existing_bbox = column_text_data[0]
                combined_text = (existing_text + word + ' ').strip()
                if existing_bbox:
                    new_bbox = (min(existing_bbox[0], bbox[0]), existing_bbox[1], max(existing_bbox[2], bbox[2]), existing_bbox[3])
                else:
                    new_bbox = bbox
                column_text_data[0] = (combined_text, new_bbox)
                
                #print("\n\nCOLUMN TEXT DATA: \n", column_text_data)



        # Clean up results to remove empty entries and prepare for output
        cleaned_data = [(text.rstrip(), bbox) for text, bbox in column_text_data if text.strip()]
        #print("\n\nCLEANED DATA: \n", cleaned_data)
        return cleaned_data
    
    except ZeroDivisionError:
        # Return the original data in case of an error
        return [(text, original_bbox)]
    
def merge_wrapped_rows_text(structured_data, annot_table=False):
    #Convert Data to a Dataframe
    if annot_table: #Return the structured dataframe
        return pd.DataFrame(structured_data) 

    structured_df = pd.DataFrame(structured_data)
    
    # The first column is considered the reference for merging
    leftmost_column = structured_df.columns[0]

    # Iterate backwards through the DataFrame to merge rows
    for i in range(len(structured_df) - 1, 0, -1):
        
        # If the leftmost column is empty, merge this row with the one above
        if pd.isna(structured_df.at[i, leftmost_column]) or structured_df.at[i, leftmost_column].strip() == '':
            
            for col in structured_df.columns:
                # Skip the 'material_scope' column entirely
                if col != 'material_scope':
                    
                    # Only merge if the current row's cell is not empty
                    if not pd.isna(structured_df.at[i, col]) and structured_df.at[i, col].strip() != '':
                        structured_df.at[i - 1, col] = structured_df.at[i - 1, col].strip() + ' ' + structured_df.at[i, col].strip()
                        #print(f"\n\nMerging row {i} into row {i-1}. Content before merge: {structured_df.at[i-1, col]}, Content being merged: {structured_df.at[i, col]}")

            # After merging, drop the current row
            structured_df = structured_df.drop(index=i)
    
    # Reset index after dropping rows
    structured_df = structured_df.reset_index(drop=True)
    
    #export_large_data_to_excel(structured_df,"merged_wrapped_df_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )
    return structured_df

def create_logical_structure_text(page_num, raw_table_df, column_coords, headers_selected, table_type, numbered_rows=True, annot_table=False, y_tolerance=5): # ADDED 7-2. Accounts for minor deviation in y0 values (VG Drawings for example)):
    #logger.debug("Starting create_logical_structure_2")
    # pd.options.display.max_columns = None
    # pd.set_option('display.width', 1000)
    # pd.options.display.max_rows = None
    
    # Set up for case insensitivity
    table_type = table_type.lower().strip() 
        
    overlap_threshold=5
    
    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    # Prepare data structure for rows and columns
    structured_data = []

    # Extracting x0 and y0 from Coordinates for sorting
    raw_table_df = raw_table_df.copy()
    raw_table_df.loc[:, 'x0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[0])
    raw_table_df.loc[:, 'y0'] = raw_table_df['Coordinates'].apply(lambda coords: coords[1])

    # ###ADDED 7-2
    # # Normalize y0 values within the threshold
    # raw_table_df = normalize_y0_values(raw_table_df, y_tolerance)
    # ###ADDED 7-2

    # Sort by y0 and then by x0 for logical reading order
    raw_table_df_sorted = raw_table_df.sort_values(by=['y0', 'x0'])
    
    # Initialize variables for row tracking
    current_row_texts = []
    last_y0 = None

    for _, row in raw_table_df_sorted.iterrows():
        text = row['Contents'] if annot_table else row['Text']
        coords = row['Coordinates']
        bbox = fitz.Rect(coords)
        
        row_data = {'text': text, 'bbox': bbox}
        
        if table_type == "bom":
            #material_scope = row.get('Material Scope', '')  # Get the Material Category if it exists
            row_data['material_scope'] = row.get('Material Scope', '')
            

        # Check if we should start a new row
        if last_y0 is None or (bbox.y0 - last_y0) > overlap_threshold:
            # Before starting a new row, assign existing row texts to columns
            if current_row_texts:
                structured_data.append(assign_texts_to_columns(current_row_texts, column_coords, column_names, table_type))
                current_row_texts = []
            last_y0 = bbox.y0
            
        current_row_texts.append(row_data)
            
        # if table_type == "bom":
        #     current_row_texts.append({'text': text, 'bbox': bbox, 'material_scope': material_scope})
        # else:
        #     current_row_texts.append({'text': text, 'bbox': bbox})
            
    # Don't forget to process the last row
    if current_row_texts:
        structured_data.append(assign_texts_to_columns(current_row_texts, column_coords, column_names, table_type))
        
    # --Debug
    # Convert structured data to DataFrame for easier handling later on
    structured_df = pd.DataFrame(structured_data)
    #print("\nBEFORE MERGE DF: \n", structured_data)
    
    if debug_mode == True:
        structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))
        #structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    # --> Handle rows that should be wrapped
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)  
    
    if debug_mode == True:
        structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))
    
    return structured_df

def assign_texts_to_columns(row_texts, column_coords, column_names, table_type):
    #material_scope
    # Initialize row structure with empty strings for each column
    row_structure = {col: '' for col in column_names} # Lists to hold texts temporarily
    
    # Variable to hold the material category for this row
    row_material_scope = None

    for text_item in row_texts:
        text, bbox = text_item['text'], text_item['bbox']
        
        if table_type == 'bom':
            material_scope = text_item.get('material_scope', '')  # Get material_scope, or default to empty string
        
        for col_name, col_info in zip(column_names, column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            if col_rect.intersects(bbox):
                
                # If the column already has text, add a space before appending more text
                separator = ' ' if row_structure[col_name] else ''
                row_structure[col_name] += separator + text.strip()

                if table_type == 'bom':
                    # Store the material category for this row (if not already set)
                    if material_scope and not row_material_scope:
                        row_material_scope = material_scope

                break  # Break after assigning text to the first intersecting column

    if table_type == 'bom': 
        # Assign the material category for the entire row
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''
    
    return row_structure

def extract_annotations(pdf_path, page, page_num, rect, table_type, annotations_data=None):    
    # Now, process annotations
    if annotations_data is None:
        annotations_data = []
        
    annotations = page.annots()
    if annotations:
        for annot in annotations:
            annot_rect = annot.rect
            
            # Adjust rect for rotation matrix if needed
            annot_rect = adjust_bbox(annot_rect, page.rotation_matrix)
            
            #adjust_bbox(annot_rect, page.rotation_matrix)

            if rect.intersects(annot_rect):  # Check if the annotation intersects with the table
                annot_info = annot.info
                annotations_data.append({
                    "PdfPath": pdf_path,
                    "PdfPage": page_num + 1,
                    "outlierScope": table_type,
                    "Type": annot_info.get("type", ""),
                    "Text": '',
                    "Subject": annot_info.get("subject", ""),  # Subject might be a better descriptor
                    "Contents": annot_info.get("content", "").strip(),
                    "Coordinates": annot_rect, #(annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                    "Font": '', 
                    "FontSize": '', 
                    "Flags": '',
                    "CreationDate": annot_info.get("creationDate", ""),
                    "ModDate": annot_info.get("modDate", "")
                })
    if debug_mode:
        print(f"Page Rotation: {page.rotation}")
        print("\n\nAnnotations Data Rect:\n", annot_rect)        
        print("\n\nAnnotations Data:\n", annotations_data)
    return annotations_data

# Temporarily used for Annotation type tables until a more robust solution is found 
def create_logical_structure_annot(raw_table_df, column_coords, overlap_threshold=0, numbered_rows=True, annot_table=True):
    if debug_mode:
        print("\n\n--> ANNOT CREATE LOGICAL STRUCTURE ACCESSED...")
    #logger.debug("Starting create_logical_structure_2")
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None
    
    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]
    
    # Data structure to hold the text for each column
    structured_data = {col: [] for col in column_names}
    
    # Track the last processed y-coordinate for the first column
    last_y = None

    # Process each text item in raw_table_df
    for _, row in raw_table_df.iterrows():
        if annot_table:
            text = row['Contents']
        else:
            text = row['Text']
        #
        #print("\n\nTEXT: ", text)
        coords = row['Coordinates']
        bbox = fitz.Rect(coords)
        #print(f"\n\nText: '{text}', \nCoordinates: \n {bbox}\n")
        ##pp(bbox)

        # Determine which column the text belongs to
        for i, col_info in enumerate(column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            
            if col_rect.intersects(bbox):
                column_name = col_info['columnName']
                
                # Check if we need to start a new row
                #print(f"\nChecking new row criteria for text: '{text}', Y: {coords[1]}, Last Y: {last_y}, Overlap Threshold: {overlap_threshold}")
                if last_y is None or coords[1] > last_y + overlap_threshold:
                    # Append empty strings to columns that have no data yet
                    for col_name in structured_data:
                        structured_data[col_name].append('')
                    last_y = coords[3]
                
                if i == 0 and numbered_rows:  # Leftmost column and numbered rows
                    match = re.match(r'(\d+)(?:\.\s+)?(?:\s+(.*))?', text)
                    if match:
                        structured_data[column_name][-1] = match.group(1)
                        if match.group(2):
                            next_column_name = column_names[i+1]
                            structured_data[next_column_name][-1] = match.group(2)
                    else:
                        structured_data[column_name][-1] = text
                else:
                    structured_data[column_name][-1] = text
                break
    
    structured_df_bf = pd.DataFrame(structured_data) # TYPO???
    
    # Call the merge_wrapped_rows function on the structured DataFrame
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)  
    
    return structured_df

def extract_headers_from_structured_data(structured_df, column_coords):
    headers = {}
    header_coords = {}

    if len(structured_df) > 0:
        first_row = structured_df.iloc[0]
        
        for col_info in column_coords:
            column_name = col_info['columnName']
            if column_name in first_row:
                headers[column_name] = first_row[column_name]
                header_coords[column_name] = {
                    'x0': col_info['x0'],
                    'y0': col_info['y0'],
                    'x1': col_info['x1'],
                    'y1': col_info['y1']
                }
            else:
                headers[column_name] = ''
                header_coords[column_name] = {}
                
    #print(f"IDENTIFIED HEADERS: {headers}. COORDS: {header_coords}")

    return headers, header_coords

def search_headers_above_table(raw_table_df, column_coords):
    headers = {}
    header_coords = {}

    # Implement the logic to search for headers above the table coordinates
    # based on the raw_table_df and column_coords
    # Store the found headers and their coordinates in the headers and header_coords dictionaries

    return headers, header_coords

def store_header_reference(headers, header_coords):
    # Store the headers and their coordinates for future reference
    # You can save them to a file, database, or any other storage mechanism
    pass
# ^^^^^ General Functions ^^^^^

