# Fast Saving of files

'''
Notes:

- If a column contains multiple non-compatible dtypes, p<PERSON><PERSON><PERSON> is likely
to fail saving this data.

- PyArrow can store tuples as numpy arrays. This means we can store coordinates
column of tuple and load without parsing each field e.g. safe_literal_eval as with
using Pandas. Prefer keeping each column to a single dtype to reduce processing time
post-load.

Option 1: Save as Parquet
output_path = "path/to/your/data/raw_data"  # Adjust path as needed
save_df_fast(combined_raw_df, output_path, format='parquet')

Later, to load the data:
df = load_df_fast(f"{output_path}.parquet")

OR Option 2: Save as Feather
save_df_fast(combined_raw_df, output_path, format='feather')
Later: df = load_df_fast(f"{output_path}.feather")
'''
import pyarrow as pa
import pyarrow.parquet as pq
from pathlib import Path
import pandas as pd
from pandas.api.types import is_object_dtype


def clean_dataframe_for_pyarrow(df: pd.DataFrame, dtypes: dict = {}, ignore: list = []) -> pd.DataFrame:
    """Using Pandas inferred dtype for each column and apply, if needed,
    some cleaning before casting

    Based on the assumption that if there are mixed types in a column series,
    Pandas will detect this as an object, and so fill any nans with empty string
    Otherwise, a column with a  dtype other than object remains unchanged

    Steps:
    1. For object dtypes, fill `nan` values with empty string ``
    2. Override with user defined columns using dtypes. TODO

    Args:
        df: Input DataFrame to be cleaned
        dtypes: Explicit dtypes to set to.
        ignores: List of columns to ignore
    Returns:
        Cleaned Pandas DataFrame
    """
    for col in df.columns:
        if col in ignore:
            continue
        if is_object_dtype(df[col]):
            df.fillna({col: ""}, inplace=True)
            df[col] = df[col].astype("string")
    return df


def save_df_fast(df, output_path, format='parquet', clean: bool = False):
    """
    Save DataFrame to disk in specified format

    Args:
        df (pd.DataFrame): DataFrame to save
        output_path (str): Base path to save file (without extension)
        format (str): 'parquet' or 'feather'
        clean: Attempt to clean DataFrame before saving. Preserve original copy
    """
    if clean:
        df = clean_dataframe_for_pyarrow(df.copy())

    # Create directory if it doesn't exist
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    if format == 'parquet':
        # Parquet format - excellent compression and performance
        full_path = f"{output_path}.parquet"
        df.to_parquet(full_path, engine='pyarrow', compression='snappy')
    else:
        # Feather format - fastest read/write but larger files
        full_path = f"{output_path}.feather"
        df.to_feather(full_path)

    print(f"Saved {len(df)} rows to {full_path}")

def load_df_fast(file_path):
    """
    Load DataFrame from disk

    Args:
        file_path (str): Path to saved file (with extension)

    Returns:
        pd.DataFrame: Loaded DataFrame
    """
    if file_path.endswith('.parquet'):
        df = pd.read_parquet(file_path, engine='pyarrow')
    elif file_path.endswith('.feather'):
        df = pd.read_feather(file_path)
    else:
        raise ValueError("Unsupported file format. Use .parquet or .feather files")

    print(f"Loaded {len(df)} rows from {file_path}")
    return df


def main():
    # Convert test books for debuggin

    in_xl_path = r"debug\raw_data_full_patched_ocr.xlsx"
    out_path = r"debug\raw_data.xlsx"

    chunk = [
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_1_start_1_end_100.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_5_start_401_end_500.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_6_start_501_end_600.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_7_start_601_end_700.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_8_start_701_end_800.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_9_start_801_end_900.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_10_start_901_end_1000.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_11_start_1001_end_1100.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_12_start_1101_end_1200.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_13_start_1201_end_1300.xlsx",
        # r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_14_start_1301_end_1387.xlsx",

        r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_2_start_101_end_200.xlsx",
    r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_3_start_201_end_300.xlsx",
    r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\ocr_patch\chunks\chunk_result_job_4_start_301_end_400.xlsx",
    ]

    for c in chunk:
        f =c.rstrip(".xlsx") + ".feather"

        # load_df_fast(f)
        # exit()
        df = pd.read_excel(c)
        # Convert all object columns to string
        for col in df.select_dtypes(['object']):
            df[col] = df[col].astype(str)
        out_path = c.rstrip(".xlsx")
        save_df_fast(df, out_path, format='feather')
        # break
    exit()
    # Load excel file
    in_df = pd.read_excel(in_xl_path) # Read from Excel #load_df_fast(f"{in_xl_path}.feather")


    # Convert all object columns to string
    for col in in_df.select_dtypes(['object']):
        in_df[col] = in_df[col].astype(str)

    # Save converted file
    save_df_fast(in_df, out_path, format='feather')

if __name__ == "__main__":
    main()

