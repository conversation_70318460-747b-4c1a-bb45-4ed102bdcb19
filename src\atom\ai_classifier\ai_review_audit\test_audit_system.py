"""
Test script for the BOM Classification Audit System

This script demonstrates the audit system functionality without making actual API calls.
It shows how the system builds prompts, processes requests, and handles different scenarios.
"""

import sys
import os
import pandas as pd
import json
from datetime import datetime

# Add the parent directory to the path to import audit_main
sys.path.append(os.path.dirname(__file__))

from audit_main import (
    BOMAuditSystem, 
    AuditConfig, 
    ModelType,
    SystemPromptBuilder,
    RequestBuilder,
    CrossFieldValidationRules,
    create_sample_dataframe,
    print_audit_results
)


def test_system_prompt_building():
    """Test the system prompt building functionality"""
    print("=" * 60)
    print("TESTING SYSTEM PROMPT BUILDING")
    print("=" * 60)
    
    builder = SystemPromptBuilder()
    prompt = builder.build_system_prompt()
    
    print(f"System prompt length: {len(prompt)} characters")
    print(f"Number of fields: {len(builder.categorization_fields)}")
    print(f"Number of validation rules: {len(builder.validation_rules)}")
    
    # Show a sample of the prompt
    print("\nSample from system prompt:")
    print("-" * 40)
    lines = prompt.split('\n')
    for i, line in enumerate(lines[:20]):  # Show first 20 lines
        print(f"{i+1:2d}: {line}")
    print("... (truncated)")
    print("-" * 40)
    
    return prompt


def test_request_building():
    """Test the request building functionality"""
    print("\n" + "=" * 60)
    print("TESTING REQUEST BUILDING")
    print("=" * 60)
    
    builder = RequestBuilder()
    df = create_sample_dataframe()
    
    print("Testing with sample data:")
    print(df.to_string(index=False))
    
    print("\nBuilding requests for each row:")
    print("-" * 40)
    
    for idx, row in df.iterrows():
        row_data = row.to_dict()
        request = builder.build_request(row_data)
        
        print(f"\nRow {idx + 1} - ID: {request['id']}")
        print(f"Material Description: {request['material_description']}")
        print(f"Classification fields: {len(request['current_classification'])}")
        print(f"User prompt length: {len(request['user_prompt'])} characters")
        
        # Show current classification
        print("Current Classification:")
        for field, value in request['current_classification'].items():
            if value:  # Only show non-empty values
                print(f"  {field}: {value}")


def test_validation_rules():
    """Test the cross-field validation rules"""
    print("\n" + "=" * 60)
    print("TESTING VALIDATION RULES")
    print("=" * 60)
    
    rules = CrossFieldValidationRules.get_rules()
    
    print(f"Total validation rules: {len(rules)}")
    print("\nRule details:")
    print("-" * 40)
    
    for i, rule in enumerate(rules, 1):
        print(f"\n{i}. {rule['name']}")
        print(f"   Explanation: {rule['explanation']}")
        
        if 'condition' in rule:
            condition = rule['condition']
            requirement = rule['requirement']
            
            if 'values' in condition:
                print(f"   Condition: {condition['field']} in {condition['values']}")
            elif 'pattern' in condition:
                print(f"   Condition: {condition['field']} matches pattern")
            
            if 'values' in requirement:
                print(f"   Requirement: {requirement['field']} should be {requirement['values']}")
            elif 'not_null' in requirement:
                print(f"   Requirement: {requirement['field']} should not be null")


def test_config_and_setup():
    """Test configuration and system setup"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION AND SETUP")
    print("=" * 60)
    
    # Test default config
    default_config = AuditConfig()
    print("Default Configuration:")
    print(f"  Primary Model: {default_config.primary_model.value}")
    print(f"  Fallback Model: {default_config.fallback_model.value}")
    print(f"  Max Retries: {default_config.max_retries}")
    print(f"  Temperature: {default_config.temperature}")
    print(f"  Max Output Tokens: {default_config.max_output_tokens}")
    
    # Test custom config
    custom_config = AuditConfig(
        primary_model=ModelType.GEMINI_20_FLASH,
        fallback_model=ModelType.GEMINI_15_FLASH,
        max_retries=5,
        temperature=0.1
    )
    
    print("\nCustom Configuration:")
    print(f"  Primary Model: {custom_config.primary_model.value}")
    print(f"  Fallback Model: {custom_config.fallback_model.value}")
    print(f"  Max Retries: {custom_config.max_retries}")
    print(f"  Temperature: {custom_config.temperature}")
    
    # Test system initialization (without API calls)
    print("\nTesting System Initialization:")
    try:
        audit_system = BOMAuditSystem(default_config)
        print("✓ BOMAuditSystem initialized successfully")
        print(f"  Request builder ready: {audit_system.request_builder is not None}")
        print(f"  Initial metrics: {audit_system.get_metrics()}")
    except Exception as e:
        print(f"✗ System initialization failed: {e}")


def test_dataframe_validation():
    """Test DataFrame validation logic"""
    print("\n" + "=" * 60)
    print("TESTING DATAFRAME VALIDATION")
    print("=" * 60)
    
    # Test valid DataFrame
    valid_df = create_sample_dataframe()
    print("Valid DataFrame:")
    print(f"  Columns: {list(valid_df.columns)}")
    print(f"  Rows: {len(valid_df)}")
    print("✓ Contains required columns: id, material_description")
    
    # Test invalid DataFrame (missing required columns)
    invalid_df = pd.DataFrame({
        'item_id': ['1', '2'],  # Wrong column name
        'description': ['test1', 'test2']  # Wrong column name
    })
    
    print("\nInvalid DataFrame:")
    print(f"  Columns: {list(invalid_df.columns)}")
    print("✗ Missing required columns: id, material_description")
    
    # Test DataFrame with extra columns
    extra_df = valid_df.copy()
    extra_df['extra_column'] = ['extra1', 'extra2', 'extra3', 'extra4', 'extra5']
    
    print("\nDataFrame with extra columns:")
    print(f"  Columns: {list(extra_df.columns)}")
    print("✓ Contains required columns plus extras")


def simulate_audit_results():
    """Simulate audit results to demonstrate the output format"""
    print("\n" + "=" * 60)
    print("SIMULATING AUDIT RESULTS")
    print("=" * 60)
    
    from audit_main import AuditResult
    
    # Create simulated results
    simulated_results = [
        AuditResult(
            id="item_1",
            status="issues",
            issues={
                "material": {
                    "current_value": "Steel, Stainless",
                    "confidence": 0.95,
                    "explanation": "ASTM A106 is specifically for carbon steel pipes",
                    "suggested": "Steel, Carbon"
                },
                "rfq_scope": {
                    "current_value": "Miscellaneous",
                    "confidence": 0.90,
                    "explanation": "Material description indicates this is a pipe",
                    "suggested": "Pipe"
                }
            },
            model_used="gemini-1.5-flash",
            processing_time=1.234
        ),
        AuditResult(
            id="item_2",
            status="issues",
            issues={
                "rfq_scope": {
                    "current_value": "Pipe",
                    "confidence": 0.99,
                    "explanation": "Vendor codes provide insufficient information for specific classification",
                    "suggested": "Miscellaneous"
                }
            },
            model_used="gemini-1.5-flash",
            processing_time=0.987
        ),
        AuditResult(
            id="item_3",
            status="ok",
            issues={},
            model_used="gemini-1.5-flash",
            processing_time=0.756
        ),
        AuditResult(
            id="item_4",
            status="error",
            error_message="API rate limit exceeded",
            processing_time=0.123
        )
    ]
    
    print("Simulated audit results:")
    print_audit_results(simulated_results)


def main():
    """Run all tests"""
    print("BOM Classification Audit System - Test Suite")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    try:
        # Run all tests
        test_system_prompt_building()
        test_request_building()
        test_validation_rules()
        test_config_and_setup()
        test_dataframe_validation()
        simulate_audit_results()
        
        print("\n" + "=" * 80)
        print("ALL TESTS COMPLETED SUCCESSFULLY")
        print("=" * 80)
        print("\nThe audit system is ready for use!")
        print("To enable API calls:")
        print("1. Install: pip install google-generativeai")
        print("2. Set environment variable: GEMINI_API_KEY=your_api_key")
        print("3. Run: asyncio.run(audit_bom_dataframe(df))")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
