import os
import time
import asyncio
import datetime
import pandas as pd
from multiprocessing import freeze_support
from pathlib import Path
from src.atom.ocr.openai.prompts.llm_prompts import (build_bom_prompt,
                                                     build_spec_prompt,
                                                     build_weld_prompt,
                                                     single_prompt,
                                                     iso_area_text_prompt)
# Import OpenAI functions to handle outgoing requests
from src.atom.ocr.openai.vision.vision_client import (client,
                                                      generate_vision_requests,
                                                      process_vision_requests,
                                                      process_vision_results)
from src.atom.ocr.openai.utils.file_handling import clear_file, validate_paths, load_json_file
# from data_handlers.page_groups import create_page_groups_df_from_json
from src.atom.ocr.core.image_processing import extract_images_pdf
from src.atom.ocr.core.image_processing import get_image_folder_paths
from src.utils.convert_roi_payload import convert_roi_payload

#from processors.bom_processor import process_bom_extraction
#from processors.spec_processor import process_spec_extraction
#from processors.weld_processor import process_weld_extraction
#from processors.general_processor import process_general_extraction
#from processors.iso_processor import process_iso_extraction
#from core.extractor import setup_project_structure

async def openai_extract(pdf_path: str,
                         extract_types: list[str],
                         output_image_path: str,
                         roi_payload: str|dict,
                         base_path: str = "debug/ocr",
                         client_folder: str = "Client Folder",
                         project_folder: str = "Project Folder",
                         project_name: str = "Project Name",
                         pages: list[int] = None,
                         table_mappings: dict[str, pd.DataFrame] = {},
                         gen_field_list: list[str] = None,
                         has_gridlines=False):
    """
    Args:
        pdf_path (str): Path to the PDF file.
        extract_types (list[str]): List of extract types.
        output_image_path (str): Path to the output image.
        roi_payload (str|dict): ROI payload.
        base_path (str): Base path for output.
        pages_list (list[int]): List of pages to extract.
        table_mappings (dict[str, dict[str, str]]): Table column header mapping.
        gen_field_list (list[str]): List of general fields to extract. None for all.
        create_images (bool): Whether to create images.
        has_gridlines (bool): Whether the document has gridlines.
    """

    try:
        roi_payload = convert_roi_payload(roi_payload)
    except Exception as e:
        print(f"Warning - error converting ROI payload. Ignore if already converted")

    # This populates a dictionary of table type to list of column names
    # e.g. {"bom": ["PT NO", "COMPONENT DESCRIPTION", "DN", "QTY"]}
    column_headers = {}
    for extract_type, mapping in table_mappings.items():
        column_headers[extract_type] = [s.strip() for s in mapping["column_name"].tolist()]

    # Set BOM Table config
    include_install_type = True # Include the install type ("Shop Material/Field Materials")

    ###############################################################
    ### --> CONFIG
    ###############################################################

    # Create Paths
    if not os.path.exists(os.path.join(base_path, client_folder)): # Create Client Directory
        os.makedirs(os.path.join(base_path, client_folder))
        print(f"Created Client directory ('Client Folder') at {os.path.join(base_path, client_folder)}")

    if not os.path.exists(os.path.join(base_path, client_folder, project_folder)): # Create Client Directory
        os.makedirs(os.path.join(base_path, client_folder, project_folder))
        print(f"Created Client directory ('Client Folder') at {os.path.join(base_path, client_folder, project_folder)}")

    if not os.path.exists(os.path.join(base_path, client_folder, project_folder, "Data")): # Create Data Path
        os.makedirs(os.path.join(base_path, client_folder, project_folder, "Data"))
        #print(f"Created 'Data' directory at {os.path.join(base_path, project_folder, "Data"})

    project_base_path = os.path.join(base_path, client_folder, project_folder)

    image_folder_paths = get_image_folder_paths(output_image_path)

    # Build Paths
    bom_output_path = os.path.join(project_base_path, "Data", f"OCR BOM Data - {project_name}.xlsx")
    spec_output_path = os.path.join(project_base_path, "Data", f"OCR SPEC Data - {project_name}.xlsx")
    weld_output_path = os.path.join(project_base_path, "Data", f"OCR WELD Data - {project_name}.xlsx")
    general_output_path = os.path.join(project_base_path, "Data", f"OCR General Data - {project_name}.xlsx")
    iso_output_path = os.path.join(project_base_path, "Data", f"OCR ISO Data - {project_name}.xlsx")

    print("\n\nFolders")
    print(output_image_path)
    print(image_folder_paths)

    print("\n\nFiles")
    print(bom_output_path)
    print(general_output_path)

    extraction_prompts = {}
    for extract_type in extract_types:
        if extract_type == "weld":
            extraction_prompts["weld"] = build_weld_prompt(column_headers["weld"], has_gridlines)
        elif extract_type == "bom":
            extraction_prompts["bom"] = build_bom_prompt(column_headers["bom"], include_install_type, has_gridlines)
        elif extract_type == "spec":
            extraction_prompts["spec"] = build_spec_prompt(column_headers["spec"], has_gridlines)

    # Validate the paths
    validate_paths(image_folder_paths)

    print(f"\n\nStarting Extraction process...")

    results = {}
    filenames = {}

    def save_safe(path: str, data: pd.DataFrame, attempts=5) -> str:
        """Save and return the filename which saved to"""
        for i in range(attempts):
            time_now  = datetime.datetime.now().strftime('%m_%d_%Y_%H_%M_%S')
            path2 = f"{Path(path).parent}/{Path(path).stem}_{time_now}.xlsx"
            try:
                data.to_excel(path2)
                return path2
            except Exception:
                path2 = path
        return None

    def rename_dataframe(table_type: str, df: pd.DataFrame) -> pd.DataFrame:
        mapping = table_mappings.get(table_type, pd.DataFrame())
        rename = {}
        if "Page" in df:
            rename["Page"] = "pdf_page"

        for _, m in mapping.iterrows():
            if m["column_name"].strip() not in df.columns:
                continue
            if not m["internal_field"].strip():
                continue
            try:
                rename[m["column_name"].strip()] = m["internal_field"].strip()
            except Exception:
                pass
        try:
            df.rename(columns=rename, inplace=True)
        except Exception:
            print(f"Failed to rename {table_type} columns. Saving original columns.")

        return df.reset_index(drop=False)

    result_df = None
    os.makedirs("debug/ocr", exist_ok=True)
    for extract_type in extract_types:
        try:
            print(f"\n\n---> Generating JSONL for OCR {extract_type.upper()} Data")
            requests_jsonl = f"debug/ocr/{extract_type}_requests.jsonl"
            results_jsonl = f"debug/ocr/{extract_type}_results.jsonl"

            # Clear the files before writing
            clear_file(requests_jsonl)
            clear_file(results_jsonl)

            folder_path = image_folder_paths.get(extract_type)
            if not folder_path:
                print(f"Warning: No image folder path found for {extract_type}")
                continue

            print(f"\n\n---> OCR {extract_type.upper()} Data")

            start_time = time.time()

            if extract_type == "bom":

                await generate_vision_requests(folder_path, requests_jsonl, extraction_prompts[extract_type], pages)
                print(f"jsonl file generated: {requests_jsonl}")

                # Process the requests
                await process_vision_requests(client.api_key,
                                            requests_jsonl,
                                            results_jsonl,
                                            "Vision API table extraction")

                # Process the results
                result_df = await process_vision_results(results_jsonl)

                if result_df.empty:
                    print("Warning: The resulting DataFrame is empty. No data to export.")
                    continue

                result_df = rename_dataframe(extract_type, result_df)
                filenames[extract_type] = save_safe(bom_output_path, result_df)
                print(f"{extract_type.upper()} Exported to {bom_output_path}")

            elif extract_type == "weld":
                await generate_vision_requests(folder_path, requests_jsonl, extraction_prompts[extract_type], pages)

                print(f"jsonl file generated: {requests_jsonl}")

                # Process the requests
                await process_vision_requests(client.api_key,
                                            requests_jsonl,
                                            results_jsonl,
                                            "Vision API table extraction")

                # Process the results
                result_df = await process_vision_results(results_jsonl)

                if result_df.empty:
                    print("Warning: The resulting DataFrame is empty. No data to export.")
                    continue

                result_df = rename_dataframe(extract_type, result_df)
                filenames[extract_type] = save_safe(weld_output_path, result_df)

            elif extract_type == "spec":
                await generate_vision_requests(folder_path, requests_jsonl, extraction_prompts[extract_type], pages)
                print(f"jsonl file generated: {requests_jsonl}")
                # Process the requests
                await process_vision_requests(client.api_key,
                                            requests_jsonl,
                                            results_jsonl,
                                            "Vision API table extraction")
                result_df = await process_vision_results(results_jsonl)

                if result_df.empty:
                    print("Warning: The resulting DataFrame is empty. No data to export.")
                    continue

                result_df = rename_dataframe(extract_type, result_df)
                filenames[extract_type] = save_safe(spec_output_path, result_df)

            elif extract_type == "general":
                await generate_vision_requests(folder_path, requests_jsonl, single_prompt, is_general=True, pages=pages, gen_field_list=gen_field_list)
                print(f"JSONL file generated: {requests_jsonl}")
                await process_vision_requests(client.api_key,
                                            requests_jsonl,
                                            results_jsonl,
                                            "Vision API general data extraction")

                result_df = await process_vision_results(results_jsonl, is_general=True)

                if result_df.empty:
                    print("Warning: The resulting DataFrame is empty. No data to export.")
                    continue

                result_df = rename_dataframe(extract_type, result_df)
                filenames[extract_type] = save_safe(general_output_path, result_df)

            elif extract_type == "iso":
                await generate_vision_requests(folder_path, requests_jsonl, iso_area_text_prompt, is_general=True, pages=pages)

                print(f"JSONL file generated: {requests_jsonl}")

                await process_vision_requests(client.api_key,
                                            requests_jsonl,
                                            results_jsonl,
                                            "Vision API iso data extraction")

                result_df = await process_vision_results(results_jsonl, is_iso=True)

                if result_df.empty:
                    print("Warning: The resulting DataFrame is empty. No data to export.")
                    continue

                filenames[extract_type] = save_safe(iso_output_path, result_df)

            elif extract_type == "spool":
                pass

            elif extract_type == "ifc":
                pass

            else:
                print(f"Warning: Unknown extract type: {extract_type}")
                continue

            end_time = time.time()
            print(f"\n\n{extract_type.upper()} images analyzed in {end_time - start_time} seconds...")

            # Add to final results
            if result_df is not None:
                result_df["__ocr_method__"] = "openai"
                results[extract_type] = result_df
                result_df = None
        except Exception as e:
            # Failed to extract data. Critical
            print(f"Error processing {extract_type}: {e}")

    print("End process...")
    return results, filenames


def run(clear_images_at_start=True):
    from core.image_processing import clear_images

    freeze_support()
    output_images_path='outputImages'
    if clear_images_at_start:
        clear_images(output_images_path=output_images_path)

    asyncio.run(openai_extract())

if __name__ == "__main__":
    run(False)
