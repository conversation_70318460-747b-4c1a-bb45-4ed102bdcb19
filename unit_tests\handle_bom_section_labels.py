import pandas as pd

def process_categories(file_path, output_path=None):
    """
    Process categories in an Excel file to fill down section labels.
    
    This function:
    1. Creates "Install Type" and "Component Category" columns
    2. Fills down section labels appropriately
    3. Maps install types to SHOP/FIELD
    4. Handles special cases for missing install types
    
    Parameters:
    file_path (str): Path to the input Excel file
    output_path (str, optional): Path to save the processed file. If None, the function returns the DataFrame
    
    Returns:
    pandas.DataFrame: Processed DataFrame (if output_path is None)
    """
    # Read the Excel file
    df = pd.read_excel(file_path)
    
    # Create new columns after Section Labels
    df.insert(2, 'Install Type', '')
    df.insert(3, 'Component Category', '')
    
    # Define install types and their mappings
    install_types = ['FABRICATION MATERIALS', 'ERECTION MATERIALS', 'SHOP MATERIAL', 'FIELD MATERIAL']
    shop_types = ['FABRICATION MATERIALS', 'SHOP MATERIAL']
    field_types = ['ERECTION MATERIALS', 'FIELD MATERIAL']
    
    # Process each page separately
    for page in df['pdf_page'].unique():
        page_mask = df['pdf_page'] == page
        page_df = df[page_mask]
        
        # Reset values for each page
        current_install_type = ''
        current_component_category = ''
        
        # Fill down the section labels and categorize them
        for idx in page_df.index:
            section_label = df.at[idx, 'Section Labels']
            
            # Check if we have a section label
            if pd.notna(section_label) and section_label.strip() != '':
                # Determine if it's an install type or component category
                if section_label in install_types:
                    current_install_type = section_label
                    df.at[idx, 'Install Type'] = current_install_type
                else:
                    current_component_category = section_label
                    df.at[idx, 'Component Category'] = current_component_category
                
                # Clear the section labels after transferring
                df.at[idx, 'Section Labels'] = ''
            else:
                # Fill down the values
                df.at[idx, 'Install Type'] = current_install_type
                df.at[idx, 'Component Category'] = current_component_category
        
        # Handle special case for missing Install Type with SHOP/FIELD rules
        has_shop = any(val in shop_types for val in df.loc[page_mask, 'Install Type'].unique() 
                      if pd.notna(val) and val != '')
        has_field = any(val in field_types for val in df.loc[page_mask, 'Install Type'].unique() 
                       if pd.notna(val) and val != '')
        
        # Apply the special rules for missing install types
        if has_shop and not has_field:
            # Set blanks to FIELD
            blank_mask = page_mask & (df['Install Type'] == '')
            df.loc[blank_mask, 'Install Type'] = 'FIELD MATERIAL'
        elif has_field and not has_shop:
            # Set blanks to SHOP
            blank_mask = page_mask & (df['Install Type'] == '')
            df.loc[blank_mask, 'Install Type'] = 'SHOP MATERIAL'
    
    # Map install types to SHOP or FIELD
    shop_field_map = {
        'FABRICATION MATERIALS': 'SHOP',
        'SHOP MATERIAL': 'SHOP',
        'ERECTION MATERIALS': 'FIELD',
        'FIELD MATERIAL': 'FIELD'
    }
    
    # Apply the mapping
    df['Install Type'] = df['Install Type'].apply(
        lambda x: shop_field_map.get(x, '') if pd.notna(x) and x != '' else ''
    )
    
    # Save the result if output_path is provided
    if output_path:
        df.to_excel(output_path, index=False)
        return None
    
    return df

# Example usage
if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Downloads\Telegram Desktop\validated_df.xlsx"
    result_df = process_categories(file_path)
    
    # Save to a new file if needed
    # result_df.to_excel("processed_categories.xlsx", index=False)