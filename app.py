# main.py
import pip_system_certs.wrapt_requests  # https://pypi.org/project/pip-system-certs/4.0/ DO NOT REMOVE. Important for SSL requests

import os
import sys
import time
import asyncio

from multiprocessing import freeze_support
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from qasync import QEventLoop # Import after PySide6

import __features__
import __version__
from createDB import create_database
from src.utils.logger import logger
from src.splashscreen import SplashScreen
from src.mainwindow import MainWindow
from src.theme import stylesheet, initFonts
from src.config.workspace import Workspace

from src.cpu_pool import start_pool_in_background


def initialize_app(splash: SplashScreen):
    from src.updater import AppUpdater
    # pre-checks before running app
    splash.raise_()
    splash.setStatus("Initializing...")
    updater = None
    try:
        if __features__.CHECK_FOR_UPDATES:
            if __file__.endswith(".pyc"):
                updater = AppUpdater(blocking=True)
            else: # Dev build so non blocking update check
                updater = AppUpdater(blocking=False)
        else:
            print("Note - Build with no software updater")
    except:
        pass

    def onProgress(value):
        splash.setStatus("Downloading latest version...")
        splash.setProgress(value)

    def startUpdate(setup_file):
        logger.debug("Running installer... exiting app")
        if __file__.endswith(".pyc"):
            os.startfile(setup_file)
            sys.exit()

    # __version__.mode = "release"
    if updater:
        try:
            splash.setStatus("Checking for updates...")
            updater.progressChanged.connect(onProgress)
            updater.startUpdate.connect(startUpdate)
            updater.statusUpdate.connect(splash.setStatus)
            updater.start()
        except Exception as e:
            print(e)

        while not updater._stop.is_set():
            time.sleep(0.1)
            QApplication.processEvents()

        logger.debug(f"Updated to latest? {updater.ok}")
        if updater.ok:
            splash.setStatus("")
    else:
        time.sleep(1)

    splash.setStatus("") # Dont need to tell user
    create_database()

def on_app_exit(workspace):
    workspace.exit()
    from src.cpu_pool import shutdown_pool
    shutdown_pool()
    return 0


if __name__ == "__main__":

    freeze_support()
    # setup_logging()

    print(f"ATEM version: {__version__.version}, mode: {__version__.mode}, build_date: {__version__.build_date}")
    start_pool_in_background()
    try:
        app = QApplication(sys.argv)

        if __features__.SINGLE_INSTANCE:
            # If another instance of the app is running, prompt user and exit
            window_id = "architektatemapp"
            shared_mem_id = "architektatemmem"
            semaphore = QSystemSemaphore(window_id, 1)
            semaphore.acquire()  # Raise the semaphore, barring other instances to work with shared memory

            if sys.platform != "win32":
                # Unix-based shared memory is not freed when the application terminates abnormally,
                # so you need to get rid of the garbage
                nix_fix_shared_mem = QSharedMemory(shared_mem_id)
                if nix_fix_shared_mem.attach():
                    nix_fix_shared_mem.detach()
            shared_memory = QSharedMemory(shared_mem_id)

            # Attach a copy of the shared memory, if successful, the application is already running
            if shared_memory.attach():
                is_running = True
            else:
                shared_memory.create(1)  # allocate a shared memory block of 1 byte
                is_running = False

            semaphore.release()

            if is_running:  # if the application is already running, show the warning message
                QMessageBox.warning(None, "ATEM", "Another instance of ATEM is already running.")
                sys.exit()

        initFonts(app)
        app.setStyleSheet(stylesheet)
        loop = QEventLoop(app)
        asyncio.set_event_loop(loop)
        splash = SplashScreen()
        splash.show()
        splash.raise_()
        window = MainWindow()
        workspace = None
        initialize_app(splash)
        splash.finish(window)
        workspace = Workspace()
        window.showMaximized()
        app.exec()
        if workspace:
            sys.exit(on_app_exit(workspace))

        with loop:
            loop.run_forever()

    except Exception as e:
        logger.exception(f"Unhandled exception caused the application to crash: {e}", exc_info=True)