import pkg_resources



def check_version(current, latest):
    currentVersion = pkg_resources.parse_version(current)
    latestVersion = pkg_resources.parse_version(latest)

    print(f"current={current}, latest={latest}")
    if latestVersion > currentVersion:
        print("Later version available")
    elif latestVersion == currentVersion:
        print("Version is the latest")
    elif latestVersion < currentVersion:
        print("Warning: Latest version is less than the current")


if __name__ == "__main__":

    check_version("0.15.1", "0.15.1")
    check_version("0.15.1", "0.15.5")
    check_version("1.15.1", "0.15.5")
    check_version("0.15.1", "0.16.5")
    check_version("0.15.1", "0.12.5")