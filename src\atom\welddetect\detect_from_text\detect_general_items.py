import pandas as pd
import re, os, ast
from time import time

from src.utils.logger import logger
from src.atom.fast_storage import load_df_fast

def parse_coordinates(coord_str):
    """
    Parse coordinate string to a list of coordinates.
    
    Args:
        coord_str: String representation of coordinates
        
    Returns:
        List of coordinates [x0, y0, x1, y1] or [None, None, None, None] if parsing fails
    """
    try:
        coords = ast.literal_eval(coord_str)
        if isinstance(coords, (list, tuple)) and len(coords) == 4:
            return coords
        else:
            return [None, None, None, None]
    except (ValueError, SyntaxError):
        return [None, None, None, None]
    
def safe_string_conversion(value):
    """
    Safely convert various types to string.
    
    Args:
        value: Value to convert to string
        
    Returns:
        String representation of the value
    """
    # Handle the case when value is a pandas Series or numpy array
    if hasattr(value, 'size') and value.size > 1:
        return str(value)
    
    try:
        if pd.isna(value):
            return ""
        elif isinstance(value, (int, float)):
            return str(value)
        elif isinstance(value, str):
            return value
        else:
            return str(value)
    except (ValueError, TypeError):
        # Fallback for any other case
        return str(value)

def detect_general_items(df, search_patterns, use_regex=True, case_sensitive=False):
    """
    Detect general items in text data based on provided search patterns.
    
    Args:
        df: DataFrame containing the text data with columns 'value', 'coordinates', etc.
        search_patterns: Dictionary where keys are item types and values are lists of patterns to search for
        use_regex: Whether to use regex for pattern matching (looks for pattern within text)
        case_sensitive: Whether the search should be case sensitive
        
    Returns:
        DataFrame containing detected items with their coordinates and metadata
    """
    print(f"Starting detection with {len(df)} rows of data")
    columns = ['pdf_id', 'pdf_page', 'object_type', 'object_label', 'object_value', 
               'object_group_num', 'size', 'coordinates', 'coordinates2',
               'c1_x0', 'c1_y0', 'c1_x1', 'c1_y1', 'c2_x0', 'c2_y0', 'c2_x1', 'c2_y1']
    result_df = pd.DataFrame(columns=columns)
    
    group_num = 0
    processed_coords = set()

    # Loop through each item type and its patterns
    for item_type, patterns in search_patterns.items():
        logger.info(f"Searching for {item_type} patterns: {patterns}")
        
        # Process rows one by one
        for i, (_, row) in enumerate(df.iterrows()):
            if i % 1000 == 0:
                print(f"Processing row {i}/{len(df)}")
                
            try:
                # Get the text value
                if 'value' in row:
                    value = safe_string_conversion(row['value']).strip().upper()
                else:
                    print(f"Warning: 'value' column not found in row {i}")
                    continue
                    
                # Get coordinates
                if 'coordinates' in row:
                    coords = safe_string_conversion(row['coordinates'])
                else:
                    coords = ""
                    
                coords2 = safe_string_conversion(row.get('coordinates2', ''))
                
                if coords in processed_coords:
                    continue
                    
                # Check if the pattern is contained within the text
                pattern_found = False
                matched_pattern = None
                
                for pattern in patterns:
                    pattern_to_check = pattern if case_sensitive else pattern.upper()
                    
                    if use_regex:
                        try:
                            if re.search(pattern_to_check, value, re.IGNORECASE if not case_sensitive else 0):
                                pattern_found = True
                                matched_pattern = pattern
                                break
                        except re.error:
                            # If the pattern is not a valid regex, treat it as a literal string
                            if pattern_to_check in value:
                                pattern_found = True
                                matched_pattern = pattern
                                break
                    else:
                        if pattern_to_check in value:
                            pattern_found = True
                            matched_pattern = pattern
                            break
                
                if pattern_found:
                    group_num += 1
                    logger.info(f"Found match for {item_type}: '{matched_pattern}' in '{value}' (Group {group_num})")
                    
                    result_df = pd.concat([result_df, pd.DataFrame({
                        'pdf_id': [row.get('pdf_id', '')],
                        'pdf_page': [row.get('pdf_page', '')],
                        'object_type': [item_type],
                        'object_label': [matched_pattern],
                        'object_value': [value],
                        'object_group_num': [group_num],
                        'size': [row.get('size', None)],
                        'coordinates': [coords],
                        'coordinates2': [coords2]
                    })], ignore_index=True)
                    
                    processed_coords.add(coords)
            except Exception as e:
                print(f"Error processing row {i}: {e}")
                continue
    
    # Add parsed coordinate columns
    if not result_df.empty:
        for coord_type in ['coordinates', 'coordinates2']:
            parsed = result_df[coord_type].apply(parse_coordinates)
            result_df[[f'c{coord_type[-1]}_x0', f'c{coord_type[-1]}_y0', f'c{coord_type[-1]}_x1', f'c{coord_type[-1]}_y1']] = pd.DataFrame(parsed.tolist(), index=result_df.index)
    
    return result_df

def main():
    """
    Example usage of the detect_general_items function.
    """
    # Define search patterns for different item types
    search_patterns = {
        'gusset': ['gusset'],  # Case insensitive search will match 'GUSSET', 'Gusset', etc.
        # Add more item types and patterns as needed
        # 'bracket': ['bracket'],
        # 'support': ['support'],
    }
    
    # Path to the raw data file
    raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001\data.feather"
    
    # Output folder and file
    output_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001"
    filename_prefix = "TR-001"
    excel_output_path = os.path.join(output_folder, f"{filename_prefix}_gusset_items_test.xlsx")
    
    start_time = time()
    
    # Load the data using the fast_storage utility
    print(f"Loading data from {raw_data_path}...")
    try:
        df = load_df_fast(raw_data_path)
    except Exception as e:
        print(f"Error loading with load_df_fast: {e}")
        print("Falling back to direct pandas read_feather...")
        df = pd.read_feather(raw_data_path)
    
    # Print DataFrame info for debugging
    print("\nDataFrame info:")
    print(f"Shape: {df.shape}")
    print("Columns:")
    for col in df.columns:
        print(f"  - {col}: {df[col].dtype}")
    
    # Check if required columns exist
    required_columns = ['value', 'coordinates']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"\nWARNING: Missing required columns: {missing_columns}")
        print("Available columns:", df.columns.tolist())
        
        # Try to find similar column names
        for missing_col in missing_columns:
            similar_cols = [col for col in df.columns if missing_col.lower() in col.lower()]
            if similar_cols:
                print(f"Possible alternatives for '{missing_col}': {similar_cols}")
    
    print(f"Loaded {len(df)} rows. Processing...")
    
    # Modify column names if needed
    column_mapping = {}
    if 'value' not in df.columns and 'text' in df.columns:
        column_mapping['text'] = 'value'
    if 'coordinates' not in df.columns and 'bbox' in df.columns:
        column_mapping['bbox'] = 'coordinates'
    
    if column_mapping:
        print(f"\nRenaming columns: {column_mapping}")
        df = df.rename(columns=column_mapping)
    
    # Show a sample of the data
    print("\nSample data (first 5 rows):")
    print(df.head())
    
    # Detect general items
    try:
        result_df = detect_general_items(df, search_patterns, use_regex=True, case_sensitive=False)
    except Exception as e:
        print(f"\nError in detect_general_items: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Save results
    os.makedirs(output_folder, exist_ok=True)
    result_df.to_excel(excel_output_path, index=False)
    
    end_time = time()
    
    print(f"\nFinished...\nData exported to {excel_output_path}\nTotal elapsed time: {end_time-start_time} seconds")
    print(f"Found {len(result_df)} matching items")

if __name__ == "__main__":
    main()
