"""
Merge RFQ into BOM and General (Optionally) Dataframes

"""
from src.utils.logger import logger
from logging.handlers import RotatingFileHandler
import math

import numpy as np
import pandas as pd
from collections import defaultdict

from data_conversions import convert_quantity_to_float, process_size_column
from src.atom.ef_lookup import update_bom_ef_lookup
from src.app_paths import getDataTempPath

# logger = logging.getLogger(__file__)

# def setup_logging():
#     # Create a custom logger
#     global logger
#     logger = logging.getLogger()
#     logger.setLevel(logging.DEBUG)  # Logger is set to capture all messages from DEBUG and above.
#     #logger.setLevel(logging.INFO)  # Logger is set to capture all messages from info and above.
#
#     # Prevent adding multiple handlers to the logger
#     if not logger.handlers:
#         # Create console handler and set level to debug
#         console_handler = logging.StreamHandler()
#         #console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
#         console_handler.setFormatter(formatter)
#         logger.addHandler(console_handler)
#
#         try:
#             # Optionally, add a file handler for persistent logging
#             file_handler = RotatingFileHandler('app_log.log', maxBytes=1024*1024*5, backupCount=5)
#
#         except PermissionError as e:
#             print("No permission to set file handler")
#             file_handler = RotatingFileHandler(getDataTempPath('app_log.log'), maxBytes=1024*1024*5, backupCount=5)
#
#         file_handler.setLevel(logging.DEBUG)
#         file_handler.setFormatter(formatter)
#         # Add the handlers to the logger
#         logger.addHandler(file_handler)
#
#         print("<----------RotatingFileHandler enabled for logging (app.py)------------->")
#     else:
#         print("\n\n-----------------------------")
#         print("\n-----------------------------")
#         print("LOGGER IS ALREADY ACTIVE - COULD NOT CONFIGURE - (app.py)")
#         print("\n-----------------------------")
#         print("\n-----------------------------\n\n")

def format_size(value):
        try:
            if pd.isna(value) or value == '' or value == 'nan':
                return None
            return f"{float(value):.3f}"
        except (ValueError, TypeError):
            logger.error(f"Could not convert value to float: {value}")
            return None

def is_valid_size(size):
        return size is not None and size != '' and size != 'nan' and not pd.isna(size)

def _group_bom_data(bom_data: pd.DataFrame):
    print("Grouped Bom data columns", bom_data.columns)
    #print("\n\nDEBUG - Exporting Grouped BOM Data 1 (groupBomData)")
    #bom_data.to_excel("1  - Grouped BOM Data.xlsx")

    # grouped_bom = bom_data.groupby(['pdf_id', 'general_category', 'size1', 'size2',])['quantity'].sum().reset_index()

    # Convert 'size2' to float, replacing empty or non-numeric values with NaN
    bom_data['size2'] = pd.to_numeric(bom_data['size2'], errors='coerce')

    # Use groupby with dropna=False to keep rows with null 'size2' values
    grouped_bom = bom_data.groupby(['pdf_id',
                                    'general_category',
                                    'material_description',
                                    'size1',
                                    'size2'], dropna=False)['quantity'].sum().reset_index()

    # An empty `general_category` column becomes float64 on grouping
    # so we need to ensure correct dtype
    grouped_bom['general_category'] = grouped_bom['general_category'].astype(object)

    #print("\n\nDEBUG - Exporting Grouped BOM Data 2(groupBomData)")
    # grouped_bom.to_excel("2  - Grouped BOM Data.xlsx")

    # Merge the grouped data with the original data to preserve row values
    # grouped_bom = pd.merge(bom_data, bom_data[['pdf_id', 'general_category', 'size1', 'material_description', 'rfq_scope', 'ef', 'sf']], #'pdf_page', 'size2',
    #                     on=['pdf_id', 'general_category', 'size1', 'size2'], how='left')
    bom_subset_keys = ['__uid__', 'pdf_id', 'general_category', 'size1',
                    'size2', 'material_description', 'rfq_scope', 'ef', 'sf']

    grouped_bom = pd.merge(grouped_bom, bom_data[bom_subset_keys],
                        on=['pdf_id', 'general_category', 'material_description', 'size1', 'size2'],
                        how='left')

    # Remove duplicates using the columns we grouped on as the key
    grouped_bom = grouped_bom.drop_duplicates(subset=['pdf_id',
                                                      'general_category',
                                                      'material_description',
                                                      'size1',
                                                      'size2'])

    #print(f"Columns in deduped_bom: {deduped_bom.columns}")
    print(f"Total quantity in original bom_data: {bom_data['quantity'].sum()}")
    print(f"Total quantity in grouped_bom: {grouped_bom['quantity'].sum()}")

    return grouped_bom

def _handle_special_categories(general_data: pd.DataFrame, updated_bom: pd.DataFrame, canceled: bool = False):
    special_categories = ['LF']
    skipped_pdf_ids = set()

    print("\n\n--> Handle Existing Sizes\n\n")

    # Helper function to format size values to 3 decimal places
    # def format_size(value):
    #     try:
    #         return f"{float(value):.3f}"
    #     except (ValueError, TypeError):
    #         logger.error(f"Could not convert value to float: {value}")
    #         return None



    # Convert 'pdf_id' columns to the same datatype
    general_data['pdf_id'] = general_data['pdf_id'].astype(int)
    updated_bom['pdf_id'] = updated_bom['pdf_id'].astype(int)

    # Process size columns
    # new_sizes = updated_bom['size'].apply(lambda x: pd.Series(process_size_column(x)))
    # updated_bom['size1'] = new_sizes[0]

    # Format the 'size' columns in generalData and updatedBom
    general_data['size'] = general_data['size'].apply(format_size)
    updated_bom['size1'] = updated_bom['size1'].apply(format_size)

    # Iterate through groups of rows with the same pdf_id in updated_bom
    for pdf_id, pdf_group in updated_bom.groupby('pdf_id'):

        if canceled:
            return None, None

        # Filter rows with special categories
        special_cat_rows = pdf_group[pdf_group['general_category'].str.upper().isin(special_categories)]

        if not special_cat_rows.empty:
            # Get unique sizes from special category rows
            unique_sizes = special_cat_rows['size1'].dropna().unique()

            # Get existing sizes for this pdf_id in general_data, excluding blanks and invalid values
            existing_sizes = general_data[
                (general_data['pdf_id'] == pdf_id) &
                (general_data['size'].notna()) &
                (general_data['size'] != '') &
                (general_data['size'] != 'nan')
            ]['size'].unique()

            # Get rows with blank or invalid sizes for this pdf_id in general_data
            existing_rows = general_data[
                (general_data['pdf_id'] == pdf_id) &
                ((general_data['size'].isna()) |
                 (general_data['size'] == '') |
                 (general_data['size'] == 'nan'))
            ]

            for size in unique_sizes:
                if is_valid_size(size) and size not in existing_sizes:
                    if not existing_rows.empty:
                        first_blank_idx = existing_rows.index[0]
                        general_data.at[first_blank_idx, 'size'] = size
                        existing_rows = existing_rows.drop(first_blank_idx)
                    else:
                        pdf_rows = general_data[general_data['pdf_id'] == pdf_id]
                        if pdf_rows.empty:
                            logger.error(f"Could not find PDF ID {pdf_id}", exc_info=True)
                            skipped_pdf_ids.add(str(pdf_id))
                        else:
                            new_row = pdf_rows.iloc[0].copy()
                            new_row['size'] = size
                            general_data = pd.concat([general_data, pd.DataFrame([new_row])], ignore_index=True)

            existing_sizes = general_data[general_data['pdf_id'] == pdf_id]['size'].dropna().unique()

            # print(f"Existing Sizes Pg {pdf_id}: {existing_sizes}")


    return general_data, skipped_pdf_ids


def _check_quantities(grouped_bom: pd.DataFrame,
                      generalData: pd.DataFrame,
                      genMap: dict,
                      debug_qty=False):
    discrepancies = []
    combined_sums = defaultdict(float)

    # First, calculate the combined sums for each column in generalData
    for category, column in genMap.items():
        bom_sum = grouped_bom[(grouped_bom['general_category'] == category) &
                              (grouped_bom['size1'].notna())]['quantity'].sum()
        combined_sums[column] += bom_sum

    if debug_qty:
        print("\n\nDebugging Quantities...\n\n")

    # Now compare the combined sums with generalData
    for column, bom_sum in combined_sums.items():
        # Convert the column to numeric, treating non-numeric values as NaN
        generalData[column] = pd.to_numeric(generalData[column], errors='coerce')

        # Calculate the actual sum of the column
        general_sum = generalData[column].sum()

        if debug_qty:
            print(f"\n\n--> Column: {column}")
            print(f"BOM QTY: {bom_sum}")
            print(f"General Sum: {general_sum}")
            print(f"Sum Difference (BOM - General): {bom_sum - general_sum}")

        if not math.isclose(bom_sum, general_sum, rel_tol=1e-9):
            discrepancies.append({
                'column': column,
                'bom_sum': bom_sum,
                'general_sum': general_sum,
                'difference': bom_sum - general_sum
            })

    return discrepancies

def _cleanup_general_data(general_data: pd.DataFrame):
    #print("\n\nCleanup Columns: \n", generalData.columns)
    try:
        # Replace null/blank values
        general_data.replace(['nan', 'None', 'null'], np.nan, inplace=True)

        # Determine which column to use for sorting
        page_column = 'pdf_page' if 'pdf_page' in general_data.columns else 'page_number' if 'page_number' in general_data.columns else None

        if page_column:
            # If a valid page column exists, sort by it and 'size'
            sort_columns = ['size', page_column]
        else:
            # If neither page column exists, sort only by 'size'
            sort_columns = ['size']
            print("Warning: Neither 'pdf_page' nor 'page_number' column found. Sorting only by 'size'.")

        # Perform the sorting
        general_data.sort_values(by=sort_columns, inplace=True)

        general_data['ef'] = general_data['ef'].fillna('').replace(np.nan, '')
        general_data['sf'] = general_data['sf'].fillna('').replace(np.nan, '')
        general_data['lf'] = general_data['lf'].fillna('').replace(np.nan, '')

        # Format 'size' column to show minimum decimal places needed
        general_data['size'] = general_data['size'].apply(lambda x: ('%.15g' % float(x)) if pd.notnull(x) else x)
    except Exception as e:
        logger.warning(f"General data cleanup failed: {e}")

    # try:
    #     general_data.sort_values(by=['size', 'pdf_page'], inplace=True)
    #     logger.info("Exported Final General Data File")
    # except Exception as e:
    #     logger.warning(f"Could not sort General Data: {e}")

    # Remove duplicates
    # try:
    #     dropped = []
    #     # Check if column is empty and exclude it
    #     empty = general_data["elbows_90"].astype(str).replace(r'^\s*$', np.nan, regex=True).isna().all()
    #     if empty:
    #         dropped.append('elbows_90')
    #     else:
    #         dropped.append('90s')

    #     empty2 = general_data["elbows_45"].astype(str).replace(r'^\s*$', np.nan, regex=True).isna().all()
    #     if empty2:
    #         dropped.append('elbows_45')
    #     else:
    #         dropped.append('45s')

    #     # https://pandas.pydata.org/docs/whatsnew/v2.2.0.html#deprecated-automatic-downcasting
    #     # Ignore these warnings without suppressing for now
    #     general_data.drop(dropped, axis=1, inplace=True)
    #     logger.info(f"Dropped duplicate columns: {dropped}")
    # except Exception as e:
    #     logger.warning("Could not remove duplicates", exc_info=True)

    return general_data

def _update_general_data(general_data: pd.DataFrame, general_map: dict, bom_data: pd.DataFrame, canceled:bool = False):
    print()
    print("<--- Updating General Data from RFQ/BOM --->")
    print("Remember to update RFQ -> General merge logic")

    # print("\n\nDEBUG - Exporting General Data (Initital)")
    # general_data.to_excel("0  - Updated General Data.xlsx")

    # Ensure size column
    if 'size' not in general_data.columns:
        general_data['size'] = ''  # np.nan

    # Normalize size values
    bom_data.fillna({'size1': bom_data['size']}, inplace=True)

    # Convert quantities to floats
    bom_data['quantity'] = bom_data['quantity'].apply(convert_quantity_to_float)

    if canceled:
        return None, None
    # print("\n\nDEBUG - Exporting General Data (ensureSizeColumn)")
    # general_data.to_excel("1 - Updated General Data.xlsx")

    general_data, skipped_pdf_ids = _handle_special_categories(general_data, bom_data, canceled)
    general_data.reset_index(drop=True, inplace=True)

    # print("\n\nDEBUG - Exporting General Data (handleSpecialCategories)")
    # general_data.to_excel("2 - Updated General Data.xlsx")
    if canceled:
        return None, None

    grouped_bom = _group_bom_data(bom_data)

    # print("\n\nDEBUG - Exporting Grouped BOM Data (groupBomData)")
    # grouped_bom.to_excel("3  - Grouped BOM Data.xlsx")

    general_data = _update_general_data_quantities(general_data, general_map, grouped_bom)

    # print("\n\nDEBUG - Exporting General Data (updateGeneralDataQuantities)")
    # general_data.to_excel("3  - Updated General Data.xlsx")

    if canceled:
        return None, None

    general_data = _cleanup_general_data(general_data)

    # print("\n\nDEBUG - Exporting General Data (cleanupGeneralData)")
    # general_data.to_excel("4  - Updated General Data.xlsx")

    return general_data, skipped_pdf_ids

def _update_general_data_quantities(general_data: pd.DataFrame,
                                    general_map: dict,
                                    grouped_bom: pd.DataFrame):
    print("\n\n--------> ENTERING UPDATE GENERAL QUANTITIES")

    general_data['size'] = general_data['size'].apply(format_size)
    general_data['pdf_id'] = general_data['pdf_id'].astype(str)

    grouped_bom['size1'] = grouped_bom['size1'].apply(format_size)
    grouped_bom['size2'] = grouped_bom['size2'].apply(format_size)
    grouped_bom['pdf_id'] = grouped_bom['pdf_id'].astype(str)

    processed_items = set()
    sizes_added = set()

    for _, row in grouped_bom.iterrows():
        category = row['general_category']
        size1 = format_size(row['size1'])
        size2 = format_size(row['size2'])
        uid = row['__uid__']
        quantity = row['quantity']
        pdf_id = row['pdf_id']

        item_key = uid

        if not (category in general_map and quantity > 0 and item_key not in processed_items):
            continue

        general_column = general_map[category]

        try:
            matching_indices = general_data[(general_data['pdf_id'] == pdf_id) & (general_data['size'] == size1)].index

            # if matching_indices.empty and pd.notna(size2):
            if matching_indices.empty and is_valid_size(size2):
                matching_indices = general_data[(general_data['pdf_id'] == pdf_id) & (general_data['size'] == size2)].index
                if not matching_indices.empty:
                    size1, size2 = size2, size1

            if matching_indices.empty:
                if is_valid_size(size1):
                    size_key = (pdf_id, size1)
                    if size_key not in sizes_added:
                        new_row = general_data[general_data['pdf_id'] == pdf_id].iloc[0].copy() if not general_data[general_data['pdf_id'] == pdf_id].empty else None
                        if new_row is not None:
                            new_row['size'] = size1
                            for col in general_map.values():
                                new_row[col] = 0  # Initialize all quantity columns to 0
                            general_data = pd.concat([general_data, pd.DataFrame([new_row])], ignore_index=True)
                            matching_indices = general_data[(general_data['pdf_id'] == pdf_id) & (general_data['size'] == size1)].index
                            sizes_added.add(size_key)
                        else:
                            print(f"WARNING: Unable to insert new row for pdf_id: {pdf_id}, size: {size1}, category: {category}")
                            continue
                    else:
                        print(f"INFO: Skipping size addition for pdf_id: {pdf_id}, size: {size1} (already added)")
                else:
                    print(f"INFO: Skipping invalid size for pdf_id: {pdf_id}, size: {size1}, category: {category}")
                    continue

            if not matching_indices.empty:
                for idx in matching_indices:
                    try:
                        float(general_data.at[idx, general_column])
                    except:
                        # Initialize values to 0
                        general_data.at[idx, general_column] = 0

                    # prev_value = general_data.at[idx, general_column]
                    general_data.at[idx, general_column] = float(general_data.at[idx, general_column]) + float(quantity)
                    # print(f"INFO: Updated {general_column} for pdf_id: {pdf_id}, size: {size1}. Previous: {prev_value}, Added: {quantity}, New: {generalData.at[idx, general_column]}")

                    # Update 'ef' and 'sf' columns
                    matching_ef = grouped_bom[(grouped_bom['pdf_id'] == pdf_id) & (grouped_bom['size1'] == size1)]['ef']
                    matching_sf = grouped_bom[(grouped_bom['pdf_id'] == pdf_id) & (grouped_bom['size1'] == size1)]['sf']

                    matching_ef = pd.to_numeric(matching_ef, errors='coerce')
                    matching_sf = pd.to_numeric(matching_sf, errors='coerce')

                    matching_ef_sum = matching_ef.sum()
                    matching_sf_sum = matching_sf.sum()

                    try:
                        lf_value = float(general_data.at[idx, 'lf'])
                    except:
                        lf_value = 0 # Initialize values to 0
                    general_data.at[idx, 'ef'] = matching_ef_sum + float(lf_value)
                    general_data.at[idx, 'sf'] = matching_sf_sum

                processed_items.add(item_key)
            else:
                print(f"WARNING: No matching indices found for UID: {uid}, pdf_id: {pdf_id}, size1: {size1}, category: {category}")

        except Exception as e:
            logger.exception(e)
            print(f"ERROR: Error updating quantity for pdf_id {pdf_id}, size {size1}, category {category}: {e}")

    # Check quantities
    discrepancies = _check_quantities(grouped_bom, general_data, general_map, debug_qty=True)
    if discrepancies:
        print("\n\nDiscrepancies found:")
        for disc in discrepancies:
            print(f"--> Column: {disc['column']}")
            print(f"    BOM sum: {disc['bom_sum']}")
            print(f"    General Data sum: {disc['general_sum']}")
            print(f"    Difference: {disc['difference']}\n")
    else:
        print("\n\nNo discrepancies found. All quantities match.")

    return general_data

def _update_bom_data(bom_data: pd.DataFrame, rfq_data: pd.DataFrame, rfq_fields: list):
    """Prepare BOM data for merge by extracting a subset and performing EF lookup"""

    # Exclude 'componentCategory' from RFQ columns
    rfq_columns = [col for col in rfq_fields if col != 'componentCategory']

    key_fields = ['size', 'material_description']
    rfq_subset = rfq_data[key_fields + rfq_fields].drop_duplicates()
    updated_bom = pd.merge(bom_data,
                          rfq_subset,
                          on=key_fields,
                          how='left',
                          suffixes=('', '_rfq_update'))

    for field in rfq_fields:
        bom_field = field + '_rfq_update'
        if not bom_field in updated_bom:
            continue
        updated_bom[field] = updated_bom[bom_field].combine_first(updated_bom[field])
        del updated_bom[bom_field]  # Remove the temporary update columns

    # EF lookup update
    update_bom_ef_lookup(bom_data)

    return updated_bom

#
# Public functions
#
#

save_input_data = True


def merge_rfq_into_bom(rfq_data: pd.DataFrame,
                       rfq_fields: list,
                       bom_data: pd.DataFrame,
                       general_data: pd.DataFrame = None,
                       general_map: dict = None,
                       canceled = False) -> None:

    print("\n\n--> ENTERING merge_rfq_into_bom\n\n")

    if canceled:
        return

    updated_bom = _update_bom_data(bom_data, rfq_data, rfq_fields)

    if canceled:
        return

    def to_excel(df, filename):
        try:
            df.to_excel(filename)
        except:
            logger.info(f"Failed to save debug data, {filename}", exc_info=True)

    if save_input_data and not __file__.endswith(".pyc"):
        to_excel(rfq_data, 'debug/my_rfq_data.xlsx')
        to_excel(bom_data, 'debug/my_bom_data.xlsx')
        to_excel(general_data, 'debug/my_general_data.xlsx')

    if canceled:
        return

    if general_data is None:
        general_data = pd.DataFrame()
    if bom_data is None:
        bom_data = pd.DataFrame()

    skipped_pdf_ids = None
    # Update General data using the merged BOM data
    if all([not general_data.empty, not updated_bom.empty, general_map]):
        updated_general, skipped_pdf_ids = _update_general_data(general_data, general_map, updated_bom, canceled)
    else:
        updated_general = None
        logger.warning("General Data or gen_map was None and was not updated")

    if canceled:
        return

    result = {
        "bom_data": updated_bom,
        "general_data": updated_general,
    }

    # updated_bom.to_excel('updated_bom.xlsx')
    if skipped_pdf_ids:
        result["skipped_pdf_ids"] = skipped_pdf_ids
    return result


# Example test merge
if __name__ == '__main__':
    from os import path
    import sys
    sys.path[0] = ""

    # Setup Logging
    # setup_logging()

    gen_map = {
        "LF": "lf",
        "90 Short Radius": "elbows_90",
        "90 Long Radius": "elbows_90",
        "45": "elbows_45",
        "Bevel": "bevels",
        "Tee": "tees",
        "Reducer (Concentric)": "reducers",
        "Reducer (Eccentric)": "reducers",
        "Cap": "caps",
        "Flanges": "flanges",
        "Flanged Valve": "valves_flanged",
        "Welded Valve": "valves_welded",
        "Cut Out": "cut_outs",
        "Bend": "bends",
        "Field Weld": "field_welds",
        "Support": "supports"
    }

    rfq_fields = ['rfq_scope', 'general_category', 'size1', 'size2',
                  'unit_of_measure', 'material', 'abbreviated_material',
                  'ansme_ansi', 'astm', 'grade', 'rating', 'schedule',
                  'coating', 'forging', 'ends', 'item_tag', 'tie_point',
                  'pipe_category', 'valve_type', 'fitting_category',
                  'weld_category', 'bolt_category', 'gasket_category', 'ef', 'sf']

    sample_dir = "src/tests/samples/merge_rfq_into_bom"

    # To emulate app behavior, we need to also specify the dtypes

    rfq_dtypes = {
        '__uid__': 'int64', 'material_description': 'object', 'size': 'object',
        'quantity': 'float64', 'size1': 'object', 'size2': 'object', '__checked__': 'bool',
        'rfq_scope': 'object', 'general_category': 'object', 'unit_of_measure': 'object',
        'material': 'object', 'abbreviated_material': 'object', 'ansme_ansi': 'object',
        'astm': 'object', 'grade': 'object', 'rating': 'object', 'schedule': 'object',
        'coating': 'object', 'forging': 'object', 'ends': 'object', 'item_tag': 'object',
        'tie_point': 'object', 'pipe_category': 'object', 'valve_type': 'object',
        'fitting_category': 'object', 'weld_category': 'object', 'bolt_category': 'object',
        'gasket_category': 'object', 'ef': 'object', 'sf': 'object', 'review_explanation': 'object',
        'answer_explanation': 'object', 'review': 'object'
    }

    bom_dtypes = {
        '__uid__': 'int64', 'id': 'int64', 'page_number': 'int64', 'pos': 'object',
        'ident': 'object', 'size': 'object', 'material_description': 'object',
        'quantity': 'float64', 'item': 'object', 'tag': 'object', 'pdf_id': 'int64',
        'status': 'object', '__checked__': 'bool', 'rfq_scope': 'object',
        'general_category': 'object', 'size1': 'object', 'size2': 'object',
        'unit_of_measure': 'object', 'material': 'object', 'abbreviated_material': 'object',
        'ansme_ansi': 'object', 'astm': 'object', 'grade': 'object', 'rating': 'object',
        'schedule': 'object', 'coating': 'object', 'forging': 'object', 'ends': 'object',
        'item_tag': 'object', 'tie_point': 'object', 'pipe_category': 'object',
        'valve_type': 'object', 'fitting_category': 'object', 'weld_category': 'object',
        'bolt_category': 'object', 'gasket_category': 'object', 'ef': 'object', 'sf': 'object'}

    general_dtypes = {
        '__uid__': 'int64', 'id': 'int64', 'pdf_id': 'int64', 'annotMarkups': 'object',
        'area': 'object', 'avg_elevation': 'object', 'blockCoordinates': 'object',
        'clientDocumentId': 'object', 'coordinates': 'object', 'designCode': 'object',
        'documentDescription': 'object', 'documentId': 'object', 'documentTitle': 'object',
        'drawing': 'object', 'elevation': 'object', 'flangeID': 'object', 'heatTrace': 'object',
        'insulationSpec': 'object', 'insulationThickness': 'object', 'lineNumber': 'object',
        'max_elevation': 'object', 'mediumCode': 'object', 'min_elevation': 'object',
        'modDate': 'object', 'paintSpec': 'object', 'pid': 'object', 'pipeSpec': 'object',
        'pipeStandard': 'object', 'processLineList': 'object', 'processUnit': 'object',
        'projectNo': 'object', 'projectName': 'object', 'pwht': 'object', 'revision': 'object',
        'sequence': 'object', 'service': 'object', 'sheet': 'object', 'size': 'object',
        'sys_build': 'object', 'sys_layout_valid': 'object', 'sysDocument': 'object',
        'sysDocumentName': 'object', 'sys_filename': 'object', 'sys_path': 'object',
        'system': 'object', 'totalSheets': 'object', 'unit': 'object', 'vendorDocumentId': 'object',
        'weldId': 'object', 'weldClass': 'object', 'xCoord': 'object', 'xray': 'object',
        'yCoord': 'object', 'lf': 'object', 'sf': 'object', 'ef': 'object', 'elbows_90': 'object',
        'elbows_45': 'object', 'bevels': 'object', 'tees': 'object', 'reducers': 'object',
        'caps': 'object', 'flanges': 'object', 'valves_flanged': 'object', 'valves_welded': 'object',
        'cut_outs': 'object', 'supports': 'object', 'bends': 'object', 'field_welds': 'object',
        'page_number': 'int64', '__checked__': 'bool'}


    # Specify test data paths here
    rfq_file = path.join(sample_dir, "sample_rfq_data.xlsx")
    bom_file = path.join(sample_dir, "sample_bom_data.xlsx")
    general_file = path.join(sample_dir, "sample_general_data.xlsx")


    rfq_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\BOG\RFQ Tests\my_rfq_data.xlsx"
    bom_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\BOG\RFQ Tests\my_bom_data.xlsx"
    general_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\BOG\RFQ Tests\my_general_data.xlsx"


    general_file = r"C:\Drawings\13-march-combined isos\final data\exported_general_data_nofieldmap.xlsx"

    rfq_data = pd.read_excel(rfq_file, dtype=rfq_dtypes)
    bom_data = pd.read_excel(bom_file, dtype=bom_dtypes)
    general_data = pd.read_excel(general_file, dtype=general_dtypes)

    bom_data.reset_index(inplace=True)
    if not "__uid__" in bom_data:
        bom_data["__uid__"] = bom_data.index + 1

    result = merge_rfq_into_bom(rfq_data,
                                rfq_fields,
                                bom_data,
                                general_data,
                                gen_map)
    # print(result["general_data"])
    save_result = True
    if save_result:
        result["bom_data"].to_excel("debug/merged_bom_result.xlsx")
        result["general_data"].to_excel("debug/merged_general_result.xlsx")
