import platform, io, ast, random
from http.client import PARTIAL_CONTENT
import sys, os, re, json, argparse
import multiprocessing
from multiprocessing import freeze_support
#from fitz.extra import page_count
import pandas as pd
import fitz  # PyMuPDF fitz uses points coordinate system. 
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import time
import uuid
import threading
import numpy as np
from fractions import Fraction
from PySide6.QtWidgets import QApplication, QMessageBox
from collections import defaultdict

print_converted_coords = False

# logger = logging.getLogger(__name__)


def load_and_combine_jsons(json_paths):
    combined_json = {}
    for group_number, path in json_paths.items():
        try:
            with open(path, 'r') as file:
                combined_json[str(group_number)] = json.load(file)
        except Exception as e:
            logger.error(f"Failed to load JSON for group {group_number}: {e}")
    return combined_json

def multi_convert_relative_coords_to_points(combined_roi_payload, page_groups):
    converted_payload = {}
    
    for group_number, roi_payload in combined_roi_payload.items():
        group_data = page_groups[page_groups['group_number'] == int(group_number)].iloc[0]
        width, height = group_data['width'], group_data['height']
        x_offset, y_offset = 0, 0  # You can add these to page_groups if needed
        
        converted_group_payload = []
        
        for item in roi_payload:
            try:
                converted_item = {"columnName": item.get("columnName", "Unknown")}
                process_coordinates(item, converted_item, width, height, x_offset, y_offset)
                if "headersSelected" in item:
                    converted_item["headersSelected"] = item["headersSelected"]
                converted_group_payload.append(converted_item)
            except Exception as e:
                logger.error(f"Error processing item {item.get('columnName', 'Unknown')} in group {group_number}: {e}")
        
        converted_payload[str(group_number)] = converted_group_payload
    
    if print_converted_coords:
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and "tableColumns" not in item:
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))

def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset

    }

# --> Handle ROI Functions

#####################
#####################
#####################

# --> Handle Raw Data Functions
def adjust_bbox(bbox, rotation, page_width, page_height):
    """
    Adjust a bounding box based on the page's rotation and dimensions.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation: The rotation angle in degrees (0, 90, 180, or 270).
    - page_width: The width of the page.
    - page_height: The height of the page.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    x0, y0, x1, y1 = bbox

    if rotation == 90:
        return (y0, page_width - x1, y1, page_width - x0)
    elif rotation == 180:
        return (page_width - x1, page_height - y1, page_width - x0, page_height - y0)
    elif rotation == 270:
        return (page_height - y1, x0, page_height - y0, x1)
    else:  # rotation == 0 or any other value
        return bbox

