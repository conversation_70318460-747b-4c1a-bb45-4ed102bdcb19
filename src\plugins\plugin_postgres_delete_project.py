import pandas as pd
from psycopg2.extras import execute_values
from datetime import datetime

from src.atom.pg_database import pg_connection


def plugin_postgres_delete_project(project_id: int = 0,
                                   confirm: str = ""):
    """
    Deletes a project and all associated data from the PostgreSQL database.

    This will delete:
    1. All BOM data associated with the project
    2. All general data associated with the project
    3. The project record itself

    Args:
        project_id (int): Project ID to delete (must be >= 1)
        confirm (str): Must be exactly "confirm" to proceed with deletion

    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Request to delete project ID {project_id} from PostgreSQL database.")

    # Validate project_id
    try:
        project_id = int(project_id)
        if project_id <= 0:
            print("Error: project_id must be a positive integer.")
            return False
    except (ValueError, TypeError):
        print(f"Error: Invalid project_id: {project_id}")
        return False

    # Validate confirmation
    if confirm != "confirm":
        print("Error: You must set confirm='confirm' to proceed with deletion.")
        print("This is a safety measure to prevent accidental deletion.")
        return False

    # Use database connection context manager
    try:
        with pg_connection.get_db_connection() as conn:
            with conn.cursor() as cursor:
                # First check if the project exists
                cursor.execute("SELECT project_name FROM public.atem_projects WHERE id = %s", (project_id,))
                project = cursor.fetchone()

                if not project:
                    print(f"Error: Project with ID {project_id} does not exist.")
                    return False

                project_name = project[0]
                print(f"Found project: {project_name} (ID: {project_id})")
                print("Proceeding with deletion...")

                # Count records that will be deleted
                cursor.execute("SELECT COUNT(*) FROM public.bom WHERE project_id = %s", (project_id,))
                bom_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM public.general WHERE project_id = %s", (project_id,))
                general_count = cursor.fetchone()[0]

                print(f"Will delete: {bom_count} BOM records, {general_count} general records, and 1 project record.")

                # Delete associated data
                # Note: If CASCADE is properly set up in the database schema, this might not be necessary,
                # but we're being explicit for safety and clarity

                # Delete BOM data
                cursor.execute("DELETE FROM public.bom WHERE project_id = %s", (project_id,))
                print(f"Deleted {cursor.rowcount} BOM records.")

                # Delete general data
                cursor.execute("DELETE FROM public.general WHERE project_id = %s", (project_id,))
                print(f"Deleted {cursor.rowcount} general records.")

                # Finally, delete the project itself
                cursor.execute("DELETE FROM public.atem_projects WHERE id = %s", (project_id,))
                print(f"Deleted project: {project_name} (ID: {project_id})")

                # Commit the transaction
                conn.commit()
                print("Deletion completed successfully.")
                return True

    except Exception as e:
        # Transaction will automatically roll back on exception
        print(f"Error during deletion: {e}")
        print("No changes were made to the database (transaction rolled back).")
        import traceback
        traceback.print_exc()
        return False