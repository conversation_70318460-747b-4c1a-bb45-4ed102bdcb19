import fitz  # PyMuPDF
import pandas as pd
import re

filename = r"C:\Drawings\Clients\axisindustries\Axis 2025-08-05 - Heartwell OSBL\received\Heartwell OSBL Combined.pdf"
save_filename = r"C:\Drawings\Clients\axisindustries\Axis 2025-08-05 - Heartwell OSBL\received\Heartwell OSBL Combined_boxes.pdf"
doc = fitz.open(filename)

# Create a new document for saving annotated version
output_doc = fitz.open()

all_dfs = []  # Store DataFrames from all pages

page_list = [
    7, 37, 75, 156, 167, 169, 239, 251, 301, 312, 331, 355, 399, 411, 442, 445, 475, 482, 484, 494, 514, 590,
    630, 671, 684, 690, 714, 723, 753, 769, 804, 836, 885, 888, 914, 1050, 1081, 1089, 1091, 1096, 1115, 1126, 1131, 1223,
    1228, 1241, 1339, 1352, 1389, 1404, 1406, 1420, 1436, 1488, 1517, 1537, 1579, 1598, 1599, 1611, 1618, 1639, 1657, 1685,
    1692, 1802, 1824, 1825, 1876, 1954, 1959, 1979, 2019, 2045, 2050, 2053, 2055, 2064, 2077, 2078, 2106, 2147, 2167, 2230,
    2251, 2256, 2257, 2311, 2343, 2358, 2386, 2390, 2403, 2410, 2424, 2425, 2453, 2498, 2569, 2588
]

for pdf_page in page_list:
    page = doc[pdf_page - 1]

    # Get all words on the page
    words = page.get_text("words")  # list of (x0, y0, x1, y1, word, block_no, line_no, word_no)

    print(f"Processing page {pdf_page}")

    # Create DataFrame for this page
    page_df = pd.DataFrame(words, columns=[
        "x0", "y0", "x1", "y1", "word", "block_no", "line_no", "word_no"
    ])
    page_df["pdf_page"] = pdf_page
    page_df = page_df.sort_values(by=["y0", "x0"]).reset_index(drop=True)


    # Find FW words on this page
    fw_df = page_df[(page_df["word"].isin(["FW", "FFW"])) & (page_df["y0"] < 0.85 * page.rect.height)]

    # Add to collection
    all_dfs.append(fw_df)

    # Define crop boundaries based on guide lines
    cutoff_x = 0.7 * page.rect.width
    cutoff_y = 0.85 * page.rect.height
    crop_rect = fitz.Rect(0, 0, cutoff_x, cutoff_y)

    # Copy the page to output document with cropped dimensions
    new_page = output_doc.new_page(width=cutoff_x, height=cutoff_y)
    new_page.show_pdf_page(crop_rect, doc, pdf_page - 1, clip=crop_rect)

    # Add page number and FW count to top left
    fw_count_on_page = len(fw_df)
    page_info = f"Page {pdf_page} | FW Count: {fw_count_on_page}"

    # Create a background rectangle for the text
    text_rect = fitz.Rect(10, 10, 200, 30)
    new_page.draw_rect(text_rect, color=(0, 0, 0), fill=(1, 1, 1), width=1)  # White background with black border

    # Insert the page info text
    new_page.insert_text((15, 25), page_info, fontsize=12, color=(0, 0, 0))

    # Draw bounding boxes around FW words (only those within the cropped area)
    for index, row in fw_df.iterrows():
        # Only draw boxes for FW words within the cropped area
        if row["x0"] <= cutoff_x and row["y0"] <= cutoff_y:
            bbox = fitz.Rect(row["x0"], row["y0"], row["x1"], row["y1"])

            # Draw a red rectangle around the FW text
            new_page.draw_rect(bbox, color=(1, 0, 0), width=2)  # Red color, 2pt width

            # Optionally add a label
            label_rect = fitz.Rect(bbox.x0, bbox.y0 - 15, bbox.x1 + 20, bbox.y0)
            new_page.draw_rect(label_rect, color=(1, 0, 0), fill=(1, 1, 0))  # Yellow fill
            new_page.insert_text((bbox.x0, bbox.y0 - 2), "FW", fontsize=8, color=(0, 0, 0))

# Combine all DataFrames
if all_dfs:
    df = pd.concat(all_dfs, ignore_index=True)
else:
    df = pd.DataFrame()

# Get FW count per page
fw_count = df.groupby("pdf_page").size()

# Ensure all pages from page_list are included with count 0 if no FW words found
fw_count_complete = pd.Series(0, index=page_list, name='fw_count')
fw_count_complete.update(fw_count)
fw_count = fw_count_complete

print("FW count per page:")
print(fw_count)

fw_count.to_excel("debug/fw_count.xlsx")

# Get all FW words
fw_words = df[df["word"] == "FW"]
print(f"\nTotal FW words found: {len(fw_words)}")
print(fw_words[["pdf_page", "x0", "y0", "x1", "y1", "word"]])

# Save the annotated PDF
output_doc.save(save_filename)
output_doc.close()

# Save the word data to Excel
df.to_excel("debug/fw_words.xlsx", index=False)

print(f"\nAnnotated PDF saved to: {save_filename}")
print(f"Word data saved to: debug/fw_words.xlsx")

# Close the original document
doc.close()
