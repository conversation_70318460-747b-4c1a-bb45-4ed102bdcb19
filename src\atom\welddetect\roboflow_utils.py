import requests
import base64
from io import BytesIO

CONFIDENCE = 0.5
IOU_THRESH = 0.5

class RoboFlowHelper():

    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.workspace = "architekt-integrated-systems"
        self.workspaceData = {}

    def fetch_info(self):
        if not self.api_key:
            raise Exception("No api key set")
        res = requests.get(
            f"https://api.roboflow.com/{self.workspace}?api_key={self.api_key}",
        )
        self.workspaceData = res.json()
        return self.workspaceData

    @property
    def project_ids(self):
        try:
            return [project.get("id") for project in self.workspaceData.get("workspace", {}).get("projects", [])]
        except:
            return []

    @property
    def projects(self) -> list:
        try:
            return self.workspaceData.get("workspace", {}).get("projects", [])
        except:
            return []

    def run_prediction(self, image_buffer: BytesIO, project: str, version: int):
        img_str = base64.b64encode(image_buffer.getvalue())
        img_str = img_str.decode("ascii")

        res = requests.post(
            f"https://detect.roboflow.com/{project}/{version}?api_key={self.api_key}&confidence={CONFIDENCE}&overlap={IOU_THRESH}",
            headers={"Content-Type": "application/json"},
            data=img_str,
        )
        predictions = res.json()
        return predictions


if __name__ == "__main__":

    rf = RoboFlowHelper()
    projectName = "eng-od-1"
    projectName = "architekt-integrated-systems/eng-od-1"