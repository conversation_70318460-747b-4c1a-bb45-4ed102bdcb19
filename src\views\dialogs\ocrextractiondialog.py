import os
import fitz  # PyMuPDF
import json
import re
import asyncio

import pandas as pd
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime

from src.atom.dbManager import DatabaseManager
from src.app_paths import getSourceRawDataPath, getSourceDataDir
from src.utils.convert_roi_payload import convert_roi_payload
from src.atom.ocr.core.image_processing import extract_images_pdf
from src.atom.ocr.openai_ocr_main import openai_extract
from src.atom.ocr.winrt.structure_table_results import extract_bom_table_from_ocr, extract_standard_table
from src.atom.ocr.winrt.extract_win import process_folder


OCR_OPTIONS = ["openai", "winrt"]

TABLE_TYPES = ["bom", "spec", "spool", "ifc", "generic_1", "generic_2", "weld"]

def parse_page_range(page_range_str):
    """
    Parse a page range string into a list of page numbers.

    Format examples:
    - "1-5,7,9-12" -> [1, 2, 3, 4, 5, 7, 9, 10, 11, 12]
    - "1,3,5" -> [1, 3, 5]
    - "" -> [] (empty list means all pages)

    Returns:
        list: List of page numbers, or empty list for all pages
    """
    if not page_range_str or page_range_str.strip() == "":
        return []

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            # Range like "1-5"
            try:
                start, end = part.split('-')
                start, end = int(start.strip()), int(end.strip())
                if start <= end:
                    pages.extend(range(start, end + 1))
            except ValueError:
                # Skip invalid ranges
                continue
        else:
            # Single page like "7"
            try:
                pages.append(int(part))
            except ValueError:
                # Skip invalid pages
                continue

    return sorted(list(set(pages)))  # Remove duplicates and sort{{ ... }}

def getSimpleFieldMap():
    import sys
    sys.path[0] = ""
    from src.app_paths import getSavedFieldMapJson
    fieldMap = getSavedFieldMapJson()
    simpleFieldMap = {}
    for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
        subData = fieldMap.get(key, {})
        # Discard unuseful data
        for _, v in subData.items():
            try:
                del v["options"]
            except Exception as e:
                pass
        simpleFieldMap.update(subData)
    return simpleFieldMap

class PandasModel(QAbstractTableModel):
    """
    A model for displaying pandas DataFrames in a QTableView.
    Optimized for large datasets.
    """
    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else pd.DataFrame()

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._data.columns)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            value = self._data.iloc[index.row(), index.column()]
            # Handle different data types appropriately
            try:
                # Try pandas isna first which handles various NA types
                if pd.isna(value):
                    return ""
                # Handle numpy arrays and other array-like objects
                elif hasattr(value, 'dtype') and hasattr(value, 'shape'):
                    if hasattr(value, 'tolist'):
                        return str(value.tolist())
                    return str(value)
                elif isinstance(value, (int, float, complex)):
                    return str(value)
                elif isinstance(value, (list, tuple)):
                    return str(value)
                else:
                    return str(value)
            except (TypeError, ValueError):
                # Fallback for any values that cause issues
                return str(value)

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._data.columns[section])
            else:
                return str(section + 1)
        return None

    def setData(self, data):
        self.beginResetModel()
        self._data = data if data is not None else pd.DataFrame()
        self.endResetModel()

class ColoredPandasModel(PandasModel):
    """
    An extension of PandasModel that supports cell background colors based on row values.
    """
    def __init__(self, data=None, color_column=None, color_map=None):
        super().__init__(data)
        self.color_column = color_column
        self.color_map = color_map or {}

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.BackgroundRole and self.color_column is not None:
            row = index.row()
            if row < len(self._data):
                value = self._data.iloc[row][self.color_column]
                if value in self.color_map:
                    return QBrush(QColor(self.color_map[value]))

        return super().data(index, role)

    def setColorColumn(self, column):
        self.color_column = column
        self.layoutChanged.emit()

    def setColorMap(self, color_map):
        self.color_map = color_map
        self.layoutChanged.emit()

class DrawingsTableModel(QAbstractTableModel):
    """
    A model for displaying PDF drawings in a QTableView.
    """
    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else []
        self._headers = ["ID", "Type", "Color", "Fill", "Line Width", "bbox", "Area", "Length", "Page"]

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            drawing = self._data[index.row()]
            col = index.column()

            if col == 0:  # ID
                return str(index.row() + 1)
            elif col == 1:  # Type
                return self._get_drawing_type(drawing['drawing'])
            elif col == 2:  # Color
                return self._get_drawing_color(drawing['drawing'])
            elif col == 3:  # Fill
                return "Yes" if self._has_fill(drawing['drawing']) else "No"
            elif col == 4:  # Line Width
                return self._get_line_width(drawing['drawing'])
            elif col == 5:  # bbox
                return self._get_bounds(drawing['drawing'])
            elif col == 6:  # Area
                return self._calculate_area(drawing['drawing'])
            elif col == 7:  # Length
                return self._calculate_length(drawing['drawing'])
            elif col == 8:  # Page
                return str(drawing['page'] + 1)  # 1-based page number for display

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return self._headers[section]
            else:
                return str(section + 1)
        return None

    def setData(self, data):
        self.beginResetModel()
        self._data = data if data is not None else []
        self.endResetModel()

    def _get_drawing_type(self, drawing):
        """Get the type of drawing (line, rectangle, etc.)"""
        if 'type' in drawing:
            return drawing['type']

        # Try to determine type from items
        items = drawing.get('items', [])
        if not items:
            return "Unknown"

        # Check first item's type
        item = items[0]
        if item.get('type') == 'l':
            return "Line"
        elif item.get('type') == 're':
            return "Rectangle"
        elif item.get('type') == 'qu':
            return "Quad"
        elif item.get('type') == 'c':
            return "Curve"
        else:
            return item.get('type', "Unknown")

    def _get_drawing_color(self, drawing):
        """Get the color of the drawing as a hex string"""
        # Try to get stroke color
        stroke_color = drawing.get('stroke_color')
        if stroke_color:
            print(f"Found stroke_color: {stroke_color}")
            if isinstance(stroke_color, (list, tuple)) and len(stroke_color) >= 3:
                r, g, b = stroke_color[:3]
                # Convert from 0-1 range to 0-255 range
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(stroke_color)

        # Try to get color from items
        items = drawing.get('items', [])
        if items:
            print(f"Checking items: {len(items)} items")

            # Check all items for any color-related attributes
            for item in items:
                if not isinstance(item, dict):
                    continue

                # Check for 'color' attribute
                if 'color' in item:
                    color = item['color']
                    print(f"Found color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

                # Check for 'colors' attribute
                if 'colors' in item:
                    colors = item['colors']
                    print(f"Found colors in item: {colors}")
                    if isinstance(colors, (list, tuple)) and len(colors) > 0:
                        color = colors[0]
                        if isinstance(color, (list, tuple)) and len(color) >= 3:
                            r, g, b = color[:3]
                            r = min(255, max(0, int(r * 255)))
                            g = min(255, max(0, int(g * 255)))
                            b = min(255, max(0, int(b * 255)))
                            return f"#{r:02x}{g:02x}{b:02x}"
                        return str(colors[0])

                # Check for 'stroke_color' attribute
                if 'stroke_color' in item:
                    color = item['stroke_color']
                    print(f"Found stroke_color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

                # Check for 'fill_color' attribute
                if 'fill_color' in item:
                    color = item['fill_color']
                    print(f"Found fill_color in item: {color}")
                    if isinstance(color, (list, tuple)) and len(color) >= 3:
                        r, g, b = color[:3]
                        r = min(255, max(0, int(r * 255)))
                        g = min(255, max(0, int(g * 255)))
                        b = min(255, max(0, int(b * 255)))
                        return f"#{r:02x}{g:02x}{b:02x}"
                    return str(color)

        # Check for fill color as a fallback
        fill_color = drawing.get('fill_color')
        if fill_color:
            print(f"Found fill_color: {fill_color}")
            if isinstance(fill_color, (list, tuple)) and len(fill_color) >= 3:
                r, g, b = fill_color[:3]
                # Convert from 0-1 range to 0-255 range
                r = min(255, max(0, int(r * 255)))
                g = min(255, max(0, int(g * 255)))
                b = min(255, max(0, int(b * 255)))
                return f"#{r:02x}{g:02x}{b:02x}"
            return str(fill_color)

        # Check for any other color-related attributes
        for key in drawing.keys():
            if 'color' in key.lower():
                color_value = drawing[key]
                print(f"Found color in key {key}: {color_value}")
                if isinstance(color_value, (list, tuple)) and len(color_value) >= 3:
                    r, g, b = color_value[:3]
                    r = min(255, max(0, int(r * 255)))
                    g = min(255, max(0, int(g * 255)))
                    b = min(255, max(0, int(b * 255)))
                    return f"#{r:02x}{g:02x}{b:02x}"
                return str(color_value)

        # If we still don't have a color, use a default based on drawing type
        drawing_type = self._get_drawing_type(drawing)
        if drawing_type == "Line":
            return "#000000"  # Black for lines
        elif drawing_type == "Rectangle":
            return "#0000FF"  # Blue for rectangles
        elif drawing_type == "Curve":
            return "#FF0000"  # Red for curves
        elif drawing_type == "Quad":
            return "#00FF00"  # Green for quads

        print(f"No color found in drawing: {drawing.keys()}")
        return "Unknown"

    def _has_fill(self, drawing):
        """Check if the drawing has a fill color"""
        # Check for fill color
        if drawing.get('fill_color'):
            return True

        # Check for fill in items
        items = drawing.get('items', [])
        for item in items:
            # Check if item is a dictionary before using get()
            if isinstance(item, dict) and item.get('fill'):
                return True

        return False

    def _get_line_width(self, drawing):
        """Get the line width of the drawing"""
        # Check for width
        width = drawing.get('width')
        if width is not None:
            return f"{width:.2f}"

        # Check for width in items
        items = drawing.get('items', [])
        if items and 'width' in items[0]:
            return f"{items[0]['width']:.2f}"

        return "1.00"  # Default width

    def _get_bounds(self, drawing):
        """Get the bounding box coordinates of the drawing"""
        rect = drawing.get('rect')
        if rect and len(rect) == 4:
            x0, y0, x1, y1 = rect
            return f"({x0:.1f},{y0:.1f},{x1:.1f},{y1:.1f})"

        # Try to get bounds from items
        items = drawing.get('items', [])
        if items:
            # For lines, use start and end points
            if self._get_drawing_type(drawing) == "Line" and len(items) >= 1:
                item = items[0]
                if isinstance(item, dict) and 'start' in item and 'end' in item:
                    x0, y0 = item['start']
                    x1, y1 = item['end']
                    return f"({x0:.1f},{y0:.1f},{x1:.1f},{y1:.1f})"

        return "N/A"

    def _calculate_area(self, drawing):
        """Calculate the approximate area of the drawing"""
        rect = drawing.get('rect')
        if rect and len(rect) == 4:
            width = abs(rect[2] - rect[0])
            height = abs(rect[3] - rect[1])
            return f"{width * height:.2f}"

        return "N/A"

    def _calculate_length(self, drawing):
        """Calculate the approximate length/perimeter of the drawing"""
        items = drawing.get('items', [])
        if not items:
            return "N/A"

        # For lines, calculate length
        if self._get_drawing_type(drawing) == "Line" and len(items) >= 1:
            item = items[0]
            if 'start' in item and 'end' in item:
                x1, y1 = item['start']
                x2, y2 = item['end']

                # Calculate length
                length = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                return f"{length:.2f}"

        # For rectangles, calculate perimeter
        if self._get_drawing_type(drawing) == "Rectangle" and 'rect' in drawing:
            rect = drawing['rect']
            width = abs(rect[2] - rect[0])
            height = abs(rect[3] - rect[1])
            return f"{2 * (width + height):.2f}"

        return "N/A"


class PDFGraphicsView(QGraphicsView):
    """
    A custom QGraphicsView for displaying PDF pages with zoom and scroll functionality.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setScene(QGraphicsScene(self))
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setDragMode(QGraphicsView.ScrollHandDrag)

        # Set background color
        self.setBackgroundBrush(QBrush(QColor(240, 240, 240)))

        # Initialize variables
        self.currentPage = None
        self.pdfDocument = None
        self.currentProjectId = None
        self.currentFilename = None
        self.zoomFactor = 1.0
        self.pixmapItem = None
        self.maintainPosition = False

    def wheelEvent(self, event):
        """Handle zoom in/out with mouse wheel"""
        if event.modifiers() & Qt.ControlModifier:
            # Zoom factor
            zoomInFactor = 1.25
            zoomOutFactor = 1 / zoomInFactor

            # Save the scene pos
            oldPos = self.mapToScene(event.position().toPoint())

            # Zoom
            if event.angleDelta().y() > 0:
                zoomFactor = zoomInFactor
            else:
                zoomFactor = zoomOutFactor
            self.scale(zoomFactor, zoomFactor)
            self.zoomFactor *= zoomFactor

            # Get the new position
            newPos = self.mapToScene(event.position().toPoint())

            # Move scene to old position
            delta = newPos - oldPos
            self.translate(delta.x(), delta.y())
        else:
            super().wheelEvent(event)

    def resetZoom(self):
        """Reset zoom to original size"""
        self.resetTransform()
        self.zoomFactor = 1.0

    def zoomIn(self):
        """Zoom in by 25%"""
        self.scale(1.25, 1.25)
        self.zoomFactor *= 1.25

    def zoomOut(self):
        """Zoom out by 20%"""
        self.scale(0.8, 0.8)
        self.zoomFactor *= 0.8

    def fitInView(self):
        """Fit the current page in view"""
        if self.pixmapItem:
            self.resetTransform()
            self.zoomFactor = 1.0
            super().fitInView(self.pixmapItem, Qt.KeepAspectRatio)
            # Update zoom factor based on the transformation
            self.zoomFactor = self.transform().m11()  # Get the horizontal scale factor

    def loadPDF(self, projectId, filename):
        """Load the PDF document"""
        self.currentProjectId = projectId
        self.currentFilename = filename

        try:
            # Load the PDF directly using the filename as the path
            self.pdfDocument = fitz.open(filename)
            print(f"Successfully loaded PDF: {filename}")
            return True
        except Exception as e:
            print(f"Error loading PDF: {e}")

        self.pdfDocument = None
        return False

    def displayPage(self, page_number):
        """Display the specified page number"""
        if not self.pdfDocument or page_number < 0 or page_number >= len(self.pdfDocument):
            return False

        try:
            # Get the page
            page = self.pdfDocument[page_number]

            # Render page to pixmap
            # Higher zoom factor for better quality
            matrix = fitz.Matrix(2, 2)
            pixmap = page.get_pixmap(matrix=matrix)

            # Convert pixmap to QImage
            img = QImage(pixmap.samples, pixmap.width, pixmap.height,
                         pixmap.stride, QImage.Format_RGB888)

            # Convert QImage to QPixmap
            pixmap = QPixmap.fromImage(img)

            # Clear previous content
            self.scene().clear()

            # Add pixmap to scene
            self.pixmapItem = self.scene().addPixmap(pixmap)

            # Fit the page in view
            self.fitInView()

            self.currentPage = page_number
            return True
        except Exception as e:
            print(f"Error displaying page {page_number}: {e}")
            return False


class ImageLoaderWorker(QRunnable):
    """Worker class for loading images in a background thread."""

    def __init__(self, image_path, size, callback):
        super().__init__()
        self.image_path = image_path
        self.size = size
        self.callback = callback
        # Store callback as instance attribute to prevent garbage collection
        self.callback_obj = callback

    def run(self):
        """Load the image and call the callback with the result."""
        try:
            pixmap = QPixmap(self.image_path)

            if not pixmap.isNull():
                # Scale the image
                pixmap = pixmap.scaled(self.size, self.size, Qt.KeepAspectRatio, Qt.SmoothTransformation)

                # Call the callback with the loaded pixmap
                # Use QMetaObject.invokeMethod to safely call the callback from the main thread
                QMetaObject.invokeMethod(self.callback_obj, "on_image_loaded",
                                        Qt.QueuedConnection,
                                        Q_ARG(str, self.image_path),
                                        Q_ARG(int, self.size),
                                        Q_ARG(QPixmap, pixmap))
            else:
                QMetaObject.invokeMethod(self.callback_obj, "on_image_loaded",
                                        Qt.QueuedConnection,
                                        Q_ARG(str, self.image_path),
                                        Q_ARG(int, self.size),
                                        Q_ARG(QPixmap, QPixmap()))
        except Exception as e:
            print(f"Error loading image {self.image_path}: {str(e)}")
            QMetaObject.invokeMethod(self.callback_obj, "on_image_loaded",
                                    Qt.QueuedConnection,
                                    Q_ARG(str, self.image_path),
                                    Q_ARG(int, self.size),
                                    Q_ARG(QPixmap, QPixmap()))

class ImageCache(QObject):
    """A cache for storing image thumbnails to avoid reloading from disk."""

    def __init__(self, max_size=100):
        super().__init__()
        self.cache = {}
        self.max_size = max_size
        self.thread_pool = QThreadPool.globalInstance()
        self.thread_pool.setMaxThreadCount(4)  # Limit to 4 threads

    def get(self, image_path, size, callback_obj):
        """Get an image from the cache or load it asynchronously."""
        cache_key = (image_path, size)

        # Check if image is in cache
        if cache_key in self.cache:
            pixmap = self.cache[cache_key]
            if pixmap and not pixmap.isNull():
                callback_obj.on_image_loaded(image_path, size, pixmap)
                return True

        # Load image asynchronously
        worker = ImageLoaderWorker(image_path, size, callback_obj)
        self.thread_pool.start(worker)
        return False

    @Slot(str, int, QPixmap)
    def on_image_loaded(self, image_path, size, pixmap):
        """Callback for when an image is loaded."""
        if not pixmap.isNull():
            # Add to cache
            cache_key = (image_path, size)
            self.cache[cache_key] = pixmap

            # Clean cache if too large
            if len(self.cache) > self.max_size:
                # Remove oldest items
                keys_to_remove = list(self.cache.keys())[:(len(self.cache) - self.max_size)]
                for key in keys_to_remove:
                    del self.cache[key]

    def clear(self):
        """Clear the cache."""
        self.cache.clear()

# Global image cache instance
_image_cache = ImageCache(max_size=200)

class ZoomableImageViewer(QGraphicsView):
    """A custom QGraphicsView that displays a zoomable image"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setScene(QGraphicsScene(self))
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setBackgroundBrush(QBrush(QColor(30, 30, 30)))
        self.setFrameShape(QFrame.Box)
        self.setDragMode(QGraphicsView.ScrollHandDrag)

        # Image item
        self._pixmap_item = None
        self._zoom = 0
        self._empty = True
        self._current_image_path = None
        self._table_name = None

        # Enable mouse tracking for detailed tooltip info
        self.setMouseTracking(True)

        # Info text item
        self._text_item = QGraphicsTextItem()
        self._text_item.setDefaultTextColor(Qt.white)
        self._text_item.setHtml("<center>No image</center>")
        self._text_item.setTextWidth(300)
        self.scene().addItem(self._text_item)

    def hasImage(self):
        return not self._empty

    def fitInView(self, scale=True):
        if not self._pixmap_item:
            return

        rect = QRectF(self._pixmap_item.pixmap().rect())
        if not rect.isNull():
            self.setSceneRect(rect)
            if self.hasImage():
                unity = self.transform().mapRect(QRectF(0, 0, 1, 1))
                # Check for zero width or height to avoid division by zero
                if unity.width() > 0 and unity.height() > 0:
                    self.scale(1 / unity.width(), 1 / unity.height())
                    viewrect = self.viewport().rect()
                    scenerect = self.transform().mapRect(rect)
                    # Check for zero width or height to avoid division by zero
                    if scenerect.width() > 0 and scenerect.height() > 0:
                        factor = min(viewrect.width() / scenerect.width(),
                                     viewrect.height() / scenerect.height())
                        self.scale(factor, factor)
            self._zoom = 0

    def setImage(self, pixmap=None, image_path=None, table_name=None, reset_zoom=False):
        """Set the image to display

        Args:
            pixmap: The QPixmap to display
            image_path: Path to the image file (for tracking)
            table_name: Name of the table this image belongs to
            reset_zoom: Whether to reset zoom level
        """
        # Skip if it's the same image
        if image_path and image_path == self._current_image_path:
            return

        # Store image path and table name
        self._current_image_path = image_path

        # Check if we're changing tables
        changing_table = table_name != self._table_name
        self._table_name = table_name

        if pixmap and not pixmap.isNull():
            self._empty = False
            self.setDragMode(QGraphicsView.ScrollHandDrag)

            # Remove old pixmap
            if self._pixmap_item is not None:
                self.scene().removeItem(self._pixmap_item)

            # Add new pixmap
            self._pixmap_item = QGraphicsPixmapItem(pixmap)
            self._pixmap_item.setTransformationMode(Qt.SmoothTransformation)
            self.scene().addItem(self._pixmap_item)

            # Hide text item
            self._text_item.setVisible(False)

            # Reset zoom if requested or changing tables
            if reset_zoom or changing_table or table_name == "general":
                self.fitInView()
        else:
            self._empty = True
            self.setDragMode(QGraphicsView.NoDrag)

            # Show text item
            self._text_item.setVisible(True)
            self._text_item.setPos(self.sceneRect().center() - self._text_item.boundingRect().center())

    def setText(self, text):
        self._empty = True
        self.setDragMode(QGraphicsView.NoDrag)
        self._text_item.setHtml(f"<center>{text}</center>")
        self._text_item.setVisible(True)

        # Center text
        self.scene().setSceneRect(0, 0, self.viewport().width(), self.viewport().height())
        self._text_item.setPos(self.sceneRect().center() - self._text_item.boundingRect().center())

        # Remove pixmap if exists
        if self._pixmap_item is not None:
            self.scene().removeItem(self._pixmap_item)
            self._pixmap_item = None

    def wheelEvent(self, event):
        if self.hasImage():
            if event.angleDelta().y() > 0:
                factor = 1.25
                self._zoom += 1
            else:
                factor = 0.8
                self._zoom -= 1

            if self._zoom > 0:
                self.scale(factor, factor)
            elif self._zoom == 0:
                self.fitInView()
            else:
                self._zoom = 0
                self.fitInView()

    def resizeEvent(self, event):
        if self.hasImage():
            # Only fit in view if we're at zoom level 0
            if self._zoom == 0:
                self.fitInView()
        else:
            # Center text
            self.scene().setSceneRect(0, 0, self.viewport().width(), self.viewport().height())
            self._text_item.setPos(self.sceneRect().center() - self._text_item.boundingRect().center())
        super().resizeEvent(event)

    def getZoomLevel(self):
        """Get the current zoom level"""
        return self._zoom

    def setZoomLevel(self, level):
        """Set zoom to a specific level"""
        if not self.hasImage():
            return

        # Reset to fit
        self.fitInView()

        # Apply zoom if needed
        if level > 0:
            self._zoom = level
            factor = pow(1.25, level)
            self.scale(factor, factor)

class EditableTableView(QTableView):
    """A custom table view with Excel-like keyboard functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        self.setSelectionBehavior(QAbstractItemView.SelectItems)  # Select cells instead of rows
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)  # Allow selecting multiple cells
        self.setContextMenuPolicy(Qt.CustomContextMenu)

        # Enable editing
        self.setEditTriggers(QAbstractItemView.DoubleClicked |
                           QAbstractItemView.EditKeyPressed |
                           QAbstractItemView.AnyKeyPressed)

        # Track if we're in edit mode
        self.editing = False

    def keyPressEvent(self, event):
        """Handle key press events for Excel-like navigation and editing"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # Enter key behavior depends on whether we're editing
            if self.state() == QAbstractItemView.EditingState:
                # If editing, commit the edit and move to the cell below
                self.editing = False
                self.commitData(self.sender())
                # In PySide6, use NoEditTriggers instead of EditNextItem
                self.closeEditor(self.sender(), QAbstractItemView.NoEditTriggers)
                # Move to the cell below manually
                current = self.currentIndex()
                next_row = min(current.row() + 1, self.model().rowCount() - 1)
                self.setCurrentIndex(self.model().index(next_row, current.column()))
            else:
                # If not editing, start editing the current cell
                self.edit(self.currentIndex())
                self.editing = True
            return

        elif event.key() == Qt.Key_Tab:
            # Tab key moves to the next cell (right)
            current = self.currentIndex()
            if current.column() < self.model().columnCount() - 1:
                self.setCurrentIndex(self.model().index(current.row(), current.column() + 1))
            else:
                # If at the last column, move to the first column of the next row
                if current.row() < self.model().rowCount() - 1:
                    self.setCurrentIndex(self.model().index(current.row() + 1, 0))
            return

        elif event.key() == Qt.Key_Backtab:
            # Shift+Tab moves to the previous cell (left)
            current = self.currentIndex()
            if current.column() > 0:
                self.setCurrentIndex(self.model().index(current.row(), current.column() - 1))
            else:
                # If at the first column, move to the last column of the previous row
                if current.row() > 0:
                    self.setCurrentIndex(self.model().index(current.row() - 1, self.model().columnCount() - 1))
            return

        elif event.key() == Qt.Key_Escape:
            # Escape key cancels editing
            if self.state() == QAbstractItemView.EditingState:
                self.editing = False
                self.closeEditor(self.sender(), QAbstractItemView.NoHint)
            return

        elif event.key() == Qt.Key_C and event.modifiers() & Qt.ControlModifier:
            # Ctrl+C copies selected cells to clipboard
            self.copySelection()
            return

        elif event.key() == Qt.Key_V and event.modifiers() & Qt.ControlModifier:
            # Ctrl+V pastes clipboard content to selected cell
            self.pasteFromClipboard()
            return

        elif event.key() == Qt.Key_Delete or event.key() == Qt.Key_Backspace:
            # Delete or backspace clears the selected cells
            self.clearSelectedCells()
            return

        # For all other keys, use the default behavior
        super().keyPressEvent(event)

    def copySelection(self):
        """Copy selected cells to clipboard in a tab-separated format"""
        selection = self.selectionModel().selection()
        if not selection:
            return

        # Get all selected indexes
        indexes = []
        for range_idx in selection:
            for row in range(range_idx.top(), range_idx.bottom() + 1):
                for col in range(range_idx.left(), range_idx.right() + 1):
                    indexes.append(self.model().index(row, col))

        # Sort indexes by row, then column
        indexes.sort(key=lambda idx: (idx.row(), idx.column()))

        # Group indexes by row
        rows = {}
        for idx in indexes:
            if idx.row() not in rows:
                rows[idx.row()] = []
            rows[idx.row()].append(idx)

        # Build tab-separated text
        text = ""
        for row in sorted(rows.keys()):
            row_text = ""
            for idx in sorted(rows[row], key=lambda idx: idx.column()):
                if row_text:
                    row_text += "\t"
                row_text += str(self.model().data(idx, Qt.DisplayRole) or "")
            text += row_text + "\n"

        # Copy to clipboard
        QApplication.clipboard().setText(text)

    def pasteFromClipboard(self):
        """Paste clipboard content to the table starting from the current cell"""
        text = QApplication.clipboard().text()
        if not text:
            return

        # Get current index
        current = self.currentIndex()
        if not current.isValid():
            return

        # Split text into rows and columns
        rows = text.split("\n")
        if not rows:
            return

        # Paste data
        model = self.model()
        start_row = current.row()
        start_col = current.column()

        for i, row_text in enumerate(rows):
            if not row_text:
                continue

            columns = row_text.split("\t")
            for j, cell_text in enumerate(columns):
                row = start_row + i
                col = start_col + j

                if row < model.rowCount() and col < model.columnCount():
                    model.setData(model.index(row, col), cell_text, Qt.EditRole)

    def clearSelectedCells(self):
        """Clear the content of selected cells"""
        selection = self.selectionModel().selection()
        if not selection:
            return

        # Get all selected indexes
        indexes = []
        for range_idx in selection:
            for row in range(range_idx.top(), range_idx.bottom() + 1):
                for col in range(range_idx.left(), range_idx.right() + 1):
                    indexes.append(self.model().index(row, col))

        # Clear each cell
        model = self.model()
        for idx in indexes:
            model.setData(idx, "", Qt.EditRole)

class OcrExtractionDialog(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.db = DatabaseManager()
        self.currentProjectId = None
        self.currentFilename = None
        self.projectSourcesDf = None
        self.availablePages = None
        self.currentPage = None
        self.currentDataFrame = None

        self.imageDisplays = {}

        self.setObjectName("popup")
        self.setWindowTitle("OCR Extraction")
        self.setLayout(QVBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setMinimumWidth(900)
        self.setMinimumHeight(600)

        # Create toolbar
        self.setupToolbar()

        # Create options toolbar
        self.setupToolbarOptions()

        # Create filter toolbar
        self.setupExtractionToolbar()

        # Create tabbed interface
        self.setupTabbedInterface()

        # Initialize data
        self.refreshProjectSources()

    def setupToolbar(self):
        """Set up the horizontal toolbar with controls"""
        self.toolbar = QToolBar()
        self.toolbar.setMovable(False)
        self.toolbar.setFloatable(False)
        self.toolbar.setIconSize(QSize(16, 16))

        # Add project source selection
        self.lblSource = QLabel("Project Source:")
        self.toolbar.addWidget(self.lblSource)

        self.cboxSource = QComboBox()
        self.cboxSource.setMinimumWidth(300)
        self.cboxSource.setEditable(True)
        self.cboxSource.currentIndexChanged.connect(self.onSourceSelected)
        self.toolbar.addWidget(self.cboxSource)

        # Add load button
        self.pbLoad = QPushButton("Open Source Project")
        self.pbLoad.clicked.connect(self.loadData)
        self.toolbar.addWidget(self.pbLoad)

        # Add refresh button
        self.pbRefresh = QPushButton("Refresh Sources")
        self.pbRefresh.clicked.connect(self.refreshProjectSources)
        self.toolbar.addWidget(self.pbRefresh)

        # Add generate report button
        # self.pbGenerateReport = QPushButton("Generate Report")
        # self.pbGenerateReport.clicked.connect(self.onGenerateReport)
        # self.toolbar.addWidget(self.pbGenerateReport)

        # Add separator
        self.toolbar.addSeparator()

        # Add OCR extraction mode selection
        self.lblExtractionMode = QLabel("Extraction Mode:")
        self.toolbar.addWidget(self.lblExtractionMode)

        self.cboxExtractionMode = QComboBox()
        self.cboxExtractionMode.addItems(OCR_OPTIONS)
        self.cboxExtractionMode.setCurrentIndex(0)  # Default to first option (winrt)
        self.toolbar.addWidget(self.cboxExtractionMode)

        # Add separator
        self.toolbar.addSeparator()

        # Add position anchoring toggle button
        # self.cbAnchorPosition = QCheckBox("Maintain Position")
        # self.cbAnchorPosition.setToolTip("Maintain zoom level and scroll position when changing pages")
        # self.cbAnchorPosition.setChecked(False)
        # self.cbAnchorPosition.stateChanged.connect(self.onAnchorPositionChanged)
        # self.toolbar.addWidget(self.cbAnchorPosition)

        # Add toolbar to layout
        self.layout().addWidget(self.toolbar)

    def setupExtractionToolbar(self):
        """Set up the extraction toolbar with page navigation controls"""
        extractionToolbarWidget = QWidget()
        extractionToolbarLayout = QHBoxLayout(extractionToolbarWidget)
        extractionToolbarLayout.setContentsMargins(10, 5, 10, 5)

        # Output Images Path
        self.lblOutputImages = QLabel("OCR Folder:")
        extractionToolbarLayout.addWidget(self.lblOutputImages)

        self.txtOutputImages = QLineEdit()
        self.txtOutputImages.setReadOnly(True)
        self.txtOutputImages.setPlaceholderText("Path to source OCR folder...")
        self.txtOutputImages.setMinimumWidth(300)
        extractionToolbarLayout.addWidget(self.txtOutputImages)

        self.btnOpenOutputLocation = QPushButton("Open Location")
        self.btnOpenOutputLocation.clicked.connect(self.onOpenOutputLocation)
        extractionToolbarLayout.addWidget(self.btnOpenOutputLocation)

        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        extractionToolbarLayout.addWidget(separator)

        # Extraction Type Checkboxes
        self.chkExtractBom = QCheckBox("BOM")
        self.chkExtractBom.setChecked(True)
        extractionToolbarLayout.addWidget(self.chkExtractBom)

        self.chkExtractGeneral = QCheckBox("General")
        self.chkExtractGeneral.setChecked(True)
        extractionToolbarLayout.addWidget(self.chkExtractGeneral)

        self.chkExtractSpool = QCheckBox("Spool")
        extractionToolbarLayout.addWidget(self.chkExtractSpool)

        self.chkExtractSpec = QCheckBox("Spec")
        extractionToolbarLayout.addWidget(self.chkExtractSpec)

        self.chkExtractIfc = QCheckBox("IFC")
        extractionToolbarLayout.addWidget(self.chkExtractIfc)

        self.chkGeneric1 = QCheckBox("Generic 1")
        extractionToolbarLayout.addWidget(self.chkGeneric1)

        self.chkGeneric2 = QCheckBox("Generic 2")
        extractionToolbarLayout.addWidget(self.chkGeneric2)

        self.chkIso = QCheckBox("ISO (experimental)")
        extractionToolbarLayout.addWidget(self.chkIso)

        extractionToolbarLayout.addStretch(1)

        # Navigation buttons
        self.pbPrevPage = QPushButton("◀ Previous")
        self.pbPrevPage.clicked.connect(self.onPreviousPage)
        self.pbPrevPage.setEnabled(False)

        self.pbNextPage = QPushButton("Next ▶")
        self.pbNextPage.clicked.connect(self.onNextPage)
        self.pbNextPage.setEnabled(False)

        # # Text filter
        # self.lblFilter = QLabel("Filter:")
        # self.txtFilter = QLineEdit()
        # self.txtFilter.setPlaceholderText("Enter text to filter...")
        # self.txtFilter.setEnabled(False)
        # self.txtFilter.textChanged.connect(self.applyFilters)
        # self.txtFilter.setClearButtonEnabled(True)

        # Detect empty cells button
        self.pbDetectBlank = QPushButton("Detect General Empty Cells")
        self.pbDetectBlank.clicked.connect(self.detectGeneralEmptyCells)
        self.pbDetectBlank.setEnabled(False)

        # Page range input
        pageRangeLabel = QLabel("Page Range:")
        self.txtPageRange = QLineEdit()
        self.txtPageRange.setPlaceholderText("e.g., 1-5,7,9-12 (empty for all)")
        self.txtPageRange.setToolTip("Specify page ranges to extract. Format: 1-5,7,9-12. Leave empty to extract all pages.")
        self.txtPageRange.setFixedWidth(150)

        # Reset button
        self.pbExtractImages = QPushButton("Extract Images")
        self.pbExtractImages.clicked.connect(self.runExtractImages)
        self.pbExtractImages.setEnabled(False)


        self.chkLimitWidth = QCheckBox("Max Cropped Image Width (px)")
        self.chkLimitWidth.setChecked(True)
        extractionToolbarLayout.addWidget(self.chkLimitWidth)
        self.spinMaxWidth = QSpinBox()
        self.spinMaxWidth.setRange(1, 6000)
        self.spinMaxWidth.setValue(4000)
        extractionToolbarLayout.addWidget(self.spinMaxWidth)

        extractionToolbarLayout.addWidget(QLabel("DPI:"))
        self.spinDpi = QSpinBox()
        self.spinDpi.setRange(1, 1000)
        self.spinDpi.setValue(400)

        self.pbExtract = QPushButton("Extract")
        self.pbExtract.clicked.connect(self.runExtraction)
        self.pbExtract.setEnabled(False)

        # Add widgets to toolbar
        extractionToolbarLayout.addWidget(self.spinDpi)
        extractionToolbarLayout.addWidget(self.pbExtractImages)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        extractionToolbarLayout.addWidget(separator)
        # extractionToolbarLayout.addStretch(16)
        extractionToolbarLayout.addWidget(self.pbDetectBlank)
        extractionToolbarLayout.addWidget(pageRangeLabel)
        extractionToolbarLayout.addWidget(self.txtPageRange)
        extractionToolbarLayout.addWidget(self.pbExtract)

        self.layout().addWidget(extractionToolbarWidget)

    def setupToolbarOptions(self):
        """Set up the options toolbar with ROI payload selection"""
        optionsToolbarWidget = QWidget()
        optionsToolbarLayout = QHBoxLayout(optionsToolbarWidget)
        optionsToolbarLayout.setContentsMargins(10, 5, 10, 5)

        # ROI Payload selection
        self.lblRoiPayload = QLabel("ROI Payload:")
        optionsToolbarLayout.addWidget(self.lblRoiPayload)

        # Radio buttons for payload selection
        self.radioInternalPayload = QRadioButton("Internal")
        self.radioInternalPayload.setChecked(True)
        self.radioInternalPayload.toggled.connect(self.onPayloadTypeToggled)
        optionsToolbarLayout.addWidget(self.radioInternalPayload)

        self.radioCustomPayload = QRadioButton("Custom")
        self.radioCustomPayload.toggled.connect(self.onPayloadTypeToggled)
        optionsToolbarLayout.addWidget(self.radioCustomPayload)

        # Custom payload path
        self.txtRoiPayload = QLineEdit()
        self.txtRoiPayload.setPlaceholderText("Path to custom ROI payload JSON...")
        self.txtRoiPayload.setMinimumWidth(200)
        self.txtRoiPayload.setEnabled(False)  # Disabled by default (using internal)
        optionsToolbarLayout.addWidget(self.txtRoiPayload)

        # Browse button
        self.btnBrowseRoiPayload = QPushButton("Browse...")
        self.btnBrowseRoiPayload.clicked.connect(self.onBrowseRoiPayload)
        self.btnBrowseRoiPayload.setEnabled(False)  # Disabled by default (using internal)
        optionsToolbarLayout.addWidget(self.btnBrowseRoiPayload)

        optionsToolbarLayout.addStretch(1)

        self.layout().addWidget(optionsToolbarWidget)

    def setupTabbedInterface(self):
        """Set up the tabbed interface with table and PDF viewer"""
        self.tabWidget = QTabWidget()

        # Table tab
        self.imagesTab = QWidget()
        self.imagesTab.setLayout(QVBoxLayout())
        self.imagesTab.layout().setContentsMargins(0, 0, 0, 0)

        self.setupImagesView()
        # self.imagesTab.layout().addWidget(self.imagesView)

        # OpenAI tab
        self.openAiTab = QWidget()
        self.openAiTab.setLayout(QVBoxLayout())
        self.openAiTab.layout().setContentsMargins(0, 0, 0, 0)

        self.setupOpenAiTab()

        # PDF Viewer tab
        # self.pdfTab = QWidget()
        # self.pdfTab.setLayout(QVBoxLayout())
        # self.pdfTab.layout().setContentsMargins(0, 0, 0, 0)

        # self.setupPdfViewer()
        # self.pdfTab.layout().addWidget(self.pdfSplitter)

        # Add tabs to tab widget
        self.tabWidget.addTab(self.imagesTab, "Images")
        self.tabWidget.addTab(self.openAiTab, "OpenAI Configuration")
        # self.tabWidget.addTab(self.pdfTab, "PDF Viewer")

        # Connect tab changed signal
        self.tabWidget.currentChanged.connect(self.onTabChanged)

        # Add tab widget to layout
        self.layout().addWidget(self.tabWidget)

    def setupImagesView(self):
        """Set up the Images tab with folder list, image list, and image preview"""
        # Create a splitter for the images view
        self.imagesSplitter = QSplitter(Qt.Horizontal)
        self.imagesTab.layout().addWidget(self.imagesSplitter)

        # Left panel - folder list
        self.imageFolderPanel = QWidget()
        self.imageFolderPanel.setLayout(QVBoxLayout())
        self.imageFolderPanel.layout().setContentsMargins(10, 10, 10, 10)

        # Folder list label
        self.imageFolderPanel.layout().addWidget(QLabel("Folders:"))

        # Folder list
        self.imageFolderList = QListWidget()
        self.imageFolderList.setMinimumWidth(200)
        self.imageFolderList.currentItemChanged.connect(lambda item: self.onFolderSelected(item) if item else None)
        self.imageFolderPanel.layout().addWidget(self.imageFolderList)

        # Refresh button
        refreshButton = QPushButton("Refresh Folders")
        refreshButton.clicked.connect(self.refreshImages)
        self.imageFolderPanel.layout().addWidget(refreshButton)

        # Add to splitter
        self.imagesSplitter.addWidget(self.imageFolderPanel)

        # Middle panel - image list
        self.imageListPanel = QWidget()
        self.imageListPanel.setLayout(QVBoxLayout())
        self.imageListPanel.layout().setContentsMargins(10, 10, 10, 10)

        # List label
        self.imageListLabel = QLabel("Select a folder to view images")
        self.imageListPanel.layout().addWidget(self.imageListLabel)

        # Create list widget for images
        self.imageListWidget = QListWidget()
        self.imageListWidget.setSelectionMode(QListWidget.ExtendedSelection)
        self.imageListWidget.itemDoubleClicked.connect(self.onImageDoubleClicked)
        self.imageListWidget.currentItemChanged.connect(self.onImageSelected)
        self.imageListPanel.layout().addWidget(self.imageListWidget)

        # Add to splitter
        self.imagesSplitter.addWidget(self.imageListPanel)

        # Right panel - image preview
        self.imagePreviewPanel = QWidget()
        self.imagePreviewPanel.setLayout(QVBoxLayout())
        self.imagePreviewPanel.layout().setContentsMargins(10, 10, 10, 10)

        # Preview label
        self.imagePreviewLabel = QLabel("Image Preview")
        self.imagePreviewLabel.setAlignment(Qt.AlignCenter)
        self.imagePreviewPanel.layout().addWidget(self.imagePreviewLabel)

        # Image display label
        self.previewImageLabel = QLabel()
        self.previewImageLabel.setAlignment(Qt.AlignCenter)
        self.previewImageLabel.setMinimumSize(400, 400)
        self.previewImageLabel.setScaledContents(False)
        self.previewImageLabel.setFrameShape(QFrame.Box)
        self.previewImageLabel.setText("Select an image to preview")

        # Create a scroll area for the image
        scrollArea = QScrollArea()
        scrollArea.setWidget(self.previewImageLabel)
        scrollArea.setWidgetResizable(True)
        self.imagePreviewPanel.layout().addWidget(scrollArea)

        # Text extract button
        self.extractTextButton = QPushButton("Extract Text")
        self.extractTextButton.clicked.connect(self.onExtractText)
        self.extractTextButton.setEnabled(False)  # Disabled until an image is selected
        self.imagePreviewPanel.layout().addWidget(self.extractTextButton)

        # Add to splitter
        self.imagesSplitter.addWidget(self.imagePreviewPanel)

        # Set splitter sizes
        self.imagesSplitter.setSizes([200, 400, 400])

    def setupOpenAiTab(self):
        """Set up the OpenAI tab with configuration tables"""
        # Initialize current table type
        self.current_table_type = None

        # Create a toolbar for the OpenAI tab
        toolbarWidget = QWidget()
        toolbarLayout = QHBoxLayout(toolbarWidget)
        toolbarLayout.setContentsMargins(5, 5, 5, 5)

        # Add gridlines checkbox
        self.chkGridlines = QCheckBox("Table Has Gridlines")
        self.chkGridlines.setChecked(True)
        self.chkGridlines.stateChanged.connect(self.onGridlinesToggled)
        toolbarLayout.addWidget(self.chkGridlines)

        # Add import/export buttons
        self.btnImportMapping = QPushButton("Import Mapping .xlsx")
        self.btnImportMapping.clicked.connect(self.onImportMapping)
        toolbarLayout.addWidget(self.btnImportMapping)

        self.btnExportMapping = QPushButton("Export Mapping .xlsx")
        self.btnExportMapping.clicked.connect(self.onExportMapping)
        toolbarLayout.addWidget(self.btnExportMapping)

        self.btnSaveMapping = QPushButton("Save Mapping For This Source")
        self.btnSaveMapping.clicked.connect(self.saveMapping)
        toolbarLayout.addWidget(self.btnSaveMapping)

        # Add stretch to push everything to the left
        toolbarLayout.addStretch(1)
        toolbarWidget.setSizePolicy(QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed))

        # Add the toolbar to the layout
        self.openAiTab.layout().addWidget(toolbarWidget)

        # Create a horizontal splitter
        self.openAiSplitter = QSplitter(Qt.Horizontal)
        self.openAiTab.layout().addWidget(self.openAiSplitter)

        # Left pane - Table Types
        self.tableTypesWidget = QWidget()
        self.tableTypesWidget.setLayout(QVBoxLayout())
        self.tableTypesWidget.layout().setContentsMargins(5, 5, 5, 5)

        # Add label
        self.tableTypesWidget.layout().addWidget(QLabel("Table Types:"))

        # Create table view for table types
        self.tableTypesTable = QTableView()
        self.tableTypesTable.setAlternatingRowColors(True)
        self.tableTypesTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tableTypesTable.setSelectionMode(QAbstractItemView.SingleSelection)
        self.tableTypesTable.horizontalHeader().setStretchLastSection(True)
        self.tableTypesTable.verticalHeader().setVisible(False)
        self.tableTypesTable.setShowGrid(True)

        # Create model for table types
        self.tableTypesModel = QStandardItemModel(0, 1)
        self.tableTypesModel.setHorizontalHeaderLabels(["Table Type"])

        # Populate model with table types
        for table_type in TABLE_TYPES:
            item = QStandardItem(table_type)
            self.tableTypesModel.appendRow(item)

        # Set model to table view
        self.tableTypesTable.setModel(self.tableTypesModel)

        # Connect selection changed signal
        self.tableTypesTable.selectionModel().selectionChanged.connect(self.onTableTypeSelected)

        # Add table view to layout
        self.tableTypesWidget.layout().addWidget(self.tableTypesTable)

        # Add to splitter
        self.openAiSplitter.addWidget(self.tableTypesWidget)

        # Right pane - Field Mapping
        self.fieldMappingWidget = QWidget()
        self.fieldMappingWidget.setLayout(QVBoxLayout())
        self.fieldMappingWidget.layout().setContentsMargins(5, 5, 5, 5)

        # Add label
        self.fieldMappingLabel = QLabel("Field Mapping:")
        self.fieldMappingWidget.layout().addWidget(self.fieldMappingLabel)

        # Create table view for field mapping
        self.fieldMappingTable = EditableTableView()
        self.fieldMappingTable.setAlternatingRowColors(True)
        self.fieldMappingTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.fieldMappingTable.horizontalHeader().setStretchLastSection(True)
        self.fieldMappingTable.setShowGrid(True)

        # Create model for field mapping
        self.fieldMappingModel = QStandardItemModel(0, 2)
        self.fieldMappingModel.setHorizontalHeaderLabels(["column_name", "internal_field"])

        # Set model to table view
        self.fieldMappingTable.setModel(self.fieldMappingModel)

        # Add buttons for adding/removing items
        buttonLayout = QHBoxLayout()

        self.addFieldButton = QPushButton("Add Field")
        self.addFieldButton.clicked.connect(self.onAddField)
        buttonLayout.addWidget(self.addFieldButton)

        self.removeFieldButton = QPushButton("Remove Field")
        self.removeFieldButton.clicked.connect(self.onRemoveField)
        buttonLayout.addWidget(self.removeFieldButton)

        buttonLayout.addStretch(1)

        # Add button layout to widget
        self.fieldMappingWidget.layout().addLayout(buttonLayout)

        # Add table view to layout
        self.fieldMappingWidget.layout().addWidget(self.fieldMappingTable)

        # Add to splitter
        self.openAiSplitter.addWidget(self.fieldMappingWidget)

        # Set splitter sizes
        self.openAiSplitter.setSizes([300, 700])

        # Select the first table type by default
        if self.tableTypesModel.rowCount() > 0:
            self.tableTypesTable.selectRow(0)

    def onGridlinesToggled(self, state):
        """Handle toggling of gridlines checkbox"""
        show_grid = state == Qt.Checked
        self.tableTypesTable.setShowGrid(show_grid)
        self.fieldMappingTable.setShowGrid(show_grid)

    def saveCurrentTableMappings(self):
        """Save the current table's data to the appropriate table type"""
        if self.current_table_type is None:
            return

        if not hasattr(self, 'table_mappings'):
            self.table_mappings = {}

        # Extract data from the model
        mapping_data = []
        for row in range(self.fieldMappingModel.rowCount()):
            column_name = self.fieldMappingModel.item(row, 0).text()
            internal_field = self.fieldMappingModel.item(row, 1).text()
            mapping_data.append((column_name, internal_field))

        # Update the mapping for this table type
        self.table_mappings[self.current_table_type] = pd.DataFrame(mapping_data, columns=["column_name", "internal_field"])

    def onTableTypeSelected(self, selected, deselected, save=True):
        """Handle selection of a table type"""
        indexes = selected.indexes()
        if not indexes:
            return

        # Save current table data before switching
        if save and hasattr(self, 'table_mappings') and self.current_table_type is not None:
            self.saveCurrentTableMappings()

        # Get the selected table type
        row = indexes[0].row()
        table_type = self.tableTypesModel.item(row, 0).text()

        # Update current table type
        self.current_table_type = table_type.lower()

        # Update the field mapping label
        self.fieldMappingLabel.setText(f"Field Mapping for {table_type} - Set the correct column names and order (as displayed in PDF). Optional - rename to internal field names - leave blank if not renamed.")

        # Clear the current field mapping
        self.fieldMappingModel.removeRows(0, self.fieldMappingModel.rowCount())

        df = self.table_mappings.get(self.current_table_type, pd.DataFrame(columns=["column_name", "internal_field"]))
        for r in df.itertuples():
            column_name, internal_field = r.column_name, r.internal_field
            row = [QStandardItem(column_name), QStandardItem(internal_field)]
            self.fieldMappingModel.appendRow(row)

        # Resize columns to contents
        self.fieldMappingTable.resizeColumnsToContents()

    def onAddField(self):
        """Add a new field to the mapping table"""
        self.fieldMappingModel.appendRow([QStandardItem(""), QStandardItem("")])

        # Select the new row for immediate editing
        new_row = self.fieldMappingModel.rowCount() - 1
        self.fieldMappingTable.selectRow(new_row)
        self.fieldMappingTable.edit(self.fieldMappingModel.index(new_row, 0))

        # Update the mapping data
        self.updateTableMappings()

    def onRemoveField(self):
        """Remove the selected field from the mapping table"""
        selected_rows = self.fieldMappingTable.selectionModel().selectedRows()
        if not selected_rows:
            return

        # Sort rows in descending order to avoid index shifting during removal
        rows = sorted([index.row() for index in selected_rows], reverse=True)

        for row in rows:
            self.fieldMappingModel.removeRow(row)

        # Update the mapping data
        self.updateTableMappings()

    def updateTableMappings(self):
        """Update the internal table mappings from the current model"""
        # Save the current table's data
        self.saveCurrentTableMappings()
        return self.table_mappings

    def onImportMapping(self, filename=None):
        """Import field mappings from an Excel file"""
        # Show file picker dialog

        if not filename:
            filename, _ = QFileDialog.getOpenFileName(
                self,
                "Import Field Mappings",
                "",
                "Excel Files (*.xlsx)"
            )

            if not filename:
                return

        try:
            # Initialize table_mappings if it doesn't exist
            if not hasattr(self, 'table_mappings'):
                self.table_mappings = {}

            # Read Excel file
            excel_file = pd.ExcelFile(filename)

            # Process each sheet (tab)
            for sheet_name in excel_file.sheet_names:
                # Skip sheets that aren't in TABLE_TYPES
                if sheet_name.lower() not in [t.lower() for t in TABLE_TYPES]:
                    continue

                # Read the sheet into a DataFrame
                df = pd.read_excel(excel_file, sheet_name=sheet_name)

                # Ensure required columns exist
                if 'column_name' not in df.columns or 'internal_field' not in df.columns:
                    QMessageBox.warning(
                        self,
                        "Import Error",
                        f"Sheet '{sheet_name}' is missing required columns (column_name, internal_field)"
                    )
                    continue

                # Extract mapping data
                mapping_data = []
                for _, row in df.iterrows():
                    column_name = str(row['column_name'])
                    internal_field = str(row['internal_field'])
                    mapping_data.append({"column_name": column_name, "internal_field": internal_field})

                # Find the matching table type (case-insensitive)
                for table_type in TABLE_TYPES:
                    if table_type.lower() == sheet_name.lower():
                        df2 = pd.DataFrame(mapping_data)
                        self.table_mappings[table_type.lower()] = df2
                        break

            # Refresh the current view
            self.onTableTypeSelected(self.tableTypesTable.selectionModel().selection(), None, False)

            QMessageBox.information(
                self,
                "Import Successful",
                f"Field mappings imported from {filename}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Import Error",
                f"Failed to import field mappings: {str(e)}"
            )

    def getTableMappings(self):
        return self.updateTableMappings()

    def saveMapping(self):
        path = self.txtOutputImages.text().strip()
        filename = os.path.join(path, "table_mapping.xlsx")
        self.onExportMapping(filename)
        QMessageBox.information(self, "Save Successful", "Field mappings saved successfully. This is auto loaded when source opened")

    def onExportMapping(self, filename=None):
        """Export field mappings to an Excel file"""
        # Ensure we have the latest mappings
        self.updateTableMappings()

        if not filename:

            # Show file picker dialog
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Export Field Mappings",
                "",
                "Excel Files (*.xlsx)"
            )

        if not filename:
            return

        # Add .xlsx extension if not present
        if not filename.lower().endswith('.xlsx'):
            filename += '.xlsx'

        try:
            # Create a dictionary of DataFrames for each table type
            dfs = {}

            # Process each table type
            for table_type in TABLE_TYPES:
                # Get mapping data for this table type
                df = self.table_mappings.get(table_type, pd.DataFrame(columns=['column_name', 'internal_field']))

                # Add to dictionary
                dfs[table_type] = df

            # Write to Excel file with each table type in a separate sheet
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                for table_type, df in dfs.items():
                    if df.empty:
                        continue
                    df.to_excel(writer, sheet_name=table_type, index=False)

            QMessageBox.information(
                self,
                "Export Successful",
                f"Field mappings exported to {filename}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export field mappings: {str(e)}"
            )

    def refreshProjectSources(self):
        """Refresh the project sources and update the combobox"""
        statement = ("SELECT projectId, filename from ProjectSources")

        if self.isVisible() and QMessageBox.question(self, "Confirmation", "Continue refresh all project sources?") != QMessageBox.Yes:
            return
        try:
            with self.db.connect() as conn:
                df = pd.read_sql_query(statement, conn)
                self.projectSourcesDf = df

                # Update combobox
                self.cboxSource.clear()
                if not df.empty:
                    items = []
                    for _, row in df.iterrows():
                        fileExists = os.path.exists(getSourceRawDataPath(row['projectId'], row['filename']))
                        displayText = f"{row['filename']} (Project ID: {row['projectId']}) (Preprocessed: {fileExists})"
                        items.append(displayText)
                        self.cboxSource.addItem(displayText, (row['projectId'], row['filename']))

                    completer = QCompleter(items, self.cboxSource)
                    completer.setCaseSensitivity(Qt.CaseInsensitive)
                    completer.setFilterMode(Qt.MatchContains)  # optional: match substring
                    self.cboxSource.setCompleter(completer)

                return df
        except Exception as e:
            print(f"Failed to refresh all project sources info: {e}")
            QMessageBox.warning(self, "Database Error", f"Failed to refresh project sources: {e}")
            return None

    def onSourceSelected(self, index):
        """Handle selection of a project source from the combobox"""
        if index >= 0:
            # Get the selected project ID and filename from the combobox's user data
            self.currentProjectId, self.currentFilename = self.cboxSource.itemData(index)
            print(f"Selected project ID: {self.currentProjectId}, filename: {self.currentFilename}")

            self.pbDetectBlank.setEnabled(True)
            self.pbExtractImages.setEnabled(True)
            self.pbExtract.setEnabled(True)

            ocrDir = getSourceDataDir(self.currentProjectId, self.currentFilename)
            ocrDir = os.path.join(ocrDir, "ocr_data")
            self.txtOutputImages.setText(ocrDir)

            # Optionally load data automatically when selection changes
            # self.loadData()

    def refreshImages(self):
        """Load the folders from the OCR data directory"""
        print("Refreshing images")

        # Clear the current list
        self.imageFolderList.clear()

        ocrDir = self.txtOutputImages.text()
        if not ocrDir or not os.path.exists(ocrDir):
            print(f"OCR directory not found: {ocrDir}")
            self.imageListLabel.setText("OCR directory not found")
            return

        ocrImagesDir = os.path.join(ocrDir, "output_images")

        try:
            if not os.path.exists(ocrImagesDir):
                print(f"Output images directory not found: {ocrImagesDir}")
                self.imageListLabel.setText("Output images directory not found")
                return

            # Get all subdirectories
            subdirs = [d for d in os.listdir(ocrImagesDir)
                      if os.path.isdir(os.path.join(ocrImagesDir, d))]

            # Add each folder to the list
            for folder in sorted(subdirs):
                self.imageFolderList.addItem(folder)

            # Update the status
            if subdirs:
                self.imageListLabel.setText(f"Select a folder to view images ({len(subdirs)} folders found)")
            else:
                self.imageListLabel.setText("No folders found in OCR directory")

        except Exception as e:
            print(f"Error loading folder list: {str(e)}")
            self.imageListLabel.setText(f"Error loading folders: {str(e)}")

    def loadData(self):
        """Load data for the selected project source"""
        if self.currentProjectId is None or self.currentFilename is None:
            QMessageBox.information(self, "Selection Required", "Please select a project source first.")
            return

        if QMessageBox.question(self, "Confirmation", "Proceed opening project?") != QMessageBox.Yes:
            return

        self.refreshImages()

        path = self.txtOutputImages.text().strip()
        filename = os.path.join(path, "table_mapping.xlsx")
        if os.path.exists(filename):
            self.onImportMapping(filename)
        return
        try:
            # Load table data
            rawFile = getSourceRawDataPath(self.currentProjectId, self.currentFilename)
            df = pd.read_feather(rawFile)

            print(df["coordinates2"].head(), df["coordinates2"].dtype, type(df["coordinates2"].iloc[0]))
            print(df["coordinates2"].head(), df["coordinates2"].dtype, type(df["color"].iloc[0]))

            # Check if DataFrame is empty using len() instead of .empty property
            if len(df) == 0:
                QMessageBox.information(self, "No Data", "No data found for the selected project source.")
            else:
                # Update the model with the new data
                self.currentDataFrame = df
                self.model.setData(df)

                # Resize columns to content - do this after model is updated
                try:
                    self.tableView.resizeColumnsToContents()
                except Exception as resize_error:
                    print(f"Warning: Could not resize columns: {resize_error}")

                # Update window title with selection info
                self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId})")

                # Populate page filter combobox
                self.availablePages = df['pdf_page'].unique().tolist()
                self.availablePages.sort()
                self.cboxPage.clear()
                for page in self.availablePages:
                    self.cboxPage.addItem(str(page))

                # Enable text filter
                self.txtFilter.setEnabled(True)

                # Load PDF document
                if self.pdfViewer.loadPDF(self.currentProjectId, self.currentFilename):
                    # If PDF loaded successfully and we have pages, display the first page
                    if self.availablePages and len(self.availablePages) > 0:
                        self.displayPDFPage(self.availablePages[0])
                else:
                    QMessageBox.warning(self, "PDF Loading Error",
                                       "Could not load the PDF document. PDF viewing will not be available.")

        except Exception as e:
            print(f"Error loading data: {e}")
            QMessageBox.warning(self, "Data Loading Error", f"Failed to load data: {e}")

    def onPageTextChanged(self, text):
        """Handle direct input of page number"""
        if not text or not hasattr(self, 'availablePages') or not self.availablePages:
            return

        try:
            # Only process if the text is a valid integer
            page_number = int(text)

            # Check if this page exists in our available pages
            if page_number in self.availablePages:
                # Find the index of this page in our sorted list
                index = self.availablePages.index(page_number)

                # Block signals to prevent recursive calls
                self.cboxPage.blockSignals(True)

                # Set the combobox to this index
                self.cboxPage.setCurrentIndex(index)

                # Unblock signals
                self.cboxPage.blockSignals(False)

                # Apply the filter directly
                self.currentPage = page_number
                self.applyFilters()

                # Update PDF view if on PDF tab
                self.displayPDFPage(page_number)

                # Update navigation buttons
                self.pbPrevPage.setEnabled(index > 0)
                self.pbNextPage.setEnabled(index < len(self.availablePages) - 1)
            # Don't show error messages during typing - it would be annoying
        except ValueError:
            # Not a valid integer, ignore
            pass

    def onPageSelected(self, index):
        """Handle page selection from the combobox"""
        if index >= 0 and self.availablePages and index < len(self.availablePages):
            self.currentPage = self.availablePages[index]
            self.applyFilters()

            # Update PDF view if on PDF tab
            self.displayPDFPage(self.currentPage)

            # Update navigation buttons
            self.pbPrevPage.setEnabled(index > 0)
            self.pbNextPage.setEnabled(index < len(self.availablePages) - 1)

    def onPreviousPage(self):
        """Navigate to the previous page"""
        currentIndex = self.cboxPage.currentIndex()
        if currentIndex > 0:
            self.cboxPage.setCurrentIndex(currentIndex - 1)

    def onNextPage(self):
        """Navigate to the next page"""
        currentIndex = self.cboxPage.currentIndex()
        if currentIndex < self.cboxPage.count() - 1:
            self.cboxPage.setCurrentIndex(currentIndex + 1)

    def detectGeneralEmptyCells(self):
        """Detect blank pages in the extracted images"""
        print("Detecting blank pages...")

        if QMessageBox.question(self,
                "Confirmation",
                "Are you sure you want to detect blank pages? This may take some time.") != QMessageBox.Yes:
            return

        ocrDir = self.txtOutputImages.text().strip()
        if not ocrDir or not os.path.exists(ocrDir):
            QMessageBox.warning(self, "Warning", "OCR directory not found. Please select a valid OCR directory.")
            return

        # Get the directory for general images
        ocrGeneralImagesDir = os.path.join(ocrDir, "output_images", "general")
        if not os.path.exists(ocrGeneralImagesDir):
            QMessageBox.warning(self, "Warning", "General images directory not found")
            return

        # Get list of PNG images
        images = os.listdir(ocrGeneralImagesDir)
        if not images:
            QMessageBox.warning(self, "Warning", "No general images found")
            return

        from src.utils.cv_utils import analyze_images_whitespace
        import re
        import pandas as pd

        # Filter for PNG images and get full paths
        image_paths = [os.path.join(ocrGeneralImagesDir, image) for image in images if image.endswith(".png")]
        if not image_paths:
            QMessageBox.warning(self, "Warning", "No PNG images found")
            return

        # Analyze whitespace in images
        df = analyze_images_whitespace(image_paths, use_multiprocessing=False)

        # Extract pdf_page and field from image_name
        def extract_info(image_name):
            # Extract page number
            page_match = re.search(r'_page_(\d+)', image_name)
            pdf_page = int(page_match.group(1)) if page_match else 0

            # Extract field (word preceding _page_)
            field_match = re.search(r'([^_/\\]+)_page_', image_name)
            field = field_match.group(1) if field_match else ""

            return pdf_page, field

        # Apply extraction to create new columns
        pdf_pages = []
        fields = []

        for _, row in df.iterrows():
            pdf_page, field = extract_info(row['image_name'])
            pdf_pages.append(pdf_page)
            fields.append(field)

        df['pdf_page'] = pdf_pages
        df['field'] = fields

        # Reorder columns to put pdf_page and field first
        cols = ['pdf_page', 'field', 'image_name', 'whitespace_pct', 'is_blank']
        if 'error' in df.columns:
            cols.append('error')
        df = df[cols]

        # Sort by pdf_page and field
        df = df.sort_values(['pdf_page', 'field']).reset_index(drop=True)

        # Ensure debug directory exists
        debug_dir = os.path.join(os.getcwd(), "debug")
        os.makedirs(debug_dir, exist_ok=True)

        # Save to Excel
        excel_path = os.path.join(debug_dir, "whitespace_general.xlsx")
        df.to_excel(excel_path, index=False)

        # Show summary to user
        blank_count = df['is_blank'].sum()
        total_count = len(df)
        blank_pct = (blank_count / total_count) * 100 if total_count > 0 else 0

        QMessageBox.information(
            self,
            "Blank Page Detection Results",
            f"Found {blank_count} blank pages out of {total_count} total images ({blank_pct:.1f}%).\n\n"
            f"Results saved to {excel_path}"
        )

        # Create PDFs for blank and non-blank pages
        pdf_msg = ""
        try:
            from PIL import Image
            import fitz  # PyMuPDF is already imported at the top

            # Create separate DataFrames for blank and non-blank images
            blank_df = df[df['is_blank'] == 1].copy()
            nonblank_df = df[df['is_blank'] == 0].copy()

            # Function to create a PDF with multiple images per page
            def create_pdf_from_images(image_df, output_path, images_per_page=32, cols=8):
                if len(image_df) == 0:
                    return False

                # Calculate rows needed based on images_per_page and cols
                rows = images_per_page // cols

                # Create new PDF document
                doc = fitz.open()

                # Calculate image size and positions
                page_width, page_height = 842, 595  # A4 landscape size in points
                margin = 20  # Smaller margin to fit more images
                img_width = (page_width - 2 * margin) / cols
                img_height = (page_height - 2 * margin) / rows

                # Process images in batches
                for i in range(0, len(image_df), images_per_page):
                    # Create a new page (landscape orientation)
                    page = doc.new_page(width=page_width, height=page_height)

                    # Process a batch of images for this page
                    batch = image_df.iloc[i:i+images_per_page]

                    for j, (idx, row) in enumerate(batch.iterrows()):
                        try:
                            # Calculate position on page
                            col = j % cols
                            row_pos = j // cols

                            x = margin + col * img_width
                            y = margin + row_pos * img_height

                            # Open image
                            img_path = os.path.join(ocrGeneralImagesDir, row['image_name'])
                            if not os.path.exists(img_path):
                                continue

                            # Insert image into PDF
                            rect = fitz.Rect(x, y, x + img_width, y + img_height)
                            page.insert_image(rect, filename=img_path)

                            # Add metadata text
                            metadata_text = f"P{row['pdf_page']}-{row['field']}-{row['whitespace_pct']:.0f}%"
                            text_rect = fitz.Rect(x, y, x + img_width, y + 12)  # Smaller text area

                            # Add semi-transparent background for text
                            page.draw_rect(text_rect, color=(0, 0, 0), fill=(0, 0, 0), fill_opacity=0.7)

                            # Add text (smaller font size)
                            page.insert_text(
                                fitz.Point(x + 2, y + 9),  # Adjusted position for smaller text
                                metadata_text,
                                color=(1, 0, 0),  # Red text
                                fontsize=7  # Smaller font size
                            )

                        except Exception as e:
                            print(f"Error processing image {row['image_name']}: {e}")

                # Save the PDF
                doc.save(output_path)
                doc.close()
                return True

            # Create PDFs
            blank_pdf_path = os.path.join(debug_dir, "blank_pages.pdf")
            nonblank_pdf_path = os.path.join(debug_dir, "nonblank_pages.pdf")

            # Create PDFs with progress updates
            QApplication.processEvents()
            blank_created = create_pdf_from_images(blank_df, blank_pdf_path)
            QApplication.processEvents()
            nonblank_created = create_pdf_from_images(nonblank_df, nonblank_pdf_path)
            QApplication.processEvents()

            # Update message with PDF information
            if blank_created:
                pdf_msg += f"\nBlank pages PDF: {blank_pdf_path}"
            if nonblank_created:
                pdf_msg += f"\nNon-blank pages PDF: {nonblank_pdf_path}"

        except ImportError as e:
            pdf_msg = f"\nCould not create PDFs: {str(e)}"
        except Exception as e:
            pdf_msg = f"\nError creating PDFs: {str(e)}"

        QMessageBox.information(
            self,
            "Blank Page Detection Results",
            f"Found {blank_count} blank pages out of {total_count} total images ({blank_pct:.1f}%).\n\n"
            f"Results saved to {excel_path}{pdf_msg}"
        )

    def runExtractImages(self):
        """Run OCR extraction with the selected options"""
        # Get current project and filename

        if not self.currentProjectId or not self.currentFilename:
            QMessageBox.warning(self, "Warning", "Please select a project source first.")
            return

        if QMessageBox.question(self,
                                "Confirmation",
                                "Are you sure you want to extract images? This will clear existing images from OCR Data folder.\n\n"
                                "Note - All pages with ROIs will be processed. Page range is not used") != QMessageBox.Yes:
            return

        dpi = self.spinDpi.value()
        widthLimit = None
        if self.chkLimitWidth.isChecked():
            maxWidth = self.spinMaxWidth.value()
            m = []
            if QMessageBox.question(self,
                "Confirmation",
                f"Maximum width of image has been set to {maxWidth} pixels.\n\n"
                f"Images will be cropped based on {dpi} DPI but may reduce if necessary.\n\n"
                "A limit may be necessary for OCR e.g. Surya.") != QMessageBox.Yes:
                return
            widthLimit = maxWidth

        print(self.currentProjectId, self.currentFilename)

        payloadFile = None

        if self.radioInternalPayload.isChecked():
            # Use internal payload
            sourceDir = getSourceDataDir(self.currentProjectId, self.currentFilename, makedir=True)
            payloadFile = os.path.join(sourceDir, "options.json")
            roiPayload = convert_roi_payload(payloadFile, force_extract=True, ignore_bom=True)
        elif self.radioCustomPayload.isChecked():
            # Use custom payload
            payloadFile = self.txtRoiPayload.text().strip()
            if not payloadFile:
                QMessageBox.warning(self,
                        "Warning",
                        "No valid ROI Payload selected. Choose a valid json file or save one using Blueprinter Reader")
                return
            roiPayload = convert_roi_payload(payloadFile, force_extract=True)

        if not payloadFile:
            QMessageBox.warning(self,
                    "Warning",
                    "No valid ROI Payload selected. Choose a valid json file or save one using Blueprinter Reader")
            return

        outputImagesPath = self.txtOutputImages.text().strip()
        if not outputImagesPath:
            QMessageBox.warning(self,
                    "Warning",
                    "No valid output images path specified. Please provide a valid path.")
            return

        outputImagesPath = os.path.join(outputImagesPath, "output_images")

        extract_images_pdf(
            self.currentFilename,
            roiPayload,
            outputImagesPath,
            dpi,
            width_limit=widthLimit,
        )
        self.refreshImages()

    def runExtraction(self):
        """Run OCR extraction with the selected options"""
        # Get current project and filename
        project_id, filename = self.get_current_selection()
        if not project_id or not filename:
            QMessageBox.warning(self, "Warning", "Please select a project source first.")
            return

        ocrMethod = self.cboxExtractionMode.currentText()

        if QMessageBox.question(self,
                                "Confirmation",
                                f"Are you sure you want to run - {ocrMethod.upper()} OCR extraction? This will clear existing OCR results.") != QMessageBox.Yes:
            return

        if ocrMethod == "openai":
            if QMessageBox.question(self,
                    "Warning",
                    "OpenAI extraction is not free. Continue?") != QMessageBox.Yes:
                return

        payloadFile = None

        if self.radioInternalPayload.isChecked():
            # Use internal payload
            sourceDir = getSourceDataDir(self.currentProjectId, self.currentFilename, makedir=True)
            payloadFile = os.path.join(sourceDir, "options.json")
            try:
                roiPayload = convert_roi_payload(payloadFile, force_extract=True, ignore_bom=True)
            except Exception as e:
                QMessageBox.warning(self,
                        "Warning",
                        f"Failed to load saved ROI payload: {str(e)}")
                return
        elif self.radioCustomPayload.isChecked():
            # Use custom payload
            payloadFile = self.txtPayloadPath.text().strip()
            if not payloadFile:
                QMessageBox.warning(self,
                        "Warning",
                        "No valid ROI Payload selected. Choose a valid json file or save one using Blueprinter Reader")
                return
            roiPayload = convert_roi_payload(payloadFile, force_extract=True, ignore_bom=True)

        if not payloadFile:
            QMessageBox.warning(self,
                    "Warning",
                    "No valid ROI Payload selected. Choose a valid json file or save one using Blueprinter Reader")
            return # Get extraction mode

        ocrResultsPaths = {
            "bom": "debug/ocr/bom_ocr_results.json",
            "general": "debug/ocr/general_ocr_results.json",
            "spool": "debug/ocr/spool_ocr_results.json",
            "spec": "debug/ocr/spec_ocr_results.json",
            "ifc": "debug/ocr/ifc_ocr_results.json",
            "weld": "debug/ocr/weld_ocr_results.json",
            "iso": "debug/ocr/iso_ocr_results.json",
            "generic_1": "debug/ocr/generic_1_ocr_results.json",
            "generic_2": "debug/ocr/generic_2_ocr_results.json"
        }

        extract_types = []
        if self.chkExtractBom.isChecked():
            extract_types.append("bom")
        if self.chkExtractGeneral.isChecked():
            extract_types.append("general")
        if self.chkExtractSpool.isChecked():
            extract_types.append("spool")
        if self.chkExtractSpec.isChecked():
            extract_types.append("spec")
        if self.chkExtractIfc.isChecked():
            extract_types.append("ifc")
        if self.chkGeneric1.isChecked():
            extract_types.append("generic_1")
        if self.chkGeneric2.isChecked():
            extract_types.append("generic_2")
        if self.chkIso.isChecked():
            extract_types.append("iso")

        # Get page range
        pages_to_extract = parse_page_range(self.txtPageRange.text().strip())
        if not pages_to_extract:
            if QMessageBox.question(self,
                    "Extract all pages",
                    "No page range specified or invalid. Extracting all pages. Continue?") != QMessageBox.Yes:
                return
            pages_to_extract = None
        else:
            if QMessageBox.question(self,
                    "Extract specific pages",
                    f"Extracting {len(pages_to_extract)} pages - {self.txtPageRange.text().strip()}. Continue?") != QMessageBox.Yes:
                return

        resultDfs = {}

        ocrDir = self.txtOutputImages.text().strip()
        outputImagesDir = os.path.join(ocrDir, "output_images")
        lang = 'en-US'  # Default to English

        hasGridlines = self.chkGridlines.isChecked()

        if ocrMethod == "openai":
            base_path = "debug/ocr"

            tableMappings = self.getTableMappings()

            messages = []

            valid_tables = ["bom", "spec"]
            for t in TABLE_TYPES:
                if t not in extract_types:
                    # not being extracted
                    continue
                if t not in valid_tables:
                    QMessageBox.warning(self,
                            "Warning",
                            f"{t} is set for extraction. Only {valid_tables} are supported currently.")
                    return
                df = tableMappings.get(t)
                if df is None or df.empty:
                    QMessageBox.warning(self,
                            "Warning",
                            f"{t} is set for extraction but no table mapping found. Go to OpenAI configuration to set all tables which are needed for extraction")
                    return
                m = []
                internal_names = set()
                column_names = set()
                for _, row in df.iterrows():
                    if pd.isna(row['column_name']):
                        QMessageBox.warning(self,
                            "Warning",
                            f"A column name empty in table {t}")
                        return

                    if pd.notna(row['internal_field']) and row['internal_field'].lower() in internal_names:
                        QMessageBox.warning(self,
                            "Warning",
                            f"Duplicate internal field {row['internal_field']} in table {t}. Only one field per internal name allowed.")
                        return
                    internal_names.add(row['internal_field'].lower())

                    if row['column_name'].lower() in column_names:
                        QMessageBox.warning(self,
                            "Warning",
                            f"Duplicate column name {row['column_name']} in table {t}. Only one column name per table allowed.")
                        return
                    column_names.add(row['column_name'].lower())

                    m.append(f"{row['column_name']} -> {row['internal_field']}")
                messages.append(f"{t}: " +", ".join(m))

            messages = '\n'.join(messages)
            if messages and QMessageBox.question(self, "Confirm mapping", f"Confirm mapping for tables:\n{messages}") != QMessageBox.Yes:
                return

            resultDfs, filenames = asyncio.run(openai_extract(self.currentFilename,
                                                extract_types,
                                                outputImagesDir,
                                                roiPayload,
                                                base_path,
                                                pages=pages_to_extract,
                                                table_mappings=tableMappings,
                                                gen_field_list=[],
                                                has_gridlines=hasGridlines))

            QMessageBox.information(self, "Success", f"Saved to {filenames}")

        elif ocrMethod == "winrt":

            for extract_type in extract_types:
                print(f"Extracting {extract_type}...")
                folder_path = os.path.join(ocrDir, "output_images", extract_type)
                ocr_file = ocrResultsPaths[extract_type]
                results = process_folder(folder_path, lang, ocr_file, pages_to_extract)

                # Try to extract tables from each result
                if not results:
                    QMessageBox.warning(self, "Warning", "No valid OCR results produced.")
                    return

                df_file = f"debug/ocr/{extract_type}_data.xlsx"

                with open(ocr_file, 'r') as f:
                    ocr_results = json.load(f)

                if extract_type == "general":
                    rows = {}
                    for key, value in results.items():
                        pdf_page = value['pdf_page']
                        field = value['field']
                        text = value['text']
                        rows.setdefault(pdf_page, {"pdf_page": pdf_page})
                        rows[pdf_page].update({field: text})

                    rows = list(rows.values())
                    general_df = pd.DataFrame(rows)
                    general_df.to_excel(df_file, index=False)
                    resultDfs["general"] = general_df
                    resultDf = general_df
                elif extract_type == "bom":
                    resultDf = extract_bom_table_from_ocr(ocr_results, roiPayload)
                    resultDfs["bom"] = resultDf
                else:
                    #
                    resultDf = extract_standard_table(ocr_results, roiPayload, extract_type)
                    resultDfs[extract_type] = resultDf
                    # continue

                resultDf["__ocr_method__"] = ocrMethod
                resultDf.to_excel(df_file, index=False)

        else:
            QMessageBox.warning(self, "Warning", "Unsupported OCR method selected.")
            return

        # Create tabs for each extracted dataframe
        if resultDfs:
            # Create a new tab widget if it doesn't exist
            if not hasattr(self, 'resultTabWidget'):
                # Create a vertical splitter
                self.resultSplitter = QSplitter(Qt.Vertical)

                # Create a container for the existing content
                self.topContainer = QWidget()
                self.topContainer.setLayout(QVBoxLayout())
                self.topContainer.layout().setContentsMargins(0, 0, 0, 0)

                # Create the tab widget for results
                self.resultTabWidget = QTabWidget()
                self.resultTabWidget.setTabsClosable(True)
                self.resultTabWidget.tabCloseRequested.connect(self.onTabCloseRequested)

                # Get the main dialog layout
                mainLayout = self.layout()

                # Save existing widgets
                existingWidgets = []
                for i in range(mainLayout.count()):
                    widget = mainLayout.itemAt(i).widget()
                    if widget:
                        existingWidgets.append(widget)

                # Clear the main layout
                while mainLayout.count():
                    item = mainLayout.takeAt(0)

                # Add the splitter to the main layout
                mainLayout.addWidget(self.resultSplitter)

                # Add existing widgets to the top container
                for widget in existingWidgets:
                    widget.setParent(None)
                    self.topContainer.layout().addWidget(widget)

                # Add containers to the splitter
                self.resultSplitter.addWidget(self.topContainer)
                self.resultSplitter.addWidget(self.resultTabWidget)

                # Set initial sizes (70% top, 30% bottom)
                self.resultSplitter.setSizes([700, 300])
            else:
                # Just clear existing tabs
                self.resultTabWidget.clear()

            # Add an export button to the tab widget
            self.exportButton = QPushButton("Export Data")
            self.exportButton.clicked.connect(self.showExportOptions)
            self.exportButton.setToolTip("Export table data to Excel files")

            # Create a corner widget to hold the export button
            cornerWidget = QWidget()
            cornerLayout = QHBoxLayout(cornerWidget)
            cornerLayout.setContentsMargins(0, 0, 5, 0)
            cornerLayout.addWidget(self.exportButton)
            self.resultTabWidget.setCornerWidget(cornerWidget)

            db = DatabaseManager()
            pdf_id_map = db.get_source_pdf_id_map(self.currentProjectId, self.currentFilename)

            # Add a tab for each dataframe
            for tableName, df in resultDfs.items():
                if df is None or df.empty:
                    continue

                # Create a horizontal splitter for table and image preview
                tabSplitter = QSplitter(Qt.Horizontal)

                # Create a table view for the dataframe
                tableView = EditableTableView()
                tableView.setAlternatingRowColors(True)
                tableView.setSortingEnabled(True)
                tableView.setSelectionBehavior(QAbstractItemView.SelectItems)  # Select cells instead of rows
                tableView.setSelectionMode(QAbstractItemView.SingleSelection)  # Allow selecting one cell at a time
                tableView.setContextMenuPolicy(Qt.CustomContextMenu)
                tableView.customContextMenuRequested.connect(
                    lambda pos, tv=tableView, name=tableName:
                    self.showResultContextMenu(pos, tv, name))

                if "pdf_page" in df.columns:
                    # Create a temporary column to preserve original order
                    df = df.reset_index(drop=True)
                    df["_original_index"] = df.index
                    # Sort by pdf_page, but maintain original order within each page
                    df = df.sort_values(by=["pdf_page", "_original_index"])
                    # Remove the temporary column
                    df = df.drop("_original_index", axis=1)

                    df["pdf_id"] = None
                    for row in df.itertuples():
                        pdf_id = pdf_id_map.get(row.pdf_page, None)
                        if pdf_id is None:
                            continue
                        index = row.Index
                        df.loc[index, "pdf_id"] = pdf_id

                # Create model and set data
                model = PandasModel(df)
                tableView.setModel(model)

                # Connect selection model AFTER setting the model
                tableView.selectionModel().selectionChanged.connect(
                    lambda selected, deselected, tv=tableView, name=tableName:
                    self.onResultRowSelected(selected, deselected, tv, name))

                # Format the table view
                tableView.resizeColumnsToContents()

                # Create image preview widget
                imagePreviewWidget = QWidget()
                imagePreviewLayout = QVBoxLayout(imagePreviewWidget)
                imagePreviewLayout.setContentsMargins(0, 0, 0, 0)

                # Add label for image preview
                imagePreviewLabel = QLabel("Image Preview")
                imagePreviewLabel.setAlignment(Qt.AlignCenter)
                imagePreviewLayout.addWidget(imagePreviewLabel)

                # Add zoomable image viewer
                imageViewer = ZoomableImageViewer()
                imageViewer.setText("Select a cell to view associated image")
                imagePreviewLayout.addWidget(imageViewer)

                # Store the image viewer in a dictionary keyed by table name
                if not hasattr(self, 'imageViewers'):
                    self.imageViewers = {}
                self.imageViewers[tableName] = imageViewer

                # Store the preview label in a dictionary keyed by table name
                if not hasattr(self, 'imagePreviewLabels'):
                    self.imagePreviewLabels = {}
                self.imagePreviewLabels[tableName] = imagePreviewLabel

                # Add widgets to splitter
                tabSplitter.addWidget(tableView)
                tabSplitter.addWidget(imagePreviewWidget)

                # Set initial sizes (70% table, 30% image)
                tabSplitter.setSizes([700, 300])

                # Add the tab with the splitter
                self.resultTabWidget.addTab(tabSplitter, tableName.lower())

            # Show the tab widget
            self.resultTabWidget.setVisible(True)

            # Add a status bar below the tab widget if it doesn't exist
            if not hasattr(self, 'resultStatusBar'):
                self.resultStatusBar = QStatusBar()
                self.resultStatusBar.setSizeGripEnabled(False)
                self.layout().addWidget(self.resultStatusBar)

            self.resultStatusBar.setVisible(True)
            self.resultStatusBar.showMessage("Select a cell to view details")

            # Message to user
            QMessageBox.information(self, "Extraction Complete",
                                   f"Extracted {len(resultDfs)} data types successfully.")

    def onTabCloseRequested(self, index):
        """Handle tab close request with confirmation dialog"""
        tab_name = self.resultTabWidget.tabText(index)
        reply = QMessageBox.question(
            self,
            "Confirm Tab Close",
            f"Are you sure you want to close the '{tab_name}' tab?\nThis action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.resultTabWidget.removeTab(index)

            # If no tabs left, hide the tab widget
            if self.resultTabWidget.count() == 0:
                self.resultTabWidget.setVisible(False)

    def showExportOptions(self):
        """Show export options dialog for exporting table data"""
        if not hasattr(self, 'resultTabWidget') or self.resultTabWidget.count() == 0:
            QMessageBox.warning(self, "Export Error", "No data available to export.")
            return

        # Create dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Export Options")
        dialog.setMinimumWidth(400)
        layout = QVBoxLayout(dialog)

        # Export scope
        scopeGroup = QGroupBox("Export Scope")
        scopeLayout = QVBoxLayout(scopeGroup)

        self.radioCurrentTab = QRadioButton("Current Tab Only")
        self.radioCurrentTab.setChecked(True)
        self.radioAllTabs = QRadioButton("All Tabs")

        scopeLayout.addWidget(self.radioCurrentTab)
        scopeLayout.addWidget(self.radioAllTabs)
        layout.addWidget(scopeGroup)

        # Export format
        formatGroup = QGroupBox("Export Format")
        formatLayout = QVBoxLayout(formatGroup)

        self.radioSeparateFiles = QRadioButton("Separate Files (one per tab)")
        self.radioSeparateFiles.setChecked(True)
        self.radioSingleWorkbook = QRadioButton("Single Workbook (all tabs)")

        formatLayout.addWidget(self.radioSeparateFiles)
        formatLayout.addWidget(self.radioSingleWorkbook)
        layout.addWidget(formatGroup)

        # Filename options
        filenameGroup = QGroupBox("Filename Options")
        filenameLayout = QVBoxLayout(filenameGroup)

        self.chkIncludeDateTime = QCheckBox("Include Date/Time in Filename")
        filenameLayout.addWidget(self.chkIncludeDateTime)

        # Output directory
        dirLayout = QHBoxLayout()
        dirLayout.addWidget(QLabel("Output Directory:"))
        self.txtExportDir = QLineEdit()
        self.txtExportDir.setText("debug/ocr/export")
        dirLayout.addWidget(self.txtExportDir)

        browseButton = QPushButton("Browse...")
        browseButton.clicked.connect(lambda: self.txtExportDir.setText(
            QFileDialog.getExistingDirectory(self, "Select Export Directory", self.txtExportDir.text())
        ))
        dirLayout.addWidget(browseButton)
        filenameLayout.addLayout(dirLayout)

        layout.addWidget(filenameGroup)

        # Buttons
        buttonBox = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttonBox.accepted.connect(dialog.accept)
        buttonBox.rejected.connect(dialog.reject)
        layout.addWidget(buttonBox)

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            self.exportData()

    def exportData(self):
        """Export data based on selected options"""
        export_dir = self.txtExportDir.text()
        include_datetime = self.chkIncludeDateTime.isChecked()
        export_all = self.radioAllTabs.isChecked()
        single_workbook = self.radioSingleWorkbook.isChecked()

        # Validate export directory
        if not os.path.exists(export_dir):
            try:
                os.makedirs(export_dir)
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Could not create export directory: {str(e)}")
                return

        # Get timestamp for filename if needed
        timestamp = ""
        if include_datetime:
            timestamp = f"_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            if single_workbook:
                # Export all tabs to a single workbook
                if export_all:
                    # Create a writer for the combined Excel file
                    combined_filename = os.path.join(export_dir, f"combined_export{timestamp}.xlsx")
                    with pd.ExcelWriter(combined_filename, engine='openpyxl') as writer:
                        # Export each tab to a sheet
                        for i in range(self.resultTabWidget.count()):
                            tab_name = self.resultTabWidget.tabText(i)
                            table_view = self.resultTabWidget.widget(i)
                            if isinstance(table_view, QSplitter):
                                tableView = table_view.widget(0)
                                if isinstance(tableView, QTableView):
                                    model = tableView.model()
                                    if hasattr(model, '_data'):
                                        df = model._data
                                        df.to_excel(writer, sheet_name=tab_name, index=False)

                                        # Apply autofilter and column sizing
                                        worksheet = writer.sheets[tab_name]
                                        # Get the dimensions of the DataFrame
                                        if not df.empty:
                                            # Add autofilter
                                            worksheet.auto_filter.ref = f"A1:{chr(64 + min(len(df.columns), 26))}{min(len(df) + 1, 1048576)}"

                                            # Auto-size columns based on first 100 rows
                                            for idx, col in enumerate(df.columns):
                                                column_letter = chr(65 + idx) if idx < 26 else chr(64 + idx // 26) + chr(65 + idx % 26)
                                                # Get max length from column name and first 100 values
                                                max_length = len(str(col)) + 2  # Add padding
                                                for row_idx in range(min(100, len(df))):
                                                    cell_value = str(df.iloc[row_idx, idx])
                                                    max_length = max(max_length, min(len(cell_value) + 2, 50))  # Limit to 50 chars
                                                worksheet.column_dimensions[column_letter].width = max_length

                    # Show success message with options to open file or folder
                    self.showExportSuccessDialog([combined_filename], export_dir)
                else:
                    # Export current tab only to a single file
                    current_index = self.resultTabWidget.currentIndex()
                    tab_name = self.resultTabWidget.tabText(current_index)
                    table_view = self.resultTabWidget.widget(current_index)

                    if isinstance(table_view, QSplitter):
                        tableView = table_view.widget(0)
                        if isinstance(tableView, QTableView):
                            model = tableView.model()
                            if hasattr(model, '_data'):
                                df = model._data
                                filename = os.path.join(export_dir, f"{tab_name}{timestamp}.xlsx")

                                # Export with autofilter and column sizing
                                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                                    df.to_excel(writer, index=False)
                                    worksheet = writer.sheets['Sheet1']

                                    # Add autofilter
                                    if not df.empty:
                                        worksheet.auto_filter.ref = f"A1:{chr(64 + min(len(df.columns), 26))}{min(len(df) + 1, 1048576)}"

                                        # Auto-size columns based on first 100 rows
                                        for idx, col in enumerate(df.columns):
                                            column_letter = chr(65 + idx) if idx < 26 else chr(64 + idx // 26) + chr(65 + idx % 26)
                                            # Get max length from column name and first 100 values
                                            max_length = len(str(col)) + 2  # Add padding
                                            for row_idx in range(min(100, len(df))):
                                                cell_value = str(df.iloc[row_idx, idx])
                                                max_length = max(max_length, min(len(cell_value) + 2, 50))  # Limit to 50 chars
                                            worksheet.column_dimensions[column_letter].width = max_length

                                # Show success message with options to open file or folder
                                self.showExportSuccessDialog([filename], export_dir)
            else:
                # Export to separate files
                exported_files = []

                if export_all:
                    # Export all tabs to separate files
                    for i in range(self.resultTabWidget.count()):
                        tab_name = self.resultTabWidget.tabText(i)
                        table_view = self.resultTabWidget.widget(i)

                        if isinstance(table_view, QSplitter):
                            tableView = table_view.widget(0)
                            if isinstance(tableView, QTableView):
                                model = tableView.model()
                                if hasattr(model, '_data'):
                                    df = model._data
                                    filename = os.path.join(export_dir, f"{tab_name}{timestamp}.xlsx")

                                    # Export with autofilter and column sizing
                                    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                                        df.to_excel(writer, index=False)
                                        worksheet = writer.sheets['Sheet1']

                                        # Add autofilter
                                        if not df.empty:
                                            worksheet.auto_filter.ref = f"A1:{chr(64 + min(len(df.columns), 26))}{min(len(df) + 1, 1048576)}"

                                            # Auto-size columns based on first 100 rows
                                            for idx, col in enumerate(df.columns):
                                                column_letter = chr(65 + idx) if idx < 26 else chr(64 + idx // 26) + chr(65 + idx % 26)
                                                # Get max length from column name and first 100 values
                                                max_length = len(str(col)) + 2  # Add padding
                                                for row_idx in range(min(100, len(df))):
                                                    cell_value = str(df.iloc[row_idx, idx])
                                                    max_length = max(max_length, min(len(cell_value) + 2, 50))  # Limit to 50 chars
                                                worksheet.column_dimensions[column_letter].width = max_length

                                    exported_files.append(filename)
                else:
                    # Export current tab only
                    current_index = self.resultTabWidget.currentIndex()
                    tab_name = self.resultTabWidget.tabText(current_index)
                    table_view = self.resultTabWidget.widget(current_index)

                    if isinstance(table_view, QSplitter):
                        tableView = table_view.widget(0)
                        if isinstance(tableView, QTableView):
                            model = tableView.model()
                            if hasattr(model, '_data'):
                                df = model._data
                                filename = os.path.join(export_dir, f"{tab_name}_data{timestamp}.xlsx")

                                # Export with autofilter and column sizing
                                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                                    df.to_excel(writer, index=False)
                                    worksheet = writer.sheets['Sheet1']

                                    # Add autofilter
                                    if not df.empty:
                                        worksheet.auto_filter.ref = f"A1:{chr(64 + min(len(df.columns), 26))}{min(len(df) + 1, 1048576)}"

                                        # Auto-size columns based on first 100 rows
                                        for idx, col in enumerate(df.columns):
                                            column_letter = chr(65 + idx) if idx < 26 else chr(64 + idx // 26) + chr(65 + idx % 26)
                                            # Get max length from column name and first 100 values
                                            max_length = len(str(col)) + 2  # Add padding
                                            for row_idx in range(min(100, len(df))):
                                                cell_value = str(df.iloc[row_idx, idx])
                                                max_length = max(max_length, min(len(cell_value) + 2, 50))  # Limit to 50 chars
                                            worksheet.column_dimensions[column_letter].width = max_length

                                exported_files.append(filename)

                if exported_files:
                    # Show success message with options to open files or folder
                    self.showExportSuccessDialog(exported_files, export_dir)

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Error exporting data: {str(e)}")

    def showExportSuccessDialog(self, files, export_dir):
        """Show a success dialog with options to open files or containing folder"""
        # Create custom dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Export Complete")
        dialog.setMinimumWidth(500)
        layout = QVBoxLayout(dialog)

        # Message
        message = QLabel(f"Successfully exported {len(files)} file(s):")
        layout.addWidget(message)

        # List of files
        fileList = QListWidget()
        for file_path in files:
            item = QListWidgetItem(os.path.basename(file_path))
            item.setData(Qt.UserRole, file_path)  # Store full path
            fileList.addItem(item)

        if len(files) <= 5:  # Only show list for a reasonable number of files
            layout.addWidget(fileList)
        else:
            # Just show count for many files
            layout.addWidget(QLabel(f"{len(files)} files exported to {export_dir}"))

        # Buttons
        buttonLayout = QHBoxLayout()

        # Open folder button
        openFolderButton = QPushButton("Open Containing Folder")
        openFolderButton.clicked.connect(lambda: QDesktopServices.openUrl(QUrl.fromLocalFile(export_dir)))
        buttonLayout.addWidget(openFolderButton)

        # Open file button (only if 1 file)
        if len(files) == 1:
            openFileButton = QPushButton("Open File")
            openFileButton.clicked.connect(lambda: QDesktopServices.openUrl(QUrl.fromLocalFile(files[0])))
            buttonLayout.addWidget(openFileButton)

        # Close button
        closeButton = QPushButton("Close")
        closeButton.clicked.connect(dialog.accept)
        buttonLayout.addWidget(closeButton)

        layout.addLayout(buttonLayout)

        # Show dialog
        dialog.exec_()

    def applyFilters(self):
        """Apply all active filters to the dataset"""
        if self.currentDataFrame is None:
            return

        filtered_df = self.currentDataFrame.copy()

        # Apply page filter if a page is selected
        if self.currentPage is not None:
            filtered_df = filtered_df[filtered_df['pdf_page'] == self.currentPage]

        # Apply text filter if text is entered
        filter_text = self.txtFilter.text().strip()
        if filter_text:
            # Apply text filter to all string columns
            mask = pd.Series(False, index=filtered_df.index)
            for column in filtered_df.select_dtypes(include=['object']).columns:
                mask |= filtered_df[column].astype(str).str.contains(filter_text, case=False, na=False)
            filtered_df = filtered_df[mask]

        # Update model with filtered data
        self.model.setData(filtered_df)

        # Enable/disable reset button
        has_filters = (self.currentPage is not None) or bool(filter_text)
        self.pbExtract.setEnabled(has_filters)

        # Update status info
        if len(filtered_df) < len(self.currentDataFrame):
            status = f"Showing {len(filtered_df)} of {len(self.currentDataFrame)} rows"
            self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId}) - {status}")
        else:
            self.setWindowTitle(f"Raw Data Viewer - {self.currentFilename} (Project ID: {self.currentProjectId})")

    def get_current_selection(self):
        """Return the currently selected project ID and filename"""
        return {
            "projectId": self.currentProjectId,
            "filename": self.currentFilename
        }

    def displayPDFPage(self, page_number):
        """Display the specified page in the PDF viewer"""
        if self.pdfViewer.pdfDocument is None:
            return

        # In the PDF, page numbers are 0-based, but our data uses 1-based page numbers
        pdf_page_index = page_number - 1

        # Check if the page index is valid
        if pdf_page_index >= 0 and pdf_page_index < len(self.pdfViewer.pdfDocument):
            if self.pdfViewer.displayPage(pdf_page_index):
                # Update zoom label
                self.updateZoomLabel()

    def onZoomIn(self):
        """Handle zoom in button click"""
        self.pdfViewer.zoomIn()
        self.updateZoomLabel()

    def onZoomOut(self):
        """Handle zoom out button click"""
        self.pdfViewer.zoomOut()
        self.updateZoomLabel()

    def onZoomReset(self):
        """Handle zoom reset button click"""
        self.pdfViewer.resetZoom()
        self.updateZoomLabel()

    def onFitToView(self):
        """Handle fit to view button click"""
        self.pdfViewer.fitInView()
        self.updateZoomLabel()

    def updateZoomLabel(self):
        """Update the zoom percentage label"""
        zoom_percent = int(self.pdfViewer.zoomFactor * 100)
        self.lblZoom.setText(f"Zoom: {zoom_percent}%")

    def onTabChanged(self, index):
        """Handle tab widget tab change"""
        if index == 1:  # PDF View tab
            # If we have a current page and PDF document, display it
            if self.currentPage is not None and self.pdfViewer.pdfDocument is not None:
                self.displayPDFPage(self.currentPage)

    def onFetchDrawings(self):
        """Fetch and display drawings from the current PDF page"""
        if not self.pdfViewer.pdfDocument:
            QMessageBox.warning(self, "No PDF Loaded", "Please load a PDF document first.")
            return

        if self.pdfViewer.currentPage is None:
            QMessageBox.warning(self, "No Page Selected", "Please select a page first.")
            return

        try:
            # Clear previous drawings
            self.drawingsList = []

            # Get drawings from the current page using PyMuPDF
            doc = self.pdfViewer.pdfDocument
            page_idx = self.pdfViewer.currentPage
            drawings = []

            # Get the page
            page = doc[page_idx]

            # Get drawings from the page
            page_drawings = page.get_drawings()
            if page_drawings:
                for i, drawing in enumerate(page_drawings):
                    # Add to our list with page number and index
                    drawings.append({
                        'page': page_idx,
                        'index': i,
                        'drawing': drawing,
                        'rect': drawing.get('rect', None)
                    })

            # Update the drawings model
            self.drawingsList = drawings
            self.drawingsModel.setData(drawings)

            # Resize columns to content
            self.drawingsTable.resizeColumnsToContents()

            # Show message with count
            if drawings:
                QMessageBox.information(self, "Drawings Found", f"Found {len(drawings)} drawings on page {page_idx+1}.")
            else:
                QMessageBox.information(self, "No Drawings", f"No drawings found on page {page_idx+1}.")

        except Exception as e:
            print(f"Error fetching drawings: {e}")
            QMessageBox.warning(self, "Error", f"Failed to extract drawings: {e}")

    def onDrawingSelectionChanged(self, selected, deselected):
        """Handle selection change in the drawings table"""
        # Clear any previous highlighting by refreshing the view
        self.refreshPdfView()

        indexes = selected.indexes()
        if not indexes:
            # Clear intersections text
            self.intersectionsText.clear()
            return

        # Get the selected row
        row = indexes[0].row()
        if row < 0 or row >= len(self.drawingsList):
            return

        # Get the drawing data
        drawing_data = self.drawingsList[row]

        # Navigate to the page if needed
        page_idx = drawing_data['page']
        if self.pdfViewer.currentPage != page_idx:
            self.pdfViewer.displayPage(page_idx)
            self.updateZoomLabel()

        # Highlight the drawing
        self.highlightDrawing(drawing_data)

        # Find intersecting bboxes in raw data
        self.findIntersectingBboxes(drawing_data)

    def highlightDrawing(self, drawing_data):
        """Highlight the selected drawing on the PDF page"""
        if not drawing_data or not self.pdfViewer.pixmapItem:
            return

        # Get the drawing and its rect
        drawing = drawing_data['drawing']
        rect = drawing_data.get('rect')

        if not rect or len(rect) != 4:
            print("No valid rectangle for highlighting")
            return

        try:
            # Create a copy of the current pixmap
            original_pixmap = self.pdfViewer.pixmapItem.pixmap()
            pixmap = QPixmap(original_pixmap)

            # Create a painter to draw on the pixmap
            painter = QPainter(pixmap)

            # Set up the pen for highlighting - changed to green
            pen = QPen(QColor(0, 255, 0))  # Green
            pen.setWidth(3)
            painter.setPen(pen)

            # Get the drawing type
            drawing_type = self.drawingsModel._get_drawing_type(drawing_data)

            # Draw the highlight based on the type
            x0, y0, x1, y1 = rect

            # Convert from PDF coordinates to pixmap coordinates
            # The pixmap might be at a different scale than the PDF
            scale_x = pixmap.width() / self.pdfViewer.pdfDocument[self.pdfViewer.currentPage].rect.width
            scale_y = pixmap.height() / self.pdfViewer.pdfDocument[self.pdfViewer.currentPage].rect.height

            x0 = int(x0 * scale_x)
            y0 = int(y0 * scale_y)
            x1 = int(x1 * scale_x)
            y1 = int(y1 * scale_y)

            if drawing_type == "Rectangle":
                painter.drawRect(x0, y0, x1 - x0, y1 - y0)
            elif drawing_type == "Line":
                # For lines, try to use the actual line coordinates
                items = drawing.get('items', [])
                if items and 'start' in items[0] and 'end' in items[0]:
                    start_x, start_y = items[0]['start']
                    end_x, end_y = items[0]['end']

                    # Convert coordinates
                    start_x = int(start_x * scale_x)
                    start_y = int(start_y * scale_y)
                    end_x = int(end_x * scale_x)
                    end_y = int(end_y * scale_y)

                    painter.drawLine(start_x, start_y, end_x, end_y)
                else:
                    # Fall back to drawing a line across the bounding box
                    painter.drawLine(x0, y0, x1, y1)
            else:
                # For other shapes, just draw the bounding box
                painter.drawRect(x0, y0, x1 - x0, y1 - y0)

            # End painting
            painter.end()

            # Update the pixmap in the scene
            self.pdfViewer.scene().clear()
            self.pdfViewer.pixmapItem = self.pdfViewer.scene().addPixmap(pixmap)

        except Exception as e:
            print(f"Error highlighting drawing: {e}")

    def refreshPdfView(self):
        """Refresh the PDF view to clear any highlighting"""
        if self.pdfViewer.currentPage is not None and self.pdfViewer.pdfDocument is not None:
            self.pdfViewer.displayPage(self.pdfViewer.currentPage)

    def findIntersectingBboxes(self, drawing_data):
        """Find raw data entries with bboxes that intersect with the selected drawing"""
        if not self.currentDataFrame is not None or len(self.currentDataFrame) == 0:
            self.intersectionsText.setText("No raw data available.")
            return

        # Get the drawing bbox
        drawing_rect = drawing_data.get('rect')
        if not drawing_rect or len(drawing_rect) != 4:
            self.intersectionsText.setText("No bounding box available for selected drawing.")
            return

        # Get the current page
        page_idx = drawing_data['page']

        # Filter data for current page
        page_data = self.currentDataFrame[self.currentDataFrame['pdf_page'] == page_idx + 1]  # +1 because PDF pages are 0-based but data might be 1-based

        if len(page_data) == 0:
            self.intersectionsText.setText(f"No raw data for page {page_idx + 1}.")
            return

        # Convert drawing rect to format comparable with data
        x0, y0, x1, y1 = drawing_rect
        drawing_bbox = [x0, y0, x1, y1]

        # Check for intersections
        intersections = []

        for idx, row in page_data.iterrows():
            # Check if coordinates2 exists and is not null
            if 'coordinates2' in row and row['coordinates2'] is not None:
                try:
                    # Get raw data bbox
                    raw_bbox = row['coordinates2']

                    if self.bboxesIntersect(drawing_bbox, raw_bbox):
                        # Format the intersection information
                        value = row.get('value', 'No value')
                        confidence = row.get('confidence', 'N/A')

                        intersection_info = f"Row {idx}:\n"
                        intersection_info += f"  Value: {value}\n"
                        intersection_info += f"  Confidence: {confidence}\n"
                        intersection_info += f"  BBox: {raw_bbox}\n"
                        intersection_info += f"  Overlap: {self.calculateOverlap(drawing_bbox, raw_bbox):.2f}%\n"
                        intersection_info += "-" * 50 + "\n"

                        intersections.append((self.calculateOverlap(drawing_bbox, raw_bbox), intersection_info))
                except Exception as e:
                    print(f"Error processing row {idx}: {e}")

        # Sort by overlap percentage (descending)
        intersections.sort(reverse=True)

        if intersections:
            # Display intersections
            result_text = f"Found {len(intersections)} intersecting items on page {page_idx + 1}:\n\n"
            result_text += "\n".join([info for _, info in intersections])
            self.intersectionsText.setText(result_text)
        else:
            self.intersectionsText.setText(f"No intersecting items found on page {page_idx + 1}.")

    def bboxesIntersect(self, bbox1, bbox2):
        """Check if two bounding boxes intersect"""
        # Extract coordinates
        x0_1, y0_1, x1_1, y1_1 = bbox1

        # Handle different formats of bbox2
        if isinstance(bbox2, (list, tuple)) and len(bbox2) == 4:
            x0_2, y0_2, x1_2, y1_2 = bbox2
        elif hasattr(bbox2, 'tolist'):  # numpy array
            bbox_list = bbox2.tolist()
            if len(bbox_list) == 4:
                x0_2, y0_2, x1_2, y1_2 = bbox_list
            else:
                return False
        else:
            return False

        # Check for intersection
        # Two boxes intersect if they overlap in both x and y directions
        return not (x1_1 < x0_2 or x1_2 < x0_1 or y1_1 < y0_2 or y1_2 < y0_1)

    def calculateOverlap(self, bbox1, bbox2):
        """Calculate the percentage of overlap between two bounding boxes"""
        # Extract coordinates
        x0_1, y0_1, x1_1, y1_1 = bbox1

        # Handle different formats of bbox2
        if isinstance(bbox2, (list, tuple)) and len(bbox2) == 4:
            x0_2, y0_2, x1_2, y1_2 = bbox2
        elif hasattr(bbox2, 'tolist'):  # numpy array
            bbox_list = bbox2.tolist()
            if len(bbox_list) == 4:
                x0_2, y0_2, x1_2, y1_2 = bbox_list
            else:
                return 0
        else:
            return 0

        # Calculate areas
        area1 = (x1_1 - x0_1) * (y1_1 - y0_1)
        area2 = (x1_2 - x0_2) * (y1_2 - y0_2)

        if area1 <= 0 or area2 <= 0:
            return 0

        # Calculate intersection area
        x_overlap = max(0, min(x1_1, x1_2) - max(x0_1, x0_2))
        y_overlap = max(0, min(y1_1, y1_2) - max(y0_1, y0_2))
        intersection_area = x_overlap * y_overlap

        # Calculate percentage of overlap relative to the smaller area
        smaller_area = min(area1, area2)
        return (intersection_area / smaller_area) * 100 if smaller_area > 0 else 0

    def onResultRowSelected(self, selected, deselected, tableView, tableName):
        """Handle cell selection in result tables"""
        if not selected.indexes():
            self.resultStatusBar.showMessage(f"No cell selected in {tableName}")
            return

        # Get the selected cell data
        index = selected.indexes()[0]
        row_idx = index.row()
        col_idx = index.column()
        model = tableView.model()

        if hasattr(model, '_data'):
            # Get the DataFrame
            df = model._data

            # Get column name and value
            selected_column = df.columns[col_idx]
            cell_value = df.iloc[row_idx, col_idx]

            # Get the row data as a Series
            row_data = df.iloc[row_idx]

            # Store the selected data for later use
            self.selectedRowData = row_data
            self.selectedColumnName = selected_column
            self.selectedCellValue = cell_value
            self.selectedTableName = tableName

            image = None
            image_path = None
            # Display info in status bar
            if 'pdf_page' in row_data:
                self.resultStatusBar.showMessage(
                    f"Selected: {tableName} - Page {row_data['pdf_page']} - {selected_column}: {cell_value}")

                ocrDir = self.txtOutputImages.text().strip()
                if tableName != "general":
                    if selected_column != "pdf_page":
                        image = f"table_{tableName}_page_{row_data['pdf_page']}.png"
                else:
                    image = f"{selected_column}_page_{row_data['pdf_page']}.png"

                if image:
                    image_path = os.path.join(ocrDir, "output_images", tableName, image)
                    print(image_path)
            else:
                # Show selected cell info
                self.resultStatusBar.showMessage(
                    f"Selected: {tableName} - {selected_column}: {cell_value}")

            # Update image preview label with detailed information
            if tableName in self.imagePreviewLabels:
                preview_text = f"Image Preview - {tableName}"
                if 'pdf_page' in row_data:
                    preview_text += f" - Page {row_data['pdf_page']}"
                preview_text += f"\nField: {selected_column}, Value: {cell_value}"
                self.imagePreviewLabels[tableName].setText(preview_text)

            # Try to find associated image
            if image_path and os.path.exists(image_path):
                # Display the image if it exists
                if tableName in self.imageViewers:
                    pixmap = QPixmap(image_path)
                    if not pixmap.isNull():
                        # Set the image in the zoomable viewer
                        self.imageViewers[tableName].setImage(pixmap, image_path, tableName)
                    else:
                        self.imageViewers[tableName].setText(f"Failed to load image:<br>{image_path}")
                else:
                    print(f"No image viewer for table: {tableName}")
            else:
                if tableName in self.imageViewers:
                    if image_path:
                        self.imageViewers[tableName].setText(f"Image not found:<br>{image_path}")
                    else:
                        self.imageViewers[tableName].setText("No image associated with this selection")

    def onResizeEvent(self, event):
        """Handle resize events to update image scaling"""
        # Call the original resize event handler
        super().resizeEvent(event)

        # Resize all displayed images
        if hasattr(self, 'originalPixmaps') and hasattr(self, 'imageViewers'):
            for tableName, pixmap in self.originalPixmaps.items():
                if tableName in self.imageViewers and not pixmap.isNull():
                    # Scale pixmap to fit the label while maintaining aspect ratio
                    self.imageViewers[tableName].setImage(pixmap)

    def showResultContextMenu(self, position, tableView, tableName):
        """Show context menu for result table cells"""
        # Get the cell under the mouse
        idx = tableView.indexAt(position)
        if not idx.isValid():
            return

        row_idx = idx.row()
        col_idx = idx.column()
        model = tableView.model()

        if hasattr(model, '_data'):
            # Get the DataFrame and cell data
            df = model._data
            row_data = df.iloc[row_idx]
            column_name = df.columns[col_idx]
            cell_value = df.iloc[row_idx, col_idx]

            # Create context menu
            menu = QMenu()

            # Add cell-specific actions
            copyCellAction = menu.addAction(f"Copy Value: {cell_value}")
            copyCellAction.triggered.connect(lambda: QApplication.clipboard().setText(str(cell_value)))

            # Add row actions
            menu.addSeparator()
            copyRowAction = menu.addAction("Copy Row Data")
            copyRowAction.triggered.connect(lambda: self.copyRowDataToClipboard(row_data))

            # Add view image action if image_name exists
            if 'image_name' in row_data and row_data['image_name']:
                viewImageAction = menu.addAction("View Image")
                viewImageAction.triggered.connect(lambda: self.viewRowImage(row_data))

            # Add view PDF page action if pdf_page exists
            if 'pdf_page' in row_data:
                viewPageAction = menu.addAction(f"View PDF Page {row_data['pdf_page']}")
                viewPageAction.triggered.connect(lambda: self.viewPdfPage(row_data))

            # Show the menu
            menu.exec_(tableView.viewport().mapToGlobal(position))

    def copyRowDataToClipboard(self, row_data):
        """Copy selected row data to clipboard"""
        # Format as tab-separated values
        text = "\t".join([f"{col}: {row_data[col]}" for col in row_data.index])
        QApplication.clipboard().setText(text)
        self.resultStatusBar.showMessage("Row data copied to clipboard", 3000)

    def viewRowImage(self, row_data):
        """View the image associated with this row"""
        image_path = row_data['image_name']
        if os.path.exists(image_path):
            # Use QDesktopServices to open the image in the default viewer
            QDesktopServices.openUrl(QUrl.fromLocalFile(image_path))
        else:
            QMessageBox.warning(self, "Image Not Found",
                              f"Could not find image at {image_path}")

    def viewPdfPage(self, row_data):
        """View the PDF page associated with this row"""
        if hasattr(self, 'currentFilename') and self.currentFilename:
            page_num = int(row_data['pdf_page'])
            # Switch to PDF tab
            self.tabWidget.setCurrentIndex(2)  # Assuming PDF tab is index 2
            # TODO: Implement logic to navigate to the specific page
            self.resultStatusBar.showMessage(f"Viewing PDF page {page_num}", 3000)
        else:
            QMessageBox.warning(self, "PDF Not Available",
                              "Could not locate the source PDF file")

    def onImageSelected(self, current, previous):
        """Handle selection of an image in the list"""
        if not current:
            self.previewImageLabel.setText("Select an image to preview")
            self.extractTextButton.setEnabled(False)
            return

        # Get the image path from the item data
        image_path = current.data(Qt.UserRole)
        if not image_path or not os.path.exists(image_path):
            self.previewImageLabel.setText("Image not found")
            self.extractTextButton.setEnabled(False)
            return

        try:
            # Load the image
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                self.previewImageLabel.setText("Failed to load image")
                self.extractTextButton.setEnabled(False)
                return

            # Scale the pixmap to fit in the label while maintaining aspect ratio
            self.previewImageLabel.setPixmap(pixmap.scaled(
                self.previewImageLabel.width(),
                self.previewImageLabel.height(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            ))

            # Update the preview label with the filename
            self.imagePreviewLabel.setText(f"Previewing: {os.path.basename(image_path)}")

            # Enable the extract text button
            self.extractTextButton.setEnabled(True)

        except Exception as e:
            print(f"Error loading preview image: {str(e)}")
            self.previewImageLabel.setText(f"Error: {str(e)}")
            self.extractTextButton.setEnabled(False)

    def onExtractText(self):
        """Extract text from the selected image"""
        current_item = self.imageListWidget.currentItem()
        if not current_item:
            return

        image_path = current_item.data(Qt.UserRole)
        if not image_path or not os.path.exists(image_path):
            QMessageBox.warning(self, "Warning", "Image file not found.")
            return

        # Show a message box with placeholder text
        # QMessageBox.information(self, "Extract Text", f"Text extraction from {os.path.basename(image_path)} will be implemented here.")

        # TODO: Implement actual text extraction functionality

        print(image_path)

        from src.atom.ocr.winrt.extract_win import recognize_file

        result = recognize_file(image_path, lang='en-US')
        print(result)

    def onFolderSelected(self, item):
        """Handle folder selection from the list"""
        if not item:
            return

        folder_name = item.text()
        ocrDir = self.txtOutputImages.text()
        folder_path = os.path.join(ocrDir, "output_images", folder_name)

        print(f"Selected folder: {folder_name}")
        self.imageListLabel.setText(f"Loading images from {folder_name}...")

        # Clear the current list
        self.imageListWidget.clear()

        # Load images from the selected folder
        self.loadImagesFromFolder(folder_path)

    def loadImagesFromFolder(self, folder_path):
        """Load and display images from the selected folder"""
        try:
            # Get all image files in the folder
            image_files = [f for f in os.listdir(folder_path)
                          if os.path.isfile(os.path.join(folder_path, f)) and
                          f.lower().endswith(('.png'))]

            if not image_files:
                self.imageListLabel.setText("No images found in the selected folder")
                return

            # Sort the files by page number
            def extract_page_number(filename):
                # Try to extract page number from patterns like "page_100" or "page_1"
                match = re.search(r'page_(\d+)', filename)
                if match:
                    return int(match.group(1))
                return float('inf')  # Files without page numbers will be at the end

            # Sort files by page number
            image_files = sorted(image_files, key=extract_page_number)

            # Store the folder path
            self.current_folder_path = folder_path

            # Add items to the list widget
            for image_file in image_files:
                # Try to extract page number for display
                page_num = extract_page_number(image_file)
                display_text = image_file
                if page_num != float('inf'):
                    display_text = f"Page {page_num}: {image_file}"

                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, os.path.join(folder_path, image_file))  # Store full path
                self.imageListWidget.addItem(item)

            # Update the status
            if image_files:
                self.imageListLabel.setText(f"Displaying {len(image_files)} images from {os.path.basename(folder_path)}")

        except Exception as e:
            print(f"Error loading images: {str(e)}")
            self.imageListLabel.setText(f"Error loading images: {str(e)}")

    def onBrowseRoiPayload(self):
        """Browse for custom ROI payload JSON file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select ROI Payload JSON File",
            os.path.expanduser("~"),
            "JSON Files (*.json)"
        )
        if file_path:
            self.txtRoiPayload.setText(file_path)

    def onOpenOutputLocation(self):
        """Open the output images directory in file explorer"""
        path = self.txtOutputImages.text().strip()
        if path and os.path.exists(path):
            # Use the appropriate command based on the OS
            if os.name == 'nt':  # Windows
                os.startfile(path)
            elif os.name == 'posix':  # macOS or Linux
                import subprocess
                if sys.platform == 'darwin':  # macOS
                    subprocess.Popen(['open', path])
                else:  # Linux
                    subprocess.Popen(['xdg-open', path])
        else:
            QMessageBox.warning(self, "Warning", "The OCR directory for the project source does not exist. This will be generated when you extract images.")

    def onPayloadTypeToggled(self):
        """Handle toggling between internal and custom ROI payload"""
        use_custom = self.radioCustomPayload.isChecked()
        self.txtRoiPayload.setEnabled(use_custom)
        self.btnBrowseRoiPayload.setEnabled(use_custom)

    def onImageDoubleClicked(self, item):
        """Handle double-click on an image item to open it"""
        image_path = item.data(Qt.UserRole)
        if image_path and os.path.exists(image_path):
            self.openFile(image_path)

if __name__ == "__main__":
    import sys
    sys.path[0] = ""
    app = QApplication([])
    window = OcrExtractionDialog()
    window.show()
    app.exec()