from normalize_description import normalize_rating

# Test case for the #3000 format
test_description = "CAP, ASME B16.11, NPT, #3000, A182 F304/304L"

# Test the fix
print(f"Testing: {test_description}")
normalized, metadata, review, match_type = normalize_rating(test_description, review=True)
print(f"Normalized: {normalized}")
print(f"Metadata: {metadata}")
print(f"Review: {review}")
print(f"Match type: {match_type}")
