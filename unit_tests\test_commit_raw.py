from src.atom import dbManager
import pandas as pd
import numpy as np

# Create an instance of DatabaseManager
db_manager = dbManager.DatabaseManager()

excel_filepath = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Data\dev\test_raw_data.xlsx"

# Read the Excel file
df = pd.read_excel(excel_filepath)

# Clean up NaN values
# Replace NaN with appropriate values based on data type
df = df.replace({np.nan: None})  # Replace NaN with None for SQLite compatibility
df = df.replace({'nan': None})   # Replace string 'nan' with None
df = df.replace({'': None})      # Replace empty strings with None

# Convert specific columns if needed, for example:
if 'pdf_id' in df.columns:
    df['pdf_id'] = pd.to_numeric(df['pdf_id'], errors='coerce').fillna(0).astype(int)
if 'pdf_page' in df.columns:
    df['pdf_page'] = pd.to_numeric(df['pdf_page'], errors='coerce').fillna(0).astype(int)
if 'sys_layout_valid' in df.columns:
    df['sys_layout_valid'] = pd.to_numeric(df['sys_layout_valid'], errors='coerce').fillna(0).astype(int)

# Add this before the insert to verify the table exists
with db_manager.connect() as conn:
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='raw_data';")
    result = cursor.fetchone()
    print("Table exists:", result is not None)

# Insert the cleaned DataFrame into the database
db_manager.insert_dataframe(df, "raw_data")

with db_manager.connect() as conn:
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM raw_data")
    count = cursor.fetchone()[0]
    print(f"Row count in raw_data table: {count}")
