#multiprocessing
#_worker.py
import platform, io
from http.client import PARTIAL_CONTENT
import sys, os, re, json, argparse
import multiprocessing
#from fitz.extra import page_count
import pandas as pd
import fitz  # PyMuPDF fitz uses points coordinate system. 
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import time
import uuid
import threading
import numpy as np
from fractions import Fraction
#from PyQt5.QtWidgets import QApplication, QMessageBox
from PySide6.QtWidgets import QApplication, QMessageBox


from .extract_tables import get_table_data, get_table_data_2, create_logical_structure_1, create_logical_structure_2, merge_wrapped_rows_1, merge_wrapped_rows_2
from .ai_processing import Gpt4Turbo, analyze_bom_data
from .dbManager import DatabaseManager

# This logger will inherit configurations from the root logger configured in main.py
logger = logging.getLogger(__name__)

# Temporarily set the logger level to INFO
logger.setLevel(logging.INFO)

# Create a console handler (if you haven't already)
console_handler = logging.StreamHandler()

# Optionally, set a formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(console_handler)

# Set to limit the number of workers/processes for debugging
is_testing = False

# Check if the operating system is Windows
if platform.system() == 'Windows':
    # Import launch_window only if on Windows
    logger.debug("Windows OS Detected. --Importing launch_window.py")
    try:
        from src.atom.dynamic_rename import launch_window
    except:
        from dynamic_rename import launch_window  # Older import
        
#PyMuPDF examples
'''
#https://artifex.com/blog/text-extraction-with-pymupdf
#https://artifex.com/blog/table-recognition-extraction-from-pdfs-pymupdf-python
#https://pymupdf.readthedocs.io/en/latest/recipes-text.html#how-to-extract-table-content-from-documents
https://pymupdf.readthedocs.io/en/latest/page.html#Page.find_tables
'''

class DataFrameHolder:
    def __init__(self):
        self.modified_df = None
        self.callback_received = threading.Event()

    def handle_modified_dataframe(self, df):
        self.modified_df = df
        logger.debug("Modified DataFrame received:")
        self.callback_received.set()  # Signal that the callback has been executed

    def wait_for_callback(self):
        self.callback_received.wait()  # Wait for the callback to be executed
        
def confirm_analysis():
    app = QApplication.instance()  # Check if there is already a running QApplication instance
    created_app = False
    if not app:  # If no instance exists, create a new one
        app = QApplication([])
        created_app = True
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle("Confirm Analysis")
    msg_box.setText("Do you want to run smart analysis on your extracted \"Bill of Materials\"?")
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)
    #response = msg_box.exec_()
    response = msg_box.exec()

    if created_app:
        app.exit()  # Properly terminate the app you created

    return response == QMessageBox.Yes
        
def parse_elevation(elevation_str):
    try:
        # Remove 'E' or 'EL' prefix, any trailing double quotes, and the sign
        elevation_str = re.sub(r'[Ee][Ll]?|\s*\"', '', elevation_str).strip()
        sign = -1 if elevation_str.startswith('-') else 1
        elevation_str = elevation_str.lstrip('+-')

        # Split the string by the foot and inch separator ("'")
        parts = elevation_str.split("'")

        # Parse the feet and inch parts
        feet = int(parts[0]) if parts[0] else 0
        inches = 0
        fraction = 0

        # If there is an inches part, it might contain a whole number and a fractional part
        if len(parts) > 1:
            inch_parts = parts[1].split('.')
            inches = int(inch_parts[0]) if inch_parts[0] else 0

            # If there's a fractional part, convert it to a decimal
            if len(inch_parts) > 1:
                fraction = float(Fraction(inch_parts[1]))

        # Calculate total inches and convert to decimal feet
        total_inches = feet * 12 + inches + fraction
        decimal_feet = total_inches / 12
        return sign * decimal_feet
    except Exception as e:
        # Handle any unexpected error during the parsing
        logger.warning(f"Error parsing elevation: {elevation_str} - {e}")
        return float('nan')  # Return NaN if there's an error

def calculate_elevation_metrics(row):
    elevation_strings = row['elevation'].split(';')
    elevations = [parse_elevation(e.strip()) for e in elevation_strings if e.strip()]

    # Calculate min, max, and average elevations in decimal feet
    min_elevation = f"{min(elevations):.2f}" if elevations else '' #None
    max_elevation = f"{max(elevations):.2f}" if elevations else '' #None
    avg_elevation = f"{sum(elevations) / len(elevations):.2f}" if elevations else ''#None


    return min_elevation, max_elevation, avg_elevation

def trim_all_columns(df):
    """
    Trims leading and trailing whitespaces from all string values in all columns of the DataFrame.
    """
    trim_strings = lambda x: x.strip() if isinstance(x, str) else x
    return df.map(trim_strings) #return df.applymap(trim_strings)

# Function to trim all columns in a DataFrame
def trim_all_columns2(df):
    return df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

def remove_outliers_from_bom(bom_data, outlier_data):
    for outlier in outlier_data:
        # Extract identifiers from the outlier row
        outlier_text = outlier.get("Text", "")
        outlier_page = outlier.get("PdfPage", "")
        outlier_path = outlier.get("PdfPath", "")

        # Create a condition to match the relevant rows in the BoM data
        bom_data = [row for row in bom_data if not (
            row.get("PdfPage", "") == outlier_page and 
            row.get("PdfPath", "") == outlier_path and 
            (outlier_text in row.get("Text", "") or row.get("Text", "") in outlier_text)
        )]

    return bom_data

def get_correct_path(path):
    logger.debug("Checking OS")
    if os.name != 'nt':
        # Convert Windows-style paths to Linux-style paths
        # This assumes that the path starts with a Windows drive letter (e.g., C:/)
        path = re.sub(r'^[a-zA-Z]:', '', path)  # Remove drive letter
        path = path.replace('\\', '/')  # Replace backslashes with forward slashes
        path = '/app' + path  # Prefix with the base directory used in Docker
    return path

# Function to split the PDF into individual pages with unique temporary IDs
def split_pdf(pdf_path, output_dir, dataframe, page_limit=None):
    logger.debug("Splitting PDF Pages")
    with fitz.open(pdf_path) as doc:
        max_page = range(len(doc)) if page_limit is None else range(min(page_limit, len(doc)))
        
        # Generate unique IDs only for the pages being processed
        unique_ids = [str(uuid.uuid4()) for _ in max_page]
        dataframe = dataframe.iloc[list(max_page)].copy()
        dataframe['sys_AISdocID'] = unique_ids

        for page_num in max_page:
            page = doc.load_page(page_num)
            page_pdf = fitz.open()
            page_pdf.insert_pdf(doc, from_page=page_num, to_page=page_num)
            
            # Use the unique ID from the dataframe for the filename
            unique_id = dataframe.iloc[page_num - max_page.start]['sys_AISdocID']
            filename = f"{unique_id}.pdf"
            page_pdf.save(os.path.join(output_dir, filename))
            page_pdf.close()
    
    return dataframe
            
def convert_tuple_to_string(row):
    return [str(item) if isinstance(item, tuple) else item for item in row]

def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None

def export_large_data_to_excel(df, filename, directory):
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

    excel_filename = os.path.join(directory, filename)

    if len(df) > 0:
        logger.debug(f"Creating Workbook: {filename} in {directory} with {len(df)} rows")
        wb = Workbook()
        ws = wb.active

        # Adding header row (column names)
        ws.append(list(df.columns))

        ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
        hyperlink_font = Font(color="0000FF", underline="single")

        for index, row in df.iterrows():
            row_values = [str(value) if not is_scalar(value) else value for value in row]
            ws.append(row_values)

            # Create a hyperlink for 'AIS_LINK'
            if ais_link_col_idx:
                ais_link_value = row['AIS_LINK']
                
                # Use the relative path directly
                if ais_link_value:
                    hyperlink = ais_link_value
                    cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                    cell.hyperlink = hyperlink
                    cell.font = hyperlink_font

        try:
            wb.save(excel_filename)
            logger.debug(f">>> Data exported and saved to: {excel_filename}")
        except PermissionError as e:
            logger.error(f"PermissionError: {e}")
            logger.error(f"Failed to write to {excel_filename}. The file might be open or locked.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
    else:
        logger.debug(f"The DataFrame for {filename} is empty.")
    
def load_json(file_path):
    try:
        with open(file_path, 'r') as file:
            logger.info("Loaded Json: %s", file_path)
            return json.load(file)
        
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {file_path}: {e}")
        return None  # or return an empty dict {}
    except FileNotFoundError as e:
        logger.error(f"File not found: {file_path}: {e}")
        return None  # or return an empty dict {}
    
def update_value_type(value, patterns):
    for category, category_patterns in patterns.items():
        for pattern in category_patterns:
            if re.match(pattern, value):
                return category, value
    return '', ''

def string_to_tuple(coords):
    if isinstance(coords, tuple):
        # It's already a tuple, return it as is
        return coords
    try:
        # Assuming the input is a string that needs to be converted to a tuple
        coords = coords.strip("() ")
        return tuple(map(float, coords.split(', ')))
    except (ValueError, SyntaxError):
        # Return a default value that indicates invalid coordinates
        return (0, 0, 0, 0)
    
def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):
    #logger.info("\n\n ----convert_relative_coords_to_points accessed\n")
    
    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ROI payload: {e}")
        return []

    converted_payload = []
    
    for item in roi_payload:
        try:
            converted_item = {"columnName": item.get("columnName", "Unknown")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            converted_payload.append(converted_item)
        except Exception as e:
            logger.error(f"Error processing item {item.get('columnName', 'Unknown')}: {e}")

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))
        
def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }

def calculate_overlap_area(box1, box2):
    # Calculate the overlapping area between two rectangles
    x_left = max(box1[0], box2[0])
    y_top = max(box1[1], box2[1])
    x_right = min(box1[2], box2[2])
    y_bottom = min(box1[3], box2[3])

    if x_right < x_left or y_bottom < y_top:
        return 0.0  # No overlap
    return (x_right - x_left) * (y_bottom - y_top)

def draw_coordinates_on_page(document, page_number, coordinates_list, sub_dir, x_offset=0, y_offset=0):
    try:
        page = document.load_page(page_number)
        rect = page.rect  # Get the page dimensions
        page_width = rect.width
        page_height = rect.height
    except Exception as e:
        logger.error(f"Failed to load page {page_number}: {e}")
        return None

    for coordinates in coordinates_list:
        if "tableCoordinates" in coordinates:
            logger.debug("Drawing table coordinates")
            # Convert and draw table rectangle using page dimensions
            draw_rectangle(page, coordinates["tableCoordinates"], page_width, page_height, x_offset, y_offset)

            # Draw rectangles for each column in the table
            for column_dict in coordinates.get("tableColumns", []):
                for column_name, column_coords in column_dict.items():
                    logger.debug(f"Drawing column: {column_name}")
                    # Convert and draw column rectangle using page dimensions
                    draw_rectangle(page, column_coords, page_width, page_height, x_offset, y_offset)
        else:
            # It's a single coordinate entry, not a table
            #logger.debug(f"Drawing coordinate: {coordinates.get('columnName')}")
            # Convert and draw single rectangle using page dimensions
            draw_rectangle(page, coordinates, page_width, page_height, x_offset, y_offset)

    # Save the page as an image for inspection
    try:
        image = page.get_pixmap()
        image_path = os.path.join(sub_dir, 'validate.png')
        image.save(image_path)
        logger.info(f"Image saved to {image_path}")
        return image_path
    except Exception as e:
        logger.error(f"Error saving the image: {e}")
        return None

def draw_rectangle(page, coordinates, page_width, page_height, x_offset=0, y_offset=0):
    try:
        # Scale the relative coordinates to the page dimensions
        x0 = (coordinates["relativeX0"] * page_width) + x_offset
        y0 = (coordinates["relativeY0"] * page_height) + y_offset
        x1 = (coordinates["relativeX1"] * page_width) + x_offset
        y1 = (coordinates["relativeY1"] * page_height) + y_offset

        rect = fitz.Rect(x0, y0, x1, y1)
        page.draw_rect(rect, color=(1, 0, 0), width=1.5)  # Draw red rectangle
        #logger.debug(f"Rectangle drawn with coordinates: {x0}, {y0}, {x1}, {y1}")
    except KeyError as e:
        logger.error(f"Missing coordinate key in {coordinates}: {e}")
    except Exception as e:
        logger.error(f"Error drawing rectangle: {e}")

def worker(pdf_path, start_page, end_page, roi_payload, value_patterns, columns_to_aggregate, 
           columns_to_keep, sub_dir, x_offset, y_offset, overlap_threshold, project_id, validate_string=None):
    data = []
    combined_raw_df = pd.DataFrame()
    combined_bom_df = pd.DataFrame()
    combined_annot_df = pd.DataFrame()
    combined_weld_df = pd.DataFrame()
    combined_outlier_df = pd.DataFrame()
    combined_annot_outlier_df = pd.DataFrame()
    outliers_df = pd.DataFrame()
    document = fitz.open(pdf_path)
    bom_df = pd.DataFrame()
    annot_df = pd.DataFrame()
    logger.debug(f"Worker started for pages {start_page} to {end_page}")

    #validate_string = "LINDE AG - LE DIVISION"
    
    #if db_manager is None:
    db_manager = DatabaseManager('ais_database.db')
            
    # Extracting the filename
    filename = os.path.basename(pdf_path)

    # Extracting the directory path
    directory_path = os.path.dirname(pdf_path)

    # Splitting the directory path into a list of parent folders
    parent_folders = directory_path.split(os.sep)
    parent_folders_str = str(parent_folders)
   
    rect = document.load_page(0).rect
    page_width = rect.width
    page_height = rect.height
    
    #Convert relative coordinates
    converted_roi_payload = convert_relative_coords_to_points(roi_payload, page_width, page_height, x_offset, y_offset)

    for page_number in range(start_page, end_page):
        try:
            page = document.load_page(page_number)
            
            # Store the PDF Page
            ##########################################
            # Initialize a binary stream to hold the PDF page
            pdf_stream = io.BytesIO()

            # Create a new PDF with just this page and save to the stream
            single_page_doc = fitz.open()  # Create a new empty PDF
            single_page_doc.insert_pdf(document, from_page=page_number, to_page=page_number)  # Insert the current page
            single_page_doc.save(pdf_stream)  # Save the single-page document to the stream
            single_page_doc.close()  # Close the single-page document

            # Convert the stream's content to binary data
            pdf_bytes = pdf_stream.getvalue()

            # Proceed to store `pdf_bytes` in your database
            document_name = f"{str(uuid.uuid4())}.pdf"  # Unique name for the stored PDF page
            pdfID = db_manager.insert_pdf_page_to_storage(project_id, page_number + 1, "Isometric", document_name, pdf_bytes)
            ##########################################

        except Exception as e:
            logger.error(f"Error loading page {page_number}: {e}")
            continue  # Skip this page if it can't be loaded
        
        # Reset page_validated
        page_validated = False
        
        #Get BOM table data \\\\\\\BOM TABLE LOGIC//////// bom_df=Text, annot_df=Annotations
        try:
            bom_df, annot_df, outliers_df, annot_outlier_df = get_table_data(pdf_path, page, page_number, converted_roi_payload)   
            
            # Insert the pdfID(db pdf_id into each DataFrame
            if bom_df is not None and not bom_df.empty:
                bom_df['pdfID'] = pdfID
            if annot_df is not None and not annot_df.empty:
                annot_df['pdfID'] = pdfID
            if outliers_df is not None and not outliers_df.empty:
                outliers_df['pdfID'] = pdfID
            if annot_outlier_df is not None and not annot_outlier_df.empty:
                annot_outlier_df['pdfID'] = pdfID
            
            # Try combining ANNOTATION TABLE DATA
            try:
                if bom_df is not None and not bom_df.empty:
                    combined_bom_df = pd.concat([combined_bom_df, bom_df], ignore_index=True)
                    
                    try:
                        if outliers_df is not None and not outliers_df.empty:
                            combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)
                            
                    except Exception as e:
                        logger.error(f"An error occurred concatting combined_outliers_df: {e}")
                        
            except Exception as e:
                logger.error(f"An error occurred concatting combined_bom_df: {e}")
                
            # Try combining ANNOTATION TABLE DATA
            try:
                if len(annot_df) > 0:
                    combined_annot_df = pd.concat([combined_annot_df, annot_df], ignore_index=True)
                    
                    try:
                        if annot_outlier_df is not None and not annot_outlier_df.empty:
                            combined_annot_outlier_df = pd.concat([combined_annot_outlier_df, annot_outlier_df], ignore_index=True)
                    except Exception as e:
                        logger.error(f"An error occurred concatting combined_annot_outlier_df: {e}")
            except Exception as e:
                logger.error(f"An error occurred concatting combined_annot_df: {e}")
            
            #logger.debug(f"BOM Processing completed successfully for page {page_number}")
        except Exception as e:
            #logger.error(f"An error occurred in get_table_data_2: {e}")
            logger.error(f"Error getting table data on page {page_number}: {e}")   

        '''            
        Extract text along with its bounding box (coordinates)
        Coordinate (bbox format):(x1, y1, x2, y2)
        Where x0, y0 is top-left corner of rectangle bounding box
        and x1, y1 are the coordinates of the lower-right corner of the rectangle bounding the text.
        '''
        text_blocks = page.get_text("dict")["blocks"]
        
        #Get text data \\\\\\\GENERAL DATA TEXT EXTRACTION LOGIC////////
        logger.debug(f"Extracting TEXT/ANNOTATION Data: {page_number}")
        for block in text_blocks:
            if block["type"] == 0:  # 0 for text block, 1 for image block
                try:
                    bbox1 = block["bbox"]
                    for line in block["lines"]:
                        for span in line["spans"]:
                            #text = span.get("text", "").strip()
                            text = span.get("text", "")
                            #print("Text: ", text)
                            if text:
                                # Using regex to remove leading and trailing whitespace
                                text = re.sub(r'^\s+|\s+$', '', text)

                            bbox2 = span.get("bbox", (0, 0, 0, 0))  # Use the bbox of the span for individual coordinates
                            if text: # Add text to data only if it's not empty
                                if validate_string and page_validated ==False:
                                    if validate_string.replace(" ", "") in text.replace(" ", ""):
                                        #print("Page Validated")
                                        page_validated = True
                            
                                #Evaluate value with regex
                                category, extracted_value = update_value_type(text, value_patterns)
                                        
                                data.append({
                                    'sys_build': parent_folders_str,
                                    'sys_path': pdf_path,
                                    'sys_filename': filename,
                                    'pdfID': pdfID,
                                    'pdf_page': page_number + 1,
                                    'sys_layout_valid': page_validated,
                                    'type': 'Text',
                                    'category': category,
                                    'value': text, #text.strip(),
                                    'elevation': extracted_value if category == 'elevation' else '',
                                    'X Position': extracted_value if category == 'x_coordinate' else '',
                                    'Y Position': extracted_value if category == 'y_coordinate' else '',
                                    'coordinates': bbox1, #The main bounding box
                                    'coordinates2': bbox2, #Precise Bounding Box for individual value
                                    'name': '',
                                    'title': '',
                                    'createdDate': '',
                                    'modDate': '',
                                    'id': '',
                                })
                except Exception as e:
                    logger.error(f"Error processing text block on page {page_number}: {e}")
                    
                #logger.debug(f">>> Processed Text Data type(0): {page_number}")               
            #Handle Images
            elif block["type"] == 1:  # Check if block is an image
                try:
                    #print("/nBLOCK TYPE 1")
                    image_bbox = block["bbox"]
                    xref = block["image"]  # xref number of the image

                    # Ensure xref is an integer
                    if not isinstance(xref, int):
                        #print(f"Warning: Expected integer for xref, got {type(xref)} with value {xref}")
                        continue  # Skip to next block

                    # Extract and save the image
                    try:
                        image_info = document.extract_image(xref)
                        if image_info is not None:
                            image_bytes = image_info["image"]
                            image_filename = f"image_{page_number}_{xref}.png"
                            image_filepath = os.path.join(r"C:\Users\<USER>\AIS_work_dir\Test Integration\Test1", image_filename) ###Fix this Absolute Path
            
                            with open(image_filepath, "wb") as img_file:
                                img_file.write(image_bytes)

                            # Append image information to data
                            data.append({
                                'sys_build': parent_folders_str,
                                'sys_path': pdf_path,
                                'sys_filename': filename,
                                'pdfID': pdfID,
                                'pdf_page': page_number + 1,
                                'type': 'Image',
                                'image_xref': xref,
                                'coordinates': image_bbox,
                                'image_path': image_filepath
                            })
                        #logger.debug(f">>> Processed Text Type (1)(Images): {page_number}")      
                    except Exception as e:
                        logger.error(f"Error extracting image at xref") #{xref}: {e}")
                except Exception as e:
                    logger.error(f"Error processing image block on page {page_number}: {e}")
                   
        # Extract annotations along with their coordinates \\\\\\\GENERAL DATA ANNOTATION EXTRACTION LOGIC////////
        try:
            annotations = page.annots()
            if annotations:
                for annot in annotations:
                    annot_info = annot.info
                    annot_rect = annot.rect
                    value = annot_info.get('content', '').strip()

                    #Evaluate value with regex
                    category, extracted_value = update_value_type(value, value_patterns)
                    #elevation, category = update_if_elevation(value, elevation_patterns)

                    data.append({
                        'sys_build': parent_folders_str,
                        'sys_path': pdf_path,
                        'sys_filename': filename,
                        'pdfID': pdfID,
                        'pdf_page': page_number + 1,
                        'sys_layout_valid':"",
                        'type': annot_info.get('subject', ''),
                        'category': category,
                        'value': value,
                        'elevation': extracted_value if category == 'elevation' else '',
                        'X Position': extracted_value if category == 'x_coordinate' else '',
                        'Y Position': extracted_value if category == 'y_coordinate' else '',
                        'coordinates': (annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
                        'coordinates2': "",
                        'name': annot_info.get('name', ''),
                        'title': annot_info.get('title', ''),
                        'createdDate': annot_info.get('creationDate', ''),
                        'modDate': annot_info.get('creationDate', ''),
                        'id': annot_info.get('id', ''),
                    })
                    
            #logger.debug(f">>> Processed Annotations Data: {page_number}")
        except Exception as e:
            logger.error(f"Error processing annotations on page {page_number}: {e}")
               
    #logger.debug(f"Completed Processing TEXT/ANNOTATION/IMAGE Data: {start_page} to {page_number}")   
    document.close()
    
    try:
        raw_data_df = pd.DataFrame(data)   
    except Exception as e:
        logger.error(f"Error assigning Raw Data to Dataframe: {e}")
        
    # Only combine if raw_data_df has one or more rows
    if not raw_data_df.empty:
        combined_raw_df = pd.concat([combined_raw_df, raw_data_df], ignore_index=True)

        weld_df = consolidate_lines_with_multiple_ranges(combined_raw_df, converted_roi_payload, columns_to_aggregate, columns_to_keep, overlap_threshold)
        
        # Only combine if weld_df has one or more rows
        if not weld_df.empty:
            combined_weld_df = pd.concat([combined_weld_df, weld_df], ignore_index=True)
             
    if combined_raw_df is None:
        print("combined_raw_df is None")
    if combined_weld_df is None:
        print("combined_weld_df is None")
    if combined_bom_df is None:
        print("combined_bom_df is None")
    if combined_outlier_df is None:
        print("combined_outlier_df is None")
        
    # Check the overall length of boM DataFrames and apply the specified logic
    if len(combined_bom_df) == 0 and len(combined_annot_df) == 0:
        # Both DataFrames have no rows, do nothing
        logger.debug("BOM: TEXT/ANNOTS ARE BLANK FOR")
    elif len(combined_bom_df) > 0 and len(combined_annot_df) == 0:
        # combined_bom_df has rows and combined_annot_df has 0 rows, keep combined_bom_df as is
        logger.debug("BOM: USING TEXT DATA")
    elif len(combined_annot_df) > 0 and len(combined_bom_df) == 0:
        # combined_annot_df has rows and combined_bom_df has 0 rows, set combined_bom_df = combined_annot_df
        combined_bom_df = combined_annot_df
        logger.debug("BOM: USING ANNOT DATA")
    elif len(combined_bom_df) > 0 and len(combined_annot_df) > 0:
        # Both have rows, use the one with the greatest number of rows
        if len(combined_bom_df) > len(combined_annot_df):
            logger.debug("BOM: USING TEXT DATA. -- TEXT ROWS > ANNOT ROWS ---HANDLE THIS CASE")
        elif len(combined_bom_df) < len(combined_annot_df):
            combined_bom_df = combined_annot_df
            logger.debug("BOM: USING ANNOT DATA. -- ANNOT ROWS > TEXT ROWS ---HANDLE THIS CASE")
        else:
            logger.debug("BOM: Both DataFrames have the same number of rows. Defaulting to using TEXT DATA.")

    return combined_raw_df, combined_weld_df, combined_bom_df, combined_outlier_df

def consolidate_lines_with_multiple_ranges(df, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold=0):
    logger.debug("Starting consolidation process")
    
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = 10
    
    if df.empty:
        logger.warning("Dataframe is empty. Exiting consolidation process.")
        return pd.DataFrame()
    
    try:
        # Convert the string representation of coordinates to actual tuples
        df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
        df['coordinates'] = df['coordinates'].apply(string_to_tuple)
        
        # Update 'coordinates2' if it's (0,0,0,0). Annotations are stored in 'coordinates'
        df.loc[df['coordinates2'] == (0, 0, 0, 0), 'coordinates2'] = df['coordinates']
    except Exception as e:
        logger.error(f"Error converting string to tuple: {e}")
        return pd.DataFrame()
    
    try:
        # Initialize a dictionary to store annotations by type, excluding 'Text'
        annotation_data = {}
        for annotation_type in df['type'].unique():
            if annotation_type != 'Text':  # Skip 'Text' annotations
                # Extract relevant annotations for this type
                temp_df = df[df['type'] == annotation_type][['sys_path', 'pdf_page', 'value']].copy()

                # Store these annotations in the dictionary under the current annotation type
                annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page'])['value'].apply(list).reset_index()

        # Create 'weld_df' from 'df' without 'Text' annotations and without initial filtering
        weld_df = df[df['type'] != 'Text'][['sys_path', 'pdf_page']].drop_duplicates().reset_index(drop=True)

        # Now, let's integrate the annotation data into 'weld_df'
        # We aim to create a structure where each 'sys_path' and 'pdf_page' has a dictionary of annotations by type
        for index, row in weld_df.iterrows():
            # For each row in 'weld_df', compile annotations from 'annotation_data' that match 'sys_path' and 'pdf_page'
            annotations_for_row = {}
            for annotation_type, annotations_df in annotation_data.items():
                matching_annotations = annotations_df[
                    (annotations_df['sys_path'] == row['sys_path']) & 
                    (annotations_df['pdf_page'] == row['pdf_page'])
                ]['value'].tolist()

                if matching_annotations:
                    # Flatten the list if it contains another list
                    if matching_annotations and isinstance(matching_annotations[0], list):
                        matching_annotations = [item for sublist in matching_annotations for item in sublist]
            
                    annotations_for_row[annotation_type] = matching_annotations

            # Update the 'Weld ID' column with formatted annotation data for this row
            weld_df.at[index, 'Weld ID'] = str(annotations_for_row)
        
    except Exception as e:
        #logger.error(f"Error separating WELD CALLOUT rows: {e}")
        logger.error(f"Error integrating annotation data: {e}")
        return pd.DataFrame()

    # Consider a small epsilon for float comparison inaccuracy
    epsilon = 0.001
    
    # Group by file path and page number
    grouped = df.groupby(['sys_path', 'pdf_page'])
    
    # Initialize a set to hold all column names from JSON
    json_column_names = set()
    for item in converted_roi:
        try:
            json_column_names.add(item['columnName'])
            
            # Check if 'tableColumns' is present in the item
            if 'tableColumns' in item:
                # Iterate through each column in 'tableColumns'
                for column_dict in item['tableColumns']:
                    # Each column_dict has the column name as the key
                    for column_name in column_dict.keys():
                        # Add the column name to the set of JSON column names
                        json_column_names.add(column_name)

        except Exception as e:
            logger.error(f"Error processing converted_roi item: {e} ---{item}")

    # Create a list to hold the consolidated data for each group
    consolidated_data_list = []
    
    try:
        # Initialize a template dictionary with columns based on the first page
        template_data = {col: '' for col in columns_to_aggregate if col in df.columns}
    except Exception as e:
        logger.error(f"Error intializing template fields: {e}")
        
    try:
        # Process each group
        for (filepath, page), group in grouped:
            #if all(group['sys_layout_valid']):
            # Initialize consolidated_data with JSON column names
            consolidated_data = {col: set() for col in json_column_names}
            # Initialize consolidated_data
            consolidated_data = {col: set() for col in columns_to_aggregate if col in group.columns}
            consolidated_data.update({col: group[col].iloc[0] for col in columns_to_keep if col in group.columns})
        
            # Check if any 'sys_layout_valid' value in the group is True
            if any(group['sys_layout_valid']):
                page_valid = True
                consolidated_data['sys_layout_valid'] = True
    
            # Apply logic for each coordinate range in JSON
            for index, row in group.iterrows():
                for col in columns_to_aggregate:
                    if row[col] and row[col].strip():
                        consolidated_data[col].add(row[col].strip())
                        text_box = string_to_tuple(row['coordinates2'])  # Assuming this gives the text bounding box
      
                for item in converted_roi:
                
                    try:
                        if item['columnName'].lower() == 'bom':
                            continue
   
                            # Calculate overlap for other items
                            item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
                            overlap_area = calculate_overlap_area(text_box, item_box)
                            text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
                            overlap_ratio = overlap_area / text_area if text_area else 0

                            if overlap_ratio >= overlap_threshold:
                                check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold)
                            ######
                    
                        if 'tableColumns' in item:
                            for col in item['tableColumns']:
                                check_and_update_data(row, col, consolidated_data, epsilon, overlap_threshold)
                        else:
                            #print("/n/nERROR PASSING ITEM ON ELSE")
                            check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold)
                    except Exception as e:
                        logger.error(f"Error analyzing field {item['columnName']}: {e}")
                   
            try:
                # Convert sets to strings, joining unique values with '; '
                for col in columns_to_aggregate:
                    consolidated_data[col] = '; '.join(consolidated_data[col])
                    
            except Exception as e:
                logger.error(f"Error consolidating columns_to_aggregate: {e}")
        
            # Add the consolidated data dictionary to the list
            consolidated_data_list.append(consolidated_data)
            
        # else:
        #    # Print which pages are skipped when 'sys_layout_valid' is False
        #     print(f"Skipped: filepath='{filepath}' (missing sys_path), page={page} (missing pdf_page)")
    except Exception as e:
        logger.error(f"Error processing group {filepath}, {page}: {e}")
    try:  
        # Create a new DataFrame from the consolidated data list
        consolidated_df = pd.DataFrame(consolidated_data_list)

        # Aggregate 'Weld ID' values into a comma-delimited string for each 'sys_path', 'pdf_page'
        weld_df = weld_df.groupby(['sys_path', 'pdf_page'])['Weld ID'].apply(lambda x: ', '.join(x)).reset_index()
        #print("\nWELD DF:\n", weld_df)
        final_df = pd.merge(consolidated_df, weld_df, on=['sys_path', 'pdf_page'], how='outer')
    except Exception as e:
        logger.error(f"Error creating final DataFrame: {e}")
        return pd.DataFrame()

    logger.debug("Consolidation process completed successfully")
    
    # print("\n\n\n>>>>>CONSOLIDATED DATAFRAME:\n", final_df,"\n>>>>>>>>>\n\n")
    #print("\n\n\n>>>>>CONSOLIDATED DATAFRAME COLUMNS:\n", final_df.columns,"\n>>>>>>>>>\n\n")
    return final_df

def check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold=0):
    coords = row['coordinates2']
    item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
    text_box = (coords[0], coords[1], coords[2], coords[3])

    # Calculate overlap area
    overlap_area = calculate_overlap_area(text_box, item_box)
    text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
    overlap_ratio = overlap_area / text_area if text_area else 0

    if overlap_ratio >= overlap_threshold:
        # If the overlap is sufficient, update consolidated data
        consolidated_data[item['columnName']] = consolidated_data.get(item['columnName'], '') + ' ' + row['value'].strip()
        # Additional check for 'drawing' column
        # if item['columnName'] == 'insulation_thickness':
        #     print(f"'insulation_thickness' column contents: {consolidated_data['insulation_thickness']}")
        
    # """
    # Check if the row's coordinates fall within the given item's coordinates and update consolidated data.
    # """
    # coords = row['coordinates2']
    
    # ##print(f"/nCoords: /n{type(coords)}/n{coords}")
    # ##print(f"/nitem: /n{type(item)}/n{item}")
    
    # x0, y0, x1, y1 = item['x0'], item['y0'], item['x1'], item['y1']
    # if (x0 - epsilon <= coords[0] <= x1 + epsilon and
    #     y0 - epsilon <= coords[1] <= y1 + epsilon and
    #     x0 - epsilon <= coords[2] <= x1 + epsilon and
    #     y0 - epsilon <= coords[3] <= y1 + epsilon):
    #     consolidated_data[item['columnName']] = consolidated_data.get(item['columnName'], '') + ' ' + row['value'].strip()
        
def run_multiprocessing(pdf_path, roi_payload, value_patterns, payload_options, sub_dir, x_offset, y_offset, overlap_threshold, project_id, debug_page_limit=None):
    run_extract_dir = False #Change to True to run extraction for full directory
    
    ##initialize blank dataframes
    all_data = []
    combined_bom_df = pd.DataFrame()
    combined_raw_df = pd.DataFrame()
    combined_general_df = pd.DataFrame()
    combined_outlier_df = pd.DataFrame()
    outliers_df = pd.DataFrame()

    ################################
    bom_df = pd.DataFrame()
    
    
    # Access elements in the dictionary
    columns_to_aggregate = payload_options.get('aggregate_cols_general', [])
    columns_to_keep = payload_options.get('keep_cols_general', [])
    columns_to_keep.append('sys_layout_valid')
    
    #num_processes = multiprocessing.cpu_count()    
    document = fitz.open(pdf_path)
    
     # Decide on the number of processes
    if is_testing:
        num_processes = 1  # Use a small number for testing
    else:
        num_processes = multiprocessing.cpu_count()  # Use all available CPUs for production
        
    logger.info(f"Using {num_processes}...")
        
    # Call the function to draw the coordinates
    logger.info("Drawing page coordinates for validation.")
    image_path = draw_coordinates_on_page(document, 0, roi_payload, sub_dir, x_offset, y_offset)

    total_pages = min(len(document), debug_page_limit) if debug_page_limit is not None else len(document)
    document.close()
    pages_per_chunk = total_pages // num_processes
    pages_per_worker = [(i * pages_per_chunk, min((i + 1) * pages_per_chunk, total_pages)) for i in range(num_processes)]
    
    print("\n\n---------------------DEBUG PAGE LIMIT: ", debug_page_limit)
    print("\n\n---------------------Document: ", total_pages)
    
    #Track job progress
    progress_increment = total_pages // 10  # 10% of total pages
    pages_processed = 0
    
    logger.info(f"Starting multiprocessing with {num_processes} processes for {total_pages} pages")

    # Creating a pool of worker processes
    with multiprocessing.Pool(processes=num_processes) as pool:
        results = [pool.apply_async(worker, args=(pdf_path, 
                i * pages_per_chunk, 
                (i + 1) * pages_per_chunk if i != num_processes - 1 else total_pages,
                roi_payload, 
                value_patterns, 
                columns_to_aggregate, 
                columns_to_keep, 
                sub_dir,
                x_offset,
                y_offset, 
                overlap_threshold,
                project_id)) 
                for i in range(num_processes)]

        for i, (start_page, end_page) in enumerate(pages_per_worker):
            result_content = None
            try:
                result = results[i].get()
                # Before unpacking, log the type and length of the result to check its structure
                logger.debug(f"Iteration {i}: Received result of type {type(result)} with length {len(result)}",exc_info=True)
                result_content = result  # Keep the content for potential logging after exception

                # Attempt to unpack all four expected items
                raw_data_df, weld_df, bom_df, outliers_df = result

            except Exception as e:
                # Log detailed error including the unexpected result's structure and partial content
                logger.error(f"Warning: Unexpected result format in iteration {i}: {e}", exc_info=True)
                #logger.error(f"Result content (partial): {result_content[:3]}")  # Log the first few items to avoid overwhelming logs

                # Since the 'continue' statement prevents further execution, handle missing data here if needed
                try:
                    # Attempt a partial unpack to handle whatever is available
                    raw_data_df, weld_df, bom_df = result[:3]
                except Exception as unpacking_error:
                    logger.error(f"Error unpacking partial results in iteration {i}: {unpacking_error}")
                    # Initialize dataframes to empty ones if unpacking fails
                    raw_data_df, weld_df, bom_df = pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

################################################################  
################################################################  
################################################################  

            all_data.extend(raw_data_df)
            
            combined_bom_df = pd.concat([combined_bom_df, bom_df], ignore_index=True)
            combined_raw_df = pd.concat([combined_raw_df, raw_data_df], ignore_index=True)
            combined_general_df = pd.concat([combined_general_df, weld_df], ignore_index=True)
            combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)

            # Update pages processed and check for progress
            pages_processed += (end_page - start_page)
            if pages_processed >= progress_increment:
                logger.info(f"Progress: {pages_processed / total_pages * 100:.2f}% completed")
                progress_increment += total_pages // 10
                
        logger.info("Progress: 100.00% completed")
    return combined_bom_df, combined_raw_df, combined_general_df, combined_outlier_df, total_pages
  
def print_metrics(total_pages, extract_time):
    pages_per_second_app = (total_pages)  / extract_time if extract_time > 0 else 0
    # logger.info(f"Current App Performance: Processes {pages_per_second_app} pages/second")
    print("\n\n-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print(f"Current App Performance: Processes {pages_per_second_app} pages/second")
    # # Given values
    drawings_per_hour_person = 50

    # Converting drawings per hour to drawings per second for a person
    drawings_per_second_person = drawings_per_hour_person / 3600  # 3600 seconds in an hour
    drawings_per_minute_person = drawings_per_hour_person / 60  # 60 minute in an hour

    # Calculating how much faster the app is compared to one person
    speed_factor = pages_per_second_app / drawings_per_second_person

    # Calculating the number of people required to match the speed of the app
    people_needed = pages_per_second_app / drawings_per_second_person

    # Calculating the time it takes for a person to do one drawing (in seconds)
    time_per_drawing_person = 1 / drawings_per_second_person

    speed_factor, people_needed, time_per_drawing_person
    # logger.info("\n\n-------------------------------------------------------------------------------------------")
    # logger.info(f"Processed {total_pages} in {extract_time:.2f} seconds.")
    # logger.info(f"Average Person one drawing = {time_per_drawing_person:.2f} s.")
    # logger.info(f"Average Person: {drawings_per_minute_person:.2f} drawings /min.")
    # logger.info(f"Average Person: {drawings_per_hour_person} /hr")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info(f"AIS Pages per second: {pages_per_second_app:.2f}")
    # logger.info(f"AIS is approximately {speed_factor:.2f} times faster.")
    # logger.info(f"It would take about {people_needed:.0f} people to match the speed of the AIS.")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info("-------------------------------------------------------------------------------------------")
    # logger.info("-------------------------------------------------------------------------------------------")

    print(f"Processed {total_pages} in {extract_time:.2f} seconds.")
    print(f"Average Person one drawing = {time_per_drawing_person:.2f} s.")
    print(f"Average Person: {drawings_per_minute_person:.2f} drawings /min.")
    print(f"Average Person: {drawings_per_hour_person} /hr")
    print("-------------------------------------------------------------------------------------------")
    print(f"AIS Pages per second: {pages_per_second_app:.2f}")
    print(f"AIS is approximately {speed_factor:.2f} times faster.")
    print(f"It would take about {people_needed:.0f} people to match the speed of the AIS.")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")
    print("-------------------------------------------------------------------------------------------")

def run_main(pdf_path, roi_payload, payload_options, project_id, parent_dir=None, sub_dir=None, debug_page_limit=None, analyze_bom=False, rename_files=False):

    directory = None 

    # Ensure work_dir and work_project are defined
    work_project = parent_dir if parent_dir is not None else 'AIS Default'
    sub_folder = sub_dir if sub_dir is not None else 'AIS Project'
    
    #Adjustments
    x_offset = 0
    y_offset = 0
    overlap_threshold=.92 #.92
    
    st_extract = time.time()
    
    #Load JSON Values
    value_patterns = {
        "elevation": [
        "EL [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "EL [-+]?[0-9]+\\.?[0-9]+/?[0-9]*\"",
        "EL [-+]?[0-9]+'[0-9]+\\.?[0-9]+/?[0-9]*\""
        ],
        "x_coordinate": [
        "X [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
        "X [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "X [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\"",
        "[X|Y] [-+]?(\\d+' ?)?\\d+\\.\\d+/\\d+\\\"",
        "[X|Y] [-+]?(\\d+\\.?\\d*/?\\d+)"
        ],
        "y_coordinate": [
        "Y [-+]?(\\d+' ?)?\\d+\\.?\\d*/?\\d*\\\"",
        "Y [-+]?[0-9]*'?[0-9]+\\.?[0-9]*/?[0-9]*\"",
        "Y [-+]?[0-9]+'[0-9]+\\.[0-9]+/[0-9]+\""
        ]
    }

    payload_options = load_json(payload_options)
    try:
        roi_payload = load_json(roi_payload)
    except Exception as e:
        logger.info("...")
        logger.info("...")
        logger.info("Direct json format used. Loading a json file not neccessary...")
        logger.info("...")
        logger.info("...")
        pass
        
    
    run_extract_dir = False #Change to True to run extraction for full directory
    
    # Construct the path for the "_work" directory in the user's home directory
    work_dir = os.path.join(os.path.expanduser('~'), 'AIS_work_dir')
    
    # Construct the path for the "work_project" subdirectory
    project_dir = os.path.join(work_dir, work_project)
    
    # Create the directories if they don't exist
    os.makedirs(project_dir, exist_ok=True)
    
    sub_dir = os.path.join(project_dir, sub_folder)
    
    # Create the directories if they don't exist
    os.makedirs(project_dir, exist_ok=True)
    
    # Create a 'docs' folder inside your project_dir
    docs_dir = os.path.join(sub_dir, 'docs')
    os.makedirs(docs_dir, exist_ok=True)
    
    #------------------------------------------------
    #---------------RUN MULTIPROCESSING-------------|
    #------------------------------------------------

    combined_bom_df, combined_raw_df, combined_general_df, combined_outlier_df, total_pages = run_multiprocessing(pdf_path, roi_payload, value_patterns, payload_options, 
                                                                                                                  sub_dir, x_offset, y_offset, overlap_threshold, project_id, debug_page_limit)

    # Ensure that the necessary columns exist in the DataFrame
    if 'elevation' in combined_general_df.columns:
        # Add or initialize 'min_elevation', 'max_elevation', and 'avg_elevation' columns
        combined_general_df['min_elevation'] = np.nan
        combined_general_df['max_elevation'] = np.nan
        combined_general_df['avg_elevation'] = np.nan

        combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = combined_general_df.apply(
            calculate_elevation_metrics, axis=1, result_type='expand'
        )
    else:
        # Handle the case where 'elevation' column does not exist
         logging.warning("'elevation' column not found in the DataFrame.")
    
    # Further processing of combined_bom_df, combined_raw_df, combined_general_df
    # Replace 'nan' (string), np.nan and pd.NA with blank ('') in the DataFrames
    combined_bom_df = combined_bom_df.replace(['nan', np.nan, pd.NA], '')
    combined_general_df = combined_general_df.replace(['nan', np.nan, pd.NA], '')
    combined_raw_df = combined_raw_df.replace(['nan', np.nan, pd.NA], '')
    combined_outlier_df = combined_outlier_df.replace(['nan', np.nan, pd.NA], '')
    
    #End Extraction Timer
    et_extract = time.time()
    extract_time = et_extract - st_extract

    #Logging
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info("-------------------------------------------------------------------------------------------")
    logger.info(f"Data Consolidated... \nGeneral Data Rows: {len(combined_general_df)}\nBOM Data Rows: {len(combined_bom_df)} \nRAW Data Rows: {len(combined_raw_df)}")
      
    end_time2 = time.time()
    
    print_metrics(total_pages, extract_time)

    # if rename_files == False:
    #     export_large_data_to_excel(combined_general_df, 'General Data.xlsx', sub_dir)
        
    # export_large_data_to_excel(combined_bom_df, 'BOM Data.xlsx', sub_dir)  
    # export_large_data_to_excel(combined_outlier_df, 'Outlier Data.xlsx', sub_dir)
    # export_large_data_to_excel(combined_raw_df, 'consolidated_raw_data.xlsx', sub_dir)

    if rename_files == True and platform.system() == 'Windows':
        logger.debug("rename_files = True. Rename the files")
        #Start the timer
        start_time3 =  time.time()
            
        #Call Function to Split the PDF Pages
        combined_general_df = split_pdf(pdf_path, docs_dir, combined_general_df, debug_page_limit)
            
        #Prompt for rename
        # Assuming you have a DataFrame named combined_general_df
        general_df_columns = combined_general_df.columns.tolist()
            
        df_holder = DataFrameHolder()
        launch_window(combined_general_df, docs_dir, df_holder.handle_modified_dataframe)
            
        # Wait for the callback to complete
        df_holder.wait_for_callback()

        # Use the df_holder.modified_df here
        if df_holder.modified_df is not None:
            logger.debug("Using the modified DataFrame:")
            #print(df_holder.modified_df)
            
        #End Timer
        end_time3 = time.time()
        pdf_split_time = end_time3 - start_time3
            
        logger.debug(f"Split PDF Time for {total_pages} pages: {pdf_split_time}")
        
        #export_large_data_to_excel(df_holder.modified_df, 'General Data.xlsx', sub_dir)
        
#--------------------------------------------------------------
#--------------------------------------------------------------
    #Get Lists from json
    merge_to_bom = payload_options.get('merge_to_bom', [])
    bom_merge_on = payload_options.get('bom_merge_on', [])
    
    # Filter out the columns that do not exist in combined_general_df
    bom_merge_on = [col for col in bom_merge_on if col in combined_general_df.columns]
    merge_to_bom = [col for col in merge_to_bom if col in combined_general_df.columns]

    #Merge columns to BOM using 'merge_to_bom' list
    if not combined_bom_df.empty and not combined_general_df.empty :
        try:
            combined_bom_df = combined_bom_df.merge(combined_general_df[merge_to_bom + bom_merge_on], 
                                        on=bom_merge_on, 
                                        how='left')
        except Exception as e:
            logger.error(f"An error occurred: {e}")
        
    # Trim all columns
    combined_raw_df = trim_all_columns(combined_raw_df)
    combined_bom_df = trim_all_columns(combined_bom_df)
    combined_general_df = trim_all_columns(combined_general_df)
    combined_outlier_df = trim_all_columns(combined_outlier_df)
    
    # export_large_data_to_excel(combined_general_df, 'General Data-Flange BU.xlsx', sub_dir)
    #export_large_data_to_excel(combined_raw_df, 'raw data.xlsx', sub_dir)
    
#------------------------------------------------------------------------------GPT ANALYZE
#------------------------------------------------------------------------------GPT ANALYZE
#------------------------------------------------------------------------------GPT ANALYZE
#------------------------------------------------------------------------------GPT ANALYZE

    #Analyze Data; Get values with GPT4 Advanced Data Analysis
    if analyze_bom and len(combined_bom_df) > 0: #If 'material_description' in dataframe
        if confirm_analysis():  # Prompt user for confirmation
            ai_start_time = time.time()
            print("\n\n--Analyzing the data...\n")
            #combined_bom_df = trim_all_columns2(combined_bom_df)  # Trim dataframe columns if necessary

            # Create a copy of the DataFrame to ensure the original is not modified
            preprocessed_df = combined_bom_df.copy()
        
            # Retain only 'material_description' and 'nps_od' columns before dropping duplicates
            preprocessed_df = preprocessed_df[['material_description', 'nps_od']]

            # Drop rows where 'material_description' is null or blank
            preprocessed_df = preprocessed_df.dropna(subset=['material_description'])
            preprocessed_df = preprocessed_df[preprocessed_df['material_description'].str.strip() != '']

            # Drop duplicate rows based on the combination of 'material_description' and 'nps_od'
            preprocessed_df = preprocessed_df.drop_duplicates()

            print("\n\nPREPROCESS: \n", preprocessed_df)

        
            gpt4_turbo = Gpt4Turbo()  # Create an instance of the Gpt4Turbo class
            gpt_bom_results = analyze_bom_data(preprocessed_df, gpt4_turbo)  # Call the function with the dataframe and the Gpt4Turbo instance

            # Flatten the nested 'classification' into the main dictionary for each item
            flattened_data = []
            for response_wrapper in gpt_bom_results:  # Here, gpt_bom_results is your entire JSON structure
                try:
                    for item in response_wrapper['responses']:
                        classification_info = item.pop('classification', {})  # Safely remove and get 'classification'
                        item.update(classification_info)  # Merge the classification info into the main dictionary
                        flattened_data.append(item)
                    
                except Exception as e:
                    for item in response_wrapper['results']:
                        classification_info = item.pop('classification', {})  # Safely remove and get 'classification'
                        item.update(classification_info)  # Merge the classification info into the main dictionary
                        flattened_data.append(item)

            # Create the DataFrame from the flattened data
            gpt_bom_results_df = pd.DataFrame(flattened_data)
        
            # Assuming gpt_bom_results is the result of the analyze_bom_data function
            #gpt_bom_results_df = pd.DataFrame(gpt_bom_results)
        

            # Specify the filename for the Excel workbook
            filename = "combined_results.xlsx"

            # Using ExcelWriter to write multiple DataFrames to separate sheets
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # Write the gpt_bom_results DataFrame to the first sheet
                gpt_bom_results_df.to_excel(writer, sheet_name='GPT_BOM_Results', index=False)
    
                # Write the preprocessed_df DataFrame to the second sheet
                preprocessed_df.to_excel(writer, sheet_name='Preprocessed_Data', index=False)
        
            ai_end_time = time.time()
        
            # Print the time taken to preprocess the DataFrame
            print("\n\nTime taken to preprocess DataFrame: {:.2f} seconds".format(ai_end_time - ai_start_time, "\n"))

            print(f"Results and preprocessed data have been exported to {filename}")

        
#-------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------

    # Convert each DataFrame to a JSON string and then load it as a Python dictionary
    bom_data_json = json.loads(combined_bom_df.to_json(orient='records'))
    raw_data_json = json.loads(combined_raw_df.to_json(orient='records'))
    outlier_data_json = json.loads(combined_outlier_df.to_json(orient='records'))

    if rename_files == True and platform.system() == 'Windows':
        general_data_json = json.loads(df_holder.modified_df.to_json(orient='records'))
        #export_large_data_to_excel(df_holder.modified_df, 'General Data.xlsx', sub_dir)
    else:
        general_data_json = json.loads(combined_general_df.to_json(orient='records'))

        
    # Set the output file path, you can modify this as needed
    output_file_path = os.path.join(sub_dir, 'output.json')

    # Construct the final JSON object with three keys
    final_json = {
        "bom_data": bom_data_json,
        "general_data": general_data_json,
        #"raw_data": raw_data_json,
        "outlier_data": outlier_data_json
    }
        
    # with open(output_file_path, 'w') as file:
    #     json.dump(final_json, file)
    #     logger.debug(f"Exported json data to: {output_file_path}")

    #Export dataframes
    #export_large_data_to_excel(combined_bom_df, 'BOM Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_general_df, 'General Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_raw_df, 'Raw Data.xlsx', sub_dir)
    #export_large_data_to_excel(combined_outlier_df, 'Outlier Data.xlsx', sub_dir)
    
    end_time1 = time.time()

    # Filter out JSON objects that have the 'tableCoordinates' property
    try:
        print("\n\n\n CLEANING BOM USING OUTLIER DATA")
        filtered_json = [item for item in roi_payload if 'tableCoordinates' not in item]

        # Extract the 'columnName' values
        json_column_names = {item['columnName'] for item in filtered_json}

        # Find the intersection with DataFrame columns
        matching_columns = set(combined_general_df.columns).intersection(json_column_names)

        # Convert to a list if needed
        matching_columns_list = list(matching_columns)
        
        # Sort the matching column names list in ascending order, case-insensitive
        sorted_columns_list = sorted(matching_columns_list, key=lambda s: s.lower())
        print(sorted_columns_list)
    
        #Clean data using the outlier data. 

    except Exception as e:
        logger.error(f"An Exception occured while trying to parse the JSON coordinates. EXCEPTION: {e}")
        return


        logger.debug(">>>>> PROCESS COMPLETE")

    return final_json, sorted_columns_list
   
def parse_arguments():
    #Useage: python main_mp.py "path/to/pdf.pdf" "path/to/roi_payload.json" "path/to/payload_options.json"
    #python main_mp.py "C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" "C:/Users/<USER>/source/repos/AIS_MarkupExtractor/Linde BCH1 Coords.json" "C:/Users/<USER>/source/repos/AIS_MarkupExtractor/payload_options.json"
    if len(sys.argv) > 1:  # Check if any arguments were passed
        parser = argparse.ArgumentParser(description='Process a PDF file.')
        parser.add_argument('pdf_path', type=str, help='Path to the PDF file')
        parser.add_argument('roi_payload', type=str, help='Path to the JSON file containing ROI payload')
        parser.add_argument('payload_options', type=str, help='Path to the JSON file containing payload options')
        parser.add_argument('parent_dir', type=str, help='Name of Parent Folder ex. Company Name Etc.')
        parser.add_argument('sub_dir', type=str, help='Name of Sub Parent Folder ex. Project Name, Job, Etc.')
        parser.add_argument('--debug_page_limit', type=int, default=None, help='Debug page limit (optional)')
        parser.add_argument('--analyze_bom', type=int, default=False, help='Run advanced analysis on returned bom data (optional)')
        parser.add_argument('--rename_files', type=int, default=None, help='Custom rename files (optional)')
        return parser.parse_args()
    else:
        # Default values for running in IDE
        class Args:
            #pdf_path = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 001\Data Dump\Merged Linde Output.pdf"#/Linde ISOs Combined (1).pdf" #r"C:/Users/<USER>/Downloads/17180B-LINDE-SMR LL.pdf" #r"C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" #TEST PAGE VALIDATION.pdf"
            #pdf_path = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Validation Drawings\Combined OCI Drawings_ALL ISOS - 81148.pdf" #r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Validation Drawings\Combined OCI Drawings - 81148.pdf" #r"C:/Users/<USER>/Downloads/Linde Combind.pdf" #/Linde ISOs Combined (1).pdf" #r"C:/Users/<USER>/Downloads/17180B-LINDE-SMR LL.pdf" #r"C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" #TEST PAGE VALIDATION.pdf"
            pdf_path = r"C:\Users\<USER>\DockerShared\Test OCI Subset.pdf"  #r"C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" C:\Users\<USER>\Downloads\Linde Combind.pdf
            #pdf_path = r"C:\Users\<USER>\DockerShared\RCM Combined.pdf"  #r"C:/Users/<USER>/Downloads/Linde ISOs Combined (1).pdf" C:\Users\<USER>\Downloads\Linde Combind.pdf
            roi_payload = 'oci_coordinates.json' #'Linde BCH1 Coords.json' #'IHI BCH Coords.json' ' #load_json('linde_phx_coords.json')
            #values_patterns = 'value_patterns.json'
            parent_dir = "Testing"
            sub_dir = "GPT"
            payload_options = 'payload_options.json'
            debug_page_limit = 5
            rename_files = False
            analyze_bom=True
        return Args()

###------------------------------------->>>
if __name__ == '__main__':
    args = parse_arguments()
    run_main(args.pdf_path, args.roi_payload, 
         args.payload_options, args.parent_dir, args.sub_dir, args.debug_page_limit, args.analyze_bom, args.rename_files)
   
