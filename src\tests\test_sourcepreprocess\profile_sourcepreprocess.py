import time
import fitz
import cv2
import os
import openpyxl

import pandas as pd

from functools import wraps

from src.atom.grouppdfs.detect_lines import get_hough_lines, get_contours, draw_contours, draw_hough_lines
from src.atom.vision.ocr_patch_multi import page_to_opencv
from src.atom.vision.detect_text_regions.flag_text_regions import visualize_page
from src.atom.vision.ocr_patcher import detect_page_regions
from src.atom.fast_storage import load_df_fast


def timing(f):
    """
    Decorator to be used to measure the time it takes for a single call
    """

    @wraps(f)
    def wrap(*args, **kw):
        actual_time = time.perf_counter()
        comput_time = time.process_time()

        result = f(*args, **kw)

        actual_time = time.perf_counter() - actual_time
        comput_time = time.process_time() - comput_time

        print(
            '[%2.4fs | %2.4fs] func:%r args:[%r, %r]' % (
                actual_time,
                comput_time,
                f.__name__,
                args if len(args) < 1000 else "<something large>",
                kw
            )
        )
        return result

    return wrap

@timing
def get_cv_regions(page: fitz.Page, page_num: int, dpi=300, config: dict={}):
    cv2_page_regions = detect_page_regions(page=page,
                                           page_number=page_num,
                                           dpi=dpi,
                                           min_area=config.get("min_area", 10),
                                           blur_ksize=config.get("blur_ksize", (3, 3)),
                                           min_thresh=config.get("min_thresh", 250),
                                           max_thresh=config.get("max_thresh", 255),
                                        )
    return cv2_page_regions

def save_excel(df, filename):

    df.to_excel(filename, index=False)

    # Use openpyxl's load_workbook method to open the Excel file and get the worksheet object
    wb = openpyxl.load_workbook(filename)
    ws = wb[wb.sheetnames[0]]

    # Iterate through all columns in the worksheet, use the column_dimensions property to get the column object, and set the auto_size attribute to True for automatic column width adjustment
    for col in ws.columns:
        ws.column_dimensions[col[0].column_letter].auto_size = True

    # Use the save method to save the modified Excel file
    wb.save(filename)

def mser_regions(image):
    # Read image
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # Modify the parameters of MSER
    mser = cv2.MSER_create(delta=15, min_area=1, max_area=200, max_variation=0.2, min_diversity=0.2)
    # Detect regions
    regions, _ = mser.detectRegions(gray)
    # Filter regions by area
    filtered_regions = [p for p in regions if len(p) > 10]
    # Draw the regions on the image
    for p in filtered_regions:
        x, y, w, h = cv2.boundingRect(p.reshape(-1, 1, 2))
        cv2.rectangle(image, (x, y), (x + w, y + h), (255, 0, 0), 2)
    # Display the result
    cv2.imshow("MSER regions", image)
    cv2.waitKey(0)

def draw_contours_rects(image, contours):
    # drawing Contours
    radius = 2
    color = (30,255,50)
    rows, cols = image.shape[:2]
    # cv2.drawContours(image, contours, -1, color , radius)
    for contour in contours:
        # x, y, w, h = cv2.boundingRect(contour[0])
        x, y, w, h = cv2.boundingRect(contour)
        # Drawing a rectangle on copied image
        area = w * h
        if area > 200:
            continue
        # # if area < 40:
        # #     continue
        # ratio_w_h = w / h
        # if ratio_w_h > 2:
        #     continue
        rotrect = cv2.minAreaRect(contour)
        angle = rotrect[-1]
        if abs(angle) == 90:
            color = (255,0,0)
        elif abs(angle) == 45:
            color = (0,255,0)
        else:
            color = (0,0,255)
            # continue
        cv2.rectangle(image, (x, y), (x + w, y + h), color, 1)


        # vx, vy, x, y = cv2.fitLine(contour, cv2.DIST_L2,0,0.01,0.01)
        # lefty = int((-x*vy/vx) + y)
        # righty = int(((cols-x)*vy/vx)+y)
        # if lefty < 0  or righty < 0:
        #     continue
        # # print(lefty, righty)
        # cv2.line(image, (cols-1, righty), (0, lefty), (0,0,250), 1)

    return image

def remove_structure(image):
    result = image.copy()
    gray = cv2.cvtColor(image,cv2.COLOR_BGR2GRAY)
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

    # Remove horizontal lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40,1))
    remove_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
    cnts = cv2.findContours(remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255,255,255), 5)

    # Remove vertical lines
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1,40))
    remove_vertical = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel, iterations=3)
    cnts = cv2.findContours(remove_vertical, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255,255,255), 5)

    cv2.imshow('original', image)
    cv2.imshow('result', result)
    cv2.imwrite('result.png', result)
    cv2.waitKey()


if __name__ == "__main__":

    # list of pdfs to test
    pdfs = [
        r"C:/Users/<USER>/Documents/Drawings/Binder1.pdf",
        r"C:\Drawings\SanMateoOCR\San Mateo Midstream_Black River_ISOs.pdf",
        r"C:\Users\<USER>\Documents\Drawings\PLNG BOG Drawings.pdf",
        r"C:\Users\<USER>\Documents\Drawings\RCM Combined.pdf",
        r"c:\Users\<USER>\Documents\Drawings\Classification - ISO -  25 - TEST 3.pdf",

        # # sharefile
        r"C:\Drawings\FromSharefile\Modified Sarpy Co Jones St Total Combined.pdf",
        r"C:\Drawings\FromSharefile\Combined Drawings - CF B vs C.pdf",
        r"C:\Drawings\FromSharefile\SCOPED-PQ-376510-PIP-ISO-KZV-0E2Z6-001 (E2Z6)ISOMETRIC BLANK.pdf",
        r"C:\Drawings\FromSharefile\81272 Weld Maps.pdf",
        r"C:\Drawings\FromSharefile\Excel 012 - BUNGE Combined.pdf",
        r"C:\Drawings\FromSharefile\Group 4.pdf",
        r"C:\Drawings\FromSharefile\1601 - Insulation Only.pdf",
        r"C:\Drawings\FromSharefile\P&S 008 - combined ISOs.pdf",
        r"C:\Drawings\FromSharefile\P&S 004 - Exxon LT Combined.pdf",
        r"C:\Drawings\FromSharefile\P&S 006 - 24-P-1002.pdf",
    ]
    for pdf in pdfs:
        print(pdf)

    # Some configurable vars
    config = {
        "min_area": 5,
        "blur_ksize": (3, 3),
        "min_thresh": 240,
        "max_thresh": 250,
    }
    page_limit = 1
    results = []
    region_times = []
    summary = []
    dpis = [72, 96, 128, 144, 300]
    # dpis = [72]
    dpis.sort()

    save_page_num = 1 # choose a page number to save for debugging

    os.makedirs("debug/cv_regions", exist_ok=True)
    for pdf_path in pdfs:
        basename = os.path.basename(pdf_path)
        start_time = time.perf_counter()
        doc = fitz.open(pdf_path)
        load_doc_time = time.perf_counter() - start_time
        page_count = doc.page_count
        pages = min(page_limit, page_count)

        all_page_regions = [] # store for saving later

        # page_to_opencv
        page = doc[0]
        dpi = 72
        cv_image = page_to_opencv(page, dpi=dpi)

        # mser_regions(cv_image)
        # remove_structure(cv_image)

        from pytesseract import Output
        import pytesseract

        rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

        results = pytesseract.image_to_data(rgb, output_type=Output.DICT)
        for i in range(0, len(results["text"])):
            # extract the bounding box coordinates of the text region from
            # the current result
            x = results["left"][i]
            y = results["top"][i]
            w = results["width"][i]
            h = results["height"][i]
            # extract the OCR text itself along with the confidence of the
            # text localization
            text = results["text"][i]
            conf = int(results["conf"][i])
        exit()

        # --- Contours alternative (uncomment block if just want to test contours instead of detect_page_regions)
        contours = get_contours(cv_image)

        con = draw_contours_rects(cv_image, contours)

        contour_data = []
        # save contours data
        for n, contour in enumerate(contours):
            x, y, w, h = cv2.boundingRect(contour)
            ratio_w_h = w / h

            rotrect = cv2.minAreaRect(contour)
            angle = rotrect[-1]

            contour_data.append({
                "n": n,
                "x": x,
                "y": x,
                "w": w,
                "h": h,
                "area": w * h,
                "ratio_w_h": ratio_w_h,
                "angle": angle,
            })

        contours_df = pd.DataFrame(contour_data)
        contours_df.sort_values("area", ascending=[False], inplace=True)
        contours_df.to_excel(f"debug/cv_regions/{basename}_contours.xlsx")
        cv2.imwrite(f"debug/cv_regions/{basename}_contours_dpi={dpi}.png", con)

        # # hough_lines = get_hough_lines(cv_image)
        # # image = draw_hough_lines(cv_image, hough_lines)
        # # cv2.imshow("hough_lines", image)
        # # cv2.waitKey()

        # # # cv2.imshow("contours", con)
        # # # cv2.waitKey()

        # continue

        # --- END Contours alternative

        for dpi in dpis:

            times = []
            checkpoint = time.perf_counter()
            unique_page_shapes = set()

            total_regions = 0

            for n in range(pages):
                page_num = n + 1
                page = doc[n]
                s = time.perf_counter()

                dpi_regions = []
                cv_images = []

                regions = get_cv_regions(page, page_num, dpi, config)
                rt = time.perf_counter() - s
                page_rect = [int(x) for x in [page.rect[2], page.rect[3]]]
                unique_page_shapes.add(str(page_rect))
                # region_times.append({
                #     "basename": basename,
                #     "page_num": page_num,
                #     "cv_region_time": rt,
                #     "num_regions": len(regions),
                #     "page_rect": page_rect,
                #     "dpi": dpi
                # })
                total_regions += len(regions)

                if page_num == save_page_num:
                    all_page_regions.append(regions)

                times.append(rt)

            detect_regions_time = time.perf_counter() - checkpoint

            avg = sum(times) / len(times)

            summary.append({
                "basename": basename,
                "total_pages": doc.page_count,
                "pages_tested": pages,
                "total_region_detect_time": round(detect_regions_time, 3),
                "avg_cv_region_time": round(avg, 3),
                "doc_load": round(load_doc_time, 3),
                "unique_page_shapes": list(unique_page_shapes),
                "approx. total seconds": round(avg * doc.page_count, 3),
                "approx. total minutes": round((avg * doc.page_count) / 60, 3),
                "dpi_used": dpi,
                "total_regions": total_regions,
                "config": config,
            })

        # Optional
        # save for visual debug
        cv_images = []
        for x, dpi in enumerate(dpis):
            cv_image = visualize_page(page, page_num, all_page_regions[x], output_path=None)
            color = (255, 0, 0)
            cv2.putText(cv_image, f"{basename}-dpi={dpi}", (40, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            cv2.putText(cv_image, f"Page={page_num}, Config={config}", (40, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            cv_images.append(cv_image)

        full_image = cv2.hconcat(cv_images)
        config_str = f"min_area={config['min_area']}-kblur={config['blur_ksize']}-minval={config['min_thresh']}-maxval={config['max_thresh']}"
        img_path = os.path.join("debug/cv_regions", f"{basename}-[{dpis}]--page={save_page_num}-{config_str}.png")
        print(img_path)
        cv2.imwrite(img_path, full_image)

    save_excel(pd.DataFrame(summary), "debug/cv_regions/summary_test.xlsx")
    # save_excel(pd.DataFrame(region_times), "debug/cv_regions/region_times_test.xlsx")


# cv_regions = get_cv_regions(page, page_num)
# print(cv_regions)

# missing_regions = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\4\sources\cuserss1documentsdrawingsbinder1pdf\pmr.feather"

# df = load_df_fast(missing_regions)

# output_path = "debug"
# # cv_image = visualize_page(page, page_num, cv_regions, output_path=output_path)
# # cv2.imshow("test", cv_image)
# # cv2.waitKey(0)

