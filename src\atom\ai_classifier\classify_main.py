"""

https://medium.com/@lars.chr.wiik/how-to-use-llm-apis-openai-claude-google-50bc7ce2c8de
https://github.com/tiny-rawr/parallel_process_gpt
"""

import asyncio
import os
from dotenv import load_dotenv  # Import the load_dotenv function from the python-dotenv package
from .api_request_parallel_processor import process_api_requests_from_file
from .generate_requests import generate_chat_completion_requests_stage1, generate_chat_completion_requests_stage2, filter_json_by_fields, category_filter
from .save_generated_data_to_csv import save_generated_data_to_csv, create_dataframe_from_jsonl, transform_dataframe
from .csv_to_array import convert_csv_to_array, dataframe_to_structure, convert_json_to_structure, convert_json_to_structure_NoAnswer
from .prompts import (categorization_table, cat_prompt_1_answerTrue, cat_prompt_1_answerFalse, cat_prompt_2_answerFalse,
                      cat_prompt_1_answerFalse_mod, cat_prompt_2_answerFalse_mod, cat_prompt_1_answerFals<PERSON>_<PERSON>, cat_prompt_2_answerFalse_Claude)
from .cls_general import classify_general_items, map_abbreviated_material, map_unit_of_measure
import time
import pandas as pd
import numpy as np
from src.utils.logger import logger
from pubsub import pub
from threading import Thread, Event
from src.app_paths import getDataTempPath
import statistics
import json
from fractions import Fraction
from unit_tests import data_integrity



# logger = logging.getLogger(__name__)

'''
    Current Stats:
    GPT 3.5 (tier 3) w/ answer descriptions:
        benchmark: 84 items @ 4.6 minutes
        18.26 items per min
        1095.6 items per hr

    GPT 3.5 (Tier 3) no asnwer descriptions: - 5/31/24
        benchmark: 56 items @ 1.82 minutes (109 s)
        30.88 items per min
        1853 items per hr

    GPT 4 Turbo (Tier 3):
        benchmark: 84 items @ 4.13 minutes
        20.24 items per min
        1000 items per hr

    GPT 4o - mini (Tier 3)
'''

unicode_fractions = {
    '½': '1/2',
    '¼': '1/4',
    '¾': '3/4',
    '⅓': '1/3',
    '⅔': '2/3',
    '⅛': '1/8',
    '⅜': '3/8',
    '⅝': '5/8',
    '⅞': '7/8'
}


# Set true for testing as this will send many API requests
TEST_LIMIT: int = 10

load_dotenv()  # This loads the .env file's contents into the environment
#ai_key=os.getenv("API_KEY2") # Load the OpenAI key
API_KEY2="***************************************************"
ai_key=API_KEY2

class FileMonitor(Thread):

    def __init__(self, file) -> None:
        super().__init__()
        self.file = file
        self.linesRead = 0
        self.stopEvent = Event()

    def run(self):
        last_modified = os.path.getmtime(self.file)
        while not self.stopEvent.is_set():
            current_modified = os.path.getmtime(self.file)
            if current_modified != last_modified:
                print("File has changed!")
                with open(self.file, "r") as file:
                    last_modified = current_modified
                    count = sum(1 for _ in file)
                    lines = []
                    for n, line in enumerate(file):
                        if n < self.linesRead:
                            continue
                        lines.append(line.strip())
                    print("new lines: ", lines)
                    self.linesRead = count
            time.sleep(2)

def merge_dataframes(df1, df2):
    # Merge df2 into df1 on '__uid__' to add 'rfq_scope' from df1 to df2
    merged_df = pd.merge(df2, df1[['__uid__', 'rfq_scope']], on='__uid__', how='left')

    # Combine 'answer_explanation' and 'review_explanation' into one column, handling NaNs and blanks
    for col in ['answer_explanation', 'review_explanation']:
        merged_df[col] = merged_df[col].fillna('')  # Replace NaN with empty strings

    # Combine the explanations into one field <-- DO NOT COMBINE
    # merged_df['combined_explanation'] = merged_df.apply(
    #     lambda x: f"{x['answer_explanation'].strip()} {x['review_explanation'].strip()}".strip(), axis=1)

    # Determine the review status
    merged_df['review'] = merged_df.apply(
        lambda x: x['review'] or (x['__uid__'] in df1['__uid__'].values and df1[df1['__uid__'] == x['__uid__']]['review'].any()), axis=1)

    return merged_df

def optimize_rfq_data(item_df):
    # Initialize an empty list to hold DataFrame rows before concatenation
    data = []

    # Group by 'material_description' and aggregate __uid__'s
    grouped = item_df.groupby('material_description')['__uid__'].apply(list).reset_index()

    for _, row in grouped.iterrows():
        uid_list = row['__uid__']
        print(f"Full list: {uid_list}, Related UIDs: {uid_list[1:]}")  # Debugging line
        # Instead of appending, create a DataFrame for each row and add it to the list
        data.append(pd.DataFrame({
            'material_description': [row['material_description']],
            '__uid__': [uid_list[0]],  # First UID as the primary
            'related_uids': [uid_list[1:]]  # Rest of the UIDs as related
        }))

    # Concatenate all DataFrame rows in the list into a single DataFrame
    mtrl_df = pd.concat(data, ignore_index=True)

    return mtrl_df

def clear_file_contents(file_path: str):
    # Open the file in write mode which clears the existing contents
    with open(file_path, 'w') as file:
        pass  # Opening in 'w' mode and closing the file clears it

def process_ai_requests(API_KEY,
                        requests_filepath,
                        requests_output_filepath,
                        request_stage_desc,
                        cancel_event=None,
                        next_request_made_cb=None):
    print(f"\n\n-->ASYNC START: {request_stage_desc}")
    #Process multiple api requests to ChatGPT
    # print(requests_filepath, requests_output_filepath)
    try:
        asyncio.run(
            process_api_requests_from_file(
                requests_filepath=requests_filepath,
                save_filepath=requests_output_filepath,
                request_url="https://api.openai.com/v1/chat/completions",
                api_key=API_KEY,
                max_requests_per_minute=float(9500),#float(90000),
                max_tokens_per_minute=float(1900000),#float(170000),
                token_encoding_name="cl100k_base",
                max_attempts=int(5),
                logging_level=int(20),
                cancel_event=cancel_event,
                next_request_made_cb=next_request_made_cb,
            )
        )
    except Exception as e:
        logger.error(f"Error processing requests: {e}")
        return False, e # This allows us to collect failures

    print(f"\n\n-->ASYNC END - {request_stage_desc}")
    return True, None

class NoClassificationError(Exception):
    pass

class Classifier():

    def __init__(self,
                 data: dict,
                 config: dict,
                 jobId: str,
                 include_answer: bool):
        self.config: dict = config
        self.jobId: str = jobId
        self.include_answer: bool = include_answer
        self.testMode: bool = self.config.get("testMode", False)
        self.project: dict = data["project"]  # A dict record of the project requesting this assistance
        self.df: pd.DataFrame = data["df"]  # A dict record of the project requesting this assistance
        self.classificationsRequested = len(self.df)
        self._result = None
        self._cancel: asyncio.Event = asyncio.Event()
        self._total_expected_requests = 0
        self._time_of_last_request = None
        self._times = []
        self._eta_updated_cb = lambda x: None
        self._last_eta = None
        self._timer = Thread(target=self.on_timer)
        self._errors = set()

    def setup(self):
        self._errors.clear()

        if self.include_answer:
            self.prompt1 = cat_prompt_1_answerTrue
            self.prompt2 = cat_prompt_1_answerTrue
        else:
            self.prompt1 = cat_prompt_1_answerFalse_mod
            self.prompt2 = cat_prompt_2_answerFalse_mod

        gptVersion = self.config["gptVersion"]
        if gptVersion == "3.5":
            self.selected_model = "gpt-3.5-turbo-0125" # $0.50/$1.50
            #self.selected_model = "gpt-4o-mini"
        elif gptVersion == "4":
            self.selected_model = "gpt-4-turbo" # $10.00/$30.00
        elif gptVersion == "4o":
            self.selected_model = "gpt-4o" # $2.50/$10.00
        elif gptVersion == "4o-Mini":
            self.selected_model = "gpt-4o-mini" # $0.15 / $0.60
        elif gptVersion == "o1":
            self.selected_model = "o1-2024-12-17" # $15.00/$60.00
        elif gptVersion == "o1-Mini":
            self.selected_model = "o1-mini-2024-09-12" # $1.10/$4.40
        elif gptVersion == "o3-Mini":
            self.selected_model = "o3-mini-2025-01-31" # $1.10/$4.40

        else:
            raise ValueError("Invalid GPT version specified. Options are `3.5` or `4`")
        # assert testMode in [True, False]

        if self.include_answer:
            self.cls_data = convert_json_to_structure(categorization_table)
        else:
            self.cls_data = convert_json_to_structure_NoAnswer(categorization_table)

        self.requests_filepath_stage1 = getDataTempPath("stage1_requests_chat_completion.jsonl")
        self.requests_filepath_stage2 = getDataTempPath("stage2_requests_chat_completion.jsonl")

        self.requests_output_filepath_stage1 = getDataTempPath("stage1_requests_chat_completion_results.jsonl")
        self.requests_output_filepath_stage2 = getDataTempPath("stage2_requests_chat_completion_results.jsonl")

    def on_next_classify_request_made(self):
        """
        https://stackoverflow.com/questions/2779600/how-to-estimate-download-time-remaining-accurately
        https://stackoverflow.com/questions/933242/smart-progress-bar-eta-computation?noredirect=1&lq=1
        """
        if self._time_of_last_request is None:
            self._time_of_last_request = time.time()
            return
        self._total_expected_requests = max(0, self._total_expected_requests - 1)
        since = time.time() - self._time_of_last_request
        self._times.append(since)
        self._time_of_last_request = time.time()

    def optimize_rfq(self):
        # Reduce number of rows by getting unique material_descriptions. We will submit these as requests and map the others later
        pub.sendMessage("set-statusbar-realtime", message="Optimizing RFQ", jobId=self.jobId)

        # try:
        #     mtrl_df = optimize_rfq_data(self.df)
        # except:
        #     mtrl_df = pd.DataFrame()
        #print(f"\n\n > item_df rows: {len(item_df)}")
        #print(f"\n\n > item_df columns: {item_df.columns}")
        #print(f"\n\n > mtrl_df rows: {len(mtrl_df)}")

        #mtrl_df.to_excel("test_matrl_df.xlsx", index=False)

        # Create Data (This should be the rfq data)
        item_data = dataframe_to_structure(self.df)
        # item_data = dataframe_to_structure(mtrl_df)

        #print("\n\nITEM DATA (From DF):\n",item_data)

        def test_key(k):
            api_key=os.getenv("API_KEY2")
            #print(f"KEY: {api_key}")
            return api_key

        #open_ai_key = test_key("TEST") # Uncomment to test key
        #####################################
        #####################################
        #testMode=False #                                                            <-- REMOVE!!! <---

        if self.testMode:
            item_data = item_data[:TEST_LIMIT] #limit data for testing

        self.item_data = item_data

    def classify_rfq_scope(self):
        """Stage 1 - Classify rfq_scope"""
        # Clear json l                  < -------------------- WARNING: WILL CLEAR EXISTING CONTENTS
        pub.sendMessage("set-statusbar-realtime", message="MTO - Stage 1", jobId=self.jobId)

        clear_file_contents(self.requests_filepath_stage1)
        clear_file_contents(self.requests_output_filepath_stage1)

        clear_file_contents(self.requests_filepath_stage2)
        clear_file_contents(self.requests_output_filepath_stage2)
        # < -------------------- WARNING: WILL CLEAR EXISTING CONTENTS

        # TODO - Monitor file size
        # t = FileMonitor(file=requests_output_filepath_stage1)
        # t.start()
        print("\n\n Generating...")

        stage1_fields_to_include = ['rfq_scope']
        stage1_fields = filter_json_by_fields(self.cls_data, stage1_fields_to_include) # <-- Filter by rfq_scope first

        generate_chat_completion_requests_stage1(self.requests_filepath_stage1,
                                                 self.item_data,
                                                 stage1_fields,
                                                 self.prompt1,
                                                 model_name=self.selected_model,
                                                 temperature=0.0,
                                                 top_p=0.1,
                                                 include_answer=self.include_answer) # gpt-3.5-turbo-0125 #

        if self._cancel.is_set():
            return

        # Get number of requests in file to calculate ETA
        with open(self.requests_filepath_stage1, 'r', encoding='utf-8') as json_file:
            json_list = list(json_file)
            self._total_expected_requests += len(json_list)

        ok, error = process_ai_requests(ai_key,
                                        self.requests_filepath_stage1,
                                        self.requests_output_filepath_stage1,
                                        "Stage 1 ('rfq_scope')",
                                        cancel_event=self._cancel,
                                        next_request_made_cb=self.on_next_classify_request_made)

        if self._cancel.is_set():
            return

        # Output results for debugging
        df_1 = create_dataframe_from_jsonl(self.requests_output_filepath_stage1)
        if df_1.empty:
            raise NoClassificationError(error)

        trans_df_1 = transform_dataframe(df_1, self.df) # Turn the structure into a usable tabular format.
        # Apply the category_filter function to the 'rfq_scope' column and create a new 'cls_list' column with the result
        trans_df_1['cls_list'] = trans_df_1['rfq_scope'].apply(category_filter)
        #-----------------------------------------------------------------
        # DEBUG
        # Export the Raw DataFrame to an Excel file
        # raw_excel_filename = 'raw_output_stage1.xlsx'  # You can choose your own file name and path
        #df_1.to_excel(raw_excel_filename, index=False)
        #print(f"DataFrame is exported to Excel file {raw_excel_filename} successfully.")

        # Export the Transformed (FINAL) DataFrame to an Excel file
        # trans_excel_filename = 'transformed_output_stage1.xlsx'  # You can choose your own file name and path
        # trans_df_1.to_excel(trans_excel_filename, index=False)
        # print(f"DataFrame is exported to Excel file {trans_excel_filename} successfully.")
        # # DEBUG
        #-----------------------------------------------------------------

        self.trans_df_1 = trans_df_1

    def classify(self):
        """Stage 2 - Classify"""
        pub.sendMessage("set-statusbar-realtime", message="MTO - Stage 2 - Classifying", jobId=self.jobId)
        stage2_data = dataframe_to_structure(self.trans_df_1, stage_2=True)

        # Generate requests
        generate_chat_completion_requests_stage2(self.requests_filepath_stage2,
                                                 stage2_data, self.cls_data,
                                                 self.prompt2,
                                                 model_name=self.selected_model,
                                                 temperature=0.0,
                                                 top_p=0.1,
                                                 include_answer=self.include_answer)
        if self._cancel.is_set():
            return

        # Get number of requests in file to calculate ETA
        with open(self.requests_filepath_stage2, 'r', encoding='utf-8') as json_file:
            json_list = list(json_file)
            self._total_expected_requests += len(json_list)

        ok, error = process_ai_requests(ai_key,
                            self.requests_filepath_stage2,
                            self.requests_output_filepath_stage2,
                            "stage 2 ('classify filtered fields')",
                            cancel_event=self._cancel,
                            next_request_made_cb=self.on_next_classify_request_made
                            )

        if self._cancel.is_set():
            return

        df_2 = create_dataframe_from_jsonl(self.requests_output_filepath_stage2)
        if df_2.empty:
            raise NoClassificationError(error)
        self.trans_df_2 = transform_dataframe(df_2, self.df) # turn the structure into a usable tabular format.

        # debug
        # export the raw dataframe to an excel file
        # raw_excel_filename = 'debug/raw_output_stage2.xlsx'
        # df_2.to_excel(raw_excel_filename, index=False)
        # print(f"dataframe is exported to excel file {raw_excel_filename} successfully.")

        # export the transformed (final) dataframe to an excel file
        # trans_excel_filename = 'debug/transformed_output_stage2.xlsx'
        # trans_df_2.to_excel(trans_excel_filename, index=False)
        # print(f"dataframe is exported to excel file {trans_excel_filename} successfully.")
        # debug

    def combine_results(self):
        """Stage 3 - Combine results"""
        pub.sendMessage("set-statusbar-realtime", message="MTO - Stage 3 - Processing", jobId=self.jobId)
        self.final_df = merge_dataframes(self.trans_df_1, self.trans_df_2)

    def translate_to_general_categories(self):
        """Stage 4 - Translate to general categories"""
        pub.sendMessage("set-statusbar-realtime", message="MTO General- Stage 4 - Processing", jobId=self.jobId)
        # # --> Apply the classification function to categorize fittings, flanges, valves, etc.
        self.final_df['general_category'] = self.final_df.apply(classify_general_items, axis=1)

        # --> Get unit of measure
        self.final_df['unit_of_measure'] = self.final_df['general_category'].apply(map_unit_of_measure)

        # --> Get Abbreviated Material
        self.final_df['abbreviated_material'] = self.final_df['material'].apply(map_abbreviated_material)

        try:
            # Replace 'nan', 'None', and 'null' with np.nan
            self.final_df.replace(['nan', 'None', 'null'], np.nan, inplace=True)
        except Exception as e:
            logger.error(f"Could not replace null in RFQ.: {e}")

        #-----------------------------------------------------------------
        # Save the final merged DataFrame to an Excel file
        # final_excel_filename = 'final_merged_output.xlsx'
        # final_df.to_excel(final_excel_filename, index=False)
        # print(f"Final merged DataFrame is exported to Excel file {final_excel_filename} successfully.")
        #-----------------------------------------------------------------

    def remerge_dataframe(self):
        """Remerge columns from input dataframe into output results dataframe"""
        # self.final_df['quantity'] = pd.Series(dtype='float')
        df = self.df[['__uid__', 'quantity', 'size1', 'size2']]
        df.rename({'__uid__': 'tko_id'}, axis=1, inplace=True)
        # print(self.final_df[['tko_id', 'quantity']])
        self.final_df = self.final_df.merge(df, how='inner', on=['tko_id'])

    def clean_dataframe(self):
        from src.atom.convert_excel_fields import convert_display_to_key
        from unit_tests import data_integrity
        self.final_df = convert_display_to_key(self.final_df)
        self.final_df = data_integrity.replace_empty_with_na(self.final_df)
        self.final_df = data_integrity.validate_options(self.final_df)
        self.final_df = data_integrity.process_dataframe(self.final_df, inplace=False)

    def finalize_result(self):
        pub.sendMessage("set-statusbar-realtime", message=f"MTO Assistant Complete", jobId=self.jobId)
        self.duration = self.start_time - time.time()  # Calculate the duration
        self._result = {
            "ok": True,
            "status": "complete",
            "final_df": self.final_df,
            "configuration": self.config,
            "project": self.project,
            "duration": round(self.duration, 3),
            "requested_classifications": self.classificationsRequested,
            "errors": sorted(list(self._errors))
        }
        self.cleanup()
        pub.sendMessage("mto-assistant-update", data=self._result)
        logger.info(f"\n\n--> Total time taken for AI Classification: {self.duration} seconds <--\n\n")

    def cleanup(self):
        self.df = pd.DataFrame()
        self.trans_df_1 = None
        self.trans_df_2 = None
        self.final_df = None

    def run(self):
        """Run classification"""
        pub.sendMessage("set-statusbar-realtime", message="Classification Started", jobId=self.jobId)
        self.start_time = time.time()  # Capture the start time
        self._timer.start()
        try:
            for n, state in enumerate([
                self.setup,
                self.optimize_rfq,
                self.classify_rfq_scope,
                self.classify,
                self.combine_results,
                self.translate_to_general_categories,
                self.remerge_dataframe,
                self.clean_dataframe,
                self.finalize_result
            ]):
                if self._cancel.is_set():
                    print(f"Cancelled classification at step {n} - function `{state.__name__}`")
                    return
                logger.info(f"MTO Classification step {n} - function `{state.__name__}`")
                state()
        except NoClassificationError as e:
            logger.debug("No classifications were made. Return the original records")
            self.cancel()
            if str(e) == "insufficient_quota":
                e = "\nReason: Insufficient Quota"
            raise NoClassificationError(f"MTO assistant found no classifications for {len(self.df)} record(s).{e}")
        except Exception as e:
            print(f"Error on MTO Classification step {n} - function `{state.__name__}`")
            self.cancel()
            raise Exception(f"MTO Error (code={n}) - {e}")

        self.cleanup()
        self._cancel.set()  # Done

    def cancel(self):
        """Cancel classification and all running tasks"""
        logger.debug("Cancelling classification")
        self._cancel.set()

    def on_timer(self):
        smoothing_factor = 0.05
        while not self._cancel.is_set():
            time.sleep(1)
            if not self._eta_updated_cb:
                continue

            self._times = self._times[-5:]
            if len(self._times) < 5:
                continue
            eta = int(statistics.fmean(self._times) * self._total_expected_requests)
            if self._last_eta is None:
                self._last_eta = eta
                continue
            eta = smoothing_factor * self._last_eta + (1 - smoothing_factor) * eta
            self._last_eta = int(eta)
            if self._eta_updated_cb:
                self._eta_updated_cb(self._last_eta)

        self._eta_updated_cb(0)