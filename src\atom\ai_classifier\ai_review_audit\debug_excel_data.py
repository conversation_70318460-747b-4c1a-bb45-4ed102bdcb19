"""
Debug script to examine the actual Excel data
"""

import pandas as pd
import os

def examine_excel_file():
    """Examine the actual Excel file to see what's in it"""
    
    excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 044 - Heartwell Valentine PTU\Data\Workspace\rfq_template.xlsx"
    
    print("EXAMINING EXCEL FILE")
    print("=" * 50)
    print(f"File path: {excel_file_path}")
    
    # Check if file exists
    if not os.path.exists(excel_file_path):
        print("❌ File does not exist!")
        return
    
    print("✓ File exists")
    
    try:
        # Load the Excel file
        print("\nLoading Excel file...")
        df = pd.read_excel(excel_file_path)
        
        print(f"✓ File loaded successfully")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Show first 2 rows
        print("\nFirst 2 rows:")
        print("=" * 80)
        first_2_rows = df.head(2)
        
        # Show all columns for first 2 rows
        for i, (idx, row) in enumerate(first_2_rows.iterrows()):
            print(f"\nROW {i+1} (Index {idx}):")
            print("-" * 40)
            for col in df.columns:
                value = row[col]
                print(f"  {col}: {repr(value)} (type: {type(value).__name__})")
        
        # Check for key columns
        print("\nKEY COLUMN ANALYSIS:")
        print("-" * 40)
        
        key_columns = ['id', 'material_description', 'rating', 'rfq_scope', 'material', 'astm']
        
        for col in key_columns:
            if col in df.columns:
                print(f"✓ {col}: Found")
                sample_values = df[col].head(2).tolist()
                print(f"    First 2 values: {sample_values}")
            else:
                print(f"❌ {col}: Missing")
        
        # Look for similar column names
        print("\nSIMILAR COLUMN NAMES:")
        print("-" * 40)
        
        for target_col in ['id', 'material_description', 'rating']:
            if target_col not in df.columns:
                similar = [col for col in df.columns if target_col.lower() in col.lower() or col.lower() in target_col.lower()]
                if similar:
                    print(f"  Columns similar to '{target_col}': {similar}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading Excel file: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    examine_excel_file()
