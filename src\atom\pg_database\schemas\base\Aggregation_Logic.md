# RFQ to BOM to General Data Merge Implementation Guide for PostgreSQL

This document provides a step-by-step guide for implementing the RFQ to BOM to General data merge functionality using PostgreSQL database tables. It mirrors the Python implementation found in the ATEM application, specifically focusing on the data flow and transformation logic.

## Overview of Data Flow

The process follows this logical order:
1. RFQ data is processed and prepared
2. RFQ data is merged into BOM data
3. Updated BOM data is aggregated and merged into General data
4. Special handling for size values in General data

## Source Files Reference
* Main merge function: `src/atom/merge_rfq_into_bom.py`
* UI implementation: `src/atom/tables/rfqtableview.py`
* Equivalent factor lookup: `src/atom/ef_lookup.py`

## Step 1: Data Preparation and Basic Table Structure

First, ensure you have the following tables with appropriate schema:

```sql
-- Create the RFQ table
CREATE TABLE rfq (
    id SERIAL PRIMARY KEY,
    material_description TEXT,
    size VARCHAR(50),
    size1 NUMERIC,
    size2 NUMERIC,
    quantity NUMERIC,
    general_category VARCHAR(100),
    rfq_scope VARCHAR(100),
    -- Other RFQ fields
);

-- Create the BOM table
CREATE TABLE bom (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER,
    material_description TEXT,
    size VARCHAR(50),
    size1 NUMERIC,
    size2 NUMERIC,
    quantity NUMERIC,
    general_category VARCHAR(100),
    rfq_scope VARCHAR(100),
    ef NUMERIC,  -- Equivalent factor
    sf NUMERIC,  -- Surface factor
    -- Other BOM fields
);

-- Create the General table
CREATE TABLE general (
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER,
    size VARCHAR(50),
    -- Quantity columns for different categories
    lf NUMERIC DEFAULT 0,  -- Linear Feet
    ea NUMERIC DEFAULT 0,  -- Each
    -- Other category quantity columns
    -- Other General fields
);
```

## Step 2: RFQ to BOM Merge Implementation

### 2.1 Process RFQ Data

```sql
-- Function to process size fields in RFQ
CREATE OR REPLACE FUNCTION process_size_column(size_value TEXT)
RETURNS TABLE(size1 NUMERIC, size2 NUMERIC) AS $$
BEGIN
    -- Extract size1 and size2 from size string
    -- This would implement the logic from process_size_column in Python
    -- For example, handling formats like "4x2", "4", etc.
    
    -- Simplified implementation:
    IF size_value ~ '[0-9]+x[0-9]+' THEN
        RETURN QUERY SELECT 
            CAST(split_part(size_value, 'x', 1) AS NUMERIC),
            CAST(split_part(size_value, 'x', 2) AS NUMERIC);
    ELSE
        RETURN QUERY SELECT 
            CAST(NULLIF(size_value, '') AS NUMERIC),
            NULL::NUMERIC;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update RFQ with processed size values
CREATE OR REPLACE FUNCTION update_rfq_sizes()
RETURNS VOID AS $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN SELECT id, size FROM rfq WHERE size IS NOT NULL
    LOOP
        UPDATE rfq
        SET 
            size1 = (SELECT size1 FROM process_size_column(r.size)),
            size2 = (SELECT size2 FROM process_size_column(r.size))
        WHERE id = r.id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 2.2 Merge RFQ Data into BOM

```sql
CREATE OR REPLACE FUNCTION merge_rfq_into_bom()
RETURNS VOID AS $$
BEGIN
    -- Update BOM entries with matching RFQ data
    UPDATE bom b
    SET 
        general_category = r.general_category,
        rfq_scope = r.rfq_scope,
        -- Update other RFQ fields
        last_updated = CURRENT_TIMESTAMP
    FROM rfq r
    WHERE b.material_description = r.material_description
      AND (b.size = r.size OR (b.size1 = r.size1 AND b.size2 = r.size2));
    
    -- Next, update EF (Equivalent Factor) values
    PERFORM update_bom_ef_lookup();
END;
$$ LANGUAGE plpgsql;
```

### 2.3 EF Lookup Function

```sql
CREATE OR REPLACE FUNCTION update_bom_ef_lookup()
RETURNS VOID AS $$
BEGIN
    -- Update the EF and SF values based on lookup tables
    UPDATE bom b
    SET 
        ef = l.length_ft,
        sf = l.area_ft
    FROM fittings_lookup l
    WHERE b.general_category = l.lookup_category
      AND b.rfq_scope = 'Fittings'
      -- The following implements the smart size matching algorithm
      -- Step 1: Try exact match for size1
      AND ((b.size1 = l.size1 AND b.size2 IS NULL AND l.size2 IS NULL)
      -- Step 2: Try exact match for compound sizes in any order
      OR (b.size1 = l.size1 AND b.size2 = l.size2)
      OR (b.size1 = l.size2 AND b.size2 = l.size1));
      
    -- Handle cases where no exact match is found (implement fallback logic)
    -- This would implement the fallback logic to use larger size
    UPDATE bom b
    SET 
        ef = l.length_ft,
        sf = l.area_ft
    FROM fittings_lookup l
    WHERE b.general_category = l.lookup_category
      AND b.rfq_scope = 'Fittings'
      AND b.ef IS NULL  -- No match found yet
      -- Use larger size for compound fittings
      AND b.size1 > 0 AND b.size2 > 0
      AND l.size1 = GREATEST(b.size1, b.size2)
      AND l.size2 IS NULL;
      
    -- Implement nearest size approximation for remaining unmatched items
    -- This would be more complex logic to find the nearest size
END;
$$ LANGUAGE plpgsql;
```

## Step 3: BOM to General Merge Implementation

### 3.1 Group BOM Data by Category

```sql
CREATE OR REPLACE FUNCTION group_bom_data()
RETURNS TABLE(
    pdf_id INTEGER,
    general_category VARCHAR(100),
    material_description TEXT,
    size1 NUMERIC,
    size2 NUMERIC,
    quantity NUMERIC,
    rfq_scope VARCHAR(100),
    ef NUMERIC,
    sf NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.pdf_id,
        b.general_category,
        b.material_description,
        b.size1,
        b.size2,
        SUM(b.quantity) as quantity,
        b.rfq_scope,
        b.ef,
        b.sf
    FROM bom b
    GROUP BY 
        b.pdf_id,
        b.general_category,
        b.material_description,
        b.size1,
        b.size2,
        b.rfq_scope,
        b.ef,
        b.sf;
END;
$$ LANGUAGE plpgsql;
```

### 3.2 Handle Special Categories and Size Management

```sql
CREATE OR REPLACE FUNCTION handle_special_categories()
RETURNS VOID AS $$
DECLARE
    pdf_id_val INTEGER;
    size_val NUMERIC;
    special_cat_cursor CURSOR FOR
        SELECT DISTINCT b.pdf_id, b.size1
        FROM bom b
        WHERE UPPER(b.general_category) = 'LF'  -- Linear Feet
        AND b.size1 IS NOT NULL;
BEGIN
    -- Process special categories (like 'LF')
    OPEN special_cat_cursor;
    LOOP
        FETCH special_cat_cursor INTO pdf_id_val, size_val;
        EXIT WHEN NOT FOUND;
        
        -- Check if this size already exists for this pdf_id
        IF NOT EXISTS (
            SELECT 1 FROM general g 
            WHERE g.pdf_id = pdf_id_val 
            AND g.size = size_val::TEXT
        ) THEN
            -- Check if there's a row with NULL/empty size
            IF EXISTS (
                SELECT 1 FROM general g 
                WHERE g.pdf_id = pdf_id_val 
                AND (g.size IS NULL OR g.size = '' OR g.size = 'nan')
                LIMIT 1
            ) THEN
                -- Update the first blank size row
                UPDATE general
                SET size = size_val::TEXT
                WHERE id = (
                    SELECT id FROM general g 
                    WHERE g.pdf_id = pdf_id_val 
                    AND (g.size IS NULL OR g.size = '' OR g.size = 'nan')
                    LIMIT 1
                );
            ELSE
                -- Create a new row based on an existing one
                INSERT INTO general (
                    pdf_id, size, lf, ea, -- other category fields
                    -- other necessary fields
                )
                SELECT 
                    pdf_id_val, size_val::TEXT, 0, 0, -- initialize with zero quantities
                    -- copy other fields from existing row
                FROM general
                WHERE pdf_id = pdf_id_val
                LIMIT 1;
            END IF;
        END IF;
    END LOOP;
    CLOSE special_cat_cursor;
END;
$$ LANGUAGE plpgsql;
```

### 3.3 Update General Data Quantities

```sql
CREATE OR REPLACE FUNCTION update_general_data_quantities()
RETURNS VOID AS $$
DECLARE
    r RECORD;
    category_column TEXT;
    update_stmt TEXT;
BEGIN
    -- This implements the category mapping and quantity aggregation
    FOR r IN SELECT * FROM group_bom_data()
    LOOP
        -- Map general category to the correct column in general table
        -- This would use the general_map logic from Python
        IF UPPER(r.general_category) IN ('LF', 'EA', 'SF') THEN
            category_column := LOWER(r.general_category);
        ELSE
            -- Default mapping or handle custom categories
            category_column := 'other';
        END IF;
        
        -- Find matching row in general
        update_stmt := format(
            'UPDATE general SET %I = %I + $1 * COALESCE($2, 1) 
             WHERE pdf_id = $3 AND size = $4',
            category_column, category_column
        );
        
        -- Apply the appropriate calculation based on rfq_scope
        IF r.rfq_scope = 'Fittings' AND r.ef IS NOT NULL THEN
            -- For fittings, multiply by equivalent factor
            EXECUTE update_stmt USING r.quantity, r.ef, r.pdf_id, r.size1::TEXT;
        ELSE
            -- For other items, use direct quantity
            EXECUTE update_stmt USING r.quantity, 1, r.pdf_id, r.size1::TEXT;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 3.4 Cleanup General Data

```sql
CREATE OR REPLACE FUNCTION cleanup_general_data()
RETURNS VOID AS $$
BEGIN
    -- Remove rows with no data or zero quantities
    DELETE FROM general
    WHERE (lf = 0 OR lf IS NULL)
      AND (ea = 0 OR ea IS NULL)
      -- Check other category columns as well
      AND (size IS NULL OR size = '' OR size = 'nan');
END;
$$ LANGUAGE plpgsql;
```

## Step 4: Complete Merge Function

```sql
CREATE OR REPLACE FUNCTION complete_rfq_bom_general_merge()
RETURNS VOID AS $$
BEGIN
    -- Step 1: Process RFQ data
    PERFORM update_rfq_sizes();
    
    -- Step 2: Merge RFQ into BOM
    PERFORM merge_rfq_into_bom();
    
    -- Step 3: Group BOM data
    -- (This is handled within the next steps)
    
    -- Step 4: Handle special categories and size management
    PERFORM handle_special_categories();
    
    -- Step 5: Update General data quantities
    PERFORM update_general_data_quantities();
    
    -- Step 6: Clean up General data
    PERFORM cleanup_general_data();
    
    -- Log completion
    RAISE NOTICE 'RFQ to BOM to General merge completed successfully';
END;
$$ LANGUAGE plpgsql;
```

## Step 5: Trigger Implementation

Create triggers to automatically execute these functions when relevant data changes:

```sql
CREATE OR REPLACE FUNCTION trigger_rfq_update()
RETURNS TRIGGER AS $$
BEGIN
    -- When RFQ is updated, process the update to BOM
    PERFORM merge_rfq_into_bom();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER rfq_after_update
AFTER UPDATE ON rfq
FOR EACH STATEMENT
EXECUTE FUNCTION trigger_rfq_update();

CREATE TRIGGER rfq_after_insert
AFTER INSERT ON rfq
FOR EACH STATEMENT
EXECUTE FUNCTION trigger_rfq_update();
```

## Expected Behavior and Logic Flow

1. **RFQ Processing**:
   - Size fields are parsed and split into size1 and size2
   - Additional categorization from lookup tables is applied

2. **RFQ to BOM Merge**:
   - Materials in BOM are matched with RFQ entries by description and size
   - Matching BOM entries are updated with RFQ categories and attributes
   - Equivalent factors (EF) and surface factors (SF) are calculated using lookup tables
   - Smart size matching is applied:
     * First attempts exact matches
     * Then tries size reversals (size1=4, size2=2 vs size1=2, size2=4)
     * Falls back to larger size for compound fittings
     * Uses nearest size approximation as a final fallback

3. **BOM to General Merge**:
   - BOM data is grouped by PDF ID, category, and size
   - Quantities are summed within groups
   - Special category handling:
     * For categories like 'LF', ensures appropriate size values in General
     * Creates new rows or updates existing blank rows to accommodate all sizes
   - Quantities are aggregated into the appropriate columns in General:
     * Category columns (lf, ea, sf, etc.) are updated based on the general_category
     * Quantities are multiplied by equivalent factors for Fittings
   - Rows with no meaningful data are cleaned up

4. **Row Creation for Sizes**:
   - When a new size is encountered in BOM (particularly for category 'LF'):
     * First checks if this size already exists for the PDF ID
     * If not, tries to find a row with blank/null size for that PDF ID
     * If found, updates that row with the new size
     * If no blank row is available, creates a new row by copying an existing row for that PDF ID
     * Initializes all quantity columns to zero

5. **Trigger-Based Updates**:
   - Changes to RFQ trigger the merge process automatically
   - This ensures that BOM and General data stay consistent with the latest RFQ information

## Implementation Notes

1. The PostgreSQL implementation should maintain the same logical flow as the Python code, but leverage database-specific features for efficiency.

2. Consider adding appropriate indexes to improve performance, especially on frequently joined columns:
   ```sql
   CREATE INDEX idx_bom_material_size ON bom(material_description, size1, size2);
   CREATE INDEX idx_general_pdf_size ON general(pdf_id, size);
   ```

3. Consider implementing row-level locks or transaction isolation levels to prevent data inconsistency during the merge process.

4. Add proper error handling and logging for troubleshooting.

5. For large datasets, consider using temporary tables for intermediate results to improve performance.

This implementation guide provides the core logic needed to recreate the RFQ to BOM to General merge functionality in a PostgreSQL database. The functions should be further refined based on specific application requirements and tested thoroughly.
