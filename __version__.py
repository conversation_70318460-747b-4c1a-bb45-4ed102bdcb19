# Include build details. Some vars will be overridden during build using __version2__
import datetime
def get_time_string():
    return datetime.datetime.now().strftime("%Y%m%d%H%M%S")

title = "ATEM"
version = "0.15.5"
mode = "dev"
build_date = get_time_string()

# Overrides
try:
    if __file__.endswith(".pyc"):
        import __version2__
        title = __version2__.title
        version = __version2__.version
        mode = __version2__.mode
        build_date = __version2__.build_date
except Exception:
    # Dev mode
    pass


def get_base_app_title():
    m = mode.lower()
    m = m.replace("_", " ")
    v = "" if m == "release" else f" [{m.upper()}]"
    return f"{title} {version}{v}"
