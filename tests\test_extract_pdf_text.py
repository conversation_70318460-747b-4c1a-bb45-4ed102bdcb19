import fitz  # PyMuPDF
import pandas as pd
import re

filename = "C:/Drawings/Clients/brockservices/BRS_0026 - BSLTX34469 - 2025-1231 - OLIN C-650 AIR COMPRESSOR REPLACEMENT/received/20250804+C-650_11x17_Combined.pdf"
doc = fitz.open(filename)
page = doc[11]  # or any page number

# Get all words on the page
words = page.get_text("words")  # list of (x0, y0, x1, y1, word, block_no)

print(words)

print(words[0])
print(len(words[0]))  # Should be 8

# print(fitz.__version__)

df = pd.DataFrame(words, columns=[
    "x0", "y0", "x1", "y1", "word", "block_no", "line_no", "word_no"
])
df = df.sort_values(by=["y0", "x0"]).reset_index(drop=True)

original_phrase_bboxes = []
phrase_bboxes = []
for annot in page.annots():
    rect = annot.rect
    content = annot.info.get("content", "").strip()
    original_phrase_bboxes.append((content, rect))

    if not content:
        continue

    lines = content.splitlines()
    num_lines = len(lines)
    max_line_length = max((len(line) for line in lines), default=1)

    char_width = rect.width / max_line_length
    line_height = rect.height / num_lines


    for i, line in enumerate(lines):
        phrases = re.split(r'\s{2,}', line)
        cursor = 0

        print(rect.width, len(line), char_width)

        for phrase in phrases:
            start_idx = line.find(phrase, cursor)
            end_idx = start_idx + len(phrase)

            x0 = rect.x0 + start_idx * char_width
            x1 = rect.x0 + end_idx * char_width
            y0 = rect.y0 + i * line_height
            y1 = y0 + line_height

            bbox = fitz.Rect(x0, y0, x1, y1)
            phrase_bboxes.append((phrase, bbox))
            cursor = end_idx

    # Output example
    for phrase, bbox in phrase_bboxes:
        print(f"{phrase!r} → {bbox}")

def save_image(phrases, save_file_name="debug/annotated.png", color="red"):
    from PIL import Image, ImageDraw
    # Render page to pixmap (RGB)
    pix = page.get_pixmap(dpi=150)
    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
    draw = ImageDraw.Draw(img)

    scale = 150 / 72  # dpi / 72

    for phrase, bbox in phrases:
        bbox_scaled = fitz.Rect(bbox.x0 * scale, bbox.y0 * scale, bbox.x1 * scale, bbox.y1 * scale)
        draw.rectangle(bbox_scaled, outline=color, width=2)
        draw.text((bbox_scaled.x0, bbox_scaled.y0), phrase, fill=color)
        # print(phrase)

    img.save(save_file_name)

save_image(phrase_bboxes, save_file_name="debug/annotated_phrase.png", color="red")
save_image(original_phrase_bboxes, save_file_name="debug/annotated_original.png", color="blue")
# annot_rect = annot.rect  # your annotation
# df_in_annot = df[
#     (df["x0"] >= annot_rect.x0) & (df["x1"] <= annot_rect.x1) &
#     (df["y0"] >= annot_rect.y0) & (df["y1"] <= annot_rect.y1)
# ]

df.to_excel("debug/words.xlsx")
# print(df_in_annot)
