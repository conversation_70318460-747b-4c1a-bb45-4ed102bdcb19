from src.utils.logger import logger
from PySide6.QtWidgets import QWidget, QLabel, QFormLayout, QSizePolicy, QPushButton
from PySide6.QtCore import Signal, QSize, Qt
from PySide6.QtGui import QCursor, QResizeEvent, QShortcut, QKeySequence
from src.pyside_util import get_resource_qicon


class BaseForm(QWidget):

    # Name to switch to; handled by FormsView
    sgnSwitchTo = Signal(str, (str, ), (str, dict, ))
    sgnInitDefaults = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self._projects = None
        # Override this to allow dynamic sizing of form
        self.formSize = QSize(500, 540)
        # Provide all forms with a standard title and subtitle
        self.setObjectName("formWidget")
        self.setLayout(QFormLayout())
        self.title = QLabel("Title")
        self.layout().addRow(self.title)
        self.title.setObjectName("titleLabel")
        self.subtitle = QLabel("Subtitle")
        self.subtitle.setContentsMargins(0, 4, 0, 8)
        self.layout().addRow(self.subtitle)
        self.subtitle.setObjectName("headerLabel")
        self.errorMsg = QLabel()
        self.errorMsg.hide()
        self.errorMsg.setObjectName("formError")

        # Small utility button anchored to top right
        self.pbFloating = QPushButton("", self)
        self.pbFloating.setFixedSize(24, 24)
        self.pbFloating.setIcon(get_resource_qicon("x.svg"))
        self.pbFloating.hide()

        self.initUi()
        self.initDefaults()
        self.resizeEvent(None)
        self.sgnInitDefaults.connect(self.initDefaults)
    
    def layout(self) -> QFormLayout:
        return super().layout()

    def initUi(self):
        """ Override this with custom form UI """
        raise NotImplementedError

    def initDefaults(self):
        """ Initialize default state """
        logger.warn(f"initDefaults not implemented: {self.__class__}")

    def addStretchSpacer(self):
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().addRow(spacer)
    
    def addVSpace(self, height: int = 16):
        """ Insert some vertical space """
        spacer = QWidget()
        spacer.setFixedHeight(height)
        self.layout().addRow(spacer)

    def animateIn(self):
        pass

    def animateOut(self):
        pass

    def updateObjectName(self, widget: QWidget, objectName: str):
        """ Required for updating style post-init """
        self.style().unpolish(widget)
        widget.setObjectName(objectName)
        self.style().polish(widget)

    def setWidgetError(self, widget: QWidget):
        """ Helper to set any widget to an error state style.
        See `formError` in stylesheet
        """
        self.updateObjectName(widget, "formError")

    def setWidgetDefault(self, widget: QWidget):
        """ Helper to set any widget to an error state style.
        See `formError` in stylesheet
        """
        self.updateObjectName(widget, None)
    
    def setBusyState(self):
        self.setEnabled(False)
        self.setCursor(QCursor(Qt.CursorShape.WaitCursor))

    def setNormalState(self):
        self.setEnabled(True)
        self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
    
    def getData(self) -> dict:
        """ For passing data between forms """
        return {}

    def addErrorStatusWidget(self):
        """ By default, widget is not added. This should be called per-form at the
        appropriate position """
        self.layout().addRow(self.errorMsg)

    def hideErrorStatus(self):
        self.errorMsg.hide()

    def setErrorStatusMessage(self, message: str):
        try:
            self.errorMsg.setText(message)
        except Exception as e:
            print("Error Setting Message", e)
        self.errorMsg.show()
    
    def onCancel(self):
        pass

    def resizeEvent(self, event: QResizeEvent) -> None:
        self.pbFloating.move(self.width() - self.pbFloating.width() - 8, 8)
    
    def onFloatingButton(self):
        pass

    def setFloatingButton(self, name, visible: bool = True):
        if name == "back":
            icon = get_resource_qicon("arrow-left.svg")
        elif name == "cancel":
            icon = get_resource_qicon("x.svg")
        self.pbFloating.setIcon(icon)
        self.pbFloating.setVisible(visible)
        self.pbFloating.clicked.connect(self.onFloatingButton)
    
    def setFloatingButtonBack(self, visible: bool = True):
        self.setFloatingButton("back", visible)

    def setFloatingButtonCancel(self, visible: bool = True):
        self.setFloatingButton("cancel", visible)
    
    def setProjectsData(self, data):
        self._projects = data

    def setParams(self, params):
        logger.info("Params have been passed but `setParams` has not been implemented")
    
    def setButtonActivateOnEnter(self, btn: QPushButton, callback, enabled: bool = True):

        shortcut = QShortcut(QKeySequence(Qt.Key.Key_Return), self)
        shortcut.activated.connect(callback)
        shortcut.setEnabled(False)

        def focusIn(event, btn: QPushButton):
            shortcut.setEnabled(True)
            super(QPushButton, btn).focusInEvent(event)

        def focusOut(event, btn: QPushButton):
            shortcut.setEnabled(False)
            super(QPushButton, btn).focusOutEvent(event)

        btn.focusInEvent = lambda event: focusIn(event, btn)
        btn.focusOutEvent = lambda event: focusOut(event, btn)