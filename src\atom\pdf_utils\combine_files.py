
import os
import fitz  # PyMuPDF
import pandas as pd
import openpyxl
from time import perf_counter

def detailed_pdf_diagnosis(pdf_path):
    """
    Performs a detailed analysis of PDF structure and first few pages.
    Examines metadata, page structure, fonts, images, and other PDF components to help diagnose issues.
    """
    print(f"\nDetailed PDF Analysis: {os.path.basename(pdf_path)}")
    print("-" * 50)

    with fitz.open(pdf_path) as doc:
        try:
            print(f"Total pages: {len(doc)}")
            print(f"File size: {os.path.getsize(pdf_path) / (1024*1024):.2f} MB")

            # Get basic document info
            print("\nDocument Info:")
            print(f"Is Encrypted: {doc.is_encrypted}")
            if hasattr(doc, 'is_repaired'):
                print(f"Is Repaired: {doc.is_repaired}")

            # Get metadata
            print("\nMetadata:")
            for key, value in doc.metadata.items():
                if value:
                    print(f"{key}: {value}")

            # Sample a few pages across the document
            sample_pages = [0, len(doc)//2, len(doc)-1]  # First, middle, and last page
            print("\nSampling pages across document...")

            for page_num in sample_pages:
                print(f"\nAnalyzing page {page_num + 1}:")
                try:
                    page = doc[page_num]
                    print(f"Rotation: {page.rotation}")
                    print(f"Page Size: {page.rect.width:.0f} x {page.rect.height:.0f}")

                    # Try to get page structure info
                    try:
                        images = list(page.get_images())
                        print(f"Images on page: {len(images)}")
                    except Exception as e:
                        print(f"Could not get images: {str(e)}")

                    try:
                        fonts = list(page.get_fonts())
                        print(f"Fonts on page: {len(fonts)}")
                        if fonts:
                            print("Font types:", set(font[0] for font in fonts))
                    except Exception as e:
                        print(f"Could not get fonts: {str(e)}")

                    try:
                        # Get rough size of page content
                        text = page.get_text("text")
                        print(f"Text length: {len(text)} characters")
                    except Exception as e:
                        print(f"Could not get text: {str(e)}")

                except Exception as e:
                    print(f"Error analyzing page {page_num + 1}: {str(e)}")

            # Try to get TOC size
            try:
                toc = doc.get_toc()
                print(f"\nTOC entries: {len(toc)}")
            except Exception as e:
                print(f"Could not get TOC: {str(e)}")

            # Check for file attachments
            try:
                names = doc.embfile_names()
                if names:
                    print(f"\nEmbedded files: {len(names)}")
            except Exception as e:
                print(f"Could not check for embedded files: {str(e)}")

        except Exception as e:
            print(f"Error during document analysis: {str(e)}")

def optimize_pdf_splitting(input_pdf_path, mapping_file_path, output_directory, batch_size=20):
    """
    Splits a PDF into multiple group-based PDFs using an Excel mapping file.
    Optimized version focusing on direct page copying with minimal processing for better performance.
    Uses batching to handle large files efficiently and disables annotations and links.
    """
    print("\nStarting optimized PDF processing...")

    # Read mapping data
    df = pd.read_excel(mapping_file_path)
    grouped_pages = df.groupby('group_number')['page_number'].apply(list).to_dict()
    os.makedirs(output_directory, exist_ok=True)
    base_filename = os.path.splitext(os.path.basename(input_pdf_path))[0]

    with fitz.open(input_pdf_path) as input_pdf:
        # Disable structure processing
        input_pdf.set_toc([])  # Clear TOC

        for group_num, pages in grouped_pages.items():
            print(f"\nProcessing group {group_num}...")
            start_time = perf_counter()

            try:
                # Create output PDF with minimal structure
                output_pdf = fitz.open()

                # Convert to 0-based indexing
                pages_zero_indexed = [page - 1 for page in pages]

                # Process in batches
                for i in range(0, len(pages_zero_indexed), batch_size):
                    batch = pages_zero_indexed[i:i + batch_size]
                    print(f"Processing pages {i+1}-{i+len(batch)} of {len(pages_zero_indexed)}")

                    # Copy pages with minimal processing
                    for page_num in batch:
                        output_pdf.insert_pdf(
                            input_pdf,
                            from_page=page_num,
                            to_page=page_num,
                            annots=False,
                            links=False
                        )

                # Save with minimal processing
                output_path = os.path.join(
                    output_directory,
                    f"{base_filename}_group_{group_num}.pdf"
                )

                print(f"Saving group {group_num}...")
                output_pdf.save(
                    output_path,
                    deflate=False,
                    clean=False,  # Disable cleaning
                    garbage=0,    # Disable garbage collection
                    pretty=False,
                    ascii=False
                )

                end_time = perf_counter()
                print(f"Group {group_num} completed in {end_time - start_time:.2f} seconds")

            except Exception as e:
                print(f"Error processing group {group_num}: {str(e)}")

            finally:
                if 'output_pdf' in locals():
                    output_pdf.close()

    print("\nProcessing complete!")


def diagnose_pdf(pdf_path):
    """
    Diagnoses potential issues in a PDF that might cause slow processing.
    Performs a quick analysis of PDF structure, checking for TOC entries, annotations, links, and form fields
    that could impact processing performance.
    """
    print(f"\nDiagnosing PDF: {os.path.basename(pdf_path)}")
    print("-" * 50)

    with fitz.open(pdf_path) as doc:
        # Basic information
        print(f"Total pages: {len(doc)}")
        print(f"File size: {os.path.getsize(pdf_path) / (1024*1024):.2f} MB")

        # Check for TOC
        toc = doc.get_toc()
        print(f"TOC entries: {len(toc)}")

        # Check first page for potential issues
        page = doc[0]

        # Check for annotations - convert generator to list first
        annots = list(page.annots()) if page.annots() else []
        print(f"Annotations on first page: {len(annots)}")

        # Check for links
        links = page.get_links()
        print(f"Links on first page: {len(links)}")

        # Check for forms
        widgets = list(page.widgets())  # Convert generator to list
        print(f"Form fields on first page: {len(widgets)}")

        # Get metadata
        metadata = doc.metadata
        print("\nMetadata:")
        for key, value in metadata.items():
            if value:
                print(f"{key}: {value}")

def group_pdf_pages_optimized(input_pdf_path, mapping_file_path, output_directory, batch_size=5):
    """
    Highly optimized version with TOC removal and smaller batches.
    Splits a PDF into multiple group-based PDFs using an Excel mapping file with detailed progress tracking.
    Uses very small batch sizes (default 5) for better memory management and returns detailed results dictionary.
    """
    print("\nPreparing to process PDF...")

    # Read and prepare mapping data
    df = pd.read_excel(mapping_file_path)
    grouped_pages = df.groupby('group_number')['page_number'].apply(list).to_dict()
    os.makedirs(output_directory, exist_ok=True)
    base_filename = os.path.splitext(os.path.basename(input_pdf_path))[0]
    results = {}

    print("Opening input PDF...")
    with fitz.open(input_pdf_path) as input_pdf:
        print("Clearing TOC to improve performance...")
        input_pdf.set_toc([])  # Clear the TOC

        # Process each group
        for group_num, pages in grouped_pages.items():
            print(f"\nProcessing group {group_num}...")
            start_time = perf_counter()

            # Create new PDF for this group
            output_pdf = fitz.open()

            try:
                pages_zero_indexed = [page - 1 for page in pages]
                total_pages = len(pages_zero_indexed)

                # Process in very small batches
                for i in range(0, total_pages, batch_size):
                    batch_start = perf_counter()
                    batch_pages = pages_zero_indexed[i:i + batch_size]
                    current_batch = i//batch_size + 1
                    total_batches = (total_pages + batch_size - 1)//batch_size

                    print(f"Processing batch {current_batch}/{total_batches} (pages {i+1}-{min(i+batch_size, total_pages)})")

                    for page_num in batch_pages:
                        try:
                            output_pdf.insert_pdf(
                                input_pdf,
                                from_page=page_num,
                                to_page=page_num,
                                annots=False,
                                links=False
                            )
                        except Exception as e:
                            print(f"Error on page {page_num + 1}: {str(e)}")
                            continue

                    batch_time = perf_counter() - batch_start
                    print(f"Batch completed in {batch_time:.2f} seconds")

                print(f"Saving group {group_num}...")
                output_filename = f"{base_filename}_group_{group_num}.pdf"
                output_path = os.path.join(output_directory, output_filename)

                output_pdf.save(
                    output_path,
                    deflate=False,
                    pretty=False,
                    ascii=False
                )

                process_time = perf_counter() - start_time
                results[group_num] = {
                    'pages': len(output_pdf),
                    'time': process_time
                }
                print(f"Group {group_num} completed in {process_time:.2f} seconds")

            except Exception as e:
                print(f"Error processing group {group_num}: {str(e)}")
                results[group_num] = {'error': str(e)}

            finally:
                output_pdf.close()

    return results

def process_pdf_with_diagnostics(input_pdf_path, mapping_file_path, output_directory):
    """
    Process PDF with diagnostics and progress reporting.
    Combines diagnostic analysis with optimized PDF splitting in a single workflow.
    Runs diagnostics first, then processes the PDF with optimizations, and provides a detailed summary report.
    """
    print("Starting PDF processing with diagnostics...")

    # Run diagnostics first
    diagnose_pdf(input_pdf_path)

    # Process the PDF with optimizations
    total_start = perf_counter()
    results = group_pdf_pages_optimized(input_pdf_path, mapping_file_path, output_directory)

    # Print summary
    total_time = perf_counter() - total_start
    print("\nProcessing Summary:")
    print("-" * 50)
    print(f"Total processing time: {total_time:.2f} seconds")

    successful_groups = {k: v for k, v in results.items() if isinstance(v, dict) and 'pages' in v}
    if successful_groups:
        total_pages = sum(v['pages'] for v in successful_groups.values())
        print(f"Total pages processed: {total_pages}")
        print(f"Average time per page: {total_time/total_pages:.2f} seconds")

    return results

def check_text_in_page(page, text_string):
    """
    Simple utility function to check if a specific text string exists in a PDF page.
    Returns True if the text is found, False otherwise.
    """
    return text_string in page.get_text()


def split_pdf_individual(input_path, output_path):
    """
    Splits a PDF into individual single-page PDFs.
    Takes an input PDF and creates a separate PDF file for each page in the specified output directory.
    Useful for breaking down multi-page documents for individual processing.
    """
    # Open the PDF
    doc = fitz.open(input_path)

    # # Create output directory if it doesn't exist
    # os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Iterate through pages
    for page_num in range(len(doc)):
        # Create a new PDF with just this page
        new_doc = fitz.open()
        new_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)

        # Save the single page as a new PDF
        output_file = os.path.join(output_path, f"page_{page_num + 1}.pdf")
        new_doc.save(output_file)
        new_doc.close()

        print(f"Saved page {page_num + 1} as {output_file}")

    # Close the original document
    doc.close()


def split_pdf_pages(input_pdf_path, pages_to_remove, output_original_path, output_removed_path, mapping_file_path):
    """
    Splits a PDF into two new PDFs based on specified pages to remove and creates a mapping Excel file.

    Args:
    input_pdf_path (str): Path to the input PDF file.
    pages_to_remove (list): List of page numbers to remove (1-indexed).
    output_original_path (str): Path to save the PDF with remaining pages.
    output_removed_path (str): Path to save the PDF with removed pages.
    mapping_file_path (str): Path to save the Excel mapping file.

    Returns:
    tuple: (pages_in_original, pages_in_removed)
    """
    # Open the input PDF
    input_pdf = fitz.open(input_pdf_path)

    # Create new PDFs for the split documents
    pdf_original = fitz.open()
    pdf_removed = fitz.open()

    # Adjust page numbers to 0-indexing
    pages_to_remove = [page - 1 for page in pages_to_remove]

    # Prepare data for Excel mapping
    mapping_data = []
    original_new_page = 1
    removed_new_page = 1

    # Get the filename without path
    input_filename = os.path.basename(input_pdf_path)

    # Iterate through all pages of the input PDF
    for page_num in range(len(input_pdf)):
        if page_num in pages_to_remove:
            pdf_removed.insert_pdf(input_pdf, from_page=page_num, to_page=page_num)
            mapping_data.append({
                'File': input_filename,
                'Original Page': page_num + 1,
                'New Page': f'Removed-{removed_new_page}'
            })
            removed_new_page += 1
        else:
            pdf_original.insert_pdf(input_pdf, from_page=page_num, to_page=page_num)
            mapping_data.append({
                'File': input_filename,
                'Original Page': page_num + 1,
                'New Page': original_new_page
            })
            original_new_page += 1

    # Save the new PDFs
    pdf_original.save(output_original_path)
    pdf_removed.save(output_removed_path)

    # Create and save the Excel mapping file
    df = pd.DataFrame(mapping_data)
    df.to_excel(mapping_file_path, index=False)

    input_len, og_with_removed, extracted_pages = len(input_pdf), len(pdf_original), len(pdf_removed)

    # Close all PDF objects
    input_pdf.close()
    pdf_original.close()
    pdf_removed.close()

    return og_with_removed, extracted_pages

def combine_pdf_in_folder(folder_path, output_filename, include_phrases=None, exclude_phrases=None, mapping_file=None, exclude_directories=None, deduplicate=False):
    """
    Combines multiple PDF files from a folder into a single PDF with optional filtering and deduplication.
    Recursively walks through all subfolders, can filter files based on phrases in filenames,
    can exclude specific subdirectories by name, and optionally creates a mapping Excel file
    tracking original files and pages.

    Args:
        folder_path: Path to the root folder to process
        output_filename: Path where the combined PDF will be saved
        include_phrases: List of phrases to include in filenames (if None, include all)
        exclude_phrases: List of phrases to exclude from filenames
        mapping_file: Path to save Excel mapping file (if None, no mapping file is created)
        exclude_directories: List of directory names to exclude from processing
        deduplicate: Whether to detect and skip duplicate pages based on visual content
    """
    # Create a new PDF document for the combined file
    pdf_writer = fitz.open()

   #  '''
   #  Inspect 'MuPDF error: format error: cannot find object in xref (120 0 R)
   #  folder: r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Received\Area 1200 - 1065 total"
   #  https://github.com/pymupdf/PyMuPDF/issues/352
   # '''

    # Convert phrases to lowercase for case-insensitive matching
    include_phrases = [phrase.lower() for phrase in (include_phrases or [])]
    exclude_phrases = [phrase.lower() for phrase in (exclude_phrases or [])]
    exclude_directories = exclude_directories or []

    print(f"Processing Files in {folder_path}...")

    # Create a list to store mapping data
    mapping_data = []

    # For deduplication
    page_signatures = {}  # Maps signature -> (file_path, page_num, new_page_num)
    duplicate_count = 0
    total_pages_processed = 0

    # Function to generate a signature for a page
    def get_page_signature(page):
        if not deduplicate:
            return None  # Skip signature generation if deduplication is disabled

        try:
            # Get full resolution pixmap for maximum accuracy
            pix = page.get_pixmap()

            # Get the raw image data
            img_data = pix.samples

            # Generate checksum from the image data
            import hashlib
            return hashlib.sha256(img_data).hexdigest()
        except Exception as e:
            print(f"Error generating page signature: {str(e)}")
            return None  # Return None if signature generation fails

    # Walk through all subfolders and files in the folder
    for root, dirs, files in os.walk(folder_path):
        # Modify dirs in-place to exclude specified directories
        # This prevents os.walk from traversing into those directories
        if exclude_directories:
            dirs[:] = [d for d in dirs if d not in exclude_directories]
        for file in sorted(files):
            if file.endswith('.pdf'):
                file_path = os.path.join(root, file)
                file_lower = file.lower()

                # Check if file should be included based on phrases
                should_include = not include_phrases or any(phrase in file_lower for phrase in include_phrases)
                should_exclude = any(phrase in file_lower for phrase in exclude_phrases)

                if should_include and not should_exclude:
                    pass
                    #print(f"Processing: {file_path}")

                    try:
                        with fitz.open(file_path) as pdf_document:
                            # Record the current page count before inserting new pages
                            start_page = pdf_writer.page_count

                            # Process each page individually for deduplication
                            pages_added = 0

                            for i in range(pdf_document.page_count):
                                total_pages_processed += 1
                                page = pdf_document.load_page(i)

                                # Get page signature if deduplication is enabled
                                if deduplicate:
                                    signature = get_page_signature(page)

                                    # Check if this page is a duplicate
                                    if signature in page_signatures:
                                        duplicate_count += 1
                                        original_info = page_signatures[signature]

                                        # Add to mapping data even if page is not added
                                        if mapping_file:
                                            mapping_data.append({
                                                'Original File': file,
                                                'Full Path': file_path,
                                                'Original Page': i + 1,
                                                'New Page': 'DUPLICATE',
                                                'Duplicate Of': f"{original_info[0]} (Page {original_info[1] + 1})",
                                                'Duplicate New Page': original_info[2] + 1,
                                                'Content': page.get_text()[:500] + "..." if len(page.get_text()) > 500 else page.get_text()
                                            })
                                        continue  # Skip this page

                                    # Store signature for future reference
                                    page_signatures[signature] = (file, i, start_page + pages_added)

                                # Add the page to the combined document
                                pdf_writer.insert_pdf(pdf_document, from_page=i, to_page=i)

                                # Add mapping information
                                if mapping_file:
                                    mapping_data.append({
                                        'Original File': file,
                                        'Full Path': file_path,
                                        'Original Page': i + 1,
                                        'New Page': start_page + pages_added + 1,
                                        'Is Duplicate': False,
                                        'Content': page.get_text()[:500] + "..." if len(page.get_text()) > 500 else page.get_text()
                                    })

                                pages_added += 1


                    except Exception as e:
                        print(f"\n\n      ---> !!! Error processing file '{file}': {e}")
                else:
                    print(f"Skipping: {file_path}")

    # Save and close the combined PDF if it contains any pages
    if pdf_writer.page_count > 0:
        # Store the page count before closing the document
        final_page_count = pdf_writer.page_count

        # Save and close the PDF
        pdf_writer.save(output_filename)
        pdf_writer.close()
        print(f'\n\n--> Complete Combined PDF saved as {output_filename}')

        # Print deduplication statistics if enabled
        if deduplicate:
            print(f"\nDeduplication Statistics:")
            print(f"  Total pages processed: {total_pages_processed}")
            print(f"  Duplicate pages skipped: {duplicate_count}")
            print(f"  Unique pages in combined PDF: {final_page_count}")
            print(f"  Deduplication ratio: {duplicate_count/total_pages_processed*100:.1f}% of pages were duplicates")
    else:
        print("No pages were added to the combined PDF. No file was saved.")

    # Create DataFrame from mapping data and save to Excel if mapping_file is provided
    if mapping_file and mapping_data:
        # Create a DataFrame from the mapping data
        df = pd.DataFrame(mapping_data)

        # Create a summary sheet with statistics
        if deduplicate:
            with pd.ExcelWriter(mapping_file, engine='openpyxl') as writer:
                # Main mapping data
                df.to_excel(writer, sheet_name='Page Mapping', index=False)

                # Summary statistics
                summary_data = {
                    'Metric': [
                        'Total Pages Processed',
                        'Unique Pages in Combined PDF',
                        'Duplicate Pages Skipped',
                        'Deduplication Ratio (%)'
                    ],
                    'Value': [
                        total_pages_processed,
                        final_page_count,
                        duplicate_count,
                        f"{duplicate_count/total_pages_processed*100:.1f}%"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
        else:
            # Simple Excel file without summary if deduplication is disabled
            df.to_excel(mapping_file, index=False)

        print(f'Mapping file saved as {mapping_file}')

def combine_pdf_in_folder_check_string(folder_path, output_filename, text_string, discard_folder):
    """
    Combines PDFs from a folder but filters pages based on text content.
    Checks each page for a specific text string (e.g., "Isometric") and only includes pages containing that text.
    Pages that don't contain the specified text are moved to separate "discard" PDFs in a designated folder.
    """
    # Create a new PDF document for the combined file
    pdf_writer = fitz.open()

    # Ensure discard folder exists
    if not os.path.exists(discard_folder):
        os.makedirs(discard_folder)

    # Walk through all subfolders and files in the folder
    for root, dirs, files in os.walk(folder_path):
        for file in sorted(files):
            if file.endswith('.pdf'):
                file_path = os.path.join(root, file)
                print(f"Processing: {file_path}")

                try:
                    with fitz.open(file_path) as pdf_document:
                        # Check if text_string is None or empty, and set a flag accordingly
                        check_text_string = text_string is not None and text_string != ""
                        # Create a discard document only if needed and text_string check is enabled
                        discard_pdf = None

                        for page_num in range(pdf_document.page_count):
                            page = pdf_document.load_page(page_num)
                            # Modify condition to check if text_string check is disabled or if text_string is in page
                            if not check_text_string or text_string in page.get_text():
                                # Import the page to the combined document
                                pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)
                            elif check_text_string:
                                # Initialize discard document if needed and text_string check is enabled
                                if discard_pdf is None:
                                    discard_pdf = fitz.open()
                                # Import the page to the discard document
                                discard_pdf.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

                        # Save discard document if it has pages and was created
                        if discard_pdf and discard_pdf.page_count > 0:
                            discard_pdf_path = os.path.join(discard_folder, f"discard_{file}")
                            discard_pdf.save(discard_pdf_path)
                            discard_pdf.close()

                except Exception as e:
                    print(f"Error processing file '{file}': {e}")

    # Save and close the combined PDF if it contains any pages
    if pdf_writer.page_count > 0:
        pdf_writer.save(output_filename)
        pdf_writer.close()
        print(f'Combined PDF saved as {output_filename}')
    else:
        print("No pages were added to the combined PDF. No file was saved.")

def extract_and_combine_pages(excel_path, pdf_path, output_filename):
    """
    Extracts specific pages from a PDF based on an Excel list and combines them.
    Takes page numbers from an Excel file (first column), extracts those specific pages from a source PDF,
    and creates a new combined PDF with just those pages.
    """
    # Read page numbers from the Excel file
    page_numbers = pd.read_excel(excel_path, header=None, dtype=int).iloc[:, 0]

    # Create a new PDF document for the combined file
    pdf_writer = fitz.open()

    # Open the source PDF document
    with fitz.open(pdf_path) as pdf_document:
        for page_num in page_numbers:
            # Page numbers in Excel are likely 1-based, PDF pages are 0-based
            page = pdf_document.load_page(page_num - 1)
            # Import the page to the combined document
            pdf_writer.insert_pdf(pdf_document, from_page=page_num - 1, to_page=page_num - 1)

    # Save and close the combined PDF
    pdf_writer.save(output_filename)
    pdf_writer.close()

    print(f'Combined PDF saved as {output_filename}')

def combine_pdf_in_directory(source_directory, output_filename):
    """
    Combines all PDF files in a single directory (non-recursive) into one PDF.
    Simple implementation that processes only the files in the specified directory without subdirectories.
    Does not create mapping files or provide filtering options.
    """
    # Create a new PDF document for the combined file
    combined_pdf_writer = fitz.open()

    # Iterate over all files in the directory
    for filename in os.listdir(source_directory):
        # Construct the full file path
        file_path = os.path.join(source_directory, filename)
        # Check if the current file is a PDF
        if os.path.isfile(file_path) and filename.lower().endswith('.pdf'):
            # Open the current PDF
            with fitz.open(file_path) as pdf_document:
                # Iterate through each page of the current PDF
                for page_num in range(len(pdf_document)):
                    # Get the page object
                    page = pdf_document.load_page(page_num)
                    # Import the page to the combined document
                    combined_pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

    # Save and close the combined PDF
    combined_pdf_writer.save(output_filename)
    combined_pdf_writer.close()

    print(f'Combined PDF saved as {output_filename}')

def count_total_pdf_pages(directory):
    """
    Recursively counts the total number of pages in all PDF files within a directory and its subdirectories.
    Useful for verifying page counts before and after PDF processing operations.
    """
    print(f"Recursively counting pages in Directory: {directory}")
    total_pages = 0
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.pdf'):
                file_path = os.path.join(root, file)
                try:
                    with fitz.open(file_path) as pdf:
                        total_pages += len(pdf)
                except Exception as e:
                    print(f"Error processing {file_path}: {str(e)}")
    return total_pages

def combine_pdf_in_directory_recursive(source_directory, output_filename, excel_filename):
    """
    Combines PDFs from a directory and all its subdirectories with detailed tracking.
    Creates a comprehensive Excel file with two sheets: "File Summary" tracking source files and page ranges,
    and "Page Details" tracking individual page mapping between source and combined PDF.
    Provides detailed progress reporting and verification of page counts.
    """
    # Create a new PDF document for the combined file
    combined_pdf_writer = fitz.open()
    # List to store file information
    file_info = []
    page_info = []
    # Current page number in the combined PDF
    current_page = 0
    total_pages = 0

    # Function to process PDFs in a directory
    def process_directory(directory):
        nonlocal current_page, total_pages
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isfile(item_path) and item.lower().endswith('.pdf'):
                with fitz.open(item_path) as pdf_document:
                    num_pages = len(pdf_document)
                    print(f"Processing: {item_path} - Pages: {num_pages}")
                    total_pages += num_pages # Update total pages

                    for page_num in range(num_pages):
                        page = pdf_document.load_page(page_num)
                        combined_pdf_writer.insert_pdf(pdf_document, from_page=page_num, to_page=page_num)

                    # Add page tracking
                    page_info.append({
                        'Source Path': item_path,
                        'Original Page': page_num + 1,
                        'Combined PDF Page': current_page + page_num + 1
                    })

                    # Add file information to the list
                    file_info.append({
                        'Source Path': item_path,
                        'Start Page': current_page + 1,
                        'End Page': current_page + num_pages
                    })
                    current_page += num_pages
            elif os.path.isdir(item_path):
                process_directory(item_path)

    # Start processing from the source directory
    process_directory(source_directory)

    # Save and close the combined PDF
    combined_pdf_writer.save(output_filename)
    combined_pdf_writer.close()

    print(f'Combined PDF saved as {output_filename}')
    print(f'Total pages processed: {total_pages}')
    print(f'Total pages in combined PDF: {current_page}')

    # Create a DataFrame from the file information
    df = pd.DataFrame(file_info)

    # Save the DataFrame to an Excel file
    # df.to_excel(excel_filename, index=False)

    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        pd.DataFrame(file_info).to_excel(writer, sheet_name='File Summary', index=False)
        pd.DataFrame(page_info).to_excel(writer, sheet_name='Page Details', index=False)

    print(f'Excel file with source paths and page numbers saved as {excel_filename}')

def extract_filenames_to_excel(folder_path, output_excel_path):
    """
    Creates an Excel file listing all files in a directory and its subdirectories.
    Recursively walks through the folder structure and records full file paths and filenames.
    Useful for creating file inventories or preparing for batch processing operations.
    """
    # List to store file information
    file_info = []

    # Walk through all subfolders and files in the folder
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            full_filepath = os.path.join(root, file)
            file_info.append({
                'full_filepath': full_filepath,
                'filename': file
            })

    # Create a DataFrame from the file information
    df = pd.DataFrame(file_info)

    # Save the DataFrame to an Excel file
    df.to_excel(output_excel_path, index=False)

    print(f"File information saved to {output_excel_path}")

def append_pdfs_to_existing(existing_pdf_path, pdf_paths_to_append):
    """
    Appends multiple PDFs to an existing PDF file.
    Takes an existing PDF and a list of PDFs to append, modifying the existing PDF in place.
    Uses a temporary file for safety to ensure the original isn't corrupted if the operation fails.
    """
    # Open the existing PDF
    existing_pdf = fitz.open(existing_pdf_path)

    # Iterate through the list of PDFs to append
    for pdf_path in pdf_paths_to_append:
        # Check if the file exists
        if os.path.isfile(pdf_path):
            try:
                # Open the PDF to append
                with fitz.open(pdf_path) as pdf_to_append:
                    # Insert all pages from pdf_to_append to existing_pdf
                    existing_pdf.insert_pdf(pdf_to_append)
                print(f"Appended: {pdf_path}")
            except Exception as e:
                print(f"Error appending {pdf_path}: {e}")
        else:
            print(f"File not found: {pdf_path}")

    # Save the modified existing PDF with a temporary name
    temp_pdf_path = existing_pdf_path + ".temp"
    existing_pdf.save(temp_pdf_path)
    existing_pdf.close()

    # Replace the original file with the temporary file
    os.replace(temp_pdf_path, existing_pdf_path)

    print(f"All specified PDFs have been appended to {existing_pdf_path}")

# # Excel workbook path
# excel_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\pages_missing.xlsx"  # Update this to the path of your Excel file

# # PDF file to extract pages from
# pdf_path = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Received\COMBINED - SHOP SCANNED PIECEMARKS.pdf"


# # Directory path
# folder_path = r"S:\Shared With Me\08 Contractors\80457 - XTO - Cowboy - Mech\DRAWINGS\ISOs"#'D:\\Architekt Integrated Systems\\Excel 81179_81216 Data Dump\\Individual ISOs\\Linde Validated'
# # Output file
# output_filename = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Received"
# filename = "U1Z6 UTILITIES.pdf"
# discard_folder = r"D:\Architekt Integrated Systems\CLIENTS\EXCEL USA\JOB 002\Validation Drawings\Discard_Pages"
# text_to_search = None

combine_pdfs=False
combine_pdf_recursive=True # Combine PDFs in a directory and all subdirectories
append_pdfs=False
get_folder_files=False
split_files=False
split_single=False
split_groups = False
count_pages_in_dir = False

if count_pages_in_dir:
    folder_path = r"S:\Favorites\Client Uploads\Excel USA\TR-002"
    total_pages = count_total_pdf_pages(folder_path)
    print(f"Total pages in all PDFs in the directory: {total_pages}")

if get_folder_files: # Creates an xlsx file of all files in directory

    # Directory path
    folder_path = r"C:\Users\<USER>\Documents\EXC_0015\AUX BOILER"
    output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Modified"
    new_filename= "file_list - U1Z7.xlsx"

    output_excel_path = os.path.join(output_dir, new_filename)

    # Call the function to extract filenames and create Excel file
    extract_filenames_to_excel(folder_path, output_excel_path)

if combine_pdfs: # Combine all files in a directory
    # Directory path
    folder_path = r"C:\Users\<USER>\Documents\EXC_0015\STG"
    output_dir = r"C:\Users\<USER>\Documents\EXC_0015"
    pdf_new_filename = "STG - Combined.pdf"
    mapping_file = "STG - Combined - Mapping.xlsx"

    # Combine output_dir and output_filename
    pdf_output_filename = os.path.join(output_dir, pdf_new_filename)
    mapping_output_filename = os.path.join(output_dir, mapping_file)

    # Define include and exclude phrases (optional) in filename
    include_phrases = None #["WM", "-WM", "- WM"]
    exclude_phrases = None # ["DRAFT", "OLD"]
    exclude_directories = ["OLD", "4325-FY-VD-PD3014393PU1002-IS1_BBA_ISOs_with_Markups"]

    #extract_and_combine_pages(excel_path, pdf_path, output_filename)

    # Call the function with optional arguments
    combine_pdf_in_folder(folder_path, pdf_output_filename, include_phrases=include_phrases, exclude_phrases=exclude_phrases, mapping_file=mapping_output_filename, exclude_directories=exclude_directories, deduplicate=True)


    # combine_pdf_in_directory(folder_path, output_filename)

if combine_pdf_recursive:
    source_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0014 - MPLX Seretariat 1\Received"
    output_pdf = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0014 - MPLX Seretariat 1\Modified\MPLX ISO Combined.pdf"
    output_excel = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BRS_0014 - MPLX Seretariat 1\Modified\MPLX ISO Combined - File Map.xlsx"

    print("Entering Recursive Combine PDF. \nChecking for total pages in directory...\n")
    total_pages_in_directory = count_total_pdf_pages(source_dir)
    print(f"Total pages in all PDFs in the directory: {total_pages_in_directory}")

    combine_pdf_in_directory_recursive(source_dir, output_pdf, output_excel)

    with fitz.open(output_pdf) as combined_pdf:
        final_page_count = len(combined_pdf)
        print(f"Final combined PDF page count: {final_page_count}")

    if total_pages_in_directory != final_page_count:
        print(f"WARNING: Mismatch in page counts. Expected {total_pages_in_directory}, got {final_page_count}")

if append_pdfs: # Appends list of paths to existing file
    existing_pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Received\U1Z6 UTILITIES-WM.pdf"
    pdf_paths_to_append = [
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Received\U1Z7 UTILITIES\PU1Z7-028-000-CE28103.01.pdf",
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 008\Received\U1Z7 UTILITIES\PU1Z7-028-000-PN28126.01.pdf"


    ]

    # Call the function to append PDFs
    append_pdfs_to_existing(existing_pdf_path, pdf_paths_to_append)

if split_files: # Used to manually split PDF pages. Needed to use for non text/vector type pdfs.
    input_pdf = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\TR-001 Combined.pdf"
    pages_to_remove = [4,5,6,7,8,9,10,11,12,13,14,15,16,17,24,25,26,37,46,48]



    output_original = r"C:\Users\<USER>\OneDrive\Documents\Excel Dev\COMBINED EQP COPY.pdf"
    output_removed = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\output_tests\Test_original_1.pdf"
    mapping_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\output_tests\page_mapping - 2_0.xlsx"

    original_count, removed_count = split_pdf_pages(input_pdf, pages_to_remove, output_original, output_removed, mapping_file)
    print(f"Original PDF has {original_count} pages")

if split_single:
    '''
    Used to split pdf pages that have 'Model' views for pages.
    Recombine later to get a more accurate page count and for clarity when extracting
    '''

    combine_split_files = True

    input_filepath = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BS_001\Received\Shady Hills HRSG Pipe ISOs 10.pdf"
    output_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BS_001\Modified"

    split_pdf_individual(input_filepath, output_folder)

    if combine_split_files:

        # Directory path
        folder_path = output_folder
        output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Brock\BS_001\Modified"
        pdf_new_filename = "M-Shady Hills HRSG Pipe ISOs 10.pdf"
        mapping_file = "M-Shady Hills HRSG Pipe ISOs 10.xlsx"

        # Combine output_dir and output_filename
        pdf_output_filename = os.path.join(output_dir, pdf_new_filename)
        mapping_output_filename = os.path.join(output_dir, mapping_file)

        # Define include and exclude phrases (optional) in filename
        include_phrases = None #["WM", "-WM", "- WM"]
        exclude_phrases = None # ["DRAFT", "OLD"]


        # Call the function with optional arguments
        combine_pdf_in_folder(folder_path, pdf_output_filename, include_phrases=include_phrases, exclude_phrases=exclude_phrases, mapping_file=mapping_output_filename, exclude_directories=None, deduplicate=True)

if split_groups:

    input_pdf = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified\Combined OCI-Tecnimont-Rotated.pdf"
    mapping_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified\Grouped Page Results.xlsx"
    output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified"



    # First create clean copy
    # clean_pdf_path = create_clean_pdf(input_pdf)

    try:
        detailed_pdf_diagnosis(input_pdf)
    except Exception as e:
        print(f"Error during diagnosis: {str(e)}")


    # Test the function
    optimize_pdf_splitting(
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified\Combined OCI-Tecnimont-Rotated.pdf",
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified\Grouped Page Results.xlsx",
        r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 015\Modified"
    )

    # Then process the clean copy
    # results = process_pdf_with_diagnostics(clean_pdf_path, mapping_file, output_dir)

    # This will run diagnostics and then process the PDF with optimizations
    # results = process_pdf_with_diagnostics(input_pdf, mapping_file, output_dir)
    # print(f"Created {len(results)} PDF groups with the following page counts:")
    # for group_num, page_count in results.items():
    #     print(f"Group {group_num}: {page_count} pages")

