"""
Patch Raw DataFrame with OCR Data

1. Two-Phase Processing:
    Phase 1: Collect all CV2 data first
    Phase 2: Compare entire datasets at once. I like to use pandas vectorized operations.

2. OCR the identified pages

3. Merge results as before

"""

import json
import fitz
import io
import os
import time
import tempfile

import numpy as np
import pandas as pd
from PIL import Image
from dataclasses import dataclass
from typing import List, Tuple
import multiprocessing as mp
from PyPDF2 import PdfReader, PdfWriter

from src.atom.vision.detect_text_regions import detect_text_regions_cv2, flag_text_regions
from src.atom.vision import convert_to_pdf_aws
from src.atom.fast_storage import load_df_fast, save_df_fast
from src.utils.logger import logger


_CHUNK_SIZE = 100
MISSING_CHUNK_SIZE = 50

SAVE_CHUNKS = _CHUNK_SIZE

def load_json(file) -> dict:
    with open(file) as json_file:
        return json.load(json_file)

@dataclass
class PageConfig:
    page_number: int
    group_number: int
    roi_layout: dict
    pdf_path: str
    dpi: int = 300

def dummy_task(a):
    """Return the page number"""
    return a.page_number

# Multiprocessing - Pickle friendly functions
def detect_page_regions(config: PageConfig) -> pd.DataFrame:
    """Detect text regions for a single page"""
    doc = fitz.open(config.pdf_path)
    page = doc[config.page_number-1]
    cv_image = page_to_opencv(page, config.dpi)
    text_regions = detect_text_regions_cv2.detect_text_regions(cv_image)
    doc.close()

    # Convert to DataFrame and add necessary columns
    df = pd.DataFrame(text_regions, columns=["x0", "y0", "width", "height"])
    df["x1"] = df["x0"] + df["width"]
    df["y1"] = df["y0"] + df["height"]
    df['pdf_page'] = config.page_number
    df['group_number'] = config.group_number

    return df[["pdf_page", "group_number", "x0", "y0", "x1", "y1", "width", "height"]]

def page_to_opencv(page: fitz.Page, dpi: int = None) -> np.ndarray:
    """Convert PyMuPDF page to OpenCV image"""
    zoom = dpi / 72 if dpi else 1
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)

    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    return open_cv_image[:, :, ::-1].copy()

def extract_raw_regions(df: pd.DataFrame):
    """Parses coordinates and extracts bounding box values"""
    raw_regions = []

    for record in df.to_dict("records"):
        try:
            # Workaround - parse string to tuple
            record["coordinates2"] = record["coordinates2"].replace("(", "")
            record["coordinates2"] = record["coordinates2"].replace(")", "")
            coordinates = tuple(map(float, record["coordinates2"].split(', ')))
            record["x0"] = coordinates[0]
            record["y0"] = coordinates[1]
            record["x1"] = coordinates[2]
            record["y1"] = coordinates[3]
            record["width"] = record["x1"] - record["x0"]
            record["height"] = record["y1"] - record["y0"]
            raw_regions.append({
                "pdf_page": record["pdf_page"],
                "x0": record["x0"],
                "y0": record["y0"],
                "x1": record["x1"],
                "y1": record["y1"],
            })
        except Exception as e:
            continue
            # raw_regions.append(record["coordinates2"])

    return raw_regions

def process_textract_response(data) -> pd.DataFrame:
    res = []
    for b in data["Blocks"]:
        if not b.get("Text"):
            continue
        bbox = b["Geometry"]["BoundingBox"]
        confidence = b["Confidence"]
        text = b["Text"]
        page = b.get("Page")
        res.append({
            "pdf_page": page,
            "text": text,
            "x0": bbox["Left"],
            "y0": bbox["Top"],
            "x1": bbox["Left"] + bbox["Width"],
            "y1": bbox["Top"] + bbox["Height"],
            "confidence": confidence
        })
    return pd.DataFrame(res)

def find_missing_regions_vectorized(page: int, page_cv2, page_regions, page_rois) -> pd.DataFrame:
    """Find missing regions using vectorized operations"""

    # Extracts coordinates 2 from all raw df records
    print("Finding final missing regions for page", page)

    # Create a helper function for vectorized overlap checking
    def check_overlap(row, page_regions: pd.DataFrame):
        # Convert row coordinates to array for broadcasting
        r1 = np.array([row['x0'], row['y0'], row['x1'], row['y1']])

        if len(page_regions) == 0:
            return False

        # Convert all region coordinates to arrays
        r2 = page_regions[['x0', 'y0', 'x1', 'y1']].values # raw regions

        # Vectorized overlap check
        overlaps = (r1[0] < r2[:, 2]) & (r1[2] > r2[:, 0]) & \
                    (r1[1] < r2[:, 3]) & (r1[3] > r2[:, 1])

        # Need missing to NOT overlap with existing raw region but DOES overlap with
        # an ROI layout selection
        return overlaps.any()

    def check_overlap2(row, page_rois: pd.DataFrame):
        # Convert row coordinates to array for broadcasting
        r1 = np.array([row['x0'], row['y0'], row['x1'], row['y1']])

        # Convert all region coordinates to arrays
        rois = page_rois[['x0', 'y0', 'x1', 'y1']].values # roi regions

        overlaps = (r1[0] < rois[:, 2]) & (r1[2] > rois[:, 0]) & \
                    (r1[1] < rois[:, 3]) & (r1[3] > rois[:, 1])

        # Need missing to NOT overlap with existing raw region but DOES overlap with
        # an ROI layout selection
        return overlaps.any()

    # Vectorized overlap check for this page
    mask = page_cv2.apply(lambda row: ~check_overlap(row, page_regions), axis=1)

    # Store missing regions
    missing = page_cv2[mask].copy()

    # Filter out missing regions which are not in the ROI layout
    mask2 = missing.apply(lambda row: check_overlap2(row, page_rois), axis=1)
    missing2 = missing[mask2].copy()

    return missing2

def is_overlapping(rect_a, rect_b) -> bool:
    """
    Return True if two regions overlap, where lx and rx are top left
    and bottom right coordinates of a rect respectively
    """
    a_left, a_top = rect_a[0], rect_a[1]
    a_right, a_bottom = rect_a[2], rect_a[3]
    b_left, b_top = rect_b[0], rect_b[1]
    b_right, b_bottom = rect_b[2], rect_b[3]
    return (a_left < b_right
        and a_right > b_left
        and a_top < b_bottom
        and a_bottom > b_top)


def extract_missing_text(roi_coords, regions) -> pd.DataFrame:
    """Return text which overlap with regions"""
    all_flagged = []
    page_groups = regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_roi_regions = roi_coords[roi_coords["pdf_page"] == pdf_page].to_dict("records")
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])


        # If text region does not overlap with any region of
        # the Raw DataFrame, then flag it for OCR
        flagged = []
        for n, r1 in enumerate(page_regions):
            for n2, r2 in enumerate(page_roi_regions):
                if r2 in flagged:
                    continue
                r2_coords = [r2["x0"], r2["y0"], r2["x1"], r2["y1"]]
                if is_overlapping(r1, r2_coords):
                    # Overlapping with ROI - add flag for OCR
                    record = [pdf_page] + r1 + [r2["text"]]
                    # flagged.append(record)
                    flagged.append(r2)
                    print(f"Page {pdf_page}, Text region index={n} flagged for OCR")
                    break

        all_flagged.extend(flagged)

        print(f"Page {pdf_page} - Number of missing regions which overlap with an ROI:", len(flagged))

    return pd.DataFrame(all_flagged, columns=["pdf_page", "x0", "y0", "x1", "y1", "text"])

class PDFProcessor:

    """

    Args:
        overwrite_chunk - If False, skip job if chunk file already exists
    """

    def __init__(self, pdf_path: str, raw_data_path: str, page_group_path: str, roi_configs: dict, dpi = 300, overwrite_chunk=True):
        self.pdf_path = pdf_path
        self.raw_data_path = raw_data_path
        self.page_group_path = page_group_path
        self.roi_configs = roi_configs
        self.doc = None
        self.raw_data = None
        self.page_groups = None
        self.dpi = dpi
        self.overwrite_chunk = overwrite_chunk
        self.raw_regions = pd.DataFrame()

    def initialize(self) -> int:
        """Initialize document and data sources"""
        print("Initializing document and data sources...")
        self.doc = fitz.open(self.pdf_path)
        self.raw_data = pd.read_feather(f"{self.raw_data_path}.feather")
        self.page_groups = pd.read_excel(self.page_group_path)
        print(f"Loaded document with {len(self.doc)} pages")
        print(f"Raw data contains {len(self.raw_data)} records")
        return len(self.doc)

    def process_pages(self, max_pages: int = None) -> Tuple[List[int], pd.DataFrame]:
        """Two-phase processing: CV2 detection first, then comparison"""
        doc_length = self.initialize()

        # Phase 1: Detect all text regions using CV2 in parallel
        print("\nPhase 1: Detecting text regions with CV2...")
        page_configs = self._prepare_page_configs(doc_length, max_pages)
        cv2_results = self._process_cv2_detection(page_configs)
        print(f"Found {len(cv2_results)} text regions across {cv2_results['pdf_page'].nunique()} pages")

        # CSV because too many
        print(len(cv2_results))
        save_df_fast(cv2_results, "debug/ocr_patch/cv2_results_full", format="feather")

        # Phase 2: Compare with raw data all at once
        print("\nPhase 2: Comparing datasets...")
        requires_ocr, missing_regions = self._compare_datasets(cv2_results)

        return requires_ocr, missing_regions

    def _prepare_page_configs(self, doc_length: int, max_pages: int = None) -> List[PageConfig]:
        """Prepare configurations for all pages to be processed"""
        configs = []
        for record in self.page_groups.itertuples():
            if record.page_number > doc_length:
                continue
            if max_pages and record.page_number > max_pages:
                break

            configs.append(PageConfig(
                page_number=record.page_number,
                group_number=record.group_number,
                roi_layout=self.roi_configs[record.group_number],
                pdf_path=self.pdf_path
            ))
        return configs

    def _process_cv2_detection(self, page_configs: List[PageConfig]) -> pd.DataFrame:
        """Process all pages with CV2 and return combined results"""

        # page_configs = page_configs[:6]  # First 6 Pages
        # page_configs = [page_configs[1254]] # Page 1255 only, must be in list/iterable

        os.makedirs("debug/ocr_patch/chunks", exist_ok=True)
        start_time = time.time()
        if mp.current_process().name == 'MainProcess':
            jobs = range(0, len(page_configs), _CHUNK_SIZE)
            print("total chunks", len(jobs))
            results = []
            for job_no, n in enumerate(range(0, len(page_configs), _CHUNK_SIZE), start=1):
                chunk_configs = [[page_configs[m]] for m in range(n, min(len(page_configs), n+_CHUNK_SIZE))]

                start_page = chunk_configs[0][0].page_number
                end_page = chunk_configs[-1][0].page_number
                print(f"Processing chunk of size {len(chunk_configs)} - pages {start_page} to {end_page}")
                with mp.Pool(processes=mp.cpu_count(), maxtasksperchild=100) as pool:
                    chunk_file = f"debug/ocr_patch/chunks/chunk_result_job_{job_no}_start_{start_page}_end_{end_page}.feather"
                    if self.overwrite_chunk is False and os.path.exists(chunk_file):
                        print(f"Loading existing chunk: {chunk_file}")
                        # chunk_results = pd.read_excel(chunk_file)
                        chunk_results = load_df_fast(chunk_file)
                        results.append(chunk_results)
                    else:
                        chunk_results = pool.starmap(detect_page_regions, chunk_configs)
                        chunk_df = pd.concat([r for r in chunk_results if not r.empty], ignore_index=True)
                        save_df_fast(chunk_df, chunk_file, format="feather")
                        results.extend(chunk_results)
        else:
            results = map(detect_page_regions, page_configs)

        end_time = time.time()
        print(f"Phase 1 - CV2 Processing Time ({end_time - start_time})s")

        results = [r for r in results if not r.empty]  # Exclude error pages
        # Combine all CV2 results into a single DataFrame
        all_regions = pd.concat(results, ignore_index=True)
        return all_regions

    def _compare_datasets(self, cv2_results: pd.DataFrame) -> Tuple[List[int], pd.DataFrame]:
        """Compare CV2 results with raw data efficiently using vectorized operations"""
        # Convert coordinates for all results at once
        print("Converting coordinates...")
        mapped_regions = self._convert_coordinates_vectorized(cv2_results)

        # Process ROI regions for all pages at once
        print("Processing ROI regions...")
        roi_regions = self._process_all_roi_regions()

        # Find missing regions using vectorized operations
        print("Finding missing regions...")
        missing_regions = self._find_missing_regions_vectorized(
            raw_df=self.raw_data,
            cv2_regions=mapped_regions,
            roi_regions=roi_regions
        )

        # Get unique pages requiring OCR
        requires_ocr = missing_regions['pdf_page'].unique().tolist()

        return requires_ocr, missing_regions

    def _convert_coordinates_vectorized(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert coordinates using vectorized operations"""
        # Assuming DPI conversion is needed
        scale_factor = 72 / self.dpi

        coord_columns = ['x0', 'y0', 'x1', 'y1', 'width', 'height']
        df_converted = df.copy()
        df_converted[coord_columns] = df_converted[coord_columns] * scale_factor

        return df_converted

    def _process_all_roi_regions(self) -> pd.DataFrame:
        """Process ROI regions for all pages at once using vectorized operations"""
        roi_data = []

        # Filter page_groups to only include pages that exist in the document (Avoids error when test pdf is a subset of the full page group summary file)
        valid_pages = self.page_groups[self.page_groups.page_number <= len(self.doc)]

        # for record in self.page_groups.itertuples():
        for record in valid_pages.itertuples():
            page = self.doc[record.page_number - 1]

            roi_layout = self.roi_configs[record.group_number]

            # Extract all regions at once for this page
            regions_data = [
                {
                    'pdf_page': record.page_number,
                    'group_number': record.group_number,
                    'columnName': r['columnName'],
                    'x0': r.get('tableCoordinates', r)['relativeX0'] * page.rect.width,
                    'y0': r.get('tableCoordinates', r)['relativeY0'] * page.rect.height,
                    'x1': r.get('tableCoordinates', r)['relativeX1'] * page.rect.width,
                    'y1': r.get('tableCoordinates', r)['relativeY1'] * page.rect.height,
                }
                for r in roi_layout
            ]
            roi_data.extend(regions_data)

        roi_df = pd.DataFrame(roi_data)
        roi_df['width'] = roi_df['x1'] - roi_df['x0']
        roi_df['height'] = roi_df['y1'] - roi_df['y0']

        return roi_df

    def _find_missing_regions_vectorized(
        self,
        raw_df: pd.DataFrame,
        cv2_regions: pd.DataFrame,
        roi_regions: pd.DataFrame
    ) -> pd.DataFrame:
        """Find missing regions using vectorized operations"""

        # Extracts coordinates 2 from all raw df records
        print("Extracting raw region bbox from coordinates2")
        self.raw_regions = pd.DataFrame(extract_raw_regions(raw_df))

        # Process each page group in chunks for memory efficiency
        missing_regions = []
        starmap_args = []
        for page in cv2_regions['pdf_page'].unique():
            # print("Filtering out regions for page:", page)
            # Get regions for this page
            page_cv2 = cv2_regions[cv2_regions['pdf_page'] == page]
            page_regions = self.raw_regions[self.raw_regions['pdf_page'] == page]
            page_rois = roi_regions[roi_regions['pdf_page'] == page]
            starmap_args.append([page, page_cv2, page_regions, page_rois])

        if missing_regions:
            return pd.concat(missing_regions, ignore_index=True)

        print("Processing missing regions")
        os.makedirs("debug/ocr_patch/chunks_missing_regions", exist_ok=True)
        start_time = time.time()
        pages = [page for page in cv2_regions['pdf_page'].unique()]

        jobs = range(0, len(pages), MISSING_CHUNK_SIZE)
        print("total chunks", len(jobs))
        results = []
        for job_no, n in enumerate(range(0, len(pages), MISSING_CHUNK_SIZE), start=1):
            chunk_args = [starmap_args[m] for m in range(n, min(len(pages), n+MISSING_CHUNK_SIZE))]
            # start_page = chunk_configs[0][0].page_number
            # end_page = chunk_configs[-1][0].page_number
            print(f"Processing chunk of size {len(chunk_args)} - job_no {job_no}")
            with mp.Pool(processes=mp.cpu_count(), maxtasksperchild=100) as pool:
                chunk_file = f"debug/ocr_patch/chunks_missing_regions/chunk_result_job_{job_no}.feather"
                if self.overwrite_chunk is False and os.path.exists(chunk_file):
                    print(f"Loading existing chunk: {chunk_file}")
                    # chunk_results = pd.read_excel(chunk_file)
                    chunk_results = load_df_fast(chunk_file)
                    results.append(chunk_results)
                else:
                    chunk_results = pool.starmap(find_missing_regions_vectorized, chunk_args)
                    valid_results = [r for r in chunk_results if not r.empty]
                    if valid_results:
                        chunk_df = pd.concat(valid_results, ignore_index=True)
                        save_df_fast(chunk_df, chunk_file.rstrip(".feather"), format="feather")
                        results.extend(chunk_results)
                    else:
                        print("!!!!No results were found for chunk job no", job_no)  # job 14, where chunk 50

        end_time = time.time()
        print(f"Phase 2 - Processing Missing regions Time ({end_time - start_time})s")

        results = [r for r in results if not r.empty]  # Exclude error pages
        # Combine all CV2 results into a single DataFrame
        all_missing = pd.concat(results, ignore_index=True)
        return all_missing



def ocr_specific_pages(source_pdf,
                       pages_to_process,
                       dpi=300,
                       chunk_size=100,
                       use_checkpoint: bool = False) -> pd.DataFrame:
    """Process specific pages while maintaining original page numbers with checkpointing"""

    use_checkpoint = use_checkpoint and not __file__.endswith(".pyc")

    # temp_dir = r"debug/temp_pdf_pages"
    for page in pages_to_process:
        if page <= 0:
            raise ValueError("Page number must be greater than 0")

    # Create directories for temp and checkpoint files
    if use_checkpoint:
        checkpoint_dir = r"debug/ocr_checkpoints"
        # os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(checkpoint_dir, exist_ok=True)

    # Initialize doc only once
    doc = PdfReader(source_pdf)
    combined_textract = []

    # Process in chunks for efficiency
    page_chunks = [pages_to_process[i:i + chunk_size] for i in range(0, len(pages_to_process), chunk_size)]

    completed_chunks = []
    if use_checkpoint:
        # Load previous progress if exists
        checkpoint_file = f"{checkpoint_dir}/ocr_progress.json"
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, 'r') as f:
                completed_chunks = json.load(f)
            print(f"Found previous progress: {len(completed_chunks)} chunks completed")

    base_temp_dir = os.path.join(tempfile.gettempdir(), "atem")
    os.makedirs(base_temp_dir, exist_ok=True)

    temp_dir = tempfile.TemporaryDirectory(dir=base_temp_dir, prefix="atem-")


    for chunk_idx, page_chunk in enumerate(page_chunks):
        # Skip if chunk was already processed
        chunk_id = f"chunk_{page_chunk[0]}-{page_chunk[-1]}"
        if chunk_id in completed_chunks:
            print(f"Skipping completed chunk {chunk_idx + 1}/{len(page_chunks)} (pages {page_chunk[0]}-{page_chunk[-1]})")
            # Load previously processed results
            chunk_results = pd.read_feather(f"{checkpoint_dir}/{chunk_id}.feather")
            combined_textract.extend(chunk_results.to_dict("records"))
            continue

        logger.debug(f"Processing chunk {chunk_idx + 1}/{len(page_chunks)} (pages {page_chunk[0]}-{page_chunk[-1]})")

        # Create new PDF with just these pages
        new_doc = PdfWriter()
        for page_num in page_chunk:
            page = doc.pages[page_num-1]
            new_doc.add_page(page)

        temp_pdf = f"{temp_dir.name}/chunk_{chunk_idx}-{page_chunk[0]}-{page_chunk[-1]}.pdf"
        with open(temp_pdf, 'wb') as out:
            new_doc.write(out)
        new_doc.close()

        try:
            # Process chunk with optimized Textract
            textract_response =  convert_to_pdf_aws.convert_multiple_pages(temp_pdf, existing_file=False)
            chunk_results = []
            # Process response and map back to original page numbers
            for page_idx, page_num in enumerate(page_chunk):

                # Filter blocks for this page and process
                page_blocks = [block for block in textract_response['Blocks']
                            if 'Page' in block and block['Page'] == page_idx + 1]

                # Convert to DataFrame with proper coordinates
                page_textract = process_textract_response({'Blocks': page_blocks})

                # Convert back to absolute coordinates
                # page_textract["x0"] *= page.rect.width
                # page_textract["y0"] *= page.rect.height
                # page_textract["x1"] *= page.rect.width
                # page_textract["y1"] *= page.rect.height
                page_textract["pdf_page"] = page_num  # Use original page number

                # Save individual page results
                # save_df_fast(page_textract, f"debug/ocr_patch/textract_results/page_{page_num}_textract", format="feather")
                # page_textract.to_excel(f"debug/ocr_patch/textract_results/page_{page_num}_textract.xlsx")

                # Add to chunk results
                chunk_results.extend(page_textract.to_dict("records"))

            # Save chunk checkpoint
            if use_checkpoint:
                chunk_df = pd.DataFrame(chunk_results)
                save_df_fast(chunk_df, f"{checkpoint_dir}/{chunk_id}", format="feather")

                # Update progress file
                completed_chunks.append(chunk_id)
                with open(checkpoint_file, 'w') as f:
                    json.dump(completed_chunks, f)

            # Add to combined results
            combined_textract.extend(chunk_results)

        except Exception as e:
            print(f"Error processing chunk {chunk_idx} (pages {page_chunk[0]}-{page_chunk[-1]}): {str(e)}")
            # Save error log
            if use_checkpoint:
                with open(f"{checkpoint_dir}/errors.log", 'a') as f:
                    f.write(f"\nError on chunk {chunk_id}: {str(e)}")

    # Remove the temp dir and contents
    temp_dir.cleanup()

    # Convert combined results to DataFrame
    combined_textract = pd.DataFrame(combined_textract)

    # Save final combined results
    if use_checkpoint and not combined_textract.empty:
        save_df_fast(combined_textract, "debug/ocr_patch/combined_textract_final", format="feather")

    return combined_textract


def main(TEST_MODE=False, EXPORT_FINAL_PATCH_DATA=False, LOAD_MISSING = False,
         SOURCE_PDF="", ROI_CONFIGS={}):

    # -- Explanation of Logic --

    """
    Process PDF documents to extract text data, perform OCR on regions where text extraction failed,
    and combine the results into a unified dataset.

    This function handles the complete workflow of:
    1. Loading configuration and source files (PDF, ROI configs, page groups)
    2. Processing PDF pages to identify regions requiring OCR
    3. Performing OCR on specific pages with missing text
    4. Combining OCR results with existing text data
    5. Saving the final patched dataset

    Arguments:
        TEST_MODE (bool): When True, bypasses OCR processing and exits after identifying
                         regions requiring OCR. Useful for testing the identification process
                         without running expensive OCR operations.

        EXPORT_FINAL_PATCH_DATA (bool): When True, exports the final combined dataset
                                       to Excel format in addition to Feather format.
                                       When False, only saves in Feather format.

        LOAD_MISSING (bool): Controls whether to process the PDF from scratch or use cached results.
                            When True, loads a previously generated 'missing regions' file that
                            contains coordinates of areas where text extraction failed.
                            When False, processes the PDF to identify these regions.

                            The missing regions file contains:
                            - PDF page numbers
                            - Coordinate boxes (x0, y0, x1, y1) for each region
                            - Region metadata

                            These regions are then sent to OCR processing to extract
                            text that couldn't be extracted through normal PDF parsing.
                            This two-step process (identify then OCR) allows for faster
                            iteration during development and recovery from OCR failures.

    File Dependencies:
        - Source PDF file
        - ROI configuration JSON files (regions of interest)
        - Page group summary Excel file
        - Previously processed missing regions data (when LOAD_MISSING=True)
        - The missing regions file is saved as both Feather and Excel:
          - debug/ocr_patch/combined_missing_regions.feather
          - debug/ocr_patch/combined_missing_regions.xlsx

    Output Files:
        - Combined missing regions (Feather and Excel)
        - OCR results (Feather)
        - Final patched dataset (Feather and optionally Excel)
        - Backup files in case of errors

    Error Handling:
        - Implements comprehensive error handling at each major processing step
        - Creates backup files if errors occur during final save operations
        - Continues processing when possible, with appropriate warning messages

    System Fields:
        Attempts to preserve system fields from original data:
        - sys_build
        - sys_path
        - sys_filename
        - pdf_id
        Falls back to 'unknown' if fields are not available


    """

    # LOAD_MISSING: Bypass processor and load previous combined missing results
    # Configuration
    # SOURCE_PDF = r"E:\IoT\M\Architekt-ATOM-res\Combined Remuriate.pdf" #Combined Remuriate.pdf"
    # Original Raw data from inspect_raw
    RAW_DATA_PATH = r"debug\raw_data" #r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Data\dev\test_raw_data.xlsx"
    PAGE_GROUP_PATH = r"debug\Grouped Page Results.xlsx"

    # ROI_CONFIGS  = {
    #     1: load_json(r"E:\IoT\M\Architekt-ATOM-res\1.json"),
    #     2: load_json(r"E:\IoT\M\Architekt-ATOM-res\2.json"),
    #     3: load_json(r"E:\IoT\M\Architekt-ATOM-res\3.json"),
    #     4: load_json(r"E:\IoT\M\Architekt-ATOM-res\4.json"),
    # }

     # Storage Paths/Names
    combined_missing_feather = "debug\ocr_patch\combined_missing_regions"
    patch_file = "debug\raw_data_full_patched_ocr.xlsx"
    output_path = r"debug\raw_data_full_patched_ocr"


    print(f"\n----------------\nTEST_MODE={TEST_MODE}\nEXPORT_FINAL_PATH_DATA:{EXPORT_FINAL_PATCH_DATA}\nLOAD_MISSING{LOAD_MISSING}\n----------------\n")
    print(f"\n----------------\nSOURCE_PDF:{SOURCE_PDF}\nRAW_DATA_PATH:{RAW_DATA_PATH}\nPAGE_GROUP_PATH{PAGE_GROUP_PATH}\n----------------\n")
    print(f"\n----------------\ncombined_missing_feather:{combined_missing_feather}\npatch_file:{patch_file}\output_path(Final){output_path}\n----------------\n")
    print(f"\n----------------\nROI_CONFIGS:{ROI_CONFIGS}\n----------------\n")


    # Simon's Config
    # SOURCE_PDF = r"c:\Users\<USER>\Desktop\remuriate\Combined Remuriate.pdf"

    # # Original Raw data from inspect_raw
    # RAW_DATA_PATH = r"C:\Users\<USER>\Desktop\remuriate\raw_data" #r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Data\dev\test_raw_data.xlsx

    # PAGE_GROUP_PATH = r"C:\Users\<USER>\Desktop\remuriate\Page Group Summary.xlsx"
    # ROI_CONFIGS  = {
    #     1: load_json(r"C:\Users\<USER>\Desktop\remuriate\roi\1.json"),
    #     2: load_json(r"C:\Users\<USER>\Desktop\remuriate\roi\2.json"),
    #     3: load_json(r"C:\Users\<USER>\Desktop\remuriate\roi\3.json"),
    #     4: load_json(r"C:\Users\<USER>\Desktop\remuriate\roi\4.json"),
    # }
    # End - Simon's Config


    # Initialize processor
    processor = PDFProcessor(
        pdf_path=SOURCE_PDF,
        raw_data_path=RAW_DATA_PATH, # r"debug\raw_data"
        page_group_path=PAGE_GROUP_PATH,
        roi_configs=ROI_CONFIGS,
        overwrite_chunk=False,
    )

    processor.initialize()  # Add this line

    # REPLACE WITH THIS:
    if LOAD_MISSING:
        print("Loading previously identified missing regions...")
        combined_missing_rois = load_df_fast(combined_missing_feather + ".feather")
        requires_ocr = combined_missing_rois['pdf_page'].unique().tolist()
    else:
        print("Processing PDF to identify missing regions...")
        requires_ocr, combined_missing_rois = processor.process_pages()
        if not combined_missing_rois.empty:
            save_df_fast(combined_missing_rois, "debug/ocr_patch/combined_missing_regions", format="feather")
            pd.DataFrame(combined_missing_rois).to_excel("debug/ocr_patch/combined_missing_regions.xlsx")



    if TEST_MODE:
        print("\n\n---------------------------\nTEST_MODE ACTIVE! BYPASING OCR\n---------------------------\n")

        return
        # # Load previously processed textract results
        # combined_textract_file = "debug/ocr_patch/combined_textract"
        # combined_textract = load_df_fast(combined_textract_file + ".feather")


    os.makedirs("debug/ocr_patch/textract_results", exist_ok=True)

    try:
        # Replace the single page processing loop with our new function
        combined_textract = ocr_specific_pages(SOURCE_PDF, requires_ocr, processor.dpi)

        if combined_textract.empty:
            logger.error("No OCR results were found in any pages")
            return

        logger.info(f"OCR completed. Results shape: {combined_textract.shape}")

        # Save combined results (same as before)
        combined_textract_file = "debug/ocr_patch/combined_textract"
        save_df_fast(combined_textract, combined_textract_file, format="feather")
        logger.info(f"Saved combined OCR results to {combined_textract_file}")

        # Extract missing ROIs with error handling
        try:
            logger.info("Extracting missing text...")
            final_missing_rois = extract_missing_text(combined_textract, combined_missing_rois)

            if final_missing_rois.empty:
                logger.error("No missing ROIs were found after OCR")
                return

            final_missing_rois.to_excel("debug/final_missing_rois.xlsx")
            logger.info(f"Saved final missing ROIs. Shape: {final_missing_rois.shape}")

        except Exception as e:
            logger.error(f"Error extracting missing text: {e}", exc_info=True)
            return

        raw_data = processor.raw_data

        # Safely get system fields with fallback values
        try:
            # Try to get first page with system fields
            system_pages = raw_data[raw_data['sys_build'].notna()]
            if system_pages.empty:
                print("Warning: No system fields found in raw data, using defaults")
                sys_fields = {
                    'sys_build': 'unknown',
                    'sys_path': 'unknown',
                    'sys_filename': 'unknown',
                    'pdf_id': 'unknown'
                }
            else:
                first_system_row = system_pages.iloc[0]
                sys_fields = {
                    'sys_build': str(first_system_row.get('sys_build', 'unknown')),
                    'sys_path': str(first_system_row.get('sys_path', 'unknown')),
                    'sys_filename': str(first_system_row.get('sys_filename', 'unknown')),
                    'pdf_id': str(first_system_row.get('pdf_id', 'unknown'))
                }
        except Exception as e:
            print(f"Error getting system fields: {str(e)}, using defaults")

            sys_fields = {
                'sys_build': 'unknown',
                'sys_path': 'unknown',
                'sys_filename': 'unknown',
                'pdf_id': 'unknown'
            }

        print("\n\nRAW DATA: \n\n", raw_data)

        # Process records with error handling for each page
        raw_data_records = raw_data.to_dict("records")
        for page_number, page_group in final_missing_rois.groupby("pdf_page"):
            try:
                for record in page_group.itertuples():
                    try:
                        coordinates = (record.x0, record.y0, record.x1, record.y1)
                        raw_data_records.append({
                            "pdf_page": page_number,
                            "type": "OCR",
                            "value": str(record.text) if hasattr(record, 'text') else '',
                            "coordinates": coordinates,
                            "coordinates2": coordinates,
                            "font": "ArialMT",
                            "font_size": "12",
                            "sys_build": sys_fields['sys_build'],
                            "sys_path": sys_fields['sys_path'],
                            "sys_filename": sys_fields['sys_filename'],
                            "pdf_id": sys_fields['pdf_id']
                        })
                    except Exception as e:
                        print(f"Warning: Error processing record for page {page_number}: {str(e)}")
                        continue
            except Exception as e:
                logger.error(f"Warning: Error processing page {page_number}: {e}", exc_info=True)(f"Warning: Error processing page {page_number}: {str(e)}")
                continue



        try:
            # Create final dataframe

            out_df = pd.DataFrame(raw_data_records)

            # Convert all object columns to string with error handling
            for col in out_df.select_dtypes(['object']):
                try:
                    out_df[col] = out_df[col].astype(str)

                except Exception as e:
                    logger.error(f"Warning: Error converting column {col} to string: {str(e)}")
                    out_df[col] = out_df[col].fillna('').astype(str)

            # Save as Feather

            return out_df

            save_df_fast(out_df, output_path, format='feather')

            # Optional:

            if EXPORT_FINAL_PATCH_DATA:
                out_df.to_excel(patch_file)

                print("Finished patching RAW Data:", patch_file)
            else:
                print("Finished patching RAW Data. EXPORT_FINAL_PATCH_DATA=FALSE")


        except Exception as e:
            print(f"Error saving final results: {str(e)}")
            # Try to save what we have as backup
            try:
                backup_path = r"debug\raw_data_full_patched_ocr_backup"
                save_df_fast(pd.DataFrame(raw_data_records), backup_path, format='feather')
                print(f"Saved backup to {backup_path}")
            except:
                print("Could not save backup")

    except Exception as e:
        logger.error(f"Critical error in OCR processing: {e}", exc_info=True)


if __name__ == "__main__":
    from src.utils.logger import logger

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    main(TEST_MODE=False, EXPORT_FINAL_PATCH_DATA=True, LOAD_MISSING=True)


# else:

    #     os.makedirs("debug/ocr_patch/textract_results")
    #     combined_textract = []
    #     doc = fitz.open(SOURCE_PDF)
    #     # Single convert - Replace with multi
    #     for page_number in requires_ocr:

    #         # Check the pdf contains the page it is attempting
    #         if page_number > len(doc):
    #             print(f"Skipping OCR for page {page_number} as document only has {len(doc)} pages")
    #             continue

    #         print("OCR Page", page_number)
    #         page = doc[page_number-1]

    #         cv_image = page_to_opencv(page, dpi=processor.dpi)
    #         # Perform OCR on this page
    #         image_bytes = io.BytesIO()
    #         Image.fromarray(cv_image).save(image_bytes, format='JPEG')
    #         image_bytes = image_bytes.getvalue()

    #         res = convert_to_pdf_aws.convert_single_page(image_bytes)

    #         page_textract = process_textract_response(res)
    #         # Convert back to absolute coordinates
    #         page_textract["x0"] *= page.rect.width
    #         page_textract["y0"] *= page.rect.height
    #         page_textract["x1"] *= page.rect.width
    #         page_textract["y1"] *= page.rect.height
    #         page_textract["pdf_page"] = page_number

    #         # Add to combined full textract results
    #         combined_textract.extend(page_textract.to_dict("records"))

    #         # save each textract
    #         page_textract.to_excel(f"debug/ocr_patch/page_{page_number}_textract.xlsx")
    #         save_df_fast(page_textract, f"debug/ocr_patch/page_{page_number}_textract", format="feather")


    #     combined_textract_file = "debug/ocr_patch/combined_textract"
    #     combined_textract = pd.DataFrame(combined_textract)
    #     save_df_fast(combined_textract, combined_textract_file, format="feather")

    #     # COPIED FROM OCR_PATCH_RAW.... Needs some work
    #     #

    #     # Finally use combined textract to find to detect missing ROIS and patch file
    #     final_missing_rois = extract_missing_text(combined_textract, combined_missing_rois) # These are missing regions which need to be patched into Raw

    #     final_missing_rois.to_excel("debug/final_missing_rois.xlsx")

    #     ##############################
    #     ##############################
    #     ##############################
    #     raw_data = processor.raw_data

    #     # For page_raw_df, we can just use the first page of raw_data since we only need system fields
    #     page_raw_df = raw_data[raw_data['pdf_page'] == raw_data['pdf_page'].iloc[0]]

    #     ##############################
    #     ##############################
    #     ##############################


    #     # Extract system fields from the first row of page_raw_df
    #     sys_fields = {
    #         'sys_build': page_raw_df['sys_build'].iloc[0],
    #         'sys_path': page_raw_df['sys_path'].iloc[0],
    #         'sys_filename': page_raw_df['sys_filename'].iloc[0],
    #         'pdf_id': page_raw_df['pdf_id'].iloc[0]
    #     }

    #     raw_data_records = raw_data.to_dict("records")
    #     for page_number, page_group in final_missing_rois.groupby("pdf_page"):
    #         for record in page_group.itertuples():
    #             coordinates = (record.x0, record.y0, record.x1, record.y1)
    #             raw_data_records.append({
    #                 "pdf_page": page_number,
    #                 "type": "OCR",
    #                 "value": str(record.text),
    #                 "coordinates": coordinates,
    #                 "coordinates2": coordinates,
    #                 "font": "ArialMT",
    #                 "font_size": "12",
    #                 "sys_build": sys_fields['sys_build'],
    #                 "sys_path": sys_fields['sys_path'],
    #                 "sys_filename": sys_fields['sys_filename'],
    #                 "pdf_id": sys_fields['pdf_id']
    #             })

    #     # page = doc[page_number-1]
    #     # image = visualize_page(page, page_number, res)
    #     # cv2.imshow("test", image)
    #     # cv2.waitKey(0)

        # patch_file = "debug/raw_data_full_patched_ocr.xlsx"

        # # pd.DataFrame(raw_data_records).to_excel(patch_file)
        # out_df = pd.DataFrame(raw_data_records)

        # # Convert all object columns to string
        # for col in out_df.select_dtypes(['object']):
        #     out_df[col] = out_df[col].astype(str)

        # # Save as Parquet/Feather
        # output_path = r"debug\raw_data_full_patched_ocr"  # Adjust path as needed
        # save_df_fast(out_df, output_path, format='feather')
        # print("Finished patching RAW Data:", patch_file)