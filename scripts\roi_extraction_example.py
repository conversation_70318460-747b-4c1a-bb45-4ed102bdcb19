import asyncio
from src.atom.extraction.extractionconfig import RoiExtractionConfig
from src.atom.extraction.extraction import RoiExtraction


async def main():
    brock_pid = 1
    axis_pid = 7
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0017 - Alliant RICE IPL Dual Sites/Recieved/177493_Mech_Combined.pdf", brock_pid)
    filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0022 - BSL026683/received/BSL026683 ISOs Combined.pdf", brock_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0023 - Duke Energy Dual Fuel Auxiliary Boiler Project/received/MAR_00_DUKE_M-0446 Combined.pdf", brock_pid)
    filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0014 - MPLX Secretariat 1/received/MPLX ISO Combined.pdf", brock_pid)
    filename, project_id = ("C:/Drawings/Clients/axisindustries/Axis 2025-08-05 - Heartwell OSBL/received/Heartwell OSBL Combined.pdf", axis_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0026 - BSLTX34469 - 2025-1231 - OLIN C-650 AIR COMPRESSOR REPLACEMENT/received/20250804+C-650_11x17_Combined.pdf", brock_pid)
    filename, project_id = ("C:/Drawings/Clients/axisindustries/Axis 2025-08-07 - Bluebonnet field route Isos/received/STEAM FR & CFR COMBINED.pdf", axis_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0028 - BSLTX34282 Phillips 66 Lake Charles I-10 General Construction/received/P66- ISO Binder Combined.pdf", brock_pid)
    filename, project_id = ("C:/Drawings/Clients/axisindustries/Axis 2025-07-22 Novellis KEQ3 Rebid/received/combined (2).pdf", axis_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0022 - BSL026683/received/BSL026683 ISOs Combined.pdf", brock_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0030 - STI - ONEOK Medford/data/Isos.pdf", brock_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0020 - BSLTX33994/received/BSLTX33994 - combined.pdf", brock_pid)
    # filename, project_id = ("C:/Drawings/Clients/brockservices/BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL #107/received/20250812+S160001-0083850__IFB.pdf", brock_pid)

    config = RoiExtractionConfig(
        project_id = project_id,
        filename = filename,
        # roi_layout = roi_layout,
    )
    # Specific configuration
    config.extract_general = False
    # config.extract_tables = False
    # config.tables_to_extract = ["bom"]
    config.replace_non_standard_characters = True

    config.enable_ocr = False
    # config.enable_concurrency = False

    # config.pages_to_extract = "4-20"
    # config.pages_to_extract = "90"

    # config.pages_to_extract = "116"
    # config.test_page_limit = 1
    # config.pages_to_extract = "2,18"
    config.tables_to_extract = ["bom"]
    # config.pages_to_extract = "170"
    # config.pages_to_extract = ""
    # print(config.pages_to_extract)

    extractor = RoiExtraction(config=config)
    a = await extractor.run_extraction()

    extractor.save_results("debug/extraction_results/all_results.xlsx")

    print("Finished extraction:", extractor.duration, "seconds")

    # TODO
    # "merge_to_bom": [ "lineNumber", "drawing", "sheet", "area", "revision", "pid" ],

if __name__ == "__main__":
    asyncio.run(main())


