# File: c:\Users\<USER>\Documents\GitHub\Architekt-ATOM\src\utils\pdf\detectgroups_ui.py

import os
import sys
from pathlib import Path
from collections import Counter

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

import pandas as pd
import numpy as np
from PIL import Image
import fitz  # PyMuPDF
import json

# Import the detectgroups module
from src.utils.pdf.detectgroups import detect_page_groups, PageLayoutDetector


class GroupsModel(QAbstractTableModel):
    """Table model for displaying group information"""
    def __init__(self):
        super().__init__()
        self._data = []  # List of (group_id, page_count) tuples
        self._headers = ["Group", "Pages"]
        self._empty_groups = set()  # Track empty groups explicitly

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            if index.column() == 0:
                return f"Group {self._data[index.row()][0]}"
            else:
                return str(self._data[index.row()][1])

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return None

    def updateGroups(self, pages_df):
        """Update the groups model based on the pages dataframe"""
        self.beginResetModel()

        # Count pages in each group
        group_counts = {}
        if 'group' in pages_df.columns:
            for group in pages_df['group'].unique():
                # Don't count placeholder rows (pdf_page = 0)
                count = len(pages_df[(pages_df['group'] == group) & (pages_df['pdf_page'] > 0)])
                group_counts[group] = count

        # Add empty groups that aren't in the dataframe
        for group in self._empty_groups:
            if group not in group_counts:
                group_counts[group] = 0

        # Sort groups by ID
        self._data = [(group, count) for group, count in sorted(group_counts.items())]

        self.endResetModel()

    def addGroup(self):
        """Add a new empty group"""
        self.beginResetModel()

        # Find the next available group number
        next_group = 1
        if self._data:
            existing_groups = [g[0] for g in self._data]
            next_group = max(existing_groups) + 1

        # Add the new group with 0 pages
        self._data.append((next_group, 0))

        self.endResetModel()
        return next_group

    def removeGroup(self, row):
        """Remove a group"""
        if 0 <= row < len(self._data):
            self.beginResetModel()
            group_id = self._data[row][0]
            self._data.pop(row)
            self.endResetModel()
            return group_id
        return None

    def getGroupId(self, row):
        """Get the group ID for a given row"""
        if 0 <= row < len(self._data):
            return self._data[row][0]
        return None


class PagesModel(QAbstractTableModel):
    """Table model for displaying page information"""
    def __init__(self):
        super().__init__()
        self._data = pd.DataFrame(columns=['pdf_page', 'group'])
        self._filtered_data = self._data.copy()
        self._headers = ["Page", "Group"]
        self._current_filter = None

    def rowCount(self, parent=None):
        return len(self._filtered_data)

    def columnCount(self, parent=None):
        return len(self._headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            value = self._filtered_data.iloc[index.row(), index.column()]
            return str(value)

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return None

    def setDataFrame(self, dataframe):
        """Set the full dataframe and reset filtering"""
        self.beginResetModel()
        self._data = dataframe
        self.applyFilter(self._current_filter)
        self.endResetModel()

    def applyFilter(self, group=None):
        """Filter pages to show only those in the specified group"""
        self.beginResetModel()
        self._current_filter = group

        if group is None:
            # No filter, show all pages
            self._filtered_data = self._data.copy()
        else:
            # Filter by group
            self._filtered_data = self._data[self._data['group'] == group].copy()

        self.endResetModel()

    def movePages(self, page_indices, target_group):
        """Move selected pages to a different group"""
        if not page_indices:
            return False

        # Get the actual page numbers from the filtered view
        page_numbers = [int(self._filtered_data.iloc[idx, 0]) for idx in page_indices]

        # Update the group for these pages in the full dataframe
        for page_num in page_numbers:
            self._data.loc[self._data['pdf_page'] == page_num, 'group'] = target_group

        # Re-apply the current filter
        self.applyFilter(self._current_filter)

        return True

    def getDataFrame(self):
        """Get the full (unfiltered) dataframe"""
        return self._data

    def getPageNumber(self, row):
        """Get the page number for a given row"""
        if 0 <= row < len(self._filtered_data):
            return int(self._filtered_data.iloc[row, 0])
        return None

    def getGroup(self, row):
        """Get the group for a given row"""
        if 0 <= row < len(self._filtered_data):
            return int(self._filtered_data.iloc[row, 1])
        return None


class PageDragDropTable(QTableView):
    """Table view that supports drag and drop for moving pages between groups"""

    pagesDropped = Signal(list, int)  # page_indices, target_group

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QTableView.DragDrop)
        self.setSelectionBehavior(QTableView.SelectRows)
        self.setSelectionMode(QTableView.ExtendedSelection)

    def mouseMoveEvent(self, event):
        """Start drag operation when mouse is moved with button pressed"""
        if not (event.buttons() & Qt.LeftButton):
            return

        # Check if we have any selected rows
        if not self.selectedIndexes():
            return

        # Start drag operation
        drag = QDrag(self)
        mime_data = QMimeData()

        # Store selected row indices
        selected_rows = set()
        for index in self.selectedIndexes():
            selected_rows.add(index.row())

        # Store as string data
        mime_data.setText(','.join(map(str, selected_rows)))
        drag.setMimeData(mime_data)

        # Execute drag operation
        result = drag.exec_(Qt.MoveAction)

    def dragEnterEvent(self, event):
        """Accept drag enter events from other widgets"""
        event.accept()

    def dragMoveEvent(self, event):
        """Accept drag move events"""
        event.accept()

    def dropEvent(self, event):
        """Handle drop events to move pages between groups"""
        # Get the target row
        index = self.indexAt(event.pos())
        if not index.isValid():
            event.ignore()
            return

        # Get the target group
        target_group = self.model().getGroup(index.row())
        if target_group is None:
            event.ignore()
            return

        # Get the source rows
        mime_data = event.mimeData()
        if not mime_data.hasText():
            event.ignore()
            return

        # Parse the row indices
        try:
            source_rows = [int(idx) for idx in mime_data.text().split(',')]
            self.pagesDropped.emit(source_rows, target_group)
            event.accept()
        except Exception as e:
            print(f"Drop error: {str(e)}")
            event.ignore()


class GroupDragDropTable(QTableView):
    """Table view that accepts drops for groups"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setSelectionBehavior(QTableView.SelectRows)
        self.setSelectionMode(QTableView.SingleSelection)

    def dragEnterEvent(self, event):
        """Accept drag enter events from the pages table"""
        event.accept()

    def dragMoveEvent(self, event):
        """Accept drag move events"""
        event.accept()

    def dropEvent(self, event):
        """Handle drop events to move pages to this group"""
        # Get the target row (group)
        index = self.indexAt(event.pos())
        if not index.isValid():
            event.ignore()
            return

        # Get the target group
        target_group = self.model().getGroupId(index.row())
        if target_group is None:
            event.ignore()
            return

        # Get the source rows
        mime_data = event.mimeData()
        if not mime_data.hasText():
            event.ignore()
            return

        # Parse the row indices
        try:
            source_rows = [int(idx) for idx in mime_data.text().split(',')]
            # Find the parent widget (GroupsWidget) to access its methods
            parent = self.parent()
            while parent and not isinstance(parent, GroupsWidget):
                parent = parent.parent()

            if parent:
                # Call the on_pages_dropped method
                parent.on_pages_dropped(source_rows, target_group)

            event.accept()
        except Exception as e:
            print(f"Group drop error: {str(e)}")
            event.ignore()


class ROI:
    """Class representing a Region of Interest (ROI)"""

    def __init__(self, x1, y1, x2, y2, name=None, color=None):
        """Initialize ROI with coordinates and optional name and color"""
        self.x1 = min(x1, x2)  # Ensure x1 is the smaller value
        self.y1 = min(y1, y2)  # Ensure y1 is the smaller value
        self.x2 = max(x1, x2)  # Ensure x2 is the larger value
        self.y2 = max(y1, y2)  # Ensure y2 is the larger value
        self.name = name or f"ROI_{id(self)}"
        self.color = color or QColor(255, 0, 0, 128)  # Semi-transparent red by default
        self.group_id = None

    def contains_point(self, x, y):
        """Check if point is inside ROI"""
        return (self.x1 <= x <= self.x2) and (self.y1 <= y <= self.y2)

    def get_rect(self, page_width, page_height):
        """Get QRectF for drawing on a page of given dimensions"""
        return QRectF(
            self.x1 * page_width,
            self.y1 * page_height,
            (self.x2 - self.x1) * page_width,
            (self.y2 - self.y1) * page_height
        )

    def get_handle_at_point(self, x, y):
        """
        Check if point is on a resize handle
        Returns: None, 'top-left', 'top-right', 'bottom-left', 'bottom-right',
                'top', 'right', 'bottom', 'left', or 'inside'
        """
        # Handle size in relative coordinates
        handle_size = 0.01  # 1% of image size

        # Check corners first (they take precedence)
        if abs(x - self.x1) <= handle_size and abs(y - self.y1) <= handle_size:
            return 'top-left'
        elif abs(x - self.x2) <= handle_size and abs(y - self.y1) <= handle_size:
            return 'top-right'
        elif abs(x - self.x1) <= handle_size and abs(y - self.y2) <= handle_size:
            return 'bottom-left'
        elif abs(x - self.x2) <= handle_size and abs(y - self.y2) <= handle_size:
            return 'bottom-right'

        # Then check sides
        elif abs(y - self.y1) <= handle_size and self.x1 <= x <= self.x2:
            return 'top'
        elif abs(x - self.x2) <= handle_size and self.y1 <= y <= self.y2:
            return 'right'
        elif abs(y - self.y2) <= handle_size and self.x1 <= x <= self.x2:
            return 'bottom'
        elif abs(x - self.x1) <= handle_size and self.y1 <= y <= self.y2:
            return 'left'

        # Check if inside
        elif self.contains_point(x, y):
            return 'inside'

        # Not on any handle or inside
        return None

    def to_dict(self):
        """Convert ROI to dictionary for serialization"""
        return {
            'x1': self.x1,
            'y1': self.y1,
            'x2': self.x2,
            'y2': self.y2,
            'name': self.name,
            'color': [self.color.red(), self.color.green(), self.color.blue(), self.color.alpha()],
            'group_id': self.group_id
        }

    @classmethod
    def from_dict(cls, data):
        """Create ROI from dictionary"""
        roi = cls(
            data['x1'],
            data['y1'],
            data['x2'],
            data['y2'],
            data.get('name')
        )

        # Set color if available
        if 'color' in data:
            roi.color = QColor(*data['color'])
        if 'group_id' in data:
            roi.group_id = data['group_id']
        return roi


class ROIListModel:
    """Model for managing ROIs"""

    def __init__(self):
        self._data = {}  # Dictionary mapping group_id to list of ROIs
        self._current_group = None

    def setCurrentGroup(self, group_id):
        """Set the current group for displaying ROIs"""
        self._current_group = group_id

    def addROI(self, roi):
        """Add a new ROI to the current group"""
        if self._current_group is None:
            return

        if self._current_group not in self._data:
            self._data[self._current_group] = []

        roi.group_id = self._current_group
        self._data[self._current_group].append(roi)

    def removeROI(self, index):
        """Remove ROI at the specified index"""
        if self._current_group is None or self._current_group not in self._data:
            return

        if 0 <= index < len(self._data[self._current_group]):
            del self._data[self._current_group][index]

    def getROI(self, index):
        """Get ROI at the specified index"""
        if self._current_group is None or self._current_group not in self._data:
            return None

        if 0 <= index < len(self._data[self._current_group]):
            return self._data[self._current_group][index]
        return None

    def getROIs(self, group_id=None):
        """Get all ROIs for a group"""
        if group_id is None:
            group_id = self._current_group

        if group_id is None or group_id not in self._data:
            return []

        return self._data[group_id]

    def clear(self):
        """Clear all ROIs"""
        self._data = {}

    def toDict(self):
        """Convert all ROIs to dictionary for serialization"""
        result = {}
        for group_id, rois in self._data.items():
            result[str(group_id)] = [roi.to_dict() for roi in rois]
        return result

    def fromDict(self, data):
        """Load ROIs from dictionary"""
        self._data = {}
        for group_id, rois_data in data.items():
            group_id = int(group_id)  # Convert back to integer
            self._data[group_id] = [ROI.from_dict(roi_data) for roi_data in rois_data]


class ZoomablePreviewArea(QWidget):
    """Widget for displaying a zoomable PDF page preview with ROI drawing capabilities"""

    roiCreated = Signal(float, float, float, float)  # x1, y1, x2, y2
    roiSelected = Signal(int)  # ROI index
    roiMoved = Signal(int, float, float, float, float)  # index, new_x1, new_y1, new_x2, new_y2
    roiResized = Signal(int, float, float, float, float)  # index, new_x1, new_y1, new_x2, new_y2

    def __init__(self, parent=None):
        super().__init__(parent)
        self.zoom_factor = 1.0
        self.pixmap = None
        self.original_pixmap = None
        self.drawing_roi = False
        self.start_point = None
        self.current_point = None
        self.rois = []  # List of ROIs for the current group
        self.selected_roi_indices = []  # List of selected ROI indices
        self.roi_mode = False  # Whether in ROI drawing mode
        self.roi_model = ROIListModel()
        self.fit_mode = False  # Whether fit mode is active

        # For preventing accidental zoom during selection
        self.selection_in_progress = False

        # For panning
        self.panning = False
        self.pan_start_point = None
        self.scroll_bar_positions = (0, 0)

        # For ROI moving
        self.moving_roi = False
        self.move_start_point = None
        self.roi_original_positions = []  # List of (x1, y1, x2, y2) for each selected ROI

        # For ROI resizing
        self.resizing_roi = False
        self.resize_handle = None
        self.resize_roi_index = -1
        self.resize_original_position = None  # (x1, y1, x2, y2)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create toolbar for ROI controls
        roi_toolbar = QToolBar()

        self.roi_mode_action = QAction("Draw ROI", self)
        self.roi_mode_action.setCheckable(True)
        self.roi_mode_action.toggled.connect(self.toggle_roi_mode)
        roi_toolbar.addAction(self.roi_mode_action)

        roi_toolbar.addSeparator()

        self.delete_roi_action = QAction("Delete ROI", self)
        self.delete_roi_action.triggered.connect(self.delete_selected_roi)
        self.delete_roi_action.setEnabled(False)
        roi_toolbar.addAction(self.delete_roi_action)

        self.rename_roi_action = QAction("Rename ROI", self)
        self.rename_roi_action.triggered.connect(self.rename_selected_roi)
        self.rename_roi_action.setEnabled(False)
        roi_toolbar.addAction(self.rename_roi_action)

        # Create zoom controls
        zoom_layout = QHBoxLayout()

        self.zoom_out_btn = QPushButton("-")
        self.zoom_out_btn.setFixedSize(30, 30)
        self.zoom_out_btn.clicked.connect(self.zoom_out)

        self.zoom_in_btn = QPushButton("+")
        self.zoom_in_btn.setFixedSize(30, 30)
        self.zoom_in_btn.clicked.connect(self.zoom_in)

        self.zoom_reset_btn = QPushButton("100%")
        self.zoom_reset_btn.clicked.connect(self.zoom_reset)

        self.zoom_fit_btn = QPushButton("Fit")
        self.zoom_fit_btn.setCheckable(True)
        self.zoom_fit_btn.clicked.connect(self.zoom_fit)

        zoom_layout.addWidget(self.zoom_out_btn)
        zoom_layout.addWidget(self.zoom_in_btn)
        zoom_layout.addWidget(self.zoom_reset_btn)
        zoom_layout.addWidget(self.zoom_fit_btn)
        zoom_layout.addStretch()

        # Create scroll area for the image
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)

        # Create label for the image
        self.image_label = QLabel("Select a page to preview")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: #f0f0f0;")

        # Add label to scroll area
        self.scroll_area.setWidget(self.image_label)

        # Add widgets to layout
        layout.addWidget(roi_toolbar)
        layout.addLayout(zoom_layout)
        layout.addWidget(self.scroll_area, 1)

        # Enable mouse tracking for ROI drawing
        self.image_label.setMouseTracking(True)
        self.image_label.installEventFilter(self)

        # Enable keyboard focus
        self.setFocusPolicy(Qt.StrongFocus)
        self.image_label.setFocusPolicy(Qt.StrongFocus)

        # Add global shortcut for ROI drawing mode
        self.shift_shortcut = QShortcut(QKeySequence(Qt.Key_Shift), self)
        self.shift_shortcut.activated.connect(self.enable_roi_mode)
        self.shift_shortcut.activatedAmbiguously.connect(self.enable_roi_mode)

    def screen_to_relative_coords(self, pos):
        """Convert screen coordinates to relative coordinates (0-1)"""
        if not self.pixmap or self.pixmap.isNull():
            return 0, 0

        # Get the label's content rect (where the image is actually displayed)
        content_rect = self.image_label.contentsRect()

        # Get the actual displayed image size
        pixmap_size = self.pixmap.size()

        # Calculate the image position within the label (accounting for centering)
        img_x = max(0, (content_rect.width() - pixmap_size.width()) // 2)
        img_y = max(0, (content_rect.height() - pixmap_size.height()) // 2)

        # Adjust position relative to the actual image position
        adjusted_x = pos.x() - img_x
        adjusted_y = pos.y() - img_y

        # Convert to relative coordinates (0-1) and clamp to valid range
        x_rel = max(0, min(1, adjusted_x / max(1, pixmap_size.width())))
        y_rel = max(0, min(1, adjusted_y / max(1, pixmap_size.height())))

        return x_rel, y_rel

    def relative_to_screen_coords(self, x_rel, y_rel):
        """Convert relative coordinates (0-1) to screen coordinates"""
        if not self.pixmap or self.pixmap.isNull():
            return QPoint(0, 0)

        # Get the label's content rect
        content_rect = self.image_label.contentsRect()

        # Get the actual displayed image size
        pixmap_size = self.pixmap.size()

        # Calculate the image position within the label (accounting for centering)
        img_x = max(0, (content_rect.width() - pixmap_size.width()) // 2)
        img_y = max(0, (content_rect.height() - pixmap_size.height()) // 2)

        # Convert relative coordinates to screen coordinates
        x = int(x_rel * pixmap_size.width()) + img_x
        y = int(y_rel * pixmap_size.height()) + img_y

        return QPoint(x, y)

    def update_zoom(self):
        """Update the displayed image based on zoom factor"""
        if self.original_pixmap:
            scaled_pixmap = self.original_pixmap.scaled(
                int(self.original_pixmap.width() * self.zoom_factor),
                int(self.original_pixmap.height() * self.zoom_factor),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.pixmap = scaled_pixmap

            # Create a new pixmap for drawing ROIs
            if self.rois or (self.drawing_roi and self.start_point and self.current_point):
                result_pixmap = QPixmap(scaled_pixmap)
                painter = QPainter(result_pixmap)

                # Draw all ROIs
                for i, roi in enumerate(self.rois):
                    pen = QPen(roi.color)
                    pen.setWidth(2)
                    if i in self.selected_roi_indices:
                        pen.setWidth(3)
                        pen.setStyle(Qt.DashLine)
                    painter.setPen(pen)

                    rect = roi.get_rect(scaled_pixmap.width(), scaled_pixmap.height())
                    painter.drawRect(rect)

                    # Draw ROI name
                    painter.drawText(
                        rect.x() + 5,
                        rect.y() + 15,
                        roi.name
                    )

                # Draw current ROI being created
                if self.drawing_roi and self.start_point and self.current_point:
                    pen = QPen(QColor(0, 255, 0, 200))
                    pen.setWidth(2)
                    pen.setStyle(Qt.DashLine)
                    painter.setPen(pen)

                    # Convert screen coordinates to relative coordinates and back
                    # This ensures consistent behavior with the existing ROIs
                    start_x_rel, start_y_rel = self.screen_to_relative_coords(self.start_point)
                    current_x_rel, current_y_rel = self.screen_to_relative_coords(self.current_point)

                    # Calculate rectangle coordinates in screen space
                    x1 = min(start_x_rel, current_x_rel) * scaled_pixmap.width()
                    y1 = min(start_y_rel, current_y_rel) * scaled_pixmap.height()
                    width = abs(current_x_rel - start_x_rel) * scaled_pixmap.width()
                    height = abs(current_y_rel - start_y_rel) * scaled_pixmap.height()

                    painter.drawRect(QRectF(x1, y1, width, height))

                painter.end()
                self.image_label.setPixmap(result_pixmap)
            else:
                self.image_label.setPixmap(scaled_pixmap)

            self.image_label.resize(scaled_pixmap.size())

    def eventFilter(self, obj, event):
        """Handle mouse events for ROI drawing, panning, and ROI moving"""
        if obj == self.image_label and self.pixmap and not self.pixmap.isNull():
            # Handle key press events for toggling ROI mode with Shift key
            if event.type() == QEvent.KeyPress:
                if event.key() == Qt.Key_Shift:
                    # Enable ROI drawing mode on Shift press
                    self.roi_mode = True
                    self.roi_mode_action.setChecked(True)

                    # Reset selection when entering drawing mode
                    self.selected_roi_indices = []
                    self.delete_roi_action.setEnabled(False)
                    self.rename_roi_action.setEnabled(False)

                    self.update_zoom()
                    return True

            # Handle key release events
            elif event.type() == QEvent.KeyRelease:
                if event.key() == Qt.Key_Shift:
                    # Disable ROI drawing mode on Shift release
                    self.roi_mode = False
                    self.roi_mode_action.setChecked(False)
                    self.update_zoom()
                    return True

            # Handle wheel events for zooming
            elif event.type() == QEvent.Wheel:
                if event.modifiers() == Qt.ControlModifier:
                    # If we're in the middle of moving, resizing, or selecting, don't zoom
                    if self.moving_roi or self.resizing_roi or self.selection_in_progress:
                        return True

                    # Ctrl+wheel for zooming
                    delta = event.angleDelta().y()
                    if delta > 0:
                        # Zoom in
                        self.zoom_factor *= 1.1
                    else:
                        # Zoom out
                        self.zoom_factor *= 0.9

                    # Turn off fit mode when manually zooming
                    if self.fit_mode:
                        self.fit_mode = False
                        self.zoom_fit_btn.setChecked(False)

                    self.update_zoom()
                    return True

            # Handle mouse move for cursor changes
            elif event.type() == QEvent.MouseMove:
                if not self.drawing_roi and not self.moving_roi and not self.resizing_roi and not self.panning:
                    # Update cursor based on position
                    pos = event.pos()
                    x_rel, y_rel = self.screen_to_relative_coords(pos)

                    # Check if over any ROI handle
                    cursor_changed = False
                    for i, roi in enumerate(self.rois):
                        handle = roi.get_handle_at_point(x_rel, y_rel)
                        if handle:
                            if handle == 'top-left' or handle == 'bottom-right':
                                self.setCursor(Qt.SizeFDiagCursor)
                            elif handle == 'top-right' or handle == 'bottom-left':
                                self.setCursor(Qt.SizeBDiagCursor)
                            elif handle == 'top' or handle == 'bottom':
                                self.setCursor(Qt.SizeVerCursor)
                            elif handle == 'left' or handle == 'right':
                                self.setCursor(Qt.SizeHorCursor)
                            elif handle == 'inside':
                                self.setCursor(Qt.OpenHandCursor)
                            cursor_changed = True
                            break

                    if not cursor_changed:
                        self.setCursor(Qt.ArrowCursor)

                # Handle ROI drawing
                if self.drawing_roi and self.start_point:
                    # Drawing ROI
                    self.current_point = event.pos()
                    self.update_zoom()
                    return True
                # Handle ROI moving
                elif self.moving_roi and self.move_start_point and self.selected_roi_indices:
                    # Moving ROI(s)
                    current_pos = event.pos()

                    # Calculate the delta in relative coordinates
                    current_x_rel, current_y_rel = self.screen_to_relative_coords(current_pos)
                    start_x_rel, start_y_rel = self.screen_to_relative_coords(self.move_start_point)

                    dx = current_x_rel - start_x_rel
                    dy = current_y_rel - start_y_rel

                    # Update all selected ROIs
                    for i, idx in enumerate(self.selected_roi_indices):
                        if idx < len(self.rois):
                            roi = self.rois[idx]
                            orig_pos = self.roi_original_positions[i]

                            # Calculate new positions
                            roi.x1 = max(0, min(1, orig_pos[0] + dx))
                            roi.y1 = max(0, min(1, orig_pos[1] + dy))
                            roi.x2 = max(0, min(1, orig_pos[2] + dx))
                            roi.y2 = max(0, min(1, orig_pos[3] + dy))

                    self.update_zoom()
                    return True
                # Handle ROI resizing
                elif self.resizing_roi and self.move_start_point and self.resize_roi_index >= 0:
                    # Resizing ROI
                    current_pos = event.pos()

                    # Calculate the current position in relative coordinates
                    current_x_rel, current_y_rel = self.screen_to_relative_coords(current_pos)

                    # Get the ROI being resized
                    roi = self.rois[self.resize_roi_index]
                    orig_pos = self.resize_original_position

                    # Update coordinates based on which handle is being dragged
                    if self.resize_handle == 'top-left':
                        roi.x1 = max(0, min(orig_pos[2] - 0.01, current_x_rel))
                        roi.y1 = max(0, min(orig_pos[3] - 0.01, current_y_rel))
                    elif self.resize_handle == 'top-right':
                        roi.x2 = max(orig_pos[0] + 0.01, min(1, current_x_rel))
                        roi.y1 = max(0, min(orig_pos[3] - 0.01, current_y_rel))
                    elif self.resize_handle == 'bottom-left':
                        roi.x1 = max(0, min(orig_pos[2] - 0.01, current_x_rel))
                        roi.y2 = max(orig_pos[1] + 0.01, min(1, current_y_rel))
                    elif self.resize_handle == 'bottom-right':
                        roi.x2 = max(orig_pos[0] + 0.01, min(1, current_x_rel))
                        roi.y2 = max(orig_pos[1] + 0.01, min(1, current_y_rel))
                    elif self.resize_handle == 'top':
                        roi.y1 = max(0, min(orig_pos[3] - 0.01, current_y_rel))
                    elif self.resize_handle == 'right':
                        roi.x2 = max(orig_pos[0] + 0.01, min(1, current_x_rel))
                    elif self.resize_handle == 'bottom':
                        roi.y2 = max(orig_pos[1] + 0.01, min(1, current_y_rel))
                    elif self.resize_handle == 'left':
                        roi.x1 = max(0, min(orig_pos[2] - 0.01, current_x_rel))

                    self.update_zoom()
                    return True
                # Handle panning
                elif self.panning and self.pan_start_point:
                    # Panning the view
                    delta = event.pos() - self.pan_start_point
                    self.scroll_area.horizontalScrollBar().setValue(
                        self.scroll_bar_positions[0] - delta.x()
                    )
                    self.scroll_area.verticalScrollBar().setValue(
                        self.scroll_bar_positions[1] - delta.y()
                    )
                    return True

            # Handle mouse press events
            elif event.type() == QEvent.MouseButtonPress:
                if event.button() == Qt.LeftButton:
                    # Set selection in progress flag if Ctrl is pressed
                    if event.modifiers() == Qt.ControlModifier:
                        self.selection_in_progress = True

                    if self.roi_mode:
                        # Start drawing ROI
                        self.drawing_roi = True
                        self.start_point = event.pos()
                        self.current_point = event.pos()
                        self.update_zoom()
                        return True
                    else:
                        # Check if clicked on an existing ROI
                        pos = event.pos()
                        x_rel, y_rel = self.screen_to_relative_coords(pos)

                        # Check for resize handles first
                        for i, roi in enumerate(self.rois):
                            handle = roi.get_handle_at_point(x_rel, y_rel)
                            if handle and handle != 'inside':
                                # Start resizing the ROI
                                self.resizing_roi = True
                                self.resize_handle = handle
                                self.resize_roi_index = i
                                self.resize_original_position = (roi.x1, roi.y1, roi.x2, roi.y2)
                                self.move_start_point = event.pos()

                                # Update UI
                                self.selected_roi_indices = [i]
                                self.delete_roi_action.setEnabled(True)
                                self.rename_roi_action.setEnabled(True)

                                # Set appropriate cursor
                                if handle == 'top-left' or handle == 'bottom-right':
                                    self.setCursor(Qt.SizeFDiagCursor)
                                elif handle == 'top-right' or handle == 'bottom-left':
                                    self.setCursor(Qt.SizeBDiagCursor)
                                elif handle == 'top' or handle == 'bottom':
                                    self.setCursor(Qt.SizeVerCursor)
                                elif handle == 'left' or handle == 'right':
                                    self.setCursor(Qt.SizeHorCursor)

                                self.update_zoom()
                                self.roiSelected.emit(i)
                                return True

                        # Check if clicked inside an ROI (for moving)
                        clicked_roi_index = -1
                        for i, roi in enumerate(self.rois):
                            handle = roi.get_handle_at_point(x_rel, y_rel)
                            if handle == 'inside':
                                clicked_roi_index = i
                                break

                        if clicked_roi_index >= 0:
                            # Handle ROI selection
                            if event.modifiers() == Qt.ControlModifier:
                                # Ctrl+click for multi-select
                                if clicked_roi_index in self.selected_roi_indices:
                                    self.selected_roi_indices.remove(clicked_roi_index)
                                else:
                                    self.selected_roi_indices.append(clicked_roi_index)
                            else:
                                # Regular click selects only this ROI
                                self.selected_roi_indices = [clicked_roi_index]

                            # Start moving the ROI
                            self.moving_roi = True
                            self.move_start_point = event.pos()
                            self.setCursor(Qt.ClosedHandCursor)

                            # Store original positions of all selected ROIs
                            self.roi_original_positions = []
                            for idx in self.selected_roi_indices:
                                roi = self.rois[idx]
                                self.roi_original_positions.append((roi.x1, roi.y1, roi.x2, roi.y2))

                            # Update UI
                            self.delete_roi_action.setEnabled(len(self.selected_roi_indices) > 0)
                            self.rename_roi_action.setEnabled(len(self.selected_roi_indices) == 1)

                            # Turn off ROI drawing mode when selecting an ROI
                            if self.roi_mode:
                                self.roi_mode = False
                                self.roi_mode_action.setChecked(False)

                            self.update_zoom()
                            self.roiSelected.emit(clicked_roi_index)
                            return True
                        else:
                            # Clicked outside any ROI
                            if event.modifiers() != Qt.ControlModifier:
                                # Clear selection unless Ctrl is held
                                self.selected_roi_indices = []
                                self.delete_roi_action.setEnabled(False)
                                self.rename_roi_action.setEnabled(False)
                                self.update_zoom()

                elif event.button() == Qt.MiddleButton:
                    # Start panning
                    self.panning = True
                    self.pan_start_point = event.pos()
                    self.scroll_bar_positions = (
                        self.scroll_area.horizontalScrollBar().value(),
                        self.scroll_area.verticalScrollBar().value()
                    )
                    self.setCursor(Qt.ClosedHandCursor)
                    return True

            # Handle mouse release events
            elif event.type() == QEvent.MouseButtonRelease:
                # Reset selection in progress flag
                self.selection_in_progress = False

                if event.button() == Qt.LeftButton:
                    if self.drawing_roi and self.start_point and self.current_point:
                        # Finish drawing ROI
                        self.drawing_roi = False

                        # Convert to relative coordinates
                        x1_rel, y1_rel = self.screen_to_relative_coords(self.start_point)
                        x2_rel, y2_rel = self.screen_to_relative_coords(self.current_point)

                        # Ensure minimum size
                        if abs(x2_rel - x1_rel) > 0.01 and abs(y2_rel - y1_rel) > 0.01:
                            # Emit signal to create ROI
                            self.roiCreated.emit(
                                min(x1_rel, x2_rel),
                                min(y1_rel, y2_rel),
                                max(x1_rel, x2_rel),
                                max(y1_rel, y2_rel)
                            )

                        self.start_point = None
                        self.current_point = None
                        self.update_zoom()
                        return True
                    elif self.moving_roi and self.move_start_point and self.selected_roi_indices:
                        # Finish moving ROI(s)
                        self.moving_roi = False
                        self.move_start_point = None
                        self.setCursor(Qt.ArrowCursor)

                        # Emit signals for each moved ROI
                        for idx in self.selected_roi_indices:
                            if idx < len(self.rois):
                                roi = self.rois[idx]
                                self.roiMoved.emit(idx, roi.x1, roi.y1, roi.x2, roi.y2)

                        self.roi_original_positions = []
                        return True
                    elif self.resizing_roi and self.resize_roi_index >= 0:
                        # Finish resizing ROI
                        self.resizing_roi = False
                        self.setCursor(Qt.ArrowCursor)

                        # Emit signal for resized ROI
                        roi = self.rois[self.resize_roi_index]
                        self.roiResized.emit(self.resize_roi_index, roi.x1, roi.y1, roi.x2, roi.y2)

                        self.resize_roi_index = -1
                        self.resize_handle = None
                        self.resize_original_position = None
                        return True

                elif event.button() == Qt.MiddleButton:
                    # Stop panning
                    self.panning = False
                    self.pan_start_point = None
                    self.setCursor(Qt.ArrowCursor)
                    return True

        return super().eventFilter(obj, event)

    def enable_roi_mode(self):
        """Enable ROI drawing mode (triggered by Shift shortcut)"""
        self.roi_mode = True
        self.roi_mode_action.setChecked(True)

        # Reset selection when entering drawing mode
        self.selected_roi_indices = []
        self.delete_roi_action.setEnabled(False)
        self.rename_roi_action.setEnabled(False)

        self.update_zoom()

        # Set up a timer to disable ROI mode when Shift is released
        QTimer.singleShot(100, self.check_shift_key)

    def check_shift_key(self):
        """Check if Shift key is still pressed, if not, disable ROI mode"""
        modifiers = QApplication.keyboardModifiers()
        if not (modifiers & Qt.ShiftModifier):
            self.roi_mode = False
            self.roi_mode_action.setChecked(False)
            self.update_zoom()
        else:
            # Still pressed, check again in a bit
            QTimer.singleShot(100, self.check_shift_key)

    def toggle_roi_mode(self, checked):
        """Toggle ROI drawing mode"""
        self.roi_mode = checked
        self.delete_roi_action.setEnabled(False)
        self.rename_roi_action.setEnabled(False)
        self.selected_roi_indices = []
        self.update_zoom()

    def delete_selected_roi(self):
        """Delete the selected ROIs"""
        if self.selected_roi_indices:
            # Sort indices in descending order to avoid index shifting during removal
            for idx in sorted(self.selected_roi_indices, reverse=True):
                if idx < len(self.rois):
                    del self.rois[idx]

            self.selected_roi_indices = []
            self.delete_roi_action.setEnabled(False)
            self.rename_roi_action.setEnabled(False)
            self.update_zoom()

    def rename_selected_roi(self):
        """Rename the selected ROI"""
        if self.selected_roi_indices:
            roi = self.rois[self.selected_roi_indices[0]]
            roi.name = "ROI " + str(self.selected_roi_indices[0] + 1)
            self.update_zoom()

    def set_pixmap(self, pixmap):
        """Set the pixmap to display"""
        if pixmap:
            self.original_pixmap = pixmap

            # Apply fit mode if active
            if self.fit_mode:
                self.zoom_fit()
            else:
                self.update_zoom()
        else:
            self.image_label.setText("No preview available")
            self.original_pixmap = None
            self.pixmap = None

    def set_text(self, text):
        """Set text to display when no image is available"""
        self.image_label.setText(text)
        self.original_pixmap = None
        self.pixmap = None

    def zoom_in(self):
        """Increase zoom factor"""
        self.zoom_factor *= 1.25
        self.update_zoom()

    def zoom_out(self):
        """Decrease zoom factor"""
        self.zoom_factor *= 0.8
        self.update_zoom()

    def zoom_reset(self):
        """Reset zoom to 100%"""
        self.zoom_factor = 1.0
        self.update_zoom()

    def zoom_fit(self):
        """Fit image to view and toggle fit mode"""
        if self.original_pixmap and not self.original_pixmap.isNull():
            # Toggle fit mode
            self.fit_mode = self.zoom_fit_btn.isChecked()

            if self.fit_mode:
                view_size = self.scroll_area.viewport().size()
                pixmap_size = self.original_pixmap.size()

                width_ratio = view_size.width() / pixmap_size.width()
                height_ratio = view_size.height() / pixmap_size.height()

                self.zoom_factor = min(width_ratio, height_ratio) * 0.95  # 95% of fit
                self.update_zoom()


class DetectGroupsTool(QWidget):
    """Widget for managing page groups in PDF documents"""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.pdf_path = None
        self.output_dir = None
        self.doc = None  # Store the PDF document

        # Create models
        self.groups_model = GroupsModel()
        self.pages_model = PagesModel()
        self.roi_model = ROIListModel()

        self.setWindowTitle("PDF Page Groups")
        self.setMinimumSize(1000, 700)

        self.setup_ui()

    def setup_ui(self):
        """Create the user interface"""
        main_layout = QVBoxLayout(self)

        # File selection area
        file_group = QGroupBox("PDF Selection")
        file_layout = QHBoxLayout()

        self.pdf_path_label = QLabel("No file selected")
        self.pdf_path_label.setWordWrap(True)

        browse_button = QPushButton("Browse...")
        browse_button.clicked.connect(self.on_browse_file)

        file_layout.addWidget(self.pdf_path_label, 1)
        file_layout.addWidget(browse_button)
        file_group.setLayout(file_layout)

        # Parameters area
        params_group = QGroupBox("Grouping Parameters")
        params_layout = QFormLayout()

        self.strictness_combo = QComboBox()
        self.strictness_combo.addItems([
            "very_loose", "loose", "medium", "strict", "very_strict"
        ])
        self.strictness_combo.setCurrentText("medium")

        self.dpi_spin = QSpinBox()
        self.dpi_spin.setRange(72, 300)
        self.dpi_spin.setValue(150)
        self.dpi_spin.setSingleStep(25)

        self.multiprocessing_check = QCheckBox("Use multiprocessing")
        self.multiprocessing_check.setChecked(True)

        self.visualize_check = QCheckBox("Create visualizations")
        self.visualize_check.setChecked(True)

        self.excel_check = QCheckBox("Save to Excel")
        self.excel_check.setChecked(True)

        # Create horizontal layout for checkboxes
        checkboxes_layout = QHBoxLayout()
        checkboxes_layout.addWidget(self.multiprocessing_check)
        checkboxes_layout.addWidget(self.visualize_check)
        checkboxes_layout.addWidget(self.excel_check)
        checkboxes_layout.addStretch()

        params_layout.addRow("Grouping strictness:", self.strictness_combo)
        params_layout.addRow("DPI:", self.dpi_spin)
        params_layout.addRow("Options:", checkboxes_layout)

        params_group.setLayout(params_layout)

        # Action buttons
        actions_layout = QHBoxLayout()

        self.detect_button = QPushButton("Detect Groups")
        self.detect_button.clicked.connect(self.on_detect_groups)
        self.detect_button.setEnabled(False)

        self.save_button = QPushButton("Save Groups")
        self.save_button.clicked.connect(self.on_save_groups)
        self.save_button.setEnabled(False)

        self.open_output_button = QPushButton("Open Output Folder")
        self.open_output_button.clicked.connect(self.on_open_output)
        self.open_output_button.setEnabled(False)

        actions_layout.addWidget(self.detect_button)
        actions_layout.addWidget(self.save_button)
        actions_layout.addWidget(self.open_output_button)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # Main content area with splitters
        main_splitter = QSplitter(Qt.Horizontal)

        # Left panel: Tables area (groups and pages)
        tables_panel = QWidget()
        tables_layout = QVBoxLayout(tables_panel)

        # Create horizontal splitter for the two tables
        tables_splitter = QSplitter(Qt.Horizontal)

        # Groups table panel
        groups_panel = QWidget()
        groups_layout = QVBoxLayout(groups_panel)

        groups_label = QLabel("Groups")
        groups_label.setStyleSheet("font-weight: bold;")

        self.groups_table = GroupDragDropTable(self)
        self.groups_table.setModel(self.groups_model)
        self.groups_table.selectionModel().selectionChanged.connect(self.on_group_selected)

        # Set column widths for groups table
        self.groups_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Group
        self.groups_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Pages

        # Group management buttons
        group_buttons_layout = QHBoxLayout()

        add_group_button = QPushButton("Add Group")
        add_group_button.clicked.connect(self.on_add_group)

        remove_group_button = QPushButton("Remove Group")
        remove_group_button.clicked.connect(self.on_remove_group)

        group_buttons_layout.addWidget(add_group_button)
        group_buttons_layout.addWidget(remove_group_button)

        groups_layout.addWidget(groups_label)
        groups_layout.addWidget(self.groups_table)
        groups_layout.addLayout(group_buttons_layout)

        # Pages table panel
        pages_panel = QWidget()
        pages_layout = QVBoxLayout(pages_panel)

        pages_label = QLabel("Pages")
        pages_label.setStyleSheet("font-weight: bold;")

        self.pages_table = PageDragDropTable(self)
        self.pages_table.setModel(self.pages_model)
        self.pages_table.selectionModel().selectionChanged.connect(self.on_page_selected)
        self.pages_table.pagesDropped.connect(self.on_pages_dropped)

        # Set column widths for pages table
        self.pages_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Page
        self.pages_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Group

        pages_layout.addWidget(pages_label)
        pages_layout.addWidget(self.pages_table)

        # Add tables to the horizontal splitter
        tables_splitter.addWidget(groups_panel)
        tables_splitter.addWidget(pages_panel)
        tables_splitter.setSizes([200, 300])  # Set initial sizes

        # Add the tables splitter to the tables panel
        tables_layout.addWidget(tables_splitter)

        # Right panel: Preview area
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)

        # Header with fixed height
        preview_header = QWidget()
        preview_header.setFixedHeight(30)  # Fixed height for the header
        preview_header_layout = QHBoxLayout(preview_header)
        preview_header_layout.setContentsMargins(0, 0, 0, 0)

        preview_label = QLabel("Preview")
        preview_label.setStyleSheet("font-weight: bold;")
        preview_header_layout.addWidget(preview_label)
        preview_header_layout.addStretch()

        # Create zoomable preview area
        self.preview_area = ZoomablePreviewArea()
        self.preview_area.roiCreated.connect(self.on_roi_created)
        self.preview_area.roiSelected.connect(self.on_roi_selected)
        self.preview_area.roiMoved.connect(self.on_roi_moved)
        self.preview_area.roiResized.connect(self.on_roi_resized)

        # Add widgets to preview layout
        preview_layout.addWidget(preview_header)
        preview_layout.addWidget(self.preview_area, 1)  # Give it stretch factor

        # Add panels to main splitter
        main_splitter.addWidget(tables_panel)
        main_splitter.addWidget(preview_widget)
        main_splitter.setSizes([500, 500])  # Equal sizes

        # Add all components to main layout
        main_layout.addWidget(file_group)
        main_layout.addWidget(params_group)
        main_layout.addLayout(actions_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(main_splitter, 1)

    def update_roi_list(self, group_id):
        """Update the ROI list for the selected group"""
        self.roi_model.setCurrentGroup(group_id)
        self.preview_area.rois = self.roi_model.getROIs(group_id)
        self.preview_area.update_zoom()

    def on_roi_created(self, x1, y1, x2, y2):
        """Handle new ROI creation"""
        # Generate a unique name for the ROI
        roi_count = len(self.roi_model.getROIs())
        name = f"ROI_{roi_count + 1}"

        # Create a random color for the ROI
        color = QColor(
            np.random.randint(100, 255),
            np.random.randint(100, 255),
            np.random.randint(100, 255),
            120  # Medium transparency
        )

        # Create new ROI
        roi = ROI(x1, y1, x2, y2, name, color)

        # Add to model
        self.roi_model.addROI(roi)

        # Update preview area
        self.preview_area.rois = self.roi_model.getROIs()

        # Turn off ROI drawing mode after creating an ROI
        self.preview_area.roi_mode = False
        self.preview_area.roi_mode_action.setChecked(False)

        # Update UI
        self.update_roi_list(self.roi_model._current_group)
        self.preview_area.update_zoom()

    def on_roi_selected(self, index):
        """Handle ROI selection in preview area"""
        self.preview_area.selected_roi_indices = [index]

    def on_roi_moved(self, index, x1, y1, x2, y2):
        """Handle ROI movement"""
        roi = self.roi_model.getROI(index)
        if roi:
            roi.x1 = x1
            roi.y1 = y1
            roi.x2 = x2
            roi.y2 = y2
            self.update_roi_list(self.roi_model._current_group)

    def on_roi_resized(self, index, x1, y1, x2, y2):
        """Handle ROI resizing"""
        roi = self.roi_model.getROI(index)
        if roi:
            roi.x1 = x1
            roi.y1 = y1
            roi.x2 = x2
            roi.y2 = y2
            self.update_roi_list(self.roi_model._current_group)

    def on_browse_file(self):
        """Open file dialog to select a PDF file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select PDF File", "", "PDF Files (*.pdf)"
        )

        if file_path:
            self.pdf_path = file_path
            self.pdf_path_label.setText(file_path)
            self.detect_button.setEnabled(True)

            # Load PDF and initialize with all pages in group 1
            try:
                # Close previous document if open
                if self.doc:
                    self.doc.close()

                # Open the PDF
                self.doc = fitz.open(self.pdf_path)
                page_count = self.doc.page_count

                # Create default grouping (all pages in group 1)
                default_groups = pd.DataFrame({
                    'pdf_page': range(1, page_count + 1),  # 1-based page numbers
                    'group': [1] * page_count  # All pages in group 1
                })

                # Update the models
                self.pages_model.setDataFrame(default_groups)
                self.groups_model.updateGroups(default_groups)

                # Reset preview
                self.preview_area.set_text("Select a page to preview")
                self.open_output_button.setEnabled(False)
                self.save_button.setEnabled(True)

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to load PDF: {str(e)}"
                )
                self.pdf_path = None
                self.pdf_path_label.setText("No file selected")
                self.detect_button.setEnabled(False)
                self.save_button.setEnabled(False)

    def on_detect_groups(self):
        """Run the group detection process"""
        if not self.pdf_path:
            return

        # Get parameters
        strictness = self.strictness_combo.currentText()
        dpi = self.dpi_spin.value()
        use_multiprocessing = self.multiprocessing_check.isChecked()
        visualize = self.visualize_check.isChecked()
        save_excel = self.excel_check.isChecked()

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate

        # Disable UI during processing
        self.detect_button.setEnabled(False)

        try:
            # Run detection
            report_path = detect_page_groups(
                pdf_path=self.pdf_path,
                output_dir=None,  # Use default (same dir as PDF)
                dpi=dpi,
                use_multiprocessing=use_multiprocessing,
                visualize=visualize,
                save_excel=save_excel,
                grouping_strictness=strictness
            )

            # Get output directory
            self.output_dir = os.path.dirname(report_path)

            # Load results
            groups_path = os.path.splitext(report_path)[0].replace('_clusters', '_groups') + '.xlsx'
            if os.path.exists(groups_path):
                groups_df = pd.read_excel(groups_path)
                self.pages_model.setDataFrame(groups_df)
                self.groups_model.updateGroups(groups_df)

            # Enable output folder button
            self.open_output_button.setEnabled(True)
            self.save_button.setEnabled(True)

            # Show success message
            QMessageBox.information(
                self,
                "Detection Complete",
                f"Page groups detected and saved to:\n{groups_path}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred during group detection:\n{str(e)}"
            )

        finally:
            # Reset UI
            self.progress_bar.setVisible(False)
            self.detect_button.setEnabled(True)

    def on_save_groups(self):
        """Save the current groups to a file"""
        if not self.pdf_path:
            return

        try:
            # Get the output directory
            if self.output_dir is None:
                self.output_dir = os.path.dirname(self.pdf_path)

            # Create output directory if it doesn't exist
            os.makedirs(self.output_dir, exist_ok=True)

            # Get base filename without extension
            base_name = os.path.splitext(os.path.basename(self.pdf_path))[0]

            # Create output Excel file path
            excel_path = os.path.join(self.output_dir, f"{base_name}_groups.xlsx")

            # Get groups data
            groups_data = self.pages_model.getDataFrame()

            # Save to Excel
            groups_data.to_excel(excel_path, index=False)

            # Save ROIs to JSON
            roi_data = self.roi_model.toDict()
            roi_path = os.path.join(self.output_dir, f"{base_name}_rois.json")

            with open(roi_path, 'w') as f:
                json.dump(roi_data, f, indent=2)

            # Show success message
            QMessageBox.information(
                self,
                "Save Successful",
                f"Groups saved to {excel_path}\nROIs saved to {roi_path}"
            )

            # Enable open output button
            self.open_output_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(
                self,
                "Save Error",
                f"Error saving groups: {str(e)}"
            )

    def on_open_output(self):
        """Open the output folder in file explorer"""
        if self.output_dir and os.path.exists(self.output_dir):
            os.startfile(self.output_dir)

    def on_group_selected(self, selected, deselected):
        """Update pages table when a group is selected"""
        if not selected.indexes():
            return

        # Get the selected group ID
        row = selected.indexes()[0].row()
        group_id = self.groups_model.getGroupId(row)

        if group_id is not None:
            # Filter pages to show only those in the selected group
            self.pages_model.applyFilter(group_id)
            self.update_roi_list(group_id)

    def on_page_selected(self, selected, deselected):
        """Update preview when a page is selected in the table"""
        if not selected.indexes():
            return

        # Get the selected page number (1-based in the table)
        row = selected.indexes()[0].row()
        page_num = self.pages_model.getPageNumber(row)

        if page_num is not None:
            # Load and display the page (convert to 0-based)
            self.show_page_preview(page_num - 1)

    def on_pages_dropped(self, page_indices, target_group):
        """Handle pages being dropped onto a different group"""
        print(f"Pages dropped: {page_indices} to group {target_group}")

        # If target group is in empty groups, remove it since it will have pages now
        if target_group in self.groups_model._empty_groups:
            self.groups_model._empty_groups.remove(target_group)

        # Move the pages to the target group
        if self.pages_model.movePages(page_indices, target_group):
            # Update the groups model
            self.groups_model.updateGroups(self.pages_model.getDataFrame())

    def on_add_group(self):
        """Add a new empty group"""
        # Find the next available group number
        next_group = 1

        # Find the next available group number
        pages_df = self.pages_model.getDataFrame()
        if not pages_df.empty and 'group' in pages_df.columns:
            existing_groups = pages_df['group'].unique()
            if len(existing_groups) > 0:
                next_group = int(max(existing_groups)) + 1

        # Also check the empty groups
        existing_empty_groups = self.groups_model._empty_groups
        if existing_empty_groups:
            next_group = max(next_group, max(existing_empty_groups) + 1)

        # Add to empty groups set
        self.groups_model._empty_groups.add(next_group)

        # Update the groups model
        current_groups = [(g[0], g[1]) for g in self.groups_model._data]
        current_groups.append((next_group, 0))

        self.groups_model.beginResetModel()
        self.groups_model._data = current_groups
        self.groups_model.endResetModel()

    def on_remove_group(self):
        """Remove the selected group"""
        indexes = self.groups_table.selectedIndexes()
        if not indexes:
            return

        row = indexes[0].row()
        group_id = self.groups_model.getGroupId(row)

        if group_id is None:
            return

        # Check if the group has pages
        pages_df = self.pages_model.getDataFrame()
        group_pages = pages_df[pages_df['group'] == group_id]

        if len(group_pages) > 0:
            # Ask what to do with the pages
            msg_box = QMessageBox()
            msg_box.setWindowTitle("Remove Group")
            msg_box.setText(f"Group {group_id} has {len(group_pages)} pages.")
            msg_box.setInformativeText("What would you like to do with these pages?")
            move_button = msg_box.addButton("Move to another group", QMessageBox.ActionRole)
            delete_button = msg_box.addButton("Delete pages", QMessageBox.ActionRole)
            cancel_button = msg_box.addButton("Cancel", QMessageBox.RejectRole)

            msg_box.exec_()

            if msg_box.clickedButton() == move_button:
                # Get all other groups
                all_groups = pages_df['group'].unique()
                other_groups = [g for g in all_groups if g != group_id]

                if not other_groups:
                    QMessageBox.warning(self, "Error", "No other groups to move pages to.")
                    return

                # Move pages to the first other group
                target_group = other_groups[0]
                pages_df.loc[pages_df['group'] == group_id, 'group'] = target_group

                # Update the models
                self.pages_model.setDataFrame(pages_df)

            elif msg_box.clickedButton() == delete_button:
                # Remove pages from this group
                pages_df = pages_df[pages_df['group'] != group_id]

                # Update the models
                self.pages_model.setDataFrame(pages_df)

            elif msg_box.clickedButton() == cancel_button:
                return

        # Remove from empty groups if it was there
        if group_id in self.groups_model._empty_groups:
            self.groups_model._empty_groups.remove(group_id)

        # Update the groups model
        self.groups_model.updateGroups(pages_df)

    def show_page_preview(self, page_num):
        """Show preview of the selected page"""
        if not self.doc or not self.pdf_path or not os.path.exists(self.pdf_path):
            return

        try:
            if 0 <= page_num < self.doc.page_count:
                # Render the page
                page = self.doc[page_num]
                pix = page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))  # Higher resolution for zooming

                # Convert to QImage and then QPixmap
                img = QImage(pix.samples, pix.width, pix.height, pix.stride, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(img)

                # Set the pixmap in the preview area
                self.preview_area.set_pixmap(pixmap)

                # Update ROIs for the current group
                group_id = self.groups_model.getGroupId(self.groups_table.selectedIndexes()[0].row())
                if group_id is not None:
                    self.roi_model.setCurrentGroup(group_id)
                    self.preview_area.rois = self.roi_model.getROIs(group_id)
                    self.preview_area.update_zoom()

        except Exception as e:
            self.preview_area.set_text(f"Error loading preview: {str(e)}")

    def closeEvent(self, event):
        """Clean up resources when the widget is closed"""
        if self.doc:
            self.doc.close()
        event.accept()


def main():
    app = QApplication(sys.argv)
    widget = DetectGroupsTool()
    widget.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()