"""
_atom: internal values which should be fixed and not changed
by custom themes
"""

import os
import json

from collections import OrderedDict
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from PIL import ImageColor

from src.app_paths import resource_path

if __name__ == "__main__":
    import sys
    sys.path[0] = ""

def gradient(color: str, step=5):
    """Generates a similar color used for gradient

    step - lighter for positive, negative for darker color
    """
    rgb = ImageColor.getcolor(color, "RGB")
    r, g, b = rgb
    rgb = {"r": r, "g": g, "b": b}
    rgb = dict(sorted(rgb.items(), key=lambda item: item[1]))

    new = {}
    for n, (k, x) in enumerate(rgb.items(), start=1):
        v = x + ((n * step))
        v = min(v, 255)
        v = max(v, 0)
        new[k] = v

    final = []
    for x in new.values():
        final.append(x)

    return f"rgb({','.join(str(n) for n in final)})"


_atom = {
    "atom.orange": "#F7941D",
    "atom.lightorange": "#F7A71D",
    "atom.activecell": "#f4df73",
}

defaults_light = {

}

defaults_dark = {
    "red": "#FF2222",
    "green": "green",
    "white": "#FAFAFC",
    "black": "#000000",
    "grey1": "#E5E6EB",
    "grey2": "#D1D3DA",
    "grey3": "#BEC0C9",
    "grey4": "#888C93",
    "grey5": "#44474B",
    "grey6": "#222426",

    "panel.background": "#001526",
    "foreground": "#cdcfd2",
    "button.background": "#5993c5",
    "button.hoverBackground": "#1251BB",
    "button.pressedBackground": "#326DED",
    "disabled.background": "#1d2229",

    "topbar.background": "#001526",

    "secondary.background": "#1e293b",

    "tabbutton.background": "#001526",

    "toolbar.background": "#292f39",
    "toolbar.foreground": "#cdcfd2",
    "toolbarButton.background": "#001526",
    "toolbarButton.hoverBackground": "#1251BB",
    "toolbarButton.pressedBackground": "#001526",
    "sidebar.background": "#001526",
    "sidebar.hover": "#1251BB",

    "statusbar.background": "#001526",

    "menu.background": "#001526",

    "input.background": "#292f39",

    # "scrollbar.handle": "#001526",
    "scrollbar.handle": "#F7A71D",
}

themes = OrderedDict()
themes["default-light"] = {
    "background": "#ffffff",
    "color1": "#C3CBFF",
    "color2": "#6288F7",
    "color3": "#6288F7",
    "color4": "#326DED",
    "color5": "#1251BB",
    "color6": "#053E8A",
    "color7": "#012C58",
    "color8": "#001526",
}


# https://goodpalette.io/326ded-4fcc14-abafb8
themes["default-blue-light"] = {
    "background": "#ffffff",
    "color1": "#C3CBFF",
    "color2": "#6288F7",
    "color3": "#6288F7",
    "color4": "#326DED",
    "color5": "#1251BB",
    "color6": "#053E8A",
    "color7": "#012C58",
    "color8": "#001526",
    "green": "#4FCC14",
}

# DCF2F1 - lightest
# 7FC7D9
# 365486
# 0F1035 - darkest

def _getJsonTheme(name: str):
    file = resource_path(f"src/data/themes/{name}.json")
    with open(file) as f:
        d = json.load(f)
        return d

def getStylesheet(themeName):
    # print(_getJsonTheme("default-color-theme"))
    qss_path = resource_path("src/data/themes/stylesheet.qss")
    """ Load the stylesheet and substitute in the theme """
    with open(qss_path) as f:
        s = "\n".join(f.readlines())

    # Base global theme
    theme: dict = themes[themeName]
    theme.update(_atom)
    for key, value in defaults_dark.items():
        theme.setdefault(key, value)

    added = {}
    for key, color in theme.items():

        # Leave keys inserted in correctly to
        # avoid string replacing 'er'

        lighter = gradient(color, step=6)
        added[f"{key}.lighter"] = lighter
        # print(f"{key}.light", lighter)

        light = gradient(color, step=2)
        added[f"{key}.light"] = light
        # print(f"{key}.light", light)

        if key == "secondary.background":
            pass

        darker = gradient(color, step=-6)
        added[f"{key}.darker"] = darker

        dark = gradient(color, step=-2)
        added[f"{key}.dark"] = dark

        # print(f"{key}.dark", dark)



    for key, value in added.items():
        s = s.replace(f"@{key}", value)

    for key, value in theme.items():
        s = s.replace(f"@{key}", value)

        # print(f"{key}.darker", darker)
    # logger.info("Stylesheet", s)
    # print(s)

    print(theme["topbar.background"])
    return s

def initFonts(app):
    """ TODO: custom fonts """
    # Env vars to deal with jagged fonts on higher dpi screens
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    os.environ["QT_SCALE_FACTOR"] = "1"
    id = QFontDatabase.addApplicationFont(resource_path("src/resources/fonts/Inter-Regular.ttf"))
    id = QFontDatabase.addApplicationFont(resource_path("src/resources/fonts/FiraSans-Medium.ttf"))
    id = QFontDatabase.addApplicationFont(resource_path("src/resources/fonts/Assistant-SemiBold.ttf"))
    if id < 0: print("Error")
    families = QFontDatabase.applicationFontFamilies(id)
    font = QFont(families[0])
    font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
    app.setFont(font)

stylesheet = getStylesheet("default-blue-light")


if __name__ == "__main__":
    import sys
    sys.path[0] = ""
    # outputs theme
    # for name, theme in themes.items():

    #     print(name)
    #     for key, color in theme.items():

    #         print(key, color)

    #         light = gradient(color)
    #         print(f"{key}.light", light)

    #         lighter = gradient(color, step=10)
    #         print(f"{key}.light", lighter)

    #         dark = gradient(color, step=-5)
    #         print(f"{key}.dark", dark)

    #         darker = gradient(color, step=-10)
    #         print(f"{key}.darker", darker)