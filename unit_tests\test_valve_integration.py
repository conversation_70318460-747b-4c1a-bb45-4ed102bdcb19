import pandas as pd
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_valve_integration():
    """
    Test the integration of valve type normalization into the normalize_description workflow.
    """
    print("Testing valve type normalization integration...")
    
    # Create test data with valve descriptions and rfq_scope
    test_data = {
        'material_description': [
            '2" BALL VALVE 800# A105 SW',
            '4" GATE VALVE 150# WCB FLANGED RF', 
            '1" GLOBE VALVE 300# A105 THREADED NPT',
            '6" BUTTERFLY VALVE 150# CI FLANGED',
            '1/2" NEEDLE VALVE 3000# SS316 SW',
            '3" CHECK VALVE 300# WCB RF FLANGED',
            '2" BALL VALVE 600# A105 BW',
            '1" RELIEF VALVE 150# BRONZE THREADED',
            '4" TEE A234 WPB SCH 40 BE',  # Non-valve item
            '2" ELBOW 90 DEG LR A234 WPB SCH 40 BE'  # Non-valve item
        ],
        'rfq_scope': [
            'Valves', 'Valves', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 
            '<PERSON>ves', '<PERSON>ves', 'Valves', 'Fittings', 'Fittings'
        ]
    }
    
    df = pd.DataFrame(test_data)
    print(f"Created test DataFrame with {len(df)} rows")
    
    # Import the normalize_description function
    from normalize_description import normalize_description
    
    # Apply normalization to each description
    print("\nApplying normalize_description to each row...")
    normalized_results = df['material_description'].apply(normalize_description)
    
    # Split the results into separate columns
    df['normalized_description'] = normalized_results.apply(lambda x: x[0])
    df['metadata_tags'] = normalized_results.apply(lambda x: ','.join(x[1]) if x[1] else '')
    df['review_tags'] = normalized_results.apply(lambda x: ','.join(x[2]) if x[2] else '')
    df['match_type'] = normalized_results.apply(lambda x: ','.join(x[3]) if x[3] else '')
    
    # Extract metadata tags to separate columns
    from normalize_description import extract_metadata_tags_to_columns
    df = extract_metadata_tags_to_columns(df)
    
    # Apply valve type normalization
    print("Applying valve type normalization...")
    from normalize_valve_type import normalize_valve_types_dataframe
    df = normalize_valve_types_dataframe(df)
    
    # Display results
    print("\n" + "="*100)
    print("RESULTS:")
    print("="*100)
    
    for idx, row in df.iterrows():
        print(f"\nRow {idx + 1}:")
        print(f"  Original: {row['material_description']}")
        print(f"  RFQ Scope: {row['rfq_scope']}")
        print(f"  Normalized: {row['normalized_description']}")
        print(f"  Metadata: {row['metadata_tags']}")
        if 'general_category' in row and row['general_category']:
            print(f"  Valve Type: {row['general_category']}")
        if 'ends' in row and row['ends']:
            print(f"  End Types: {row['ends']}")
    
    # Summary for valve items
    valve_rows = df[df['rfq_scope'] == 'Valves']
    print(f"\n" + "="*50)
    print("VALVE TYPE SUMMARY:")
    print("="*50)
    
    if 'general_category' in df.columns:
        valve_type_counts = valve_rows['general_category'].value_counts()
        for valve_type, count in valve_type_counts.items():
            print(f"  {valve_type}: {count}")
        
        blank_count = len(valve_rows[valve_rows['general_category'] == ''])
        if blank_count > 0:
            print(f"  Blank (requires review): {blank_count}")
    else:
        print("  No general_category column found")
    
    print(f"\nTest completed successfully!")
    return df

if __name__ == "__main__":
    test_df = test_valve_integration()
