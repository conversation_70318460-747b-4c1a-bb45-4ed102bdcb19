-- BOM Reference Functions
-- 
-- This file contains SQL functions to handle the linking of BOM records to reference tables
-- using rfq_ref_id and gen_ref_id fields.

/*
-- Link all unlinked records:
SELECT * FROM public.link_bom_references();

-- Link records for project ID 1:
SELECT * FROM public.link_bom_references(1);

-- Force relink all records for project ID 1:
SELECT * FROM public.link_bom_references(1, TRUE);
*/

-- Function to link BOM records to RFQ records
CREATE OR REPLACE FUNCTION public.link_bom_to_rfq(
    p_project_id INTEGER DEFAULT NULL,  -- Optional project ID filter
    p_force_relink BOOLEAN DEFAULT FALSE  -- Whether to force relink already linked records
)
RETURNS TABLE (
    linked_to_existing INTEGER,
    new_rfq_created INTEGER,
    unchanged INTEGER
)
AS $$
DECLARE
    v_linked INTEGER := 0;
    v_created INTEGER := 0;
    v_unchanged INTEGER := 0;
    bom_rec RECORD;
    rfq_id INTEGER;
    existing_rfq_id INTEGER;
BEGIN
    -- Build the query to get BOM records to process
    FOR bom_rec IN
        SELECT 
            id, project_id, profile_id, material_description, size, size1, size2,
            rfq_scope, general_category, unit_of_measure,
            material, rating, ends, fitting_category, valve_type,
            calculated_eq_length, calculated_area, quantity, rfq_ref_id
        FROM public.bom
        WHERE (p_project_id IS NULL OR project_id = p_project_id)
        AND (p_force_relink OR rfq_ref_id IS NULL)
    LOOP
        -- Check if an RFQ with matching values already exists
        SELECT id INTO existing_rfq_id
        FROM public.atem_rfq
        WHERE project_id = bom_rec.project_id
        AND UPPER(material_description) = UPPER(bom_rec.material_description)
        AND size::TEXT = bom_rec.size
        LIMIT 1;
        
        IF existing_rfq_id IS NOT NULL THEN
            -- RFQ record already exists
            IF bom_rec.rfq_ref_id IS DISTINCT FROM existing_rfq_id THEN
                -- Update the BOM record with the rfq_ref_id
                UPDATE public.bom
                SET rfq_ref_id = existing_rfq_id, updated_at = CURRENT_TIMESTAMP
                WHERE id = bom_rec.id;
                
                v_linked := v_linked + 1;
            ELSE
                -- Reference is already correct
                v_unchanged := v_unchanged + 1;
            END IF;
        ELSE
            -- No matching RFQ exists, create a new one
            INSERT INTO public.atem_rfq (
                project_id, material_description, size, size1, size2,
                quantity, rfq_scope, general_category, unit_of_measure,
                material, rating, ends, fitting_category, valve_type,
                calculated_eq_length, calculated_area
            ) VALUES (
                bom_rec.project_id,
                bom_rec.material_description,
                bom_rec.size,
                bom_rec.size1,
                bom_rec.size2,
                COALESCE(bom_rec.quantity, 1),
                bom_rec.rfq_scope,
                bom_rec.general_category,
                bom_rec.unit_of_measure,
                bom_rec.material,
                bom_rec.rating,
                bom_rec.ends,
                bom_rec.fitting_category,
                bom_rec.valve_type,
                bom_rec.calculated_eq_length,
                bom_rec.calculated_area
            )
            RETURNING id INTO rfq_id;
            
            -- Link the BOM record to the new RFQ record
            UPDATE public.bom
            SET rfq_ref_id = rfq_id, updated_at = CURRENT_TIMESTAMP
            WHERE id = bom_rec.id;
            
            v_created := v_created + 1;
        END IF;
    END LOOP;
    
    -- Return the results
    RETURN QUERY SELECT v_linked, v_created, v_unchanged;
END;
$$ LANGUAGE plpgsql;

-- Function to link BOM records to General records
CREATE OR REPLACE FUNCTION public.link_bom_to_general(
    p_project_id INTEGER DEFAULT NULL,  -- Optional project ID filter
    p_force_relink BOOLEAN DEFAULT FALSE  -- Whether to force relink already linked records
)
RETURNS TABLE (
    linked_to_general INTEGER,
    unchanged INTEGER
)
AS $$
DECLARE
    v_linked INTEGER := 0;
    v_unchanged INTEGER := 0;
    bom_rec RECORD;
    gen_id INTEGER;
BEGIN
    -- Build the query to get BOM records to process
    FOR bom_rec IN
        SELECT 
            id, project_id, pdf_id, pdf_page, size, gen_ref_id
        FROM public.bom
        WHERE (p_project_id IS NULL OR project_id = p_project_id)
        AND (p_force_relink OR gen_ref_id IS NULL)
        AND pdf_id IS NOT NULL 
        AND pdf_page IS NOT NULL
    LOOP
        -- Check if a matching General record exists
        SELECT id INTO gen_id
        FROM public.general
        WHERE project_id = bom_rec.project_id
        AND pdf_id = bom_rec.pdf_id
        AND pdf_page = bom_rec.pdf_page
        AND (
            -- Handle type conversion with safety checks
            (bom_rec.size ~ E'^\\d+(\\.\\d+)?$' AND size = bom_rec.size::DECIMAL)
            OR
            (size::TEXT = bom_rec.size)
        )
        LIMIT 1;
        
        IF gen_id IS NOT NULL THEN
            -- General record exists
            IF bom_rec.gen_ref_id IS DISTINCT FROM gen_id THEN
                -- Update the BOM record with the gen_ref_id
                UPDATE public.bom
                SET gen_ref_id = gen_id, updated_at = CURRENT_TIMESTAMP
                WHERE id = bom_rec.id;
                
                v_linked := v_linked + 1;
            ELSE
                -- Reference is already correct
                v_unchanged := v_unchanged + 1;
            END IF;
        ELSE
            -- No matching General record found
            v_unchanged := v_unchanged + 1;
        END IF;
    END LOOP;
    
    -- Return the results
    RETURN QUERY SELECT v_linked, v_unchanged;
END;
$$ LANGUAGE plpgsql;

-- Function to combine both linking operations
CREATE OR REPLACE FUNCTION public.link_bom_references(
    p_project_id INTEGER DEFAULT NULL,
    p_force_relink BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    rfq_linked INTEGER,
    rfq_created INTEGER,
    rfq_unchanged INTEGER,
    general_linked INTEGER,
    general_unchanged INTEGER
)
AS $$
DECLARE
    rfq_results RECORD;
    general_results RECORD;
BEGIN
    -- Call the RFQ linking function
    SELECT * INTO rfq_results 
    FROM public.link_bom_to_rfq(p_project_id, p_force_relink);
    
    -- Call the General linking function
    SELECT * INTO general_results 
    FROM public.link_bom_to_general(p_project_id, p_force_relink);
    
    -- Return the combined results
    RETURN QUERY SELECT 
        rfq_results.linked_to_existing,
        rfq_results.new_rfq_created,
        rfq_results.unchanged,
        general_results.linked_to_general,
        general_results.unchanged;
END;
$$ LANGUAGE plpgsql;

-- Example usage:
-- Link all unlinked BOM records to RFQ and General records:
-- SELECT * FROM public.link_bom_references();

-- Link BOM records for a specific project:
-- SELECT * FROM public.link_bom_references(project_id);

-- Force relink all BOM records for a project, even if already linked:
-- SELECT * FROM public.link_bom_references(project_id, TRUE);

-- Link just to RFQ records:
-- SELECT * FROM public.link_bom_to_rfq();

-- Link just to General records:
-- SELECT * FROM public.link_bom_to_general();
