{
	"name": "<PERSON><PERSON>",
	"colors": {
		// Main Godot bg color #363d4a
		"activityBar.background": "#363d4a",
		"banner.background": "#363d4a",
		"titleBar.activeBackground": "#363d4a",
		"titleBar.inactiveBackground": "#363d4a",
		"tab.activeBackground": "#363d4a",
		"panel.background": "#363d4a",
		"list.hoverBackground": "#363d4a",
		"dropdown.listBackground": "#363d4a",
		// A litle darker version of the bg #292f39
		"sideBar.background": "#292f39",
		"tab.inactiveBackground": "#292f39",
		"editorGroupHeader.noTabsBackground": "#292f39",
		"editorGroupHeader.tabsBackground": "#292f39",
		"statusBar.background": "#292f39",
		"input.background": "#292f39",
		"dropdown.background": "#292f39",
		"toolbar.hoverBackground": "#292f39",
		"notifications.background": "#292f39",
		// Base Godot accent color #70bafa
		// Darker accent color #5993c5 
		"button.background": "#5993c5",
		"activityBarBadge.background": "#5993c5",
		// Even darker accent color #22272f
		"button.hoverBackground": "#4d7fab",
		// Debugging colors
		"statusBar.debuggingBackground": "#364359",
		// Other colors
		"editor.background": "#1d2229",
		"editor.foreground": "#cdcfd2",
		"sideBarTitle.foreground": "#bbbbbb",
		"editor.lineHighlightBackground": "#ffffff12",
		"editor.selectionHighlightBackground": "#ffffff12",
		"editorCursor.foreground": "#ffffff",
		// Godot error and warning colors
		"editorError.foreground": "#ff786b",
		"editorWarning.foreground": "#f5ff6b",
		"list.errorForeground": "#ff786b",
		"errorForeground": "#ff786b",
		"problemsErrorIcon.foreground": "#ff786b",
		"list.warningForeground": "#f5ff6b",
		"problemsWarningIcon.foreground": "#f5ff6b",
		// Bracket colorization
		"editorBracketHighlight.foreground1": "#abc9ff",
		"editorBracketHighlight.foreground2": "#ffe3ab",
		"editorBracketHighlight.foreground3": "#aeffab",
		"editorBracketHighlight.foreground4": "#ffabff",
		"editorBracketHighlight.foreground5": "#ffabae",
		"editorBracketHighlight.foreground6": "#abfff9",
	},
	"tokenColors": [
		{
			"name": "Comment",
			"scope": [
				"comment",
				"punctuation.definition.comment"
			],
			"settings": {
				// "fontStyle": "italic",
				"foreground": "#cdcfd280"
			}
		},
		{
			"name": "Variables",
			"scope": [
				"variable",
				"meta.function-call.arguments",
				"meta.indexed-name",
				"meta.attribute",
				"entity.name.variable",
			],
			"settings": {
				"foreground": "#cdcfd2"
			}
		},
		{
			"name": "Properties",
			"scope": [
				"variable.other.property",
				"variable.other.object.property"
			],
			"settings": {
				"foreground": "#bce0ff"
			}
		},
		{
			"name": "Invalid",
			"scope": [
				"invalid",
				"invalid.illegal"
			],
			"settings": {
				"foreground": "#ff786b"
			}
		},
		{
			"name": "Keyword, Storage",
			"scope": [
				"keyword",
				"keyword.control",
				"storage.type",
				"storage.modifier",
				"constant.language",
				"support.constant",
				"keyword.other.unit",
				"keyword.other"
			],
			"settings": {
				"foreground": "#ff7085"
			}
		},
		{
			"name": "Number",
			"scope": [
				"constant.numeric"
			],
			"settings": {
				"foreground": "#a1ffe0"
			}
		},
		{
			"name": "Operator, Misc",
			"scope": [
				"constant.other.color",
				"punctuation",
				"meta.tag",
				"punctuation.definition.tag",
				"punctuation.separator.inheritance.php",
				"punctuation.definition.tag.html",
				"punctuation.definition.tag.begin.html",
				"punctuation.definition.tag.end.html",
				"punctuation.section.embedded",
				"keyword.other.template",
				"keyword.other.substitution",
				"keyword.operator"
			],
			"settings": {
				"foreground": "#abc9ff"
			}
		},
		{
			"name": "Functions",
			"scope": [
				"entity.name.function",
				"meta.function-call",
				"variable.function",
				"support.function",
				"keyword.other.special-method"
			],
			"settings": {
				"foreground": "#57b3ff"
			}
		},
		{
			"name": "String, Symbols, Markup Heading",
			"scope": [
				"string",
				"punctuation.definition.string",
				"punctuation.support.type.property-name",
				"support.type.property-name",
				"storage.type.string",
				"constant.other.symbol",
				"constant.other.key",
				"constant.character",
				"constant.escape",
				"markup.heading",
				"markup.inserted.git_gutter",
				"meta.group.braces.curly constant.other.object.key.js string.unquoted.label.js"
			],
			"settings": {
				"foreground": "#ffeda1"
			}
		},
		{
			"name": "Class",
			"scope": [
				"entity.name",
				"entity.other.inherited-class",
				"keyword.type",
			],
			"settings": {
				"foreground": "#42ffc2"
			}
		},
		{
			"name": "Builtin Classes",
			"scope": [
				"entity.name.class.builtin",
				"support.type",
				"support.class"
			],
			"settings": {
				"foreground": "#8fffdb"
			}
		},
		{
			"name": "Entity Types",
			"scope": [
				"support.type"
			],
			"settings": {
				"foreground": "#B2CCD6"
			}
		},
		{
			"name": "Regular Expressions",
			"scope": [
				"string.regexp"
			],
			"settings": {
				"foreground": "#89DDFF"
			}
		},
		{
			"name": "URL",
			"scope": [
				"*url*",
				"*link*",
				"*uri*"
			],
			"settings": {
				"fontStyle": "underline"
			}
		},
		{
			"name": "Decorators",
			"scope": [
				"tag.decorator.js entity.name.tag.js",
				"tag.decorator.js punctuation.definition.tag.js"
			],
			"settings": {
				"fontStyle": "italic",
				"foreground": "#82AAFF"
			}
		}
	]
}