import os
import pandas as pd

def plugin_import_table_data(
    bom_file: str = "",
    general_file: str = "",
    spool_file: str = "",
    spec_file: str = "",
    outlier_file: str = "",
    rfq_file: str = "",
    ifc_file: str = "",
    generic_1_file: str = "",
    generic_2_file: str = "",
    signals: dict = None
):
    """
    Import data from selected files and stage them for import.

    This plugin allows you to select files for different tables and stage them
    for import. You can then choose whether to append or replace the data in
    each table before importing.

    Args:
        bom_file: Path to BOM data file (CSV, Excel)
        general_file: Path to General data file (CSV, Excel)
        spool_file: Path to Spool data file (CSV, Excel)
        spec_file: Path to Spec data file (CSV, Excel)
        outlier_file: Path to Outlier data file (CSV, Excel)
        rfq_file: Path to RFQ data file (CSV, Excel)
        ifc_file: Path to IFC data file (CSV, Excel)
        generic_1_file: Path to Generic 1 data file (CSV, Excel)
        generic_2_file: Path to Generic 2 data file (CSV, Excel)
        signals: Signal dictionary for staging data

    Returns:
        str: Summary of files processed
    """
    # Collect all file parameters and their values
    file_params = {
        'bom_file': bom_file,
        'general_file': general_file,
        'spool_file': spool_file,
        'spec_file': spec_file,
        'outlier_file': outlier_file,
        'rfq_file': rfq_file,
        'ifc_file': ifc_file,
        'generic_1_file': generic_1_file,
        'generic_2_file': generic_2_file
    }

    # Filter out empty file paths
    selected_files = {param: path for param, path in file_params.items() if path}

    if not selected_files:
        return "No files selected for import."

    # Dictionary to store imported data
    imported_data = {}
    results = []

    # Process each selected file
    for param, file_path in selected_files.items():
        try:
            # Extract table name from parameter name (remove '_file' suffix)
            table_name = param.replace("_file", "")
            # Get the corresponding payload name
            payload_name = f"{table_name}_data"

            # Check if file exists
            if not os.path.exists(file_path):
                results.append(f"Error: File not found - {file_path}")
                continue

            # Determine file type and read data
            if file_path.lower().endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith(('.xls', '.xlsx')):
                df = pd.read_excel(file_path)
            else:
                results.append(f"Error: Unsupported file format for {file_path}")
                continue

            # Store the imported data
            imported_data[payload_name] = df

            # Add result
            results.append(f"Successfully read {len(df)} rows from {os.path.basename(file_path)} for {table_name.capitalize()} table")

        except Exception as e:
            results.append(f"Error importing {param}: {str(e)}")

    # Stage the imported data if we have any
    if imported_data and signals and "stageData" in signals:
        signals["stageData"].emit(imported_data)
        results.append("\nData has been staged for import. Go to the 'Staged Import Data' tab to review and import.")

    # Return summary
    return "\n".join(results)