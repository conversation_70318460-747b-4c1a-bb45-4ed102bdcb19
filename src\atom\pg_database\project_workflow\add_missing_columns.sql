-- Add operation-related fields
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS dp1 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS dp2 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS dt1 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS dt2 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS op1 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS op2 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS opt1 VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS opt2 VARCHAR(50);

-- Add testing-related fields
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS test_type VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS test_pressure VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS test_temp VARCHAR(50);


-- Add identification and specification fields
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS iso_size DECIMAL(12,3);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS spool_id VARCHAR(50);


-- Add additional fields
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS has_revision BOOLEAN;
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS ref_elevation VARCHAR(50);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS cwa VARCHAR(100);

-- Add generic fields
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_1 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_2 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_3 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_4 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_5 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_6 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_7 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_8 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_9 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_10 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_11 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_12 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_13 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_14 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_15 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_16 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_17 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_18 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_19 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_20 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_21 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_22 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_23 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_24 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_25 VARCHAR(255);
ALTER TABLE public.general ADD COLUMN IF NOT EXISTS field_26 VARCHAR(255);

-- Add comments to explain the fields
COMMENT ON COLUMN public.general.dp1 IS 'Design Pressure 1';
COMMENT ON COLUMN public.general.dp2 IS 'Design Pressure 2';
COMMENT ON COLUMN public.general.dt1 IS 'Design Temperature 1';
COMMENT ON COLUMN public.general.dt2 IS 'Design Temperature 2';
COMMENT ON COLUMN public.general.op1 IS 'Operating Pressure 1';
COMMENT ON COLUMN public.general.op2 IS 'Operating Pressure 2';
COMMENT ON COLUMN public.general.opt1 IS 'Operating Temperature 1';
COMMENT ON COLUMN public.general.opt2 IS 'Operating Temperature 2';

COMMENT ON COLUMN public.general.test_type IS 'Type of test (e.g., hydrostatic, pneumatic)';
COMMENT ON COLUMN public.general.test_pressure IS 'Test pressure';
COMMENT ON COLUMN public.general.test_temp IS 'Test temperature';

COMMENT ON COLUMN public.general.iso_size IS 'Main size of the isometric drawing';
COMMENT ON COLUMN public.general.spool_id IS 'Spool number';
COMMENT ON COLUMN public.general.has_revision IS 'Indicates if the document has a revision drawn over or around specified text';
COMMENT ON COLUMN public.general.ref_elevation IS 'Reference elevation';
COMMENT ON COLUMN public.general.cwa IS 'Construction Work Area';