"""
Test script for the data upload auditor.
Creates sample data to test the auditing functionality.
"""

import pandas as pd
import os
from field_mapping import audit_workbook

def create_test_data():
    """Create test Excel file with sample data for testing."""
    
    # Create sample General data
    general_data = {
        'pdf_id': [1, 2, 3, 4, 5],
        'sys_filename': ['file1.pdf', 'file2.pdf', 'file3.pdf', 'file4.pdf', 'file5.pdf'],
        'project_id': [100, 100, 100, 100, 100],
        'lineNumber': ['L001', 'L002', 'L003', 'L004', 'L005'],
        'isoNumber': ['ISO-001', 'ISO-002', 'ISO-003', 'ISO-004', 'ISO-005'],
        'size': ['2"', '4"', '6"', '3"', '1"'],
        'lf': [10.5, 20.0, 15.5, 8.0, 12.5],  # Should be ignored
        'sf': [5.2, 10.0, 7.8, 4.0, 6.2],     # Should be ignored
        'drawing': ['DWG-001', 'DWG-002', 'DWG-003', 'DWG-004', 'DWG-005'],
        'unmapped_field': ['test1', 'test2', 'test3', 'test4', 'test5'],  # Should show as unmapped
        'another_unmapped': [1, 2, 3, 4, 5]  # Should show as unmapped
    }
    
    # Create sample BOM data
    bom_data = {
        'pdf_id': [1, 1, 2, 2, 3],
        'sys_filename': ['file1.pdf', 'file1.pdf', 'file2.pdf', 'file2.pdf', 'file3.pdf'],
        'project_id': [100, 100, 100, 100, 100],
        'materialDescription': ['Pipe', 'Valve', 'Fitting', 'Elbow', 'Tee'],
        'componentCategory': ['Piping', 'Valve', 'Fitting', 'Fitting', 'Fitting'],
        'quantity': [10, 2, 5, 3, 1],
        'size': ['2"', '4"', '2"', '2"', '4"'],
        'ef': [1.0, 0.5, 0.8, 0.3, 0.6],  # Should be ignored
        'unknown_bom_field': ['A', 'B', 'C', 'D', 'E']  # Should show as unmapped
    }
    
    # Create sample Verified Material Classifications data
    verified_data = {
        'id': [1, 2, 3, 4, 5],
        'material_description': ['Carbon Steel Pipe', 'Stainless Steel Valve', 'Bronze Fitting', 'Cast Iron Elbow', 'Alloy Tee'],
        'classification': ['Pipe', 'Valve', 'Fitting', 'Fitting', 'Fitting'],
        'standard': ['ASTM A106', 'ASTM A351', 'ASTM B62', 'ASTM A126', 'ASTM A182'],
        'unmapped_verified_field': ['X', 'Y', 'Z', 'W', 'V']  # Should show as unmapped
    }
    
    # Create test file
    test_file = 'test_data.xlsx'
    
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        pd.DataFrame(general_data).to_excel(writer, sheet_name='General', index=False)
        pd.DataFrame(bom_data).to_excel(writer, sheet_name='BOM', index=False)
        pd.DataFrame(verified_data).to_excel(writer, sheet_name='Verified Material Classifications', index=False)
    
    print(f"✅ Test data created: {test_file}")
    return test_file

def create_test_data_with_errors():
    """Create test data with critical errors for testing error handling."""

    # General data missing critical fields
    general_data_bad = {
        'project_id': [100, 100, 100],
        'lineNumber': ['L001', 'L002', 'L003'],
        'size': ['2"', '4"', '6"'],
        # Missing pdf_id and sys_filename - should cause critical error
    }

    # BOM data with missing values in critical fields
    bom_data_bad = {
        'pdf_id': [1, None, 3],  # Missing value in critical field
        'sys_filename': ['file1.pdf', 'file2.pdf', ''],  # Empty value in critical field
        'project_id': [100, 100, 100],
        'materialDescription': ['Pipe', 'Valve', 'Fitting'],
    }

    test_file_bad = 'test_data_with_errors.xlsx'

    with pd.ExcelWriter(test_file_bad, engine='openpyxl') as writer:
        pd.DataFrame(general_data_bad).to_excel(writer, sheet_name='General', index=False)
        pd.DataFrame(bom_data_bad).to_excel(writer, sheet_name='BOM', index=False)

    print(f"✅ Test data with errors created: {test_file_bad}")
    return test_file_bad

def create_test_data_with_sys_path():
    """Create test data with sys_path but missing sys_filename to test extraction."""

    # General data with sys_path but no sys_filename
    general_data_sys_path = {
        'pdf_id': [1, 2, 3, 4, 5],
        'sys_path': [
            r'C:\Documents\Projects\file1.pdf',
            r'C:\Documents\Projects\file2.pdf',
            r'C:\Documents\Projects\file3.pdf',
            r'C:\Documents\Projects\not_a_pdf.txt',  # Should fail extraction
            ''  # Empty path should fail extraction
        ],
        'project_id': [100, 100, 100, 100, 100],
        'lineNumber': ['L001', 'L002', 'L003', 'L004', 'L005'],
        'size': ['2"', '4"', '6"', '3"', '1"'],
    }

    test_file_sys_path = 'test_data_sys_path.xlsx'

    with pd.ExcelWriter(test_file_sys_path, engine='openpyxl') as writer:
        pd.DataFrame(general_data_sys_path).to_excel(writer, sheet_name='General', index=False)

    print(f"✅ Test data with sys_path created: {test_file_sys_path}")
    return test_file_sys_path

def run_tests():
    """Run comprehensive tests of the auditor."""
    
    print("🧪 RUNNING AUDITOR TESTS")
    print("=" * 50)
    
    # Test 1: Normal data (should pass)
    print("\n📋 TEST 1: Normal Data (Should Pass)")
    print("-" * 30)
    test_file = create_test_data()
    
    try:
        results = audit_workbook(
            input_file=test_file,
            output_file='test_results_normal.xlsx',
            audit_general=True,
            audit_bom=True,
            audit_verified_materials=True
        )
        print("✅ Test 1 completed successfully")
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
    
    # Test 2: Data with critical errors (should fail)
    print("\n📋 TEST 2: Data with Critical Errors (Should Fail)")
    print("-" * 30)
    test_file_bad = create_test_data_with_errors()
    
    try:
        results = audit_workbook(
            input_file=test_file_bad,
            output_file='test_results_errors.xlsx',
            audit_general=True,
            audit_bom=True,
            audit_verified_materials=False
        )
        print("❌ Test 2 should have failed but didn't")
    except Exception as e:
        print(f"✅ Test 2 correctly failed with: {e}")

    # Test 3: Selective auditing
    print("\n📋 TEST 3: Selective Auditing (General Only)")
    print("-" * 30)
    try:
        results = audit_workbook(
            input_file=test_file,
            output_file='test_results_selective.xlsx',
            audit_general=True,
            audit_bom=False,
            audit_verified_materials=False
        )
        print("✅ Test 3 completed successfully")
    except Exception as e:
        print(f"❌ Test 3 failed: {e}")

    # Test 4: sys_filename extraction from sys_path (should pass)
    print("\n📋 TEST 4: sys_filename Extraction from sys_path (Should Pass)")
    print("-" * 30)
    test_file_sys_path = create_test_data_with_sys_path()

    try:
        results = audit_workbook(
            input_file=test_file_sys_path,
            output_file='test_results_sys_path.xlsx',
            audit_general=True,
            audit_bom=False,
            audit_verified_materials=False
        )
        print("✅ Test 4 completed successfully")
    except Exception as e:
        print(f"❌ Test 4 failed: {e}")

    # Test 5: Non-existent file (should fail)
    print("\n📋 TEST 5: Non-existent File (Should Fail)")
    print("-" * 30)
    try:
        results = audit_workbook(
            input_file='non_existent_file.xlsx',
            audit_general=True,
            audit_bom=True,
            audit_verified_materials=True
        )
        print("❌ Test 5 should have failed but didn't")
    except Exception as e:
        print(f"✅ Test 5 correctly failed with: {e}")

    # Cleanup
    print("\n🧹 Cleaning up test files...")
    for file in ['test_data.xlsx', 'test_data_with_errors.xlsx', 'test_data_sys_path.xlsx',
                 'test_results_normal.xlsx', 'test_results_errors.xlsx',
                 'test_results_selective.xlsx', 'test_results_sys_path.xlsx']:
        if os.path.exists(file):
            os.remove(file)
            print(f"   Removed: {file}")

    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    run_tests()
