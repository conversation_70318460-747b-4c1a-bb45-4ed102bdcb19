import os
import fitz  # PyMuPDF
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from concurrent.futures import ProcessPoolExecutor
import logging
from tqdm import tqdm
import time
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def detect_rectangles(page, min_size=10, max_size=None, color_filter=None):
    """
    Detect rectangles in a PDF page that could represent ROIs.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider (default: 10)
        max_size: Maximum size (width or height) in points to consider (default: None)
        color_filter: Optional color filter as (r, g, b) tuple or list of tuples

    Returns:
        List of dictionaries with rectangle coordinates
    """
    rects = []

    # Get the page's drawing operations
    paths = page.get_drawings()

    for path in paths:
        # Check if this is a rectangle
        if len(path["items"]) >= 4:  # Rectangles typically have at least 4 operations
            # Extract coordinates
            rect = None

            # Try to get the rect directly if it's a simple rectangle
            if "rect" in path:
                rect = path["rect"]
            else:
                # Try to compute bounding box from the path items
                try:
                    points = []
                    for item in path["items"]:
                        if item[0] == "l" or item[0] == "m":  # line or move
                            points.append(item[1:3])

                    if points:
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        rect = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                except Exception as e:
                    logger.debug(f"Error computing bounding box: {e}")
                    continue

            if rect:
                # Check if the path is closed (likely a rectangle)
                is_closed = any(item[0] == "h" for item in path["items"])  # 'h' means close path

                # Check size constraints
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]

                if width < min_size or height < min_size:
                    continue

                if max_size and (width > max_size or height > max_size):
                    continue

                # Check color if filter is provided
                if color_filter:
                    # Get stroke color
                    stroke_color = path.get("stroke_color")
                    fill_color = path.get("fill_color")

                    # Skip if no color information
                    if not stroke_color and not fill_color:
                        continue

                    # Use stroke color if available, otherwise fill color
                    color = stroke_color if stroke_color else fill_color

                    # Convert color to RGB if needed
                    if color and len(color) == 3:
                        r, g, b = color

                        # Check if color matches any in the filter
                        if not any(
                            abs(r - fr) < 0.1 and abs(g - fg) < 0.1 and abs(b - fb) < 0.1
                            for fr, fg, fb in ([color_filter] if isinstance(color_filter, tuple) else color_filter)
                        ):
                            continue

                # Add the rectangle to our list
                rects.append({
                    "x1": rect[0],
                    "y1": rect[1],
                    "x2": rect[2],
                    "y2": rect[3],
                    "width": width,
                    "height": height,
                    "is_closed": is_closed,
                    "stroke_color": path.get("stroke_color"),
                    "fill_color": path.get("fill_color"),
                    "stroke_width": path.get("stroke_width", 0),
                    "is_filled": path.get("fill") is not None
                })

    return rects

def detect_text_blocks(page, min_size=10):
    """
    Detect text blocks in a PDF page that could represent ROIs.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with text block coordinates
    """
    blocks = []

    # Get text blocks
    text_page = page.get_textpage()
    dict_blocks = page.get_text("dict")["blocks"]

    for block in dict_blocks:
        if block.get("type") == 0:  # Text block
            x1, y1, x2, y2 = block["bbox"]
            width = x2 - x1
            height = y2 - y1

            # Skip blocks that are too small
            if width < min_size or height < min_size:
                continue

            blocks.append({
                "x1": x1,
                "y1": y1,
                "x2": x2,
                "y2": y2,
                "width": width,
                "height": height,
                "text": block.get("text", ""),
                "type": "text"
            })

    return blocks

def detect_tables(page, min_rows=2, min_cols=2):
    """
    Attempt to detect tables in a PDF page.

    Args:
        page: PyMuPDF page object
        min_rows: Minimum number of rows to consider as a table
        min_cols: Minimum number of columns to consider as a table

    Returns:
        List of dictionaries with table coordinates
    """
    tables = []

    try:
        # This is a simple heuristic approach - for more accurate table detection,
        # consider using specialized libraries like camelot-py or tabula-py

        # Get horizontal and vertical lines
        paths = page.get_drawings()

        h_lines = []
        v_lines = []

        for path in paths:
            # Check if this is a line
            if len(path["items"]) == 2 and path["items"][0][0] == "m" and path["items"][1][0] == "l":
                # Extract start and end points
                start_x, start_y = path["items"][0][1:3]
                end_x, end_y = path["items"][1][1:3]

                # Determine if horizontal or vertical
                if abs(end_y - start_y) < 2:  # Horizontal line
                    h_lines.append((min(start_x, end_x), start_y, max(start_x, end_x), end_y))
                elif abs(end_x - start_x) < 2:  # Vertical line
                    v_lines.append((start_x, min(start_y, end_y), end_x, max(start_y, end_y)))

        # Group lines that might form a table
        if len(h_lines) >= min_rows and len(v_lines) >= min_cols:
            # Sort lines by position
            h_lines.sort(key=lambda x: x[1])  # Sort by y-coordinate
            v_lines.sort(key=lambda x: x[0])  # Sort by x-coordinate

            # Find potential tables by looking for intersections
            # This is a simplified approach - real table detection is more complex
            if h_lines and v_lines:
                # Get the bounding box of the potential table
                table_x1 = min(line[0] for line in v_lines)
                table_y1 = min(line[1] for line in h_lines)
                table_x2 = max(line[2] for line in v_lines)
                table_y2 = max(line[3] for line in h_lines)

                tables.append({
                    "x1": table_x1,
                    "y1": table_y1,
                    "x2": table_x2,
                    "y2": table_y2,
                    "width": table_x2 - table_x1,
                    "height": table_y2 - table_y1,
                    "h_lines": len(h_lines),
                    "v_lines": len(v_lines),
                    "type": "table"
                })
    except Exception as e:
        logger.warning(f"Error detecting tables: {e}")

    return tables

def detect_engineering_tables(page, min_size=50):
    """
    Specialized function to detect tables in engineering drawings like Bill of Materials.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with table coordinates
    """
    tables = []

    # Get text blocks
    dict_blocks = page.get_text("dict")["blocks"]

    # Initialize h_lines and v_lines at the beginning of the function
    paths = page.get_drawings()
    h_lines = []
    v_lines = []

    for path in paths:
        # Check if this is a line
        if len(path["items"]) == 2 and path["items"][0][0] == "m" and path["items"][1][0] == "l":
            # Extract start and end points
            start_x, start_y = path["items"][0][1:3]
            end_x, end_y = path["items"][1][1:3]

            # Determine if horizontal or vertical
            if abs(end_y - start_y) < 2:  # Horizontal line
                h_lines.append((min(start_x, end_x), start_y, max(start_x, end_x), end_y))
            elif abs(end_x - start_x) < 2:  # Vertical line
                v_lines.append((start_x, min(start_y, end_y), end_x, max(start_y, end_y)))

    # First, try to find blocks with "BILL OF MATERIALS" or similar text
    bom_blocks = []
    for block in dict_blocks:
        if block.get("type") == 0:  # Text block
            text = block.get("text", "").upper()
            if "BILL OF MATERIALS" in text or "BOM" in text or "PARTS LIST" in text:
                bom_blocks.append(block)

    # If we found BOM blocks, look for tables nearby
    if bom_blocks:
        # For each BOM block, try to find a table structure below it
        for bom_block in bom_blocks:
            bom_x1, bom_y1, bom_x2, bom_y2 = bom_block["bbox"]

            # Look for horizontal lines below the BOM title
            below_h_lines = [line for line in h_lines if line[1] > bom_y2]

            if below_h_lines:
                # Sort by y-coordinate
                below_h_lines.sort(key=lambda x: x[1])

                # Find vertical lines that intersect with these horizontal lines
                relevant_v_lines = []
                for v_line in v_lines:
                    v_x1, v_y1, v_x2, v_y2 = v_line
                    # Check if vertical line intersects with any horizontal line
                    for h_line in below_h_lines[:5]:  # Check first few horizontal lines
                        h_x1, h_y1, h_x2, h_y2 = h_line
                        if h_x1 <= v_x1 <= h_x2 and v_y1 <= h_y1 <= v_y2:
                            relevant_v_lines.append(v_line)
                            break

                if relevant_v_lines:
                    # Get the bounding box of the potential table
                    table_x1 = min(min(line[0] for line in relevant_v_lines), bom_x1)
                    table_y1 = bom_y1  # Start from BOM title
                    table_x2 = max(max(line[2] for line in relevant_v_lines), bom_x2)
                    table_y2 = max(line[1] for line in below_h_lines[:5])  # Use first few horizontal lines

                    width = table_x2 - table_x1
                    height = table_y2 - table_y1

                    if width >= min_size and height >= min_size:
                        tables.append({
                            "x1": table_x1,
                            "y1": table_y1,
                            "x2": table_x2,
                            "y2": table_y2,
                            "width": width,
                            "height": height,
                            "type": "bom_table"
                        })

    # If we didn't find BOM tables, try a more general approach for tables
    if not tables and len(h_lines) >= 3 and len(v_lines) >= 3:
        # Look for grid patterns that might be tables
        # Sort lines
        h_lines.sort(key=lambda x: x[1])
        v_lines.sort(key=lambda x: x[0])

        # Group horizontal lines that are close to each other
        h_groups = []
        if h_lines:  # Add this check to ensure h_lines is not empty
            current_group = [h_lines[0]]

            for i in range(1, len(h_lines)):
                if h_lines[i][1] - h_lines[i-1][1] < 50:  # Lines close to each other
                    current_group.append(h_lines[i])
                else:
                    if len(current_group) >= 3:  # At least 3 lines in a group
                        h_groups.append(current_group)
                    current_group = [h_lines[i]]

            if len(current_group) >= 3:
                h_groups.append(current_group)

        # For each group of horizontal lines, check if there are vertical lines forming a grid
        for h_group in h_groups:
            # Get the bounding box of this group
            h_x1 = min(line[0] for line in h_group)
            h_y1 = min(line[1] for line in h_group)
            h_x2 = max(line[2] for line in h_group)
            h_y2 = max(line[1] for line in h_group)

            # Find vertical lines that intersect with this group
            intersecting_v_lines = []
            for v_line in v_lines:
                v_x1, v_y1, v_x2, v_y2 = v_line
                # Check if vertical line intersects with any horizontal line
                for h_line in h_group[:5]:  # Check first few horizontal lines
                    h_x1, h_y1, h_x2, h_y2 = h_line
                    if h_x1 <= v_x1 <= h_x2 and v_y1 <= h_y1 <= v_y2:
                        intersecting_v_lines.append(v_line)
                        break

            if len(intersecting_v_lines) >= 3:
                # Get the bounding box of the potential table
                table_x1 = min(line[0] for line in intersecting_v_lines)
                table_y1 = h_y1
                table_x2 = max(line[2] for line in intersecting_v_lines)
                table_y2 = h_y2

                width = table_x2 - table_x1
                height = table_y2 - table_y1

                if width >= min_size and height >= min_size:
                    tables.append({
                        "x1": table_x1,
                        "y1": table_y1,
                        "x2": table_x2,
                        "y2": table_y2,
                        "width": width,
                        "height": height,
                        "type": "grid_table"
                    })

    return tables

def detect_title_blocks(page, min_size=50):
    """
    Detect title blocks in engineering drawings.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with title block coordinates
    """
    title_blocks = []

    # Title blocks are often in the bottom right corner
    page_width = page.rect.width
    page_height = page.rect.height

    # Get text blocks
    dict_blocks = page.get_text("dict")["blocks"]

    # Look for blocks with keywords often found in title blocks
    title_keywords = ["TITLE", "DRAWING", "SCALE", "DATE", "DWG", "REV", "SHEET", "PROJECT", "ENGINEER"]

    # Count keywords in each block
    keyword_counts = {}

    for block in dict_blocks:
        if block.get("type") == 0:  # Text block
            text = block.get("text", "").upper()
            count = sum(1 for keyword in title_keywords if keyword in text)
            if count > 0:
                keyword_counts[block["bbox"]] = count

    # Sort blocks by keyword count (descending)
    sorted_blocks = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)

    # Get the top blocks
    top_blocks = sorted_blocks[:3] if sorted_blocks else []

    for bbox, _ in top_blocks:
        x1, y1, x2, y2 = bbox

        # Title blocks are often in the bottom right corner
        # Check if this block is in the bottom half and right half of the page
        if y1 > page_height / 2 and x1 > page_width / 2:
            # Expand the block to include nearby text
            expanded_x1 = max(0, x1 - 50)
            expanded_y1 = max(0, y1 - 50)
            expanded_x2 = min(page_width, x2 + 50)
            expanded_y2 = min(page_height, y2 + 50)

            # Check for other text blocks within this expanded area
            for block in dict_blocks:
                if block.get("type") == 0:  # Text block
                    bx1, by1, bx2, by2 = block["bbox"]

                    # If this block overlaps with our expanded area
                    if (bx1 < expanded_x2 and bx2 > expanded_x1 and
                        by1 < expanded_y2 and by2 > expanded_y1):
                        # Expand our title block to include this block
                        expanded_x1 = min(expanded_x1, bx1)
                        expanded_y1 = min(expanded_y1, by1)
                        expanded_x2 = max(expanded_x2, bx2)
                        expanded_y2 = max(expanded_y2, by2)

            width = expanded_x2 - expanded_x1
            height = expanded_y2 - expanded_y1

            if width >= min_size and height >= min_size:
                title_blocks.append({
                    "x1": expanded_x1,
                    "y1": expanded_y1,
                    "x2": expanded_x2,
                    "y2": expanded_y2,
                    "width": width,
                    "height": height,
                    "type": "title_block"
                })

    return title_blocks

def detect_drawing_borders(page, min_size=100):
    """
    Detect the main drawing border/viewport in engineering drawings.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with border coordinates
    """
    borders = []

    # Get the page's drawing operations
    paths = page.get_drawings()

    # Look for large rectangles that might be the drawing border
    for path in paths:
        # Check if this is a rectangle
        if "rect" in path:
            rect = path["rect"]
            x1, y1, x2, y2 = rect

            width = x2 - x1
            height = y2 - y1

            # Check if this is a large rectangle (but not the entire page)
            page_width = page.rect.width
            page_height = page.rect.height

            # It should be large but not the entire page
            if (width >= min_size and height >= min_size and
                width < page_width - 10 and height < page_height - 10):

                # Check if it's near the page edges
                near_edge = (x1 < 50 or y1 < 50 or
                            x2 > page_width - 50 or y2 > page_height - 50)

                if near_edge:
                    borders.append({
                        "x1": x1,
                        "y1": y1,
                        "x2": x2,
                        "y2": y2,
                        "width": width,
                        "height": height,
                        "type": "drawing_border"
                    })

    # If we didn't find borders using rectangles, try using lines
    if not borders:
        # Get horizontal and vertical lines
        h_lines = []
        v_lines = []

        for path in paths:
            # Check if this is a line
            if len(path["items"]) == 2 and path["items"][0][0] == "m" and path["items"][1][0] == "l":
                # Extract start and end points
                start_x, start_y = path["items"][0][1:3]
                end_x, end_y = path["items"][1][1:3]

                # Determine if horizontal or vertical
                if abs(end_y - start_y) < 2:  # Horizontal line
                    h_lines.append((min(start_x, end_x), start_y, max(start_x, end_x), end_y))
                elif abs(end_x - start_x) < 2:  # Vertical line
                    v_lines.append((start_x, min(start_y, end_y), end_x, max(start_y, end_y)))

        # Look for long lines near the edges
        page_width = page.rect.width
        page_height = page.rect.height

        edge_h_lines = []
        edge_v_lines = []

        for line in h_lines:
            x1, y1, x2, y2 = line
            length = x2 - x1

            # Long horizontal lines near top or bottom
            if length > page_width * 0.5 and (y1 < 50 or y1 > page_height - 50):
                edge_h_lines.append(line)

        for line in v_lines:
            x1, y1, x2, y2 = line
            length = y2 - y1

            # Long vertical lines near left or right
            if length > page_height * 0.5 and (x1 < 50 or x1 > page_width - 50):
                edge_v_lines.append(line)

        # If we have at least one horizontal and one vertical edge line
        if edge_h_lines and edge_v_lines:
            # Find the bounding box
            x1 = min(line[0] for line in edge_v_lines)
            y1 = min(line[1] for line in edge_h_lines)
            x2 = max(line[2] for line in edge_v_lines)
            y2 = max(line[3] for line in edge_h_lines)

            width = x2 - x1
            height = y2 - y1

            if width >= min_size and height >= min_size:
                borders.append({
                    "x1": x1,
                    "y1": y1,
                    "x2": x2,
                    "y2": y2,
                    "width": width,
                    "height": height,
                    "type": "drawing_border"
                })

    return borders

def detect_isolated_text(page, min_size=10):
    """
    Detect isolated text areas that might be annotations or labels.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with text area coordinates
    """
    text_areas = []

    # Get text blocks
    dict_blocks = page.get_text("dict")["blocks"]

    for block in dict_blocks:
        if block.get("type") == 0:  # Text block
            x1, y1, x2, y2 = block["bbox"]
            width = x2 - x1
            height = y2 - y1

            # Skip blocks that are too small
            if width < min_size or height < min_size:
                continue

            # Get the text content
            text = block.get("text", "").strip()

            # Skip empty blocks
            if not text:
                continue

            # Check if this is an isolated text block (not part of a paragraph)
            lines = text.split("\n")

            # Engineering labels are often short
            is_label = len(lines) <= 3 and all(len(line.strip()) < 50 for line in lines)

            if is_label:
                text_areas.append({
                    "x1": x1,
                    "y1": y1,
                    "x2": x2,
                    "y2": y2,
                    "width": width,
                    "height": height,
                    "text": text[:100],  # Truncate long text
                    "type": "label"
                })

    return text_areas

def detect_grid_cells(page, min_size=5):
    """
    Detect individual cells within tables or grids.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with cell coordinates
    """
    cells = []

    # Get the page's drawing operations
    paths = page.get_drawings()

    # Extract horizontal and vertical lines
    h_lines = []
    v_lines = []

    for path in paths:
        # Check if this is a line
        if len(path["items"]) == 2 and path["items"][0][0] == "m" and path["items"][1][0] == "l":
            # Extract start and end points
            start_x, start_y = path["items"][0][1:3]
            end_x, end_y = path["items"][1][1:3]

            # Determine if horizontal or vertical
            if abs(end_y - start_y) < 2:  # Horizontal line
                h_lines.append((min(start_x, end_x), start_y, max(start_x, end_x), end_y))
            elif abs(end_x - start_x) < 2:  # Vertical line
                v_lines.append((start_x, min(start_y, end_y), end_x, max(start_y, end_y)))

    # Sort lines by position
    h_lines.sort(key=lambda x: x[1])  # Sort by y-coordinate
    v_lines.sort(key=lambda x: x[0])  # Sort by x-coordinate

    # Find cell boundaries by identifying intersections
    # For each pair of adjacent horizontal lines
    for i in range(len(h_lines) - 1):
        h1 = h_lines[i]
        h2 = h_lines[i + 1]

        h1_x1, h1_y1, h1_x2, h1_y2 = h1
        h2_x1, h2_y1, h2_x2, h2_y2 = h2

        # Skip if the lines are too far apart
        if h2_y1 - h1_y1 > 100:  # Arbitrary threshold
            continue

        # For each pair of adjacent vertical lines
        for j in range(len(v_lines) - 1):
            v1 = v_lines[j]
            v2 = v_lines[j + 1]

            v1_x1, v1_y1, v1_x2, v1_y2 = v1
            v2_x1, v2_y1, v2_x2, v2_y2 = v2

            # Skip if the lines are too far apart
            if v2_x1 - v1_x1 > 200:  # Arbitrary threshold
                continue

            # Check if these lines form a cell (all lines must intersect)
            if (v1_y1 <= h1_y1 <= v1_y2 and v1_y1 <= h2_y1 <= v1_y2 and
                v2_y1 <= h1_y1 <= v2_y2 and v2_y1 <= h2_y1 <= v2_y2 and
                h1_x1 <= v1_x1 <= h1_x2 and h1_x1 <= v2_x1 <= h1_x2 and
                h2_x1 <= v1_x1 <= h2_x2 and h2_x1 <= v2_x1 <= h2_x2):

                # Cell boundaries
                cell_x1 = v1_x1
                cell_y1 = h1_y1
                cell_x2 = v2_x1
                cell_y2 = h2_y1

                width = cell_x2 - cell_x1
                height = cell_y2 - cell_y1

                # Skip cells that are too small
                if width < min_size or height < min_size:
                    continue

                # Check if this cell contains text
                has_text = False
                text_content = ""

                # Get text blocks
                dict_blocks = page.get_text("dict")["blocks"]

                for block in dict_blocks:
                    if block.get("type") == 0:  # Text block
                        block_x1, block_y1, block_x2, block_y2 = block["bbox"]

                        # Check if the text block is inside this cell
                        if (cell_x1 <= block_x1 and block_x2 <= cell_x2 and
                            cell_y1 <= block_y1 and block_y2 <= cell_y2):
                            has_text = True
                            text_content = block.get("text", "").strip()
                            break

                cells.append({
                    "x1": cell_x1,
                    "y1": cell_y1,
                    "x2": cell_x2,
                    "y2": cell_y2,
                    "width": width,
                    "height": height,
                    "has_text": has_text,
                    "text": text_content[:100] if has_text else "",  # Truncate long text
                    "type": "grid_cell"
                })

    # Alternative approach: detect cells by finding small rectangles
    # This can catch cells that are drawn as rectangles rather than with individual lines
    for path in paths:
        # Check if this is a rectangle
        if "rect" in path:
            rect = path["rect"]
            x1, y1, x2, y2 = rect

            width = x2 - x1
            height = y2 - y1

            # Check if this could be a cell (not too large, not too small)
            if min_size <= width <= 200 and min_size <= height <= 100:
                # Check if this rectangle is likely a cell
                # (e.g., it's within a larger grid structure)
                is_cell = False

                # Check if there are nearby cells or grid lines
                for cell in cells:
                    cell_x1, cell_y1 = cell["x1"], cell["y1"]
                    cell_x2, cell_y2 = cell["x2"], cell["y2"]

                    # If this rectangle is adjacent to an existing cell
                    if ((abs(x1 - cell_x2) < 5 or abs(x2 - cell_x1) < 5) and
                        (y1 <= cell_y2 and y2 >= cell_y1)):
                        is_cell = True
                        break

                    if ((abs(y1 - cell_y2) < 5 or abs(y2 - cell_y1) < 5) and
                        (x1 <= cell_x2 and x2 >= cell_x1)):
                        is_cell = True
                        break

                # If no nearby cells, check if it's within a table
                if not is_cell:
                    for h_line in h_lines:
                        h_x1, h_y1, h_x2, h_y2 = h_line
                        if abs(y1 - h_y1) < 5 or abs(y2 - h_y1) < 5:
                            is_cell = True
                            break

                    if not is_cell:
                        for v_line in v_lines:
                            v_x1, v_y1, v_x2, v_y2 = v_line
                            if abs(x1 - v_x1) < 5 or abs(x2 - v_x1) < 5:
                                is_cell = True
                                break

                if is_cell:
                    # Check if this cell contains text
                    has_text = False
                    text_content = ""

                    # Get text blocks
                    dict_blocks = page.get_text("dict")["blocks"]

                    for block in dict_blocks:
                        if block.get("type") == 0:  # Text block
                            block_x1, block_y1, block_x2, block_y2 = block["bbox"]

                            # Check if the text block is inside this cell
                            if (x1 <= block_x1 and block_x2 <= x2 and
                                y1 <= block_y1 and block_y2 <= y2):
                                has_text = True
                                text_content = block.get("text", "").strip()
                                break

                    # Add to cells list if not too similar to existing cells
                    duplicate = False
                    for cell in cells:
                        if (abs(x1 - cell["x1"]) < 5 and abs(y1 - cell["y1"]) < 5 and
                            abs(x2 - cell["x2"]) < 5 and abs(y2 - cell["y2"]) < 5):
                            duplicate = True
                            break

                    if not duplicate:
                        cells.append({
                            "x1": x1,
                            "y1": y1,
                            "x2": x2,
                            "y2": y2,
                            "width": width,
                            "height": height,
                            "has_text": has_text,
                            "text": text_content[:100] if has_text else "",  # Truncate long text
                            "type": "grid_cell"
                        })

    return cells

def detect_text_based_rois(page, min_size=5):
    """
    Detect ROIs based on text content using Windows SDK OCR.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with ROI coordinates
    """
    import os
    import tempfile
    import asyncio
    import sys
    import time
    import uuid

    # Check if running on Windows
    if sys.platform != 'win32':
        logger.warning("Windows SDK OCR is only available on Windows platforms")
        return []

    try:
        # Import Windows SDK components
        import winsdk.windows.media.ocr as ocr
        import winsdk.windows.globalization as globalization
        import winsdk.windows.graphics.imaging as imaging
        import winsdk.windows.storage as storage
        from winsdk.windows.foundation import AsyncStatus
    except ImportError:
        logger.warning("Windows SDK Python modules not found. Install with: pip install winsdk")
        return []

    text_rois = []
    temp_filename = None

    try:
        # Render the page to a temporary image file
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # Higher resolution for better OCR

        # Create a unique filename in the temp directory
        # Using a UUID to avoid collisions
        temp_dir = tempfile.gettempdir()
        unique_id = str(uuid.uuid4())
        temp_filename = os.path.join(temp_dir, f"ocr_temp_{unique_id}.png")

        # Save the image directly to the file
        pix.save(temp_filename)

        # Let's make sure the file exists before proceeding
        if not os.path.exists(temp_filename):
            logger.error(f"Failed to create temporary file: {temp_filename}")
            return []

        async def process_image():
            try:
                # Open the image file
                file = await storage.StorageFile.get_file_from_path_async(temp_filename)

                # Create a decoder
                stream = await file.open_async(storage.FileAccessMode.READ)
                decoder = await imaging.BitmapDecoder.create_async(stream)
                bitmap = await decoder.get_software_bitmap_async()

                # Get available OCR engine
                available_languages = ocr.OcrEngine.available_recognizer_languages
                language = available_languages[0]  # Use the first available language

                # Create OCR engine
                ocr_engine = ocr.OcrEngine.try_create_from_language(language)

                if ocr_engine:
                    # Recognize text
                    result = await ocr_engine.recognize_async(bitmap)

                    # Process the results
                    for line in result.lines:
                        # Get the bounding rectangle
                        rect = line.words[0].bounding_rect
                        x1 = rect.x / 2  # Adjust for the 2x scaling we used
                        y1 = rect.y / 2

                        # Find the rightmost word in the line
                        rightmost_word = line.words[0]
                        for word in line.words:
                            if word.bounding_rect.x + word.bounding_rect.width > rightmost_word.bounding_rect.x + rightmost_word.bounding_rect.width:
                                rightmost_word = word

                        # Get the right edge of the line
                        rect_right = rightmost_word.bounding_rect
                        x2 = (rect_right.x + rect_right.width) / 2

                        # Get the bottom edge (use the maximum height of any word)
                        max_height = 0
                        for word in line.words:
                            if word.bounding_rect.height > max_height:
                                max_height = word.bounding_rect.height

                        y2 = (rect.y + max_height) / 2

                        width = x2 - x1
                        height = y2 - y1

                        # Skip if too small
                        if width < min_size or height < min_size:
                            continue

                        # Get the text content
                        text_content = line.text

                        text_rois.append({
                            "x1": x1,
                            "y1": y1,
                            "x2": x2,
                            "y2": y2,
                            "width": width,
                            "height": height,
                            "text": text_content[:100],  # Truncate long text
                            "type": "text_roi"
                        })

                    # Now look for potential cells based on text positioning
                    # Group text lines that are aligned horizontally or vertically

                    # Sort by y-coordinate (top to bottom)
                    sorted_by_y = sorted(text_rois, key=lambda roi: roi["y1"])

                    # Group lines that are at similar y-positions (potential rows)
                    y_tolerance = 10  # Points
                    rows = []
                    current_row = [sorted_by_y[0]] if sorted_by_y else []

                    for roi in sorted_by_y[1:]:
                        # If this ROI is at a similar y-position as the previous one
                        if abs(roi["y1"] - current_row[0]["y1"]) < y_tolerance:
                            current_row.append(roi)
                        else:
                            # Start a new row
                            if current_row:
                                rows.append(current_row)
                            current_row = [roi]

                    # Add the last row
                    if current_row:
                        rows.append(current_row)

                    # For each row, sort by x-coordinate (left to right)
                    for i, row in enumerate(rows):
                        rows[i] = sorted(row, key=lambda roi: roi["x1"])

                    # Now look for grid-like structures
                    # If we have multiple rows with a similar number of elements,
                    # they might form a table
                    if len(rows) >= 2:
                        # Check if rows have a similar number of elements
                        row_lengths = [len(row) for row in rows]
                        avg_length = sum(row_lengths) / len(row_lengths)

                        # If most rows have a similar length, this might be a table
                        similar_length_rows = [row for row in rows if abs(len(row) - avg_length) <= 1]

                        if len(similar_length_rows) >= 2:
                            # This might be a table
                            # Create cell ROIs based on the text positions
                            for i, row in enumerate(similar_length_rows):
                                for j, cell in enumerate(row):
                                    # Mark this as a potential table cell
                                    cell["type"] = "table_cell"

                                    # If this is not the last row, and there's a cell below this one
                                    if i < len(similar_length_rows) - 1 and j < len(similar_length_rows[i+1]):
                                        # Create a cell that spans from this text to the one below
                                        below_cell = similar_length_rows[i+1][j]

                                        # Calculate the cell boundaries
                                        cell_x1 = cell["x1"]
                                        cell_y1 = cell["y1"]
                                        cell_x2 = cell["x2"]
                                        cell_y2 = below_cell["y2"]

                                        # If this is not the last column, extend to the next cell
                                        if j < len(row) - 1:
                                            next_cell = row[j+1]
                                            cell_x2 = next_cell["x1"]

                                        width = cell_x2 - cell_x1
                                        height = cell_y2 - cell_y1

                                        # Skip if too small
                                        if width < min_size or height < min_size:
                                            continue

                                        # Add as a grid cell
                                        text_rois.append({
                                            "x1": cell_x1,
                                            "y1": cell_y1,
                                            "x2": cell_x2,
                                            "y2": cell_y2,
                                            "width": width,
                                            "height": height,
                                            "text": cell["text"],
                                            "type": "inferred_cell"
                                        })

            except Exception as e:
                logger.error(f"Error in Windows OCR: {str(e)}")

        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(process_image())

    except Exception as e:
        logger.error(f"Error running Windows OCR: {str(e)}")

    finally:
        # Try to clean up the temporary file, but don't worry if it fails
        # Windows will clean up temporary files eventually
        if temp_filename and os.path.exists(temp_filename):
            try:
                os.unlink(temp_filename)
                logger.debug(f"Successfully removed temporary file: {temp_filename}")
            except Exception as e:
                # Just log the error and continue - this is not critical
                logger.debug(f"Could not remove temporary file {temp_filename}: {str(e)}")
                # This is expected on Windows with certain file operations
                # The OS will clean up the temp directory eventually

    return text_rois

def detect_cv2_text_regions(page, min_size=5):
    """
    Detect ROIs based on text regions using OpenCV.

    Args:
        page: PyMuPDF page object
        min_size: Minimum size (width or height) in points to consider

    Returns:
        List of dictionaries with ROI coordinates
    """
    import os
    import tempfile
    import sys
    import numpy as np
    import cv2

    # Import the detect_text_regions function from the project
    from src.atom.vision.detect_text_regions.detect_text_regions_cv2 import detect_text_regions

    text_rois = []
    temp_filename = None

    try:
        # Render the page to a temporary image file
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # Higher resolution for better detection

        # Create a unique filename in the temp directory
        # Using a timestamp to avoid collisions
        temp_dir = tempfile.gettempdir()
        temp_filename = os.path.join(temp_dir, f"roi_temp_{int(time.time())}.png")

        # Save the image directly to the file
        pix.save(temp_filename)

        # Let's make sure the file exists before proceeding
        if not os.path.exists(temp_filename):
            logger.error(f"Failed to create temporary file: {temp_filename}")
            return []

        # Load the image with OpenCV
        img = cv2.imread(temp_filename)
        if img is None:
            logger.error(f"Failed to read image with OpenCV: {temp_filename}")
            return []

        # Detect text regions using the existing function
        # Parameters tuned for engineering drawings
        regions = detect_text_regions(
            img,
            min_area=min_size * 2,  # Slightly larger minimum area
            remove_grid=True,       # Remove grid lines
            debug=False             # No debug output
        )

        # Process the detected regions
        for i, (x, y, w, h) in enumerate(regions):
            # Convert from the 2x scale back to original coordinates
            x1 = x / 2
            y1 = y / 2
            x2 = (x + w) / 2
            y2 = (y + h) / 2
            width = w / 2
            height = h / 2

            # Skip if too small
            if width < min_size or height < min_size:
                continue

            # Extract text content from this region using PyMuPDF
            text_content = page.get_text("text", clip=(x1, y1, x2, y2)).strip()

            # Add as a text ROI
            text_rois.append({
                "x1": x1,
                "y1": y1,
                "x2": x2,
                "y2": y2,
                "width": width,
                "height": height,
                "text": text_content[:100] if text_content else "",  # Truncate long text
                "has_text": bool(text_content),
                "type": "cv2_text_roi"
            })

        # Now identify potential table structures based on text region alignment
        if len(text_rois) >= 4:  # Need at least a few regions to form a table
            # Sort regions by y-coordinate (top to bottom)
            sorted_by_y = sorted(text_rois, key=lambda roi: roi["y1"])

            # Group regions that are at similar y-positions (potential rows)
            y_tolerance = 10  # Points
            rows = []
            current_row = [sorted_by_y[0]]

            for roi in sorted_by_y[1:]:
                # If this ROI is at a similar y-position as the previous one
                if abs(roi["y1"] - current_row[0]["y1"]) < y_tolerance:
                    current_row.append(roi)
                else:
                    # Start a new row
                    if current_row:
                        rows.append(current_row)
                    current_row = [roi]

            # Add the last row
            if current_row:
                rows.append(current_row)

            # For each row, sort by x-coordinate (left to right)
            for i, row in enumerate(rows):
                rows[i] = sorted(row, key=lambda roi: roi["x1"])

            # Check if we have a grid-like structure (multiple rows with similar number of columns)
            if len(rows) >= 2:
                # Check if rows have a similar number of elements
                row_lengths = [len(row) for row in rows]
                avg_length = sum(row_lengths) / len(row_lengths)

                # If most rows have a similar length, this might be a table
                similar_length_rows = [row for row in rows if abs(len(row) - avg_length) <= 1]

                if len(similar_length_rows) >= 2 and avg_length >= 2:
                    # This looks like a table - create a table ROI
                    # Find the bounding box of all text regions in the table
                    min_x = min(roi["x1"] for row in similar_length_rows for roi in row)
                    min_y = min(roi["y1"] for row in similar_length_rows for roi in row)
                    max_x = max(roi["x2"] for row in similar_length_rows for roi in row)
                    max_y = max(roi["y2"] for row in similar_length_rows for roi in row)

                    width = max_x - min_x
                    height = max_y - min_y

                    # Add as a table ROI
                    text_rois.append({
                        "x1": min_x,
                        "y1": min_y,
                        "x2": max_x,
                        "y2": max_y,
                        "width": width,
                        "height": height,
                        "type": "cv2_table",
                        "rows": len(similar_length_rows),
                        "columns": int(avg_length)
                    })

                    # Now create cell ROIs for each cell in the table
                    for i, row in enumerate(similar_length_rows):
                        for j, cell in enumerate(row):
                            # Mark this as a table cell
                            cell["type"] = "cv2_table_cell"

                            # If this is not the last row, and there's a cell below this one
                            if i < len(similar_length_rows) - 1 and j < len(similar_length_rows[i+1]):
                                # Create a cell that spans from this text to the one below
                                below_cell = similar_length_rows[i+1][j]

                                # Calculate the cell boundaries
                                cell_x1 = cell["x1"]
                                cell_y1 = cell["y1"]
                                cell_x2 = cell["x2"]
                                cell_y2 = below_cell["y2"]

                                # If this is not the last column, extend to the next cell
                                if j < len(row) - 1:
                                    next_cell = row[j+1]
                                    cell_x2 = next_cell["x1"]

                                width = cell_x2 - cell_x1
                                height = cell_y2 - cell_y1

                                # Skip if too small
                                if width < min_size or height < min_size:
                                    continue

                                # Add as a grid cell
                                text_rois.append({
                                    "x1": cell_x1,
                                    "y1": cell_y1,
                                    "x2": cell_x2,
                                    "y2": cell_y2,
                                    "width": width,
                                    "height": height,
                                    "text": cell.get("text", ""),
                                    "type": "cv2_inferred_cell"
                                })

    except Exception as e:
        logger.error(f"Error in CV2 text region detection: {str(e)}")

    finally:
        # Try to clean up the temporary file, but don't worry if it fails
        if temp_filename and os.path.exists(temp_filename):
            try:
                os.unlink(temp_filename)
            except Exception as e:
                # Just log the error and continue - this is not critical
                logger.debug(f"Could not remove temporary file {temp_filename}: {str(e)}")

    return text_rois

def create_visualizations(pdf_path, df, output_dir=None):
    """
    Create visualizations of detected ROIs.

    Args:
        pdf_path: Path to the PDF file
        df: DataFrame with ROI information
        output_dir: Directory for output files (default: same as PDF)
    """
    if df.empty:
        logger.warning("No ROIs to visualize")
        return

    # Determine output directory
    if output_dir is None:
        output_dir = os.path.dirname(pdf_path)

    os.makedirs(output_dir, exist_ok=True)

    # Get base filename
    base_name = os.path.splitext(os.path.basename(pdf_path))[0]

    # Open the PDF
    doc = fitz.open(pdf_path)
    total_pages = len(doc)

    # Get unique page numbers
    page_numbers = df['pdf_page'].unique()

    # Create a visualization for each page
    for page_num in page_numbers:
        # Convert from 1-based to 0-based page index
        page_idx = int(page_num) - 1

        # Validate page index
        if page_idx < 0 or page_idx >= total_pages:
            logger.warning(f"Page {page_num} (index {page_idx}) is out of range for document with {total_pages} pages. Skipping.")
            continue

        try:
            # Get the page
            page = doc[page_idx]

            # Render the page to an image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)

            # Create figure
            fig, ax = plt.subplots(figsize=(12, 16))

            # Display the page
            ax.imshow(img)

            # Filter ROIs for this page
            page_rois = df[df['pdf_page'] == page_num]

            # Add rectangles for each ROI
            colors = {
                'rectangle': 'red',
                'text_block': 'blue',
                'table': 'green',
                'bom_table': 'orange',
                'grid_table': 'purple',
                'title_block': 'pink',
                'drawing_border': 'brown',
                'label': 'gray',
                'grid_cell': 'cyan',
                'text_roi': 'magenta',
                'table_cell': 'yellow',
                'inferred_cell': 'lime',
                'cv2_text_roi': 'darkgreen',
                'cv2_table': 'darkblue',
                'cv2_table_cell': 'darkred',
                'cv2_inferred_cell': 'darkorange'
            }

            # Create a separate figure for text regions
            text_fig, text_ax = plt.subplots(figsize=(12, 16))
            text_ax.imshow(img)
            text_ax.set_title(f"Page {page_num} - Text Regions")
            text_ax.axis('off')

            # Track text regions for the separate visualization
            text_region_count = 0

            for i, roi in page_rois.iterrows():
                # Get coordinates
                x1, y1, x2, y2 = roi['x1'], roi['y1'], roi['x2'], roi['y2']
                width = x2 - x1
                height = y2 - y1

                # Get color based on type
                color = colors.get(roi['type'], 'orange')

                # Add rectangle to main visualization
                rect = Rectangle((x1 * 2, y1 * 2), width * 2, height * 2,
                                linewidth=1, edgecolor=color, facecolor='none')
                ax.add_patch(rect)

                # Add small label with ROI type
                ax.text(x1 * 2, y1 * 2 - 5, roi['type'],
                        fontsize=6, color=color,
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

                # For text regions, add to the text visualization
                if 'text' in roi and roi['text'] and any(text_type in roi['type'] for text_type in [
                    'text_roi', 'cv2_text_roi', 'text_block', 'label',
                    'cv2_table_cell', 'cv2_inferred_cell', 'table_cell', 'grid_cell'
                ]):
                    text_region_count += 1

                    # Add rectangle to text visualization
                    text_rect = Rectangle((x1 * 2, y1 * 2), width * 2, height * 2,
                                    linewidth=1, edgecolor=color, facecolor='none')
                    text_ax.add_patch(text_rect)

                    # Add text content
                    display_text = roi['text']
                    if len(display_text) > 20:
                        display_text = display_text[:17] + "..."

                    # Add text with region number for reference
                    text_ax.text(x1 * 2, y1 * 2 - 10, f"{text_region_count}: {display_text}",
                            fontsize=8, color='black',
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

                    # Add region number to main visualization for cross-reference
                    if any(cv2_type in roi['type'] for cv2_type in ['cv2_text_roi', 'cv2_table_cell', 'cv2_inferred_cell']):
                        ax.text(x1 * 2 + width * 2 / 2, y1 * 2 + height * 2 / 2, str(text_region_count),
                                fontsize=8, color='white', weight='bold',
                                ha='center', va='center',
                                bbox=dict(facecolor='black', alpha=0.7, edgecolor='none', pad=1))

            # Set title
            ax.set_title(f"Page {page_num} - {len(page_rois)} ROIs")

            # Remove axes
            ax.axis('off')

            # Save main figure
            output_path = os.path.join(output_dir, f"{base_name}_page{page_num}_rois.png")
            plt.figure(fig.number)
            plt.savefig(output_path, bbox_inches='tight')

            # Save text regions figure if there are any text regions
            if text_region_count > 0:
                text_output_path = os.path.join(output_dir, f"{base_name}_page{page_num}_text_regions.png")
                plt.figure(text_fig.number)
                plt.savefig(text_output_path, bbox_inches='tight')
                logger.info(f"Text regions visualization saved to {text_output_path}")

            # Close both figures
            plt.close(fig)
            plt.close(text_fig)

            logger.info(f"Visualization saved to {output_path}")

        except Exception as e:
            logger.error(f"Error creating visualization for page {page_num}: {str(e)}")

    # Close the document
    doc.close()

    # Create a text report file with all detected text
    try:
        text_report_path = os.path.join(output_dir, f"{base_name}_text_report.txt")
        with open(text_report_path, 'w', encoding='utf-8') as f:
            f.write(f"Text Report for {pdf_path}\n")
            f.write("=" * 80 + "\n\n")

            for page_num in sorted(df['pdf_page'].unique()):
                f.write(f"Page {page_num}:\n")
                f.write("-" * 40 + "\n")

                # Get text regions for this page
                page_text_rois = df[(df['pdf_page'] == page_num) &
                                   (df['text'].notna()) &
                                   (df['text'] != "")]

                if len(page_text_rois) == 0:
                    f.write("No text regions detected.\n\n")
                    continue

                # Sort by y-coordinate (top to bottom)
                page_text_rois = page_text_rois.sort_values(by=['y1', 'x1'])

                # Write each text region
                for i, roi in page_text_rois.iterrows():
                    f.write(f"Region Type: {roi['type']}\n")
                    f.write(f"Position: ({roi['x1']:.1f}, {roi['y1']:.1f}) to ({roi['x2']:.1f}, {roi['y2']:.1f})\n")
                    f.write(f"Text: {roi['text']}\n")
                    f.write("-" * 40 + "\n")

                f.write("\n")

        logger.info(f"Text report saved to {text_report_path}")
    except Exception as e:
        logger.error(f"Error creating text report: {str(e)}")

def process_page(args):
    """Process a single page to detect ROIs"""
    pdf_path, page_num, options = args

    try:
        # Open the document and get the page
        doc = fitz.open(pdf_path)
        page = doc[page_num]

        # Detect different types of ROIs
        all_rois = []

        # Standard rectangle detection
        if options.get('detect_rectangles', True):
            rectangles = detect_rectangles(
                page,
                min_size=options.get('min_size', 10),
                max_size=options.get('max_size', None),
                color_filter=options.get('color_filter', None)
            )

            for rect in rectangles:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": rect["x1"],
                    "y1": rect["y1"],
                    "x2": rect["x2"],
                    "y2": rect["y2"],
                    "width": rect["width"],
                    "height": rect["height"],
                    "type": "rectangle",
                    "is_closed": rect["is_closed"],
                    "stroke_color": rect.get("stroke_color"),
                    "fill_color": rect.get("fill_color"),
                    "stroke_width": rect.get("stroke_width"),
                    "is_filled": rect.get("is_filled")
                })

        # Text block detection
        if options.get('detect_text_blocks', True):
            text_blocks = detect_text_blocks(page, min_size=options.get('min_size', 10))

            for block in text_blocks:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": block["x1"],
                    "y1": block["y1"],
                    "x2": block["x2"],
                    "y2": block["y2"],
                    "width": block["width"],
                    "height": block["height"],
                    "type": "text_block",
                    "text": block.get("text", "")[:100]  # Truncate long text
                })

        # Table detection
        if options.get('detect_tables', True):
            tables = detect_tables(
                page,
                min_rows=options.get('min_table_rows', 2),
                min_cols=options.get('min_table_cols', 2)
            )

            for table in tables:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": table["x1"],
                    "y1": table["y1"],
                    "x2": table["x2"],
                    "y2": table["y2"],
                    "width": table["width"],
                    "height": table["height"],
                    "type": "table",
                    "h_lines": table["h_lines"],
                    "v_lines": table["v_lines"]
                })

        # Grid cell detection
        if options.get('detect_grid_cells', False):
            cells = detect_grid_cells(page, min_size=options.get('min_cell_size', 5))

            for cell in cells:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": cell["x1"],
                    "y1": cell["y1"],
                    "x2": cell["x2"],
                    "y2": cell["y2"],
                    "width": cell["width"],
                    "height": cell["height"],
                    "type": "grid_cell",
                    "has_text": cell["has_text"],
                    "text": cell["text"]
                })

        # CV2-based text region detection
        if options.get('use_cv2_detection', False):
            cv2_regions = detect_cv2_text_regions(page, min_size=options.get('min_size', 5))

            for roi in cv2_regions:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": roi["x1"],
                    "y1": roi["y1"],
                    "x2": roi["x2"],
                    "y2": roi["y2"],
                    "width": roi["width"],
                    "height": roi["height"],
                    "type": roi["type"],
                    "text": roi.get("text", "")[:100]  # Truncate long text
                })

        # Text-based ROI detection with Windows SDK
        if options.get('use_winsdk_ocr', False):
            text_rois = detect_text_based_rois(page, min_size=options.get('min_size', 5))

            for roi in text_rois:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": roi["x1"],
                    "y1": roi["y1"],
                    "x2": roi["x2"],
                    "y2": roi["y2"],
                    "width": roi["width"],
                    "height": roi["height"],
                    "type": roi["type"],
                    "text": roi.get("text", "")[:100]  # Truncate long text
                })

        # Engineering-specific detections
        if options.get('engineering_mode', False):
            # Detect engineering tables (like BOM)
            eng_tables = detect_engineering_tables(page, min_size=options.get('min_size', 50))

            for table in eng_tables:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": table["x1"],
                    "y1": table["y1"],
                    "x2": table["x2"],
                    "y2": table["y2"],
                    "width": table["width"],
                    "height": table["height"],
                    "type": table["type"]  # bom_table or grid_table
                })

            # Detect title blocks
            title_blocks = detect_title_blocks(page, min_size=options.get('min_size', 50))

            for block in title_blocks:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": block["x1"],
                    "y1": block["y1"],
                    "x2": block["x2"],
                    "y2": block["y2"],
                    "width": block["width"],
                    "height": block["height"],
                    "type": "title_block"
                })

            # Detect drawing borders
            borders = detect_drawing_borders(page, min_size=options.get('min_size', 100))

            for border in borders:
                all_rois.append({
                    "pdf_page": page_num + 1,  # 1-based page numbering
                    "x1": border["x1"],
                    "y1": border["y1"],
                    "x2": border["x2"],
                    "y2": border["y2"],
                    "width": border["width"],
                    "height": border["height"],
                    "type": "drawing_border"
                })

            # Detect isolated text/labels
            if options.get('detect_labels', True):
                labels = detect_isolated_text(page, min_size=options.get('min_size', 10))

                for label in labels:
                    all_rois.append({
                        "pdf_page": page_num + 1,  # 1-based page numbering
                        "x1": label["x1"],
                        "y1": label["y1"],
                        "x2": label["x2"],
                        "y2": label["y2"],
                        "width": label["width"],
                        "height": label["height"],
                        "type": "label",
                        "text": label.get("text", "")[:100]  # Truncate long text
                    })

        # Close the document
        doc.close()

        return all_rois

    except Exception as e:
        logger.error(f"Error processing page {page_num}: {e}")
        return []

def detect_rois(pdf_path, page_numbers=None, **options):
    """
    Detect regions of interest (ROIs) in a PDF document.

    Args:
        pdf_path: Path to the PDF file
        page_numbers: List of page numbers to process (0-based). If None, process all pages.
        **options: Additional options for ROI detection:
            - min_size: Minimum size (width or height) in points to consider (default: 10)
            - max_size: Maximum size (width or height) in points to consider (default: None)
            - color_filter: Optional color filter as (r, g, b) tuple or list of tuples
            - detect_text_blocks: Whether to detect text blocks (default: True)
            - detect_tables: Whether to detect tables (default: True)
            - use_multiprocessing: Whether to use multiprocessing (default: True)
            - num_processes: Number of processes to use (default: None, uses CPU count)
            - visualize: Whether to create visualizations (default: False)
            - output_dir: Directory for output files (default: same as PDF)
    Returns:
        DataFrame with ROI information
    """
    start_time = time.time()

    # Validate input
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    # Open the document to get page count
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    doc.close()

    # Determine which pages to process
    if page_numbers is None:
        page_numbers = list(range(total_pages))
    else:
        # Ensure page numbers are valid
        page_numbers = [p for p in page_numbers if 0 <= p < total_pages]

    # Prepare arguments for processing
    process_args = [(pdf_path, page_num, options) for page_num in page_numbers]

    # Process pages
    all_rois = []

    if options.get('use_multiprocessing', True) and len(page_numbers) > 1:
        # Use multiprocessing for multiple pages
        num_processes = options.get('num_processes', None)  # None uses CPU count

        logger.info(f"Using multiprocessing with {num_processes or 'CPU count'} processes")

        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            # Process pages in parallel with progress bar
            results = list(tqdm(
                executor.map(process_page, process_args),
                total=len(process_args),
                desc="Detecting ROIs"
            ))

            # Combine results
            for page_rois in results:
                all_rois.extend(page_rois)
    else:
        # Process pages sequentially with progress bar
        for args in tqdm(process_args, desc="Detecting ROIs"):
            page_rois = process_page(args)
            all_rois.extend(page_rois)

    # Convert to DataFrame
    if all_rois:
        df = pd.DataFrame(all_rois)

        # Sort by page number and position
        df = df.sort_values(by=['pdf_page', 'y1', 'x1'])

        # Reset index
        df = df.reset_index(drop=True)
    else:
        # Create empty DataFrame with expected columns
        df = pd.DataFrame(columns=[
            'pdf_page', 'x1', 'y1', 'x2', 'y2', 'width', 'height', 'type'
        ])

    # Create visualizations if requested
    if options.get('visualize', False):
        create_visualizations(pdf_path, df, options.get('output_dir', None))

    # Save to Excel if requested
    if options.get('save_excel', True):
        excel_path = save_to_excel(pdf_path, df, options.get('output_dir', None))
        logger.info(f"Results saved to {excel_path}")

    elapsed_time = time.time() - start_time
    logger.info(f"ROI detection completed in {elapsed_time:.2f} seconds")

    return df

def save_to_excel(pdf_path, df, output_dir=None):
    """
    Save ROI information to Excel.

    Args:
        pdf_path: Path to the PDF file
        df: DataFrame with ROI information
        output_dir: Directory for output files (default: same as PDF)

    Returns:
        Path to the saved Excel file
    """
    # Determine output directory
    if output_dir is None:
        output_dir = os.path.dirname(pdf_path)

    os.makedirs(output_dir, exist_ok=True)

    # Get base filename
    base_name = os.path.splitext(os.path.basename(pdf_path))[0]

    # Create Excel file path
    excel_path = os.path.join(output_dir, f"{base_name}_rois.xlsx")

    # Create a simplified DataFrame for export
    export_df = df[['pdf_page', 'x1', 'y1', 'x2', 'y2', 'width', 'height', 'type']].copy()

    # Save to Excel
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        # Write main sheet with all ROIs
        export_df.to_excel(writer, sheet_name='All ROIs', index=False)

        # Write sheets for each ROI type
        for roi_type in export_df['type'].unique():
            type_df = export_df[export_df['type'] == roi_type]
            type_df.to_excel(writer, sheet_name=f'{roi_type.capitalize()}s', index=False)

        # Write summary sheet
        summary_data = {
            'Page': export_df['pdf_page'].unique(),
            'Total ROIs': [len(export_df[export_df['pdf_page'] == p]) for p in export_df['pdf_page'].unique()]
        }

        # Add counts by type
        for roi_type in export_df['type'].unique():
            summary_data[f'{roi_type.capitalize()}s'] = [
                len(export_df[(export_df['pdf_page'] == p) & (export_df['type'] == roi_type)])
                for p in export_df['pdf_page'].unique()
            ]

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)

    return excel_path

def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip()) - 1  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(0, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part) - 1  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return sorted(set(pages))  # Remove duplicates and sort

def detect_page_layouts(pdf_path, page_range=None, min_size=10, max_size=None,
                      color_filter=None, detect_text_blocks=True, detect_tables=True,
                      use_multiprocessing=True, num_processes=None, visualize=False,
                      save_excel=True, output_dir=None, engineering_mode=True,
                      detect_labels=True, detect_grid_cells=False, min_cell_size=5,
                      use_winsdk_ocr=False, use_cv2_detection=False):
    """
    Detect page layouts (ROIs) in a PDF document and save results to Excel.

    Args:
        pdf_path: Path to the PDF file
        page_range: String with page ranges (e.g., "1-5,7,9-12") or None for all pages
        min_size: Minimum size (width or height) in points to consider (default: 10)
        max_size: Maximum size (width or height) in points to consider (default: None)
        color_filter: Optional color filter as (r, g, b) tuple or list of tuples
        detect_text_blocks: Whether to detect text blocks (default: True)
        detect_tables: Whether to detect tables (default: True)
        use_multiprocessing: Whether to use multiprocessing (default: True)
        num_processes: Number of processes to use (default: None, uses CPU count)
        visualize: Whether to create visualizations (default: False)
        save_excel: Whether to save results to Excel (default: True)
        output_dir: Directory for output files (default: same as PDF)
        engineering_mode: Whether to use specialized engineering drawing detection (default: True)
        detect_labels: Whether to detect isolated text labels (default: True)
        detect_grid_cells: Whether to detect individual cells within grids (default: False)
        min_cell_size: Minimum size for grid cells in points (default: 5)
        use_winsdk_ocr: Whether to use Windows SDK OCR for text-based ROI detection (default: False)
        use_cv2_detection: Whether to use OpenCV-based text region detection (default: False)

    Returns:
        DataFrame with ROI information
    """
    # Validate input
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    # Open the document to get page count
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    doc.close()

    # Parse page range
    if page_range:
        page_numbers = parse_page_range(page_range, total_pages)
    else:
        page_numbers = list(range(total_pages))

    # Set up options
    options = {
        'min_size': min_size,
        'max_size': max_size,
        'color_filter': color_filter,
        'detect_text_blocks': detect_text_blocks,
        'detect_tables': detect_tables,
        'use_multiprocessing': use_multiprocessing,
        'num_processes': num_processes,
        'visualize': visualize,
        'save_excel': save_excel,
        'output_dir': output_dir,
        'engineering_mode': engineering_mode,
        'detect_labels': detect_labels,
        'detect_grid_cells': detect_grid_cells,
        'min_cell_size': min_cell_size,
        'use_winsdk_ocr': use_winsdk_ocr,
        'use_cv2_detection': use_cv2_detection
    }

    # Detect ROIs
    return detect_rois(pdf_path, page_numbers, **options)

# Example usage
if __name__ == "__main__":
    # Example: Detect ROIs in an engineering drawing
    pdf_path = r"C:\Drawings\Unclean Docs\Combined ISO Yates - Rotation Needed_rotated_pypdf.pdf"

    # Detect all ROIs with engineering mode enabled
    rois_df = detect_page_layouts(
        pdf_path,
        page_range="1-5",
        engineering_mode=True,     # Enable specialized engineering drawing detection
        detect_grid_cells=True,
        use_cv2_detection=True,    # Enable OpenCV-based text region detection
        min_size=20,               # Minimum ROI size (points)
        visualize=True,            # Create visualizations
        save_excel=True            # Save to Excel
    )

    print(f"Detected {len(rois_df)} ROIs")