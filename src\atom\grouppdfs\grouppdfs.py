"""
PDF Grouping
"""
import os
import time
import fitz
from src.utils.logger import logger
import multiprocessing
import asyncio
import statistics
import cv2
import io
import math

from random import randint
import pandas as pd
from multiprocessing import freeze_support, Manager, Event
from pubsub import pub
from threading import Thread
from PIL import Image
import numpy as np

from src.atom.grouppdfs.detect_lines import get_hough_lines

# logger = logging.getLogger()

# Global variable to store the document in each worker process
_process_doc = None

def init_worker(pdf_path: str) -> None:
    """Initialize worker process with PDF document.
    This runs once per worker process."""
    global _process_doc
    if not _process_doc:
        _process_doc = fitz.open(pdf_path)

# Multiprocessing lock
lock = multiprocessing.Lock()


# Configuration
FILENAME = r"E:\IoT\M\Architekt-ATOM-res\Combined Remuriate.pdf"
OUTPUT_PATH = r"E:\IoT\M\Architekt-ATOM-res"
# For performance boost, speed up using multiprocessing
MULTIPROCESS: bool = True
# Debug MODE
# Save results .xlsx file to OUTPUT_PATH
# Generates PDF groups to OUTPUT_PATH
DEBUG_MODE: bool = True

def distance_to(p1, p2):
    # Gives the absolute distance between two points
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])

def page_to_opencv(page: fitz.Page):
    """Return open CV image from PyMuPDF page"""
    rgb = page.get_pixmap()
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.

    Note: same function as calculate_angle but supply two co-ords
    instead of line
    """
    x1, y1 = p1
    x2, y2 = p2
    dx = x2 - x1
    dy = y2 - y1
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range

def is_isometric_angle(angle, tolerance=5):
    isometric_angles = [30, 150, 90, 330, 210, 270]
    return any(min(abs(angle - iso_angle), abs(360 - abs(angle - iso_angle))) <= tolerance for iso_angle in isometric_angles)


class CancelException(Exception):

    def __init__(self):
        super().__init__()

def process_page(doc,
                 page_num: int,
                 process_times: list,
                 cancel_event: asyncio.Event):

    def check_cancelled():
        # Raising exception helps to break out of starmap
        # more immediately
        if cancel_event.is_set():
            raise CancelException

    check_cancelled()

    start = time.perf_counter()
    page: fitz.Page = doc[page_num]

    result = {
        "page_number": page_num + 1,
        "group_number": None,
    }
    margins = {}
    image = page_to_opencv(page)
    page_height, page_width, _ = image.shape
    result["width"] = page_width
    result["height"] = page_height
    result["page_area"] = page_width * page_height
    result["rotation"] = page.rotation
    result["left_margin"] = 0
    result["top_margin"] = 0
    result["right_margin"] = page.rect.width
    result["bottom_margin"] = page.rect.height
    result["top_left"] = (0,0)
    result["bottom_right"] = (page.rect.width, page.rect.height)
    result["error"] = None
    result["fallback"] = 0

    # Page orientation landscape/square = 0, portrait = 1
    # PAGE_HORIZONTAL = 0
    # PAGE_VERTICAL = 1
    result["page_orientation"] = 0 if page_width >= page_height else 1

    ph_half = page_height // 2
    pw_half = page_width // 2

    hough_lines = get_hough_lines(image)

    # No lines detected, might be a blank page
    if hough_lines is None:
        result["error"] = "No Hough Lines"
        result["fallback"] = 1
        return result

    lines = []
    for l in hough_lines:
        lines.extend(l)

    # Detect outer margins excluding the page boundaries itself
    for line in lines:
        x1, y1, x2, y2 = line
        p1 = [x1, y1]
        p2 = [x2, y2]

        length = distance_to(p1, p2)
        angle = angle_between_points(p1, p2)

        # line_data = {
        #     "lines": [[line]],
        #     "angle": angle,
        #     "length": length,
        #     "linetype": None,
        # }

        orientation = None
        # Excluding Keep selecting lines which are outer
        if all([abs(angle - 0) < 10, length > pw_half]): # horizontal
            orientation = "horizontal"
        elif all([abs(angle - 270) < 10, length > ph_half]): # vertical
            orientation = "vertical"
        else:
            continue

        # Excluding lines which are the page edges but keep selecting
        # the outermost border lines
        if orientation == "horizontal":
            if any([y1 < 5, page_height - y1 < 5]):
                continue
            if y1 <= margins.setdefault("top", y1):
                margins["top"] = y1
            if y1 >= margins.setdefault("bottom", y1):
                margins["bottom"] = y1
        else:
            if any([x1 < 5, page_width - x1 < 5]):
                continue
            if x1 <= margins.setdefault("left", x1):
                margins["left"] = x1
            if x1 >= margins.setdefault("right", x1):
                margins["right"] = x1

    # Fallback to page boundaries if outer border not detected
    margins.setdefault("left", 0)
    margins.setdefault("top", 0)
    margins.setdefault("right", page_width)
    margins.setdefault("bottom", page_height)

    if abs(margins["left"] - margins["right"]) < 100:
        margins["left"] = 0
        margins["right"] = page_width

    if abs(margins["top"] - margins["bottom"]) < 100:
        margins["top"] = 0
        margins["bottom"] = page_height

    result["left_margin"] = margins["left"]
    result["top_margin"] = margins["top"]
    result["right_margin"] = margins["right"]
    result["bottom_margin"] = margins["bottom"]
    result["top_left"] = (margins["left"], margins["top"])
    result["bottom_right"] = (margins["right"], margins["bottom"])

    process_time = time.perf_counter() - start
    with lock:
        process_times.append(process_time)

    return result

def group_pages_by_layout_multiprocess(filename, page_num: int, process_times: list, cancel_event):
    global _process_doc
    if _process_doc:
        doc = fitz.open(filename)
    else:
        doc = _process_doc

    try:
        return process_page(doc, page_num, process_times, cancel_event)
    except Exception as e:
        # Fallback page
        page: fitz.Page = doc[page_num]
        result = {
            "page_number": page_num + 1,
            "group_number": None,
        }
        result["width"] = page.rect.width
        result["height"] = page.rect.height
        result["page_area"] = page.rect.width * page.rect.height
        result["rotation"] = page.rotation
        result["left_margin"] = 0
        result["top_margin"] = 0
        result["right_margin"] = page.rect.width
        result["bottom_margin"] = page.rect.height
        result["top_left"] = (0,0)
        result["bottom_right"] = (page.rect.width, page.rect.height)
        result["error"] = str(e)
        result["fallback"] = 1
        return result

class GroupPdfs():

    def __init__(self,
                 filename: str,
                 output_path: str = None,
                 pages=None,
                 multiprocess: bool = False,
                 jobId=None,
                 save_dataframe_results=True,
                 debug: bool = False):

        self._debug_mode = debug
        self.pages = pages
        self.filename = filename
        self.output_path = output_path
        self.doc = fitz.open(filename)
        self.jobId = jobId
        self.total_groups = None

        self._multiprocess = multiprocess
        self._cancel = Event()
        self._total_page_processes = None
        self._time_of_last_request = None
        self._times = []
        self._eta_updated_cb = None
        self._last_eta = None
        self._timer = Thread(target=self.on_timer)
        self._page_results = None
        self._results_df = None
        self._save_dataframe_results = save_dataframe_results

    def _process_pages(self):
        """Process PDF and group pages and return a dataframe.

        Args:
            filename: Input PDF filename.
            output_path: Optionally generate.
            pages: A list[int] of pages to be processed. If None is supplied, all pages
                are analyzed.
            multiprocess: Enable multiprocessing

        Returns:
            A dataframe containing results of pages grouped
        """

        if self._multiprocess:
            freeze_support()
            manager = Manager()
            self._cancel = manager.Event()
            self._times = manager.list()

        print("Processing pages...")
        # Ensure the output directory exists
        if self.output_path:
            os.makedirs(self.output_path, exist_ok=True)

        if self.pages:
            pages = [int(p) for p in self.pages]
        else:
            pages = range(fitz.open(self.filename).page_count)

        self._total_page_processes = len(pages)

        if self._multiprocess:
            with multiprocessing.Pool(processes=multiprocessing.cpu_count(), initializer=init_worker, initargs=(self.filename,)) as pool:
                try:
                    results = pool.starmap(group_pages_by_layout_multiprocess,
                                        [(self.filename, page_num, self._times, self._cancel) for page_num in pages])
                except CancelException:
                    pass # Externally cancelled
                except Exception as e:
                    print(e)
                    raise e # TODO
                if self._cancel.is_set():
                    return
                results.sort(key=lambda x: x["page_number"])  # Ensure the results are in order of page numbers
        else:
            results = []
            self._times = []
            for page_num in pages:
                doc = fitz.open(self.filename)
                results.append(process_page(doc,
                                            page_num,
                                            self._times,
                                            self._cancel))
                if self._cancel.is_set():
                    return

        if self._cancel.is_set():
            return

        self._page_results = results

    def _process_results(self):
        """Group the page results"""
        print("Processing results...")
        self.total_groups = 0
        for r1 in self._page_results:
            r1["doc_path"] = self.filename
            # Top left coord and bottom right coord must be similar
            for r2 in self._page_results:
                if r1["page_number"] == r2["page_number"]:
                    continue
                if r2["group_number"] is None:
                    continue
                if r1["page_orientation"] != r2["page_orientation"]:
                    continue
                if r1["rotation"] != r2["rotation"]:
                    continue
                if not abs(r1["page_area"] - r2["page_area"]) < 5:
                    continue
                if not all([distance_to(r1["top_left"], r2["top_left"]) < 5,
                        distance_to(r1["bottom_right"], r2["bottom_right"]) < 5]):
                    continue
                r1["group_number"] = r2["group_number"]
                break
            else:
                # No group found, create a new one
                self.total_groups += 1
                r1["group_number"] = self.total_groups

        grouped_results = pd.DataFrame(self._page_results)
        grouped_results.sort_values(["group_number", "page_number"], ascending=[True, True], inplace=True)
        self._results_df = grouped_results

    def _create_debug_pdf(self):
        """Generates a PDF for debugging"""
        if not self._debug_mode or __file__.endswith(".pyc"):
            return

        print("Creating debug PDF...")
        outfile = os.path.join(self.output_path, "Grouped Page Results.pdf")
        pil_images = []
        # Assign random colors to each group
        group_colors = {}
        for n in range(1, self.total_groups + 1):
            group_colors[n] = (randint(10, 250), randint(10, 250), randint(10, 250))

        for pg in self._results_df.itertuples():
            page_number = pg.page_number
            print(f"Drawing debug page {page_number}")
            page: fitz.Page = self.doc[page_number-1]
            group_number = pg.group_number
            cv_image = page_to_opencv(page)

            border_color = group_colors[group_number]
            cv2.rectangle(cv_image, (int(pg.left_margin), int(pg.top_margin)), (int(pg.right_margin), int(pg.bottom_margin)), border_color, 3)

            text = f'group={group_number}/{self.total_groups}, page={page_number}, rotation={pg.rotation}'
            cv2.putText(cv_image, text, (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)

            text2 = f'size=({pg.width}x{pg.height})'
            cv2.putText(cv_image, text2, (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)

            text3 = f'border tl={pg.top_left} br={pg.bottom_right}'
            cv2.putText(cv_image, text3, (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)

            pil_image = Image.fromarray(cv_image)
            pil_images.append(pil_image)

        pil_images[0].save(
            outfile, "pdf", resolution=20.0, save_all=True, append_images=pil_images[1:]
        )

    def run(self):
        """Run classification"""
        pub.sendMessage("set-statusbar-realtime", message="Preprocessing Started", jobId=self.jobId)
        self.start_time = time.time()  # Capture the start time
        self._timer.start()
        for n, state in enumerate([
            self._process_pages,
            self._process_results,
            self._create_debug_pdf,
        ]):
            if self._cancel.is_set():
                print(f"Cancelled preprocessing at step {n} - function `{state.__name__}`")
                self._results_df = None
                return
            print(f"State {n} - function `{state.__name__}`")
            state()

        self._cancel.set()  # Done

    def cancel(self):
        """Cancel classification and all running tasks"""
        logger.debug("Cancelling classification")
        self._cancel.set()

    def on_timer(self):
        smoothing_factor = 0.05
        while not self._cancel.is_set():
            time.sleep(1)
            with lock:
                if not self._eta_updated_cb:
                    continue
                if self._total_page_processes is None:
                    continue
                times = self._times[-5:]
                remaining_pages = max(0, self._total_page_processes - len(self._times))
                if len(times) < 5:
                    continue
                eta = int(statistics.fmean(times) * remaining_pages)
                if self._last_eta is None:
                    self._last_eta = eta
                    continue
                eta = smoothing_factor * self._last_eta + (1 - smoothing_factor) * eta
                self._last_eta = int(eta)
                if self._eta_updated_cb:
                    self._eta_updated_cb(self._last_eta)

        if self._eta_updated_cb:
            self._eta_updated_cb(0)

    def get_results(self):
        return self._results_df


if __name__ == "__main__":

    pages = None # All pages
    start = time.time()

    def print_eta(eta):
        print(f"ETA: {eta}")

    def test_cancel(n=None):
        # Test function to cancel processing after `n` seconds
        import time
        if n is None:
            return
        time.sleep(n)
        processor.cancel()

    time_to_cancel = None
    Thread(target=lambda: test_cancel(time_to_cancel)).start()
    processor = GroupPdfs(filename=FILENAME,
                          output_path=OUTPUT_PATH,
                          pages=None,
                          multiprocess=MULTIPROCESS,
                          debug=DEBUG_MODE)
    processor._eta_updated_cb = print_eta
    processor.run()

    print(f"Processing time {time.time() - start}s")

    # Create the results output
    results_file = os.path.join(OUTPUT_PATH, "Grouped Page Results.xlsx")
    df = pd.DataFrame(processor.get_results())
    df = df.sort_values(['page_number'], ascending=True)
    df.to_excel(results_file)
    print("Created results xlsx at:", results_file)