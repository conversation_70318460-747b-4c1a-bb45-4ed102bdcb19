from typing import Dict, Optional
import pandas as pd
from src.atom.pg_database.pg_connection import DatabaseConfig
from src.atom.pg_database.data_import.workbook_db_importer import WorkbookImporter, get_table_columns, get_db_engine
from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper


def import_atem_rfq_input_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.atem_rfq_input table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names and first row to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            print("Converting display column names to PostgreSQL format...")
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq_input", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert integer columns
        integer_columns = ["project_id", "profile_id", "client_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq_input')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the database connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Insert the data
                # Use psycopg2's executemany for better NULL handling
                columns = df_pg.columns.tolist()
                placeholders = ", ".join([f"%({col})s" for col in columns])

                insert_query = f"""
                INSERT INTO public.atem_rfq_input ({', '.join(columns)})
                VALUES ({placeholders})
                """

                # Convert DataFrame to list of dictionaries for executemany
                records = df_pg.to_dict('records')
                cursor.executemany(insert_query, records)

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq_input table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_atem_rfq_df(df, db_config=None, import_to_input_first=False):
    """
    Import data from a DataFrame into the public.atem_rfq table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.
        import_to_input_first (bool, optional): If True, also import to atem_rfq_input table first.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # If requested, import to atem_rfq_input table first
        input_result = None
        if import_to_input_first:
            print("\nImporting to atem_rfq_input table first...")
            # Create a deduplicated dataframe based on material_description (case-insensitive)
            df_input = df.copy()

            # Check if required columns already exist in PostgreSQL format or need conversion
            required_pg_columns = ["project_id", "material_description"]
            if all(col in df_input.columns for col in required_pg_columns):
                # Columns are already in PostgreSQL format, use as is
                print("Using columns as-is, already in PostgreSQL format")
            else:
                # Convert display names to PostgreSQL column names
                print("Converting display column names to PostgreSQL format...")
                mapper = ColumnMapper()
                df_input, unmapped_columns = mapper.convert_dataframe_columns(
                    df_input, "atem_rfq", "display", "pg"
                )

                if unmapped_columns:
                    print(f"Warning: Could not map columns: {unmapped_columns}")

            # Now check if the required column exists after potential conversion
            if 'material_description' not in df_input.columns:
                print(f"Error: 'material_description' column not found in dataframe. Columns: {df_input.columns.tolist()}")
                return {
                    "status": "error",
                    "message": "Required column 'material_description' not found",
                    "total_rows": len(df),
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": len(df),
                    "errors": [{"row_index": "N/A", "error": "Required column 'material_description' not found"}]
                }

            # Convert material_description to lowercase for case-insensitive comparison
            # but keep the original values in the dataframe
            # Create a temporary lowercase column for deduplication
            df_input['material_description_lower'] = df_input['material_description'].astype(str).str.lower()

            # Deduplicate based on the lowercase column
            df_input = df_input.drop_duplicates(subset=['material_description_lower'])

            # Remove the temporary column
            df_input = df_input.drop(columns=['material_description_lower'])

            print(f"Created case-insensitive deduplicated dataframe with {len(df_input)} rows (from original {len(df)} rows)")

            try:
                # Import to atem_rfq_input table in a separate try/except block
                # to ensure transaction isolation
                input_result = import_atem_rfq_input_df(df_input, db_config)
                print(f"Import to atem_rfq_input complete: {input_result['inserted']} rows inserted")

                # Ask user if they want to continue with atem_rfq import
                if input_result['status'] == 'error':
                    print("\nWarning: There were errors importing to atem_rfq_input table.")
                    continue_import = input("Do you want to continue with importing to atem_rfq? (y/n): ")
                    if continue_import.lower() not in ['y', 'yes']:
                        return {
                            "status": "cancelled",
                            "message": "Import to atem_rfq cancelled by user after atem_rfq_input import",
                            "total_rows": len(df),
                            "valid_rows": 0,
                            "inserted": 0,
                            "updated": 0,
                            "error_rows": 0,
                            "errors": [],
                            "input_table_result": input_result
                        }
            except Exception as e:
                # If there's an error importing to atem_rfq_input, ask if user wants to continue
                error_msg = str(e)
                print(f"\nError importing to atem_rfq_input: {error_msg}")
                continue_import = input("Do you want to continue with importing to atem_rfq anyway? (y/n): ")
                if continue_import.lower() not in ['y', 'yes']:
                    return {
                        "status": "error",
                        "message": f"Error importing to atem_rfq_input: {error_msg}",
                        "total_rows": len(df),
                        "valid_rows": 0,
                        "inserted": 0,
                        "updated": 0,
                        "error_rows": len(df),
                        "errors": [{"row_index": "N/A", "error": error_msg}]
                    }
                # If user wants to continue despite errors, set input_result to indicate the error
                input_result = {
                    "status": "error",
                    "message": error_msg,
                    "total_rows": len(df_input),
                    "valid_rows": 0,
                    "inserted": 0,
                    "updated": 0,
                    "error_rows": len(df_input),
                    "errors": [{"row_index": "N/A", "error": error_msg}]
                }

        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names and first row to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "atem_rfq", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "size1", "size2", "quantity",
            "calculated_eq_length", "calculated_area"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "rfq_input_id", "profile_id", "client_id"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item", "mapping_not_found"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'atem_rfq')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Make sure all NULL values in boolean columns are explicitly False before sending to database
        # This is critical for the database trigger functions
        for col in ['deleted', 'ignore_item', 'mapping_not_found']:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(False)
                # Double-check no empty strings exist
                df_pg.loc[df_pg[col] == '', col] = False
                # Ensure boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Use the database connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Set session variable to avoid trigger errors with boolean values
                cursor.execute("SET LOCAL sync_in_progress.rfq_bom = false;")

                # Insert the data
                # Use psycopg2's executemany for better NULL handling
                columns = df_pg.columns.tolist()
                placeholders = ", ".join([f"%({col})s" for col in columns])

                insert_query = f"""
                INSERT INTO public.atem_rfq ({', '.join(columns)})
                VALUES ({placeholders})
                """

                # Convert DataFrame to list of dictionaries for executemany
                records = df_pg.to_dict('records')
                cursor.executemany(insert_query, records)

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()
        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        result = {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.atem_rfq table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

        # Add input table results if applicable
        if input_result:
            result["input_table_result"] = input_result

        return result

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_bom_direct(workbook_path=None, sheet_name=None, db_config=None, dataframe=None):
    """
    Import data from an Excel workbook or DataFrame into the public.bom table using direct SQL
    to bypass trigger issues entirely.

    Args:
        workbook_path (str, optional): Path to the Excel workbook. Not used if dataframe is provided.
        sheet_name (str, optional): Name of the sheet to import. Not used if dataframe is provided.
        db_config (dict, optional): Database configuration. If None, uses default.
        dataframe (pd.DataFrame, optional): DataFrame containing the data to import. If provided, workbook_path is ignored.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np
    import psycopg2
    import psycopg2.extras
    from psycopg2.extras import execute_values

    try:
        # Use provided dataframe or read from Excel file
        if dataframe is not None:
            df = dataframe.copy()
            print("Using provided DataFrame")
        else:
            # Read the Excel file
            if sheet_name is None:
                # Use the first sheet by default
                df = pd.read_excel(workbook_path, sheet_name=0)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            print(f"Read DataFrame from {workbook_path}")

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["project_id", "material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "bom", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = [
            "pdf_id", "pdf_page", "size1", "size2", "quantity",
            "length", "calculated_area", "calculated_eq_length"
        ]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Handle boolean columns
        boolean_columns = ["deleted", "ignore_item"]
        for col in boolean_columns:
            if col in df_pg.columns:
                # Convert various representations of boolean values
                df_pg[col] = df_pg[col].map({
                    True: True, 'True': True, 'true': True, 'TRUE': True, 't': True, 'T': True, 'Yes': True, 'yes': True, 'Y': True, 'y': True, 1: True,
                    False: False, 'False': False, 'false': False, 'FALSE': False, 'f': False, 'F': False, 'No': False, 'no': False, 'N': False, 'n': False, 0: False,
                    '': False, pd.NA: False, None: False, np.nan: False
                }, na_action='ignore').fillna(False)

                # Ensure the column is explicitly boolean type
                df_pg[col] = df_pg[col].astype(bool)

        # Set up database connection using the raw database configuration
        if db_config is None:
            from src.atom.pg_database.pg_connection import DatabaseConfig
            db_config = DatabaseConfig()

        # Get the columns that would exist in the database table
        engine = get_db_engine(db_config)
        existing_columns = get_table_columns(engine, 'bom')

        # Filter the DataFrame to only include columns that exist in the database
        extra_columns = [col for col in df_pg.columns if col not in existing_columns]
        if extra_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {extra_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Create a direct connection to PostgreSQL using psycopg2
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )

        try:
            # Make sure the connection doesn't autocommit
            conn.autocommit = False

            with conn.cursor() as cursor:
                # Disable BOM triggers for better import performance
                print("Disabling BOM triggers for faster import...")
                cursor.execute("""
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq;
                ALTER TABLE public.bom DISABLE TRIGGER trg_sync_bom_to_rfq_after;
                ALTER TABLE public.bom DISABLE TRIGGER trg_update_bom_component_calculations;
                """)
                # Get the column names in the dataframe
                columns = list(df_pg.columns)

                # Create the INSERT statement with ON CONFLICT DO NOTHING
                # This will skip rows that would cause constraint violations
                insert_query = f"""
                INSERT INTO public.bom ({', '.join(columns)})
                VALUES %s
                ON CONFLICT DO NOTHING
                """

                # Process the data in batches to avoid memory issues
                batch_size = 1000
                total_rows = len(df_pg)
                inserted_rows = 0

                print(f"Importing {total_rows} rows in batches of {batch_size}...")

                # Convert the dataframe to a list of tuples for direct insertion
                # This bypasses many of the SQLAlchemy and pandas overhead
                for i in range(0, total_rows, batch_size):
                    batch_df = df_pg.iloc[i:min(i+batch_size, total_rows)]

                    # Replace all forms of NaN with None so they become SQL NULL
                    # Create a copy to avoid modifying the original dataframe
                    batch_df_copy = batch_df.copy()

                    # Replace pandas NA, NaN and numpy nan with None
                    batch_df_copy = batch_df_copy.replace([pd.NA, pd.NaT, np.nan], None)

                    # Also replace any 'NaN' strings that might exist
                    batch_df_copy = batch_df_copy.replace('NaN', None)

                    # Convert to list of tuples for database insertion
                    batch_values = [tuple(row) for row in batch_df_copy.values]

                    # Use execute_values for bulk insertion (much faster than individual inserts)
                    execute_values(cursor, insert_query, batch_values)
                    inserted_rows += len(batch_values)
                    # More detailed progress reporting
                    current_percentage = (inserted_rows / total_rows) * 100
                    print(f"Processed {inserted_rows}/{total_rows} rows ({current_percentage:.1f}%)...")

                # Note: Would run manual sync here if we had disabled triggers, but skipping as triggers were active during import

                # Commit the transaction
                conn.commit()
                print(f"Successfully imported {inserted_rows} rows directly.")

        except Exception as e:
            # Rollback the transaction if there's an error
            conn.rollback()
            print(f"Error during direct SQL import: {e}")
            raise
        finally:
            try:
                # Re-enable BOM triggers that were disabled
                with conn.cursor() as cursor:
                    print("Re-enabling BOM triggers...")
                    cursor.execute("""
                    ALTER TABLE public.bom ENABLE TRIGGER trg_update_bom_component_calculations;
                    """)

                    # Intentionally disabled triggers as manual workflow works better
                    # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq;
                    # ALTER TABLE public.bom ENABLE TRIGGER trg_sync_bom_to_rfq_after;
                    conn.commit()
            except Exception as e:
                print(f"Warning: Failed to re-enable triggers: {e}")
            finally:
                # Always close the connection
                conn.close()

        # Format result to match expected output format
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.bom table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_verified_material_classifications_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.verified_material_classifications table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Always convert display names to PostgreSQL column names
        mapper = ColumnMapper()
        df_pg, unmapped_columns = mapper.convert_dataframe_columns(
            df, "verified_material_classifications", "display", "pg"
        )

        if unmapped_columns:
            print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check if required columns exist after conversion
        required_pg_columns = ["material_description"]

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'verified_material_classifications')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Add created_by and updated_by if they exist in the table but not in the dataframe
        if 'created_by' in existing_columns and 'created_by' not in df_pg.columns:
            df_pg['created_by'] = 'system_import'
        if 'updated_by' in existing_columns and 'updated_by' not in df_pg.columns:
            df_pg['updated_by'] = 'system_import'

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Use the database connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Insert the data with ON CONFLICT DO UPDATE
                # This will update existing rows based on the unique constraint
                columns = df_pg.columns.tolist()
                placeholders = ", ".join([f"%({col})s" for col in columns])
                update_set = ", ".join([f"{col} = EXCLUDED.{col}" for col in columns if col != 'material_description'])

                insert_query = f"""
                INSERT INTO public.verified_material_classifications ({', '.join(columns)})
                VALUES ({placeholders})
                ON CONFLICT (material_description) DO UPDATE
                SET {update_set}, updated_at = CURRENT_TIMESTAMP
                """

                # Convert DataFrame to list of dictionaries for executemany
                records = df_pg.to_dict('records')
                cursor.executemany(insert_query, records)

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.verified_material_classifications table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }


def import_verified_material_classifications_direct(workbook_path=None, sheet_name=None, db_config=None, dataframe=None):
    """
    Import data from an Excel workbook or DataFrame into the public.verified_material_classifications table using direct SQL
    with batch processing and progress updates.

    Args:
        workbook_path (str, optional): Path to the Excel workbook. Not used if dataframe is provided.
        sheet_name (str, optional): Name of the sheet to import. Not used if dataframe is provided.
        db_config (dict, optional): Database configuration. If None, uses default.
        dataframe (pd.DataFrame, optional): DataFrame containing the data to import. If provided, workbook_path is ignored.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np
    import psycopg2
    import psycopg2.extras
    from psycopg2.extras import execute_values

    try:
        # Use provided dataframe or read from Excel file
        if dataframe is not None:
            df = dataframe.copy()
            print("Using provided DataFrame")
        else:
            # Read the Excel file
            if sheet_name is None:
                # Use the first sheet by default
                df = pd.read_excel(workbook_path, sheet_name=0)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            print(f"Read DataFrame from {workbook_path}")

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # Check if required columns already exist in PostgreSQL format
        required_pg_columns = ["material_description"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "verified_material_classifications", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Set up database connection using the raw database configuration
        if db_config is None:
            from src.atom.pg_database.pg_connection import DatabaseConfig
            db_config = DatabaseConfig()

        # Get the columns that would exist in the database table
        engine = get_db_engine(db_config)
        existing_columns = get_table_columns(engine, 'verified_material_classifications')

        # Filter the DataFrame to only include columns that exist in the database
        extra_columns = [col for col in df_pg.columns if col not in existing_columns]
        if extra_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {extra_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Add created_by and updated_by if they exist in the table but not in the dataframe
        if 'created_by' in existing_columns and 'created_by' not in df_pg.columns:
            df_pg['created_by'] = 'system_import'
        if 'updated_by' in existing_columns and 'updated_by' not in df_pg.columns:
            df_pg['updated_by'] = 'system_import'

        # Ensure general_category is treated as string if it exists
        if 'general_category' in df_pg.columns:
            df_pg['general_category'] = df_pg['general_category'].astype(str)

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Create a direct connection to PostgreSQL using psycopg2
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )

        try:
            # Make sure the connection doesn't autocommit
            conn.autocommit = False

            with conn.cursor() as cursor:
                # Get the column names in the dataframe
                columns = list(df_pg.columns)

                # Create the INSERT statement with ON CONFLICT DO UPDATE
                # This will update existing rows based on the unique constraint
                update_set = ", ".join([f"{col} = EXCLUDED.{col}" for col in columns if col != 'material_description'])

                insert_query = f"""
                INSERT INTO public.verified_material_classifications ({', '.join(columns)})
                VALUES %s
                ON CONFLICT (material_description) DO UPDATE
                SET {update_set}, updated_at = CURRENT_TIMESTAMP
                """

                # Process the data in batches to avoid memory issues
                batch_size = 100  # Smaller batch size for UPSERT operations
                total_rows = len(df_pg)
                processed_rows = 0

                print(f"Importing {total_rows} rows in batches of {batch_size}...")

                # Convert the dataframe to a list of tuples for direct insertion
                # This bypasses many of the SQLAlchemy and pandas overhead
                for i in range(0, total_rows, batch_size):
                    batch_df = df_pg.iloc[i:min(i+batch_size, total_rows)]

                    # Replace all forms of NaN with None so they become SQL NULL
                    # Create a copy to avoid modifying the original dataframe
                    batch_df_copy = batch_df.copy()

                    # Replace pandas NA, NaN and numpy nan with None
                    batch_df_copy = batch_df_copy.replace([pd.NA, pd.NaT, np.nan], None)

                    # Also replace any 'NaN' strings that might exist
                    batch_df_copy = batch_df_copy.replace('NaN', None)

                    # Convert to list of tuples for database insertion
                    batch_values = [tuple(row) for row in batch_df_copy.values]

                    # Use execute_values for bulk insertion (much faster than individual inserts)
                    execute_values(cursor, insert_query, batch_values)
                    processed_rows += len(batch_values)

                    # More detailed progress reporting
                    current_percentage = (processed_rows / total_rows) * 100
                    print(f"Processed {processed_rows}/{total_rows} rows ({current_percentage:.1f}%)...")

                # Commit the transaction
                conn.commit()
                print(f"Successfully processed {processed_rows} rows directly.")

        except Exception as e:
            # Rollback the transaction if there's an error
            conn.rollback()
            print(f"Error during direct SQL import: {e}")
            raise
        finally:
            # Always close the connection
            conn.close()

        # Format result to match expected output format
        total_rows = len(df)
        processed_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully processed {processed_rows} rows in public.verified_material_classifications table",
            "total_rows": total_rows,
            "valid_rows": processed_rows,
            "inserted": processed_rows,  # Note: This is actually "processed" since we're doing UPSERT
            "updated": 0,
            "error_rows": total_rows - processed_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_general_direct(workbook_path=None, sheet_name=None, db_config=None, dataframe=None):
    """
    Import data from an Excel workbook or DataFrame into the public.general table using direct SQL
    with batch processing and progress updates.

    Args:
        workbook_path (str, optional): Path to the Excel workbook. Not used if dataframe is provided.
        sheet_name (str, optional): Name of the sheet to import. Not used if dataframe is provided.
        db_config (dict, optional): Database configuration. If None, uses default.
        dataframe (pd.DataFrame, optional): DataFrame containing the data to import. If provided, workbook_path is ignored.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np
    import psycopg2
    import psycopg2.extras
    from psycopg2.extras import execute_values

    try:
        # Use provided dataframe or read from Excel file
        if dataframe is not None:
            df = dataframe.copy()
            print("Using provided DataFrame")
        else:
            # Read the Excel file
            if sheet_name is None:
                # Use the first sheet by default
                df = pd.read_excel(workbook_path, sheet_name=0)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            print(f"Read DataFrame from {workbook_path}")

        # Drop any completely empty rows
        df = df.dropna(how='all')

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the workbook",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the workbook"}]
            }

        # For general table, we only require project_id
        required_pg_columns = ["project_id"]
        if all(col in df.columns for col in required_pg_columns):
            # Columns are already in PostgreSQL format, use as is
            df_pg = df
            print("Using columns as-is, already in PostgreSQL format")
        else:
            # Convert display names to PostgreSQL column names
            mapper = ColumnMapper()
            df_pg, unmapped_columns = mapper.convert_dataframe_columns(
                df, "general", "display", "pg"
            )

            if unmapped_columns:
                print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = ["size1", "size2", "quantity", "tally_count"]
        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Ensure general_category is treated as string
        if 'general_category' in df_pg.columns:
            df_pg['general_category'] = df_pg['general_category'].astype(str)

        # Convert integer columns - only project_id is required
        # pdf_id and pdf_page are optional but should be integers if present
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Convert date columns to proper datetime format
        date_columns = ["revision_date"]
        for col in date_columns:
            if col in df_pg.columns:
                # Convert to datetime, handling various date formats
                df_pg[col] = pd.to_datetime(df_pg[col], errors='coerce', infer_datetime_format=True)
                # Convert to date format (remove time component) for PostgreSQL DATE columns
                df_pg[col] = df_pg[col].dt.date

        # Set up database connection using the raw database configuration
        if db_config is None:
            from src.atom.pg_database.pg_connection import DatabaseConfig
            db_config = DatabaseConfig()

        # Get the columns that would exist in the database table
        engine = get_db_engine(db_config)
        existing_columns = get_table_columns(engine, 'general')

        # Filter the DataFrame to only include columns that exist in the database
        extra_columns = [col for col in df_pg.columns if col not in existing_columns]
        if extra_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {extra_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Create a direct connection to PostgreSQL using psycopg2
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )

        try:
            # Make sure the connection doesn't autocommit
            conn.autocommit = False

            with conn.cursor() as cursor:
                # Get the column names in the dataframe
                columns = list(df_pg.columns)

                # Create the INSERT statement
                insert_query = f"""
                INSERT INTO public.general ({', '.join(columns)})
                VALUES %s
                """

                # Process the data in batches to avoid memory issues
                batch_size = 100  # Smaller batch size for better progress visibility
                total_rows = len(df_pg)
                processed_rows = 0

                print(f"Importing {total_rows} rows into general table in batches of {batch_size}...")

                # Convert the dataframe to a list of tuples for direct insertion
                # This bypasses many of the SQLAlchemy and pandas overhead
                for i in range(0, total_rows, batch_size):
                    batch_df = df_pg.iloc[i:min(i+batch_size, total_rows)]

                    # Replace all forms of NaN with None so they become SQL NULL
                    # Create a copy to avoid modifying the original dataframe
                    batch_df_copy = batch_df.copy()

                    # Replace pandas NA, NaN and numpy nan with None
                    batch_df_copy = batch_df_copy.replace([pd.NA, pd.NaT, np.nan], None)

                    # Also replace any 'NaN' strings that might exist
                    batch_df_copy = batch_df_copy.replace('NaN', None)

                    # Convert to list of tuples for database insertion
                    batch_values = [tuple(row) for row in batch_df_copy.values]

                    # Use execute_values for bulk insertion (much faster than individual inserts)
                    execute_values(cursor, insert_query, batch_values)
                    processed_rows += len(batch_values)

                    # More detailed progress reporting
                    current_percentage = (processed_rows / total_rows) * 100
                    print(f"Processed {processed_rows}/{total_rows} rows ({current_percentage:.1f}%)...")

                # Commit the transaction
                conn.commit()
                print(f"Successfully imported {processed_rows} rows directly.")

        except Exception as e:
            # Rollback the transaction if there's an error
            conn.rollback()
            print(f"Error during direct SQL import: {e}")
            raise
        finally:
            # Always close the connection
            conn.close()

        # Format result to match expected output format
        total_rows = len(df)
        processed_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {processed_rows} rows into public.general table",
            "total_rows": total_rows,
            "valid_rows": processed_rows,
            "inserted": processed_rows,
            "updated": 0,
            "error_rows": total_rows - processed_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

def import_general_df(df, db_config=None):
    """
    Import data from a DataFrame into the public.general table.

    Args:
        df (pd.DataFrame): DataFrame containing the data to import
        db_config (dict, optional): Database configuration. If None, uses default.

    Returns:
        dict: Result of the import operation
    """
    from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper
    import numpy as np

    try:
        # Work with a copy of the DataFrame to avoid modifying the original
        df = df.copy()

        # Drop any completely empty rows
        df = df.dropna(how='all')

        # Debug: Print column names to verify data
        print(f"DataFrame columns before processing: {df.columns.tolist()}")

        if df.empty:
            return {
                "status": "error",
                "message": "No data found in the dataframe",
                "total_rows": 0,
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": 0,
                "errors": [{"row_index": "N/A", "error": "No data found in the dataframe"}]
            }

        # Always convert display names to PostgreSQL column names
        # This ensures proper mapping of columns like 'Line Number' to 'line_number'
        mapper = ColumnMapper()
        df_pg, unmapped_columns = mapper.convert_dataframe_columns(
            df, "general", "display", "pg"
        )

        if unmapped_columns:
            print(f"Warning: Could not map columns: {unmapped_columns}")

        # Check if required columns exist after conversion
        required_pg_columns = ["project_id", "pdf_id", "pdf_page"]

        # Check for required columns
        missing_columns = [col for col in required_pg_columns if col not in df_pg.columns]
        if missing_columns:
            errors = []
            for col in missing_columns:
                errors.append({"row_index": "ALL", "error": f"Required column missing: {col}"})

            return {
                "status": "error",
                "message": f"Required columns missing: {missing_columns}",
                "total_rows": len(df),
                "valid_rows": 0,
                "inserted": 0,
                "updated": 0,
                "error_rows": len(df),
                "errors": errors
            }

        # Convert numeric columns to appropriate types
        numeric_columns = ["size1", "size2", "quantity", "tally_count"]

        for col in numeric_columns:
            if col in df_pg.columns:
                df_pg[col] = pd.to_numeric(df_pg[col], errors='coerce')

        # Convert integer columns
        integer_columns = ["project_id", "pdf_id", "pdf_page"]
        for col in integer_columns:
            if col in df_pg.columns:
                df_pg[col] = df_pg[col].fillna(0).astype(int)

        # Convert date columns to proper datetime format
        date_columns = ["revision_date"]
        for col in date_columns:
            if col in df_pg.columns:
                # Convert to datetime, handling various date formats
                df_pg[col] = pd.to_datetime(df_pg[col], errors='coerce', infer_datetime_format=True)
                # Convert to date format (remove time component) for PostgreSQL DATE columns
                df_pg[col] = df_pg[col].dt.date

        # Set up database connection
        engine = get_db_engine(db_config)

        # Get the columns that exist in the database table
        existing_columns = get_table_columns(engine, 'general')

        # Filter the DataFrame to only include columns that exist in the database
        missing_columns = [col for col in df_pg.columns if col not in existing_columns]
        if missing_columns:
            print(f"Warning: Skipping columns that don't exist in the database table: {missing_columns}")
            df_pg = df_pg[[col for col in df_pg.columns if col in existing_columns]]

        # Clean the data before inserting
        # Replace NaN values with None (SQL NULL)
        # Also trim leading/trailing whitespace from string columns
        for col in df_pg.columns:
            # Replace various forms of NaN with None
            df_pg[col] = df_pg[col].replace([pd.NA, pd.NaT, np.nan], None)

            # Replace 'nan', 'NaN', 'NAN' strings with None
            if df_pg[col].dtype == 'object':
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

                # Trim leading/trailing whitespace from string values
                df_pg[col] = df_pg[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df_pg[col] = df_pg[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        # Insert data into the database
        print(f"Inserting {len(df_pg)} rows into the database...")

        # Use the database connection directly for more control
        connection = engine.raw_connection()
        try:
            with connection.cursor() as cursor:
                # Start a transaction
                cursor.execute("BEGIN;")

                # Insert the data
                # Use psycopg2's copy_from for better NULL handling
                columns = df_pg.columns.tolist()
                placeholders = ", ".join([f"%({col})s" for col in columns])

                insert_query = f"""
                INSERT INTO public.general ({', '.join(columns)})
                VALUES ({placeholders})
                """

                # Convert DataFrame to list of dictionaries for executemany
                records = df_pg.to_dict('records')
                cursor.executemany(insert_query, records)

                # Commit the transaction
                connection.commit()
        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

        print("Database insertion complete.")

        # Format result to match expected output format from main function
        total_rows = len(df)
        inserted_rows = len(df_pg)

        return {
            "status": "success",
            "message": f"Successfully imported {inserted_rows} rows into public.general table",
            "total_rows": total_rows,
            "valid_rows": inserted_rows,
            "inserted": inserted_rows,
            "updated": 0,
            "error_rows": total_rows - inserted_rows,
            "errors": []
        }

    except Exception as e:
        error_msg = str(e)
        # Create proper error structure
        errors = [{
            "row_index": "N/A",
            "error": error_msg
        }]

        return {
            "status": "error",
            "message": error_msg,
            "total_rows": 0,
            "valid_rows": 0,
            "inserted": 0,
            "updated": 0,
            "error_rows": 1,
            "errors": errors
        }

