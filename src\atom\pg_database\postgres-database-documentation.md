# PostgreSQL Database Documentation - ATEM System

## Overview

The ATEM (Agile Takeoff & Estimation Model) system is designed to extract, process, and analyze piping isometric drawings from structured PDF documents. This database architecture replaces a previous pandas DataFrame-based data processing with a more robust PostgreSQL solution, leveraging advanced features for improved performance and flexibility.

The system works by extracting data from PDF documents into structured tables, classifying components, performing calculations for equivalent lengths and surface areas, and generating aggregated data for estimation purposes.

### Key Objectives
- Provide real-time data updates across different views
- Standardize data formats (especially size formatting)
- Improve performance for large datasets
- Eliminate redundant code and mappings

### Database Connection
The PostgreSQL database is hosted on Neon. Connection details:
```
PGHOST='ep-silent-dew-a5ukl5n3-pooler.us-east-2.aws.neon.tech'
PGDATABASE='architekt-atem'
PGUSER='c.mccall%40architekt-is.com'
PGPASSWORD='[PASSWORD]'
```

## Key Database Components and Flow

### Origins and Source Files

This implementation replaces logic from several key Python files:
1. `value_mappings.py` - Contains mapping dictionaries converted to PostgreSQL lookup tables
2. `data_conversions.py` - Handles size formatting and conversions
3. `src/atom/tables/rfqtableview.py` - UI components and data processing logic
4. `src/atom/merge_rfq_into_bom.py` - Core merging logic for RFQ to BOM integration

### Data Flow

The system follows this logical flow:
1. Client and project setup (client → project → client_profile)
2. Data extraction from PDFs into general (page-level) and BOM (item-level) tables
3. Classification and deduplication via RFQ and RFQ_INPUT tables
4. Calculation of equivalent lengths and areas using lookups or factors
5. Aggregation back to general table by size/page

### Bidirectional Updates

The database employs a bidirectional update mechanism:
- Initial data extraction: PDF source → general → bom → atem_rfq → rfq_input
- Classification and updates: rfq_input ⟷ atem_rfq → bom → general

This bidirectional synchronization ensures data consistency across the application while preventing infinite update loops.

## Database Tables

### Client and Project Tables

#### `atem_clients`
- Core table for client information
- Stores company details and contact information
- Fields: `id`, `client_name`, `contact_name`, `contact_email`, `contact_phone`, `address`
- Primary table that links to projects

#### `atem_projects`
- Stores project information and metadata
- Links to clients via `client_id` foreign key
- Contains project status, timeline, and contact information
- Key columns: `id`, `client_id`, `project_name`, `location`, `jobsite_location`, `ais_project_status`
- Links to `profile_id` for calculation configuration

#### `atem_client_profiles`
- Defines calculation methodologies for clients
- Contains configuration for equivalent length and area calculations
- Specifies method types: `equivalent_length_method` (lookup/factor), `area_method`, `flange_calculation_method`
- Used by calculation functions to determine which approach to use

### Core Data Processing Tables

#### `general` (PDF Page Level)
- Contains page-level information from PDF isometrics
- One row per PDF page (with multiple rows possible for different pipe sizes)
- Includes title block information, metadata, and drawing references
- Aggregation columns for quantities by type (elbows, flanges, etc.)
- Stores calculated length and area totals by size
- Size-specific data is crucial for aggregations

#### `bom` (Bill of Materials - Component Level)
- Detailed component data with multiple rows per page
- Material descriptions, sizes, and quantities
- Linked to general table via `pdf_id` and `pdf_page`
- Classification fields for component categories
- Calculated fields for equivalent length and area

#### `atem_rfq` (Request for Quote)
- Stores deduplicated material entries with calculations
- Created automatically from BOM entries
- Contains all sizing, classification, and quantity information
- Linked to BOM via `rfq_ref_id` relationship
- Key fields for calculations: `size1`, `size2`, `general_category`, `rfq_scope`, `fitting_category`

#### `rfq_input` (Master Reference)
- Standardized material catalog across projects
- Used for consistent classification and categorization
- Bidirectionally synced with `atem_rfq`
- Contains extensive classification metadata

### Lookup and Reference Tables

#### `standard_fittings`
- Standard size fittings with length and area values
- Stores data for various fitting types (elbows, tees, etc.)
- Used by lookup function for equivalent length calculations
- Key fields: `profile`, `lookup_category`, `size1`, `length_ft`, `area_ft`

#### `reducers`, `elbows_reducing`, etc.
- Similar to standard_fittings but for multi-size components
- Contains `size1` and `size2` fields for compound sizes
- Used for component-specific sizing and calculations

#### `flange_data`
- Specialized table for detailed flange calculations
- Different columns for flange types: weld_neck, slip_on_thrd, lap_joint
- Indexed by profile_id, size_in, and pressure rating (lbs)
- Contains both length and area values for each flange type

#### `atem_bom_component_mapping`
- Maps component types to categories and aggregation columns
- Used to determine how components are classified
- Links `fitting_category` to `general_category` and `rfq_scope`
- `map_to_gen` field specifies which general table column to update

#### `atem_equiv_length_factors`
- Used for factor-based calculations
- Multiplier factors for each component type
- Applied when client profile uses "factor" calculation method

#### `vw_fittings_lookup` (View)
- Unified view combining standard_fittings, reducers, and other fitting tables
- Standardized columns for consistent lookup operations
- Used by `update_rfq_calculations` function for data retrieval

### Utility Tables

#### `trigger_logs`
- Tracks trigger operations and errors
- Used for debugging and monitoring
- Records mapping operations and status

## Key Database Functions

### Calculation Functions

#### `update_rfq_calculations()`
- Core calculation function for equivalent lengths and areas
- Handles both lookup and factor calculation methods
- Implements sophisticated size matching algorithm
- Contains special flange calculation logic based on flange type
- Usage: Triggered on RFQ updates affecting sizing or classification

#### `update_flange_calculations()` (Integrated in `update_rfq_calculations()`)
- Special handling for flanges based on fitting_category
- Uses flange_data table for detailed calculations
- Extracts flange type and pressure rating from descriptions
- Maps to appropriate length/area columns based on flange type

#### `calculate_pipe_surface_area()`
- Utility function for complex area calculations
- Supports multiple input/output units
- Used for specialized pipe area calculations

### Synchronization Functions

#### `sync_rfq_to_input()` / `sync_input_to_rfq()`
- Bidirectional sync between RFQ and RFQ_INPUT tables
- Handles creation and updates of standardized material records
- Preserves categorical data during updates

#### `sync_bom_to_rfq()` / `sync_rfq_to_bom()`
- Manages relationships between BOM and RFQ tables
- Handles RFQ record creation, linking, and updates
- Prevents infinite recursion during bidirectional updates

#### `update_categories_from_mapping()`
- Updates RFQ categories based on component mapping
- Applies mapping rules from `atem_bom_component_mapping` table
- Handles defaults for unmapped components

### Aggregation Functions

#### `preview_bom_to_general_aggregation()` / `manage_bom_to_general_aggregation()`
- Aggregates BOM data by size and category to general table
- Creates or updates size-specific rows in general table
- Sums quantities into appropriate category columns

#### `report_general_with_length()`
- Generates comprehensive reports combining general and aggregated data
- Used for reporting and analysis

## Important Triggers

### RFQ Calculation Trigger
```sql
CREATE TRIGGER trg_update_rfq_calculations
BEFORE INSERT OR UPDATE OF size1, size2, general_category, rfq_scope, 
                        fitting_category, valve_type, pipe_category, 
                        weld_category, quantity, profile_id
ON public.atem_rfq
FOR EACH ROW EXECUTE FUNCTION update_rfq_calculations();
```

### Sync Triggers
```sql
-- RFQ to RFQ_INPUT sync
CREATE TRIGGER trg_sync_rfq_to_input
BEFORE INSERT OR UPDATE ON public.atem_rfq
FOR EACH ROW EXECUTE FUNCTION sync_rfq_to_input();

-- RFQ_INPUT to RFQ sync
CREATE TRIGGER trg_sync_input_to_rfq
AFTER UPDATE ON public.atem_rfq_input
FOR EACH ROW EXECUTE FUNCTION sync_input_to_rfq();

-- BOM to RFQ sync
CREATE TRIGGER trg_sync_bom_to_rfq
BEFORE INSERT OR UPDATE ON public.bom
FOR EACH ROW EXECUTE FUNCTION sync_bom_to_rfq();

-- RFQ to BOM sync
CREATE TRIGGER trg_sync_rfq_to_bom
AFTER UPDATE OF general_category, rfq_scope, material, rating, ends, 
                fitting_category, valve_type, calculated_eq_length, calculated_area
ON public.atem_rfq
FOR EACH ROW EXECUTE FUNCTION sync_rfq_to_bom();
```

### Profile ID Sync Triggers
```sql
-- Update RFQ profile_id from project
CREATE TRIGGER trg_update_rfq_profile_id
BEFORE INSERT OR UPDATE OF project_id ON public.atem_rfq
FOR EACH ROW EXECUTE FUNCTION update_rfq_profile_id();

-- Update BOM profile_id from project
CREATE TRIGGER trg_update_bom_profile_id
BEFORE INSERT OR UPDATE OF project_id ON public.bom
FOR EACH ROW EXECUTE FUNCTION update_bom_profile_id();
```

## Setting Up a New Project

1. **Create Client Record**
   ```sql
   INSERT INTO atem_clients (client_name, contact_name, contact_email)
   VALUES ('Client Name', 'Contact Person', '<EMAIL>');
   ```

2. **Create Client Profile**
   ```sql
   INSERT INTO atem_client_profiles (
       ref_id, client_name, profile_name, profile_description,
       equivalent_length_method, area_method, flange_calculation_method
   ) VALUES (
       (SELECT id FROM atem_clients WHERE client_name = 'Client Name'),
       'Client Name', 'Standard Profile', 'Default calculation profile',
       'lookup', 'standard', 'detailed'
   );
   ```

3. **Create Project**
   ```sql
   INSERT INTO atem_projects (
       client_id, project_name, location, profile_id
   ) VALUES (
       (SELECT id FROM atem_clients WHERE client_name = 'Client Name'),
       'Project Name',
       'Project Location',
       (SELECT id FROM atem_client_profiles WHERE profile_name = 'Standard Profile')
   );
   ```

4. **Set Up Component Mappings**
   ```sql
   INSERT INTO atem_bom_component_mapping (
       profile_id, component_name, takeoff_category, general_category, map_to_gen
   ) VALUES (
       (SELECT id FROM atem_client_profiles WHERE profile_name = 'Standard Profile'),
       'WN Flange RF', 'Fittings', 'flanges', 'flanges'
   );
   ```

5. **Configure Equivalent Length Factors (If Using Factor Method)**
   ```sql
   INSERT INTO atem_equiv_length_factors (
       profile_id, component_type, eq_length_factor
   ) VALUES (
       (SELECT id FROM atem_client_profiles WHERE profile_name = 'Standard Profile'),
       'Elbow 90', 3.5
   );
   ```

## Calculation Methods

### Equivalent Length Calculation Methods

1. **Lookup Method** (`equivalent_length_method = 'lookup'`)
   - Uses predefined values from lookup tables
   - Steps:
     1. Try exact match with fitting_category and exact size
     2. Try exact match with general_category and exact size
     3. For compound sizes, try size reversals (4"×2" vs 2"×4")
     4. Try with larger of the two sizes
     5. Try nearest size approximation

2. **Factor Method** (`equivalent_length_method = 'factor'`)
   - Uses multiplier factors from `atem_equiv_length_factors`
   - Multiplies quantity by factor value
   - No area calculation with this method

### Flange Calculation Methods

1. **Standard Method** (`flange_calculation_method = 'standard'`)
   - Uses existing lookup or factor method without special handling

2. **Detailed Method** (`flange_calculation_method = 'detailed'`)
   - Uses `flange_data` table with dedicated columns by flange type
   - Extracts flange type from `fitting_category`
   - Determines pressure rating from `rating` field
   - Matches to specific flange type columns: `weld_neck_length_ft`, `slip_on_thrd_length_ft`, etc.

## Critical SQL Patterns and Optimizations

### Preventing Infinite Recursion
Bidirectional sync creates risk of infinite loops. Prevention techniques:
```sql
-- Check if we're in a sync operation
IF EXISTS (
    SELECT 1 FROM pg_locks
    WHERE objid = project_lock_key
    AND locktype = 'advisory'
) THEN
    RETURN NEW;
END IF;

-- Acquire a project-specific advisory lock
PERFORM pg_advisory_xact_lock(project_lock_key);
```

### Size Matching Algorithm
The function implements sophisticated size matching:
```sql
-- Exact match (size1=4, size2=NULL)
(size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)

-- Size reversal (size1=4, size2=2 and size1=2, size2=4)
(size1 = NEW.size1 AND size2 = NEW.size2) OR (size1 = NEW.size2 AND size2 = NEW.size1)

-- Largest size approximation
SELECT MIN(size1) INTO v_closest_size
FROM public.vw_fittings_lookup
WHERE lookup_category = v_lookup_category
AND profile = v_profile_name
AND size1 >= NEW.size1
AND size2 IS NULL;
```

### Rating Extraction Pattern
For extracting pressure ratings from text fields:
```sql
-- Extract from string like "150#" or "150.0"
v_lbs := SUBSTRING(NEW.rating FROM '^[0-9]+')::INTEGER;

-- Handle decimal values if needed
v_lbs := CAST(SUBSTRING(NEW.rating FROM '^[0-9]+\.[0-9]+') AS DECIMAL)::INTEGER;
```

## Common Issues and Troubleshooting

### Calculation Not Applied
- Check `profile_id` is set correctly on RFQ entries
- Verify `equivalent_length_method` is correctly set in client profile
- For flanges, ensure both `general_category` and `rfq_scope` are set to 'flanges' (case-insensitive)
- Confirm lookup data exists for the specific size and type

### Lookup Failures
- Check if size exists in lookup tables
- Verify lookup_category matches fitting_category or general_category
- Examine profile name matches between RFQ and lookup data
- For multi-size components, try both size orders

### Synchronization Issues
- Check for trigger failures in `trigger_logs` table
- Verify RFQ references in BOM are set correctly
- Check for transaction isolation issues during high concurrency

## Database Indexing Strategy

Key indexes for performance:
- Client and project foreign keys: `idx_projects_client_id`
- Category and component mappings: `idx_bom_component_mapping_component`
- Size-based lookups: `idx_flange_lookup`, `idx_standard_fittings_size`
- PDF identifiers: `idx_general_pdf_id`, `idx_bom_pdf_id`
- Material descriptions: `idx_bom_material_description`, `idx_rfq_material_desc`
- Composite indexes for multi-field lookups: `idx_rfq_sizes` (size1, size2), `idx_client_profile` (client_id, profile)

These indexes are strategically placed to optimize both transactional operations and reporting queries. They support efficient data retrieval patterns observed in the application and ensure good performance as the dataset grows.

## Test Cases and Validation

The following SQL snippets can be used to test the proper functioning of the database system:

### 1. Testing RFQ to RFQ_INPUT Synchronization
```sql
-- Import data from RFQ_INPUT to RFQ
SELECT import_rfq_input_data();

-- Check results
SELECT * FROM rfq LIMIT 10;

-- Update an RFQ entry and verify the changes propagate
UPDATE rfq SET quantity = 30 WHERE id = 3;
SELECT * FROM rfq_input WHERE id = (SELECT rfq_input_id FROM rfq WHERE id = 3);
```

### 2. Testing Size Formatting
```sql
-- Test the process_size function
SELECT * FROM process_size('4x2');
SELECT * FROM process_size('4');
SELECT * FROM process_size('');

-- Test size formatting
SELECT format_size_value('4');
SELECT format_size_value('4.0');
SELECT format_size_value('nan');
```

### 3. Testing Classification
```sql
-- Update an item's classification
UPDATE rfq 
SET rfq_scope = 'Fittings', fitting_category = 'Elbow' 
WHERE id = 1;

-- Verify classification was applied
SELECT id, material_description, rfq_scope, fitting_category, general_category
FROM rfq WHERE id = 1;
```

## Future Development Considerations

1. **Performance Optimization**
   - Consider partial indexes for large tables
   - Implement materialized views for complex aggregations
   - Add statistics for query planner on size/category columns

2. **Enhanced Classification**
   - Add AI-based classification for material_description
   - Implement fuzzy matching for component categories
   - Develop self-learning mapping system

3. **Data Validation**
   - Add constraints for valid size ranges
   - Implement cross-table validation triggers
   - Add enhanced logging for calculation failures

4. **Reporting Enhancements**
   - Create standardized reporting views
   - Implement time-series tracking of calculation changes
   - Add project comparison functionality

5. **Integration with Python Application**
   - Refine database connection management in the Python codebase
   - Implement transaction handling for data consistency
   - Create helper functions to simplify database interactions
