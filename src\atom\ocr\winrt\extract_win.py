import sys
import os
import asyncio
import base64
import copy
import pprint
import json
import re
import gc
import cv2
from PIL import Image
import numpy as np

# Try to import the Windows OCR libraries
# First try winsdk (recommended newer package)
try:
    from winsdk.windows.media.ocr import OcrEngine
    from winsdk.windows.globalization import Language
    from winsdk.windows.graphics.imaging import Bitmap<PERSON>lphaMode, BitmapPixelFormat, SoftwareBitmap
    from winsdk.windows.security.cryptography import CryptographicBuffer
    from winsdk.windows.foundation import Rect
    WINDOWS_SDK = 'winsdk'
    print("Using winsdk package")
except ImportError:
    try:
        # Fall back to winrt (older package)
        from winrt.windows.media.ocr import OcrEngine
        from winrt.windows.globalization import Language
        from winrt.windows.graphics.imaging import BitmapAlphaMode, BitmapPixelFormat, SoftwareBitmap
        from winrt.windows.security.cryptography import CryptographicBuffer
        from winrt.windows.foundation import Rect
        WINDOWS_SDK = 'winrt'
        print("Using winrt package (deprecated)")
    except ImportError:
        print("ERROR: Neither 'winsdk' nor 'winrt' package is installed.")
        print("Please install one of these packages:")
        print("  pip install winsdk  # Recommended")
        print("  pip install winrt   # Alternative (deprecated)")
        print("\nAlso make sure you have Pillow installed:")
        print("  pip install Pillow")
        sys.exit(1)


class WinRTResource:
    def __init__(self, resource):
        self.resource = resource

    def __enter__(self):
        return self.resource

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            if hasattr(self.resource, 'close'):
                self.resource.close()
        except:
            pass
        finally:
            self.resource = None

class rect:
    def __init__(self, x, y, w, h):
        self.x = x
        self.y = y
        self.width = w
        self.height = h

    def __repr__(self):
        return 'rect(%d, %d, %d, %d)' % (self.x, self.y, self.width, self.height)

    def right(self):
        return self.x + self.width

    def bottom(self):
        return self.y + self.height

    def set_right(self, value):
        self.width = value - self.x

    def set_bottom(self, value):
        self.height = value - self.y


def dump_rect(rtrect):
    """Convert a Windows Rect to our custom rect class"""
    return rect(rtrect.x, rtrect.y, rtrect.width, rtrect.height)

def dump_ocrword(word):
    return {
        'bounding_rect': dump_rect(word.bounding_rect),
        'text': word.text
    }

def merge_words(words):
    if len(words) == 0:
        return words
    new_words = [copy.deepcopy(words[0])]
    words = words[1:]
    for word in words:
        lastnewword = new_words[-1]
        lastnewwordrect = new_words[-1]['bounding_rect']
        wordrect = word['bounding_rect']
        if len(word['text']) == 1 and wordrect.x - lastnewwordrect.right() <= wordrect.width * 0.2:
            lastnewword['text'] += word['text']
            lastnewwordrect.x = min((wordrect.x, lastnewwordrect.x))
            lastnewwordrect.y = min((wordrect.y, lastnewwordrect.y))
            lastnewwordrect.set_right(max((wordrect.right(), lastnewwordrect.right())))
            lastnewwordrect.set_bottom(max((wordrect.bottom(), lastnewwordrect.bottom())))
        else:
            new_words.append(copy.deepcopy(word))
    return new_words

def dump_ocrline(line):
    words = list(map(dump_ocrword, line.words))
    merged = merge_words(words)
    return {
        'text': line.text,
        'words': words,
        'merged_words': merged,
        'merged_text': ' '.join(map(lambda x: x['text'], merged))
    }

def dump_ocrresult(ocrresult):
    lines = list(map(dump_ocrline, ocrresult.lines))
    return {
        'text': ocrresult.text,
        'text_angle': ocrresult.text_angle if ocrresult.text_angle is not None else None,
        'lines': lines,
        'merged_text': ' '.join(map(lambda x: x['merged_text'], lines))
    }


def ibuffer(s):
    """create WinRT IBuffer instance from a bytes-like object"""
    return CryptographicBuffer.decode_from_base64_string(base64.b64encode(s).decode('ascii'))

def swbmp_from_pil_image(img):
    try:
        # Create a copy to avoid modifying original
        rgba_img = img.convert("RGBA") if img.mode != "RGBA" else img.copy()

        # Get bytes and create buffer
        pybuf = rgba_img.tobytes()
        rtbuf = ibuffer(pybuf)

        # Create bitmap
        swbmp = SoftwareBitmap.create_copy_from_buffer(
            rtbuf,
            BitmapPixelFormat.RGBA8,
            rgba_img.width,
            rgba_img.height,
            BitmapAlphaMode.STRAIGHT
        )

        return swbmp
    finally:
        # Explicitly clean up
        if 'rgba_img' in locals():
            rgba_img.close()

        # Clear references
        if 'pybuf' in locals():
            del pybuf
        if 'rtbuf' in locals():
            del rtbuf


async def ensure_coroutine(awaitable):
    return await awaitable

def blocking_wait(awaitable):
    return asyncio.run(ensure_coroutine(awaitable))

def recognize_pil_image(img, lang):
    lang_obj = None
    eng = None
    swbmp = None
    res = None
    try:
        lang_obj = Language(lang)
        assert(OcrEngine.is_language_supported(lang_obj))

        with WinRTResource(OcrEngine.try_create_from_language(lang_obj)) as eng:
            with WinRTResource(swbmp_from_pil_image(img)) as swbmp:
                # Get OCR result and immediately convert to plain Python objects
                ocr_result = blocking_wait(eng.recognize_async(swbmp))
                res = dump_ocrresult(ocr_result)
                return res
    finally:
        # Ensure Windows Runtime objects are properly disposed
        if swbmp:
            try:
                swbmp.close()
            except:
                pass
        if eng:
            try:
                eng.close()
            except:
                pass

        # Release references
        swbmp = None
        eng = None
        lang_obj = None
        ocr_result = None

def recognize_cv2_image(cv_img, lang='en'):
    """
    Converts a cv2 image (numpy array) to PIL and uses recognize_pil_image.
    """
    # Convert BGR to RGB
    rgb = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)

    # Convert to PIL Image
    pil_img = Image.fromarray(rgb)

    # Call existing recognition function
    return recognize_pil_image(pil_img, lang)

def add_padding(image, pad=10, color=255):
    return cv2.copyMakeBorder(image, pad, pad, pad, pad, cv2.BORDER_CONSTANT, value=color)

def recognize_file(filename, lang='en-US'):
    img = None
    result = None
    try:

        # preprocess image for ocr
        image = cv2.imread(filename)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # improve contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)

        # 2. Invert if white text on black
        white_pct = np.sum(gray > 200) / gray.size
        if white_pct < 0.5:
            gray = cv2.bitwise_not(gray)

        # Noise reduction
        # blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # Binarization (Thresholding) to make text foreground clear
        # _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        print("binary shape:", binary.shape)
        print("nonzero:", cv2.countNonZero(binary))

        # cv2.imwrite("test_img.png", binary)
        # cv2.imshow("test", binary)
        # cv2.waitKey(0)

        # 5. Dilate to thicken text
        inverted = cv2.bitwise_not(binary) # thickens white text on black background
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        dilated = cv2.dilate(inverted, kernel, iterations=1)
        thickened = cv2.bitwise_not(dilated)  # Re-invert back to original polarity

        # _, binary = cv2.threshold(thickened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # inverted = cv2.bitwise_not(binary)
        # 4. Find bounding box of non-empty region
        coords = cv2.findNonZero(binary)
        # coords = cv2.findNonZero(binary)
        x, y, w, h = cv2.boundingRect(coords)
        cropped = binary[y:y+h, x:x+w]

        # For WinRT, invert to black text on white (OCR often works better this way)
        # binary = cv2.bitwise_not(binary)

        # Morphological Operations
        # Clean up or unify character
        # kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        # cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        padded = add_padding(cropped, pad=16)

        # Resize / Upscale
        #
        scale_factor = 3
        resized = cv2.resize(padded, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_LINEAR)

        img = Image.open(filename)
        w, h = img.size

        # Get OCR result
        result = recognize_cv2_image(resized, lang)

        print(result)
        # cv2.imwrite("test_img.png", resized)
        # cv2.imshow("title", resized)
        # cv2.waitKey(0)

        # Create a new dict with only the needed data
        clean_result = {
            "image_size": [w, h],
            "text": result.get("text", ""),
            "merged_text": result.get("merged_text", ""),
            "text_angle": result.get("text_angle"),
            "lines": []
        }

        # Copy line data without references to WinRT objects
        for line in result.get("lines", []):
            clean_line = {
                "text": line.get("text", ""),
                "merged_text": line.get("merged_text", ""),
                "words": [{
                    "text": word.get("text", ""),
                    "bounding_rect": {
                        "x": word["bounding_rect"].x,
                        "y": word["bounding_rect"].y,
                        "width": word["bounding_rect"].width,
                        "height": word["bounding_rect"].height
                    }
                } for word in line.get("words", [])]
            }
            clean_result["lines"].append(clean_line)

        return clean_result
    finally:
        # Ensure image is closed even if an exception occurs
        if img:
            try:
                img.close()
            except:
                pass
        # Clear references
        img = None
        result = None

def process_folder(folder_path, lang='en-US', output_file=None, pages_to_extract=None):
    """Process all PNG images in a folder and save results"""
    results = {}

    # Ensure folder path exists
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return

    # Get all PNG files in the folder
    png_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.png')]

    if not png_files:
        print(f"No PNG files found in {folder_path}")
        return

    def extract_page_number(filename):
        # Try to extract page number from patterns like "page_100" or "page_1"
        match = re.search(r'page_(\d+)', filename)
        if match:
            return int(match.group(1))
        return

    print(f"Found {len(png_files)} PNG files to process.")

    n = 0
    filecount = len(png_files)
    # Process each file
    for i, png_file in enumerate(png_files):

        field_match = re.search(r'(.+?)_page_', png_file)
        field = field_match.group(1) if field_match else "unknown"
        pdf_page = extract_page_number(png_file)

        if pages_to_extract is not None and pdf_page not in pages_to_extract:
            continue

        if n > 50:
            print(f"gc.collect()")
            gc.collect()
            n = 0
            # print(n, i)
        print(i, "/", filecount)
        n += 1

        full_path = os.path.join(folder_path, png_file)
        print(f"Processing {i+1}/{len(png_files)}: {png_file}")

        try:
            result = recognize_file(full_path, lang)
            result['pdf_page'] = pdf_page
            result['field'] = field
            results[png_file] = result

            # Print brief summary of the result
            print(f"  Extracted text: {result['merged_text'][:100]}..." if result['merged_text'] else "  No text extracted")

            # # Save individual result (optional)
            # individual_output = os.path.splitext(png_file)[0] + "_ocr.txt"
            # individual_output = os.path.join(folder_path, individual_output)
            # with open(individual_output, 'w', encoding='utf-8') as f:
            #     f.write(result['merged_text'])
            # print(f"  Text saved to {individual_output}")

        except Exception as e:
            print(f"  Error processing {png_file}: {str(e)}")

    # Save all results to a file if requested
    if output_file:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        try:
            output_path = output_file
            # output_path = output_file
            # with open(output_path, 'w', encoding='utf-8') as f:
            #     f.write(pprint.pformat(results, width=120))
            # print(f"Results saved to {output_path}")

            # Also save as JSON for easier processing
            json_output = os.path.splitext(output_path)[0] + ".json"
            with open(json_output, 'w', encoding='utf-8') as f:
                # Create a JSON-serializable version of the results
                json_results = {}
                for image, result in results.items():
                    json_results[image] = {
                        'pdf_page': pdf_page,
                        'image_size': result['image_size'],
                        'text': result['text'],
                        'text_angle': result['text_angle'],
                        'merged_text': result['merged_text'],
                        'lines': [],
                    }

                    # Convert lines and rect objects to dictionaries
                    for line in result['lines']:
                        json_line = {
                            'text': line['text'],
                            'merged_text': line['merged_text'],
                            'words': [],
                            'merged_words': []
                        }

                        for word in line['words']:
                            rect_obj = word['bounding_rect']
                            json_line['words'].append({
                                'text': word['text'],
                                'bounding_rect': {
                                    'x': rect_obj["x"],
                                    'y': rect_obj["y"],
                                    'width': rect_obj["width"],
                                    'height': rect_obj["height"]
                                }
                            })

                        for word in line.get('merged_words', []):
                            rect_obj = word['bounding_rect']
                            json_line['merged_words'].append({
                                'text': word['text'],
                                'bounding_rect': {
                                    'x': rect_obj["x"],
                                    'y': rect_obj["y"],
                                    'width': rect_obj["width"],
                                    'height': rect_obj["height"]
                                }
                            })

                        json_results[image]['lines'].append(json_line)

                json.dump(json_results, f, indent=2)
            print(f"JSON results saved to {json_output}")
        except Exception as e:
            print(f"Error saving results: {str(e)}")

    return results

def extract_table_to_csv(ocr_result, output_csv):
    """
    Attempt to convert OCR result to a CSV table based on spatial arrangement of text
    """
    import csv

    lines = ocr_result['lines']

    # Skip if no lines detected
    if not lines:
        print("No text lines detected for table extraction")
        return

    # Sort lines by Y coordinate (top to bottom)
    sorted_lines = sorted(lines, key=lambda line: line['merged_words'][0]['bounding_rect'].y if line['merged_words'] else 0)

    # Collect all word positions to identify columns
    all_words_with_positions = []
    for line in sorted_lines:
        for word in line['merged_words']:
            all_words_with_positions.append({
                'text': word['text'],
                'x': word['bounding_rect'].x,
                'right': word['bounding_rect'].x + word['bounding_rect'].width,
                'y': word['bounding_rect'].y,
                'bottom': word['bounding_rect'].y + word['bounding_rect'].height
            })

    # Skip if no words detected
    if not all_words_with_positions:
        print("No words detected for table extraction")
        return

    # Simple approach: group words into rows based on y-position
    # This is a simplified approach and might need refinement for complex tables
    y_sorted = sorted(all_words_with_positions, key=lambda w: w['y'])

    rows = []
    current_row = []
    current_y = y_sorted[0]['y']

    # Threshold for considering words on the same line (adjust as needed)
    y_threshold = 15

    for word in y_sorted:
        if abs(word['y'] - current_y) > y_threshold:
            # New row
            if current_row:
                # Sort words in the row by x-position
                current_row.sort(key=lambda w: w['x'])
                rows.append([w['text'] for w in current_row])
            current_row = [word]
            current_y = word['y']
        else:
            current_row.append(word)

    # Add the last row
    if current_row:
        current_row.sort(key=lambda w: w['x'])
        rows.append([w['text'] for w in current_row])

    # Write to CSV
    with open(output_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(rows)

    print(f"Table data extracted to {output_csv}")


if __name__ == '__main__':
    print(f"OCR Table Extractor - Using {WINDOWS_SDK} package\n")

    output_file = "debug/ocr/ocr_results.txt"

    try:
        if len(sys.argv) == 1:
            # No arguments, use default folder
            # folder_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Example\Example 01\outputImages\BOM"
            folder_path = r"debug/ocr/output_images/BOM"
            lang = 'en-US'  # Default to English

            print(f"No arguments provided. Using default settings:")
            print(f"  Folder: {folder_path}")
            print(f"  Language: {lang}")
            print(f"  Output file: {output_file}")

            # Make sure the folder exists
            if not os.path.exists(folder_path):
                print(f"ERROR: Default folder not found: {folder_path}")
                print("Please provide a valid folder path as an argument.")
                sys.exit(1)

            results = process_folder(folder_path, lang, output_file)

            # Try to extract tables from each result
            if results:
                print("\nAttempting to extract table data...")
                for image_name, result in results.items():
                    csv_file = os.path.join(folder_path, os.path.splitext(image_name)[0] + "_table.csv")
                    try:
                        extract_table_to_csv(result, csv_file)
                    except Exception as e:
                        print(f"Error extracting table from {image_name}: {str(e)}")

        elif 2 <= len(sys.argv) <= 4:
            # Process arguments
            if len(sys.argv) == 2:
                # Just folder provided
                folder_path = sys.argv[1]
                lang = 'en-US'
            elif len(sys.argv) == 3:
                # Folder and language provided
                folder_path = sys.argv[1]
                lang = sys.argv[2]
            else:
                # All arguments provided
                folder_path = sys.argv[1]
                lang = sys.argv[2]
                output_file = sys.argv[3]

            results = process_folder(folder_path, lang, output_file)

            # Try to extract tables
            if results:
                print("\nAttempting to extract table data...")
                for image_name, result in results.items():
                    csv_file = os.path.join(folder_path, os.path.splitext(image_name)[0] + "_table.csv")
                    try:
                        extract_table_to_csv(result, csv_file)
                    except Exception as e:
                        print(f"Error extracting table from {image_name}: {str(e)}")

        else:
            print('usage: %s [folder_path] [language=en-US] [output_file=ocr_results.txt]' % sys.argv[0])
            # Show available languages
            try:
                langs = list(map(lambda x: x.language_tag, OcrEngine.get_available_recognizer_languages()))
                print('installed languages:', ', '.join(langs))
            except Exception as e:
                print(f"Error getting available languages: {str(e)}")

    except Exception as e:
        print(f"ERROR: {str(e)}")
        print("\nPlease ensure you have installed the required packages:")
        print("  pip install winsdk  # Recommended")
        print("  pip install Pillow")
        print("\nIf you're still having issues, try the older package:")
        print("  pip install winrt")
