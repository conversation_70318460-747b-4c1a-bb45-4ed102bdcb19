# Data Uploads Module

A simple, reliable, and modular system for preparing and uploading data to PostgreSQL tables. This module replaces the complex existing mapping systems with straightforward, easy-to-maintain tools.

## Overview

This module provides three main components:

1. **Field Mapping** - Simple SQLite to PostgreSQL column name mapping
2. **Data Auditing** - Data quality validation and issue detection  
3. **Data Preparation** - Data cleaning and formatting for uploads

## Quick Start

```python
from data_uploads import prepare_for_upload, quick_audit

# Prepare data for upload (does everything)
prepared_df, report = prepare_for_upload(df, 'general')

# Quick audit only
audit_results = quick_audit(df, 'bom')
```

## Field Mapping

### Simple Key-Value Mappings

The field mapping uses simple dictionaries with `'sqlite_name': 'postgresql_name'` pairs:

```python
from data_uploads.field_mapping import map_dataframe_columns, map_column_name

# Map all columns in a DataFrame
mapped_df, unmapped_columns = map_dataframe_columns(df, 'general')

# Map a single column name
pg_name = map_column_name('bom', 'componentCategory')  # Returns 'component_category'
```

### Supported Tables

- **general** - General table mappings (measurements, coordinates, etc.)
- **bom** - Bill of Materials mappings  
- **rfq** - RFQ table mappings

### Common Mappings

```python
# Measurement abbreviations
'lf' -> 'length'
'sf' -> 'calculated_area' 
'ef' -> 'calculated_eq_length'

# CamelCase to snake_case
'lineNumber' -> 'line_number'
'materialDescription' -> 'material_description'
'componentCategory' -> 'component_category'
```

## Data Auditing

### Quick Audit

```python
from data_uploads import quick_audit

audit_results = quick_audit(df, 'general')
print(f"Issues found: {len(audit_results['issues'])}")
print(f"Warnings: {len(audit_results['warnings'])}")
```

### Detailed Audit

```python
from data_uploads import DataAuditor

auditor = DataAuditor('bom')
results = auditor.audit_dataframe(df)
auditor.print_audit_report()

# Get cleaned DataFrame
clean_df = auditor.get_clean_dataframe(df, remove_duplicates=True)
```

### What Gets Audited

- **Required fields** - Checks for missing required columns
- **Data types** - Validates expected data types
- **Null values** - Identifies null values in required fields
- **Duplicates** - Finds duplicate rows
- **Suspicious values** - Detects negative quantities, outliers, etc.
- **Text issues** - Finds unusually long strings, special characters

## Data Preparation

### Full Preparation

```python
from data_uploads import DataPreparator

prep = DataPreparator('general')
prepared_df, report = prep.prepare_dataframe(
    df, 
    map_columns=True,    # Map SQLite to PostgreSQL names
    clean_data=True,     # Clean and fix data issues
    validate_data=True   # Run full audit
)

prep.print_preparation_report(report)

# Check if ready for upload
is_valid, issues = prep.validate_for_upload(prepared_df)
```

### What Gets Cleaned

- **Empty rows** - Removes completely empty rows
- **Duplicates** - Removes duplicate rows
- **Text cleaning** - Strips whitespace, fixes encoding
- **Numeric conversion** - Converts text to numbers where expected
- **Default values** - Fills missing values with sensible defaults

## Usage Examples

### Example 1: Basic Upload Preparation

```python
import pandas as pd
from data_uploads import prepare_for_upload

# Load your data
df = pd.read_excel('my_data.xlsx')

# Prepare for upload to general table
prepared_df, report = prepare_for_upload(df, 'general')

# Check the report
if not report['issues_found']:
    print("✅ Data ready for upload!")
    # Upload prepared_df to PostgreSQL
else:
    print("❌ Issues found:")
    for issue in report['issues_found']:
        print(f"  - {issue}")
```

### Example 2: Column Mapping Only

```python
from data_uploads.field_mapping import map_dataframe_columns

# Just map column names without other processing
mapped_df, unmapped = map_dataframe_columns(df, 'bom')

if unmapped:
    print(f"Unmapped columns: {unmapped}")
```

### Example 3: Custom Audit

```python
from data_uploads import DataAuditor

auditor = DataAuditor('rfq')
results = auditor.audit_dataframe(df)

# Print detailed report
auditor.print_audit_report()

# Get summary stats
summary = results['summary']
print(f"Memory usage: {summary['memory_usage_mb']:.2f} MB")
print(f"Null percentage: {summary['null_percentage']:.2f}%")
```

## Configuration

### Adding New Mappings

To add new field mappings, edit the dictionaries in `field_mapping.py`:

```python
GENERAL_TABLE_MAPPING = {
    'old_sqlite_name': 'new_postgresql_name',
    'another_field': 'mapped_field_name',
    # Add your mappings here
}
```

### Customizing Default Values

Edit the `default_values` in `data_prep.py`:

```python
self.default_values = {
    'general': {
        'length': 0.0,
        'calculated_area': 0.0,
        # Add your defaults here
    }
}
```

### Required Fields

Modify `required_fields` in `data_auditor.py`:

```python
self.required_fields = {
    'general': ['project_id', 'pdf_id'],
    'bom': ['project_id', 'pdf_id', 'material_description'],
    # Add your requirements here
}
```

## Error Handling

The module provides clear error messages and warnings:

- **Critical Issues** - Block upload (missing required fields, wrong data types)
- **Warnings** - Don't block upload but should be reviewed (duplicates, outliers)

## Integration

This module is designed to work with your existing PostgreSQL upload pipeline:

```python
from data_uploads import prepare_for_upload
from your_upload_module import upload_to_postgresql

# Prepare data
prepared_df, report = prepare_for_upload(df, 'general')

# Check if ready
if not report['issues_found']:
    # Upload using your existing method
    upload_to_postgresql(prepared_df, 'general_table')
else:
    print("Fix issues before uploading")
```

## Benefits Over Existing System

1. **Simple** - Clear key-value mappings instead of complex nested logic
2. **Reliable** - Focused functionality with comprehensive testing
3. **Modular** - Use only what you need (mapping, auditing, or preparation)
4. **Maintainable** - Easy to add new mappings and modify behavior
5. **Transparent** - Clear reports on what was changed and why

## Files

- `field_mapping.py` - Column name mapping dictionaries and functions
- `data_auditor.py` - Data quality auditing and validation
- `data_prep.py` - Data cleaning and preparation utilities
- `__init__.py` - Package initialization and convenience imports
- `README.md` - This documentation file
