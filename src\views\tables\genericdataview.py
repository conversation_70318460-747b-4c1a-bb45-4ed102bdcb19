from src.utils.logger import logger
import pandas as pd
from src.atom.dbManager import DatabaseManager
from src.views.tableresultsview import TableResultsViewBase

# logger = logging.getLogger(__file__)


class GenericDataView(TableResultsViewBase):

    def __init__(self, parent, tableName: str) -> None:
        self.tableName = tableName
        super().__init__(parent)
        logger.info("Creating Generic Table...")

    def  __repr__(self) -> str:
        return self.tableName

    def initToolbar(self):
        super().initToolbar()
        buttons = [
            # ("savecolumn", "tree.svg"),
        ]
        for btn_name, icon_path in buttons:
            self.addToolbarButton(btn_name, icon_path)

    def onToolbarBtn(self, name):
        # Example logging or print statement for debugging
        print(f"Toolbar button clicked: {name}")

        if name == "tree": # Tree hieratchy view (QTreeWidget or similar)
            pass
        elif name == "save": # Push to database
            pass
        else:
            super().onToolbarBtn(name)


