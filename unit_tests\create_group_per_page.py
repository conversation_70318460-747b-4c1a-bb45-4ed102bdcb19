
import json

"""
Creates 1 group per page. Use this for scanned documents that are skewed 
until we determine a better method for dynamically adjusting. RAG Operations Possibly?
Input: Ungrouped ROI json file
"""

import json

def split_pages_into_groups(input_json, groups_to_split=None):
    # Load the input JSON
    with open(input_json, "r") as file:
        data = json.load(file)
    
    # Create new structure for groups
    new_groups = {}
    
    for group_id, group_data in data["groups"].items():
        if groups_to_split is None or group_id in groups_to_split:
            # Split each page into its own group
            for page_entry in group_data["pages"]:
                page_number = page_entry["page"]
                new_groups[str(page_number)] = {
                    "pages": [{"page": page_number}],
                    "rois": group_data["rois"]  # Keep the same ROIs for each page
                }
        else:
            # Keep the group as is
            new_groups[group_id] = group_data
    
    # Replace the groups in the original structure
    data["groups"] = new_groups
    
    return data


if __name__ == '__main__':

    # Example usage
    input_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 026\Data\w.json" 
    groups_to_split = ["1"]  # Specify which groups to split, or None to split all
    output_data = split_pages_into_groups(input_file,groups_to_split)
    

    # Save to a new file
    with open(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 026\Data\roi_split.json", "w") as outfile:
        json.dump(output_data, outfile, indent=4)

