"""
Example of multiprocessing with clean cancellation handling.
Features:
1. Process<PERSON>oolExecutor for parallel processing
2. Proper cleanup of worker processes
3. Graceful cancellation with timeout
4. Progress tracking
5. Signal handling for Ctrl+C
"""
import time
import signal
import threading
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Dict, List, Optional
from datetime import datetime
from multiprocessing import Value

# Shared counter for completed items
class Counter:
    def __init__(self, init_val=0):
        self.val = Value('i', init_val)

    def increment(self):
        with self.val.get_lock():
            self.val.value += 1

    @property
    def value(self):
        return self.val.value

class ProcessManager:
    def __init__(self, num_workers: Optional[int] = None):
        self.num_workers = num_workers or multiprocessing.cpu_count()
        self.total_items = 0
        self.completed_counter = Counter()
        self.error_counter = Counter()
        self.start_time = None
        self.is_cancelled = False
        self._executor = None
        self._futures = []
        self._stop_status_thread = threading.Event()
        self.results = {}
        
        # Set up signal handling
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle interrupt signals by initiating cancellation"""
        print("\nReceived interrupt signal. Initiating graceful shutdown...")
        self.cancel()

    def _status_monitor(self):
        """Monitor and print processing status"""
        while not self._stop_status_thread.is_set():
            if self.start_time:
                completed = self.completed_counter.value
                elapsed = time.time() - self.start_time
                
                if completed > 0:
                    time_per_item = elapsed / completed
                    remaining_items = self.total_items - completed
                    eta = time_per_item * remaining_items
                    eta_str = f"ETA: {eta:.1f}s"
                else:
                    eta_str = "ETA: calculating..."

                percent = (completed / self.total_items) * 100 if self.total_items else 0
                
                print(f"\rProgress: {percent:.1f}% "
                      f"({completed}/{self.total_items}) "
                      f"Elapsed: {elapsed:.1f}s {eta_str}", 
                      end="", flush=True)
            time.sleep(0.5)
        print()  # New line after monitoring stops

    @staticmethod
    def _process_item(item: int) -> Dict:
        """Example processing function - replace with your actual work"""
        try:
            # Simulate varying workload
            process_time = (item % 3 + 1) * 0.5
            time.sleep(process_time)
            
            # Simulate occasional errors
            if item % 7 == 0:
                raise ValueError(f"Simulated error processing item {item}")
                
            result = {
                "item": item,
                "status": "completed",
                "process_time": process_time,
                "timestamp": datetime.now().isoformat(),
                "worker": multiprocessing.current_process().name
            }
            return result
            
        except Exception as e:
            return {
                "item": item,
                "status": "error",
                "error": str(e)
            }

    def process_items(self, items: List[int], show_progress: bool = True) -> Dict:
        """Process items in parallel with proper cancellation handling"""
        self.total_items = len(items)
        self.start_time = time.time()
        self.is_cancelled = False
        
        if show_progress:
            status_thread = threading.Thread(target=self._status_monitor)
            status_thread.start()

        try:
            with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
                self._executor = executor
                
                # Submit all jobs
                futures_to_items = {
                    executor.submit(self._process_item, item): item 
                    for item in items
                }
                self._futures = list(futures_to_items.keys())
                
                # Process results as they complete
                for future in as_completed(self._futures):
                    if self.is_cancelled:
                        break
                        
                    try:
                        result = future.result(timeout=1)
                        item = futures_to_items[future]
                        self.results[item] = result
                        
                        if result["status"] == "error":
                            self.error_counter.increment()
                            
                        self.completed_counter.increment()
                        
                    except Exception as e:
                        self.error_counter.increment()
                        print(f"\nError processing item: {str(e)}")

        finally:
            if show_progress:
                self._stop_status_thread.set()
                status_thread.join()

            if self.is_cancelled:
                print("\nProcessing cancelled. Cleaning up...")
            
            # Final status
            elapsed = time.time() - self.start_time
            print(f"\nProcessed {self.completed_counter.value}/{self.total_items} items "
                  f"in {elapsed:.1f}s with {self.error_counter.value} errors")
            
        return self.results

    def cancel(self):
        """Cancel ongoing processing"""
        self.is_cancelled = True
        if self._executor:
            for future in self._futures:
                future.cancel()

def main():
    """Example usage of ProcessManager"""
    # Create some test items
    items = list(range(20))
    
    # Initialize process manager
    manager = ProcessManager()
    
    print(f"Starting processing of {len(items)} items with {manager.num_workers} workers")
    print("Press Ctrl+C to cancel processing")
    
    try:
        # Process items
        results = manager.process_items(items)
        
        # Analyze results
        completed = sum(1 for r in results.values() if r["status"] == "completed")
        errors = sum(1 for r in results.values() if r["status"] == "error")
        cancelled = sum(1 for r in results.values() if r["status"] == "cancelled")
        
        print(f"\nResults Summary:")
        print(f"Completed: {completed}")
        print(f"Errors: {errors}")
        print(f"Cancelled: {cancelled}")
        
        # Print some example results
        print("\nExample Results:")
        for item_id in sorted(results.keys())[:3]:
            print(f"Item {item_id}: {results[item_id]}")
            
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user")
    except Exception as e:
        print(f"\nError during processing: {str(e)}")

if __name__ == "__main__":
    main()
