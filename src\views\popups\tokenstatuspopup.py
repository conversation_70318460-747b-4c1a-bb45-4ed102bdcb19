from pubsub import pub

from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *

from src.utils.logger import logger
from src.pyside_util import get_resource_qicon, applyDropShadowEffect


# logger = logging.getLogger()


class TokenStatusPopup(QWidget):

    def __init__(self, parent):
        super().__init__(parent)

        self.data = {}  # Cache the latest details
        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(200)
        self.setMinimumHeight(64)

        widget = QWidget()
        widget.setObjectName("popup")
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())
        grid.setObjectName("popup")

        self.layout().addWidget(widget)

        self.pbGetTokens = QPushButton("Get Tokens")
        self.pbGetTokens.setFixedHeight(32)
        self.pbGetTokens.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        widget.layout().addWidget(self.pbGetTokens)
        self.pbGetTokens.clicked.connect(self.onGetTokens)

        applyDropShadowEffect(self, 12, offset=(0, 2))
        # widget.layout().setContentsMargins(0, 0, 0, 0)

    def setVisible(self, visible: bool) -> None:
        return super().setVisible(visible)

    def onUserDetailsGetResponse(self, data):
        try:
            self.nameValue.setText(data.get("username"))
            self.emailValue.setText(data.get("email"))
            self.tokenValue.setText(str(data.get("tokens")))
            self.data = data
            self.setErrorMessage("")
        except Exception as e:
            logger.info(f"Failed to sync user details {e}")

    def onGetTokens(self):
        pub.sendMessage("goto-form", name="TokenPaymentForm")
        self.hide()

    def onUserDetailsUpdateResponse(self, data: dict):
        self.setErrorMessage(data.get("error", ""))

    def setErrorMessage(self, text):
        self.lblError.setText(text)
