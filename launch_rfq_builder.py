"""
Launch Configuration for RFQ Template Builder
=============================================

This script provides different ways to launch the RFQ Template Builder:
1. GUI Interface (recommended)
2. Command Line Interface
3. Configuration file based execution

Usage:
    python launch_rfq_builder.py [options]

Options:
    --gui           Launch GUI interface (default)
    --cli           Launch command line interface
    --config FILE   Run with configuration file
    --help          Show this help message
"""

import sys
import os
import argparse
from pathlib import Path

def launch_gui():
    """Launch the GUI interface"""
    try:
        from rfq_template_builder_gui import main as gui_main
        print("🚀 Launching RFQ Template Builder GUI...")
        gui_main()
    except ImportError as e:
        print(f"❌ Error: Could not import GUI module: {e}")
        print("Make sure PySide6 is installed: pip install PySide6")
        return False
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        return False
    return True

def launch_cli():
    """Launch the command line interface"""
    try:
        from rfq_template_builder import main as cli_main
        print("🚀 Launching RFQ Template Builder CLI...")
        success = cli_main()
        return success
    except Exception as e:
        print(f"❌ Error launching CLI: {e}")
        return False

def launch_with_config(config_file):
    """Launch with configuration file"""
    try:
        import json
        from rfq_template_builder import (
            run_create_combined_workbook,
            run_material_check,
            run_normalize_descriptions,
            run_create_rfq_template,
            load_excel_first_sheet
        )
        
        # Load configuration
        if not os.path.exists(config_file):
            print(f"❌ Configuration file not found: {config_file}")
            return False
            
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"🚀 Running RFQ Template Builder with config: {config_file}")
        print("Configuration:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        print()
        
        # Validate required fields
        required_fields = ['input_path', 'output_dir']
        for field in required_fields:
            if field not in config:
                print(f"❌ Missing required field in config: {field}")
                return False
        
        # Run workflow
        current_file = config['input_path']
        df = None
        
        # Step 1: Create Combined Workbook
        if config.get('run_step1', False):
            print("Step 1: Creating Combined Workbook...")
            try:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                combined_filename = f"Stage1_Combined_{timestamp}.xlsx"
                
                # Handle multiple files if specified
                input_files = config.get('combine_files', [config['input_path']])
                
                current_file = run_create_combined_workbook(
                    input_excel_path=input_files,
                    output_dir=config['output_dir'],
                    output_filename=combined_filename,
                    combine_general=config.get('combine_general', False),
                    combine_bom=config.get('combine_bom', True),
                    combine_spec=config.get('combine_spec', False),
                    combine_rfq=config.get('combine_rfq', True)
                )
                print("✓ Step 1 completed")
            except Exception as e:
                print(f"✗ Step 1 failed: {e}")
                if not config.get('continue_on_error', True):
                    return False
        
        # Step 2: Material Check
        if config.get('run_step2', False):
            print("Step 2: Checking Material Descriptions...")
            try:
                current_file = run_material_check(
                    input_excel_path=current_file,
                    output_dir=config['output_dir'],
                    sheet_name=None
                )
                print("✓ Step 2 completed")
            except Exception as e:
                print(f"✗ Step 2 failed: {e}")
                if not config.get('continue_on_error', True):
                    return False
        
        # Step 3: Normalize Descriptions
        if config.get('run_step3', False):
            print("Step 3: Normalizing Descriptions...")
            try:
                current_file, df = run_normalize_descriptions(
                    input_excel_path=current_file,
                    output_dir=config['output_dir']
                )
                print("✓ Step 3 completed")
            except Exception as e:
                print(f"✗ Step 3 failed: {e}")
                if not config.get('continue_on_error', True):
                    return False
        
        # Step 4: Create RFQ Template
        if config.get('run_step4', False):
            print("Step 4: Creating RFQ Template...")
            try:
                if df is None:
                    df = load_excel_first_sheet(current_file)
                
                final_path = run_create_rfq_template(
                    df=df,
                    output_dir=config['output_dir'],
                    enable_validation=config.get('enable_validation', True),
                    deduplicate_materials=config.get('deduplicate_materials', True)
                )
                print("✓ Step 4 completed")
                print(f"🎉 Final RFQ Template: {final_path}")
            except Exception as e:
                print(f"✗ Step 4 failed: {e}")
                return False
        
        print("🎉 Workflow completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error running with config: {e}")
        return False

def create_sample_config():
    """Create a sample configuration file"""
    sample_config = {
        "input_path": "C:/path/to/your/input/file.xlsx",
        "output_dir": "C:/path/to/your/output/directory",
        "combine_files": [
            "C:/path/to/file1.xlsx",
            "C:/path/to/file2.xlsx"
        ],
        "run_step1": True,
        "run_step2": True,
        "run_step3": True,
        "run_step4": True,
        "combine_general": False,
        "combine_bom": True,
        "combine_spec": False,
        "combine_rfq": True,
        "enable_validation": True,
        "deduplicate_materials": True,
        "continue_on_error": True
    }
    
    config_file = "sample_rfq_config.json"
    import json
    with open(config_file, 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print(f"📄 Sample configuration created: {config_file}")
    print("Edit this file with your actual paths and settings, then run:")
    print(f"python launch_rfq_builder.py --config {config_file}")
    return config_file

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    # Check for PySide6 (for GUI)
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6 (for GUI interface)")
    
    # Check for pandas
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    # Check for xlsxwriter
    try:
        import xlsxwriter
    except ImportError:
        missing_deps.append("xlsxwriter")
    
    # Check for openpyxl
    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")
    
    if missing_deps:
        print("⚠️  Missing dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nInstall missing dependencies with:")
        print("pip install PySide6 pandas xlsxwriter openpyxl")
        return False
    
    print("✅ All dependencies are available")
    return True

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(
        description="RFQ Template Builder Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_rfq_builder.py                    # Launch GUI (default)
  python launch_rfq_builder.py --gui              # Launch GUI explicitly
  python launch_rfq_builder.py --cli              # Launch CLI
  python launch_rfq_builder.py --config my.json   # Run with config file
  python launch_rfq_builder.py --sample-config    # Create sample config
        """
    )
    
    parser.add_argument('--gui', action='store_true', 
                       help='Launch GUI interface (default)')
    parser.add_argument('--cli', action='store_true',
                       help='Launch command line interface')
    parser.add_argument('--config', type=str,
                       help='Run with configuration file')
    parser.add_argument('--sample-config', action='store_true',
                       help='Create sample configuration file')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies')
    
    args = parser.parse_args()
    
    print("RFQ Template Builder Launcher")
    print("=" * 40)
    
    # Check dependencies if requested
    if args.check_deps:
        check_dependencies()
        return
    
    # Create sample config if requested
    if args.sample_config:
        create_sample_config()
        return
    
    # Check dependencies before launching
    if not check_dependencies():
        print("\nPlease install missing dependencies before continuing.")
        return
    
    # Determine launch mode
    if args.config:
        success = launch_with_config(args.config)
    elif args.cli:
        success = launch_cli()
    elif args.gui or not any([args.config, args.cli]):
        # Default to GUI if no specific mode specified
        success = launch_gui()
    else:
        # Fallback to GUI
        success = launch_gui()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
