from multiprocessing import Pool, cpu_count
import fitz
import os

def render_page(args):
    pdf_path, page_num, output_folder, zoom, image_format = args
    doc = fitz.open(pdf_path)
    mat = fitz.Matrix(zoom, zoom)
    page = doc.load_page(page_num)
    pix = page.get_pixmap(matrix=mat, alpha=False)
    output_path = os.path.join(output_folder, f"page_{page_num + 1}.{image_format}")
    pix.save(output_path)
    doc.close()

def pdf_to_images_parallel(pdf_path, output_folder, zoom=2.0, image_format="png"):
    os.makedirs(output_folder, exist_ok=True)
    doc = fitz.open(pdf_path)
    num_pages = len(doc)
    doc.close()

    args = [(pdf_path, i, output_folder, zoom, image_format) for i in range(num_pages)]

    with Pool(processes=cpu_count()) as pool:
        pool.map(render_page, args)
