#!/usr/bin/env python3
"""
Standalone launcher for the AIS PostgreSQL Interface

This is a standalone application that launches the AIS Interface Tab
in its own window for testing and development purposes.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Import the AIS Interface Tab
from atom.pg_database.ais_interface_tab import AISInterfaceTab


class AISInterfaceWindow(QMainWindow):
    """Main window for the standalone AIS Interface application"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("AIS PostgreSQL Interface - Standalone")
        self.setGeometry(100, 100, 1600, 1000)

        # Set modern window styling
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
        """)

        # Set window icon if available
        try:
            # You can add an icon file later if desired
            pass
        except:
            pass

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create layout with no margins (the interface handles its own spacing)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create and add the AIS Interface Tab
        self.ais_interface = AISInterfaceTab()
        layout.addWidget(self.ais_interface)

        # Set window properties
        self.setMinimumSize(1200, 800)

        # Center the window on screen
        self.center_on_screen()

    def center_on_screen(self):
        """Center the window on the screen"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())


def main():
    """Main entry point for the standalone application"""
    # Create QApplication
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("AIS PostgreSQL Interface")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Architekt ATOM")

    # Create and show the main window
    window = AISInterfaceWindow()
    window.show()

    # Start the event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
