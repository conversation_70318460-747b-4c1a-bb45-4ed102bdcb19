"""
Open RFQ data from Excel file and run it through the MTO assistant
"""
import time
import pandas as pd

from src.atom import ai_classifier

# logging.getLogger().setLevel("debug")
rfq_file = "src/tests/mto/data/rfq_test_data.xlsx"

# Real data but dummy data should be fine
project = {
    'clientName': None,
    'documents': [
        {'dateAdded': '2024-07-08 13:39:20.206436',
         'documentVendor': 'cl',
         'filename': '/home/<USER>/Desktop/wetransfer_combined-oci-drawings-81148-pdf_2024-03-20_1815/Combined Linde 81213-2.pdf',
         'projectId': 2,
         'sortNumber': None}
    ],
    'id': 2,
    'projectLocation': 'cl',
    'projectName': 'cl',
    'projectNumber': 'cl',
    'projectScope': None,
    'userCompanyName': 'n/a'
}


if __name__ == "__main__":
    jobId = 1
    aiConfig = {"gptVersion": "4o-Mini"}

    df = pd.read_excel(rfq_file)

    df = df.reset_index(drop=True)
    df["__uid__"] = df.index

    print(df.head())
    # df = xl.parse("Sheet1")

    data = {
        "project": project,
        "df": df,
    }
    classifier = ai_classifier.Classifier(data, aiConfig, jobId, include_answer=False)

    def cancel():
        # Test - After timeout, cancel classification
        time.sleep(30)
        classifier.cancel()

    # Thread(target=cancel).start()

    classifier.run()

    res = classifier._result
    # print(res)

    df = pd.DataFrame(res["final_df"])
    df.to_excel("debug/classified-rfq-data-result.xlsx", index=False)
