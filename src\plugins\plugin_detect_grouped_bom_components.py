import os
import time

from pubsub import pub
from collections import defaultdict

from src.app_paths import getSourceRawDataPath

def detect_grouped_bom_components(project_source: tuple,
                                  classified_bom_file: str,
                                  output_dir: str = "debug/detected_grouped_bom_components"):
    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    print(projectId, filename)

    raw_data_file = getSourceRawDataPath(projectId, filename)
    raw_data_exists = os.path.exists(raw_data_file)
    if not raw_data_exists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, raw_data_exists)

    os.makedirs(output_dir, exist_ok=True)
    print("Created output directory", output_dir)

    import fitz

    try:
        doc = fitz.open(filename)
    except Exception as e:
        return "Failed to open PDF: " + str(e)

    raw_df = pd.read_feather(raw_data_file)

    print("Loading classified BOM")
    if not os.path.exists(classified_bom_file):
        return "Classified BOM file not found"
    df = pd.read_excel(classified_bom_file)

    # if not roi_payload:
    #     print("Loading saved ROI payload")
    #     roi_payload = getSourceExtractionOptionsPath(projectId, filename)
    #     if not os.path.exists(roi_payload):
    #         error = "A saved ROI payload not found for this project source."
    #         print(error)
    #         return error

    # roi_payload = convert_roi_payload(roi_payload, force_extract=True, ignore_bom=True)

    # cleaned_roi_payload = {
    #     "ocr": {}
    # }
    # cleaned_roi_payload['pageToGroup'] = {}
    # for page, group in roi_payload['pageToGroup'].items():
    #     cleaned_roi_payload['pageToGroup'][page] = group

    # return cleaned_roi_payload