"""
S1601 Insulation Only

Construct the BOM as best as possible with raw data

S1601 Uploaded by Brock Services

These pages have no BOM, and need data fetched from isometric areas
"""
import cv2
import fitz
import ast
import re
import math

import numpy as np
import pandas as pd
from itertools import combinations
from shapely.geometry import Polygon

from data_conversions import convert_quantity_to_float
from src.utils import convert_roi_payload
from src.app_paths import getSourceRawDataPath
from src.utils.pdf.page_to_opencv import page_to_opencv
from scripts.cv2_detection_utils import feature_detection
from scripts.detect_isometric import detect_isometric

filename = r"C:\Drawings\Joey\S1601 Insulation Only (1).pdf"
projectId = 30

page_width, page_height = [2448, 1584]
relative_isometric_area = [0.04811764705882353, 0.027676767676767676, 0.6834150326797386, 0.7986111111111112]
absolute_isometric_area = [relative_isometric_area[0] * page_width,
                           relative_isometric_area[1] * page_height,
                           relative_isometric_area[2] * page_width,
                           relative_isometric_area[3] * page_height]

CATEGORY_PIPE = "pipe"
SIZE_VALUE = "size"
CROSS_SIZE = "cross_size"
FIELD_WELD = "field_weld"
PIPE_SIZE = "pipe_size"
VALVE = "valve"
QUANTITY = "quantity"


def safe_literal_eval(coord_str):
    if isinstance(coord_str, np.ndarray):
        return tuple(coord_str)
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

ignore_terms = [
    "insul:",
    "02CG1E-M",
    "REST ON STEEL",
    "NONE",
    "N"
]

ignore_contains = [
    "REF : DETAIL",
]

ignore_startswith = [
    "E ",
    "N ",
    "EL ",
    "SEE ISO",
    "FG-"
]

bom_labels = ["1", "2", "3", "4"]


categories = [
    "bom_label",
    "pipe_size",
    "valve",
    "quantity",
    "field_weld",
    "span",
    "pipe_qty",
    "cross_size"
]

pipe_quantities = [
    # "7/16",
    # "15'5.1/2",
    # "14'11.1/2",
]


def is_isometric(row):
    return 1 if (row["x1"] >= absolute_isometric_area[0] and row["x1"] <= absolute_isometric_area[2] and
            row["y1"] >= absolute_isometric_area[1] and row["y1"] <= absolute_isometric_area[3]) else 0

def is_size_value(value):
    if not value:
        return 0
    return 1 if value.lower() in [PIPE_SIZE, CROSS_SIZE] else 0

def assign_category(value):
    if any(val.lower() == value.lower() for val in bom_labels):
        return "bom_label"

    if value.lower().endswith('"NPD'.lower()):
        if "x" in value.lower():
            return CROSS_SIZE
        else:
            return PIPE_SIZE

    if value.lower().startswith("stem"):
        return VALVE

    if '"' in value and convert_quantity_to_float(value) > 0:
        return QUANTITY

    if value.lower() == "fw":
        return FIELD_WELD

    # quantity
    return

def is_pipe_quantity(value):
    if any(val.lower() in value.lower() for val in pipe_quantities):
        return "pipe_qty"
    return "quantity"

def is_within_area(bounds, area):
    """Check if an item's bounding box is within the specified area."""
    x_min, y_min, x_max, y_max = bounds
    return (area['x0'] <= x_min <= area['x1'] and
            area['y0'] <= y_min <= area['y1'] and
            area['x0'] <= x_max <= area['x1'] and
            area['y0'] <= y_max <= area['y1'])

def get_rect_center(rect, to_int=True):
    x1, y1, x2, y2 = rect
    # print(rect)
    center = ((x1+x2)/2, (y1+y2)/2)
    if to_int:
        center = (int(center[0]), int(center[1]))
    return center

def calculate_quantity(value):
    return convert_quantity_to_float(value)

def is_ignore(value: str):
    if any(val.lower() == value.lower() for val in ignore_terms):
        return 1
    if any(val.lower() in value.lower() for val in ignore_contains):
        return 1
    if any(value.lower().startswith(val.lower()) for val in ignore_startswith):
        return 1

    return 0

def get_line_center(a, b):
    x1, y1 = a
    x2, y2 = b
    return ((x1+x2)/2, (y1+y2)/2)

def get_pipes(page):
    drawings = page.get_drawings()
    pipes = []
    for vector_id, drawing in enumerate(drawings):
        x0, y0, x1, y1 = drawing['rect']
        geometry = Polygon([(x0, y0), (x1, y0), (x1, y1), (x0, y1)])
        # Check if the item is within the isometric drawing area
        item_count = len(drawing['items'])

        rect_center = get_rect_center(drawing['rect'], to_int=True)
        area = int((x1 - x0) * (y1 - y0))

        if (12 <= item_count <= 35 and 1000 < area < 9000):
            pipes.append([x0, y0, x1, y1])

    return pipes

        # width = drawing["width"]
        # if not width:
        #     continue
        # if area > 200:
        #     continue

def safe_polygon_eval(value):
    try:
        return ast.literal_eval(value)
    except:
        return None

def get_points_along_line(x1, y1, x2, y2, number_of_points=50):
    """Generate number of points points along a line i.e. from point A (x1, y1) to B (x2, y2)"""
    xs = np.linspace(x1, x2, num=number_of_points)
    ys = np.linspace(y1, y2, num=number_of_points)
    for i in range(len(xs)):
        x = xs[i].astype(int)
        y = ys[i].astype(int)
        yield (x, y)

def extend_line(a, b, num_steps=50):
    x1, y1 = a
    x2, y2 = b

    # Direction vector
    dx = x2 - x1
    dy = y2 - y1
    length = np.hypot(dx, dy)
    if length == 0:
        return []

    # Normalize direction
    dx /= length
    dy /= length

    # Extend backwards from A
    points_before = [(int(round(x1 - i*dx)), int(round(y1 - i*dy))) for i in range(1, num_steps+1)]

    # Extend forwards from B
    points_after = [(int(round(x2 + i*dx)), int(round(y2 + i*dy))) for i in range(1, num_steps+1)]

    return points_before, points_after

def get_direction_vector(a, b):
    x1, y1 = a
    x2, y2 = b

    # Direction vector
    dx = x2 - x1
    dy = y2 - y1
    length = np.hypot(dx, dy)
    if length == 0:
        return []

    # Normalize direction
    dx /= length
    dy /= length

    return dx, dy

def extend_previous_point(point, direction_vector, step: int = 3):
    """Returns previous point in the direction of the vector"""
    x1, y1 = point
    dx, dy = direction_vector
    # Extend backwards from A
    points_before = x1 - dx*step, y1 - dy*step
    return points_before

def extend_next_point(point, direction_vector, step: int = 5):
    """Returns next point in the direction of the vector"""
    x1, y1 = point
    dx, dy = direction_vector
    # Extend backwards from A
    points_before = x1 + dx * step, y1 + dy * step
    return points_before

def non_whitespace_percentage(image, white_threshold=240):
    """
    Returns the percentage of non-white pixels in the rectangle (x1, y1, x2, y2)
    - white_threshold: pixels >= this value are considered "white"
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate mask: pixels below white_threshold are "non-white"
    non_white = image < white_threshold
    percent = (np.count_nonzero(non_white) / non_white.size) * 100

    return percent

def whitespace_percentage(image, white_threshold=240):
    """
    Returns the percentage of non-white pixels in the rectangle (x1, y1, x2, y2)
    - white_threshold: pixels >= this value are considered "white"
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate mask: pixels below white_threshold are "non-white"
    non_white = image > white_threshold
    percent = (np.count_nonzero(non_white) / non_white.size) * 100

    return percent

def get_cropped_cv2_image(page: fitz.Page, rect, zoom=2):
    """
    Extract a cropped portion from a PDF page as a cv2-compatible image.

    Arguments:
    - pdf_path: path to PDF file
    - page_number: 0-based page index
    - rect: (x0, y0, x1, y1) in page coordinates

    Returns:
    - image (numpy array) in BGR format (OpenCV-compatible)
    """
    # Define rectangle (in PDF coordinate space)
    crop_rect = fitz.Rect(rect)

    # Render just the cropped area
    mat = fitz.Matrix(zoom, zoom)  # zoom factor (increase for higher res)
    pix = page.get_pixmap(matrix=mat, clip=crop_rect, alpha=False)

    # Convert to numpy array
    img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)

    # Convert RGB to BGR for OpenCV
    if pix.n == 3:
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

    return img

def distance_to(p1, p2):
    """Returns the distance between two points."""
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])

def close_to(p1, p2, threshold=3):
    """Returns True if the distance between two points is less than the threshold."""
    return distance_to(p1, p2) < threshold

def angle_between(p1, p2):
    """Returns the angle between two points in degrees."""
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    angle_rad = np.arctan2(dy, dx)
    angle_deg = np.degrees(angle_rad)
    # return angle_deg % 180  # ignore direction, keep in [0, 180)
    return (angle_deg + 360) % 360  # ignore direction, keep in [0, 360)

def is_close_angle(a, b, tol=0.2):
    """Check if two angles are close within a tolerance i.e. if on the same orientated line"""
    return abs(a - b) < tol

def merge_groups(groups):
    """Merge overlapping groups."""
    merged = []
    for group in groups:
        group_set = set(group)
        merged_into_existing = False
        for existing in merged:
            if group_set & existing:  # overlap
                existing |= group_set
                merged_into_existing = True
                break
        if not merged_into_existing:
            merged.append(set(group))
    # Repeat until no more merges
    stable = False
    while not stable:
        stable = True
        new_merged = []
        while merged:
            current = merged.pop()
            for other in merged:
                if current & other:
                    current |= other
                    merged.remove(other)
                    stable = False
                    break
            new_merged.append(current)
        merged = new_merged
    return [sorted(list(g)) for g in merged]


def adaptive_angle_tolerance(pt1, pt2, base_tolerance=0.1, max_tolerance=2.2, scale=0.004):
    """
    Critical function. TODO - finetune this

    Adaptively adjust the angle tolerance based on the distance between points.

    For longer lines, smaller angle deviation is more significant visually. Smaller tolerance can be used (stricter)

    For shorter lines, larger angle deviation is more significant visually. Larger tolerance can be used (looser)

    Arguments:
    - pt1, pt2: points to measure distance between
    - base_tolerance: minimum tolerance (even for very long lines)
    - max_tolerance: maximum tolerance (even for very short lines)
    - scale: how much tolerance changes with distance. Larger values make tolerance decrease faster with distance

    Returns:
    - adjusted tolerance
    """
    distance = np.linalg.norm(np.array(pt2) - np.array(pt1))

    # Tolerance decreases with longer distance
    tolerance = max_tolerance - scale * distance
    print(f"distance: {distance}, tolerance: {tolerance}")  # distance, tolerance)
    return max(base_tolerance, min(max_tolerance, tolerance))


def find_maximal_collinear_groups(df, target_slopes_deg=[30, 90, 150], angle_tolerance=0.24):
    """
    Find maximal collinear groups of points in the DataFrame based on target slopes.

    90 vertical, 30, 150 diagonal

    Returns a dictionary where keys are slopes and values are lists of sets of indices.
    """
    points = list(zip(df['center_x'], df['center_y']))
    n = len(points)
    groups_by_slope = {slope: [] for slope in target_slopes_deg}

    for i, j in combinations(range(n), 2):
        p1, p2 = points[i], points[j]
        angle = angle_between(p1, p2)
        for slope in target_slopes_deg:
            tolerance = adaptive_angle_tolerance(p1, p2)
            if is_close_angle(angle, slope, tol=tolerance):
            # if is_close_angle(angle, slope, tol=angle_tolerance):
                # Start or extend a group with i, j
                matched = False
                for group in groups_by_slope[slope]:
                    if i in group or j in group:
                        group.update([i, j])
                        matched = True
                        break
                if not matched:
                    groups_by_slope[slope].append(set([i, j]))
                break  # don't match this pair to multiple slopes

    # Merge overlapping groups per slope
    for slope in groups_by_slope:
        groups_by_slope[slope] = merge_groups(groups_by_slope[slope])

    return groups_by_slope

def best_fit_line_segment(points):
    """Return a best fit line given a set of points."""
    points = np.array(points)
    x = points[:, 0]
    y = points[:, 1]

    # Center the points (helps with numerical stability)
    centroid = np.mean(points, axis=0)
    centered = points - centroid

    # Use PCA-like method to find direction vector
    _, _, vh = np.linalg.svd(centered)
    direction = vh[0]  # principal axis

    # Project points onto the direction vector
    projections = centered @ direction

    # Find extreme projections
    t_min = projections.min()
    t_max = projections.max()

    # Reconstruct endpoints in original space
    point_a = centroid + t_min * direction
    point_b = centroid + t_max * direction

    return tuple(point_a), tuple(point_b)

def get_arrowed_lines(img: np.ndarray):
    # Unused but may be useful
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)

    # === 2. Detect lines ===
    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=50,
                            minLineLength=40, maxLineGap=5)

    # === 3. Annotate and visualize lines ===
    annotated_img = img.copy()
    arrow_points = []

    if lines is not None:
        for i, line in enumerate(lines):
            x1, y1, x2, y2 = line[0]

            if not (x1 >= absolute_isometric_area[0] and x1 <= absolute_isometric_area[2] and
                y1 >= absolute_isometric_area[1] and y1 <= absolute_isometric_area[3]):
                continue

            # Compute angle of line
            dx, dy = x2 - x1, y2 - y1
            angle = math.degrees(math.atan2(dy, dx))

            # Draw arrow line
            cv2.arrowedLine(annotated_img, (x1, y1), (x2, y2), (0, 0, 255), 2, tipLength=0.1)

            # Annotate with index and angle
            mid_x, mid_y = (x1 + x2) // 2, (y1 + y2) // 2
            cv2.putText(annotated_img, f"{i} ({int(angle)}°)", (mid_x, mid_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            arrow_points.append(((x1, y1), (x2, y2), angle))

    annotated_img_rgb = cv2.cvtColor(annotated_img, cv2.COLOR_BGR2RGB)
    # cv2.imshow("features", annotated_img_rgb)
    # cv2.waitKey(0)
    return arrow_points

    df = pd.DataFrame(arrow_points, columns=["start", "end", "angle"])
    # df.to_csv("debug/detected_arrows.csv", index=False)
    return df
    # cv2.imwrite("debug/annotated_arrows.png", annotated_img_rgb)


def is_visible_between(img, pt1, pt2, mean_threshold=20, visibility_threshold=0.2):
    """
    Check if the line segment between two points is visible.
    This function samples the line segment and checks the mean value of the pixels.

    Args:
        img: Grayscale image
        pt1: First point
        pt2: Second point
        mean_threshold: Mean value threshold for considering a pixel visible
        visibility_threshold: Threshold for considering the line segment visible

    Returns:
        True if the line segment is visible, False otherwise
    """
    # Line iterator: Bresenham-style sampling
    line_img = np.zeros_like(img, dtype=np.uint8)
    cv2.line(line_img, pt1, pt2, 255, 1)
    ys, xs = np.where(line_img == 255)

    values = img[ys, xs]
    visible = np.mean(values > mean_threshold)  # adjust based on your contrast
    return visible > visibility_threshold

def split_visible_segments(points, img, mean_threshold=20, visibility_threshold=0.2):
    """
    Detects line segment breaks and returns a list of visible segments.
    This function assumes that the points are ordered along the line.
    It uses a visibility threshold to determine if a segment is visible.

    Args:
        points: List of (x, y) points
        img: Grayscale image
        mean_threshold: Mean value threshold for considering a pixel visible. Higher values are more strict.
        visibility_threshold: Threshold for considering a segment visible. Higher values are more strict.

    Returns:
        List of lists of visible segments
    """
    segments = []
    current_segment = [points[0]]

    for i in range(1, len(points)):
        pt_prev = tuple(map(int, points[i - 1]))
        pt_curr = tuple(map(int, points[i]))

        if is_visible_between(img, pt_prev, pt_curr, mean_threshold, visibility_threshold):
            current_segment.append(points[i])
        else:
            if len(current_segment) >= 2:
                segments.append(current_segment)
            current_segment = [points[i]]

    if len(current_segment) >= 2:
        segments.append(current_segment)

    return segments


def analyze_features(features_df: pd.DataFrame,
                     cv_img: np.ndarray):

    angle_checks = [30, 90, 150]

    cv_img = cv_img.copy()
    for row in features_df.itertuples():
        center_x = row.center_x
        center_y = row.center_y
        cv2.circle(cv_img, (int(center_x), int(center_y)), 3, (0, 0, 255), -1)
    # cv2.imshow("features", features_img)
    # cv2.waitKey(0)
    # return

    features_df.reset_index(drop=True, inplace=True)

    unique_angles = []
    checks = []

    gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0) # optional for noise
    edge_image = cv2.Canny(blurred, 50, 150) # create edge map

    line_groups = find_maximal_collinear_groups(features_df, [30, 90, 150], angle_tolerance=0.3)

    cv_img2 = cv_img.copy()
    line_results = []
    for angle, lines in line_groups.items():
        for line in lines:
            if not line:
                continue
            points = []
            for index in line:
                point = features_df.loc[index, ["center_x", "center_y"]].values
                points.append(tuple(point))

            # original
            segment = best_fit_line_segment(points)
            segment_a = tuple(map(int, segment[0]))
            segment_b = tuple(map(int, segment[1]))
            cv2.line(cv_img2, segment_a, segment_b, (0, 0, 255), 1)

            # Some parts of the segment are deemed connected even if there is no actual connection
            # on the image
            segments = split_visible_segments(points, edge_image, mean_threshold=10, visibility_threshold=0.25)
            for segment in segments:
                segment_line = best_fit_line_segment(segment)
                segment_a = tuple(map(int, segment_line[0]))
                segment_b = tuple(map(int, segment_line[1]))
                line_results.append({"type": "line", "x1": segment_a[0], "y1": segment_a[1], "x2": segment_b[0], "y2": segment_b[1], "angle": angle, "points": segment})
                cv2.line(cv_img2, segment_a, segment_b, (0, 255, 0), 2)

            for point in points:
                x, y = point
                cv2.circle(cv_img2, (int(x), int(y)), 3, (255, 0, 255), -1)

    line_results = pd.DataFrame(line_results)
    print(line_results.head(10))

    print("TODO - assign values to lines")

    # cv2.imshow("features", cv_img2)
    # cv2.waitKey(0)
    return {
        "debug_image": cv_img2,
        "results": pd.DataFrame(line_results)
    }

    # Experimental code below for debugging

    # return

    all_line_checks = []
    ignore = set()

    def display_relative_angles(df, ref_index = None):
        # debug function
        # Displays the reference point and all the relative angles to the other points
        img = cv_img.copy()
        for idx, row in df.iterrows():
            angle_deg = int(row.relative_angle)
            if ref_index != idx:
                cv2.putText(img, f"{idx},({angle_deg})", (int(row.center_x), int(row.center_y)), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 200, 0), 1)
            else:
                cv2.circle(img, (int(row.center_x), int(row.center_y)), 5, (255, 0, 255), -1)

        cv2.imshow("features", img)
        cv2.waitKey(0)

    for idx, row in features_df.iterrows():
        dx = features_df['center_x'] - row['center_x']
        dy = features_df['center_y'] - row['center_y']

        angle_rad = np.arctan2(dy, dx)
        angle_deg = np.degrees(angle_rad)
        relative_angle = (angle_deg + 180) % 360 - 180
        features_df["relative_angle"] = relative_angle

        angle_distance = np.abs(relative_angle)
        features_df["angle_distance"] = angle_distance

        cv_img2 = cv_img.copy()
        cv2.circle(cv_img2, (int(row.center_x), int(row.center_y)), 5, (255, 0, 255), -1)

        checks = {30: [idx], 90: [idx]}

        # if idx in [4, 7, 11, 13, 18, 20, 23, 25]:
        # if idx == 4:
        display_relative_angles(features_df, idx)
        continue

        # Check other points with identical relative angles
        for idx2, row2 in features_df.iterrows():
            if idx == idx2:
                continue

            if idx == 4 and idx2 == 25:
                cv2.circle(cv_img2, (int(row.center_x), int(row.center_y)), 5, (255, 0, 255), -1)
                cv2.circle(cv_img2, (int(row2.center_x), int(row2.center_y)), 5, (255, 0, 255), -1)
                # cv2.imshow("features", cv_img2)
                # cv2.waitKey(0)
            degrees = row2.relative_angle
            dist = min(abs(degrees), abs(degrees))

            # Angle matching logic
            for angle_check in angle_checks:
                if abs(dist - angle_check) > 0.2:
                    continue
                checks[angle_check].append(idx2)

        for angle, indexes in checks.items():
            # Skip if only one point
            if len(indexes) == 1:
                continue
            # Check if we need to create a new line check or update the set of existing line points
            for line_checks in all_line_checks:
                if line_checks["angle"] != angle:
                    continue
                if idx in line_checks["points"]:
                    if 5 in indexes and angle == 30:
                        print("Found 5")
                    # Update existing line check
                    line_checks["points"].update(indexes)
                    break
            else:
                all_line_checks.append({"angle": angle, "points": set(indexes)})

        print(idx, all_line_checks)
        pass


    print(all_line_checks)

    for n, line_check in enumerate(all_line_checks):
        print("checking line check", n, len(line_check["points"]), line_check["points"])
        cv_img2 = cv_img.copy()
        # Convert indexes to points
        for index in line_check["points"]:
            pos = features_df.loc[index, "center_x"], features_df.loc[index, "center_y"]
            cv2.circle(cv_img2, (int(pos[0]), int(pos[1])), 2, (255, 0, 255), 2)
            cv2.putText(cv_img2, str(index), (int(pos[0])+1, int(pos[1])), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 200, 0), 2)
            print(index, pos)
        cv2.imshow("features", cv_img2)
        cv2.waitKey(0)


    detected_lines = []

    # Convert indices to points
    line_points = []
    for angle, indexes in all_line_checks.items():
        for idx in indexes:
            center_x = features_df.loc[idx, "center_x"]
            center_y = features_df.loc[idx, "center_y"]
            pos = center_x, center_y
            line_points.append({"pos": pos, "angle": angle, "checked": False, "center_x": center_x, "center_y": center_y})

    line_points = pd.DataFrame(line_points)

    limit = 500

    rect_configs = {
        "diagonal": {
            "width": 3,
            "height": 3,
            "y_offset": 0,
            "check_limit": 200
        },
        "vertical": {
            "width": 2,
            "height": 3,
            "y_offset": 0,
            "check_limit": 200
        }
    }
    whitespace_limit = 3 # Number of empty whitespace checks before aborting
    whitespace_thresh = 0.1

    checked_points = {}
    lines_detected = []

    line_points.sort_values(by="center_x", ascending=True, inplace=True)

    for row in line_points.itertuples():
        if row.checked:
            continue
        points_checked = []

        direction = None
        if abs(row.angle) == 90:
            direction = "vertical"
            continue
        else:
            direction = "diagonal"

        rect_config = rect_configs[direction]

        pos = row.pos
        point = int(pos[0]), int(pos[1])
        angle = row.angle
        theta_rad = math.radians(-angle)  # convert to radians
        direction_vector = (math.cos(theta_rad), math.sin(theta_rad))

        whitespace_count = 0

        # Now check points along the along for line detection.
        # Remove any points on the same check
        group_points = line_points[line_points["angle"] == angle]
        for index, row in group_points.iterrows():
            point2 = row.pos
            # Ignore same angle point if close
            if close_to(point, point2, threshold=5):
                line_points.loc[index, "checked"] = True
                break

            if relative_angle == 30:
                print("30")


    cv2.namedWindow("main", cv2.WINDOW_NORMAL)
    cv2.imshow("main", cv_img2)
    cv2.waitKey(0)
    print(unique_angles)
    return features_df

def draw_table(df, img=None, cell_height=40, font=cv2.FONT_HERSHEY_SIMPLEX, font_scale=0.5, thickness=1,
               align='top-right', margin=(40, 40), max_table_width=None):
    """
    Draws a pandas DataFrame as a table on an OpenCV image.

    Args:
        df (pd.DataFrame): The DataFrame to render.
        img (np.ndarray or None): Existing image to draw on. If None, a new white image is created.
        cell_height (int): Height of each cell.
        font (int): OpenCV font type.
        font_scale (float): Font scale.
        thickness (int): Thickness of the font.
        align (str): 'top-left', 'top-right', or 'center'.
        margin (tuple): (margin_x, margin_y) in pixels.
        max_table_width (int or None): Maximum width for the table in pixels. Columns will shrink proportionally if needed.

    Returns:
        np.ndarray: The image with the drawn table.
    """
    # Helper to calculate text width
    def get_text_size(text):
        (w, h), _ = cv2.getTextSize(str(text), font, font_scale, thickness)
        return w

    # Calculate column widths dynamically
    col_widths = []
    index_width = max(get_text_size(str(idx)) for idx in df.index)
    col_widths.append(index_width + 20)  # padding

    for col in df.columns:
        max_content_width = max(get_text_size(val) for val in df[col])
        header_width = get_text_size(col)
        col_widths.append(max(max_content_width, header_width) + 20)  # padding

    table_width = sum(col_widths)
    table_height = cell_height * (df.shape[0] + 1)

    # Handle max_table_width
    if max_table_width and table_width > max_table_width:
        scale = max_table_width / table_width
        col_widths = [int(w * scale) for w in col_widths]
        table_width = sum(col_widths)

    # Create new image if none provided
    if img is None:
        img_width = table_width + 2 * margin[0]
        img_height = table_height + 2 * margin[1]
        img = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255
    else:
        img_height, img_width = img.shape[:2]

    # Determine start_x based on alignment
    if align == 'top-left':
        start_x = margin[0]
    elif align == 'top-right':
        start_x = img_width - table_width - margin[0]
    elif align == 'center':
        start_x = (img_width - table_width) // 2
    else:
        raise ValueError("align must be 'top-left', 'top-right', or 'center'.")

    start_y = margin[1]

    # Draw the table
    y = start_y
    for row_idx in range(df.shape[0] + 1):  # +1 for header
        x = start_x
        for col_idx in range(len(col_widths)):
            w = col_widths[col_idx]
            # Draw rectangle
            cv2.rectangle(img, (x, y), (x + w, y + cell_height), (0, 0, 0), 1)
            # Put text
            if row_idx == 0:
                # Header
                text = str(df.columns[col_idx - 1]) if col_idx > 0 else ""
            else:
                if col_idx == 0:
                    text = str(df.index[row_idx - 1])
                else:
                    text = str(df.iloc[row_idx - 1, col_idx - 1])

            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = x + 5  # small padding
            text_y = y + (cell_height + text_size[1]) // 2
            cv2.putText(img, text, (text_x, text_y), font, font_scale, (0, 0, 0), thickness, cv2.LINE_AA)

            x += w
        y += cell_height

    return img

def preprocess_raw_data(df: pd.DataFrame,
                        isometric_area: list[float] = None,
                        pages_to_process: list[int] = None,
                        min_span_level: int = 30) -> pd.DataFrame:
    """
    Args:
        df: Raw data DataFrame
        isometric_area: List of coordinates defining the isometric area
        pages_to_process
        min_span_level: Minimum quantity to consider as a span. Use very large value to prevent span detection

    Returns:
        Preprocessed DataFrame
    """
    df = df[df["pdf_page"].isin(pages_to_process)]

    df["polygon"] = df["polygon"].apply(safe_polygon_eval)
    df["x1"], df["y1"], df["x2"], df["y2"] = zip(*df["coordinates2"])
    df["bbox_width"] = df["x2"] - df["x1"]
    df["bbox_height"] = df["y2"] - df["y1"]
    df["bbox_area"] = df["bbox_width"] * df["bbox_height"]

    # Apply the isometric function to determine which data is inside the isometric area
    # TODO - account for each page dimension
    if isometric_area:
        df["inside_isometric"] = df.apply(is_isometric, axis=1)
        df = df[df["inside_isometric"] == 1]

    # Check pre defined quantities
    # df.loc[df["category"] == "quantity", "category"] = df["value"].apply(is_pipe_quantity)

    # Perform some categorization of isometric data
    df["ignore"] = df["value"].apply(is_ignore)
    df = df[df["ignore"] == 0]

    df["category"] = df["value"].apply(assign_category)
    df.loc[df["category"] == "quantity", "quantity"] = df["value"].apply(calculate_quantity)

    # Larger quantities auto assigned as spans
    df.loc[df["quantity"] > min_span_level, "category"] = "span"

    df["is_size_value"] = df["category"].apply(is_size_value)

    return df

def parse_page_range(page_range_str, total_pages):
    """Parse page range string into list of page numbers (1-based)"""
    if type(page_range_str) == int:
        return [page_range_str]
    elif type(page_range_str) == list:
        return page_range_str
    elif page_range_str is None:
        return list(range(1, total_pages + 1))
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return list(range(1, total_pages + 1))

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            end = min(total_pages, end)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            if 1 <= page <= total_pages:
                pages.append(page)

    return sorted(set(pages))

def line_length(line):
    x1, y1, x2, y2 = line
    return np.hypot(x2 - x1, y2 - y1)

def get_line_segments(img: np.ndarray):
    """Targets findings line segments belonging to arrow pointers"""
    # Make everything outside of isometric area black
    masked = np.zeros_like(img)
    image_width = img.shape[1]
    image_height = img.shape[0]
    x1, y1, x2, y2 = relative_isometric_area
    x1 = int(x1) * image_width
    y1 = int(y1) * image_height
    x2 = int(x2) * image_width
    y2 = int(y2) * image_height
    masked[y1:y2, x1:x2] = img[y1:y2, x1:x2]
    # img = masked

    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    lsd = cv2.createLineSegmentDetector(refine=cv2.LSD_REFINE_NONE)

    # Detect lines
    lines = lsd.detect(gray)[0]  # Position 0 has the lines

    lines = [line[0] for line in lines]

    import random
    def random_color(min_val=100, max_val=255):
        return (random.randint(min_val, max_val),
                random.randint(min_val, max_val),
                random.randint(min_val, max_val))

    line_data = []
    # Draw detected lines
    for line in lines:
        # color = random_color()
        # cv2.line(cv_img, (int(line[0]), int(line[1])), (int(line[2]), int(line[3])), color, zoom)
        a, b = (line[0], line[1]), (line[2], line[3])
        d = {
            # 'pdf_page': page_num + 1,
            'x1': line[0] / image_width,
            'y1': line[1] / image_height,
            'x2': line[2] / image_width,
            'y2': line[3] / image_height,
            'angle': angle_between(a, b),
            'distance': line_length(line)
        }
        d["start_point"] = (d['x1'], d['y1'])
        d["end_point"] = (d['x2'], d['y2'])

        orientation = None
        d["orientation"] = orientation

        line_data.append(d)

    # cv = lsd.drawSegments(img, lines)

    for line in line_data:
        color = random_color()
        x1, y1, x2, y2 = line["x1"], line["y1"], line["x2"], line["y2"]
        x1 = int(x1 * image_width)
        y1 = int(y1 * image_height)
        x2 = int(x2 * image_width)
        y2 = int(y2 * image_height)

        angle = line["angle"]
        if abs(angle - 330) < 3:
            continue
        if abs(angle - 150) < 3:
            continue
        if abs(angle - 270) < 3:
            continue

        # vert lines
        if abs(angle - 90) < 3:
            continue
        if abs(angle - 180) < 3:
            continue

        cv2.circle(img, (x1, y1), 2, (0, 0, 255), -1)
        cv2.circle(img, (x2, y2), 2, (0, 255, 0), -1)

        cv2.line(img, (x1, y1), (x2, y2), color, 1)

        cv2.putText(img,
            f"{int(angle)}",
            (x1 + 2, y1 + 2),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.3,
            color,
            1)

    return line_data

    cv2.imwrite("debug/lines.png", img)

    exit()
    cv2.imshow("debug", img)
    cv2.waitKey(0)


def draw_fitz_page(page: fitz.Page,
                   zoom: int = 3,
                   lines: bool = True,
                   text: bool = False) -> np.ndarray:
    """Redraw specific page elements and return the image.

    Args:
        text: Whether to include text in the image. - Not implemented
        lines: Whether to include lines in the image.
        zoom: Zoom factor for the image.
    """
    # Get the page's drawing operations
    # img = page_to_opencv(page, zoom=zoom)
    drawings = page.get_drawings()

    # Create a blank image with white background
    img = np.ones((int(page.rect.height * zoom),
                   int(page.rect.width * zoom),
                  3), dtype=np.uint8) * 255

    # Scale the isometric area according to zoom factor
    iso_area = [
        int(absolute_isometric_area[0] * zoom),
        int(absolute_isometric_area[1] * zoom),
        int(absolute_isometric_area[2] * zoom),
        int(absolute_isometric_area[3] * zoom)
    ]

    # Draw only the vector graphics within isometric area
    for drawing in drawings:
        items = drawing["items"]  # List of drawing commands
        width = drawing.get("width", 1)
        if not width:
            width = 1
        width = int(math.ceil(width) * zoom)
        for item in items:
            if item[0] == "l":  # Line drawing command
                # Extract line coordinates and scale them
                point1 = item[1]
                point2 = item[2]
                x1, y1 = int(point1[0] * zoom), int(point1[1] * zoom)
                x2, y2 = int(point2[0] * zoom), int(point2[1] * zoom)

                # Check if line is within isometric area
                if (iso_area[0] <= x1 <= iso_area[2] and
                    iso_area[1] <= y1 <= iso_area[3] and
                    iso_area[0] <= x2 <= iso_area[2] and
                    iso_area[1] <= y2 <= iso_area[3]):
                    # Draw the line on our image
                    cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), width)

    return img

def point_to_line_distance(P, A, B):
    """Return shortest distance from point P to line AB."""
    P = np.array(P)
    A = np.array(A)
    B = np.array(B)

    AB = B - A
    AP = P - A

    # Compute the perpendicular distance using cross product
    distance = np.abs(np.cross(AB, AP)) / np.linalg.norm(AB)
    return distance

def main(page_range="1-60",
         verbose=False,
         verbosity=0):
    """
    Main function to process PDF and generate BOM

    Args:
        page_range: Page range to process
        verbose: Print verbose output
        verbosity: Verbosity level
    """

    # Read doc and perform some processing on raw data
    doc = fitz.open(filename)
    page_count = len(doc)
    pages_to_process = parse_page_range(page_range, page_count)
    feather = getSourceRawDataPath(projectId, filename)
    df = pd.read_feather(feather)

    df = preprocess_raw_data(df, pages_to_process=pages_to_process, isometric_area=absolute_isometric_area)

    # Debug - Get unique value total count for category == quantity
    unique_values = df[df["category"] == "quantity"]["value"].unique()

    counts = []
    for value in unique_values:
        counts.append({"value": value, "quantity": convert_quantity_to_float(value), "count": df[df["value"] == value]["value"].count()})

    if verbose and verbosity > 0:
        quantities = pd.DataFrame(counts)
        quantities.sort_values(by=["count", "quantity"], ascending=[True, False], inplace=True)
        quantities.to_excel("debug/quantities.xlsx", index=False)
        # Save the DataFrame to Excel
        quantities.to_excel(r"debug\s1601 - quantity_counts.xlsx", index=False)

    page_groups = df.groupby("pdf_page")

    # Create a new PDF document for output
    output_doc = fitz.open()

    # Different cropped rect configs for more dynamic checking
    rect_configs = {
        "diagonal": {
            "width": 3,
            "height": 2,
            "y_offset": -1,
            "check_limit": 200
        },
        "vertical": {
            "width": 2,
            "height": 3,
            "y_offset": 0,
            "check_limit": 200
        }
    }
    whitespace_limit = 3 # Number of empty whitespace checks before aborting
    whitespace_thresh = 0.1

    results = []

    all_bom_items = []
    # cv2.namedWindow("window", cv2.WINDOW_NORMAL)
    for pdf_page, page_df in page_groups:

        print("Analyzing page: ", pdf_page)
        page = doc.load_page(pdf_page-1)
        cv_img = page_to_opencv(page)

        # Need variable zoom for different types of processing
        img_zoom_1 = draw_fitz_page(page, zoom=1)

        # Geared towards fetching arrow lines
        cv_img_2 = draw_fitz_page(page, zoom=2)

        # Extract isometric lines and features' data
        isometric_df, debug_img = detect_isometric(img_zoom_1)
        isometric_df["size"] = None


        size_values = page_df[page_df["is_size_value"] == 1]
        size_value_count = len(size_values)
        unique_size_value_count = len(size_values["value"].unique())
        unique_size = None

        if unique_size_value_count > 1:
            print("Warning: More than one size value found on page: ", pdf_page)
        elif unique_size_value_count == 1:
            unique_size = size_values["value"].unique()[0]
        else:
            print("No size values found on page: ", pdf_page)

        print(size_values)

        isometric_pipes = isometric_df[isometric_df["category"] == "isometric_line"]

        main_sizes = []

        min_dist_threshold = 20

        # basic pipe size assignment
        # Note - Pipes will need segmenting further before this stage
        for row in size_values.itertuples():
            print(row.value)
            print(row.polygon)
            print(row.angle)
            print(row.category)
            size = row.value
            category = row.category
            rect = row.coordinates2
            if category != "cross_size":
                main_sizes.append(size)
            center = get_rect_center(rect, to_int=True)
            min_dist = None
            closest_index = None
            for row in isometric_pipes.itertuples():
                index = row.Index
                x1 = row.x1 * page.rect.width
                y1 = row.y1 * page.rect.height
                x2 = row.x2 * page.rect.width
                y2 = row.y2 * page.rect.height
                dist = point_to_line_distance(center, (x1, y1), (x2, y2))
                if min_dist is None or dist < min_dist:
                    min_dist = dist
                    closest_index = index
            print(dist, closest_index)
            if category == "cross_size":
                size = size.split("X")[1]
            isometric_df.loc[closest_index, "size"] = size
            if row.angle in [270, 90]:
                pass

        print(main_sizes)
        # line_segments = get_line_segments(cv_img_2)

        def insert_original_page():
            output_doc.insert_pdf(doc, from_page=pdf_page-1, to_page=pdf_page-1)
            output_page = output_doc[output_doc.page_count-1]
            output_page.insert_text((2, 24), f"Page: {pdf_page}", fontsize = 32)
            return output_page

        # Create a copy of the page for the output document
        _ = insert_original_page()

        isometric_df["fitz_color"] = [(0, 0, 0, )] * len(isometric_df)

        output_page = insert_original_page()
        for row in isometric_df.itertuples():
            x1 = row.x1 * output_page.rect.width
            y1 = row.y1 * output_page.rect.height
            x2 = row.x2 * output_page.rect.width
            y2 = row.y2 * output_page.rect.height
            category = row.category
            size = row.size
            endpoint_connected = row.connected
            base_angle = row.base_angle
            angle_colors = {90: (1, 0, 0), 30: (0, 1, 0), 330: (0, 0, 1)}
            fitz_color = angle_colors.get(base_angle, (0, 0, 0))
            if category == "isometric_line":
                output_page.draw_line((x1, y1), (x2, y2), color=fitz_color, width=6, stroke_opacity=0.5)
                if size is not None:
                    output_page.insert_text((x1 + 4, y1 + 4), size, fontsize = 28, color=(0, 0.8, 0.2))
            elif category == "endpoint":
                if endpoint_connected:
                    fitz_color = (0, 1, 0)
                else:
                    fitz_color = (1, 0, 0)
                circle_center = fitz.Point(x1, y1)
                radius = 8
                circle_rect = fitz.Rect(circle_center.x - radius, circle_center.y - radius,
                        circle_center.x + radius, circle_center.y + radius)
                output_page.draw_oval(circle_rect, color=fitz_color, width=4)

        continue

        # # Create a copy of the page for the output document
        # output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        # output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)

        # TODO: Analyze features with endpoints
        if False:
            page_features_df = feature_detection.detect_image_features(cv_img)
            page_features_df["pdf_page"] = pdf_page
            # Filter by inside isometric
            page_features_df["inside_isometric"] = page_features_df.apply(is_isometric, axis=1)
            page_features_df = page_features_df[page_features_df["inside_isometric"] == 1]
            result = analyze_features(page_features_df, cv_img)

            debug_img = result["debug_image"]
            line_results = result["results"]

            # Assign values to lines
            for row in line_results.itertuples():
                x1 = row.x1
                y1 = row.y1
                x2 = row.x2
                y2 = row.y2
                angle = row.angle
                points = row.points

        # cv2.imshow("debug", debug_img)
        # cv2.waitKey(0)

        # Create new PDF page with image dimensions
        output_page = output_doc.new_page(width=debug_img.shape[1], height=debug_img.shape[0])

        size_values = page_df[page_df["is_size_value"] == 1]
        size_value_count = len(size_values)
        unique_size_value_count = len(size_values["value"].unique())
        unique_size = None

        if unique_size_value_count > 1:
            print("Warning: More than one size value found on page: ", pdf_page)
        elif unique_size_value_count == 1:
            unique_size = size_values["value"].unique()[0]
        else:
            print("No size values found on page: ", pdf_page)


        # Extrapolate known iso lines. This is a much looser approach to
        # assigning values until more complete and accurate lines can be detected

        for row in page_df.itertuples():
            print("Size value: ", row.value)
            print("Angle value: ", row.angle)
            if (row.is_size_value == 0 and row.category != "quantity") or row.polygon is not None:
                continue
            x1, y1, x2, y2 = row.x1, row.y1, row.x2, row.y2

            if int(row.angle) == 330:
                print("diagonal_up_right")
            if row.polygon is not None:
                print("Skipping arrow assignment for parallel text")
                continue

            rect_points = [
                # Corners of rect
                # [row.x1, row.y1],
                # [row.x2, row.y1],
                # [row.x2, row.y2],
                # [row.x1, row.y2],
                # Mid points of rect border
                # [row.x1 + (row.x2 - row.x1) / 2, row.y1],
                # [row.x1 + (row.x2 - row.x1) / 2, row.y2],
                [row.x1, row.y1 + (row.y2 - row.y1) / 2],
                [row.x2, row.y1 + (row.y2 - row.y1) / 2]
            ]
            found_line = None
            for line in line_segments:
                start_point = line["start_point"]
                start_point = (int(start_point[0] * page_width), int(start_point[1] * page_height))
                length = line["distance"]
                for point in rect_points:
                    cv2.circle(debug_img, (int(point[0]), int(point[1])), 2, (0, 100, 255), -1)
                    # distance = distance_to(start_point, point)
                    if close_to(start_point, point, threshold=5):
                        if not found_line or length > found_line["distance"]:
                            found_line = line
                            continue

            if found_line:
                print("Found close point")
                start_point = found_line["start_point"]
                start_point = (int(start_point[0] * page_width), int(start_point[1] * page_height))
                cv2.circle(debug_img, (int(start_point[0]), int(start_point[1])), 3, (255, 100, 0), -1)
                end_point = found_line["end_point"]
                end_point = (int(end_point[0] * page_width), int(end_point[1] * page_height))
                cv2.circle(debug_img, (int(end_point[0]), int(end_point[1])), 3, (255, 100, 0), -1)
                cv2.line(debug_img, start_point, end_point, (255, 0, 0), 1)

        bom_items = []

        n = 1
        for row in page_df.itertuples():
            category = row.category
            item = {}

            detected_size = unique_size
            if category == QUANTITY:
                print("Quantity: ", row.value)
                item["description"] = CATEGORY_PIPE
                item["quantity"] = row.value
                item["size"] = detected_size
                item["quantity_float"] = row.quantity
                item["angle"] = row.angle

            if item:
                item["pdf_page"] = pdf_page
                item["pos"] = n
                if not detected_size:
                    item["warning_size"] = "Failed to assign a size"
                else:
                    item["warning_size"] = None
                n += 1
                bom_items.append(item)

        all_bom_items.extend(bom_items)

        color_map = {
            QUANTITY: (0, 1, 0),
            SIZE_VALUE: (1, 0.5, 0),
            CROSS_SIZE: (0, 0, 1),
        }

        # Insert the page image

        page_bom = pd.DataFrame(bom_items)
        page_bom = page_bom[["pos", "quantity", "size", "description", "angle", "warning_size"]]
        debug_img = draw_table(page_bom, img=debug_img)

        # Convert BGR to RGB if needed and encode as PNG
        if len(debug_img.shape) == 3:
            debug_img_rgb = cv2.cvtColor(debug_img, cv2.COLOR_BGR2RGB)
        else:
            # If grayscale, convert to RGB
            debug_img_rgb = cv2.cvtColor(debug_img, cv2.COLOR_GRAY2RGB)

        # Encode image as PNG bytes
        success, img_data = cv2.imencode('.png', debug_img_rgb)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())


        # Draw rectangles for each row in the group
        for row in page_df.itertuples():
            value = row.value
            quantity = row.quantity
            angle = row.angle if row.angle else 0
            category = row.category
            is_size_value = row.is_size_value
            x1 = row.x1
            y1 = row.y1
            x2 = row.x2
            y2 = row.y2

            whitespace_count = 0

            # Create a rectangle using the coordinates
            rect = fitz.Rect(x1, y1, x2, y2)

            polygon_vertices = None
            center_a = None
            center_b = None

            # Oriented text has polygon data
            if row.polygon is not None:
                polygon_vertices = row.polygon
                polygon_vertices = [tuple(pt) for pt in polygon_vertices]
                polygon_vertices += polygon_vertices[:1]  # Close the polygon

                polygon_left_line = polygon_vertices[3], polygon_vertices[4]
                polygon_right_line = polygon_vertices[1], polygon_vertices[2]

                center_a = get_line_center(polygon_left_line[0], polygon_left_line[1])
                center_b = get_line_center(polygon_right_line[0], polygon_right_line[1])
                # polygon_center_b = polygon_vertices[3:4]
                if is_size_value:
                    output_page.draw_polyline(polygon_vertices, color=color_map[SIZE_VALUE], width=2)
                else:
                    output_page.draw_polyline(polygon_vertices, color=(0.0, 0, 1), width=1, stroke_opacity=0.5)
                # output_page.draw_polyline(polygon_center_a, color=(1, 0, 0), width=3)
                output_page.draw_line(polygon_left_line[0], polygon_left_line[1], color=(1, 0, 0), width=1, stroke_opacity=0.5)
                output_page.draw_line(polygon_right_line[0], polygon_right_line[1], color=(1, 0, 0), width=1, stroke_opacity=0.5)

                rect_config = rect_configs["diagonal"]

            elif angle == 270:
                # Vertical bboxes - check extended points above and below
                center_a = get_line_center((x1, y1), (x2, y1))
                center_b = get_line_center((x1, y2), (x2, y2))

                output_page.draw_line((x1, y1), (x2, y1), color=(1, 0, 0), width=1, stroke_opacity=0.5)
                output_page.draw_line((x1, y2), (x2, y2), color=(1, 0, 0), width=1, stroke_opacity=0.5)

                rect_config = rect_configs["vertical"]

            # Extend before and after points
            endpoint_a = center_a
            endpoint_b = center_b
            # Valid if searching not stopped early due to consecutive whitespace checks
            endpoint_a_valid = False
            endpoint_b_valid = False
            if center_a and center_b and False:
                output_page.draw_circle(center_a, 2, color=(0, 1, 0))
                output_page.draw_circle(center_b, 2, color=(0, 1, 0))

                # extended_left, extended_right = extend_line(left_center, right_center)
                direction_vector = get_direction_vector(center_a, center_b)
                point = center_a
                check_rect_w = rect_config["width"]
                check_rect_h = rect_config["height"]
                check_rect_y_offset = rect_config["y_offset"]
                check_limit = rect_config["check_limit"]
                for n in range(check_limit):
                    endpoint_a = point
                    point = extend_previous_point(point, direction_vector)
                    check_rect = fitz.Rect(point[0] - check_rect_w, point[1] - check_rect_h + check_rect_y_offset,
                                            point[0] + check_rect_w, point[1] + check_rect_h + check_rect_y_offset)

                    # cv_img_crop = get_cropped_cv2_image(page, check_rect)
                    cv_img_crop = cv_img[int(check_rect.y0):int(check_rect.y1), int(check_rect.x0):int(check_rect.x1)]
                    # cv2.imshow("test", cv_img_crop)
                    non_white_pct = non_whitespace_percentage(cv_img_crop)
                    print("Check previous rect - non_white_pct:", non_white_pct)
                    if non_white_pct > 70:
                        output_page.draw_rect(check_rect, color=(1, 0, 1), width=1)
                        endpoint_a_valid = True
                        break

                    if non_white_pct < whitespace_thresh:
                        whitespace_count += 1
                        if whitespace_count >= whitespace_limit:
                            output_page.draw_rect(check_rect, color=(1, 0, 0), width=1) # error. stop rect
                            break
                    # cv2.waitKey(0)
                    output_page.draw_rect(check_rect, color=(0, 0.4, 0.4), width=1, stroke_opacity=0.2)

                point = center_b
                for n in range(check_limit):
                    point = extend_next_point(point, direction_vector)
                    endpoint_b = point
                    check_rect = fitz.Rect(point[0] - check_rect_w, point[1] - check_rect_h + check_rect_y_offset,
                                        point[0] + check_rect_w, point[1] + check_rect_h + check_rect_y_offset)

                    # cv_img_crop = get_cropped_cv2_image(page, check_rect)
                    cv_img_crop = cv_img[int(check_rect.y0):int(check_rect.y1), int(check_rect.x0):int(check_rect.x1)]
                    # cv2.imshow("test", cv_img_crop)
                    non_white_pct = non_whitespace_percentage(cv_img_crop)
                    print("Check next rect - non_white_pct:", non_white_pct)
                    if non_white_pct > 70:
                        output_page.draw_rect(check_rect, color=(1, 0, 1), width=1)
                        endpoint_b_valid = True
                        break

                    if non_white_pct < whitespace_thresh:
                        whitespace_count += 1
                        if whitespace_count >= whitespace_limit:
                            output_page.draw_rect(check_rect, color=(1, 0, 0), width=1) # error. stop rect
                            break

                    output_page.draw_rect(check_rect, color=(0, 0.4, 0.4), width=1, stroke_opacity=0.2)

                if endpoint_a_valid or endpoint_b_valid:
                    results.append({
                        "pdf_page": pdf_page,
                        "value": value,
                        "category": category,
                        "type": "measurement_line",
                        "quantity": row.quantity,
                        "angle": row.angle,
                        "endpoint_a": endpoint_a,
                        "endpoint_b": endpoint_b,
                        "endpoint_a_valid": endpoint_a_valid,
                        "endpoint_b_valid": endpoint_b_valid
                    })
                elif not endpoint_a_valid and not endpoint_b_valid:
                    pass
                    # results.append({
                    #     "pdf_page": pdf_page,
                    #     "value": value,
                    #     "category": category,
                    #     "quantity": row.quantity,
                    #     "angle": row.angle,
                    #     "endpoint_a": endpoint_a,
                    #     "endpoint_b": endpoint_b
                    # })

                # Draw other rects
                # if polygon_vertices:
                #     # Draw the rectangle on the page with a red color and 2pt width
                #     if pd.isna(category) or category is None:
                #         # output_page.draw_rect(rect, color=(0.6, 0, 0), width=1)
                #         output_page.draw_polyline(polygon_vertices, color=(0.6, 0, 0), width=1)
                #     else:
                #         # output_page.draw_rect(rect, color=(1, 0, 0), width=2)
                #         output_page.draw_polyline(polygon_vertices, color=(1, 0, 0), width=2)

            elif not polygon_vertices:
                color = color_map.get(category, (0, 1, 1))
                if is_size_value:
                    color = color_map[SIZE_VALUE]
                if pd.isna(category) or category is None:
                    output_page.draw_rect(rect, color=color, width=1)
                else:
                    output_page.draw_rect(rect, color=color, width=2)

            if polygon_vertices:
                start = polygon_vertices[0]
                start = (start[0], start[1] - 4)
            else:
                start = (x1, y1 - 4)



            # Optionally add text label with the value
            if hasattr(row, 'value') and row.value:
                if pd.isna(category) or category is None:
                    output_page.insert_text(start, str(row.value), color=(0.7, 0.3, 0.3), fontsize=8)
                else:
                    if quantity > 0:
                        c = "qty" if category == "quantity" else category
                        text = f"{row.value} - {c} | {round(quantity, 2)}, {round(angle, 2)}°"
                    else:
                        text = f"{row.value} | {category}, {angle}°"
                    output_page.insert_text(start, text, color=(0, 0, 1), fontsize=8)

        # Separate logic for segmenting
        for row in page_df.itertuples():
            value = row.value
            category = row.category
            x1 = row.x1
            y1 = row.y1
            x2 = row.x2
            y2 = row.y2

            # Create a rectangle using the coordinates
            rect = fitz.Rect(x1, y1, x2, y2)

            polygon_vertices = None
            if row.polygon is not None:
                polygon_vertices = row.polygon
                polygon_vertices = [tuple(pt) for pt in polygon_vertices]
                polygon_vertices += polygon_vertices[:1]  # Close the polygon by adding the first point again

            # results.append({
            #     "pdf_page": pdf_page,
            #     "value": value,
            #     "category": category,
            #     # "x1": x1,
            #     # "y1": y1,
            #     # "x2": x2,
            #     # "y2": y2,
            #     # "polygon": polygon_vertices,
            #     "quantity": row.quantity,
            #     "angle": row.angle
            # })

    # pd.DataFrame(results).to_excel("debug/s1601 - endpoints data.xlsx", index=False)

    bom_df = pd.DataFrame(all_bom_items)

    bom_df_columns = ["pdf_page", "pos", "description", "quantity", "size", "quantity_float", "angle", "warning_size"]
    columns = [c for c in bom_df_columns if c in bom_df.columns] + [c for c in bom_df.columns if c not in bom_df_columns]
    bom_df = bom_df[columns]

    bom_df.to_excel("debug/s1601 - bom_data.xlsx", index=False)

    # Save the output document
    outfile = "debug/s1601 - raw.pdf"
    output_doc.save(outfile)
    output_doc.close()
    doc.close()

    print(f"PDF with rectangles saved to: {outfile}")


if __name__ == "__main__":
    main()
