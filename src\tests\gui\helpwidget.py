from PySide6.QtWidgets import (QApplication, QVBoxLayout, QSplitter, QTextEdit, 
                               QWidget, QHeaderView, QPushButton, QSizePolicy)
from PySide6.QtCore import Qt
import sys

from PySide6.QtWidgets import (QWidget, QSplitter, QVBoxLayout, QPushButton, QComboBox, QGraphicsRectItem, 
                               QListWidgetItem, QListWidget, QGraphicsScene, QHBoxLayout, QSizePolicy, 
                               QSpacerItem, QGraphicsLineItem, QCompleter, QGraphicsItemGroup, QMessageBox, 
                               QGraphicsView, QLabel, QDialog, QCheckBox, QMenu, QSlider, QGraphicsOpacityEffect, 
                               QLineEdit, QGridLayout, QInputDialog, QFileDialog, QScrollArea)


class HelpWidget(QWidget):

    def __init__(self, parent):
        super().__init__(parent)

        self.setMinimumSize(480, 400)
        
        self.setLayout(QVBoxLayout())
        self.text = QTextEdit(self)
        self.text.setReadOnly(True)
        
        self.text.setAcceptRichText(True)
        self.layout().addWidget(self.text)

        self.show()
        self.setObjectName("helpPopup")
        self.text.setObjectName("helpPopup")

    def setText(self, text):
        self.text.setText(text)

if __name__ == "__main__":
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    w = HelpWidget(None)
    r = """
<h3>Keyboard shortcuts</h3>
<br>
<b>Ctrl + Home</b> 
<p>Go to cell (1,1)</p>
<b>Ctrl + End</b> 
<p>Go to last loaded cell (n,n)</p>
<b>Ctrl + Shift + Arrow Key</b>
<p>Go to next non-blank value in row or column</p>
<br>
<h3>Build RFQ</h3>
<p>Generate a RFQ. This will create a new RFQ tab or replace the existing one</p>

<h3>MTO Assistant</h3>
<p>Request AI assistance to fill the RFQ table.
Saved records will be restored from the database</p>

<h3>Apply</h3>
<p>Map the RFQ into the data tables</p>
<br>
"""
    w.text.setHtml(r)
    app.exec()
