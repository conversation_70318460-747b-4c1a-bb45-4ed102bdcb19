# table_processing.py
import pandas as pd
import numpy as np
import fitz
import logging
import re
import ast
import re
import unicodedata
from difflib import SequenceMatcher
from src.app_paths import getDataTempPath
from .table_utils import check_bbox_overlap
from .table_structure import find_spanning_columns, merge_wrapped_rows_text

logger = logging.getLogger(__name__)

logger_debug_mode=False # Ovveride App Logger settings
debug_mode= False # Export pages as xlsx files
debug_row_content = False # Debug row information at the end of detect_bom_rows
debug_discarded = False # Print intentionally skipped and unassigned items


def filter_data_within_table(page_num, raw_data, rect, table_type):
    def is_within_rect(coords, rect):

        try:
            # If coords is already a tuple, use it directly
            if isinstance(coords, tuple):
                item_rect = fitz.Rect(coords)
            else:
                # If it's a string, try to evaluate it
                coords = ast.literal_eval(coords)
                item_rect = fitz.Rect(coords)

            # Check if the item's rectangle intersects with the table rectangle
            return rect.intersects(item_rect)

        except (SyntaxError, ValueError, TypeError) as e:
            print()
            logger.warning(f"Warning: Unable to parse coordinates on page {page_num + 1}: Coordinates: {coords}, Rect: {rect}. Error: {e}", exc_info=True)
            return False

    # Apply the filter to the DataFrame
    filtered_data = raw_data[raw_data['coordinates2'].apply(lambda x: is_within_rect(x, rect))]

   
    # Ensure 'Coordinates' column contains tuples, not strings
    # filtered_data['coordinates2'] = filtered_data['coordinates2'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
    filtered_data.loc[:, 'coordinates2'] = filtered_data['coordinates2'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x) # Avoid pandas SettingWithCopyWarning
    # try:
    #     # Convert 'Fontsize' to numeric
    #     filtered_data['font_size'] = pd.to_numeric(filtered_data['font_size'], errors='coerce')
    # except Exception as e:
    #     logger.error(f"Error setting 'font_size' to numeric. {e}", exc_info=True)

    # Add empty columns
    new_columns = ["Type", "Subject", "Contents", "outlierScope"]
    # for col in new_columns:
    #     # filtered_data[col] = ""
    #     filtered_data.loc[:, col] = "" # Avoid pandas SettingWithCopyWarning

    filtered_data = filtered_data.assign(**{col: "" for col in new_columns})

    # Assign table_type to outlierScope column
    filtered_data["outlierScope"] = table_type

    return filtered_data


def analyze_and_filter_outliers(page_num, raw_table_df, table_type, font_size_tolerance=0.5): # Changed
    # Define red color for revisions as a tuple
    red_color = (255, 0, 0)

    # Initialize DataFrames
    red_text_df = pd.DataFrame()
    non_red_text_df = pd.DataFrame()
    text_outliers_df = pd.DataFrame()

    # Ensure 'color' column is a tuple of three values for the entire DataFrame
    raw_table_df['color'] = raw_table_df['color'].apply(lambda x: tuple(x) if isinstance(x, (list, np.ndarray)) and len(x) == 3 else (0, 0, 0))

    # Separate annotations and text data
    annotations_df = raw_table_df[raw_table_df['type'] != 'Text'].copy()
    text_df = raw_table_df[raw_table_df['type'] == 'Text']

    # Add annotations to outliers immediately
    # annotations_df['drop_reason'] = 'Annotation'

    # Separate red text and non-red text
    red_text_df = text_df[text_df['color'] == red_color]
    non_red_text_df = text_df[text_df['color'] != red_color]

    # Add this line to filter out 'Times-Roman' font - DEBUG TEMPORARY! Lines contains hidden characters!!
    non_red_text_df = non_red_text_df[non_red_text_df['font'] != 'Times-Roman']

    # non_red_text_df['font_size'] = pd.to_numeric(non_red_text_df['font_size'], errors='coerce')

    # Find common attributes from non-red text
    if len(non_red_text_df) > 0:
        common_font = non_red_text_df['font'].mode()[0]
        common_font_size = non_red_text_df['font_size'].mode()[0]
        common_font_color = non_red_text_df['color'].mode()[0]
    elif len(text_df) > 0:
        # If all text is red, use the most common attributes from all text
        common_font = text_df['font'].mode()[0]
        common_font_size = text_df['font_size'].mode()[0]
        common_font_color = text_df['color'].mode()[0]
    else:
        # Both are blank
        common_font, common_font_size, common_font_color = None, None, None

    # print(f"\n\nPAGE {page_num + 1}:\n COMMON FONT: {common_font} \n SIZE: {common_font_size} \n COLOR: {common_font_color}")

    # Create separate conditions for each outlier reason
    if common_font:
        font_condition = non_red_text_df['font'] != common_font
        size_condition = abs(non_red_text_df['font_size'] - common_font_size) > font_size_tolerance

        # Combine conditions
        outlier_condition = font_condition | size_condition

    # # Create a DataFrame for outliers with reasons
    # outliers_df = non_red_text_df[outlier_condition].copy()
    # outliers_df['drop_reason'] = ''
    # outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
    # outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
    # outliers_df['drop_reason'] = outliers_df['drop_reason'].str.rstrip('; ')

        # Create a DataFrame for outliers with reasons
        text_outliers_df = non_red_text_df[outlier_condition].copy()
        text_outliers_df['drop_reason'] = ''
        text_outliers_df.loc[font_condition, 'drop_reason'] += 'Uncommon Font; '
        text_outliers_df.loc[size_condition, 'drop_reason'] += 'Font Size Outlier; '
        text_outliers_df['drop_reason'] = text_outliers_df['drop_reason'].str.rstrip('; ')

    # Combine text outliers with annotations
    outliers_df = pd.concat([text_outliers_df, annotations_df])


    # Regular data is non-red text that's not an outlier, plus all red text
    if common_font is not None:
        regular_data_df = pd.concat([non_red_text_df[~outlier_condition], red_text_df])
    else:
        regular_data_df = text_df  # If no common font, all text data is considered regular

    # regular_data_df.to_excel(f"Regular Text DF Pg {page_num + 1}.xlsx")
    # outliers_df.to_excel(f"Outliers DF Pg {page_num + 1}.xlsx")

    # print(f"\n\nANALYZE OUTLIERS: {len(outliers_df)}")

    return outliers_df, regular_data_df

def assign_texts_to_columns(row_texts: list[dict], column_coords: list[dict], column_names: list[str], table_type: str, previous_row=None):
    '''
    <DEV>
    Handle Annotation Rows differently

    Collect text and annotation data (if it a row can be created) separately.
    Handle these data structures separately and join after.
    Need to identify row types (Text, Annot, Text w/Rev Markup)
    '''

    if debug_row_content:
        print(f"\n=== Starting new row assignment in assign_texts_to_columns===")
        print(f"Number of texts to process: {len(row_texts)}")
        print(f"Initial row_texts: {row_texts}")

    row_structure = {col: [] for col in column_names}  # Use lists instead of strings

    row_colors = {col: [] for col in column_names}  # To keep track of font colors
    row_material_scope = None
    row_component_category = None
    non_text_items = [] # New list to store non-text items (Annots)
    leftmost_column = column_names[0]  # Assuming the first column is the leftmost

    replace_mat_desc = False # Will replace values in material_description if 'True'

    sorted_texts = sorted(row_texts, key=lambda item: (item['bbox'][1], item['bbox'][0]))

    if debug_row_content:
        print(f"Sorted texts: {sorted_texts}")

    # print("\n\n-->INCOMING ROW TEXTS:\n")
    # pp(row_texts)
    # print("\n\n")

    col_rects = {}
    for col_name, col_info in zip(column_names, column_coords):
        col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
        col_rects[col_name] = col_rect

    for text_item in sorted_texts:
        # if text_item.get('type', '') != 'Text':
        try:
            val_type = text_item.get('type')

            if debug_row_content:
                print(f"\nProcessing text item: {text_item}")
                print(f"Type: {val_type}")

        except Exception as e:
            logger.error(f"Could not get 'val_type': {e}")
            val_type = ''

        # if val_type.upper() != 'TEXT':
        # if text_item.get('type', '') != 'Text':

        # Modified condition to properly handle nan values # Treat empty type as valid text # Temporarily ovveride for annotations ATTENTION
        if pd.isna(val_type) or val_type == '': #or val_type == 'OCR':
            # This is actually a valid text item
            pass
        elif val_type not in ['Text', '', 'OCR']:
            print(f"Skipping non-text item (val_type not in configured) options")

            print(f"\n--> \n   Non-text item found: {text_item}")
            print(f"       Value Type: {val_type}")
            print(f"       Table Type: {table_type}")
            non_text_items.append(text_item)
            continue

        text, bbox, color = str(text_item['text']).strip(), text_item['bbox'], text_item['color'] #, text_item['words']

        if debug_row_content:
            print(f"Text: {text}")
            print(f"BBox: {bbox}")

        # Handle the case where 'words' might be a string or missing
        try:
            words = text_item.get('words', [])

            if isinstance(words, str):
                words = [{'text': words, 'bbox': bbox}]
            elif not isinstance(words, list):
                words = [{'text': text, 'bbox': bbox}]

        except Exception as e:
            print(f"\n\n--> Failure on initial words get: {e}")

        if table_type == 'bom':

            material_scope = text_item.get('material_scope', '')
            if material_scope and not row_material_scope:
                row_material_scope = material_scope

            component_category = text_item.get('componentCategory', '')
            if component_category and not row_component_category:
                row_component_category = component_category

        # Check for multi-column span
        spanning_columns = []
        for col_name, col_info in zip(column_names, column_coords):
            col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
            if col_rect.intersects(bbox):
                spanning_columns.append(col_name)

        # Sort words by y-coordinate and then x-coordinate
        # sorted_words = sorted(words, key=lambda w: (w['bbox'][1], w['bbox'][0]))

        # Handle multi-column span
        if len(spanning_columns) > 1:

            for word_info in words:
                if isinstance(word_info, dict):
                    if 'word' in word_info and 'bbox' in word_info:
                        # Handle the case where word_info is already in the correct format
                        word = word_info['word']
                        word_bbox = fitz.Rect(word_info['bbox'])
                    elif 'text' in word_info:
                        # Handle the case where word_info contains a 'text' key
                        word = word_info['text']
                        word_bbox = fitz.Rect(word_info.get('bbox', bbox))  # Use the overall bbox if no specific bbox
                    else:
                        print(f"\n\nWARNING: Unexpected word_info structure: {word_info}")
                        continue
                else:
                    print(f"\n\nWARNING: word_info is not a dictionary: {word_info}")
                    continue

                for col_name, col_info in zip(column_names, column_coords):
                    col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                    if col_rect.intersects(word_bbox):

                        # Check for duplicates before adding
                        is_duplicate = False
                        for i, existing_word in enumerate(row_structure[col_name]):
                            existing_bbox = None
                            for t in sorted_texts:
                                if str(t['text']).strip() == existing_word:
                                    existing_bbox = t['bbox']
                                    break

                            if existing_word == word and existing_bbox and check_bbox_overlap(word_bbox, existing_bbox):
                                is_duplicate = True
                                break

                        if not is_duplicate:

                            if col_name == "material_description" or (color == (255, 0, 0) and row_structure[col_name]):
                                if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                    row_structure[col_name][-1] += f" {word}"
                                else:
                                    if replace_mat_desc:
                                        row_structure[col_name] = [word]
                                        row_colors[col_name] = [color]
                                    else:
                                        row_structure[col_name].append(word)
                                        row_colors[col_name].append(color)
                            else:
                                row_structure[col_name].append(word)
                                row_colors[col_name].append(color)
                        break

        else:
            assigned = False # Where text fits in a single column
            for col_name, col_info in zip(column_names, column_coords):
                col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])
                if col_rect.intersects(bbox):

                    # Check for duplicates before adding
                    is_duplicate = False
                    for i, existing_text in enumerate(row_structure[col_name]):
                        # Get bbox of existing text if available
                        existing_bbox = None
                        for t in sorted_texts:
                            if str(t['text']).strip() == existing_text:
                                existing_bbox = t['bbox']
                                break

                        if existing_text == text and existing_bbox and check_bbox_overlap(bbox, existing_bbox):
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        if color == (255, 0, 0) and text not in ["1", "2", "3"]:


                    # if color == (255, 0, 0) and text not in ["1", "2", "3"]:
                            if col_name == "material_description" or row_structure[col_name]:
                                # For material_description or if the column already has content
                                if row_structure[col_name] and row_colors[col_name] and row_colors[col_name][-1] == (255, 0, 0):
                                    # If last text was red, append
                                    row_structure[col_name][-1] += f" {text}"
                                else:
                                    # If last text wasn't red or column is empty, replace
                                    row_structure[col_name] = [text]
                                    row_colors[col_name] = [color]
                            else:
                                # For other columns or if it's the first entry, replace
                                row_structure[col_name] = [text]
                                row_colors[col_name] = [color]
                        else:

                            # For black text, append only if the last entry wasn't red
                            if not row_structure[col_name] or row_colors[col_name][-1] != (255, 0, 0):
                                row_structure[col_name].append(text)
                                row_colors[col_name].append(color)
                    assigned = True
                    break

            if not assigned:
                print(f"\n\n--> !Warning!: Text '{text}' could not be assigned to any column.")


    # Join texts in each column
    for col in row_structure:
        row_structure[col] = ' '.join(row_structure[col])

    if table_type == 'bom':
        row_structure['material_scope'] = row_material_scope if row_material_scope else ''
        row_structure['componentCategory'] = row_component_category if row_component_category else ''

    # Check for duplicates
    if previous_row is not None:
        if all(row_structure[col].strip() == previous_row[col].strip() for col in row_structure if col in previous_row):
            print(f"Duplicate row detected: {row_structure}")
            return None  # Return None for duplicate rows

    # print(f"\n\nROW STRUCTURE: {row_structure}\n\n")
    # print(f"\n\nNON TEXT ITEMS: {non_text_items}\n\n")
    if debug_row_content:
        print(f"\n=== Final row structure ===")
        print(row_structure)
        print()
        print()

    return row_structure

def create_logical_structure_text(page_num, raw_table_df, column_coords, headers_selected, table_type,
                                  numbered_rows=True, include_annot_rows=False,annot_table=False, y_tolerance=5):

    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    # --> Debug
    # --> Debug
    # raw_table_df.to_excel(f"debug/Raw Data - create_logical - Pg {page_num + 1}.xlsx")

    table_type = table_type.lower().strip()

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    structured_data = []

    raw_table_df = raw_table_df.copy()
    raw_table_df.loc[:, 'x0'] = raw_table_df['coordinates2'].apply(lambda coords: coords[0])
    raw_table_df.loc[:, 'x1'] = raw_table_df['coordinates2'].apply(lambda coords: coords[2])
    raw_table_df.loc[:, 'y0'] = raw_table_df['coordinates2'].apply(lambda coords: coords[1])
    # file = pd.DataFrame(raw_table_df)
    # file.to_excel('debug/raw_table_df.xlsx')
    raw_table_df_sorted = raw_table_df.sort_values(by=['y0', 'x0'])

    if len(raw_table_df) > 0:
        if table_type == "bom":
            leftmost_column = column_coords[0]

            try:
                bom_rows, bom_texts, filtered_contents, unassigned_items, skipped_items = detect_bom_rows(page_num, raw_table_df_sorted, leftmost_column)

                if debug_row_content:
                    print(f"\n-----------------\nRETURNED BOM ROWS FROM 'detect_bom_rows' \n {bom_rows}\n-----------------\n")
            except Exception as e:
                print()
                logger.error(f"Error in detect_bom_rows on page {page_num + 1}: {e}", exc_info=True)
                print()
                # return empty lists or handle this error in some way
                bom_rows, bom_texts, filtered_contents, unassigned_items = [], [], [], []

            # if unassigned_items:
            if debug_row_content:
                if unassigned_items is not None and len(unassigned_items) > 0:
                    print()
                    print()
                    print(f"-----> ERROR! PG {page_num + 1} - UNASSIGNED TABLE ITEMS!:")
                    for index, text, coords, reason in unassigned_items:
                        #unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
                        logger.info(f" Index: {index}, \n Text: '{text}', \n coordinates2: {coords}, \n Reason: {reason}")

                # if skipped_items
                if skipped_items is not None and len(skipped_items) > 0:
                    print(f"\n\n -----> ERROR! PG {page_num + 1} - INTENTIONALLY SKIPPED TABLE ITEMS!:")
                    for index, text, coords, reason in skipped_items:
                        #unassigned_items.append((index,text, row['Coordinates'], "Could not be assigned to a row"))
                        print(f" Index: {index}, \n Text: '{text}', \n coordinates2: {coords}, \n Reason: {reason}")


            index = 0
            for content in filtered_contents:
                row_texts = []
                for _, item in content.iterrows():
                    # text = item['Contents'] if annot_table else item['Text']
                    text = item['value']
                    # color = item['color']
                    coords = item['coordinates2']
                    words = item.get('words', None)
                    bbox = fitz.Rect(coords)

                    try:
                        color = item['color']
                    except Exception as e:
                        try:
                            color = item.get('Color', None)
                            if color is None:
                                logger.warning(f"Unable to get color value for row at item {row.name}: {e}")
                        except Exception as e2:
                            logger.error(f"Critical error getting color for row at item {row.name}: {e2}")
                            color = None

                        logger.info(f"Color Item Value: {item}")

                    # print(f"\n\nCONTENT: {content}")

                    row_data = {
                        'text': text,
                        'bbox': bbox,
                        'color': color,
                        'words': words,
                        'material_scope': item.get('Material Scope', ''),
                        'componentCategory': item.get('componentCategory', ''),
                        'type': item.get('type', '')
                    }
                    row_texts.append(row_data)

                # file = pd.DataFrame(row_texts)
                # file.to_excel(f'debug/raw_table_df{index}.xlsx')

                # print(f"\n\n----------DEBUG ROW TEXTS (create_logical_structure_text): \n{row_texts}----------------\n\n")
                structured_data.append(assign_texts_to_columns(row_texts, column_coords, column_names, table_type))

                # print(f"\n\nStructured data after assign texts to columns (filtered_contents):\n\n")
                structured_df_debug = pd.DataFrame(structured_data)
                # print(f"DEBUG: Structured DF after text to columns: \n{structured_df_debug}")
                # pp(structured_data)
                index = index+1

        else:
            for _, row in raw_table_df_sorted.iterrows():
                # text = row['Contents'] if annot_table else row['Text']
                text = row['value']
                # color = row['Color']
                block_coords = row['coordinates']
                coords = row['coordinates2']
                words = row['words']

                try:
                    x0, y0, x1, y1 = [float(coord) for coord in block_coords]
                    block = fitz.Rect(x0, y0, x1, y1)
                except:
                    block = None

                if not block:
                    try:
                        # Convert string representation of tuple to actual numbers
                        # Strip the parentheses and split by comma
                        coords_str = block_coords.strip('()').split(',')
                        # Convert each string to float
                        x0, y0, x1, y1 = [float(coord.strip()) for coord in coords_str]
                        block = fitz.Rect(x0, y0, x1, y1)
                    except Exception as e:
                        logger.error(f"Invalid block coordinates: {block_coords}")
                        logger.error(f"Error type: {type(e).__name__}, Error message: {str(e)}")
                        logger.error(f"Coordinates type: {type(block_coords)}")
                        block = fitz.Rect(0, 0, 0, 0)  # fallback

                bbox = fitz.Rect(coords)

                # Get Color
                try:
                    color = row['color']
                except Exception as e:
                    try:
                        color = row.get('color', None)
                        if color is None:
                            logger.warning(f"Unable to get 'color' value for row at index {row.name}: {e}")
                    except Exception as e2:
                        logger.error(f"Critical error getting 'color' for row at index {row.name}: {e2}")
                        color = None

                    logger.warning(f"Error 'color' Row Value: {row}")

                row_data = {
                    'text': text,
                    'block': block,
                    'bbox': bbox,
                    'color': color,
                    'type': row.get('type', ''),
                    'words': words
                }

                structured_data.append(assign_texts_to_columns([row_data], column_coords, column_names, table_type))

                # print("\n\nStructured data after assign texts to columns (else block):\n\n")

        # print(f"\n\nCreate Structured Table: \n{row_texts}")

        structured_df = pd.DataFrame(structured_data)

        # print(f"\n\nDEBUG: Structured DF Rows Before After Text to Columns: {len(structured_df)}")

    # structured_df.to_excel(f"Structured DF - Pg {page_num + 1}.xlsx")

    else:
        structured_df = pd.DataFrame()
        logger.warning("Structured DF is empty - Text")

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF Before Merge - {page_num+1}.xlsx"))

    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    if debug_mode:
        structured_df.to_excel(getDataTempPath(f"Structured DF After Merge - {page_num+1}.xlsx"))

    # print(f"\n\nDEBUG: Structured DF Rows Before Return: {len(structured_df)}")
    if debug_row_content:
        print(f"\n\nLENTH OF STRUCTURE DF!: \n{len(structured_df)}\n\n")

    return structured_df

# Temporarily used for Annotation type tables until a more robust solution is found
def create_logical_structure_annot(raw_table_df, column_coords, overlap_threshold=0, numbered_rows=True, annot_table=True):

    # print(f"ANNOT DF LENGTH: {len(raw_table_df)}\n Columns{raw_table_df.columns}")


    if debug_mode:
        print("\n\n--> ANNOT CREATE LOGICAL STRUCTURE ACCESSED...")
    #logger.debug("Starting create_logical_structure_2")
    pd.options.display.max_columns = None
    pd.set_option('display.width', 1000)
    pd.options.display.max_rows = None

    column_coords = sorted(column_coords, key=lambda x: x['x0'])
    column_names = [col['columnName'] for col in column_coords]

    # Data structure to hold the text for each column
    structured_data = {col: [] for col in column_names}
    unassigned_values = []  # New list to store unassigned values

    # Track the last processed y-coordinate for the first column
    last_y = None

    # print("Table Colmns: ", raw_table_df.columns)

    # Process each text item in raw_table_df
    for _, row in raw_table_df.iterrows():
        if annot_table:
            # text = row['Contents'] # CHANGED
            # text = row['Text']
            text = row['Contents'] if 'Contents' in row else row.get('value', row.get('Text', ''))
        else:
            # text = row['Text']
            text = row.get('value', row.get('Text', ''))
        #
        #print("\n\nTEXT: ", text)
        coords = row['coordinates2']

        # print(f"\nCoords type: {type(coords)}, Value: {coords}")


        try:
            if isinstance(coords, str):
                # If coords is a string, try to evaluate it as a tuple
                coords = eval(coords)

            if isinstance(coords, (list, tuple)) and len(coords) == 4:
                bbox = fitz.Rect(coords[0], coords[1], coords[2], coords[3])
            elif isinstance(coords, (list, tuple)) and len(coords) == 2:
                # Assuming coords are (x, y) of top-left corner, create a small 1x1 rectangle
                bbox = fitz.Rect(coords[0], coords[1], coords[0] + 1, coords[1] + 1)
            else:
                raise ValueError(f"Unexpected coords format: {coords}")

        except Exception as e:
            print(f"\n\nError assigning bbox: Text: {text}, Coords: {coords} \n Error: {e}")
            # If all else fails, create a default rectangle at (0, 0)
            print(f"Warning: Using default bbox for text: {text}")
            bbox = fitz.Rect(0, 0, 1, 1)

        # Calculate the centerpoint of the text's x-coordinate
        text_center_x = (bbox.x0 + bbox.x1) / 2

        # # Determine which column the text belongs to
        # assigned_column = None
        # for i, col_info in enumerate(column_coords):
        #     col_rect = fitz.Rect(col_info['x0'], col_info['y0'], col_info['x1'], col_info['y1'])

        # Determine which column the text belongs to
        assigned_column = None
        for i, col_info in enumerate(column_coords):
            if col_info['x0'] <= text_center_x <= col_info['x1']:
                assigned_column = col_info['columnName']
                break

        if assigned_column is None:
            unassigned_values.append({'text': text, 'bbox': bbox})
            continue

    # if col_rect.intersects(bbox):
    #     column_name = col_info['columnName']

        # Check if we need to start a new row
        #print(f"\nChecking new row criteria for text: '{text}', Y: {coords[1]}, Last Y: {last_y}, Overlap Threshold: {overlap_threshold}")
        if last_y is None or coords[1] > last_y + overlap_threshold:
            # Append empty strings to columns that have no data yet
            for col_name in structured_data:
                structured_data[col_name].append('')
            last_y = coords[3]

        if i == 0 and numbered_rows:  # Leftmost column and numbered rows
            match = re.match(r'(\d+)(?:\.\s+)?(?:\s+(.*))?', text)
            if match:
                structured_data[assigned_column][-1] = match.group(1)
                if match.group(2):
                    next_column_name = column_names[i+1]
                    structured_data[next_column_name][-1] = match.group(2)
            else:
                structured_data[assigned_column][-1] = text
        else:
            structured_data[assigned_column][-1] = text
        #break

    structured_df_bf = pd.DataFrame(structured_data) # TYPO???

    # Call the merge_wrapped_rows function on the structured DataFrame
    structured_df = merge_wrapped_rows_text(structured_data, annot_table)

    #print(f"\n\nANNOT CREATE LOGICAL END ROWS {len(structured_df)}")

    # Print unassigned values
    # if unassigned_values:
    if unassigned_values is not None and len(unassigned_values) > 0:
        print("\nUnassigned Values:")
        for item in unassigned_values:
            print(f"Text: {item['text']}, Bounding Box: {item['bbox']}")

    return structured_df

def process_table_data(raw_table_df, annotations_df, outliers_df, annot_outliers_df, column_coords, headers_selected, filename,
                       pdf_path, page_num, parent_folders_str, table_type, include_annot_rows=False, remove_outliers=True):

    if table_type == "bom":
        numbered_rows=True

    structured_table_df = pd.DataFrame()
    if len(annotations_df)>0:
        pass
    else:
        annotations_df=pd.DataFrame() # Annotations will be included with text dataframe

    #Identify outliers in the text data and logically structure it
    if len(raw_table_df) > 0:
        # export_large_data_to_excel(structured_table_df,"raw_table_2.xlsx",r"C:\Users\<USER>\AIS_work_dir\Architekt IS_\AIS Folder_" )

        # Regular data is the data where the majority of the data is the the same font, font size (Red text is added to regular data to preserve revisions)
        potential_outliers_df, regular_data_df = analyze_and_filter_outliers(page_num, raw_table_df, table_type)


        #potential_outliers_df.to_excel(f"potential_ouliers {page_num + 1}.xlsx")

        # Filter out outliers from the raw data - REMOVE OUTLIERS
        # structured_data = raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]

        if remove_outliers:
            #print("REMOVE OUTLIERS = TRUE")
            structured_data = regular_data_df #raw_table_df[~raw_table_df.isin(potential_outliers_df).all(1)]
            outliers_data = potential_outliers_df
        else:
            #print("REMOVE OUTLIERS = FALSE")
            structured_data = raw_table_df
            outliers_data = pd.DataFrame()  # Empty DataFrame if not removing outliers

        #outliers_data = raw_table_df[raw_table_df.isin(potential_outliers_df).all(1)]

        # print(f"PAGE {page_num + 1} OUTLIER AFTER FILTER: {len(raw_table_df)}")
        # print(f"PAGE {page_num + 1} OUTLIER SIZE: {len(outliers_data)}")

        # Process only non-outlier data for structured_table_df
        #structured_table_df = create_logical_structure_with_dynamic_rows(structured_data, column_coords, annot_table=False)

        # print("\n\nTEXT - Calling create_logical_structure_text")
        # structured_table_df = create_logical_structure_text(page_num, structured_data, column_coords, headers_selected, table_type, numbered_rows=True, annot_table=False)

        structured_table_df = create_logical_structure_text(page_num, raw_table_df, column_coords, headers_selected, table_type,
                                                            numbered_rows=False, include_annot_rows=False, annot_table=False, y_tolerance=5)

        # Insert columns at the beginning of the dataframe
        structured_table_df.insert(0, 'pdf_page', page_num + 1)
        structured_table_df.insert(0, 'sys_filename', filename)
        structured_table_df.insert(0, 'sys_path', pdf_path)
        structured_table_df.insert(0, 'sys_build', parent_folders_str)

        # Process outliers_df
        outliers_df = outliers_data

    if len(annotations_df):

        if debug_mode:
            annotations_df.to_excel("Annot Before create_logical_structure.xlsx")

        #annotations_df = create_logical_structure_2(annotations_df, column_coords, overlap_threshold=0, numbered_rows=False, annot_table=True)

        # print("\n\nANNOT - Calling create_logical_structure_annot")
        if include_annot_rows:
            pass # Annotations will be included with text dataframe
        else:
            annotations_df = create_logical_structure_annot(annotations_df, column_coords, annot_table=True)

        try:
            # Insert columns at the beginning of the dataframe
            annotations_df.insert(0, 'pdf_page', page_num + 1)
            annotations_df.insert(0, 'sys_filename', filename)
            annotations_df.insert(0, 'sys_path', pdf_path)
            annotations_df.insert(0, 'sys_build', parent_folders_str)
            # annotations_df.to_excel("Annot After create_logical_structure.xlsx")
        except Exception as e:
            pass

        if debug_mode:
            annotations_df.to_excel("debug/DEBUG MODE- Annot After create_logical_structure.xlsx")

        #print("\n\nExtracted Annotations for Table:\n", annotations_df)  # Example action, adjust as needed
    else:
        logger.debug("\n\nAnnotations table is empty.")

    # Return structured table data, outliers, and annotations
    return structured_table_df, annotations_df, outliers_df, annot_outliers_df #, annotations_df

def is_keyword(text, prefixes, keywords, max_words=3):
    '''
    Checks keywords like Material Labels/Descriptors, Install Type (Shop,Field etc)
    to determine whether or not to drop/ignore them when creating the table structure
    '''

    # Convert text to uppercase for case-insensitive matching
    text = text.strip().upper()

    # Split the text into words
    words = text.split()

    # Check if the text starts with any of the prefixes and has <= max_words
    if len(words) <= max_words and any(text.startswith(prefix) for prefix in prefixes):
        #print(f"\n\nDEBUG: '{text}' is a keyword (prefix match and short)")
        return True

    # Check if the entire text matches any of the keywords
    if text in keywords:
        #print(f"DEBUG: '{text}' is a keyword (exact match)")
        return True

    return False

def word_count(text):
    return len(text.split())

def starts_with_prefix_and_short(text, prefixes, max_words=2):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False

    words = text.split()
    result = any(text.startswith(prefix) for prefix in prefixes) and len(words) <= max_words
    # if result:
    #     print(f"DEBUG: '{text}' starts with prefix {[p for p in prefixes if text.startswith(p)]} and has {len(words)} words")
    return result

def keyword_in_first_words(text, keywords, first_n_words=5, min_total_words=4):
    # Handle NaN and non-string values
    if pd.isna(text) or not isinstance(text, str):
        return False

    words = text.split()

    # Check for exact match first
    if text in keywords:
        return True

    if len(words) < min_total_words:
        return False

    # Only check the first 'n' words
    first_words = ' '.join(words[:first_n_words])

    # Check if any of the keywords match the first 'n' words
    result = any(keyword in first_words for keyword in keywords)
    #print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")

    # if result:
    #     print(f"DEBUG: '{text}' contains a keyword in first {first_n_words} words")
    # else:
    #     print(f"DEBUG: '{text}' does not contain a keyword in first {first_n_words} words")
    return result
    # return any(keyword in first_words for keyword in keywords)

def preprocess_text(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = ''.join(c for c in unicodedata.normalize('NFD', text) if unicodedata.category(c) != 'Mn')
    text = re.sub(r'[^\w\s]', '', text)
    text = ' '.join(text.split())
    return text

def find_exact_keyword_match(text, keywords):
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if preprocess_text(keyword) == processed_text:
            return keyword

    return None

def find_similar_keyword_match(text, keywords, threshold: float = 0.8):
    """Returns keyword if meets similarity threshold"""
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if SequenceMatcher(None, preprocess_text(keyword), processed_text).ratio() >= threshold:
            return keyword

    return None


