from src.utils.logger import logger
import pandas as pd
from src.atom.dbManager import DatabaseManager
from src.views.tableresultsview import TableResultsViewBase

# logger = logging.getLogger(__file__)


class SpoolResultsView(TableResultsViewBase):
    
    def __init__(self, parent) -> None:
        super().__init__(parent)
        logger.info("Creating Spool Table...")
    
    def  __repr__(self) -> str:
        return "SPOOL"

    def initToolbar(self):
        super().initToolbar()
        buttons = [
            # ("savecolumn", "tree.svg"),
        ]
        for btn_name, icon_path in buttons:
            self.addToolbarButton(btn_name, icon_path)

    def onToolbarBtn(self, name):
        # Example logging or print statement for debugging
        print(f"Toolbar button clicked: {name}")

        if name == "tree": # Tree hieratchy view (QTreeWidget or similar)
            pass
        elif name == "save": # Push to database
            data = self.getTableData()  # Use your existing method
            df = pd.DataFrame(data)
            print("\n\nSPOOL DF ROWS FOR DB COMMIT: \n", len(df))
            db_manager = DatabaseManager()
            db_manager.insert_dataframe(df, "SPOOL")
        else:
            super().onToolbarBtn(name)


