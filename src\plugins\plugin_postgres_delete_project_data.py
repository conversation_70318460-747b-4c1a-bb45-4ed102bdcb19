import pandas as pd
from psycopg2.extras import execute_values
from datetime import datetime

from src.atom.pg_database import pg_connection


def plugin_postgres_delete_project_data(project_id: int = 0,
                                       delete_bom: bool = False,
                                       delete_general: bool = False,
                                       confirm: str = ""):
    """
    Selectively deletes BOM and/or general data for a specific project from the PostgreSQL database.

    This allows you to clear project data without deleting the project itself.

    Args:
        project_id (int): Project ID to delete data for (must be >= 1)
        delete_bom (bool): If True, delete all BOM data for this project
        delete_general (bool): If True, delete all general data for this project
        confirm (str): Must be exactly "confirm" to proceed with deletion

    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Request to delete data for project ID {project_id} from PostgreSQL database.")

    # Validate project_id
    try:
        project_id = int(project_id)
        if project_id <= 0:
            print("Error: project_id must be a positive integer.")
            return False
    except (ValueError, TypeError):
        print(f"Error: Invalid project_id: {project_id}")
        return False

    # Validate that at least one table is selected for deletion
    if not delete_bom and not delete_general:
        print("Error: You must select at least one table to delete data from (delete_bom and/or delete_general).")
        return False

    # Validate confirmation
    if confirm != "confirm":
        print("Error: You must set confirm='confirm' to proceed with deletion.")
        print("This is a safety measure to prevent accidental deletion.")
        return False

    # Use database connection context manager
    try:
        with pg_connection.get_db_connection() as conn:
            with conn.cursor() as cursor:
                # First check if the project exists
                cursor.execute("SELECT project_name FROM public.atem_projects WHERE id = %s", (project_id,))
                project = cursor.fetchone()

                if not project:
                    print(f"Error: Project with ID {project_id} does not exist.")
                    return False

                project_name = project[0]
                print(f"Found project: {project_name} (ID: {project_id})")
                print("Proceeding with data deletion...")

                # Count records that will be deleted
                if delete_bom:
                    cursor.execute("SELECT COUNT(*) FROM public.bom WHERE project_id = %s", (project_id,))
                    bom_count = cursor.fetchone()[0]
                    print(f"Will delete: {bom_count} BOM records")

                if delete_general:
                    cursor.execute("SELECT COUNT(*) FROM public.general WHERE project_id = %s", (project_id,))
                    general_count = cursor.fetchone()[0]
                    print(f"Will delete: {general_count} general records")

                # Delete BOM data if requested
                if delete_bom:
                    cursor.execute("DELETE FROM public.bom WHERE project_id = %s", (project_id,))
                    print(f"Deleted {cursor.rowcount} BOM records.")

                # Delete general data if requested
                if delete_general:
                    cursor.execute("DELETE FROM public.general WHERE project_id = %s", (project_id,))
                    print(f"Deleted {cursor.rowcount} general records.")

                # Commit the transaction
                conn.commit()
                print("Data deletion completed successfully.")
                print(f"Project {project_name} (ID: {project_id}) still exists in the database.")
                return True

    except Exception as e:
        # Transaction will automatically roll back on exception
        print(f"Error during data deletion: {e}")
        print("No changes were made to the database (transaction rolled back).")
        import traceback
        traceback.print_exc()
        return False