import pandas as pd
import numpy as np
from src.utils.logger import logger
from logging.handlers import RotatingFileHandler
import re
from src.app_paths import getDataTempPath
from pathlib import Path


# def setup_logging():
#     # Create a custom logger
#     global logger
#     logger = logging.getLogger()
#     logger.setLevel(logging.DEBUG)  # Logger is set to capture all messages from DEBUG and above.
#     #logger.setLevel(logging.INFO)  # Logger is set to capture all messages from info and above.
#
#     # Prevent adding multiple handlers to the logger
#     if not logger.handlers:
#         # Create console handler and set level to debug
#         console_handler = logging.StreamHandler()
#         #console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         console_handler.setLevel(logging.WARNING)  # Console handler will only emit ERROR and CRITICAL messages.
#         formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
#         console_handler.setFormatter(formatter)
#         logger.addHandler(console_handler)
#
#         try:
#             # Optionally, add a file handler for persistent logging
#             file_handler = RotatingFileHandler('app_log.log', maxBytes=1024*1024*5, backupCount=5)
#
#         except PermissionError as e:
#             print("No permission to set file handler")
#             file_handler = RotatingFileHandler(getDataTempPath('app_log.log'), maxBytes=1024*1024*5, backupCount=5)
#
#         file_handler.setLevel(logging.DEBUG)
#         file_handler.setFormatter(formatter)
#         # Add the handlers to the logger
#         logger.addHandler(file_handler)
#
#         print("<----------RotatingFileHandler enabled for logging (app.py)------------->")
#     else:
#         print("\n\n-----------------------------")
#         print("\n-----------------------------")
#         print("LOGGER IS ALREADY ACTIVE - COULD NOT CONFIGURE - (app.py)")
#         print("\n-----------------------------")
#         print("\n-----------------------------\n\n")



# setup_logging()

def parse_complex_size(value):
    """Parse complex size values that may include whole numbers, decimals, and fractions."""
    try:
        value = str(value).strip()
        # Handle cases with spaces, like "1 1/2"
        if ' ' in value:
            parts = value.split()
            return sum(parse_complex_size(part) for part in parts)
        elif '.' in value and '/' in value:
            # Handle cases like "1.1/2"
            whole_part, fraction_part = value.split('.')
            return float(whole_part) + convert_fraction_to_float(fraction_part)
        elif '/' in value:
            # Handle simple fractions
            return convert_fraction_to_float(value)
        else:
            # Handle decimal or whole numbers
            return float(value)
    except Exception as e:
        logger.error(f"Failed to parse complex size. Value: '{value}'. Error: {e}", exc_info=True)
        raise ValueError(f"Unable to parse size: {value}")

def convert_fraction_to_float(fraction_str):
    """Convert a fraction string to a float."""
    try:
        if '/' in fraction_str:
            numerator, denominator = map(float, fraction_str.split('/'))
            return numerator / denominator
        else:
            return float(fraction_str)
    except Exception as e:
        logger.error(f"Error converting fraction to float: {fraction_str}. Error: {e}")
        raise ValueError(f"Invalid fraction: {fraction_str}")

def format_size_value(size):
    """Ensure proper float format, adding leading zero if necessary."""
    try:
        if pd.isna(size):
            return np.nan
        size_float = float(size)
        if size_float == 0:
            return "0"
        formatted_size = "{:.3f}".format(size_float).rstrip('0').rstrip('.')
        return formatted_size if size_float < 1 else str(int(size_float)) if size_float.is_integer() else formatted_size
    except Exception as e:
        logger.error(f"Failed to format size value. Size: '{size}'. Error: {e}", exc_info=True)
        raise ValueError(f"Unable to format size: {size}")

def process_size_column(size):
    """Process the 'size' column and return 'size1' and 'size2'."""
    if pd.isna(size) or str(size).strip() == '':
        return np.nan, np.nan
    
    size = str(size).strip()
    size1 = size2 = np.nan
    
    try:
        # Handling case sensitivity for 'x'
        if 'x' in size.lower():
            parts = re.split(r'\s*[xX]\s*', size, flags=re.IGNORECASE)
            size1 = parse_complex_size(parts[0])
            size2 = parse_complex_size(parts[1]) if len(parts) > 1 else np.nan
        else:
            size = size.replace('-', ' ')
            size1 = parse_complex_size(size)
            size2 = np.nan
        
        # Format sizes and ensure correct ordering
        size1 = format_size_value(size1) if not pd.isna(size1) else np.nan
        size2 = format_size_value(size2) if not pd.isna(size2) else np.nan
        if not pd.isna(size1) and not pd.isna(size2) and float(size2) > float(size1):
            size1, size2 = size2, size1
    except Exception as e:
        logger.error(f"Error processing size: '{size}'. Error: {e}", exc_info=True)
        return np.nan, np.nan

    return size1, size2


def load_and_process_workbook(input_path, output_path):
    try:
        # Load the Excel file
        logger.info(f"Loading workbook from {input_path}")
        data = pd.read_excel(input_path)
        
        # Check if 'size' column exists
        if 'size' not in data.columns:
            logger.error("'size' column not found in the workbook")
            return
        
        # Process the 'size' column
        logger.info("Processing 'size' column")
        new_sizes = data['size'].apply(lambda x: pd.Series(process_size_column(x)))
        
        # Assign new values to the DataFrame
        data['size1'] = new_sizes[0]
        data['size2'] = new_sizes[1]
        data['last_updated'] = None
        
        # Add a column to show original size for comparison
        data['original_size'] = data['size']
        
        # Reorder columns to put new columns next to original for easy comparison
        columns = data.columns.tolist()
        new_order = columns[:columns.index('size')+1] + ['size1', 'size2', 'original_size'] + columns[columns.index('size')+1:-3]
        data = data[new_order]
        
        # Export to new Excel file
        logger.info(f"Exporting processed data to {output_path}")
        data.to_excel(output_path, index=False)
        
        logger.info("Processing completed successfully")
        
        # Print some statistics
        total_rows = len(data)
        processed_rows = data['size1'].notna().sum()
        logger.info(f"Total rows: {total_rows}")
        logger.info(f"Rows with processed size: {processed_rows}")
        #logger.info(f"Percentage processed: {processed_rows/total_rows*100:.2f}%")
        
    except Exception as e:
        logger.error(f"An error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    input_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 011\Data\BOG\exported_bom_data.xlsx"
    output_path = Path(input_path).parent / "processed_bom_data_debug.xlsx"
    
    load_and_process_workbook(input_path, str(output_path))