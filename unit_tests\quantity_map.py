'''
Tests for rfqtableview.py in: Architekt ATOM\src\views\tables\rfqtableview.py

'''

import pandas as pd

import math
from collections import defaultdict

# logger = logging.getLogger(__file__)

genMap = {'LF': 'lf', '90 Short Radius': 'elbows_90', '90 Long Radius': 'elbows_90', '45': 'elbows_45', 'Bevel': 'bevels', 'Tee': 'tees', 'Reducer (Concentric)': 'reducers', 
          'Reducer (Eccentric)': 'reducers', 'Cap': 'caps', 'Flanges': 'flanges', 'Flanged Valve': 'valves_flanged', 'Welded Valve': 'valves_welded', 'Cut Out': 'cut_outs', 'Bend': 
          'bends', 'Field Weld': 'field_welds', 'Support': 'supports'}

# Ensures Pandas does not interpret "N/A" as nan
def read_excel_with_na(file_path):
    return pd.read_excel(file_path, 
                         keep_default_na=False,
                         na_values=[''])  # This will treat empty cells as NaN, but keep 'N/A' as a string


# 
def check_quantities(grouped_bom: pd.DataFrame, generalData: pd.DataFrame, genMap: dict, debug_qty=False):
    discrepancies = []
    combined_sums = defaultdict(float)
    
    # First, calculate the combined sums for each column in generalData
    for category, column in genMap.items():
        bom_sum = grouped_bom[(grouped_bom['general_category'] == category) & 
                              (grouped_bom['size1'].notna())]['quantity'].sum()
        combined_sums[column] += bom_sum


    if debug_qty:
        print("\n\nDebugging Quantities...\n\n")
    # Now compare the combined sums with generalData
    for column, bom_sum in combined_sums.items():
        general_sum = generalData[column].sum()

        if debug_qty:
            print(f"--> Column: {column} \n    BOM QTY {bom_sum} \n    General Sum {general_sum} \n    Sum Difference (BOM - General): {bom_sum - general_sum}\n\n")

        
        if not math.isclose(bom_sum, general_sum, rel_tol=1e-9):
            discrepancies.append({
                'column': column,
                'bom_sum': bom_sum,
                'general_sum': general_sum,
                'difference': bom_sum - general_sum
            })
    
    return discrepancies

def updateGeneralDataQuantities(generalData: pd.DataFrame, genMap: dict, grouped_bom: pd.DataFrame, updated_bom: pd.DataFrame):
    print("\n\n--------> ENTERING UPDATE GENERAL QUANTITIES")

    def format_size(value):
        try:
            return f"{float(value):.3f}"
        except (ValueError, TypeError):
            return None

    generalData['size'] = generalData['size'].apply(format_size)
    generalData['pdf_id'] = generalData['pdf_id'].astype(str)
    
    grouped_bom['size1'] = grouped_bom['size1'].apply(format_size)
    grouped_bom['size2'] = grouped_bom['size2'].apply(format_size)
    grouped_bom['pdf_id'] = grouped_bom['pdf_id'].astype(str)

    processed_items = set()
    sizes_added = set()

    for _, row in grouped_bom.iterrows():
        category = row['general_category']
        size1 = format_size(row['size1'])
        size2 = format_size(row['size2'])
        uid = row['__uid__']
        quantity = row['quantity']
        pdf_id = row['pdf_id']
        
        item_key = uid
        
        if category in genMap and quantity > 0 and item_key not in processed_items:
            general_column = genMap[category]
            
            try:
                matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size1)].index

                if matching_indices.empty and pd.notna(size2):
                    matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size2)].index
                    if not matching_indices.empty:
                        size1, size2 = size2, size1

                if matching_indices.empty:
                    size_key = (pdf_id, size1)
                    if size_key not in sizes_added:
                        new_row = generalData[generalData['pdf_id'] == pdf_id].iloc[0].copy() if not generalData[generalData['pdf_id'] == pdf_id].empty else None
                        if new_row is not None:
                            new_row['size'] = size1
                            for col in genMap.values():
                                new_row[col] = 0  # Initialize all quantity columns to 0
                            generalData = pd.concat([generalData, pd.DataFrame([new_row])], ignore_index=True)
                            matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size1)].index
                            sizes_added.add(size_key)
                            #print(f"INFO: New row added for pdf_id: {pdf_id}, size: {size1}")
                        else:
                            print(f"WARNING: Unable to insert new row for pdf_id: {pdf_id}, size: {size1}, category: {category}")
                            continue
                    else:
                        print(f"INFO: Skipping size addition for pdf_id: {pdf_id}, size: {size1} (already added)")

                if not matching_indices.empty:
                    for idx in matching_indices:
                        if pd.isna(generalData.at[idx, general_column]):
                            generalData.at[idx, general_column] = 0
                        prev_value = generalData.at[idx, general_column]
                        generalData.at[idx, general_column] += quantity
                        # print(f"INFO: Updated {general_column} for pdf_id: {pdf_id}, size: {size1}. Previous: {prev_value}, Added: {quantity}, New: {generalData.at[idx, general_column]}")
                        
                        # Update 'ef' and 'sf' columns
                        matching_ef = grouped_bom[(grouped_bom['pdf_id'] == pdf_id) & (grouped_bom['size1'] == size1)]['ef']
                        matching_sf = grouped_bom[(grouped_bom['pdf_id'] == pdf_id) & (grouped_bom['size1'] == size1)]['sf']
                        
                        matching_ef = pd.to_numeric(matching_ef, errors='coerce')
                        matching_sf = pd.to_numeric(matching_sf, errors='coerce')
                        
                        matching_ef_sum = matching_ef.sum()
                        matching_sf_sum = matching_sf.sum()
                        
                        lf_value = generalData.at[idx, 'lf'] if pd.notna(generalData.at[idx, 'lf']) else 0
                        generalData.at[idx, 'ef'] = matching_ef_sum + lf_value
                        generalData.at[idx, 'sf'] = matching_sf_sum
                        
                    processed_items.add(item_key)
                else:
                    print(f"WARNING: No matching indices found for UID: {uid}, pdf_id: {pdf_id}, size1: {size1}, category: {category}")
                    
            except Exception as e:
                print(f"ERROR: Error updating quantity for pdf_id {pdf_id}, size {size1}, category {category}: {e}")

    # Check quantities
    discrepancies = check_quantities(grouped_bom, generalData, genMap, True)
    
    if discrepancies:
        print("\n\nDiscrepancies found:")
        for disc in discrepancies:
            print(f"--> Column: {disc['column']}")
            print(f"    BOM sum: {disc['bom_sum']}")
            print(f"    General Data sum: {disc['general_sum']}")
            print(f"    Difference: {disc['difference']}\n")
    else:
        print("\n\nNo discrepancies found. All quantities match.")
    
    return generalData

# Load Tables
general_path = r"C:\Users\<USER>\source\repos\Architekt ATOM\generalData.xlsx"
bom_path = r"C:\Users\<USER>\source\repos\Architekt ATOM\GroupedBom.xlsx"

general_df = read_excel_with_na(general_path)
grouped_bom_df = read_excel_with_na(bom_path)

final_gen_df = updateGeneralDataQuantities(general_df, genMap, grouped_bom_df, grouped_bom_df)

final_gen_df.to_excel("_General Dataframe Updated.xlsx")



# def updateGeneralDataQuantities(generalData: pd.DataFrame, genMap: dict, grouped_bom: pd.DataFrame, updated_bom: pd.DataFrame):
#         print("\n\n--------> ENTERING UPDATE GENERAL QUANTITIES")

#         print("\n\nGeneral Columns", generalData.columns)
#         print("\n\nBOM Columns", grouped_bom.columns)

#         # Helper function to format size values to 3 decimal places
#         def format_size(value):
#             try:
#                 return f"{float(value):.3f}"
#             except (ValueError, TypeError):
#                 return None

#         generalData['size'] = generalData['size'].apply(format_size)
#         generalData['pdf_id'] = generalData['pdf_id'].astype(str)
    
#         grouped_bom['size1'] = grouped_bom['size1'].apply(format_size)
#         grouped_bom['size2'] = grouped_bom['size2'].apply(format_size)
#         grouped_bom['pdf_id'] = grouped_bom['pdf_id'].astype(str)

#         print(f"\n\nGrouped BOM Columns: {grouped_bom.columns}")

#         unmatched_items = set()

#         # ef_lookup_df = self.getEfLookupDataframe()
#         for _, row in grouped_bom.iterrows():

#             category = row['general_category']
#             rfq_scope = row['rfq_scope']
#             size1 = format_size(row['size1'])
#             size2 = format_size(row['size2'])

#             uid = row['__uid__']
#             quantity = row['quantity']
#             pdf_id = row['pdf_id']  # Get pdf_id from the row
#             #pdf_page = row['pdf_page']  # Get pdf_page from the row
#             mtrl_desc = row['material_description']  # Get pdf_page from the row

#             if category in genMap and quantity > 0:
#                 general_column = genMap[category]
                
#                 try:
#                     #matching_indices = generalData[(generalData['pdf_id'] == row['pdf_id']) & (generalData['size'] == size1)].index
#                     matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size1)].index

#                     if matching_indices.empty:
#                         #print(f"No matching indices found for pdf_id: {pdf_id}, size1: {size1}")
#                         #logger.warning(f"No matching indices found for: pdf_id={pdf_id}, size={size}, category={category}, description={mtrl_desc}")
#                         #logger.warning(f"No matching indices found for: pdf_id={pdf_id}, size={size1}, category={category}") #, description={mtrl_desc}")

#                         # Attempt to find a match on size2
#                         if pd.notna(size2):
#                             matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size2)].index
                            
#                             if not matching_indices.empty: 
#                                 #print(f"-->Matched on size2 for pdf_id: {pdf_id}")
#                                 #logger.info(f"Matched on 'size2 - Swapping size1 and size2 pdf_id={pdf_id}, size={size1} & {size2}, category={category}")
                                
#                                 # If match, swap the 'size1' and 'size2' values
#                                 current_index = grouped_bom.index[grouped_bom.index.get_loc(_)]
#                                 grouped_bom.loc[current_index, ['size1', 'size2']] = [size2, size1]
#                             else:
#                                 pass
#                                 #logger.warning(f"No match for size1 or size2 for pdf_id: {pdf_id}")
#                                 #logger.warning(f"No match for size1 or size2. size2 value: {size2}, description: {mtrl_desc}")

#                         # If still no match, insert a new row with the size
#                         if matching_indices.empty:
#                             new_row = generalData[generalData['pdf_id'] == pdf_id].iloc[0].copy() if not generalData[generalData['pdf_id'] == pdf_id].empty else None
                        
#                             if new_row is not None:
#                                 new_row['size'] = size1
#                                 generalData = pd.concat([generalData, pd.DataFrame([new_row])], ignore_index=True)
#                                 matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size1)].index
#                             else:
#                                 logger.warning(f"Unable to insert new row for pdf_id: {pdf_id}, size: {size1}, category: {category}")
#                                 continue

#                         else:
                            
#                             # Insert a row in generalData with 'size' equal to 'size1'
#                             existing_sizes = generalData[generalData['pdf_id'] == pdf_id]['size'].dropna().unique()
#                             existing_rows = generalData[(generalData['pdf_id'] == pdf_id) & generalData['size'].isna()]

#                             # print(f"Existing sizes for pdf_id {pdf_id}: {existing_sizes}")
#                             # print(f"Size1 being inserted: {size1}")

#                             if size1 and size1 not in existing_sizes:
#                                 if not existing_rows.empty:
#                                     first_blank_idx = existing_rows.index[0]
#                                     generalData.at[first_blank_idx, 'size'] = size1
#                                     existing_rows = existing_rows.drop(first_blank_idx)

#                                 else:
#                                     if generalData[generalData['pdf_id'] == pdf_id].empty:
#                                         #skipped_pdf_ids.add(str(pdf_id))
#                                         continue
                                        
#                                     new_row = generalData[generalData['pdf_id'] == pdf_id].iloc[0].copy()
#                                     new_row['size'] = size1
#                                     generalData = pd.concat([generalData, pd.DataFrame([new_row])], ignore_index=True)
                                
#                             existing_sizes = generalData[generalData['pdf_id'] == pdf_id]['size'].dropna().unique()
#                             matching_indices = generalData[(generalData['pdf_id'] == pdf_id) & (generalData['size'] == size1)].index
      
#                     if not matching_indices.empty:
#                         debug_indices = "if not matching_indices.empty"
#                         for idx in matching_indices:
#                             if pd.isna(generalData.at[idx, general_column]):
#                                 generalData.at[idx, general_column] = 0
#                             else:
#                                 try:
#                                     generalData.at[idx, general_column] = float(generalData.at[idx, general_column])
#                                 except ValueError:
                                
#                                     #logger.warning(f"Invalid value in generalData at index {idx}, column {general_column}: {generalData.at[idx, general_column]}")
#                                     logger.error(f"Invalid value in generalData at index {idx}, column {general_column}: {generalData.at[idx, general_column]}")
#                                     generalData.at[idx, general_column] = 0
 
#                             generalData.at[idx, general_column] += quantity
                        
#                             try:
#                                 # Update 'ef' and 'sf' column using SUMIFS logic
#                                 matching_ef = grouped_bom[(grouped_bom['pdf_id'] == row['pdf_id']) & (grouped_bom['size1'] == size1)]['ef']
#                                 matching_sf = grouped_bom[(grouped_bom['pdf_id'] == row['pdf_id']) & (grouped_bom['size1'] == size1)]['sf']
        
#                                 # Convert 'ef' and 'sf' column to numeric data type
#                                 matching_ef = pd.to_numeric(matching_ef, errors='coerce')
#                                 matching_sf = pd.to_numeric(matching_sf, errors='coerce')
        
#                                 matching_ef_sum = matching_ef.sum()
#                                 matching_sf_sum = matching_sf.sum()
                            
#                                 #generalData.at[idx, 'ef'] = matching_ef_sum
                            
#                                 # Get the current value of 'lf' from generalData
#                                 lf_value = generalData.at[idx, 'lf']

#                                 # Convert 'lf' value to numeric data type
#                                 if pd.isna(lf_value):
#                                     lf_value = 0
#                                 else:
#                                     try:
#                                         lf_value = float(lf_value)
#                                     except ValueError:
#                                         #logger.warning(f"Invalid value in generalData at index {idx}, column 'lf': {lf_value}")
#                                         logger.warning(f"Invalid value in generalData at index {idx}, column 'lf': {lf_value}")
#                                         lf_value = 0

#                                 # Add the value of 'lf' to the summed 'ef' value
#                                 generalData.at[idx, 'ef'] = matching_ef_sum + lf_value
                            
#                                 # Update 'sf' in General Data with
#                                 generalData.at[idx, 'sf'] = matching_sf_sum
                            
#                             except Exception as e:
#                                 #logger.error(f"Error updating general data ef: {e}")
#                                 logger.error(f"Error updating general data ef: {e}", exc_info=True)
#                     else:
#                         logger.warning(f"No matching indices found for UID: {uid}, pdf_id: {pdf_id}, size1: {size1}, size1: {size1}, category {category},mtrl: {mtrl_desc}")
                            
#                 except Exception as e:
#                     #logger.error(f"Error updating quantity for pdf_id {pdf_id}, size {size1}, category {category}: {e}", exc_info=True)
#                     logger.error(f"Error updating quantity for pdf_id {pdf_id}, size {size1}, category {category}: {e}")

#         # At the very end of the function, add:
#         discrepancies = check_quantities(grouped_bom, generalData, genMap)
    
#         if discrepancies:
#             print("\n\nDiscrepancies found:")
#             for disc in discrepancies:
#                 print(f"--> Column: {disc['column']}")
#                 print(f"    BOM sum: {disc['bom_sum']}")
#                 print(f"    General Data sum: {disc['general_sum']}")
#                 print(f"    Difference: {disc['difference']}\n\n")
#         else:
#             print("\n\nNo discrepancies found. All quantities match.")
    
#         return generalData
