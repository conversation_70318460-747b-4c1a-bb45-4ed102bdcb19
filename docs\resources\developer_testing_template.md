﻿# ATEM Application Testing Checklist

## Test Session Information
```
Name: 
Date: 
App: ATEM
Version: 
Branch: 
Method: [IDE/EXE]
Testing Mode: [Dev/Production/Staging]
Operating System:
Browser (if applicable):
Additional Notes:
```

## Issue Reporting Template
For any result other than PASS, provide the following information:
```
Issue: [Brief description of the problem]
Environment: [Specific conditions when issue occurred]
Severity: [Critical/Severe/Moderate/Minor]
Notes: [Additional context, reproducibility steps]
Image: [URL or file path to screenshot/video]
Jira Issue: [Ticket number if created]
```

## Severity Definitions
- Critical: Application crash, data loss, security vulnerability
- Severe: Feature completely non-functional, blocking workflow
- Moderate: Feature partially working, has workaround
- Minor: Cosmetic issues, minor inconvenience

## 1. Login Component
- [ ] Test login functionality with valid credentials
- [ ] Verify error handling with invalid credentials
- [ ] Test "Forgot Password" workflow
- [ ] Verify password reset email delivery
- [ ] Test session persistence
- [ ] Verify logout functionality
- [ ] Check redirect to login page after logout

## 2. Project Setup & File Menu
- [ ] Test project dropdown component functionality
- [ ] Verify "Create New Project" workflow
- [ ] Test all required fields in project setup
- [ ] Verify special character handling in project names (@, ., /, \, etc.)
- [ ] Test "Back" arrow navigation in project setup
- [ ] Verify "Add to Existing Project" functionality
- [ ] Test project switching using left dropdown
- [ ] Check file menu controls
- [ ] Verify product key activation
- [ ] Test support ticket submission
- [ ] Verify MTO Assistant functionality
- [ ] Test Help menu accessibility

## 3. Document Viewer
- [ ] Verify document library loads correctly
- [ ] Check thumbnail generation and display
- [ ] Test page navigation (forward/back)
- [ ] Verify zoom controls
- [ ] Test keyboard shortcuts (Shift + Draw)
- [ ] Check split view functionality
- [ ] Test undock feature
- [ ] Verify close functionality
- [ ] Check right-click "View Document" option
- [ ] Test document formatting in viewer

## 4. Sidebar/Navigation
- [ ] Verify all sidebar components are visible
- [ ] Test Notepad functionality
- [ ] Check Construction Calculator
- [ ] Verify Dashboard display
- [ ] Test Upload Queue
- [ ] Check Document Library access
- [ ] Verify Manage Account section
- [ ] Test navigation between components

## 5. Layout & ROI Processing
- [ ] Test layout assignment functionality
- [ ] Verify ROI drawing tools
- [ ] Check "Detect Groups" feature
- [ ] Test group saving functionality
- [ ] Verify group OCR button activation
- [ ] Test layout syncing
- [ ] Check missing regions detection
- [ ] Verify pre-processing workflow
- [ ] Test pre-process confirmation dialog
- [ ] Verify ROI field name selection
- [ ] Check theme and color differentiation
- [ ] Test detected text accuracy

## 6. Bill of Materials
- [ ] Verify BOM preview functionality
- [ ] Check quantity accuracy
- [ ] Test data display formatting
- [ ] Verify preview data is non-editable
- [ ] Check automatic updates during page navigation
- [ ] Test preview window resizing
- [ ] Verify table filters for different data types
- [ ] Check freeze panes functionality
- [ ] Test edit columns feature
- [ ] Verify delete functionality
- [ ] Test table saving behavior
- [ ] Check export and quick export features

## 7. Extraction Process
- [ ] Test extraction initiation
- [ ] Record extraction completion time
- [ ] Verify user alerts for pages without text
- [ ] Check extracted content accuracy
- [ ] Test edit dialog columns
- [ ] Verify table filters (numeric, text, mixed)
- [ ] Check continuous vs. overwrite behavior
- [ ] Test general data extraction
- [ ] Verify elevation detection (annotation & text documents)
- [ ] Check outlier data identification
- [ ] Test outlier data export

## 8. RFQ Processing
- [ ] Test "Build RFQ" button
- [ ] Verify data consolidation
- [ ] Check column ordering
- [ ] Verify pipe quantities
- [ ] Test AI consolidation on new data
- [ ] Verify AI categorization
- [ ] Check quantity accuracy on old data
- [ ] Test keyboard shortcuts (Ctrl+Shift combinations)
- [ ] Verify multi-select functionality
- [ ] Test insult dropdown
- [ ] Check quantity matching with BOM
- [ ] Verify table sorting

## 9. AI Classifier
- [ ] Test AI classification on new documents
- [ ] Verify classification accuracy
- [ ] Check confidence scores
- [ ] Test retraining functionality
- [ ] Verify classification override options

## 10. Data Consolidation
- [ ] Test data consolidation process
- [ ] Verify merged data accuracy
- [ ] Check duplicate handling
- [ ] Test conflict resolution
- [ ] Verify consolidated data export

## 11. Final Verification
- [ ] Compare exported quantities with source data
- [ ] Verify OCR page order
- [ ] Check complete data extraction workflow
- [ ] Test cross-component integration
- [ ] Verify system performance under load

## Result Categories
- Pass: Feature works as expected
- Fail: Feature doesn't work or has significant issues
- Partial: Feature works with minor issues
- N/A: Feature not applicable or not implemented

## Notes
- Document any failures with specific steps to reproduce
- Include screenshots for visual bugs
- Note environment details for any failures
- Record time stamps for performance-related issues
- Document any workarounds discovered

## Final Test Report Summary
```
Total Tests: 
Passed: 
Failed: 
Partial: 
N/A: 
Critical Issues: 
Severe Issues: 
Moderate Issues: 
Minor Issues: 
Testing Duration: 
Additional Notes:
```

## Important Testing Notes
1. All failed tests must have complete issue documentation
2. Screenshots are required for UI-related issues
3. Video captures required for behavior/workflow issues
4. Create Jira tickets for all Critical and Severe issues immediately
5. Document any workarounds discovered during testing
6. Note any performance degradation or unusual system behavior
7. Record exact steps to reproduce each issue
8. Document any test data used (especially for extraction/processing tests)
9. Note any dependencies or prerequisites for each test
10. Track time spent on each major component for resource planning

## Example Issue Report
```
Issue: Dropdown on RFQ does not work
Environment: Extracted data already existed
Severity: Severe
Notes: Dropdown works on BOM but fails only in RFQ view after data extraction
Image: path/to/screenshot.png
Jira Issue: ATEM-1234
Additional Context: Issue appears only when switching between multiple projects
Steps to Reproduce:
1. Extract data from Project A
2. Switch to Project B
3. Attempt to use RFQ dropdown
Expected Result: Dropdown should display options
Actual Result: Dropdown remains unresponsive
```

## Testing Environment Matrix
Keep track of testing coverage across different environments:
```
Environment Combinations Tested:
□ Windows 10 - IDE
□ Windows 10 - EXE
□ Windows 11 - IDE
□ Windows 11 - EXE
□ Dev Environment
□ Production Environment
□ Staging Environment
```
