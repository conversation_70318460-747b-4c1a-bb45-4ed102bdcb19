"""https://sateeshkumarb.wordpress.com/2012/04/01/paginated-display-of-table-data-in-pyqt/"""

import sys
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
 

class Person(object):
    """Name of the person along with his city"""
    def __init__(self,name,city):
        self.name = name
        self.city = city


class PersonDisplay(QMainWindow):
    def __init__(self,parent=None):
        super(PersonDisplay,self).__init__(parent)
        self.setWindowTitle('Person City')
        view = QTableView()
        tableData = PersonTableModel()
        view.setModel(tableData)
        self.setCentralWidget(view)
 
        for n in range(50000):
            tableData.addPerson(Person('Row', f'{n}'))


class PersonTableModel(QAbstractTableModel):
 
    ROW_BATCH_COUNT = 15
 
    def __init__(self):
        super(PersonTableModel,self).__init__()
        self.headers = ['Name','City']
        self.records  = []
        self.rowsLoaded = PersonTableModel.ROW_BATCH_COUNT

    def rowCount(self, index=QModelIndex()):
        if not self.records:
            return 0

        if len(self.records) <= self.rowsLoaded:
            return len(self.records)
        else:
            return self.rowsLoaded
 
    def canFetchMore(self, index=QModelIndex()):
        if len(self.records) > self.rowsLoaded:
            return True
        else:
            return False

    def fetchMore(self, index=QModelIndex()):
        remainder = len(self.records) - self.rowsLoaded
        itemsToFetch = min(remainder, PersonTableModel.ROW_BATCH_COUNT)
        # self.beginRemoveRows(index, 0, 1)
        # del self.records[0]
        # self.endRemoveRows()
        self.beginInsertRows(QModelIndex(), self.rowsLoaded, self.rowsLoaded + itemsToFetch-1)
        self.rowsLoaded += itemsToFetch
        self.endInsertRows()
 
    def addPerson(self,person):
        self.beginResetModel()
        self.records.append(person)
        self.endResetModel()
 
    def columnCount(self, index=QModelIndex()):
        return len(self.headers)

    def data(self,index,role=Qt.DisplayRole):
        col = index.column()
        person = self.records[index.row()]
        if role == Qt.DisplayRole:
            if col == 0:
                return person.name
            elif col == 1:
                return person.city
            return ""
 
    def headerData(self,section,orientation,role=Qt.DisplayRole):
        if role != Qt.DisplayRole:
            return ""
 
        if orientation == Qt.Horizontal:
            return self.headers[section]
        return int(section + 1)


def start():
    app  = QApplication(sys.argv)
    appWin = PersonDisplay()
    appWin.show()
    app.exec_()


if __name__ == "__main__":
    start()