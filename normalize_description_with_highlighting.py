"""
Enhanced version of normalize_description.py that highlights the text portions
that led to each classification decision.

This module provides visual feedback showing exactly how classifications were determined.
"""

import pandas as pd
import re
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass

# Import existing modules
try:
    from bom_patterns import SchedulePatterns, RatingPatterns
    from normalize_end_types import create_end_tags
    from normalize_astm import create_astm_tags
    from prompts import categorization_table
    from normalize_description import (
        remove_extra_spaces, normalize_punctuation, capitalize_text,
        get_all_options_as_set, validate_cross_category_conflicts
    )
except ImportError:
    print("Warning: Some modules not available. Using mock data for demonstration.")
    categorization_table = []

@dataclass
class HighlightMatch:
    """Represents a highlighted text match with explanation"""
    start: int
    end: int
    matched_text: str
    classification_type: str
    classification_value: str
    pattern_used: str
    confidence: str
    explanation: str

class DescriptionHighlighter:
    """Main class for highlighting classification decisions"""
    
    def __init__(self):
        self.matches: List[HighlightMatch] = []
        self.original_text = ""
        
    def add_match(self, start: int, end: int, matched_text: str, 
                  classification_type: str, classification_value: str,
                  pattern_used: str, confidence: str, explanation: str):
        """Add a highlighted match"""
        self.matches.append(HighlightMatch(
            start=start, end=end, matched_text=matched_text,
            classification_type=classification_type, classification_value=classification_value,
            pattern_used=pattern_used, confidence=confidence, explanation=explanation
        ))
    
    def generate_html_output(self) -> str:
        """Generate HTML with highlighted text and explanations"""
        if not self.original_text:
            return ""
            
        # Sort matches by start position
        sorted_matches = sorted(self.matches, key=lambda x: x.start)
        
        # Color scheme for different classification types
        colors = {
            'schedule': '#FFE6E6',  # Light red
            'rating': '#E6F3FF',    # Light blue  
            'astm': '#E6FFE6',      # Light green
            'grade': '#FFF0E6',     # Light orange
            'ends': '#F0E6FF',      # Light purple
            'forging': '#FFFFE6',   # Light yellow
        }
        
        html_parts = []
        last_end = 0
        
        # Build HTML with highlights
        for match in sorted_matches:
            # Add text before this match
            if match.start > last_end:
                html_parts.append(self.original_text[last_end:match.start])
            
            # Add highlighted match
            color = colors.get(match.classification_type, '#F0F0F0')
            tooltip = f"{match.classification_type}: {match.classification_value} | Pattern: {match.pattern_used} | {match.explanation}"
            
            html_parts.append(
                f'<span style="background-color: {color}; border: 1px solid #999; padding: 1px 3px; margin: 0 1px; border-radius: 3px;" '
                f'title="{tooltip}" data-classification="{match.classification_type}">'
                f'{match.matched_text}</span>'
            )
            
            last_end = match.end
        
        # Add remaining text
        if last_end < len(self.original_text):
            html_parts.append(self.original_text[last_end:])
        
        return ''.join(html_parts)
    
    def generate_text_output(self) -> str:
        """Generate text output with explanations"""
        if not self.matches:
            return f"Original: {self.original_text}\nNo classifications found."
        
        output = [f"Original Description: {self.original_text}\n"]
        output.append("=" * 60)
        output.append("CLASSIFICATION BREAKDOWN:")
        output.append("=" * 60)
        
        # Group matches by classification type
        by_type = {}
        for match in self.matches:
            if match.classification_type not in by_type:
                by_type[match.classification_type] = []
            by_type[match.classification_type].append(match)
        
        for class_type, matches in by_type.items():
            output.append(f"\n{class_type.upper()}:")
            for i, match in enumerate(matches, 1):
                output.append(f"  {i}. Found: '{match.matched_text}' → {match.classification_value}")
                output.append(f"     Pattern: {match.pattern_used}")
                output.append(f"     Confidence: {match.confidence}")
                output.append(f"     Explanation: {match.explanation}")
                output.append(f"     Position: characters {match.start}-{match.end}")
        
        return "\n".join(output)

def normalize_schedule_with_highlighting(text: str, highlighter: DescriptionHighlighter) -> Tuple[str, Optional[str]]:
    """
    Enhanced schedule normalization that captures highlighting information
    """
    found_schedule = None
    patterns = SchedulePatterns() if 'SchedulePatterns' in globals() else None
    
    # Simple pattern matching for demonstration
    # In real implementation, would use the full logic from normalize_description.py
    
    # Look for SCH patterns
    sch_pattern = r'\b(SCH(?:EDULE)?\.?\s*(\w+(?:\.\d+)?))\b'
    match = re.search(sch_pattern, text, re.IGNORECASE)
    
    if match:
        full_match = match.group(1)
        schedule_value = match.group(2).upper()
        
        # Validate schedule (simplified)
        valid_schedules = ['5', '10', '20', '30', '40', '60', '80', '100', '120', '140', '160', 
                          '5S', '10S', '40S', '80S', 'STD', 'XS', 'XXS', 'XH', 'XXH']
        
        if schedule_value in valid_schedules:
            found_schedule = schedule_value.lower()
            
            # Add highlighting information
            highlighter.add_match(
                start=match.start(),
                end=match.end(),
                matched_text=full_match,
                classification_type='schedule',
                classification_value=schedule_value,
                pattern_used='SCH prefix pattern',
                confidence='High',
                explanation=f'Found explicit schedule designation "{full_match}" which maps to schedule value "{schedule_value}"'
            )
            
            # Replace in text
            text = text[:match.start()] + f"SCH {schedule_value}" + text[match.end():]
    
    # Look for wall thickness patterns
    if not found_schedule:
        wall_pattern = r'\b(WALL\s+(?:THICKNESS|THK)?\s*(?:=|:)?\s*(\d+\.\d+)(?:\s*(?:"|IN|MM))?)\b'
        match = re.search(wall_pattern, text, re.IGNORECASE)
        
        if match:
            full_match = match.group(1)
            thickness = match.group(2)
            found_schedule = thickness
            
            highlighter.add_match(
                start=match.start(),
                end=match.end(),
                matched_text=full_match,
                classification_type='schedule',
                classification_value=thickness,
                pattern_used='Wall thickness pattern',
                confidence='Medium',
                explanation=f'Found wall thickness specification "{full_match}" which indicates schedule value "{thickness}"'
            )
            
            text = text[:match.start()] + f"SCH {thickness}" + text[match.end():]
    
    return text, f"schedule:{found_schedule}" if found_schedule else None

def normalize_rating_with_highlighting(text: str, highlighter: DescriptionHighlighter) -> Tuple[str, Optional[str], Optional[str], Optional[str]]:
    """
    Enhanced rating normalization that captures highlighting information
    """
    found_rating = None
    review_tag = None
    match_type = None
    
    # Valid ratings (simplified)
    valid_ratings = ['150', '300', '600', '800', '1500', '2500', '3000', '4000', '5000']
    
    # Look for hash rating patterns (#300, 150#)
    hash_patterns = [
        (r'#\s*(\d+)', 'Hash prefix (#300)'),
        (r'(\d+)\s*#', 'Hash suffix (300#)'),
        (r'\b(?:CLASS|CL\.?|RATING)\s*(\d+)', 'Class/Rating prefix'),
        (r'(\d+)\s*(?:LB|PSI|PSIG)', 'Pressure unit suffix')
    ]
    
    for pattern, pattern_name in hash_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            rating_value = match.group(1)
            
            if rating_value in valid_ratings:
                found_rating = rating_value
                match_type = f"rating:{pattern_name}"
                
                highlighter.add_match(
                    start=match.start(),
                    end=match.end(),
                    matched_text=match.group(0),
                    classification_type='rating',
                    classification_value=rating_value,
                    pattern_used=pattern_name,
                    confidence='High',
                    explanation=f'Found pressure rating "{match.group(0)}" using {pattern_name} pattern, maps to rating "{rating_value}"'
                )
                
                # Replace in text
                text = text[:match.start()] + f"CLASS {rating_value}" + text[match.end():]
                break
    
    return text, f"rating:{found_rating}" if found_rating else None, review_tag, match_type

def normalize_description_with_highlighting(description: str) -> Tuple[str, List[str], List[str], List[str], DescriptionHighlighter]:
    """
    Enhanced version of normalize_description that provides highlighting information
    """
    if pd.isna(description):
        return "", [], [], [], DescriptionHighlighter()
    
    # Initialize highlighter
    highlighter = DescriptionHighlighter()
    highlighter.original_text = str(description)
    
    # Normalize text
    normalized = str(description)
    normalized = remove_extra_spaces(normalized) if 'remove_extra_spaces' in globals() else normalized
    normalized = normalize_punctuation(normalized) if 'normalize_punctuation' in globals() else normalized
    normalized = capitalize_text(normalized) if 'capitalize_text' in globals() else normalized.upper()
    
    # Collect results
    metadata_tags = []
    review_tags = []
    match_types = []
    
    # Normalize schedule with highlighting
    normalized, schedule_tag = normalize_schedule_with_highlighting(normalized, highlighter)
    if schedule_tag:
        metadata_tags.append(schedule_tag)
    
    # Normalize rating with highlighting
    normalized, rating_tag, review_rating, match_type_rating = normalize_rating_with_highlighting(normalized, highlighter)
    if rating_tag:
        metadata_tags.append(rating_tag)
    if review_rating:
        review_tags.append(review_rating)
    if match_type_rating:
        match_types.append(match_type_rating)
    
    # TODO: Add other normalization functions (ASTM, ends, etc.) with highlighting
    
    return normalized, metadata_tags, review_tags, match_types, highlighter

def demonstrate_highlighting(test_descriptions: List[str]):
    """
    Demonstrate the highlighting functionality with test descriptions
    """
    print("PIPING MATERIAL CLASSIFICATION - HIGHLIGHTING DEMONSTRATION")
    print("=" * 70)
    
    for i, description in enumerate(test_descriptions, 1):
        print(f"\nEXAMPLE {i}:")
        print("-" * 40)
        
        # Process description
        normalized, metadata_tags, review_tags, match_types, highlighter = normalize_description_with_highlighting(description)
        
        # Show text output
        print(highlighter.generate_text_output())
        
        # Show HTML output (for web display)
        html_output = highlighter.generate_html_output()
        print(f"\nHTML Output (for web display):")
        print(html_output)
        
        print("\n" + "=" * 70)

def create_educational_examples():
    """
    Create educational examples for the reference guide
    """
    examples = [
        {
            "description": "Pipe, 150lb, sch 10s, pe x be, seamless astm 106b",
            "learning_focus": "Complete pipe specification with multiple indicators"
        },
        {
            "description": "SO Flange RF 300# A105 4\"",
            "learning_focus": "Flange identification with rating and material"
        },
        {
            "description": "Ball Valve 800# A105 SW 2\"",
            "learning_focus": "Valve classification with end type determination"
        },
        {
            "description": "90 LR Elbow A234 WPB SCH 40 BE B16.9",
            "learning_focus": "Fitting with ASTM/grade combination and standard"
        },
        {
            "description": "2\" NIPPLE SCH 40 A106 GR B SMLS",
            "learning_focus": "Common AI misclassification - nipple vs pipe"
        },
        {
            "description": "WALL THICKNESS 0.250\" PIPE A312 TP316L",
            "learning_focus": "Wall thickness as schedule indicator"
        },
        {
            "description": "F316 FLANGE WN 150# RF",
            "learning_focus": "F-prefix grade indicating forged manufacturing"
        },
        {
            "description": "PART NO: ABC-123-XYZ",
            "learning_focus": "Part number requiring human review"
        }
    ]

    return examples

def generate_educational_html(examples: List[Dict]) -> str:
    """
    Generate HTML for educational examples in the reference guide
    """
    html_parts = ["""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Piping Material Classification Examples</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .example { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .description { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
            .highlighted { margin: 10px 0; padding: 10px; background-color: #f9f9f9; border-radius: 3px; }
            .explanation { margin-top: 10px; font-size: 14px; color: #666; }
            .classification-type { font-weight: bold; color: #333; }
            .legend { margin: 20px 0; padding: 15px; background-color: #f0f0f0; border-radius: 5px; }
            .legend-item { display: inline-block; margin: 5px 10px; padding: 3px 8px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>Piping Material Classification Examples</h1>
        <div class="legend">
            <h3>Color Legend:</h3>
            <span class="legend-item" style="background-color: #FFE6E6;">Schedule</span>
            <span class="legend-item" style="background-color: #E6F3FF;">Rating</span>
            <span class="legend-item" style="background-color: #E6FFE6;">ASTM</span>
            <span class="legend-item" style="background-color: #FFF0E6;">Grade</span>
            <span class="legend-item" style="background-color: #F0E6FF;">End Types</span>
            <span class="legend-item" style="background-color: #FFFFE6;">Manufacturing</span>
        </div>
    """]

    for i, example in enumerate(examples, 1):
        # Process the description
        normalized, metadata_tags, review_tags, match_types, highlighter = normalize_description_with_highlighting(example["description"])

        html_parts.append(f"""
        <div class="example">
            <h3>Example {i}: {example["learning_focus"]}</h3>
            <div class="description">Original: {example["description"]}</div>
            <div class="highlighted">{highlighter.generate_html_output()}</div>
            <div class="explanation">
                <strong>Classifications Found:</strong><br>
        """)

        # Add classification details
        for match in highlighter.matches:
            html_parts.append(f"""
                <span class="classification-type">{match.classification_type.title()}:</span>
                {match.classification_value} - {match.explanation}<br>
            """)

        html_parts.append("</div></div>")

    html_parts.append("</body></html>")
    return "".join(html_parts)

def create_reference_guide_examples():
    """
    Create examples specifically for the reference guide
    """
    examples = create_educational_examples()

    print("EDUCATIONAL EXAMPLES FOR REFERENCE GUIDE")
    print("=" * 60)

    for i, example in enumerate(examples, 1):
        print(f"\n### Example {i}: {example['learning_focus']}")
        print(f"**Description**: \"{example['description']}\"")
        print()

        # Process description
        normalized, metadata_tags, review_tags, match_types, highlighter = normalize_description_with_highlighting(example["description"])

        print("**Classification Breakdown**:")
        if highlighter.matches:
            for match in highlighter.matches:
                print(f"- **{match.matched_text}** → {match.classification_type}: \"{match.classification_value}\"")
                print(f"  - Pattern: {match.pattern_used}")
                print(f"  - Explanation: {match.explanation}")
        else:
            print("- No automatic classifications found (requires human review)")

        print()

    # Generate HTML file
    html_content = generate_educational_html(examples)
    with open("classification_examples.html", "w") as f:
        f.write(html_content)

    print(f"\nHTML examples saved to: classification_examples.html")
    print("Open this file in a web browser to see highlighted examples.")

if __name__ == '__main__':
    create_reference_guide_examples()
