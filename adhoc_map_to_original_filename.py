import pandas as pd
import os

mapping_file = r"C:\Users\<USER>\Desktop\adhoc mapping\BSLTX33994 - combined page mapping.xlsx"
file = r"C:\Users\<USER>\Desktop\adhoc mapping\BRS_0020 - BSLTX33994.xlsx"

sheet_names = ["General", "General Agg.", "BOM"]

# Load mapping data (assuming it's in the first sheet or default sheet)
mapping_df = pd.read_excel(mapping_file)
print(f"Mapping data loaded with {len(mapping_df)} rows")
print("Mapping columns:", mapping_df.columns.tolist())

# Load data from all sheets
data_df = pd.read_excel(file, sheet_name=sheet_names)
print(f"Data loaded from {len(sheet_names)} sheets")

def map_page_to_filename(pdf_page, mapping_df):
    """
    Map a PDF page number to its original filename based on start_page and end_page ranges
    """
    for _, row in mapping_df.iterrows():
        start_page = row['start_page']
        end_page = row['end_page']
        if start_page <= pdf_page <= end_page:
            return row['original_filename']
    return None  # Return None if no mapping found

# Process each sheet
for sheet_name in sheet_names:
    print(f"\nProcessing sheet: {sheet_name}")
    df = data_df[sheet_name]

    # Check if 'PDF Page' column exists
    if 'PDF Page' not in df.columns:
        print(f"Warning: 'PDF Page' column not found in {sheet_name}. Available columns: {df.columns.tolist()}")
        continue

    # Create the 'original filename' column by mapping PDF page numbers
    df['original filename'] = df['PDF Page'].apply(lambda x: map_page_to_filename(x, mapping_df))

    # Check for unmapped pages
    unmapped_count = df['original filename'].isna().sum()
    if unmapped_count > 0:
        print(f"Warning: {unmapped_count} pages in {sheet_name} could not be mapped to original filenames")
        print("Unmapped PDF pages:", df[df['original filename'].isna()]['PDF Page'].unique())

    # Update the data_df with the modified sheet
    data_df[sheet_name] = df
    print(f"Added 'original filename' column to {sheet_name} ({len(df)} rows)")

# Create output filename
base_name = os.path.splitext(os.path.basename(file))[0]
output_dir = os.path.dirname(file)
output_file = os.path.join(output_dir, f"{base_name}_with_filenames.xlsx")

# Save the modified data to Excel with all sheets
with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
    for sheet_name in sheet_names:
        data_df[sheet_name].to_excel(writer, sheet_name=sheet_name, index=False)

print(f"\nData saved to: {output_file}")
print("Processing complete!")