import os
import fitz
from concurrent.futures import ProcessPoolExecutor
import multiprocessing
from typing import Optional


def parse_page_range(page_range_str):
    """Parse page range string into list of page numbers (1-based)"""
    print("page_range_str", page_range_str, type(page_range_str))
    if type(page_range_str) == int:
        if page_range_str < 1:
            raise ValueError("Invalid page range. Page numbers must be greater than 0")
        return [page_range_str]
    elif type(page_range_str) == list:
        page_range = sorted([int(p) for p in page_range_str])
        return page_range
    elif page_range_str is None:
        return None
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return None

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            pages.append(page)

    return sorted(set(pages))

def _extract_single_page(args):
    """
    Worker function to extract a single page from PDF to image.
    This function runs in a separate process to avoid memory accumulation.
    """
    input_file, page_num, save_dir, dpi = args

    try:
        # Open PDF document in worker process
        pdf_document = fitz.open(input_file)
        page = pdf_document.load_page(page_num)

        # Calculate zoom factor
        zoom = dpi / 72

        # Extract page as image
        pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))

        # Save the image
        output_path = os.path.join(save_dir, f"page_{page_num + 1}.png")
        pix.save(output_path)

        # Clean up resources
        pix = None
        page = None
        pdf_document.close()

        return f"Page {page_num + 1} extracted successfully"

    except Exception as e:
        return f"Error extracting page {page_num + 1}: {str(e)}"


def plugin_extract_pdf_to_images(
    input_file: str,
    save_dir: str,
    dpi: int = 300,
    use_multiprocessing: bool = False,
    num_processes: Optional[int] = None,
    page_range: str = None,
):
    """
    Extract PDF pages to individual PNG images with optional parallelization.

    Args:
        input_file: Path to the input PDF file
        save_dir: Directory to save the extracted images
        dpi: DPI for image extraction (default: 300)
        use_multiprocessing: Whether to use multiprocessing for faster extraction
        num_processes: Number of processes to use (defaults to CPU count if None)

    Returns:
        str: Status message with extraction results
    """
    # Ensure save directory exists
    os.makedirs(save_dir, exist_ok=True)

    pages = parse_page_range(str(page_range))

    # Open PDF to get page count
    pdf_document = fitz.open(input_file)
    total_pages = len(pdf_document)
    pdf_document.close()

    if not pages:
        pages = [int(p) for p in range(1, total_pages + 1)]

    if not use_multiprocessing or total_pages == 1:
        # Sequential processing for single page or when multiprocessing is disabled
        pdf_document = fitz.open(input_file)
        zoom = dpi / 72

        for page_num in pages:
            page = pdf_document.load_page(page_num - 1)
            pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
            output_path = os.path.join(save_dir, f"page_{page_num}.png")
            pix.save(output_path)

            # Clean up resources
            pix = None
            page = None

        pdf_document.close()
        msg = f"Extracted {total_pages} pages to {save_dir} (sequential)"

    else:
        # Parallel processing using threading instead of multiprocessing to avoid pickling issues
        from concurrent.futures import ThreadPoolExecutor

        if num_processes is None:
            num_processes = min(multiprocessing.cpu_count(), total_pages)
        else:
            try:
                num_processes = min(multiprocessing.cpu_count(), int(num_processes))
            except (ValueError, TypeError):
                num_processes = min(multiprocessing.cpu_count(), total_pages)

        def extract_page_threaded(pdf_page):
            """Thread-safe page extraction function"""
            try:
                # Each thread opens its own PDF document to avoid conflicts
                pdf_doc = fitz.open(input_file)
                page = pdf_doc.load_page(pdf_page - 1)

                zoom = dpi / 72
                pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))

                output_path = os.path.join(save_dir, f"page_{pdf_page}.png")
                pix.save(output_path)

                # Clean up resources
                pix = None
                page = None
                pdf_doc.close()

                return f"Page {pdf_page} extracted successfully"

            except Exception as e:
                return f"Error extracting page {pdf_page}: {str(e)}"

        # Process pages in parallel using threads
        with ThreadPoolExecutor(max_workers=num_processes) as executor:
            results = list(executor.map(extract_page_threaded, pages))

        # Check for any errors
        errors = [result for result in results if "Error" in result]
        if errors:
            error_msg = f"Extracted {total_pages} pages with {len(errors)} errors. First error: {errors[0]}"
            print(error_msg)
            return error_msg

        msg = f"Extracted {total_pages} pages to {save_dir} (parallel, {num_processes} threads)"

    print(msg)
    return msg


# For compatibility, keep the module-level worker function in case multiprocessing is needed later
if __name__ == "__main__":
    # This allows the module to be imported properly for multiprocessing if needed
    pass