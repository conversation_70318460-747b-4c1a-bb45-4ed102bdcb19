from PySide6.QtWidgets import (QApplication, QVBoxLayout, QSplitter, QTextEdit, 
                               QWidget, QHeaderView, QPushButton, QSizePolicy)
from PySide6.QtCore import Qt
import random
import numpy as np
import sys
import pandas as pd
from src.widgets.groupedtableview import GroupedTableView, GroupedTableFlags

from src.atom.dbManager import DatabaseManager

def load_project_df() -> pd.DataFrame:
    db = DatabaseManager()
    project_id = 1
    df = db.get_user_general_data(project_id)
    return df


if __name__ == "__main__":
    sys.path[0] = ""  # For relative resource paths
    
    app = QApplication()
    w = QWidget(None)
    w.setLayout(QVBoxLayout())
    w.setMinimumSize(1280, 768)

    ROWS = 25

    splitter = QSplitter()
    splitter.setOrientation(Qt.Orientation.Horizontal)
    splitter.setContentsMargins(0,0,0,0)
    # splitter.setLayout(QHBoxLayout())
    # splitter.layout().setSpacing(0)
    splitter.setHandleWidth(0)

    checkboxStyle = GroupedTableFlags.HEADER_CHECKBOX_FIRST
    checkboxStyle = GroupedTableFlags.HEADER_CHECKBOX_OFF
    tableView: GroupedTableView = GroupedTableView(None, 
                                                   lazyloading=True,
                                                   checkboxStyle=checkboxStyle)
    fieldMapJson =  {"fields": {"Name": {"display": "NameAlias"}}, "rfq_fields": {}}
    simpleFieldMap = {}
    simpleFieldMap.update(fieldMapJson.get("fields", {}))
    simpleFieldMap.update(fieldMapJson.get("rfq_fields", {}))
    tableView.fieldMap = simpleFieldMap
    # Set table data
    data = []
    for n in range(ROWS):
        data.append([n])

    df = pd.DataFrame(data, columns=['id'])
    # tableView.setTableData(df)

    # tableView.setComboboxDelegateForColumn("id", ["1", "2", "3"])

    # newColumns = [f"x{x}" for x in range(10)]
    # tableView.addColumns(newColumns)
    # newColumns = [f"y{y}" for y in range(2)]
    # tableView.addColumns(newColumns)
    # Add delegates for the newly added columns
    
    extraColumnCount = 20
    for x in range(extraColumnCount):
        df[f"x{x}"] = np.random.randint(1, 20, df.shape[0])
    
    # Add some random null values
    nan_percent = {'x2':0.3, 'x1':0.2, 'x0':0.23, 'id': 0.4}
    for col, perc in nan_percent.items():
        df['null'] = np.random.choice([0, 1], size=df.shape[0], p=[1-perc, perc])
        df.loc[df['null'] == 1, col] = ""

    df.drop(columns=['null'], inplace=True)

    df = df.astype(str)

    if 1:
        df = load_project_df()
        df = pd.read_excel("_debug_raw_data_full.xlsx")
        tableView.setTableData(df)
    else:
        tableView.setEditFreezeColumns(["x0", "x1"])
        tableView.setTableData(df)
        tableView.setComboboxDelegateForColumn("id", ["1", "2", "3"])
        tableView.setComboboxDelegateForColumn("x1", ["1", "2", "3"])
    
    # for x in range(extraColumnCount):
    #     tableView.setComboboxDelegateForColumn(f"x{x}", ["Jupiter", "Saturn", "Mars"])
    
    tableView.horizontalHeader().setSectionsMovable(True)
    tableView.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
    tableView.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Fixed)

    def getIndexByColumnName(name):
        pass

    columnOrder = ["id", "x2", "x3", "__uid__"]
    # tableView.setColumnOrder(columnOrder)
    # tableView.setColumnOrder(tableView.getDefaultColumnNameOrder())
    w.layout().addWidget(tableView)

    w2 = QWidget()
    w2.setLayout(QVBoxLayout())
    pbPrintChecked = QPushButton("Print Checked")
    w2.layout().addWidget(pbPrintChecked)
    pbRemoveChecked = QPushButton("Remove Checked")
    w2.layout().addWidget(pbRemoveChecked)
    pbTogglePane = QPushButton("Toggle Freeze Pane")
    w2.layout().addWidget(pbTogglePane)
    pbToggleVis = QPushButton("Toggle Pane Visibility")
    w2.layout().addWidget(pbToggleVis)

    pbTableFocus = QPushButton("Switch Table Focus")
    w2.layout().addWidget(pbTableFocus)

    pbSelectedPrint = QPushButton("Print Selected Row Data")
    w2.layout().addWidget(pbSelectedPrint)

    pbPrintColumnOrder = QPushButton("Print displayed column order")
    w2.layout().addWidget(pbPrintColumnOrder)
    pbRefresh = QPushButton("Refresh")
    text = QTextEdit()
    text.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    w2.layout().addWidget(pbRefresh)
    w2.layout().addWidget(text)

    w2.setFixedWidth(400)

    splitter.addWidget(tableView)
    splitter.addWidget(w2)

    w.layout().addWidget(splitter)

    tableView.setEditFreezeColumns(["x2", "x1"])
    # tableView.setEditFreezeColumns(["lineNumber", "sheet"])
    def print_df():
        output = "Unfiltered Model (All data)\n"
        df = tableView.getDataFrame()
        if df is None:
            df = pd.DataFrame()
        output += str(df.to_string())
        if tableView.filterProxyModel.filteredModel:
            output += "\n\n\n"
            output += "Filtered Model\n"
            output += str(tableView.filterProxyModel.filteredModel._data.to_string())
        output += "\n\n\n"
        output += "Default Columns\n"
        output += str(tableView.getDefaultCozlumnNameOrder())
        output += "\n\n\n"
        output += "Swapped Columns\n"
        output += str(tableView.getSwappedColumnNameOrder())
        text.setText(output)

    pbRefresh.clicked.connect(print_df)

    def print_checked():
        output = "Unfiltered Model (All data)\n"
        selectedDf = tableView.getCheckedSelections()
        output += str(selectedDf.to_string())
        if tableView.filterProxyModel.filteredModel:
            output += "\n\n\n"
        output += "\n\n\n"
        # output += "Default Columns\n"
        # output += str(tableView.getDefaultColumnNameOrder())
        # output += "\n\n\n"
        # output += "Swapped Columns\n"
        # output += str(tableView.getSwappedColumnNameOrder())
        text.setText(output)

    pbPrintChecked.clicked.connect(print_checked)

    def remove_checked():
        output = "Unfiltered Model (All data)\n"
        tableView.removeCheckedRows()
        # output += "Default Columns\n"
        # output += str(tableView.getDefaultColumnNameOrder())
        # output += "\n\n\n"
        # output += "Swapped Columns\n"
        # output += str(tableView.getSwappedColumnNameOrder())
        text.setText(output)

    pbRemoveChecked.clicked.connect(remove_checked)

    def toggle_vis():
        tableView.frozenTable.setVisible(not tableView.frozenTable.isVisible())

    pbToggleVis.clicked.connect(toggle_vis)

    def toggle_pane():
        tableView.toggleFreezePane()

    pbTogglePane.clicked.connect(toggle_pane)

    frozeFocus = False
    def toggle_table_focus():
        global frozeFocus
        if not frozeFocus:
            tableView.frozenTable.setFocus()
        else:
            tableView.setFocus()
        frozeFocus = not frozeFocus

    pbTableFocus.clicked.connect(toggle_table_focus)

    def print_column_order():
        output = "Swapped Column Name Order\n"
        order = tableView.getSwappedColumnNameOrder(cache=False)
        output += str(order)
        text.setText(output)

    pbPrintColumnOrder.clicked.connect(print_column_order)

    def print_selected_rows():
        output = "Selected Rows\n"
        df = tableView.getSelectedRowData()
        output += str(df)
        text.setText(output)

    pbSelectedPrint.clicked.connect(print_selected_rows)


    w.show()


    try:
        from src.theme import stylesheet
        app.setStyleSheet(stylesheet)
    except:
        pass


    tableView.toggleFreezePane()
    app.exec()