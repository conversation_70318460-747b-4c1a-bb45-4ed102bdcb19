"""
RFQ Template Builder - GUI Interface
===================================

PySide6 interface for the RFQ Template Builder workflow.
Provides file pickers, step configuration, and progress tracking.
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import pandas as pd

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QLabel, QLineEdit, QPushButton, QCheckBox, QGroupBox, QTextEdit,
    QFileDialog, QProgressBar, QTabWidget, QFormLayout, QSpinBox,
    QComboBox, QMessageBox, QSplitter, QListWidget, QListWidgetItem
)
from PySide6.QtCore import QThread, Signal, Qt
from PySide6.QtGui import QFont, QIcon

# Add paths for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / "src" / "atom" / "pg_database"))
sys.path.append(str(current_dir / "unit_tests"))

class WorkflowThread(QThread):
    """Background thread for running the workflow"""
    progress_update = Signal(str)
    step_completed = Signal(str, bool)  # step_name, success
    workflow_completed = Signal(bool)   # success
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
    def run(self):
        """Run the workflow in background thread"""
        try:
            from rfq_template_builder import (
                run_create_combined_workbook,
                run_material_check,
                run_normalize_descriptions,
                run_create_rfq_template,
                load_excel_first_sheet
            )
            
            current_file = self.config['input_path']
            df = None
            
            # Step 1: Create Combined Workbook
            if self.config['run_step1']:
                self.progress_update.emit("Step 1: Creating Combined Workbook...")
                try:
                    # Handle multiple files for combining
                    if len(self.config['combine_files']) > 1:
                        # Multiple files - use the original logic
                        current_file = run_create_combined_workbook(
                            input_excel_path=self.config['combine_files'],
                            output_dir=self.config['output_dir'],
                            output_filename=f"Stage1_Combined_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                            combine_general=self.config['combine_general'],
                            combine_bom=self.config['combine_bom'],
                            combine_spec=self.config['combine_spec'],
                            combine_rfq=self.config['combine_rfq']
                        )
                    else:
                        # Single file - load with automatic column renaming
                        self.progress_update.emit("Loading and preprocessing single file...")
                        df = load_excel_first_sheet(current_file)
                        # Note: load_excel_first_sheet now handles the column renaming automatically

                        # Save the preprocessed file
                        current_file = os.path.join(
                            self.config['output_dir'],
                            f"Stage1_Preprocessed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                        )
                        os.makedirs(self.config['output_dir'], exist_ok=True)
                        df.to_excel(current_file, index=False)
                        self.progress_update.emit(f"Preprocessed file saved: {os.path.basename(current_file)}")
                    
                    self.step_completed.emit("Step 1: Combined Workbook", True)
                except Exception as e:
                    self.progress_update.emit(f"Step 1 failed: {e}")
                    self.step_completed.emit("Step 1: Combined Workbook", False)
                    if not self.config.get('continue_on_error', True):
                        self.workflow_completed.emit(False)
                        return
            
            # Step 2: Material Check
            if self.config['run_step2']:
                self.progress_update.emit("Step 2: Checking Material Descriptions...")
                try:
                    current_file = run_material_check(
                        input_excel_path=current_file,
                        output_dir=self.config['output_dir'],
                        sheet_name=None
                    )
                    self.step_completed.emit("Step 2: Material Check", True)
                except Exception as e:
                    self.progress_update.emit(f"Step 2 failed: {e}")
                    self.step_completed.emit("Step 2: Material Check", False)
                    if not self.config.get('continue_on_error', True):
                        self.workflow_completed.emit(False)
                        return
            
            # Step 3: Normalize Descriptions
            if self.config['run_step3']:
                self.progress_update.emit("Step 3: Normalizing Descriptions...")
                try:
                    current_file, df = run_normalize_descriptions(
                        input_excel_path=current_file,
                        output_dir=self.config['output_dir']
                    )
                    self.step_completed.emit("Step 3: Normalize Descriptions", True)
                except Exception as e:
                    self.progress_update.emit(f"Step 3 failed: {e}")
                    self.step_completed.emit("Step 3: Normalize Descriptions", False)
                    if not self.config.get('continue_on_error', True):
                        self.workflow_completed.emit(False)
                        return
            
            # Step 4: Create RFQ Template
            if self.config['run_step4']:
                self.progress_update.emit("Step 4: Creating RFQ Template...")
                try:
                    if df is None:
                        df = load_excel_first_sheet(current_file)
                    
                    final_path = run_create_rfq_template(
                        df=df,
                        output_dir=self.config['output_dir'],
                        enable_validation=self.config['enable_validation'],
                        deduplicate_materials=self.config['deduplicate_materials']
                    )
                    self.step_completed.emit("Step 4: RFQ Template", True)
                    self.progress_update.emit(f"Workflow completed! Final template: {os.path.basename(final_path)}")
                except Exception as e:
                    self.progress_update.emit(f"Step 4 failed: {e}")
                    self.step_completed.emit("Step 4: RFQ Template", False)
                    self.workflow_completed.emit(False)
                    return
            
            self.workflow_completed.emit(True)
            
        except Exception as e:
            self.progress_update.emit(f"Workflow failed: {e}")
            self.workflow_completed.emit(False)

class RFQTemplateBuilderGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RFQ Template Builder")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialize variables
        self.combine_files = []
        self.workflow_thread = None
        
        self.setup_ui()
        self.load_default_config()
        
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # Configuration Tab
        config_tab = QWidget()
        tab_widget.addTab(config_tab, "Configuration")
        self.setup_config_tab(config_tab)
        
        # Files Tab
        files_tab = QWidget()
        tab_widget.addTab(files_tab, "Files to Combine")
        self.setup_files_tab(files_tab)
        
        # Progress Tab
        progress_tab = QWidget()
        tab_widget.addTab(progress_tab, "Progress")
        self.setup_progress_tab(progress_tab)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.run_button = QPushButton("Run Workflow")
        self.run_button.clicked.connect(self.run_workflow)
        button_layout.addWidget(self.run_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop_workflow)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        save_config_btn = QPushButton("Save Config")
        save_config_btn.clicked.connect(self.save_config)
        button_layout.addWidget(save_config_btn)
        
        load_config_btn = QPushButton("Load Config")
        load_config_btn.clicked.connect(self.load_config)
        button_layout.addWidget(load_config_btn)
        
        main_layout.addLayout(button_layout)
        
    def setup_config_tab(self, tab):
        """Setup configuration tab"""
        layout = QVBoxLayout(tab)
        
        # Input/Output Section
        io_group = QGroupBox("Input/Output")
        io_layout = QFormLayout(io_group)
        
        # Input file
        input_layout = QHBoxLayout()
        self.input_path_edit = QLineEdit()
        input_browse_btn = QPushButton("Browse")
        input_browse_btn.clicked.connect(self.browse_input_file)
        input_layout.addWidget(self.input_path_edit)
        input_layout.addWidget(input_browse_btn)
        io_layout.addRow("Input Excel File:", input_layout)
        
        # Output directory
        output_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        output_browse_btn = QPushButton("Browse")
        output_browse_btn.clicked.connect(self.browse_output_dir)
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(output_browse_btn)
        io_layout.addRow("Output Directory:", output_layout)
        
        layout.addWidget(io_group)
        
        # Step Configuration
        steps_group = QGroupBox("Workflow Steps")
        steps_layout = QVBoxLayout(steps_group)
        
        self.step1_cb = QCheckBox("Step 1: Create Combined Workbook")
        self.step2_cb = QCheckBox("Step 2: Material Database Check")
        self.step3_cb = QCheckBox("Step 3: Normalize Descriptions")
        self.step4_cb = QCheckBox("Step 4: Create RFQ Template")
        
        steps_layout.addWidget(self.step1_cb)
        steps_layout.addWidget(self.step2_cb)
        steps_layout.addWidget(self.step3_cb)
        steps_layout.addWidget(self.step4_cb)
        
        layout.addWidget(steps_group)
        
        # Step 1 Options
        step1_group = QGroupBox("Step 1 Options - What to Combine")
        step1_layout = QVBoxLayout(step1_group)
        
        self.combine_general_cb = QCheckBox("Combine General")
        self.combine_bom_cb = QCheckBox("Combine BOM")
        self.combine_spec_cb = QCheckBox("Combine Spec")
        self.combine_rfq_cb = QCheckBox("Combine RFQ")
        
        step1_layout.addWidget(self.combine_general_cb)
        step1_layout.addWidget(self.combine_bom_cb)
        step1_layout.addWidget(self.combine_spec_cb)
        step1_layout.addWidget(self.combine_rfq_cb)
        
        layout.addWidget(step1_group)
        
        # Step 4 Options
        step4_group = QGroupBox("Step 4 Options - RFQ Template")
        step4_layout = QVBoxLayout(step4_group)
        
        self.enable_validation_cb = QCheckBox("Enable Validation Dropdowns")
        self.deduplicate_materials_cb = QCheckBox("Deduplicate Material Descriptions")
        self.continue_on_error_cb = QCheckBox("Continue on Error")
        
        step4_layout.addWidget(self.enable_validation_cb)
        step4_layout.addWidget(self.deduplicate_materials_cb)
        step4_layout.addWidget(self.continue_on_error_cb)
        
        layout.addWidget(step4_group)
        
        layout.addStretch()
        
    def setup_files_tab(self, tab):
        """Setup files tab for combining multiple files"""
        layout = QVBoxLayout(tab)
        
        # Instructions
        instructions = QLabel(
            "Add multiple Excel files to combine them in Step 1.\n"
            "If only one file is selected, it will be preprocessed (column renaming) instead of combined."
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # File list
        self.files_list = QListWidget()
        layout.addWidget(self.files_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        add_files_btn = QPushButton("Add Files")
        add_files_btn.clicked.connect(self.add_combine_files)
        button_layout.addWidget(add_files_btn)
        
        remove_file_btn = QPushButton("Remove Selected")
        remove_file_btn.clicked.connect(self.remove_combine_file)
        button_layout.addWidget(remove_file_btn)
        
        clear_files_btn = QPushButton("Clear All")
        clear_files_btn.clicked.connect(self.clear_combine_files)
        button_layout.addWidget(clear_files_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def setup_progress_tab(self, tab):
        """Setup progress tab"""
        layout = QVBoxLayout(tab)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # Progress text
        self.progress_text = QTextEdit()
        self.progress_text.setReadOnly(True)
        layout.addWidget(self.progress_text)
        
    def browse_input_file(self):
        """Browse for input Excel file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Input Excel File", "", "Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.input_path_edit.setText(file_path)
            
    def browse_output_dir(self):
        """Browse for output directory"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if dir_path:
            self.output_dir_edit.setText(dir_path)
            
    def add_combine_files(self):
        """Add files to combine list"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Select Excel Files to Combine", "", "Excel Files (*.xlsx *.xls)"
        )
        for file_path in file_paths:
            if file_path not in self.combine_files:
                self.combine_files.append(file_path)
                item = QListWidgetItem(os.path.basename(file_path))
                item.setToolTip(file_path)
                self.files_list.addItem(item)
                
    def remove_combine_file(self):
        """Remove selected file from combine list"""
        current_row = self.files_list.currentRow()
        if current_row >= 0:
            self.files_list.takeItem(current_row)
            del self.combine_files[current_row]
            
    def clear_combine_files(self):
        """Clear all files from combine list"""
        self.files_list.clear()
        self.combine_files.clear()
        
    def load_default_config(self):
        """Load default configuration"""
        # Set default values
        self.step1_cb.setChecked(False)  # Default to False since we need file selection
        self.step2_cb.setChecked(True)
        self.step3_cb.setChecked(True)
        self.step4_cb.setChecked(True)
        
        self.combine_general_cb.setChecked(False)
        self.combine_bom_cb.setChecked(True)
        self.combine_spec_cb.setChecked(False)
        self.combine_rfq_cb.setChecked(True)
        
        self.enable_validation_cb.setChecked(True)
        self.deduplicate_materials_cb.setChecked(True)
        self.continue_on_error_cb.setChecked(True)
        
    def get_config(self):
        """Get current configuration"""
        return {
            'input_path': self.input_path_edit.text(),
            'output_dir': self.output_dir_edit.text(),
            'combine_files': self.combine_files if self.combine_files else [self.input_path_edit.text()],
            'run_step1': self.step1_cb.isChecked(),
            'run_step2': self.step2_cb.isChecked(),
            'run_step3': self.step3_cb.isChecked(),
            'run_step4': self.step4_cb.isChecked(),
            'combine_general': self.combine_general_cb.isChecked(),
            'combine_bom': self.combine_bom_cb.isChecked(),
            'combine_spec': self.combine_spec_cb.isChecked(),
            'combine_rfq': self.combine_rfq_cb.isChecked(),
            'enable_validation': self.enable_validation_cb.isChecked(),
            'deduplicate_materials': self.deduplicate_materials_cb.isChecked(),
            'continue_on_error': self.continue_on_error_cb.isChecked()
        }
        
    def validate_config(self, config):
        """Validate configuration"""
        errors = []
        
        if not config['input_path'] and not config['combine_files']:
            errors.append("Please select an input file or files to combine")
            
        if not config['output_dir']:
            errors.append("Please select an output directory")
            
        if not any([config['run_step1'], config['run_step2'], config['run_step3'], config['run_step4']]):
            errors.append("Please select at least one step to run")
            
        if config['run_step1'] and not any([config['combine_general'], config['combine_bom'], 
                                           config['combine_spec'], config['combine_rfq']]):
            errors.append("Step 1 is enabled but no combine options are selected")
            
        return errors
        
    def run_workflow(self):
        """Run the workflow"""
        config = self.get_config()
        errors = self.validate_config(config)
        
        if errors:
            QMessageBox.warning(self, "Configuration Error", "\n".join(errors))
            return
            
        # Setup UI for running
        self.run_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_text.clear()
        self.progress_bar.setValue(0)
        
        # Start workflow thread
        self.workflow_thread = WorkflowThread(config)
        self.workflow_thread.progress_update.connect(self.update_progress)
        self.workflow_thread.step_completed.connect(self.step_completed)
        self.workflow_thread.workflow_completed.connect(self.workflow_completed)
        self.workflow_thread.start()
        
    def stop_workflow(self):
        """Stop the workflow"""
        if self.workflow_thread and self.workflow_thread.isRunning():
            self.workflow_thread.terminate()
            self.workflow_thread.wait()
        self.workflow_completed(False)
        
    def update_progress(self, message):
        """Update progress display"""
        self.progress_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        self.progress_text.ensureCursorVisible()
        
    def step_completed(self, step_name, success):
        """Handle step completion"""
        status = "✓" if success else "✗"
        self.update_progress(f"{status} {step_name} {'completed' if success else 'failed'}")
        
    def workflow_completed(self, success):
        """Handle workflow completion"""
        self.run_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100 if success else 0)
        
        if success:
            self.update_progress("🎉 Workflow completed successfully!")
            QMessageBox.information(self, "Success", "RFQ Template Builder completed successfully!")
        else:
            self.update_progress("❌ Workflow failed or was stopped")
            
    def save_config(self):
        """Save configuration to file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Configuration", "rfq_config.json", "JSON Files (*.json)"
        )
        if file_path:
            import json
            config = self.get_config()
            with open(file_path, 'w') as f:
                json.dump(config, f, indent=2)
            QMessageBox.information(self, "Success", "Configuration saved successfully!")
            
    def load_config(self):
        """Load configuration from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Configuration", "", "JSON Files (*.json)"
        )
        if file_path:
            import json
            try:
                with open(file_path, 'r') as f:
                    config = json.load(f)
                self.set_config(config)
                QMessageBox.information(self, "Success", "Configuration loaded successfully!")
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Failed to load configuration: {e}")
                
    def set_config(self, config):
        """Set configuration values"""
        self.input_path_edit.setText(config.get('input_path', ''))
        self.output_dir_edit.setText(config.get('output_dir', ''))
        
        self.step1_cb.setChecked(config.get('run_step1', False))
        self.step2_cb.setChecked(config.get('run_step2', True))
        self.step3_cb.setChecked(config.get('run_step3', True))
        self.step4_cb.setChecked(config.get('run_step4', True))
        
        self.combine_general_cb.setChecked(config.get('combine_general', False))
        self.combine_bom_cb.setChecked(config.get('combine_bom', True))
        self.combine_spec_cb.setChecked(config.get('combine_spec', False))
        self.combine_rfq_cb.setChecked(config.get('combine_rfq', True))
        
        self.enable_validation_cb.setChecked(config.get('enable_validation', True))
        self.deduplicate_materials_cb.setChecked(config.get('deduplicate_materials', True))
        self.continue_on_error_cb.setChecked(config.get('continue_on_error', True))
        
        # Load combine files
        self.combine_files = config.get('combine_files', [])
        self.files_list.clear()
        for file_path in self.combine_files:
            if os.path.exists(file_path):
                item = QListWidgetItem(os.path.basename(file_path))
                item.setToolTip(file_path)
                self.files_list.addItem(item)

def main():
    """Main function to launch the GUI"""
    app = QApplication(sys.argv)
    app.setApplicationName("RFQ Template Builder")
    app.setApplicationVersion("1.0")

    # Set application icon if available
    try:
        app.setWindowIcon(QIcon("icon.ico"))
    except:
        pass  # Icon file not found, continue without it

    window = RFQTemplateBuilderGUI()
    window.show()

    return app.exec()

if __name__ == "__main__":
    """
    Launch the RFQ Template Builder GUI directly

    Usage:
        python rfq_template_builder_gui.py
    """
    print("🚀 Starting RFQ Template Builder GUI...")

    # Check for required dependencies
    try:
        import pandas
        import xlsxwriter
        import openpyxl
        print("✅ All dependencies available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Install with: pip install pandas xlsxwriter openpyxl PySide6")
        sys.exit(1)

    # Launch the GUI
    exit_code = main()
    sys.exit(exit_code)
