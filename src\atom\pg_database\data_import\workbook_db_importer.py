"""
PostgreSQL Database Table Importer from Excel Workbooks.
Core workbook import functionality and shared utilities.

This module provides functionality to import data from Excel workbooks into PostgreSQL database tables.
It handles data validation, transformation, and insertion/update operations with special attention to
data type requirements and precision specifications of the target database schema.
"""

import os
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
from sqlalchemy import create_engine, inspect
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field, field_validator, create_model


# Import database connection utilities from project
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor, execute_query


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# We'll still keep a SQLAlchemy URL for schema inspection
DEFAULT_DB_URL = "postgresql://{user}:{password}@{host}:{port}/{database}"

# Configure database connection string
# For now, we'll create a placeholder that can be overridden
# DEFAULT_DB_URL = "postgresql://username:password@localhost:5432/database_name"


class DataTypeValidator:
    """
    Utility class for validating and converting data types according to PostgreSQL schema requirements.
    """

    @staticmethod
    def validate_integer(value: Any) -> Optional[int]:
        """Validate and convert a value to integer."""
        if pd.isna(value):
            # For required fields, we need to indicate this clearly
            raise ValueError(f"Value is required but got NaN/empty value")
        try:
            return int(float(value))  # Convert to float first to handle values like "123.0"
        except (ValueError, TypeError):
            raise ValueError(f"Invalid integer value: {value}")

    @staticmethod
    def validate_decimal(value: Any, precision: int, scale: int) -> Optional[float]:
        """
        Validate and convert a value to a decimal with specific precision and scale.

        Args:
            value: The value to validate
            precision: Total number of digits
            scale: Number of digits after the decimal point

        Returns:
            Properly formatted decimal value or None if invalid
        """
        if pd.isna(value):
            return None

        try:
            # Convert to float first
            float_val = float(value)

            # Check if value exceeds precision
            str_val = f"{float_val:.{scale}f}"
            whole_digits = len(str_val.split('.')[0])

            if whole_digits > (precision - scale):
                raise ValueError(f"Value {value} exceeds precision {precision}/{scale}")

            # Return the properly formatted decimal
            return round(float_val, scale)

        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid decimal value: {value}. Error: {str(e)}")

    @staticmethod
    def validate_varchar(value: Any, max_length: int) -> Optional[str]:
        """Validate and convert a value to VARCHAR with specified max length."""
        if pd.isna(value):
            return None

        try:
            str_val = str(value).strip()
            if len(str_val) > max_length:
                logger.warning(f"String value '{str_val}' exceeds max length {max_length}")
                return str_val[:max_length]  # Truncate to max length
            return str_val
        except Exception as e:
            raise ValueError(f"Invalid string value: {value}. Error: {str(e)}")


class SchemaValidator:
    """
    Class for validating dataframe against a PostgreSQL table schema.
    """

    def __init__(self, table_name: str, db_config: DatabaseConfig):
        """
        Initialize the schema validator.

        Args:
            table_name: Fully qualified table name (schema.table)
            db_config: DatabaseConfig instance
        """
        self.table_name = table_name
        self.db_config = db_config
        self._load_schema()

    def _load_schema(self):
        """Load the database schema for the specified table."""
        schema_name, table_name = self._parse_table_name()

        try:
            # Use the project's database connection utilities
            with get_db_cursor(self.db_config) as cursor:
                query = f"SELECT column_name, data_type, character_maximum_length, numeric_precision, numeric_scale, is_nullable " \
                        f"FROM information_schema.columns WHERE table_schema = '{schema_name}' AND table_name = '{table_name}'"
                cursor.execute(query)
                columns = cursor.fetchall()

            if not columns:
                raise ValueError(f"No columns found for table {self.table_name}")

            logger.info(f"Loaded schema for {self.table_name} with {len(columns)} columns")

            # Create a mapping of column name to type information
            self.columns = []  # Store column info for create_pydantic_model
            self.column_types = {}
            for col in columns:
                col_name = col['column_name']
                col_type = col['data_type']
                is_nullable = col['is_nullable'] == 'YES'

                # Create a column info dict similar to SQLAlchemy format for compatibility
                col_info = {
                    'name': col_name,
                    'type': col_type,
                    'nullable': is_nullable
                }

                type_info = {
                    'type': col_type,
                    'nullable': is_nullable
                }

                # Extract precision and scale for DECIMAL types
                if col_type == 'numeric':
                    precision = col['numeric_precision']
                    scale = col['numeric_scale']
                    type_info['precision'] = precision
                    type_info['scale'] = scale

                # Get string length for VARCHAR types
                if col_type == 'character varying':
                    length = col['character_maximum_length']
                    type_info['length'] = length

                self.column_types[col_name] = type_info
                self.columns.append(col_info)

        except Exception as e:
            logger.error(f"Error loading schema for {self.table_name}: {str(e)}")
            raise

    def _parse_table_name(self) -> Tuple[Optional[str], str]:
        """Parse the fully qualified table name into schema and table components."""
        parts = self.table_name.split('.')
        if len(parts) == 1:
            return None, parts[0]
        elif len(parts) == 2:
            return parts[0], parts[1]
        else:
            raise ValueError(f"Invalid table name format: {self.table_name}")

    def create_pydantic_model(self):
        """
        Dynamically create a Pydantic model based on the database schema.

        Returns:
            A Pydantic model class for validating data against the schema
        """
        fields = {}
        validators = {}

        for col in self.columns:
            col_name = col['name']
            nullable = col.get('nullable', True)
            col_type = col['type']

            # Skip primary key and timestamp columns for imports
            if col_name in ('id', 'created_at', 'updated_at'):
                continue

            # Determine Python type and any validation needed
            if col_type == 'integer':
                field_type = Optional[int] if not nullable else int
                fields[col_name] = (field_type, Field(None))

                # Add validator
                validator_name = f'validate_{col_name}'
                validators[validator_name] = field_validator(col_name)(
                    lambda v, info: DataTypeValidator.validate_integer(v)
                )

            elif col_type == 'numeric':
                field_type = Optional[float] if not nullable else float
                precision = self.column_types[col_name]['precision']
                scale = self.column_types[col_name]['scale']

                fields[col_name] = (field_type, Field(None))

                # Add validator with closure to capture precision/scale
                def make_decimal_validator(name, prec, sc):
                    @field_validator(name)
                    def validate_decimal(cls, v, info):
                        return DataTypeValidator.validate_decimal(v, prec, sc)
                    return validate_decimal

                validators[f'validate_{col_name}'] = make_decimal_validator(col_name, precision, scale)

            elif col_type == 'character varying':
                field_type = Optional[str] if not nullable else str
                length = self.column_types[col_name]['length']

                fields[col_name] = (field_type, Field(None))

                # Add validator with closure to capture length
                def make_varchar_validator(name, max_length):
                    @field_validator(name)
                    def validate_varchar(cls, v, info):
                        return DataTypeValidator.validate_varchar(v, max_length)
                    return validate_varchar

                validators[f'validate_{col_name}'] = make_varchar_validator(col_name, length)

            else:
                # Default to string for other types
                field_type = Optional[str] if not nullable else str
                fields[col_name] = (field_type, Field(None))

        # Create the model dynamically
        model_name = ''.join(word.capitalize() for word in self.table_name.replace('.', '_').split('_')) + 'Model'
        model = create_model(
            model_name,
            **fields,
            __validators__=validators
        )

        return model

    def validate_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        Validate a dataframe against the table schema.

        Args:
            df: Pandas DataFrame containing the data to validate

        Returns:
            Tuple of (validated_df, error_records)
        """
        model = self.create_pydantic_model()
        valid_records = []
        error_records = []

        # Check for missing columns
        db_columns = set(self.column_types.keys())
        df_columns = set(df.columns)

        # Identify required columns (those that are not nullable)
        required_columns = {col for col, info in self.column_types.items()
                           if not info.get('nullable', True) and col not in {'id', 'created_at', 'updated_at'}}

        missing_columns = db_columns - df_columns - {'id', 'created_at', 'updated_at'}
        if missing_columns:
            logger.warning(f"Missing columns in input data: {missing_columns}")

            # If any required columns are missing, this is a critical error
            missing_required = missing_columns.intersection(required_columns)
            if missing_required:
                logger.error(f"Missing REQUIRED columns: {missing_required}")

        extra_columns = df_columns - db_columns
        if extra_columns:
            logger.warning(f"Extra columns in input data (will be ignored): {extra_columns}")

        # Pre-check for missing values in required columns
        for col in required_columns:
            if col in df.columns and df[col].isna().any():
                null_rows = df[df[col].isna()].index.tolist()
                logger.warning(f"Column '{col}' is required but has NULL values in rows: {null_rows}")

        # Process each row
        for idx, row in df.iterrows():
            try:
                # Check for missing required values before creating model
                missing_required_values = []
                for col in required_columns:
                    if col in row.index and pd.isna(row[col]):
                        missing_required_values.append(col)

                if missing_required_values:
                    raise ValueError(f"Required columns have NULL values: {', '.join(missing_required_values)}")

                # Convert row to dict, filtering out extra columns
                row_data = {col: row[col] for col in row.index if col in db_columns}

                # Validate with Pydantic model
                valid_data = model(**row_data).dict(exclude_none=True)
                valid_records.append(valid_data)

            except Exception as e:
                error_info = {
                    'row_index': idx,
                    'error': str(e),
                    'data': {k: str(v) if not pd.isna(v) else "NULL" for k, v in row.to_dict().items()}
                }
                error_records.append(error_info)
                logger.warning(f"Validation error in row {idx}: {e}")

        # Create a new dataframe from validated records
        if valid_records:
            validated_df = pd.DataFrame(valid_records)
        else:
            validated_df = pd.DataFrame(columns=list(db_columns - {'id', 'created_at', 'updated_at'}))

        # Log summary of validation results
        logger.info(f"Validation complete: {len(valid_records)} valid rows, {len(error_records)} errors")
        if error_records:
            error_summary = {}
            for err in error_records:
                error_msg = err['error']
                if error_msg not in error_summary:
                    error_summary[error_msg] = 1
                else:
                    error_summary[error_msg] += 1

            logger.info("Error summary:")
            for error, count in error_summary.items():
                logger.info(f"  - {error}: {count} occurrences")

        return validated_df, error_records


class WorkbookImporter:
    """
    Class for importing data from Excel workbooks into PostgreSQL database tables.
    """

    def __init__(self, db_config: DatabaseConfig):
        """
        Initialize the workbook importer.

        Args:
            db_config: DatabaseConfig instance
        """
        self.db_config = db_config

    def import_workbook(
        self,
        table_name: str,
        workbook_path: str,
        sheet_name: Optional[str] = None,
        key_columns: Optional[List[str]] = None,
        batch_size: int = 100,
        dataframe: Optional[pd.DataFrame] = None,
        disable_triggers: bool = True
    ) -> Dict:
        """
        Import data from an Excel workbook into a database table.

        Args:
            table_name: Fully qualified table name (schema.table)
            workbook_path: Path to the Excel workbook
            sheet_name: Name of the sheet to import (optional)
            key_columns: List of columns to use as identifying keys for updates
            batch_size: Number of records to process in each batch
            dataframe: Optional dataframe to import instead of reading from workbook

        Returns:
            Dictionary with import statistics
        """
        # Validate workbook exists
        if not os.path.exists(workbook_path):
            raise FileNotFoundError(f"Workbook not found: {workbook_path}")

        logger.info(f"Loading workbook: {workbook_path}")

        # Track statistics
        stats = {
            'total_rows': 0,
            'valid_rows': 0,
            'error_rows': 0,
            'inserted': 0,
            'updated': 0,
            'errors': []
        }

        try:
            # 1. Test database connection
            logger.info("Testing database connection...")
            try:
                with get_db_connection(self.db_config) as conn:
                    logger.info(f"Database connection successful.")
                    logger.info(f"PostgreSQL server version: {conn.server_version}")

                    # Disable triggers before processing any data
                    # with conn.cursor() as cursor:
                    #     logger.info("Disabling triggers for import operation...")
                    #     cursor.execute("SET session_replication_role = 'replica';")
                    #     conn.commit()
            except Exception as e:
                logger.error(f"Error connecting to database: {e}")
                raise RuntimeError(f"Database connection failed: {e}")

            # 2. Load Excel file
            logger.info(f"Reading Excel file: {workbook_path}")
            try:
                if dataframe is None:
                    if sheet_name:
                        logger.info(f"Reading sheet: {sheet_name}")
                        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
                    else:
                        # If no sheet specified, use the first sheet
                        logger.info("No sheet specified, using first sheet")
                        df = pd.read_excel(workbook_path)
                else:
                    df = dataframe

                stats['total_rows'] = len(df)
                logger.info(f"Loaded {stats['total_rows']} rows from workbook")
                logger.info(f"Columns in workbook: {', '.join(df.columns.tolist())}")
            except Exception as e:
                logger.error(f"Error reading Excel file: {e}")
                raise RuntimeError(f"Failed to read Excel file: {e}")

            # 3. Clean column names
            df.columns = [str(col).strip() for col in df.columns]

            # 4. Initialize schema validator
            logger.info(f"Validating data against schema for {table_name}")
            try:
                validator = SchemaValidator(table_name, self.db_config)
            except Exception as e:
                logger.error(f"Error initializing schema validator: {e}")
                raise RuntimeError(f"Failed to initialize schema validator: {e}")

            # 5. Validate the data
            try:
                validated_df, errors = validator.validate_dataframe(df)
                stats['valid_rows'] = len(validated_df)
                stats['error_rows'] = len(errors)
                stats['errors'] = errors
                logger.info(f"Validation completed: {stats['valid_rows']} valid rows, {stats['error_rows']} errors")
            except Exception as e:
                logger.error(f"Error during data validation: {e}")
                raise RuntimeError(f"Data validation failed: {e}")

            # 6. If no valid records, return early
            if stats['valid_rows'] == 0:
                logger.warning("No valid records found in workbook")
                return stats

            # 7. Insert or update the data
            logger.info(f"Beginning database import to {table_name}")
            schema_name, table_name_only = table_name.split('.') if '.' in table_name else (None, table_name)

            # 8. Process in batches
            batch_count = 0
            for i in range(0, len(validated_df), batch_size):
                batch_count += 1
                batch_df = validated_df.iloc[i:i+batch_size]
                logger.info(f"Processing batch {batch_count} with {len(batch_df)} records")

                for _, row in batch_df.iterrows():
                    # Convert row to dict
                    row_dict = row.to_dict()

                    # Remove None values
                    row_dict = {k: v for k, v in row_dict.items() if pd.notna(v)}

                    # Check if record exists (if key columns provided)
                    if key_columns:
                        # Build filter condition
                        filter_conditions = []
                        for key in key_columns:
                            if key in row_dict:
                                value = row_dict[key]
                                if isinstance(value, str):
                                    filter_conditions.append(f"""{key} = '{value.replace("'", "''")}'""")

                                else:
                                    filter_conditions.append(f"{key} = {value}")

                        if filter_conditions:
                            # Try to update existing record
                            try:
                                # Check if record exists
                                check_query = f"SELECT COUNT(*) FROM {table_name} WHERE {' AND '.join(filter_conditions)}"
                                result = execute_query(check_query, config=self.db_config, fetch="one")
                                count = result['count'] if isinstance(result, dict) else result[0]

                                if count > 0:
                                    # Update existing record
                                    update_cols = []
                                    for k, v in row_dict.items():
                                        if k not in key_columns:  # Don't update key columns
                                            if isinstance(v, str):
                                                update_cols.append(f"""{k} = '{v.replace("'", "''")}'""")


                                            elif isinstance(v, (int, float)):
                                                update_cols.append(f"{k} = {v}")

                                    if update_cols:
                                        update_query = f"UPDATE {table_name} SET {', '.join(update_cols)} WHERE {' AND '.join(filter_conditions)}"
                                        execute_query(update_query, config=self.db_config, fetch=None)
                                        stats['updated'] += 1
                                        continue  # Skip to next record
                            except Exception as e:
                                logger.error(f"Error checking/updating record: {e}")
                                stats['errors'].append(f"Update error: {e}")
                                continue

                    # If we get here, insert a new record
                    try:
                        cols = ', '.join(row_dict.keys())
                        vals = []
                        for v in row_dict.values():
                            if isinstance(v, str):
                                vals.append(f"""'{v.replace("'", "''")}'""")


                            elif isinstance(v, (int, float)):
                                vals.append(str(v))
                            else:
                                vals.append("NULL")

                        insert_query = f"INSERT INTO {table_name} ({cols}) VALUES ({', '.join(vals)})"
                        execute_query(insert_query, config=self.db_config, fetch=None)
                        stats['inserted'] += 1
                    except Exception as e:
                        logger.error(f"Error inserting record: {e}")
                        logger.error(f"Failed query: {insert_query if 'insert_query' in locals() else 'N/A'}")
                        stats['errors'].append(f"Insert error: {e}")
                        continue

                logger.info(f"Batch {batch_count} processed: {stats['inserted']} inserts, {stats['updated']} updates")


            # After processing all batches, re-enable triggers
            # try:
            #     with get_db_connection(self.db_config) as conn:
            #         with conn.cursor() as cursor:
            #             logger.info("Re-enabling triggers after import operation...")
            #             cursor.execute("SET session_replication_role = 'origin';")
            #             conn.commit()
            # except Exception as e:
            #     logger.error(f"Error re-enabling triggers: {e}")
            #     # Continue with returning stats, don't raise here

            logger.info(f"Import completed successfully")

            return stats

        except Exception as e:
            logger.error(f"Error during workbook import: {e}")

            # IMPORTANT: Make sure to re-enable triggers even if there's an error
            try:
                with get_db_connection(self.db_config) as conn:
                    with conn.cursor() as cursor:
                        logger.info("Re-enabling triggers after error...")
                        cursor.execute("SET session_replication_role = 'origin';")
                        conn.commit()
            except Exception as re_enable_error:
                logger.error(f"Error re-enabling triggers after error: {re_enable_error}")

            stats['errors'].append(f"Import error: {e}")
            return stats

def get_table_columns(engine, table_name, schema='public'):
    """
    Get the column names for a table in the database.

    Args:
        engine: SQLAlchemy engine
        table_name: Name of the table
        schema: Schema name

    Returns:
        List of column names
    """
    inspector = inspect(engine)
    return [column['name'] for column in inspector.get_columns(table_name, schema=schema)]


def get_db_engine(db_config=None):
    """
    Create a SQLAlchemy engine for database operations.

    Args:
        db_config: Database configuration object

    Returns:
        SQLAlchemy engine
    """
    if db_config is None:
        db_config = DatabaseConfig()

    # Create SQLAlchemy engine
    db_url = f"postgresql://{db_config.user}:{db_config.password}@{db_config.host}:{db_config.port}/{db_config.database}"
    return create_engine(db_url)


