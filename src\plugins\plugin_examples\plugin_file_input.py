import os
from pathlib import Path


def plugin_file_input_example(
    general_file: str = "",
    save_file: str = "output.txt",
    save_dir: str = "",
):
    """
    Example plugin demonstrating file and directory input functionality.

    This plugin shows how to use the special parameter names that will
    automatically get file/directory picker UI elements in the plugin dialog.

    Args:
        general_file: Input file to analyze (uses file picker)
        save_file: Output file to save results (uses save file picker)
        save_dir: Directory to save additional results (uses directory picker)

    Returns:
        str: Summary of the selected files and directories
    """
    results = []

    # Process input file
    results.append("=== INPUT FILE ===")
    if not general_file:
        results.append("No input file selected.")
    else:
        results.append(f"Selected file: {general_file}")

        # Check if file exists
        if os.path.isfile(general_file):
            file_size = os.path.getsize(general_file)
            file_size_kb = file_size / 1024
            file_size_mb = file_size_kb / 1024

            results.append(f"File exists: Yes")
            results.append(f"File size: {file_size} bytes ({file_size_kb:.2f} KB, {file_size_mb:.2f} MB)")

            # Get file stats
            file_stats = os.stat(general_file)
            results.append(f"Created: {os.path.getctime(general_file)}")
            results.append(f"Last modified: {os.path.getmtime(general_file)}")

            # Get file extension
            _, ext = os.path.splitext(general_file)
            results.append(f"File extension: {ext}")

            # Preview file content (first 5 lines)
            try:
                with open(general_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:5]
                    if lines:
                        results.append("\nFile preview (first 5 lines):")
                        for i, line in enumerate(lines):
                            results.append(f"  {i+1}: {line.rstrip()}")
                        if len(lines) == 5:
                            results.append("  ...")
            except Exception as e:
                results.append(f"Could not read file content: {str(e)}")
        else:
            results.append(f"File exists: No")

    # Process save file
    results.append("\n=== OUTPUT FILE ===")
    if not save_file:
        results.append("No output file selected.")
    else:
        results.append(f"Selected output file: {save_file}")

        # Check if file exists
        if os.path.isfile(save_file):
            results.append(f"File exists: Yes (will be overwritten)")
        else:
            results.append(f"File exists: No (will be created)")

        # Check if directory exists
        save_file_dir = os.path.dirname(save_file)
        if save_file_dir and not os.path.exists(save_file_dir):
            results.append(f"Warning: Directory {save_file_dir} does not exist")

    # Process directory
    results.append("\n=== DIRECTORY ===")
    if not save_dir:
        results.append("No directory selected.")
    else:
        results.append(f"Selected directory: {save_dir}")

        # Check if directory exists
        if os.path.isdir(save_dir):
            results.append(f"Directory exists: Yes")

            # Count files in directory
            try:
                files = os.listdir(save_dir)
                file_count = len(files)
                results.append(f"Contains {file_count} files/directories")

                # List first 5 files
                if files:
                    results.append("\nDirectory contents (first 5 items):")
                    for i, file in enumerate(files[:5]):
                        full_path = os.path.join(save_dir, file)
                        if os.path.isdir(full_path):
                            results.append(f"  {i+1}: {file}/ (directory)")
                        else:
                            file_size = os.path.getsize(full_path)
                            results.append(f"  {i+1}: {file} ({file_size} bytes)")
                    if len(files) > 5:
                        results.append(f"  ... and {len(files) - 5} more items")
            except Exception as e:
                results.append(f"Could not read directory contents: {str(e)}")
        else:
            results.append(f"Directory exists: No")

    # Join all results into a single string
    return "\n".join(results)