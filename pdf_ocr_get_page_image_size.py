import fitz  # PyMuPDF
from PIL import Image, ImageEnhance, ImageFilter
import os
import subprocess
import tempfile

file = r"C:/Drawings/Clients/axisindustries/Axis 2025-08-14 - ENTERPRISE/received/Binder1 (4).pdf"

print("Testing multiple PDF rendering libraries for best quality...\n")
import pypdfium2 as pdfium

def render_first_page_png(pdf_path: str, out_png: str, scale=2.0):
    pdf = pdfium.PdfDocument(pdf_path)
    page = pdf.get_page(0)
    bitmap = page.render(scale=scale)
    pil_image = bitmap.to_pil()
    pil_image.save(out_png)
    page.close(); pdf.close()

render_first_page_png(file, f"debug/temp_images/page_1_pypdfium2.png", scale=2)