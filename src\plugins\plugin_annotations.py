def plugin_extract_annotations(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf"):
    """
    Extracts annotations from a PDF file using pypdf.

    This plugin extracts various types of annotations including:
    - Text annotations (comments)
    - Highlight annotations
    - Underline annotations
    - Square/circle annotations
    - Line annotations
    - Ink annotations (free drawing)

    Returns a DataFrame containing annotation details.

    Args:
        pdf_file (str): Path to the PDF file to analyze

    Returns:
        pandas.DataFrame: DataFrame containing annotation details
    """
    import pandas as pd
    import os
    from pypdf import PdfReader
    import json
    from datetime import datetime

    # Check if file exists
    if not os.path.exists(pdf_file):
        print(f"File not found: {pdf_file}")
        return None

    # Initialize lists to store annotation data
    annotations = []

    try:
        # Open the PDF file
        reader = PdfReader(pdf_file)

        # Process each page
        for page_num, page in enumerate(reader.pages):

            # Check if the page has annotations
            if '/Annots' in page:
                page_annots = page['/Annots']

                # Process each annotation on the page
                for i, annot in enumerate(page_annots):
                    if isinstance(annot, dict):
                        annot_obj = annot
                    else:
                        # If it's an indirect reference, resolve it
                        annot_obj = annot.get_object()

                    # Skip if not a valid annotation
                    if not isinstance(annot_obj, dict):
                        continue

                    # Get annotation type
                    subtype = annot_obj.get('/Subtype', '')
                    if isinstance(subtype, str):
                        subtype = subtype
                    else:
                        subtype = str(subtype)

                    # Get annotation rectangle (position)
                    rect = annot_obj.get('/Rect', [0, 0, 0, 0])

                    # Get annotation content based on type
                    contents = ""
                    if '/Contents' in annot_obj:
                        contents = annot_obj['/Contents']

                    # Get creation date if available
                    creation_date = ""
                    if '/CreationDate' in annot_obj:
                        creation_date = annot_obj['/CreationDate']

                    # Get author if available
                    author = ""
                    if '/T' in annot_obj:
                        author = annot_obj['/T']

                    # Get color if available
                    color = []
                    if '/C' in annot_obj:
                        color = annot_obj['/C']

                    # Get quadpoints for highlight annotations
                    quadpoints = []
                    if '/QuadPoints' in annot_obj:
                        quadpoints = annot_obj['/QuadPoints']

                    # Get vertices for ink annotations
                    vertices = []
                    if '/InkList' in annot_obj:
                        vertices = annot_obj['/InkList']

                    # Collect annotation data
                    annotation = {
                        'page': page_num + 1,
                        'id': i + 1,
                        'type': subtype,
                        'contents': contents,
                        'rect': rect,
                        'author': author,
                        'creation_date': creation_date,
                        'color': color,
                        'quadpoints': quadpoints if quadpoints else None,
                        'vertices': vertices if vertices else None
                    }

                    annotations.append(annotation)

    except Exception as e:
        print(f"Error processing PDF: {str(e)}")
        return None

    # Convert to DataFrame
    if annotations:
        # Convert complex objects to strings for DataFrame
        for i, annot in enumerate(annotations):
            for key, value in annot.items():
                if isinstance(value, (list, dict)):
                    annotations[i][key] = json.dumps(value)

        df = pd.DataFrame(annotations)

        # Print summary
        print(f"Found {len(annotations)} annotations in {pdf_file}")
        print(f"Annotation types: {df['type'].value_counts().to_dict()}")

        return df
    else:
        print(f"No annotations found in {pdf_file}")
        return pd.DataFrame()


def plugin_extract_annotations_pdfplumber(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf"):
    """
    Extracts annotations from a PDF file using pdfplumber.

    This plugin provides an alternative method for extracting annotations
    using the pdfplumber library, which may capture different annotation types
    than pypdf.

    Args:
        pdf_file (str): Path to the PDF file to analyze

    Returns:
        pandas.DataFrame: DataFrame containing annotation details
    """
    import pandas as pd
    import os
    import pdfplumber
    import json

    # Check if file exists
    if not os.path.exists(pdf_file):
        print(f"File not found: {pdf_file}")
        return None

    # Initialize lists to store annotation data
    annotations = []

    try:
        # Open the PDF file
        with pdfplumber.open(pdf_file) as pdf:

            # Process each page
            for page_num, page in enumerate(pdf.pages):

                # Get annotations from page
                annots = page.annots

                if annots:
                    for i, annot in enumerate(annots):
                        # Extract annotation data
                        annotation = {
                            'page': page_num + 1,
                            'id': i + 1,
                            'type': annot.get('subtype', ''),
                            'contents': annot.get('contents', ''),
                            'rect': annot.get('rect', []),
                            'data': json.dumps(annot)  # Store all data
                        }

                        annotations.append(annotation)

    except Exception as e:
        print(f"Error processing PDF with pdfplumber: {str(e)}")
        return None

    # Convert to DataFrame
    if annotations:
        df = pd.DataFrame(annotations)

        # Print summary
        print(f"Found {len(annotations)} annotations with pdfplumber in {pdf_file}")
        print(f"Annotation types: {df['type'].value_counts().to_dict()}")

        return df
    else:
        print(f"No annotations found with pdfplumber in {pdf_file}")
        return pd.DataFrame()


def plugin_extract_annotations_pymupdf(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf"):
    """
    Extracts annotations from a PDF file using PyMuPDF (fitz).

    This plugin provides another method for extracting annotations
    using the PyMuPDF library, which may capture different annotation types
    than pypdf or pdfplumber.

    Args:
        pdf_file (str): Path to the PDF file to analyze

    Returns:
        pandas.DataFrame: DataFrame containing annotation details
    """
    import pandas as pd
    import os
    import fitz  # PyMuPDF
    import json

    # Check if file exists
    if not os.path.exists(pdf_file):
        print(f"File not found: {pdf_file}")
        return None

    # Initialize lists to store annotation data
    annotations = []

    try:
        # Open the PDF file
        doc = fitz.open(pdf_file)

        # Process each page
        for page_num, page in enumerate(doc):
            # Get annotations from page
            annots = page.annots()

            if annots:
                for i, annot in enumerate(annots):
                    # Get annotation info
                    info = annot.info

                    # Get annotation rectangle
                    rect = annot.rect

                    # Get annotation type
                    annot_type = annot.type[1]  # Get type name without the prefix

                    # Get annotation content
                    content = info.get("content", "")

                    # Get creation date
                    creation_date = info.get("creationDate", "")

                    # Get author/title
                    author = info.get("title", "")

                    # Get colors if available
                    colors = {
                        "stroke_color": annot.colors.get("stroke"),
                        "fill_color": annot.colors.get("fill"),
                    }

                    # Get vertices for various annotation types
                    vertices = None
                    if annot_type in ["Ink", "Line", "PolyLine"]:
                        try:
                            vertices = annot.vertices
                        except:
                            pass

                    # Get highlight points
                    quad_points = None
                    if annot_type in ["Highlight", "Underline", "StrikeOut"]:
                        try:
                            quad_points = annot.vertices
                        except:
                            pass

                    # Collect annotation data
                    annotation = {
                        'page': page_num + 1,
                        'id': i + 1,
                        'type': annot_type,
                        'contents': content,
                        'rect': [rect.x0, rect.y0, rect.x1, rect.y1],
                        'author': author,
                        'creation_date': creation_date,
                        'colors': colors,
                        'vertices': vertices,
                        'quad_points': quad_points,
                        'flags': annot.flags,
                    }

                    annotations.append(annotation)

        # Close the document
        doc.close()

    except Exception as e:
        print(f"Error processing PDF with PyMuPDF: {str(e)}")
        return None

    # Convert to DataFrame
    if annotations:
        # Convert complex objects to strings for DataFrame
        for i, annot in enumerate(annotations):
            for key, value in annot.items():
                if isinstance(value, (list, dict)):
                    annotations[i][key] = json.dumps(value)

        df = pd.DataFrame(annotations)

        # Print summary
        print(f"Found {len(annotations)} annotations with PyMuPDF in {pdf_file}")
        print(f"Annotation types: {df['type'].value_counts().to_dict()}")

        return df
    else:
        print(f"No annotations found with PyMuPDF in {pdf_file}")
        return pd.DataFrame()


def plugin_compare_annotation_methods(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf",
                                     save_results: bool=True,
                                     output_dir: str="debug/results"):
    """
    Compares different methods of extracting annotations from a PDF file.

    This plugin runs pypdf, pdfplumber, and PyMuPDF extraction methods and compares
    the results to help determine which method works best for your specific PDF files.

    Args:
        pdf_file (str): Path to the PDF file to analyze
        save_results (bool): Whether to save results to Excel files
        output_dir (str): Directory to save output files (will be created if it doesn't exist)

    Returns:
        dict: Dictionary containing results from all methods
    """
    import pandas as pd
    import os
    import datetime

    print(f"Analyzing annotations in {pdf_file}")
    print("-" * 50)

    # Create timestamp for filenames
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Get PDF filename without path or extension
    pdf_basename = os.path.splitext(os.path.basename(pdf_file))[0]

    # Extract annotations using pypdf
    print("Method 1: Using pypdf")
    pypdf_results = plugin_extract_annotations(pdf_file)

    print("\nMethod 2: Using pdfplumber")
    pdfplumber_results = plugin_extract_annotations_pdfplumber(pdf_file)

    print("\nMethod 3: Using PyMuPDF (fitz)")
    pymupdf_results = plugin_extract_annotations_pymupdf(pdf_file)

    # Compare results
    pypdf_count = len(pypdf_results) if isinstance(pypdf_results, pd.DataFrame) else 0
    pdfplumber_count = len(pdfplumber_results) if isinstance(pdfplumber_results, pd.DataFrame) else 0
    pymupdf_count = len(pymupdf_results) if isinstance(pymupdf_results, pd.DataFrame) else 0

    print("\nComparison:")
    print(f"pypdf found: {pypdf_count} annotations")
    print(f"pdfplumber found: {pdfplumber_count} annotations")
    print(f"PyMuPDF found: {pymupdf_count} annotations")

    # Save results to Excel if requested
    if save_results:
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")

        # Save pypdf results
        if isinstance(pypdf_results, pd.DataFrame) and not pypdf_results.empty:
            pypdf_filename = os.path.join(output_dir, f"{pdf_basename}_pypdf_{timestamp}.xlsx")
            pypdf_results.to_excel(pypdf_filename, index=False)
            print(f"Saved pypdf results to: {pypdf_filename}")

        # Save pdfplumber results
        if isinstance(pdfplumber_results, pd.DataFrame) and not pdfplumber_results.empty:
            pdfplumber_filename = os.path.join(output_dir, f"{pdf_basename}_pdfplumber_{timestamp}.xlsx")
            pdfplumber_results.to_excel(pdfplumber_filename, index=False)
            print(f"Saved pdfplumber results to: {pdfplumber_filename}")

        # Save PyMuPDF results
        if isinstance(pymupdf_results, pd.DataFrame) and not pymupdf_results.empty:
            pymupdf_filename = os.path.join(output_dir, f"{pdf_basename}_pymupdf_{timestamp}.xlsx")
            pymupdf_results.to_excel(pymupdf_filename, index=False)
            print(f"Saved PyMuPDF results to: {pymupdf_filename}")

        # Save comparison summary
        comparison_df = pd.DataFrame({
            'Method': ['pypdf', 'pdfplumber', 'PyMuPDF'],
            'Annotations Found': [pypdf_count, pdfplumber_count, pymupdf_count],
            'PDF File': [pdf_file, pdf_file, pdf_file],
            'Timestamp': [timestamp, timestamp, timestamp]
        })

        comparison_filename = os.path.join(output_dir, f"{pdf_basename}_comparison_{timestamp}.xlsx")
        comparison_df.to_excel(comparison_filename, index=False)
        print(f"Saved comparison summary to: {comparison_filename}")

    return {
        "pypdf_results": pypdf_results,
        "pdfplumber_results": pdfplumber_results,
        "pymupdf_results": pymupdf_results
    }