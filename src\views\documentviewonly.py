"""
PDF viewer - read only

"""
import os
import fitz
import cv2
import pandas as pd

from src.utils.logger import logger

from PySide6.QtCore import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from pubsub import pub

from src.pyside_util import applyDropShadowEffect
from src.app_paths import getSourceOcrPath, getSourceRawDataPath
from src.atom.fast_storage import load_df_fast
from functools import partial

try:
    from src.pyside_util import get_resource_qicon
    from src import app_paths
except:
    get_resource_qicon = QIcon


class HoverRectItem(QGraphicsRectItem, QObject):

    hovered = Signal(bool)
    def __init__(self, start, end, text=""):
        QObject.__init__(self)
        super().__init__(None)
        self.setRect(QRect(start, end))
        self.setAcceptHoverEvents(True)
        self.hovered.connect(self.onHover)
        self.text = text

    def hoverEnterEvent(self, event):
        self.hovered.emit(True)
        return super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        self.hovered.emit(False)
        return super().hoverLeaveEvent(event)

    def onHover(self, hovered: bool):
        brush = self.brush()
        color = brush.color()
        color.setAlphaF(0.3 if hovered else 0.1)
        brush.setColor(color)
        self.setBrush(brush)


class PdfScene(QGraphicsScene):

    sgnRemoveItem = Signal(object)

    def __init__(self):
        super().__init__()
        self.sgnRemoveItem.connect(self.onRemoveItem)

    def onRemoveItem(self, item):
        self.removeItem(item)


class OverlayShortcutHelper(QWidget):

    def __init__(self, parent: QGraphicsView) -> None:
        pass


class OverlayHint(QWidget):

    sgnSetHint = Signal(str)

    MAX_OPACITY = 0.8

    SHORTCUT_HINT = "Shorctuts: Ctrl +/- Zoom"
    ACTIVE_SHORTCUT_HINT = ""

    def __init__(self, parent: QGraphicsView) -> None:
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        self.hint = QLabel("This is an overlay hint")
        self.hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hint.setContentsMargins(16, 8, 16, 8)
        self.layout().addWidget(self.hint)
        self.setMinimumHeight(64)
        self.setObjectName("overlayHint")
        self.hint.setObjectName("overlayHint")
        self.sgnSetHint.connect(self.onSetHint)

        # Ignores any mouse input
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setWindowFlags(Qt.FramelessWindowHint)

        self.effect: QGraphicsOpacityEffect = QGraphicsOpacityEffect(self)
        self.effect.setOpacity(0.0)
        self.setGraphicsEffect(self.effect)

        duration = 400
        self.animShow = QVariantAnimation(self.hint)
        self.animShow.setDuration(duration)
        self.animShow.valueChanged.connect(self.onOpacityUpdate)

    def update(self):
        """ Align hint centrally on bottom """
        geom = self.parent().geometry()
        margin = 32
        self.move((geom.width()//2)-(self.width()//2),
                  geom.height()-self.height()-margin)

    def onSetHint(self, text: str):
        """ Display new message and resize to width of string """
        self.hint.setText(text)
        self.adjustSize()
        self.update()
        self.startShowAnimation()

    def onOpacityUpdate(self, value):
        if value:
            self.effect.setOpacity(value)

    def startShowAnimation(self):
        try:
            self.animShow.finished.disconnect(self.onShowAnimationFinished)
        except Exception as e:
            pass
        self.animShow.setStartValue(min(0.6, self.effect.opacity()))
        self.animShow.setEndValue(self.MAX_OPACITY)
        self.animShow.setDirection(self.animShow.Direction.Forward)
        self.animShow.finished.connect(self.onShowAnimationFinished)
        self.animShow.start()

    def startHideAnimation(self):
        """ TODO - why is the valuechanged for hiding not passing a float """
        self.effect.setOpacity(0)

    def onShowAnimationFinished(self):
        self.animShow.finished.disconnect(self.onShowAnimationFinished)
        anim = QVariantAnimation(self.hint)
        anim.setDuration(1500) # Show time before hiding again
        anim.finished.connect(self.startHideAnimation)
        anim.start()


class ZoomSlider(QWidget):

    def __init__(self, parent: QGraphicsView):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("plus.svg"))
        pb.clicked.connect(self.onZoomIn)
        self.layout().addWidget(pb)
        self.slider = QSlider()
        self.slider.valueChanged.connect(self.onSlider)
        self.layout().addWidget(self.slider)
        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("minus.svg"))
        pb.clicked.connect(self.onZoomOut)
        self.layout().addWidget(pb)
        self.setFixedSize(32, 144)
        self.setObjectName("zoomSlider")

    @property
    def scaleDelta(self):
        return self.parent().scaleDelta

    @property
    def minScale(self):
        return self.parent().minScale

    @property
    def maxScale(self):
        return self.parent().maxScale

    def update(self):
        geom = self.parent().geometry()
        margin = 16
        self.move(geom.width()-self.width()-margin,
                  geom.height()-self.height()-margin)
        self.slider.setMaximum(self.maxScale * 100)
        self.slider.setMinimum(self.minScale * 100)
        self.slider.setValue(self.scaleDelta * 100)

    def onZoomIn(self):
        self.parent().zoomIn()

    def onZoomOut(self):
        self.parent().zoomOut()

    def onSlider(self):
        scale = self.slider.value() / 100
        self.parent().scaleDelta = scale


class PdfViewer(QGraphicsView):
    """ PDF viewer and editor """

    sgnLoadPdfRecord = Signal(object) # pdf record
    sgnPdfLoaded = Signal(bool) # Success (True) / Fail (False)
    sgnSetContextMenu = Signal(object)
    sgnUpdateStatus = Signal(str, object)
    sgnZoomUpdated = Signal(float)
    sgnShowEditRoiDialog = Signal(object, bool)
    sgnUpdateCursor = Signal(object)
    sgnRoiModified = Signal()
    roiHovered = Signal()


    roiMotionListener = None

    # dpi = 1500
    dpi = 144

    maxScale = 4
    minScale = 0.1

    def __init__(self, parent):
        super().__init__()
        self.setScene(PdfScene())
        self.data: dict = {}
        self.parent = parent
        self.pages: int = None
        self.page: int = None
        self.pageWidth = None
        self.pageHeight = None
        self._roiDrawEnabled: bool = False
        self.roiStart, self.roiEnd = None, None
        self._scaleDelta: float = 1
        self.currentPdfPath = None
        self.infoGroup = None

        self.zoomSlider = ZoomSlider(self)
        self.zoomSlider.move(500,500)
        self.zoomSlider.show()
        self.overlayHint = OverlayHint(self)
        self.overlayHint.show()

        self._currHoverRegion = None
        self._currHoverRoi = None
        self._currRegion = None
        self.roiHovered.connect(self.onRoiHovered)

        self.setDragMode(self.DragMode.NoDrag)
        self.sgnLoadPdfRecord.connect(self.loadPdfRecord)
        self.setMouseTracking(True)
        self.setEnabled(False)
        self.show()

        # Signals
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.sgnUpdateCursor.connect(lambda x: self.setCursor(QCursor(x)))

        self.setObjectName("pdfViewer")

    @property
    def scaleDelta(self) -> float:
        try:
            return self._scaleDelta
        except Exception as e:
            logger.error(f"Error adjusting scale: {e}", exc_info=True)

    @scaleDelta.setter
    def scaleDelta(self, value):
        try:
            self.resetTransform()
            self._scaleDelta = value
            self.scale(self._scaleDelta, self._scaleDelta)
        except Exception as e:
            logger.error(f"Error adjusting scale: {e}", exc_info=True)

    def onUpdateStatus(self, key, object):
        self.parent.setStatusKey(key, object)

    def convertToPdfPoint(self, point: QPoint) -> QPoint:
        """ Returns a point given scrolled offset position of the viewer """
        try:
            point = QPoint(int(point.x()), int(point.y()))
            pos = self.mapToScene(point)
            return QPoint(int(pos.x()), int(pos.y()))
        except Exception as e:
            logger.error(f"Error converting screen point to PDF point: {e}", exc_info=True)

    def mouseMoveEvent(self, event: QMouseEvent) -> None:

        super().mouseMoveEvent(event) # TODO - Review if safe to keep this

        if self.dragMode() == self.DragMode.ScrollHandDrag:
            return super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        try:
            if event.button() == Qt.MouseButton.MiddleButton:
                self.setDragMode(QGraphicsView.DragMode.NoDrag)
                return
        except Exception as e:
            logger.error(f"Error during mouse release: {e}", exc_info=True)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        try:
            # Allow panning only if not currently drawing roi
            if event.button() == Qt.MouseButton.MiddleButton:
                self.setDragMode(QGraphicsView.DragMode.ScrollHandDrag)
                self.overlayHint.sgnSetHint.emit("Drag mouse to pan")
                handmade_event = QMouseEvent(
                    QEvent.Type.MouseButtonPress,
                    QPointF(event.pos()), Qt.MouseButton.LeftButton,
                    event.buttons(), event.modifiers())
                return super().mousePressEvent(handmade_event)

            pos = self.convertToPdfPoint(event.pos())
        except Exception as e:
            logger.error(f"Error during mouse press: {e}", exc_info=True)

    def hideScrollbars(self):
        try:
            v = Qt.ScrollBarPolicy.ScrollBarAlwaysOff
            self.setVerticalScrollBarPolicy(v)
            self.setHorizontalScrollBarPolicy(v)
        except Exception as e:
            logger.error(f"Error hiding scrollbars: {e}", exc_info=True)

    def showScrollbars(self):
        try:
            v = Qt.ScrollBarPolicy.ScrollBarAlwaysOn
            self.setVerticalScrollBarPolicy(v)
            self.setHorizontalScrollBarPolicy(v)
        except Exception as e:
            logger.error(f"Error showing scrollbars: {e}", exc_info=True)

    def setPage(self, page: int):
        try:
            if page <= 0:
                raise ValueError("Invalid page number")
            self.scaleDelta = 1
            zoom = self.dpi / 72  # Default DPI in PyMuPDF is 72
            mat = fitz.Matrix(zoom, zoom)
            page = self._pdf.load_page(0)  # Load the first page
            pix = page.get_pixmap(matrix=mat) # dpi=1200
            qt_img = QImage(pix.samples, pix.width, pix.height, pix.stride, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qt_img)
            self.pageWidth, self.pageHeight = pix.width, pix.height
            self.scene().clear()
            self.setScene(PdfScene())
            self.pageItem = self.scene().addPixmap(pixmap)
            self.pageItem.setZValue(0)
            # TODO: self.sgnPdfPageChanged.emit
        except Exception as e:
            logger.error(f"Error setting page to {page}: {e}", exc_info=True)

    def loadPdf(self, filename, page=1):
        try:
            self.closePdf()
            self._pdf = fitz.open(filename)
            self.pages = self._pdf.page_count if self._pdf.page_count else None
            self.setPage(0)

            #Store The successfully loaded PDF Path #Added
            self.currentPdfPath = filename

            # Update UI to allow PDF editing
            self.sgnPdfLoaded.emit(True)

            # Cleanup - Potential crashes caused by keeping instance of opened PyMuPDF
            self._pdf = None
        except Exception as e:
            logger.error(f"Failed to load PDF: {filename}. Error: {e}", exc_info=True)
            self.sgnPdfLoaded.emit(False)

    def loadPdfRecord(self, record):
        try:
            self.scene().clear()
            self.infoGroup = None
            self.filename = record["originalFilename"]
            self.page = record["page_number"]
            pixmap = record["document"]
            self.pageWidth, self.pageHeight = pixmap.width(), pixmap.height()
            self.setScene(PdfScene())
            self.pageItem = self.scene().addPixmap(pixmap)
            self.pageItem.setZValue(0)
            self.currentPdfPath = record["documentName"]
            self.sgnPdfLoaded.emit(True)
            self.fitPage()
        except Exception as e:
            logger.error(f"Failed to load PDF: {record['id']}. Error: {e}", exc_info=True)
            self.sgnPdfLoaded.emit(False)

    def resizeEvent(self, event: QResizeEvent) -> None:
        try:
            self.resetTransform()
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.update()
        except Exception as e:
            logger.error(f"Error handling resize event: {e}", exc_info=True)

    def fitPage(self):
        try:
            """ TODO: Fit page to available client space """
            # self.pdfViewer.fitInView(self.pdfViewer.pageItem, Qt.AspectRatioMode.KeepAspectRatio)
            clientSize = self.viewport().size()
            self.resetTransform()
            sx = clientSize.width() / self.pageWidth
            sy = clientSize.height() / self.pageHeight
            sd = min(sx, sy)
            self.scaleDelta = sd
        except Exception as e:
            logger.error(f"Error fitting page: {e}", exc_info=True)

    def zoomTo(self, scale: float):
        try:
            self.scaleDelta = scale
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.resizeEvent(None)
        except Exception as e:
            logger.error(f"Error zooming in: {e}", exc_info=True)

    def zoomIn(self):
        try:
            self.resetTransform()
            self.scaleDelta += 0.03
            self.scaleDelta = min(self.maxScale, self.scaleDelta)
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.sgnSetHint.emit("Zooming In")
        except Exception as e:
            logger.error(f"Error zooming in: {e}", exc_info=True)

    def zoomOut(self):
        try:
            self.resetTransform()
            self.scaleDelta -= 0.03
            self.scaleDelta = max(self.minScale, self.scaleDelta)
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.sgnSetHint.emit("Zooming Out")
        except Exception as e:
            logger.error(f"Error zooming out: {e}", exc_info=True)

    def relativeScrollX(self) -> float:
        try:
            return self.horizontalScrollBar().value() / self.horizontalScrollBar().maximum()
        except:
            return 0

    def relativeScrollY(self) -> float:
        try:
            return self.verticalScrollBar().value() / self.verticalScrollBar().maximum()
        except:
            return 0

    def setRelativeScrollX(self, value) -> float:
        try:
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().maximum() * value)
        except:
            return 0

    def setRelativeScrollY(self, value) -> float:
        try:
            self.verticalScrollBar().setValue(self.verticalScrollBar().maximum() * value)
        except:
            return 0

    def drawRegions(self, regions, color: QColor = "lightgreen"):
        pen = QPen()
        pen.setColor(QColor(color))
        pen.setWidth(2)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)

        brush = QBrush()
        fill = pen.color()
        fill.setAlphaF(0.1)
        brush.setColor(fill)
        brush.setStyle(Qt.BrushStyle.SolidPattern)

        if not self.infoGroup:
            self.infoGroup = QGraphicsItemGroup()
            self.infoGroup.setHandlesChildEvents(False) # Propogate hover to child items
            self.scene().addItem(self.infoGroup)
            self.infoGroup.setZValue(99)

        n = 1
        for start, end, value in regions:
            try:
                item = HoverRectItem(start, end, value)
                item.hovered.connect(partial(self.onRegionHovered, value))
                n += 1
                self.scene().addItem(item)
                self.infoGroup.addToGroup(item)
                item.setPen(pen)
                item.setBrush(brush)
            except Exception as e:
                logger.error(f"Error adding ROI with values {start} - {end}: {e}", exc_info=True)

    def clearRegions(self):
        try:
            if self.infoGroup:
                self.scene().removeItem(self.infoGroup)
            self.infoGroup = None
        except Exception as e:
            self.infoGroup = None

    def onRegionHovered(self, value: str, hovered: bool):
        if hovered:
            self._currHoverRegion = value
        else:
            self._currHoverRegion = None
        self.roiHovered.emit()

    def onRoiHovered(self):
        if any([
                self.dragMode() != self.DragMode.NoDrag,
            ]):
            QToolTip.hideText()
            return
        if not self._currHoverRegion and not self._currHoverRoi:
            QToolTip.hideText()
            return

        text = ""
        if self._currHoverRoi:
            text += self._currHoverRoi
            if self._currHoverRegion:
                text += "\n"
        if self._currHoverRegion:
            text += "Text: " + self._currHoverRegion

        QToolTip.showText(QCursor.pos(), f"{text}")


class DocumentViewOnly(QWidget):

    sgnSetDockedMode = Signal(bool)
    sgnClose = Signal()
    pageReceived = Signal(dict)
    regionTextCopied = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("popup")
        self.setLayout(QVBoxLayout())

        self.data = {}
        self.pdfId = -1
        self.lastZoom = None
        self.lastScrollX = 0
        self.lastScrollY = 0

        # Top toolbar
        self.hboxTop = hboxTop = QWidget()
        hboxTop.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        hboxTop.setLayout(QHBoxLayout())
        self.layout().addWidget(hboxTop)
        label = QLabel("Document Viewer")
        label.setObjectName("headerLabel")
        hboxTop.layout().addWidget(label)

        hboxTop.layout().addWidget(QLabel("Dev Options: "))
        group = QWidget()
        group.setLayout(QGridLayout())

        self.pbKeepPosition = QPushButton("Keep Position")
        self.pbKeepPosition.setToolTip("Anchor to the previous zoom and position on page change")
        self.pbKeepPosition.setCheckable(True)
        self.pbKeepPosition.clicked.connect(self.onKeepPosition)
        group.layout().addWidget(self.pbKeepPosition, 0, 0)

        self.pbFollowSelection = QPushButton("Follow Selection")
        self.pbFollowSelection.setToolTip("Automatically view the page of the selected row")
        self.pbFollowSelection.setCheckable(True)
        self.pbFollowSelection.clicked.connect(self.onFollowSelection)
        group.layout().addWidget(self.pbFollowSelection, 0, 1)

        self.pbSimilarity = QPushButton("Similarity")
        self.pbSimilarity.setToolTip("Display similar items")
        self.pbSimilarity.setCheckable(True)
        self.pbSimilarity.clicked.connect(self.onSimilarity)
        group.layout().addWidget(self.pbSimilarity, 0, 2)

        self.pbShowRegions = QPushButton("Regions")
        self.pbShowRegions.setToolTip("Show raw and ocr regions")
        self.pbShowRegions.setCheckable(True)
        self.pbShowRegions.clicked.connect(self.onShowRegions)
        group.layout().addWidget(self.pbShowRegions, 1, 0)
        hboxTop.layout().addWidget(group)

        self.pbShowLayout = QPushButton("Layout")
        self.pbShowLayout.setToolTip("Draw ROI layout")
        self.pbShowLayout.setCheckable(True)
        self.pbShowLayout.clicked.connect(self.onShowLayout)
        group.layout().addWidget(self.pbShowLayout, 1, 1)
        hboxTop.layout().addWidget(group)

        self.pbCopyRegionText = QPushButton("Copy Region Text")
        self.pbCopyRegionText.setToolTip("Copy Region Text")
        self.pbCopyRegionText.setCheckable(True)
        group.layout().addWidget(self.pbCopyRegionText, 1, 2)
        hboxTop.layout().addWidget(group)

        spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        hboxTop.layout().addItem(spacer)

        self.pbDockToggle = pbDockToggle = QPushButton("Undock")
        applyDropShadowEffect(self.pbDockToggle, offset=(0, 1))
        pbDockToggle.setMinimumHeight(36)
        pbDockToggle.setIcon(get_resource_qicon("external-link.svg"))
        pbDockToggle.clicked.connect(self.onDockToggle)
        hboxTop.layout().addWidget(pbDockToggle)

        self.pbExit = pbExit = QPushButton("Close")
        applyDropShadowEffect(self.pbExit, offset=(0, 1))
        pbExit.setMinimumHeight(36)
        pbExit.setIcon(get_resource_qicon("x.svg"))
        pbExit.clicked.connect(lambda: self.sgnClose.emit())
        hboxTop.layout().addWidget(pbExit)

        # Siderbar and PDF Viewer
        self.splitter = QSplitter(self)
        self.splitter.setHandleWidth(0)
        self.layout().addWidget(self.splitter)

        rightWidget = QWidget()
        rightWidget.setLayout(QVBoxLayout())
        rightWidget.setStyleSheet("QGraphicsView { border: 3px solid #0080ff; border-radius: 3px}")
        self.pdfViewer = PdfViewer(self)
        rightWidget.layout().addWidget(self.pdfViewer)
        self.lblText = QLabel("")
        rightWidget.layout().addWidget(self.lblText)
        rightWidget.layout().setContentsMargins(0, 0, 0, 0)
        rightWidget.layout().setSpacing(0)
        rightWidget.setObjectName("pdfViewer")
        self.splitter.addWidget(rightWidget)
        self.splitter.setStretchFactor(1, 1)

        self.pdfViewer.sgnPdfLoaded.connect(self.onPdfLoaded)
        self.pdfViewer.setFocus()

        self.status = {}
        self.updateStatusBar()
        self.updateUi()

        self.pageReceived.connect(self.onPageReceived)

    @property
    def projectId(self):
        try:
            return self.data["project_id"]
        except:
            return None

    def setStatusKey(self, key, value):
        try:
            self.status[key] = value
            self.updateStatusBar()
        except Exception as e:
            logger.error(f"Error setting status key: {e}", exc_info=True)

    def closeEvent(self, event: QCloseEvent) -> None:
        self.sgnClose.emit()
        super().closeEvent(event)

    def updateStatusBar(self):
        try:
            text = []
            pos = self.status.get('pos')
            if (pos and (pos.x() > 0 and pos.x() < self.pdfViewer.pageWidth)
                and (pos.y() > 0 and pos.y() < self.pdfViewer.pageHeight)):
                pos = f"{pos.x()}, {pos.y()}, Rel: {round(pos.x()/self.pdfViewer.pageWidth, 3), round(pos.y()/self.pdfViewer.pageHeight, 3)}"
                text.append(f"Pos: {pos}")
            selected = self.roiSidebar.getSelectedItemRoi()
            if selected:
                rect = selected.bounds.rect()
                text.append(f"Selected: (x={rect.x()}, y={rect.y()}, w={rect.width()}, h={rect.height()})")

            data = { "type": "DocumentViewer", "params": {"pos": " ".join(text)} }
            pub.sendMessage("set-statusbar-message", data=data)
        except Exception as e:
            pass
            # logger.error(f"Error updating status bar: {e}", exc_info=True)

    def centerPage(self):
        return

    def eventFilter(self, source, event):
        """ Handle PDF zoom / scale """
        try:
            if source == self.pdfViewer.viewport():
                if event.type() == QEvent.Wheel:
                    # self.pdfViewer.setTransformationAnchor(QGraphicsView.NoAnchor)
                    # self.pdfViewer.setResizeAnchor(QGraphicsView.NoAnchor)
                    oldPos = self.pdfViewer.mapToScene(self.cursor().pos())
                    if event.angleDelta().y() > 0:
                        self.pdfViewer.zoomIn()
                    else:
                        self.pdfViewer.zoomOut()
                    return True

            return super().eventFilter(source, event)
        except Exception as e:
            logger.error(f"Error filtering events for zoom/scale handling: {e}", exc_info=True)

    def loadPdfById(self, pdfId: int):
        logger.info(f"User requested to view document {pdfId}")
        pub.sendMessage("document-viewer-get-pdf", data={"id": int(pdfId)}, callback=self.pageReceived.emit)

    def onPdfLoaded(self, success: bool):
        if success:
            filename = os.path.basename(self.pdfViewer.filename)
            page = self.pdfViewer.page
            text = f"{filename} - Page {page}"
            self.lblText.setText(text)
            self.pdfViewer.setEnabled(True)
        self.pdfViewer.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self.pdfViewer.scaleDelta = 1

    def onPageReceived(self, data: dict):
        self.data = data["pdf"]
        self.pdfId = data["id"]
        if data["ok"]:
            self.saveState()
            self.pixmap: cv2.Mat = self.data["document"].copy()
            self.pdfViewer.sgnLoadPdfRecord.emit(data["pdf"])
            self.restoreState()
        else:
            page = data["pdf"]["page_number"]
            filename = os.path.basename(data["pdf"]["originalFilename"])
            self.pdfViewer.scene().clear()
            self.lblText.setText(f"{filename} - Page {page} (Failed to load page preview)")

    def keyReleaseEvent(self, event: QKeyEvent) -> None:
        if (event.key() == Qt.Key.Key_Control):
            self.enableZoom(False)

    def keyPressEvent(self, event: QKeyEvent) -> None:
        if (event.key() == Qt.Key.Key_Delete):
            pass
        elif (event.key() == Qt.Key.Key_Control):
            self.enableZoom(True)

    def enableZoom(self, enabled: bool = True):
        """ Scroll wheel override to zoom in/out of PDF. """
        if enabled:
            self.pdfViewer.viewport().installEventFilter(self)
            self.pdfViewer.overlayHint.sgnSetHint.emit("Ctrl + Scroll Up <Zoom In>, Ctrl + Scroll Down <Zoom Out> ")
        else:
            self.pdfViewer.viewport().removeEventFilter(self)

    def onDockToggle(self):
        self.sgnSetDockedMode.emit(not self.parent())
        self.updateUi()

    def updateUi(self):
        text = "Dock" if not self.parent() else "Undock"
        self.pbDockToggle.setText(f"  {text}")

    def saveState(self):
        """Saves dev options state"""
        self.lastZoom = self.pdfViewer.scaleDelta
        self.lastScrollX = self.pdfViewer.relativeScrollX()
        self.lastScrollY = self.pdfViewer.relativeScrollY()

    def isFollowing(self):
        """Returns True if following PDF ID selection"""
        return self.pbFollowSelection.isChecked()

    def onKeepPosition(self):
        """Returns True if following PDF ID selection"""
        pass

    def onSimilarity(self):
        """Returns True if following PDF ID selection"""
        pass

    def onFollowSelection(self):
        """Returns True if following PDF ID selection"""
        pass

    def onShowRegions(self):
        """Returns True if following PDF ID selection"""
        pass

    def onShowLayout(self):
        """Returns True if following PDF ID selection"""
        pass

    def restoreState(self):
        """Restores checked dev options"""
        if self.pbKeepPosition.isChecked():
            self.pdfViewer.zoomTo(self.lastZoom)
            self.pdfViewer.setRelativeScrollX(self.lastScrollX)
            self.pdfViewer.setRelativeScrollY(self.lastScrollY)

        self.pbSimilarity.isChecked()

        if self.pbShowRegions.isChecked():
            try:
                self.drawRegions()
            except Exception as e:
                logger.debug("Draw regions failed", exc_info=True)

    def ensureInViewport(self):
        """Move window into view if titlebar is outside of screen"""
        geom = self.geometry()
        screenGeom = self.screen().geometry()
        if geom.y() <= screenGeom.y():
            geom.setY(geom.y() + 32)
        self.setGeometry(geom)

    def drawRegions(self):
        self.pdfViewer.clearRegions()
        filename = self.data["originalFilename"]
        pdfLoaded = bool(filename) and self.projectId
        page = self.pdfViewer.page
        if not pdfLoaded:
            return False

        # draw missing
        ocrFile = getSourceOcrPath(self.projectId, filename)
        ocrDf = pd.DataFrame()
        try:
            ocrDf = load_df_fast(ocrFile)
        except Exception as e:
            pass

        # Draw Raw
        raw = getSourceRawDataPath(self.projectId, filename)
        logger.debug("Checking for raw regions")

        try:
            raw_df = load_df_fast(raw)
        except Exception as e:
            return

        ocrRegions = []

        if not ocrDf.empty:
            from src.atom.vision.ocr_patcher import reduce_regions
            ocrDf = ocrDf[ocrDf["pdf_page"] == page]
            ocrDf = reduce_regions(ocrDf, inverse=False)
            # scale = self.pdfViewer.dpi / 72
            ocrDf["x0"] = ocrDf["x0"].apply(lambda x: x * self.pdfViewer.pageWidth)
            ocrDf["y0"] = ocrDf["y0"].apply(lambda y: y * self.pdfViewer.pageHeight)
            ocrDf["x1"] = ocrDf["x1"].apply(lambda x: x * self.pdfViewer.pageWidth)
            ocrDf["y1"] = ocrDf["y1"].apply(lambda y: y * self.pdfViewer.pageHeight)
            # ocrDf.to_excel("ocr_df.xlsx")

            for row in ocrDf.itertuples():
                start = QPoint(row.x0, row.y0)
                end = QPoint(row.x1, row.y1)
                ocrRegions.append((start, end, row.text))

            # print(ocrRegions)
            self.pdfViewer.drawRegions(ocrRegions, "purple")

        regions = []

        raw_df = raw_df[raw_df["pdf_page"] == page]
        scale = self.pdfViewer.dpi / 72
        for row in raw_df.itertuples():
            x0, y0, x1, y1 = (n * scale for n in row.coordinates2)
            start = QPoint(x0, y0)
            end = QPoint(x1, y1)
            regions.append((start, end, row.value))

        self.pdfViewer.drawRegions(regions, "lightgreen")
