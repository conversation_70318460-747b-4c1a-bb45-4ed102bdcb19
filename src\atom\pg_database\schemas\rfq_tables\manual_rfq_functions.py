"""
Manual PostgreSQL functions for RFQ management without trigger-based two-way sync.
These functions provide the same functionality as the trigger-based versions but
are designed to be called explicitly from application code.
"""

"""
RFQ MANUAL FUNCTION WORKFLOW GUIDE

These PostgreSQL functions enable manual synchronization and management of data between 
RFQ, RFQ_INPUT and BOM tables without relying on database triggers. This allows more 
controlled data flow, better error handling, and explicit transaction management.

== WORKFLOW SEQUENCE ==

1. SYNC RFQ → RFQ_INPUT: Use manual_sync_rfq_to_input() to push RFQ changes to the master RFQ_INPUT table
   - Updates/creates standardized material records in RFQ_INPUT from project-specific RFQ data
   - Preserves existing category information in RFQ_INPUT
   - Returns the input_id and rfq_id of affected records

2. SYNC RFQ_INPUT → RFQ: Use manual_sync_input_to_rfq() to pull standardized data to RFQ records
   - Updates all linked RFQ records when master material definitions change
   - Maintains project-specific fields while updating material attributes
   - Returns count of updated RFQ records

3. UPDATE CATEGORIES: Use manual_update_rfq_categories() to classify items
   - Applies mappings from component_mapping table to set rfq_scope and general_category
   - Handles special component types (valves, fittings, bolts, etc.)
   - Returns TRUE if mapping found, FALSE if defaults applied

4. PROPAGATE CHANGES: Use manual_propagate_mapping_changes() after modifying mappings
   - Updates all RFQ/RFQ_INPUT records affected by mapping changes
   - Ensures consistent categorization across the database
   - Returns count of updated records

5. SYNC BOM → RFQ: Use manual_sync_bom_to_rfq() to create RFQ records from BOM data
   - Links BOM components to RFQ records for takeoff calculations
   - Creates new RFQ records when no matching ones exist
   - Returns rfq_id of created/updated record

== IMPORTANT NOTES ==

- Call these functions from Python using execute_query() with appropriate parameters
- Functions handle both creation of new records and updating existing ones
- Synchronization is bidirectional but must be called explicitly
- Each function handles a specific data flow direction or categorization task
- Run functions in transaction blocks for atomicity when performing multiple operations
"""

# Function to sync data from RFQ to RFQ_INPUT (previously trigger-based)
MANUAL_SYNC_RFQ_TO_INPUT = """
    CREATE OR REPLACE FUNCTION manual_sync_rfq_to_input(
        p_project_id INTEGER, 
        p_material_description TEXT,
        p_normalized_description TEXT DEFAULT NULL,
        p_rfq_scope VARCHAR DEFAULT NULL,
        p_general_category VARCHAR DEFAULT NULL,
        p_unit_of_measure VARCHAR DEFAULT NULL,
        p_material VARCHAR DEFAULT NULL,
        p_abbreviated_material VARCHAR DEFAULT NULL,
        p_technical_standard VARCHAR DEFAULT NULL,
        p_astm VARCHAR DEFAULT NULL,
        p_grade VARCHAR DEFAULT NULL,
        p_rating VARCHAR DEFAULT NULL,
        p_schedule VARCHAR DEFAULT NULL,
        p_coating VARCHAR DEFAULT NULL,
        p_forging VARCHAR DEFAULT NULL,
        p_ends VARCHAR DEFAULT NULL,
        p_item_tag VARCHAR DEFAULT NULL,
        p_tie_point VARCHAR DEFAULT NULL,
        p_pipe_category VARCHAR DEFAULT NULL,
        p_valve_type VARCHAR DEFAULT NULL,
        p_fitting_category VARCHAR DEFAULT NULL,
        p_weld_category VARCHAR DEFAULT NULL,
        p_bolt_category VARCHAR DEFAULT NULL,
        p_gasket_category VARCHAR DEFAULT NULL,
        p_notes TEXT DEFAULT NULL,
        p_deleted BOOLEAN DEFAULT FALSE,
        p_ignore_item BOOLEAN DEFAULT FALSE,
        p_created_by VARCHAR DEFAULT NULL,
        p_updated_by VARCHAR DEFAULT NULL
    )
    RETURNS TABLE(input_id INTEGER, rfq_id INTEGER) AS $$
    DECLARE
        v_input_id INTEGER;
        v_rfq_id INTEGER;
        existing_record RECORD;
    BEGIN
        -- Check if a matching RFQ_INPUT record already exists
        -- Use case-insensitive comparison
        SELECT id INTO v_input_id
        FROM public.atem_rfq_input
        WHERE project_id = p_project_id
        AND UPPER(material_description) = UPPER(p_material_description);
        
        -- If no matching record exists, create one
        IF v_input_id IS NULL THEN
            INSERT INTO public.atem_rfq_input (
                project_id, material_description, normalized_description,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                p_project_id, p_material_description, p_normalized_description,
                p_rfq_scope, p_general_category, p_unit_of_measure,
                p_material, p_abbreviated_material, p_technical_standard,
                p_astm, p_grade, p_rating, p_schedule, p_coating,
                p_forging, p_ends, p_item_tag, p_tie_point,
                p_pipe_category, p_valve_type, p_fitting_category,
                p_weld_category, p_bolt_category, p_gasket_category,
                p_notes, p_deleted, p_ignore_item,
                p_created_by, p_updated_by
            )
            RETURNING id INTO v_input_id;
        ELSE
            -- Check existing record and don't overwrite valid category data
            SELECT * INTO existing_record 
            FROM public.atem_rfq_input 
            WHERE id = v_input_id;
            
            -- Selective update that preserves category information
            UPDATE public.atem_rfq_input
            SET
                -- These fields always get updated
                normalized_description = p_normalized_description,
                unit_of_measure = p_unit_of_measure,
                material = p_material,
                abbreviated_material = p_abbreviated_material,
                technical_standard = p_technical_standard,
                astm = p_astm,
                grade = p_grade,
                rating = p_rating,
                schedule = p_schedule,
                coating = p_coating,
                forging = p_forging,
                ends = p_ends,
                item_tag = p_item_tag,
                tie_point = p_tie_point,
                notes = p_notes,
                deleted = p_deleted,
                ignore_item = p_ignore_item,
                updated_by = p_updated_by,
                
                -- Only update category fields if they're NULL in the existing record
                rfq_scope = COALESCE(existing_record.rfq_scope, p_rfq_scope),
                general_category = COALESCE(existing_record.general_category, p_general_category),
                pipe_category = COALESCE(existing_record.pipe_category, p_pipe_category),
                valve_type = COALESCE(existing_record.valve_type, p_valve_type),
                fitting_category = COALESCE(existing_record.fitting_category, p_fitting_category),
                weld_category = COALESCE(existing_record.weld_category, p_weld_category),
                bolt_category = COALESCE(existing_record.bolt_category, p_bolt_category),
                gasket_category = COALESCE(existing_record.gasket_category, p_gasket_category)
            WHERE id = v_input_id;
        END IF;
        
        -- Return the input_id and NULL for rfq_id since this function doesn't modify RFQ records
        RETURN QUERY SELECT v_input_id, v_rfq_id;
    END;
    $$ LANGUAGE plpgsql;
"""

# Function to sync data from RFQ_INPUT to RFQ (previously trigger-based)
MANUAL_SYNC_INPUT_TO_RFQ = """
    CREATE OR REPLACE FUNCTION manual_sync_input_to_rfq(p_input_id INTEGER)
    RETURNS INTEGER AS $$
    DECLARE
        v_input RECORD;
        v_rfq_count INTEGER;
    BEGIN
        -- Get the RFQ_INPUT record
        SELECT * INTO v_input
        FROM public.atem_rfq_input
        WHERE id = p_input_id;
        
        IF NOT FOUND THEN
            RAISE EXCEPTION 'RFQ_INPUT record with ID % not found', p_input_id;
        END IF;
        
        -- Update all RFQ records linked to this RFQ_INPUT
        UPDATE public.atem_rfq
        SET
            material_description = v_input.material_description,
            normalized_description = v_input.normalized_description,
            rfq_scope = v_input.rfq_scope,
            general_category = v_input.general_category,
            unit_of_measure = v_input.unit_of_measure,
            material = v_input.material,
            abbreviated_material = v_input.abbreviated_material,
            technical_standard = v_input.technical_standard,
            astm = v_input.astm,
            grade = v_input.grade,
            rating = v_input.rating,
            schedule = v_input.schedule,
            coating = v_input.coating,
            forging = v_input.forging,
            ends = v_input.ends,
            item_tag = v_input.item_tag,
            tie_point = v_input.tie_point,
            pipe_category = v_input.pipe_category,
            valve_type = v_input.valve_type,
            fitting_category = v_input.fitting_category,
            weld_category = v_input.weld_category,
            bolt_category = v_input.bolt_category,
            gasket_category = v_input.gasket_category,
            notes = v_input.notes,
            deleted = v_input.deleted,
            ignore_item = v_input.ignore_item
        WHERE rfq_input_id = p_input_id;
        
        GET DIAGNOSTICS v_rfq_count = ROW_COUNT;
        
        RETURN v_rfq_count;
    END;
    $$ LANGUAGE plpgsql;
"""

# Update RFQ categories based on component mapping
MANUAL_UPDATE_RFQ_CATEGORIES = """
    CREATE OR REPLACE FUNCTION manual_update_rfq_categories(
        p_id INTEGER,
        p_table_name VARCHAR,  -- 'atem_rfq' or 'atem_rfq_input'
        p_component_column VARCHAR,  -- 'pipe_category', 'fitting_category', etc.
        p_component_value VARCHAR
    )
    RETURNS BOOLEAN AS $$
    DECLARE
        v_record RECORD;
        v_profile_id INTEGER;
        v_rfq_scope VARCHAR(100);
        v_general_category VARCHAR(100);
        v_mapping_found BOOLEAN := FALSE;
        v_project_id INTEGER;
    BEGIN
        -- Get the record from the specified table
        IF p_table_name = 'atem_rfq' THEN
            EXECUTE format('SELECT * FROM public.atem_rfq WHERE id = %s', p_id) INTO v_record;
            v_profile_id := v_record.profile_id;
            v_project_id := v_record.project_id;
        ELSIF p_table_name = 'atem_rfq_input' THEN
            EXECUTE format('SELECT * FROM public.atem_rfq_input WHERE id = %s', p_id) INTO v_record;
            v_project_id := v_record.project_id;
            
            -- Get profile_id from projects table
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = v_project_id;
        ELSE
            RAISE EXCEPTION 'Invalid table name: %', p_table_name;
        END IF;
        
        IF NOT FOUND THEN
            RAISE EXCEPTION 'Record with ID % not found in %', p_id, p_table_name;
        END IF;
        
        -- Look up rfq_scope and general_category from component mapping
        IF v_profile_id IS NOT NULL AND p_component_value IS NOT NULL THEN
            SELECT takeoff_category, general_category INTO v_rfq_scope, v_general_category
            FROM public.atem_bom_component_mapping
            WHERE profile_id = v_profile_id AND component_name = p_component_value
            LIMIT 1;
            
            -- Update the categories if mapping was found
            IF v_rfq_scope IS NOT NULL OR v_general_category IS NOT NULL THEN
                -- Prepare update fields
                IF v_rfq_scope IS NULL THEN
                    v_rfq_scope := v_record.rfq_scope;
                END IF;
                
                IF v_general_category IS NULL THEN
                    v_general_category := v_record.general_category;
                END IF;
                
                -- Update the record
                IF p_table_name = 'atem_rfq' THEN
                    UPDATE public.atem_rfq
                    SET rfq_scope = v_rfq_scope,
                        general_category = v_general_category,
                        mapping_not_found = FALSE
                    WHERE id = p_id;
                ELSE
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = v_rfq_scope,
                        general_category = v_general_category,
                        mapping_not_found = FALSE
                    WHERE id = p_id;
                END IF;
                
                v_mapping_found := TRUE;
            ELSE
                -- No mapping found, apply defaults
                v_mapping_found := FALSE;
                
                -- Apply default values based on the component type
                IF p_component_value IS NOT NULL THEN
                    CASE
                        WHEN p_component_column = 'valve_type' THEN
                            -- Only set if rfq_scope is NULL
                            IF v_record.rfq_scope IS NULL THEN
                                v_rfq_scope := 'Valves';
                                v_general_category := v_record.general_category;
                            ELSE 
                                v_rfq_scope := v_record.rfq_scope;
                                v_general_category := v_record.general_category;
                            END IF;
                            
                        WHEN p_component_column = 'pipe_category' THEN
                            -- Only set if rfq_scope is NULL
                            IF v_record.rfq_scope IS NULL THEN
                                v_rfq_scope := 'Pipe';
                                v_general_category := 'LF';
                            ELSE
                                v_rfq_scope := v_record.rfq_scope;
                                v_general_category := v_record.general_category;
                            END IF;
                            
                        WHEN p_component_column = 'bolt_category' THEN
                            -- Only set if rfq_scope is NULL
                            IF v_record.rfq_scope IS NULL THEN
                                v_rfq_scope := 'Bolts';
                                v_general_category := v_record.general_category;
                            ELSE
                                v_rfq_scope := v_record.rfq_scope;
                                v_general_category := v_record.general_category;
                            END IF;
                            
                        WHEN p_component_column = 'gasket_category' THEN
                            -- Only set if rfq_scope is NULL
                            IF v_record.rfq_scope IS NULL THEN
                                v_rfq_scope := 'Gaskets';
                                v_general_category := v_record.general_category;
                            ELSE
                                v_rfq_scope := v_record.rfq_scope;
                                v_general_category := v_record.general_category;
                            END IF;
                        
                        ELSE
                            v_rfq_scope := v_record.rfq_scope;
                            v_general_category := v_record.general_category;
                    END CASE;
                    
                    -- Update the record with defaults
                    IF p_table_name = 'atem_rfq' THEN
                        UPDATE public.atem_rfq
                        SET rfq_scope = v_rfq_scope,
                            general_category = v_general_category,
                            mapping_not_found = TRUE
                        WHERE id = p_id;
                    ELSE
                        UPDATE public.atem_rfq_input
                        SET rfq_scope = v_rfq_scope,
                            general_category = v_general_category,
                            mapping_not_found = TRUE
                        WHERE id = p_id;
                    END IF;
                END IF;
            END IF;
        END IF;
        
        RETURN v_mapping_found;
    END;
    $$ LANGUAGE plpgsql;
"""

# Propagate BOM component mapping changes to RFQ and RFQ_INPUT records
MANUAL_PROPAGATE_MAPPING_CHANGES = """
    CREATE OR REPLACE FUNCTION manual_propagate_mapping_changes(
        p_profile_id INTEGER,
        p_component_name VARCHAR,
        p_takeoff_category VARCHAR,
        p_general_category VARCHAR
    )
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;
        rec RECORD;
    BEGIN
        -- Find all RFQ_INPUT records that match this component mapping for pipe_category
        FOR rec IN (
            SELECT ri.id, p.profile_id
            FROM public.atem_rfq_input ri
            JOIN public.atem_projects p ON ri.project_id = p.id
            WHERE p.profile_id = p_profile_id
            AND ri.pipe_category = p_component_name
        ) LOOP
            UPDATE public.atem_rfq_input
            SET rfq_scope = p_takeoff_category,
                general_category = p_general_category,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            v_count := v_count + 1;
        END LOOP;
        
        -- Find all RFQ_INPUT records that match this component mapping for fitting_category
        FOR rec IN (
            SELECT ri.id, p.profile_id
            FROM public.atem_rfq_input ri
            JOIN public.atem_projects p ON ri.project_id = p.id
            WHERE p.profile_id = p_profile_id
            AND ri.fitting_category = p_component_name
        ) LOOP
            UPDATE public.atem_rfq_input
            SET rfq_scope = p_takeoff_category,
                general_category = p_general_category,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            v_count := v_count + 1;
        END LOOP;
        
        -- Find all RFQ_INPUT records that match this component mapping for gasket_category
        FOR rec IN (
            SELECT ri.id, p.profile_id
            FROM public.atem_rfq_input ri
            JOIN public.atem_projects p ON ri.project_id = p.id
            WHERE p.profile_id = p_profile_id
            AND ri.gasket_category = p_component_name
        ) LOOP
            UPDATE public.atem_rfq_input
            SET rfq_scope = p_takeoff_category,
                general_category = p_general_category,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            v_count := v_count + 1;
        END LOOP;
        
        -- Find all RFQ_INPUT records that match this component mapping for bolt_category
        FOR rec IN (
            SELECT ri.id, p.profile_id
            FROM public.atem_rfq_input ri
            JOIN public.atem_projects p ON ri.project_id = p.id
            WHERE p.profile_id = p_profile_id
            AND ri.bolt_category = p_component_name
        ) LOOP
            UPDATE public.atem_rfq_input
            SET rfq_scope = p_takeoff_category,
                general_category = p_general_category,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            v_count := v_count + 1;
        END LOOP;
        
        -- Find all RFQ_INPUT records that match this component mapping for valve_type
        FOR rec IN (
            SELECT ri.id, p.profile_id
            FROM public.atem_rfq_input ri
            JOIN public.atem_projects p ON ri.project_id = p.id
            WHERE p.profile_id = p_profile_id
            AND ri.valve_type = p_component_name
        ) LOOP
            UPDATE public.atem_rfq_input
            SET rfq_scope = p_takeoff_category,
                general_category = p_general_category,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            v_count := v_count + 1;
        END LOOP;
        
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;
"""

# Manual function to sync from BOM to RFQ
MANUAL_SYNC_BOM_TO_RFQ = """
CREATE OR REPLACE FUNCTION manual_sync_bom_to_rfq(p_bom_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_bom RECORD;
    v_rfq_id INTEGER;
    BEGIN
        -- Get the BOM record
        SELECT * INTO v_bom
        FROM public.bom
        WHERE id = p_bom_id;
        
        IF NOT FOUND THEN
            RAISE EXCEPTION 'BOM record with ID % not found', p_bom_id;
        END IF;
        
        -- Check if a matching RFQ record already exists for this material and size combination
        SELECT id INTO v_rfq_id
        FROM public.atem_rfq
        WHERE project_id = v_bom.project_id
        AND material_description = v_bom.material_description
        AND (
            (COALESCE(size1, 0) = COALESCE(v_bom.size1, 0) AND COALESCE(size2, 0) = COALESCE(v_bom.size2, 0))
            OR (COALESCE(size1, 0) = COALESCE(v_bom.size2, 0) AND COALESCE(size2, 0) = COALESCE(v_bom.size1, 0))
        )
        LIMIT 1;
        
        -- If no matching record exists, create one
        IF v_rfq_id IS NULL THEN
            INSERT INTO public.atem_rfq (
                project_id, profile_id, material_description, normalized_description,
                size, size1, size2,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                calculated_eq_length, calculated_area,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                v_bom.project_id, v_bom.profile_id, v_bom.material_description, v_bom.normalized_description,
                v_bom.size, v_bom.size1, v_bom.size2,
                v_bom.rfq_scope, v_bom.general_category, v_bom.unit_of_measure,
                v_bom.material, v_bom.abbreviated_material, v_bom.technical_standard,
                v_bom.astm, v_bom.grade, v_bom.rating, v_bom.schedule, v_bom.coating,
                v_bom.forging, v_bom.ends, v_bom.item_tag, v_bom.tie_point,
                v_bom.pipe_category, v_bom.valve_type, v_bom.fitting_category,
                v_bom.weld_category, v_bom.bolt_category, v_bom.gasket_category,
                v_bom.calculated_eq_length, v_bom.calculated_area,
                v_bom.notes, v_bom.deleted, v_bom.ignore_item,
                v_bom.created_by, v_bom.updated_by
            )
            RETURNING id INTO v_rfq_id;
        END IF;
        
        -- Link the BOM record to its RFQ record
        UPDATE public.bom
        SET rfq_ref_id = v_rfq_id
        WHERE id = p_bom_id;
        
        RETURN v_rfq_id;
    END;
    $$ LANGUAGE plpgsql;
    """

# Manual function to sync from RFQ to BOM
MANUAL_SYNC_RFQ_TO_BOM = """
    CREATE OR REPLACE FUNCTION manual_sync_rfq_to_bom(p_rfq_id INTEGER)
    RETURNS INTEGER AS $$
    DECLARE
        v_rfq RECORD;
        v_count INTEGER := 0;
    BEGIN
        -- Get the RFQ record
        SELECT * INTO v_rfq
        FROM public.atem_rfq
        WHERE id = p_rfq_id;
        
        IF NOT FOUND THEN
            RAISE EXCEPTION 'RFQ record with ID % not found', p_rfq_id;
        END IF;
        
        -- Update linked BOM records with classification data from RFQ
        UPDATE public.bom
        SET 
            general_category = v_rfq.general_category,
            rfq_scope = v_rfq.rfq_scope,
            material = v_rfq.material,
            rating = v_rfq.rating,
            ends = v_rfq.ends,
            fitting_category = v_rfq.fitting_category,
            valve_type = v_rfq.valve_type,
            calculated_eq_length = v_rfq.calculated_eq_length,
            calculated_area = v_rfq.calculated_area,
            updated_at = CURRENT_TIMESTAMP
        WHERE rfq_ref_id = p_rfq_id;
        
        GET DIAGNOSTICS v_count = ROW_COUNT;
        
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;
    """

# Manual function to update BOM profile_id based on project_id (This can probably remain as a trigger)
MANUAL_UPDATE_BOM_PROFILE_ID = """
    CREATE OR REPLACE FUNCTION manual_update_bom_profile_id(p_bom_id INTEGER DEFAULT NULL)
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;
    BEGIN
        IF p_bom_id IS NULL THEN
            -- Update all BOM records
            UPDATE public.bom b
            SET profile_id = p.profile_id
            FROM public.atem_projects p
            WHERE b.project_id = p.id;
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
        ELSE
            -- Update a specific BOM record
            UPDATE public.bom b
            SET profile_id = p.profile_id
            FROM public.atem_projects p
            WHERE b.id = p_bom_id
            AND b.project_id = p.id;
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
        END IF;
        
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;
    """

# Manual function to refresh RFQ categories based on project_id
MANUAL_REFRESH_RFQ_CATEGORIES = """
    CREATE OR REPLACE FUNCTION manual_refresh_rfq_categories(p_project_id INTEGER DEFAULT NULL)
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;  -- Counter for affected rows
        rec RECORD;            -- For iterating through records
        BEGIN
            -- Validate the project_id if provided
            IF p_project_id IS NOT NULL AND NOT EXISTS (
                SELECT 1 FROM public.atem_projects WHERE id = p_project_id
            ) THEN
                RAISE EXCEPTION 'Project ID % does not exist', p_project_id;
            END IF;

            -- Loop through each row in atem_rfq_input that needs updating
            FOR rec IN (
                SELECT ri.id, ri.project_id, ri.rfq_scope, ri.material_description, 
                    ri.fitting_category, ri.valve_type, ri.pipe_category, 
                    ri.bolt_category, ri.gasket_category
                FROM public.atem_rfq_input ri
                -- Project filter based on parameter
                WHERE (p_project_id IS NULL OR ri.project_id = p_project_id)
            ) LOOP
                -- Find the most appropriate category to update based on description patterns
                IF rec.material_description ILIKE '%valve%' THEN
                    -- Set valve_type to 'Valve' to trigger categorization
                    UPDATE public.atem_rfq_input 
                    SET valve_type = 'Valve',
                        updated_at = CURRENT_TIMESTAMP  
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSIF rec.material_description ILIKE '%elbow%' OR 
                    rec.material_description ILIKE '%tee%' OR 
                    rec.material_description ILIKE '%reducer%' OR
                    rec.material_description ILIKE '%coupling%' OR
                    rec.material_description ILIKE '%blind%' OR
                    rec.material_description ILIKE '%figure 8%' THEN
                    -- Set fitting_category for fittings
                    UPDATE public.atem_rfq_input 
                    SET fitting_category = 'Fitting',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSIF rec.material_description ILIKE '%bolt%' OR 
                    rec.material_description ILIKE '%stud%' OR 
                    rec.material_description ILIKE '%nut%' THEN
                    -- Set bolt_category
                    UPDATE public.atem_rfq_input 
                    SET bolt_category = 'Bolt',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSIF rec.material_description ILIKE '%gasket%' OR 
                    rec.material_description ILIKE '%seal%' THEN
                    -- Set gasket_category
                    UPDATE public.atem_rfq_input 
                    SET gasket_category = 'Gasket',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSIF rec.material_description ILIKE '%pipe%' OR
                    rec.material_description ILIKE '%SCH%' THEN
                    -- Set pipe_category
                    UPDATE public.atem_rfq_input 
                    SET pipe_category = 'Pipe',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSIF rec.material_description ILIKE '%base support%' OR
                    rec.material_description ILIKE '%support%' THEN
                    -- Handle support items by setting proper categories
                    UPDATE public.atem_rfq_input 
                    SET general_category = 'Support',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                ELSE
                    -- For unclassified items, just update timestamp
                    UPDATE public.atem_rfq_input 
                    SET updated_at = CURRENT_TIMESTAMP
                    WHERE id = rec.id;
                    
                    v_count := v_count + 1;
                END IF;
            END LOOP;
            
            RETURN v_count;
        END;
        $$ LANGUAGE plpgsql;
    """