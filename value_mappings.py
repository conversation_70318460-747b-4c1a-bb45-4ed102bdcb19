
# Create the lookup dictionary (OD Pipe Chart)
pipe_od_lookup = {
    "0.125": 0.405,
    "0.250": 0.540,
    "0.375": 0.675,
    "0.500": 0.840,
    "0.750": 1.050,
    "1.000": 1.315,
    "1.250": 1.660,
    "1.500": 1.900,
    "2.000": 2.375,
    "2.500": 2.875,
    "3.000": 3.500,
    "3.500": 4.000,
    "4.000": 4.500,
    "5.000": 5.563,
    "6.000": 6.625,
    "8.000": 8.625,
    "10.000": 10.750,
    "12.000": 12.750,
    "14.000": 14.000
}

# Map values to unit of measure
category_to_unit_map = {
    "LF": "LF",
    "90 Short Radius": "EA",
    "90 Long Radius": "EA",
    "45": "EA",
    "Bevel": "EA",
    "Tee": "EA",
    "Reducer (Concentric)": "EA",
    "Reducer (Eccentric)": "EA",
    "Cap": "EA",
    "Flanges": "EA",
    "Flanged Valve": "EA",
    "Welded Valve": "EA",
    "Cut Out": "EA",
    "Bend": "EA",
    "Field Weld": "EA",
    "Support": "EA",
    "Union Coupling": "EA",
    # Default case if not listed
    "default": "EA"
    }

# Maps rfq_scope to general_category
scope_to_category_map = {
        '': '',
        'Bolts': '',
        'Gaskets': '',
        'Miscellaneous': '',
        'Pipe': 'LF',
        'Supports': 'Support',
        'Flanges': 'Flanges'
    }

# Used in AI Classifier to map determined fitting values to rfq_scope
fitting_to_category_map = {
        '180 Red Return LR': 'Bend',
        '180 Return LR': 'Bend',
        '180 Return SR': 'Bend',
        '45 Elbow': '45',
        '45 SR Elbow': '45',
        '90 LR Elbow': '90 Long Radius',
        '90 LR Reducing Elbow': '90 Long Radius',
        '90 SR Elbow': '90 Short Radius',
        'Basket Strainer': 'Tee',
        'Bellmouth': '',
        'Bellow': '',
        'Bellow Reducing': '',
        'Blind Flange': 'Flanges',
        'Blind Flange - Drill And Tap': 'Flanges',
        'Blind Flange - Drill Only': 'Flanges',
        'Bucket Strainer': 'Tee',
        'Bulkhead Fitting': '',
        'Bushing': '',
        'Bushing Reducing': '',
        'Cap': 'Cap',
        'Compression Fitting': '',
        'Conical Strainer': 'Tee',
        'Coupling': '',
        'Coupling (Half)': '',
        'Coupling (Half) Reducing': '',
        'Coupling Reducing': '',
        'Cross': '',
        'Cross Reducing': '',
        'Elbolet BW': '',
        'Elbolet PE': '',
        'Elbolet THRD': '',
        'Equal Tee': 'Tee',
        'Ferrule': '',
        'Figure 8': '',
        'Flange Other': '',
        'Flangeolet': 'Flanges',
        'Flatolet': 'Flanges',
        'Hammer Blind': '',
        'Hex Plug': '',
        'Hose Coupling': '',
        'Lateral': 'Tee',
        'Lateral Reducing': 'Tee',
        'Latrolet BW': '',
        'Latrolet PE': '',
        'Latrolet THRD': '',
        'LJ Flange FF': 'Flanges',
        'LJ Flange Long FF': 'Flanges',
        'LJ Flange Long RF': 'Flanges',
        'LJ Flange Orifice FF': 'Flanges',
        'LJ Flange Orifice RF': 'Flanges',
        'LJ Flange Reducing FF': 'Flanges',
        'LJ Flange Reducing RF': 'Flanges',
        'LJ Flange RF': 'Flanges',
        'LJ Flange Ring Joint FF': 'Flanges',
        'LJ Flange Ring Joint RF': 'Flanges',
        'Nipolet BW': '',
        'Nipolet PE': '',
        'Nipolet THRD': '',
        'Nipple': '',
        'Orifice Plate': '',
        'Other Fitting': '',
        'Other Fitting Reducing': '',
        'Other Strainer': 'Tee',
        'Paddle Spacer': '',
        'Pipe Bend': 'Bend',
        'Plate Flange': '',
        'Puddle Flange': '',
        'Reducer Bushing': '',
        'Reducer Concentric': 'Reducer (Concentric)',
        'Reducer Eccentric': 'Reducer (Eccentric)',
        'Reducer Insert': '',
        'Reducing Tee': 'Tee',
        'Round Plug': '',
        'SO Flange FF': 'Flanges',
        'SO Flange Long FF': 'Flanges',
        'SO Flange Long RF': 'Flanges',
        'SO Flange Orifice FF': 'Flanges',
        'SO Flange Orifice RF': 'Flanges',
        'SO Flange Reducing FF': 'Flanges',
        'SO Flange Reducing RF': 'Flanges',
        'SO Flange RF': 'Flanges',
        'SO Flange Ring Joint FF': 'Flanges',
        'SO Flange Ring Joint RF': 'Flanges',
        'Sockolet': '',
        'Spacer': '',
        'Spade Blind': '',
        'Spectacle Blind': '',
        'Square Plug': '',
        'Stub End BW': '',
        'Stub End Flgd': '',
        'Stub End PE': '',
        'Stub End SW': '',
        'Stub End Te': '',
        'SW Flange FF': 'Flanges',
        'SW Flange Long FF': 'Flanges',
        'SW Flange Long RF': 'Flanges',
        'SW Flange Orifice FF': 'Flanges',
        'SW Flange Orifice RF': 'Flanges',
        'SW Flange Reducing FF': 'Flanges',
        'SW Flange Reducing RF': 'Flanges',
        'SW Flange RF': 'Flanges',
        'SW Flange Ring Joint FF': 'Flanges',
        'SW Flange Ring Joint RF': 'Flanges',
        'Swage Con. Reducer': 'Reducer (Concentric)',
        'Swage Ecc. Reducer': 'Reducer (Eccentric)',
        'Swage Nipple': '',
        'Sweepolet BW': '',
        'Sweepolet PE': '',
        'Sweepolet THRD': '',
        'Swivel Joint': '',
        'T-Strainer': 'Tee',
        'T-Strainer Reducing': 'Tee',
        'Tee': 'Tee',
        'Tee Reducing': 'Tee',
        'THRD Flange FF': 'Flanges',
        'THRD Flange Long FF': 'Flanges',
        'THRD Flange Long RF': 'Flanges',
        'THRD Flange Orifice FF': 'Flanges',
        'THRD Flange Orifice RF': 'Flanges',
        'THRD Flange Reducing FF': 'Flanges',
        'THRD Flange Reducing RF': 'Flanges',
        'THRD Flange RF': 'Flanges',
        'THRD Flange Ring Joint FF': 'Flanges',
        'THRD Flange Ring Joint RF': 'Flanges',
        'Threadolet': '',
        'Union': '',
        'Weldolet': '',
        'WN Flange FF': 'Flanges',
        'WN Flange Long FF': 'Flanges',
        'WN Flange Long RF': 'Flanges',
        'WN Flange Orifice FF': 'Flanges',
        'WN Flange Orifice RF': 'Flanges',
        'WN Flange Reducing FF': 'Flanges',
        'WN Flange Reducing RF': 'Flanges',
        'WN Flange RF': 'Flanges',
        'WN Flange Ring Joint FF': 'Flanges',
        'WN Flange Ring Joint RF': 'Flanges',
        'Wye (Compact)': 'Tee',
        'Wye (Compact) Reducing': 'Tee',
        'Wye (Standard)': 'Tee',
        'Wye (Standard) Reducing': 'Tee',
        'Y-Strainer': 'Tee',
        'Y-Strainer Reducing': 'Tee',
    }

# Maps Material full name to its abbreviation
material_to_abbreviation_map = {
        "Aluminium": "AL",
        "Bronze/Brass": "CuZn",
        "Steel, Carbon": "CS",
        "Steel, Carbon (Lined)": "CS-L",
        "Incoloy 20": "20Cb-3",
        "Cast Iron": "CI",
        "Chrome": "Cr",
        "Copper": "Cu",
        "Iron, Ductile": "DI",
        "Iron, Ductile (Lined)": "DI-L",
        "Duplex": "DSS",
        "Fusion Bond": "FBE",
        "Galvenized": "GI",
        "Hastelloy": "HAST",
        "(HDPE) High-Density Polyethylene": "HDPE",
        "Incoloy": "Incoloy",
        "Inconel": "Inconel",
        "Low Temp. Carbon Steel": "LTCS",
        "Iron, Malleable": "MI",
        "Monel": "MON",
        "Nickel": "NI",
        "Polypropylene": "PP",
        "Steel, Stainless": "SS",
        "Steel, Stainless (Lined)": "SS-L",
        "Titanium": "TI",
        "(CPVC) Chlorinated Polyvinyl Chloride": "CPVC",
        "(PVC) Polyvinyl Chloride": "PVC",
        "(UPVC) Unplasticized Polyvinyl Chloride": "UPVC",
        "(FRP) Fiber Reinforced Plastic": "FRP",
        "Fiberglass": "FRP",
        "Glass Reinforced Plastic": "GRP",
        "(PTFE) Polytetrafluoroethylene": "PTFE",
        "Lined, Rubber": "RL",
    }

# MODIFIED Used in EF Lookup. Maps 'fitting_category' values to values in the ef lookup table when applying RFQ. Complex Sizes return keywords to trigger specific table lookups.
fitting_category_to_ef = {    
    '45 Elbow': '45 Long Radius',
    '45 SR Elbow': '45',
    '90 LR Elbow': '90 Long Radius',
    '90 LR Reducing Elbow': 'Red. 90',
    '90 SR Elbow': '90 Short Radius',
    '180 Red Return LR': 'Bend Long Radius',  # New option, no exact match, assumed similar to '180 RETURN LR'
    '180 Return LR': 'Bend Long Radius',
    '180 Return SR': 'Bend',
    'Basket Strainer': 'Tee',
    'Bellmouth': '',  # New option, no match
    'Bellow': '',
    'Bellow Reducing': '',
    'Blind Flange': 'Flanges',
    'Blind Flange - Drill And Tap': 'Flanges',
    'Blind Flange - Drill Only': 'Flanges',
    'Bucket Strainer': 'Tee',
    'Bulkhead Fitting': '',  # New option, no match
    'Bushing': '',
    'Bushing Reducing': '',
    'Cap': 'Cap',
    'Compression Fitting': '',  # New option, no match
    'Conical Strainer': '',
    'Coupling': '',
    'Coupling (Half)': '',
    'Coupling (Half) Reducing': '',
    'Coupling Reducing': '',
    'Cross': 'Cross',
    'Cross Reducing': 'Red. Cross',
    'Elbolet BW': '',
    'Elbolet PE': '',
    'Elbolet THRD': '',
    'Equal Tee': 'Tee',  # Assumed match for 'TEE'
    'Ferrule': '',  # New option, no match
    'Figure 8': '',
    'Flange Other': 'Flanges',
    'Flangeolet': 'Flanges',
    'Flatolet': 'Flanges',
    'Hammer Blind': '',
    'Hex Plug': '',
    'Hose Coupling': '',
    'Lateral': 'Tee',
    'Lateral Reducing': 'Red. Tee',
    'Latrolet BW': '',
    'Latrolet PE': '',
    'Latrolet THRD': '',
    'LJ Flange FF': 'Flanges',
    'LJ Flange Long FF': 'Flanges',
    'LJ Flange Long RF': 'Flanges',
    'LJ Flange Orifice FF': 'Flanges',
    'LJ Flange Orifice RF': 'Flanges',
    'LJ Flange Reducing FF': 'Flanges',
    'LJ Flange Reducing RF': 'Flanges',
    'LJ Flange RF': 'Flanges',
    'LJ Flange Ring Joint FF': 'Flanges',
    'LJ Flange Ring Joint RF': 'Flanges',
    'Nipolet BW': '',
    'Nipolet PE': '',
    'Nipolet THRD': '',
    'Nipple': '',
    'Orifice Plate': 'Flanges',
    'Other Fitting': '',
    'Other Fitting Reducing': '',
    'Other Strainer': 'Tee',
    'Paddle Spacer': '',
    'Pipe Bend': 'Bend',
    'Plate Flange': 'Flanges',
    'Puddle Flange': 'Flanges',
    'Reducer Bushing': '',  # New option, no match
    'Reducer Concentric': 'Reducer',
    'Reducer Eccentric': 'Reducer',
    'Reducer Insert': '',  # New option, no match
    'Reducing Tee': 'Red. Tee',
    'Round Plug': '',  # New option, no match
    'SO Flange FF': 'Flanges',
    'SO Flange Long FF': 'Flanges',
    'SO Flange Long RF': 'Flanges',
    'SO Flange Orifice FF': 'Flanges',
    'SO Flange Orifice RF': 'Flanges',
    'SO Flange Reducing FF': 'Flanges',
    'SO Flange Reducing RF': 'Flanges',
    'SO Flange RF': 'Flanges',
    'SO Flange Ring Joint FF': 'Flanges',
    'SO Flange Ring Joint RF': 'Flanges',
    'Sockolet': '',  # New option, no match
    'Spacer': '',  # New option, no match
    'Spade Blind': '',
    'Spectacle Blind': '',
    'Square Plug': '',  # New option, no match
    'Stub End BW': '',  # New option, no match
    'Stub End Flgd': '',  # New option, no match
    'Stub End PE': '',  # New option, no match
    'Stub End SW': '',  # New option, no match
    'Stub End Te': '',  # New option, no match
    'SW Flange FF': 'Flanges',
    'SW Flange Long FF': 'Flanges',
    'SW Flange Long RF': 'Flanges',
    'SW Flange Orifice FF': 'Flanges',
    'SW Flange Orifice RF': 'Flanges',
    'SW Flange Reducing FF': 'Flanges',
    'SW Flange Reducing RF': 'Flanges',
    'SW Flange RF': 'Flanges',
    'SW Flange Ring Joint FF': 'Flanges',
    'SW Flange Ring Joint RF': 'Flanges',
    'Swage Con. Reducer': 'Reducer (Concentric)',
    'Swage Ecc. Reducer': 'Reducer (Eccentric)',
    'Swage Nipple': '',  # New option, no match
    'Sweepolet BW': '',
    'Sweepolet PE': '',
    'Sweepolet THRD': '',
    'Swivel Joint': '',  # New option, no match
    'T-Strainer': 'Tee',
    'T-Strainer Reducing': 'Red. Tee',
    'Tee': 'Tee',
    'Tee Reducing': 'Red. Tee',
    'THRD Flange FF': 'Flanges',
    'THRD Flange Long FF': 'Flanges',
    'THRD Flange Long RF': 'Flanges',
    'THRD Flange Orifice FF': 'Flanges',
    'THRD Flange Orifice RF': 'Flanges',
    'THRD Flange Reducing FF': 'Flanges',
    'THRD Flange Reducing RF': 'Flanges',
    'THRD Flange RF': 'Flanges',
    'THRD Flange Ring Joint FF': 'Flanges',
    'THRD Flange Ring Joint RF': 'Flanges',
    'Threadolet': '',
    'Union': '',
    'Weldolet': '',
    'WN Flange FF': 'Flanges',
    'WN Flange Long FF': 'Flanges',
    'WN Flange Long RF': 'Flanges',
    'WN Flange Orifice FF': 'Flanges',
    'WN Flange Orifice RF': 'Flanges',
    'WN Flange Reducing FF': 'Flanges',
    'WN Flange Reducing RF': 'Flanges',
    'WN Flange RF': 'Flanges',
    'WN Flange Ring Joint FF': 'Flanges',
    'WN Flange Ring Joint RF': 'Flanges',
    'Wye (Compact)': 'Tee',
    'Wye (Compact) Reducing': 'Red. Tee',
    'Wye (Standard)': 'Tee',
    'Wye (Standard) Reducing': 'Red. Tee',
    'Y-Strainer': 'Tee',
    'Y-Strainer Reducing': 'Red. Tee'
}


# Used in EF Lookup. Maps 'fitting_category' values to values in the ef lookup table when applying RFQ
fitting_category_to_sf = {    
    '45 Elbow': '45 Long Radius',
    '45 SR Elbow': '45',
    '90 LR Elbow': '90 Long Radius',
    '90 LR Reducing Elbow': '90 Long Radius',
    '90 SR Elbow': '90 Short Radius',
    '180 Red Return LR': 'Bend Long Radius',  # New option, no exact match, assumed similar to '180 RETURN LR'
    '180 Return LR': 'Bend Long Radius',
    '180 Return SR': 'Bend',
    'Basket Strainer': 'Tee',
    'Bellmouth': '',  # New option, no match
    'Bellow': '',
    'Bellow Reducing': '',
    'Blind Flange': 'Flanges',
    'Blind Flange - Drill And Tap': 'Flanges',
    'Blind Flange - Drill Only': 'Flanges',
    'Bucket Strainer': 'Tee',
    'Bulkhead Fitting': '',  # New option, no match
    'Bushing': '',
    'Bushing Reducing': '',
    'Cap': 'Cap',
    'Compression Fitting': '',  # New option, no match
    'Conical Strainer': '',
    'Coupling': '',
    'Coupling (Half)': '',
    'Coupling (Half) Reducing': '',
    'Coupling Reducing': '',
    'Cross': '',
    'Cross Reducing': '',
    'Elbolet BW': '',
    'Elbolet PE': '',
    'Elbolet THRD': '',
    'Equal Tee': 'Tee',  # Assumed match for 'TEE'
    'Ferrule': '',  # New option, no match
    'Figure 8': '',
    'Flange Other': 'Flanges',
    'Flangeolet': 'Flanges',
    'Flatolet': 'Flanges',
    'Hammer Blind': '',
    'Hex Plug': '',
    'Hose Coupling': '',
    'Lateral': 'Tee',
    'Lateral Reducing': 'Tee',
    'Latrolet BW': '',
    'Latrolet PE': '',
    'Latrolet THRD': '',
    'LJ Flange FF': 'Flanges',
    'LJ Flange Long FF': 'Flanges',
    'LJ Flange Long RF': 'Flanges',
    'LJ Flange Orifice FF': 'Flanges',
    'LJ Flange Orifice RF': 'Flanges',
    'LJ Flange Reducing FF': 'Flanges',
    'LJ Flange Reducing RF': 'Flanges',
    'LJ Flange RF': 'Flanges',
    'LJ Flange Ring Joint FF': 'Flanges',
    'LJ Flange Ring Joint RF': 'Flanges',
    'Nipolet BW': '',
    'Nipolet PE': '',
    'Nipolet THRD': '',
    'Nipple': '',
    'Orifice Plate': 'Flanges',
    'Other Fitting': '',
    'Other Fitting Reducing': '',
    'Other Strainer': 'Tee',
    'Paddle Spacer': '',
    'Pipe Bend': 'Bend',
    'Plate Flange': '',
    'Puddle Flange': '',
    'Reducer Bushing': '',  # New option, no match
    'Reducer Concentric': 'Reducer (Concentric)',
    'Reducer Eccentric': 'Reducer (Eccentric)',
    'Reducer Insert': '',  # New option, no match
    'Reducing Tee': 'Tee',
    'Round Plug': '',  # New option, no match
    'SO Flange FF': 'Flanges',
    'SO Flange Long FF': 'Flanges',
    'SO Flange Long RF': 'Flanges',
    'SO Flange Orifice FF': 'Flanges',
    'SO Flange Orifice RF': 'Flanges',
    'SO Flange Reducing FF': 'Flanges',
    'SO Flange Reducing RF': 'Flanges',
    'SO Flange RF': 'Flanges',
    'SO Flange Ring Joint FF': 'Flanges',
    'SO Flange Ring Joint RF': 'Flanges',
    'Sockolet': '',  # New option, no match
    'Spacer': '',  # New option, no match
    'Spade Blind': '',
    'Spectacle Blind': '',
    'Square Plug': '',  # New option, no match
    'Stub End BW': '',  # New option, no match
    'Stub End Flgd': '',  # New option, no match
    'Stub End PE': '',  # New option, no match
    'Stub End SW': '',  # New option, no match
    'Stub End Te': '',  # New option, no match
    'SW Flange FF': 'Flanges',
    'SW Flange Long FF': 'Flanges',
    'SW Flange Long RF': 'Flanges',
    'SW Flange Orifice FF': 'Flanges',
    'SW Flange Orifice RF': 'Flanges',
    'SW Flange Reducing FF': 'Flanges',
    'SW Flange Reducing RF': 'Flanges',
    'SW Flange RF': 'Flanges',
    'SW Flange Ring Joint FF': 'Flanges',
    'SW Flange Ring Joint RF': 'Flanges',
    'Swage Con. Reducer': 'Reducer (Concentric)',
    'Swage Ecc. Reducer': 'Reducer (Eccentric)',
    'Swage Nipple': '',  # New option, no match
    'Sweepolet BW': '',
    'Sweepolet PE': '',
    'Sweepolet THRD': '',
    'Swivel Joint': '',  # New option, no match
    'T-Strainer': 'Tee',
    'T-Strainer Reducing': 'Tee',
    'Tee': 'Tee',
    'Tee Reducing': 'Tee',
    'THRD Flange FF': 'Flanges',
    'THRD Flange Long FF': 'Flanges',
    'THRD Flange Long RF': 'Flanges',
    'THRD Flange Orifice FF': 'Flanges',
    'THRD Flange Orifice RF': 'Flanges',
    'THRD Flange Reducing FF': 'Flanges',
    'THRD Flange Reducing RF': 'Flanges',
    'THRD Flange RF': 'Flanges',
    'THRD Flange Ring Joint FF': 'Flanges',
    'THRD Flange Ring Joint RF': 'Flanges',
    'Threadolet': '',
    'Union': '',
    'Weldolet': '',
    'WN Flange FF': 'Flanges',
    'WN Flange Long FF': 'Flanges',
    'WN Flange Long RF': 'Flanges',
    'WN Flange Orifice FF': 'Flanges',
    'WN Flange Orifice RF': 'Flanges',
    'WN Flange Reducing FF': 'Flanges',
    'WN Flange Reducing RF': 'Flanges',
    'WN Flange RF': 'Flanges',
    'WN Flange Ring Joint FF': 'Flanges',
    'WN Flange Ring Joint RF': 'Flanges',
    'Wye (Compact)': 'Tee',
    'Wye (Compact) Reducing': 'Tee',
    'Wye (Standard)': 'Tee',
    'Wye (Standard) Reducing': 'Tee',
    'Y-Strainer': 'Tee',
    'Y-Strainer Reducing': 'Tee'
}


# Used in EF Lookup. Maps 'fitting_category' values to values in the ef lookup table when applying RFQ --> (NOT IN USE)
fitting_category_to_ef_og = {    
    '45 Elbow': '45 Long Radius',
    '45 SR Elbow': '45',
    '90 LR Elbow': '90 Long Radius',
    '90 LR Reducing Elbow': '90 Long Radius',
    '90 SR Elbow': '90 Short Radius',
    '180 Red Return LR': 'Bend Long Radius',  # New option, no exact match, assumed similar to '180 RETURN LR'
    '180 Return LR': 'Bend Long Radius',
    '180 Return SR': 'Bend',
    'Basket Strainer': 'Tee',
    'Bellmouth': '',  # New option, no match
    'Bellow': '',
    'Bellow Reducing': '',
    'Blind Flange': 'Flanges',
    'Blind Flange - Drill And Tap': 'Flanges',
    'Blind Flange - Drill Only': 'Flanges',
    'Bucket Strainer': 'Tee',
    'Bulkhead Fitting': '',  # New option, no match
    'Bushing': '',
    'Bushing Reducing': '',
    'Cap': 'Cap',
    'Compression Fitting': '',  # New option, no match
    'Conical Strainer': '',
    'Coupling': '',
    'Coupling (Half)': '',
    'Coupling (Half) Reducing': '',
    'Coupling Reducing': '',
    'Cross': '',
    'Cross Reducing': '',
    'Elbolet BW': '',
    'Elbolet PE': '',
    'Elbolet THRD': '',
    'Equal Tee': 'Tee',  # Assumed match for 'TEE'
    'Ferrule': '',  # New option, no match
    'Figure 8': '',
    'Flange Other': 'Flanges',
    'Flangeolet': 'Flanges',
    'Flatolet': 'Flanges',
    'Hammer Blind': '',
    'Hex Plug': '',
    'Hose Coupling': '',
    'Lateral': 'Tee',
    'Lateral Reducing': 'Tee',
    'Latrolet BW': '',
    'Latrolet PE': '',
    'Latrolet THRD': '',
    'LJ Flange FF': 'Flanges',
    'LJ Flange Long FF': 'Flanges',
    'LJ Flange Long RF': 'Flanges',
    'LJ Flange Orifice FF': 'Flanges',
    'LJ Flange Orifice RF': 'Flanges',
    'LJ Flange Reducing FF': 'Flanges',
    'LJ Flange Reducing RF': 'Flanges',
    'LJ Flange RF': 'Flanges',
    'LJ Flange Ring Joint FF': 'Flanges',
    'LJ Flange Ring Joint RF': 'Flanges',
    'Nipolet BW': '',
    'Nipolet PE': '',
    'Nipolet THRD': '',
    'Nipple': '',
    'Orifice Plate': 'Flanges',
    'Other Fitting': '',
    'Other Fitting Reducing': '',
    'Other Strainer': 'Tee',
    'Paddle Spacer': '',
    'Pipe Bend': 'Bend',
    'Plate Flange': '',
    'Puddle Flange': '',
    'Reducer Bushing': '',  # New option, no match
    'Reducer Concentric': 'Reducer (Concentric)',
    'Reducer Eccentric': 'Reducer (Eccentric)',
    'Reducer Insert': '',  # New option, no match
    'Reducing Tee': 'Tee',
    'Round Plug': '',  # New option, no match
    'SO Flange FF': 'Flanges',
    'SO Flange Long FF': 'Flanges',
    'SO Flange Long RF': 'Flanges',
    'SO Flange Orifice FF': 'Flanges',
    'SO Flange Orifice RF': 'Flanges',
    'SO Flange Reducing FF': 'Flanges',
    'SO Flange Reducing RF': 'Flanges',
    'SO Flange RF': 'Flanges',
    'SO Flange Ring Joint FF': 'Flanges',
    'SO Flange Ring Joint RF': 'Flanges',
    'Sockolet': '',  # New option, no match
    'Spacer': '',  # New option, no match
    'Spade Blind': '',
    'Spectacle Blind': '',
    'Square Plug': '',  # New option, no match
    'Stub End BW': '',  # New option, no match
    'Stub End Flgd': '',  # New option, no match
    'Stub End PE': '',  # New option, no match
    'Stub End SW': '',  # New option, no match
    'Stub End Te': '',  # New option, no match
    'SW Flange FF': 'Flanges',
    'SW Flange Long FF': 'Flanges',
    'SW Flange Long RF': 'Flanges',
    'SW Flange Orifice FF': 'Flanges',
    'SW Flange Orifice RF': 'Flanges',
    'SW Flange Reducing FF': 'Flanges',
    'SW Flange Reducing RF': 'Flanges',
    'SW Flange RF': 'Flanges',
    'SW Flange Ring Joint FF': 'Flanges',
    'SW Flange Ring Joint RF': 'Flanges',
    'Swage Con. Reducer': 'Reducer (Concentric)',
    'Swage Ecc. Reducer': 'Reducer (Eccentric)',
    'Swage Nipple': '',  # New option, no match
    'Sweepolet BW': '',
    'Sweepolet PE': '',
    'Sweepolet THRD': '',
    'Swivel Joint': '',  # New option, no match
    'T-Strainer': 'Tee',
    'T-Strainer Reducing': 'Tee',
    'Tee': 'Tee',
    'Tee Reducing': 'Tee',
    'THRD Flange FF': 'Flanges',
    'THRD Flange Long FF': 'Flanges',
    'THRD Flange Long RF': 'Flanges',
    'THRD Flange Orifice FF': 'Flanges',
    'THRD Flange Orifice RF': 'Flanges',
    'THRD Flange Reducing FF': 'Flanges',
    'THRD Flange Reducing RF': 'Flanges',
    'THRD Flange RF': 'Flanges',
    'THRD Flange Ring Joint FF': 'Flanges',
    'THRD Flange Ring Joint RF': 'Flanges',
    'Threadolet': '',
    'Union': '',
    'Weldolet': '',
    'WN Flange FF': 'Flanges',
    'WN Flange Long FF': 'Flanges',
    'WN Flange Long RF': 'Flanges',
    'WN Flange Orifice FF': 'Flanges',
    'WN Flange Orifice RF': 'Flanges',
    'WN Flange Reducing FF': 'Flanges',
    'WN Flange Reducing RF': 'Flanges',
    'WN Flange RF': 'Flanges',
    'WN Flange Ring Joint FF': 'Flanges',
    'WN Flange Ring Joint RF': 'Flanges',
    'Wye (Compact)': 'Tee',
    'Wye (Compact) Reducing': 'Tee',
    'Wye (Standard)': 'Tee',
    'Wye (Standard) Reducing': 'Tee',
    'Y-Strainer': 'Tee',
    'Y-Strainer Reducing': 'Tee'
}
