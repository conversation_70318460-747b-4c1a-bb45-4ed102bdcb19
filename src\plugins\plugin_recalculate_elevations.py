import pandas as pd
import numpy as np
from src.atom.roiextraction import calculate_elevation_metrics
from src.utils.fieldmap_utils import map_from_field_map
from natsort import natsort_keygen

def plugin_recalculate_elevations(general_file,
                                  pdf_id_key: str = "pdf_id",
                                  elevation_key: str ="elevation",
                                  output_file="debug/general_elevations_recalculated.xlsx",
                                  sort_page: bool = False):
    """
    Ensure correct elevation and pdf_id field

    elevation_key should be Elevation or elevation
    """
    general_df = pd.read_excel(general_file)

    general_df[elevation_key] = general_df[elevation_key].astype(str)

    print(general_df[elevation_key].head())

    if elevation_key not in ["Elevation", "elevation"]:
        return f"{elevation_key} is not a supported elevation key. Dataframe requires 'Elevation' or 'elevation'"

    if elevation_key == "Elevation":
        general_df['Minimum Elevation'] = np.nan
        general_df['Maximum Elevation'] = np.nan
        general_df['Average Elevation'] = np.nan
        elevation_keys = ["Minimum Elevation", "Maximum Elevation", "Average Elevation"]
        general_df.rename(columns={"Elevation": "elevation"}, inplace=True)
    else:
        general_df['min_elevation'] = np.nan
        general_df['max_elevation'] = np.nan
        general_df['avg_elevation'] = np.nan
        elevation_keys = ["min_elevation", "max_elevation", "avg_elevation"]

    # Check if 'pdf_page' column exists in the DataFrame
    if pdf_id_key in general_df.columns:
        general_df[elevation_keys] = general_df.apply(
            lambda row: calculate_elevation_metrics(row, row[pdf_id_key]), axis=1, result_type='expand'
        )
    else:
        return f"{pdf_id_key} column not found in the DataFrame. Ensure correct name"

    if elevation_key == "Elevation":
        general_df.rename(columns={"Elevation": "elevation"}, inplace=True)

    def replace_nans(df):
        for column in df.columns:
            try:
                df[column] = df[column].replace(['nan', np.nan, pd.NA], '')
            except:
                pass

        return df

    general_df = replace_nans(general_df)

    if sort_page:
        general_df.sort_values("pdf_page", key=natsort_keygen(), inplace=True)

    general_df.to_excel(output_file, index=False)

    res = {
        "message": "Elevations recalculated",
        "save_file": output_file
    }
    return res
