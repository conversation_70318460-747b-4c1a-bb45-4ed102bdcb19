import pandas as pd

def getSimpleFieldMap():
    import sys
    sys.path[0] = ""
    from src.app_paths import getSavedFieldMapJson
    fieldMap = getSavedFieldMapJson()
    simpleFieldMap = {}
    for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
        subData = fieldMap.get(key, {})
        # Discard unuseful data
        for _, v in subData.items():
            try:
                del v["options"]
            except Exception as e:
                pass
        simpleFieldMap.update(subData)
    return simpleFieldMap

def map_from_field_map(df: pd.DataFrame):
    """Maps DataFrame from field map"""
    field_map = getSimpleFieldMap()

    from_field_map = {}
    for k, v in field_map.items():
        from_field_map[v.get("display", k)] = k

    rename_dict = {}
    for column in df.columns:
        rename_dict[column] = from_field_map.get(column, column)
    return df.rename(columns=rename_dict)

def map_to_field_map():
    """Maps DataFrame to field map"""
    field_map = getSimpleFieldMap()

    to_field_map = {}
    for k, v in field_map.items():
        to_field_map[k] = v.get("display", k)

    rename_dict = {}
    for column in df.columns:
        rename_dict[column] = to_field_map.get(column, column)
    return df.rename(columns=rename_dict)