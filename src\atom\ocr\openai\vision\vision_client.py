import re
import os
import json
import logging

from openai import OpenAI
import pandas as pd

from src.atom.ocr.openai.utils.file_handling import encode_image
from src.atom.ocr.openai.utils.data_cleansing import normalize_text
from src.atom.ocr.openai.api_request_parallel_processor import process_api_requests_from_file

# OpenAI API Key
client = OpenAI(api_key = os.getenv("API_KEY2"))


async def generate_vision_requests(folder_path,
                                   output_jsonl,
                                   prompt,
                                   pages: list[int] = None,
                                   is_general=False,
                                   gen_field_list: list[str] = None):
    png_files = [f for f in os.listdir(folder_path) if f.endswith('.png')]

    # Sort the files by page number
    def extract_page_number(filename):
        # Try to extract page number from patterns like "page_100" or "page_1"
        match = re.search(r'page_(\d+)', filename)
        if match:
            return int(match.group(1))
        return

    def extract_column_name(filename):
        # Try to extract column name from patterns like "column_name_page_1.png"
        match = re.search(r'^(.+?)_page_', filename)
        if match:
            return match.group(1)
        return

    with open(output_jsonl, 'w') as f:
        for png_file in sorted(png_files):
            image_path = os.path.join(folder_path, png_file)
            base64_image = encode_image(image_path)

            page_num = extract_page_number(png_file)
            if not page_num:
                print(f"No page number found in filename {png_file}. Skipping.")
                continue

            if pages and page_num not in pages:
                continue

            if is_general:
                # Join all parts except the last two (page number and attempt number)
                column_name = extract_column_name(png_file)
                # Remove trailing "_page" if it exists and is not the entire column name
                # if column_name.endswith("_page") and column_name != "page":
                #     column_name = column_name[:-5]
                # else:
                #     column_name = None

                if gen_field_list and column_name not in gen_field_list:
                    continue

                # Add page number and column name to the prompt for general data
                page_prompt = f"Page {page_num}, Column {column_name}: " + prompt
            else:
                # Add page number to the prompt
                page_prompt = f"Page {page_num}: " + prompt

            request = {
                "model": "gpt-4o",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": page_prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4096,
                "temperature": 0,
                "top_p": 1,
                "frequency_penalty": 0,
                "presence_penalty": 0
            }
            f.write(json.dumps(request) + '\n')

async def process_vision_requests(API_KEY,
                                  requests_filepath,
                                  requests_output_filepath,
                                  request_stage_desc,
                                  cancel_event=None,
                                  next_request_made_cb=None):
    print(f"\n\n-->ASYNC START: {request_stage_desc}")
    await process_api_requests_from_file(
        requests_filepath=requests_filepath,
        save_filepath=requests_output_filepath,
        request_url="https://api.openai.com/v1/chat/completions",  # The endpoint is the same for Vision API
        api_key=API_KEY,
        max_requests_per_minute=float(9500),  # Adjust these values based on your rate limits
        max_tokens_per_minute=float(1900000),  # Adjust these values based on your rate limits
        token_encoding_name="cl100k_base",
        max_attempts=int(5),
        logging_level=int(20),
        cancel_event=cancel_event,
        next_request_made_cb=next_request_made_cb,
    )
    print(f"\n\n-->ASYNC END - {request_stage_desc}")

async def process_vision_results(results_jsonl, is_general=False, is_iso=False):
    combined_df = pd.DataFrame()
    all_data = {} if is_general else []
    failed_files = []
    total_tokens = 0
    iteration_count = 0

    with open(results_jsonl, 'r') as f:
        for line in f:
            try:
                result = json.loads(line)
                request_data = result[0]
                response_data = result[1]

                prompt_text = request_data['messages'][0]['content'][0]['text']
                page_match = re.search(r"Page (\d+)", prompt_text)
                page_num = int(page_match.group(1)) if page_match else 'Unknown'

                if is_general:
                    column_match = re.search(r"Column (.+?):", prompt_text)
                    column_name = column_match.group(1) if column_match else 'Unknown'

                content = response_data['choices'][0]['message']['content']
                tokens_used = response_data['usage']['total_tokens']
                total_tokens += tokens_used


                if is_general:
                    try:
                        # Extract JSON content from the response if it's wrapped in code blocks
                        json_match = re.search(r'```json\n(.*?\n)```', content, re.DOTALL)
                        if json_match:
                            content = json_match.group(1)

                        data = json.loads(content)
                        extracted_text = data[0]['answer'].strip()

                        if page_num not in all_data:
                            all_data[page_num] = {}
                        all_data[page_num][column_name] = extracted_text

                        print(f"Processed page {page_num}, column {column_name} successfully")
                    except Exception as e:
                        print(f"Error processing general data for page {page_num}: {e}")
                        print(f"Content: {content}")
                        failed_files.append({"file": f"Page {page_num}", "error": str(e), "page": page_num})
                else:
                    data = []  # Initialize data as an empty list
                    try:
                        # Extract JSON content from the response if it's wrapped in code blocks
                        json_match = re.search(r'```json\n(.*?\n)```', content, re.DOTALL)
                        if json_match:
                            content = json_match.group(1)

                        data = json.loads(content)
                    except json.JSONDecodeError:
                        print(f"Failed to parse JSON for page {page_num}. Attempting to fix the JSON.")
                        # Try to fix common JSON issues
                        fixed_content = content.replace("None", "null")
                        try:
                            data = json.loads(fixed_content)
                        except json.JSONDecodeError as e:
                            print(f"Still unable to parse JSON after attempted fix: {e}")
                            print(f"Content: {content}")

                    if data:
                        df = pd.DataFrame(data)
                        df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
                        df = df.replace('', pd.NA).dropna(how='all')

                        if len(df) > 0:
                            df['Page'] = page_num
                            all_data.append(df)
                            print(f"Processed page {page_num} successfully with: {len(df)} rows")
                        else:
                            print(f"Warning: Empty DataFrame for page {page_num}")
                            failed_files.append({"file": f"Page {page_num}", "error": "Empty DataFrame", "page": page_num})
                    else:
                        # raise ValueError("DataFrame is empty")
                        print(f"Warning: Empty DataFrame for page {page_num}")
                        failed_files.append({"file": f"Page {page_num}", "error": "Empty DataFrame", "page": page_num})

                print(f"Tokens used for page {page_num}: {tokens_used}")
                # Increment iteration count
                iteration_count += 1


            except Exception as e:
                print(f"Error processing page {page_num}: {e}")
                print("Response content:")
                print(content)
                print("Raw response data:")
                print(response_data)
                failed_files.append({"file": f"Page {page_num}", "error": str(e), "page": page_num})

    if is_general:
        if all_data:
            combined_df = pd.DataFrame.from_dict(all_data, orient='index').sort_index()
            combined_df.index.name = 'pdf_page'  # Name the index
            combined_df = combined_df.map(normalize_text)
            combined_df.to_excel("debug/Combined_Exported_General_Data.xlsx", index=True)
            print("Combined general data saved to Combined_Exported_General_Data.xlsx")
        else:
            print("No general data to combine. All files failed to process.")
    else:
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.map(normalize_text)
            combined_df.to_excel("debug/Combined_Exported_TABLE_Data.xlsx", index=False)
            print("Combined TABLE data saved to Combined_Exported_TABLE_Data.xlsx")
        else:
            print("No TABLE data to combine. All files failed to process.")
            combined_df = pd.DataFrame()  # Return an empty DataFrame instead of None

    print(f"Total tokens used: {total_tokens}")

    if failed_files:
        failed_files_df = pd.DataFrame(failed_files)
        failed_files_df.to_csv("debug/Failed_Files.csv", index=False)
        print("Failed files exported to Failed_Files.csv")
        print("Failed files:")
        for file in failed_files:
            print(f"  Page {file['page']}: {file['file']} - {file['error']}")
    else:
        print("All files processed successfully")

    return combined_df #if all_data else None



def extract_table_gptVision_single(image_path, bom_prompt):
    # Getting the base64 string
    base64_image = encode_image(image_path)

    response = client.chat.completions.create(
    model="gpt-4o",
    messages=[
    {
        "role": "user",
        "content": [
        {"type": "text", "text": f"{bom_prompt}"},
        {
            "type": "image_url",
            "image_url": {
            "url": f"data:image/png;base64,{base64_image}",
            "detail": "high"
            },
        },
        ],
    }
    ],
    max_tokens=4096,
    temperature=1,
    top_p=1,
    frequency_penalty=0,
    presence_penalty=0
    )

    print(response.choices[0])

    # Extracting the content from the response object
    content = response.choices[0].message.content

    try:
        # Parse the JSON content
        data = json.loads(content)

        # Convert the list of dictionaries to a DataFrame
        df = pd.DataFrame(data)

        # Clean up the DataFrame
        df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
        df = df.replace('', pd.NA).dropna(how='all')

        df.to_excel("Exported_Dictionary.xlsx", index=False)

        # Printing the DataFrame
        print(df)

    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {str(e)}")
        print("Response content:")
        print(content)
        return pd.DataFrame()  # Return an empty DataFrame if parsing fails

    return df

def extract_table_gptVision(folder_path, prompt, export_interval=10):
    all_dataframes = []
    failed_files = []
    total_tokens = 0
    iteration_count = 0

    # Get all PNG files in the folder
    png_files = [f for f in os.listdir(folder_path) if f.endswith('.png')]

    for png_file in sorted(png_files):
        # Extract the page number from the filename
        page_num_str = png_file.split('_')[-1].split('.')[0]
        page_num = int(page_num_str)

        # Full path to the image
        image_path = os.path.join(folder_path, png_file)

        # print(f"Processing image: {png_file}")
        # print(f"Page number: {page_num}")

        try:
            # Getting the base64 string
            base64_image = encode_image(image_path)

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": f"{prompt}"},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}",
                                    "detail": "auto"
                                },
                            },
                        ],
                    }
                ],
                max_tokens=4096,
                temperature=0,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )

            # Extracting the content and token count from the response object
            content = response.choices[0].message.content

            try:
                tokens_used = response.usage.total_tokens
                total_tokens += tokens_used
            except Exception as e:
                print(f"Error getting total tokens for page {page_num}: {e}")

            try:
                # Parse the JSON content
                data = json.loads(content)

                # Convert the list of dictionaries to a DataFrame
                df = pd.DataFrame(data)

                # Clean up the DataFrame
                df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
                df = df.replace('', pd.NA).dropna(how='all')

                # Check if the DataFrame has rows
                if len(df) > 0:
                    # Add a column for the page number
                    df['Page'] = page_num
                    all_dataframes.append(df)
                    print(f"Processed page {page_num} successfully with: {len(df)} rows")
                else:
                    raise ValueError("DataFrame is empty")

            except (json.JSONDecodeError, ValueError) as e:
                logging.error(f"Error processing page {page_num}: {e}", exc_info=True)
                print(f"Error processing page {page_num}: {e}")
                print("Response content:")
                print(content)
                failed_files.append({"file": png_file, "error": str(e), "page": page_num})
                continue  # Skip to the next file

            print(f"Tokens used for page {page_num}: {tokens_used}")

            # Increment iteration count
            iteration_count += 1

            # Export combined DataFrame at specified intervals
            if iteration_count % export_interval == 0:
                interim_df = pd.concat(all_dataframes, ignore_index=True)
                interim_df.to_excel(f"Interim_Combined_Table_to_Page_{page_num}.xlsx", index=False)
                print(f"Interim combined data saved up to page {page_num}")

        except Exception as e:
            print(f"Error processing page {page_num}: {e}")
            print("Response content:")
            print(content)
            failed_files.append({"file": png_file, "error": str(e), "page": page_num})
            continue  # Skip to the next file

    # Combine all DataFrames
    if all_dataframes:
        combined_df = pd.concat(all_dataframes, ignore_index=True)

        # Apply the normalization function to the entire DataFrame
        combined_df = combined_df.map(normalize_text)

        # Save the combined DataFrame to Excel
        combined_df.to_excel("Combined_Exported_Markdown.xlsx", index=False)
        print("Combined data saved to Combined_Exported_Markdown.xlsx")
    else:
        print("No data to combine. All files failed to process.")

    # Print total tokens used
    print(f"Total tokens used: {total_tokens}")

    # Export failed files to CSV
    if failed_files:
        failed_files_df = pd.DataFrame(failed_files)
        failed_files_df.to_csv("Failed_Files.csv", index=False)
        print("Failed files exported to Failed_Files.csv")
        print("Failed files:")
        for file in failed_files:
            print(f"  Page {file['page']}: {file['file']} - {file['error']}")
    else:
        print("All files processed successfully")

    return combined_df if all_dataframes else None

def extract_text_gptVision(folder_path, prompt, model, export_interval=10):
    all_data = {}
    failed_files = []
    total_tokens = 0
    iteration_count = 0

    folder_path = os.path.join(folder_path)

    export_interval = int(export_interval)

    # Get all PNG files in the folder
    png_files = [f for f in os.listdir(folder_path) if f.endswith('.png')]

    for png_file in sorted(png_files):
        # Extract the page number from the filename
        page_num_str = png_file.split('_')[-1].split('.')[0]
        page_num = int(page_num_str) # + 1  # Add 1 to the extracted number

        # Extract the column name from the filename
        column_name = '_'.join(png_file.split('_')[:-2])

        # Full path to the image
        image_path = os.path.join(folder_path, png_file)

        print(f"Processing image: {png_file}")
        print(f"Page number: {page_num}")
        print(f"Column name: {column_name}")

        try:
            # Getting the base64 string
            base64_image = encode_image(image_path)

            response = client.chat.completions.create(
                model=model,  #<- GPT Model
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": f"{prompt}"},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}",
                                    "detail": "auto"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=4096,
                temperature=1,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )

            # Extracting the content and token count from the response object
            content = response.choices[0].message.content

            try:
                tokens_used = response.usage.total_tokens
                total_tokens += tokens_used
            except Exception as e:
                print(f"Error Getting total tokens {page_num}")

            try:
                # Parse the JSON content

                # Remove trailing commas
                cleaned_content = re.sub(r',\s*]', ']', content)
                data = json.loads(cleaned_content)
                #data = demjson.loads(content)

                # Check if the JSON content is valid and has an "answer" key
                if not data or not isinstance(data, list) or not all(isinstance(d, dict) and 'answer' in d for d in data):
                    raise ValueError("Invalid JSON response format")

                # Extract the text value
                extracted_text = data[0]['answer'].strip()

                # Add the extracted text to the dictionary
                if page_num not in all_data:
                    all_data[page_num] = {}
                all_data[page_num][column_name] = extracted_text

                print(f"Processed page {page_num} successfully with extracted text: {extracted_text}")

            except (json.JSONDecodeError, ValueError) as e:
                print(f"Error processing page {page_num}: {str(e)}")
                print("Response content:")
                print(content)
                failed_files.append({"file": png_file, "error": str(e), "page": page_num})
                continue  # Skip to the next file

            try:
                print(f"Tokens used for page {page_num}: {tokens_used}")
            except Exception as e:
                print(f"Tokens used not set for page: {page_num}", e)

            # Increment iteration count
            iteration_count += 1

            print(f"\n\nITERATION: {iteration_count}")

            # Export combined DataFrame at specified intervals
            if iteration_count % export_interval == 0:
                interim_df = pd.DataFrame.from_dict(all_data, orient='index').sort_index()
                interim_df.to_excel(f"Interim_Combined_Data_to_Page_{page_num}.xlsx", index=True)
                print(f"Interim combined data saved up to page {page_num}")

        except Exception as e:
            print(f"Error processing page {page_num}: {str(e)}")
            if 'content' in locals():
                print("Response content:")
                print(content)
            failed_files.append({"file": png_file, "error": str(e), "page": page_num})
            continue  # Skip to the next file

    # Combine all Data into a DataFrame
    if all_data:
        combined_df = pd.DataFrame.from_dict(all_data, orient='index').sort_index()

        # Apply the normalization function to the entire DataFrame
        combined_df = combined_df.map(normalize_text)

        # Save the combined DataFrame to Excel
        combined_df.to_excel("Combined_Exported_Data.xlsx", index=True)
        print("Combined data saved to Combined_Exported_Data.xlsx")
    else:
        print("No data to combine. All files failed to process.")

    # Print total tokens used
    print(f"Total tokens used: {total_tokens}")

    # Export failed files to CSV
    if failed_files:
        failed_files_df = pd.DataFrame(failed_files)
        failed_files_df.to_csv("Failed_Files.csv", index=False)
        print("Failed files exported to Failed_Files.csv")
        print("Failed files:")
        for file in failed_files:
            print(f"  Page {file['page']}: {file['file']} - {file['error']}")
    else:
        print("All files processed successfully")

    return combined_df if all_data else None
