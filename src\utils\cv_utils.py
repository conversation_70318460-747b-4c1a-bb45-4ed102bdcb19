import cv2
import numpy as np
from typing import Tuple, Union, Optional
import pandas as pd
import os
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing


def detect_whitespace_percentage(
    image: Union[str, np.ndarray],
    threshold: int = 230,
    blank_threshold: float = 98.0
) -> Tuple[float, bool]:
    """
    Detects the percentage of whitespace in an image to determine if it's blank.

    Args:
        image: Either a path to an image file or a numpy array containing the image data
        threshold: Pixel value threshold to consider as white (0-255), default 230
        blank_threshold: Percentage threshold above which an image is considered blank, default 98.0%

    Returns:
        Tuple containing:
            - float: Percentage of the image that is whitespace (0-100)
            - bool: True if the image is considered blank, False otherwise

    Example:
        >>> whitespace_pct, is_blank = detect_whitespace_percentage("path/to/image.jpg")
        >>> print(f"Image is {whitespace_pct:.2f}% whitespace and is {'blank' if is_blank else 'not blank'}")
    """
    # Load the image if a path is provided
    if isinstance(image, str):
        img = cv2.imread(image)
        if img is None:
            raise ValueError(f"Could not load image from path: {image}")
    else:
        img = image.copy()

    # Convert to grayscale if the image is in color
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    # Apply thresholding to separate white pixels from non-white pixels
    # Pixels with value >= threshold will be set to 255 (white)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

    # Count white pixels (value 255)
    white_pixels = np.sum(binary == 255)
    total_pixels = binary.size

    # Calculate whitespace percentage
    whitespace_percentage = (white_pixels / total_pixels) * 100

    # Determine if the image is blank based on the whitespace percentage
    is_blank = whitespace_percentage >= blank_threshold

    return whitespace_percentage, is_blank


def is_image_blank(
    image: Union[str, np.ndarray],
    threshold: int = 230,
    blank_threshold: float = 98.0
) -> bool:
    """
    Simplified function that just returns whether an image is blank.

    Args:
        image: Either a path to an image file or a numpy array containing the image data
        threshold: Pixel value threshold to consider as white (0-255), default 230
        blank_threshold: Percentage threshold above which an image is considered blank, default 98.0%

    Returns:
        bool: True if the image is considered blank, False otherwise
    """
    whitespace_pct, is_blank = detect_whitespace_percentage(image, threshold, blank_threshold)
    return is_blank


# Helper function for multiprocessing - must be at module level to be picklable
def _process_single_image(args):
    """
    Process a single image and return results.
    This function must be at module level for multiprocessing.

    Args:
        args: Tuple containing (idx, img, threshold, blank_threshold)

    Returns:
        Dict with image_name, whitespace_pct, is_blank, and optional error
    """
    idx, img, threshold, blank_threshold = args
    try:
        # Get image name (filename or index)
        if isinstance(img, str):
            image_name = os.path.basename(img)
        else:
            image_name = f"image_{idx}"

        # Get whitespace percentage and blank status
        whitespace_pct, is_blank = detect_whitespace_percentage(img, threshold, blank_threshold)

        return {
            "image_name": image_name,
            "whitespace_pct": whitespace_pct,
            "is_blank": is_blank
        }
    except Exception as e:
        # Handle errors gracefully
        return {
            "image_name": f"image_{idx}" if not isinstance(img, str) else os.path.basename(img),
            "whitespace_pct": None,
            "is_blank": None,
            "error": str(e)
        }


def analyze_images_whitespace(
    images: Union[list[str], list[np.ndarray]],
    threshold: int = 230,
    blank_threshold: float = 98.0,
    use_multiprocessing: bool = True,
    num_processes: Optional[int] = None
) -> pd.DataFrame:
    """
    Analyzes multiple images for whitespace percentage and blank detection.
    Optimized for speed using parallel processing when possible.

    Args:
        images: List of image paths or numpy arrays containing image data
        threshold: Pixel value threshold to consider as white (0-255), default 230
        blank_threshold: Percentage threshold above which an image is considered blank, default 98.0%
        use_multiprocessing: Whether to use multiprocessing for faster processing, default True
        num_processes: Number of processes to use, default is None (uses CPU count)

    Returns:
        pandas DataFrame with columns:
            - image_name: Name of the image (filename or index)
            - whitespace_pct: Percentage of whitespace in the image
            - is_blank: Boolean indicating if the image is blank

    Example:
        >>> image_paths = ["image1.jpg", "image2.jpg", "image3.jpg"]
        >>> results = analyze_images_whitespace(image_paths)
        >>> print(results)
    """
    results = []

    # Prepare arguments for processing
    process_args = [(i, img, threshold, blank_threshold) for i, img in enumerate(images)]

    # Use multiprocessing if enabled and we have more than one image
    if use_multiprocessing and len(images) > 1:
        # Determine number of processes
        if num_processes is None:
            num_processes = multiprocessing.cpu_count()

        # Limit processes to number of images or CPU count, whichever is smaller
        num_processes = min(num_processes, len(images), multiprocessing.cpu_count())

        # Process images in parallel
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            # Map function to all images
            results = list(executor.map(_process_single_image, process_args))
    else:
        # Process images sequentially
        results = [_process_single_image(args) for args in process_args]

    # Convert results to DataFrame
    df = pd.DataFrame(results)
    df['is_blank'] = df['is_blank'].astype(int)

    # Sort by image_name for consistency
    if "image_name" in df.columns:
        df = df.sort_values("image_name").reset_index(drop=True)

    return df
