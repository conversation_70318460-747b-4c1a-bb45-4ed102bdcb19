from src.utils.logger import logger
from PySide6.QtWidgets import (QVBoxLayout, QTabWidget, QGridLayout, QButtonGroup, QScrollArea, QFrame,
                               QPushButton, QSizePolicy, QWidget, QLabel)
from PySide6.QtCore import Qt
from functools import partial
from pubsub import pub


class PlaceholderView(QWidget):

    def __init__(self, parent, name):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        label = QLabel(f"{name}")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(label)