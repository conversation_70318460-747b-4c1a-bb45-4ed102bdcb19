from PySide6.QtWidgets import QPushButton, QCheckBox, QTextEdit
from PySide6.QtGui import QShowEvent, QShortcut, QKeySequence
from PySide6.QtWidgets import QSizePolicy
from PySide6.QtCore import Qt, Signal
from .baseform import BaseForm
from src.app_paths import resource_path


placeholder = """Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. 
Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis ligula consectetur, ultrices mauris. 
Maecenas vitae mattis tellus. Nullam quis imperdiet augue. 
Vestibulum auctor ornare leo, non suscipit magna interdum eu. Curabitur pellentesque nibh nibh, at maximus ante fermentum sit amet.

"""

USER_AGREEMENT_FILE = resource_path("src/data/useragreement.txt")


class UserAgreementForm(BaseForm):

    sgnUserAgreementSigned = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)

    def initUi(self):
        self.title.setText("User Agreement")
        self.subtitle.hide()

        self.addVSpace()

        try:
            text = ""
            with open(USER_AGREEMENT_FILE) as f:
                lines = f.readlines()
                for l in lines:
                    if not l.strip():
                        continue
                    text += l
                    if l.endswith("</b>"):
                        text += "<br>"
        except:
            text = placeholder

        self.agreementText: QTextEdit = QTextEdit(text)
        self.agreementText.setReadOnly(True)
        self.layout().addRow(self.agreementText)

        self.addVSpace()

        self.pbNext = QPushButton("Next")
        self.chkAgree = QCheckBox("I accept the user agreement")
        self.chkAgree.stateChanged.connect(lambda: self.pbNext.setEnabled(self.chkAgree.isChecked()))
        self.layout().addRow(self.chkAgree)

        self.addVSpace()

        self.pbNext.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbNext.setText("Create Account")
        self.pbNext.setMinimumHeight(48)
        self.pbNext.clicked.connect(self.onNext)
        self.layout().addRow(self.pbNext)

        self.pbNext.setContentsMargins(0, 132, 0, 32)

        self.setFloatingButtonBack()

        shortcut = QShortcut(QKeySequence(Qt.Key.Key_Return), self)
        shortcut.activated.connect(self.onReturnPressed)
        shortcut.setEnabled(True)

    def initDefaults(self):
        self.chkAgree.setFocus()
        self.chkAgree.setChecked(False)
        self.pbNext.setEnabled(False)

    def onLinkActivated(self, event):
        print(event)

    def onNext(self, event=None):
        self.sgnUserAgreementSigned.emit()
        self.initDefaults()
    
    def onFloatingButton(self):
        self.sgnSwitchTo.emit("CompanyProfileForm")
    
    def showEvent(self, event: QShowEvent) -> None:
        self.chkAgree.setFocus()
        return super().showEvent(event)

    def onReturnPressed(self, event=None):
        if self.chkAgree.hasFocus():
            self.pbNext.setFocus()
        elif self.pbNext.hasFocus():
            self.onNext()