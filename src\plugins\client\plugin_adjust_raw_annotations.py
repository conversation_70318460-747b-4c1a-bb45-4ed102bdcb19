"""
adhoc grouping purely based on some text match from source preprocessing text
"""
import fitz
import os
import json
import pandas as pd
from src.app_paths import getSourceRawDataPath

def plugin_adjust_raw_annotations(project_source: tuple, height: str = ""):
    """
    """
    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        return "Project source needs to be preprocessed first."

    print(projectId, filename, fileExists)

    doc = fitz.open(filename)

    rawFeather = getSourceRawDataPath(project_source[0], project_source[1])
    df = pd.read_feather(rawFeather)

    print(df.head())

    annot = "AutoCAD SHX Text".lower()

    def resize(annot):
        """Resize the annotation to the height of the text"""
        x0,y0,x1,y1 = annot
        w, h = x1-x0, y1-y0
        new_rect = [x0 + 2, y0 + h//3, x1 - 2, y1 - h//3]
        return new_rect

    df["coordinates"] = df.apply(
        lambda row: resize(row["coordinates"]) if row["title"].lower() == annot else row["coordinates"],
        axis=1
    )
    df["coordinates2"] = df.apply(
        lambda row: row["coordinates"] if row["title"].lower() == annot else row["coordinates2"],
        axis=1
    )

    os.rename(rawFeather, os.path.join(os.path.dirname(rawFeather), "raw_data_backup.feather"))

    df.to_feather(rawFeather)