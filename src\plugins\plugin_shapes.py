import os
import pandas as pd
import json
import fitz  # PyMuPDF
import numpy as np
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Rectangle, Circle, Ellipse
from matplotlib.collections import PatchCollection
import math
import logging
from datetime import datetime
from pypdf import PdfWriter, PdfReader
import io

# Set up logging to file
def setup_logging(output_dir):
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f"shape_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also output to console
        ]
    )
    return logging.getLogger("shape_extractor")

def plugin_extract_shapes(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf",
                          page_numbers: str=None,
                          save_visualizations: bool=False,
                          output_dir: str="debug/shapes",
                          save_color_pdf: bool=False,
                          extended_mode: bool=True,
                          ensure_visible_colors: bool=False,
                          color_filter: str="blue,red",
                          combine_pdfs: bool=False,
                          save_page_classification: bool=True):
    """
    Extracts shapes from a PDF file using PyMuPDF.

    This plugin detects and extracts various shapes including:
    - Lines
    - Rectangles
    - Circles/Ellipses
    - Polygons
    - Curves

    Args:
        pdf_file (str): Path to the PDF file to analyze
        page_numbers (str, optional): List of page numbers to process (1-indexed, comma-separated). If None, process all pages.
        save_visualizations (bool): Whether to save visualizations of detected shapes
        output_dir (str): Directory to save output files (will be created if it doesn't exist)
        save_color_pdf (bool): Whether to save visualizations to PDF with original colors
        extended_mode (bool): Whether to use extended mode for drawing extraction (may find more shapes but requires more robust error handling)
        ensure_visible_colors (bool): Replace white or near-white colors with more visible alternatives
        color_filter (str): Filter shapes by color. Options: comma-separated list of colors like 'red,blue' or None (no filter)
        combine_pdfs (bool): Whether to combine pages with/without shapes into separate PDFs. If False, individual pages will still be processed but not combined.
        save_page_classification (bool): Whether to save page classification to Excel files

    Returns:
        pandas.DataFrame: DataFrame containing shape details
    """
    logger = setup_logging(output_dir)

    # Check if file exists
    if not os.path.exists(pdf_file):
        logger.error(f"File not found: {pdf_file}")
        return None

    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")

    # Create separate directories for pages with and without shapes
    with_shapes_dir = os.path.join(output_dir, "with_shapes")
    no_shapes_dir = os.path.join(output_dir, "no_shapes")

    if save_visualizations or combine_pdfs or save_page_classification:
        if not os.path.exists(with_shapes_dir):
            os.makedirs(with_shapes_dir)
            logger.info(f"Created directory for pages with shapes: {with_shapes_dir}")
        if not os.path.exists(no_shapes_dir):
            os.makedirs(no_shapes_dir)
            logger.info(f"Created directory for pages without shapes: {no_shapes_dir}")

    # Get PDF basename for output files
    pdf_basename = os.path.splitext(os.path.basename(pdf_file))[0]

    # Initialize lists to store shape data
    shapes = []

    # Track pages with and without shapes
    pages_with_shapes = []
    pages_without_shapes = []

    # For combining PDFs with shapes
    pdf_pages_with_shapes = []

    try:
        # Open the PDF file
        doc = fitz.open(pdf_file)

        # Parse page numbers (1-indexed) if provided
        processed_page_numbers = []
        if page_numbers:
            try:
                # Process the page numbers string
                page_ranges = page_numbers.split(',')
                for page_range in page_ranges:
                    page_range = page_range.strip()

                    # Check if it's a range (contains '-')
                    if '-' in page_range:
                        start, end = map(int, page_range.split('-'))
                        # Convert 1-indexed to 0-indexed for internal processing
                        processed_page_numbers.extend(range(start-1, end))
                    else:
                        # It's a single page
                        page_num = int(page_range)
                        # Convert 1-indexed to 0-indexed for internal processing
                        processed_page_numbers.append(page_num-1)

                # Remove duplicates and sort
                processed_page_numbers = sorted(list(set(processed_page_numbers)))

                # Validate page numbers are within document range
                processed_page_numbers = [p for p in processed_page_numbers if 0 <= p < len(doc)]

                if not processed_page_numbers:
                    logger.warning(f"No valid page numbers found in: {page_numbers}. Using all pages.")
                    processed_page_numbers = list(range(len(doc)))
            except ValueError:
                logger.warning(f"Invalid page numbers format: {page_numbers}. Using all pages.")
                processed_page_numbers = list(range(len(doc)))
        else:
            processed_page_numbers = list(range(len(doc)))

        # Process each selected page
        for page_idx in processed_page_numbers:
            if page_idx < 0 or page_idx >= len(doc):
                logger.warning(f"Page {page_idx+1} is out of range. PDF has {len(doc)} pages.")
                continue

            page = doc[page_idx]
            logger.info(f"Processing page {page_idx+1}/{len(doc)}")

            # Get page dimensions
            page_width, page_height = page.rect.width, page.rect.height

            # Extract paths (vector graphics)
            paths = page.get_drawings(extended=extended_mode)

            # Visualization setup if enabled
            if save_visualizations:
                fig, ax = plt.subplots(figsize=(page_width/72, page_height/72))
                ax.set_xlim(0, page_width)
                ax.set_ylim(page_height, 0)  # Inverted y-axis (origin at top-left)
                patches = []

            # Process each path
            for i, path in enumerate(paths):
                # Extract path properties
                path_type = "unknown"
                stroke_color = path.get("color")
                if stroke_color:
                    # Convert to RGB if it's not already
                    if len(stroke_color) == 1:  # Grayscale
                        gray = stroke_color[0]
                        stroke_color = (gray, gray, gray)
                    elif len(stroke_color) == 4:  # CMYK
                        # Simple CMYK to RGB conversion
                        c, m, y, k = stroke_color
                        r = 1 - min(1, c + k)
                        g = 1 - min(1, m + k)
                        b = 1 - min(1, y + k)
                        stroke_color = (r, g, b)

                # Get fill color if available
                fill_color = path.get("fill_color")
                if fill_color:
                    # Convert to RGB if it's not already
                    if len(fill_color) == 1:  # Grayscale
                        gray = fill_color[0]
                        fill_color = (gray, gray, gray)
                    elif len(fill_color) == 4:  # CMYK
                        # Simple CMYK to RGB conversion
                        c, m, y, k = fill_color
                        r = 1 - min(1, c + k)
                        g = 1 - min(1, m + k)
                        b = 1 - min(1, y + k)
                        fill_color = (r, g, b)

                stroke_width = path.get("width", 0)
                fill = path.get("fill", False)
                stroke = path.get("stroke", False)

                # Enhanced fill detection for polygons and rectangles
                if path_type == "polygon" or path_type == "rectangle":
                    logger.info(f"Polygon/Rectangle: fill={fill}, stroke={stroke}, closed={is_closed}, has_curves={has_curves}")
                    # Check if the path has a 'f' command which indicates fill
                    has_f_command = any(item[0] == 'f' for item in items if isinstance(item, (list, tuple)) and len(item) > 0)
                    if has_f_command:
                        fill = True
                        logger.info(f"  Found 'f' command, setting fill=True")

                    # Check if the path is closed, which often indicates it should be filled
                    if is_closed and not fill and len(vertices) > 2:
                        # For closed paths with 3+ vertices, assume they might be filled
                        fill = True
                        logger.info(f"  Closed path with {len(vertices)} vertices, setting fill=True")

                # Get the path's rect if available (can be used as fallback for bbox)
                path_rect = path.get("rect")

                # Get the items (segments) in the path
                items = path.get("items", [])

                # Skip empty paths
                if not items:
                    continue

                # Extract vertices from path items
                vertices = []

                # Track if this is a closed shape
                is_closed = False

                # Track shape type based on commands
                has_curves = False

                for item in items:
                    cmd = item[0]  # Command (m = move, l = line, c = curve, etc.)

                    if cmd == "m":  # Move to
                        try:
                            vertices.append(item[1:])
                        except Exception as e:
                            logger.warning(f"Warning: Could not parse move command: {item}, error: {e}")
                    elif cmd == "l":  # Line to
                        try:
                            vertices.append(item[1:])
                        except Exception as e:
                            logger.warning(f"Warning: Could not parse line command: {item}, error: {e}")
                    elif cmd == "re":  # Rectangle
                        try:
                            # Standard format: x, y, width, height
                            if len(item) >= 5:
                                x, y, w, h = item[1:5]
                                vertices = [(x, y), (x+w, y), (x+w, y+h), (x, y+h)]
                                path_type = "rectangle"
                                is_closed = True
                            # Alternative format: might have just two points (top-left, bottom-right)
                            elif len(item) >= 3:
                                try:
                                    # Assuming item[1] is top-left and item[2] is bottom-right
                                    x0, y0 = item[1]
                                    x1, y1 = item[2]
                                    vertices = [(x0, y0), (x1, y0), (x1, y1), (x0, y1)]
                                    path_type = "rectangle"
                                    is_closed = True
                                except (TypeError, ValueError):
                                    # If points are not as expected, treat as polygon
                                    vertices = item[1:]
                                    path_type = "polygon"
                                    is_closed = True
                            else:
                                logger.warning(f"Warning: Rectangle command with unexpected format: {item}")
                                continue
                        except (ValueError, TypeError, IndexError) as e:
                            logger.warning(f"Warning: Could not parse rectangle command: {item}, error: {e}")
                            continue
                        break
                    elif cmd == "c":  # Curve
                        try:
                            # Bezier curve control points and end point
                            if len(item) >= 6:
                                vertices.append(item[5:])  # End point of curve
                            else:
                                # If format is different, try to get the last point
                                vertices.append(item[-1])
                            has_curves = True
                        except Exception as e:
                            logger.warning(f"Warning: Could not parse curve command: {item}, error: {e}")
                    elif cmd == "h":  # Close path
                        is_closed = True
                    else:
                        # Handle other commands
                        try:
                            # For unknown commands, try to extract points if present
                            if len(item) > 1:
                                for point in item[1:]:
                                    if isinstance(point, (list, tuple)) and len(point) >= 2:
                                        vertices.append(point)
                        except Exception as e:
                            logger.warning(f"Warning: Could not parse unknown command: {item}, error: {e}")

                # Determine shape type based on vertices and commands
                if path_type == "unknown":
                    if len(vertices) == 0:
                        path_type = "empty"
                    elif len(vertices) == 2 and not is_closed:
                        path_type = "line"
                    elif has_curves:
                        path_type = "curve"
                    elif len(vertices) == 4 and is_closed:
                        # Check if it's a rectangle (parallel sides)
                        x_coords = []
                        y_coords = []
                        for v in vertices:
                            try:
                                if hasattr(v, 'x') and hasattr(v, 'y'):
                                    # It's a Point object
                                    x_coords.append(float(v.x))
                                    y_coords.append(float(v.y))
                                elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                    # It's a tuple or list
                                    x_coords.append(float(v[0]))
                                    y_coords.append(float(v[1]))
                                else:
                                    logger.warning(f"Warning: Unrecognized vertex format: {v}")
                            except (TypeError, IndexError, ValueError, AttributeError) as e:
                                logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                continue

                        if (len(set(x_coords)) == 2 and len(set(y_coords)) == 2):
                            path_type = "rectangle"
                        else:
                            path_type = "polygon"
                    elif is_closed and len(vertices) > 2:
                        # Check if it might be a circle/ellipse
                        if len(vertices) >= 8:
                            # Compute center and average radius
                            x_coords = []
                            y_coords = []
                            for v in vertices:
                                try:
                                    if hasattr(v, 'x') and hasattr(v, 'y'):
                                        # It's a Point object
                                        x_coords.append(float(v.x))
                                        y_coords.append(float(v.y))
                                    elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                        # It's a tuple or list
                                        x_coords.append(float(v[0]))
                                        y_coords.append(float(v[1]))
                                    else:
                                        logger.warning(f"Warning: Unrecognized vertex format: {v}")
                                except (TypeError, IndexError, ValueError, AttributeError) as e:
                                    logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                    continue

                            center_x = sum(x_coords) / len(x_coords)
                            center_y = sum(y_coords) / len(y_coords)

                            # Calculate distances from center to each vertex
                            distances = [math.sqrt((x - center_x)**2 + (y - center_y)**2)
                                        for x, y in zip(x_coords, y_coords)]
                            avg_radius = sum(distances) / len(distances)

                            # Calculate variance in distances
                            variance = sum((d - avg_radius)**2 for d in distances) / len(distances)

                            # If variance is small, it's likely a circle
                            if variance < 1.0:
                                path_type = "circle"
                            else:
                                # Check if it might be an ellipse
                                # For simplicity, we'll just call it a polygon
                                path_type = "polygon"
                        else:
                            path_type = "polygon"
                    else:
                        path_type = "polygon"

                # Calculate bounding box
                bbox = [0, 0, 0, 0]  # Default empty bbox

                # Try to get bbox from vertices first
                if vertices:
                    # Extract numeric values from vertices
                    # Some vertices might be Point objects which don't support direct comparison
                    x_coords = []
                    y_coords = []
                    for v in vertices:
                        try:
                            if hasattr(v, 'x') and hasattr(v, 'y'):
                                # It's a Point object
                                x_coords.append(float(v.x))
                                y_coords.append(page_height - float(v.y))  # Invert y-coordinate
                            elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                # It's a tuple or list
                                x_coords.append(float(v[0]))
                                y_coords.append(page_height - float(v[1]))  # Invert y-coordinate
                            else:
                                logger.warning(f"Warning: Unrecognized vertex format: {v}")
                        except (TypeError, IndexError, ValueError, AttributeError) as e:
                            logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                            continue

                    # Double-check that we have numeric values
                    if x_coords and y_coords:
                        try:
                            x0, y0 = min(x_coords), min(y_coords)
                            x1, y1 = max(x_coords), max(y_coords)
                            bbox = [x0, y0, x1, y1]
                        except TypeError as e:
                            logger.warning(f"Warning: Could not compute bounding box from vertices, error: {e}")
                            bbox = [0, 0, 0, 0]

                # If we couldn't get bbox from vertices, try using path_rect
                if bbox == [0, 0, 0, 0] and path_rect:
                    try:
                        # path_rect is usually [x0, y0, x1, y1]
                        if isinstance(path_rect, (list, tuple)) and len(path_rect) >= 4:
                            bbox = [float(path_rect[0]), page_height - float(path_rect[3]), float(path_rect[2]), page_height - float(path_rect[1])]
                        elif hasattr(path_rect, 'x0') and hasattr(path_rect, 'y0') and \
                             hasattr(path_rect, 'x1') and hasattr(path_rect, 'y1'):
                            bbox = [float(path_rect.x0), page_height - float(path_rect.y1), float(path_rect.x1), page_height - float(path_rect.y0)]
                    except (TypeError, IndexError, ValueError, AttributeError) as e:
                        logger.warning(f"Warning: Could not compute bounding box from path_rect, error: {e}")

                # If we still don't have a valid bbox, try to get it from the item's rect
                if bbox == [0, 0, 0, 0]:
                    for item in items:
                        if len(item) > 1 and isinstance(item[1], (list, tuple)) and len(item[1]) >= 4:
                            try:
                                # Some items might contain rect information
                                rect_item = item[1]
                                bbox = [float(rect_item[0]), page_height - float(rect_item[3]), float(rect_item[2]), page_height - float(rect_item[1])]
                                break
                            except (TypeError, IndexError, ValueError) as e:
                                continue

                # Ensure bbox has valid dimensions
                if bbox != [0, 0, 0, 0]:
                    # Make sure x0 <= x1 and y0 <= y1
                    x0, y0, x1, y1 = bbox
                    if x0 > x1:
                        x0, x1 = x1, x0
                    if y0 > y1:
                        y0, y1 = y1, y0
                    bbox = [x0, y0, x1, y1]

                    # If bbox has zero area, expand it slightly
                    if x0 == x1:
                        x0 -= 0.5
                        x1 += 0.5
                    if y0 == y1:
                        y0 -= 0.5
                        y1 += 0.5
                    bbox = [x0, y0, x1, y1]

                # Store shape data
                # Convert vertices to a standard format (list of tuples)
                standardized_vertices = []
                for v in vertices:
                    try:
                        if hasattr(v, 'x') and hasattr(v, 'y'):
                            # It's a Point object
                            standardized_vertices.append((float(v.x), page_height - float(v.y)))  # Invert y-coordinate
                        elif isinstance(v, (list, tuple)) and len(v) >= 2:
                            # Check if it's a tuple of Point objects
                            if all(hasattr(p, 'x') and hasattr(p, 'y') for p in v if p is not None):
                                # It's a tuple of Point objects, add each point separately
                                for p in v:
                                    if p is not None:
                                        standardized_vertices.append((float(p.x), page_height - float(p.y)))  # Invert y-coordinate
                            # Check if it's a list of coordinate pairs
                            elif all(isinstance(p, (list, tuple)) and len(p) >= 2 for p in v if p is not None):
                                # It's a list of coordinate pairs, add each pair
                                for p in v:
                                    if p is not None:
                                        standardized_vertices.append((float(p[0]), page_height - float(p[1])))  # Invert y-coordinate
                            else:
                                # It's a tuple or list of coordinates
                                standardized_vertices.append((float(v[0]), page_height - float(v[1])))  # Invert y-coordinate
                    except (TypeError, IndexError, ValueError, AttributeError) as e:
                        logger.warning(f"Warning: Could not standardize vertex: {v}, error: {e}")
                        continue

                # Check if color matches the filter
                if color_filter:
                    # Skip shapes that don't match the color filter
                    color_match = False

                    # Parse comma-separated list of colors
                    colors_to_match = [c.strip().lower() for c in color_filter.split(',')]

                    # Check if we have RGB color information for stroke
                    if stroke_color and len(stroke_color) == 3:
                        r, g, b = stroke_color

                        # Check for each color in the filter list
                        for color in colors_to_match:
                            # Check for red (high red, low green and blue)
                            if color == 'red' and r > 0.6 and g < 0.4 and b < 0.4:
                                color_match = True
                                logger.info(f"Matched RED stroke: {stroke_color}")
                                break

                            # Check for blue (high blue, low red and green)
                            elif color == 'blue' and b > 0.6 and r < 0.4 and g < 0.4:
                                color_match = True
                                logger.info(f"Matched BLUE stroke: {stroke_color}")
                                break

                    # If no match yet and this shape has a fill, use stroke color for fill check
                    # PyMuPDF doesn't consistently provide fill_color, so we check fill property
                    # and use stroke_color as an approximation
                    if not color_match and fill and stroke_color and len(stroke_color) == 3:
                        r, g, b = stroke_color

                        logger.info(f"Checking filled shape with color: {stroke_color}, type: {path_type}")

                        # Check for each color in the filter list
                        for color in colors_to_match:
                            # Check for red (high red, low green and blue)
                            if color == 'red' and r > 0.6 and g < 0.4 and b < 0.4:
                                color_match = True
                                logger.info(f"Matched RED fill: {stroke_color}")
                                break

                            # Check for blue (high blue, low red and green)
                            elif color == 'blue' and b > 0.6 and r < 0.4 and g < 0.4:
                                color_match = True
                                logger.info(f"Matched BLUE fill: {stroke_color}")
                                break

                    # Skip this shape if it doesn't match the color filter
                    if not color_match:
                        continue

                shape = {
                    'page': page_idx,
                    'id': i,
                    'type': path_type,
                    'vertices': standardized_vertices,
                    'bbox': bbox,
                    'stroke_color': stroke_color,
                    'fill_color': fill_color,
                    'stroke_width': stroke_width,
                    'fill': fill,
                    'stroke': stroke,
                    'closed': is_closed,
                    'has_curves': has_curves
                }

                shapes.append(shape)

                # Add to visualization if enabled
                if save_visualizations:
                    if path_type == "rectangle":
                        x0, y0, x1, y1 = bbox
                        color_to_use = stroke_color if stroke_color else ('red' if stroke else 'blue')
                        rect = Rectangle((x0, y0), x1-x0, y1-y0,
                                         fill=False,
                                         edgecolor=color_to_use,
                                         linewidth=1)
                        ax.add_patch(rect)
                    elif path_type == "circle":
                        # Extract coordinates again to ensure we're using the right values
                        x_coords = []
                        y_coords = []
                        for v in vertices:
                            try:
                                if hasattr(v, 'x') and hasattr(v, 'y'):
                                    # It's a Point object
                                    x_coords.append(float(v.x))
                                    y_coords.append(page_height - float(v.y))  # Invert y-coordinate
                                elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                    # It's a tuple or list
                                    x_coords.append(float(v[0]))
                                    y_coords.append(page_height - float(v[1]))  # Invert y-coordinate
                                else:
                                    logger.warning(f"Warning: Unrecognized vertex format: {v}")
                            except (TypeError, IndexError, ValueError, AttributeError) as e:
                                logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                continue

                        center_x = sum(x_coords) / len(x_coords)
                        center_y = sum(y_coords) / len(y_coords)
                        # Calculate distances from center to each vertex
                        distances = [math.sqrt((x - center_x)**2 + (y - center_y)**2)
                                    for x, y in zip(x_coords, y_coords)]
                        radius = sum(distances) / len(distances)

                        color_to_use = stroke_color if stroke_color else 'green'
                        circle = Circle((center_x, center_y), radius,
                                        fill=False,
                                        edgecolor=color_to_use,
                                        linewidth=1)
                        ax.add_patch(circle)
                    elif path_type == "line" and len(vertices) >= 2:
                        # Extract line endpoints
                        points = []
                        for v in vertices[:2]:  # Just use the first two vertices
                            try:
                                if hasattr(v, 'x') and hasattr(v, 'y'):
                                    # It's a Point object
                                    points.append((float(v.x), page_height - float(v.y)))  # Invert y-coordinate
                                elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                    # It's a tuple or list
                                    points.append((float(v[0]), page_height - float(v[1])))  # Invert y-coordinate
                            except (TypeError, IndexError, ValueError, AttributeError) as e:
                                logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                continue

                        if len(points) == 2:
                            color_to_use = stroke_color if stroke_color else 'blue'
                            ax.plot([points[0][0], points[1][0]],
                                    [points[0][1], points[1][1]],
                                    color=color_to_use,
                                    linewidth=1)
                    elif path_type == "polygon" and len(vertices) > 2:
                        # Extract coordinates for polygon vertices
                        x_points = []
                        y_points = []
                        for v in vertices:
                            try:
                                if hasattr(v, 'x') and hasattr(v, 'y'):
                                    # It's a Point object
                                    x_points.append(float(v.x))
                                    y_points.append(page_height - float(v.y))  # Invert y-coordinate
                                elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                    # It's a tuple or list
                                    x_points.append(float(v[0]))
                                    y_points.append(page_height - float(v[1]))  # Invert y-coordinate
                            except (TypeError, IndexError, ValueError, AttributeError) as e:
                                logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                continue

                        # Draw the polygon as a line connecting all points
                        if x_points and y_points:
                            try:
                                # Close the polygon if needed
                                if is_closed and len(x_points) > 2:
                                    x_points.append(x_points[0])
                                    y_points.append(y_points[0])

                                color_to_use = stroke_color if stroke_color else 'purple'
                                ax.plot(x_points, y_points, color=color_to_use, linewidth=1)
                            except Exception as e:
                                logger.warning(f"Warning: Could not plot polygon: {e}")
                    elif path_type == "curve":
                        # For curves, just plot the control points
                        x_coords = []
                        y_coords = []
                        for v in vertices:
                            try:
                                if hasattr(v, 'x') and hasattr(v, 'y'):
                                    # It's a Point object
                                    x_coords.append(float(v.x))
                                    y_coords.append(page_height - float(v.y))  # Invert y-coordinate
                                elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                    # It's a tuple or list
                                    x_coords.append(float(v[0]))
                                    y_coords.append(page_height - float(v[1]))  # Invert y-coordinate
                                else:
                                    logger.warning(f"Warning: Unrecognized vertex format: {v}")
                            except (TypeError, IndexError, ValueError, AttributeError) as e:
                                logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                continue

                        color_to_use = stroke_color if stroke_color else 'orange'
                        ax.plot(x_coords, y_coords, color=color_to_use, marker='.', linestyle='', markersize=3)


            if not save_visualizations:
                page_shapes = [s for s in shapes if s['page'] == page_idx]
                # This page has shapes
                if page_shapes:
                    pages_with_shapes.append(page_idx + 1)  # Convert to 1-indexed
                    pdf_pages_with_shapes.append(page_idx)
                else:
                    pages_without_shapes.append(page_idx + 1)  # Convert to 1-indexed

            # Save visualization if enabled
            if save_visualizations:
                plt.title(f"Shapes on Page {page_idx+1}")
                plt.axis('off')

                # Determine if page has shapes after filtering
                page_shapes = [s for s in shapes if s['page'] == page_idx]
                if page_shapes:
                    # This page has shapes
                    pages_with_shapes.append(page_idx + 1)  # Convert to 1-indexed
                    viz_filename = os.path.join(with_shapes_dir, f"{pdf_basename}_page{page_idx+1}_shapes.png")
                    pdf_pages_with_shapes.append(page_idx)
                else:
                    # This page has no shapes
                    pages_without_shapes.append(page_idx + 1)  # Convert to 1-indexed
                    viz_filename = os.path.join(no_shapes_dir, f"{pdf_basename}_page{page_idx+1}_no_shapes.png")

                # Save as PNG
                plt.savefig(viz_filename, dpi=300, bbox_inches='tight')
                plt.close(fig)
                logger.info(f"Saved visualization to: {viz_filename}")

                # Save as PDF with original colors
                if save_color_pdf:
                    # Create a new figure for PDF output with colored shapes
                    fig_pdf, ax_pdf = plt.subplots(figsize=(8.5, 11))

                    # Add page image as background if available
                    try:
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
                        # Use origin='upper' for top-left origin orientation
                        ax_pdf.imshow(img, extent=[0, page.rect.width, page.rect.height, 0],
                                     alpha=0.3, origin='upper')
                    except Exception as e:
                        logger.warning(f"Could not add page background: {e}")

                    # Set the limits to match page dimensions
                    ax_pdf.set_xlim(0, page.rect.width)
                    ax_pdf.set_ylim(page.rect.height, 0)  # Inverted y-axis (origin at top-left)

                    # Draw shapes with their original colors
                    for shape in [s for s in shapes if s['page'] == page_idx]:
                        shape_type = shape['type']
                        bbox = shape['bbox']
                        vertices = shape['vertices']
                        is_closed = shape['closed']

                        # Use original color if available, otherwise use default colors
                        rgb_color = shape['stroke_color']
                        if rgb_color and all(isinstance(c, (int, float)) for c in rgb_color):
                            try:
                                # Convert RGB tuple to matplotlib color format
                                r = min(max(int(rgb_color[0]*255), 0), 255)
                                g = min(max(int(rgb_color[1]*255), 0), 255)
                                b = min(max(int(rgb_color[2]*255), 0), 255)
                                color_str = f'#{r:02x}{g:02x}{b:02x}'
                            except (IndexError, TypeError):
                                # Fallback if color conversion fails
                                color_str = "black"
                        else:
                            # Default colors by shape type
                            color_map = {
                                "rectangle": "red",
                                "circle": "green",
                                "line": "blue",
                                "polygon": "purple",
                                "curve": "orange"
                            }
                            color_str = color_map.get(shape_type, "gray")

                        # Replace white or near-white colors with more visible alternatives
                        if ensure_visible_colors:
                            if color_str in ["#ffffff", "#fff", "white"]:
                                color_str = "lightgray"
                            elif color_str.startswith("#") and len(color_str) == 7:
                                r, g, b = int(color_str[1:3], 16), int(color_str[3:5], 16), int(color_str[5:7], 16)
                                if (r > 240 and g > 240 and b > 240) or (r < 15 and g < 15 and b < 15):
                                    color_str = "lightgray"

                        # Get line width from shape or use default
                        line_width = 1
                        if shape['stroke_width'] and isinstance(shape['stroke_width'], (int, float)):
                            line_width = min(max(shape['stroke_width'], 0.5), 3)  # Limit to reasonable range

                        if shape_type == "rectangle":
                            x0, y0, x1, y1 = bbox
                            rect = Rectangle((x0, y0), x1-x0, y1-y0,
                                          fill=False,
                                          edgecolor=color_str,
                                          linewidth=line_width)
                            ax_pdf.add_patch(rect)

                        elif shape_type == "circle":
                            # Extract coordinates for center and radius calculation
                            x_coords = []
                            y_coords = []
                            for v in vertices:
                                if isinstance(v, tuple) and len(v) >= 2:
                                    x_coords.append(float(v[0]))
                                    y_coords.append(page.rect.height - float(v[1]))  # Invert y-coordinate

                            if x_coords and y_coords:
                                center_x = sum(x_coords) / len(x_coords)
                                center_y = sum(y_coords) / len(y_coords)
                                # Calculate average radius
                                distances = [math.sqrt((x - center_x)**2 + (y - center_y)**2)
                                         for x, y in zip(x_coords, y_coords)]
                                radius = sum(distances) / len(distances)

                                circle = Circle((center_x, center_y), radius,
                                             fill=False,
                                             edgecolor=color_str,
                                             linewidth=line_width)
                                ax_pdf.add_patch(circle)

                        elif shape_type == "line" and len(vertices) >= 2:
                            # Extract line endpoints
                            x_points = []
                            y_points = []
                            for v in vertices[:2]:  # Just use the first two vertices
                                if isinstance(v, tuple) and len(v) >= 2:
                                    x_points.append(float(v[0]))
                                    y_points.append(page.rect.height - float(v[1]))  # Invert y-coordinate

                            if len(x_points) == 2 and len(y_points) == 2:
                                ax_pdf.plot(x_points, y_points,
                                        color=color_str,
                                        linewidth=line_width)

                        elif shape_type in ["polygon", "curve"]:
                            # Extract polygon vertices
                            x_points = []
                            y_points = []
                            for v in vertices:
                                try:
                                    if hasattr(v, 'x') and hasattr(v, 'y'):
                                        # It's a Point object
                                        x_points.append(float(v.x))
                                        y_points.append(page.rect.height - float(v.y))  # Invert y-coordinate
                                    elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                        # It's a tuple or list
                                        x_points.append(float(v[0]))
                                        y_points.append(page.rect.height - float(v[1]))  # Invert y-coordinate
                                except (TypeError, IndexError, ValueError, AttributeError) as e:
                                    logger.warning(f"Warning: Could not extract coordinates from vertex: {v}, error: {e}")
                                    continue

                            # Draw the polygon as a line connecting all points
                            if x_points and y_points:
                                try:
                                    # Close the polygon if needed
                                    if is_closed and len(x_points) > 2:
                                        x_points.append(x_points[0])
                                        y_points.append(y_points[0])

                                    ax_pdf.plot(x_points, y_points,
                                               color=color_str,
                                               linewidth=line_width,
                                               linestyle='-' if shape_type == "polygon" else '--')
                                except Exception as e:
                                    logger.warning(f"Warning: Could not plot polygon: {e}")

                    # Add page number and title
                    ax_pdf.set_title(f"Shapes on Page {page_idx+1} (with original colors)")
                    ax_pdf.set_aspect('equal')
                    ax_pdf.axis('off')
                    plt.tight_layout()

                    # Save as PDF in the appropriate directory
                    if page_shapes:
                        pdf_filename = os.path.join(with_shapes_dir, f"{pdf_basename}_page{page_idx+1}_shapes_color.pdf")
                    else:
                        pdf_filename = os.path.join(no_shapes_dir, f"{pdf_basename}_page{page_idx+1}_no_shapes_color.pdf")

                    plt.savefig(pdf_filename, format='pdf', bbox_inches='tight')
                    plt.close(fig_pdf)
                    logger.info(f"Saved color PDF to: {pdf_filename}")

        # Close the document
        doc.close()

    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

    # Combine PDF pages with shapes into a single PDF if there are any
    if pdf_pages_with_shapes and save_color_pdf and combine_pdfs:
        combined_pdf_path = os.path.join(output_dir, f"{pdf_basename}_combined_pages_with_shapes.pdf")

        try:
            # Create a new PDF writer
            pdf_writer = PdfWriter()

            # Process pages in smaller batches to reduce memory usage
            batch_size = 5
            for i in range(0, len(pdf_pages_with_shapes), batch_size):
                # Process a batch of pages
                batch_pages = pdf_pages_with_shapes[i:i+batch_size]

                try:
                    # Open the original PDF for this batch
                    pdf_reader = PdfReader(pdf_file)

                    # Add pages with shapes to the new document
                    for page_idx in batch_pages:
                        # Add the page from the original document
                        page = pdf_reader.pages[page_idx]
                        pdf_writer.add_page(page)

                        # Create a page number annotation
                        # PyPDF doesn't support drawing directly, so we'll use a simple text annotation
                        original_page_num = page_idx + 1  # Convert to 1-indexed

                        # Create a temporary PDF with the page number using PyMuPDF
                        temp_doc = fitz.open()
                        temp_page = temp_doc.new_page(width=page.mediabox[2], height=page.mediabox[3])

                        # Add visible page number at the top left of the page with a background rectangle
                        rect = fitz.Rect(0, 0, 100, 30)
                        temp_page.draw_rect(rect, color=(0, 0, 0), fill=(1, 1, 1), width=1)
                        temp_page.insert_textbox(rect, f"Page {original_page_num}", fontname="helv",
                                            fontsize=16, color=(0, 0, 1), align=1)  # Blue text, centered

                        # Convert the temporary page to bytes
                        temp_bytes = io.BytesIO()
                        temp_doc.save(temp_bytes)
                        temp_doc.close()

                        # Create a new PDF reader from the bytes
                        temp_bytes.seek(0)
                        temp_reader = PdfReader(temp_bytes)

                        # Merge the annotation page with the original page
                        page.merge_page(temp_reader.pages[0])

                    # Help garbage collection
                    pdf_reader = None

                    # Save intermediate progress to avoid memory buildup
                    with open(combined_pdf_path, "wb") as f:
                        pdf_writer.write(f)

                    logger.info(f"Saved progress: {min(i+batch_size, len(pdf_pages_with_shapes))}/{len(pdf_pages_with_shapes)} pages with shapes")

                    # Force garbage collection after each batch
                    import gc
                    gc.collect()

                except Exception as e:
                    logger.error(f"Error processing batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch instead of failing completely
                    continue

            # Final save is already done in the loop
            logger.info(f"Saved combined PDF with shapes to {combined_pdf_path}")

        except Exception as e:
            logger.error(f"Error creating combined PDF: {str(e)}")

    # Combine PDF pages without shapes into a single PDF if there are any
    if pages_without_shapes and save_color_pdf and combine_pdfs:
        # Convert 1-indexed page numbers back to 0-indexed for PyPDF
        pdf_pages_without_shapes = [p-1 for p in pages_without_shapes]

        combined_pdf_path = os.path.join(output_dir, f"{pdf_basename}_combined_pages_without_shapes.pdf")

        try:
            # Create a new PDF writer
            pdf_writer = PdfWriter()

            # Process pages in smaller batches to reduce memory usage
            batch_size = 5
            for i in range(0, len(pdf_pages_without_shapes), batch_size):
                # Process a batch of pages
                batch_pages = pdf_pages_without_shapes[i:i+batch_size]

                try:
                    # Open the original PDF for this batch
                    pdf_reader = PdfReader(pdf_file)

                    # Add pages without shapes to the new document
                    for page_idx in batch_pages:
                        # Add the page from the original document
                        page = pdf_reader.pages[page_idx]
                        pdf_writer.add_page(page)

                        # Create a page number annotation
                        original_page_num = page_idx + 1  # Convert to 1-indexed

                        # Create a temporary PDF with the page number using PyMuPDF
                        temp_doc = fitz.open()
                        temp_page = temp_doc.new_page(width=page.mediabox[2], height=page.mediabox[3])

                        # Add visible page number at the top left of the page with a background rectangle
                        rect = fitz.Rect(0, 0, 100, 30)
                        temp_page.draw_rect(rect, color=(0, 0, 0), fill=(1, 1, 1), width=1)
                        temp_page.insert_textbox(rect, f"Page {original_page_num}", fontname="helv",
                                            fontsize=16, color=(1, 0, 0), align=1)  # Red text, centered

                        # Convert the temporary page to bytes
                        temp_bytes = io.BytesIO()
                        temp_doc.save(temp_bytes)
                        temp_doc.close()

                        # Create a new PDF reader from the bytes
                        temp_bytes.seek(0)
                        temp_reader = PdfReader(temp_bytes)

                        # Merge the annotation page with the original page
                        page.merge_page(temp_reader.pages[0])

                    # Help garbage collection
                    pdf_reader = None

                    # Save intermediate progress to avoid memory buildup
                    with open(combined_pdf_path, "wb") as f:
                        pdf_writer.write(f)

                    logger.info(f"Saved progress: {min(i+batch_size, len(pdf_pages_without_shapes))}/{len(pdf_pages_without_shapes)} pages without shapes")

                    # Force garbage collection after each batch
                    import gc
                    gc.collect()

                except Exception as e:
                    logger.error(f"Error processing batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch instead of failing completely
                    continue

            # Final save is already done in the loop
            logger.info(f"Saved combined PDF without shapes to {combined_pdf_path}")

        except Exception as e:
            logger.error(f"Error creating combined PDF: {str(e)}")

    # Create a DataFrame from the shapes list
    if shapes:
        df = pd.DataFrame(shapes)

        # Save to CSV
        # csv_file = os.path.join(output_dir, f"{pdf_basename}_shapes.csv")
        # df.to_csv(csv_file, index=False)
        # logger.info(f"Saved shapes data to: {csv_file}")

        # Save to Excel
        excel_file = os.path.join(output_dir, f"{pdf_basename}_shapes.xlsx")
        df.to_excel(excel_file, index=False)
        logger.info(f"Saved shapes data to: {excel_file}")

        # Save page classification if requested
        if save_page_classification:
            logger.info("Saving page classification to Excel files")

            # Create DataFrames for pages with and without shapes
            if pages_with_shapes:
                df_with_shapes = pd.DataFrame({
                    'PDF': pdf_basename,
                    'Page': pages_with_shapes,
                    'Has_Shapes': 'Yes'
                })

                # Save to Excel
                with_shapes_excel = os.path.join(output_dir, f"{pdf_basename}_pages_with_shapes.xlsx")
                df_with_shapes.to_excel(with_shapes_excel, index=False)
                logger.info(f"Saved pages with shapes to {with_shapes_excel}")

            if pages_without_shapes:
                df_without_shapes = pd.DataFrame({
                    'PDF': pdf_basename,
                    'Page': pages_without_shapes,
                    'Has_Shapes': 'No'
                })

                # Save to Excel
                without_shapes_excel = os.path.join(output_dir, f"{pdf_basename}_pages_without_shapes.xlsx")
                df_without_shapes.to_excel(without_shapes_excel, index=False)
                logger.info(f"Saved pages without shapes to {without_shapes_excel}")

            # Save combined classification
            all_pages = []
            for page in pages_with_shapes:
                all_pages.append({'PDF': pdf_basename, 'Page': page, 'Has_Shapes': 'Yes'})
            for page in pages_without_shapes:
                all_pages.append({'PDF': pdf_basename, 'Page': page, 'Has_Shapes': 'No'})

            if all_pages:
                df_all_pages = pd.DataFrame(all_pages)
                df_all_pages = df_all_pages.sort_values('Page')

                # Save to Excel
                all_pages_excel = os.path.join(output_dir, f"{pdf_basename}_all_pages_classification.xlsx")
                df_all_pages.to_excel(all_pages_excel, index=False)
                logger.info(f"Saved combined page classification to {all_pages_excel}")

        return df
    else:
        logger.info("No shapes found in the PDF")

        # Even if no shapes found, save page classification if requested
        if save_page_classification and pages_without_shapes:
            logger.info("Saving page classification to Excel files (no shapes found)")

            df_without_shapes = pd.DataFrame({
                'PDF': pdf_basename,
                'Page': pages_without_shapes,
                'Has_Shapes': 'No'
            })

            # Save to Excel
            without_shapes_excel = os.path.join(output_dir, f"{pdf_basename}_pages_without_shapes.xlsx")
            df_without_shapes.to_excel(without_shapes_excel, index=False)
            logger.info(f"Saved pages without shapes to {without_shapes_excel}")

            # Save combined classification (all pages without shapes)
            all_pages_excel = os.path.join(output_dir, f"{pdf_basename}_all_pages_classification.xlsx")
            df_without_shapes.to_excel(all_pages_excel, index=False)
            logger.info(f"Saved combined page classification to {all_pages_excel}")

        return pd.DataFrame()


def plugin_analyze_shape_distribution(pdf_file: str=r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf",
                                     save_results: bool=True,
                                     output_dir: str="debug/shapes"):
    """
    Analyzes the distribution of shapes in a PDF file.

    This plugin extracts shapes and generates statistics and visualizations
    about their distribution across pages.

    Args:
        pdf_file (str): Path to the PDF file to analyze
        save_results (bool): Whether to save results and visualizations
        output_dir (str): Directory to save output files

    Returns:
        dict: Dictionary containing analysis results
    """
    import matplotlib.pyplot as plt
    import os

    # Extract shapes
    shapes_df = plugin_extract_shapes(pdf_file, save_visualizations=save_results, output_dir=output_dir)

    if shapes_df is None or shapes_df.empty:
        logger.info("No shapes found for analysis.")
        return None

    # Create output directory if needed
    if save_results and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Get PDF basename for output files
    pdf_basename = os.path.splitext(os.path.basename(pdf_file))[0]

    # Analyze shape distribution by type
    type_counts = shapes_df['type'].value_counts()

    # Analyze shape distribution by page
    page_counts = shapes_df.groupby('page').size()

    # Analyze shape types by page
    type_by_page = shapes_df.groupby(['page', 'type']).size().unstack(fill_value=0)

    # Create visualizations
    if save_results:
        # Shape types pie chart
        plt.figure(figsize=(10, 6))
        type_counts.plot(kind='pie', autopct='%1.1f%%')
        plt.title('Distribution of Shape Types')
        plt.ylabel('')
        pie_filename = os.path.join(output_dir, f"{pdf_basename}_shape_types_pie.png")
        plt.savefig(pie_filename, dpi=150, bbox_inches='tight')
        plt.close()

        # Shapes per page bar chart
        plt.figure(figsize=(12, 6))
        page_counts.plot(kind='bar')
        plt.title('Number of Shapes per Page')
        plt.xlabel('Page Number')
        plt.ylabel('Number of Shapes')
        plt.xticks(rotation=0)
        bar_filename = os.path.join(output_dir, f"{pdf_basename}_shapes_per_page.png")
        plt.savefig(bar_filename, dpi=150, bbox_inches='tight')
        plt.close()

        # Shape types by page stacked bar chart
        if not type_by_page.empty:
            plt.figure(figsize=(14, 8))
            type_by_page.plot(kind='bar', stacked=True)
            plt.title('Shape Types by Page')
            plt.xlabel('Page Number')
            plt.ylabel('Number of Shapes')
            plt.xticks(rotation=0)
            plt.legend(title='Shape Type')
            stacked_filename = os.path.join(output_dir, f"{pdf_basename}_shape_types_by_page.png")
            plt.savefig(stacked_filename, dpi=150, bbox_inches='tight')
            plt.close()

        # Save summary to Excel
        summary_df = pd.DataFrame({
            'Metric': ['Total Shapes', 'Pages with Shapes', 'Most Common Shape'],
            'Value': [len(shapes_df), len(page_counts), type_counts.index[0]]
        })

        summary_filename = os.path.join(output_dir, f"{pdf_basename}_shape_analysis_summary.xlsx")

        # Create Excel writer
        with pd.ExcelWriter(summary_filename) as writer:
            # Write summary sheet
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # Write shape types sheet
            type_counts.reset_index().rename(columns={'index': 'Shape Type', 'type': 'Count'}).to_excel(
                writer, sheet_name='Shape Types', index=False)

            # Write shapes per page sheet
            page_counts.reset_index().rename(columns={'index': 'Page', 0: 'Count'}).to_excel(
                writer, sheet_name='Shapes Per Page', index=False)

            # Write shape types by page sheet
            if not type_by_page.empty:
                type_by_page.reset_index().to_excel(writer, sheet_name='Types By Page', index=False)

        logger.info(f"Saved shape analysis to: {summary_filename}")

    # Return analysis results
    return {
        'shape_counts': type_counts.to_dict(),
        'page_counts': page_counts.to_dict(),
        'type_by_page': type_by_page.to_dict() if not type_by_page.empty else {},
        'total_shapes': len(shapes_df),
        'pages_with_shapes': len(page_counts),
        'most_common_shape': type_counts.index[0] if not type_counts.empty else None
    }


if __name__ == "__main__":
    import sys

    # Configuration variables - modify these as needed
    pdf_file = r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf"  # Path to your PDF file
    page_numbers = "1,2,3"  # List of page numbers to process (e.g., "1-5,10,15-20"), None for all pages
    output_dir = "debug/shapes"  # Directory to save output files
    save_visualizations = False  # Save visualizations
    save_color_pdf = False  # Save color PDF
    extended_mode = True  # Whether to use extended mode for drawing extraction
    color_filter = "blue,red"  # Filter shapes by color (comma-separated list)
    run_analysis = True  # Whether to run shape analysis after extraction
    combine_pdfs = False  # Whether to combine PDF pages with/without shapes
    save_page_classification = True  # Whether to save page classification to Excel files

    # Set up logging
    logger = setup_logging(output_dir)
    logger.info(f"Starting shape extraction for {pdf_file}")

    try:
        # Run the shape extraction
        result = plugin_extract_shapes(
            pdf_file=pdf_file,
            page_numbers=page_numbers,
            save_visualizations=save_visualizations,
            output_dir=output_dir,
            save_color_pdf=save_color_pdf,
            extended_mode=extended_mode,
            color_filter=color_filter,
            combine_pdfs=combine_pdfs,
            save_page_classification=save_page_classification
        )

        if run_analysis and result is not None and not result.empty:
            # Run the shape analysis if requested
            logger.info("Running shape analysis...")
            analysis = plugin_analyze_shape_distribution(
                pdf_file=pdf_file,
                save_results=True,
                output_dir=output_dir
            )
            if analysis:
                logger.info(f"Analysis complete. Found {analysis['total_shapes']} shapes across {analysis['pages_with_shapes']} pages.")
                if analysis['most_common_shape']:
                    logger.info(f"Most common shape: {analysis['most_common_shape']}")

        logger.info("Shape extraction complete")

    except Exception as e:
        logger.error(f"Error during execution: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)