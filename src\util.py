"""
General util functions

"""
import re
# import phonenumbers
from collections import OrderedDict

def is_valid_email_string(email: str) -> bool:
    # Make a regular expression
    # for validating an Email
    regex = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b'
    if (re.fullmatch(regex, email)):
        return True  # Valid email
    return False  # Invalid email


def parse_phone_number(phone_number: str) -> dict:
    try:
        if not phone_number.startswith("+"):
            phone_number = "+" + phone_number
        # number_parsed = phonenumbers.parse(phone_number, None)
        # clean_phone = phonenumbers.format_number(number_parsed, phonenumbers.PhoneNumberFormat.E164)
        # region_code = phonenumbers.region_code_for_number(number_parsed)

        # return {
        #     "phone_number": phone_number,
        #     "clean_phone": clean_phone,
        #     "region_code": region_code,
        # }
        return {
            "phone_number": phone_number,
            # "clean_phone": phone_number,
            # "region_code": phone_number,
        }
    except Exception as e:
        return {
            "phone_number": phone_number,
        }

def get_region_code_map() -> dict:
    """"""
    mapped = {}
    return mapped
    for code in phonenumbers.supported_calling_codes():
        rc = phonenumbers.region_code_for_country_code(code)
        if rc == "001":
            continue
        mapped[rc] = code
    return mapped