"""
This is a helper script to build and package atem

Code signing requirements - dotnet runtime v6 or later

https://dotnet.microsoft.com/en-us/download"

"""
import glob
import os

from posixpath import dirname
import subprocess
from os import path
from __version__ import version
import datetime
import shutil
import sys
from inno import build_exe_installer, app_signing
from src.updater import getHashBlake2b
python_exe = sys.executable

SPEC = {
    "release": {
        'excludes': ['src.views.placeholderview'],
    },
    "debug": {

    },
    "early_access": {
        'excludes': ['src.views.placeholderview'],
    },
    "early_access_console": {
        'excludes': ['src.views.placeholderview'],
    }
}

print(python_exe)
print()
print("Build ATEM script.....")
print()
print()

_version = str(version)
is_windows = os.name == 'nt'

print("Checking Azure Environment Variables for Code Signing")

azure_vars = ["AZURE_CLIENT_ID", "AZURE_CLIENT_SECRET", "AZURE_TENANT_ID"]

azure_vars_ok = False
for av in azure_vars:
    try:
        v = os.environ[av]
        print(f"Found environment variable {av}. Ok")
    except Exception as e:
        print(f"No environment variable {av} set. Error")
        azure_vars_ok = False
else:
    azure_vars_ok = True

if not azure_vars_ok:
    print()
    print("Code signing will not work. Ensure all environment variables are set")
    print(f"Required vars {azure_vars}")
    print()
else:
    print()
    print("AZURE env vars found. Ok")
    print()

if is_windows:
    # Workaround for missing libopenblas which is stored in numpy.libs. Add this to path
    numpy_libs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.venv/Lib/site-packages/numpy.libs')
    os.environ['PATH'] = numpy_libs_dir + os.pathsep + os.environ['PATH']

modes = ["release", "debug", "early_access", "early_access_console"]

print()
print("Note: if script not fully rebuilding the exe, try removing the build folder")
print()


VALID_DOTNET = app_signing.dotnet_runtime_ok()
if not VALID_DOTNET:
    print()
    y = input(f"No valid dotnet installed, required for code signing. Continue anyway? (y/n)").lower() == "y"
    if not y:
        print("Exiting...")
        exit()
    print()


# Tries to find any exes previously built
def find_exes() -> list:
    p = path.join(dirname(__file__), "dist")
    files = glob.glob(p + '/**/*.exe', recursive=True)
    return files

def build_exe(filename: str = None):
    if not build_exe_installer.compiler_exists():
        print(f"Locating compiler: {build_exe_installer.compiler_exe}")
        print("INNO compiler could not be found. Please install and run this script again")
        print("Downloads at - https://jrsoftware.org/isdl.php#stable")
        sys.exit()

    exes = find_exes()
    print()
    for n, exe in enumerate(exes):
        print(f"({n})", exe)
    print()
    
    while True:
        y = input(f"Which exe to create installer (0-{len(exes)-1})? Default={None} x=Exit  ")
        if y == "":
            if filename is None:
                print("Must select a file")
                continue
            else:
                assert path.exists(filename)
                build_exe_installer(filename)
                break
        elif y.lower() == "x":
            break
        else:
            try:
                exe = exes[int(y)]
                assert path.exists(exe)
            except Exception as e:
                print("Invalid selection -", e)
                continue

            try:
                full_path = path.join(path.dirname(__file__), exe)
                SIGN_APP = VALID_DOTNET and input(f"Sign exe? {full_path} (y/n)").lower() == "y"
                if SIGN_APP:
                    ok = app_signing.setup_environment()
                    if ok:
                        print()
                        print(f"Signing ATEM executable {full_path}")
                        print()
                        app_signing.sign_exe(full_path)
                        print("Signed...")
                    else:
                        print()
                        print("App signing environment setup failed. Exiting...")
                        print()
                        exit()
                
                # Sign Setup EXE
                if SIGN_APP:
                    print()
                    print(f"Building and Signing SETUP installer executable")
                    signtool = app_signing.get_signtool_exe()
                    signtool: str = app_signing.get_signtool_exe()
                    if not signtool:
                        raise Exception("Failed to find signtool path")
                    dlib = app_signing.get_code_signing_dlib()
                    if not dlib:
                        raise Exception("Failed to find dlib path")
                    metadata_path = app_signing.METADATA_JSON_PATH
                    setup_exe: str = build_exe_installer.build_installer(full_path, signtool, dlib=dlib, metadata_path=metadata_path)
                    print()
                    # app_signing.sign_exe(setup_exe)
                    print("Setup EXE built and signed...")
                else:
                    print()
                    print(f"Building SETUP installer executable")
                    setup_exe = build_exe_installer.build_installer(full_path)
                    print("Setup EXE built")

                hashRecheck = build_exe_installer.getHashBlake2b(setup_exe)
                print(f"Check Setup exe hash: {hashRecheck}")

            except Exception as e:
                print("Build failed -", e)
                break
            
    sys.exit()

# Tries to find any installer exes previously built
def find_installer_exes() -> list:
    p = path.join(dirname(__file__), "inno", "output")
    files = glob.glob(p + '/**/*.exe', recursive=True)
    return files

def read_blake2():
    print()
    print("Choose SETUP EXE to get Blake2 checksum (Listing detected .exe files from /inno/output folder...)")
    while True:
        exes = find_installer_exes()
        for n, exe in enumerate(exes):
            print(f"{n}) {exe}")
        v = input(f"Choose file (0-{len(exes)}), exit=x\n")
        if v.lower() == "x":
            print("Exiting...")
            break

        try:
            hash = getHashBlake2b(exes[int(v)])
            print()
            print(f"Blake2 Hash=={hash}")
            print()
            print("Exiting...")
            break
        except Exception as e:
            print(f"Failed to read checksum for option {v}", e)

print("""
Modes

    1) release (no console)
    2) debug (console)
    3) early_access (no console, restricted/removed features)
    4) early_access (console, restricted/removed features)
    5) Build Inno EXE Installer (choose from existing exe)
    6) Get SETUP EXE Checksum (BLAKE2)
"""
)
print()
mode = 0
while True:
    m = input("Select a mode? 1=release (default), 2=debug, 3=early_access, 4=early_access (console),\n\t\t 5=build_installer, 6=read checksum\n")
    if m == "" or m is None:
        break
    if m in ["1", "2", "3", "4", "5", "6", "7"]:
        mode = int(m) - 1
        break

if mode == 4:
    build_exe()
    sys.exit()

if mode == 5:
    read_blake2()
    sys.exit()

print()

version = input(f"Version? default={version}\n")
if not version:
    print(f"Using default version {version}")
    version = _version
    [float(n) for n in version.split(".")]


exe_name = f"ATEM-{version}-{modes[mode]}"
def get_time_string():
    return datetime.datetime.now().strftime("%Y%m%d%H%M%S")


add_timestamp = input(f"Attach timestamp to output filename e.g. {exe_name}-{get_time_string()} (default=y)").lower()
if len(add_timestamp) == 0 or add_timestamp == "y":
    exe_name += "-" + get_time_string()

print()
print("Output filename:", exe_name)
print()

specpath = "atem_temp.spec"

excludes = [e for e in SPEC.get(modes[mode], {}).get('excludes', [])]
if not excludes:
    excludes = None
print("Excluding modules...")
print(excludes)
print()


# 
version2_spec = f"""# This file is autogenerated during build
import datetime
def get_time_string():
    return datetime.datetime.now().strftime('%Y%m%d%H%M%S')

title = 'ATEM'
version = '{version}'
mode = '{modes[mode]}'
build_date = get_time_string()

"""

print("Writing __version2__.py file")
version2_path = "__version2__.py"
with open(version2_path, "w") as file:
    file.write(version2_spec)

print()

features = {
    "TABLE_CHECKBOX_ENABLED": False,
    "CHECK_FOR_UPDATES": False,
    "GET_WELD_COUNT_ENABLED": False,
}

print("Features included...")
for k, default_value in features.items():
    v = input(f"{k}? (y/n) Default={default_value}  ")
    if v.lower() == "y":
        v = True
    elif v.lower() == "n":
        v = False
    else:
        v = default_value
    features[k] = v
    print("SET", k, "=", v)

# Output feature settings to features2
features2_spec = f"""# This file is autogenerated during build
"""
for k, value in features.items():
    features2_spec += f"\n{k} = {value}"

TABLE_CHECKBOX_ENABLED = {features["TABLE_CHECKBOX_ENABLED"]}
CHECK_FOR_UPDATES = {features["CHECK_FOR_UPDATES"]}


print("Writing __features2__.py file")
features2_path = "__features2__.py"
with open(features2_path, "w") as file:
    file.write(features2_spec)

print()

template_spec = f"""# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_submodules

all_libraries = [
    "tiktoken",
]

hidden_imports = [
    "tiktoken_ext.openai_public",
    "tiktoken_ext",
]
for l in all_libraries:
    hidden_imports += collect_submodules(l)


add_files = [

    ('src/data/themes/stylesheet.qss', 'src/data/themes'),
    ('src/data/themes', 'src/data/themes'),
    ('src/data/useragreement.txt', "src/data"),
    ('src/data/updater/latest-release.txt', "src/data/updater"),
    ('src/data/fieldmap.json', 'src/data'),
    ('src/data/tables/ef_template.xlsx', 'src/data/tables'),

    ('src/resources/fonts', 'src/resources/fonts'),
    ('src/resources/*.svg', 'src/resources'),
    ('src/resources/*.png', 'src/resources'),
    ('src/resources/*.jpg', 'src/resources'),
    ('src/resources/*.gif', 'src/resources'),
    ('src/resources/*.gif', 'src/resources'),

    ('src/atom/payload_options.json', 'src/atom'),

]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=add_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={excludes},
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='{exe_name}',
    debug={True if mode in [1, 3] else False},
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={True if mode in [1, 3] else False},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)

"""

print("--- Final Spec File ---")
print()
print(template_spec)
print()

with open(specpath, "w") as specfile:
    specfile.write(template_spec)

assert os.path.exists(specpath)

if is_windows:
    cmd = [python_exe, "-m", "PyInstaller", f"{specpath}"]
else:
    cmd = ["python3", "-m", "PyInstaller", f"{specpath}"]

print("CMD")
print("".join(cmd))
# Build installer
subprocess.check_output(cmd)

# Cleanup
print()
print("Cleaning up")

print()
print()
print("Finished")

outdir = path.join(path.dirname(path.abspath(__file__)), "build")
outdir = path.join(path.dirname(path.abspath(__file__)), "dist")

print()

subdir = ""
if is_windows:
    subdir = "win"
    exe_name += ".exe"
else:
    subdir = "linux"

os.makedirs(f"dist/{version}/{subdir}", exist_ok=True)

final_path = path.join(outdir, version, subdir, exe_name)
# Move to final path
shutil.move(path.join(outdir, exe_name), final_path)


print("Base output dir:", path.join(outdir, version))
print("Output dir:", path.join(outdir, version, subdir))
print("Final exe path:", final_path)
print()

# Sign ATEM exe
SIGN_APP = VALID_DOTNET and input(f"Sign exe? {final_path} (y/n)").lower() == "y"
if SIGN_APP:
    if not azure_vars_ok:
        raise Exception("Cannot sign exe if AZURE env vars are not set")
    ok = app_signing.setup_environment()
    if ok:
        print()
        print(f"Signing ATEM executable {final_path}")
        print()
        app_signing.sign_exe(final_path)
        print("Signed...")
    else:
        print()
        print("App signing environment setup failed. Exiting...")
        print()
        exit()

y = input(f"Build setup installer? {final_path} (y/n)")
if y.lower() == "y":
    # Sign Setup EXE
    if SIGN_APP:
        if not azure_vars_ok:
            raise Exception("Cannot sign setup exe if AZURE env vars are not set")
        print(f"Building and signgin SETUP installer executable...")
        signtool: str = app_signing.get_signtool_exe()
        if not signtool:
            raise Exception("Failed to find signtool path")
        dlib = app_signing.get_code_signing_dlib()
        if not dlib:
            raise Exception("Failed to find dlib path")
        metadata_path = app_signing.METADATA_JSON_PATH
        setup_exe: str = build_exe_installer.build_installer(final_path, signtool, dlib=dlib, metadata_path=metadata_path)
        print("Signed...")
    else:
        print(f"Building SETUP installer executable... (no signing)")
        setup_exe: str = build_exe_installer.build_installer(final_path)    
        app_signing.sign_exe(setup_exe)

    print()        
    print()

    hashRecheck = build_exe_installer.getHashBlake2b(setup_exe)
    print(f"Check Setup exe hash: {hashRecheck}")

print("END")