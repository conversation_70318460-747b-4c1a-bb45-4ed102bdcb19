#!/usr/bin/env python3
"""
Test script to verify the export plugin works with the new file picker functionality.
This script tests the plugin_export_postgres function with the general_data_workbook parameter.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_export_plugin_signature():
    """Test that the export plugin function has the expected signature including general_data_workbook parameter."""
    try:
        from src.plugins.pg.plugin_export_project_data import plugin_export_postgres
        import inspect
        
        # Get the function signature
        sig = inspect.signature(plugin_export_postgres)
        
        print("=== Export Plugin Function Signature ===")
        print(f"Function: plugin_export_postgres{sig}")
        print("\nParameters:")
        
        for param_name, param in sig.parameters.items():
            default_val = param.default if param.default != inspect.Parameter.empty else "No default"
            print(f"  {param_name}: {param.annotation if param.annotation != inspect.Parameter.empty else 'Any'} = {default_val}")
        
        # Check if general_data_workbook parameter exists
        if 'general_data_workbook' in sig.parameters:
            print("\n✅ SUCCESS: general_data_workbook parameter found in function signature")
            param = sig.parameters['general_data_workbook']
            print(f"   Type: {param.annotation if param.annotation != inspect.Parameter.empty else 'str'}")
            print(f"   Default: {param.default if param.default != inspect.Parameter.empty else 'No default'}")
        else:
            print("\n❌ ERROR: general_data_workbook parameter NOT found in function signature")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ ERROR: Could not import plugin: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        return False

def test_plugin_dialog_constants():
    """Test that the plugin dialog has the updated constants for file picker support."""
    try:
        from src.views.dialogs.plugindialog import RESERVED_FILEINPUT_ARGS
        
        print("\n=== Plugin Dialog File Input Arguments ===")
        print("RESERVED_FILEINPUT_ARGS:")
        for arg in RESERVED_FILEINPUT_ARGS:
            print(f"  - {arg}")
        
        if 'general_data_workbook' in RESERVED_FILEINPUT_ARGS:
            print("\n✅ SUCCESS: general_data_workbook found in RESERVED_FILEINPUT_ARGS")
        else:
            print("\n❌ ERROR: general_data_workbook NOT found in RESERVED_FILEINPUT_ARGS")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ ERROR: Could not import plugin dialog: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        return False

def test_export_plugin_dry_run():
    """Test calling the export plugin with invalid project_id to verify parameter handling."""
    try:
        from src.plugins.pg.plugin_export_project_data import plugin_export_postgres
        
        print("\n=== Export Plugin Dry Run Test ===")
        print("Testing with project_id=0 (should return error message)")
        
        # Call with project_id=0 which should return an error without doing any real work
        result = plugin_export_postgres(
            project_id=0,  # Invalid project_id should cause early return
            export_profile="debug/template_export_profile.xlsx",
            general_data_workbook="test_workbook.xlsx"  # Test the new parameter
        )
        
        print(f"Result: {result}")
        
        if "Error: project_id must be greater than 0" in result:
            print("✅ SUCCESS: Plugin correctly validates project_id and accepts general_data_workbook parameter")
        else:
            print("❌ ERROR: Unexpected result from plugin")
            return False
            
        return True
        
    except TypeError as e:
        if "unexpected keyword argument" in str(e) and "general_data_workbook" in str(e):
            print(f"❌ ERROR: Plugin does not accept general_data_workbook parameter: {e}")
            return False
        else:
            print(f"❌ ERROR: Unexpected TypeError: {e}")
            return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Export Plugin File Picker Integration")
    print("=" * 50)
    
    tests = [
        ("Plugin Function Signature", test_export_plugin_signature),
        ("Plugin Dialog Constants", test_plugin_dialog_constants),
        ("Plugin Dry Run", test_export_plugin_dry_run),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ ERROR: Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The file picker integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
