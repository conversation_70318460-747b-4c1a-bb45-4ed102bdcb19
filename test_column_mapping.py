#!/usr/bin/env python3
"""
Test script to verify column mapping for abbreviated_material and technical_standard fields
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import pandas as pd
from src.atom.pg_database.schemas.base.column_mapping import ColumnMapper

def test_column_mapping():
    """Test the column mapping for abbreviated_material and technical_standard"""
    
    # Initialize the mapper
    mapper = ColumnMapper()
    
    print("=== Testing Column Mapping for verified_material_classifications ===\n")
    
    # Test cases for display names that should map to database columns
    test_cases = [
        ("Abbrev. Mtrl", "abbreviated_material"),
        ("Technical Standard", "technical_standard"),
        ("ansme_ansi", "technical_standard"),  # Direct field name
        ("abbreviated_material", "abbreviated_material"),  # Direct field name
        ("material_description", "material_description"),  # Should work as-is
    ]
    
    print("Testing display name to PostgreSQL column mapping:")
    for display_name, expected_pg_col in test_cases:
        result = mapper.display_to_pg("verified_material_classifications", display_name)
        status = "✅ PASS" if result == expected_pg_col else "❌ FAIL"
        print(f"  {status} '{display_name}' -> '{result}' (expected: '{expected_pg_col}')")
    
    print("\n" + "="*60)
    
    # Test DataFrame conversion
    print("\nTesting DataFrame column conversion:")
    
    # Create a test DataFrame with display names
    test_data = {
        "material_description": ["Test Material 1", "Test Material 2"],
        "Abbrev. Mtrl": ["CS", "SS"],
        "Technical Standard": ["B16.9", "B16.5"],
        "material": ["Steel, Carbon", "Steel, Stainless"],
        "rfq_scope": ["Pipe", "Fittings"]
    }
    
    df = pd.DataFrame(test_data)
    print(f"Original DataFrame columns: {list(df.columns)}")
    
    # Convert using the mapper
    df_converted, unmapped = mapper.convert_dataframe_columns(
        df, "verified_material_classifications", "display", "pg"
    )
    
    print(f"Converted DataFrame columns: {list(df_converted.columns)}")
    
    if unmapped:
        print(f"Unmapped columns: {unmapped}")
    else:
        print("All columns mapped successfully!")
    
    # Check specific mappings
    expected_columns = [
        "material_description",
        "abbreviated_material", 
        "technical_standard",
        "material",
        "rfq_scope"
    ]
    
    print("\nColumn mapping verification:")
    for expected_col in expected_columns:
        if expected_col in df_converted.columns:
            print(f"  ✅ {expected_col} - Found")
        else:
            print(f"  ❌ {expected_col} - Missing")
    
    print(f"\nSample converted data:")
    print(df_converted.head())

if __name__ == "__main__":
    test_column_mapping()
