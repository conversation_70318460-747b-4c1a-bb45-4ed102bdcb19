import sys
import cv2
import fitz  # PyMuPDF

import pandas as pd
import numpy as np

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *


class FeatureDetectionDialog(QMainWindow):
    MAX_PAGES = 100  # Maximum number of pages to process

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Feature Detection Tool")
        self.setMinimumSize(1200, 800)

        # Initialize variables
        self.original_image = None
        self.processed_image = None
        self.doc = None
        self.original_cv_image = None

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Controls
        controls_layout = QHBoxLayout()

        # File selection
        file_group = QGroupBox("File Selection")
        file_layout = QHBoxLayout()

        self.file_btn = QPushButton("Open PDF/Image")
        self.file_btn.clicked.connect(self.open_file)
        file_layout.addWidget(self.file_btn)

        # Page selection for PDF
        self.page_combo = QComboBox()
        self.page_combo.currentIndexChanged.connect(self.page_changed)
        self.page_combo.setEnabled(False)
        file_layout.addWidget(self.page_combo)

        file_group.setLayout(file_layout)
        controls_layout.addWidget(file_group)

        # Detection method selection
        method_group = QGroupBox("Detection Method")
        method_layout = QHBoxLayout()
        self.detector_combo = QComboBox()
        self.detector_combo.addItems([
            "SIFT", "ORB", "FAST", "BRISK", "AKAZE"
        ])
        self.detector_combo.currentTextChanged.connect(self.update_params_visibility)
        method_layout.addWidget(self.detector_combo)
        method_group.setLayout(method_layout)
        controls_layout.addWidget(method_group)

        # Parameters
        self.params_group = QGroupBox("Parameters")
        self.params_layout = QVBoxLayout()

        # AKAZE parameters
        self.akaze_params = {}
        self.akaze_params['threshold'] = self.create_param_spinbox(
            "Threshold:", 0.0001, 0.03, 0.001, double=True, step=0.0001)
        self.akaze_params['nOctaves'] = self.create_param_spinbox(
            "Octaves:", 1, 8, 4)
        self.akaze_params['nOctaveLayers'] = self.create_param_spinbox(
            "Octave Layers:", 1, 8, 4)

        # SIFT parameters
        self.sift_params = {}
        self.sift_params['nfeatures'] = self.create_param_spinbox(
            "Max Features:", 0, 10000, 500)
        self.sift_params['n_octave_layers'] = self.create_param_spinbox(
            "Octave Layers:", 1, 10, 3)
        self.sift_params['contrast_threshold'] = self.create_param_spinbox(
            "Contrast Threshold:", 0.01, 0.5, 0.04, double=True)
        self.sift_params['edge_threshold'] = self.create_param_spinbox(
            "Edge Threshold:", 1, 50, 10)
        self.sift_params['sigma'] = self.create_param_spinbox(
            "Sigma:", 0.1, 5.0, 1.6, double=True)

        # ORB parameters
        self.orb_params = {}
        self.orb_params['nfeatures'] = self.create_param_spinbox(
            "Max Features:", 0, 10000, 500)
        self.orb_params['scaleFactor'] = self.create_param_spinbox(
            "Scale Factor:", 1.0, 2.0, 1.2, double=True)
        self.orb_params['nlevels'] = self.create_param_spinbox(
            "Levels:", 1, 20, 8)
        self.orb_params['edgeThreshold'] = self.create_param_spinbox(
            "Edge Threshold:", 1, 50, 31)

        # FAST parameters
        self.fast_params = {}
        self.fast_params['threshold'] = self.create_param_spinbox(
            "Threshold:", 1, 100, 20)
        self.fast_params['nonmaxSuppression'] = QCheckBox("Non-max Suppression")
        self.fast_params['nonmaxSuppression'].setChecked(True)

        # BRISK parameters
        self.brisk_params = {}
        self.brisk_params['thresh'] = self.create_param_spinbox(
            "BRISK Threshold:", 1, 100, 30)
        self.brisk_params['octaves'] = self.create_param_spinbox(
            "Octaves:", 1, 8, 3)
        self.brisk_params['patternScale'] = self.create_param_spinbox(
            "Pattern Scale:", 0.1, 5.0, 1.0, double=True)

        # Add all parameter groups to layout
        for params in [self.sift_params, self.orb_params, self.fast_params,
                      self.brisk_params, self.akaze_params]:
            for widget in params.values():
                self.params_layout.addWidget(widget)
                if isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                    widget.valueChanged.connect(self.process_image)
                elif isinstance(widget, QCheckBox):
                    widget.stateChanged.connect(self.process_image)

        self.params_group.setMinimumWidth(160)
        self.params_group.setLayout(self.params_layout)
        controls_layout.addWidget(self.params_group)

        # Preprocessing options
        preprocess_group = QGroupBox("Preprocessing")
        preprocess_layout = QVBoxLayout()

        self.use_preprocessing = QCheckBox("Enable Preprocessing")
        self.use_preprocessing.setChecked(True)
        self.use_preprocessing.stateChanged.connect(self.process_image)
        preprocess_layout.addWidget(self.use_preprocessing)

        self.show_keypoints = QCheckBox("Show Keypoints")
        self.show_keypoints.setChecked(True)
        self.show_keypoints.stateChanged.connect(self.process_image)
        preprocess_layout.addWidget(self.show_keypoints)

        preprocess_group.setLayout(preprocess_layout)
        controls_layout.addWidget(preprocess_group)

        # PDF Generation options
        pdf_group = QGroupBox("PDF Options")
        pdf_layout = QVBoxLayout()

        # Add page range input
        page_range_layout = QHBoxLayout()
        page_range_layout.addWidget(QLabel("Page Range:"))
        self.page_range_input = QLineEdit()
        self.page_range_input.setPlaceholderText("e.g., 1-5,7,9-12 (empty for all)")
        page_range_layout.addWidget(self.page_range_input)
        pdf_layout.addLayout(page_range_layout)

        self.generate_pdf_btn = QPushButton("Generate PDF Report")
        self.generate_pdf_btn.clicked.connect(self.generate_pdf)
        self.generate_pdf_btn.setEnabled(False)
        pdf_layout.addWidget(self.generate_pdf_btn)

        self.generate_excel_btn = QPushButton("Generate Excel Report")
        self.generate_excel_btn.clicked.connect(self.generate_excel)
        self.generate_excel_btn.setEnabled(False)
        pdf_layout.addWidget(self.generate_excel_btn)

        self.show_config_text = QCheckBox("Show Configuration")
        self.show_config_text.setChecked(True)
        self.show_config_text.stateChanged.connect(self.process_image)
        pdf_layout.addWidget(self.show_config_text)

        self.show_center_points = QCheckBox("Show Center Points")
        self.show_center_points.setChecked(True)
        self.show_center_points.stateChanged.connect(self.process_image)
        pdf_layout.addWidget(self.show_center_points)

        self.show_bounding_box = QCheckBox("Show Bounding Boxes")
        self.show_bounding_box.setChecked(True)
        self.show_bounding_box.stateChanged.connect(self.process_image)
        pdf_layout.addWidget(self.show_bounding_box)

        pdf_group.setLayout(pdf_layout)
        controls_layout.addWidget(pdf_group)

        # Save button
        self.save_btn = QPushButton("Save Image")
        self.save_btn.clicked.connect(self.save_image)
        self.save_btn.setEnabled(False)
        controls_layout.addWidget(self.save_btn)

        layout.addLayout(controls_layout)

        # Image display
        display_layout = QHBoxLayout()

        # Original image (hidden by default)
        self.original_label = QLabel()
        self.original_label.setMinimumSize(500, 600)
        self.original_label.setAlignment(Qt.AlignCenter)
        self.original_label.hide()  # Hide by default
        display_layout.addWidget(self.original_label)

        # Add toggle button for original image
        self.toggle_original_btn = QPushButton("Show Original")
        self.toggle_original_btn.setCheckable(True)
        self.toggle_original_btn.clicked.connect(self.toggle_original_image)
        controls_layout.addWidget(self.toggle_original_btn)

        # Processed image
        self.processed_label = QLabel()
        self.processed_label.setMinimumSize(500, 600)
        self.processed_label.setAlignment(Qt.AlignCenter)
        display_layout.addWidget(self.processed_label)

        layout.addLayout(display_layout)

        # Initialize parameter visibility
        self.update_params_visibility()

    def create_param_spinbox(self, label_text, min_val, max_val, default_val, double=False, step=0.01):
        if double:
            spinbox = QDoubleSpinBox()
            spinbox.setSingleStep(step)
            spinbox.setDecimals(4)  # Set to 4 decimal places for better precision
        else:
            spinbox = QSpinBox()

        spinbox.setRange(min_val, max_val)
        spinbox.setValue(default_val)
        spinbox.setPrefix(f"{label_text} ")
        return spinbox

    def update_params_visibility(self):
        detector = self.detector_combo.currentText()

        # Hide all parameters
        for params in [self.sift_params, self.orb_params, self.fast_params,
                      self.brisk_params, self.akaze_params]:
            for widget in params.values():
                widget.hide()

        # Show relevant parameters
        if detector == "SIFT":
            for widget in self.sift_params.values():
                widget.show()
        elif detector == "ORB":
            for widget in self.orb_params.values():
                widget.show()
        elif detector == "FAST":
            for widget in self.fast_params.values():
                widget.show()
        elif detector == "BRISK":
            for widget in self.brisk_params.values():
                widget.show()
        elif detector == "AKAZE":
            for widget in self.akaze_params.values():
                widget.show()

        self.process_image()

    def open_file(self):
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open File", "", "All Files (*.pdf *.png *.jpg *.jpeg *.bmp);;PDF Files (*.pdf);;Image Files (*.png *.jpg *.jpeg *.bmp)")
        if not filename:
            return

        if filename.lower().endswith('.pdf'):
            self.open_pdf(filename)
        else:
            self.open_image(filename)

        self.generate_excel_btn.setEnabled(True)  # Enable Excel button when file is loaded

    def open_pdf(self, filename):
        # filename, _ = QFileDialog.getOpenFileName(
        #     self, "Open PDF", "", "PDF Files (*.pdf)")
        if filename:
            self.doc = fitz.open(filename)
            self.page_combo.clear()
            page_count = min(self.doc.page_count, self.MAX_PAGES)
            self.page_combo.addItems([f"Page {i+1}" for i in range(page_count)])
            self.page_combo.setEnabled(True)
            self.page_changed(0)
            self.save_btn.setEnabled(True)

    def page_changed(self, index):
        if self.doc is None:
            return

        # Get page
        page = self.doc[index]

        # Convert to cv2 image
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        self.original_image = img
        self.process_image()

    def open_image(self, filename):
        self.doc = None
        self.page_combo.clear()
        self.page_combo.setEnabled(False)

        self.original_image = cv2.imread(filename)
        if self.original_image is None:
            QMessageBox.critical(self, "Error", "Failed to load image")
            return

        self.original_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
        self.save_btn.setEnabled(True)
        self.process_image()

    def preprocess_image(self, image):
        """Apply preprocessing steps to enhance feature detection"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)

        return enhanced

    def create_detector(self):
        """Create feature detector based on selected method and parameters"""
        detector_type = self.detector_combo.currentText()

        if detector_type == "SIFT":
            return cv2.SIFT_create(
                nfeatures=self.sift_params['nfeatures'].value(),
                nOctaveLayers=self.sift_params['n_octave_layers'].value(),
                contrastThreshold=self.sift_params['contrast_threshold'].value(),
                edgeThreshold=self.sift_params['edge_threshold'].value(),
                sigma=self.sift_params['sigma'].value()
            )
        elif detector_type == "ORB":
            return cv2.ORB_create(
                nfeatures=self.orb_params['nfeatures'].value(),
                scaleFactor=self.orb_params['scaleFactor'].value(),
                nlevels=self.orb_params['nlevels'].value(),
                edgeThreshold=self.orb_params['edgeThreshold'].value()
            )
        elif detector_type == "FAST":
            return cv2.FastFeatureDetector_create(
                threshold=self.fast_params['threshold'].value(),
                nonmaxSuppression=self.fast_params['nonmaxSuppression'].isChecked()
            )
        elif detector_type == "BRISK":
            return cv2.BRISK_create(
                thresh=self.brisk_params['thresh'].value(),
                octaves=self.brisk_params['octaves'].value(),
                patternScale=self.brisk_params['patternScale'].value()
            )
        elif detector_type == "AKAZE":
            return cv2.AKAZE_create(
                threshold=self.akaze_params['threshold'].value(),
                nOctaves=self.akaze_params['nOctaves'].value(),
                nOctaveLayers=self.akaze_params['nOctaveLayers'].value()
            )

        return None

    def process_image(self):
        if self.original_image is None:
            return

        # Display original image
        height, width = self.original_image.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(self.original_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)
        scaled_pixmap = pixmap.scaled(self.original_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.original_label.setPixmap(scaled_pixmap)

        # Preprocess the image if enabled
        if self.use_preprocessing.isChecked():
            processed = self.preprocess_image(self.original_image)
        else:
            processed = cv2.cvtColor(self.original_image, cv2.COLOR_RGB2GRAY)

        # Create detector and detect features
        detector = self.create_detector()
        if detector is None:
            return

        keypoints = detector.detect(processed, None)

        # Create a copy of the original image for drawing
        self.processed_image = self.original_image.copy()

        # Draw features
        if self.show_keypoints.isChecked():
            cv2.drawKeypoints(
                self.original_image,
                keypoints,
                self.processed_image,
                color=(0, 255, 0),
                flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS
            )

        # Draw center points and bounding boxes
        for kp in keypoints:
            x, y = map(int, kp.pt)
            size = int(kp.size)

            if self.show_center_points.isChecked():
                # Draw center point
                cv2.circle(self.processed_image, (x, y), 2, (255, 0, 0), -1)
                cv2.circle(self.processed_image, (x, y), 4, (0, 0, 255), 1)

            if self.show_bounding_box.isChecked():
                # Draw bounding box
                half_size = size // 2
                cv2.rectangle(self.processed_image,
                            (x - half_size, y - half_size),
                            (x + half_size, y + half_size),
                            (255, 255, 0), 1)

        # Add configuration text if enabled
        if self.show_config_text.isChecked():
            method = self.detector_combo.currentText()
            config_text = [
                f"Method: {method}",
                f"Features: {len(keypoints)}",
                f"Preprocessing: {self.use_preprocessing.isChecked()}"
            ]

            # Add method-specific parameters
            if method == "SIFT":
                params = self.sift_params
                config_text.extend([
                    f"Max Features: {params['nfeatures'].value()}",
                    f"Octave Layers: {params['n_octave_layers'].value()}",
                    f"Contrast Threshold: {params['contrast_threshold'].value():.2f}",
                    f"Edge Threshold: {params['edge_threshold'].value()}",
                    f"Sigma: {params['sigma'].value():.2f}"
                ])
            # Add similar blocks for other detectors...

            # Draw text
            y_offset = 30
            for text in config_text:
                cv2.putText(self.processed_image, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                y_offset += 20

        # Display processed image
        height, width = self.processed_image.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(self.processed_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)
        scaled_pixmap = pixmap.scaled(self.processed_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.processed_label.setPixmap(scaled_pixmap)

        # Enable PDF generation if we have processed image
        self.generate_pdf_btn.setEnabled(True)

    def save_image(self):
        if self.processed_image is None:
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Image", "", "Image Files (*.png *.jpg *.jpeg *.bmp)")
        if filename:
            cv2.imwrite(filename, cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2BGR))

    def parse_page_range(self, page_range_str, total_pages):
        """Parse page range string into list of page numbers (1-based)"""
        if not page_range_str.strip():
            return list(range(1, total_pages + 1))

        pages = []
        parts = page_range_str.split(',')

        for part in parts:
            part = part.strip()
            if '-' in part:
                start, end = map(int, part.split('-'))
                start = max(1, start)
                end = min(total_pages, end)
                pages.extend(range(start, end + 1))
            else:
                page = int(part)
                if 1 <= page <= total_pages:
                    pages.append(page)

        return sorted(set(pages))

    def process_page(self, page_idx):
        """Process a single page and return the processed image and keypoints"""
        # Get page
        page = self.doc[page_idx]

        # Convert to cv2 image
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        if pix.n == 4:  # RGBA
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        # Preprocess
        if self.use_preprocessing.isChecked():
            processed = self.preprocess_image(img)
        else:
            processed = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)

        # Detect features
        detector = self.create_detector()
        if detector is None:
            return None, None

        keypoints = detector.detect(processed, None)

        # Create output image
        output_img = img.copy()

        # Draw features
        if self.show_keypoints.isChecked():
            cv2.drawKeypoints(
                img, keypoints, output_img,
                color=(0, 255, 0),
                flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS
            )

        # Draw center points and bounding boxes
        for kp in keypoints:
            x, y = map(int, kp.pt)
            size = int(kp.size)

            if self.show_center_points.isChecked():
                cv2.circle(output_img, (x, y), 2, (255, 0, 0), -1)
                cv2.circle(output_img, (x, y), 4, (0, 0, 255), 1)

            if self.show_bounding_box.isChecked():
                half_size = size // 2
                cv2.rectangle(output_img,
                            (x - half_size, y - half_size),
                            (x + half_size, y + half_size),
                            (255, 255, 0), 1)

        # Add configuration text
        if self.show_config_text.isChecked():
            method = self.detector_combo.currentText()
            config_text = [
                f"Page {page_idx + 1}",
                f"Method: {method}",
                f"Features: {len(keypoints)}",
                f"Preprocessing: {self.use_preprocessing.isChecked()}"
            ]

            # Add method-specific parameters
            if method == "SIFT":
                params = self.sift_params
                config_text.extend([
                    f"Max Features: {params['nfeatures'].value()}",
                    f"Octave Layers: {params['n_octave_layers'].value()}",
                    f"Contrast Threshold: {params['contrast_threshold'].value():.2f}",
                    f"Edge Threshold: {params['edge_threshold'].value()}",
                    f"Sigma: {params['sigma'].value():.2f}"
                ])
            elif method == "AKAZE":
                params = self.akaze_params
                config_text.extend([
                    f"Threshold: {params['threshold'].value():.4f}",
                    f"Octaves: {params['nOctaves'].value()}",
                    f"Octave Layers: {params['nOctaveLayers'].value()}"
                ])
            # Add other detector parameters as needed...

            y_offset = 30
            for text in config_text:
                cv2.putText(output_img, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
                y_offset += 20

        return output_img, keypoints

    def generate_pdf(self):
        if self.doc is None:
            QMessageBox.warning(self, "Warning", "No PDF document loaded")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save PDF Report", "", "PDF Files (*.pdf)")
        if not filename:
            return

        try:
            # Parse page range
            page_range = self.parse_page_range(
                self.page_range_input.text(),
                self.doc.page_count
            )

            # Create progress dialog
            progress = QProgressDialog("Generating PDF report...", "Cancel", 0, len(page_range), self)
            progress.setWindowModality(Qt.WindowModal)

            # Create output PDF
            output_doc = fitz.open()

            for i, page_num in enumerate(page_range):
                if progress.wasCanceled():
                    break

                # Process page (0-based index)
                processed_img, _ = self.process_page(page_num - 1)
                if processed_img is None:
                    continue

                # Convert processed image to PNG for PDF embedding
                success, img_data = cv2.imencode('.png',
                                               cv2.cvtColor(processed_img,
                                                          cv2.COLOR_RGB2BGR))
                if not success:
                    continue

                # Create new page
                page = output_doc.new_page(
                    width=processed_img.shape[1],
                    height=processed_img.shape[0]
                )

                # Insert image
                page.insert_image(page.rect, stream=img_data.tobytes())

                progress.setValue(i + 1)

            # Save PDF
            output_doc.save(filename)
            output_doc.close()

            if not progress.wasCanceled():
                QMessageBox.information(self, "Success",
                                      f"PDF report generated with {len(page_range)} pages!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate PDF: {str(e)}")

    def generate_excel(self):
        """Generate Excel report with feature detection results"""
        if self.doc is None:
            QMessageBox.warning(self, "Warning", "No PDF document loaded")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Excel Report", "", "Excel Files (*.xlsx)")
        if not filename:
            return

        if not filename.lower().endswith('.xlsx'):
            filename += '.xlsx'

        try:
            # Parse page range
            page_range = self.parse_page_range(
                self.page_range_input.text(),
                self.doc.page_count
            )

            # Create progress dialog
            progress = QProgressDialog("Generating Excel report...", "Cancel", 0, len(page_range), self)
            progress.setWindowModality(Qt.WindowModal)

            # Prepare data for Excel
            data = []

            for i, page_num in enumerate(page_range):
                if progress.wasCanceled():
                    break

                # Process page (0-based index)
                _, keypoints = self.process_page(page_num - 1)
                if keypoints is None:
                    continue

                # Extract keypoint information
                for kp in keypoints:
                    x, y = map(int, kp.pt)
                    size = int(kp.size)
                    half_size = size // 2

                    # Calculate bounding box
                    x1 = x - half_size
                    y1 = y - half_size
                    x2 = x + half_size
                    y2 = y + half_size

                    # Calculate area
                    area = size * size

                    # Add to data
                    data.append({
                        'pdf_page': page_num,
                        'center_x': x,
                        'center_y': y,
                        'x1': x1,
                        'y1': y1,
                        'x2': x2,
                        'y2': y2,
                        'area': area,
                        'size': size,
                        'response': kp.response,
                        'dominant_angle': kp.angle
                    })

                progress.setValue(i + 1)

            if progress.wasCanceled():
                return

            # Create DataFrame and save to Excel
            import pandas as pd
            df = pd.DataFrame(data)

            # Reorder columns
            columns = ['pdf_page', 'center_x', 'center_y', 'x1', 'y1', 'x2', 'y2',
                      'area', 'size', 'response', 'dominant_angle']
            df = df[columns]

            # Save to Excel with formatting
            with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Features', index=False)

                workbook = writer.book
                worksheet = writer.sheets['Features']

                # Add column descriptions as comments
                descriptions = {
                    'response': 'Strength/reliability of the detected feature. Higher values indicate more distinctive features.',
                    'dominant_angle': 'Dominant orientation of the feature in degrees (0-360°). Used for rotation-invariant matching.'
                }

                # Add header formatting and comments
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'bg_color': '#D9D9D9',
                    'border': 1
                })

                for col_num, col_name in enumerate(df.columns.values):
                    worksheet.write(0, col_num, col_name, header_format)
                    if col_name in descriptions:
                        worksheet.write_comment(0, col_num, descriptions[col_name])

                # Adjust column widths
                for idx, col in enumerate(df.columns):
                    series = df[col]
                    max_len = max(
                        series.astype(str).apply(len).max(),
                        len(str(series.name))
                    ) + 1
                    worksheet.set_column(idx, idx, max_len)

            QMessageBox.information(self, "Success",
                                  f"Excel report generated with {len(data)} features!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate Excel report: {str(e)}")

    def toggle_original_image(self):
        """Toggle visibility of the original image"""
        is_visible = self.toggle_original_btn.isChecked()
        self.original_label.setVisible(is_visible)
        self.toggle_original_btn.setText("Hide Original" if is_visible else "Show Original")


def detect_image_features(image, detector_type="AKAZE", detector_params=None, preprocessing=True):
    """
    Process an image to detect features without GUI dependency.

    Args:
        image: numpy array of the image (BGR or RGB format)
        detector_type: str, one of ["SIFT", "ORB", "FAST", "BRISK", "AKAZE"]
        detector_params: dict, parameters for the detector
        preprocessing: bool, whether to apply preprocessing

    Returns:
        tuple: (processed_image, df, detector)
            - processed_image: numpy array with drawn features
            - df: pandas DataFrame with feature information
            - detector: the created detector object
    """
    # Default parameters for each detector
    default_params = {
        "SIFT": {
            "nfeatures": 0,
            "nOctaveLayers": 3,
            "contrastThreshold": 0.04,
            "edgeThreshold": 10,
            "sigma": 1.6
        },
        "ORB": {
            "nfeatures": 500,
            "scaleFactor": 1.2,
            "nlevels": 8,
            "edgeThreshold": 31
        },
        "FAST": {
            "threshold": 10,
            "nonmaxSuppression": True
        },
        "BRISK": {
            "thresh": 30,
            "octaves": 3,
            "patternScale": 1.0
        },
        "AKAZE": {
            "threshold": 0.0128,
            "nOctaves": 4,
            "nOctaveLayers": 4
        }
    }

    # Use default params if none provided
    if detector_params is None:
        detector_params = default_params[detector_type]

    # Create detector
    if detector_type == "SIFT":
        detector = cv2.SIFT_create(**detector_params)
    elif detector_type == "ORB":
        detector = cv2.ORB_create(**detector_params)
    elif detector_type == "FAST":
        detector = cv2.FastFeatureDetector_create(**detector_params)
    elif detector_type == "BRISK":
        detector = cv2.BRISK_create(**detector_params)
    elif detector_type == "AKAZE":
        detector = cv2.AKAZE_create(**detector_params)
    else:
        raise ValueError(f"Unsupported detector type: {detector_type}")

    # Ensure RGB format
    if len(image.shape) == 3 and image.shape[2] == 3:
        if detector_type == "SIFT":  # SIFT works better with BGR
            working_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        else:
            working_image = image.copy()
    else:
        raise ValueError("Image must be in RGB format")

    # Preprocess
    if preprocessing:
        # Convert to grayscale
        gray = cv2.cvtColor(working_image, cv2.COLOR_RGB2GRAY)
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        # Apply CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        processed = clahe.apply(blurred)
    else:
        processed = cv2.cvtColor(working_image, cv2.COLOR_RGB2GRAY)

    # Detect features
    keypoints = detector.detect(processed, None)

    # Create output image with features
    # output_img = image.copy()
    # cv2.drawKeypoints(
    #     image,
    #     keypoints,
    #     output_img,
    #     color=(0, 255, 0),
    #     flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS
    # )

    import math

    def distance_to(p1, p2):
        # Gives the absolute distance between two points
        return math.hypot(p2[0] - p1[0], p2[1] - p1[1])

    def close_to(p1, p2, threshold=3):
        return distance_to(p1, p2) < threshold

    added = set()

    # Create DataFrame with feature information
    data = []
    for kp in keypoints:
        x, y = map(int, kp.pt)
        size = int(kp.size)
        half_size = size // 2

        for k in added:
            if close_to((x, y), (k[0], k[1]), threshold=3):
                print("Skipping close point: ", x, y, k[0], k[1])
                continue
        added.add((x, y))

        data.append({
            'center_x': x,
            'center_y': y,
            'x1': x - half_size,
            'y1': y - half_size,
            'x2': x + half_size,
            'y2': y + half_size,
            'area': size * size,
            'size': size,
            'response': kp.response,
            'dominant_angle': kp.angle
        })

    # Create DataFrame and order columns
    df = pd.DataFrame(data)
    columns = ['center_x', 'center_y', 'x1', 'y1', 'x2', 'y2',
              'area', 'size', 'response', 'dominant_angle']
    df = df[columns]

    return df#, df, detector

def get_keypoint_info(keypoint):
    """
    Extract information from a keypoint.

    Args:
        keypoint: cv2.KeyPoint object

    Returns:
        dict: Keypoint information including coordinates, size, response, etc.
    """
    x, y = map(int, keypoint.pt)
    size = int(keypoint.size)
    half_size = size // 2

    return {
        'center_x': x,
        'center_y': y,
        'x1': x - half_size,
        'y1': y - half_size,
        'x2': x + half_size,
        'y2': y + half_size,
        'area': size * size,
        'size': size,
        'response': keypoint.response,
        'angle': keypoint.angle
    }


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FeatureDetectionDialog()
    window.show()
    sys.exit(app.exec())

