"""
For printing BOM regions with row start, end and contiguous check

"""

import os
import io
import re
import ast
import fitz
import pandas as pd
import numpy as np
import cv2
from PIL import Image
from pathlib import Path
from src.app_paths import getSourceExtractionOptionsPath
from src.utils.convert_roi_payload import convert_roi_payload
from src.utils.pdf.page_to_opencv import page_to_opencv
from src.app_paths import getSourceMissingRegionsPath, getSourceRawDataPath
from src.atom.fast_storage import load_df_fast

def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip())  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(1, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part)  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return pages


def plugin_print_boms(project_source: tuple,
                    page_range: str,
                    extract_group_list: str = None,
                    roi_payload: str = None,
                    zoom: float = 1.5,
                    bom_file: str = r"",
                    save_file: str = "debug/bom_roi_checks.pdf"):

    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    print(projectId, filename)

    if not bom_file:
        return "BOM data required"

    df = pd.read_excel(bom_file)
    df["pos"]
    df["pdf_page"]

    group_list = []
    if extract_group_list:
        try:
            group_list = [int(g) for g in extract_group_list.split(",")]
        except Exception:
            pass

        if not group_list:
            try:
                group_list = [int(extract_group_list)]
            except Exception:
                return "Invalid group list format."

    # Group list takes precedence over page range
    if not roi_payload:
        print("Loading saved ROI payload")
        roi_payload = getSourceExtractionOptionsPath(projectId, filename)
        if not os.path.exists(roi_payload):
            error = "A saved ROI payload not found for this project source."
            print(error)
            return error

    roi_payload = convert_roi_payload(roi_payload, force_extract=True, ignore_bom=True)

    cleaned_roi_payload = {}
    cleaned_roi_payload["ocr"] = roi_payload["ocr"]

    pages = parse_page_range(str(page_range), 9999)
    final_pages = set()
    unique_groups = set()
    group_pages = {}
    cleaned_roi_payload['pageToGroup'] = {}
    for page, group in roi_payload['pageToGroup'].items():
        if extract_group_list and group not in group_list:
            continue
        if page in pages:
            cleaned_roi_payload['pageToGroup'][page] = group
            group_pages.setdefault(group, []).append(page)
            final_pages.add(page)
            unique_groups.add(group)

    final_groups = set()
    cleaned_roi_payload['groupRois'] = {}
    for group, rois in roi_payload['groupRois'].items():
        if group not in unique_groups:
            continue
        if extract_group_list and group not in group_list:
            continue
        cleaned = []
        for roi in rois:
            column_name = roi["columnName"].lower()
            if column_name != "bom":
                continue
            cleaned.append(roi)

        if cleaned:
            cleaned_roi_payload['groupRois'][group] = cleaned
            final_groups.add(group)

    doc = fitz.open(filename)
    os.makedirs(os.path.dirname(save_file), exist_ok=True)
    stem = Path(filename).stem

    def is_contiguous(lst):
        # doesn't account for 1a, 1b.
        try:
            if not lst:
                return False  # empty list can't be contiguous
            lst = [int(n) for n in lst]
            min_val = min(lst)
            max_val = max(lst)
            # A contiguous list should have exactly (max - min + 1) unique elements
            return len(set(lst)) == (max_val - min_val + 1)
        except:
            return False

    pages_added = 0
    output_pdf = fitz.open()

    for pdf_page in sorted(final_pages):
        group = cleaned_roi_payload['pageToGroup'].get(pdf_page, None)
        if group is None:
            # Handle case where page has no group mapping
            rois = []
        else:
            rois = cleaned_roi_payload['groupRois'].get(group, [])

        page = doc[pdf_page - 1]
        page_width = page.rect.width
        page_height = page.rect.height

        # If there are no ROIs for this page, add the original page with NO BOM status
        if not rois:
            # Add a new page to the PDF with the original page dimensions
            new_page = output_pdf.new_page(width=page_width, height=page_height)

            # Copy the original page content
            new_page.show_pdf_page(new_page.rect, doc, pdf_page - 1)

            # Add a "NO BOM" status text with background
            # Convert page to image for adding text
            pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            image = np.array(img)

            # Font settings
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1.0  # Larger font for visibility
            font_thickness = 2

            # Create text labels
            page_text = f"Page: {pdf_page}"
            status_text = "Status: NO BOM"

            # Get text sizes for positioning
            (page_text_width, page_text_height), _ = cv2.getTextSize(page_text, font, font_scale, font_thickness)
            (status_text_width, status_text_height), _ = cv2.getTextSize(status_text, font, font_scale, font_thickness)

            # Calculate positions (top right corner)
            page_x = image.shape[1] - page_text_width - 20
            status_x = image.shape[1] - status_text_width - 20

            # Draw background rectangles
            bg_color = (50, 50, 50)  # Dark gray background

            # Background for page number
            cv2.rectangle(image,
                         (page_x - 5, 30 - page_text_height - 5),
                         (page_x + page_text_width + 5, 30 + 5),
                         bg_color, -1)

            # Background for status
            cv2.rectangle(image,
                         (status_x - 5, 60 - status_text_height - 5),
                         (status_x + status_text_width + 5, 60 + 5),
                         bg_color, -1)

            # Draw text
            cv2.putText(image, page_text, (page_x, 30),
                        font, font_scale, (0, 0, 255), font_thickness)  # Red for page number
            cv2.putText(image, status_text, (status_x, 60),
                        font, font_scale, (0, 0, 255), font_thickness)  # Red for NO BOM

            # Convert back to PIL and insert into PDF
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)

            # Convert PIL Image to bytes
            img_bytes = io.BytesIO()
            pil_image.save(img_bytes, format="PNG")
            img_bytes.seek(0)

            # Insert the image into the PDF
            rect = fitz.Rect(0, 0, pil_image.width, pil_image.height)
            new_page.insert_image(rect, stream=img_bytes)

            pages_added += 1
            print(f"Added original page {pdf_page} with NO BOM status")
            continue

        page_df = df[df["pdf_page"] == pdf_page]
        pos_values = page_df["pos"].values.tolist()
        material_descriptions = page_df["material_description"].values.tolist()
        contiguous = is_contiguous(pos_values)

        if rois:
            bom_roi = rois[0]

            # Create an OpenCV image from the page
            image = page_to_opencv(page, zoom)

            # Variables to track region offsets for ROI adjustments
            x_offset = 0
            y_offset = 0

            # Get the BOM ROI coordinates
            tableCoords = bom_roi["tableCoordinates"]
            x0 = tableCoords["relativeX0"] * page_width
            y0 = tableCoords["relativeY0"] * page_height
            x1 = tableCoords["relativeX1"] * page_width
            y1 = tableCoords["relativeY1"] * page_height

            # Scale coordinates for zoom
            x0_zoomed = int(x0 * zoom)
            y0_zoomed = int(y0 * zoom)
            x1_zoomed = int(x1 * zoom)
            y1_zoomed = int(y1 * zoom)

            # Ensure coordinates are within image bounds
            x0_zoomed = max(0, min(x0_zoomed, image.shape[1] - 1))
            y0_zoomed = max(0, min(y0_zoomed, image.shape[0] - 1))
            x1_zoomed = max(0, min(x1_zoomed, image.shape[1]))
            y1_zoomed = max(0, min(y1_zoomed, image.shape[0]))

            # Add some padding around the ROI (20 pixels on each side)
            padding = 20
            y0_padded = max(0, y0_zoomed - padding)
            x0_padded = max(0, x0_zoomed - padding)
            y1_padded = min(image.shape[0], y1_zoomed + padding)
            x1_padded = min(image.shape[1], x1_zoomed + padding)

            # Crop the image to show only the BOM ROI with padding
            cropped_image = image[y0_padded:y1_padded, x0_padded:x1_padded]

            # Add a border around the image if positions are not contiguous
            # if not contiguous:
            # Draw a red border around the entire cropped image
            border_thickness = 5  # Thicker border for visibility
            border_color = (0, 0, 255) if not contiguous else (0, 255, 0)  # Red in BGR format
            h, w = cropped_image.shape[:2]
            # Draw rectangle on all four sides
            cv2.rectangle(cropped_image, (0, 0), (w-1, h-1), border_color, border_thickness)

            # Prepare label information
            first_pos = pos_values[0] if pos_values else "N/A"
            last_pos = pos_values[-1] if pos_values else "N/A"
            contiguous_text = "CONTIGUOUS" if contiguous else "NON-CONTIGUOUS"

            # Set color based on contiguous status - BGR format for OpenCV
            status_color = (0, 255, 0) if contiguous else (0, 0, 255)  # Green if contiguous, Red if not

            # Calculate text positions
            roi_width = cropped_image.shape[1]
            roi_height = cropped_image.shape[0]

            # Font settings
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            font_thickness = 2

            # Create text labels for right side
            page_text = f"Page: {pdf_page}"
            status_text = f"Status: {contiguous_text}"

            # Get text sizes for right alignment
            (page_text_width, page_text_height), _ = cv2.getTextSize(page_text, font, font_scale, font_thickness)
            (status_text_width, status_text_height), _ = cv2.getTextSize(status_text, font, font_scale, font_thickness)

            # Calculate positions for right alignment
            page_x = roi_width - page_text_width - 10
            status_x = roi_width - status_text_width - 10

            # Draw background rectangles
            bg_color = (50, 50, 50)  # Dark gray background

            # Background for page number
            cv2.rectangle(cropped_image,
                         (page_x - 5, 30 - page_text_height - 5),
                         (page_x + page_text_width + 5, 30 + 5),
                         bg_color, -1)

            # Background for status
            cv2.rectangle(cropped_image,
                         (status_x - 5, 60 - status_text_height - 5),
                         (status_x + status_text_width + 5, 60 + 5),
                         bg_color, -1)

            # Draw text with right alignment for page and status
            cv2.putText(cropped_image, page_text, (page_x, 30),
                        font, font_scale, (0, 0, 255), font_thickness)  # Red for page number
            cv2.putText(cropped_image, status_text, (status_x, 60),
                        font, font_scale, status_color, font_thickness)  # Green/Red based on status

            # Calculate left margin for position values (20% of image width)
            left_margin = int(roi_width * 0.2)

            # Draw position values vertically on the left side with fixed spacing
            if pos_values:
                # Fixed vertical spacing
                vertical_gap = 25  # Fixed gap between position values

                # Start position for the first value
                y_pos = 40

                # Draw each position value with background
                for i, pos in enumerate(pos_values):
                    # Skip if we're going to run out of vertical space
                    if y_pos > roi_height - 30:
                        # Show how many more values are left
                        remaining = len(pos_values) - i

                        # Add background for the "more" text
                        more_text = f"... {remaining} more"
                        (text_width, text_height), _ = cv2.getTextSize(more_text, font, font_scale, font_thickness)

                        # Draw background rectangle
                        bg_color = (50, 50, 50)  # Dark gray background
                        cv2.rectangle(cropped_image,
                                     (left_margin - 5, y_pos - text_height - 5),
                                     (left_margin + text_width + 5, y_pos + 5),
                                     bg_color, -1)  # -1 fills the rectangle

                        # Draw text
                        cv2.putText(cropped_image, more_text,
                                   (left_margin, y_pos),
                                   font, font_scale, status_color, font_thickness)
                        break

                    # Get text size for this position value
                    pos_text = f"{pos}"
                    (text_width, text_height), _ = cv2.getTextSize(pos_text, font, font_scale, font_thickness)

                    # Draw background rectangle
                    bg_color = (50, 50, 50)  # Dark gray background
                    cv2.rectangle(cropped_image,
                                 (left_margin - 5, y_pos - text_height - 5),
                                 (left_margin + text_width + 5, y_pos + 5),
                                 bg_color, -1)  # -1 fills the rectangle

                    # Draw position value
                    cv2.putText(cropped_image, pos_text, (left_margin, y_pos),
                                font, font_scale, status_color, font_thickness)

                    # Draw material description (first 5 characters) next to position value
                    if i < len(material_descriptions):
                        # Get first 5 characters of description
                        desc_text = material_descriptions[i][:9] if material_descriptions[i] and not pd.isna(material_descriptions[i]) else ""
                        # Calculate position with a small gap after the position value
                        desc_x = left_margin + text_width + 10  # 10 pixels gap

                        # Get text size for description
                        (desc_width, desc_height), _ = cv2.getTextSize(desc_text, font, 0.4, 2)

                        # Draw light transparent background for description
                        # Create a semi-transparent overlay
                        overlay = cropped_image.copy()
                        bg_color_desc = (30, 30, 30)  # Dark gray but lighter than position background
                        cv2.rectangle(overlay,
                                     (desc_x - 3, y_pos - desc_height - 3),
                                     (desc_x + desc_width + 3, y_pos + 3),
                                     bg_color_desc, -1)  # -1 fills the rectangle
                        # Apply the overlay with transparency
                        alpha = 0.5  # 50% transparency
                        cv2.addWeighted(overlay, alpha, cropped_image, 1 - alpha, 0, cropped_image)

                        # Draw description text
                        cv2.putText(cropped_image, desc_text, (desc_x, y_pos),
                                   font, 0.4, (255, 0, 0), 2)  # Red text

                    # Move to next position
                    y_pos += vertical_gap
            else:
                # No positions
                no_pos_text = "No positions found"
                (text_width, text_height), _ = cv2.getTextSize(no_pos_text, font, font_scale, font_thickness)

                # Draw background rectangle
                bg_color = (50, 50, 50)  # Dark gray background
                y_pos = roi_height // 2
                cv2.rectangle(cropped_image,
                             (left_margin - 5, y_pos - text_height - 5),
                             (left_margin + text_width + 5, y_pos + 5),
                             bg_color, -1)  # -1 fills the rectangle

                # Draw text
                cv2.putText(cropped_image, no_pos_text, (left_margin, y_pos),
                            font, font_scale, (0, 0, 255), font_thickness)

            # Convert OpenCV image (BGR) to RGB for PIL
            cropped_image_rgb = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(cropped_image_rgb)

            # Add a new page to the PDF
            page = output_pdf.new_page(width=pil_image.width, height=pil_image.height)

            # Convert PIL Image to bytes
            img_bytes = io.BytesIO()
            pil_image.save(img_bytes, format="PNG")
            img_bytes.seek(0)

            # Insert the image into the PDF
            rect = fitz.Rect(0, 0, pil_image.width, pil_image.height)
            page.insert_image(rect, stream=img_bytes)

            pages_added += 1
            print(f"Added page {pdf_page} to PDF")

    # Save the PDF if we have pages
    if pages_added > 0:
        output_pdf.save(save_file)
        print(f"Saved combined PDF to {save_file}")
    else:
        print("No valid BOM regions found to save")

    # Close the documents
    doc.close()
    output_pdf.close()

    ret = {
        "message": "Finished",
        "pages_saved": pages_added,
        "save_file": save_file
    }

    return ret