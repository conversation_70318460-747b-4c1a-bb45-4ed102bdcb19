"""
Simple test to verify Langchain Google GenAI integration works
"""

import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set GOOGLE_API_KEY from GEMINI_API_KEY if needed
gemini_key = os.getenv('GEMINI_API_KEY')
if gemini_key and not os.getenv('GOOGLE_API_KEY'):
    os.environ['GOOGLE_API_KEY'] = gemini_key
    print(f"✓ Set GOOGLE_API_KEY from GEMINI_API_KEY")

try:
    from langchain_google_genai import ChatGoogleGenerative<PERSON>I
    from langchain_core.messages import SystemMessage, HumanMessage
    print("✓ Langchain Google GenAI imports successful")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    exit(1)

async def test_simple_call():
    """Test a simple API call"""
    print("\n" + "=" * 50)
    print("TESTING SIMPLE LANGCHAIN CALL")
    print("=" * 50)
    
    try:
        # Create model
        model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.0,
            max_tokens=100
        )
        
        print("✓ Model created successfully")
        
        # Test simple call
        messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content="What is 2+2? Answer with just the number.")
        ]
        
        print("Making API call...")
        response = await model.ainvoke(messages)
        
        print(f"✓ Response received: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ API call failed: {e}")
        return False

async def test_structured_output():
    """Test structured output with Pydantic"""
    print("\n" + "=" * 50)
    print("TESTING STRUCTURED OUTPUT")
    print("=" * 50)
    
    try:
        from langchain_core.pydantic_v1 import BaseModel, Field
        
        class SimpleResponse(BaseModel):
            """Simple response structure"""
            status: str = Field(..., description="Either 'success' or 'error'")
            message: str = Field(..., description="Response message")
            number: int = Field(..., description="A number")
        
        # Create model with structured output
        base_model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.0,
            max_tokens=200
        )
        
        structured_model = base_model.with_structured_output(SimpleResponse)
        print("✓ Structured model created successfully")
        
        # Test structured call
        messages = [
            SystemMessage(content="You are a helpful assistant that responds with structured data."),
            HumanMessage(content="Give me a success response with the message 'Hello World' and the number 42.")
        ]
        
        print("Making structured API call...")
        response = await structured_model.ainvoke(messages)
        
        print(f"✓ Structured response received:")
        print(f"  Status: {response.status}")
        print(f"  Message: {response.message}")
        print(f"  Number: {response.number}")
        
        return True
        
    except Exception as e:
        print(f"❌ Structured API call failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("Langchain Google GenAI Integration Test")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY not found")
        return
    
    print(f"✓ API key found: {api_key[:10]}...")
    
    # Test simple call
    simple_success = await test_simple_call()
    
    if simple_success:
        # Test structured output
        structured_success = await test_structured_output()
        
        if structured_success:
            print("\n" + "=" * 50)
            print("✅ ALL TESTS PASSED!")
            print("Langchain Google GenAI integration is working correctly.")
            print("=" * 50)
        else:
            print("\n❌ Structured output test failed")
    else:
        print("\n❌ Simple API call test failed")

if __name__ == "__main__":
    asyncio.run(main())
