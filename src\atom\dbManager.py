import sqlite3
import json
import time
import datetime
import polars as pl

import pandas as pd
from pubsub import pub

from src.utils.logger import logger

def extract_coordinates(record):
    if not record:
        return None
    record = record.replace("(", "")
    record = record.replace(")", "")
    coordinates = tuple(map(float, record.split(', ')))
    return coordinates

class DatabaseManager:

    def __init__(self, db_path=None):
        if not db_path:
            from src.app_paths import getDatabasePath
            db_path = getDatabasePath()
        self.db_path = db_path

    def connect(self):
        return sqlite3.connect(self.db_path)

    def insert_new_project(self, projectNumber, projectName, userCompanyName, clientName, projectLocation, projectScope):
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute('''INSERT INTO Projects (projectName, projectNumber, userCompanyName, clientName, projectLocation, projectScope)
                              VALUES (?, ?, ?, ?, ?, ?)''', (projectName, projectNumber, userCompanyName, clientName, projectLocation, projectScope))
            return cursor.lastrowid

    def insert_pdf_page_to_storage(self,
                                   project_id,
                                   pdf_path,
                                   pdf_page,
                                   documentType,
                                   documentName,
                                   pdf_content,
                                   doc_size,
                                   skipped):
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute('''SELECT id FROM PDFStorage WHERE project_id=? AND originalFilename=? AND pdf_page=? ORDER BY pdf_page ASC''',
                           (project_id, pdf_path, pdf_page))
            result = cursor.fetchall()
            if len(result) > 0:
                return result[0][0]

        max_retries = 5  # Maximum number of retries
        backoff_factor = 0.2  # Time to wait between retries, increases each retry
        for attempt in range(max_retries):
            try:
                with self.connect() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO PDFStorage (project_id, originalFilename, pdf_page, documentType, documentName, document, docSize, skipped)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (project_id, pdf_path, pdf_page, documentType, documentName, pdf_content, doc_size, skipped))
                    conn.commit()
                    return cursor.lastrowid
            except sqlite3.OperationalError as e:
                if "locked" in str(e):
                    wait = backoff_factor * (2 ** attempt)  # Exponential backoff
                    logger.warning(f"Database is locked, retrying in {wait} seconds...")
                    time.sleep(wait)
                else:
                    logger.critical(f"Could not commit page ({pdf_page}) to PDFStorage: {e}", exc_info=True)
                    break  # If the error is not a lock, don't retry
            except Exception as e:
                logger.critical(f"Could not commit page ({pdf_page}) to PDFStorage: {e}", exc_info=True)
                return None  # Return None immediately on unrecoverable errors
                break  # Break on other exceptions
        else:  # If the loop completes without returning or breaking, all retries failed
            logger.error(f"All retries failed for page {pdf_page}")
        return None  # Explicitly return None if all retries fail

    def get_highest_pdf_id(self):
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT MAX(pdf_id) FROM General')
            result = cursor.fetchone()
            return result[0] if result[0] is not None else 0

    def insert_dataframe(self, df, table_name, batch_size=400):
        """Insert a DataFrame into the database after converting data types and filtering columns."""
        start_time = time.time()
        engine = create_engine(f'sqlite:///{self.db_path}')
        error_messages = []  # Track all errors
        error_message = ""  # To hold the error message if the insert fails
        total_processed = 0

        delete_statements = {
            "BOM": "Delete from BOM where id=?",
            "General": "Delete from General where id=?",
            "RawData": "Delete from RawData where id=?",
            "SPOOL": "Delete from SPOOL where id=?",
            "SPEC": "Delete from SPEC where id=?",
        }
        success = True

        # with engine.begin() as conn:
        with self.connect() as conn:
            # Convert DataFrame data types and filter columns to match the database schema
            df_ready: pd.DataFrame = self.convert_data_types_and_filter_columns(df.copy(), table_name, engine)

            if table_name == "raw_data":
                # First, get all unique sys_paths we'll need to delete
                all_paths = set(df_ready['sys_path'].unique())
                if all_paths:
                    # Do one big delete for all paths at the start
                    batch_paths = tuple(all_paths)
                    placeholders = ','.join('?' for _ in batch_paths)
                    delete_query = f"DELETE FROM raw_data WHERE sys_path IN ({placeholders})"
                    conn.execute(delete_query, batch_paths)
                    conn.commit()
                    print(f"Deleted {len(all_paths)} existing paths")

                # Now process inserts in batches
                for start_idx in range(0, len(df_ready), batch_size):
                    # Get current batch
                    batch_df = df_ready.iloc[start_idx:start_idx + batch_size]

                    # Insert batch
                    batch_df.to_sql(table_name, conn, if_exists='append', index=False)

                    # Commit each batch
                    conn.commit()

                    total_processed += len(batch_df)
                    print(f"Processed batch: {total_processed} / {len(df_ready)} records")

            elif table_name in ["BOM", "General", "RawData", "SPOOL", "SPEC"]:
                delete_statement = delete_statements.get(table_name)
                if not delete_statement:
                    print("Table insert/delete not supported", table_name)
                    return
                # Debugging: Print the DataFrame columns after processing
                print(f"\nDataFrame columns ready for insert: {list(df_ready.columns)}")
                # Try to delete items if the id already exists
                if "id" in df_ready:
                    for tableId in df_ready["id"].to_list():
                        try:
                            # TODO: review SQL injection prevention
                            conn.execute(delete_statement, (int(tableId), ))
                        except Exception as e:
                            logger.info(f"Failed to delete {table_name} by id. May not exist", e)

                if table_name == "BOM":
                    cursor = conn.cursor()
                    existing_indices = []
                    for index, row in df_ready.iterrows():
                        conditions = ', '.join(f"{col} = ?" for col in df_ready.columns.tolist())
                        # query = f"DELETE FROM {table_name} WHERE pdf_id = ? AND pos = ?"
                        query = f"SELECT 1 FROM {table_name} WHERE pdf_id = ? AND pos = ? LIMIT 1"
                        # query = f"-- UPDATE {table_name} SET {conditions} WHERE pdf_id = ? AND pos = ?"

                        cursor.execute(query, (row['pdf_id'], row['pos']))
                        r = cursor.fetchone()
                        print(r)
                        if r:
                            print(index)
                            existing_indices.append(index)
                            cursor.execute(f"UPDATE {table_name} SET {conditions} WHERE pdf_id = ? AND pos = ?", tuple(row[col] for col in df_ready.columns.tolist()) +
                                           (row['pdf_id'], row['pos']))
                            conn.commit()
                    df_ready = df_ready.drop(existing_indices).reset_index(drop=True)

            try:
                # Insert the DataFrame into the database tables
                df_ready.to_sql(table_name, conn, if_exists='append', index=False)
                print("Insert operation completed successfully.")

            except Exception as e:
                success = False
                error_message = str(e)
                print(f"\nFailed to insert DataFrame: {e}")

        end_time = time.time()
        duration = end_time - start_time

        if success:
            message = (f"Finished inserting data into {table_name}.\n"
                       f"Duration: {duration:.2f} seconds.\n"
                       f"Rows inserted: {len(df_ready)}.\n"
                       f"Columns inserted: {len(df_ready.columns)}.")
        else:
            message = (f"Failed to insert {len(df_ready)} data rows into {table_name}.\n"
                       f"Reason: {error_message}")

        new_message = ""
        if error_messages:
            new_message += "\nErrors encountered:\n" + "\n".join(error_messages[:5])
            if len(error_messages) > 5:
                new_message += f"\n... and {len(error_messages) - 5} more errors"

        # Display the message in the console
        print(message)

        if success:
            pub.sendMessage("show-app-messagebox", msg=message, title="Insert Successful", icon="info")
        else:
            pub.sendMessage("show-app-messagebox", msg=message, title="Insert Failed", icon="critical")

    def update_rfq(self, df: pd.DataFrame):
        """Update or insert RFQ DataFrame into the database"""
        table_name = "RFQ"
        start_time = time.time()
        engine = create_engine(f'sqlite:///{self.db_path}')
        success = True  # Flag to track if the operation was successful
        error_message = ""  # To hold the error message if the insert fails

        timestamp = datetime.datetime.now()

        # with engine.begin() as conn:
        with self.connect() as conn:
            # Convert DataFrame data types and filter columns to match the database schema
            df_ready: pd.DataFrame = self.convert_data_types_and_filter_columns(df.copy(), table_name, engine)

            # Debugging: Print the DataFrame columns after processing
            print(f"\nDataFrame columns ready for insert: {list(df_ready.columns)}")
            for _, row in df_ready.iterrows():
                try:
                    material_description = row["material_description"]
                    size = row["size"]
                    row["last_updated"] = timestamp

                    # Check if the record already exists
                    existing_record = conn.execute(
                        "SELECT 1 FROM RFQ WHERE material_description = ? AND size = ?",
                        (material_description, size)
                    ).fetchone()

                    if existing_record:
                        # Update the existing record
                        update_statement = f"""
                            UPDATE RFQ
                            SET {', '.join([f"{col} = ?" for col in df_ready.columns if col not in ['material_description', 'size']])}
                            WHERE material_description = ? AND size = ?
                        """
                        update_values = [row[col] for col in df_ready.columns if col not in ['material_description', 'size']]
                        update_values.extend([material_description, size])
                        conn.execute(update_statement, update_values)
                    else:
                        # Insert the new record
                        insert_statement = f"INSERT INTO RFQ ({', '.join(df_ready.columns)}) VALUES ({', '.join(['?' for _ in df_ready.columns])})"
                        insert_values = [row[col] for col in df_ready.columns]
                        conn.execute(insert_statement, insert_values)

                except Exception as e:
                    success = False
                    error_message = str(e)
                    print(f"\nFailed to insert/update DataFrame: {e}")
                    break

        end_time = time.time()
        duration = end_time - start_time

        if success:
            message = (f"Finished inserting data into {table_name}.\n"
                       f"Duration: {duration:.2f} seconds.\n"
                       f"Rows inserted: {len(df_ready)}.\n"
                       f"Columns inserted: {len(df_ready.columns)}.")
        else:
            message = (f"Failed to insert {len(df_ready)} data rows into {table_name}.\n"
                       f"Reason: {error_message}")

        # Display the message in the console
        print(message)

        if success:
            pub.sendMessage("show-app-messagebox", msg="Updated", title="RFQ Update Successful", icon="info")
        else:
            pub.sendMessage("show-app-messagebox", msg=message, title="Insert Failed", icon="critical")

        return {
            "success": success,
            "last_updated": timestamp if success else None
        }

    def convert_data_types_and_filter_columns(self, df, table_name, engine):
        """Convert DataFrame columns to match the data types of the database table and drop non-matching columns."""
        import sqlalchemy
        inspector = sqlalchemy.inspect(engine)
        columns_info = sqlalchemy.inspector.get_columns(table_name)
        db_fields_types = {col['name']: col['type'] for col in columns_info}
        db_fields = set(db_fields_types.keys())

        # Drop columns that are not in the database table
        df_filtered = df[[col for col in df.columns if col in db_fields]].copy()
        df = None # Clear from mem

        # Convert data types
        for col_name, col_type in db_fields_types.items():
            if col_name in df_filtered.columns:
                if isinstance(col_type, sqlalchemy.sql.sqltypes.INTEGER):
                    df_filtered[col_name] = pd.to_numeric(df_filtered[col_name], errors='coerce').fillna(0).astype(int)
                elif isinstance(col_type, sqlalchemy.sql.sqltypes.TEXT):
                    df_filtered[col_name] = df_filtered[col_name].fillna('').astype(str)
                # Add more type conversions here (e.g., for DATE fields)

        return df_filtered

    def get_sorted_fields(self, roi_exclude_keys=['annotMarkups'], include_fields=[]):
        print("\n\n-----------------------------------Accessed get_sorted_fields\n\n")

        roi_exclude_keys = ['annotMarkups', "avg_elevation", "coordinates", "coordinates2", "flangeID", "max_elevation",
                        "min_elevation", "modDate", "sys_build", "sys_document", "sys_filename", "sys_path",
                        "xCoord", "yCoord", "weldId", "id_annot_info", "pdf_id", "pdf_page"] # Exclude from ROI drop down selection

        include_fields=[]

        conn = None
        try:
            # Connect to the database
            conn = self.connect()
            cursor = conn.cursor()

            # Lists to store field names
            fields = []
            fields_dict = {}

            # SQL to get column names excluding primary and foreign keys
            for table_name in ["General", "BOM"]:
                query = f"PRAGMA table_info({table_name})"
                cursor.execute(query)
                columns = cursor.fetchall()
                for column in columns:
                    # Column format: (cid, name, type, notnull, dflt_value, pk)
                    if column[5] == 0:  # Not a primary key
                        field_name = column[1]
                        if field_name not in roi_exclude_keys:
                            fields.append(field_name)

            # Sort fields, add 'BOM' at the start, and append any additional fields
            sorted_fields = sorted(set(fields))  # Remove duplicates and sort
            if 'BOM' in sorted_fields:  # Ensure 'BOM' is not in the list before adding to top
                sorted_fields.remove('BOM')
            sorted_fields = ['BOM'] + sorted_fields + include_fields

            print(f"\n\n\n-----------------------------------------------------------------Sorted BOM fields: {sorted_fields}")

            if 'Spool' in sorted_fields:  # Ensure 'Spool' is not in the list before adding to top
                sorted_fields.remove('Spool')
            sorted_fields = ['Spool'] + sorted_fields + include_fields

            if 'SPEC' in sorted_fields:  # Ensure 'SPEC' is not in the list before adding to top
                sorted_fields.remove('SPEC')
            sorted_fields = ['SPEC'] + sorted_fields + include_fields

            if 'Isometric Drawing Area' in sorted_fields:  # Ensure 'BOM' is not in the list before adding to top
                sorted_fields.remove('Isometric Drawing Area')
            sorted_fields = ['Isometric Drawing Area'] + sorted_fields + include_fields

            return sorted_fields
        finally:
            if conn:
                conn.close()

    def get_sorted_fields_and_export(self, exclude_keys=['annotMarkups'], include_fields=[]):
        print("\n\n-----------------------------------Accessed get_sorted_fields_and_export\n\n")

        conn = None
        try:
            conn = self.connect()
            cursor = conn.cursor()

            # Lists to store field names
            fields = []
            fields_dict = {}

            # SQL to get column names excluding primary and foreign keys
            for table_name in ["General", "BOM"]:
                query = f"PRAGMA table_info({table_name})"
                cursor.execute(query)
                columns = cursor.fetchall()
                for column in columns:
                    if column[5] == 0:  # Not a primary key
                        field_name = column[1]
                        if field_name not in exclude_keys:
                            fields.append(field_name)
                            # Assuming a simple display name transformation
                            display_name = field_name.replace('_', ' ').title()
                            fields_dict[display_name] = field_name

            # Sort fields, add 'BOM' at the start, and append any additional fields
            sorted_fields = sorted(set(fields))  # Remove duplicates and sort
            if 'BOM' in sorted_fields:  # Ensure 'BOM' is not in the list before adding to top
                sorted_fields.remove('BOM')
            sorted_fields = ['BOM'] + sorted_fields + include_fields

            # Additional fields not in database tables
            for field in include_fields:
                display_name = field.replace('_', ' ').title()
                fields_dict[display_name] = field

            # Export sorted list and dictionary to a JSON file
            config = {
                'sorted_fields': sorted_fields,
                'fields_mapping': fields_dict
            }
            with open('fields_config.json', 'w') as json_file:
                json.dump(config, json_file, indent=4)

            return sorted_fields
        finally:
            if conn:
                conn.close()

    # Users - Create, Read, Update and Delete

    def create_user(self, email):
        """Insert new user to the database"""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO Users (email) VALUES (?)", (email, ))
            return cursor.lastrowid

    def get_users(self) -> list:
        """Return all user records"""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * from Users')
            return cursor.fetchall()

    def get_user(self, username) -> list:
        """Return user record for username"""
        try:
            statement = "Select * from Users where username=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (username, ))
                return cursor.fetchone()
        except Exception as e:
            print(e)
            return None

    # def get_user_from_email(self, email) -> list:
    #     """Return user record given email address"""
    #     try:
    #         statement = "Select * from Users where email=?"
    #         with self.connect() as conn:
    #             cursor = conn.cursor()
    #             cursor.execute(statement, (email, ))
    #             return cursor.fetchone()
    #     except Exception as e:
    #         print(f"User email {email} does not exist: {e}")
    #         return None

    # def delete_user(self, username):
    #     self.execute("DELETE from Users where username=?", (username, ))

    def update_user(self, oldEmail, newEmail):
        # TODO - need to better define PK for updating users. Also depends on actual login
        """Update user details for given user"""
        sql = "UPDATE Users SET email=? WHERE email=?"
        try:
            with self.connect() as conn:
                cur = conn.cursor()
                cur.execute(sql, (newEmail, oldEmail))
                conn.commit()
        except Exception as e:
            print("TODO: Failed to update user {e}")

    # def update_user_tokens(self, username, tokens: int):
    #     """Update tokens for user"""
    #     sql = "UPDATE Users SET tokens = ? WHERE username = ?"
    #     try:
    #         with self.connect() as conn:
    #             cur = conn.cursor()
    #             cur.execute(sql, (tokens, username))
    #             conn.commit()
    #     except Exception as e:
    #         print("TODO: Failed to update user tokens {e}")

    # Projects - Create, Read, Update and Delete

    # def create_project(self, username, userCompanyName, projectName, projectNumber, projectLocation, clientName, documentVendor, projectScope):
    #     """Important - use username instead of userCompanyId for now as we need composite key"""
    #     return self.insert_new_project(userCompanyName,
    #                                    username,
    #                                    projectName,
    #                                    projectNumber,
    #                                    projectLocation,
    #                                    clientName,
    #                                    documentVendor,
    #                                    projectScope)

    def delete_project(self, projectId):
        """Delete project given projectId"""
        statements = [
            ("BOM", ("DELETE from BOM WHERE id in (SELECT BOM.id from BOM INNER JOIN PDFStorage WHERE BOM.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("GENERAL", ("DELETE from GENERAL WHERE id in (SELECT GENERAL.id from GENERAL INNER JOIN PDFStorage WHERE GENERAL.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("SPEC", ("DELETE from SPEC WHERE id in (SELECT SPEC.id from SPEC INNER JOIN PDFStorage WHERE SPEC.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("SPOOL", ("DELETE from SPOOL WHERE id in (SELECT SPOOL.id from SPOOL INNER JOIN PDFStorage WHERE SPOOL.pdf_id=PDFStorage.id AND PDFStorage.project_id=?)") ),
            ("PDFStorage", "DELETE from PDFStorage WHERE project_id=?"),
            ("ProjectSources", "DELETE from ProjectSources WHERE projectId=?"),
            ("Projects", "DELETE from Projects WHERE id=?"),
        ]
        name = None
        # Delete all project related data
        try:
            with self.connect() as conn:
                cur = conn.cursor()
                for name, statement in statements:
                    cur.execute(statement, (projectId, ))
                conn.commit()
                cur.execute('VACUUM')  # reclaim space
        except Exception as e:
            print(f"Failed to delete project (id:{projectId}), {name}, `{statement}` data {e}")

    def update_project(self, projectId, projectName, projectNumber, projectLocation):
        sql = "UPDATE Projects SET projectName=?, projectNumber=?, projectLocation=? WHERE id=? "
        try:
            with self.connect() as conn:
                cur = conn.cursor()
                cur.execute(sql, (projectName, projectNumber, projectLocation, projectId))
                conn.commit()
        except Exception as e:
            raise e

    def update_projectsource_document_vendor(self, projectId, filename, documentVendor):
        """Update the document vendor / engineer drafter for project"""
        sql = "UPDATE ProjectSources SET documentVendor=? WHERE projectId=? AND filename=?"
        try:
            with self.connect() as conn:
                cur = conn.cursor()
                cur.execute(sql, (documentVendor, projectId, filename))
                conn.commit()
        except Exception as e:
            print("TODO: Failed to project vendor")

    def get_user_projects(self, companyName):
        """Return all projects for the given username"""
        try:
            statement = "Select * from Projects p where p.userCompanyName=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                # cursor.execute(statement, (companyName, ))
                cursor.execute(statement, (companyName, ))
                return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get user projects for {companyName}, {e}")
            return None

    def get_project(self, projectId):
        """Return project for the given project ID"""
        try:
            statement = "Select * from Projects where id=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (projectId, ))
                return cursor.fetchone()
        except Exception as e:
            print(f"Failed to get project given projectId={projectId}: {e}")
            return None

    # Project Sources - Create, Read, Update and Delete

    def create_project_source(self, projectId, filename, documentVendor=None, dateAdded=None, sortNumber=None):
        """Insert source document for given project"""
        try:
            with self.connect() as conn:
                cursor = conn.cursor()
                if not dateAdded:
                    dateAdded = datetime.datetime.now()
                cursor.execute('''INSERT INTO ProjectSources (projectId, filename, documentVendor, dateAdded, sortNumber)
                                VALUES (?, ?, ?, ?, ?)''',
                                (projectId, filename, documentVendor, dateAdded, sortNumber))
                return cursor.lastrowid
        except Exception as e:
            raise Exception(e)
            print(f"Failed to create project source: {e}")

    def get_project_sources(self, projectId):
        """Return list of documents for given project"""
        """Return project for the given project ID"""
        try:
            statement = "Select * from ProjectSources where projectId=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (projectId, ))
                return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get project sources given projectId={projectId}: {e}")
            return None

    def get_project_source(self, projectId, filename):
        """Return single project source"""
        try:
            statement = "Select * from ProjectSources where projectId=? and filename=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (projectId, filename, ))
                return cursor.fetchone()
        except Exception as e:
            print(f"Failed to get project source given projectId={projectId}: {e} and filename={filename}")
            return None

    def delete_project_source(self, projectId, filename):
        """Remove document for given given project"""
        try:
            statement = "Delete from ProjectSources where projectId=? and filename=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (projectId, filename, ))
                return cursor.rowcount
        except Exception as e:
            print(f"Failed to get project sources given projectId={projectId}: {e}")
            return False

    # PDF Storage - Create, Read, Update and Delete

    def create_pdf(self, originalFilename: str, pageNumber: int, document, documentType, documentName: str):
        """Inserts document into PDF storage

        @param document is a BLOB - convert page to bytes
        """
        # try:
        #     with self.connect() as conn:
        #         cursor = conn.cursor()
        #         cursor.execute('''INSERT INTO ProjectSources (projectId, filename, dateAdded, sortNumber)
        #                         VALUES (?, ?, ?, ?)''',
        #                         (projectId, filename, dateAdded, sortNumber))
        #         return cursor.lastrowid
        # except Exception as e:
        #     print(f"Failed to create project source: {e}")
        # Pdfs = self.metadata.tables["Pdfs"]
        # conn = self.engine.connect()
        # query = db.insert(Pdfs).values(originalFilename="ab.pdf",
        #                             pageNumber=pageNumber,
        #                             document=document,
        #                             documentType=documentType,
        #                             documentName=documentName)
        # Result = conn.execute(query)
        # return Result.inserted_primary_key

    # def get_pdfstorage(self) -> list:
    #     return self.fetchall("Select * from PDFS")

    def get_project_pdf_storage(self, projectId):
        """Return list of documents for given project"""
        try:
            start_time = time.time()  # Start timing
            statement = "Select * from PdfStorage where project_id=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (projectId, ))

                end_time = time.time()
                logger.info(f"get_project_pdf_storage took: {end_time - start_time:.2f} seconds")

                return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get project sources given projectId={projectId}: {e}")
            return None

    def get_user_pdf_storage(self, userCompanyName):
        """Return list all pdf storage documents for given user"""
        try:
            start_time = time.time()  # Start timing
            statement = "SELECT pd.id, project_id, pdf_page, documentName, docSize, skipped, userCompanyName FROM PDFStorage pd INNER JOIN projects p on p.userCompanyName=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (userCompanyName, ))
                end_time = time.time()
                logger.info(f"get_user_pdf_storage took: {end_time - start_time:.2f} seconds")
                return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get project sources for userCompanyName={userCompanyName}: {e}")
            return None

    def get_project_source_pdf_storage(self, project_id, filename, output_format="polars"):
        """Returns polars DataFrame of PDFStorage records for given project_id and filename"""
        try:
            from sqlalchemy import create_engine, text
            engine = create_engine(f'sqlite:///{self.db_path}')
            params = {"project_id": project_id, "originalFilename": filename}
            query = text("Select * from PDFStorage where project_id=:project_id and originalFilename=:originalFilename")
            with engine.connect() as conn:
                result = conn.execute(query, params)
                rows = result.fetchall()
                columns = result.keys()
                if output_format == "polars":
                    df = pl.DataFrame(rows, schema=columns)
                    if df.is_empty():
                        return None
                elif output_format == "pandas":
                    df = pd.DataFrame(rows, columns=columns)
                    if df.empty:
                        return None
                else:
                    raise ValueError(f"Invalid output format: {output_format}. Valid formats are 'polars' and 'pandas'.")

            return df
        except Exception as e:
            print(f"Failed to get project source given projectId={project_id}: {e} and filename={filename}")
            return None

    # def update_pdf(self):
    #     raise NotImplementedError

    # def delete_pdf(self):
    #     raise NotImplementedError

    # ROIs - Create, Read, Update and Delete

    def create_roi(self, pdfId, roiName: str, isTable: bool, x: float, y: float, width: float, height: float):
        """Inserts ROI for given PDF"""
        try:
            statement = "Insert into ROI (pdf_id, roiName) VALUES (?, ?, ?, ?, ?)"
            rowid = self.execute(statement, (pdfId, roiName, isTable, x, y, width, height))
            return rowid
        except Exception as e:
            print(e)

    def get_rois(self, pdfId):
        raise NotImplementedError

    def update_roi(self):
        raise NotImplementedError

    def delete_roi(self, roiId):
        """Remove ROI and columns for given ROI"""
        raise NotImplementedError

    # ROICOLUMN - Create, Read, Update and Delete

    def create_roi_column(self, roiId, columnName: str, ratio: float):
        """Inserts ROI for given PDF"""
        statement = "Insert into PDFS (roiId, columnName, ratio) VALUES (?, ?, ?)"
        rowid = self.execute(statement, (roiId, columnName, ratio))
        return rowid

    def get_rois(self, pdfId):
        raise NotImplementedError

    def update_roi(self):
        raise NotImplementedError

    def delete_roi(self, roiId):
        """Remove ROI and columns for given ROI"""
        raise NotImplementedError

    # Other/Temporary utility functions

    def user_exists(self, username):
        user = self.get_user(username)
        return True if user else False

    def email_exists(self, email):
        try:
            statement = "Select * from Users where email=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (email, ))
                return True if cursor.fetchone() else False
        except Exception as e:
            print(f"Failed to login {e}")
            return None

    def get_project_bom_data(self, projectId: int) -> pd.DataFrame:
        """Returns Dataframe BOM data for project"""
        statement = ("SELECT BOM.id, PDFStorage.originalFilename as 'sys_path', PDFStorage.pdf_page, pos, ident, size, material_description, quantity, "
                    + "item, tag, material_code, sch_class, parts, type, weight, number, remarks, ident_code, pdf_id, status from BOM INNER JOIN PDFStorage "
                    + "WHERE BOM.pdf_id=PDFStorage.id AND PDFStorage.project_id=?;")
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(projectId, ))
                logger.debug(f"BOM DF ROWS: {len(df)} ")
                return df
                # return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get BOM data for project given projectId={projectId}: {e}")
            return None

    def get_project_spec_data(self, projectId: int) -> pd.DataFrame:
        """Returns Dataframe SPEC data for project"""
        statement = ("SELECT SPEC.*, PDFStorage.pdf_page "
                    + "from SPEC INNER JOIN PDFStorage "
                    + "WHERE SPEC.pdf_id=PDFStorage.id AND PDFStorage.project_id=?;")
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(projectId, ))
                logger.debug(f"SPEC DF ROWS: {len(df)} ")
                return df
                # return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get SPEC data for project given projectId={projectId}: {e}")
            return None

    def get_project_spool_data(self, projectId: int) -> pd.DataFrame:
        """Returns Dataframe SPOOL data for project"""
        statement = ("SELECT SPOOL.*, PDFStorage.pdf_page "
                    + "from SPOOL INNER JOIN PDFStorage "
                    + "WHERE SPOOL.pdf_id=PDFStorage.id AND PDFStorage.project_id=?;")
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(projectId, ))
                logger.debug(f"SPOOL DF ROWS: {len(df)} ")
                return df
                # return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get SPOOL data for project given projectId={projectId}: {e}")
            return None

    def get_project_outlier_data(self, projectId: int) -> pd.DataFrame:
        """Returns Dataframe BOM data for project"""
        statement = ("SELECT BOM.id, PDFStorage.pdf_page, pos, ident, size, material_description, quantity, "
                    + "item, tag, pdf_id, status from BOM INNER JOIN PDFStorage "
                    + "WHERE BOM.pdf_id=PDFStorage.id AND PDFStorage.project_id=?;")
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(projectId, ))
                return df
                # return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get BOM data for project given projectId={projectId}: {e}")
            return None

    def get_user_general_data(self, projectId: str) -> pd.DataFrame:
        """Returns Dataframe all General data for user"""
        statement = ("SELECT General.*, PDFStorage.pdf_page "
                    + "from General INNER JOIN PDFStorage "
                    + "WHERE General.pdf_id=PDFStorage.id AND PDFStorage.project_id=?;")
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(projectId, ))
                logger.debug(f"General DF ROWS: {len(df)} ")
                return df
                # return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get General data for project given projectId={projectId}: {e}")
            return None

    # def delete_bom(self, id):
    #     self.execute("DELETE from BOM where id=?", (id, ))

    def get_pdf_record(self, pdfId: int):
        """Return pdf record given pdf id"""
        try:
            start_time = time.time()  # Start timing
            statement = "Select * from PdfStorage where id=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (pdfId, ))
                end_time = time.time()
                logger.info(f"get_pdf_blob: {end_time - start_time:.2f} seconds")
                return cursor.fetchone()
        except Exception as e:
            print(f"Failed to get pdf blob given id={pdfId}: {e}")
            return None

    def delete_from_table(self, tableName, id):
        statement = None
        tableName = tableName.lower()
        if tableName == "bom":
            statement = "Delete from BOM where id=?"
        elif tableName == "general":
            statement = "Delete from General where id=?"
        elif tableName == "spec":
            statement = "Delete from SPEC where id=?"
        elif tableName == "spool":
            statement = "Delete from SPOOL where id=?"
        else:
            return False

        try:
            with self.connect() as conn:
                conn.execute(statement, (int(id), ))
                return True
        except Exception as e:
            logger.info(f"Failed to delete from {tableName} by id. May not exist", e)

        return False

    # def get_rfq_record(self, materialDescription: str, size: str):
    #     """Return record from RFQ"""
    #     try:
    #         start_time = time.time()  # Start timing
    #         statement = "Select * from RFQ where material_description=? and size=?"
    #         with self.connect() as conn:
    #             cursor = conn.cursor()
    #             cursor.execute(statement, (materialDescription, size, ))
    #             end_time = time.time()
    #             logger.info(f"get_pdf_blob: {end_time - start_time:.2f} seconds")
    #             return cursor.fetchone()
    #     except Exception as e:
    #         print(f"Failed to get rfq record={materialDescription}, {size}: {e}")
    #         return None

    def get_latest_rfq_record_by_material(self, materialDescription: str) -> pd.DataFrame:
        """Returns latest DataFrame RFQ data for given material description
            - Added on 3-23-25 to replace get_rfq_record. Size is not needed for a match
        """
        statement = """
            SELECT * FROM RFQ
            WHERE material_description=?
            ORDER BY last_updated IS NULL ASC, last_updated DESC
            LIMIT 1
        """
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(materialDescription,))
                logger.debug(f"Latest RFQ record for {materialDescription}: {len(df)} rows")
                return df
        except Exception as e:
            logger.error(f"Failed to get latest rfq record for material={materialDescription}: {e}")
            return pd.DataFrame()  # Return empty DataFrame instead of None

    def delete_rfq_record(self, materialDescription: str, size: str):
        """Return record from RFQ"""
        try:
            statement = f"Delete from RFQ where material_description=? and size=?"
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute(statement, (materialDescription, size, ))
                return cursor.rowcount
        except Exception as e:
            print(f"Failed to delete rfq record={materialDescription}, {size}: {e}")
            return False

        return False

    def get_rfq_record(self, materialDescription: str, size: str) -> pd.DataFrame:
        """Returns Dataframe BOM data for project"""
        statement = "Select * from RFQ where material_description=? and size=?"
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn, params=(materialDescription, size, ))
                logger.debug(f"RFQ record ROWS: {len(df)} ")
                return df
        except Exception as e:
            print(f"Failed to get rfq record={materialDescription}, {size}: {e}")
            return None

    def get_rfq_data(self) -> pd.DataFrame:
        """Returns Dataframe RFQ data for project"""
        statement = "Select * from RFQ"
        try:
            with self.connect() as conn:
                df = pd.read_sql_query(statement, conn)
                logger.debug(f"RFQ Data ROWS: {len(df)} ")
                return df
        except Exception as e:
            print(f"Failed to get rfq data: {e}")
            return None

    def delete_all_from_table(self, tableName):
        statement = None
        tableName = tableName.lower()
        if tableName == "bom":
            statement = "Delete from BOM"
        elif tableName == "general":
            statement = "Delete from General"
        elif tableName == "spec":
            statement = "Delete from SPEC"
        elif tableName == "spool":
            statement = "Delete from SPOOL"
        else:
            return False
        try:
            with self.connect() as conn:
                conn.execute(statement)
                return True
        except Exception as e:
            logger.info(f"Failed to delete all rows from {tableName}. May not exist", e)

        return False

    # def get_raw_data(self, projectId: int, filename: str):
    #     try:
    #         statement = "Select * from RawData r where r.pdf_page=? and r.sys_path=?"
    #         with self.connect() as conn:
    #             cursor = conn.cursor()
    #             cursor.execute(statement, (projectId, filename))
    #             return cursor.fetchall()
    #     except Exception as e:
    #         print(f"Failed to get raw data for projectId: {projectId} - {filename}, {e}")
    #         return None

    def get_raw_data(self, pdf_ids: list[int]):
        """
        Returns RawData given PDF IDs

        """
        raise Exception("This is unused.")
        # try:
        #     placeholders = ",".join("?" * (len(pdf_ids)))
        #     statement = "SELECT * FROM RawData r WHERE r.pdf_id IN (%s)" % placeholders
        #     params = [str(pdf_id) for pdf_id in pdf_ids]
        #     with self.connect() as conn:
        #         df = pd.read_sql_query(statement, conn, params=params)
        #         df["coordinates"].apply(extract_coordinates)
        #         df["coordinates2"].apply(extract_coordinates)
        #         return df
        # except Exception as e:
        #     print(f"Failed to get raw data for projectId: {pdf_ids}, {e}")
        #     return None

    def insert_raw_data(self, raw_df: pd.DataFrame, replace_existing: bool = True):
        """
        Args:
            raw_df: DataFrame to be inserted into database
            replace_existing: If True, drop existing RawData records given pdf_id
        """
        if replace_existing:
            pdf_ids = raw_df["pdf_id"].unique().tolist()
            self.delete_raw_data(pdf_ids)

        # Clean raw data columns
        engine = create_engine(f'sqlite:///{self.db_path}')
        raw_df = self.convert_data_types_and_filter_columns(raw_df, "RawData", engine)
        with self.connect() as conn:
            raw_df.to_sql("RawData", conn, if_exists="append", index=False)

    def delete_source_raw_data(self, project_id: int, sys_path: str, pages: list[int] = None):
        """Removes RawData records for a given ProjectSource record

        Args:
            project_id: Project Id of record
            sys_path: Filename of document
            pages: Optional list of pdf_page numbers to remove. If None, removes all records for ProjectSource
        """
        with self.connect() as conn:
            cursor = conn.cursor()
            if not pages:
                params = [project_id, sys_path]
                cursor.execute("DELETE FROM RawData WHERE pdf_id=?", params)
                logger.info(f"Removed existing RawData records {cursor.rowcount}")
            else:
                unique_pages = set(pages)
                unique_pages = [str(pdf_id) for pdf_id in unique_pages]
                placeholders = ",".join("?" * len(unique_pages))
                params = [project_id, sys_path] + unique_pages
                cursor.execute("DELETE FROM RawData WHERE project_id=? AND sys_path=? WHERE pdf_id IN (%s)" % placeholders, params)
                logger.info(f"Removed existing RawData records {cursor.rowcount}")

    def delete_raw_data(self, pdf_ids: list[int] = None):
        """Removes RawData records for a given pdf_ids"""

        with self.connect() as conn:
            cursor = conn.cursor()
            if not pdf_ids:
                cursor.execute("DELETE FROM RawData WHERE pdf_id=?", params)
                logger.info(f"Removed existing RawData records {cursor.rowcount}")
            else:
                unique_pdf_ids = set(pdf_ids)
                unique_pdf_ids = [str(pdf_id) for pdf_id in unique_pdf_ids]
                placeholders = ",".join("?" * len(unique_pdf_ids))
                params = unique_pdf_ids
                cursor.execute("DELETE FROM RawData WHERE pdf_id IN (%s)" % placeholders, params)
                logger.info(f"Removed existing RawData records {cursor.rowcount}")

    def get_source_pdf_id_map(self, projectId: int, filename: str, pages: list[int] = None) -> dict:
        """Returns a dictionary of {pdf_page: pdf_id} given a ProjectSource record

        With potential duplicate records, return the lowest pdf_id that is mapped to a page number
        """
        result = {}
        with self.connect() as conn:
            # if not pages:
            statement = """SELECT id, pdf_page FROM PDFStorage WHERE project_id=? AND originalFilename=? ORDER BY pdf_page, id ASC"""
            df = pd.read_sql_query(statement, conn, params=(projectId, filename, ))
            # else:
            #     placeholders = ",".join("?" * len(pages))
            #     statement = """SELECT id, pdf_page FROM PDFStorage WHERE project_id=? AND originalFilename=? AND pdf_page IN (%s) ORDER BY pdf_page, id ASC""" % placeholders
            #     pages = [str(pdf_id) for pdf_id in pages]
            #     params = [projectId, filename] + pages
            #     df = pd.read_sql_query(statement, conn, params=params)
            for r in df.itertuples():
                if pages and not r.pdf_page in pages:
                    continue
                result.setdefault(r.pdf_page, r.id)
        return result

    def get_pdf_storage_id(self, project_id, pdf_path, pdf_page):
        """Return the smallest PDFStorage pdf_id linked to a ProjeectSource record"""
        with self.connect() as conn:
            cursor = conn.cursor()
            cursor.execute('''SELECT id FROM PDFStorage WHERE project_id=? AND originalFilename=? AND pdf_page=? ORDER BY pdf_page ASC''',
                            (project_id, pdf_path, pdf_page))
            result = cursor.fetchall()
            if len(result) > 0:
                return result[0][0]

        return None

