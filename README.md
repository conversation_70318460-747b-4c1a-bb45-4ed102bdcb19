# Architekt-ATEM

<img align="left" src="resources/images/icon.ico?raw=true"/>

#
# Screenshots
<img align="center" src="docs/resources/images/screen.png?raw=true"/>

# Downloads
https://github.com/cmccall95/Architekt-ATOM/releases

# Overview

ATEM (Agile Takeoff & Estimation Model) is a specialized application designed for construction and engineering professionals to extract, analyze, and organize data from PDF blueprints and technical documents. The application uses PyMuPDF/fitz for PDF processing, PySide6 for the GUI, and PostgreSQL for database management.

# Project Environment Setup

**Requirements**: Python >= 3.7 and < 3.11 for PyMuPDF compatibility.
**Python 3.10 Recommended for compatibility**

## Creating Virtual Environment

### Command Prompt
```cmd
# Create virtual environment with Python 3.9 (recommended)
py -3.10 -m venv venv

# Activate virtual environment
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

# Manually install boto3 and boto3[crt]
```cmd
pip install boto3
pip install "boto3[crt]"
```

### PowerShell
```powershell
# Create virtual environment with Python 3.9 (recommended)
py -3.10 -m venv venv

# Activate virtual environment
venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt
```
# Manually install boto3 and boto3[crt]
```powershell
pip install boto3
pip install "boto3[crt]"
```

**Note**: If you encounter execution policy issues in PowerShell, you may need to run:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## Verifying Installation
After activation, verify your Python version:
```cmd
python --version
```

# Features

## Project Management
ATEM provides comprehensive project management capabilities, allowing users to organize projects by client and track project-specific information such as location, bid revision, and engineering details.

## ROI Extraction
The application enables users to define Regions of Interest (ROIs) on PDF documents and extract structured data from these regions for further processing.

### ROI Extraction Workflow

The ROI extraction process follows a specific workflow through multiple components of the application. Understanding this flow is crucial for developers working on extraction-related features.

#### 1. User Interface Trigger
**File**: `src/views/blueprintreaderview.py`
- **Extract Button**: Line 3730-3734, connects to `onExtractionClicked()`
- **onExtractionClicked()**: Lines 5119-5251
  - Validates PDF path and preprocessing status
  - Checks for overlapping ROIs and required BOM tables
  - Converts ROI state to payload format
  - Emits `extracted` signal with payload data

#### 2. Signal Connection & Message Dispatch
**File**: `src/views/projectview.py`
- **Signal Connection**: Line 190, connects `extracted` signal to `onExtraction()`
- **onExtraction()**: Lines 192-197, calls `confirmExtraction()`
- **confirmExtraction()**: Lines 199-246
  - Shows confirmation dialog to user
  - Dispatches `pub.sendMessage("request-roi-extraction-job", ...)` (Line 229)

#### 3. Message Handler & Job Creation
**File**: `src/config/workspace.py`
- **Message Subscription**: Line 695, `pub.subscribe(self.onRequestRoiExtraction, "request-roi-extraction-job")`
- **onRequestRoiExtraction()**: Lines 993-1047
  - Validates token availability and PDF accessibility
  - Requests job from cloud service
  - Creates `RoiExtractionJob` via `self._queueManager.addRoiExtractionJob()` (Line 1044)

#### 4. Job Queue Management
**File**: `src/config/workspace.py`
- **QueueManager.addRoiExtractionJob()**: Lines 548-554
- **RoiExtractionJob Constructor**: Lines 388-421
  - **Debug Page Limiting**: Lines 401-409 (NEW)
    - Imports `IS_TESTING` and `OVERRIDE_PAGE_LIMIT` from `src/atom/_worker.py`
    - If `IS_TESTING=True`, limits extraction to first N pages
    - Creates `pages=[1,2,3,4,5]` list for `OVERRIDE_PAGE_LIMIT=5`
  - Creates `RoiExtraction` processor instance with debug page limit

#### 5. Core Extraction Processing
**File**: `src/atom/roiextraction.py`
- **RoiExtraction Constructor**: Lines 2233-2280
  - Accepts `pages` parameter to limit which pages to process
  - If `pages=None`, processes all pages in document
  - If `pages=[1,2,3,4,5]`, only processes those specific pages
- **Page Processing**: Lines 2361-2371
  - Converts 1-based page numbers to 0-based for internal processing
  - Filters pages based on ROI payload and document bounds
  - Sets `self._extract_pages` for actual processing

#### 6. Table Data Extraction
**File**: `unit_tests/get_tables_test.py`
- **get_table_data()**: Line 777, main function for extracting table data from ROIs
- Called by `RoiExtraction` for each page being processed
- Extracts structured data from defined ROI regions

#### 7. Legacy Worker System (Fallback)
**File**: `src/atom/_worker.py`
- Contains older extraction system with debug variables:
  - `IS_TESTING = True` (Line 75)
  - `OVERRIDE_PAGE_LIMIT = 5` (Line 77)
- **run_extraction()**: Lines 2146-2187, implements page limiting logic
- Still used as reference for debug settings in newer system

### Debug Configuration

To limit extraction for debugging purposes:

1. **Set Debug Variables** in `src/atom/_worker.py`:
   ```python
   IS_TESTING = True
   OVERRIDE_PAGE_LIMIT = 5  # Process only first 5 pages
   ```

2. **How It Works**:
   - `RoiExtractionJob` imports these variables
   - Creates `pages=[1,2,3,4,5]` list when `IS_TESTING=True`
   - Passes to `RoiExtraction` constructor
   - Only specified pages are processed instead of entire document

### Key Files Summary

| File | Purpose | Key Functions |
|------|---------|---------------|
| `src/views/blueprintreaderview.py` | UI trigger | `onExtractionClicked()` |
| `src/views/projectview.py` | Signal handling | `onExtraction()`, `confirmExtraction()` |
| `src/config/workspace.py` | Job management | `onRequestRoiExtraction()`, `RoiExtractionJob` |
| `src/atom/roiextraction.py` | Core extraction | `RoiExtraction` class |
| `unit_tests/get_tables_test.py` | Table extraction | `get_table_data()` |
| `src/atom/_worker.py` | Debug settings | `IS_TESTING`, `OVERRIDE_PAGE_LIMIT` |

### Message Flow

```
Extract Button Click → extracted Signal → onExtraction() →
pub.sendMessage("request-roi-extraction-job") → onRequestRoiExtraction() →
RoiExtractionJob → RoiExtraction → get_table_data() → Results
```

## Build RFQ
ATEM streamlines the Request for Quote (RFQ) process with sophisticated data processing capabilities:

### PostgreSQL Database Integration
The application uses a PostgreSQL database for robust data management with the following key components:

#### Tables Structure
- **Clients & Projects**: Store client and project information
- **Client Profiles**: Define calculation methods and standards
- **RFQ Input**: Master reference for material specifications
- **RFQ**: Project-specific material entries with calculated values
- **Component Mapping**: Governs categorization of RFQ items
- **Equivalent Length Factors**: Defines multipliers for calculations
- **Lookup Tables**: Stores standardized fitting data (standard fittings, reducers, elbows, tees)

#### Advanced Calculation Engine
The RFQ module features a sophisticated calculation engine for equivalent lengths and surface areas:

1. **Multi-Method Approach**:
   - **Lookup Method**: Uses predefined values from lookup tables
   - **Factor Method**: Uses standardized multiplier factors

2. **Intelligent Category Selection**:
   - Uses fitting_category for Fittings scope items
   - Uses general_category for all other categories
   - Includes safety checks for NULL values

3. **Smart Size Matching Algorithm**:
   - **Exact Match**: First attempts to find exact size matches including compound sizes in any order
   - **Size Order Flexibility**: Handles size reversals (treats size1=4, size2=2 the same as size1=2, size2=4)
   - **Larger Size Fallback**: For compound fittings without exact matches, falls back to using the larger size
   - **Nearest Size Approximation**: Uses sophisticated approximation with preference for rounding up
   - **Edge Case Handling**: Implements specific rules for sizes outside the available range

4. **Data Synchronization**:
   - Bidirectional sync between RFQ and RFQ_INPUT tables
   - Automatic category updates based on component mapping
   - Profile synchronization based on project settings

5. **Trigger-Based Updates**:
   - Automatic recalculation on relevant field changes
   - Propagation of mapping changes throughout the system
   - Profile and client information synchronization

## Lookup 
The application provides comprehensive lookup capabilities for standardized components and materials, supporting both imperial and metric units.

# Documentation

For detailed PostgreSQL database documentation, see `src/atom/pg_database/README.pgDatabase.md`.

# Development
ATEM is built using:
- Python 3.x
- PySide6 for GUI
- PyMuPDF/fitz for PDF processing
- PostgreSQL for database management
- pandas for data manipulation

Architekt data path Windows %LocalAppData%\Architekt

# Building Executable

Try one of the variants (replace 'python3' with your own python.exe i.e. from the virtual environment)

Build Specs 

- atem_release.spec
- atem_debug.spec (console)


Note - make sure to install requirements. Replace [.spec file] with chosen build e.g. atem_release.spec

- python3 -m PyInstaller -F app.py -n [.spec file]
- pyinstaller atem.spec

Windows
- python -m PyInstaller [.spec file]

Windows (Virtual Environment)

	1. Navigate to your virtual environment in your project directory

        `cd C:\Users\<USER>\Architekt ATOM\vir_env`


    2. Activate the environment:
    
        `Scripts\activate`


    3. Navigate back to project directory
        
        `cd C:\Users\<USER>\Architekt ATOM`

    4. Run installer

        `python build_atem.py`
    
    5. Follow the prompts in the console

    6. Run the exe in the created 'dist' folder




This should create a build and dist folder. Executable is in the dist folder.


# Installer
TODO