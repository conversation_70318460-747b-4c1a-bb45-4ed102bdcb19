CHECK_FOR_UPDATES = False
TABLE_CHECKBOX_ENABLED = False
GET_WELD_COUNT_ENABLED = True
MANUAL_EXTRACTION_RESULTS = False # Change this to 'True' allow manual uploading (BOM/Gneral as .xlsx). Set filenames in workspace.py
EXPORT_WITH_FIELDMAP = True
SINGLE_INSTANCE = True
GROUPED_ROI_UI = True
ROI_EXTRACTION_MULTIPROCESS = True # Multiprocessing enabled for worker extraction
LOG_PATH = True
ATEM_USE_NEW_EXTRACTION = False # Whether in-app extraction uses new polars extraction method

# Tolerance for font size outlier. Ex. to distinguish triangle revisions
# which can be same color as common text
LEGACY_EXTRACTION_FONT_SIZE_TOLERANCE = 1.5 # last value 0.5
LEGACY_EXTRACTION_REMOVE_OUTLIERS = True

# Overrides
try:
    if __file__.endswith(".pyc"):
        import __features2__
        TABLE_CHECKBOX_ENABLED = __features2__.TABLE_CHECKBOX_ENABLED
        CHECK_FOR_UPDATES = __features2__.CHECK_FOR_UPDATES
        GET_WELD_COUNT_ENABLED = __features2__.GET_WELD_COUNT_ENABLED
        # Do not change these
        MANUAL_EXTRACTION_RESULTS = False
        EXPORT_WITH_FIELDMAP = True
        SINGLE_INSTANCE = True
        GROUPED_ROI_UI = False
        LOG_PATH = False
        EXTRACTION_MULTIPROCESSING = True
        ATEM_USE_NEW_EXTRACTION = True
except Exception:
    # Dev mode
    pass