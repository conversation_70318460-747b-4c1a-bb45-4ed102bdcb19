"""
RFQ Template Creation Script
============================

Creates RFQ (Request for Quotation) templates with data validation using xlsxwriter.

Creates the exact 47 columns specified and adds dropdown validation only where
categorization_table provides options.
"""
import os
import pandas as pd
import xlsxwriter
from datetime import datetime
import sys

# Import categorization table from prompts.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from prompts import categorization_table
    print(f"Successfully imported categorization_table with {len(categorization_table)} entries")
except ImportError:
    # Fallback if prompts.py not available
    categorization_table = []
    print("Failed to import categorization_table")

# Define the exact column order as specified
RFQ_COLUMNS = [
    "material_description", "size", "size1", "size2", "quantity", "component_category",
    "rfq_scope", "general_category", "unit_of_measure", "material", "abbreviated_material",
    "technical_standard", "astm", "grade", "rating", "schedule", "coating", "forging", "ends",
    "item_tag", "tie_point", "pipe_category", "valve_type", "fitting_category",
    "weld_category", "bolt_category", "gasket_category", "ef", "sf", "last_updated",
    "__checked__", "__checked_2__", "exists_in_verified_materials", "option_review",
    "invalid_options", "Review", "Category Check", "Rating Check", "SCH CHECK",
    "ASTM Check", "Grade Check", "Tag Check", "normalized_description", "metadata_tags",
    "review_tags", "match_type", "mto_id"
]

def get_validation_options():
    """
    Get validation options for fields from categorization_table.

    Returns:
        dict: Dictionary with field names as keys and their validation options as values
    """
    validation_options = {}

    # Process categorization table to extract validation options
    for field_info in categorization_table:
        field_name = field_info.get("field", "")
        options = field_info.get("options", [])

        if field_name and len(options) > 0:
            validation_options[field_name] = options
    return validation_options

def create_rfq_template(output_path: str, enable_validation: bool = True,
                       custom_validation: dict = None, max_rows: int = 1000):
    """
    Create an RFQ template Excel file using xlsxwriter with range-based validation.

    Args:
        output_path: Path where the Excel template will be saved
        enable_validation: Whether to add dropdown validation to applicable fields
        custom_validation: Optional dict to override validation lists for specific fields
        max_rows: Maximum number of data rows to prepare (affects validation range)
    """
    # Get validation options
    validation_options = get_validation_options()

    # Apply custom validation overrides if provided
    if custom_validation:
        validation_options.update(custom_validation)

    # Create workbook and worksheets
    workbook = xlsxwriter.Workbook(output_path)
    worksheet = workbook.add_worksheet('RFQ Template')
    validation_sheet = workbook.add_worksheet('Validation Lists')

    # Hide the validation sheet
    validation_sheet.hide()

    # Write validation lists to the validation sheet
    validation_ranges = {}
    current_col = 0

    for field_name, options in validation_options.items():
        if options:
            # Sort options alphabetically
            sorted_options = sorted(options)

            # Write field name as header
            validation_sheet.write(0, current_col, field_name)

            # Write sorted options starting from row 1
            for row_idx, option in enumerate(sorted_options, start=1):
                validation_sheet.write(row_idx, current_col, option)

            # Create range reference for this field with absolute references
            end_row = len(sorted_options)
            range_ref = f"'Validation Lists'!${xlsxwriter.utility.xl_col_to_name(current_col)}$2:${xlsxwriter.utility.xl_col_to_name(current_col)}${end_row + 1}"
            validation_ranges[field_name] = range_ref

            current_col += 1

    # Define column widths and formatting
    column_widths = {
        'material_description': 40,
        'normalized_description': 40,
        'metadata_tags': 25,
        'review_tags': 25,
        'match_type': 25
    }

    wrap_columns = ['material_description', 'normalized_description']

    # Create cell format for text wrapping
    wrap_format = workbook.add_format({
        'text_wrap': True,
        'valign': 'top',
        'align': 'left'
    })

    # Create cell format for regular cells
    regular_format = workbook.add_format({
        'valign': 'top',
        'align': 'left'
    })

    # Write headers to main sheet
    for col_idx, column_name in enumerate(RFQ_COLUMNS):
        worksheet.write(0, col_idx, column_name)

        # Add data validation if enabled and options exist
        if enable_validation and column_name in validation_ranges:
            validation_range = f"{xlsxwriter.utility.xl_col_to_name(col_idx)}2:{xlsxwriter.utility.xl_col_to_name(col_idx)}{max_rows+1}"
            range_ref = validation_ranges[column_name]

            worksheet.data_validation(validation_range, {
                'validate': 'list',
                'source': range_ref,
                'show_error': False
            })

    # Create table object with explicit column headers
    table_range = f"A1:{xlsxwriter.utility.xl_col_to_name(len(RFQ_COLUMNS)-1)}{max_rows+1}"

    # Create columns list for table
    table_columns = []
    for col_name in RFQ_COLUMNS:
        table_columns.append({'header': col_name})

    worksheet.add_table(table_range, {
        'name': 'RFQ_Template_Table',
        'style': 'Table Style Medium 2',
        'columns': table_columns,
        'first_column': False,
        'last_column': False,
        'banded_rows': True,
        'banded_columns': False
    })

    # Autofit all columns LAST (after table and validation)
    worksheet.autofit()

    # Then override ONLY the specific columns with fixed widths
    for col_idx, column_name in enumerate(RFQ_COLUMNS):
        if column_name in wrap_columns:
            # Text wrapping columns with fixed width
            worksheet.set_column(col_idx, col_idx, column_widths[column_name], wrap_format)
        elif column_name in column_widths:
            # Fixed width columns without wrapping
            worksheet.set_column(col_idx, col_idx, column_widths[column_name], regular_format)

    # Close workbook
    workbook.close()

    print(f"RFQ template created successfully: {output_path}")
    print(f"Template includes {len(RFQ_COLUMNS)} columns")
    print(f"Table object created with range {table_range}")
    if enable_validation:
        validation_count = len(validation_ranges)
        print(f"Validation enabled for {validation_count} fields using range references")

    return validation_options

def load_data_to_template(df: pd.DataFrame, output_path: str, enable_validation: bool = True,
                         custom_validation: dict = None):
    """
    Load DataFrame data into an RFQ template with range-based validation.

    Args:
        df: DataFrame containing the data to load
        output_path: Path where the Excel file will be saved
        enable_validation: Whether to add dropdown validation to applicable fields
        custom_validation: Optional dict to override validation lists for specific fields
    """
    # Ensure DataFrame has all required columns in correct order
    df_columns = list(df.columns)
    missing_columns = [col for col in RFQ_COLUMNS if col not in df_columns]

    # Add missing columns with empty values
    for col in missing_columns:
        df[col] = ""

    # Reorder columns to match RFQ_COLUMNS
    df = df[RFQ_COLUMNS]

    # Get validation options
    validation_options = get_validation_options()

    # Apply custom validation overrides if provided
    if custom_validation:
        validation_options.update(custom_validation)

    # Create workbook and worksheets
    workbook = xlsxwriter.Workbook(output_path)
    worksheet = workbook.add_worksheet('RFQ Data')
    validation_sheet = workbook.add_worksheet('Validation Lists')

    # Hide the validation sheet
    validation_sheet.hide()

    # Write validation lists to the validation sheet
    validation_ranges = {}
    current_col = 0

    for field_name, options in validation_options.items():
        if options:
            # Sort options alphabetically
            sorted_options = sorted(options)

            # Write field name as header
            validation_sheet.write(0, current_col, field_name)

            # Write sorted options starting from row 1
            for row_idx, option in enumerate(sorted_options, start=1):
                validation_sheet.write(row_idx, current_col, option)

            # Create range reference for this field with absolute references
            end_row = len(sorted_options)
            range_ref = f"'Validation Lists'!${xlsxwriter.utility.xl_col_to_name(current_col)}$2:${xlsxwriter.utility.xl_col_to_name(current_col)}${end_row + 1}"
            validation_ranges[field_name] = range_ref

            current_col += 1

    # Define column widths and formatting
    column_widths = {
        'material_description': 40,
        'normalized_description': 40,
        'metadata_tags': 25,
        'review_tags': 25,
        'match_type': 25
    }

    wrap_columns = ['material_description', 'normalized_description']

    # Create cell format for text wrapping
    wrap_format = workbook.add_format({
        'text_wrap': True,
        'valign': 'top',
        'align': 'left'
    })

    # Create cell format for regular cells
    regular_format = workbook.add_format({
        'valign': 'top',
        'align': 'left'
    })

    # Write headers to main sheet
    for col_idx, column_name in enumerate(RFQ_COLUMNS):
        worksheet.write(0, col_idx, column_name)

        # Add data validation if enabled and options exist
        if enable_validation and column_name in validation_ranges:
            max_row = len(df) + 100  # Add buffer for new entries
            validation_range = f"{xlsxwriter.utility.xl_col_to_name(col_idx)}2:{xlsxwriter.utility.xl_col_to_name(col_idx)}{max_row+1}"
            range_ref = validation_ranges[column_name]

            worksheet.data_validation(validation_range, {
                'validate': 'list',
                'source': range_ref,
                'show_error': False
            })

    # Write data rows
    for row_idx, (_, row) in enumerate(df.iterrows(), start=1):
        for col_idx, column_name in enumerate(RFQ_COLUMNS):
            value = row[column_name]
            # Handle pandas Series comparison issue and NaN values
            try:
                if pd.isna(value) or value == "" or value is None:
                    worksheet.write(row_idx, col_idx, "")
                else:
                    # Convert to string but handle special cases
                    if isinstance(value, (int, float)):
                        if pd.isna(value):
                            worksheet.write(row_idx, col_idx, "")
                        else:
                            worksheet.write(row_idx, col_idx, value)
                    else:
                        worksheet.write(row_idx, col_idx, str(value))
            except Exception as e:
                # Fallback for any conversion issues
                worksheet.write(row_idx, col_idx, str(value) if value is not None else "")

    # Create table object with explicit column headers
    table_end_row = len(df) + 1  # +1 for header row
    table_range = f"A1:{xlsxwriter.utility.xl_col_to_name(len(RFQ_COLUMNS)-1)}{table_end_row}"

    # Create columns list for table
    table_columns = []
    for col_name in RFQ_COLUMNS:
        table_columns.append({'header': col_name})

    worksheet.add_table(table_range, {
        'name': 'RFQ_Data_Table',
        'style': 'Table Style Medium 2',
        'columns': table_columns,
        'first_column': False,
        'last_column': False,
        'banded_rows': True,
        'banded_columns': False
    })

    # Autofit all columns LAST (after data, validation, and table)
    worksheet.autofit()

    # Then override ONLY the specific columns with fixed widths
    for col_idx, column_name in enumerate(RFQ_COLUMNS):
        if column_name in wrap_columns:
            # Text wrapping columns with fixed width
            worksheet.set_column(col_idx, col_idx, column_widths[column_name], wrap_format)
        elif column_name in column_widths:
            # Fixed width columns without wrapping
            worksheet.set_column(col_idx, col_idx, column_widths[column_name], regular_format)

    # Close workbook
    workbook.close()

    print(f"RFQ template with data created successfully: {output_path}")
    print(f"Loaded {len(df)} rows with {len(RFQ_COLUMNS)} columns")
    print(f"Table object created with range {table_range}")
    if enable_validation:
        validation_count = len(validation_ranges)
        print(f"Validation enabled for {validation_count} fields using range references")

    return validation_options

def create_sample_data(num_rows: int = 10) -> pd.DataFrame:
    """
    Create sample data for testing the RFQ template.

    Args:
        num_rows: Number of sample rows to create

    Returns:
        DataFrame with sample RFQ data
    """
    import random

    # Sample data for testing
    sample_descriptions = [
        "2\" SCH 40 CS PIPE A106 GR B SMLS BE",
        "4\" 150# WN FLANGE A105 RF",
        "2\" 90 LR ELBOW A234 WPB SCH 40 BE",
        "1/2\" BALL VALVE 800# A105 SW",
        "M16 X 50 HEX BOLT A193 B7",
        "2\" SPIRAL WOUND GASKET 150# 304SS",
        "6\" REDUCER 8\" X 6\" A234 WPB SCH 40",
        "3\" GATE VALVE 300# A216 WCB FLG",
        "1\" COUPLING A105 3000# SW",
        "4\" BLIND FLANGE 300# A105 RF"
    ]

    sample_materials = ["Steel, Carbon", "Steel, Stainless", "Bronze/Brass", "Cast Iron"]
    sample_scopes = ["Pipe", "Fittings", "Flanges", "Valves", "Bolts", "Gaskets"]
    sample_schedules = ["40", "80", "STD", "XH", "160"]
    sample_ratings = ["150", "300", "600", "800", "1500"]

    data = []
    for i in range(num_rows):
        row = {
            "material_description": random.choice(sample_descriptions),
            "size": f"{random.choice([1, 2, 3, 4, 6, 8])}\"",
            "size1": random.choice([1.0, 2.0, 3.0, 4.0, 6.0, 8.0]),
            "size2": random.choice([1.0, 2.0, 3.0, 4.0]) if random.random() > 0.7 else "",
            "quantity": random.randint(1, 50),
            "rfq_scope": random.choice(sample_scopes),
            "material": random.choice(sample_materials),
            "schedule": random.choice(sample_schedules) if random.random() > 0.3 else "",
            "rating": random.choice(sample_ratings) if random.random() > 0.5 else "",
            "last_updated": datetime.now(),
            "Review": random.choice([True, False]),
            "mto_id": f"MTO_{i+1:03d}"
        }

        # Fill remaining columns with empty values
        for col in RFQ_COLUMNS:
            if col not in row:
                row[col] = ""

        data.append(row)

    return pd.DataFrame(data)

if __name__ == '__main__':
    # Configuration flags
    create_template = False # Creates a blank template
    load_sample_data = False  # Disabled since we're loading from Excel
    load_from_excel = True    # New flag for loading from existing Excel
    enable_validation = True
    deduplicate_materials = True  # Remove duplicate material_descriptions (case-insensitive)

    # Excel file path to load data from
    excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 054\Workspace\RFQ - Stage3 - Normalized.xlsx"

    # Output paths - save to designated output folder outside repo
    output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 054\Workspace"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    template_path = os.path.join(output_dir, f"RFQ_Classify_{timestamp}.xlsx")
    data_path = os.path.join(output_dir, f"RFQ_Classify_Data_{timestamp}.xlsx")

    # Create empty template
    if create_template:
        print("Creating RFQ template...")
        try:
            validation_options = create_rfq_template(
                output_path=template_path,
                enable_validation=enable_validation,
                max_rows=1000
            )
            print(f"Template saved to: {os.path.abspath(template_path)}")

            # Print summary of validation fields
            if enable_validation:
                print(f"\nValidation fields:")
                for field in list(validation_options.keys())[:10]:  # Show first 10
                    options_count = len(validation_options[field])
                    print(f"  - {field}: {options_count} options")
                if len(validation_options) > 10:
                    print(f"  ... and {len(validation_options) - 10} more fields")

        except Exception as e:
            print(f"Error creating template: {e}")
            import traceback
            traceback.print_exc()

    # Create template with data from Excel file
    if load_from_excel:
        print(f"\nLoading data from Excel file: {excel_file_path}")
        try:
            # Check if file exists
            if not os.path.exists(excel_file_path):
                print(f"Error: Excel file not found at {excel_file_path}")
            else:
                # Load data from Excel file
                print("Reading Excel file...")
                df = pd.read_excel(excel_file_path)
                print(f"Successfully loaded {len(df)} rows from Excel file")

                # Rename 'ansme_ansi' column to 'technical_standard' if it exists
                if 'ansme_ansi' in df.columns:
                    print("Renaming 'ansme_ansi' column to 'technical_standard'...")
                    df = df.rename(columns={'ansme_ansi': 'technical_standard'})
                    print("Column renamed successfully.")

                # Deduplicate material descriptions if enabled
                if deduplicate_materials and 'material_description' in df.columns:
                    original_count = len(df)
                    print(f"Deduplicating material descriptions (case-insensitive)...")

                    # Convert material_description to lowercase for case-insensitive comparison
                    # but keep the original values in the dataframe
                    df['material_description_lower'] = df['material_description'].astype(str).str.lower()

                    # Deduplicate based on the lowercase column
                    df = df.drop_duplicates(subset=['material_description_lower'])

                    # Remove the temporary column
                    df = df.drop(columns=['material_description_lower'])

                    deduplicated_count = len(df)
                    removed_count = original_count - deduplicated_count
                    print(f"Deduplication complete: {removed_count} duplicate rows removed ({original_count} -> {deduplicated_count} rows)")

                # Print column information from loaded file
                print(f"Columns in Excel file ({len(df.columns)}):")
                for i, col in enumerate(df.columns[:10], 1):
                    print(f"  {i:2d}. {col}")
                if len(df.columns) > 10:
                    print(f"  ... and {len(df.columns) - 10} more columns")

                # Load data into template
                validation_options = load_data_to_template(
                    df=df,
                    output_path=data_path,
                    enable_validation=enable_validation
                )
                print(f"Template with Excel data saved to: {os.path.abspath(data_path)}")

                # Print data summary
                print(f"\nExcel data summary:")
                print(f"  - Total rows: {len(df)}")
                print(f"  - Total columns: {len(RFQ_COLUMNS)}")
                if enable_validation:
                    print(f"  - Validation fields: {len(validation_options)}")

                # Show sample of loaded data
                if 'material_description' in df.columns:
                    print(f"\nFirst 3 material descriptions:")
                    for i, desc in enumerate(df['material_description'].head(3)):
                        print(f"  {i+1}. {desc}")
                else:
                    print(f"\nFirst few rows of data:")
                    print(df.head(3).to_string())

        except Exception as e:
            print(f"Error loading data from Excel file: {e}")
            import traceback
            traceback.print_exc()

    # Create template with sample data (fallback option)
    if load_sample_data:
        print("\nCreating RFQ template with sample data...")
        try:
            # Generate sample data
            sample_df = create_sample_data(num_rows=25)

            # Load data into template
            validation_options = load_data_to_template(
                df=sample_df,
                output_path=data_path,
                enable_validation=enable_validation
            )
            print(f"Template with data saved to: {os.path.abspath(data_path)}")

            # Print data summary
            print(f"\nSample data summary:")
            print(f"  - Total rows: {len(sample_df)}")
            print(f"  - Total columns: {len(RFQ_COLUMNS)}")
            if enable_validation:
                print(f"  - Validation fields: {len(validation_options)}")

            # Show sample of loaded data
            print(f"\nFirst 3 material descriptions:")
            for i, desc in enumerate(sample_df['material_description'].head(3)):
                print(f"  {i+1}. {desc}")

        except Exception as e:
            print(f"Error creating template with data: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nRFQ Template creation completed!")
    print(f"Files created in: {os.path.abspath(output_dir)}")

    # Print column information
    print(f"\nTemplate includes {len(RFQ_COLUMNS)} columns:")
    for i, col in enumerate(RFQ_COLUMNS[:10], 1):
        print(f"  {i:2d}. {col}")
    if len(RFQ_COLUMNS) > 10:
        print(f"  ... and {len(RFQ_COLUMNS) - 10} more columns")

    print(f"\nTo use the template:")
    if load_from_excel:
        print(f"1. Open the Excel file: {os.path.basename(data_path)}")
        print(f"2. Review the loaded data from: {os.path.basename(excel_file_path)}")
    else:
        print(f"1. Open the Excel file: {os.path.basename(template_path)}")
        print(f"2. Enter your material data in the rows below the headers")
    print(f"3. Use dropdown validation for classification fields")
    print(f"4. Required fields are highlighted when empty")
