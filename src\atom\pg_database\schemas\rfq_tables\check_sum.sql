-- Function to check BOM and General values after aggregation
-- This will actually perform the aggregation properly with force_apply

CREATE OR REPLACE FUNCTION public.check_bom_general_values(
    p_project_id INTEGER
)
RETURNS TABLE (
    source TEXT,
    length NUMERIC,
    elbows_90 NUMERIC,
    elbows_45 NUMERIC, 
    bevels NUMERIC,
    tees NUMERIC
)
AS $$
BEGIN
    -- First run the aggregation with force_apply=TRUE to make actual changes
    PERFORM manage_bom_to_general_aggregation(p_project_id, FALSE, TRUE);
    
    -- Return BOM sums
    RETURN QUERY
    SELECT 
        'BOM' AS source,
        COALESCE(SUM(b.length), 0) AS length,
        COALESCE(SUM(b.elbows_90), 0) AS elbows_90,
        COALESCE(SUM(b.elbows_45), 0) AS elbows_45,
        COALESCE(SUM(b.bevels), 0) AS bevels,
        COALESCE(SUM(b.tees), 0) AS tees
    FROM public.bom b
    WHERE b.project_id = p_project_id;
    
    -- Return General sums
    RETURN QUERY
    SELECT 
        'General' AS source,
        COALESCE(SUM(g.length), 0) AS length,
        COALESCE(SUM(g.elbows_90), 0) AS elbows_90,
        COALESCE(SUM(g.elbows_45), 0) AS elbows_45,
        COALESCE(SUM(g.bevels), 0) AS bevels,
        COALESCE(SUM(g.tees), 0) AS tees
    FROM public.general g
    WHERE g.project_id = p_project_id;
END;
$$ LANGUAGE plpgsql;
