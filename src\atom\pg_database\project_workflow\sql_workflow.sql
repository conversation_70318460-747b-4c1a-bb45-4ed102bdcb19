-- Refresh input categories
--SELECT update_rfq_input_categories(ARRAY[20]);

-- Refresh BOM
--SELECT * FROM update_bom_from_rfq_input(ARRAY[20]);

-- If Supports with blank sizes, attempt to fill them from the largest pipe size on the same drawing
--SELECT public.fill_support_size_from_lf(20);

--Validate Quantities
-- Validate BOM vs RFQ View
	SELECT * FROM validate_bom_vs_view(20);

-- Verify General
	SELECT
		SUM(length) AS total_length,
		SUM(elbows_90) AS total_elbows_90,
		SUM(elbows_45) AS total_elbows_45,
		SUM(bevels) AS total_bevels,
		SUM(tees) AS total_tees,
		SUM(reducers) AS total_reducers,
		SUM(caps) AS total_caps,
		SUM(flanges) AS total_flanges,
		SUM(valves_flanged) AS total_valves_flanged,
		SUM(valves_welded) AS total_valves_welded,
		SUM(cut_outs) AS total_cut_outs,
		SUM(supports) AS total_supports,
		SUM(bends) AS total_bends,
		SUM(union_couplings) AS total_union_couplings,
		SUM(expansion_joints) AS total_expansion_joints,
		SUM(field_welds) AS total_field_welds,
		SUM(calculated_eq_length) AS total_calculated_eq_length,
		SUM(calculated_area) AS total_calculated_area
	FROM manage_bom_to_general_aggregation(20, FALSE);

-- Force refresh qunatities if validation fails
	--SELECT public.force_refresh_bom_categories(20);