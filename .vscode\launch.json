{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: App",
            "type": "debugpy",
            "request": "launch",
            "program": "app.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: ROI Extraction Example",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/roi_extraction_example.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "ROI Extraction",
            "type": "debugpy",
            "request": "launch",
            "module": "src.atom.extraction.extraction",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: Current File (no pythonpath)",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Python: App 2 Bypass Login",
            "type": "debugpy",
            "request": "launch",
            "program": "app2.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python Debugger: Column Organizer",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/dialogs/columnorganizer.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${cwd}"
            },
        },
        {
            "name": "Python Debugger: Table Exporter",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/dialogs/tableexporter.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${cwd}"
            },
        },
        {
            "name": "Extract ROI Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/extract_roi_test.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: Firebase test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/firebase/firebase_test.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: Login View",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/loginview.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Python: UI Preview",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/uipreview.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Blueprint Reader UI",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/gui/blueprintreader.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "EditRoiDialog",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/gui/editroidialogtest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Item Roles",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/gui/itemrolescomboboxtest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Custom database test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/database/dbtest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "GroupedTableView Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/gui/groupedtableviewtest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "UploadQueueView",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/uploadqueueview.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "DatabaseTest",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/database/databasetest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Create Database createDB.py",
            "type": "debugpy",
            "request": "launch",
            "program": "createDB.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Lookup Table Standalone",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/tables/lookuptable.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Build ATEM",
            "type": "debugpy",
            "request": "launch",
            "program": "build_atem.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Extract Symbols Main",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/extract_symbols/vector_classify.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "GUI - Delete Project Preview",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/database/deleteprojectpreview.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "AI Classifier - MTO Assistant Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/mto/aiclassifier_test.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Column Order Saving - Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/setcolumnordertest.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Group PDFs - Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/group_pdfs.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Group PDFs ui - Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/pdf_drawings_gui.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "classify_iso_merge_V2_V3_b.py",
            "type": "debugpy",
            "request": "launch",
            "program": "classify_iso_merge_V2_V3_b.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Modify DB - Insert RFQ Data",
            "type": "debugpy",
            "request": "launch",
            "program": "modify_db.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Merge into BOM Test",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/merge_rfq_into_bom.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "ROI Extraction Worker",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/_worker.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Source Preprocessing",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/sourcepreprocess.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [
                // "C:/Drawings/SanMateoOCR/San Mateo Midstream_Black River_ISOs - Extracted_Pages=[1].pdf",
                // "C:/Drawings/SanMateoOCR/San Mateo Midstream_Black River_ISOs.pdf",
                "C:/Users/<USER>/Documents/Drawings/Binder1.pdf",
                // "C:/Users/<USER>/Desktop/remuriate/Combined Remuriate.pdf",
            ]

        },

        {
            "name": "Source ROI Extraction",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/roiextraction.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [
                // "C:/Drawings/SanMateoOCR/San Mateo Midstream_Black River_ISOs - Extracted_Pages=[1].pdf",
                // "C:/Users/<USER>/Documents/Drawings/PLNG BOG Drawings Page 1.pdf",
                // "C:/Users/<USER>/Documents/Drawings/Binder1 - Extracted_Pages=[1].pdf",
                "C:/Users/<USER>/Documents/Drawings/Binder1.pdf"
                // "C:/Users/<USER>/Documents/Drawings/Exxon/exxon documents.pdf"
            ]
        },

        {
            "name": "Profile Source Preprocess",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/test_sourcepreprocess/profile_sourcepreprocess.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "App Paths",
            "type": "debugpy",
            "request": "launch",
            "program": "src/app_paths.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Generate Icon Sequence",
            "type": "debugpy",
            "request": "launch",
            "program": "misc/generate_icon_sequence.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "View Textract Result",
            "type": "debugpy",
            "request": "launch",
            "program": "src/tests/textract_test.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Test - Convert Quantities to Float",
            "type": "debugpy",
            "request": "launch",
            "program": "tests/test_convert_quantities_to_float.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Test - Process Extract Page",
            "type": "debugpy",
            "request": "launch",
            "program": "tests/test_process_page.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Plugin Dialog",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/dialogs/plugindialog.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Raw Data Viewer + Analysis",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/dialogs/rawdataviewer.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "OCR Extraction Dialog",
            "type": "debugpy",
            "request": "launch",
            "program": "src/views/dialogs/ocrextractiondialog.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Source Preparation Utility Dialog",
            "type": "debugpy",
            "request": "launch",
            "program": "src/utils/pdf/sourcepreparationutility.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Autorotation Tool",
            "type": "debugpy",
            "request": "launch",
            "program": "src/utils/pdf/autorotation_ui.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "AIS PostgreSQL Interface",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/pg_database/ais_interface_launcher.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Isomtric BOM",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/skimage_utils/detect_lines.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Step 1 - Adjust coords to template",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/vision/detect_text_regions/adjust_coords_to_template.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Step 2 - Apply Template",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/vision/detect_text_regions/apply_template.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },
        {
            "name": "Langgraph Classifications Run",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/ai_classifier/langgraph_classifier/run_classification.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "Normalize Description",
            "type": "debugpy",
            "request": "launch",
            "program": "unit_tests/normalize_description.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
        },

        {
            "name": "RFQ Template Builder - GUI",
            "type": "debugpy",
            "request": "launch",
            "program": "rfq_template_builder_gui.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [],
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        },
        {
            "name": "RFQ Template Builder - CLI",
            "type": "debugpy",
            "request": "launch",
            "program": "rfq_template_builder.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [],
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        },
        {
            "name": "RFQ Builder - Launcher (GUI)",
            "type": "debugpy",
            "request": "launch",
            "program": "launch_rfq_builder.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": ["--gui"],
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        },
        {
            "name": "RFQ Builder - Create Sample Config",
            "type": "debugpy",
            "request": "launch",
            "program": "launch_rfq_builder.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": ["--sample-config"],
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        },
        {
            "name": "Audit Workbook",
            "type": "debugpy",
            "request": "launch",
            "program": "src/atom/pg_database/data_uploads/field_mapping.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        },
        {
            "name": "Detect Support Assemblies",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/detect_support_assemblies.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "purpose": ["debug-in-terminal"]
        }

    ]
}