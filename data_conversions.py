import re
from src.utils.logger import logger
import pandas as pd
import numpy as np

from value_mappings import fitting_category_to_ef, fitting_category_to_sf

# logger = logging.getLogger(__file__)
# # Temporarily set the logger level to INFO
# logger.setLevel(logging.INFO)
#
# # Create a console handler (if you haven't already)
# console_handler = logging.StreamHandler()
#
# # Optionally, set a formatter
# formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
# console_handler.setFormatter(formatter)
#
# # Add the handler to the logger
# logger.addHandler(console_handler)


def parse_fraction(fraction_str):
    """
    Parse a fraction string with optional whitespace around the slash.
    Examples:
    - "1/2"
    - "1 /2"
    - "1/ 2"
    - "1 / 2"
    """
    # Remove outer whitespace and split by slash, preserving any internal whitespace
    parts = fraction_str.strip().split('/')
    if len(parts) != 2:
        raise ValueError(f"Invalid fraction format: {fraction_str}")

    # Convert to integers, removing any whitespace
    numerator = int(parts[0].strip())
    denominator = int(parts[1].strip())

    if denominator == 0:
        raise ValueError("Denominator cannot be zero")

    return numerator / denominator

def parse_mixed_number(number_str):
    """
    Parse a string containing a whole number and/or fraction.
    Examples:
    - "1 1/2" -> 1.5
    - "1 1 / 2" -> 1.5
    - "13/4" -> 3.25
    - "13 / 4" -> 3.25
    - "2" -> 2.0
    """
    # Remove extra whitespace from ends while preserving internal spacing
    number_str = number_str.strip()

    # Pattern for mixed number (whole + fraction)
    # Allows whitespace around the slash
    mixed_pattern = r"^(\d+)\s+(\d+\s*/\s*\d+)$"

    # Pattern for fraction only
    fraction_pattern = r"^(\d+\s*/\s*\d+)$"

    # Pattern for whole number only
    whole_pattern = r"^(\d+)$"

    # Try mixed number pattern
    match = re.match(mixed_pattern, number_str)
    if match:
        whole = int(match.group(1))
        fraction = parse_fraction(match.group(2))
        return whole + fraction

    # Try fraction only pattern
    match = re.match(fraction_pattern, number_str)
    if match:
        return parse_fraction(match.group(1))

    # Try whole number pattern
    match = re.match(whole_pattern, number_str)
    if match:
        return float(match.group(1))

    raise ValueError(f"Invalid number format: {number_str}")

def convert_meters_to_feet(value_str):
    """
    Convert meter or millimeter measurements to feet.
    Handles formats like:
    - "20.5M" or "20.5 M" (meters)
    - "20.5m" or "20.5 m" (meters)
    - "2050MM" or "2050 MM" (millimeters)
    - "2050mm" or "2050 mm" (millimeters)

    Returns float value in feet, or None if not a meter/mm measurement
    """
    if not isinstance(value_str, str):
        return None

    # Create a copy for processing while preserving original for unit detection
    original_value = value_str.strip()
    processed_value = original_value.upper()

    # Check for meter indicators (case insensitive)
    is_meters = False
    is_millimeters = False

    # Check for millimeters first (more specific)
    if processed_value.endswith('MM') or ' MM' in processed_value:
        is_millimeters = True
    # Check for meters
    elif processed_value.endswith('M') or ' M' in processed_value:
        is_meters = True

    if not (is_meters or is_millimeters):
        return None

    # Extract numeric value by removing unit indicators
    numeric_str = original_value
    # Remove unit indicators (case insensitive)
    for unit in ['MM', 'mm', 'M', 'm']:
        numeric_str = numeric_str.replace(unit, '').strip()

    # Clean up numeric string - remove extra spaces that might be in decimals
    # Handle cases like '20. 5' -> '20.5'
    numeric_str = ''.join(numeric_str.split())

    try:
        numeric_value = float(numeric_str)

        if is_millimeters:
            # Convert millimeters to feet: 1 mm = 0.00328084 feet
            return numeric_value * 0.00328084
        elif is_meters:
            # Convert meters to feet: 1 meter = 3.28084 feet
            return numeric_value * 3.28084

    except ValueError:
        return None

    return None

def convert_quantity_to_float(value):
    """
    Parse a string containing feet and/or inches measurements, or metric measurements.
    Accepts formats like:
    - "21'-3"" (21 feet 3 inches with hyphen)
    - "21' 3"" (21 feet 3 inches with space)
    - "21'3"" (21 feet 3 inches no separator)
    - "21 ' 3 "" (21 feet 3 inches with arbitrary whitespace)
    - "11"" (11 inches)
    - "5'" (5 feet)
    - "8.13/16"" (8 and 13/16 inches)
    - "8.1/2"" (8 and 1/2 inches)
    - "9'3.1/2"" (9 feet and 3-1/2 inches)
    - "20.5M" or "20.5 M" (meters)
    - "2050MM" or "2050 MM" (millimeters)

    Returns float value in feet
    """
    if pd.isna(value):
        return 0.0

    if isinstance(value, (int, float)):
        return float(value)

    if isinstance(value, str):
        # Check for meter/millimeter measurements first (before processing other formats)
        meter_result = convert_meters_to_feet(value)
        if meter_result is not None:
            return meter_result
        # Remove all whitespace from the string
        measurement = ''.join(value.split())

        # Pattern for feet with complex inches (decimal + fraction):
        # - Digits (feet)
        # - Single quote
        # - Optional hyphen or whitespace
        # - Digits (whole inches)
        # - Optional decimal point followed by fraction (n/m) with optional whitespace
        # - Optional double or single quote
        # - Optional trailing characters (letters, etc)
        # Note: Allows for space within the denominator (e.g. "11/1 6" for "11/16")
        complex_feet_inches_pattern = r"^(\d+)'(?:-?\s*(\d+)\.?\s*(\d+\s*/\s*\d+\s*\d*)?)[\"']?\s*\w*$"

        # Pattern for inches with fraction:
        # - Digits (whole inches)
        # - Decimal point
        # - Numerator/denominator with optional whitespace, allowing space in denominator
        # - Optional double or single quote
        # - Optional trailing characters (letters, etc)
        inches_fraction_pattern = r"^(\d+)\.\s*(\d+\s*/\s*\d+\s*\d*)[\"']?\s*\w*$"

        # Pattern for full feet and inches:
        # - Digits
        # - Optional whitespace
        # - Single quote
        # - Optional hyphen or whitespace
        # - Optional digits (if digits exist, must end with double or single quote)
        # - Optional trailing characters (letters, etc)
        feet_inches_pattern = r"^(\d+)'(?:-?\s*(\d+)[\"']?)?\s*\w*$"

        # Pattern for inches only: digits followed by double or single quote
        # - Optional trailing characters (letters, etc)
        inches_only_pattern = r"^(\d+)[\"']\s*\w*$"

        # Try inches fraction pattern first (e.g. "8.13/16"")
        match = re.match(inches_fraction_pattern, measurement)
        if match:
            whole_inches = int(match.group(1))
            fraction = convert_fraction_to_float(match.group(2))
            return (whole_inches + fraction) / 12

        # Try complex feet and inches pattern (e.g. "9'3.1/2"")
        match = re.match(complex_feet_inches_pattern, measurement)
        if match:
            feet = int(match.group(1))
            inches_whole = match.group(2)
            fraction_part = match.group(3)

            if fraction_part:
                # If we have a fraction part, parse it and add to the whole inches
                inches = float(inches_whole) + convert_fraction_to_float(fraction_part)
            else:
                inches = float(inches_whole)

            return feet + (inches / 12)

        # Try feet and inches pattern
        match = re.match(feet_inches_pattern, measurement)
        if match:
            feet = int(match.group(1))
            inches = int(match.group(2)) if match.group(2) else 0
            return feet + (inches / 12)

        # Try inches only pattern (no feet)
        match = re.match(inches_only_pattern, measurement)
        if match:
            inches = int(match.group(1))
            return (inches / 12)

        # Handle mixed numbers like "10 7/8"" - check original value before whitespace removal
        if ' ' in value and '/' in value:
            parts = value.split()
            if len(parts) == 2 and '/' in parts[1]:
                try:
                    whole_part = float(parts[0])
                    # Remove quotes from fraction part before converting
                    fraction_str = parts[1].replace('"', '').replace("'", '')
                    fraction_part = convert_fraction_to_float(fraction_str)
                    result = whole_part + fraction_part
                    # Check if this is an inch measurement (has quote delimiter) and convert to feet
                    if '"' in value or "'" in value:
                        return result / 12  # Convert inches to feet
                    else:
                        return result  # Already in feet or unitless
                except Exception as e:
                    pass

        # parse original value
        try:
            value = parse_mixed_number(value)
        except:
            pass

    # Standard number extraction
    try:
        return float(value)
    except ValueError:
        # Attempt to extract the first float-like number from the string
        number_match = re.findall(r"[-+]?\d*\.\d+|\d+", value)
        if number_match:
            return float(number_match[0])

    return 0.0

def convert_quantity_to_float_old(value):
    """Original function 18th Feb 2025"""
    if pd.isna(value):
        return 0.0

    if isinstance(value, (int, float)):
        return float(value)

    if isinstance(value, str):
        # Remove non-numeric characters except necessary ones for numbers
        value = re.sub(r'[^\d\.\-\+/\' ]', '', value.replace(',', '').strip())

    # if isinstance(value, str):
    #     # Remove commas for thousands separators
    #     value = value.replace(',', '').strip()

        # Check for foot and inch notation like "4'2"
        foot_inch_match = re.match(r"(\d+)'(\d+)", value)
        if not foot_inch_match:
            foot_inch_match = re.match(r"(\d+)'-(\d+)", value)
        if foot_inch_match:
            feet = int(foot_inch_match.group(1))
            inches = int(foot_inch_match.group(2))
            return feet + inches / 12  # Convert to total feet

        # Handle fractions and mixed numbers (e.g., "1 1/4", "3/4", "1-1/4")
        fraction_match = re.search(r'(\d+)?\s*[-]?\s*(\d+)/(\d+)', value)
        if fraction_match:
            whole_number = int(fraction_match.group(1)) if fraction_match.group(1) else 0
            numerator = int(fraction_match.group(2))
            denominator = int(fraction_match.group(3))
            fraction_value = whole_number + (numerator / denominator)
            return fraction_value

        # Standard number extraction
        try:
            return float(value)
        except ValueError:
            # Attempt to extract the first float-like number from the string
            number_match = re.findall(r"[-+]?\d*\.\d+|\d+", value)
            if number_match:
                return float(number_match[0])

    # Default return if all parsing fails
    return 0.0

def parse_complex_size(value):
    """Parse complex size values that may include decimals followed by fractions. i.e '1.1/2' (1.5) """

    try:
        # Only remove quotes from the value
        value = str(value).replace('"', '').replace("'", '').strip()

        if '.' in value and '/' in value:
            # Handle cases where decimal and fraction are combined, e.g., "1.1/2"
            decimal_part, fraction_part = value.split('.')
            fraction_str = fraction_part.split('/')[0] + '/' + fraction_part.split('/')[1]
            fraction = convert_fraction_to_float(fraction_str)
            return float(decimal_part) + fraction
        elif ' ' in value and '/' in value:
            # Handle mixed numbers with spaces, e.g., "10 7/8"
            parts = value.split()
            if len(parts) == 2 and '/' in parts[1]:
                whole_part = float(parts[0])
                fraction_part = convert_fraction_to_float(parts[1])
                return whole_part + fraction_part
            else:
                # Fall back to original logic for other space-separated cases
                parts = re.findall(r'(\d+/\d+|\d+\.\d+|\d+)', value)
                if parts:
                    return sum(convert_fraction_to_float(part) for part in parts)
        else:
            # Handling cases without combined decimals
            parts = re.findall(r'(\d+/\d+|\d+\.\d+|\d+)', value)
            if parts:
                return sum(convert_fraction_to_float(part) for part in parts)
        return np.nan
    except Exception as e:
        logger.error(f"Failed to parse complex size. Value: '{value}'. Error: {e}", exc_info=True)
        return np.nan

def convert_fraction_to_float(fraction_str):
    """Convert a fraction string to a float."""
    try:
        if '/' in fraction_str:
            numerator, denominator = fraction_str.split('/')
            return float(numerator) / float(denominator)
        else:
            return float(fraction_str)
    except ValueError as e:
        # Log the error
        logger.error(f"Error converting fraction to float: {e}")
        return fraction_str # Return original string instead of blank #np.nan  # or np.nan or other appropriate value to signify an error

def format_size_value(size):
    """Ensure proper float format, adding leading zero if necessary."""
    try:
        if size == '' or pd.isna(size):
            return np.nan
        size_float = float(size)
        formatted_size = "{:.3f}".format(size_float).rstrip('0').rstrip('.')
        return formatted_size if size_float < 1 else str(int(size_float)) if size_float.is_integer() else formatted_size
    except Exception as e:
        logger.error(f"Failed to format size value. Size: '{size}'. Error: {e}", exc_info=True)
        return size #np.nan

def process_size_column(size):
    """Process the 'size' column and return 'size1' and 'size2'."""
    if size is None or str(size).strip() == '':
        return np.nan, np.nan

    size = str(size).strip()
    size1 = size2 = np.nan

    # Handling case sensitivity for 'x'
    if 'x' in size.lower():
        parts = re.split(r'\s*[xX]\s*', size, flags=re.IGNORECASE)
        size1 = parse_complex_size(parts[0])
        size2 = parse_complex_size(parts[1]) if len(parts) > 1 else np.nan
    else:
        size = size.replace('-', ' ')
        size1 = parse_complex_size(size)
        size2 = np.nan

    # Format sizes and ensure correct ordering
    try:
        size1 = format_size_value(size1) if not pd.isna(size1) else np.nan
        size2 = format_size_value(size2) if not pd.isna(size2) else np.nan
        if not pd.isna(size1) and not pd.isna(size2) and float(size2) > float(size1):
            size1, size2 = size2, size1
    except Exception as e:
        logger.warning(f"Error swapping size1 & size2: {e}")
    return size1, size2

def process_complex_size(size):
    if 'x' in size.lower():
        parts = re.split(r'\s*[xX]\s*', size, flags=re.IGNORECASE)
        size1 = format_size_value(parse_complex_size(parts[0]))
        size2 = format_size_value(parse_complex_size(parts[1])) if len(parts) > 1 else ''
        size3 = format_size_value(parse_complex_size(parts[2])) if len(parts) > 2 else ''
    else:
        size1 = format_size_value(parse_complex_size(size))
        size2 = ''
        size3 = ''

    # Format size values as strings with three decimal places
    size1 = '{:.3f}'.format(float(size1)) if size1 else ''
    size2 = '{:.3f}'.format(float(size2)) if size2 else ''
    size3 = '{:.3f}'.format(float(size3)) if size3 else ''

    return size1, size2, size3

#   Equivalent Linear Feet
#----------------------------------#
#----------------------------------#
#----------------------------------#

def lookup_value(lookup_category, size1_value, quantity, lookup_df: pd.DataFrame):

    try:
        # Make sure that size1_value is converted to a float or the format that matches your size columns in Excel
        # size1_value = float(size1_value)
        # Process size1_value
        size1_value_formatted = parse_complex_size(str(size1_value).strip())
        size1_value_formatted = format_size_value(size1_value_formatted)

        # Convert quantity to int or float depending on what is expected, handle None or empty strings safely
        quantity = float(quantity) if quantity else 0

    except ValueError as e:
        logger.warning(f"Value conversion error, size1_value={size1_value}, quantity={quantity} : {e}")
        return ''

    #ef_lookup_df.loc['90 Long Radius', size1_value]

    try:
        # Log the actual values being used for lookup
        #logger.debug(f"Looking up Category: '{lookup_category}' and Size: '{size1_value}'")

        value = lookup_df.loc[lookup_category, size1_value]

        # Check if the value is NaN, indicating a missing entry
        if pd.isna(value):
            logger.warning(f"Found NaN for Lookup Category '{lookup_category}' and Size '{size1_value}'")
            return ''

        # Perform the multiplication, ensuring that the resulting value is a float
        result = float(value) * quantity
        return result

    except KeyError as e:
        if len(lookup_category) > 0:
            # logger.exception(e)
            logger.warning(f"No match found for Lookup Category '{lookup_category}' and Size '{size1_value}', formatted Size '{size1_value_formatted}'")
        return ''
    except Exception as e:
        logger.warning(f"Error in multiplication: {e}")
        return ''

def lookup_complex_fitting(row, lookup_df: pd.DataFrame):
    size = str(row['size']).strip()
    quantity = float(row['quantity']) if pd.notnull(row['quantity']) else 0

    if size == '' or pd.isna(size):
        return '', ''

    size1, size2, size3 = process_complex_size(size)

    #print(f"Formatted size values: size1={size1}, size2={size2}, size3={size3}")

    try:
        if size1 and size2 and not size3:
            if 'Size3' in lookup_df.columns:
                # If the lookup table has 'Size3' but we only have two sizes, use 'Size2' and 'Size3' for lookup
                matching_rows = lookup_df[
                    ((lookup_df['Size2'] == size1) & (lookup_df['Size3'] == size2)) |
                    ((lookup_df['Size2'] == size2) & (lookup_df['Size3'] == size1))
                ]
            else:
                # Search for rows where Size1 and Size2 match, regardless of order
                matching_rows = lookup_df[
                    ((lookup_df['Size1'] == size1) & (lookup_df['Size2'] == size2)) |
                    ((lookup_df['Size1'] == size2) & (lookup_df['Size2'] == size1))
                ]
        elif size1 and size2 and size3:
            # Ensure size1 and size2 are the larger values, and size3 is the smallest value
            size1, size2, size3 = sorted([size1, size2, size3], reverse=True)[:3]

            # Search for rows where Size1, Size2, and Size3 match, regardless of order
            matching_rows = lookup_df[
                ((lookup_df['Size1'] == size1) & (lookup_df['Size2'] == size2) & (lookup_df['Size3'] == size3))
            ]
        else:
            logger.warning(f"Invalid size values: size1={size1}, size2={size2}, size3={size3}")
            return '', ''

        if not matching_rows.empty:
            ef_value = float(matching_rows['ef_ft'].iloc[0]) * quantity
            sf_value = float(matching_rows['a_ft'].iloc[0]) * quantity
            return ef_value, sf_value
        else:
            logger.warning(f"No matching rows found for size1={size1}, size2={size2}, size3={size3}")
            return '', ''

    except Exception as e:
        logger.error(f"Error in lookup_complex_fitting: {e}")
        return '', ''

def get_lookup_category(row, category_to_table):
    # Determine whether to use 'fitting_category' or 'valve_type' and then lookup the correct table name
    if pd.notna(row['fitting_category']) and row['fitting_category']:
        #logger.debug(f"Row fitting_category: {row['fitting_category']}")
        fitting_category = category_to_table.get(row['fitting_category'], '')
        #print(f"\n\n CLS FITTING CATEGORY: {fitting_category}, ROW 'fitting_vategory: {row['fitting_category']}'")
        return "fitting", fitting_category #category_to_table.get(row['fitting_category'], '')

    elif pd.notna(row['valve_type']) and row['valve_type']:
        #logger.info(f"Row valve_type: {row['valve_type']}")
        valve_category = row['valve_type'] #category_to_table.get(row['valve_type'], '')
        return "valve", valve_category#category_to_table.get(row['valve_type'], '')
    else:
        #logger.warning("No fitting_category or valve_type found")
        return '', ''

def get_ef(df: pd.DataFrame, lookup_tables): #get_ef(df, ef_lookup_df: pd.DataFrame, sf_lookup_df: pd.DataFrame):

    ef_category_to_table = fitting_category_to_ef
    sf_category_to_table = fitting_category_to_sf

    # Get the EF and SF lookup dataframes from the lookup_tables dictionary
    ef_lookup_df = lookup_tables['ef']
    sf_lookup_df = lookup_tables['sf']
    table_red = lookup_tables['reducers']
    table_el_red = lookup_tables['elbows_reducers']
    table_tee_red = lookup_tables['tee_reducers']
    table_cross_red = lookup_tables['cross_reducers']

    #print("\n\nEF TEMPLATE: ", ef_lookup_df)

    logger.info("EF Lookup: Start")

    # Ensure the 'equiv_lf' column exists
    if 'ef' not in df.columns:
        df['ef'] = ''  # initialize as default value

    # Calculate equivalent linear footage and square footage for each row
    for index, row in df.iterrows():
        item_type, ef_lookup_category = get_lookup_category(row, ef_category_to_table)
        item_type, sf_lookup_category = get_lookup_category(row, sf_category_to_table)

        #if ef_lookup_category == "Flanges":
            #print(f"\n\nITEM: {item_type}, {ef_lookup_category}, {sf_lookup_category}, {row['size1']}")

        if item_type == 'fitting':

            try:
                if ef_lookup_category == 'Reducer':
                    ef_value, sf_value = lookup_complex_fitting(row, lookup_df=table_red)

                elif ef_lookup_category == 'Red. 90':
                    ef_value, sf_value = lookup_complex_fitting(row, lookup_df=table_el_red)

                elif ef_lookup_category == 'Red. Tee':
                    ef_value, sf_value = lookup_complex_fitting(row, lookup_df=table_tee_red)

                elif ef_lookup_category == 'Red. Cross':
                    ef_value, sf_value = lookup_complex_fitting(row, lookup_df=table_cross_red)

                else:

                    ef_value = lookup_value(ef_lookup_category, row['size1'], row['quantity'], lookup_df=ef_lookup_df)
                    sf_value = lookup_value(sf_lookup_category, row['size1'], row['quantity'], lookup_df=sf_lookup_df)

                df.at[index, 'ef'] = ef_value if ef_value != '' else np.nan
                df.at[index, 'sf'] = sf_value if sf_value != '' else np.nan

                #print(f"Found EF: lookup_category={lookup_category}, size1={row['size1']}, value={value}")
            except Exception as e:
                # For rows which have not been MTO classified, they are
                # not candidates for EF lookup. Likely None values for `size1`
                logger.error(f"Error processing row {index}: {e}", exc_info=True)
                #logger.error(f"{row[['material_description', 'quantity', 'size1']]}")

        elif item_type =="valve": # Get only SF

            #print(f"Type is valve, SF Category: {sf_lookup_category}")
            #print(f"Valve Row data: size1={row['size1']}, quantity={row['quantity']}")
            sf_value = lookup_value(sf_lookup_category, row['size1'], row['quantity'], lookup_df=sf_lookup_df)
            # print(f"Lookup result: sf_value={sf_value}")
            df.at[index, 'sf'] = sf_value if sf_value != '' else np.nan


    logger.info("EF/SF Lookup Complete")
