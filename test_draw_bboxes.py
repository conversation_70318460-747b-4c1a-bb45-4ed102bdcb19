import fitz
import math

filename = r"C:\Drawings\Joey\S1601 Insulation Only (1).pdf"


def rotate_point(x, y, cx, cy, angle_rad):
    dx, dy = x - cx, y - cy
    cos_a, sin_a = math.cos(angle_rad), math.sin(angle_rad)
    rx = cx + dx * cos_a - dy * sin_a
    ry = cy + dx * sin_a + dy * cos_a
    return (rx, ry)

def bbox_to_rotated_poly(bbox, angle_rad):
    x0, y0, x1, y1 = bbox
    cx = (x0 + x1) / 2
    cy = (y0 + y1) / 2
    corners = [(x0, y0), (x1, y0), (x1, y1), (x0, y1)]
    return [rotate_point(x, y, cx, cy, angle_rad) for x, y in corners]

# Load document
doc = fitz.open(filename)
page = doc[4]

# Extract text structure
text = page.get_text("dict")

for block in text["blocks"]:
    for line in block.get("lines", []):
        # Merge span bboxes into a line-wide bbox
        x0s, y0s, x1s, y1s = [], [], [], []
        dirs = []

        for span in line.get("spans", []):
            x0, y0, x1, y1 = span["bbox"]
            x0s.append(x0)
            y0s.append(y0)
            x1s.append(x1)
            y1s.append(y1)

        # Use min/max of all span coords to define line bbox
        line_bbox = (min(x0s), min(y0s), max(x1s), max(y1s))

        # Assume all spans in line have same dir
        direction = line.get("dir", None)
        if direction:
            angle_rad = math.atan2(direction[1], direction[0])
            print(angle_rad)
        else:
            angle_rad = 0

        poly = bbox_to_rotated_poly(line_bbox, angle_rad)
        poly.append(poly[0])  # close the polygon
        page.draw_polyline(poly, color=(1, 0, 0), width=1.0)

# Save the result
doc.save("debug/output_with_rotated_polygons.pdf")