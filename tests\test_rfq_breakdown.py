"""For validating parsing, formatting and summation of quantities"""

import pandas as pd
from data_conversions import convert_quantity_to_float

if __name__ == "__main__":

    bom_df = pd.read_excel(r"c:\Users\<USER>\Architekt\exported\New folder\exported_bom_data.xlsx")
    rfq_df = pd.read_excel(r"C:\Users\<USER>\Architekt\exported\New folder\exported_rfq_data.xlsx")

    bom_converted = bom_df.copy()
    bom_converted["Quantity_Converted"] = bom_converted["Quantity"].apply(convert_quantity_to_float)

    columns = ["Material Description", "Size", "Quantity", "Quantity_Converted"]
    bom_converted[columns].to_excel("debug/bom_converted.xlsx")
    print(bom_converted["Quantity_Converted"])

    grouped = bom_converted.groupby(["Material Description", "Size"])

    final = []
    for key, group in grouped:
        description, size = key
        # print(key)

        group_res = []

        expected_sum = 0
        for row in group.itertuples():
            group_res.append({"material_description": description,
                            "original_quantity_value": row.Quantity,
                            "quantity_converted": row.Quantity_Converted,
                            "rfq_quantity": None})

            try:
                expected_sum += row.Quantity_Converted
            except:
                pass

        rfq_query = rfq_df[(rfq_df["Material Description"] == description) & (rfq_df["Size"] == size)]
        if not rfq_query.empty:
            rfq_result = rfq_query.iloc[0]["Quantity"]
        else:
            rfq_result = "n/a"

        group_res.insert(0, {"material_description": ""})
        group_res.insert(0, {"material_description": description, "size": size, "expected_sum": expected_sum, "rfq_quantity": rfq_result})
        group_res.insert(0, {"material_description": "----------------START-------------------"})

        final.extend(group_res)

        # add some empty rows
        final.append({"material_description": ""})
        final.append({"material_description": "----------------END---------------"})
        final.append({"material_description": ""})
        final.append({"material_description": ""})
        final.append({"material_description": ""})

    print(final)
    final_df = pd.DataFrame(final)

    print(final_df.columns)
    columns = ["material_description", "size", "original_quantity_value", "quantity_converted", "expected_sum", "rfq_quantity"]
    final_df = final_df[columns]
    final_df.to_excel("debug/final_converted_quantities_df.xlsx", index=False)
