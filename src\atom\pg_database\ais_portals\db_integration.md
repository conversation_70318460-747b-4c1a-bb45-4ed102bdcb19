# AIS_Web Upload Form Logic Outline

---

## ✅ 1️⃣ Fix Progress Bar
Objective: Ensure the progress bar displays real-time upload progress during file upload.

Expected behavior:
- Show % progress (0% → 100%) during upload.
- On success → display "Upload Complete" at 100%.
- On failure → reset bar and display error message.

Implementation details:
- Progress should reflect actual file transfer, not just form submission.
- Update frontend to handle and display progress dynamically.

---
**NOTE**: We need to apply some validation to check if the client company exists and user exists in the database before user is able to upload.
Check the table 'public.atem_clients' for the client_domain fields (ex. email@**domain.com**). 
Check the table 'public.organization_members' for the user's email. 
If either do not exist, the user will need to create them. 

---

## ✅ 2️⃣ Send Email Notifications
Trigger: When the user clicks Submit Project and upload completes successfully.

Emails to send:
- client_confirmation.html → sent to the client (submitter).
- project_notification.html → sent internally to notify AIS team.

Implementation details:
- Backend should trigger both emails after upload success.
- Include relevant project info (company name, project ID, submission timestamp, etc.).

---

## ✅ 3️⃣ Tie AIS_Web API to AIS_Portal 'Projects' Button/Table
Objective: Clicking the Projects button in AIS_Portal pulls project data from AIS_Web API.

Implementation details:
- The API endpoint should return a list of all projects with required fields (ID, name, client, status, submission date, etc.).
- The portal should consume this endpoint and display results dynamically in the table.

---

## ✅ 4️⃣ Tie AIS_Web API to Client_Portal 'Projects' Button/Table
Objective: Same as above but for Client_Portal.

Implementation details:
- API should return only projects relevant to the authenticated client.
- Table dynamically updates based on API data (filter by client’s domain or ID).

---

## ✅ 5️⃣ Integrate with Postgres atem_clients Table
Objective: When form is submitted:
- Check if the user’s domain/email exists in atem_clients table.
- If no record is found:
  - Prompt user: "Create an account to proceed."
  - Redirect them or open a modaorganization_members l form for account creation.

Implementation details:
- Add backend validation on form submit → query atem_clients.
- If found → proceed with upload.
- If not → return a specific response to frontend to trigger the account prompt flow.

---

## 💡 Developer Notes
- Ensure API authentication/authorization for both AIS_Portal and Client_Portal endpoints.
- Email templates should be ready and stored server-side (or in templates folder).
- Make sure progress reporting works efficiently for large file uploads (consider chunking or resumable upload support if needed).
- Ensure user feedback (frontend) is clean: progress, errors, and success confirmations.