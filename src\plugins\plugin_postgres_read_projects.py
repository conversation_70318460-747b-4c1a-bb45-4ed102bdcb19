import pandas as pd
from datetime import datetime

from src.atom.pg_database import pg_connection


def plugin_postgres_read_projects(project_id: int = 0,
                                 project_name: str = "",
                                 client_id: int = 0,
                                 profile_id: int = 0,
                                 limit: int = 100,
                                 as_dataframe: bool = True):
    """
    Reads project data from the PostgreSQL database.

    Args:
        project_id (int): Filter by project ID (0 for all projects)
        project_name (str): Filter by project name (case-insensitive partial match)
        client_id (int): Filter by client ID (0 for all clients)
        profile_id (int): Filter by profile ID (0 for all profiles)
        limit (int): Maximum number of records to return
        as_dataframe (bool): Return results as pandas DataFrame if True, else as list of dicts

    Returns:
        pd.DataFrame or list: Project data
    """
    print("Reading project data from PostgreSQL database.")

    # Validate inputs
    try:
        project_id = int(project_id)
    except (ValueError, TypeError):
        print("Error: project_id must be an integer.")
        return None

    try:
        client_id = int(client_id)
    except (ValueError, TypeError):
        print("Error: client_id must be an integer.")
        return None

    try:
        profile_id = int(profile_id)
    except (ValueError, TypeError):
        print("Error: profile_id must be an integer.")
        return None

    try:
        limit = int(limit)
        if limit <= 0:
            limit = 100
            print(f"Invalid limit value. Using default: {limit}")
    except (ValueError, TypeError):
        limit = 100
        print(f"Invalid limit value. Using default: {limit}")

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")

            # Build query based on filters
            query = """
            SELECT
                p.id, p.project_name, p.location, p.jobsite_location,
                p.bid_revision, p.bid_due_date, p.received_from_client,
                p.engineering_drafter, p.ais_project_status, p.notes,
                p.client_id, c.client_name,
                p.profile_id, p.created_at, p.updated_at
            FROM public.atem_projects p
            LEFT JOIN public.atem_clients c ON p.client_id = c.id
            WHERE 1=1
            """

            params = []

            if project_id > 0:
                query += " AND p.id = %s"
                params.append(project_id)

            if project_name:
                query += " AND p.project_name ILIKE %s"
                params.append(f"%{project_name}%")

            if client_id > 0:
                query += " AND p.client_id = %s"
                params.append(client_id)

            if profile_id > 0:
                query += " AND p.profile_id = %s"
                params.append(profile_id)

            query += " ORDER BY p.id ASC LIMIT %s"
            params.append(limit)

            # Execute query
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                results = cursor.fetchall()

                if not results:
                    print("No projects found matching the criteria.")
                    if as_dataframe:
                        return pd.DataFrame(columns=columns)
                    else:
                        return []

                # Format results
                if as_dataframe:
                    df = pd.DataFrame(results, columns=columns)
                    print(f"Found {len(df)} project(s).")

                    # Print the DataFrame
                    print("\nProject Data:")
                    print(df)

                    return df
                else:
                    # Convert to list of dictionaries
                    project_list = []
                    for row in results:
                        project_dict = dict(zip(columns, row))
                        project_list.append(project_dict)

                    print(f"Found {len(project_list)} project(s).")

                    # Print the project list
                    print("\nProject Data:")
                    for project in project_list:
                        print(f"ID: {project['id']}, Name: {project['project_name']}")
                        print(f"  Client: {project['client_name']} (ID: {project['client_id']})")
                        print(f"  Location: {project['location']}")
                        print(f"  Jobsite: {project['jobsite_location']}")
                        print(f"  Status: {project['ais_project_status']}")
                        print(f"  Bid Due Date: {project['bid_due_date']}")
                        print(f"  Engineer: {project['engineering_drafter']}")
                        if project['notes']:
                            print(f"  Notes: {project['notes']}")
                        print(f"  Created: {project['created_at']}, Updated: {project['updated_at']}")
                        print("-" * 50)

                    return project_list

    except Exception as e:
        print(f"Failed to read project data: {e}")
        import traceback
        traceback.print_exc()
        return None