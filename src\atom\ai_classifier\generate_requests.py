import json, ast
from sys import exc_info
from .prompts import sch_append
from src.utils.logger import logger

def filter_json_by_fields(json_data, fields_to_include):
    # Create an empty list to store the filtered results
    filtered_results = []

    # Loop through each item in the json data
    for item in json_data:
        #print("ITEM:", item)
        try:
            # Check if the 'field' of the item is in the list of fields to include
            if item['classification field'] in fields_to_include:
                # If it is, append the item to the filtered results
                filtered_results.append(item)
        except Exception as e:
            print(f"Failed to append item: {e}")
            logger.error(f"Failed to append item: {e}", exc_info=True)

    return filtered_results

def category_filter(scope_value):
    lst_PIPE = ["ansme_ansi", "astm", "coating", "ends", "forging", "grade", "material", "pipe_category", "rating", "schedule", "tie_point"]
    lst_FITTINGS = ["ansme_ansi", "astm", "coating", "ends", "forging", "grade", "material", "rating", "schedule", "fitting_category"]
    lst_FLANGES = ["ansme_ansi", "astm", "coating", "ends", "forging", "grade", "material", "rating", "schedule",  "item_tag", "tie_point", "fitting_category"]
    lst_BOLTS = ["ansme_ansi", "astm", "coating", "bolt_category", "grade", "material"]
    lst_GASKETS = ["ansme_ansi", "astm", "coating", "gasket_category", "grade", "material", "rating"]
    lst_SUPPORTS = ["ansme_ansi", "astm", "coating", "grade", "material",  "weld_category", "item_tag"]
    lst_VALVES = ["ansme_ansi", "astm", "coating", "ends", "grade", "material", "rating", "forging", "valve_type", "weld_category", "item_tag", "tie_point"]
    lst_OTHER = ["ansme_ansi", "astm", "coating", "ends", "forging", "grade", "material", "rating", "schedule", "item_tag", "tie_point"]

    if scope_value == "Pipe":
        return lst_PIPE
    elif scope_value == "Fittings":
        return lst_FITTINGS
    elif scope_value == "Flanges":
        return lst_FLANGES
    elif scope_value == "Bolts":
        return lst_BOLTS
    elif scope_value == "Gaskets":
        return lst_GASKETS
    elif scope_value == "Supports":
        return lst_SUPPORTS
    elif scope_value == "Valves":
        return lst_VALVES
    elif scope_value == "Miscellaneous":
        return lst_OTHER
    else:
        return None

def parse_item_data_string(item_string):
    """Parse a single item data string into a dictionary."""
    item_dict = {}
    for line in item_string.split('\n'):
        if line.strip():  # Ensure the line contains data
            if ':' in line:
                key, value = line.split(':', 1)  # Split by the first colon only
                # Strip extra spaces and quotes from key and value
                key = key.strip().replace("'", "\"")
                value = value.strip().replace("'", "\"")
                # Add the key-value pair to the dictionary
                item_dict[key] = value
            else:
                print(f"Skipping line without colon: {line}")
    return item_dict

def generate_chat_completion_requests_stage2(filename, item_data, cls_data, prompt_template, model_name="gpt-3.5-turbo", temperature=0.2, top_p=0.1, include_answer=True):

    with open(filename, "w", encoding="utf-8") as f:
        for item_string in item_data:

            # Convert the item string to a dictionary
            item = parse_item_data_string(item_string)

            # Get the 'rfq_scope' (For additional context in a custom prompt)
            scope = item.get('rfq_scope', '')
            mtrl_description = item.get('material_description', '')
            p_size = item.get('size', '')
            p_id = item.get('id', '')

            # Get the filter classification list from the stage 2 dataframe (To specify rfq category fields )
            cls_list_str = item.get('cls_list', '')
            try:
                # Check if the cls_list is the fallback string 'Not Available'
                if cls_list_str == 'Not Available':
                    # Use a default classification list based on scope if available
                    default_cls_fields = category_filter(scope) if scope else []
                    if not default_cls_fields:
                        # Fallback to a minimal default list
                        default_cls_fields = ['material', 'grade', 'schedule']
                    cls_list = default_cls_fields
                else:
                    cls_list = ast.literal_eval(cls_list_str)  # Safely evaluate the string to a list

                # Filter cls_data (all fields) by the list of field names
                filtered_cls_data = filter_json_by_fields(cls_data, cls_list)

            except Exception as e:
                # Log the error and problematic string
                print(f"Error parsing 'cls_list': {cls_list_str}")
                print(f"Exception: {str(e)}")
                logger.error(f"Error parsing 'cls_list': {cls_list_str}")
                logger.error(f"Exception: {str(e)}")

                # Handle the error by using a default classification list based on scope
                default_cls_fields = category_filter(scope) if scope else []
                if not default_cls_fields:
                    # Fallback to a minimal default list
                    default_cls_fields = ['material', 'grade', 'schedule']

                cls_list = default_cls_fields
                filtered_cls_data = filter_json_by_fields(cls_data, cls_list)


            # Replace placeholders in the prompt with actual values from the item
            prompt = prompt_template.replace("_ID_", p_id)
            prompt = prompt.replace("_MATDESC_", mtrl_description)
            prompt = prompt.replace("_SIZE_", p_size)
            prompt = prompt.replace("_SCOPE_", scope)

            # prompt = prompt_template.replace("_ID_", item.get('id', ''))
            # prompt = prompt.replace("_MATDESC_", item.get('material_description', ''))
            # prompt = prompt.replace("_SIZE_", item.get('size', ''))

            # For each item, generate a prompt for every category in filtered_cls_data
            for cls_item in filtered_cls_data:
                # Convert cls_item to dictionary if it's not already
                if isinstance(cls_item, str):
                    cls_item = parse_item_data_string(cls_item)

                #print("\n\nCLS ITEM 2: ", cls_item)

                cls_field = cls_item.get('classification field', '')

                # Insert Classification field
                o_prompt = prompt.replace("_CLS_FIELD_", cls_field)

                # print("\n\nCLS FIELD: ", cls_field)
                # print("\n\nSCOPE: ", scope)

                if cls_field == 'schedule':
                    use_prompt = o_prompt.replace("_____", sch_append)
                else:
                    use_prompt = o_prompt




                # Ensure 'id' from item is included in user content, which might be your 'tko_id'
                cls_item['tko_id'] = item.get('id', '')

                # Properly format the user content as a JSON string
                user_content = json.dumps(cls_item)

                # Create a list of messages for each request
                messages = [
                    {"role": "system", "content": use_prompt},
                    {"role": "user", "content": user_content}
                ]

                # Write the structured prompt for the category to the JSONL file
                json_string = json.dumps({"model": model_name, "messages": messages, "temperature": temperature, "top_p": top_p}, ensure_ascii=False)
                f.write(json_string + "\n")

def generate_chat_completion_requests_stage1(filename, item_data, cls_data, prompt_template, model_name="gpt-3.5-turbo", temperature=0.2, top_p=0.1, include_answer=True):
    with open(filename, "w", encoding="utf-8") as f:
        for item_string in item_data:
            # Convert the item string to a dictionary
            print()
            print(item_string)
            print()
            print("length", len(item_string))
            if "id: 21" in item_string:
                pass
            item = parse_item_data_string(item_string)

            # Replace placeholders in the prompt with actual values from the item
            prompt = prompt_template.replace("_ID_", item.get('id', ''))
            prompt = prompt.replace("_MATDESC_", item.get('material_description', ''))
            prompt = prompt.replace("_SIZE_", item.get('size', ''))
            prompt_copy = prompt

            # For each item, generate a prompt for every category in cls_data
            for cls_item in cls_data:
                # Convert cls_item to dictionary if it's not already
                if isinstance(cls_item, str):
                    cls_item = parse_item_data_string(cls_item)

                # Ensure 'id' from item is included in user content, which might be your 'tko_id'
                try:
                    cls_item['tko_id'] = item.get('id', '')
                except Exception as e:
                    logger.error(f"Error getting tko_id: {e}")

                try:
                    cls_field = cls_item.get('classification field', '')

                    # Insert Classification field
                    prompt_copy = prompt_copy.replace("_CLS_FIELD_", cls_field)

                except Exception as e:
                    logger.error(f"Error getting classification field or replacing prompt item '_CLS_FIELD_': {e}")

                # Properly format the user content as a JSON string
                user_content = json.dumps(cls_item)

                # Create a list of messages for each request
                messages = [
                    {"role": "system", "content": prompt_copy},
                    {"role": "user", "content": user_content}
                ]

                # Write the structured prompt for the category to the JSONL file
                json_string = json.dumps({"model": model_name, "messages": messages, "temperature": temperature, "top_p": top_p}, ensure_ascii=False)
                f.write(json_string + "\n")


#-------------WORKS BUT IMPROPER JSON FORMAT -DO NOT DELETE-
# def parse_item_data_string(item_string):
#     """Parse a single item data string into a dictionary."""
#     item_dict = {}
#     for line in item_string.split('\n'):
#         key, value = line.split(':', 1)  # Split by the first colon only
#         item_dict[key.strip()] = value.strip()
#     return item_dict

# def generate_chat_completion_requests(filename, item_data, cls_data, prompt_template, model_name="gpt-3.5-turbo"):
#     with open(filename, "w") as f:
#         for item_string in item_data:
#             # Convert the item string to a dictionary
#             item = parse_item_data_string(item_string)

#             #print(f"\n\nITEM: {item}\n\n")
#             # Replace placeholders in the prompt with actual values from the item
#             prompt = prompt_template.replace("_ID_", item.get('id', ''))
#             prompt = prompt.replace("_MATDESC_", item.get('material_description', ''))
#             prompt = prompt.replace("_SIZE_", item.get('size', ''))

#             # For each item, generate a prompt for every category in cls_data
#             for cls_item in cls_data:
#                 # Embed the 'tko_id' in the user content or modify 'cls_item' to include it
#                 user_content = f"tko_id: {item.get('id', '')}\n{cls_item}"

#                 # Create a list of messages for each request
#                 messages = [
#                     {"role": "system", "content": prompt},
#                     {"role": "user", "content": user_content}
#                 ]

#                 # Write the structured prompt for the category to the JSONL file
#                 json_string = json.dumps({"model": model_name, "messages": messages})
#                 f.write(json_string + "\n")


# In progress: This is to generate more complex requests.
# Pushing to main to share as example, but not used in rest of prog rn.
def generate_gpt_request(prompt, data, functions=[]):
    model = "gpt-3.5-turbo"
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": str(data)}
    ]
    if functions:
        return json.dumps({"model": model, "messages": messages, "tools": functions})
    else:
        return json.dumps({"model": model, "messages": messages})

if __name__ == "__main__":
    # Generate simple request for summarising text
    prompt = "Please summarise the following text."
    text = "Chat Completions API: Chat models take a list of messages as input and return a model-generated message as output. Although the chat format is designed to make multi-turn conversations easy, it’s just as useful for single-turn tasks without any conversation."
    summarise_text = generate_gpt_request(prompt, text)
    print(summarise_text)

    # Generate complex request for extracting podcast details
    prompt = "Please extract the requested key details from this podcast."
    text = "Hello this is Tim Ferris and in this podcast we will be chatting with Harry Potter about his time at Hogwarts and his journey to vanquishing voldemort."
    functions = [{
        "type": "function",
        "function": {
            "name": "get_guest_name",
            "description": "Get the name of the guest speaker",
            "parameters": {
                "type": "object",
                "properties": {
                    "interviewer": {
                        "type": "string",
                        "description": "The name of the interviewer",
                    },
                    "guest": {
                        "type": "string",
                        "description": "The name of the guest speaker",
                    },
                    "topics": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "What topics are discussed?"
                        }
                    }
                },
                "required": ["interviewer", "guest", "topics"],
            },
        },
    }]

    podcast_deets = generate_gpt_request(prompt, text, functions)
    print(podcast_deets)