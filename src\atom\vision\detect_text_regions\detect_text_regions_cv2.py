import cv2
import pandas as pd
import numpy as np
import openpyxl
import os

import time
from functools import wraps

# Code Explanation
'''
Purpose:
    Detect text elements within images while filtering out table gridlines and other non-text elements.
    This detection is used to cross-reference against PyMuPDF text/annotation bounding boxes to identify
    cases where tables contain both text and image elements (e.g., in projects Axis 017 & Axis 018).

Method:
    This implementation uses Connected Component Analysis (CCA), which is particularly effective for
    text detection in structured documents for several reasons:
    - CCA treats connected pixels as single objects, making it ideal for identifying distinct text elements
    - Unlike contour detection or edge detection, CCA naturally groups related pixels into meaningful units
    - CCA is less sensitive to noise and grid lines compared to other methods like Hough transforms
    - The method provides useful statistics (area, width, height) that help filter out non-text elements

Process Overview:
    1. Image Preprocessing:
       - Convert to grayscale for simpler processing
       - Apply Gaussian blur to reduce noise
       - Create binary image using threshold to separate text from background

    2. Connected Component Analysis:
       - Use cv2.connectedComponentsWithStats() to identify distinct regions
       - Each component represents a potential text region
       - Component statistics are used to filter out non-text elements:
         * Area (filters out noise and very large regions)
         * Density (ensures enough dark pixels to be text)
         * Dimensions (removes lines and dots)

    3. Region Filtering and Sorting:
       - Filter regions based on size, density, and position criteria
       - Sort regions by their position (top-to-bottom, left-to-right)
       - Group nearby regions that likely belong together

    4. Visualization (Debug Mode):
       - Generate debug images showing each processing step
       - Draw bounding boxes around detected text regions

Key OpenCV Methods Used:
    - cv2.cvtColor(): Convert between color spaces (BGR to grayscale)
    - cv2.GaussianBlur(): Reduce image noise and detail
    - cv2.threshold(): Create binary image
    - cv2.connectedComponentsWithStats(): Perform connected component analysis
        * Returns: number of labels, label matrix, stats matrix, centroids
        * Stats include: left, top, width, height, area for each component
    - cv2.rectangle(): Draw bounding boxes for visualization
    - cv2.putText(): Add region labels in debug output

Why This Method Works Well:
    - Robust against table gridlines and other document elements
    - Computationally efficient compared to deep learning approaches
    - Provides good control over text region identification through filtering parameters
    - Simple to debug and adjust with visualization options
    - Reliable for structured documents with clear text/background separation

Usage:
    Should be run during file upload to identify areas requiring OCR processing.
    Debug mode generates visualization of each processing step for verification.
'''

# Add this timing decorator
def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not wrapper.debug:  # Skip timing if debug is False
            return func(*args, **kwargs)

        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        print(f"Function '{func.__name__}' took {end_time - start_time:.4f} seconds")
        return result

    wrapper.debug = False  # Default debug state
    return wrapper

def remove_grid_lines(thresh, size: int = 30):
    """Removes horizontal and vertical lines

    1. Copies threshold input image
    2. Detect horizontal lines
    3. Detect vertical lines
    4. Subtract horizontal and vertical lines from threshol image

    Params:
        thresh: Thresholded image
        size: Vary the horizontal and vertical structure

    Returns:
        Image with removed grid lines
    """
    horizontal = np.copy(thresh)
    vertical = np.copy(thresh)

    # Create structure element for extracting horizontal lines through morphology operations
    cols = horizontal.shape[1]
    horizontal_size = cols // size
    horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
    # Apply morphology operations
    horizontal = cv2.erode(horizontal, horizontalStructure)
    horizontal = cv2.dilate(horizontal, horizontalStructure)
    # Remove horizontal lines
    thresh = cv2.subtract(thresh, horizontal)

    # Create structure element for extracting vertical lines through morphology operations
    rows = vertical.shape[0]
    verticalsize = rows // size
    verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, verticalsize))
    # Apply morphology operations
    vertical = cv2.erode(vertical, verticalStructure)
    vertical = cv2.dilate(vertical, verticalStructure)
    # Remove horizontal lines
    thresh = cv2.subtract(thresh, vertical)

    return thresh

def detect_text_regions(image,
                        min_area=10,
                        remove_grid=True,
                        debug=False,
                        debug_dir=None,
                        debug_bottleneck=False):
    """
    Detect text regions using connected component analysis (CCA) method.
    """
    timer.debug_bottleneck = debug_bottleneck  # Set the debug_bottleneck state for the timer decorator

    start_total = time.time() if debug_bottleneck else 0

    # Read the image
    start = time.time() if debug_bottleneck else 0

    if isinstance(image, str):
        img = cv2.imread(image)
    else:
        img = image  # Expect a CV image

    if debug_bottleneck:
        print(f"Image reading took {time.time() - start:.4f} seconds")

    if img is None:
        raise ValueError("Could not read the image")

    print(f"Image dimensions: {img.shape}")
    img_height, img_width = img.shape[:2]

    # Time image processing operations
    start = time.time() if debug_bottleneck else 0

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply binary threshold - values should be ignored when using OTSU
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

    # Perform bitwise operation
    bitwise = cv2.bitwise_not(thresh)

    # Remove horizontal and vertical border and grid lines
    if remove_grid:
        img2 = remove_grid_lines(bitwise)
    else:
        img2 = bitwise

    # Horizontal dilation - this influences CCA into forming regions by word
    # and reduce grouping of vertical i.e. paragraphs or small regions
    dilated = cv2.dilate(img2, np.ones((1, 3), np.uint8), iterations=4)

    if debug_bottleneck:
        print(f"Image preprocessing took {time.time() - start:.4f} seconds")

    # Time connected component analysis
    start = time.time() if debug_bottleneck else 0
    # num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(dilated, connectivity=connectivity)
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStatsWithAlgorithm(dilated, 8, cv2.CV_32S, 0)
    if debug_bottleneck:
        print(f"Connected component analysis took {time.time() - start:.4f} seconds")

    # Time region extraction and filtering
    start = time.time() if debug_bottleneck else 0

    # Pre-calculate all component properties at once using NumPy operations
    areas = stats[:, cv2.CC_STAT_AREA]
    widths = stats[:, cv2.CC_STAT_WIDTH]
    heights = stats[:, cv2.CC_STAT_HEIGHT]
    left = stats[:, cv2.CC_STAT_LEFT]
    top = stats[:, cv2.CC_STAT_TOP]

    # Create boolean mask for size-based filtering
    max_area = img_width * img_height * 0.1
    size_mask = (
        (areas > min_area) &
        (areas < max_area) &
        (widths > 2) &
        (heights > 2)
    )

    # Get indices of components that pass size criteria (excluding background)
    valid_indices = np.where(size_mask)[0]
    valid_indices = valid_indices[valid_indices != 0]  # Remove background (label 0)

    # Extract regions that are likely to contain text
    text_regions = []
    labels_view = labels  # Create a view for faster access

    # Process only the valid components
    for label in valid_indices:
        x = left[label]
        y = top[label]
        w = widths[label]
        h = heights[label]

        # Calculate density using the original method but with optimized array access
        mask = (labels_view[y:y+h, x:x+w] == label)
        density = np.sum(mask) / (w * h)

        if density > 0.2:  # Has enough content
            text_regions.append((x, y, w, h))

    # Sort regions by position using the exact same criteria
    text_regions.sort(key=lambda r: (r[1] // 15, r[0]))

    if debug_bottleneck:
        print(f"Region extraction and filtering took {time.time() - start:.4f} seconds")

    # Save debug images if requested
    if debug and debug_dir:
        start = time.time() if debug_bottleneck else 0

        os.makedirs(debug_dir, exist_ok=True)

        # Save intermediate processing steps
        cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
        # cv2.imwrite(os.path.join(debug_dir, "2_blurred.png"), blurred)
        cv2.imwrite(os.path.join(debug_dir, "3_threshold.png"), thresh)

        # Create a color-coded visualization of components
        label_hue = np.uint8(179 * labels / np.max(labels))
        blank_ch = 255 * np.ones_like(label_hue)
        labeled_img = cv2.merge([label_hue, blank_ch, blank_ch])
        labeled_img = cv2.cvtColor(labeled_img, cv2.COLOR_HSV2BGR)
        labeled_img[label_hue == 0] = 0
        cv2.imwrite(os.path.join(debug_dir, "4_components.png"), labeled_img)

        # Draw bounding boxes for debug
        debug_img = img.copy()
        for x, y, w, h in text_regions:
            cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 1)
        cv2.imwrite(os.path.join(debug_dir, "5_detected_regions.png"), debug_img)

        if debug_bottleneck:
            print(f"Debug image saving took {time.time() - start:.4f} seconds")

    if debug_bottleneck:
        print(f"Total processing time: {time.time() - start_total:.4f} seconds")

    return text_regions

@timer
def visualize_regions(image_path, regions, output_path=None):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    try:
        img = cv2.imread(image_path)
    except Exception as e:
        img = image_path

    vis_img = img.copy()

    # Draw text regions with alternating colors
    for i, (x, y, w, h) in enumerate(regions):
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

    if output_path is None:
        return vis_img

    if not output_path.lower().endswith(('.png', '.jpg', '.jpeg')):
        output_path += '.png'

    cv2.imwrite(output_path, vis_img)
    return vis_img

if __name__ == "__main__":
    output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\outputImages\CV2"
    debug_dir = os.path.join(output_dir, "debug")

    # Get all page files in the directory
    page_files = sorted([f for f in os.listdir(output_dir) if f.startswith('page_') and f.endswith('.png')])

    # Create an empty list to store all regions data
    all_regions_data = []

    for page_file in page_files:
        # Extract page number from filename
        page_num = int(page_file.replace('page_', '').replace('.png', ''))
        image_path = os.path.join(output_dir, page_file)

        try:
            regions = detect_text_regions(
                image_path,
                min_area=10,
                debug=True,
                debug_dir=debug_dir,
                debug_bottleneck=True
            )

            # Convert regions to dataframe format
            for x, y, w, h in regions:
                all_regions_data.append({
                    'pdf_page': page_num,
                    'x0': x,
                    'y0': y,
                    'x1': x + w,
                    'y1': y + h,
                    'width': w,
                    'height': h
                })

            # Optional: Create visualization for each page
            output_path = os.path.join(output_dir, f"detected_regions_{page_num}.png")
            visualize_regions(image_path, regions, output_path)

        except Exception as e:
            print(f"Error processing page {page_num}: {str(e)}")

    # Create final dataframe
    df = pd.DataFrame(all_regions_data)

    # Sort by page number and reset index to get region numbers
    df = df.sort_values('pdf_page').reset_index(drop=True)

    print("\nFinal DataFrame:")
    print(df)

    # Export
    df.to_excel(os.path.join(output_dir, 'text_regions.xlsx'), index=True)

# if __name__ == "__main__":

#     pg = "1"
#     image_path = fr"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\outputImages\CV2\page_{pg}.png"

#     output_dir = os.path.dirname(image_path)
#     debug_dir = os.path.join(output_dir, "debug")

#     try:
#         regions = detect_text_regions(
#             image_path,
#             min_area=10,
#             debug=True,
#             debug_dir=debug_dir,
#             debug_bottleneck=True
#         )

#         print(f"Found {len(regions)} text regions:")
#         for i, (x, y, w, h) in enumerate(regions, 1):
#             print(f"Region {i}: x={x}, y={y}, width={w}, height={h}")

#         output_path = os.path.join(output_dir, f"detected_regions_{pg}.png")
#         visualize_regions(image_path, regions, output_path)
#         print(f"\nVisualization saved to: {output_path}")

#     except Exception as e:
#         print(f"Error processing image: {str(e)}")



# # Code works but is slow on large images
# def detect_text_regions_nonvectorized(image_path, min_area=10, debug=False, debug_dir=None, debug_bottleneck=False):
#     """
#     Detect text regions using connected component analysis (CCA) method.
#     """

#     timer.debug_bottleneck = debug_bottleneck  # Set the debug_bottleneck state for the timer decorator

#     start_total = time.time() if debug_bottleneck else 0

#     # Read the image
#     start = time.time() if debug_bottleneck else 0

#     # Read the image
#     img = cv2.imread(image_path)

#     if debug_bottleneck:
#         print(f"Image reading took {time.time() - start:.4f} seconds")

#     if img is None:
#         raise ValueError("Could not read the image")

#     print(f"Image dimensions: {img.shape}")
#     img_height, img_width = img.shape[:2]

#     # Time image processing operations
#     start = time.time() if debug_bottleneck else 0

#     # Convert to grayscale
#     gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

#     # Apply Gaussian blur to reduce noise
#     blurred = cv2.GaussianBlur(gray, (3, 3), 0)

#     # Apply binary threshold
#     _, thresh = cv2.threshold(blurred, 200, 255, cv2.THRESH_BINARY_INV)

#     if debug_bottleneck:
#         print(f"Image preprocessing took {time.time() - start:.4f} seconds")

#     # Time region extraction and filtering
#     start = time.time() if debug_bottleneck else 0

#     # Perform connected component analysis
#     num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(thresh, connectivity=8)

#     if debug_bottleneck:
#         print(f"Connected component analysis took {time.time() - start:.4f} seconds")

#     # Time region extraction and filtering
#     start = time.time() if debug_bottleneck else 0

#     # Extract regions that are likely to contain text
#     text_regions = []

#     # Skip label 0 as it's the background
#     for label in range(1, num_labels):
#         x = stats[label, cv2.CC_STAT_LEFT]
#         y = stats[label, cv2.CC_STAT_TOP]
#         w = stats[label, cv2.CC_STAT_WIDTH]
#         h = stats[label, cv2.CC_STAT_HEIGHT]
#         area = stats[label, cv2.CC_STAT_AREA]

#         # Calculate density of the component
#         mask = (labels == label).astype(np.uint8)
#         density = cv2.countNonZero(mask[y:y+h, x:x+w]) / (w * h)

#         # Filter components based on various criteria
#         if (area > min_area and
#             area < (img_width * img_height * 0.1) and  # Not too large
#             w > 2 and h > 2 and  # Not too small
#             density > 0.2):  # Has enough content

#             text_regions.append((x, y, w, h))

#     # Sort regions by position
#     text_regions.sort(key=lambda r: (r[1] // 15, r[0]))

#     if debug_bottleneck:
#         print(f"Region extraction and filtering took {time.time() - start:.4f} seconds")

#     # Save debug images if requested
#     if debug and debug_dir:

#         # Time debug image saving
#         start = time.time() if debug_bottleneck else 0

#         os.makedirs(debug_dir, exist_ok=True)

#         # Save intermediate processing steps
#         cv2.imwrite(os.path.join(debug_dir, "1_grayscale.png"), gray)
#         cv2.imwrite(os.path.join(debug_dir, "2_blurred.png"), blurred)
#         cv2.imwrite(os.path.join(debug_dir, "3_threshold.png"), thresh)

#         # Create a color-coded visualization of components
#         label_hue = np.uint8(179 * labels / np.max(labels))
#         blank_ch = 255 * np.ones_like(label_hue)
#         labeled_img = cv2.merge([label_hue, blank_ch, blank_ch])
#         labeled_img = cv2.cvtColor(labeled_img, cv2.COLOR_HSV2BGR)
#         labeled_img[label_hue == 0] = 0
#         cv2.imwrite(os.path.join(debug_dir, "4_components.png"), labeled_img)

#         # Draw bounding boxes for debug
#         debug_img = img.copy()
#         for x, y, w, h in text_regions:
#             cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 1)
#         cv2.imwrite(os.path.join(debug_dir, "5_detected_regions.png"), debug_img)

#         if debug_bottleneck:
#             print(f"Debug image saving took {time.time() - start:.4f} seconds")

#     if debug_bottleneck:
#         print(f"Total processing time: {time.time() - start_total:.4f} seconds")

#     return text_regions
