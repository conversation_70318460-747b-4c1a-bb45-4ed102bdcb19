'''
Handles file conversions and processing
'''

import os, base64, json

from src.utils.logger import logger

# Function to encode the image
def encode_image(image_path):
  with open(image_path, "rb") as image_file:
    return base64.b64encode(image_file.read()).decode('utf-8')


def clear_file(file_path):
    """Clear the contents of a file."""
    open(file_path, 'w').close()

def validate_paths(paths):
    for path_name, path in paths.items():
        if os.path.exists(path):
            if os.path.isdir(path):
                print(f"? Directory '{path_name}' is valid: {path}")
            elif os.path.isfile(path):
                print(f"? File '{path_name}' is valid: {path}")
        else:
            print(f"? Path '{path_name}' does not exist: {path}")

def load_json_file(file_path):
    try:
        with open(file_path, 'r') as file:
            content = file.read().strip()
            if not content:
                raise ValueError("JSON file is empty")
            return json.loads(content)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return []
    except Exception as e:
        print(f"Error loading JSON file {file_path}: {e}")
        return []




