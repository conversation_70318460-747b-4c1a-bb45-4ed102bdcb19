import sqlite3
import pandas as pd
from src.atom.dbManager import DatabaseManager
from src.app_paths import getDatabasePath
from src.atom.convert_excel_fields import convert_display_to_key


db_path = getDatabasePath()

def modify_table():
    # Connect to the SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Add new columns if they don't exist
    alter_statements = [
        "ALTER TABLE BOM ADD COLUMN item_count TEXT",
        "ALTER TABLE BOM ADD COLUMN item_length TEXT",
        "ALTER TABLE BOM ADD COLUMN total_length TEXT",
        "ALTER TABLE BOM ADD COLUMN shape TEXT"
        # "ALTER TABLE General ADD COLUMN isoNumber TEXT",
        # "ALTER TABLE General ADD COLUMN isoType TEXT"
        #"ALTER TABLE General ADD COLUMN paint_color TEXT"
        
    ]

    for statement in alter_statements:
        try:
            cursor.execute(statement)
            print(f"Added column: {statement.split()[-2]}")
        except sqlite3.OperationalError as e:
            print(f"Column already exists or error occurred: {e}")

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    print("Database connection closed.")


def update_rfq(df):
    db_manager = DatabaseManager()
    try:
        db_manager.update_rfq(df)
    except:
        db_manager.insert_dataframe(df, "RFQ")

if __name__ == "__main__":
    convert_columns = False
    modify_db_table = True

    if convert_columns:
        # Load the Excel workbook into a DataFrame
        excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Employee Testing\TEST 3 - Heartwell (AX_0044)\Unclassified.xlsx"

        df = pd.read_excel(excel_file_path)

        

        # convert to db field names
        converted_general_df = convert_display_to_key(df)

        # Save the converted data back to Excel
        with pd.ExcelWriter(excel_file_path, engine='xlsxwriter') as writer:
            converted_general_df.to_excel(writer, sheet_name='General', index=False)

        print("Original columns:", df.columns.tolist())
        print("Converted columns:", converted_general_df.columns.tolist())

        ### --> RUN
        update_rfq(converted_general_df)  # Use converted dataframe instead of original

    if modify_db_table:
        modify_table()



