# Requires-Python >=3.7,<3.11
annotated-types==0.6.0
anyio==4.2.0
certifi==2024.2.2
colorama==0.4.6
distro==1.9.0
et-xmlfile==1.1.0
exceptiongroup==1.2.0
h11==0.14.0
httpcore==1.0.3
httpx==0.27.2
idna==3.6
numpy==1.26.4
openai==1.35.15
openpyxl==3.1.2
pandas==2.2.0
#pip==22.0.4
# pydantic_core==2.16.2 <- Causes issue in requirements.txt
pydantic==2.6.1
# TODO - Need to handle paddleOCR version PyMuPDF<1.21.0
# https://github.com/PaddlePaddle/PaddleOCR/discussions/12795#discussioncomment-9696870
PyMuPDF==1.24.8
PyMuPDFb==1.24.8

#PySide6_Addons==6.6.2
#PySide6_Essentials==6.6.2
PySide6==6.6.2
python-dateutil==2.8.2
python-dotenv==1.0.1
pytz==2024.1
#setuptools==58.1.0
shiboken6==6.6.2
six==1.16.0
sniffio==1.3.0
tqdm==4.66.2
typing_extensions==4.9.0
tzdata==2024.1
pyside6-drawer==0.0.1
Pypubsub==4.0.3
pytest==8.2.1
pytest-qt==4.4.0
platformdirs==4.2.2
greenlet==3.0.3
SQLAlchemy==2.0.28
pyinstaller==5.13.2
firebase_admin

# AI classifier
aiohttp==3.8.6
aiosignal==1.3.1
async-timeout==4.0.3
attrs==23.1.0
# certifi==2023.7.22
charset-normalizer==3.3.2
frozenlist==1.4.0
# idna==3.4
multidict==6.0.4
regex==2023.10.3
requests==2.31.0
tiktoken==0.5.1
# urllib3==2.0.7  # Version dependency confilcis with 2.0.7
yarl==1.9.2
# pip==22.0.4
# setuptools==58.1.0
# python-dotenv==1.0.1
# et-xmlfile==1.1.0
# numpy==1.26.4
# openpyxl==3.1.2
# pandas==2.2.1
# python-dateutil==2.9.0.post0
# pytz==2024.1
# six==1.16.0
# tzdata==2024.1
keyring==25.2.0
#pypiwin32; platform_system == "Windows"
pywin32; platform_system == "Windows"
# phonenumbers
phonenumbers==8.13.37
#platformdirs==4.2.2
XlsxWriter==3.2.0

# extract symbols
matplotlib==3.9.0
Shapely==2.0.4
unidecode==1.3.8
pypdf==4.3.1

# AWS
# boto3==1.35.68
# boto3[crt]==1.35.68 <-- Install manually. This causes timeout issues
amazon-textract-textractor==1.8.5

scipy #==1.14.1
opencv-contrib-python==*********
debugpy==1.8.9  # helps with debugging QThread in vscode

# python-certifi-win32; platform_system == "Windows"
pip_system_certs==4.0
pyarrow==18.1.0
natsort==8.4.0
psycopg2
winsdk>=1.0.0b10
rtree
polars>=1.13.0

PyPDF2==3.0.1

structlog
orjson
qasync
google-genai==1.13.0

fastexcel
scikit-learn
