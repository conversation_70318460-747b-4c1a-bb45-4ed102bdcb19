"""
Given AWS OCR results, determine angles and perform regex on values

Categorize values and directions
"""

import json
import math
import boto3
import io
import fitz
import cv2
import unicodedata
import re
from difflib import SequenceMatcher

from PIL import Image
import pandas as pd

from src.atom.sourcepreprocess import page_to_opencv
from data_conversions import convert_quantity_to_float

def safe_json_loads(item):
    if isinstance(item, str):
        try:
            return json.loads(item)
        except json.JSONDecodeError:
            return item  # Return the original string if it's not valid JSON
    return item  # Return the item as is if it's not a string

def angle_between_points(p1, p2):
    """
    Calculates the angle between two points.
    Args:
        p1: The first point.
        p2: The second point.
    Returns:
        The angle between the two points in degrees.

    Note: same function as calculate_angle but supply two co-ords
    instead of line
    """
    x1, y1 = p1
    x2, y2 = p2
    dx = x2 - x1
    dy = y2 - y1
    angle = math.degrees(math.atan2(dy, dx))
    return (angle + 360) % 360  # Normalize angle to 0-360 range


def run_text_detection(filename: str,
                       page_num: int,
                       region: list[float] = [0, 0, 1, 1],
                       zoom: float = 2) -> dict:
    """Returns the raw response from AWS rekognition with original params

    rekognition seems to handle rotated text better

    Note: This returns relative values to the cropped image.
          So conversion needed to return back to full image

    Args:
        filename: PDF file
        page_num: 1-indexed page number
        region: Crop the isometric region using relative coords (x0, y0, x1, y1)
        zoom: fitz upscale
    """
    client = boto3.client('rekognition')

    x0, y0, x1, y1 = region

    doc = fitz.open(filename)
    page = doc[page_num - 1]

    page_width = page.rect.width
    page_height = page.rect.height
    cvImage = page_to_opencv(page, zoom=zoom)

    x0 = int(x0 * page_width * zoom)
    y0 = int(y0 * page_height * zoom)
    x1 = int(x1 * page_width * zoom)
    y1 = int(y1 * page_height * zoom)

    cropImage = cvImage[y0:y1, x0:x1]

    image_bytes = io.BytesIO()
    Image.fromarray(cropImage).save(image_bytes, format='JPEG')
    image_bytes = image_bytes.getvalue()

    response = client.detect_text(
        Image={
            "Bytes": image_bytes,
        }
    )

    data = {
        "response": response,
        "params": {
            "filename": filename,
            "page_num": page_num,
            "region": region,
            "zoom": zoom,
        }
    }
    return data

def run_text_detection_cv(image: cv2.Mat) -> dict:
    """Returns the raw response from AWS rekognition with original params

    rekognition seems to handle rotated text better

    Note: This returns relative values to the cropped image.
          So conversion needed to return back to full image

    Args:
        filename: PDF file
        image: CV2 Mat Image
    """
    client = boto3.client('rekognition')

    image_bytes = io.BytesIO()
    Image.fromarray(image).save(image_bytes, format='JPEG')
    image_bytes = image_bytes.getvalue()

    response = client.detect_text(
        Image={
            "Bytes": image_bytes,
        }
    )

    return response

def map_point_to_full_size(point, region):
    """
    Maps a point that is relative to a region back to coordinates relative to full_size.

    Args:
        point (list): [x, y] coordinates relative to region
        region (list): [x0, y0, x1, y1] coordinates of region relative to full_size
        full_size (list): [x0, y0, x1, y1] base coordinates

    Returns:
        list: [x, y] coordinates relative to full_size
    """
    # Calculate region's actual width and height
    region_width = region[2] - region[0]
    region_height = region[3] - region[1]

    # Map point to actual position within region
    x = region[0] + (point[0] * region_width)
    y = region[1] + (point[1] * region_height)

    return [x, y]

def process_response(data: dict) -> pd.DataFrame:
    """Convert to DataFrame. Given params, convert coords back to full image"""

    response: dict = data["response"]
    params: dict = data["params"]

    page_num = params["page_num"]
    filename = params["filename"]
    region = params["region"]

    records = []
    textDetections = response['TextDetections']
    for text in textDetections:
        data = {
            "pdf_page": page_num,
            "filename": filename,
        }
        data.update(dict(text))

        geom = text["Geometry"]
        bbox = geom["BoundingBox"]

        bbox_tl = (bbox["Left"], bbox["Top"])
        bbox_br = (bbox["Left"] + bbox["Width"], bbox["Top"] + bbox["Height"])

        bx0, by0 = map_point_to_full_size(bbox_tl, region)
        bx1, by1 = map_point_to_full_size(bbox_br, region)

        data["x0"] = bx0
        data["y0"] = by0
        data["x1"] = bx1
        data["y1"] = by1

        polygon = geom["Polygon"]

        points = []
        for pt in polygon:
            x = pt["X"]
            y = pt["Y"]
            point = (x, y)
            mapped = map_point_to_full_size(point, region)
            points.append(mapped)

        data["polygon"] = points

        del data["Geometry"]

        records.append(data)

    df = pd.DataFrame(records)
    return df


def draw_analysis(filename: str, page_num: int, data: pd.DataFrame, region=None) -> cv2.Mat:
    """Returns cv2 image"""

    doc = fitz.open(filename)
    page = doc[page_num - 1]

    page_width = page.rect.width
    page_height = page.rect.height
    zoom = 2

    # Draw results
    colors = [
        (0, 255, 0), # green
        (0, 255, 255), # yellow
        (0, 0, 255), # red
        (255, 0, 0), # blue
    ]

    image = page_to_opencv(page, zoom=zoom)
    for row in data.itertuples():
        polygon = row.polygon
        points = []
        for n, pt in enumerate(polygon):
            x, y = pt
            x, y = int(x * page_width * zoom), int(y * page_height * zoom)
            radius = 3
            thickness = 1
            point = (x, y)
            points.append(point)
            cv2.circle(image, (x, y), radius, colors[n], thickness)

        angle = angle_between_points(points[0], points[1])
        angle = int(angle)

        # font
        font = cv2.FONT_HERSHEY_SIMPLEX
        # fontScale
        fontScale = 0.3
        # Blue color in BGR
        color = (255, 0, 0)
        # Line thickness of 2 px
        thickness = 1

        cv2.putText(image, f'{angle}', points[0], font, fontScale, color, thickness, cv2.LINE_AA)

    if region:
        x0, y0, x1, y1 = region
        x0 = int(x0 * page_width * zoom)
        y0 = int(y0 * page_height * zoom)
        x1 = int(x1 * page_width * zoom)
        y1 = int(y1 * page_height * zoom)
        # Crop the image
        image = image[y0:y1, x0:x1]

    return image


def preprocess_text(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = ''.join(c for c in unicodedata.normalize('NFD', text) if unicodedata.category(c) != 'Mn')
    text = re.sub(r'[^\w\s]', '', text)
    text = ' '.join(text.split())
    return text


def find_similar_keyword_match(text, keywords, threshold: float = 0.8):
    """Returns keyword if meets similarity threshold"""
    if pd.isna(text) or not isinstance(text, str):
        return None

    processed_text = preprocess_text(text)

    if not processed_text:
        return None

    for keyword in keywords:
        if SequenceMatcher(None, preprocess_text(keyword), processed_text).ratio() >= threshold:
            return keyword

    return None

def analyze(data: pd.DataFrame,
            page_num: int,
            line_number_df: pd.DataFrame = pd.DataFrame()) -> pd.DataFrame:
    """
    Categorizes data
    """
    print("Analyzing...")
    data = data[data["page"] == page_num]

    analysis = []

    line_number_df = line_number_df.sort_values("page")
    try:
        page_line_number = line_number_df[line_number_df["page"] == page_num]["lineNumber"].iloc[0]
    except:
        page_line_number = None

    # .iloc[0]

    for row in data.itertuples():
        points = row.polygon

        angle = angle_between_points(points[0], points[1])
        angle = int(angle)

        category1 = ""
        category2 = ""
        category3 = ""
        other_value = ""
        reference = ""

        value = str(row.DetectedText)

        if find_similar_keyword_match(value, ["CONN TO", "CONN. TO"]) or value == "CONN":
            category1 = "connection"
        elif find_similar_keyword_match(value, ["CONT. ON", "CONT ON", "CONT"]) or value == "CONT":
            category1 = "continuation"
            category2 = "to"
        elif find_similar_keyword_match(value, ["CONT. FROM", "CONT FROM", "CONT"]) or value == "CONT":
            category1 = "continuation"
            category2 = "from"
        elif any(["'" in value, '"' in value]):
            category1 = "measurement"
            if "EL" in value:
                category1 = "elevation"
            other_value = convert_quantity_to_float(value)

        if value.startswith("DRG") and page_line_number:
            # number = value.split("")
            category1 = "reference"
            category2 = "drg"

            # Find pages with same line number, and get indexed page by drg N
            line_numbers = line_number_df[line_number_df["lineNumber"] == page_line_number]
            try:
                drg_number = int(value.split(" ")[1])
                other_value = f"{page_line_number} - ({drg_number})"
                # e.g. if DRG == 2, look for 2nd result
                reference = line_numbers.iloc[int(drg_number)].iloc[drg_number-1]["page"]
            except:
                print("Drag Number found, but corresponding page")
                reference = "Not found"

        else:
            for row2 in line_number_df.itertuples():
                if row2.lineNumber == value:
                    category1 = "reference"
                    category2 = "page"
                    reference = row2.page
                    break

        if value == "FW":
            category1 = "weld"
            category2 = "fw"

        angle_facing = ""
        if 260 < angle < 275:
            angle_facing = "north/south"
            category2 = "height"
        elif 305 < angle < 325:
            angle_facing = "northeast/southwest"
            category2 = "width"
        elif 35 < angle < 50:
            angle_facing = "northwest/southeast"
            category2 = "width"

        record = {
            "page": row.page,
            "filename": row.filename,
            "value": value,
            "type": row.Type,
            "confidence": round(row.Confidence, 2),
            "x0": row.x0,
            "y0": row.y0,
            "x1": row.x1,
            "y1": row.y1,
            "polygon": row.polygon,
            "angle": angle,
            "angle_facing": angle_facing,
            "category1": category1,
            "category2": category2,
            "category3": category3,
            "other_value": other_value,
            "ParentId": row.ParentId,
            "reference": reference,
        }
        analysis.append(record)

    analysisDf = pd.DataFrame(analysis)
    return analysisDf


if __name__ == "__main__":

    filename = r""
    page_num = 1
    isometric_region = []
    zoom = 2

    # Run Text detection
    print("Running AWS text detection")
    response = run_text_detection(filename, page_num, isometric_region, zoom)


    print("Returned response. Processing...")
    # Converts to Dataframe and maps relative coords to full image
    df = process_response(response)


    print("Saved to processed response df to 'debug/processed_response.xlsx'")
    df.to_excel("debug/processed_response.xlsx")


    print("Running analysis on data")
    # Categorize response
    result: pd.DataFrame = analyze(filename, page_num, df)


    print("Saved to analysis df to 'debug/isometric_analysis.xlsx'")
    result.to_excel("debug/isometric_analysis.xlsx", index=False)


    # Output analysis results onto cv image
    image = draw_analysis(filename, page_num, result)

    cv2.imshow("Analysis Result", image)
    cv2.waitKey()
