from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from pubsub import pub
from src.pyside_util import get_resource_qicon, applyDropShadowEffect


values = [25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85,
          90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140,
          145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 195,
          200, 205, 210, 215, 220, 225, 230, 235, 240, 245, 250,
          255, 260, 265, 270, 275, 280, 285, 290, 295, 300, 305,
          310, 315, 320, 325, 330, 335, 340, 345, 350, 355, 360,
          365, 370, 375, 380, 385, 390, 395, 400]


class ZoomStatusWidget(QWidget):

    sgnUpdateStatus = Signal(object)
    def __init__(self):
        super().__init__()
        self.setLayout(QHBoxLayout())

        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("minus.svg"))
        pb.clicked.connect(self.onZoomOut)
        self.layout().addWidget(pb)
        self.slider = QSlider(orientation=Qt.Orientation.Horizontal)
        self.slider.setMinimum(0)
        self.slider.setMaximum(len(values) - 1)
        self.slider.setValue(14)
        self.slider.setObjectName("tableZoomSlider")
        self.slider.valueChanged.connect(self.onSlider)
        self.layout().addWidget(self.slider)
        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("plus.svg"))
        pb.clicked.connect(self.onZoomIn)
        self.layout().addWidget(pb)

        self.value = 100
        self.lblZoom = QLabel(f"{self.value}%")
        self.lblZoom.setFixedWidth(50)
        self.layout().addWidget(self.lblZoom)
        # self.setObjectName("zoomSlider")
        self.setToolTip("Table Zoom Slider")

        self.setFixedWidth(256)
        self.setFixedHeight(32)

        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        pub.subscribe(self.onStatus, "update-statusbar-table-zoom")

        self.hide()

    @property
    def scaleDelta(self):
        return self.parent().scaleDelta

    @property
    def minScale(self):
        return self.parent().minScale

    @property
    def maxScale(self):
        return self.parent().maxScale

    def onZoomIn(self):
        self.slider.setValue(self.slider.value() + 1)
        self.onSlider()

    def onZoomOut(self):
        self.slider.setValue(self.slider.value() - 1)
        self.onSlider()

    def onSlider(self):
        value = self.slider.value()
        try:
            value = values[value] / 100
        except Exception as e:
            print(value, e)
            return
        pub.sendMessage("set-table-zoom", value=value)

    def onStatus(self, data):
        self.sgnUpdateStatus.emit(data)

    def onUpdateStatus(self, data):
        if data is None:
            return
        value = data["value"] * 100
        value = int(round(value, 2))
        index = values.index(value)

        self.slider.setValue(index)
        self.lblZoom.setText(f"{value}%")
