import os
import logging
import time

import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor
from psycopg2.extensions import connection, cursor
from dotenv import load_dotenv
from typing import Dict, Any, Optional, Union
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file if it exists
load_dotenv()

class DatabaseConfig:
    """Configuration class for database connection parameters."""

    def __init__(self,
                 host: str = None,
                 port: int = None,
                 database: str = None,
                 user: str = None,
                 password: str = None,
                 sslmode: str = None,
                 min_connections: int = 1,
                 max_connections: int = 10):
        """
        Initialize database configuration with provided parameters or environment variables.

        Args:
            host: Database host address
            port: Database port
            database: Database name
            user: Database user
            password: Database password
            sslmode: SSL mode for connection (e.g., 'require', 'prefer', 'disable')
            min_connections: Minimum number of connections in the pool
            max_connections: Maximum number of connections in the pool
        """
        # Use provided values or fall back to environment variables
        self.host = host or os.getenv("PG_HOST", "localhost")
        self.port = port or int(os.getenv("PG_PORT", "5432"))
        self.database = database or os.getenv("PG_DATABASE", "atom_db")
        self.user = user or os.getenv("PG_USER", "postgres")
        self.password = password or os.getenv("PG_PASSWORD", "")
        self.sslmode = sslmode or os.getenv("PG_SSLMODE", "require")  # Default to 'require' for security
        self.min_connections = min_connections or int(os.getenv("PG_MIN_CONNECTIONS", "1"))
        self.max_connections = max_connections or int(os.getenv("PG_MAX_CONNECTIONS", "10"))

    def get_connection_params(self) -> Dict[str, Any]:
        """Return connection parameters as a dictionary."""
        return {
            "host": self.host,
            "port": self.port,
            "database": self.database,
            "user": self.user,
            "password": self.password,
            "sslmode": self.sslmode
        }

    def get_dsn(self) -> str:
        """Return connection string in DSN format."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?sslmode={self.sslmode}"

    def __str__(self) -> str:
        """Return a string representation with password masked."""
        params = self.get_connection_params()
        params["password"] = "********" if params["password"] else ""
        return str(params)


class DatabaseConnectionError(Exception):
    """Custom exception for database connection errors."""
    pass


class PostgresConnectionPool:
    """
    Manages a pool of PostgreSQL database connections.
    """
    _instance = None

    def __new__(cls, config: Optional[DatabaseConfig] = None):
        """Singleton pattern to ensure only one connection pool exists."""
        if cls._instance is None:
            cls._instance = super(PostgresConnectionPool, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config: Optional[DatabaseConfig] = None):
        """Initialize the connection pool if not already initialized."""
        if self._initialized:
            return

        self.config = config or DatabaseConfig()
        self._pool = None
        self._max_retries = 3
        self._retry_delay = 2  # seconds
        self._initialized = True

        # Initialize the connection pool
        self._create_pool()

    def _create_pool(self) -> None:
        """Create the connection pool."""
        try:
            logger.info(f"Creating connection pool to {self.config.host}:{self.config.port}/{self.config.database}")
            self._pool = pool.ThreadedConnectionPool(
                minconn=self.config.min_connections,
                maxconn=self.config.max_connections,
                **self.config.get_connection_params()
            )
            logger.info("Connection pool created successfully")
        except psycopg2.Error as e:
            logger.error(f"Failed to create connection pool: {e}")
            raise DatabaseConnectionError(f"Failed to create connection pool: {e}")

    def get_connection(self) -> connection:
        """
        Get a connection from the pool with retry logic.

        Returns:
            A database connection from the pool.

        Raises:
            DatabaseConnectionError: If unable to get a connection after retries.
        """
        retries = 0
        last_error = None

        while retries < self._max_retries:
            try:
                if self._pool is None:
                    self._create_pool()

                conn = self._pool.getconn()
                if conn:
                    return conn
            except (psycopg2.Error, pool.PoolError) as e:
                last_error = e
                logger.warning(f"Connection attempt {retries + 1} failed: {e}")
                retries += 1
                if retries < self._max_retries:
                    time.sleep(self._retry_delay)
                    # Attempt to recreate the pool if it might be stale
                    if retries == self._max_retries - 1:
                        try:
                            self._create_pool()
                        except Exception:
                            pass

        error_msg = f"Failed to get database connection after {self._max_retries} attempts"
        logger.error(error_msg)
        if last_error:
            error_msg += f": {last_error}"
        raise DatabaseConnectionError(error_msg)

    def return_connection(self, conn: connection) -> None:
        """
        Return a connection to the pool.

        Args:
            conn: The connection to return to the pool.
        """
        if self._pool is not None and conn is not None:
            self._pool.putconn(conn)

    def close_all(self) -> None:
        """Close all connections in the pool."""
        if self._pool is not None:
            self._pool.closeall()
            logger.info("All connections in the pool have been closed")
            self._pool = None


@contextmanager
def get_db_connection(config: Optional[DatabaseConfig] = None) -> connection:
    """
    Context manager for getting a database connection.

    Args:
        config: Optional database configuration.

    Yields:
        A database connection.

    Raises:
        DatabaseConnectionError: If a connection cannot be established.
    """
    pool_instance = PostgresConnectionPool(config)
    conn = None

    try:
        conn = pool_instance.get_connection()
        yield conn
    except Exception as e:
        if conn and not conn.closed:
            conn.rollback()
        raise DatabaseConnectionError(f"Database connection error: {e}")
    finally:
        if conn:
            pool_instance.return_connection(conn)


@contextmanager
def get_db_cursor(config: Optional[DatabaseConfig] = None,
                  cursor_factory: Any = RealDictCursor,
                  autocommit: bool = False) -> cursor:
    """
    Context manager for getting a database cursor.

    Args:
        config: Optional database configuration.
        cursor_factory: The cursor factory to use (default: RealDictCursor).
        autocommit: Whether to enable autocommit mode.

    Yields:
        A database cursor.

    Raises:
        DatabaseConnectionError: If a cursor cannot be obtained.
    """
    with get_db_connection(config) as conn:
        if autocommit:
            conn.autocommit = True

        curr = None
        try:
            curr = conn.cursor(cursor_factory=cursor_factory)
            yield curr
            if not autocommit:
                conn.commit()
        except Exception as e:
            if not autocommit and conn and not conn.closed:
                conn.rollback()
            raise DatabaseConnectionError(f"Database cursor error: {e}")
        finally:
            if curr:
                curr.close()


def execute_query(query: str,
                  params: Optional[Union[Dict[str, Any], tuple]] = None,
                  config: Optional[DatabaseConfig] = None,
                  fetch: str = "all") -> Any:
    """
    Execute a SQL query and return the results.

    Args:
        query: The SQL query to execute.
        params: Query parameters (dict for named parameters, tuple for positional).
        config: Optional database configuration.
        fetch: What to fetch - "all", "one", "many", or None for no fetch (e.g., for INSERT/UPDATE).

    Returns:
        Query results based on the fetch parameter.

    Raises:
        DatabaseConnectionError: If the query execution fails.
    """
    with get_db_cursor(config) as cursor:
        try:
            cursor.execute(query, params)

            if fetch == "all":
                return cursor.fetchall()
            elif fetch == "one":
                return cursor.fetchone()
            elif fetch == "many":
                return cursor.fetchmany()
            elif fetch is None:
                return cursor.rowcount
            else:
                raise ValueError(f"Invalid fetch option: {fetch}")
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise DatabaseConnectionError(f"Query execution error: {e}")


def execute_transaction(queries: list,
                        params_list: Optional[list] = None,
                        config: Optional[DatabaseConfig] = None) -> bool:
    """
    Execute multiple queries in a single transaction.

    Args:
        queries: List of SQL queries to execute.
        params_list: List of parameters for each query (can be None, dict, or tuple).
        config: Optional database configuration.

    Returns:
        True if the transaction was successful.

    Raises:
        DatabaseConnectionError: If the transaction fails.
    """
    if params_list is None:
        params_list = [None] * len(queries)

    if len(queries) != len(params_list):
        raise ValueError("Number of queries must match number of parameter sets")

    with get_db_connection(config) as conn:
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for i, (query, params) in enumerate(zip(queries, params_list)):
                    cursor.execute(query, params)
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            logger.error(f"Transaction failed: {e}")
            raise DatabaseConnectionError(f"Transaction failed: {e}")


def test_connection(config: Optional[DatabaseConfig] = None) -> bool:
    """
    Test the database connection.

    Args:
        config: Optional database configuration.

    Returns:
        True if the connection is successful, False otherwise.
    """
    try:
        with get_db_connection(config) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result[0] == 1
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return False


# Example usage
if __name__ == "__main__":
    # Test the connection
    if test_connection():
        print("Database connection successful!")

        # Example query
        try:
            result = execute_query("SELECT version()", fetch="one")
            print(f"PostgreSQL version: {result['version']}")
        except Exception as e:
            print(f"Query failed: {e}")
    else:
        print("Failed to connect to the database.")