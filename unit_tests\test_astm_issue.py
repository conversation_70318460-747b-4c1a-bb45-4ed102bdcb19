"""
Test script to debug ASTM extraction issues with pipe anchor descriptions.
This script tests the normalize_astm.py logic on specific problematic cases.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from normalize_astm import create_astm_tags, parse_astm_and_grade
import pandas as pd

def test_problematic_descriptions():
    """Test the specific problematic descriptions that are incorrectly extracting ASTM values."""

    test_cases = [
        {
            'description': 'A4 DIRECTIONAL PIPE ANCHOR FOR INSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
            'expected_astm': None,  # Should not extract any ASTM
            'expected_grade': None,  # Should not extract any grade
            'issue': 'Incorrectly selecting A420 as ASTM'
        },
        {
            'description': 'A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
            'expected_astm': None,  # Should not extract any ASTM
            'expected_grade': None,  # Should not extract any grade
            'issue': 'Incorrectly selecting A36 as ASTM'
        },
        {
            'description': 'ASTM A106 GR B PIPE',  # Control case - should work correctly
            'expected_astm': 'A106',
            'expected_grade': 'B',
            'issue': 'Control case - should work correctly'
        }
    ]

    print("=== TESTING PROBLEMATIC ASTM EXTRACTION ===\n")

    for i, test_case in enumerate(test_cases, 1):
        description = test_case['description']
        expected_astm = test_case['expected_astm']
        expected_grade = test_case['expected_grade']
        issue = test_case['issue']

        print(f"Test Case {i}:")
        print(f"Description: {description}")
        print(f"Expected ASTM: {expected_astm}")
        print(f"Expected Grade: {expected_grade}")
        print(f"Issue: {issue}")
        print("-" * 80)

        # Test the create_astm_tags function
        tags, match_types, review = create_astm_tags(description)

        print(f"Results:")
        print(f"  Tags: {tags}")
        print(f"  Match Types: {match_types}")
        print(f"  Review: {review}")

        # Parse the results
        extracted_astm = None
        extracted_grade = None

        if tags:
            tag_parts = tags.split(', ')
            for tag in tag_parts:
                if tag.startswith('astm:'):
                    extracted_astm = tag[5:]  # Remove 'astm:' prefix
                elif tag.startswith('grade:'):
                    extracted_grade = tag[6:]  # Remove 'grade:' prefix

        print(f"  Extracted ASTM: {extracted_astm}")
        print(f"  Extracted Grade: {extracted_grade}")

        # Check if results match expectations
        astm_correct = (extracted_astm == expected_astm)
        grade_correct = (extracted_grade == expected_grade)

        print(f"  ASTM Correct: {astm_correct}")
        print(f"  Grade Correct: {grade_correct}")

        if not astm_correct or not grade_correct:
            print(f"  ❌ FAILED: Expected ASTM={expected_astm}, Grade={expected_grade}")
            print(f"           Got ASTM={extracted_astm}, Grade={extracted_grade}")

            # If review tags don't indicate uncertainty, that's a problem
            if not review or 'review:' not in review:
                print(f"  ⚠️  WARNING: No review tags indicating uncertainty!")
        else:
            print(f"  ✅ PASSED")

        print("\n" + "=" * 80 + "\n")

def debug_parse_function():
    """Debug the parse_astm_and_grade function step by step."""

    problematic_descriptions = [
        'A4 DIRECTIONAL PIPE ANCHOR FOR INSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
        'A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000'
    ]

    print("=== DEBUGGING parse_astm_and_grade FUNCTION ===\n")

    for desc in problematic_descriptions:
        print(f"Debugging: {desc}")
        print("-" * 60)

        # Call the parse function directly
        tags, match_types, review = parse_astm_and_grade(desc)

        print(f"Raw Results:")
        print(f"  Tags: {tags}")
        print(f"  Match Types: {match_types}")
        print(f"  Review: {review}")
        print()

def analyze_pattern_matching():
    """Analyze what patterns are being matched in the problematic descriptions."""

    import re
    from normalize_astm import pattern, official_astm_set

    problematic_descriptions = [
        'A4 DIRECTIONAL PIPE ANCHOR FOR INSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
        'A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000'
    ]

    print("=== ANALYZING PATTERN MATCHING ===\n")
    print(f"ASTM Pattern: {pattern}")
    print(f"Official ASTM Set (first 10): {list(official_astm_set)[:10]}")
    print()

    for desc in problematic_descriptions:
        print(f"Analyzing: {desc}")
        print("-" * 60)

        # Test the main ASTM pattern
        match = re.search(pattern, desc, re.IGNORECASE)
        if match:
            print(f"Pattern Match Found:")
            print(f"  Full match: {match.group(0)}")
            print(f"  Group 1 (main_code): {match.group(1)}")
            print(f"  Group 2 (appended_code): {match.group(2)}")
            print(f"  Group 3 (pattern_grade): {match.group(3)}")
        else:
            print("No pattern match found")

        # Check fallback token matching
        tokens = re.split(r"[^A-Za-z0-9\-]+", desc.upper())
        tokens = [t for t in tokens if t]
        print(f"Tokens: {tokens}")

        # Check which tokens might match ASTM codes
        for i, token in enumerate(tokens):
            if token in official_astm_set:
                print(f"  Token '{token}' at position {i} matches ASTM set")
            elif token.isdigit() and ("A" + token) in official_astm_set:
                print(f"  Token '{token}' at position {i} would match as 'A{token}' in ASTM set")
                print(f"    A{token} is in ASTM set: {'A' + token in official_astm_set}")

        print()

def test_additional_variations():
    """Test additional variations to see if we can reproduce the issue."""

    variations = [
        'A4 DIRECTIONAL PIPE ANCHOR FOR INSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
        'A3 DIRECTIONAL PIPE ANCHOR FOR UNINSULATED PIPE (PREFERRED), SEE DOC.NO. PIP-STD-3000',
        'A420 DIRECTIONAL PIPE ANCHOR',  # Test if A420 gets extracted
        'A36 DIRECTIONAL PIPE ANCHOR',   # Test if A36 gets extracted
        'DIRECTIONAL PIPE ANCHOR A4',    # Test with A4 at the end
        'DIRECTIONAL PIPE ANCHOR A3',    # Test with A3 at the end
    ]

    print("=== TESTING ADDITIONAL VARIATIONS ===\n")

    for desc in variations:
        print(f"Testing: {desc}")
        tags, match_types, review = create_astm_tags(desc)
        print(f"  Tags: {tags}")
        print(f"  Match Types: {match_types}")
        print(f"  Review: {review}")
        print()

def check_astm_set_contents():
    """Check what's actually in the ASTM set."""
    from normalize_astm import official_astm_set

    print("=== CHECKING ASTM SET CONTENTS ===\n")

    # Check if A420, A36, A4, A3 are in the set
    test_codes = ['A420', 'A36', 'A4', 'A3']

    for code in test_codes:
        in_set = code in official_astm_set
        print(f"{code} in ASTM set: {in_set}")

    print(f"\nTotal ASTM codes in set: {len(official_astm_set)}")
    print(f"Sample ASTM codes: {sorted(list(official_astm_set))[:20]}")
    print()

def test_cross_category_validation():
    """Test the cross-category validation to prevent conflicts like rating:300 and astm:A300."""
    from normalize_description import validate_cross_category_conflicts

    print("=== TESTING CROSS-CATEGORY VALIDATION ===\n")

    test_cases = [
        {
            'name': 'Rating/ASTM Conflict',
            'metadata_tags': ['rating:300', 'astm:A300'],
            'review_tags': [],
            'expected_conflict': True
        },
        {
            'name': 'No Conflict',
            'metadata_tags': ['rating:150', 'astm:A106'],
            'review_tags': [],
            'expected_conflict': False
        },
        {
            'name': 'Schedule/Rating Conflict',
            'metadata_tags': ['schedule:40', 'rating:40'],
            'review_tags': [],
            'expected_conflict': True
        }
    ]

    for test_case in test_cases:
        print(f"Test: {test_case['name']}")
        print(f"Input tags: {test_case['metadata_tags']}")

        cleaned_tags, updated_review = validate_cross_category_conflicts(
            test_case['metadata_tags'],
            test_case['review_tags']
        )

        print(f"Cleaned tags: {cleaned_tags}")
        print(f"Review tags: {updated_review}")

        conflict_detected = len(updated_review) > len(test_case['review_tags'])
        print(f"Conflict detected: {conflict_detected}")
        print(f"Expected conflict: {test_case['expected_conflict']}")

        if conflict_detected == test_case['expected_conflict']:
            print("✅ PASSED")
        else:
            print("❌ FAILED")

        print("-" * 60)
        print()

if __name__ == "__main__":
    print("ASTM Extraction Issue Debugging\n")

    # Run all tests
    test_problematic_descriptions()
    debug_parse_function()
    analyze_pattern_matching()
    test_additional_variations()
    check_astm_set_contents()
    test_cross_category_validation()
