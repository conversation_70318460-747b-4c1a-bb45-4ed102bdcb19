"""
The column order defaults for tables

Populate list with all fields which may appear in each table scope
"""

default_column_order = {}


default_column_order["BOM"] = [
    "sys_path",
    "pdf_page",
    "pos",
    "ident",
    "size",
    "material_description",
    "quantity",
    "item",
    "item_count",        # ADD THIS
    "item_length",       # ADD THIS
    "total_length",      # ADD THIS
    "shape",             # ADD THIS
    "material_code",
    "sch_class",
    "parts",
    "type",
    "weight",
    "number",
    "remarks",
    "ident_code",
    "material_scope",
    "componentCategory",
    "rating_sch",
    "tag",
    "pdf_id",
    "status",
    "rfq_scope",
    "general_category",
    "size1",
    "size2",
    "quantity_original",
    "size_original",
    "unit_of_measure",
    "material",
    "abbreviated_material",
    "ansme_ansi",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    "forging",
    "ends",
    "item_tag",
    "tie_point",
    "pipe_category",
    "valve_type",
    "fitting_category",
    "weld_category",
    "bolt_category",
    "gasket_category",
    "ef",
    "sf",
    "drawing",
    "sheet",
    "pid",
    "has_revision",
]


default_column_order["SPOOL"] = [
    "sys_path",
    'pdf_id',
    'pdf_page',
    'cutPiece',
    'length',
    'spool',
    'spec',
    'size',
]

default_column_order["SPEC"] = [
    "sys_path",
    "pdf_id",
    "pdf_page",
    "dp1",
    "dp2",
    "dt1",
    "dt2",
    "op1",
    "op2",
    "opt1",
    "opt2",
    "pipeSpec",
    "size",
]

default_column_order["General"] = [
    "sys_path",
    "pdf_id",
    "pdf_page",
    "annotMarkups",
    "area",
    "avg_elevation",
    "blockCoordinates",
    "clientDocumentId",
    "clean_spec",
    "coordinates",
    "cwp",
    "designCode",
    "documentDescription",
    "documentId",
    "documentTitle",
    "dp1",
    "dp2",
    "dt1",
    "dt2",
    "drawing",
    "elevation",
    "flangeID",
    "heatTrace",
    "insulationSpec",
    "insulationThickness",
    "isoNumber",
    "iso_size",
    "isoType",
    "lineNumber",
    "max_elevation",
    "mediumCode",
    "min_elevation",
    "op1",
    "op2",
    "opt1",
    "opt2",
    "paintSpec",
    "paint_color",
    "pid",
    "pipeSpec",
    "pipeStandard",
    "processLineList",
    "processUnit",
    "projectNo",
    "projectName",
    "pwht",
    "revision",
    "sequence",
    "service",
    "sheet",
    "size",
    "sysDocument",
    "sysDocumentName",
    "sys_filename",
    "system",
    "test_type",
    "test_psi",
    "test_temp",
    "totalSheets",
    "unit",
    "vendorDocumentId",
    "weldId",
    "weldClass",
    "xCoord",
    "xray",
    "yCoord",
    "lf",
    "sf",
    "ef",
    "elbows_90",
    "elbows_45",
    "bevels",
    "tees",
    "reducers",
    "caps",
    "flanges",
    "valves_flanged",
    "valves_welded",
    "cut_outs",
    "supports",
    "bends",
    "field_welds",
    "calculated_eq_length",
    "calculated_area",
    "union_couplings",
    "expansion_joints",
    "pmi_req",
    "paut_req",
    "hardness_req",
    "flange_guard",
    "continued_on",
    "connects_to",
    "pickling_req",
    "flushing_req",
    "pipeline_num",
    "weight",
    "general_field_1",
    "general_field_2",
    "general_field_3",
    "general_field_4",
    "general_field_5",
    "general_field_6",
    "general_field_7",
    "general_field_8",
    "general_field_9",
    "general_field_10",
    "general_field_11",
    "general_field_12",
    "general_field_13",
    "general_field_14",
    "general_field_15",
]

default_column_order["RFQ"] = [
    'material_description',
    'size',
    'quantity',
    'size1',
    'size2',
    'componentCategory',
    'ef',
    'sf',
    'rfq_scope',
    'unit_of_measure',
    'schedule',
    'rating',
    'astm',
    'grade',
    'ansme_ansi',
    'material',
    'abbreviated_material',
    'coating',
    'forging',
    'ends',
    'item_tag',
    'tie_point',
    'pipe_category',
    'valve_type',
    'general_category',
    'fitting_category',
    'weld_category',
    'bolt_category',
    'gasket_category',
    'answer_explanation',
    'review',
    'review_explanation',
    'last_updated',
]


default_column_order["IFC"] = [
    "sys_path",
    "pdf_id",
    "pdf_page",
    "issue_no",
    "issue_date",
    "issue_description",
    "approved_by",
    "drawn_by",
    "checked_by"
]

default_column_order["generic_1"] = [
    "sys_path",
    "pdf_id",
    "pdf_page",
    "field_1",
    "field_2",
    "field_3",
    "field_4",
    "field_5",
    "field_6",
    "field_7",
    "field_8",
    "field_9",
    "field_10",
    "field_11",
    "field_12",
    "field_13",
    "field_14",
    "field_15",
    "field_16",
    "field_17",
    "field_18",
    "field_19",
    "field_20",
    "field_21",
    "field_22",
    "field_23",
    "field_24",
    "field_25",
    "field_26",
]

default_column_order["generic_2"] = [
    "sys_path",
    "pdf_id",
    "pdf_page",
    "field_1",
    "field_2",
    "field_3",
    "field_4",
    "field_5",
    "field_6",
    "field_7",
    "field_8",
    "field_1",
    "field_2",
    "field_3",
    "field_4",
    "field_5",
    "field_6",
    "field_7",
    "field_8",
    "field_9",
    "field_10",
    "field_11",
    "field_12",
    "field_13",
    "field_14",
    "field_15",
    "field_16",
    "field_17",
    "field_18",
    "field_19",
    "field_20",
    "field_21",
    "field_22",
    "field_23",
    "field_24",
    "field_25",
    "field_26",
]

default_column_order["LookupTable"] = [
    'Category',
]