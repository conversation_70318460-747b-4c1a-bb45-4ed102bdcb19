/* Global or shared */


QSlider#zoomSlider {
  background-color: @grey2;
  border-radius: 12px;
  border-color: green;
}

/* END */

#navSidebar {
  background-color: @sidebar.background;
}

#navSidebarSeparater {
  background-color: @atom.orange;
}

/* Vertical line indicator when sidebar hidden */
#sidebarVline {
  background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0.0 @panel.background,
                    stop: 0.001 @panel.background,
                    stop: 0.0011 @atom.orange,
                    stop: 0.3 @atom.orange,
                    stop: 0.31 @atom.lightorange,
                    stop: 0.69 @atom.lightorange,
                    stop: 0.997 @atom.orange,
                    stop: 0.998 @panel.background,
                    stop: 1 @panel.background
                    );
}

QPushButton#navSidebar {
  background-color: @sidebar.background;
}

QPushButton:navSidebar:hover {
  background-color: @sidebar.background;
}

QWidget#skipped {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.1,
        fy: -0.1,
        radius: 1.35, stop: 0 #001526, stop: 1 rgb(2, 28, 49)
    );
  color: @white;
  border: none; /* For windows - hides unintended white borders */
}

QWidget {
  background-color: transparent;
  color: @grey1;
  border: none; /* For windows - hides unintended white borders */
}

QWidget::pane {
  border: none; /* For windows - hides unintended white borders */
}

/* Main background color */
#panel {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.1,
        fy: -0.1,
        radius: 1.35, stop: 0 @panel.background, stop: 1 @panel.background.light
    );
  color: @black;
}

/* END */

/* Status bar */

QStatusBar {
  background: qradialgradient(
      cx: 0.1,
      cy: -0.1,
      fx: 0.4,
      fy: -0.4,
      radius: 1.35, stop: 0 @color7.light, stop: 1 @color7.dark
  );
  border-color: transparent;
  border: 2px;
  color: transparent;
  font: 12px;
}

#statusSpacer {
  background: transparent;
}

QStatusBar::item {
  border: 0px solid red;
  border-radius: 3px;
}

QStatusBar::sizeGrip {
  background: @color7;
  border-color: white;
  border: 2px;
  color: red;
}

/* END */

#titleLabel {
  font-weight: bold;
  font-size: 32px;
}

#headerLabel {
  font-weight: normal;
  font-size: 18px;
}

QLabel {
  color: @foreground;
}

/* Forms */

#formButton {
  text-align: center;
  font-weight: bold;
}


QLabel#formBackground {  /* The background image behind a form */
  background-color: @grey3;
}

QLabel#formError {
  color: red;
  font: italic;
}

#formError {
}


/* Tables START */

QTableView {
  background-color: @input.background;
  selection-background-color: orange; /* Table cell background color */
  selection-color: black;
  font: 12px;
  color: white;
}

QTableCornerButton::section {
  background: @sidebar.background.light;
  border: none;
}

QTableView::pane {
  background-color: @input.background.light;
}

QTableView#frozenTable {
  border-right: 1px dashed @atom.orange;
}

QTableView::indicator:hover {
 border: 1px red;
}

QTableView::indicator:checked {
  image: url(src/resources/check-square.svg);
}
QTableView::indicator:unchecked {
  image: url(src/resources/square.svg);
}

QTableView::item:focus{
  border: 2px solid @atom.orange;
  background-color: @atom.activecell;
  color: rgb(0, 0, 0);
}

QTableView::item:selected{
  gridline-color: rgba(255,0,0,0.0);
}

QHeaderView {
  background: @sidebar.background.lighter;
  qproperty-defaultAlignment: AlignCenter;
}

QHeaderView::section { /* Column and row header background */
  background: @sidebar.background.lighter;
  color: @white;
  font: 13px bold;
  min-width: 48px;
  min-height: 28px;
  text-align: center;
}

/* Tables END */

#formWidget {
  background-color: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.5, stop: 0 @panel.background, stop: 1 @panel.background.light
  );
  color: @foreground;
  border-color: black;
}

/* TODO - Merge these input widgets together */

QSpinBox {
  background-color: @input.background;
}
QLineEdit {
  min-height: 8ex;
  background-color: @secondary.background;
}
QLineEdit#pager {
  min-height: 6ex;
  background-color: @secondary.background;
}
QTextEdit {
  background-color: @secondary.background;
}
QComboBox {
  background-color: @secondary.background;
}
QComboBox::drop-down {
  border: none;
  width: 24px;
}

QComboBox::down-arrow {
    image: url(src/resources/chevron-down.svg);
    border: none;
}
QComboBox QAbstractItemView {
    background: @secondary.background;
    border-radius: 0px;
    color: white;
    selection-background-color: @color5;
}

QAbstractItemView#completerPopup {
    background: red;
    border-radius: 0px;
    color: white;
    selection-background-color: @color5;
}

QLineEdit, QSpinBox, QComboBox {
    border: 1px solid @grey4;
    border-radius: 4px;
    background: @secondary.background;
    color: white;
    selection-background-color: @color5;
}

QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
    border: 3px solid @color4;
    background: @secondary.background.light;
}

QLineEdit[text=""] { color: white; }

QCheckBox {
  color: white;
}

/* TODO - Checked style background without removing the checkmark */
QCheckBox::indicator:unchecked {
  background-color: @input.background.lighter;
}

/* TODO: Error specific */
QLineEdit#formError {
  border-color: red;
  border-width: 1px;
}

#pinDigit {
  font: 24px bold;
  text-align: center;
}

/* END */
QLabel#sidebarLogo {
  background-color: @sidebar.background;
  margin: 0px;
  border: 4px;
  border-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f6f7fa, stop: 1 #dadbde);
}

/* Projects Tree in Navigation sidebar */

#projectsTree {
  background-color: @sidebar.background;
  font-weight: normal;
  font-size: 14px;
  border-bottom: 1px solid white;
  border-top: 0px solid white;
  border-right: 0px solid white;
  border-left: 0px solid white;
}

/* END */

/* GraphicsView, Viewports, PDF Viewer */

QGraphicsView {
}

QWidget#pdfViewer {
  border: 3px solid @color4;
  border-radius: 4px;
  background-color: qradialgradient(
        cx: 0.3,
        cy: -0.4,
        fx: 0.3,
        fy: -0.4,
        radius: 1.35, stop: 0 #9a979b, stop: 1 #9a979b
  );
}

QWidget#pdfViewer:disabled {
  border: 2px solid @grey4;
  border-radius: 4px;
}

/* END */

QPushButton#navSidebarButton {
  background-color: @sidebar.background;
  border-radius: 1px;
  border: 0px;
  padding: 0px;
  padding-right: 4px;
  text-align: left;
}

QPushButton#navSidebarButton:hover {
  background-color: @sidebar.hover;
  border: 0px;
}

QPushButton#navSidebarButton:pressed {
  background-color: @sidebar.hover;
  border: 0px;
  border-radius: 2px;
}

QPushButton#navSidebarButton:checked {
  background-color: @sidebar.hover;
  border-right: 1px solid @atom.lightorange;
}

QPushButton:enabled {
  background-color: qradialgradient(
      cx: 0.4,
      cy: -0.4,
      fx: 0.4,
      fy: -0.4,
      radius: 0.2, stop: 0 @color6, stop: 1 @color6.lighter
  );
  border-radius: 4px
}

QPushButton:enabled:hover {
  background-color: qradialgradient(
      cx: 0.4,
      cy: -0.4,
      fx: 0.4,
      fy: -0.4,
      radius: 0.2, stop: 0 @color6.light, stop: 1 @color6
  );
}

QPushButton:enabled:pressed {
    background-color: @button.pressedBackground;
}

QPushButton:disabled {
    background-color: @color7;
    border-radius: 4px
}

QPushButton:checked {
  color: white;
  background-color: green;
  border-radius: 8px
}

QToolBar {
  background-color: @toolbar.background;
  border: 0px;
  padding: 0px;
  margin: 0px;
}

QWidget#topbar {
  background-color: qradialgradient(
        cx: 0.8,
        cy: -0.8,
        fx: 0.1,
        fy: -0.1,
        radius: 0.8, stop: 0 @topbar.background, stop: 1 @topbar.background
    );
  border-bottom: 1px solid @topbar.background.light;
}


QPushButton#toolbarLogo {
  background-color: qradialgradient(
        cx: 0.3,
        cy: -0.3,
        fx: 0.4,
        fy: -0.4,
        radius: 1.5, stop: 0 @atom.orange, stop: 1 @atom.lightorange
    );
  margin: 0px;
  border: 0px;
  border-radius: 0px;
  font: bold;
  color: white;
}
QPushButton#toolbarLogo:hover {
  background-color: @atom.orange;
}
QPushButton#toolbarLogo:pressed {
  background-color: @atom.lightorange;
}
QPushButton#toolbarLogo:checked {
  background-color: qradialgradient(
        cx: 0.3,
        cy: -0.3,
        fx: 0.4,
        fy: -0.4,
        radius: 1.5, stop: 0 @atom.lightorange, stop: 1 @atom.orange
    );
  color: #f4f4f4;
}

/* Top level menubar */
QPushButton#toolbarButton {
  background-color: transparent;
  color: @toolbar.foreground;
  padding-left: 12px;
  padding-right: 8px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  border-radius: 0px;
}

QPushButton#toolbarButton::disabled {
  color: grey;
}

QPushButton#toolbarButton::menu-indicator {
  width: 0px;
  background: green;
}

QPushButton#toolbarButton::menu-indicator::disabled {
  background: green;
  foreground: blue;
}

QMenu, #subMenu {
  background-color: qradialgradient(
        cx: 0.3,
        cy: -0.3,
        fx: 0.4,
        fy: -0.4,
        radius: 1.5, stop: 0 @secondary.background.light, stop: 1 @secondary.background.light
    );
  border-radius: 2px;
  padding-top: 4px;
  padding-bottom: 4px;
  font: 16px;
  color: white;
}

QMenu::item:disabled {
  color: grey;
}

QMenuBar:hover {
  background-color: @white;
  border-radius: 2px;
}

QMenu:selected {
  background-color: @toolbarButton.hoverBackground;
  border-bottom: 1px solid @white;
  border-radius: 2px;
}

QPushButton#toolbarButton:hover {
  background-color: @toolbarButton.hoverBackground;
  color: @toolbar.foreground;
}

QPushButton#toolbarButton:checked {
  background-color: @toolbarButton.hoverBackground;
  color: @toolbar.foreground;
}

/* Tab Widget projectTabs - START */

#projectTabs::pane {
  background: @sidebar.background;
}


QPushButton#projectTabs {
  border-radius: 0px;
  background-color: @sidebar.background;
  padding-left: 2ex;
  padding-right: 2ex;
  font: 13px bold;
}

QFrame#projectTabs {
  border-radius: 0px;
  background-color: @sidebar.background;
}

#projectTabsDivider {
  background: 2px solid qlineargradient(spread: pad, x1:0, y1:0, x2:0, y2:1,
          stop: 0 @sidebar.background,
          stop: 0.3 @sidebar.background,
          stop: 0.31 red,
          stop: 0.7 red,
          stop: 0.71 @sidebar.background,
          stop: 1 @sidebar.background);
}

QLabel#projectTabs {
  /* tab divider line */
  background: @sidebar.background;
  border-right: 1px solid qlineargradient(spread: pad, x1:0, y1:0, x2:0, y2:1,
          stop: 0 @sidebar.background,
          stop: 0.3 @sidebar.background,
          stop: 0.31 white,
          stop: 0.7 white,
          stop: 0.71 @sidebar.background,
          stop: 1 @sidebar.background);
  padding-left: 2px;
  padding-right: 2px;
}

QPushButton#projectTabs::checked {
  background: 1px solid qlineargradient(spread: pad, x1:0.0, y1:0, x2:0, y2:1,
          stop: 0 @sidebar.background,
          stop: 0.1 @sidebar.background,
          stop: 0.11 red,
          stop: 0.9 white,
          stop: 0.91 @sidebar.background,
          stop: 1 @sidebar.background);
  background: @white;
  color: black;
  border-top: 3px solid;
  border-color: @atom.orange;
  font: 13px bold;
}


/* Tab Widget projectTabs - START */

/* Tab Widget - START */

QTabWidget {
  border: 0px;
}

QTabWidget::pane { /* The tab widget frame */
  border: none;
  background: none;
}

QTabBar {
  background-color: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.2, stop: 0 @sidebar.background.light, stop: 1 @sidebar.background
  );
}

/* Style the tab using the tab sub-control. Note that it reads QTabBar _not_ QTabWidget */
QTabBar::tab {
    min-height: 8ex;
    border: none;
    border-bottom: 0px solid transparent;

    border-bottom-left-radius: 8ex;
    border-bottom-right-radius: 8ex;
    border-radius: 8ex;
    min-width: 8ex;
    padding-left: 2ex;
    padding-right: 2ex;
    margin-bottom: 0ex;
    margin-top: 0ex;
    font: 12ex;
}

QTabBar::tab:selected {
    background: qradialgradient(
        cx: 0.2,
        cy: -0.2,
        fx: 0.2,
        fy: -0.2,
        radius: 1.35, stop: 0 @atom.lightorange, stop: 1 @atom.orange
    );
    padding-left: 2ex;
    padding-right: 2ex;
    color: white;
    border: 1px solid transparent;
}

QTabBar::tab:!selected:hover {
    background: qradialgradient(
        cx: 0.5,
        cy: -0.5,
        fx: 0.2,
        fy: -0.2,
        radius: 1, stop: 0 @atom.orange, stop: 1 @atom.lightorange
    );
    color: @grey1;
    border: 1px solid @atom.lightorange;
}

QTabBar::tab:!selected {
    background: transparent;
    color: @grey4;
}


/* Tab Widget - END */

QPushButton#hover:pressed
{
  background-color: rgb(224, 255, 0);
  border-radius: 8px
}

QPushButton#hover:!pressed
{
  background-color: rgb(224, 255, 0);
  border-radius: 8px
}

QListWidget {
    background: transparent;
    font-weight: normal;
    font-size: 16px;
    color: white;
}
QListWidgetItem {
    background : white;
    font-weight: normal;
    font-size: 24px;
}

QWidget#AWidget {
  background-color: green;
  border-radius: 8px
}

/* Document Viewer, thumbnails */

#documentThumbnail {
  background: @input.background;
}

QWidget#overlayHint {
  background: green;
  border-radius: 8px;
}

QPushButton#documentThumbnail:hover {
  background: @white;
}

#documentThumbnail {
  border-radius: 2px;
  border: 2px black;
}

QTreeView::item:selected {
  font: bold;
}

QScrollbar {
  background: white;
}

/* Scrollbars */
QScrollBar:vertical {
  border: 0px;
  background: @input.background;
  width: 8px;
}

QScrollBar:horizontal {
  border: 0px;
  background: @input.background;
  height: 8px;
}

QScrollBar::handle {
  background: blue; /*qlineargradient(x1:0, y1:0, x2:1, y2:0, stop: 0 rgb(32, 47, 130), stop: 0.5 rgb(32, 47, 130), stop:1 rgb(32, 47, 130));*/
  min-height: 24px;
}

QScrollBar::handle:vertical {
  border-radius: 4px;
  min-height: 48px;
}

QScrollBar::handle:horizontal {
  border-radius: 4px;
  min-width: 48px;
}

/* Hide scroll arrow */
  QScrollBar::handle {  /* dark mode scroll handle */
  background: grey;
}

QScrollBar::add-line:vertical {
  height: 0px;
}

QScrollBar::sub-line:vertical {
  height: 0px;
}

QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
  height: 0px;
}

QScrollBar::add-line:horizontal {
  width: 0px;
}

QScrollBar::sub-line:horizontal {
  width: 0px;
}

QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
  width: 0px;
}

/* END */

#openDrawerButton {
  background-color: @atom.orange;
}
#openDrawerButton:hover {
  background-color: @atom.lightorange;
}
#openDrawerButton:pressed {
  background-color: @atom.lightorange;
  border-radius: 8px;
}

QWidget#popup {
  background-color: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.2, stop: 0 @panel.background, stop: 1 @panel.background.light
  );
  border-radius: 8px;
  border: 1px solid @panel.background.light
}

#cellEditor {
  border-color: @atom.orange;
  border: 2px solid;
  color: red;
}

#jobQueueCard {
  border-color: @atom.orange;
  border: 1px solid @atom.orange;
  border-radius: 4px;
}

#jobQueueCardError {
  border-color: red;
  border: 1px solid red;
  border-radius: 4px;
}

#jobQueueCardOk {
  border-color: green;
  border: 1px solid green;
  border-radius: 4px;
}

#engineerDrafterValue {
  background-color: @input.background;
  border: 3px solid red;
  border-radius: 4px;
}

/* Checkbox */

QSplashScreen {
  font-size: 12px;
}

#helpPopup {
  border-color: @atom.orange;
  border: 3px solid @atom.orange;
  border-radius: 2px;
  background: @sidebar.background;
}

QListWidget#ColumnOrganizer {
  border-radius: 2px;
  border: 1px solid white;
}

QSlider#tableZoomSlider:groove:horizontal {
  background: @color7;
}

QSlider#tableZoomSlider:handle:horizontal {
  background: @atom.orange;
  width: 8px;
  height: 20px;
  border-radius: 1px;
}

/* Forms */
QGroupBox#formWidget {
  border-radius: 8px;
  padding: 2px;
}

QGroupBox {
  background-color: transparent;
  border-radius: 8px;
  border-color: @atom.orange;
  border: 1px solid white;
  margin-top: 1ex;
  padding: 1ex;
}

/* More standard looking QGroupBox */
QGroupBox::title {
  font-size: 10px;
  subcontrol-origin: margin;
}

QTableWidget::indicator {
  image: url(src/resources/square.svg);
}

QTableWidget::indicator:checked {
  image: url(src/resources/check-square.svg);
}

QRadioButton::indicator {
  image: url(src/resources/square.svg);
  width: 16px;
  height: 16px;
}

QRadioButton::indicator:checked {
  image: url(src/resources/check-square.svg);
}

QPushButton#MyButtonSidebar:checked {
  border-radius: 0px;
  background-color: green;
}

QPushButton#MyButtonSidebar {
  padding-left: 28px;
  text-align: left;
  background-color: @sidebar.background;
  border-radius: 0px;
}

QPushButton#MyButtonSidebar:hover:!checked {
  background-color: @color6;
  border-radius: 0px;
}

QPushButton#MyButtonSidebar {
  border-radius: 0px;
}

/* Hides the indicator when menu set */
QPushButton#menuButton1::menu-indicator{
  width: 0px;
}

QPushButton#MyButtonPrimary {
  background-color: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.2, stop: 0 @color6, stop: 1 @color6.light
  );
  border-radius: 6px;
  font: 16px;
  color: white;
}

QPushButton#MyButtonSecondary::menu-indicator{
  width: 0px;
}

QPushButton#MyButtonMenu::menu-indicator{
  width: 0px;
}

QPushButton#MyButtonPrimary:hover {
  background: @color6
}

QPushButton#MyButtonPrimary:checked {
  background: green
}

QPushButton#MyButtonDanger {
  background: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.2, stop: 0 @red, stop: 1 @red
  );
  opacity: 0.6;
}


QPushButton#MyButtonSecondary {
  background-color: transparent;
  border: 0px solid white;
  border-radius: 6px;
  font: 16px;
  color: white;
  font-weight: normal;
}

QPushButton#MyButtonSecondary:hover {
  background: @color6;
  color: @grey1;
  border-radius: 7px;
}

QPushButton#MyButtonMenu {
  background-color: transparent;
  border-radius: 6px;
  font: 16px;
  color: @color6;
}

QPushButton#MyButtonMenu:hover {
  background: @color6;
  color: @grey1;
  border-radius: 7px;
}

QPushButton#MyButton::disabled {
  color: darkgrey;
  background: @color7;
}

QToolTip {
  background-color: qradialgradient(
        cx: 0.4,
        cy: -0.4,
        fx: 0.4,
        fy: -0.4,
        radius: 0.2, stop: 0 @color5, stop: 1 @color5.light
  );
  color: white;
  border: black solid 2px;
  border-radius: 16px;
  padding: 8px;
  font: 14px;
}

/* Any background of a sidebar item */
#primarySidebar {
  background-color: @sidebar.background;
}

QTableWidget::item#myTableWidget {
  background-color : none;
  border: none;
}


#background {
  background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 @grey6,
        stop:1 @grey5);
  margin: 0px;
  padding: 0px;
}

#toolbar {
  border-bottom: 0px solid @grey5;
  border-top: 0px solid @grey5;
}

#temp {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.1,
        fy: -0.1,
        radius: 1.35, stop: 0 #001526, stop: 1 rgb(2, 28, 49)
    );
}

#temp {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.1,
        fy: -0.1,
        radius: 1.35, stop: 0 #001526, stop: 1 #001526
    );
  border-radius: 2px;
}

QSplitter {
  margin: 0px;
  padding: 0px;
  border: none;
}

QWidget#groupPageSection {
  background-color: @secondary.background;
  border-radius: 2px;
  border-bottom: 1px solid @secondary.background.dark;
}

#secondaryBackground {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.2,
        fy: -0.2,
        radius: 0.2, stop: 0 @secondary.background.dark, stop: 1 @secondary.background.darker
    );
}

QWidget#groupPageSection:hover {
  background-color: @secondary.background.lighter;
}

QMessageBox, QDialog {
  background-color: qradialgradient(
        cx: 0.1,
        cy: -0.1,
        fx: 0.4,
        fy: -0.4,
        radius: 1.35, stop: 0 @topbar.background.light, stop: 1 @topbar.background.dark
    );
  font: 16px;
  min-width: 256px;
}

QTreeView {
    background-color: @input.background;
}

QTreeView::branch:has-children {
    background-color: @input.background;
}

/* Or if you want to style the entire row of parent items */
QTreeView::item:has-children {
  font-weight: bold;
  alternate-background-color: @input.background;
  background-color: @input.background;
}

QTreeView::item:alternate {
    background-color: @input.background;
}

QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
    border-image: none;
    image: none;
    padding: 2px;
}

QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
    border-image: none;
    image: none;
    background: transparent url(src/resources/chevron-right.svg) center no-repeat;
}

QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings {
    border-image: none;
    image: none;
    background: transparent url(src/resources/chevron-down.svg) center no-repeat;
}

QTreeView::item:selected {
    background-color: @atom.orange;  /* background when selected */
    color: black;      /* text color when selected */
}

/* Optional: Style for hover state */
QTreeView::item:hover {
  color: black;      /* text color when selected */
  background-color: @atom.lightorange;
}

/* Optional: Style for both selected and hover */
QTreeView::item:selected:hover {
  color: black;      /* text color when selected */
    background-color: @atom.lightorange;
}

/* Optional: Style for active (pressed) state */
QTreeView::item:selected:active {
    color: black;      /* text color when selected */
    background-color: @atom.lightorange;
}

