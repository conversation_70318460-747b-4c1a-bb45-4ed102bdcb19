
import os
import pandas as pd

def plugin_concat_files(
        input_1_file: str,
        input_2_file: str,
        input_3_file: str,
        input_4_file: str,
        input_5_file: str,
        input_6_file: str,
        input_7_file: str,
        input_8_file: str,
        save_file="debug/concatenated_data.xlsx",
        # sort_key = "sys_path,pdf_page"
        # sort=True
        ):

    input_files = [input_1_file, input_2_file, input_3_file, input_4_file, input_5_file, input_6_file, input_7_file, input_8_file]
    input_files = [fn for fn in input_files if fn]

    if not input_files:
        return "must specify file paths to concat"

    df = pd.concat([pd.read_excel(f) for f in input_files], ignore_index=True)
    # if sort:
    #     df = df.sort_values(by=["sys_path", "pdf_page"], key=natsort_keygen())

    df.to_excel(save_file, index=False)

    return f"Saved to {save_file}"