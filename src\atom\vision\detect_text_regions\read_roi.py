def extract_roi_data_from_json(json_data):
  """
  Reads field labels and coordinates from ROI data within groups in a JSON object.

  Args:
    json_data: A Python dictionary loaded from the JSON file content.

  Returns:
    A dictionary where keys are group IDs and values are lists of 
    dictionaries, each containing the name and coordinates for an ROI
    within that group. Returns an empty dictionary if the structure is invalid 
    or no groups/ROIs are found.
  """
  extracted_data = {}
  if not isinstance(json_data, dict) or 'groups' not in json_data:
    print("Invalid JSON structure: 'groups' key not found.")
    return extracted_data

  groups = json_data.get('groups', {})
  if not isinstance(groups, dict):
      print("Invalid JSON structure: 'groups' is not a dictionary.")
      return extracted_data

  for group_id, group_content in groups.items():
    if not isinstance(group_content, dict) or 'rois' not in group_content:
      print(f"Skipping group '{group_id}': 'rois' key not found or invalid format.")
      continue
      
    rois = group_content.get('rois', [])
    if not isinstance(rois, list):
        print(f"Skipping group '{group_id}': 'rois' is not a list.")
        continue

    group_rois_data = []
    for roi in rois:
       # Check if roi is a dictionary and contains the required keys
      if isinstance(roi, dict) and all(k in roi for k in ['name', 'relativeX0', 'relativeY0', 'relativeX1', 'relativeY1']):
        roi_info = {
            'name': roi.get('name'), # Field label
            'coordinates': {         # Coordinates
                'x0': roi.get('relativeX0'),
                'y0': roi.get('relativeY0'),
                'x1': roi.get('relativeX1'),
                'y1': roi.get('relativeY1')
            },
            'isTable': roi.get('isTable', False), # Check if it's a table
             # Include table specific info if present and isTable is True
            'columnNames': roi.get('columnNames', []) if roi.get('isTable') else [],
            'columnRatios': roi.get('columnRatios', []) if roi.get('isTable') else []
        }
        group_rois_data.append(roi_info)
      else:
          print(f"Skipping an ROI in group '{group_id}' due to missing keys or invalid format.")

    if group_rois_data:
      extracted_data[group_id] = group_rois_data
            
  return extracted_data
