"""
Purely raw data analysis

"""
import cv2
import fitz
import ast
import re
import math

import numpy as np
import pandas as pd

from data_conversions import convert_quantity_to_float
from shapely.geometry import Polygon
from src.utils import convert_roi_payload
from src.app_paths import getSourceRawDataPath
from src.utils.pdf.page_to_opencv import page_to_opencv
from scripts.cv2_detection_utils import feature_detection
from itertools import combinations


filename = r"C:\Drawings\Joey\S1601 Insulation Only (1).pdf"
projectId = 30

page_width, page_height = [2448, 1584]
relative_isometric_area = [0.04411764705882353, 0.017676767676767676, 0.6834150326797386, 0.7986111111111112]
absolute_isometric_area = [relative_isometric_area[0] * page_width,
                           relative_isometric_area[1] * page_height,
                           relative_isometric_area[2] * page_width,
                           relative_isometric_area[3] * page_height]

CATEGORY_PIPE = "pipe"
SIZE_VALUE = "size"
CROSS_SIZE = "cross_size"
FIELD_WELD = "field_weld"
PIPE_SIZE = "pipe_size"
VALVE = "valve"
QUANTITY = "quantity"

color_map = {
    QUANTITY: (0, 1, 0),
    SIZE_VALUE: (1, 0.5, 0),
    CROSS_SIZE: (0, 0, 1),
}


def safe_literal_eval(coord_str):
    if isinstance(coord_str, np.ndarray):
        return tuple(coord_str)
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

ignore_terms = [
    "insul:",
    "02CG1E-M",
    "REST ON STEEL",
    "NONE",
    "N"
]

ignore_contains = [
    "REF : DETAIL",
]

ignore_startswith = [
    "E ",
    "N ",
    "EL ",
    "SEE ISO",
    "FG-"
]

bom_labels = ["1", "2", "3", "4"]


categories = [
    "bom_label",
    "pipe_size",
    "valve",
    "quantity",
    "field_weld",
    "span",
    "pipe_qty",
    "cross_size"
]

def is_isometric(row):
    return 1 if (row["x1"] >= absolute_isometric_area[0] and row["x1"] <= absolute_isometric_area[2] and
            row["y1"] >= absolute_isometric_area[1] and row["y1"] <= absolute_isometric_area[3]) else 0

def is_size_value(value):
    if not value:
        return 0
    return 1 if value.lower() in [PIPE_SIZE, CROSS_SIZE] else 0

def assign_category(value):
    if any(val.lower() == value.lower() for val in bom_labels):
        return "bom_label"

    if value.lower().endswith('"NPD'.lower()):
        if "x" in value.lower():
            return CROSS_SIZE
        else:
            return PIPE_SIZE

    if value.lower().startswith("stem"):
        return VALVE

    if '"' in value and convert_quantity_to_float(value) > 0:
        return QUANTITY

    if value.lower() == "fw":
        return FIELD_WELD

    # quantity
    return

def is_within_area(bounds, area):
    """Check if an item's bounding box is within the specified area."""
    x_min, y_min, x_max, y_max = bounds
    return (area['x0'] <= x_min <= area['x1'] and
            area['y0'] <= y_min <= area['y1'] and
            area['x0'] <= x_max <= area['x1'] and
            area['y0'] <= y_max <= area['y1'])

def get_rect_center(rect, to_int=True):
    x1, y1, x2, y2 = rect
    print(rect)
    center = ((x1+x2)/2, (y1+y2)/2)
    if to_int:
        center = (int(center[0]), int(center[1]))
    return center

def safe_polygon_eval(value):
    try:
        return ast.literal_eval(value)
    except:
        return None

def is_ignore(value: str):
    if any(val.lower() == value.lower() for val in ignore_terms):
        return 1
    if any(val.lower() in value.lower() for val in ignore_contains):
        return 1
    if any(value.lower().startswith(val.lower()) for val in ignore_startswith):
        return 1

    return 0

def calculate_quantity(value):
    return convert_quantity_to_float(value)

def is_ignore(value: str):
    if any(val.lower() == value.lower() for val in ignore_terms):
        return 1
    if any(val.lower() in value.lower() for val in ignore_contains):
        return 1
    if any(value.lower().startswith(val.lower()) for val in ignore_startswith):
        return 1

    return 0

def preprocess_raw_data(df: pd.DataFrame,
                        isometric_area: list[float] = None,
                        pages_to_process: list[int] = None,
                        min_span_level: int = 30) -> pd.DataFrame:
    """
    Args:
        df: Raw data DataFrame
        isometric_area: List of coordinates defining the isometric area
        pages_to_process
        min_span_level: Minimum quantity to consider as a span. Use very large value to prevent span detection

    Returns:
        Preprocessed DataFrame
    """
    df = df[df["pdf_page"].isin(pages_to_process)]

    df["polygon"] = df["polygon"].apply(safe_polygon_eval)
    df["x1"], df["y1"], df["x2"], df["y2"] = zip(*df["coordinates2"])
    df["bbox_width"] = df["x2"] - df["x1"]
    df["bbox_height"] = df["y2"] - df["y1"]
    df["bbox_area"] = df["bbox_width"] * df["bbox_height"]

    # Apply the isometric function to determine which data is inside the isometric area
    # TODO - account for each page dimension
    if isometric_area:
        df["inside_isometric"] = df.apply(is_isometric, axis=1)
        df = df[df["inside_isometric"] == 1]

    # Perform some categorization of isometric data
    df["ignore"] = df["value"].apply(is_ignore)
    df = df[df["ignore"] == 0]

    df["category"] = df["value"].apply(assign_category)
    df.loc[df["category"] == "quantity", "quantity"] = df["value"].apply(calculate_quantity)

    # Larger quantities auto assigned as spans
    df.loc[df["quantity"] > min_span_level, "category"] = "span"

    df["is_size_value"] = df["category"].apply(is_size_value)

    return df

def parse_page_range(page_range_str, total_pages):
    """Parse page range string into list of page numbers (1-based)"""
    if type(page_range_str) == int:
        return [page_range_str]
    elif type(page_range_str) == list:
        return page_range_str
    elif page_range_str is None:
        return list(range(1, total_pages + 1))
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return list(range(1, total_pages + 1))

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            end = min(total_pages, end)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            if 1 <= page <= total_pages:
                pages.append(page)

    return sorted(set(pages))

def get_line_center(a, b):
    x1, y1 = a
    x2, y2 = b
    return ((x1+x2)/2, (y1+y2)/2)

def remove_structure(image):
    """For drawing on structured lines - horizontal, vertical and diagonal"""
    result = image.copy()
    gray = cv2.cvtColor(image,cv2.COLOR_BGR2GRAY)
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

    # Remove horizontal lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40,1))
    remove_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
    cnts = cv2.findContours(remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255,255,255), 4)

    # Remove vertical lines
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
    remove_vertical = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel, iterations=2)
    cnts = cv2.findContours(remove_vertical, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = cnts[0] if len(cnts) == 2 else cnts[1]
    for c in cnts:
        cv2.drawContours(result, [c], -1, (255,0,0), 4)

    # cv2.imshow('original', image)
    # cv2.imshow('result', result)
    # cv2.imwrite('result.png', result)
    # cv2.waitKey()
    return result


def main(page_range="1-5"):
    # Read doc and perform some processing on raw data
    doc = fitz.open(filename)
    page_count = len(doc)
    pages_to_process = parse_page_range(page_range, page_count)
    feather = getSourceRawDataPath(projectId, filename)
    df = pd.read_feather(feather)

    output_doc = fitz.open()
    df = preprocess_raw_data(df, pages_to_process=pages_to_process, isometric_area=absolute_isometric_area)

    page_groups = df.groupby("pdf_page")
    for pdf_page, page_df in page_groups:
        page = doc.load_page(pdf_page-1)

        cv_img = page_to_opencv(page)

        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)

        print("Analyzing page: ", pdf_page)
        page = doc.load_page(pdf_page-1)
        # cv_img = page_to_opencv(page)

        # Draw rectangles for each row in the group
        for row in page_df.itertuples():
            value = row.value
            quantity = row.quantity
            angle = row.angle if row.angle else 0
            category = row.category
            is_size_value = row.is_size_value
            x1 = row.x1
            y1 = row.y1
            x2 = row.x2
            y2 = row.y2

            whitespace_count = 0

            # Create a rectangle using the coordinates
            rect = fitz.Rect(x1, y1, x2, y2)

            polygon_vertices = None
            center_a = None
            center_b = None

            # # Oriented text has polygon data
            # if row.polygon is not None:
            #     polygon_vertices = row.polygon
            #     polygon_vertices = [tuple(pt) for pt in polygon_vertices]
            #     polygon_vertices += polygon_vertices[:1]  # Close the polygon

            #     polygon_left_line = polygon_vertices[3], polygon_vertices[4]
            #     polygon_right_line = polygon_vertices[1], polygon_vertices[2]

            #     center_a = get_line_center(polygon_left_line[0], polygon_left_line[1])
            #     center_b = get_line_center(polygon_right_line[0], polygon_right_line[1])
            #     # polygon_center_b = polygon_vertices[3:4]
            #     if is_size_value:
            #         output_page.draw_polyline(polygon_vertices, color=color_map[SIZE_VALUE], width=2)
            #     else:
            #         output_page.draw_polyline(polygon_vertices, color=(0.0, 0, 1), width=1, stroke_opacity=0.5)
            #     # output_page.draw_polyline(polygon_center_a, color=(1, 0, 0), width=3)
            #     output_page.draw_line(polygon_left_line[0], polygon_left_line[1], color=(1, 0, 0), width=1, stroke_opacity=0.5)
            #     output_page.draw_line(polygon_right_line[0], polygon_right_line[1], color=(1, 0, 0), width=1, stroke_opacity=0.5)

            # elif angle == 270:
            #     # Vertical bboxes - check extended points above and below
            #     center_a = get_line_center((x1, y1), (x2, y1))
            #     center_b = get_line_center((x1, y2), (x2, y2))

            #     output_page.draw_line((x1, y1), (x2, y1), color=(1, 0, 0), width=1, stroke_opacity=0.5)
            #     output_page.draw_line((x1, y2), (x2, y2), color=(1, 0, 0), width=1, stroke_opacity=0.5)

            # # Extend before and after points
            # endpoint_a = center_a
            # endpoint_b = center_b
            # # Valid if searching not stopped early due to consecutive whitespace checks
            # if not polygon_vertices:
            #     color = color_map.get(category, (0, 1, 1))
            #     if is_size_value:
            #         color = color_map[SIZE_VALUE]
            #     if pd.isna(category) or category is None:
            #         output_page.draw_rect(rect, color=color, width=1)
            #     else:
            #         output_page.draw_rect(rect, color=color, width=2)

            # if polygon_vertices:
            #     start = polygon_vertices[0]
            #     start = (start[0], start[1] - 4)
            # else:
            #     start = (x1, y1 - 4)

            # # Optionally add text label with the value
            # if hasattr(row, 'value') and row.value:
            #     if pd.isna(category) or category is None:
            #         output_page.insert_text(start, str(row.value), color=(0.7, 0.3, 0.3), fontsize=8)
            #     else:
            #         if quantity > 0:
            #             c = "qty" if category == "quantity" else category
            #             text = f"{c}={round(quantity, 2)}, dg={round(angle, 2)}°"
            #         else:
            #             text = f"{category}, dg={angle}°"
            #         output_page.insert_text(start, text, color=(0, 0, 1), fontsize=8)

        # Encode image as PNG bytes
        success, img_data = cv2.imencode('.png', cv_img)
        if success:
            # Insert image into PDF page
            output_page.insert_image(output_page.rect, stream=img_data.tobytes())

        output_page: fitz.Page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        output_page.show_pdf_page(output_page.rect, doc, pdf_page-1)
        cv_img = remove_structure(cv_img)
        success, img_data = cv2.imencode('.png', cv_img)
        output_page.insert_image(output_page.rect, stream=img_data.tobytes())
        # radius = 2
        # color = (30,255,50)
        # cv2.drawContours(cv_img, contours, -1, color , radius)


    outfile = "debug/s1601 - raw.pdf"
    output_doc.save(outfile)
    output_doc.close()
    doc.close()



if __name__ == "__main__":
    main()