"""
Patch Raw DataFrame with OCR Data
"""

import json
import cv2
import fitz
import io
import os

import numpy as np
import pandas as pd
from PIL import Image

from src.atom.vision.detect_text_regions import detect_text_regions_cv2, flag_text_regions
from src.atom.vision import convert_to_pdf_aws

from src.atom import dbManager

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from pathlib import Path

from src.atom.fast_storage import load_df_fast, save_df_fast

# Create an instance of DatabaseManager
db_manager = dbManager.DatabaseManager()

# from src.atom.vision.convert_to_pdf_aws import convert_multiple_pages


#
# pdf = r"C:\Users\<USER>\Documents\Drawings\Tables Requiring Some OCR\Bulletin 003 - 10-CP-19003.pdf"
# res = convert_multiple_pages(pdf)
# json.dump(res, open(os.path.join("debug", f"full_textract_response.json"), "w"), indent=4)

# fte = load_json(os.path.join("debug", f"full_textract_response.json"))
# r = process_textract_response(fte)
# r.to_excel("processed_textract.xlsx")
# exit()


def load_json(file) -> dict:
    with open(file) as json_file:
        return json.load(json_file)

SOURCE_PDF = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Modified\Combined Remuriate.pdf"

MAX_PAGE_LIMIT = None

# SOURCE_PDF = pdf
# p = r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\debug\1_response.json"


# Original Raw data from inspect_raw
RAW_DATA_PATH = r"debug\raw_data" #r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Data\dev\test_raw_data.xlsx"
PAGE_GROUP_PATH = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\Modified\Groups\Page Group Summary.xlsx"

MULTI_ROIS = {
    1: load_json(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\ROIs\1.json"),
    2: load_json(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\ROIs\2.json"),
    3: load_json(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\ROIs\3.json"),
    4: load_json(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Axis\Axis 018\ROIs\4.json"),
}

OCR_DPI = 300  # Adjust this value suitable for OCR


def process_textract_response(data) -> pd.DataFrame:
    res = []
    for b in data["Blocks"]:
        if not b.get("Text"):
            continue
        bbox = b["Geometry"]["BoundingBox"]
        confidence = b["Confidence"]
        text = b["Text"]
        page = b.get("Page")
        res.append({
            "pdf_page": page,
            "text": text,
            "x0": bbox["Left"],
            "y0": bbox["Top"],
            "x1": bbox["Left"] + bbox["Width"],
            "y1": bbox["Top"] + bbox["Height"],
            "confidence": confidence
        })
    return pd.DataFrame(res)

def extract_missing_text(roi_coords, regions) -> pd.DataFrame:
    """Return text which overlap with regions"""
    all_flagged = []
    page_groups = regions.groupby("pdf_page")
    for pdf_page, page_text_regions in page_groups:
        page_roi_regions = roi_coords[roi_coords["pdf_page"] == pdf_page].to_dict("records")
        page_regions = []
        for record in page_text_regions.to_dict("records"):
            page_regions.append([record["x0"], record["y0"], record["x1"], record["y1"]])

        # If text region does not overlap with any region of
        # the Raw DataFrame, then flag it for OCR
        flagged = []
        for n, r1 in enumerate(page_regions):
            for r2 in page_roi_regions:
                r2_coords = [r2["x0"], r2["y0"], r2["x1"], r2["y1"]]
                if is_overlapping(r1, r2_coords):
                    # Overlapping with ROI - add flag for OCR
                    record = [pdf_page] + r1 + [r2["text"]]
                    flagged.append(record)
                    print(f"Page {pdf_page}, Text region index={n} flagged for OCR")
                    break

        all_flagged.extend(flagged)

        print(f"Page {pdf_page} - Number of missing regions which overlap with an ROI:", len(flagged))

    return pd.DataFrame(all_flagged, columns=["pdf_page", "x0", "y0", "x1", "y1", "text"])

def is_overlapping(rect_a, rect_b) -> bool:
    """
    Return True if two regions overlap, where lx and rx are top left
    and bottom right coordinates of a rect respectively
    """
    a_left, a_top = rect_a[0], rect_a[1]
    a_right, a_bottom = rect_a[2], rect_a[3]
    b_left, b_top = rect_b[0], rect_b[1]
    b_right, b_bottom = rect_b[2], rect_b[3]
    return (a_left < b_right
        and a_right > b_left
        and a_top < b_bottom
        and a_bottom > b_top)

def visualize_regions(cv_image, regions):
    """
    Draw rectangles around detected text regions and save the visualization.
    """
    vis_img = cv_image.copy()

    # Draw text regions with alternating colors
    for i, region in enumerate(regions):
        x = int(region["x0"])
        y = int(region["y0"])
        w = int(region["x1"]- region["x0"])
        h = int(region["y1"]- region["y0"])
        color = (0, 255, 0) if i % 3 == 0 else (255, 0, 0) if i % 3 == 1 else (0, 0, 255)
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), color, 1)
        cv2.putText(vis_img, str(i+1), (x, y-1), cv2.FONT_HERSHEY_SIMPLEX, 0.2, color, 1)

    return vis_img

def visualize_page(page: fitz.Page, pdf_page: int, regions, raw_df=None, output_path=None):
    """
    Visualize results for each page. Optionally display Raw Dataframe boxes for comparison
    """
    page_text_regions = regions[regions["pdf_page"] == pdf_page]
    cv_image = page_to_opencv(page)
    raw_vis_image = None
    if raw_df is not None:
        page_raw_regions = raw_df[raw_df["pdf_page"] == pdf_page].to_dict("records")
        extracted_raw_regions = page_raw_regions
        extracted_raw_regions = pd.DataFrame(extracted_raw_regions, columns=["x0", "y0", "x1", "y1"]).to_dict("records")
        raw_vis_image = visualize_regions(cv_image, extracted_raw_regions)

    flagged_vis_image = visualize_regions(cv_image, page_text_regions.to_dict("records"))

    # Concat images horizontally for output image if raw image created
    if output_path:
        if raw_vis_image is not None:
            image = cv2.hconcat([raw_vis_image, flagged_vis_image])
        else:
            image = flagged_vis_image
        cv2.imwrite(os.path.join(output_path, f"flagged_regions_{pdf_page}.png"), image)
    else:
        image = flagged_vis_image

    return image


def page_to_opencv(page: fitz.Page, zoom=None, dpi=None):
    """Return open CV image from PyMuPDF page"""
    if zoom is None:
        zoom = 1
    if dpi:
        zoom = dpi / 72
    matrix = fitz.Matrix(zoom, zoom)
    rgb = page.get_pixmap(matrix=matrix)
    # Convert RGB to BGR
    pil_image = Image.open(io.BytesIO(rgb.tobytes()))
    open_cv_image = np.array(pil_image)        
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image

def pil_to_opencv(image: Image):
    open_cv_image = np.array(image)        
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    return open_cv_image



if __name__ == "__main__":

    print(f"\n\nOpening PDF from: {SOURCE_PDF}")

    doc = fitz.open(SOURCE_PDF)

    print(f"\n\nNumber of pages in document: {len(doc)}\n\n")

    # raw_data = pd.read_excel(RAW_DATA_PATH) # Read from Excel

    # # Read from the raw_data table
    # with db_manager.connect() as conn:
    #     # This will read all records from raw_data table
        # raw_data = pd.read_sql_query("SELECT * FROM raw_data", conn)

    raw_data = load_df_fast(f"{RAW_DATA_PATH}.feather")


    print(f"RAW DATA LENGTH: {len(raw_data)}" )
    page_groups = pd.read_excel(PAGE_GROUP_PATH)

    combined_missing_rois = []
    requires_ocr: list[int] = []
    for record in page_groups.itertuples():
        page_number = record.page_number

        # Check if page exists in document
        if page_number > len(doc):
            # print(f"Skipping page {page_number} as document only has {len(doc)} pages")
            continue

        group_number = record.group_number
        roi_layout = MULTI_ROIS[group_number]

        print(f"\n-------------\nRecord: {record}")
        print(f"Page: {page_number}(page_number-1)\n-------------\n")
        
        page = doc[page_number-1]

        # Detect text regions for each page and identify missing regions from Raw DataFrame
        cv_image = page_to_opencv(page, dpi=OCR_DPI)
        text_regions = detect_text_regions_cv2.detect_text_regions(cv_image)
        text_regions = pd.DataFrame(text_regions, columns=["x0", "y0", "width", "height"])
        text_regions["pdf_page"] = page_number
        text_regions["x1"] = text_regions["x0"] + text_regions["width"]
        text_regions["y1"] = text_regions["y0"] + text_regions["height"]
        text_regions = text_regions[["pdf_page", "x0", "y0", "x1", "y1", "width", "height"]]
        # text_regions.to_excel(f"debug/{page_number}_text_regions.xlsx")

        # Convert coords back to standard based on dpi value
        page_raw_df = raw_data[raw_data["pdf_page"] == page_number]

        # Extract system fields from the first row of page_raw_df
        sys_fields = {
            'sys_build': page_raw_df['sys_build'].iloc[0],
            'sys_path': page_raw_df['sys_path'].iloc[0],
            'sys_filename': page_raw_df['sys_filename'].iloc[0],
            'pdf_id': page_raw_df['pdf_id'].iloc[0]
}

        mapped_text_regions = flag_text_regions.convert_coords(text_regions, dpi=OCR_DPI)

        # Missing ROIs from Raw Data
        missing_rois = flag_text_regions.detect_missing_regions(raw_df=page_raw_df, text_regions=mapped_text_regions)
        # missing_rois.to_excel(f"debug/page_{page_number}_missing_rois.xlsx")

        # Filter missing regions from ROI
        roi_regions = []
        for r in roi_layout:
            region = r.get("tableCoordinates")
            columnName = r["columnName"]
            if not region:
                region = r
            roi_regions.append({
                "pdf_page": page_number,
                "columnName": columnName,
                "x0": region["relativeX0"],
                "y0": region["relativeY0"],
                "x1": region["relativeX1"],
                "y1": region["relativeY1"],
                # "width": region["relativeX1"] - region["relativeX0"],
                # "height": region["relativeY1"] - region["relativeY0"],
            })
        roi_regions = pd.DataFrame(roi_regions)
        roi_regions["x0"] *= page.rect.width
        roi_regions["y0"] *= page.rect.height
        roi_regions["x1"] *= page.rect.width
        roi_regions["y1"] *= page.rect.height
        roi_regions["width"] = roi_regions["x1"] - roi_regions["x0"]
        roi_regions["height"] = roi_regions["y1"] - roi_regions["y0"]

        os.makedirs("debug/images", exist_ok=True)
        if not missing_rois.empty:
            requires_ocr.append(page_number)
            combined_missing_rois.extend(missing_rois.to_dict("records"))

        if MAX_PAGE_LIMIT:
            if page_number > MAX_PAGE_LIMIT:
                break

    print(f"\n-------------\nOCR required for number [{len(requires_ocr)}] of pages\n-------------\n")

    combined_textract = []
    # Single convert - Replace with multi
    for page_number in requires_ocr:

        # Check the pdf contains the page it is attempting
        if page_number > len(doc):
            print(f"Skipping OCR for page {page_number} as document only has {len(doc)} pages")
            continue

        print("OCR Page", page_number)
        page = doc[page_number-1]

        cv_image = page_to_opencv(page, dpi=OCR_DPI)
        # Perform OCR on this page
        image_bytes = io.BytesIO()
        Image.fromarray(cv_image).save(image_bytes, format='JPEG')
        image_bytes = image_bytes.getvalue()

        res = convert_to_pdf_aws.convert_single_page(image_bytes)

        page_textract = process_textract_response(res)
        # Convert back to absolute coordinates
        page_textract["x0"] *= page.rect.width
        page_textract["y0"] *= page.rect.height
        page_textract["x1"] *= page.rect.width
        page_textract["y1"] *= page.rect.height
        page_textract["pdf_page"] = page_number

        combined_textract.extend(page_textract.to_dict("records"))

        # image = visualize_page(page, 1, textract_df)

        # cv2.imshow("test", image)
        # cv2.waitKey(0)

        # res = extract_missing_text(textract_df, missing_rois)
        # res.to_excel("temp_res.xlsx")

        if page_number > 1:
            break

    combined_textract = pd.DataFrame(combined_textract)
    combined_textract.to_excel("debug/combined_textract_response.xlsx")

    combined_missing_rois = pd.DataFrame(combined_missing_rois)
    combined_missing_rois.to_excel("debug/combined_missing_rois.xlsx")
    # for page_number in requires_ocr:
    # page_textract = combined_textract[combined_textract["pdf_page"] == page_number]
    print(combined_missing_rois.head(10))


    final_missing_rois = extract_missing_text(combined_textract, combined_missing_rois) # These are missing regions which need to be patched into Raw


    final_missing_rois.to_excel("debug/final_missing_rois.xlsx")

    raw_data_records = raw_data.to_dict("records")
    for page_number, page_group in final_missing_rois.groupby("pdf_page"):
        for record in page_group.itertuples():
            coordinates = (record.x0, record.y0, record.x1, record.y1)
            raw_data_records.append({
                "pdf_page": page_number,
                "type": "OCR",
                "value": str(record.text),
                "coordinates": coordinates,
                "coordinates2": coordinates,
                "font": "ArialMT",
                "font_size": "12",
                "sys_build": sys_fields['sys_build'],
                "sys_path": sys_fields['sys_path'],
                "sys_filename": sys_fields['sys_filename'],
                "pdf_id": sys_fields['pdf_id']
            })
            
    # page = doc[page_number-1]
    # image = visualize_page(page, page_number, res)
    # cv2.imshow("test", image)
    # cv2.waitKey(0)

    patch_file = "debug/raw_data_full_patched_ocr.xlsx"

    # pd.DataFrame(raw_data_records).to_excel(patch_file)
    out_df = pd.DataFrame(raw_data_records)

    # Convert all object columns to string
    for col in out_df.select_dtypes(['object']):
        out_df[col] = out_df[col].astype(str)

    # Save as Parquet/Feather
    output_path = r"debug\raw_data_full_patched_ocr"  # Adjust path as needed
    save_df_fast(out_df, output_path, format='feather')
    print("Finished patching RAW Data:", patch_file)






