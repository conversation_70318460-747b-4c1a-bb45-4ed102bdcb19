
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
from openpyxl import load_workbook
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection, get_db_cursor, execute_query
from src.atom.pg_database.data_import.workbook_db_importer import WorkbookImporter


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

### Import Profile Tables
def import_flange_data(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import flange data from a workbook into the public.flange_data table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.flange_data"

    # Key columns used to identify existing records
    key_columns = ["profile_id", "size_in", "lbs"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        if 'lbs' not in df.columns:
            logger.error("Required column 'lbs' not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': "Required column 'lbs' missing from Excel"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing lbs values - identify rows with NaN in required fields
        missing_lbs_rows = df[df['lbs'].isna()].index.tolist()
        if missing_lbs_rows:
            logger.warning(f"Found {len(missing_lbs_rows)} rows with missing 'lbs' values")
            logger.warning(f"These rows will be skipped: {missing_lbs_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=['lbs'])
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = ['size_in', 'size_mm', 'lbs', 'o_in', 'c_in', 'o_mm', 'c_mm',
                        'bolt_circle_mm', 'bolt_circle_in', 'no_holes', 'hole_size_in', 'hole_size_mm']

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Save pre-processed data to a temporary Excel file if needed
        # df.to_excel(f"{workbook_path}.cleaned.xlsx", index=False)

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_lbs_rows) if missing_lbs_rows else 0

        # Log results
        logger.info(f"Flange data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data: {e}")
        raise

def import_tee_reducing(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import reducing tee data from a workbook into the public.tee_reducing table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.tee_reducing"

    # Key columns used to identify existing records
    key_columns = ["client_id", "profile", "size1", "size2", "size3"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for tee_reducing from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['client_id', 'client', 'profile', 'size1', 'size2', 'size3']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['client_id'].isna() |
            df['client'].isna() |
            df['profile'].isna() |
            df['size1'].isna() |
            df['size2'].isna() |
            df['size3'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = [
            'client_id', 'size1', 'size2', 'size3',
            'run_mm', 'run_in', 'outlet_mm', 'outlet_in',
            'run_c_mm', 'run_c_in', 'outlet_m_mm', 'outlet_m_in',
            'length_in', 'length_ft', 'length_mm',
            'area_mm', 'area_in', 'area_ft'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['client_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"Tee reducing data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for tee_reducing: {e}")
        raise

def import_standard_fittings(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None) -> Dict:
    """
    Specialized function to import standard fittings data from a workbook into the public.standard_fittings table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.standard_fittings"

    # Key columns used to identify existing records
    key_columns = ["client_id", "client", "profile", "lookup_category", "size1"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for standard_fittings from {workbook_path}")
    try:
        # Load the Excel data
        df = pd.read_excel(workbook_path, sheet_name=sheet_name)
        original_rows = len(df)

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        required_columns = ['client_id', 'client', 'profile', 'lookup_category', 'size1']
        missing_required = [col for col in required_columns if col not in df.columns]

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['client_id'].isna() |
            df['client'].isna() |
            df['profile'].isna() |
            df['lookup_category'].isna() |
            df['size1'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure lookup_category is a string - this addresses the validation error
        if 'lookup_category' in df.columns:
            df['lookup_category'] = df['lookup_category'].astype(str)
            logger.info("Converted lookup_category to string to fix validation errors")

        # Ensure numeric columns are numeric
        numeric_cols = [
            'client_id', 'size1',
            'length_ft', 'length_in', 'length_mm',
            'area_ft', 'area_in', 'area_mm'
        ]

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['client_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"Standard fittings data import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for standard_fittings: {e}")
        raise

def import_bom_component_mapping(workbook_path: str, sheet_name: Optional[str] = None, db_config: Optional[DatabaseConfig] = None, profile_id: Optional[int] = None) -> Dict:
    """
    Specialized function to import BOM component mapping data from a workbook into the public.atem_bom_component_mapping table.

    Args:
        workbook_path: Path to the Excel workbook
        sheet_name: Name of the sheet to import (optional)
        db_config: DatabaseConfig instance (optional)
        profile_id: Profile ID to use if not in the Excel file (optional)

    Returns:
        Dictionary with import statistics
    """
    table_name = "public.atem_bom_component_mapping"

    # Key columns used to identify existing records
    key_columns = ["profile_id", "class_description", "component_name"]

    # Create importer
    importer = WorkbookImporter(db_config or DatabaseConfig())

    # Read Excel first to pre-process
    logger.info(f"Pre-processing Excel data for BOM component mapping from {workbook_path}")
    try:
        # Load the Excel data
        try:
            if sheet_name is None:
                # Try to find a sheet with a name related to component mapping
                # Get all sheet names
                xls = pd.ExcelFile(workbook_path)
                all_sheets = xls.sheet_names
                logger.info(f"Available sheets in workbook: {all_sheets}")

                # Look for sheets with names that might contain component mapping data
                component_sheets = [s for s in all_sheets if any(term in s.lower() for term in
                                                              ['component', 'mapping', 'bom', 'takeoff'])]

                if component_sheets:
                    # Use the first matching sheet
                    sheet_name = component_sheets[0]
                    logger.info(f"Automatically selected sheet: {sheet_name}")
                else:
                    # Use the first sheet if no match found
                    sheet_name = all_sheets[0]
                    logger.info(f"No component mapping sheet found, using first sheet: {sheet_name}")

                df = pd.read_excel(workbook_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(workbook_path, sheet_name=sheet_name)

            logger.info(f"Successfully loaded data from sheet: {sheet_name}")
            original_rows = len(df)
            logger.info(f"Loaded {original_rows} rows from workbook")
        except Exception as e:
            logger.error(f"Error loading Excel file: {e}")
            raise ValueError(f"Failed to load Excel file: {e}")

        # Clean up the data

        # 1. Remove completely empty rows
        df = df.dropna(how='all')

        # 2. Check for required columns
        logger.info(f"DataFrame columns: {df.columns.tolist()}")

        # Try to map common column names to the required ones
        column_mapping = {
            'profile id': 'profile_id',
            'profileid': 'profile_id',
            'profile': 'profile_id',
            'class': 'class_description',
            'class description': 'class_description',
            'component': 'component_name',
            'component name': 'component_name',
            'takeoff': 'takeoff_category',
            'takeoff category': 'takeoff_category',
            'general': 'general_category',
            'general category': 'general_category',
            'map': 'map_to_gen',
            'map to': 'map_to_gen',
            'map to gen': 'map_to_gen'
        }

        # Rename columns based on case-insensitive matching
        for col in df.columns:
            col_lower = col.lower()
            if col_lower in column_mapping and column_mapping[col_lower] not in df.columns:
                logger.info(f"Renaming column '{col}' to '{column_mapping[col_lower]}'")
                df.rename(columns={col: column_mapping[col_lower]}, inplace=True)

        # After renaming, check for required columns
        required_columns = ['profile_id', 'class_description', 'component_name']
        missing_required = [col for col in required_columns if col not in df.columns]

        logger.info(f"After column mapping, DataFrame columns: {df.columns.tolist()}")

        # If profile_id is missing but was provided as a parameter, add it to the DataFrame
        if 'profile_id' in missing_required and profile_id is not None:
            logger.info(f"Adding profile_id={profile_id} to DataFrame (not found in Excel)")
            df['profile_id'] = profile_id
            missing_required.remove('profile_id')

        if missing_required:
            logger.error(f"Required columns {missing_required} not found in the Excel file")
            return {
                'total_rows': original_rows,
                'valid_rows': 0,
                'error_rows': original_rows,
                'inserted': 0,
                'updated': 0,
                'errors': [{'error': f"Required columns missing from Excel: {', '.join(missing_required)}"}]
            }

        # 3. Data cleaning for specific columns
        # Handle missing values in required fields
        missing_required_rows = df[
            df['profile_id'].isna() |
            df['class_description'].isna() |
            df['component_name'].isna()
        ].index.tolist()

        if missing_required_rows:
            logger.warning(f"Found {len(missing_required_rows)} rows with missing required values")
            logger.warning(f"These rows will be skipped: {missing_required_rows}")

            # Remove rows with missing required values
            df = df.dropna(subset=required_columns)
            logger.info(f"After removing rows with missing required values: {len(df)} rows remaining")

        # 4. Convert data types to expected formats
        # Ensure numeric columns are numeric
        numeric_cols = ['profile_id']

        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Ensure integer columns are integers
        int_cols = ['profile_id']
        for col in int_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)

        # 5. Clean string columns - trim whitespace and handle NaN values
        string_cols = ['class_description', 'component_name', 'takeoff_category', 'general_category', 'map_to_gen']
        for col in string_cols:
            if col in df.columns:
                # Replace NaN with None
                df[col] = df[col].replace([pd.NA, pd.NaT, None], None)

                # Convert numeric values to strings for string columns
                df[col] = df[col].apply(lambda x: str(x) if isinstance(x, (int, float)) else x)

                # Convert to string and trim whitespace
                df[col] = df[col].apply(lambda x: x.strip() if isinstance(x, str) else x)

                # Replace empty strings with None
                df[col] = df[col].apply(lambda x: None if isinstance(x, str) and x == '' else x)

        logger.info(f"Data pre-processing complete. Original rows: {original_rows}, Cleaned rows: {len(df)}")

        # Import the cleaned data
        result = importer.import_workbook(
            table_name=table_name,
            workbook_path=workbook_path,
            sheet_name=sheet_name,
            key_columns=key_columns,
            dataframe=df  # Pass the pre-cleaned dataframe
        )

        # Add pre-processing information to the result
        result['original_rows'] = original_rows
        result['rows_with_missing_required_values'] = len(missing_required_rows) if missing_required_rows else 0

        # Log results
        logger.info(f"BOM component mapping import completed: {result['valid_rows']} valid rows, "
                    f"{result['inserted']} inserted, {result['updated']} updated, "
                    f"{result['error_rows']} errors")

        return result

    except Exception as e:
        logger.error(f"Error pre-processing Excel data for BOM component mapping: {e}")
        raise
