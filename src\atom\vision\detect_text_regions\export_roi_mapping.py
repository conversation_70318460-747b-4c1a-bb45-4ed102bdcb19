'''
Export ROI to rectangle mapping to Excel for validation.

This module provides functions to export the mapping between ROI fields and
detected rectangles to an Excel file for validation and analysis.
'''
import os
import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
import cv2
import traceback

def export_roi_mapping_to_excel(matched_regions, template_rectangles, target_rectangles,
                               template_image_path, target_image_path, output_path):
    """
    Export ROI to rectangle mapping to Excel for validation.

    Args:
        matched_regions: Dictionary containing matched regions data
        template_rectangles: List of rectangles from template
        target_rectangles: List of rectangles from target
        template_image_path: Path to template image
        target_image_path: Path to target image
        output_path: Path to save the Excel file

    Returns:
        Path to the saved Excel file
    """
    # Create a DataFrame to store the mapping data
    data = []

    # Process each ROI group
    for group_id, regions in matched_regions.items():
        for region in regions:
            roi_name = region.get('name', 'unnamed')
            user_label = region.get('user_label', roi_name)
            rect_id = region.get('matched_rectangle', 'None')

            # Get original and adjusted coordinates
            orig_coords = region.get('original_coords', {})
            adj_coords = region.get('adjusted_coords', {})

            # Calculate distance between original and adjusted centers
            if orig_coords and adj_coords:
                orig_center_x = (orig_coords.get('x0', 0) + orig_coords.get('x1', 0)) / 2
                orig_center_y = (orig_coords.get('y0', 0) + orig_coords.get('y1', 0)) / 2

                adj_center_x = (adj_coords.get('x0', 0) + adj_coords.get('x1', 0)) / 2
                adj_center_y = (adj_coords.get('y0', 0) + adj_coords.get('y1', 0)) / 2

                # Calculate Euclidean distance
                distance = ((orig_center_x - adj_center_x) ** 2 +
                           (orig_center_y - adj_center_y) ** 2) ** 0.5
            else:
                distance = 0

            # Flag for potentially problematic matches (high distance)
            is_problematic = distance > 0.2  # Increased threshold to reduce false positives

            # Add to data
            data.append({
                'Group ID': group_id,
                'ROI Name': roi_name,
                'User Label': user_label,
                'Rectangle ID': rect_id,
                'Matched Rectangle': rect_id,  # Add explicit mapping of ROI to rectangle
                'Original X0': orig_coords.get('x0', 0),
                'Original Y0': orig_coords.get('y0', 0),
                'Original X1': orig_coords.get('x1', 0),
                'Original Y1': orig_coords.get('y1', 0),
                'Adjusted X0': adj_coords.get('x0', 0),
                'Adjusted Y0': adj_coords.get('y0', 0),
                'Adjusted X1': adj_coords.get('x1', 0),
                'Adjusted Y1': adj_coords.get('y1', 0),
                'Distance': distance,
                'Is Problematic': is_problematic,
                'Is Table': region.get('is_table', False),
                'Reason': 'Position shift' if is_problematic else ''  # Add explanation for problematic flag
            })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Sort by group ID and ROI name
    df = df.sort_values(['Group ID', 'ROI Name'])

    # Create Excel writer
    writer = pd.ExcelWriter(output_path, engine='openpyxl')

    # Write data to Excel
    df.to_excel(writer, sheet_name='ROI Mapping', index=False)

    # Get the workbook and worksheet
    workbook = writer.book
    worksheet = writer.sheets['ROI Mapping']

    # Define styles
    header_fill = PatternFill(start_color='4F81BD', end_color='4F81BD', fill_type='solid')
    header_font = Font(bold=True, color='FFFFFF')
    problem_fill = PatternFill(start_color='FF9999', end_color='FF9999', fill_type='solid')
    table_fill = PatternFill(start_color='CCFFCC', end_color='CCFFCC', fill_type='solid')

    # Apply header styles
    for col_num, _ in enumerate(df.columns, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.fill = header_fill
        cell.font = header_font

    # Apply conditional formatting
    for row_num, row_data in enumerate(df.values, 2):  # Start from row 2 (after header)
        is_problematic = row_data[df.columns.get_loc('Is Problematic')]
        is_table = row_data[df.columns.get_loc('Is Table')]

        if is_problematic:
            # Highlight problematic rows
            for col_num in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                cell.fill = problem_fill
        elif is_table:
            # Highlight table rows
            for col_num in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                cell.fill = table_fill

    # Adjust column widths
    for col_num, column in enumerate(df.columns, 1):
        column_width = max(len(str(column)), df[column].astype(str).map(len).max())
        worksheet.column_dimensions[get_column_letter(col_num)].width = column_width + 2

    # Create a simplified mapping sheet
    mapping_data = []
    for group_id, regions in matched_regions.items():
        for region in regions:
            roi_name = region.get('name', 'unnamed')
            user_label = region.get('user_label', roi_name)
            rect_id = region.get('matched_rectangle', 'None')
            is_problematic = region.get('is_problematic', False)

            # Add to mapping data
            mapping_data.append({
                'ROI Name': roi_name,
                'User Label': user_label,
                'Matched Rectangle ID': rect_id,
                'Is Problematic': is_problematic
            })

    # Create DataFrame for mapping
    mapping_df = pd.DataFrame(mapping_data)

    # Sort by ROI name
    mapping_df = mapping_df.sort_values('ROI Name')

    # Write to a new sheet
    mapping_df.to_excel(writer, sheet_name='ROI to Rectangle Map', index=False)

    # Get the worksheet
    mapping_sheet = writer.sheets['ROI to Rectangle Map']

    # Apply header styles
    for col_num, _ in enumerate(mapping_df.columns, 1):
        cell = mapping_sheet.cell(row=1, column=col_num)
        cell.fill = header_fill
        cell.font = header_font

    # Apply conditional formatting for problematic matches
    for row_num, row_data in enumerate(mapping_df.values, 2):  # Start from row 2 (after header)
        is_problematic = row_data[mapping_df.columns.get_loc('Is Problematic')]

        if is_problematic:
            # Highlight problematic rows
            for col_num in range(1, len(mapping_df.columns) + 1):
                cell = mapping_sheet.cell(row=row_num, column=col_num)
                cell.fill = problem_fill

    # Adjust column widths
    for col_num, column in enumerate(mapping_df.columns, 1):
        column_width = max(len(str(column)), mapping_df[column].astype(str).map(len).max())
        mapping_sheet.column_dimensions[get_column_letter(col_num)].width = column_width + 2

    # Create a visualization sheet
    vis_sheet = workbook.create_sheet('Visualization')

    # Add template image if available
    try:
        if os.path.exists(template_image_path):
            # Resize image for Excel
            img = cv2.imread(template_image_path)
            if img is not None:
                h, w = img.shape[:2]
                max_height = 500
                if h > max_height:
                    scale = max_height / h
                    new_h = int(h * scale)
                    new_w = int(w * scale)
                    img = cv2.resize(img, (new_w, new_h))

                # Save temporary resized image
                temp_path = os.path.join(os.path.dirname(output_path), 'temp_template.png')
                cv2.imwrite(temp_path, img)

                # Add to Excel
                try:
                    excel_img = Image(temp_path)
                    vis_sheet.add_image(excel_img, 'A1')
                except Exception as e:
                    print(f"Error adding template image to Excel: {e}")
                    traceback.print_exc()

                # Don't clean up immediately - keep the file for debugging
    except Exception as e:
        print(f"Error processing template image: {e}")
        traceback.print_exc()

    # Add target image if available
    try:
        if os.path.exists(target_image_path):
            # Resize image for Excel
            img = cv2.imread(target_image_path)
            if img is not None:
                h, w = img.shape[:2]
                max_height = 500
                if h > max_height:
                    scale = max_height / h
                    new_h = int(h * scale)
                    new_w = int(w * scale)
                    img = cv2.resize(img, (new_w, new_h))

                # Save temporary resized image
                temp_path = os.path.join(os.path.dirname(output_path), 'temp_target.png')
                cv2.imwrite(temp_path, img)

                # Add to Excel
                try:
                    excel_img = Image(temp_path)
                    vis_sheet.add_image(excel_img, 'A30')  # Position below template image
                except Exception as e:
                    print(f"Error adding target image to Excel: {e}")
                    traceback.print_exc()

                # Don't clean up immediately - keep the file for debugging
    except Exception as e:
        print(f"Error processing target image: {e}")
        traceback.print_exc()

    # Save the Excel file
    writer.close()

    return output_path
