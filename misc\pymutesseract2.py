import fitz
import pytesseract
from PIL import Image
import time

# Set the path to Tesseract if it's not in your PATH
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def ocr_page(page):
    pix = page.get_pixmap()
    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
    return pytesseract.image_to_string(img)

pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Axis\Axis 009\Modified\Borsig Test.pdf"
doc = fitz.open(pdf_path)

start_time = time.time()

for page_num, page in enumerate(doc):
    print(f"Processing page {page_num + 1}")
    text = ocr_page(page)
    print(f"Extracted text from page {page_num + 1}:")
    print("\nTEXT:\n\n", text)
    #print(text[:500] + "..." if len(text) > 500 else text)  # Print first 500 characters
    #print("\n" + "-"*50 + "\n")

end_time = time.time()
print(f"Total processing time: {end_time - start_time:.2f} seconds")