import fitz
import pandas as pd
from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter, PageObject
import os
from pathlib import Path
import io

pdf_path = r"C:\Drawings\31-3-25 tr-002\TR-002 Combined.pdf"
analysis_report = r"C:\Drawings\31-3-25 tr-002\tr002 analysis_report.xlsx"

# pdf_path = r"C:\Drawings\31-3-25 tr-003\TR-003 Combined.pdf"
# analysis_report = r"C:\Drawings\31-3-25 tr-003\tr-003-analysis_report.xlsx"

df = pd.read_excel(analysis_report)
doc = fitz.open(pdf_path)

# Get page rotations
page_rotations = {}
for page_num in range(len(doc)):
    page = doc[page_num]
    page_rotations[page_num + 1] = page.rotation

revision_pages = df["pdf_page"].unique().tolist()
other_pages = [i for i in range(1, len(doc) + 1) if i not in revision_pages]

# Separate pages with rotation
rotated_pages = [page_num for page_num in other_pages if page_rotations.get(page_num, 0) != 0]
non_rotated_pages = [page_num for page_num in other_pages if page_rotations.get(page_num, 0) == 0]

print(f"Pages not in analysis report: {other_pages}")
print(f"Pages with no revisions and rotation != 0: {rotated_pages}")
print(f"Pages with no revisions and rotation == 0: {non_rotated_pages}")

# Create mapping dataframes for page number tracking with rotation information
revision_mapping = []
for new_page_num, orig_page_num in enumerate(revision_pages, 1):
    revision_mapping.append({
        "original_page": orig_page_num,
        "new_page": new_page_num,
        "document": "Revisions_Detected",
        "rotation": page_rotations.get(orig_page_num, 0)
    })

no_revision_mapping = []
for new_page_num, orig_page_num in enumerate(non_rotated_pages, 1):
    no_revision_mapping.append({
        "original_page": orig_page_num,
        "new_page": new_page_num,
        "document": "No_Revisions_Detected",
        "rotation": page_rotations.get(orig_page_num, 0)
    })

rotated_mapping = []
for new_page_num, orig_page_num in enumerate(rotated_pages, 1):
    rotated_mapping.append({
        "original_page": orig_page_num,
        "new_page": new_page_num,
        "document": "No_Revisions_Rotated",
        "rotation": page_rotations.get(orig_page_num, 0)
    })

# Combine mappings
all_mappings = revision_mapping + no_revision_mapping + rotated_mapping
mapping_df = pd.DataFrame(all_mappings)

# Save mapping to Excel
output_dir = os.path.dirname(pdf_path)
basename = Path(pdf_path).stem
mapping_output_path = os.path.join(output_dir, f"{basename}_page_mapping.xlsx")
mapping_df.to_excel(mapping_output_path, index=False)
print(f"Page mapping information saved to: {mapping_output_path}")

# Generate a new PDF with pages not in the analysis report
def create_pdf_from_pages(input_path, output_path, page_numbers):
    """
    Create a new PDF containing only the specified pages from the input PDF.
    Adds page numbers to the top left corner of each page.

    Args:
        input_path (str): Path to the input PDF file
        output_path (str): Path where the output PDF will be saved
        page_numbers (list): List of page numbers to include (1-based indexing)
    """
    print(f"Creating PDF with {len(page_numbers)} pages...")

    reader = PdfReader(input_path)
    writer = PdfWriter()

    # Add selected pages to the writer with page numbers
    # Note: PyPDF uses 0-based indexing, but our page_numbers are 1-based
    for i, page_num in enumerate(page_numbers):
        # Get the page from the reader
        page = reader.pages[page_num - 1]

        # Create a temporary PDF with the page number text using PyMuPDF
        temp_doc = fitz.open()
        temp_page = temp_doc.new_page(width=page.mediabox.width, height=page.mediabox.height)

        # Add original page number text to top left corner
        text = f"Page {page_num}"  # Use original page number instead of sequential
        temp_page.insert_text((20, 20), text, fontsize=12, color=(0, 0, 0))

        # Convert the PyMuPDF page to bytes
        temp_bytes = io.BytesIO()
        temp_doc.save(temp_bytes)
        temp_bytes.seek(0)

        # Create a new PDF reader from the bytes
        temp_reader = PdfReader(temp_bytes)
        temp_page = temp_reader.pages[0]

        # Merge the original page with the page number overlay
        page.merge_page(temp_page)

        # Add the merged page to the writer
        writer.add_page(page)
        print(f"Adding page {page_num}")

        # Clean up
        temp_doc.close()

    # Write the output PDF
    with open(output_path, "wb") as output_file:
        writer.write(output_file)

    print(f"PDF created successfully at: {output_path}")


# Create output path in the same directory as the input PDF
output_dir = os.path.dirname(pdf_path)
basename = Path(pdf_path).stem
no_revision_output_path = os.path.join(output_dir, f"{basename}_No_Revisions_Detected.pdf")

# Generate the PDF with pages not in the analysis report
create_pdf_from_pages(pdf_path, no_revision_output_path, non_rotated_pages)

# Generate the PDF with pages that have revisions
revision_output_path = os.path.join(output_dir, f"{basename}_Revisions_Detected.pdf")
create_pdf_from_pages(pdf_path, revision_output_path, revision_pages)

# Generate the PDF with pages that have no revisions but are rotated
rotated_output_path = os.path.join(output_dir, f"{basename}_No_Revisions_Rotated.pdf")
if rotated_pages:
    create_pdf_from_pages(pdf_path, rotated_output_path, rotated_pages)
    print(f"Created PDF with {len(rotated_pages)} rotated pages without revisions")
else:
    print("No rotated pages without revisions found")

# Close the PyMuPDF document
doc.close()