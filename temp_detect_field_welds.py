"""
Draw supports.

used to detect if supports are grouped
"""
import os
import fitz
import datetime
import pandas as pd
import operator
from functools import reduce
from src.app_paths import getSourceRawDataPath

def plugin_detect_field_welds(filename,
                              raw_df: pd.DataFrame,
                              save_dir="debug",
                              match_text_semicolon_separated: str = "FW;FFW",
                              draw_pdf: bool = False,
                              case_sensitive: bool = False,
                              limit_relative_x0: float = 0,
                              limit_relative_x1: float = 0.72,
                              limit_relative_y0: float = 0,
                              limit_relative_y1: float = 0.83):
    """
    Plugin to extract selected matching text

    Optional define relative boundaries for which to search for the text
    limit_relative_x0: float = 0
    limit_relative_x1: float = 0.72
    limit_relative_y0: float = 0
    limit_relative_y1: float = 0.83

    Case sensitive if True, e.g. FW != fw

    """
    os.makedirs(save_dir, exist_ok=True)
    doc = fitz.open(filename)

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")

    print("Raw data count", len(raw_df))

    # Sort by pdf_page and original index. create index
    raw_df = raw_df.reset_index()
    raw_df = raw_df.sort_values(by=['pdf_page', 'index'])

    limit_relative_x0 = min(limit_relative_x0, 1)
    limit_relative_x1 = min(limit_relative_x1, 1)
    limit_relative_y0 = min(limit_relative_y0, 1)
    limit_relative_y1 = min(limit_relative_y1, 1)

    weld_matches = [s.strip() for s in match_text_semicolon_separated.split(";")]

    results = []

    page_bounds = {}

    for page in doc:
        page_num = page.number
        pdf_page = page_num + 1
        data = []

        page_width = page.rect.width
        page_height = page.rect.height

        page_df = raw_df[raw_df["pdf_page"] == pdf_page].copy()

        abs_limit_y1 = limit_relative_y1 * page_height
        abs_limit_x0 = limit_relative_x0 * page_width
        abs_limit_x1 = limit_relative_x1 * page_width
        abs_limit_y0 = limit_relative_y0 * page_height

        if page_df.empty:
            print("No data for page", pdf_page)
            continue

        filters = []
        for match in weld_matches:
            if case_sensitive:
                filters.append(page_df["value"].str.match(rf"^{match}$", na=False))
            else:
                filters.append(page_df["value"].str.match(rf"^(?i:{match})$", na=False))

        # Combine filters with OR
        weld_filter = reduce(operator.or_, filters)

        page_welds = page_df[weld_filter]

        # Limit welds by bounds
        page_welds["x0"] = page_welds["coordinates2"].apply(lambda coords: coords[0])
        page_welds["y0"] = page_welds["coordinates2"].apply(lambda coords: coords[1])
        page_welds["x1"] = page_welds["coordinates2"].apply(lambda coords: coords[2])
        page_welds["y1"] = page_welds["coordinates2"].apply(lambda coords: coords[3])

        print(len(page_welds))
        page_welds = page_welds[(page_welds["y1"] < abs_limit_y1) & (page_welds["x0"] > abs_limit_x0) & (page_welds["x1"] < abs_limit_x1) & (page_welds["y0"] > abs_limit_y0)]
        print(len(page_welds))

        weld_records = page_welds[["pdf_page", "value", "coordinates2"]].to_dict(orient="records")
        results.extend(weld_records)

    results_df = pd.DataFrame(results)
    all_welds_filename = os.path.join(save_dir, f"detected_field_welds_{timestamp}.xlsx")
    results_df.to_excel(all_welds_filename, index=False)

    # Draw bounding boxes on PDF
    if draw_pdf and not results_df.empty:
        new_doc = fitz.open()
        for page in doc:
            pdf_page = page.number + 1

            page_df = results_df[results_df["pdf_page"] == pdf_page]

            matched_count = len(page_df)
            page.insert_text((10, 10), f"Page {pdf_page}. Weld count: {matched_count}", fontsize=12)

            page_width = page.rect.width
            page_height = page.rect.height

            # Draw rect
            for row in page_df.itertuples():
                coordinates = row.coordinates2  # [x0, y0, x1, y1]
                rect = fitz.Rect(coordinates[0], coordinates[1], coordinates[2], coordinates[3])
                page.draw_rect(rect, color=(1, 0, 0), width=1)

            clip_rect = fitz.Rect(limit_relative_x0 * page_width, limit_relative_y0 * page_height, limit_relative_x1 * page_width, limit_relative_y1 * page_height)
            # Create a new page with same size as clip
            new_page = new_doc.new_page(width=clip_rect.width, height=clip_rect.height)

            # Copy region from source page into new page
            new_page.show_pdf_page(
                new_page.rect,      # destination rectangle (entire new page)
                doc,                # source document
                page.number,        # source page number
                clip=clip_rect      # crop region
            )

            if matched_count >= 1:
                # Draw border
                new_page.draw_rect(fitz.Rect(0, 0, new_page.rect.width, new_page.rect.height), color=(0, 0, 1), width=5)

        # Save the annotated PDF
        outfilename = f"raw_pages_with_field_welds_{timestamp}.pdf"
        output_pdf = os.path.join(save_dir, outfilename)
        new_doc.save(output_pdf, deflate=True)
        new_doc.close()

        # summary of welds per page
        summary = results_df.groupby("pdf_page").size().reset_index(name="count")
        # ensure missing pdf_page
        missing_df = []
        for page in doc:
            pdf_page = page.number + 1
            if pdf_page not in summary["pdf_page"].values:
                missing_df.append({"pdf_page": pdf_page, "count": 0})
        missing_df = pd.DataFrame(missing_df)
        summary = pd.concat([summary, missing_df], ignore_index=True)
        summary = summary.sort_values(by="pdf_page")
        summary_filename = f"summary_{timestamp}.xlsx"
        summary.to_excel(os.path.join(save_dir, summary_filename), index=False)

        return {
            "all_welds_file": all_welds_filename,
            "matching_pdf_file": outfilename,
            "summary_file": summary_filename
        }


if __name__ == "__main__":
    projectId = 1
    filename = "C:/Drawings/Clients/brockservices/BRS_0031 - BSLE34891 - Valero St Charles/received/BSLE34891 - Valero St Charles - Combined.pdf"
    feather = getSourceRawDataPath(projectId, filename)

    save_dir = r"C:\Drawings\Clients\brockservices\BRS_0031 - BSLE34891 - Valero St Charles\priority\output"

    draw_pdf = True
    plugin_detect_field_welds(filename, pd.read_feather(feather), save_dir, draw_pdf=draw_pdf)