﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34408.163
MinimumVisualStudioVersion = 10.0.40219.1
Project("{888888A0-9F3D-457C-B088-3A5042F75D52}") = "OpenAI_Parallel_Processing", "OpenAI_Parallel_Processing.pyproj", "{7A63FE78-52F8-44B7-8C6D-43FB1234D769}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7A63FE78-52F8-44B7-8C6D-43FB1234D769}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A63FE78-52F8-44B7-8C6D-43FB1234D769}.Release|Any CPU.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0661F223-F920-4E32-85F7-226BD166115C}
	EndGlobalSection
EndGlobal
