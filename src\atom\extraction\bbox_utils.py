"""
Helper functions for bbox processing

Functions:
    center_distance(boxA, boxB): Calculate the center distance between two bounding boxes
    compute_overlap(boxA, boxB): Calculate the overlap between two bounding boxes
    compute_overlap_np(boxesA, boxesB): Calculate the overlap between two sets of bounding boxes

Terms:
    IoU is the intersection over union of two bounding boxes. That is, the overlap of the two boxes
    Overlap values range from 0 (no overlap) to 1 (perfect match).

Tweak thresholds to consider duplicates or separate text based on IoU. Example below:

    > 0.5 = probably exact duplicate
    0.3-0.5 = partial or shifted copy
    < 0.3 = probably separate text

"""

import math
import numpy as np
from collections import defaultdict


def center_distance(boxA, boxB):
    """
    Compute center distance between two bounding boxes.

    Args:
        boxA: [x1, y1, x2, y2]
        boxB: [x1, y1, x2, y2]

    Returns:
        distance: center distance
    """
    cxA = (boxA[0] + boxA[2]) / 2
    cyA = (boxA[1] + boxA[3]) / 2
    cxB = (boxB[0] + boxB[2]) / 2
    cyB = (boxB[1] + boxB[3]) / 2
    return math.hypot(cxA - cxB, cyA - cyB)


def compute_overlap(boxA, boxB):
    """
    Compute overlap between two bounding boxes.

    Args:
        boxA: [x1, y1, x2, y2]
        boxB: [x1, y1, x2, y2]

    Returns:
        overlap: overlap value
    """
    # box format: [x1, y1, x2, y2]

    # determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # compute the area of intersection rectangle
    interArea = max(0, xB - xA) * max(0, yB - yA)

    # compute the area of both bounding boxes
    boxAArea = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxBArea = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])

    # compute IoU
    iou = interArea / float(boxAArea + boxBArea - interArea)
    return iou

def compute_overlap_np(boxesA, boxesB):
    """
    Compute overlap between two sets of bounding boxes using numpy.

    Args:
        boxesA: (N, 4) where each box is [x1, y1, x2, y2]
        boxesB: (M, 4) where each box is [x1, y1, x2, y2]

    Returns:
        overlap: (N, M) array of overlap values
    """
    # boxesA and boxesB shape: (N, 4) where each box is [x1, y1, x2, y2]
    xA = np.maximum(boxesA[:, None, 0], boxesB[None, :, 0])
    yA = np.maximum(boxesA[:, None, 1], boxesB[None, :, 1])
    xB = np.minimum(boxesA[:, None, 2], boxesB[None, :, 2])
    yB = np.minimum(boxesA[:, None, 3], boxesB[None, :, 3])

    interArea = np.maximum(0, xB - xA) * np.maximum(0, yB - yA)

    areaA = (boxesA[:, 2] - boxesA[:, 0]) * (boxesA[:, 3] - boxesA[:, 1])
    areaB = (boxesB[:, 2] - boxesB[:, 0]) * (boxesB[:, 3] - boxesB[:, 1])

    unionArea = areaA[:, None] + areaB[None, :] - interArea

    iou = interArea / np.clip(unionArea, 1e-6, None)
    return iou  # shape (len(boxesA), len(boxesB))


def deduplicate_bboxes(bboxes, iou_threshold=0.5):
    # Input: List of boxes [x1, y1, x2, y2]
    # Output: Filtered list with duplicates removed
    keep = []
    while bboxes:
        ref = bboxes.pop(0)
        keep.append(ref)
        bboxes = [box for box in bboxes if compute_iou(ref, box) < iou_threshold]
    return keep


def get_duplicates_by_indices(bboxes, iou_threshold=0.5):
    """
    Identify indices of bounding boxes that are duplicates based on IoU.

    Args:
        bboxes (list of list): Bounding boxes as [x1, y1, x2, y2]
        iou_threshold (float): IoU threshold to consider as duplicate

    Returns:
        set: Indices of duplicate boxes to remove
    """
    duplicates = set()
    n = len(bboxes)

    for i in range(n):
        if i in duplicates:
            continue
        for j in range(i + 1, n):
            if j in duplicates:
                continue
            iou = compute_iou(bboxes[i], bboxes[j])
            if iou >= iou_threshold:
                duplicates.add(j)

    return duplicates

def find_duplicate_text_bboxes(texts, bboxes, iou_threshold=0.5):
    """
    Find indices of duplicate text bboxes based on text and IoU.

    Args:
        texts (list of str): List of recognized text strings
        bboxes (list of [x0, y0, x1, y1]): Corresponding bbox list
        iou_threshold (float): IoU threshold for considering spatial duplicates

    Returns:
        set: Indices of boxes to remove
    """
    duplicates = set()
    text_groups = defaultdict(list)

    # Group indices by identical text
    for i, text in enumerate(texts):
        text_groups[text].append(i)

    # For each group of same text, find IoU-based duplicates
    for group in text_groups.values():
        for i in range(len(group)):
            idx_i = group[i]
            if idx_i in duplicates:
                continue
            for j in range(i + 1, len(group)):
                idx_j = group[j]
                if idx_j in duplicates:
                    continue
                iou = compute_overlap(bboxes[idx_i], bboxes[idx_j])
                if iou >= iou_threshold:
                    duplicates.add(idx_j)

    return duplicates