from PySide6.QtWidgets import QGraphicsDropShadowEffect
from PySide6.QtGui import QColor

from PySide6.QtSvg import QSvgRenderer
from PySide6.QtGui import QPainter, QPixmap, QIcon
from PySide6.QtCore import Qt

from os import path
import sys
import os

RESOURCE_DIR = "src/resources/"


# This is for pyinstaller
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    base_path = getattr(sys, '_MEIPASS', sys.path[0])
    return os.path.join(base_path, relative_path)


def applyDropShadowEffect(widget, blurRadius=8, color="black", offset=(1, 1)):
    try:
        shadow = QGraphicsDropShadowEffect(widget)
        shadow.setBlurRadius(blurRadius)
        shadow.setOffset(*offset)
        shadow.setColor(QColor(color))
        widget.setGraphicsEffect(shadow)
    except Exception as e:
        # Failed to apply shadow effect
        pass


def svg_to_pixmap(svg_filename: str) -> QPixmap:
    """
    Convert the SVG to pixmap
    Introduced as QIcon (in Windows) seems not be able to render SVG
    """
    renderer = QSvgRenderer(svg_filename)
    pixmap = QPixmap(renderer.defaultSize())
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter(pixmap)
    renderer.render(painter)
    painter.end()
    return pixmap


def svg_to_qicon(svg_filename: str) -> QPixmap:
    """Convert the SVG to icon"""
    svg_filename = resource_path(svg_filename)
    return QIcon(svg_to_pixmap(svg_filename))


def get_resource_qicon(name: str) -> QIcon:
    """Get file path relative to resource dir"""
    filename = path.join(RESOURCE_DIR, name)
    if name.endswith(".svg"):
        return svg_to_qicon(filename)
    else:
        return QIcon(resource_path(filename))


def get_resource_pixmap(name) -> QPixmap:
    """Get file path relative to resource dir"""
    filename = resource_path(path.join(RESOURCE_DIR, name))
    return QPixmap(filename)
