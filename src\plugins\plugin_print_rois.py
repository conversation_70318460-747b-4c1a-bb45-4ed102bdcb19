"""
For printing rois regions with labels to image or pdf

"""

import os
import fitz
import cv2
import pypdf
import io
import pandas as pd
import numpy as np
from PIL import Image
from pathlib import Path
from src.app_paths import getSourceExtractionOptionsPath
from src.utils.convert_roi_payload import convert_roi_payload
from src.utils.pdf.page_to_opencv import page_to_opencv
from src.app_paths import getSourceMissingRegionsPath, getSourceRawDataPath
from src.atom.fast_storage import load_df_fast

def parse_page_range(page_range, total_pages):
    """
    Parse a page range string into a list of page numbers.

    Args:
        page_range: String with page ranges (e.g., "1-5,7,9-12")
        total_pages: Total number of pages in the document

    Returns:
        List of page numbers (0-based)
    """
    pages = []

    if not page_range:
        return list(range(total_pages))

    parts = page_range.split(',')

    for part in parts:
        part = part.strip()

        if '-' in part:
            # Range of pages
            start, end = part.split('-')
            start = int(start.strip())  # Convert to 0-based
            end = int(end.strip())  # Keep end inclusive

            # Validate range
            start = max(1, start)
            end = min(total_pages, end)

            pages.extend(range(start, end))
        else:
            # Single page
            page = int(part)  # Convert to 0-based

            # Validate page
            if 0 <= page < total_pages:
                pages.append(page)

    return pages

def plugin_print_rois(project_source: tuple,
                    page_range: str,
                    extract_group_list: str = None,
                    roi_payload: str = None,
                    extract_as_pdf_grid: bool = False,
                    grid_columns: int = 2,
                    grid_rows: int = 2,
                    page_region: str = "0,0,1,1",
                    zoom: float = 1.0,
                    grid_zoom: float = 0.5,
                    show_raw_regions: bool = False,
                    output_dir: str = "debug"):

    """
    Draw ROIs on one page from each group. Alternatively, draw all pages in page_range in a pdf grid

    Args:
        project_source: Tuple of (project_id, filename)
        page_range: Page range to extract e.g. 1-5,7,9-12
        extract_group_list: List of groups to extract e.g. 1,2,3
        roi_payload: ROI payload to use
        extract_as_pdf_grid: Whether to extract ROIs for ALL pages as a PDF grid
        grid_columns: Number of columns in the grid when extract_as_pdf_grid is True (default: 2)
        grid_rows: Number of rows in the grid when extract_as_pdf_grid is True (default: 2)
        page_region: Optional tuple (rel_x0, rel_y0, rel_x1, rel_y1) to extract only a region of each page
        zoom: Zoom factor for single page extraction (default: 1.0)
        grid_zoom: Zoom factor for grid extraction (default: 0.5)
        output_dir: Output directory for ROIs
    """

    if not project_source:
        return "A project source must be selected"
    projectId, filename = project_source

    print(projectId, filename)

    try:
        page_region = [float(x) for x in page_region.split(",")] if page_region else None
    except Exception:
        pass

    group_list = []
    if extract_group_list:
        try:
            group_list = [int(g) for g in extract_group_list.split(",")]
        except Exception:
            pass

        if not group_list:
            try:
                group_list = [int(extract_group_list)]
            except Exception:
                return "Invalid group list format."

    # Group list takes precedence over page range
    if not roi_payload:
        print("Loading saved ROI payload")
        roi_payload = getSourceExtractionOptionsPath(projectId, filename)
        if not os.path.exists(roi_payload):
            error = "A saved ROI payload not found for this project source."
            print(error)
            return error

    roi_payload = convert_roi_payload(roi_payload, force_extract=True, ignore_bom=True)

    cleaned_roi_payload = {}
    cleaned_roi_payload["ocr"] = roi_payload["ocr"]

    pages = parse_page_range(str(page_range), 9999)
    final_pages = set()
    unique_groups = set()
    group_pages = {}
    cleaned_roi_payload['pageToGroup'] = {}
    for page, group in roi_payload['pageToGroup'].items():
        if extract_group_list and group not in group_list:
            continue
        if page in pages:
            cleaned_roi_payload['pageToGroup'][page] = group
            group_pages.setdefault(group, []).append(page)
            final_pages.add(page)
            unique_groups.add(group)

    if show_raw_regions:
        missing = getSourceMissingRegionsPath(projectId, filename)
        try:
            missingDf = load_df_fast(missing)
        except Exception as e:
            missingDf = pd.DataFrame()

        raw = getSourceRawDataPath(projectId, filename)
        try:
            rawDf = load_df_fast(raw)
            rawDf[['x0', 'y0', 'x1', 'y1']] = pd.DataFrame(rawDf['coordinates2'].tolist())
        except Exception as e:
            rawDf = pd.DataFrame()

    final_groups = set()
    cleaned_roi_payload['groupRois'] = {}
    for group, rois in roi_payload['groupRois'].items():
        if group not in unique_groups:
            continue
        if extract_group_list and group not in group_list:
            continue
        cleaned = []
        for roi in rois:
            # column_name = roi["columnName"].lower()
            cleaned.append(roi)

        if cleaned:
            cleaned_roi_payload['groupRois'][group] = cleaned
            final_groups.add(group)

    doc = fitz.open(filename)
    os.makedirs(output_dir, exist_ok=True)
    stem = Path(filename).stem

    out_pdf = f"{output_dir}/{stem}_rois.pdf"

    outfiles = []
    # Print 1 page from each group
    if not extract_as_pdf_grid:
        for group, pages in group_pages.items():
            pdf_page = pages[0]
            print(group, pdf_page)
            page = doc[pdf_page - 1]
            page_width = page.rect.width
            page_height = page.rect.height

            image = page_to_opencv(page, zoom)

            # Variables to track region offsets for ROI adjustments
            x_offset = 0
            y_offset = 0

            # Apply region cropping if specified
            if page_region:
                rel_x0, rel_y0, rel_x1, rel_y1 = page_region
                x0 = int(rel_x0 * page_width * zoom)
                y0 = int(rel_y0 * page_height * zoom)
                x1 = int(rel_x1 * page_width * zoom)
                y1 = int(rel_y1 * page_height * zoom)
                # Ensure coordinates are within image bounds
                x0 = max(0, min(x0, image.shape[1] - 1))
                y0 = max(0, min(y0, image.shape[0] - 1))
                x1 = max(0, min(x1, image.shape[1]))
                y1 = max(0, min(y1, image.shape[0]))
                # Store offsets for ROI adjustment
                x_offset = x0
                y_offset = y0
                # Crop the image
                image = image[y0:y1, x0:x1]

            # Draw raw regions if enabled
            if show_raw_regions:
                # Draw missing regions in red
                if not missingDf.empty:
                    page_missing_df = missingDf[missingDf["pdf_page"] == pdf_page]
                    for row in page_missing_df.itertuples():
                        # Calculate coordinates with offset adjustment
                        x0 = row.x0 * zoom
                        y0 = row.y0 * zoom
                        x1 = row.x1 * zoom
                        y1 = row.y1 * zoom

                        # Check if region is visible in the cropped image
                        if x1 >= 0 and y1 >= 0 and x0 < image.shape[1] and y0 < image.shape[0]:
                            # Clip coordinates to image boundaries
                            x0 = max(0, min(x0, image.shape[1] - 1))
                            y0 = max(0, min(y0, image.shape[0] - 1))
                            x1 = max(0, min(x1, image.shape[1]))
                            y1 = max(0, min(y1, image.shape[0]))

                            # Draw rectangle in red
                            cv2.rectangle(image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 0, 255), 1)

                # Draw raw regions in blue
                if not rawDf.empty:
                    page_raw_df = rawDf[rawDf["pdf_page"] == pdf_page]
                    for row in page_raw_df.itertuples():
                        # Calculate coordinates with offset adjustment
                        x0 = row.x0 * zoom
                        y0 = row.y0 * zoom
                        x1 = row.x1 * zoom
                        y1 = row.y1 * zoom

                        # Check if region is visible in the cropped image
                        if x1 >= 0 and y1 >= 0 and x0 < image.shape[1] and y0 < image.shape[0]:
                            # Clip coordinates to image boundaries
                            x0 = max(0, min(x0, image.shape[1] - 1))
                            y0 = max(0, min(y0, image.shape[0] - 1))
                            x1 = max(0, min(x1, image.shape[1]))
                            y1 = max(0, min(y1, image.shape[0]))

                            # Draw rectangle in blue
                            cv2.rectangle(image, (int(x0), int(y0)), (int(x1), int(y1)), (255, 100, 170), 1)

            rois = cleaned_roi_payload['groupRois'].get(group, [])
            for roi in rois:
                print(roi)
                if roi["isTable"]:
                    for n, tableRoi in enumerate(roi["tableColumns"]):
                        tableColumn = list(tableRoi.keys())[0]
                        tableCoords = tableRoi[tableColumn]
                        # Calculate absolute coordinates with offset adjustment
                        x0 = tableCoords["relativeX0"] * page_width * zoom - x_offset
                        y0 = tableCoords["relativeY0"] * page_height * zoom - y_offset
                        x1 = tableCoords["relativeX1"] * page_width * zoom - x_offset
                        y1 = tableCoords["relativeY1"] * page_height * zoom - y_offset

                        # Check if ROI is visible in the cropped region
                        if x1 >= 0 and y1 >= 0 and x0 < image.shape[1] and y0 < image.shape[0]:
                            # Clip coordinates to image boundaries
                            x0 = max(0, min(x0, image.shape[1] - 1))
                            y0 = max(0, min(y0, image.shape[0] - 1))
                            x1 = max(0, min(x1, image.shape[1]))
                            y1 = max(0, min(y1, image.shape[0]))

                            cv2.rectangle(image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 255, 0), 1)
                            cv2.putText(image, tableColumn, (int(x0), int(y0) + 24), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 0), 1)
                            if n == 0:
                                cv2.putText(image, f"{roi['columnName']}", (int(x0), int(y0)), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 0), 1)
                else:
                    # Calculate absolute coordinates with offset adjustment
                    x0 = roi["relativeX0"] * page_width * zoom - x_offset
                    y0 = roi["relativeY0"] * page_height * zoom - y_offset
                    x1 = roi["relativeX1"] * page_width * zoom - x_offset
                    y1 = roi["relativeY1"] * page_height * zoom - y_offset

                    # Check if ROI is visible in the cropped region
                    if x1 >= 0 and y1 >= 0 and x0 < image.shape[1] and y0 < image.shape[0]:
                        # Clip coordinates to image boundaries
                        x0 = max(0, min(x0, image.shape[1] - 1))
                        y0 = max(0, min(y0, image.shape[0] - 1))
                        x1 = max(0, min(x1, image.shape[1]))
                        y1 = max(0, min(y1, image.shape[0]))

                        cv2.rectangle(image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 255, 0), 1)
                        cv2.putText(image, roi["columnName"], (int(x0), int(y0)), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 0), 1)
            outfile = f"{output_dir}/{stem}_group_{group}.png"
            outfiles.append(outfile)
            print(f"Saved file to {outfile}")
            cv2.imwrite(outfile, image)

    else:
        # Create PDF with pages in a grid using OpenCV
        print(f"Creating PDF grid with ROIs to {out_pdf}")

        # Process pages in batches based on grid size
        page_list = list(final_pages)
        page_list.sort()  # Ensure pages are in order

        # Calculate pages per grid
        pages_per_grid = grid_columns * grid_rows

        # Calculate how many grid pages we need
        grid_pages_needed = (len(page_list) + pages_per_grid - 1) // pages_per_grid  # Ceiling division

        # Get dimensions from the first page for consistent sizing
        first_page = doc[page_list[0] - 1]
        page_width = first_page.rect.width
        page_height = first_page.rect.height

        # Calculate cropped dimensions if page_region is specified
        cropped_width = page_width
        cropped_height = page_height
        if page_region:
            rel_x0, rel_y0, rel_x1, rel_y1 = page_region
            cropped_width = page_width * (rel_x1 - rel_x0)
            cropped_height = page_height * (rel_y1 - rel_y0)

        # Create a PDF writer for output
        pdf_writer = pypdf.PdfWriter()

        for grid_idx in range(grid_pages_needed):
            # Get batch of pages for this grid
            start_idx = grid_idx * pages_per_grid
            end_idx = min(start_idx + pages_per_grid, len(page_list))
            batch = page_list[start_idx:end_idx]

            # Create a blank canvas for this grid page
            grid_width = int(cropped_width * grid_columns * grid_zoom)
            grid_height = int(cropped_height * grid_rows * grid_zoom)
            grid_image = np.ones((grid_height, grid_width, 3), dtype=np.uint8) * 255

            # Add each page to the grid
            for idx, page_num in enumerate(batch):
                # Calculate position in the grid
                row = idx // grid_columns
                col = idx % grid_columns

                # Get the page and its group
                page_num = page_num
                page = doc[page_num - 1]
                group = cleaned_roi_payload['pageToGroup'][page_num]

                # Convert page to OpenCV image
                page_image = page_to_opencv(page, grid_zoom)

                # Variables to track region offsets for ROI adjustments
                x_offset = 0
                y_offset = 0

                # Apply region cropping if specified
                if page_region:
                    rel_x0, rel_y0, rel_x1, rel_y1 = page_region
                    x0 = int(rel_x0 * page_width * grid_zoom)
                    y0 = int(rel_y0 * page_height * grid_zoom)
                    x1 = int(rel_x1 * page_width * grid_zoom)
                    y1 = int(rel_y1 * page_height * grid_zoom)
                    # Ensure coordinates are within image bounds
                    x0 = max(0, min(x0, page_image.shape[1] - 1))
                    y0 = max(0, min(y0, page_image.shape[0] - 1))
                    x1 = max(0, min(x1, page_image.shape[1]))
                    y1 = max(0, min(y1, page_image.shape[0]))
                    # Store offsets for ROI adjustment
                    x_offset = x0
                    y_offset = y0
                    # Crop the image
                    page_image = page_image[y0:y1, x0:x1]

                # Draw raw regions if enabled
                if show_raw_regions:
                    # Draw missing regions in red
                    if not missingDf.empty:
                        page_missing_df = missingDf[missingDf["pdf_page"] == page_num]
                        for record in page_missing_df.itertuples():
                            # Calculate coordinates with offset adjustment
                            x0 = record.x0 * grid_zoom - x_offset
                            y0 = record.y0 * grid_zoom - y_offset
                            x1 = record.x1 * grid_zoom - x_offset
                            y1 = record.y1 * grid_zoom - y_offset

                            # Check if region is visible in the cropped image
                            if x1 >= 0 and y1 >= 0 and x0 < page_image.shape[1] and y0 < page_image.shape[0]:
                                # Clip coordinates to image boundaries
                                x0 = max(0, min(x0, page_image.shape[1] - 1))
                                y0 = max(0, min(y0, page_image.shape[0] - 1))
                                x1 = max(0, min(x1, page_image.shape[1]))
                                y1 = max(0, min(y1, page_image.shape[0]))

                                # Draw rectangle in red
                                cv2.rectangle(page_image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 0, 255), 1)

                    # Draw raw regions in blue
                    if not rawDf.empty:
                        page_raw_df = rawDf[rawDf["pdf_page"] == page_num]
                        for record in page_raw_df.itertuples():
                            # Calculate coordinates with offset adjustment
                            x0 = record.x0 * grid_zoom - x_offset
                            y0 = record.y0 * grid_zoom - y_offset
                            x1 = record.x1* grid_zoom - x_offset
                            y1 = record.y1 * grid_zoom - y_offset

                            # Check if region is visible in the cropped image
                            if x1 >= 0 and y1 >= 0 and x0 < page_image.shape[1] and y0 < page_image.shape[0]:
                                # Clip coordinates to image boundaries
                                x0 = max(0, min(x0, page_image.shape[1] - 1))
                                y0 = max(0, min(y0, page_image.shape[0] - 1))
                                x1 = max(0, min(x1, page_image.shape[1]))
                                y1 = max(0, min(y1, page_image.shape[0]))

                                # Draw rectangle in blue
                                cv2.rectangle(page_image, (int(x0), int(y0)), (int(x1), int(y1)), (255, 50, 50), 1)

                # Calculate position on the grid - use cropped dimensions
                x_offset_grid = int(col * cropped_width * grid_zoom)
                y_offset_grid = int(row * cropped_height * grid_zoom)

                # Calculate the region to place this page
                h, w = page_image.shape[:2]
                region = grid_image[y_offset_grid:y_offset_grid+h, x_offset_grid:x_offset_grid+w]

                # Draw ROIs if they exist for this group
                if group in cleaned_roi_payload['groupRois']:
                    rois = cleaned_roi_payload['groupRois'][group]

                    for roi in rois:
                        if roi["isTable"]:
                            for n, tableRoi in enumerate(roi["tableColumns"]):
                                tableColumn = list(tableRoi.keys())[0]
                                tableCoords = tableRoi[tableColumn]

                                # Calculate coordinates with offset adjustment
                                x0 = tableCoords["relativeX0"] * page_width * grid_zoom - x_offset
                                y0 = tableCoords["relativeY0"] * page_height * grid_zoom - y_offset
                                x1 = tableCoords["relativeX1"] * page_width * grid_zoom - x_offset
                                y1 = tableCoords["relativeY1"] * page_height * grid_zoom - y_offset

                                # Check if ROI is visible in the cropped region
                                if x1 >= 0 and y1 >= 0 and x0 < page_image.shape[1] and y0 < page_image.shape[0]:
                                    # Clip coordinates to image boundaries
                                    x0 = max(0, min(x0, page_image.shape[1] - 1))
                                    y0 = max(0, min(y0, page_image.shape[0] - 1))
                                    x1 = max(0, min(x1, page_image.shape[1]))
                                    y1 = max(0, min(y1, page_image.shape[0]))

                                    # Draw rectangle
                                    cv2.rectangle(page_image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 255, 0), 1)

                                    # Add text
                                    cv2.putText(page_image, tableColumn, (int(x0), int(y0) + 24), cv2.FONT_HERSHEY_SIMPLEX, 0.6 * grid_zoom, (255, 0, 0), min(1, int(1 * grid_zoom)))
                                    if n == 0:
                                        cv2.putText(page_image, f"{roi['columnName']}", (int(x0), int(y0)), cv2.FONT_HERSHEY_SIMPLEX, 0.6 * grid_zoom, (255, 0, 0), min(1, int(1 * grid_zoom)))
                        else:
                            # Calculate coordinates with offset adjustment
                            x0 = roi["relativeX0"] * page_width * grid_zoom - x_offset
                            y0 = roi["relativeY0"] * page_height * grid_zoom - y_offset
                            x1 = roi["relativeX1"] * page_width * grid_zoom - x_offset
                            y1 = roi["relativeY1"] * page_height * grid_zoom - y_offset

                            # Check if ROI is visible in the cropped region
                            if x1 >= 0 and y1 >= 0 and x0 < page_image.shape[1] and y0 < page_image.shape[0]:
                                # Clip coordinates to image boundaries
                                x0 = max(0, min(x0, page_image.shape[1] - 1))
                                y0 = max(0, min(y0, page_image.shape[0] - 1))
                                x1 = max(0, min(x1, page_image.shape[1]))
                                y1 = max(0, min(y1, page_image.shape[0]))

                                # Draw rectangle
                                cv2.rectangle(page_image, (int(x0), int(y0)), (int(x1), int(y1)), (0, 255, 0), 1)

                                # Add text
                                cv2.putText(page_image, roi["columnName"], (int(x0), int(y0)), cv2.FONT_HERSHEY_SIMPLEX, 0.15 * grid_zoom, (255, 0, 0), min(1, int(1 * grid_zoom)))
                # Add page number label
                cv2.putText(page_image, f"Page {page_num} - Group {group}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)

                # Resize if needed
                if region.shape[:2] != page_image.shape[:2]:
                    page_image = cv2.resize(page_image, (region.shape[1], region.shape[0]))
                grid_image[y_offset_grid:y_offset_grid+h, x_offset_grid:x_offset_grid+w] = page_image

            # Convert the grid image to a PDF page
            _, buffer = cv2.imencode(".png", grid_image)
            img_io = io.BytesIO(buffer)
            img = Image.open(img_io)

            # Create a PDF page from the image
            pdf_bytes = io.BytesIO()
            img.save(pdf_bytes, format="PDF")
            pdf_bytes.seek(0)

            # Add the page to our PDF
            temp_reader = pypdf.PdfReader(pdf_bytes)
            pdf_writer.add_page(temp_reader.pages[0])

        # Save the final PDF
        with open(out_pdf, "wb") as f:
            pdf_writer.write(f)

        print(f"Saved PDF grid with ROIs to {out_pdf}")

    res = {
        "message": "Print ROIs Done",
        "save_dir": os.path.abspath(output_dir)
    }
    if extract_as_pdf_grid:
        res["save_file"] = out_pdf
    else:
        for n, out_file in enumerate(outfiles):
            res[f"image_{n}_file"] = out_file

    return res