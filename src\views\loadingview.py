from src.utils.logger import logger
from PySide6.QtWidgets import (QVBoxLayout, QWidget, QLabel)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QMovie
from pubsub import pub
from src.app_paths import resource_path

class LoadingView(QWidget):

    setLoadingStatus = Signal(str)

    def __init__(self, parent):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        self.movie = QMovie(resource_path("src/resources/loading.gif"), parent=self)
        # self.layout().addWidget(self.movie)
        self.status = QLabel(f"")
        self.status.setMovie(self.movie)
        self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout().addWidget(self.status)
        self.setLoadingStatus.connect(self.updateLoadingStatus)
        self.startAnimation()
    
    def updateLoadingStatus(self, status):
        self.status.setText(f"Loading: {status}")
    
    def startAnimation(self): 
        self.movie.start() 

    # Stop Animation(According to need) 
    def stopAnimation(self): 
        self.movie.stop() 