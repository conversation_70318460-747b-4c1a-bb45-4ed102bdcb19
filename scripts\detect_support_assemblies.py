"""
This script detect lines from a PDF

Noise should be removed.

This works on drawings with embedded data. Currently the text is excluded before being passed to cv2.

For OCR, this would require getting the polygons and bboxes and scrubbing them from the image.

"""

import os
import cv2
import math
import fitz
import datetime
import operator
import rtree

import numpy as np
import pandas as pd
from functools import reduce

from src.app_paths import getSourceRawDataPath

def load_raw_df(project_source):
    projectId, filename = project_source
    fileExists = os.path.exists(getSourceRawDataPath(project_source[0], project_source[1]))
    if not fileExists:
        raise Exception("Project source needs to be preprocessed first.")
    raw_df = pd.read_feather(getSourceRawDataPath(project_source[0], project_source[1]))
    return raw_df

def angle_between(p1, p2):
    """Returns the angle between two points in degrees."""
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    angle_rad = np.arctan2(dy, dx)
    angle_deg = np.degrees(angle_rad)
    # return angle_deg % 180  # ignore direction, keep in [0, 180)
    return (angle_deg + 360) % 360  # ignore direction, keep in [0, 360)

def compute_angle(line):
    """Compute angle of the line in degrees."""
    x1, y1, x2, y2 = line
    return np.degrees(np.arctan2(y2 - y1, x2 - x1))

def compute_distance(p1, p2):
    """Compute Euclidean distance between two points."""
    return np.linalg.norm(np.array(p1) - np.array(p2))

def fit_line(points):
    """Fit a line through points using least squares."""
    points = np.array(points)

    # Fit line: y = mx + b
    x = points[:, 0]
    y = points[:, 1]

    A = np.vstack([x, np.ones(len(x))]).T
    m, b = np.linalg.lstsq(A, y, rcond=None)[0]

    return m, b

def line_from_fit(m, b, x_min, x_max):
    """Return line endpoints from line equation."""
    y_min = m * x_min + b
    y_max = m * x_max + b
    return [x_min, y_min, x_max, y_max]

def lines_are_mergeable(line1, line2, angle_thresh=5, distance_thresh=20):
    """Check if lines are similar in angle and close enough to merge."""
    angle1 = compute_angle(line1)
    angle2 = compute_angle(line2)

    # Check angle similarity
    if abs(angle1 - angle2) > angle_thresh:
        return False

    # Check if endpoints are close
    points1 = [(line1[0], line1[1]), (line1[2], line1[3])]
    points2 = [(line2[0], line2[1]), (line2[2], line2[3])]

    for p1 in points1:
        for p2 in points2:
            if compute_distance(p1, p2) < distance_thresh:
                return True

    return False


def merge_and_fit_lines(lines_to_merge):
    """Merge multiple lines and fit a straight line."""
    points = []
    for line in lines_to_merge:
        points.append((line[0], line[1]))
        points.append((line[2], line[3]))

    m, b = fit_line(points)

    xs = [p[0] for p in points]
    x_min = min(xs)
    x_max = max(xs)

    return line_from_fit(m, b, x_min, x_max)

import numpy as np

def compute_angle(line):
    x1, y1, x2, y2 = line
    angle = np.arctan2(y2 - y1, x2 - x1)
    return np.degrees(angle) % 180  # Normalize to [0, 180)

def line_length(line):
    x1, y1, x2, y2 = line
    return np.hypot(x2 - x1, y2 - y1)

def endpoints_distance(line1, line2):
    # distance between any endpoint of line1 and any endpoint of line2
    points1 = [(line1[0], line1[1]), (line1[2], line1[3])]
    points2 = [(line2[0], line2[1]), (line2[2], line2[3])]
    dists = [np.hypot(p1[0]-p2[0], p1[1]-p2[1]) for p1 in points1 for p2 in points2]
    return min(dists)

def merge_two_lines(line1, line2):
    points = [
        (line1[0], line1[1]), (line1[2], line1[3]),
        (line2[0], line2[1]), (line2[2], line2[3])
    ]
    xs, ys = zip(*points)
    return [min(xs), min(ys), max(xs), max(ys)]

def merge_lsd_lines(lines, angle_threshold=0.3, distance_threshold=4):
    """Merge lines based on angle and distance."""
    merged = []
    used = [False] * len(lines)

    for i, line1 in enumerate(lines):
        if used[i]:
            continue

        x1, y1, x2, y2 = line1
        angle1 = compute_angle(line1)

        new_line = line1.copy()

        for j, line2 in enumerate(lines):
            if i == j or used[j]:
                continue

            angle2 = compute_angle(line2)
            if abs(angle1 - angle2) < angle_threshold:
                if endpoints_distance(new_line, line2) < distance_threshold:
                    # Merge them
                    new_line = merge_two_lines(new_line, line2)
                    used[j] = True

        merged.append(new_line)
        used[i] = True

    return merged

def smart_merge_lsd_lines(lines, angle_threshold=5, distance_threshold=20):
    """Performs least squares fit after merging points, to get straight line results"""
    merged = []
    used = [False] * len(lines)

    for i, line1 in enumerate(lines):
        if used[i]:
            continue

        group = [line1]
        used[i] = True
        angle1 = compute_angle(line1)

        for j, line2 in enumerate(lines):
            if i == j or used[j]:
                continue

            angle2 = compute_angle(line2)

            if abs(angle1 - angle2) < angle_threshold:
                if endpoints_distance(line1, line2) < distance_threshold:
                    group.append(line2)
                    used[j] = True

        merged_line = merge_and_fit_lines(group)
        merged.append(merged_line)

    return merged

def remove_rectangles_hough(img, min_line_length=10, max_line_gap=5, angle_tol=5):
    """
    Remove rectangles from a line-drawn image using Hough Transform.

    Parameters:
        img : np.ndarray
            Input BGR image with lines drawn.
        min_line_length : int
            Minimum line length for Hough transform.
        max_line_gap : int
            Maximum allowed gap between line segments.
        angle_tol : float
            Angle tolerance in degrees to consider lines perpendicular.

    Returns:
        img_clean : np.ndarray
            Image with rectangles removed.
    """
    img_clean = img.copy()

    # Convert to grayscale and detect edges
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)

    # Detect lines using Hough Transform
    lines = cv2.HoughLinesP(edges, rho=1, theta=np.pi/180, threshold=10,
                            minLineLength=min_line_length, maxLineGap=max_line_gap)
    if lines is None:
        return img_clean

    # Convert lines to a list of tuples for easier processing
    lines = [tuple(line[0]) for line in lines]  # (x1, y1, x2, y2)

    # Helper functions
    def line_angle(l1, l2):
        """Return angle between two lines in degrees"""
        def vec(line):
            return (line[2]-line[0], line[3]-line[1])

        v1 = vec(l1)
        v2 = vec(l2)

        dot = v1[0]*v2[0] + v1[1]*v2[1]
        mag1 = math.hypot(*v1)
        mag2 = math.hypot(*v2)

        if mag1*mag2 == 0:
            return 0

        # Clamp dot/(mag1*mag2) to [-1,1] to avoid math domain error
        cos_angle = max(min(dot / (mag1*mag2), 1.0), -1.0)
        angle = math.acos(cos_angle) * 180 / math.pi
        return angle

    def close(a, b, tol=5):
        """Check if points a and b are close"""
        return abs(a[0]-b[0]) <= tol and abs(a[1]-b[1]) <= tol

    # Group lines into possible rectangles
    rectangles = []
    used = set()
    for i, l1 in enumerate(lines):
        if i in used:
            continue
        for j, l2 in enumerate(lines):
            if j in used or j <= i:
                continue
            angle = line_angle(l1, l2)
            if abs(angle-90) < angle_tol or abs(angle-90) > 180-angle_tol:
                # perpendicular lines
                # check for shared endpoints
                endpoints = [(l1[0],l1[1]), (l1[2],l1[3]), (l2[0],l2[1]), (l2[2],l2[3])]
                for p1 in endpoints[:2]:
                    for p2 in endpoints[2:]:
                        if close(p1, p2):
                            # possible rectangle, add lines
                            rect_lines = [l1, l2]
                            used.add(i)
                            used.add(j)
                            rectangles.append(rect_lines)
                            break

    # Fill detected rectangles (approximate bounding boxes)
    for rect in rectangles:
        xs = [pt for line in rect for pt in line[::2]]
        ys = [pt for line in rect for pt in line[1::2]]
        x_min, x_max = min(xs), max(xs)
        y_min, y_max = min(ys), max(ys)
        cv2.rectangle(img_clean, (x_min, y_min), (x_max, y_max), (255,255,255), -1)

    return img_clean

def draw_fitz_page(page: fitz.Page,
                   zoom: int = 3,
                   lines: bool = True,
                   text: bool = False,
                   isometric_area: list = None) -> np.ndarray:
    """Redraw specific page elements and return the image.

    Args:
        text: Whether to include text in the image. - Not implemented
        lines: Whether to include lines in the image.
        zoom: Zoom factor for the image.
    """
    # Get the page's drawing operations
    # img = page_to_opencv(page, zoom=zoom)
    drawings = page.get_drawings()

    # Create a blank image with white background
    img = np.ones((int(page.rect.height * zoom),
                   int(page.rect.width * zoom),
                  3), dtype=np.uint8) * 255

    scaled_isometric_area = [
        int(isometric_area[0] * zoom),
        int(isometric_area[1] * zoom),
        int(isometric_area[2] * zoom),
        int(isometric_area[3] * zoom)
    ]

    # Draw only the vector graphics within isometric area
    for drawing in drawings:
        items = drawing["items"]  # List of drawing commands
        width = drawing.get("width", 1)
        if not width:
            width = 1

        width = int(math.ceil(width) * zoom)

        # is_closed_shape = drawing.get("closePath", False)  # detects closed boxes
        # if is_closed_shape:
        #     continue  # skip rectangles / boxes

        for item in items:
            if item[0] == "l":  # Line drawing command
                # Extract line coordinates and scale them
                point1 = item[1]
                point2 = item[2]
                x1, y1 = int(point1[0] * zoom), int(point1[1] * zoom)
                x2, y2 = int(point2[0] * zoom), int(point2[1] * zoom)

                # Check if line is within isometric area
                if (scaled_isometric_area[0] <= x1 <= scaled_isometric_area[2] and
                    scaled_isometric_area[1] <= y1 <= scaled_isometric_area[3] and
                    scaled_isometric_area[0] <= x2 <= scaled_isometric_area[2] and
                    scaled_isometric_area[1] <= y2 <= scaled_isometric_area[3]):
                    # Draw the line on our image
                    cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), width)

    img = remove_rectangles_hough(img)

    return img


def parse_page_range(page_range_str, total_pages):
    """Parse page range string into list of page numbers (1-based)"""
    if type(page_range_str) == int:
        return [page_range_str]
    elif type(page_range_str) == list:
        return page_range_str
    elif page_range_str is None:
        return list(range(1, total_pages + 1))
    elif not type(page_range_str) == str:
        raise ValueError("Invalid page range type")

    if not page_range_str.strip():
        return list(range(1, total_pages + 1))

    pages = []
    parts = page_range_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            start = max(1, start)
            end = min(total_pages, end)
            pages.extend(range(start, end + 1))
        else:
            page = int(part)
            if 1 <= page <= total_pages:
                pages.append(page)

    return sorted(set(pages))

def distance_to(p1, p2):
    """Returns the distance between two points."""
    return math.hypot(p2[0] - p1[0], p2[1] - p1[1])


from shapely.geometry import LineString, Point
import rtree
def to_linestring(row):
    return LineString([(row['x1'], row['y1']), (row['x2'], row['y2'])])

def angle_between_lines(line1, line2, shared_pt):

    def other_point(line, pt):
        coords = list(line.coords)
        return coords[0] if coords[1] == pt else coords[1]

    p1 = other_point(line1, shared_pt)
    p2 = shared_pt
    p3 = other_point(line2, shared_pt)

    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
    angle_rad = np.arccos(np.clip(cos_angle, -1, 1))
    angle_deg = np.degrees(angle_rad)
    return angle_deg

def check_arrow_shape(line1, line2, near_threshold=3, far_min_threshold=4, far_max_threshold=8):
    """
    The start of line1 is near the end of line2 i.e. the arrow tip.

    The other ends are far enough apart to form an open "V".
    """
    pts1 = [Point(p) for p in line1.coords]
    pts2 = [Point(p) for p in line2.coords]

    # Case 1: line1 start near line2 end
    near_dist = pts1[0].distance(pts2[1])
    far_dist = pts1[1].distance(pts2[0])
    if near_dist <= near_threshold and far_min_threshold <= far_dist <= far_max_threshold:
        return pts1[0]

    # Case 2: line1 end near line2 start
    near_dist = pts1[1].distance(pts2[0])
    far_dist = pts1[0].distance(pts2[1])
    if near_dist <= near_threshold and far_min_threshold <= far_dist <= far_max_threshold:
        return pts1[1]

    return None

from shapely.geometry import LineString
from shapely.ops import nearest_points

def line_as_ray(line, length=1000, reverse=False):
    """Extend a line to a ray.

    Args:
        line: LineString
        length: Length of the ray
        reverse: Reverse the direction of the ray
    """
    coords = list(line.coords)
    if reverse:
        coords.reverse()
    p1, p2 = coords
    vec = np.array(p2) - np.array(p1)
    vec = vec / np.linalg.norm(vec) * length
    new_p2 = np.array(p1) + vec
    return LineString([p1, new_p2])

def get_arrow_tip_precise(line1, line2):
    ray1 = line_as_ray(line1)
    ray2 = line_as_ray(line2)
    intersection = ray1.intersection(ray2)
    if intersection.is_empty:
        return None
    return intersection  # This is the true arrow tip

def get_arrowhead_direction(line1, line2, shared_pt):

    def other_point(line, pt):
        coords = list(line.coords)
        return coords[0] if coords[1] == pt else coords[1]

    # Get vectors pointing away from the shared point
    p1 = np.array(other_point(line1, shared_pt))
    p2 = np.array(shared_pt)
    v1 = p1 - p2

    p3 = np.array(other_point(line2, shared_pt))
    v2 = p3 - p2

    # Compute average (bisector)
    v_avg = (v1 / np.linalg.norm(v1) + v2 / np.linalg.norm(v2)) / 2

    # Arrowhead direction angle in degrees [0°, 360°]
    angle_rad = np.arctan2(-v_avg[1], v_avg[0])  # Y flipped for image coords
    angle_deg = np.degrees(angle_rad) % 360
    return angle_deg

def detect_support_boxes(raw_df: pd.DataFrame,
                         filename: str,
                         pages_to_process=[],
                         save_dir="debug",
                         limit_relative_x0: float = 0,
                         limit_relative_x1: float = 0.72,
                         limit_relative_y0: float = 0,
                         limit_relative_y1: float = 0.83):
    """
    Plugin to extract selected matching text (Possible this plugin can be used more generically for other text)

    Optional define relative boundaries for which to search for the text
    limit_relative_x0: float = 0
    limit_relative_x1: float = 0.72
    limit_relative_y0: float = 0
    limit_relative_y1: float = 0.83

    Case sensitive if True, e.g. FW != fw

    """

    os.makedirs(save_dir, exist_ok=True)
    doc = fitz.open(filename)

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")

    print("Raw data count", len(raw_df))

    # Sort by pdf_page and original index. create index
    raw_df = raw_df.reset_index()
    raw_df = raw_df.sort_values(by=['pdf_page', 'index'])

    # extract coordinates2 to x0, y0, x1, y1
    raw_df["x0"] = raw_df["coordinates2"].apply(lambda coords: coords[0])
    raw_df["y0"] = raw_df["coordinates2"].apply(lambda coords: coords[1])
    raw_df["x1"] = raw_df["coordinates2"].apply(lambda coords: coords[2])
    raw_df["y1"] = raw_df["coordinates2"].apply(lambda coords: coords[3])
    raw_df["center_x"] = raw_df["x0"] + (raw_df["x1"] - raw_df["x0"]) / 2
    raw_df["center_y"] = raw_df["y0"] + (raw_df["y1"] - raw_df["y0"]) / 2

    limit_relative_x0 = min(limit_relative_x0, 1)
    limit_relative_x1 = min(limit_relative_x1, 1)
    limit_relative_y0 = min(limit_relative_y0, 1)
    limit_relative_y1 = min(limit_relative_y1, 1)

    text_matches = [""]

    results = []
    for page in doc:
        page_num = page.number
        pdf_page = page_num + 1

        if pages_to_process and pdf_page not in pages_to_process:
            continue

        page_width = page.rect.width
        page_height = page.rect.height

        page_df = raw_df[raw_df["pdf_page"] == pdf_page].copy()

        abs_limit_y1 = limit_relative_y1 * page_height
        abs_limit_x0 = limit_relative_x0 * page_width
        abs_limit_x1 = limit_relative_x1 * page_width
        abs_limit_y0 = limit_relative_y0 * page_height

        if page_df.empty:
            print("No data for page", pdf_page)
            continue

        support_filter = page_df["value"].str.match(r"^S\d+$", na=False)
        ps_filter = page_df["value"].str.match(r"^PS-.*", na=False)
        # filters = [
        #     support_filter,
        #     ps_filter
        # ]
        # for match in weld_matches:
        #     # if case_sensitive:
        #     #     filters.append(page_df["value"].str.match(rf"^{match}$", na=False))
        #     # else:
        #     #     filters.append(page_df["value"].str.match(rf"^(?i:{match})$", na=False))
        #     # filters.append(page_df["value"].str.match(rf"^(?i:{match})$", na=False))
        #     filters.append(page_df["value"].str.endswith('"NPD', na=False))

        # Combine filters with OR
        # weld_filter = reduce(operator.or_, filters)

        s_supports_df = page_df[support_filter]
        ps_supports_df = page_df[ps_filter]

        s_supports_df["support_box_label"] = "S"
        # Give index to support
        s_supports_df["support_id"] = s_supports_df.reset_index().index + 1
        # Extract pos number from support
        s_supports_df["bom_pos"] = s_supports_df["value"].str.extract(r"^S(\d+)")[0]
        ps_supports_df["support_box_label"] = "PS"

        ps_supports_df["support_id"] = None
        ps_supports_df["closest_distance"] = None

        # Use rtree for efficient spatial overlap detection
        if not s_supports_df.empty and not ps_supports_df.empty:
            # Create rtree spatial index for s_supports
            idx = rtree.index.Index()

            # Create a mapping from positional index to DataFrame index
            s_index_map = {}

            # Insert s_supports bounding boxes into rtree
            for pos_idx, (df_idx, row) in enumerate(s_supports_df.iterrows()):
                x0, y0, x1, y1 = row["coordinates2"]
                # rtree expects (left, bottom, right, top)
                bbox = (x0, y0, x1, y1)
                # Increase S bbox size by 10% to account for text
                bbox = (x0 - (x1 - x0) * 0.1, y0 - (y1 - y0) * 0.1, x1 + (x1 - x0) * 0.1, y1 + (y1 - y0) * 0.1)
                idx.insert(pos_idx, bbox)
                s_index_map[pos_idx] = df_idx

            # For each ps_support, find overlapping s_supports
            for ps_idx, ps_row in ps_supports_df.iterrows():
                ps_x0, ps_y0, ps_x1, ps_y1 = ps_row["coordinates2"]
                ps_bbox = (ps_x0, ps_y0, ps_x1, ps_y1)

                # Find potential overlaps using rtree
                potential_matches = list(idx.intersection(ps_bbox))

                best_overlap_area = 0
                best_support_id = None
                best_distance = float('inf')

                for pos_idx in potential_matches:
                    # Get the actual DataFrame index
                    df_idx = s_index_map[pos_idx]
                    s_row = s_supports_df.loc[df_idx]
                    s_x0, s_y0, s_x1, s_y1 = s_row["coordinates2"]

                    # Calculate actual overlap area
                    overlap_x0 = max(ps_x0, s_x0)
                    overlap_y0 = max(ps_y0, s_y0)
                    overlap_x1 = min(ps_x1, s_x1)
                    overlap_y1 = min(ps_y1, s_y1)

                    if overlap_x1 > overlap_x0 and overlap_y1 > overlap_y0:
                        # There is actual overlap
                        overlap_area = (overlap_x1 - overlap_x0) * (overlap_y1 - overlap_y0)

                        if overlap_area > best_overlap_area:
                            best_overlap_area = overlap_area
                            best_support_id = s_row["support_id"]
                            # Calculate center-to-center distance for reference
                            ps_center_x, ps_center_y = ps_row["center_x"], ps_row["center_y"]
                            s_center_x, s_center_y = s_row["center_x"], s_row["center_y"]
                            best_distance = ((ps_center_x - s_center_x)**2 + (ps_center_y - s_center_y)**2) ** 0.5

                # If no overlap found, fall back to closest distance
                if best_support_id is None:
                    target_x, target_y = ps_row["center_x"], ps_row["center_y"]
                    distances = ((s_supports_df["center_x"] - target_x)**2 +
                        (s_supports_df["center_y"] - target_y)**2) ** 0.5
                    closest_idx = distances.idxmin()
                    closest_support = s_supports_df.loc[closest_idx]
                    best_support_id = closest_support["support_id"]
                    best_distance = distances.loc[closest_idx]

                # Assign the best match
                ps_supports_df.at[ps_idx, "support_id"] = best_support_id
                ps_supports_df.at[ps_idx, "closest_distance"] = best_distance

        supports_df = pd.concat([s_supports_df, ps_supports_df], ignore_index=True)

        # Limit welds by bounds
        supports_df["x0"] = supports_df["coordinates2"].apply(lambda coords: coords[0])
        supports_df["y0"] = supports_df["coordinates2"].apply(lambda coords: coords[1])
        supports_df["x1"] = supports_df["coordinates2"].apply(lambda coords: coords[2])
        supports_df["y1"] = supports_df["coordinates2"].apply(lambda coords: coords[3])

        supports_df = supports_df[(supports_df["y1"] < abs_limit_y1) & (supports_df["x0"] > abs_limit_x0) & (supports_df["x1"] < abs_limit_x1) & (supports_df["y0"] > abs_limit_y0)]

        supports_records = supports_df[["pdf_page", "value", "coordinates2",  "x0", "y0", "x1", "y1", "center_x",
                    "center_y", "bom_pos", "support_box_label", "support_id", "closest_distance"]].to_dict(orient="records")
        results.extend(supports_records)

    support_boxes_df = pd.DataFrame(results)
    return support_boxes_df

def draw_support_boxes(support_boxes_df, filename, pages_to_process=[], save_dir="debug"):
    doc = fitz.open(filename)

    # Draw bounding boxes on PDF
    if not support_boxes_df.empty:
        return

    new_doc = fitz.open()
    for page in doc:

        pdf_page = page.number + 1

        if pages_to_process and pdf_page not in pages_to_process:
            continue

        page_df = support_boxes_df[support_boxes_df["pdf_page"] == pdf_page]

        matched_count = len(page_df)
        page.insert_text((10, 10), f"Page {pdf_page}. Weld count: {matched_count}", fontsize=12, color=(0, 0, 1))

        page_width = page.rect.width
        page_height = page.rect.height

        # Draw rect
        for row in page_df.itertuples():
            coordinates = row.coordinates2  # [x0, y0, x1, y1]
            rect = fitz.Rect(coordinates[0], coordinates[1], coordinates[2], coordinates[3])
            if "NPD" in row.value:
                page.draw_rect(rect, color=(1, 0, 0), width=1)
            elif "FW" == row.value:
                page.draw_rect(rect, color=(0, 1, 0), width=1)
            else:
                page.draw_rect(rect, color=(0, 0, 1), width=1)

        clip_rect = fitz.Rect(limit_relative_x0 * page_width, limit_relative_y0 * page_height, limit_relative_x1 * page_width, limit_relative_y1 * page_height)
        # Create a new page with same size as clip
        new_page = new_doc.new_page(width=clip_rect.width, height=clip_rect.height)

        # Copy region from source page into new page
        new_page.show_pdf_page(
            new_page.rect,      # destination rectangle (entire new page)
            doc,                # source document
            page.number,        # source page number
            clip=clip_rect      # crop region
        )

        if matched_count >= 1:
            # Draw border
            new_page.draw_rect(fitz.Rect(0, 0, new_page.rect.width, new_page.rect.height), color=(0, 0, 1), width=5)

    # Save the annotated PDF
    save_file = f"support_boxes_s_and_ps.pdf"
    output_pdf = os.path.join(save_dir, save_file)
    new_doc.save(output_pdf, deflate=True)
    new_doc.close()

    return save_dir

    # # summary of welds per page
    # print(support_boxes_df)
    # summary = support_boxes_df.groupby("pdf_page").size().reset_index(name="count")
    # # ensure missing pdf_page
    # missing_df = []
    # for page in doc:
    #     pdf_page = page.number + 1
    #     if pdf_page not in summary["pdf_page"].values:
    #         missing_df.append({"pdf_page": pdf_page, "count": 0})
    # missing_df = pd.DataFrame(missing_df)
    # summary = pd.concat([summary, missing_df], ignore_index=True)
    # summary = summary.sort_values(by="pdf_page")
    # summary_file = os.path.join(save_dir, f"summary_{timestamp}.xlsx")
    # summary.to_excel(summary_file, index=False)

    # return {
    #     "save_dir": save_dir,
    #     "all_welds_file": all_welds_file,
    #     "matching_pdf_file": output_pdf,
    #     "summary_file": summary_file
    # }

def detect_lines(pdf_path: str, support_boxes_df: pd.DataFrame, pages_to_process=[], relative_isometric_area=[], save_dir="debug", debug: bool = True):

    zoom = 2

    doc = fitz.open(pdf_path)

    save_image_count = 1
    def save_debug_file(data: any,
                        name: str,
                        description: str = None,
                        pdf_page: int = ""):
        nonlocal save_image_count

        if not debug:
            return

        if pdf_page:
            pdf_page = f"page_{pdf_page}_"

        try:
            # Dataframes
            if data.empty:
                pass
            fn = f"{save_dir}/{pdf_page}stage_{save_image_count}_{name}.xlsx"
            data.to_excel(fn, index=False)
            print(f"saved to {fn}")
            return fn
        except Exception as e:
            pass

        try:
            # Save image types
            img = data.copy()
            height, width, channel = img.shape
            if description:
                description = f"Stage {save_image_count} - {description}. Image size = ({width}x{height})"
                cv2.putText(img, description, (10, 220), cv2.FONT_HERSHEY_SIMPLEX, height//2000, (0, 0, 255), 5)
            fn = f"{save_dir}/{pdf_page}stage_{save_image_count}_{name}.png"
            cv2.imwrite(fn, img)
            print(f"saved to {fn}")
            save_image_count += 1
            return fn
        except Exception as e:
            pass

        print("Unsupported debug save file type!")

    all_line_data = []

    try:
        num_pages = len(pages_to_process)
        # Determine number of pages to process
        print(f"Processing {num_pages} pages...")

        # Create a new PDF document
        output_doc = fitz.open()

        for pdf_page in pages_to_process:
            print(f"Processing page {pdf_page}...")
            save_image_count = 1

            zoom = 2

            # cv_img = page_to_opencv(doc.load_page(page_num), zoom=zoom)

            page = doc.load_page(pdf_page - 1)

            page_width = page.rect.width
            page_height = page.rect.height

            absolute_isometric_area = [relative_isometric_area[0] * page_width,
                                       relative_isometric_area[1] * page_height,
                                       relative_isometric_area[2] * page_width,
                                       relative_isometric_area[3] * page_height]

            # Draw line from vectors for cleaner image
            img = draw_fitz_page(page, zoom=zoom, isometric_area=absolute_isometric_area)

            # fill cv img white with support boxes
            # for index, row in support_boxes_df[support_boxes_df['pdf_page'] == pdf_page].iterrows():
            #     coordinates = row['coordinates2']  # [x0, y0, x1, y1]
            #     coordinates = [
            #         (int(coordinates[0]) * zoom, int(coordinates[1]) * zoom),
            #         (int(coordinates[2]) * zoom, int(coordinates[3]) * zoom)
            #     ]
            #     cv2.rectangle(img, coordinates[0], coordinates[1], (255, 0, 255), -1)

            cv2.imshow("Image", img)
            cv2.waitKey(0)
            cv2.destroyAllWindows()

            def insert_original_page():
                output_doc.insert_pdf(doc, from_page=pdf_page-1, to_page=pdf_page-1)
                output_page = output_doc[output_doc.page_count-1]
                output_page.insert_text((2, 24), f"Page: {pdf_page}", fontsize = 32)
                return output_page

            outpage = insert_original_page()

            cv_img = img

            # Make everything black
            masked = np.zeros_like(cv_img)
            x1, y1, x2, y2 = absolute_isometric_area
            x1 = int(x1) * zoom
            y1 = int(y1) * zoom
            x2 = int(x2) * zoom
            y2 = int(y2) * zoom
            masked[y1:y2, x1:x2] = cv_img[y1:y2, x1:x2]
            cv_img = masked

            gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)

            # scale = 0.5
            # gray = cv2.resize(gray, (0, 0), fx=scale, fy=scale)  # 50% size

            # Create LSD detector
            lsd = cv2.createLineSegmentDetector(refine=cv2.LSD_REFINE_NONE)

            # Detect lines
            lines = lsd.detect(gray)[0]  # Position 0 has the lines

            if lines is None:
                continue

            # cv = lsd.drawSegments(cv_img, lines)
            # save_debug_file(cv, "lsd_detected_lines", "Detected lines", page_num)

            lines = [line[0] for line in lines]
            # merged_lines = smart_merge_lsd_lines(lines)
            # merged_lines = merge_lsd_lines(lines)
            # merged_lines = merge_and_fit_lines(lines)

            import random
            def random_color(min_val=100, max_val=255):
                return (random.randint(min_val, max_val),
                        random.randint(min_val, max_val),
                        random.randint(min_val, max_val))

            def random_color_fitz(min_val=50, max_val=80):
                return (random.randint(min_val, max_val) / 100,
                        random.randint(min_val, max_val) / 100,
                        random.randint(min_val, max_val) / 100)

            image_width = gray.shape[1]
            image_height = gray.shape[0]

            def is_close_angle(angle, angles, tol=2, scale=1):
                for angle2 in angles:
                    if abs(angle - angle2) < tol:
                        return True
                return False

            line_data = []
            max_arrow_head_length = 16

            # Draw detected lines
            for line in lines:
                color = random_color_fitz()
                length = line_length(line)

                potential_arrow_head = False

                x1, y1, x2, y2 = line
                px1, py1, px2, py2 = x1 / image_width, y1 / image_height, x2 / image_width, y2 / image_height

                ax1, ay1, ax2, ay2 = px1 * outpage.rect.width, py1 * outpage.rect.height, px2 * outpage.rect.width, py2 * outpage.rect.height
                angle = angle_between((x1, y1), (x2, y2))

                potential_line_type = None
                if length < max_arrow_head_length:
                    continue
                    # outpage.insert_text((ax1, ay1), f"{int(angle)}", fontsize = 3, color=color)
                    # cv2.line(cv_img, (int(line[0]), int(line[1])), (int(line[2]), int(line[3])), color, zoom)
                    # outpage.draw_line((ax1, ay1), (ax2, ay2), color=color, width=1, stroke_opacity=1)
                    # potential_arrow_head = True
                elif is_close_angle(angle, [0, 180, 360]):
                    continue
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 1, 0), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [30, 210]): # blue measurement line
                    potential_line_type = "pipe"
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0, 1), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [330, 150]): # blue measurement line
                    potential_line_type = "pipe"
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0, 1), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [90, 270]):
                    continue
                    # outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0.5, 0.5), width=0.6, stroke_opacity=1)
                elif is_close_angle(angle, [0, 150]):
                    continue
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0.5, 0, 0.5), width=0.6, stroke_opacity=1)
                else:
                    # Line type
                    potential_line_type = "arrow_line"
                    outpage.draw_line((ax1, ay1), (ax2, ay2), color=(1, 0.2, 0.2), width=0.6, stroke_opacity=1)
                    outpage.insert_text((ax1, ay1), f"{int(angle)}", fontsize = 3, color=(1, 0.2, 0.2))

                # print((x1, y1), (x2, y2))
                d = {
                    'pdf_page': pdf_page,
                    'x0': (x1 / image_width) * outpage.rect.width,
                    'y0': (y1 / image_height) * outpage.rect.height,
                    'x1': (x2 / image_width) * outpage.rect.width,
                    'y1': (y2 / image_height) * outpage.rect.height,
                    'angle': angle,
                    'length': length,
                    'category': None,
                    'potential_arrow_head': potential_arrow_head,
                    'arrow_direction': None,
                    'potential_line_type': potential_line_type
                }
                d['endpoint_a'] = (d['x0'], d['y0'])
                d['endpoint_b'] = (d['x1'], d['y1'])

                line_data.append(d)

            all_line_data.extend(line_data)
            # save_debug_file(cv_img, "detected_lines", "Detected lines", pdf_page)

            # outpage = insert_original_page()

            # line_data_df = pd.DataFrame(line_data)
            # arrow_df = line_data_df[line_data_df['potential_arrow_head'] == True]
            # arrow_df["angle_diff"] = None
            # arrow_id = 1
            # pairs_checked = set()
            # ignore = set()

            # arrow_df["x1"] = arrow_df["x1"] * outpage.rect.width
            # arrow_df["y1"] = arrow_df["y1"] * outpage.rect.height
            # arrow_df["x2"] = arrow_df["x2"] * outpage.rect.width
            # arrow_df["y2"] = arrow_df["y2"] * outpage.rect.height

            # arrow_df["geom"] = arrow_df.apply(to_linestring, axis=1)

            # # Build Spatial Index to Reduce Comparisons
            # spatial_idx = rtree.index.Index()
            # # spatial_indexes = set()
            # for i, line in enumerate(arrow_df['geom']):
            #     spatial_idx.insert(i, line.bounds)
            #     # spatial_indexes.append(i)

            # def padded_bounds(line, padding=10):
            #     minx, miny, maxx, maxy = line.bounds
            #     return (minx - padding, miny - padding, maxx + padding, maxy + padding)

            # arrow_candidates = []

            # unfound = set([i for i in range(len(arrow_df))])
            # for i, line1 in enumerate(arrow_df['geom']):
            #     for j in spatial_idx.intersection(padded_bounds(line1, padding=12)):
            #         if i >= j:
            #             continue
            #         line2 = arrow_df['geom'].iloc[j]

            #         shared_pt = check_arrow_shape(line1, line2)
            #         if shared_pt:
            #             angle = angle_between_lines(line1, line2, (shared_pt.x, shared_pt.y))
            #             if 5 < angle < 330:  # tweak as needed
            #                 ray1 = line_as_ray(line1, 1, reverse=True) # Reverse so they expand
            #                 ray2 = line_as_ray(line2, 1, reverse=False)
            #                 precise_tip = get_arrow_tip_precise(ray1, ray2)
            #                 arrow_direction = get_arrowhead_direction(line1, line2, (shared_pt.x, shared_pt.y))
            #                 arrow_candidates.append((i, j, precise_tip, arrow_direction, angle))
            #                 unfound.discard(i)
            #                 unfound.discard(j)

            # for arrow in arrow_candidates:
            #     i, j, precise_tip, arrow_direction, angle = arrow
            #     a = arrow_df.iloc[i]
            #     b = arrow_df.iloc[j]
            #     x1, y1, x2, y2 = a.x1, a.y1, a.x2, a.y2
            #     x3, y3, x4, y4 = b.x1, b.y1, b.x2, b.y2
            #     color = random_color_fitz()
            #     outpage.draw_line((x1, y1), (x2, y2), color=color, width=1, stroke_opacity=1)
            #     outpage.draw_line((x3, y3), (x4, y4), color=color, width=1, stroke_opacity=1)
            #     # outpage.insert_text((x1, y1), f"{int(angle)}", fontsize = 2, color=color)
            #     # outpage.insert_text((x3, y3), f"{int(angle)}", fontsize = 2, color=color)

            #     line1 = arrow_df['geom'].iloc[i]
            #     line2 = arrow_df['geom'].iloc[j]

            #     outpage.insert_text((x1, y1), f"{int(i)}", fontsize = 1, color=color)
            #     outpage.insert_text((x3, y3), f"{int(j)}", fontsize = 1, color=color)

            #     ray1 = line_as_ray(line1, 16, reverse=False)
            #     ray2 = line_as_ray(line2, 16, reverse=True)
            #     outpage.draw_line((ray1.coords[0][0], ray1.coords[0][1]), (ray1.coords[1][0], ray1.coords[1][1]), color=(color[0], color[1], color[2], 0.5), width=0.4, stroke_opacity=1)
            #     outpage.draw_line((ray2.coords[0][0], ray2.coords[0][1]), (ray2.coords[1][0], ray2.coords[1][1]), color=(color[0], color[1], color[2], 0.5), width=0.4, stroke_opacity=1)

            #     if precise_tip is None:
            #         continue
            #     px1, px2 = precise_tip.x, precise_tip.y
            #     outpage.insert_text((px1, px2), f"{int(arrow_direction)}", fontsize = 5, color=color)
            #     outpage.draw_circle((px1, px2), 0.4, color=(0,1,0), fill_opacity=1, stroke_opacity=1)


            # for i in unfound:
            #     line = arrow_df.iloc[i]
            #     x1, y1, x2, y2 = line.x1, line.y1, line.x2, line.y2
            #     color = random_color_fitz()
            #     outpage.draw_line((x1, y1), (x2, y2), color=(1,0,0), width=1, stroke_opacity=1)


            # for line in arrow_df.itertuples():
            #     break
            #     index = line.Index
            #     angle = line.angle
            #     color = random_color_fitz()
            #     x1, y1, x2, y2 = line.x1, line.y1, line.x2, line.y2

            #     if index in ignore:
            #         continue
            #     ignore.add(index)

            #     arrow_df["angle_diff"] = abs(arrow_df["angle"] - angle)

            #     # Detected arrow head line match on opposite ends
            #     for line2 in arrow_df.itertuples():
            #         index2 = line2.Index
            #         if index == index2:
            #             continue
            #         # if index2 in ignore:
            #         #     continue
            #         if (min(index, index2), max(index, index2)) in pairs_checked:
            #             continue
            #         pairs_checked.add((min(index, index2), max(index, index2))) # add both directions to pairs_checked

            #         j1, k1, j2, k2 = line2.x1, line2.y1, line2.x2, line2.y2
            #         abs_j1, abs_k1, abs_j2, abs_k2 = line2.abs_x1, line2.abs_y1, line2.abs_x2, line2.abs_y2
            #         angle2 = line2.angle

            #         print(distance_to((ax1, ay1), (abs_j2, abs_k2)))
            #         if distance_to((abs_x1, abs_y1), (abs_j2, abs_k2)) < 10 or distance_to((abs_x2, abs_y2), (abs_j1, abs_k1)) < 10: # and distance_to((x2, y2), (j2, k2)) < 10:

            #             angle_diff = line2.angle_diff
            #             angle_diff = min(angle_diff, 360 - angle_diff)
            #             print(angle_diff)

            #             if angle_diff < 200:
            #                 print("Matched arrow head line")
            #                 # line_data[index]['category'] = f"arrow_{arrow_id}"
            #                 # line_data[index2]['category'] = f"arrow_{arrow_id}"
            #                 outpage.draw_line((abs_x1, abs_y1), (abs_x2, abs_y2), color=(1,0,0), width=1, stroke_opacity=1)
            #                 outpage.draw_line((abs_j1, abs_k1), (abs_j2, abs_k2), color=(1,0,0), width=1, stroke_opacity=1)
            #                 arrow_id += 1
            #                 ignore.add(index2)
            #                 break

            #         else:
            #             # Not assigned
            #             outpage.draw_line((ax1, ay1), (ax2, ay2), color=(0, 0, 1), width=1, stroke_opacity=1)



            continue

            # save_debug_file(img, "fitz_page_to_cv2", "Draws the page with cv2 using PyMuPDF embedded get_drawings()", page_num + 1)

            print("Detecting rectangles...")
            # rectangles = detect_rectangles.detect_rectangles(img)

            # print("Removing rectangles from image...")
            # # Scrubs rectangles from image
            # img = drawing.draw_rectangles(
            #         img,
            #         rectangles=rectangles,
            #         fill_color=(255, 0, 255),     # White in BGR
            #         border_color=(255, 255, 255),   # White border
            #         fill_opacity=0,
            #         border_thickness=32
            # )

            # save_debug_file(img, "detected_rectangles", "Scrubs the detected rectangles", page_num + 1)

            classified_lines = detector.detect_all_lines(img)

            # Create visualization image
            img = np.ones((int(page.rect.height * detector.zoom),
                          int(page.rect.width * detector.zoom),
                          3), dtype=np.uint8) * 255

            # Draw lines on image
            detected_img_no_labels = detector.visualize_lines(img, classified_lines, show_labels=False)
            # save_debug_file(detected_img_no_labels, "detected_lines_no_labels", "All detected lines no labels", page_num + 1)

            # Skeletonize the image
            # skeleton_lines: list = skimage_detect_lines.detect_lines(detected_img_no_labels)
            # skeleton_lines = {"other": skeleton_lines}
            # skeleton_img = detector.visualize_lines(img, skeleton_lines, show_labels=False)
            # save_debug_file(skeleton_img, "skeletonized_image", "Skeletonized image", page_num + 1)

            detected_img = detector.visualize_lines(cv_img, classified_lines, show_labels=True)
            png_path = save_debug_file(detected_img, "detected_lines_with_labels", "All detected lines with labels", pdf_page + 1)

            # Create a new page in output PDF
            new_page = output_pdf.new_page(
                width=int(page.rect.width * detector.zoom),
                height=int(page.rect.height * detector.zoom)
            )

            if png_path:
                # Insert the saved PNG into the PDF
                new_page.insert_image(
                    new_page.rect,
                    filename=png_path
                )

            # Export line data to CSV
            line_data = []
            for line_type, lines in classified_lines.items():
                for line in lines:
                    x1, y1, x2, y2 = line
                    angle = detector.calculate_angle(line)
                    length = math.hypot(x2 - x1, y2 - y1)
                    line_data.append({
                        'page': pdf_page + 1,
                        'start_point': (x1, y1),
                        'end_point': (x2, y2),
                        'angle': angle,
                        'length': length,
                        'type': line_type
                    })

            # Append to CSV
            df = pd.DataFrame(line_data)
            # save_debug_file(df, f"detected_lines_page_{page_num + 1}", "Detected lines", page_num + 1)

        # Save the output PDF
        try:
            output_doc.save(os.path.join(save_dir, "detected_lines.pdf"), garbage=4, deflate=True)
            print(f"Results saved to debug/detected_lines.pdf and debug/detected_lines.csv")
            print()
        except:
            pass

    finally:
        doc.close()
        if 'output_pdf' in locals():
            output_doc.close()

    lines_df = pd.DataFrame(all_line_data)
    return lines_df


def detect_support_groups(lines_df, support_boxes_df, filename, save_dir="debug"):
    """
    Find closest arrow line endpoints to support boxes and update support_boxes_df with arrow line references.

    Args:
        lines_df: DataFrame with detected lines including potential_line_type
        support_boxes_df: DataFrame with support boxes including support_id and center coordinates
        filename: PDF filename for reference
        save_dir: Directory to save results

    Returns:
        DataFrame with support boxes updated with closest arrow line info
    """
    if lines_df.empty or support_boxes_df.empty:
        print("No lines or support boxes found")
        return support_boxes_df.copy()

    # Filter for arrow lines only
    arrow_lines_df = lines_df[lines_df['potential_line_type'] == 'arrow_line'].copy()

    if arrow_lines_df.empty:
        print("No arrow lines found")
        return support_boxes_df.copy()

    print(f"Found {len(arrow_lines_df)} arrow lines and {len(support_boxes_df)} support boxes")

    # Create a copy of support_boxes_df to modify
    updated_support_boxes_df = support_boxes_df.copy()

    # Initialize columns for arrow line assignment
    updated_support_boxes_df['closest_arrow_line_idx'] = None
    updated_support_boxes_df['closest_arrow_distance'] = float('inf')
    updated_support_boxes_df['closest_arrow_endpoint'] = None  # 'A' or 'B'
    updated_support_boxes_df['arrow_line_angle'] = None
    updated_support_boxes_df['arrow_line_length'] = None

    # For each support box, find the closest arrow line endpoint
    for idx, support_box in updated_support_boxes_df.iterrows():
        support_center_x = support_box['center_x']
        support_center_y = support_box['center_y']

        print(support_center_x, support_center_y)

        closest_arrow_angle = None
        closest_arrow_length = None
        closest_arrow_line = None
        closest_arrow_idx = None
        min_distance = None

        # Check distance from support center to all arrow line endpoints
        for arrow_idx, arrow_line in arrow_lines_df.iterrows():
            # Get both endpoints of the arrow line
            endpoint_a = arrow_line['endpoint_a']  # (x, y)
            endpoint_b = arrow_line['endpoint_b']  # (x, y)

            # Calculate distance from support center to endpoint A
            dist_a = ((support_center_x - endpoint_a[0])**2 + (support_center_y - endpoint_a[1])**2) ** 0.5

            # Calculate distance from support center to endpoint B
            dist_b = ((support_center_x - endpoint_b[0])**2 + (support_center_y - endpoint_b[1])**2) ** 0.5

            min_dist = min(dist_a, dist_b)
            if min_distance is None or min_dist < min_distance:
                closest_arrow_line = [endpoint_a[0], endpoint_a[1], endpoint_b[0], endpoint_b[1]]
                closest_arrow_angle = arrow_line['angle']
                closest_arrow_length = arrow_line['length']
                min_distance = min_dist
                closest_arrow_idx = arrow_idx
                # Check if endpoint B is closest
                if dist_b < dist_a:
                    # Switch endpoints if B is closer
                    closest_arrow_line = [endpoint_b[0], endpoint_b[1], endpoint_a[0], endpoint_a[1]]

                print("new closest found")

            # print(arrow_idx, closest_arrow_line, min_distance)

        if closest_arrow_line is not None:
            # Assign the closest arrow line to this support box
            updated_support_boxes_df.at[idx, 'closest_arrow_x0'] = closest_arrow_line[0]
            updated_support_boxes_df.at[idx, 'closest_arrow_y0'] = closest_arrow_line[1]
            updated_support_boxes_df.at[idx, 'closest_arrow_x1'] = closest_arrow_line[2]
            updated_support_boxes_df.at[idx, 'closest_arrow_y1'] = closest_arrow_line[3]
            updated_support_boxes_df.at[idx, 'closest_arrow_distance'] = min_distance
            updated_support_boxes_df.at[idx, 'arrow_line_angle'] = closest_arrow_angle
            updated_support_boxes_df.at[idx, 'arrow_line_length'] = closest_arrow_length

    # Save results
    output_file = os.path.join(save_dir, "support_boxes_with_arrow_lines.xlsx")
    updated_support_boxes_df.to_excel(output_file, index=False)
    print(f"Support boxes with arrow line references saved to: {output_file}")

    # Print summary
    assigned_count = updated_support_boxes_df['closest_arrow_line_idx'].notna().sum()
    print(f"\nSupport boxes with assigned arrow lines: {assigned_count}/{len(updated_support_boxes_df)}")

    # Show distance statistics
    distances = updated_support_boxes_df['closest_arrow_distance'][updated_support_boxes_df['closest_arrow_distance'] != float('inf')]
    if len(distances) > 0:
        print(f"Distance statistics - Min: {distances.min():.3f}, Max: {distances.max():.3f}, Mean: {distances.mean():.3f}")

    return updated_support_boxes_df

def draw_support_groups(support_boxes_df, filename, save_dir="debug"):
    """
    Draw support boxes with their bounding boxes and closest arrow lines on PDF.

    Args:
        support_boxes_df: DataFrame with support boxes and closest arrow line info
        filename: PDF filename
        save_dir: Directory to save output
    """
    if support_boxes_df.empty:
        print("No support boxes to draw")
        return

    doc = fitz.open(filename)
    new_doc = fitz.open()

    # Process each page
    for page in doc:
        pdf_page = page.number + 1

        # Filter support boxes for this page
        page_support_boxes = support_boxes_df[support_boxes_df["pdf_page"] == pdf_page]

        if page_support_boxes.empty:
            continue

        # Create new page
        new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)

        # Copy original page content
        new_page.show_pdf_page(new_page.rect, doc, page.number)

        # Add page info
        new_page.insert_text((10, 20), f"Page {pdf_page} - Support Groups", fontsize=12, color=(0, 0, 1))

        # Draw each support box and its closest arrow line
        for idx, support_box in page_support_boxes.iterrows():
            # Get support box coordinates
            x0, y0, x1, y1 = support_box["x0"], support_box["y0"], support_box["x1"], support_box["y1"]
            center_x, center_y = support_box["center_x"], support_box["center_y"]
            support_id = support_box["support_id"]
            support_label = support_box["support_box_label"]

            # Draw support box bounding rectangle
            support_rect = fitz.Rect(x0, y0, x1, y1)
            if support_label == "S":
                # S supports in blue
                new_page.draw_rect(support_rect, color=(0, 0, 1), width=2)
                text_color = (0, 0, 1)
            else:
                # PS supports in green
                new_page.draw_rect(support_rect, color=(0, 1, 0), width=2)
                text_color = (0, 1, 0)

            # Draw support center point
            new_page.draw_circle((center_x, center_y), 3, color=text_color, fill=text_color)

            # Add support label
            new_page.insert_text((x0, y0 - 5), f"{support_label}{support_id}", fontsize=8, color=text_color)

            # Draw closest arrow line if it exists
            if 'closest_arrow_x0' in support_box and support_box['closest_arrow_x0'] is not None:
                arrow_line = support_box['closest_arrow_x0'], support_box['closest_arrow_y0'], support_box['closest_arrow_x1'], support_box['closest_arrow_y1']
                if len(arrow_line) >= 4:
                    ax0, ay0, ax1, ay1 = arrow_line[0], arrow_line[1], arrow_line[2], arrow_line[3]

                    # Draw the arrow line in red
                    new_page.draw_line((ax0, ay0), (ax1, ay1), color=(1, 0, 0), width=1)

                    # Draw connection line from support center to closest arrow endpoint
                    new_page.draw_line((center_x, center_y), (ax0, ay0), color=(1, 0.5, 0), width=1, dashes=[2, 2])

                    # Mark the closest endpoint
                    new_page.draw_circle((ax0, ay0), 2, color=(1, 0, 0), fill=(1, 0, 0))

                    # Add distance and angle info
                    distance = support_box.get('closest_arrow_distance', 0)
                    angle = support_box.get('arrow_line_angle', 0)
                    info_text = f"D:{distance:.1f} A:{angle:.0f}°"
                    new_page.insert_text((center_x + 10, center_y), info_text, fontsize=6, color=(1, 0, 0))

    # Save the annotated PDF
    if new_doc.page_count > 0:
        output_file = os.path.join(save_dir, "support_groups_with_arrow_lines.pdf")
        new_doc.save(output_file, deflate=True)
        print(f"Support groups visualization saved to: {output_file}")
    else:
        print("No pages with support boxes found")

    new_doc.close()
    doc.close()



def main():
    # Project Ids
    brock_id = 1
    axis_id = 7

    # ENSURE PDF FILE uses single forward slashes instead of backslash! As this is exact match needed for sqlite db
    filename = r"C:/Drawings/Clients/brockservices/BRS_0031 - BSLE34891 - Valero St Charles/received/BSLE34891 - Valero St Charles - Combined.pdf"
    filename = r"C:/Drawings/Clients/brockservices/BRS_0032 - STI - ONEOK Medford/received/All Isos - Combined.pdf"

    save_dir = "C:\\Drawings\\Clients\\brockservices\\BRS_0032 - STI - ONEOK Medford\\received\\output"

    project_source = (brock_id, filename)

    projectId, filename = project_source

    raw_df = load_raw_df(project_source)

    print(projectId, filename)

    doc = fitz.open(filename)
    total_pages = len(doc)

    relative_isometric_area = (0, 0, 0.72, 0.83)
    page_range = "1"
    page_range = parse_page_range(page_range, total_pages)

    support_boxes_df = detect_support_boxes(raw_df,
                                            filename=filename,
                                            pages_to_process=page_range,
                                            limit_relative_x0 = relative_isometric_area[0],
                                            limit_relative_y0 = relative_isometric_area[1],
                                            limit_relative_x1 = relative_isometric_area[2],
                                            limit_relative_y1 = relative_isometric_area[3])


    draw_support_boxes(support_boxes_df, filename, save_dir=save_dir)

    project_source = (brock_id, filename)
    # bom_file = r"c:\Drawings\Clients\brockservices\BRS_0031 - BSLE34891 - Valero St Charles\priority\BOM - merged with RFQ_Template_Final_20250818_151124.xlsx"

    # This retrieves potential pipe lines and arrow lines based on angle
    lines_df = detect_lines(filename, support_boxes_df, pages_to_process=page_range, relative_isometric_area=relative_isometric_area, save_dir=save_dir)  # Process first 10 pages

    lines_df.to_excel(os.path.join(save_dir, "detected_pipe_and_arrow_lines.xlsx"), index=False)

    support_boxes_df = detect_support_groups(lines_df, support_boxes_df, filename, save_dir=save_dir)

    support_boxes_df.to_excel(os.path.join(save_dir, "support_boxes_with_arrow_lines.xlsx"), index=False)

    draw_support_groups(support_boxes_df, filename, save_dir=save_dir)


if __name__ == "__main__":
    main()