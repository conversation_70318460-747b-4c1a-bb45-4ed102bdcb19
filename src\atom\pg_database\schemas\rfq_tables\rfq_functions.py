FUNC_TRIGGER_RFQ_REFRESH_FOR_PROJECT ="""
    -- DON'T USE! OVERWRITES CATEGORY COLUMNS!


    -- Example usage for project ID 1:
    -- SELECT refresh_rfq_categories(project_id) AS "Rows Refreshed";

    -- Create a function to refresh categories for a specific project
    CREATE OR REPLACE FUNCTION refresh_rfq_categories(p_project_id INTEGER DEFAULT NULL)
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;  -- Counter for affected rows
        rec RECORD;            -- For iterating through records
    BEGIN
        -- Validate the project_id if provided
        IF p_project_id IS NOT NULL AND NOT EXISTS (
            SELECT 1 FROM public.atem_projects WHERE id = p_project_id
        ) THEN
            RAISE EXCEPTION 'Project ID % does not exist', p_project_id;
        END IF;

        -- Log operation start
        RAISE NOTICE 'Starting RFQ category refresh for project_id: %', 
            CASE WHEN p_project_id IS NULL THEN 'ALL PROJECTS' ELSE p_project_id::TEXT END;
        
        -- Loop through each row in atem_rfq_input that needs updating
        FOR rec IN (
            SELECT ri.id, ri.project_id, ri.rfq_scope, ri.material_description, 
                ri.fitting_category, ri.valve_type, ri.pipe_category, 
                ri.bolt_category, ri.gasket_category
            FROM public.atem_rfq_input ri
            -- Project filter based on parameter
            WHERE (p_project_id IS NULL OR ri.project_id = p_project_id)
        ) LOOP
            -- Log detail for debugging
            RAISE NOTICE 'Processing ID % - "%"', rec.id, rec.material_description;
            
            -- Find the most appropriate category to update based on description patterns
            IF rec.material_description ILIKE '%valve%' THEN
                -- Set valve_type to 'Valve' to trigger categorization
                UPDATE public.atem_rfq_input 
                SET valve_type = 'Valve',
                    updated_at = CURRENT_TIMESTAMP  
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSIF rec.material_description ILIKE '%elbow%' OR 
                rec.material_description ILIKE '%tee%' OR 
                rec.material_description ILIKE '%reducer%' OR
                rec.material_description ILIKE '%coupling%' OR
                rec.material_description ILIKE '%blind%' OR
                rec.material_description ILIKE '%figure 8%' THEN
                -- Set fitting_category for fittings
                UPDATE public.atem_rfq_input 
                SET fitting_category = 'Fitting',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSIF rec.material_description ILIKE '%bolt%' OR 
                rec.material_description ILIKE '%stud%' OR 
                rec.material_description ILIKE '%nut%' THEN
                -- Set bolt_category
                UPDATE public.atem_rfq_input 
                SET bolt_category = 'Bolt',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSIF rec.material_description ILIKE '%gasket%' OR 
                rec.material_description ILIKE '%seal%' THEN
                -- Set gasket_category
                UPDATE public.atem_rfq_input 
                SET gasket_category = 'Gasket',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSIF rec.material_description ILIKE '%pipe%' OR
                rec.material_description ILIKE '%SCH%' THEN
                -- Set pipe_category
                UPDATE public.atem_rfq_input 
                SET pipe_category = 'Pipe',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSIF rec.material_description ILIKE '%base support%' OR
                rec.material_description ILIKE '%support%' THEN
                -- Handle support items by setting proper categories
                UPDATE public.atem_rfq_input 
                SET rfq_scope = 'Supports',
                    general_category = 'Support',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            ELSE
                -- For items we can't specifically categorize, just update the timestamp
                UPDATE public.atem_rfq_input 
                SET updated_at = CURRENT_TIMESTAMP
                WHERE id = rec.id;
                
                v_count := v_count + 1;
            END IF;
        END LOOP;
        
        RAISE NOTICE 'Completed RFQ category refresh. Updated % rows', v_count;
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;

"""

FUNC_TRIGGER_RFQ_REFRESH = """  -- Refreshes timestamps in the RFQ input table to trigger updates for specified project or all projects
   CREATE OR REPLACE FUNCTION refresh_rfq_input_data(p_project_id INTEGER DEFAULT NULL)  -- p_project_id: The ID of the project to refresh, or NULL to refresh all projects
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;  -- v_count: The number of rows updated
    BEGIN
        -- If project_id is provided, only refresh that project's data
        IF p_project_id IS NOT NULL THEN
            UPDATE public.atem_rfq_input
            SET updated_at = CURRENT_TIMESTAMP -- Update timestamp to trigger
            WHERE project_id = p_project_id;
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
            RAISE NOTICE 'Refreshed % rows for project ID %', v_count, p_project_id;
        ELSE
            -- Otherwise refresh all data
            UPDATE public.atem_rfq_input
            SET updated_at = CURRENT_TIMESTAMP; -- Update timestamp to trigger
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
            RAISE NOTICE 'Refreshed % rows across all projects', v_count;
        END IF;
        
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;

    /*-- Refresh all projects
    SELECT refresh_rfq_input_data();

    -- Refresh only project with ID 5
    SELECT refresh_rfq_input_data(project_id); */ 
"""

FUNC_UPDATE_RFQ_PROFILE_ID = """  -- Keeps profile_id in sync with project_id in the RFQ table through a trigger
    DROP TRIGGER IF EXISTS trg_update_rfq_profile_id ON public.atem_rfq;

    /* Create function to keep the profile_id in sync with project_id */
    CREATE OR REPLACE FUNCTION update_rfq_profile_id()
    RETURNS TRIGGER AS $$
    DECLARE
        v_profile_id INTEGER;  -- v_profile_id: The profile ID to update
    BEGIN
        IF NEW.project_id IS NOT NULL THEN
            -- Get the profile_id from the project
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = NEW.project_id
            LIMIT 1;
            
            -- Update the profile_id
            NEW.profile_id := v_profile_id;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Apply the trigger to atem_rfq
    CREATE TRIGGER trg_update_rfq_profile_id
    BEFORE INSERT OR UPDATE OF project_id ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION update_rfq_profile_id();

    /* Update existing records */
    /* UPDATE public.atem_rfq r
    SET profile_id = p.profile_id
    FROM public.atem_projects p
    WHERE r.project_id = p.id; */
"""

# ---------------------RFQ FUNCTIONS -------------------------



FUNC_UPDATE_CLIENT_NAME = """  -- Populates client_name automatically based on ref_id through a trigger
    -- Create a function that will populate client_name based on ref_id
    CREATE OR REPLACE FUNCTION update_client_name()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Only proceed if ref_id is not null
        IF NEW.ref_id IS NOT NULL THEN
            -- Look up client_name from atem_clients table
            SELECT client_name INTO NEW.client_name
            FROM public.atem_clients
            WHERE id = NEW.ref_id;
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create a trigger that fires before INSERT or UPDATE
    CREATE TRIGGER tr_update_client_name
    BEFORE INSERT OR UPDATE OF ref_id ON public.atem_client_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_client_name();
"""

FUNC_SYNC_RFQ_TO_INPUT = """  -- Syncs data from RFQ to RFQ_INPUT table, creating new input records as needed
    DROP TRIGGER IF EXISTS trg_sync_rfq_to_input ON public.atem_rfq;

    CREATE OR REPLACE FUNCTION sync_rfq_to_input()
    RETURNS TRIGGER AS $$
    DECLARE
        input_id INTEGER;  -- input_id: The ID of the RFQ_INPUT record
        existing_record RECORD; -- To store existing record data
    BEGIN
        -- Skip if this is an update being triggered by rfq_input sync
        IF (TG_OP = 'UPDATE' AND NEW.rfq_input_id IS NOT NULL) THEN
            RETURN NEW;
        END IF;
        
        -- Check if a matching RFQ_INPUT record already exists
        -- Change: Use UPPER() for case-insensitive comparison
    SELECT id INTO input_id
    FROM public.atem_rfq_input
    WHERE project_id = NEW.project_id
    AND UPPER(material_description) = UPPER(NEW.material_description);
        
        -- If no matching record exists, create one
        IF input_id IS NULL THEN
            INSERT INTO public.atem_rfq_input (
                project_id, material_description, normalized_description,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                NEW.project_id, NEW.material_description, NEW.normalized_description,
                NEW.rfq_scope, NEW.general_category, NEW.unit_of_measure,
                NEW.material, NEW.abbreviated_material, NEW.technical_standard,
                NEW.astm, NEW.grade, NEW.rating, NEW.schedule, NEW.coating,
                NEW.forging, NEW.ends, NEW.item_tag, NEW.tie_point,
                NEW.pipe_category, NEW.valve_type, NEW.fitting_category,
                NEW.weld_category, NEW.bolt_category, NEW.gasket_category,
                NEW.notes, NEW.deleted, NEW.ignore_item,
                NEW.created_by, NEW.updated_by
            )
            RETURNING id INTO input_id;
        ELSE
            -- CHANGE: Check existing record and don't overwrite valid category data
            -- Only update if the existing record has NULL/empty category data
            SELECT * INTO existing_record 
            FROM public.atem_rfq_input 
            WHERE id = input_id;
            
            -- Selective update that preserves category information
            UPDATE public.atem_rfq_input
            SET
                -- These fields always get updated
                normalized_description = NEW.normalized_description,
                unit_of_measure = NEW.unit_of_measure,
                material = NEW.material,
                abbreviated_material = NEW.abbreviated_material,
                technical_standard = NEW.technical_standard,
                astm = NEW.astm,
                grade = NEW.grade,
                rating = NEW.rating,
                schedule = NEW.schedule,
                coating = NEW.coating,
                forging = NEW.forging,
                ends = NEW.ends,
                item_tag = NEW.item_tag,
                tie_point = NEW.tie_point,
                notes = NEW.notes,
                deleted = NEW.deleted,
                ignore_item = NEW.ignore_item,
                updated_by = NEW.updated_by,
                
                -- Only update category fields if they're NULL in the existing record
                rfq_scope = COALESCE(existing_record.rfq_scope, NEW.rfq_scope),
                general_category = COALESCE(existing_record.general_category, NEW.general_category),
                pipe_category = COALESCE(existing_record.pipe_category, NEW.pipe_category),
                valve_type = COALESCE(existing_record.valve_type, NEW.valve_type),
                fitting_category = COALESCE(existing_record.fitting_category, NEW.fitting_category),
                weld_category = COALESCE(existing_record.weld_category, NEW.weld_category),
                bolt_category = COALESCE(existing_record.bolt_category, NEW.bolt_category),
                gasket_category = COALESCE(existing_record.gasket_category, NEW.gasket_category)
            WHERE id = input_id;
        END IF;
        
        -- Link the RFQ record to its RFQ_INPUT record
        NEW.rfq_input_id = input_id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER trg_sync_rfq_to_input
    BEFORE INSERT OR UPDATE ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION sync_rfq_to_input();
"""

FUNC_SYNC_INPUT_TO_RFQ = """  -- Updates RFQ records when corresponding RFQ_INPUT records change, maintaining data consistency
    DROP TRIGGER IF EXISTS trg_sync_input_to_rfq ON public.atem_rfq_input;

    -- Function to sync RFQ_INPUT→RFQ direction
    CREATE OR REPLACE FUNCTION sync_input_to_rfq()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Only sync basic attributes, not size or quantity
        UPDATE public.atem_rfq
        SET
            material_description = NEW.material_description, -- Will update the material description. Need to handle cases where material description creates unique constraint conflicts
            normalized_description = NEW.normalized_description,
            rfq_scope = NEW.rfq_scope,
            general_category = NEW.general_category,
            unit_of_measure = NEW.unit_of_measure,
            material = NEW.material,
            abbreviated_material = NEW.abbreviated_material,
            technical_standard = NEW.technical_standard,
            astm = NEW.astm,
            grade = NEW.grade,
            rating = NEW.rating,
            schedule = NEW.schedule,
            coating = NEW.coating,
            forging = NEW.forging,
            ends = NEW.ends,
            item_tag = NEW.item_tag,
            tie_point = NEW.tie_point,
            pipe_category = NEW.pipe_category,
            valve_type = NEW.valve_type,
            fitting_category = NEW.fitting_category,
            weld_category = NEW.weld_category,
            bolt_category = NEW.bolt_category,
            gasket_category = NEW.gasket_category,
            notes = NEW.notes,
            deleted = NEW.deleted,
            ignore_item = NEW.ignore_item
        WHERE rfq_input_id = NEW.id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER trg_sync_input_to_rfq
    AFTER UPDATE ON public.atem_rfq_input
    FOR EACH ROW
    EXECUTE FUNCTION sync_input_to_rfq();

"""

FUNC_UPDATE_RFQ_CATEGORIES_FROM_MAPPING = """  -- Updates RFQ and RFQ_INPUT categories based on component mapping references
    -- Update the function to handle special categories with defaults
    DROP TRIGGER IF EXISTS trg_update_rfq_categories_from_mapping ON public.atem_rfq;
    
    CREATE OR REPLACE FUNCTION update_categories_from_mapping()
    RETURNS TRIGGER AS $$
    DECLARE
        component_val VARCHAR(100);
        v_profile_id INTEGER;
        v_rfq_scope VARCHAR(100);
        v_general_category VARCHAR(100);
        changed_column VARCHAR(50);
        mapping_found BOOLEAN := FALSE;
    BEGIN
        -- Debug entry point
        RAISE NOTICE 'TRIGGER FIRED: Table=%, Op=%, Time=%', 
            TG_TABLE_NAME, TG_OP, now();
        
        -- Determine which column has changed and store its value
        IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND 
            (NEW.pipe_category IS DISTINCT FROM OLD.pipe_category OR 
            NEW.fitting_category IS DISTINCT FROM OLD.fitting_category OR
            NEW.gasket_category IS DISTINCT FROM OLD.gasket_category OR
            NEW.bolt_category IS DISTINCT FROM OLD.bolt_category OR
            NEW.valve_type IS DISTINCT FROM OLD.valve_type)) THEN
            
            RAISE NOTICE 'Changes detected in component fields';
            
            -- Reset mapping_not_found flag on any component change
            NEW.mapping_not_found := FALSE;
            
            -- Determine which component field changed and get its value
            IF NEW.pipe_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.pipe_category IS DISTINCT FROM OLD.pipe_category) THEN
                component_val := NEW.pipe_category;
                changed_column := 'pipe_category';
            ELSIF NEW.fitting_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.fitting_category IS DISTINCT FROM OLD.fitting_category) THEN
                component_val := NEW.fitting_category;
                changed_column := 'fitting_category';
            ELSIF NEW.valve_type IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.valve_type IS DISTINCT FROM OLD.valve_type) THEN
                component_val := NEW.valve_type;
                changed_column := 'valve_type';
            ELSIF NEW.gasket_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.gasket_category IS DISTINCT FROM OLD.gasket_category) THEN
                component_val := NEW.gasket_category;
                changed_column := 'gasket_category';
            ELSIF NEW.bolt_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.bolt_category IS DISTINCT FROM OLD.bolt_category) THEN
                component_val := NEW.bolt_category;
                changed_column := 'bolt_category';
            ELSE
                -- No relevant change or no value to map
                RAISE NOTICE 'No relevant changes detected in component fields';
                RETURN NEW;
            END IF;
            
            RAISE NOTICE 'Changed column: %, New value: %', changed_column, component_val;
            
            -- For atem_rfq_input, get profile_id from projects table
            IF TG_TABLE_NAME = 'atem_rfq_input' THEN
                SELECT profile_id INTO v_profile_id
                FROM public.atem_projects
                WHERE id = NEW.project_id;
                
                RAISE NOTICE 'Looking up profile_id for project_id=%: Found profile_id=%', 
                    NEW.project_id, v_profile_id;
            ELSE
                v_profile_id := NEW.profile_id;
                RAISE NOTICE 'Using direct profile_id=%', v_profile_id;
            END IF;
            
            -- Look up rfq_scope and general_category from component mapping
            IF v_profile_id IS NOT NULL AND component_val IS NOT NULL THEN
                RAISE NOTICE 'Looking up mapping for profile_id=% and component_name=%', 
                    v_profile_id, component_val;
                    
                SELECT takeoff_category, general_category INTO v_rfq_scope, v_general_category
                FROM public.atem_bom_component_mapping
                WHERE profile_id = v_profile_id AND component_name = component_val
                LIMIT 1;
                
                RAISE NOTICE 'Mapping lookup result: rfq_scope=%, general_category=%', 
                    v_rfq_scope, v_general_category;
                
                -- Update the categories if mapping was found
                IF v_rfq_scope IS NOT NULL OR v_general_category IS NOT NULL THEN
                    RAISE NOTICE 'Mapping found! Updating with mapped values';
                    
                    -- Only update values that were found in the mapping
                    IF v_rfq_scope IS NOT NULL THEN
                        NEW.rfq_scope := v_rfq_scope;
                    END IF;
                    
                    IF v_general_category IS NOT NULL THEN
                        NEW.general_category := v_general_category;
                    END IF;
                    
                    mapping_found := TRUE;
                ELSE
                    -- No mapping found, set flag
                    NEW.mapping_not_found := TRUE;
                    RAISE NOTICE 'No mapping found in lookup table. Using defaults for %', changed_column;
                    
                    -- CHANGE: Only apply defaults if explicitly set and current values are NULL
                -- Apply default values based on the column type only if field explicitly set
                    IF component_val IS NOT NULL THEN
                        CASE
                            WHEN changed_column = 'valve_type' THEN
                                -- Only set if rfq_scope is NULL (don't overwrite existing)
                                IF NEW.rfq_scope IS NULL THEN
                                    RAISE NOTICE 'Applying valve_type default: rfq_scope = Valves';
                                    NEW.rfq_scope := 'Valves';
                                END IF;
                                
                            WHEN changed_column = 'pipe_category' THEN
                                -- Only set if rfq_scope is NULL (don't overwrite existing)
                                IF NEW.rfq_scope IS NULL THEN
                                    RAISE NOTICE 'Applying pipe_category defaults: rfq_scope = Pipe, general_category = LF';
                                    NEW.rfq_scope := 'Pipe';
                                    NEW.general_category := 'LF';
                                END IF;
                                
                            WHEN changed_column = 'bolt_category' THEN
                                -- Only set if rfq_scope is NULL (don't overwrite existing)
                                IF NEW.rfq_scope IS NULL THEN
                                    RAISE NOTICE 'Applying bolt_category default: rfq_scope = Bolts';
                                    NEW.rfq_scope := 'Bolts';
                                END IF;
                                
                            WHEN changed_column = 'gasket_category' THEN
                                -- Only set if rfq_scope is NULL (don't overwrite existing)
                                IF NEW.rfq_scope IS NULL THEN
                                    RAISE NOTICE 'Applying gasket_category default: rfq_scope = Gaskets';
                                    NEW.rfq_scope := 'Gaskets';
                                END IF;
                                
                            ELSE
                                RAISE NOTICE 'No default handling for % column', changed_column;
                        END CASE;
                    ELSE
                        RAISE NOTICE 'Skipping defaults since component_val is NULL';
                    END IF;
                END IF;
            ELSE
                RAISE NOTICE 'Missing required data: profile_id=%, component_val=%', v_profile_id, component_val;
            END IF;
        ELSE
            RAISE NOTICE 'No changes to component fields detected';
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Make sure triggers are properly recreated
    DROP TRIGGER IF EXISTS trg_update_rfq_categories ON public.atem_rfq;
    DROP TRIGGER IF EXISTS trg_update_rfq_input_categories ON public.atem_rfq_input;

    -- Create triggers for both tables
    CREATE TRIGGER trg_update_rfq_categories
    BEFORE INSERT OR UPDATE OF pipe_category, fitting_category, gasket_category, bolt_category, valve_type
    ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION update_categories_from_mapping();

    CREATE TRIGGER trg_update_rfq_input_categories
    BEFORE INSERT OR UPDATE OF pipe_category, fitting_category, gasket_category, bolt_category, valve_type
    ON public.atem_rfq_input
    FOR EACH ROW
    EXECUTE FUNCTION update_categories_from_mapping();
"""


FUNCTION_PROPOGATE_BOM_MAPPING_CHANGES = """  -- Propagates changes in BOM component mapping to RFQ and RFQ_INPUT records
    CREATE OR REPLACE FUNCTION propagate_mapping_changes()
    RETURNS TRIGGER AS $$
    DECLARE
        rec RECORD;  -- rec: A record from the query
        field_name VARCHAR(50);  -- field_name: The name of the field being updated
    BEGIN
        -- Determine which component field to match based on the values
        IF TG_OP = 'UPDATE' THEN
            -- Only proceed if takeoff_category or general_category have changed
            IF (NEW.takeoff_category IS DISTINCT FROM OLD.takeoff_category OR 
                NEW.general_category IS DISTINCT FROM OLD.general_category) THEN
                
                -- Find all RFQ_INPUT records that match this component mapping for pipe_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.pipe_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for fitting_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.fitting_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for gasket_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.gasket_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for bolt_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.bolt_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
            END IF;
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create trigger on atem_bom_component_mapping
    CREATE TRIGGER trg_propagate_mapping_changes
    AFTER UPDATE ON public.atem_bom_component_mapping
    FOR EACH ROW
    EXECUTE FUNCTION propagate_mapping_changes();
"""

# ---------------------BOM FUNCTIONS -------------------------
FUNCTION_UPDATE_BOM_PROFILE_ID = """
    DROP TRIGGER IF EXISTS trg_update_bom_profile_id ON public.bom;

    /* Create function to keep the profile_id in sync with project_id */
    CREATE OR REPLACE FUNCTION update_bom_profile_id()
    RETURNS TRIGGER AS $$
    DECLARE
        v_profile_id INTEGER;  -- v_profile_id: The profile ID to update
    BEGIN
        IF NEW.project_id IS NOT NULL THEN
            -- Get the profile_id from the project
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = NEW.project_id
            LIMIT 1;
            
            -- Update the profile_id
            NEW.profile_id := v_profile_id;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Apply the trigger to bom
    CREATE TRIGGER trg_update_bom_profile_id
    BEFORE INSERT OR UPDATE OF project_id ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION update_bom_profile_id();

    /* Update existing records */
    UPDATE public.bom b
    SET profile_id = p.profile_id
    FROM public.atem_projects p
    WHERE b.project_id = p.id;
"""

FUNC_SYNC_RFQ_TO_BOM = """
    -- Function for BOM→RFQ direction with recursion protection
    CREATE OR REPLACE FUNCTION sync_bom_to_rfq()
    RETURNS TRIGGER AS $$
    DECLARE
        rfq_id INTEGER;
        setting_value TEXT;
    BEGIN
        -- RECURSION PROTECTION: Skip if this is an update being triggered by an RFQ sync
        IF (TG_OP = 'UPDATE' AND OLD.rfq_ref_id IS NOT NULL AND OLD.rfq_ref_id = NEW.rfq_ref_id) THEN
            RETURN NEW;
        END IF;
        
        -- Check if a matching RFQ record already exists for this material and size combination
        -- Modified to use only size and material_description for uniqueness
        SELECT id INTO rfq_id
        FROM public.atem_rfq
        WHERE project_id = NEW.project_id
        AND material_description = NEW.material_description
        AND size = NEW.size
        LIMIT 1;
        
        -- If no matching record exists, create one
        IF rfq_id IS NULL THEN
            INSERT INTO public.atem_rfq (
                project_id, profile_id, material_description, normalized_description,
                size, size1, size2,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                calculated_eq_length, calculated_area,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                NEW.project_id, NEW.profile_id, NEW.material_description, NEW.normalized_description,
                NEW.size, NEW.size1, NEW.size2,
                NEW.rfq_scope, NEW.general_category, NEW.unit_of_measure,
                NEW.material, NEW.abbreviated_material, NEW.technical_standard,
                NEW.astm, NEW.grade, NEW.rating, NEW.schedule, NEW.coating,
                NEW.forging, NEW.ends, NEW.item_tag, NEW.tie_point,
                NEW.pipe_category, NEW.valve_type, NEW.fitting_category,
                NEW.weld_category, NEW.bolt_category, NEW.gasket_category,
                NEW.calculated_eq_length, NEW.calculated_area,
                NEW.notes, NEW.deleted, NEW.ignore_item,
                NEW.created_by, NEW.updated_by
            )
            RETURNING id INTO rfq_id;
        END IF;
        
        -- Link the BOM record to its RFQ record
        NEW.rfq_ref_id = rfq_id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Function for RFQ→BOM synchronization (classification updates)
    CREATE OR REPLACE FUNCTION sync_rfq_to_bom()
    RETURNS TRIGGER AS $$
    DECLARE
        in_sync BOOLEAN;
        setting_value TEXT;
    BEGIN
        -- Check if we're in a sync operation to prevent infinite recursion
        -- SELECT current_setting('sync_in_progress.rfq_bom', TRUE)::BOOLEAN INTO in_sync;

        BEGIN
            -- Get the current setting value safely
            setting_value := current_setting('sync_in_progress.rfq_bom', TRUE);
            
            -- Handle the empty string case explicitly
            IF setting_value IS NULL OR setting_value = '' THEN
                in_sync := FALSE;
            ELSE
                in_sync := setting_value::BOOLEAN;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            -- In case of any error, assume we're not in sync mode
            in_sync := FALSE;
        END;

        IF in_sync THEN
            RETURN NEW;
        END IF;
        
        -- Set flag to indicate we're in a sync operation
        PERFORM set_config('sync_in_progress.rfq_bom', 'true', TRUE);
        
        BEGIN
            -- Update linked BOM records with classification data from RFQ
            UPDATE public.bom
            SET 
                general_category = NEW.general_category,
                rfq_scope = NEW.rfq_scope,
                material = NEW.material,
                rating = NEW.rating,
                ends = NEW.ends,
                fitting_category = NEW.fitting_category,
                valve_type = NEW.valve_type,
                calculated_eq_length = NEW.calculated_eq_length,
                calculated_area = NEW.calculated_area,
                updated_at = CURRENT_TIMESTAMP
            WHERE rfq_ref_id = NEW.id;
            
            -- Reset the flag when done
            PERFORM set_config('sync_in_progress.rfq_bom', 'false', TRUE);
            
            EXCEPTION WHEN OTHERS THEN
                -- Ensure flag is reset even if there's an error
                PERFORM set_config('sync_in_progress.rfq_bom', 'false', TRUE);
                RAISE;
        END;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Drop existing triggers if they exist
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq ON public.bom;
    DROP TRIGGER IF EXISTS trg_sync_rfq_to_bom ON public.atem_rfq;

    -- Create trigger for BOM→RFQ linkage
    CREATE TRIGGER trg_sync_bom_to_rfq
    BEFORE INSERT OR UPDATE ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION sync_bom_to_rfq();

    -- Create trigger for RFQ→BOM synchronization
    CREATE TRIGGER trg_sync_rfq_to_bom
    AFTER UPDATE OF general_category, rfq_scope, material, rating, ends, 
                    fitting_category, valve_type, calculated_eq_length, calculated_area
    ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION sync_rfq_to_bom();
"""