"""
Classification Nodes for LangGraph BOM Classification Workflow

This module implements the individual node functions for each stage of the
LangGraph classification workflow.
"""

import asyncio
import time
import json
from typing import Dict, Any, Optional

# Handle imports for both direct execution and module import
try:
    from .state_models import (
        ClassificationState,
        MaterialAnalysisResponse,
        CategoryClassificationResponse,
        QADecisionResponse,
        SelfAuditResponse,
        update_state_metadata
    )
    from .langgraph_prompts import (
        build_material_analysis_prompt,
        build_fitting_classification_prompt,
        build_pipe_classification_prompt,
        build_qa_decision_prompt,
        build_self_audit_prompt
    )
    from .category_intelligence import (
        lookup_component,
        is_vendor_code,
        get_component_intelligence,
        resolve_abbreviations
    )
except ImportError:
    # Direct execution - use absolute imports
    from state_models import (
        ClassificationState,
        MaterialAnalysisResponse,
        CategoryClassificationResponse,
        QADecisionResponse,
        SelfAuditResponse,
        update_state_metadata
    )
    from langgraph_prompts import (
        build_material_analysis_prompt,
        build_fitting_classification_prompt,
        build_pipe_classification_prompt,
        build_qa_decision_prompt,
        build_self_audit_prompt
    )
    from category_intelligence import (
        lookup_component,
        is_vendor_code,
        get_component_intelligence,
        resolve_abbreviations
    )

# Import model handler from integration layer
try:
    from .integration_layer import get_model_handler, LangGraphConfig
except ImportError:
    from integration_layer import get_model_handler, LangGraphConfig


# Development flags for testing
ENABLE_MATERIAL_ANALYSIS = True
ENABLE_CATEGORY_CLASSIFICATION = True
ENABLE_QA_DECISIONS = True
ENABLE_SELF_AUDIT = True
DEBUG_MODE = True

# Global model handler - will be initialized when needed
_model_handler = None


def get_or_create_model_handler(config: LangGraphConfig = None) -> Any:
    """Get or create the global model handler"""
    global _model_handler
    if _model_handler is None:
        _model_handler = get_model_handler(config)
    return _model_handler


async def material_analysis_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 1: Material Analysis Node

    Extracts key properties from material description and determines
    primary processing path for subsequent classification stages.

    Now supports actual model calls with fallback to simulation.
    """

    if not ENABLE_MATERIAL_ANALYSIS:
        # Skip this stage for testing
        return {
            **state,
            "processing_path": "fitting",  # Default for testing
            "current_stage": "classification",
            "workflow_path": state["workflow_path"] + ["material_analysis_skipped"]
        }

    start_time = time.time()

    try:
        material_description = state["material_description"]
        item_id = state.get("item_id", "unknown")

        if DEBUG_MODE:
            print(f"\n🔍 STAGE 1: Material Analysis")
            print(f"   Description: {material_description}")
            print(f"   Item ID: {item_id}")

        # Quick vendor code check
        if is_vendor_code(material_description):
            if DEBUG_MODE:
                print(f"   ✓ Detected vendor code - routing to miscellaneous")

            return update_state_metadata(
                {
                    **state,
                    "processing_path": "miscellaneous",
                    "current_stage": "classification",
                    "extracted_properties": {"vendor_code": True},
                    "confidence_scores": {"vendor_detection": 0.95}
                },
                stage_name="material_analysis",
                processing_time=time.time() - start_time,
                model_calls=0,
                tokens_used=0,
                stage_output={"category": "miscellaneous", "reason": "vendor_code"}
            )

        # Component lookup for quick classification
        component_info = lookup_component(material_description)
        if component_info and component_info["match_confidence"] > 0.8:
            category_map = {
                "Fittings": "fitting",
                "Valves": "valve",
                "Flanges": "flange",
                "Pipe": "pipe"
            }

            processing_path = category_map.get(component_info["category"], "miscellaneous")

            if DEBUG_MODE:
                print(f"   ✓ Component lookup match: {component_info['full_name']} → {processing_path}")

            return update_state_metadata(
                {
                    **state,
                    "processing_path": processing_path,
                    "current_stage": "classification",
                    "extracted_properties": {
                        "component_match": component_info,
                        "lookup_confidence": component_info["match_confidence"]
                    },
                    "confidence_scores": {"category_determination": component_info["match_confidence"]}
                },
                stage_name="material_analysis",
                processing_time=time.time() - start_time,
                model_calls=0,
                tokens_used=0,
                stage_output={"category": processing_path, "method": "component_lookup"}
            )

        # Build analysis prompt
        analysis_prompt = build_material_analysis_prompt(material_description)

        # Get model handler and configuration from state or create default
        model_config = state.get("model_config")  # Can be passed in state
        model_handler = get_or_create_model_handler(model_config)

        # Try to call actual model first, fallback to simulation on error
        try:
            if DEBUG_MODE:
                print(f"   🤖 Calling model for material analysis...")

            # Call model for Stage 1
            model_response = await model_handler.call_model_for_stage(
                stage_name="material_analysis",
                prompt=analysis_prompt,
                response_schema=MaterialAnalysisResponse,
                item_id=item_id
            )

            # Extract response data
            if hasattr(model_response, 'primary_category'):
                # Pydantic model response
                response_data = {
                    "primary_category": model_response.primary_category,
                    "extracted_properties": model_response.extracted_properties,
                    "confidence": model_response.confidence,
                    "complexity_level": model_response.complexity_level,
                    "reasoning": model_response.reasoning
                }
                model_calls = 1
                tokens_used = len(analysis_prompt) // 4  # Rough estimate

                if DEBUG_MODE:
                    print(f"   ✅ Model response: {model_response.primary_category} (confidence: {model_response.confidence})")
                    print(f"   ✅ Reasoning: {model_response.reasoning}")

            elif isinstance(model_response, dict):
                # Dict response - handle enhanced prompt structure
                response_data = model_response
                model_calls = 1
                tokens_used = len(analysis_prompt) // 4

                # Check if this is the enhanced prompt response with nested structure
                if 'category_mapping' in response_data:
                    if DEBUG_MODE:
                        print(f"   🔍 Enhanced prompt response detected with category_mapping")

                    # Extract category_mapping for field classifications
                    category_mapping = response_data.get('category_mapping', {})

                    # Use category_mapping as the primary field classifications
                    if category_mapping:
                        response_data.update(category_mapping)
                        if DEBUG_MODE:
                            print(f"   ✅ Flattened category_mapping: {list(category_mapping.keys())}")

                if DEBUG_MODE:
                    print(f"   ✅ Model response (dict): {response_data.get('primary_category', 'unknown')}")

            else:
                # Unexpected response format - raise error
                error_msg = f"❌ Unexpected model response format: {type(model_response)}"
                if DEBUG_MODE:
                    print(f"   {error_msg}")
                raise RuntimeError(error_msg)

            # Map primary_category to processing_path
            category_to_path_map = {
                "Fittings": "fitting",
                "Valves": "valve",
                "Flanges": "flange",
                "Pipe": "pipe",
                "Bolts": "bolt",
                "Gaskets": "gasket",
                "Supports": "support",
                "Instruments": "instrument",
                "Miscellaneous": "miscellaneous"
            }

            primary_category = response_data.get("primary_category", "unknown")
            processing_path = category_to_path_map.get(primary_category, "miscellaneous")

            if DEBUG_MODE:
                print(f"   🔄 Mapped '{primary_category}' → '{processing_path}'")

            # Update response_data with the mapped processing path
            response_data["processing_path"] = processing_path

        except Exception as model_error:
            error_msg = f"❌ Model call failed: {model_error}"
            if DEBUG_MODE:
                print(f"   {error_msg}")
                import traceback
                traceback.print_exc()
            raise RuntimeError(error_msg)

        if DEBUG_MODE:
            print(f"   ✓ Analysis complete: {response_data['primary_category']} (confidence: {response_data['confidence']})")
            print(f"   ✓ Extracted properties: {list(response_data.get('extracted_properties', {}).keys())}")

        # Extract field classifications from enhanced prompt response
        field_classifications = {}

        # If we have category_mapping, use those as initial field classifications
        if 'category_mapping' in response_data:
            category_mapping = response_data.get('category_mapping', {})
            for field, value in category_mapping.items():
                if value and value != "mapped_if_fitting" and value != "mapped_if_valve" and value != "mapped_if_pipe":
                    field_classifications[field] = value

        # Also extract any direct field classifications from the response
        classification_fields = ['rfq_scope', 'material', 'astm', 'grade', 'schedule', 'rating',
                                'ansme_ansi', 'ends', 'forging', 'fitting_category', 'valve_type', 'pipe_category']
        for field in classification_fields:
            if field in response_data and response_data[field]:
                field_classifications[field] = response_data[field]

        if DEBUG_MODE and field_classifications:
            print(f"   ✅ Stage 1 field classifications: {list(field_classifications.keys())}")

        # Update state with analysis results
        return update_state_metadata(
            {
                **state,
                "processing_path": response_data["processing_path"],  # Use mapped processing path
                "current_stage": "classification",
                "extracted_properties": response_data.get("extracted_properties", {}),
                "field_classifications": field_classifications,  # Store initial classifications
                "confidence_scores": {"category_determination": response_data.get("confidence", 0.0)}
            },
            stage_name="material_analysis",
            processing_time=time.time() - start_time,
            model_calls=model_calls,
            tokens_used=tokens_used,
            stage_output=response_data
        )

    except Exception as e:
        if DEBUG_MODE:
            print(f"   ❌ Error in material analysis: {str(e)}")

        # Fallback to miscellaneous on error
        return update_state_metadata(
            {
                **state,
                "processing_path": "miscellaneous",
                "current_stage": "classification",
                "identified_issues": [
                    {
                        "stage": "material_analysis",
                        "error": str(e),
                        "fallback_applied": True
                    }
                ]
            },
            stage_name="material_analysis_error",
            processing_time=time.time() - start_time
        )


async def fitting_classification_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 2: Fitting-Specific Classification Node
    
    Applies fitting-specific intelligence and rules to classify
    all relevant fields for fitting components.
    """
    
    if not ENABLE_CATEGORY_CLASSIFICATION:
        return {
            **state,
            "current_stage": "qa_decisions",
            "workflow_path": state["workflow_path"] + ["fitting_classification_skipped"]
        }
    
    start_time = time.time()
    
    try:
        material_description = state["material_description"]
        extracted_properties = state.get("extracted_properties", {})
        
        if DEBUG_MODE:
            print(f"\n🔧 STAGE 2: Fitting Classification")
            print(f"   Description: {material_description}")
            print(f"   Extracted properties: {extracted_properties}")
        
        # Get fitting-specific intelligence
        fitting_intelligence = get_component_intelligence("fitting")
        
        # Build classification prompt
        classification_prompt = build_fitting_classification_prompt(
            material_description,
            extracted_properties,
            fitting_intelligence
        )
        
        # Get model handler and configuration from state or create default
        model_config = state.get("model_config")
        model_handler = get_or_create_model_handler(model_config)

        # Call actual model for fitting classification
        try:
            if DEBUG_MODE:
                print(f"   🤖 Calling model for fitting classification...")

            # Call model for Stage 2 - Fitting Classification
            model_response = await model_handler.call_model_for_stage(
                stage_name="fitting_classification",
                prompt=classification_prompt,
                response_schema=dict,  # Use dict for now, can be enhanced with Pydantic model
                item_id=state.get("item_id", "unknown")
            )

            # Extract response data
            if isinstance(model_response, dict):
                response_data = model_response
                model_calls = 1
                tokens_used = len(classification_prompt) // 4

                if DEBUG_MODE:
                    print(f"   ✅ Model response: {response_data.get('fitting_category', 'Unknown')}")
                    print(f"   ✅ Overall confidence: {response_data.get('overall_confidence', 0.0)}")

            else:
                # Unexpected response format - raise error
                error_msg = f"❌ Unexpected model response format: {type(model_response)}"
                if DEBUG_MODE:
                    print(f"   {error_msg}")
                raise RuntimeError(error_msg)

        except Exception as model_error:
            error_msg = f"❌ Model call failed: {model_error}"
            if DEBUG_MODE:
                print(f"   {error_msg}")
                import traceback
                traceback.print_exc()
            raise RuntimeError(error_msg)
        
        # Merge with existing field classifications from Stage 1
        existing_classifications = state.get("field_classifications", {})
        merged_classifications = existing_classifications.copy()
        merged_classifications.update(response_data)  # Stage 2 results override Stage 1

        # Merge confidence scores
        existing_confidence = state.get("confidence_scores", {})
        merged_confidence = existing_confidence.copy()
        merged_confidence.update(response_data.get("field_confidence_scores", {}))

        if DEBUG_MODE:
            print(f"   ✅ Merged classifications: {len(merged_classifications)} total fields")

        # Update state with classification results
        return update_state_metadata(
            {
                **state,
                "current_stage": "qa_decisions",
                "field_classifications": merged_classifications,
                "confidence_scores": merged_confidence
            },
            stage_name="fitting_classification",
            processing_time=time.time() - start_time,
            model_calls=model_calls,
            tokens_used=tokens_used,
            stage_output=response_data
        )
        
    except Exception as e:
        if DEBUG_MODE:
            print(f"   ❌ Error in fitting classification: {str(e)}")
        
        return update_state_metadata(
            {
                **state,
                "current_stage": "qa_decisions",
                "identified_issues": state.get("identified_issues", []) + [
                    {
                        "stage": "fitting_classification",
                        "error": str(e)
                    }
                ]
            },
            stage_name="fitting_classification_error",
            processing_time=time.time() - start_time
        )


async def pipe_classification_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 2: Pipe-Specific Classification Node
    
    Applies pipe-specific intelligence and rules to classify
    all relevant fields for pipe components.
    """
    
    if not ENABLE_CATEGORY_CLASSIFICATION:
        return {
            **state,
            "current_stage": "qa_decisions",
            "workflow_path": state["workflow_path"] + ["pipe_classification_skipped"]
        }
    
    start_time = time.time()
    
    try:
        material_description = state["material_description"]
        extracted_properties = state.get("extracted_properties", {})
        
        if DEBUG_MODE:
            print(f"\n🔧 STAGE 2: Pipe Classification")
            print(f"   Description: {material_description}")
        
        # Build classification prompt
        classification_prompt = build_pipe_classification_prompt(
            material_description,
            extracted_properties
        )
        
        # Get model handler and configuration from state or create default
        model_config = state.get("model_config")
        model_handler = get_or_create_model_handler(model_config)

        # Call actual model for pipe classification
        try:
            if DEBUG_MODE:
                print(f"   🤖 Calling model for pipe classification...")

            # Call model for Stage 2 - Pipe Classification
            model_response = await model_handler.call_model_for_stage(
                stage_name="pipe_classification",
                prompt=classification_prompt,
                response_schema=dict,  # Use dict for now, can be enhanced with Pydantic model
                item_id=state.get("item_id", "unknown")
            )

            # Extract response data
            if isinstance(model_response, dict):
                response_data = model_response
                model_calls = 1
                tokens_used = len(classification_prompt) // 4

                if DEBUG_MODE:
                    print(f"   ✅ Model response: {response_data.get('pipe_category', 'Unknown')}")
                    print(f"   ✅ Overall confidence: {response_data.get('overall_confidence', 0.0)}")

            else:
                # Unexpected response format - raise error
                error_msg = f"❌ Unexpected model response format: {type(model_response)}"
                if DEBUG_MODE:
                    print(f"   {error_msg}")
                raise RuntimeError(error_msg)

        except Exception as model_error:
            error_msg = f"❌ Model call failed: {model_error}"
            if DEBUG_MODE:
                print(f"   {error_msg}")
                import traceback
                traceback.print_exc()
            raise RuntimeError(error_msg)
        
        # Merge with existing field classifications from Stage 1
        existing_classifications = state.get("field_classifications", {})
        merged_classifications = existing_classifications.copy()
        merged_classifications.update(response_data)  # Stage 2 results override Stage 1

        # Merge confidence scores
        existing_confidence = state.get("confidence_scores", {})
        merged_confidence = existing_confidence.copy()
        merged_confidence.update(response_data.get("field_confidence_scores", {}))

        if DEBUG_MODE:
            print(f"   ✅ Merged classifications: {len(merged_classifications)} total fields")

        # Update state with classification results
        return update_state_metadata(
            {
                **state,
                "current_stage": "qa_decisions",
                "field_classifications": merged_classifications,
                "confidence_scores": merged_confidence
            },
            stage_name="pipe_classification",
            processing_time=time.time() - start_time,
            model_calls=model_calls,
            tokens_used=tokens_used,
            stage_output=response_data
        )
        
    except Exception as e:
        if DEBUG_MODE:
            print(f"   ❌ Error in pipe classification: {str(e)}")
        
        return update_state_metadata(
            {
                **state,
                "current_stage": "qa_decisions",
                "identified_issues": state.get("identified_issues", []) + [
                    {
                        "stage": "pipe_classification",
                        "error": str(e)
                    }
                ]
            },
            stage_name="pipe_classification_error",
            processing_time=time.time() - start_time
        )


async def qa_decision_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 3: Q&A Decision Making Node
    
    Handles ambiguous classifications and applies business rules
    for edge cases and low-confidence decisions.
    """
    
    if not ENABLE_QA_DECISIONS:
        return {
            **state,
            "current_stage": "self_audit",
            "workflow_path": state["workflow_path"] + ["qa_decisions_skipped"]
        }
    
    start_time = time.time()
    
    try:
        material_description = state["material_description"]
        field_classifications = state.get("field_classifications", {})
        confidence_scores = state.get("confidence_scores", {})
        
        if DEBUG_MODE:
            print(f"\n🤔 STAGE 3: Q&A Decisions")
            print(f"   Low confidence fields: {[f for f, c in confidence_scores.items() if c is not None and c < 0.8]}")

        # Check if Q&A is needed - handle None values properly
        needs_qa = any(confidence is not None and confidence < 0.8 for confidence in confidence_scores.values())
        
        if not needs_qa:
            if DEBUG_MODE:
                print(f"   ✓ All fields have high confidence - skipping Q&A")
            
            return update_state_metadata(
                {
                    **state,
                    "current_stage": "self_audit"
                },
                stage_name="qa_decisions_skipped",
                processing_time=time.time() - start_time
            )
        
        # Build Q&A prompt
        qa_prompt = build_qa_decision_prompt(
            material_description,
            field_classifications,
            confidence_scores
        )
        
        # Get model handler and configuration from state or create default
        model_config = state.get("model_config")
        model_handler = get_or_create_model_handler(model_config)

        # Call actual model for Q&A decisions
        try:
            if DEBUG_MODE:
                print(f"   🤖 Calling model for Q&A decisions...")

            # Call model for Stage 3 - Q&A Decisions
            model_response = await model_handler.call_model_for_stage(
                stage_name="qa_decisions",
                prompt=qa_prompt,
                response_schema=dict,  # Use dict for now, can be enhanced with Pydantic model
                item_id=state.get("item_id", "unknown")
            )

            # Extract response data
            if isinstance(model_response, dict):
                response_data = model_response
                model_calls = 1
                tokens_used = len(qa_prompt) // 4

                if DEBUG_MODE:
                    print(f"   ✅ Q&A complete: {len(response_data.get('decisions_made', []))} decisions made")

            else:
                # Unexpected response format - raise error
                error_msg = f"❌ Unexpected model response format: {type(model_response)}"
                if DEBUG_MODE:
                    print(f"   {error_msg}")
                raise RuntimeError(error_msg)

        except Exception as model_error:
            error_msg = f"❌ Model call failed: {model_error}"
            if DEBUG_MODE:
                print(f"   {error_msg}")
                import traceback
                traceback.print_exc()
            raise RuntimeError(error_msg)
        
        # Apply corrections from Q&A decisions
        updated_classifications = field_classifications.copy()
        updated_confidence = confidence_scores.copy()

        for field, correction in response_data.get("field_corrections", {}).items():
            updated_classifications[field] = correction

        for field, new_confidence in response_data.get("confidence_adjustments", {}).items():
            updated_confidence[field] = new_confidence

        return update_state_metadata(
            {
                **state,
                "current_stage": "self_audit",
                "field_classifications": updated_classifications,
                "confidence_scores": updated_confidence
            },
            stage_name="qa_decisions",
            processing_time=time.time() - start_time,
            model_calls=model_calls,
            tokens_used=tokens_used,
            stage_output=response_data
        )
        
    except Exception as e:
        if DEBUG_MODE:
            print(f"   ❌ Error in Q&A decisions: {str(e)}")
        
        return update_state_metadata(
            {
                **state,
                "current_stage": "self_audit",
                "identified_issues": state.get("identified_issues", []) + [
                    {
                        "stage": "qa_decisions",
                        "error": str(e)
                    }
                ]
            },
            stage_name="qa_decisions_error",
            processing_time=time.time() - start_time
        )


async def self_audit_node(state: ClassificationState) -> ClassificationState:
    """
    Stage 4: Self-Audit Validation Node
    
    Performs final validation against business rules and
    cross-field consistency checks.
    """
    
    if not ENABLE_SELF_AUDIT:
        return {
            **state,
            "current_stage": "complete",
            "workflow_path": state["workflow_path"] + ["self_audit_skipped"]
        }
    
    start_time = time.time()
    
    try:
        material_description = state["material_description"]
        field_classifications = state.get("field_classifications", {})
        confidence_scores = state.get("confidence_scores", {})
        
        if DEBUG_MODE:
            print(f"\n🔍 STAGE 4: Self-Audit")
            print(f"   Validating {len(field_classifications)} classified fields")
        
        # Build audit prompt
        audit_prompt = build_self_audit_prompt(
            material_description,
            field_classifications,
            confidence_scores
        )
        
        # Get model handler and configuration from state or create default
        model_config = state.get("model_config")
        model_handler = get_or_create_model_handler(model_config)

        # Call actual model for self-audit
        try:
            if DEBUG_MODE:
                print(f"   🤖 Calling model for self-audit...")

            # Call model for Stage 4 - Self-Audit
            model_response = await model_handler.call_model_for_stage(
                stage_name="self_audit",
                prompt=audit_prompt,
                response_schema=dict,  # Use dict for now, can be enhanced with Pydantic model
                item_id=state.get("item_id", "unknown")
            )

            # Extract response data
            if isinstance(model_response, dict):
                response_data = model_response
                model_calls = 1
                tokens_used = len(audit_prompt) // 4

                if DEBUG_MODE:
                    print(f"   ✅ Audit complete: {response_data.get('validation_status', 'unknown')}")
                    print(f"   ✅ Issues found: {len(response_data.get('cross_field_violations', []))}")

            else:
                # Unexpected response format - raise error
                error_msg = f"❌ Unexpected model response format: {type(model_response)}"
                if DEBUG_MODE:
                    print(f"   {error_msg}")
                raise RuntimeError(error_msg)

        except Exception as model_error:
            error_msg = f"❌ Model call failed: {model_error}"
            if DEBUG_MODE:
                print(f"   {error_msg}")
                import traceback
                traceback.print_exc()
            raise RuntimeError(error_msg)
        
        # Apply any recommended corrections
        final_classifications = field_classifications.copy()
        for field, correction in response_data.get("recommended_corrections", {}).items():
            final_classifications[field] = correction

        # Identify issues for correction workflow
        identified_issues = []
        for violation in response_data.get("cross_field_violations", []):
            identified_issues.append({
                "field": violation.get("affected_fields", ["unknown"])[0],
                "current_value": field_classifications.get(violation.get("affected_fields", ["unknown"])[0]),
                "suggested_value": response_data.get("recommended_corrections", {}).get(
                    violation.get("affected_fields", ["unknown"])[0]
                ),
                "confidence": 0.9,
                "explanation": violation.get("violation", "Cross-field validation issue")
            })

        return update_state_metadata(
            {
                **state,
                "current_stage": "complete",
                "field_classifications": final_classifications,
                "validation_results": response_data,
                "identified_issues": identified_issues
            },
            stage_name="self_audit",
            processing_time=time.time() - start_time,
            model_calls=model_calls,
            tokens_used=tokens_used,
            stage_output=response_data
        )
        
    except Exception as e:
        if DEBUG_MODE:
            print(f"   ❌ Error in self-audit: {str(e)}")
        
        return update_state_metadata(
            {
                **state,
                "current_stage": "complete",
                "identified_issues": state.get("identified_issues", []) + [
                    {
                        "stage": "self_audit",
                        "error": str(e)
                    }
                ]
            },
            stage_name="self_audit_error",
            processing_time=time.time() - start_time
        )


# All simulation functions removed - using real model calls only
