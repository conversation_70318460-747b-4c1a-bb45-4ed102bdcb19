"""
Define table scopes here.

Example:

    roi_table_scopes["BOM"] = [
                        "Field 1",
                        "Field 2",
                        "Field 3",
                        "Field 4",
                        "Field 5",
                    ]

Use actual names and not aliases
"""


class ReadOnlyDict(dict):

    __readonly = False

    def readonly(self, readonly: bool = False):
        """Allow or deny modifying dictionary"""
        self.__readonly = readonly

    def __setitem__(self, key, value):
        if self.__readonly:
            raise TypeError("__setitem__ is not supported")
        return dict.__setitem__(self, key, value)

    def __delitem__(self, key):
        if self.__readonly:
            raise TypeError("__delitem__ is not supported")
        return dict.__delitem__(self, key)


scopes: ReadOnlyDict = ReadOnlyDict()
scopes.readonly(False)

""" Current Table Fields
'Isometric Drawing Area',
'SPEC',
'Spool',
'BOM',
'area',
'clientDocumentId',
'designCode',
'documentDescription',
'documentId',
'documentTitle',
'drawing',
'elevation',
'heatTrace',
'insulationSpec',
'insulationThickness',
'item',
'lineNumber',
'material_description',
'mediumCode',
'paintSpec',
'pdf_id',
'pid',
'pipeSpec',
'pipeStandard',
'pos',
'processLineList',
'processUnit',
'projectName',
'projectNo',
'pwht',
'quantity',
'revision',
'sequence',
'service',
'sheet',
'size',
'system',
'tag',
'totalSheets',
'unit',
'vendorDocumentId',
'xray'
"""

#
# Insert scopes below
# The key value must be the ID format not the display format
# e.g. lineNumber and not `Line Number`
#
# Adding BOM table fields
scopes["BOM"] = [
    "pos",
    "material_description",
    "size",
    "ident",
    "item",
    "tag",
    "quantity",
    "status",
    "material_code",
    "sch_class",
    "parts",
    "type",
    "weight",
    "number",
    "remarks",
    "ident_code",
    "item_count",        # ADD THIS
    "item_length",       # ADD THIS  
    "total_length",      # ADD THIS
    "shape"              # ADD THIS
]

# Adding SPEC table fields
scopes["SPEC"] = [
    "dp1",
    "dp2",
    "dt1",
    "dt2",
    "insulationSpec",
    "insulationThickness",
    "heatTrace",
    "op1",
    "op2",
    "opt1",
    "opt2",
    "paintSpec",
    "pipeSpec",
    "size",
    "nb",
    "fluid",
    "clean_spec",
    "lineNumber"
]

# Adding SPOOL table fields
scopes["Spool"] = [
    "cutPiece",
    "length",
    "ident",
    "spool",
    "spec",
    "size"
]
# Adding IFC table fields
scopes["IFC"] = [
    "issue_no",
    "issue_date",
    "issue_description",
    "approved_by",
    "drawn_by",
    "checked_by"
]

# Adding IFC table fields
scopes["generic_1"] = [
    "field_1",
    "field_2",
    "field_3",
    "field_4",
    "field_5",
    "field_6",
    "field_7",
    "field_8",
    "field_9",
    "field_10",
    "field_11",
    "field_12",
    "field_13",
    "field_14",
    "field_15",
    "field_16",
    "field_17",
    "field_18",
    "field_19",
    "field_20",
    "field_21",
    "field_22",
    "field_23",
    "field_24",
    "field_25",
    "field_26",
]

scopes["generic_2"] = [
    "field_1",
    "field_2",
    "field_3",
    "field_4",
    "field_5",
    "field_6",
    "field_7",
    "field_8",
    "field_9",
    "field_10",
    "field_11",
    "field_12",
    "field_13",
    "field_14",
    "field_15",
    "field_16",
    "field_17",
    "field_18",
    "field_19",
    "field_20",
    "field_21",
    "field_22",
    "field_23",
    "field_24",
    "field_25",
    "field_26",
]


#
# Make read only
scopes.readonly(readonly=True)


def isRoiTable(key: str):
    return key in scopes