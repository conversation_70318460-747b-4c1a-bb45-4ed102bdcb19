FUNC_TRIGGER_RFQ_REFRESH = """  -- Refreshes timestamps in the RFQ input table to trigger updates for specified project or all projects
   CREATE OR REPLACE FUNCTION refresh_rfq_input_data(p_project_id INTEGER DEFAULT NULL)  -- p_project_id: The ID of the project to refresh, or NULL to refresh all projects
    RETURNS INTEGER AS $$
    DECLARE
        v_count INTEGER := 0;  -- v_count: The number of rows updated
    BEGIN
        -- If project_id is provided, only refresh that project's data
        IF p_project_id IS NOT NULL THEN
            UPDATE public.atem_rfq_input
            SET updated_at = CURRENT_TIMESTAMP -- Update timestamp to trigger
            WHERE project_id = p_project_id;
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
            RAISE NOTICE 'Refreshed % rows for project ID %', v_count, p_project_id;
        ELSE
            -- Otherwise refresh all data
            UPDATE public.atem_rfq_input
            SET updated_at = CURRENT_TIMESTAMP; -- Update timestamp to trigger
            
            GET DIAGNOSTICS v_count = ROW_COUNT;
            RAISE NOTICE 'Refreshed % rows across all projects', v_count;
        END IF;
        
        RETURN v_count;
    END;
    $$ LANGUAGE plpgsql;

    /*-- Refresh all projects
    SELECT refresh_rfq_input_data();

    -- Refresh only project with ID 5
    SELECT refresh_rfq_input_data(5); */ 
"""

FUNC_UPDATE_RFQ_PROFILE_ID = """  -- Keeps profile_id in sync with project_id in the RFQ table through a trigger
    DROP TRIGGER IF EXISTS trg_update_rfq_profile_id ON public.atem_rfq;

    /* Create function to keep the profile_id in sync with project_id */
    CREATE OR REPLACE FUNCTION update_rfq_profile_id()
    RETURNS TRIGGER AS $$
    DECLARE
        v_profile_id INTEGER;  -- v_profile_id: The profile ID to update
    BEGIN
        IF NEW.project_id IS NOT NULL THEN
            -- Get the profile_id from the project
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = NEW.project_id
            LIMIT 1;
            
            -- Update the profile_id
            NEW.profile_id := v_profile_id;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Apply the trigger to atem_rfq
    CREATE TRIGGER trg_update_rfq_profile_id
    BEFORE INSERT OR UPDATE OF project_id ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION update_rfq_profile_id();

    /* Update existing records */
    /* UPDATE public.atem_rfq r
    SET profile_id = p.profile_id
    FROM public.atem_projects p
    WHERE r.project_id = p.id; */
"""

# ---------------------RFQ FUNCTIONS -------------------------

FUNC_CALCULATE_AREA = """  -- Calculates pipe surface area based on diameter and length with support for different units
    CREATE OR REPLACE FUNCTION calculate_pipe_surface_area(
        diameter NUMERIC,  -- diameter: The diameter of the pipe
        length NUMERIC,  -- length: The length of the pipe
        diameter_unit VARCHAR DEFAULT 'inches',  -- diameter_unit: The unit of the diameter (e.g., inches, feet, mm, m)
        length_unit VARCHAR DEFAULT 'feet',  -- length_unit: The unit of the length (e.g., inches, feet, mm, m)
        OUT area_sq_inches NUMERIC,  -- area_sq_inches: The calculated surface area in square inches
        OUT area_sq_feet NUMERIC,  -- area_sq_feet: The calculated surface area in square feet
        OUT area_sq_mm NUMERIC,  -- area_sq_mm: The calculated surface area in square millimeters
        OUT area_sq_m NUMERIC  -- area_sq_m: The calculated surface area in square meters
    )
    AS $$
    DECLARE
        diameter_in_inches NUMERIC;  -- diameter_in_inches: The diameter in inches
        length_in_inches NUMERIC;  -- length_in_inches: The length in inches
        diameter_in_mm NUMERIC;  -- diameter_in_mm: The diameter in millimeters
        length_in_mm NUMERIC;  -- length_in_mm: The length in millimeters
    BEGIN
        -- Convert diameter to standard units (inches and mm)
        CASE LOWER(diameter_unit)
            WHEN 'inches' THEN 
                diameter_in_inches := diameter;
                diameter_in_mm := diameter * 25.4;
            WHEN 'feet' THEN 
                diameter_in_inches := diameter * 12;
                diameter_in_mm := diameter_in_inches * 25.4;
            WHEN 'mm' THEN 
                diameter_in_mm := diameter;
                diameter_in_inches := diameter / 25.4;
            WHEN 'm' THEN 
                diameter_in_mm := diameter * 1000;
                diameter_in_inches := diameter_in_mm / 25.4;
            ELSE
                RAISE EXCEPTION 'Unsupported diameter unit: %', diameter_unit;
        END CASE;
        
        -- Convert length to standard units (inches and mm)
        CASE LOWER(length_unit)
            WHEN 'inches' THEN 
                length_in_inches := length;
                length_in_mm := length * 25.4;
            WHEN 'feet' THEN 
                length_in_inches := length * 12;
                length_in_mm := length_in_inches * 25.4;
            WHEN 'mm' THEN 
                length_in_mm := length;
                length_in_inches := length / 25.4;
            WHEN 'm' THEN 
                length_in_mm := length * 1000;
                length_in_inches := length_in_mm / 25.4;
            ELSE
                RAISE EXCEPTION 'Unsupported length unit: %', length_unit;
        END CASE;
        
        -- Calculate lateral surface area in imperial units
        -- Formula: SA = 2π * (d/2) * l
        area_sq_inches := 2 * PI() * (diameter_in_inches / 2) * length_in_inches;
        area_sq_feet := area_sq_inches / 144; -- Convert square inches to square feet
        
        -- Calculate lateral surface area in metric units
        area_sq_mm := 2 * PI() * (diameter_in_mm / 2) * length_in_mm;
        area_sq_m := area_sq_mm / 1000000; -- Convert square mm to square meters
        
        -- Round to 2 decimal places for readability
        area_sq_inches := ROUND(area_sq_inches, 2);
        area_sq_feet := ROUND(area_sq_feet, 2);
        area_sq_mm := ROUND(area_sq_mm, 2);
        area_sq_m := ROUND(area_sq_m, 2);
    END;
    $$ LANGUAGE plpgsql;

    -- Example usage:
    -- Imperial: SELECT * FROM calculate_pipe_surface_area(8, 2, 'inches', 'feet');
    -- Metric: SELECT * FROM calculate_pipe_surface_area(200, 1, 'mm', 'm');
"""

FUNC_UPDATE_RFQ_CALCULATIONS = """  -- Performs complex calculations for equivalent lengths and surface areas in RFQ items using lookup or factor methods
    /* 
    * IMPROVED RFQ CALCULATION FUNCTION
    * 
    * This function implements a robust calculation methodology for equivalent lengths
    * and surface areas in RFQ items. The implementation addresses several complex
    * requirements and edge cases:
    *
    * 1. CATEGORY LOOKUP LOGIC:
    *    - Uses fitting_category for items with RFQ scope of "Fittings"
    *    - Uses general_category for all other items
    *    - Supports lookup of many types in general_category (Supports, Cut Outs, etc.)
    *    - Includes safety checks to prevent errors with NULL values
    *
    * 2. MULTI-STEP SIZE MATCHING ALGORITHM:
    *    - Step 1: Tries for exact match first, including compound sizes in any order
    *    - Step 2: For compound fittings (with size1 and size2), falls back to larger size
    *    - Step 3: Uses sophisticated size approximation with preference for rounding up
    *
    * 3. COMPOUND SIZE HANDLING:
    *    - Handles size reversals (treats size1=4, size2=2 the same as size1=2, size2=4)
    *    - For unmatched compound items, uses the larger of the two sizes for single-size lookup
    *    - Maintains correct matching regardless of size order in lookup table
    *
    * 4. EDGE CASE HANDLING:
    *    - Prioritizes rounding up when exact size not found
    *    - When search size is between available sizes, rounds up to next size
    *    - When search size is larger than all available sizes, uses largest available
    *    - When search size is smaller than all available sizes, uses smallest available
    *    - Returns zero for completely unmatched items
    *
    * 5. FACTOR METHOD IMPROVEMENTS:
    *    - Tries lookup with general_category first
    *    - Falls back to fitting_category if needed
    *    - Only sets calculated_area to NULL for factor method (area not calculated this way)
    *
    * The function works with two primary calculation methods:
    *   a) 'lookup': Uses predefined values from vw_fittings_lookup view
    *   b) 'factor': Uses multiplier factors from atem_equiv_length_factors table
    *
    * The database trigger watches for changes to key columns and recalculates values
    * automatically, ensuring data consistency throughout the application.
    */

    CREATE OR REPLACE FUNCTION update_rfq_calculations()
    RETURNS TRIGGER AS $$
    DECLARE
        eq_length DECIMAL(20,12);  -- eq_length: The equivalent length
        area DECIMAL(20,12);  -- area: The calculated area
        eq_length_factor DECIMAL(10,3);  -- eq_length_factor: The factor for equivalent length calculation
        method VARCHAR(50);  -- method: The calculation method (lookup or factor)
        v_profile_name VARCHAR(255);  -- v_profile_name: The profile name
        v_lookup_category VARCHAR(100);  -- v_lookup_category: The category for lookup
        v_closest_size DECIMAL(8,3);  -- v_closest_size: The closest size found
        v_larger_size DECIMAL(8,3);  -- v_larger_size: The larger size for compound fittings
        lookup_found BOOLEAN := FALSE;  -- Flag to track if a lookup was successful
    BEGIN
        -- Set default values in case no match is found
        eq_length := 0;
        area := 0;

        -- Skip if profile_id is null
        IF NEW.profile_id IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- Retrieve calculation method and profile name
        SELECT equivalent_length_method, profile_name INTO method, v_profile_name
        FROM public.atem_client_profiles WHERE id = NEW.profile_id;
        
        -- Skip if no method found, but ensure defaults
        IF method IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- IMPROVED LOOKUP LOGIC
        IF method = 'lookup' THEN
            -- Try fitting_category first for Fittings scope
            IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                v_lookup_category := NEW.fitting_category;
                
                -- Try exact match with fitting_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                -- Check if lookup succeeded
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using fitting_category=%', v_lookup_category;
                END IF;
            END IF;
            
            -- If fitting_category didn't find a match, fall back to general_category
            IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                v_lookup_category := NEW.general_category;
                
                -- Try exact match with general_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using general_category=%', v_lookup_category;
                END IF;
            END IF;
                
            -- If still no exact match, try alternative approaches
            IF NOT lookup_found AND (NEW.fitting_category IS NOT NULL OR NEW.general_category IS NOT NULL) THEN
                -- Try both categories for more complex matching
                IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                    v_lookup_category := NEW.fitting_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        
                        SELECT length_ft, area_ft INTO eq_length, area
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 = v_larger_size
                        AND size2 IS NULL
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found match using fitting_category with larger size=%', v_larger_size;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with fitting_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using fitting_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
                
                -- If fitting_category approaches failed, try general_category approaches
                IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                    v_lookup_category := NEW.general_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        
                        SELECT length_ft, area_ft INTO eq_length, area
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 = v_larger_size
                        AND size2 IS NULL
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found match using general_category with larger size=%', v_larger_size;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with general_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using general_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END IF;

            -- Assign the calculated values (defaults to 0 if no match was found)
            -- MODIFIED: Multiply by quantity to get the total equivalent length and area
            NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * COALESCE(eq_length, 0);
            NEW.calculated_area := COALESCE(NEW.quantity, 0) * COALESCE(area, 0);
            
        ELSIF method = 'factor' THEN
            lookup_found := FALSE;
            
            -- Try with general_category first
            IF NEW.general_category IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.general_category 
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using general_category=%', NEW.general_category;
                END IF;
            END IF;
            
            -- If no factor found, try with fitting_category
            IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                SELECT eq_length_factor INTO eq_length_factor
                FROM public.atem_equiv_length_factors
                WHERE component_type = NEW.fitting_category
                AND profile_id = NEW.profile_id
                LIMIT 1;
                
                IF eq_length_factor IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using fitting_category=%', NEW.fitting_category;
                END IF;
            END IF;
            
            -- Try with other category fields as a last resort
            IF NOT lookup_found THEN
                -- Try with valve_type
                IF NEW.valve_type IS NOT NULL THEN
                    SELECT eq_length_factor INTO eq_length_factor
                    FROM public.atem_equiv_length_factors
                    WHERE component_type = NEW.valve_type
                    AND profile_id = NEW.profile_id
                    LIMIT 1;
                    
                    IF eq_length_factor IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found factor using valve_type=%', NEW.valve_type;
                    END IF;
                END IF;
                
                -- Try with pipe_category
                IF NOT lookup_found AND NEW.pipe_category IS NOT NULL THEN
                    SELECT eq_length_factor INTO eq_length_factor
                    FROM public.atem_equiv_length_factors
                    WHERE component_type = NEW.pipe_category
                    AND profile_id = NEW.profile_id
                    LIMIT 1;
                    
                    IF eq_length_factor IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found factor using pipe_category=%', NEW.pipe_category;
                    END IF;
                END IF;
            END IF;

            -- Calculate with the factor if found, otherwise use 0
            NEW.calculated_eq_length := NEW.quantity * COALESCE(eq_length_factor, 0);
            NEW.calculated_area := NULL; -- Area not calculated with factor method
        END IF;
        
        -- Ensure defaults if no lookup values were found
        IF NEW.calculated_eq_length IS NULL THEN
            NEW.calculated_eq_length := 0;
        END IF;
        
        IF NEW.calculated_area IS NULL AND method != 'factor' THEN
            NEW.calculated_area := 0;
        END IF;

        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create the updated trigger with column-specific conditions
    DROP TRIGGER IF EXISTS trg_update_rfq_calculations ON public.atem_rfq;

    CREATE TRIGGER trg_update_rfq_calculations
    BEFORE INSERT OR UPDATE OF size1, size2, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.atem_rfq
    FOR EACH ROW EXECUTE FUNCTION update_rfq_calculations();
"""

FUNC_UPDATE_RFQ_QUANTITY = """
    -- Function to update RFQ quantities from BOM records
    CREATE OR REPLACE FUNCTION update_rfq_quantities()
    RETURNS TRIGGER AS $$
    DECLARE
        v_new_rfq_id INTEGER := NULL;
        v_old_rfq_id INTEGER := NULL;
        v_new_quantity INTEGER;
        in_progress BOOLEAN;
    BEGIN
        -- Check for recursion
        BEGIN
            -- Try to get the 'bom_quantity_update_in_progress' variable
            SELECT current_setting('bom_quantity_update_in_progress', TRUE)::BOOLEAN INTO in_progress;
        EXCEPTION WHEN OTHERS THEN
            -- If it doesn't exist, set it to FALSE
            in_progress := FALSE;
        END;
        
        -- If an update is already in progress, exit to avoid recursion
        IF in_progress THEN
            RAISE NOTICE 'Update already in progress, skipping to avoid recursion';
            IF TG_OP = 'DELETE' THEN
                RETURN OLD;
            ELSE
                RETURN NEW;
            END IF;
        END IF;
        
        -- Set the status to indicate we're updating
        PERFORM set_config('bom_quantity_update_in_progress', 'true', TRUE);
        
        -- Begin transaction-style error handling
        BEGIN
            -- Determine which RFQ IDs are affected
            IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
                v_new_rfq_id := NEW.rfq_ref_id;
            END IF;
            
            IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
                v_old_rfq_id := OLD.rfq_ref_id;
            END IF;
            
            -- Update new RFQ ID if it exists
            IF v_new_rfq_id IS NOT NULL THEN
                SELECT COALESCE(SUM(quantity), 0) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_new_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_new_rfq_id, v_new_quantity;
                
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP  -- Update timestamp
                WHERE id = v_new_rfq_id;
            END IF;
            
            -- Update old RFQ ID if it exists and is different from new
            IF v_old_rfq_id IS NOT NULL AND v_old_rfq_id IS DISTINCT FROM v_new_rfq_id THEN
                SELECT COALESCE(SUM(quantity), 0) INTO v_new_quantity
                FROM public.bom
                WHERE rfq_ref_id = v_old_rfq_id;
                
                RAISE NOTICE 'Updating RFQ ID % with new quantity %', v_old_rfq_id, v_new_quantity;
                
                UPDATE public.atem_rfq
                SET quantity = v_new_quantity,
                    updated_at = CURRENT_TIMESTAMP  -- Update timestamp
                WHERE id = v_old_rfq_id;
            END IF;
            
            -- Reset the status when we're done
            PERFORM set_config('bom_quantity_update_in_progress', 'false', TRUE);
            
            EXCEPTION WHEN OTHERS THEN
                -- If an error occurs, make sure we reset the status
                PERFORM set_config('bom_quantity_update_in_progress', 'false', TRUE);
                RAISE; -- Re-throw the exception
        END;
        
        -- Return the appropriate record based on operation type
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            RETURN NEW;
        END IF;
    END;
    $$ LANGUAGE plpgsql;

    -- Create the trigger on the BOM table
    CREATE TRIGGER trg_update_rfq_quantities
    AFTER INSERT OR UPDATE OF quantity, rfq_ref_id OR DELETE
    ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION update_rfq_quantities();
"""

FUNC_UPDATE_CLIENT_NAME = """  -- Populates client_name automatically based on ref_id through a trigger
    -- Create a function that will populate client_name based on ref_id
    CREATE OR REPLACE FUNCTION update_client_name()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Only proceed if ref_id is not null
        IF NEW.ref_id IS NOT NULL THEN
            -- Look up client_name from atem_clients table
            SELECT client_name INTO NEW.client_name
            FROM public.atem_clients
            WHERE id = NEW.ref_id;
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create a trigger that fires before INSERT or UPDATE
    CREATE TRIGGER tr_update_client_name
    BEFORE INSERT OR UPDATE OF ref_id ON public.atem_client_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_client_name();
"""

FUNC_SYNC_RFQ_TO_INPUT = """  -- Syncs data from RFQ to RFQ_INPUT table, creating new input records as needed
    DROP TRIGGER IF EXISTS trg_sync_rfq_to_input ON public.atem_rfq;

    -- 2. Create a recursive-safe function for RFQ→RFQ_INPUT direction
    CREATE OR REPLACE FUNCTION sync_rfq_to_input()
    RETURNS TRIGGER AS $$
    DECLARE
        input_id INTEGER;  -- input_id: The ID of the RFQ_INPUT record
    BEGIN
        -- Skip if this is an update being triggered by rfq_input sync
        IF (TG_OP = 'UPDATE' AND NEW.rfq_input_id IS NOT NULL) THEN
            RETURN NEW;
        END IF;
        
        -- Check if a matching RFQ_INPUT record already exists
        SELECT id INTO input_id
        FROM public.atem_rfq_input
        WHERE project_id = NEW.project_id
        AND material_description = NEW.material_description;
        
        -- If no matching record exists, create one
        IF input_id IS NULL THEN
            INSERT INTO public.atem_rfq_input (
                project_id, material_description, normalized_description,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                NEW.project_id, NEW.material_description, NEW.normalized_description,
                NEW.rfq_scope, NEW.general_category, NEW.unit_of_measure,
                NEW.material, NEW.abbreviated_material, NEW.technical_standard,
                NEW.astm, NEW.grade, NEW.rating, NEW.schedule, NEW.coating,
                NEW.forging, NEW.ends, NEW.item_tag, NEW.tie_point,
                NEW.pipe_category, NEW.valve_type, NEW.fitting_category,
                NEW.weld_category, NEW.bolt_category, NEW.gasket_category,
                NEW.notes, NEW.deleted, NEW.ignore_item,
                NEW.created_by, NEW.updated_by
            )
            RETURNING id INTO input_id;
        END IF;
        
        -- Link the RFQ record to its RFQ_INPUT record
        NEW.rfq_input_id = input_id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER trg_sync_rfq_to_input
    BEFORE INSERT OR UPDATE ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION sync_rfq_to_input();
"""

FUNC_SYNC_INPUT_TO_RFQ = """  -- Updates RFQ records when corresponding RFQ_INPUT records change, maintaining data consistency
    DROP TRIGGER IF EXISTS trg_sync_input_to_rfq ON public.atem_rfq_input;

    -- Function to sync RFQ_INPUT→RFQ direction
    CREATE OR REPLACE FUNCTION sync_input_to_rfq()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Only sync basic attributes, not size or quantity
        UPDATE public.atem_rfq
        SET
            material_description = NEW.material_description, -- Will update the material description. Need to handle cases where material description creates unique constraint conflicts
            normalized_description = NEW.normalized_description,
            rfq_scope = NEW.rfq_scope,
            general_category = NEW.general_category,
            unit_of_measure = NEW.unit_of_measure,
            material = NEW.material,
            abbreviated_material = NEW.abbreviated_material,
            technical_standard = NEW.technical_standard,
            astm = NEW.astm,
            grade = NEW.grade,
            rating = NEW.rating,
            schedule = NEW.schedule,
            coating = NEW.coating,
            forging = NEW.forging,
            ends = NEW.ends,
            item_tag = NEW.item_tag,
            tie_point = NEW.tie_point,
            pipe_category = NEW.pipe_category,
            valve_type = NEW.valve_type,
            fitting_category = NEW.fitting_category,
            weld_category = NEW.weld_category,
            bolt_category = NEW.bolt_category,
            gasket_category = NEW.gasket_category,
            notes = NEW.notes,
            deleted = NEW.deleted,
            ignore_item = NEW.ignore_item
        WHERE rfq_input_id = NEW.id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER trg_sync_input_to_rfq
    AFTER UPDATE ON public.atem_rfq_input
    FOR EACH ROW
    EXECUTE FUNCTION sync_input_to_rfq();

"""

FUNC_UPDATE_RFQ_CATEGORIES_FROM_MAPPING = """  -- Updates RFQ and RFQ_INPUT categories based on component mapping references
    -- Update the function to handle special categories with defaults
    CREATE OR REPLACE FUNCTION update_categories_from_mapping()
    RETURNS TRIGGER AS $$
    DECLARE
        component_val VARCHAR(100);
        v_profile_id INTEGER;
        v_rfq_scope VARCHAR(100);
        v_general_category VARCHAR(100);
        changed_column VARCHAR(50);
        mapping_found BOOLEAN := FALSE;
    BEGIN
        -- Debug entry point
        RAISE NOTICE 'TRIGGER FIRED: Table=%, Op=%, Time=%', 
            TG_TABLE_NAME, TG_OP, now();
        
        -- Determine which column has changed and store its value
        IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND 
            (NEW.pipe_category IS DISTINCT FROM OLD.pipe_category OR 
            NEW.fitting_category IS DISTINCT FROM OLD.fitting_category OR
            NEW.gasket_category IS DISTINCT FROM OLD.gasket_category OR
            NEW.bolt_category IS DISTINCT FROM OLD.bolt_category OR
            NEW.valve_type IS DISTINCT FROM OLD.valve_type)) THEN
            
            RAISE NOTICE 'Changes detected in component fields';
            
            -- Reset mapping_not_found flag on any component change
            NEW.mapping_not_found := FALSE;
            
            -- Determine which component field changed and get its value
            IF NEW.pipe_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.pipe_category IS DISTINCT FROM OLD.pipe_category) THEN
                component_val := NEW.pipe_category;
                changed_column := 'pipe_category';
            ELSIF NEW.fitting_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.fitting_category IS DISTINCT FROM OLD.fitting_category) THEN
                component_val := NEW.fitting_category;
                changed_column := 'fitting_category';
            ELSIF NEW.valve_type IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.valve_type IS DISTINCT FROM OLD.valve_type) THEN
                component_val := NEW.valve_type;
                changed_column := 'valve_type';
            ELSIF NEW.gasket_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.gasket_category IS DISTINCT FROM OLD.gasket_category) THEN
                component_val := NEW.gasket_category;
                changed_column := 'gasket_category';
            ELSIF NEW.bolt_category IS NOT NULL AND (TG_OP = 'INSERT' OR NEW.bolt_category IS DISTINCT FROM OLD.bolt_category) THEN
                component_val := NEW.bolt_category;
                changed_column := 'bolt_category';
            ELSE
                -- No relevant change or no value to map
                RAISE NOTICE 'No relevant changes detected in component fields';
                RETURN NEW;
            END IF;
            
            RAISE NOTICE 'Changed column: %, New value: %', changed_column, component_val;
            
            -- For atem_rfq_input, get profile_id from projects table
            IF TG_TABLE_NAME = 'atem_rfq_input' THEN
                SELECT profile_id INTO v_profile_id
                FROM public.atem_projects
                WHERE id = NEW.project_id;
                
                RAISE NOTICE 'Looking up profile_id for project_id=%: Found profile_id=%', 
                    NEW.project_id, v_profile_id;
            ELSE
                v_profile_id := NEW.profile_id;
                RAISE NOTICE 'Using direct profile_id=%', v_profile_id;
            END IF;
            
            -- Look up rfq_scope and general_category from component mapping
            IF v_profile_id IS NOT NULL AND component_val IS NOT NULL THEN
                RAISE NOTICE 'Looking up mapping for profile_id=% and component_name=%', 
                    v_profile_id, component_val;
                    
                SELECT takeoff_category, general_category INTO v_rfq_scope, v_general_category
                FROM public.atem_bom_component_mapping
                WHERE profile_id = v_profile_id AND component_name = component_val
                LIMIT 1;
                
                RAISE NOTICE 'Mapping lookup result: rfq_scope=%, general_category=%', 
                    v_rfq_scope, v_general_category;
                
                -- Update the categories if mapping was found
                IF v_rfq_scope IS NOT NULL OR v_general_category IS NOT NULL THEN
                    RAISE NOTICE 'Mapping found! Updating with mapped values';
                    
                    -- Only update values that were found in the mapping
                    IF v_rfq_scope IS NOT NULL THEN
                        NEW.rfq_scope := v_rfq_scope;
                    END IF;
                    
                    IF v_general_category IS NOT NULL THEN
                        NEW.general_category := v_general_category;
                    END IF;
                    
                    mapping_found := TRUE;
                ELSE
                    -- No mapping found, set flag
                    NEW.mapping_not_found := TRUE;
                    RAISE NOTICE 'No mapping found in lookup table. Using defaults for %', changed_column;
                    
                    -- Apply default values based on the column type
                    CASE
                        WHEN changed_column = 'valve_type' THEN
                            RAISE NOTICE 'Applying valve_type default: rfq_scope = Valves';
                            NEW.rfq_scope := 'Valves';
                            
                        WHEN changed_column = 'pipe_category' THEN
                            RAISE NOTICE 'Applying pipe_category defaults: rfq_scope = Pipe, general_category = LF';
                            NEW.rfq_scope := 'Pipe';
                            NEW.general_category := 'LF';
                            
                        WHEN changed_column = 'bolt_category' THEN
                            RAISE NOTICE 'Applying bolt_category default: rfq_scope = Bolts';
                            NEW.rfq_scope := 'Bolts';
                            
                        WHEN changed_column = 'gasket_category' THEN
                            RAISE NOTICE 'Applying gasket_category default: rfq_scope = Gaskets';
                            NEW.rfq_scope := 'Gaskets';
                            
                        ELSE
                            RAISE NOTICE 'No default handling for % column', changed_column;
                    END CASE;
                END IF;
            ELSE
                RAISE NOTICE 'Missing required data: profile_id=%, component_val=%', v_profile_id, component_val;
            END IF;
        ELSE
            RAISE NOTICE 'No changes to component fields detected';
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Make sure triggers are properly recreated
    DROP TRIGGER IF EXISTS trg_update_rfq_categories ON public.atem_rfq;
    DROP TRIGGER IF EXISTS trg_update_rfq_input_categories ON public.atem_rfq_input;

    -- Create triggers for both tables
    CREATE TRIGGER trg_update_rfq_categories
    BEFORE INSERT OR UPDATE OF pipe_category, fitting_category, gasket_category, bolt_category, valve_type
    ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION update_categories_from_mapping();

    CREATE TRIGGER trg_update_rfq_input_categories
    BEFORE INSERT OR UPDATE OF pipe_category, fitting_category, gasket_category, bolt_category, valve_type
    ON public.atem_rfq_input
    FOR EACH ROW
    EXECUTE FUNCTION update_categories_from_mapping();
"""


FUNCTION_PROPOGATE_BOM_MAPPING_CHANGES = """  -- Propagates changes in BOM component mapping to RFQ and RFQ_INPUT records
    CREATE OR REPLACE FUNCTION propagate_mapping_changes()
    RETURNS TRIGGER AS $$
    DECLARE
        rec RECORD;  -- rec: A record from the query
        field_name VARCHAR(50);  -- field_name: The name of the field being updated
    BEGIN
        -- Determine which component field to match based on the values
        IF TG_OP = 'UPDATE' THEN
            -- Only proceed if takeoff_category or general_category have changed
            IF (NEW.takeoff_category IS DISTINCT FROM OLD.takeoff_category OR 
                NEW.general_category IS DISTINCT FROM OLD.general_category) THEN
                
                -- Find all RFQ_INPUT records that match this component mapping for pipe_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.pipe_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for fitting_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.fitting_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for gasket_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.gasket_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
                
                -- Find all RFQ_INPUT records that match this component mapping for bolt_category
                FOR rec IN (
                    SELECT ri.id, p.profile_id
                    FROM public.atem_rfq_input ri
                    JOIN public.atem_projects p ON ri.project_id = p.id
                    WHERE p.profile_id = NEW.profile_id
                    AND ri.bolt_category = NEW.component_name
                ) LOOP
                    UPDATE public.atem_rfq_input
                    SET rfq_scope = NEW.takeoff_category,
                        general_category = NEW.general_category
                    WHERE id = rec.id;
                END LOOP;
            END IF;
        END IF;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create trigger on atem_bom_component_mapping
    CREATE TRIGGER trg_propagate_mapping_changes
    AFTER UPDATE ON public.atem_bom_component_mapping
    FOR EACH ROW
    EXECUTE FUNCTION propagate_mapping_changes();
"""

# ---------------------BOM FUNCTIONS -------------------------
FUNCTION_UPDATE_BOM_PROFILE_ID = """
    DROP TRIGGER IF EXISTS trg_update_bom_profile_id ON public.bom;

    /* Create function to keep the profile_id in sync with project_id */
    CREATE OR REPLACE FUNCTION update_bom_profile_id()
    RETURNS TRIGGER AS $$
    DECLARE
        v_profile_id INTEGER;  -- v_profile_id: The profile ID to update
    BEGIN
        IF NEW.project_id IS NOT NULL THEN
            -- Get the profile_id from the project
            SELECT profile_id INTO v_profile_id
            FROM public.atem_projects
            WHERE id = NEW.project_id
            LIMIT 1;
            
            -- Update the profile_id
            NEW.profile_id := v_profile_id;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Apply the trigger to bom
    CREATE TRIGGER trg_update_bom_profile_id
    BEFORE INSERT OR UPDATE OF project_id ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION update_bom_profile_id();

    /* Update existing records */
    UPDATE public.bom b
    SET profile_id = p.profile_id
    FROM public.atem_projects p
    WHERE b.project_id = p.id;
"""
FUNC_SYNC_RFQ_TO_BOM = """
    -- Function for BOM→RFQ direction with recursion protection
    CREATE OR REPLACE FUNCTION sync_bom_to_rfq()
    RETURNS TRIGGER AS $$
    DECLARE
        rfq_id INTEGER;
    BEGIN
        -- RECURSION PROTECTION: Skip if this is an update being triggered by an RFQ sync
        IF (TG_OP = 'UPDATE' AND OLD.rfq_ref_id IS NOT NULL AND OLD.rfq_ref_id = NEW.rfq_ref_id) THEN
            RETURN NEW;
        END IF;
        
        -- Check if a matching RFQ record already exists for this material and size combination
        SELECT id INTO rfq_id
        FROM public.atem_rfq
        WHERE project_id = NEW.project_id
        AND material_description = NEW.material_description
        AND (
            (COALESCE(size1, 0) = COALESCE(NEW.size1, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size2, 0))
            OR (COALESCE(size1, 0) = COALESCE(NEW.size2, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size1, 0))
        )
        LIMIT 1;
        
        -- If no matching record exists, create one
        IF rfq_id IS NULL THEN
            INSERT INTO public.atem_rfq (
                project_id, profile_id, material_description, normalized_description,
                size, size1, size2,
                rfq_scope, general_category, unit_of_measure,
                material, abbreviated_material, technical_standard,
                astm, grade, rating, schedule, coating,
                forging, ends, item_tag, tie_point,
                pipe_category, valve_type, fitting_category,
                weld_category, bolt_category, gasket_category,
                calculated_eq_length, calculated_area,
                notes, deleted, ignore_item,
                created_by, updated_by
            ) VALUES (
                NEW.project_id, NEW.profile_id, NEW.material_description, NEW.normalized_description,
                NEW.size, NEW.size1, NEW.size2,
                NEW.rfq_scope, NEW.general_category, NEW.unit_of_measure,
                NEW.material, NEW.abbreviated_material, NEW.technical_standard,
                NEW.astm, NEW.grade, NEW.rating, NEW.schedule, NEW.coating,
                NEW.forging, NEW.ends, NEW.item_tag, NEW.tie_point,
                NEW.pipe_category, NEW.valve_type, NEW.fitting_category,
                NEW.weld_category, NEW.bolt_category, NEW.gasket_category,
                NEW.calculated_eq_length, NEW.calculated_area,
                NEW.notes, NEW.deleted, NEW.ignore_item,
                NEW.created_by, NEW.updated_by
            )
            RETURNING id INTO rfq_id;
        END IF;
        
        -- Link the BOM record to its RFQ record
        NEW.rfq_ref_id = rfq_id;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Function for RFQ→BOM synchronization (classification updates)
    CREATE OR REPLACE FUNCTION sync_rfq_to_bom()
    RETURNS TRIGGER AS $$
    DECLARE
        in_sync BOOLEAN;
    BEGIN
        -- Check if we're in a sync operation to prevent infinite recursion
        SELECT current_setting('sync_in_progress.rfq_bom', TRUE)::BOOLEAN INTO in_sync;
        IF in_sync THEN
            RETURN NEW;
        END IF;
        
        -- Set flag to indicate we're in a sync operation
        PERFORM set_config('sync_in_progress.rfq_bom', 'true', TRUE);
        
        BEGIN
            -- Update linked BOM records with classification data from RFQ
            UPDATE public.bom
            SET 
                general_category = NEW.general_category,
                rfq_scope = NEW.rfq_scope,
                material = NEW.material,
                rating = NEW.rating,
                ends = NEW.ends,
                fitting_category = NEW.fitting_category,
                valve_type = NEW.valve_type,
                calculated_eq_length = NEW.calculated_eq_length,
                calculated_area = NEW.calculated_area,
                updated_at = CURRENT_TIMESTAMP
            WHERE rfq_ref_id = NEW.id;
            
            -- Reset the flag when done
            PERFORM set_config('sync_in_progress.rfq_bom', 'false', TRUE);
            
            EXCEPTION WHEN OTHERS THEN
                -- Ensure flag is reset even if there's an error
                PERFORM set_config('sync_in_progress.rfq_bom', 'false', TRUE);
                RAISE;
        END;
        
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Drop existing triggers if they exist
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq ON public.bom;
    DROP TRIGGER IF EXISTS trg_sync_rfq_to_bom ON public.atem_rfq;

    -- Create trigger for BOM→RFQ linkage
    CREATE TRIGGER trg_sync_bom_to_rfq
    BEFORE INSERT OR UPDATE ON public.bom
    FOR EACH ROW
    EXECUTE FUNCTION sync_bom_to_rfq();

    -- Create trigger for RFQ→BOM synchronization
    CREATE TRIGGER trg_sync_rfq_to_bom
    AFTER UPDATE OF general_category, rfq_scope, material, rating, ends, 
                    fitting_category, valve_type, calculated_eq_length, calculated_area
    ON public.atem_rfq
    FOR EACH ROW
    EXECUTE FUNCTION sync_rfq_to_bom();
"""