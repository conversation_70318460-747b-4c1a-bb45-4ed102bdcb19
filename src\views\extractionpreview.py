"""
Extraction Preview

Process a page and display data for any existing tables with a subset of columns
"""

import pandas as pd
import fitz

from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from natsort import natsort_keygen

from src.widgets.pandastable import PandasTable
from src.utils.logger import logger
from src.atom.roiextraction import safe_literal_eval, safe_parse_color, process_page_wrapper, get_structured_table


class ExtractionPreviewWorker(QObject):

    # Dictionary of dataframes, List of requested ROIs, Replace data
    finished = Signal(dict, list, bool)
    def __init__(self,
                 doc: fitz.Document,
                 page: fitz.Page,
                 raw_df: pd.DataFrame,
                 rois: dict,
                 fieldMap: dict = {},
                 replace: bool = True,
                 ocr_df: pd.DataFrame = pd.DataFrame()):
        super().__init__()
        self.fieldMap = fieldMap
        self.doc = doc
        self.raw_df = raw_df
        self.rois = rois
        self.page = page
        self.replace = replace
        self.ocr_df = ocr_df

    def run(self):

        if not __file__.endswith(".pyc"):
            import debugpy
            debugpy.debug_this_thread()

        roiNames = set()
        for r in self.rois:
            roiNames.add(r["columnName"])

        use_ocr = not self.ocr_df.empty
        ocr = self.ocr_df

        # ocr["x0"] = ocr["x0"].apply(lambda x: x * self.page.rect.width)
        # ocr["y0"] = ocr["y0"].apply(lambda y: y * self.page.rect.height)
        # ocr["x1"] = ocr["x1"].apply(lambda x: x * self.page.rect.width)
        # ocr["y1"] = ocr["y1"].apply(lambda y: y * self.page.rect.height)

        print(ocr)
        print(self.page.rect)

        self.raw_df["coordinates"] = self.raw_df["coordinates"].apply(safe_literal_eval)
        self.raw_df["coordinates2"] = self.raw_df["coordinates2"].apply(safe_literal_eval)
        self.raw_df["color"] = self.raw_df["color"].apply(safe_parse_color)

        res = process_page_wrapper("preview_path", self.page.number, self.raw_df, pd.DataFrame(), self.rois, use_ocr, ocr, doc=self.doc)
        page_num, raw_data, annot_types_df, annot_tables, text_tables, outlier_df, process_time, skipped, error = res

        generalDf = pd.DataFrame()
        bomDf = pd.DataFrame()
        specDf = pd.DataFrame()
        spoolDf = pd.DataFrame()
        ifcDf = pd.DataFrame()
        generic1Df = pd.DataFrame()
        generic2Df = pd.DataFrame()
        try:
            # Get the TEXT type tables from the structure
            bomDf = get_structured_table('BOM', text_tables)
            specDf = get_structured_table('SPEC', text_tables)
            spoolDf = get_structured_table('Spool', text_tables)
            ifcDf = get_structured_table('IFC', text_tables)
            generic1Df = get_structured_table('generic_1', text_tables)
            generic2Df = get_structured_table('generic_2', text_tables)
        except Exception as e:
            logger.error(f"Could not get the text type table(s): {e}", exc_info=True)

        bomColumns = {
            "pos": "Pos",
            "material_description": "Description",
            "size": "Size",
            "quantity": "Quantity"
        }
        bomColumns = {k: v for k, v in bomColumns.items() if k in bomDf.columns}
        bomDf = bomDf[bomColumns.keys()]
        bomDf = bomDf.rename(columns=bomColumns)

        try:
            bomDf = bomDf.sort_values("Pos", ascending=True, na_position="first", key=natsort_keygen())
        except Exception as e:
            pass

        specColumns = {
            "pipeSpec": "Pipe Spec",
            "size": "Size",
            "dp1": "Design Press.1 (P)",
            "dp2": "Design Press.2 (P)",
            "dt1": "Design Temp.1 (T)",
            "dt2": "Design Temp.2 (T)",
            "op1": "Op. Press.1 (P)",
            "op2": "Op. Press.2 (P)",
            "opt1": "Op. Temp.1 (T)",
            "opt2": "Op. Temp.2 (T)",
        }
        specColumns = {k: v for k, v in specColumns.items() if k in specDf.columns}
        specDf = specDf[specColumns.keys()]
        specDf = specDf.rename(columns=specColumns)

        spoolColumns = {
            "cutPiece": "Spool #",
            "size": "Size",
            "length": "Length",
            "ident": "Identifier",
            "spec": "Pipe Spec",
        }
        spoolColumns = {k: v for k, v in spoolColumns.items() if k in spoolDf.columns}
        spoolDf = spoolDf[spoolColumns.keys()]
        spoolDf = spoolDf.rename(columns=spoolColumns)

        ifcColumns = {
            "issue_no": "Issue No.",
            "issue_date": "Issue Date",
            "issue_description": "Issue Description",
            "approved_by": "Approved By",
            "drawn_by": "Drawn By",
            "checked_by": "Checked By"
        }
        ifcColumns = {k: v for k, v in ifcColumns.items() if k in ifcDf.columns}
        ifcDf = ifcDf[ifcColumns.keys()]
        ifcDf = ifcDf.rename(columns=ifcColumns)

        generic1Columns = {
            "field_1": "Field 1",
            "field_2": "Field 2",
            "field_3": "Field 3",
            "field_4": "Field 4",
            "field_5": "Field 5",
            "field_6": "Field 6",
            "field_7": "Field 7",
            "field_8": "Field 8",
            "field_9": "Field 9",
            "field_10": "Field 10",
            "field_11": "Field 11",
            "field_12": "Field 12",
            "field_13": "Field 13",
            "field_14": "Field 14",
            "field_15": "Field 15",
            "field_16": "Field 16",
            "field_17": "Field 17",
            "field_18": "Field 18",
            "field_19": "Field 19",
            "field_20": "Field 20",
            "field_21": "Field 21",
            "field_22": "Field 22",
            "field_23": "Field 23",
            "field_24": "Field 24",
            "field_25": "Field 25",
            "field_26": "Field 26",
        }
        generic1Columns = {k: v for k, v in generic1Columns.items() if k in generic1Df.columns}
        generic1Df = generic1Df[generic1Columns.keys()]
        generic1Df = generic1Df.rename(columns=generic1Columns)

        generic2Columns = {
            "field_1": "Field 1",
            "field_2": "Field 2",
            "field_3": "Field 3",
            "field_4": "Field 4",
            "field_5": "Field 5",
            "field_6": "Field 6",
            "field_7": "Field 7",
            "field_8": "Field 8",
            "field_9": "Field 9",
            "field_10": "Field 10",
            "field_11": "Field 11",
            "field_12": "Field 12",
            "field_13": "Field 13",
            "field_14": "Field 14",
            "field_15": "Field 15",
            "field_16": "Field 16",
            "field_17": "Field 17",
            "field_18": "Field 18",
            "field_19": "Field 19",
            "field_20": "Field 20",
            "field_21": "Field 21",
            "field_22": "Field 22",
            "field_23": "Field 23",
            "field_24": "Field 24",
            "field_25": "Field 25",
            "field_26": "Field 26",
        }
        generic2Columns = {k: v for k, v in generic2Columns.items() if k in generic2Df.columns}
        generic2Df = generic2Df[generic2Columns.keys()]
        generic2Df = generic2Df.rename(columns=generic2Columns)

        generalDf = pd.DataFrame()
        generalDf = pd.concat([annot_types_df], ignore_index=True)
        try:
            generalDf = generalDf.iloc[0].to_dict()
            generalDf = {k: v for k, v in generalDf.items() if k in roiNames}
            generalDf = [{"fieldName": k,
                        "ROI Name": self.fieldMap.get(k, {}).get("default", k),
                        "Value": v} for k, v in generalDf.items()]
            generalDf = pd.DataFrame(generalDf)
        except:
            pass

        data = {
            "general": generalDf,
            "bom": bomDf,
            "spool": spoolDf,
            "spec": specDf,
            "ifc": ifcDf,
            "generic_1": generic1Df,
            "generic_2": generic2Df,
        }

        self.finished.emit(data, list(roiNames), self.replace)
        self.thread().exit()


class ExtractionPreview(QWidget):

    layoutUpdated = Signal(dict)
    def __init__(self, parent = None):
        super().__init__(parent)
        self.raw_df = pd.DataFrame()

        self.fieldMap = {}
        self.doc: fitz.Document = None
        self.page_num: int = -1
        self.ocr_df: pd.DataFrame = pd.DataFrame()
        self.data = {}
        self.tables = {}
        self.roiNames = set()

        self.thread = QThread()

        self.setLayout(QVBoxLayout())

        self.lblPage = QLabel("")
        # self.layout().addWidget(self.lblPage)

        self.btnGroup = QButtonGroup()

        self.tabBar = QTabBar()
        self.layout().addWidget(self.tabBar)

        self.tabs = QTabWidget(self)
        self.layout().addWidget(self.tabs)

        self.stack = QStackedWidget()
        self.stack.setLayout(QHBoxLayout())
        for _, label in enumerate(["General", "BOM", "SPEC", "SPOOL", "IFC", "Generic_1", "Generic_2"]):
            name = label.lower()
            self.tables[name] = PandasTable(self.tabs)
            self.tables[name].setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.tabs.addTab(self.tables[name], label)

        self.tabs.currentChanged.connect(self.tabChanged)
        self.tabs.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def tabChanged(self, index: int):
        name = self.tabs.tabText(index).lower()
        self.displayData(name)

    def updateData(self,
                   file_path: str = None,
                   page_num: int = None,
                   raw_df: pd.DataFrame = None,
                   ocr_df: pd.DataFrame = pd.DataFrame()):
        if raw_df is not None:
            self.raw_df = raw_df.copy()
        if file_path and not self.doc or file_path and self.doc.name != file_path:
            self.doc = fitz.open(file_path)

        self.page_num = page_num
        self.page = self.doc[page_num - 1]
        self.lblPage.setText(f"Page: {self.page_num}")

        self.ocr_df = ocr_df

    def requestPreview(self, rois, replace=True):
        """
        Args:
            rois: List of rois
            replace: Replace all the data if True. Else update
        """
        self.worker = ExtractionPreviewWorker(self.doc, self.page, self.raw_df,
                                              rois, self.fieldMap, replace, self.ocr_df)
        self.worker.moveToThread(self.thread)
        self.worker.finished.connect(self.extractionFinished)
        self.thread.started.connect(self.worker.run)
        self.thread.start()

    def extractionFinished(self, data: dict, roiNames: list[str], replace: bool):
        roiNamesLower = [r.lower() for r in roiNames]
        for r in roiNames:
            self.roiNames.add(r)
        if replace:
            self.data = data
        else:
            df: pd.DataFrame
            for k, df in data.items():
                if k == "general":
                    # Replace general field values with new ones extracted
                    generalDf = self.data.get("general", pd.DataFrame(columns=["fieldName"]))
                    if "fieldName" in generalDf.columns:
                        generalDf = generalDf[~generalDf["fieldName"].isin(roiNames)]
                    self.data["general"] = pd.concat([generalDf, df], ignore_index=True)
                    self.tables["general"].setDataFrame(self.data["general"])
                elif not df.empty or (df.empty and k in roiNamesLower): # Replace if table data
                    self.data[k] = df
                    self.tables[k].setDataFrame(df)

        try:
            self.data["general"] = self.data["general"].sort_values("ROI Name")
        except:
            pass

        self.updateVisibleTabs(replace)

    def displayData(self, name: str):
        df = self.data.get(name, pd.DataFrame())
        try:
            if name == "general":
                df = df[["ROI Name", "Value"]] # Exclude field name from display
        except:
            pass

        self.tabs.setCurrentWidget(self.tables[name])
        self.tables[name].setDataFrame(df)

    def updateVisibleTabs(self, replace=True):
        self.tabs.setVisible(True)
        # Only show tabs which are relevant to the ROIs requested
        self.tabs.blockSignals(True)
        firstVisible = None
        for n in range(self.tabs.count()):
            name = self.tabs.tabText(n).lower()
            empty = self.data.get(name, pd.DataFrame()).empty
            visible: bool = not empty or name.lower() in [r.lower() for r in self.roiNames]
            if not empty and firstVisible is None:
                firstVisible = n
            self.tabs.tabBar().setTabVisible(n, visible)
            self.tabBar.setTabVisible(n, visible)

        self.tabs.blockSignals(False)

        # Workaround to repaint glitchy tab bar
        self.tabs.addTab(QPushButton("Workaround"), "Temp")
        self.tabs.removeTab(self.tabs.count() - 1)

        index = self.tabs.currentIndex()
        if firstVisible is not None:
            self.tabs.setCurrentIndex(firstVisible)

        try:
            name = self.tabs.tabText(index).lower()
            self.displayData(name)
        except Exception as e:
            pass

        self.update()

    def clear(self):
        self.data = {}
        self.tabs.setHidden(True)
        # self.updateVisibleTabs(replace=True)

    def removeRoi(self, name: str):
        """Remove an ROI from preview"""
        try:
            self.roiNames.remove(name)
        except:
            # Should be no item to remove
            pass

        # Remove ROI table
        try:
            del self.data[name.lower()]
            self.updateVisibleTabs()
            return
        except:
            pass

        # Remove ROI from General table
        try:
            self.data["general"] = self.data["general"][self.data["general"]["fieldName"].isin(self.roiNames)]
            # No more general ROIs, then remove table
            if self.data["general"].empty:
                del self.data["general"]
            self.updateVisibleTabs()
            return
        except:
            pass


