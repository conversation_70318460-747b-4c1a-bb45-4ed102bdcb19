import pandas as pd

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

from src.views.documentviewonly import DocumentViewOnly
from src.pyside_util import get_resource_qicon

from natsort import natsort_keygen


class ActionButtonDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        # Normal mode icons
        self.iconEdit = get_resource_qicon("edit.svg")
        self.iconCopy = get_resource_qicon("copy.svg")
        # Edit mode icons
        self.iconAccept = get_resource_qicon("check-square.svg")
        self.iconCancel = get_resource_qicon("x-square-red.svg")
        self._editors = {}  # Store editors by row
        self._editModeRows = set()  # Track which rows are in edit mode
        self._tableView = parent  # Store reference to table view

        # Connect to model reset signals if parent has a model
        if parent and parent.model():
            parent.model().modelReset.connect(self.cleanup)
            parent.model().layoutChanged.connect(self.cleanup)

    def cleanup(self):
        """Clean up all editors and reset edit states"""
        # Remove all index widgets
        for row, editor in self._editors.items():
            if isinstance(editor, QWidget):
                self._tableView.setIndexWidget(self._tableView.model().index(row, len(self._tableView.model().COLUMNS)-1), None)
                editor.deleteLater()

        # Clear collections
        self._editors.clear()
        self._editModeRows.clear()

    def destroyEditor(self, editor, index):
        """Clean up specific editor"""
        row = index.row()
        if row in self._editors:
            if isinstance(self._editors[row], QWidget):
                self._editors[row].deleteLater()
            del self._editors[row]
        if row in self._editModeRows:
            self._editModeRows.remove(row)
        super().destroyEditor(editor, index)

    def createEditor(self, parent, option, index):
        # Create a widget to hold the buttons
        widget = QWidget(parent)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(4)

        row = index.row()
        isEditing: bool = row in self._editModeRows

        # Create first button (edit/accept)
        pbAction1 = QPushButton(widget)
        pbAction1.setIcon(self.iconAccept if isEditing else self.iconEdit)
        pbAction1.setToolTip("Apply Changes" if isEditing else "Toggle Edit Mode")
        pbAction1.setFixedSize(24, 24)
        pbAction1.clicked.connect(lambda: self.onAction1Clicked(index))
        layout.addWidget(pbAction1)

        # Create second button (copy/cancel)
        pbAction2 = QPushButton(widget)
        pbAction2.setIcon(self.iconCancel if isEditing else self.iconCopy)
        pbAction2.setToolTip("Cancel Edit" if isEditing else "Copy Quantity")
        pbAction2.setFixedSize(24, 24)
        pbAction2.clicked.connect(lambda: self.onAction2Clicked(index))
        layout.addWidget(pbAction2)

        # Center the buttons in the cell
        layout.addStretch()
        layout.insertStretch(0)

        widget.setAutoFillBackground(True)
        return widget

    def paint(self, painter, option, index):
        row = index.row()
        if row not in self._editors:
            editor = self.createEditor(self._tableView, option, index)
            self._tableView.setIndexWidget(index, editor)
            self._editors[row] = editor

    def onAction1Clicked(self, index):
        """Handle edit/accept button click"""
        row = index.row()
        model = index.model()

        if row in self._editModeRows:
            # Accept changes
            self._editModeRows.remove(row)
            # Signal that editing has ended for this row
            model.editingChanged.emit(model.index(row, index.column()), False)
        else:
            # Revert any other actively edited rows
            index.model().currentRowEdit = None
            editRows = list(self._editModeRows)
            self._editModeRows.clear()
            for row2 in editRows:
                # Signal that editing has been canceled with revert=True
                model.editingChanged.emit(model.index(row2, 0), False)
                # Refresh the editor to non-edit mode
                editor = self.createEditor(self._tableView, None, model.index(row2, index.column()))
                self._tableView.setIndexWidget(model.index(row2, index.column()), editor)
                self._editors[row2] = editor

            # Enter edit mode
            self._editModeRows.add(row)
            # Signal that editing has started for this row
            model.editingChanged.emit(model.index(row, 0), True)

        # Refresh the buttons
        if row in self._editors:
            editor = self.createEditor(self._tableView, None, index)
            self._tableView.setIndexWidget(index, editor)
            self._editors[row] = editor

    def showToolTip(self, text, globalPos):
        """Show tooltip at given position"""
        QToolTip.showText(globalPos, text, self._tableView)

    def onAction2Clicked(self, index):
        """Handle copy/cancel button click"""
        row = index.row()
        model = index.model()

        if row in self._editModeRows:
            # Cancel edit mode and revert changes
            self._editModeRows.remove(row)
            index.model().currentRowEdit = None
            # Signal that editing has been canceled with revert=True
            model.editingChanged.emit(model.index(row, 0), False)
            # Refresh the buttons
            if row in self._editors:
                editor = self.createEditor(self._tableView, None, index)
                self._tableView.setIndexWidget(index, editor)
                self._editors[row] = editor
        else:
            # Copy quantity value
            quantityIndex = model.index(row, index.model().quantityColumn)  # Get quantity from same row
            quantity = model.data(quantityIndex, Qt.DisplayRole)
            if quantity:
                clipboard = QApplication.clipboard()
                clipboard.setText(str(quantity))

                # Show tooltip near the button
                if row in self._editors:
                    editor = self._editors[row]
                    buttonPos = editor.children()[1].mapToGlobal(QPoint(0, 0))
                    self.showToolTip(f"Copied: {quantity}", buttonPos)


class TextEditDelegate(QStyledItemDelegate):

    def createEditor(self, parent, option, index):
        editor = super().createEditor(parent, option, index)
        # Set the current value in the editor
        currentValue = index.data(Qt.EditRole)
        if currentValue is not None:
            editor.setText(str(currentValue))
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.EditRole)
        if value is not None:
            editor.setText(str(value))

    def setModelData(self, editor, model, index):
        value = editor.text().strip()  # Remove leading/trailing whitespace
        model.setData(index, value, Qt.EditRole)


class SeverityComboDelegate(QStyledItemDelegate):
    SEVERITY_LEVELS = ["", "Low", "Medium", "High", "Critical"]
    SEVERITY_MAP = {
        "": "0",
        "Low": "1",
        "Medium": "2",
        "High": "3",
        "Critical": "4"
    }
    REVERSE_MAP = {
        "0": "",
        "1": "Low",
        "2": "Medium",
        "3": "High",
        "4": "Critical"
    }

    def createEditor(self, parent, option, index):
        return
        editor = QLineEdit(parent)
        editor.setReadOnly(True)
        return editor
        editor = QComboBox(parent)
        editor.addItems(self.SEVERITY_LEVELS)
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.EditRole)
        editor.setText(str(value))
        return
        try:
            # Convert numeric value to text
            text_value = self.REVERSE_MAP.get(str(value), "")
        except (ValueError, TypeError):
            text_value = str(value) if value is not None else ""
        editor.setCurrentText(text_value)

    def setModelData(self, editor, model, index):
        return
        text_value = editor.currentText()
        # Convert text to numeric value
        numeric_value = self.SEVERITY_MAP.get(text_value, "0")
        model.setData(index, numeric_value, Qt.EditRole)

    def displayText(self, value, locale):
        try:
            # Convert numeric value to text for display
            return self.REVERSE_MAP.get(str(value), "")
        except (ValueError, TypeError):
            return str(value) if value is not None else ""


class AuditTableModel(QAbstractTableModel):
    COLUMNS = [
        "pdf_id",
        "pdf_page",
        "pos",
        "material_description",
        "componentCategory",
        "size",
        "quantity",
        "severity",
        "actions"
    ]
    EDITABLE_COLUMNS = [2, 3, 4, 5, 6, 7]  # material_description, category, size, quantity, severity
    editingChanged = Signal(QModelIndex, bool)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.df: pd.DataFrame = pd.DataFrame(columns=self.COLUMNS[:-1])  # All columns except actions
        self.originalDf: pd.DataFrame = pd.DataFrame(columns=self.COLUMNS[:-1])  # All columns except actions
        self.currentRowEdit = None  # Track the row being edited
        self.editingChanged.connect(self.handleEditingChanged)
        self._dataCache = {}  # Cache for formatted data
        self._originalData = {}  # Store original data when editing
        self._editedData = {}  # Store edited data before committing

        self.quantityColumn: int = self.COLUMNS.index("quantity")
        self.severityColumn: int = self.COLUMNS.index("severity")

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        row, col = index.row(), index.column()

        if role in (Qt.DisplayRole, Qt.EditRole):

            # if col == self.severityColumn:
            #     return REVE.get(int(self._editedData[(row, col)]), self._editedData[(row, col)])
            # Return edited data if available
            if (row, col) in self._editedData:
                return self._editedData[(row, col)]

            # Check cache first
            if col != self.quantityColumn and (row, col) in self._dataCache:
                return self._dataCache[(row, col)]

            # Get data from DataFrame
            if col < len(self.COLUMNS) - 1:  # All columns except actions
                value = self.df.iloc[row][self.COLUMNS[col]]

                # Cache the formatted value
                self._dataCache[(row, col)] = str(value) if pd.notna(value) else ""

                if col == self.quantityColumn: # Show normalized quantity in parentheses
                    diff = self.df.iloc[row][self.COLUMNS[col]] != self.originalDf.iloc[row][self.COLUMNS[col]]
                    if not diff: # Only show normalized on original values
                        try:
                            normalizedValue = round(float(self.df.iloc[row]["normalized_value"]), 3)
                            if not pd.isna(normalizedValue):
                                newValue = f"{value} ({normalizedValue})"
                                # self._dataCache[(row, col)] = str(newValue) if pd.notna(value) else ""
                                if role == Qt.DisplayRole:
                                    return newValue
                        except (ValueError, TypeError):
                            pass

                return self._dataCache[(row, col)]

        elif role == Qt.ForegroundRole:
            if col == 7:  # Severity column
                colors = {
                        0: QColor("#808080"),  # Gray for empty/none
                        1: QColor("#4CAF50"),  # Green for Low
                        2: QColor("#FFC107"),  # Amber for Medium
                        3: QColor("#FF9800"),  # Orange for High
                        4: QColor("#F44336")   # Red for Critical
                }
                try:
                    severityValue = self.df.iloc[row][self.COLUMNS[col]]
                    if pd.isna(severityValue):
                        return None
                    severityLevel = int(float(severityValue))
                    # Color scheme from less severe to more severe

                    return colors.get(severityLevel, QColor("#808080"))
                except (ValueError, TypeError):
                    return None
            elif col < 7:
                # Display green if modified value
                value = self.df.iloc[row][self.COLUMNS[col]]
                diff = value != self.originalDf.iloc[row][self.COLUMNS[col]]
                return QColor("#4CAF50") if diff else None

        return None

    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid() or role != Qt.EditRole:
            return False

        row, col = index.row(), index.column()

        if col in self.EDITABLE_COLUMNS:
            # Store the edit in temporary storage
            self._editedData[(row, col)] = value
            columnName = self.COLUMNS[col]

            # If we're not in edit mode, commit immediately
            if not self.currentRowEdit or self.currentRowEdit != row:
                try:
                    # Convert value based on column type
                    # if col == self.quantityColumn:  # quantity column
                    #     convertedValue = float(value) if value else 0
                    if col == self.severityColumn:  # severity column
                        convertedValue = int(float(value)) if value else 0
                    else:
                        convertedValue = value
                    # Update DataFrame
                    # self.df.loc[row, columnName] = convertedValue
                    self.df.iloc[row, self.df.columns.get_loc(columnName)] = convertedValue
                    # Clear cache and edited data
                    self._dataCache.pop((row, col), None)
                    self._editedData.pop((row, col), None)
                except (ValueError, TypeError) as e:
                    # Revert to original if conversion fails
                    if (row, col) in self._originalData:
                        self.df.iloc[row, self.df.columns.get_loc(columnName)] = self._originalData[row].get(col)
                    return False

            self.dataChanged.emit(index, index, [role])
            return True

        return False

    def handleEditingChanged(self, index: QModelIndex, editing: bool):
        """Handle changes in editing state"""
        row = index.row()
        if editing:
            self.currentRowEdit = row
            # Store original row data if not already stored
            if row not in self._originalData:
                self._originalData[row] = {
                    col: self.df.iloc[row][self.COLUMNS[col]]
                    for col in self.EDITABLE_COLUMNS
                }
        else:
            if row in self._originalData:
                if row == self.currentRowEdit:
                    # If accepting edits, commit all pending changes
                    for col in self.EDITABLE_COLUMNS:
                        if (row, col) in self._editedData:
                            try:
                                value = self._editedData[(row, col)]
                                # if col == self.quantityColumn:  # quantity
                                #     value = float(value) if value else 0
                                if col == self.severityColumn:  # severity
                                    value = int(float(value)) if value else 0
                                self.df.iloc[row, self.df.columns.get_loc(self.COLUMNS[col])] = value
                            except (ValueError, TypeError):
                                # Revert to original on conversion error
                                self.df.iloc[row, self.df.columns.get_loc(self.COLUMNS[col])] = self._originalData[row][col]
                else:
                    # If canceling edit, restore original data
                    for col, value in self._originalData[row].items():
                        self.df.loc[row, self.COLUMNS[col]] = value

                # Clear cache and edited data for the entire row
                for col in self.EDITABLE_COLUMNS:
                    self._dataCache.pop((row, col), None)
                    self._editedData.pop((row, col), None)

                del self._originalData[row]

            self.currentRowEdit = None

        # Emit dataChanged for the entire row
        self.dataChanged.emit(
            self.index(row, 0),
            self.index(row, len(self.COLUMNS) - 1),
            [Qt.DisplayRole]
        )

    def flags(self, index):
        flags = super().flags(index)
        if index.column() in self.EDITABLE_COLUMNS:
            # Only allow editing if row is in edit mode
            if self.currentRowEdit is not None and index.row() == self.currentRowEdit:
                flags |= Qt.ItemIsEditable
            else:
                flags &= ~Qt.ItemIsEditable  # Remove editable flag
        return flags

    def setDataFrame(self, df):
        """Set a new DataFrame as the data source"""
        self.beginResetModel()
        # Cast numeric columns to string to avoid display/editing issues
        numericCols = df.select_dtypes(include=['int64', 'float64']).columns
        for col in numericCols:
            df[col] = df[col].astype(str)
        self.df = df
        self.originalDf = df.copy()
        self.currentRowEdit = None
        self._dataCache.clear()  # Clear the cache when data changes
        self.endResetModel()

    def rowCount(self, parent=QModelIndex()):
        if parent.isValid():
            return 0
        return len(self.df)

    def columnCount(self, parent=QModelIndex()):
        if parent.isValid():
            return 0
        return len(self.COLUMNS)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self.COLUMNS[section]
        return None

    def revertValue(self, index):
        row, col = index.row(), index.column()
        if col not in self.EDITABLE_COLUMNS:
            return
        if (row, col) in self._originalData:
            self._dataCache[(row, col)] = self._originalData[(row, col)]
            self._editedData.pop((row, col), None)
        c = self.df.columns.get_loc(self.COLUMNS[col])
        self.setData(index, self.originalDf.iloc[row, c]) # Original value


class AuditTableView(QTableView):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)  # Enable mouse tracking for hover events
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.showContextMenu)

    def mouseMoveEvent(self, event):
        """Show tooltip when hovering over rows with severity > 0"""
        index = self.indexAt(event.position().toPoint())
        if not index.isValid():
            QToolTip.hideText()
            return

        model = self.model()
        if not model:
            return

        rowData = model.df.iloc[index.row()]
        severity = rowData.get('severity', 0)
        # Convert severity to int if it's a string or other type
        try:
            severity = int(severity)
        except (ValueError, TypeError):
            severity = 0

        tooltip = []
        columnName = model.COLUMNS[index.column()]

        try:
            c = model.df.columns.get_loc(columnName)
            value = model.df.iloc[index.row(), c]
            originalValue = model.originalDf.iloc[index.row(), c]
            if value != originalValue:
                tooltip.append(f"Original: {str(originalValue)}")
        except (IndexError, KeyError):
            pass

        if severity > 0:
            issue = rowData.get('issue_description', '')
            tooltip.append(f"Severity: {severity}\nIssue: {issue}")

        tooltip = '\n'.join(tooltip)

        if tooltip:
            QToolTip.showText(event.globalPosition().toPoint(), tooltip, self)
        else:
            QToolTip.hideText()

        super().mouseMoveEvent(event)

    def showContextMenu(self, position):
        """Show context menu for the selected table item"""
        index = self.indexAt(position)
        if not index.isValid():
            return

        menu = QMenu(self)

        # Add menu actions
        copyAction = menu.addAction("Copy Value")
        revertAction = menu.addAction("Revert To Original")

        # Get global position for the menu
        globalPos = self.viewport().mapToGlobal(position)

        # Show menu and get selected action
        action = menu.exec_(globalPos)

        # if action == editAction:
        #     # Trigger edit mode for the cell if it's editable
        #     if index.flags() & Qt.ItemIsEditable:
        #         self.edit(index)
        if action == copyAction:
            # Copy cell content to clipboard
            data = self.model().data(index, Qt.DisplayRole)
            if data:
                QApplication.clipboard().setText(str(data))
        # elif action == deleteAction:
        #     # Clear the cell content if it's editable
        #     if index.flags() & Qt.ItemIsEditable:
        #         self.model().setData(index, "", Qt.EditRole)
        elif action == revertAction:
            print(f"Reverting value for row {index.row()}:", self.model().COLUMNS[index.column()])
            self.model().revertValue(index)


class AuditDialog2(QWidget):
    def __init__(self, parent=None, bomDf: pd.DataFrame = pd.DataFrame()):
        super().__init__(parent)
        self.setObjectName("popup")
        self.bomDf: pd.DataFrame = bomDf
        self.setupUi()
        self.updateCountLabel(9, 4)  # Initial count

    def getSeverityColor(self, severity):
        """Get the color for a severity level"""
        try:
            severity = int(severity)
            colors = {
                1: "#856404",  # Low - Dark yellow
                2: "#D97817",  # Medium - Orange
                3: "#DC3545",  # High - Red
                4: "#721C24",  # Critical - Dark red
                5: "#1B1E21"   # Invalid - Dark gray
            }
            return colors.get(severity, "#664D03")  # Default to brown if not found
        except (ValueError, TypeError):
            return "#664D03"  # Default brown color

    def getSeverityString(self, severity):
        """Convert severity level to string"""
        try:
            severity = int(severity)
            levels = {
                1: "Low",
                2: "Medium",
                3: "High",
                4: "Critical",
                5: "Invalid"
            }
            return levels.get(severity, str(severity))
        except (ValueError, TypeError):
            return str(severity)

    def updateIssueDetails(self, rowData):
        """Update the issue details panel with validation issues"""
        hasIssues = False

        # Check if there are any validation issues
        if rowData and 'validation_issues' in rowData and rowData['validation_issues']:
            validationIssues = rowData['validation_issues']
            try:
                # Check if it's a list-like object with contents
                hasIssues = len(validationIssues) > 0
            except TypeError:
                # If it's a string or other type, consider it as having issues
                hasIssues = bool(validationIssues)

        # Hide panel and adjust splitter if no issues
        if not hasIssues:
            self.issuesWidget.hide()
            # Store current sizes before hiding
            sizes = self.rightSplitter.sizes()
            if len(sizes) == 2 and sizes[1] > 0:
                self.lastIssuesHeight = sizes[1]
                self.rightSplitter.setSizes([sizes[0] + sizes[1], 0])
            return

        # Show panel and restore previous height
        self.issuesWidget.show()
        if hasattr(self, 'last_issues_height') and self.lastIssuesHeight:
            sizes = self.rightSplitter.sizes()
            if len(sizes) == 2 and sizes[1] == 0:
                newTableHeight = sizes[0] - self.lastIssuesHeight
                self.rightSplitter.setSizes([newTableHeight, self.lastIssuesHeight])

        # Update title with item number
        pos = rowData.get('pos', '')
        self.issueTitle.setText(f"Issues for Item #{pos}")

        # Clear previous issue details
        for i in reversed(range(self.issuesContentLayout.count())):
            widget = self.issuesContentLayout.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Display validation issues
        try:
            # Try to handle ValidationIssue objects
            for issue in validationIssues:
                severity = getattr(issue, 'severity', None)
                color = self.getSeverityColor(severity)
                severityStr = self.getSeverityString(severity)
                issueText = f"• {issue.message} (Severity: {severityStr})"
                lblIssue = QLabel(issueText)
                lblIssue.setStyleSheet(f"color: {color};")
                lblIssue.setWordWrap(True)
                self.issuesContentLayout.addWidget(lblIssue)
        except (AttributeError, TypeError):
            try:
                # Try to handle as a string that might be a list representation
                if isinstance(validationIssues, str):
                    # Remove brackets and split by commas
                    issuesList = validationIssues.strip('[]').split(',')
                else:
                    issuesList = validationIssues

                for issue in issuesList:
                    if isinstance(issue, str):
                        issueText = f"• {issue.strip()}"
                        lblIssue = QLabel(issueText)
                        lblIssue.setStyleSheet("color: #664D03;")  # Default color for string issues
                        lblIssue.setWordWrap(True)
                        self.issuesContentLayout.addWidget(lblIssue)
            except Exception as e:
                # Last resort: just display as a single bullet point
                issueText = f"• {str(validationIssues)}"
                lblIssue = QLabel(issueText)
                lblIssue.setStyleSheet("color: #664D03;")  # Default color
                lblIssue.setWordWrap(True)
                self.issuesContentLayout.addWidget(lblIssue)

        # Show original and normalized values if available
        if 'original_value' in rowData and 'normalized_value' in rowData:
            valuesLayout = QVBoxLayout()
            valuesLayout.setSpacing(2)
            lblOriginal = QLabel(f"Original value:    {rowData['original_value']}")
            lblOriginal.setStyleSheet("color: #664D03;")
            lblNormal = QLabel(f"Normalized value:  {rowData['normalized_value']}")
            lblNormal.setStyleSheet("color: #664D03;")
            valuesLayout.addWidget(lblOriginal)
            valuesLayout.addWidget(lblNormal)
            self.issuesContentLayout.addLayout(valuesLayout)

    def updateCountLabel(self, total_items, items_with_issues):
        """Update the count label with item and issue statistics"""
        self.lblCount.setText(f"{total_items} items, {items_with_issues} with issues")

    def exportToExcel(self):
        """Export the validated DataFrame to an Excel file"""
        df = self.tableModel.df
        if df.empty:
            QMessageBox.warning(self, "Export Error", "No audit data available to export. Please run the audit first.")
            return

        # Open file dialog to get save location
        filePath, _ = QFileDialog.getSaveFileName(
            self,
            "Export Audit Report",
            "",
            "Excel Files (*.xlsx);;All Files (*)"
        )

        if filePath:
            # Add .xlsx extension if not present
            if not filePath.endswith('.xlsx'):
                filePath += '.xlsx'

            try:
                # Define the desired column order
                orderedColumns = [
                    'sys_path', 'pdf_id', 'pdf_page', 'material_description',
                    'quantity', 'is_pipe_item', 'is_valid', 'normalized_value',
                    'severity', 'issue_description'
                ]

                # Get all columns from the DataFrame
                allColumns = df.columns.tolist()

                # Remove ordered columns from the list to get remaining columns
                remainingColumns = [col for col in allColumns if col not in orderedColumns]

                # Combine ordered columns with remaining columns
                finalColumns = [col for col in orderedColumns if col in allColumns] + remainingColumns

                # Reorder and save to Excel
                df[finalColumns].to_excel(filePath)#, index=False)
                QMessageBox.information(self, "Export Success", f"Audit report saved to:\n{filePath}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to save file:\n{str(e)}")

    def setupUi(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create top title bar
        titleBar = QWidget()
        titleBar.setObjectName("titleBar")
        titleBar.setStyleSheet("QWidget#titleBar { background-color: #1E2329; }")
        titleBar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        titleBar.setFixedHeight(48)
        titleBarLayout = QHBoxLayout(titleBar)
        titleBarLayout.setContentsMargins(16, 8, 16, 8)

        # Title label
        lblTitle = QLabel("Audit Data Visualization")
        lblTitle.setObjectName("titleLabel")
        lblTitle.setStyleSheet("QLabel#titleLabel { color: white; font-size: 14px; }")
        titleBarLayout.addWidget(lblTitle)

        # Add spacer
        titleBarLayout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # Right side buttons
        self.pbRunAudit = QPushButton("Run Audit")
        self.pbRunAudit.setObjectName("primaryButton")
        self.pbRunAudit.clicked.connect(self.onRunAudit)
        titleBarLayout.addWidget(self.pbRunAudit)

        self.pbSave = QPushButton("Save Audit")
        self.pbSave.setObjectName("primaryButton")
        self.pbSave.setStyleSheet("""
            QPushButton#primaryButton {
                background-color: #4B7BEC;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton#primaryButton:hover {
                background-color: #3867D6;
            }
        """)
        self.pbSave.clicked.connect(self.onSaveAudit)
        titleBarLayout.addWidget(self.pbSave)

        self.pbExport = QPushButton("Export Report")
        self.pbExport.setStyleSheet("""
            QPushButton {
                background-color: #2D3436;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #222829;
            }
        """)
        self.pbExport.clicked.connect(self.exportToExcel)
        titleBarLayout.addWidget(self.pbExport)

        layout.addWidget(titleBar)

        # Create filter toolbar
        filterBar = QWidget()
        filterBar.setObjectName("filterBar")
        # filter_bar.setStyleSheet("QWidget#filterBar { background-color: #F8F9FA; border-bottom: 1px solid #E9ECEF; }")
        filterBar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        filterBar.setFixedHeight(48)
        filterBarLayout = QHBoxLayout(filterBar)
        filterBarLayout.setContentsMargins(16, 8, 16, 8)

        # Left side controls
        leftControls = QWidget()
        leftLayout = QHBoxLayout(leftControls)
        leftLayout.setContentsMargins(0, 0, 0, 0)
        leftLayout.setSpacing(16)  # Increased spacing between controls

        # Severity filter
        lblSeverity = QLabel("Severity:")
        leftLayout.addWidget(lblSeverity)
        self.cboxSeverity = QComboBox()
        self.cboxSeverity.addItems(["All Items", "Low", "Medium", "High", "Critical"])
        leftLayout.addWidget(self.cboxSeverity)

        # Page selector
        pageLabel = QLabel("Page:")
        leftLayout.addWidget(pageLabel)
        self.page_combo = QComboBox()
        self.page_combo.addItems(["All Pages"])
        leftLayout.addWidget(self.page_combo)

        # Search field
        self.searchField = QLineEdit()
        self.searchField.setPlaceholderText("Search items...")
        self.searchField.setStyleSheet("""
            QLineEdit {
                padding: 4px 8px;
                border: 1px solid #DFE4EA;
                border-radius: 4px;
                background-color: white;
            }
        """)
        leftLayout.addWidget(self.searchField)

        filterBarLayout.addWidget(leftControls)

        # Add spacer
        filterBarLayout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        self.pbPasteOnCopy = QPushButton("Paste on Copy Region")
        self.pbPasteOnCopy.setObjectName("primaryButton")
        self.pbPasteOnCopy.setCheckable(True)
        self.pbPasteOnCopy.clicked.connect(self.onPasteOnCopy)
        filterBarLayout.addWidget(self.pbPasteOnCopy)

        # Item and issue count
        self.lblCount = QLabel()
        self.lblCount.setObjectName("countLabel")
        # self.count_label.setStyleSheet("""
        #     QLabel#countLabel {
        #         color: #636E72;
        #         font-size: 13px;
        #     }
        # """)
        filterBarLayout.addWidget(self.lblCount)

        layout.addWidget(filterBar)

        # Create horizontal splitter
        self.splitter = QSplitter(Qt.Horizontal)

        # Create document viewer for left pane
        self.docViewer = DocumentViewOnly(self)
        self.docViewer.pbKeepPosition.setChecked(True)
        self.docViewer.pbFollowSelection.setChecked(True)
        self.splitter.addWidget(self.docViewer)

        # Create right pane for table and issue details
        rightPane = QWidget()
        rightLayout = QVBoxLayout(rightPane)
        rightLayout.setContentsMargins(0, 0, 0, 0)
        rightLayout.setSpacing(0)

        # Create vertical splitter for table and issue details
        self.rightSplitter = QSplitter(Qt.Vertical)

        # Create table view in top pane
        tableWidget = QWidget()
        tableLayout = QVBoxLayout(tableWidget)
        tableLayout.setContentsMargins(0, 0, 0, 0)

        self.tableView = AuditTableView()
        self.tableModel = AuditTableModel()
        self.tableView.setModel(self.tableModel)

        # Configure table view
        self.tableView.setSelectionMode(QTableView.SingleSelection)
        self.tableView.setSelectionBehavior(QTableView.SelectRows)
        self.tableView.verticalHeader().setVisible(False)
        self.tableView.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.tableView.horizontalHeader().setStretchLastSection(True)
        self.tableView.setShowGrid(False)
        self.tableView.setEditTriggers(QAbstractItemView.AllEditTriggers)

        # Set up selection handling
        selection_model = self.tableView.selectionModel()
        selection_model.selectionChanged.connect(self.onSelectionChanged)

        tableLayout.addWidget(self.tableView)
        self.rightSplitter.addWidget(tableWidget)

        # Create issue details panel in bottom pane
        self.issuesWidget = QWidget()
        self.issuesWidget.setObjectName("issuesPanel")
        self.issuesWidget.setStyleSheet("""
            QWidget#issuesPanel {
                background-color: #FFF9E6;
                border-top: 1px solid #FFE5B2;
            }
            QLabel {
                color: #664D03;
            }
        """)
        issuesLayout = QVBoxLayout(self.issuesWidget)
        issuesLayout.setContentsMargins(16, 12, 16, 12)
        issuesLayout.setSpacing(8)

        # Issue header
        issueHeader = QHBoxLayout()
        self.issueTitle = QLabel("Issues for Item #1")
        self.issueTitle.setStyleSheet("font-weight: bold;")
        issueHeader.addWidget(self.issueTitle)
        issueHeader.addStretch()

        pbEdit = QPushButton("Edit Item")
        pbEdit.setStyleSheet("""
            QPushButton {
                background-color: #0D6EFD;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0B5ED7;
            }
        """)
        issueHeader.addWidget(pbEdit)
        issuesLayout.addLayout(issueHeader)

        # Create scrollable area for issue content
        self.issuesContent = QWidget()
        self.issuesContentLayout = QVBoxLayout(self.issuesContent)
        self.issuesContentLayout.setSpacing(8)
        issuesLayout.addWidget(self.issuesContent)

        self.rightSplitter.addWidget(self.issuesWidget)

        # Set initial splitter sizes (70-30 split)
        self.rightSplitter.setSizes([700, 300])
        self.lastIssuesHeight = 300  # Store initial height
        rightLayout.addWidget(self.rightSplitter)

        # Add right pane to main splitter
        self.splitter.addWidget(rightPane)

        # Set initial main splitter sizes (50-50 split)
        self.splitter.setSizes([500, 500])
        layout.addWidget(self.splitter)

        # Set up delegates
        self.textDelegate = TextEditDelegate(self.tableView)
        self.actionDelegate = ActionButtonDelegate(self.tableView)
        self.severityDelegate = SeverityComboDelegate(self.tableView)

        # Configure header
        header = self.tableView.horizontalHeader()
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # material_description column
        header.setStretchLastSection(False)

        # Apply delegates to columns
        for col in self.tableModel.EDITABLE_COLUMNS:
            if col == self.tableModel.severityColumn:  # severity column
                self.tableView.setItemDelegateForColumn(col, self.severityDelegate)
            else:
                self.tableView.setItemDelegateForColumn(col, self.textDelegate)
        self.tableView.setItemDelegateForColumn(len(self.tableModel.COLUMNS)-1, self.actionDelegate)

    def setValidatedDataFrame(self, df):
        """Set the DataFrame and update the view"""
        self.tableModel.setDataFrame(df)

    def onSelectionChanged(self, selected, deselected):
        if not selected.indexes():
            self.issuesWidget.hide()
            return
        row = selected.indexes()[0].row()
        selected_data = self.tableModel.df.iloc[row].to_dict()
        self.updateIssueDetails(selected_data)

        pdfId = selected_data["pdf_id"]
        print(f"Selected item: {selected_data['pdf_page']}, {selected_data['pdf_id']}")
        self.docViewer.loadPdfById(pdfId)

    def onRunAudit(self):
        print("Run Audit clicked")
        message = "Process to run audit on BOM? This will replace table results\n\nWARNING THIS WILL RUN AUDIT ON ORIGINAL BOM AND LOSE CHANGES IF\n"
        message += "MODIFICATION HAVE BEEN MADE. SAVE AUDITED BOM AND REIMPORT INTO ATEM IF YOU WANT TO RE-RUN AUDIT ON NEWER BOM"
        if QMessageBox.question(self,
                               "Run Audit",
                               message) != QMessageBox.Yes:
            return

        if self.bomDf.empty:
            QMessageBox.warning(self, "Audit Error", "BOM is empty. Please load a valid BOM.")
            return

        from unit_tests.validate_quantities import validate_dataframe
        validatedDf: pd.DataFrame = validate_dataframe(self.bomDf)
        validatedDf = validatedDf.sort_values(by=['sys_path', 'pdf_page'], ascending=[True, True], key=natsort_keygen())

        self.setValidatedDataFrame(validatedDf)

        QMessageBox.information(self, "Audit Complete", "Audit completed. Table refreshed")

    def onSaveAudit(self):
        print("Save Audit clicked")

        # if QMessageBox.question(self, "Save Audit", "Process to save audit results?") != QMessageBox.Yes:
        #     return

        # Open file dialog to get save location
        filePath, _ = QFileDialog.getSaveFileName(
            self,
            "Export Audited BOM",
            "",
            "Excel Files (*.xlsx);;All Files (*)"
        )

        if not filePath:
            return

        validatedDf = self.tableModel.df

        auditedBom = self.bomDf.copy()
        columnToCheck = [self.tableModel.COLUMNS[n] for n in self.tableModel.EDITABLE_COLUMNS]
        columnToCheck = [c for c in columnToCheck if c != "severity"]

        for idx, row in validatedDf.iterrows():
            for col in columnToCheck:
                auditedBom.loc[idx, col] = row[col]

        try:
            # Define the desired column order
            orderedColumns = [
                'sys_path',
                'pdf_id',
                'pdf_page',
                'pos',
                'material_description',
                'quantity',
            ]

            # Get all columns from the DataFrame
            allColumns = auditedBom.columns.tolist()

            # Remove ordered columns from the list to get remaining columns
            remainingColumns = [col for col in allColumns if col not in orderedColumns]

            # Combine ordered columns with remaining columns
            finalColumns = [col for col in orderedColumns if col in allColumns] + remainingColumns

            # Reorder and save to Excel
            auditedBom = auditedBom.sort_values(by=['sys_path', 'pdf_page'], ascending=[True, True], key=natsort_keygen())
            auditedBom[finalColumns].to_excel(filePath)#, index=False)
            QMessageBox.information(self, "Export Success", f"Audited BOM saved to:\n{filePath}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to save file:\n{str(e)}")

    def onPasteOnCopy(self):
        print("Paste on Copy clicked")


def main():
    import sys
    sys.path[0] = ""
    from src.theme import stylesheet

    # Sample data for testing
    # df = pd.read_excel(r"C:\Users\<USER>\Documents\GitHub\Architekt-ATOM\temp\validated_df.xlsx")
    # df.fillna("", inplace=True)

    bom_df = pd.read_excel(r"temp/uncorrected_exported_bom_data_nofieldmap.xlsx")
    bom_df.fillna("", inplace=True)

    app = QApplication(sys.argv)
    app.setStyleSheet(stylesheet)

    auditWidget = AuditDialog2(bomDf=bom_df)
    auditWidget.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
