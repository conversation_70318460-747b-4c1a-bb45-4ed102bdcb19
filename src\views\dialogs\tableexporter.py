import sys
import re
from typing import TYPE_CHECKING
from pprint import pp
from pathlib import Path

if TYPE_CHECKING:  # This block is only for type checking tools like mypy.
    from PySide6.QtWidgets import QWidget

from PySide6.QtGui import *
from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
from functools import partial

from PySide6.QtWidgets import QWidget
from src.pyside_util import get_resource_qicon, get_resource_pixmap
from src.app_paths import getSavedFieldMapJson
from src.data.tables.alwaysvisibletablefields import always_visible_table_fields
from src.data.tables.defaultcolumnorder import default_column_order
from pubsub import pub


TABLES = [
    "BOM",
    "SPOOL",
    "SPEC",
    "RFQ",
    "General",
    "Outlier",
    "IFC",
    "generic_1",
    "generic_2",
]


class ColumnsList(QListWidget):

    def __init__(self, parent: QWidget = None) -> None:
        super().__init__(parent)


class TableWidgetDragRows(QTableWidget):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # set up on demand sorting
        header = self.horizontalHeader()
        header.setSortIndicatorShown(True)
        header.sortIndicatorChanged.connect(self.sortItems)

        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.viewport().setAcceptDrops(True)
        self.setDragDropOverwriteMode(False)
        self.setDropIndicatorShown(True)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.setSizeAdjustPolicy(QAbstractScrollArea.AdjustToContents)

        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.horizontalHeader().setStretchLastSection(True)
        self.setSortingEnabled(False)
        self.horizontalHeader().sectionClicked.connect(lambda: 0)
        self.horizontalHeader().setSectionsClickable(False)
        self.verticalHeader().hide()

    def dropEvent(self, event: QDropEvent):
        if not event.isAccepted() and event.source() == self:
            swapFrom = self.currentRow()
            pos = event.position().toPoint()
            swapTo = self.indexAt(pos).row()
            if swapTo == -1:
                if pos.y() < 20:
                    swapTo = 0
                else:
                    swapTo = self.rowCount() - 1

            if swapTo == swapFrom:
                # super().dropEvent(event)
                return
            print(swapFrom, swapTo)

            if swapTo > swapFrom:
                toTableType = self.takeItem(swapTo, 0)
                toSheetName = self.takeItem(swapTo, 1)
                toExport = self.takeItem(swapTo, 2)
                fromTableType = self.takeItem(swapFrom, 0)
                fromSheetName = self.takeItem(swapFrom, 1)
                fromExport = self.takeItem(swapFrom, 2)
            else:
                fromTableType = self.takeItem(swapFrom, 0)
                fromSheetName = self.takeItem(swapFrom, 1)
                fromExport = self.takeItem(swapFrom, 2)
                toTableType = self.takeItem(swapTo, 0)
                toSheetName = self.takeItem(swapTo, 1)
                toExport = self.takeItem(swapTo, 2)


            self.setItem(swapTo, 0, fromTableType)
            self.setItem(swapTo, 1, fromSheetName)
            self.setItem(swapTo, 2, fromExport)

            self.setItem(swapFrom, 0, toTableType)
            self.setItem(swapFrom, 1, toSheetName)
            self.setItem(swapFrom, 2, toExport)

            event.accept()

        super().dropEvent(event)

    def addRow(self, tableType, sheetName="", checked=True):
        """Insert a new row"""
        row = self.rowCount()
        self.insertRow(row)
        chkItem = QTableWidgetItem("")
        chkItem.setFlags(Qt.ItemIsUserCheckable|Qt.ItemIsSelectable|Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsEnabled)
        chkItem.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)
        self.setItem(row, 2, chkItem)

        item = QTableWidgetItem(tableType)
        item.setFlags(Qt.ItemIsSelectable|Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsEnabled)
        self.setItem(row, 0, item)

        self.setItem(row, 1, QTableWidgetItem(sheetName))

    def getState(self) -> list:
        state = []
        for y in range(self.rowCount()):
            tableType = self.item(y, 0).text()
            sheetName = self.item(y, 1).text()
            checked = self.item(y, 2).checkState() is Qt.CheckState.Checked
            self.item(y, 0)
            res = [tableType, sheetName, checked]
            state.append(res)
        return state

    def setState(self, state):
        self.setRowCount(0)
        for tableType, sheetName, checked in state:
            self.addRow(tableType, sheetName, checked)


class TableColumnOrganizer(QWidget):
    """Sub widget to organize per table"""

    def __init__(self, parent, tableType: str, fieldMap):
        super().__init__(parent)

        self.tableType = tableType
        self.fields: list = []
        self.fieldMap = fieldMap
        self.default_column_order = default_column_order.get(self.tableType, [])

        self.setLayout(QVBoxLayout())

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)

        vboxA = QWidget()
        vboxA.setLayout(QVBoxLayout())

        label = QLabel("Available")
        vboxA.layout().addWidget(label)
        label.setMinimumHeight(50) # Workaround - Vertically aligns the ListWidgets

        self.listAvailable = ColumnsList()
        self.listAvailable.setObjectName("ColumnOrganizer")
        self.listAvailable.itemDoubleClicked.connect(self.onAdd)
        self.listAvailable.setDefaultDropAction(Qt.DropAction.MoveAction)
        vboxA.layout().addWidget(self.listAvailable)
        hbox.layout().addWidget(vboxA)

        vboxB = QWidget()
        vboxB.setLayout(QVBoxLayout())

        pbAdd = QPushButton(">>")
        pbAdd.setFixedSize(QSize(96, 32))
        pbAdd.clicked.connect(self.onAdd)
        vboxB.layout().addWidget(pbAdd)
        pbRemove = QPushButton("<<")
        pbRemove.setFixedSize(QSize(96, 32))
        pbRemove.clicked.connect(self.onRemove)
        vboxB.layout().addWidget(pbRemove)

        hbox.layout().addWidget(vboxB)

        vboxC = QWidget()
        vboxC.setLayout(QVBoxLayout())

        hboxC = QWidget()
        hboxC.setMinimumHeight(32)
        hboxC.setLayout(QHBoxLayout())
        hboxC.layout().addWidget(QLabel("Column Order"))

        pbDefaults = QPushButton("Restore Default Order")
        hboxC.layout().addWidget(pbDefaults)
        pbDefaults.clicked.connect(self.restoreColumnDefaults)

        vboxC.layout().addWidget(hboxC)
        self.listColumns = ColumnsList()
        self.listColumns.setObjectName("ColumnOrganizer")
        self.listColumns.itemDoubleClicked.connect(self.onRemove)
        self.listColumns.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.listColumns.setDefaultDropAction(Qt.DropAction.MoveAction)
        vboxC.layout().addWidget(self.listColumns)
        hbox.layout().addWidget(vboxC)

        # Reordering column buttons
        vboxD = QWidget()
        vboxD.setLayout(QVBoxLayout())
        for action in ["Top", "Up", "Down", "Bottom"]:
            pb = QPushButton()
            pb.setMinimumSize(QSize(32, 32))
            pb.setIcon(get_resource_qicon(f"arrow-{action.lower()}.svg"))
            vboxD.layout().addWidget(pb)
            pb.pressed.connect(partial(self.onColumnButton, action))
        hbox.layout().addWidget(vboxD)

        self.restoreColumnDefaults()

    def onAdd(self):
        """Move column from available to column order"""
        item = self.listAvailable.takeItem(self.listAvailable.currentIndex().row())
        self.listColumns.addItem(item)

    def onRemove(self):
        """Remove column from column order"""
        row = self.listColumns.currentIndex().row()
        if not self.listColumns.item(row):
            return
        item = self.listColumns.takeItem(self.listColumns.currentIndex().row())
        self.listAvailable.addItem(item)
        self.listAvailable.sortItems(Qt.SortOrder.AscendingOrder)

    def onColumnButton(self, action):
        currentRow = self.listColumns.currentIndex().row()

        def insertItem(row):
            item = self.listColumns.takeItem(currentRow)
            self.listColumns.insertItem(row, item)
            index = self.listColumns.indexFromItem(self.listColumns.item(row))
            self.listColumns.setCurrentIndex(index)

        if action == "Top":
            insertItem(0)
        elif action == "Bottom":
            insertItem(self.listColumns.count() - 1)
        elif action == "Up":
            previousRow = currentRow - 1
            if previousRow >= 0:
                insertItem(previousRow)
        elif action == "Down":
            nextRow = currentRow + 1
            if nextRow < self.listColumns.count():
                insertItem(nextRow)

    def restoreColumnDefaults(self):
        self.listAvailable.clear() # Assume all columns in the default order
        self.listColumns.clear()
        self.fields = self.default_column_order
        for field in self.fields:
            display = self.fieldMap.get(field, {}).get("display", field)
            item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listColumns.addItem(item)

    def setFields(self):
        self.alwaysVisible = []
        if self.tableType:
            self.alwaysVisible = always_visible_table_fields[self.tableType]

        self.listAvailable.clear()
        for field in self.default_column_order:
            if field in self.fields:
                continue
            display = self.fieldMap.get(field, {}).get("display", field)
            item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listAvailable.addItem(item)

        self.listColumns.clear()
        for field in self.fields:
            display = self.fieldMap.get(field, {}).get("display", field)
            item = QListWidgetItem(display)
            item.setData(Qt.ItemDataRole.UserRole, field)
            self.listColumns.addItem(item)

    def getState(self) -> dict:
        columnOrder = []
        for row in range(self.listColumns.count()):
            item = self.listColumns.item(row)
            columnOrder.append(item.data(Qt.ItemDataRole.UserRole))
        return columnOrder

    def setState(self, state: list):
        """Ensure duplicates are excluded"""
        added = set()
        self.fields = []
        for field in state:
            if field in added:
                continue
            self.fields.append(field)
            added.add(field)
        self.setFields()


class TableExporter(QWidget):

    showCursorTooltip = Signal(str)
    exportActivated = Signal(dict)
    exportActivatedDev = Signal(dict)
    def __init__(self,
                 parent=None,
                 tableType:str=None,
                 fields:list=[]):
        super().__init__(parent)

        self.setObjectName("popup")
        self._presetNames = []
        self.tableType: str = tableType # The original table type which opened this dialog

        self.setLayout(QVBoxLayout())

        vbox = QWidget()
        vbox.setLayout(QVBoxLayout())
        self.layout().addWidget(vbox)

        groupBoxPresets = QGroupBox(title="Export Presets")
        groupBoxPresets.setLayout(QHBoxLayout())
        lbl = QLabel("Preset:")
        lbl.setMaximumWidth(60)
        lbl.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        groupBoxPresets.layout().addWidget(lbl)
        self.cboxPresets = QComboBox()
        self.cboxPresets.setEditable(True)
        self.cboxPresets.setMinimumWidth(500)
        self.cboxPresets.editTextChanged.connect(self.onPresetNameEdit)
        groupBoxPresets.layout().addWidget(self.cboxPresets)
        pbLoad = QPushButton("Load")
        pbLoad.clicked.connect(self.loadPreset)
        groupBoxPresets.layout().addWidget(pbLoad)
        pbDelete = QPushButton("Delete")
        pbDelete.clicked.connect(self.deletePreset)
        groupBoxPresets.layout().addWidget(pbDelete)
        pbSave = QPushButton("Save")
        pbSave.clicked.connect(self.savePreset)
        groupBoxPresets.layout().addWidget(pbSave)
        vbox.layout().addWidget(groupBoxPresets)

        groupBoxTables = QGroupBox(title="Tables to export")
        groupBoxTables.setLayout(QHBoxLayout())

        self.tablesToExport = TableWidgetDragRows()
        self.tablesToExport.setColumnCount(3)
        self.tablesToExport.setHorizontalHeaderLabels(["Table", "Sheet Name", "Export if available?"])

        for t in TABLES:
            self.tablesToExport.addRow(t, "")

        groupBoxTables.layout().addWidget(self.tablesToExport)
        self.tablesToExport.resizeColumnsToContents()
        vbox.layout().addWidget(groupBoxTables)

        groupBoxTabs = QGroupBox()
        groupBoxTabs.setLayout(QHBoxLayout())
        self.tabs = QTabWidget()
        fieldMap = self.getSimpleFieldMap()
        for table in TABLES:
            self.tabs.addTab(TableColumnOrganizer(None, table, fieldMap), table.upper())
        groupBoxTabs.layout().addWidget(self.tabs)
        self.layout().addWidget(groupBoxTabs)

        groupBoxExport = QGroupBox("Export")
        self.layout().addWidget(groupBoxExport)
        groupBoxExport.setLayout(QGridLayout())

        lbl = QLabel("Export dir:")
        lbl.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        groupBoxExport.layout().addWidget(lbl, 1, 0)

        self.leExportDir = QLineEdit()
        groupBoxExport.layout().addWidget(self.leExportDir, 1, 1, 1, 1)

        pbFilePicker = QPushButton("")
        pbFilePicker.setFixedSize(QSize(32, 32))
        pbFilePicker.setIcon(get_resource_qicon("folder.svg"))
        pbFilePicker.clicked.connect(self.onOpenFilePicker)
        groupBoxExport.layout().addWidget(pbFilePicker, 1, 3)

        self.radioGroup = QButtonGroup()
        self.radioExportCombine = QRadioButton("Export all tables into a single spreadsheet")
        self.radioGroup.addButton(self.radioExportCombine)
        groupBoxExport.layout().addWidget(self.radioExportCombine, 2, 0)
        self.radioExportSingle = QRadioButton("Export each table separately")
        self.radioGroup.addButton(self.radioExportSingle)
        groupBoxExport.layout().addWidget(self.radioExportSingle, 3, 0)
        self.radioGroup.buttonToggled.connect(self.onRadioExportToggled)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())

        self.chkIncludeDateTime = QCheckBox("Include Datetime")
        self.chkIncludeDateTime.setChecked(True)
        self.chkIncludeDateTime.stateChanged.connect(self.updateUi)
        hbox.layout().addWidget(self.chkIncludeDateTime)

        self.chkUseSheetName = QCheckBox("Use Sheet Name (If specified)")
        self.chkUseSheetName.setToolTip("If checked, generated filename will use sheet name alias over table name")
        self.chkUseSheetName.setChecked(True)
        self.chkUseSheetName.stateChanged.connect(self.updateUi)
        hbox.layout().addWidget(self.chkUseSheetName)
        hbox.layout().addStretch()

        groupBoxExport.layout().addWidget(hbox, 4, 1)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        groupBoxExport.layout().addWidget(hbox, 5, 0, 1, 3)
        lbl = QLabel("Filename:")
        lbl.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        hbox.layout().addWidget(lbl)

        self.leBasefilename = QLineEdit()
        self.leBasefilename.textEdited.connect(self.onBaseFilenameEdited)
        hbox.layout().addWidget(self.leBasefilename)

        self.lblParams = QLabel("")
        hbox.layout().addWidget(self.lblParams)

        hbox.layout().addWidget(QLabel(".xlsx"))

        controls = QWidget()
        controls.setLayout(QHBoxLayout())
        controls.layout().addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        pbClose = QPushButton("Close")
        pbClose.setMinimumSize(QSize(160, 48))
        controls.layout().addWidget(pbClose)
        pbExport = QPushButton("Export")
        pbExport.setMinimumSize(QSize(160, 48))
        controls.layout().addWidget(pbExport)
        self.layout().addWidget(controls)

        self.setMinimumSize(QSize(1200,768))
        self.setWindowIcon(QIcon(get_resource_pixmap("FFF_Architekt Integrated Systems_LOout.png")))
        self.setWindowTitle(f"Export Tables")

        pbClose.pressed.connect(self.onClose)
        pbExport.pressed.connect(self.onExport)
        self.showCursorTooltip.connect(self.onShowCursorTooltip)
        self.show()

        self.installEventFilter(self)

        tt = ("Drag and drop rows to change the saved sheet order"
              + "\n\nSheet name - Provide a sheet name. If no sheet name is provided, the table name is used"
              + "\n\nExported if available? - Export this table if it is currently displayed")
        self.tablesToExport.horizontalHeader().setToolTip(tt)

        self.radioExportCombine.setChecked(True)
        self.getExportPresetNames()
        self.updateUi()

    @property
    def presetNames(self):
        return self._presetNames

    @presetNames.setter
    def presetNames(self, value: list):
        try:
            self._presetNames = value
        except Exception as e:
             self._presetNames = []

    def eventFilter(self, target, event):
        if event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key.Key_Escape:
                self.close()
                return True

        return super().eventFilter(target, event)

    def getExportPresetNames(self) -> list:

        def callback(res):
            if res["status"] == "ok":
                presetNames = res["data"]
                self.presetNames = presetNames
                self.refreshPresetList()
            else:
                pass

        pub.sendMessage("preset-names-get", callback=callback)

    def getSimpleFieldMap(self):
        fieldMap = getSavedFieldMapJson()
        simpleFieldMap = {}
        for key in ["fields", "rfq_fields", "ancillary_fields", "rfq_fields2"]:
            subData = fieldMap.get(key, {})
            # Discard unuseful data
            for _, v in subData.items():
                try:
                    del v["options"]
                except Exception as e:
                    pass
            simpleFieldMap.update(subData)
        return simpleFieldMap

    def closeEvent(self, event: QCloseEvent) -> None:
        return super().closeEvent(event)

    def onExport(self):
        """Update config with new column order"""
        state = self.getState()
        self.exportActivated.emit(state)

    def onClose(self):
        self.close()

    def showTooltip(self, message, pos: QPoint):
        QTimer.singleShot(50, lambda: QToolTip().showText(pos + QPoint(2,2),
                            message,
                            w=self,
                            msecShowTime=1500))

    def onShowCursorTooltip(self, message: str):
        """Shows a tooltip at the mouse cursor position"""
        QTimer.singleShot(50, lambda: QToolTip().showText(QCursor().pos() + QPoint(2,2),
                                message,
                                w=self,
                                msecShowTime=1500))

    def getState(self) -> dict:
        state = {}
        sheetOrder = self.tablesToExport.getState()
        state["sheets"] = sheetOrder
        state["fields"] = {}
        for index in range(self.tabs.count()):
            co: TableColumnOrganizer = self.tabs.widget(index)
            state["fields"][co.tableType] = co.getState()

        state["useSheetName"] = self.chkUseSheetName.isChecked()
        state["includeDateTime"] = self.chkIncludeDateTime.isChecked()
        state["exportDir"] = self.leExportDir.text()
        state["baseName"] = self.leBasefilename.text()
        state["combinedExport"] = self.radioExportCombine.isChecked()
        return state

    def setState(self, state: dict):
        self.tablesToExport.setState(state["sheets"])
        for index in range(self.tabs.count()):
            co: TableColumnOrganizer = self.tabs.widget(index)
            co.setState(state["fields"][co.tableType])

        self.chkUseSheetName.setChecked(state.get("useSheetName", True))
        self.chkIncludeDateTime.setChecked(state.get("includeDateTime", True))
        self.radioExportCombine.setChecked(state.get("combinedExport", True))
        self.radioExportSingle.setChecked(not state.get("combinedExport", True))

        self.leExportDir.setText(state.get("exportDir"))

    def savePreset(self):
        self.showCursorTooltip.emit("Preset Saved")
        state = self.getState()
        presetName = self.cboxPresets.currentText()
        if not presetName:
            self.showCursorTooltip.emit("Preset name is required")
            return

        def callback(res):
            if res["status"] == "ok":
                self.getExportPresetNames()
                QMessageBox.information(self, "Preset saved", "Preset saved            ")
            else:
                self.showCursorTooltip.emit(f"Save preset error: {res['error']}")

        pub.sendMessage("preset-set", presetName=presetName, state=state, callback=callback)

    def deletePreset(self):
        presetName = self.cboxPresets.currentText()
        if not presetName or not presetName in self.presetNames:
            return
        if QMessageBox.question(self, "Delete preset                    ", "Delete preset?         ") != QMessageBox.Yes:
            return

        def callback(res):
            if res["status"] == "ok":
                QMessageBox.information(self, "Preset deleted", "Preset deleted            ")
                self.getExportPresetNames()
            else:
                self.showCursorTooltip.emit(f"Delete preset error: {res['error']}")

        pub.sendMessage("preset-delete", presetName=presetName, callback=callback)

    def onPresetNameEdit(self, value):

        def strip_nonalnum_re(word):
            return re.sub(r"^\W+|\W+$", "", word)

        value2 = strip_nonalnum_re(value)
        if value != value2:
            p = self.mapToGlobal(self.cboxPresets.pos() + QPoint(self.cboxPresets.width() // 2, 8))
            self.showTooltip("Illegal character", p)
        self.cboxPresets.setCurrentText(value2)

    def loadPreset(self):
        presetName = self.cboxPresets.currentText()
        if not presetName or not presetName in self.presetNames:
            return

        def callback(res):
            if res["status"] == "ok":
                state = res["data"]
                self.setState(state)
                QMessageBox.information(self, "Preset loaded", "Preset loaded            ")
            else:
                self.showCursorTooltip.emit(f"Load preset error: {res['error']}")

        pub.sendMessage("preset-get", presetName=presetName, callback=callback)

    def onOpenFilePicker(self):
        path = self.leExportDir.text() if Path(self.leExportDir.text()).exists() else ""
        exportDir = QFileDialog.getExistingDirectory(self,
                                                  "Choose export directory",
                                                  path,
                                                  QFileDialog.Option.ShowDirsOnly)
        if exportDir:
            self.leExportDir.setText(exportDir)

    def onRadioExportToggled(self, event):
        if event == self.radioExportCombine:
            self.updateUi()

    def updateUi(self):
        self.chkUseSheetName.setVisible(not self.radioExportCombine.isChecked())
        params = ""

        if self.chkUseSheetName.isVisible():
            if self.chkUseSheetName.isChecked():
                params += ("-" if self.leBasefilename.text() else "") + "<SheetName>"
            else:
                params += "<TableName>"

        if self.chkIncludeDateTime.isChecked():
            params += "-<DateTime>"

        self.lblParams.setText(params)

    def onBaseFilenameEdited(self, value):
        self.updateUi()

    def refreshPresetList(self):
        current = self.cboxPresets.currentText()
        self.cboxPresets.clear()
        self.cboxPresets.addItems(self._presetNames)
        if current in self._presetNames:
            self.cboxPresets.setCurrentText(current)
        else:
            self.cboxPresets.setCurrentText("")


if __name__ == "__main__":
    import sys
    sys.path[0] = ""  # For relative resource paths
    app = QApplication()
    try:
        from src.theme import stylesheet
        app.setStyleSheet(stylesheet)
    except:
        pass
    c = TableExporter(tableType="BOM")
    app.exec()