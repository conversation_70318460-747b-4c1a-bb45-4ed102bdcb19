import platform, io, ast, random
from http.client import PARTIAL_CONTENT
import sys, os, re, json, argparse
import multiprocessing
from multiprocessing import freeze_support
#from fitz.extra import page_count
import pandas as pd
import fitz  # PyMuPDF fitz uses points coordinate system. 
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from pprint import pprint as pp
import time
import uuid
import threading
import numpy as np
from fractions import Fraction
from PySide6.QtWidgets import QApplication, QMessageBox
from collections import defaultdict

#from .extract_tables import get_table_data, get_table_data_latest
#from .ai_processing import Gpt4Turbo, analyze_bom_data
#from .dbManager import DatabaseManager

from .extract_tables_v2 import get_table_data

from src.utils.logger import logger

from pubsub import pub
import unicodedata


print_converted_coords = True
# Set to limit the number of workers/processes for debugging
is_testing = False
override_page_limit = 5
debug_mode = False # Exports data to xlsx
test_transform = 0

bom_import = False # Import excel BOM data
general_import = False # Import excel general data
commit_pdf = True
commit_pdf_bytes = True

inspect_raw = True # Export the raw data
export_outlier = False


# logger = logging.getLogger(__name__)

#####################
#####################
#####################

# --> Handle ROI Functions

def load_and_combine_jsons(json_paths):
    combined_json = {}
    for group_number, path in json_paths.items():
        try:
            with open(path, 'r') as file:
                combined_json[str(group_number)] = json.load(file)
        except Exception as e:
            logger.error(f"Failed to load JSON for group {group_number}: {e}")
    return combined_json

def multi_convert_relative_coords_to_points(combined_roi_payload, page_groups):
    converted_payload = {}
    
    for group_number, roi_payload in combined_roi_payload.items():
        group_data = page_groups[page_groups['group_number'] == int(group_number)].iloc[0]
        width, height = group_data['width'], group_data['height']
        x_offset, y_offset = 0, 0  # You can add these to page_groups if needed
        
        converted_group_payload = []
        
        for item in roi_payload:
            try:
                converted_item = {"columnName": item.get("columnName", "Unknown")}
                process_coordinates(item, converted_item, width, height, x_offset, y_offset)
                if "headersSelected" in item:
                    converted_item["headersSelected"] = item["headersSelected"]
                converted_group_payload.append(converted_item)
            except Exception as e:
                logger.error(f"Error processing item {item.get('columnName', 'Unknown')} in group {group_number}: {e}")
        
        converted_payload[str(group_number)] = converted_group_payload
    
    if print_converted_coords:
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and "tableColumns" not in item:
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))

def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset

    }

# --> Handle ROI Functions

#####################
#####################
#####################

# --> Handle Raw Data Functions
def adjust_bbox(bbox, rotation, page_width, page_height):
    """
    Adjust a bounding box based on the page's rotation and dimensions.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation: The rotation angle in degrees (0, 90, 180, or 270).
    - page_width: The width of the page.
    - page_height: The height of the page.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    x0, y0, x1, y1 = bbox

    if rotation == 90:
        return (y0, page_width - x1, y1, page_width - x0)
    elif rotation == 180:
        return (page_width - x1, page_height - y1, page_width - x0, page_height - y0)
    elif rotation == 270:
        return (page_height - y1, x0, page_height - y0, x1)
    else:  # rotation == 0 or any other value
        return bbox


# def apply_transformations(raw_data, page_groups):
#     transformed_data = raw_data.copy()
    
#     for _, group in page_groups.iterrows():
#         group_mask = (transformed_data['pdf_page'] == group['page_number'])
#         if group['rotation'] != 0:
#             transformed_data.loc[group_mask, 'coordinates'] = transformed_data.loc[group_mask, 'coordinates'].apply(
#                 lambda x: adjust_bbox(eval(x), group['rotation'], group['width'], group['height'])
#             )
#             transformed_data.loc[group_mask, 'coordinates2'] = transformed_data.loc[group_mask, 'coordinates2'].apply(
#                 lambda x: adjust_bbox(eval(x), group['rotation'], group['width'], group['height'])
#             )
#             print(f"Applied rotation transformation to page {group['page_number']} in group {group['group_number']}")
    
#     return transformed_data

def apply_transformations(raw_data, page_groups):
    transformed_data = raw_data.copy()

    for _, group in page_groups.iterrows():
        group_mask = (transformed_data['pdf_page'] == group['page_number'])
        
        # Ensure rotation is applied only when needed
        if group['rotation'] != 0:
            # Safely parse and transform the 'coordinates' column
            transformed_data.loc[group_mask, 'coordinates'] = transformed_data.loc[group_mask, 'coordinates'].apply(
                lambda x: adjust_bbox(ast.literal_eval(x) if isinstance(x, str) else x, group['rotation'], group['width'], group['height'])
            )
            
            # Safely parse and transform the 'coordinates2' column
            transformed_data.loc[group_mask, 'coordinates2'] = transformed_data.loc[group_mask, 'coordinates2'].apply(
                lambda x: adjust_bbox(ast.literal_eval(x) if isinstance(x, str) else x, group['rotation'], group['width'], group['height'])
            )

            print(f"Applied rotation transformation to page {group['page_number']} in group {group['group_number']}")
    
    return transformed_data    

# --> Handle Raw Data Functions

#####################
#####################
#####################


# --> Handle value assignemnt 

def string_to_tuple(coords):
    if isinstance(coords, tuple):
        # It's already a tuple, return it as is
        return coords
    try:
        # Assuming the input is a string that needs to be converted to a tuple
        coords = coords.strip("() ")
        return tuple(map(float, coords.split(', ')))
    except (ValueError, SyntaxError):
        # Return a default value that indicates invalid coordinates
        return (0, 0, 0, 0)

def convert_coords_to_tuple(df): # Original
    try:
        # Convert the string representation of coordinates to actual tuples
        df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
        df['coordinates'] = df['coordinates'].apply(string_to_tuple)
        
        # Update 'coordinates2' if it's (0,0,0,0). Annotations are stored in 'coordinates'
        df.loc[df['coordinates2'] == (0, 0, 0, 0), 'coordinates2'] = df['coordinates']
    except Exception as e:
        logger.error(f"Error converting string to tuple: {e}", exc_info=True)
        return pd.DataFrame()
    return df

def calculate_overlap_area(box1, box2):
    # Calculate the overlapping area between two rectangles
    x_left = max(box1[0], box2[0])
    y_top = max(box1[1], box2[1])
    x_right = min(box1[2], box2[2])
    y_bottom = min(box1[3], box2[3])

    if x_right < x_left or y_bottom < y_top:
        return 0.0  # No overlap
    return (x_right - x_left) * (y_bottom - y_top)

def initialize_json_column_names(converted_roi):
    # Initialize a set to hold all column names from JSON
    json_column_names = set()
    for item in converted_roi:
        try:
            json_column_names.add(item['columnName'])
            
            # Check if 'tableColumns' is present in the item
            if 'tableColumns' in item:
                # Iterate through each column in 'tableColumns'
                for column_dict in item['tableColumns']:
                    # Each column_dict has the column name as the key
                    for column_name in column_dict.keys():
                        # Add the column name to the set of JSON column names
                        json_column_names.add(column_name)
        except Exception as e:
            logger.error(f"Error processing converted_roi item: {e} ---{item}", exc_info=True)
    return json_column_names

def consolidate_group(group, json_column_names, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon):
        #if all(group['sys_layout_valid']):
        # Initialize consolidated_data with JSON column names
        consolidated_data = {col: set() for col in json_column_names}
        # Initialize consolidated_data
        consolidated_data = {col: set() for col in columns_to_aggregate if col in group.columns}
        consolidated_data.update({col: group[col].iloc[0] for col in columns_to_keep if col in group.columns})
        
        # Check if any 'sys_layout_valid' value in the group is True
        if any(group['sys_layout_valid']):
            page_valid = True
            consolidated_data['sys_layout_valid'] = True

        overlap_sizes = {}
        # Apply logic for each coordinate range in JSON
        for index, row in group.iterrows():
            for col in columns_to_aggregate:
                if row[col] and row[col].strip():
                    consolidated_data[col].add(row[col].strip())
                    text_box = string_to_tuple(row['coordinates2'])  # Assuming this gives the text bounding box

            for item in converted_roi:
                try:
                    # if item['columnName'].lower() == 'bom':
                    #     continue
                    roi_column_name = item['columnName'].lower()
                    if roi_column_name in ['bom', 'spec', 'spool']:
                        continue

                        # Calculate overlap for other items
                        item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
                        overlap_area = calculate_overlap_area(text_box, item_box)
                        text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
                        overlap_ratio = overlap_area / text_area if text_area else 0
                        if overlap_ratio >= overlap_threshold:
                            check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold)

                    if 'tableColumns' in item:
                        for col in item['tableColumns']:
                            check_and_update_data(row, col, consolidated_data, epsilon, overlap_threshold)
                    else:
                        #print("/n/nERROR PASSING ITEM ON ELSE")
                        check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold, overlap_sizes)
                except Exception as e:
                    logger.error(f"Error analyzing field {item['columnName']}: {e}")
        try:
            # Convert sets to strings, joining unique values with '; '
            for col in columns_to_aggregate:
                consolidated_data[col] = '; '.join(consolidated_data[col])

        except Exception as e:
            logger.error(f"Error consolidating columns_to_aggregate: {e}")
            
        return consolidated_data

def check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold=0, overlap_sizes: dict={}):
    coords = row['coordinates2']
    item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
    text_box = (coords[0], coords[1], coords[2], coords[3])

    # Calculate overlap area
    overlap_area = calculate_overlap_area(text_box, item_box)
    text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
    overlap_ratio = overlap_area / text_area if text_area else 0

    # if item['columnName'] == "insulationThickness" and overlap_ratio > 0.8:
    #     print("Check", row['value'], item_box, text_box, overlap_area, text_area)
    #     print(overlap_ratio >= overlap_threshold, overlap_ratio, overlap_threshold)

    if overlap_ratio >= overlap_threshold:
        
        # Take all values in coordinate
        consolidated_data[item['columnName']] = consolidated_data.get(item['columnName'], '') + ' ' + row['value'].strip()
        
        # When updating value, store the overlap_area. As there may be
        # more than one annot within ROI box, we keep the largest one
        # current_area = overlap_sizes.get(item['columnName'], 0)
        # # If the overlap ratio is valid and, update consolidated data
        # if overlap_area > current_area:
        #     consolidated_data[item['columnName']] = consolidated_data.get(item['columnName'], '') + ' ' + row['value'].strip()
        #     overlap_sizes[item['columnName']] = overlap_area

def consolidate_ranges(df, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold=0):
    logger.info("Starting consolidation process...")
    
    #print("\n\nDF Consolidate Ranges: \n", len(df))
    
    #df.to_excel("Consolidate Ranges.xlsx")

    # pd.options.display.max_columns = None
    # pd.set_option('display.width', 1000)
    # pd.options.display.max_rows = 10

    consolidated_data_list = [] # Create a list to hold the consolidated data for each group
    final_df = pd.DataFrame()  # Initialize final_df to handle data loss on error
    
    # Validate inputs
    if not converted_roi:
        logger.error("converted_roi is necessary and cannot be empty.")
        return final_df # <-- Exit function
    if columns_to_aggregate is None:
        columns_to_aggregate = []
    if columns_to_keep is None:
        columns_to_keep = []

    #print("1")
    
    # Check for empty DataFrame and add 'annotMarkups' column
    if df.empty:
        logger.warning("Dataframe is empty. Exiting consolidation process.")
        return pd.DataFrame()
    
    # Check if column is in df and add it
    if 'annotMarkups' not in df.columns:
        df['annotMarkups'] = pd.Series([None]*len(df))
        
    #print("2")
     
    # --> # Convert 'coordinates' and 'coordinates2' columns from string to tuple
    df = convert_coords_to_tuple(df) 

    #print("3")
    
    # --> Process and return annotation data
    annot_types_df = process_extracted_annot_data(df)
 
    epsilon = 0.001 # Consider a small epsilon for float comparison inaccuracy
    grouped = df.groupby(['sys_path', 'pdf_page']) # Group by file path and page number
    
    # --> Initialize JSON Column Names
    json_column_names = initialize_json_column_names(converted_roi)

    
    #print("4")
    
    # Doesn't appear to be used <-- Look into this
    try:
        template_data = {col: '' for col in columns_to_aggregate if col in df.columns} # Initialize a template dictionary with columns based on the first page
    except Exception as e:
        logger.error(f"Error intializing template fields: {e}")
        
    #print("5")
    
    for (filepath, page), group in grouped: # Process each group
        try: 
            # --> Process and consolidate data groups
            consolidated_data = consolidate_group(group, json_column_names, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon)
            consolidated_data_list.append(consolidated_data) # Add the consolidated data dictionary to the list
        except Exception as e:
            logger.error(f"Error processing group Path: {filepath}, Page: {page}: Error Description: {e}", exc_info=True)
    
    #print("6")


    #print(f"\n\nANNOT TYPES DF: {annot_types_df} \n\n")
    try:
        if consolidated_data_list:
            consolidated_df = pd.DataFrame(consolidated_data_list) # Create a new DataFrame from the consolidated data list
            annot_types_df = annot_types_df.groupby(['sys_path', 'pdf_page'])['annotMarkups'].apply(lambda x: ', '.join(x)).reset_index()  # Aggregate 'annotMarkups' values into a comma-delimited string for each 'sys_path', 'pdf_page'
            
            #print(f"\n\nANNOT TYPES DF-2: {annot_types_df} \n\n")
            final_df = pd.merge(consolidated_df, annot_types_df, on=['sys_path', 'pdf_page'], how='outer')
    except Exception as e:
        logger.error(f"Error creating final DataFrame: {e}", exc_info=True)
    
    # logger.debug("Consolidation process completed successfully")
    
    #print("7")
    
    #final_df.to_excel("Final DF Consolidate Ranges.xlsx")
    return final_df

def process_table_rois(pdfID, pdf_path, page_data, page, page_number, converted_roi_payload, combined_text_tables, combined_annot_tables, combined_outlier_df, combined_annot_outlier_df, table_names=["BOM", "SPEC", "Spool"]):
    # Initialize blank dataframes    
    outliers_df, bom_df, annot_df = (pd.DataFrame() for _ in range(3))
    
    try:
        structured_text_tables, structured_annot_tables, outliers_df, annot_outlier_df = get_table_data(pdf_path, page, page_number, converted_roi_payload, page_data)   
        
        print("\n\n Page 1 Table Data:\n", structured_text_tables)
        
        # Insert the pdf_ID into each DataFrame in structured_text_tables and combine with combined_text_tables
        for table_name, df in structured_text_tables.items():
            if df is not None and not df.empty:
                df['pdf_id'] = pdfID
                normalized_table_name = table_name.lower()
                matching_key = next((k for k in combined_text_tables.keys() if k.lower() == normalized_table_name), None)
                if matching_key:
                    combined_text_tables[matching_key] = pd.concat([combined_text_tables[matching_key], df], ignore_index=True)
                else:
                    combined_text_tables[normalized_table_name] = df
                    
        # Insert the pdf_ID into each DataFrame in structured_annot_tables and combine with combined_annot_tables
        for table_name, df in structured_annot_tables.items():
            if df is not None and not df.empty:
                df['pdf_id'] = pdfID
                normalized_table_name = table_name.lower()
                matching_key = next((k for k in combined_annot_tables.keys() if k.lower() == normalized_table_name), None)
                if matching_key:
                    combined_annot_tables[matching_key] = pd.concat([combined_annot_tables[matching_key], df], ignore_index=True)
                else:
                    combined_annot_tables[normalized_table_name] = df
                                
        # Insert the pdf_ID into outliers_df
        if outliers_df is not None and not outliers_df.empty:
            outliers_df['pdf_id'] = pdfID
        if annot_outlier_df is not None and not annot_outlier_df.empty:
            annot_outlier_df['pdf_id'] = pdfID
                                
        if outliers_df is not None and not outliers_df.empty:
            combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)
                            
    except Exception as e:
        logger.error(f"Error getting table data on page {page_number}: {e}", exc_info=True)  
            
    return combined_annot_tables, combined_text_tables, combined_outlier_df, combined_annot_outlier_df


def format_value_with_colors(value, stroke_color, font_color):
    formatted_value = str(value) if value else ''
    if stroke_color or font_color:
        colors = []
        if stroke_color:
            colors.append(f"stroke:{format_color(stroke_color)}")
        if font_color:
            colors.append(f"font:{format_color(font_color)}")
        
        formatted_value += f"-[{','.join(colors)}]"
    return formatted_value

def format_color(color):
    if isinstance(color, list) and len(color) == 3:
        rgb = [int(c * 255) for c in color]
        return f"rgb({rgb[0]},{rgb[1]},{rgb[2]})"
    else:
        return str(color)

def is_inside(inner_coords, outer_coords):
    ix0, iy0, ix1, iy1 = inner_coords
    ox0, oy0, ox1, oy1 = outer_coords
    return ox0 <= ix0 and ix1 <= ox1 and oy0 <= iy0 and iy1 <= oy1

def safe_literal_eval(coord_str):
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

def process_extracted_annot_data(df):
    if debug_mode:
        df.to_excel("annot_element_data.xlsx")

    # Filter for Annotation types
    df = df[df['type'] != 'Text']    
    
    # Separate Text Box annotations
    text_boxes = df[df['type'] == 'Text Box'].copy()
    other_annots = df[df['type'] != 'Text Box'].copy()

    # Safely convert coordinates2 to tuples of floats
    text_boxes['coordinates2'] = text_boxes['coordinates2'].apply(safe_literal_eval)
    other_annots['coordinates2'] = other_annots['coordinates2'].apply(safe_literal_eval)

    # Remove rows with invalid coordinates
    text_boxes = text_boxes.dropna(subset=['coordinates2'])
    other_annots = other_annots.dropna(subset=['coordinates2'])

    # Find parent annotations for each Text Box
    for idx, text_box in text_boxes.iterrows():
        for other_idx, other_annot in other_annots.iterrows():
            if (text_box['sys_path'] == other_annot['sys_path'] and
                text_box['pdf_page'] == other_annot['pdf_page'] and
                is_inside(text_box['coordinates2'], other_annot['coordinates2'])):
                # Assign the text box value to the parent annotation
                other_annots.at[other_idx, 'value'] = text_box['value']

    # Process the annotations
    annotation_data = {}
    for annotation_type in other_annots['type'].unique():
        temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color']].copy()
        
        annotation_data[annotation_type] = temp_df.groupby(['sys_path', 'pdf_page']).apply(
            lambda x: [format_value_with_colors(v, sc, fc) for v, sc, fc in zip(x['value'], x['stroke_color'], x['font_color'])]
        ).reset_index(name='value')

    annot_types_df = other_annots[['sys_path', 'pdf_page', 'annotMarkups']].drop_duplicates().reset_index(drop=True)

    for index, row in annot_types_df.iterrows():
        annotations_for_row = {}
        for annotation_type, annotations_df in annotation_data.items():
            matching_annotations = annotations_df[
                (annotations_df['sys_path'] == row['sys_path']) & 
                (annotations_df['pdf_page'] == row['pdf_page'])
            ]['value'].tolist()

            if matching_annotations:
                if matching_annotations and isinstance(matching_annotations[0], list):
                    matching_annotations = [item for sublist in matching_annotations for item in sublist]
        
                annotations_for_row[annotation_type] = matching_annotations

        annot_types_df.at[index, 'annotMarkups'] = str(annotations_for_row)
        
    return annot_types_df


# --> Handle value assignemnt 

#####################
#####################
#####################


# Main Data Processing Functions
def process_page_group(raw_data, group_data, coordinate_json):
    group_number = group_data['group_number'].iloc[0]
    rotated_pages = []
    
    # Filter raw_data for this group
    group_raw_data = raw_data[raw_data['pdf_page'].isin(group_data['page_number'])]
    
    
    combined_text_tables = {table_name: pd.DataFrame() for table_name in ["BOM", "SPEC", "Spool"]}
    combined_annot_tables = {table_name: pd.DataFrame() for table_name in ["BOM", "SPEC", "Spool"]}
    combined_outlier_df = pd.DataFrame()
    
    for page_number in group_data['page_number']:
        page_data = group_raw_data[group_raw_data['pdf_page'] == page_number]
        
        # Process table ROIs
        combined_annot_tables, combined_text_tables, combined_outlier_df, _ = process_table_rois(
            page_data['pdf_id'].iloc[0], 
            page_data['sys_path'].iloc[0], 
            page_data,
            None,  # page object is not needed anymore
            page_number, 
            coordinate_json, 
            combined_text_tables, 
            combined_annot_tables, 
            combined_outlier_df,
            pd.DataFrame(),  # We don't need combined_annot_outlier_df here
            ["BOM", "SPEC", "Spool"]
        )
        
    
    # Consolidate non-table data
    columns_to_aggregate = [item['columnName'] for item in coordinate_json if 'tableCoordinates' not in item]
    columns_to_keep = ['sys_path', 'pdf_page', 'pdf_id']
    consolidated_data = consolidate_ranges(group_raw_data, coordinate_json, columns_to_aggregate, columns_to_keep)
    
    return {
        'combined_text_tables': combined_text_tables,
        'combined_annot_tables': combined_annot_tables,
        'combined_outlier_df': combined_outlier_df,
        'consolidated_data': consolidated_data
    }


def main(raw_data_path, page_group_summary_path):
    
    # Simulate data
    raw_data = pd.read_excel(raw_data_path)
    page_groups = pd.read_excel(page_group_summary_path)
    
    # Define paths for JSON files
    json_paths = {
        1: r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\1.json",
        2: r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\2.json",
        3: r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\3.json"
    }
    
    # Load and combine JSONs
    combined_roi_payload = load_and_combine_jsons(json_paths)
    
    # Convert coordinates
    converted_payload = multi_convert_relative_coords_to_points(combined_roi_payload, page_groups)
    
    # Apply transformations
    transformed_data = apply_transformations(raw_data, page_groups)# Apply transformations to raw data coordinates (Need to do initially when extracting the data)
    
    results = []
    for group_number, group_data in page_groups.groupby('group_number'):
        group_coordinate_json = converted_payload[str(group_number)]
        group_result = process_page_group(transformed_data, group_data, group_coordinate_json)
        results.append(group_result)
        
    
    try:
        final_result = pd.concat(results)
        
    except Exception as e:
        final_result = []
        logger.error(f"All results are blank: {e}")
    return final_result


raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\Raw Data.xlsx"
page_group_summary_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\Page Group Summary.xlsx"

data = main(raw_data_path, page_group_summary_path)

data.to_excel(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Development\ATEM\ATEM Tests\Converted Coordinates.xlsx")

print("\n\nData:", data)


