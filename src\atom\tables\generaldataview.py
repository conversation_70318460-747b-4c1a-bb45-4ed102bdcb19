from src.utils.logger import logger
from src.views.tableresultsview import TableResultsViewBase
from pubsub import pub
import pandas as pd
from src.atom.dbManager import DatabaseManager

# logger = logging.getLogger(__file__)


class GeneralDataView(TableResultsViewBase):

    def __init__(self, parent) -> None:
        super().__init__(parent)
        logger.info("Creating General Table...")

    def  __repr__(self) -> str:
        return "General"

    def initToolbar(self):
        super().initToolbar()

    def onToolbarBtn(self, name):
        # Example logging or print statement for debugging
        print(f"Toolbar button clicked: {name}")
        if name == "tree": # Tree hieratchy view (QTreeWidget or similar)
            pass
        elif name == "save": # Push to database
            data = self.getTableData()  # Use your existing method
            df = pd.DataFrame(data)
            print("\n\nGeneral DF ROWS FOR DB COMMIT: \n", len(df))
            db_manager = DatabaseManager()
            db_manager.insert_dataframe(df, "General")
            pub.sendMessage("request-project-table-data", data=self.projectData, types=["general_data"])
        else:
            super().onToolbarBtn(name)

    def setTableData(self, data: pd.DataFrame):
        logger.info("Setting General Table Data...")
        super().setTableData(data, autosize=False)
        logger.info("Appending Ancillary Fields to General Table Data...")
        self._addAncillaryFields()
        self.restoreColumnOrder()
        # Column freezing
        hasDrawing = "drawing" in data.columns
        hasLineNumber = "lineNumber" in data.columns
        hasSheet = "sheet" in data.columns
        toFreeze = []
        if hasLineNumber:
            toFreeze.append("lineNumber")
        elif hasDrawing:
            toFreeze.append("drawing")
        if hasSheet:
            toFreeze.append("sheet")
        if toFreeze:
            self.table.setEditFreezeColumns(toFreeze)