from src.utils.logger import logger
import math
import pandas as pd

from data_conversions import get_ef
from value_mappings import pipe_od_lookup
from src.app_paths import resource_path, getEfLookupPath

# logger = logging.getLogger(__file__)


def _calculate_surface_area(df: pd.DataFrame):
    """Calculate surface area for rows where `general_category` is `LF`"""
    for index, row in df.iterrows():
        if row['general_category'] != "LF":
            continue
        try:
            quantity = float(row['quantity']) if pd.notnull(row['quantity']) else 0
            size1 = float(row['size1']) if pd.notnull(row['size1']) else 0

            # Look up the O.D. if size is less than 14
            if size1 < 14:
                size_key = f"{size1:.3f}"
                if size_key in pipe_od_lookup:
                    size1 = pipe_od_lookup[size_key]
                else:
                    logger.warning(f"Size {size_key} not found in lookup table for row {index}")

            # Convert size1 from inches to feet
            size1_feet = size1 / 12

            # Calculate the radius in feet
            radius_feet = size1_feet / 2

            # Calculate surface area: 2 * pi * r * L
            surface_area = 2 * math.pi * radius_feet * quantity

            df.at[index, 'sf'] = surface_area  # Update 'sf' with the calculated surface area in square feet
        except (KeyError, TypeError, ValueError) as e:
            logger.warning(f"Error calculating surface area for row {index}: {e}")

    return df


def _get_ef_lookup(units: str = 'Standard'):
    """Return User EF lookup, or internal lookup template if it does not exist"""

    # Define sheet names
    sheet_names = {
        "reducers": "Table Red",
        "elbows_reducers": "Table Elbows Red",
        "tee_reducers": "Table Tee Red",
        "cross_reducers": "Table Cross Red",
    }

    # Metric Table EF not supported yet
    if units == "Standard":
        sheet_names["ef"] = "Standard EF"
        sheet_names["sf"] = "Standard SF"
    else: 
        # TODO: Change to metric once support is added
        sheet_names["ef"] = "Standard EF"
        sheet_names["sf"] = "Standard SF"

    def read_excel(file, sheet_name_key, index_col=0) -> pd.DataFrame:
        return pd.read_excel(resource_path(file), 
                            index_col=index_col, 
                            sheet_name=sheet_names[sheet_name_key])

    ef_template_file = "src/data/tables/ef_template.xlsx"
    # Load app default lookups
    lookup_templates = {}
    for sheet_name_key in sheet_names.keys():
        lookup_templates[sheet_name_key] = read_excel(ef_template_file, sheet_name_key=sheet_name_key)

    # User defined lookups
    user_lookups = {}

    # Merge with user EF if exists
    lookup_file = getEfLookupPath()

    for sheet_name_key in sheet_names.keys():
        try:
            user_lookups[sheet_name_key] = read_excel(lookup_file, sheet_name_key=sheet_name_key)
        except Exception as e:
            # Failed to load user lookup file
            # Create a new workbook and write the template data to the appropriate sheets
            print("No existing lookup file found. Creating lookup file")
            with pd.ExcelWriter(lookup_file) as writer:
                for sheet_name_key in sheet_names.keys():
                    s = lookup_templates[sheet_name_key]
                    s.to_excel(writer, sheet_name=sheet_names[sheet_name_key], index=True)
            break

    # Re-read lookup file
    for sheet_name_key in sheet_names.keys():
        try:
            user_lookups[sheet_name_key] = read_excel(lookup_file, sheet_name_key=sheet_name_key)
        except Exception as e:
            user_lookups[sheet_name_key] = pd.DataFrame()

    def format_size_columns(df):
        size_columns = ["Size1", "Size2", "Size3"]
        for col in size_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: '{:.3f}'.format(x) if pd.notna(x) else '')
        return df
    
    def get_combined_lookup(sheet_name_key, format_size: bool = False):
        df = user_lookups[sheet_name_key].combine_first(lookup_templates[sheet_name_key].fillna(0))
        if format_size_columns:
            df = format_size_columns(df)
        return df

    # Create a dictionary to store the lookup tables
    # Merge user lookups into app's internal lookups
    lookup_tables = {}
    try:
        lookup_tables["ef"] = get_combined_lookup("ef")
        lookup_tables["sf"] = get_combined_lookup("sf")
        lookup_tables["reducers"] = get_combined_lookup("reducers", format_size=True)
        lookup_tables["elbows_reducers"] = get_combined_lookup("elbows_reducers", format_size=True)
        lookup_tables["tee_reducers"] = get_combined_lookup("tee_reducers", format_size=True)
        lookup_tables["cross_reducers"] = get_combined_lookup("cross_reducers", format_size=True)
    except:
        lookup_tables["ef"] = lookup_templates["ef"]
        lookup_tables["sf"] = lookup_templates["sf"]
        lookup_tables["reducers"] = format_size_columns(lookup_templates["reducers"])
        lookup_tables["elbows_reducers"] = format_size_columns(lookup_templates["elbows_reducers"])
        lookup_tables["tee_reducers"] = format_size_columns(lookup_templates["tee_reducers"])
        lookup_tables["cross_reducers"] = format_size_columns(lookup_templates["cross_reducers"])

    return lookup_tables

# Public functions

def update_bom_ef_lookup(bom_data: pd.DataFrame):
    """Retrieve lookup table and update BOM data. 
    Dataframe is modified inplace
    """
    lookup_tables = _get_ef_lookup()
    try:
        get_ef(bom_data, lookup_tables=lookup_tables)
        # Calculate surface area of pipe/tube (LF) and update 'sf' values
        _calculate_surface_area(bom_data)
    except Exception as e:
        logger.error(f"Error updating BOM {e}", exc_info=True)