FUNC_CALCULATE_AREA = """  -- Calculates pipe surface area based on diameter and length with support for different units
    CREATE OR REPLACE FUNCTION calculate_pipe_surface_area(
        diameter NUMERIC,  -- diameter: The diameter of the pipe
        length NUMERIC,  -- length: The length of the pipe
        diameter_unit VARCHAR DEFAULT 'inches',  -- diameter_unit: The unit of the diameter (e.g., inches, feet, mm, m)
        length_unit VARCHAR DEFAULT 'feet',  -- length_unit: The unit of the length (e.g., inches, feet, mm, m)
        OUT area_sq_inches NUMERIC,  -- area_sq_inches: The calculated surface area in square inches
        OUT area_sq_feet NUMERIC,  -- area_sq_feet: The calculated surface area in square feet
        OUT area_sq_mm NUMERIC,  -- area_sq_mm: The calculated surface area in square millimeters
        OUT area_sq_m NUMERIC  -- area_sq_m: The calculated surface area in square meters
    )
    AS $$
    DECLARE
        diameter_in_inches NUMERIC;  -- diameter_in_inches: The diameter in inches
        length_in_inches NUMERIC;  -- length_in_inches: The length in inches
        diameter_in_mm NUMERIC;  -- diameter_in_mm: The diameter in millimeters
        length_in_mm NUMERIC;  -- length_in_mm: The length in millimeters
    BEGIN
        -- Convert diameter to standard units (inches and mm)
        CASE LOWER(diameter_unit)
            WHEN 'inches' THEN 
                diameter_in_inches := diameter;
                diameter_in_mm := diameter * 25.4;
            WHEN 'feet' THEN 
                diameter_in_inches := diameter * 12;
                diameter_in_mm := diameter_in_inches * 25.4;
            WHEN 'mm' THEN 
                diameter_in_mm := diameter;
                diameter_in_inches := diameter / 25.4;
            WHEN 'm' THEN 
                diameter_in_mm := diameter * 1000;
                diameter_in_inches := diameter_in_mm / 25.4;
            ELSE
                RAISE EXCEPTION 'Unsupported diameter unit: %', diameter_unit;
        END CASE;
        
        -- Convert length to standard units (inches and mm)
        CASE LOWER(length_unit)
            WHEN 'inches' THEN 
                length_in_inches := length;
                length_in_mm := length * 25.4;
            WHEN 'feet' THEN 
                length_in_inches := length * 12;
                length_in_mm := length_in_inches * 25.4;
            WHEN 'mm' THEN 
                length_in_mm := length;
                length_in_inches := length / 25.4;
            WHEN 'm' THEN 
                length_in_mm := length * 1000;
                length_in_inches := length_in_mm / 25.4;
            ELSE
                RAISE EXCEPTION 'Unsupported length unit: %', length_unit;
        END CASE;
        
        -- Calculate lateral surface area in imperial units
        -- Formula: SA = 2π * (d/2) * l
        area_sq_inches := 2 * PI() * (diameter_in_inches / 2) * length_in_inches;
        area_sq_feet := area_sq_inches / 144; -- Convert square inches to square feet
        
        -- Calculate lateral surface area in metric units
        area_sq_mm := 2 * PI() * (diameter_in_mm / 2) * length_in_mm;
        area_sq_m := area_sq_mm / 1000000; -- Convert square mm to square meters
        
        -- Round to 2 decimal places for readability
        --area_sq_inches := ROUND(area_sq_inches, 2);
        --area_sq_feet := ROUND(area_sq_feet, 2);
        --area_sq_mm := ROUND(area_sq_mm, 2);
        --area_sq_m := ROUND(area_sq_m, 2);
    END;
    $$ LANGUAGE plpgsql;

    -- Example usage:
    -- Imperial: SELECT * FROM calculate_pipe_surface_area(8, 2, 'inches', 'feet');
    -- Metric: SELECT * FROM calculate_pipe_surface_area(200, 1, 'mm', 'm');
"""


########################################################

FUNC_UPDATE_CALCULATIONS = """
/* 
    * IMPROVED RFQ CALCULATION FUNCTION
    * APPLIED TO RFQ AND BOM TABLES
    * 
    * This function implements a robust calculation methodology for equivalent lengths
    * and surface areas in RFQ items. The implementation addresses several complex
    * requirements and edge cases:
    *
    * 1. CATEGORY LOOKUP LOGIC:
    *    - Uses fitting_category first for items with RFQ scope of "Fittings"
    *    - Falls back to general_category if fitting_category doesn't produce a match
    *    - Supports lookup of many types in both fitting_category and general_category
    *    - Includes safety checks to prevent errors with NULL values
    *
    * 2. MULTI-STEP SIZE MATCHING ALGORITHM:
    *    - Step 1: Tries for exact match first, including compound sizes in any order
    *    - Step 2: For compound fittings (with size1 and size2), uses an advanced closest-match algorithm:
    *        a. Calculates total difference between request sizes and available sizes
    *        b. Finds the compound size entry with minimal total difference
    *        c. Falls back to using larger size if no compound size matches are found
    *    - Step 3: Uses improved size approximation that:
    *        a. First tries to find the next larger size (MIN size that is ≥ requested size)
    *        b. If no larger size exists, falls back to the largest available size
    *
    * 3. COMPOUND SIZE HANDLING:
    *    - Handles size reversals (treats size1=4, size2=2 the same as size1=2, size2=4)
    *    - Explicitly identifies larger and smaller sizes for improved matching logic
    *    - First attempts to find a similar compound size using a difference-based algorithm
    *    - For unmatched compound items, uses the larger of the two sizes for single-size lookup
    *    - Maintains correct matching regardless of size order in lookup table
    *
    * 4. EDGE CASE HANDLING:
    *    - Always rounds up to the next available size when exact size not found
    *    - When search size is between available sizes (e.g., 1.5), finds next larger size (e.g., 2)
    *    - When search size is larger than all available sizes, uses largest available
    *    - When search size is smaller than all available sizes, uses smallest available
    *    - Returns zero for completely unmatched items
    *
    * 5. QUANTITY MULTIPLICATION:
    *    - Correctly applies quantity multiplier to both lookup and factor methods
    *    - Ensures calculated values reflect total quantities, not just per-unit values
    *
    * 6. FACTOR METHOD IMPROVEMENTS:
    *    - Tries lookup with general_category first
    *    - Falls back to fitting_category if needed
    *    - Also checks valve_type and pipe_category as additional fallbacks
    *    - Only sets calculated_area to NULL for factor method (area not calculated this way)
    *
    * The function works with two primary calculation methods:
    *   a) 'lookup': Uses predefined values from vw_fittings_lookup view
    *   b) 'factor': Uses multiplier factors from atem_equiv_length_factors table
    *
    * The database trigger watches for changes to key columns and recalculates values
    * automatically, ensuring data consistency throughout the application.
    */

    CREATE OR REPLACE FUNCTION update_component_calculations()
    RETURNS TRIGGER AS $$
    DECLARE
        eq_length DECIMAL(20,12);  -- eq_length: The equivalent length
        area DECIMAL(20,12);  -- area: The calculated area
        eq_length_factor DECIMAL(10,3);  -- eq_length_factor: The factor for equivalent length calculation
        method VARCHAR(50);  -- method: The calculation method (lookup or factor)
        v_profile_name VARCHAR(255);  -- v_profile_name: The profile name
        v_lookup_category VARCHAR(100);  -- v_lookup_category: The category for lookup
        v_closest_size DECIMAL(8,3);  -- v_closest_size: The closest size found
        v_closest_size1 DECIMAL(8,3);
        v_closest_size2 DECIMAL(8,3);
        v_larger_size DECIMAL(8,3);  -- v_larger_size: The larger size for compound fittings
        v_smaller_size DECIMAL(8,3); -- v_smaller_size: The smaller size for compound fittings
        v_flange_method VARCHAR(50);  -- v_flange_method: The flange calculation method
        v_flange_type VARCHAR(50);  -- v_flange_type: The derived flange type
        v_lbs INTEGER;  -- v_lbs: The pressure rating in lbs

        -- Pipe Variables
        v_profile_id INTEGER;
        v_eq_length_factor NUMERIC;

        lookup_found BOOLEAN := FALSE;  -- Flag to track if a lookup was successful
        -- Debug variables
        v_debug_sql TEXT;
        v_available_sizes TEXT;
        factor_value numeric; -- Add this line to declare the new variable
    BEGIN
        -- Set default values in case no match is found
        eq_length := 0;
        area := 0;

        -- Skip if profile_id is null
        IF NEW.profile_id IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- Retrieve calculation method and profile name
        --SELECT equivalent_length_method, profile_name INTO method, v_profile_name
        -- FROM public.atem_client_profiles WHERE id = NEW.profile_id;

        -- Retrieve calculation method, profile name, and flange method
        SELECT equivalent_length_method, profile_name, flange_calculation_method 
        INTO method, v_profile_name, v_flange_method
        FROM public.atem_client_profiles WHERE id = NEW.profile_id;

        -- Skip if no method found, but ensure defaults
        IF method IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- NEW SECTION: SPECIAL HANDLING FOR PIPE CALCULATIONS
        -- This should be placed where you handle different component types

        -- For pipe items (pipe_category = 'Pipe' or general_category = 'LF')
        IF NEW.pipe_category = 'Pipe' OR NEW.general_category = 'LF' THEN
            -- Get pipe calculation methods from profile
            PERFORM public.atem_client_profiles.pipe_area_method, 
                    public.atem_client_profiles.pipe_length_method,
                    public.atem_client_profiles.pipe_minimum_size
            FROM public.atem_client_profiles 
            WHERE id = NEW.profile_id;
            
            -- Use the calculate_pipe_surface_area function to get area per linear foot
            IF (SELECT pipe_area_method FROM public.atem_client_profiles WHERE id = NEW.profile_id) = 'formula' THEN
                -- Apply minimum size if configured
                IF (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) IS NOT NULL 
                AND (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) > 0 
                AND NEW.size1 < (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) THEN
                    
                    -- Use minimum size for calculation
                    SELECT area_sq_feet INTO area
                    FROM calculate_pipe_surface_area(
                        (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id), 
                        1.0, 'inches', 'feet'
                    );
                    
                    RAISE NOTICE 'Using minimum pipe size % instead of %', 
                        (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id), 
                        NEW.size1;
                ELSE
                    -- Use actual size for calculation
                    SELECT area_sq_feet INTO area
                    FROM calculate_pipe_surface_area(NEW.size1, 1.0, 'inches', 'feet');
                END IF;
                
                RAISE NOTICE 'Calculated pipe area using formula: % sq ft per linear foot', area;
            ELSE
                -- Use lookup method (default)
                -- Apply minimum size if configured
                IF (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) IS NOT NULL 
                AND (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) > 0 
                AND NEW.size1 < (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id) THEN
                    
                    -- Use minimum size for lookup
                    SELECT area_ft INTO area
                    FROM public.vw_fittings_lookup
                    WHERE lookup_category = 'LF'
                    AND profile = v_profile_name
                    AND size1 = (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id)
                    LIMIT 1;
                    
                    RAISE NOTICE 'Using minimum pipe size % instead of %', 
                        (SELECT pipe_minimum_size FROM public.atem_client_profiles WHERE id = NEW.profile_id), 
                        NEW.size1;
                ELSE
                    -- Use actual size for lookup
                    SELECT area_ft INTO area
                    FROM public.vw_fittings_lookup
                    WHERE lookup_category = 'LF'
                    AND profile = v_profile_name
                    AND size1 = NEW.size1
                    LIMIT 1;
                END IF;
                
                -- If no exact match, find closest size
                IF area IS NULL THEN
                    -- Try to find the next larger size
                    SELECT area_ft INTO area
                    FROM public.vw_fittings_lookup
                    WHERE lookup_category = 'LF'
                    AND profile = v_profile_name
                    AND size1 >= NEW.size1
                    ORDER BY size1
                    LIMIT 1;
                    
                    -- If still no match, use largest available
                    IF area IS NULL THEN
                        SELECT area_ft INTO area
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = 'LF'
                        AND profile = v_profile_name
                        ORDER BY size1 DESC
                        LIMIT 1;
                    END IF;
                END IF;
            END IF;
            
            -- Calculate pipe equivalent length based on method
            IF (SELECT pipe_length_method FROM public.atem_client_profiles WHERE id = NEW.profile_id) = 'factor' THEN
                -- Use factor method
                -- To use a table alias to qualify the column:
                SELECT f.eq_length_factor INTO v_eq_length_factor
                FROM public.atem_equiv_length_factors f
                WHERE f.component_type = 'Pipe'
                AND f.profile_id = NEW.profile_id
                LIMIT 1;

                -- And then update the subsequent code to use v_eq_length_factor instead of eq_length_factor:
                IF v_eq_length_factor IS NOT NULL THEN
                    eq_length := NEW.quantity * v_eq_length_factor;
                ELSE
                    eq_length := NEW.quantity; -- Default to 1:1 if no factor found
                END IF;
            ELSE
                -- Use lookup method (default) or direct quantity
                eq_length := NEW.quantity;
            END IF;
            
            -- Set the calculated values
            NEW.calculated_area := NEW.quantity * area;
            NEW.calculated_eq_length := eq_length;
            
            -- Skip the rest of the component type checks
            RETURN NEW;
        END IF;

        -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
        -- Check if this is a flange item that needs special calculation
        -- Using ILIKE for case-insensitive comparison
        -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
        -- Check if this is a flange item that needs special calculation
        -- Using ILIKE for case-insensitive comparison
        IF NEW.general_category ILIKE 'flanges' AND NEW.rfq_scope ILIKE 'Flanges' AND v_flange_method = 'detailed' THEN
            -- Extract flange type from fitting_category
            IF NEW.fitting_category ILIKE 'WN%' THEN
                v_flange_type := 'wn';
            ELSIF NEW.fitting_category ILIKE 'SO%' THEN
                v_flange_type := 'so';
            ELSIF NEW.fitting_category ILIKE 'THRD%' THEN
                v_flange_type := 'thrd';
            ELSIF NEW.fitting_category ILIKE 'LJ%' THEN
                v_flange_type := 'lj';
            ELSIF NEW.fitting_category ILIKE 'Blind%' THEN
                v_flange_type := 'blind';
            ELSE
                v_flange_type := 'wn'; -- Default to WN if specific type can't be determined
            END IF;
            
            -- Try to determine rating (lbs)
            v_lbs := 150; -- Default value
            IF NEW.rating IS NOT NULL THEN
                -- Try to extract a number from the rating, handling decimal points
                BEGIN
                    -- First, try to extract any number before a decimal point
                    v_lbs := SUBSTRING(NEW.rating FROM '^[0-9]+')::INTEGER;
                    
                    -- If that fails, try to extract the whole floating-point number and convert to integer
                    IF v_lbs IS NULL THEN
                        v_lbs := CAST(SUBSTRING(NEW.rating FROM '^[0-9]+\.[0-9]+') AS DECIMAL)::INTEGER;
                    END IF;
                    
                    -- If both attempts fail, use default
                    IF v_lbs IS NULL THEN
                        v_lbs := 150;
                    END IF;
                EXCEPTION WHEN OTHERS THEN
                    v_lbs := 150; -- Default on error
                END;
            END IF;
            
        -- Look up values based on flange type with improved size matching
            IF v_flange_type = 'wn' THEN
                -- First try exact match
                SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        weld_neck_length_ft, weld_neck_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for WN flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'so' OR v_flange_type = 'thrd' THEN
                -- First try exact match
                SELECT slip_on_thrd_length_ft, slip_on_thrd_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for SO/THRD flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'lj' THEN
                -- First try exact match
                SELECT lap_joint_length_ft, lap_joint_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        lap_joint_length_ft, lap_joint_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            lap_joint_length_ft, lap_joint_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for LJ flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'blind' THEN
                -- First try exact match
                SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        weld_neck_length_ft, weld_neck_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for Blind flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            END IF;
            
            -- If flange lookup successful, set values and return
            IF eq_length IS NOT NULL THEN
                NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * eq_length;
                NEW.calculated_area := COALESCE(NEW.quantity, 0) * area;
                RETURN NEW;
            END IF;
            
            -- If we get here, flange lookup failed - continue with standard calculation
            RAISE NOTICE 'Flange lookup failed for type % size % lbs %, falling back to standard method', 
                        v_flange_type, NEW.size1, v_lbs;
        END IF;


        -- IMPROVED LOOKUP LOGIC
        IF method = 'lookup' THEN
            -- Try fitting_category first for Fittings scope
            IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                v_lookup_category := NEW.fitting_category;
                
                -- Try exact match with fitting_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                -- Check if lookup succeeded
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using fitting_category=%', v_lookup_category;
                END IF;
            END IF;
            
            -- If fitting_category didn't find a match, fall back to general_category
            IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                v_lookup_category := NEW.general_category;
                
                -- Try exact match with general_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using general_category=%', v_lookup_category;
                END IF;
            END IF;
                
            -- If still no exact match, try alternative approaches
            IF NOT lookup_found AND (NEW.fitting_category IS NOT NULL OR NEW.general_category IS NOT NULL) THEN
                -- Try both categories for more complex matching
                IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                    v_lookup_category := NEW.fitting_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        v_smaller_size := LEAST(NEW.size1, NEW.size2);
                        
                        -- First, try to find a compound size that's close to our requested sizes
                        WITH size_options AS (
                            SELECT 
                                vf.size1, vf.size2, vf.length_ft, vf.area_ft,
                                ABS(vf.size1 - v_larger_size) + ABS(COALESCE(vf.size2, 0) - v_smaller_size) AS size_diff
                            FROM public.vw_fittings_lookup vf
                            WHERE vf.lookup_category = v_lookup_category
                            AND vf.profile = v_profile_name
                            AND vf.size2 IS NOT NULL  -- Only look at compound sizes
                        )
                        SELECT size1, size2, length_ft, area_ft INTO v_closest_size1, v_closest_size2, eq_length, area
                        FROM size_options
                        ORDER BY size_diff ASC
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found approximate compound size match: %x% for requested %x% using %',
                                v_closest_size1, v_closest_size2, v_larger_size, v_smaller_size, v_lookup_category;
                        ELSE
                            -- First try exact match with larger size
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            -- If no exact match, try approximate size matching for compound items
                            IF eq_length IS NULL THEN
                                -- First try to find the next larger size (rounding up)
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                -- If no larger size found, try the largest available size (rounding down)
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                -- If a closest size was found, get its values
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using fitting_category', 
                                            v_closest_size, NEW.size1, NEW.size2;
                                    END IF;
                                END IF;
                            END IF;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using fitting_category with compound size approximation';
                            END IF;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with fitting_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using fitting_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
                
                -- If fitting_category approaches failed, try general_category approaches
                IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                    v_lookup_category := NEW.general_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        v_smaller_size := LEAST(NEW.size1, NEW.size2);
                        
                        -- First, try to find a compound size that's close to our requested sizes
                        WITH size_options AS (
                            SELECT 
                                vf.size1, vf.size2, vf.length_ft, vf.area_ft,
                                ABS(vf.size1 - v_larger_size) + ABS(COALESCE(vf.size2, 0) - v_smaller_size) AS size_diff
                            FROM public.vw_fittings_lookup vf
                            WHERE vf.lookup_category = v_lookup_category
                            AND vf.profile = v_profile_name
                            AND vf.size2 IS NOT NULL  -- Only look at compound sizes
                        )
                        SELECT size1, size2, length_ft, area_ft INTO v_closest_size1, v_closest_size2, eq_length, area
                        FROM size_options
                        ORDER BY size_diff ASC
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found approximate compound size match: %x% for requested %x% using %', 
                                v_closest_size1, v_closest_size2, v_larger_size, v_smaller_size, v_lookup_category;
                        ELSE
                            -- If no compound sizes found, try with the larger size only
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using larger size % only for compound fitting %x% using %', 
                                    v_larger_size, v_larger_size, v_smaller_size, v_lookup_category;
                            ELSE
                                -- Continuing with previous logic for finding closest single size
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        lookup_found := TRUE;
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using %', 
                                            v_closest_size, NEW.size1, NEW.size2, v_lookup_category;
                                    END IF;
                                END IF;
                            END IF;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with general_category for single size
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL AND NEW.size2 IS NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using general_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END IF;

            -- Assign the calculated values (defaults to 0 if no match was found)
            -- MODIFIED: Multiply by quantity to get the total equivalent length and area
            NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * COALESCE(eq_length, 0);
            NEW.calculated_area := COALESCE(NEW.quantity, 0) * COALESCE(area, 0);

        -------------------------------------------------------------------    
        ELSIF method = 'factor' THEN
        lookup_found := FALSE;
        factor_value := 0; -- Use a different variable name to avoid ambiguity
        
        -- Try with general_category first
        IF NEW.general_category IS NOT NULL THEN
            SELECT ef.eq_length_factor INTO factor_value
            FROM public.atem_equiv_length_factors ef
            WHERE ef.component_type = NEW.general_category 
            AND ef.profile_id = NEW.profile_id
            LIMIT 1;
            
            IF factor_value IS NOT NULL THEN
                lookup_found := TRUE;
                RAISE NOTICE 'Found factor using general_category=%', NEW.general_category;
            END IF;
        END IF;
        
        -- If no factor found, try with fitting_category
        IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
            SELECT ef.eq_length_factor INTO factor_value
            FROM public.atem_equiv_length_factors ef
            WHERE ef.component_type = NEW.fitting_category
            AND ef.profile_id = NEW.profile_id
            LIMIT 1;
            
            IF factor_value IS NOT NULL THEN
                lookup_found := TRUE;
                RAISE NOTICE 'Found factor using fitting_category=%', NEW.fitting_category;
            END IF;
        END IF;
        
        -- Try with other category fields as a last resort
        IF NOT lookup_found THEN
            -- Try with valve_type
            IF NEW.valve_type IS NOT NULL THEN
                SELECT ef.eq_length_factor INTO factor_value
                FROM public.atem_equiv_length_factors ef
                WHERE ef.component_type = NEW.valve_type
                AND ef.profile_id = NEW.profile_id
                LIMIT 1;
                
                IF factor_value IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using valve_type=%', NEW.valve_type;
                END IF;
            END IF;
            
            -- Try with pipe_category
            IF NOT lookup_found AND NEW.pipe_category IS NOT NULL THEN
                SELECT ef.eq_length_factor INTO factor_value
                FROM public.atem_equiv_length_factors ef
                WHERE ef.component_type = NEW.pipe_category
                AND ef.profile_id = NEW.profile_id
                LIMIT 1;
                
                IF factor_value IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using pipe_category=%', NEW.pipe_category;
                END IF;
            END IF;
        END IF;

        -- Calculate with the factor if found, otherwise use 0
        NEW.calculated_eq_length := NEW.quantity * COALESCE(factor_value, 0);

        -- Calculate area based on equivalent length and pipe size
        IF NEW.size1 IS NOT NULL AND NEW.calculated_eq_length > 0 THEN
            -- Calculate pipe circumference (π × diameter)
            -- Convert size from inches to feet for area calculation
            NEW.calculated_area := 3.14159 * (NEW.size1/12) * NEW.calculated_eq_length;
        ELSE
            NEW.calculated_area := 0;
        END IF;
    END IF;

    -- Ensure defaults if no lookup values were found
    IF NEW.calculated_eq_length IS NULL THEN
        NEW.calculated_eq_length := 0;
    END IF;

    IF NEW.calculated_area IS NULL AND method != 'factor' THEN
        NEW.calculated_area := 0;
    END IF;

    RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create triggers for both tables using the new function
    CREATE OR REPLACE TRIGGER trg_update_component_calculations    
    BEFORE INSERT OR UPDATE OF size1, size2, schedule, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.atem_rfq
    FOR EACH ROW EXECUTE FUNCTION update_component_calculations();

    CREATE OR REPLACE TRIGGER trg_update_bom_component_calculations
    BEFORE INSERT OR UPDATE OF size1, size2, schedule, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.bom
    FOR EACH ROW EXECUTE FUNCTION update_component_calculations();

"""



#########################################################
### OLD LOGIC

FUNC_UPDATE_RFQ_CALCULATIONS_OLD_4_16_25 = """
    /* 
    * IMPROVED RFQ CALCULATION FUNCTION
    * APPLIED TO RFQ AND BOM TABLES
    * 
    * This function implements a robust calculation methodology for equivalent lengths
    * and surface areas in RFQ items. The implementation addresses several complex
    * requirements and edge cases:
    *
    * 1. CATEGORY LOOKUP LOGIC:
    *    - Uses fitting_category first for items with RFQ scope of "Fittings"
    *    - Falls back to general_category if fitting_category doesn't produce a match
    *    - Supports lookup of many types in both fitting_category and general_category
    *    - Includes safety checks to prevent errors with NULL values
    *
    * 2. MULTI-STEP SIZE MATCHING ALGORITHM:
    *    - Step 1: Tries for exact match first, including compound sizes in any order
    *    - Step 2: For compound fittings (with size1 and size2), uses an advanced closest-match algorithm:
    *        a. Calculates total difference between request sizes and available sizes
    *        b. Finds the compound size entry with minimal total difference
    *        c. Falls back to using larger size if no compound size matches are found
    *    - Step 3: Uses improved size approximation that:
    *        a. First tries to find the next larger size (MIN size that is ≥ requested size)
    *        b. If no larger size exists, falls back to the largest available size
    *
    * 3. COMPOUND SIZE HANDLING:
    *    - Handles size reversals (treats size1=4, size2=2 the same as size1=2, size2=4)
    *    - Explicitly identifies larger and smaller sizes for improved matching logic
    *    - First attempts to find a similar compound size using a difference-based algorithm
    *    - For unmatched compound items, uses the larger of the two sizes for single-size lookup
    *    - Maintains correct matching regardless of size order in lookup table
    *
    * 4. EDGE CASE HANDLING:
    *    - Always rounds up to the next available size when exact size not found
    *    - When search size is between available sizes (e.g., 1.5), finds next larger size (e.g., 2)
    *    - When search size is larger than all available sizes, uses largest available
    *    - When search size is smaller than all available sizes, uses smallest available
    *    - Returns zero for completely unmatched items
    *
    * 5. QUANTITY MULTIPLICATION:
    *    - Correctly applies quantity multiplier to both lookup and factor methods
    *    - Ensures calculated values reflect total quantities, not just per-unit values
    *
    * 6. FACTOR METHOD IMPROVEMENTS:
    *    - Tries lookup with general_category first
    *    - Falls back to fitting_category if needed
    *    - Also checks valve_type and pipe_category as additional fallbacks
    *    - Only sets calculated_area to NULL for factor method (area not calculated this way)
    *
    * The function works with two primary calculation methods:
    *   a) 'lookup': Uses predefined values from vw_fittings_lookup view
    *   b) 'factor': Uses multiplier factors from atem_equiv_length_factors table
    *
    * The database trigger watches for changes to key columns and recalculates values
    * automatically, ensuring data consistency throughout the application.
    */

    CREATE OR REPLACE FUNCTION update_component_calculations()
    RETURNS TRIGGER AS $$
    DECLARE
        eq_length DECIMAL(20,12);  -- eq_length: The equivalent length
        area DECIMAL(20,12);  -- area: The calculated area
        eq_length_factor DECIMAL(10,3);  -- eq_length_factor: The factor for equivalent length calculation
        method VARCHAR(50);  -- method: The calculation method (lookup or factor)
        v_profile_name VARCHAR(255);  -- v_profile_name: The profile name
        v_lookup_category VARCHAR(100);  -- v_lookup_category: The category for lookup
        v_closest_size DECIMAL(8,3);  -- v_closest_size: The closest size found
        v_closest_size1 DECIMAL(8,3);
        v_closest_size2 DECIMAL(8,3);
        v_larger_size DECIMAL(8,3);  -- v_larger_size: The larger size for compound fittings
        v_smaller_size DECIMAL(8,3); -- v_smaller_size: The smaller size for compound fittings
        v_flange_method VARCHAR(50);  -- v_flange_method: The flange calculation method
        v_flange_type VARCHAR(50);  -- v_flange_type: The derived flange type
        v_lbs INTEGER;  -- v_lbs: The pressure rating in lbs
        lookup_found BOOLEAN := FALSE;  -- Flag to track if a lookup was successful
        -- Debug variables
        v_debug_sql TEXT;
        v_available_sizes TEXT;
        factor_value numeric; -- Add this line to declare the new variable
    BEGIN
        -- Set default values in case no match is found
        eq_length := 0;
        area := 0;

        -- Skip if profile_id is null
        IF NEW.profile_id IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- Retrieve calculation method and profile name
        --SELECT equivalent_length_method, profile_name INTO method, v_profile_name
        -- FROM public.atem_client_profiles WHERE id = NEW.profile_id;

        -- Retrieve calculation method, profile name, and flange method
        SELECT equivalent_length_method, profile_name, flange_calculation_method 
        INTO method, v_profile_name, v_flange_method
        FROM public.atem_client_profiles WHERE id = NEW.profile_id;
        
        -- Skip if no method found, but ensure defaults
        IF method IS NULL THEN
            NEW.calculated_eq_length := 0;
            NEW.calculated_area := 0;
            RETURN NEW;
        END IF;

        -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
        -- Check if this is a flange item that needs special calculation
        -- Using ILIKE for case-insensitive comparison
        -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
        -- Check if this is a flange item that needs special calculation
        -- Using ILIKE for case-insensitive comparison
        IF NEW.general_category ILIKE 'flanges' AND NEW.rfq_scope ILIKE 'Flanges' AND v_flange_method = 'detailed' THEN
            -- Extract flange type from fitting_category
            IF NEW.fitting_category ILIKE 'WN%' THEN
                v_flange_type := 'wn';
            ELSIF NEW.fitting_category ILIKE 'SO%' THEN
                v_flange_type := 'so';
            ELSIF NEW.fitting_category ILIKE 'THRD%' THEN
                v_flange_type := 'thrd';
            ELSIF NEW.fitting_category ILIKE 'LJ%' THEN
                v_flange_type := 'lj';
            ELSIF NEW.fitting_category ILIKE 'Blind%' THEN
                v_flange_type := 'blind';
            ELSE
                v_flange_type := 'wn'; -- Default to WN if specific type can't be determined
            END IF;
            
            -- Try to determine rating (lbs)
            v_lbs := 150; -- Default value
            IF NEW.rating IS NOT NULL THEN
                -- Try to extract a number from the rating, handling decimal points
                BEGIN
                    -- First, try to extract any number before a decimal point
                    v_lbs := SUBSTRING(NEW.rating FROM '^[0-9]+')::INTEGER;
                    
                    -- If that fails, try to extract the whole floating-point number and convert to integer
                    IF v_lbs IS NULL THEN
                        v_lbs := CAST(SUBSTRING(NEW.rating FROM '^[0-9]+\.[0-9]+') AS DECIMAL)::INTEGER;
                    END IF;
                    
                    -- If both attempts fail, use default
                    IF v_lbs IS NULL THEN
                        v_lbs := 150;
                    END IF;
                EXCEPTION WHEN OTHERS THEN
                    v_lbs := 150; -- Default on error
                END;
            END IF;
            
        -- Look up values based on flange type with improved size matching
            IF v_flange_type = 'wn' THEN
                -- First try exact match
                SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        weld_neck_length_ft, weld_neck_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for WN flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'so' OR v_flange_type = 'thrd' THEN
                -- First try exact match
                SELECT slip_on_thrd_length_ft, slip_on_thrd_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for SO/THRD flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'lj' THEN
                -- First try exact match
                SELECT lap_joint_length_ft, lap_joint_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        lap_joint_length_ft, lap_joint_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            lap_joint_length_ft, lap_joint_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for LJ flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            ELSIF v_flange_type = 'blind' THEN
                -- First try exact match
                SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                FROM public.flange_data
                WHERE profile_id = NEW.profile_id
                AND size_in = NEW.size1
                AND lbs = v_lbs
                LIMIT 1;
                
                -- If no exact match, find the closest size
                IF eq_length IS NULL THEN
                    -- Try to find the next larger size (rounding up)
                    SELECT 
                        weld_neck_length_ft, weld_neck_area_ft,
                        size_in
                    INTO eq_length, area, v_closest_size
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in >= NEW.size1
                    AND lbs = v_lbs
                    ORDER BY size_in ASC
                    LIMIT 1;
                    
                    -- If no larger size found, try the largest available size (rounding down)
                    IF eq_length IS NULL THEN
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND lbs = v_lbs
                        ORDER BY size_in DESC
                        LIMIT 1;
                    END IF;
                    
                    IF eq_length IS NOT NULL THEN
                        RAISE NOTICE 'Using approximated size % for Blind flange (requested size: %)', 
                            v_closest_size, NEW.size1;
                    END IF;
                END IF;
            END IF;
            
            -- If flange lookup successful, set values and return
            IF eq_length IS NOT NULL THEN
                NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * eq_length;
                NEW.calculated_area := COALESCE(NEW.quantity, 0) * area;
                RETURN NEW;
            END IF;
            
            -- If we get here, flange lookup failed - continue with standard calculation
            RAISE NOTICE 'Flange lookup failed for type % size % lbs %, falling back to standard method', 
                        v_flange_type, NEW.size1, v_lbs;
        END IF;


        -- IMPROVED LOOKUP LOGIC
        IF method = 'lookup' THEN
            -- Try fitting_category first for Fittings scope
            IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                v_lookup_category := NEW.fitting_category;
                
                -- Try exact match with fitting_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                -- Check if lookup succeeded
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using fitting_category=%', v_lookup_category;
                END IF;
            END IF;
            
            -- If fitting_category didn't find a match, fall back to general_category
            IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                v_lookup_category := NEW.general_category;
                
                -- Try exact match with general_category
                SELECT length_ft, area_ft INTO eq_length, area
                FROM public.vw_fittings_lookup
                WHERE lookup_category = v_lookup_category
                AND profile = v_profile_name
                AND (
                    -- Single size match
                    (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                    OR
                    -- Compound size match with exact sizes in any order
                    (
                        (size1 = NEW.size1 AND size2 = NEW.size2)
                        OR
                        (size1 = NEW.size2 AND size2 = NEW.size1)
                    )
                )
                LIMIT 1;
                
                IF eq_length IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found match using general_category=%', v_lookup_category;
                END IF;
            END IF;
                
            -- If still no exact match, try alternative approaches
            IF NOT lookup_found AND (NEW.fitting_category IS NOT NULL OR NEW.general_category IS NOT NULL) THEN
                -- Try both categories for more complex matching
                IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                    v_lookup_category := NEW.fitting_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        v_smaller_size := LEAST(NEW.size1, NEW.size2);
                        
                        -- First, try to find a compound size that's close to our requested sizes
                        WITH size_options AS (
                            SELECT 
                                vf.size1, vf.size2, vf.length_ft, vf.area_ft,
                                ABS(vf.size1 - v_larger_size) + ABS(COALESCE(vf.size2, 0) - v_smaller_size) AS size_diff
                            FROM public.vw_fittings_lookup vf
                            WHERE vf.lookup_category = v_lookup_category
                            AND vf.profile = v_profile_name
                            AND vf.size2 IS NOT NULL  -- Only look at compound sizes
                        )
                        SELECT size1, size2, length_ft, area_ft INTO v_closest_size1, v_closest_size2, eq_length, area
                        FROM size_options
                        ORDER BY size_diff ASC
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found approximate compound size match: %x% for requested %x% using %',
                                v_closest_size1, v_closest_size2, v_larger_size, v_smaller_size, v_lookup_category;
                        ELSE
                            -- First try exact match with larger size
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            -- If no exact match, try approximate size matching for compound items
                            IF eq_length IS NULL THEN
                                -- First try to find the next larger size (rounding up)
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                -- If no larger size found, try the largest available size (rounding down)
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                -- If a closest size was found, get its values
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using fitting_category', 
                                            v_closest_size, NEW.size1, NEW.size2;
                                    END IF;
                                END IF;
                            END IF;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using fitting_category with compound size approximation';
                            END IF;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with fitting_category
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using fitting_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
                
                -- If fitting_category approaches failed, try general_category approaches
                IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                    v_lookup_category := NEW.general_category;
                    
                    -- For compound sizes in RFQ, use the larger size for lookup
                    IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                        v_larger_size := GREATEST(NEW.size1, NEW.size2);
                        v_smaller_size := LEAST(NEW.size1, NEW.size2);
                        
                        -- First, try to find a compound size that's close to our requested sizes
                        WITH size_options AS (
                            SELECT 
                                vf.size1, vf.size2, vf.length_ft, vf.area_ft,
                                ABS(vf.size1 - v_larger_size) + ABS(COALESCE(vf.size2, 0) - v_smaller_size) AS size_diff
                            FROM public.vw_fittings_lookup vf
                            WHERE vf.lookup_category = v_lookup_category
                            AND vf.profile = v_profile_name
                            AND vf.size2 IS NOT NULL  -- Only look at compound sizes
                        )
                        SELECT size1, size2, length_ft, area_ft INTO v_closest_size1, v_closest_size2, eq_length, area
                        FROM size_options
                        ORDER BY size_diff ASC
                        LIMIT 1;
                        
                        IF eq_length IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found approximate compound size match: %x% for requested %x% using %', 
                                v_closest_size1, v_closest_size2, v_larger_size, v_smaller_size, v_lookup_category;
                        ELSE
                            -- If no compound sizes found, try with the larger size only
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using larger size % only for compound fitting %x% using %', 
                                    v_larger_size, v_larger_size, v_smaller_size, v_lookup_category;
                            ELSE
                                -- Continuing with previous logic for finding closest single size
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        lookup_found := TRUE;
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using %', 
                                            v_closest_size, NEW.size1, NEW.size2, v_lookup_category;
                                    END IF;
                                END IF;
                            END IF;
                        END IF;
                    END IF;

                    -- If still no match, try size approximation with general_category for single size
                    IF NOT lookup_found AND NEW.size1 IS NOT NULL AND NEW.size2 IS NULL THEN
                        -- First try to find the next larger size (rounding up)
                        SELECT MIN(size1) INTO v_closest_size
                        FROM public.vw_fittings_lookup
                        WHERE lookup_category = v_lookup_category
                        AND profile = v_profile_name
                        AND size1 >= NEW.size1
                        AND size2 IS NULL;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF v_closest_size IS NULL THEN
                            SELECT MAX(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size2 IS NULL;
                        END IF;

                        -- If a closest size was found, get its values
                        IF v_closest_size IS NOT NULL THEN
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_closest_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found approximate match using general_category with closest size=%', v_closest_size;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END IF;

            -- Assign the calculated values (defaults to 0 if no match was found)
            -- MODIFIED: Multiply by quantity to get the total equivalent length and area
            NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * COALESCE(eq_length, 0);
            NEW.calculated_area := COALESCE(NEW.quantity, 0) * COALESCE(area, 0);
            
        -------------------------------------
        ELSIF method = 'factor' THEN
        lookup_found := FALSE;
        factor_value := 0; -- Use a different variable name to avoid ambiguity
        
        -- Try with general_category first
        IF NEW.general_category IS NOT NULL THEN
            SELECT ef.eq_length_factor INTO factor_value
            FROM public.atem_equiv_length_factors ef
            WHERE ef.component_type = NEW.general_category 
            AND ef.profile_id = NEW.profile_id
            LIMIT 1;
            
            IF factor_value IS NOT NULL THEN
                lookup_found := TRUE;
                RAISE NOTICE 'Found factor using general_category=%', NEW.general_category;
            END IF;
        END IF;
        
        -- If no factor found, try with fitting_category
        IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
            SELECT ef.eq_length_factor INTO factor_value
            FROM public.atem_equiv_length_factors ef
            WHERE ef.component_type = NEW.fitting_category
            AND ef.profile_id = NEW.profile_id
            LIMIT 1;
            
            IF factor_value IS NOT NULL THEN
                lookup_found := TRUE;
                RAISE NOTICE 'Found factor using fitting_category=%', NEW.fitting_category;
            END IF;
        END IF;
        
        -- Try with other category fields as a last resort
        IF NOT lookup_found THEN
            -- Try with valve_type
            IF NEW.valve_type IS NOT NULL THEN
                SELECT ef.eq_length_factor INTO factor_value
                FROM public.atem_equiv_length_factors ef
                WHERE ef.component_type = NEW.valve_type
                AND ef.profile_id = NEW.profile_id
                LIMIT 1;
                
                IF factor_value IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using valve_type=%', NEW.valve_type;
                END IF;
            END IF;
            
            -- Try with pipe_category
            IF NOT lookup_found AND NEW.pipe_category IS NOT NULL THEN
                SELECT ef.eq_length_factor INTO factor_value
                FROM public.atem_equiv_length_factors ef
                WHERE ef.component_type = NEW.pipe_category
                AND ef.profile_id = NEW.profile_id
                LIMIT 1;
                
                IF factor_value IS NOT NULL THEN
                    lookup_found := TRUE;
                    RAISE NOTICE 'Found factor using pipe_category=%', NEW.pipe_category;
                END IF;
            END IF;
        END IF;

        -- Calculate with the factor if found, otherwise use 0
        NEW.calculated_eq_length := NEW.quantity * COALESCE(factor_value, 0);
        NEW.calculated_area := NULL; -- Area not calculated with factor method
    END IF;

    -- Ensure defaults if no lookup values were found
    IF NEW.calculated_eq_length IS NULL THEN
        NEW.calculated_eq_length := 0;
    END IF;

    IF NEW.calculated_area IS NULL AND method != 'factor' THEN
        NEW.calculated_area := 0;
    END IF;

    RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create triggers for both tables using the new function
    CREATE TRIGGER trg_update_component_calculations    
    BEFORE INSERT OR UPDATE OF size1, size2, schedule, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.atem_rfq
    FOR EACH ROW EXECUTE FUNCTION update_component_calculations();

    CREATE TRIGGER trg_update_bom_component_calculations
    BEFORE INSERT OR UPDATE OF size1, size2, schedule, general_category, rfq_scope, 
                            fitting_category, valve_type, pipe_category, 
                            weld_category, quantity, profile_id
    ON public.bom
    FOR EACH ROW EXECUTE FUNCTION update_component_calculations();
"""

FUNC_UPDATE_RFQ_CALCULATIONS_OLD = """  -- Performs complex calculations for equivalent lengths and surface areas in RFQ items using lookup or factor methods
    /* 
    * IMPROVED RFQ CALCULATION FUNCTION
    * 
    * This function implements a robust calculation methodology for equivalent lengths
    * and surface areas in RFQ items. The implementation addresses several complex
    * requirements and edge cases:
    *
    * 1. CATEGORY LOOKUP LOGIC:
    *    - Uses fitting_category first for items with RFQ scope of "Fittings"
    *    - Falls back to general_category if fitting_category doesn't produce a match
    *    - Supports lookup of many types in both fitting_category and general_category
    *    - Includes safety checks to prevent errors with NULL values
    *
    * 2. MULTI-STEP SIZE MATCHING ALGORITHM:
    *    - Step 1: Tries for exact match first, including compound sizes in any order
    *    - Step 2: For compound fittings (with size1 and size2), falls back to larger size
    *    - Step 3: Uses improved size approximation that:
    *        a. First tries to find the next larger size (MIN size that is ≥ requested size)
    *        b. If no larger size exists, falls back to the largest available size
    *
    * 3. COMPOUND SIZE HANDLING:
    *    - Handles size reversals (treats size1=4, size2=2 the same as size1=2, size2=4)
    *    - For unmatched compound items, uses the larger of the two sizes for single-size lookup
    *    - Maintains correct matching regardless of size order in lookup table
    *
    * 4. EDGE CASE HANDLING:
    *    - Always rounds up to the next available size when exact size not found
    *    - When search size is between available sizes (e.g., 1.5), finds next larger size (e.g., 2)
    *    - When search size is larger than all available sizes, uses largest available
    *    - When search size is smaller than all available sizes, uses smallest available
    *    - Returns zero for completely unmatched items
    *
    * 5. QUANTITY MULTIPLICATION:
    *    - Correctly applies quantity multiplier to both lookup and factor methods
    *    - Ensures calculated values reflect total quantities, not just per-unit values
    *
    * 6. FACTOR METHOD IMPROVEMENTS:
    *    - Tries lookup with general_category first
    *    - Falls back to fitting_category if needed
    *    - Also checks valve_type and pipe_category as additional fallbacks
    *    - Only sets calculated_area to NULL for factor method (area not calculated this way)
    *
    * The function works with two primary calculation methods:
    *   a) 'lookup': Uses predefined values from vw_fittings_lookup view
    *   b) 'factor': Uses multiplier factors from atem_equiv_length_factors table
    *
    * The database trigger watches for changes to key columns and recalculates values
    * automatically, ensuring data consistency throughout the application.
    */
        

        CREATE OR REPLACE FUNCTION update_rfq_calculations()
        RETURNS TRIGGER AS $$
        DECLARE
            eq_length DECIMAL(20,12);  -- eq_length: The equivalent length
            area DECIMAL(20,12);  -- area: The calculated area
            eq_length_factor DECIMAL(10,3);  -- eq_length_factor: The factor for equivalent length calculation
            method VARCHAR(50);  -- method: The calculation method (lookup or factor)
            v_profile_name VARCHAR(255);  -- v_profile_name: The profile name
            v_lookup_category VARCHAR(100);  -- v_lookup_category: The category for lookup
            v_closest_size DECIMAL(8,3);  -- v_closest_size: The closest size found
            v_larger_size DECIMAL(8,3);  -- v_larger_size: The larger size for compound fittings
            v_flange_method VARCHAR(50);  -- v_flange_method: The flange calculation method
            v_flange_type VARCHAR(50);  -- v_flange_type: The derived flange type
            v_lbs INTEGER;  -- v_lbs: The pressure rating in lbs
            lookup_found BOOLEAN := FALSE;  -- Flag to track if a lookup was successful
        BEGIN
            -- Set default values in case no match is found
            eq_length := 0;
            area := 0;

            -- Skip if profile_id is null
            IF NEW.profile_id IS NULL THEN
                NEW.calculated_eq_length := 0;
                NEW.calculated_area := 0;
                RETURN NEW;
            END IF;

            -- Retrieve calculation method and profile name
            --SELECT equivalent_length_method, profile_name INTO method, v_profile_name
            -- FROM public.atem_client_profiles WHERE id = NEW.profile_id;

            -- Retrieve calculation method, profile name, and flange method
            SELECT equivalent_length_method, profile_name, flange_calculation_method 
            INTO method, v_profile_name, v_flange_method
            FROM public.atem_client_profiles WHERE id = NEW.profile_id;
            
            -- Skip if no method found, but ensure defaults
            IF method IS NULL THEN
                NEW.calculated_eq_length := 0;
                NEW.calculated_area := 0;
                RETURN NEW;
            END IF;

            -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
            -- Check if this is a flange item that needs special calculation
            -- Using ILIKE for case-insensitive comparison
            -- SPECIAL HANDLING FOR FLANGES - CORRECTED LOGIC
            -- Check if this is a flange item that needs special calculation
            -- Using ILIKE for case-insensitive comparison
            IF NEW.general_category ILIKE 'flanges' AND NEW.rfq_scope ILIKE 'Flanges' AND v_flange_method = 'detailed' THEN
                -- Extract flange type from fitting_category
                IF NEW.fitting_category ILIKE 'WN%' THEN
                    v_flange_type := 'wn';
                ELSIF NEW.fitting_category ILIKE 'SO%' THEN
                    v_flange_type := 'so';
                ELSIF NEW.fitting_category ILIKE 'THRD%' THEN
                    v_flange_type := 'thrd';
                ELSIF NEW.fitting_category ILIKE 'LJ%' THEN
                    v_flange_type := 'lj';
                ELSIF NEW.fitting_category ILIKE 'Blind%' THEN
                    v_flange_type := 'blind';
                ELSE
                    v_flange_type := 'wn'; -- Default to WN if specific type can't be determined
                END IF;
                
                -- Try to determine rating (lbs)
                v_lbs := 150; -- Default value
                IF NEW.rating IS NOT NULL THEN
                    -- Try to extract a number from the rating, handling decimal points
                    BEGIN
                        -- First, try to extract any number before a decimal point
                        v_lbs := SUBSTRING(NEW.rating FROM '^[0-9]+')::INTEGER;
                        
                        -- If that fails, try to extract the whole floating-point number and convert to integer
                        IF v_lbs IS NULL THEN
                            v_lbs := CAST(SUBSTRING(NEW.rating FROM '^[0-9]+\.[0-9]+') AS DECIMAL)::INTEGER;
                        END IF;
                        
                        -- If both attempts fail, use default
                        IF v_lbs IS NULL THEN
                            v_lbs := 150;
                        END IF;
                    EXCEPTION WHEN OTHERS THEN
                        v_lbs := 150; -- Default on error
                    END;
                END IF;
                
            -- Look up values based on flange type with improved size matching
                IF v_flange_type = 'wn' THEN
                    -- First try exact match
                    SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in = NEW.size1
                    AND lbs = v_lbs
                    LIMIT 1;
                    
                    -- If no exact match, find the closest size
                    IF eq_length IS NULL THEN
                        -- Try to find the next larger size (rounding up)
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND size_in >= NEW.size1
                        AND lbs = v_lbs
                        ORDER BY size_in ASC
                        LIMIT 1;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF eq_length IS NULL THEN
                            SELECT 
                                weld_neck_length_ft, weld_neck_area_ft,
                                size_in
                            INTO eq_length, area, v_closest_size
                            FROM public.flange_data
                            WHERE profile_id = NEW.profile_id
                            AND lbs = v_lbs
                            ORDER BY size_in DESC
                            LIMIT 1;
                        END IF;
                        
                        IF eq_length IS NOT NULL THEN
                            RAISE NOTICE 'Using approximated size % for WN flange (requested size: %)', 
                                v_closest_size, NEW.size1;
                        END IF;
                    END IF;
                ELSIF v_flange_type = 'so' OR v_flange_type = 'thrd' THEN
                    -- First try exact match
                    SELECT slip_on_thrd_length_ft, slip_on_thrd_area_ft INTO eq_length, area
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in = NEW.size1
                    AND lbs = v_lbs
                    LIMIT 1;
                    
                    -- If no exact match, find the closest size
                    IF eq_length IS NULL THEN
                        -- Try to find the next larger size (rounding up)
                        SELECT 
                            slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND size_in >= NEW.size1
                        AND lbs = v_lbs
                        ORDER BY size_in ASC
                        LIMIT 1;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF eq_length IS NULL THEN
                            SELECT 
                                slip_on_thrd_length_ft, slip_on_thrd_area_ft,
                                size_in
                            INTO eq_length, area, v_closest_size
                            FROM public.flange_data
                            WHERE profile_id = NEW.profile_id
                            AND lbs = v_lbs
                            ORDER BY size_in DESC
                            LIMIT 1;
                        END IF;
                        
                        IF eq_length IS NOT NULL THEN
                            RAISE NOTICE 'Using approximated size % for SO/THRD flange (requested size: %)', 
                                v_closest_size, NEW.size1;
                        END IF;
                    END IF;
                ELSIF v_flange_type = 'lj' THEN
                    -- First try exact match
                    SELECT lap_joint_length_ft, lap_joint_area_ft INTO eq_length, area
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in = NEW.size1
                    AND lbs = v_lbs
                    LIMIT 1;
                    
                    -- If no exact match, find the closest size
                    IF eq_length IS NULL THEN
                        -- Try to find the next larger size (rounding up)
                        SELECT 
                            lap_joint_length_ft, lap_joint_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND size_in >= NEW.size1
                        AND lbs = v_lbs
                        ORDER BY size_in ASC
                        LIMIT 1;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF eq_length IS NULL THEN
                            SELECT 
                                lap_joint_length_ft, lap_joint_area_ft,
                                size_in
                            INTO eq_length, area, v_closest_size
                            FROM public.flange_data
                            WHERE profile_id = NEW.profile_id
                            AND lbs = v_lbs
                            ORDER BY size_in DESC
                            LIMIT 1;
                        END IF;
                        
                        IF eq_length IS NOT NULL THEN
                            RAISE NOTICE 'Using approximated size % for LJ flange (requested size: %)', 
                                v_closest_size, NEW.size1;
                        END IF;
                    END IF;
                ELSIF v_flange_type = 'blind' THEN
                    -- First try exact match
                    SELECT weld_neck_length_ft, weld_neck_area_ft INTO eq_length, area
                    FROM public.flange_data
                    WHERE profile_id = NEW.profile_id
                    AND size_in = NEW.size1
                    AND lbs = v_lbs
                    LIMIT 1;
                    
                    -- If no exact match, find the closest size
                    IF eq_length IS NULL THEN
                        -- Try to find the next larger size (rounding up)
                        SELECT 
                            weld_neck_length_ft, weld_neck_area_ft,
                            size_in
                        INTO eq_length, area, v_closest_size
                        FROM public.flange_data
                        WHERE profile_id = NEW.profile_id
                        AND size_in >= NEW.size1
                        AND lbs = v_lbs
                        ORDER BY size_in ASC
                        LIMIT 1;
                        
                        -- If no larger size found, try the largest available size (rounding down)
                        IF eq_length IS NULL THEN
                            SELECT 
                                weld_neck_length_ft, weld_neck_area_ft,
                                size_in
                            INTO eq_length, area, v_closest_size
                            FROM public.flange_data
                            WHERE profile_id = NEW.profile_id
                            AND lbs = v_lbs
                            ORDER BY size_in DESC
                            LIMIT 1;
                        END IF;
                        
                        IF eq_length IS NOT NULL THEN
                            RAISE NOTICE 'Using approximated size % for Blind flange (requested size: %)', 
                                v_closest_size, NEW.size1;
                        END IF;
                    END IF;
                END IF;
                
                -- If flange lookup successful, set values and return
                IF eq_length IS NOT NULL THEN
                    NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * eq_length;
                    NEW.calculated_area := COALESCE(NEW.quantity, 0) * area;
                    RETURN NEW;
                END IF;
                
                -- If we get here, flange lookup failed - continue with standard calculation
                RAISE NOTICE 'Flange lookup failed for type % size % lbs %, falling back to standard method', 
                            v_flange_type, NEW.size1, v_lbs;
            END IF;


            -- IMPROVED LOOKUP LOGIC
            IF method = 'lookup' THEN
                -- Try fitting_category first for Fittings scope
                IF NEW.rfq_scope = 'Fittings' AND NEW.fitting_category IS NOT NULL THEN
                    v_lookup_category := NEW.fitting_category;
                    
                    -- Try exact match with fitting_category
                    SELECT length_ft, area_ft INTO eq_length, area
                    FROM public.vw_fittings_lookup
                    WHERE lookup_category = v_lookup_category
                    AND profile = v_profile_name
                    AND (
                        -- Single size match
                        (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                        OR
                        -- Compound size match with exact sizes in any order
                        (
                            (size1 = NEW.size1 AND size2 = NEW.size2)
                            OR
                            (size1 = NEW.size2 AND size2 = NEW.size1)
                        )
                    )
                    LIMIT 1;
                    
                    -- Check if lookup succeeded
                    IF eq_length IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found match using fitting_category=%', v_lookup_category;
                    END IF;
                END IF;
                
                -- If fitting_category didn't find a match, fall back to general_category
                IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                    v_lookup_category := NEW.general_category;
                    
                    -- Try exact match with general_category
                    SELECT length_ft, area_ft INTO eq_length, area
                    FROM public.vw_fittings_lookup
                    WHERE lookup_category = v_lookup_category
                    AND profile = v_profile_name
                    AND (
                        -- Single size match
                        (size1 = NEW.size1 AND size2 IS NULL AND NEW.size2 IS NULL)
                        OR
                        -- Compound size match with exact sizes in any order
                        (
                            (size1 = NEW.size1 AND size2 = NEW.size2)
                            OR
                            (size1 = NEW.size2 AND size2 = NEW.size1)
                        )
                    )
                    LIMIT 1;
                    
                    IF eq_length IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found match using general_category=%', v_lookup_category;
                    END IF;
                END IF;
                    
                -- If still no exact match, try alternative approaches
                IF NOT lookup_found AND (NEW.fitting_category IS NOT NULL OR NEW.general_category IS NOT NULL) THEN
                    -- Try both categories for more complex matching
                    IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                        v_lookup_category := NEW.fitting_category;
                        
                        -- For compound sizes in RFQ, use the larger size for lookup
                        IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                            v_larger_size := GREATEST(NEW.size1, NEW.size2);
                            
                            -- First try exact match with larger size
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            -- If no exact match, try approximate size matching for compound items
                            IF eq_length IS NULL THEN
                                -- First try to find the next larger size (rounding up)
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                -- If no larger size found, try the largest available size (rounding down)
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                -- If a closest size was found, get its values
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using fitting_category', 
                                            v_closest_size, NEW.size1, NEW.size2;
                                    END IF;
                                END IF;
                            END IF;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using fitting_category with compound size approximation';
                            END IF;
                        END IF;

                        -- If still no match, try size approximation with fitting_category
                        IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                            -- First try to find the next larger size (rounding up)
                            SELECT MIN(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 >= NEW.size1
                            AND size2 IS NULL;
                            
                            -- If no larger size found, try the largest available size (rounding down)
                            IF v_closest_size IS NULL THEN
                                SELECT MAX(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size2 IS NULL;
                            END IF;

                            -- If a closest size was found, get its values
                            IF v_closest_size IS NOT NULL THEN
                                SELECT length_ft, area_ft INTO eq_length, area
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 = v_closest_size
                                AND size2 IS NULL
                                LIMIT 1;
                                
                                IF eq_length IS NOT NULL THEN
                                    lookup_found := TRUE;
                                    RAISE NOTICE 'Found approximate match using fitting_category with closest size=%', v_closest_size;
                                END IF;
                            END IF;
                        END IF;
                    END IF;
                    
                    -- If fitting_category approaches failed, try general_category approaches
                    IF NOT lookup_found AND NEW.general_category IS NOT NULL THEN
                        v_lookup_category := NEW.general_category;
                        
                        -- For compound sizes in RFQ, use the larger size for lookup
                        IF NEW.size1 IS NOT NULL AND NEW.size2 IS NOT NULL THEN
                            v_larger_size := GREATEST(NEW.size1, NEW.size2);
                            
                            -- First try exact match with larger size
                            SELECT length_ft, area_ft INTO eq_length, area
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 = v_larger_size
                            AND size2 IS NULL
                            LIMIT 1;
                            
                            -- If no exact match, try approximate size matching for compound items
                            IF eq_length IS NULL THEN
                                -- First try to find the next larger size (rounding up)
                                SELECT MIN(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 >= v_larger_size
                                AND size2 IS NULL;
                                
                                -- If no larger size found, try the largest available size (rounding down)
                                IF v_closest_size IS NULL THEN
                                    SELECT MAX(size1) INTO v_closest_size
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size2 IS NULL;
                                END IF;
                                
                                -- If a closest size was found, get its values
                                IF v_closest_size IS NOT NULL THEN
                                    SELECT length_ft, area_ft INTO eq_length, area
                                    FROM public.vw_fittings_lookup
                                    WHERE lookup_category = v_lookup_category
                                    AND profile = v_profile_name
                                    AND size1 = v_closest_size
                                    AND size2 IS NULL
                                    LIMIT 1;
                                    
                                    IF eq_length IS NOT NULL THEN
                                        RAISE NOTICE 'Using approximate size % for compound sizes (%,%) using general_category', 
                                            v_closest_size, NEW.size1, NEW.size2;
                                    END IF;
                                END IF;
                            END IF;
                            
                            IF eq_length IS NOT NULL THEN
                                lookup_found := TRUE;
                                RAISE NOTICE 'Found match using general_category with compound size approximation';
                            END IF;
                        END IF;

                        -- If still no match, try size approximation with general_category
                        IF NOT lookup_found AND NEW.size1 IS NOT NULL THEN
                            -- First try to find the next larger size (rounding up)
                            SELECT MIN(size1) INTO v_closest_size
                            FROM public.vw_fittings_lookup
                            WHERE lookup_category = v_lookup_category
                            AND profile = v_profile_name
                            AND size1 >= NEW.size1
                            AND size2 IS NULL;
                            
                            -- If no larger size found, try the largest available size (rounding down)
                            IF v_closest_size IS NULL THEN
                                SELECT MAX(size1) INTO v_closest_size
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size2 IS NULL;
                            END IF;

                            -- If a closest size was found, get its values
                            IF v_closest_size IS NOT NULL THEN
                                SELECT length_ft, area_ft INTO eq_length, area
                                FROM public.vw_fittings_lookup
                                WHERE lookup_category = v_lookup_category
                                AND profile = v_profile_name
                                AND size1 = v_closest_size
                                AND size2 IS NULL
                                LIMIT 1;
                                
                                IF eq_length IS NOT NULL THEN
                                    lookup_found := TRUE;
                                    RAISE NOTICE 'Found approximate match using general_category with closest size=%', v_closest_size;
                                END IF;
                            END IF;
                        END IF;
                    END IF;
                END IF;

                -- Assign the calculated values (defaults to 0 if no match was found)
                -- MODIFIED: Multiply by quantity to get the total equivalent length and area
                NEW.calculated_eq_length := COALESCE(NEW.quantity, 0) * COALESCE(eq_length, 0);
                NEW.calculated_area := COALESCE(NEW.quantity, 0) * COALESCE(area, 0);
                
            ELSIF method = 'factor' THEN
                lookup_found := FALSE;
                
                -- Try with general_category first
                IF NEW.general_category IS NOT NULL THEN
                    SELECT eq_length_factor INTO eq_length_factor
                    FROM public.atem_equiv_length_factors
                    WHERE component_type = NEW.general_category 
                    AND profile_id = NEW.profile_id
                    LIMIT 1;
                    
                    IF eq_length_factor IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found factor using general_category=%', NEW.general_category;
                    END IF;
                END IF;
                
                -- If no factor found, try with fitting_category
                IF NOT lookup_found AND NEW.fitting_category IS NOT NULL THEN
                    SELECT eq_length_factor INTO eq_length_factor
                    FROM public.atem_equiv_length_factors
                    WHERE component_type = NEW.fitting_category
                    AND profile_id = NEW.profile_id
                    LIMIT 1;
                    
                    IF eq_length_factor IS NOT NULL THEN
                        lookup_found := TRUE;
                        RAISE NOTICE 'Found factor using fitting_category=%', NEW.fitting_category;
                    END IF;
                END IF;
                
                -- Try with other category fields as a last resort
                IF NOT lookup_found THEN
                    -- Try with valve_type
                    IF NEW.valve_type IS NOT NULL THEN
                        SELECT eq_length_factor INTO eq_length_factor
                        FROM public.atem_equiv_length_factors
                        WHERE component_type = NEW.valve_type
                        AND profile_id = NEW.profile_id
                        LIMIT 1;
                        
                        IF eq_length_factor IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found factor using valve_type=%', NEW.valve_type;
                        END IF;
                    END IF;
                    
                    -- Try with pipe_category
                    IF NOT lookup_found AND NEW.pipe_category IS NOT NULL THEN
                        SELECT eq_length_factor INTO eq_length_factor
                        FROM public.atem_equiv_length_factors
                        WHERE component_type = NEW.pipe_category
                        AND profile_id = NEW.profile_id
                        LIMIT 1;
                        
                        IF eq_length_factor IS NOT NULL THEN
                            lookup_found := TRUE;
                            RAISE NOTICE 'Found factor using pipe_category=%', NEW.pipe_category;
                            END IF;
                        END IF;
                    END IF;
                --END IF;

                -- Calculate with the factor if found, otherwise use 0
                NEW.calculated_eq_length := NEW.quantity * COALESCE(eq_length_factor, 0);
                NEW.calculated_area := NULL; -- Area not calculated with factor method
            END IF;
            
            -- Ensure defaults if no lookup values were found
            IF NEW.calculated_eq_length IS NULL THEN
                NEW.calculated_eq_length := 0;
            END IF;
            
            IF NEW.calculated_area IS NULL AND method != 'factor' THEN
                NEW.calculated_area := 0;
            END IF;

            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        -- Create the updated trigger with column-specific conditions
        DROP TRIGGER IF EXISTS trg_update_rfq_calculations ON public.atem_rfq;

        CREATE TRIGGER trg_update_rfq_calculations
        BEFORE INSERT OR UPDATE OF size1, size2, general_category, rfq_scope, 
                                fitting_category, valve_type, pipe_category, 
                                weld_category, quantity, profile_id
        ON public.atem_rfq
        FOR EACH ROW EXECUTE FUNCTION update_rfq_calculations();
"""