import os
import numpy as np
import cv2
import fitz  # PyMuPDF
import pandas as pd
from concurrent.futures import ProcessPoolExecutor
import matplotlib.pyplot as plt
from collections import defaultdict
import tempfile
import atexit
import logging
import hashlib
from pathlib import Path
import time

# Try to import WinSDK OCR if available (Windows-specific)
try:
    import winsdk.windows.media.ocr as ocr
    import winsdk.windows.graphics.imaging as imaging
    from winsdk.windows.storage import StorageFile
    import asyncio
    WINSDK_AVAILABLE = True
except ImportError:
    WINSDK_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# Standalone function for multiprocessing
def extract_page_features_for_mp(pdf_path, page_num, dpi, temp_dir, use_winsdk):
    """
    Standalone function for multiprocessing that extracts features from a single page.
    This function reopens the PDF in each process to avoid pickling issues.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process
        dpi: DPI for rendering
        temp_dir: Directory for temporary files
        use_winsdk: Whether to use WinSDK OCR

    Returns:
        Dictionary of features for the page
    """
    try:
        # Open the document in this process
        doc = fitz.open(pdf_path)
        page = doc[page_num]

        # Basic page properties
        features = {
            'page_num': page_num,
            'width': page.rect.width,
            'height': page.rect.height,
            'aspect_ratio': page.rect.width / page.rect.height
        }

        # Render the page to an image
        pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        # Convert from RGBA to RGB if needed
        if pix.n == 4:
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)

        # Save the image to a temporary file
        temp_img_path = os.path.join(temp_dir, f"page_{page_num}_{int(time.time())}.png")
        cv2.imwrite(temp_img_path, gray)

        # 1. Table structure features
        # Edge detection for structural elements
        edges = cv2.Canny(gray, 50, 150)

        # Line detection using Hough transform
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100,
                               minLineLength=100, maxLineGap=10)

        # Calculate horizontal and vertical line counts (for table detection)
        h_lines = 0
        v_lines = 0
        h_line_positions = []
        v_line_positions = []

        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

                if angle < 5 or angle > 175:  # Horizontal lines
                    h_lines += 1
                    h_line_positions.append((y1 + y2) / 2)  # Average y-position
                elif 85 < angle < 95:  # Vertical lines
                    v_lines += 1
                    v_line_positions.append((x1 + x2) / 2)  # Average x-position

        # Table features
        features['h_lines'] = h_lines
        features['v_lines'] = v_lines
        features['table_likelihood'] = min(h_lines, v_lines) / max(1, max(h_lines, v_lines))

        # Calculate table position (left, right, center)
        if v_line_positions:
            leftmost = min(v_line_positions) / img.shape[1]  # Normalize by page width
            rightmost = max(v_line_positions) / img.shape[1]
            table_center = (leftmost + rightmost) / 2

            features['table_left_edge'] = leftmost
            features['table_right_edge'] = rightmost
            features['table_center_x'] = table_center

            # Determine if table is on left, right, or center
            if table_center < 0.4:
                features['table_position'] = 0  # Left
            elif table_center > 0.6:
                features['table_position'] = 2  # Right
            else:
                features['table_position'] = 1  # Center
        else:
            features['table_left_edge'] = 0
            features['table_right_edge'] = 0
            features['table_center_x'] = 0.5
            features['table_position'] = 1  # Default to center

        # Estimate number of columns in table
        if v_line_positions:
            # Sort positions and calculate distances between adjacent lines
            v_line_positions.sort()
            distances = [v_line_positions[i+1] - v_line_positions[i]
                         for i in range(len(v_line_positions)-1)]

            # Group similar distances to identify column widths
            if distances:
                features['estimated_columns'] = len(distances) + 1
                features['avg_column_width'] = np.mean(distances) if distances else 0
                features['column_width_std'] = np.std(distances) if len(distances) > 1 else 0
            else:
                features['estimated_columns'] = 1
                features['avg_column_width'] = 0
                features['column_width_std'] = 0
        else:
            features['estimated_columns'] = 0
            features['avg_column_width'] = 0
            features['column_width_std'] = 0

        # Estimate number of rows in table
        if h_line_positions:
            # Sort positions and calculate distances between adjacent lines
            h_line_positions.sort()
            distances = [h_line_positions[i+1] - h_line_positions[i]
                         for i in range(len(h_line_positions)-1)]

            # Group similar distances to identify row heights
            if distances:
                features['estimated_rows'] = len(distances) + 1
                features['avg_row_height'] = np.mean(distances) if distances else 0
                features['row_height_std'] = np.std(distances) if len(distances) > 1 else 0
            else:
                features['estimated_rows'] = 1
                features['avg_row_height'] = 0
                features['row_height_std'] = 0
        else:
            features['estimated_rows'] = 0
            features['avg_row_height'] = 0
            features['row_height_std'] = 0

        # Compute perceptual hash for the table region
        # If we have table boundaries, crop to just the table area
        if v_line_positions and h_line_positions:
            left = max(0, int(min(v_line_positions) - 50))
            right = min(img.shape[1], int(max(v_line_positions) + 50))
            top = max(0, int(min(h_line_positions) - 50))
            bottom = min(img.shape[0], int(max(h_line_positions) + 50))

            # Crop to table region
            table_img = gray[top:bottom, left:right]
            if table_img.size > 0:  # Check if crop was successful
                # Resize to standard size for hashing
                table_img = cv2.resize(table_img, (32, 32))
                features['phash'] = _compute_phash_static(table_img)
            else:
                # Fallback to whole page if crop failed
                table_img = cv2.resize(gray, (32, 32))
                features['phash'] = _compute_phash_static(table_img)
        else:
            # Fallback to whole page if no table detected
            table_img = cv2.resize(gray, (32, 32))
            features['phash'] = _compute_phash_static(table_img)

        # Clean up
        try:
            os.unlink(temp_img_path)
        except:
            pass

        doc.close()
        return features

    except Exception as e:
        logger.error(f"Error extracting features from page {page_num}: {e}")
        return {'page_num': page_num, 'error': str(e)}


# Static version of the hash function for multiprocessing
def _compute_phash_static(img):
    """Compute a perceptual hash for an image."""
    # Compute DCT (Discrete Cosine Transform)
    dct = cv2.dct(np.float32(img))
    # Keep only the top-left 8x8 coefficients
    dct_low = dct[:8, :8]
    # Compute median value
    med = np.median(dct_low)
    # Create hash: 1 if value > median, 0 otherwise
    hash_bits = (dct_low > med).flatten()
    # Convert to hexadecimal string
    hash_hex = ''.join(['1' if b else '0' for b in hash_bits])
    hash_int = int(hash_hex, 2)
    return format(hash_int, 'x')


# Helper function to unpack arguments for multiprocessing
def unpack_and_process(args):
    """
    Helper function to unpack arguments and call extract_page_features_for_mp.
    This needs to be at the module level for pickling to work.
    """
    return extract_page_features_for_mp(*args)


class PageLayoutDetector:
    """
    Detects and groups similar page layouts in a PDF document.
    Uses multiple features for comparison: page size, visual features,
    structural elements, and text positioning.
    """

    def __init__(self, pdf_path, temp_dir=None, dpi=150, use_multiprocessing=True,
                 num_processes=None, use_winsdk=WINSDK_AVAILABLE):
        """
        Initialize the detector with a PDF file path.

        Args:
            pdf_path: Path to the PDF file
            temp_dir: Directory for temporary files (default: system temp)
            dpi: DPI for rendering pages (higher = more detail but slower)
            use_multiprocessing: Whether to use parallel processing
            num_processes: Number of processes to use (None = CPU count)
            use_winsdk: Whether to use WinSDK OCR when available
        """
        self.pdf_path = pdf_path
        self.temp_dir = temp_dir or tempfile.mkdtemp()
        self.dpi = dpi
        self.use_multiprocessing = use_multiprocessing
        self.num_processes = num_processes
        self.use_winsdk = use_winsdk and WINSDK_AVAILABLE
        self.doc = fitz.open(pdf_path)
        self.num_pages = len(self.doc)
        self.features = None
        self.clusters = None
        self.temp_files = []

        # Register cleanup for temporary files
        atexit.register(self._cleanup_temp_files)

    def _cleanup_temp_files(self):
        """Clean up any temporary files created during processing."""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {file_path}: {e}")

    def extract_all_features(self):
        """Extract features from all pages in the PDF."""
        logger.info(f"Extracting features from {self.num_pages} pages...")

        if self.use_multiprocessing and self.num_pages > 1:
            # Use parallel processing with a standalone function
            num_processes = self.num_processes or os.cpu_count()
            with ProcessPoolExecutor(max_workers=num_processes) as executor:
                # Create a list of arguments for each page
                args_list = [(self.pdf_path, i, self.dpi, self.temp_dir, self.use_winsdk)
                             for i in range(self.num_pages)]

                # Map the function to the arguments using the module-level helper
                all_features = list(executor.map(unpack_and_process, args_list))
        else:
            # Sequential processing
            all_features = [self.extract_page_features(i) for i in range(self.num_pages)]

        self.features = pd.DataFrame(all_features)
        logger.info(f"Feature extraction complete. Features shape: {self.features.shape}")
        return self.features

    def extract_page_features(self, page_num):
        """
        Extract multiple features from a single page.

        Args:
            page_num: Page number (0-based)

        Returns:
            Dictionary of features for the page
        """
        page = self.doc[page_num]
        features = {}

        # 1. Basic page properties
        features['page_num'] = page_num
        features['width'] = page.rect.width
        features['height'] = page.rect.height
        features['aspect_ratio'] = page.rect.width / page.rect.height

        # Create a temporary image file for this page
        temp_img_path = os.path.join(self.temp_dir, f"page_{page_num}.png")
        self.temp_files.append(temp_img_path)

        # Render the page to an image
        pix = page.get_pixmap(matrix=fitz.Matrix(self.dpi/72, self.dpi/72))
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n)

        # Convert from RGBA to RGB if needed
        if pix.n == 4:
            img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)

        # Save the image to a temporary file
        cv2.imwrite(temp_img_path, gray)

        # 1. Table structure features
        # Edge detection for structural elements
        edges = cv2.Canny(gray, 50, 150)

        # Line detection using Hough transform
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100,
                               minLineLength=100, maxLineGap=10)

        # Calculate horizontal and vertical line counts (for table detection)
        h_lines = 0
        v_lines = 0
        h_line_positions = []
        v_line_positions = []

        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

                if angle < 5 or angle > 175:  # Horizontal lines
                    h_lines += 1
                    h_line_positions.append((y1 + y2) / 2)  # Average y-position
                elif 85 < angle < 95:  # Vertical lines
                    v_lines += 1
                    v_line_positions.append((x1 + x2) / 2)  # Average x-position

        # Table features
        features['h_lines'] = h_lines
        features['v_lines'] = v_lines
        features['table_likelihood'] = min(h_lines, v_lines) / max(1, max(h_lines, v_lines))

        # Calculate table position (left, right, center)
        if v_line_positions:
            leftmost = min(v_line_positions) / img.shape[1]  # Normalize by page width
            rightmost = max(v_line_positions) / img.shape[1]
            table_center = (leftmost + rightmost) / 2

            features['table_left_edge'] = leftmost
            features['table_right_edge'] = rightmost
            features['table_center_x'] = table_center

            # Determine if table is on left, right, or center
            if table_center < 0.4:
                features['table_position'] = 0  # Left
            elif table_center > 0.6:
                features['table_position'] = 2  # Right
            else:
                features['table_position'] = 1  # Center
        else:
            features['table_left_edge'] = 0
            features['table_right_edge'] = 0
            features['table_center_x'] = 0.5
            features['table_position'] = 1  # Default to center

        # Estimate number of columns in table
        if v_line_positions:
            # Sort positions and calculate distances between adjacent lines
            v_line_positions.sort()
            distances = [v_line_positions[i+1] - v_line_positions[i]
                         for i in range(len(v_line_positions)-1)]

            # Group similar distances to identify column widths
            if distances:
                features['estimated_columns'] = len(distances) + 1
                features['avg_column_width'] = np.mean(distances) if distances else 0
                features['column_width_std'] = np.std(distances) if len(distances) > 1 else 0
            else:
                features['estimated_columns'] = 1
                features['avg_column_width'] = 0
                features['column_width_std'] = 0
        else:
            features['estimated_columns'] = 0
            features['avg_column_width'] = 0
            features['column_width_std'] = 0

        # Estimate number of rows in table
        if h_line_positions:
            # Sort positions and calculate distances between adjacent lines
            h_line_positions.sort()
            distances = [h_line_positions[i+1] - h_line_positions[i]
                         for i in range(len(h_line_positions)-1)]

            # Group similar distances to identify row heights
            if distances:
                features['estimated_rows'] = len(distances) + 1
                features['avg_row_height'] = np.mean(distances) if distances else 0
                features['row_height_std'] = np.std(distances) if len(distances) > 1 else 0
            else:
                features['estimated_rows'] = 1
                features['avg_row_height'] = 0
                features['row_height_std'] = 0
        else:
            features['estimated_rows'] = 0
            features['avg_row_height'] = 0
            features['row_height_std'] = 0

        # Compute perceptual hash for the table region
        # If we have table boundaries, crop to just the table area
        if v_line_positions and h_line_positions:
            left = max(0, int(min(v_line_positions) - 50))
            right = min(img.shape[1], int(max(v_line_positions) + 50))
            top = max(0, int(min(h_line_positions) - 50))
            bottom = min(img.shape[0], int(max(h_line_positions) + 50))

            # Crop to table region
            table_img = gray[top:bottom, left:right]
            if table_img.size > 0:  # Check if crop was successful
                # Resize to standard size for hashing
                table_img = cv2.resize(table_img, (32, 32))
                features['phash'] = _compute_phash_static(table_img)
            else:
                # Fallback to whole page if crop failed
                table_img = cv2.resize(gray, (32, 32))
                features['phash'] = _compute_phash_static(table_img)
        else:
            # Fallback to whole page if no table detected
            table_img = cv2.resize(gray, (32, 32))
            features['phash'] = _compute_phash_static(table_img)

        return features

    def cluster_pages(self, eps=0.5, min_samples=2, reorder=True):
        """
        Cluster pages based on a combination of table structure and whole-page features.
        Prioritizes table position, width, and column structure while also considering
        overall page characteristics.

        Args:
            eps: Distance threshold for considering two pages similar
            min_samples: Minimum number of pages to form a cluster
            reorder: Whether to reorder clusters by average page size with outliers last

        Returns:
            DataFrame with page numbers and their cluster assignments
        """
        if self.features is None:
            self.extract_all_features()

        # Select a combination of table-specific and whole-page features
        feature_cols = [
            # Table structure features
            'h_lines', 'v_lines', 'table_likelihood',
            'table_position', 'table_left_edge', 'table_right_edge',
            'estimated_columns', 'avg_column_width',

            # Whole page features
            'width', 'height', 'aspect_ratio'
        ]

        # Filter to only include features that exist in the DataFrame
        feature_cols = [col for col in feature_cols if col in self.features.columns]

        X = self.features[feature_cols].copy()

        # Normalize features
        for col in X.columns:
            if X[col].std() > 0:
                X[col] = (X[col] - X[col].mean()) / X[col].std()

        # Convert DataFrame to numpy array for faster processing
        X_array = X.values
        n_samples = X_array.shape[0]

        # Custom clustering algorithm (simplified DBSCAN-like approach)
        # Initialize all points as unclustered (-1)
        labels = np.full(n_samples, -1)
        cluster_id = 0

        # Calculate distance matrix with custom weighting for features
        distances = np.zeros((n_samples, n_samples))
        for i in range(n_samples):
            for j in range(i+1, n_samples):
                # Calculate weighted feature distances with custom weights
                feature_weights = np.ones(X_array.shape[1])

                # Apply weights to different feature types
                for k, col_name in enumerate(X.columns):
                    if col_name == 'width' or col_name == 'height':
                        # High weight for page dimensions
                        feature_weights[k] = 1.8
                    elif col_name == 'aspect_ratio':
                        # High weight for page orientation
                        feature_weights[k] = 1.5
                    elif 'position' in col_name or 'edge' in col_name:
                        # Higher weight for table position
                        feature_weights[k] = 1.6
                    elif 'column' in col_name:
                        # Higher weight for column structure
                        feature_weights[k] = 1.4
                    elif 'row' in col_name:
                        # Lower weight for row statistics
                        feature_weights[k] = 0.3

                # Normalize weights
                feature_weights = feature_weights / np.sum(feature_weights) * len(feature_weights)

                # Calculate weighted Euclidean distance
                feature_dist = np.sqrt(np.sum(feature_weights * (X_array[i] - X_array[j])**2))

                # Calculate hash distance
                hash1 = int(self.features.iloc[i]['phash'], 16)
                hash2 = int(self.features.iloc[j]['phash'], 16)
                hash_dist = bin(hash1 ^ hash2).count('1') / 64  # Normalize to [0,1]

                # Combined distance with balanced weights
                combined_dist = 0.85 * feature_dist + 0.15 * hash_dist
                distances[i, j] = combined_dist
                distances[j, i] = combined_dist

        # Find neighbors for each point
        neighbors = []
        for i in range(n_samples):
            neighbors.append(np.where(distances[i] < eps)[0])

        # Clustering process
        for i in range(n_samples):
            if labels[i] != -1:
                continue  # Already processed

            # Check if this point has enough neighbors to form a cluster
            if len(neighbors[i]) < min_samples:
                continue  # Not a core point

            # Start a new cluster
            labels[i] = cluster_id

            # Process neighbors
            seed_queue = list(neighbors[i])
            while seed_queue:
                current = seed_queue.pop(0)
                if labels[current] == -1:  # Unprocessed point
                    labels[current] = cluster_id

                    # If this is also a core point, add its neighbors to the queue
                    if len(neighbors[current]) >= min_samples:
                        for neighbor in neighbors[current]:
                            if labels[neighbor] == -1 and neighbor not in seed_queue:
                                seed_queue.append(neighbor)

            cluster_id += 1

        # Create result DataFrame
        result = pd.DataFrame({
            'page_num': self.features['page_num'],
            'width': self.features['width'],
            'height': self.features['height'],
            'cluster': labels
        })

        self.clusters = result
        logger.info(f"Hybrid clustering complete. Found {len(set(labels)) - (1 if -1 in labels else 0)} clusters.")

        # Reorder clusters if requested
        if reorder:
            self.reorder_clusters()

        return self.clusters

    def reorder_clusters(self):
        """
        Reorder cluster IDs by average page size (area), with outliers (-1) as the last group.

        Returns:
            DataFrame with reordered cluster assignments
        """
        if self.clusters is None:
            self.cluster_pages()

        # Create a copy of the clusters DataFrame to work with
        clusters_df = self.clusters.copy()

        # Calculate average page size (area) for each cluster
        cluster_sizes = clusters_df.groupby('cluster').apply(
            lambda x: (x['width'] * x['height']).mean()
        ).reset_index(name='avg_size')

        # Sort clusters by average size (descending)
        cluster_sizes = cluster_sizes.sort_values('avg_size', ascending=False)

        # Create a mapping from old cluster IDs to new ordered IDs
        # Move outliers (-1) to the end
        old_to_new = {}
        new_id = 0

        for old_id in cluster_sizes['cluster']:
            if old_id == -1:
                continue  # Skip outliers for now
            old_to_new[old_id] = new_id
            new_id += 1

        # Add outliers as the last group if they exist
        if -1 in cluster_sizes['cluster'].values:
            old_to_new[-1] = new_id

        # Apply the mapping to create new group IDs
        clusters_df['cluster'] = clusters_df['cluster'].map(old_to_new)

        # Update the clusters DataFrame
        self.clusters = clusters_df

        return self.clusters

    def save_clusters_to_excel(self, output_path=None):
        """
        Save clustering results to a simple Excel file with 1-based page numbers and their groups.
        Groups are reordered by average page size, with outliers (-1) as the last group.

        Args:
            output_path: Path to save the Excel file (default: same directory as PDF with _groups.xlsx suffix)

        Returns:
            Path to the saved Excel file
        """
        if self.clusters is None:
            self.cluster_pages()

        if output_path is None:
            output_path = os.path.splitext(self.pdf_path)[0] + "_groups.xlsx"

        # Create a copy of the clusters DataFrame to work with
        clusters_df = self.clusters.copy()

        # Calculate average page size (area) for each cluster
        cluster_sizes = clusters_df.groupby('cluster').apply(
            lambda x: (x['width'] * x['height']).mean()
        ).reset_index(name='avg_size')

        # Sort clusters by average size (descending)
        cluster_sizes = cluster_sizes.sort_values('avg_size', ascending=False)

        # Create a mapping from old cluster IDs to new ordered IDs
        # Move outliers (-1) to the end
        old_to_new = {}
        new_id = 0

        for old_id in cluster_sizes['cluster']:
            if old_id == -1:
                continue  # Skip outliers for now
            old_to_new[old_id] = new_id
            new_id += 1

        # Add outliers as the last group if they exist
        if -1 in cluster_sizes['cluster'].values:
            old_to_new[-1] = new_id

        # Apply the mapping to create new group IDs
        clusters_df['new_group'] = clusters_df['cluster'].map(old_to_new)

        # Create a simple DataFrame with 1-based page numbers and their reordered groups
        result_df = pd.DataFrame({
            'pdf_page': clusters_df['page_num'] + 1,  # Convert to 1-based
            'group': clusters_df['new_group']
        })

        # Sort by page number
        result_df = result_df.sort_values('pdf_page')

        # Save to Excel
        result_df.to_excel(output_path, index=False)
        logger.info(f"Saved clustering results to {output_path} with reordered groups")

        return output_path

    def visualize_clusters(self, output_dir=None, max_pages_per_cluster=5):
        """
        Visualize the clustering results by creating a grid of sample pages from each cluster.

        Args:
            output_dir: Directory to save visualization images
            max_pages_per_cluster: Maximum number of sample pages to show per cluster

        Returns:
            Dictionary mapping cluster IDs to output image paths
        """
        if self.clusters is None:
            self.cluster_pages()

        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(self.pdf_path), "cluster_viz")

        os.makedirs(output_dir, exist_ok=True)

        # Group pages by cluster
        cluster_groups = self.clusters.groupby('cluster')
        output_files = {}

        for cluster_id, group in cluster_groups:
            cluster_name = f"cluster_{cluster_id}" if cluster_id >= 0 else "outliers"
            logger.info(f"Visualizing {cluster_name} with {len(group)} pages")

            # Select sample pages (first few pages in the cluster)
            sample_pages = group.head(max_pages_per_cluster)
            n_samples = len(sample_pages)

            if n_samples == 0:
                continue

            # Create a grid for visualization
            n_cols = min(3, n_samples)
            n_rows = (n_samples + n_cols - 1) // n_cols

            plt.figure(figsize=(15, 5 * n_rows))

            for i, (_, row) in enumerate(sample_pages.iterrows()):
                page_num = int(row['page_num'])
                page = self.doc[page_num]

                # Render the page
                pix = page.get_pixmap(matrix=fitz.Matrix(0.5, 0.5))
                img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
                    pix.height, pix.width, pix.n)

                # Convert from RGBA to RGB if needed
                if pix.n == 4:
                    img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)

                # Add to the plot
                plt.subplot(n_rows, n_cols, i + 1)
                plt.imshow(img)
                plt.title(f"Page {page_num + 1}\n{row['width']}x{row['height']}")
                plt.axis('off')

            plt.tight_layout()

            # Save the visualization
            output_path = os.path.join(output_dir, f"{cluster_name}.png")
            plt.savefig(output_path, dpi=100)
            plt.close()

            output_files[cluster_id] = output_path

        return output_files

    def create_cluster_report(self, output_path=None):
        """
        Create a detailed report of the clustering results.

        Args:
            output_path: Path to save the Excel report

        Returns:
            Path to the saved report
        """
        if self.clusters is None:
            self.cluster_pages()

        if output_path is None:
            output_path = os.path.splitext(self.pdf_path)[0] + "_clusters.xlsx"

        # Create a more detailed DataFrame for the report
        report_data = self.clusters.copy()

        # Add page size category
        def page_size_category(w, h):
            area = w * h
            if area > 600000:  # Approximately A3 or larger
                return "Large"
            elif area > 400000:  # Approximately A4
                return "Medium"
            else:
                return "Small"

        report_data['size_category'] = report_data.apply(
            lambda row: page_size_category(row['width'], row['height']), axis=1)

        # Add aspect ratio category
        def aspect_ratio_category(w, h):
            ratio = w / h
            if ratio > 1.1:
                return "Landscape"
            elif ratio < 0.9:
                return "Portrait"
            else:
                return "Square"

        report_data['orientation'] = report_data.apply(
            lambda row: aspect_ratio_category(row['width'], row['height']), axis=1)

        # Add cluster size information
        cluster_sizes = report_data.groupby('cluster').size().reset_index(name='cluster_size')
        report_data = report_data.merge(cluster_sizes, on='cluster')

        # Add cluster homogeneity information (% of pages with same size category)
        def cluster_homogeneity(group):
            if len(group) <= 1:
                return 1.0

            # Size homogeneity
            size_counts = group['size_category'].value_counts()
            size_homogeneity = size_counts.max() / len(group)

            # Orientation homogeneity
            orientation_counts = group['orientation'].value_counts()
            orientation_homogeneity = orientation_counts.max() / len(group)

            return (size_homogeneity + orientation_homogeneity) / 2

        cluster_homogeneity_data = report_data.groupby('cluster').apply(
            cluster_homogeneity).reset_index(name='homogeneity')
        report_data = report_data.merge(cluster_homogeneity_data, on='cluster')

        # Save to Excel
        with pd.ExcelWriter(output_path) as writer:
            # Summary sheet
            summary = report_data.groupby('cluster').agg({
                'page_num': 'count',
                'width': 'mean',
                'height': 'mean',
                'size_category': lambda x: x.value_counts().index[0],
                'orientation': lambda x: x.value_counts().index[0],
                'homogeneity': 'first'
            }).reset_index()

            summary.columns = ['Cluster', 'Page Count', 'Avg Width', 'Avg Height',
                              'Dominant Size', 'Dominant Orientation', 'Homogeneity']
            summary.to_excel(writer, sheet_name='Summary', index=False)

            # Detailed sheet
            report_data.rename(columns={
                'page_num': 'Page Number',
                'width': 'Width',
                'height': 'Height',
                'cluster': 'Cluster',
                'size_category': 'Size Category',
                'orientation': 'Orientation',
                'cluster_size': 'Cluster Size',
                'homogeneity': 'Cluster Homogeneity'
            }).to_excel(writer, sheet_name='Details', index=False)

        logger.info(f"Cluster report saved to {output_path}")
        return output_path

    def close(self):
        """Close the PDF document and clean up resources."""
        self.doc.close()
        self._cleanup_temp_files()


def detect_page_groups(pdf_path, output_dir=None, eps=0.5, min_samples=2,
                       dpi=150, use_multiprocessing=True, visualize=True,
                       save_excel=True, grouping_strictness='medium'):
    """
    Detect groups of pages with similar layouts in a PDF document.

    Args:
        pdf_path: Path to the PDF file
        output_dir: Directory to save output files (default: same as PDF)
        eps: Distance threshold for considering two pages similar
        min_samples: Minimum number of pages to form a cluster
        dpi: DPI for rendering pages
        use_multiprocessing: Whether to use parallel processing
        visualize: Whether to create visualization images
        save_excel: Whether to save results to an Excel file
        grouping_strictness: How strict the grouping should be ('very_loose', 'loose', 'medium', 'strict', 'very_strict')
                             This affects the eps and min_samples parameters.

    Returns:
        Path to the cluster report Excel file
    """
    if output_dir is None:
        output_dir = os.path.dirname(pdf_path)

    # Adjust eps and min_samples based on grouping_strictness
    if grouping_strictness == 'very_loose':
        eps = 0.8  # Larger distance threshold = more pages considered similar
        min_samples = 2  # Minimum to form a group
    elif grouping_strictness == 'loose':
        eps = 0.65
        min_samples = 2
    elif grouping_strictness == 'medium':
        eps = 0.5  # Default
        min_samples = 2
    elif grouping_strictness == 'strict':
        eps = 0.35  # Smaller distance threshold = fewer pages considered similar
        min_samples = 2
    elif grouping_strictness == 'very_strict':
        eps = 0.25
        min_samples = 2
    else:
        # Use the provided eps and min_samples values
        pass

    detector = PageLayoutDetector(
        pdf_path=pdf_path,
        dpi=dpi,
        use_multiprocessing=use_multiprocessing
    )

    # Extract features and cluster pages
    detector.extract_all_features()
    clusters = detector.cluster_pages(eps=eps, min_samples=min_samples)

    # Create visualization if requested
    if visualize:
        viz_dir = os.path.join(output_dir, f"{Path(pdf_path).stem}_clusters")
        detector.visualize_clusters(output_dir=viz_dir)

    # Create and save detailed report
    report_path = os.path.join(output_dir, f"{Path(pdf_path).stem}_clusters.xlsx")
    detector.create_cluster_report(output_path=report_path)

    # Save simple Excel with just page numbers and groups
    if save_excel:
        simple_report_path = os.path.join(output_dir, f"{Path(pdf_path).stem}_groups.xlsx")
        detector.save_clusters_to_excel(output_path=simple_report_path)

    # Clean up
    detector.close()

    return report_path


if __name__ == "__main__":
    # Use direct variable assignments instead of argparse for easier testing

    # Required parameter
    pdf_path = r"C:\Drawings\Unclean Docs\Combined ISO Yates - Rotation Needed_rotated_pypdf.pdf"  # Replace with your PDF path

    # Optional parameters with defaults
    output_dir = None  # Default: same directory as PDF
    eps = 0.5  # Distance threshold for considering two pages similar
    min_samples = 2  # Minimum number of pages to form a cluster
    dpi = 150  # DPI for rendering pages
    use_multiprocessing = True  # Enable parallel processing
    visualize = True  # Create visualization images
    save_excel = True  # Save results to an Excel file
    grouping_strictness = 'loose'  # How strict the grouping should be

    # Run the detection
    report_path = detect_page_groups(
        pdf_path=pdf_path,
        output_dir=output_dir,
        eps=eps,
        min_samples=min_samples,
        dpi=dpi,
        use_multiprocessing=use_multiprocessing,
        visualize=visualize,
        save_excel=save_excel,
        grouping_strictness=grouping_strictness
    )

    print(f"Cluster report saved to: {report_path}")
    print(f"Simple groups saved to: {os.path.splitext(report_path)[0].replace('_clusters', '_groups')}.xlsx")
    print(f"Grouping strictness: {grouping_strictness} (eps={eps}, min_samples={min_samples})")
    print(f"Visualizations saved to: {os.path.dirname(report_path)}/{Path(pdf_path).stem}_clusters/")