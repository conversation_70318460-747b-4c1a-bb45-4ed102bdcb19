"""
===========================
Updater Steps
===========================
1. Download a remote 'latest release' file from server and save to a temporary file
2. This file should be 3 lines long
3. Read this file and parse the latest version, download link and BLAKE2 Hash
4. Now check this version against the internal app version. If we have latest, skip update
5. If app is not the latest, check if we have previously downloaded the setup installer
6. If there is a previous download, check the BLAKE2 hash of it and compare it with the 
   one from the release file. If the hash is the same, then the download is verified. We
   run the Setup but also quit ATEM app. The user should then be expected to run setup.
   After installing latest, user reopens app and can login now (Skips update at step 4)
7. If the download is not verified, e.g. broken download / invalid hash, we redownload the
   latest. Verify download and run installer (quit ATEM as in step 6)


===========================
Notes
===========================

1. Apparently BLAKE2 is more secure than MD5, SHA-1, etc. so we go with that and store 
   the Setup installer hash for verification
2. To recheck the BLAKE2 hash, rerun build_atem.py with the choice to check hash
3. We download the setup file to the same location and name e.g. atem-setup.exe. 
   See src/app_paths.py function `getSetupDownloadPath` for location
4. For development, we don't need to actually download/updates, so just print
   some prompts about checks/downloads which would be happening

===========================
Maintenance/Updates
===========================
1. Update the 'latest release' file on server e.g. https://atem-download-link/latest-release.txt
2. Stick to 3 line format e.g. see src/data/updater/latest-release.txt for example

<file START>
0.1                                           <--- Version number
https://atem-download-link/setup-0.1.exe      <--- Direct download link
qwhqe                                         <--- BLAKE2 Hash 
<END OF FILE>

"""

import requests
from PySide6.QtCore import QThread, Signal
from threading import Event
import time
import os
import hashlib
import tempfile
from src.utils.logger import logger
from src.app_paths import getSetupDownloadPath, getReleaseUrlPath
import __version__
import pkg_resources

# logger = logging.getLogger(__file__)


def getHashBlake2b(file):
    """Return the hash"""
    with open(file, "rb") as f:
        file_hash = hashlib.blake2b()
        while chunk := f.read(8192):
            file_hash.update(chunk)
    h = file_hash.hexdigest()
    print(file, h)
    return h


class Downloader(QThread):

    progressChanged = Signal(float)
    completed = Signal(bool)
    def __init__(self, src: str, dest: str) -> None:
        super().__init__()
        self._src = src
        self._dest = dest
        self.error = None

    def run(self):
        try:
            print(f"DL: src={self._src} to dest={self._dest}")
            with requests.get(self._src, stream=True) as r:
                with open(self._dest, 'wb') as file:
                    # Get the total size, in bytes, from the response header
                    total_size = int(r.headers.get('Content-Length'))
                    # Define the size of the chunk to iterate over (Mb)
                    chunk_size = 2000000
                    # iterate over every chunk and calculate % of total
                    for i, chunk in enumerate(r.iter_content(chunk_size=chunk_size)):
                        # calculate current percentage
                        c = i * chunk_size / total_size * 100
                        file.write(chunk)
                        progress = round(c, 4)
                        self.progressChanged.emit(progress)
                        time.sleep(0.1)
                    self.progressChanged.emit(100)

            self.completed.emit(True)
        except Exception as e:
            print(e)
            self.error = e
            self.completed.emit(False)


class AppUpdater(QThread):

    MAX_ATTEMPTS = 5
    updatedOk = Signal(bool)
    progressChanged = Signal(float)
    statusUpdate = Signal(str)
    startUpdate = Signal(str)
    def __init__(self, 
                 blocking: bool = True,
                 attempts: int = 5) -> None:
        super().__init__()
        self._setupInstallerPath = getSetupDownloadPath() # Constant
        self._releasePath = getReleaseUrlPath() # Constant
        self._attempts = attempts
        self.blocking = blocking
        self.latestVersion = None
        self.latestSrc = None
        self.latestHash = None
        self._ok: bool = False
        self._stop = Event() 

    def run(self):
        self.checkForUpdates()
        while not self._stop.is_set():
            time.sleep(1)
        print("Update check finished...")

    @property
    def ok(self):
        return self._ok

    @ok.setter
    def ok(self, value: bool):
        self._ok = value
        self.updatedOk.emit(self.ok)
        self._stop.set()

    def checkForUpdates(self):
        self.downloadLatestRelease()
        if self.ok:
            return

    def downloadLatestRelease(self):
        logger.debug("Checking latest release file...")

        with tempfile.TemporaryDirectory() as tmp:
            releaseFile = os.path.join(tmp, 'atem-latest-release.txt')
            # Download the release file
            if self._releasePath.startswith("https://"):
                self.releaseDownloader = Downloader(self._releasePath, releaseFile)
                self.releaseDownloader.start()
                self.releaseDownloader.wait()
                if self.releaseDownloader.error:
                    self.statusUpdate.emit(f"Failed to connect to server. Trying again...")
                    self._attempts += 1
                    if self._attempts >= self.MAX_ATTEMPTS:
                        self.ok = False
                        return
                    self.sleep(1)
                    self.downloadLatestRelease()
                    return
                    raise Exception("Failed to read latest release file")
            else:
                # Should be deprecated or strictly for testing
                print("Using local release file [testing only]")
                releaseFile = self._releasePath  # Local file for testing

            lines = []
            with open(releaseFile) as f:
                for line in f.readlines():
                    if not line:
                        continue
                    lines.append(line.rstrip())
            print(lines)

            assert len(lines) == 3, "Invalid release file format"
            self.latestVersion, self.latestSrc, self.latestHash = lines
            if self.newerVersionAvailable():
                self.downloadLatestSetup()
            else:
                self.ok = True # we have the latest

    def newerVersionAvailable(self) -> bool:
        assert self.latestVersion, "Invalid latest version"
        assert __version__.version, "Invalid app version"
        currentVersion = pkg_resources.parse_version(__version__.version)
        latestVersion = pkg_resources.parse_version(self.latestVersion)
        return (latestVersion > currentVersion)

    def downloadLatestSetup(self):
        assert self.latestSrc, "Invalid latest src"
        assert self._setupInstallerPath, "Invalid setup installer path"
        if not self.blocking:
            logger.debug(f"Installer exists and valid.... {self._setupInstallerPath}")
        elif not self.checkInstallerExists():
            logger.debug("Does not exist. Downloading latest....")
            self.downloader = Downloader(src=self.latestSrc, dest=self._setupInstallerPath)
            self.downloader.progressChanged.connect(self.onDownloadProgressChanged)
            self.downloader.completed.connect(self.onDownloadComplete)
            self.downloader.start()
            return

        # File exists
        self.onDownloadComplete()

    def checkInstallerExists(self) -> bool:
        """
        Check if installer filename exists, and same size and verified
        Same size check probably isn't necessary given hash check
        """
        assert self.latestSrc, "Invalid latest src"
        assert self.latestHash, "Invalid latest hash"
        if not self.blocking:
            logger.debug("Skipping checkInstallerExists...Forcing True")
            return True
        # print(self._setupInstallerPath)
        if os.path.exists(self._setupInstallerPath):
            local_total_size = os.path.getsize(self._setupInstallerPath)
            with requests.get(self.latestSrc, stream=True) as r:
                remote_total_size = int(r.headers.get('Content-Length'))
                local_hash = getHashBlake2b(self._setupInstallerPath)
                # print(local_hash, local_total_size, remote_total_size, self.latestHash)
                return ((local_total_size == remote_total_size)
                    and (local_hash == self.latestHash))
        else:
            return False

    def onDownloadComplete(self):
        """TODO - Download complete, now verify. Redownload if failed"""
        # Check verification again. Run if OK
        print("Setup download complete. Verifying...")
        if self.checkInstallerExists():
            self.runLatestSetup()
        else:
            self.ok = False

    def runLatestSetup(self):
        """Run setup and quit app simultaneously"""
        if not self.blocking:
            logger.debug("Running setup and quitting app... skipped)")
            self.ok = True
        else:
            logger.debug("Running setup and exiting")
            self.startUpdate.emit(self._setupInstallerPath)
            self.ok = True

    def onDownloadProgressChanged(self, value):
        self.progressChanged.emit(value)
