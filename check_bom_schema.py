import psycopg2
from src.atom.pg_database.pg_connection import DatabaseConfig

def check_bom_schema():
    # Create database configuration
    db_config = DatabaseConfig()
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_config.host,
        port=db_config.port,
        database=db_config.database,
        user=db_config.user,
        password=db_config.password
    )
    
    try:
        with conn.cursor() as cursor:
            # Get the column information for the bom table
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = 'bom'
                ORDER BY ordinal_position;
            """)
            
            columns = cursor.fetchall()
            
            print("BOM Table Schema:")
            print("=" * 80)
            print(f"{'Column Name':<30} {'Data Type':<20} {'Max Length':<10}")
            print("-" * 80)
            
            for col in columns:
                col_name, data_type, max_length = col
                max_length_str = str(max_length) if max_length is not None else "N/A"
                print(f"{col_name:<30} {data_type:<20} {max_length_str:<10}")
            
            # Specifically check the general_category column
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = 'bom' AND column_name = 'general_category';
            """)
            
            general_category_info = cursor.fetchone()
            
            if general_category_info:
                print("\nGeneral Category Column Details:")
                print("=" * 80)
                col_name, data_type, max_length = general_category_info
                max_length_str = str(max_length) if max_length is not None else "N/A"
                print(f"{col_name:<30} {data_type:<20} {max_length_str:<10}")
            else:
                print("\nGeneral Category column not found in the BOM table!")
                
    finally:
        conn.close()

if __name__ == "__main__":
    check_bom_schema()
