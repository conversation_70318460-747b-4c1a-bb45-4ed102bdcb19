import sys
from PySide6.QtWidgets import QApplication, QMainWindow

#pyside6-uic your_ui_file.ui -o output_python_file.py
#pyside6-uic "AIS_ATOM Layout1.ui" -o "C:\Users\<USER>\source\repos\Architekt_ATOM.py"

# Make sure to import your setup class here. Assuming the above code is in Architekt_ATOM.py
from Architekt_ATOM import Ui_MainWindow as Ui_MainWindow

class MainApplicationWindow(QMainWindow):
    def __init__(self, *args, **kwargs):
        super(MainApplicationWindow, self).__init__(*args, **kwargs)
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # Connect the button click signal to the slot that handles the page change
        self.ui.btn_new_project.clicked.connect(self.show_new_project_page)

    def show_new_project_page(self):
        # Change the current widget of the stackedWidget to 'page'
        self.ui.stackedWidget.setCurrentWidget(self.ui.page_2)

        

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Create an instance of the application window and show it
    mainWin = MainApplicationWindow()
    #mainWin.show()
    # Show it maximized
    mainWin.showMaximized()
    
    sys.exit(app.exec())

