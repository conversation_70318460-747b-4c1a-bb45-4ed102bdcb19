# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'AIS_ATOM Layout1.ui'
##
## Created by: Qt User Interface Compiler version 6.6.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QA<PERSON>, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QDockWidget, QFrame, QHBoxLayout,
    QHeaderView, QLabel, QLayout, QMainWindow,
    QMenu, QMenuBar, QSizePolicy, QSpacerItem,
    QStackedWidget, QStatusBar, QTabWidget, QToolButton,
    QTreeWidget, QTreeWidgetItem, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.setWindowModality(Qt.NonModal)
        MainWindow.resize(1286, 906)
        sizePolicy = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(1)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        font = QFont()
        font.setFamilies([u"Rockwell"])
        font.setPointSize(12)
        font.setBold(False)
        font.setItalic(False)
        MainWindow.setFont(font)
        MainWindow.setDocumentMode(False)
        MainWindow.setDockNestingEnabled(False)
        MainWindow.setUnifiedTitleAndToolBarOnMac(False)
        self.actionLight = QAction(MainWindow)
        self.actionLight.setObjectName(u"actionLight")
        self.actionDark = QAction(MainWindow)
        self.actionDark.setObjectName(u"actionDark")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        sizePolicy1.setHorizontalStretch(1)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.centralwidget.sizePolicy().hasHeightForWidth())
        self.centralwidget.setSizePolicy(sizePolicy1)
        self.verticalLayout_3 = QVBoxLayout(self.centralwidget)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.hboxTitle = QHBoxLayout()
        self.hboxTitle.setObjectName(u"hboxTitle")
        self.hboxTitle.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.label_title_info = QLabel(self.centralwidget)
        self.label_title_info.setObjectName(u"label_title_info")
        sizePolicy2 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy2.setHorizontalStretch(1)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.label_title_info.sizePolicy().hasHeightForWidth())
        self.label_title_info.setSizePolicy(sizePolicy2)
        font1 = QFont()
        font1.setFamilies([u"Rockwell"])
        font1.setPointSize(14)
        font1.setBold(False)
        font1.setItalic(False)
        self.label_title_info.setFont(font1)
        self.label_title_info.setAlignment(Qt.AlignCenter)

        self.hboxTitle.addWidget(self.label_title_info)

        self.hboxTitle.setStretch(0, 1)

        self.verticalLayout.addLayout(self.hboxTitle)

        self.hboxMainPanel = QHBoxLayout()
        self.hboxMainPanel.setSpacing(6)
        self.hboxMainPanel.setObjectName(u"hboxMainPanel")
        self.hboxMainPanel.setSizeConstraint(QLayout.SetDefaultConstraint)
        

        self.stackedWidget = QStackedWidget(self.centralwidget)
        self.stackedWidget.setObjectName(u"stackedWidget")
        sizePolicy1.setHeightForWidth(self.stackedWidget.sizePolicy().hasHeightForWidth())
        self.stackedWidget.setSizePolicy(sizePolicy1)
        

        self.page = QWidget()
        self.page.setObjectName(u"page")
        sizePolicy1.setHeightForWidth(self.page.sizePolicy().hasHeightForWidth())
        self.page.setSizePolicy(sizePolicy1)
        
        self.tabWidget = QTabWidget(self.page)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tabWidget.setGeometry(QRect(10, 20, 1201, 771))
        sizePolicy1.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy1)
        self.tabWidget.setAutoFillBackground(False)
        self.tabWidget.setTabPosition(QTabWidget.South)
        self.tabWidget.setDocumentMode(False)
        self.tabWidget.setTabBarAutoHide(False)

        ##########################################
        ##########################################
        ##########################################
        self.page.setLayout(QVBoxLayout())  # Assign a QVBoxLayout to page
        self.page.layout().addWidget(self.tabWidget)  # Add the tabWidget to page's layout
         ##########################################
        ##########################################
        ##########################################
               
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        sizePolicy1.setHeightForWidth(self.tab.sizePolicy().hasHeightForWidth())
        self.tab.setSizePolicy(sizePolicy1)
        self.tabWidget.addTab(self.tab, "")
        
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        sizePolicy1.setHeightForWidth(self.tab_2.sizePolicy().hasHeightForWidth())
        self.tab_2.setSizePolicy(sizePolicy1)
        self.tabWidget.addTab(self.tab_2, "")
        
        self.stackedWidget.addWidget(self.page)
        
        self.page_2 = QWidget()
        self.page_2.setObjectName(u"page_2")
        self.page_2.setEnabled(True)
        sizePolicy.setHeightForWidth(self.page_2.sizePolicy().hasHeightForWidth())
        self.page_2.setSizePolicy(sizePolicy)
        
        self.horizontalLayoutWidget_3 = QWidget(self.page_2)
        self.horizontalLayoutWidget_3.setObjectName(u"horizontalLayoutWidget_3")
        self.horizontalLayoutWidget_3.setGeometry(QRect(0, 40, 1211, 751))
        self.horizontalLayout = QHBoxLayout(self.horizontalLayoutWidget_3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        
        ##########################################
        ##########################################
        ##########################################

        # Do similar for page_2
        self.page_2.setLayout(QVBoxLayout())  # Assign a QVBoxLayout to page_2
        self.page_2.layout().addWidget(self.horizontalLayoutWidget_3)  # Assuming you want to add the entire horizontal layout widget

        ##########################################
        ##########################################
        ##########################################


        self.label_pdf_viewer = QLabel(self.horizontalLayoutWidget_3)
        self.label_pdf_viewer.setObjectName(u"label_pdf_viewer")
        sizePolicy1.setHeightForWidth(self.label_pdf_viewer.sizePolicy().hasHeightForWidth())
        self.label_pdf_viewer.setSizePolicy(sizePolicy1)
        self.label_pdf_viewer.setScaledContents(False)
        self.label_pdf_viewer.setAlignment(Qt.AlignCenter)

        self.horizontalLayout.addWidget(self.label_pdf_viewer)

        self.horizontalLayoutWidget_4 = QWidget(self.page_2)
        self.horizontalLayoutWidget_4.setObjectName(u"horizontalLayoutWidget_4")
        self.horizontalLayoutWidget_4.setGeometry(QRect(-1, 0, 1211, 41))
        self.horizontalLayout_4 = QHBoxLayout(self.horizontalLayoutWidget_4)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer)

        self.btn_load_pdf = QToolButton(self.horizontalLayoutWidget_4)
        self.btn_load_pdf.setObjectName(u"btn_load_pdf")
        sizePolicy3 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.btn_load_pdf.sizePolicy().hasHeightForWidth())
        self.btn_load_pdf.setSizePolicy(sizePolicy3)
        self.btn_load_pdf.setLayoutDirection(Qt.LeftToRight)
        self.btn_load_pdf.setAutoRaise(False)

        self.horizontalLayout_4.addWidget(self.btn_load_pdf)

        self.horizontalLayout_4.setStretch(0, 8)
        self.horizontalLayout_4.setStretch(1, 1)
        self.stackedWidget.addWidget(self.page_2)

        self.hboxMainPanel.addWidget(self.stackedWidget)


        self.verticalLayout.addLayout(self.hboxMainPanel)


        self.verticalLayout_3.addLayout(self.verticalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QMenuBar(MainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 1286, 25))
        self.menuFile = QMenu(self.menubar)
        self.menuFile.setObjectName(u"menuFile")
        self.menuOptions = QMenu(self.menubar)
        self.menuOptions.setObjectName(u"menuOptions")
        self.menuDisplay = QMenu(self.menuOptions)
        self.menuDisplay.setObjectName(u"menuDisplay")
        self.menuMode = QMenu(self.menuDisplay)
        self.menuMode.setObjectName(u"menuMode")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)
        
        #Set the dock widget
        self.dockWidget = QDockWidget(MainWindow)
        self.dockWidget.setObjectName(u"dockWidget")
        sizePolicy4 = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Maximum)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.dockWidget.sizePolicy().hasHeightForWidth())
        self.dockWidget.setSizePolicy(sizePolicy4)
        self.dockWidget.setAutoFillBackground(False)
        self.dockWidget.setFeatures(QDockWidget.DockWidgetFeatureMask)
        self.dockWidget.setAllowedAreas(Qt.NoDockWidgetArea)
        self.dockWidgetContents = QWidget()
        self.dockWidgetContents.setObjectName(u"dockWidgetContents")
        
        self.verticalLayoutWidget = QWidget(self.dockWidgetContents)
        self.verticalLayoutWidget.setObjectName(u"verticalLayoutWidget")
        self.verticalLayoutWidget.setGeometry(QRect(10, 0, 190, 631))
        self.verticalLayout_2 = QVBoxLayout(self.verticalLayoutWidget)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.label_ais_logo = QLabel(self.verticalLayoutWidget)
        self.label_ais_logo.setObjectName(u"label_ais_logo")

        self.verticalLayout_2.addWidget(self.label_ais_logo)

        self.label_ais_head = QLabel(self.verticalLayoutWidget)
        self.label_ais_head.setObjectName(u"label_ais_head")

        self.verticalLayout_2.addWidget(self.label_ais_head)

        self.btn_open_dashboard = QToolButton(self.verticalLayoutWidget)
        self.btn_open_dashboard.setObjectName(u"btn_open_dashboard")

        self.verticalLayout_2.addWidget(self.btn_open_dashboard)

        self.btn_open_notepad = QToolButton(self.verticalLayoutWidget)
        self.btn_open_notepad.setObjectName(u"btn_open_notepad")

        self.verticalLayout_2.addWidget(self.btn_open_notepad)

        self.btn_open_calculator = QToolButton(self.verticalLayoutWidget)
        self.btn_open_calculator.setObjectName(u"btn_open_calculator")

        self.verticalLayout_2.addWidget(self.btn_open_calculator)

        self.btn_new_project = QToolButton(self.verticalLayoutWidget)
        self.btn_new_project.setObjectName(u"btn_new_project")

        self.verticalLayout_2.addWidget(self.btn_new_project)

        self.tree_projects = QTreeWidget(self.verticalLayoutWidget)
        __qtreewidgetitem = QTreeWidgetItem(self.tree_projects)
        QTreeWidgetItem(__qtreewidgetitem)
        QTreeWidgetItem(__qtreewidgetitem)
        __qtreewidgetitem1 = QTreeWidgetItem(self.tree_projects)
        QTreeWidgetItem(__qtreewidgetitem1)
        QTreeWidgetItem(__qtreewidgetitem1)
        self.tree_projects.setObjectName(u"tree_projects")
        self.tree_projects.setSortingEnabled(True)
        self.tree_projects.setWordWrap(True)
        self.tree_projects.header().setHighlightSections(False)

        self.verticalLayout_2.addWidget(self.tree_projects)

        self.btn_open_settings = QToolButton(self.verticalLayoutWidget)
        self.btn_open_settings.setObjectName(u"btn_open_settings")

        self.verticalLayout_2.addWidget(self.btn_open_settings)

        self.btn_my_account = QToolButton(self.verticalLayoutWidget)
        self.btn_my_account.setObjectName(u"btn_my_account")

        self.verticalLayout_2.addWidget(self.btn_my_account)

        self.btn_contact = QToolButton(self.verticalLayoutWidget)
        self.btn_contact.setObjectName(u"btn_contact")

        self.verticalLayout_2.addWidget(self.btn_contact)

        self.btn_products = QToolButton(self.verticalLayoutWidget)
        self.btn_products.setObjectName(u"btn_products")

        self.verticalLayout_2.addWidget(self.btn_products)

        self.line = QFrame(self.verticalLayoutWidget)
        self.line.setObjectName(u"line")
        self.line.setFrameShape(QFrame.HLine)
        self.line.setFrameShadow(QFrame.Sunken)

        self.verticalLayout_2.addWidget(self.line)

        self.dockWidget.setWidget(self.dockWidgetContents)
        MainWindow.addDockWidget(Qt.LeftDockWidgetArea, self.dockWidget)

        self.menubar.addAction(self.menuFile.menuAction())
        self.menubar.addAction(self.menuOptions.menuAction())
        self.menuOptions.addAction(self.menuDisplay.menuAction())
        self.menuDisplay.addSeparator()
        self.menuDisplay.addAction(self.menuMode.menuAction())
        self.menuMode.addSeparator()
        self.menuMode.addAction(self.actionLight)
        self.menuMode.addAction(self.actionDark)

        self.retranslateUi(MainWindow)

        self.stackedWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"ATOM", None))
        self.actionLight.setText(QCoreApplication.translate("MainWindow", u"Light", None))
        self.actionDark.setText(QCoreApplication.translate("MainWindow", u"Dark", None))
        self.label_title_info.setText(QCoreApplication.translate("MainWindow", u"Title: Page Label", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"Tab 1", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), QCoreApplication.translate("MainWindow", u"Tab 2", None))
        self.label_pdf_viewer.setText(QCoreApplication.translate("MainWindow", u"PDF Viewer", None))
        self.btn_load_pdf.setText(QCoreApplication.translate("MainWindow", u"Open Document", None))
        self.menuFile.setTitle(QCoreApplication.translate("MainWindow", u"File", None))
        self.menuOptions.setTitle(QCoreApplication.translate("MainWindow", u"Options", None))
        self.menuDisplay.setTitle(QCoreApplication.translate("MainWindow", u"Display", None))
        self.menuMode.setTitle(QCoreApplication.translate("MainWindow", u"Mode", None))
        self.dockWidget.setWindowTitle(QCoreApplication.translate("MainWindow", u"Navigation", None))
        self.label_ais_logo.setText(QCoreApplication.translate("MainWindow", u"[Logo.svg]", None))
        self.label_ais_head.setText(QCoreApplication.translate("MainWindow", u"Architekt IS", None))
        self.btn_open_dashboard.setText(QCoreApplication.translate("MainWindow", u"Dashboard", None))
        self.btn_open_notepad.setText(QCoreApplication.translate("MainWindow", u"NotePad", None))
        self.btn_open_calculator.setText(QCoreApplication.translate("MainWindow", u"Construction Calculator", None))
        self.btn_new_project.setText(QCoreApplication.translate("MainWindow", u"+ New Project", None))
        ___qtreewidgetitem = self.tree_projects.headerItem()
        ___qtreewidgetitem.setText(0, QCoreApplication.translate("MainWindow", u"Projects", None));

        __sortingEnabled = self.tree_projects.isSortingEnabled()
        self.tree_projects.setSortingEnabled(False)
        ___qtreewidgetitem1 = self.tree_projects.topLevelItem(0)
        ___qtreewidgetitem1.setText(0, QCoreApplication.translate("MainWindow", u"Project 2", None));
        ___qtreewidgetitem2 = ___qtreewidgetitem1.child(0)
        ___qtreewidgetitem2.setText(0, QCoreApplication.translate("MainWindow", u"MTO", None));
        ___qtreewidgetitem3 = ___qtreewidgetitem1.child(1)
        ___qtreewidgetitem3.setText(0, QCoreApplication.translate("MainWindow", u"Documents", None));
        ___qtreewidgetitem4 = self.tree_projects.topLevelItem(1)
        ___qtreewidgetitem4.setText(0, QCoreApplication.translate("MainWindow", u"Project 1", None));
        ___qtreewidgetitem5 = ___qtreewidgetitem4.child(0)
        ___qtreewidgetitem5.setText(0, QCoreApplication.translate("MainWindow", u"MTO", None));
        ___qtreewidgetitem6 = ___qtreewidgetitem4.child(1)
        ___qtreewidgetitem6.setText(0, QCoreApplication.translate("MainWindow", u"Documents", None));
        self.tree_projects.setSortingEnabled(__sortingEnabled)

        self.btn_open_settings.setText(QCoreApplication.translate("MainWindow", u"Settings", None))
        self.btn_my_account.setText(QCoreApplication.translate("MainWindow", u"My Account", None))
        self.btn_contact.setText(QCoreApplication.translate("MainWindow", u"Contact", None))
        self.btn_products.setText(QCoreApplication.translate("MainWindow", u"More Products", None))
    # retranslateUi

