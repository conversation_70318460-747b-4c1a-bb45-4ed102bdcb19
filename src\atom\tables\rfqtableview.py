"""
RFQ Table

RFQ records can be sent to MTO Assistatnt for classification.

Records without either general_category and rfq_scope (Takeoff Category) specified are considered unclassified.
"""
import time
from src.utils.logger import logger
import math
import uuid

import pandas as pd
from pubsub import pub
import numpy as np

from PySide6.QtWidgets import *
from PySide6.QtCore import *

from data_conversions import process_size_column
from src.app_paths import getSaved<PERSON><PERSON>Map<PERSON>son
from src.atom.dbManager import DatabaseManager
from src.views.tableresultsview import TableResultsViewBase
from src.views.tables.lookuptable import LookupTable
from src.widgets.groupedtableview import GroupedTableFlags
from src.atom.merge_rfq_into_bom import merge_rfq_into_bom
from src.atom.ef_lookup import update_bom_ef_lookup
from src.atom.dbManager import DatabaseManager

from collections import defaultdict

# logger = logging.getLogger(__file__)

#############################################
#############################################
# Temporarily set the logger level to INFO
# logger.setLevel(logging.INFO)

# Create a console handler (if you haven't already)
# console_handler = logging.StreamHandler()

# Optionally, set a formatter
# formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
# console_handler.setFormatter(formatter)

# Add the handler to the logger
# logger.addHandler(console_handler)

#############################################
#############################################

show_grouped_fields = ['id', 'quantity', 'size', 'material_description', 'general_category',
    'shop/field', 'rfq_scope', 'unit', 'size1', 'size2', 'schedule', 'rating',
    'astm', 'grade', 'asme/_ansi', 'material', 'coating', 'forging', 'ends',
    'weld_type', 'exclude_from_rfq']


class BuildRfqWorker(QObject):
    """
    apply - Is true when the data received is from MTO classification
    """

    finished = Signal()

    def __init__(self, rfqData: pd.DataFrame, rfqFields, apply: bool = False):
        super().__init__()
        self._stop_flag = []
        self.error = None
        self.result = None
        self.rfqData = rfqData
        self.rfqFields = rfqFields
        self.apply = apply

    def run(self):
        # --> Process 'size', 'size1', 'size2' 'quantity'
        # Create a temporary DataFrame with the new 'size1' and 'size2' values
        # (vectorized operation to avoid iterating)

        logger.info("Appending RFQ Fields to RFQ Table Data...")
        # Add all RFQ 1 and RFQ 2 fields
        for field in self.rfqFields:
            if field not in self.rfqData.columns:
                self.rfqData[field] = ""

        logger.debug("Applying Size operation")
        new_sizes = self.rfqData['size'].apply(lambda x: pd.Series(process_size_column(x)))
        # Assign these new values to the original DataFrame's columns
        self.rfqData['size1'] = new_sizes[0]
        self.rfqData['size2'] = new_sizes[1]
        self.rfqData['last_updated'] = None

        # When results dataframe is from MTO, do not update records from database
        if not self.apply:
            db = DatabaseManager()
            found = []
            for row in self.rfqData.itertuples():
                material = row.material_description
                size = row.size
                try:
                    # record = db.get_rfq_record(material, size) Old method
                    record = db.get_latest_rfq_record_by_material(material)
                    if record.empty:
                        continue
                    found.append(record)
                    # Entry may be in database but not classified
                    rfq_scope = record['rfq_scope'][0]
                    general_category = record['general_category'][0]
                    if not rfq_scope and not general_category:
                        continue
                    for column in self.rfqData.loc[:, ~self.rfqData.columns.isin(['material_description', 'size', 'componentCategory'])]:
                        try:
                            if bool(record[column][0]): # Override field from db only if not NULL
                                self.rfqData.at[row.Index, column] = record[column][0]
                        except Exception as e:
                            if column not in ["__checked__", "quantity", "__uid__"]:
                                logger.info(f"Could not merge column. Does not exist from db record, {column}")
                except Exception as e:
                    logger.info("RFQ query failed", e)

            logger.info(f"Existing records merged: {len(found)}")
        self.rfqData.reset_index(inplace=True)
        self.rfqData.drop(["index", "level_0"], inplace=True, axis=1, errors="ignore")

        if self.apply == False:
            update_bom_ef_lookup(self.rfqData)

        self.result = self.rfqData
        self.finished.emit()

    def stop(self):
        if not self._stop_flag:
            logger.info("Apply RFQ Canceled")
            self._stop_flag.append(True)

class ApplyRfqWorker(QObject):

    finished = Signal()

    def __init__(self, rfqData, rfqColumns, bomData, generalData, genMap):
        super().__init__()
        self._stop_flag = []
        self.error = None
        self.result = None
        self.rfqData = rfqData
        self.rfqColumns = rfqColumns
        self.bomData = bomData
        self.generalData = generalData
        self.genMap = genMap

    def run(self):
        try:
            self.result = merge_rfq_into_bom(rfq_data=self.rfqData,
                                            rfq_fields=self.rfqColumns,
                                            bom_data=self.bomData,
                                            general_data=self.generalData,
                                            general_map=self.genMap,
                                            canceled=self._stop_flag)
        except Exception as e:
            logger.info("Apply RFQ Failed", exc_info=True)
            self.error = e
        self.finished.emit()

    def stop(self):
        if not self._stop_flag:
            logger.info("Apply RFQ Canceled")
            self._stop_flag.append(True)

class RfqDataView(TableResultsViewBase):

    sgnUpdate = Signal(object)
    sgnUpdateMtoData = Signal(object, dict)
    sgnRemoveTable = Signal()

    def __init__(self, parent) -> None:
        super().__init__(parent)
        self.found: pd.DataFrame = pd.DataFrame()
        self._aiConfig = {} # Read only
        self._mtoDone = False
        self.applying = False
        self.lookupTable = None
        self.thread: QThread = None
        self.worker: ApplyRfqWorker = None
        self.lastMtoStatus = None

        # Set rfq_columns and fields
        self.rfq_fields = None
        self.rfq_columns = None
        self.get_rfq_fields()

        pub.subscribe(self.onMtoAssistantUpdate, "mto-assistant-update")
        pub.subscribe(self.syncAiConfig, "sync-ai-config")

        self.table.setEditFreezeColumns(["material_description", "size"])

    def  __repr__(self) -> str:
        return "RFQ"

    def initToolbar(self):
        super().initToolbar()  # Initialize the base toolbar

        # Adjust the toolbar layout
        toolbarLayout = QHBoxLayout()
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        toolbarLayout.addItem(spacer)

        # Define the buttons and their icons
        buttons = [
            # ("MTO Merge", "chevron-down.svg"),
            ("Lookup Table", "search.svg"),
            ("MTO Assistant", "ph--open-ai-logo-duotone.svg"),
            ("apply", "tableview-edit.svg"),
        ]
        for name, icon in buttons:
            self.addToolbarButton(name, icon)
        # self._toolbarBtns["MTO Merge"].setCheckable(True)

    def setTableData(self, data: pd.DataFrame, apply=False):
        """Offload data for processing before displaying it"""
        logger.info("Setting RFQ Table Data...")

        if apply:
            title = "Applying MTO results...   "
        else:
            title = "Building RFQ...   "
        progress = QProgressDialog(title, "Cancel", 0, 0, self)
        progress.setEnabled(not apply)
        progress.setWindowTitle("ATEM")
        progress.setModal(True)
        progress.show()

        jobId = uuid.uuid4().hex

        def poll_worker():
            # Keep the ATEM job alive
            while self.worker:
                QApplication.processEvents()
                time.sleep(0.1)

        def finished():
            if not progress.wasCanceled():
                progress.canceled.disconnect()
            progress.close()
            result = self.worker.result
            error = self.worker.error
            self.worker = None
            self.thread.exit()
            if error:
                QMessageBox.information(self, "ATEM", "Build RFQ Error")
            elif result is not None:
                super(RfqDataView, self).setTableData(result)
                self._addRFQFields()
                self._addRFQFields2()
                self.restoreColumnOrder()
                if self.lastMtoStatus and apply:
                    duration = self.lastMtoStatus["duration"]
                    configuration = self.lastMtoStatus["configuration"]
                    classificationCount = self.lastMtoStatus["classificationCount"]
                    status = f"Completed - {duration}s<br>Config: {configuration}<br><br>Classifications updated: {classificationCount} record(s)"
                    QMessageBox.information(self, "ATEM", status)
                    self._mtoDone = True
                    self._toolbarBtns["MTO Assistant"].setEnabled(True)
            else:
                logger.warning("Build RFQ no exception error but no result")

        def canceled():
            self.worker.finished.disconnect()
            self.worker.stop()
            self.thread.exit()
            self.worker = None
            pub.sendMessage("job-cancel", uuid=jobId)
            self.thread.wait()
            self.sgnRemoveTable.emit()

        rfqFields = set(self.field_mapping.get("rfq_fields", {}).keys())
        rfqFields.update(self.field_mapping.get("rfq_fields2", {}).keys())

        self.thread = QThread()
        self.worker = BuildRfqWorker(rfqData=data, rfqFields=rfqFields, apply=apply)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(finished)
        self.thread.start()

        progress.canceled.connect(canceled)
        progress.setAutoClose(True)
        progress.setWindowModality(Qt.WindowModality.WindowModal)

        if not apply:
            pub.sendMessage("add-atem-job", title="Build RFQ", target=poll_worker, jobId=jobId)

    def get_rfq_fields(self):
        self.field_mapping = getSavedFieldMapJson()
        try:
            self.rfq_fields = self.field_mapping.get("rfq_fields", {})
            self.rfq_columns = [field for field in self.rfq_fields.keys()]
        except Exception as e:
            logger.error(f"Error creating rfq fields")

    def onToolbarBtn(self, name):
        logger.info(f"Toolbar button clicked: {name}", exc_info=True)
        if name == "apply":
            try:
                rfqData = self.getTableData()
                if rfqData is not None and not isinstance(rfqData, pd.DataFrame):
                    logger.info("Cannot apply: No RFQ Data")
                    return
                self.setApplyRfqEnabled(False)
                self.sgnRequestTableData.emit(str(self), ["General", "BOM"])
            except Exception as e:
                logger.error(f"Failed to apply RFQ. {e}", exc_info=True)
                status = f"Failed to apply RFQ: {e}"
                self.applying = False
                self._toolbarBtns["apply"].setEnabled(True)
                self.sgnShowQMessageBox.emit(status, "RFQ apply")
        elif name == "Lookup Table":
            self.onLookupTable()
        elif name == "remove":
            self.onRemoveRows()
        elif name == "MTO Assistant":
            self.runMtoAssistant()
        elif name == "save":
            self.saveToDatabase()
        else:
            super().onToolbarBtn(name)

    def runMtoAssistant(self):
        """Initialize and start MTO assistant"""

        self._toolbarBtns["MTO Assistant"].setEnabled(False)
        # If assistant already been run on this data, prompt user to confirm re-run
        if self._mtoDone:
            resp = QMessageBox.question(self,
                                        'MTO Assistant',
                                        'MTO Assistant has been run on this project. Would you like to run this again?',
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.No)
            if resp == QMessageBox.No:
                self._toolbarBtns["MTO Assistant"].setEnabled(True)
                return

        logger.info("Requesting MTO Assistant...")

        if not self._aiConfig:
            pub.sendMessage("sync-ai-config-request")
            return
        if self._aiConfig["gptVersion"] == "4":
            resp = QMessageBox.question(self,
                                        'GPT4 Mode Confirmation',
                                        f"Selecting GPT4 mode may accrue additional costs. Continue?",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if resp == QMessageBox.No:
                return

        try:
            # Filter for unclassified records. If all have been found, prompt
            # user if they want to reclassify all records
            df = self.table.getDataFrame(drop_uid=False).copy()
            df.replace("", pd.NA, inplace=True)
            filtered = df[df["material_description"].notna() & df["size"].notna()]
            self.found = filtered[filtered["general_category"].notna() | filtered["rfq_scope"].notna()]
            unclassified = filtered[filtered["general_category"].isna() & filtered["rfq_scope"].isna()]

            if "__checked__" in self.found:
                del self.found["__checked__"]

            print(f"{len(unclassified)} remaining records not found")
            if unclassified.empty:
                print("All records have been found in database")
                resp = QMessageBox.question(self,
                                        'MTO Assistant',
                                        "All records have been updated from the database. Proceed with MTO assistant?",
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.No)
                if resp == QMessageBox.No:
                    self._toolbarBtns["MTO Assistant"].setEnabled(True)
                    return
                unclassified = self.found
                self.found = pd.DataFrame()

            data = {
                "df": unclassified,
                "project": self.projectData
            }
            pub.sendMessage("request-mto-assistant-job", data=data)
        except Exception as e:
            status = f"Failed to initiate MTO Assistant: {e}"
            self.sgnShowQMessageBox.emit(status, "MTO Assistant")
            self._toolbarBtns["MTO Assistant"].setEnabled(True)

    def saveToDatabase(self):
        try:
            df = self.table.getDataFrame()
            print("\n\nRFQ DF ROWS FOR DB COMMIT: \n", len(df))
            db_manager = DatabaseManager()
            res = db_manager.update_rfq(df)
            if res["success"]:
                df["last_updated"] = res["last_updated"]
                self.setTableData(df)
        except Exception as e:
            logger.error(f"Error Committing RFQ to Database: {e}", exc_info=True)

    def onTableDataReceived(self, data: dict):
        logger.info(f"RFQ received data for {data.keys()}")
        if not self.applying: # Should never be receiving data without having applied for it
            return

        try:
            self.mergeRfqIntoBom(data)
        except Exception as e:
            logger.error(f"Error on data received: {e}", exc_info=True)
            status = f"Failed to merge RFQ into BOM: {e}"
            QMessageBox.information(self, "ATEM", f"Apply RFQ Error: {e}")
            self.setApplyRfqEnabled(True)

    def mergeRfqIntoBom(self, data: dict):

        if self.thread and self.thread.isRunning():
            logger.info("Apply RFQ is already running running....")
            return

        # Only proceed if bom_data is available and (general_data is available or not requested)
        rfqData = self.getTableData()
        if rfqData is not None and not isinstance(rfqData, pd.DataFrame):
            logger.warning("Error: No RFQ Data")
            return
        bomData = data.get("BOM")
        if bomData is None or not isinstance(bomData, pd.DataFrame) or bomData.empty:
            logger.warning("Error: BOM data missing. Cannot merge")
            QMessageBox.information(self, "Cannot merge with BOM data", "No BOM data to merge")
            return
        generalData = data.get("General")
        genMap = None
        if isinstance(generalData, pd.DataFrame) and not generalData.empty:
            genMap = self.getGenMap()

        progress = QProgressDialog("Applying RFQ...", "Cancel", 0, 0, self)
        progress.setWindowTitle("ATEM")
        progress.setModal(True)
        progress.show()

        jobId = uuid.uuid4().hex

        def poll_worker():
            # Keep the ATEM job alive
            while self.worker:
                QApplication.processEvents()
                time.sleep(0.1)

        def finished():
            if not progress.wasCanceled():
                progress.canceled.disconnect()
            progress.close()
            result = self.worker.result
            error = self.worker.error
            self.worker = None
            self.thread.exit()
            if error:
                QMessageBox.information(self, "ATEM", "Apply RFQ Error")
            elif result:
                self.updateOtherTableData.emit(result)
            else:
                logger.warning("Apply RFQ no exception error but no result")

            self.setApplyRfqEnabled(True)

        def canceled():
            self.worker.finished.disconnect()
            self.worker.stop()
            self.thread.exit()
            self.worker = None
            pub.sendMessage("job-cancel", uuid=jobId)
            self.thread.wait()
            self.setApplyRfqEnabled(True)

        self.thread = QThread()
        self.worker = ApplyRfqWorker(rfqData, self.rfq_columns, bomData, generalData, genMap)
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(finished)
        self.thread.start()

        progress.canceled.connect(canceled)
        progress.setAutoClose(True)
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        pub.sendMessage("add-atem-job", title="Apply RFQ", target=poll_worker, jobId=jobId)

    def onMtoAssistantUpdate(self, data):
        """Handles updates received from MTO assistant"""
        logger.info("RFQ Table received MTO Assistant Update")

        if data["ok"] is False:
            print(f"MTO assistant error... \n {data}")
            status = f"MTO Assistant failed"
            self._toolbarBtns["MTO Assistant"].setEnabled(True)
            return
        status = data["status"]
        if status == "complete":
            configuration = data["configuration"]
            duration = data["duration"]
            final_df: pd.DataFrame = data["final_df"]
            if final_df is None or final_df.empty:
                status = f"MTO Completed - No results - {duration}s<br>Config: {configuration}"
                logger.error("Error on MTO Assistant Update. Final df is None or is empty")
                self.sgnShowQMessageBox.emit(status, "MTO Assistant                 ")
                self._mtoDone = False
                self._toolbarBtns["MTO Assistant"].setEnabled(True)
                return

            del final_df["__uid__"]
            final_df.rename(columns={'tko_id': '__uid__'}, inplace=True)
            classificationCount = len(final_df)

            final_df.reset_index(inplace=True)
            # If the records from the MTO do not find category,
            # then some fields are omitted in classifier analysis? So
            # we need to insert them before being able to merge records
            rfq_fields = self.field_mapping.get("rfq_fields", {})
            rfq_columns = [field for field in rfq_fields.keys()]
            for field in rfq_columns:
                if field not in final_df:
                    print("added field", field)
                    final_df[field] = None

            if not self.found.empty:
                final_df = pd.concat([final_df, self.found])

            print(f"Existing records merged: {len(self.found)}")
            final_df.reset_index(inplace=True)
            final_df.drop(["index", "level_0"], inplace=True, axis=1, errors="ignore")
            final_df = final_df.replace(np.nan, None)

            update_bom_ef_lookup(final_df)

            mtoStatus = {
                "configuration": configuration,
                "duration": duration,
                "classificationCount": classificationCount,
                "classificationsRequested": data.get("requested_classifications"),
                "errors": data.get("errors", "")
            }
            self.sgnUpdateMtoData.emit(final_df, mtoStatus)

    def onLookupTable(self):
        if not self.lookupTable:
            self.lookupTable = LookupTable(None)
            self.lookupTable.closeEvent = self.onLookupTableClosed
        self.lookupTable.show()
        self.lookupTable.raise_()

    def onLookupTableClosed(self, event):
        self.lookupTable = None

    def showEvent(self, event) -> None:
        pub.sendMessage("sync-ai-config-request")
        return super().showEvent(event)

    def syncAiConfig(self, data):
        self._aiConfig = data

    def cleanup(self):
        """Previously not being unsubscribed"""
        super().cleanup()
        pub.unsubscribe(self.syncAiConfig, "sync-ai-config")
        pub.unsubscribe(self.onMtoAssistantUpdate, "mto-assistant-update")

    def _addRFQFields2(self):
        """Append RFQ2 fields to table"""
        try:
            if self.table.model():
                rfq_fields = self.field_mapping.get("rfq_fields2", {})
                rfq_columns = [field for field in rfq_fields.keys()]
                self.table.addColumns(rfq_columns)
                for field, _ in rfq_fields.items():
                    self.table.setLineEditDelegateForColumn(field)
            self.autoSizeColumns()
        except Exception as e:
            print(f"\n\nCould not add RFQ Fields {rfq_columns}: {e}")

    def onRemoveRows(self):
        """Override this"""
        logger.info("Delete Rows")
        self.table.getDefaultColumnNameOrder()
        df = self.getTableData(drop_uid=True)
        lenDf = 0
        db = DatabaseManager()
        deleted = 0

        def prompt(count) -> bool:
            msg = f'Remove {count} selected rows from RFQ?'
            result = QMessageBox.question(self, "Remove?", msg)
            return result

        if self.table.checkboxStyle == GroupedTableFlags.HEADER_CHECKBOX_FIRST:
            checkedDf = self.table.getSelectedRowData()
            lenDf = len(checkedDf)
            if lenDf == 0 or prompt(lenDf) != QMessageBox.Yes:
                return
            for _, row in checkedDf.iterrows():
                material = row["material_description"]
                size = row["size"]
                df.drop(df[(df['material_description'] == material) & (df['size'] == size)].index, inplace=True)
                ok = db.delete_rfq_record(materialDescription=material, size=size)
                if ok == 1:
                    deleted += 1
        else:
            # Old
            materialIndex = self.table._cacheColumnToIndex.get("material_description")
            sizeIndex = self.table._cacheColumnToIndex.get("size")
            selectedIndexes = []
            if self.table.frozenTable.isVisible():
                selectedIndexes = self.table.frozenTable.selectedIndexes()
            if not selectedIndexes:
                selectedIndexes =  self.table.selectedIndexes()
            def rowCounts(indexes):
                r = set()
                for n in indexes:
                    r.add(n.row())
                return len(r)
            lenDf = rowCounts(selectedIndexes)
            if lenDf == 0 or prompt(lenDf) != QMessageBox.Yes:
                return
            rows = set()
            for index in selectedIndexes:
                if index.row() in rows:
                    continue
                rows.add(index.row())
                material = self.table.filterProxyModel.index(index.row(), materialIndex).data(Qt.ItemDataRole.UserRole)
                size = self.table.filterProxyModel.index(index.row(), sizeIndex).data(Qt.ItemDataRole.UserRole)
                df.drop(df[(df['material_description'] == material) & (df['size'] == size)].index, inplace=True)
                ok = db.delete_rfq_record(materialDescription=material, size=size)
                if ok:
                    deleted += 1

        if df.empty:
            self.sgnRemoveTable.emit()
            return
        df.reset_index(drop=True, inplace=True)
        super().setTableData(df.reindex())
        QMessageBox.information(self, "RFQ Delete", f"Removed {lenDf} rows. {deleted} from the database")

    def setApplyRfqEnabled(self, enabled: bool = True):
        self.applying = not enabled
        self._toolbarBtns["apply"].setEnabled(enabled)