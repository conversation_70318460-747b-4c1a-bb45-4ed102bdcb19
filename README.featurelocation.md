# ATEM (File locations)


<br clear="all" />

## Overview

Relevant source paths, locations and search hints

Below lists the feature and related source files or other locations

### App Paths

Functions which return cross-platform system and app path and config locations

This includes fetching and saving config data such as fieldmap, user-defined column order

- src\app_paths.py

### Building ATEM

Freeze to an executable

- build_atem.py - interactive script to build a Setup EXE installer, with code signing
- __version__.py


### ATEM Workspace, Core Job Handling and Database transactions

Workspace object handles much of the job requests and results' responses, database transactions, and ATEM's google cloud functions e.g. login

- src\config\workspace.py


### PDF Pre-Processing

Pre-process PDF source. Store raw data, insert pdf_ids to database, detect missing regions

- src\atom\sourcepreprocess.py
- PreprocessProjectSourceJob class in src\config\workspace.py

### Source Extraction

Using ROIs, extract text/tables from preprocessed data with/or OCR

- src\atom\roiextraction.py
- unit_tests\get_tables_test.py
- RoiExtractionJob class in src\config\workspace.py

For OCR

- see OcrPages class in src\atom\roiextraction.py

### ATEM Local SQLite Database

- from src.atom.dbManager import DatabaseManager
- createDb.py (creates and alters database on app startup)

### ATEM Cloud

Calls to google cloud functions. Login, user data fetch, token payment, price fetch, etc.

- src\config\atem_cloud.py


### Build RFQ from BOM

When PrepareRfqWorker is done, this triggers BuildRfqWorker

PrepareRfqWorker groups the BOM by description, size
BuildRfqWorker fetches existing results from local RFQ database and updates accordingly


- see PrepareRfqWorker in src\views\tables\bomresultsview.py
- see BuildRfqWorker in src\views\tables\rfqtableview.py
- sgnUpdateRfq signal references

### Merge RFQ into BOM

- src\atom\merge_rfq_into_bom.py
- src\views\tables\rfqtableview.py (see ApplyRfqWorker thread worker)


### MTO Assistant + AI Classifier

Classify RFQ

- src.atom.ai_classifier
- MtoClassifierJob class in src\config\workspace.py
- src\views\tables\rfqtableview.py (gui table)

### PDF Grouping

Detect and group pages into unique groups so that ROI layouts can be configured more quickly

- from src.atom.grouppdfs.grouppdfs import GroupPdfs
- GroupPdfsJob class in src\config\workspace.py

### EF Lookup

- src\atom\ef_lookup.py
- src\data\tables\ef_template.xlsx

### Field Map. Convert internal fields to display fields

- src\data\scripts\generated\fieldmaptemplate.json
- src\data\scripts\generatefieldmapjson.py


### GUI Table Order

Defines which columns should always be shown, hidden and the default order

- src\data\tables\alwaysvisibletablefields.py
- src\data\tables\defaultcolumnorder.py
- src\data\tables\hiddentablefields.py

### Convert ROI payload

For converting multi ROI layout ready for extraction

- convert_rois in src\utils\convert_roi_payload.py

## GUI


### ATEM GUI Tables

Base table for displaying table data

- src\views\tableresultsview.py
- src\widgets\groupedtableview.py

### Blueprint Reader (GUI)

GUI for viewing project sources and configuring ROI layout

- src\views\blueprintreaderview.py

### Table Exporter (GUI)

Configurable exports of currently displayed ATEM tables

- src\views\dialogs\tableexporter.py

### Table Importer (GUI)

Import excel data, optionally map to a project source pdf_id given pdf_page

- src\views\dialogs\tableimporter.py


### Audit Dialog (QTableView Interface)

Dialog for auditing extracted data
This is the QTableView implementation and not QTreeView.

- src\views\dialogs\auditdialog2.py


### Roboflow Weld Detect Results

Experimental, sends CV images to RoboFlow, and update detected weld table

- src\views\dialogs\welddetect.py
- src\atom\welddetect\roboflow_utils.py

### Isometric Region Analysis

Experimental, for testing with VAT dipping

- src\views\dialogs\isometricregion.py


## Utils

### Fast storage (save and load feather)

Save and load .feather to DataFrame

- src\atom\fast_storage.py


### ATEM Theme

- src\data\themes\stylesheet.qss

### Resource and icon files

- src\resources

### Latest-release

- src\data\updater\latest-release.txt (Checks if current version is up-to-date)

### PDF Util

- src\atom\combine_files.py (includes functions to combine and splits pdf files)


### PySide6 utility functions

- src\pyside_util.py

### Data conversions

- data_conversions.py (Parse values, conversions and lookup)