/*
================================================================
Based on logic from `data_conversions.py`
================================================================
*/

CREATE OR REPLACE FUNCTION public.parse_numeric_size_part(p_text TEXT)
RETURNS NUMERIC
LANGUAGE plpgsql
IMMUTABLE -- Assuming parsing logic doesn't depend on table data/settings
AS $$
DECLARE
    v_cleaned_text TEXT;
    v_whole NUMERIC := 0;
    v_numerator NUMERIC;
    v_denominator NUMERIC;
    v_result NUMERIC := NULL;
    v_match TEXT[];
BEGIN
    IF p_text IS NULL OR TRIM(p_text) = '' THEN
        RETURN NULL;
    END IF;

    -- Clean the input: remove quotes, trim whitespace
    v_cleaned_text := TRIM(REPLACE(REPLACE(p_text, '"', ''), '''', ''));

    -- Attempt to parse different formats using regular expressions

    -- 1. Mixed number with space (e.g., "1 1/2", "2 3 / 4")
    v_match := regexp_match(v_cleaned_text, '^(\d+)\s+(\d+)\s*/\s*(\d+)$');
    IF v_match IS NOT NULL THEN
        BEGIN
            v_whole := v_match[1]::NUMERIC;
            v_numerator := v_match[2]::NUMERIC;
            v_denominator := v_match[3]::NUMERIC;
            IF v_denominator <> 0 THEN
                v_result := v_whole + (v_numerator / v_denominator);
            END IF;
            RETURN v_result;
        EXCEPTION WHEN OTHERS THEN
            -- Ignore parsing errors for this pattern, try next
        END;
    END IF;

    -- 2. Simple fraction (e.g., "1/2", "3 / 4")
    v_match := regexp_match(v_cleaned_text, '^(\d+)\s*/\s*(\d+)$');
    IF v_match IS NOT NULL THEN
        BEGIN
            v_numerator := v_match[1]::NUMERIC;
            v_denominator := v_match[2]::NUMERIC;
            IF v_denominator <> 0 THEN
                v_result := v_numerator / v_denominator;
            END IF;
            RETURN v_result;
        EXCEPTION WHEN OTHERS THEN
            -- Ignore parsing errors, try next
        END;
    END IF;

    -- 3. Decimal followed by fraction (e.g., "1.1/2" -> 1.5) - specific logic from parse_complex_size
     -- Check if both '.' and '/' exist
    IF POSITION('.' IN v_cleaned_text) > 0 AND POSITION('/' IN v_cleaned_text) > POSITION('.' IN v_cleaned_text) THEN
        v_match := regexp_match(v_cleaned_text, '^(\d+)\.\s*(\d+)\s*/\s*(\d+)$');
        IF v_match IS NOT NULL THEN
             BEGIN
                v_whole := v_match[1]::NUMERIC; -- Part before decimal
                v_numerator := v_match[2]::NUMERIC;
                v_denominator := v_match[3]::NUMERIC;
                IF v_denominator <> 0 THEN
                    v_result := v_whole + (v_numerator / v_denominator);
                END IF;
                RETURN v_result;
            EXCEPTION WHEN OTHERS THEN
                 -- Ignore parsing errors, try next
            END;
        END IF;
    END IF;

    -- 4. Standard decimal or integer (e.g., "123", "12.34")
    -- Use a regex to ensure it's a valid number format before casting
    IF v_cleaned_text ~ '^\d+(\.\d+)?$' THEN
        BEGIN
            v_result := v_cleaned_text::NUMERIC;
            RETURN v_result;
        EXCEPTION WHEN OTHERS THEN
             -- Ignore casting errors
             RETURN NULL; -- Failed to parse as any known format
        END;
    END IF;

    -- If none of the patterns matched or parsed correctly
    RETURN NULL;

END;
$$;


CREATE OR REPLACE FUNCTION public.process_bom_size(
    p_size TEXT,
    OUT size1 NUMERIC,
    OUT size2 NUMERIC
)
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    v_cleaned_size TEXT;
    v_parts TEXT[];
    v_temp_size1 NUMERIC;
    v_temp_size2 NUMERIC;
BEGIN
    size1 := NULL;
    size2 := NULL;

    IF p_size IS NULL OR TRIM(p_size) = '' THEN
        RETURN; -- Return NULLs
    END IF;

    v_cleaned_size := TRIM(p_size);

    -- Check for 'x' case-insensitively
    IF v_cleaned_size ~* 'x' THEN
        -- Split by 'x', allowing for surrounding whitespace
        v_parts := regexp_split_to_array(v_cleaned_size, '\s*[xX]\s*');

        IF array_length(v_parts, 1) >= 1 THEN
            v_temp_size1 := public.parse_numeric_size_part(v_parts[1]);
        END IF;

        IF array_length(v_parts, 1) >= 2 THEN
             v_temp_size2 := public.parse_numeric_size_part(v_parts[2]);
        END IF;

    ELSE
        -- No 'x' found, replace hyphen with space and parse as size1
        -- Note: The python code replaces '-' with ' ' only when 'x' is NOT present.
        v_cleaned_size := REPLACE(v_cleaned_size, '-', ' ');
        v_temp_size1 := public.parse_numeric_size_part(v_cleaned_size);
        v_temp_size2 := NULL;
    END IF;

    -- Ensure size1 >= size2 if both are not null
    IF v_temp_size1 IS NOT NULL AND v_temp_size2 IS NOT NULL AND v_temp_size2 > v_temp_size1 THEN
        -- Swap them
        size1 := v_temp_size2;
        size2 := v_temp_size1;
    ELSE
        size1 := v_temp_size1;
        size2 := v_temp_size2;
    END IF;

    RETURN;
END;
$$;

-- ====================================================================
-- Function to process 'size' column into 'size1' and 'size2'
-- for specific projects in the 'public.bom' table.
-- Requires helper functions:
--   public.parse_numeric_size_part(TEXT) RETURNS NUMERIC
--   public.process_bom_size(p_size TEXT, OUT size1 NUMERIC, OUT size2 NUMERIC)
-- ====================================================================

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.update_bom_sizes_for_projects(integer[]);

/*
=============================================================================
FUNCTION: public.update_bom_sizes_for_projects(project_ids integer[])
=============================================================================

PURPOSE:
    Processes the 'size' text field in the public.bom table into numeric 'size1' and 'size2' 
    columns for specified projects. This enables proper numeric sorting and comparison of sizes.

EXECUTION TRIGGERS:
    This function is manually called with an array of project IDs to process.
    It does not automatically fire on data changes.

PROCESSING LOGIC:
    1. For each project ID in the input array:
       a. Identifies BOM records that need size processing based on these conditions:
          - The 'size' field must NOT be NULL
          - AND EITHER:
             * 'size1' is NULL (never processed before)
             * OR 'size' contains an 'x' character AND 'size2' is NULL (needs second dimension)
       
       b. For matching records, parses the 'size' text using process_bom_size() which:
          - Handles various size formats including:
            * Simple numbers: "2", "3.5"
            * Fractions: "1/2", "3/4"
            * Mixed numbers: "1 1/2"
            * Dimensions: "2x3", "1.5 x 2"
          - For dimensions (containing 'x'), extracts both values into size1 and size2
          - For single values, populates only size1
          - Always ensures size1 ≥ size2 (swaps if needed)
       
       c. Tracks and returns the number of rows updated for each project

DEPENDENCIES:
    - public.parse_numeric_size_part(TEXT): Parses text into numeric values
    - public.process_bom_size(TEXT): Processes size strings into size1/size2 components

RETURN VALUE:
    Table with columns:
    - result_project_id: The project ID that was processed
    - updated_rows: Number of rows updated for that project

USAGE EXAMPLE:
    SELECT * FROM public.update_bom_sizes_for_projects(ARRAY[123, 456]);

IMPLEMENTATION NOTES:
    - Uses a subquery with SELECT to properly extract multiple values from the process_bom_size function
    - Processes each project ID separately to provide granular update statistics
    - Safe to run multiple times - only processes records that need updating
    - Performance depends on the number of records per project that need processing

MAINTENANCE:
    - If size format patterns change, update the parse_numeric_size_part function
    - To add support for new size formats, modify the process_bom_size function
    - Consider adding indexes on (project_id, size, size1, size2) if performance is an issue
=============================================================================
*/

-- Create the function
CREATE OR REPLACE FUNCTION public.update_bom_sizes_for_projects(project_ids integer[])
-- Returns a table summarizing the updates per project
RETURNS TABLE(result_project_id integer, updated_rows integer)
AS $$
DECLARE
    project_id_var integer;       -- Variable to hold the current project ID from the loop
    updated_count_var integer;    -- Variable to store the ROW_COUNT after an UPDATE
BEGIN
    -- Loop through each project ID provided in the input array
    FOREACH project_id_var IN ARRAY project_ids
    LOOP
        -- Update BOM records for the current project_id_var
        -- Apply the size processing logic where needed
        UPDATE public.bom
        SET
            -- Use ROW() constructor to properly assign multiple columns from a function
            (size1, size2) = (SELECT size1, size2 FROM public.process_bom_size(bom.size))
        WHERE
            -- 1. Filter for the specific project ID of this loop iteration
            bom.project_id = project_id_var
            -- 2. Apply the original conditions for when to process the size
            AND bom.size IS NOT NULL
            AND (
                bom.size1 IS NULL                             -- Process if size1 is NULL
                OR (bom.size ~* 'x' AND bom.size2 IS NULL)    -- OR if size contains 'x' and size2 is NULL
               );

        -- Get the number of rows affected by the immediately preceding UPDATE statement
        GET DIAGNOSTICS updated_count_var = ROW_COUNT;

        -- Prepare the output row for this project ID
        result_project_id := project_id_var;
        updated_rows := updated_count_var;

        -- Add the row to the result set to be returned by the function
        RETURN NEXT;

    END LOOP; -- End of loop for project IDs

    -- End of function execution
    RETURN;

END;
$$ LANGUAGE plpgsql;
