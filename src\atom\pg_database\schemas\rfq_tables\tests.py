DROP_PROJECT_DATA = """
    -- To drop the procedure if needed:
    -- DROP PROCEDURE IF EXISTS public.clear_project_tables;

    CREATE OR REPLACE PROCEDURE public.clear_project_tables(p_project_id INTEGER)
    LANGUAGE plpgsql
    AS $$
    BEGIN
        -- Start transaction
        BEGIN
            -- Delete from bom table first
            DELETE FROM public.bom 
            WHERE project_id = p_project_id;

            -- Delete from atem_rfq table second
            DELETE FROM public.atem_rfq 
            WHERE project_id = p_project_id;

            -- Delete from atem_rfq_input table last
            DELETE FROM public.atem_rfq_input 
            WHERE project_id = p_project_id;

            -- Commit the transaction if all deletes succeed
            COMMIT;

            -- Return success message
            RAISE NOTICE 'Successfully deleted records for project_id: %', p_project_id;

        EXCEPTION
            WHEN OTHERS THEN
                -- Rollback in case of any error
                ROLLBACK;
                RAISE EXCEPTION 'Error deleting records: %', SQLERRM;
        END;
    END;
    $$;
"""

GENERATE_TEST_RFQDATA = """
    -- Comprehensive test data generator function (Generates a lot of data)
    CREATE OR REPLACE FUNCTION generate_comprehensive_test_data(
        p_project_id INTEGER,
        p_items_per_type INTEGER DEFAULT 5    -- How many distinct items per type
    ) RETURNS VOID AS $$
    DECLARE
        -- Material options
        v_materials TEXT[] := ARRAY['CS', 'SS304', 'SS316', 'Duplex', 'Super Duplex', 'Alloy 20'];
        
        -- Component type options
        v_fitting_types TEXT[] := ARRAY['45 Elbow', '90 LR Elbow', '90 SR Elbow', 'Tee', 'Reducing Tee', 'Cross', 'Coupling', 'Union', 'Cap', 'Reducer'];
        v_valve_types TEXT[] := ARRAY['Gate Valve', 'Globe Valve', 'Ball Valve', 'Check Valve', 'Butterfly Valve', 'Needle Valve', 'Control Valve', 'Relief Valve'];
        v_pipe_types TEXT[] := ARRAY['Pipe', 'Seamless Pipe', 'Welded Pipe', 'ERW Pipe', 'Schedule Pipe'];
        v_support_types TEXT[] := ARRAY['Hanger', 'Support Clamp', 'Pipe Shoe', 'Spring Hanger', 'Riser Clamp', 'U-Bolt', 'Saddle Support', 'Pipe Rest'];
        
        -- Standardization options
        v_schedules TEXT[] := ARRAY['SCH 40', 'SCH 80', 'SCH 10', 'SCH 20', 'SCH 5', 'STD', 'XS', 'XXS'];
        v_grades TEXT[] := ARRAY['A', 'B', 'C', 'WPB', 'WPC', 'TP304', 'TP316', 'F304', 'F316'];
        v_ratings TEXT[] := ARRAY['150#', '300#', '600#', '900#', '1500#', '2500#', 'CL150', 'CL300', 'PN16', 'PN25'];
        v_ends_array TEXT[] := ARRAY['BW', 'SW', 'THD', 'FLG', 'RTJ', 'CL'];
        
        -- Working variables
        v_material TEXT;
        v_component TEXT;
        v_description TEXT;
        v_size DECIMAL;
        v_size2 DECIMAL;
        v_standard TEXT;
        v_rating TEXT;
        v_schedule TEXT;
        v_grade TEXT;
        v_ends TEXT;
        v_category TEXT;
        v_scope TEXT;
        
        i INTEGER;
        j INTEGER;
    BEGIN
        -- Generate fittings with multiple sizes
        FOREACH v_component IN ARRAY v_fitting_types LOOP
            FOR i IN 1..p_items_per_type LOOP
                -- Pick a random material
                v_material := v_materials[1 + floor(random() * array_length(v_materials, 1))::INT];
                v_grade := v_grades[1 + floor(random() * array_length(v_grades, 1))::INT];
                v_schedule := v_schedules[1 + floor(random() * array_length(v_schedules, 1))::INT];
                v_ends := v_ends_array[1 + floor(random() * array_length(v_ends_array, 1))::INT];
                
                -- Create a consistent description
                v_description := v_material || ' ' || v_component || ' ' || v_schedule || ' ' || v_ends;
                
                -- Generate 3-5 different sizes for this description
                FOR j IN 1..3 + floor(random() * 3)::INT LOOP
                    -- Sizes between 0.5" and 24"
                    v_size := (0.5 + floor(random() * 47) / 2.0)::DECIMAL(5,1);
                    
                    -- For reducers and other two-size components, add a second size
                    IF v_component IN ('Reducer', 'Reducing Tee') THEN
                        -- Second size is always smaller than the first
                        v_size2 := (0.5 + floor(random() * (v_size * 2 - 1)) / 2.0)::DECIMAL(5,1);
                    ELSE
                        v_size2 := NULL;
                    END IF;
                    
                    -- Insert into atem_rfq
                    INSERT INTO public.atem_rfq (
                        project_id,
                        material_description,
                        size1,
                        size2,
                        quantity,
                        material,
                        grade,
                        schedule,
                        ends,
                        rfq_scope,
                        fitting_category
                    ) VALUES (
                        p_project_id,
                        v_description,
                        v_size,
                        v_size2,
                        5 + floor(random() * 45)::INT,  -- Random quantity between 5-50
                        v_material,
                        v_grade,
                        v_schedule,
                        v_ends,
                        'Fittings',
                        v_component
                    );
                END LOOP;
            END LOOP;
        END LOOP;
        
        -- Generate valves with multiple sizes
        FOREACH v_component IN ARRAY v_valve_types LOOP
            FOR i IN 1..p_items_per_type LOOP
                -- Pick a random material
                v_material := v_materials[1 + floor(random() * array_length(v_materials, 1))::INT];
                v_rating := v_ratings[1 + floor(random() * array_length(v_ratings, 1))::INT];
                v_ends := v_ends_array[1 + floor(random() * array_length(v_ends_array, 1))::INT];
                
                -- Create a consistent description
                v_description := v_material || ' ' || v_component || ' ' || v_rating || ' ' || v_ends;
                
                -- Generate 3-5 different sizes for this description
                FOR j IN 1..3 + floor(random() * 3)::INT LOOP
                    -- Sizes between 0.5" and 24"
                    v_size := (0.5 + floor(random() * 47) / 2.0)::DECIMAL(5,1);
                    
                    -- Insert into atem_rfq
                    INSERT INTO public.atem_rfq (
                        project_id,
                        material_description,
                        size1,
                        quantity,
                        material,
                        rating,
                        ends,
                        rfq_scope,
                        valve_type
                    ) VALUES (
                        p_project_id,
                        v_description,
                        v_size,
                        2 + floor(random() * 18)::INT,  -- Random quantity between 2-20
                        v_material,
                        v_rating,
                        v_ends,
                        'Valves',
                        v_component
                    );
                END LOOP;
            END LOOP;
        END LOOP;
        
        -- Generate pipes with multiple sizes
        FOREACH v_component IN ARRAY v_pipe_types LOOP
            FOR i IN 1..p_items_per_type LOOP
                -- Pick a random material
                v_material := v_materials[1 + floor(random() * array_length(v_materials, 1))::INT];
                v_schedule := v_schedules[1 + floor(random() * array_length(v_schedules, 1))::INT];
                v_grade := v_grades[1 + floor(random() * array_length(v_grades, 1))::INT];
                
                -- Create a consistent description
                v_description := v_material || ' ' || v_component || ' ' || v_schedule || ' ' || v_grade;
                
                -- Generate 3-5 different sizes for this description
                FOR j IN 1..3 + floor(random() * 3)::INT LOOP
                    -- Sizes between 0.5" and 24"
                    v_size := (0.5 + floor(random() * 47) / 2.0)::DECIMAL(5,1);
                    
                    -- Insert into atem_rfq
                    INSERT INTO public.atem_rfq (
                        project_id,
                        material_description,
                        size1,
                        quantity,
                        material,
                        schedule,
                        grade,
                        rfq_scope,
                        pipe_category
                    ) VALUES (
                        p_project_id,
                        v_description,
                        v_size,
                        20 + floor(random() * 480)::INT,  -- Random length between 20-500 feet
                        v_material,
                        v_schedule,
                        v_grade,
                        'Pipe',
                        v_component
                    );
                END LOOP;
            END LOOP;
        END LOOP;
        
        -- Generate supports with multiple sizes
        FOREACH v_component IN ARRAY v_support_types LOOP
            FOR i IN 1..p_items_per_type LOOP
                -- Pick a random material
                v_material := v_materials[1 + floor(random() * array_length(v_materials, 1))::INT];
                
                -- Create a consistent description
                v_description := v_material || ' ' || v_component;
                
                -- Generate 3-5 different sizes for this description
                FOR j IN 1..3 + floor(random() * 3)::INT LOOP
                    -- Sizes between 0.5" and 24"
                    v_size := (0.5 + floor(random() * 47) / 2.0)::DECIMAL(5,1);
                    
                    -- Insert into atem_rfq
                    INSERT INTO public.atem_rfq (
                        project_id,
                        material_description,
                        size1,
                        quantity,
                        material,
                        rfq_scope
                    ) VALUES (
                        p_project_id,
                        v_description,
                        v_size,
                        5 + floor(random() * 45)::INT,  -- Random quantity between 5-50
                        v_material,
                        'Supports'
                    );
                END LOOP;
            END LOOP;
        END LOOP;
    END;
    $$ LANGUAGE plpgsql;

    -- USE THE TEST DATA
    -- Clear existing test data if needed
    DELETE FROM public.atem_rfq WHERE project_id = 2;
    DELETE FROM public.atem_rfq_input WHERE project_id = 2;

    -- Generate test data with 5 types per category 
    SELECT generate_comprehensive_test_data(2, 5);

    -- Verify the results
    SELECT 
        rfq_scope,
        COUNT(DISTINCT material_description) AS unique_descriptions,
        COUNT(*) AS total_items
    FROM 
        public.atem_rfq 
    WHERE 
        project_id = 2
    GROUP BY 
        rfq_scope;

    -- Check the RFQ_INPUT table to verify deduplication
    SELECT 
        COUNT(*) AS rfq_input_count 
    FROM 
        public.atem_rfq_input 
    WHERE 
        project_id = 2;

    -- See a sample of the test data
    SELECT 
        r.id, 
        r.material_description, 
        r.size1, 
        r.size2,
        r.rfq_scope,
        r.fitting_category,
        r.valve_type,
        r.pipe_category,
        i.id AS input_id
    FROM 
        public.atem_rfq r
    JOIN 
        public.atem_rfq_input i ON r.rfq_input_id = i.id
    WHERE 
        r.project_id = 2
    ORDER BY 
        r.material_description, 
        r.size1
    LIMIT 20;
"""

GENERATE_MINIMAL_TEST_DATA = """
    -- Create a simpler test data generator
    CREATE OR REPLACE FUNCTION generate_minimal_test_data(p_project_id INTEGER) RETURNS VOID AS $$
    BEGIN
        -- Generate exactly 20 records across different types
        
        -- 5 Fitting records
        INSERT INTO public.atem_rfq (
            project_id, material_description, size1, quantity, material, rfq_scope, fitting_category
        ) VALUES
        (p_project_id, 'CS 90 LR Elbow SCH 40 BW', 2.0, 10, 'CS', 'Fittings', '90 LR Elbow'),
        (p_project_id, 'CS 90 LR Elbow SCH 40 BW', 3.0, 8, 'CS', 'Fittings', '90 LR Elbow'),
        (p_project_id, 'SS304 Tee SCH 80 SW', 1.5, 12, 'SS304', 'Fittings', 'Tee'),
        (p_project_id, 'SS304 Tee SCH 80 SW', 2.0, 6, 'SS304', 'Fittings', 'Tee'),
        (p_project_id, 'SS316 Reducer SCH 40 BW', 4.0, 4, 'SS316', 'Fittings', 'Reducer');
        
        -- 5 Valve records
        INSERT INTO public.atem_rfq (
            project_id, material_description, size1, quantity, material, rfq_scope, valve_type
        ) VALUES
        (p_project_id, 'CS Gate Valve 150# FLG', 2.0, 3, 'CS', 'Valves', 'Gate Valve'),
        (p_project_id, 'CS Gate Valve 150# FLG', 3.0, 2, 'CS', 'Valves', 'Gate Valve'),
        (p_project_id, 'SS304 Ball Valve 300# FLG', 1.0, 4, 'SS304', 'Valves', 'Ball Valve'),
        (p_project_id, 'SS304 Ball Valve 300# FLG', 1.5, 2, 'SS304', 'Valves', 'Ball Valve'),
        (p_project_id, 'SS316 Check Valve 600# FLG', 2.0, 1, 'SS316', 'Valves', 'Check Valve');
        
        -- 5 Pipe records
        INSERT INTO public.atem_rfq (
            project_id, material_description, size1, quantity, material, rfq_scope, pipe_category
        ) VALUES
        (p_project_id, 'CS Pipe SCH 40 A', 2.0, 100, 'CS', 'Pipe', 'Pipe'),
        (p_project_id, 'CS Pipe SCH 40 A', 3.0, 80, 'CS', 'Pipe', 'Pipe'),
        (p_project_id, 'SS304 Seamless Pipe SCH 80 TP304', 1.5, 50, 'SS304', 'Pipe', 'Seamless Pipe'),
        (p_project_id, 'SS304 Seamless Pipe SCH 80 TP304', 2.0, 40, 'SS304', 'Pipe', 'Seamless Pipe'),
        (p_project_id, 'SS316 Welded Pipe SCH 10 TP316', 4.0, 60, 'SS316', 'Pipe', 'Welded Pipe');
        
        -- 5 Support records
        INSERT INTO public.atem_rfq (
            project_id, material_description, size1, quantity, material, rfq_scope
        ) VALUES
        (p_project_id, 'CS Pipe Shoe', 2.0, 20, 'CS', 'Supports'),
        (p_project_id, 'CS Pipe Shoe', 3.0, 15, 'CS', 'Supports'),
        (p_project_id, 'SS304 U-Bolt', 1.5, 30, 'SS304', 'Supports'),
        (p_project_id, 'SS304 U-Bolt', 2.0, 25, 'SS304', 'Supports'),
        (p_project_id, 'SS316 Riser Clamp', 4.0, 10, 'SS316', 'Supports');
    END;
    $$ LANGUAGE plpgsql;

    -- Run the minimal generator
    SELECT generate_minimal_test_data(2);
"""
    
GENERATE_BOM_DATA = """
    -- Insert 5 pages with 5 rows each directly into BOM table
    DO $$
    BEGIN
        -- Use project_id 3 and profile_id 2 as specified
        FOR page_num IN 1..5 LOOP
            -- Create common materials that vary by size
            INSERT INTO public.bom (
                pdf_id, project_id, profile_id, material_description, size, size1, size2,
                pos, quantity, component_category, general_category, rfq_scope,
                material, line_number, fitting_category
            ) VALUES
            -- Row 1: Pipe with different sizes per page
            (page_num, 3, 2, 
            'Carbon Steel Pipe, Seamless, SCH 40', page_num + 1, page_num + 1, NULL,
            '10', 12, 'Pipe', 'LF', 'Pipe',
            'Carbon Steel', 'LINE-' || page_num || '00', NULL),
            
            -- Row 2: 90° Elbow with different sizes per page
            (page_num, 3, 2, 
            '90° LR Elbow, 150#, SMLS', page_num + 0.5, page_num + 0.5, NULL,
            '20', 2, 'Fitting', 'Elbow', 'Fittings',
            'Carbon Steel', 'LINE-' || page_num || '00', '90 Long Radius'),
            
            -- Row 3: Tee with different sizes per page (compound sizes)
            (page_num, 3, 2, 
            'Tee, SCH 40, SMLS', page_num + 2, page_num + 2, page_num + 1,
            '30', 1, 'Fitting', 'Tee', 'Fittings',
            'Carbon Steel', 'LINE-' || page_num || '00', 'Tee'),
            
            -- Row 4: Flanged valve with different sizes per page
            (page_num, 3, 2, 
            'Gate Valve, 150#, Flanged', page_num + 1.5, page_num + 1.5, NULL,
            '40', 1, 'Valve', 'Flanged Valve', 'Valves',
            'Carbon Steel', 'LINE-' || page_num || '00', NULL),
            
            -- Row 5: Reducer with different sizes per page (compound sizes)
            (page_num, 3, 2, 
            'Concentric Reducer, SCH 40, SMLS', page_num + 3, page_num + 3, page_num + 1.5,
            '50', 1, 'Fitting', 'Reducer', 'Fittings',
            'Carbon Steel', 'LINE-' || page_num || '00', 'Reducer');
        END LOOP;
        
        -- Set some calculated values for testing
        /* UPDATE public.bom 
        SET calculated_eq_length = size1 * 1.5,
            calculated_area = CASE
                WHEN size2 IS NOT NULL THEN size1 * size2 * 3.14
                ELSE size1 * size1 * 3.14
            END
        WHERE project_id = 3; */
    END $$;

    -- Verify the data was inserted correctly
    SELECT pdf_id, profile_id, material_description, size1, size2, general_category, quantity
    FROM public.bom
    WHERE project_id = 3
    ORDER BY pdf_id, pos;
"""

MANUAL_SYNC_BOM_TO_RFQ = """
    -- Manual function to sync all existing BOM records to RFQ
    CREATE OR REPLACE FUNCTION sync_all_bom_to_rfq()
    RETURNS INTEGER AS $$
    DECLARE
        bom_rec RECORD;
        rfq_id INTEGER;
        existing_rfq_id INTEGER;
        counter INTEGER := 0;
    BEGIN
        -- Process each BOM record
        FOR bom_rec IN 
            SELECT * FROM public.bom 
            WHERE rfq_ref_id IS NULL
        LOOP
            -- Check if an RFQ with matching values already exists
            SELECT id INTO existing_rfq_id
            FROM public.atem_rfq
            WHERE project_id = bom_rec.project_id
            AND material_description = bom_rec.material_description
            AND (
                (COALESCE(size1, 0) = COALESCE(bom_rec.size1, 0) AND COALESCE(size2, 0) = COALESCE(bom_rec.size2, 0))
                OR (COALESCE(size1, 0) = COALESCE(bom_rec.size2, 0) AND COALESCE(size2, 0) = COALESCE(bom_rec.size1, 0))
            )
            LIMIT 1;
            
            IF existing_rfq_id IS NOT NULL THEN
                -- Link to existing RFQ record
                UPDATE public.bom
                SET rfq_ref_id = existing_rfq_id
                WHERE id = bom_rec.id;
            ELSE
                -- No matching RFQ exists, create a new one
                INSERT INTO public.atem_rfq (
                    project_id, profile_id, material_description, normalized_description,
                    size, size1, size2,
                    rfq_scope, general_category, unit_of_measure,
                    material, abbreviated_material, technical_standard,
                    astm, grade, rating, schedule, coating,
                    forging, ends, item_tag, tie_point,
                    pipe_category, valve_type, fitting_category,
                    weld_category, bolt_category, gasket_category,
                    calculated_eq_length, calculated_area,
                    notes, deleted, ignore_item,
                    created_by, updated_by
                ) VALUES (
                    bom_rec.project_id, bom_rec.profile_id, bom_rec.material_description, bom_rec.normalized_description,
                    bom_rec.size, bom_rec.size1, bom_rec.size2,
                    bom_rec.rfq_scope, bom_rec.general_category, bom_rec.unit_of_measure,
                    bom_rec.material, bom_rec.abbreviated_material, bom_rec.technical_standard,
                    bom_rec.astm, bom_rec.grade, bom_rec.rating, bom_rec.schedule, bom_rec.coating,
                    bom_rec.forging, bom_rec.ends, bom_rec.item_tag, bom_rec.tie_point,
                    bom_rec.pipe_category, bom_rec.valve_type, bom_rec.fitting_category,
                    bom_rec.weld_category, bom_rec.bolt_category, bom_rec.gasket_category,
                    bom_rec.calculated_eq_length, bom_rec.calculated_area,
                    bom_rec.notes, bom_rec.deleted, bom_rec.ignore_item,
                    bom_rec.created_by, bom_rec.updated_by
                )
                RETURNING id INTO rfq_id;
                
                -- Link the BOM record to the new RFQ record
                UPDATE public.bom
                SET rfq_ref_id = rfq_id
                WHERE id = bom_rec.id;
            END IF;
            
            counter := counter + 1;
        END LOOP;
        
        RETURN counter;
    END;
    $$ LANGUAGE plpgsql;

    -- Run the sync function
    SELECT sync_all_bom_to_rfq();
"""

FORCE_BOM_UPDATE = """
    -- Force update of all BOM records
    UPDATE public.bom
    SET updated_at = CURRENT_TIMESTAMP;
"""


INSERT_BOM_AND_RECORDS = """
    -- Drop existing triggers and functions
    DROP TRIGGER IF EXISTS trg_sync_bom_to_rfq_after ON public.bom;
    DROP FUNCTION IF EXISTS sync_bom_to_rfq_after();

    -- Create a simpler function with a more reliable recursion prevention technique
    CREATE OR REPLACE FUNCTION sync_bom_to_rfq_after()
    RETURNS TRIGGER AS $$
    DECLARE
        rfq_id INTEGER;
        existing_rfq_id INTEGER;
        old_refs_count INTEGER;
    BEGIN
        -- For insert operations, always create an RFQ
        IF TG_OP = 'INSERT' THEN
            -- Check if an RFQ with these values already exists
            SELECT id INTO existing_rfq_id
            FROM public.atem_rfq
            WHERE project_id = NEW.project_id
            AND material_description = NEW.material_description
            AND (
                (COALESCE(size1, 0) = COALESCE(NEW.size1, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size2, 0))
                OR (COALESCE(size1, 0) = COALESCE(NEW.size2, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size1, 0))
            )
            LIMIT 1;
            
            IF existing_rfq_id IS NOT NULL THEN
                -- RFQ exists, update the BOM directly in the database table
                -- This is critical: don't use NEW.rfq_ref_id := existing_rfq_id
                EXECUTE 'UPDATE public.bom SET rfq_ref_id = $1 WHERE id = $2'
                USING existing_rfq_id, NEW.id;
            ELSE
                -- No existing RFQ, create a new one
                INSERT INTO public.atem_rfq (
                    project_id, 
                    material_description, 
                    size, 
                    size1, 
                    size2,
                    quantity,
                    rfq_scope,
                    general_category,
                    unit_of_measure,
                    material,
                    rating,
                    ends,
                    fitting_category,
                    valve_type,
                    calculated_eq_length,
                    calculated_area
                ) VALUES (
                    NEW.project_id,
                    NEW.material_description,
                    NEW.size,
                    NEW.size1,
                    NEW.size2,
                    COALESCE(NEW.quantity, 1),
                    NEW.rfq_scope,
                    NEW.general_category,
                    NEW.unit_of_measure,
                    NEW.material,
                    NEW.rating,
                    NEW.ends,
                    NEW.fitting_category,
                    NEW.valve_type,
                    NEW.calculated_eq_length,
                    NEW.calculated_area
                )
                RETURNING id INTO rfq_id;
                
                -- Link BOM to new RFQ with a direct database update
                EXECUTE 'UPDATE public.bom SET rfq_ref_id = $1 WHERE id = $2'
                USING rfq_id, NEW.id;
            END IF;
        -- For update operations, only proceed if key fields have changed
        ELSIF TG_OP = 'UPDATE' AND 
            (NEW.material_description IS DISTINCT FROM OLD.material_description OR
            NEW.size IS DISTINCT FROM OLD.size OR
            NEW.size1 IS DISTINCT FROM OLD.size1 OR
            NEW.size2 IS DISTINCT FROM OLD.size2) THEN
            
            -- Check if an RFQ with the new values already exists
            SELECT id INTO existing_rfq_id
            FROM public.atem_rfq
            WHERE project_id = NEW.project_id
            AND material_description = NEW.material_description
            AND (
                (COALESCE(size1, 0) = COALESCE(NEW.size1, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size2, 0))
                OR (COALESCE(size1, 0) = COALESCE(NEW.size2, 0) AND COALESCE(size2, 0) = COALESCE(NEW.size1, 0))
            )
            LIMIT 1;
            
            IF existing_rfq_id IS NOT NULL AND NEW.rfq_ref_id IS DISTINCT FROM existing_rfq_id THEN
                -- RFQ exists and is different from current reference, update the BOM directly
                EXECUTE 'UPDATE public.bom SET rfq_ref_id = $1 WHERE id = $2'
                USING existing_rfq_id, NEW.id;
                
                -- Handle orphaned RFQ
                IF OLD.rfq_ref_id IS NOT NULL AND OLD.rfq_ref_id != existing_rfq_id THEN
                    SELECT COUNT(*) INTO old_refs_count
                    FROM public.bom 
                    WHERE rfq_ref_id = OLD.rfq_ref_id 
                    AND id != OLD.id;
                    
                    IF old_refs_count = 0 THEN
                        DELETE FROM public.atem_rfq WHERE id = OLD.rfq_ref_id;
                    END IF;
                END IF;
            ELSIF existing_rfq_id IS NULL THEN
                -- No matching RFQ exists, create a new one
                INSERT INTO public.atem_rfq (
                    project_id, 
                    material_description, 
                    size, 
                    size1, 
                    size2,
                    quantity,
                    rfq_scope,
                    general_category,
                    unit_of_measure,
                    material,
                    rating,
                    ends,
                    fitting_category,
                    valve_type,
                    calculated_eq_length,
                    calculated_area
                ) VALUES (
                    NEW.project_id,
                    NEW.material_description,
                    NEW.size,
                    NEW.size1,
                    NEW.size2,
                    COALESCE(NEW.quantity, 1),
                    NEW.rfq_scope,
                    NEW.general_category,
                    NEW.unit_of_measure,
                    NEW.material,
                    NEW.rating,
                    NEW.ends,
                    NEW.fitting_category,
                    NEW.valve_type,
                    NEW.calculated_eq_length,
                    NEW.calculated_area
                )
                RETURNING id INTO rfq_id;
                
                -- Link BOM to new RFQ with a direct database call
                EXECUTE 'UPDATE public.bom SET rfq_ref_id = $1 WHERE id = $2'
                USING rfq_id, NEW.id;
                
                -- Handle orphaned RFQ
                IF OLD.rfq_ref_id IS NOT NULL THEN
                    SELECT COUNT(*) INTO old_refs_count
                    FROM public.bom 
                    WHERE rfq_ref_id = OLD.rfq_ref_id 
                    AND id != OLD.id;
                    
                    IF old_refs_count = 0 THEN
                        DELETE FROM public.atem_rfq WHERE id = OLD.rfq_ref_id;
                    END IF;
                END IF;
            END IF;
        END IF;
        
        RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;


    -- Insert a test BOM record
    INSERT INTO public.bom (
        project_id, profile_id, material_description, size, size1, size2
    ) VALUES (
        3, 2, 'Test Material COMPLEX 2', '2x1', 2.0, 1.0
    );

    -- Insert 5 pages with 5 rows each directly into BOM table
        DO $$
        BEGIN
            -- Use project_id 3 and profile_id 2 as specified
            FOR page_num IN 1..5 LOOP
                -- Create common materials that vary by size
                INSERT INTO public.bom (
                    pdf_id, project_id, profile_id, material_description, size, size1, size2,
                    pos, quantity, component_category, general_category, rfq_scope,
                    material, line_number, fitting_category
                ) VALUES
                -- Row 1: Pipe with different sizes per page
                (page_num, 3, 2, 
                'Carbon Steel Pipe, Seamless, SCH 40', page_num + 1, page_num + 1, NULL,
                '10', 12, 'Pipe', 'LF', 'Pipe',
                'Carbon Steel', 'LINE-' || page_num || '00', NULL),
                
                -- Row 2: 90° Elbow with different sizes per page
                (page_num, 3, 2, 
                '90° LR Elbow, 150#, SMLS', page_num + 0.5, page_num + 0.5, NULL,
                '20', 2, 'Fitting', 'Elbow', 'Fittings',
                'Carbon Steel', 'LINE-' || page_num || '00', '90 Long Radius'),
                
                -- Row 3: Tee with different sizes per page (compound sizes)
                (page_num, 3, 2, 
                'Tee, SCH 40, SMLS', page_num + 2, page_num + 2, page_num + 1,
                '30', 1, 'Fitting', 'Tee', 'Fittings',
                'Carbon Steel', 'LINE-' || page_num || '00', 'Tee'),
                
                -- Row 4: Flanged valve with different sizes per page
                (page_num, 3, 2, 
                'Gate Valve, 150#, Flanged', page_num + 1.5, page_num + 1.5, NULL,
                '40', 1, 'Valve', 'Flanged Valve', 'Valves',
                'Carbon Steel', 'LINE-' || page_num || '00', NULL),
                
                -- Row 5: Reducer with different sizes per page (compound sizes)
                (page_num, 3, 2, 
                'Concentric Reducer, SCH 40, SMLS', page_num + 3, page_num + 3, page_num + 1.5,
                '50', 1, 'Fitting', 'Reducer', 'Fittings',
                'Carbon Steel', 'LINE-' || page_num || '00', 'Reducer');
            END LOOP;
            
            -- Set some calculated values for testing
            /* UPDATE public.bom 
            SET calculated_eq_length = size1 * 1.5,
                calculated_area = CASE
                    WHEN size2 IS NOT NULL THEN size1 * size2 * 3.14
                    ELSE size1 * size1 * 3.14
                END
            WHERE project_id = 3; */
        END $$;

        -- Verify the data was inserted correctly
        SELECT pdf_id, profile_id, material_description, size1, size2, general_category, quantity
        FROM public.bom
        WHERE project_id = 3
        ORDER BY pdf_id, pos;
    """

TEST_COMPONENT_MAPPING = """
    -- Function to test the trigger with various component values
    CREATE OR REPLACE FUNCTION test_component_mapping(
        p_record_id INTEGER, 
        p_component_type VARCHAR, 
        p_component_value VARCHAR
    ) RETURNS TABLE (
        id INTEGER,
        component_column VARCHAR,
        component_value VARCHAR,
        rfq_scope VARCHAR,
        general_category VARCHAR,
        mapping_not_found BOOLEAN
    ) AS $$
    DECLARE
        v_sql TEXT;
        v_record RECORD;
    BEGIN
        -- Get current values for comparison
        SELECT id, rfq_scope, general_category, mapping_not_found INTO v_record
        FROM public.atem_rfq_input
        WHERE id = p_record_id;
        
        -- Record the original state
        component_column := 'original';
        component_value := 'original';
        id := v_record.id;
        rfq_scope := v_record.rfq_scope;
        general_category := v_record.general_category;
        mapping_not_found := v_record.mapping_not_found;
        RETURN NEXT;
        
        -- Build and execute dynamic SQL to update the specific component column
        v_sql := format('
            UPDATE public.atem_rfq_input
            SET %I = %L
            WHERE id = %s
            RETURNING id, rfq_scope, general_category, mapping_not_found
        ', p_component_type, p_component_value, p_record_id);
        
        EXECUTE v_sql INTO v_record;
        
        -- Return the updated state
        component_column := p_component_type;
        component_value := p_component_value;
        id := v_record.id;
        rfq_scope := v_record.rfq_scope;
        general_category := v_record.general_category;
        mapping_not_found := v_record.mapping_not_found;
        RETURN NEXT;
        
        RETURN;
    END;
    $$ LANGUAGE plpgsql;

    -- Example usage:
    -- SELECT * FROM test_component_mapping(1, 'pipe_category', 'Carbon Steel Pipe');
    -- SELECT * FROM test_component_mapping(2, 'valve_type', 'Gate Valve');

"""

DROP_DUPLICATE_GENERAL_PDF_ID = """
    CREATE OR REPLACE FUNCTION remove_duplicate_pdf_records(p_project_id INTEGER)
    RETURNS INTEGER AS $$
    DECLARE
        deleted_count INTEGER;
    BEGIN
        -- Delete duplicates, keeping one record per pdf_id (the one with the lowest id)
        WITH numbered_rows AS (
            SELECT id, pdf_id, ROW_NUMBER() OVER(PARTITION BY pdf_id ORDER BY id) as rn
            FROM public.general
            WHERE project_id = p_project_id
        )
        DELETE FROM public.general 
        WHERE id IN (
            SELECT id FROM numbered_rows WHERE rn > 1
        )
        RETURNING COUNT(*) INTO deleted_count;
        
        -- Return the number of deleted records
        RETURN deleted_count;
    END;
    $$ LANGUAGE plpgsql;

    -- Example usage:
    -- SELECT remove_duplicate_pdf_records(1);
    -- Will return the number of deleted records for project_id 1
"""
DROP_PROJECT_ROWS = """
    -- CALL clear_project_tables(1)

    -- SELECT * FROM debug_bom_aggregation(1);

"""