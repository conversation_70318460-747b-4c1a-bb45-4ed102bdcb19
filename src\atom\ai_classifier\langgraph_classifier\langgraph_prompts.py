"""
Prompt Templates for LangGraph BOM Classification Workflow

This module provides focused, stage-specific prompt templates for the multi-stage
LangGraph classification workflow.
"""

from typing import Dict, List, Any, Optional

# Handle imports for both direct execution and module import
try:
    from ..prompts import categorization_table
except ImportError:
    # Direct execution or missing parent module - use mock data
    categorization_table = [
        {
            "field": "fitting_category",
            "options": ["Nipple", "90 LR Elbow", "45 Elbow", "Tee", "Reducer Concentric", "Cap"]
        }
    ]
    print("Warning: Using mock categorization_table - parent module not available")


def build_enhanced_material_analysis_prompt(material_description: str) -> str:
    """
    Build enhanced two-phase prompt for Stage 1: Material Analysis

    Phase 1: Detailed property extraction
    Phase 2: Category classification based on extracted properties
    """

    prompt = f"""You are a senior piping engineer analyzing material specifications. Perform a comprehensive two-phase analysis of this material description.

MATERIAL DESCRIPTION: "{material_description}"

🔍 CRITICAL ANALYSIS INSTRUCTIONS:
🚫 NEVER GUESS OR ASSUME information not explicitly stated
🚫 NEVER infer properties from general knowledge - only extract what is written
🚫 NEVER make assumptions about missing information
✅ ONLY extract properties that are explicitly mentioned in the text
✅ Use null for any information not clearly stated
✅ Indicate uncertainty when information is ambiguous

═══════════════════════════════════════════════════════════════════════════════
📋 PHASE 1: DETAILED PROPERTY EXTRACTION
═══════════════════════════════════════════════════════════════════════════════

Extract ALL technical details explicitly mentioned in the description:

🔧 BASIC ITEM IDENTIFICATION:
- item_type: Primary component type (Elbow, Tee, Valve, Flange, Pipe, etc.)
- item_subtype: Specific details (45°, 90°, Long Radius, Short Radius, Ball, Gate, WN, SO, etc.)

🧪 MATERIAL SPECIFICATIONS:
- material_grade: Material composition (316/316L SS, Carbon Steel, Duplex, etc.)
- astm_standard: ASTM designation (A403, A106, A182, A815, etc.)
- astm_grade: ASTM grade (WP316/316L, WPB, F304, WPS32750, etc.)

🏭 MANUFACTURING & TESTING:
- manufacturing_process: Process type (Welded, Seamless, Forged, WLD, SMLS, etc.)
- testing_requirements: Testing specs (100% X-Ray, RT, etc.)

🔗 PHYSICAL SPECIFICATIONS:
- end_connections: Connection types (BE, PE, SW, RF, TE, etc.)
- schedule_thickness: Wall thickness (SCH 10S, SCH 40, STD, XH, etc.)
- pressure_rating: Pressure class (150, 300, 600, 1500, 3000, etc.)

📏 STANDARDS & DIMENSIONS:
- ansme_ansi_standard: Standards (B16.9, B16.11, B16.5, etc.)
- primary_size: Main dimension (pipe diameter, etc.)
- secondary_size: Secondary dimension (for reducers, bolt length, etc.)

🎨 ADDITIONAL PROPERTIES:
- coating: Surface treatment (if mentioned)
- special_features: Any unique characteristics

═══════════════════════════════════════════════════════════════════════════════
📊 PHASE 2: CATEGORY CLASSIFICATION MAPPING
═══════════════════════════════════════════════════════════════════════════════

Based on the extracted properties, map to categorization table fields:

🎯 PRIMARY CATEGORIZATION:
- rfq_scope: ["Bolts","Fittings","Flanges","Gaskets","Miscellaneous","Pipe","Supports","Valves","Instruments"]
- unit_of_measure: ["Cubic Feet","EA","LBS","Linear Feet","Lot","Pair","Square Feet","Tons"]

🧪 MATERIAL MAPPING:
- material: ["Steel, Carbon","Steel, Stainless","Duplex","Bronze/Brass","Titanium","Monel","Inconel","Hastelloy", etc.]
- abbreviated_material: ["CS","SS","DSS","CuZn","TI","MON","Inconel","HAST", etc.]
- astm: Exact ASTM standard from description
- grade: Exact grade designation from description

🔧 PHYSICAL SPECIFICATIONS:
- ansme_ansi: Standard designation (B16.9, B16.11, B16.5, etc.)
- rating: Pressure rating number only (150, 300, 600, etc.)
- schedule: Exact schedule designation (10S, 40, 80, STD, etc.)
- forging: ["(SMLS) Seamless","Welded","Forged","Threaded", etc.]
- ends: Connection type (BE, PE, SW, RF, etc.)

📏 SIZE HANDLING:
- Size fields (size, size1, size2, quantity) are handled elsewhere in the pipeline
- Do NOT classify or include size fields in your response

🔧 CATEGORY-SPECIFIC FIELDS:
- fitting_category: Specific fitting type if rfq_scope is "Fittings"
- valve_type: Specific valve type if rfq_scope is "Valves"
- pipe_category: Specific pipe type if rfq_scope is "Pipe"

CLASSIFICATION PRIORITY RULES:
1. "Nipple" or "NPL" → rfq_scope: "Fittings", fitting_category: "Nipple"
2. "Elbow" → rfq_scope: "Fittings", fitting_category: "45 Elbow" or "90 LR Elbow" etc.
3. "Tee" → rfq_scope: "Fittings", fitting_category: "Equal Tee" or "Reducing Tee"
4. "Valve" → rfq_scope: "Valves", valve_type: specific type
5. "Flange" or "FLG" → rfq_scope: "Flanges", fitting_category: specific flange type
6. "Pipe" (not nipple) → rfq_scope: "Pipe", pipe_category: "Pipe"
7. "Expansion Joint" → rfq_scope: "Fittings", fitting_category: "Bellow" or "Bellow Reducing"

Respond with complete JSON structure:
{{
    "detailed_properties": {{
        "item_type": "extracted_or_null",
        "item_subtype": "extracted_or_null",
        "material_grade": "extracted_or_null",
        "astm_standard": "extracted_or_null",
        "astm_grade": "extracted_or_null",
        "manufacturing_process": "extracted_or_null",
        "testing_requirements": "extracted_or_null",
        "end_connections": "extracted_or_null",
        "schedule_thickness": "extracted_or_null",
        "pressure_rating": "extracted_or_null",
        "ansme_ansi_standard": "extracted_or_null",
        "primary_size": "extracted_or_null",
        "secondary_size": "extracted_or_null",
        "coating": "extracted_or_null",
        "special_features": "extracted_or_null"
    }},
    "category_mapping": {{
        "rfq_scope": "mapped_category",
        "unit_of_measure": "EA",
        "material": "mapped_material",
        "abbreviated_material": "mapped_abbrev",
        "astm": "mapped_astm",
        "grade": "mapped_grade",
        "ansme_ansi": "mapped_standard",
        "rating": "mapped_rating",
        "schedule": "mapped_schedule",
        "forging": "mapped_manufacturing",
        "ends": "mapped_ends",

        "fitting_category": "mapped_if_fitting",
        "valve_type": "mapped_if_valve",
        "pipe_category": "mapped_if_pipe"
    }},
    "primary_category": "legacy_category",
    "extracted_properties": {{}},
    "confidence": 0.95,
    "complexity_level": "standard",
    "reasoning": "Detailed explanation of property extraction and classification decisions"
}}"""

    return prompt


def build_material_analysis_prompt(material_description: str) -> str:
    """
    Legacy prompt function for backward compatibility
    """
    return build_enhanced_material_analysis_prompt(material_description)


def build_fitting_classification_prompt(
    material_description: str,
    extracted_properties: Dict[str, Any],
    category_intelligence: Dict[str, Any]
) -> str:
    """Build focused prompt for fitting classification"""
    
    # Get fitting-specific field options from categorization table
    fitting_options = {}
    for field_info in categorization_table:
        if field_info["field"] == "fitting_category":
            fitting_options = field_info["options"]
            break
    
    prompt = f"""You are a piping fittings classification expert. Classify this fitting component with high precision.

MATERIAL DESCRIPTION: "{material_description}"

EXTRACTED PROPERTIES: {extracted_properties}

FITTING CLASSIFICATION RULES:
1. CRITICAL RULES (always apply):
   - "Pipe Nipple", "NPL", "Nipple" → fitting_category: "Nipple"
   - "90 Elbow", "90°" → fitting_category: "90 LR Elbow" (default to LR unless SR specified)
   - "45 Elbow", "45°" → fitting_category: "45 Elbow"
   - "Tee" → fitting_category: "Tee" (or "Tee Reducing" if reducing mentioned)
   - "Reducer" → fitting_category: "Reducer Concentric" (default) or "Reducer Eccentric"
   - "Cap" → fitting_category: "Cap"
   - "Expansion Joint" → fitting_category: "Bellow" (or "Bellow Reducing" if reducing mentioned) 
   - "Bend" → fitting_category: "Bend"
   - "180 Return" → fitting_category: "Bend" (or "Bend Long Radius" if LR mentioned)

2. ABBREVIATION MEANINGS:
   - LR = Long Radius, SR = Short Radius
   - SO = Slip On (flange context), SW = Socket Weld
   - THRD = Threaded, BE = Beveled End, PE = Plain End

3. MATERIAL CLASSIFICATION:
   - ASTM A106 → material: "Steel, Carbon"
   - ASTM A234 → material: "Steel, Carbon" (for fittings)
   - ASTM A312 → material: "Steel, Stainless"
   - CS = Carbon Steel, SS = Stainless Steel

VALID FITTING CATEGORIES:
{fitting_options}

FIELD CLASSIFICATION ORDER:
1. rfq_scope: "Fittings" (always for fittings)
2. fitting_category: Select from valid options above
3. material: Based on ASTM or material indicators
4. astm: Extract ASTM standard if mentioned
5. grade: Material grade if specified
6. schedule: Pipe schedule if mentioned
7. rating: Pressure rating if mentioned
8. ends: End connection type
9. forging: Manufacturing process

NOTE: Do NOT classify size, size1, size2, or quantity fields - these are handled elsewhere.

Classify each field with confidence score. Respond in JSON format:
{{
    "rfq_scope": "Fittings",
    "fitting_category": "specific_category",
    "material": "material_type",
    "astm": "astm_standard",
    "grade": "grade_if_applicable",
    "schedule": "schedule_if_applicable",
    "rating": "rating_if_applicable",
    "ends": "end_type_if_applicable",
    "forging": "manufacturing_if_applicable",
    "field_confidence_scores": {{
        "rfq_scope": 0.98,
        "fitting_category": 0.95,
        "material": 0.90
    }},
    "overall_confidence": 0.94,
    "processing_notes": ["reasoning for key decisions"]
}}"""
    
    return prompt


def build_pipe_classification_prompt(
    material_description: str,
    extracted_properties: Dict[str, Any]
) -> str:
    """Build focused prompt for pipe classification"""
    
    prompt = f"""You are a piping classification expert. Classify this pipe component with high precision.

MATERIAL DESCRIPTION: "{material_description}"

EXTRACTED PROPERTIES: {extracted_properties}

PIPE CLASSIFICATION RULES:
1. CRITICAL EXCLUSIONS (NOT pipes):
   - Contains "Nipple", "NPL" → This is a FITTING, not pipe
   - Contains "Elbow", "Tee", "Reducer" → This is a FITTING, not pipe
   - Contains "Valve" → This is a VALVE, not pipe
   - Contains "Flange" → This is a FLANGE, not pipe
   - Contains "Bend" → This is a FITTING, not pipe
   - Contains "180 Return" → This is a "Bend" FITTING, not pipe 

2. PIPE INDICATORS:
   - "Pipe", "PIPE", length specifications
   - ASTM A106, A53 (carbon steel pipes)
   - ASTM A312 (stainless steel pipes)
   - Schedule specifications (SCH 40, SCH 80, etc.)

3. MATERIAL CLASSIFICATION:
   - ASTM A106 → material: "Steel, Carbon"
   - ASTM A53 → material: "Steel, Carbon"
   - ASTM A312 → material: "Steel, Stainless"
   - SMLS = Seamless, ERW = Electric Resistance Welded

4. SCHEDULE NORMALIZATION:
   - "SCH 40" → "40"
   - "Schedule 80" → "80"
   - "STD" → "STD"
   - "XH" → "XH"

FIELD CLASSIFICATION ORDER:
1. rfq_scope: "Pipe" (always for pipes)
2. pipe_category: Usually "Pipe"
3. material: Based on ASTM or material indicators
4. astm: Extract ASTM standard
5. grade: Material grade if specified
6. schedule: Pipe schedule (critical for pipes)
7. ends: End preparation (BE, PE, etc.)
8. forging: Manufacturing process (Seamless, ERW, etc.)

NOTE: Do NOT classify size, size1, size2, or quantity fields - these are handled elsewhere.

Respond in JSON format with confidence scores for each field."""
    
    return prompt


def build_qa_decision_prompt(
    material_description: str,
    current_classifications: Dict[str, Any],
    confidence_scores: Dict[str, float]
) -> str:
    """Build prompt for Q&A decision making on ambiguous classifications"""
    
    # Identify low confidence fields - handle None values properly
    low_confidence_fields = [
        field for field, confidence in confidence_scores.items()
        if confidence is not None and confidence < 0.8
    ]
    
    prompt = f"""You are a BOM classification quality assurance expert. Review these classifications and make decisions on ambiguous or low-confidence items.

MATERIAL DESCRIPTION: "{material_description}"

CURRENT CLASSIFICATIONS: {current_classifications}

CONFIDENCE SCORES: {confidence_scores}

LOW CONFIDENCE FIELDS: {low_confidence_fields}

Q&A DECISION RULES:
1. VENDOR CODE DETECTION:
   - Pattern: "VENDOR" + alphanumeric code
   - Decision: rfq_scope → "Miscellaneous"
   - Confidence: 0.9

2. ASTM-MATERIAL CONSISTENCY:
   - ASTM A106 + Material: Stainless → Force material: "Steel, Carbon"
   - ASTM A312 + Material: Carbon → Force material: "Steel, Stainless"
   - Confidence: 0.95

3. PIPE vs FITTING DISAMBIGUATION:
   - Contains "Pipe" AND "Nipple"
   - Confidence: 0.8

4. RATING FORMAT VALIDATION:
   - "300.0" → "3000" (remove decimal for whole numbers)
   - "600 PSI" → "600"
   - "Class 150" → "150"

For each decision made, provide:
- Field name
- Original value
- Corrected value
- Reasoning
- Confidence adjustment

Respond in JSON format:
{{
    "decisions_made": [
        {{
            "field": "field_name",
            "original_value": "original",
            "corrected_value": "corrected",
            "reasoning": "explanation",
            "confidence": 0.95
        }}
    ],
    "field_corrections": {{
        "field_name": "corrected_value"
    }},
    "confidence_adjustments": {{
        "field_name": 0.95
    }},
    "requires_human_review": false,
    "reasoning": "Summary of decisions made"
}}"""
    
    return prompt


def build_self_audit_prompt(
    material_description: str,
    final_classifications: Dict[str, Any],
    confidence_scores: Dict[str, float]
) -> str:
    """Build prompt for self-audit validation"""
    
    prompt = f"""You are a BOM classification auditor. Perform final validation of these classifications against business rules and cross-field consistency.

MATERIAL DESCRIPTION: "{material_description}"

FINAL CLASSIFICATIONS: {final_classifications}

CONFIDENCE SCORES: {confidence_scores}

VALIDATION RULES:
1. CROSS-FIELD VALIDATION:
   - ASTM A106 → material MUST be "Steel, Carbon"
   - ASTM A312 → material MUST be "Steel, Stainless"
   - rfq_scope "Pipe" → pipe_category should not be null
   - rfq_scope "Fittings" → fitting_category should not be null

2. LOGICAL CONSISTENCY:
   - Pipe schedule without pipe category → inconsistent
   - Valve type without valve scope → inconsistent
   - Flange face type without flange category → inconsistent

3. CONFIDENCE THRESHOLDS:
   - Overall confidence < 0.7 → requires human review
   - Critical field confidence < 0.8 → flag for review
   - Cross-field violations → confidence penalty

4. BUSINESS RULES:
   - No decimal points in rating values (3000.0 → 3000)
   - Standard abbreviations properly expanded
   - Required fields populated based on category
   - Expansion Joints is a 'Bellow' fitting type

Perform comprehensive audit and identify any issues:

Respond in JSON format:
{{
    "validation_status": "passed|issues_found|failed",
    "cross_field_violations": [
        {{
            "rule": "rule_name",
            "violation": "description",
            "affected_fields": ["field1", "field2"],
            "severity": "high|medium|low"
        }}
    ],
    "consistency_issues": [
        {{
            "issue": "description",
            "affected_field": "field_name",
            "recommendation": "suggested_fix"
        }}
    ],
    "confidence_assessment": {{
        "field_name": 0.95
    }},
    "recommended_corrections": {{
        "field_name": "corrected_value"
    }},
    "audit_summary": "Overall assessment and recommendations"
}}"""
    
    return prompt


if __name__ == "__main__":
    """Test prompt templates"""
    
    print("Testing LangGraph Prompt Templates")
    print("=" * 50)
    
    # Test material analysis prompt
    test_description = "Pipe Nipple 2\" SCH 40 ASTM A106 BE"
    analysis_prompt = build_material_analysis_prompt(test_description)
    
    print("✓ Material Analysis Prompt Generated")
    print(f"  Length: {len(analysis_prompt)} characters")
    print(f"  Contains category rules: {'Nipple' in analysis_prompt}")
    
    # Test fitting classification prompt
    extracted_props = {
        "astm": "A106",
        "size": "2",
        "schedule": "40",
        "ends": "BE"
    }
    
    fitting_prompt = build_fitting_classification_prompt(
        test_description,
        extracted_props,
        {}
    )
    
    print("\n✓ Fitting Classification Prompt Generated")
    print(f"  Length: {len(fitting_prompt)} characters")
    print(f"  Contains fitting rules: {'Nipple' in fitting_prompt}")
    
    # Test Q&A decision prompt
    current_classifications = {
        "rfq_scope": "Fittings",
        "fitting_category": "Nipple",
        "material": "Steel, Carbon"
    }
    
    confidence_scores = {
        "rfq_scope": 0.95,
        "fitting_category": 0.75,  # Low confidence
        "material": 0.90
    }
    
    qa_prompt = build_qa_decision_prompt(
        test_description,
        current_classifications,
        confidence_scores
    )
    
    print("\n✓ Q&A Decision Prompt Generated")
    print(f"  Length: {len(qa_prompt)} characters")
    print(f"  Identifies low confidence: {'fitting_category' in qa_prompt}")
    
    # Test self-audit prompt
    audit_prompt = build_self_audit_prompt(
        test_description,
        current_classifications,
        confidence_scores
    )
    
    print("\n✓ Self-Audit Prompt Generated")
    print(f"  Length: {len(audit_prompt)} characters")
    print(f"  Contains validation rules: {'ASTM A106' in audit_prompt}")
    
    print("\n" + "=" * 50)
    print("All prompt templates tested successfully!")
    print("Prompts are focused and category-specific as designed.")
