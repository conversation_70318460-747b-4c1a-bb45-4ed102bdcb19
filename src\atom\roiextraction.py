﻿'''
Worker - New Workflow

# PyMuPDF examples

https://artifex.com/blog/text-extraction-with-pymupdf
https://artifex.com/blog/table-recognition-extraction-from-pdfs-pymupdf-python
https://pymupdf.readthedocs.io/en/latest/recipes-text.html#how-to-extract-table-content-from-documents
https://pymupdf.readthedocs.io/en/latest/page.html#Page.find_tables
'''
import random
import time
import uuid
import threading
import platform
import os
import re
import json
import asyncio
import fitz
import multiprocessing
import statistics
import tempfile
import numpy as np
import pandas as pd

from openpyxl import Workbook
from openpyxl.styles import Font
from pprint import pprint as pp
from PySide6.QtWidgets import QApplication, QMessageBox
from pubsub import pub
from collections import defaultdict
from multiprocessing import Lock, Manager, Event
from threading import Thread
from natsort import natsort_keygen
from typing import Union
from collections import deque

from unit_tests.get_tables_test import get_table_data
from src.atom.fast_storage import save_df_fast, load_df_fast
from src.utils import convert_roi_payload
from src.utils.logger import logger
from src.atom.ai_processing import Gpt4Turbo, analyze_bom_data
from src.atom.dbManager import DatabaseManager
from src.atom.vision.ocr_patcher import reduce_regions, convert_to_pdf_aws
from src.app_paths import getSourceMissingRegionsPath, getSourceRawDataPath, getSourceDataDir, getSourceOcrPath, getSourceExtractionOptionsPath
# from src.atom.roiextraction_workarounds import patch_bboxes
from src.utils.spatial_utils import find_overlapping_rectangles_with_areas


# Increase the maximum number of columns displayed
pd.set_option('display.max_columns', None)

def _getDataTempPath(filename):
    return filename

def _savePagePixmap(page):
    return page


try:
    from src.app_paths import savePagePixmap
    from src.app_paths import getDataTempPath
except:
    # Testing, if cannot access app_paths, just return the filename
    getDataTempPath = _getDataTempPath
    savePagePixmap = _savePagePixmap

# This logger will inherit configurations from the root logger configured in main.py # Not inheriting logger correctly
# logger = logging.getLogger(__name__)

# Increase the maximum number of columns displayed
pd.set_option('display.max_columns', None)

# Set to limit the number of workers/processes for debugging
DEBUG_MODE = False # Exports data to xlsx

COMMIT_PDF = True

INSPECT_RAW = False # Export the raw data

PRINT_CONVERTED_COORDS = False # Print coordinates
EXPORT_OUTLIER = False

check_debug_log = {} # Test For debugging Check_update_row function (Line 1620 ish)

MULTIPROCESS = True

PATCH_REVISION_BBOX = False # workaround for spanned revision text


# Multiprocessing lock
lock = Lock()

# Global variable to store the document in each worker process
_process_doc = None

def init_worker(pdf_path: str) -> None:
    """Initialize worker process with PDF document.
    This runs once per worker process."""
    global _process_doc
    if not _process_doc:
        _process_doc = fitz.open(pdf_path)

def safe_literal_eval(coord_str):
    if isinstance(coord_str, np.ndarray):
        return tuple(coord_str)
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None


def upscale_pdf(doc: fitz.Document, output_path: str, scale_factor: float = 2.0):
    """
    Upscale a PDF document while maintaining quality and optimizing file size.

    Args:
        input_path: Path to input PDF file
        output_path: Path to save the upscaled PDF
        scale_factor: Scale factor for upscaling (default: 2.0)
    """
    # Create a new PDF for output
    out_doc = fitz.open()

    # Process each page
    for page in doc:
        # Create new page with scaled dimensions
        rect = page.rect
        new_rect = fitz.Rect(0, 0, rect.width * scale_factor, rect.height * scale_factor)
        out_page: fitz.Page = out_doc.new_page(width=new_rect.width, height=new_rect.height)

        # Create transformation matrix for scaling
        matrix = fitz.Matrix(scale_factor, scale_factor)

        # Copy content to new page with scaling
        out_page.show_pdf_page(new_rect, doc, page.number, matrix)

    # Save with optimization
    out_doc.save(output_path,
                 garbage=4,  # Maximum garbage collection
                 deflate=True,  # Use deflate compression
                 clean=True)  # Clean unused elements

    # Close documents
    doc.close()
    out_doc.close()

raw_df_patch = None


#############################################
#############################################
# Temporarily set the logger level to INFO
# #logger.setLevel(logging.INFO)

# # Create a console handler (if you haven't already)
# console_handler = logging.StreamHandler()

# # Optionally, set a formatter
# formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s | line: %(lineno)d') # Create formatter and add it to the handlers
# console_handler.setFormatter(formatter)

# # Add the handler to the logger
# logger.addHandler(console_handler)

#############################################
#############################################

TABLE_NAMES = ["BOM", "SPEC", "Spool"]

PAYLOAD_OPTIONS = {
    "aggregate_cols_general": ["elevation"],
    "keep_cols_general": ["sys_build", "sys_path", "sys_filename", "pdf_id", "pdf_page",
                          "source_filename", "modDate", "id_annot_info", "image_xref",
                          "image_path", "coordinates2", "min_elevation", "max_elevation",
                          "avg_elevation", "sys_layout_valid"],
    "merge_to_bom": [ "lineNumber", "drawing", "sheet", "area", "revision", "pid" ],
    "bom_merge_on": [ "sys_path", "pdf_page" ],
    "overlap_threshold": 0.2,
}


# Check if the operating system is Windows
if platform.system() == 'Windows':
    # Import launch_window only if on Windows
    logger.debug("Windows OS Detected. --Importing launch_window.py")
    try:
        from src.atom.dynamic_rename import launch_window
    except:
        from dynamic_rename import launch_window  # Older import


def check_bbox_overlap(bbox1, bbox2):
    """Check if two bounding boxes overlap"""
    x0_1, y0_1, x1_1, y1_1 = bbox1
    x0_2, y0_2, x1_2, y1_2 = bbox2

    # Check if one rectangle is to the left of the other
    if x1_1 < x0_2 or x1_2 < x0_1:
        return False

    # Check if one rectangle is above the other
    if y1_1 < y0_2 or y1_2 < y0_1:
        return False

    return True


class DataFrameHolder:
    def __init__(self):
        self.modified_df = None
        self.callback_received = threading.Event()

    def handle_modified_dataframe(self, df):
        self.modified_df = df
        logger.debug("Modified DataFrame received:")
        self.callback_received.set()  # Signal that the callback has been executed

    def wait_for_callback(self):
        self.callback_received.wait()  # Wait for the callback to be executed

def generate_unique_id(existing_ids, size=6):
    while True:
        new_id = random.randint(10**(size-1), (10**size)-1)
        if new_id not in existing_ids:
            return new_id

 #Used when importing General from xlsx
def merge_general_data(from_df, imported_df, columns_to_merge):
    """
    Merges specified columns from from_df into imported_df based on 'pdf_page',
    maintaining the row count of imported_df and generating unique ids for missing values.
    """
    # Ensure 'pdf_page' is of integer type
    from_df['pdf_page'] = pd.to_numeric(from_df['pdf_page'], errors='coerce').fillna(0).astype(int)
    imported_df['pdf_page'] = pd.to_numeric(imported_df['pdf_page'], errors='coerce').fillna(0).astype(int)

    # Replace 'nan', np.nan, and pd.NA with empty strings in both dataframes
    from_df = from_df.replace(['nan', np.nan, pd.NA], '')
    imported_df = imported_df.replace(['nan', np.nan, pd.NA], '')

    # Create a dataframe with only the columns to merge
    merge_df = from_df[['pdf_page'] + columns_to_merge].drop_duplicates('pdf_page')

    # Perform the merge operation
    merged_df = imported_df.merge(merge_df, on='pdf_page', how='left')

    # Fill NaN values with empty strings
    merged_df = merged_df.fillna('')

    # Ensure the data types are correct
    for col in columns_to_merge:
        if from_df[col].dtype == 'int64' or from_df[col].dtype == 'float64':
            merged_df[col] = pd.to_numeric(merged_df[col], errors='coerce').fillna(0).astype(int)
        else:
            merged_df[col] = merged_df[col].astype(from_df[col].dtype)

    # Get the highest pdf_id from from_df
    highest_pdf_id = from_df['pdf_id'].max()

    # Generate unique ids for missing pdf_id starting from highest_pdf_id + 1
    next_pdf_id = highest_pdf_id + 1
    for index, row in merged_df.iterrows():
        if row['pdf_id'] == 0 or row['pdf_id'] == '':
            merged_df.at[index, 'pdf_id'] = next_pdf_id
            next_pdf_id += 1

    return merged_df

def merge_bom_data(from_df, imported_df): # Used when importing BOM from xlsx
    '''
    Used to merge and imported BOM Dataframe (Created with Excel)
    This is necessary when performing operations outside of the app so that we can still run our Classifier
    '''

    db_manager = DatabaseManager() # Connect to database

    # Replace 'nan', np.nan, and pd.NA with empty strings in from_df
    from_df = from_df.replace(['nan', np.nan, pd.NA], '')

    # Perform the merge operation
    merged_df = pd.merge(imported_df,
                         from_df[['pdf_page', 'sys_build', 'sys_path', 'pdf_id']],
                         on='pdf_page',
                         how='left')



    return merged_df

# Function to replace non-standard characters with standard ASCII ones
def replace_non_standard_characters(text):
    #print("\n\nREPLACE FUNCTION ENTERED")
    replacements = {
        '\u00A0': ' ',  # Non-breaking space
        '\u2019': "'",  # Right single quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u2018': "'",  # Left single quotation mark
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
        '\u2026': '...',  # Ellipsis
        '\u00B7': '-',  # Middle dot
        '\u00AB': '"',  # Left-pointing double angle quotation mark
        '\u00BB': '"',  # Right-pointing double angle quotation mark
        '\u00E9': 'e',  # e with acute
        '\u00E8': 'e',  # e with grave
        '\u00EA': 'e',  # e with circumflex
        '\u00F4': 'o',  # o with circumflex
        '\u00F6': 'o',  # o with diaeresis
        '\u00E0': 'a',  # a with grave
        '\u00E2': 'a',  # a with circumflex
        '\u00FB': 'u',  # u with circumflex
        '\u00F9': 'u',  # u with grave
        '\u00FC': 'u'   # u with diaeresis
    }
    for non_standard, standard in replacements.items():
        #if non_standard in text:
            #print(f"\n\nReplacing '{non_standard}' with '{standard}' in text: {text}")
        text = text.replace(non_standard, standard)
    return text

def confirm_analysis():
    app = QApplication.instance()  # Check if there is already a running QApplication instance
    created_app = False
    if not app:  # If no instance exists, create a new one
        app = QApplication([])
        created_app = True

    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle("Confirm Analysis")
    msg_box.setText("Do you want to run smart analysis on your extracted \"Bill of Materials\"?")
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)
    response = msg_box.exec()

    if created_app:
        app.exit()  # Properly terminate the app you created

    return response == QMessageBox.Yes

def parse_complex_size(value):
    """Parse complex size values that may include decimals followed by fractions. i.e '1.1/2' (1.5) """
    try:
        # Remove any non-numeric characters (except for decimal point, forward slash, and hyphen)
        value = re.sub(r'[^0-9./\-]', '', str(value))

        # Remove any leading or trailing "." or "," characters
        value = value.strip(".,")

        if '.' in value and '/' in value:
            # Handle cases where decimal and fraction are combined, e.g., "1.1/2"
            decimal_part, fraction_part = value.split('.')
            fraction_str = fraction_part.split('/')[0] + '/' + fraction_part.split('/')[1]
            fraction = convert_fraction_to_float(fraction_str)
            return float(decimal_part) + fraction
        else:
            # Handling cases without combined decimals
            parts = re.findall(r'(\d+/\d+|\d+\.\d+|\d+)', value)
            if parts:
                return sum(convert_fraction_to_float(part) for part in parts)
        return np.nan
    except Exception as e:
        logger.error(f"Failed to parse complex size. Value: '{value}'. Error: {e}", exc_info=True)
        return np.nan

def convert_fraction_to_float(fraction_str):
    try:
        if '/' in fraction_str:
            numerator, denominator = fraction_str.split('/')
            return float(numerator) / float(denominator)
        else:
            return float(fraction_str)
    except ValueError:
        return float(fraction_str)


def parse_elevation(elevation_str, pdf_id, convert_mm_to_feet=False, convert_m_to_feet=True):
    # Check if the string contains feet (') or inches (") symbols
    original_elevation_str = elevation_str
    if "'" in elevation_str or '"' in elevation_str or '-' in elevation_str:  # Added check for hyphen
        # Handle standard format elevations
        # Check if the string does not start with "E" or "EL" (case-insensitive)
        if not re.match(r'^[EeZzFf][Ll]\.?\s*', elevation_str):  # Added \.?\s* to match "EL." with optional space
            return float('nan')

        try:
            # Remove 'E', 'EL', or 'EL.' prefix, any trailing double quotes
            elevation_str = re.sub(r'[EeZzFf][Ll]\.?\s*|\s*\"', '', elevation_str).strip()
            sign = -1 if elevation_str.startswith('-') else 1
            elevation_str = elevation_str.lstrip('+-')

            # Handle formats with and without foot symbol
            if "'" in elevation_str:
                parts = elevation_str.split("'")
                feet = int(parts[0]) if parts[0] else 0
                inch_part = parts[1].strip(' -"') if len(parts) > 1 else '0'
            else:
                # Handle format like "8-7 1/2" (8 feet 7.5 inches)
                parts = elevation_str.split('-')
                feet = int(parts[0]) if parts[0] else 0
                inch_part = parts[1].strip(' -"') if len(parts) > 1 else '0'

            inches = 0
            fraction = 0

            # Parse inches and fractions if present
            if ' ' in inch_part:  # Format: "7 1/2"
                whole_inches, fraction_str = inch_part.split()
                whole_inches = whole_inches.replace(".", "")
                inches = int(whole_inches)
                if '/' in fraction_str:
                    num, denom = map(int, fraction_str.split('/'))
                    fraction = num / denom
            else:  # Format: "7" or "1/2"
                if '/' in inch_part:  # Just a fraction
                    inch_part_split = inch_part.split('/')
                    num, denom = inch_part_split
                    if "." in num: # Format: "4.3/16"
                        inches, num = map(int, num.split("."))
                    else:
                        num = int(num)
                    denom = int(denom)
                    fraction = num / denom
                else:  # Just whole inches
                    if inch_part:  # Check if inch_part is not empty
                        inches = int(inch_part)
                    # If inch_part is empty, inches remains 0

            # Calculate total inches and convert to decimal feet
            total_inches = feet * 12 + inches + fraction
            decimal_feet = sign * total_inches / 12
            return decimal_feet

        except Exception as e:
            logger.warning(f"Error parsing elevation: PDF ID: {pdf_id}, {original_elevation_str} - {e}", exc_info=True)
            return float('nan')
    else:
        # logger.debug(f"Elevation '{elevation_str}' is in metric format")
        try:
            # Remove 'E', 'EL', or 'Z' prefix and any trailing whitespace
            elevation_str = re.sub(r'[EeZz][Ll]?', '', elevation_str).strip()

            # Add check for 'nan'
            if elevation_str.lower() == 'nan':
                return float('nan')

            # Convert the elevation to millimeters
            # millimeters = int(elevation_str)

            # Convert the elevation to millimeters
            value  = float(elevation_str)  # <-- Changed to float()

            print(f"Parsed numeric value: {value}")

            if convert_mm_to_feet:
                # Convert millimeters to feet
                decimal_feet = value / 304.8
                return decimal_feet

            elif convert_m_to_feet:
                # Convert millimeters to feet
                decimal_feet = value * 0.328084
                # print(f"Converting from m to feet: {value} m = {decimal_feet} feet")
                return decimal_feet
            else:
                # print(f"No conversion performed, returning original value: {value}")
                # Return the value in millimeters
                return value

        except Exception as e:
            logger.error(f"Error parsing elevation (metric): PDF ID: {pdf_id} {elevation_str} - {e}", exc_info=True)
            return float('nan')  # Return NaN if there's an error

def calculate_elevation_metrics(row, pdf_id=0, convert_mm_to_feet=False, convert_m_to_feet=False):
    try:
        elevation_values = row['elevation']
    except Exception as e:
        logger.warning(f"'elevation' field not found in table {e}", exc_info=True)
        return 0, 0, 0
    try:

        elevation_strings = elevation_values.split(';') #row['elevation'].split(';')
        elevations = [parse_elevation(el.strip(), pdf_id, convert_mm_to_feet=convert_mm_to_feet, convert_m_to_feet=convert_m_to_feet) for el in elevation_strings if el.strip()]

        # logger.debug(f"Parsed elevations for page {pdf_id}: {elevations}")

        # Calculate min, max, and average elevations in decimal feet
        min_elevation = f"{min(elevations):.2f}" if elevations else '' #None
        max_elevation = f"{max(elevations):.2f}" if elevations else '' #None
        avg_elevation = f"{sum(elevations) / len(elevations):.2f}" if elevations else ''#None

        # logger.debug(f"Page {pdf_id} - Min: {min_elevation}, Max: {max_elevation}, Avg: {avg_elevation}")

        return min_elevation, max_elevation, avg_elevation
    except Exception as e:
        logger.error(f"Error in calculate_elevation_metrics. Elevation values: {elevation_values} -- {e}", exc_info=True)
        return 0, 0, 0

def trim_all_columns(df):
    """
    Trims leading and trailing whitespaces from all string values in all columns of the DataFrame.
    """
    trim_strings = lambda x: x.strip() if isinstance(x, str) else x
    return df.map(trim_strings) #return df.applymap(trim_strings)

# Function to trim all columns in a DataFrame
def trim_all_columns2(df):
    return df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

def remove_outliers_from_bom(bom_data, outlier_data):
    for outlier in outlier_data:
        # Extract identifiers from the outlier row
        outlier_text = outlier.get("Text", "")
        outlier_page = outlier.get("PdfPage", "")
        outlier_path = outlier.get("PdfPath", "")

        # Create a condition to match the relevant rows in the BoM data
        bom_data = [row for row in bom_data if not (
            row.get("PdfPage", "") == outlier_page and
            row.get("PdfPath", "") == outlier_path and
            (outlier_text in row.get("Text", "") or row.get("Text", "") in outlier_text)
        )]

    return bom_data

def get_correct_path(path):
    logger.debug("Checking OS")
    if os.name != 'nt':
        # Convert Windows-style paths to Linux-style paths
        # This assumes that the path starts with a Windows drive letter (e.g., C:/)
        path = re.sub(r'^[a-zA-Z]:', '', path)  # Remove drive letter
        path = path.replace('\\', '/')  # Replace backslashes with forward slashes
        path = '/app' + path  # Prefix with the base directory used in Docker
    return path

# Function to split the PDF into individual pages with unique temporary IDs
def split_pdf(pdf_path, output_dir, dataframe, page_limit=None):
    # logger.debug("Splitting PDF Pages")
    with fitz.open(pdf_path) as doc:
        max_page = range(len(doc)) if page_limit is None else range(min(page_limit, len(doc)))

        # Generate unique IDs only for the pages being processed
        unique_ids = [str(uuid.uuid4()) for _ in max_page]
        dataframe = dataframe.iloc[list(max_page)].copy()
        dataframe['sys_AISdocID'] = unique_ids

        for page_num in max_page:
            page = doc.load_page(page_num)
            page_pdf = fitz.open()

            page_pdf.insert_pdf(doc, from_page=page_num, to_page=page_num)

            # Use the unique ID from the dataframe for the filename
            unique_id = dataframe.iloc[page_num - max_page.start]['sys_AISdocID']
            filename = f"{unique_id}.pdf"
            page_pdf.save(os.path.join(output_dir, filename))
            page_pdf.close()

    return dataframe

def convert_tuple_to_string(row):
    return [str(item) if isinstance(item, tuple) else item for item in row]

def is_scalar(value):
    # Function to check if a value is a scalar (can be written directly to Excel)
    return isinstance(value, (int, float, str, bool)) or value is None

def clean_row_values(row_values):
    return [remove_illegal_xml_characters(str(cell)) if isinstance(cell, str) else cell for cell in row_values]

def remove_illegal_xml_characters(input_str):
    # XML 1.0 legal characters:
    # - Any Unicode character, excluding the surrogate blocks, FFFE, and FFFF.
    # - The legal characters are Tab (0x09), LF (0x0A), CR (0x0D),
    #   and characters in the range 0x20 to 0xD7FF, 0xE000 to 0xFFFD, 0x10000 to 0x10FFFF.
    # This regex matches any character not in the ranges above.
    illegal_xml_chars_re = re.compile('[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]+')
    return illegal_xml_chars_re.sub('', input_str)

def export_large_data_to_excel(df, filename, directory):
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

    excel_filename = os.path.join(directory, filename)

    if len(df) > 0:
        logger.debug(f"Creating Workbook: {filename} in {directory} with {len(df)} rows")
        wb = Workbook()
        ws = wb.active

        # Adding header row (column names)
        ws.append(list(df.columns))

        ais_link_col_idx = df.columns.get_loc('AIS_LINK') + 1 if 'AIS_LINK' in df.columns else None
        hyperlink_font = Font(color="0000FF", underline="single")

        for index, row in df.iterrows():
            row_values = [str(value) if not is_scalar(value) else value for value in row]
            row_values_cleaned = clean_row_values(row_values)
            ws.append(row_values_cleaned)
            #ws.append(row_values)

            # Create a hyperlink for 'AIS_LINK'
            if ais_link_col_idx:
                ais_link_value = row['AIS_LINK']

                # Use the relative path directly
                if ais_link_value:
                    hyperlink = ais_link_value
                    cell = ws.cell(row=index + 2, column=ais_link_col_idx)
                    cell.hyperlink = hyperlink
                    cell.font = hyperlink_font

        try:
            wb.save(excel_filename)
            logger.debug(f">>> Data exported and saved to: {excel_filename}")
        except PermissionError as e:
            logger.error(f"PermissionError: {e}")
            logger.error(f"Failed to write to {excel_filename}. The file might be open or locked.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
    else:
        logger.debug(f"The DataFrame for {filename} is empty.")

def load_json(file_path):
    try:
        with open(file_path, 'r') as file:
            logger.info("Loaded Json: %s", file_path)
            return json.load(file)

    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {file_path}: {e}")
        return None  # or return an empty dict {}
    except FileNotFoundError as e:
        logger.error(f"File not found: {file_path}: {e}")
        return None  # or return an empty dict {}

def update_value_type(value, patterns):
    for category, category_patterns in patterns.items():
        for pattern in category_patterns:
            if re.match(pattern, value):
                return category, value
    return '', ''

def string_to_tuple(coords):
    if isinstance(coords, tuple):
        # It's already a tuple, return it as is
        return coords
    elif isinstance(coords, ):
        # It's already a tuple, return it as is
        return coords
    try:
        # Assuming the input is a string that needs to be converted to a tuple
        coords = coords.strip("() ")
        return tuple(map(float, coords.split(', ')))
    except (ValueError, SyntaxError):
        # Return a default value that indicates invalid coordinates
        return (0, 0, 0, 0)


def convert_coords_to_tuple(df): # Original
    try:
        # Convert the string representation of coordinates to actual tuples
        df['coordinates2'] = df['coordinates2'].apply(string_to_tuple)
        df['coordinates'] = df['coordinates'].apply(string_to_tuple)

        # Update 'coordinates2' if it's (0,0,0,0). Annotations are stored in 'coordinates'
        df.loc[df['coordinates2'] == (0, 0, 0, 0), 'coordinates2'] = df['coordinates']
    except Exception as e:
        logger.error(f"Error converting string to tuple: {e}", exc_info=True)
        return pd.DataFrame()
    return df


def convert_relative_coords_to_points(roi_payload, width, height, x_offset=0, y_offset=0):

    try:
        if isinstance(roi_payload, str):
            roi_payload = json.loads(roi_payload)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ROI payload: {e}")
        return []

    converted_payload = []

    for item in roi_payload:
        try:
            converted_item = {"columnName": item.get("columnName", "Unknown")}
            process_coordinates(item, converted_item, width, height, x_offset, y_offset)
            if "headersSelected" in item:
                converted_item["headersSelected"] = item["headersSelected"]

            converted_payload.append(converted_item)
        except Exception as e:
            logger.error(f"Error processing item {item.get('columnName', 'Unknown')}: {e}")

    if PRINT_CONVERTED_COORDS:
        print('\n\nCONVERTED COORDS')
        pp(converted_payload)

    return converted_payload

def process_coordinates(item, converted_item, width, height, x_offset, y_offset):
    if "tableCoordinates" in item:
        tc = item["tableCoordinates"]
        converted_item["tableCoordinates"] = convert_coords(tc, width, height, x_offset, y_offset)

    if "tableColumns" in item:
        converted_columns = []
        for column in item["tableColumns"]:
            for column_name, coords in column.items():
                converted_column = {
                    column_name: convert_coords(coords, width, height, x_offset, y_offset)
                }
                converted_columns.append(converted_column)
        converted_item["tableColumns"] = converted_columns

    if "relativeX0" in item and not "tableColumns" in item:  # Adjusted to avoid double-processing
        converted_item.update(convert_coords(item, width, height, x_offset, y_offset))

def convert_coords(coords, width, height, x_offset, y_offset):
    return {
        "x0": coords["relativeX0"] * width + x_offset,
        "y0": coords["relativeY0"] * height + y_offset,
        "x1": coords["relativeX1"] * width + x_offset,
        "y1": coords["relativeY1"] * height + y_offset
    }

def calculate_overlap_area(box1, box2):
    # Calculate the overlapping area between two rectangles
    x_left = max(box1[0], box2[0])
    y_top = max(box1[1], box2[1])
    x_right = min(box1[2], box2[2])
    y_bottom = min(box1[3], box2[3])

    if x_right < x_left or y_bottom < y_top:
        return 0.0  # No overlap
    return (x_right - x_left) * (y_bottom - y_top)

def draw_coordinates_on_page(document, page_number, coordinates_list, sub_dir, x_offset=0, y_offset=0):
    try:
        page = document.load_page(page_number)

        rect = page.rect  # Get the page dimensions
        page_width, page_height = rect.width, rect.height

    except Exception as e:
        logger.error(f"Failed to load page {page_number}: {e}")
        return None

    for coordinates in coordinates_list:
        if "tableCoordinates" in coordinates:
            # logger.debug("Drawing table coordinates")
            # Convert and draw table rectangle using page dimensions
            draw_rectangle(page, coordinates["tableCoordinates"], page_width, page_height, x_offset, y_offset)

            # Draw rectangles for each column in the table
            for column_dict in coordinates.get("tableColumns", []):
                for column_name, column_coords in column_dict.items():
                    # logger.debug(f"Drawing column: {column_name}")
                    # Convert and draw column rectangle using page dimensions
                    draw_rectangle(page, column_coords, page_width, page_height, x_offset, y_offset)
        else:
            # It's a single coordinate entry, not a table
            #logger.debug(f"Drawing coordinate: {coordinates.get('columnName')}")
            # Convert and draw single rectangle using page dimensions
            draw_rectangle(page, coordinates, page_width, page_height, x_offset, y_offset)

    # Save the page as an image for inspection
    try:
        image = page.get_pixmap()
        image_path = os.path.join(sub_dir, 'validate.png')
        image.save(image_path)
        logger.info(f"Image saved to {image_path}")
        return image_path
    except Exception as e:
        logger.error(f"Error saving the image: {e}")
        return None

def draw_rectangle(page, coordinates, page_width, page_height, x_offset=0, y_offset=0):
    try:
        # Scale the relative coordinates to the page dimensions
        x0 = (coordinates["relativeX0"] * page_width) + x_offset
        y0 = (coordinates["relativeY0"] * page_height) + y_offset
        x1 = (coordinates["relativeX1"] * page_width) + x_offset
        y1 = (coordinates["relativeY1"] * page_height) + y_offset

        rect = fitz.Rect(x0, y0, x1, y1)
        page.draw_rect(rect, color=(1, 0, 0), width=1.5)  # Draw red rectangle
        #logger.debug(f"Rectangle drawn with coordinates: {x0}, {y0}, {x1}, {y1}")
    except KeyError as e:
        logger.error(f"Missing coordinate key in {coordinates}: {e}")
    except Exception as e:
        logger.error(f"Error drawing rectangle: {e}")

# --------> Worker & Helper Functions
def adjust_bbox(bbox, rotation_matrix):
    """
    Adjust a bounding box based on the page's rotation matrix.

    Parameters:
    - bbox: The original bounding box (x0, y0, x1, y1).
    - rotation_matrix: The matrix used to transform points based on the page's rotation.

    Returns:
    - The adjusted bounding box as a tuple (x0, y0, x1, y1).
    """
    # Extract the bounding box corners as points
    p1 = fitz.Point(bbox[0], bbox[1])  # Top-left
    p2 = fitz.Point(bbox[2], bbox[3])  # Bottom-right

    # Apply the rotation matrix to each point
    p1_transformed = p1 * rotation_matrix
    p2_transformed = p2 * rotation_matrix

    # Ensure x1 > x0 and y1 > y0
    x0, x1 = sorted([p1_transformed.x, p2_transformed.x])
    y0, y1 = sorted([p1_transformed.y, p2_transformed.y])

    # Construct and return the adjusted bounding box with corrected order
    adjusted_bbox = (x0, y0, x1, y1)
    return adjusted_bbox

def get_structured_table(table_type, structured_tables):
    """
    Retrieves a specific table from the structured tables dictionary,
    accounting for case-insensitivity and blank tables.

    Parameters:
    - table_type (str): The type of table to retrieve (e.g., 'BOM', 'SPEC', 'Spool').
    - structured_tables (dict): A dictionary of structured tables,
                                where keys are table types and values are DataFrames.

    Returns:
    - pd.DataFrame: The DataFrame for the requested table type, or an empty DataFrame
                    if the table does not exist or is blank.
    """
    # Convert the table_type to lowercase to ensure case-insensitive matching
    table_type = table_type.lower()

    # Search for the table in the structured tables dictionary
    for key, table_df in structured_tables.items():
        if key.lower() == table_type:
            # Check if the table DataFrame is not empty
            if not table_df.empty:
                return table_df
            else:
                # Return an empty DataFrame if the table exists but is blank
                #logger.info(f"The '{table_type}' table exists but is blank.")
                return pd.DataFrame()

    # Return an empty DataFrame if the table type was not found
    logger.info(f"No '{table_type}' table was found.")
    return pd.DataFrame()

def clean_for_json(data):
    if isinstance(data, dict):
        return {key: clean_for_json(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [clean_for_json(item) for item in data]
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        return str(data)  # Convert any other types to string


def process_table_rois(pdf_path: str,
                       raw_data: pd.DataFrame,
                       page: fitz.Page,
                       page_number: int,
                       converted_roi_payload: dict):
    """
    Refactored process_table_rois()

    Args:

    Returns:
        annot_tables:
        text_tables:
        outlier_df:
        annot_outlier_df:

    """
    structured_text_tables = pd.DataFrame()
    structured_annot_tables = pd.DataFrame()
    outlier_df = pd.DataFrame()

    try:
        logger.debug(f"\n\n--> Extracting tables from page {page_number}")
        structured_text_tables, structured_annot_tables, outlier_df = get_table_data(pdf_path,
                                                                                    page,
                                                                                    page_number,
                                                                                    converted_roi_payload,
                                                                                    raw_data)

        # Ensure normalized table names
        structured_text_tables = {k.lower(): v for k, v in structured_text_tables.items()}
        structured_annot_tables = {k.lower(): v for k, v in structured_annot_tables.items()}

        # for table_name, df in structured_text_tables.items():
        #     print(table_name)
        #     print(df.head(2))

    except Exception as e:
        #logger.error(f"An error occurred in get_table_data_2: {e}")
        logger.exception(e)
        logger.error(f"Error getting table data on page {page_number + 1}", exc_info=True)

    # print(f"\n\nprocess_table_rois (combined_annot_tables): {combined_annot_tables}")
    # print(f"\n\nprocess_table_rois:(combined_annot_outlier_df): {combined_annot_outlier_df}")

    return structured_annot_tables, structured_text_tables, outlier_df


def format_value_with_colors(value, stroke_color, font_color, font_name, font_style, font_size):
    formatted_value = value if value else ''
    attributes = []
    try:
        attributes.append(f"stroke:{format_color(stroke_color)}")
    except:
        pass
    try:
        attributes.append(f"font:{format_color(font_color)}")
    except:
        pass
    try:
        attributes.append(f"font-name:{font_name}")
    except:
        pass
    try:
        attributes.append(f"font-style:{font_style}")
    except:
        pass
    try:
        attributes.append(f"font-size:{font_size}")
    except:
        pass

    if attributes:
        formatted_value += f"-[{','.join(attributes)}]"
    return formatted_value

def format_color(color):
    if isinstance(color, list) and len(color) == 3:
        rgb = [int(c * 255) for c in color]
        return f"rgb({rgb[0]},{rgb[1]},{rgb[2]})"
    else:
        return str(color)

def is_inside(inner_coords, outer_coords):
    ix0, iy0, ix1, iy1 = inner_coords
    ox0, oy0, ox1, oy1 = outer_coords
    return ox0 <= ix0 and ix1 <= ox1 and oy0 <= iy0 and iy1 <= oy1

def safe_literal_eval(coord_str):
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    elif isinstance(coord_str, np.ndarray):
        return tuple(map(float, coord_str))
    # print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

def safe_parse_color(color_str):
    try:
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", color_str)
        return tuple(float(num) for num in numbers)
    except:
        pass

    try:
        return tuple(map(float, color_str))
    except:
        return color_str

def process_extracted_annot_data(df):
    """Builds 'annot_markups' dictionary"""

    if DEBUG_MODE:
        df.to_excel(getDataTempPath("annot_element_data.xlsx"))

    if df.empty:
        return pd.DataFrame()
    # Filter out both Text and OCR types
    df = df[~df['type'].isin(['Text', 'OCR'])]

    # Separate Text Box annotations
    text_boxes = df[df['type'] == 'Text Box'].copy()
    other_annots = df[df['type'] != 'Text Box'].copy()

    # Safely convert coordinates2 to tuples of floats
    text_boxes['coordinates2'] = text_boxes['coordinates2'].apply(safe_literal_eval)
    other_annots['coordinates2'] = other_annots['coordinates2'].apply(safe_literal_eval)

    # Remove rows with invalid coordinates
    text_boxes = text_boxes.dropna(subset=['coordinates2'])
    other_annots = other_annots.dropna(subset=['coordinates2'])

    # Find parent annotations for each Text Box
    for text_box in text_boxes.itertuples():
        for other_annot in other_annots.itertuples():
            other_idx = other_annot.Index
            if (text_box.sys_path == other_annot.sys_path and
                text_box.pdf_page == other_annot.pdf_page and
                is_inside(text_box.coordinates2, other_annot.coordinates2)):
                # Assign the text box value to the parent annotation
                other_annots.at[other_idx, 'value'] = text_box.value
                # Merge properties
                other_annots.at[other_idx, 'value'] = text_box.value
                other_annots.at[other_idx, 'font_color'] = text_box.font_color
                other_annots.at[other_idx, 'font'] = text_box.font
                other_annots.at[other_idx, 'font_style'] = text_box.font_style
                other_annots.at[other_idx, 'font_size'] = text_box.font_size

    # Process the annotations
    annotation_data = {}

    for annotation_type in other_annots['type'].unique():

        if pd.isna(annotation_type):  # Skip NaN annotation types
            continue

        temp_df = other_annots[other_annots['type'] == annotation_type][['sys_path', 'pdf_page', 'value', 'stroke_color', 'font_color', 'font', 'font_style', 'font_size']].copy()

        # Only process if we have data
        if not temp_df.empty:
            formatted_values = []
            grouped = temp_df.groupby(['sys_path', 'pdf_page'])

            for name, group in grouped:
                sys_path, pdf_page = name
                values = [format_value_with_colors(v, sc, fc, fn, fs, fsz)
                         for v, sc, fc, fn, fs, fsz in zip(group['value'],
                                                          group['stroke_color'],
                                                          group['font_color'],
                                                          group['font'],
                                                          group['font_style'],
                                                          group['font_size'])]
                formatted_values.append({
                    'sys_path': sys_path,
                    'pdf_page': pdf_page,
                    'value': values
                })

            if formatted_values:  # Only create DataFrame if we have values
                annotation_data[annotation_type] = pd.DataFrame(formatted_values)

    # explicitly set the dtype of annot_markups to string when creating annot_types_df to avoid deprecation
    annot_types_df = other_annots[['sys_path', 'pdf_page', 'annot_markups']].drop_duplicates().reset_index(drop=True)
    annot_types_df['annot_markups'] = annot_types_df['annot_markups'].astype('str')  # Add this line

    for row in annot_types_df.itertuples():
        index = row.Index
        annotations_for_row = {}
        for annotation_type, annotations_df in annotation_data.items():
            if not annotations_df.empty:  # Check if DataFrame has data
                matching_annotations = annotations_df[
                    (annotations_df.sys_path == row.sys_path) &
                    (annotations_df.pdf_page == row.pdf_page)
                ]['value'].tolist()

                if matching_annotations:
                    if matching_annotations and isinstance(matching_annotations[0], list):
                        matching_annotations = [item for sublist in matching_annotations for item in sublist]

                    annotations_for_row[annotation_type] = matching_annotations

        annot_types_df.at[index, 'annot_markups'] = str(annotations_for_row)

    return annot_types_df

def initialize_json_column_names(converted_roi):
    # Initialize a set to hold all column names from JSON
    json_column_names = set()
    for item in converted_roi:
        try:
            json_column_names.add(item['columnName'])

            # Check if 'tableColumns' is present in the item
            if 'tableColumns' in item:
                # Iterate through each column in 'tableColumns'
                for column_dict in item['tableColumns']:
                    # Each column_dict has the column name as the key
                    for column_name in column_dict.keys():
                        # Add the column name to the set of JSON column names
                        json_column_names.add(column_name)
        except Exception as e:
            logger.error(f"Error processing converted_roi item: {e} ---{item}", exc_info=True)
    return json_column_names

def vertical_overlap_ratio(box1, box2):
    top = max(box1['y0'], box2['y0'])
    bottom = min(box1['y1'], box2['y1'])
    overlap = max(0, bottom - top)
    height = min(box1['y1'] - box1['y0'], box2['y1'] - box2['y0'])
    return overlap / height

# def sort_boxes_reading_order(df, y_thresh=8):
def sort_boxes_reading_order(df, overlap_thresh=0.5, y_gap_factor=1.5):
    """More efficient line assignment using active lines"""
    if df.empty:
        return df

    df = df.copy()
    df = df.sort_values('y0').reset_index(drop=True)

    active_lines = []  # List of (line_id, bottom_y) tuples
    df['line_id'] = -1
    current_line = 0

    for idx, box in df.iterrows():
        y0, y1 = box['y0'], box['y1']
        height = y1 - y0

        # Remove inactive lines (those that are too far above current box)
        active_lines = [(lid, by) for lid, by in active_lines if by > y0 - height * y_gap_factor]

        # Try to assign to an existing active line
        assigned = False
        for i, (line_id, bottom_y) in enumerate(active_lines):
            # Calculate overlap
            overlap = max(0, min(bottom_y, y1) - max(bottom_y - height, y0))
            if overlap / height > overlap_thresh:
                df.at[idx, 'line_id'] = line_id
                # Update the line's bottom_y
                active_lines[i] = (line_id, max(bottom_y, y1))
                assigned = True
                break

        # If not assigned, create a new line
        if not assigned:
            df.at[idx, 'line_id'] = current_line
            active_lines.append((current_line, y1))
            current_line += 1

    # Sort by line_id and x0
    return df.sort_values(['line_id', 'x0']).reset_index(drop=True)

def consolidate_group(group: pd.DataFrame, json_column_names, converted_roi, columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon):

    # Sort to improve logical general data output
    # group = group.sort_values(by=["y0", "x0"], ignore_index=True) # basic sort by top-left first
    group = sort_boxes_reading_order(group)
    outliers = []
    #if all(group['sys_layout_valid']):
    # Initialize consolidated_data with JSON column names
    consolidated_data = {col: set() for col in json_column_names}
    # Initialize consolidated_data
    consolidated_data = {col: set() for col in columns_to_aggregate if col in group.columns}
    consolidated_data.update({col: group[col].iloc[0] for col in columns_to_keep if col in group.columns})

    if group.empty:
        return consolidated_data, []

    # Check if any 'sys_layout_valid' value in the group is True
    if 'sys_layout_valid' not in group.columns:
        consolidated_data['sys_layout_valid'] = 1
    else:
        if any(group['sys_layout_valid']):
            consolidated_data['sys_layout_valid'] = 1

    processed_values = {}  # Track processed values and their bounding boxes

    text_bboxes = group[['x0', 'y0', 'x1', 'y1']].values.tolist()

    # Track red text values i.e. revisions. Store by index, is_revision (bool)
    revisions = {}

    # Find all overlaps with each ROI first using spatial indexing
    roi_bboxes = {}
    for item in converted_roi:
        roi_column_name = item['columnName']
        if roi_column_name.lower() in ['bom', 'spec', 'spool', 'ifc', 'generic_1', 'generic_2']:
            continue
        roi_bboxes[roi_column_name] = (item['x0'], item['y0'], item['x1'], item['y1'])

    # All overlaps of general columns to list of (index, overlap_area)
    overlaps = find_overlapping_rectangles_with_areas(roi_bboxes.values(), text_bboxes)
    overlaps = {list(roi_bboxes.keys())[i]: values for i, values in overlaps.items()}

    for roi_column_name, roi_overlaps in overlaps.items():
        # Use only text which meets overlap threshold
        valid_overlaps = []
        for index, overlap_area in roi_overlaps:
            text_box = text_bboxes[index]
            text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
            overlap_ratio = overlap_area / text_area if text_area else 0
            if overlap_ratio >= overlap_threshold:
                valid_overlaps.append(index)

        if not valid_overlaps:
            consolidated_data[roi_column_name] = "" # No overlapping text with this ROI
            continue

        # Handle combined text or revision. Cache
        overlap_items = group.iloc[valid_overlaps].copy()
        texts = overlap_items['value'].tolist()
        combined_text = []
        combined_red_text = []
        for n, overlap_item in enumerate(overlap_items.itertuples()):
            overlap_index = overlap_item.Index
            if overlap_index in revisions: # Check cache
                is_revision = revisions[overlap_index]
            else:
                color = None
                try:
                    # No cache. Detect if red text
                    color_val = overlap_item.color
                    if isinstance(color_val, str):
                        color_val = color_val.strip('()').split(',')
                    color_val = tuple(map(int, color_val))
                    if color_val:
                        if (color_val[0] > 200 and color_val[1] < 100 and color_val[2] < 100): # red
                            color = "r"
                        # Can check other colors here e.g. ignore_blue
                except Exception as e:
                    # Cannot parse color. Set default as non-revision text
                    color_val = (0, 0, 0)

                is_red = color == "r"
                is_revision = is_red
                revisions[overlap_index] = is_revision

            if is_revision:
                # Handle revision logic here
                combined_red_text.append(texts[n])
            else:
                combined_text.append(texts[n])

            # Add to processed values
            # processed_values[overlap_index] = (overlap_item['value'], overlap_item['coordinates2'])

        if combined_red_text:
            value = " ".join(combined_red_text)
            outliers.append({
                "value": " ".join(combined_text),
                "coordinates": (0, 0, 0, 0), # TODO
                "coordinates2": (0, 0, 0, 0), # TODO
                "outlier_scope_2": roi_column_name,
                "outlier_reason": "revision text",
                "outlier_action": "replaced_text_with_revision"
            })
        else:
            value = " ".join(combined_text)

        consolidated_data[roi_column_name] = value

    # Consolidate numeric columns e.g. elevation
    for col in columns_to_aggregate:
        rows = group[col].dropna().unique()
        rows = [str(row).strip() for row in rows]
        rows = {row for row in rows if row}
        consolidated_data[col] = '; '.join(rows)

    # # Apply logic for each coordinate range in JSON
    # for _, row in group.iterrows():
    #     for col in columns_to_aggregate:
    #         if pd.isna(row[col]):  # Check value which are not NaN
    #             continue
    #         value = str(row[col])  # Convert to string
    #         if not value.strip():  # Now safe to use strip()
    #             continue
    #         # consolidated_data[col].add(value.strip()) Original 12.4.24

    #         # Check for duplicates based on value and coordinates
    #         coords = row['coordinates2']
    #         is_duplicate = False

    #         for existing_value, existing_coords in processed_values.get(col, []):
    #             if (value == existing_value or
    #                 (len(value) > 0 and len(existing_value) > 0 and
    #                     (value in existing_value or existing_value in value)) and
    #                 check_bbox_overlap(coords, existing_coords)):
    #                 # is_duplicate = True
    #                 is_duplicate = False # Disable for now as critical in excluding valid values
    #                 break

    #         if not is_duplicate:
    #             consolidated_data[col].add(value.strip())

    #             if col not in processed_values:
    #                 processed_values[col] = []
    #             processed_values[col].append((value, coords))

        # for item in converted_roi:
        #     try:
        #         roi_column_name = item['columnName'].lower()
        #         if roi_column_name in ['bom', 'spec', 'spool', 'ifc', 'generic_1', 'generic_2']:
        #             continue

        #             # # Calculate overlap for other items (Being Skipped???)
        #             # item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
        #             # overlap_area = calculate_overlap_area(text_box, item_box)

        #             # text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
        #             # overlap_ratio = overlap_area / text_area if text_area else 0

        #             # if overlap_ratio >= overlap_threshold:
        #             #     check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold)

        #         # if 'tableColumns' in item:
        #         #     for col in item['tableColumns']:
        #         #         check_and_update_data(row, col, consolidated_data, epsilon, overlap_threshold)
        #         else:
        #             #print("/n/nERROR PASSING ITEM ON ELSE")
        #             check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold, overlap_sizes)

        #             # if roi_column_name == 'drawing':
        #             #     print(f"\n\nDRAWING COORDINATE CHECK: CALLS check_and_update")

        #     except Exception as e:
        #         logger.error(f"Error analyzing field {item['columnName']}: {e}")

    # try:
    #     # Convert sets to strings, joining unique values with '; '
    #     for col in columns_to_aggregate:
    #         consolidated_data[col] = '; '.join(consolidated_data[col])

    # except Exception as e:
    #     logger.error(f"Error consolidating columns_to_aggregate: {e}")

    # Ensure any concated data is handled
    consolidated_data = finalize_consolidated_data(consolidated_data)

    return consolidated_data, outliers

def check_and_update_data(row, item, consolidated_data, epsilon, overlap_threshold=0, overlap_sizes: dict={}, precedence_for_annot: bool=True,
                          debug_log=check_debug_log, debug_columns=None):

    """
    Function responsible for determining when values overlap with regions of interest and consolidating
    them into the final dataset. This is a critical component of the document parsing system.

    Key features:
    - Calculates spatial overlap between text elements and defined regions
    - Handles priority for red text (typically used for revisions in engineering documents)
    - Manages proper reading order for text within a field
    - Detects and handles duplicate values
    - Supports special tracking and debugging for specified columns

    Parameters:
    - row: Dictionary containing information about a text element
    - item: Dictionary containing information about a region of interest
    - consolidated_data: The consolidated data being built
    - epsilon: Small tolerance value for floating point comparisons
    - overlap_threshold: Minimum overlap ratio required to consider a text element part of a region
    - overlap_sizes: Dictionary to track overlap sizes (for debugging)
    - precedence_for_annot: Whether annotations take precedence over text
    - debug_log: Dictionary to store debugging information
    - debug_columns: Column(s) to enable detailed debugging output for (None, string, or list)

    Returns: None (modifies consolidated_data in place)
    """

    if debug_log is None:
        debug_log = {}

    # Initialize debug log for this page if it doesn't exist
    page_num = str(row['pdf_page'])
    if page_num not in debug_log:
        debug_log[page_num] = []

    coords = row['coordinates2']
    item_box = (item['x0'], item['y0'], item['x1'], item['y1'])
    text_box = (coords[0], coords[1], coords[2], coords[3])

    # Calculate overlap area
    overlap_area = calculate_overlap_area(text_box, item_box)
    text_area = (text_box[2] - text_box[0]) * (text_box[3] - text_box[1])
    overlap_ratio = overlap_area / text_area if text_area else 0

    # Function to check if we should debug this column
    def should_debug(col_name):
        if debug_columns is None:
            return False
        if isinstance(debug_columns, list):
            return col_name in debug_columns
        return col_name == debug_columns  # Handle single string case

    if overlap_ratio >= overlap_threshold:
        column_name = item['columnName']
        new_value = row['value'].strip()

        # Get current values as a set (split by spaces since that's how we concatenate)
        current_value = str(consolidated_data.get(column_name, '')).strip()
        current_values = set(current_value.split())

        # DEBUG FOR SPECIFIED COLUMNS THAT OVERLAP
        if should_debug(column_name):
            print(f"\n=== {column_name.upper()} FIELD OVERLAP DETECTED ===")
            print(f"Text value: '{new_value}'")

            # Debugging for color detection
            if 'color' in row:
                print(f"Color raw value: {row['color']}")
                print(f"Color type: {type(row['color'])}")

                # Check if it's a tuple - expected format is (255,0,0) for red
                if isinstance(row['color'], tuple):
                    print(f"Color tuple values: R={row['color'][0]}, G={row['color'][1]}, B={row['color'][2]}")

                # Handle other potential formats
                elif isinstance(row['color'], list):
                    print(f"Color list values: {row['color']}")
                elif isinstance(row['color'], str):
                    print(f"Color string value: {row['color']}")
                else:
                    print(f"Color is in unexpected format: {row['color']}")

        # Extract color properly - parse string representation of tuple
        is_red = False
        try:
            if 'color' in row:
                color_val = row['color']

                # Check if it's a string representation of a tuple
                if isinstance(color_val, str) and color_val.startswith('(') and color_val.endswith(')'):
                    # Parse the string into a tuple
                    try:
                        # Clean and parse the string representation
                        color_str = color_val.strip('()')
                        color_components = [int(x.strip()) for x in color_str.split(',')]
                        if len(color_components) == 3:
                            new_color = tuple(color_components)
                            if should_debug(column_name):
                                print(f"Parsed string into tuple: {new_color}")
                        else:
                            new_color = (0, 0, 0)
                    except Exception as e:
                        if should_debug(column_name):
                            print(f"Error parsing color string: {e}")
                        new_color = (0, 0, 0)
                elif isinstance(color_val, tuple) and len(color_val) == 3:
                    new_color = color_val
                else:
                    new_color = (0, 0, 0)

                # Check if red - with parsed tuple
                if isinstance(new_color, tuple) and len(new_color) == 3:
                    is_red = (new_color[0] > 200 and new_color[1] < 100 and new_color[2] < 100)
                    if should_debug(column_name):
                        print(f"Is red text: {is_red} (R={new_color[0]}, G={new_color[1]}, B={new_color[2]})")
            else:
                new_color = (0, 0, 0)

        except Exception as e:
            if should_debug(column_name):
                print(f"Error processing color: {e}")
            new_color = (0, 0, 0)
            is_red = False

        # Special tracking for specified debug columns
        if should_debug(column_name):
            # Track items with color information
            tracking_key = f"_{column_name}_tracking"
            if tracking_key not in consolidated_data:
                consolidated_data[tracking_key] = []

            # Add this item
            consolidated_data[tracking_key].append({
                'value': new_value,
                'coords': coords,
                'is_red': is_red
            })

            # Final debug check before processing
            print(f"All tracked {column_name} items:")
            for idx, item in enumerate(consolidated_data[tracking_key]):
                print(f"  [{idx}] value='{item['value']}', is_red={item['is_red']}, coords={item['coords']}")

        # Check if this value or a duplicate is already present
        # Needs review - Disabled for now as critical in excluding valid values
        is_duplicate = False
        # for existing_value in current_values:
        #     if (existing_value == new_value or
        #         (len(existing_value) > 0 and len(new_value) > 0 and
        #          (existing_value in new_value or new_value in existing_value))):
        #         is_duplicate = False
        #         break

        # Check if any previous text for this column was red
        previously_red = False
        tracking_key = f"_{column_name}_tracking"
        if tracking_key in consolidated_data:
            for item in consolidated_data[tracking_key]:
                if item['is_red']:
                    previously_red = True
                    break

        # Red text priority handling for all columns
        if not is_duplicate:
            if is_red:
                # If this is red text, it replaces all existing values
                if isinstance(consolidated_data.get(column_name), set):
                    consolidated_data[column_name] = {new_value}
                    if should_debug(column_name):
                        print(f"Set {column_name} (red text priority): {consolidated_data[column_name]}")
                else:
                    consolidated_data[column_name] = new_value
                    if should_debug(column_name):
                        print(f"Updated {column_name} to red text: '{new_value}'")
            else:
                # # Original logic for non-red text
                # if consolidated_data.get(column_name):
                #     consolidated_data[column_name] = f"{consolidated_data[column_name]} {new_value}"
                # else:
                #     consolidated_data[column_name] = new_value

                # Only append/set non-red text if no previous red text exists
                if not previously_red:
                    if consolidated_data.get(column_name):
                        consolidated_data[column_name] = f"{consolidated_data[column_name]} {new_value}"
                        if should_debug(column_name):
                            print(f"Appended to {column_name}: '{new_value}'")
                    else:
                        consolidated_data[column_name] = new_value
                        if should_debug(column_name):
                            print(f"Set {column_name}: '{new_value}'")
                elif should_debug(column_name):
                    print(f"Skipped non-red text '{new_value}' because red text has priority")

        # Create debug log entry
        log_entry = {
            'column': column_name,
            'current_value': current_value,
            'new_value': new_value,
            'coordinates': text_box,
            'overlap_ratio': overlap_ratio,
            'type': row.get('Type', ''),
            'is_red': is_red
        }

        # Handle precedence for annotations
        if precedence_for_annot and row.get('Type') != 'Text':
            log_entry['action'] = 'replaced'
            log_entry['reason'] = 'annotation_precedence'
            debug_log[page_num].append(log_entry)
            return

        # Check for duplicate values
        if is_duplicate:
            log_entry['action'] = 'skipped'
            log_entry['reason'] = 'duplicate_value'
        elif is_red:
            log_entry['action'] = 'red_priority'
            log_entry['reason'] = 'red_text_priority'
        else:
            log_entry['action'] = 'appended' if current_value else 'added'
            log_entry['reason'] = 'normal_processing'

        debug_log[page_num].append(log_entry)

def finalize_consolidated_data(consolidated_data, debug_columns=None):
    """
    Function responsible for cleaning up temporary tracking data and finalizing the consolidated results.
    This function runs after all data has been processed, providing the final opportunity to format
    and prepare the output before it's returned to the calling system.

    Key features:
    - Removes temporary tracking keys used during processing
    - Provides debug information about tracked columns before removal
    - Ensures the final data structure is clean and ready for use

    Parameters:
    - consolidated_data: The consolidated data to finalize
    - debug_columns: Column(s) to enable detailed debugging output for (None, string, or list)

    Returns: The finalized consolidated data with temporary keys removed
    """

    print("\n=== FINALIZING CONSOLIDATED DATA ===")

    # Function to check if we should debug this column
    def should_debug(col_name):
        if debug_columns is None:
            return False
        if isinstance(debug_columns, list):
            return col_name in debug_columns
        return col_name == debug_columns  # Handle single string case

    # Special handling for tracked columns
    for key in list(consolidated_data.keys()):
        if key.startswith('_') and key.endswith('_tracking'):
            column_name = key[1:-9]  # Extract column name from tracking key

            if should_debug(column_name):
                print(f"Found {column_name} items: {consolidated_data[key]}")

                # Final check to ensure proper value
                if column_name in consolidated_data:
                    print(f"Final {column_name} value: {consolidated_data[column_name]}")

            # Remove temporary key
            del consolidated_data[key]

    return consolidated_data

def consolidate_ranges(raw_data: pd.DataFrame, converted_roi: dict):
    """
    Consolidate ranges for a page of raw data

    Args:
        page_roi_payloads: Map of {page number: converted_roi_layout}

    Returns:
        final_df:

    """
    logger.info("Starting consolidation process...")

    consolidated_data = None # Create a list to hold the consolidated data for each group
    final_df = pd.DataFrame()  # Initialize final_df to handle data loss on error
    outliers = [] # Initialize general outliers

    columns_to_aggregate = PAYLOAD_OPTIONS.get('aggregate_cols_general', [])
    columns_to_keep = PAYLOAD_OPTIONS.get('keep_cols_general', [])
    overlap_threshold = PAYLOAD_OPTIONS.get('overlap_threshold', 0)

    # Check for empty DataFrame and add 'annot_markups' column
    if raw_data.empty:
        logger.warning("Dataframe is empty. Exiting consolidation process.")
        return pd.DataFrame(), outliers

    # Check if column is in df and add it
    if 'annot_markups' not in raw_data.columns:
        raw_data['annot_markups'] = None # pd.Series([None] * len(raw_data))

    # --> Process and return annotation data
    annot_types_df = process_extracted_annot_data(raw_data)

    epsilon = 0.001 # Consider a small epsilon for float comparison inaccuracy

    # --> Initialize JSON Column Names
    json_column_names = None

    try:
        json_column_names = initialize_json_column_names(converted_roi)

        # --> Process and consolidate data groups
        consolidated_data, outliers = consolidate_group(raw_data, json_column_names, converted_roi,
                                              columns_to_aggregate, columns_to_keep, overlap_threshold, epsilon)
    except Exception as e:
        raise Exception(f"Error consolidating page - {e}")

    try:
        if consolidated_data:
            consolidated_df = pd.DataFrame([consolidated_data]) # Create a new DataFrame from the consolidated data list
            # annot_types_df = annot_types_df.groupby(['sys_path', 'pdf_page'])['annot_markups'].apply(lambda x: ', '.join(x)).reset_index()  # Aggregate 'annot_markups' values into a comma-delimited string for each 'sys_path', 'pdf_page'
            # annot_types_df = annot_types_df['annot_markups'].apply(lambda x: ', '.join(x)).reset_index()  # Aggregate 'annot_markups' values into a comma-delimited string for each 'sys_path', 'pdf_page'
            annot_types_df = annot_types_df.groupby(['sys_path', 'pdf_page'])['annot_markups'].apply(lambda x: ', '.join(x)).reset_index()

            #print(f"\n\nANNOT TYPES DF-2: {annot_types_df} \n\n")
            final_df = pd.merge(consolidated_df, annot_types_df, on=['sys_path', 'pdf_page'], how='outer')

            if DEBUG_MODE:
                print(f"Final DataFrame rows after merge: {len(final_df)}")

    except Exception as e:
        raise Exception(f"Error creating final DataFrame - {e}")

    outliers_df = pd.DataFrame(outliers) if outliers else pd.DataFrame()
    outliers_df['outlier_scope'] = 'general'
    return final_df, outliers_df

# --------> consolidate_ranges & Helper Functions

def organize_pages_by_size(document, limit=None):
    """Organize pages by their size, including original page numbers."""
    page_sizes = defaultdict(list)
    for page_num in range(min(len(document), limit) if limit else len(document)):
        page = document.load_page(page_num)
        size = (round(page.rect.width, 2), round(page.rect.height, 2))
        page_sizes[size].append(page_num)
    return dict(page_sizes)

def merge_spec_and_general_data(combined_spec_df, combined_general_df, columns_to_merge=None):
    if columns_to_merge is None:
        columns_to_merge = ['size', 'pipeSpec', 'insulationSpec', 'insulationThickness', 'heatTrace', 'paintSpec']

    try:
        # Ensure 'PDF ID' and 'PDF Page' columns are present and clean
        # combined_spec_df = combined_spec_df.rename(columns={'PDF ID': 'pdf_id', 'PDF Page': 'pdf_page'})
        # combined_general_df = combined_general_df.rename(columns={'PDF ID': 'pdf_id', 'PDF Page': 'pdf_page'})

        combined_spec_df['pdf_id'] = combined_spec_df['pdf_id'].astype(str)
        combined_spec_df['pdf_page'] = combined_spec_df['pdf_page'].astype(str)
        combined_general_df['pdf_id'] = combined_general_df['pdf_id'].astype(str)
        combined_general_df['pdf_page'] = combined_general_df['pdf_page'].astype(str)

        # Filter columns_to_merge to only include columns present in combined_spec_df
        available_columns = [col for col in columns_to_merge if col in combined_spec_df.columns]

        # Select the available columns from combined_spec_df
        spec_df_selected = combined_spec_df[['pdf_id', 'pdf_page'] + available_columns]

        # Merge 'combined_spec_df' and 'combined_general_df' based on both 'pdf_id' and 'pdf_page'
        merged_df = pd.merge(spec_df_selected, combined_general_df, on=['pdf_id', 'pdf_page'], how='left')

        #print("\n\n --> COLUMNS AFTER MERGE: ", merged_df.columns)

        return merged_df

    except Exception as e:
        print(f"Error occurred while merging spec and general data: {str(e)}")
        return combined_general_df

def process_page(doc: fitz.Document,
                 pdf_path: str,
                 page_num: int,
                 raw_data: pd.DataFrame,
                 missing_regions: pd.DataFrame,
                 roi_payload: dict,
                 use_ocr: bool,
                 ocr_data: pd.DataFrame):
    """
    Core logic for extracting page ROIs

    Args:
        doc: Loaded PDF document
        pdf_path: Full filename of PDF
        raw_data: Raw data of the page
        missing_regions: DataFrame of all missing regions in the page
        roi_payload: ROI name and respective relative coordinates

    """
    start_time = time.perf_counter()
    if not doc:
        doc = fitz.open(pdf_path)
    page: fitz.Page = doc[page_num]

    if raw_data.empty:
        raw_data = get_empty_raw_dataframe()

    raw_data["patched"] = None

    # if PATCH_REVISION_BBOX:
    #     # Patch red color regions
    #     # raw_data_red = patch_bboxes(raw_data[raw_data['color'] == (255,0,0)], page)
    #     # # Merge patched bboxes
    #     # raw_data = pd.concat([raw_data[raw_data['color'] != (255,0,0)], raw_data_red]).reset_index(drop=True)

    #     raw_data = patch_bboxes(raw_data, page).reset_index(drop=True)

        # raw_data.to_excel("debug/raw_data.xlsx", index=True)

    # Connvert relative payload coords to absolute given page dimension
    converted_roi_payload = convert_relative_coords_to_points(roi_payload, page.rect.width, page.rect.height)

    if use_ocr and not ocr_data.empty: # is not None and not missing_regions.empty:

        ocr_data["x0"] = ocr_data["x0"].apply(lambda x: x * page.rect.width)
        ocr_data["y0"] = ocr_data["y0"].apply(lambda y: y * page.rect.height)
        ocr_data["x1"] = ocr_data["x1"].apply(lambda x: x * page.rect.width)
        ocr_data["y1"] = ocr_data["y1"].apply(lambda y: y * page.rect.height)

        print(page.rotation)

        # Patch raw data
        page_roi_regions = [
            {
                'pdf_page': page_num + 1,
                'columnName': r['columnName'],
                'x0': r.get('tableCoordinates', r)['x0'],
                'y0': r.get('tableCoordinates', r)['y0'],
                'x1': r.get('tableCoordinates', r)['x1'],
                'y1': r.get('tableCoordinates', r)['y1'],
            }
            for r in converted_roi_payload
        ]
        page_roi_regions = pd.DataFrame(page_roi_regions)

        reduced_textract = ocr_data


        reduced_textract = reduce_regions(ocr_data, inverse=False)

        # # Convert textract to absolute coords
        # reduced_textract["x0"] *= page.rect.width
        # reduced_textract["y0"] *= page.rect.height
        # reduced_textract["x1"] *= page.rect.width
        # reduced_textract["y1"] *= page.rect.height


        # try:
        #     logger.info("Extracting missing text...")
        #     final_missing_rois = extract_missing_text(reduced_textract, missing_regions)

        #     if final_missing_rois.empty:
        #         logger.error("No missing ROIs were found after OCR")
        #         return

        #     # final_missing_rois.to_excel("debug/final_missing_rois.xlsx")
        #     logger.info(f"Saved final missing ROIs. Shape: {final_missing_rois.shape}")

        # except Exception as e:
        #     logger.error(f"Error extracting missing text: {e}", exc_info=True)
        #     return

        # reduced_textract = ocr_data

        def workaround_align_y0(df: pd.DataFrame, tolerance=6) -> pd.DataFrame:
            """
            OCR region words are likely not y0 aligned. This workaround
            tries to queries the regions which are close in y0 and set the average
            value. This addresses the issue with y0 sorting where words can be scrambled
            due to y0 being priority sorted
            """
            sorted_y0 = sorted(df["y0"].unique().tolist())
            for y0 in sorted_y0:
                if df[df["y0"] == y0].empty:
                    continue
                within_y0 = (df["y0"] > y0 - tolerance) & (df["y0"] < y0 + tolerance)
                # print(df[within_y0]["y0"])
                avg = df[within_y0]["y0"].mean()
                df.loc[within_y0, "y0"] = avg
                # print(df[within_y0]["y0"])
                # print(df[within_y0])
            return df

        def workarounds(df: pd.DataFrame) -> pd.DataFrame:
            # These workarounds needs adjustments for better generalization

            # Split any text with pipe symbols into separate regions
            split_regions = df[df["text"].str.contains("\|")]

            records = []
            for row in split_regions.itertuples():
                text_parts = row.text.split("|")
                x0, y0, x1, y1 = row.x0, row.y0, row.x1, row.y1
                records.append({
                    "text": text_parts[0],
                    "x0": x0,
                    "y0": y0,
                    "x1": x0 + 1,
                    "y1": y1,
                })

                # Join the rest and make the region start at x1
                records.append({
                    "text": " ".join(text_parts[1:]),
                    "x0": x1,
                    "y0": y0,
                    "x1": x1 + 1,
                    "y1": y1,
                })
            df = df[~df["text"].str.contains("\|")].to_dict("records")
            df.extend(records)
            df = pd.DataFrame(df)
            return df

        reduced_textract = workaround_align_y0(reduced_textract)
        reduced_textract = workarounds(reduced_textract)

        final_ocr_df = []
        # for n, record in enumerate(final_missing_rois.itertuples()):
        for n, record in enumerate(reduced_textract.itertuples()):
            try:
                coordinates = (record.x0, record.y0, record.x1, record.y1)
                final_ocr_df.append({
                    "type": "OCR",
                    "value": str(record.text) if hasattr(record, 'text') else '',
                    "coordinates": coordinates,
                    "coordinates2": coordinates,
                    "font": "ArialMT",
                    "font_size": "12",
                })
            except Exception as e:
                print(f"Warning: Error adding OCR record for page: {page_num+1}, ocr_record={n}, {str(e)}")

        final_ocr_df = pd.DataFrame(final_ocr_df)
        final_ocr_df["pdf_page"] = page_num + 1
        raw_data = pd.concat([raw_data, pd.DataFrame(final_ocr_df)], ignore_index=True)

    if raw_data.empty:
        raise Exception("Raw data empty for this page. Page requires OCR?")

    # Convert 'coordinates' and 'coordinates2' columns from string to tuple
    raw_data = convert_coords_to_tuple(raw_data) # Changed - raw data *should* have been converted before
    # Vectorized approach
    coordinates = np.array(raw_data['coordinates2'].tolist())
    raw_data['x0'] = coordinates[:, 0]
    raw_data['y0'] = coordinates[:, 1]
    raw_data['x1'] = coordinates[:, 2]
    raw_data['y1'] = coordinates[:, 3]

    annot_tables, text_tables, outlier_df = process_table_rois(
        pdf_path,
        raw_data,
        page,
        page_num,
        converted_roi_payload,
    )

    # Consolidate Page Data
    annot_types_df = pd.DataFrame()
    general_outliers_df = pd.DataFrame()
    if not raw_data.empty:
        # --> Consolidate non-table data
        try:
            annot_types_df, general_outliers_df = consolidate_ranges(raw_data, converted_roi_payload)
        except Exception as e:
            logger.error(f"Consolidating ranges for {pdf_path}, Page {page_num + 1}. Error {e}", exc_info=True)

        if not general_outliers_df.empty:
            general_outliers_df['pdf_page'] = page_num + 1
            outlier_df = pd.concat([outlier_df, general_outliers_df], ignore_index=True)

    process_time = time.perf_counter() - start_time

    skipped = False # force unskipped for now
    error = None
    print(f"Page processing {page_num} finished... {process_time}s")

    return page_num + 1, raw_data, annot_types_df, annot_tables, text_tables, outlier_df, process_time, skipped, error

def get_empty_raw_dataframe() -> pd.DataFrame:
    """If the whole drawing is blank, then the Raw Data would be blank

    Return an empty dataframe with correct columns
    """
    columns = [
        'sys_build',
        'sys_path',
        'sys_filename',
        'pdf_id',
        'pdf_page',
        'sys_layout_valid',
        'type',
        'category',
        'value', #text.strip(),
        'elevation',
        'x_position',
        'y_position',
        'coordinates', #The main bounding box
        'coordinates2', #bbox2 normalized_bbox2 #Precise Bounding Box for individual value
        'words',  # Store as JSON string for DataFrame compatibility
        'name',
        'title',
        'created_date',
        'mod_date',
        'id_annot_info',
        # 'annot_',
        'page_rotation',
        'color',
        'annot_type',
        'annot_subtype',
        'vertices',
        'endpoint',
        'stroke_color',
        "font",
        "font_style",
        "font_size",
        "flags"
    ]
    df = pd.DataFrame(columns=columns)
    return df

def process_page_wrapper(pdf_path,
                         page_num: int,
                         raw_data: pd.DataFrame,
                         missing_regions: pd.DataFrame,
                         roi_payload: dict,
                         use_ocr: bool,
                         ocr_data: pd.DataFrame,
                         doc=None):
    """Function to support multiprocessing of page preprocessing

    Returns:
        Result of process_page. On failure, return fallback results
    """
    if not doc:
        global _process_doc
        doc = _process_doc
    try:
        return process_page(doc, pdf_path, page_num, raw_data, missing_regions, roi_payload, use_ocr, ocr_data)
    except Exception as error:
        # Fallback results in case of error
        process_time = -1
        annot_types_df = pd.DataFrame()
        annot_tables = None
        text_tables = None
        outlier_df = None
        skipped = True
        logger.error(f"Page processing {page_num} error... {process_time}s")
        return page_num + 1, raw_data, annot_types_df, annot_tables, text_tables, outlier_df, process_time, skipped, error

def process_chunk_pages(project_id: int, filename, page_numbers: list[int], process_times: list, cancel_event, doc: fitz.Document=None):
    """Function to support multiprocessing of page preprocessing"""
    doc = fitz.open(filename)

    chunk_results = []
    for page_num in page_numbers:
        try:
            res = process_page_wrapper(project_id, filename, page_num, process_times, cancel_event, doc)
            chunk_results.append(res)
        except Exception as e:
            logger.error(e)

    return chunk_results

class OcrPages(Thread):
    """OCR specific pages with results callback

    For each page chunk:
        1. Generate a temporary PDF
        2. Send to AWS for analysis
        3. Combine results.
        4. Pass each chunk result to callback function

    Args:
        file_path: Document file to be analyzed
        pages: Page list for OCR
        chunk_size: Number of pages to per PDF chunk
        callback: Callback on receiving a PDF chunk textract result
        use_checkpoint: Not fully implemented

    Returns:
        None

    Note:
        Textract coords are unmodified and remain as relative values

    On completion, assigns the full combined textract results to self.results
    """

    def __init__(self,
                 file_path: str,
                 pages: list[int],
                 callback,
                 chunk_size: int = 100,
                 use_checkpoint: bool = False,
                 cancel_event: asyncio.Event = None):
        super().__init__()
        self.file_path: str = file_path
        self.chunk_size = chunk_size
        self.pages = pages
        self.callback = callback
        self.use_checkpoint = use_checkpoint
        self.results = None
        self.checkpoint_dir = r"debug/ocr_checkpoints" # TODO
        self.finished = False
        self.cancel = cancel_event

    @classmethod
    def process_textract_response(cls, data: dict) -> pd.DataFrame:
        res = []
        for b in data["Blocks"]:
            if not b.get("Text"):
                continue
            bbox = b["Geometry"]["BoundingBox"]
            confidence = b["Confidence"]
            text = b["Text"]
            page = b.get("Page")
            res.append({
                "pdf_page": page,
                "text": text,
                "x0": bbox["Left"],
                "y0": bbox["Top"],
                "x1": bbox["Left"] + bbox["Width"],
                "y1": bbox["Top"] + bbox["Height"],
                "confidence": confidence
            })
        return pd.DataFrame(res)

    def run(self):
        use_checkpoint = self.use_checkpoint and not __file__.endswith(".pyc")

        # temp_dir = r"debug/temp_pdf_pages"
        for page in self.pages:
            if page <= 0:
                raise ValueError("Page number must be greater than 0")

        self.pages.sort()
        # Create directories for temp and checkpoint files
        if use_checkpoint:
            os.makedirs(self.checkpoint_dir, exist_ok=True)

        # Initialize doc only once
        # doc = PdfReader(self.file_path)
        doc = fitz.open(self.file_path)
        combined_textract = []

        # Process in chunks for efficiency
        page_chunks = [self.pages[i:i + self.chunk_size] for i in range(0, len(self.pages), self.chunk_size)]

        completed_chunks = []
        if use_checkpoint:
            # Load previous progress if exists
            checkpoint_file = f"{self.checkpoint_dir}/ocr_progress.json"
            if os.path.exists(checkpoint_file):
                with open(checkpoint_file, 'r') as f:
                    completed_chunks = json.load(f)
                print(f"Found previous progress: {len(completed_chunks)} chunks completed")

        base_temp_dir = os.path.join(tempfile.gettempdir(), "atem")
        os.makedirs(base_temp_dir, exist_ok=True)

        temp_dir = tempfile.TemporaryDirectory(dir=base_temp_dir, prefix="atem-")

        for chunk_idx, page_chunk in enumerate(page_chunks):
            # Skip if chunk was already processed
            chunk_id = f"chunk_{page_chunk[0]}-{page_chunk[-1]}"
            if chunk_id in completed_chunks:
                print(f"Skipping completed chunk {chunk_idx + 1}/{len(page_chunks)} (pages {page_chunk[0]}-{page_chunk[-1]})")
                # Load previously processed results
                chunk_results = pd.read_feather(f"{self.checkpoint_dir}/{chunk_id}.feather")
                combined_textract.extend(chunk_results.to_dict("records"))
                continue

            logger.debug(f"Processing chunk {chunk_idx + 1}/{len(page_chunks)} (pages {page_chunk[0]}-{page_chunk[-1]})")

            # Create new PDF with just these pages
            # new_doc = PdfWriter()
            # for page_num in page_chunk:
            #     page = doc.pages[page_num-1]
            #     new_doc.add_page(page)

            temp_pdf = f"{temp_dir.name}/chunk_{chunk_idx}-{page_chunk[0]}-{page_chunk[-1]}.pdf"
            # with open(temp_pdf, 'wb') as out:
            #     new_doc.write(out)
            # new_doc.close()

            scale_factor = 2
            out_doc = fitz.Document()
            for page_num in page_chunk:
                # Create new page with scaled dimensions
                page = doc[page_num - 1]
                page.remove_rotation()
                rect = page.rect
                new_rect = fitz.Rect(0, 0, rect.width * scale_factor, rect.height * scale_factor)
                out_page: fitz.Page = out_doc.new_page(width=new_rect.width, height=new_rect.height)

                # Create transformation matrix for scaling
                matrix = fitz.Matrix(scale_factor, scale_factor)

                # Copy content to new page with scaling
                out_page.show_pdf_page(new_rect, doc, page_num - 1, matrix)

            # with open(temp_pdf, 'wb') as out:
            # Save with optimization
            out_doc.save(temp_pdf,
                        garbage=4,  # Maximum garbage collection
                        deflate=True,  # Use deflate compression
                        clean=True)  # Clean unused elements
            out_doc.close()

            try:
                # Process chunk with optimized Textract
                textract_response = convert_to_pdf_aws.convert_multiple_pages(temp_pdf, existing_file=False)
                chunk_results = []
                # Process response and map back to original page numbers
                for page_idx, page_num in enumerate(page_chunk):

                    # Filter blocks for this page and process
                    page_blocks = [block for block in textract_response['Blocks']
                                if 'Page' in block and block['Page'] == page_idx + 1]

                    # Convert to DataFrame with proper coordinates
                    page_textract = OcrPages.process_textract_response({'Blocks': page_blocks})
                    page_textract["pdf_page"] = page_num  # Use original page number

                    # Convert back to absolute coordinates
                    # page_textract["x0"] *= page.rect.width
                    # page_textract["y0"] *= page.rect.height
                    # page_textract["x1"] *= page.rect.width
                    # page_textract["y1"] *= page.rect.height

                    # Save individual page results
                    # save_df_fast(page_textract, f"debug/ocr_patch/textract_results/page_{page_num}_textract", format="feather")
                    # page_textract.to_excel(f"debug/ocr_patch/textract_results/page_{page_num}_textract.xlsx")

                    # Add to chunk results
                    chunk_results.extend(page_textract.to_dict("records"))

                chunk_df = pd.DataFrame(chunk_results)
                # Save chunk checkpoint
                if use_checkpoint:
                    save_df_fast(chunk_df, f"{self.checkpoint_dir}/{chunk_id}", format="feather")

                    # Update progress file
                    completed_chunks.append(chunk_id)
                    with open(checkpoint_file, 'w') as f:
                        json.dump(completed_chunks, f)

                pages_processed = page_chunk
                self.callback(pages_processed, pd.DataFrame(chunk_results))

                # Add to combined results
                combined_textract.extend(chunk_results)

            except Exception as e:
                print(f"Error processing chunk {chunk_idx} (pages {page_chunk[0]}-{page_chunk[-1]}): {str(e)}")
                self.callback(page_chunk, pd.DataFrame())

                # Save error log
                if use_checkpoint:
                    with open(f"{self.checkpoint_dir}/errors.log", 'a') as f:
                        f.write(f"\nError on chunk {chunk_id}: {str(e)}")

        # Remove the temp dir and contents
        temp_dir.cleanup()

        # Convert combined results to DataFrame
        combined_textract = pd.DataFrame(combined_textract)

        # Save final combined results
        if use_checkpoint and not combined_textract.empty:
            save_df_fast(combined_textract, "debug/ocr_patch/combined_textract_final", format="feather")

        self.results = combined_textract
        self.finished = True

class CancelException(Exception):
    """Polled for canceling extraction job"""
    def __init__(self):
        super().__init__()

class RoiExtraction():

    def __init__(self,
                 project_id: int,
                 pdf_path: str,
                 roi_payload = None,
                 pages = None,
                 raw_df: Union[pd.DataFrame, str, None] = None,
                 missing_regions: Union[pd.DataFrame, str, None] = None,
                 multiprocess: bool = False,
                 jobId = None,
                 save_dataframe_results=True,
                 debug: bool = False):

        # Reference to Project Id
        self.project_id: int = project_id

        self._debug_mode = debug

        self.pages: list[int] = pages

        # Pages to extract (zero-indexed)
        self._extract_pages = []

        # Full path to PDF for extraction
        self.pdf_path: str = pdf_path

        # PyMuPDF document
        self.doc: fitz.Document = None

        # Job Id - Used to update ATEM job status
        self.jobId = jobId

        # Safe shared access
        self.process_worker_lock = multiprocessing.Lock()

        # Page and Page Group Payload Data
        self.roi_payload = roi_payload

        # Job configuration and status vars
        self._multiprocess = multiprocess
        self._cancel = Event()
        self._total_pages = None
        self._time_of_last_request = None
        self._times = []
        self._eta_updated_cb = None
        self._last_eta = None
        self._timer = Thread(target=self.on_timer)
        self._page_results = None
        self._results_df = None
        self._save_dataframe_results = save_dataframe_results
        self._pages_remaining = [] # 1-indexed page numbers
        self._ocr_thread: OcrPages = None
        self._worker_args = deque()

        self._parse_coords = False  # For xlsx, need to parse string to tuple of floats

        self._raw_df: Union[pd.DataFrame, str, None] = raw_df
        self._missing_regions = missing_regions

        # Extraction Timing
        self.start_time = 0

    def _initialize(self):
        """Perform pre-checks"""

        self.start_time = time.perf_counter()

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Initialization", jobId=self.jobId)

        self.doc: fitz.Document = fitz.open(self.pdf_path)

        # Load preprocessed data
        if isinstance( self._raw_df, str):
            if self._raw_df.endswith(".xlsx"):
                self._raw_df = pd.read_excel( self._raw_df)
                self._parse_coords = True
            else:
                self._raw_df = load_df_fast(self._raw_df)
        elif isinstance( self._raw_df, pd.DataFrame):
            self._raw_df = self._raw_df
            self._parse_coords = True
        else:
            raw_df_path = getSourceRawDataPath(self.project_id, self.pdf_path)
            try:
                self._raw_df = load_df_fast(raw_df_path)
            except Exception as e:
                logger.error("Could not detect or load raw data for this source")

        empty_raw_df = get_empty_raw_dataframe()
        if isinstance(self._raw_df, pd.DataFrame) and self._raw_df.empty:
            logger.warning("Preprocessed Raw data is empty")

        try:
            self._raw_df = pd.concat([empty_raw_df, self._raw_df], ignore_index=True) # ensures columns
        except:
            self._raw_df = empty_raw_df

        # self._missing_regions = pd.DataFrame()
        if isinstance(self._missing_regions, str):
            if self._missing_regions.endswith(".xlsx"):
                self._missing_regions = pd.read_excel(self._missing_regions)
            else:
                self._missing_regions = load_df_fast(self._missing_regions)
        elif isinstance(self._missing_regions, pd.DataFrame):
            pass
        else:
            pmr_path = getSourceMissingRegionsPath(self.project_id, self.pdf_path)
            try:
                self._missing_regions = load_df_fast(pmr_path)
            except Exception as e:
                logger.warning("Could not detect or load missing regions for this source")
        try:
            if self._missing_regions.empty:
                logger.warning("Preprocessed missing regions data is empty")
        except Exception as e:
            logger.warning("Error checking _missing_regions.empty")

        self._manager = Manager()
        if self._multiprocess:
            # Slow...
            # self._cancel = Manager().Event()
            self._cancel = self._manager.Event()

        # Initialize ROI layouts
        try:
            self.roi_payload = convert_roi_payload.convert_roi_payload(self.roi_payload)
        except Exception as e:
            logger.warning("Could not load ROI payload. Ignore if already converted")

        if self.pages:
            extract_pages = [int(p) - 1 for p in self.pages]
        else:
            extract_pages = [n for n in range(fitz.open(self.pdf_path).page_count)]

        self._extract_pages = [p for p in extract_pages if p >= 0 and p < len(self.doc)]
        self._extract_pages = [p for p in self._extract_pages if p + 1 in self.roi_payload["pageToGroup"]]
        self._extract_pages.sort()
        if not self._extract_pages:
            raise Exception("No valid pages for extraction. Ensure pages arg and roi payload are set correctly")
        self._total_pages = len(self._extract_pages)

        logger.debug("Mapping PDF ids...")
        self.page_num_to_pdf_id = DatabaseManager().get_source_pdf_id_map(self.project_id, self.pdf_path, [n+1 for n in self._extract_pages])

        # Get existing saved data for this project source
        logger.debug("Fetching preproprocessed data...")

        # Filter preprocessed data to just include the pdf ids that we need
        pdf_ids = list(self.page_num_to_pdf_id.values())
        self._raw_df = self._raw_df[self._raw_df["pdf_id"].isin(pdf_ids)]
        if not self._raw_df.empty:
            self._raw_df["coordinates"] = self._raw_df["coordinates"].apply(safe_literal_eval)
            self._raw_df["coordinates2"] = self._raw_df["coordinates2"].apply(safe_literal_eval)
            self._raw_df["color"] = self._raw_df["color"].apply(safe_parse_color)

        # Load missing regions data
        try:
            if not self._missing_regions.empty:
                self._missing_regions = self._missing_regions[self._missing_regions["pdf_id"].isin(pdf_ids)]
        except Exception as e:
            logger.warning(f"Error checking self._missing_regions. {e}")
        # Initialize final results
        self.reset_result_dataframes()

    def reset_result_dataframes(self):
        """Clears DataFrames"""
        # self._raw_df = pd.DataFrame()
        self.combined_raw_df = [] #pd.DataFrame()
        self.combined_general_df = [] #pd.DataFrame()
        self.combined_outlier_df = [] #pd.DataFrame()
        self.combined_bom_df = [] #pd.DataFrame()
        self.combined_spool_df = [] #pd.DataFrame()
        self.combined_spec_df = [] #pd.DataFrame()
        self.combined_ifc_df = [] #pd.DataFrame()
        self.combined_generic_1_df = [] #pd.DataFrame()
        self.combined_generic_2_df = [] #pd.DataFrame()

    def get_page_args(self, page_num: int, ocr_data: pd.DataFrame = None):
        """Returns the args that are passed into process_page function"""
        use_ocr = ocr_data is not None
        group_num = self.roi_payload["pageToGroup"][page_num + 1]
        page_roi_payload = self.roi_payload["groupRois"].get(group_num)
        if not page_roi_payload:
            return None
        if self._raw_df.empty:
            page_raw_data = pd.DataFrame()
        else:
            page_raw_data = self._raw_df[self._raw_df["pdf_id"] == self.page_num_to_pdf_id[page_num+1]]
            page_raw_data["pdf_page"] = page_num + 1

        try:
            if self._missing_regions.empty:
                page_missing_regions = pd.DataFrame()
            else:
                page_missing_regions = self._missing_regions[self._missing_regions["pdf_id"] == self.page_num_to_pdf_id[page_num+1]]
        except Exception as e:
            logger.warning(f"Error checking self._missing_regions.empty. Setting to empty dataframe: {e}")
            page_missing_regions = pd.DataFrame()

        return [
            self.pdf_path,
            page_num,
            page_raw_data,
            page_missing_regions,
            page_roi_payload,
            use_ocr,
            ocr_data,
        ]

    def on_textract_chunk(self, pages: list[int], textract_chunk: pd.DataFrame):
        """
        Add processing jobs for pages in textract chunk. If no OCR data returned for a page, continue
        processing without OCR.

        TODO - log these as OCR warning/error
        """
        textract_pages = textract_chunk['pdf_page'].unique().tolist()
        non_result_pages = [p for p in pages if p not in textract_pages]
        for page_num in non_result_pages:
            logger.debug(f"-- No textract data was received for page {page_num}. Processing without OCR for this page")
            page_args = self.get_page_args(page_num-1)
            self._worker_args.append(page_args)

        logger.debug(f"Received textract chunk. Adding pages count {len(textract_pages)} to processing")
        for page_num, page_ocr_df in textract_chunk.groupby("pdf_page"):
            page_args = self.get_page_args(page_num-1, page_ocr_df)
            self._worker_args.append(page_args)

    def _process_pages(self):
        """Process PDF and group pages and return a dataframe.

        Args:
            filename: Input PDF filename.
            output_path: Optionally generate.
            pages: A list[int] of pages to be processed. If None is supplied, all pages
                are analyzed.
            multiprocess: Enable multiprocessing

        Returns:
            A dataframe containing results of pages grouped
        """
        print("Processing pages...")

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Starting Extraction", jobId=self.jobId)

        # COMMIT_CHUNKS
        self.summary = []
        self.remaining = self._total_pages

        # Fetch existing page numbers
        # If any are missing, the source has not been preprocessed

        # if self._multiprocess:
        #     self._times = self._manager.list()
        #     self._worker_args = self._manager.list()

        self._times = []
        self._worker_args = deque()
        self._pages_remaining = {}

        print(self._raw_df["coordinates"].head(), self._raw_df["coordinates"].dtype)

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Running Extraction", jobId=self.jobId)

        # Process the args for each page. Defer processing of OCR pages until the results
        # are received
        ocr_pages = []
        for page_num in reversed(self._extract_pages):
            self._pages_remaining[page_num + 1] = None
            if page_num + 1 in self.roi_payload.get("ocrPages", []):
                ocr_pages.append(page_num + 1)
                logger.debug(f"Note - Page {page_num + 1} is marked for OCR. Processing is deferred until textract is received")
                continue
            page_args = self.get_page_args(page_num)
            if page_args:
                self._worker_args.append(page_args)

        if ocr_pages:
            # Laods previously saved textract data
            from pathlib import Path
            existing_ocr = getSourceOcrPath(self.project_id, self.pdf_path)
            ocr_df = pd.DataFrame(columns=["pdf_page"])
            if os.path.exists(existing_ocr):
                ocr_df = load_df_fast(existing_ocr)
                ocr_df = ocr_df[ocr_df["pdf_page"].isin(ocr_pages)]
            existing_ocr_pages = ocr_df["pdf_page"].unique().tolist()
            # existing_ocr_pages = []
            self.on_textract_chunk(existing_ocr_pages, ocr_df) # Add workers for previously added OCR
            remaining_ocr = [p for p in ocr_pages if p not in existing_ocr_pages]
            if remaining_ocr:
                self._ocr_thread = OcrPages(self.pdf_path, remaining_ocr, callback=self.on_textract_chunk)
                self._ocr_thread.start()
            else:
                print()
                print()
                print("All textract data was loaded for all specified OCR pages. No OCR is needed to run")
                print()
                print()

        # Per page multiprocessing
        if self._multiprocess:

            multiprocessing.Process
            with multiprocessing.Pool(processes=multiprocessing.cpu_count(), initializer=init_worker, initargs=(self.pdf_path,)) as pool:
                while self._pages_remaining:
                    # logger.debug(f"Pages remaining - {len(self._pages_remaining)}")
                    try:
                        page_args = self._worker_args.pop()
                        pool.apply_async(process_page_wrapper, page_args, callback=self.on_page_result_received)
                    except IndexError:
                        pass

                    if self._cancel.is_set():
                        break

            #TODO - This will the mergest the OCR data fetched in this extraction with existing data
            try:
                sourceDir = getSourceDataDir(self.project_id, self.pdf_path)
                textract = self._ocr_thread.results
                if textract.empty: # raises error
                    return
            except:
                return
            try:
                logger.info("merging textract results")
                textract2 = load_df_fast(os.path.join(sourceDir, "textract.feather"))
                textract_pages = textract['pdf_page'].unique().tolist()
                textract2 = textract2[~textract2['pdf_page'].isin(textract_pages)]
                textract = pd.concat([textract, textract2], ignore_index=True)
            except:
                pass

            try:
                logger.info("Saving combined textract results")
                save_df_fast(textract, os.path.join(sourceDir, "textract"), format="feather")
            except:
                pass

            return

        # Non-multiprocessing
        self._times = []
        while self._pages_remaining:
            logger.debug(f"Pages remaining - {self._pages_remaining}")
            try:
                page_args = self._worker_args.pop()
                doc = fitz.open(self.pdf_path)
                result = process_page_wrapper(*page_args + [doc])
                self.on_page_result_received(result)
                if self._cancel.is_set():
                    return
            except IndexError:
                pass
                # logger.debug("Waiting to receive page processing jobs to add...")
#
        if self._cancel.is_set():
            return

    def _process_final_results(self):
        """Finalize the page results"""
        logger.info("Processing final extraction results...")

        # Combine DataFrame results
        try:
            self.combined_bom_df = pd.concat(self.combined_bom_df, ignore_index=True)
        except:
            self.combined_bom_df = pd.DataFrame()

        try:
            self.combined_spec_df = pd.concat(self.combined_spec_df, ignore_index=True)
        except:
            self.combined_spec_df = pd.DataFrame()

        try:
            self.combined_spool_df = pd.concat(self.combined_spool_df, ignore_index=True)
        except:
            self.combined_spool_df = pd.DataFrame()

        try:
            self.combined_general_df = pd.concat(self.combined_general_df, ignore_index=True)
        except:
            self.combined_general_df = pd.DataFrame()

        try:
            self.combined_outlier_df = pd.concat(self.combined_outlier_df, ignore_index=True)
        except:
            self.combined_outlier_df = pd.DataFrame()

        try:
            self.combined_ifc_df = pd.concat(self.combined_ifc_df, ignore_index=True)
        except:
            self.combined_ifc_df = pd.DataFrame()

        try:
            self.combined_generic_1_df = pd.concat(self.combined_generic_1_df, ignore_index=True)
        except:
            self.combined_generic_1_df = pd.DataFrame()

        try:
            self.combined_generic_2_df = pd.concat(self.combined_generic_2_df, ignore_index=True)
        except:
            self.combined_generic_2_df = pd.DataFrame()

        try:
            self.combined_raw_df = pd.concat(self.combined_raw_df, ignore_index=True)
        except:
            self.combined_raw_df = pd.DataFrame()

        summary_df = pd.DataFrame(self.summary)
        if not summary_df.empty and not __file__.endswith(".pyc") and False:
            summary_df = summary_df.sort_values(by=['page_num'], ascending=True)
            print("Saving extraction summary results to debug/extraction_summary_df.xlsx")
            try:
                summary_df.to_excel("debug/extraction_summary_df.xlsx")
            except Exception as e:
                print("failed to save summary results", e)

            logger.info("Final Results Summary")
            for r in summary_df[summary_df["error"].notna()].itertuples():
                logger.warning(f"Skipped Page - {r.pdf_page}, Error - {r.error}")


        # Ensure that the necessary columns exist in the DataFrame
        if 'elevation' in self.combined_general_df.columns:
            # Add or initialize 'min_elevation', 'max_elevation', and 'avg_elevation' columns
            self.combined_general_df['min_elevation'] = np.nan
            self.combined_general_df['max_elevation'] = np.nan
            self.combined_general_df['avg_elevation'] = np.nan

            # Check if 'pdf_page' column exists in the DataFrame
            if 'pdf_id' in self.combined_general_df.columns:
                self.combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = self.combined_general_df.apply(
                    lambda row: calculate_elevation_metrics(row, row['pdf_id']), axis=1, result_type='expand'
                )
            else:
                logger.warning("'pdf_id' column not found in the DataFrame. Calculating elevation metrics without page information.")
                self.combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = self.combined_general_df.apply(
                    calculate_elevation_metrics, axis=1, result_type='expand'
                )
            # combined_general_df[['min_elevation', 'max_elevation', 'avg_elevation']] = combined_general_df.apply(
            #     calculate_elevation_metrics, axis=1, result_type='expand'
            # )
        else:
            # Handle the case where 'elevation' column does not exist
            logger.debug("'elevation' column not found in the DataFrame.")

        logger.info("Cleaning final DataFrames...")
        # Replace 'nan' (string), np.nan and pd.NA with blank ('') in the DataFrames

        def replace_nans(df):
            for column in df.columns:
                try:
                    df[column] = df[column].replace(['nan', np.nan, pd.NA], '')
                except:
                    pass

            return df

        self.combined_bom_df = replace_nans(self.combined_bom_df)
        self.combined_spec_df = replace_nans(self.combined_spec_df)
        self.combined_spool_df = replace_nans(self.combined_spool_df)
        self.combined_general_df = replace_nans(self.combined_general_df)
        self.combined_raw_df = replace_nans(self.combined_raw_df)
        self.combined_outlier_df = replace_nans(self.combined_outlier_df)
        self.combined_generic_1_df = replace_nans(self.combined_generic_1_df)
        self.combined_generic_2_df = replace_nans(self.combined_generic_2_df)
        self.combined_ifc_df = replace_nans(self.combined_ifc_df)

        # self.combined_bom_df = self.combined_bom_df.replace(['nan', np.nan, pd.NA], '')
        # self.combined_spec_df = self.combined_spec_df.replace(['nan', np.nan, pd.NA], '')
        # self.combined_spool_df = self.combined_spool_df.replace(['nan', np.nan, pd.NA], '')
        # self.combined_general_df = self.combined_general_df.replace(['nan', np.nan, pd.NA], '')
        # self.combined_raw_df = self.combined_raw_df.replace(['nan', np.nan, pd.NA], '')
        # self.combined_outlier_df = self.combined_outlier_df.replace(['nan', np.nan, pd.NA], '')

        # Apply the function to the entire DataFrame
        try:
            self.combined_bom_df = self.combined_bom_df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)
        except Exception as e:
            logger.warning(f"Could not replace non standard characters in combined BOM: {e}")

        try:
            self.combined_general_df = self.combined_general_df.map(lambda x: replace_non_standard_characters(x) if isinstance(x, str) else x)
        except Exception as e:
            logger.warning(f"Could not replace non standard characters in combined General: {e}")

        # End Extraction Timer
        et_extract = time.perf_counter()
        extract_time = et_extract - self.start_time

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Data extracted")

        #Logging
        logger.info("-------------------------------------------------------------------------------------------")
        logger.info("-------------------------------------------------------------------------------------------")
        logger.info("-------------------------------------------------------------------------------------------")
        info = (
            "\nData Consolidated...",
            f"General Data Rows: {len(self.combined_general_df)}",
            f"BOM Table Rows: {len(self.combined_bom_df)}\nSPOOL Table Rows: {len(self.combined_spool_df)}",
            f"SPOOL Table Rows: {len(self.combined_spool_df)}",
            f"SPEC Table Rows: {len(self.combined_spec_df)}",
            f"IFC Table Rows: {len(self.combined_ifc_df)}",
            f"Generic 1 Table Rows: {len(self.combined_generic_1_df)}",
            f"Generic 2 Table Rows: {len(self.combined_generic_2_df)}",
            f"RAW Data Rows: {len(self.combined_raw_df)}",
        )
        logger.info("\n".join(info))

        # print_metrics(self._total_pages, extract_time)

        # if rename_files == True and platform.system() == 'Windows':
        #     logger.error("Not yet implemented BOM_IMPORT")

        merge_to_bom = PAYLOAD_OPTIONS.get('merge_to_bom', [])
        bom_merge_on = PAYLOAD_OPTIONS.get('bom_merge_on', [])

        # Filter out the columns that do not exist in combined_general_df
        bom_merge_on = [col for col in bom_merge_on if col in self.combined_general_df.columns]
        merge_to_bom = [col for col in merge_to_bom if col in self.combined_general_df.columns]

        # Merge columns to BOM using 'merge_to_bom' list
        if not self.combined_bom_df.empty and not self.combined_general_df.empty:
            try:
                self.combined_bom_df = self.combined_bom_df.merge(self.combined_general_df[merge_to_bom + bom_merge_on],
                                                                  on=bom_merge_on,
                                                                  how='left')
            except Exception as e:
                logger.error(f"An error occurred when combining BOM: {e}")

        # Combine SPEC tables and General Data
        try:
            if len(self.combined_general_df) > 0 and len(self.combined_spec_df) > 0:

                # Check if 'size' field exists in combined_spec_df
                if 'size' in self.combined_spec_df.columns:
                    # Apply parse_complex_size function to the 'size' column
                    self.combined_spec_df['size'] = self.combined_spec_df['size'].apply(parse_complex_size)

                # Call the helper function to merge 'combined_spec_df' and 'combined_general_df'
                self.combined_general_df = merge_spec_and_general_data(self.combined_spec_df, self.combined_general_df)
        except Exception as e:
            logger.warning(f"Error checking general data length: {e}", exc_info = True)

        # --> Trim all columns
        self.combined_raw_df = trim_all_columns(self.combined_raw_df)
        self.combined_bom_df = trim_all_columns(self.combined_bom_df)
        self.combined_spec_df = trim_all_columns(self.combined_spec_df)
        self.combined_spool_df = trim_all_columns(self.combined_spool_df)
        self.combined_general_df = trim_all_columns(self.combined_general_df)
        self.combined_outlier_df = trim_all_columns(self.combined_outlier_df)

        print(f"\n---------------------\nTOTAL BOM ROWS: {len(self.combined_bom_df)}")
        print(f"TOTAL GENERAL ROWS: {len(self.combined_general_df)}\n---------------------\n")

        if INSPECT_RAW:
            print("\n\n-------------------")
            print("-------------------")
            print(f"DEBUG: Exporting Raw data. Rows={len(self.combined_raw_df)}")
            print("-------------------")
            self.combined_raw_df.to_excel("_debug_raw_data_full.xlsx")
            print("--> RAW DATA EXPORTED - TESTING")

        if EXPORT_OUTLIER:
            print("\n\n-------------------")
            print("-------------------")
            print("-------------------")

            self.combined_outlier_df.to_excel("_outlier_data_full.xlsx")
            print("--> OUTLIER DATA EXPORTED - TESTING")

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Data Preprocessing", jobId=self.jobId)

        logger.warning("Analyze BOM not implemented")

        pub.sendMessage("set-statusbar-realtime", message="ROI Extraction - Finalizing Result", jobId=self.jobId)

        if not self.combined_outlier_df.empty:
            # reorder columns to pdf_page to start
            outlier_start_columns = ["outlier_scope", "outlier_scope_2", "outlier_reason", "outlier_action", "value"]
            for column in outlier_start_columns:
                if column not in self.combined_outlier_df.columns:
                    self.combined_outlier_df[column] = ""
            self.combined_outlier_df = self.combined_outlier_df.reindex(columns=['pdf_page']
                                    + outlier_start_columns
                                    + [col for col in self.combined_outlier_df.columns if col not in ['pdf_page']
                                    + outlier_start_columns])

        final_df = {
            "bom_data": self.combined_bom_df,
            "spool_data": self.combined_spool_df,
            "spec_data": self.combined_spec_df,
            "general_data": self.combined_general_df, #general_data_df,
            "outlier_data": self.combined_outlier_df,
            "generic_1_data": self.combined_generic_1_df,
            "generic_2_data": self.combined_generic_2_df,
            "ifc_data": self.combined_ifc_df,
            #"raw_data": raw_data_json,
        }

        for key, df in final_df.items():
            try:
                if not df.empty:
                    df = df.reset_index().sort_values(by=['pdf_page', 'index'],
                                                        ascending=[True, True],
                                                        key=natsort_keygen()).drop('index', axis=1)
                    final_df[key] = df
            except Exception as e:
                logger.warning(f"Error sorting {key} DataFrame: {e}")

        # Temporary - workflow adjustment for audit
        if "bom_data" in final_df:
            if "quantity" in final_df["bom_data"]:
                # from src.atom.pg_database.data_uploads.field_mapping import audit_workbook
                final_df["bom_data"]["quantity_original"] = final_df["bom_data"]["quantity"].copy()
                final_df["bom_data"]["size_original"] = final_df["bom_data"]["size"].copy()

        table_to_key = {
            "bom": "bom_data",
            "spool": "spool_data",
            "spec": "spec_data",
            "generic_1": "generic_1_data",
            "generic_2": "generic_2_data",
            "ifc": "ifc_data",
        }

        first_group_payload = self.roi_payload["groupRois"][list(self.roi_payload["groupRois"].keys())[0]] # check the first group. All groups should have same rois
        # Ensure originally requested fields are returned in result df
        for roi in first_group_payload:
            try:
                columnName = roi["columnName"]
                if "tableCoordinates" in roi:
                    df = final_df[table_to_key[columnName.lower()]]
                    for tableColumn in roi.get("tableColumns", []):
                        field = list(tableColumn.keys())[0]
                        if field not in df:
                            df[field] = np.nan
                else:
                    field = columnName
                    df = final_df["general_data"]
                    if field not in df:
                        df[field] = np.nan
            except Exception as e:
                logger.debug("Failed to insert requested field to final dataframe", exc_info=True)

        end_time1 = time.time()

        # Filter out JSON objects that have the 'tableCoordinates' property
        #print(f"\n\n--BOM TEST 4: \n{len(combined_bom_df)}")
        try:
            print("\n\n\n CLEANING BOM USING OUTLIER DATA")
            filtered_json = [item for item in first_group_payload if 'tableCoordinates' not in item]

            # Extract the 'columnName' values
            json_column_names = {item['columnName'] for item in filtered_json}

            # Find the intersection with DataFrame columns
            matching_columns = set(self.combined_general_df.columns).intersection(json_column_names)

            # Convert to a list if needed
            matching_columns_list = list(matching_columns)

            # Sort the matching column names list in ascending order, case-insensitive
            sorted_columns_list = sorted(matching_columns_list, key=lambda s: s.lower())

            #Clean data using the outlier data.

        except Exception as e:
            logger.error(f"An Exception occured while trying to parse the JSON coordinates. EXCEPTION: {e}")
            return

        logger.info("--> EXTRACTION/ASSIGNMENT PROCESS COMPLETE")

        logger.warning("Commit Raw Data not yet implemented. Preprocessed raw data should have been stored.")
        logger.warning("Additional OCR Raw Data may not yet be saved - TODO")

        self._results_df = final_df
        self.sorted_columns_list = sorted_columns_list

        # Clears references to DataFrames
        self.reset_result_dataframes()
        self._raw_df = pd.DataFrame()

        logger.info("Extraction complete")

    def run(self):
        """Run preprocessing"""
        pub.sendMessage("set-statusbar-realtime", message="Preprocessing Started", jobId=self.jobId)
        self.start_time = time.perf_counter()  # Capture the start time
        self._timer.start()
        for n, state in enumerate([
            self._initialize,
            self._process_pages,
            self._process_final_results,
        ]):
            if self._cancel.is_set():
                print(f"Cancelled preprocessing at step {n} - function `{state.__name__}`")
                self._results_df = None
                return
            print(f"State {n} - function `{state.__name__}`")
            state()

        self._cancel.set()  # Done

    def cancel(self):
        """Cancel and all running tasks"""
        logger.debug("Canceling")
        self._cancel.set()

    def on_timer(self):
        smoothing_factor = 0.05
        while not self._cancel.is_set():
            time.sleep(1)
            if not self._eta_updated_cb:
                continue

            if self._total_pages is None:
                continue
            lock.acquire()
            times = self._times[-5:]
            remaining_pages = max(0, self._total_pages - len(self._times))
            lock.release()
            if len(times) < 5:
                continue
            eta = int(statistics.fmean(times) * remaining_pages)
            if self._last_eta is None:
                self._last_eta = eta
                continue
            eta = smoothing_factor * self._last_eta + (1 - smoothing_factor) * eta
            self._last_eta = int(eta)
            if self._eta_updated_cb:
                self._eta_updated_cb(self._last_eta)

        if self._eta_updated_cb:
            self._eta_updated_cb(0)

    def get_results(self):
        return self._results_df

    def on_page_result_received(self, page_result):
        """Handle incoming extraction result from a page"""
        page_num, raw_data_df, annot_types_df, annot_tables, text_tables, outliers_df, process_time, skipped, error = page_result

        logger.info(f"Handling page result for Page {page_num}")

        try:
            # print(text_tables)
            # Get the TEXT type tables from the structure
            bom_df = get_structured_table('BOM', text_tables)
            spec_df = get_structured_table('SPEC', text_tables)
            spool_df = get_structured_table('Spool', text_tables)
            ifc_df = get_structured_table('IFC', text_tables)
            generic_1_df = get_structured_table('generic_1', text_tables)
            generic_2_df = get_structured_table('generic_2', text_tables)
        except Exception as e:
            logger.error(f"Could not get the text type table(s): {e}", exc_info=True)

            with self.process_worker_lock:
                del self._pages_remaining[page_num]
                self.remaining = len(self._pages_remaining)
                return

        # TODO - unhandled currently
        try:
            # Get the ANNOTATION type tables from the structure
            bom_annot_df = get_structured_table('BOM', annot_tables)
            spec_annot_df = get_structured_table('SPEC', annot_tables)
            spool_annot_df = get_structured_table('Spool', annot_tables)
        except Exception as e:
            logger.error(f"Could not get the text type table(s): {e}", exc_info=True)

        with self.process_worker_lock:
            try:
                self.summary.append({"page_num": page_num, "skipped": skipped, "error": error})

                pdf_id = self.page_num_to_pdf_id[page_num]
                if not bom_df.empty:
                    bom_df["pdf_id"] = pdf_id
                    self.combined_bom_df.append(bom_df)

                if not spec_df.empty:
                    spec_df["pdf_id"] = pdf_id
                    self.combined_spec_df.append(spec_df)

                if not spool_df.empty:
                    spool_df["pdf_id"] = pdf_id
                    self.combined_spool_df.append(spool_df)

                if not annot_types_df.empty:
                    annot_types_df["pdf_id"] = pdf_id
                    self.combined_general_df.append(annot_types_df)

                if not outliers_df.empty:
                    outliers_df["pdf_id"] = pdf_id
                    self.combined_outlier_df.append(outliers_df)

                if not raw_data_df.empty:
                    self.combined_raw_df.append(raw_data_df)

                if not generic_1_df.empty:
                    generic_1_df["pdf_id"] = pdf_id
                    self.combined_generic_1_df.append(generic_1_df)

                if not generic_2_df.empty:
                    generic_2_df["pdf_id"] = pdf_id
                    self.combined_generic_2_df.append(generic_2_df)

                if not ifc_df.empty:
                    ifc_df["pdf_id"] = pdf_id
                    self.combined_ifc_df.append(ifc_df)

            except Exception as e:
                logger.error("Critical - failed to handle page result", exc_info=True)

            del self._pages_remaining[page_num]
            self.remaining = len(self._pages_remaining)

    def get_results(self):
        return self._results_df

if __name__ == "__main__":
    import json
    from src.utils import convert_roi_payload
    from src.atom.dbManager import DatabaseManager
    from src.utils.logger import logger
    import pandas as pd
    import os

    # Project and file setup
    project_id = 7
    project_id = 1
    pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\output_tests\Test_original_1.pdf"
    pdf_path = r"C:/Drawings/Clients/axisindustries/Axis 2025-06-18 All Isos/received/All Isos.pdf"
    pdf_path = r"C:/Drawings/Clients/brockservices/27_BSLE32128/received/20250617+Carbon_Steel_COMBINED.pdf"
    pdf_path = r"C:/Drawings/Clients/brockservices/BRS_0029 - 160001-0083850 B-6400 NEW POTABLE WATER WELL %23107/received/20250812+S160001-0083850__IFB.pdf"


    # IMPORTANT: Use 1-based page numbers (as seen in PDF viewer)
    # This is what the RoiExtraction class expects
    # target_pages = [13] # List: [1,2,5,etc] (One based)
    # If you want to process all pages, use:
    # target_pages = [46]

    multiprocess = False
    debug = True

    # # Load and analyze ROI payload
    # with open(roi_path, 'r') as f:
    #     roi_payload = json.load(f)

    roi_payload = getSourceExtractionOptionsPath(project_id, pdf_path)
    roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)
    roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

    final_pages = set()
    unique_groups = set()
    cleaned_roi_payload = {
        "ocr": {}
    }
    cleaned_roi_payload['pageToGroup'] = {}
    for page, group in roi_payload['pageToGroup'].items():
        cleaned_roi_payload['pageToGroup'][page] = group
        final_pages.add(page)
        unique_groups.add(group)

    table_names = [
        "bom",
        # "general",
        # "spool",
        # "spec",
        # "ifc",
        # "generic_1",
        # "generic_2"
    ]

    final_groups = set()
    cleaned_roi_payload['groupRois'] = {}
    for group, rois in roi_payload['groupRois'].items():
        cleaned = []
        for roi in rois:
            column_name = roi["columnName"].lower()
            # if column_name == "bom":
            #     continue
            # if not extract_general and column_name == "general":
            #     continue
            # if not extract_spool and column_name == "spool":
            #     continue
            # if not extract_spec and column_name == "spec":
            #     continue
            # if not extract_ifc and column_name == "ifc":
            #     continue
            # if not extract_generic_1 and column_name == "generic_1":
            #     continue
            # if not extract_generic_2 and column_name == "generic_2":
            #     continue

            # if column_name not in table_names:
            #     if not extract_general:
            #         continue
            #     if general_fields and column_name not in general_fields:
            #         continue
            cleaned.append(roi)

        if cleaned:
            cleaned_roi_payload['groupRois'][group] = cleaned
            final_groups.add(group)

    roi_payload = cleaned_roi_payload

    # Load raw data
    raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Modified\output_tests\_debug_raw_data_full.xlsx"
    # raw_df = pd.read_excel(raw_data_path)
    raw_df = None

    # Create PDF ID mapping from raw data
    # manual_pdf_ids = {}
    # for _, row in raw_df.drop_duplicates(['pdf_page', 'pdf_id']).iterrows():
    #     manual_pdf_ids[int(row['pdf_page'])] = row['pdf_id']

    # Debug information
    print(f"\nProcessing pages: {target_pages}")
    # print(f"Raw data has {len(raw_df)} rows")
    # for page in target_pages if target_pages else []:
    #     page_data = raw_df[raw_df['pdf_page'] == page]
    #     print(f"Data for page {page}: {len(page_data)} rows")

    # Monkey patch the database method
    # original_get_map = DatabaseManager.get_source_pdf_id_map
    # DatabaseManager.get_source_pdf_id_map = lambda self, *args, **kwargs: manual_pdf_ids

    try:
        # Create processor with 1-based page numbers
        processor = RoiExtraction(
            project_id=project_id,
            pdf_path=pdf_path,
            roi_payload=None,
            pages=target_pages,  # RoiExtraction expects 1-based page numbers
            raw_df=raw_df,
            missing_regions=pd.DataFrame(),
            multiprocess=multiprocess,
            debug=debug
        )

        # Add debug logging
        original_initialize = processor._initialize
        def patched_initialize():
            original_initialize()
            # Show what pages will actually be processed
            processed_pages = [p+1 for p in processor._extract_pages]  # Convert back to 1-based for display
            print(f"\nWill process these pages: {processed_pages}")

            # Check for critical issues
            if not processed_pages:
                print("\n⚠️ WARNING: No pages will be processed! Check that:")
                print("  1. The pages you specified exist in the PDF")
                print("  2. The pages have entries in the ROI payload pageToGroup")
                print("  3. The ROI payload has the correct structure")

        # processor._initialize = patched_initialize

        # Run the extraction
        start = time.perf_counter()
        processor.run()

        # Get and print results
        results = processor.get_results()
        print("\n=== RESULTS SUMMARY ===")
        for key, df in results.items():
            print(f"{key}: {len(df)} rows")

        # Create an output directory
        # output_dir = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Received\TR-001 Tests\extraction_results"
        output_dir = r"debug/extraction_results"
        os.makedirs(output_dir, exist_ok=True)

        # # Method 1: Save each dataframe to a separate file
        # for key, df in results.items():
        #     if not df.empty:
        #         output_file = os.path.join(output_dir, f"{key}.xlsx")
        #         df.to_excel(output_file, index=False)
        #         print(f"Saved {key} to {output_file}")

        # Method 2: Save all dataframes to different sheets in one file
        # This approach creates a single file with multiple sheets
        with pd.ExcelWriter(os.path.join(output_dir, "all_results.xlsx")) as writer:
            for key, df in results.items():
                sheet_name = key[:31]  # Excel sheet names limited to 31 chars
                if not df.empty:
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # Create an empty sheet with a message
                    pd.DataFrame({"Message": ["No data found"]}).to_excel(
                        writer, sheet_name=sheet_name, index=False
                    )
            print(f"Saved all results to {os.path.join(output_dir, 'all_results.xlsx')}")


    except Exception as e:
        import traceback
        print(f"Error running extraction: {e}")
        traceback.print_exc()
    # finally:
    #     # Restore the original method
    #     DatabaseManager.get_source_pdf_id_map = original_get_map

    print(f"Processing time {time.perf_counter() - start}s")

# if __name__ == "__main__":

#     run_ide = True

#     if run_ide == False:

#         print(sys.argv[1:])

#         try:
#             pdf_path = sys.argv[1]
#         except Exception as e:
#             raise ValueError("pdf_path required for preprocessing")

#         try:
#             pages = sys.argv[2]
#         except Exception as e:
#             pages = None # All pages

#     start = time.perf_counter()

#     def print_eta(eta):
#         print(f"ETA: {eta}")

#     def test_cancel(n=None):
#         # Test function to cancel processing after `n` seconds
#         import time
#         if n is None:
#             return
#         time.sleep(n)
#         processor.cancel()

#     time_to_cancel = None

#     # project_id = 2

#     # roi_payload = r"C:\Drawings\SanMateoOCR\sanmateo.json"
#     # roi_payload = r"C:\Users\<USER>\Documents\Drawings\binder.json"

#     # roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\1\sources\cuserss1documentsdrawingsexxonexxon-documentspdf\options.json"
#     # roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\1\sources\cuserss1documentsdrawingsbinder1pdf\options.json"
#     # # roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

#     # # Test 2
#     # # project_id = 2
#     # pdf_path = r"C:/Users/<USER>/Desktop/remuriate/Combined Remuriate.pdf"
#     # roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\2\sources\cuserss1desktopremuriatecombined-remuriatepdf\options.json"
#     # roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

#     # project_id = 7
#     # pdf_path = r"C:/Drawings/Axis 25 - Combined Sources/axis25-combined.pdf"
#     # roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\7\sources\cdrawingsaxis-25-combined-sourcesaxis25-combinedpdf\options.json"
#     # roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

#     # project_id = 1
#     # pdf_path = r"C:/Users/<USER>/Documents/Drawings/RCM Combined.pdf"
#     # roi_payload = r"C:\Users\<USER>\AppData\Local\Architekt\Architekt\data\1\sources\cuserss1documentsdrawingsrcm-combinedpdf\options.json"
#     # roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)


#     project_id = 10
#     pdf_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Received\TR-001 Tests\Test Revisions.pdf"
#     roi_payload = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Received\TR-001 Tests\roi-111.json"
#     roi_payload = convert_roi_payload.convert_roi_payload(roi_payload)

#     # Pass raw data
#     raw_data_path = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Received\TR-001 Tests\_raw_test_revisions.xlsx"

#     MULTIPROCESS = True

#     pages = [n + 1 for n in range(10)]
#     pages = [1,2]
#     pages = [1]
#     pages = None
#     # pages = [n + 1 for n in range(12)]

#     Thread(target=lambda: test_cancel(time_to_cancel)).start()
#     processor = RoiExtraction(project_id=project_id,
#                                  pdf_path=pdf_path,
#                                  roi_payload=roi_payload,
#                                  pages=pages,
#                                  raw_df=raw_data_path, # Pass the path to raw data
#                                  multiprocess=MULTIPROCESS,
#                                  debug=DEBUG_MODE)
#     processor._eta_updated_cb = print_eta
#     processor.run()

#     print(processor.get_results())

#     print(f"Processing time {time.perf_counter() - start}s")
