"""
PDF viewer and ROI editor

"""
import os
import weakref
import fitz
import json
import re

from PySide6.QtWidgets import *
from PySide6.QtGui import *
from PySide6.QtCore import *
import pandas as pd
from pubsub import pub
from copy import deepcopy
from functools import partial
from threading import Lock
from natsort import natsorted

from src.pyside_util import get_resource_qicon
from src.data import roi_table_scopes
from src.app_paths import getSavedFieldMapJson, getSourceDataDir, getSourceMissingRegionsPath, getSourceMissingRegionsPath, getSourceOcrPath
from src.views.dialogs.isometricregion import IsometricRegionPopup
from src.views.popups import HelpPopup
from src.widgets.mybutton import MyButton
from src.widgets.pandastable import PandasTable
from src.pyside_util import get_resource_qicon, applyDropShadowEffect
from src.atom.fast_storage import load_df_fast
from src.utils.logger import logger
from src.widgets.flowlayout import FlowLayout
from src.utils.convert_roi_payload import convert_roi_payload, convert_rois
from src.app_paths import getSourceDataDir, getSourceMetadataPath, getSourceRawDataPath, getRoiConfig
from src.views.extractionpreview import ExtractionPreview
from src.widgets.busybutton import BusyButton

# This logger will inherit configurations from the root logger configured in main.py
# logger = logging.getLogger(__name__)

logger.debug("Logger Format?")


HK_SELECT_REGION = Qt.Key.Key_Shift

HELP_HTML = """
<h3>Document Viewer</h3>
<p>
The document viewer supports data extraction from ROIs.
<br>
<br>
<b>Select Region</b> - Create an ROI by selecting a region of the document.
<br>
<br>
<b>Extract</b> - Run extract to perform data extraction the ROIs.
The extraction results are populated into categorized tables, each displayed in its own tab.
<br>
</p>

<h3>Keyboard shortcuts</h3>
<p>
<b>Draw new ROI (Select Region)</b>
<i>Shift + Left click</i>
</p>
<p>
<b>Pan</b>
<i>Mouse middle click + drag</i>
</p>
<p>
<b>Zoom In/Out</b>
<i>Ctrl + Mouse Wheel Up/Down</i>
</p>
"""

TABLE_NAMES = ["BOM", "SPEC", "Spool", "IFC", "generic_1", "generic_2"]

def is_overlapping(l1: QPointF, r1: QPointF, l2: QPointF, r2: QPointF) -> bool:
    """Return True if two rects overlap, where lx and rx are top left
    and bottom right coordinates of a rect respectively
    """
    if l1.x() < r2.x():
        pass
    if r1.x() > l2.x():
        pass
    if l1.y() > r2.y() :
        pass
    if r1.y() < l2.y():
        pass
    return (l1.x() < r2.x()
        and r1.x() > l2.x()
        and l1.y() < r2.y()
        and r1.y() > l2.y())


class ExtractionPreviewWorker(QObject):

    finished = Signal(object)

    def __init__(self, projectId: int, filename: str):
        super().__init__()
        self.projectId = projectId
        self.filename = filename

    def run(self):
        self.thread().exit()

class PreprocessWorker(QObject):

    finished = Signal(object)

    def __init__(self, projectId: int, filename: str):
        super().__init__()
        self.projectId = projectId
        self.filename = filename

    def run(self):
        pub.sendMessage("preprocess-drawings",
                        projectId=self.projectId,
                        filename=self.filename,
                        callback=lambda x: self.finished.emit(x))
        self.thread().exit()

class DetectPageGroupsWorker(QObject):

    finished = Signal(object)

    def __init__(self, projectId: int, filename: str):
        super().__init__()
        self.projectId = projectId
        self.filename = filename

    def run(self):
        pub.sendMessage("analyze-drawings",
                        projectId=self.projectId,
                        filename=self.filename,
                        callback=lambda x: self.finished.emit(x))
        self.thread().exit()


class EditLayoutPropertiesDialog(QDialog):

    def __init__(self, parent, documentVendor: str, tags: list):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())

        self.lineDocVendor = QLineEdit()
        self.layout().addWidget(QLabel(text="Document Vendor"))
        self.layout().addWidget(self.lineDocVendor)
        self.layout().addWidget(QLabel(text="Tags (Comma separated)"))
        self.lineTags = QLineEdit()
        self.layout().addWidget(self.lineTags)

        hbox = QWidget()
        hbox.setFixedHeight(64)
        hbox.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox)
        pbSave = QPushButton("Save")
        hbox.layout().addWidget(pbSave)
        pbCancel = QPushButton("Cancel")
        hbox.layout().addWidget(pbCancel)

        pbSave.clicked.connect(self.accept)
        pbCancel.clicked.connect(self.reject)
        # pbSave.setFixedHeight(32)
        # pbCancel.setFixedHeight(32)
        self.lineDocVendor.setText(documentVendor)
        if tags:
            self.lineTags.setText(",".join(tags))

        self.setMinimumHeight(320)
        self.setMinimumWidth(320)

        self.setWindowTitle("Edit ROI Layout Properties")

    def getValues(self):
        text = self.lineTags.text()
        if not text or text.isspace():
            tags = []
        else:
            tags = text.split(",")
            tags = [t.strip() for t in tags]
        return {
            "documentVendor": self.lineDocVendor.text(),
            "tags": tags,
        }


class StyledCheckboxDelegate(QStyledItemDelegate):
    """QTableView centered checkbox delegate"""

    itemChecked = Signal(object, bool)
    def __init__(self, parent=None):

        super().__init__(parent=parent)


    def paint(self, painter, option, index):
        self.initStyleOption(option, index)
        widget = option.widget
        style = widget.style() if widget else QApplication.style()
        style.drawPrimitive(
                QStyle.PrimitiveElement.PE_PanelItemViewItem,
                option,
                painter,
                widget
            )

        if (QStyleOptionViewItem.ViewItemFeature.HasCheckIndicator in option.features):
            if option.checkState == Qt.CheckState.Unchecked:
                option.state |= QStyle.StateFlag.State_Off
            else:
                option.state |= QStyle.StateFlag.State_On

            rect = style.subElementRect(
                QStyle.SubElement.SE_ItemViewItemCheckIndicator,
                option,
                widget
            )
            option.rect = QStyle.alignedRect(
                option.direction,
                Qt.AlignmentFlag(Qt.AlignmentFlag.AlignCenter),
                rect.size(),
                option.rect
            )
            option.state &= ~QStyle.StateFlag.State_HasFocus
            style.drawPrimitive(
                QStyle.PrimitiveElement.PE_IndicatorItemViewItemCheck,
                option,
                painter,
                widget
            )

        elif not option.icon.isNull():
            icon_rect = style.subElementRect(
                QStyle.SubElement.SE_ItemViewItemDecoration,
                option,
                widget
            )
            icon_rect = QStyle.alignedRect(
                option.direction,
                Qt.AlignmentFlag(
                        index.data(Qt.ItemDataRole.TextAlignmentRole).value
                        ),
                icon_rect.size(),
                option.rect
            )
            mode = QIcon.Mode.Normal
            if QStyle.StateFlag.State_Enabled not in option.state:
                mode = QIcon.Mode.Disabled
            elif QStyle.StateFlag.State_Selected in option.state:
                mode = QIcon.Mode.Selected

            state = (
                QIcon.State.On
                if QStyle.StateFlag.State_Open in option.state
                else QIcon.State.Off
            )
            option.icon.paint(
                painter,
                icon_rect,
                option.decorationAlignment,
                mode,
                state
            )

        else:
            super().paint(painter, option, index)


    def editorEvent(self, event, model, option, index):

        # Make sure that the item is checkable
        flags = model.flags(index)
        if (Qt.ItemFlag.ItemIsUserCheckable not in flags
                or QStyle.StateFlag.State_Enabled not in option.state
                or Qt.ItemFlag.ItemIsEnabled not in flags):
            return False

        # Make sure that we have a check state
        state = index.data(Qt.ItemDataRole.CheckStateRole)
        if state is None:
            return False

        widget = option.widget
        style = widget.style() if widget else QApplication.style()

        # Make sure that we have the right event type
        if event.type() in (
                QEvent.Type.MouseButtonRelease,
                QEvent.Type.MouseButtonDblClick,
                QEvent.Type.MouseButtonPress,
                ):
            view_opt = QStyleOptionViewItem(option)
            self.initStyleOption(view_opt, index)
            check_rect = style.subElementRect(
                QStyle.SubElement.SE_ItemViewItemCheckIndicator,
                view_opt,
                widget,
            )
            check_rect = QStyle.alignedRect(
                view_opt.direction,
                Qt.AlignmentFlag(
                        Qt.AlignmentFlag.AlignCenter
                        ),
                check_rect.size(),
                view_opt.rect,
            )
            if (isinstance(event, QMouseEvent)
                    and (event.button() != Qt.MouseButton.LeftButton
                         or not check_rect.contains(event.position().toPoint())
                         )):
                return False

            if event.type() in (
                    QEvent.Type.MouseButtonPress,
                    QEvent.Type.MouseButtonDblClick,
                    ):
                return True

        elif event.type() == QEvent.Type.KeyPress:
            if event.key() not in (
                    Qt.Key.Key_Space,
                    Qt.Key.Key_Select,
                    ):
                return False

        else:
            return False

        # convert to an Enum for easy comparison
        state = Qt.CheckState(state)
        # if Qt.ItemFlag.ItemIsUserTristate in flags:
        #     state = Qt.CheckState((state.value + 1) % 3)

        if state == Qt.CheckState.Unchecked:
            state = Qt.CheckState.Checked
        else:
            state = Qt.CheckState.Unchecked

        # set the new checkbox state in the model (as its Enum value)
        return model.setData(
            index,
            state.value,
            Qt.ItemDataRole.CheckStateRole,
        )

class MyProxyStyle(QProxyStyle):

    """Reduces the delay in displaying a tooltip"""

    def styleHint(self, hint, option: QStyleOption = None, widget: QWidget = None, returnData: QStyleHintReturn = None):

        if (hint == QStyle.StyleHint.SH_ToolTip_WakeUpDelay):
            return 20

        return super().styleHint(hint, option, widget, returnData)

class HoverRectItem(QGraphicsRectItem, QObject):

    hovered = Signal(bool)
    def __init__(self, start, end, text=""):
        QObject.__init__(self)
        super().__init__(None)
        self.setRect(QRect(start, end))
        self.setAcceptHoverEvents(True)
        self.hovered.connect(self.onHover)
        self.text = text

    def hoverEnterEvent(self, event):
        self.hovered.emit(True)
        return super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        self.hovered.emit(False)
        return super().hoverLeaveEvent(event)

    def onHover(self, hovered: bool):
        brush = self.brush()
        color = brush.color()
        color.setAlphaF(0.5 if hovered else 0.1)
        brush.setColor(color)
        self.setBrush(brush)


class SyncLayoutPopup(QWidget):

    applied = Signal()
    def __init__(self, parent = None, groups: dict = {}):
        super().__init__(parent)
        self._currentGroup = None
        self._groups = groups
        self._currentGroupChk = None
        self.checkBoxes = {}
        self._checked = set() # Keep state of user checked
        self.initUi()
        self.updateCheckboxes()

    def initUi(self):
        self.setLayout(QVBoxLayout())
        # self.layout().setSpacing(0)

        label = QLabel("Synchronize changes made in this layout\nwith layouts in the other checked groups")
        self.layout().addWidget(label)

        self.layout().addWidget(QLabel("Groups"))

        self.flow = QWidget()
        self.flow.setLayout(FlowLayout(self.flow))
        self.layout().addWidget(self.flow)
        self.flow.layout().setContentsMargins(0, 0, 0, 0)
        self.flow.setMinimumHeight(64)

        self.chkRemove = QCheckBox("Remove ROIs not in this layout")
        self.layout().addWidget(self.chkRemove)

        hbox3 = QWidget()
        hbox3.setLayout(QHBoxLayout())
        self.layout().addWidget(hbox3)
        hbox3.layout().setContentsMargins(0, 0, 0, 0)
        self.chkAll = QPushButton("Check All")
        self.chkAll.clicked.connect(self.onCheckAll)
        hbox3.layout().addWidget(self.chkAll)

        self.chkClear = QPushButton("Clear")
        self.chkClear.clicked.connect(self.onClearChecked)
        hbox3.layout().addWidget(self.chkClear)

        self.pbApply = QPushButton("Apply")
        self.pbApply.clicked.connect(lambda: self.applied.emit())
        hbox3.layout().addWidget(self.pbApply)

        self.mousePressEvent = self.blockMouseEvent
        self.setMinimumWidth(360)

    def isSyncEnabled(self) -> bool:
        return self.chkSyncEnabled.isChecked()

    def updateState(self, groups: list, currentGroup: int):
        self._groups = groups
        self._currentGroup = currentGroup
        self.updateCheckboxes()
        self.chkRemove.setChecked(False)

    def updateCheckboxes(self):
        for n, chk in self.checkBoxes.items():
            chk.setParent(None)
            self.flow.layout().removeWidget(chk)
            del chk

        self.checkBoxes = {}

        for (n, _) in self._groups:
            chk = QCheckBox(f"{n}")
            if self._currentGroup == n:
                chk.setText(f"{n} (Current Group)")
                chk.setChecked(True)
                self._currentGroupChk = chk
            if n in self._checked:
                chk.setChecked(True)
            self.checkBoxes[n] = chk
            self.flow.layout().addWidget(chk)
            chk.clicked.connect(partial(self.onCheckBox, chk, n))

        if self._currentGroupChk:
            self._currentGroupChk.clicked.connect(lambda: self._currentGroupChk.setChecked(True))

    def blockMouseEvent(self, event=None):
        """QMenu is sensitive to mouse click events. Reduce accidental closing of
        menu"""
        pass

    def onCheckBox(self, chk: QCheckBox, group):
        if chk == self._currentGroupChk:
            chk.setChecked(True)
            return
        print(group)
        if chk.isChecked():
            self._checked.add(group)
        else:
            self._checked.discard(group)

    def onCheckAll(self):
        for n, chk in self.checkBoxes.items():
            chk.setChecked(True)
            self._checked.add(n)

    def onClearChecked(self):
        for n, chk in self.checkBoxes.items():
            if self._currentGroupChk == chk:
                continue
            chk.setChecked(False)
        self._checked.clear()

    def getCheckedGroups(self):
        checked = []
        for n, chk in self.checkBoxes.items():
            if n == self._currentGroup:
                continue
            if chk.isChecked():
                checked.append(n)
        return checked

    def clear(self):
        """Reset UI State"""
        self.onClearChecked()

    def isSyncRoiRemove(self) -> bool:
        """Returns flag to ROIs from other layouts which do not
        exist in source layout
        """
        return self.chkRemove.isChecked()


class SortFilterProxyModel(QSortFilterProxyModel):

    def lessThan(self, left_index, right_index):

        left_var = left_index.data(Qt.EditRole)
        right_var = right_index.data(Qt.EditRole)

        try:
            return float(left_var) < float(right_var)
        except (ValueError, TypeError):
            pass
        return left_var < right_var


class UpdateProjectDetailsPopup(QWidget):

    def __init__(self, parent, sourceData):
        super().__init__(parent)
        self._projectData = {}
        self.sourceData = sourceData

        self.data = {}  # Cache the latest details
        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(256)
        self.setMinimumHeight(196)

        widget = QWidget()
        widget.setObjectName("popup")
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())
        grid.setObjectName("popup")

        self.layout().addWidget(widget)

        lblName = QLabel("Engineer drafter required:", self)
        lblName.setObjectName("popup")
        grid.layout().addWidget(lblName, 1, 0, 1, 1)
        self.engineerDrafterValue = QLineEdit("", self)
        self.engineerDrafterValue.setObjectName("engineerDrafterValue")
        self.engineerDrafterValue.textChanged.connect(self.onTextChanged)
        grid.layout().addWidget(self.engineerDrafterValue, 2, 0, 1, 1)

        widget.layout().addWidget(grid)

        self.pbUpdate = QPushButton("Update")
        self.pbUpdate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbUpdate.setEnabled(False)
        widget.layout().addWidget(self.pbUpdate)

        self.pbCancel = QPushButton("Cancel")
        self.pbCancel.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbCancel.clicked.connect(self.close)
        widget.layout().addWidget(self.pbCancel)

        self.pbUpdate.clicked.connect(self.onUpdateDetails)

    def setSourceData(self, data: dict):
        self.engineerDrafterValue.setText(data.get("documentVendor"))
        if not data.get("documentVendor"):
            self.show()
        self.sourceData = data

    def updateProjectData(self):
        """ For now, just update drafter value """
        if not self.engineerDrafterValue.text():
            return
            # self.setErrorMessage("Engineer drafter required for extraction")

    def setErrorMessage(self, text):
        self.lblError.setText(text)

    def onUpdateDetails(self):
        engineerDrafter = self.engineerDrafterValue.text()
        if not engineerDrafter:
            self.pbUpdate.setEnabled(False)
            return
        self.sourceData["documentVendor"] = engineerDrafter
        pub.sendMessage("project-source-update", data=self.sourceData)
        QMessageBox.information(self, "Engineer drafter updated", "Engineer drafter updated")
        self.close()

    def onTextChanged(self, text):
        self.pbUpdate.setEnabled(True if text else False)

    def focus(self):
        self.engineerDrafterValue.setFocus()

class PdfScene(QGraphicsScene):

    sgnRemoveItem = Signal(object)

    def __init__(self):
        super().__init__()
        self.sgnRemoveItem.connect(self.onRemoveItem)

    @property
    def pageWidth(self):
        return self.views()[0].pageWidth

    @property
    def pageHeight(self):
        return self.views()[0].pageHeight

    @property
    def dpi(self):
        return self.views()[0].dpi

    def onRemoveItem(self, item):
        self.removeItem(item)


class CustomComboBox(QComboBox):

    contextMenuRequested = Signal(object)

    def contextMenuEvent(self, event):
        self.contextMenuRequested.emit(event)

class RoiItem(QGraphicsItemGroup, QObject):
    """
    Used for both a standard rectangle region or, optionally,
    a table with adjustable columns
    """

    DEFAULT_COLOR = QColor("#0080ff")
    SELECTED_COLOR = QColor("#F7A71D")
    HOVERED_COLOR = QColor("red")

    sgnAddItemToGroup = Signal(object)
    sgnRemoveItemToGroup = Signal(object)
    sgnRemoved = Signal()
    sgnUpdated = Signal()
    sgnSelected = Signal()
    sgnResizeBounds = Signal(object)

    requestMotionListener = Signal(object)
    cursorUpdated = Signal(object)
    mouseMoved = Signal(object, object)
    boundsChanged = Signal() # Boundaries modified
    updated = Signal(object, dict) # ROI Details changed (self, old state)


    minColumnWidth = 16

    hovered = Signal(bool)
    def __init__(self,
                 start: QPoint,
                 end: QPoint,
                 scene: PdfScene) -> None:
        QObject.__init__(self)
        super().__init__()
        self.sgnAddItemToGroup.connect(self.onAddItemToGroup)

        self.name: str = None  # Internal ID
        self.displayName: str = None  # Mapped name or user-defined alias
        self.lockedColumnRatios = None
        self.columnNames: list = []
        self.isTable = False
        self.headersSelected: bool = True

        self._selected: bool = False
        self._hovered: bool = False
        self._dragging: bool = False
        self._dragSide = None  # Component of ROI which can be dragged or resized
        self._dragStart = None

        self.pen = QPen()
        self.pen.setColor(self.DEFAULT_COLOR)
        self.pen.setWidth(2.5)
        self.pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        self.pen.setStyle(Qt.PenStyle.DashLine)
        self.pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)

        scene.addItem(self)
        self.bounds: QGraphicsRectItem = None
        path = QPainterPath()
        path.addRect(QRectF(start, end))
        self.bounds = self.scene().addRect(QRectF(start, end))
        self.addToGroup(self.bounds)
        self.bounds.setPen(self.pen)

        self.sgnResizeBounds.connect(self.onResizeBoundsRect)
        self.setZValue(1)

        logger.info(f"RoiItem created with start: {start}, end: {end}")

    def scene(self) -> PdfScene:
        return super().scene()

    @property
    def hovered(self) -> bool:
        try:
            return self._hovered
        except Exception as e:
            logger.error(f"Error on hovered: {e}", exc_info=True)

    @hovered.setter
    def hovered(self, state: bool) -> bool:
        try:
            self._hovered = state
            self.update()
        except Exception as e:
            logger.error(f"Error on hovered.setter: {e}", exc_info=True)

    @property
    def selected(self) -> bool:
        try:
            return self._selected
        except Exception as e:
            logger.error(f"Error on selected: {e}", exc_info=True)

    @selected.setter
    def selected(self, state: bool):
        try:
            # Updates all the other roi's in the scene
            for s in self.scene().items():
                if isinstance(s, RoiItem) and s.scene() == self.scene():
                    s._selected = state and (s == self)
                    if s.selected:
                        s.sgnSelected.emit()
                    s.update()
        except Exception as e:
            logger.error(f"Error setting selected state to {state}: {e}", exc_info=True)

    @property
    def columnCount(self) -> int:
        try:
            n = 0
            for item in self.childItems():
                if isinstance(item, QGraphicsLineItem):
                    n += 1
            return n
        except Exception as e:
            logger.error(f"Error on columnCount: {e}", exc_info=True)

    @property
    def columnRatios(self) -> list:
        try:
            ratios = []
            bounds = self.boundingRect()
            bounds = self.bounds.boundingRect()
            # If rect is inverted, we need to ensure ratio
            # is relative to onscreen topLeft
            x = min(bounds.x(), bounds.x() + bounds.width())
            # leftX = min(bounds.x(), bounds.x() + bounds.width())
            for c in self.getColumnLines():
                r = (c.boundingRect().x() - x) / bounds.width()
                r = round(r, 2)
                ratios.append(r)
            return sorted(ratios)
        except Exception as e:
            logger.error(f"Error on columnRatios: {e}", exc_info=True)

    def select(self):
        try:
            self.selected = True
        except Exception as e:
            logger.error(f"Error on select: {e}", exc_info=True)

    def deselect(self):
        try:
            self.selected = False
        except Exception as e:
            logger.error(f"Error on deselect: {e}", exc_info=True)

    def getColumnLines(self) -> list:
        try:
            columns = []
            for item in self.childItems():
                if isinstance(item, QGraphicsLineItem):
                    columns.append(item)
            columns = sorted(columns, key=lambda x: x.boundingRect().x())
            return columns
        except Exception as e:
            logger.error(f"Error on on getColumnLines: {e}", exc_info=True)

    def clearColumnLines(self):
        """Remove all column lines"""
        try:
            for c in self.getColumnLines():
                self.scene().sgnRemoveItem.emit(c)
        except Exception as e:
            logger.error(f"Error on clearColumnLines: {e}", exc_info=True)

    def addColumnLine(self, ratio: float):
        try:
            ratio = min(1, ratio)
            ratio = max(0, ratio)
            boundsRect = self.bounds.rect()
            # Collapse workaround, add buffer to x value
            extra = 2
            x = abs(boundsRect.width() + extra) * ratio
            boundsLeft = min(boundsRect.left(), boundsRect.right())
            top = QPoint(boundsLeft + x, boundsRect.top())
            bottom = QPoint(boundsLeft + x, boundsRect.bottom())
            newLine = self.scene().addLine(QLineF(top, bottom))
            newLine.setZValue(2)
            newLine.setPen(self.pen)
            # self.addToGroup(newLine)
            self.sgnAddItemToGroup.emit(newLine)
            # Hide the last line as we want to fix x it right side of rect
            if ratio == 1:
                newLine.setOpacity(0)
            return newLine
        except Exception as e:
            logger.error(f"Error adding column line with ratio {ratio}: {e}", exc_info=True)

    def setColumnLines(self, ratios: list):
        try:
            """Clear and set new column lines"""
            self.clearColumnLines()
            if ratios:
                ratios[-1] = 1
            for r in ratios:
                self.addColumnLine(r)
        except Exception as e:
            logger.error(f"Error setting column: {e}", exc_info=True)

    def boundingRect(self):
        """
        https://stackoverflow.com/a/67473837
        Important - ensures the rect is updated
        """
        try:
            return self.childrenBoundingRect()
        except Exception as e:
            logger.error(f"Error returning childerenBoundingRect: {e}", exc_info=True)

    def resizeBoundsRect(self, rect: QRectF):
        self.sgnResizeBounds.emit(rect)

    def onResizeBoundsRect(self, rect: QRectF):
        """Resized bounds, need to fit columns to the same proportion"""
        try:
            ratios = self.lockedColumnRatios # self.columnRatios)
            if not ratios:
                ratios = [1]
            x, y, w, h = rect.x(), rect.y(), rect.width(), rect.height()
            rect.setX(x)
            rect.setY(y)
            rect.setWidth(w)
            rect.setHeight(h)
            self.prepareGeometryChange()
            self.boundingRect().setRect(x, y, w, h)
            self.bounds.setRect(rect)
            self.setColumnLines(ratios)
        except Exception as e:
            logger.error(f"Error on resizeBoundsRect with {rect}: {e}", exc_info=True)

    def mouseMoveEvent(self, event: QMouseEvent):
        """Continue tracking movement while the mouse is pressed"""
        self.mouseMoved.emit(self, event)

    def updateMousePos(self, pos: QPoint, buttons: Qt.MouseButton):

        if not self.scene():
            raise Exception("Removed item should not be handling mouse movement")

        if self._dragging:
            pageWidth = self.scene().pageWidth
            pageHeight = self.scene().pageHeight
            boundsRect = self.bounds.rect()
            if not self.lockedColumnRatios:
                self.lockedColumnRatios = self.columnRatios
            if isinstance(self._dragSide, QGraphicsLineItem): # Column line dragging
                try:
                    # Columns can only move horizontally
                    line: QGraphicsLineItem = self._dragSide
                    lines = self.getColumnLines()
                    index = lines.index(line)
                    if pos.x() < line.boundingRect().x():
                        if index > 0:
                            leftLimit = lines[index-1].boundingRect().x() + self.minColumnWidth//2
                        else:
                            leftLimit = boundsRect.left() + self.minColumnWidth//2
                        x = max(leftLimit, pos.x())
                    elif pos.x() > line.boundingRect().x():
                        if index < len(lines) - 1:
                            rightLimit = lines[index+1].boundingRect().x() - self.minColumnWidth//2
                        else:
                            rightLimit = boundsRect.right() - self.minColumnWidth//2
                        x = min(rightLimit, pos.x())
                    else:
                        # x is unchanged
                        return
                    top = QPoint(x, boundsRect.top())
                    bottom = QPoint(x, boundsRect.bottom())
                    newLine = self.scene().addLine(QLineF(top, bottom))
                    newLine.setPen(line.pen())
                    newLine.setZValue(2)
                    # self.addToGroup(newLine)
                    self.sgnAddItemToGroup.emit(newLine)
                    self.scene().sgnRemoveItem.emit(line)
                    self._dragSide = newLine
                    # self.boundsChanged.emit()
                    return
                except Exception as e:
                    logger.error(f"Error handling column line dragging: {e}", exc_info=True)
            elif self._dragSide == "move":
                dx = pos.x() - self._dragStart.x()
                dy = pos.y() - self._dragStart.y()
                self._dragStart = pos
                # Limit movement to the bounds of the PDF page
                newTopLeft = boundsRect.topLeft() + QPoint(dx, dy)
                newTopLeft.setX(max(0, newTopLeft.x()))
                newTopLeft.setY(max(0, newTopLeft.y()))
                if newTopLeft.x() + boundsRect.width() > pageWidth:
                    newTopLeft.setX(pageWidth - boundsRect.width())
                if newTopLeft.y() + boundsRect.height() > pageHeight:
                    newTopLeft.setY(pageHeight - boundsRect.height())
                boundsRect.moveTo(newTopLeft)
            elif self._dragSide == "right":
                boundsRect.setRight(min(max(0, pos.x()), pageWidth))
            elif self._dragSide == "left":
                boundsRect.setLeft(max(0, min(pageWidth, pos.x())))
            elif self._dragSide == "top":
                boundsRect.setTop(max(0, min(pageHeight, pos.y())))
            elif self._dragSide == "bottom":
                boundsRect.setBottom(min(pageHeight, max(0, pos.y())))
            else:
                pt = QPoint(
                    max(0, min(pageWidth, pos.x())),
                    max(0, min(pageHeight, pos.y())),
                )
                if self._dragSide == "topLeft":
                    boundsRect.setTopLeft(pt)
                elif self._dragSide == "topRight":
                    boundsRect.setTopRight(pt)
                elif self._dragSide == "bottomLeft":
                    boundsRect.setBottomLeft(pt)
                elif self._dragSide == "bottomRight":
                    boundsRect.setBottomRight(pt)

            # Bounds have changed
            if boundsRect != self.boundingRect():
                self.boundsChanged.emit()
            self.resizeBoundsRect(boundsRect)
            return

        self.lockedColumnRatios = None

        def inCornerZone(corner: QPoint) -> bool:
            """A single pixel for corner drag detection is too small
            So check in point inside zone
            """
            corner = corner
            margin = 5 * (self.scene().dpi // 72)
            if not (pos.x() >= corner.x() - margin and pos.x() <= corner.x() + margin):
                return False
            if not (pos.y() >= corner.y() - margin and pos.y() <= corner.y() + margin):
                return False
            return True

        topLeft = self.bounds.boundingRect().topLeft().toPoint()
        topRight = self.bounds.boundingRect().topRight().toPoint()
        bottomLeft = self.bounds.boundingRect().bottomLeft().toPoint()
        bottomRight = self.bounds.boundingRect().bottomRight().toPoint()

        shape = Qt.CursorShape.ArrowCursor
        self._dragSide = None

        extra = 3 * (self.scene().dpi // 72) # DPI-aware buffer

        # Prioritize dragging columns before outside bounds
        try:
            for item in self.getColumnLines()[:-1]:
                columnX = int(item.boundingRect().x())
                if pos.x() in range(columnX - extra, columnX + extra):
                    shape = Qt.CursorShape.SizeHorCursor
                    self._dragSide = item
                    break
        except Exception as e:
            logger.error(f"Error during column line position check: {e}", exc_info=True)

        # Check outside bounds and corners if no column dragging
        if not self._dragSide:
            try:
                if inCornerZone(topLeft):
                    shape = Qt.CursorShape.SizeFDiagCursor
                    self._dragSide = "topLeft"
                elif inCornerZone(bottomRight):
                    self._dragSide = "bottomRight"
                    shape = Qt.CursorShape.SizeFDiagCursor
                elif inCornerZone(bottomLeft):
                    self._dragSide = "bottomLeft"
                    shape = Qt.CursorShape.SizeBDiagCursor
                elif inCornerZone(topRight):
                    self._dragSide = "topRight"
                    shape = Qt.CursorShape.SizeBDiagCursor
                elif pos.y() in range(topLeft.y()-extra, topLeft.y()+extra):
                    if pos.x() > bottomLeft.x() and pos.x() < bottomRight.x():
                        self._dragSide = "top"
                        shape = Qt.CursorShape.SizeVerCursor
                elif pos.y() in range(bottomLeft.y()-extra, bottomLeft.y()+extra):
                    if pos.x() > bottomLeft.x() and pos.x() < bottomRight.x():
                        self._dragSide = "bottom"
                        shape = Qt.CursorShape.SizeVerCursor
                elif pos.x() in range(topLeft.x()-extra, topLeft.x()+extra):
                    if pos.y() > topLeft.y() and pos.y() < bottomLeft.y():
                        self._dragSide = "left"
                        shape = Qt.CursorShape.SizeHorCursor
                elif pos.x() in range(topRight.x()-extra, topRight.x()+extra):
                    if pos.y() > topLeft.y() and pos.y() < bottomLeft.y():
                        self._dragSide = "right"
                        shape = Qt.CursorShape.SizeHorCursor
                elif self.bounds.boundingRect().contains(pos):
                    self._dragSide = "move"
                    shape = Qt.CursorShape.DragMoveCursor
            except Exception as e:
                logger.error(f"Error updating cursor or drag side logic: {e}", exc_info=True)

        if self._dragSide:
            self._dragStart = pos

        self.cursorUpdated.emit(QCursor(shape))

    def update(self):
        brush = QBrush()
        if self.selected:
            self.pen.setColor(self.SELECTED_COLOR)
            self.setOpacity(1)
            fill = self.pen.color()
            fill.setAlphaF(0.2)
            brush.setColor(fill)
            self.requestMotionListener.emit(self)
        else:
            self.setOpacity(0.6)
            self.pen.setColor(self.DEFAULT_COLOR)

        if self.hovered:
            fill = self.pen.color()
            fill.setAlphaF(0.2)
            brush.setColor(fill)
            brush.setStyle(Qt.BrushStyle.SolidPattern)

        for c in self.getColumnLines():
            c.setPen(self.pen)
        self.bounds.setPen(self.pen)
        self.bounds.setBrush(brush)

    def setTableMode(self, isTable: bool, columns: int = 1):
        try:
            if not self.isTable and isTable:
                self.isTable = True
                # self.columns = [None] * columns
            else:
                self.isTable = False
                # self.columns = [1]
                self.columnNames = []
            self.resetColumns()
        except Exception as e:
            logger.error(f"Error setting table mode: {e}", exc_info=True)

    def resetColumns(self):
        """ Clears columns and creates equally spaced columns """
        try:
            columnLines = self.getColumnLines()
            columnCount = len(columnLines)
            for line in columnLines:
                if isinstance(line, QGraphicsLineItem):
                    self.scene().removeItem(line)
            if columnCount == 0:
                return
            try:
                w = 1 / columnCount # Equal width
            except ZeroDivisionError:
                w = 0  # Handle division by zero # Added
                logger.warning(f"Error division by 0. 'w' set to 0")

            for n in range(columnCount):
                ratio = w * (n + 1)
                self.addColumnLine(ratio)
        except Exception as e:
            logger.error(f"Error resetting columns: {e}", exc_info=True)

    def addColumn(self, fieldId: str, reset=True):
        """Adds a new column to the ROI

        Args:
            fieldId (str): Field name of the column
            reset (bool, optional): Resets the column line h-spacing. Defaults to True.
        """
        # Calculate equal position to place line
        self.columnNames.append(fieldId)
        if reset:
            self.addColumnLine(0)
            self.resetColumns()
        else:
            # Move the far right column to halfway between
            # And add a new column to end
            ratios = self.columnRatios
            if ratios:
                try:
                    secondLast = ratios[-2]
                    mid = 1 - ((1 - secondLast) / 2)
                    ratios[-1] = mid
                except Exception as e:
                    ratios[-1] = 0.5
            else:
                pass
            ratios.append(1)
            self.setColumnLines(ratios)

    def removeColumn(self, index=-1):
        del self.columnNames[index]
        try:
            column = self.getColumnLines()[index]
            self.scene().removeItem(column)
            self.removeFromGroup(column)
        except Exception as e:
            logger.error(f"Error setting index({index}): {e}", exc_info=True)
        # Ensure last line moves to right side
        self.setColumnLines(self.columnRatios)

        # self.resetColumns()

    def correctCorners(self):
        # If a QRect corner goes beyond the x,y of another the corner, it is still
        # determined to be the same corner
        # For example, topLeft having a greater x than topRight, will still
        # remain as topLeft.
        # So determine the correct corners and swap them
        try:
            boundsRect = self.bounds.rect()
            top = min(boundsRect.bottom(), boundsRect.top())
            bottom = max(boundsRect.bottom(), boundsRect.top())
            left = min(boundsRect.left(), boundsRect.right())
            right = max(boundsRect.left(), boundsRect.right())
            self.prepareGeometryChange()
            boundsRect.setRect(left, top, right-left, bottom-top)
            self.bounds.setRect(boundsRect)
        except Exception as e:
            logger.error(f"Error swapping coordinates: {e}", exc_info=True)

    def updateColumn(self, column, name):
        try:
            self.columnNames[column] = name
        except Exception as e:
            logger.error(f"Error updating column name: {e}", exc_info=True)

    def onAddItemToGroup(self, item):
        """ Signal handler """
        try:
            self.addToGroup(item)
        except Exception as e:
            logger.error(f"Error adding item to group", exc_info=True)

    def getSaveState(self) -> dict:
        state = {
            "name": self.name,
            "columnNames": self.columnNames,
            "columnRatios": self.columnRatios,
            "isTable": self.isTable,
            "relativeX0": 0,
            "relativeY0": 0,
            "relativeX1": 0,
            "relativeY1": 0,
        }
        if self.isTable:
            state["headersSelected"] = self.headersSelected
        coords = self.getRoiRelativeCoords()
        state.update(coords)
        return state

    def getRoiRelativeCoords(self) -> dict:
        """Relative to the PDF page"""
        pageWidth, pageHeight = self.scene().pageWidth, self.scene().pageHeight
        coords = {}
        rect = self.boundingRect() # Important - this was previous bounding rect
        rect = self.bounds.rect()
        x0, y0 = rect.topLeft().x() / pageWidth, rect.topLeft().y() / pageHeight
        x1, y1 = rect.bottomRight().x() / pageWidth, rect.bottomRight().y() / pageHeight
        coords["relativeX0"] = x0
        coords["relativeY0"] = y0
        coords["relativeX1"] = x1
        coords["relativeY1"] = y1
        return coords

    def cleanup(self):
        self.prepareGeometryChange()  # Helps to properly refresh and clear leftover artifacts

    def updateDetails(self,
                      name: str,
                      displayName: str,
                      isTable: bool,
                      headersSelected: bool,
                      columnNames: list[str]):
        oldState = self.getSaveState()
        self.name = name
        self.displayName = displayName
        self.isTable = isTable
        self.headersSelected = headersSelected
        self.columnNames = columnNames
        self.updated.emit(self, oldState)


class FieldFilter(QSortFilterProxyModel):

    def __init__(self, parent):
        super().__init__(parent)
        self.setDynamicSortFilter(True)
        self.setFilterRole(Qt.ItemDataRole.UserRole)
        self._terms = set()
        self._tags: dict = {}

    @property
    def tags(self) -> dict:
        return self._tags

    @tags.setter
    def tags(self, tags: dict):
        self._tags = tags
        self.invalidate()

    def filterAcceptsRow(self, sourceRow, sourceParent) -> bool:
        """ Apply our custom filter rules to match not only DisplayRole but tags, etc. """

        def escape_regex(text):
            if text is None:
                return ""
            # Escape backslashes and other special regex characters
            import re
            return re.escape(str(text))

        column = 0
        index = self.sourceModel().index(sourceRow, column, sourceParent)
        display = index.data(Qt.ItemDataRole.DisplayRole)
        # Precaution
        if not display:
            return False
        if (self.filterRegularExpression().match(escape_regex(display)).hasMatch()):
            return True
        data = index.data(self.filterRole())
        if not data:
            return
        fieldId = data.get("id")
        if fieldId and (self.filterRegularExpression().match(escape_regex(fieldId)).hasMatch()):
            return True

        # Check Tags
        try:
            for tag in self.tags.get(fieldId, []):
                if not self.filterRegularExpression().match(escape_regex(tag)).hasMatch():
                    continue
                return True
        except Exception as e:
            logger.info(f"Error checking tags {e}")

        return False


class RoiLayoutFilter(QSortFilterProxyModel):

    def __init__(self, parent):
        super().__init__(parent)
        self.setDynamicSortFilter(True)
        self.setFilterRole(Qt.ItemDataRole.UserRole)
        self._terms = set()
        self._tags: dict = {}

    @property
    def tags(self) -> dict:
        return self._tags

    @tags.setter
    def tags(self, tags: dict):
        self._tags = tags
        self.invalidate()

    def filterAcceptsRow(self, sourceRow, sourceParent) -> bool:
        """ Apply our custom filter rules to match not only DisplayRole but tags, etc. """

        def escape_regex(text):
            if text is None:
                return ""
            # Escape backslashes and other special regex characters
            import re
            return re.escape(str(text))

        column = 0
        index = self.sourceModel().index(sourceRow, column, sourceParent)
        display = index.data(Qt.ItemDataRole.DisplayRole)
        # Precaution
        if not display:
            return False
        if (self.filterRegularExpression().match(escape_regex(display)).hasMatch()):
            return True
        data = index.data(self.filterRole())
        if not data:
            return
        roiName = data.get("name")
        if roiName is None: # None-layout, we just need a regex match
            return self.filterRegularExpression().match(escape_regex(roiName)).hasMatch()
        if roiName and (self.filterRegularExpression().match(escape_regex(roiName)).hasMatch()):
            return True

        # Did not return a match, but also check tags
        tags = data.get("rois", {}).get("tags", [])
        documentVendor = data.get("rois", {}).get("documentVendor")
        tags.append(documentVendor)
        # Check Tags
        try:
            for tag in tags:
                if not self.filterRegularExpression().match(escape_regex(tag)).hasMatch():
                    continue
                return True
        except Exception as e:
            logger.info(f"Error checking tags {e}")

        return False

class CustomFilterComboBox(QComboBox):

    def __init__(self, parent, customFilter: QSortFilterProxyModel):
        super().__init__(parent)
        self.setEditable(True)
        self.setLineEdit(QLineEdit())
        self.lineEdit().installEventFilter(self)
        self.setInsertPolicy(QComboBox.NoInsert)

        # Add a filter model to filter matching items
        self.filterModel: QSortFilterProxyModel = customFilter(self)
        self.filterModel.setFilterCaseSensitivity(Qt.CaseInsensitive)
        self.filterModel.setFilterRole(Qt.ItemDataRole.UserRole)
        # self.currentTextChanged.connect(self.onCurrentTextChanged)
        self.setModel(self.model())
        # Add a completer, which uses the filter model
        self.setCompleter(QCompleter(self.filterModel, self))
        self.completer().setModel(self.filterModel)
        self.completer().setCaseSensitivity(Qt.CaseInsensitive)
        self.completer().popup().setStyleSheet("completerPopup")
        # Important - shows our custom filtered results
        self.completer().setCompletionMode(QCompleter.CompletionMode.UnfilteredPopupCompletion)

    def focusNextChild(self) -> bool:
        return False

    def focusOutEvent(self, e):
        super().focusOutEvent(e)
        self.lineEdit().deselect()

    def keyPressEvent(self, e: QKeyEvent) -> None:
        """
        Important to handle here and not `currentTextChanged`. We want the
        user to select the dropdown choices without triggering the re-filter
        """
        if e.key() in [Qt.Key.Key_Left, Qt.Key.Key_Right, Qt.Key.Key_Down, Qt.Key.Key_Up]:
            return
        ret = super().keyPressEvent(e)
        self.filterModel.setFilterRegularExpression(re.escape(self.currentText()))
        # Invalidate the filter to trigger a re-filter
        self.filterModel.invalidate()
        return ret

    def eventFilter(self, source, event):
        """ Select all if in not text is currently selected """
        if event.type() == QEvent.MouseButtonPress:
            if event.button() == Qt.LeftButton:
                if self.lineEdit().hasSelectedText():
                    return super().eventFilter(source, event)
                self.lineEdit().selectAll()
                return True

        return super().eventFilter(source, event)

    def setModel(self, model) -> None:
        super().setModel(model)
        self.filterModel.setSourceModel(model)
        return super().setModel(model)


class EditRoiDialog(QDialog):
    """ Dialog handler for ROI creation and editing """

    sgnFieldMapUpdated = Signal()

    def __init__(self, parent, roi: RoiItem, excludeNames: list = []):
        super().__init__(parent=parent)
        self.excludeNames: list = excludeNames
        self.fieldMap: dict = {}
        self.roi: RoiItem = roi
        try:
            self.roi.correctCorners()
        except Exception as e:
            logger.error(f"Error correcting corners in EditRoiDialog: {e}", exc_info=True)

        self.setWindowTitle("Edit Selection")
        self.setLayout(QVBoxLayout())

        self.cboxInputName: CustomFilterComboBox = CustomFilterComboBox(self, FieldFilter)
        self.cboxInputName.setEditable(True)
        self.cboxInputName.setMinimumHeight(36)
        self.layout().addWidget(self.cboxInputName)

        debug = False

        self.chkTable = QCheckBox("Table Selection?")
        self.chkTable.clicked.connect(self.onTableSelectionChecked)
        self.chkTable.setEnabled(False) # view only for debug
        self.layout().addWidget(self.chkTable)

        if not debug:
            self.chkTable.hide()

        self.chkHeadersSelected = QCheckBox("Headers Selected")
        # self.chkHeadersSelected.clicked.connect(self.onTableSelectionChecked)
        self.layout().addWidget(self.chkHeadersSelected)

        self.tableHbox = hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        self.cboxAddColumn: QComboBox = CustomFilterComboBox(self, FieldFilter)
        self.cboxAddColumn.clear()
        self.cboxAddColumn.setEditable(True)
        self.cboxAddColumn.setMinimumHeight(36)
        hbox.layout().addWidget(self.cboxAddColumn)
        self.pbAddColumn = QPushButton("", default=False, autoDefault=False)
        self.pbAddColumn.setFixedSize(QSize(32,32))
        self.pbAddColumn.setIcon(get_resource_qicon("plus.svg"))
        applyDropShadowEffect(self.pbAddColumn, offset=(0, 1))
        self.pbAddColumn.clicked.connect(self.onAddColumn)
        hbox.layout().addWidget(self.pbAddColumn)
        self.pbRemoveColumn = QPushButton("", default=False, autoDefault=False)
        applyDropShadowEffect(self.pbRemoveColumn, offset=(0, 1))
        self.pbRemoveColumn.setFixedSize(QSize(32,32))
        self.pbRemoveColumn.setIcon(get_resource_qicon("minus.svg"))
        self.pbRemoveColumn.clicked.connect(self.onRemoveColumn)
        hbox.layout().addWidget(self.pbRemoveColumn)
        self.pbUpdateColumn = QPushButton("Update", default=False, autoDefault=False)
        applyDropShadowEffect(self.pbUpdateColumn, offset=(0, 1))
        self.pbUpdateColumn.setFixedSize(QSize(64,32))
        self.pbUpdateColumn.clicked.connect(self.onUpdateColumn)
        hbox.layout().addWidget(self.pbUpdateColumn)
        self.layout().addWidget(hbox)

        self.roiTable = QListWidget(self)
        self.roiTable.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.roiTable.setDefaultDropAction(Qt.DropAction.MoveAction)
        self.roiTable.itemClicked.connect(self.updateUi)
        self.layout().addWidget(self.roiTable)

        btnBox = QWidget()
        btnBox.setMinimumHeight(32)
        btnBox.setLayout(QHBoxLayout())
        self.pbCancel = QPushButton("Cancel", default=False, autoDefault=False)
        self.pbCancel.setMinimumHeight(32)
        self.pbCancel.clicked.connect(self.onCancel)
        applyDropShadowEffect(self.pbCancel, offset=(0, 1))
        btnBox.layout().addWidget(self.pbCancel)
        self.pbSave = QPushButton("Save", default=False, autoDefault=False)
        applyDropShadowEffect(self.pbSave, offset=(0, 1))
        self.pbSave.setMinimumHeight(32)
        self.pbSave.clicked.connect(self.onSave)
        btnBox.layout().addWidget(self.pbSave)
        self.layout().addWidget(btnBox)

        # Restore UI based on ROI
        logger.info(f"name: {self.roi.name}, table: {self.roi.isTable}, ratios:{self.roi.columnRatios}")
        self.backup = {}  # Restore default state if cancelled
        self.backup["name"] = roi.name
        self.backup["rect"] = roi.boundingRect()
        self.backup["isTable"] = roi.isTable
        self.backup["headersSelected"] = roi.headersSelected
        self.cboxInputName.setCurrentIndex(0)
        if not roi.name:
            self.chkTable.setChecked(True)
        self.chkHeadersSelected.setChecked(roi.headersSelected)

        self.show()
        self.setFocus()
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)

        shortcut = QShortcut(QKeySequence(Qt.Key_Tab), self)
        shortcut.activated.connect(self.onTabPress)

        self.sgnFieldMapUpdated.connect(self.onFieldMapUpdated)
        self.getFieldMap()

    def onTabPress(self):
        """ Tab to autocomplete and activate first entry """

        def selectFirstEntry(cbox: QComboBox):
            completer = cbox.completer()
            index = cbox.findText(cbox.currentText())
            if index == -1:
                view = completer.popup()
                index = completer.completionModel().index(0, 0, QModelIndex())
                view.setCurrentIndex(index)
            index = cbox.findText(cbox.currentText())
            cbox.setCurrentIndex(index)
            cbox.lineEdit().selectAll()
            completer.popup().hide()

            if cbox == self.cboxInputName:
                self.refreshColumnFieldsList()
                self.onCboxInputNameChanged(-1)

        if self.cboxInputName.hasFocus():
            selectFirstEntry(self.cboxInputName)
        elif self.cboxAddColumn.hasFocus():
            selectFirstEntry(self.cboxAddColumn)
        else:
            self.focusNextChild()

    def onCancel(self):
        try:
            """ Restore previous state """
            # print(self.backup)
            self.roi.isTable = self.backup["isTable"]
            self.roi.headersSelected = self.backup["headersSelected"]
            if self.roi.isTable:
                self.roi.columnNames = self.backup["columnNames"]
                self.roi.setColumnLines(self.backup["columnRatios"])
            self.reject()
        except Exception as e:
            logger.error(f"Error Canceling/Restoring state: {e}", exc_info=True)

    def onSave(self):
        try:
            # Persist changes before closing
            _id = self.cboxInputName.itemData(self.cboxInputName.currentIndex(), Qt.ItemDataRole.UserRole)["id"]
            name = _id
            displayName = self.cboxInputName.currentText()
            isTable = self.chkTable.isChecked()
            headersSelected = self.chkHeadersSelected.isChecked()
            columnNames = []
            if isTable:
                for n in range(self.roiTable.count()):
                    fieldId = self.roiTable.item(n).data(Qt.ItemDataRole.UserRole)["id"]
                    columnNames.append(fieldId)
            self.roi.updateDetails(
                name = name,
                displayName = displayName,
                isTable = isTable,
                headersSelected = headersSelected,
                columnNames = columnNames,
            )
            logger.info(f"Saved ROI: {self.roi.name}, table: {self.roi.isTable}")
            self.accept()
        except Exception as e:
            logger.error(f"Error saving ROI: {e}", exc_info=True)

    def checkSaveable(self):
        try:
            """ Update state if ROI configuration can be saved """
            # Only allow user to save table if at least one column defined
            if self.chkTable.isChecked():
                self.pbSave.setEnabled(self.roiTable.count() > 0)
            else:
                self.pbSave.setEnabled(True)
        except Exception as e:
            logger.error(f"Error updating state: {e}", exc_info=True)

    def onTableSelectionChecked(self, checked):
        try:
            # Update ROI
            self.roi.setTableMode(checked, columns=self.roiTable.count())
            # self.updateUi()
        except Exception as e:
            logger.error(f"Error onTableSelectionChecked: {e}", exc_info=True)

    def onAddColumn(self):
        try:
            fieldData = self.cboxAddColumn.itemData(self.cboxAddColumn.currentIndex(), Qt.ItemDataRole.UserRole)
            fieldId = fieldData["id"]
            displayName = fieldData["display"]
            item = QListWidgetItem(displayName)
            item.setData(Qt.ItemDataRole.UserRole, fieldData)
            self.roiTable.addItem(item)
            # For new ROI, add new columns in equal spacing
            # For existing ROI, try to keep same ratio for previous columns
            self.roi.addColumn(fieldId, reset=self.roi.name is None)
            self.refreshColumnFieldsList()
            self.updateUi()
        except Exception as e:
            # No item to remove
            logger.error(f"Error adding column in EditRoiDialog: {e}", exc_info=True)

    def onRemoveColumn(self):
        try:
            index = self.roiTable.selectedIndexes()[0]
            row = index.row()
            self.roiTable.model().removeRow(row)
            self.roi.removeColumn(row)
        except Exception as e:
            # No item to remove
            logger.error(f"Error removing column in EditRoiDialog: {e}", exc_info=True)
        self.refreshColumnFieldsList()
        self.updateUi()

    def onUpdateColumn(self):
        """ Updates the column field to the currently selected column choice """
        try:
            index = self.roiTable.selectedIndexes()[0]
            name = self.cboxAddColumn.currentText()
            newFieldData = self.cboxAddColumn.itemData(self.cboxAddColumn.currentIndex(),
                                                       Qt.ItemDataRole.UserRole)
            currFieldData = index.data(Qt.ItemDataRole.UserRole)
            if newFieldData["id"] == currFieldData["id"]:
                return
            # Update the table item data and the ROI
            item = self.roiTable.item(index.row())
            item.setData(Qt.ItemDataRole.DisplayRole, newFieldData["display"])
            item.setData(Qt.ItemDataRole.UserRole, newFieldData)
            self.roi.updateColumn(index.row(), newFieldData["id"])
        except Exception as e:
            # No item to remove
            logger.error(f"Error updating column in EditRoiDialog: {e}", exc_info=True)
        self.refreshColumnFieldsList()
        self.updateUi()

    def updateUi(self):
        try:
            tableChecked = self.chkTable.isChecked()
            self.chkHeadersSelected.setVisible(tableChecked)
            self.roiTable.setEnabled(tableChecked)
            self.cboxAddColumn.setEnabled(tableChecked)
            self.pbAddColumn.setEnabled(tableChecked)
            selected = True if self.roiTable.selectedIndexes() else False
            self.pbUpdateColumn.setEnabled(selected)
            self.pbRemoveColumn.setEnabled(selected)
            self.tableHbox.setVisible(tableChecked)
            self.roiTable.setVisible(tableChecked)
            self.checkSaveable()

            self.cboxAddColumn.setEnabled(self.cboxAddColumn.count())
            self.pbAddColumn.setEnabled(self.cboxAddColumn.count())
            self.pbUpdateColumn.setEnabled(self.cboxAddColumn.count())

            if tableChecked:
                self.setFixedSize(400, 400)
            else:
                self.setFixedSize(400, 150)
            self.layout()
        except Exception as e:
            logger.error(f"Error updating UI in EditRoiDialog: {e}", exc_info=True)

    def getAddedColumnsNamesList(self) -> list:
        """ Return a list of added columns """
        columns = []
        for row in range(self.roiTable.model().rowCount()):
            item = self.roiTable.item(row)
            field = item.data(Qt.ItemDataRole.UserRole)["id"]
            columns.append(field)
        return columns

    def onCboxInputNameChanged(self, index):
        self.refreshColumnFieldsList()
        index = self.cboxInputName.currentIndex()
        name = self.cboxInputName.itemText(index)
        if name != self.roi.name:
            self.clearTableColumns()
        if "Table >" in name or name in TABLE_NAMES:
            self.chkTable.setChecked(True)
        else:
            self.chkTable.setChecked(False)
        self.updateUi()

    def refreshColumnFieldsList(self):
        """ Filter Database fields based on dropdown selection """
        index = self.cboxInputName.currentIndex()
        itemData = self.cboxInputName.itemData(index, Qt.ItemDataRole.UserRole)
        if not itemData:
            return
        name = itemData["id"]
        scopes = roi_table_scopes.scopes
        usedFields = self.getAddedColumnsNamesList()
        fields = scopes.get(name)

        self.cboxAddColumn.clear()
        self.cboxAddColumn.update()
        # If id is a scope - then just display fields for the given scope table
        if fields:
            for field in fields:
                if field in usedFields:
                    continue
                fieldData = self.fieldMap[field]
                self.cboxAddColumn.addItem(fieldData.get("display", field), userData=fieldData)
        else:
            # Otherwise, add everything except excluded names
            for field in sorted(self.fieldMap.keys()):
                if field != self.roi.name and field in self.excludeNames:
                    continue
                if field in usedFields:
                    continue
                fieldData = self.fieldMap[field]
                self.cboxAddColumn.addItem(fieldData.get("display", field), userData=fieldData)

        self.cboxAddColumn.update()
        self.updateUi()

    def clearTableColumns(self):
        try:
            for row in range(self.roiTable.count(), 0, -1):
                row -= 1
                self.roiTable.model().removeRow(row)
                self.roi.removeColumn(row)
        except Exception as e:
            # No item to remove
            logger.error(f"Error removing all table columns in EditRoiDialog: {e}", exc_info=True)

    def getFieldMap(self):
        fieldMapJson = getSavedFieldMapJson()
        exclude_roi_keys = ['annotMarkups', 'avg_elevation', 'coordinates', 'coordinates2',
                            'elevation','flangeID', 'max_elevation', 'min_elevation',
                            'modDate', 'sys_build', 'sys_document', 'sys_filename',
                            'sys_layout_valid', 'sys_path', 'xCoord', 'yCoord', 'weldId',
                            'id_annot_info', 'pdf_id', 'pdf_page', 'page_number']  # Exclude from ROI drop-down selection # Added

        tagMap = {}
        newFields = {}  # Create a new dictionary for filtered fields
        for field, fieldData in fieldMapJson.get("fields", {}).items():
            ############################################# Added
            # Exclude fields based on their name
            if field not in exclude_roi_keys:
                tagMap[field] = fieldData.get("tags", [])
                newFields[field] = fieldData  # Add field to the new dictionary if not excluded
            #############################################

        self.cboxInputName.filterModel.tags = tagMap
        self.cboxAddColumn.filterModel.tags = tagMap
        self.fieldMap = newFields
        self.sgnFieldMapUpdated.emit()

    def onFieldMapUpdated(self):
        try:
            self.cboxInputName.currentIndexChanged.disconnect(self.onCboxInputNameChanged)
        except Exception as e:
            # On first call, we should expect exception
            logger.info(f"Cannot disconnect signal {e}", exc_info=True)

        self.cboxInputName.clear()
        self.cboxInputName.update()
        for field in TABLE_NAMES:
            if field != self.roi.name and field in self.excludeNames:
                continue
            fieldData = self.fieldMap[field]
            name = fieldData.get("display", field)
            name = f"Table > {name}"
            self.cboxInputName.addItem(name, userData=fieldData)

        table_scope_fields = set()
        for table_name, scope_fields in roi_table_scopes.scopes.items():
            if table_name == "SPEC": # Allow spec fields in general
                continue
            for f in scope_fields:
                table_scope_fields.add(f)
                print(table_name, f)

        for field in natsorted(self.fieldMap.keys()):
            if field in TABLE_NAMES:
                continue
            if field != self.roi.name and field in self.excludeNames:
                continue
            if field in table_scope_fields:
                continue
            fieldData = self.fieldMap[field]
            self.cboxInputName.addItem(fieldData.get("display", field), userData=fieldData)

        # We have to defer restoring the ROI state until the field map has been updated
        roi = self.roi
        if roi.name:
            field = roi.name
            displayKey = self.fieldMap.get(field, {}).get("display", roi.displayName)
            if displayKey in ["BOM", "SPEC", "Spool", "RFQ", "IFC", "Generic 1", "Generic 2"]:
                displayKey = f"Table > {displayKey}"
            findIndex = self.cboxInputName.findText(displayKey)
            self.cboxInputName.setCurrentIndex(findIndex)
            if field in TABLE_NAMES:
                self.backup["columnNames"] = [c for c in roi.columnNames]
                self.backup["columnRatios"] = [r for r in roi.columnRatios]
                for name in roi.columnNames:
                    fieldData = self.fieldMap[name]
                    item = QListWidgetItem(fieldData.get("display", name))
                    item.setData(Qt.ItemDataRole.UserRole, fieldData)
                    self.roiTable.addItem(item)
                self.chkTable.setChecked(roi.isTable)
                self.checkSaveable()

        # self.updateUi()
        # Late signal connect here to avoid premature refresh
        self.cboxInputName.currentIndexChanged.connect(self.onCboxInputNameChanged)
        self.refreshColumnFieldsList()


class UpdateProjectSourcePopup(QWidget):

    def __init__(self, parent, sourceData):
        super().__init__(parent)
        self._projectData = {}
        self.sourceData = sourceData

        self.setLayout(QVBoxLayout())
        self.setMinimumWidth(600)
        self.setMinimumHeight(216)

        widget = QWidget()
        widget.setObjectName("popup")
        widget.setLayout(QVBoxLayout())

        grid = QWidget()
        grid.setLayout(QGridLayout())
        grid.setObjectName("popup")

        self.layout().addWidget(widget)

        p = os.path.basename(self.sourceData["filename"])
        lblName = QLabel(f"{p}", self)
        lblName.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        lblName.setObjectName("popup")
        lblName.setToolTip(self.sourceData["filename"])
        grid.layout().addWidget(lblName, 1, 0, 1, 1)

        lblName = QLabel("Engineer drafter:", self)
        lblName.setObjectName("popup")
        grid.layout().addWidget(lblName, 2, 0, 1, 1)
        self.engineerDrafterValue = QLineEdit("", self)
        self.engineerDrafterValue.setObjectName("engineerDrafterValue")
        documentVendor = self.sourceData.get("documentVendor")
        if documentVendor:
            self.engineerDrafterValue.setText(documentVendor)
        self.engineerDrafterValue.textChanged.connect(self.onTextChanged)
        grid.layout().addWidget(self.engineerDrafterValue, 3, 0, 1, 1)

        widget.layout().addWidget(grid)

        self.pbUpdate = QPushButton("Update")
        self.pbUpdate.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbUpdate.setEnabled(False)
        widget.layout().addWidget(self.pbUpdate)

        self.pbCancel = QPushButton("Cancel")
        self.pbCancel.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.pbCancel.clicked.connect(self.close)
        widget.layout().addWidget(self.pbCancel)

        self.pbUpdate.clicked.connect(self.onUpdateDetails)

        self.topLevelWidget().windowResized.connect(self.resizeEvent)
        applyDropShadowEffect(self)

    def setSourceData(self, data: dict):
        self.engineerDrafterValue.setText(data.get("documentVendor"))
        if not data.get("documentVendor"):
            self.show()
        self.sourceData = data

    def setErrorMessage(self, text):
        self.lblError.setText(text)

    def onUpdateDetails(self):
        engineerDrafter = self.engineerDrafterValue.text()
        if not engineerDrafter:
            self.pbUpdate.setEnabled(False)
            return
        self.sourceData["documentVendor"] = engineerDrafter

        def callback(res):
            if res["status"] == "ok":
                message = "Project source updated"
                self.sourceData.update(res["data"])
            else:
                message = "Update project source failed"
            QMessageBox.information(self, "Update Project Source", message)
            self.close()

        pub.sendMessage("project-source-update", data=self.sourceData, callback=callback)

    def onTextChanged(self, text):
        self.pbUpdate.setEnabled(True if text else False)

    def focus(self):
        self.engineerDrafterValue.setFocus()

    def resizeEvent(self, event: QResizeEvent) -> None:
        x = (self.topLevelWidget().width() // 2) - (self.width() // 2)
        y = 30
        self.move(QPoint(x, y))
        return super().resizeEvent(event)

class GroupPagesTable(PandasTable):

    contextMenuRequested = Signal()
    def __init__(self, parent=None):
        super().__init__(parent)

    def contextMenuEvent(self, event):
        res = super().contextMenuEvent(event)
        self.contextMenuRequested.emit()
        return res

    def deletePage(self, row: int, page: int, parent=QModelIndex()):
        self.model().beginRemoveRows(parent, row, 1)
        self.model()._data.drop(index=page, inplace=True)
        self.model().endRemoveRows()

    def removePages(self, rows: list[int], pages: list[int], parent=QModelIndex()):
        first = rows[0]
        last = rows[-1]
        self.model().beginRemoveRows(parent, first, last)
        df = self.model()._data
        df = df[~self.model()._data["Page"].isin(pages)]
        self.model()._data = df
        self.model().endRemoveRows()

    def addPages(self, pages: pd.DataFrame, parent=QModelIndex()):
        first = self.model().rowCount()
        last = first + len(pages) - 1
        self.model().beginInsertRows(parent, first, last)
        df = self.model()._data
        df = pd.concat([df, pages])
        df = df.sort_values("page")
        self.model()._data = df
        self.model().endInsertRows()

    def updatePage(self, page: int, extract: bool, ocr: bool):
        """Sets the page extraction options

        Usage:
            Called externally e.g. from another button
        """
        df = self.model()._data["Page"].reset_index()
        row = df[df["Page"] == page].index.item()
        index = self.model().index(row, 1, QModelIndex())
        self.model().setData(index, extract)
        index = self.model().index(row, 2, QModelIndex())
        self.model().setData(index, ocr)


class GroupPagesSection(QWidget):

    pageSelected = Signal(dict)
    def __init__(self, group, parent=None):
        super().__init__(parent)

        self.group: int = group
        self.rois: list = []
        self.isCollapsed = True

        self.setLayout(QVBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setAlignment(Qt.AlignmentFlag.AlignTop)

        # Header
        self.header = QWidget()
        self.header.setObjectName("groupPageSection")

        self.header.setLayout(QHBoxLayout())
        self.header.layout().setContentsMargins(8, 8, 8, 8)
        self.header.layout().setAlignment(Qt.AlignmentFlag.AlignTop)

        self.pbToggle = QPushButton("▶")
        self.pbToggle.setFixedSize(8, 8)
        self.pbToggle.setStyleSheet("""
            QPushButton {
                border: none;
                color: #94a3b8;
                background: transparent;
            }
        """)
        self.pbToggle.clicked.connect(self.toggleCollapse)

        self.lblTitle = QLabel(f"Group {group}")
        self.lblTitle.setStyleSheet("color: #e2e8f0; font-weight: bold; font: 13px;")

        self.lblPages = QLabel("(0 pages)")
        self.lblPages.setStyleSheet("color: #94a3b8; font-size: 12px;")

        self.cbExtract = QCheckBox(text="Extract")
        self.cbExtract.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.cbExtract.setStyleSheet("font-size: 11px;")
        self.cbOcr = QCheckBox(text="OCR")
        self.cbOcr.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.cbOcr.setStyleSheet("font-size: 11px;")

        self.header.layout().addWidget(self.pbToggle)
        self.header.layout().addWidget(self.lblTitle)
        self.header.layout().addWidget(self.lblPages)
        self.header.layout().addStretch()
        self.header.layout().addWidget(self.cbExtract)
        self.header.layout().addWidget(self.cbOcr)
        self.header.setMaximumHeight(36)

        self.layout().addWidget(self.header)

        # Content
        self.content = QWidget()
        self.content.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.content.setLayout(QVBoxLayout())
        self.content.layout().setContentsMargins(0, 0, 0, 0)
        self.content.layout().setSpacing(0)
        self.content.layout().setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout().addWidget(self.content)

        self.table: GroupPagesTable = GroupPagesTable(self.content)
        self.table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.table.model().setCheckableColumns([1, 2])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.table.clicked.connect(self.onTableClicked)
        self.table.selectionChanged = self.onSelectionChanged
        self.content.layout().addWidget(self.table)

        self.isCollapsed = False
        self.toggleCollapse()

    @property
    def pages(self) -> list[int]:
        """Return the list of page numbers in this group"""
        return self.table.model()._data["Page"].unique()

    @property
    def pageCount(self) -> int:
        """Return the list of page numbers in this group"""
        return self.table.model().rowCount()

    def getPageData(self, page: int) -> dict:
        df: pd.DataFrame = self.table.model()._data
        data = df.loc[page].to_dict()
        data = {
            "group": self.group,
            "extract": data["Extract"],
            "ocr": data["OCR"],
            "page": data["Page"],
        }
        return data

    def updateUi(self):
        self.lblPages.setText(f"({self.table.model().rowCount()} pages)")
        self.lblTitle.setText(f"Group {self.group}")
        self.updateTableHeight()

    def updateTableHeight(self):
        # Resize table height to show all rows
        rows = self.table.model().rowCount()
        rowHeight = self.table.rowHeight(0)
        tableHeight = rows * rowHeight
        tableHeight += self.table.horizontalHeader().height() + 2 * self.table.frameWidth()
        self.table.setFixedHeight(tableHeight)
        # self.content.setFixedHeight(tableHeight)

    def resizeEvent(self, event):
        # self.content.setFixedWidth(self.width()-90)
        return super().resizeEvent(event)

    def onTableClicked(self, event: QModelIndex):
        pageData = self.table.model().data(self.table.currentIndex(), Qt.UserRole)
        if not pageData:
            return
        pageData = {
            "group": self.group,
            "page": pageData["Page"],
            "extract": pageData["Extract"],
            "applyOcr": pageData["OCR"],
        }
        self.pageSelected.emit(pageData)

    def onSelectionChanged(self, selected, deselected):
        super(PandasTable, self.table).selectionChanged(selected, deselected)
        pageData = self.table.model().data(self.table.currentIndex(), Qt.UserRole)
        if not pageData:
            return
        pageData = {
            "group": self.group,
            "page": pageData["Page"],
            "extract": pageData["Extract"],
            "applyOcr": pageData["OCR"],
        }
        self.pageSelected.emit(pageData)

    def toggleCollapse(self):
        self.isCollapsed = not self.isCollapsed
        self.pbToggle.setText("▼" if not self.isCollapsed else "►")
        self.content.setVisible(not self.isCollapsed)
        self.updateTableHeight()

    def setData(self, data):
        self.rois = data.get("rois", [])
        pages = data["pages"]

        self.cbExtract.setChecked(data.get("extract", True))
        self.cbOcr.setChecked(data.get("ocr", False))

        self.lblPages.setText(f"({len(pages)} pages)")

        df = []
        for index, pageData in enumerate(pages):
            pageData["index"] = index
            page = pageData["page"]
            extract = pageData.get("extract", True)
            applyOcr = pageData.get("ocr", False)

            df.append({
                "page": page,
                "Page": page,
                "Extract": extract,
                "OCR": applyOcr,
            })

        df = pd.DataFrame(df).sort_values("page")
        df.set_index("page", inplace=True)
        self.table.setDataFrame(df)

    def getState(self) -> dict:
        """Returns saveable state of the group"""
        df = self.table.dataframe()
        pages = []
        for row in df.itertuples():
            pageData = {
                "page": row.Page
            }
            if not row.Extract:
                pageData["extract"] = False
            if row.OCR:
                pageData["ocr"] = True
            pages.append(pageData)

        state = {
            self.group: {
                "pages": pages,
                "rois": self.rois,
            }
        }
        if not self.cbExtract.isChecked():
            state[self.group]["extract"] = False
        if self.cbOcr.isChecked():
            state[self.group]["ocr"] = True

        return state

class PageGroups(QWidget):
    """Displays the list of ROIs"""

    analyzePagesPressed = Signal()
    saveExtractionOptions = Signal()
    restoreDefaultExtractionOptions = Signal()
    setPageGroups = Signal(dict)
    pageItemSelected = Signal(int)
    pageDataChanged = Signal(dict)
    restoreDefaultGroups = Signal()
    restoreSavedGroups = Signal()
    groupsModified = Signal(bool)  # bool - save layout
    setPageGroupsFailed = Signal()

    def __init__(self, parent):
        super().__init__(parent=parent)
        self.setLayout(QVBoxLayout())

        self.currentGroup = None
        self.data = {}
        self.groupRois = {}
        self.pageData = {} # Cache page num to page option
        self.groupPages: list[GroupPagesSection] = []
        self._contextGroup: int = 0 # The group number

        spacer = QSpacerItem(1, 16, QSizePolicy.Policy.Ignored, QSizePolicy.Policy.Minimum)
        self.layout().layout().addItem(spacer)

        hbox = QWidget()
        hbox.setLayout(QHBoxLayout())
        hbox.layout().setSpacing(4)
        hbox.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.pbMenu = MyButton(text="", style=MyButton.BUTTON_MENU_STYLE)
        self.pbMenu.setIcon(get_resource_qicon("menu.svg"))
        menu = QMenu(self.pbMenu)
        menu.triggered.connect(self.onMenu)

        self.actRestoreDefaults = QAction("Restore Defaults")
        self.actRestoreSaved = QAction("Restore Saved")
        menu.addAction(self.actRestoreDefaults)
        menu.addAction(self.actRestoreSaved)

        self.pbMenu.setMenu(menu)
        self.pbMenu.setMinimumHeight(32)
        self.pbMenu.setFixedWidth(32)
        self.pbMenu.clicked.connect(lambda: self.restoreDefaultExtractionOptions.emit())
        hbox.layout().addWidget(self.pbMenu)
        hbox.setMaximumHeight(42)
        self.layout().addWidget(hbox)

        # self.pbDetectPageGroups = MyButton(text="Detect Groups", leading_icon=None)
        self.pbDetectPageGroups = BusyButton(text="Detect Groups")
        self.pbDetectPageGroups.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.pbDetectPageGroups.setFixedHeight(32)
        self.pbDetectPageGroups.clicked.connect(self.onAnalyze)
        hbox.layout().addWidget(self.pbDetectPageGroups)

        self.pbSaveOptions = QPushButton(text="Save")
        self.pbSaveOptions.setFixedHeight(32)
        self.pbSaveOptions.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.pbSaveOptions.clicked.connect(lambda: self.saveExtractionOptions.emit())
        hbox.layout().addWidget(self.pbSaveOptions)

        spacer = QSpacerItem(1, 16, QSizePolicy.Policy.Ignored, QSizePolicy.Policy.Minimum)
        self.layout().layout().addItem(spacer)

        self.scrollArea = QScrollArea()
        self.scrollArea.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setLayout(QVBoxLayout())
        # self.scrollArea.layout().addStretch()

        self.layout().addWidget(self.scrollArea)

        self.vsplitter = QSplitter()
        self.vsplitter.setOrientation(Qt.Orientation.Vertical)
        self.setPageGroups.connect(self.onSetPageGroups)

        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.layout().setSpacing(2)
        self.layout().setContentsMargins(0, 0, 0, 0)

        self.initContextMenu()

        # self.setObjectName("toolbar")
        self.scrollArea.setObjectName("panel")

    def getGroupCount(self):
        """Return number of groups"""
        return len(self.groupPages)

    def onAnalyze(self):
        self.analyzePagesPressed.emit()

    def getGroupFromPage(self, page: int) -> int:
        """Return Group number for page"""
        section: GroupPagesSection
        for section in self.groupPages:
            group = section.group
            if page in section.pages:
                return group

    def saveCurrentGroup(self):
        """Update data for group only if it is opened"""
        self.getGroupPageSection(self.currentGroup)

    def onSetPageGroups(self, data: dict):
        self.data = deepcopy(data)
        while self.scrollArea.layout().count():
            child = self.scrollArea.layout().takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        self.groupPages = []
        self.scrollArea.setWidget(QWidget())
        self.scrollArea.widget().setLayout(QVBoxLayout())
        self.scrollArea.widget().layout().setSpacing(0)
        self.scrollArea.widget().layout().setContentsMargins(0, 0, 0, 0)
        self.scrollArea.widget().setObjectName("panel")
        self.scrollArea.setObjectName("panel")
        # Important - ensures that layout adjusts to changes in table height
        # self.scrollArea.widget().setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # self.scrollArea.widget().layout().setSizeConstraint(QLayout.SetFixedSize)

        def to_int(k):
            try:
                return int(k)
            except:
                return 0

        try:
            keys = [to_int(k) for k in data.get("groups", {}).keys() if to_int(k)]
            keys.sort()
            for groupNumber in keys:
                # Ensure key is int
                try:
                    data["groups"][int(groupNumber)] = data["groups"].pop(str(groupNumber))
                except:
                    pass
                section = GroupPagesSection(groupNumber, self)
                section.table.contextMenuRequested.connect(partial(self.onContextMenu, groupNumber))
                section.pageSelected.connect(self.onPageChanged)

                groupData = data["groups"][int(groupNumber)]
                section.setData(groupData)
                self.scrollArea.widget().layout().addWidget(section)
                self.groupPages.append(section)
        except Exception as e:
            logger.warning("Failed to load extraction options")
            self.setPageGroupsFailed.emit()
            return

        self.stretch = QSpacerItem(8, 8, QSizePolicy.Minimum,QSizePolicy.Expanding)
        self.scrollArea.widget().layout().addItem(self.stretch)

        # Try to select the first page of the first group
        for s in self.groupPages:
            if s.table.model().rowCount():
                s.table.selectRow(0)
                break

        self.resizeEvent(None)

    def initContextMenu(self):
        self.menu = menu = QMenu(self)

        extractSubMenu: QMenu = menu.addMenu("Extraction")
        ocrSubMenu: QMenu = menu.addMenu("OCR")
        pageSubMenu: QMenu = menu.addMenu("Group")

        items = [
            ("Move Selected Pages To New Group", self.moveSelectedPagesToNewGroup),
        ]
        for item, listener in items:
            action = QAction(item, menu)
            action.triggered.connect(partial(listener))
            pageSubMenu.addAction(action)

        self.actMoveToGroup: QMenu = pageSubMenu.addMenu("Move Selected Pages To Group")
        self.actMoveToGroup.triggered.connect(self.moveSelectedPagesToGroup)

        items = [
            ("Mark Selected Pages", self.checkSelectedPagesForExtraction),
            ("Mark All Pages", self.checkCurrentGroupForExtraction),
            ("Remove Selected", lambda: self.checkSelectedPagesForExtraction(False)),
            ("Remove All Pages", lambda: self.checkCurrentGroupForExtraction(False)),
        ]
        for item, listener in items:
            action = QAction(item, menu)
            action.triggered.connect(partial(listener))
            extractSubMenu.addAction(action)

        items = [
            ("Mark Selected Pages", self.checkSelectedPagesForOcr),
            ("Mark All Pages", self.checkCurrentGroupForOcr),
            ("Remove Selected Pages", lambda: self.checkSelectedPagesForOcr(False)),
            ("Remove All Pages", lambda: self.checkCurrentGroupForOcr(False)),
        ]
        for item, listener in items:
            action = QAction(item, menu)
            action.triggered.connect(partial(listener))
            ocrSubMenu.addAction(action)

    def onContextMenu(self, group: int):
        self._contextGroup: int = group
        self.updateContextMenu()
        self.menu.popup(QCursor.pos())

    def onPageChanged(self, data: dict):
        self.currentGroup = data["group"]
        self.updateContextMenu()
        self.pageItemSelected.emit(data["page"])

    def getGroupNumberFromPage(self, page: int):
        return self.pageToGroup[page]

    def getGroupRois(self, group: int) -> dict:
        for section in self.groupPages:
            if section.group == group:
                return section.rois

    def setGroupRois(self, group: int, rois):
        """Set group ROIs"""
        for section in self.groupPages:
            if section.group == group:
                section.rois = rois
                break

    def syncGroupRois(self, rois, groups: list[int], remove: bool = False):
        """Synchronize rois with other selected groups"""
        # Update, remove or add ROIs to all other groups
        logger.debug(f"Synchronizing layouts to groups: {groups[:5]}...")
        roiNames = [r['name'] for r in rois]

        for section in self.groupPages:
            if section.group not in groups:
                continue
            rois2 = section.rois
            updated = []
            roiNames2 = [r['name'] for r in rois2]
            # Add ROIs which are not currently in group
            for roi in rois:
                if roi['name'] not in roiNames2:
                    updated.append(roi)
            for roi in rois2:
                # Remove ROIs which have been removed from saved group
                if remove and roi['name'] not in roiNames:
                    continue
                updated.append(roi)
            section.rois = updated

    def getState(self) -> dict:

        data = {"groups": {}}

        section: GroupPagesSection
        for section in self.groupPages:
            groupData = section.getState()
            data["groups"].update(groupData)

        return data

    def clear(self):
        pass

    def onMenu(self, action):
        if action == self.actRestoreDefaults:
            self.restoreDefaultGroups.emit()
        elif action == self.actRestoreSaved:
            self.restoreSavedGroups.emit()

    def createNewGroup(self):
        newGroup = self.groupList.rowCount() + 1
        self.groupList.addRow([newGroup, 0, ""], [None, None, False], data=newGroup)
        self.data["groups"][newGroup] = {"pages": [], "rois": []}
        self.updateContextMenu()

    def removeCurrentGroup(self):
        if not self.currentGroup:
            return

        if self.pagesList.rowCount() > 0:
            QMessageBox.information(self, "Remove Group", f"Group {self.currentGroup} cannot be removed as it contains pages")
            return

        if QMessageBox.question(self, "Remove Group", f"[Not Implemented] Remove Group {self.currentGroup}") != QMessageBox.Yes:
            return

        # group = self.groupList.item(index.row(), 0).data(Qt.ItemDataRole.UserRole)
        # if self.currentGroup == group:
        #     return

    def onPageRowDataChanged(self, row: int):
        if not self.currentGroup:
            return
        page = self.pagesList.item(row, 0).data(Qt.ItemDataRole.UserRole)
        extract = True if self.pagesList.item(row, 1).checkState() is Qt.CheckState.Checked else False
        ocr = True if self.pagesList.item(row, 2).checkState() is Qt.CheckState.Checked else False

        pageData = self.pageData[page]

        if not extract:
            self.pageData[page]["extract"] = False
        elif "extract" in pageData:
            del self.pageData[page]["extract"]

        if ocr:
            pageData["ocr"] = True
        elif "ocr" in pageData:
            del pageData["ocr"]

        self.data["groups"][self.currentGroup]["pages"][row] = pageData
        self.pageDataChanged.emit(pageData)

    def onPagesListHeaderClicked(self):
        if self.currentGroup is None:
            return
        self.pagesList.showHeaderContextMenu()

    def checkCurrentGroupForOcr(self, checked: bool = True):
        """Checks/unchecks all pages in the group for OCR"""
        section = self.getGroupPageSection(self._contextGroup)
        section.table.model()._data["OCR"] = checked

    def checkSelectedPagesForOcr(self, checked: bool = True):
        """Checks/unchecks selected pages in the group for OCR"""
        section = self.getGroupPageSection(self._contextGroup)
        column = 2
        for index in section.table.selectionModel().selectedRows():
            section.table.model().setData(index.siblingAtColumn(column), checked, Qt.ItemDataRole.EditRole)

    def checkCurrentGroupForExtraction(self, checked: bool = True):
        """Checks/unchecks all pages in the group for extraction"""
        section = self.getGroupPageSection(self._contextGroup)
        section.table.model()._data["Extract"] = checked

    def getGroupPageSection(self, group: int) -> GroupPagesSection:
        for section in self.groupPages:
            if section.group == group:
                return section

    def checkSelectedPagesForExtraction(self, checked: bool = True):
        """Checks/unchecks selected pages in the group for extraction"""
        section = self.getGroupPageSection(self._contextGroup)
        column = 1
        for index in section.table.selectionModel().selectedRows():
            section.table.model().setData(index.siblingAtColumn(column), checked, Qt.ItemDataRole.EditRole)

    def updateContextMenu(self):
        """Update options in context menu"""
        self.actMoveToGroup.clear()
        self.actMoveToGroup.setEnabled(False)
        for n in range(self.getGroupCount()):
            if n + 1 == self._contextGroup:
                continue
            action = QAction(f"{n+1}", self.actMoveToGroup)
            action.setData(n+1)
            self.actMoveToGroup.addAction(action)
            self.actMoveToGroup.setEnabled(True)

    def moveSelectedPagesToNewGroup(self):
        """Create a new group and move items"""
        section = self.getGroupPageSection(self._contextGroup)

        # Get seletected page rows
        pages = []
        removePages = set()
        rows = set()
        for index in section.table.selectionModel().selectedRows():
            pageData = section.table.model().data(index, Qt.ItemDataRole.UserRole)
            pageData["page"] = pageData.pop("Page")
            removePages.add(pageData["page"])
            rows.add(index.row())
            if "Extract" in pageData:
                 pageData["extract"] = pageData.pop("Extract")
            if "OCR" in pageData:
                 pageData["ocr"] = pageData.pop("OCR")
            pages.append(pageData)

        section.blockSignals(True)
        section.table.selectionModel().clear()
        section.blockSignals(False)

        # Remove pages from their current group
        section.table.removePages(sorted(rows), sorted(removePages))
        section.updateUi()

        newGroup = len(self.groupPages) + 1
        newSection = GroupPagesSection(newGroup, self)
        newSection.table.contextMenuRequested.connect(partial(self.onContextMenu, newGroup))
        newSection.pageSelected.connect(self.onPageChanged)

        newData = {
            "pages": pages,
            "rois": deepcopy(section.rois),
            "extract": section.cbExtract.isChecked(),
            "ocr": section.cbOcr.isChecked(),
        }
        newSection.setData(newData)
        self.scrollArea.widget().layout().addWidget(newSection)
        self.groupPages.append(newSection)

        self.cleanGroupSection()
        self.groupsModified.emit(True)

    def cleanGroupSection(self):
        """Remove empty groups and reindex group numbers"""

        layout = self.scrollArea.widget().layout()
        for n in reversed(range(layout.count())):
            section = layout.itemAt(n)
            if not section.widget():
                continue
            section = section.widget()
            if not section.pageCount:
                layout.takeAt(n)
                section.deleteLater()

        self.groupPages = []
        index = 0
        for n in range(layout.count()):
            section = layout.itemAt(n)
            if not section.widget():
                continue
            section: GroupPagesSection = section.widget()
            section.group = index + 1
            section.updateUi()

            section.table.contextMenuRequested.disconnect()
            section.table.contextMenuRequested.connect(partial(self.onContextMenu, section.group))
            self.groupPages.append(section)
            index += 1

        # Reappends the stretch
        self.scrollArea.widget().layout().removeItem(self.stretch)
        self.scrollArea.widget().layout().addItem(self.stretch)

    def moveSelectedPagesToGroup(self, action: QAction):
        """Move selected items from current group to another existing group"""
        existingGroup = action.data()
        fromSection = self.getGroupPageSection(self._contextGroup)
        toSection = self.getGroupPageSection(existingGroup)

        pages = []
        removePages = set()
        rows = set()
        for index in fromSection.table.selectionModel().selectedRows():
            pageData = fromSection.table.model().data(index, Qt.ItemDataRole.UserRole)
            removePages.add(pageData["Page"])
            rows.add(index.row())
            pages.append(pageData)

        fromSection.table.selectionModel().clear()
        fromSection.table.removePages(sorted(rows), sorted(removePages))

        pages = pd.DataFrame(pages)
        pages["page"] = pages["Page"]
        pages.set_index("page", inplace=True)
        toSection.table.addPages(pages)

        fromSection.updateUi()
        toSection.updateUi()

        self.cleanGroupSection()
        self.groupsModified.emit(False)

    def getPageData(self, page: int):
        section: GroupPagesSection
        for section in self.groupPages:
            if page in section.pages:
                return section.getPageData(page)

    def setPageData(self, page: int, extract: bool, ocr: bool):
        group = self.getGroupFromPage(page)
        section = self.getGroupPageSection(group)
        section.table.updatePage(page, extract, ocr)


class RoiSidebar(QWidget):
    """ Displays the list of ROIs """
    extractionClicked = Signal()
    sgnRoiClearAll = Signal()
    sgnPdfSaveRois = Signal(object) # List of current ROIS e.g. from self.getRois()
    removeRoi = Signal(object)

    def __init__(self, parent):
        super().__init__(parent=parent)
        try:
            self._projectData = None
            self._sourceData = None
            self._rois = []
            self.setLayout(QVBoxLayout())

            self.updateProjectPopup = None  # Dialog

            self.gbRoiList = QGroupBox()
            self.gbRoiList.setLayout(QVBoxLayout())
            self.gbRoiList.layout().setSpacing(0)
            self.gbRoiList.layout().setContentsMargins(0, 0, 0, 0)
            self.layout().addWidget(self.gbRoiList)

            self.roiList = QListWidget(self)
            self.roiList.setItemAlignment(Qt.AlignmentFlag.AlignCenter)
            self.roiList.setSortingEnabled(True)

            self.gbRoiList.layout().addWidget(self.roiList)

            hbox = QWidget(self)
            hbox.setLayout(QHBoxLayout())
            hbox.layout().setSpacing(2)
            hbox.layout().setContentsMargins(0, 0, 0, 0)
            self.pbEdit = QPushButton(text="Edit")
            self.pbEdit.clicked.connect(self.onRoiEdit)
            self.pbEdit.setMinimumHeight(24)
            hbox.layout().addWidget(self.pbEdit)
            self.pbRemove = QPushButton(text="Remove")
            self.pbRemove.clicked.connect(self.onRoiRemove)
            self.pbRemove.setMinimumHeight(24)
            # self.pbRemove.setObjectName("MyButtonDanger")
            hbox.layout().addWidget(self.pbRemove)

            self.pbClearAll = QPushButton(text="Clear")
            self.pbClearAll.setMinimumHeight(24)
            self.pbClearAll.clicked.connect(self.onClearAll)
            # self.pbClearAll.setObjectName("MyButtonDanger")
            hbox.layout().addWidget(self.pbClearAll)

            self.gbRoiList.layout().addWidget(hbox)

            self.roiList.itemActivated.connect(self.onRoiEdit)
            self.roiList.selectionModel().selectionChanged.connect(self.onRoiItemSelected)

            self.updateButtonState()
        except Exception as e:
            logger.error(f"Error setting up RoiSidebar: {e}", exc_info=True)

        pub.subscribe(self.onProjectSourceUpdated, "project-source-updated")
        self.sgnRoiClearAll.connect(self.onSgnRoiClearAll)

    def updateButtonState(self):
        try:
            if self.roiList.selectedIndexes():
                self.pbEdit.setEnabled(True)
                self.pbRemove.setEnabled(True)
            else:
                self.pbEdit.setEnabled(False)
                self.pbRemove.setEnabled(False)

            self.pbClearAll.setEnabled(self.roiList.model().rowCount() > 0)

            # if self.roiList.count() == 0:
            #     self.pbExtract.setToolTip("ROIs are required for extraction")
            # else:
            #     self.pbExtract.setToolTip("")
            #     self.pbExtract.setEnabled(True)

        except Exception as e:
            logger.error(f"Error updating button state in RoiSidebar: {e}", exc_info=True)

    def getRois(self) -> list[RoiItem]:
        rois = []
        try:
            for n in range(self.roiList.model().rowCount()):
                item = self.roiList.item(n)
                roi: RoiItem = item.data(Qt.ItemDataRole.UserRole)()
                rois.append(roi)
        except Exception as e:
            logger.error(f"Error getting ROIs: {e}", exc_info=True)
        return rois

    def getRoisNames(self) -> list:
        """Return list of ROI names"""
        names = set()
        try:
            for n in range(self.roiList.model().rowCount()):
                item = self.roiList.item(n)
                roi = item.data(Qt.ItemDataRole.UserRole)
                names.add(roi.name)
        except Exception as e:
            logger.error(f"Error getting ROI names: {e}", exc_info=True)
        return names

    def getSelectedItem(self):
        try:
            if self.roiList.selectedIndexes():
                row = self.roiList.selectedIndexes()[0].row()
                return self.roiList.item(row)
        except Exception as e:
            logger.info(f"INFO ONLY: Error getting selected item in RoiSidebar. Return None: {e}", exc_info=True)
            return None

    def getSelectedItemRoi(self) -> RoiItem:
        """Return ROI linked to selected list item"""
        try:
            item = self.getSelectedItem()
            if item:
                roiRef = item.data(Qt.ItemDataRole.UserRole)
                return roiRef()
        except Exception as e:
            logger.error(f"Error getting selected item ROI in RoiSidebar: {e}", exc_info=True)

    def onRoiEdit(self):
        self.onRoiItemSelected() # Select on activation
        try:
            item: QListWidgetItem = self.getSelectedItem()
            roi: RoiItem = self.getSelectedItemRoi()
            self.editRoiDialog = dlg = EditRoiDialog(self, roi, excludeNames=self.getRoisNames()) # I am not quite understanding this line and how this is an exclusion. Attaching manual list below
            resp = dlg.exec()
            # Update the item
            item.setData(Qt.ItemDataRole.DisplayRole, roi.displayName)
            item.setData(Qt.ItemDataRole.UserRole, weakref.ref(roi))
            if roi.isTable:
                dn = roi.displayName
                if not roi.displayName.startswith("Table > "):
                    dn = "Table > " + dn
                item.setData(Qt.ItemDataRole.DisplayRole, f"{dn}")
                icon = get_resource_qicon("columns.svg")
            else:
                icon = get_resource_qicon("square.svg")
            item.setIcon(icon)
        except Exception as e:
            logger.error(f"Error editing ROI in RoiSidebar: {e}", exc_info=True)

    def onRoiRemove(self):
        """Remove selected ROI"""
        try:
            indexes = self.roiList.selectedIndexes()
            if indexes:
                roi = self.getSelectedItemRoi()
                self.removeRoi.emit(roi)
        except Exception as e:
            logger.error(f"Error removing ROI in RoiSidebar: {e}", exc_info=True)

    def onRoiRemoved(self, removedRoi):
        """Remove list item given roi"""
        try:
            for row in range(self.roiList.model().rowCount()):
                item = self.roiList.item(row)
                roi = item.data(Qt.ItemDataRole.UserRole)()
                if roi == removedRoi:
                    self.roiList.model().removeRow(row)
                    self.updateButtonState()
                    break
        except Exception as e:
            logger.error(f"Error in onRoiRemoved in RoiSidebar for ROI: {removedRoi.name if removedRoi else 'Unknown'}: {e}")

    def onRoiUpdated(self, updatedRoi):
        """Update list item given roi"""
        try:
            for row in range(self.roiList.model().rowCount()):
                item = self.roiList.item(row)
                roi = item.data(Qt.ItemDataRole.UserRole)()
                if roi == updatedRoi:
                    item.setText(roi.displayName)
                    if roi.isTable:
                        icon = get_resource_qicon("columns.svg")
                    else:
                        icon = get_resource_qicon("square.svg")
                    item.setIcon(icon)
                    self.updateButtonState()
                    break
        except Exception as e:
            logger.error(f"Error updating ROI in list: {e}", exc_info=True)

    def onRoiSelected(self, selectedRoi):
        try:
            for row in range(self.roiList.model().rowCount()):
                item = self.roiList.item(row)
                roi = item.data(Qt.ItemDataRole.UserRole)()
                if roi == selectedRoi:
                    item.setSelected(True)
                    self.updateButtonState()
                    break
        except Exception as e:
            logger.error(f"Error handling ROI selection: {e}", exc_info=True)

    def onRoiItemSelected(self):
        try:
            self.updateButtonState()
            roi: RoiItem = self.getSelectedItemRoi()
            if not roi:
                return
            roi.selected = True
        except Exception as e:
            logger.error(f"Error in item selected event handling: {e}", exc_info=True)

    def onClearAll(self):
        """ Remove all rois subject to user confirmation"""
        try:
            resp = QMessageBox.question(self,
                                        'Confirm Clear All',
                                        "Clear ROIs?",
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.Yes)
            if resp == QMessageBox.Yes:
                self.clearAll()
        except Exception as e:
            logger.error(f"Error clearing all ROIs: {e}", exc_info=True)

    def clearAll(self):
        self.sgnRoiClearAll.emit()

    def onSgnRoiClearAll(self):
        self.roiList.clear()
        self.updateButtonState()

    def onRoiAdded(self, newRoi: RoiItem):
        try:
            ref = weakref.ref(newRoi)
            newRoi.sgnRemoved.connect(lambda: self.onRoiRemoved(ref()))
            newRoi.sgnUpdated.connect(lambda: self.onRoiUpdated(ref()))
            newRoi.sgnSelected.connect(lambda: self.onRoiSelected(ref()))

            name = newRoi.displayName
            if newRoi.isTable:
                icon = get_resource_qicon("columns.svg")
            else:
                icon = get_resource_qicon("square.svg")
            if name in TABLE_NAMES:
                name = f"Table > {name}"
            item = QListWidgetItem(icon, name)
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            item.setData(Qt.ItemDataRole.UserRole, ref)
            self.roiList.addItem(item)
            self.updateButtonState()
        except Exception as e:
            logger.error(f"Error adding new ROI to list: {newRoi.name if newRoi else 'Unknown'}: {e}", exc_info=True)

    def syncProjectData(self, projectData: dict):
        self._projectData = projectData

    @property
    def sourceData(self):
        return self._sourceData

    @sourceData.setter
    def sourceData(self, data):
        self._sourceData = data
        if not data:
            return
        # readyToExtract = True if "documentVendor" in self.sourceData else False
        # self.pbExtract.setEnabled(readyToExtract)
        # if readyToExtract and self.updateProjectPopup:
        #     self.updateProjectPopup.close()
        #     self.updateProjectPopup = None

    def onProjectSourceUpdated(self, data):
        # Only update data if the loaded source is same as updated source
        try:
            if data["filename"] == self.sourceData["filename"]:
                if data["projectId"] == self.sourceData["projectId"]:
                    self.sourceData = data
        except Exception:
            pass

class OverlayShortcutHelper(QWidget):

    def __init__(self, parent: QGraphicsView) -> None:
        pass


class OverlayHint(QWidget):

    sgnSetHint = Signal(str)

    MAX_OPACITY = 0.8

    SHORTCUT_HINT = "Shorctuts: Ctrl +/- Zoom"
    ACTIVE_SHORTCUT_HINT = ""

    def __init__(self, parent: QGraphicsView) -> None:
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        self.hint = QLabel("This is an overlay hint")
        self.hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hint.setContentsMargins(16, 8, 16, 8)
        self.layout().addWidget(self.hint)
        self.setMinimumHeight(64)
        self.setObjectName("overlayHint")
        self.hint.setObjectName("overlayHint")
        self.sgnSetHint.connect(self.onSetHint)

        # Ignores any mouse input
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setWindowFlags(Qt.FramelessWindowHint)

        self.effect: QGraphicsOpacityEffect = QGraphicsOpacityEffect(self)
        self.effect.setOpacity(0.0)
        self.setGraphicsEffect(self.effect)

        duration = 400
        self.animShow = QVariantAnimation(self.hint)
        self.animShow.setDuration(duration)
        self.animShow.valueChanged.connect(self.onOpacityUpdate)

        # self.animShow = QVariantAnimation(self.hint)
        # self.animShow.setDuration(duration)

    def update(self):
        """ Align hint centrally on bottom """
        geom = self.parent().geometry()
        margin = 32
        self.move((geom.width()//2)-(self.width()//2),
                  geom.height()-self.height()-margin)

    def onSetHint(self, text: str):
        """Display new message and resize to width of string"""
        self.hint.setText(text)
        self.adjustSize()
        self.update()
        self.startShowAnimation()

    def onOpacityUpdate(self, value):
        if value:
            self.effect.setOpacity(value)

    def startShowAnimation(self):
        try:
            self.animShow.finished.disconnect(self.onShowAnimationFinished)
        except Exception as e:
            pass
        self.animShow.setStartValue(min(0.6, self.effect.opacity()))
        self.animShow.setEndValue(self.MAX_OPACITY)
        self.animShow.setDirection(self.animShow.Direction.Forward)
        self.animShow.finished.connect(self.onShowAnimationFinished)
        self.animShow.start()

    def startHideAnimation(self):
        """TODO - why is the valuechanged for hiding not passing a float"""
        self.effect.setOpacity(0)

    def onShowAnimationFinished(self):
        self.animShow.finished.disconnect(self.onShowAnimationFinished)
        anim = QVariantAnimation(self.hint)
        anim.setDuration(1500) # Show time before hiding again
        anim.finished.connect(self.startHideAnimation)
        anim.start()


class ZoomSlider(QWidget):

    def __init__(self, parent: QGraphicsView):
        super().__init__(parent)
        self.setLayout(QVBoxLayout())
        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("plus.svg"))
        pb.clicked.connect(self.onZoomIn)
        self.layout().addWidget(pb)
        self.slider = QSlider()
        self.slider.valueChanged.connect(self.onSlider)
        self.layout().addWidget(self.slider)
        pb = QPushButton("")
        applyDropShadowEffect(pb, offset=(0, 1))
        pb.setIcon(get_resource_qicon("minus.svg"))
        pb.clicked.connect(self.onZoomOut)
        self.layout().addWidget(pb)
        self.setFixedSize(32, 144)
        self.setObjectName("zoomSlider")

    @property
    def scaleDelta(self):
        return self.parent().scaleDelta

    @property
    def minScale(self):
        return self.parent().minScale

    @property
    def maxScale(self):
        return self.parent().maxScale

    def update(self):
        geom = self.parent().geometry()
        margin = 16
        self.move(geom.width()-self.width()-margin,
                  geom.height()-self.height()-margin)
        self.slider.setMaximum(self.maxScale * 100)
        self.slider.setMinimum(self.minScale * 100)
        self.slider.setValue(self.scaleDelta * 100)

    def onZoomIn(self):
        self.parent().zoomIn()

    def onZoomOut(self):
        self.parent().zoomOut()

    def onSlider(self):
        scale = self.slider.value() / 100
        self.parent().scaleDelta = scale


class PdfViewer(QGraphicsView):
    """PDF viewer and editor"""

    sgnLoadPdf = Signal(str)
    sgnPdfLoaded = Signal(bool) # Success (True) / Fail (False)
    sgnRoiAdd = Signal(object, object, object)  # (QPos, QPos, callback)
    sgnRoiAdded = Signal(object)
    sgnRoiRemove = Signal(object)
    sgnRoiDrawChanged = Signal(bool) # Enabled/Disabled (True/False)
    sgnSetContextMenu = Signal(object)
    sgnPdfChanged = Signal()
    sgnUpdateStatus = Signal(str, object)
    sgnZoomUpdated = Signal(float)
    sgnShowEditRoiDialog = Signal(object, bool)
    sgnUpdateCursor = Signal(object)
    sgnRoiModified = Signal()
    sgnPageChanged = Signal(int)
    roiHovered = Signal()
    roiResized = Signal(object) # RoiItem
    roiRemoved = Signal(str) # ROI name
    roiUpdated = Signal(object, dict) # ROI name

    roiMotionListener = None

    # dpi = 1500
    dpi = 144

    maxScale = 4
    minScale = 0.1

    def __init__(self, parent):
        super().__init__()
        self.setScene(PdfScene())
        self.parent = parent
        self.pages: int = None
        self.page = None
        self.pageWidth = None
        self.pageHeight = None
        self._roiDrawEnabled: bool = False
        self.roiStart, self.roiEnd = None, None
        self.previewRoi: RoiItem = None
        self._scaleDelta: float = 1
        self.currentPdfPath = None
        self._currHoverRegion = None
        self._currHoverRoi = None
        self._currRegion = None

        self.zoomSlider = ZoomSlider(self)
        self.zoomSlider.move(500,500)
        self.zoomSlider.show()
        self.sgnRoiDrawChanged.connect(self.zoomSlider.setHidden)

        self.overlayHint = OverlayHint(self)
        self.overlayHint.show()

        self.setDragMode(self.DragMode.NoDrag)
        self.sgnLoadPdf.connect(self.loadPdf)
        self.setMouseTracking(True)
        self.setEnabled(False)
        self.show()

        # Signals
        self.sgnUpdateStatus.connect(self.onUpdateStatus)
        self.sgnUpdateCursor.connect(lambda x: self.setCursor(QCursor(x)))
        self.sgnRoiRemove.connect(self.onRoiRemove)
        self.sgnRoiAdd.connect(self.addRoi)
        self.roiHovered.connect(self.onRoiHovered)

        self.setObjectName("pdfViewer")

        # Dialogs
        self.sgnShowEditRoiDialog.connect(self.onShowEditRoiDialog)
        self.editRoiDialog = None


    @property
    def scaleDelta(self) -> float:
        try:
            return self._scaleDelta
        except Exception as e:
            logger.error(f"Error adjusting scale: {e}", exc_info=True)

    @scaleDelta.setter
    def scaleDelta(self, value):
        try:
            self._scaleDelta = value
            self.resetTransform()
            self.scale(self._scaleDelta, self._scaleDelta)
        except Exception as e:
            logger.error(f"Error adjusting scale: {e}", exc_info=True)

    @property
    def roiDrawEnabled(self):
        return self._roiDrawEnabled

    @roiDrawEnabled.setter
    def roiDrawEnabled(self, enabled: bool):
        try:
            self._roiDrawEnabled = enabled
            self.sgnRoiDrawChanged.emit(enabled)
        except Exception as e:
            logger.error(f"Error changing ROI draw enabled state to {enabled}: {e}", exc_info=True)

    def onUpdateStatus(self, key, object):
        self.parent.setStatusKey(key, object)

    def convertToPdfPoint(self, point: QPoint) -> QPoint:
        """ Returns a point given scrolled offset position of the viewer """
        try:
            point = QPoint(int(point.x()), int(point.y()))
            pos = self.mapToScene(point)
            return QPoint(int(pos.x()), int(pos.y()))
        except Exception as e:
            logger.error(f"Error converting screen point to PDF point: {e}", exc_info=True)

    def getRoisContainPos(self, pos: QPoint) -> list:
        """ Return rois which contain the given position """
        try:
            rois = []
            buffer = 3 * (self.dpi // 72)
            for r in self.getRois():
                # Check over the bounds slightly for better mouse reactivity
                rect = r.boundingRect()
                rect.setX(rect.x() - buffer)
                rect.setY(rect.y() - buffer)
                rect.setWidth(rect.width() + (2 * buffer))
                rect.setHeight(rect.height() + (2 * buffer))
                if rect.contains(pos):
                    rois.append(r)
            return rois
        except Exception as e:
            logger.error(f"Error finding ROIs containing position {pos}: {e}", exc_info=True)

    def mouseMoveEvent(self, event: QMouseEvent) -> None:

        super().mouseMoveEvent(event) # TODO - Review if safe to keep this

        if self.dragMode() == self.DragMode.ScrollHandDrag:
            return super().mouseMoveEvent(event)
        try:
            pos = self.convertToPdfPoint(event.pos())
            self.sgnUpdateStatus.emit("pos", pos)
            currHovered: RoiItem = None
            for r in self.getRois():
                if r.hovered:
                    currHovered = r
                if r.boundingRect().contains(pos):
                    r.hovered = True
                    currHovered = r
                else:
                    r.hovered = False

            if currHovered:
                hoverText = f"ROI: {currHovered.displayName}"
                # hoverText += f"\nColumns: {', '.join(currHovered.displayColumnNames)}"
                self._currHoverRoi = hoverText
            else:
                self._currHoverRoi = None

            self.roiHovered.emit()

            if not self.roiDrawEnabled and self.roiMotionListener and self.roiMotionListener():
                self.roiMotionListener().mouseMoveEvent(event)
                return
            # Resizing the live roi preview as mouse moves
            if self.roiStart and not self.roiEnd:
                if not self.previewRoi:

                    def roiAddedCb(roi):
                        self.previewRoi = roi

                    self.sgnRoiAdd.emit(self.roiStart, pos, roiAddedCb)
                else:
                    a, b = self.roiStart, pos
                    b.setX(max(0, b.x()))
                    b.setX(min(b.x(), self.pageWidth))
                    b.setY(max(0, b.y()))
                    b.setY(min(b.y(), self.pageHeight))
                    self.previewRoi.sgnResizeBounds.emit(QRectF(a, b))
        except Exception as e:
            logger.error(f"Error processing mouse move event: {e}", exc_info=True)

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        try:
            if event.button() == Qt.MouseButton.MiddleButton:
                self.setDragMode(QGraphicsView.DragMode.NoDrag)
                return
            selected = None
            for r in self.getRois():
                if r.selected:
                    selected = r
                    break
            if event.button() == Qt.MouseButton.LeftButton:
                # Disable dragging
                if selected:
                    # ROI has resized if current mouse pos is different to starting point
                    if selected._dragStart and selected._dragStart != event.pos():
                        self.roiResized.emit(selected)
                    selected._dragging = False
                    selected._dragSide = None
                    selected._dragStart = None
                    selected.correctCorners()
        except Exception as e:
            logger.error(f"Error during mouse release: {e}", exc_info=True)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        try:
            if self.editRoiDialog:
                self.sgnUpdateCursor.emit(Qt.CursorShape.ArrowCursor)
                return
            # Allow panning only if not currently drawing roi
            if event.button() == Qt.MouseButton.MiddleButton and not self.roiDrawEnabled:
                self.setDragMode(QGraphicsView.DragMode.ScrollHandDrag)
                self.overlayHint.sgnSetHint.emit("Drag mouse to pan")
                handmade_event = QMouseEvent(
                    QEvent.Type.MouseButtonPress,
                    QPointF(event.pos()), Qt.MouseButton.LeftButton,
                    event.buttons(), event.modifiers())
                return super().mousePressEvent(handmade_event)

            pos = self.convertToPdfPoint(event.pos())
            rois = self.getRoisContainPos(pos)
            selected = None
            for r in rois:
                if r.selected:
                    selected = r
                    break
            if event.button() == Qt.MouseButton.LeftButton:
                # No ROI is clicked, so deselect if applicable
                if not self.roiDrawEnabled and not rois:
                    for r in self.getRois():
                        r.deselect()
                    return
                # If user presses an already selected ROI, enable
                # dragging
                if not self.roiDrawEnabled and selected:
                    selected._dragging = True
                    return
                # Start region selection
                if self.roiDrawEnabled and not self.roiStart:
                    if (pos.x() < 0 or pos.y() < 0 or pos.x() > self.pageWidth - 8
                        or pos.y() > self.pageHeight - 8):
                        return
                    self.roiStart = pos
                elif self.roiStart:
                    # Region selection finished
                    # Keep end position within PDF page
                    pos.setX(max(0, min(self.pageWidth, pos.x())))
                    pos.setY(max(0, min(self.pageHeight, pos.y())))
                    self.roiEnd = pos
                    self.sgnRoiRemove.emit(self.previewRoi)

                    def roiAddedCb(roi):
                        self.previewRoi = None
                        self.roiStart, self.roiEnd = None, None
                        # Configure and confirm selection
                        self.sgnShowEditRoiDialog.emit(roi, False)

                    self.sgnRoiAdd.emit(self.roiStart, self.roiEnd, roiAddedCb)

                elif not selected:
                    # Check if any roi's can be selected
                    # Create the context menu
                    menu = QMenu(None)
                    menu.addAction("Select ROI")
                    if rois:
                        if len(rois) > 1:
                            r: RoiItem = None
                            for r in rois:
                                if r == selected:
                                    continue
                                action = menu.addAction(r.displayName)
                                action.triggered.connect(r.select)
                            self.sgnSetContextMenu.emit(menu)
                        else:
                            rois[0].selected = True
            elif event.button() == Qt.MouseButton.RightButton:

                # Right click on ROI auto-select
                menu = QMenu(None)
                if rois and not selected:
                    if len(rois) > 1:
                        menu = QMenu(None)
                        menu.addAction("Select ROI")
                        r: RoiItem = None
                        for r in rois:
                            if r == selected:
                                continue
                            action = menu.addAction(r.displayName)
                            action.triggered.connect(r.select)
                        self.sgnSetContextMenu.emit(menu)
                    else:
                        rois[0].selected = True
                        selected = rois[0]

                if selected:
                    menu.addSeparator().setText(f"ROI: {selected.name}")
                    menu.addSeparator().setText(" ")
                    action = menu.addAction("Edit")
                    action.triggered.connect(lambda: self.sgnShowEditRoiDialog.emit(selected, True))
                    action = menu.addAction("Remove")
                    action.triggered.connect(lambda: self.sgnRoiRemove.emit(selected))
                    menu.addSeparator().setText(" ")

                if not rois:
                    return
                if len(rois) > 1:
                    sub = menu.addMenu("Select ROI")
                    r: RoiItem = None
                    for r in rois:
                        if r == selected:
                            continue
                        action = sub.addAction(r.displayName)
                        action.triggered.connect(r.select)
                else:
                    pass

                self.sgnSetContextMenu.emit(menu)

            else:
                # Mouse input other than primary button cancels the region
                # selection
                self.roiStart, self.roiEnd = None, None
                if self.previewRoi:
                    self.scene().sgnRemoveItem(self.previewRoi)
                    self.previewRoi = None
        except Exception as e:
            logger.error(f"Error during mouse press: {e}", exc_info=True)

    def hideScrollbars(self):
        try:
            v = Qt.ScrollBarPolicy.ScrollBarAlwaysOff
            self.setVerticalScrollBarPolicy(v)
            self.setHorizontalScrollBarPolicy(v)
        except Exception as e:
            logger.error(f"Error hiding scrollbars: {e}", exc_info=True)

    def showScrollbars(self):
        try:
            v = Qt.ScrollBarPolicy.ScrollBarAlwaysOn
            self.setVerticalScrollBarPolicy(v)
            self.setHorizontalScrollBarPolicy(v)
        except Exception as e:
            logger.error(f"Error showing scrollbars: {e}", exc_info=True)

    def closePdf(self):
        try:
            self._pdf = None
            self.roiMotionListener = None
            self.scene().clear()
            self.currentPdfPath = None
        except Exception as e:
            logger.error(f"Error closing PDF: {e}", exc_info=True)

    def setPage(self, page: int):
        try:
            if page < 1:
                return
            if page > len(self._pdf):
                return
            self.page = page
            self.sgnPageChanged.emit(page)
            zoom = self.dpi / 72  # Default DPI in PyMuPDF is 72
            mat = fitz.Matrix(zoom, zoom)
            fitzPage = self._pdf.load_page(page - 1)  # Load the first page
            pix = fitzPage.get_pixmap(matrix=mat) # dpi=1200
            qt_img = QImage(pix.samples, pix.width, pix.height, pix.stride, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qt_img)
            self.pageWidth, self.pageHeight = pix.width, pix.height
            self.setScene(PdfScene())
            self.pageItem = self.scene().addPixmap(pixmap)
            self.pageItem.setZValue(0)

            self.infoGroup = None

        except Exception as e:
            logger.error(f"Error setting page to {page}: {e}", exc_info=True)

    def loadPdf(self, filename, page=0):
        try:
            self.closePdf()
            if not os.path.exists(filename):
                raise FileNotFoundError(f"Source {filename} not found")
            self._pdf = fitz.open(filename)
            self.pages = self._pdf.page_count if self._pdf.page_count else None
            #Store The successfully loaded PDF Path #Added
            self.currentPdfPath = filename

            # Update UI to allow PDF editing
            self.sgnPdfLoaded.emit(True)

        except FileNotFoundError as e:
            QMessageBox.information(self, "Source not found", str(e))
            self.sgnPdfLoaded.emit(False)
        except Exception as e:
            logger.error(f"Failed to load PDF: {filename}. Error: {e}", exc_info=True)
            QMessageBox.information(self, "Load source error", f"Could not open {filename}")
            self.sgnPdfLoaded.emit(False)

    def addRoi(self, start: QPoint, end: QPoint, callback) -> RoiItem:
        """Signal handler, do not call directly"""
        try:
            roi: RoiItem = RoiItem(start, end, self.scene())
            roi.setZValue(1)
            roi.boundsChanged.connect(lambda: self.sgnRoiModified.emit())
            roi.updated.connect(lambda x, y: self.roiUpdated.emit(x, y))
            roi.requestMotionListener.connect(self.onRoiMotionListenerRequested)
            roi.mouseMoved.connect(self.onRoiMouseMoved)
            roi.cursorUpdated.connect(self.onRoiCursorUpdated)
            callback(roi)
        except Exception as e:
            logger.error(f"Error adding ROI with values {start} - {end}: {e}", exc_info=True)

    def getRois(self) -> list[RoiItem]:
        try:
            rois = []
            c: QGraphicsRectItem
            for c in self.scene().items():
                if isinstance(c, RoiItem):
                    rois.append(c)
            return rois
        except Exception as e:
            logger.error(f"Error retrieving ROIs: {e}", exc_info=True)

    def setSelectRegionMode(self, enabled: bool):
        try:
            # Allow region mode only if not currently panning
            if (self.dragMode() != self.DragMode.ScrollHandDrag) and enabled:
                    self.sgnUpdateCursor.emit(Qt.CursorShape.CrossCursor)
                    self.roiDrawEnabled = True
            else:
                self.sgnUpdateCursor.emit(Qt.CursorShape.ArrowCursor)
                self.roiDrawEnabled = False
        except Exception as e:
                logger.error(f"Error setting region mode: {e}", exc_info=True)

    def clearRoi(self):
        """Remove all items i.e. above the PDF z-level"""
        try:
            c: QGraphicsRectItem
            for c in self.scene().items():
                if c.zValue() > 0:
                    self.sgnRoiRemove.emit(c)
        except Exception as e:
            logger.error(f"Error clearing ROIs: {e}", exc_info=True)

    def resizeEvent(self, event: QResizeEvent) -> None:
        try:
            self.resetTransform()
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.update()
        except Exception as e:
            logger.error(f"Error handling resize event: {e}", exc_info=True)

    def fitPage(self):
        try:
            """Fit page to available client space"""
            clientSize = self.viewport().size()
            self.resetTransform()
            sx = clientSize.width() / self.pageWidth
            sy = clientSize.height() / self.pageHeight
            sd = min(sx, sy) - 0.02  # Slight margin
            self.scaleDelta = max(0, sd)
        except Exception as e:
            logger.error(f"Error fitting page: {e}", exc_info=True)

    def zoomIn(self):
        try:
            self.resetTransform()
            self.scaleDelta += 0.03
            self.scaleDelta = min(self.maxScale, self.scaleDelta)
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.sgnSetHint.emit("Zooming In")
        except Exception as e:
            logger.error(f"Error zooming in: {e}", exc_info=True)

    def zoomOut(self):
        try:
            self.resetTransform()
            self.scaleDelta -= 0.03
            self.scaleDelta = max(self.minScale, self.scaleDelta)
            self.scale(self.scaleDelta, self.scaleDelta)
            self.zoomSlider.update()
            self.overlayHint.sgnSetHint.emit("Zooming Out")
        except Exception as e:
            logger.error(f"Error zooming out: {e}", exc_info=True)

    def onShowEditRoiDialog(self, roi: RoiItem, edit: bool):
        """ edit: if False, is a new ROI """
        try:
            excludes = [roi.name for roi in self.getRois()]
            self.editRoiDialog = dlg = EditRoiDialog(self, roi, excludeNames=excludes)
            resp = dlg.exec()
            self.setSelectRegionMode(False)
            if not edit:  # A new ROI
                if resp:
                    self.sgnRoiAdded.emit(roi)
                else:
                    # User cancelled, remove the ROI
                    self.sgnRoiRemove.emit(roi)
            else:  # Editing existing
                if resp:
                    roi.sgnUpdated.emit()
            if resp:
                self.sgnRoiModified.emit()
            dlg.close()
            self.editRoiDialog = None
        except Exception as e:
            action = "editing" if edit else "adding"
            logger.error(f"Error {action} ROI dialog for ROI: {roi.name if roi else 'Unknown'}. Error: {e}", exc_info=True)

    def onRoiRemove(self, roi: RoiItem):
        """Signal handler for removing roi"""
        try:
            if isinstance(roi, RoiItem):
                # Stop motion listening if ROI is currently monitored
                if self.roiMotionListener and self.roiMotionListener() == roi:
                    self.roiMotionListener = None
                self.scene().removeItem(roi)
                roi.sgnRemoved.emit()
                self.roiRemoved.emit(roi.name)
        except Exception as e:
            logger.error(f"Error removing ROI. Error: {e}", exc_info=True)

    def onRoiMotionListenerRequested(self, roi: RoiItem):
        """Set the active ROI to listen to"""
        self.roiMotionListener = weakref.ref(roi)

    def onRoiMouseMoved(self, roi: RoiItem, event: QMouseEvent):
        # Keep static arrow cursor when edit dialog is open
        if self.editRoiDialog:
            self.sgnUpdateCursor.emit(Qt.CursorShape.ArrowCursor)
            return
        pos = self.convertToPdfPoint(event)
        roi.updateMousePos(pos, event.buttons())

    def onRoiCursorUpdated(self, cursor: QCursor):
        # Keep static arrow cursor when edit dialog is open
        self.setCursor(cursor)

    def drawRegion(self, start, end):
        try:
            roi = self.scene().addRect(QRectF(start, end))
            roi.setZValue(99)
        except Exception as e:
            logger.error(f"Error adding ROI with values {start} - {end}: {e}", exc_info=True)

    def drawRegions(self, regions, color: QColor = "lightgreen"):
        pen = QPen()
        pen.setColor(QColor(color))
        pen.setWidth(2)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)

        brush = QBrush()
        fill = pen.color()
        fill.setAlphaF(0.1)
        brush.setColor(fill)
        brush.setStyle(Qt.BrushStyle.SolidPattern)

        if not self.infoGroup:
            self.infoGroup = QGraphicsItemGroup()
            self.infoGroup.setHandlesChildEvents(False) # Propogate hover to child items
            self.scene().addItem(self.infoGroup)
            self.infoGroup.setZValue(99)

        n = 1
        for start, end, value in regions:
            try:
                item = HoverRectItem(start, end, value)
                item.hovered.connect(partial(self.onRegionHovered, value))
                n += 1
                self.scene().addItem(item)
                self.infoGroup.addToGroup(item)
                item.setPen(pen)
                item.setBrush(brush)
            except Exception as e:
                logger.error(f"Error adding ROI with values {start} - {end}: {e}", exc_info=True)

    def clearRegions(self):
        try:
            self.scene().removeItem(self.infoGroup)
            self.infoGroup = None
        except:
            pass

    def onRegionHovered(self, value: str, hovered: bool):
        if hovered:
            self._currHoverRegion = value
        else:
            self._currHoverRegion = None
        self.roiHovered.emit()

    def onRoiHovered(self):
        if any([
                self.dragMode() != self.DragMode.NoDrag,
            ]):
            QToolTip.hideText()
            return
        if not self._currHoverRegion and not self._currHoverRoi:
            QToolTip.hideText()
            return

        text = ""
        if self._currHoverRoi:
            text += self._currHoverRoi
            if self._currHoverRegion:
                text += "\n"
        if self._currHoverRegion:
            text += "Text: " + self._currHoverRegion

        QToolTip.showText(QCursor.pos(), f"{text}")

class BlueprintReaderView(QWidget):

    sgnProjectDataSync = Signal(dict)
    extracted = Signal(object)
    enableExtraction = Signal(bool)
    drawingAnalyzed = Signal(object)
    preprocessed = Signal(object)
    tasksChanged = Signal()

    def __init__(self, parent):
        super().__init__(parent=parent)
        self.fieldMap = {}
        self._currSourceData = None
        self._attemptFallback: bool = False
        self._taskRunning: bool = False
        self._currentTask: str = None
        self._lock: Lock = Lock()
        self.raw_df: pd.DataFrame = None

        self.updateProjectPopup: UpdateProjectSourcePopup = None
        self.isometricRegionPopup: IsometricRegionPopup = None  # Dialog

        # Fetch Field Map
        self.getFieldMap()

        self.setLayout(QVBoxLayout())
        self.layout().setSpacing(0)
        self.layout().setContentsMargins(0, 0, 0, 0)

        # Siderbar and PDF Viewer
        self.splitter = QSplitter(self)
        self.splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.splitter.setHandleWidth(1)
        self.splitter.setContentsMargins(0, 0, 0, 0)
        self.layout().addWidget(self.splitter)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)

        centerWidget = QWidget()
        centerWidget.setLayout(QVBoxLayout())
        centerWidget.layout().setContentsMargins(0, 0, 0, 0)
        centerWidget.layout().setSpacing(0)
        centerWidget.setObjectName("background")

        # Top toolbar
        self.hboxTop = QWidget()
        self.hboxTop.setContentsMargins(0, 0, 0, 0)
        self.hboxTop.setObjectName("panel")
        self.hboxTop.setStyleSheet("")
        self.hboxTop.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.hboxTop.setLayout(QHBoxLayout())
        self.hboxTop.layout().setSpacing(6)

        # self.pbMenu = MyButton(text="", style=MyButton.BUTTON_MENU_STYLE)
        # self.pbMenu.setIcon(get_resource_qicon("menu.svg"))
        # menu = QMenu(self.pbMenu)
        # menu.addAction("Add Source")
        # menu.addAction("Close")
        # self.pbMenu.setMenu(menu)
        # self.pbMenu.setFixedHeight(36)
        # self.pbMenu.setFixedWidth(36)
        # self.hboxTop.layout().addWidget(self.pbMenu)

        self.cboxSources = CustomComboBox()
        self.cboxSources.setEditable(False)
        self.cboxSources.setMinimumWidth(280)
        self.cboxSources.setFixedHeight(42)
        self.cboxSources.addItem("C:\Test.png")
        self.cboxSources.contextMenuRequested.connect(self.onCboxSourcesContextMenuRequested)
        self.cboxSources.view().setMinimumWidth(600)

        self.cboxSources.activated.connect(self.onCboxSources)

        self.hboxTop.layout().addWidget(self.cboxSources)

        self.pbPreprocess = BusyButton("Preprocess")
        self.pbPreprocess.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.MinimumExpanding)
        self.pbPreprocess.setFixedHeight(self.cboxSources.height())
        # self.pbPreprocess.setMinimumWidth(72)
        self.pbPreprocess.setFixedWidth(96)
        self.pbPreprocess.clicked.connect(self.onPreprocess)
        self.hboxTop.layout().addWidget(self.pbPreprocess)
        # self.pbPreprocess.setAlert("")

        self.pbExtract = BusyButton("Extract")
        self.pbExtract.setFixedHeight(self.cboxSources.height())
        self.pbExtract.setMinimumWidth(96)
        self.pbExtract.setMaximumWidth(108)
        self.pbExtract.clicked.connect(self.onExtractionClicked)
        self.hboxTop.layout().addWidget(self.pbExtract)

        # self.pbDetectWelds = BusyButton("Welds")
        # self.pbDetectWelds.setFixedHeight(self.cboxSources.height())
        # self.pbDetectWelds.setMinimumWidth(96)
        # self.pbDetectWelds.setMaximumWidth(108)
        # self.pbDetectWelds.clicked.connect(self.onExtractionClicked)
        # self.hboxTop.layout().addWidget(self.pbDetectWelds)

        self.hboxTop.layout().addStretch()

        spacer = QSpacerItem(20, 40, QSizePolicy.Policy.MinimumExpanding, QSizePolicy.Policy.MinimumExpanding)
        self.hboxTop.layout().addItem(spacer)

        self.pbRoiLayout = MyButton(" Layout: None  ")
        # self.pbRoiLayout.setObjectName("MyButtonSecondary")
        self.pbRoiLayout.setMinimumSize(108, 42)
        self.pbRoiLayout.setIconSize(QSize(16,16))
        self.pbRoiLayoutMenu = menu = QMenu(self.pbRoiLayout)
        self.actRoiLinkLocalProject = menu.addAction("Link Layout To This Project")
        self.actRoiLinkLocalProject.setVisible(False)
        self.actRoiNew = menu.addAction("New")
        self.actRoiEdit = menu.addAction("Edit Properties")
        self.actRoiEdit.setVisible(False)
        self.actRoiSave = menu.addAction("Save")
        self.actRoiSaveAs = menu.addAction("Save As...")
        self.actRoiSave2 = menu.addAction("Save To Active Project")
        self.actRoiSave2.setVisible(False)
        self.actRoiDuplicate = menu.addAction("Duplicate")
        self.actRoiDelete = menu.addAction("Delete")
        menu.triggered.connect(self.onRoiLayoutMenu)
        self.pbRoiLayout.setMenu(menu)
        self.pbRoiLayout.clicked.connect(self.onRoiLayoutClicked)
        self.hboxTop.layout().addWidget(self.pbRoiLayout)

        self._currentLayoutModified: bool = None
        self._currentLayoutData: dict = None
        self.cboxRoiPresets: CustomFilterComboBox = CustomFilterComboBox(self, RoiLayoutFilter)
        self.cboxRoiPresets.setEditable(True)
        self.cboxRoiPresets.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.cboxRoiPresets.setMaximumHeight(42)
        self.cboxRoiPresets.currentIndexChanged.connect(self.onRoiPresetChanged)
        self.cboxRoiPresets.lineEdit().returnPressed.connect(self.onRoiPresetReturnPressed)
        self._currentPresetIndex = None
        self.hboxTop.layout().addWidget(self.cboxRoiPresets)

        self.popupHelp = None
        self.pbHelp = QPushButton("")
        self.pbHelp.setContentsMargins(8, 2, 8, 2)
        self.pbHelp.setFixedSize(24, 24)
        self.pbHelp.setIcon(get_resource_qicon("help-circle.svg"))
        self.pbHelp.setIconSize(QSize(18,18))
        self.pbHelp.setCheckable(True)
        self.pbHelp.leaveEvent = self.onHelpLeave
        self.pbHelp.enterEvent = self.onHelpEnter
        self.hboxTop.layout().addWidget(self.pbHelp)
        self.pbHelp.hide()

        spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        centerWidget.layout().addWidget(self.hboxTop)

        self.pageToolbar = QWidget()
        self.pageToolbar.setObjectName("panel")
        self.pageToolbar.setLayout(QHBoxLayout())
        self.pageToolbar.setFixedHeight(46)
        centerWidget.layout().addWidget(self.pageToolbar)

        self.pdfViewer = PdfViewer(self)
        centerWidget.layout().addWidget(self.pdfViewer)

        self.lblCurrentGroup = QLabel("Group: n/a")
        self.pageToolbar.layout().addWidget(self.lblCurrentGroup)

        self.pbPrevPage = QPushButton(" ")
        applyDropShadowEffect(self.pbPrevPage)
        self.pbPrevPage.setIcon(get_resource_qicon("chevron-left.svg"))
        self.pbPrevPage.setMinimumHeight(24)
        self.pageToolbar.layout().addWidget(self.pbPrevPage)
        self.pbPrevPage.clicked.connect(self.previousPage)

        self.lePage = QLineEdit(" ")
        self.lePage.setObjectName("pager")
        self.pageToolbar.layout().addWidget(self.lePage)
        self.lePage.setFixedHeight(self.pageToolbar.height()-2)
        # self.lePage.setFixedHeight(24)
        self.lePage.setFixedWidth(48)
        self.lePage.returnPressed.connect(self.onChangePage)

        self.lblPage = QLabel("")
        self.lblPage.hide()
        self.pageToolbar.layout().addWidget(self.lblPage)

        self.pbNextPage = QPushButton(" ")
        applyDropShadowEffect(self.pbNextPage)
        self.pbNextPage.setIcon(get_resource_qicon("chevron-right.svg"))
        self.pbNextPage.setMinimumHeight(24)
        self.pageToolbar.layout().addWidget(self.pbNextPage)
        self.pbNextPage.clicked.connect(self.nextPage)

        self.chkExtract = QCheckBox("Extract")
        self.chkExtract.setCheckable(True)
        self.chkExtract.setEnabled(False)
        self.chkExtract.clicked.connect(self.onPageOptionChecked)
        self.pageToolbar.layout().addWidget(self.chkExtract)

        self.chkApplyOcr = QCheckBox("Apply OCR")
        self.chkApplyOcr.setCheckable(True)
        self.chkApplyOcr.setEnabled(False)
        self.chkApplyOcr.clicked.connect(self.onPageOptionChecked)
        self.pageToolbar.layout().addWidget(self.chkApplyOcr)

        # self.pbMissingRegions = MyButton(text=" Missing Regions: N/A ")
        self.pbMissingRegions = QPushButton(text=" Missing Regions: N/A ")
        self.pbMissingRegions.setFixedHeight(self.pageToolbar.height()-16)
        self.pbMissingRegions.setCheckable(True)
        self.pbMissingRegions.clicked.connect(self.refreshDisplayedRegions)
        self.pageToolbar.layout().addWidget(self.pbMissingRegions)

        # self.pbOcrRegions = QPushButton(text=" OCR Regions")
        # self.pbOcrRegions.setFixedHeight(self.pageToolbar.height()-16)
        # self.pbOcrRegions.setCheckable(True)
        # self.pbOcrRegions.clicked.connect(self.refreshOcrRegions)
        # self.pageToolbar.layout().addWidget(self.refreshOcrRegions)

        self.pbSyncLayout = QPushButton(text="Sync Layout")
        self.pbSyncLayout.setCheckable(True)
        self.pbSyncLayout.setMinimumSize(108, self.pageToolbar.height()-16)
        self.pbSyncLayout.setEnabled(False)
        self.pageToolbar.layout().addWidget(self.pbSyncLayout)


        self.pbRoiExport = QPushButton(text="Export ROI Layout")
        self.pbRoiExport.clicked.connect(self.onMultiRoiExport)
        self.pbRoiExport.setMinimumSize(108, self.pageToolbar.height()-16)
        self.pageToolbar.layout().addWidget(self.pbRoiExport)

        self.pbRoiImport = QPushButton(text="Import ROI Layout")
        self.pbRoiImport.clicked.connect(self.onMultiRoiImport)
        self.pbRoiImport.setMinimumSize(108, self.pageToolbar.height()-16)
        self.pageToolbar.layout().addWidget(self.pbRoiImport)

        self.pbIsometricRegion = QPushButton(text="Isometric Region")
        # self.pbIsometricRegion.clicked.connect(self.onIsometricRegion)
        self.pbIsometricRegion.setMinimumSize(108, self.pageToolbar.height()-16)
        # self.pageToolbar.layout().addWidget(self.pbIsometricRegion)

        self.syncLayoutMenu = QMenu(None)
        widgetAction: QWidgetAction = QWidgetAction(self.syncLayoutMenu)

        self.syncLayoutPopup = SyncLayoutPopup(self.syncLayoutMenu)
        self.syncLayoutPopup.applied.connect(self.onSyncLayoutApplied)
        widgetAction.setDefaultWidget(self.syncLayoutPopup)
        self.syncLayoutMenu.addAction(widgetAction)

        self.pbSyncLayout.setMenu(self.syncLayoutMenu)

        self.pageToolbar.layout().addStretch()

        # centerWidget.hide()
        self.splitter.addWidget(centerWidget)

        # box= QWidget()
        # box.setLayout(QVBoxLayout())
        # box.layout().setContentsMargins(0, 0, 0, 0)
        # box.layout().setSpacing(0)
        # pb = QPushButton("test")
        # pb.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # box.layout().addWidget(pb)
        # pb = QPushButton("test2")
        # pb.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # box.layout().addWidget(pb)
        # self.splitter.addWidget(box)

        # Box Content
        rightWidget = QWidget()
        rightWidget.setObjectName("panel")
        rightWidget.setLayout(QVBoxLayout())
        rightWidget.layout().setContentsMargins(12, 12, 12, 12)
        rightWidget.layout().setContentsMargins(0, 0, 0, 0)
        self.tabs = QTabWidget()
        self.tabs.tabBar().setMinimumHeight(32)
        self.tabs.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.tabs.setDocumentMode(True)
        self.tabs.tabBar().setExpanding(True)
        # self.tabs.setObjectName("temp1")
        # self.pageToolbar.setObjectName("temp1")

        # Page Info Tab
        self.pageGroups: PageGroups = PageGroups(self.tabs)
        self.pageGroups.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.tabs.addTab(self.pageGroups, "Page Info")
        rightWidget.layout().addWidget(self.tabs)
        self.splitter.addWidget(rightWidget)

        applyDropShadowEffect(centerWidget, 8, "black")
        applyDropShadowEffect(rightWidget, 8, "grey")
        applyDropShadowEffect(self.tabs, 8, "black")

        # Extraction ROI Tab
        roiWidget = QWidget(self.tabs)
        roiWidget.setLayout(QVBoxLayout())

        hboxRegion = QWidget()
        hboxRegion.setContentsMargins(2, 2, 2, 2)
        hboxRegion.setLayout(QHBoxLayout())
        hboxRegion.layout().addWidget(QLabel("Regions of Interest"))
        # hboxRegion.setMinimumHeight(42)

        self.pbSelectRegion = MyButton(text="Add Region", style=MyButton.BUTTON_PRIMARY_STYLE)
        self.pbSelectRegion.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        self.pbSelectRegion.setCheckable(True)
        self.pbSelectRegion.setMinimumSize(130, 32)
        self.pbSelectRegion.clicked.connect(self.onSelectRegionClicked)
        self.pbSelectRegion.setEnabled(False)
        hboxRegion.layout().addWidget(self.pbSelectRegion)
        roiWidget.layout().addWidget(hboxRegion)

        self.roiSidebar: RoiSidebar = RoiSidebar(roiWidget)
        self.roiSidebar.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        roiWidget.layout().addWidget(self.roiSidebar)
        self.tabs.addTab(roiWidget, "ROI")
        # Extraction Preview Tab
        self.extractionPreview = ExtractionPreview(self.tabs)
        self.tabs.addTab(self.extractionPreview, "Preview")

        self.splitter.setStretchFactor(0, 1)
        self.splitter.setStretchFactor(1, 1)
        self.splitter.setSizes([9999, 1])

        def onSplitterMoved(pos, index):
            """Prevents left sidebar from collapsing"""
            a = self.splitter.widget(0)
            b = self.splitter.widget(1)
            aw = a.width()
            bw = b.width()
            if a.width() < self.tabs.minimumWidth():
                aw = self.tabs.minimumWidth()
            self.splitter.setSizes([aw, bw])
        self.splitter.splitterMoved.connect(onSplitterMoved)

        self.pdfViewer.sgnPdfLoaded.connect(self.onPdfLoaded)
        self.pdfViewer.sgnRoiAdded.connect(self.roiSidebar.onRoiAdded)
        self.pdfViewer.sgnRoiAdded.connect(self.onRoiUpdated)
        self.pdfViewer.sgnRoiDrawChanged.connect(self.onRoiDrawChanged)
        self.pdfViewer.sgnSetContextMenu.connect(self.onSetContextMenu)
        self.pdfViewer.sgnPageChanged.connect(self.updatePageStatus)
        self.pdfViewer.roiResized.connect(self.onRoiUpdated)
        self.pdfViewer.roiRemoved.connect(self.onRoiRemoved)
        self.pdfViewer.roiUpdated.connect(self.onRoiUpdated)
        self.roiSidebar.sgnRoiClearAll.connect(self.onClearRoi)
        self.roiSidebar.sgnPdfSaveRois.connect(self.savePdfRois)
        self.roiSidebar.removeRoi.connect(self.pdfViewer.onRoiRemove)
        self.roiSidebar.extractionClicked.connect(self.onExtractionClicked)

        self.pageGroups.analyzePagesPressed.connect(self.onAnalyzePages)
        self.pageGroups.pageItemSelected.connect(self.onPageSelected)
        self.pageGroups.saveExtractionOptions.connect(self.saveExtractionOptions)
        self.pageGroups.pageDataChanged.connect(self.onPageDataChanged)
        self.pageGroups.restoreDefaultGroups.connect(self.restoreDefaultGroups)
        self.pageGroups.restoreSavedGroups.connect(self.restoreSavedGroups)
        self.pageGroups.groupsModified.connect(self.onGroupsModified)

        # Capture key events without needing to focus the widget
        self.contextMenu = None
        self.pdfViewer.setFocus()

        self.status = {}
        self.updateStatusBar()
        self.updateCurrentLayoutUi()
        self.refreshCboxPresets()

        pub.subscribe(self.loadPdf, "blueprintreader-load-pdf")
        pub.subscribe(self.onJobQueueUpdated, "job-queue-updated")

        self.sgnProjectDataSync.connect(self.roiSidebar.syncProjectData)
        self.sgnProjectDataSync.connect(self.syncProjectData)
        self.enableExtraction.connect(self.setExtractEnabled)
        self.drawingAnalyzed.connect(self.onAnalysisResults)
        self.preprocessed.connect(self.onPreprocessResults)
        self.pageGroups.setPageGroupsFailed.connect(self.onSetPageGroupsFailed)

        self.splitter.handle(0).hide()

        self.updateUi()
        self.tasksChanged.connect(self.updateUi)

    @property
    def projectId(self):
        try:
            return self._projectData["id"]
        except:
            return None

    def setStatusKey(self, key, value):
        try:
            self.status[key] = value
            self.updateStatusBar()
        except Exception as e:
            logger.error(f"Error setting status key: {e}", exc_info=True)

    def updateStatusBar(self):
        try:
            text = []
            pos = self.status.get('pos')
            if (pos and (pos.x() > 0 and pos.x() < self.pdfViewer.pageWidth)
                and (pos.y() > 0 and pos.y() < self.pdfViewer.pageHeight)):
                pos = f"{pos.x()}, {pos.y()}, Rel: {round(pos.x()/self.pdfViewer.pageWidth, 3), round(pos.y()/self.pdfViewer.pageHeight, 3)}"
                text.append(f"Pos: {pos}")
            selected = self.roiSidebar.getSelectedItemRoi()
            if selected:
                rect = selected.bounds.rect()
                text.append(f"Selected: (x={rect.x()}, y={rect.y()}, w={rect.width()}, h={rect.height()})")

            data = { "params": {"Pos": " ".join(text)} }
            pub.sendMessage("set-statusbar-blueprintreader", data=data)
        except Exception as e:
            pass
            # logger.error(f"Error updating status bar: {e}", exc_info=True)

    def centerPage(self):
        return

    def eventFilter(self, source, event):
        """ Handle PDF zoom / scale """
        try:
            if source == self.pdfViewer.viewport():
                if event.type() == QEvent.Wheel:
                    # self.pdfViewer.setTransformationAnchor(QGraphicsView.NoAnchor)
                    # self.pdfViewer.setResizeAnchor(QGraphicsView.NoAnchor)
                    oldPos = self.pdfViewer.mapToScene(self.cursor().pos())
                    if event.angleDelta().y() > 0:
                        self.pdfViewer.zoomIn()
                    else:
                        self.pdfViewer.zoomOut()
                    return True

            return super().eventFilter(source, event)
        except Exception as e:
            logger.error(f"Error filtering events for zoom/scale handling: {e}", exc_info=True)

    def onSetContextMenu(self, menu: QMenu):
        try:
            self.contextMenu = menu
            if not self.contextMenu:
                return
            self.releaseKeyboard()
            # self.contextMenu.aboutToHide.connect(self.grabKeyboard)
            self.contextMenu.exec(self.cursor().pos())
        except Exception as e:
            logger.error(f"Error setting Conext Menu: {e}", exc_info=True)

    def loadPdf(self, data):
        try:
            filename = data["filename"]
            if self.pdfViewer.currentPdfPath != filename:
                self.pdfViewer.sgnLoadPdf.emit(filename)
                self._currSourceData = data
                self.sourceData = data
                for index in range(self.cboxSources.count()):
                    if self.cboxSources.itemData(index, Qt.UserRole)["filename"] == filename:
                        self.cboxSources.setCurrentIndex(index)
                        break
            pub.sendMessage("project-switch-to-view", name="Document Viewer")
        except Exception as e:
            logger.error(f"Error emitting loadpdf signal: {e}", exc_info=True)

    def onRoiDrawChanged(self, enabled):
        self.pbSelectRegion.setChecked(enabled)

    def onSelectRegionClicked(self):
        self.pdfViewer.setSelectRegionMode(self.pbSelectRegion.isChecked())

    def getDefaultGroupState(self, data: pd.DataFrame):
        """Initialize group data format"""
        validPages = {n for n in range(1, self.pdfViewer.pages+1)}
        data = {"groups": {}}
        grouped = data.groupby("group_number")
        for groupNumber, group in grouped:
            groupPages = group["page_number"].unique().to_list()
            groupPages = sorted([p for p in groupPages if p in validPages])
            data["groups"] = {
                groupNumber: {
                    "pages": [{"page": n} for n in groupPages],
                },
            }

    def onPdfLoaded(self, success: bool):
        if success:
            self.pdfViewer.page = None
            self.pbSelectRegion.setEnabled(True)
            self.updatePageStatus()
            self.roiSidebar.clearAll()
            self.extractionPreview.clear()
            try:
                self.pdfViewer.sgnRoiModified.disconnect()
            except: # safe to ignore this
                pass

            self.cboxRoiPresets.setEnabled(True)
            self.refreshCboxPresets()
            self.pdfViewer.sgnRoiModified.connect(self.onRoiModified)

            self._attemptFallback = False
            self.restoreExtractionOptions()
            # After loading extraction options, load group rois
            # for 1st page
            # self.restoreGroupRois()
            self.syncLayoutPopup.clear()
            self.updateSyncOptions()

        self.pdfViewer.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self.pdfViewer.scaleDelta = 1
        self.updateUi()

    def onClearRoi(self):
        self.pdfViewer.clearRoi()

    def keyReleaseEvent(self, event: QKeyEvent) -> None:
        if (event.key() == HK_SELECT_REGION):
            if not self.pdfViewer.roiStart:
                self.pdfViewer.setSelectRegionMode(False)
        elif (event.key() == Qt.Key.Key_Control):
            self.enableZoom(False)

    def keyPressEvent(self, event: QKeyEvent) -> None:
        if (event.key() == HK_SELECT_REGION):
            self.pdfViewer.setSelectRegionMode(True)
        elif (event.key() == Qt.Key.Key_Delete):
            pass
        elif (event.key() == Qt.Key.Key_Control):
            self.enableZoom(True)

    def enableZoom(self, enabled: bool = True):
        """ Scroll wheel override to zoom in/out of PDF. """
        if enabled:
            self.pdfViewer.viewport().installEventFilter(self)
            self.pdfViewer.overlayHint.sgnSetHint.emit("Ctrl + Scroll Up <Zoom In>, Ctrl + Scroll Down <Zoom Out> ")
        else:
            self.pdfViewer.viewport().removeEventFilter(self)

    def resizeEvent(self, event: QResizeEvent) -> None:
        if self.isEnabled():
            self.pdfViewer.resizeEvent(event)
        return super().resizeEvent(event)

    def loadRois(self, roiJson):
        # Restore each ROI
        for roiDict in roiJson:
            self.loadRoi(roiDict)

    def loadRoi(self, roiDict: dict):
        """Load ROI"""

        def roiAddedCb(roi):
            roi.name = roiDict["name"]
            display = self.fieldMap.get(roi.name, {}).get("display", roi.name)
            roi.displayName = display
            roi.setTableMode(roiDict["isTable"])
            roi.headersSelected = roiDict.get("headersSelected", False)
            roi.columnNames = roiDict["columnNames"]
            roi.setColumnLines(ratios=roiDict["columnRatios"])
            roi.setZValue(1)
            self.pdfViewer.sgnRoiAdded.emit(roi)

        try:
            rx0, ry0, rx1, ry1 = roiDict["relativeX0"], roiDict["relativeY0"], roiDict["relativeX1"], roiDict["relativeY1"]
            w, h = self.pdfViewer.pageWidth, self.pdfViewer.pageHeight
            x0, y0, x1, y1 = rx0 * w, ry0 * h, rx1 * w, ry1 * h
            self.pdfViewer.sgnRoiAdd.emit(QPoint(x0, y0), QPoint(x1, y1), roiAddedCb)
        except Exception as e:
            logger.info(f"Failed to restore PDF Roi {roiDict}", exc_info=True)

    def getCurrentPageRois(self) -> list:
        """Return a list<dict> that are displayed on current page"""
        return [roi.getSaveState() for roi in self.roiSidebar.getRois()]

    def loadRoiConfig(self):
        roiJson = {}
        try:
            path = getRoiConfig()
            with open(path) as f:
                roiJson = json.load(f)
        except Exception as e:
            print("Error loading ROI config", e)
            with open(path, 'w') as f:
                json.dump({}, f)

        # Saves us from doing this elsewhere
        roiJson.setdefault("projects", {})
        roiJson.setdefault("global", {})
        roiJson["projects"].setdefault(str(self.projectId), {})
        return roiJson

    def saveRoiConfig(self, data):
        path = getRoiConfig()
        with open(path, 'w') as f:
            json.dump(data, f, indent=4)

    def syncProjectData(self, projectData: dict):
        self.roiSidebar.syncProjectData(projectData)
        self._projectData = projectData
        self.updateSourceList()

    def updateSourceList(self):
        cboxSources = self.cboxSources
        cboxSources.clear()
        filenames = []
        selectedIndex = -1
        tooltip = ""

        # Sort by filename
        for index, d in enumerate(sorted(self._projectData.get("documents", []), key=lambda x: os.path.basename(x["filename"].lower()))):
            fn = d["filename"]
            try:
                filenames.append(fn)
                display = os.path.basename(fn)
            except:
                pass
            cboxSources.addItem(display, userData=d)
            if fn == self.pdfViewer.currentPdfPath:
                selectedIndex = index
                tooltip = f"Filename: {d['filename']}"
                tooltip += f"\nEngineer Drafter: {d['documentVendor']}"

        cboxSources.setCurrentIndex(selectedIndex)
        cboxSources.setToolTip(tooltip)
        if self.pdfViewer.currentPdfPath:
            if self.pdfViewer.currentPdfPath not in filenames:
                self.closePdf()

    def onRoiLayoutClicked(self):
        self.pbRoiLayout.showMenu()
        self.pbRoiLayout.menu().exec()

    def askForInput(self, title, message, placeholder=""):
        # Workaround, increases width of dialog by lengthening message
        message += " " * 64
        dlg = QInputDialog(self)
        dlg.setInputMode(QInputDialog.TextInput)
        lineEdit: QLineEdit = dlg.findChild(QLineEdit)
        dlg.resize(480, 320) # does nothing
        res, ok = dlg.getText(self, title, message, text=placeholder)
        return res, ok

    def onRoiLayoutMenu(self, action):
        modified: bool = False
        if action == self.actRoiNew:
            self.newPdfRois()
        elif action == self.actRoiSave:
            self.savePdfRois()
        elif action == self.actRoiDuplicate:
            self.duplicatePdfRois()
        elif action == self.actRoiSaveAs:
            self.saveAsPdfRois()
        elif action == self.actRoiDelete:
            self.deletePdfRois()
        elif action == self.actRoiLinkLocalProject:
            self.toggleLinkProject()
        elif action == self.actRoiSave2:
            scope = self._currentLayoutData.get("rois", {}).get("scope")
            if scope == "local":
                self.saveLayoutToGlobal()
            else: # linked, global
                self.saveToActiveProject()
        elif action == self.actRoiEdit:
            self.editLayoutProperties()

    def editLayoutProperties(self):
        data = self._currentLayoutData
        name = self._currentLayoutData["name"]
        state = data["rois"]
        documentVendor = state.get("documentVendor", "")
        tags = state.get("tags", [])
        dlg = EditLayoutPropertiesDialog(self, documentVendor, tags)
        res = dlg.exec()
        if not res:
            return
        result = dlg.getValues()
        roiJson = self.loadRoiConfig()
        copyData: dict = deepcopy(self._currentLayoutData)
        state = copyData["rois"]
        scope = state.get("scope")
        # Update values here
        state["documentVendor"] = result["documentVendor"]
        state["tags"] = result["tags"]
        projectId = str(self.projectId)
        # Save changes
        if scope == "local":
            roiJson["projects"][projectId][name] = state
        else:
            roiJson["global"][name] = state
        self.saveRoiConfig(roiJson)
        self._currentLayoutData = {"name": name, "rois": {"scope": scope}}  # For autoselecting after refresh
        self.refreshCboxPresets()
        self.updateCurrentLayoutUi()

    def saveToActiveProject(self):
        """Save a copy of global to active project"""
        projectId = self.projectId
        roiJson = self.loadRoiConfig()
        copyData: dict = deepcopy(self._currentLayoutData)
        name = copyData["name"]
        state = copyData["rois"]
        scope = state.get("scope")
        assert scope in ["global", "linked"]
        try:
            if "linked" in state:
                del state["linked"]
            if "scope" in state:
                del state["scope"]
            # Need a unique name
            newLocalName = name
            exists = False
            names = self.getLocalRoiConfigNames()
            while True:
                message = "Enter name:" if not exists else "Unique local name required:"
                newLocalName, ok = self.askForInput("Save Global Layout To Active Project", message, f"{newLocalName}")
                if not ok:
                    return
                if newLocalName not in names:
                    break
                exists = True
            state["rois"] = self.getCurrentPageRois()
            copyData["rois"] = state
            roiJson["projects"][str(projectId)][newLocalName] = copyData["rois"]
            self.saveRoiConfig(roiJson)
            self._currentLayoutData = {"name": newLocalName, "rois": {"scope": "local"}}  # For autoselecting after refresh
            self.refreshCboxPresets()
            self.updateCurrentLayoutUi()
        except Exception:
            logger.info("Could not save layout to local project", exc_info=True)

    def saveLayoutToGlobal(self):
        """Local project saved globally and removed from local. We autolink this"""
        projectId = self.projectId
        roiJson = self.loadRoiConfig()
        copyData: dict = deepcopy(self._currentLayoutData)
        name = copyData["name"]
        state = copyData["rois"]
        scope = state.get("scope")
        assert scope == "local"
        try:
            if "scope" in state:
                del state["scope"]
            # Need a unique name
            newGlobalName = name
            exists = False
            names = self.getGlobalRoiConfigNames()
            while True:
                message = "Enter name:" if not exists else "Unique global name required:"
                newGlobalName, ok = self.askForInput("Convert Local Layout To Global", message, f"{newGlobalName}")
                if not ok:
                    return
                if newGlobalName not in names:
                    break
                exists = True
            state["rois"] = self.getCurrentPageRois()
            state["linked"] = [projectId]
            copyData["rois"] = state
            roiJson["global"][newGlobalName] = copyData["rois"]

            # Remove from local
            if name in roiJson["projects"][str(projectId)]:
                del roiJson["projects"][str(projectId)][name]

            self.saveRoiConfig(roiJson)
            self._currentLayoutData = {"name": newGlobalName, "rois": {"scope": "linked"}}  # For autoselecting after refresh
            self.refreshCboxPresets()
            self.updateCurrentLayoutUi()
        except Exception:
            logger.info("Could not convert local layout to global project", exc_info=True)

    def toggleLinkProject(self):
        """Link layout to project. Unlink if already linked"""
        projectId = self.projectId
        roiJson = self.loadRoiConfig()
        data = self._currentLayoutData
        name = data["name"]
        try:
            linked = set(roiJson["global"][name].get("linked", []))
            if projectId in linked:
                linked.remove(projectId)
            else:
                linked.add(projectId)
            roiJson["global"][name]["linked"] = list(linked)
            self.saveRoiConfig(roiJson)
            self.refreshCboxPresets()
            self.updateCurrentLayoutUi()
        except:
            logger.info("Could not (un)link layout to project")

    def getGlobalRoiConfigNames(self):
        roiJson = self.loadRoiConfig()
        names = []
        names.extend(roiJson.get("global", {}).keys())
        return names

    def getLocalRoiConfigNames(self):
        projectId = str(self.projectId)
        roiJson = self.loadRoiConfig()
        names = []
        names.extend(roiJson.get("projects", {}).get(projectId).keys())
        return names

    def newPdfRois(self):
        """Create new local layout"""
        documentVendor = self._projectData.get("documentVendor", "New Roi Layout")
        exists = False
        names = self.getLocalRoiConfigNames()
        while True:
            message = "Enter name:" if not exists else "Unique local name required:"
            newName, ok = self.askForInput("Create New ROI Layout", message, f"{documentVendor} Config")
            if not ok:
                return
            if newName not in names:
                break
            exists = True

        projectId = str(self.projectId)
        roiJson = self.loadRoiConfig()
        roiJson.setdefault("projects", {})
        roiJson["projects"].setdefault(projectId, {})
        roiJson["projects"][projectId][newName] = {
            "documentVendor": documentVendor,
            "tags": [],
            "linked": [int(projectId)],
        }
        self.saveRoiConfig(roiJson)
        self._currentLayoutData = {"name": newName, "rois": {"scope": "local"}}  # So we now which to item to select after refresh
        self.refreshCboxPresets()
        self.updateCurrentLayoutUi()

    def savePdfRois(self):
        """Save to local ROI config file"""
        if self._currentPresetIndex is None or self._currentPresetIndex == -1:
            return
        currentData = deepcopy(self.cboxRoiPresets.itemData(self._currentPresetIndex))
        name = currentData["name"]
        # Clear, may not exist
        try:
            del currentData["modified"]
        except:
            pass
        exists = False
        scope = currentData["rois"]["scope"]
        state = self.getCurrentPageRois()
        roiJson = self.loadRoiConfig()
        if scope in ["global", "linked"]:
            roiJson.setdefault("global", {})
            roiJson["global"][name].setdefault("rois", {})
            roiJson["global"][name]["rois"] = state
        else:
            projectId = str(self.projectId)
            roiJson.setdefault("projects", {})
            roiJson["projects"].setdefault(projectId, {})
            roiJson["projects"][projectId].setdefault(name, {})
            roiJson["projects"][projectId][name]["rois"] = state

        self._currentLayoutData = {"name": name, "rois": {"scope": scope}}
        self.saveRoiConfig(roiJson)
        self._currentLayoutModified = False
        self.refreshCboxPresets()
        self._currentLayoutData = currentData
        self.updateCurrentLayoutUi()
        print(f"ROI layout saved: {name}")

    def duplicatePdfRois(self):
        """Duplicate layout"""
        currentData = deepcopy(self.cboxRoiPresets.currentData())
        if not currentData:
            return
        if "name" not in currentData:
            return
        exists = False
        scope = currentData["rois"]["scope"]
        if scope in ["global", "linked"]:
            names = self.getGlobalRoiConfigNames()
            while True:
                message = "Enter name:" if exists else "Unique global name required:"
                newName, ok = self.askForInput("Duplicate ROI Layout", message, f"{currentData['name']} (Copy)")
                if not ok:
                    return
                if newName not in names:
                    break
                exists = True
            roiJson = self.loadRoiConfig()
            roiJson.setdefault("global", {})
            del currentData["rois"]["scope"]
            roiJson["global"][newName] = currentData["rois"]

            self._currentLayoutData = {"name": newName, "rois": {"scope": scope}}

        else: # Local duplicate
            projectId = str(self.projectId)
            names = self.getLocalRoiConfigNames()
            while True:
                message = "Enter name:" if exists else "Unique local name required:"
                newName, ok = self.askForInput("Duplicate ROI Layout", message, f"{currentData['name']} (Copy)")
                if not ok:
                    return
                if newName not in names:
                    break
                exists = True
            roiJson = self.loadRoiConfig()
            roiJson.setdefault("projects", {})
            del currentData["rois"]["scope"]
            roiJson["projects"][projectId][newName] = currentData["rois"]

            self._currentLayoutData = {"name": newName, "rois": {"scope": "local"}}

        self.saveRoiConfig(roiJson)
        self.refreshCboxPresets()
        self.updateCurrentLayoutUi()

    def deletePdfRois(self):
        """Delete layout"""
        currentData = self.cboxRoiPresets.currentData()
        if not currentData:
            return
        name = currentData["name"]
        resp = QMessageBox.question(self, 'Confirm Delete',  f"Delete '{name}'?",
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.Yes)
        if resp == QMessageBox.Yes:
            roiJson = self.loadRoiConfig()
            try:
                scope = currentData["rois"]["scope"]
                if scope in ["global", "linked"]:
                    del roiJson["global"][name]
                else:
                    del roiJson["projects"][str(self.projectId)][name]
                self.saveRoiConfig(roiJson)
                self.roiSidebar.sgnRoiClearAll.emit()
                self.refreshCboxPresets()
            except Exception as e:
                pass
        self._currentPresetIndex = None
        self._currentLayoutModified = None
        self._currentLayoutData = None
        self.refreshCboxPresets()
        self.updateCurrentLayoutUi()

    def saveAsPdfRois(self):
        """Save as new global layout"""
        exists = False
        names = self.getGlobalRoiConfigNames()
        documentVendor = self._projectData.get("documentVendor", "New Roi Layout")
        while True:
            message = "Enter name:" if exists else "Unique name required:"
            newName, ok = self.askForInput("Save ROI Layout As...", message, f"{documentVendor}")
            if not ok:
                return
            if newName not in names:
                break
            exists = True

        roiJson = self.loadRoiConfig()
        roiJson.setdefault("projects", {})
        roiJson.setdefault("global", {})
        roiJson["global"][newName] = {
            "rois": self.getCurrentPageRois(),
            "documentVendor": documentVendor,
            "tags": [],
        }
        print(f"Saved Roi Layout: {newName}")
        self.saveRoiConfig(roiJson)
        self._currentLayoutData = {"name": newName, "rois": {"scope": "global"}}
        self.refreshCboxPresets()
        self.updateCurrentLayoutUi()

    def onRoiPresetReturnPressed(self):
        if not self.pdfViewer.currentPdfPath:
            return
        currentData = self.cboxRoiPresets.currentData()
        currentIndex = self.cboxRoiPresets.currentIndex()
        if not currentData:
            return
        self.cboxRoiPresets.setEnabled(False)
        if "rois" in currentData:
            self.switchToRoiLayout()
        elif "addroi" in currentData:
            self.addRoiToLayout(currentData["addroi"])
        elif "addrois" in currentData:
            self.addRoisToLayout(currentData["addrois"]["rois"])
        self.cboxRoiPresets.setEnabled(True)

    def addRoisToLayout(self, rois):
        for roi in rois:
            self.addRoiToLayout(roi)

    def addRoiToLayout(self, roi):
        """Can only add if not already added"""
        names = self.roiSidebar.getRoisNames()
        if roi["name"] in names:
            QMessageBox.information(self, 'ROI already exists', f'ROI type `{roi["name"]}` already added. Remove existing before adding')
        else:
            self.loadRoi(roi)

    def switchToRoiLayout(self):
        """Need to prompt user to save modified layout before switching"""
        if self._currentPresetIndex is None:
            self._currentPresetIndex = self.cboxRoiPresets.currentIndex()

        lastData = self.cboxRoiPresets.itemData(self._currentPresetIndex)
        if lastData and "modified" in lastData and lastData["modified"]:
            resp = QMessageBox.question(self, 'ROI Layout has unsaved changes',  f'Save changes?',
                                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                                    QMessageBox.Yes)
            if resp == QMessageBox.Yes:
                self.savePdfRois(lastData)
            elif resp == QMessageBox.Cancel:
                return
            elif resp == QMessageBox.No:
                pass # continue without saving
                # dname = f"Switch Layout  >>  {lastData['name']}"
                # self.cboxRoiPresets.setItemData(self._currentPresetIndex, dname, Qt.ItemDataRole.DisplayRole)

        self.roiSidebar.sgnRoiClearAll.emit()
        roiData = self.cboxRoiPresets.currentData().get("rois", {}).get("rois", {})
        # Set current layout
        self._currentPresetIndex = self.cboxRoiPresets.currentIndex()
        self._currentLayoutModified = False
        self.loadRois(roiData)
        self._currentLayoutData = self.cboxRoiPresets.currentData()
        self.updateCurrentLayoutUi()
        self.refreshCboxPresets()

    def updateCurrentLayoutUi(self):
        name = None
        data = self._currentLayoutData
        if not data:
            data = {}
        name = data.get("name")
        rois = data.get("rois", {})
        scope: str = rois.get("scope", "")
        layout = f"{scope.title()} Layout"
        self.pbRoiLayout.setText(f"  {layout}: {name}{' [Modified] ' if self._currentLayoutModified else ' '}")
        state = name is not None
        self.actRoiNew.setEnabled(True)
        self.actRoiSave.setVisible(state)
        self.actRoiSaveAs.setVisible(True)
        self.actRoiDelete.setVisible(state)
        self.actRoiDuplicate.setVisible(state)

        if scope == "linked":
            self.actRoiLinkLocalProject.setText("Unlink Layout From This Project")
            self.actRoiSave2.setText("Save To Active Project")
            self.actRoiLinkLocalProject.setVisible(True)
        elif scope == "global":
            self.actRoiLinkLocalProject.setText("Link Layout To This Project")
            self.actRoiSave2.setText("Save To Active Project")
            self.actRoiLinkLocalProject.setVisible(True)
        elif scope == "local":
            self.actRoiSave2.setText("Convert To Global Layout")
        else:
            self.actRoiLinkLocalProject.setVisible(False)
            self.actRoiSave2.setVisible(False)
            return

        self.actRoiSave2.setVisible(True)
        self.actRoiEdit.setVisible(True)

    def onRoiPresetChanged(self, index: int):
        """Load the preset that was changed to"""
        data = self.cboxRoiPresets.itemData(index, Qt.ItemDataRole.UserRole)
        return

    def refreshCboxPresets(self):
        projectId = self.projectId
        self.cboxRoiPresets.clear()
        roiJson = self.loadRoiConfig()
        globalRois = roiJson.get("global", {})
        localRois = roiJson.get("projects", {}).get(str(projectId), {})

        currentName = None
        if self._currentLayoutData:
            currentName = self._currentLayoutData.get("name")

        self.cboxRoiPresets.addItem("")

        # Firstly load local layouts
        for name, state in localRois.items():
            state["scope"] = "local"
            dname = f"Switch To Local Layout  >>  {name}"
            self.cboxRoiPresets.addItem(dname, userData={"rois": state, "name": name})

        # Then load linked layouts
        for name, state in globalRois.items():
            linked = [int(l) for l in state.get("linked", [])]
            if not projectId in linked:
                continue
            state["scope"] = "linked"
            dname = f"Switch To Linked Layout  >>  {name}"
            self.cboxRoiPresets.addItem(dname, userData={"rois": state, "name": name})

        # Finally load global layouts
        for name, state in globalRois.items():
            linked = [int(l) for l in state.get("linked", [])]
            if projectId in linked:
                continue
            state["scope"] = "global"
            dname = f"Switch To Global Layout  >>  {name}"
            self.cboxRoiPresets.addItem(dname, userData={"rois": state, "name": name})

        for name, state in globalRois.items():
            rois = state.get("rois", [])
            if not rois:
                continue  # Don't allow adding ROIS from empty layouts
            if name == currentName: # Prevent option to add to the same layout
                continue
            dname = f"Add All Rois From Layout  >>  {name}"
            self.cboxRoiPresets.addItem(dname, userData={"addrois": state, "name": name})

        for name, state in globalRois.items():
            rois = state.get("rois", [])
            if name == currentName: # Prevent option to add to the same layout
                continue
            for roi in rois:
                dname = f"Add Roi  >>  {name}  >> {roi['name']}"
                self.cboxRoiPresets.addItem(dname, userData={"addroi": roi, "name": roi['name']})

        # After refresh, reselect index if applicable
        if self._currentLayoutData:
            if self._currentLayoutData.get("rois", {}).get("scope") == "local":
                for n in range(self.cboxRoiPresets.count()):
                    itemData = self.cboxRoiPresets.itemData(n, Qt.ItemDataRole.UserRole)
                    if not itemData:
                        continue
                    if "rois" not in itemData:
                        continue
                    if itemData.get("rois", {}).get("scope") != "local":
                        continue
                    if itemData["name"] == self._currentLayoutData["name"]:
                        self._currentPresetIndex = n
                        self.cboxRoiPresets.setCurrentIndex(n)
                        self._currentLayoutData = itemData
                        break
            else:
                for n in range(self.cboxRoiPresets.count()):
                    itemData = self.cboxRoiPresets.itemData(n, Qt.ItemDataRole.UserRole)
                    if not itemData:
                        continue
                    if "rois" not in itemData:
                        continue
                    if itemData.get("rois", {}).get("scope") == "local":
                        continue
                    if itemData["name"] == self._currentLayoutData["name"]:
                        self._currentPresetIndex = n
                        self.cboxRoiPresets.setCurrentIndex(n)
                        self._currentLayoutData = itemData
                        break

    def onRoiModified(self):
        """An ROI / layout has been modified"""
        if self._currentPresetIndex is None or self._currentPresetIndex == -1:
            return
        current = self.cboxRoiPresets.itemData(self._currentPresetIndex, Qt.ItemDataRole.UserRole)
        index = self._currentPresetIndex
        if not current:
            return
        self._currentLayoutModified = True
        self.updateCurrentLayoutUi()
        current.setdefault("modified", True)
        self.cboxRoiPresets.setItemData(index, current, Qt.ItemDataRole.UserRole)
        dname = f"Switch Layout  >>  {current['name']}  (Modified)  [Current Layout]"
        self.cboxRoiPresets.setItemData(index,  dname, Qt.ItemDataRole.DisplayRole)

    def getFieldMap(self):
        fieldMapJson = getSavedFieldMapJson()
        exclude_roi_keys = ['annotMarkups', 'avg_elevation', 'coordinates', 'coordinates2',
                            'elevation','flangeID', 'max_elevation', 'min_elevation',
                            'modDate', 'sys_build', 'sys_document', 'sys_filename',
                            'sys_layout_valid', 'sys_path', 'xCoord', 'yCoord', 'weldId',
                            'id_annot_info', 'pdf_id', 'pdf_page']  # Exclude from ROI drop-down selection # Added
        tagMap = {}
        newFields = {}  # Create a new dictionary for filtered fields
        for field, fieldData in fieldMapJson.get("fields", {}).items():
            # Exclude fields based on their name
            if field not in exclude_roi_keys:
                tagMap[field] = fieldData.get("tags", [])
                newFields[field] = fieldData
        self.fieldMap = newFields

    def closePdf(self):
        self.pdfViewer.closePdf()
        self.roiSidebar.sgnRoiClearAll.emit()
        self._currSourceData = None
        self.roiSidebar.updateButtonState()
        self.cboxSources.setCurrentIndex(-1)
        self.lePage.clear()
        self.lblPage.setText("")
        self.lblPage.hide()
        self.updateUi()

    def onCboxSources(self, event):
        try:
            source = self.cboxSources.itemData(event)
            if not source:
                self.cboxSources.setToolTip("")
            else:
                try:
                    text = f"Filename: {source['filename']}"
                    text += f"\nEngineer Drafter: {source['documentVendor']}"
                    self.cboxSources.setToolTip(text)
                except Exception as e:
                    logger.info("Could not set cboxSources tooltip")
            if not source.get("filename"):
                return
            if source.get("filename") == self.pdfViewer.currentPdfPath:
                return
            if self.pdfViewer.currentPdfPath:
                res = QMessageBox.question(self, "Change Drawing?", "Continue?              ")
                if res != QMessageBox.Yes:
                    return

            self.loadPdf(source)
            self.cboxRoiPresets.clear()
            self.refreshCboxPresets()
        except Exception as e:
            print("Failed to load PDF selection", e)

    def onHelpEnter(self, event: QEnterEvent):
        if self.popupHelp:
            return
        self.popupHelp = HelpPopup(self)

        self.popupHelp.setText(HELP_HTML)
        self.popupHelp.show()

    def onHelpLeave(self, event):
        if self.pbHelp.isChecked():
            return
        self.popupHelp.close()
        self.popupHelp = None

    def previousPage(self):
        self.setPage(self.pdfViewer.page - 1)

    def nextPage(self):
        self.setPage(self.pdfViewer.page + 1)

    def setPage(self, page: int):

        try:
            self.pdfViewer.sgnRoiAdded.disconnect(self.onRoiUpdated)
        except RuntimeError:
            pass

        if not self.pdfViewer.pages:
            return
        if page >= 1 and page <= self.pdfViewer.pages:
            self.storeGroupRois()
            self.pdfViewer.setPage(page)
            self.restoreGroupRois()
            pageData = self.pageGroups.getPageData(page)
            self.chkExtract.setEnabled(True)
            self.chkApplyOcr.setEnabled(True)
            self.chkExtract.setChecked(pageData.get("extract", True))
            self.chkApplyOcr.setChecked(pageData.get("ocr", False))

            self.refreshDisplayedRegions()
            self.updateSyncOptions()

        self.pdfViewer.sgnRoiAdded.connect(self.onRoiUpdated)

    def onChangePage(self):
        logger.info("Page number input changed")
        try:
            page = int(self.lePage.text())
            self.setPage(page)
        except:
            pass

    def updatePageStatus(self):
        self.roiSidebar.clearAll()
        if self.pdfViewer.page:
            self.lePage.setText(f"{self.pdfViewer.page}")
        text = f"/{self.pdfViewer.pages}"
        self.lblPage.setText(text)
        self.lblPage.show()

    def onAnalyzePages(self):
        """Start group pages job"""

        if self.checkTaskRunning():
            return False

        text = "Running drawing analysis will auto-detect and refresh the page groups list. Proceed?"
        if QMessageBox.question(self, "Confirm Drawing Analysis", text) != QMessageBox.Yes:
            return

        filename = self.pdfViewer.currentPdfPath
        if not filename or not os.path.exists(filename):
            return

        with self._lock:
            self._taskRunning = True
            self._currentTask = "Analyze Pages"
            self.thread = QThread()
            self.worker = DetectPageGroupsWorker(self.projectId, filename)
            self.worker.moveToThread(self.thread)
            self.worker.finished.connect(lambda x: self.drawingAnalyzed.emit(x))
            self.thread.started.connect(self.worker.run)
            self.thread.start()

    def checkTaskRunning(self) -> bool:
        with self._lock:
            return False
            if not self._taskRunning:
                return False
            text = f"{self._currentTask} is currently in progress. Try again when this completes"
            QMessageBox.information(self, "Running Task", text)
            return True

    def onPreprocess(self):
        filename = self.pdfViewer.currentPdfPath
        if not filename or not os.path.exists(filename):
            QMessageBox.information(self, "Preprocess			", "An opened source must be selected to perform preprocessing")
            return
        if self.checkTaskRunning():
            return

        if self.isSourcePreprocessed():
            text = "Preprocessed data has been detected for this drawing. Would you like to run this again?"
        else:
            text = "Processing drawing. Proceed?"
        if QMessageBox.question(self, "Confirm Preprocess", text) != QMessageBox.Yes:
            return

        with self._lock:
            self._taskRunning = True
            self._currentTask = None
            self.thread = QThread()
            self.worker = PreprocessWorker(self.projectId, filename)
            self.worker.moveToThread(self.thread)
            self.worker.finished.connect(lambda x: self.preprocessed.emit(x))
            self.thread.started.connect(self.worker.run)
            self.thread.start()

        self.updateUi()

    def onAnalysisResults(self, groups: pd.DataFrame):
        """Callback handler for grouped pages results"""

        # Initialize compatible format
        validPages = {n for n in range(1, self.pdfViewer.pages+1)}
        data = {"groups": {}}
        self.storeGroupRois()
        for groupNumber, group in groups.groupby("group_number"):
            groupPages = group["page_number"].unique().tolist()
            groupPages = sorted([p for p in groupPages if p in validPages])
            data["groups"][groupNumber] = {"pages": []}

            for pageNum in groupPages:
                page = {"page": pageNum}
                try:
                    page.update(self.pageGroups.pageData.get(pageNum))
                except Exception as e:
                    pass

                data["groups"][groupNumber]["pages"].append(page)

            # Try to merge results with existing ROI groups
            try:
                data["groups"][groupNumber]["rois"] = self.pageGroups.data["groups"][groupNumber]["rois"]
            except Exception:
                pass

        self.pageGroups.onSetPageGroups(data)
        # self.pageGroups.pbDetectPageGroups.setBusy(False)
        # self.restoreGroupRois()
        numGroups = groups["group_number"].max()
        s = "groups" if numGroups > 1 else ""
        QMessageBox.information(self, "Drawing Analysis Finished", f"Analysis detected {numGroups} page {s}. Page groups refreshed")

    def onPreprocessResults(self, results):
        """Preprocessing complete"""
        QMessageBox.information(self, "Source Preprocessing               ", "Finished")
        # self.pbPreprocess.setBusy(False)
        self.updateUi()

    def onPageSelected(self, page):
        currentPage = self.pdfViewer.page
        if currentPage == page:
            return
        self.setPage(page)

    def onPageDataChanged(self, data: dict):
        page = data.get("page")
        if not page or page != self.pdfViewer.page:
            return
        self.chkExtract.setChecked(data.get("extract", True))
        self.chkApplyOcr.setChecked(data.get("ocr", False))

    def storeGroupRois(self):
        """Set the current group ROI layout and updates all other
        groups based on differences to current group
        """
        currentPage = self.pdfViewer.page
        if not currentPage:
            return
        group = self.pageGroups.getGroupFromPage(currentPage)
        rois = self.getCurrentPageRois()
        self.pageGroups.setGroupRois(group, rois)
        print(f"TODO save group ROIs # {group}")

    def restoreGroupRois(self):
        self.pdfViewer.clearRoi()
        currentPage = self.pdfViewer.page
        group = self.pageGroups.getGroupFromPage(currentPage)
        groupRois = self.pageGroups.getGroupRois(group)
        self.lblCurrentGroup.setText(f"Group: {group}")
        for r in groupRois:
            self.loadRoi(r)
        logger.debug(f"Restore ROIs for group {group}")

    def onMultiRoiImport(self):
        filename, _ = QFileDialog.getOpenFileName(self,
                                                  "Open Mult ROI File",
                                                  "",
                                                  "Json Files (*.json);;All Files (*)")
        if not filename:
            return

        try:
            allPages = set([n+1 for n in range(self.pdfViewer.pages)])
            optionsPath = filename
            with open(optionsPath, "r") as file:
                data = json.load(file)

                # Create new group for unaccounted pages
                maxGroup = 1
                for groupNumber, groupData in data.get("groups", {}).items():
                    maxGroup = max(int(groupNumber), maxGroup)
                    cleanedGroup = []
                    for p in groupData["pages"]:
                        pageNum = p.get("page", -1)
                        if pageNum == -1:
                            continue
                        if pageNum not in allPages:
                            # page doesn't exist in doc. remove it
                            continue
                        cleanedGroup.append(p)
                        allPages.discard(pageNum)
                    data["groups"][str(groupNumber)]["pages"] = cleanedGroup
                if allPages:
                    data["groups"][str(maxGroup + 1)] = {"pages": [{"page": page} for page in allPages], "extract": False}

                res = self.pageGroups.setPageGroups.emit(data)
                logger.info("Restored extraction options")
                self.restoreGroupRois()
                return True
        except Exception as e:
            logger.info("No saved extraction options found")
            logger.debug(e)

        defaultGroups = self.getDefaultGroupState()
        self.pageGroups.setPageGroups.emit(defaultGroups)

        if defaultGroups:
            return True

        return False

    def onMultiRoiExport(self):
        filename, _ = QFileDialog.getSaveFileName(self,
                                                  "Save Multi ROI",
                                                  "",
                                                  "Json Files (*.json);;All Files (*)")
        if not filename:
            return

        self.storeGroupRois()
        data = self.pageGroups.getState()
        with open(filename, "w") as file:
            json.dump(data, file, indent=4)

    def setExtractEnabled(self, enabled: bool = True):
        """Enable/disable Extraction button"""
        if enabled:
            with self._lock:
                if not self._currentTask or self._currentTask == "ROI Extraction":
                    self._taskRunning = False
                    self._currentTask = None

    def onExtractionClicked(self):
        """Extract current ROI layout for all groups and request extraction"""
        try:
            if not self.pdfViewer.currentPdfPath:
                raise FileNotFoundError("")
            if not os.path.exists(self.pdfViewer.currentPdfPath):
                raise FileNotFoundError("The selected source does not exist")
            if not self.isSourcePreprocessed():
                raise Exception("Action required: Preprocessing is required on this source to perform extraction")
        except Exception as e:
            QMessageBox.information(self, "Extraction\t\t\t", str(e))
            return

        if self.checkTaskRunning():
            return

        with self._lock:
            self._taskRunning = True
            self._currentTask = None

        # Require documentVendor field
        def closePopup():
            if self.updateProjectPopup:
                self.updateProjectPopup = None
                with self._lock:
                    self._taskRunning = False
                self.pbExtract.setEnabled(True)

        if not self._currSourceData:
            return

        # Prompt for document vendor if not set
        documentVendor = self._currSourceData.get("documentVendor")
        if not documentVendor:
            if not self.updateProjectPopup:
                self.updateProjectPopup = UpdateProjectDetailsPopup(self, self._currSourceData)
            self.updateProjectPopup.pbCancel.clicked.connect(closePopup)
            offset = QPoint(0, self.pbExtract.height())
            self.updateProjectPopup.move(self.pbExtract.pos() + offset)
            self.updateProjectPopup.setSourceData(self._currSourceData)
            self.updateProjectPopup.show()
            self.updateProjectPopup.focus()
            return

        # if self.roiSidebar.roiList.count() == 0:
        #     logger.info("Create ROI before extract")
        #     self.pbExtract.setAlert("ROIs are required for extraction", MyButton.ALERT_ERROR)
        #     return

        errs = []
        pageCount = 0
        try:
            self.storeGroupRois()

            # Check all groups if they have overlapping ROIs
            state = self.pageGroups.getState()
            # self.setExtractEnabled(False)

            # Filter out the groups and pages which user has selected
            filtered = {}
            overlap_err = []
            for groupNumber, groupData in state["groups"].items():
                if groupData.get("extract") is False:
                    continue

                # Extract pages if not False
                pages = [p for p in groupData.get("pages", []) if p.get("extract", True) is not False]
                if not pages:
                    continue
                pageCount += len(pages)
                rois = groupData.get("rois", [])
                groupData["pages"] = pages

                filtered[int(groupNumber)] = groupData
                roiNames = set()

                # Check for overlaps
                checks = []
                for r in rois:
                    roiNames.add(r["name"])
                    checks.append([
                        r["name"],
                        QPointF(r["relativeX0"], r["relativeY0"]),
                        QPointF(r["relativeX1"], r["relativeY1"])
                    ])

                overlapping = set()
                for n, (name, l1, r1) in enumerate(checks):
                    for n2, (name2, l2, r2) in enumerate(checks):
                        if n == n2:
                            continue
                        if name2 in overlapping:
                            continue
                        if is_overlapping(l1, r1, l2, r2):
                            overlapping.add(name)
                            overlapping.add(name2)

                if overlapping:
                    overlap_err.append((groupNumber, overlapping))

                if "BOM" not in roiNames:
                    errs.append(f"Required: BOM table is required for group {groupNumber}")

            message = ""
            if errs:
                message += f"\n\nAction required:\n"
                for err in errs:
                    message += err + "\n"

            if overlap_err:
                message += "Warning: Overlapping ROIs are detected"
                message += f"\n\nDetected Groups with overlapping ROIS:\n"
                for groupNumber, overlapping in overlap_err:
                    message += f"\nGroup {groupNumber}. Overlap count {len(overlapping)}"
                message += "\n"
                message += "\nProceed with extraction?"

            if message:
                if QMessageBox.question(self, "Overlapping ROIs", message) != QMessageBox.Yes:
                    self.setExtractEnabled(True)
                    return

            # Convert state as worker accepts slightly different format
            payload = convert_roi_payload({"groups": filtered})
            payload["pageCount"] = pageCount

            # Emit the signal with the JSON data
            self.extracted.emit(payload)

        except Exception as e:
            logger.error(f"Error extracting and saving ROI coordinates: {e}", exc_info=True)
            self.setExtractEnabled(True)

    def saveExtractionOptions(self):
        if not self.pdfViewer.currentPdfPath:
            return

        if QMessageBox.question(self, "Confirm Save", "Save group layouts and extraction options?") != QMessageBox.Yes:
            return

        # Save group state if opened
        if self.pageGroups.currentGroup:
            self.storeGroupRois()
            # self.pageGroups.saveCurrentGroup()
        data = self.pageGroups.getState()

        sourceDir = getSourceDataDir(self.projectId, self.pdfViewer.currentPdfPath, makedir=True)
        with open(os.path.join(sourceDir, "options.json"), "w") as file:
            json.dump(data, file)

    def getDefaultGroupState(self):
        data = {}
        try:
            # Default page groups / TODO load existing page groups
            data = {
                "groups": {
                    1: {
                        "pages": [{"page": n} for n in range(1, self.pdfViewer.pages+1)],
                        "extract": True
                    }
                }
            }
        except Exception as e:
            logger.warning("Failed to initialize default extraction options")
            logger.debug(e)

        return data

    def restoreExtractionOptions(self):
        """Load previous extraction options and restore state

        Returns True if extraction options were set sucessfully

        """
        sourceDir = getSourceDataDir(self.projectId, self.pdfViewer.currentPdfPath, makedir=False)
        try:
            optionsPath = os.path.join(sourceDir, "options.json")
            with open(optionsPath, "r") as file:
                data = json.load(file)
                res = self.pageGroups.setPageGroups.emit(data)
                logger.info("Restored extraction options")
                return True
        except Exception as e:
            logger.info("No saved extraction options found")
            logger.debug(e)

        defaultGroups = self.getDefaultGroupState()
        self.pageGroups.setPageGroups.emit(defaultGroups)

        if defaultGroups:
            return True

        return False

    def addProjectSource(self):
        """User chooses a PDF"""
        projectId = self.projectId
        if not projectId:
            return
        filename, _ = QFileDialog.getOpenFileName(self,
                                                  "Open PDF File",
                                                  "",
                                                  "PDF Files (*.pdf);;All Files (*)")
        if not filename:
            return
        for document in self._projectData.get("documents", []):
            if filename in document.get("filename", ""):
                msgBox = QMessageBox()
                msgBox.setWindowTitle("Already Added")
                msgBox.setIcon(QMessageBox.Critical)
                msgBox.setText("Document has already been added")
                msgBox.exec()
                return
        pub.sendMessage("project-source-add", data = {"projectId": projectId, "filename": filename})

    def isSourcePreprocessed(self) -> bool:
        filename = self.pdfViewer.currentPdfPath
        pdfLoaded = bool(filename) and self.projectId
        if not pdfLoaded:
            return False
        metadata = getSourceMetadataPath(self.projectId, filename)
        return os.path.exists(metadata)

    def updateUi(self):
        """Update UI state"""

        filename = self.pdfViewer.currentPdfPath
        pdfLoaded = bool(filename) and self.projectId

        if pdfLoaded and False:
            # Check if preprocessing data is missing
            if self.isSourcePreprocessed():
                # self.pbPreprocess.setFixedWidth(160)
                self.pbPreprocess.setText("Preprocessed")
                self.pbPreprocess.setAlert("Preprocessing data found", alertType=MyButton.ALERT_OK)
                self.pbExtract.removeAlert()
            else:
                self.pbPreprocess.setText("Preprocessing Required")
                # self.pbPreprocess.setFixedWidth(220)
                self.pbPreprocess.setAlert("Preprocessing is required to perform extraction", alertType=MyButton.ALERT_ERROR)
                self.pbExtract.setAlert("Preprocessing is required to perform extraction", alertType=MyButton.ALERT_ERROR)
        else:
            self.chkExtract.setChecked(False)
            self.chkApplyOcr.setChecked(False)

        print(self._currentTask, self._taskRunning)
        with self._lock:
            if self._taskRunning:
                preprocess = self._currentTask == "Source Preprocessing"
                self.pbPreprocess.setText("Preprocessing" if preprocess else "Preprocess")
                self.pbPreprocess.setBusy(preprocess)

                extract = self._currentTask == "ROI Extraction"
                self.pbExtract.setText("Extracting" if extract else "Extract")
                self.pbExtract.setBusy(extract)

                detect = self._currentTask == "Detect Page Groups"
                self.pageGroups.pbDetectPageGroups.setText("Detecting" if detect else "Detect Groups")
                self.pageGroups.pbDetectPageGroups.setBusy(detect)

            else:
                self.pbPreprocess.setText("Preprocess")
                self.pbExtract.setText("Extract")
                self.pageGroups.pbDetectPageGroups.setText("Detect Groups")

                self.pbPreprocess.setBusy(False)
                self.pbExtract.setBusy(False)
                self.pageGroups.pbDetectPageGroups.setBusy(False)

        self.pdfViewer.setEnabled(pdfLoaded)
        self.roiSidebar.setEnabled(pdfLoaded)

    def updateSyncOptions(self):
        groups = self.pageGroups.getGroupCount()
        state = [(n+1, None) for n in range(groups)]
        currentGroup = self.pageGroups.currentGroup
        self.syncLayoutPopup.updateState(state, currentGroup)
        self.pbSyncLayout.setEnabled(True)

    def onSyncLayoutApplied(self):
        groups = self.syncLayoutPopup.getCheckedGroups()
        if not groups:
            return
        remove = self.syncLayoutPopup.isSyncRoiRemove()
        rois = self.getCurrentPageRois()
        self.pageGroups.syncGroupRois(rois, groups, remove)
        self.syncLayoutMenu.close()
        QMessageBox.information(self, "Sync Layout", "Layout Synchronized")

    def onPageOptionChecked(self, event=None):
        extract = self.chkExtract.isChecked()
        ocr = self.chkApplyOcr.isChecked()
        self.pageGroups.setPageData(self.pdfViewer.page, extract, ocr)

    def restoreDefaultGroups(self):
        res = QMessageBox.question(self,
                            "Restore Default Groups  ",
                            "This will restore groups to the default state. Proceed?")
        if res != QMessageBox.Yes:
            return
        data = self.getDefaultGroupState()
        self.pageGroups.setPageGroups.emit(data)

    def restoreSavedGroups(self):
        res = QMessageBox.question(self,
                            "Restore Saved Groups  ",
                            "This will restore groups to the last saved state. Proceed?")
        if res != QMessageBox.Yes:
            return
        self.restoreExtractionOptions()

    def onJobQueueUpdated(self, data):
        jobs = data.get("jobs", [])
        tasks = ["Detect Page Groups", "Source Preprocessing", "ROI Extraction"]
        for job in jobs:
            if job["finished"]:
                continue
            name = job.get("name")
            if name in tasks:
                with self._lock:
                    if self._currentTask == name:
                        break
                    self._currentTask = name
                    self.tasksChanged.emit()
                    break
        else:
            with self._lock:
                if not self._currentTask:
                    return
                self._currentTask = None
                self._taskRunning = False
                self.tasksChanged.emit()

    def refreshDisplayedRegions(self):
        """TODO - Quick implementation of displaying missing regions"""
        print(self.pbMissingRegions.isChecked())
        if not self.pbMissingRegions.isChecked():
            self.pdfViewer.clearRegions()
            return

        filename = self.pdfViewer.currentPdfPath
        pdfLoaded = bool(filename) and self.projectId
        page = self.pdfViewer.page
        if not pdfLoaded:
            return False

        # draw missing
        ocrFile = getSourceOcrPath(self.projectId, filename)
        ocrDf = pd.DataFrame()
        try:
            ocrDf = load_df_fast(ocrFile)
        except Exception as e:
            pass

        missing = getSourceMissingRegionsPath(self.projectId, filename)
        logger.debug("Checking for missing regions")

        try:
            df = load_df_fast(missing)
        except Exception as e:
            return

        regions = []

        df = df[df["pdf_page"] == page]
        scale = self.pdfViewer.dpi / 72
        df["x0"] = df["x0"].apply(lambda x: x * scale)
        df["y0"] = df["y0"].apply(lambda y: y * scale)
        df["x1"] = df["x1"].apply(lambda x: x * scale)
        df["y1"] = df["y1"].apply(lambda y: y * scale)

        for row in df.itertuples():
            start = QPoint(row.x0, row.y0)
            end = QPoint(row.x1, row.y1)
            regions.append((start, end, None))

        self.pdfViewer.drawRegions(regions, "red")

        # Draw Raw
        raw = getSourceRawDataPath(self.projectId, filename)
        logger.debug("Checking for raw regions")

        try:
            raw_df = load_df_fast(raw)
        except Exception as e:
            return

        ocrRegions = []

        if not ocrDf.empty:
            ocrDf = ocrDf[ocrDf["pdf_page"] == page]
            from src.atom.vision.ocr_patcher import reduce_regions
            ocrDf = reduce_regions(ocrDf, inverse=False)
            # scale = self.pdfViewer.dpi / 72
            ocrDf["x0"] = ocrDf["x0"].apply(lambda x: x * self.pdfViewer.pageWidth)
            ocrDf["y0"] = ocrDf["y0"].apply(lambda y: y * self.pdfViewer.pageHeight)
            ocrDf["x1"] = ocrDf["x1"].apply(lambda x: x * self.pdfViewer.pageWidth)
            ocrDf["y1"] = ocrDf["y1"].apply(lambda y: y * self.pdfViewer.pageHeight)
            # ocrDf.to_excel("ocr_df.xlsx")

            for row in ocrDf.itertuples():
                start = QPoint(row.x0, row.y0)
                end = QPoint(row.x1, row.y1)
                ocrRegions.append((start, end, row.text))

            self.pdfViewer.drawRegions(ocrRegions, "purple")

        regions = []

        raw_df = raw_df[raw_df["pdf_page"] == page]
        scale = self.pdfViewer.dpi / 72
        for row in raw_df.itertuples():
            x0, y0, x1, y1 = (n * scale for n in row.coordinates2)
            start = QPoint(x0, y0)
            end = QPoint(x1, y1)
            regions.append((start, end, row.value))

        self.pdfViewer.drawRegions(regions, "lightgreen")

        # Display extraction preview
        self.getExtractionPreview(raw_df)

    def getExtractionPreview(self, raw_df: pd.DataFrame):
        """Get extraction preview for current page"""
        rois = self.getCurrentPageRois()
        page = self.pdfViewer.page
        if not rois:
            return
        rois = convert_rois(rois)
        pdf_path = self.pdfViewer.currentPdfPath
        raw_df["coordinates"] = raw_df["coordinates"].apply(tuple)
        raw_df["coordinates2"] = raw_df["coordinates2"].apply(tuple)
        self.raw_df = raw_df

        try:
            ocr_df = getSourceOcrPath(self.projectId, pdf_path)
            ocr_df = load_df_fast(ocr_df)
            ocr_df = ocr_df[ocr_df["pdf_page"] == page]
        except Exception as e:
            ocr_df = pd.DataFrame()

        self.extractionPreview.fieldMap = self.fieldMap
        self.extractionPreview.updateData(file_path=pdf_path,
                                          raw_df=raw_df,
                                          page_num=page,
                                          ocr_df=ocr_df)
        # self.extractionPreview.finished.
        self.extractionPreview.requestPreview(rois)

    def onRoiUpdated(self, roi: RoiItem, oldState: dict = {}):
        """User updated an ROI by add, remove or editing. Update the extraction preview"""
        try:
            self.extractionPreview.removeRoi(oldState["name"])
        except:
            pass
        try:
            roi = roi()
        except:
            pass

        if self.raw_df is None:
            return

        pdf_path = self.pdfViewer.currentPdfPath
        page = self.pdfViewer.page

        try:
            ocr_df = getSourceOcrPath(self.projectId, pdf_path)
            ocr_df = load_df_fast(ocr_df)
            ocr_df = ocr_df[ocr_df["pdf_page"] == page]
        except Exception as e:
            ocr_df = pd.DataFrame()

        page = self.pdfViewer.page
        pdf_path = self.pdfViewer.currentPdfPath
        self.extractionPreview.updateData(file_path=pdf_path,
                                          raw_df=self.raw_df,
                                          page_num=page,
                                          ocr_df=ocr_df)
        try:
            rois = [roi.getSaveState()]
            rois = convert_rois(rois)
            self.extractionPreview.requestPreview(rois, replace=False)
        except Exception as e:
            logger.error(f"Error requesting preview update: {roi.name if roi else 'Unknown'}: {e}", exc_info=True)

    def onRoiRemoved(self, name: str):
        self.extractionPreview.removeRoi(name)

    def onGroupsModified(self, save: bool):
        """A group has changed or a new group has been created

        Usage:
            If `save` is True, store the group ROIs. Used when moving
            pages to a new group.

            If False, discard layout
        """
        if save:
            self.storeGroupRois()
        else:
            self.restoreGroupRois()
        self.updateSyncOptions()

    def onSetPageGroupsFailed(self):
        """Restore the single default group if loading failed"""
        if self._attemptFallback:
            logger.warning("Failed to restore default groups")
            return
        self._attemptFallback = True
        defaultGroups = self.getDefaultGroupState()
        self.pageGroups.setPageGroups.emit(defaultGroups)

    def onIsometricRegion(self):
        """Detect if Isometric Drawing Area is added to ROIS"""

        if not self.pdfViewer.currentPdfPath:
            return

        file = self.pdfViewer.currentPdfPath

        if self.isometricRegionPopup:
            self.isometricRegionPopup.raise_()
            return

        def closeEvent(event):
            super(IsometricRegionPopup, self.isometricRegionPopup).closeEvent(event)
            self.isometricRegionPopup = None

        self.isometricRegionPopup = IsometricRegionPopup()
        self.isometricRegionPopup.closeEvent = closeEvent
        self.isometricRegionPopup.show()

        # Use the current group states
        state = self.pageGroups.getState()
        group: int = self.pageGroups.currentGroup
        pageRois = self.getCurrentPageRois()

        state["groups"][group]["rois"] = pageRois

        self.isometricRegionPopup.setData(file, state)

    def onCboxSourcesContextMenuRequested(self, event):
        menu = QMenu(self.cboxSources)

        data = self.cboxSources.currentData()
        if not data:
            return

        filename = data.get("filename")
        if not filename:
            return

        action1 = QAction(f"Open file with system viewer", self.cboxSources)
        action2 = QAction("Open containing folder", self.cboxSources)
        action3 = QAction("Copy filename", self.cboxSources)

        action1.triggered.connect(lambda: os.startfile(filename))
        action2.triggered.connect(lambda: os.startfile(os.path.dirname(filename)))
        action3.triggered.connect(lambda: QApplication.clipboard().setText(filename))

        menu.addAction(action1)
        menu.addAction(action2)
        menu.addAction(action3)

        menu.exec_(QCursor.pos())
