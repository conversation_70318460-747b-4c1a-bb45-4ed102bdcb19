# def worker(pdf_path, start_page, end_page, converted_roi_payload, value_patterns, columns_to_aggregate, 
#            columns_to_keep, sub_dir, x_offset, y_offset, ref_width, ref_height, overlap_threshold, project_id, doc_tolerance=2, validate_string=None):
#     data = []
#     combined_raw_df = pd.DataFrame()
#     combined_bom_df = pd.DataFrame()
#     combined_annot_df = pd.DataFrame()
#     combined_annot_types_df = pd.DataFrame()
#     combined_outlier_df = pd.DataFrame()
#     combined_annot_outlier_df = pd.DataFrame()
#     outliers_df = pd.DataFrame()
#     document = fitz.open(pdf_path)
#     bom_df = pd.DataFrame()
#     annot_df = pd.DataFrame()
#     logger.debug(f"Worker started for pages {start_page} to {end_page}")
    
#     #validate_string = "LINDE AG - LE DIVISION"
    
#     #if db_manager is None:
#     db_manager = DatabaseManager('ais_database.db')
            
#     # Extracting the filename
#     filename = os.path.basename(pdf_path)

#     # Extracting the directory path
#     directory_path = os.path.dirname(pdf_path)

#     # Splitting the directory path into a list of parent folders
#     parent_folders = directory_path.split(os.sep)
#     parent_folders_str = str(parent_folders)
    
#      # Initialize a dictionary to collect skipped page numbers and their sizes
#     skipped_pages = {}
   
#     ############################################################## process_pdf_pages
#     for page_number in range(start_page, end_page):
#         # Reset Offset
#         x_offset, y_offset = 0, 0
#         doc_skip = 0
 
#         ######################################################### process_page_size
#         # Get document sizes
#         try:
#             page = document.load_page(page_number)
#             current_width, current_height = page.rect.width, page.rect.height
#             doc_size = ",".join([str(current_width), str(current_height)])
            
#             width_diff = abs(current_width - ref_width)
#             height_diff = abs(current_height - ref_height)
            
#             # Store the PDF Page if valid
#             # Initialize a binary stream to hold the PDF page
#             pdf_stream = io.BytesIO()
#             # Create a new PDF with just this page and save to the stream
#             single_page_doc = fitz.open()  # Create a new empty PDF
#             single_page_doc.insert_pdf(document, from_page=page_number, to_page=page_number)  # Insert the current page
#             single_page_doc.save(pdf_stream)  # Save the single-page document to the stream
#             single_page_doc.close()  # Close the single-page document
#             # Convert the stream's content to binary data
#             pdf_bytes = pdf_stream.getvalue()
#             # Proceed to store `pdf_bytes` in your database

#             document_name = f"{str(uuid.uuid4())}.pdf"  # Unique name for the stored PDF page
            
#             # Check if differences are within the tolerance
#             if width_diff <= doc_tolerance and height_diff <= doc_tolerance:
#                 # Adjust x_offset and y_offset based on the difference
#                 adjusted_x_offset = x_offset + (current_width - ref_width) / 2
#                 adjusted_y_offset = y_offset + (current_height - ref_height) / 2

#                 # Proceed with processing using the adjusted offsets
#                 # Make sure to use adjusted_x_offset and adjusted_y_offset for processing this page
#                 #logger.info(f"Adjusting for page size within tolerance for page {page_number + 1}. Adjusted:{adjusted_x_offset}, {adjusted_y_offset}")
#             else:
#                 # If the page size difference is beyond the tolerance, log and skip this page
#                 logger.info(f"Skipping page {page_number + 1} due to size mismatch ({current_width}x{current_height}). REF SIZE: {ref_width}x{ref_height}. OUT OF TOLERANCE")
#                 skipped_pages[page_number + 1] = (current_width, current_height)
#                 doc_skip=1
#                 continue
            
#         except Exception as e:
#             logger.error(f"Error loading page {page_number}: {e}", exc_info=True)
#             continue  # Skip this page if it can't be loaded
        
#         finally:
#             try:
#                 # Attempt to commit to database, including the skip status
#                 pdfID = db_manager.insert_pdf_page_to_storage(project_id, page_number + 1, "Isometric", document_name, pdf_bytes, doc_size, doc_skip)
#             except Exception as e:
#                 logger.critical(f"Error committing pdf page ({page_number+1}) to database: {e}", exc_info=True)
#         ########################## END process_page_size

#         # Reset page_validated
#         page_validated = False
        
#         ######################### START process_table_rois
        
#         #Get BOM table data \\\\\\\BOM TABLE LOGIC//////// bom_df=Text, annot_df=Annotations
#         try:
#             bom_df, annot_df, outliers_df, annot_outlier_df = get_table_data(pdf_path, page, page_number, converted_roi_payload)   
            
#             # Insert the pdf_ID(db pdf_id into each DataFrame
#             if bom_df is not None and not bom_df.empty:
#                 bom_df['pdf_id'] = pdfID
#             if annot_df is not None and not annot_df.empty:
#                 annot_df['pdf_id'] = pdfID
#             if outliers_df is not None and not outliers_df.empty:
#                 outliers_df['pdf_id'] = pdfID
#             if annot_outlier_df is not None and not annot_outlier_df.empty:
#                 annot_outlier_df['pdf_id'] = pdfID
            
#             # Try combining TABLE TEXT DATA
#             try:
#                 if bom_df is not None and not bom_df.empty:
#                     combined_bom_df = pd.concat([combined_bom_df, bom_df], ignore_index=True)
                    
#                     try:
#                         if outliers_df is not None and not outliers_df.empty:
#                             combined_outlier_df = pd.concat([combined_outlier_df, outliers_df], ignore_index=True)
                            
#                     except Exception as e:
#                         logger.error(f"An error occurred concatting combined_outliers_df: {e}")
                        
#             except Exception as e:
#                 logger.error(f"An error occurred concatting combined_bom_df: {e}")
                
#             # Try combining TABLE ANNOTATION DATA
#             try:
#                 if len(annot_df) > 0:
#                     combined_annot_df = pd.concat([combined_annot_df, annot_df], ignore_index=True)
                    
#                     try:
#                         if annot_outlier_df is not None and not annot_outlier_df.empty:
#                             combined_annot_outlier_df = pd.concat([combined_annot_outlier_df, annot_outlier_df], ignore_index=True)
#                     except Exception as e:
#                         logger.error(f"An error occurred concatting combined_annot_outlier_df: {e}")
#             except Exception as e:
#                 logger.error(f"An error occurred concatting combined_annot_df: {e}")
            
#             #logger.debug(f"BOM Processing completed successfully for page {page_number}")
#         except Exception as e:
#             #logger.error(f"An error occurred in get_table_data_2: {e}")
#             logger.error(f"Error getting table data on page {page_number}: {e}", exc_info=True)   
            
#         ######################### END process_table_rois

#         ######################### START process_text_rois
#         '''            
#         Extract text along with its bounding box (coordinates)
#         Coordinate (bbox format):(x1, y1, x2, y2)
#         Where x0, y0 is top-left corner of rectangle bounding box
#         and x1, y1 are the coordinates of the lower-right corner of the rectangle bounding the text.
#         '''
#         text_blocks = page.get_text("dict")["blocks"]
#         #Get text data \\\\\\\GENERAL DATA TEXT EXTRACTION LOGIC////////
#         logger.debug(f"Extracting TEXT/ANNOTATION Data: {page_number}")
#         for block in text_blocks:
#             if block["type"] == 0:  # 0 for text block, 1 for image block
#                 try:
#                     bbox1 = block["bbox"]
#                     for line in block["lines"]:
#                         for span in line["spans"]:
#                             #text = span.get("text", "").strip()
#                             text = span.get("text", "")
#                             #print("Text: ", text)
#                             if text:
#                                 # Using regex to remove leading and trailing whitespace
#                                 text = re.sub(r'^\s+|\s+$', '', text)

#                             bbox2 = span.get("bbox", (0, 0, 0, 0))  # Use the bbox of the span for individual coordinates
#                             if text: # Add text to data only if it's not empty
#                                 if validate_string and page_validated ==False:
#                                     if validate_string.replace(" ", "") in text.replace(" ", ""):
#                                         #print("Page Validated")
#                                         page_validated = True
                            
#                                 #Evaluate value with regex
#                                 category, extracted_value = update_value_type(text, value_patterns)
                                        
#                                 data.append({
#                                     'sys_build': parent_folders_str,
#                                     'sys_path': pdf_path,
#                                     'sys_filename': filename,
#                                     'pdf_id': pdfID,
#                                     'pdf_page': page_number + 1,
#                                     'sys_layout_valid': page_validated,
#                                     'type': 'Text',
#                                     'category': category,
#                                     'value': text, #text.strip(),
#                                     'elevation': extracted_value if category == 'elevation' else '',
#                                     'X Position': extracted_value if category == 'x_coordinate' else '',
#                                     'Y Position': extracted_value if category == 'y_coordinate' else '',
#                                     'coordinates': bbox1, #The main bounding box
#                                     'coordinates2': bbox2, #Precise Bounding Box for individual value
#                                     'name': '',
#                                     'title': '',
#                                     'createdDate': '',
#                                     'modDate': '',
#                                     'id_annot_info': '',
#                                 })
#                 except Exception as e:
#                     logger.error(f"Error processing text block on page {page_number}: {e}")
                    
#                 #logger.debug(f">>> Processed Text Data type(0): {page_number}")               
#             #Handle Images
#             elif block["type"] == 1:  # Check if block is an image
#                 try:
#                     #print("/nBLOCK TYPE 1")
#                     image_bbox = block["bbox"]
#                     xref = block["image"]  # xref number of the image

#                     # Ensure xref is an integer
#                     if not isinstance(xref, int):
#                         #print(f"Warning: Expected integer for xref, got {type(xref)} with value {xref}")
#                         continue  # Skip to next block

#                     # Extract and save the image
#                     try:
#                         image_info = document.extract_image(xref)
#                         if image_info is not None:
#                             image_bytes = image_info["image"]
#                             image_filename = f"image_{page_number}_{xref}.png"
#                             image_filepath = os.path.join(r"C:\Users\<USER>\AIS_work_dir\Test Integration\Test1", image_filename) ###Fix this Absolute Path
            
#                             with open(image_filepath, "wb") as img_file:
#                                 img_file.write(image_bytes)

#                             # Append image information to data
#                             data.append({
#                                 'sys_build': parent_folders_str,
#                                 'sys_path': pdf_path,
#                                 'sys_filename': filename,
#                                 'pdf_id': pdfID,
#                                 'pdf_page': page_number + 1,
#                                 'type': 'Image',
#                                 'image_xref': xref,
#                                 'coordinates': image_bbox,
#                                 'image_path': image_filepath
#                             })
#                         #logger.debug(f">>> Processed Text Type (1)(Images): {page_number}")      
#                     except Exception as e:
#                         logger.error(f"Error extracting image at xref") #{xref}: {e}")
#                 except Exception as e:
#                     logger.error(f"Error processing image block on page {page_number}: {e}")
                    
#         ######################### START process_general_annotation_rois

#         # Extract annotations along with their coordinates \\\\\\\GENERAL DATA ANNOTATION EXTRACTION LOGIC////////
#         try:
#             annotations = page.annots()
#             if annotations:
#                 for annot in annotations:
#                     annot_info = annot.info
#                     annot_rect = annot.rect
#                     value = annot_info.get('content', '').strip()

#                     #Evaluate value with regex
#                     category, extracted_value = update_value_type(value, value_patterns)
#                     #elevation, category = update_if_elevation(value, elevation_patterns)

#                     data.append({
#                         'sys_build': parent_folders_str,
#                         'sys_path': pdf_path,
#                         'sys_filename': filename,
#                         'pdf_id': pdfID,
#                         'pdf_page': page_number + 1,
#                         'sys_layout_valid':"",
#                         'type': annot_info.get('subject', ''),
#                         'category': category,
#                         'value': value,
#                         'elevation': extracted_value if category == 'elevation' else '',
#                         'X Position': extracted_value if category == 'x_coordinate' else '',
#                         'Y Position': extracted_value if category == 'y_coordinate' else '',
#                         'coordinates': (annot_rect.x0, annot_rect.y0, annot_rect.x1, annot_rect.y1),
#                         'coordinates2': "",
#                         'name': annot_info.get('name', ''),
#                         'title': annot_info.get('title', ''),
#                         'createdDate': annot_info.get('creationDate', ''),
#                         'modDate': annot_info.get('creationDate', ''),
#                         'id_annot_info': annot_info.get('id', ''),
#                     })
                    
#             #logger.debug(f">>> Processed Annotations Data: {page_number}")
#         except Exception as e:
#             logger.error(f"Error processing annotations on page {page_number}: {e}")
#     ##############################################################           
#     #logger.debug(f"Completed Processing TEXT/ANNOTATION/IMAGE Data: {start_page} to {page_number}")   


#     document.close()
    
#     try:
#         raw_data_df = pd.DataFrame(data)   
#     except Exception as e:
#         logger.error(f"Error assigning Raw Data to Dataframe: {e}")
        
#     # Only combine if raw_data_df has one or more rows
#     if not raw_data_df.empty:
#         combined_raw_df = pd.concat([combined_raw_df, raw_data_df], ignore_index=True)

#         annot_types_df = consolidate_lines_with_multiple_ranges(combined_raw_df, converted_roi_payload, columns_to_aggregate, columns_to_keep, overlap_threshold)
        
#         # Only combine if annot_types_df has one or more rows
#         if not annot_types_df.empty:
#             combined_annot_types_df = pd.concat([combined_annot_types_df, annot_types_df], ignore_index=True)

#     if combined_raw_df is None:
#         print("combined_raw_df is None")
#     if combined_annot_types_df is None:
#         print("combined_annot_types_df is None")
#     if combined_bom_df is None:
#         print("combined_bom_df is None")
#     if combined_outlier_df is None:
#         print("combined_outlier_df is None")
        
#     # Check the overall length of boM DataFrames and apply the specified logic
#     if len(combined_bom_df) == 0 and len(combined_annot_df) == 0:
#         # Both DataFrames have no rows, do nothing
#         logger.debug("BOM: TEXT/ANNOTS ARE BLANK FOR")
#     elif len(combined_bom_df) > 0 and len(combined_annot_df) == 0:
#         # combined_bom_df has rows and combined_annot_df has 0 rows, keep combined_bom_df as is
#         logger.debug("BOM: USING TEXT DATA")
#     elif len(combined_annot_df) > 0 and len(combined_bom_df) == 0:
#         # combined_annot_df has rows and combined_bom_df has 0 rows, set combined_bom_df = combined_annot_df
#         combined_bom_df = combined_annot_df
#         logger.debug("BOM: USING ANNOT DATA")
#     elif len(combined_bom_df) > 0 and len(combined_annot_df) > 0:
#         # Both have rows, use the one with the greatest number of rows
#         if len(combined_bom_df) > len(combined_annot_df):
#             logger.debug("BOM: USING TEXT DATA. -- TEXT ROWS > ANNOT ROWS ---HANDLE THIS CASE")
#         elif len(combined_bom_df) < len(combined_annot_df):
#             combined_bom_df = combined_annot_df
#             logger.debug("BOM: USING ANNOT DATA. -- ANNOT ROWS > TEXT ROWS ---HANDLE THIS CASE")
#         else:
#             logger.debug("BOM: Both DataFrames have the same number of rows. Defaulting to using TEXT DATA.")
#     # After processing all pages, log the skipped pages and their sizes
#     if skipped_pages:
#         logger.warning(f"Skipped pages and their sizes: {skipped_pages}")
        
#     return combined_raw_df, combined_annot_types_df, combined_bom_df, combined_outlier_df, skipped_pages
