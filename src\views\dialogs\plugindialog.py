import os
import inspect
import importlib.util
import io
import sys
import contextlib
import datetime
from pathlib import Path

import pandas as pd
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

from src.data.tablemap import get_table_from_payload
from src.atom.dbManager import DatabaseManager

RESERVED_ARGS = [
    "bom_data",
    "general_data",
    "spool_data",
    "spec_data",
    "outlier_data",
    "rfq_data",
    "project_source",
]

RESERVED_FILEINPUT_ARGS = [
    "bom_file",
    "general_file",
    "spool_file",
    "spec_file",
    "outlier_file",
    "rfq_file",
    "ifc_file",
    "generic_1_file",
    "generic_2_file",
    "save_file",
    "export_profile",
    "roi_payload",
    "input_file",
    "input_1_file",
    "input_2_file",
    "input_3_file",
    "input_4_file",
    "input_5_file",
    "input_6_file",
    "input_7_file",
    "input_8_file",
    "general_data_workbook",
]

RESERVED_DIRINPUT_ARGS = [
    "input_dir",
    "save_dir"
]

RESERVED_SIGNAL_ARGS = [
    "signals"
]

class PluginDialog(QWidget):
    """
    A dialog for listing and running plugins from the src.plugins directory.
    Lists all functions beginning with plugin_ in Python files beginning with plugin_.
    """

    stageDataActivated = Signal(dict)
    importActivated = Signal(dict)

    def __init__(self, parent=None, projectId: int = None) -> None:
        super().__init__(parent)

        self.projectId = projectId
        self.paramaters = {}
        self.getAllTableData = None  # Will be set by MainWindow
        self.customParamWidgets = {}  # Store parameter input widgets
        self.signalMap = {
            "stageData": self.stageDataActivated
        }
        self.db = DatabaseManager()  # Initialize the database manager

        # Dictionary to store staged data for import
        # Format: {payload_name: {"data": DataFrame, "mode": "append"|"replace"}}
        self.stagedData = {}

        self.setObjectName("popup")
        self.setWindowTitle("Plugin Runner")

        # Main layout
        self.setLayout(QVBoxLayout())

        # Create tab widget
        self.tabWidget = QTabWidget()
        self.layout().addWidget(self.tabWidget)

        # Create Plugin tab
        self.pluginTab = QWidget()
        self.pluginTab.setLayout(QVBoxLayout())
        self.tabWidget.addTab(self.pluginTab, "Plugins")

        # Create Help tab
        self.helpTab = QWidget()
        self.helpTab.setLayout(QVBoxLayout())
        self.tabWidget.addTab(self.helpTab, "Help")

        # Create Stage Import Data tab
        self.stageTab = QWidget()
        self.stageTab.setLayout(QVBoxLayout())
        self.tabWidget.addTab(self.stageTab, "Staged Import Data")

        # Setup Plugin tab content (existing UI without reserved args display)
        self._setupPluginTab()

        # Setup Help tab content
        self._setupHelpTab()

        # Setup Stage Import Data tab
        self._setupStageTab()

        # Set minimum size
        self.setMinimumSize(QSize(1280, 600))

        self.stageDataActivated.connect(self.onStageDataActivated)

    def _setupPluginTab(self):
        """Setup the Plugin tab with the existing UI components"""
        # Top buttons layout
        topButtons = QWidget()
        topButtons.setLayout(QHBoxLayout())
        topButtons.layout().setContentsMargins(0, 0, 0, 0)
        topButtons.setFixedHeight(40)  # Fix the height of the container

        # Refresh button on the left
        self.pbRefresh = QPushButton("Refresh Plugins", self)
        self.pbRefresh.setFixedSize(120, 30)
        self.pbRefresh.clicked.connect(self.refreshPlugins)
        topButtons.layout().addWidget(self.pbRefresh)

        # Add stretch to push Run button to the right
        topButtons.layout().addStretch()

        # Run button at the right
        self.pbRun = QPushButton("Run", self)
        self.pbRun.setFixedSize(100, 30)
        self.pbRun.clicked.connect(self.runSelectedPlugin)
        topButtons.layout().addWidget(self.pbRun)

        # Add buttons to plugin tab layout
        self.pluginTab.layout().addWidget(topButtons)

        # Create a splitter for the main content
        self.splitter = QSplitter(Qt.Horizontal)

        # Left side - Plugin list
        leftContainer = QWidget()
        leftContainer.setLayout(QVBoxLayout())
        leftContainer.layout().setContentsMargins(0, 0, 0, 0)

        # Create a container for the header
        headerContainer = QWidget()
        headerContainer.setLayout(QVBoxLayout())
        headerContainer.layout().setContentsMargins(0, 0, 0, 0)

        # Add a header label
        headerLabel = QLabel("Plugin Location")
        headerLabel.setAlignment(Qt.AlignCenter)
        headerLabel.setStyleSheet("font-weight: bold; padding: 5px;")
        headerContainer.layout().addWidget(headerLabel)

        # Add search/filter box
        self.filterContainer = QWidget()
        self.filterContainer.setLayout(QHBoxLayout())
        self.filterContainer.layout().setContentsMargins(5, 0, 5, 5)

        filterLabel = QLabel("Filter:")
        self.filterEdit = QLineEdit()
        self.filterEdit.setPlaceholderText("Type to filter plugins...")
        self.filterEdit.textChanged.connect(self._filterPlugins)

        self.filterContainer.layout().addWidget(filterLabel)
        self.filterContainer.layout().addWidget(self.filterEdit)

        # Clear filter button
        self.clearFilterButton = QPushButton("Clear")
        self.clearFilterButton.setFixedWidth(60)
        self.clearFilterButton.clicked.connect(self._clearFilter)
        self.filterContainer.layout().addWidget(self.clearFilterButton)

        headerContainer.layout().addWidget(self.filterContainer)

        # Create the plugin list widget
        self.pluginList = QListWidget()
        self.pluginList.setSelectionMode(QAbstractItemView.SingleSelection)
        self.pluginList.itemSelectionChanged.connect(self.onPluginSelected)
        self.pluginList.itemDoubleClicked.connect(self.onPluginDoubleClicked)
        self.pluginList.setContextMenuPolicy(Qt.CustomContextMenu)
        self.pluginList.customContextMenuRequested.connect(self._showPluginContextMenu)

        # Add the header and list to the container
        headerContainer.layout().addWidget(self.pluginList)
        leftContainer.layout().addWidget(headerContainer)

        # Add left container to splitter
        self.splitter.addWidget(leftContainer)

        # Right side - Details and execution
        self.detailsWidget = QWidget()
        self.detailsWidget.setLayout(QVBoxLayout())
        self.detailsWidget.layout().setContentsMargins(0, 0, 0, 0)

        # Create a text area for plugin details
        self.detailsText = QTextEdit()
        self.detailsText.setReadOnly(True)
        self.detailsText.setPlaceholderText("Select a plugin to view details")

        # Add details text to details widget
        self.detailsWidget.layout().addWidget(self.detailsText)

        # Create a container for parameters and output
        paramsOutputContainer = QWidget()
        paramsOutputContainer.setLayout(QHBoxLayout())
        paramsOutputContainer.layout().setContentsMargins(0, 5, 0, 0)
        self.detailsWidget.layout().addWidget(paramsOutputContainer)

        # Left side of params/output - Parameters
        paramsContainer = QWidget()
        paramsContainer.setLayout(QVBoxLayout())
        paramsContainer.layout().setContentsMargins(0, 0, 5, 0)
        paramsOutputContainer.layout().addWidget(paramsContainer)

        # Create a label for the parameters section
        self.paramHeader = QLabel("Custom Parameters:")
        self.paramHeader.setVisible(False)  # Hide initially
        paramsContainer.layout().addWidget(self.paramHeader)

        # Create a widget to hold parameter inputs
        self.paramWidget = QWidget()
        self.paramWidget.setLayout(QFormLayout())
        self.paramWidget.setVisible(False)  # Hide initially
        paramsContainer.layout().addWidget(self.paramWidget)

        # Add stretch to push everything to the top
        paramsContainer.layout().addStretch()

        # Right side - Output
        outputContainer = QWidget()
        outputContainer.setLayout(QVBoxLayout())
        outputContainer.layout().setContentsMargins(5, 0, 0, 0)
        paramsOutputContainer.layout().addWidget(outputContainer)

        # Create a label for the output section
        outputHeader = QLabel("Execution Output:")
        outputContainer.layout().addWidget(outputHeader)

        # Create a text area for plugin output
        self.outputText = QTextEdit()
        self.outputText.setPlaceholderText("Output will appear here when plugin is executed")
        outputContainer.layout().addWidget(self.outputText)

        # Add a container for the Clear button in the right pane
        clearContainer = QWidget()
        clearContainer.setLayout(QHBoxLayout())
        clearContainer.layout().setContentsMargins(0, 5, 0, 0)

        # Add a spacer to push the button to the right
        clearContainer.layout().addStretch()

        # Clear button in the right pane
        self.pbClear = QPushButton("Clear", self)
        self.pbClear.setFixedSize(100, 30)  # Fixed size instead of just height
        self.pbClear.clicked.connect(self.clearDetails)
        clearContainer.layout().addWidget(self.pbClear)

        # Add the clear button container to the output container
        outputContainer.layout().addWidget(clearContainer)

        self.splitter.addWidget(self.detailsWidget)

        # Set initial sizes for the splitter
        self.splitter.setSizes([200, 400])

        # Add splitter to plugin tab layout
        self.pluginTab.layout().addWidget(self.splitter)

        # Load plugins
        self.plugins = {}
        self.loadPlugins()

    def _setupHelpTab(self):
        """Setup the Help tab with information about reserved arguments"""
        # Create a scroll area for the help content
        scrollArea = QScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # Create a widget to hold the help content
        helpWidget = QWidget()
        helpWidget.setLayout(QVBoxLayout())
        helpWidget.layout().setAlignment(Qt.AlignTop)
        scrollArea.setWidget(helpWidget)

        # Add title
        titleLabel = QLabel("Plugin System Help")
        titleLabel.setStyleSheet("font-size: 16pt; font-weight: bold; margin-bottom: 10px;")
        helpWidget.layout().addWidget(titleLabel)

        # Add introduction
        introText = QLabel(
            "The plugin system allows you to extend the application with custom functionality. "
            "This help page explains the special parameter types available to plugins."
        )
        introText.setWordWrap(True)
        helpWidget.layout().addWidget(introText)

        # Add section: Reserved Data Arguments
        self._addHelpSection(
            helpWidget,
            "Reserved Data Arguments",
            "These arguments provide access to the data currently loaded in the application:",
            RESERVED_ARGS,
            {
                "bom_data": "Bill of Materials data as a DataFrame",
                "general_data": "General project data as a DataFrame",
                "spool_data": "Spool data as a DataFrame",
                "spec_data": "Specification data as a DataFrame",
                "outlier_data": "Outlier data as a DataFrame",
                "rfq_data": "Request for Quote data as a DataFrame",
                "project_source": "Project source data as a tuple (projectId, filename)"
            }
        )

        # Add section: File Input Arguments
        self._addHelpSection(
            helpWidget,
            "File Input Arguments",
            "These arguments provide a file picker UI for selecting input or output files:",
            RESERVED_FILEINPUT_ARGS,
            {
                "bom_file": "Select a Bill of Materials file",
                "general_file": "Select a General data file",
                "spool_file": "Select a Spool data file",
                "spec_file": "Select a Specification file",
                "outlier_file": "Select an Outlier data file",
                "rfq_file": "Select a Request for Quote file",
                "save_file": "Select a file to save output (shows a Save dialog)",
                "general_data_workbook": "Select a general data workbook for column filtering"
            }
        )

        # Add section: Directory Input Arguments
        self._addHelpSection(
            helpWidget,
            "Directory Input Arguments",
            "These arguments provide a directory picker UI for selecting folders:",
            RESERVED_DIRINPUT_ARGS,
            {
                "save_dir": "Select a directory to save output files"
            }
        )

        # Add section: Signal Arguments
        self._addHelpSection(
            helpWidget,
            "Signal Arguments",
            "These arguments provide access to application signals that plugins can connect to or emit:",
            RESERVED_SIGNAL_ARGS,
            {
                "signals": "Dictionary of available signals that can be used by plugins"
            }
        )

        # Add the scroll area to the help tab
        self.helpTab.layout().addWidget(scrollArea)

    def _setupStageTab(self):
        """Setup the Stage Import Data tab"""
        # Create a label for the tab
        titleLabel = QLabel("Staged Data for Import")
        titleLabel.setStyleSheet("font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        self.stageTab.layout().addWidget(titleLabel)

        # Add description
        descLabel = QLabel(
            "This tab shows data that has been staged for import by plugins. "
            "You can review the data and import it into the application when ready."
        )
        descLabel.setWordWrap(True)
        self.stageTab.layout().addWidget(descLabel)

        # Create a table to display staged data
        self.stagedDataTable = QTableWidget()
        self.stagedDataTable.setColumnCount(4)
        self.stagedDataTable.setHorizontalHeaderLabels(["Table", "Rows", "Import Mode", "Actions"])
        self.stagedDataTable.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.stagedDataTable.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.stagedDataTable.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.stagedDataTable.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.stagedDataTable.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.stageTab.layout().addWidget(self.stagedDataTable)

        # Add button container
        buttonContainer = QWidget()
        buttonContainer.setLayout(QHBoxLayout())
        buttonContainer.layout().setContentsMargins(0, 10, 0, 0)

        # Add clear button
        self.clearButton = QPushButton("Clear All Staged Data")
        self.clearButton.setEnabled(False)
        self.clearButton.clicked.connect(self.onClearStagedData)
        buttonContainer.layout().addWidget(self.clearButton)

        # Add spacer
        buttonContainer.layout().addStretch()

        # Add import button
        self.importButton = QPushButton("Import All Staged Data")
        self.importButton.setEnabled(False)
        self.importButton.clicked.connect(self.onImportStagedData)
        buttonContainer.layout().addWidget(self.importButton)

        self.stageTab.layout().addWidget(buttonContainer)

        # Add stretch to push everything to the top
        self.stageTab.layout().addStretch()

    def _addHelpSection(self, parent, title, description, args_list, args_descriptions):
        """Add a help section with a title, description, and list of arguments"""
        # Add section title
        sectionTitle = QLabel(title)
        sectionTitle.setStyleSheet("font-size: 14pt; font-weight: bold; margin-top: 20px;")
        parent.layout().addWidget(sectionTitle)

        # Add section description
        sectionDesc = QLabel(description)
        sectionDesc.setWordWrap(True)
        parent.layout().addWidget(sectionDesc)

        # Create a table for the arguments
        argsTable = QTableWidget()
        argsTable.setColumnCount(2)
        argsTable.setRowCount(len(args_list))
        argsTable.setHorizontalHeaderLabels(["Argument", "Description"])
        argsTable.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        argsTable.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        argsTable.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # Populate the table
        for i, arg in enumerate(args_list):
            # Argument name
            nameItem = QTableWidgetItem(arg)
            nameItem.setFont(QFont("Courier New"))
            argsTable.setItem(i, 0, nameItem)

            # Argument description
            descItem = QTableWidgetItem(args_descriptions.get(arg, ""))
            argsTable.setItem(i, 1, descItem)

        # Set a reasonable height for the table
        tableHeight = min(200, (len(args_list) + 1) * 30)
        argsTable.setMinimumHeight(tableHeight)
        argsTable.setMaximumHeight(tableHeight)

        parent.layout().addWidget(argsTable)

    def loadPlugins(self):
        """
        Load all plugin functions from Python files in the src.plugins directory
        that start with 'plugin_' and contain functions that start with 'plugin_'.

        Searches in the plugins directory and one level of subdirectories.
        """
        self.pluginList.clear()
        self.plugins = {}

        # Get the plugins directory
        pluginsDir = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))) / "plugins"
        projectRoot = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

        if not pluginsDir.exists():
            self.pluginList.addItem("No plugins directory found")
            return

        # Find all Python files starting with plugin_
        pluginFiles = []

        # Search in the root plugins directory
        pluginFiles.extend(list(pluginsDir.glob("plugin_*.py")))

        # Also search one level into subdirectories
        pluginFiles.extend(list(pluginsDir.glob("*/plugin_*.py")))

        if not pluginFiles:
            self.pluginList.addItem("No plugin files found")
            return

        # Load each plugin file and find plugin functions
        for pluginFile in pluginFiles:
            moduleName = pluginFile.stem

            # For plugins in subdirectories, include the subdirectory in the module name
            relPath = pluginFile.relative_to(pluginsDir)
            parentDir = relPath.parent.name

            if parentDir and parentDir != '.':
                # Create a qualified module name including the subdirectory
                qualifiedModuleName = f"{parentDir}.{moduleName}"
            else:
                qualifiedModuleName = moduleName

            spec = importlib.util.spec_from_file_location(qualifiedModuleName, pluginFile)

            try:
                # Remove the module from sys.modules if it's already loaded
                if qualifiedModuleName in sys.modules:
                    del sys.modules[qualifiedModuleName]

                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # Calculate relative path from project root
                relPath = pluginFile.relative_to(projectRoot)

                # Find all functions in the module that start with plugin_
                for name, obj in inspect.getmembers(module):
                    if name.startswith("plugin_") and inspect.isfunction(obj):
                        # Include "src." prefix in the plugin ID
                        pluginId = f"src.{qualifiedModuleName}.{name}"
                        # Store both the function object and the file path
                        self.plugins[pluginId] = {
                            "func": obj,
                            "file_path": str(pluginFile),
                            "rel_path": str(relPath)
                        }
                        self.pluginList.addItem(pluginId)
            except Exception as e:
                self.pluginList.addItem(f"Error loading {qualifiedModuleName}: {str(e)}")

    def refreshPlugins(self):
        """Refresh the list of available plugins"""
        # Store the currently selected plugin
        selectedItems = self.pluginList.selectedItems()
        selectedPlugin = selectedItems[0].text() if selectedItems else None

        # Reload all plugins
        self.loadPlugins()

        # Display a message
        self.detailsText.setPlainText("Plugins refreshed successfully.")

        # Try to reselect the previously selected plugin
        if selectedPlugin:
            items = self.pluginList.findItems(selectedPlugin, Qt.MatchExactly)
            if items:
                self.pluginList.setCurrentItem(items[0])
                self.onPluginSelected()
            else:
                # Remove "src." prefix for the message to maintain compatibility with previous selections
                if selectedPlugin.startswith("src."):
                    originalPlugin = selectedPlugin[4:]  # Remove "src." prefix
                    items = self.pluginList.findItems(f"src.{originalPlugin}", Qt.MatchExactly)
                    if items:
                        self.pluginList.setCurrentItem(items[0])
                        self.onPluginSelected()
                        return

                self.detailsText.setPlainText(f"Previously selected plugin '{selectedPlugin}' is no longer available.")

        # Show a confirmation message
        QMessageBox.information(self, "Plugins Refreshed", "Plugin list has been refreshed.")

    def clearDetails(self):
        """Clear the details text edit area"""
        self.detailsText.clear()
        self.outputText.clear()

        # Clear custom parameter widgets
        self._clearParamWidgets()

        # If a plugin is selected, restore its basic information
        selectedItems = self.pluginList.selectedItems()
        if selectedItems:
            self.onPluginSelected()
        else:
            self.detailsText.setPlaceholderText("Select a plugin to view details")
            self.outputText.setPlaceholderText("Output will appear here when plugin is executed")

    def _clearParamWidgets(self):
        """Clear all parameter input widgets"""
        # Remove all widgets from the form layout
        while self.paramWidget.layout().rowCount() > 0:
            self.paramWidget.layout().removeRow(0)

        # Clear the widget dictionary
        self.customParamWidgets = {}

        # Hide the parameter section
        self.paramHeader.setVisible(False)
        self.paramWidget.setVisible(False)

    def onPluginSelected(self):
        """Handle plugin selection change"""
        selected_items = self.pluginList.selectedItems()
        if not selected_items:
            self.detailsText.setPlainText("")
            self.detailsText.setPlaceholderText("Select a plugin to view details")
            self.pbRun.setEnabled(False)
            self._clearParamWidgets()
            return

        pluginId = selected_items[0].text()
        if pluginId in self.plugins:
            pluginInfo = self.plugins[pluginId]
            pluginFunc = pluginInfo["func"]
            filePath = pluginInfo["file_path"]
            relPath = pluginInfo["rel_path"]

            # Get function signature
            signature = str(inspect.signature(pluginFunc))

            # Build a comprehensive description
            description = []

            # Add function name and signature
            description.append(f"Function: {pluginId}{signature}")
            description.append(f"Path: {relPath}")
            description.append("")  # Empty line for spacing

            # Add docstring if available
            if pluginFunc.__doc__:
                # Clean up the docstring - remove leading whitespace from each line
                docLines = pluginFunc.__doc__.strip().split('\n')
                cleanedDoc = '\n'.join(line.strip() for line in docLines)
                description.append("Description:")
                description.append(cleanedDoc)
            else:
                description.append("No description available for this plugin.")

            # Add parameter information if available
            sig = inspect.signature(pluginFunc)
            hasParams = False

            if sig.parameters:
                description.append("")  # Empty line for spacing
                description.append("Parameters:")
                for name, param in sig.parameters.items():
                    paramDesc = f"  {name}"
                    if param.annotation != inspect.Parameter.empty:
                        try:
                            paramDesc += f" ({param.annotation.__name__})"
                        except AttributeError:
                            # Handle complex types like typing.List[str]
                            paramDesc += f" ({str(param.annotation)})"
                    if param.default != inspect.Parameter.empty:
                        try:
                            # Safely handle default values that might cause truth value issues
                            defaultStr = str(param.default)
                            paramDesc += f" = {defaultStr}"
                        except Exception as e:
                            # If we can't convert to string, just note there's a default
                            paramDesc += " = [has default]"
                    description.append(paramDesc)

                    # Only create input fields for non-reserved parameters
                    if name not in RESERVED_ARGS:
                        hasParams = True

            # Set the text
            self.detailsText.setPlainText('\n'.join(description))
            self.pbRun.setEnabled(True)

            # Create input fields for parameters
            self._createParamInputs(pluginFunc)

            # Show parameter section if there are custom parameters
            self.paramHeader.setVisible(hasParams)
            self.paramWidget.setVisible(hasParams)

        else:
            # Show information message for invalid plugin
            if pluginId.startswith("Error loading"):
                errorMsg = pluginId.replace("Error loading ", "")
                self.detailsText.setPlainText(f"This plugin could not be loaded.\n\nError details:\n{errorMsg}")
                QMessageBox.information(
                    self,
                    "Invalid Plugin",
                    f"This item represents a plugin that failed to load.\n\nError details:\n{errorMsg}"
                )
            else:
                self.detailsText.setPlainText("Plugin information not available")
                QMessageBox.information(
                    self,
                    "Invalid Plugin",
                    "The selected item is not a valid plugin or cannot be executed."
                )
            self.pbRun.setEnabled(False)
            self._clearParamWidgets()

    def _createParamInputs(self, pluginFunc):
        """Create input fields for plugin parameters"""
        # Clear existing parameter widgets
        self._clearParamWidgets()

        # Get function signature
        sig = inspect.signature(pluginFunc)
        if not sig.parameters:
            return

        # Check if the plugin uses signals
        uses_signals = "signals" in sig.parameters

        # Create a label to show if the plugin uses signals
        if uses_signals:
            signalLabel = QLabel("✓ Uses application signals")
            signalLabel.setStyleSheet("color: green; font-weight: bold;")
            self.paramWidget.layout().addWidget(signalLabel)

        # Check if the plugin uses project_source
        uses_project_source = "project_source" in sig.parameters

        # Create a project source combobox if needed
        if uses_project_source:
            # Create a widget to hold the combobox
            projectSourceWidget = QWidget()
            projectSourceWidget.setLayout(QHBoxLayout())
            projectSourceWidget.layout().setContentsMargins(0, 0, 0, 0)

            # Create the combobox
            projectSourceCombo = QComboBox()
            projectSourceCombo.setMinimumWidth(250)

            # Add a refresh button
            refreshButton = QPushButton("Refresh")
            refreshButton.setFixedWidth(80)

            # Add widgets to layout
            projectSourceWidget.layout().addWidget(projectSourceCombo)
            projectSourceWidget.layout().addWidget(refreshButton)

            # Store the combobox for later retrieval
            projectSourceWidget.comboBox = projectSourceCombo

            projectSourceCombo.setEditable(True)

            # Populate the combobox
            def populateProjectSourceCombo():
                projectSourceCombo.clear()
                try:
                    df = self.fetchProjectSources()
                    if not df.empty:
                        # Add a placeholder item
                        projectSourceCombo.addItem("Select a project source...", None)

                        items = []

                        # Add items from the dataframe
                        for _, row in df.iterrows():
                            project_id = row['projectId']
                            filename = row['filename']
                            # Display format: "ProjectID: X - Filename"
                            display_text = f"ProjectID: {project_id} - {filename}"
                            # Store the tuple (projectId, filename) as item data
                            items.append(display_text)
                            projectSourceCombo.addItem(display_text, (project_id, filename))

                        # Set up completer to only allow valid options
                        completer = QCompleter(items, projectSourceCombo)
                        completer.setCaseSensitivity(Qt.CaseInsensitive)
                        completer.setFilterMode(Qt.MatchContains)  # optional: match substring
                        projectSourceCombo.setCompleter(completer)
                except Exception as e:
                    print(f"Error populating project sources: {e}")

            # Connect the refresh button
            refreshButton.clicked.connect(populateProjectSourceCombo)

            # Populate initially
            populateProjectSourceCombo()

            # Add the widget to the form layout
            self.paramWidget.layout().addRow("Project Source", projectSourceWidget)
            self.customParamWidgets["project_source"] = projectSourceWidget

            # Make the parameter section visible
            self.paramHeader.setVisible(True)
            self.paramWidget.setVisible(True)

        # Create input fields for each parameter
        for name, param in sig.parameters.items():
            # Skip reserved arguments
            if name in RESERVED_ARGS:
                continue

            # Skip signals parameter
            if name in RESERVED_SIGNAL_ARGS:
                continue

            # Create a label for the parameter
            label = f"{name}"
            if param.annotation != inspect.Parameter.empty:
                try:
                    label += f" ({param.annotation.__name__})"
                except AttributeError:
                    label += f" ({str(param.annotation)})"

            # Create an appropriate input widget based on the parameter type
            inputWidget = None

            # Get default value if available
            default_value = None
            if param.default is not None and param.default != inspect.Parameter.empty:
                default_value = param.default

            # Check if this is a file input parameter
            if name in RESERVED_FILEINPUT_ARGS:
                # Create a file picker widget
                fileWidget = QWidget()
                fileWidget.setLayout(QHBoxLayout())
                fileWidget.layout().setContentsMargins(0, 0, 0, 0)

                # Create a text field for the file path
                filePathEdit = QLineEdit()
                if default_value:
                    try:
                        filePathEdit.setText(str(default_value))
                    except:
                        pass

                # Create a browse button
                browseButton = QPushButton("Browse...")
                browseButton.setFixedWidth(80)

                # Connect the browse button
                def openFileDialog(edit=filePathEdit, save_mode=name == "save_file", param_name=name):
                    # Set appropriate file filters based on parameter name
                    file_filter = "All Files (*)"
                    dialog_title = "Save File" if save_mode else "Open File"

                    if param_name == "general_data_workbook":
                        file_filter = "Excel Files (*.xlsx *.xls);;All Files (*)"
                        dialog_title = "Select General Data Workbook"
                    elif param_name == "export_profile":
                        file_filter = "Excel Files (*.xlsx *.xls);;All Files (*)"
                        dialog_title = "Select Export Profile"
                    elif "file" in param_name and ("bom" in param_name or "general" in param_name or "rfq" in param_name):
                        file_filter = "Excel Files (*.xlsx *.xls);;Feather Files (*.feather);;All Files (*)"

                    if save_mode:
                        filePath, _ = QFileDialog.getSaveFileName(self, dialog_title, "", file_filter)
                    else:
                        filePath, _ = QFileDialog.getOpenFileName(self, dialog_title, "", file_filter)

                    if filePath:
                        edit.setText(filePath)

                browseButton.clicked.connect(openFileDialog)

                # Add widgets to layout
                fileWidget.layout().addWidget(filePathEdit)
                fileWidget.layout().addWidget(browseButton)

                # Store the line edit for later retrieval
                fileWidget.filePathEdit = filePathEdit

                inputWidget = fileWidget

            # Check if this is a directory input parameter
            elif name in RESERVED_DIRINPUT_ARGS:
                # Create a directory picker widget
                dirWidget = QWidget()
                dirWidget.setLayout(QHBoxLayout())
                dirWidget.layout().setContentsMargins(0, 0, 0, 0)

                # Create a text field for the directory path
                dirPathEdit = QLineEdit()
                if default_value:
                    try:
                        dirPathEdit.setText(str(default_value))
                    except:
                        pass

                # Create a browse button
                browseButton = QPushButton("Browse...")
                browseButton.setFixedWidth(80)

                # Connect the browse button
                def openDirDialog(edit=dirPathEdit):
                    dirPath = QFileDialog.getExistingDirectory(self, "Select Directory")
                    if dirPath:
                        dirPathEdit.setText(dirPath)
                        # edit.setText(dirPath)

                browseButton.clicked.connect(openDirDialog)

                # Add widgets to layout
                dirWidget.layout().addWidget(dirPathEdit)
                dirWidget.layout().addWidget(browseButton)

                # Store the line edit for later retrieval
                dirWidget.dirPathEdit = dirPathEdit

                inputWidget = dirWidget

            # Check the parameter type for other widgets
            elif param.annotation == tuple or (default_value is not None and isinstance(default_value, tuple)):
                inputWidget = QComboBox(self)
                inputWidget.setEditable(True)
                # Set up completer to only allow valid options
                completer = QCompleter(default_value, inputWidget)
                completer.setCaseSensitivity(Qt.CaseInsensitive)
                completer.setFilterMode(Qt.MatchContains)  # optional: match substring
                inputWidget.setCompleter(completer)
                inputWidget.addItems(default_value)

            elif param.annotation == bool or (default_value is not None and isinstance(default_value, bool)):
                # Boolean parameter - use checkbox
                inputWidget = QCheckBox()
                if default_value is not None and isinstance(default_value, bool):
                    inputWidget.setChecked(default_value)
            elif param.annotation == int or (default_value is not None and isinstance(default_value, int)):
                # Integer parameter - use spin box
                inputWidget = QSpinBox()
                inputWidget.setRange(-1000000, 1000000)
                if default_value is not None and isinstance(default_value, int):
                    inputWidget.setValue(default_value)
            elif param.annotation == float or (default_value is not None and isinstance(default_value, float)):
                # Float parameter - use double spin box
                inputWidget = QDoubleSpinBox()
                inputWidget.setRange(-1000000, 1000000)
                inputWidget.setDecimals(4)
                if default_value is not None and isinstance(default_value, float):
                    inputWidget.setValue(default_value)
            elif param.annotation == datetime.datetime or str(param.annotation).find('datetime') >= 0:
                # Datetime parameter - use a custom widget with date picker and current time button
                datetimeWidget = QWidget()
                datetimeWidget.setLayout(QHBoxLayout())
                datetimeWidget.layout().setContentsMargins(0, 0, 0, 0)

                # Create a date time edit widget
                dateTimeEdit = QDateTimeEdit()
                dateTimeEdit.setCalendarPopup(True)
                dateTimeEdit.setDisplayFormat("yyyy-MM-dd HH:mm:ss")

                # Set current date/time as default
                current_time = datetime.datetime.now()
                dateTimeEdit.setDateTime(QDateTime(
                    current_time.year, current_time.month, current_time.day,
                    current_time.hour, current_time.minute, current_time.second
                ))

                # Add a button to set current time
                nowButton = QPushButton("Now")
                nowButton.setToolTip("Set to current timestamp")
                nowButton.setFixedWidth(50)

                # Connect the button to update the datetime
                def setCurrentTime():
                    now = datetime.datetime.now()
                    dateTimeEdit.setDateTime(QDateTime(
                        now.year, now.month, now.day,
                        now.hour, now.minute, now.second
                    ))

                nowButton.clicked.connect(setCurrentTime)

                # Add widgets to layout
                datetimeWidget.layout().addWidget(dateTimeEdit)
                datetimeWidget.layout().addWidget(nowButton)

                # Store both widgets for later retrieval
                datetimeWidget.dateTimeEdit = dateTimeEdit

                inputWidget = datetimeWidget
            else:
                # Default to text input for all other types
                inputWidget = QLineEdit()
                if default_value is not None:
                    try:
                        inputWidget.setText(str(default_value))
                    except:
                        pass

            # Add the widget to the form layout
            if inputWidget:
                self.paramWidget.layout().addRow(label, inputWidget)
                self.customParamWidgets[name] = inputWidget

    def onPluginDoubleClicked(self, item):
        """Handle double-click on a plugin item to run it"""
        # Make sure the item is valid
        if item and item.text() in self.plugins:
            # Run the plugin directly - the runSelectedPlugin method has its own confirmation
            self.runSelectedPlugin()

    def runSelectedPlugin(self):
        """Run the selected plugin function"""
        selectedItems = self.pluginList.selectedItems()
        if not selectedItems:
            return

        # Get table data if the function is available
        if callable(self.getAllTableData):
            data = self.getAllTableData()
            self.paramaters = data

        # Get custom parameter values from input widgets
        self._getCustomParamValues()

        pluginId = selectedItems[0].text()
        if pluginId in self.plugins:
            try:
                # Get the plugin function
                pluginFunc = self.plugins[pluginId]["func"]

                # Check if the plugin needs signals and add them to parameters
                sig = inspect.signature(pluginFunc)
                if "signals" in sig.parameters:
                    self.paramaters["signals"] = self.signalMap

                # Show confirmation dialog
                confirm = QMessageBox.question(
                    self,
                    "Confirm Plugin Execution",
                    f"Are you sure you want to run the plugin function:\n\n{pluginId}?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No  # Default is No for safety
                )

                if confirm != QMessageBox.Yes:
                    return

                # Clear the output area
                self.outputText.clear()

                # Get current datetime and store start time
                startTime = datetime.datetime.now()
                current_time = startTime.strftime("%Y-%m-%d %H:%M:%S")

                # Add header with function name and execution time
                self.outputText.setPlainText(f"--- EXECUTING {pluginId} ---\n")
                self.outputText.append(f"Started: {current_time}\n")

                # Get function signature to determine which parameters to pass
                valid_params = {}

                # Only include parameters that exist in the function signature
                try:
                    for param_name, param in sig.parameters.items():
                        if param_name in self.paramaters:
                            valid_params[param_name] = self.paramaters[param_name]

                    # Show parameters being passed
                    if valid_params:
                        param_names = ", ".join(valid_params.keys())
                        self.outputText.append(f"Parameters: {param_names}\n")
                    else:
                        self.outputText.append("No parameters passed\n")
                except Exception as e:
                    self.outputText.append(f"Error preparing parameters: {str(e)}\n")

                self.outputText.append("--- OUTPUT ---")

                # Capture stdout to redirect print statements to the text area
                capturedOutput = io.StringIO()

                # Run the plugin function and capture its output
                with contextlib.redirect_stdout(capturedOutput):
                    # Pass only the parameters that exist in the function signature
                    result = pluginFunc(**valid_params)

                # Get the captured output
                output = capturedOutput.getvalue()

                # Update the text area with the captured output
                if output:
                    self.outputText.append(output)

                # Display result (even if None)
                if result is not None:
                    # Check if result is a DataFrame
                    if hasattr(result, 'shape') and hasattr(result, 'iloc'):
                        # It's a DataFrame
                        self.outputText.append(f"\n--- RESULT ---\nDataFrame returned with {len(result)} rows")
                    else:
                        # Not a DataFrame, display as normal
                        self.outputText.append(f"\n--- RESULT ---\n{str(result)}")
                else:
                    self.outputText.append("\n--- RESULT ---\nNone")

                # Calculate execution time
                endTime = datetime.datetime.now()
                timeDiff = endTime - startTime
                totalSeconds = timeDiff.total_seconds()
                minutes = int(totalSeconds // 60)
                seconds = totalSeconds % 60

                # Format the completion time
                endTimeStr = endTime.strftime("%Y-%m-%d %H:%M:%S")
                if minutes > 0:
                    executionTime = f"{minutes} min {seconds:.2f} sec"
                else:
                    executionTime = f"{seconds:.2f} sec"

                # Add completion time with execution duration
                self.outputText.append(f"\nCompleted: {endTimeStr}")
                self.outputText.append(f"Execution time: {executionTime}")

                def toQUrl(string) -> QUrl:
                    return QUrl.fromLocalFile(string).toString()

                # Also show a message box with the result
                if hasattr(result, 'shape') and hasattr(result, 'iloc'):
                    # It's a DataFrame, show simplified message
                    QMessageBox.information(self, "Plugin Result", f"DataFrame returned with {len(result)} rows")
                elif isinstance(result, dict):
                    msg = ""
                    for key, value in result.items():
                        if key.endswith(("filename", "_file", "_dir")):
                            msg += f"<br><a href='{toQUrl(value)}'>{key}: {value}</a>"
                        else:
                            msg += f"<br>{key}: {value}"

                    msgBox = QMessageBox()
                    msgBox.setTextFormat(Qt.TextFormat.RichText)
                    msgBox.setWindowTitle("Plugin Result              ")  # Enlarges message box
                    msgBox.setIcon(QMessageBox.Icon.Information)
                    msgBox.setText(msg)
                    msgBox.exec()
                else:
                    QMessageBox.information(self, "Plugin Result", str(result))
            except Exception as e:
                # Show error in message box
                QMessageBox.critical(self, "Plugin Error", f"Error running plugin: {str(e)}")

                # Calculate execution time for error case
                errorTime = datetime.datetime.now()
                timeDiff = errorTime - startTime
                totalSeconds = timeDiff.total_seconds()
                minutes = int(totalSeconds // 60)
                seconds = totalSeconds % 60

                if minutes > 0:
                    executionTime = f"{minutes} min {seconds:.2f} sec"
                else:
                    executionTime = f"{seconds:.2f} sec"

                # Also append error to the text area
                errorText = f"\n\n--- ERROR ---\n{str(e)}"
                self.outputText.append(errorText)

                # Add error time with execution duration
                errorTimeStr = errorTime.strftime("%Y-%m-%d %H:%M:%S")
                self.outputText.append(f"\nError occurred: {errorTimeStr}")
                self.outputText.append(f"Execution time: {executionTime}")

    def _getCustomParamValues(self):
        """Get values from custom parameter input widgets and add to parameters dict"""
        for name, widget in self.customParamWidgets.items():
            # Get the value based on widget type
            if isinstance(widget, QCheckBox):
                self.paramaters[name] = widget.isChecked()
            elif isinstance(widget, QSpinBox):
                self.paramaters[name] = widget.value()
            elif isinstance(widget, QDoubleSpinBox):
                self.paramaters[name] = widget.value()
            elif isinstance(widget, QWidget) and hasattr(widget, 'dateTimeEdit'):
                # Handle custom datetime widget
                dt = widget.dateTimeEdit.dateTime()
                self.paramaters[name] = datetime.datetime(
                    dt.date().year(), dt.date().month(), dt.date().day(),
                    dt.time().hour(), dt.time().minute(), dt.time().second()
                )
            elif isinstance(widget, QWidget) and hasattr(widget, 'filePathEdit'):
                # Handle file picker widget
                self.paramaters[name] = widget.filePathEdit.text()
            elif isinstance(widget, QWidget) and hasattr(widget, 'dirPathEdit'):
                # Handle directory picker widget
                self.paramaters[name] = widget.dirPathEdit.text()
            elif isinstance(widget, QWidget) and hasattr(widget, 'comboBox'):
                # Handle project source combobox
                combo = widget.comboBox
                current_index = combo.currentIndex()
                if current_index > 0:  # Skip the placeholder item
                    self.paramaters[name] = combo.itemData(current_index)
                else:
                    self.paramaters[name] = None
            elif isinstance(widget, QComboBox):
                # Handle project source combobox
                self.paramaters[name] = widget.currentText()
            elif isinstance(widget, QLineEdit):
                # Try to convert to appropriate type
                value = widget.text()
                # Try to convert to int or float if possible
                try:
                    if value.isdigit():
                        value = int(value)
                    elif value.replace('.', '', 1).isdigit() and value.count('.') <= 1:
                        value = float(value)
                except:
                    pass
                self.paramaters[name] = value

    def getAllTableData(self):
        pass

    def onStageDataActivated(self, data: dict):
        """Handle data staged for import by plugins"""
        # Store the staged data
        for payload_name, df in data.items():
            # Store data with default mode as replace
            self.stagedData[payload_name] = {"data": df, "mode": "replace"}

        # Update the staged data table
        self._updateStagedDataTable()

        # Switch to the Stage Import Data tab
        self.tabWidget.setCurrentWidget(self.stageTab)

        # Enable the buttons
        self.importButton.setEnabled(True)
        self.clearButton.setEnabled(True)

    def _updateStagedDataTable(self):
        """Update the staged data table with current staged data"""
        # Clear the table
        self.stagedDataTable.setRowCount(0)

        # Add rows for each staged data item
        for payload_name, data_info in self.stagedData.items():
            row = self.stagedDataTable.rowCount()
            self.stagedDataTable.insertRow(row)

            # Get the DataFrame
            df = data_info["data"]

            # Get the table name from the payload name
            table_name = get_table_from_payload(payload_name) or payload_name
            table_name = table_name.capitalize()

            # Add table name
            tableItem = QTableWidgetItem(table_name)
            self.stagedDataTable.setItem(row, 0, tableItem)

            # Add row count
            rowCountItem = QTableWidgetItem(str(len(df)))
            self.stagedDataTable.setItem(row, 1, rowCountItem)

            # Add mode selection combobox
            modeCombo = QComboBox()
            modeCombo.addItems(["replace", "append"])
            modeCombo.setCurrentText(data_info["mode"])
            modeCombo.setProperty("payload_name", payload_name)
            modeCombo.currentTextChanged.connect(self._onImportModeChanged)
            self.stagedDataTable.setCellWidget(row, 2, modeCombo)

            # Add remove button
            removeButton = QPushButton("Remove")
            removeButton.setProperty("payload_name", payload_name)
            removeButton.clicked.connect(self._onRemoveStagedData)

            # Add button to table
            self.stagedDataTable.setCellWidget(row, 3, removeButton)

    def _onImportModeChanged(self, mode):
        """Update the import mode when changed in the combobox"""
        # Get the combobox that was changed
        combo = self.sender()
        if not combo:
            return

        # Get the payload name from the combobox property
        payload_name = combo.property("payload_name")
        if payload_name in self.stagedData:
            # Update the mode
            self.stagedData[payload_name]["mode"] = mode

    def _onRemoveStagedData(self):
        """Remove staged data when the remove button is clicked"""
        # Get the button that was clicked
        button = self.sender()
        if not button:
            return

        # Get the payload name from the button property
        payload_name = button.property("payload_name")
        if payload_name not in self.stagedData:
            return

        # Get the table name for the confirmation message
        table_name = get_table_from_payload(payload_name) or payload_name
        table_name = table_name.capitalize()

        # Show confirmation dialog
        confirm = QMessageBox.question(
            self,
            "Confirm Remove",
            f"Are you sure you want to remove the staged data for {table_name}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # Default is No for safety
        )

        if confirm != QMessageBox.Yes:
            return

        # Remove the data
        del self.stagedData[payload_name]

        # Update the table
        self._updateStagedDataTable()

        # Disable buttons if no data left
        if not self.stagedData:
            self.importButton.setEnabled(False)
            self.clearButton.setEnabled(False)

    def onClearStagedData(self):
        """Clear all staged data"""
        if not self.stagedData:
            return

        # Show confirmation dialog
        confirm = QMessageBox.question(
            self,
            "Confirm Clear All",
            f"Are you sure you want to clear all staged data? This will remove {len(self.stagedData)} tables.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # Default is No for safety
        )

        if confirm != QMessageBox.Yes:
            return

        # Clear the staged data
        self.stagedData = {}

        # Update the table
        self._updateStagedDataTable()

        # Disable the buttons
        self.importButton.setEnabled(False)
        self.clearButton.setEnabled(False)

    def onImportStagedData(self):
        """Import all staged data into the application"""
        if not self.stagedData:
            return

        # Create a dictionary for results with separate append flags for each table
        import_data = {}

        # Process each staged item and track its import mode
        for payload_name, data_info in self.stagedData.items():
            # Add the DataFrame to the import data
            import_data[payload_name] = data_info["data"]

        # Create the complete import data structure
        staged_import = {
            "results": import_data,
            "append_tables": [payload_name for payload_name, data_info in self.stagedData.items()
                             if data_info["mode"] == "append"]
        }

        # Emit the importActivated signal with the staged data and append information
        self.importActivated.emit(staged_import)

        # Show confirmation message
        QMessageBox.information(
            self,
            "Data Imported",
            f"Successfully imported data into {len(self.stagedData)} tables."
        )

        # Clear the staged data
        self.stagedData = {}

        # Update the table
        self._updateStagedDataTable()

        # Disable the buttons
        self.importButton.setEnabled(False)
        self.clearButton.setEnabled(False)

    def _filterPlugins(self, text):
        """Filter the plugin list based on the search text"""
        # Store the original items' text
        if not hasattr(self, 'originalPluginTexts'):
            self.originalPluginTexts = []
            for i in range(self.pluginList.count()):
                item = self.pluginList.item(i)
                if item:
                    self.originalPluginTexts.append(item.text())

        # Clear the list
        self.pluginList.clear()

        # Filter the items
        for plugin_text in self.originalPluginTexts:
            if text.lower() in plugin_text.lower():
                self.pluginList.addItem(plugin_text)

    def _clearFilter(self):
        """Clear the filter and restore the original plugin list"""
        # Clear the filter text
        self.filterEdit.clear()

        # Restore the original items
        if hasattr(self, 'originalPluginTexts'):
            self.pluginList.clear()
            for plugin_text in self.originalPluginTexts:
                self.pluginList.addItem(plugin_text)

    def _showPluginContextMenu(self, pos):
        """Show a context menu for the plugin list"""
        # Get the item under the cursor
        item = self.pluginList.itemAt(pos)
        if not item:
            return

        # Create a context menu
        menu = QMenu(self)

        # Add a copy option
        copyAction = QAction("Copy Plugin Name", self)
        copyAction.triggered.connect(lambda: self._copyPluginName(item.text()))
        menu.addAction(copyAction)

        # Show the menu
        menu.exec_(self.pluginList.viewport().mapToGlobal(pos))

    def _copyPluginName(self, plugin_name):
        """Copy the plugin name to the clipboard"""
        clipboard = QApplication.clipboard()
        clipboard.setText(plugin_name)

    def fetchProjectSources(self):
        statement = ("SELECT projectId, filename from ProjectSources")
        try:
            with self.db.connect() as conn:
                df = pd.read_sql_query(statement, conn)

                if self.projectId:
                    df = df[df["projectId"] == self.projectId]

                return df
        except Exception as e:
            print(f"Failed to refresh all project sources info: {e}")
            QMessageBox.warning(self, "Database Error", f"Failed to refresh project sources: {e}")
            return pd.DataFrame(columns=["projectId", "filename"])

if __name__ == "__main__":
    import sys

    app = QApplication(sys.argv)

    # Create and show the plugin dialog
    dialog = PluginDialog()
    dialog.show()

    # Run the application
    sys.exit(app.exec())