import phonenumbers
from pprint import pp
from phonenumbers.phonenumberutil import (
    region_code_for_country_code,
    region_code_for_number,
)
from src.util import parse_phone_number
from src.pyside_util import get_resource_qicon
from os import path


pn = phonenumbers.parse('+4420832661177')

mapped = {}
for code in phonenumbers.supported_calling_codes():
    rc = phonenumbers.region_code_for_country_code(code)
    mapped[code] = rc
    p = f"src/resources/flags/3x2/{rc}.svg"
    print(rc, p, path.exists(p))

get_resource_qicon("wqdqw")

# pp(mapped)