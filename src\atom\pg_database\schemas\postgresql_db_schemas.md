# PostgreSQL Database Schemas for ATOM

Generated on: 2025-04-18

## atem_bom_component_mapping

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_bom_component_mapping_id_seq'::regclass) | PRIMARY KEY (id) |
| profile_id | integer | 32 | YES | NULL | |
| class_description | character varying | 255 | YES | NULL | |
| component_name | character varying | 255 | YES | NULL | |
| takeoff_category | character varying | 100 | YES | NULL | |
| general_category | character varying | 100 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |

## atem_client_profiles

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_client_profiles_id_seq'::regclass) | PRIMARY KEY (id) |
| client_id | integer | 32 | NO | | |
| profile_name | character varying | 100 | NO | | |
| description | text | | YES | NULL | |
| is_active | boolean | | YES | true | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |

## atem_clients

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_clients_id_seq'::regclass) | PRIMARY KEY (id) |
| client_name | character varying | 255 | NO | | |
| contact_name | character varying | 255 | YES | NULL | |
| contact_email | character varying | 255 | YES | NULL | |
| contact_phone | character varying | 50 | YES | NULL | |
| address | text | | YES | NULL | |
| notes | text | | YES | NULL | |
| is_active | boolean | | YES | true | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |

## atem_equiv_length_factors

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_equiv_length_factors_id_seq'::regclass) | PRIMARY KEY (id) |
| component_type | character varying | 100 | NO | | |
| size | character varying | 50 | YES | NULL | |
| factor | numeric | 10,4 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## atem_projects

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_projects_id_seq'::regclass) | PRIMARY KEY (id) |
| client_id | integer | 32 | NO | | |
| project_number | character varying | 100 | NO | | |
| project_name | character varying | 255 | NO | | |
| location | character varying | 255 | YES | NULL | |
| scope | text | | YES | NULL | |
| start_date | date | | YES | NULL | |
| end_date | date | | YES | NULL | |
| status | character varying | 50 | YES | 'active'::character varying | |
| notes | text | | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## atem_rfq

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_rfq_id_seq'::regclass) | PRIMARY KEY (id) |
| material_description | character varying | 255 | NO | | |
| size | character varying | 50 | NO | | |
| ef | character varying | 50 | YES | NULL | |
| sf | character varying | 50 | YES | NULL | |
| general_category | character varying | 100 | YES | NULL | |
| rfq_scope | character varying | 100 | YES | NULL | |
| unit_of_measure | character varying | 50 | YES | NULL | |
| size1 | character varying | 50 | YES | NULL | |
| size2 | character varying | 50 | YES | NULL | |
| schedule | character varying | 50 | YES | NULL | |
| rating | character varying | 50 | YES | NULL | |
| astm | character varying | 100 | YES | NULL | |
| grade | character varying | 50 | YES | NULL | |
| ansme_ansi | character varying | 100 | YES | NULL | |
| material | character varying | 100 | YES | NULL | |
| abbreviated_material | character varying | 50 | YES | NULL | |
| coating | character varying | 100 | YES | NULL | |
| forging | character varying | 100 | YES | NULL | |
| ends | character varying | 100 | YES | NULL | |
| item_tag | character varying | 100 | YES | NULL | |
| tie_point | character varying | 100 | YES | NULL | |
| pipe_category | character varying | 100 | YES | NULL | |
| valve_type | character varying | 100 | YES | NULL | |
| fitting_category | character varying | 100 | YES | NULL | |
| weld_category | character varying | 100 | YES | NULL | |
| bolt_category | character varying | 100 | YES | NULL | |
| gasket_category | character varying | 100 | YES | NULL | |
| answer_explanation | text | | YES | NULL | |
| review | boolean | | YES | NULL | |
| review_explanation | text | | YES | NULL | |
| last_updated | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| component_category | character varying | 100 | YES | NULL | |

## atem_rfq_input

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('atem_rfq_input_id_seq'::regclass) | PRIMARY KEY (id) |
| project_id | integer | 32 | NO | | |
| pdf_id | integer | 32 | YES | NULL | |
| material_description | character varying | 255 | YES | NULL | |
| size | character varying | 50 | YES | NULL | |
| ef | character varying | 50 | YES | NULL | |
| sf | character varying | 50 | YES | NULL | |
| general_category | character varying | 100 | YES | NULL | |
| rfq_scope | character varying | 100 | YES | NULL | |
| unit_of_measure | character varying | 50 | YES | NULL | |
| size1 | character varying | 50 | YES | NULL | |
| size2 | character varying | 50 | YES | NULL | |
| schedule | character varying | 50 | YES | NULL | |
| rating | character varying | 50 | YES | NULL | |
| astm | character varying | 100 | YES | NULL | |
| grade | character varying | 50 | YES | NULL | |
| ansme_ansi | character varying | 100 | YES | NULL | |
| material | character varying | 100 | YES | NULL | |
| abbreviated_material | character varying | 50 | YES | NULL | |
| coating | character varying | 100 | YES | NULL | |
| forging | character varying | 100 | YES | NULL | |
| ends | character varying | 100 | YES | NULL | |
| item_tag | character varying | 100 | YES | NULL | |
| tie_point | character varying | 100 | YES | NULL | |
| pipe_category | character varying | 100 | YES | NULL | |
| valve_type | character varying | 100 | YES | NULL | |
| fitting_category | character varying | 100 | YES | NULL | |
| weld_category | character varying | 100 | YES | NULL | |
| bolt_category | character varying | 100 | YES | NULL | |
| gasket_category | character varying | 100 | YES | NULL | |
| answer_explanation | text | | YES | NULL | |
| review | boolean | | YES | NULL | |
| review_explanation | text | | YES | NULL | |
| last_updated | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| component_category | character varying | 100 | YES | NULL | |
| quantity | character varying | 50 | YES | NULL | |

## bom

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('bom_id_seq'::regclass) | PRIMARY KEY (id) |
| project_id | integer | 32 | NO | | |
| pdf_id | integer | 32 | YES | NULL | |
| pos | character varying | 50 | YES | NULL | |
| material_description | character varying | 255 | YES | NULL | |
| size | character varying | 50 | YES | NULL | |
| ident | character varying | 100 | YES | NULL | |
| item | character varying | 100 | YES | NULL | |
| tag | character varying | 100 | YES | NULL | |
| quantity | character varying | 50 | YES | NULL | |
| status | character varying | 50 | YES | NULL | |
| nb | character varying | 50 | YES | NULL | |
| fluid | character varying | 100 | YES | NULL | |
| clean_spec | character varying | 100 | YES | NULL | |
| line_number | character varying | 100 | YES | NULL | |
| component_category | character varying | 100 | YES | NULL | |
| material_code | character varying | 100 | YES | NULL | |
| sch_class | character varying | 50 | YES | NULL | |
| parts | character varying | 100 | YES | NULL | |
| type | character varying | 100 | YES | NULL | |
| weight | character varying | 50 | YES | NULL | |
| number | character varying | 50 | YES | NULL | |
| remarks | text | | YES | NULL | |
| ident_code | character varying | 100 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| ef | character varying | 50 | YES | NULL | |
| sf | character varying | 50 | YES | NULL | |

## cross_reducing

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('cross_reducing_id_seq'::regclass) | PRIMARY KEY (id) |
| size | character varying | 50 | NO | | |
| size1 | character varying | 50 | NO | | |
| size2 | character varying | 50 | NO | | |
| description | character varying | 255 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## elbows_reducing

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('elbows_reducing_id_seq'::regclass) | PRIMARY KEY (id) |
| size | character varying | 50 | NO | | |
| size1 | character varying | 50 | NO | | |
| size2 | character varying | 50 | NO | | |
| description | character varying | 255 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## flange_data

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('flange_data_id_seq'::regclass) | PRIMARY KEY (id) |
| size | character varying | 50 | NO | | |
| class | character varying | 50 | NO | | |
| type | character varying | 100 | NO | | |
| diameter | numeric | 10,4 | YES | NULL | |
| thickness | numeric | 10,4 | YES | NULL | |
| bolt_circle | numeric | 10,4 | YES | NULL | |
| bolt_holes | integer | 32 | YES | NULL | |
| bolt_diameter | character varying | 50 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## public.general

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('general_id_seq'::regclass) | PRIMARY KEY (id) |
| project_id | integer | 32 | NO | | |
| pdf_id | integer | 32 | YES | NULL | |
| annot_markups | text | | YES | NULL | |
| area | character varying | 100 | YES | NULL | |
| avg_elevation | character varying | 50 | YES | NULL | |
| block_coordinates | text | | YES | NULL | |
| client_document_id | character varying | 100 | YES | NULL | |
| coordinates | text | | YES | NULL | |
| design_code | character varying | 100 | YES | NULL | |
| document_description | text | | YES | NULL | |
| document_id | character varying | 100 | YES | NULL | |
| document_title | character varying | 255 | YES | NULL | |
| drawing | character varying | 100 | YES | NULL | |
| elevation | character varying | 50 | YES | NULL | |
| flange_id | character varying | 100 | YES | NULL | |
| heat_trace | character varying | 100 | YES | NULL | |
| insulation_spec | character varying | 100 | YES | NULL | |
| insulation_thickness | character varying | 50 | YES | NULL | |
| line_number | character varying | 100 | YES | NULL | |
| max_elevation | character varying | 50 | YES | NULL | |
| medium_code | character varying | 100 | YES | NULL | |
| min_elevation | character varying | 50 | YES | NULL | |
| mod_date | character varying | 50 | YES | NULL | |
| paint_spec | character varying | 100 | YES | NULL | |
| pid | character varying | 100 | YES | NULL | |
| pipe_spec | character varying | 100 | YES | NULL | |
| pipe_standard | character varying | 100 | YES | NULL | |
| process_line_list | character varying | 255 | YES | NULL | |
| process_unit | character varying | 100 | YES | NULL | |
| project_no | character varying | 100 | YES | NULL | |
| project_name | character varying | 255 | YES | NULL | |
| pwht | character varying | 50 | YES | NULL | |
| revision | character varying | 50 | YES | NULL | |
| sequence | character varying | 50 | YES | NULL | |
| service | character varying | 100 | YES | NULL | |
| sheet | character varying | 50 | YES | NULL | |
| size | character varying | 50 | YES | NULL | |
| sys_build | character varying | 100 | YES | NULL | |
| sys_layout_valid | character varying | 50 | YES | NULL | |
| sys_document | character varying | 255 | YES | NULL | |
| sys_document_name | character varying | 255 | YES | NULL | |
| sys_filename | character varying | 255 | YES | NULL | |
| sys_path | character varying | 255 | YES | NULL | |
| system | character varying | 100 | YES | NULL | |
| total_sheets | character varying | 50 | YES | NULL | |
| unit | character varying | 50 | YES | NULL | |
| vendor_document_id | character varying | 100 | YES | NULL | |
| weld_id | character varying | 100 | YES | NULL | |
| weld_class | character varying | 100 | YES | NULL | |
| x_coord | character varying | 50 | YES | NULL | |
| xray | character varying | 50 | YES | NULL | |
| y_coord | character varying | 50 | YES | NULL | |
| lf | character varying | 50 | YES | NULL | |
| sf | character varying | 50 | YES | NULL | |
| ef | character varying | 50 | YES | NULL | |
| elbows_90 | character varying | 50 | YES | NULL | |
| elbows_45 | character varying | 50 | YES | NULL | |
| bevels | character varying | 50 | YES | NULL | |
| tees | character varying | 50 | YES | NULL | |
| reducers | character varying | 50 | YES | NULL | |
| caps | character varying | 50 | YES | NULL | |
| flanges | character varying | 50 | YES | NULL | |
| valves_flanged | character varying | 50 | YES | NULL | |
| valves_welded | character varying | 50 | YES | NULL | |
| cut_outs | character varying | 50 | YES | NULL | |
| supports | character varying | 50 | YES | NULL | |
| bends | character varying | 50 | YES | NULL | |
| field_welds | character varying | 50 | YES | NULL | |
| paint_color | character varying | 100 | YES | NULL | |
| cwp | character varying | 50 | YES | NULL | |
| iso_number | character varying | 100 | YES | NULL | |
| iso_type | character varying | 100 | YES | NULL | |
| calculated_eq_length | numeric | 10,2 | YES | NULL | |
| calculated_area | numeric | 10,2 | YES | NULL | |
| union_couplings | numeric | 10,2 | YES | NULL | |
| expansion_joints | numeric | 10,2 | YES | NULL | |
| pmi_req | character varying | 50 | YES | NULL | |
| paut_req | character varying | 50 | YES | NULL | |
| hardness_req | character varying | 50 | YES | NULL | |
| flange_guard | character varying | 50 | YES | NULL | |
| continued_on | character varying | 100 | YES | NULL | |
| connects_to | character varying | 100 | YES | NULL | |
| pickling_req | character varying | 50 | YES | NULL | |
| flushing_req | character varying | 50 | YES | NULL | |
| pipeline_num | character varying | 100 | YES | NULL | |
| weight | character varying | 50 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |

## reducers

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('reducers_id_seq'::regclass) | PRIMARY KEY (id) |
| size | character varying | 50 | NO | | |
| size1 | character varying | 50 | NO | | |
| size2 | character varying | 50 | NO | | |
| description | character varying | 255 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## standard_fittings

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('standard_fittings_id_seq'::regclass) | PRIMARY KEY (id) |
| component_type | character varying | 100 | NO | | |
| size | character varying | 50 | NO | | |
| description | character varying | 255 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## tee_reducing

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('tee_reducing_id_seq'::regclass) | PRIMARY KEY (id) |
| size | character varying | 50 | NO | | |
| size1 | character varying | 50 | NO | | |
| size2 | character varying | 50 | NO | | |
| description | character varying | 255 | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| updated_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| profile_id | integer | 32 | YES | NULL | |

## trigger_logs

| Column Name | Data Type | Length/Precision | Nullable | Default | Primary Key |
|-------------|-----------|------------------|----------|---------|------------|
| id | integer | 32 | NO | nextval('trigger_logs_id_seq'::regclass) | PRIMARY KEY (id) |
| table_name | character varying | 100 | NO | | |
| operation | character varying | 50 | YES | NULL | |
| record_id | integer | 32 | YES | NULL | |
| before_values | jsonb | | YES | NULL | |
| after_values | jsonb | | YES | NULL | |
| error_message | text | | YES | NULL | |
| created_at | timestamp without time zone | | YES | CURRENT_TIMESTAMP | |
| mapping_not_found | boolean | | YES | NULL | |
