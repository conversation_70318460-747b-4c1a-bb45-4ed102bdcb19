import fitz
import re

import pandas as pd
import numpy as np

from src.utils import convert_roi_payload
from src.app_paths import getSourceRawDataPath


def safe_literal_eval(coord_str):
    if isinstance(coord_str, np.ndarray):
        return tuple(coord_str)
    if isinstance(coord_str, str):
        # Use regex to extract the numbers from the string
        numbers = re.findall(r"[-+]?\d*\.\d+|\d+", coord_str)
        if len(numbers) == 4:
            return tuple(float(num) for num in numbers)
    elif isinstance(coord_str, tuple) and len(coord_str) == 4:
        return coord_str
    print(f"Warning: Could not parse coordinate string: {coord_str}")
    return None

def plugin_convert_raw_data(project_source, save_file="debug/converted_raw_data.xlsx"):
    import os

    projectId, filename = project_source
    feather = getSourceRawDataPath(projectId, filename)
    df = pd.read_feather(feather)

    df["coordinates"] = df["coordinates"].apply(safe_literal_eval)
    df["coordinates2"] = df["coordinates2"].apply(safe_literal_eval)
    # df["polygon"] = df["polygon"].apply(safe_literal_eval)

    df["x1"], df["y1"], df["x2"], df["y2"] = zip(*df["coordinates2"])

    # updates only if rotated bbox
    # Update x1, y1, x2, y2 only if rotated_bbox has a value
    # for idx, row in df[df["rotated_bbox"].notna()].iterrows():
    #     df.at[idx, "x1"], df.at[idx, "y1"], df.at[idx, "x2"], df.at[idx, "y2"] = row["rotated_bbox"]

    df["bbox_width"] = df["x2"] - df["x1"]
    df["bbox_height"] = df["y2"] - df["y1"]
    df["bbox_area"] = df["bbox_width"] * df["bbox_height"]

    df.to_excel(save_file, index=False)
    return {
        "success": True,
        "save_dir": os.path.dirname(save_file),
        "save_file": save_file
    }

    df.to_excel(save_file, index=False)
    df["x1"], df["y1"], df["x2"], df["y2"] = zip(*df["rotated_bbox"].apply(unpack_coordinates).dropna())

    df["bbox_width"] = df["x2"] - df["x1"]
    df["bbox_height"] = df["y2"] - df["y1"]

    df["bbox_area"] = df["bbox_width"] * df["bbox_height"]

    df.to_excel(save_file, index=False)
    return {"success": True}

    df.to_excel(save_file, index=False)
    return {"success": True}
