import pandas as pd
from datetime import datetime

from src.atom.pg_database import pg_connection


def plugin_postgres_read_client(client_id: int = 0,
                               client_name: str = "",
                               limit: int = 100,
                               as_dataframe: bool = True):
    """
    Reads client data from the PostgreSQL database.

    Args:
        client_id (int): Filter by client ID (0 for all clients)
        client_name (str): Filter by client name (case-insensitive partial match)
        limit (int): Maximum number of records to return
        as_dataframe (bool): Return results as pandas DataFrame if True, else as list of dicts

    Returns:
        pd.DataFrame or list: Client data
    """
    print("Reading client data from PostgreSQL database.")

    # Validate inputs
    try:
        client_id = int(client_id)
    except (ValueError, TypeError):
        print("Error: client_id must be an integer.")
        return None

    try:
        limit = int(limit)
        if limit <= 0:
            limit = 100
            print(f"Invalid limit value. Using default: {limit}")
    except (ValueError, TypeError):
        limit = 100
        print(f"Invalid limit value. Using default: {limit}")

    try:
        with pg_connection.get_db_connection() as conn:
            print("Connection established.")

            # Build query based on filters
            query = """
            SELECT
                id, client_name, contact_name, contact_email,
                contact_phone, address, created_at, updated_at
            FROM public.atem_clients
            WHERE 1=1
            """

            params = []

            if client_id > 0:
                query += " AND id = %s"
                params.append(client_id)

            if client_name:
                query += " AND client_name ILIKE %s"
                params.append(f"%{client_name}%")

            query += " ORDER BY id ASC LIMIT %s"
            params.append(limit)

            # Execute query
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                results = cursor.fetchall()

                if not results:
                    print("No clients found matching the criteria.")
                    if as_dataframe:
                        return pd.DataFrame(columns=columns)
                    else:
                        return []

                # Format results
                if as_dataframe:
                    df = pd.DataFrame(results, columns=columns)
                    print(f"Found {len(df)} client(s).")

                    # Print the DataFrame
                    print("\nClient Data:")
                    print(df)

                    return df
                else:
                    # Convert to list of dictionaries
                    client_list = []
                    for row in results:
                        client_dict = dict(zip(columns, row))
                        client_list.append(client_dict)

                    print(f"Found {len(client_list)} client(s).")

                    # Print the client list
                    print("\nClient Data:")
                    for client in client_list:
                        print(f"ID: {client['id']}, Name: {client['client_name']}")
                        print(f"  Contact: {client['contact_name']} ({client['contact_email']})")
                        print(f"  Phone: {client['contact_phone']}")
                        print(f"  Address: {client['address']}")
                        print(f"  Created: {client['created_at']}, Updated: {client['updated_at']}")
                        print("-" * 50)

                    return client_list

    except Exception as e:
        print(f"Failed to read client data: {e}")
        import traceback
        traceback.print_exc()
        return None