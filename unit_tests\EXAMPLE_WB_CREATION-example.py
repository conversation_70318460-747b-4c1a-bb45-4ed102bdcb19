"""
RFQ Template Creation Script with Advanced Excel Features
=========================================================

Creates RFQ (Request for Quotation) templates with comprehensive data validation using xlsxwriter.
This script provides advanced Excel features including dropdown validation, conditional formatting,
and structured data entry templates for piping material takeoff categorization.

EXPLANATION OF USE:
------------------
This script provides functions for creating RFQ templates with:

1. create_rfq_template() - Creates a comprehensive RFQ template with validation
2. load_data_to_template() - Loads DataFrame data into the template with validation

The template includes all categorization fields from the piping material classification system
with dropdown validation, conditional formatting, and data entry assistance.

VALIDATION FEATURES:
-------------------
When validation is enabled, the script adds dropdown validation to these classification fields:
- RFQ Scope: 9 options (Bolts, Fittings, Flanges, Gaskets, Miscellaneous, Pipe, Supports, Valves, Instruments)
- Unit of Measure: 8 options (Cubic Feet, EA, LBS, Linear Feet, Lot, Pair, Square Feet, Tons)
- Size1/Size2: 400+ size options (0.03125" to 100")
- Schedule: 500+ schedule options (5, 5S, SCH40, STD, XH, etc.)
- Rating: 40+ pressure ratings (100, 150, 300, 600, 1500, 2500, etc.)
- ASTM: 200+ ASTM standards (A36, A53, A106, A312, B88, etc.)
- Grade: 200+ grade specifications (304L, 316L, WPB, F11, etc.)
- ANSME/ANSI: 50+ standards (B16.5, B16.9, B31.3, API 600, etc.)
- Material: 30+ material types (Carbon Steel, Stainless Steel, Bronze, PVC, etc.)
- Abbreviated Material: 35+ abbreviations (CS, SS, AL, CPVC, etc.)
- Coating: 10+ coating options (Galvanized, Teflon, Cadmium, etc.)
- Forging: 7 manufacturing methods (SMLS, ERW, Forged, Threaded, etc.)
- Ends: 25+ end connection types (BE, SW, TE, FLG, etc.)
- Category-specific fields (Pipe, Valve, Fitting, Bolt, Gasket categories)

USAGE EXAMPLES:
--------------
# Create basic template without data
create_rfq_template("rfq_template.xlsx")

# Create template and load DataFrame data
df = pd.DataFrame(your_data)
load_data_to_template(df, "rfq_with_data.xlsx")

# Create template with custom validation lists
custom_validation = {"material": ["Custom Material 1", "Custom Material 2"]}
create_rfq_template("custom_template.xlsx", custom_validation=custom_validation)

DEVELOPER NOTES:
---------------
- Uses xlsxwriter for advanced Excel features and better performance
- Validation lists are derived from categorization_table in prompts.py
- Column structure follows RFQ database schema
- Supports conditional formatting based on field values
- Includes data entry assistance with cell comments and formatting
- Template structure is extensible for additional fields

TECHNICAL DETAILS:
-----------------
- xlsxwriter provides better performance than openpyxl for large datasets
- Validation uses Excel's data validation feature with dropdown lists
- Conditional formatting highlights incomplete or invalid entries
- Cell formatting includes proper data types (text, number, date)
- Template supports up to 1 million rows of data

SCRIPT EXECUTION:
----------------
Run directly as a script (do not import):
    python -m unit_tests.rfq_template

Or from an IDE, modify the flags in the __main__ block:
    create_template = True/False
    load_sample_data = True/False
    output_path = "path/to/output.xlsx"
"""
import os
import pandas as pd
import xlsxwriter
from datetime import datetime
import sys

# Import categorization table from prompts.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from unit_tests.prompts import categorization_table

# Validation Lists - converted from the selected text
FLANGE_TYPES = [
    "FFWN", "FIXED", "GRAYLOC", "HEAVY DUTY", "LAP JOINT", "RFBF",
    "RFFESW", "RFFEWN", "RFLWN", "RFSW", "RFWN", "RJ", "SOCKETED",
    "SOLAP", "SOFF", "SORF", "VANSTONE"
]

SIZES = [
    "0.0625", "0.125", "0.1875", "0.25", "0.3125", "0.5", "0.75", "1",
    "1.5", "2", "2.5", "3", "4", "6", "8", "10", "12", "14", "16", "18",
    "20", "22", "24", "26", "28", "30", "32", "34", "36", "38", "40",
    "42", "44", "46", "48", "50", "52"
]

RATINGS = ["150", "232", "300", "600", "800", "900", "1500", "2500"]

FLANGE_MATERIALS = [
    "304L", "304SS", "316L", "316SS", "357301", "CPVC", "CS", "GRE",
    "HDPE", "PVC", "PVDF", "SS"
]

GASKET_TYPES = [
    "FLAT", "FLAT RING", "FULL FACE", "GRAYLOC", "GARLOCK", "GRAFOIL GHR",
    "RING JOINT", "SPIRAL SPRING", "SPIRAL WOUND", "SPIRIAL WOUND IR", "UNSPECIFIED"
]

STUD_TYPES = ["A193-B7", "A193-B7ZP", "A320-L7"]

NUT_TYPES = ["A194-2H", "A194-2HZP", "A194-4"]

# Column mapping for validation - maps Excel column names to validation lists
VALIDATION_MAPPING = {
    "Flange Type": FLANGE_TYPES,
    "Flange Size": SIZES,
    "Flange Rating": RATINGS,
    "Flange Material": FLANGE_MATERIALS,
    "Gasket Type": GASKET_TYPES,
    "Stud Type": STUD_TYPES,
    "Nut Type": NUT_TYPES
}

SQL = '''
SELECT 
    test_pkg_num AS "Test Package Number",
    drawing_id AS "Drawing ID",
    flange_number AS "Flange Number",
    tag_id AS "Tag ID",
    flange_type AS "Flange Type",
    flange_size AS "Flange Size",
    flange_rating AS "Flange Rating",
    flange_material AS "Flange Material",
    gasket_type AS "Gasket Type",
    gasket_material AS "Gasket Material",
    stud_type AS "Stud Type",
    stud_length AS "Stud Length",
    nut_type AS "Nut Type",
    stud_count AS "Stud Count",
    coating AS "Coating",
    washer AS "Washer",
    achieved_torque_final AS "Achieved Torque Final",
    torque_tool_id_final AS "Torque Tool ID Final",
    final_tool_calibration_date AS "Final Tool Calibration Date",
    date_torqued AS "Date Torqued",
    foreman AS "Foreman",
    tool_operator_final AS "Tool Operator Final",
    qc_rep AS "QC Rep",
    qc_date AS "QC Date"
FROM public.flange_log_view
WHERE job = '81148-'
AND (
    tag_id IS NOT NULL 
    OR qc_date IS NOT NULL 
    OR qc_rep IS NOT NULL 
    OR date_torqued IS NOT NULL 
    OR inspector_name IS NOT NULL
    OR inspection_status = 'completed'
)
AND void_record IS NOT True
AND inspection_status != 'none'
ORDER BY flange_number;
'''

SQL_ALL_FLANGE_LOGS = '''
SELECT
    job AS "Job",
    drawing_id AS "Drawing",
    test_pkg_num AS "Test Package #",
    fluid_code AS "Fluid Code",
    flange_number AS "Flange #",
    tag_id AS "Tag",
    flange_type AS "Flange Type",
    flange_size AS "Flange Size",
    flange_rating AS "Flange Rating",
    flange_material AS "Flange Material",
    gasket_type AS "Gasket Type",
    gasket_material AS "Gasket Material",
    stud_type AS "Stud Type",
    stud_length AS "Stud Length",
    nut_type AS "Nut Type",
    stud_count AS "Stud Count",
    coating AS "Coating",
    washer AS "Washer",
    date_torqued AS "Date Torqued",
    foreman AS "Foreman",
    qc_rep AS "QC Rep",
    qc_date AS "QC Date",
    tag_hung_date AS "Tag Hung Date",
    lubricant AS "Lubricant",
    notes AS "Notes",
    connects_to AS "Connects To",
    connection_ident AS "Connection Ident",
    primary_scope AS "Primary Scope",
    sub_scope AS "Sub Scope",
    void_record AS "Void Record",
    void_reason AS "Void Reason",
    tpsl_id AS "Tpsl Id",
    id AS "Id",
    created_at AS "Created At",
    created_by AS "Created By",
    updated_at AS "Updated At",
    updated_by AS "Updated By"
FROM public.flange_log_view
WHERE job = '81148-'
ORDER BY flange_number;
'''


def create_db_engine():
    """Create SQLAlchemy engine from environment variables with connection pooling"""
    load_dotenv()
    
    DB_NAME = os.getenv('DB_NAME')
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_HOST = os.getenv('DB_HOST', 'excelsiordb.postgres.database.azure.com')
    DB_PORT = os.getenv('DB_PORT', '5432')
    
    DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

    return create_engine(
        DATABASE_URL,
        pool_size=5,               # Set maximum number of permanent connections
        max_overflow=10,           # Allow up to 10 additional temporary connections
        pool_timeout=30,           # Wait up to 30 seconds for a connection
        pool_recycle=1800         # Recycle connections after 30 minutes
    )


def add_dropdown_validation(file_path: str, df: pd.DataFrame):
    """
    Add dropdown validation to Excel file for specified columns.

    Args:
        file_path: Path to the Excel file
        df: DataFrame containing the data (used to determine data range)
    """
    # Load the workbook
    wb = load_workbook(file_path)
    ws = wb.active

    # Get the number of rows with data (including header)
    max_row = len(df) + 1

    # Add validation for each mapped column
    for col_name, validation_list in VALIDATION_MAPPING.items():
        # Find the column index for this column name
        col_index = None
        for idx, header in enumerate(df.columns, 1):
            if header == col_name:
                col_index = idx
                break

        if col_index is None:
            continue  # Skip if column not found

        # Convert column index to letter (A, B, C, etc.)
        col_letter = get_column_letter(col_index)

        # Create validation formula (comma-separated list)
        validation_formula = '"' + ','.join(validation_list) + '"'

        # Create data validation object with proper dropdown settings
        # IMPORTANT: showDropDown=False actually ENABLES the dropdown arrow (counter-intuitive!)
        dv = DataValidation(
            type="list",
            formula1=validation_formula,
            allow_blank=True,
            showDropDown=False,  # Counter-intuitive: False enables the dropdown arrow!
            showInputMessage=False,
            showErrorMessage=False  # Disable error messages to avoid compatibility issues
        )

        # Apply validation to the data range (excluding header)
        if max_row > 1:  # Only add validation if there are data rows
            range_string = f"{col_letter}2:{col_letter}{max_row}"
            dv.add(range_string)
            ws.add_data_validation(dv)

    # Save the workbook
    wb.save(file_path)
    wb.close()


def generate_flange_report(output_path: str, add_validation: bool = False):
    """
    Connects to PostgreSQL, runs the flange report query, and writes the result to an Excel file.
    NULL values are replaced with blanks.

    Args:
        output_path: Path where the Excel file will be saved
        add_validation: If True, adds dropdown validation to specified columns
    """
    engine = create_db_engine()
    with engine.connect() as conn:
        df = pd.read_sql_query(text(SQL), conn)
        df = df.where(pd.notnull(df), '')  # Replace NaN (from NULL) with blank
        df.to_excel(output_path, index=False)

        # Add dropdown validation if requested
        if add_validation:
            add_dropdown_validation(output_path, df)
            print(f"Flange report with validation written to: {output_path}")
        else:
            print(f"Flange report written to: {output_path}")

def export_all_flange_logs_excel(output_path: str, add_validation: bool = False):
    """
    Connects to PostgreSQL, runs the all flange logs query, and writes the result to an Excel file.
    NULL values are replaced with blanks.

    Args:
        output_path: Path where the Excel file will be saved
        add_validation: If True, adds dropdown validation to specified columns
    """
    engine = create_db_engine()
    with engine.connect() as conn:
        df = pd.read_sql_query(text(SQL_ALL_FLANGE_LOGS), conn)
        df = df.where(pd.notnull(df), '')  # Replace NaN (from NULL) with blank

        # Convert timezone-aware datetimes to timezone-naive
        for col in df.select_dtypes(include=['datetime64[ns, UTC]', 'datetimetz']).columns:
            df[col] = df[col].dt.tz_localize(None)

        df.to_excel(output_path, index=False)

        # Add dropdown validation if requested
        if add_validation:
            add_dropdown_validation(output_path, df)
            print(f"All flange logs with validation exported to: {output_path}")
        else:
            print(f"All flange logs exported to: {output_path}")

if __name__ == '__main__':

    pull_work_report = True    
    pull_all_flange_logs = False # '81148-' only
    add_validation = False  # Set to True to add dropdown validation to Excel files

    # Example: Generate flange report
    if pull_work_report:
        print("Starting flange report generation...")
        output_path = r"C:\Users\<USER>\Downloads\Excel Reports\Flange Reports\Flange Work Report - 6-7-2025.xlsx"
        try:
            generate_flange_report(output_path, add_validation=add_validation)
        except Exception as e:
            print(f"Error: {e}")

    # Example: Export all flange logs to Excel
    if pull_all_flange_logs:
        print("Starting all flange logs export...")
        try:
            all_logs_output = r"C:\Users\<USER>\Downloads\Excel Reports\Flange Reports\Flange Log - 6-13-2025.xlsx"
            export_all_flange_logs_excel(all_logs_output, add_validation=add_validation)
        except Exception as e:
            print(f"Error exporting all flange logs: {e}")
