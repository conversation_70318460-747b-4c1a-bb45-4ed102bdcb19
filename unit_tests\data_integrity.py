"""
Functions for checking and correcting data integrity in classification results.
"""

import pandas as pd
import numpy as np
import logging
import json, re
from unit_tests.prompts import categorization_table
from src.atom.convert_excel_fields import convert_display_to_key, rfq_column_order

logger = logging.getLogger(__name__)

def get_valid_options():
    """
    Get valid options for each classification field from categorization_table in prompts.py
    """
    options = {item['field']: item['options'] for item in categorization_table
              if item['field'] not in ['size1', 'size2']}
    return options

def validate_options(df: pd.DataFrame) -> pd.DataFrame:
    """
    Validate that each cell value is in the allowed options for its column.
    Adds 'option_review' and 'invalid_options' columns.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with validation columns added
    """
    df = df.copy()
    valid_options = get_valid_options()

    # Initialize new columns
    df['option_review'] = False
    df['invalid_options'] = ''

    # Check each row
    for idx, row in df.iterrows():
        invalid_fields = {}

        # Check each field that should be validated
        for field, options in valid_options.items():
            if field in row.index:
                value = row[field]
                # Skip actual blank cells and '' values
                if pd.isna(value) or value == '':
                    continue

                # Convert value to string for comparison
                str_value = str(value)
                # Remove .0 from float strings
                if str_value.endswith('.0'):
                    str_value = str_value[:-2]

                if str_value not in options:
                    invalid_fields[field] = str_value

        if invalid_fields:
            df.at[idx, 'option_review'] = True
            df.at[idx, 'invalid_options'] = json.dumps(invalid_fields)

    # Log validation results
    review_needed = df['option_review'].sum()
    if review_needed > 0:
        logger.info(f"Found {review_needed} rows with invalid options")
        for idx, row in df[df['option_review']].iterrows():
            logger.info(f"Row {idx}: {row['invalid_options']}")
    else:
        logger.info("All options are valid")

    return df

def replace_empty_with_na(df: pd.DataFrame) -> pd.DataFrame:
    """
    Replace double quotes ('') with empty strings in the DataFrame.
    Does not modify actual empty cells or other values.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with double quotes replaced with empty strings
    """
    # Create a copy to avoid modifying the original
    df = df.copy()

    # Only replace double quotes ('') with empty string ''
    df = df.replace("''", '')

    # Log the changes
    changes = {}
    for column in df.columns:
        empty_count = df[column].eq('').sum()
        if empty_count > 0:
            changes[column] = empty_count

    if changes:
        logger.info("Replaced double quotes ('') with empty strings in the following columns:")
        for col, count in changes.items():
            logger.info(f"  - {col}: {count}")
    else:
        logger.info("No double quotes ('') found to replace")

    return df

def analyze_data_integrity(df: pd.DataFrame) -> dict:
    """
    Analyze the data integrity of the DataFrame and return a summary of findings.

    Args:
        df: Input DataFrame

    Returns:
        Dictionary containing analysis results
    """
    analysis = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'double_quotes': {},
        'na_cells': {},
        'unique_values': {},
        'data_types': df.dtypes.to_dict()
    }

    for column in df.columns:
        # Count double quotes ('')
        double_quotes_count = df[column].eq("''").sum()
        if double_quotes_count > 0:
            analysis['double_quotes'][column] = double_quotes_count

        # Count '' values
        na_count = df[column].eq('').sum()
        if na_count > 0:
            analysis['na_cells'][column] = na_count

        # Count unique values
        unique_count = df[column].nunique()
        analysis['unique_values'][column] = unique_count



    # Correct malformed data

    rfq_fields_to_process = ['rfq_scope','general_category','unit_of_measure','material','abbreviated_material','ansme_ansi',
    'astm','grade','rating','schedule','coating','forging','ends','item_tag','tie_point','pipe_category','valve_type','fitting_category',
    'weld_category','bolt_category','gasket_category'
    ]

    process_dataframe(df, columns=rfq_fields_to_process, inplace=True)

    return analysis

"""
Test script to demonstrate the classification value correction function.
"""

import pandas as pd
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_correct_value_old(value, column_name=None):
    """
    Extract the correct value from potentially malformed JSON responses.

    Args:
        value: The potentially malformed value from the DataFrame
        column_name: Optional column name context to identify relevant keys

    Returns:
        The corrected value
    """
    # Skip empty values, NaN, None, etc.
    if pd.isna(value) or value == '' or value == '_NA_':
        return value

    # Convert value to string if it's not already
    str_value = str(value).strip()

    # Remove surrounding quotes if they exist (e.g., '"A105N"' -> 'A105N')
    if (str_value.startswith('"') and str_value.endswith('"')) or \
       (str_value.startswith("'") and str_value.endswith("'")):
        str_value = str_value[1:-1]

    # Check if value is a JSON string
    try:
        # Try to parse as JSON (handle single quotes)
        json_data = json.loads(str_value.replace("'", '"'))

        # If it's a simple value (not dict/list), return it directly
        if not isinstance(json_data, (dict, list)):
            return str(json_data)

        # For dictionary formats
        if isinstance(json_data, dict):
            # Explicit case: {"value": "..."}
            if 'value' in json_data:
                extracted = str(json_data['value']).strip("'\" ")
                return extracted

            # Explicit case: {"classification field": "...", "value": "..."}
            if 'classification field' in json_data and 'value' in json_data:
                extracted = str(json_data['value']).strip("'\" ")
                return extracted

            # Case where 'classification field' defines which field to use
            if 'classification field' in json_data:
                classification_field = json_data['classification field']
                if classification_field in json_data:
                    extracted = str(json_data[classification_field]).strip("'\" ")
                    return extracted

            # Column-name specific extraction if column name provided
            if column_name and column_name in json_data:
                extracted = str(json_data[column_name]).strip("'\" ")
                return extracted

            # Fallback: first simple value (non-dict/list) found in dict
            for key, val in json_data.items():
                if not isinstance(val, (dict, list)):
                    extracted = str(val).strip("'\" ")
                    return extracted

        # For list formats, return comma-separated values
        if isinstance(json_data, list):
            return ', '.join(str(item).strip("'\" ") for item in json_data)

        # Fallback serialization if uncertain
        return str(json_data)

    except json.JSONDecodeError:
        # Handle nested escaped JSON strings
        if '\\\"' in str_value or '\\\\' in str_value:
            try:
                cleaned = str_value.replace('\\"', '"').replace('\\\\', '\\')
                json_data = json.loads(cleaned)

                # Repeat same logic as above after cleaning
                if isinstance(json_data, dict):
                    if 'value' in json_data:
                        extracted = str(json_data['value']).strip("'\" ")
                        return extracted

                    if 'classification field' in json_data:
                        classification_field = json_data['classification field']
                        if classification_field in json_data:
                            extracted = str(json_data[classification_field]).strip("'\" ")
                            return extracted

                    if column_name and column_name in json_data:
                        extracted = str(json_data[column_name]).strip("'\" ")
                        return extracted

                    for key, val in json_data.items():
                        if not isinstance(val, (dict, list)):
                            extracted = str(val).strip("'\" ")
                            return extracted

                # Handle lists
                if isinstance(json_data, list):
                    return ', '.join(str(item).strip("'\" ") for item in json_data)

            except:
                pass

        # Special case handling explicitly preserved
        if "\"schedule\":" in str_value and "size" in str_value:
            try:
                match = re.search(r'"schedule"\s*:\s*"([^"]+)"', str_value)
                if match:
                    return match.group(1).strip("'\" ")
            except:
                pass

        # Return original value if parsing fails
        return str_value

def extract_correct_value(value, column_name=None, debug=False):
    if pd.isna(value) or value == '':
        return value

    str_value = str(value).strip()

    if debug:
        print(f"    DEBUG extract_correct_value: Input value type={type(value)}, column_name={column_name}")
        print(f"    DEBUG extract_correct_value: str_value={repr(str_value[:200])}...")

    # Remove surrounding quotes
    if (str_value.startswith('"') and str_value.endswith('"')) or \
       (str_value.startswith("'") and str_value.endswith("'")):
        str_value = str_value[1:-1]
        if debug:
            print(f"    DEBUG extract_correct_value: Removed surrounding quotes: {repr(str_value[:200])}...")

    # Utility to safely parse JSON
    def safe_json_load(val):
        try:
            # First try parsing as-is (in case it's already valid JSON)
            result = json.loads(val)
            if debug:
                print(f"    DEBUG safe_json_load: Successfully parsed JSON as-is")
            return result, True
        except json.JSONDecodeError:
            try:
                # Handle escaped quotes properly before replacing single quotes
                # Replace escaped single quotes with a placeholder
                temp_val = val.replace("\\'", "___ESCAPED_QUOTE___")
                # Replace remaining single quotes with double quotes
                temp_val = temp_val.replace("'", '"')
                # Restore escaped quotes as proper JSON escaped quotes
                temp_val = temp_val.replace("___ESCAPED_QUOTE___", "\\'")

                result = json.loads(temp_val)
                if debug:
                    print(f"    DEBUG safe_json_load: Successfully parsed JSON after quote handling")
                return result, True
            except json.JSONDecodeError as e:
                if debug:
                    print(f"    DEBUG safe_json_load: JSON parse failed: {e}")
                return val, False

    # Attempt initial parsing
    json_data, success = safe_json_load(str_value)

    if success:
        if debug:
            print(f"    DEBUG extract_correct_value: JSON parsed successfully, type={type(json_data)}")

        # Handle nested/double-encoded JSON strings
        if isinstance(json_data, str):
            json_data_inner, inner_success = safe_json_load(json_data)
            if inner_success:
                json_data = json_data_inner
                if debug:
                    print(f"    DEBUG extract_correct_value: Nested JSON parsed, type={type(json_data)}")

        if isinstance(json_data, dict):
            if debug:
                print(f"    DEBUG extract_correct_value: Dict keys: {list(json_data.keys())}")

            # Common extraction cases
            for key in ['value', column_name, 'classification field', 'schedule', 'grade', 'ends', 'material']:
                if key and key in json_data:
                    extracted = json_data[key]
                    if debug:
                        print(f"    DEBUG extract_correct_value: Found key '{key}' with value: {repr(extracted)}")
                    if isinstance(extracted, (str, int, float)):
                        result = str(extracted).strip()
                        if debug:
                            print(f"    DEBUG extract_correct_value: Returning extracted value: {repr(result)}")
                        return result

            # Generic dict handling: return first non-dict/list value
            for key, val in json_data.items():
                if not isinstance(val, (dict, list)):
                    result = str(val).strip()
                    if debug:
                        print(f"    DEBUG extract_correct_value: Returning first simple value from key '{key}': {repr(result)}")
                    return result

        elif isinstance(json_data, list):
            # Handle lists by joining their values
            result = ', '.join(str(item).strip() for item in json_data)
            if debug:
                print(f"    DEBUG extract_correct_value: Returning joined list: {repr(result)}")
            return result

        else:
            result = str(json_data).strip()
            if debug:
                print(f"    DEBUG extract_correct_value: Returning stringified JSON: {repr(result)}")
            return result

    # Handle escaped JSON strings
    if '\\\"' in str_value or '\\\\' in str_value:
        if debug:
            print(f"    DEBUG extract_correct_value: Trying escaped JSON handling")
        cleaned = str_value.replace('\\"', '"').replace('\\\\', '\\')
        json_data, success = safe_json_load(cleaned)
        if success and isinstance(json_data, dict):
            for key in ['value', column_name, 'classification field', 'schedule', 'grade', 'ends', 'material']:
                if key and key in json_data:
                    extracted = json_data[key]
                    if isinstance(extracted, (str, int, float)):
                        result = str(extracted).strip()
                        if debug:
                            print(f"    DEBUG extract_correct_value: Escaped JSON - returning: {repr(result)}")
                        return result

    # Special regex extraction as last fallback
    match_schedule = re.search(r'"schedule"\s*:\s*"([^"]+)"', str_value)
    if match_schedule:
        result = match_schedule.group(1).strip()
        if debug:
            print(f"    DEBUG extract_correct_value: Regex match - returning: {repr(result)}")
        return result

    # If all else fails, return original
    if debug:
        print(f"    DEBUG extract_correct_value: No extraction possible, returning original: {repr(str_value.strip())}")
    return str_value.strip()

def process_dataframe(df, columns=None, inplace=True, add_corrected_columns=False, suffix='_corrected'):
    """
    Process a DataFrame by extracting correct values from specified columns.

    Args:
        df (pandas.DataFrame): The DataFrame to process
        columns (list, optional): List of column names to process. If None, uses a predefined list of columns.
        inplace (bool): Whether to modify the original DataFrame or return a copy
        add_corrected_columns (bool): If True, adds new columns with corrected values instead of
                                    replacing the original values
        suffix (str): Suffix to add to column names for corrected values if add_corrected_columns=True

    Returns:
        pandas.DataFrame: The processed DataFrame
    """
    # Default columns to process
    DEFAULT_COLUMNS = [
        'rfq_scope', 'general_category', 'unit_of_measure', 'material',
        'abbreviated_material', 'ansme_ansi', 'astm', 'grade', 'rating',
        'schedule', 'coating', 'forging', 'ends', 'item_tag', 'tie_point',
        'pipe_category', 'valve_type', 'fitting_category', 'weld_category',
        'bolt_category', 'gasket_category'
    ]

    # Work on a copy if not inplace
    if not inplace:
        df = df.copy()

    # If columns is None, use the default columns list (filtering for only those that exist in the df)
    if columns is None:
        columns = [col for col in DEFAULT_COLUMNS if col in df.columns]
        if not columns:
            print("Warning: None of the default columns found in DataFrame. No processing performed.")
            return df

    # Process each column
    for col in columns:
        if col not in df.columns:
            print(f"Warning: Column '{col}' not found in DataFrame. Skipping.")
            continue

        print(f"\n=== DEBUGGING: Processing column '{col}' ===")

        # Count how many values need processing
        non_empty_count = df[col].notna().sum()
        print(f"Non-empty values in {col}: {non_empty_count}")

        # Show sample before processing
        sample_before = df[col].head(3)
        print(f"Sample values before processing:")
        for idx, val in sample_before.items():
            print(f"  Row {idx}: {repr(val)}")

        # Apply the extraction function with explicit column_name parameter
        # Use debug mode for first few rows to see what's happening
        def extract_with_debug(val):
            row_idx = df[df[col] == val].index
            use_debug = len(row_idx) > 0 and row_idx[0] < 3  # Debug first 3 rows
            return extract_correct_value(val, column_name=col, debug=use_debug)

        corrected_values = df[col].apply(extract_with_debug)

        # Show sample after processing
        sample_after = corrected_values.head(3)
        print(f"Sample values after processing:")
        for idx, val in sample_after.items():
            print(f"  Row {idx}: {repr(val)}")

        # Count changes
        changes_count = (df[col].astype(str) != corrected_values.astype(str)).sum()
        print(f"Number of values changed in {col}: {changes_count}")

        # Apply the corrections based on the options
        if add_corrected_columns:
            df[f"{col}{suffix}"] = corrected_values
        else:
            df[col] = corrected_values

    return df

if __name__ == "__main__":
    # Example usage

    # Load your dataframe
    input_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Combined Classification\BRS_010&011\Combined RFQ - BRS_010&011.xlsx"
    output_file = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Combined Classification\BRS_010&011\Combined RFQ - BRS_010&011 - cleaned.xlsx"
    try:
        # Import required functions
        import sys
        sys.path.append(r"C:\Users\<USER>\source\repos\Architekt ATOM")
        from src.atom.convert_excel_fields import read_excel_with_na, convert_display_to_key


        # Read the Excel file using the special function that handles NA values properly
        df = read_excel_with_na(input_file)

        # # DEBUGGING: Limit to first 10 rows for testing
        # print(f"Original dataframe shape: {df.shape}")
        # df = df.head(2)
        # print(f"Limited dataframe shape: {df.shape}")

        # Convert display column names to database keys
        df = convert_display_to_key(df, column_order=rfq_column_order)

        # DEBUGGING: Show what columns we have
        print(f"\nColumns after conversion: {list(df.columns)}")

        # DEBUGGING: Show sample of problematic data before processing
        print("\n=== DEBUGGING: Sample data before processing ===")
        for col in ['material', 'grade', 'astm']:  # Focus on key columns
            if col in df.columns:
                print(f"\n{col} column sample:")
                for idx, val in df[col].head(5).items():
                    print(f"  Row {idx}: {type(val)} -> {repr(val)}")

        # Print initial analysis
        print("\nInitial Data Analysis:")
        initial_analysis = analyze_data_integrity(df)
        print(f"Total Rows: {initial_analysis['total_rows']}")
        print(f"Total Columns: {initial_analysis['total_columns']}")
        print("\nDouble Quotes ('') by Column:")
        for col, count in initial_analysis['double_quotes'].items():
            print(f"  - {col}: {count}")


        # Print sample of data before cleaning
        print("\nSample of data before cleaning:")
        for col in df.columns:
            double_quotes_vals = df[df[col] == "''"][col]
            if not double_quotes_vals.empty:
                print(f"\n{col}:")
                print(double_quotes_vals.head())

        # Replace double quotes with ''
        df_cleaned = replace_empty_with_na(df)


        # Validate options
        df_validated = validate_options(df_cleaned)

        # DEBUGGING: Test extract_correct_value function on specific problematic values
        print("\n=== DEBUGGING: Testing extract_correct_value function ===")
        test_columns = ['material', 'grade', 'astm']
        for col in test_columns:
            if col in df_validated.columns:
                print(f"\nTesting {col} column:")
                for idx, val in df_validated[col].head(3).items():
                    if pd.notna(val) and val != '':
                        print(f"  Row {idx} BEFORE: {repr(val)}")
                        extracted = extract_correct_value(val, column_name=col, debug=True)
                        print(f"  Row {idx} AFTER:  {repr(extracted)}")
                        print(f"  Row {idx} CHANGE: {'YES' if str(val) != str(extracted) else 'NO'}")

        # Process and clean the model responses
        print("\n=== DEBUGGING: Running process_dataframe ===")
        df_validated = process_dataframe(df_validated, inplace=False)

        # df_validated.to_excel(r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-001\Workspace\checkpoint_exported_rfq_data.xlsx", index=False)

        # Print validation results
        print("\nOption Validation Results:")
        invalid_rows = df_validated[df_validated['option_review']]
        if not invalid_rows.empty:
            print(f"\nFound {len(invalid_rows)} rows with invalid options:")
            for idx, row in invalid_rows.iterrows():
                print(f"\nRow {idx}:")
                invalid_opts = json.loads(row['invalid_options'])
                for field, value in invalid_opts.items():
                    print(f"  - {field}: {value}")
        else:
            print("All options are valid")

        # Print final analysis
        print("\nFinal Data Analysis:")
        final_analysis = analyze_data_integrity(df_validated)
        print("\nNA Values by Column:")
        for col, count in final_analysis['na_cells'].items():
            print(f"  - {col}: {count}")

        # Print sample of data after cleaning
        print("\nSample of previously double quotes after cleaning:")
        for col in df_validated.columns:
            na_vals = df_validated[df_validated[col] == ''][col]
            if not na_vals.empty:
                print(f"\n{col}:")
                print(na_vals.head())

        # Save the cleaned and validated data
        df_validated.to_excel(output_file, index=False)
        print(f"\nCleaned and validated data saved to: {output_file}")

    except Exception as e:
        print(f"Error: {e}")
