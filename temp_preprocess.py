import fitz


def unpack_rgb(color_int):
    """Convert packed integer color to RGB tuple."""
    r = (color_int >> 16) & 255
    g = (color_int >> 8) & 255
    b = color_int & 255
    return (r, g, b)

def extract_text_with_bbox(pdf_path):
    doc = fitz.open(pdf_path)
    extracted_data = []

    for page_num, page in enumerate(doc, start=1):
        blocks = page.get_text("dict")["blocks"]
        for block in blocks:
            if block["type"] == 0:  # Text block
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        bbox = span["bbox"]
                        font = span.get("font", "")
                        size = span.get("size", 0)
                        color = span.get("color", 0)
                        opacity = span.get("opacity", 1.0)

                        r, g, b = unpack_rgb(color)

                        # Heuristic: consider text invisible if opacity is 0.0
                        # You can also add a brightness threshold here if needed
                        is_visible = opacity > 0.0

                        if text:
                            extracted_data.append({
                                "page": page_num,
                                "text": text,
                                "bbox": bbox,
                                "font": font,
                                "font_size": size,
                                "color_rgb": (r, g, b),
                                "opacity": opacity,
                                "is_visible": is_visible
                            })
    return extracted_data

# Example usage:
pdf_path = "C:/Drawings/Clients/axisindustries/Axis 2025-07-22 Novellis KEQ3 Rebid/received/combined (2).pdf"

results = extract_text_with_bbox(pdf_path)

import pandas as pd
page_results = []
for item in results:
    # print(f"[Page {item['page']}] '{item['text']}' → BBox: {item['bbox']} Font: {item['font']} Size: {item['font_size']}")

    page_results.append(
        {
            "page": item["page"],
            "text": item["text"],
            "bbox": item["bbox"],
            "font": item["font"],
            "font_size": item["font_size"],
            "color_rgb": item["color_rgb"],
            "opacity": item["opacity"],
            "is_visible": item["is_visible"]
        }
    )

print(page_results)
results_df = pd.DataFrame(page_results)
results_df.to_excel("debug/extracted_text.xlsx", engine="openpyxl")
