"""
Quick updater for public.atem_rfq_input from Excel file.
Reads an Excel file and updates the matching rows in the database where 'id' matches.
Columns in Excel must match the table columns. Skips rows where 'id' is missing.
"""
import pandas as pd
import logging
from src.atom.pg_database.pg_connection import DatabaseConfig, get_db_connection

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def update_rows_from_excel(excel_path, table_name, db_config):
    df = pd.read_excel(excel_path)
    logger.info(f"Loaded {len(df)} rows from {excel_path}")
    
    with get_db_connection(db_config) as conn:
        cur = conn.cursor()
        updated = 0
        errors = 0
        for idx, row in df.iterrows():
            if pd.isna(row.get('id')):
                logger.warning(f"Row {idx} skipped: missing 'id'")
                continue
            row_dict = row.dropna().to_dict()
            id_value = row_dict.pop('id')
            if not row_dict:
                logger.warning(f"Row {idx} skipped: no fields to update (id={id_value})")
                continue
            set_clause = ', '.join([f'"{col}" = %s' for col in row_dict.keys()])
            sql = f'UPDATE {table_name} SET {set_clause} WHERE id = %s'
            values = list(row_dict.values()) + [id_value]
            try:
                cur.execute(sql, values)
                updated += cur.rowcount
            except Exception as e:
                logger.error(f"Error updating row {idx} (id={id_value}): {e}")
                errors += 1
        conn.commit()
        logger.info(f"Update complete. Rows updated: {updated}. Errors: {errors}.")

if __name__ == "__main__":
    db_config = DatabaseConfig()
    
    EXCEL_PATH = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Clients\Excel USA\EXC_0014\Data\TR-003\Final\FIX INPUT.xlsx"
    TABLE_NAME = "public.atem_rfq_input"
    update_rows_from_excel(EXCEL_PATH, TABLE_NAME, db_config)
