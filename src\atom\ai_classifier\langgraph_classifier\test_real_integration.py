"""
Test Real Model Integration with Actual BOM Data

This script tests the LangGraph BOM classification system with actual Gemini model calls
using real BOM data instead of simulated responses.
"""

import asyncio
import os
import sys
import time
import pandas as pd
from pathlib import Path
from dotenv import load_dotenv

# Add the parent directories to the path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent))
sys.path.insert(0, str(current_dir.parent.parent))

try:
    from integration_layer import (
        create_default_langgraph_config,
        create_stage1_only_config,
        LangGraphModelHandler,
        ModelType
    )
    from state_models import create_initial_state, MaterialAnalysisResponse
    from classification_nodes import material_analysis_node
    from langgraph_main import create_classification_workflow
    from export_results import export_classified_results
    print("✅ Successfully imported LangGraph modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the correct directory")
    import traceback
    traceback.print_exc()
    sys.exit(1)


async def test_real_model_integration():
    """Test the real model integration with actual BOM data"""
    
    print("🧪 Testing Real Model Integration with LangGraph BOM Classification")
    print("=" * 80)
    
    # Load environment variables from .env file
    load_dotenv()

    # Check for API key (try both GOOGLE_API_KEY and GEMINI_API_KEY)
    api_key = os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ API Key not found!")
        print("Please set your Google API key in one of these ways:")
        print("1. Create a .env file in the project root with: GEMINI_API_KEY=your_key_here")
        print("2. Set environment variable: set GEMINI_API_KEY=your_key_here")
        print("3. Set environment variable: set GOOGLE_API_KEY=your_key_here")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Test data - real BOM descriptions
    test_cases = [
        {
            "id": "test_001",
            "material_description": "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9",
            "expected_category": "fitting"
        },
        {
            "id": "test_002", 
            "material_description": "PIPE NIPPLE 2\" SCH 40 ASTM A106 BE",
            "expected_category": "fitting"
        },
        {
            "id": "test_003",
            "material_description": "BALL VALVE 3\" 600# CS",
            "expected_category": "valve"
        },
        {
            "id": "test_004",
            "material_description": "WN FLANGE 6\" 300# RF ASTM A105",
            "expected_category": "flange"
        },
        {
            "id": "test_005",
            "material_description": "PIPE SMLS 8\" SCH 40 ASTM A106",
            "expected_category": "pipe"
        }
    ]
    
    # Create configuration for Stage 1 only (Material Analysis)
    config = create_stage1_only_config(
        model_type=ModelType.GEMINI_20_FLASH,
        debug_mode=True,
        api_key=api_key
    )
    
    print(f"\n🔧 Configuration:")
    print(f"   Model: {config.material_analysis_config.model_type.value}")
    print(f"   Temperature: {config.material_analysis_config.temperature}")
    print(f"   Debug mode: {config.debug_mode}")
    
    # Test each case
    results = []
    total_start_time = time.time()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}/{len(test_cases)}")
        print(f"   ID: {test_case['id']}")
        print(f"   Description: {test_case['material_description']}")
        print(f"   Expected: {test_case['expected_category']}")
        
        try:
            # Create initial state
            state = create_initial_state(
                item_id=test_case["id"],
                material_description=test_case["material_description"],
                original_classification={},
                debug_mode=True
            )
            
            # Add model configuration to state
            state["model_config"] = config
            
            # Test Stage 1 - Material Analysis
            start_time = time.time()
            result_state = await material_analysis_node(state)
            processing_time = time.time() - start_time
            
            # Extract results
            processing_path = result_state.get("processing_path", "unknown")
            confidence = result_state.get("confidence_scores", {}).get("category_determination", 0.0)
            extracted_properties = result_state.get("extracted_properties", {})
            
            # Check if result matches expectation
            is_correct = processing_path == test_case["expected_category"]
            
            result = {
                "id": test_case["id"],
                "description": test_case["material_description"],
                "expected": test_case["expected_category"],
                "actual": processing_path,
                "confidence": confidence,
                "correct": is_correct,
                "processing_time": processing_time,
                "extracted_properties": extracted_properties
            }
            
            results.append(result)
            
            # Print result
            status_icon = "✅" if is_correct else "❌"
            print(f"   {status_icon} Result: {processing_path} (confidence: {confidence:.2f})")
            print(f"   ⏱️  Processing time: {processing_time:.2f}s")
            
            if extracted_properties:
                print(f"   📊 Properties: {list(extracted_properties.keys())}")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            result = {
                "id": test_case["id"],
                "description": test_case["material_description"],
                "expected": test_case["expected_category"],
                "actual": "error",
                "confidence": 0.0,
                "correct": False,
                "processing_time": 0.0,
                "error": str(e)
            }
            results.append(result)
    
    # Summary
    total_time = time.time() - total_start_time
    correct_count = sum(1 for r in results if r["correct"])
    accuracy = correct_count / len(results) if results else 0.0
    
    print(f"\n📊 SUMMARY")
    print(f"=" * 50)
    print(f"Total tests: {len(results)}")
    print(f"Correct: {correct_count}")
    print(f"Accuracy: {accuracy:.1%}")
    print(f"Total time: {total_time:.2f}s")
    print(f"Average time per test: {total_time/len(results):.2f}s")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS")
    print(f"=" * 50)
    for result in results:
        status = "✅ PASS" if result["correct"] else "❌ FAIL"
        print(f"{status} {result['id']}: {result['expected']} → {result['actual']} ({result['confidence']:.2f})")
    
    return accuracy >= 0.8  # 80% accuracy threshold


async def test_full_workflow():
    """Test the complete workflow with one item"""
    
    print(f"\n🔄 Testing Complete Workflow")
    print("=" * 50)
    
    # API key should already be loaded from previous test
    api_key = os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ API key not found")
        return False
    
    # Create full configuration
    config = create_default_langgraph_config(
        debug_mode=True,
        api_key=api_key
    )
    
    # Test case
    test_case = {
        "id": "workflow_test_001",
        "material_description": "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9"
    }
    
    try:
        # Create workflow
        workflow = create_classification_workflow(config)
        
        # Create initial state
        state = create_initial_state(
            item_id=test_case["id"],
            material_description=test_case["material_description"],
            original_classification={},
            debug_mode=True
        )
        
        # Add model configuration to state
        state["model_config"] = config
        
        print(f"📝 Testing: {test_case['material_description']}")
        
        # Run complete workflow
        start_time = time.time()
        final_state = await workflow.ainvoke(state)
        processing_time = time.time() - start_time
        
        print(f"✅ Workflow completed in {processing_time:.2f}s")
        print(f"📊 Final stage: {final_state.get('current_stage', 'unknown')}")
        print(f"🛤️  Workflow path: {final_state.get('workflow_path', [])}")
        
        # Print final classifications
        field_classifications = final_state.get("field_classifications", {})
        if field_classifications:
            print(f"📋 Final classifications:")
            for field, value in field_classifications.items():
                print(f"   {field}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_export_functionality():
    """Test the export functionality with sample results"""

    print(f"\n📤 Testing Export Functionality")
    print("=" * 50)

    # API key should already be loaded from previous tests
    api_key = os.environ.get("GOOGLE_API_KEY") or os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ API key not found - skipping export test")
        return False

    # Create configuration
    config = create_stage1_only_config(
        model_type=ModelType.GEMINI_20_FLASH,
        debug_mode=True,
        api_key=api_key
    )

    # Test cases for export
    export_test_cases = [
        {
            "id": "export_001",
            "material_description": "45 LR ELBOW, 316/316L SS A403-WP316/316L-WX, WLD 100% X-RAY, BE, SCH 10S, B16.9"
        },
        {
            "id": "export_002",
            "material_description": "PIPE NIPPLE 2\" SCH 40 ASTM A106 BE"
        },
        {
            "id": "export_003",
            "material_description": "BALL VALVE 3\" 600# CS"
        }
    ]

    print(f"📝 Processing {len(export_test_cases)} items for export test...")

    # Process each test case
    workflow_results = []

    for test_case in export_test_cases:
        try:
            # Create initial state
            state = create_initial_state(
                item_id=test_case["id"],
                material_description=test_case["material_description"],
                original_classification={},
                debug_mode=False  # Reduce output for export test
            )

            # Add model configuration to state
            state["model_config"] = config

            # Run Stage 1 only for export test
            result_state = await material_analysis_node(state)
            workflow_results.append(result_state)

            print(f"   ✅ Processed {test_case['id']}: {result_state.get('processing_path', 'unknown')}")

        except Exception as e:
            print(f"   ❌ Failed to process {test_case['id']}: {e}")
            # Create a mock result for testing export
            mock_result = {
                "item_id": test_case["id"],
                "material_description": test_case["material_description"],
                "processing_path": "error",
                "field_classifications": {"rfq_scope": "Error"},
                "confidence_scores": {"rfq_scope": 0.0},
                "workflow_path": ["error"],
                "processing_time": 0.0,
                "model_calls": 0,
                "tokens_used": 0,
                "identified_issues": [{"field": "processing", "explanation": str(e)}],
                "extracted_properties": {}
            }
            workflow_results.append(mock_result)

    # Create original data DataFrame for reference
    original_data = pd.DataFrame([
        {"id": case["id"], "material_description": case["material_description"]}
        for case in export_test_cases
    ])

    # Test export
    job_folder = r"C:\Users\<USER>\OneDrive\Documents\Architekt IS\Employee Testing\TEST 3 - Heartwell (AX_0044)"

    try:
        export_path = export_classified_results(
            workflow_results=workflow_results,
            job_folder=job_folder,
            original_data=original_data,
            job_name="Heartwell_LangGraph_Test"
        )

        print(f"\n🎉 Export test successful!")
        print(f"📁 Results exported to: {export_path}")

        # Verify file exists
        if os.path.exists(export_path):
            file_size = os.path.getsize(export_path)
            print(f"📊 File size: {file_size:,} bytes")
            print(f"✅ Export file verified and accessible")
        else:
            print(f"❌ Export file not found at expected location")
            return False

        return True

    except Exception as e:
        print(f"❌ Export test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    
    print("LangGraph BOM Classification - Real Model Integration Test")
    print("=" * 80)
    
    # Test Stage 1 only
    stage1_success = await test_real_model_integration()
    
    if stage1_success:
        print(f"\n✅ Stage 1 tests passed! Proceeding to full workflow test...")
        # Test complete workflow
        workflow_success = await test_full_workflow()

        if workflow_success:
            print(f"\n🎉 ALL TESTS PASSED!")
            print("Real model integration is working correctly.")

            # Test export functionality
            await test_export_functionality()
        else:
            print(f"\n⚠️  Stage 1 passed but full workflow failed.")
    else:
        print(f"\n❌ Stage 1 tests failed. Check model integration.")


if __name__ == "__main__":
    asyncio.run(main())
