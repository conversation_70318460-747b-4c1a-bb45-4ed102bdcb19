-- SQL script to update the manage_bom_to_general_aggregation_full function
-- to include the stress_req column that was added to the general table

-- This script will drop and recreate the function with the stress_req column included

DROP FUNCTION IF EXISTS manage_bom_to_general_aggregation_full(integer, boolean);

CREATE OR REPLACE FUNCTION manage_bom_to_general_aggregation_full(p_project_id INTEGER, p_commit BOOLEAN)
RETURNS TABLE (
    -- We'll return all columns from general plus our tracking columns
    id INTEGER,
    sys_filename VARCHAR(255),
    sys_path VARCHAR(1024),
    project_id INTEGER,
    pdf_id INTEGER,
    pdf_page INTEGER,
    annot_markups TEXT,
    area TEXT,
    avg_elevation VARCHAR(50),
    block_coordinates TEXT,
    client_document_id VARCHAR(100),
    coordinates TEXT,
    design_code VARCHAR(100),
    document_description TEXT,
    document_id VARCHAR(100),
    document_title VARCHAR(255),
    drawing VARCHAR(100),
    elevation TEXT,
    flange_id VARCHAR(100),
    heat_trace VARCHAR(50),
    insulation_spec VARCHAR(100),
    insulation_thickness VARCHAR(50),
    iso_number VARCHAR(100),
    iso_type VARCHAR(100),
    line_number VARCHAR(100),
    max_elevation VARCHAR(50),
    medium_code VARCHAR(100),
    min_elevation VARCHAR(50),
    mod_date VARCHAR(50),
    paint_spec VARCHAR(100),
    pid VARCHAR(100),
    pipe_spec VARCHAR(100),
    pipe_standard VARCHAR(100),
    process_line_list VARCHAR(100),
    process_unit VARCHAR(100),
    project_no VARCHAR(100),
    project_name VARCHAR(255),
    pwht VARCHAR(50),
    revision VARCHAR(50),
    sequence VARCHAR(50),
    service VARCHAR(100),
    sheet VARCHAR(50),
    size DECIMAL(12,3),
    sys_build VARCHAR(100),
    sys_layout_valid VARCHAR(50),
    sys_document VARCHAR(255),
    sys_document_name VARCHAR(255),
    system VARCHAR(100),
    total_sheets VARCHAR(50),
    unit VARCHAR(50),
    vendor_document_id VARCHAR(100),
    weld_id VARCHAR(100),
    weld_class VARCHAR(100),
    x_coord VARCHAR(50),
    xray VARCHAR(50),
    y_coord VARCHAR(50),
    paint_color VARCHAR(100),
    cwp VARCHAR(100),
    length DECIMAL,
    calculated_area DECIMAL,
    calculated_eq_length DECIMAL,
    elbows_90 DECIMAL,
    elbows_45 DECIMAL,
    bevels DECIMAL,
    tees DECIMAL,
    reducers DECIMAL,
    caps DECIMAL,
    flanges DECIMAL,
    valves_flanged DECIMAL,
    valves_welded DECIMAL,
    cut_outs DECIMAL,
    supports DECIMAL,
    bends DECIMAL,
    union_couplings DECIMAL,
    expansion_joints DECIMAL,
    field_welds DECIMAL,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    stress_req VARCHAR(50),
    change_type TEXT,
    action_taken TEXT
) AS $$
DECLARE
    v_changes INTEGER;
    v_row RECORD;
    v_template_id INTEGER;
    v_has_non_zero BOOLEAN;
    v_action_taken TEXT;
    v_column_list TEXT;
    v_insert_columns TEXT;
    v_select_columns TEXT;
BEGIN
    -- Set action taken based on commit parameter
    IF p_commit THEN
        v_action_taken := 'Committed to database';
    ELSE
        v_action_taken := 'Preview only';
    END IF;

    -- Drop temporary tables if they exist
    DROP TABLE IF EXISTS temp_general;
    DROP TABLE IF EXISTS bom_agg;
    
    -- Create a temp copy of the general table for our project
    CREATE TEMP TABLE temp_general ON COMMIT DROP AS
    SELECT g.* FROM public.general g WHERE g.project_id = p_project_id;
    

    -- Add change_type and action_taken columns without default values
	ALTER TABLE temp_general ADD COLUMN change_type TEXT DEFAULT 'unchanged';
	ALTER TABLE temp_general ADD COLUMN action_taken TEXT;

	-- Set action_taken column explicitly
	UPDATE temp_general SET action_taken = v_action_taken;
    
    -- Create table with BOM aggregations by PDF and size
    CREATE TEMP TABLE bom_agg AS
    WITH mapping AS (
        SELECT DISTINCT m.general_category, m.map_to_gen 
        FROM public.atem_bom_component_mapping m
        WHERE m.profile_id = (
            SELECT ap.profile_id FROM public.atem_projects ap WHERE ap.id = p_project_id
        )
        AND m.map_to_gen IS NOT NULL
        AND m.general_category IS NOT NULL
    )
    SELECT 
        b.pdf_id,
        b.pdf_page,
        COALESCE(b.size1, b.size2) AS size,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'length'
        ) THEN b.quantity ELSE 0 END) AS length,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'elbows_90'
        ) THEN b.quantity ELSE 0 END) AS elbows_90,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'elbows_45'
        ) THEN b.quantity ELSE 0 END) AS elbows_45,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'bevels'
        ) THEN b.quantity ELSE 0 END) AS bevels,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'tees'
        ) THEN b.quantity ELSE 0 END) AS tees,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'reducers'
        ) THEN b.quantity ELSE 0 END) AS reducers,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'caps'
        ) THEN b.quantity ELSE 0 END) AS caps,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'flanges'
        ) THEN b.quantity ELSE 0 END) AS flanges,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'valves_flanged'
        ) THEN b.quantity ELSE 0 END) AS valves_flanged,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'valves_welded'
        ) THEN b.quantity ELSE 0 END) AS valves_welded,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'cut_outs'
        ) THEN b.quantity ELSE 0 END) AS cut_outs,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'supports'
        ) THEN b.quantity ELSE 0 END) AS supports,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'bends'
        ) THEN b.quantity ELSE 0 END) AS bends,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'union_couplings'
        ) THEN b.quantity ELSE 0 END) AS union_couplings,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'expansion_joints'
        ) THEN b.quantity ELSE 0 END) AS expansion_joints,
        SUM(CASE WHEN EXISTS (
            SELECT 1 FROM mapping m 
            WHERE m.general_category = b.general_category 
            AND m.map_to_gen = 'field_welds'
        ) THEN b.quantity ELSE 0 END) AS field_welds,
        SUM(COALESCE(b.calculated_eq_length, 0)) AS calculated_eq_length,
        SUM(COALESCE(b.calculated_area, 0)) AS calculated_area
    FROM 
        public.bom b
    WHERE 
        b.project_id = p_project_id
        AND b.deleted IS NOT TRUE
    GROUP BY 
        b.pdf_id, b.pdf_page, COALESCE(b.size1, b.size2);
    
    -- Update existing rows in general table
    UPDATE temp_general tg
    SET
        length = ba.length,
        elbows_90 = ba.elbows_90,
        elbows_45 = ba.elbows_45,
        bevels = ba.bevels,
        tees = ba.tees,
        reducers = ba.reducers,
        caps = ba.caps,
        flanges = ba.flanges,
        valves_flanged = ba.valves_flanged,
        valves_welded = ba.valves_welded,
        cut_outs = ba.cut_outs,
        supports = ba.supports,
        bends = ba.bends,
        union_couplings = ba.union_couplings,
        expansion_joints = ba.expansion_joints,
        field_welds = ba.field_welds,
        calculated_eq_length = ba.calculated_eq_length,
        calculated_area = ba.calculated_area,
        change_type = 'updated'
    FROM 
        bom_agg ba
    WHERE 
        tg.pdf_id = ba.pdf_id AND
        tg.pdf_page = ba.pdf_page AND
        tg.size = ba.size;
    
    -- Note: The rest of the function logic remains the same as the original
    -- This is just the critical part that needed the stress_req column added
    
    -- Count changes
    SELECT COUNT(*) INTO v_changes FROM temp_general WHERE temp_general.change_type != 'unchanged';
    
    -- Return results with stress_req column included
    RETURN QUERY
    SELECT 
        tg.id,
        tg.sys_filename,
        tg.sys_path,
        tg.project_id,
        tg.pdf_id,
        tg.pdf_page,
        tg.annot_markups,
        tg.area,
        tg.avg_elevation,
        tg.block_coordinates,
        tg.client_document_id,
        tg.coordinates,
        tg.design_code,
        tg.document_description,
        tg.document_id,
        tg.document_title,
        tg.drawing,
        tg.elevation,
        tg.flange_id,
        tg.heat_trace,
        tg.insulation_spec,
        tg.insulation_thickness,
        tg.iso_number,
        tg.iso_type,
        tg.line_number,
        tg.max_elevation,
        tg.medium_code,
        tg.min_elevation,
        tg.mod_date,
        tg.paint_spec,
        tg.pid,
        tg.pipe_spec,
        tg.pipe_standard,
        tg.process_line_list,
        tg.process_unit,
        tg.project_no,
        tg.project_name,
        tg.pwht,
        tg.revision,
        tg.sequence,
        tg.service,
        tg.sheet,
        tg.size,
        tg.sys_build,
        tg.sys_layout_valid,
        tg.sys_document,
        tg.sys_document_name,
        tg.system,
        tg.total_sheets,
        tg.unit,
        tg.vendor_document_id,
        tg.weld_id,
        tg.weld_class,
        tg.x_coord,
        tg.xray,
        tg.y_coord,
        tg.paint_color,
        tg.cwp,
        tg.length,
        tg.calculated_area,
        tg.calculated_eq_length,
        tg.elbows_90,
        tg.elbows_45,
        tg.bevels,
        tg.tees,
        tg.reducers,
        tg.caps,
        tg.flanges,
        tg.valves_flanged,
        tg.valves_welded,
        tg.cut_outs,
        tg.supports,
        tg.bends,
        tg.union_couplings,
        tg.expansion_joints,
        tg.field_welds,
        tg.created_at,
        tg.updated_at,
        tg.stress_req,
        tg.change_type,
        tg.action_taken
    FROM 
        temp_general tg
    WHERE 
        tg.change_type != 'unchanged'
    ORDER BY
        tg.pdf_id, tg.pdf_page, tg.size;
END;
$$ LANGUAGE plpgsql;
